# mybatis配置
#mybatis:
#  database:
#    provider:
#      # 数据库类型
#      type: mysql
#    pageHelper:
#      # 数据库方言
#      dialect: mysql

# 数据库连接配置
dbuts:
  druid:
    # 数据库驱动名称
#    driverClassName: com.mysql.cj.jdbc.Driver
    # 数据库连接url
#    url: *********************************************************************************************************************************************************************************************************************************
#    # 数据库用户名
#    username: root
#    # 数据库密码
#    password: ENC(kNRXLPlKEKN3bitQl4LWg4zKByvqDZoV)
    driverClassName: org.postgresql.Driver
    url: *********************************************************************************************************************************************************
#    url: ***********************************************************************************************************************************************
    username: postgres
    password: ENC(SD5+U4id1/8ihC4hORqZrqztvJfZdYttVN9Jw+BgiDuiltptfMHUEv9xKQBQBMwo)
    # 验证数据库连接的查询语句
    validationQuery: SELECT 'x'
dbhis:
  druid:
#    his视图对应接口版本  v2.1 、v5 、procedure 、fusion(双D+智审融合版)。DbView2Api根据不同版本会注入不同的UtsThirdCommonBOImpl实现类
    versionType: v5
    specialQuery: default
#    versionType: v4
    #   his视图查询需要在视图名前加用户名，配置此参数
    tablePrefix:
#   his视图名前增加特殊用户名 例查询费用视图 配置980010:prefixname
    specialTablePrefix:
    # his视图扩展字段。逗号分割。可加数据为：
    # 患者基本信息视图yh_ipt_baseinfo：ipt_no（住院号）,med_team_codg(医疗小组编码),cur_dept_codg(当前科室标识),psn_type(人员类型)
    # 费用视图yh_ipt_feedetail: his_ocur_time(数据插入时间),hosp_appr_flag（医院审批标识）,fee_cur_dept(当前)
    extraColumn:
      ad_pay,hosp_appr_flag,adm_cond_code,oprn_appy_id,ipt_no,fee_cur_dept,setl_stas,rx_id,hilist_code
    # his 数据库类型 oracle 、 sqlserver 、db2 （2023年5月4日前的包替换新的程序包 需要配置此项）
    hisDbType:
    #    mysql数据库驱动名称
    driverClassName: com.mysql.cj.jdbc.Driver
    # 数据库连接url
    url: ****************************************************************************************************************************************************************************
#    url: ***************************************************************************************************************************************************************************
    username: audit_his_dev
    password: ENC(Bz+0bzwv1QRa7gdzbg3wda8aZc6DdfyRbu/WdpC0zg9nuqrp/LUd3iv4okM1GyRB)
    # 验证数据库连接的查询语句
    validationQuery: SELECT 'x'

    # oracle数据库驱动名称
#    driverClassName: oracle.jdbc.driver.OracleDriver
#    # 数据库连接url
#    url: *************************************
#    username: c##his_test
#    password: yh$3641
#    # 验证数据库连接的查询语句
#    validationQuery: SELECT 1 from dual

    # sqlserver数据库驱动名称(支持TSL1.2、TSL1.3协议)
#    driverClassName: com.microsoft.sqlserver.jdbc.SQLServerDriver
#    # 数据库连接url
#    url: ***********************************************
#    username: root
#    password: yh$3641
#    # 验证数据库连接的查询语句
#    validationQuery: SELECT 'x'

#    # jTDS连接sqlserver驱动程序类名(支持TSL1.0协议)
#    driverClassName: net.sourceforge.jtds.jdbc.Driver
#    url: ***************************************
#    # SQL Server 登录用户名和密码
#    username: your_username
#    password: your_password
#    # 验证数据库连接的查询语句
#    validationQuery: SELECT 'x'

    # sybase数据库驱动名称
#    driverClassName: net.sourceforge.jtds.jdbc.Driver
#    # 数据库连接url
#    url: **************************************
#    username: root
#    password: yh$3641
#    # 验证数据库连接的查询语句
#    validationQuery: SELECT 'x'

    # db2数据库驱动名称
#    driverClassName: com.ibm.db2.jcc.DB2Driver
#    # 数据库连接url
#    url: ***********************************
#    username: root
#    password: root
#    # 验证数据库连接的查询语句
#    validationQuery: select 1 from sysibm.sysdummy1

    # cache数据库驱动名称
#    driverClassName: com.intersys.jdbc.CacheDriver
#    # 数据库连接url
#    url: ***************************************
#    username: root
#    password: root
#    # 验证数据库连接的查询语句
#    validationQuery: SELECT 'x'

uts:
  okhttp:
    readTimeout: 30000
    writeTimeout: 30000
    connectTimeout: 30000
    addCustomHttpHeaders: N
  log:
    type: db
    scope: default
    scheduled: "0 0 4 1 1/2 ?"
    keep-days: 60
  lock:
    type: db
    duration: 2000
  webservice:
    enable: true
    v21-path: /v21
    name: ZntxServiceWSImplService
    targetNamespace: http://ws.zntx.yinhai.com/
    serviceName: ZntxServiceWSImplService
    portName: ZntxServiceWSImplPort
  # 接口数据是否加密 Y：是 N：否
  crypto: N
# BaseDTO校验方式，0:默认校验方式，1:患者基本信息节点下 只校验住院号和住院次数以及保留费用节点或医嘱节点的默认校验
  validateType: 0

uts.open:
  fixmedins-validate: false
  client-list:
      - access-key: testApp
        security-key: 5A6A675E1D7D54C0A81383BAA558496C2CD1F98A
  route-list:
      - service-id: common
        infno-list:
          - infno: 230101
            bean-id: utsOpenCommonApi
            interface-type: com.yinhai.uts.open.api.common.UtsOpenCommonApi
            method-name: call230101
            sign: false
          - infno: 230200
            bean-id: utsOpenCommonApi
            interface-type: com.yinhai.uts.open.api.common.UtsOpenCommonApi
            method-name: call230200
            sign: false
          - infno: 440200
            bean-id: utsOpenCommonApi
            interface-type: com.yinhai.uts.open.api.common.UtsOpenCommonApi
            method-name: call440200
            sign: false
          - infno: 450100
            bean-id: utsOpenCommonApi
            interface-type: com.yinhai.uts.open.api.common.UtsOpenCommonApi
            method-name: call450100
            sign: false
          - infno: 450200
            bean-id: utsOpenCommonApi
            interface-type: com.yinhai.uts.open.api.common.UtsOpenCommonApi
            method-name: call450200
            sign: false


      - service-id: miim
        infno-list:
          - infno: 990101
            bean-id: utsOpenMiimApi
            interface-type: com.yinhai.uts.open.api.miim.UtsOpenMiimApi
            method-name: call990101
            sign: false
          - infno: 990102
            bean-id: utsOpenMiimApi
            interface-type: com.yinhai.uts.open.api.miim.UtsOpenMiimApi
            method-name: call990102
            sign: false
          - infno: 990103
            bean-id: utsOpenMiimApi
            interface-type: com.yinhai.uts.open.api.miim.UtsOpenMiimApi
            method-name: call990103
            sign: false
          - infno: 990104
            bean-id: utsOpenMiimApi
            interface-type: com.yinhai.uts.open.api.miim.UtsOpenMiimApi
            method-name: call990104
            sign: false
          - infno: 990105
            bean-id: utsOpenMiimApi
            interface-type: com.yinhai.uts.open.api.miim.UtsOpenMiimApi
            method-name: call990105
            sign: false
          - infno: 990106
            bean-id: utsOpenMiimApi
            interface-type: com.yinhai.uts.open.api.miim.UtsOpenMiimApi
            method-name: call990106
            sign: false
          - infno: 990107
            bean-id: utsOpenMiimApi
            interface-type: com.yinhai.uts.open.api.miim.UtsOpenMiimApi
            method-name: call990107
            sign: false
          - infno: 990108
            bean-id: utsOpenMiimApi
            interface-type: com.yinhai.uts.open.api.miim.UtsOpenMiimApi
            method-name: call990108
            sign: false
          - infno: 990109
            bean-id: utsOpenMiimApi
            interface-type: com.yinhai.uts.open.api.miim.UtsOpenMiimApi
            method-name: call990109
            sign: false
          - infno: 990110
            bean-id: utsOpenMiimApi
            interface-type: com.yinhai.uts.open.api.miim.UtsOpenMiimApi
            method-name: call990110
            sign: false
          - infno: 990111
            bean-id: utsOpenMiimApi
            interface-type: com.yinhai.uts.open.api.miim.UtsOpenMiimApi
            method-name: call990111
            sign: false
          - infno: 990112
            bean-id: utsOpenMiimApi
            interface-type: com.yinhai.uts.open.api.miim.UtsOpenMiimApi
            method-name: call990112
            sign: false
          - infno: 990113
            bean-id: utsOpenMiimApi
            interface-type: com.yinhai.uts.open.api.miim.UtsOpenMiimApi
            method-name: call990113
            sign: false
          - infno: 990115
            bean-id: utsOpenMiimApi
            interface-type: com.yinhai.uts.open.api.miim.UtsOpenMiimApi
            method-name: call990115
            sign: false
          - infno: 990121
            bean-id: utsOpenMiimApi
            interface-type: com.yinhai.uts.open.api.miim.UtsOpenMiimApi
            method-name: call990121
            sign: false
          - infno: 990122
            bean-id: utsOpenMiimApi
            interface-type: com.yinhai.uts.open.api.miim.UtsOpenMiimApi
            method-name: call990122
            sign: false
#第三方依赖服务配置
uts.third:
  route-list:
#    - infno: 100001
#      type: local
#      address: utsThirdCommonService
#      method-name: call440100
#      extension:
#      paramResolver:
#    - infno: 980101
#      type: local
#      address: utsThirdCommonService
#      method-name: call980101
#      extension:
#      paramResolver: com.yinhai.uts.third.resolver.T980101ParamResolver
#    - infno: 980102
#      type: http
#      address: http://localhost:8098/api/third/test
#      method-name: dopost
#    - infno: 980103
#      type: webservice
#      address: http://localhost:8098/cxf/api?wsdl
#      method-name: testWs2
#      paramResolver: com.yinhai.uts.third.resolver.T980103ParamResolver
#    - infno: 980104
#      type: http
#      #协同平台测试地址：http://*************:8005/console/template/index.html
#      address: http://*************:8003/engine/rest/uts_test
#      method-name: dopost
#      paramResolver: com.yinhai.uts.transform.third.service.impl.bcp3.BCP3HttpParamResolver
#      extension: {service_key: uts_test, version: 1.0.0}
    - infno: 980001
      type: local
      address: utsThirdCommonService
      method-name: callT980001
      extension:
      paramResolver:
    - infno: 980002
      type: local
      address: utsThirdCommonService
      method-name: callT980002
      extension:
      paramResolver:
    - infno: 980003
      type: local
      address: utsThirdCommonService
      method-name: callT980003
      extension:
      paramResolver:
    - infno: 980004
      type: local
      address: utsThirdCommonService
      method-name: callT980004
      extension:
      paramResolver:
    - infno: 980005
      type: local
      address: utsThirdCommonService
      method-name: callT980005
      extension:
      paramResolver:
    - infno: 980006
      type: local
      address: utsThirdCommonService
      method-name: callT980006
      extension:
      paramResolver:
    - infno: 980007
      type: local
      address: utsThirdCommonService
      method-name: callT980007
      extension:
      paramResolver:
    - infno: 980008
      type: local
      address: utsThirdCommonService
      method-name: callT980008
      extension:
      paramResolver:
    - infno: 980009
      type: local
      address: utsThirdCommonService
      method-name: callT980009
      extension:
      paramResolver:
    - infno: 980010
      type: local
      address: utsThirdCommonService
      method-name: callT980010
      extension:
      paramResolver:
    - infno: 980011
      type: local
      address: utsThirdCommonService
      method-name: callT980011
      extension:
      paramResolver:
    - infno: 980012
      type: local
      address: utsThirdCommonService
      method-name: callT980012
      extension:
      paramResolver:
    - infno: 980013
      type: local
      address: utsThirdCommonService
      method-name: callT980013
      extension:
      paramResolver:
    - infno: 980014
      type: local
      address: utsThirdCommonService
      method-name: callT980014
      extension:
      paramResolver:
    - infno: 980015
      type: local
      address: utsThirdCommonService
      method-name: callT980015
      extension:
      paramResolver:
    - infno: 980016
      type: local
      address: utsThirdCommonService
      method-name: callT980016
      extension:
      paramResolver:
    - infno: 980017
      type: local
      address: utsThirdCommonService
      method-name: callT980017
      extension:
      paramResolver:
    - infno: 980018
      type: local
      address: utsThirdCommonService
      method-name: callT980018
      extension:
      paramResolver:
    - infno: 980019
      type: webservice
#      部署时配置HIS提供的实际地址
      address: http://127.0.0.1:8901/zntx/services/ZntxService?wsdl
      method-name: SEND990108
      paramResolver: com.yinhai.uts.third.resolver.T980019ParamResolver
    - infno: 980020
      type: http
#      部署时配置HIS提供的实际地址
      address: http://127.0.0.1:8081/hiiss/nurfee/testhttp
      method-name: dopost
#980021采用接口方式获取his数据接口配置。当aa01表中DataGetType(获取his数据方式)配置为 0 时使用
    - infno: 980021  # 固定配置,不要动
      type: http  # 接口方式，可选值 webservice,http,local
      address: http://************:8088/rule-mode/web/execRuleMode/exec_rule_mode #部署时配置HIS提供的实际地址
      method-name: dopost # 1,当type配置为 webservice 时，此处配置his实际提供webservice方法名称。
      #2，当type为http时。可选值为 dopost,doget,dopostwithjson,dopostwithfile
      paramResolver: com.yinhai.uts.third.resolver.T980021HttpParamResolver #解析器，http默认解析器为T980021HttpParamResolver，webservice默认解析器为T980021ParamResolver
    # 深圳龙华区人民医院扩展配置（T980021HttpSzlhParamResolver）
    #      extension:
    #        serviceCode: S006201
    #        companyCode: 1009
    #        appCode: C2599EEBCDFC401BB6A7720D09B504DC
    #        hospitalCode: 10
    #        endpointCode: cmp002ed001
    - infno: 980022
      type: local
      address: utsThirdCommonService
      method-name: callT980022
      extension:
      paramResolver:
    - infno: 980023
      type: local
      address: utsThirdCommonService
      method-name: callT980023
      extension:
      paramResolver:
    - infno: 980024
      type: local
      address: utsThirdCommonService
      method-name: callT980024
      extension:
      paramResolver:
    - infno: 980025
      type: local
      address: utsThirdCommonService
      method-name: callT980025
      extension:
      paramResolver:
    - infno: 980026
      type: local
      address: utsThirdCommonService
      method-name: callT980026
      extension:
      paramResolver:
    - infno: 980027
      type: local
      address: utsThirdCommonService
      method-name: callT980027
      extension:
      paramResolver:
    - infno: 980028
      type: local
      address: utsThirdCommonService
      method-name: callT980028
      extension:
      paramResolver:
    - infno: 980029
      type: local
      address: utsThirdCommonService
      method-name: callT980029
      extension:
      paramResolver:
    - infno: 980030
      type: local
      address: utsThirdCommonService
      method-name: callT980030
      extension:
      paramResolver:
    - infno: 980031
      type: local
      address: utsThirdCommonService
      method-name: callT980031
      extension:
      paramResolver:
    - infno: 980032
      type: local
      address: utsThirdCommonService
      method-name: callT980032
      extension:
      paramResolver:
    - infno: 980033
      type: local
      address: utsThirdCommonService
      method-name: callT980033
      extension:
      paramResolver:
    - infno: 980034
      type: local
      address: utsThirdCommonService
      method-name: callT980034
      extension:
      paramResolver:
#银海协同平台配置old代表协同平台2.0，new代表协同平台3.0
  bcp:
    oldNameSpace:
    oldAppNo: uts
    oldSignFlag: 0
    oldPrivateKeyPath: bcp.keystore
    newNameSpace:
    #协同平台注册系统标识
    newAppNo: access
    newSignFlag: 0
    newPrivateKeyPath:

ta404:
  application:
    name: hiiss
    version: 5.3.2-RELEASE
spring:
  profiles:
    active: datasource,dev,uts,redis,websecurity
#    active: datasource,dev,uts,redis,websecurity,eng
#    active: datasource,dev,uts-hkd,redis,websecurity
  application:
    name: ${ta404.application.name}
  main:
    allow-bean-definition-overriding: true
  banner:
    location: banner.txt
  output:
    ansi:
      enabled: always
  session:
    store-type: redis
  redis:
    host: 127.0.0.1
    database: 0
    port: 6379
    password:
    lettuce:
      pool:
        # 最大活跃链接数 默认8
        max-active: 256
        # 最大空闲连接数 默认8
        max-idle: 64
        # 最小空闲连接数 默认0
        min-idle: 10
        # 最大等待时间 单位ms
        max-wait: 10000

        time-between-eviction-runs: 60

  mvc:
    static-path-pattern: /**
    view:
      prefix: /page/
      suffix: .html
#  devtools:
#    restart:
#      enabled: false
  thymeleaf:
    cache: false        # 禁用缓存
    prefix: classpath:/static/page/
#  每晚预审定时器配置,定时时间必须是凌晨0点以后
  quartz:
#    每晚预审配置
    enabled: true
    cron: 0 5 0 * * ? *
#    三目对照配置
    smdzEnabled: false
    smdzCron: 0 4 0 * * ? *
#    结算统计配置
    settleStatisticsEnabled: false
    settleStatisticsCron: 0 2 0 * * ? *
#    审核统计配置
    auditStatisticsEnabled: false
    auditStatisticsCron: 0 1 0 * * ? *
#    清理过期（无用）的审核业务数据配置
    cleanExpireAuditDataEnabled: false
    cleanExpireAuditDataCon: 0 1 2 * * ? *
    #贵医大医保局定时签到签退
    signInAndOutEnabled: false
    signInCon: 0 0 0 * * ? *
    signOutCon: 59 59 23 * * ? *
    diagnoseSyncCron: 0 45 0 * * ? *
    diagnoseSyncEnabled: false

    settleAuditCron: 0 45 0 * * ? *
    settleAuditEnabled: false
  #   每晚预审明细批量入库 线程池大小
  poolNum: 10
#   插入费用明细分组（kc22），400一组，可调整（行数*列数<32767）
  groupNum: 400
#   插入疑点明细分组（kf10），1000一组，可调整（行数*列数<32767）
  groupNumKf10: 500
#   插入疑点明细分组（kf22） （行数*列数<32767）
  groupNumKf22: 600
#   插入审核详情（audit_node） （行数*列数<32767）
  groupAuditNode: 1100
server:
  port: 8081
  servlet:
    session:
      timeout: 7200s
    context-path: /hiiss
  tomcat.resource.allow-caching: false

secret:
  mode: SM2
  pri-path: config/sm2.pri.pem
  pub-path: config/sm2.pub.pem
#广西医保局审核
center:
  encounter:
    fsiPrefix: true
  # 医保局审核接口地址
  url: http://68jdyj.natappfree.cc/fsi/web/api/fsi/callService
#  事前审核交易编号
  beforeHandInfNo: 3101
#  事前智审场景编码
  beforeHandSceneCode: 1,2,3,11
#  事中审核交易编号
  amongHandInfNo: 3102
#  事中智审场景编码
  amongHandSceneCode: 4,7
#  医保局接口版本号
  infver: V1.0
# 接收方系统代码
  recerSysCode: xxxxxx
# 智能审核系统场景编码转中心端审核场景编码，例：智审场景编码（2）转中心场景编码（5），即配置为：2-5，可配多个，以逗号隔开，
  transSceneCode: 1-6,2-5,7-9
# 3101接口调用超时时间，单位毫秒
  timeout3101: 10000
# 3101接口调用超时时间，单位毫秒
  timeout3102: 20000
#医院编码转换 青岛需求
#  properties:
#    transFixmedinsCode:
#      H4103050053A: 'H41030500531'
  # 签到流水号，
  signNo:
  # 中心端医嘱和处方审核医嘱id超长 是否特殊处理
  specialHandle: false
  #患者医保就诊号为空，是否调用医保局审核
  skipCenterAudit: false
#  医保局反馈服务，如果选择取消，则必填理由，默认
#  调用医保局接口请求头配置  yunnan-云南  shenzhen-深圳（需要配置paasid和secretKey） guangdong-广东(需要配置应用编码（paasid）和密钥(secretKey))
#  headerConfig: guangdong
#  账号
#  paasid: sztest_hosp
#  秘钥
#  secretKey: 4poYlzt2jteX1pT3tijh7BqyWH4Lp0zN
#  需要过滤掉的险种类型(该险种类型不用调用医保局接口审核)：
#  filterInsutype: 3995,39914,39913,331
##  需要过滤掉的目录类别
#  filterAke003: 901
#audit:
#  个性化前端页面配置，默认不用配置。
#  可选配置： hx2   华西二院个性化出院审核前端页面
#  localizedPageVersion: hx2
#  cutAke001: false
#  night:
#    每晚审核线程池设置
#    max-pool-size: 8
#    core-pool-size: 8
#  get-data-type:
## his数据获取特殊定义
#    special-list:
#        # 获取数据类型的代码，目前支持
#        # kc21，kc21k1，kc22,kc23,kc37,kc39,kc42,kf06,kf05,smdz
#      - data-code: kc22
#        # 获取方式 0.代表webService方式获取 1.代表连接数据库视图获取
#        # 配置为0时inf-no不能为空，配置为1的时候data-source-key不能为空
#        get-type: 1
#        # 在audit.datasource.dynamic.source-map 定义的动态数据源的key值。
#        data-source-key: ds1

#      - data-code: kc23
#        get-type: 0
#        # 在uts.third.route-list 定义的第三方接口的infno值。
#        inf-no: 980021-2

ta404:
  database:
    transaction:
      log: true
      #启动事务类检测，只检测以下包路径开头的类
      packageStartCheck: com.yinhai.hiiss
  component:
    security:
      db-encoder-type-target: Sm3
  modules:
    refresh-mapper-xml:
      enabled: true
    websecurity:
      data-req:
        req-url-white-list:
          /**/**
      xss-filter:
        xss-white-url-list:
          /**/auditWebservice/**
  log:
    output-type: console,file   # 日志输出目的地，目前支持控制台、文件、kafka，如果需要增加别的输出，可以扩展
    package-level:    # 日志级别，按包名区分
      com.yinhai: ERROR
      org.springframework: INFO
      org.springframework.boot: INFO
      org.hibernate.SQL: ERROR
      org.apache.tomcat: TRACE
      org.apache.catalina: TRACE
      org.eclipse.jetty: TRACE
      com.codingapi: ERROR
    appender-type:    #appender类型，默认提供三种appender（console、file、kafka）
      console:    # 控制台配置
        appender-loader-class: com.yinhai.ta404.core.log.logback.config.ConsoleAppenderLoader    # 控制台配置加载器类名
        format: "%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr %clr(%logger{36}){cyan} %clr(:){faint} %clr(%msg%n){faint}"    #日志输出格式
        use-all-package-level: false    # 是否使用package-level所配置的包和包的日志级别（true：使用，false：不使用）
        monitor-packages:    # 需要把日志输出到控制台的包（注意：在ta404.log.input.level里配置了的包，才能在这里配置）
          - com.yinhai
          - org.springframework
          - org.springframework.boot
          - com.codingapi
      file:    # 文件配置
        appender-loader-class: com.yinhai.ta404.core.log.logback.config.RollingFileAppenderLoader    # 文件配置加载器类名
        file-name-pattern: hiiss_logs/file_%d{yyyy-MM-dd}_%i.log    # 配置日志文件的路径和命名规则
        format: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} : %msg%n"    # 日志输出格式
        max-history: 15    # 文件最大保留天数
        max-file-Size: 10mb    # 单个文件最大容量(单位可以是kb,mb,gb)
        clear-size: 10gb    # 所有文件最大容量(单位可以是kb,mb,gb)
        use-all-package-level: false    # 是否使用package-level所配置的包和包的日志级别（true：使用，false：不使用）
        monitor-packages:    # 同上
          - com.yinhai
          - org.springframework
          - org.springframework.boot
          - com.codingapi
audit:
  queryRule:
    Rule:
      url: /hiiss-backend/mtt/api/ruleSearch
#      url: /hiiss/mtt/api/ruleSearch
  client:
    msg:
      enabled: true
      user: deepblue
      password: yinhai@789456123
      host: **************
      port: 1883
      webAppId: hiissAudit
#      appealDoctorUrl: /hiiss/appealForDoc.html#/appealForDoc
#      nightDoctorUrl: /hiiss/approvalHandle.html#/approvalHandle
#      assistantWindowUrl: /hiiss/assistantLogin.html
#      assistantWorkUrl: /hiiss/assistantWork.html#/assistantWork

      appealDoctorUrl: /hiiss-backend/template/appealForDoc.html#/appealForDoc
      nightDoctorUrl: /hiiss-backend/template/approvalHandle.html#/approvalHandle
      assistantWindowUrl: /hiiss-backend/template/assistantLogin.html
      assistantWorkUrl: /hiiss-backend/template/assistantWork.html#/assistantWork
      assistantLoginUrl: /hiiss-backend/template/assistantLogin.html#assistantLogin
      indexwarningUrl: /hiiss-backend/template/indexWarning.html#/indexWarning
  msgplat:
    enabled: false
    ssystemCode: HIISS
    ticket: FE0BB512319B793F32A246203B7EF4AA
    businessCode: auditMsg
    templateCode: auditMsg
    msgCenterAddress: http://*************:30001/msgcas/services/messageWebService?wsdl

  special:
    Send990108:
      enabled: false
#      HIS提供的接口实际地址
      address: http://127.0.0.1:8901/zntx/services/ZntxService?wsdl
#      连接超时时间
      timeOut: 2000
#   深圳龙华区中心医院计费场景历史费用过滤是否特殊处理
    filterHistoryFee: false
#   深圳龙华区中心医院医嘱场景重复医嘱过滤是否特殊处理
    filterDrord: false
    syncSetlinfoType: default
# 医嘱计费审核提醒界面待审查项目表格显示第三方合理用药系统的药品说明书跳转配置
  instruction:
    # 是否展示
    enabled: false
    # 合理用药厂商 yiyao（杭州逸曜）
    company: yiyao
    # 药品说明书地址
    url: http://**********:9999/zlcx/data_detail.action
  modePlatform:
    modeName: ZSV5MNHISTGSJCXJK
    envType: test
#   是否通过平台推送操作结果
    send990108Enabled: false
# 预上线规则审核引擎地址
  batch:
    engUrl: http://************:8917

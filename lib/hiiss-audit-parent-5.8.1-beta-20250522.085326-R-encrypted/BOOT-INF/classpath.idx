- "ta404-core-5.3.2-RELEASE.jar"
- "spring-boot-starter-data-jpa-2.3.4.RELEASE.jar"
- "spring-boot-starter-aop-2.3.4.RELEASE.jar"
- "spring-boot-starter-jdbc-2.3.4.RELEASE.jar"
- "HikariCP-3.4.5.jar"
- "spring-jdbc-5.2.9.RELEASE.jar"
- "jakarta.transaction-api-1.3.3.jar"
- "jakarta.persistence-api-2.2.3.jar"
- "hibernate-core-5.4.21.Final.jar"
- "javassist-3.24.0-GA.jar"
- "antlr-2.7.7.jar"
- "jandex-2.1.3.Final.jar"
- "dom4j-2.1.3.jar"
- "hibernate-commons-annotations-5.1.0.Final.jar"
- "spring-data-jpa-2.3.4.RELEASE.jar"
- "spring-data-commons-2.3.4.RELEASE.jar"
- "spring-orm-5.2.9.RELEASE.jar"
- "spring-tx-5.2.9.RELEASE.jar"
- "bcprov-jdk15on-1.65.jar"
- "commons-lang3-3.10.jar"
- "commons-codec-1.14.jar"
- "commons-beanutils-1.9.3.jar"
- "zip4j-2.1.3.jar"
- "disruptor-3.4.2.jar"
- "protostuff-core-1.4.0.jar"
- "protostuff-api-1.4.0.jar"
- "protostuff-runtime-1.4.0.jar"
- "protostuff-collectionschema-1.4.0.jar"
- "kryo-4.0.0.jar"
- "reflectasm-1.11.3.jar"
- "minlog-1.3.0.jar"
- "objenesis-2.5.1.jar"
- "kryo-serializers-0.42.jar"
- "pinyin4j-2.5.0.jar"
- "commons-io-2.6.jar"
- "spring-security-web-5.3.4.RELEASE.jar"
- "spring-security-core-5.3.4.RELEASE.jar"
- "spring-aop-5.2.9.RELEASE.jar"
- "spring-beans-5.2.9.RELEASE.jar"
- "spring-expression-5.2.9.RELEASE.jar"
- "spring-web-5.2.9.RELEASE.jar"
- "ta404-core-adapter-5.3.2-RELEASE.jar"
- "ta404-core-adapter-common-5.3.2-RELEASE.jar"
- "spring-boot-starter-web-2.3.4.RELEASE.jar"
- "spring-boot-starter-json-2.3.4.RELEASE.jar"
- "jackson-datatype-jdk8-2.11.2.jar"
- "jackson-datatype-jsr310-2.11.2.jar"
- "jackson-module-parameter-names-2.11.2.jar"
- "spring-boot-starter-tomcat-2.3.4.RELEASE.jar"
- "tomcat-embed-core-9.0.38.jar"
- "tomcat-embed-websocket-9.0.38.jar"
- "spring-webmvc-5.2.9.RELEASE.jar"
- "jackson-databind-2.11.2.jar"
- "jackson-annotations-2.11.2.jar"
- "jackson-core-2.11.2.jar"
- "validation-api-2.0.1.Final.jar"
- "hibernate-validator-6.1.5.Final.jar"
- "jakarta.validation-api-2.0.2.jar"
- "jboss-logging-3.4.1.Final.jar"
- "classmate-1.5.1.jar"
- "swagger-annotations-1.5.20.jar"
- "ta404-module-datasource-5.3.2-RELEASE.jar"
- "ta404-module-mybatis-5.3.2-RELEASE.jar"
- "pagehelper-5.2.0.jar"
- "jsqlparser-3.2.jar"
- "mybatis-spring-2.0.6.jar"
- "commons-dbcp-1.4.jar"
- "commons-pool-1.6.jar"
- "ta404-module-encryption-5.3.2-RELEASE.jar"
- "log4j-api-2.17.1.jar"
- "log4j-to-slf4j-2.17.1.jar"
- "slf4j-api-1.7.30.jar"
- "ta404-module-websecurity-5.3.2-RELEASE.jar"
- "esapi-2.1.0.1.jar"
- "commons-configuration-1.10.jar"
- "commons-lang-2.6.jar"
- "commons-logging-1.1.1.jar"
- "commons-beanutils-core-1.8.3.jar"
- "commons-fileupload-1.3.1.jar"
- "commons-collections-3.2.2.jar"
- "xom-1.2.5.jar"
- "xml-apis-1.3.03.jar"
- "xercesImpl-2.8.0.jar"
- "xalan-2.7.0.jar"
- "bsh-core-2.0b4.jar"
- "antisamy-1.5.3.jar"
- "nekohtml-1.9.22.jar"
- "batik-css-1.8.jar"
- "batik-ext-1.8.jar"
- "batik-util-1.8.jar"
- "xml-apis-ext-1.3.04.jar"
- "mysql-connector-java-8.0.21.jar"
- "spring-boot-starter-2.3.4.RELEASE.jar"
- "spring-boot-2.3.4.RELEASE.jar"
- "spring-boot-autoconfigure-2.3.4.RELEASE.jar"
- "spring-boot-starter-logging-2.3.4.RELEASE.jar"
- "logback-classic-1.2.3.jar"
- "logback-core-1.2.3.jar"
- "jul-to-slf4j-1.7.30.jar"
- "jakarta.annotation-api-1.3.5.jar"
- "snakeyaml-1.26.jar"
- "json-smart-2.3.jar"
- "accessors-smart-1.2.jar"
- "jakarta.xml.bind-api-2.3.3.jar"
- "jakarta.activation-api-1.2.2.jar"
- "byte-buddy-1.10.14.jar"
- "spring-core-5.2.9.RELEASE.jar"
- "spring-jcl-5.2.9.RELEASE.jar"
- "spring-boot-starter-thymeleaf-2.3.4.RELEASE.jar"
- "thymeleaf-spring5-3.0.11.RELEASE.jar"
- "thymeleaf-3.0.11.RELEASE.jar"
- "attoparser-2.0.5.RELEASE.jar"
- "unbescape-1.1.6.RELEASE.jar"
- "thymeleaf-extras-java8time-3.0.4.RELEASE.jar"
- "hiiss-audit-drord-0.0.1-SNAPSHOT.jar"
- "guava-19.0.jar"
- "quartz-2.3.2.jar"
- "mchange-commons-java-0.2.15.jar"
- "hiiss-audit-hiddscg-0.0.1-SNAPSHOT.jar"
- "hiiss-audit-night-0.0.1-SNAPSHOT.jar"
- "hiiss-audit-nurfee-0.0.1-SNAPSHOT.jar"
- "hiiss-audit-opsp-0.0.1-SNAPSHOT.jar"
- "hiiss-audit-opsp-plan-0.0.1-SNAPSHOT.jar"
- "hiiss-audit-otp-0.0.1-SNAPSHOT.jar"
- "hiiss-audit-extra-0.0.1-SNAPSHOT.jar"
- "hiiss-audit-refldept-0.0.1-SNAPSHOT.jar"
- "hiiss-audit-opmd-0.0.1-SNAPSHOT.jar"
- "hiiss-common-0.0.1-SNAPSHOT.jar"
- "spring-context-5.2.9.RELEASE.jar"
- "lombok-1.18.12.jar"
- "hsaf-cache-redis-1.0.0.jar"
- "spring-boot-starter-cache-2.3.4.RELEASE.jar"
- "spring-context-support-5.2.9.RELEASE.jar"
- "jedis-3.3.0.jar"
- "cxf-core-3.4.4.jar"
- "jaxb-runtime-2.3.3.jar"
- "txw2-2.3.3.jar"
- "istack-commons-runtime-3.0.11.jar"
- "jakarta.activation-1.2.2.jar"
- "woodstox-core-6.2.6.jar"
- "stax2-api-4.2.1.jar"
- "xmlschema-core-2.2.5.jar"
- "cxf-rt-frontend-jaxws-3.4.4.jar"
- "xml-resolver-1.2.jar"
- "asm-9.1.jar"
- "cxf-rt-bindings-soap-3.4.4.jar"
- "cxf-rt-wsdl-3.4.4.jar"
- "wsdl4j-1.6.3.jar"
- "cxf-rt-databinding-jaxb-3.4.4.jar"
- "cxf-rt-bindings-xml-3.4.4.jar"
- "cxf-rt-frontend-simple-3.4.4.jar"
- "cxf-rt-ws-addr-3.4.4.jar"
- "cxf-rt-ws-policy-3.4.4.jar"
- "neethi-3.1.1.jar"
- "cxf-rt-transports-http-3.4.4.jar"
- "httpclient-4.5.12.jar"
- "httpcore-4.4.13.jar"
- "commons-pool2-2.8.1.jar"
- "spring-boot-starter-data-redis-2.3.4.RELEASE.jar"
- "spring-data-redis-2.3.4.RELEASE.jar"
- "spring-data-keyvalue-2.3.4.RELEASE.jar"
- "spring-oxm-5.2.9.RELEASE.jar"
- "lettuce-core-5.3.4.RELEASE.jar"
- "netty-common-4.1.52.Final.jar"
- "netty-handler-4.1.52.Final.jar"
- "netty-resolver-4.1.52.Final.jar"
- "netty-buffer-4.1.52.Final.jar"
- "netty-codec-4.1.52.Final.jar"
- "netty-transport-4.1.52.Final.jar"
- "reactor-core-3.3.10.RELEASE.jar"
- "reactive-streams-1.0.3.jar"
- "uts-spring-boot-starter-0.1.1-SNAPSHOT.jar"
- "spring-boot-starter-validation-2.3.4.RELEASE.jar"
- "jakarta.el-3.0.3.jar"
- "uts-open-api-0.0.1-SNAPSHOT.jar"
- "uts-base-api-0.0.1-SNAPSHOT.jar"
- "uts-open-impl-0.0.1-SNAPSHOT.jar"
- "uts-third-impl-0.0.1-SNAPSHOT.jar"
- "uts-transform-third-0.0.1-SNAPSHOT.jar"
- "uts-third-api-0.0.1-SNAPSHOT.jar"
- "uts-transform-core-0.0.1-SNAPSHOT.jar"
- "org.everit.json.schema-1.12.1.jar"
- "json-20190722.jar"
- "commons-validator-1.6.jar"
- "commons-digester-1.8.1.jar"
- "handy-uri-templates-2.1.8.jar"
- "joda-time-2.10.2.jar"
- "re2j-1.3.jar"
- "okhttp-3.14.9.jar"
- "okio-1.17.2.jar"
- "hutool-all-4.5.10.jar"
- "gson-2.8.6.jar"
- "uts-repository-his-0.0.1-SNAPSHOT.jar"
- "uts-repository-transform-0.1.1-SNAPSHOT.jar"
- "uts-transform-open-0.1.1-SNAPSHOT.jar"
- "jaxen-1.2.0.jar"
- "dom4j-1.6.1.jar"
- "annotations-24.0.1.jar"
- "uts-transform-admin-0.0.1-SNAPSHOT.jar"
- "redisson-3.12.1.jar"
- "netty-resolver-dns-4.1.52.Final.jar"
- "netty-codec-dns-4.1.52.Final.jar"
- "cache-api-1.1.1.jar"
- "rxjava-2.2.19.jar"
- "fst-2.57.jar"
- "jackson-dataformat-yaml-2.11.2.jar"
- "jodd-bean-5.0.13.jar"
- "jodd-core-5.0.13.jar"
- "ojdbc6-11.2.0.3.jar"
- "commons-text-1.1.jar"
- "DBPNSPublisher-1.0.0.jar"
- "commons-httpclient-3.1.jar"
- "hiiss-sys-0.0.1-SNAPSHOT.jar"
- "hiiss-audit-settle-0.0.1-SNAPSHOT.jar"
- "postgresql-42.2.25.jar"
- "checker-qual-3.5.0.jar"
- "mssql-jdbc-6.1.0.jre8.jar"
- "azure-keyvault-0.9.3.jar"
- "azure-core-0.9.3.jar"
- "mail-1.4.5.jar"
- "activation-1.1.jar"
- "jersey-client-1.13.jar"
- "jersey-core-1.13.jar"
- "jersey-json-1.13.jar"
- "jettison-1.1.jar"
- "stax-api-1.0.1.jar"
- "jaxb-impl-2.2.3-1.jar"
- "jaxb-api-2.3.1.jar"
- "javax.activation-api-1.2.0.jar"
- "jackson-core-asl-1.9.2.jar"
- "jackson-mapper-asl-1.9.2.jar"
- "jackson-jaxrs-1.9.2.jar"
- "jackson-xc-1.9.2.jar"
- "javax.inject-1.jar"
- "adal4j-1.0.0.jar"
- "oauth2-oidc-sdk-7.1.1.jar"
- "jcip-annotations-1.0-1.jar"
- "content-type-2.0.jar"
- "lang-tag-1.4.4.jar"
- "nimbus-jose-jwt-8.19.jar"
- "javax.mail-1.6.1.jar"
- "jtds-1.3.1.jar"
- "jcc-********.jar"
- "db2jcc-db2jcc4.jar"
- "jasypt-spring-boot-starter-3.0.2.jar"
- "jasypt-spring-boot-3.0.2.jar"
- "jasypt-1.9.3.jar"
- "license_common-1.0.0.jar"
- "truelicense-core-1.32.jar"
- "truelicense-xml-1.32.jar"
- "spring-aspects-5.2.9.RELEASE.jar"
- "aspectjweaver-1.9.6.jar"
- "spring-boot-configuration-processor-2.3.4.RELEASE.jar"
- "fastjson-1.2.83.jar"
- "CacheDB-1.0.0.jar"
- "data-security-1.0-SNAPSHOT.jar"
- "mybatis-3.5.6.jar"
- "druid-1.2.22.jar"
- "ta404-security-patch-1.0.0.jar"

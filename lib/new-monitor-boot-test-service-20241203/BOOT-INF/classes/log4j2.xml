<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="DEBUG" strict="true" name="XMLConfigTest" packages="org.apache.logging.log4j.test">
    <Properties>
        <Property name="monitorLogFileName">logs/monitor.log</Property>
        <Property name="monitorErrorLogFileName">logs/monitorError.log</Property>
        <Property name="rollingPattern">logs/monitor-%d{yyyyMMdd-HH-mm-ss}.log</Property>
        <Property name="performLogFileName">logs/monitorPerformance.log</Property>
        <Property name="logLayoutPattern">%d [%-5level] [%t] %c{1.} [%L] %m%n</Property>
    </Properties>

    <Appenders>
        <!--标准输出-->
        <Appender type="Console" name="standardOutAppender">
            <Layout type="PatternLayout" pattern="${logLayoutPattern}"/>
            <Filters>
                <Filter type="MarkerFilter" marker="performance" onMatch="DENY" onMismatch="NEUTRAL"/>
            </Filters>
        </Appender>

        <!--    日志文件滚动输出    -->
        <Appender type="RollingFile" name="monitorLogAppender" fileName="${monitorLogFileName}"
                  filePattern="${rollingPattern}">
            <Layout type="PatternLayout" pattern="${logLayoutPattern}"/>
            <Filters>
                <Filter type="MarkerFilter" marker="performance" onMatch="DENY" onMismatch="NEUTRAL"/>
            </Filters>
            <Policies>
                <SizeBasedTriggeringPolicy size="10MB"/>
            </Policies>
        </Appender>

        <!--    引擎错误输出    -->
        <Appender type="File" name="monitorErrorAppender" fileName="${monitorErrorLogFileName}">
            <Layout type="PatternLayout" pattern="${logLayoutPattern}"/>
            <Filters>
                <Filter type="MarkerFilter" marker="performance" onMatch="DENY" onMismatch="NEUTRAL"/>
                <Filter type="LevelRangeFilter" minLevel="ERROR" maxLevel="ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
        </Appender>

        <!--    性能测试    -->
        <Appender type="File" name="performAppender" fileName="${performLogFileName}">
            <Layout type="PatternLayout" pattern="${logLayoutPattern}"/>
            <Filters>
                <Filter type="MarkerFilter" marker="performance" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
        </Appender>

    </Appenders>

    <Loggers>
        <!--    默认配置    -->
        <Root level="info">
            <AppenderRef ref="standardOutAppender"/>
            <AppenderRef ref="monitorLogAppender"/>
            <AppenderRef ref="monitorErrorAppender"/>
        </Root>

        <!--    spring  -->
        <Logger name="org.springframework" level="info" additivity="false">
            <AppenderRef ref="standardOutAppender"/>
            <AppenderRef ref="monitorLogAppender"/>
            <AppenderRef ref="monitorErrorAppender"/>
        </Logger>

        <!--    Mybatis相关，非SQL    -->
        <Logger name="org.mybatis" level="info" additivity="false">
            <AppenderRef ref="standardOutAppender"/>
            <AppenderRef ref="monitorLogAppender"/>
            <AppenderRef ref="monitorErrorAppender"/>
        </Logger>

        <!--    阶梯用药SQL    -->
        <Logger name="jtyy" level="debug" additivity="false">
            <AppenderRef ref="standardOutAppender"/>
            <AppenderRef ref="monitorLogAppender"/>
            <AppenderRef ref="monitorErrorAppender"/>
        </Logger>

        <!--    重做启停SQL    -->
        <Logger name="com.yinhai.medicare.monitor.core.dao.mapper" level="debug" additivity="false">
            <AppenderRef ref="standardOutAppender"/>
            <AppenderRef ref="monitorLogAppender"/>
            <AppenderRef ref="monitorErrorAppender"/>
        </Logger>

        <!--引擎logger-->
        <Logger name="com.yinhai.medicare.monitor" level="info" additivity="false">
            <AppenderRef ref="standardOutAppender"/>
            <AppenderRef ref="monitorLogAppender"/>
            <AppenderRef ref="monitorErrorAppender"/>
            <AppenderRef ref="performAppender"/>
        </Logger>

  </Loggers>

</Configuration>

<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.yinhai.medicare</groupId>
		<artifactId>monitor</artifactId>
		<version>20241203-SNAPSHOT</version>
	</parent>
    <packaging>jar</packaging>
	<artifactId>monitor-boot-test-service</artifactId>
	<version>20241203-SNAPSHOT</version>

	<dependencies>
		<!--    数据库驱动    -->
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
		</dependency>
		<dependency>
			<groupId>org.postgresql</groupId>
			<artifactId>postgresql</artifactId>
		</dependency>
		<dependency>
			<groupId>com.oracle.database.jdbc</groupId>
			<artifactId>ojdbc8</artifactId>
		</dependency>
		<dependency>
			<groupId>com.oracle.database.nls</groupId>
			<artifactId>orai18n</artifactId>
		</dependency>
        <!--	引擎控制模块	-->
        <dependency>
			<groupId>com.yinhai.medicare</groupId>
			<artifactId>monitor-web-engine-management</artifactId>
			<version>20241203-SNAPSHOT</version>
		</dependency>
        <!-- 引擎加载模块 -->
        <dependency>
			<groupId>com.yinhai.medicare</groupId>
			<artifactId>monitor-rule-core-engine-service</artifactId>
			<version>20241203-SNAPSHOT</version>
		</dependency>
        <!--	审核数据类型模块	-->
        <dependency>
			<groupId>com.yinhai.medicare</groupId>
			<artifactId>monitor-rule-core-cost</artifactId>
			<version>20241203-SNAPSHOT</version>
		</dependency>
        <dependency>
			<groupId>com.yinhai.medicare</groupId>
			<artifactId>monitor-rule-core-advice</artifactId>
			<version>20241203-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.yinhai.medicare</groupId>
			<artifactId>monitor-rule-core-scheme</artifactId>
			<version>20241203-SNAPSHOT</version>
		</dependency>
        <dependency>
			<groupId>com.yinhai.medicare</groupId>
			<artifactId>monitor-rule-core-bill</artifactId>
			<version>20241203-SNAPSHOT</version>
		</dependency>
        <!--	审核接口模块	-->
        <dependency>
			<groupId>com.yinhai.medicare</groupId>
			<artifactId>monitor-web-cost</artifactId>
			<version>20241203-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.yinhai.medicare</groupId>
			<artifactId>monitor-web-advice</artifactId>
			<version>20241203-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.yinhai.medicare</groupId>
			<artifactId>monitor-web-scheme</artifactId>
			<version>20241203-SNAPSHOT</version>
		</dependency>
        <dependency>
            <groupId>com.yinhai.medicare</groupId>
            <artifactId>monitor-web-bill</artifactId>
            <version>20241203-SNAPSHOT</version>
        </dependency>
        <!--	阶梯用药自动缓存扩展模块	-->
        <dependency>
			<groupId>com.yinhai.medicare</groupId>
			<artifactId>monitor-rule-extension-redis</artifactId>
			<version>20241203-SNAPSHOT</version>
		</dependency>
		<!--    引擎校验组件    -->
		<dependency>
			<groupId>com.yinhai.medicare</groupId>
			<artifactId>monitor-rule-core-validation</artifactId>
			<version>20241203-SNAPSHOT</version>
		</dependency>
        <!--	内嵌tomcat引入	-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>spring-boot-starter-logging</artifactId>
					<groupId>org.springframework.boot</groupId>
				</exclusion>
			</exclusions>
        </dependency>
		<!--    web安全组件    -->
		<dependency>
			<groupId>com.yinhai.medicare</groupId>
			<artifactId>monitor-web-security</artifactId>
			<version>20241203-SNAPSHOT</version>
		</dependency>
        <!-- 日志模块 -->
        <dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-api</artifactId>
		</dependency>
    	<dependency>
    	  <groupId>org.slf4j</groupId>
    	  <artifactId>jul-to-slf4j</artifactId>
    	</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>jcl-over-slf4j</artifactId>
		</dependency>
    	<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-slf4j-impl</artifactId>
    	</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-api</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
		    <artifactId>log4j-core</artifactId>
		</dependency>
		<dependency>
			<groupId>com.yinhai.medicare</groupId>
			<artifactId>monitor-support</artifactId>
			<version>20241203-SNAPSHOT</version>
		</dependency>
	</dependencies>

    <build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-deploy-plugin</artifactId>
				<configuration>
					<skip>true</skip>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
		</plugins>
	</build>

</project>

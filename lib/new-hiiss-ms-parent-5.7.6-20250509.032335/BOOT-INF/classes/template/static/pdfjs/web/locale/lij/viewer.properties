# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=Pagina primma
previous_label=Precedente
next.title=Pagina dÃ²ppo
next_label=PrÃ²scima

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=Pagina
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages=de {{pagesCount}}
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pageNumber}} de {{pagesCount}})

zoom_out.title=Diminoisci zoom
zoom_out_label=Diminoisci zoom
zoom_in.title=Aomenta zoom
zoom_in_label=Aomenta zoom
zoom.title=Zoom
presentation_mode.title=Vanni into mÃ²ddo de prezentaÃ§ion
presentation_mode_label=MÃ²ddo de prezentaÃ§ion
open_file.title=Arvi file
open_file_label=Arvi
print.title=Stanpa
print_label=Stanpa
download.title=Descaregamento
download_label=Descaregamento
bookmark.title=Vixon corente (cÃ²pia Ã² arvi inte 'n neuvo barcon)
bookmark_label=Vixon corente

# Secondary toolbar and context menu
tools.title=Strumenti
tools_label=Strumenti
first_page.title=Vanni a-a primma pagina
first_page.label=Vanni a-a primma pagina
first_page_label=Vanni a-a primma pagina
last_page.title=Vanni a l'urtima pagina
last_page.label=Vanni a l'urtima pagina
last_page_label=Vanni a l'urtima pagina
page_rotate_cw.title=Gia into verso oraio
page_rotate_cw.label=Gia in senso do releuio
page_rotate_cw_label=Gia into verso oraio
page_rotate_ccw.title=Gia into verso antioraio
page_rotate_ccw.label=Gia in senso do releuio a-a reversa
page_rotate_ccw_label=Gia into verso antioraio


# Document properties dialog box
document_properties.title=PropietÃ¦ do documentoâ¦
document_properties_label=PropietÃ¦ do documentoâ¦
document_properties_file_name=Nomme file:
document_properties_file_size=Dimenscion file:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} kB ({{size_b}} byte)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_kb}} MB ({{size_b}} byte)
document_properties_title=Titolo:
document_properties_author=Aoto:
document_properties_subject=Ogetto:
document_properties_keywords=ParÃ²lle ciave:
document_properties_creation_date=DÃ¦ta creaÃ§ion:
document_properties_modification_date=DÃ¦ta cangiamento:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=AotÃ´ originale:
document_properties_producer=ProdutÃ´ PDF:
document_properties_version=Verscion PDF:
document_properties_page_count=Contezzo pagine:
document_properties_close=SÃ¦ra

print_progress_message=Praparo o documento pe-a stanpaâ¦
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=Anulla

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=Ativa/dizativa bara de scianco
toggle_sidebar_notification.title=Cangia bara de lÃ¶o (o documento o contegne di alegÃ¦)
toggle_sidebar_label=Ativa/dizativa bara de scianco
document_outline.title=Fanni vedde o contorno do documento (scicca doggio pe espande/ridue tutti i elementi)
document_outline_label=Contorno do documento
attachments.title=Fanni vedde alegÃ¦
attachments_label=AlegÃ¦
thumbs.title=Mostra miniatue
thumbs_label=Miniatue
findbar.title=Treuva into documento
findbar_label=Treuva

# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=Pagina {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=Miniatua da pagina {{page}}

# Find panel button title and messages
find_input.title=Treuva
find_input.placeholder=Treuva into documentoâ¦
find_previous.title=Treuva a ripetiÃ§ion precedente do testo da Ã§ercÃ¢
find_previous_label=Precedente
find_next.title=Treuva a ripetiÃ§ion dÃ²ppo do testo da Ã§ercÃ¢
find_next_label=Segoente
find_highlight=EvidenÃ§ia
find_match_case_label=Maioscole/minoscole
find_reached_top=Razonto a fin da pagina, continoa da l'iniÃ§io
find_reached_bottom=Razonto l'iniÃ§io da pagina, continoa da-a fin
find_not_found=Testo no trovou

# Error panel labels
error_more_info=CiÃ¹ informaÃ§ioin
error_less_info=Meno informaÃ§ioin
error_close=SÃ¦ra
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (build: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=Mesaggio: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=Stack: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=File: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=Linia: {{line}}
rendering_error=Gh'Ã© stÃ¦to 'n'erÃ´ itno rendering da pagina.

# Predefined zoom values
page_scale_width=Larghessa pagina
page_scale_fit=Adatta a una pagina
page_scale_auto=Zoom aotomatico
page_scale_actual=Dimenscioin efetive
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading_error_indicator=ErÃ´
loading_error=S'Ã© verificou 'n'erÃ´ itno caregamento do PDF.
invalid_file_error=O file PDF o l'Ã© no valido Ã² aroinou.
missing_file_error=O file PDF o no gh'Ã©.
unexpected_response_error=Risposta inprevista do-u server

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 â Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[AnotaÃ§ion: {{type}}]
password_label=Dimme a parÃ²lla segreta pe arvÃ® sto file PDF.
password_invalid=ParÃ²lla segreta sbalia. Preuva torna.
password_ok=Va ben
password_cancel=Anulla

printing_not_supported=AtenÃ§ion: a stanpa a no l'Ã© conpletamente soportÃ¢ da sto navegatÃ´.
printing_not_ready=AtenÃ§ion: o PDF o no l'Ã© ancon caregou conpletamente pe-a stanpa.
web_fonts_disabled=I font do web en dizativÃ¦: inposcibile adeuviÃ¢ i carateri do PDF.
document_colors_not_allowed=No l'Ã© poscibile adeuviÃ¢ i prÃ²pi coÃ® pe-i documenti PDF: l'opÃ§ion do navegatÃ´  âPermetti a-e pagine de Ã§erne i prÃ²pi coÃ® in cangio de quelli inpostÃ¦â a l'Ã© dizativÃ¢.

"use strict";var opMap,FontInspector=function(){var e,t=!1,n="data-font-name";function a(){for(var e=document.querySelectorAll("div["+n+"]"),t=0,a=e.length;t<a;++t){var r=e[t];r.className=""}}function r(){for(var e=document.querySelectorAll("div["+n+"]"),t=0,a=e.length;t<a;++t){var r=e[t];r.className="debuggerHideText"}}function i(e,t){for(var a=document.querySelectorAll("div["+n+"="+e+"]"),r=0,i=a.length;r<i;++r){var d=a[r];d.className=t?"debuggerShowText":"debuggerHideText"}}function d(e){if(e.target.dataset.fontName&&"DIV"===e.target.tagName.toUpperCase())for(var t=e.target.dataset.fontName,n=document.getElementsByTagName("input"),a=0;a<n.length;++a){var r=n[a];r.dataset.fontName===t&&(r.checked=!r.checked,i(t,r.checked),r.scrollIntoView())}}return{id:"FontInspector",name:"Font Inspector",panel:null,manager:null,init:function(t){var n=this.panel;n.setAttribute("style","padding: 5px;");var a=document.createElement("button");a.addEventListener("click",r),a.textContent="Refresh",n.appendChild(a),e=document.createElement("div"),n.appendChild(e)},cleanup:function(){e.textContent=""},enabled:!1,get active(){return t},set active(e){t=e,t?(document.body.addEventListener("click",d,!0),r()):(document.body.removeEventListener("click",d,!0),a())},fontAdded:function(t,n){function a(e,t){for(var n=document.createElement("table"),a=0;a<t.length;a++){var r=document.createElement("tr"),i=document.createElement("td");i.textContent=t[a],r.appendChild(i);var d=document.createElement("td");d.textContent=e[t[a]].toString(),r.appendChild(d),n.appendChild(r)}return n}var d=a(t,["name","type"]),l=t.loadedName,o=document.createElement("div"),s=document.createElement("span");s.textContent=l;var p=document.createElement("a");n?(n=/url\(['"]?([^\)"']+)/.exec(n),p.href=n[1]):t.data&&(n=URL.createObjectURL(new Blob([t.data],{type:t.mimeType})),p.href=n),p.textContent="Download";var c=document.createElement("a");c.href="",c.textContent="Log",c.addEventListener("click",(function(e){e.preventDefault()}));var u=document.createElement("input");u.setAttribute("type","checkbox"),u.dataset.fontName=l,u.addEventListener("click",function(e,t){return function(){i(t,e.checked)}}(u,l)),o.appendChild(u),o.appendChild(s),o.appendChild(document.createTextNode(" ")),o.appendChild(p),o.appendChild(document.createTextNode(" ")),o.appendChild(c),o.appendChild(d),e.appendChild(o),setTimeout((()=>{this.active&&r()}),2e3)}}}(),StepperManager=function(){var e=[],t=null,n=null,a=null,r=Object.create(null);return{id:"Stepper",name:"Stepper",panel:null,manager:null,init:function(e){var i=this;for(var d in this.panel.setAttribute("style","padding: 5px;"),n=document.createElement("div"),a=document.createElement("select"),a.addEventListener("change",(function(e){i.selectStepper(this.value)})),n.appendChild(a),t=document.createElement("div"),this.panel.appendChild(n),this.panel.appendChild(t),sessionStorage.getItem("pdfjsBreakPoints")&&(r=JSON.parse(sessionStorage.getItem("pdfjsBreakPoints"))),opMap=Object.create(null),e.OPS)opMap[e.OPS[d]]=d},cleanup:function(){a.textContent="",t.textContent="",e=[]},enabled:!1,active:!1,create:function(n){var i=document.createElement("div");i.id="stepper"+n,i.setAttribute("hidden",!0),i.className="stepper",t.appendChild(i);var d=document.createElement("option");d.textContent="Page "+(n+1),d.value=n,a.appendChild(d);var l=r[n]||[],o=new Stepper(i,n,l);return e.push(o),1===e.length&&this.selectStepper(n,!1),o},selectStepper:function(t,n){var r;for(t|=0,n&&this.manager.selectPanel(this),r=0;r<e.length;++r){var i=e[r];i.pageIndex===t?i.panel.removeAttribute("hidden"):i.panel.setAttribute("hidden",!0)}var d=a.options;for(r=0;r<d.length;++r){var l=d[r];l.selected=(0|l.value)===t}},saveBreakPoints:function(e,t){r[e]=t,sessionStorage.setItem("pdfjsBreakPoints",JSON.stringify(r))}}}(),Stepper=function(){function e(e,t){var n=document.createElement(e);return t&&(n.textContent=t),n}function t(e){if("string"===typeof e){var n=75;return e.length<=n?e:e.substr(0,n)+"..."}if("object"!==typeof e||null===e)return e;if("length"in e){var a,r,i=[],d=10;for(a=0,r=Math.min(d,e.length);a<r;a++)i.push(t(e[a]));return a<e.length&&i.push("..."),i}var l={};for(var o in e)l[o]=t(e[o]);return l}function n(e,t,n){this.panel=e,this.breakPoint=0,this.nextBreakPoint=null,this.pageIndex=t,this.breakPoints=n,this.currentIdx=-1,this.operatorListIdx=0}return n.prototype={init:function(t){var n=this.panel,a=e("div","c=continue, s=step"),r=e("table");a.appendChild(r),r.cellSpacing=0;var i=e("tr");r.appendChild(i),i.appendChild(e("th","Break")),i.appendChild(e("th","Idx")),i.appendChild(e("th","fn")),i.appendChild(e("th","args")),n.appendChild(a),this.table=r,this.updateOperatorList(t)},updateOperatorList:function(n){var a=this;function r(){var e=+this.dataset.idx;this.checked?a.breakPoints.push(e):a.breakPoints.splice(a.breakPoints.indexOf(e),1),StepperManager.saveBreakPoints(a.pageIndex,a.breakPoints)}var i=15e3;if(!(this.operatorListIdx>i)){for(var d=document.createDocumentFragment(),l=Math.min(i,n.fnArray.length),o=this.operatorListIdx;o<l;o++){var s=e("tr");s.className="line",s.dataset.idx=o,d.appendChild(s);var p=-1!==this.breakPoints.indexOf(o),c=n.argsArray[o]||[],u=e("td"),h=e("input");h.type="checkbox",h.className="points",h.checked=p,h.dataset.idx=o,h.onclick=r,u.appendChild(h),s.appendChild(u),s.appendChild(e("td",o.toString()));var v=opMap[n.fnArray[o]],m=c;if("showText"===v){for(var f=c[0],g=[],b=[],C=0;C<f.length;C++){var x=f[C];"object"===typeof x&&null!==x?b.push(x.fontChar):(b.length>0&&(g.push(b.join("")),b=[]),g.push(x))}b.length>0&&g.push(b.join("")),m=[g]}s.appendChild(e("td",v)),s.appendChild(e("td",JSON.stringify(t(m))))}if(l<n.fnArray.length){s=e("tr");var k=e("td","...");k.colspan=4,d.appendChild(k)}this.operatorListIdx=n.fnArray.length,this.table.appendChild(d)}},getNextBreakPoint:function(){this.breakPoints.sort((function(e,t){return e-t}));for(var e=0;e<this.breakPoints.length;e++)if(this.breakPoints[e]>this.currentIdx)return this.breakPoints[e];return null},breakIt:function(e,t){StepperManager.selectStepper(this.pageIndex,!0);var n=this,a=document;n.currentIdx=e;var r=function(e){switch(e.keyCode){case 83:a.removeEventListener("keydown",r),n.nextBreakPoint=n.currentIdx+1,n.goTo(-1),t();break;case 67:a.removeEventListener("keydown",r);var i=n.getNextBreakPoint();n.nextBreakPoint=i,n.goTo(-1),t();break}};a.addEventListener("keydown",r),n.goTo(e)},goTo:function(e){for(var t=this.panel.getElementsByClassName("line"),n=0,a=t.length;n<a;++n){var r=t[n];(0|r.dataset.idx)===e?(r.style.backgroundColor="rgb(251,250,207)",r.scrollIntoView()):r.style.backgroundColor=null}}},n}(),Stats=function(){var e=[];function t(e){while(e.hasChildNodes())e.removeChild(e.lastChild)}function n(t){for(var n=0,a=e.length;n<a;++n)if(e[n].pageNumber===t)return n;return!1}return{id:"Stats",name:"Stats",panel:null,manager:null,init(e){this.panel.setAttribute("style","padding: 5px;"),e.PDFJS.enableStats=!0},enabled:!1,active:!1,add(a,r){if(r){var i=n(a);if(!1!==i){var d=e[i];this.panel.removeChild(d.div),e.splice(i,1)}var l=document.createElement("div");l.className="stats";var o=document.createElement("div");o.className="title",o.textContent="Page: "+a;var s=document.createElement("div");s.textContent=r.toString(),l.appendChild(o),l.appendChild(s),e.push({pageNumber:a,div:l}),e.sort((function(e,t){return e.pageNumber-t.pageNumber})),t(this.panel);for(var p=0,c=e.length;p<c;++p)this.panel.appendChild(e[p].div)}},cleanup(){e=[],t(this.panel)}}}();window.PDFBug=function(){var e=300,t=[],n=null;return{tools:[FontInspector,StepperManager,Stats],enable(e){var t=!1,n=this.tools;1===e.length&&"all"===e[0]&&(t=!0);for(var a=0;a<n.length;++a){var r=n[a];(t||-1!==e.indexOf(r.id))&&(r.enabled=!0)}t||n.sort((function(t,a){var r=e.indexOf(t.id);r=r<0?n.length:r;var i=e.indexOf(a.id);return i=i<0?n.length:i,r-i}))},init(n,a){var r=document.createElement("div");r.id="PDFBug";var i=document.createElement("div");i.setAttribute("class","controls"),r.appendChild(i);var d=document.createElement("div");d.setAttribute("class","panels"),r.appendChild(d),a.appendChild(r),a.style.right=e+"px";for(var l=this.tools,o=this,s=0;s<l.length;++s){var p=l[s],c=document.createElement("div"),u=document.createElement("button");u.textContent=p.name,u.addEventListener("click",function(e){return function(t){t.preventDefault(),o.selectPanel(e)}}(s)),i.appendChild(u),d.appendChild(c),p.panel=c,p.manager=this,p.enabled?p.init(n):c.textContent=p.name+' is disabled. To enable add  "'+p.id+'" to the pdfBug parameter and refresh (separate multiple by commas).',t.push(u)}this.selectPanel(0)},cleanup(){for(var e=0,t=this.tools.length;e<t;e++)this.tools[e].enabled&&this.tools[e].cleanup()},selectPanel(e){if("number"!==typeof e&&(e=this.tools.indexOf(e)),e!==n){n=e;for(var a=this.tools,r=0;r<a.length;++r)r===e?(t[r].setAttribute("class","active"),a[r].active=!0,a[r].panel.removeAttribute("hidden")):(t[r].setAttribute("class",""),a[r].active=!1,a[r].panel.setAttribute("hidden","true"))}}}}();
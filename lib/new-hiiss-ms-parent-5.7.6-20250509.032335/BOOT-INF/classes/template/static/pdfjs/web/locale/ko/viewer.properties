# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=ì´ì  íì´ì§
previous_label=ì´ì 
next.title=ë¤ì íì´ì§
next_label=ë¤ì

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=íì´ì§
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages=ì ì²´ {{pagesCount}}
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pagesCount}} ì¤ {{pageNumber}})

zoom_out.title=ì¶ì
zoom_out_label=ì¶ì
zoom_in.title=íë
zoom_in_label=íë
zoom.title=í¬ê¸°
presentation_mode.title=ë°í ëª¨ëë¡ ì í
presentation_mode_label=ë°í ëª¨ë
open_file.title=íì¼ ì´ê¸°
open_file_label=ì´ê¸°
print.title=ì¸ì
print_label=ì¸ì
download.title=ë¤ì´ë¡ë
download_label=ë¤ì´ë¡ë
bookmark.title=ì§ê¸ ë³´ì´ë ê·¸ëë¡ (ë³µì¬íê±°ë ì ì°½ì ì´ê¸°)
bookmark_label=ì§ê¸ ë³´ì´ë ê·¸ëë¡

# Secondary toolbar and context menu
tools.title=ëêµ¬
tools_label=ëêµ¬
first_page.title=ì²« íì´ì§ë¡ ì´ë
first_page.label=ì²« íì´ì§ë¡ ì´ë
first_page_label=ì²« íì´ì§ë¡ ì´ë
last_page.title=ë§ì§ë§ íì´ì§ë¡ ì´ë
last_page.label=ë§ì§ë§ íì´ì§ë¡ ì´ë
last_page_label=ë§ì§ë§ íì´ì§ë¡ ì´ë
page_rotate_cw.title=ìê³ë°©í¥ì¼ë¡ íì 
page_rotate_cw.label=ìê³ë°©í¥ì¼ë¡ íì 
page_rotate_cw_label=ìê³ë°©í¥ì¼ë¡ íì 
page_rotate_ccw.title=ìê³ ë°ëë°©í¥ì¼ë¡ íì 
page_rotate_ccw.label=ìê³ ë°ëë°©í¥ì¼ë¡ íì 
page_rotate_ccw_label=ìê³ ë°ëë°©í¥ì¼ë¡ íì 

cursor_text_select_tool.title=íì¤í¸ ì í ëêµ¬ íì±í
cursor_text_select_tool_label=íì¤í¸ ì í ëêµ¬
cursor_hand_tool.title=ì ëêµ¬ íì±í
cursor_hand_tool_label=ì ëêµ¬

# Document properties dialog box
document_properties.title=ë¬¸ì ìì±â¦
document_properties_label=ë¬¸ì ìì±â¦
document_properties_file_name=íì¼ ì´ë¦:
document_properties_file_size=íì¼ ì¬ì´ì¦:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} KB ({{size_b}}ë°ì´í¸)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} MB ({{size_b}}ë°ì´í¸)
document_properties_title=ì ëª©:
document_properties_author=ì ì:
document_properties_subject=ì£¼ì :
document_properties_keywords=í¤ìë:
document_properties_creation_date=ìì±ì¼:
document_properties_modification_date=ìì ì¼:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=ìì±ì:
document_properties_producer=PDF ìì±ê¸°:
document_properties_version=PDF ë²ì :
document_properties_page_count=ì´ íì´ì§:
document_properties_close=ë«ê¸°

print_progress_message=ë¬¸ì ì¶ë ¥ ì¤ë¹ì¤â¦
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=ì·¨ì

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=íìì°½ ì´ê³  ë«ê¸°
toggle_sidebar_notification.title=íìì°½ ì´ê³  ë«ê¸° (ë¬¸ìì ììë¼ì¸ì´ë ì²¨ë¶íì¼ì´ ë¤ì´ìì)
toggle_sidebar_label=íìì°½ ì´ê³  ë«ê¸°
document_outline.title=ë¬¸ì ììë¼ì¸ ë³´ê¸°(ëë¸ í´ë¦­í´ì ëª¨ë  í­ëª© ì´ê³  ë«ê¸°)
document_outline_label=ë¬¸ì ììë¼ì¸
attachments.title=ì²¨ë¶íì¼ ë³´ê¸°
attachments_label=ì²¨ë¶íì¼
thumbs.title=ë¯¸ë¦¬ë³´ê¸°
thumbs_label=ë¯¸ë¦¬ë³´ê¸°
findbar.title=ê²ì
findbar_label=ê²ì

# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title={{page}}ìª½
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas={{page}}ìª½ ë¯¸ë¦¬ë³´ê¸°

# Find panel button title and messages
find_input.title=ì°¾ê¸°
find_input.placeholder=ë¬¸ììì ì°¾ê¸°â¦
find_previous.title=ì§ì  ë¬¸ìì´ì ì¼ì¹íë 1ê° ë¶ë¶ì ê²ì
find_previous_label=ì´ì 
find_next.title=ì§ì  ë¬¸ìì´ì ì¼ì¹íë ë¤ì ë¶ë¶ì ê²ì
find_next_label=ë¤ì
find_highlight=ëª¨ë ê°ì¡° íì
find_match_case_label=ëë¬¸ì/ìë¬¸ì êµ¬ë³
find_reached_top=ë¬¸ì ì²ìê¹ì§ ê²ìíê³  ëì¼ë¡ ëìì ê²ìíìµëë¤.
find_reached_bottom=ë¬¸ì ëê¹ì§ ê²ìíê³  ìì¼ë¡ ëìì ê²ìíìµëë¤.
find_not_found=ê²ì ê²°ê³¼ ìì

# Error panel labels
error_more_info=ì ë³´ ë ë³´ê¸°
error_less_info=ì ë³´ ê°ë¨í ë³´ê¸°
error_close=ë«ê¸°
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (ë¹ë: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=ë©ìì§: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=ì¤í: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=íì¼: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=ì¤ ë²í¸: {{line}}
rendering_error=íì´ì§ë¥¼ ë ëë§íë¤ ì¤ë¥ê° ë¬ìµëë¤.

# Predefined zoom values
page_scale_width=íì´ì§ ëë¹ì ë§ì¶¤
page_scale_fit=íì´ì§ì ë§ì¶¤
page_scale_auto=ììì ë§ì¶¤
page_scale_actual=ì¤ì  í¬ê¸°ì ë§ì¶¤
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading_error_indicator=ì¤ë¥
loading_error=PDFë¥¼ ì½ë ì¤ ì¤ë¥ê° ìê²¼ìµëë¤.
invalid_file_error=ì í¨íì§ ìê±°ë íìë PDF íì¼
missing_file_error=PDF íì¼ì´ ììµëë¤.
unexpected_response_error=ì ì ìë ìë² ìëµìëë¤.

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 â Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[{{type}} ì£¼ì]
password_label=ì´ PDF íì¼ì ì´ ì ìë ìí¸ë¥¼ ìë ¥íì­ìì¤.
password_invalid=ìëª»ë ìí¸ìëë¤. ë¤ì ìëí´ ì£¼ì­ìì¤.
password_ok=íì¸
password_cancel=ì·¨ì

printing_not_supported=ê²½ê³ : ì´ ë¸ë¼ì°ì ë ì¸ìë¥¼ ìì í ì§ìíì§ ììµëë¤.
printing_not_ready=ê²½ê³ : ì´ PDFë¥¼ ì¸ìë¥¼ í  ì ìì ì ëë¡ ì½ì´ë¤ì´ì§ ëª»íìµëë¤.
web_fonts_disabled=ì¹ í°í¸ê° êº¼ì ¸ìì: ë´ì¥ë PDF ê¸ê¼´ì ì¸ ì ììµëë¤.
document_colors_not_allowed=PDF ë¬¸ìì ììì ì°ì§ ëª»íê² ëì´ ìì: 'ì¹ íì´ì§ ìì²´ ìì ì¬ì© íì©'ì´ ë¸ë¼ì°ì ìì êº¼ì ¸ ììµëë¤.

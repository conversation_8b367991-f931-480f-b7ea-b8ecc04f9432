# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.

zoom.title=Zuma/gogela
open_file.title=Bula Faele
open_file_label=Bula

# Secondary toolbar and context menu


# Document properties dialog box
document_properties_file_name=Leina la faele:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_title=Leina:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.

# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)

# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.

# Find panel button title and messages
find_previous.title=Batla tiragalo e e fetileng ya setlhopha sa mafoko
find_next.title=Batla tiragalo e e latelang ya setlhopha sa mafoko
find_not_found=Setlhopha sa mafoko ga se a bonwa

# Error panel labels
error_more_info=Tshedimosetso e Nngwe
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number

# Predefined zoom values
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.

# Loading indicator messages
loading_error_indicator=Phoso

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 â Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
password_ok=Siame

web_fonts_disabled=Mefutatlhaka ya Webo ga e dire: ga e kgone go dirisa mofutatlhaka wa PDF o tsentsweng.

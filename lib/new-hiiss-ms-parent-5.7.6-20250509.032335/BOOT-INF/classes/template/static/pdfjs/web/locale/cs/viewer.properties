# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=PÅejde na pÅedchozÃ­ strÃ¡nku
previous_label=PÅedchozÃ­
next.title=PÅejde na nÃ¡sledujÃ­cÃ­ strÃ¡nku
next_label=DalÅ¡Ã­

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=StrÃ¡nka
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages=z {{pagesCount}}
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pageNumber}} z {{pagesCount}})

zoom_out.title=ZmenÅ¡Ã­ velikost
zoom_out_label=ZmenÅ¡it
zoom_in.title=ZvÄtÅ¡Ã­ velikost
zoom_in_label=ZvÄtÅ¡it
zoom.title=NastavÃ­ velikost
presentation_mode.title=PÅepne do reÅ¾imu prezentace
presentation_mode_label=ReÅ¾im prezentace
open_file.title=OtevÅe soubor
open_file_label=OtevÅÃ­t
print.title=Vytiskne dokument
print_label=Tisk
download.title=StÃ¡hne dokument
download_label=StÃ¡hnout
bookmark.title=SouÄasnÃ½ pohled (kopÃ­rovat nebo otevÅÃ­t v novÃ©m oknÄ)
bookmark_label=SouÄasnÃ½ pohled

# Secondary toolbar and context menu
tools.title=NÃ¡stroje
tools_label=NÃ¡stroje
first_page.title=PÅejde na prvnÃ­ strÃ¡nku
first_page.label=PÅejÃ­t na prvnÃ­ strÃ¡nku
first_page_label=PÅejÃ­t na prvnÃ­ strÃ¡nku
last_page.title=PÅejde na poslednÃ­ strÃ¡nku
last_page.label=PÅejÃ­t na poslednÃ­ strÃ¡nku
last_page_label=PÅejÃ­t na poslednÃ­ strÃ¡nku
page_rotate_cw.title=OtoÄÃ­ po smÄru hodin
page_rotate_cw.label=OtoÄit po smÄru hodin
page_rotate_cw_label=OtoÄit po smÄru hodin
page_rotate_ccw.title=OtoÄÃ­ proti smÄru hodin
page_rotate_ccw.label=OtoÄit proti smÄru hodin
page_rotate_ccw_label=OtoÄit proti smÄru hodin

cursor_text_select_tool.title=PovolÃ­ vÃ½bÄr textu
cursor_text_select_tool_label=VÃ½bÄr textu
cursor_hand_tool.title=PovolÃ­ nÃ¡stroj ruÄiÄka
cursor_hand_tool_label=NÃ¡stroj ruÄiÄka

# Document properties dialog box
document_properties.title=Vlastnosti dokumentuâ¦
document_properties_label=Vlastnosti dokumentuâ¦
document_properties_file_name=NÃ¡zev souboru:
document_properties_file_size=Velikost souboru:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} KB ({{size_b}} bajtÅ¯)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} MB ({{size_b}} bajtÅ¯)
document_properties_title=Nadpis:
document_properties_author=Autor:
document_properties_subject=PÅedmÄt:
document_properties_keywords=KlÃ­ÄovÃ¡ slova:
document_properties_creation_date=Datum vytvoÅenÃ­:
document_properties_modification_date=Datum Ãºpravy:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=VytvoÅil:
document_properties_producer=TvÅ¯rce PDF:
document_properties_version=Verze PDF:
document_properties_page_count=PoÄet strÃ¡nek:
document_properties_close=ZavÅÃ­t

print_progress_message=PÅÃ­prava dokumentu pro tiskâ¦
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}} %
print_progress_close=ZruÅ¡it

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=PostrannÃ­ liÅ¡ta
toggle_sidebar_notification.title=PÅepne postrannÃ­ liÅ¡tu (dokument obsahuje osnovu/pÅÃ­lohy)
toggle_sidebar_label=PostrannÃ­ liÅ¡ta
document_outline.title=ZobrazÃ­ osnovu dokumentu (dvojitÃ© klepnutÃ­ rozbalÃ­/sbalÃ­ vÅ¡echny poloÅ¾ky)
document_outline_label=Osnova dokumentu
attachments.title=ZobrazÃ­ pÅÃ­lohy
attachments_label=PÅÃ­lohy
thumbs.title=ZobrazÃ­ nÃ¡hledy
thumbs_label=NÃ¡hledy
findbar.title=Najde v dokumentu
findbar_label=NajÃ­t

# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=Strana {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=NÃ¡hled strany {{page}}

# Find panel button title and messages
find_input.title=NajÃ­t
find_input.placeholder=NajÃ­t v dokumentuâ¦
find_previous.title=Najde pÅedchozÃ­ vÃ½skyt hledanÃ©ho textu
find_previous_label=PÅedchozÃ­
find_next.title=Najde dalÅ¡Ã­ vÃ½skyt hledanÃ©ho textu
find_next_label=DalÅ¡Ã­
find_highlight=ZvÃ½raznit
find_match_case_label=RozliÅ¡ovat velikost
find_reached_top=DosaÅ¾en zaÄÃ¡tek dokumentu, pokraÄuje se od konce
find_reached_bottom=DosaÅ¾en konec dokumentu, pokraÄuje se od zaÄÃ¡tku
find_not_found=HledanÃ½ text nenalezen

# Error panel labels
error_more_info=VÃ­ce informacÃ­
error_less_info=MÃ©nÄ informacÃ­
error_close=ZavÅÃ­t
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (sestavenÃ­: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=ZprÃ¡va: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=ZÃ¡sobnÃ­k: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=Soubor: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=ÅÃ¡dek: {{line}}
rendering_error=PÅi vykreslovÃ¡nÃ­ strÃ¡nky nastala chyba.

# Predefined zoom values
page_scale_width=Podle Å¡Ã­Åky
page_scale_fit=Podle vÃ½Å¡ky
page_scale_auto=AutomatickÃ¡ velikost
page_scale_actual=SkuteÄnÃ¡ velikost
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading_error_indicator=Chyba
loading_error=PÅi nahrÃ¡vÃ¡nÃ­ PDF nastala chyba.
invalid_file_error=NeplatnÃ½ nebo chybnÃ½ soubor PDF.
missing_file_error=ChybÃ­ soubor PDF.
unexpected_response_error=NeoÄekÃ¡vanÃ¡ odpovÄÄ serveru.

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 â Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[Anotace typu {{type}}]
password_label=Pro otevÅenÃ­ PDF souboru vloÅ¾te heslo.
password_invalid=NeplatnÃ© heslo. Zkuste to znovu.
password_ok=OK
password_cancel=ZruÅ¡it

printing_not_supported=UpozornÄnÃ­: Tisk nenÃ­ v tomto prohlÃ­Å¾eÄi plnÄ podporovÃ¡n.
printing_not_ready=UpozornÄnÃ­: Dokument PDF nenÃ­ kompletnÄ naÄten.
web_fonts_disabled=WebovÃ¡ pÃ­sma jsou zakÃ¡zÃ¡na, proto nenÃ­ moÅ¾nÃ© pouÅ¾Ã­t vloÅ¾enÃ¡ pÃ­sma PDF.
document_colors_not_allowed=PDF dokumenty nemajÃ­ povoleno pouÅ¾Ã­vat vlastnÃ­ barvy: volba 'Povolit strÃ¡nkÃ¡m pouÅ¾Ã­vat vlastnÃ­ barvy' je v prohlÃ­Å¾eÄi deaktivovÃ¡na.

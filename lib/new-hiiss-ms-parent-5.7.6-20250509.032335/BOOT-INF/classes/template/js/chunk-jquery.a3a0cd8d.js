(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[4651],{77387:function(e,t,n){var r,i;e=n.nmd(e);var o=n(57847)["default"];n(32564),
/*!
 * jQuery JavaScript Library v2.2.4
 * http://jquery.com/
 *
 * Includes Sizzle.js
 * http://sizzlejs.com/
 *
 * Copyright jQuery Foundation and other contributors
 * Released under the MIT license
 * http://jquery.org/license
 *
 * Date: 2016-05-20T17:23Z
 */
function(t,n){"object"===o(e)&&"object"===o(e.exports)?e.exports=t.document?n(t,!0):function(e){if(!e.document)throw new Error("jQuery requires a window with a document");return n(e)}:n(t)}("undefined"!==typeof window?window:this,(function(n,s){var a=[],u=n.document,l=a.slice,c=a.concat,f=a.push,p=a.indexOf,d={},h=d.toString,g=d.hasOwnProperty,v={},m="2.2.4",y=function e(t,n){return new e.fn.init(t,n)},x=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,b=/^-ms-/,w=/-([\da-z])/gi,T=function(e,t){return t.toUpperCase()};function C(e){var t=!!e&&"length"in e&&e.length,n=y.type(e);return"function"!==n&&!y.isWindow(e)&&("array"===n||0===t||"number"===typeof t&&t>0&&t-1 in e)}y.fn=y.prototype={jquery:m,constructor:y,selector:"",length:0,toArray:function(){return l.call(this)},get:function(e){return null!=e?e<0?this[e+this.length]:this[e]:l.call(this)},pushStack:function(e){var t=y.merge(this.constructor(),e);return t.prevObject=this,t.context=this.context,t},each:function(e){return y.each(this,e)},map:function(e){return this.pushStack(y.map(this,(function(t,n){return e.call(t,n,t)})))},slice:function(){return this.pushStack(l.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},eq:function(e){var t=this.length,n=+e+(e<0?t:0);return this.pushStack(n>=0&&n<t?[this[n]]:[])},end:function(){return this.prevObject||this.constructor()},push:f,sort:a.sort,splice:a.splice},y.extend=y.fn.extend=function(){var e,t,n,r,i,s,a=arguments[0]||{},u=1,l=arguments.length,c=!1;for("boolean"===typeof a&&(c=a,a=arguments[u]||{},u++),"object"===o(a)||y.isFunction(a)||(a={}),u===l&&(a=this,u--);u<l;u++)if(null!=(e=arguments[u]))for(t in e)n=a[t],r=e[t],a!==r&&(c&&r&&(y.isPlainObject(r)||(i=y.isArray(r)))?(i?(i=!1,s=n&&y.isArray(n)?n:[]):s=n&&y.isPlainObject(n)?n:{},a[t]=y.extend(c,s,r)):void 0!==r&&(a[t]=r));return a},y.extend({expando:"jQuery"+(m+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isFunction:function(e){return"function"===y.type(e)},isArray:Array.isArray,isWindow:function(e){return null!=e&&e===e.window},isNumeric:function(e){var t=e&&e.toString();return!y.isArray(e)&&t-parseFloat(t)+1>=0},isPlainObject:function(e){var t;if("object"!==y.type(e)||e.nodeType||y.isWindow(e))return!1;if(e.constructor&&!g.call(e,"constructor")&&!g.call(e.constructor.prototype||{},"isPrototypeOf"))return!1;for(t in e);return void 0===t||g.call(e,t)},isEmptyObject:function(e){var t;for(t in e)return!1;return!0},type:function(e){return null==e?e+"":"object"===o(e)||"function"===typeof e?d[h.call(e)]||"object":o(e)},globalEval:function(e){var t,n=eval;e=y.trim(e),e&&(1===e.indexOf("use strict")?(t=u.createElement("script"),t.text=e,u.head.appendChild(t).parentNode.removeChild(t)):n(e))},camelCase:function(e){return e.replace(b,"ms-").replace(w,T)},nodeName:function(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()},each:function(e,t){var n,r=0;if(C(e)){for(n=e.length;r<n;r++)if(!1===t.call(e[r],r,e[r]))break}else for(r in e)if(!1===t.call(e[r],r,e[r]))break;return e},trim:function(e){return null==e?"":(e+"").replace(x,"")},makeArray:function(e,t){var n=t||[];return null!=e&&(C(Object(e))?y.merge(n,"string"===typeof e?[e]:e):f.call(n,e)),n},inArray:function(e,t,n){return null==t?-1:p.call(t,e,n)},merge:function(e,t){for(var n=+t.length,r=0,i=e.length;r<n;r++)e[i++]=t[r];return e.length=i,e},grep:function(e,t,n){for(var r,i=[],o=0,s=e.length,a=!n;o<s;o++)r=!t(e[o],o),r!==a&&i.push(e[o]);return i},map:function(e,t,n){var r,i,o=0,s=[];if(C(e))for(r=e.length;o<r;o++)i=t(e[o],o,n),null!=i&&s.push(i);else for(o in e)i=t(e[o],o,n),null!=i&&s.push(i);return c.apply([],s)},guid:1,proxy:function(e,t){var n,r,i;if("string"===typeof t&&(n=e[t],t=e,e=n),y.isFunction(e))return r=l.call(arguments,2),i=function(){return e.apply(t||this,r.concat(l.call(arguments)))},i.guid=e.guid=e.guid||y.guid++,i},now:Date.now,support:v}),"function"===typeof Symbol&&(y.fn[Symbol.iterator]=a[Symbol.iterator]),y.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),(function(e,t){d["[object "+t+"]"]=t.toLowerCase()}));var k=
/*!
   * Sizzle CSS Selector Engine v2.2.1
   * http://sizzlejs.com/
   *
   * Copyright jQuery Foundation and other contributors
   * Released under the MIT license
   * http://jquery.org/license
   *
   * Date: 2015-10-17
   */
function(e){var t,n,r,i,o,s,a,u,l,c,f,p,d,h,g,v,m,y,x,b="sizzle"+1*new Date,w=e.document,T=0,C=0,k=oe(),E=oe(),N=oe(),S=function(e,t){return e===t&&(f=!0),0},D=1<<31,j={}.hasOwnProperty,A=[],q=A.pop,L=A.push,H=A.push,O=A.slice,F=function(e,t){for(var n=0,r=e.length;n<r;n++)if(e[n]===t)return n;return-1},P="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",R="[\\x20\\t\\r\\n\\f]",M="(?:\\\\.|[\\w-]|[^\\x00-\\xa0])+",I="\\["+R+"*("+M+")(?:"+R+"*([*^$|!~]?=)"+R+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+M+"))|)"+R+"*\\]",W=":("+M+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+I+")*)|.*)\\)|)",$=new RegExp(R+"+","g"),B=new RegExp("^"+R+"+|((?:^|[^\\\\])(?:\\\\.)*)"+R+"+$","g"),_=new RegExp("^"+R+"*,"+R+"*"),X=new RegExp("^"+R+"*([>+~]|"+R+")"+R+"*"),z=new RegExp("="+R+"*([^\\]'\"]*?)"+R+"*\\]","g"),U=new RegExp(W),V=new RegExp("^"+M+"$"),Y={ID:new RegExp("^#("+M+")"),CLASS:new RegExp("^\\.("+M+")"),TAG:new RegExp("^("+M+"|[*])"),ATTR:new RegExp("^"+I),PSEUDO:new RegExp("^"+W),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+R+"*(even|odd|(([+-]|)(\\d*)n|)"+R+"*(?:([+-]|)"+R+"*(\\d+)|))"+R+"*\\)|)","i"),bool:new RegExp("^(?:"+P+")$","i"),needsContext:new RegExp("^"+R+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+R+"*((?:-\\d)?\\d*)"+R+"*\\)|)(?=[^-]|$)","i")},G=/^(?:input|select|textarea|button)$/i,Q=/^h\d$/i,J=/^[^{]+\{\s*\[native \w/,K=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,Z=/[+~]/,ee=/'|\\/g,te=new RegExp("\\\\([\\da-f]{1,6}"+R+"?|("+R+")|.)","ig"),ne=function(e,t,n){var r="0x"+t-65536;return r!==r||n?t:r<0?String.fromCharCode(r+65536):String.fromCharCode(r>>10|55296,1023&r|56320)},re=function(){p()};try{H.apply(A=O.call(w.childNodes),w.childNodes),A[w.childNodes.length].nodeType}catch(Ce){H={apply:A.length?function(e,t){L.apply(e,O.call(t))}:function(e,t){var n=e.length,r=0;while(e[n++]=t[r++]);e.length=n-1}}}function ie(e,t,r,i){var o,a,l,c,f,h,m,y,T=t&&t.ownerDocument,C=t?t.nodeType:9;if(r=r||[],"string"!==typeof e||!e||1!==C&&9!==C&&11!==C)return r;if(!i&&((t?t.ownerDocument||t:w)!==d&&p(t),t=t||d,g)){if(11!==C&&(h=K.exec(e)))if(o=h[1]){if(9===C){if(!(l=t.getElementById(o)))return r;if(l.id===o)return r.push(l),r}else if(T&&(l=T.getElementById(o))&&x(t,l)&&l.id===o)return r.push(l),r}else{if(h[2])return H.apply(r,t.getElementsByTagName(e)),r;if((o=h[3])&&n.getElementsByClassName&&t.getElementsByClassName)return H.apply(r,t.getElementsByClassName(o)),r}if(n.qsa&&!N[e+" "]&&(!v||!v.test(e))){if(1!==C)T=t,y=e;else if("object"!==t.nodeName.toLowerCase()){(c=t.getAttribute("id"))?c=c.replace(ee,"\\$&"):t.setAttribute("id",c=b),m=s(e),a=m.length,f=V.test(c)?"#"+c:"[id='"+c+"']";while(a--)m[a]=f+" "+ge(m[a]);y=m.join(","),T=Z.test(e)&&de(t.parentNode)||t}if(y)try{return H.apply(r,T.querySelectorAll(y)),r}catch(k){}finally{c===b&&t.removeAttribute("id")}}}return u(e.replace(B,"$1"),t,r,i)}function oe(){var e=[];function t(n,i){return e.push(n+" ")>r.cacheLength&&delete t[e.shift()],t[n+" "]=i}return t}function se(e){return e[b]=!0,e}function ae(e){var t=d.createElement("div");try{return!!e(t)}catch(Ce){return!1}finally{t.parentNode&&t.parentNode.removeChild(t),t=null}}function ue(e,t){var n=e.split("|"),i=n.length;while(i--)r.attrHandle[n[i]]=t}function le(e,t){var n=t&&e,r=n&&1===e.nodeType&&1===t.nodeType&&(~t.sourceIndex||D)-(~e.sourceIndex||D);if(r)return r;if(n)while(n=n.nextSibling)if(n===t)return-1;return e?1:-1}function ce(e){return function(t){var n=t.nodeName.toLowerCase();return"input"===n&&t.type===e}}function fe(e){return function(t){var n=t.nodeName.toLowerCase();return("input"===n||"button"===n)&&t.type===e}}function pe(e){return se((function(t){return t=+t,se((function(n,r){var i,o=e([],n.length,t),s=o.length;while(s--)n[i=o[s]]&&(n[i]=!(r[i]=n[i]))}))}))}function de(e){return e&&"undefined"!==typeof e.getElementsByTagName&&e}for(t in n=ie.support={},o=ie.isXML=function(e){var t=e&&(e.ownerDocument||e).documentElement;return!!t&&"HTML"!==t.nodeName},p=ie.setDocument=function(e){var t,i,s=e?e.ownerDocument||e:w;return s!==d&&9===s.nodeType&&s.documentElement?(d=s,h=d.documentElement,g=!o(d),(i=d.defaultView)&&i.top!==i&&(i.addEventListener?i.addEventListener("unload",re,!1):i.attachEvent&&i.attachEvent("onunload",re)),n.attributes=ae((function(e){return e.className="i",!e.getAttribute("className")})),n.getElementsByTagName=ae((function(e){return e.appendChild(d.createComment("")),!e.getElementsByTagName("*").length})),n.getElementsByClassName=J.test(d.getElementsByClassName),n.getById=ae((function(e){return h.appendChild(e).id=b,!d.getElementsByName||!d.getElementsByName(b).length})),n.getById?(r.find["ID"]=function(e,t){if("undefined"!==typeof t.getElementById&&g){var n=t.getElementById(e);return n?[n]:[]}},r.filter["ID"]=function(e){var t=e.replace(te,ne);return function(e){return e.getAttribute("id")===t}}):(delete r.find["ID"],r.filter["ID"]=function(e){var t=e.replace(te,ne);return function(e){var n="undefined"!==typeof e.getAttributeNode&&e.getAttributeNode("id");return n&&n.value===t}}),r.find["TAG"]=n.getElementsByTagName?function(e,t){return"undefined"!==typeof t.getElementsByTagName?t.getElementsByTagName(e):n.qsa?t.querySelectorAll(e):void 0}:function(e,t){var n,r=[],i=0,o=t.getElementsByTagName(e);if("*"===e){while(n=o[i++])1===n.nodeType&&r.push(n);return r}return o},r.find["CLASS"]=n.getElementsByClassName&&function(e,t){if("undefined"!==typeof t.getElementsByClassName&&g)return t.getElementsByClassName(e)},m=[],v=[],(n.qsa=J.test(d.querySelectorAll))&&(ae((function(e){h.appendChild(e).innerHTML="<a id='"+b+"'></a><select id='"+b+"-\r\\' msallowcapture=''><option selected=''></option></select>",e.querySelectorAll("[msallowcapture^='']").length&&v.push("[*^$]="+R+"*(?:''|\"\")"),e.querySelectorAll("[selected]").length||v.push("\\["+R+"*(?:value|"+P+")"),e.querySelectorAll("[id~="+b+"-]").length||v.push("~="),e.querySelectorAll(":checked").length||v.push(":checked"),e.querySelectorAll("a#"+b+"+*").length||v.push(".#.+[+~]")})),ae((function(e){var t=d.createElement("input");t.setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),e.querySelectorAll("[name=d]").length&&v.push("name"+R+"*[*^$|!~]?="),e.querySelectorAll(":enabled").length||v.push(":enabled",":disabled"),e.querySelectorAll("*,:x"),v.push(",.*:")}))),(n.matchesSelector=J.test(y=h.matches||h.webkitMatchesSelector||h.mozMatchesSelector||h.oMatchesSelector||h.msMatchesSelector))&&ae((function(e){n.disconnectedMatch=y.call(e,"div"),y.call(e,"[s!='']:x"),m.push("!=",W)})),v=v.length&&new RegExp(v.join("|")),m=m.length&&new RegExp(m.join("|")),t=J.test(h.compareDocumentPosition),x=t||J.test(h.contains)?function(e,t){var n=9===e.nodeType?e.documentElement:e,r=t&&t.parentNode;return e===r||!(!r||1!==r.nodeType||!(n.contains?n.contains(r):e.compareDocumentPosition&&16&e.compareDocumentPosition(r)))}:function(e,t){if(t)while(t=t.parentNode)if(t===e)return!0;return!1},S=t?function(e,t){if(e===t)return f=!0,0;var r=!e.compareDocumentPosition-!t.compareDocumentPosition;return r||(r=(e.ownerDocument||e)===(t.ownerDocument||t)?e.compareDocumentPosition(t):1,1&r||!n.sortDetached&&t.compareDocumentPosition(e)===r?e===d||e.ownerDocument===w&&x(w,e)?-1:t===d||t.ownerDocument===w&&x(w,t)?1:c?F(c,e)-F(c,t):0:4&r?-1:1)}:function(e,t){if(e===t)return f=!0,0;var n,r=0,i=e.parentNode,o=t.parentNode,s=[e],a=[t];if(!i||!o)return e===d?-1:t===d?1:i?-1:o?1:c?F(c,e)-F(c,t):0;if(i===o)return le(e,t);n=e;while(n=n.parentNode)s.unshift(n);n=t;while(n=n.parentNode)a.unshift(n);while(s[r]===a[r])r++;return r?le(s[r],a[r]):s[r]===w?-1:a[r]===w?1:0},d):d},ie.matches=function(e,t){return ie(e,null,null,t)},ie.matchesSelector=function(e,t){if((e.ownerDocument||e)!==d&&p(e),t=t.replace(z,"='$1']"),n.matchesSelector&&g&&!N[t+" "]&&(!m||!m.test(t))&&(!v||!v.test(t)))try{var r=y.call(e,t);if(r||n.disconnectedMatch||e.document&&11!==e.document.nodeType)return r}catch(Ce){}return ie(t,d,null,[e]).length>0},ie.contains=function(e,t){return(e.ownerDocument||e)!==d&&p(e),x(e,t)},ie.attr=function(e,t){(e.ownerDocument||e)!==d&&p(e);var i=r.attrHandle[t.toLowerCase()],o=i&&j.call(r.attrHandle,t.toLowerCase())?i(e,t,!g):void 0;return void 0!==o?o:n.attributes||!g?e.getAttribute(t):(o=e.getAttributeNode(t))&&o.specified?o.value:null},ie.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},ie.uniqueSort=function(e){var t,r=[],i=0,o=0;if(f=!n.detectDuplicates,c=!n.sortStable&&e.slice(0),e.sort(S),f){while(t=e[o++])t===e[o]&&(i=r.push(o));while(i--)e.splice(r[i],1)}return c=null,e},i=ie.getText=function(e){var t,n="",r=0,o=e.nodeType;if(o){if(1===o||9===o||11===o){if("string"===typeof e.textContent)return e.textContent;for(e=e.firstChild;e;e=e.nextSibling)n+=i(e)}else if(3===o||4===o)return e.nodeValue}else while(t=e[r++])n+=i(t);return n},r=ie.selectors={cacheLength:50,createPseudo:se,match:Y,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(te,ne),e[3]=(e[3]||e[4]||e[5]||"").replace(te,ne),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||ie.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&ie.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return Y["CHILD"].test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&U.test(n)&&(t=s(n,!0))&&(t=n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(te,ne).toLowerCase();return"*"===e?function(){return!0}:function(e){return e.nodeName&&e.nodeName.toLowerCase()===t}},CLASS:function(e){var t=k[e+" "];return t||(t=new RegExp("(^|"+R+")"+e+"("+R+"|$)"))&&k(e,(function(e){return t.test("string"===typeof e.className&&e.className||"undefined"!==typeof e.getAttribute&&e.getAttribute("class")||"")}))},ATTR:function(e,t,n){return function(r){var i=ie.attr(r,e);return null==i?"!="===t:!t||(i+="","="===t?i===n:"!="===t?i!==n:"^="===t?n&&0===i.indexOf(n):"*="===t?n&&i.indexOf(n)>-1:"$="===t?n&&i.slice(-n.length)===n:"~="===t?(" "+i.replace($," ")+" ").indexOf(n)>-1:"|="===t&&(i===n||i.slice(0,n.length+1)===n+"-"))}},CHILD:function(e,t,n,r,i){var o="nth"!==e.slice(0,3),s="last"!==e.slice(-4),a="of-type"===t;return 1===r&&0===i?function(e){return!!e.parentNode}:function(t,n,u){var l,c,f,p,d,h,g=o!==s?"nextSibling":"previousSibling",v=t.parentNode,m=a&&t.nodeName.toLowerCase(),y=!u&&!a,x=!1;if(v){if(o){while(g){p=t;while(p=p[g])if(a?p.nodeName.toLowerCase()===m:1===p.nodeType)return!1;h=g="only"===e&&!h&&"nextSibling"}return!0}if(h=[s?v.firstChild:v.lastChild],s&&y){p=v,f=p[b]||(p[b]={}),c=f[p.uniqueID]||(f[p.uniqueID]={}),l=c[e]||[],d=l[0]===T&&l[1],x=d&&l[2],p=d&&v.childNodes[d];while(p=++d&&p&&p[g]||(x=d=0)||h.pop())if(1===p.nodeType&&++x&&p===t){c[e]=[T,d,x];break}}else if(y&&(p=t,f=p[b]||(p[b]={}),c=f[p.uniqueID]||(f[p.uniqueID]={}),l=c[e]||[],d=l[0]===T&&l[1],x=d),!1===x)while(p=++d&&p&&p[g]||(x=d=0)||h.pop())if((a?p.nodeName.toLowerCase()===m:1===p.nodeType)&&++x&&(y&&(f=p[b]||(p[b]={}),c=f[p.uniqueID]||(f[p.uniqueID]={}),c[e]=[T,x]),p===t))break;return x-=i,x===r||x%r===0&&x/r>=0}}},PSEUDO:function(e,t){var n,i=r.pseudos[e]||r.setFilters[e.toLowerCase()]||ie.error("unsupported pseudo: "+e);return i[b]?i(t):i.length>1?(n=[e,e,"",t],r.setFilters.hasOwnProperty(e.toLowerCase())?se((function(e,n){var r,o=i(e,t),s=o.length;while(s--)r=F(e,o[s]),e[r]=!(n[r]=o[s])})):function(e){return i(e,0,n)}):i}},pseudos:{not:se((function(e){var t=[],n=[],r=a(e.replace(B,"$1"));return r[b]?se((function(e,t,n,i){var o,s=r(e,null,i,[]),a=e.length;while(a--)(o=s[a])&&(e[a]=!(t[a]=o))})):function(e,i,o){return t[0]=e,r(t,null,o,n),t[0]=null,!n.pop()}})),has:se((function(e){return function(t){return ie(e,t).length>0}})),contains:se((function(e){return e=e.replace(te,ne),function(t){return(t.textContent||t.innerText||i(t)).indexOf(e)>-1}})),lang:se((function(e){return V.test(e||"")||ie.error("unsupported lang: "+e),e=e.replace(te,ne).toLowerCase(),function(t){var n;do{if(n=g?t.lang:t.getAttribute("xml:lang")||t.getAttribute("lang"))return n=n.toLowerCase(),n===e||0===n.indexOf(e+"-")}while((t=t.parentNode)&&1===t.nodeType);return!1}})),target:function(t){var n=e.location&&e.location.hash;return n&&n.slice(1)===t.id},root:function(e){return e===h},focus:function(e){return e===d.activeElement&&(!d.hasFocus||d.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},enabled:function(e){return!1===e.disabled},disabled:function(e){return!0===e.disabled},checked:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&!!e.checked||"option"===t&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,!0===e.selected},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!r.pseudos["empty"](e)},header:function(e){return Q.test(e.nodeName)},input:function(e){return G.test(e.nodeName)},button:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&"button"===e.type||"button"===t},text:function(e){var t;return"input"===e.nodeName.toLowerCase()&&"text"===e.type&&(null==(t=e.getAttribute("type"))||"text"===t.toLowerCase())},first:pe((function(){return[0]})),last:pe((function(e,t){return[t-1]})),eq:pe((function(e,t,n){return[n<0?n+t:n]})),even:pe((function(e,t){for(var n=0;n<t;n+=2)e.push(n);return e})),odd:pe((function(e,t){for(var n=1;n<t;n+=2)e.push(n);return e})),lt:pe((function(e,t,n){for(var r=n<0?n+t:n;--r>=0;)e.push(r);return e})),gt:pe((function(e,t,n){for(var r=n<0?n+t:n;++r<t;)e.push(r);return e}))}},r.pseudos["nth"]=r.pseudos["eq"],{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})r.pseudos[t]=ce(t);for(t in{submit:!0,reset:!0})r.pseudos[t]=fe(t);function he(){}function ge(e){for(var t=0,n=e.length,r="";t<n;t++)r+=e[t].value;return r}function ve(e,t,n){var r=t.dir,i=n&&"parentNode"===r,o=C++;return t.first?function(t,n,o){while(t=t[r])if(1===t.nodeType||i)return e(t,n,o)}:function(t,n,s){var a,u,l,c=[T,o];if(s){while(t=t[r])if((1===t.nodeType||i)&&e(t,n,s))return!0}else while(t=t[r])if(1===t.nodeType||i){if(l=t[b]||(t[b]={}),u=l[t.uniqueID]||(l[t.uniqueID]={}),(a=u[r])&&a[0]===T&&a[1]===o)return c[2]=a[2];if(u[r]=c,c[2]=e(t,n,s))return!0}}}function me(e){return e.length>1?function(t,n,r){var i=e.length;while(i--)if(!e[i](t,n,r))return!1;return!0}:e[0]}function ye(e,t,n){for(var r=0,i=t.length;r<i;r++)ie(e,t[r],n);return n}function xe(e,t,n,r,i){for(var o,s=[],a=0,u=e.length,l=null!=t;a<u;a++)(o=e[a])&&(n&&!n(o,r,i)||(s.push(o),l&&t.push(a)));return s}function be(e,t,n,r,i,o){return r&&!r[b]&&(r=be(r)),i&&!i[b]&&(i=be(i,o)),se((function(o,s,a,u){var l,c,f,p=[],d=[],h=s.length,g=o||ye(t||"*",a.nodeType?[a]:a,[]),v=!e||!o&&t?g:xe(g,p,e,a,u),m=n?i||(o?e:h||r)?[]:s:v;if(n&&n(v,m,a,u),r){l=xe(m,d),r(l,[],a,u),c=l.length;while(c--)(f=l[c])&&(m[d[c]]=!(v[d[c]]=f))}if(o){if(i||e){if(i){l=[],c=m.length;while(c--)(f=m[c])&&l.push(v[c]=f);i(null,m=[],l,u)}c=m.length;while(c--)(f=m[c])&&(l=i?F(o,f):p[c])>-1&&(o[l]=!(s[l]=f))}}else m=xe(m===s?m.splice(h,m.length):m),i?i(null,s,m,u):H.apply(s,m)}))}function we(e){for(var t,n,i,o=e.length,s=r.relative[e[0].type],a=s||r.relative[" "],u=s?1:0,c=ve((function(e){return e===t}),a,!0),f=ve((function(e){return F(t,e)>-1}),a,!0),p=[function(e,n,r){var i=!s&&(r||n!==l)||((t=n).nodeType?c(e,n,r):f(e,n,r));return t=null,i}];u<o;u++)if(n=r.relative[e[u].type])p=[ve(me(p),n)];else{if(n=r.filter[e[u].type].apply(null,e[u].matches),n[b]){for(i=++u;i<o;i++)if(r.relative[e[i].type])break;return be(u>1&&me(p),u>1&&ge(e.slice(0,u-1).concat({value:" "===e[u-2].type?"*":""})).replace(B,"$1"),n,u<i&&we(e.slice(u,i)),i<o&&we(e=e.slice(i)),i<o&&ge(e))}p.push(n)}return me(p)}function Te(e,t){var n=t.length>0,i=e.length>0,o=function(o,s,a,u,c){var f,h,v,m=0,y="0",x=o&&[],b=[],w=l,C=o||i&&r.find["TAG"]("*",c),k=T+=null==w?1:Math.random()||.1,E=C.length;for(c&&(l=s===d||s||c);y!==E&&null!=(f=C[y]);y++){if(i&&f){h=0,s||f.ownerDocument===d||(p(f),a=!g);while(v=e[h++])if(v(f,s||d,a)){u.push(f);break}c&&(T=k)}n&&((f=!v&&f)&&m--,o&&x.push(f))}if(m+=y,n&&y!==m){h=0;while(v=t[h++])v(x,b,s,a);if(o){if(m>0)while(y--)x[y]||b[y]||(b[y]=q.call(u));b=xe(b)}H.apply(u,b),c&&!o&&b.length>0&&m+t.length>1&&ie.uniqueSort(u)}return c&&(T=k,l=w),x};return n?se(o):o}return he.prototype=r.filters=r.pseudos,r.setFilters=new he,s=ie.tokenize=function(e,t){var n,i,o,s,a,u,l,c=E[e+" "];if(c)return t?0:c.slice(0);a=e,u=[],l=r.preFilter;while(a){for(s in n&&!(i=_.exec(a))||(i&&(a=a.slice(i[0].length)||a),u.push(o=[])),n=!1,(i=X.exec(a))&&(n=i.shift(),o.push({value:n,type:i[0].replace(B," ")}),a=a.slice(n.length)),r.filter)!(i=Y[s].exec(a))||l[s]&&!(i=l[s](i))||(n=i.shift(),o.push({value:n,type:s,matches:i}),a=a.slice(n.length));if(!n)break}return t?a.length:a?ie.error(e):E(e,u).slice(0)},a=ie.compile=function(e,t){var n,r=[],i=[],o=N[e+" "];if(!o){t||(t=s(e)),n=t.length;while(n--)o=we(t[n]),o[b]?r.push(o):i.push(o);o=N(e,Te(i,r)),o.selector=e}return o},u=ie.select=function(e,t,i,o){var u,l,c,f,p,d="function"===typeof e&&e,h=!o&&s(e=d.selector||e);if(i=i||[],1===h.length){if(l=h[0]=h[0].slice(0),l.length>2&&"ID"===(c=l[0]).type&&n.getById&&9===t.nodeType&&g&&r.relative[l[1].type]){if(t=(r.find["ID"](c.matches[0].replace(te,ne),t)||[])[0],!t)return i;d&&(t=t.parentNode),e=e.slice(l.shift().value.length)}u=Y["needsContext"].test(e)?0:l.length;while(u--){if(c=l[u],r.relative[f=c.type])break;if((p=r.find[f])&&(o=p(c.matches[0].replace(te,ne),Z.test(l[0].type)&&de(t.parentNode)||t))){if(l.splice(u,1),e=o.length&&ge(l),!e)return H.apply(i,o),i;break}}}return(d||a(e,h))(o,t,!g,i,!t||Z.test(e)&&de(t.parentNode)||t),i},n.sortStable=b.split("").sort(S).join("")===b,n.detectDuplicates=!!f,p(),n.sortDetached=ae((function(e){return 1&e.compareDocumentPosition(d.createElement("div"))})),ae((function(e){return e.innerHTML="<a href='#'></a>","#"===e.firstChild.getAttribute("href")}))||ue("type|href|height|width",(function(e,t,n){if(!n)return e.getAttribute(t,"type"===t.toLowerCase()?1:2)})),n.attributes&&ae((function(e){return e.innerHTML="<input/>",e.firstChild.setAttribute("value",""),""===e.firstChild.getAttribute("value")}))||ue("value",(function(e,t,n){if(!n&&"input"===e.nodeName.toLowerCase())return e.defaultValue})),ae((function(e){return null==e.getAttribute("disabled")}))||ue(P,(function(e,t,n){var r;if(!n)return!0===e[t]?t.toLowerCase():(r=e.getAttributeNode(t))&&r.specified?r.value:null})),ie}(n);y.find=k,y.expr=k.selectors,y.expr[":"]=y.expr.pseudos,y.uniqueSort=y.unique=k.uniqueSort,y.text=k.getText,y.isXMLDoc=k.isXML,y.contains=k.contains;var E=function(e,t,n){var r=[],i=void 0!==n;while((e=e[t])&&9!==e.nodeType)if(1===e.nodeType){if(i&&y(e).is(n))break;r.push(e)}return r},N=function(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n},S=y.expr.match.needsContext,D=/^<([\w-]+)\s*\/?>(?:<\/\1>|)$/,j=/^.[^:#\[\.,]*$/;function A(e,t,n){if(y.isFunction(t))return y.grep(e,(function(e,r){return!!t.call(e,r,e)!==n}));if(t.nodeType)return y.grep(e,(function(e){return e===t!==n}));if("string"===typeof t){if(j.test(t))return y.filter(t,e,n);t=y.filter(t,e)}return y.grep(e,(function(e){return p.call(t,e)>-1!==n}))}y.filter=function(e,t,n){var r=t[0];return n&&(e=":not("+e+")"),1===t.length&&1===r.nodeType?y.find.matchesSelector(r,e)?[r]:[]:y.find.matches(e,y.grep(t,(function(e){return 1===e.nodeType})))},y.fn.extend({find:function(e){var t,n=this.length,r=[],i=this;if("string"!==typeof e)return this.pushStack(y(e).filter((function(){for(t=0;t<n;t++)if(y.contains(i[t],this))return!0})));for(t=0;t<n;t++)y.find(e,i[t],r);return r=this.pushStack(n>1?y.unique(r):r),r.selector=this.selector?this.selector+" "+e:e,r},filter:function(e){return this.pushStack(A(this,e||[],!1))},not:function(e){return this.pushStack(A(this,e||[],!0))},is:function(e){return!!A(this,"string"===typeof e&&S.test(e)?y(e):e||[],!1).length}});var q,L=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]*))$/,H=y.fn.init=function(e,t,n){var r,i;if(!e)return this;if(n=n||q,"string"===typeof e){if(r="<"===e[0]&&">"===e[e.length-1]&&e.length>=3?[null,e,null]:L.exec(e),!r||!r[1]&&t)return!t||t.jquery?(t||n).find(e):this.constructor(t).find(e);if(r[1]){if(t=t instanceof y?t[0]:t,y.merge(this,y.parseHTML(r[1],t&&t.nodeType?t.ownerDocument||t:u,!0)),D.test(r[1])&&y.isPlainObject(t))for(r in t)y.isFunction(this[r])?this[r](t[r]):this.attr(r,t[r]);return this}return i=u.getElementById(r[2]),i&&i.parentNode&&(this.length=1,this[0]=i),this.context=u,this.selector=e,this}return e.nodeType?(this.context=this[0]=e,this.length=1,this):y.isFunction(e)?void 0!==n.ready?n.ready(e):e(y):(void 0!==e.selector&&(this.selector=e.selector,this.context=e.context),y.makeArray(e,this))};H.prototype=y.fn,q=y(u);var O=/^(?:parents|prev(?:Until|All))/,F={children:!0,contents:!0,next:!0,prev:!0};function P(e,t){while((e=e[t])&&1!==e.nodeType);return e}y.fn.extend({has:function(e){var t=y(e,this),n=t.length;return this.filter((function(){for(var e=0;e<n;e++)if(y.contains(this,t[e]))return!0}))},closest:function(e,t){for(var n,r=0,i=this.length,o=[],s=S.test(e)||"string"!==typeof e?y(e,t||this.context):0;r<i;r++)for(n=this[r];n&&n!==t;n=n.parentNode)if(n.nodeType<11&&(s?s.index(n)>-1:1===n.nodeType&&y.find.matchesSelector(n,e))){o.push(n);break}return this.pushStack(o.length>1?y.uniqueSort(o):o)},index:function(e){return e?"string"===typeof e?p.call(y(e),this[0]):p.call(this,e.jquery?e[0]:e):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(y.uniqueSort(y.merge(this.get(),y(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),y.each({parent:function(e){var t=e.parentNode;return t&&11!==t.nodeType?t:null},parents:function(e){return E(e,"parentNode")},parentsUntil:function(e,t,n){return E(e,"parentNode",n)},next:function(e){return P(e,"nextSibling")},prev:function(e){return P(e,"previousSibling")},nextAll:function(e){return E(e,"nextSibling")},prevAll:function(e){return E(e,"previousSibling")},nextUntil:function(e,t,n){return E(e,"nextSibling",n)},prevUntil:function(e,t,n){return E(e,"previousSibling",n)},siblings:function(e){return N((e.parentNode||{}).firstChild,e)},children:function(e){return N(e.firstChild)},contents:function(e){return e.contentDocument||y.merge([],e.childNodes)}},(function(e,t){y.fn[e]=function(n,r){var i=y.map(this,t,n);return"Until"!==e.slice(-5)&&(r=n),r&&"string"===typeof r&&(i=y.filter(r,i)),this.length>1&&(F[e]||y.uniqueSort(i),O.test(e)&&i.reverse()),this.pushStack(i)}}));var R,M=/\S+/g;function I(e){var t={};return y.each(e.match(M)||[],(function(e,n){t[n]=!0})),t}function W(){u.removeEventListener("DOMContentLoaded",W),n.removeEventListener("load",W),y.ready()}y.Callbacks=function(e){e="string"===typeof e?I(e):y.extend({},e);var t,n,r,i,o=[],s=[],a=-1,u=function(){for(i=e.once,r=t=!0;s.length;a=-1){n=s.shift();while(++a<o.length)!1===o[a].apply(n[0],n[1])&&e.stopOnFalse&&(a=o.length,n=!1)}e.memory||(n=!1),t=!1,i&&(o=n?[]:"")},l={add:function(){return o&&(n&&!t&&(a=o.length-1,s.push(n)),function t(n){y.each(n,(function(n,r){y.isFunction(r)?e.unique&&l.has(r)||o.push(r):r&&r.length&&"string"!==y.type(r)&&t(r)}))}(arguments),n&&!t&&u()),this},remove:function(){return y.each(arguments,(function(e,t){var n;while((n=y.inArray(t,o,n))>-1)o.splice(n,1),n<=a&&a--})),this},has:function(e){return e?y.inArray(e,o)>-1:o.length>0},empty:function(){return o&&(o=[]),this},disable:function(){return i=s=[],o=n="",this},disabled:function(){return!o},lock:function(){return i=s=[],n||(o=n=""),this},locked:function(){return!!i},fireWith:function(e,n){return i||(n=n||[],n=[e,n.slice?n.slice():n],s.push(n),t||u()),this},fire:function(){return l.fireWith(this,arguments),this},fired:function(){return!!r}};return l},y.extend({Deferred:function(e){var t=[["resolve","done",y.Callbacks("once memory"),"resolved"],["reject","fail",y.Callbacks("once memory"),"rejected"],["notify","progress",y.Callbacks("memory")]],n="pending",r={state:function(){return n},always:function(){return i.done(arguments).fail(arguments),this},then:function(){var e=arguments;return y.Deferred((function(n){y.each(t,(function(t,o){var s=y.isFunction(e[t])&&e[t];i[o[1]]((function(){var e=s&&s.apply(this,arguments);e&&y.isFunction(e.promise)?e.promise().progress(n.notify).done(n.resolve).fail(n.reject):n[o[0]+"With"](this===r?n.promise():this,s?[e]:arguments)}))})),e=null})).promise()},promise:function(e){return null!=e?y.extend(e,r):r}},i={};return r.pipe=r.then,y.each(t,(function(e,o){var s=o[2],a=o[3];r[o[1]]=s.add,a&&s.add((function(){n=a}),t[1^e][2].disable,t[2][2].lock),i[o[0]]=function(){return i[o[0]+"With"](this===i?r:this,arguments),this},i[o[0]+"With"]=s.fireWith})),r.promise(i),e&&e.call(i,i),i},when:function(e){var t,n,r,i=0,o=l.call(arguments),s=o.length,a=1!==s||e&&y.isFunction(e.promise)?s:0,u=1===a?e:y.Deferred(),c=function(e,n,r){return function(i){n[e]=this,r[e]=arguments.length>1?l.call(arguments):i,r===t?u.notifyWith(n,r):--a||u.resolveWith(n,r)}};if(s>1)for(t=new Array(s),n=new Array(s),r=new Array(s);i<s;i++)o[i]&&y.isFunction(o[i].promise)?o[i].promise().progress(c(i,n,t)).done(c(i,r,o)).fail(u.reject):--a;return a||u.resolveWith(r,o),u.promise()}}),y.fn.ready=function(e){return y.ready.promise().done(e),this},y.extend({isReady:!1,readyWait:1,holdReady:function(e){e?y.readyWait++:y.ready(!0)},ready:function(e){(!0===e?--y.readyWait:y.isReady)||(y.isReady=!0,!0!==e&&--y.readyWait>0||(R.resolveWith(u,[y]),y.fn.triggerHandler&&(y(u).triggerHandler("ready"),y(u).off("ready"))))}}),y.ready.promise=function(e){return R||(R=y.Deferred(),"complete"===u.readyState||"loading"!==u.readyState&&!u.documentElement.doScroll?n.setTimeout(y.ready):(u.addEventListener("DOMContentLoaded",W),n.addEventListener("load",W))),R.promise(e)},y.ready.promise();var $=function e(t,n,r,i,o,s,a){var u=0,l=t.length,c=null==r;if("object"===y.type(r))for(u in o=!0,r)e(t,n,u,r[u],!0,s,a);else if(void 0!==i&&(o=!0,y.isFunction(i)||(a=!0),c&&(a?(n.call(t,i),n=null):(c=n,n=function(e,t,n){return c.call(y(e),n)})),n))for(;u<l;u++)n(t[u],r,a?i:i.call(t[u],u,n(t[u],r)));return o?t:c?n.call(t):l?n(t[0],r):s},B=function(e){return 1===e.nodeType||9===e.nodeType||!+e.nodeType};function _(){this.expando=y.expando+_.uid++}_.uid=1,_.prototype={register:function(e,t){var n=t||{};return e.nodeType?e[this.expando]=n:Object.defineProperty(e,this.expando,{value:n,writable:!0,configurable:!0}),e[this.expando]},cache:function(e){if(!B(e))return{};var t=e[this.expando];return t||(t={},B(e)&&(e.nodeType?e[this.expando]=t:Object.defineProperty(e,this.expando,{value:t,configurable:!0}))),t},set:function(e,t,n){var r,i=this.cache(e);if("string"===typeof t)i[t]=n;else for(r in t)i[r]=t[r];return i},get:function(e,t){return void 0===t?this.cache(e):e[this.expando]&&e[this.expando][t]},access:function(e,t,n){var r;return void 0===t||t&&"string"===typeof t&&void 0===n?(r=this.get(e,t),void 0!==r?r:this.get(e,y.camelCase(t))):(this.set(e,t,n),void 0!==n?n:t)},remove:function(e,t){var n,r,i,o=e[this.expando];if(void 0!==o){if(void 0===t)this.register(e);else{y.isArray(t)?r=t.concat(t.map(y.camelCase)):(i=y.camelCase(t),t in o?r=[t,i]:(r=i,r=r in o?[r]:r.match(M)||[])),n=r.length;while(n--)delete o[r[n]]}(void 0===t||y.isEmptyObject(o))&&(e.nodeType?e[this.expando]=void 0:delete e[this.expando])}},hasData:function(e){var t=e[this.expando];return void 0!==t&&!y.isEmptyObject(t)}};var X=new _,z=new _,U=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,V=/[A-Z]/g;function Y(e,t,n){var r;if(void 0===n&&1===e.nodeType)if(r="data-"+t.replace(V,"-$&").toLowerCase(),n=e.getAttribute(r),"string"===typeof n){try{n="true"===n||"false"!==n&&("null"===n?null:+n+""===n?+n:U.test(n)?y.parseJSON(n):n)}catch(i){}z.set(e,t,n)}else n=void 0;return n}y.extend({hasData:function(e){return z.hasData(e)||X.hasData(e)},data:function(e,t,n){return z.access(e,t,n)},removeData:function(e,t){z.remove(e,t)},_data:function(e,t,n){return X.access(e,t,n)},_removeData:function(e,t){X.remove(e,t)}}),y.fn.extend({data:function(e,t){var n,r,i,s=this[0],a=s&&s.attributes;if(void 0===e){if(this.length&&(i=z.get(s),1===s.nodeType&&!X.get(s,"hasDataAttrs"))){n=a.length;while(n--)a[n]&&(r=a[n].name,0===r.indexOf("data-")&&(r=y.camelCase(r.slice(5)),Y(s,r,i[r])));X.set(s,"hasDataAttrs",!0)}return i}return"object"===o(e)?this.each((function(){z.set(this,e)})):$(this,(function(t){var n,r;if(s&&void 0===t)return n=z.get(s,e)||z.get(s,e.replace(V,"-$&").toLowerCase()),void 0!==n?n:(r=y.camelCase(e),n=z.get(s,r),void 0!==n?n:(n=Y(s,r,void 0),void 0!==n?n:void 0));r=y.camelCase(e),this.each((function(){var n=z.get(this,r);z.set(this,r,t),e.indexOf("-")>-1&&void 0!==n&&z.set(this,e,t)}))}),null,t,arguments.length>1,null,!0)},removeData:function(e){return this.each((function(){z.remove(this,e)}))}}),y.extend({queue:function(e,t,n){var r;if(e)return t=(t||"fx")+"queue",r=X.get(e,t),n&&(!r||y.isArray(n)?r=X.access(e,t,y.makeArray(n)):r.push(n)),r||[]},dequeue:function(e,t){t=t||"fx";var n=y.queue(e,t),r=n.length,i=n.shift(),o=y._queueHooks(e,t),s=function(){y.dequeue(e,t)};"inprogress"===i&&(i=n.shift(),r--),i&&("fx"===t&&n.unshift("inprogress"),delete o.stop,i.call(e,s,o)),!r&&o&&o.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return X.get(e,n)||X.access(e,n,{empty:y.Callbacks("once memory").add((function(){X.remove(e,[t+"queue",n])}))})}}),y.fn.extend({queue:function(e,t){var n=2;return"string"!==typeof e&&(t=e,e="fx",n--),arguments.length<n?y.queue(this[0],e):void 0===t?this:this.each((function(){var n=y.queue(this,e,t);y._queueHooks(this,e),"fx"===e&&"inprogress"!==n[0]&&y.dequeue(this,e)}))},dequeue:function(e){return this.each((function(){y.dequeue(this,e)}))},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){var n,r=1,i=y.Deferred(),o=this,s=this.length,a=function(){--r||i.resolveWith(o,[o])};"string"!==typeof e&&(t=e,e=void 0),e=e||"fx";while(s--)n=X.get(o[s],e+"queueHooks"),n&&n.empty&&(r++,n.empty.add(a));return a(),i.promise(t)}});var G=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,Q=new RegExp("^(?:([+-])=|)("+G+")([a-z%]*)$","i"),J=["Top","Right","Bottom","Left"],K=function(e,t){return e=t||e,"none"===y.css(e,"display")||!y.contains(e.ownerDocument,e)};function Z(e,t,n,r){var i,o=1,s=20,a=r?function(){return r.cur()}:function(){return y.css(e,t,"")},u=a(),l=n&&n[3]||(y.cssNumber[t]?"":"px"),c=(y.cssNumber[t]||"px"!==l&&+u)&&Q.exec(y.css(e,t));if(c&&c[3]!==l){l=l||c[3],n=n||[],c=+u||1;do{o=o||".5",c/=o,y.style(e,t,c+l)}while(o!==(o=a()/u)&&1!==o&&--s)}return n&&(c=+c||+u||0,i=n[1]?c+(n[1]+1)*n[2]:+n[2],r&&(r.unit=l,r.start=c,r.end=i)),i}var ee=/^(?:checkbox|radio)$/i,te=/<([\w:-]+)/,ne=/^$|\/(?:java|ecma)script/i,re={option:[1,"<select multiple='multiple'>","</select>"],thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};function ie(e,t){var n="undefined"!==typeof e.getElementsByTagName?e.getElementsByTagName(t||"*"):"undefined"!==typeof e.querySelectorAll?e.querySelectorAll(t||"*"):[];return void 0===t||t&&y.nodeName(e,t)?y.merge([e],n):n}function oe(e,t){for(var n=0,r=e.length;n<r;n++)X.set(e[n],"globalEval",!t||X.get(t[n],"globalEval"))}re.optgroup=re.option,re.tbody=re.tfoot=re.colgroup=re.caption=re.thead,re.th=re.td;var se=/<|&#?\w+;/;function ae(e,t,n,r,i){for(var o,s,a,u,l,c,f=t.createDocumentFragment(),p=[],d=0,h=e.length;d<h;d++)if(o=e[d],o||0===o)if("object"===y.type(o))y.merge(p,o.nodeType?[o]:o);else if(se.test(o)){s=s||f.appendChild(t.createElement("div")),a=(te.exec(o)||["",""])[1].toLowerCase(),u=re[a]||re._default,s.innerHTML=u[1]+y.htmlPrefilter(o)+u[2],c=u[0];while(c--)s=s.lastChild;y.merge(p,s.childNodes),s=f.firstChild,s.textContent=""}else p.push(t.createTextNode(o));f.textContent="",d=0;while(o=p[d++])if(r&&y.inArray(o,r)>-1)i&&i.push(o);else if(l=y.contains(o.ownerDocument,o),s=ie(f.appendChild(o),"script"),l&&oe(s),n){c=0;while(o=s[c++])ne.test(o.type||"")&&n.push(o)}return f}(function(){var e=u.createDocumentFragment(),t=e.appendChild(u.createElement("div")),n=u.createElement("input");n.setAttribute("type","radio"),n.setAttribute("checked","checked"),n.setAttribute("name","t"),t.appendChild(n),v.checkClone=t.cloneNode(!0).cloneNode(!0).lastChild.checked,t.innerHTML="<textarea>x</textarea>",v.noCloneChecked=!!t.cloneNode(!0).lastChild.defaultValue})();var ue=/^key/,le=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,ce=/^([^.]*)(?:\.(.+)|)/;function fe(){return!0}function pe(){return!1}function de(){try{return u.activeElement}catch(e){}}function he(e,t,n,r,i,s){var a,u;if("object"===o(t)){for(u in"string"!==typeof n&&(r=r||n,n=void 0),t)he(e,u,n,r,t[u],s);return e}if(null==r&&null==i?(i=n,r=n=void 0):null==i&&("string"===typeof n?(i=r,r=void 0):(i=r,r=n,n=void 0)),!1===i)i=pe;else if(!i)return e;return 1===s&&(a=i,i=function(e){return y().off(e),a.apply(this,arguments)},i.guid=a.guid||(a.guid=y.guid++)),e.each((function(){y.event.add(this,t,i,r,n)}))}y.event={global:{},add:function(e,t,n,r,i){var o,s,a,u,l,c,f,p,d,h,g,v=X.get(e);if(v){n.handler&&(o=n,n=o.handler,i=o.selector),n.guid||(n.guid=y.guid++),(u=v.events)||(u=v.events={}),(s=v.handle)||(s=v.handle=function(t){return"undefined"!==typeof y&&y.event.triggered!==t.type?y.event.dispatch.apply(e,arguments):void 0}),t=(t||"").match(M)||[""],l=t.length;while(l--)a=ce.exec(t[l])||[],d=g=a[1],h=(a[2]||"").split(".").sort(),d&&(f=y.event.special[d]||{},d=(i?f.delegateType:f.bindType)||d,f=y.event.special[d]||{},c=y.extend({type:d,origType:g,data:r,handler:n,guid:n.guid,selector:i,needsContext:i&&y.expr.match.needsContext.test(i),namespace:h.join(".")},o),(p=u[d])||(p=u[d]=[],p.delegateCount=0,f.setup&&!1!==f.setup.call(e,r,h,s)||e.addEventListener&&e.addEventListener(d,s)),f.add&&(f.add.call(e,c),c.handler.guid||(c.handler.guid=n.guid)),i?p.splice(p.delegateCount++,0,c):p.push(c),y.event.global[d]=!0)}},remove:function(e,t,n,r,i){var o,s,a,u,l,c,f,p,d,h,g,v=X.hasData(e)&&X.get(e);if(v&&(u=v.events)){t=(t||"").match(M)||[""],l=t.length;while(l--)if(a=ce.exec(t[l])||[],d=g=a[1],h=(a[2]||"").split(".").sort(),d){f=y.event.special[d]||{},d=(r?f.delegateType:f.bindType)||d,p=u[d]||[],a=a[2]&&new RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"),s=o=p.length;while(o--)c=p[o],!i&&g!==c.origType||n&&n.guid!==c.guid||a&&!a.test(c.namespace)||r&&r!==c.selector&&("**"!==r||!c.selector)||(p.splice(o,1),c.selector&&p.delegateCount--,f.remove&&f.remove.call(e,c));s&&!p.length&&(f.teardown&&!1!==f.teardown.call(e,h,v.handle)||y.removeEvent(e,d,v.handle),delete u[d])}else for(d in u)y.event.remove(e,d+t[l],n,r,!0);y.isEmptyObject(u)&&X.remove(e,"handle events")}},dispatch:function(e){e=y.event.fix(e);var t,n,r,i,o,s=[],a=l.call(arguments),u=(X.get(this,"events")||{})[e.type]||[],c=y.event.special[e.type]||{};if(a[0]=e,e.delegateTarget=this,!c.preDispatch||!1!==c.preDispatch.call(this,e)){s=y.event.handlers.call(this,e,u),t=0;while((i=s[t++])&&!e.isPropagationStopped()){e.currentTarget=i.elem,n=0;while((o=i.handlers[n++])&&!e.isImmediatePropagationStopped())e.rnamespace&&!e.rnamespace.test(o.namespace)||(e.handleObj=o,e.data=o.data,r=((y.event.special[o.origType]||{}).handle||o.handler).apply(i.elem,a),void 0!==r&&!1===(e.result=r)&&(e.preventDefault(),e.stopPropagation()))}return c.postDispatch&&c.postDispatch.call(this,e),e.result}},handlers:function(e,t){var n,r,i,o,s=[],a=t.delegateCount,u=e.target;if(a&&u.nodeType&&("click"!==e.type||isNaN(e.button)||e.button<1))for(;u!==this;u=u.parentNode||this)if(1===u.nodeType&&(!0!==u.disabled||"click"!==e.type)){for(r=[],n=0;n<a;n++)o=t[n],i=o.selector+" ",void 0===r[i]&&(r[i]=o.needsContext?y(i,this).index(u)>-1:y.find(i,this,null,[u]).length),r[i]&&r.push(o);r.length&&s.push({elem:u,handlers:r})}return a<t.length&&s.push({elem:this,handlers:t.slice(a)}),s},props:"altKey bubbles cancelable ctrlKey currentTarget detail eventPhase metaKey relatedTarget shiftKey target timeStamp view which".split(" "),fixHooks:{},keyHooks:{props:"char charCode key keyCode".split(" "),filter:function(e,t){return null==e.which&&(e.which=null!=t.charCode?t.charCode:t.keyCode),e}},mouseHooks:{props:"button buttons clientX clientY offsetX offsetY pageX pageY screenX screenY toElement".split(" "),filter:function(e,t){var n,r,i,o=t.button;return null==e.pageX&&null!=t.clientX&&(n=e.target.ownerDocument||u,r=n.documentElement,i=n.body,e.pageX=t.clientX+(r&&r.scrollLeft||i&&i.scrollLeft||0)-(r&&r.clientLeft||i&&i.clientLeft||0),e.pageY=t.clientY+(r&&r.scrollTop||i&&i.scrollTop||0)-(r&&r.clientTop||i&&i.clientTop||0)),e.which||void 0===o||(e.which=1&o?1:2&o?3:4&o?2:0),e}},fix:function(e){if(e[y.expando])return e;var t,n,r,i=e.type,o=e,s=this.fixHooks[i];s||(this.fixHooks[i]=s=le.test(i)?this.mouseHooks:ue.test(i)?this.keyHooks:{}),r=s.props?this.props.concat(s.props):this.props,e=new y.Event(o),t=r.length;while(t--)n=r[t],e[n]=o[n];return e.target||(e.target=u),3===e.target.nodeType&&(e.target=e.target.parentNode),s.filter?s.filter(e,o):e},special:{load:{noBubble:!0},focus:{trigger:function(){if(this!==de()&&this.focus)return this.focus(),!1},delegateType:"focusin"},blur:{trigger:function(){if(this===de()&&this.blur)return this.blur(),!1},delegateType:"focusout"},click:{trigger:function(){if("checkbox"===this.type&&this.click&&y.nodeName(this,"input"))return this.click(),!1},_default:function(e){return y.nodeName(e.target,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}}},y.removeEvent=function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n)},y.Event=function(e,t){if(!(this instanceof y.Event))return new y.Event(e,t);e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&!1===e.returnValue?fe:pe):this.type=e,t&&y.extend(this,t),this.timeStamp=e&&e.timeStamp||y.now(),this[y.expando]=!0},y.Event.prototype={constructor:y.Event,isDefaultPrevented:pe,isPropagationStopped:pe,isImmediatePropagationStopped:pe,isSimulated:!1,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=fe,e&&!this.isSimulated&&e.preventDefault()},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=fe,e&&!this.isSimulated&&e.stopPropagation()},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=fe,e&&!this.isSimulated&&e.stopImmediatePropagation(),this.stopPropagation()}},y.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},(function(e,t){y.event.special[e]={delegateType:t,bindType:t,handle:function(e){var n,r=this,i=e.relatedTarget,o=e.handleObj;return i&&(i===r||y.contains(r,i))||(e.type=o.origType,n=o.handler.apply(this,arguments),e.type=t),n}}})),y.fn.extend({on:function(e,t,n,r){return he(this,e,t,n,r)},one:function(e,t,n,r){return he(this,e,t,n,r,1)},off:function(e,t,n){var r,i;if(e&&e.preventDefault&&e.handleObj)return r=e.handleObj,y(e.delegateTarget).off(r.namespace?r.origType+"."+r.namespace:r.origType,r.selector,r.handler),this;if("object"===o(e)){for(i in e)this.off(i,t,e[i]);return this}return!1!==t&&"function"!==typeof t||(n=t,t=void 0),!1===n&&(n=pe),this.each((function(){y.event.remove(this,e,n,t)}))}});var ge=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([\w:-]+)[^>]*)\/>/gi,ve=/<script|<style|<link/i,me=/checked\s*(?:[^=]|=\s*.checked.)/i,ye=/^true\/(.*)/,xe=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g;function be(e,t){return y.nodeName(e,"table")&&y.nodeName(11!==t.nodeType?t:t.firstChild,"tr")?e.getElementsByTagName("tbody")[0]||e.appendChild(e.ownerDocument.createElement("tbody")):e}function we(e){return e.type=(null!==e.getAttribute("type"))+"/"+e.type,e}function Te(e){var t=ye.exec(e.type);return t?e.type=t[1]:e.removeAttribute("type"),e}function Ce(e,t){var n,r,i,o,s,a,u,l;if(1===t.nodeType){if(X.hasData(e)&&(o=X.access(e),s=X.set(t,o),l=o.events,l))for(i in delete s.handle,s.events={},l)for(n=0,r=l[i].length;n<r;n++)y.event.add(t,i,l[i][n]);z.hasData(e)&&(a=z.access(e),u=y.extend({},a),z.set(t,u))}}function ke(e,t){var n=t.nodeName.toLowerCase();"input"===n&&ee.test(e.type)?t.checked=e.checked:"input"!==n&&"textarea"!==n||(t.defaultValue=e.defaultValue)}function Ee(e,t,n,r){t=c.apply([],t);var i,o,s,a,u,l,f=0,p=e.length,d=p-1,h=t[0],g=y.isFunction(h);if(g||p>1&&"string"===typeof h&&!v.checkClone&&me.test(h))return e.each((function(i){var o=e.eq(i);g&&(t[0]=h.call(this,i,o.html())),Ee(o,t,n,r)}));if(p&&(i=ae(t,e[0].ownerDocument,!1,e,r),o=i.firstChild,1===i.childNodes.length&&(i=o),o||r)){for(s=y.map(ie(i,"script"),we),a=s.length;f<p;f++)u=i,f!==d&&(u=y.clone(u,!0,!0),a&&y.merge(s,ie(u,"script"))),n.call(e[f],u,f);if(a)for(l=s[s.length-1].ownerDocument,y.map(s,Te),f=0;f<a;f++)u=s[f],ne.test(u.type||"")&&!X.access(u,"globalEval")&&y.contains(l,u)&&(u.src?y._evalUrl&&y._evalUrl(u.src):y.globalEval(u.textContent.replace(xe,"")))}return e}function Ne(e,t,n){for(var r,i=t?y.filter(t,e):e,o=0;null!=(r=i[o]);o++)n||1!==r.nodeType||y.cleanData(ie(r)),r.parentNode&&(n&&y.contains(r.ownerDocument,r)&&oe(ie(r,"script")),r.parentNode.removeChild(r));return e}y.extend({htmlPrefilter:function(e){return e.replace(ge,"<$1></$2>")},clone:function(e,t,n){var r,i,o,s,a=e.cloneNode(!0),u=y.contains(e.ownerDocument,e);if(!v.noCloneChecked&&(1===e.nodeType||11===e.nodeType)&&!y.isXMLDoc(e))for(s=ie(a),o=ie(e),r=0,i=o.length;r<i;r++)ke(o[r],s[r]);if(t)if(n)for(o=o||ie(e),s=s||ie(a),r=0,i=o.length;r<i;r++)Ce(o[r],s[r]);else Ce(e,a);return s=ie(a,"script"),s.length>0&&oe(s,!u&&ie(e,"script")),a},cleanData:function(e){for(var t,n,r,i=y.event.special,o=0;void 0!==(n=e[o]);o++)if(B(n)){if(t=n[X.expando]){if(t.events)for(r in t.events)i[r]?y.event.remove(n,r):y.removeEvent(n,r,t.handle);n[X.expando]=void 0}n[z.expando]&&(n[z.expando]=void 0)}}}),y.fn.extend({domManip:Ee,detach:function(e){return Ne(this,e,!0)},remove:function(e){return Ne(this,e)},text:function(e){return $(this,(function(e){return void 0===e?y.text(this):this.empty().each((function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=e)}))}),null,e,arguments.length)},append:function(){return Ee(this,arguments,(function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=be(this,e);t.appendChild(e)}}))},prepend:function(){return Ee(this,arguments,(function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=be(this,e);t.insertBefore(e,t.firstChild)}}))},before:function(){return Ee(this,arguments,(function(e){this.parentNode&&this.parentNode.insertBefore(e,this)}))},after:function(){return Ee(this,arguments,(function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)}))},empty:function(){for(var e,t=0;null!=(e=this[t]);t++)1===e.nodeType&&(y.cleanData(ie(e,!1)),e.textContent="");return this},clone:function(e,t){return e=null!=e&&e,t=null==t?e:t,this.map((function(){return y.clone(this,e,t)}))},html:function(e){return $(this,(function(e){var t=this[0]||{},n=0,r=this.length;if(void 0===e&&1===t.nodeType)return t.innerHTML;if("string"===typeof e&&!ve.test(e)&&!re[(te.exec(e)||["",""])[1].toLowerCase()]){e=y.htmlPrefilter(e);try{for(;n<r;n++)t=this[n]||{},1===t.nodeType&&(y.cleanData(ie(t,!1)),t.innerHTML=e);t=0}catch(i){}}t&&this.empty().append(e)}),null,e,arguments.length)},replaceWith:function(){var e=[];return Ee(this,arguments,(function(t){var n=this.parentNode;y.inArray(this,e)<0&&(y.cleanData(ie(this)),n&&n.replaceChild(t,this))}),e)}}),y.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},(function(e,t){y.fn[e]=function(e){for(var n,r=[],i=y(e),o=i.length-1,s=0;s<=o;s++)n=s===o?this:this.clone(!0),y(i[s])[t](n),f.apply(r,n.get());return this.pushStack(r)}}));var Se,De={HTML:"block",BODY:"block"};function je(e,t){var n=y(t.createElement(e)).appendTo(t.body),r=y.css(n[0],"display");return n.detach(),r}function Ae(e){var t=u,n=De[e];return n||(n=je(e,t),"none"!==n&&n||(Se=(Se||y("<iframe frameborder='0' width='0' height='0'/>")).appendTo(t.documentElement),t=Se[0].contentDocument,t.write(),t.close(),n=je(e,t),Se.detach()),De[e]=n),n}var qe=/^margin/,Le=new RegExp("^("+G+")(?!px)[a-z%]+$","i"),He=function(e){var t=e.ownerDocument.defaultView;return t&&t.opener||(t=n),t.getComputedStyle(e)},Oe=function(e,t,n,r){var i,o,s={};for(o in t)s[o]=e.style[o],e.style[o]=t[o];for(o in i=n.apply(e,r||[]),t)e.style[o]=s[o];return i},Fe=u.documentElement;function Pe(e,t,n){var r,i,o,s,a=e.style;return n=n||He(e),s=n?n.getPropertyValue(t)||n[t]:void 0,""!==s&&void 0!==s||y.contains(e.ownerDocument,e)||(s=y.style(e,t)),n&&!v.pixelMarginRight()&&Le.test(s)&&qe.test(t)&&(r=a.width,i=a.minWidth,o=a.maxWidth,a.minWidth=a.maxWidth=a.width=s,s=n.width,a.width=r,a.minWidth=i,a.maxWidth=o),void 0!==s?s+"":s}function Re(e,t){return{get:function(){if(!e())return(this.get=t).apply(this,arguments);delete this.get}}}(function(){var e,t,r,i,o=u.createElement("div"),s=u.createElement("div");function a(){s.style.cssText="-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;position:relative;display:block;margin:auto;border:1px;padding:1px;top:1%;width:50%",s.innerHTML="",Fe.appendChild(o);var a=n.getComputedStyle(s);e="1%"!==a.top,i="2px"===a.marginLeft,t="4px"===a.width,s.style.marginRight="50%",r="4px"===a.marginRight,Fe.removeChild(o)}s.style&&(s.style.backgroundClip="content-box",s.cloneNode(!0).style.backgroundClip="",v.clearCloneStyle="content-box"===s.style.backgroundClip,o.style.cssText="border:0;width:8px;height:0;top:0;left:-9999px;padding:0;margin-top:1px;position:absolute",o.appendChild(s),y.extend(v,{pixelPosition:function(){return a(),e},boxSizingReliable:function(){return null==t&&a(),t},pixelMarginRight:function(){return null==t&&a(),r},reliableMarginLeft:function(){return null==t&&a(),i},reliableMarginRight:function(){var e,t=s.appendChild(u.createElement("div"));return t.style.cssText=s.style.cssText="-webkit-box-sizing:content-box;box-sizing:content-box;display:block;margin:0;border:0;padding:0",t.style.marginRight=t.style.width="0",s.style.width="1px",Fe.appendChild(o),e=!parseFloat(n.getComputedStyle(t).marginRight),Fe.removeChild(o),s.removeChild(t),e}}))})();var Me=/^(none|table(?!-c[ea]).+)/,Ie={position:"absolute",visibility:"hidden",display:"block"},We={letterSpacing:"0",fontWeight:"400"},$e=["Webkit","O","Moz","ms"],Be=u.createElement("div").style;function _e(e){if(e in Be)return e;var t=e[0].toUpperCase()+e.slice(1),n=$e.length;while(n--)if(e=$e[n]+t,e in Be)return e}function Xe(e,t,n){var r=Q.exec(t);return r?Math.max(0,r[2]-(n||0))+(r[3]||"px"):t}function ze(e,t,n,r,i){for(var o=n===(r?"border":"content")?4:"width"===t?1:0,s=0;o<4;o+=2)"margin"===n&&(s+=y.css(e,n+J[o],!0,i)),r?("content"===n&&(s-=y.css(e,"padding"+J[o],!0,i)),"margin"!==n&&(s-=y.css(e,"border"+J[o]+"Width",!0,i))):(s+=y.css(e,"padding"+J[o],!0,i),"padding"!==n&&(s+=y.css(e,"border"+J[o]+"Width",!0,i)));return s}function Ue(e,t,n){var r=!0,i="width"===t?e.offsetWidth:e.offsetHeight,o=He(e),s="border-box"===y.css(e,"boxSizing",!1,o);if(i<=0||null==i){if(i=Pe(e,t,o),(i<0||null==i)&&(i=e.style[t]),Le.test(i))return i;r=s&&(v.boxSizingReliable()||i===e.style[t]),i=parseFloat(i)||0}return i+ze(e,t,n||(s?"border":"content"),r,o)+"px"}function Ve(e,t){for(var n,r,i,o=[],s=0,a=e.length;s<a;s++)r=e[s],r.style&&(o[s]=X.get(r,"olddisplay"),n=r.style.display,t?(o[s]||"none"!==n||(r.style.display=""),""===r.style.display&&K(r)&&(o[s]=X.access(r,"olddisplay",Ae(r.nodeName)))):(i=K(r),"none"===n&&i||X.set(r,"olddisplay",i?n:y.css(r,"display"))));for(s=0;s<a;s++)r=e[s],r.style&&(t&&"none"!==r.style.display&&""!==r.style.display||(r.style.display=t?o[s]||"":"none"));return e}function Ye(e,t,n,r,i){return new Ye.prototype.init(e,t,n,r,i)}y.extend({cssHooks:{opacity:{get:function(e,t){if(t){var n=Pe(e,"opacity");return""===n?"1":n}}}},cssNumber:{animationIterationCount:!0,columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{float:"cssFloat"},style:function(e,t,n,r){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var i,s,a,u=y.camelCase(t),l=e.style;if(t=y.cssProps[u]||(y.cssProps[u]=_e(u)||u),a=y.cssHooks[t]||y.cssHooks[u],void 0===n)return a&&"get"in a&&void 0!==(i=a.get(e,!1,r))?i:l[t];s=o(n),"string"===s&&(i=Q.exec(n))&&i[1]&&(n=Z(e,t,i),s="number"),null!=n&&n===n&&("number"===s&&(n+=i&&i[3]||(y.cssNumber[u]?"":"px")),v.clearCloneStyle||""!==n||0!==t.indexOf("background")||(l[t]="inherit"),a&&"set"in a&&void 0===(n=a.set(e,n,r))||(l[t]=n))}},css:function(e,t,n,r){var i,o,s,a=y.camelCase(t);return t=y.cssProps[a]||(y.cssProps[a]=_e(a)||a),s=y.cssHooks[t]||y.cssHooks[a],s&&"get"in s&&(i=s.get(e,!0,n)),void 0===i&&(i=Pe(e,t,r)),"normal"===i&&t in We&&(i=We[t]),""===n||n?(o=parseFloat(i),!0===n||isFinite(o)?o||0:i):i}}),y.each(["height","width"],(function(e,t){y.cssHooks[t]={get:function(e,n,r){if(n)return Me.test(y.css(e,"display"))&&0===e.offsetWidth?Oe(e,Ie,(function(){return Ue(e,t,r)})):Ue(e,t,r)},set:function(e,n,r){var i,o=r&&He(e),s=r&&ze(e,t,r,"border-box"===y.css(e,"boxSizing",!1,o),o);return s&&(i=Q.exec(n))&&"px"!==(i[3]||"px")&&(e.style[t]=n,n=y.css(e,t)),Xe(e,n,s)}}})),y.cssHooks.marginLeft=Re(v.reliableMarginLeft,(function(e,t){if(t)return(parseFloat(Pe(e,"marginLeft"))||e.getBoundingClientRect().left-Oe(e,{marginLeft:0},(function(){return e.getBoundingClientRect().left})))+"px"})),y.cssHooks.marginRight=Re(v.reliableMarginRight,(function(e,t){if(t)return Oe(e,{display:"inline-block"},Pe,[e,"marginRight"])})),y.each({margin:"",padding:"",border:"Width"},(function(e,t){y.cssHooks[e+t]={expand:function(n){for(var r=0,i={},o="string"===typeof n?n.split(" "):[n];r<4;r++)i[e+J[r]+t]=o[r]||o[r-2]||o[0];return i}},qe.test(e)||(y.cssHooks[e+t].set=Xe)})),y.fn.extend({css:function(e,t){return $(this,(function(e,t,n){var r,i,o={},s=0;if(y.isArray(t)){for(r=He(e),i=t.length;s<i;s++)o[t[s]]=y.css(e,t[s],!1,r);return o}return void 0!==n?y.style(e,t,n):y.css(e,t)}),e,t,arguments.length>1)},show:function(){return Ve(this,!0)},hide:function(){return Ve(this)},toggle:function(e){return"boolean"===typeof e?e?this.show():this.hide():this.each((function(){K(this)?y(this).show():y(this).hide()}))}}),y.Tween=Ye,Ye.prototype={constructor:Ye,init:function(e,t,n,r,i,o){this.elem=e,this.prop=n,this.easing=i||y.easing._default,this.options=t,this.start=this.now=this.cur(),this.end=r,this.unit=o||(y.cssNumber[n]?"":"px")},cur:function(){var e=Ye.propHooks[this.prop];return e&&e.get?e.get(this):Ye.propHooks._default.get(this)},run:function(e){var t,n=Ye.propHooks[this.prop];return this.options.duration?this.pos=t=y.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):this.pos=t=e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):Ye.propHooks._default.set(this),this}},Ye.prototype.init.prototype=Ye.prototype,Ye.propHooks={_default:{get:function(e){var t;return 1!==e.elem.nodeType||null!=e.elem[e.prop]&&null==e.elem.style[e.prop]?e.elem[e.prop]:(t=y.css(e.elem,e.prop,""),t&&"auto"!==t?t:0)},set:function(e){y.fx.step[e.prop]?y.fx.step[e.prop](e):1!==e.elem.nodeType||null==e.elem.style[y.cssProps[e.prop]]&&!y.cssHooks[e.prop]?e.elem[e.prop]=e.now:y.style(e.elem,e.prop,e.now+e.unit)}}},Ye.propHooks.scrollTop=Ye.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},y.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2},_default:"swing"},y.fx=Ye.prototype.init,y.fx.step={};var Ge,Qe,Je=/^(?:toggle|show|hide)$/,Ke=/queueHooks$/;function Ze(){return n.setTimeout((function(){Ge=void 0})),Ge=y.now()}function et(e,t){var n,r=0,i={height:e};for(t=t?1:0;r<4;r+=2-t)n=J[r],i["margin"+n]=i["padding"+n]=e;return t&&(i.opacity=i.width=e),i}function tt(e,t,n){for(var r,i=(it.tweeners[t]||[]).concat(it.tweeners["*"]),o=0,s=i.length;o<s;o++)if(r=i[o].call(n,t,e))return r}function nt(e,t,n){var r,i,o,s,a,u,l,c,f=this,p={},d=e.style,h=e.nodeType&&K(e),g=X.get(e,"fxshow");for(r in n.queue||(a=y._queueHooks(e,"fx"),null==a.unqueued&&(a.unqueued=0,u=a.empty.fire,a.empty.fire=function(){a.unqueued||u()}),a.unqueued++,f.always((function(){f.always((function(){a.unqueued--,y.queue(e,"fx").length||a.empty.fire()}))}))),1===e.nodeType&&("height"in t||"width"in t)&&(n.overflow=[d.overflow,d.overflowX,d.overflowY],l=y.css(e,"display"),c="none"===l?X.get(e,"olddisplay")||Ae(e.nodeName):l,"inline"===c&&"none"===y.css(e,"float")&&(d.display="inline-block")),n.overflow&&(d.overflow="hidden",f.always((function(){d.overflow=n.overflow[0],d.overflowX=n.overflow[1],d.overflowY=n.overflow[2]}))),t)if(i=t[r],Je.exec(i)){if(delete t[r],o=o||"toggle"===i,i===(h?"hide":"show")){if("show"!==i||!g||void 0===g[r])continue;h=!0}p[r]=g&&g[r]||y.style(e,r)}else l=void 0;if(y.isEmptyObject(p))"inline"===("none"===l?Ae(e.nodeName):l)&&(d.display=l);else for(r in g?"hidden"in g&&(h=g.hidden):g=X.access(e,"fxshow",{}),o&&(g.hidden=!h),h?y(e).show():f.done((function(){y(e).hide()})),f.done((function(){var t;for(t in X.remove(e,"fxshow"),p)y.style(e,t,p[t])})),p)s=tt(h?g[r]:0,r,f),r in g||(g[r]=s.start,h&&(s.end=s.start,s.start="width"===r||"height"===r?1:0))}function rt(e,t){var n,r,i,o,s;for(n in e)if(r=y.camelCase(n),i=t[r],o=e[n],y.isArray(o)&&(i=o[1],o=e[n]=o[0]),n!==r&&(e[r]=o,delete e[n]),s=y.cssHooks[r],s&&"expand"in s)for(n in o=s.expand(o),delete e[r],o)n in e||(e[n]=o[n],t[n]=i);else t[r]=i}function it(e,t,n){var r,i,o=0,s=it.prefilters.length,a=y.Deferred().always((function(){delete u.elem})),u=function(){if(i)return!1;for(var t=Ge||Ze(),n=Math.max(0,l.startTime+l.duration-t),r=n/l.duration||0,o=1-r,s=0,u=l.tweens.length;s<u;s++)l.tweens[s].run(o);return a.notifyWith(e,[l,o,n]),o<1&&u?n:(a.resolveWith(e,[l]),!1)},l=a.promise({elem:e,props:y.extend({},t),opts:y.extend(!0,{specialEasing:{},easing:y.easing._default},n),originalProperties:t,originalOptions:n,startTime:Ge||Ze(),duration:n.duration,tweens:[],createTween:function(t,n){var r=y.Tween(e,l.opts,t,n,l.opts.specialEasing[t]||l.opts.easing);return l.tweens.push(r),r},stop:function(t){var n=0,r=t?l.tweens.length:0;if(i)return this;for(i=!0;n<r;n++)l.tweens[n].run(1);return t?(a.notifyWith(e,[l,1,0]),a.resolveWith(e,[l,t])):a.rejectWith(e,[l,t]),this}}),c=l.props;for(rt(c,l.opts.specialEasing);o<s;o++)if(r=it.prefilters[o].call(l,e,c,l.opts),r)return y.isFunction(r.stop)&&(y._queueHooks(l.elem,l.opts.queue).stop=y.proxy(r.stop,r)),r;return y.map(c,tt,l),y.isFunction(l.opts.start)&&l.opts.start.call(e,l),y.fx.timer(y.extend(u,{elem:e,anim:l,queue:l.opts.queue})),l.progress(l.opts.progress).done(l.opts.done,l.opts.complete).fail(l.opts.fail).always(l.opts.always)}y.Animation=y.extend(it,{tweeners:{"*":[function(e,t){var n=this.createTween(e,t);return Z(n.elem,e,Q.exec(t),n),n}]},tweener:function(e,t){y.isFunction(e)?(t=e,e=["*"]):e=e.match(M);for(var n,r=0,i=e.length;r<i;r++)n=e[r],it.tweeners[n]=it.tweeners[n]||[],it.tweeners[n].unshift(t)},prefilters:[nt],prefilter:function(e,t){t?it.prefilters.unshift(e):it.prefilters.push(e)}}),y.speed=function(e,t,n){var r=e&&"object"===o(e)?y.extend({},e):{complete:n||!n&&t||y.isFunction(e)&&e,duration:e,easing:n&&t||t&&!y.isFunction(t)&&t};return r.duration=y.fx.off?0:"number"===typeof r.duration?r.duration:r.duration in y.fx.speeds?y.fx.speeds[r.duration]:y.fx.speeds._default,null!=r.queue&&!0!==r.queue||(r.queue="fx"),r.old=r.complete,r.complete=function(){y.isFunction(r.old)&&r.old.call(this),r.queue&&y.dequeue(this,r.queue)},r},y.fn.extend({fadeTo:function(e,t,n,r){return this.filter(K).css("opacity",0).show().end().animate({opacity:t},e,n,r)},animate:function(e,t,n,r){var i=y.isEmptyObject(e),o=y.speed(t,n,r),s=function(){var t=it(this,y.extend({},e),o);(i||X.get(this,"finish"))&&t.stop(!0)};return s.finish=s,i||!1===o.queue?this.each(s):this.queue(o.queue,s)},stop:function(e,t,n){var r=function(e){var t=e.stop;delete e.stop,t(n)};return"string"!==typeof e&&(n=t,t=e,e=void 0),t&&!1!==e&&this.queue(e||"fx",[]),this.each((function(){var t=!0,i=null!=e&&e+"queueHooks",o=y.timers,s=X.get(this);if(i)s[i]&&s[i].stop&&r(s[i]);else for(i in s)s[i]&&s[i].stop&&Ke.test(i)&&r(s[i]);for(i=o.length;i--;)o[i].elem!==this||null!=e&&o[i].queue!==e||(o[i].anim.stop(n),t=!1,o.splice(i,1));!t&&n||y.dequeue(this,e)}))},finish:function(e){return!1!==e&&(e=e||"fx"),this.each((function(){var t,n=X.get(this),r=n[e+"queue"],i=n[e+"queueHooks"],o=y.timers,s=r?r.length:0;for(n.finish=!0,y.queue(this,e,[]),i&&i.stop&&i.stop.call(this,!0),t=o.length;t--;)o[t].elem===this&&o[t].queue===e&&(o[t].anim.stop(!0),o.splice(t,1));for(t=0;t<s;t++)r[t]&&r[t].finish&&r[t].finish.call(this);delete n.finish}))}}),y.each(["toggle","show","hide"],(function(e,t){var n=y.fn[t];y.fn[t]=function(e,r,i){return null==e||"boolean"===typeof e?n.apply(this,arguments):this.animate(et(t,!0),e,r,i)}})),y.each({slideDown:et("show"),slideUp:et("hide"),slideToggle:et("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},(function(e,t){y.fn[e]=function(e,n,r){return this.animate(t,e,n,r)}})),y.timers=[],y.fx.tick=function(){var e,t=0,n=y.timers;for(Ge=y.now();t<n.length;t++)e=n[t],e()||n[t]!==e||n.splice(t--,1);n.length||y.fx.stop(),Ge=void 0},y.fx.timer=function(e){y.timers.push(e),e()?y.fx.start():y.timers.pop()},y.fx.interval=13,y.fx.start=function(){Qe||(Qe=n.setInterval(y.fx.tick,y.fx.interval))},y.fx.stop=function(){n.clearInterval(Qe),Qe=null},y.fx.speeds={slow:600,fast:200,_default:400},y.fn.delay=function(e,t){return e=y.fx&&y.fx.speeds[e]||e,t=t||"fx",this.queue(t,(function(t,r){var i=n.setTimeout(t,e);r.stop=function(){n.clearTimeout(i)}}))},function(){var e=u.createElement("input"),t=u.createElement("select"),n=t.appendChild(u.createElement("option"));e.type="checkbox",v.checkOn=""!==e.value,v.optSelected=n.selected,t.disabled=!0,v.optDisabled=!n.disabled,e=u.createElement("input"),e.value="t",e.type="radio",v.radioValue="t"===e.value}();var ot,st=y.expr.attrHandle;y.fn.extend({attr:function(e,t){return $(this,y.attr,e,t,arguments.length>1)},removeAttr:function(e){return this.each((function(){y.removeAttr(this,e)}))}}),y.extend({attr:function(e,t,n){var r,i,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return"undefined"===typeof e.getAttribute?y.prop(e,t,n):(1===o&&y.isXMLDoc(e)||(t=t.toLowerCase(),i=y.attrHooks[t]||(y.expr.match.bool.test(t)?ot:void 0)),void 0!==n?null===n?void y.removeAttr(e,t):i&&"set"in i&&void 0!==(r=i.set(e,n,t))?r:(e.setAttribute(t,n+""),n):i&&"get"in i&&null!==(r=i.get(e,t))?r:(r=y.find.attr(e,t),null==r?void 0:r))},attrHooks:{type:{set:function(e,t){if(!v.radioValue&&"radio"===t&&y.nodeName(e,"input")){var n=e.value;return e.setAttribute("type",t),n&&(e.value=n),t}}}},removeAttr:function(e,t){var n,r,i=0,o=t&&t.match(M);if(o&&1===e.nodeType)while(n=o[i++])r=y.propFix[n]||n,y.expr.match.bool.test(n)&&(e[r]=!1),e.removeAttribute(n)}}),ot={set:function(e,t,n){return!1===t?y.removeAttr(e,n):e.setAttribute(n,n),n}},y.each(y.expr.match.bool.source.match(/\w+/g),(function(e,t){var n=st[t]||y.find.attr;st[t]=function(e,t,r){var i,o;return r||(o=st[t],st[t]=i,i=null!=n(e,t,r)?t.toLowerCase():null,st[t]=o),i}}));var at=/^(?:input|select|textarea|button)$/i,ut=/^(?:a|area)$/i;y.fn.extend({prop:function(e,t){return $(this,y.prop,e,t,arguments.length>1)},removeProp:function(e){return this.each((function(){delete this[y.propFix[e]||e]}))}}),y.extend({prop:function(e,t,n){var r,i,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return 1===o&&y.isXMLDoc(e)||(t=y.propFix[t]||t,i=y.propHooks[t]),void 0!==n?i&&"set"in i&&void 0!==(r=i.set(e,n,t))?r:e[t]=n:i&&"get"in i&&null!==(r=i.get(e,t))?r:e[t]},propHooks:{tabIndex:{get:function(e){var t=y.find.attr(e,"tabindex");return t?parseInt(t,10):at.test(e.nodeName)||ut.test(e.nodeName)&&e.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),v.optSelected||(y.propHooks.selected={get:function(e){var t=e.parentNode;return t&&t.parentNode&&t.parentNode.selectedIndex,null},set:function(e){var t=e.parentNode;t&&(t.selectedIndex,t.parentNode&&t.parentNode.selectedIndex)}}),y.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],(function(){y.propFix[this.toLowerCase()]=this}));var lt=/[\t\r\n\f]/g;function ct(e){return e.getAttribute&&e.getAttribute("class")||""}y.fn.extend({addClass:function(e){var t,n,r,i,o,s,a,u=0;if(y.isFunction(e))return this.each((function(t){y(this).addClass(e.call(this,t,ct(this)))}));if("string"===typeof e&&e){t=e.match(M)||[];while(n=this[u++])if(i=ct(n),r=1===n.nodeType&&(" "+i+" ").replace(lt," "),r){s=0;while(o=t[s++])r.indexOf(" "+o+" ")<0&&(r+=o+" ");a=y.trim(r),i!==a&&n.setAttribute("class",a)}}return this},removeClass:function(e){var t,n,r,i,o,s,a,u=0;if(y.isFunction(e))return this.each((function(t){y(this).removeClass(e.call(this,t,ct(this)))}));if(!arguments.length)return this.attr("class","");if("string"===typeof e&&e){t=e.match(M)||[];while(n=this[u++])if(i=ct(n),r=1===n.nodeType&&(" "+i+" ").replace(lt," "),r){s=0;while(o=t[s++])while(r.indexOf(" "+o+" ")>-1)r=r.replace(" "+o+" "," ");a=y.trim(r),i!==a&&n.setAttribute("class",a)}}return this},toggleClass:function(e,t){var n=o(e);return"boolean"===typeof t&&"string"===n?t?this.addClass(e):this.removeClass(e):y.isFunction(e)?this.each((function(n){y(this).toggleClass(e.call(this,n,ct(this),t),t)})):this.each((function(){var t,r,i,o;if("string"===n){r=0,i=y(this),o=e.match(M)||[];while(t=o[r++])i.hasClass(t)?i.removeClass(t):i.addClass(t)}else void 0!==e&&"boolean"!==n||(t=ct(this),t&&X.set(this,"__className__",t),this.setAttribute&&this.setAttribute("class",t||!1===e?"":X.get(this,"__className__")||""))}))},hasClass:function(e){var t,n,r=0;t=" "+e+" ";while(n=this[r++])if(1===n.nodeType&&(" "+ct(n)+" ").replace(lt," ").indexOf(t)>-1)return!0;return!1}});var ft=/\r/g,pt=/[\x20\t\r\n\f]+/g;y.fn.extend({val:function(e){var t,n,r,i=this[0];return arguments.length?(r=y.isFunction(e),this.each((function(n){var i;1===this.nodeType&&(i=r?e.call(this,n,y(this).val()):e,null==i?i="":"number"===typeof i?i+="":y.isArray(i)&&(i=y.map(i,(function(e){return null==e?"":e+""}))),t=y.valHooks[this.type]||y.valHooks[this.nodeName.toLowerCase()],t&&"set"in t&&void 0!==t.set(this,i,"value")||(this.value=i))}))):i?(t=y.valHooks[i.type]||y.valHooks[i.nodeName.toLowerCase()],t&&"get"in t&&void 0!==(n=t.get(i,"value"))?n:(n=i.value,"string"===typeof n?n.replace(ft,""):null==n?"":n)):void 0}}),y.extend({valHooks:{option:{get:function(e){var t=y.find.attr(e,"value");return null!=t?t:y.trim(y.text(e)).replace(pt," ")}},select:{get:function(e){for(var t,n,r=e.options,i=e.selectedIndex,o="select-one"===e.type||i<0,s=o?null:[],a=o?i+1:r.length,u=i<0?a:o?i:0;u<a;u++)if(n=r[u],(n.selected||u===i)&&(v.optDisabled?!n.disabled:null===n.getAttribute("disabled"))&&(!n.parentNode.disabled||!y.nodeName(n.parentNode,"optgroup"))){if(t=y(n).val(),o)return t;s.push(t)}return s},set:function(e,t){var n,r,i=e.options,o=y.makeArray(t),s=i.length;while(s--)r=i[s],(r.selected=y.inArray(y.valHooks.option.get(r),o)>-1)&&(n=!0);return n||(e.selectedIndex=-1),o}}}}),y.each(["radio","checkbox"],(function(){y.valHooks[this]={set:function(e,t){if(y.isArray(t))return e.checked=y.inArray(y(e).val(),t)>-1}},v.checkOn||(y.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})}));var dt=/^(?:focusinfocus|focusoutblur)$/;y.extend(y.event,{trigger:function(e,t,r,i){var s,a,l,c,f,p,d,h=[r||u],v=g.call(e,"type")?e.type:e,m=g.call(e,"namespace")?e.namespace.split("."):[];if(a=l=r=r||u,3!==r.nodeType&&8!==r.nodeType&&!dt.test(v+y.event.triggered)&&(v.indexOf(".")>-1&&(m=v.split("."),v=m.shift(),m.sort()),f=v.indexOf(":")<0&&"on"+v,e=e[y.expando]?e:new y.Event(v,"object"===o(e)&&e),e.isTrigger=i?2:3,e.namespace=m.join("."),e.rnamespace=e.namespace?new RegExp("(^|\\.)"+m.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=r),t=null==t?[e]:y.makeArray(t,[e]),d=y.event.special[v]||{},i||!d.trigger||!1!==d.trigger.apply(r,t))){if(!i&&!d.noBubble&&!y.isWindow(r)){for(c=d.delegateType||v,dt.test(c+v)||(a=a.parentNode);a;a=a.parentNode)h.push(a),l=a;l===(r.ownerDocument||u)&&h.push(l.defaultView||l.parentWindow||n)}s=0;while((a=h[s++])&&!e.isPropagationStopped())e.type=s>1?c:d.bindType||v,p=(X.get(a,"events")||{})[e.type]&&X.get(a,"handle"),p&&p.apply(a,t),p=f&&a[f],p&&p.apply&&B(a)&&(e.result=p.apply(a,t),!1===e.result&&e.preventDefault());return e.type=v,i||e.isDefaultPrevented()||d._default&&!1!==d._default.apply(h.pop(),t)||!B(r)||f&&y.isFunction(r[v])&&!y.isWindow(r)&&(l=r[f],l&&(r[f]=null),y.event.triggered=v,r[v](),y.event.triggered=void 0,l&&(r[f]=l)),e.result}},simulate:function(e,t,n){var r=y.extend(new y.Event,n,{type:e,isSimulated:!0});y.event.trigger(r,null,t)}}),y.fn.extend({trigger:function(e,t){return this.each((function(){y.event.trigger(e,t,this)}))},triggerHandler:function(e,t){var n=this[0];if(n)return y.event.trigger(e,t,n,!0)}}),y.each("blur focus focusin focusout load resize scroll unload click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup error contextmenu".split(" "),(function(e,t){y.fn[t]=function(e,n){return arguments.length>0?this.on(t,null,e,n):this.trigger(t)}})),y.fn.extend({hover:function(e,t){return this.mouseenter(e).mouseleave(t||e)}}),v.focusin="onfocusin"in n,v.focusin||y.each({focus:"focusin",blur:"focusout"},(function(e,t){var n=function(e){y.event.simulate(t,e.target,y.event.fix(e))};y.event.special[t]={setup:function(){var r=this.ownerDocument||this,i=X.access(r,t);i||r.addEventListener(e,n,!0),X.access(r,t,(i||0)+1)},teardown:function(){var r=this.ownerDocument||this,i=X.access(r,t)-1;i?X.access(r,t,i):(r.removeEventListener(e,n,!0),X.remove(r,t))}}}));var ht=n.location,gt=y.now(),vt=/\?/;y.parseJSON=function(e){return JSON.parse(e+"")},y.parseXML=function(e){var t;if(!e||"string"!==typeof e)return null;try{t=(new n.DOMParser).parseFromString(e,"text/xml")}catch(r){t=void 0}return t&&!t.getElementsByTagName("parsererror").length||y.error("Invalid XML: "+e),t};var mt=/#.*$/,yt=/([?&])_=[^&]*/,xt=/^(.*?):[ \t]*([^\r\n]*)$/gm,bt=/^(?:about|app|app-storage|.+-extension|file|res|widget):$/,wt=/^(?:GET|HEAD)$/,Tt=/^\/\//,Ct={},kt={},Et="*/".concat("*"),Nt=u.createElement("a");function St(e){return function(t,n){"string"!==typeof t&&(n=t,t="*");var r,i=0,o=t.toLowerCase().match(M)||[];if(y.isFunction(n))while(r=o[i++])"+"===r[0]?(r=r.slice(1)||"*",(e[r]=e[r]||[]).unshift(n)):(e[r]=e[r]||[]).push(n)}}function Dt(e,t,n,r){var i={},o=e===kt;function s(a){var u;return i[a]=!0,y.each(e[a]||[],(function(e,a){var l=a(t,n,r);return"string"!==typeof l||o||i[l]?o?!(u=l):void 0:(t.dataTypes.unshift(l),s(l),!1)})),u}return s(t.dataTypes[0])||!i["*"]&&s("*")}function jt(e,t){var n,r,i=y.ajaxSettings.flatOptions||{};for(n in t)void 0!==t[n]&&((i[n]?e:r||(r={}))[n]=t[n]);return r&&y.extend(!0,e,r),e}function At(e,t,n){var r,i,o,s,a=e.contents,u=e.dataTypes;while("*"===u[0])u.shift(),void 0===r&&(r=e.mimeType||t.getResponseHeader("Content-Type"));if(r)for(i in a)if(a[i]&&a[i].test(r)){u.unshift(i);break}if(u[0]in n)o=u[0];else{for(i in n){if(!u[0]||e.converters[i+" "+u[0]]){o=i;break}s||(s=i)}o=o||s}if(o)return o!==u[0]&&u.unshift(o),n[o]}function qt(e,t,n,r){var i,o,s,a,u,l={},c=e.dataTypes.slice();if(c[1])for(s in e.converters)l[s.toLowerCase()]=e.converters[s];o=c.shift();while(o)if(e.responseFields[o]&&(n[e.responseFields[o]]=t),!u&&r&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),u=o,o=c.shift(),o)if("*"===o)o=u;else if("*"!==u&&u!==o){if(s=l[u+" "+o]||l["* "+o],!s)for(i in l)if(a=i.split(" "),a[1]===o&&(s=l[u+" "+a[0]]||l["* "+a[0]],s)){!0===s?s=l[i]:!0!==l[i]&&(o=a[0],c.unshift(a[1]));break}if(!0!==s)if(s&&e.throws)t=s(t);else try{t=s(t)}catch(f){return{state:"parsererror",error:s?f:"No conversion from "+u+" to "+o}}}return{state:"success",data:t}}Nt.href=ht.href,y.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:ht.href,type:"GET",isLocal:bt.test(ht.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Et,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":y.parseJSON,"text xml":y.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?jt(jt(e,y.ajaxSettings),t):jt(y.ajaxSettings,e)},ajaxPrefilter:St(Ct),ajaxTransport:St(kt),ajax:function(e,t){"object"===o(e)&&(t=e,e=void 0),t=t||{};var r,i,s,a,l,c,f,p,d=y.ajaxSetup({},t),h=d.context||d,g=d.context&&(h.nodeType||h.jquery)?y(h):y.event,v=y.Deferred(),m=y.Callbacks("once memory"),x=d.statusCode||{},b={},w={},T=0,C="canceled",k={readyState:0,getResponseHeader:function(e){var t;if(2===T){if(!a){a={};while(t=xt.exec(s))a[t[1].toLowerCase()]=t[2]}t=a[e.toLowerCase()]}return null==t?null:t},getAllResponseHeaders:function(){return 2===T?s:null},setRequestHeader:function(e,t){var n=e.toLowerCase();return T||(e=w[n]=w[n]||e,b[e]=t),this},overrideMimeType:function(e){return T||(d.mimeType=e),this},statusCode:function(e){var t;if(e)if(T<2)for(t in e)x[t]=[x[t],e[t]];else k.always(e[k.status]);return this},abort:function(e){var t=e||C;return r&&r.abort(t),E(0,t),this}};if(v.promise(k).complete=m.add,k.success=k.done,k.error=k.fail,d.url=((e||d.url||ht.href)+"").replace(mt,"").replace(Tt,ht.protocol+"//"),d.type=t.method||t.type||d.method||d.type,d.dataTypes=y.trim(d.dataType||"*").toLowerCase().match(M)||[""],null==d.crossDomain){c=u.createElement("a");try{c.href=d.url,c.href=c.href,d.crossDomain=Nt.protocol+"//"+Nt.host!==c.protocol+"//"+c.host}catch(N){d.crossDomain=!0}}if(d.data&&d.processData&&"string"!==typeof d.data&&(d.data=y.param(d.data,d.traditional)),Dt(Ct,d,t,k),2===T)return k;for(p in f=y.event&&d.global,f&&0===y.active++&&y.event.trigger("ajaxStart"),d.type=d.type.toUpperCase(),d.hasContent=!wt.test(d.type),i=d.url,d.hasContent||(d.data&&(i=d.url+=(vt.test(i)?"&":"?")+d.data,delete d.data),!1===d.cache&&(d.url=yt.test(i)?i.replace(yt,"$1_="+gt++):i+(vt.test(i)?"&":"?")+"_="+gt++)),d.ifModified&&(y.lastModified[i]&&k.setRequestHeader("If-Modified-Since",y.lastModified[i]),y.etag[i]&&k.setRequestHeader("If-None-Match",y.etag[i])),(d.data&&d.hasContent&&!1!==d.contentType||t.contentType)&&k.setRequestHeader("Content-Type",d.contentType),k.setRequestHeader("Accept",d.dataTypes[0]&&d.accepts[d.dataTypes[0]]?d.accepts[d.dataTypes[0]]+("*"!==d.dataTypes[0]?", "+Et+"; q=0.01":""):d.accepts["*"]),d.headers)k.setRequestHeader(p,d.headers[p]);if(d.beforeSend&&(!1===d.beforeSend.call(h,k,d)||2===T))return k.abort();for(p in C="abort",{success:1,error:1,complete:1})k[p](d[p]);if(r=Dt(kt,d,t,k),r){if(k.readyState=1,f&&g.trigger("ajaxSend",[k,d]),2===T)return k;d.async&&d.timeout>0&&(l=n.setTimeout((function(){k.abort("timeout")}),d.timeout));try{T=1,r.send(b,E)}catch(N){if(!(T<2))throw N;E(-1,N)}}else E(-1,"No Transport");function E(e,t,o,a){var u,c,p,b,w,C=t;2!==T&&(T=2,l&&n.clearTimeout(l),r=void 0,s=a||"",k.readyState=e>0?4:0,u=e>=200&&e<300||304===e,o&&(b=At(d,k,o)),b=qt(d,b,k,u),u?(d.ifModified&&(w=k.getResponseHeader("Last-Modified"),w&&(y.lastModified[i]=w),w=k.getResponseHeader("etag"),w&&(y.etag[i]=w)),204===e||"HEAD"===d.type?C="nocontent":304===e?C="notmodified":(C=b.state,c=b.data,p=b.error,u=!p)):(p=C,!e&&C||(C="error",e<0&&(e=0))),k.status=e,k.statusText=(t||C)+"",u?v.resolveWith(h,[c,C,k]):v.rejectWith(h,[k,C,p]),k.statusCode(x),x=void 0,f&&g.trigger(u?"ajaxSuccess":"ajaxError",[k,d,u?c:p]),m.fireWith(h,[k,C]),f&&(g.trigger("ajaxComplete",[k,d]),--y.active||y.event.trigger("ajaxStop")))}return k},getJSON:function(e,t,n){return y.get(e,t,n,"json")},getScript:function(e,t){return y.get(e,void 0,t,"script")}}),y.each(["get","post"],(function(e,t){y[t]=function(e,n,r,i){return y.isFunction(n)&&(i=i||r,r=n,n=void 0),y.ajax(y.extend({url:e,type:t,dataType:i,data:n,success:r},y.isPlainObject(e)&&e))}})),y._evalUrl=function(e){return y.ajax({url:e,type:"GET",dataType:"script",async:!1,global:!1,throws:!0})},y.fn.extend({wrapAll:function(e){var t;return y.isFunction(e)?this.each((function(t){y(this).wrapAll(e.call(this,t))})):(this[0]&&(t=y(e,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&t.insertBefore(this[0]),t.map((function(){var e=this;while(e.firstElementChild)e=e.firstElementChild;return e})).append(this)),this)},wrapInner:function(e){return y.isFunction(e)?this.each((function(t){y(this).wrapInner(e.call(this,t))})):this.each((function(){var t=y(this),n=t.contents();n.length?n.wrapAll(e):t.append(e)}))},wrap:function(e){var t=y.isFunction(e);return this.each((function(n){y(this).wrapAll(t?e.call(this,n):e)}))},unwrap:function(){return this.parent().each((function(){y.nodeName(this,"body")||y(this).replaceWith(this.childNodes)})).end()}}),y.expr.filters.hidden=function(e){return!y.expr.filters.visible(e)},y.expr.filters.visible=function(e){return e.offsetWidth>0||e.offsetHeight>0||e.getClientRects().length>0};var Lt=/%20/g,Ht=/\[\]$/,Ot=/\r?\n/g,Ft=/^(?:submit|button|image|reset|file)$/i,Pt=/^(?:input|select|textarea|keygen)/i;function Rt(e,t,n,r){var i;if(y.isArray(t))y.each(t,(function(t,i){n||Ht.test(e)?r(e,i):Rt(e+"["+("object"===o(i)&&null!=i?t:"")+"]",i,n,r)}));else if(n||"object"!==y.type(t))r(e,t);else for(i in t)Rt(e+"["+i+"]",t[i],n,r)}y.param=function(e,t){var n,r=[],i=function(e,t){t=y.isFunction(t)?t():null==t?"":t,r[r.length]=encodeURIComponent(e)+"="+encodeURIComponent(t)};if(void 0===t&&(t=y.ajaxSettings&&y.ajaxSettings.traditional),y.isArray(e)||e.jquery&&!y.isPlainObject(e))y.each(e,(function(){i(this.name,this.value)}));else for(n in e)Rt(n,e[n],t,i);return r.join("&").replace(Lt,"+")},y.fn.extend({serialize:function(){return y.param(this.serializeArray())},serializeArray:function(){return this.map((function(){var e=y.prop(this,"elements");return e?y.makeArray(e):this})).filter((function(){var e=this.type;return this.name&&!y(this).is(":disabled")&&Pt.test(this.nodeName)&&!Ft.test(e)&&(this.checked||!ee.test(e))})).map((function(e,t){var n=y(this).val();return null==n?null:y.isArray(n)?y.map(n,(function(e){return{name:t.name,value:e.replace(Ot,"\r\n")}})):{name:t.name,value:n.replace(Ot,"\r\n")}})).get()}}),y.ajaxSettings.xhr=function(){try{return new n.XMLHttpRequest}catch(e){}};var Mt={0:200,1223:204},It=y.ajaxSettings.xhr();v.cors=!!It&&"withCredentials"in It,v.ajax=It=!!It,y.ajaxTransport((function(e){var t,r;if(v.cors||It&&!e.crossDomain)return{send:function(i,o){var s,a=e.xhr();if(a.open(e.type,e.url,e.async,e.username,e.password),e.xhrFields)for(s in e.xhrFields)a[s]=e.xhrFields[s];for(s in e.mimeType&&a.overrideMimeType&&a.overrideMimeType(e.mimeType),e.crossDomain||i["X-Requested-With"]||(i["X-Requested-With"]="XMLHttpRequest"),i)a.setRequestHeader(s,i[s]);t=function(e){return function(){t&&(t=r=a.onload=a.onerror=a.onabort=a.onreadystatechange=null,"abort"===e?a.abort():"error"===e?"number"!==typeof a.status?o(0,"error"):o(a.status,a.statusText):o(Mt[a.status]||a.status,a.statusText,"text"!==(a.responseType||"text")||"string"!==typeof a.responseText?{binary:a.response}:{text:a.responseText},a.getAllResponseHeaders()))}},a.onload=t(),r=a.onerror=t("error"),void 0!==a.onabort?a.onabort=r:a.onreadystatechange=function(){4===a.readyState&&n.setTimeout((function(){t&&r()}))},t=t("abort");try{a.send(e.hasContent&&e.data||null)}catch(u){if(t)throw u}},abort:function(){t&&t()}}})),y.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(e){return y.globalEval(e),e}}}),y.ajaxPrefilter("script",(function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type="GET")})),y.ajaxTransport("script",(function(e){var t,n;if(e.crossDomain)return{send:function(r,i){t=y("<script>").prop({charset:e.scriptCharset,src:e.url}).on("load error",n=function(e){t.remove(),n=null,e&&i("error"===e.type?404:200,e.type)}),u.head.appendChild(t[0])},abort:function(){n&&n()}}}));var Wt=[],$t=/(=)\?(?=&|$)|\?\?/;y.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=Wt.pop()||y.expando+"_"+gt++;return this[e]=!0,e}}),y.ajaxPrefilter("json jsonp",(function(e,t,r){var i,o,s,a=!1!==e.jsonp&&($t.test(e.url)?"url":"string"===typeof e.data&&0===(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&$t.test(e.data)&&"data");if(a||"jsonp"===e.dataTypes[0])return i=e.jsonpCallback=y.isFunction(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,a?e[a]=e[a].replace($t,"$1"+i):!1!==e.jsonp&&(e.url+=(vt.test(e.url)?"&":"?")+e.jsonp+"="+i),e.converters["script json"]=function(){return s||y.error(i+" was not called"),s[0]},e.dataTypes[0]="json",o=n[i],n[i]=function(){s=arguments},r.always((function(){void 0===o?y(n).removeProp(i):n[i]=o,e[i]&&(e.jsonpCallback=t.jsonpCallback,Wt.push(i)),s&&y.isFunction(o)&&o(s[0]),s=o=void 0})),"script"})),y.parseHTML=function(e,t,n){if(!e||"string"!==typeof e)return null;"boolean"===typeof t&&(n=t,t=!1),t=t||u;var r=D.exec(e),i=!n&&[];return r?[t.createElement(r[1])]:(r=ae([e],t,i),i&&i.length&&y(i).remove(),y.merge([],r.childNodes))};var Bt=y.fn.load;function _t(e){return y.isWindow(e)?e:9===e.nodeType&&e.defaultView}y.fn.load=function(e,t,n){if("string"!==typeof e&&Bt)return Bt.apply(this,arguments);var r,i,s,a=this,u=e.indexOf(" ");return u>-1&&(r=y.trim(e.slice(u)),e=e.slice(0,u)),y.isFunction(t)?(n=t,t=void 0):t&&"object"===o(t)&&(i="POST"),a.length>0&&y.ajax({url:e,type:i||"GET",dataType:"html",data:t}).done((function(e){s=arguments,a.html(r?y("<div>").append(y.parseHTML(e)).find(r):e)})).always(n&&function(e,t){a.each((function(){n.apply(this,s||[e.responseText,t,e])}))}),this},y.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],(function(e,t){y.fn[t]=function(e){return this.on(t,e)}})),y.expr.filters.animated=function(e){return y.grep(y.timers,(function(t){return e===t.elem})).length},y.offset={setOffset:function(e,t,n){var r,i,o,s,a,u,l,c=y.css(e,"position"),f=y(e),p={};"static"===c&&(e.style.position="relative"),a=f.offset(),o=y.css(e,"top"),u=y.css(e,"left"),l=("absolute"===c||"fixed"===c)&&(o+u).indexOf("auto")>-1,l?(r=f.position(),s=r.top,i=r.left):(s=parseFloat(o)||0,i=parseFloat(u)||0),y.isFunction(t)&&(t=t.call(e,n,y.extend({},a))),null!=t.top&&(p.top=t.top-a.top+s),null!=t.left&&(p.left=t.left-a.left+i),"using"in t?t.using.call(e,p):f.css(p)}},y.fn.extend({offset:function(e){if(arguments.length)return void 0===e?this:this.each((function(t){y.offset.setOffset(this,e,t)}));var t,n,r=this[0],i={top:0,left:0},o=r&&r.ownerDocument;return o?(t=o.documentElement,y.contains(t,r)?(i=r.getBoundingClientRect(),n=_t(o),{top:i.top+n.pageYOffset-t.clientTop,left:i.left+n.pageXOffset-t.clientLeft}):i):void 0},position:function(){if(this[0]){var e,t,n=this[0],r={top:0,left:0};return"fixed"===y.css(n,"position")?t=n.getBoundingClientRect():(e=this.offsetParent(),t=this.offset(),y.nodeName(e[0],"html")||(r=e.offset()),r.top+=y.css(e[0],"borderTopWidth",!0),r.left+=y.css(e[0],"borderLeftWidth",!0)),{top:t.top-r.top-y.css(n,"marginTop",!0),left:t.left-r.left-y.css(n,"marginLeft",!0)}}},offsetParent:function(){return this.map((function(){var e=this.offsetParent;while(e&&"static"===y.css(e,"position"))e=e.offsetParent;return e||Fe}))}}),y.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},(function(e,t){var n="pageYOffset"===t;y.fn[e]=function(r){return $(this,(function(e,r,i){var o=_t(e);if(void 0===i)return o?o[t]:e[r];o?o.scrollTo(n?o.pageXOffset:i,n?i:o.pageYOffset):e[r]=i}),e,r,arguments.length)}})),y.each(["top","left"],(function(e,t){y.cssHooks[t]=Re(v.pixelPosition,(function(e,n){if(n)return n=Pe(e,t),Le.test(n)?y(e).position()[t]+"px":n}))})),y.each({Height:"height",Width:"width"},(function(e,t){y.each({padding:"inner"+e,content:t,"":"outer"+e},(function(n,r){y.fn[r]=function(r,i){var o=arguments.length&&(n||"boolean"!==typeof r),s=n||(!0===r||!0===i?"margin":"border");return $(this,(function(t,n,r){var i;return y.isWindow(t)?t.document.documentElement["client"+e]:9===t.nodeType?(i=t.documentElement,Math.max(t.body["scroll"+e],i["scroll"+e],t.body["offset"+e],i["offset"+e],i["client"+e])):void 0===r?y.css(t,n,s):y.style(t,n,r,s)}),t,o?r:void 0,o,null)}}))})),y.fn.extend({bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,r){return this.on(t,e,n,r)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)},size:function(){return this.length}}),y.fn.andSelf=y.fn.addBack,r=[],i=function(){return y}.apply(t,r),void 0===i||(e.exports=i);var Xt=n.jQuery,zt=n.$;return y.noConflict=function(e){return n.$===y&&(n.$=zt),e&&n.jQuery===y&&(n.jQuery=Xt),y},s||(n.jQuery=n.$=y),y}))}}]);
(typeof window=='undefined'?global:window).tc_cfg_09272470305613778={"url":"css/theme-colors-ea095f1b.css","colors":["#1b65b9","#3274c0","#4984c7","#5f93ce","#76a3d5","#8db2dc","#a4c1e3","#bbd1ea","#d1e0f1","#ebf4fa","#bbd9ed","#8dbce0","#639fd4","#3e82c7","#1b65b9","#0f4894","#052f6e","#001a47","#000b21","27,101,185"]};

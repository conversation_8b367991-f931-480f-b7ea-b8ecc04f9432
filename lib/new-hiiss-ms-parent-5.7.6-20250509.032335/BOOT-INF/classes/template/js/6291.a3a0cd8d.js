"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[6291],{16291:function(t,e,n){n.r(e),n.d(e,{default:function(){return s}});var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticStyle:{padding:"20px"}},[n("h2",[t._v("模拟路由页面1")]),n("div",[n("span",[t._v("父页面控制路由跳转：")]),n("ta-button",{on:{click:function(e){return t.click(1)}}},[t._v(" 跳转模拟路由页面1的子路由1 ")]),n("ta-button",{staticStyle:{"margin-left":"20px"},on:{click:function(e){return t.click(2)}}},[t._v(" 跳转模拟路由页面1的子路由2 ")])],1),n("hr"),n("keep-alive",[n("router-view")],1)],1)},u=[],c={name:"parent1",data:function(){return{value:1}},created:function(){this.$router.push({name:"child1-1"})},activated:function(){1===this.value&&this.$router.push({name:"child1-1"}),2===this.value&&this.$router.push({name:"child1-2"})},methods:{click:function(t){this.value=t,this.$router.push({name:"child1-"+t})}}},a=c,l=n(1001),r=(0,l.Z)(a,i,u,!1,null,null,null),s=r.exports}}]);
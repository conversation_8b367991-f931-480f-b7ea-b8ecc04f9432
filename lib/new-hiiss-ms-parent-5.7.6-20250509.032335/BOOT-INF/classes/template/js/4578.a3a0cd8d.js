"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[4578],{34578:function(t,e,a){a.r(e),a.d(e,{default:function(){return L}});var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("ta-layout",{staticClass:"fit",staticStyle:{"background-color":"#fff"}},[a("ta-drawer",{staticClass:"left-drawer",class:{"more-message":t.moreMessage},attrs:{placement:"left",visible:t.drawerVisible,closable:!1,mask:!1},on:{close:function(){t.drawerVisible=!1}},scopedSlots:t._u([{key:"title",fn:function(){return[a("ta-title",{attrs:{title:"患者列表"}})]},proxy:!0}])},[a("div",{staticClass:"drawer-toggler",class:{"drawer-open":t.drawerVisible},staticStyle:{color:"rgb(39, 108, 245)"},on:{click:t.toggleMoreMessage}},[t.moreMessage?a("ta-icon",{attrs:{type:"double-left"}}):a("ta-icon",{attrs:{type:"double-right"}})],1),a("div",{staticClass:"part-top1"},[a("div",{staticClass:"part-title"},[a("tag-change",{attrs:{"tag-value":t.tagValue,"status-arr":t.statusArr},on:{change:t.changeTag}})],1)]),a("div",{staticStyle:{height:"calc(100% - 110px)","margin-left":"10px"}},[a("ta-big-table",{ref:"xTable",staticStyle:{width:"calc(100% - 10px)",cursor:"pointer"},attrs:{border:"","highlight-hover-row":"","show-overflow":"","sort-config":{trigger:"cell"},"checkbox-config":{trigger:"row"},data:t.dataSource,height:"auto",resizable:!0,"auto-resize":"",size:"mini","empty-text":" ",width:"min-width","row-style":t.rowStyle,"cell-style":t.cellStyle},on:{"cell-click":t.getPatientInfo},scopedSlots:t._u([{key:"empty",fn:function(){return[a("ta-empty",{attrs:{description:t.emptyDescription}})]},proxy:!0}])},[a("ta-big-table-column",{attrs:{type:"seq",fixed:"left",width:"20px",title:" ",align:"center"}}),a("ta-big-table-colgroup",{scopedSlots:t._u([{key:"header",fn:function(){return[a("ta-input",{attrs:{placeholder:"筛选患者/床位",size:"small",type:"type"},on:{keyup:t.searchEvent},model:{value:t.searchText,callback:function(e){t.searchText=e},expression:"searchText"}})]},proxy:!0}])},[a("ta-big-table-column",{attrs:{sortable:"",field:"patnName",title:"患者姓名",align:"left",width:t.widthConfig.name}}),a("ta-big-table-column",{attrs:{sortable:"",field:"bedno",title:"床位",align:"left","min-width":t.widthConfig.bch}})],1),a("ta-big-table-column",{attrs:{sortable:"",field:"chfpdrName",title:"主诊医生",align:"center",width:"100px"}}),a("ta-big-table-column",{attrs:{field:"printFlag",title:"打印标志",align:"center",width:"110px"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return["0"==n.printFlag?a("div",{staticStyle:{color:"orange"}},[a("ta-icon",{attrs:{type:"exclamation-circle"}}),t._v(" "+t._s("未打印")+" ")],1):a("div",{staticStyle:{color:"#52C41A"}},[a("ta-icon",{attrs:{type:"check-circle"}}),t._v(" "+t._s("已打印")+" ")],1)]}}])})],1)],1),t.support.length>0?a("div",{staticClass:"supportDiv"},[t._v(" "+t._s(t.supportText)+"   "),t._l(t.support,(function(e,n){return a("span",{key:n},[a("span",{staticStyle:{display:"inline-block",width:"auto"}},[t._v(t._s(e.supportnum)+"  ( "+t._s(e.supportname)+");")]),t._v("   ")])}))],2):t._e()]),a("ta-layout-content",{staticClass:"fit mian-content",staticStyle:{"margin-left":"28%"}},[a("div",{staticClass:"fit",staticStyle:{padding:"16px"}},[a("ta-title",{attrs:{title:"打印预览"}}),a("div",[a("ta-button",{directives:[{name:"show",rawName:"v-show",value:t.printHtmlStr,expression:"printHtmlStr"}],staticStyle:{float:"right"},attrs:{type:"primary"},on:{click:t.openPrint}},[a("ta-icon",{attrs:{type:"printer"}}),t._v("打印 ")],1)],1),a("div",{staticStyle:{clear:"both"}}),a("div",{staticStyle:{height:"calc(100% - 90px)",overflow:"scroll"}},[t.printHtmlStr?a("pdf-viewer",{ref:"pdfViewer",staticStyle:{height:"100%"},attrs:{"is-simple-show-mode":!0}}):t._e(),a("div",{directives:[{name:"show",rawName:"v-show",value:!t.printHtmlStr,expression:"!printHtmlStr"}]},[a("ta-empty"),t.editRecord.mdtrtId?a("div",{staticStyle:{width:"130px","margin-top":"10px","margin-left":"auto","margin-right":"auto"}},[t._v(" 该患者暂无自费项目 ")]):t._e()],1)],1)],1)])],1)},i=[],r=a(66347),o=a(95082),l=a(82482),s=(a(32564),"selfPayPrint/"),c={getBasePath:function(){return faceConfig.basePath+s},queryTableData:function(t,e){Base.submit(null,{url:s+"queryPatentList",data:t},{successCallback:function(t){return e(t)}})},generatePrintDoc:function(t,e){Base.submit(null,{url:s+"generatePrintDoc",data:t},{successCallback:function(t){return e(t)}})},savePrintLog:function(t,e,a){Base.submit(null,{url:s+"savePrintLog",data:t},{successCallback:function(t){return e(t)},failCallback:function(){return a()}})}},u=a(36797),d=a.n(u),f=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",t._l(t.tagList,(function(e){return a("ta-tag",{key:e.label,attrs:{color:e.checked?e.color2:e.color1},on:{click:function(a){return t.handleChange(e)}}},[a("span",{staticClass:"tag-icon"},[t._v(t._s(e.icon))]),a("span",{staticClass:"tag-label",staticStyle:{cursor:"pointer"}},[t._v(" "+t._s(e.label)+" ")])])})),1)},p=[],h={name:"tagChange",props:{tagValue:{type:String},statusArr:{type:Array,default:function(){return[]}}},data:function(){return{tagList:this.statusArr}},watch:{tagValue:function(t){this.changeTag(t)}},mounted:function(){this.changeTag(this.tagValue)},methods:{changeTag:function(t){this.tagList=this.tagList.map((function(e){return e.value===t?e.checked=!0:e.checked=!1,e}))},handleChange:function(t){this.$emit("change",t)}}},g=h,m=a(1001),v=(0,m.Z)(g,f,p,!1,null,"1320fc08",null),y=v.exports,b=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},w=[],x={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '},S={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:x}}},k=S,T=(0,m.Z)(k,b,w,!1,null,"5e7ef0ae",null),_=T.exports;a(94628),a(48534),a(36133);var z=[{value:"0",label:"未打印",checked:!0,color1:"orange",color2:"#FCB76B",icon:"待"},{value:"1",label:"已打印",checked:!1,color1:"green",color2:"#52C41A",icon:"已"},{value:"-1",label:"全部",checked:!1,color1:"blue",color2:"#1890ff",icon:"全"}],C={normal:{name:"110px",bch:"100px"},mini:{name:"50px",bch:"90px"}},P={name:"selfPayPrint",components:{TagChange:y,TaTitle:_},data:function(){var t;return t={blnLoading:!1,printHtmlStr:void 0,widthConfig:C.normal,buttonVisiable:!1,moreMessage:!0,drawerVisible:!0,statusArr:z,tagValue:"-1",searchText:void 0,dataSource:[],dataRange:"self",aaz217:"",selectRow:{},whetherDiagStats:"1",patientDetails:{},patientObjects:[],patientBillObjects:[],patientDiagnose:[],treeData:[],patientDiagName:"",keepOnRecordVisible:!1,keepOnRecordVisible2:!1,treeSelectValue:void 0,treeSelectShow:!0,permissions:{aaz263Disable:!0},aaz309List:[],rowData:{},filterParam:"",ykz010Arr:[],ykz042Arr:[],aaz263:"",aaz307:"",systemName:"",cainfo:"",customTipsText:"N",aaz309:null,clientId:"",clickFlag:"",height:220,visible:!1,submitCheckType:"Y",ykz018:"",nodeList:[],activeKey:[],columns:[],nodeData:[],params:{},expandedRow:{},ykz042:""},(0,l.Z)(t,"searchText",""),(0,l.Z)(t,"pageSize",10),(0,l.Z)(t,"current",1),(0,l.Z)(t,"total",0),(0,l.Z)(t,"support",[]),(0,l.Z)(t,"supportText","服务支持咨询"),(0,l.Z)(t,"editRecord",{}),(0,l.Z)(t,"savePrintTimeout",void 0),t},computed:{emptyDescription:function(){var t={0:"您没有未打印的在院患者",1:"您没有已打印的在院患者","-1":"您没有未打印和已打印的在院患者"};return t[this.tagValue]}},watch:{pageSize:function(t){},current:function(t){this.current=t}},mounted:function(){var t=this;this.$nextTick((function(){if(t.tagValue="-1",t.$route.query.params){var e=JSON.parse(t.$route.query.params);t.aaz307=e.aaz307,t.aaz263=e.aaz263}t.$route.query.aaz307&&(t.aaz307=t.$route.query.aaz307),t.$route.query.aaz263&&(t.aaz263=t.$route.query.aaz263),t.queryTableData()})),this.__fixTableLayoutWhenSmallScreen(),window.addEventListener("resize",this.__fixTableLayoutWhenSmallScreen)},unmounted:function(){window.removeEventListener("resize",this.__fixTableLayoutWhenSmallScreen)},methods:{savePrintLog:function(){var t=this;this.editRecord&&"0"==this.editRecord.printFlag&&this.$confirm({title:"打印成功询问",content:"是否成功打印?",cancelText:"未打印",okText:"成功打印",onOk:function(){c.savePrintLog((0,o.Z)((0,o.Z)({},t.editRecord),{},{aaz263:t.aaz263}),(function(){t.$message.success("用户确定打印成功"),t.queryTableData()}))},onCancel:function(){}})},getPatientInfo:function(t){var e=this,a=t.row;this.editRecord=a,this.printHtmlStr=void 0,this.blnLoading=!0,c.generatePrintDoc({currMdtrtId:a.currMdtrtId,mdtrtId:a.mdtrtId,patnId:a.patnId},(function(t){e.blnLoading=!1,e.printHtmlStr=t.data.result.htmlStr,e.printHtmlStr&&e.$nextTick((function(){e.previewPdf(a)}))}),(function(){e.blnLoading=!1}))},previewPdf:function(t){this.$refs.pdfViewer.show({currMdtrtId:t.currMdtrtId,mdtrtId:t.mdtrtId,patnId:t.patnId},this.getPdfUrl())},getPdfUrl:function(){return"selfPayPrint/downloadPrintPdf"},addPrintEvent:function(t){var e,a=this,n=(0,r.Z)(t);try{for(n.s();!(e=n.n()).done;){var i=e.value;i.contentWindow.addEventListener("afterprint",(function(t){clearTimeout(a.savePrintTimeout),a.savePrintTimeout=setTimeout((function(){a.savePrintLog()}),500)}))}}catch(o){n.e(o)}finally{n.f()}},printPdf:function(){var t=document.querySelectorAll('iframe[id^="pdfIframe"]');this.addPrintEvent(t);var e,a=(0,r.Z)(t);try{for(a.s();!(e=a.n()).done;){var n=e.value;n.contentWindow.print();break}}catch(i){a.e(i)}finally{a.f()}},openPrint:function(){this.printPdf()},searchEvent:TaUtils.debounce((function(){this.queryTableData()}),1e3,{leading:!1,trailing:!0}),__fixTableLayoutWhenSmallScreen:function(){var t=this;setTimeout((function(){t.widthConfig=C[parseInt(window.innerWidth)>1024?"normal":"mini"],t.$nextTick((function(){t.__fixOperationLayout()}))}),300)},__fixOperationLayout:function(){var t=Array.from(document.getElementsByClassName("custom-anchor"));t.forEach((function(t){var e=t.parent||t.parentNode||t.parentElement;if(e){var a=e.parent||e.parentNode||e.parentElement;e.style.width=parseInt(a.clientWidth)+6+"px"}}))},isSmallScreen:function(){var t=document.getElementsByClassName("drawer-toggler");if(t){var e=t[0],a=getComputedStyle(e);return"block"===a.display}return!1},toggleMoreMessage:function(){this.moreMessage=!this.moreMessage,this.$refs.xTable.scrollTo(0)},toggleDrawer:function(){this.drawerVisible=!this.drawerVisible},cellClickEvent:function(t){var e=t.column,a=t.row;this.expandedRow=a,e.property||"oparete"==e.property||this.$refs.dTable.toggleRowExpand(a)},headerCellStyle:function(t){t.column,t.columnIndex;return{fontSize:"12px",backgroundColor:"#ffffff"}},toggleExpandChangeEvent:function(t){var e=t.row,a=t.expanded;a&&(this.expandedRow=e),this.$refs.dTable.toggleRowExpand(e)},moment:d(),changeTag:function(t){this.tagValue=t.value,this.queryTableData()},convertYkz061:function(t){var e=t.match(/^<(.*)>(.*)<\/\1>$/);return"".concat(e[1],":").concat(e[2])},aaz309Change:function(t){this.aaz309=t,this.queryTableData()},queryTableData:function(){var t=this;this.patientDetails={},this.patientObjects=[],this.patientBillObjects=[],this.patientDiagnose=[],this.buttonVisiable=!1;var e={searchText:this.searchText,printFlag:this.tagValue,aaz263:this.aaz263};c.queryTableData(e,(function(e){t.dataSource=e.data.list}))},formatOperate:function(t){t.options=[];var e=t.bxczid.split(";"),a=e[0].substring(1,e[0].length-1).split(","),n=e[1].substring(1,e[1].length-1).split(",");if(a.length>0&&n.length>0&&-1===a.indexOf("")&&-1===n.indexOf("")){for(var i=new Map,r=0;r<a.length;r++)i.set(n[r],a[r]);for(var o=0;o<a.length;o++)t.bxczid.indexOf(n[o])>-1&&("0"!==n[o]&&"4"!==n[o]||"4"===n[o]&&t.ykz042)&&t.options.push({value:n[o],label:a[o]})}t.operate="0"===t.ape893?[]:t.ape893.split(","),t.aaz560="0"===t.aaz560?"":t.aaz560},cellStyle:function(t){t.row,t.rowIndex,t.column,t.columnIndex;return{}},rowStyle:function(t){var e=t.row;t.rowIndex;return this.editRecord.mdtrtId==e.mdtrtId&&this.editRecord.currMdtrtId==e.currMdtrtId?{backgroundColor:"rgb(235,244,250)"}:{backgroundColor:"#ffffff"}}}},I=P,D=(0,m.Z)(I,n,i,!1,null,"c789ff82",null),L=D.exports}}]);
"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[2545],{12697:function(e,t,a){a.r(t),a.d(t,{default:function(){return d}});var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"fit",attrs:{id:"userRole"}},[a("ta-border-layout",{attrs:{layout:{header:"55px",footer:"70px"},"center-cfg":{showBar:!0,toolBarStyle:{height:"50px",lineHeight:"48px"}},"header-cfg":{showBorder:!1},"show-border":!1,"footer-cfg":{showBorder:!1}}},[a("div",{staticClass:"center-box",attrs:{slot:"header"},slot:"header"},[a("ta-input-search",{staticClass:"search-box",attrs:{placeholder:"请输入人员姓名、帐号、证件号"},on:{search:e.onSearchUser},model:{value:e.searchInfo,callback:function(t){e.searchInfo=t},expression:"searchInfo"}},[a("ta-button",{attrs:{slot:"enterButton",type:"primary"},slot:"enterButton"},[e._v(" 搜索 ")])],1)],1),a("div",{attrs:{slot:"centerExtraContent"},slot:"centerExtraContent"},[a("ta-tree-select",{staticStyle:{width:"250px","margin-right":"10px"},attrs:{"dropdown-style":{maxHeight:"300px",overflow:"auto"},placeholder:"请选择组织机构",url:e.organizationTreeData,"tree-data":e.orgOptions,"tree-node-label-prop":"namePath","tree-id":"orgVos","tree-node-id":"orgId","tree-data-label":"orgName","tree-data-value":"orgId","allow-clear":"","search-placeholder":"请输入组织机构名称","show-search":"","filter-tree-node":e.filter,"loaded-data-call-back":e.fnLoadedOrgCallBack},on:{"update:treeData":function(t){e.orgOptions=t},"update:tree-data":function(t){e.orgOptions=t},change:e.onChange},model:{value:e.casValue,callback:function(t){e.casValue=t},expression:"casValue"}}),a("ta-checkbox",{attrs:{checked:e.isSub},on:{change:e.onChangeIsSub}},[e._v(" 包含子组织 ")]),a("ta-tag-select",{staticClass:"filter-name",attrs:{title:"锁定",data:[{value:"1",label:"是"},{value:"0",label:"否"}]},on:{change:e.filterClick},model:{value:e.islockList,callback:function(t){e.islockList=t},expression:"islockList"}})],1),a("ta-table",{attrs:{columns:e.userColumns,"data-source":e.userList,pagination:!1},scopedSlots:e._u([{key:"sex",fn:function(t){return a("span",{},[e._v(e._s(e.CollectionLabel("SEX",t)))])}},{key:"isLock",fn:function(t){return a("span",{},["1"==t?a("ta-tag",{staticClass:"no-cursor",attrs:{type:"danger"}},[e._v("是")]):a("ta-tag",{staticClass:"no-cursor",attrs:{type:"success"}},[e._v("否")])],1)}},{key:"operation",fn:function(t,r){return a("span",{},[a("ta-table-operate",{attrs:{"operate-menu":e.operateMenu}})],1)}}])}),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("ta-pagination",{ref:"gridPager",staticStyle:{float:"right","margin-top":"10px"},attrs:{"show-size-changer":"","show-quick-jumper":"","data-source":e.userList,"default-page-size":10,params:e.userPageParams,url:"org/authority/roleAuthorityManagementRestService/queryUserByCondition"},on:{"update:dataSource":function(t){e.userList=t},"update:data-source":function(t){e.userList=t}}})],1)],1)],1)},o=[],n=a(51097),s=[{title:"姓名",dataIndex:"name",width:"15%",overflowTooltip:!1,scopedSlots:{customRender:"name"}},{title:"帐号",dataIndex:"loginId",width:"15%",overflowTooltip:!0},{title:"性别",dataIndex:"sex",width:80,scopedSlots:{customRender:"sex"}},{title:"所属组织",width:"15%",overflowTooltip:"namePath",dataIndex:"namePath",customRender:function(e){return e?e.slice(e.lastIndexOf("/")+1):"--"}},{title:"手机号",dataIndex:"mobile",width:"15%",overflowTooltip:!0},{title:"锁定",dataIndex:"isLock",width:80,scopedSlots:{customRender:"isLock"}},{title:"操作",dataIndex:"operation",align:"center",width:240,scopedSlots:{customRender:"operation"}}],i={name:"UserRole",data:function(){var e=this;return{organizationTreeData:"org/authority/roleAuthorityManagementRestService/queryCurrentAdminRoleWrapeOrgTree",orgOptions:[],casValue:[],isSub:!0,userColumns:s,operateMenu:[{name:"角色管理",isShow:function(e){return"1"===e.admin},title:"该角色已关联管理员角色，无法再关联普通角色",style:{cursor:"not-allowed",color:"#ccc"}},{name:"角色管理",isShow:function(e){return"1"!==e.admin},onClick:function(t){return e.fnRouteToRoleMg(t)}}],userList:[],searchInfo:"",islockList:[]}},methods:{fnRouteToRoleMg:function(e){this.$router.push({name:"roleMg",params:{user:e}})},filter:function(e,t){var a=e.toLowerCase(),r=null;return r=(0,n.Z)([t],(function(e){return e.label.toLowerCase().indexOf(a)>-1})),r&&r.length>0},onChange:function(e,t){var a=this;this.casValue=e,this.$nextTick((function(){a.fnQueryUserByOrgId()}))},onChangeIsSub:function(e){var t=this;this.isSub=e.target.checked,this.$nextTick((function(){t.fnQueryUserByOrgId()}))},fnLoadedOrgCallBack:function(e){this.orgOptions=e,this.fnQueryUserByOrgId()},fnQueryUserByOrgId:function(){this.$refs.gridPager.loadData()},onSearchUser:function(){this.fnQueryUserByOrgId()},userPageParams:function(){var e,t={},a=this.orgOptions[0]&&this.orgOptions[0].orgId?this.orgOptions[0].orgId:"";return t.orgId=null!==(e=this.casValue)&&void 0!==e?e:a,t.showChildUser=this.isSub?"1":"0",t.searchType="normal",""!==this.searchInfo&&(t.name=this.searchInfo,t.loginId=this.searchInfo,t.idCardNo=this.searchInfo),t.islock=this.islockList,t},filterClick:function(){this.fnQueryUserByOrgId()}}},l=i,c=a(1001),u=(0,c.Z)(l,r,o,!1,null,"0599e65c",null),d=u.exports}}]);
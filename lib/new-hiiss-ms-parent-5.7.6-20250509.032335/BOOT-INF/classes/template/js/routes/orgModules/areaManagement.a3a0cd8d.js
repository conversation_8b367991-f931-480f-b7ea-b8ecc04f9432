"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[4329],{61476:function(e,t,a){a.r(t),a.d(t,{default:function(){return O}});var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{ref:"areaMg",staticClass:"fit",attrs:{id:"app"}},[a("ta-border-layout",{attrs:{layout:{header:"70px"},centerCfg:{showBar:!0,toolBarStyle:{height:"50px",lineHeight:"48px"}}}},[a("div",{staticClass:"center-box",attrs:{slot:"header"},slot:"header"},[a("ta-input-search",{staticClass:"search-box",attrs:{placeholder:"请输入区划名称\\代码",enterButton:"搜索"},on:{search:e.queryAreaByCondition},model:{value:e.searchStr,callback:function(t){e.searchStr=t},expression:"searchStr"}})],1),a("div",{attrs:{slot:"centerExtraContent"},slot:"centerExtraContent"},[a("ta-tag-select",{staticClass:"filter-name step1",attrs:{title:"有效性",data:e.CollectionData("EFFECTIVE")},on:{change:e.filterClick},model:{value:e.selectFilter,callback:function(t){e.selectFilter=t},expression:"selectFilter"}}),a("div",{staticStyle:{float:"right"}},[a("ta-button",{staticClass:"step2",attrs:{type:"primary"},on:{click:function(t){return e.showAddDrawer("0")}}},[e._v("新增顶级行政区划")]),a("ta-dropdown",[a("ta-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("ta-menu-item",{attrs:{disabled:!e.selectedRowKeys.length}},[a("ta-popconfirm",{attrs:{title:"确认启用所选行政区?",cancelText:"取消",okText:"确认"},on:{confirm:function(t){return e.fnBatchPickArea(!1)}}},[a("ta-icon",{attrs:{type:"check-circle"}}),a("span",{staticClass:"mg-l12"},[e._v("启用")])],1)],1),a("ta-menu-divider"),a("ta-menu-item",{attrs:{disabled:!e.selectedRowKeys.length}},[a("ta-popconfirm",{attrs:{title:"确认禁用所选行政区?",cancelText:"取消",okText:"确认"},on:{confirm:function(t){return e.fnBatchBanArea(!1)}}},[a("ta-icon",{attrs:{type:"stop"}}),a("span",{staticClass:"mg-l12"},[e._v("禁用")])],1)],1),a("ta-menu-divider"),a("ta-menu-item",{attrs:{disabled:!e.selectedRowKeys.length},on:{click:function(t){0===e.selectedRows.length?e.$message.warning("请先选择数据"):e.batchDeleteVisible=!0}}},[a("ta-icon",{attrs:{type:"close-circle"}}),e._v(" 删除 ")],1)],1),a("ta-button",[e._v("批量操作 "),a("ta-icon",{attrs:{type:"down"}})],1)],1)],1)],1),a("ta-table",{attrs:{columns:e.columns,scroll:{y:"100%"},dataSource:e.areaList,pagination:!1,rowSelection:{selectedRowKeys:e.selectedRowKeys,onChange:e.handleSelectChange,getCheckboxProps:function(e){return{props:{disabled:"0"==e.isAuthority}}}},rowKey:"areaId",expandedRowKeys:e.currentExpand,locale:{filterConfirm:"确定",filterReset:"重置",emptyText:"暂无数据"}},on:{expand:e.loadChild},scopedSlots:e._u([{key:"areaName",fn:function(t,r){return[a("a",{staticClass:"operate",class:{"disable-color":"0"==r.effective},on:{click:function(t){return e.showDrawer(r,!0)}}},[e._v(e._s(t))])]}},{key:"effective",fn:function(t){return a("span",{},[e._v(e._s(e.CollectionLabel("EFFECTIVE",t)))])}},{key:"areaLevel",fn:function(t,r){return e._l(e.CollectionData("AREALEVEL"),(function(t){return t.value==r.areaLevel?a("span",{key:t.value},[e._v(e._s(t.label))]):e._e()}))}},{key:"operation",fn:function(t,r){return a("span",{staticClass:"table-operation"},[a("ta-table-operate",{attrs:{operateMenu:e.operateMenu}})],1)}}])})],1),a("ta-drawer",{attrs:{getContainer:!1,destroyOnClose:!0,title:e.editOrShowTitle,placement:"right",closable:!0,visible:e.showVisible,width:"500",footerHeight:""},on:{close:function(t){e.showVisible=!1}}},[a("showArea",{ref:"showArea",attrs:{showArea:e.editAreaParam}})],1),a("ta-drawer",{attrs:{getContainer:!1,destroyOnClose:!0,title:e.editOrShowTitle,placement:"right",closable:!0,visible:e.editVisible,width:"500",footerHeight:""},on:{close:e.onClose}},[a("div",{attrs:{slot:"footer"},slot:"footer"},[a("ta-button-group",[a("ta-button",{on:{click:e.onResetForm}},[e._v("重置")]),a("ta-button",{attrs:{type:"primary"},on:{click:e.onSubmitForm}},[e._v("保存")])],1)],1),a("edit-area-info",{ref:"editAreaDrawer",attrs:{area:e.editAreaParam,flag:e.flag},on:{editSuccess:e.editSuccess}})],1),a("ta-drawer",{attrs:{getContainer:!1,destroyOnClose:!0,title:e.addOrChildTitle,placement:"right",closable:!0,visible:e.addVisible,width:"500"},on:{close:e.onClose}},[a("div",{attrs:{slot:"footer"},slot:"footer"},[a("ta-button-group",[a("ta-button",{on:{click:e.onAddAreaResetForm}},[e._v("重置")]),a("ta-button",{attrs:{type:"primary"},on:{click:e.addAreaSubmit}},[e._v("保存")])],1)],1),a("addArea",{ref:"addAreaDrawer",attrs:{area:e.areaItem},on:{addSuccess:e.addSuccess}})],1),a("ta-careful-delete",{attrs:{visible:e.batchDeleteVisible,title:"行政区删除",description:"选中行政区"},on:{close:function(t){e.batchDeleteVisible=!1},delete:function(t){return e.fnBatchDeleteArea(!1)}}})],1)},o=[],i="org/orguser/areaManagementRestService/",l={queryAllArea:function(e,t){Base.submit(null,{url:i+"queryAllArea",data:e},{successCallback:function(e){return t(e)}})},queryAreaByCondition:function(e,t){Base.submit(null,{url:i+"queryAreaByCondition",data:e},{successCallback:function(e){return t(e)}})},addArea:function(e,t,a){Base.submit(e,{url:i+"addArea",data:t,autoValid:!0},{successCallback:function(e){return a(e)}})},deleteBatchAreaByAreaIds:function(e,t){Base.submit(null,{url:i+"deleteBatchAreaByAreaIds",data:e},{successCallback:function(e){return t(e)}})},updateBatchAreaStatus:function(e,t){Base.submit(null,{url:i+"updateBatchEffectiveByAreaIdPath",data:e},{successCallback:function(e){return t(e)}})},updateArea:function(e,t,a){Base.submit(e,{url:i+"updateArea",data:t,autoValid:!0},{successCallback:function(e){return a(e)}})}},n=function(){var e=this,t=this,a=t.$createElement,r=t._self._c||a;return r("ta-form",{attrs:{autoFormCreate:function(t){e.form=t}}},[r("ta-form-item",{attrs:{label:"行政区划名称",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"areaName",fieldDecoratorOptions:{rules:[{required:!0,whitespace:!0,message:"行政区划名称不能为空"},{pattern:/^[\u4E00-\u9FA5A-Za-z0-9_]+$/,message:"只能输入中文英文数字及下划线"}]}}},[r("ta-input",{attrs:{disabled:t.flag}})],1),r("ta-form-item",{attrs:{label:"行政区划编码",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"areaCode",fieldDecoratorOptions:{rules:[{required:!0,whitespace:!0,message:"行政区划编码不能为空"},{pattern:"^[1-9][0-9]{5,11}$",message:"请输入正确的行政区划编码"}]}}},[r("ta-input",{attrs:{disabled:t.flag}})],1),r("ta-form-item",{attrs:{label:"排序号",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"areaOrder",fieldDecoratorOptions:{rules:[{required:!1,message:"排序号不能为空!"},{pattern:"^(0|[1-9][0-9]{0,3})$",message:"请输入正确的排序号"}]}}},[r("ta-input",{attrs:{disabled:t.flag}})],1),r("ta-form-item",{attrs:{label:"区划层级",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"areaLevel"}},[r("ta-radio-group",{attrs:{size:"small",buttonStyle:"solid",disabled:t.flag}},t._l(t.CollectionData("AREALEVEL"),(function(e){return r("ta-radio-button",{key:e.value,attrs:{value:e.value}},[t._v(t._s(e.label))])})),1)],1),r("ta-form-item",{attrs:{label:"状态",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"effective"}},[r("ta-switch",{attrs:{checkedChildren:"有效",unCheckedChildren:"无效",disabled:t.flag},model:{value:t.effective,callback:function(e){t.effective=e},expression:"effective"}})],1)],1)},s=[],c={labelCol:{span:6},wrapperCol:{span:18}},d={name:"editAreaInfo",props:["area","flag"],mounted:function(){this.initForm()},data:function(){return{form:null,effective:!0,formItemLayout:c}},methods:{onSubmitForm:function(){var e=this,t=this.form.getFieldsValue(),a=this.area,r=a.areaId,o=a.parentId,i=a.idPath,n=a.namePath;t.areaId=r,t.parentId=o,t.idPath=i,t.namePath=n,t.effective=!0===this.effective?"1":"0",l.updateArea(this.form,t,(function(a){e.$message.success("更新操作成功"),e.$emit("editSuccess",t)}))},onResetForm:function(){this.initForm()},initForm:function(){var e=this.area,t=e.areaName,a=e.areaCode,r=e.areaOrder,o=e.areaLevel,i=e.effective;this.form.setFieldsValue({areaName:t,areaCode:a,areaOrder:null===r||void 0===r?void 0:r.toString(),areaLevel:null===o||void 0===o?void 0:o.toString()}),this.effective="1"==i}}},u=d,f=a(1001),m=(0,f.Z)(u,n,s,!1,null,null,null),h=m.exports,p=function(){var e=this,t=this,a=t.$createElement,r=t._self._c||a;return r("ta-form",{attrs:{autoFormCreate:function(t){e.form=t}}},["0"!==t.area?r("ta-form-item",{attrs:{label:"上级行政区划",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol}},[r("ta-input",{attrs:{value:t.area.areaName,disabled:""}})],1):t._e(),r("ta-form-item",{attrs:{label:"行政区划名称",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"areaName",fieldDecoratorOptions:{rules:[{required:!0,whitespace:!0,message:"行政区划名称不能为空"},{pattern:/^[\u4E00-\u9FA5A-Za-z0-9_]+$/,message:"只能输入中文英文数字及下划线"}]}}},[r("ta-input",{attrs:{placeholder:"请输入行政区划名称"}})],1),r("ta-form-item",{attrs:{label:"行政区划编码",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"areaCode",fieldDecoratorOptions:{rules:[{required:!0,whitespace:!0,message:"行政区划编码不能为空"},{pattern:"^[1-9][0-9]{5,11}$",message:"请输入正确的行政区划编码"}]}}},[r("ta-input",{attrs:{placeholder:"请输入行政区划编码"}})],1)],1)},C=[],b={labelCol:{span:6},wrapperCol:{span:18}},v={props:["area"],data:function(){return{form:null,editFlag:!1,formItemLayout:b}},methods:{onResetForm:function(){this.form.resetFields()},handleSubmit:function(){var e=this,t=this.form.getFieldsValue();t.parentId=this.area.areaId,l.addArea(this.form,t,(function(t){e.$emit("addSuccess",e.area,t.data.result),e.$message.success("新增行政区划成功")}))}}},w=v,y=(0,f.Z)(w,p,C,!1,null,null,null),A=y.exports,g=function(){var e=this,t=this,a=t.$createElement,r=t._self._c||a;return r("ta-form",{attrs:{autoFormCreate:function(t){e.form=t}}},[r("ta-form-item",{attrs:{label:"行政区划名称",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol}},[t._v(" "+t._s(t.showArea.areaName)+" ")]),r("ta-form-item",{attrs:{label:"行政区划编码",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol}},[t._v(" "+t._s(t.showArea.areaCode)+" ")]),r("ta-form-item",{attrs:{label:"排序号",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol}},[t._v(" "+t._s(t.showArea.areaOrder)+" ")]),r("ta-form-item",{attrs:{label:"区划层级",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol}},t._l(t.CollectionData("AREALEVEL"),(function(e){return e.value==t.showArea.areaLevel?r("span",{key:e.value},[t._v(t._s(e.label))]):t._e()})),0),r("ta-form-item",{attrs:{label:"状态",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol}},t._l(t.CollectionData("EFFECTIVE"),(function(e){return e.value==t.showArea.effective?r("span",{key:e.value},[t._v(t._s(e.label))]):t._e()})),0),r("ta-form-item",{attrs:{label:"最后修改时间",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol}},[t._v(" "+t._s(t.showArea.modifyTime)+" ")])],1)},I=[],L={labelCol:{span:6},wrapperCol:{span:18}},_={name:"showArea",props:["showArea"],data:function(){return{formItemLayout:L}}},k=_,S=(0,f.Z)(k,g,I,!1,null,null,null),B=S.exports,D=a(80790),R=[{title:"区划名称",key:"areaName",width:"35%",overflowTooltip:!0,dataIndex:"areaName",scopedSlots:{customRender:"areaName"}},{title:"区划代码",key:"areaCode",width:"15%",dataIndex:"areaCode"},{title:"区划层级",key:"areaLevel",dataIndex:"areaLevel",width:"15%",scopedSlots:{customRender:"areaLevel"}},{title:"状态",key:"effective",dataIndex:"effective",width:"10%",yesOrNoTag:!0,scopedSlots:{customRender:"effective"}},{title:"最后修改时间",overflowTooltip:!0,key:"modifyTime",width:"15%",dataIndex:"modifyTime"},{title:"操作",dataIndex:"operation",key:"operation",align:"center",width:220,scopedSlots:{customRender:"operation"}}],x={name:"areaManagement",data:function(){var e=this;return{currentExpand:[],searchStr:"",effective:"",columns:R,operateMenu:[{name:"新增下级",disabled:function(e){return"0"==e.effective},title:function(e){return"0"==e.effective?"禁用的行政区不允许新增下级":""},onClick:function(t){return e.showAddDrawer(t)}},{name:"编辑",disabled:function(e){return"0"==e.effective},title:function(e){return"0"==e.effective?"禁用的行政区不允许编辑":""},onClick:function(t){return e.showDrawer(t,!1)}},{name:"更多",type:"more",moreMenuList:[{name:"启用",type:"confirm",confirmTitle:"确认启用该行政区?",onOk:function(t){e.fnBatchPickArea(t)}},{name:"禁用",type:"confirm",confirmTitle:"确认禁用该行政区?",onOk:function(t){e.fnBatchBanArea(t)}},{name:"删除",type:"confirm",confirmTitle:"确认删除该行政区?",onOk:function(t){e.fnBatchDeleteArea(t)}}]}],areaList:[],editVisible:!1,showVisible:!1,addVisible:!1,editAreaParam:{},flag:!0,areaItem:"0",parentId:[],areaFieldNames:{label:"areaName",value:"areaId",children:"children"},areaOptions:[],selectedRows:[],selectedRowKeys:[],editOrShowTitle:"编辑行政区划",addOrChildTitle:"新增下级行政区划",selectFilter:[],batchDeleteVisible:!1}},mixins:[D.Z],mounted:function(){this.fnLoadAllArea();var e=[{element:".step1",popover:{title:"有效性",description:"通过【有效性】标签实现行政区划过滤",position:"bottom"}},{element:".step2",popover:{title:"新增顶级行政区划",description:"创建顶级行政区划，创建成功后通过【新增下级】创建下级行政区划",position:"left"}}];this.fnCommonGuide(e)},components:{editAreaInfo:h,addArea:A,showArea:B},methods:{onResetForm:function(){this.$refs.editAreaDrawer.onResetForm()},onSubmitForm:function(){this.$refs.editAreaDrawer.onSubmitForm()},onAddAreaResetForm:function(){this.$refs.addAreaDrawer.onResetForm()},addAreaSubmit:function(){this.$refs.addAreaDrawer.handleSubmit()},addSuccess:function(e,t){this.fnLoadAllArea(),this.onClose()},editSuccess:function(e){this.fnLoadAllArea(),this.onClose()},changeRowData:function(e,t,a){for(var r=0;r<e.length;r++){if(e[r][t]==a[t]){e[r]=Object.assign({},e[r],a);break}e[r].children&&this.changeRowData(e[r].children,"areaId",a)}},filterClick:function(e){this.effective=e[0],this.queryAreaByCondition()},loadChild:function(e,t){var a=this;if(0!=e)l.queryAllArea({areaId:t.areaId,effective:this.effective},(function(e){e.data.areaList[0].children?(t.children=e.data.areaList[0].children,a.currentExpand.push(t.areaId)):a.$message.warning("当前行政区划不存在下级!")}));else{var r=this.currentExpand.indexOf(t.areaId);this.currentExpand.splice(r,1)}},fnLoadAllArea:function(){var e=this;this.currentExpand=[];var t={effective:this.effective};l.queryAllArea(t,(function(t){e.areaList=t.data.areaList}))},onChange:function(e){e&&(this.parentId=e,this.queryAreaByCondition())},queryAreaByCondition:function(){var e=this,t=this.searchStr,a=this.parentId[this.parentId.length-1],r={nameOrCode:t,areaId:a,effective:this.effective};l.queryAreaByCondition(r,(function(t){e.currentExpand=[],e.areaList=t.data.areaList}))},handleSelectChange:function(e,t){this.selectedRowKeys=e,this.selectedRows=t},fnBatchDeleteArea:function(e){var t,a=this,r=[];e?(r.push(e.idPath),t={idPaths:r.join(",")}):(r=this.selectedRows.map((function(e){return e.idPath})),t={idPaths:r.join(",")}),l.deleteBatchAreaByAreaIds(t,(function(e){a.$message.success("删除数据成功"),a.fnLoadAllArea(),a.batchDeleteVisible=!1}))},fnBatchBanArea:function(e){var t,a=this,r=[];if(e){if("0"==e.effective)return void this.$message.warning("该记录已经禁用，请勿重复操作！");r.push(e.idPath),t={areaIdPaths:r.join(","),effective:"0"}}else{for(var o=0;o<this.selectedRows.length;o++)r.push(this.selectedRows[o].idPath);t={areaIdPaths:r.join(","),effective:"0"}}l.updateBatchAreaStatus(t,(function(e){a.fnLoadAllArea(),a.selectedRowKeys=[],a.selectedRows=[],a.$message.success("更新数据成功")}))},fnBatchPickArea:function(e){var t,a=this,r=[];if(e){if("1"==e.effective&&0==e.childNum)return void this.$message.warning("该记录已经启用，请勿重复操作！");r.push(e.idPath),t={areaIdPaths:r.join(","),effective:"1"}}else{for(var o=0;o<this.selectedRows.length;o++)r.push(this.selectedRows[o].idPath);t={areaIdPaths:r.join(","),effective:"1"}}l.updateBatchAreaStatus(t,(function(e){a.fnLoadAllArea(),a.selectedRowKeys=[],a.selectedRows=[],a.$message.success("更新数据成功")}))},showDrawer:function(e,t){this.flag=t,this.editAreaParam=e,t?(this.editOrShowTitle="查看行政区划",this.showVisible=!0):(this.editOrShowTitle="修改行政区划",this.editVisible=!0)},showAddDrawer:function(e){if("0"===e)this.addOrChildTitle="新增行政区划";else if("0"==e.effective)return void this.$message.warning("禁用的行政区划不允许添加下级");this.areaItem=e,this.addVisible=!0},onClose:function(){this.editVisible=!1,this.addVisible=!1}}},F=x,E=(0,f.Z)(F,r,o,!1,null,"564b705b",null),O=E.exports},80790:function(e,t,a){var r=a(76698);a(34530);t["Z"]={activated:function(){var e=window.pageVmObj,t=e["steps_"+e._route.name];this.methods?this.methods.fnCommonGuide(t):this.fnCommonGuide(t)},deactivated:function(){this.methods?this.methods.fnCommonGuide([],!0):this.fnCommonGuide([],!0)},methods:{fnCommonGuide:function(e,t){var a=window.pageVmObj;t?(a.driver.reset(),window.fnPageGuide=null):(a["steps_"+a._route.name]=e,a.driver=new r.Z({allowClose:!1}),window.fnPageGuide=function(){a.driver.defineSteps(e),a.driver.start()})}}}}}]);
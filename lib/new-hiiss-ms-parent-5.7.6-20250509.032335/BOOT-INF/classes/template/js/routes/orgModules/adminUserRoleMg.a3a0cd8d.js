"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[9081],{10189:function(e,t,s){s.r(t),s.d(t,{default:function(){return f}});var a=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"fit",attrs:{id:"adminUserRoleMg"}},[s("ta-border-layout",{attrs:{layout:{header:"70px",footer:"70px"},"footer-cfg":{showBorder:!1}}},[s("div",{staticClass:"center-box",attrs:{slot:"header"},slot:"header"},[s("ta-breadcrumb",{staticStyle:{"line-height":"42px",float:"left"},attrs:{separator:">"}},[s("ta-breadcrumb-item",[s("a",{on:{click:e.fnBack}},[e._v("人员维度")])]),s("ta-breadcrumb-item",[e._v("角色管理")])],1),s("div",{staticClass:"divider"}),s("ta-alert",{staticClass:"notice-box",attrs:{message:"当前人员："+this.$route.query.name,type:"info","show-icon":""}})],1),s("ta-tabs",{staticClass:"fit"},[s("ta-tab-pane",{attrs:{tab:"人员下角色列表123"}},[s("ta-table",{attrs:{columns:e.userRoleColumns,"data-source":e.roleData,pagination:!1,"row-selection":{selectedRowKeys:e.selectedRolesKeys,onChange:e.onSelectRoleChange}},scopedSlots:e._u([{key:"namePath",fn:function(t){return s("span",{},[e._v(e._s(e.getLastName(t)))])}},{key:"action",fn:function(t,a){return s("span",{},[s("ta-table-operate",{attrs:{"operate-menu":e.operateMenu}})],1)}}])})],1),s("template",{slot:"tabBarExtraContent"},[s("ta-button",{on:{click:e.fnBack}},[s("ta-icon",{attrs:{type:"rollback"}}),e._v(" 返回 ")],1),s("ta-button-group",[s("ta-button",{on:{click:e.fnBatchDeleteUserRoles}},[e._v(" 批量移除 ")]),s("ta-button",{attrs:{type:"primary"},on:{click:e.fnAssociateRole}},[e._v(" 分配角色 ")])],1)],1)],2),s("div",{attrs:{slot:"footer"},slot:"footer"},[s("ta-pagination",{ref:"gridPager",staticStyle:{float:"right","padding-top":"10px"},attrs:{"show-size-changer":"","show-quick-jumper":"","data-source":e.roleData,"default-page-size":10,params:e.rolePageParams,url:e.adminUserRoleUrl},on:{"update:dataSource":function(t){e.roleData=t},"update:data-source":function(t){e.roleData=t}}})],1)],1),s("ta-modal",{attrs:{centered:"",width:"1000px","body-style":{height:"500px",padding:"0"},"destroy-on-close":"","get-container":e.setContainer,"mask-closable":!1},model:{value:e.associateVisible,callback:function(t){e.associateVisible=t},expression:"associateVisible"}},[s("div",{staticStyle:{"text-align":"center"},attrs:{slot:"title"},slot:"title"},[e._v(" 给人员 ["+e._s(this.$route.query.name)+"] 分配管理员角色 ")]),s("div",{staticStyle:{"text-align":"center"},attrs:{slot:"footer"},slot:"footer"},[s("ta-button",{on:{click:function(t){e.associateVisible=!1}}},[s("ta-icon",{attrs:{type:"rollback"}}),e._v(" 返回 ")],1),s("ta-button",{attrs:{type:"primary"},on:{click:e.fnSave}},[e._v(" 保存 ")])],1),s("ta-tabs",{staticClass:"fit"},[s("ta-tab-pane",{attrs:{tab:"可分配管理员角色列表"}},[s("ta-table",{staticStyle:{width:"100%","padding-top":"10px"},attrs:{columns:e.roleColumns,"data-source":e.roleReData,size:"small",pagination:!1,"row-selection":{selectedRowKeys:e.selectedRowKeys,onChange:e.onSelectChange}}}),s("ta-pagination",{ref:"modalGridPager",staticStyle:{float:"right","margin-top":"10px","padding-right":"10px"},attrs:{"show-size-changer":"","show-quick-jumper":"","data-source":e.roleReData,"default-page-size":10,params:e.rolePageParams,url:e.adminUserModalRoleUrl},on:{"update:dataSource":function(t){e.roleReData=t},"update:data-source":function(t){e.roleReData=t}}})],1)],1)],1)],1)},n=[],r=s(16158),u=[{title:"角色名称",dataIndex:"roleName",overflowTooltip:!0,width:"20%"},{title:"组织路径",overflowTooltip:!0,dataIndex:"namePath",scopedSlots:{customRender:"namePath"},width:"30%"},{title:"角色描述",dataIndex:"roleDesc",width:"20%",overflowTooltip:!0},{title:"操作",dataIndex:"operation",width:"10%",scopedSlots:{customRender:"action"},align:"center"}],o=[{title:"角色名称",dataIndex:"roleName",overflowTooltip:!0,width:"20%"},{title:"组织路径",overflowTooltip:!0,dataIndex:"namePath",width:"30%"},{title:"角色描述",dataIndex:"roleDesc",width:"30%",overflowTooltip:!0}],l={name:"adminUserRoleMg",data:function(){var e=this;return{adminUserRoleUrl:"org/authority/adminAuthorityManagementRestService/queryRolesByUserId",adminUserModalRoleUrl:"org/authority/adminAuthorityManagementRestService/queryNoWrapperRolesByUserId",userRoleColumns:u,operateMenu:[{name:"移除",type:"confirm",confirmTitle:"确认移除该管理员角色吗?",onOk:function(t){e.fnAdminRecordDelete(t)}}],roleColumns:o,roleData:[],roleReData:[],associateVisible:!1,selectedRowKeys:[],selectedRoles:[],selectedRolesKeys:[],selectedCurrentRoles:[]}},activated:function(){var e=this.$route.query;e.userId&&e.name?this.fnLoadDefaultAdminUserRoles():this.fnBack()},methods:{setContainer:function(){return document.getElementById("adminUserRoleMg")},getLastName:function(e){return e&&-1!==e.indexOf("/")?e.slice(e.lastIndexOf("/")+1):e},fnLoadDefaultAdminUserRoles:function(){this.$refs.gridPager.loadData()},fnAdminRecordDelete:function(e){var t=this,s={userId:this.$route.query.userId,roleIds:e.roleId};r.Z.deleteBatchUserRole(s,(function(e){t.$message.success("移除成功"),t.fnLoadDefaultAdminUserRoles()}))},fnBack:function(){this.$router.push({name:"adminUserManagement"})},fnAssociateRole:function(){var e=this;this.associateVisible=!0,this.$nextTick((function(){e.$refs.modalGridPager.loadData()}))},rolePageParams:function(){return{userId:this.$route.query.userId}},onSelectChange:function(e,t){this.selectedRowKeys=e,this.selectedRoles=t},onSelectRoleChange:function(e,t){this.selectedRolesKeys=e,this.selectedCurrentRoles=t},fnSave:function(){var e=this;if(0!==this.selectedRoles.length){var t=this.selectedRoles.map((function(e){return e.roleId})),s={userId:this.$route.query.userId,roleIds:t.join(",")};r.Z.addBatchUserRole(s,(function(t){e.$message.success("分配角色成功"),e.fnLoadDefaultAdminUserRoles(),e.associateVisible=!1}))}else this.$message.warn("请先勾选要分配的角色")},fnBatchDeleteUserRoles:function(){var e=this;0!==this.selectedCurrentRoles.length?this.$confirm({title:"批量移除管理员角色",content:"确认移除这些管理员角色吗?",cancelText:"取消",okText:"确认",onOk:function(){var t=e.selectedCurrentRoles.map((function(e){return e.roleId})),s={userId:e.$route.query.userId,roleIds:t.join(",")};r.Z.deleteBatchUserRole(s,(function(t){e.$message.success("移除人员下角色成功"),e.fnLoadDefaultAdminUserRoles()}))}}):this.$message.warn("请先勾选要移除的角色")}}},i=l,c=s(1001),d=(0,c.Z)(i,a,n,!1,null,"fd1e7b0a",null),f=d.exports},16158:function(e,t){var s="/org/authority/adminAuthorityManagementRestService/";t["Z"]={queryUsersByRoleId:function(e,t){Base.submit(null,{url:s+"queryUsersByRoleId",data:e},{successCallback:function(e){return t(e)}})},queryUsersByRoleIdNoPage:function(e,t){Base.submit(null,{url:s+"queryUsersByRoleIdNoPage",data:e},{successCallback:function(e){return t(e)}})},addBatchAdminUser:function(e,t){Base.submit(null,{url:s+"addBatchRoleUsers",data:e},{successCallback:function(e){return t(e)}})},deleteBatchAdmin:function(e,t){Base.submit(null,{url:s+"deleteBatchRole",data:e},{successCallback:function(e){return t(e)}})},queryAllTaOrg:function(e,t){Base.submit(null,{url:s+"queryCurrentAdminRoleWrapOrgTree",data:e},{successCallback:function(e){return t(e)}})},deleteRoleUserByKey:function(e,t){Base.submit(null,{url:s+"deleteRoleUserByKey",data:e},{successCallback:function(e){return t(e)}})},deleteBatchRoleUsers:function(e,t){Base.submit(null,{url:s+"deleteBatchRoleUsers",data:e},{successCallback:function(e){return t(e)}})},queryBatchUserByOrgId:function(e,t){Base.submit(null,{url:s+"queryUsersNoWraperByRoleId",data:e},{successCallback:function(e){return t(e)}})},queryBatchUserByOrgIdNoPage:function(e,t){Base.submit(null,{url:s+"queryUsersNoWraperByRoleIdNoPage",data:e},{successCallback:function(e){return t(e)}})},queryOrgPermission:function(e,t){Base.submit(null,{url:s+"selectOrgScope",data:e},{successCallback:function(e){return t(e)}})},removeOrgPermission:function(e,t){Base.submit(null,{url:s+"removeOrgScope",data:e},{successCallback:function(e){return t(e)}})},selectPermissionOrgScope:function(e,t){Base.submit(null,{url:s+"selectPermissionOrgScope",data:e},{successCallback:function(e){return t(e)}})},addOrgPermission:function(e,t){Base.submit(null,{url:s+"saveOrgScope",data:e},{successCallback:function(e){return t(e)}})},queryBatchPermissionTreeData:function(e,t){Base.submit(null,{url:s+"queryBatchPermissionTreeData",data:e},{successCallback:function(e){return t(e)}})},batchChangeAdminPermission:function(e,t){Base.submit(null,{url:s+"batchChangeAdminPermission",data:e},{successCallback:function(e){return t(e)}})},addAdminRole:function(e,t){Base.submit(null,{url:s+"addAdminRole",data:e},{successCallback:function(e){return t(e)}})},updateAdmin:function(e,t){Base.submit(null,{url:s+"updateAdmin",data:e},{successCallback:function(e){return t(e)}})},updateBatchAdminStatus:function(e,t){Base.submit(null,{url:s+"updateRoleEffectiveByRoleId",data:e},{successCallback:function(e){return t(e)}})},queryUseSysPermission:function(e,t){Base.submit(null,{url:s+"queryRootResource",data:e},{successCallback:function(e){return t(e)}})},queryUsePermissionByResourceId:function(e,t){Base.submit(null,{url:s+"queryChildResourceByRoleId",data:e},{successCallback:function(e){return t(e)}})},saveAdminUsePermission:function(e,t){Base.submit(null,{url:s+"changeResourceUsePermission",data:e},{successCallback:function(e){return t(e)}})},updateAdminUsePermissionEffectiveTime:function(e,t){Base.submit(null,{url:s+"updateUsePermissionEffectiveTime",data:e},{successCallback:function(e){return t(e)}})},saveBatchAdminUsePermissionEffectiveTime:function(e,t){Base.submit(null,{url:s+"updateBatchUsePermissionEffectiveTime",data:e},{successCallback:function(e){return t(e)}})},queryObjectUseSysPermission:function(e,t){Base.submit(null,{url:s+"queryRootCustomResource",data:e},{successCallback:function(e){return t(e)}})},queryObjectUsePermissionByResourceId:function(e,t){Base.submit(null,{url:s+"queryChildCustomResourceByRoleId",data:e},{successCallback:function(e){return t(e)}})},saveAdminObjectUsePermission:function(e,t){Base.submit(null,{url:s+"changeCustomResourceUsePermission",data:e},{successCallback:function(e){return t(e)}})},updateAdminObjectUsePermissionEffectiveTime:function(e,t){Base.submit(null,{url:s+"updateCustomResourceUsePermissionEffectiveTime",data:e},{successCallback:function(e){return t(e)}})},updateBatchAdminObjectUsePermissionEffectiveTime:function(e,t){Base.submit(null,{url:s+"updateBatchCustomResourceUsePermissionEffectiveTime",data:e},{successCallback:function(e){return t(e)}})},queryGrantSysPermission:function(e,t){Base.submit(null,{url:s+"queryRootResourceAuthority",data:e},{successCallback:function(e){return t(e)}})},queryGrantPermissionByResourceId:function(e,t){Base.submit(null,{url:s+"queryChildResourceAuthorityByRoleId",data:e},{successCallback:function(e){return t(e)}})},saveAdminGrantPermission:function(e,t){Base.submit(null,{url:s+"changeResourceAuthority",data:e},{successCallback:function(e){return t(e)}})},queryObjectGrantSysPermission:function(e,t){Base.submit(null,{url:s+"queryRootCustomResourceAuthority",data:e},{successCallback:function(e){return t(e)}})},queryObjectGrantPermissionByResourceId:function(e,t){Base.submit(null,{url:s+"queryChildCustomResourceAuthorityByRoleId",data:e},{successCallback:function(e){return t(e)}})},saveAdminObjectGrantPermission:function(e,t){Base.submit(null,{url:s+"changeCustomResourceAuthority",data:e},{successCallback:function(e){return t(e)}})},addBatchUserRole:function(e,t){Base.submit(null,{url:s+"addBatchUserRole",data:e},{successCallback:function(e){return t(e)}})},deleteBatchUserRole:function(e,t){Base.submit(null,{url:s+"deleteBatchUserRole",data:e},{successCallback:function(e){return t(e)}})},queryChildCustomResourceAsync:function(e,t){Base.submit(null,{url:s+"queryChildCustomResourceAsync",data:e},{successCallback:function(e){return t(e)}})},addAllRoleUsersByOrgId:function(e,t){Base.submit(null,{url:s+"addAllRoleUsersByOrgId",data:e},{successCallback:function(e){return t(e)}})}}}}]);
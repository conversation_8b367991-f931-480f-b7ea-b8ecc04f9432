"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[4832],{82057:function(e,t,a){a.r(t),a.d(t,{default:function(){return o}});var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("ta-row",[a("ta-col",{attrs:{span:e.row.col.fullSpan}},[a("span",{staticClass:"title"},[e._v("组织权限")]),a("div",{staticClass:"modalTreeStyle"},[a("ta-e-tree",{ref:"tree",attrs:{data:e.treeData,load:e.loadData,"show-checkbox":"","highlight-current":"","check-strictly":"","node-key":"orgId",props:e.default<PERSON><PERSON>,lazy:""},on:{"check-change":e.handle<PERSON><PERSON><PERSON><PERSON>},scopedSlots:e._u([{key:"default",fn:function(t){t.node;var r=t.data;return a("span",{staticClass:"custom-tree-node"},[e._v(" "+e._s(r.orgName)+" "),"0"===r.isAuthority?a("span",[a("span",{staticStyle:{float:"right",color:"#ccc","font-size":"12px",cursor:"not-allowed"}},[e._v("无操作权限")])]):e._e(),r.disabled?a("span",[a("span",{staticStyle:{float:"right",color:"#ccc","font-size":"12px"}},[e._v("该组织已选择")])]):e._e()])}}])})],1)])],1),a("ta-row",[a("ta-col",[a("div",{staticClass:"ant-modal-footer",staticStyle:{"text-align":"center"}},[a("ta-button",{on:{click:function(t){return e.$emit("modalCancel")}}},[a("ta-icon",{attrs:{type:"rollback"}}),e._v("返回")],1),a("ta-button",{attrs:{type:"primary"},on:{click:e.fnSaveOrUpdate}},[e._v("保存")])],1)])],1)],1)},n=[],u=a(61109),s={name:"adminAuthorityMg",props:["item"],data:function(){return{roleId:"",adminFilterText:"",treeData:[],defaultProps:{id:"orgId",label:"orgName",isLeaf:"isLeaf"},row:{col:{span:12,fullSpan:24}}}},watch:{adminFilterText:function(e){}},created:function(){this.roleId=this.item.roleId},methods:{handleCheckNodeChange:function(e,t,a){var r=this.$refs.tree.getCheckedKeys();if(t){if("0"===e.isAuthority)return this.$message.warning("您没有该组织的操作权限"),void this.$refs.tree.setChecked(e,!1);if(r.length>=2)for(var n=0;n<r.length;n++)r[n]!==e.orgId&&this.$refs.tree.setChecked(r[n],!1,!1)}},loadData:function(e,t){if(0===e.level){var a={roleId:this.roleId};u.Z.queryOrgAuthTreeByAsync(a,(function(e){return t(e.data.orgVos)}))}if(e.level>=1){var r=e.data.orgId,n={orgId:r,roleId:this.roleId};u.Z.queryOrgAuthTreeByAsync(n,(function(a){var r=a.data.orgVos;if(r[0].children&&r[0].children instanceof Array&&r[0].children.length>0){var n=r[0].children;if(e.data.disabled){var u=n.map((function(e){return e.disabled=!0,e}));return t(u)}return t(n)}}))}},fnSaveOrUpdate:function(){var e=this,t=this.$refs.tree.getCheckedNodes();if(t.length<1)this.$message.warning("请选择组织",2.5);else{var a=t[0],r={roleId:this.roleId,orgId:a.orgId,idPath:a.idPath};u.Z.addOrgAuth(r,(function(t){e.$message.success("保存数据成功"),e.$emit("modalCancel")}))}}}},l=s,c=a(1001),i=(0,c.Z)(l,r,n,!1,null,"0062e0af",null),o=i.exports},61109:function(e,t){var a="/org/authority/examinerAuthorityRestService/";t["Z"]={queryUserNoWrapperByRoleId:function(e,t){var r=a+"queryUserNoWrapperByRoleId";Base.submit(null,{url:r,data:e},{successCallback:function(e){return t(e)}})},addExaminer:function(e,t){var r=a+"addExaminer";Base.submit(null,{url:r,data:e},{successCallback:function(e){return t(e)}})},updateExaminer:function(e,t){var r=a+"updateExaminer";Base.submit(null,{url:r,data:e},{successCallback:function(e){return t(e)}})},unableExaminer:function(e,t){var r=a+"unableExaminer";Base.submit(null,{url:r,data:e},{successCallback:function(e){return t(e)}})},enableExaminer:function(e,t){var r=a+"enableExaminer";Base.submit(null,{url:r,data:e},{successCallback:function(e){return t(e)}})},deleteBatchExaminer:function(e,t){var r=a+"deleteBatchExaminer";Base.submit(null,{url:r,data:e},{successCallback:function(e){return t(e)}})},queryOrgAuth:function(e,t){var r=a+"queryOrgAuth";Base.submit(null,{url:r,data:e},{successCallback:function(e){return t(e)}})},queryOrgAuthTreeByAsync:function(e,t){var r=a+"queryOrgAuthTreeByAsync";Base.submit(null,{url:r,data:e},{successCallback:function(e){return t(e)}})},queryOrgTreeByAsync:function(e,t){var r=a+"queryOrgTreeByAsync";Base.submit(null,{url:r,data:e},{successCallback:function(e){return t(e)}})},removeOrgAuth:function(e,t){var r=a+"removeOrgAuth";Base.submit(null,{url:r,data:e},{successCallback:function(e){return t(e)}})},addOrgAuth:function(e,t){var r=a+"addOrgAuth";Base.submit(null,{url:r,data:e},{successCallback:function(e){return t(e)}})},addBatchExaminerUser:function(e,t){var r=a+"addBatchRoleUser";Base.submit(null,{url:r,data:e},{successCallback:function(e){return t(e)}})},deleteBatchRoleUser:function(e,t){var r=a+"deleteBatchRoleUser";Base.submit(null,{url:r,data:e},{successCallback:function(e){return t(e)}})},deleteBatchUserRole:function(e,t){var r=a+"deleteBatchUserRole";Base.submit(null,{url:r,data:e},{successCallback:function(e){return t(e)}})},addBatchUserRole:function(e,t){var r=a+"addBatchUserRole";Base.submit(null,{url:r,data:e},{successCallback:function(e){return t(e)}})}}}}]);
"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[6613],{42309:function(e,t,n){n.r(t),n.d(t,{default:function(){return b}});var s=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"fit",attrs:{id:"publicRoleAuthority"}},[n("ta-border-layout",{attrs:{layout:{header:"70px"}}},[n("div",{staticStyle:{"text-align":"center",overflow:"hidden"},attrs:{slot:"header"},slot:"header"},[n("ta-breadcrumb",{staticStyle:{"line-height":"42px",float:"left"},attrs:{separator:">"}},[n("ta-breadcrumb-item",[n("a",{on:{click:e.fnBack}},[e._v("角色维度")])]),n("ta-breadcrumb-item",[e._v("功能菜单权限管理")])],1),n("div",{staticClass:"divider-header"}),n("ta-alert",{staticStyle:{float:"left","margin-top":"2px"},attrs:{message:"当前角色为："+e.role.roleName,type:"info","show-icon":""}}),n("ta-button",{staticStyle:{float:"right","margin-top":"8px"},on:{click:e.fnBack}},[n("ta-icon",{attrs:{type:"rollback"}}),e._v("返回 ")],1)],1),n("ta-tabs",{staticClass:"fit content-box"},[n("ta-tab-pane",{attrs:{tab:"功能菜单权限"}},[n("ta-row",{staticClass:"fit"},[n("ta-col",{staticClass:"fit authority-box",staticStyle:{"border-right":"1px solid #eee",height:"100%"},attrs:{span:4}},[n("div",{staticClass:"menu-title"},[n("ta-icon",{attrs:{type:"menu-fold"}}),n("span",{staticStyle:{"margin-left":"5px"}},[e._v("功能一级菜单")])],1),n("ta-divider",{staticStyle:{margin:"0"}}),n("ta-menu",{attrs:{mode:"inline","selected-keys":e.menuSelectedKeys},on:{click:e.onSelectMenu}},e._l(e.menuData,(function(t){return n("ta-menu-item",{key:t.resourceId},[n("ta-icon",{attrs:{type:"appstore"}}),e._v(e._s(t.name)+" ")],1)})),1)],1),n("ta-col",{staticClass:"right-box",attrs:{span:20}},[n("div",{key:e.authrityTree.length,staticClass:"fit",staticStyle:{border:"1px solid #e8e8e8"}},[n("ta-big-table",{ref:"xTable",attrs:{height:"100%",resizable:"",border:"","row-id":"resourceId","cell-class-name":e.cellClassName,"tree-config":{children:"children",expandAll:!0},"expand-config":{lazy:!0},data:e.authrityTree,"checkbox-config":{labelField:"name",checkField:"checked",showHeader:!1,checkMethod:e.checkMethod}},on:{"checkbox-change":e.checkedChange}},[n("ta-big-table-column",{attrs:{type:"checkbox",field:"name",title:"可授权的功能菜单","tree-node":"","filter-method":e.filterAgeMethod,filters:[{data:""}]},scopedSlots:e._u([{key:"header",fn:function(t){t.row;return[n("span",[e._v("可授权的功能菜单")]),n("ta-input",{staticStyle:{width:"250px","margin-left":"10px"},attrs:{placeholder:"输入关键字进行过滤"},on:{click:function(e){e.stopPropagation(),e.preventDefault()}},model:{value:e.filterText,callback:function(t){e.filterText=t},expression:"filterText"}}),n("ta-button",{attrs:{type:"primary"},on:{click:function(t){return t.stopPropagation(),t.preventDefault(),e.fnAuthorityMgSave.apply(null,arguments)}}},[e._v(" 权限保存 ")])]}},{key:"default",fn:function(t){var s=t.row;return[s.loginAccess?n("span",{staticStyle:{color:"#67c23a",padding:"0 8px"}},[e._v(e._s(s.name)+" (登录即可访问)")]):n("span",{staticStyle:{padding:"0 8px"}},[e._v(e._s(s.name))]),s.restUrlAuthorityPoList?n("span",{on:{click:function(e){e.stopPropagation()}}},e._l(s.restUrlAuthorityPoList,(function(t,r){return n("ta-checkbox",{key:r,attrs:{checked:"1"===t.checked,"default-checked":"1"==t.checked},on:{click:function(e){e.stopPropagation()},change:function(n){return e.changeTempCheckedValue(n,t,s)}}},[n("span",{staticClass:"iterface-font",on:{click:function(e){e.stopPropagation()}}},[e._v(e._s(t.urlName)+"(url)")])])})),1):e._e()]}}])}),n("ta-big-table-column",{attrs:{field:"effectTime",title:"有效期",width:"25%"},scopedSlots:e._u([{key:"header",fn:function(t){t.row;return[n("span",[e._v("有效期")]),n("ta-popover",{attrs:{placement:"bottom",trigger:"manual",value:e.batchPop}},[n("div",{staticClass:"pop-calendar"},[n("ta-calendar",{attrs:{fullscreen:!1},on:{select:e.onPanelChange}})],1),n("div",{staticStyle:{float:"right","margin-top":"10px"}},[n("ta-button",{attrs:{size:"small"},on:{click:function(t){e.batchPop=!1}}},[e._v(" 取消 ")]),n("ta-button",{attrs:{size:"small"},on:{click:function(t){return e.fnSaveAuthorityEffectiveTime(!0)}}},[e._v(" 设为永久 ")]),n("ta-button",{attrs:{type:"primary",size:"small"},on:{click:function(t){return e.fnSaveAuthorityEffectiveTime(!1)}}},[e._v(" 确定 ")])],1),n("ta-button",{staticStyle:{float:"right","margin-right":"10px"},attrs:{slot:"reference"},on:{click:function(t){e.batchPop=!0}},slot:"reference"},[e._v(" 批量设置有效期 ")])],1)]}},{key:"default",fn:function(t){var s=t.row;return[s.checked?n("span",{staticClass:"node-right"},[e._v(" "+e._s(s.effectTime?s.effectTime.split(" ")[0]:"永久")+" "),n("ta-popover",{attrs:{placement:"bottom",trigger:"manual",value:s.resourceId==e.indexClick}},[n("div",{staticClass:"pop-calendar"},[n("ta-calendar",{attrs:{fullscreen:!1},on:{select:e.onPanelChange}})],1),n("div",{staticStyle:{float:"right","margin-top":"10px"}},[n("ta-button",{attrs:{size:"small"},on:{click:function(t){e.indexClick=null}}},[e._v("取消")]),n("ta-button",{attrs:{size:"small"},on:{click:function(t){return e.onCellChange(s,!0)}}},[e._v("设为永久")]),n("ta-button",{attrs:{type:"primary",size:"small"},on:{click:function(t){return e.onCellChange(s,!1)}}},[e._v("确定 ")])],1),n("ta-icon",{staticStyle:{"margin-left":"10px"},attrs:{slot:"reference",type:"edit"},on:{click:function(t){e.indexClick=s.resourceId}},slot:"reference"})],1)],1):e._e()]}}])})],1)],1)])],1)],1)],1)],1)],1)},r=[],a=n(89584),i=n(46981),c=n(96671),o=n(61543),u=n(52245),l=n(28464),d=n(61785),f=[{title:"一级功能菜单",dataIndex:"name"}],h={name:"publicRoleAuthority",data:function(){return{menuColumns:f,selectedRowKeys:[],effectiveTime:"",role:{},defaultProps:{children:"children",label:"name",id:"resourceId",disabled:"loginAccess"},indexClick:null,allCheckedList:[],currentCheckedList:[],tempCheckedList:[],authrityTree:[],originAuthrityTreeCopyData:[],menuData:[],rootId:"",popVisible:!1,batchPop:!1,filterText:"",menuSelectedKeys:[],tempNodeCheck:[]}},watch:{filterText:function(e){this.filterAgeMethod(e)}},activated:function(){this.$route.params.role instanceof Object?(this.role=this.$route.params.role||{},this.fnQueryRePermissionByRoleId()):this.$router.push({name:"publicRoleManager"}),this.batchPop=!1,this.indexClick=null},methods:{checkedChange:function(e){e.records;var t=e.row;if(t.restUrlAuthorityPoList&&t.restUrlAuthorityPoList.length>0){var n=t.restUrlAuthorityPoList;n.forEach((function(e){t.checked||(e.checked="0")}))}this.setRestUrlAuthorityPoLis(t.children)},setRestUrlAuthorityPoLis:function(e){e&&0!==e.length&&(0,d.Z)(e,(function(e){if(e.restUrlAuthorityPoList&&e.restUrlAuthorityPoList.length>0){var t=e.restUrlAuthorityPoList;t.forEach((function(t){t.checked=e.checked?"1":"0"}))}}))},checkMethod:function(e){var t=e.row;return!t.loginAccess},changeTempCheckedValue:function(e,t,n){e&&(n.checked=!0),t.checked=e.target.checked?"1":"0"},filterAgeMethod:function(e){var t=this,n=(0,l.Z)(e).trim();if(n){var s={children:"children"},r=["name"];this.authrityTree=(0,u.Z)(this.originAuthrityTreeCopyData,(function(e){return r.some((function(t){return(0,l.Z)(e[t]).indexOf(n)>-1}))}),s),this.$nextTick((function(){t.$refs.xTable.setAllTreeExpand(!0)}))}else this.authrityTree=this.originAuthrityTreeCopyData,this.$refs.xTable.setAllTreeExpand(!0)},cellClassName:function(e){var t=e.row,n=(e.rowIndex,e.column);e.columnIndex;return"name"!==n.property||t.children?"":"no-children-tree-node"},fnBack:function(){this.$router.push({name:"publicRoleManager"})},onPanelChange:function(e,t){this.effectiveTime=e.format("YYYY-MM-DD")},onSelectMenu:function(e){var t=this,n=(e.item,e.key),s=e.keyPath,r=[],i=this.$refs.xTable;r=[].concat((0,a.Z)(r),(0,a.Z)(i.getCheckboxRecords()),(0,a.Z)(i.getCheckboxIndeterminateRecords(!0)));var c=[];r.forEach((function(e){c.push(e.resourceId)})),this.currentCheckedList.sort().toString()!==c.sort().toString()?this.$confirm({title:"提示",content:"当前菜单授权修改未保存，是否保存?",cancelText:"取消",okText:"确认",onOk:function(){t.rootId=n,t.fnAuthorityMgSave(),t.menuSelectedKeys=s},onCancel:function(){t.menuSelectedKeys=s,t.fnQueryRePermissionByResourceId(n)}}):(this.menuSelectedKeys=s,this.fnQueryRePermissionByResourceId(n))},fnCreateResourceIds:function(e,t){var n=this;e.children&&e.children.forEach((function(e){n.fnCreateResourceIds(e,t)})),t.push(e.resourceId)},fnCreateCheckedResourceIds:function(e,t){var n=this;e.children&&e.children.forEach((function(e){n.fnCreateCheckedResourceIds(e,t)})),e.checked&&t.push(e.resourceId)},fnSaveAuthorityEffectiveTime:function(e){var t=this,n=[];this.fnCreateCheckedResourceIds(this.authrityTree[0],n);var s={roleId:this.role.roleId,resourceIds:n.join(","),effectTime:e?null:this.effectiveTime};i.Z.updateBatchUsePermissionEffectiveTime(s,(function(e){t.$message.success("批量设置有效期成功"),t.fnQueryRePermissionByResourceId(t.rootId),t.batchPop=!1})),this.indexClick=null},onCellChange:function(e,t){var n=this,s={roleId:this.role.roleId,resourceId:e.resourceId,effectTime:t?null:this.effectiveTime};i.Z.updateUsePermissionEffectiveTime(s,(function(e){n.$message.success("修改有效期成功"),n.fnQueryRePermissionByResourceId(n.rootId),n.indexClick=null}))},fnAuthorityMgSave:function(){var e=this,t=[],n=this.$refs.xTable;t=[].concat((0,a.Z)(t),(0,a.Z)(n.getCheckboxRecords()),(0,a.Z)(n.getCheckboxIndeterminateRecords(!0))),t=t.filter((function(e){return!e.loginAccess}));var s=[];t.forEach((function(e){s.push(e.resourceId)})),s=[].concat((0,a.Z)(s),(0,a.Z)(this.tempCheckedList)),i.Z.addUsePermission({roleId:this.role.roleId,resourceIds:s.join(",")},(function(t){e.fnSaveRestUrl()}))},fnSaveRestUrl:function(){var e=this,t=this.$refs.xTable,n=(0,c.Z)(t.getData()),s=[];n.forEach((function(e){var t=e.restUrlAuthorityPoList;t&&t.length>0&&t.forEach((function(e){"1"===e.checked&&s.push({resourceId:e.resourceId,urlId:e.urlId})}))})),i.Z.changeRestAuthority({roleId:this.role.roleId,resourceId:this.rootId,jsonArray:JSON.stringify(s)},(function(t){e.$message.success("保存功能菜单权限成功"),e.fnQueryRePermissionByResourceId(e.rootId)}))},fnQueryRePermissionByRoleId:function(){var e=this;i.Z.queryRePermissionByRoleId({roleId:this.role.roleId},(function(t){e.menuData=t.data.rePermissions,e.$nextTick((function(){e.menuSelectedKeys=[e.menuData[0].resourceId],e.fnQueryRePermissionByResourceId(e.menuData[0].resourceId,0)}))}))},fnQueryRePermissionByResourceId:function(e,t){var n=this;this.authrityTree=[],this.rootId=e,i.Z.queryRePermissionByRoleId({roleId:this.role.roleId,resourceId:e},(function(e){n.authrityTree=e.data.rePermissions,n.originAuthrityTreeCopyData=(0,o.Z)(n.authrityTree,!0),n.currentCheckedList=e.data.checkedList,n.allCheckedList=e.data.allCheckedList,n.tempCheckedList=n.allCheckedList.filter((function(t){return!e.data.checkedList.includes(t)})),n.$nextTick((function(){n.tempNodeCheck=e.data.checkedList}))}))}}},m=h,y=n(1001),p=(0,y.Z)(m,s,r,!1,null,"20851674",null),b=p.exports},46981:function(e,t){var n="org/authority/roleAuthorityManagementRestService/";t["Z"]={queryCurrentAdminRoleWrapeOrgTree:function(e,t){Base.submit(null,{url:n+"queryCurrentAdminRoleWrapeOrgTree",data:e,showPageLoading:!1},{successCallback:function(e){return t(e)}})},queryRolesByOrgId:function(e,t){Base.submit(null,{url:n+"queryRolesByOrgId",data:e},{successCallback:function(e){return t(e)}})},queryAuthRole:function(e,t){Base.submit(null,{url:n+"queryAuthRole",data:e},{successCallback:function(e){return t(e)}})},copyResource:function(e,t,s){Base.submit(e,{url:n+"copyResource",data:t,autoValid:!0},{successCallback:function(e){return s(e)}}).then((function(e){})).catch((function(e){}))},queryUsePermissionByRoleId:function(e,t){Base.submit(null,{url:n+"queryUsePermissionByRoleId",data:e},{successCallback:function(e){return t(e)}})},queryCustomUsePermissionByRoleId:function(e,t){Base.submit(null,{url:n+"queryCustomUsePermissionByRoleId",data:e},{successCallback:function(e){return t(e)}})},copyRole:function(e,t,s){Base.submit(e,{url:n+"copyRole",data:t,autoValid:!0},{successCallback:function(e){return s(e)}}).then((function(e){})).catch((function(e){}))},queryRePermission:function(e,t){Base.submit(null,{url:n+"queryRePermission",data:e},{successCallback:function(e){return t(e)}})},queryCustomRePermission:function(e,t){Base.submit(null,{url:n+"queryCustomRePermission",data:e},{successCallback:function(e){return t(e)}})},addBatchUsePermissionByMoreRole:function(e,t){Base.submit(null,{url:n+"addBatchUsePermissionByMoreRole",data:e},{successCallback:function(e){return t(e)}})},deleteBatchUsePermissionByMoreRole:function(e,t){Base.submit(null,{url:n+"deleteBatchUsePermissionByMoreRole",data:e},{successCallback:function(e){return t(e)}})},addRole:function(e,t,s){Base.submit(e,{url:n+"addRole",data:t,autoValid:!0},{successCallback:function(e){return s(e)}}).then((function(e){})).catch((function(e){}))},updateRoleByRoleId:function(e,t,s){Base.submit(null,{url:n+"updateRoleByRoleId",data:t,autoValid:!0},{successCallback:function(e){return s(e)}}).then((function(e){})).catch((function(e){}))},updateBatchUsePermissionEffectiveTime:function(e,t){Base.submit(null,{url:n+"updateBatchUsePermissionEffectiveTime",data:e},{successCallback:function(e){return t(e)}})},updateUsePermissionEffectiveTime:function(e,t){Base.submit(null,{url:n+"updateUsePermissionEffectiveTime",data:e},{successCallback:function(e){return t(e)}})},addUsePermission:function(e,t){Base.submit(null,{url:n+"addUsePermission",data:e},{successCallback:function(e){return t(e)}})},changeRestAuthority:function(e,t){Base.submit(null,{url:n+"changeRestAuthority",data:e},{successCallback:function(e){return t(e)}})},queryRePermissionByRoleId:function(e,t){Base.submit(null,{url:n+"queryRePermissionByRoleId",data:e},{successCallback:function(e){return t(e)}})},updateBatchCustomResourceUsePermissionEffectiveTime:function(e,t){Base.submit(null,{url:n+"updateBatchCustomResourceUsePermissionEffectiveTime",data:e},{successCallback:function(e){return t(e)}})},updateCustomResourceUsePermissionEffectiveTime:function(e,t){Base.submit(null,{url:n+"updateCustomResourceUsePermissionEffectiveTime",data:e},{successCallback:function(e){return t(e)}})},queryCustomRePermissionByRoleId:function(e,t){Base.submit(null,{url:n+"queryCustomRePermissionByRoleId",data:e},{successCallback:function(e){return t(e)}})},addCustomResourceUsePermission:function(e,t){Base.submit(null,{url:n+"addCustomResourceUsePermission",data:e},{successCallback:function(e){return t(e)}})},updateRoleEffectiveByRoleId:function(e,t){Base.submit(null,{url:n+"updateRoleEffectiveByRoleId",data:e},{successCallback:function(e){return t(e)}})},deleteBatchRole:function(e,t){Base.submit(null,{url:n+"deleteBatchRole",data:e},{successCallback:function(e){return t(e)}})},deleteRoleUserByKey:function(e,t){Base.submit(null,{url:n+"deleteRoleUserByKey",data:e},{successCallback:function(e){return t(e)}})},deleteBatchRoleUsers:function(e,t){Base.submit(null,{url:n+"deleteBatchRoleUser",data:e},{successCallback:function(e){return t(e)}})},queryUsersByRoleId:function(e,t){Base.submit(null,{url:n+"queryUsersByRoleId",data:e},{successCallback:function(e){return t(e)}})},queryUsersByRoleIdNoPage:function(e,t){Base.submit(null,{url:n+"queryUsersByRoleIdNoPage",data:e},{successCallback:function(e){return t(e)}})},queryUsersNoWraperByRoleId:function(e,t){Base.submit(null,{url:n+"queryUsersNoWraperByRoleId",data:e},{successCallback:function(e){return t(e)}})},queryUsersNoWraperByRoleIdNoPage:function(e,t){Base.submit(null,{url:n+"queryUsersNoWraperByRoleIdNoPage",data:e},{successCallback:function(e){return t(e)}})},addBatchRoleUsers:function(e,t,s){Base.submit(null,{url:n+"addBatchRoleUsers",data:e,showPageLoading:!1},{successCallback:function(e){return t(e)},failCallback:function(e){return s(e)}})},deleteBatchRoleUser:function(e,t,s){Base.submit(null,{url:n+"deleteBatchRoleUser",data:e,showPageLoading:!1},{successCallback:function(e){return t(e)},failCallback:function(e){return s(e)}})},addBatchUserRole:function(e,t){Base.submit(null,{url:n+"addBatchUserRole",data:e},{successCallback:function(e){return t(e)}})},deleteBatchUserRole:function(e,t){Base.submit(null,{url:n+"deleteBatchUserRole",data:e},{successCallback:function(e){return t(e)}})},queryCustomRePermissionAsync:function(e,t){Base.submit(null,{url:n+"queryCustomRePermissionAsync",data:e},{successCallback:function(e){return t(e)}})},addAllRoleUsersByOrgId:function(e,t){Base.submit(null,{url:n+"addAllRoleUsersByOrgId",data:e},{successCallback:function(e){return t(e)}})}}}}]);
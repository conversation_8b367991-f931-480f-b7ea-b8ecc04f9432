"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[7073],{79747:function(e,t,o){o.r(t),o.d(t,{default:function(){return N}});var a=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",{staticClass:"fit",attrs:{id:"publicRoleManager"}},[o("ta-border-layout",{staticStyle:{padding:"10px"},attrs:{"layout-type":"flexBorder"}},[o("div",{staticStyle:{"text-align":"center"},attrs:{slot:"header"},slot:"header"},[o("ta-input-search",{staticClass:"search-box",attrs:{placeholder:"请输入角色名称"},on:{search:e.fnQueryRolesByOrgId},model:{value:e.searchInfo,callback:function(t){e.searchInfo=t},expression:"searchInfo"}},[o("ta-button",{attrs:{slot:"enterButton",type:"primary"},slot:"enterButton"},[e._v(" 搜索 ")])],1)],1),o("ta-border-layout",{attrs:{"layout-type":"flexBorder"}},[o("div",{staticStyle:{"line-height":"45px"},attrs:{slot:"header"},slot:"header"},[o("ta-tree-select",{staticStyle:{width:"250px","margin-right":"10px"},attrs:{"dropdown-style":{maxHeight:"300px",overflow:"auto"},placeholder:"请选择组织机构",url:e.organizationTreeData,"tree-data":e.orgOptions,"tree-node-label-prop":"namePath","tree-id":"orgVos","tree-node-id":"orgId","tree-data-label":"orgName","tree-data-value":"orgId","allow-clear":"","search-placeholder":"请输入组织机构名称","show-search":"","filter-tree-node":e.filter,"loaded-data-call-back":e.fnLoadedOrgCallBack},on:{"update:treeData":function(t){e.orgOptions=t},"update:tree-data":function(t){e.orgOptions=t},change:e.onChange},model:{value:e.casValue,callback:function(t){e.casValue=t},expression:"casValue"}}),o("ta-checkbox",{attrs:{checked:e.isSub},on:{change:e.onChangeIsSub}},[e._v(" 包含子组织 ")]),o("ta-tag-select",{attrs:{title:"状态",data:[{value:"1",label:"启用"},{value:"0",label:"禁用"}]},on:{change:e.fnQueryRolesByOrgId},model:{value:e.effective,callback:function(t){e.effective=t},expression:"effective"}}),o("ta-tag-select",{attrs:{title:"该角色是否对子组织管理员可见",data:[{value:"1",label:"是"},{value:"0",label:"否"}]},on:{change:e.fnQueryRolesByOrgId},model:{value:e.subordinate,callback:function(t){e.subordinate=t},expression:"subordinate"}}),o("div",{staticStyle:{float:"right"}},[o("ta-button",{staticClass:"step1",attrs:{type:"primary"},on:{click:e.fnNewPublicRole}},[e._v(" 新增角色 ")]),o("ta-dropdown",{attrs:{trigger:["click"]}},[o("ta-menu",{attrs:{slot:"overlay"},slot:"overlay"},[o("ta-menu-item",{attrs:{disabled:!e.selectedRowKeys.length}},[o("ta-popconfirm",{attrs:{title:"确认启用所选角色?","cancel-text":"取消","ok-text":"确认"},on:{confirm:function(t){return e.handleMenuClick("1")}}},[o("ta-icon",{attrs:{type:"check-circle"}}),o("span",{staticClass:"mg-l12"},[e._v("启用")])],1)],1),o("ta-menu-divider"),o("ta-menu-item",{attrs:{disabled:!e.selectedRowKeys.length}},[o("ta-popconfirm",{attrs:{title:"确认禁用所选角色?","cancel-text":"取消","ok-text":"确认"},on:{confirm:function(t){return e.handleMenuClick("0")}}},[o("ta-icon",{attrs:{type:"stop"}}),o("span",{staticClass:"mg-l12"},[e._v("禁用")])],1)],1),o("ta-menu-divider"),o("ta-menu-item",{attrs:{disabled:!e.selectedRowKeys.length},on:{click:function(t){0===e.selectedRowKeys.length?e.$message.warning("请先选择数据"):e.batchDeleteVisible=!0}}},[o("ta-icon",{attrs:{type:"close-circle"}}),e._v(" 删除 ")],1),o("ta-menu-divider"),o("ta-menu-item",{key:"1",attrs:{disabled:!e.selectedRowKeys.length},on:{click:function(t){return e.fnBatchRoles(1)}}},[o("ta-icon",{attrs:{type:"setting"}}),e._v(" 批量授予 ")],1),o("ta-menu-divider"),o("ta-menu-item",{key:"2",attrs:{disabled:!e.selectedRowKeys.length},on:{click:function(t){return e.fnBatchRoles(2)}}},[o("ta-icon",{attrs:{type:"setting"}}),e._v(" 批量收回 ")],1)],1),o("ta-button",[e._v(" 批量操作 "),o("ta-icon",{attrs:{type:"down"}})],1)],1)],1)],1),o("ta-big-table",{attrs:{stripe:"",resizable:"","highlight-hover-row":"",height:"auto","checkbox-config":{highlight:!0,checkMethod:e.customCheckbox},data:e.roleData},on:{"checkbox-all":e.onSelectChange,"checkbox-change":e.onSelectChange,"cell-click":e.onRowClick}},[o("ta-big-table-column",{attrs:{type:"checkbox",width:"40",fixed:"left"}}),o("ta-big-table-column",{attrs:{field:"roleName",fixed:"left",width:"120",title:"角色名称",sortable:"","show-overflow":"tooltip"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[o("span",{class:{invalidStyle:"0"==a.effective||"1"==a.expire||a.effectiveTime&&a.effectiveTime<e.currentTime}},[e._v(e._s(a.roleName))])]}}])}),o("ta-big-table-column",{attrs:{width:"120",field:"namePath",title:"组织路径","show-overflow":"ellipsis",sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[o("ta-tooltip",[o("span",{attrs:{slot:"title"},slot:"title"},[e._v(e._s(a.namePath))]),o("span",[e._v(e._s(a.namePath.slice(a.namePath.lastIndexOf("/")+1)))])])]}}])}),o("ta-big-table-column",{attrs:{field:"roleSign",title:"角色标识","collection-type":"ROLESIGN",width:"120"}}),o("ta-big-table-column",{attrs:{field:"roleDesc",title:"角色描述","min-width":"120","show-overflow":"tooltip"}}),o("ta-big-table-column",{attrs:{field:"subordinate",width:"120",title:"该角色是否对子组织管理员可见",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[e._v(" "+e._s("1"===o.subordinate?"是":"否")+" ")]}}])}),o("ta-big-table-column",{attrs:{field:"effectiveTime",width:"120",title:"有效期",sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[o("span",{class:{invalidStyle:"1"==a.expire}},[e._v(" "+e._s(null==a.effectiveTime?"永久":e.Base.getMoment(a.effectiveTime).format("YYYY-MM-DD"))+" ")])]}}])}),o("ta-big-table-column",{attrs:{field:"expire",width:"120",title:"是否过期"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return["0"==a.expire?o("ta-tag",{staticClass:"no-cursor",attrs:{type:"success"}},[e._v(" 未过期 ")]):"1"==a.expire?o("ta-tag",{staticClass:"no-cursor",attrs:{type:"danger"}},[e._v(" 过期 ")]):e._e()]}}])}),o("ta-big-table-column",{attrs:{field:"effective",title:"状态",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[o("ta-tag",{staticClass:"no-cursor",attrs:{type:"1"==a.effective?"success":"danger"}},[e._v(" "+e._s(e.CollectionLabel("STATE",a.effective))+" ")])]}}])}),o("ta-big-table-column",{attrs:{fixed:"right",field:"roleOperation",width:"150",align:"center"},scopedSlots:e._u([{key:"header",fn:function(){return[o("span",{staticClass:"step2"},[e._v("角色操作")])]},proxy:!0},{key:"default",fn:function(t){return o("span",{},[o("ta-table-operate",{attrs:{"operate-menu":e.operateMenu,"row-info":t}})],1)}}])}),o("ta-big-table-column",{attrs:{fixed:"right",field:"operation",title:"管理",width:"200",align:"center"},scopedSlots:e._u([{key:"header",fn:function(){return[o("span",{staticClass:"step3"},[e._v("管理")])]},proxy:!0},{key:"default",fn:function(t){return o("span",{},[o("ta-table-operate",{attrs:{"operate-menu":e.manageOperateMenu,"row-info":t}})],1)}}])})],1)],1),o("div",{attrs:{slot:"footer"},slot:"footer"},[o("ta-pagination",{ref:"roleGridPager",staticStyle:{float:"right","margin-top":"10px"},attrs:{"show-size-changer":"","show-quick-jumper":"","data-source":e.roleData,"default-page-size":10,params:e.rolePageParams,url:"org/authority/roleAuthorityManagementRestService/queryRolesByOrgId"},on:{"update:dataSource":function(t){e.roleData=t},"update:data-source":function(t){e.roleData=t}}})],1)],1),o("ta-modal",{attrs:{title:"角色复制",centered:"",width:"1000px","body-style":{height:"500px",padding:"0"},"destroy-on-close":!0,"mask-closable":!1,getContainer:e.fnGetContainer},model:{value:e.roleCopyVisible,callback:function(t){e.roleCopyVisible=t},expression:"roleCopyVisible"}},[o("template",{slot:"footer"},[o("ta-button",{on:{click:function(t){e.roleCopyVisible=!1}}},[e._v(" 取消 ")]),o("ta-button",{key:"submit",attrs:{type:"primary"},on:{click:e.fnRoleCopySave}},[e._v(" 保存 ")])],1),o("roleCopy",{ref:"roleCopyChild",attrs:{"role-obj":e.currentSelectedPublicRole,"org-tree-data":e.orgOptions},on:{fnQueryRolesByOrgId:e.fnQueryRolesByOrgId}})],2),o("ta-modal",{attrs:{title:"权限复制",centered:"",width:"1000px","body-style":{height:"500px",padding:"0"},"destroy-on-close":!0,"mask-closable":!1,getContainer:e.fnGetContainer},model:{value:e.authorityCopyVisible,callback:function(t){e.authorityCopyVisible=t},expression:"authorityCopyVisible"}},[o("template",{slot:"footer"},[o("ta-button",{on:{click:function(t){e.authorityCopyVisible=!1}}},[e._v(" 取消 ")]),o("ta-button",{key:"submit",attrs:{type:"primary"},on:{click:e.fnAuthorityCopySave}},[e._v(" 保存 ")])],1),o("authorityCopy",{ref:"authorityCopyChild",attrs:{"role-obj":e.currentSelectedPublicRole}})],2),o("ta-drawer",{attrs:{title:e.newRoleTitle+"667",placement:"right",visible:e.newRoleVisible,"destroy-on-close":!0,width:"500","get-container":e.fnGetContainer},on:{close:function(t){e.newRoleVisible=!1}}},[o("newRole",{ref:"newRole",attrs:{"update-obj":e.updateObj},on:{onNewRoleClose:function(t){e.newRoleVisible=!1},fnQueryRolesByOrgId:e.fnQueryRolesByOrgId}}),o("div",{attrs:{slot:"footer"},slot:"footer"},[o("ta-button-group",[o("ta-button",{on:{click:function(t){return e.$refs.newRole.fnResetForm()}}},[e._v(" 重置 ")]),o("ta-button",{attrs:{type:"primary"},on:{click:function(t){return e.$refs.newRole.fnAddOrUpdateRole()}}},[e._v(" 保存 ")])],1)],1)],1),o("ta-careful-delete",{attrs:{visible:e.batchDeleteVisible,title:"角色删除",description:"选中角色"},on:{close:function(t){e.batchDeleteVisible=!1},delete:e.fnBeforeDeleteRole}})],1)},r=[],i=o(46981),n=function(){var e=this,t=this,o=t.$createElement,a=t._self._c||o;return a("div",{staticClass:"fit",attrs:{id:"roleCopy"}},[a("ta-row",{staticClass:"fit"},[a("ta-col",{staticClass:"fit",attrs:{span:12}},[a("ta-border-layout",{staticStyle:{"border-right-width":"7px"},attrs:{layout:{header:"50px"}}},[a("template",{slot:"header"},[t._v(t._s(this.roleObj.roleName)+"下的权限")]),a("ta-tabs",{staticClass:"fit",attrs:{defaultActiveKey:"1"}},[a("ta-tab-pane",{key:"1",staticStyle:{"padding-top":"10px"},attrs:{tab:"功能菜单权限"}},[a("ta-table",{attrs:{columns:t.menuAuthorityColumns,dataSource:t.menuAuthorityData,defaultExpandAllRows:"",size:"small",pagination:!1}})],1),a("ta-tab-pane",{key:"2",staticStyle:{"padding-top":"10px"},attrs:{tab:"自定义对象权限"}},[a("ta-table",{attrs:{columns:t.customAuthorityColumns,dataSource:t.customAuthorityData,defaultExpandAllRows:"",size:"small",pagination:!1}})],1)],1)],2)],1),a("ta-col",{staticClass:"fit",attrs:{span:12}},[a("ta-border-layout",{staticStyle:{"border-left-width":"7px"},attrs:{layout:{header:"50px"}}},[a("template",{slot:"header"},[t._v("复制的新角色信息")]),a("ta-form",{staticStyle:{"padding-top":"10px"},attrs:{autoFormCreate:function(t){e.form=t}}},[a("ta-form-item",{attrs:{label:"新名称",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"newRoleName",fieldDecoratorOptions:{rules:[{required:!0,message:"请输入角色名称"}]}}},[a("ta-input")],1),a("ta-form-item",{attrs:{label:"目标组织",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"orgId",fieldDecoratorOptions:{rules:[{required:!0,message:"请选择目标组织"}]}}},[a("ta-cascader",{staticClass:"vertical-space cascader-box",attrs:{options:t.orgOptions,showSearch:{filter:t.filter},changeOnSelect:"",placeholder:"请选择组织机构",expandTrigger:"hover",fieldNames:{label:"orgName",value:"orgId",children:"children"},url:"org/authority/roleAuthorityManagementRestService/queryCurrentAdminRoleWrapeOrgTree",treeId:"orgVos"},on:{"update:options":function(e){t.orgOptions=e},change:t.onChange}})],1)],1)],2)],1)],1)],1)},s=[],l=[{title:"功能名称",dataIndex:"resourceName"},{title:"有效期",dataIndex:"effectTime",customRender:function(e){return e?e.split(" ")[0]:"永久"}}],u=[{title:"功能名称",dataIndex:"resourceName"},{title:"有效期",dataIndex:"effectTime",customRender:function(e){return e?e.split(" ")[0]:"永久"}}],c={name:"roleCopy",props:["roleObj","orgTreeData"],data:function(){return{orgOptions:this.orgTreeData,menuAuthorityColumns:l,menuAuthorityData:[],customAuthorityColumns:u,customAuthorityData:[],formItemLayout:{labelCol:{span:6},wrapperCol:{span:18}}}},mounted:function(){this.fnQueryAuthorityByRoleId(),this.fnQueryCustomUsePermissionByRoleId()},methods:{onChange:function(e,t){var o=this;t&&"0"==t[t.length-1].isAuthority&&(this.$message.error("没有权限在该组织下新增角色"),this.$nextTick((function(){o.form.resetFields("orgId")})))},filter:function(e,t){return t.some((function(t){return t.label.toLowerCase().indexOf(e.toLowerCase())>-1}))},fnSave:function(){var e=this,t=this.form.getFieldValue("newRoleName"),o=this.form.getFieldValue("orgId"),a={roleId:this.roleObj.roleId,roleName:t,orgId:o?o[o.length-1]:null};i.Z.copyRole(this.form,a,(function(t){t.serviceSuccess?(e.$message.success("角色复制成功"),e.$emit("fnQueryRolesByOrgId")):e.$message.error("角色复制失败")}))},fnQueryAuthorityByRoleId:function(){var e=this;i.Z.queryUsePermissionByRoleId({roleId:this.roleObj.roleId},(function(t){e.menuAuthorityData=t.data.usePermissionPos}))},fnQueryCustomUsePermissionByRoleId:function(){var e=this;i.Z.queryCustomUsePermissionByRoleId({roleId:this.roleObj.roleId},(function(t){e.customAuthorityData=t.data.customUsePermissionPos}))}}},d=c,f=o(1001),m=(0,f.Z)(d,n,s,!1,null,null,null),h=m.exports,p=function(){var e=this,t=this,o=t.$createElement,a=t._self._c||o;return a("div",{staticClass:"fit",attrs:{id:"authrityCopy"}},[a("ta-row",{staticClass:"fit"},[a("ta-col",{staticClass:"fit",attrs:{span:12}},[a("ta-border-layout",{staticStyle:{"border-right-width":"7px"},attrs:{layout:{header:"50px"}}},[a("template",{slot:"header"},[t._v(" "+t._s(this.roleObj.roleName)+"下的权限 ")]),a("ta-tabs",{staticClass:"fit",attrs:{"default-active-key":"1"}},[a("ta-tab-pane",{key:"1",staticStyle:{"padding-top":"10px"},attrs:{tab:"功能菜单权限"}},[a("ta-table",{attrs:{columns:t.menuAuthorityColumns,"data-source":t.menuAuthorityData,"default-expand-all-rows":"",size:"small",pagination:!1}})],1),a("ta-tab-pane",{key:"2",staticStyle:{"padding-top":"10px"},attrs:{tab:"自定义对象权限"}},[a("ta-table",{attrs:{columns:t.customAuthorityColumns,"data-source":t.customAuthorityData,"default-expand-all-rows":"",size:"small",pagination:!1}})],1)],1)],2)],1),a("ta-col",{staticClass:"fit",attrs:{span:12}},[a("ta-border-layout",{staticStyle:{"border-left-width":"7px"},attrs:{layout:{header:"50px"}}},[a("template",{slot:"header"},[t._v(" 权限复制的目标角色 ")]),a("ta-form",{staticStyle:{"padding-top":"10px"},attrs:{"auto-form-create":function(t){e.form=t}}},[a("ta-form-item",{attrs:{label:"目标角色","label-col":{span:6},"wrapper-col":{span:16},"field-decorator-id":"targetRole","field-decorator-options":{rules:[{required:!0,message:"请选择目标角色"}]}}},[a("ta-input-search",{ref:"targetRoleInput",attrs:{placeholder:"请选择目标角色"},on:{focus:t.handleShwoRolePanel,search:function(e){t.isUserShow=!0}}})],1)],1)],2)],1)],1),a("ta-user-select",{attrs:{id:"targetRoleSelect",title:"目标角色选择","is-show":t.isUserShow,"include-child-title":"包含子部门下角色","user-tree-data":t.orgOptions,"user-list-data":t.roleListData,"default-user-list":t.defaultUserList,props:t.selectRoleProps,"is-multi":!0,load:t.loadNode,"is-select":!1,pagination:!0},on:{close:t.handleClose,queryUserList:t.fnQueryRoleList,getUserListResult:t.fnGetRoleListResult,checkAllMembers:t.checkAllMembers}})],1)},y=[],b=[{title:"功能名称",dataIndex:"resourceName"},{title:"有效期",dataIndex:"effectTime",customRender:function(e){return e?e.split(" ")[0]:"永久"}}],g=[{title:"功能名称",dataIndex:"resourceName"},{title:"有效期",dataIndex:"effectTime",customRender:function(e){return e?e.split(" ")[0]:"永久"}}],R={name:"authorityCopy",props:["roleObj"],data:function(){return{roleListData:[],orgOptions:[],defaultUserList:[],isUserShow:!1,menuAuthorityColumns:b,menuAuthorityData:[],customAuthorityColumns:g,customAuthorityData:[],targetRoleIds:"",selectRoleProps:{treeNodeKey:"value",treeLabel:"label",treeChildren:"children",listKey:"roleId",listTitle:"roleName",listSubTitle:"mobile",listDescription:"namePath"},total:void 0}},mounted:function(){this.fnQueryAuthorityByRoleId(),this.fnQueryCustomUsePermissionByRoleId()},methods:{handleShwoRolePanel:function(){this.$refs.targetRoleInput.blur(),this.isUserShow=!0},handleClose:function(){this.isUserShow=!1},loadNode:function(e,t){i.Z.queryCurrentAdminRoleWrapeOrgTree({orgId:e.data?e.data.value:null},(function(e){t(e.data.orgVos)}))},fnGetRoleListResult:function(e){var t=[],o=[];e.forEach((function(e,a){t.push(e.roleName),o.push(e.roleId)})),this.defaultUserList=e,this.form.setFieldsValue({targetRole:t.join(",")}),this.targetRoleIds=o.join(",")},fnQueryRoleList:function(e,t,o){var a=this,r={pageNumber:o,pageSize:10,orgId:e,isSub:t,roleType:this.roleObj.roleType};i.Z.queryAuthRole(r,(function(e){a.roleListData=e.data.pageBean.list,void 0===a.total&&(a.total=e.data.pageBean.total)}))},filter:function(e,t){return t.some((function(t){return t.label.toLowerCase().indexOf(e.toLowerCase())>-1}))},fnSave:function(){var e=this,t={sourceRoleId:this.roleObj.roleId,targetRoleIds:this.targetRoleIds};i.Z.copyResource(this.form,t,(function(t){t.serviceSuccess?e.$message.success("权限复制成功"):e.$message.error("权限复制失败")}))},fnQueryAuthorityByRoleId:function(){var e=this;i.Z.queryUsePermissionByRoleId({roleId:this.roleObj.roleId},(function(t){e.menuAuthorityData=t.data.usePermissionPos}))},fnQueryCustomUsePermissionByRoleId:function(){var e=this;i.Z.queryCustomUsePermissionByRoleId({roleId:this.roleObj.roleId},(function(t){e.customAuthorityData=t.data.customUsePermissionPos}))},checkAllMembers:function(e,t,o){var a={pageNumber:1,pageSize:this.total,orgId:e,isSub:t,roleType:this.roleObj.roleType};i.Z.queryAuthRole(a,(function(e){o(e.data.pageBean.list)}))}}},v=R,C=(0,f.Z)(v,p,y,!1,null,null,null),w=C.exports,k=function(){var e=this,t=this,o=t.$createElement,a=t._self._c||o;return a("ta-form",{attrs:{"auto-form-create":function(t){e.form=t}}},[a("ta-form-item",{attrs:{label:"角色名称","field-decorator-id":"roleName","field-decorator-options":{initialValue:t.formData.roleName,rules:[{required:!0,message:"请输入角色名称"},{max:50,message:"角色名称最大长度为50"}]}}},[a("ta-input",{attrs:{placeholder:"请输入角色名称"}})],1),a("ta-form-item",{staticStyle:{"margin-bottom":"0"},attrs:{label:"所属组织","field-decorator-id":"orgId",require:{message:"请选择所属组织"},"field-decorator-options":{initialValue:t.formData.orgId}}},[a("project-search-tree",{attrs:{"get-container-id":"publicRoleManager","init-value":t.formData.namePath}})],1),a("ta-form-item",{attrs:{"wrapper-col":{span:18,offset:6},"field-decorator-id":"subordinate","field-decorator-options":{initialValue:t.formData.subordinate,valuePropName:"checked"}}},[a("ta-checkbox",[t._v(" 该角色是否对子组织管理员可见 ")])],1),a("ta-form-item",{attrs:{label:"有效期","field-decorator-id":"effectiveTime","field-decorator-options":{initialValue:t.formData.effectiveTime}}},[a("ta-date-picker",{staticStyle:{width:"100%"},attrs:{format:"YYYY-MM-DD","valid-now-time":"left"}})],1),a("ta-form-item",{attrs:{label:"角色标识","field-decorator-id":"roleSign","field-decorator-options":{initialValue:t.formData.roleSign}}},[a("ta-select",{attrs:{placeholder:"请选择角色标识","allow-clear":""}},t._l(t.CollectionData("ROLESIGN"),(function(e){return a("ta-select-option",{key:e.value,attrs:{value:e.value}},[t._v(" "+t._s(e.label)+" ")])})),1)],1),a("ta-form-item",{attrs:{label:"角色描述","field-decorator-id":"roleDesc","field-decorator-options":{initialValue:t.formData.roleDesc,rules:[{max:50,message:"角色描述最大长度为50"}]}}},[a("ta-textarea",{attrs:{rows:4,placeholder:"请选择角色描述"}})],1)],1)},B=[],I=o(95082),P=o(36797),S=o.n(P),O={name:"newRole",props:["updateObj"],data:function(){return{orgOptions:[],formData:{}}},mounted:function(){var e=this.updateObj,t=e.isUpdate,o=e.roleObj;t&&(this.formData=(0,I.Z)({},o),this.formData.idPath=o.idPath,this.formData.namePath=o.namePath,this.formData.subordinate="1"===o.subordinate,this.formData.roleDesc=o.roleDesc||"",this.formData.effectiveTime=o.effectiveTime?S()(o.effectiveTime,"YYYY-MM-DD HH:mm:ss"):null)},methods:{moment:S(),fnResetForm:function(){this.form.resetFields()},onChange:function(e,t){var o=this;t&&"0"===t[t.length-1].isAuthority&&(this.$message.error("没有权限在该组织下操作角色"),this.$nextTick((function(){o.form.resetFields("orgId")})))},filter:function(e,t){return t.some((function(t){return t.label.toLowerCase().indexOf(e.toLowerCase())>-1}))},loadOrgTree:function(){var e=this;i.Z.queryCurrentAdminRoleWrapeOrgTree({},(function(t){e.orgOptions=t.data.orgVos}))},fnNewRole:function(){var e=this;this.form.validateFields((function(t,o){if(!t){var a=o.orgId;o.orgId=null!==a&&void 0!==a?a:"",o.subordinate=o.subordinate?"1":"0",o.roleType="01",o.effectiveTime=o.effectiveTime?o.effectiveTime.format("YYYY-MM-DD")+" 23:59:59":""}i.Z.addRole(e.form,(0,I.Z)((0,I.Z)({},o),{},{effective:"1"}),(function(t){e.$message.success("新增角色成功"),e.$emit("fnQueryRolesByOrgId"),e.$emit("onNewRoleClose")}))}))},fnUpdateRole:function(){var e=this;this.form.validateFields((function(t,o){if(!t){var a=o.orgId;o.orgId=null!==a&&void 0!==a?a:"";for(var r=!1,n=Object.keys(o),s=0;s<n.length;s++)if(o[n[s]]!==e.formData[n[s]]){r=!0;break}r?(o.subordinate=o.subordinate?"1":"0",o.roleType="01",o.effectiveTime=o.effectiveTime?o.effectiveTime.format("YYYY-MM-DD")+" 23:59:59":"",i.Z.updateRoleByRoleId(e.form,(0,I.Z)((0,I.Z)({roleId:e.updateObj.roleObj.roleId},o),{},{effective:"1"}),(function(t){e.$message.success("修改角色成功"),e.$emit("fnQueryRolesByOrgId"),e.$emit("onNewRoleClose")}))):e.$message.warning("没有需要保存的修改")}}))},fnAddOrUpdateRole:function(){this.updateObj.isUpdate?this.fnUpdateRole():this.fnNewRole()}}},x=O,D=(0,f.Z)(x,k,B,!1,null,null,null),U=D.exports,T=o(80790),_=o(51097),A=[{element:".step1",popover:{title:"新增角色",description:"创建角色，多个人员可同属一个角色，一个人员可同时拥有多个角色",position:"left"}},{element:".step2",popover:{title:"角色操作",description:"可对角色信息进行编辑，还可启用、禁用、删除角色，同时可以复制角色和角色权限",position:"left"}},{element:".step3",popover:{title:"管理",description:"角色下可通过【人员管理】绑定人员，通过【权限管理】进行【功能菜单权限管理】和【自定义对象权限管理】",position:"left"}}],M={name:"PublicRoleManager",components:{roleCopy:h,authorityCopy:w,newRole:U},mixins:[T.Z],data:function(){var e=this;return{organizationTreeData:"org/authority/roleAuthorityManagementRestService/queryCurrentAdminRoleWrapeOrgTree",searchInfo:"",orgOptions:[],isSub:!0,casValue:void 0,operateMenu:[{name:"编辑",isShow:function(e){return"1"===e.roleMark},disabled:function(e){return"0"===e.effective},title:function(e){return"0"===e.effective?"禁用的角色不允许编辑":""},onClick:function(t){return e.updatePublicRole(t)}},{name:"编辑",style:{color:"rgb(204,204,204)",cursor:"not-allowed"},isShow:function(e){return"1"!==e.roleMark},title:function(e){return"1"!==e.roleMark?"您对相应组织下的该角色无编辑权限":""}},{name:"更多",type:"more",isShow:function(e){return"1"===e.roleMark},moreMenuList:[{name:"启用",type:"confirm",confirmTitle:"确认启用该角色?",onOk:function(t){e.handleMenuClick("1",t)}},{name:"禁用",type:"confirm",confirmTitle:"确认禁用该角色?",onOk:function(t){e.handleMenuClick("0",t)}},{name:"删除",type:"confirm",confirmTitle:"确认删除该角色?",onOk:function(t){e.fnBeforeDeleteRole(t)}},{name:"角色复制",disabled:function(e){return"0"===e.effective},onClick:function(t){return e.fnRoleCopy(t)}},{name:"权限复制",disabled:function(e){return"0"===e.effective},onClick:function(t){return e.fnAuthorityCopy(t)}}]},{name:"更多",type:"more",style:{color:"rgb(204,204,204)",cursor:"not-allowed"},isShow:function(e){return"1"!==e.roleMark},title:function(e){return"1"!==e.roleMark?"您对相应组织下的该角色无操作权限":""}}],manageOperateMenu:[{name:"人员管理",disabled:function(e){return"0"===e.effective},title:function(e){return"0"===e.effective?"禁用的角色不允许进行人员管理":""},onClick:function(t){return e.fnRouteTo(t,"publicRoleUser")}},{name:"权限管理",type:"more",disabled:function(e){return"0"===e.effective},title:function(e){return"0"===e.effective?"禁用的角色不允许进行权限管理":""},isShow:function(e){return"1"===e.roleMark},moreMenuList:[{name:"功能菜单权限管理",onClick:function(t){return e.fnRouteTo(t,"publicRoleAuthority")}},{name:"自定义对象权限管理",onClick:function(t){return e.fnRouteTo(t,"publicRoleCustomAuthority")}}]},{name:"权限管理",type:"more",title:function(e){return"1"!==e.roleMark?"您对相应组织下的该角色无权限管理权限":""},isShow:function(e){return"1"!==e.roleMark},style:{color:"rgb(204,204,204)",cursor:"not-allowed"}}],roleData:[],selectedRowKeys:[],selectedPublicRole:[],currentSelectedPublicRole:{},roleCopyVisible:!1,authorityCopyVisible:!1,updateObj:{},newRoleVisible:!1,newRoleTitle:"新增角色",batchRoles:{},effective:[],subordinate:[],batchDeleteVisible:!1,currentTime:"",steps:A}},mounted:function(){this.currentTime=S()().format("YYYY-MM-DD HH:MM:SS"),this.fnCommonGuide(this.steps)},methods:{moment:S(),rolePageParams:function(){var e,t=this.orgOptions[0]&&this.orgOptions[0].orgId?this.orgOptions[0].orgId:"";return{orgId:null!==(e=this.casValue)&&void 0!==e?e:t,isSub:this.isSub,roleType:"01",roleName:this.searchInfo,effective:this.effective.length>0?this.effective[0]:null,subordinate:this.subordinate.length>0?this.subordinate[0]:null}},fnRouteTo:function(e,t){this.$router.push({name:t,params:{role:e}})},fnBatchRoles:function(e){this.selectedPublicRole.length<1?this.$message.warn("请先选择角色"):this.$router.push({name:"batchAuthority",params:{roles:this.selectedPublicRole,batchType:1===e?"add":"delete"}})},fnAuthorityMgSave:function(){this.$refs.authorityMgChild.fnSave()},handleMenuClick:function(e,t){var o=this,a=[];if(t){if("1"===e&&"1"===t.effective)return void this.$message.warning("该记录已经启用，请勿重复操作！");if("0"===e&&"0"===t.effective)return void this.$message.warning("该记录已经禁用，请勿重复操作！");a.push(t.roleId)}else if(this.selectedPublicRole.forEach((function(e,t){a.push(e.roleId)})),a.length<1)return void this.$message.warn("请先勾选要操作的角色");i.Z.updateRoleEffectiveByRoleId({roleIds:a.join(","),effective:"1"===e?"1":"0"},(function(t){o.$message.success("1"===e?"启用成功":"禁用成功"),o.fnQueryRolesByOrgId()}))},fnBeforeDeleteRole:function(e){var t=[];if(e)t.push(e.roleId);else if(this.selectedPublicRole.forEach((function(e,o){t.push(e.roleId)})),t.length<1)return void this.$message.warn("请勾选要删除的角色");this.deletePublicRole(t)},deletePublicRole:function(e){var t=this;i.Z.deleteBatchRole({roleIds:e.join(",")},(function(e){t.$message.success("删除角色成功"),t.batchDeleteVisible=!1,t.fnQueryRolesByOrgId()}))},fnLoadedOrgCallBack:function(e){this.orgOptions=e,this.fnQueryRolesByOrgId()},onChange:function(e,t){this.casValue=e,this.fnQueryRolesByOrgId()},onChangeIsSub:function(e){this.isSub=e.target.checked,this.fnQueryRolesByOrgId()},filter:function(e,t){var o=e.toLowerCase(),a=null;return a=(0,_.Z)([t],(function(e){return e.label.toLowerCase().indexOf(o)>-1})),a&&a.length>0},onSelectChange:function(e){var t=e.records;this.selectedRowKeys=t,this.selectedPublicRole=t},customCheckbox:function(e){var t=e.row;return"1"===t.roleMark},onRowClick:function(e){var t=e.row,o=e.rowIndex;this.currentSelectedPublicRole=t,this.rowFlag=o},changeRowStyle:function(e,t){if(this.rowFlag===t)return"row-active-bg"},fnRoleCopy:function(e){this.roleCopyVisible=!0,this.currentSelectedPublicRole=e},fnRoleCopySave:function(){this.$refs.roleCopyChild.fnSave()},fnAuthorityCopy:function(e){this.authorityCopyVisible=!0,this.currentSelectedPublicRole=e},fnAuthorityCopySave:function(){this.$refs.authorityCopyChild.fnSave()},fnNewPublicRole:function(){this.newRoleTitle="新增角色",this.newRoleVisible=!0,this.updateObj={isUpdate:!1}},updatePublicRole:function(e){this.newRoleTitle="修改角色",this.newRoleVisible=!0,this.updateObj={isUpdate:!0,roleObj:e}},fnQueryRolesByOrgId:function(){this.$refs.roleGridPager.loadData(),this.selectedRowKeys=[],this.selectedPublicRole=[]},fnGetContainer:function(){return document.getElementById("publicRoleManager")}}},V=M,q=(0,f.Z)(V,a,r,!1,null,"892ebd68",null),N=q.exports},80790:function(e,t,o){var a=o(76698);o(34530);t["Z"]={activated:function(){var e=window.pageVmObj,t=e["steps_"+e._route.name];this.methods?this.methods.fnCommonGuide(t):this.fnCommonGuide(t)},deactivated:function(){this.methods?this.methods.fnCommonGuide([],!0):this.fnCommonGuide([],!0)},methods:{fnCommonGuide:function(e,t){var o=window.pageVmObj;t?(o.driver.reset(),window.fnPageGuide=null):(o["steps_"+o._route.name]=e,o.driver=new a.Z({allowClose:!1}),window.fnPageGuide=function(){o.driver.defineSteps(e),o.driver.start()})}}}},46981:function(e,t){var o="org/authority/roleAuthorityManagementRestService/";t["Z"]={queryCurrentAdminRoleWrapeOrgTree:function(e,t){Base.submit(null,{url:o+"queryCurrentAdminRoleWrapeOrgTree",data:e,showPageLoading:!1},{successCallback:function(e){return t(e)}})},queryRolesByOrgId:function(e,t){Base.submit(null,{url:o+"queryRolesByOrgId",data:e},{successCallback:function(e){return t(e)}})},queryAuthRole:function(e,t){Base.submit(null,{url:o+"queryAuthRole",data:e},{successCallback:function(e){return t(e)}})},copyResource:function(e,t,a){Base.submit(e,{url:o+"copyResource",data:t,autoValid:!0},{successCallback:function(e){return a(e)}}).then((function(e){})).catch((function(e){}))},queryUsePermissionByRoleId:function(e,t){Base.submit(null,{url:o+"queryUsePermissionByRoleId",data:e},{successCallback:function(e){return t(e)}})},queryCustomUsePermissionByRoleId:function(e,t){Base.submit(null,{url:o+"queryCustomUsePermissionByRoleId",data:e},{successCallback:function(e){return t(e)}})},copyRole:function(e,t,a){Base.submit(e,{url:o+"copyRole",data:t,autoValid:!0},{successCallback:function(e){return a(e)}}).then((function(e){})).catch((function(e){}))},queryRePermission:function(e,t){Base.submit(null,{url:o+"queryRePermission",data:e},{successCallback:function(e){return t(e)}})},queryCustomRePermission:function(e,t){Base.submit(null,{url:o+"queryCustomRePermission",data:e},{successCallback:function(e){return t(e)}})},addBatchUsePermissionByMoreRole:function(e,t){Base.submit(null,{url:o+"addBatchUsePermissionByMoreRole",data:e},{successCallback:function(e){return t(e)}})},deleteBatchUsePermissionByMoreRole:function(e,t){Base.submit(null,{url:o+"deleteBatchUsePermissionByMoreRole",data:e},{successCallback:function(e){return t(e)}})},addRole:function(e,t,a){Base.submit(e,{url:o+"addRole",data:t,autoValid:!0},{successCallback:function(e){return a(e)}}).then((function(e){})).catch((function(e){}))},updateRoleByRoleId:function(e,t,a){Base.submit(null,{url:o+"updateRoleByRoleId",data:t,autoValid:!0},{successCallback:function(e){return a(e)}}).then((function(e){})).catch((function(e){}))},updateBatchUsePermissionEffectiveTime:function(e,t){Base.submit(null,{url:o+"updateBatchUsePermissionEffectiveTime",data:e},{successCallback:function(e){return t(e)}})},updateUsePermissionEffectiveTime:function(e,t){Base.submit(null,{url:o+"updateUsePermissionEffectiveTime",data:e},{successCallback:function(e){return t(e)}})},addUsePermission:function(e,t){Base.submit(null,{url:o+"addUsePermission",data:e},{successCallback:function(e){return t(e)}})},changeRestAuthority:function(e,t){Base.submit(null,{url:o+"changeRestAuthority",data:e},{successCallback:function(e){return t(e)}})},queryRePermissionByRoleId:function(e,t){Base.submit(null,{url:o+"queryRePermissionByRoleId",data:e},{successCallback:function(e){return t(e)}})},updateBatchCustomResourceUsePermissionEffectiveTime:function(e,t){Base.submit(null,{url:o+"updateBatchCustomResourceUsePermissionEffectiveTime",data:e},{successCallback:function(e){return t(e)}})},updateCustomResourceUsePermissionEffectiveTime:function(e,t){Base.submit(null,{url:o+"updateCustomResourceUsePermissionEffectiveTime",data:e},{successCallback:function(e){return t(e)}})},queryCustomRePermissionByRoleId:function(e,t){Base.submit(null,{url:o+"queryCustomRePermissionByRoleId",data:e},{successCallback:function(e){return t(e)}})},addCustomResourceUsePermission:function(e,t){Base.submit(null,{url:o+"addCustomResourceUsePermission",data:e},{successCallback:function(e){return t(e)}})},updateRoleEffectiveByRoleId:function(e,t){Base.submit(null,{url:o+"updateRoleEffectiveByRoleId",data:e},{successCallback:function(e){return t(e)}})},deleteBatchRole:function(e,t){Base.submit(null,{url:o+"deleteBatchRole",data:e},{successCallback:function(e){return t(e)}})},deleteRoleUserByKey:function(e,t){Base.submit(null,{url:o+"deleteRoleUserByKey",data:e},{successCallback:function(e){return t(e)}})},deleteBatchRoleUsers:function(e,t){Base.submit(null,{url:o+"deleteBatchRoleUser",data:e},{successCallback:function(e){return t(e)}})},queryUsersByRoleId:function(e,t){Base.submit(null,{url:o+"queryUsersByRoleId",data:e},{successCallback:function(e){return t(e)}})},queryUsersByRoleIdNoPage:function(e,t){Base.submit(null,{url:o+"queryUsersByRoleIdNoPage",data:e},{successCallback:function(e){return t(e)}})},queryUsersNoWraperByRoleId:function(e,t){Base.submit(null,{url:o+"queryUsersNoWraperByRoleId",data:e},{successCallback:function(e){return t(e)}})},queryUsersNoWraperByRoleIdNoPage:function(e,t){Base.submit(null,{url:o+"queryUsersNoWraperByRoleIdNoPage",data:e},{successCallback:function(e){return t(e)}})},addBatchRoleUsers:function(e,t,a){Base.submit(null,{url:o+"addBatchRoleUsers",data:e,showPageLoading:!1},{successCallback:function(e){return t(e)},failCallback:function(e){return a(e)}})},deleteBatchRoleUser:function(e,t,a){Base.submit(null,{url:o+"deleteBatchRoleUser",data:e,showPageLoading:!1},{successCallback:function(e){return t(e)},failCallback:function(e){return a(e)}})},addBatchUserRole:function(e,t){Base.submit(null,{url:o+"addBatchUserRole",data:e},{successCallback:function(e){return t(e)}})},deleteBatchUserRole:function(e,t){Base.submit(null,{url:o+"deleteBatchUserRole",data:e},{successCallback:function(e){return t(e)}})},queryCustomRePermissionAsync:function(e,t){Base.submit(null,{url:o+"queryCustomRePermissionAsync",data:e},{successCallback:function(e){return t(e)}})},addAllRoleUsersByOrgId:function(e,t){Base.submit(null,{url:o+"addAllRoleUsersByOrgId",data:e},{successCallback:function(e){return t(e)}})}}}}]);
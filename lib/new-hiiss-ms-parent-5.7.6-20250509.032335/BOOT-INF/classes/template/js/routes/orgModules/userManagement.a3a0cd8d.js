"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[2050],{80715:function(e,t,r){r.d(t,{Z:function(){return v}});var a,o,s=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("ta-select",{attrs:{mode:"multiple",placeholder:e.placeholder,options:e.CollectionData(e.collection),disabled:e.disabled,"allow-clear":e.allowClear},on:{change:e.handleChange},model:{value:e.selectValue,callback:function(t){e.selectValue=t},expression:"selectValue"}})},i=[],n=r(98754),l=r(41538),c={name:"SelectMultiple",props:["collection","value","disabled","placeholder","allowClear"],data:function(){return{selectValue:[]}},watch:{value:{immediate:!0,handler:function(e,t){(0,n.Z)(e)&&""!==e.trim()?this.selectValue=e.split(","):this.selectValue=[]}}},methods:{handleChange:function(e){(0,l.Z)(e)||(e=[]),this.$emit("input",e.join(",")),this.$emit("change",e.join(","))}}},d=c,u=r(1001),m=(0,u.Z)(d,s,i,!1,null,"5c5ae317",null),f=m.exports,h=r(72610),g=(r(26227),r(8145)),p={ADD:"ADD",EDIT:"EDIT",SHOW:"SHOW"},y={components:{selectMultiple:f,taSensitiveInput:h.Z},props:{renderType:{type:String,default:p.ADD},renderProp:{type:Object,default:function(){return{}}},showValues:{type:Object,default:function(){return{}}},simpleShowSlot:{type:Array,default:function(){return[]}},formSetting:{required:!0,type:Object,default:function(){return{}}},isShowParentItem:{type:Boolean,default:!0}},data:function(){return{}},methods:{buildItemShowContext:function(e){var t=this,r=this.$slots.default,a=this.formSetting.formItem,o=this.formSetting.formId||"",s=this.showValues[o];switch(e){case"slot":return-1===this.simpleShowSlot.indexOf(o)?r:s;case"select":case"radio":case"radioButton":return this.CollectionLabel(a.collection,s);case"select-multiple":return(0,n.Z)(s)?s.split(",").map((function(e){return t.CollectionLabel(a.collection,e)})).join(","):s;case"sensitive-input":var i=(0,n.Z)(s)?JSON.parse(s):s;return(0,g.Z)(i)?i.sensitiveField:s;default:return s}},buildItemContext:function(e){var t,r=this.$createElement,a=this.$slots.default,o=this.formSetting.disabled,s=this.formSetting.formItem,i=this.formSetting.formId,n=this.formSetting.label,l=this.formSetting.placeholder,c=this.renderType,d=this.renderProp;switch(e){case"slot":return a;case"select":return r("ta-select",{attrs:{placeholder:l,disabled:o,allowClear:!0,"collection-type":s.collection}});case"select-multiple":return r("select-multiple",{attrs:{placeholder:l,collection:s.collection,disabled:o,allowClear:!0}});case"radio":return r("ta-radio-group",{attrs:{disabled:o,"collection-type":s.collection}});case"radioButton":return r("ta-radio-group",{class:"lalal",attrs:{buttonStyle:"solid",disabled:o}},[null===(t=this.CollectionData(s.collection))||void 0===t?void 0:t.map((function(e){var t=e.label,a=e.value;return r("ta-radio-button",{key:a,attrs:{value:a}},[t])}))]);case"sensitive-input":return r("ta-sensitive-input",{attrs:{inputKey:i,placeholder:l,description:n,"auth-user":c===p.EDIT,authRequest:d.authRequest}});default:return r("ta-input",{attrs:{placeholder:l,disabled:o}})}}},render:function(){var e=arguments[0],t=this.renderType,r=this.formSetting.class||"",a=this.formSetting.formId||"",o=this.formSetting.label,s=this.formSetting.decoratorOptions;if(s&&s.rules){var i=s.rules;i.map((function(e){"number"===e.type&&(e.transform=function(e){return Number(e)},e.message="请输入数字")}))}var n=this.formSetting.formItemLayout,l=this.formSetting.formItem,c=!1!==this.formSetting.display;return c||(r+=" displayNone"),"pResourceName"!==this.formSetting.formId||this.isShowParentItem||(r+=" displayNone"),t===p.SHOW?e("ta-form-item",{attrs:{label:o,className:r,labelCol:n.labelCol,wrapperCol:n.wrapperCol}},[this.buildItemShowContext(l.type)]):e("ta-form-item",{attrs:{label:o,labelCol:n.labelCol,wrapperCol:n.wrapperCol,fieldDecoratorId:a,fieldDecoratorOptions:s},class:r},[this.buildItemContext(l.type)])}},_=y,I=(0,u.Z)(_,a,o,!1,null,"7322d522",null),v=I.exports},94461:function(e,t,r){r.r(t),r.d(t,{default:function(){return d}});var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"fit",attrs:{id:"adminUserManagement"}},[r("ta-border-layout",{attrs:{layout:{header:"70px",footer:"70px"},centerCfg:{showBar:!0,toolBarStyle:{height:"50px",lineHeight:"48px"}},"header-cfg":{showBorder:!1},showBorder:!1,"footer-cfg":{showBorder:!1}}},[r("div",{staticClass:"center-box",attrs:{slot:"header"},slot:"header"},[r("ta-input-search",{staticClass:"search-box",attrs:{placeholder:"请输入人员姓名、帐号、证件号"},on:{search:e.onSearchUser},model:{value:e.searchInfo,callback:function(t){e.searchInfo=t},expression:"searchInfo"}},[r("ta-button",{attrs:{slot:"enterButton",type:"primary"},slot:"enterButton"},[e._v("搜索")])],1)],1),r("div",{attrs:{slot:"centerExtraContent"},slot:"centerExtraContent"},[r("ta-cascader",{staticClass:"vertical-space cascader-box",staticStyle:{width:"250px","min-width":"150px","margin-right":"10px"},attrs:{options:e.orgOptions,changeOnSelect:!0,placeholder:"请选择组织机构",expandTrigger:"hover",fieldNames:{label:"orgName",value:"orgId",children:"children"},url:e.orgCascaderUrl,treeId:"orgVos",loadedDataCallBack:e.fnLoadedOrgCallBack},on:{"update:options":function(t){e.orgOptions=t},change:e.fnQueryUserListByOrgId},model:{value:e.casValue,callback:function(t){e.casValue=t},expression:"casValue"}}),r("ta-checkbox",{attrs:{checked:e.isSub},on:{change:e.onChangeIsSub}},[e._v("包含子组织")]),r("ta-tag-select",{attrs:{title:"锁定",data:e.CollectionData("YESORNO")},on:{change:e.filterClick},model:{value:e.selectFilter,callback:function(t){e.selectFilter=t},expression:"selectFilter"}})],1),r("ta-table",{attrs:{columns:e.userColumns,dataSource:e.userList,pagination:!1},scopedSlots:e._u([{key:"name",fn:function(t,a){return[r("span",{class:{invalidStyle:"0"==a.effective}},[e._v(e._s(t))])]}},{key:"sex",fn:function(t){return r("span",{},[e._v(e._s(e.CollectionLabel("SEX",t)))])}},{key:"namePath",fn:function(t){return r("span",{},[e._v(e._s(e.getLastName(t)))])}},{key:"isLock",fn:function(t){return r("span",{},[r("ta-tag",{staticClass:"no-cursor",attrs:{color:"0"==t?"green":"red"}},[e._v(" "+e._s(e.CollectionLabel("YESORNO",t))+" ")])],1)}},{key:"operation",fn:function(t,a){return r("span",{},[r("ta-table-operate",{attrs:{operateMenu:e.operateMenu}})],1)}}])}),r("div",{attrs:{slot:"footer"},slot:"footer"},[r("ta-pagination",{ref:"userGridPager",staticStyle:{float:"right","margin-top":"10px"},attrs:{showSizeChanger:"",showQuickJumper:"",dataSource:e.userList,defaultPageSize:10,params:e.userPageParams,url:e.adminUserUrl},on:{"update:dataSource":function(t){e.userList=t},"update:data-source":function(t){e.userList=t}}})],1)],1)],1)},o=[],s=[{title:"姓名",dataIndex:"name",width:"15%",scopedSlots:{customRender:"name"}},{title:"账号",dataIndex:"loginId",width:"15%"},{title:"性别",dataIndex:"sex",width:80,scopedSlots:{customRender:"sex"}},{title:"所属组织",dataIndex:"namePath",width:"15%",overflowTooltip:!0,scopedSlots:{customRender:"namePath"}},{title:"手机号",dataIndex:"mobile",width:"15%",overflowTooltip:!0},{title:"锁定",dataIndex:"isLock",width:80,scopedSlots:{customRender:"isLock"}},{title:"操作",dataIndex:"operation",width:240,scopedSlots:{customRender:"operation"},align:"center"}],i={name:"adminUserManagement",data:function(){var e=this;return{orgCascaderUrl:"org/authority/adminAuthorityManagementRestService/queryCurrentAdminRoleWrapOrgTree",adminUserUrl:"org/authority/examinerAuthorityRestService/queryUserByCondition",casValue:[],orgOptions:[],isSub:!0,searchInfo:"",userColumns:s,operateMenu:[{name:"角色管理",onClick:function(t){e.fnRouteToRoleMg(t)}}],userList:[],arrayData:{},isDetailShow:!1,selectFilter:[]}},methods:{filterClick:function(){this.fnQueryUserByOrgId()},showRecordDetail:function(e){this.isDetailShow=!0,this.arrayData=[{label:"姓名",value:e.name},{label:"账号",value:e.loginId},{label:"性别",type:"codeTable",dictType:"SEX",value:e.sex},{label:"组织路径",type:"desc",value:e.namePath}]},getLastName:function(e){return e&&-1!=e.indexOf("/")?e.slice(e.lastIndexOf("/")+1):e},fnRouteToRoleMg:function(e){this.$router.push({path:"userRoleMg",query:{name:e.name,userId:e.userId}})},userPageParams:function(){var e={},t=this.orgOptions[0]&&this.orgOptions[0].orgId?this.orgOptions[0].orgId:"";return e.orgId=this.casValue.length>0?this.casValue[this.casValue.length-1]:t,e.showChildUser=this.isSub?"1":"0",this.searchInfo&&(e.name=this.searchInfo,e.loginId=this.searchInfo,e.idCardNo=this.searchInfo,e.searchType="normal"),this.selectFilter&&(e.islock=this.selectFilter.join(",")),e},fnQueryUserListByOrgId:function(e){this.casValue=e,this.fnQueryUserByOrgId()},onChangeIsSub:function(e){var t=this;this.isSub=e.target.checked,this.$nextTick((function(){t.fnQueryUserByOrgId()}))},fnLoadedOrgCallBack:function(e){this.fnQueryUserByOrgId()},fnQueryUserByOrgId:function(){this.$refs.userGridPager.loadData()},onSearchUser:function(){this.fnQueryUserByOrgId()}}},n=i,l=r(1001),c=(0,l.Z)(n,a,o,!1,null,"73d82065",null),d=c.exports},37671:function(e,t,r){r.r(t),r.d(t,{default:function(){return ee}});var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"fit",attrs:{id:"userManagement"}},[r("ta-border-layout",{staticStyle:{padding:"10px"},attrs:{"layout-type":"flexBorder"}},[r("div",{staticClass:"center-box",attrs:{slot:"header"},slot:"header"},[r("div",{staticClass:"center-item"},[r("ta-input-search",{staticClass:"search-box",attrs:{placeholder:"请输入人员姓名、帐号、证件号","enter-button":"搜索"},on:{search:e.onSearchUser},model:{value:e.searchInfo,callback:function(t){e.searchInfo=t},expression:"searchInfo"}})],1),r("div",{staticClass:"center-item"},[r("userSearchPanel",{on:{dealSearchPanelResult:e.dealSearchPanelResult}})],1)]),r("ta-border-layout",{attrs:{"layout-type":"flexBorder"}},[r("div",{staticStyle:{"line-height":"45px"},attrs:{slot:"header"},slot:"header"},[r("ta-tree-select",{staticStyle:{width:"250px","margin-right":"10px"},attrs:{"dropdown-style":{maxHeight:"300px",overflow:"auto"},placeholder:"请选择人员的所属组织",url:"org/orguser/orgManagementRestService/getOrgByAsync","tree-id":"orgTreeData","tree-node-id":"orgId","tree-data":e.orgUserTreeData,"tree-node-label-prop":"namePath","tree-data-label":"orgName","tree-data-value":"orgId","allow-clear":""},on:{"update:treeData":function(t){e.orgUserTreeData=t},"update:tree-data":function(t){e.orgUserTreeData=t},change:e.onSearchUser},model:{value:e.orgIds,callback:function(t){e.orgIds=t},expression:"orgIds"}}),r("ta-checkbox",{attrs:{checked:e.checkedOrgChild},on:{change:e.onChangeChildOrg}},[e._v(" 包含子组织 ")]),r("ta-checkbox",{attrs:{checked:e.checkedNoneffectiveOrg},on:{change:e.onChangeNoneffectiveOrg}},[e._v(" 无有效组织人员 ")]),r("ta-checkbox",{attrs:{checked:e.checkedVirtualOrg},on:{change:e.onChangeVirtualOrg}},[e._v(" 无组织人员 ")]),r("ta-tag-select",{staticClass:"filter-name",attrs:{title:"锁定",data:e.CollectionData("YESORNO")},on:{change:e.onSearchUser},model:{value:e.islockList,callback:function(t){e.islockList=t},expression:"islockList"}}),r("ta-tag-select",{staticClass:"filter-name",attrs:{title:"有效性",data:e.CollectionData("EFFECTIVE")},on:{change:e.onSearchUser},model:{value:e.userEffectiveList,callback:function(t){e.userEffectiveList=t},expression:"userEffectiveList"}}),r("div",{staticStyle:{float:"right"}},[r("ta-button",{staticClass:"step1",attrs:{type:"primary"},on:{click:function(t){e.editVisible=!0,e.editType="1",e.rowData={}}}},[e._v(" 新增 ")]),r("ta-dropdown",{attrs:{trigger:["click"]}},[r("ta-menu",{attrs:{slot:"overlay"},slot:"overlay"},[r("ta-menu-item",{attrs:{disabled:e.btnDisable}},[r("ta-popconfirm",{attrs:{title:"确认启用所选账户?","cancel-text":"取消","ok-text":"确认"},on:{confirm:function(t){return e.changeEffectiveUnlock(1)}}},[r("ta-icon",{attrs:{type:"check-circle"}}),r("span",{staticClass:"mg-l12"},[e._v("启用")])],1)],1),r("ta-menu-divider"),r("ta-menu-item",{attrs:{disabled:e.btnDisable}},[r("ta-popconfirm",{attrs:{title:"确认禁用所选账户?","cancel-text":"取消","ok-text":"确认"},on:{confirm:function(t){return e.changeEffectiveUnlock(2)}}},[r("ta-icon",{attrs:{type:"stop"}}),r("span",{staticClass:"mg-l12"},[e._v("禁用")])],1)],1),r("ta-menu-divider"),r("ta-menu-item",{attrs:{disabled:e.btnDisable},on:{click:function(t){return e.changeEffectiveUnlock(3)}}},[r("ta-icon",{attrs:{type:"unlock"}}),e._v(" 解锁 ")],1),r("ta-menu-divider"),r("ta-menu-item",{attrs:{disabled:e.btnDisable},on:{click:e.fnDeleteUser}},[r("ta-icon",{attrs:{type:"close-circle"}}),e._v(" 删除 ")],1)],1),r("ta-button",[e._v(" 批量操作 "),r("ta-icon",{attrs:{type:"down"}})],1)],1)],1)],1),r("ta-table",{attrs:{"row-key":"userId",columns:e.columns,"data-source":e.userList,pagination:!1,locale:{filterConfirm:"确定",filterReset:"重置",emptyText:"暂无数据"},"row-selection":{selectedRowKeys:e.selectedRowKeys,onChange:e.handleSelectChange}},scopedSlots:e._u([{key:"name",fn:function(t,a){return r("a",{staticClass:"operate",class:{"disable-color":"0"==a.effective},on:{click:function(t){e.showVisible=!0,e.rowData=a}}},[e._v(e._s(t))])}},{key:"sex",fn:function(t){return r("span",{},[e._v(e._s(e.CollectionLabel("SEX",t)))])}},{key:"namePath",fn:function(t,a){return r("span",{},[e.checkedNoneffectiveOrg?r("span",{staticStyle:{"text-decoration":"line-through red 3px",color:"gray"}},["0"==e.config?r("span",[e._v(e._s(e.getLastName(t)))]):r("a",[r("ta-popover",{attrs:{placement:"right",width:"400",trigger:"click"}},[r("ta-table",{attrs:{columns:e.gridColumns,"data-source":e.orgInfo,bordered:"",size:"small",pagination:!1}}),r("ta-icon",{attrs:{slot:"reference",type:"search"},on:{click:function(t){return e.queryOrgInfo(a.userId)}},slot:"reference"})],1)],1)]):r("span",["0"==e.config?r("span",[e._v(e._s(e.getLastName(t)))]):r("a",[r("ta-popover",{attrs:{placement:"right",width:"400",trigger:"click"}},[r("ta-table",{attrs:{columns:e.gridColumns,"data-source":e.orgInfo,bordered:"",size:"small",pagination:!1}}),r("ta-icon",{attrs:{slot:"reference",type:"search"},on:{click:function(t){return e.queryOrgInfo(a.userId)}},slot:"reference"})],1)],1)])])}},{key:"isLock",fn:function(t){return r("span",{},["1"==t?r("ta-tag",{staticClass:"no-cursor",attrs:{type:"danger"}},[e._v("是")]):r("ta-tag",{staticClass:"no-cursor",attrs:{type:"success"}},[e._v("否")])],1)}},{key:"mobile",fn:function(t){return r("span",{},[e._v(e._s(e.formatSensitiveValue("mobile",t)))])}},{key:"effective",fn:function(t){return r("span",{},[e._v(e._s(e.CollectionLabel("EFFECTIVE",t)))])}},{key:"operation",fn:function(t,a,o){return r("span",{},[r("ta-table-operate",{attrs:{"operate-menu":e.operateMenu}})],1)}}])},[r("span",{staticClass:"step2",attrs:{slot:"operationTitle"},slot:"operationTitle"},[e._v("操作")])])],1),r("div",{attrs:{slot:"footer"},slot:"footer"},[r("ta-pagination",{ref:"gridPager",staticStyle:{float:"right","margin-top":"10px"},attrs:{"show-size-changer":"","show-quick-jumper":"","data-source":e.userList,"default-page-size":10,params:e.userPageParams,url:e.userSearchUrl},on:{"update:dataSource":function(t){e.userList=t},"update:data-source":function(t){e.userList=t}}})],1)],1),e.editVisible?r("edit-user",{attrs:{visible:e.editVisible,"edit-type":e.editType,"row-data":e.rowData},on:{close:function(t){e.editVisible=!1},editSuccess:e.editSuccess,queryTable:e.onSearchUser}}):e._e(),e.showVisible?r("show-user",{attrs:{visible:e.showVisible,"row-data":e.rowData},on:{close:function(t){e.showVisible=!1}}}):e._e(),r("reset-pwd",{attrs:{visible:e.resetVisible,"user-id":e.rowData.userId},on:{close:function(t){e.resetVisible=!1},queryTable:e.onSearchUser}}),r("ta-careful-delete",{attrs:{visible:e.deleteVisible,title:"人员删除",description:"选中人员"},on:{close:function(t){e.deleteVisible=!1},delete:e.deleteBatch}}),r("change-org",{attrs:{visible:e.changeVisible,config:e.config,"row-data":e.rowData},on:{close:function(t){e.changeVisible=!1},queryTable:e.onSearchUser}})],1)},o=[],s=function(){var e=this,t=this,r=t.$createElement,a=t._self._c||r;return a("ta-drawer",{attrs:{title:"个人信息",placement:"right",closable:!0,visible:t.visible,"destroy-on-close":"",width:"500px","get-container":t.setContainer},on:{close:t.closeDrawer}},[a("ta-form",{attrs:{"auto-form-create":function(t){e.form=t}}},[t._l(t.formNormalShowSettings,(function(e){return[a("renderFormItem",{key:e.id,attrs:{"form-setting":e,"is-show":!0,"show-values":t.formData,"simple-show-slot":t.simpleShowSlot,"render-type":"SHOW"}},["avatar"==e.id?a("div",{staticClass:"pos-avatar"},[t.imageUrl&&t.imageUrl.length?a("div",{staticClass:"img-avatar",style:{backgroundImage:"url("+t.imageUrl+")"}}):a("ta-icon",{attrs:{type:"user"}})],1):"orgIdShow"==e.id?[t._v(" "+t._s(t.formData.namePath)+" ")]:"tags"==e.id?t._l(t.tags,(function(e){return[t.selectedTags.indexOf(e.tagId)>-1?a("ta-tag",{key:e.tagId,staticClass:"tag-select"},[t._v(" "+t._s(e.tagName)+" ")]):t._e()]})):t._e()],2)]})),t.formMoreShowSettings.length>0?a("ta-collapse",{attrs:{bordered:!1}},[a("ta-collapse-panel",{key:"1",staticStyle:{border:"none"},attrs:{header:"更多个人信息"}},[t._l(t.formMoreShowSettings,(function(e){return[a("renderFormItem",{key:e.id,attrs:{"form-setting":e,"is-show":!0,"show-values":t.formData,"simple-show-slot":t.simpleShowSlot,"render-type":"SHOW"}},["avatar"==e.id?a("div",{staticClass:"pos-avatar"},[t.imageUrl&&t.imageUrl.length?a("div",{staticClass:"img-avatar",style:{backgroundImage:"url("+t.imageUrl+")"}}):a("ta-icon",{attrs:{type:"user"}})],1):"orgIdShow"==e.id?[t._v(" "+t._s(t.formData.namePath)+" ")]:"tags"==e.id?t._l(t.tags,(function(e){return[t.selectedTags.indexOf(e.tagId)>-1?a("ta-tag",{key:e.tagId,staticClass:"tag-select"},[t._v(" "+t._s(e.tagName)+" ")]):t._e()]})):t._e()],2)]}))],2)],1):t._e()],2)],1)},i=[],n=r(63458),l=r(80715),c=r(75145),d=["password","password_2"],u=["loginId"],m={name:"rowData",components:{renderFormItem:l.Z},mixins:[c.Z],props:["visible","rowData"],data:function(){return{formData:{},simpleShowSlot:u,imageUrl:"",tags:[],selectedTags:[]}},computed:{formNormalShowSettings:function(){return this.formNormalSettings.filter((function(e){return-1===d.indexOf(e.id)}))||[]},formMoreShowSettings:function(){return this.formMoreSettings.filter((function(e){return-1===d.indexOf(e.id)}))||[]}},mounted:function(){this.formData=this.rowData,this.buildForm(this.formData),this.queryPortrait(),this.queryTag()},methods:{setContainer:function(){return document.getElementById("userManagement")},getFormNecessarySettings:function(e){return[{id:"avatar",formId:"",formItem:{type:"slot",collection:null},label:"用户头像",class:"avatar-form-item",display:!0,exist:!0,formItemLayout:this.formItemLayouts.default},{id:"orgIdShow",formId:"",formItem:{type:"slot",collection:null},label:"所属组织",decoratorOptions:{},display:!0,exist:!0,formItemLayout:this.formItemLayouts.default},{id:"name",formId:"name",formItem:{type:"input",collection:null},label:"姓名",decoratorOptions:{},display:!0,exist:!0,formItemLayout:this.formItemLayouts.default},{id:"loginId",formId:"loginId",formItem:{type:"slot",collection:null},label:"账号",decoratorOptions:{},display:!0,exist:!0,formItemLayout:this.formItemLayouts.default}]},closeDrawer:function(){this.$emit("close")},queryPortrait:function(){var e=this;n.Z.queryAvatar({userId:this.rowData.userId},(function(t){e.imageUrl=t.data.portrait||""}))},queryTag:function(){var e=this;n.Z.queryTagByUserId({userId:this.rowData.userId},(function(t){e.tags=t.data.tagList,e.selectedTags=[];for(var r=0;r<e.tags.length;r++)"1"===e.tags[r].isChecked&&e.selectedTags.push(e.tags[r].tagId)}))}}},f=m,h=r(1001),g=(0,h.Z)(f,s,i,!1,null,"06c39c5b",null),p=g.exports,y=function(){var e=this,t=this,r=t.$createElement,a=t._self._c||r;return a("ta-drawer",{attrs:{title:"1"===t.editType?"添加人员信息":"编辑人员信息",placement:"right",closable:!0,visible:t.visible,"destroy-on-close":"",width:"500px",getContainer:t.setContainer},on:{close:t.closeEdit}},[a("ta-form",{attrs:{id:"userForm","auto-form-create":function(t){e.form=t}}},[t.form?[t._l(t.formNormalSettings,(function(e){return[a("renderFormItem",{key:e.id,attrs:{"form-setting":e,"render-type":t.renderType,"render-prop":t.renderProp}},["avatar"===e.id?a("div",{staticClass:"pos-avatar"},[t.imageUrl?a("div",{staticClass:"img-avatar",style:{backgroundImage:"url("+t.imageUrl+")"}}):t._e(),t.imageUrl?a("ta-icon",{staticClass:"clear-image",attrs:{type:"close",title:"清空图片"},on:{click:t.clearImage}}):a("ta-icon",{staticClass:"icon-upload",attrs:{type:t.loading?"loading":"user"}}),a("input",{staticClass:"hide-input",attrs:{id:"uploadPhotoId",type:"file",accept:"image/gif,image/jpeg,image/jpg,image/png"},on:{change:function(e){return t.uploadPhoto(e)}}})],1):"orgId"===e.id?a("project-search-tree",{attrs:{"get-container-id":"userManagement"}}):"orgIdShow"===e.id?[t._v(" "+t._s(t.formData.namePath)+" ")]:"password"===e.id?a("ta-input",{attrs:{type:"password"},on:{blur:t.handleConfirmBlurs}}):"password_2"===e.id?a("ta-input",{attrs:{type:"password"}}):"tags"===e.id?t._l(t.tags,(function(e){return[a("ta-tag",{key:e.tagId,staticClass:"tag-select",class:{"tag-active":t.selectedTags.indexOf(e.tagId)>-1},on:{click:function(r){return t.tagClick(e.tagId)}}},[t._v(" "+t._s(e.tagName)),t.selectedTags.indexOf(e.tagId)>-1?a("ta-icon",{staticStyle:{"margin-left":"5px"},attrs:{type:"check"}}):t._e()],1)]})):t._e()],2)]})),t.formMoreSettings.length>0?a("ta-collapse",{attrs:{bordered:!1}},[a("ta-collapse-panel",{key:"1",staticStyle:{border:"none",padding:"0px"},attrs:{header:"更多个人信息","force-render":!0}},[t._l(t.formMoreSettings,(function(e){return[a("renderFormItem",{key:e.id,attrs:{"form-setting":e,"render-type":t.renderType,"render-prop":t.renderProp}},["avatar"===e.id?a("div",{staticClass:"pos-avatar"},[t.imageUrl?a("div",{staticClass:"img-avatar",style:{backgroundImage:"url("+t.imageUrl+")"}}):t._e(),t.imageUrl?a("ta-icon",{staticClass:"clear-image",attrs:{type:"close",title:"清空图片"},on:{click:t.clearImage}}):a("ta-icon",{staticClass:"icon-upload",attrs:{type:t.loading?"loading":"user"}}),a("input",{staticClass:"hide-input",attrs:{id:"uploadPhotoId",type:"file",accept:"image/gif,image/jpeg,image/jpg,image/png"},on:{change:function(e){return t.uploadPhoto(e)}}})],1):"orgId"===e.id?a("project-search-tree"):"orgIdShow"===e.id?[t._v(" "+t._s(t.formData.namePath)+" ")]:"password"===e.id?a("ta-input",{attrs:{type:"password"},on:{blur:t.handleConfirmBlurs}}):"password_2"===e.id?a("ta-input",{attrs:{type:"password"}}):"tags"===e.id?t._l(t.tags,(function(e){return[a("ta-tag",{key:e.tagId,staticClass:"tag-select",class:{"tag-active":t.selectedTags.indexOf(e.tagId)>-1},on:{click:function(r){return t.tagClick(e.tagId)}}},[t._v(" "+t._s(e.tagName)),t.selectedTags.indexOf(e.tagId)>-1?a("ta-icon",{staticStyle:{"margin-left":"5px"},attrs:{type:"check"}}):t._e()],1)]})):t._e()],2)]}))],2)],1):t._e()]:t._e()],2),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("ta-button-group",[a("ta-button",{on:{click:function(e){return t.onResetForm()}}},[t._v(" 重置 ")]),a("ta-button",{attrs:{type:"primary"},on:{click:function(e){return t.onSubmitForm()}}},[t._v(" 保存 ")])],1)],1)],1)},_=[],I=r(89584),v=r(95082),b=r(63822),w=r(57456),C=r(7029);function S(e,t){var r=new FileReader;r.addEventListener("load",(function(){return t(r.result)})),r.readAsDataURL(e)}var x={name:"EditUser",components:{renderFormItem:l.Z},mixins:[c.Z],props:["visible","editType","rowData"],data:function(){return{form:null,formData:{},tags:[],initTags:[],selectedTags:[],pwdRules:{rules:[{required:!0,message:"请输入登录口令"},{min:6,message:"登录口令不能少于6位字符"},{validator:this.validatePwd}]},confirmDirty:!1,loading:!1,imageUrl:"",initImg:"",options:[],rsaKey:{},initForm:{}}},mounted:function(){this.setValue()},created:function(){this.$store.dispatch("getSysCfg")},computed:(0,v.Z)((0,v.Z)({},(0,b.Se)({passwordRSAState:"passwordRSAState",passwordLevel:"passwordLevel"})),{},{renderType:function(){return"2"===this.editType?"EDIT":"ADD"},renderProp:function(){var e=this;return{authRequest:function(t,r){var a=e.rowData.userId;n.Z.authRequestForUserInfo((0,v.Z)((0,v.Z)({},t),{},{userId:a}),(function(e){r(e.userInfo[t.inputKey])}))}}}}),methods:{setContainer:function(){return document.getElementById("userManagement")},setValue:function(){var e=this,t=this.rowData,r=t.userId,a=t.jobNumber,o=t.mobile,s=t.email,i=t.address,n=t.zipCode,l=t.workplace;"2"===this.editType?(Object.keys(this.rowData).forEach((function(t){e.formData[t]=e.rowData[t]||""})),this.queryPortrait({userId:r})):this.formData.sex="0",this.initForm=Object.assign({},this.formData,{jobNumber:a||"",mobile:o||"",email:s||"",address:i||"",zipCode:n||"",workplace:l||""}),this.getUserTags({userId:r})},getUserTags:function(e){var t=this;n.Z.queryTagByUserId(e,(function(e){t.tags=e.data.tagList,t.initTags=[],t.selectedTags=[];for(var r=0;r<t.tags.length;r++)"1"===t.tags[r].isChecked&&t.initTags.push(t.tags[r].tagId);t.initTags.length&&(t.selectedTags=t.initTags),t.formData.tags=t.selectedTags,t.buildForm(t.formData)}))},queryPortrait:function(e){var t=this;n.Z.queryAvatar(e,(function(e){t.imageUrl=e.data.portrait||"",t.initImg=t.imageUrl}))},validatePwd:function(e,t,r){t&&this.confirmDirty&&this.form.validateFields(["password_2"],{force:!0});var a=(0,w.Z)(t);1===this.passwordLevel?a!==this.passwordLevel&&r("请输入6位数字"):a<this.passwordLevel?r("请至少包含大写字母、小写字母、数字、特殊字符(除去空格)中的"+this.passwordLevel+"种，且长度为8~20位"):r()},compareToFirstPwd:function(e,t,r){t&&t!==this.form.getFieldValue("password")?r("两次口令输入不一致"):r()},handleConfirmBlurs:function(e){var t=e.target.value;this.confirmDirty=this.confirmDirty||!!t},tagClick:function(e){var t=this.selectedTags,r=t.some((function(t){return t===e}));this.selectedTags=r?t.filter((function(t){return t!==e})):[].concat((0,I.Z)(t),[e]),this.form.setFieldsValue({tags:this.selectedTags})},closeEdit:function(){this.$emit("close"),this.form.resetFields(),this.formData={},this.imageUrl="",this.initImg=""},onResetForm:function(){this.form.resetFields(),this.imageUrl="",this.selectedTags=this.initTags,"2"===this.editType&&this.queryPortrait({userId:this.formData.userId}),document.getElementById("uploadPhotoId").value=""},uploadPhoto:function(e){var t=this,r=e.target.files[0],a=document.getElementById("uploadPhotoId");if(r){if(this.loading=!0,r.size>51200)return this.$message.error("用户头像大小不能超过50kb"),this.loading=!1,a.value="",!1;S(r,(function(e){t.imageUrl=e||"",t.loading=!1}))}},clearImage:function(){this.imageUrl="",document.getElementById("uploadPhotoId").value=""},onSubmitForm:function(){var e=this;this.form.validateFieldsAndScroll((function(t,r){if(!t)if(e.selectedTags.length&&(r.tags=e.selectedTags.join(",")),r.portrait=e.imageUrl||"",void 0===r.idCardType&&(r.idCardType=""),delete r.password_2,r.password=(0,C.xY)(r.password),"2"===e.editType){r.userId=e.formData.userId,r.orgId=e.formData.orgId,r.jobNumber=r.jobNumber||"",r.mobile=r.mobile||"",r.email=r.email||"",r.address=r.address||"",r.zipCode=r.zipCode||"",r.workplace=r.workplace||"",r.selectedTags=r.selectedTags||[];var a=(0,v.Z)({},r),o=Object.keys(a),s=e.initForm,i=!1;if(e.initImg===a.portrait){for(var l=0;l<o.length;l++)if("password"!==o[l]&&"portrait"!==o[l]&&s[o[l]]!==a[o[l]]){i=!0;break}}else i=!0;i?n.Z.updateUserByUserId(r,(function(t){e.$message.success("更新人员信息成功"),e.$emit("editSuccess",r),e.closeEdit()})):e.$message.warning("没有需要保存的修改")}else n.Z.addUser(r,(function(t){t.data.addUser&&e.showConfirm()}))}))},showConfirm:function(){var e=this,t=this.$createElement,r=this.$confirm({title:"保存人员信息成功",content:"保存成功。是否继续新增人员?",footer:t("span",[t("ta-button",{on:{click:function(){return e.handleCancel(r)}}},["取消"]),t("ta-button",{attrs:{type:"primary"},on:{click:function(){return e.handleOk(r)}}},["确定"]),t("ta-button",{attrs:{type:"primary"},on:{click:function(){return e.handleOk2(r)}}},["同组织新增"])])})},handleCancel:function(e){this.closeEdit(),this.$emit("queryTable"),e.destroy()},handleOk:function(e){this.form.resetFields(),this.selectedTags=[],this.imageUrl="",document.getElementById("uploadPhotoId").value="",this.$emit("queryTable"),e.destroy()},handleOk2:function(e){var t=this.form.getFieldValue("orgId");this.form.resetFields(),this.selectedTags=[],this.imageUrl="",document.getElementById("uploadPhotoId").value="",this.$emit("queryTable"),this.form.setFieldsValue({orgId:t}),e.destroy()}}},k=x,O=(0,h.Z)(k,y,_,!1,null,"77681000",null),T=O.exports,U=function(){var e=this,t=this,r=t.$createElement,a=t._self._c||r;return a("ta-search-panel",{attrs:{id:"demo",form:t.searchForm,width:800,"custom-show-value":t.customShowValue},on:{search:t.onSearch}},[a("ta-button",{attrs:{slot:"target"},slot:"target"},[t._v(" 高级搜索 ")]),a("div",{attrs:{slot:"formPanel"},slot:"formPanel"},[a("ta-form",{attrs:{"auto-form-create":function(t){e.searchForm=t},"form-layout":!0,col:2}},[t.searchForm?[a("ta-form-item",{attrs:{label:"所属组织","label-col":t.formItemLayout.labelCol,"wrapper-col":t.formItemLayout.wrapperCol,"field-decorator-id":"userOrgIds"}},[a("project-search-tree")],1),a("ta-form-item",{attrs:{label:"包含子组织",colon:!1,"label-col":t.formItemLayout.labelCol,"wrapper-col":t.formItemLayout.wrapperCol,"field-decorator-id":"showChildUser","field-decorator-options":{initialValue:!0,valuePropName:"checked"}}},[a("ta-checkbox")],1),a("ta-form-item",{attrs:{label:"姓名","label-col":t.formItemLayout.labelCol,"wrapper-col":t.formItemLayout.wrapperCol,"field-decorator-id":"name1"}},[a("ta-user-input",{attrs:{"select-title":"选择姓名","org-load-fn":t.handleLoadOrgNode,"user-load-fn":t.handleQueryUserList,"user-select-call":function(e){t.selectName=e.name}}})],1),a("ta-form-item",{attrs:{label:"账号","label-col":t.formItemLayout.labelCol,"wrapper-col":t.formItemLayout.wrapperCol,"field-decorator-id":"loginId1"}},[a("ta-input")],1),a("ta-form-item",{attrs:{label:"性别","label-col":t.formItemLayout.labelCol,"wrapper-col":t.formItemLayout.wrapperCol,"field-decorator-id":"sex"}},[a("ta-radio-group",t._l(t.CollectionData("SEX"),(function(e){return a("ta-radio",{key:e.value,attrs:{value:e.value}},[t._v(" "+t._s(e.label)+" ")])})),1),a("a",{on:{click:function(e){return t.searchForm.resetFields("sex")}}},[t._v("清除选项")])],1),a("ta-form-item",{attrs:{label:"工号","label-col":t.formItemLayout.labelCol,"wrapper-col":t.formItemLayout.wrapperCol,"field-decorator-id":"jobnumber"}},[a("ta-input")],1),a("ta-form-item",{attrs:{label:"证件类型","label-col":t.formItemLayout.labelCol,"wrapper-col":t.formItemLayout.wrapperCol,"field-decorator-id":"idCardType"}},[a("ta-select",{attrs:{mode:"multiple"}},t._l(t.CollectionData("IDCARDTYPE"),(function(e){return a("ta-select-option",{key:e.value,attrs:{value:e.value}},[t._v(" "+t._s(e.label)+" ")])})),1)],1),a("ta-form-item",{attrs:{label:"证件号","label-col":t.formItemLayout.labelCol,"wrapper-col":t.formItemLayout.wrapperCol,"field-decorator-id":"idCardNo1"}},[a("ta-input")],1),a("ta-form-item",{attrs:{label:"手机号","label-col":t.formItemLayout.labelCol,"wrapper-col":t.formItemLayout.wrapperCol,"field-decorator-id":"mobile1"}},[a("ta-input")],1),a("ta-form-item",{attrs:{label:"学历","label-col":t.formItemLayout.labelCol,"wrapper-col":t.formItemLayout.wrapperCol,"field-decorator-id":"education"}},[a("ta-select",t._l(t.CollectionData("EDUCATION"),(function(e){return a("ta-select-option",{key:e.value,attrs:{value:e.value}},[t._v(" "+t._s(e.label)+" ")])})),1)],1),a("ta-form-item",{attrs:{label:"邮箱地址","label-col":t.formItemLayout.labelCol,"wrapper-col":t.formItemLayout.wrapperCol,"field-decorator-id":"email","field-decorator-options":{rules:[{type:"email",message:"请输入正确的邮箱地址"}]}}},[a("ta-input")],1),a("ta-form-item",{attrs:{label:"邮政编码","label-col":t.formItemLayout.labelCol,"wrapper-col":t.formItemLayout.wrapperCol,"field-decorator-id":"zipCode","field-decorator-options":{rules:[{pattern:/^[0-9]{6}$/,message:"请输入正确的邮政编码"}]}}},[a("ta-input")],1),a("ta-form-item",{attrs:{label:"工作单位","label-col":t.formItemLayout.labelCol,"wrapper-col":t.formItemLayout.wrapperCol,"field-decorator-id":"workplace"}},[a("ta-input")],1),a("ta-form-item",{attrs:{label:"创建时间","label-col":t.formItemLayout.labelCol,"wrapper-col":t.formItemLayout.wrapperCol,"field-decorator-id":"createtime"}},[a("ta-range-picker",{staticStyle:{width:"100%"}})],1),a("ta-form-item",{attrs:{label:"是否锁定","label-col":t.formItemLayout.labelCol,"wrapper-col":t.formItemLayout.wrapperCol,"field-decorator-id":"islock"}},[a("ta-select",{attrs:{mode:"multiple"}},[a("ta-select-option",{attrs:{value:"0"}},[t._v(" 未锁定 ")]),a("ta-select-option",{attrs:{value:"1"}},[t._v(" 已锁定 ")])],1)],1),a("ta-form-item",{attrs:{label:"是否有效","label-col":t.formItemLayout.labelCol,"wrapper-col":t.formItemLayout.wrapperCol,"field-decorator-id":"userEffective"}},[a("ta-select",{staticStyle:{width:"100%"},attrs:{mode:"multiple"}},t._l(t.CollectionData("EFFECTIVE"),(function(e){return a("ta-select-option",{key:e.value,attrs:{value:e.value}},[t._v(" "+t._s(e.label)+" ")])})),1)],1)]:t._e()],2)],1)],1)},D=[],P=r(79754),L={name:"search-panel",props:{result:{type:Object,default:function(){return{}}}},data:function(){return{searchForm:null,formItemLayout:{labelCol:{span:6},wrapperCol:{span:18}},userOrgIds:"",options:[],selectName:""}},methods:{onSearch:function(e){e.createtime&&e.createtime.length&&(e.createtimeMin=(0,P.Z)(e.createtime[0],"YYYY-MM-DD")+" 00:00:00",e.createtimeMax=(0,P.Z)(e.createtime[1],"YYYY-MM-DD")+" 23:59:59",delete e.createtime),e.mobile1&&(e.mobile=e.mobile1,delete e.mobile1),e.name1&&(e.name=this.selectName,delete e.name1),e.idCardNo1&&(e.idCardNo=e.idCardNo1,delete e.idCardNo1),e.loginId1&&(e.loginId=e.loginId1,delete e.loginId1),e.userOrgIds&&(e.orgId=e.userOrgIds),this.$emit("dealSearchPanelResult",e)},handleLoadOrgNode:function(e,t){var r=e.data&&e.data.orgId||"";n.Z.loadOrgTree(r,(function(e){t(e.orgTreeData)}))},handleQueryUserList:function(e,t){var r=e.orgId,a=e.userId,o=e.includeChild,s=e.pageSize,i=e.pageNum,l=e.searchVal,c=e.searchType,d=e.searchParam;n.Z.queryUserList({orgId:r,userId:a,includeChild:o,pageSize:s,pageNum:i,searchVal:l,searchType:c,searchParam:d},(function(e){t(e.userList.list)}))},customShowValue:function(e){if(-1!==e.className.indexOf("project-search-tree"))return e.childNodes[0].childNodes[0].childNodes[0].value}}},E=L,B=(0,h.Z)(E,U,D,!1,null,null,null),R=B.exports,M=function(){var e=this,t=this,r=t.$createElement,a=t._self._c||r;return a("ta-modal",{attrs:{getContainer:t.setContainer,title:"重置密码",visible:t.visible,okText:"确认",cancelText:"关闭",width:"400px",bodyStyle:{height:"160px"},size:"small",destroyOnClose:""},on:{ok:t.resetPwd,cancel:t.closeModal}},[a("ta-form",{attrs:{id:"pwdForm",autoFormCreate:function(t){e.formPass=t}}},[a("ta-form-item",{attrs:{label:"登录口令",labelCol:{span:7},wrapperCol:{span:17},fieldDecoratorId:"password",fieldDecoratorOptions:t.pwdRules}},[a("ta-input",{attrs:{type:"password"},on:{blur:t.blurPwd}})],1),a("ta-form-item",{attrs:{label:"确认口令",labelCol:{span:7},wrapperCol:{span:17},fieldDecoratorId:"password_2",fieldDecoratorOptions:{rules:[{validator:this.compareToFirstPwd},{required:!0,message:"请再次输入登录口令!"}]}}},[a("ta-input",{attrs:{type:"password"}})],1)],1)],1)},N=[],A={name:"resetPwd",props:["visible","userId"],data:function(){return{formPass:null,pwdRules:{rules:[{required:!0,message:"请输入登录口令!"},{validator:this.validatePwd}]}}},computed:(0,v.Z)({},(0,b.Se)({passwordRSAState:"passwordRSAState",passwordLevel:"passwordLevel"})),created:function(){this.$store.dispatch("getSysCfg")},methods:{setContainer:function(){return document.getElementById("userManagement")},blurPwd:function(e){var t=e.target.value;this.confirmDirty=this.confirmDirty||!!t},compareToFirstPwd:function(e,t,r){var a=this.formPass;t&&t!==a.getFieldValue("password")?r("两次口令输入不一致!"):r()},validatePwd:function(e,t,r){var a=this.formPass;t&&this.confirmDirty&&a.validateFields(["password_2"],{force:!0});var o=(0,w.Z)(t);1==this.passwordLevel?o!=this.passwordLevel&&r("请输入6位数字"):o<this.passwordLevel?r("请至少包含大写字母、小写字母、数字、特殊字符(除去空格)中的"+this.passwordLevel+"种，且长度为8~20位"):r()},resetPwd:function(){var e=this;this.formPass.validateFields((function(t,r){if(!t){var a=(0,C.xY)(r.password);n.Z.updateUserPwdByUserId({password:a,userId:e.userId},(function(t){e.$emit("queryTable"),e.closeModal(),e.$message.success("重置密码成功")}))}}))},closeModal:function(){this.formPass.resetFields(),this.$emit("close")}}},F=A,q=(0,h.Z)(F,M,N,!1,null,null,null),Z=q.exports,V=function(){var e=this,t=this,r=t.$createElement,a=t._self._c||r;return a("ta-modal",{attrs:{getContainer:t.setContainer,title:"更改组织",visible:t.visible,"ok-text":"确认","cancel-text":"关闭",width:"500px",size:"small","destroy-on-close":""},on:{ok:t.saveOrg,cancel:t.closeModal}},[a("ta-form",{attrs:{id:"orgForm","auto-form-create":function(t){e.formOrg=t}}},[a("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:"0"==t.config||"2"==t.config,expression:"config == '0' || config == '2'"}],attrs:{label:"0"==t.config?"所属组织":"直属组织","field-decorator-id":"orgId"}},[a("project-search-tree",{attrs:{getContainerId:"userManagement","init-value":t.namePath||""}})],1),a("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:"1"==t.config||"2"==t.config,expression:"config == '1' || config == '2'"}],staticClass:"item-tree",attrs:{label:"1"==t.config?"所属组织":"副属组织"}},[a("ta-e-tree",{ref:"tree",staticClass:"tree-style",attrs:{load:t.handleLoadTreeNode,"show-checkbox":"","highlight-current":"",props:t.defaultProps,"node-key":"orgId","check-strictly":"","default-checked-keys":t.checkedKeys,lazy:""},on:{check:t.handleNodeCheck}})],1)],1)],1)},j=[],$={name:"changeOrg",props:["visible","rowData","config"],data:function(){return{formOrg:null,changeOptions:[],defaultProps:{children:"children",label:"label",isLeaf:"isLeaf",id:"orgId"},checkedKeys:[],removeOrgIds:[],namePath:"",currOrgId:"",direct:""}},watch:{visible:function(e){var t=this;e&&n.Z.getOrgUserByUserId({userId:this.rowData.userId},(function(e){var r=e.data,a=r.list,o=r.direct;t.checkedKeys=a,t.direct=o?o.orgId:"",t.namePath=o?o.namePath:"",t.currOrgId=t.direct}))}},methods:{setContainer:function(){return document.getElementById("userManagement")},saveOrg:function(){var e=this,t={},r=this.formOrg.getFieldValue("orgId");if(r&&r.length?t.directOrgId=r:t.directOrgId=this.direct,t.userId=this.rowData.userId,"0"!=this.config){var a=this.$refs.tree.getCheckedKeys();t.addOrgIds=a.filter((function(t){return-1==e.checkedKeys.indexOf(t)})).join(","),t.removeOrgIds=this.removeOrgIds.join(",")}n.Z.updateOrgUserByUserId(t,(function(t){e.$message.success("重置所属组织成功"),e.$emit("queryTable"),e.closeModal()}))},closeModal:function(){this.formOrg.resetFields(),this.checkedKeys=[],this.removeOrgIds=[],this.currOrgId=this.direct,this.namePath="",this.$emit("close")},handleLoadTreeNode:function(e,t){if(0===e.level&&n.Z.loadOrgTree(null,(function(e){return t(e.orgTreeData)})),e.level>=1){var r=e.data.orgId||"";n.Z.loadOrgTree(r,(function(e){return t(e.orgTreeData)}))}},orgChange:function(e){var t=this,r=this.$refs.tree.getCheckedKeys().concat(this.checkedKeys),a=e[e.length-1];e.length?-1!=r.indexOf(a)?(this.$message.warning("直属组织不能与副属组织相同"),this.$nextTick((function(){t.currOrgId=t.direct,t.formOrg.resetFields()}))):this.currOrgId=a:(this.direct="",this.currOrgId="",this.namePath="")},handleNodeCheck:function(e,t){var r=t.checkedKeys,a=e.orgId,o=-1!=r.indexOf(a);if(o){if(a==this.currOrgId){this.$message.warning("副属组织不能与直属组织相同");var s=r.indexOf(a),i=r.slice(0,s).concat(r.slice(s+1));this.$refs.tree.setCheckedKeys(i)}var n=this.removeOrgIds.indexOf(a);-1!=n&&(0==n?this.removeOrgIds=[]:this.removeOrgIds.slice(0,n).concat(this.removeOrgIds.slice(n+1)))}else-1!=this.checkedKeys.indexOf(a)&&this.removeOrgIds.push(a)}}},K=$,z=(0,h.Z)(K,V,j,!1,null,null,null),W=z.exports,G=[{title:"姓名",dataIndex:"name",width:"15%",overflowTooltip:!0,scopedSlots:{customRender:"name"}},{title:"帐号",dataIndex:"loginId",width:"15%",overflowTooltip:!0},{title:"性别",dataIndex:"sex",width:80,scopedSlots:{customRender:"sex"}},{title:"所属组织",width:"15%",overflowTooltip:"namePath",dataIndex:"namePath",scopedSlots:{customRender:"namePath"}},{title:"手机号",dataIndex:"mobile",width:"15%",overflowTooltip:!0,scopedSlots:{customRender:"mobile"}},{title:"锁定",dataIndex:"isLock",width:80,scopedSlots:{customRender:"isLock"}},{title:"有效性",dataIndex:"effective",width:80,yesOrNoTag:!0,scopedSlots:{customRender:"effective"}},{slots:{title:"operationTitle"},dataIndex:"operation",align:"center",width:240,scopedSlots:{customRender:"operation"}}],Y=[{title:"组织名称",dataIndex:"orgName"},{title:"组织路径",dataIndex:"namePath"}],Q=[{element:".step1",popover:{title:"新增",description:"人员管理功能是提供给管理员对人员进行管理及维护，通过【新增】按钮添加人员信息，绑定组织关系，创建账号",position:"left"}},{element:".step2",popover:{title:"操作栏",description:"通过操作栏实现人员组织关系变更、账号解锁、密码重置等操作",position:"left"}}],H={name:"userManagement",components:{editUser:T,showUser:p,userSearchPanel:R,resetPwd:Z,changeOrg:W},mixins:[c.Z],data:function(){var e=this;return{orgUserTreeData:[],gridColumns:Y,columns:G,operateMenu:[{name:"编辑",disabled:function(e){return"0"===e.effective},title:function(e){return"0"===e.effective?"禁用的人员不允许编辑":""},onClick:function(t,r){e.editVisible=!0,e.editType="2",e.rowData=t,e.editIndex=r}},{name:"更改组织",disabled:function(e){return"0"===e.effective},title:function(e){return"0"===e.effective?"禁用的人员不允许更改组织":""},onClick:function(t){e.changeVisible=!0,e.rowData=t}},{name:"更多",type:"more",moreMenuList:[{name:"启用",type:"confirm",confirmTitle:"确认启用该账户?",onOk:function(t){e.changeEffectiveUnlock(1,t)}},{name:"禁用",type:"confirm",confirmTitle:"确认禁用该账户?",onOk:function(t){e.changeEffectiveUnlock(2,t)}},{name:"解锁",onClick:function(t){e.changeEffectiveUnlock(3,t)}},{name:"删除",type:"confirm",confirmTitle:"确认删除该用户?",isShow:function(t){return e.userList.length},onOk:function(t){e.onDeleteUser(t.userId)}},{name:"重置密码",onClick:function(t){e.resetVisible=!0,e.rowData=t}}]}],userList:[],editType:"",editVisible:!1,showVisible:!1,resetVisible:!1,deleteVisible:!1,changeVisible:!1,deleteUserIds:"",rowData:{},editIndex:"",selectedRowKeys:[],selectedRows:[],searchInfo:"",orgIds:[],checkedOrgChild:!0,checkedVirtualOrg:!1,checkedNoneffectiveOrg:!1,searchFlag:"normal",searchPanelResult:{},userSearchUrl:"org/orguser/userManagementRestService/queryUserByConditon",options:[],islockList:[],userEffectiveList:[],config:"",orgInfo:[],steps:Q}},computed:{btnDisable:function(){return!this.selectedRowKeys.length}},mounted:function(){this.getUsers(),this.getConfig(),this.fnCommonGuide(this.steps)},methods:{getConfig:function(){var e=this;n.Z.getUserOrgMultiConfig({},(function(t){e.config=t.data.config}))},userPageParams:function(){if("normal"===this.searchFlag){var e={},t=this.orgIds;return void 0!==t&&""!==t&&(e.orgId=t),""!==this.searchInfo&&(e.name=this.searchInfo,e.loginId=this.searchInfo,e.idCardNo=this.searchInfo),e.showChildUser=this.checkedOrgChild,e.virtualOrg=this.checkedVirtualOrg,e.noneffectiveOrg=this.checkedNoneffectiveOrg,e.islock=this.islockList,e.userEffective=this.userEffectiveList,e.searchType="normal",e}return this.searchPanelResult},onSearchUser:function(){this.searchFlag="normal",this.getUsers()},getUsers:function(){var e=this;this.$nextTick((function(){e.$refs.gridPager.loadData()}))},dealSearchPanelResult:function(e){this.searchFlag="advance",null!=e&&(this.searchPanelResult=e),this.searchPanelResult.searchType="advance",this.getUsers()},handleSelectChange:function(e,t){this.selectedRowKeys=e,this.selectedRows=t},onChangeChildOrg:function(e){this.checkedOrgChild=e.target.checked,this.onSearchUser()},onChangeNoneffectiveOrg:function(e){this.checkedNoneffectiveOrg=e.target.checked,!0===this.checkedVirtualOrg&&(this.checkedVirtualOrg=!e.target.checked),this.orgIds=void 0,this.onSearchUser()},onChangeVirtualOrg:function(e){this.checkedVirtualOrg=e.target.checked,!0===this.checkedNoneffectiveOrg&&(this.checkedNoneffectiveOrg=!e.target.checked),this.orgIds=void 0,this.onSearchUser()},editSuccess:function(e){this.getUsers()},changeEffectiveUnlock:function(e,t){var r=this,a=[];if(t)if(1===e){if("0"!==t.effective)return void this.$message.warning("该记录已经启用，请勿重复操作！");a.push(t.userId)}else if(2===e){if("1"!==t.effective)return void this.$message.warning("该记录已经禁用，请勿重复操作！");a.push(t.userId)}else{if("1"!==t.isLock)return void this.$message.warning("该记录未锁定，无需解锁！");a.push(t.userId)}else a=this.selectedRowKeys;n.Z.changeEffectiveUnlock({userIds:a.join(","),type:e},(function(t){r.onSearchUser(),r.selectedRowKeys=[],r.selectedRows=[],3===e?r.$message.success("解锁成功"):r.$message.success((1===e?"启用":"禁用")+"成功")}))},fnDeleteUser:function(e){this.deleteUserIds=this.selectedRowKeys.join(","),this.deleteVisible=!0},deleteBatch:function(){var e=this;n.Z.deleteBatchUserByUserIds({userIds:this.deleteUserIds},(function(t){e.$message.success("删除成功"),e.onSearchUser(),e.deleteVisible=!1,e.selectedRowKeys=[],e.selectedRows=[]}))},onDeleteUser:function(e){var t=this;n.Z.deleteBatchUserByUserIds({userIds:e},(function(e){t.$message.success("删除操作成功"),t.onSearchUser()}))},queryOrgInfo:function(e){var t=this;n.Z.queryOrgInfoByUserId({userId:e},(function(e){t.orgInfo=e.data.list}))},getLastName:function(e){return e&&-1!==e.indexOf("/")?e.slice(e.lastIndexOf("/")+1):e}}},X=H,J=(0,h.Z)(X,a,o,!1,null,"90d56fb6",null),ee=J.exports},35478:function(e,t){t["Z"]={data:{formTypeSettings:{}},watch:{formSettings:function(){var e={};this.formSettings.forEach((function(t){e[t.formId]=t.formItem.type})),this.formTypeSettings=e}},methods:{formatSensitiveValue:function(e,t){return"sensitive-input"==this.formTypeSettings[e]&&t?JSON.parse(t).sensitiveField:t}}}},51095:function(e,t){var r=[{fieldId:"sex",formId:"sex",displayText:"性别",hide:"0",effective:"1",required:"0",unchangeable:"0",protectPrivacy:"0",orderNo:1,contentSize:null,tiText:"请输入性别",validReg:null,connectAA10:"SEX",formType:"radio",more:"0"},{fieldId:"jobNumber",formId:"jobNumber",displayText:"工号",hide:"0",effective:"1",required:"0",unchangeable:"0",protectPrivacy:"0",orderNo:2,contentSize:null,tiText:"",validReg:"",connectAA10:null,formType:"input",more:"0"},{fieldId:"idCardType",formId:"idCardType",displayText:"证件类型",hide:"0",effective:"1",required:"0",unchangeable:"0",protectPrivacy:"0",orderNo:3,contentSize:null,tiText:"",validReg:"",connectAA10:"IDCARDTYPE",formType:"select",more:"0"},{fieldId:"idCardNo",formId:"idCardNo",displayText:"证件号",hide:"0",effective:"1",required:"0",unchangeable:"0",protectPrivacy:"0",orderNo:4,contentSize:null,tiText:"",validReg:"/(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)/",connectAA10:null,formType:"input",more:"0"},{fieldId:"mobile",formId:"mobile",displayText:"手机号",hide:"0",effective:"1",required:"0",unchangeable:"0",protectPrivacy:"0",orderNo:5,contentSize:null,tiText:"",validReg:"/^1[3|4|5|7|8|9][0-9]\\d{8}$/",connectAA10:null,formType:"input",more:"0"},{fieldId:"tags",formId:"tags",displayText:"用户标签",hide:"0",effective:"1",required:"0",unchangeable:"0",protectPrivacy:"0",orderNo:6,contentSize:null,tiText:"",validReg:null,connectAA10:null,formType:"slot",more:"0"},{fieldId:"education",formId:"education",displayText:"学历",hide:"0",effective:"1",required:"0",unchangeable:"0",protectPrivacy:"0",orderNo:7,contentSize:null,tiText:"",validReg:null,connectAA10:"EDUCATION",formType:"select",more:"1"},{fieldId:"email",formId:"email",displayText:"邮箱地址",hide:"0",effective:"1",required:"0",unchangeable:"0",protectPrivacy:"0",orderNo:8,contentSize:null,tiText:"",validReg:"/^([A-Za-z0-9_\\-\\.])+\\@([A-Za-z0-9_\\-\\.])+\\.([A-Za-z]{2,4})$/",connectAA10:null,formType:"input",more:"1"},{fieldId:"address",formId:"address",displayText:"联系地址",hide:"0",effective:"1",required:"0",unchangeable:"0",protectPrivacy:"0",orderNo:9,contentSize:null,tiText:"",validReg:null,connectAA10:null,formType:"input",more:"1"},{fieldId:"zipCode",formId:"zipCode",displayText:"邮政编码",hide:"0",effective:"1",required:"0",unchangeable:"0",protectPrivacy:"0",orderNo:10,contentSize:null,tiText:"",validReg:"/^[0-9]{6}$/",connectAA10:null,formType:"input",more:"1"},{fieldId:"workplace",formId:"workplace",displayText:"工作单位",hide:"0",effective:"1",required:"0",unchangeable:"0",protectPrivacy:"0",orderNo:11,contentSize:null,tiText:"",validReg:null,connectAA10:null,formType:"input",more:"1"}],a={fieldId:"field",formId:"field",displayText:"扩展信息",effective:"0",hide:"1",required:"0",unchangeable:"0",protectPrivacy:"0",orderNo:1,contentSize:null,tiText:null,validReg:null,connectAA10:null,formType:"input",more:"1"};function o(e,t){return(Array(t).join(0)+e).slice(-t)}for(var s=10,i=1;i<=s;i++){var n=Object.assign({},a),l=o(i,2);n.fieldId=n.fieldId+l,n.formId=n.formId+l,n.displayText=n.displayText+l,n.orderNo=r.length+1,r.push(n)}t["Z"]=r},80790:function(e,t,r){var a=r(76698);r(34530);t["Z"]={activated:function(){var e=window.pageVmObj,t=e["steps_"+e._route.name];this.methods?this.methods.fnCommonGuide(t):this.fnCommonGuide(t)},deactivated:function(){this.methods?this.methods.fnCommonGuide([],!0):this.fnCommonGuide([],!0)},methods:{fnCommonGuide:function(e,t){var r=window.pageVmObj;t?(r.driver.reset(),window.fnPageGuide=null):(r["steps_"+r._route.name]=e,r.driver=new a.Z({allowClose:!1}),window.fnPageGuide=function(){r.driver.defineSteps(e),r.driver.start()})}}}},63458:function(e,t){var r="/org/orguser/userManagementRestService/";t["Z"]={queryTagByUserId:function(e,t){Base.submit(null,{url:r+"queryTagByUserId",data:e,showPageLoading:!1},{successCallback:function(e){return t(e)}})},addUser:function(e,t){Base.submit(null,{url:r+"addUser",data:e},{successCallback:function(e){return t(e)}})},updateUserByUserId:function(e,t){Base.submit(null,{url:r+"updateUserByUserId",data:e},{successCallback:function(e){return t(e)}})},queryAvatar:function(e,t){Base.submit(null,{url:r+"queryAvatar",data:e,showPageLoading:!1},{successCallback:function(e){return t(e)}})},deleteBatchUserByUserIds:function(e,t){Base.submit(null,{url:r+"deleteBatchUserByUserIds",data:e},{successCallback:function(e){return t(e)}})},updateUserOrgByUserId:function(e,t){Base.submit(null,{url:r+"updateUserOrgByUserId",data:e},{successCallback:function(e){return t(e)}})},changeEffectiveUnlock:function(e,t){var a="";a=1==e.type?"updateBatchUserAbleByUserIds":2==e.type?"updateBatchUserDisabledByUserIds":"updateBatchUserUnLockByUserIds",Base.submit(null,{url:r+a,data:e},{successCallback:function(e){return t(e)}})},updateUserPwdByUserId:function(e,t){Base.submit(null,{url:r+"updateUserPwdByUserId",data:e},{successCallback:function(e){return t(e)}})},loadOrgTree:function(e,t){Base.submit(null,{url:"org/orguser/orgManagementRestService/getOrgByAsync",data:{orgId:e}},{successCallback:function(e){return t(e.data)}})},queryUserList:function(e,t){var r=e.orgId,a=e.userId,o=e.includeChild,s=e.pageSize,i=e.pageNum,n=e.searchVal,l=e.searchType,c=e.searchParam,d={orgId:r,userId:a,showChildUser:o?1:0,pageSize:s,pageNumber:i,name:n};l&&(d[l]=c),Base.submit(null,{url:"org/orguser/userManagementRestService/queryEffectiveUser",data:d},{successCallback:function(e){return t(e.data)}})},queryUserSettingTable:function(e){Base.submit(null,{url:"org/sysmg/manageableFieldsRestService/queryManageableFields",data:{type:"1"}},{successCallback:function(t){return e(t.data)}})},getOrgUserByUserId:function(e,t){Base.submit(null,{url:r+"getOrgUserByUserId",data:e},{successCallback:function(e){return t(e)}})},queryOrgInfoByUserId:function(e,t){Base.submit(null,{url:r+"queryOrgInfoByUserId",data:e},{successCallback:function(e){return t(e)}})},getUserOrgMultiConfig:function(e,t){Base.submit(null,{url:r+"getUserOrgMultiConfig",data:e},{successCallback:function(e){return t(e)}})},updateOrgUserByUserId:function(e,t){Base.submit(null,{url:r+"updateOrgUserByUserId",data:e},{successCallback:function(e){return t(e)}})},authRequestForUserInfo:function(e,t){Base.submit(null,{url:r+"getUserByUserId",data:e},{successCallback:function(e){return t(e.data)}})}}},75145:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){var D_GIT_hiiss_ms_frontend_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_5__=__webpack_require__(95082),_api_index__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__(63458),_projectCommon_js_extendConfig_extendUserSetting__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__(51095),_yh_ta_utils_isArray__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__(41538),_projectCommon_js_extendConfig_commonMixins__WEBPACK_IMPORTED_MODULE_3__=__webpack_require__(35478),_projectCommon_js_extendConfig_guideMixins__WEBPACK_IMPORTED_MODULE_4__=__webpack_require__(80790),formItemLayouts={default:{labelCol:{span:6},wrapperCol:{span:18}}},mixins={data:function(){return(0,D_GIT_hiiss_ms_frontend_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_5__.Z)({formItemLayouts:formItemLayouts,extendSettings:_projectCommon_js_extendConfig_extendUserSetting__WEBPACK_IMPORTED_MODULE_1__.Z,formSettings:[]},_projectCommon_js_extendConfig_commonMixins__WEBPACK_IMPORTED_MODULE_3__.Z.data)},created:function(){var e=this;window.mixinsUser?(this.extendSettings=(0,_yh_ta_utils_isArray__WEBPACK_IMPORTED_MODULE_2__.Z)(window.mixinsUser)&&window.mixinsUser.length?window.mixinsUser:this.extendSettings,this.buildForm({})):(window.mixinsUser=!0,_api_index__WEBPACK_IMPORTED_MODULE_0__.Z.queryUserSettingTable((function(t){(0,_yh_ta_utils_isArray__WEBPACK_IMPORTED_MODULE_2__.Z)(t.resultData)&&t.resultData.length>0&&(e.extendSettings=t.resultData),window.mixinsUser=t.resultData,e.buildForm({})})))},computed:{formNormalSettings:function(){return this.formSettings.filter((function(e){return!e.isMore&&e.exist}))||[]},formMoreSettings:function(){return this.formSettings.filter((function(e){return e.isMore&&e.exist}))||[]}},activated:function(){_projectCommon_js_extendConfig_guideMixins__WEBPACK_IMPORTED_MODULE_4__.Z.activated()},deactivated:function(){_projectCommon_js_extendConfig_guideMixins__WEBPACK_IMPORTED_MODULE_4__.Z.deactivated()},watch:(0,D_GIT_hiiss_ms_frontend_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_5__.Z)({},_projectCommon_js_extendConfig_commonMixins__WEBPACK_IMPORTED_MODULE_3__.Z.watch),methods:(0,D_GIT_hiiss_ms_frontend_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_5__.Z)((0,D_GIT_hiiss_ms_frontend_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_5__.Z)((0,D_GIT_hiiss_ms_frontend_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_5__.Z)({},_projectCommon_js_extendConfig_commonMixins__WEBPACK_IMPORTED_MODULE_3__.Z.methods),_projectCommon_js_extendConfig_guideMixins__WEBPACK_IMPORTED_MODULE_4__.Z.methods),{},{buildForm:function(e){this.formSettings=this.getFormSettings(e)},getFormNecessarySettings:function(e){return[{id:"avatar",formId:"",formItem:{type:"slot",collection:null},label:"用户头像",class:"avatar-form-item",display:!0,exist:!0,formItemLayout:formItemLayouts.default},{id:"orgId",formId:"orgId",formItem:{type:"slot",collection:null},label:"所属组织",decoratorOptions:{rules:[{required:!0,message:"请选择人员的所属组织"}]},display:!0,exist:"1"==this.editType,formItemLayout:formItemLayouts.default},{id:"orgIdShow",formId:"",formItem:{type:"slot",collection:null},label:"所属组织",decoratorOptions:{},display:!0,exist:"2"==this.editType,formItemLayout:formItemLayouts.default},{id:"name",formId:"name",formItem:{type:"input",collection:null},label:"姓名",decoratorOptions:{rules:[{required:!0,message:"请输入人员姓名"},{max:20,message:"姓名长度不能大于20"}],initialValue:e.name},display:!0,exist:!0,formItemLayout:formItemLayouts.default},{id:"loginId",formId:"loginId",formItem:{type:"input",collection:null},label:"账号",decoratorOptions:{rules:[{required:!0,message:"请输入人员登录帐号"},{max:30,message:"长度限制"}],initialValue:e.loginId},display:!0,exist:!0,disabled:"2"==this.editType,formItemLayout:formItemLayouts.default},{id:"password",formId:"password",formItem:{type:"slot",collection:null},label:"登录口令",decoratorOptions:{rules:[{required:!0,message:"请输入登录口令"},{min:6,message:"登录口令不能少于6位字符"},{validator:this.validatePwd}]},display:!0,exist:"1"==this.editType,formItemLayout:formItemLayouts.default},{id:"password_2",formId:"password_2",formItem:{type:"slot",collection:null},label:"确认口令",decoratorOptions:{rules:[{validator:this.compareToFirstPwd},{required:!0,message:"请再次输入登录口令"}]},display:!0,exist:"1"==this.editType,formItemLayout:formItemLayouts.default}]},getFormSettings:function getFormSettings(){var initData=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},propSettings=this.extendSettings.sort((function(e,t){return e.orderNo-t.orderNo})).map((function(setting){var formId=setting.formId,label=setting.displayText||"",placeholder=setting.tiText||"",propSetting={id:setting.fieldId,formId:formId,class:null,formItem:{type:setting.formType,collection:setting.connectAA10},label:label,display:"0"===setting.hide,exist:"1"===setting.effective,disabled:"1"===setting.unchangeable,formItemLayout:formItemLayouts[formId]||formItemLayouts.default,decoratorOptions:{},placeholder:placeholder,isMore:"1"===setting.more},rules=[];if("1"===setting.required&&rules.push({required:!0,message:label+"是必须的"}),isNaN(parseInt(setting.contentSize))||rules.push({max:setting.contentSize,message:label+"内容长度不能超过"+setting.contentSize}),setting.validReg){var isreg;try{isreg=eval(setting.validReg)instanceof RegExp}catch(e){isreg=!1}isreg&&rules.push({pattern:eval(setting.validReg),message:"请输入正确的"+label+"内容"})}return propSetting.decoratorOptions.rules=rules,initData.hasOwnProperty(formId)&&(propSetting.decoratorOptions.initialValue=initData[formId]),propSetting}));return this.getFormNecessarySettings(initData).concat(propSettings)}})};__webpack_exports__["Z"]=mixins}}]);
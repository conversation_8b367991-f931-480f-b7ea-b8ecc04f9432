"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[7939],{82947:function(e,t,r){r.d(t,{Z:function(){return l}});var s=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",[r("div",{staticClass:"menu-title"},[r("ta-icon",{attrs:{type:"menu-fold"}}),r("span",{staticStyle:{"margin-left":"5px","font-weight":"bold"}},[e._v(e._s(e.title))])],1),r("ta-divider",{staticStyle:{margin:"0"}}),r("ta-menu",{attrs:{mode:"inline",selectedKeys:e.selectedKeys},on:{click:e.onClick}},e._l(e.data,(function(t){return r("ta-menu-item",{key:t.resourceId},[r("ta-icon",{attrs:{type:"appstore"}}),e._v(" "+e._s(t.name)+" ")],1)})),1)],1)},a=[],n={name:"adminLeftMenu",props:{title:{type:String,required:!0},data:{type:Array,required:!0},selectedKeys:{type:Array}},data:function(){return{}},methods:{onClick:function(e){var t=e.item,r=e.key,s=e.keyPath;this.$emit("click",{item:t,key:r,keyPath:s})}}},i=n,c=r(1001),u=(0,c.Z)(i,s,a,!1,null,"8bcd3b7a",null),l=u.exports},50074:function(e,t,r){r.r(t),r.d(t,{default:function(){return f}});var s=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"fit"},[r("ta-border-layout",{attrs:{layout:{header:"70px"}}},[r("div",{staticStyle:{"text-align":"center",overflow:"hidden"},attrs:{slot:"header"},slot:"header"},[r("ta-breadcrumb",{staticStyle:{"line-height":"42px",float:"left"},attrs:{separator:">"}},[r("ta-breadcrumb-item",[r("a",{on:{click:e.fnBackToHome}},[e._v("角色维度")])]),r("ta-breadcrumb-item",[e._v("自定义授权权限")])],1),r("div",{staticClass:"divider-header"}),r("ta-alert",{staticStyle:{float:"left","margin-top":"2px"},attrs:{message:"当前角色为："+this.$route.query.roleName,type:"info","show-icon":""}})],1),r("ta-tabs",{staticClass:"fit"},[r("ta-tab-pane",{attrs:{tab:"自定义授权权限&&自定义再授权权限"}},[r("ta-row",{staticClass:"fit"},[r("ta-col",{staticClass:"fit authority-box",staticStyle:{"border-right":"1px solid #eee"},attrs:{span:4}},[r("admin-left-menu",{attrs:{title:"自定义授权一级菜单",data:e.menuData,"selected-keys":e.menuSelectedKeys},on:{click:e.onSelectMenu}})],1),r("ta-col",{staticClass:"fit authority-box",staticStyle:{padding:"5px","border-left":"1px solid #eee"},attrs:{span:20}},[r("ta-row",{staticClass:"fit",attrs:{gutter:10}},[r("ta-col",{attrs:{span:11}},[r("span",{staticStyle:{"font-weight":"bold"}},[e._v("对象授权权限:")]),r("div",{staticStyle:{padding:"10px 0"}},[r("ta-input",{staticStyle:{width:"250px"},attrs:{placeholder:"输入关键字进行过滤"},model:{value:e.leftFilterText,callback:function(t){e.leftFilterText=t},expression:"leftFilterText"}})],1),e.leafKeys.length>0?r("ta-e-tree",{ref:"tree",attrs:{"show-checkbox":"",load:e.loadLeftNode,"node-key":"resourceId","highlight-current":"",props:e.defaultProps,"expand-on-click-node":!0,"check-strictly":!0,"filter-node-method":e.filterNode,lazy:""},on:{check:e.fnCheckLeft}}):e._e()],1),r("ta-col",{attrs:{span:11}},[r("span",{staticStyle:{"font-weight":"bold"}},[e._v("对象再授权权限:")]),r("div",{staticStyle:{padding:"10px 0"}},[r("ta-input",{staticStyle:{width:"250px"},attrs:{placeholder:"输入关键字进行过滤"},model:{value:e.rightFilterText,callback:function(t){e.rightFilterText=t},expression:"rightFilterText"}})],1),e.leafKeys.length>0?r("ta-e-tree",{ref:"rtree",attrs:{"show-checkbox":"",load:e.loadRightNode,"node-key":"resourceId","highlight-current":"","check-strictly":!0,props:e.defaultProps,"expand-on-click-node":!0,"filter-node-method":e.filterNode,lazy:""},on:{check:e.fnRightCheck}}):e._e()],1)],1)],1)],1)],1),r("template",{slot:"tabBarExtraContent"},[r("ta-button",{on:{click:e.fnBackToHome}},[r("ta-icon",{attrs:{type:"rollback"}}),e._v("返回 ")],1),r("ta-button",{attrs:{type:"primary"},on:{click:function(t){return e.fnSaveAdminObjectUsePermission("refresh")}}},[e._v(" 保存 ")])],1)],2)],1)],1)},a=[],n=r(89584),i=r(16158),c=r(82947),u={name:"adminGrantAuthority",components:{AdminLeftMenu:c.Z},data:function(){return{clickIndex:0,menuData:[],leftFilterText:"",leftFullCheckKeys:[],leftHalfCheckKeys:[],leftDefaultCheckedKeys:[],leftExpandList:[],menuSelectedKeys:[],rightFilterText:"",rightFullCheckKeys:[],rightHalfCheckKeys:[],rightDefaultCheckedKeys:[],rightExpandList:[],leafKeys:[],defaultProps:{children:"children",label:"name",id:"resourceId"},resourceItem:"",row:{col:{span:12}},item:{},roleId:""}},watch:{leftFilterText:function(e){this.$refs.tree.filter(e)},rightFilterText:function(e){this.$refs.rtree.filter(e)}},activated:function(){this.item=this.$route.query,this.item.roleId?(this.roleId=this.item.roleId,this.menuData=[],this.leftFullCheckKeys=[],this.leftHalfCheckKeys=[],this.rightFullCheckKeys=[],this.rightHalfCheckKeys=[],this.leafKeys=[],this.fnLoadDefault()):this.fnBackToHome()},methods:{loadLeftNode:function(e,t){var r=this,s=this.resourceItem,a=s.resourceId;if(0===e.level){var c=this.inArray(a,this.leftFullCheckKeys),u=this.inArray(a,this.leftHalfCheckKeys);return 0==s.auth&&(s.disabled=!0),s.idPath=[a],t([s]),void this.$nextTick((function(){var e=r.$refs.tree;if(u>=0){var t=e.getNode(a);t.isLeaf=s.isLeaf,t.indeterminate=!0}else c>=0?e.setChecked(a,!0,!1):e.setChecked(a,!1,!1)}))}var l=e.data.resourceId;this.leftExpandList.push(l);var o={roleId:this.roleId,resourceId:l};i.Z.queryChildCustomResourceAsync(o,(function(s){var a=s.data.customResourceTree;a.forEach((function(e){0==e.auth&&(e.disabled=!0)})),t(a);var i=e.data.idPath;a.forEach((function(t){var s=t.resourceId,a=r.$refs.tree,c=a.getNode(s),u=r.inArray(s,r.leafKeys);c.isLeaf=u>=0,c.data.idPath=[].concat((0,n.Z)(i),[c.data.resourceId]),e.checked&&(c.checked=!0)})),r.leftFullCheckKeys.forEach((function(e){var t=r.$refs.tree;t.setChecked(e,!0,!1)})),e.checked||r.leftHalfCheckKeys.forEach((function(e){var t=r.$refs.tree,s=t.getNode(e);null!=s&&(s.indeterminate=!0)})),r.$nextTick((function(){r.$refs.rtree.updateKeyChildren(e.id,a),r.$refs.rtree.getNode(l).expand()}))}))},loadRightNode:function(e,t){var r=this,s=this.resourceItem,a=s.resourceId;if(0===e.level){var c=this.inArray(a,this.rightFullCheckKeys),u=this.inArray(a,this.rightHalfCheckKeys);return 0==s.auth&&(s.disabled=!0),s.idPath=[a],t([s]),void this.$nextTick((function(){var e=r.$refs.rtree;if(u>=0){var t=e.getNode(a);t.isLeaf=s.isLeaf,t.indeterminate=!0}else c>=0?e.setChecked(a,!0,!1):e.setChecked(a,!1,!1)}))}var l=e.data.resourceId;this.rightExpandList.push(l);var o={roleId:this.roleId,resourceId:l};i.Z.queryChildCustomResourceAsync(o,(function(s){var a=s.data.customResourceTree;a.forEach((function(e){0==e.auth&&(e.disabled=!0)})),t(a);var i=e.data.idPath;a.forEach((function(t){var s=t.resourceId,a=r.$refs.rtree,c=a.getNode(s),u=r.inArray(s,r.leafKeys);c.isLeaf=u>=0,c.data.idPath=[].concat((0,n.Z)(i),[c.data.resourceId]),e.checked&&(c.checked=!0)})),r.rightFullCheckKeys.forEach((function(e){var t=r.$refs.rtree;t.setChecked(e,!0,!1)})),e.checked||r.rightHalfCheckKeys.forEach((function(e){var t=r.$refs.rtree,s=t.getNode(e);null!=s&&(s.indeterminate=!0)})),r.$nextTick((function(){r.$refs.tree.updateKeyChildren(e.id,a),r.$refs.tree.getNode(l).expand()}))}))},inArray:function(e,t){for(var r=0;r<t.length;r++)if(e==t[r])return r;return-1},filterNode:function(e,t){return!e||-1!==t.name.indexOf(e)},onSelectMenu:function(e){var t=this,r=(e.item,e.key),s=e.keyPath;if(this.resourceItem.resouceId!==r){var a=[],i=this.$refs.tree;a=[].concat((0,n.Z)(a),(0,n.Z)(i.getCheckedKeys(!0)));var c=(0,n.Z)(this.menuData),u=c.filter((function(e){return r===e.resourceId}))[0];this.$refs.rtree.getCheckedKeys(!0);this.leftDefaultCheckedKeys.sort().toString()!==a.sort().toString()?this.$confirm({title:"提示",content:"当前对象授权权限修改未保存，是否保存?",cancelText:"取消",okText:"确认",onOk:function(){t.fnSaveAdminObjectUsePermission(),t.menuSelectedKeys=s,t.$nextTick((function(){t.fnQueryObjectGrantPermissionByResourceId(u)}))},onCancel:function(){t.menuSelectedKeys=s,t.fnQueryObjectGrantPermissionByResourceId(u)}}):(this.menuSelectedKeys=s,this.fnQueryObjectGrantPermissionByResourceId(u))}},fnCheckLeft:function(e,t){if(e){var r=this.$refs.tree.getNode(e.resourceId).checked;this.checkTree(e,r,this.$refs.tree),r||(this.$refs.rtree.setChecked(e.resourceId,!1,!1),this.checkTree(e,r,this.$refs.rtree))}},fnRightCheck:function(e,t){if(e){var r=this.$refs.rtree.getNode(e.resourceId).checked;this.checkTree(e,r,this.$refs.rtree),r&&(this.$refs.tree.setChecked(e.resourceId,!0,!1),this.checkTree(e,r,this.$refs.tree))}},checkTree:function(e,t,r){var s=r.getNode(e.resourceId);if(t){var a=r.getNode(e.parentId);while(null!=a&&null!==a.parent.data){var n=r.getNode(a.data.resourceId);n.checked?n.checked=!0:n.indeterminate=!0,a=r.getNode(a.data.parentId)}}else{var i=r.getNode(e.parentId);while(null!=i&&null!==i.parent.data){var c=i.childNodes.find((function(e){return e.checked||e.indeterminate}));c?(i.indeterminate=!0,i.checked=!1):(i.indeterminate=!1,i.checked=!1),i=r.getNode(i.data.parentId)}}this.checkChild(s,t)},checkChild:function(e,t){var r=this,s=e.childNodes;null!=s&&s.length>0&&s.forEach((function(e){e.indeterminate=!1,e.checked=t,r.checkChild(e,t)}))},fnSaveAdminObjectUsePermission:function(e){var t=this,r=this.getResourceTreeParam();i.Z.saveAdminObjectGrantPermission(r,(function(r){t.$message.success("更新数据成功"),"refresh"==e&&t.fnQueryObjectGrantPermissionByResourceId(t.resourceItem)}))},fnLoadDefault:function(){var e=this,t={roleId:this.roleId};i.Z.queryObjectGrantSysPermission(t,(function(t){e.menuData=t.data.customResourceList,e.$nextTick((function(){e.menuData&&e.menuData[0]&&(e.menuSelectedKeys=[e.menuData[0].resourceId],e.fnQueryObjectGrantPermissionByResourceId(e.menuData[0]))}))}))},fnQueryObjectGrantPermissionByResourceId:function(e){var t=this;this.resourceItem=e;var r={roleId:this.roleId,resourceId:e.resourceId};this.leftFullCheckKeys=[],this.leftHalfCheckKeys=[],this.rightFullCheckKeys=[],this.rightHalfCheckKeys=[],this.leafKeys=[],i.Z.queryObjectGrantPermissionByResourceId(r,(function(e){t.leftFullCheckKeys=e.data.rePerFullCheckList,t.leftHalfCheckKeys=e.data.rePerHalfCheckList,t.leftDefaultCheckedKeys=[].concat((0,n.Z)(t.leftFullCheckKeys),(0,n.Z)(t.leftHalfCheckKeys)),t.leafKeys=e.data.leafList,t.rightFullCheckKeys=e.data.reAuthFullCheckList,t.rightHalfCheckKeys=e.data.reAuthHalfCheckList,t.rightDefaultCheckedKeys=[].concat((0,n.Z)(t.rightFullCheckKeys),(0,n.Z)(t.rightHalfCheckKeys)),t.leftExpandList=[],t.rightExpandList=[]}))},getResourceTreeParam:function(){var e=this,t=this.$refs.tree,r=t.getCheckedNodes(),s=[],a=[],n=[];r.forEach((function(r){t.getNode(r.resourceId).expanded||n.push(r.resourceId),r.isLeaf||e.inArray(r.resourceId,e.leftExpandList)>=0?s.push(r.resourceId):a.push(r.resourceId)}));var i=this.$refs.rtree,c=i.getCheckedNodes(),u=[],l=[],o=[];c.forEach((function(t){i.getNode(t.resourceId).expanded||o.push(t.resourceId),t.isLeaf||e.inArray(t.resourceId,e.rightExpandList)>=0?u.push(t.resourceId):l.push(t.resourceId)}));var d=this.resourceItem.resourceId;return{roleId:this.roleId,parentResourceId:d,rePerResourceIds:s.join(","),rePerNeedChildResourceIds:a.join(","),rePerHalfNeedChildResourceIds:n.join(","),reAuthResourceIds:u.join(","),reAuthNeedChildResourceIds:l.join(","),reAuthHalfNeedChildResourceIds:o.join(",")}},fnBackToHome:function(){this.$router.push({name:"adminRoleManagement"})}}},l=u,o=r(1001),d=(0,o.Z)(l,s,a,!1,null,"f51d2272",null),f=d.exports},16158:function(e,t){var r="/org/authority/adminAuthorityManagementRestService/";t["Z"]={queryUsersByRoleId:function(e,t){Base.submit(null,{url:r+"queryUsersByRoleId",data:e},{successCallback:function(e){return t(e)}})},queryUsersByRoleIdNoPage:function(e,t){Base.submit(null,{url:r+"queryUsersByRoleIdNoPage",data:e},{successCallback:function(e){return t(e)}})},addBatchAdminUser:function(e,t){Base.submit(null,{url:r+"addBatchRoleUsers",data:e},{successCallback:function(e){return t(e)}})},deleteBatchAdmin:function(e,t){Base.submit(null,{url:r+"deleteBatchRole",data:e},{successCallback:function(e){return t(e)}})},queryAllTaOrg:function(e,t){Base.submit(null,{url:r+"queryCurrentAdminRoleWrapOrgTree",data:e},{successCallback:function(e){return t(e)}})},deleteRoleUserByKey:function(e,t){Base.submit(null,{url:r+"deleteRoleUserByKey",data:e},{successCallback:function(e){return t(e)}})},deleteBatchRoleUsers:function(e,t){Base.submit(null,{url:r+"deleteBatchRoleUsers",data:e},{successCallback:function(e){return t(e)}})},queryBatchUserByOrgId:function(e,t){Base.submit(null,{url:r+"queryUsersNoWraperByRoleId",data:e},{successCallback:function(e){return t(e)}})},queryBatchUserByOrgIdNoPage:function(e,t){Base.submit(null,{url:r+"queryUsersNoWraperByRoleIdNoPage",data:e},{successCallback:function(e){return t(e)}})},queryOrgPermission:function(e,t){Base.submit(null,{url:r+"selectOrgScope",data:e},{successCallback:function(e){return t(e)}})},removeOrgPermission:function(e,t){Base.submit(null,{url:r+"removeOrgScope",data:e},{successCallback:function(e){return t(e)}})},selectPermissionOrgScope:function(e,t){Base.submit(null,{url:r+"selectPermissionOrgScope",data:e},{successCallback:function(e){return t(e)}})},addOrgPermission:function(e,t){Base.submit(null,{url:r+"saveOrgScope",data:e},{successCallback:function(e){return t(e)}})},queryBatchPermissionTreeData:function(e,t){Base.submit(null,{url:r+"queryBatchPermissionTreeData",data:e},{successCallback:function(e){return t(e)}})},batchChangeAdminPermission:function(e,t){Base.submit(null,{url:r+"batchChangeAdminPermission",data:e},{successCallback:function(e){return t(e)}})},addAdminRole:function(e,t){Base.submit(null,{url:r+"addAdminRole",data:e},{successCallback:function(e){return t(e)}})},updateAdmin:function(e,t){Base.submit(null,{url:r+"updateAdmin",data:e},{successCallback:function(e){return t(e)}})},updateBatchAdminStatus:function(e,t){Base.submit(null,{url:r+"updateRoleEffectiveByRoleId",data:e},{successCallback:function(e){return t(e)}})},queryUseSysPermission:function(e,t){Base.submit(null,{url:r+"queryRootResource",data:e},{successCallback:function(e){return t(e)}})},queryUsePermissionByResourceId:function(e,t){Base.submit(null,{url:r+"queryChildResourceByRoleId",data:e},{successCallback:function(e){return t(e)}})},saveAdminUsePermission:function(e,t){Base.submit(null,{url:r+"changeResourceUsePermission",data:e},{successCallback:function(e){return t(e)}})},updateAdminUsePermissionEffectiveTime:function(e,t){Base.submit(null,{url:r+"updateUsePermissionEffectiveTime",data:e},{successCallback:function(e){return t(e)}})},saveBatchAdminUsePermissionEffectiveTime:function(e,t){Base.submit(null,{url:r+"updateBatchUsePermissionEffectiveTime",data:e},{successCallback:function(e){return t(e)}})},queryObjectUseSysPermission:function(e,t){Base.submit(null,{url:r+"queryRootCustomResource",data:e},{successCallback:function(e){return t(e)}})},queryObjectUsePermissionByResourceId:function(e,t){Base.submit(null,{url:r+"queryChildCustomResourceByRoleId",data:e},{successCallback:function(e){return t(e)}})},saveAdminObjectUsePermission:function(e,t){Base.submit(null,{url:r+"changeCustomResourceUsePermission",data:e},{successCallback:function(e){return t(e)}})},updateAdminObjectUsePermissionEffectiveTime:function(e,t){Base.submit(null,{url:r+"updateCustomResourceUsePermissionEffectiveTime",data:e},{successCallback:function(e){return t(e)}})},updateBatchAdminObjectUsePermissionEffectiveTime:function(e,t){Base.submit(null,{url:r+"updateBatchCustomResourceUsePermissionEffectiveTime",data:e},{successCallback:function(e){return t(e)}})},queryGrantSysPermission:function(e,t){Base.submit(null,{url:r+"queryRootResourceAuthority",data:e},{successCallback:function(e){return t(e)}})},queryGrantPermissionByResourceId:function(e,t){Base.submit(null,{url:r+"queryChildResourceAuthorityByRoleId",data:e},{successCallback:function(e){return t(e)}})},saveAdminGrantPermission:function(e,t){Base.submit(null,{url:r+"changeResourceAuthority",data:e},{successCallback:function(e){return t(e)}})},queryObjectGrantSysPermission:function(e,t){Base.submit(null,{url:r+"queryRootCustomResourceAuthority",data:e},{successCallback:function(e){return t(e)}})},queryObjectGrantPermissionByResourceId:function(e,t){Base.submit(null,{url:r+"queryChildCustomResourceAuthorityByRoleId",data:e},{successCallback:function(e){return t(e)}})},saveAdminObjectGrantPermission:function(e,t){Base.submit(null,{url:r+"changeCustomResourceAuthority",data:e},{successCallback:function(e){return t(e)}})},addBatchUserRole:function(e,t){Base.submit(null,{url:r+"addBatchUserRole",data:e},{successCallback:function(e){return t(e)}})},deleteBatchUserRole:function(e,t){Base.submit(null,{url:r+"deleteBatchUserRole",data:e},{successCallback:function(e){return t(e)}})},queryChildCustomResourceAsync:function(e,t){Base.submit(null,{url:r+"queryChildCustomResourceAsync",data:e},{successCallback:function(e){return t(e)}})},addAllRoleUsersByOrgId:function(e,t){Base.submit(null,{url:r+"addAllRoleUsersByOrgId",data:e},{successCallback:function(e){return t(e)}})}}}}]);
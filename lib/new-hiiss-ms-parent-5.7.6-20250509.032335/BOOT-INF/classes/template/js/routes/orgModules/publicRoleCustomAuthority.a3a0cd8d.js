"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[5578],{15720:function(e,t,a){a.r(t),a.d(t,{default:function(){return d}});var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"fit",attrs:{id:"publicRoleCustomAuthority"}},[a("ta-border-layout",{attrs:{layout:{header:"70px"}}},[a("div",{staticStyle:{"text-align":"center",overflow:"hidden"},attrs:{slot:"header"},slot:"header"},[a("ta-breadcrumb",{staticStyle:{"line-height":"42px",float:"left"},attrs:{separator:">"}},[a("ta-breadcrumb-item",[a("a",{on:{click:e.fnBack}},[e._v("角色维度")])]),a("ta-breadcrumb-item",[e._v("自定义对象权限管理")])],1),a("div",{staticClass:"divider-header"}),a("ta-alert",{staticStyle:{float:"left","margin-top":"2px"},attrs:{message:"当前角色为："+e.role.roleName,type:"info","show-icon":""}}),a("ta-button",{staticStyle:{float:"right","margin-top":"8px"},on:{click:e.fnBack}},[a("ta-icon",{attrs:{type:"rollback"}}),e._v("返回 ")],1)],1),a("ta-tabs",{staticClass:"fit content-box"},[a("ta-tab-pane",{attrs:{tab:"自定义对象权限"}},[a("ta-row",{staticClass:"fit"},[a("ta-col",{staticClass:"fit authority-box",staticStyle:{"border-right":"1px solid #eee",height:"100%"},attrs:{span:4}},[a("div",{staticClass:"menu-title"},[a("ta-icon",{attrs:{type:"menu-fold"}}),a("span",{staticStyle:{"margin-left":"5px"}},[e._v("自定义对象一级菜单")])],1),a("ta-divider",{staticStyle:{margin:"0"}}),a("ta-menu",{attrs:{mode:"inline","selected-keys":e.menuSelectedKeys},on:{click:e.onSelectMenu}},e._l(e.menuData,(function(t){return a("ta-menu-item",{key:t.id},[a("ta-icon",{attrs:{type:"appstore"}}),e._v(" "+e._s(t.name)+" ")],1)})),1)],1),a("ta-col",{staticClass:"right-box",attrs:{span:20}},[a("div",{staticClass:"fit",staticStyle:{border:"1px solid #e8e8e8"}},[a("div",{staticClass:"divider"}),a("ta-row",{staticStyle:{height:"5%"}},[a("ta-col",{staticClass:"col-header",attrs:{span:17}},[e._v(" 可授权的自定义对象 ")]),a("ta-col",{staticClass:"col-header",attrs:{span:7}},[e._v(" 有效期 ")])],1),a("ta-divider"),a("div",{staticClass:"authority-box"},[a("ta-input",{staticStyle:{width:"250px","margin-left":"10px"},attrs:{placeholder:"输入关键字进行过滤"},model:{value:e.filterText,callback:function(t){e.filterText=t},expression:"filterText"}}),a("ta-button",{staticClass:"button-save",attrs:{type:"primary"},on:{click:e.fnCustomAuthorityMgSave}},[e._v(" 权限保存 ")]),e.defaultCheckedList.length>0?a("ta-popover",{attrs:{placement:"bottom",trigger:"manual",value:e.batchPop}},[a("div",{staticClass:"pop-calendar"},[a("ta-calendar",{attrs:{fullscreen:!1},on:{change:e.onPanelChange}})],1),a("div",{staticStyle:{float:"right","margin-top":"10px"}},[a("ta-button",{attrs:{size:"small"},on:{click:function(t){e.batchPop=!1}}},[e._v(" 取消 ")]),a("ta-button",{attrs:{size:"small"},on:{click:function(t){return e.fnSaveCustomEffectiveTime(!0)}}},[e._v(" 设为永久 ")]),a("ta-button",{attrs:{type:"primary",size:"small"},on:{click:function(t){return e.fnSaveCustomEffectiveTime(!1)}}},[e._v(" 确定 ")])],1),a("ta-button",{staticStyle:{float:"right","margin-right":"10px"},attrs:{slot:"reference",size:"small"},on:{click:function(t){e.batchPop=!0}},slot:"reference"},[e._v(" 批量设置有效期 ")])],1):e._e(),e.authorityTree.length>0?a("ta-e-tree",{ref:"atree",attrs:{"show-checkbox":"",load:e.loadNode,"node-key":"id","highlight-current":"",props:e.defaultProps,"expand-on-click-node":!0,"filter-node-method":e.filterNode,"check-strictly":!0,lazy:""},on:{check:e.nodeCheck},scopedSlots:e._u([{key:"default",fn:function(t){var s=t.node,n=t.data;return a("span",{staticClass:"custom-tree-node"},[n.loginAccess?a("span",{staticStyle:{color:"#67c23a"}},[e._v(e._s(s.label)+" (登录即可访问)")]):e._e(),a("span",[e._v(e._s(s.label))]),n.checked&&!n.disabled?a("span",{staticClass:"node-right"},[e._v(" "+e._s(n.effectTime?n.effectTime.split(" ")[0]:"永久")+" "),n.checked?a("ta-popover",{attrs:{placement:"bottom",trigger:"manual",value:n.id==e.indexClick}},[a("div",{staticClass:"pop-calendar"},[a("ta-calendar",{attrs:{fullscreen:!1},on:{change:e.onPanelChange}})],1),a("div",{staticStyle:{float:"right","margin-top":"10px"}},[a("ta-button",{attrs:{size:"small"},on:{click:function(t){e.indexClick=null}}},[e._v("取消")]),a("ta-button",{attrs:{size:"small"},on:{click:function(t){return e.onCustomCellChange(s,n,!0)}}},[e._v("设为永久")]),a("ta-button",{attrs:{type:"primary",size:"small"},on:{click:function(t){return e.onCustomCellChange(s,n,!1)}}},[e._v("确定 ")])],1),a("ta-icon",{staticStyle:{"margin-left":"10px"},attrs:{slot:"reference",type:"edit"},on:{click:function(t){e.indexClick=n.id}},slot:"reference"})],1):e._e()],1):a("span")])}}],null,!1,1259408423)}):e._e()],1)],1)])],1)],1)],1)],1)],1)},n=[],i=a(89584),u=a(46981),r={name:"publicRoleCustomAuthority",data:function(){return{effectiveTime:"",role:{},defaultProps:{children:"children",label:"name",id:"id",isLeaf:"isLeaf"},indexClick:null,currentCheckedList:[],authorityTree:[],defaultCheckedList:[],menuData:[],rootId:"",popVisible:!1,batchPop:!1,filterText:"",menuSelectedKeys:[],menuSelectedNode:{},virtualTree:{},fullCheckList:[],halfCheckList:[],leafList:[],changed:!1,expandList:[],tmp:null}},watch:{filterText:function(e){this.$refs.atree.filter(e)}},activated:function(){this.$route.params.role instanceof Object?(this.role=this.$route.params.role||{},this.fnQueryCustomUsePermissionByRoleId()):this.$router.push({name:"publicRoleManager"}),this.batchPop=!1,this.indexClick=null},methods:{loadNode:function(e,t){var a=this,s=this.menuSelectedNode;if(0===e.level){var n=this.inArray(s.id,this.fullCheckList),r=this.inArray(s.id,this.halfCheckList);return s.idPath=[s.id],0==s.auth&&(s.disabled=!0),t([s]),void this.$nextTick((function(){var e=a.$refs.atree;if(r>=0){var t=e.getNode(s.id);t.isLeaf=s.isLeaf,t.indeterminate=!0}else n>=0?e.setChecked(s.id,!0,!1):e.setChecked(s.id,!1,!1)}))}var o=e.data.id;this.expandList.push(o);var c={roleId:this.role.roleId,resourceId:o},l=!1;u.Z.queryCustomRePermissionAsync(c,(function(s){var n=s.data.customRePermissions,u=e.data.idPath;n.forEach((function(e){0==e.auth&&(e.disabled=!0)})),t(n),n.forEach((function(t){var s=t.id,n=a.$refs.atree,r=n.getNode(s);r.data.idPath=[].concat((0,i.Z)(u),[r.data.id]);var o=a.inArray(s,a.leafList);r.isLeaf=o>=0,e.checked?(r.checked=!0,l=!0):e.indeterminate||(r.checked=!1,l=!0)})),l||(a.fullCheckList.forEach((function(e){var t=a.$refs.atree;t.setChecked(e,!0,!1)})),e.checked||a.halfCheckList.forEach((function(e){var t=a.$refs.atree,s=t.getNode(e);null!=s&&(s.indeterminate=!0)})))}))},inArray:function(e,t){for(var a=0;a<t.length;a++)if(e==t[a])return a;return-1},fnQueryCustomUsePermissionByRoleId:function(){var e=this;u.Z.queryCustomRePermissionByRoleId({roleId:this.role.roleId},(function(t){if(e.menuData=t.data.customRePermissions,e.menuData&&e.menuData.length>0){var a=e.menuData[0].id;e.rootId=a,e.menuSelectedKeys=[a],e.menuSelectedNode=e.menuData[0],e.fnQueryCustomUsePermissionByResourceId(a,0)}}))},fnQueryCustomUsePermissionByResourceId:function(e,t){var a=this;this.authorityTree=[],u.Z.queryCustomRePermissionByRoleId({roleId:this.role.roleId,resourceId:e},(function(e){a.fullCheckList=e.data.full,a.halfCheckList=e.data.half,a.leafList=e.data.leaf,a.defaultCheckedList=[].concat((0,i.Z)(a.fullCheckList),(0,i.Z)(a.halfCheckList)),a.$nextTick((function(){a.authorityTree=[a.menuSelectedNode],a.expandList=[],a.changed=!1}))}))},getResourceTreeParam:function(){var e=this,t=this.$refs.atree,a=t.getCheckedNodes(),s=[],n=t.getHalfCheckedNodes(),i=[],u=[];return n.forEach((function(e){t.getNode(e.id).expanded||u.push(e.id)})),a.forEach((function(t){t.isLeaf||e.inArray(t.id,e.expandList)>=0?s.push(t.id):i.push(t.id)})),{roleId:this.role.roleId,categoryId:this.rootId,resourceIds:s.join(","),needChildIds:i.join(","),halfNeedChildIds:u.join(",")}},nodeCheck:function(e,t,a){if(a){var s=e.idPath,n=this.$refs.atree,i=n.getNode(e.id),u=i.checked;if(u){this.changeCheckStatus(i,u);for(var r=s.length-2;r>=0;r--){var o=s[r],c=n.getNode(o);c.checked||(c.indeterminate=!0)}}else{this.changeCheckStatus(i,u);for(var l=function(e){var t=s[e],a=n.getNode(t),i=a.childNodes;a.checked=!1,a.indeterminate=!1,null!=i&&i.length>0&&i.forEach((function(e){(e.checked||e.indeterminate)&&(a.indeterminate=!0)}))},d=s.length-2;d>=0;d--)l(d)}this.changed=!1}},changeCheckStatus:function(e,t){var a=this;e.indeterminate=!1,e.checked=t;var s=e.childNodes;null!=s&&s.length>0&&s.forEach((function(e){a.changeCheckStatus(e,t)}))},filterNode:function(e,t){return!e||-1!==t.name.indexOf(e)},fnBack:function(){this.$router.push({name:"publicRoleManager"})},onPanelChange:function(e,t){this.effectiveTime=e.format("YYYY-MM-DD")},onSelectMenu:function(e){var t=this,a=e.item,s=e.key,n=e.keyPath;this.changed?this.$confirm({title:"提示",content:"当前自定义对象修改未保存，是否保存?",cancelText:"取消",okText:"确认",onOk:function(){t.rootId=s,t.fnCustomAuthorityMgSave(),t.menuSelectedKeys=n,t.menuSelectedNode=t.menuData[a.index]},onCancel:function(){t.rootId=s,t.menuSelectedKeys=n,t.menuSelectedNode=t.menuData[a.index],t.fnQueryCustomUsePermissionByResourceId(s)}}):(this.rootId=s,this.menuSelectedKeys=n,this.menuSelectedNode=this.menuData[a.index],this.fnQueryCustomUsePermissionByResourceId(s))},fnCreateCheckedCustomResourceIds:function(e,t){var a=this;e.children&&e.children.forEach((function(e){a.fnCreateCheckedCustomResourceIds(e,t)})),e.checked&&t.push(e.id)},fnSaveCustomEffectiveTime:function(e){var t=this,a=this.getResourceTreeParam();a.effectTime=e?null:this.effectiveTime,u.Z.updateBatchCustomResourceUsePermissionEffectiveTime(a,(function(e){t.$message.success("批量设置有效期成功"),u.Z.queryCustomRePermissionByRoleId({roleId:t.role.roleId},(function(e){if(t.menuData=e.data.customRePermissions,t.menuData&&t.menuData.length>0){var a=null;t.menuData.forEach((function(e){e.id==t.rootId&&(a=e)})),null==a&&(a=t.menuData[0]);var s=a.id;t.rootId=s,t.menuSelectedKeys=[s],t.menuSelectedNode=a,t.fnQueryCustomUsePermissionByResourceId(s,0)}})),t.batchPop=!1}))},onCustomCellChange:function(e,t,a){var s=this,n={roleId:this.role.roleId,resourceId:t.id,effectTime:a?null:this.effectiveTime};u.Z.updateCustomResourceUsePermissionEffectiveTime(n,(function(t){e.data.effectTime=n.effectTime,s.indexClick=null}))},fnCustomAuthorityMgSave:function(){var e=this,t=this.getResourceTreeParam();u.Z.addCustomResourceUsePermission(t,(function(t){e.$message.success("保存自定义对象权限成功"),u.Z.queryCustomRePermissionByRoleId({roleId:e.role.roleId},(function(t){if(e.menuData=t.data.customRePermissions,e.menuData&&e.menuData.length>0){var a=null;e.menuData.forEach((function(t){t.id==e.rootId&&(a=t)})),null==a&&(a=e.menuData[0]);var s=a.id;e.rootId=s,e.menuSelectedKeys=[s],e.menuSelectedNode=a,e.fnQueryCustomUsePermissionByResourceId(s,0)}}))}))}}},o=r,c=a(1001),l=(0,c.Z)(o,s,n,!1,null,"6ad8e519",null),d=l.exports},46981:function(e,t){var a="org/authority/roleAuthorityManagementRestService/";t["Z"]={queryCurrentAdminRoleWrapeOrgTree:function(e,t){Base.submit(null,{url:a+"queryCurrentAdminRoleWrapeOrgTree",data:e,showPageLoading:!1},{successCallback:function(e){return t(e)}})},queryRolesByOrgId:function(e,t){Base.submit(null,{url:a+"queryRolesByOrgId",data:e},{successCallback:function(e){return t(e)}})},queryAuthRole:function(e,t){Base.submit(null,{url:a+"queryAuthRole",data:e},{successCallback:function(e){return t(e)}})},copyResource:function(e,t,s){Base.submit(e,{url:a+"copyResource",data:t,autoValid:!0},{successCallback:function(e){return s(e)}}).then((function(e){})).catch((function(e){}))},queryUsePermissionByRoleId:function(e,t){Base.submit(null,{url:a+"queryUsePermissionByRoleId",data:e},{successCallback:function(e){return t(e)}})},queryCustomUsePermissionByRoleId:function(e,t){Base.submit(null,{url:a+"queryCustomUsePermissionByRoleId",data:e},{successCallback:function(e){return t(e)}})},copyRole:function(e,t,s){Base.submit(e,{url:a+"copyRole",data:t,autoValid:!0},{successCallback:function(e){return s(e)}}).then((function(e){})).catch((function(e){}))},queryRePermission:function(e,t){Base.submit(null,{url:a+"queryRePermission",data:e},{successCallback:function(e){return t(e)}})},queryCustomRePermission:function(e,t){Base.submit(null,{url:a+"queryCustomRePermission",data:e},{successCallback:function(e){return t(e)}})},addBatchUsePermissionByMoreRole:function(e,t){Base.submit(null,{url:a+"addBatchUsePermissionByMoreRole",data:e},{successCallback:function(e){return t(e)}})},deleteBatchUsePermissionByMoreRole:function(e,t){Base.submit(null,{url:a+"deleteBatchUsePermissionByMoreRole",data:e},{successCallback:function(e){return t(e)}})},addRole:function(e,t,s){Base.submit(e,{url:a+"addRole",data:t,autoValid:!0},{successCallback:function(e){return s(e)}}).then((function(e){})).catch((function(e){}))},updateRoleByRoleId:function(e,t,s){Base.submit(null,{url:a+"updateRoleByRoleId",data:t,autoValid:!0},{successCallback:function(e){return s(e)}}).then((function(e){})).catch((function(e){}))},updateBatchUsePermissionEffectiveTime:function(e,t){Base.submit(null,{url:a+"updateBatchUsePermissionEffectiveTime",data:e},{successCallback:function(e){return t(e)}})},updateUsePermissionEffectiveTime:function(e,t){Base.submit(null,{url:a+"updateUsePermissionEffectiveTime",data:e},{successCallback:function(e){return t(e)}})},addUsePermission:function(e,t){Base.submit(null,{url:a+"addUsePermission",data:e},{successCallback:function(e){return t(e)}})},changeRestAuthority:function(e,t){Base.submit(null,{url:a+"changeRestAuthority",data:e},{successCallback:function(e){return t(e)}})},queryRePermissionByRoleId:function(e,t){Base.submit(null,{url:a+"queryRePermissionByRoleId",data:e},{successCallback:function(e){return t(e)}})},updateBatchCustomResourceUsePermissionEffectiveTime:function(e,t){Base.submit(null,{url:a+"updateBatchCustomResourceUsePermissionEffectiveTime",data:e},{successCallback:function(e){return t(e)}})},updateCustomResourceUsePermissionEffectiveTime:function(e,t){Base.submit(null,{url:a+"updateCustomResourceUsePermissionEffectiveTime",data:e},{successCallback:function(e){return t(e)}})},queryCustomRePermissionByRoleId:function(e,t){Base.submit(null,{url:a+"queryCustomRePermissionByRoleId",data:e},{successCallback:function(e){return t(e)}})},addCustomResourceUsePermission:function(e,t){Base.submit(null,{url:a+"addCustomResourceUsePermission",data:e},{successCallback:function(e){return t(e)}})},updateRoleEffectiveByRoleId:function(e,t){Base.submit(null,{url:a+"updateRoleEffectiveByRoleId",data:e},{successCallback:function(e){return t(e)}})},deleteBatchRole:function(e,t){Base.submit(null,{url:a+"deleteBatchRole",data:e},{successCallback:function(e){return t(e)}})},deleteRoleUserByKey:function(e,t){Base.submit(null,{url:a+"deleteRoleUserByKey",data:e},{successCallback:function(e){return t(e)}})},deleteBatchRoleUsers:function(e,t){Base.submit(null,{url:a+"deleteBatchRoleUser",data:e},{successCallback:function(e){return t(e)}})},queryUsersByRoleId:function(e,t){Base.submit(null,{url:a+"queryUsersByRoleId",data:e},{successCallback:function(e){return t(e)}})},queryUsersByRoleIdNoPage:function(e,t){Base.submit(null,{url:a+"queryUsersByRoleIdNoPage",data:e},{successCallback:function(e){return t(e)}})},queryUsersNoWraperByRoleId:function(e,t){Base.submit(null,{url:a+"queryUsersNoWraperByRoleId",data:e},{successCallback:function(e){return t(e)}})},queryUsersNoWraperByRoleIdNoPage:function(e,t){Base.submit(null,{url:a+"queryUsersNoWraperByRoleIdNoPage",data:e},{successCallback:function(e){return t(e)}})},addBatchRoleUsers:function(e,t,s){Base.submit(null,{url:a+"addBatchRoleUsers",data:e,showPageLoading:!1},{successCallback:function(e){return t(e)},failCallback:function(e){return s(e)}})},deleteBatchRoleUser:function(e,t,s){Base.submit(null,{url:a+"deleteBatchRoleUser",data:e,showPageLoading:!1},{successCallback:function(e){return t(e)},failCallback:function(e){return s(e)}})},addBatchUserRole:function(e,t){Base.submit(null,{url:a+"addBatchUserRole",data:e},{successCallback:function(e){return t(e)}})},deleteBatchUserRole:function(e,t){Base.submit(null,{url:a+"deleteBatchUserRole",data:e},{successCallback:function(e){return t(e)}})},queryCustomRePermissionAsync:function(e,t){Base.submit(null,{url:a+"queryCustomRePermissionAsync",data:e},{successCallback:function(e){return t(e)}})},addAllRoleUsersByOrgId:function(e,t){Base.submit(null,{url:a+"addAllRoleUsersByOrgId",data:e},{successCallback:function(e){return t(e)}})}}}}]);
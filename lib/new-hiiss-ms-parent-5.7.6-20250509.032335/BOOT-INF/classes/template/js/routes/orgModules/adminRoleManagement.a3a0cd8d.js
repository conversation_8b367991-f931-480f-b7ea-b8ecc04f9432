"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[1327],{31503:function(e,t,a){a.r(t),a.d(t,{default:function(){return B}});var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"fit",attrs:{id:"roleManagement"}},[a("ta-border-layout",{staticStyle:{padding:"10px"},attrs:{"layout-type":"flexBorder"}},[a("div",{staticStyle:{"text-align":"center"},attrs:{slot:"header"},slot:"header"},[a("ta-input-search",{staticClass:"search-box",attrs:{placeholder:"请输入角色名称"},on:{search:e.onSearchRole},model:{value:e.searchInfo,callback:function(t){e.searchInfo=t},expression:"searchInfo"}},[a("ta-button",{attrs:{slot:"enterButton",type:"primary"},slot:"enterButton"},[e._v(" 搜索 ")])],1)],1),a("ta-border-layout",{attrs:{"layout-type":"flexBorder"}},[a("div",{staticStyle:{"line-height":"45px"},attrs:{slot:"header"},slot:"header"},[a("ta-tree-select",{staticStyle:{width:"250px","margin-right":"10px"},attrs:{"dropdown-style":{maxHeight:"300px",overflow:"auto"},placeholder:"请选择组织机构",url:e.casCaderOrgUrl,"tree-data":e.options,"tree-id":"orgVos","tree-node-id":"orgId","tree-data-label":"orgName","tree-node-label-prop":"namePath","tree-data-value":"orgId","allow-clear":"","search-placeholder":"请输入组织机构名称","show-search":"","filter-tree-node":e.filter,"loaded-data-call-back":e.fnLoadedOrgCallBack},on:{"update:treeData":function(t){e.options=t},"update:tree-data":function(t){e.options=t},change:e.fnQueryAdmin},model:{value:e.orgId,callback:function(t){e.orgId=t},expression:"orgId"}}),a("ta-checkbox",{attrs:{checked:e.includeSub},on:{change:e.onChangeIsSub}},[e._v(" 包含子组织 ")]),a("ta-tag-select",{attrs:{title:"状态",data:e.CollectionData("STATE")},on:{change:e.filterClick},model:{value:e.selectFilter,callback:function(t){e.selectFilter=t},expression:"selectFilter"}}),a("div",{staticStyle:{float:"right"}},[a("ta-button",{attrs:{type:"primary"},on:{click:e.fnAddAdmin}},[e._v(" 新增角色 ")]),a("ta-dropdown",{attrs:{trigger:e.clickTrigger}},[a("ta-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("ta-menu-item",{attrs:{disabled:!e.selectedRowKeys.length}},[a("ta-popconfirm",{attrs:{title:"确认启用所选管理员角色?","cancel-text":"取消","ok-text":"确认"},on:{confirm:function(t){return e.fnBatchPickAdmin(!1)}}},[a("ta-icon",{attrs:{type:"check-circle"}}),a("span",{staticClass:"mg-l12"},[e._v("启用")])],1)],1),a("ta-menu-divider"),a("ta-menu-item",{attrs:{disabled:!e.selectedRowKeys.length}},[a("ta-popconfirm",{attrs:{title:"确认禁用所选管理员角色?","cancel-text":"取消","ok-text":"确认"},on:{confirm:function(t){return e.fnBatchBanAdmin(!1)}}},[a("ta-icon",{attrs:{type:"stop"}}),a("span",{staticClass:"mg-l12"},[e._v("禁用")])],1)],1),a("ta-menu-divider"),a("ta-menu-item",{attrs:{disabled:!e.selectedRowKeys.length},on:{click:function(t){e.deleteVisible=!0}}},[a("ta-icon",{attrs:{type:"close-circle"}}),e._v(" 删除 ")],1)],1),a("ta-button",[e._v(" 批量操作 "),a("ta-icon",{attrs:{type:"down"}})],1)],1)],1)],1),a("ta-big-table",{ref:"adminRef",attrs:{stripe:"",resizable:"","highlight-hover-row":"",height:"auto","checkbox-config":{highlight:!0},data:e.adminData},on:{"checkbox-all":e.fnOnChange,"checkbox-change":e.fnOnChange}},[a("ta-big-table-column",{attrs:{type:"checkbox",width:"40",fixed:"left"}}),a("ta-big-table-column",{attrs:{field:"roleName",fixed:"left",width:"120",title:"角色名称",sortable:"","show-overflow":"tooltip"},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.row;return[a("span",{class:{invalidStyle:"0"==i.effective||"1"==i.expire}},[e._v(e._s(i.roleName))])]}}])}),a("ta-big-table-column",{attrs:{width:"120",field:"namePath",title:"组织路径","show-overflow":"ellipsis",sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.row;return[a("ta-tooltip",[a("span",{attrs:{slot:"title"},slot:"title"},[e._v(e._s(i.namePath))]),a("span",[e._v(e._s(e.getLastName(i.namePath)))])])]}}])}),a("ta-big-table-column",{attrs:{field:"roleDesc",title:"角色描述","min-width":"120","show-overflow":"tooltip"}}),a("ta-big-table-column",{attrs:{field:"effectiveTime",width:"120",title:"有效期",sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.row;return[a("span",{class:{invalidStyle:"1"==i.expire}},[e._v(" "+e._s(null==i.effectiveTime?"永久":e.Base.getMoment(i.effectiveTime).format("YYYY-MM-DD"))+" ")])]}}])}),a("ta-big-table-column",{attrs:{field:"expire",width:"120",title:"是否过期"},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.row;return["0"==i.expire?a("ta-tag",{staticClass:"no-cursor",attrs:{type:"success"}},[e._v(" 未过期 ")]):"1"==i.expire?a("ta-tag",{staticClass:"no-cursor",attrs:{type:"danger"}},[e._v(" 过期 ")]):e._e()]}}])}),a("ta-big-table-column",{attrs:{field:"effective",title:"状态",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.row;return[a("ta-tag",{staticClass:"no-cursor",attrs:{type:"1"==i.effective?"success":"danger"}},[e._v(" "+e._s(e.CollectionLabel("STATE",i.effective))+" ")])]}}])}),a("ta-big-table-column",{attrs:{fixed:"right",field:"operation",title:"角色操作",width:"200",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return a("span",{},[a("ta-table-operate",{attrs:{"operate-menu":e.operateMenu,"row-info":t}})],1)}}])}),a("ta-big-table-column",{attrs:{fixed:"right",field:"adminMg",title:"管理",width:"340",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.row;return["1"==i.effective?a("span",{staticClass:"step1"},[a("router-link",{attrs:{to:{path:"adminUserMg",query:{roleName:i.roleName,roleId:i.roleId}}}},[e._v(" 人员管理 ")])],1):a("span",{staticClass:"step1",attrs:{title:"禁用的管理员角色不允许进行人员管理"}},[a("span",{staticClass:"invalidStyle"},[e._v("人员管理")])]),a("ta-divider",{attrs:{type:"vertical"}}),"1"==i.effective?a("span",{staticClass:"step2"},[a("ta-dropdown",{attrs:{trigger:e.clickTrigger}},[a("a",{staticClass:"ant-dropdown-link"},[e._v("权限管理 "),a("ta-icon",{attrs:{type:"down"}})],1),a("ta-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("ta-menu-item",[a("a",{on:{click:function(t){return e.fnToPath("adminUseAuthority",i)}}},[e._v("功能使用权限")])]),a("ta-menu-item",[a("router-link",{attrs:{to:{path:"adminGrantAuthority",query:{roleName:i.roleName,roleId:i.roleId}}}},[e._v("授权权限")])],1),a("ta-menu-item",[a("router-link",{attrs:{to:{path:"adminOrgAuthority",query:{roleName:i.roleName,roleId:i.roleId}}}},[e._v("组织范围权限")])],1)],1)],1)],1):a("span",{staticClass:"step2",attrs:{title:"禁用的管理员角色不允许进行权限管理"}},[a("span",{staticClass:"ant-dropdown-link invalidStyle"},[e._v("权限管理 "),a("ta-icon",{attrs:{type:"down"}})],1)]),a("ta-divider",{attrs:{type:"vertical"}}),"1"==i.effective?a("span",{staticClass:"step3"},[a("ta-dropdown",{attrs:{trigger:e.clickTrigger}},[a("a",{staticClass:"ant-dropdown-link"},[e._v("自定义权限管理 "),a("ta-icon",{attrs:{type:"down"}})],1),a("ta-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("ta-menu-item",[a("router-link",{attrs:{to:{path:"adminObjectGrantAuthority",query:{roleName:i.roleName,roleId:i.roleId}}}},[e._v("自定义授权权限")])],1)],1)],1)],1):a("span",{staticClass:"step3",attrs:{title:"禁用的管理员角色不允许进行自定义权限管理"}},[a("span",{staticClass:"ant-dropdown-link invalidStyle"},[e._v("自定义权限管理 "),a("ta-icon",{attrs:{type:"down"}})],1)])]}}])})],1)],1),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("ta-pagination",{ref:"adminPager",staticStyle:{float:"right","margin-top":"10px"},attrs:{"show-size-changer":"","show-quick-jumper":"","data-source":e.adminData,"default-page-size":10,params:e.adminPageParams,url:e.adminTableUrl},on:{"update:dataSource":function(t){e.adminData=t},"update:data-source":function(t){e.adminData=t}}})],1)],1),a("ta-modal",{attrs:{visible:e.batch.visible,centered:!0,"destroy-on-close":!0,width:"800px",getContainer:e.fnGetContainer,"body-style":{height:"400px"}},on:{cancel:function(t){e.batch.visible=!1}}},[a("template",{slot:"title"},[a("div",{staticStyle:{"text-align":"center"}},[e._v(" "+e._s(e.batch.title)+" ")])]),a("template",{slot:"footer"},[a("div",{staticStyle:{"text-align":"center"}},[a("ta-button",{on:{click:function(t){e.batch.visible=!1}}},[e._v(" 取消 ")]),a("ta-button",{attrs:{type:"primary"},on:{click:e.fnSaveModalAuthority}},[e._v(" 保存 ")])],1)]),a("ta-row",{staticStyle:{width:"100%"},attrs:{gutter:10}},[a("ta-col",{attrs:{span:e.row.col.span}},[a("span",{staticClass:"title"},[e._v("使用权限")]),a("div",{staticClass:"filter"},[a("ta-input",{attrs:{placeholder:"请输入资源名称"},model:{value:e.adminLeftFilterText,callback:function(t){e.adminLeftFilterText=t},expression:"adminLeftFilterText"}})],1),a("div",{staticClass:"modalTreeStyle"},[a("ta-e-tree",{ref:"tree",attrs:{data:e.leftTreeData,props:e.modalDefaultProps,"filter-node-method":e.filterNode,"default-checked-keys":e.leftDefaultKeys,"show-checkbox":"","highlight-current":"","node-key":"resourceId"}})],1)]),a("ta-col",{attrs:{span:e.row.col.span}},[a("span",{staticClass:"title"},[e._v("对象使用权限")]),a("div",{staticClass:"filter"},[a("ta-input",{attrs:{placeholder:"请输入资源名称"},model:{value:e.adminRightFilterText,callback:function(t){e.adminRightFilterText=t},expression:"adminRightFilterText"}})],1),a("div",{staticClass:"modalTreeStyle"},[a("ta-e-tree",{ref:"rtree",attrs:{data:e.rightTreeData,props:e.modalDefaultProps,"filter-node-method":e.filterNode,"default-checked-keys":e.rightDefaultKeys,"show-checkbox":"","highlight-current":"","node-key":"resourceId"}})],1)])],1)],2),a("ta-drawer",{attrs:{"destroy-on-close":"",width:"500",title:e.adminDrawerTitle,placement:"right",closable:"",visible:e.adminDrawerVisible,"footer-height":"","get-container":e.fnGetContainer},on:{close:e.fnCloseAdminDrawer}},[a("div",{attrs:{slot:"footer"},slot:"footer"},[a("ta-button-group",[a("ta-button",{on:{click:e.fnResetForm}},[e._v(" 重置 ")]),a("ta-button",{attrs:{type:"primary"},on:{click:e.fnAddAdminInfo}},[e._v(" 保存 ")])],1)],1),a("add-admin",{ref:"addAdmin",attrs:{admin:e.adminItem},on:{closeAdminDrawer:e.fnCloseAdminDrawer}})],1),a("ta-careful-delete",{attrs:{visible:e.deleteVisible,title:"管理员角色删除",description:"所选管理员角色"},on:{close:function(t){e.deleteVisible=!1},delete:function(t){return e.fnDeleteBatchAdmin(!1)}}})],1)},n=[],r=a(16158),s=a(36797),o=a.n(s),l=function(){var e=this,t=this,a=t.$createElement,i=t._self._c||a;return i("div",[i("ta-form",{attrs:{"auto-form-create":function(t){e.form=t}}},[i("ta-form-item",{attrs:{label:"角色名称","label-col":t.formItemLayout.labelCol,"wrapper-col":t.formItemLayout.wrapperCol,"field-decorator-id":"roleName","init-value":t.formData.roleName,"field-decorator-options":{rules:[{required:!0,message:"角色名称不能为空"},,{max:50,message:"角色名称最大长度为50"}]}}},[i("ta-input",{attrs:{placeholder:"请输入角色名称"}})],1),i("ta-form-item",{attrs:{label:"所属组织","label-col":t.formItemLayout.labelCol,"wrapper-col":t.formItemLayout.wrapperCol,"field-decorator-id":"orgId",require:{message:"请选择所属组织"},"field-decorator-options":{initialValue:t.formData.idPath}}},[i("project-search-tree",{attrs:{"get-container-id":"roleManagement","init-value":t.formData.namePath}})],1),i("ta-form-item",{attrs:{label:"有效期","label-col":t.formItemLayout.labelCol,"wrapper-col":t.formItemLayout.wrapperCol,"init-value":t.formData.effectiveTime,"field-decorator-id":"effectiveTime"}},[i("ta-date-picker",{staticStyle:{width:"100%"},attrs:{format:"YYYY-MM-DD","allow-clear":"",placeholder:"请选择有效时间","disabled-date":t.disabledDate}})],1),i("ta-form-item",{attrs:{label:"有效标识","label-col":t.formItemLayout.labelCol,"wrapper-col":t.formItemLayout.wrapperCol,"field-decorator-id":"effective","init-value":t.formData.effective,"field-decorator-options":{valuePropName:"checked"}}},[i("ta-switch",{attrs:{"checked-children":"启用","un-checked-children":"禁用"}})],1),i("ta-form-item",{attrs:{label:"角色描述","label-col":t.formItemLayout.labelCol,"wrapper-col":t.formItemLayout.wrapperCol,"field-decorator-id":"roleDesc","init-value":t.formData.roleDesc,"field-decorator-options":{rules:[{max:50,message:"角色描述最大长度为50"}]}}},[i("ta-textarea",{attrs:{rows:4,placeholder:"角色描述"}})],1)],1)],1)},c=[],u=a(95082),d={labelCol:{span:6},wrapperCol:{span:18}},f={name:"addAdmin",props:["admin"],data:function(){return{casCaderOrgUrl:"org/authority/adminAuthorityManagementRestService/queryCurrentAdminRoleWrapOrgTree",options:[],formData:{},formItemLayout:d}},mounted:function(){var e=this.admin,t=e.type,a=e.roleObj;"edit"===t?(this.formData=(0,u.Z)({},a),this.formData.idPath=a.orgId,this.formData.namePath=a.namePath,this.formData.effective="1"===a.effective,this.formData.roleDesc=a.roleDesc||"",this.formData.effectiveTime=a.effectiveTime?o()(a.effectiveTime,"YYYY-MM-DD"):null):this.$set(this.formData,"effective",!0)},methods:{moment:o(),fnResetForm:function(){this.fnInitForm()},fnAddAdminInfo:function(){var e=this;this.form.validateFields((function(t,a){if(!t){var i=e,n=e.admin,s=n.type,o=n.roleObj,l=i.form.getFieldsValue();l.effective=l.effective?"1":"0";var c=i.form.getFieldValue("orgId"),u=!c||(null!==c&&void 0!==c?c:null);"edit"!==s||u||(u=o.orgId),l.orgId=u;var d=i.form.getFieldValue("effectiveTime");l.effectiveTime=d?d.format("YYYY-MM-DD")+" 23:59:59":null,"add"===s?r.Z.addAdminRole(l,(function(e){i.$message.success("新增角色成功"),i.$emit("closeAdminDrawer")})):(l.roleType=o.roleType,l.roleId=o.roleId,r.Z.updateAdmin(l,(function(e){i.$message.success("修改角色成功"),i.$emit("closeAdminDrawer",l)})))}}))},fnInitForm:function(){this.form.resetFields()},disabledDate:function(e){return e&&e<o()().endOf("day")},filter:function(e,t){return t.some((function(t){return t.label.toLowerCase().indexOf(e.toLowerCase())>-1}))},onChange:function(e,t){var a=this;t&&"0"===t[t.length-1].isAuthority&&(this.$message.error("没有权限在该组织下操作角色"),this.$nextTick((function(){a.form.resetFields("orgId")})))}}},m=f,h=a(1001),p=(0,h.Z)(m,l,c,!1,null,null,null),b=p.exports,g=a(80790),y=a(51097),v=[{title:"角色名称",width:120,dataIndex:"roleName",overflowTooltip:!0,scopedSlots:{customRender:"customOrgName"}},{title:"组织路径",width:120,dataIndex:"namePath",overflowTooltip:"namePath",scopedSlots:{customRender:"namePath"}},{title:"角色描述",width:120,dataIndex:"roleDesc",overflowTooltip:!0},{title:"有效期",width:120,dataIndex:"effecttime",scopedSlots:{customRender:"effecttime"}},{title:"是否过期",width:120,dataIndex:"expire",scopedSlots:{customRender:"expire"}},{title:"状态",width:120,dataIndex:"effective",yesOrNoTag:!0,scopedSlots:{customRender:"effective"}},{title:"角色操作",dataIndex:"operation",width:150,align:"center",scopedSlots:{customRender:"action"}},{title:"管理",dataIndex:"adminMg",width:340,align:"center",scopedSlots:{customRender:"adminMg"}}],C={name:"adminRoleManagement",components:{AddAdmin:b},mixins:[g.Z],data:function(){var e=this;return{searchInfo:"",clickTrigger:["click"],selectFilter:[],casCaderOrgUrl:"org/authority/adminAuthorityManagementRestService/queryCurrentAdminRoleWrapOrgTree",adminTableUrl:"org/authority/adminAuthorityManagementRestService/queryAdminRoleByOrgId",options:[],orgId:void 0,includeSub:!0,adminDrawerTitle:"",adminLeftFilterText:"",adminRightFilterText:"",leftTreeData:[],rightTreeData:[],leftDefaultKeys:[],rightDefaultKeys:[],modalDefaultProps:{label:"name"},adminColumns:v,operateMenu:[{name:"编辑",disabled:function(e){return"0"===e.effective},title:function(e){return"0"===e.effective?"禁用的管理员角色不允许进行编辑":""},onClick:function(t){e.fnEditAdmin(t)}},{name:"更多",type:"more",moreMenuList:[{name:"启用",type:"confirm",confirmTitle:"确认启用该管理员角色?",onOk:function(t){e.fnBatchPickAdmin(t)}},{name:"禁用",type:"confirm",confirmTitle:"确认禁用该管理员角色?",onOk:function(t){e.fnBatchBanAdmin(t)}},{name:"删除",type:"confirm",confirmTitle:"确认删除该管理员角色?",onOk:function(t){e.fnDeleteBatchAdmin(t)}}]}],adminData:[],selectedRows:[],selectedRowKeys:[],adminDrawerVisible:!1,adminItem:{},batch:{title:"",visible:!1,type:"grant"},arrayData:{},isDetailShow:!1,row:{col:{span:12}},deleteVisible:!1,currentTime:""}},watch:{adminLeftFilterText:function(e){this.$refs.tree.filter(e)},adminRightFilterText:function(e){this.$refs.rtree.filter(e)}},mounted:function(){this.currentTime=o()().format("YYYY-MM-DD HH:MM:SS");var e=[{element:".step1",popover:{title:"人员管理",description:"为当前管理员角色进行人员绑定，绑定的人员拥有当前管理员角色权限",position:"left"}},{element:".step2",popover:{title:"权限管理",description:"【功能使用权限】在菜单功能的粒度上对当前管理员角色进行授权<br/>【授权权限】如果当前角色是再授权角色，通过此功能划定当前角色能够对下级角色授权功能的范围<br/>【组织范围权限】划定当前角色的权限在哪个组织层级下生效",position:"left"}},{element:".step3",popover:{title:"自定义授权权限",description:"划定当前角色能够对下级角色授权的自定义对象权限范围",position:"left"}}];this.fnCommonGuide(e)},methods:{fnGetContainer:function(){return document.getElementById("roleManagement")},filter:function(e,t){var a=e.toLowerCase(),i=null;return i=(0,y.Z)([t],(function(e){return e.label.toLowerCase().indexOf(a)>-1})),i&&i.length>0},fnOnChange:function(e){var t=e.records;this.selectedRowKeys=t,this.selectedRows=t},moment:o(),onSearchRole:function(e){this.fnLoadDefaultAdmin()},filterClick:function(){this.fnLoadDefaultAdmin()},getLastName:function(e){return e&&-1!==e.indexOf("/")?e.slice(e.lastIndexOf("/")+1):e},fnAddAdminInfo:function(){this.$refs.addAdmin.fnAddAdminInfo()},fnResetForm:function(){this.$refs.addAdmin.fnResetForm()},fnCloseAdminDrawer:function(){this.adminDrawerVisible=!1,this.fnLoadDefaultAdmin()},fnToPath:function(e,t){this.$router.push({path:e,query:{roleName:t.roleName,roleId:t.roleId}})},fnSaveModalAuthority:function(){var e,t,a=this;"grant"===this.batch.type?(e=this.$refs.tree.getCheckedNodes(!1,!0).map((function(e){return e.resourceId})),t=this.$refs.rtree.getCheckedNodes(!1,!0).map((function(e){return e.resourceId}))):(e=this.$refs.tree.getCheckedKeys(),t=this.$refs.rtree.getCheckedKeys());var i={isAdd:"grant"===this.batch.type?"1":"0",roles:JSON.stringify(this.selectedRows),resourceIds:JSON.stringify(e),objectResourceIds:JSON.stringify(t)};r.Z.batchChangeAdminPermission(i,(function(e){a.$message.success("操作成功"),a.batch.visible=!1}))},filterNode:function(e,t,a){return!e||-1!==t.name.indexOf(e)},adminPageParams:function(){var e=this.orgId,t=null!==e&&void 0!==e?e:this.options[0].orgId,a=this.selectFilter;return a=1===a.length?a.join(","):null,{roleName:this.searchInfo,effective:a,orgId:t,isSub:this.includeSub}},fnLoadDefaultAdmin:function(){this.$refs.adminPager.loadData()},onChangeIsSub:function(e){this.includeSub=e.target.checked,this.fnLoadDefaultAdmin()},fnLoadedOrgCallBack:function(e){this.options=e,this.fnLoadDefaultAdmin()},fnQueryAdmin:function(e){this.orgId=e,this.fnLoadDefaultAdmin()},fnLoadModalDefault:function(){var e=this;this.batch.visible=!0,r.Z.queryBatchPermissionTreeData(null,(function(t){e.leftTreeData=t.data.batchPermissionTree,e.rightTreeData=t.data.batchCustomPermissionTree}))},fnGrantAuthority:function(){0!==this.selectedRows.length?(this.batch.title="授予使用权限",this.batch.type="grant",this.fnLoadModalDefault()):this.$message.warning("请先选择数据")},fnRevokeAuthority:function(){0!==this.selectedRows.length?(this.batch.title="回收使用权限",this.batch.type="revoke",this.fnLoadModalDefault()):this.$message.warning("请先选择数据")},fnEditAdmin:function(e){this.adminDrawerTitle="修改管理员角色",this.adminDrawerVisible=!0,this.adminItem.roleObj=e,this.adminItem.type="edit"},fnDeleteBatchAdmin:function(e){var t=this,a=[];e?a.push(e.roleId):a=this.selectedRows.map((function(e){return e.roleId}));var i={roleIds:a.join(",")};r.Z.deleteBatchAdmin(i,(function(e){t.$message.success("移除管理员角色成功"),t.deleteVisible=!1,t.fnLoadDefaultAdmin()}))},fnAddAdmin:function(){this.adminDrawerTitle="新增管理员角色",this.adminDrawerVisible=!0,this.adminItem={},this.adminItem.type="add"},fnBatchPickAdmin:function(e){var t=this,a=[];if(e){if("1"===e.effective)return void this.$message.warning("该记录已经启用，请勿重复操作！");a.push(e.roleId)}else{var i=this.selectedRows.filter((function(e){return"0"===e.effective}));if(0===i.length)return void this.$message.warning("所选记录已经全部启用，请勿重复操作！");a=i.map((function(e){return e.roleId}))}var n={roleIds:a.join(","),effective:"1"};r.Z.updateBatchAdminStatus(n,(function(e){t.$message.success("更新数据成功"),t.fnQueryAdmin()}))},fnBatchBanAdmin:function(e){var t=this,a=[];if(e){if("0"===e.effective)return void this.$message.warning("该记录已经禁用，请勿重复操作！");a.push(e.roleId)}else{var i=this.selectedRows.filter((function(e){return"1"===e.effective}));if(0===i.length)return void this.$message.warning("所选记录已经全部禁用，请勿重复操作！");a=i.map((function(e){return e.roleId}))}var n={roleIds:a.join(","),effective:"0"};r.Z.updateBatchAdminStatus(n,(function(e){t.$message.success("更新数据成功"),t.fnQueryAdmin()}))}}},w=C,k=(0,h.Z)(w,i,n,!1,null,"5fb8bd88",null),B=k.exports},80790:function(e,t,a){var i=a(76698);a(34530);t["Z"]={activated:function(){var e=window.pageVmObj,t=e["steps_"+e._route.name];this.methods?this.methods.fnCommonGuide(t):this.fnCommonGuide(t)},deactivated:function(){this.methods?this.methods.fnCommonGuide([],!0):this.fnCommonGuide([],!0)},methods:{fnCommonGuide:function(e,t){var a=window.pageVmObj;t?(a.driver.reset(),window.fnPageGuide=null):(a["steps_"+a._route.name]=e,a.driver=new i.Z({allowClose:!1}),window.fnPageGuide=function(){a.driver.defineSteps(e),a.driver.start()})}}}},16158:function(e,t){var a="/org/authority/adminAuthorityManagementRestService/";t["Z"]={queryUsersByRoleId:function(e,t){Base.submit(null,{url:a+"queryUsersByRoleId",data:e},{successCallback:function(e){return t(e)}})},queryUsersByRoleIdNoPage:function(e,t){Base.submit(null,{url:a+"queryUsersByRoleIdNoPage",data:e},{successCallback:function(e){return t(e)}})},addBatchAdminUser:function(e,t){Base.submit(null,{url:a+"addBatchRoleUsers",data:e},{successCallback:function(e){return t(e)}})},deleteBatchAdmin:function(e,t){Base.submit(null,{url:a+"deleteBatchRole",data:e},{successCallback:function(e){return t(e)}})},queryAllTaOrg:function(e,t){Base.submit(null,{url:a+"queryCurrentAdminRoleWrapOrgTree",data:e},{successCallback:function(e){return t(e)}})},deleteRoleUserByKey:function(e,t){Base.submit(null,{url:a+"deleteRoleUserByKey",data:e},{successCallback:function(e){return t(e)}})},deleteBatchRoleUsers:function(e,t){Base.submit(null,{url:a+"deleteBatchRoleUsers",data:e},{successCallback:function(e){return t(e)}})},queryBatchUserByOrgId:function(e,t){Base.submit(null,{url:a+"queryUsersNoWraperByRoleId",data:e},{successCallback:function(e){return t(e)}})},queryBatchUserByOrgIdNoPage:function(e,t){Base.submit(null,{url:a+"queryUsersNoWraperByRoleIdNoPage",data:e},{successCallback:function(e){return t(e)}})},queryOrgPermission:function(e,t){Base.submit(null,{url:a+"selectOrgScope",data:e},{successCallback:function(e){return t(e)}})},removeOrgPermission:function(e,t){Base.submit(null,{url:a+"removeOrgScope",data:e},{successCallback:function(e){return t(e)}})},selectPermissionOrgScope:function(e,t){Base.submit(null,{url:a+"selectPermissionOrgScope",data:e},{successCallback:function(e){return t(e)}})},addOrgPermission:function(e,t){Base.submit(null,{url:a+"saveOrgScope",data:e},{successCallback:function(e){return t(e)}})},queryBatchPermissionTreeData:function(e,t){Base.submit(null,{url:a+"queryBatchPermissionTreeData",data:e},{successCallback:function(e){return t(e)}})},batchChangeAdminPermission:function(e,t){Base.submit(null,{url:a+"batchChangeAdminPermission",data:e},{successCallback:function(e){return t(e)}})},addAdminRole:function(e,t){Base.submit(null,{url:a+"addAdminRole",data:e},{successCallback:function(e){return t(e)}})},updateAdmin:function(e,t){Base.submit(null,{url:a+"updateAdmin",data:e},{successCallback:function(e){return t(e)}})},updateBatchAdminStatus:function(e,t){Base.submit(null,{url:a+"updateRoleEffectiveByRoleId",data:e},{successCallback:function(e){return t(e)}})},queryUseSysPermission:function(e,t){Base.submit(null,{url:a+"queryRootResource",data:e},{successCallback:function(e){return t(e)}})},queryUsePermissionByResourceId:function(e,t){Base.submit(null,{url:a+"queryChildResourceByRoleId",data:e},{successCallback:function(e){return t(e)}})},saveAdminUsePermission:function(e,t){Base.submit(null,{url:a+"changeResourceUsePermission",data:e},{successCallback:function(e){return t(e)}})},updateAdminUsePermissionEffectiveTime:function(e,t){Base.submit(null,{url:a+"updateUsePermissionEffectiveTime",data:e},{successCallback:function(e){return t(e)}})},saveBatchAdminUsePermissionEffectiveTime:function(e,t){Base.submit(null,{url:a+"updateBatchUsePermissionEffectiveTime",data:e},{successCallback:function(e){return t(e)}})},queryObjectUseSysPermission:function(e,t){Base.submit(null,{url:a+"queryRootCustomResource",data:e},{successCallback:function(e){return t(e)}})},queryObjectUsePermissionByResourceId:function(e,t){Base.submit(null,{url:a+"queryChildCustomResourceByRoleId",data:e},{successCallback:function(e){return t(e)}})},saveAdminObjectUsePermission:function(e,t){Base.submit(null,{url:a+"changeCustomResourceUsePermission",data:e},{successCallback:function(e){return t(e)}})},updateAdminObjectUsePermissionEffectiveTime:function(e,t){Base.submit(null,{url:a+"updateCustomResourceUsePermissionEffectiveTime",data:e},{successCallback:function(e){return t(e)}})},updateBatchAdminObjectUsePermissionEffectiveTime:function(e,t){Base.submit(null,{url:a+"updateBatchCustomResourceUsePermissionEffectiveTime",data:e},{successCallback:function(e){return t(e)}})},queryGrantSysPermission:function(e,t){Base.submit(null,{url:a+"queryRootResourceAuthority",data:e},{successCallback:function(e){return t(e)}})},queryGrantPermissionByResourceId:function(e,t){Base.submit(null,{url:a+"queryChildResourceAuthorityByRoleId",data:e},{successCallback:function(e){return t(e)}})},saveAdminGrantPermission:function(e,t){Base.submit(null,{url:a+"changeResourceAuthority",data:e},{successCallback:function(e){return t(e)}})},queryObjectGrantSysPermission:function(e,t){Base.submit(null,{url:a+"queryRootCustomResourceAuthority",data:e},{successCallback:function(e){return t(e)}})},queryObjectGrantPermissionByResourceId:function(e,t){Base.submit(null,{url:a+"queryChildCustomResourceAuthorityByRoleId",data:e},{successCallback:function(e){return t(e)}})},saveAdminObjectGrantPermission:function(e,t){Base.submit(null,{url:a+"changeCustomResourceAuthority",data:e},{successCallback:function(e){return t(e)}})},addBatchUserRole:function(e,t){Base.submit(null,{url:a+"addBatchUserRole",data:e},{successCallback:function(e){return t(e)}})},deleteBatchUserRole:function(e,t){Base.submit(null,{url:a+"deleteBatchUserRole",data:e},{successCallback:function(e){return t(e)}})},queryChildCustomResourceAsync:function(e,t){Base.submit(null,{url:a+"queryChildCustomResourceAsync",data:e},{successCallback:function(e){return t(e)}})},addAllRoleUsersByOrgId:function(e,t){Base.submit(null,{url:a+"addAllRoleUsersByOrgId",data:e},{successCallback:function(e){return t(e)}})}}}}]);
"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[4704],{75249:function(e,t,r){r.r(t),r.d(t,{default:function(){return F}});var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{ref:"customOrg",staticClass:"fit",attrs:{id:"customOrgManagement"}},[r("ta-border-layout",{attrs:{layout:{left:"300px"},"center-cfg":{layoutConStyle:{padding:0,border:0}}}},[r("div",{attrs:{slot:"left"},slot:"left"},[r("ta-card",{staticStyle:{width:"100%"},attrs:{bordered:!1,"body-style":{padding:0}}},[r("div",{attrs:{slot:"title"},slot:"title"},[e._v(" 自定义组织列表 "),r("div",{staticStyle:{float:"right"}},[r("ta-button",{staticClass:"step1",staticStyle:{"font-size":"12px"},attrs:{size:"small",icon:"edit"},on:{click:function(t){e.customOrgTypeNameVisible=!0}}},[e._v(" 组织类别管理 ")])],1)]),e._l(e.orgTypeNameList,(function(t,a){return r("a",{key:t.customOrgTypeNameId,attrs:{value:t.customOrgTypeNameId},on:{click:function(r){return e.fnHandleOrgTypeNameChange(t.customOrgTypeNameId,a)}}},[r("div",{staticClass:"left-item",class:{activeClass:a==e.clickIndex}},[e._v(e._s(t.customOrgTypeName))])])}))],2)],1),r("ta-border-layout",{attrs:{layout:{header:"70px"},"show-border":!1,"center-cfg":{showBar:!0,toolBarStyle:{height:"50px",lineHeight:"48px"}}}},[r("div",{staticClass:"center-box",attrs:{slot:"header"},slot:"header"},[r("ta-input-search",{staticClass:"search-box",attrs:{placeholder:"请输入组织名称,自定义编码查询"},on:{search:e.fnQueryOrg},model:{value:e.validId,callback:function(t){e.validId=t},expression:"validId"}},[r("ta-button",{attrs:{slot:"enterButton",type:"primary"},slot:"enterButton"},[e._v(" 搜索 ")])],1)],1),r("div",{attrs:{slot:"centerExtraContent"},slot:"centerExtraContent"},[r("ta-tag-select",{attrs:{title:"有效性",data:e.CollectionData("EFFECTIVE")},on:{change:e.filterClick},model:{value:e.selectFilter,callback:function(t){e.selectFilter=t},expression:"selectFilter"}}),r("div",{staticStyle:{float:"right"}},[e.showAddTop?r("ta-button",{staticClass:"step2",attrs:{type:"primary"},on:{click:e.fnAddTopOrg}},[e._v(" 新增组织 ")]):e._e(),r("ta-dropdown",{attrs:{trigger:["click"]}},[r("ta-menu",{attrs:{slot:"overlay"},slot:"overlay"},[r("ta-menu-item",{attrs:{disabled:!e.selectedRowKeys.length}},[r("ta-popconfirm",{attrs:{title:"确认启用所选账户?","cancel-text":"取消","ok-text":"确认"},on:{confirm:function(t){return e.fnBatchPickOrg(!1)}}},[r("ta-icon",{attrs:{type:"check-circle"}}),r("span",{staticClass:"mg-l12"},[e._v("启用")])],1)],1),r("ta-menu-divider"),r("ta-menu-item",{attrs:{disabled:!e.selectedRowKeys.length}},[r("ta-popconfirm",{attrs:{title:"确认禁用所选账户?","cancel-text":"取消","ok-text":"确认"},on:{confirm:function(t){return e.fnBatchBanOrg(!1)}}},[r("ta-icon",{attrs:{type:"stop"}}),r("span",{staticClass:"mg-l12"},[e._v("禁用")])],1)],1),r("ta-menu-divider"),r("ta-menu-item",{attrs:{disabled:!e.selectedRowKeys.length},on:{click:function(t){0===e.selectedRows.length?e.$message.warning("请先选择数据"):e.batchDeleteVisible=!0}}},[r("ta-icon",{attrs:{type:"close-circle"}}),e._v(" 删除 ")],1)],1),r("ta-button",[e._v(" 批量操作 "),r("ta-icon",{attrs:{type:"down"}})],1)],1)],1)],1),r("ta-table",{ref:"customOrgTableRef",attrs:{columns:e.orgColumns,scroll:{y:"100%"},"data-source":e.orgData,"row-selection":{selectedRowKeys:e.selectedRowKeys,onChange:e.fnOnChange},"row-key":"customOrgId",pagination:!1,"default-expanded-row-keys":e.currentExpand,"expanded-row-keys":e.currentExpand},on:{expand:e.fnLoadSubTreeData},scopedSlots:e._u([{key:"customOrgName",fn:function(t,a){return r("span",{class:{invalidStyle:"0"==a.effective}},[e._v(e._s(t))])}},{key:"updateTime",fn:function(t,a){return r("span",{},[e._v(e._s(e.moment(a.updateTime).format("YYYY-MM-DD")))])}},{key:"effective",fn:function(t){return r("span",{},[e._v(e._s(e.CollectionLabel("EFFECTIVE",t)))])}},{key:"action",fn:function(t,a){return r("span",{},[r("span",{staticStyle:{display:"inline-block"},attrs:{title:"0"===a.effective?"禁用的自定义组织不允许进行人员管理":""}},[r("router-link",{class:{invalidStyle:"0"==a.effective},attrs:{to:{path:"customOrgUser",query:{customOrgId:a.customOrgId,customOrgPath:a.customOrgPath}},disabled:"0"===a.effective}},[e._v(" 人员管理 ")])],1),r("ta-divider",{attrs:{type:"vertical"}}),r("ta-table-operate",{staticStyle:{display:"inline-block"},attrs:{"operate-menu":e.operateMenu}})],1)}}])})],1)],1),e.customOrg.visible?r("ta-drawer",{attrs:{"destroy-on-close":"",title:e.customOrg.orgTitle,width:"500",placement:"right",visible:e.customOrg.visible,"get-container":!1},on:{close:function(t){e.customOrg.visible=!1}}},[r("div",{attrs:{slot:"footer"},slot:"footer"},[r("ta-button-group",[r("ta-button",{on:{click:e.fnResetForm}},[e._v(" 重置 ")]),r("ta-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.fnUpdateOrSaveOrg}},[e._v(" 保存 ")])],1)],1),r("add-custom-org",{ref:"customOrgChild",attrs:{org:e.org},on:{closeCustomOrgDrawer:e.drawerClose}})],1):e._e(),r("ta-careful-delete",{attrs:{visible:e.batchDeleteVisible,title:"自定义组织删除",description:"选中自定义组织"},on:{close:function(t){e.batchDeleteVisible=!1},delete:function(t){return e.fnBatchDeleteOrg(!1)}}}),r("ta-modal",{attrs:{width:"80%","destroy-on-close":!0,title:"自定义组织类别管理",visible:e.customOrgTypeNameVisible,centered:!0,"get-container":e.setContainer,"body-style":{height:"400px"},footer:null},on:{cancel:e.fnCloseCustomOrgTypeNameModal}},[r("add-custom-org-type-name")],1)],1)},s=[],o=(r(26602),r(71012),r(1888)),n=r(36797),i=r.n(n),l=r(80790),u=function(){var e=this,t=this,r=t.$createElement,a=t._self._c||r;return a("div",[a("ta-form",{attrs:{layout:"horizontal",autoFormCreate:function(t){e.form=t}}},[a("ta-form-item",{attrs:{label:"组织分类",fieldDecoratorId:"customOrgTypeNameId",initValue:t.org.customOrgTypeNameId,fieldDecoratorOptions:{rules:[{required:!0,message:"组织分类不能为空"}]}}},[a("ta-select",{attrs:{showSearch:"",allowClear:"",notFoundContent:"无数据显示",placeholder:"请选择组织类别",optionFilterProp:"children",disabled:""}},t._l(t.orgTypeNameList,(function(e,r){return a("ta-select-option",{key:r,attrs:{value:e.customOrgTypeNameId}},[t._v(" "+t._s(e.customOrgTypeName)+" ")])})),1)],1),"addTop"!=this.org.type&&"0"!=this.org.parentId?a("ta-form-item",{attrs:{label:"父级组织",fieldDecoratorId:"customOrgPath",initValue:t.customOrgPath}},[a("ta-input",{attrs:{disabled:"addTop"!=this.org.type}})],1):t._e(),a("ta-form-item",{attrs:{label:"组织名称",fieldDecoratorId:"customOrgName",initValue:"edit"==this.org.type?t.org.customOrgName:"",fieldDecoratorOptions:{rules:[{required:!0,whitespace:!0,message:"组织名称不能为空"}]}}},[a("project-search-tree",{attrs:{getContainerId:"customOrgManagement",allowInput:""}})],1),a("ta-form-item",{attrs:{label:"自定义编码",initValue:"edit"==this.org.type?t.org.customCode:"",fieldDecoratorId:"customCode",fieldDecoratorOptions:{rules:[{pattern:/^[\u4E00-\u9FA5A-Za-z0-9_/\\-]+$/,message:"只能输入中文英文数字、下划线、横杠及斜杠"}]}}},[a("ta-input",{attrs:{placeholder:"请输入自定义编码"}})],1),a("ta-form-item",{attrs:{label:"是否有效",fieldDecoratorId:"effective",fieldDecoratorOptions:{initialValue:!t.org.effective||"1"==t.org.effective,valuePropName:"checked"}}},[a("ta-switch",{attrs:{checkedChildren:"有效",unCheckedChildren:"无效"}})],1),a("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:"edit"!=this.org.type,expression:"this.org.type!='edit'"}],attrs:{label:"关联人员"}},[a("custom-org-user-select-tag",{model:{value:t.userIds,callback:function(e){t.userIds=e},expression:"userIds"}})],1)],1)],1)},c=[],d=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("ta-modal",{attrs:{visible:e.treeVisible,title:"选择组织名称",centered:!0,bodyStyle:{height:"450px",overflow:"auto",paddingBottom:"0px"}},on:{cancel:e.fnCloseModal}},[r("div",{staticStyle:{display:"flex",height:"10%"}},[r("ta-input",{attrs:{placeholder:"输入组织名称进行过滤"},model:{value:e.orgFilterText,callback:function(t){e.orgFilterText=t},expression:"orgFilterText"}})],1),r("div",{staticStyle:{height:"90%"}},[r("ta-tabs",{staticClass:"fit"},[r("ta-tab-pane",{attrs:{tab:"组织机构树"}},[r("ta-e-tree",{ref:"tree",attrs:{load:e.loadOrgData,"show-checkbox":"","check-strictly":"","highlight-current":"","node-key":"orgId",props:e.defaultProps,"filter-node-method":e.filterNode,"default-expanded-keys":e.expandKeys,lazy:""},on:{"check-change":e.handleCheckNodeChange},scopedSlots:e._u([{key:"default",fn:function(t){t.node;var a=t.data;return r("span",{staticClass:"custom-tree-node"},[e._v(" "+e._s(a.orgName)+" "),"0"===a.isAuthority?r("span",[r("span",{staticClass:"no-authority"},[e._v("无操作权限")])]):e._e()])}}])})],1)],1)],1),r("div",{staticStyle:{"text-align":"center"},attrs:{slot:"footer"},slot:"footer"},[r("ta-button",{attrs:{type:"primary"},on:{click:e.fnConfirmNode}},[e._v("保存")])],1)])},m=[],f={name:"customOrgModalTree",props:{visible:{type:Boolean,required:!0}},data:function(){return{treeVisible:!1,expandKeys:[],orgFilterText:"",defaultProps:{children:"children",label:"orgName",isLeaf:"isLeaf",id:"orgId"}}},watch:{visible:function(e){this.treeVisible=e},orgFilterText:function(e){this.$refs.tree.filter(e)}},methods:{filterNode:function(e,t,r){return!e||-1!==t.label.indexOf(e)},handleCheckNodeChange:function(e,t,r){var a=this.$refs.tree.getCheckedKeys();if(t){if("0"===e.isAuthority)return this.$message.warning("您没有该组织的操作权限"),void this.$refs.tree.setChecked(e,!1);if(a.length>=2)for(var s=0;s<a.length;s++)a[s]!==e.orgId&&this.$refs.tree.setChecked(a[s],!1,!1)}},loadOrgData:function(e,t){var r=this;if(0===e.level&&o.Z.queryAllTaOrg(null,(function(e){var a=e.data.orgTreeData;if(a[0]&&a[0].children&&a[0].children instanceof Array&&a[0].children.length>0){r.expandKeys.push(a[0].orgId);var s=a[0].children.map((function(e){var t=e;e.childNum>0&&(t.children=[])}));a[0].children=s}return t(a)})),e.level>=1){var a=e.data.orgId,s=e.data.isLeaf,n={orgId:a};o.Z.queryAllTaOrg(n,(function(e){var r=e.data.orgTreeData;return s&&(r=r.map((function(e){var t=e;t.children=[]}))),t(r)}))}},fnCloseModal:function(){this.treeVisible=!1,this.$emit("close")},fnConfirmNode:function(){var e=this.$refs.tree.getCheckedNodes();e.length<1?this.$message.warning("请选择组织",2.5):e.length>=2?this.$message.warning("只能选择一个组织,或取消当前选择,再选择其他组织",2.5):(this.treeVisible=!1,this.$emit("close",this.$refs.tree.getCheckedNodes()[0].label))}}},g=f,h=r(1001),p=(0,h.Z)(g,d,m,!1,null,"ace9336c",null),y=(p.exports,function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("ta-user-select",{ref:"relationUserTagRef",attrs:{id:"relationUserTag",title:"新增组织人员","is-show":e.isShow,"with-tag":"",load:e.loadOrgTreeNode,"user-list-data":e.relationUserListData,"user-select-data":e.userSelectData,"default-user-list":e.userListResult,props:e.userDefaultProps,pagination:!0},on:{search:e.fnSearch,close:e.fnCloseUserModal,queryUserList:e.fnQueryUserList,getUserListResult:e.fnGetUserListResult,checkAllMembers:e.checkAllMembers},model:{value:e.userListInfo,callback:function(t){e.userListInfo=t},expression:"userListInfo"}})}),O=[],v={name:"customOrgUserSelectTag",props:{visible:{type:Boolean}},data:function(){return{isShow:!1,userListResult:[],relationUserListData:[],userSelectData:[],includeChild:!1,userDefaultProps:{treeNodeKey:"orgId",treeLabel:"label",treeChildren:"children",treeIsLeaf:"isLeaf",listKey:"userId",listTitle:"name",listSubTitle:"mobile",listDescription:"namePath"},userListInfo:[],total:void 0}},computed:{getUserIds:function(){var e=[];return this.userListInfo.map((function(t){e.push(t.userId)})),e.join(",")}},watch:{visible:function(e){e&&(this.isShow=e)},getUserIds:function(e){this.$emit("input",e)}},methods:{resetTags:function(){this.$refs.relationUserTagRef.deleteAll()},loadOrgTreeNode:function(e,t){if(0===e.level&&o.Z.queryAllTaOrg(null,(function(e){var r=e.data.orgTreeData;if(r[0]&&r[0].children&&r[0].children instanceof Array&&r[0].children.length>0){var a=r[0].children.map((function(e){var t=e;e.childNum>0&&(t.children=[])}));r[0].children=a}return t(r)})),e.level>=1){var r=e.data.orgId,a=e.data.isLeaf,s={orgId:r};o.Z.queryAllTaOrg(s,(function(e){var r=e.data.orgTreeData;return a&&(r=r.map((function(e){var t=e;t.children=[]}))),t(r)}))}},fnSearch:function(e){var t=this,r={name:e,loginId:e,idCardNo:e,showChildUser:!0,searchType:"normal",pageNumber:0,pageSize:20};Base.submit(null,{url:"org/orguser/userManagementRestService/queryUserByConditon",data:r}).then((function(e){t.userSelectData=e.data.pageBean.list}))},fnCloseUserModal:function(){this.isShow=!1,this.userListResult=[],this.relationUserListData=[],this.userSelectData=[]},fnQueryUserList:function(e,t,r,a,s){var n=this;this.includeChild=t;var i={orgId:e,showChildUser:t?"1":"0",pageNumber:r,pageSize:10};a&&(i[a]=s),o.Z.queryBatchUserByOrgId(i,(function(e){n.relationUserListData=e.data.userList.list,void 0===n.total&&(n.total=e.data.userList.total)}))},fnGetUserListResult:function(e){this.userListResult=e},checkAllMembers:function(e,t,r){o.Z.queryBatchUserByOrgId({orgId:e,showChildUser:t?"1":"0",pageNumber:1,pageSize:this.total},(function(e){r(e.data.userList.list)}))}}},C=v,T=(0,h.Z)(C,y,O,!1,null,null,null),b=T.exports,N={components:{CustomOrgUserSelectTag:b},props:["org"],name:"addCustomOrg",data:function(){return{orgTypeNameList:[],userIds:"",customOrgPath:""}},mounted:function(){if("addTop"!=this.org.type){var e,t=this.org.customOrgPath,r=null===t||void 0===t?void 0:t.lastIndexOf("/");"edit"===this.org.type&&r>-1?e=t.substr(0,r):"add"===this.org.type&&(e=t),this.customOrgPath=e}this.fnQueryAllCustomOrgTypeName()},methods:{fnResetForm:function(){this.form.resetFields(),this.$refs.userSelectPart.resetTags()},fnQueryAllCustomOrgTypeName:function(){var e=this;o.Z.queryCustomOrgTypeName(null,(function(t){e.orgTypeNameList=t.data.customOrgTypeNameList}))},fnUpdateOrSaveOrg:function(){var e=this;this.form.validateFields((function(t){if(!t){var r=e.form.getFieldsValue(),a=e.customOrgPath,s=e.form.getFieldValue("customOrgName");r.customOrgPath=a?a+"/"+s:s,r.effective=r.effective?"1":"0","addTop"===e.org.type&&(r.userIds=e.userIds,r.addTop=!0,o.Z.addCustomOrg(r,(function(t){e.$emit("closeCustomOrgDrawer",r),e.$message.success("更新数据成功")}))),"add"===e.org.type&&(r.customOrgPathId=e.org.customOrgPathId,r.parentId=e.org.customOrgId,r.userIds=e.userIds,o.Z.addCustomOrg(r,(function(t){e.$emit("closeCustomOrgDrawer",t.data.result),e.$message.success("更新数据成功")}))),"edit"===e.org.type&&(r.parentId=e.org.customOrgId,r.customOrgPathId=e.org.customOrgPathId,r.customOrgId=e.org.customOrgId,o.Z.updateCustomOrg(r,(function(t){e.$emit("closeCustomOrgDrawer",r),e.$message.success("更新数据成功")})))}}))},closeCustomOrgDrawer:function(){this.$emit("closeCustomOrgDrawer")}}},I=N,w=(0,h.Z)(I,u,c,!1,null,null,null),D=w.exports,S=function(){var e=this,t=this,r=t.$createElement,a=t._self._c||r;return a("div",{staticClass:"fit"},[a("ta-tabs",{staticClass:"fit"},[a("ta-button",{attrs:{slot:"tabBarExtraContent",type:"primary"},on:{click:function(e){t.addCustomOrgModalVisible=!0}},slot:"tabBarExtraContent"},[t._v(" 新增组织类别 ")]),a("ta-tab-pane",{attrs:{tab:"自定义组织类别列表"}},[a("ta-form",{attrs:{autoFormCreate:function(t){e.form1=t}}},[a("ta-table",{staticStyle:{"padding-top":"10px"},attrs:{columns:t.customOrgTypeNameColumns,dataSource:t.customOrgTypeNameData,rowKey:"customOrgTypeNameId"},scopedSlots:t._u([{key:"customOrgTypeName",fn:function(e,r){return a("ta-table-edit",{attrs:{editForm:t.form1,type:"input","row-editable":!0,rules:[{validator:t.fnValidate}],placeholder:"请输入类别名称"}})}},{key:"customOrgTypeNameCode",fn:function(e,r){return a("ta-table-edit",{attrs:{editForm:t.form1,type:"input","row-editable":!0,placeholder:"请输入类别自定义编码",rules:[{required:!0,message:"自定义编码不能为空"},{pattern:/^[\u4E00-\u9FA5A-Za-z0-9_/\\-]+$/,message:"只能输入中文英文数字、下划线、横杠及斜杠"}]}})}},{key:"customOrgTypeNameDesc",fn:function(e,r){return a("ta-table-edit",{attrs:{editForm:t.form1,type:"input","row-editable":!0,placeholder:"请输入类别描述"}})}},{key:"effective",fn:function(e,r){return a("ta-table-edit",{attrs:{editForm:t.form1,type:"switch",trueText:"有效",falseText:"无效","row-editable":!0}})}},{key:"action",fn:function(e,r){return a("ta-table-edit",{attrs:{editForm:t.form1,dataSource:t.customOrgTypeNameData,rowKey:"customOrgTypeNameId",type:"rowEdit",beforeChange:t.beforeChange},on:{tableChange:t.changeTable,rowDelete:t.fnDelete}})}}])})],1)],1)],1),a("ta-modal",{attrs:{title:"添加自定义组织",visible:t.addCustomOrgModalVisible,destroyOnClose:!0,getContainer:t.setContainer},on:{cancel:function(e){t.addCustomOrgModalVisible=!1}}},[a("ta-form",{attrs:{autoFormCreate:function(t){return e.form=t}}},[a("ta-row",[a("ta-col",{attrs:{span:24}},[a("ta-form-item",{attrs:{label:"类别名称",fieldDecoratorId:"customOrgTypeName",initValue:t.formInitValue.customOrgTypeName,fieldDecoratorOptions:{rules:[{pattern:/^[\u4E00-\u9FA5A-Za-z0-9_/\\-]+$/,message:"只能输入中文英文数字、下划线、横杠及斜杠"}]},require:{message:"请输入类别名称!"}}},[a("ta-input")],1)],1)],1),a("ta-row",[a("ta-col",{attrs:{span:24}},[a("ta-form-item",{attrs:{label:"类别自定义编码",fieldDecoratorId:"customOrgTypeNameCode",initValue:t.formInitValue.customOrgTypeNameCode,fieldDecoratorOptions:{rules:[{pattern:/^[\u4E00-\u9FA5A-Za-z0-9_/\\-]+$/,message:"只能输入中文英文数字、下划线、横杠及斜杠"}]},require:{message:"请输入类别自定义编码!"}}},[a("ta-input")],1)],1)],1),a("ta-row",[a("ta-col",{attrs:{span:24}},[a("ta-form-item",{attrs:{label:"类别描述",fieldDecoratorId:"customOrgTypeNameDesc",initValue:t.formInitValue.customOrgTypeNameDesc}},[a("ta-input")],1)],1)],1),a("ta-row",[a("ta-col",{attrs:{span:24}},[a("ta-form-item",{attrs:{label:"有效标识",fieldDecoratorId:"effective",fieldDecoratorOptions:{initialValue:t.formInitValue.effective,valuePropName:"checked"}}},[a("ta-switch")],1)],1)],1)],1),a("template",{slot:"footer"},[a("ta-button",{on:{click:function(e){return t.form.resetFields()}}},[t._v(" 重置 ")]),a("ta-button",{key:"submit",attrs:{type:"primary"},on:{click:t.fnOnSave}},[t._v(" 保存 ")])],1)],2)],1)},k=[],x=[{title:"类别名称",width:"20%",overflowTooltip:!0,dataIndex:"customOrgTypeName",scopedSlots:{customRender:"customOrgTypeName"}},{title:"类别自定义编码",width:"23%",overflowTooltip:!0,dataIndex:"customOrgTypeNameCode",scopedSlots:{customRender:"customOrgTypeNameCode"}},{title:"类别描述",width:"30%",overflowTooltip:!0,dataIndex:"customOrgTypeNameDesc",scopedSlots:{customRender:"customOrgTypeNameDesc"}},{title:"有效标识",width:"12%",dataIndex:"effective",scopedSlots:{customRender:"effective"}},{title:"操作",width:"15%",align:"center",scopedSlots:{customRender:"action"}}],B={name:"addCustomOrgTypeName",data:function(){return{customOrgTypeNameColumns:x,customOrgTypeNameData:[],formInitValue:{effective:!0},addCustomOrgModalVisible:!1}},mounted:function(){this.fnQueryAllCustomOrgTypeName()},methods:{setContainer:function(){return document.getElementById("customOrgManagement")},fnQueryAllCustomOrgTypeName:function(){var e=this;o.Z.queryCustomOrgTypeNameByCondition(null,(function(t){var r=t.data.customOrgTypeNameList;r.map((function(e){e.effective="1"===e.effective})),e.customOrgTypeNameData=r}))},fnValidate:function(e,t,r){var a=/^[\u4E00-\u9FA5A-Za-z0-9_/\\-]+$/;a.test(t)||""==t?r():r("只能输入中文英文数字、下划线、横杠及斜杠")},beforeChange:function(e,t){var r=this,a=e.newData,s=e.record,n=Object.assign({},s,a),i=n.customOrgTypeName,l=n.customOrgTypeNameCode,u=n.customOrgTypeNameDesc,c=n.effective,d=n.customOrgTypeNameId;if(i&&""!=i.trim()){var m={customOrgTypeName:i,customOrgTypeNameCode:l,customOrgTypeNameDesc:u,effective:c?"1":"0"};d?(m.customOrgTypeNameId=d,o.Z.updateCustomOrgTypeName(m,(function(e){t(),r.$message.success("修改组织类别成功"),r.fnQueryAllCustomOrgTypeName()}))):o.Z.addCustomOrgTypeName(m,(function(e){t(),r.$message.success("新增组织类别成功"),r.fnQueryAllCustomOrgTypeName()}))}else t("自定义组织名称不能为空")},changeTable:function(e){var t=e[0];t.customOrgTypeName&&""!==t.customOrgTypeName.trim()||e.splice(0,1),this.customOrgTypeNameData=e},fnOnSave:function(){var e=this;this.form.validateFieldsAndScroll((function(t){if(!t){e.addCustomOrgModalVisible=!1;var r=e.form.getFieldValue("effective")?"1":"0",a=e.form.getFieldsValue();a.effective=r,o.Z.addCustomOrgTypeName(a,(function(t){e.$message.success("新增组织类别成功"),e.fnQueryAllCustomOrgTypeName()}))}}))},fnDelete:function(e){var t=this;o.Z.deleteBatchCustomOrgTypeName({customOrgTypeNameId:e},(function(r){t.$message.success("更新数据成功"),t.customOrgTypeNameData=t.customOrgTypeNameData.filter((function(t){return t.customOrgTypeNameId!=e}))}))}}},V=B,_=(0,h.Z)(V,S,k,!1,null,null,null),L=_.exports,R=[{title:"组织名称",width:"25%",dataIndex:"customOrgName",overflowTooltip:!0,scopedSlots:{customRender:"customOrgName"}},{title:"自定义编码",width:"16%",overflowTooltip:!0,dataIndex:"customCode"},{title:"状态",width:"9%",dataIndex:"effective",yesOrNoTag:!0,scopedSlots:{customRender:"effective"}},{title:"最后修改时间",width:"19%",dataIndex:"updateTime",scopedSlots:{customRender:"updateTime"}},{title:"操作",dataIndex:"operation",width:"38%",align:"center",scopedSlots:{customRender:"action"}}],$={name:"customOrgManagement",components:{AddCustomOrgTypeName:L,addCustomOrg:D},mixins:[l.Z],data:function(){var e=this;return{selectFilter:[],clickIndex:0,tabTitle:"自定义组织列表",btnLoading:!1,currentExpand:[],orgTypeNameList:[],orgTypeNameSelVal:"baa315851d3c4a90a47889672404505d",effectiveSelVal:null,validId:"",orgColumns:R,operateMenu:[{name:"新增下级",disabled:function(e){return"0"==e.effective},title:function(e){return"0"==e.effective?"禁用的自定义组织不允许使用新增下级":""},onClick:function(t){return e.fnShowCustomOrgDrawer(t)}},{name:"更多",type:"more",moreMenuList:[{name:"编辑",disabled:function(e){return"0"==e.effective},title:function(e){return"0"==e.effective?"禁用的自定义组织不允许使用编辑":""},onClick:function(t){return e.fnShowEditOrgModal(t)}},{name:"启用",type:"confirm",confirmTitle:"确认启用该自定义组织?",onOk:function(t){e.fnBatchPickOrg(t)}},{name:"禁用",type:"confirm",confirmTitle:"确认禁用该自定义组织?",onOk:function(t){e.fnBatchBanOrg(t)}},{name:"删除",type:"confirm",confirmTitle:"确认删除该组织?",onOk:function(t){e.fnBatchDeleteOrg(t)}}]}],orgData:[],filteredInfo:null,sortedInfo:null,customOrg:{orgTitle:"新增组织机构",orgId:"",orgName:"",visible:!1},org:{},selectedRowKeys:[],selectedRows:[],customOrgTypeNameVisible:!1,defaultExpandAll:!0,arrayData:{},isDetailShow:!1,showAddTop:!1,batchDeleteVisible:!1}},mounted:function(){this.currentExpand=[],this.fnLoadDefaultCustomOrg();var e=[{element:".step1",popover:{title:"组织类别管理",description:"创建和编辑组织类别，通过组织类别管理多个自定义组织树",position:"bottom"}},{element:".step2",popover:{title:"新增组织",description:"创建组织树，组织树创建成功之后可以通过【人员管理】进行组织人员关系绑定，通过【新增下级】创建下级组织",position:"left"}}];this.fnCommonGuide(e)},methods:{moment:i(),setContainer:function(){return document.getElementById("customOrgManagement")},fnOnChange:function(e,t){this.selectedRowKeys=e,this.selectedRows=t},filterClick:function(){this.effectiveSelVal=this.selectFilter.join(","),this.fnInitDefaultOrg(this.orgTypeNameSelVal,null)},fnLoadDefaultCustomOrg:function(){this.fnQueryAllCustomOrgTypeName()},fnLoadSubTreeData:function(e,t){var r=this,a=t.customOrgId;if(e)if(t.children&&t.children.length>0)this.currentExpand.push(a);else{var s=this.effectiveSelVal,n=null;if(s&&s.length>0){var i=s.indexOf(",");-1===i&&(n=s)}var l={customOrgTypeNameId:this.orgTypeNameSelVal,parentId:a,effective:n};o.Z.queryCustomOrgByParentId(l,(function(e){if(t.children=e.data.customOrgList,void 0!==a&&""!==a)r.currentExpand.push(a);else{var s=t.children&&t.children>0;s&&r.currentExpand.push(r.record.children[0].customOrgId)}}))}else{var u=this.currentExpand.indexOf(a);this.currentExpand.splice(u,1)}},fnInitDefaultOrg:function(e,t){var r=this,a=this.effectiveSelVal,s=null;if(a&&a.length>0){var n=a.indexOf(",");-1===n&&(s=a)}var i={customOrgTypeNameId:e,parentId:t,effective:s};o.Z.queryCustomOrgByParentId(i,(function(e){r.currentExpand.length=0,r.orgData=e.data.customOrgList,r.$nextTick((function(){var e=r.orgData&&r.orgData.length>0;e&&r.orgData[0].children.length>0&&r.currentExpand.push(r.orgData[0].customOrgId),s||(r.showAddTop=!e)}))}))},fnResetForm:function(){this.$refs.customOrgChild.fnResetForm()},fnUpdateOrSaveOrg:function(){this.btnLoading=!0,this.$refs.customOrgChild.fnUpdateOrSaveOrg(),this.btnLoading=!1},fnShowCustomOrgTypeNameModal:function(){this.customOrgTypeNameVisible=!0},fnQueryAllCustomOrgTypeName:function(){var e=this;o.Z.queryCustomOrgTypeName(null,(function(t){e.orgTypeNameList=t.data.customOrgTypeNameList;var r=e.orgTypeNameList.filter((function(t,r){return t.customOrgTypeNameId===e.orgTypeNameSelVal?(e.clickIndex=r,t):null}));r&&0!==r.length||(e.orgTypeNameSelVal=e.orgTypeNameList[0].customOrgTypeNameId),e.fnInitDefaultOrg(e.orgTypeNameSelVal,null)}))},fnHandleOrgTypeNameChange:function(e,t){this.clickIndex!=t&&(this.orgTypeNameSelVal=e,this.clickIndex=t,this.currentExpand=[],this.selectFilter=[],this.showAddTop=!1,this.effectiveSelVal=null,this.fnInitDefaultOrg(e,null))},fnQueryOrg:function(){var e=this;if(this.orgTypeNameSelVal){var t=this.validId,r=this.effectiveSelVal;r=r&&"all"!==r?r:null;var a={validId:t,customOrgTypeNameId:this.orgTypeNameSelVal,effective:r};o.Z.queryCustomOrgByValidId(a,(function(t){e.orgData=t.data.customOrgList}))}else this.$message.warning("请选择组织类别")},fnBatchDeleteOrg:function(e){var t=this,r={};if(e)r.customOrgIds=e.customOrgId,this.selectedRows.push(e);else{var a=this.selectedRows.map((function(e){return e.customOrgId}));r.customOrgIds=a.join(",")}o.Z.deleteBatchCustomOrg(r,(function(e){t.$message.success("更新数据成功"),t.fnInitDefaultOrg(t.orgTypeNameSelVal,null),t.selectedRowKeys=[],t.selectedRows=[],t.batchDeleteVisible=!1,0==t.orgData.length&&(t.showAddTop=!0)}))},fnBatchBanOrg:function(e){var t=this,r={};if(e){if("0"==e.effective)return void this.$message.warning("该记录已经已禁用，请勿重复操作！");r.customOrgIds=e.customOrgId,r.effective="0"}else r.customOrgIds=this.selectedRowKeys.join(","),r.effective="0";o.Z.updateBatchCustomOrgStatus(r,(function(e){t.$message.success("更新数据成功"),t.fnInitDefaultOrg(t.orgTypeNameSelVal,null),t.selectedRowKeys=[],t.selectedRows=[]}))},fnBatchPickOrg:function(e){var t=this,r={};if(e){if("1"==e.effective&&e.isLeaf)return void this.$message.warning("该记录已经启用，请勿重复操作！");r.customOrgIds=e.customOrgId,r.effective="1"}else r.customOrgIds=this.selectedRowKeys.join(","),r.effective="1";o.Z.updateBatchCustomOrgStatus(r,(function(e){t.$message.success("更新数据成功"),t.fnInitDefaultOrg(t.orgTypeNameSelVal,null),t.selectedRowKeys=[],t.selectedRows=[]}))},fnShowRelationUserModal:function(e){var t=this;this.isShow=!0,o.Z.queryAllTaOrg(null,(function(e){t.orgTreeData=e.data.orgTreeData;var r={customOrgId:t.org.customOrgId,includeChildren:"0",pageNumber:0,pageSize:10};o.Z.queryBatchUserByCustomOrgId(r,(function(e){t.result=e.data.customOrgUserList.list}))})),this.org=e},fnExportOpenNotification:function(){this.$notification.open({message:"导出文件提醒",description:"导出文件默认为xlsx文件格式,下载完成后,请使用office等工具进行打开,如果没有,请进行安装"})},fnShowCustomOrgDrawer:function(e){this.customOrg.visible=!0,this.org={},this.org=e,this.org.type="add",this.customOrg.orgTitle="新增下级组织机构"},drawerClose:function(e){this.customOrg.visible=!1,this.fnInitDefaultOrg(this.orgTypeNameSelVal,null)},fnLoadLeftTab:function(){var e=this,t=this.orgTypeNameList.filter((function(t,r){return t.customOrgTypeNameId===e.orgTypeNameSelVal?(e.clickIndex=r,t):null}));t&&0!==t.length||(this.orgTypeNameSelVal=this.orgTypeNameList[0].customOrgTypeNameId),this.fnInitDefaultOrg(this.orgTypeNameSelVal,null)},changeRowData:function(e,t,r){for(var a=0;a<e.length;a++){if(e[a][t]==r[t]){if(e[a]=Object.assign(e[a],r),e[a].children)for(var s=0;s<e[a].children.length;s++)e[a].children[s].customOrgPath=r.customOrgName+"/"+e[a].children[s].customOrgName;break}e[a].children&&this.changeRowData(e[a].children,"customOrgId",r)}},fnShowEditOrgModal:function(e){this.customOrg.visible=!0,this.org={},this.org=e,this.org.type="edit",this.customOrg.orgTitle="修改组织结构"},fnAddTopOrg:function(){this.customOrg.visible=!0,this.customOrg.orgTitle="新增组织结构",this.org={},this.org.type="addTop",this.org.customOrgName="",this.org.customCode="",this.org.customOrgTypeNameId=this.orgTypeNameSelVal},fnCloseCustomOrgTypeNameModal:function(){this.customOrgTypeNameVisible=!1,this.fnQueryAllCustomOrgTypeName()}}},A=$,U=(0,h.Z)(A,a,s,!1,null,"68075177",null),F=U.exports},80790:function(e,t,r){var a=r(76698);r(34530);t["Z"]={activated:function(){var e=window.pageVmObj,t=e["steps_"+e._route.name];this.methods?this.methods.fnCommonGuide(t):this.fnCommonGuide(t)},deactivated:function(){this.methods?this.methods.fnCommonGuide([],!0):this.fnCommonGuide([],!0)},methods:{fnCommonGuide:function(e,t){var r=window.pageVmObj;t?(r.driver.reset(),window.fnPageGuide=null):(r["steps_"+r._route.name]=e,r.driver=new a.Z({allowClose:!1}),window.fnPageGuide=function(){r.driver.defineSteps(e),r.driver.start()})}}}},1888:function(e,t){var r="/org/orguser/customOrgManagementRestService/";t["Z"]={queryCustomOrgByParentId:function(e,t){Base.submit(null,{url:r+"queryCustomOrgByParentId",data:e},{successCallback:function(e){return t(e)}})},queryCustomOrgByCondition:function(e,t){Base.submit(null,{url:r+"queryCustomOrgByCondition",data:e},{successCallback:function(e){return t(e)}})},queryCustomOrgTypeName:function(e,t){Base.submit(null,{url:r+"queryCustomOrgTypeName",data:e},{successCallback:function(e){return t(e)}})},queryCustomOrgTypeNameByCondition:function(e,t){Base.submit(null,{url:r+"queryCustomOrgTypeNameByCondition",data:e},{successCallback:function(e){return t(e)}})},queryCustomOrgByValidId:function(e,t){Base.submit(null,{url:r+"queryCustomOrgByValidId",data:e},{successCallback:function(e){return t(e)}})},deleteBatchCustomOrg:function(e,t){Base.submit(null,{url:r+"deleteBatchCustomOrg",data:e},{successCallback:function(e){return t(e)}})},updateBatchCustomOrgStatus:function(e,t){Base.submit(null,{url:r+"updateBatchCustomOrgStatus",data:e},{successCallback:function(e){return t(e)}})},addBatchCustomOrgUser:function(e,t){Base.submit(null,{url:r+"addBatchCustomOrgUser",data:e},{successCallback:function(e){return t(e)}})},deleteBatchCustomOrgUser:function(e,t){Base.submit(null,{url:r+"deleteBatchCustomOrgUser",data:e},{successCallback:function(e){return t(e)}})},addCustomOrg:function(e,t){Base.submit(null,{url:r+"addCustomOrg",data:e},{successCallback:function(e){return t(e)}})},updateCustomOrg:function(e,t){Base.submit(null,{url:r+"updateCustomOrg",data:e},{successCallback:function(e){return t(e)}})},queryAllTaOrg:function(e,t){Base.submit(null,{url:"org/orguser/orgManagementRestService/getOrgByAsync",data:e},{successCallback:function(e){return t(e)}})},queryOrgUser:function(e,t){Base.submit(null,{url:"org/orguser/userManagementRestService/queryUserByConditon",data:e},{successCallback:function(e){return t(e)}})},queryBatchUserByCustomOrgId:function(e,t){Base.submit(null,{url:r+"queryBatchUserByCustomOrgId",data:e},{successCallback:function(e){return t(e)}})},queryBatchUserByOrgId:function(e,t){Base.submit(null,{url:"org/orguser/userManagementRestService/queryEffectiveUser",data:e},{successCallback:function(e){return t(e)}})},deleteBatchCustomOrgTypeName:function(e,t){Base.submit(null,{url:r+"deleteBatchCustomOrgTypeName",data:e},{successCallback:function(e){return t(e)}})},addCustomOrgTypeName:function(e,t){Base.submit(null,{url:r+"addCustomOrgTypeName",data:e},{successCallback:function(e){return t(e)}})},updateCustomOrgTypeName:function(e,t){Base.submit(null,{url:r+"updateCustomOrgTypeName",data:e},{successCallback:function(e){return t(e)}})}}}}]);
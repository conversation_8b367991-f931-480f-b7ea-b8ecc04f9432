"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[80],{86270:function(t,e,a){a.r(e),a.d(e,{default:function(){return f}});var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"fit",attrs:{id:"app"}},[a("ta-border-layout",{attrs:{layout:{header:"70px"},centerCfg:{showBar:!0,toolBarStyle:{height:"50px",lineHeight:"48px"}}}},[a("div",{staticClass:"center-box",attrs:{slot:"header"},slot:"header"},[a("ta-input-search",{staticClass:"search-box",attrs:{placeholder:"请输入作业名称",enterButton:"搜索"},on:{search:t.loadData},model:{value:t.jobName,callback:function(e){t.jobName=e},expression:"jobName"}})],1),a("div",{staticStyle:{float:"right"},attrs:{slot:"centerExtraContent"},slot:"centerExtraContent"},[a("ta-button",{on:{click:t.clearCache}},[t._v("清除已完成缓存")])],1),a("ta-table",{attrs:{columns:t.columns,dataSource:t.gridData,pagination:!1},scopedSlots:t._u([{key:"percent",fn:function(t,e){return a("span",{},[a("ta-progress",{attrs:{percent:Math.round(e.progress/e.total*100),size:"small"}})],1)}},{key:"action",fn:function(e,r){return a("span",{},[a("ta-button",{staticClass:"refresh",attrs:{size:"small"},on:{click:function(e){return t.refresh(r.jobName,r.shardingItem)}}},[t._v("刷新")])],1)}}])})],1)],1)},s=[],n=a(89584),o=(a(32564),"/jobmg/elasticjob/jobOperateRestService/"),i={getJobProgress:function(t,e){Base.submit(null,{url:o+"getJobProgress",data:t},{successCallback:function(t){return e(t)}})},refreshProgress:function(t,e){Base.submit(null,{url:o+"refreshProgress",data:t},{successCallback:function(t){return e(t)}})},clearCache:function(t,e){Base.submit(null,{url:o+"clearCache",data:t},{successCallback:function(t){return e(t)}})}},c=[{title:"作业名称",dataIndex:"jobName",width:"20%"},{title:"分片项",dataIndex:"shardingItem",width:"10%"},{title:"数据总数",dataIndex:"total",width:"10%"},{title:"当前处理数",dataIndex:"progress",width:"10%"},{title:"处理异常数",dataIndex:"errorNum",width:"10%"},{title:"开始时间",dataIndex:"startDate",width:"10%"},{title:"作业进度",dataIndex:"percent",width:"10%",scopedSlots:{customRender:"percent"}},{title:"操作",dataIndex:"action",width:"10%",scopedSlots:{customRender:"action"},align:"center"}],l={name:"app",data:function(){return{timer:void 0,jobName:"",columns:c,gridData:[]}},mounted:function(){var t=this;this.loadData(),this.timer?clearInterval(this.timer):this.timer=setInterval((function(){t.loadData()}),5e3)},beforeDestroy:function(){clearInterval(this.timer)},methods:{loadData:function(){var t=this,e={jobName:this.jobName};i.getJobProgress(e,(function(e){t.gridData=e.data.jobProgress}))},refresh:function(t,e){var a=this,r=(0,n.Z)(this.gridData),s=r.filter((function(a){return t===a.jobName&&a.shardingItem==e}))[0],o={jobName:t,shardingItem:e};i.refreshProgress(o,(function(t){s.total=t.data.jobProgress.total,s.progress=t.data.jobProgress.progress,s.errorNum=t.data.jobProgress.errorNum,s.startDate=t.data.jobProgress.startDate,a.gridData=r}))},clearCache:function(){var t=this;i.clearCache({},(function(e){t.$message.success("清除已完成任务进度缓存成功"),t.loadData()}))}}},u=l,d=a(1001),h=(0,d.Z)(u,r,s,!1,null,"29319bea",null),f=h.exports}}]);
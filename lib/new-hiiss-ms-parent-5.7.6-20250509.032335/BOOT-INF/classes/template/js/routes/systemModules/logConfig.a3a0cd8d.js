"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[2935],{31812:function(e,t,a){a.r(t),a.d(t,{default:function(){return y}});var l=function(){var e=this,t=this,a=t.$createElement,l=t._self._c||a;return l("div",{staticClass:"fit",attrs:{id:"app"}},[l("ta-border-layout",{attrs:{centerCfg:{showBar:!0,toolBarStyle:{height:"50px",lineHeight:"48px"}}}},[l("div",{attrs:{slot:"centerExtraContent"},slot:"centerExtraContent"},[l("ta-tag-select",{attrs:{title:"日志级别",data:t.CollectionData("LOGLEVEL"),"is-multi":!0},on:{change:t.filterClick},model:{value:t.logLevels,callback:function(e){t.logLevels=e},expression:"logLevels"}}),l("div",{staticStyle:{float:"right"}},[l("ta-button",{attrs:{type:"primary"},on:{click:t.fnAddLogConfig}},[t._v("新增")]),l("ta-button",{on:{click:t.showFileConfigModal}},[t._v("配置文件路径")])],1)],1),l("ta-form",{attrs:{autoFormCreate:function(t){e.form=t}}},[l("ta-table",{attrs:{columns:t.columns,dataSource:t.logGridData,pagination:!1},scopedSlots:t._u([{key:"level",fn:function(e,a){return l("ta-table-edit",{attrs:{type:"select",option:t.CollectionData("LOGLEVEL"),beforeChange:t.changeData}})}},{key:"appenderType",fn:function(e,a){return l("ta-table-edit",{attrs:{type:"select",option:t.output,multiple:!0,beforeChange:t.changeData}})}},{key:"action",fn:function(e,a){return l("span",{},[l("ta-table-operate",{attrs:{operateMenu:t.operateMenu}})],1)}}])},[l("span",{attrs:{slot:"levelTitle"},slot:"levelTitle"},[t._v("日志级别 "),l("ta-icon",{attrs:{type:"edit"}})],1),l("span",{attrs:{slot:"appenderTypeTitle"},slot:"appenderTypeTitle"},[t._v("输出类型 "),l("ta-icon",{attrs:{type:"edit"}})],1)])],1)],1),l("ta-drawer",{attrs:{destroyOnClose:!0,title:"新增日志配置",width:"500px",placement:"right",closable:!0,visible:t.drawVisible},on:{close:function(e){return t.fnCloseLogDrawer(!1)}}},[l("add-log-config",{ref:"addLogConfigDrawer",attrs:{bindData:t.bindData},on:{closeLogDrawer:t.fnCloseLogDrawer}}),l("div",{attrs:{slot:"footer"},slot:"footer"},[l("ta-button-group",[l("ta-button",{on:{click:t.handleReset}},[t._v("重置")]),l("ta-button",{attrs:{type:"primary"},on:{click:t.saveLogConfig}},[t._v("保存")])],1)],1)],1),l("ta-modal",{attrs:{title:"配置文件路径",maskClosable:!1,width:"480px",destroyOnClose:!0},on:{ok:t.handleFileConfig},model:{value:t.fileVisible,callback:function(e){t.fileVisible=e},expression:"fileVisible"}},[l("template",{slot:"footer"},[l("ta-button",{key:"back",on:{click:function(e){t.fileVisible=!1}}},[t._v("取消")]),l("ta-button",{key:"submit",attrs:{type:"primary"},on:{click:t.handleFileConfig}},[t._v(" 保存 ")])],1),l("ta-label-con",{attrs:{label:"文件路径"}},[l("ta-input",{model:{value:t.fileNamePattern,callback:function(e){t.fileNamePattern=e},expression:"fileNamePattern"}})],1)],2)],1)},o=[],n="/logmg/logconfig/logConfigRestService/",i={getLogConfig:function(e,t){Base.submit(null,{url:n+"getLogConfig",data:e},{successCallback:function(e){return t(e)}})},configLevelAndAppenderType:function(e,t){Base.submit(null,{url:n+"configLevelAndAppenderType",data:e},{successCallback:function(e){return t(e)},failCallback:function(e){return t(e)}})},configFileNamePattern:function(e,t){Base.submit(null,{url:n+"configFileNamePattern",data:e},{successCallback:function(e){return t(e)}})},deleteLogConfigByName:function(e,t){Base.submit(null,{url:n+"deleteLogConfigByName",data:e},{successCallback:function(e){return t(e)}})},addLogConfig:function(e,t,a){Base.submit(e,{url:n+"addLogConfig",data:t,autoValid:!0},{successCallback:function(e){return a(e)}}).then((function(e){})).catch((function(e){}))}},s=function(){var e=this,t=this,a=t.$createElement,l=t._self._c||a;return l("ta-form",{attrs:{autoFormCreate:function(t){e.form=t}}},[l("ta-form-item",{attrs:{label:"包名称",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"packageName",fieldDecoratorOptions:{rules:[{required:!0,message:"请输入包名称"}]}}},[l("ta-input")],1),l("ta-form-item",{attrs:{label:"日志级别",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"level",fieldDecoratorOptions:{rules:[{required:!0,message:"请选择日志级别"}]}}},[l("ta-select",{attrs:{showSearch:""}},t._l(t.CollectionData("LOGLEVEL"),(function(e){return l("ta-select-option",{key:e.key,attrs:{value:e.label}},[t._v(t._s(e.label))])})),1)],1),l("ta-form-item",{attrs:{label:"输出类型",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"appenderType"}},[l("ta-select",{attrs:{mode:"multiple",tokenSeparators:[","],placeholder:"支持多选"}},t._l(t.output,(function(e){return l("ta-select-option",{key:e.key,attrs:{disabled:e.disabled}},[t._v(t._s(e.value))])})),1)],1)],1)},r=[],c=[{key:"console",value:"console"},{key:"file",value:"file"},{key:"kafka",value:"kafka"}],u={name:"addLogConfig",props:["bindData"],data:function(){return{formItemLayout:{labelCol:{span:6},wrapperCol:{span:18}},output:c}},methods:{saveLogConfig:function(){var e=this,t=this.form.getFieldsValue();t.appenderType=t.appenderType?t.appenderType.join(","):"",i.addLogConfig(this.form,t,(function(t){e.$message.success("新增成功"),e.$emit("closeLogDrawer",!0)}))},handleReset:function(){this.form.resetFields()}}},f=u,d=a(1001),p=(0,d.Z)(f,s,r,!1,null,null,null),g=p.exports,m=[{title:"包名称",dataIndex:"packageName",width:"25%",overflowTooltip:!0},{dataIndex:"level",slots:{title:"levelTitle"},scopedSlots:{customRender:"level"},width:"200px"},{dataIndex:"appenderType",slots:{title:"appenderTypeTitle"},scopedSlots:{customRender:"appenderType"},width:"200px"},{title:"文件路径",dataIndex:"fileNamePattern",width:"25%",overflowTooltip:!0},{title:"操作选项",dataIndex:"action",scopedSlots:{customRender:"action"},align:"center",width:"200px"}],b={OFF:{value:"0",label:"OFF"},ERROR:{value:"1",label:"ERROR"},WARN:{value:"2",label:"WARN"},INFO:{value:"3",label:"INFO"},DEBUG:{value:"4",label:"DEBUG"},TRACE:{value:"5",label:"TRACE"},ALL:{value:"6",label:"ALL"}},v=[{label:"console",value:"console"},{label:"file",value:"file"},{label:"kafka",value:"kafka"}],C={name:"app",components:{addLogConfig:g},data:function(){var e=this;return{fileVisible:!1,isConsole:!1,isFile:!1,isKafka:!1,fileNamePattern:"",level:"",appenderType:[],columns:m,operateMenu:[{name:"删除",type:"confirm",confirmTitle:"确定要删除吗?",isShow:function(t){return e.logGridData.length},onOk:function(t){e.onDelete(t)}}],logGridData:[],drawVisible:!1,bindData:{},logLevels:[],output:v,logLevelList:b}},mounted:function(){this.loadData()},methods:{loadData:function(e){var t=this,a={};e&&(a={levels:e.join(",")}),i.getLogConfig(a,(function(e){t.isConsole=e.data.isConsole,t.isFile=e.data.isFile,t.isKafka=e.data.isKafka,t.output.map((function(e){e.disabled=!0,("console"==e.value&&t.isConsole||"file"==e.value&&t.isFile||"kafka"==e.value&&t.isKafka)&&(e.disabled=!1)})),t.fileNamePattern=e.data.fileNamePattern,t.logGridData=e.data.logGridData,t.logGridData.forEach((function(e){e.appenderType=e.appenderType&&e.appenderType.length?e.appenderType.split(","):[],e.level=t.logLevelList[e.level].value}))}))},changeData:function(e,t){var a=this,l=e.newData,o=e.columnKey,n=e.record,s={packageName:n.packageName,level:"level"==o?this.CollectionLabel("LOGLEVEL",l):this.CollectionLabel("LOGLEVEL",n.level),appenderType:"appenderType"==o?l.join(","):n.appenderType.join(",")};i.configLevelAndAppenderType(s,(function(e){e.serviceSuccess?(a.$message.success("配置成功"),n[o]=l,t()):t("配置失败")}))},showFileConfigModal:function(){if(!this.fileNamePattern)return this.$message.warn("未配置'file'输出类型"),!1;this.fileVisible=!0},handleFileConfig:function(e){var t=this,a={fileNamePattern:this.fileNamePattern};i.configFileNamePattern(a,(function(e){t.loadData(),t.fileVisible=!1,t.$message.success("配置成功")}))},fnAddLogConfig:function(){this.bindData={isConsole:this.isConsole,isFile:this.isFile,isKafka:this.isKafka},this.drawVisible=!0},fnCloseLogDrawer:function(e){this.drawVisible=!1,e&&this.loadData()},onDelete:function(e){var t=this,a={packageName:e.packageName,appenderType:e.appenderType.join(",")};i.deleteLogConfigByName(a,(function(e){t.$message.success("删除成功"),t.loadData()}))},handleReset:function(){this.$refs.addLogConfigDrawer.handleReset()},saveLogConfig:function(){this.$refs.addLogConfigDrawer.saveLogConfig()},filterClick:function(e){var t=this,a=[];this.logLevels.map((function(e){a.push(t.CollectionLabel("LOGLEVEL",e))})),this.loadData(a)}}},h=C,L=(0,d.Z)(h,l,o,!1,null,null,null),y=L.exports}}]);
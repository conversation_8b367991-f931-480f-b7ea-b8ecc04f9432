"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[3132],{26484:function(e,t,a){a.r(t),a.d(t,{default:function(){return x}});var c=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("ta-border-layout",{attrs:{layout:{header:"70px",footer:"70px"},"footer-cfg":{showBorder:!1},centerCfg:{showBar:!0,toolBarStyle:{height:"50px",lineHeight:"48px"}}}},[a("div",{staticClass:"center-box",attrs:{slot:"header"},slot:"header"},[a("ta-input-search",{staticClass:"search-box",attrs:{placeholder:"请输入缓存名称",enterButton:"搜索"},on:{search:e.fnQueryCache},model:{value:e.cacheName,callback:function(t){e.cacheName=t},expression:"cacheName"}})],1),a("div",{attrs:{slot:"centerExtraContent"},slot:"centerExtraContent"},[a("ta-tag-select",{attrs:{title:"缓存类型",data:e.CollectionData("CACHETYPE")},on:{change:e.cacheQuery},model:{value:e.cacheType,callback:function(t){e.cacheType=t},expression:"cacheType"}})],1),a("ta-table",{attrs:{columns:e.cacheColumns,dataSource:e.cacheData,loading:e.loading,pagination:!1,rowKey:"cacheId",showOverflowTooltip:!0,locale:{filterConfirm:"确定",filterReset:"重置",emptyText:"暂无数据"}},scopedSlots:e._u([{key:"action",fn:function(t,c){return a("span",{},[a("ta-table-operate",{attrs:{operateMenu:e.operateMenu}})],1)}}])}),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("ta-pagination",{ref:"gridPager",staticStyle:{float:"right","margin-top":"10px"},attrs:{showSizeChanger:"",showQuickJumper:"",dataSource:e.cacheData,defaultPageSize:10,params:e.cachePageParams,url:e.cacheUrl},on:{"update:dataSource":function(t){e.cacheData=t},"update:data-source":function(t){e.cacheData=t}}})],1),a("ta-modal",{attrs:{visible:e.cache.visible,centered:!0,destroyOnClose:"",footer:null,width:"90%",bodyStyle:{height:"400px"}},on:{cancel:e.onModalCancel}},[a("template",{slot:"title"},[a("div",{staticStyle:{"text-align":"center"}},[e._v(" "+e._s(e.cacheNameTitle)+"缓存详情 ")])]),a("cache-detail",{attrs:{cache:e.cacheItem}})],2)],1)},n=[],i="/cacheMg/cacheMgRestService/",o={queryKeyList:function(e,t){Base.submit(null,{url:i+"queryKeyList",data:e},{successCallback:function(e){return t(e)}})},queryCacheNameList:function(e,t){Base.submit(null,{url:"/cacheMg/cacheMgRestService",data:e},{successCallback:function(e){return t(e)}})},queryCacheContent:function(e,t){Base.submit(null,{url:i+"queryCacheContent",data:e},{successCallback:function(e){return t(e)}})},queryCacheNodeList:function(e,t){Base.submit(null,{url:i+"queryCacheNodeList",data:e},{successCallback:function(e){return t(e)}})}},l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("ta-row",{staticStyle:{width:"100%"},attrs:{gutter:10}},[a("ta-col",{attrs:{span:8}},[a("ta-tabs",{staticClass:"fit"},[a("ta-tab-pane",{staticStyle:{"padding-top":"10px"},attrs:{tab:"缓存节点列表"}},[a("ta-table",{attrs:{columns:e.nodeColumns,dataSource:e.nodeData,size:"small",pagination:!1}})],1)],1)],1),a("ta-col",{attrs:{span:8}},[a("ta-tabs",{staticClass:"fit"},[a("ta-tab-pane",{staticStyle:{"padding-top":"10px"},attrs:{tab:"缓存键列表"}})],1)],1),a("ta-col",{attrs:{span:8}},[a("ta-tabs",{staticClass:"fit"},[a("ta-tab-pane",{staticStyle:{"padding-top":"10px"},attrs:{tab:"缓存值"}})],1)],1)],1)},s=[],r=[{title:"节点名称",width:"100",dataIndex:"nodeName",key:"nodeName"},{title:"节点状态",width:"100",dataIndex:"nodeState",key:"nodeState"},{title:"版本",width:"100",dataIndex:"version",key:"version"}],h=[{title:"节点名称",width:"100",dataIndex:"nodeName",key:"nodeName"},{title:"节点状态",width:"100",dataIndex:"nodeState",key:"nodeState"},{title:"版本",width:"100",dataIndex:"version",key:"version"}],u={name:"cacheDetail",props:["cache"],data:function(){return{nodeColumns:r,nodeData:[],keyColumns:h,keyData:[]}},mounted:function(){this.fnLoadDefaultNode()},methods:{fnLoadDefaultNode:function(){var e=this,t={cacheName:this.cache.cacheName,version:this.cache.version};o.queryCacheNodeList(t,(function(t){e.nodeData=t.data.nodeList}))}}},d=u,f=a(1001),p=(0,f.Z)(d,l,s,!1,null,null,null),m=p.exports,y=[{value:"",label:"所有"},{value:"0",label:"未同步"},{value:"1",label:"同步中"},{value:"2",label:"同步完成"},{value:"3",label:"不需同步"}],C=[{title:"缓存名称",dataIndex:"cacheName",width:"20%"},{title:"缓存类型",dataIndex:"cacheType",width:"20%"},{title:"最近更新时间",dataIndex:"lastUpdateDate",width:"20%"},{title:"最近使用时间",dataIndex:"lastUseDate",width:"20%"},{title:"版本号",dataIndex:"version",width:"20%"},{title:"操作",key:"operation",align:"center",width:"100px",overflowTooltip:!1,scopedSlots:{customRender:"action"}}],b={name:"cacheMg",components:{CacheDetail:m},data:function(){var e=this;return{cacheUrl:"cacheMg/cacheMgRestService",cacheStateDict:y,cacheType:[],cacheName:"",loading:!1,cacheColumns:C,operateMenu:[{name:"编辑",onClick:function(t){e.fnEditCache(t)}}],cacheData:[],pagination:{size:10},defaultTitle:"编辑缓存",cache:{visible:!1},cacheItem:{},cacheNameTitle:""}},mounted:function(){this.fnQueryCache()},methods:{cacheQuery:function(e){},cachePageParams:function(){},onModalCancel:function(){this.cache.visible=!1,this.fnQueryCache()},fnCacheReset:function(){this.cacheName=""},fnQueryCache:function(){var e=this;o.queryCacheNameList({cacheName:this.cacheName},(function(t){e.cacheData=t.data.cacheNameList}))},fnEditCache:function(e){this.cache.visible=!0,this.cacheItem=e,this.cacheNameTitle=e.cacheName}}},g=b,v=(0,f.Z)(g,c,n,!1,null,"389b2c13",null),x=v.exports}}]);
"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[121],{46118:function(t,e,a){a.r(e),a.d(e,{default:function(){return D}});var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"fit",attrs:{id:"jobDimensionality"}},[a("ta-border-layout",{attrs:{layout:{header:"55px"},centerCfg:{showBar:!0}}},[a("template",{slot:"header"},[a("ta-breadcrumb",{staticStyle:{"line-height":"29px"},attrs:{separator:">"}},[a("ta-breadcrumb-item",[a("a",{on:{click:t.fnBackToHome}},[t._v("作业管理")])]),a("ta-breadcrumb-item",[t._v("作业维度")])],1)],1),a("div",{attrs:{slot:"centerExtraContent"},slot:"centerExtraContent"},[a("ta-alert",{attrs:{message:"当前注册中心名称："+this.zkData.appName,type:"info",showIcon:""}})],1),a("ta-tabs",{staticClass:"fit content-box"},[a("ta-tab-pane",{attrs:{tab:"作业详情"}},[a("ta-table",{attrs:{columns:t.jobColumns,dataSource:t.jobGridData,rowKey:"jobName",pagination:!1},scopedSlots:t._u([{key:"status",fn:function(e){return["OK"==e?a("ta-tag",{staticClass:"no-cursor",attrs:{color:"#00a65a "}},[t._v("正常")]):"CRASHED"==e?a("ta-tag",{staticClass:"no-cursor",attrs:{color:"#d2d6de"}},[t._v("已下线")]):"DISABLED"==e?a("ta-tag",{staticClass:"no-cursor",attrs:{color:"#f39c12"}},[t._v("已失效")]):"SHARDING_FLAG"==e?a("ta-tag",{staticClass:"no-cursor",attrs:{color:"#00c0ef"}},[t._v("分片待调整")]):t._e()]}},{key:"action",fn:function(e,o){return["OK"==o.status?a("span",[a("ta-button",{staticClass:"edit action-button-common",attrs:{size:"small"},on:{click:function(e){return t.handleShowJobDetailDrawer(o.jobName)}}},[t._v(" 修改 ")]),a("ta-button",{staticClass:"detail action-button-common",attrs:{size:"small"},on:{click:function(e){return t.routeToJobDetail(o.jobName)}}},[t._v(" 详情 ")]),a("ta-button",{staticClass:"trigger action-button-common",attrs:{size:"small"},on:{click:function(e){return t.triggerJob(o.jobName)}}},[t._v(" 触发 ")]),a("ta-button",{staticClass:"failure action-button-common",attrs:{size:"small"},on:{click:function(e){return t.disableJob(o.jobName)}}},[t._v(" 失效 ")]),a("ta-popconfirm",{attrs:{title:"确定要继续终止操作吗?",okText:"确定",cancelText:"取消",okType:"default"},on:{confirm:function(){return t.shutdownJob(o.jobName)}}},[a("ta-icon",{staticStyle:{color:"red"},attrs:{slot:"icon",type:"question-circle-o"},slot:"icon"}),a("ta-button",{staticClass:"terminate action-button-common",attrs:{size:"small"}},[t._v(" 终止 ")])],1)],1):t._e(),"CRASHED"==o.status?a("span",[a("ta-button",{staticClass:"edit action-button-common",attrs:{size:"small"},on:{click:function(e){return t.handleShowJobDetailDrawer(o.jobName)}}},[t._v(" 修改 ")]),a("ta-popconfirm",{attrs:{title:"确定要继续删除操作吗?",okText:"确定",cancelText:"取消",okType:"default"},on:{confirm:function(e){return t.removeJob(o.jobName)}}},[a("ta-icon",{staticStyle:{color:"red"},attrs:{slot:"icon",type:"question-circle-o"},slot:"icon"}),a("ta-button",{staticClass:"delete action-button-common",attrs:{size:"small"}},[t._v(" 删除 ")])],1)],1):t._e(),"DISABLED"==o.status?a("span",[a("ta-button",{staticClass:"edit action-button-common",attrs:{size:"small"},on:{click:function(t){o.jobName}}},[t._v(" 修改 ")]),a("ta-button",{staticClass:"detail action-button-common",attrs:{size:"small"},on:{click:function(e){return t.routeToJobDetail(o.jobName)}}},[t._v(" 详情 ")]),a("ta-button",{staticClass:"effect action-button-common",attrs:{size:"small"},on:{click:function(e){return t.enableJob(o.jobName)}}},[t._v(" 生效 ")]),a("ta-popconfirm",{attrs:{title:"确定要继续终止操作吗?",okText:"确定",cancelText:"取消",okType:"default"},on:{confirm:function(){return t.shutdownJob(o.jobName)}}},[a("ta-icon",{staticStyle:{color:"red"},attrs:{slot:"icon",type:"question-circle-o"},slot:"icon"}),a("ta-button",{staticClass:"terminate action-button-common",attrs:{size:"small"}},[t._v(" 终止 ")])],1)],1):t._e(),"SHARDING_FLAG"==o.status?a("span",[a("ta-button",{staticClass:"edit action-button-common",attrs:{size:"small"},on:{click:function(e){return t.handleShowJobDetailDrawer(o.jobName)}}},[t._v(" 修改 ")]),a("ta-button",{staticClass:"detail action-button-common",attrs:{size:"small"},on:{click:function(e){return t.routeToJobDetail(o.jobName)}}},[t._v(" 详情 ")]),a("ta-popconfirm",{attrs:{title:"确定要继续终止操作吗?",okText:"确定",cancelText:"取消",okType:"default"},on:{confirm:function(){return t.shutdownJob(o.jobName)}}},[a("ta-icon",{staticStyle:{color:"red"},attrs:{slot:"icon",type:"question-circle-o"},slot:"icon"}),a("ta-button",{staticClass:"terminate action-button-common",attrs:{size:"small"}},[t._v(" 终止 ")])],1)],1):t._e()]}}])})],1),a("template",{slot:"tabBarExtraContent"},[a("ta-button",{on:{click:t.fnBackToHome}},[a("ta-icon",{attrs:{type:"rollback"}}),t._v("返回")],1)],1)],2)],2),a("ta-drawer",{attrs:{title:"修改作业",width:"520",visible:t.jobDetailDrawerVisible,destroyOnClose:""},on:{close:t.handleCloseJobDetailDrawer}},[a("edit-job",{ref:"jobDetail",attrs:{initData:t.jobDetailData},on:{close:function(e){return t.handleHideJobDetailDrawer(!0)}}}),a("template",{slot:"footer"},[a("ta-button-group",[a("ta-button",{on:{click:function(e){return t.handleResetJobDetail()}}},[t._v("重置")]),a("ta-button",{attrs:{type:"primary"},on:{click:function(e){return t.handleSaveJobDetail()}}},[t._v("保存")])],1)],1)],2)],1)},r=[],l=a(47878),i=function(){var t=this,e=this,a=e.$createElement,o=e._self._c||a;return o("ta-form",{attrs:{autoFormCreate:function(e){t.form=e},layout:e.formLayout}},[o("ta-form-item",{attrs:{label:"作业名称",labelCol:e.formItemLayout.labelCol,wrapperCol:e.formItemLayout.wrapperCol,fieldDecoratorId:"jobName",fieldDecoratorOptions:{initialValue:e.initData.jobName}}},[o("ta-input",{attrs:{disabled:!0}})],1),o("ta-form-item",{attrs:{label:"作业类型",labelCol:e.formItemLayout.labelCol,wrapperCol:e.formItemLayout.wrapperCol,fieldDecoratorId:"jobType",fieldDecoratorOptions:{initialValue:e.initData.jobType}}},[o("ta-input",{attrs:{disabled:!0}})],1),o("ta-form-item",{attrs:{label:"作业实现类",labelCol:e.formItemLayout.labelCol,wrapperCol:e.formItemLayout.wrapperCol,fieldDecoratorId:"jobClass",fieldDecoratorOptions:{initialValue:e.initData.jobClass}}},[o("ta-input",{attrs:{disabled:!0}})],1),o("ta-form-item",{attrs:{label:"Cron表达式",labelCol:e.formItemLayout.labelCol,wrapperCol:e.formItemLayout.wrapperCol,fieldDecoratorId:"cron",fieldDecoratorOptions:{initialValue:e.initData.cron}}},[o("ta-input")],1),o("ta-form-item",{attrs:{label:"自定义参数",labelCol:e.formItemLayout.labelCol,wrapperCol:e.formItemLayout.wrapperCol,fieldDecoratorId:"jobParameter",fieldDecoratorOptions:{initialValue:e.initData.jobParameter}}},[o("ta-input")],1),o("ta-form-item",{attrs:{label:"分片总数",labelCol:e.formItemLayout.labelColTwo,wrapperCol:e.formItemLayout.wrapperColTwo,fieldDecoratorId:"shardingTotalCount",fieldDecoratorOptions:{initialValue:e.initData.shardingTotalCount}}},[o("ta-input-number")],1),o("ta-form-item",{attrs:{label:"监听作业端口",labelCol:e.formItemLayout.labelColTwo,wrapperCol:e.formItemLayout.wrapperColTwo,fieldDecoratorId:"monitorPort",fieldDecoratorOptions:{initialValue:e.initData.monitorPort}}},[o("ta-input-number")],1),o("ta-form-item",{attrs:{label:"时间误差秒数",labelCol:e.formItemLayout.labelCol,wrapperCol:e.formItemLayout.wrapperCol,fieldDecoratorId:"maxTimeDiffSeconds",fieldDecoratorOptions:{initialValue:e.initData.maxTimeDiffSeconds},extra:"全称：最大容忍本机与注册中心的时间误差秒数"}},[o("ta-input-number")],1),o("ta-form-item",{attrs:{label:"状态修复周期",labelCol:e.formItemLayout.labelCol,wrapperCol:e.formItemLayout.wrapperCol,fieldDecoratorId:"reconcileIntervalMinutes",fieldDecoratorOptions:{initialValue:e.initData.reconcileIntervalMinutes},extra:"全称：作业服务器状态修复周期"}},[o("ta-input-number")],1),o("ta-form-item",{attrs:{label:"监控状态",labelCol:e.formItemLayout.labelCol,wrapperCol:e.formItemLayout.wrapperCol,fieldDecoratorId:"monitorExecution",fieldDecoratorOptions:{initialValue:e.initData.monitorExecution,valuePropName:"checked"},extra:"全称：监控作业执行时状态"}},[o("ta-switch",{attrs:{checkedChildren:"是",unCheckedChildren:"否"}})],1),o("ta-form-item",{attrs:{label:"支持转移",labelCol:e.formItemLayout.labelCol,wrapperCol:e.formItemLayout.wrapperCol,fieldDecoratorId:"failover",fieldDecoratorOptions:{initialValue:e.initData.failover,valuePropName:"checked"},extra:"全称：支持自动失效转移"}},[o("ta-switch",{attrs:{checkedChildren:"是",unCheckedChildren:"否"}})],1),o("ta-form-item",{attrs:{label:"支持错过重执行",labelCol:e.formItemLayout.labelCol,wrapperCol:e.formItemLayout.wrapperCol,fieldDecoratorId:"misfire",fieldDecoratorOptions:{initialValue:e.initData.misfire,valuePropName:"checked"}}},[o("ta-switch",{attrs:{checkedChildren:"是",unCheckedChildren:"否"}})],1),o("ta-form-item",{attrs:{label:"分片对照表",labelCol:e.formItemLayout.labelCol,wrapperCol:e.formItemLayout.wrapperCol,fieldDecoratorId:"shardingItemParameters",fieldDecoratorOptions:{initialValue:e.initData.shardingItemParameters},extra:"全称：分片序列号/参数对照表"}},[o("ta-textarea",{attrs:{autosize:{minRows:2,maxRows:2}}})],1),o("ta-form-item",{attrs:{label:"策略类全路径",labelCol:e.formItemLayout.labelCol,wrapperCol:e.formItemLayout.wrapperCol,fieldDecoratorId:"jobShardingStrategyClass",fieldDecoratorOptions:{initialValue:e.initData.jobShardingStrategyClass},extra:"全称：作业分片策略实现类全路径"}},[o("ta-input")],1),o("ta-form-item",{attrs:{label:"异常类全路径",labelCol:e.formItemLayout.labelCol,wrapperCol:e.formItemLayout.wrapperCol,fieldDecoratorId:"job_exception_handler",fieldDecoratorOptions:{initialValue:e.initData.jobExceptionHandler},extra:"全称：定制异常处理类全路径"}},[o("ta-input")],1),o("ta-form-item",{attrs:{label:"线程池全路径",labelCol:e.formItemLayout.labelCol,wrapperCol:e.formItemLayout.wrapperCol,fieldDecoratorId:"executor_service_handler",fieldDecoratorOptions:{initialValue:e.initData.executorServiceHandler},extra:"全称：定制线程池全路径"}},[o("ta-input")],1),o("ta-form-item",{attrs:{label:"作业描述",labelCol:e.formItemLayout.labelCol,wrapperCol:e.formItemLayout.wrapperCol,fieldDecoratorId:"description",fieldDecoratorOptions:{initialValue:e.initData.description}}},[o("ta-input")],1)],1)},n=[],s={name:"jobDetail",props:["initData"],components:{},data:function(){return{formLayout:"horizontal",formItemLayout:{labelCol:{span:6},wrapperCol:{span:18},labelColTwo:{span:12},wrapperColTwo:{span:12}}}},methods:{fnResetJobDetail:function(){this.form.resetFields()},fnSaveJobDetail:function(){var t=this;this.form.validateFields((function(e,a){a.zkId=t.initData.zkId,e||l.Z.saveJobDetailInfo(a,(function(e){t.$message.success("更新作业信息成功"),t.$emit("close")}))}))}}},c=s,u=a(1001),b=(0,u.Z)(c,i,n,!1,null,null,null),m=b.exports,d=[{title:"作业名称",dataIndex:"jobName",width:"20%"},{title:"分片总数",dataIndex:"shardingTotalCount",width:"10%"},{title:"Cron表达式",dataIndex:"cron",width:"15%"},{title:"描述信息",dataIndex:"description",width:"25%"},{title:"状态",dataIndex:"status",scopedSlots:{customRender:"status"},width:"10%"},{title:"操作",dataIndex:"action",scopedSlots:{customRender:"action"},width:"20%",align:"center"}],f={name:"jobDimensionality",components:{editJob:m},data:function(){return{jobGridData:[],jobColumns:d,jobDetailData:{},jobDetailDrawerVisible:!1,zkData:{}}},mounted:function(){this.$route.params.zkData instanceof Object?(this.zkData=this.$route.params.zkData||{},this.loadData()):this.$router.push({name:"zookeeperRegistryCenterConfig"})},methods:{loadData:function(){var t=this,e={zkId:this.zkData.zkId,zkAddress:this.zkData.zkAddress,appNamespace:this.zkData.appNamespace};l.Z.getJobInfo(e,(function(e){t.jobGridData=e.data.jobGridData}))},handleCloseJobDetailDrawer:function(){this.handleHideJobDetailDrawer(!1)},handleHideJobDetailDrawer:function(t){this.jobDetailDrawerVisible=!1,t&&this.loadData()},handleResetJobDetail:function(){this.$refs.jobDetail.fnResetJobDetail()},handleSaveJobDetail:function(){this.$refs.jobDetail.fnSaveJobDetail()},handleShowJobDetailDrawer:function(t){var e=this,a={zkId:this.zkData.zkId,jobName:t};l.Z.getJobDetailInfo(a,(function(t){e.jobDetailData=t.data.jobDetailData,e.jobDetailData.zkId=e.zkData.zkId})),this.jobDetailDrawerVisible=!0},fnBackToHome:function(){this.$router.push({name:"zookeeperRegistryCenterConfig"})},routeToJobDetail:function(t){this.$router.push({name:"jobDetail",params:{jobDetailData:{zkData:this.zkData,jobName:t}}})},triggerJob:function(t){var e=this,a={zkId:this.zkData.zkId,jobName:t};l.Z.triggerJob(a,(function(t){e.$message.success("触发操作成功"),e.loadData()}))},disableJob:function(t){var e=this,a={zkId:this.zkData.zkId,jobName:t};l.Z.disableJob(a,(function(t){e.$message.success("失效操作成功"),e.loadData()}))},enableJob:function(t){var e=this,a={zkId:this.zkData.zkId,jobName:t};l.Z.enableJob(a,(function(t){e.$message.success("生效操作成功"),e.loadData()}))},shutdownJob:function(t){var e=this,a={zkId:this.zkData.zkId,jobName:t};l.Z.shutdownJob(a,(function(t){e.$message.success("终止操作成功"),e.loadData()}))},removeJob:function(t){var e=this;l.Z.removeJob({zkId:this.zkData.zkId,jobName:t},(function(t){e.$message.success("删除操作成功"),e.loadData()}))}}},p=f,C=(0,u.Z)(p,o,r,!1,null,"03382be6",null),D=C.exports},42371:function(t,e,a){a.r(e),a.d(e,{default:function(){return D}});var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"fit",attrs:{id:"jobDimensionality"}},[a("ta-border-layout",{attrs:{layout:{header:"70px"},centerCfg:{showBar:!1}}},[a("div",{staticClass:"center-box",attrs:{slot:"header"},slot:"header"},[a("ta-input-search",{staticClass:"search-box",attrs:{placeholder:"请输入作业名称",enterButton:"搜索"},on:{search:t.loadData},model:{value:t.searchInfo,callback:function(e){t.searchInfo=e},expression:"searchInfo"}})],1),a("ta-table",{attrs:{columns:t.jobColumns,dataSource:t.jobGridData,pagination:!1},scopedSlots:t._u([{key:"status",fn:function(e){return["OK"==e?a("ta-tag",{staticClass:"no-cursor",attrs:{color:"#00a65a "}},[t._v("正常")]):"CRASHED"==e?a("ta-tag",{staticClass:"no-cursor",attrs:{color:"#FF0000"}},[t._v("已下线")]):"DISABLED"==e?a("ta-tag",{staticClass:"no-cursor",attrs:{color:"#f39c12"}},[t._v("已失效")]):a("ta-tag",{staticClass:"no-cursor",attrs:{color:"#00c0ef"}},[t._v("分片待调整")])]}},{key:"action",fn:function(e,o){return["OK"==o.status?a("span",[a("ta-button",{staticClass:"edit action-button-common",attrs:{size:"small"},on:{click:function(e){return t.handleShowJobDetailDrawer(o.jobName)}}},[t._v(" 修改 ")]),a("ta-button",{staticClass:"detail action-button-common",attrs:{size:"small"}},[t._v(" 详情 ")]),a("ta-button",{staticClass:"trigger action-button-common",attrs:{size:"small"}},[t._v(" 触发 ")]),a("ta-button",{staticClass:"failure action-button-common",attrs:{size:"small"}},[t._v(" 失效 ")]),a("ta-button",{staticClass:"terminate action-button-common",attrs:{size:"small"}},[t._v(" 终止 ")])],1):t._e(),"CRASHED"==o.status?a("span",[a("ta-button",{staticClass:"edit action-button-common",attrs:{size:"small"}},[t._v(" 修改 ")]),a("ta-button",{staticClass:"delete action-button-common",attrs:{size:"small"}},[t._v(" 删除 ")])],1):t._e(),"DISABLED"==o.status?a("span",[a("ta-button",{staticClass:"edit action-button-common",attrs:{size:"small"}},[t._v(" 修改 ")]),a("ta-button",{staticClass:"detail action-button-common",attrs:{size:"small"}},[t._v(" 详情 ")]),a("ta-button",{staticClass:"effect action-button-common",attrs:{size:"small"}},[t._v(" 生效 ")]),a("ta-button",{staticClass:"terminate action-button-common",attrs:{size:"small"}},[t._v(" 终止 ")])],1):t._e(),"SHARDING_FLAG"==o.status?a("span",[a("ta-button",{staticClass:"edit action-button-common",attrs:{size:"small"}},[t._v(" 修改 ")]),a("ta-button",{staticClass:"detail action-button-common",attrs:{size:"small"}},[t._v(" 详情 ")]),a("ta-button",{staticClass:"terminate action-button-common",attrs:{size:"small"}},[t._v(" 终止 ")])],1):t._e()]}}])})],1),a("ta-drawer",{attrs:{title:"修改作业",width:"520",visible:t.jobDetailDrawerVisible,destroyOnClose:""},on:{close:t.handleCloseJobDetailDrawer}},[a("edit-job",{ref:"jobDetail",attrs:{initData:t.jobDetailData},on:{close:t.handleHideJobDetailDrawer}}),a("template",{slot:"footer"},[a("ta-button-group",[a("ta-button",{on:{click:function(e){return t.handleResetJobDetail()}}},[t._v("重置")]),a("ta-button",{attrs:{type:"primary"},on:{click:function(e){return t.handleSaveJobDetail()}}},[t._v("保存")])],1)],1)],2)],1)},r=[],l=a(77722),i=function(){var t=this,e=this,a=e.$createElement,o=e._self._c||a;return o("div",{attrs:{id:"jobDetail"}},[o("ta-form",{attrs:{autoFormCreate:function(e){t.form=e},layout:e.formLayout}},[o("ta-form-item",{attrs:{label:"作业名称",labelCol:e.formItemLayout.labelCol,wrapperCol:e.formItemLayout.wrapperCol,fieldDecoratorId:"jobName",fieldDecoratorOptions:{initialValue:e.initData.jobName}}},[o("ta-input",{attrs:{disabled:!0}})],1),o("ta-form-item",{attrs:{label:"作业类型",labelCol:e.formItemLayout.labelCol,wrapperCol:e.formItemLayout.wrapperCol,fieldDecoratorId:"jobType",fieldDecoratorOptions:{initialValue:e.initData.jobType}}},[o("ta-input",{attrs:{disabled:!0}})],1),o("ta-form-item",{attrs:{label:"作业实现类",labelCol:e.formItemLayout.labelCol,wrapperCol:e.formItemLayout.wrapperCol,fieldDecoratorId:"jobClass",fieldDecoratorOptions:{initialValue:e.initData.jobClass}}},[o("ta-input",{attrs:{disabled:!0}})],1),o("ta-form-item",{attrs:{label:"Cron表达式",labelCol:e.formItemLayout.labelCol,wrapperCol:e.formItemLayout.wrapperCol,fieldDecoratorId:"cron",fieldDecoratorOptions:{initialValue:e.initData.cron}}},[o("ta-input")],1),o("ta-form-item",{attrs:{label:"自定义参数",labelCol:e.formItemLayout.labelCol,wrapperCol:e.formItemLayout.wrapperCol,fieldDecoratorId:"jobParameter",fieldDecoratorOptions:{initialValue:e.initData.jobParameter}}},[o("ta-input")],1),o("ta-form-item",{attrs:{label:"分片总数",labelCol:e.formItemLayout.labelColTwo,wrapperCol:e.formItemLayout.wrapperColTwo,fieldDecoratorId:"shardingTotalCount",fieldDecoratorOptions:{initialValue:e.initData.shardingTotalCount}}},[o("ta-input-number")],1),o("ta-form-item",{attrs:{label:"监听作业端口",labelCol:e.formItemLayout.labelColTwo,wrapperCol:e.formItemLayout.wrapperColTwo,fieldDecoratorId:"monitorPort",fieldDecoratorOptions:{initialValue:e.initData.monitorPort}}},[o("ta-input-number")],1),o("ta-form-item",{attrs:{label:"时间误差秒数",labelCol:e.formItemLayout.labelCol,wrapperCol:e.formItemLayout.wrapperCol,fieldDecoratorId:"maxTimeDiffSeconds",fieldDecoratorOptions:{initialValue:e.initData.maxTimeDiffSeconds},extra:"全称：最大容忍本机与注册中心的时间误差秒数"}},[o("ta-input-number")],1),o("ta-form-item",{attrs:{label:"状态修复周期",labelCol:e.formItemLayout.labelCol,wrapperCol:e.formItemLayout.wrapperCol,fieldDecoratorId:"reconcileIntervalMinutes",fieldDecoratorOptions:{initialValue:e.initData.reconcileIntervalMinutes},extra:"全称：作业服务器状态修复周期"}},[o("ta-input-number")],1),o("ta-form-item",{attrs:{label:"监控状态",labelCol:e.formItemLayout.labelCol,wrapperCol:e.formItemLayout.wrapperCol,fieldDecoratorId:"monitorExecution",fieldDecoratorOptions:{initialValue:e.initData.monitorExecution,valuePropName:"checked"},extra:"全称：监控作业执行时状态"}},[o("ta-switch",{attrs:{checkedChildren:"是",unCheckedChildren:"否"}})],1),o("ta-form-item",{attrs:{label:"支持转移",labelCol:e.formItemLayout.labelCol,wrapperCol:e.formItemLayout.wrapperCol,fieldDecoratorId:"failover",fieldDecoratorOptions:{initialValue:e.initData.failover,valuePropName:"checked"},extra:"全称：支持自动失效转移"}},[o("ta-switch",{attrs:{checkedChildren:"是",unCheckedChildren:"否"}})],1),o("ta-form-item",{attrs:{label:"分片对照表",labelCol:e.formItemLayout.labelCol,wrapperCol:e.formItemLayout.wrapperCol,fieldDecoratorId:"shardingItemParameters",fieldDecoratorOptions:{initialValue:e.initData.shardingItemParameters},extra:"全称：分片序列号/参数对照表"}},[o("ta-textarea",{attrs:{autosize:{minRows:2,maxRows:2}}})],1),o("ta-form-item",{attrs:{label:"策略类全路径",labelCol:e.formItemLayout.labelCol,wrapperCol:e.formItemLayout.wrapperCol,fieldDecoratorId:"jobShardingStrategyClass",fieldDecoratorOptions:{initialValue:e.initData.jobShardingStrategyClass},extra:"全称：作业分片策略实现类全路径"}},[o("ta-input")],1),o("ta-form-item",{attrs:{label:"异常类全路径",labelCol:e.formItemLayout.labelCol,wrapperCol:e.formItemLayout.wrapperCol,fieldDecoratorId:"job_exception_handler",fieldDecoratorOptions:{initialValue:e.initData.jobExceptionHandler},extra:"全称：定制异常处理类全路径"}},[o("ta-input")],1),o("ta-form-item",{attrs:{label:"线程池全路径",labelCol:e.formItemLayout.labelCol,wrapperCol:e.formItemLayout.wrapperCol,fieldDecoratorId:"executor_service_handler",fieldDecoratorOptions:{initialValue:e.initData.executorServiceHandler},extra:"全称：定制线程池全路径"}},[o("ta-input")],1),o("ta-form-item",{attrs:{label:"作业描述",labelCol:e.formItemLayout.labelCol,wrapperCol:e.formItemLayout.wrapperCol,fieldDecoratorId:"description",fieldDecoratorOptions:{initialValue:e.initData.description}}},[o("ta-input")],1)],1)],1)},n=[],s={name:"jobDetail",props:["initData"],components:{},data:function(){return{formLayout:"horizontal",formItemLayout:{labelCol:{span:6},wrapperCol:{span:18},labelColTwo:{span:12},wrapperColTwo:{span:12}}}},mounted:function(){},methods:{fnResetJobDetail:function(){this.form.resetFields()},fnSaveJobDetail:function(){var t=this;this.form.validateFields((function(e,a){e||l.Z.saveJobDetailInfo(a,(function(e){t.$message.success("更新作业信息成功"),t.$emit("close")}))}))}}},c=s,u=a(1001),b=(0,u.Z)(c,i,n,!1,null,null,null),m=b.exports,d=[{title:"作业名称",dataIndex:"jobName",width:"20%"},{title:"分片总数",dataIndex:"shardingTotalCount",width:"10%"},{title:"Cron表达式",dataIndex:"cron",width:"15%"},{title:"描述信息",dataIndex:"description",width:"25%"},{title:"状态",dataIndex:"status",scopedSlots:{customRender:"status"},width:"10%"},{title:"操作",dataIndex:"action",scopedSlots:{customRender:"action"},width:"20%"}],f={name:"jobDimensionality",components:{editJob:m},data:function(){return{jobGridData:[],jobColumns:d,searchInfo:"",jobDetailData:{},jobDetailDrawerVisible:!1}},mounted:function(){this.loadData()},methods:{loadData:function(){var t=this,e={jobName:this.searchInfo};l.Z.getJobInfo(e,(function(e){t.jobGridData=e.data.jobGridData}))},onSearchJob:function(){var t=this;this.jobGridData.filter((function(e){return e.jobName!=t.searchInfo}))},handleCloseJobDetailDrawer:function(){this.handleHideJobDetailDrawer(!1)},handleHideJobDetailDrawer:function(t){this.jobDetailDrawerVisible=!1},handleResetJobDetail:function(){this.$refs.jobDetail.fnResetJobDetail()},handleSaveJobDetail:function(){this.$refs.jobDetail.fnSaveJobDetail()},handleShowJobDetailDrawer:function(t){var e=this,a={jobName:t};l.Z.getJobDetailInfo(a,(function(t){e.jobDetailData=t.data.jobDetailData})),this.jobDetailDrawerVisible=!0}}},p=f,C=(0,u.Z)(p,o,r,!1,null,"196a3763",null),D=C.exports},47878:function(t,e){e["Z"]={addZookeeperRegistryCenter:function(t,e){Base.submit(null,{url:"jobmg/elasticjob/zookeeperAddressManagementRestService/addZookeeperRegistryCenter",data:t},{successCallback:function(t){return e(t)}})},batchDeleteZookeeperRegistryCenter:function(t,e){Base.submit(null,{url:"jobmg/elasticjob/zookeeperAddressManagementRestService/deleteZookeeperRegistryCenter",data:t},{successCallback:function(t){return e(t)}})},connectZookeeperRegistryCenter:function(t,e){Base.submit(null,{url:"jobmg/elasticjob/zookeeperAddressManagementRestService/connectZookeeperRegistryCenter",data:t},{successCallback:function(t){return e(t)}})},getJobInfo:function(t,e){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/getJobInfo",data:t},{successCallback:function(t){return e(t)}})},getJobDetailInfo:function(t,e){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/getJobDetailInfo",data:t},{successCallback:function(t){return e(t)}})},saveJobDetailInfo:function(t,e){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/saveJobDetailInfo",data:t},{successCallback:function(t){return e(t)}})},getJobServerDetail:function(t,e){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/getJobServerDetail",data:t},{successCallback:function(t){return e(t)}})},disabledSharding:function(t,e){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/disabledSharding",data:t},{successCallback:function(t){return e(t)}})},effectSharding:function(t,e){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/effectSharding",data:t},{successCallback:function(t){return e(t)}})},triggerJob:function(t,e){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/triggerJob",data:t},{successCallback:function(t){return e(t)}})},disableJob:function(t,e){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/disableJob",data:t},{successCallback:function(t){return e(t)}})},enableJob:function(t,e){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/enableJob",data:t},{successCallback:function(t){return e(t)}})},shutdownJob:function(t,e){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/shutdownJob",data:t},{successCallback:function(t){return e(t)}})},removeJob:function(t,e){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/removeJob",data:t},{successCallback:function(t){return e(t)}})},getServerInfo:function(t,e){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/getServerInfo",data:t},{successCallback:function(t){return e(t)}})},disableServer:function(t,e){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/disableServer",data:t},{successCallback:function(t){return e(t)}})},enableServer:function(t,e){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/enableServer",data:t},{successCallback:function(t){return e(t)}})},shutdownServer:function(t,e){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/shutdownServer",data:t},{successCallback:function(t){return e(t)}})},removeServer:function(t,e){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/removeServer",data:t},{successCallback:function(t){return e(t)}})},getServerJobDetail:function(t,e){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/getServerJobDetail",data:t},{successCallback:function(t){return e(t)}})},disabledServerJob:function(t,e){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/disabledServerJob",data:t},{successCallback:function(t){return e(t)}})},enableServerJob:function(t,e){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/enableServerJob",data:t},{successCallback:function(t){return e(t)}})},shutdownServerJob:function(t,e){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/shutdownServerJob",data:t},{successCallback:function(t){return e(t)}})},removeServerJob:function(t,e){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/removeServerJob",data:t},{successCallback:function(t){return e(t)}})},getJobNameByZkId:function(t,e){Base.submit(null,{url:"jobmg/elasticjob/freeBusyJobManagerRestService/getJobNameByZkId",data:t},{successCallback:function(t){return e(t)}})},getServerIpsByJobName:function(t,e){Base.submit(null,{url:"jobmg/elasticjob/freeBusyJobManagerRestService/getServerIpsByJobName",data:t},{successCallback:function(t){return e(t)}})},addFreeBusyJob:function(t,e){Base.submit(null,{url:"jobmg/elasticjob/freeBusyJobManagerRestService/addFreeBusyJob",data:t},{successCallback:function(t){return e(t)}})},deleteFreeBusyJob:function(t,e){Base.submit(null,{url:"jobmg/elasticjob/freeBusyJobManagerRestService/deleteFreeBusyJob",data:t},{successCallback:function(t){return e(t)}})},getAllServerIpsByJobName:function(t,e){Base.submit(null,{url:"jobmg/elasticjob/freeBusyJobManagerRestService/getAllServerIpsByJobName",data:t},{successCallback:function(t){return e(t)}})},updateFreeBusyJob:function(t,e){Base.submit(null,{url:"jobmg/elasticjob/freeBusyJobManagerRestService/updateFreeBusyJob",data:t},{successCallback:function(t){return e(t)}})}}},77722:function(t,e){var a="/jobmg/elasticjob/jobOperateRestService/";e["Z"]={getJobInfo:function(t,e){Base.submit(null,{url:a+"getJobInfo",data:t},{successCallback:function(t){return e(t)}})},getJobDetailInfo:function(t,e){Base.submit(null,{url:a+"getJobDetailInfo",data:t},{successCallback:function(t){return e(t)}})},saveJobDetailInfo:function(t,e){Base.submit(null,{url:a+"saveJobDetailInfo",data:t},{successCallback:function(t){return e(t)}})}}}}]);
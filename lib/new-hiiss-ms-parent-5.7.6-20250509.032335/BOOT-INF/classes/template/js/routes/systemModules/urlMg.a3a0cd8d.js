"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[1932],{21393:function(e,t,a){a.r(t),a.d(t,{default:function(){return y}});var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"fit"},[a("ta-border-layout",{attrs:{"layout-type":"fixTop"}},[a("div",{staticClass:"center-box",attrs:{slot:"header"},slot:"header"},[a("ta-input-search",{staticClass:"search-box",attrs:{placeholder:"名称/URL","enter-button":"搜索"},on:{search:e.searchUrl},model:{value:e.search,callback:function(t){e.search=t},expression:"search"}})],1),a("ta-border-layout",{staticClass:"noborder",staticStyle:{padding:"5px"},attrs:{"layout-type":"fixTop"}},[a("div",{staticStyle:{"line-height":"44px",float:"right"},attrs:{slot:"header"},slot:"header"},[a("ta-button",{on:{click:e.refreshTable}},[e._v(" 刷新 ")]),a("ta-button",{attrs:{type:"primary"},on:{click:e.addNameSpace}},[e._v(" 新增 ")]),a("ta-dropdown",{attrs:{trigger:["click"]}},[a("ta-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("ta-menu-item",{attrs:{disabled:!e.selectedRowKeys.length}},[a("ta-popconfirm",{attrs:{title:"确认启用所选URL以及下级URL?","cancel-text":"取消","ok-text":"确认"},on:{confirm:function(t){return e.enableBatch(!1)}}},[a("ta-icon",{attrs:{type:"check-circle"}}),a("span",{staticClass:"mg-l12"},[e._v("启用")])],1)],1),a("ta-menu-divider"),a("ta-menu-item",{attrs:{disabled:!e.selectedRowKeys.length}},[a("ta-popconfirm",{attrs:{title:"确认禁用所选动态服务?","cancel-text":"取消","ok-text":"确认"},on:{confirm:function(t){return e.disableBatch(!1)}}},[a("ta-icon",{attrs:{type:"stop"}}),a("span",{staticClass:"mg-l12"},[e._v("禁用")])],1)],1),a("ta-menu-divider"),a("ta-menu-item",{attrs:{disabled:!e.selectedRowKeys.length},on:{click:function(t){e.deleteVisible=!0}}},[a("ta-icon",{staticStyle:{"margin-right":"20px"},attrs:{type:"close-circle"}}),e._v(" 删除 ")],1)],1),a("ta-button",[e._v(" 批量操作 "),a("ta-icon",{attrs:{type:"down"}})],1)],1)],1),a("ta-big-table",{attrs:{border:"",resizable:"","row-id":"id",height:"auto","tree-config":{lazy:!0,reserve:!0,children:"children",hasChild:"hasChild",loadMethod:e.loadChildrenMethod},"checkbox-config":{checkStrictly:!0},data:e.tableData},on:{"checkbox-change":e.onSelectChange}},[a("ta-big-table-column",{attrs:{type:"checkbox",width:"40"}}),a("ta-big-table-column",{attrs:{field:"name","tree-node":"",title:"名称",width:"200","show-overflow":"tooltip"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[a("span",{class:{"disable-color":"0"===r.effective}},[e._v(e._s(r.name))])]}}])}),a("ta-big-table-column",{attrs:{field:"url",title:"URL","min-width":"200","show-overflow":"tooltip"}}),a("ta-big-table-column",{attrs:{field:"type",title:"类型",width:"120","collection-type":"URLTYPE"}}),a("ta-big-table-column",{attrs:{field:"effective",title:"有效性",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return["1"==r.effective?a("ta-tag",{staticClass:"no-cursor",attrs:{type:"success"}},[e._v(" 有效 ")]):"0"==r.effective?a("ta-tag",{staticClass:"no-cursor",attrs:{type:"danger"}},[e._v(" 无效 ")]):e._e()]}}])}),a("ta-big-table-column",{attrs:{field:"createTime",title:"创建时间",width:"180","show-overflow":"tooltip"}}),a("ta-big-table-column",{attrs:{fixed:"right",title:"操作",field:"operation",width:"200",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return a("span",{},[a("ta-table-operate",{attrs:{"operate-menu":e.operateMenu,"row-info":t}})],1)}}])})],1)],1)],1),a("url-edit",{attrs:{visible:e.formVisible,"row-data":e.rowData},on:{close:e.closeForm,searchUrl:e.searchUrl}}),a("ta-careful-delete",{attrs:{visible:e.deleteVisible,title:"URL删除",description:"所选URL"},on:{close:function(t){e.deleteVisible=!1},delete:e.deleteBatch}})],1)},s=[],i="org/sysmg/url/",l={queryUrlByParam:function(e,t){var a={url:i+"queryByParam",data:e};Base.submit(null,a,{successCallback:function(e){return t(e)}})},queryNamespace:function(e,t){var a={url:i+"queryNamespace",data:e};Base.submit(null,a,{successCallback:function(e){return t(e)}})},queryUrlByNamespace:function(e,t){var a={url:i+"queryUrlByNamespace",data:e};Base.submit(null,a,{successCallback:function(e){return t(e)}})},saveUrl:function(e,t){var a={url:i+"saveUrl",data:e};Base.submit(null,a,{successCallback:function(e){return t(e)}})},updateUrl:function(e,t){var a={url:i+"updateUrl",data:e};Base.submit(null,a,{successCallback:function(e){return t(e)}})},deleteUrl:function(e,t){var a={url:i+"deleteBatchUrl",data:e};Base.submit(null,a,{successCallback:function(e){return t(e)}})},disableUrl:function(e,t){var a={url:i+"disableBatchUrl",data:e};Base.submit(null,a,{successCallback:function(e){return t(e)}})},enableUrl:function(e,t){var a={url:i+"enableBatchUrl",data:e};Base.submit(null,a,{successCallback:function(e){return t(e)}})}},n=function(){var e=this,t=this,a=t.$createElement,r=t._self._c||a;return r("ta-drawer",{attrs:{destroyOnClose:"",title:"动态服务",width:"500",placement:"right",closable:!0,visible:t.visible},on:{close:t.closeDrawer}},[r("ta-form",{attrs:{autoFormCreate:function(t){e.form=t}}},[r("ta-form-item",{attrs:{id:"name",label:"名称",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"name",fieldDecoratorOptions:{rules:[{required:!0,message:"请输入名称"},{max:90,message:"不能超过90个字符"}],initialValue:t.urlData.name}}},[r("ta-input",{attrs:{placeholder:"名称"}})],1),r("ta-form-item",{attrs:{label:"URL",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"url",fieldDecoratorOptions:{rules:[{required:!0,message:"请输入URL"},{max:200,message:"不能超过200个字符"},{pattern:/^[^\u4e00-\u9fa5]+$/,message:"URL不允许出现中文"}],initialValue:t.urlData.url}}},[r("ta-input",{attrs:{placeholder:"请输入URL"}})],1),"1"==t.opBody?r("ta-form-item",{attrs:{label:"命名空间",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"namespaceName",fieldDecoratorOptions:{rules:[{required:!0,message:"请输入"},{max:1024,message:"不能超过1024个字符"}],initialValue:t.urlData.namespaceName}}},[r("ta-input",{attrs:{disabled:!0}})],1):t._e()],1),r("div",{attrs:{slot:"footer"},slot:"footer"},[r("ta-button-group",[r("ta-button",{on:{click:function(e){return t.form.resetFields()}}},[t._v("重置")]),r("ta-button",{attrs:{type:"primary"},on:{click:t.handleSubmit}},[t._v("保存")])],1)],1)],1)},o=[],c={name:"edit",props:["visible","rowData"],data:function(){return{form:null,formItemLayout:{labelCol:{span:6},wrapperCol:{span:18}},isPortal:!0,urlData:{},op_add:!0,opBody:"1"}},watch:{visible:function(e){e&&this.initForm()}},methods:{initForm:function(){this.urlData=this.rowData,null!=this.urlData.id&&""!==this.urlData.id?(this.op_add=!1,""!=this.urlData.namespaceName&&null!=this.urlData.namespaceName||(this.urlData.namespaceName="无"),this.opBody=this.urlData.type):(this.op_add=!0,null!=this.urlData.namespace&&""!==this.urlData.namespace?this.opBody="1":this.opBody="0")},handleSubmit:function(){this.op_add?this.save():this.update()},save:function(){var e=this;this.form.validateFields((function(t,a){t||(a.namespace=e.urlData.namespace,l.saveUrl(a,(function(t){e.$emit("searchUrl"),e.$message.success("新增成功"),e.closeDrawer()})))}))},update:function(){var e=this;this.form.validateFields((function(t,a){t||(a.id=e.urlData.id,l.updateUrl(a,(function(t){e.$emit("searchUrl"),e.$message.success("更新成功"),e.closeDrawer()})))}))},closeDrawer:function(){this.$emit("close"),this.urlData={},this.form.resetFields()}}},u=c,d=a(1001),f=(0,d.Z)(u,n,o,!1,null,null,null),m=f.exports,h={name:"urlMg",components:{urlEdit:m},data:function(){var e=this;return{selectedRowKeys:[],selectedRows:[],search:"",expandedRowKeys:[],operateMenu:[{name:"添加下级",disabled:function(e){return"0"===e.effective},title:function(e){return"0"===e.effective?"禁用的URL不允许添加下级":""},isShow:function(e){return"0"===e.type},onClick:function(t){e.addRestUrl(t)}},{name:"编辑",disabled:function(e){return"0"===e.effective},title:function(e){return"0"===e.effective?"禁用的URL不允许编辑":""},onClick:function(t){e.update(t)}},{name:"更多",type:"more",moreMenuList:[{name:"启用",type:"confirm",confirmTitle:"确认启用该URL资源?",onOk:function(t){e.enableBatch(t)}},{name:"禁用",type:"confirm",confirmTitle:"确认禁用该URL资源?",onOk:function(t){e.disableBatch(t)}},{name:"删除",type:"confirm",confirmTitle:"确认删除该URL资源?",onOk:function(t){e.deleteUrl(t.id)}}]}],tableData:[],formVisible:!1,rowData:{},deleteVisible:!1}},mounted:function(){this.queryUrl()},methods:{searchUrl:function(){if(null!=this.search&&""!==this.search){var e={url:this.search,name:this.search};this.queryUrlByParam(e)}else this.queryNamespace()},queryUrlByParam:function(e){var t=this;l.queryUrlByParam(e,(function(e){t.tableData=e.data.urls,t.expandedRowKeys=[]}))},queryNamespace:function(){var e=this;l.queryNamespace(null,(function(t){e.tableData=t.data.urls,e.expandedRowKeys=[]}))},loadChildrenMethod:function(e){var t=e.row;return new Promise((function(e){l.queryUrlByNamespace({namespace:t.id},(function(t){return e(t.data.urls)}))}))},queryUrl:function(){this.queryNamespace()},deleteUrl:function(e){var t=this,a={ids:e};l.deleteUrl(a,(function(){t.$message.success("删除成功！"),t.deleteVisible=!1}))},deleteBatch:function(){var e=this.selectedRowKeys,t=e.join(",");this.deleteUrl(t)},update:function(e){this.rowData=e,this.openForm()},addNameSpace:function(){this.rowData={},this.openForm()},addRestUrl:function(e){this.rowData={},this.rowData.namespace=e.id,this.rowData.namespaceName=e.name,this.openForm()},disableBatch:function(e){var t=this,a=[];if(e){if("0"===e.effective)return void this.$message.warning("该记录已经禁用，请勿重复操作！");a.push(e.id)}else a=this.selectedRowKeys;var r={ids:a.join(",")};l.disableUrl(r,(function(){t.$message.success("禁用成功！")}))},enableBatch:function(e){var t=this,a=[];if(e){if("1"===e.effective)return void this.$message.warning("该记录已经启用，请勿重复操作！");a.push(e.id)}else a=this.selectedRowKeys;var r={ids:a.join(",")};l.enableUrl(r,(function(){t.$message.success("启用成功！")}))},openForm:function(){this.formVisible=!0},closeForm:function(){this.formVisible=!1},onSelectChange:function(e){var t=e.records;this.selectedRowKeys=t.map((function(e){return e.id})),this.selectedRows=t},refreshTable:function(){top.indexTool.reload()}}},p=h,b=(0,d.Z)(p,r,s,!1,null,"255149d1",null),y=b.exports}}]);
"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[6197],{38894:function(e,t,a){a.r(t),a.d(t,{default:function(){return y}});var o=function(){var e=this,t=this,a=t.$createElement,o=t._self._c||a;return o("div",{staticClass:"fit",attrs:{id:"app"}},[o("ta-border-layout",{attrs:{layout:{left:"300px"},"center-cfg":{layoutConStyle:{padding:0,border:0}}}},[o("div",{staticClass:"fit",attrs:{slot:"left"},slot:"left"},[o("ta-card",{staticStyle:{width:"100%"},attrs:{bordered:!1,title:"资源类别","body-style":{padding:0}}},t._l(t.selectData,(function(e,a){return o("a",{key:e.id,attrs:{value:e.id},on:{click:function(o){return t.selectClick(e.id,a)}}},[o("div",{staticClass:"select-item",class:{active:e.id==t.selectCategory}},[t._v(" "+t._s(e.name))])])})),0)],1),o("ta-border-layout",{attrs:{layout:{header:"70px"},"show-border":!1,"center-cfg":{showBar:!0,toolBarStyle:{height:"50px",lineHeight:"48px"}}}},[o("div",{staticClass:"center-box",attrs:{slot:"header"},slot:"header"},[o("ta-input-search",{staticClass:"search-box",attrs:{placeholder:"输入资源名称/自定义编码/所属系统","enter-button":"搜索"},on:{search:t.queryAllCustomResource},model:{value:t.param,callback:function(e){t.param=e},expression:"param"}})],1),o("div",{staticStyle:{float:"right"},attrs:{slot:"centerExtraContent"},slot:"centerExtraContent"},[o("ta-button",{staticClass:"step1",attrs:{type:"primary"},on:{click:function(e){return t.showCatagoryDrawer()}}},[t._v(" 新增授权对象类型 ")])],1),o("ta-table",{attrs:{columns:t.customResourceColumns,pagination:!1,"row-key":"customResourceId","default-expand-all-rows":"","indent-size":5,scroll:{y:"100%"},"data-source":t.gridData},scopedSlots:t._u([{key:"resourceName",fn:function(e,a){return o("span",{},[o("span",{class:{invalidStyle:"0"==a.effective}},[t._v(t._s(a.resourceName))])])}},{key:"actions",fn:function(e,a){return o("span",{},[o("ta-table-operate",{attrs:{"operate-menu":t.operateMenu}})],1)}}])})],1)],1),o("ta-drawer",{attrs:{"destroy-on-close":!0,title:"授权对象信息",width:"500",placement:"right",closable:!0,visible:t.visible,"get-container":!1},on:{close:function(e){t.visible=!1}}},[o("ta-form",{attrs:{"auto-form-create":function(t){e.form=t}}},[o("ta-form-item",{staticStyle:{display:"none"},attrs:{label:"资源ID","label-col":t.labelCol,"wrapper-col":t.wrapperCol,"field-decorator-id":"customResourceId","field-decorator-options":{initialValue:t.formData.customResourceId}}},[o("ta-input")],1),o("ta-form-item",{attrs:{label:"资源名称","label-col":t.labelCol,"wrapper-col":t.wrapperCol,"field-decorator-id":"resourceName","field-decorator-options":{initialValue:t.formData.resourceName,rules:[{required:!0,message:"请输入资源名称"},{max:450,message:"不能超过450个字符"}]}}},[o("ta-input",{attrs:{placeholder:"请输入资源名称"}})],1),o("ta-form-item",{attrs:{label:"自定义编码","label-col":t.labelCol,"wrapper-col":t.wrapperCol,"field-decorator-id":"code","field-decorator-options":{initialValue:t.formData.code,rules:[{required:!0,message:"请输入自定义编码"},{max:100,message:"不能超过100个字符"},{pattern:new RegExp("^[a-z0-9]*$"),message:"输入格式错误"}]}}},[o("ta-input",{attrs:{placeholder:"请输入自定义编码"}})],1),o("ta-form-item",{attrs:{label:"所属类别","label-col":t.labelCol,"wrapper-col":t.wrapperCol,"field-decorator-id":"resourceCategory","field-decorator-options":{initialValue:t.formData.resourceCategory,rules:[{required:!0,message:"请选择所属类别"}]}}},[o("ta-select",{attrs:{"allow-clear":"",placeholder:"请选择所属类别",disabled:""}},t._l(t.selectData,(function(e){return o("ta-select-option",{key:e.id},[t._v(" "+t._s(e.name)+" ")])})),1)],1),o("ta-form-item",{attrs:{label:"上级节点","label-col":t.labelCol,"wrapper-col":t.wrapperCol,"field-decorator-id":"parentId","field-decorator-options":{initialValue:t.formData.parentId,rules:[{required:!0,message:"请选择上级节点"}]}}},[o("ta-select",{attrs:{"allow-clear":"",placeholder:"请选择上级节点",disabled:!0}},t._l(t.selectParent,(function(e){return o("ta-select-option",{key:e.id},[t._v(" "+t._s(e.name)+" ")])})),1)],1),o("ta-form-item",{attrs:{label:"自定义资源内容","label-col":t.labelCol,"wrapper-col":t.wrapperCol,"field-decorator-id":"resourceContent","field-decorator-options":{initialValue:t.formData.resourceContent}}},[o("ta-input",{attrs:{placeholder:"请输入自定义资源内容"}})],1),o("ta-form-item",{attrs:{label:"所属系统","label-col":t.labelCol,"wrapper-col":t.wrapperCol,"field-decorator-id":"system","field-decorator-options":{initialValue:t.formData.system,rules:[{required:!0,message:"请选择所属系统"}]}}},[o("ta-select",{attrs:{"allow-clear":"",placeholder:"请选择所属系统"}},t._l(t.selectSystem,(function(e){return o("ta-select-option",{key:e.sysCode},[t._v(" "+t._s(e.name)+" ")])})),1)],1)],1),o("div",{attrs:{slot:"footer"},slot:"footer"},[o("ta-button-group",[o("ta-button",{on:{click:function(e){return t.reSetForm()}}},[t._v(" 重置 ")]),o("ta-button",{attrs:{type:"primary"},on:{click:t.saveCustomResource}},[t._v(" 保存 ")])],1)],1)],1),o("ta-drawer",{attrs:{title:"授权对象类型信息",width:"500",placement:"right",closable:!0,visible:t.categoryVisible,getContainer:!1},on:{close:function(e){t.categoryVisible=!1,t.form1.resetFields(),t.formData1={}}}},[o("ta-form",{attrs:{"auto-form-create":function(t){e.form1=t}}},[o("ta-form-item",{staticStyle:{display:"none"},attrs:{label:"类别ID","label-col":t.labelCol,"wrapper-col":t.wrapperCol,"field-decorator-id":"categoryId","field-decorator-options":{initialValue:t.formData1.categoryId}}},[o("ta-input")],1),o("ta-form-item",{attrs:{label:"类别名称","label-col":t.labelCol,"wrapper-col":t.wrapperCol,"field-decorator-id":"categoryName","field-decorator-options":{initialValue:t.formData1.categoryName,rules:[{required:!0,message:"请输入类别名称"},{max:10,message:"不能超过10个字符"}]}}},[o("ta-input",{attrs:{placeholder:"请输入类别名称"}})],1),o("ta-form-item",{attrs:{label:"类别编码","label-col":t.labelCol,"wrapper-col":t.wrapperCol,"field-decorator-id":"code","field-decorator-options":{initialValue:t.formData1.code,rules:[{required:!0,message:"请输入类别编码"},{max:100,message:"不能超过100个字符"},{pattern:new RegExp("^[a-z0-9]*$"),message:"输入格式错误"}]}}},[o("ta-input",{attrs:{placeholder:"请输入类别编码"}})],1),o("ta-form-item",{attrs:{label:"类别描述","label-col":t.labelCol,"wrapper-col":t.wrapperCol,"field-decorator-id":"categoryContent","field-decorator-options":{initialValue:t.formData1.categoryContent,rules:[{required:!0,message:"请输入类别描述"},{max:1024,message:"不能超过1024个字符"}]}}},[o("ta-input",{attrs:{placeholder:"请输入类别描述"}})],1),o("ta-form-item",{attrs:{label:"有效标识","label-col":t.labelCol,"wrapper-col":t.wrapperCol,"field-decorator-id":"effective","field-decorator-options":{valuePropName:"checked",initialValue:t.formData1.effective}}},[o("ta-switch",{attrs:{"checked-children":"有效","un-checked-children":"无效"},on:{change:t.onchange}})],1)],1),o("div",{attrs:{slot:"footer"},slot:"footer"},[o("ta-button-group",[o("ta-button",{on:{click:function(e){return t.reSetForm()}}},[t._v(" 重置 ")]),o("ta-button",{attrs:{type:"primary"},on:{click:t.saveCatagory}},[t._v(" 保存 ")])],1)],1)],1)],1)},r=[],s="/org/sysmg/customResourceManagementRestService/",c={queryALLTaResourceCategory:function(e,t){Base.submit(null,{url:"org/sysmg/resourceCategoryManagementRestService/queryALLTaResourceCategory",data:e},{successCallback:function(e){return t(e)}})},queryALLTaCustomResourceTreeByCategoryId:function(e,t){Base.submit(null,{url:s+"queryALLTaCustomResourceTreeByCategoryId",data:e},{successCallback:function(e){return t(e)}})},queryAccessSystemByParam:function(e,t){Base.submit(null,{url:"org/sysmg/accessSystemManagementRestService/queryEffectiveAccessSystem",data:e},{successCallback:function(e){return t(e)}})},queryALLTaCustomResourceParent:function(e,t){Base.submit(null,{url:s+"queryALLTaCustomResourceParent",data:e},{successCallback:function(e){return t(e)}})},addTaResourceCategory:function(e,t){Base.submit(null,{url:"org/sysmg/resourceCategoryManagementRestService/addTaResourceCategory",data:e},{successCallback:function(e){return t(e)}})},updateTaResourceCategoryByCategoryId:function(e,t){Base.submit(null,{url:"org/sysmg/resourceCategoryManagementRestService/updateTaResourceCategoryByCategoryId",data:e},{successCallback:function(e){return t(e)}})},addTaCustomResource:function(e,t){Base.submit(null,{url:s+"addTaCustomResource",data:e},{successCallback:function(e){return t(e)}})},updateTaCustomResourceByCustomResourceId:function(e,t){Base.submit(null,{url:s+"updateTaCustomResourceByCustomResourceId",data:e},{successCallback:function(e){return t(e)}})},deleteTaCustomResourceByCustomResourceId:function(e,t){Base.submit(null,{url:s+"deleteTaCustomResourceByCustomResourceId",data:e},{successCallback:function(e){return t(e)}})},deleteTaResourceCategoryByCategoryId:function(e,t){Base.submit(null,{url:"org/sysmg/resourceCategoryManagementRestService/deleteTaResourceCategoryByCategoryId",data:e},{successCallback:function(e){return t(e)}})},queryALLTaCustomResourceTree:function(e,t){Base.submit(null,{url:s+"queryALLTaCustomResourceTree",data:e},{successCallback:function(e){return t(e)}})}},l=a(80790),i=[],u=[{title:"资源名称",dataIndex:"resourceName",key:"resourceName",width:"30%",overflowTooltip:!0,scopedSlots:{customRender:"resourceName"}},{title:"分类",dataIndex:"categoryName",key:"categoryName",align:"center",width:"15%",overflowTooltip:!0},{title:"自定义编码",dataIndex:"code",key:"code",align:"center",width:"15%",overflowTooltip:!0},{title:"自定义资源内容",dataIndex:"resourceContent",key:"resourceContent",align:"center",width:"20%",overflowTooltip:!0},{title:"所属系统",dataIndex:"systemName",key:"systemName",align:"center",width:"15%",overflowTooltip:!0},{title:"操作",key:"actions",align:"center",width:205,scopedSlots:{customRender:"actions"}}],n={name:"customResource",mixins:[l.Z],data:function(){var e=this;return{gridData:i,customResourceColumns:u,operateMenu:[{name:"新增下级",disabled:function(e){return"0"==e.effective},title:function(e){return"0"==e.effective?"禁用的顶级自定义授权类型不能新增下级":""},onClick:function(t){e.toAddResource(t)}},{name:"编辑",disabled:function(e){return"0"==e.effective},title:function(e){return"0"==e.effective?"禁用的顶级自定义授权类型不能编辑":""},onClick:function(t){e.toUpdateResource(t)}},{name:"删除",type:"confirm",isShow:function(e){return"resource"==e.type},confirmTitle:"确定删除该授权对象?",onOk:function(t){e.toDeleteResource(t)}},{name:"更多",type:"more",isShow:function(e){return"category"==e.type},moreMenuList:[{name:"启用",type:"confirm",confirmTitle:"确认启用该账户?",onOk:function(t){var a={categoryId:t.customResourceId,categoryName:t.categoryName,code:t.code,categoryContent:t.categoryContent,effective:"1"};"1"!==t.effective?c.updateTaResourceCategoryByCategoryId(a,(function(t){e.$message.success("启用成功"),e.queryALLTaCustomOrgCategory(),e.selectClick(e.selectCategory),e.queryAllCustomResourcePatent()})):e.$message.warn("该资源已经为启用状态")}},{name:"禁用",type:"confirm",confirmTitle:"确认禁用该账户?",onOk:function(t){var a={categoryId:t.customResourceId,categoryName:t.categoryName,code:t.code,categoryContent:t.categoryContent,effective:"0"};"0"!==t.effective?c.updateTaResourceCategoryByCategoryId(a,(function(t){e.$message.success("禁用成功"),e.queryALLTaCustomOrgCategory(),e.selectClick(e.selectCategory),e.queryAllCustomResourcePatent()})):e.$message.warn("该资源已经为禁用状态")}},{name:"删除",type:"confirm",confirmTitle:"确认删除该用户?",onOk:function(t){e.toDeleteResource(t)}}]}],selectData:[],selectSystem:[],selectParent:[],selectCategory:"",param:"",visible:!1,categoryVisible:!1,wrapperCol:{span:17},labelCol:{span:7},clickData:{},form:null,form1:null,formData:{},formData1:{}}},mounted:function(){this.queryALLTaCustomOrgCategory(!0),this.queryALLTaAccessSystem(),this.queryAllCustomResourcePatent();var e=[{element:".step1",popover:{title:"新增授权对象类型",description:"自定义资源用于业务系统权限功能拓展，新增的自定义资源可以像普通功能资源一样被角色权限控制",position:"left"}}];this.fnCommonGuide(e)},methods:{queryALLTaCustomOrgCategory:function(e){var t=this;c.queryALLTaResourceCategory({},(function(a){var o=a.data.customResourceList;t.selectData=[],o.length&&(o.forEach((function(e){t.selectData.push({id:e.categoryId,name:e.categoryName})})),e&&(t.selectCategory=t.selectData[0].id,t.selectClick(t.selectData[0].id)))}))},selectClick:function(e){var t=this;this.selectCategory=e,e?c.queryALLTaCustomResourceTreeByCategoryId({categoryId:e,param:this.param},(function(e){t.gridData=e.data.customResourceTree})):this.queryAllCustomResource()},queryALLTaAccessSystem:function(){var e=this;c.queryAccessSystemByParam({},(function(t){var a=t.data.list;a&&a.length&&a.forEach((function(t){e.selectSystem.push({sysCode:t.sysCode,name:t.sysName})}))}))},queryAllCustomResourcePatent:function(){var e=this;c.queryALLTaCustomResourceParent({},(function(t){var a=t.data.parentList;e.selectParent=[],a.length&&a.forEach((function(t){e.selectParent.push({id:t.customResourceId,name:t.resourceName})}))}))},reSetForm:function(){var e,t,a=this.clickData;this.visible&&(null===(e=this.form)||void 0===e||e.resetFields(),this.form.getFieldValue("customResourceId")||(this.formData={parentId:a.customResourceId,resourceCategory:"resource"==a.type?a.resourceCategory:a.customResourceId}));this.categoryVisible&&(null===(t=this.form1)||void 0===t||t.resetFields())},saveCatagory:function(){var e=this.clickData;e.type?this.updateCategory():this.addCategory()},showCatagoryDrawer:function(){this.categoryVisible=!0,this.clickData={},this.formData1={categoryId:"",categoryName:"",categoryContent:"",code:"",effective:!0},this.reSetForm()},addCategory:function(){var e=this;this.form1.validateFields((function(t,a){t||(a.effective=e.form1.getFieldValue("effective")?"1":"0",c.addTaResourceCategory(a,(function(t){e.$message.success("新增对象类型成功"),e.queryALLTaCustomOrgCategory(),e.selectCategory=t.data.categoryId,e.selectClick(e.selectCategory),e.queryAllCustomResourcePatent(),e.categoryVisible=!1})))}))},updateCategory:function(){var e=this;this.form1.validateFields((function(t,a){if(!t){if(!e.form1.getFieldValue("categoryId"))return void e.$message.warn("授权对象类型ID不能为空");a.effective=e.form1.getFieldValue("effective")?"1":"0",c.updateTaResourceCategoryByCategoryId(a,(function(t){e.$message.success("修改成功"),e.queryALLTaCustomOrgCategory(),e.selectClick(e.selectCategory),e.queryAllCustomResourcePatent(),e.categoryVisible=!1}))}}))},onchange:function(e){this.form1.setFieldsValue({effective:1==e?"1":"0"})},toAddResource:function(e){this.clickData=e,this.visible=!0,this.formData={parentId:e.customResourceId,resourceCategory:"resource"==e.type?e.resourceCategory:e.customResourceId}},saveCustomResource:function(){this.form.getFieldValue("customResourceId")?this.updateResource():this.addResource()},addResource:function(){var e=this;this.form.validateFields((function(t,a){t||c.addTaCustomResource(a,(function(t){e.$message.success("保存成功"),e.queryALLTaCustomOrgCategory(),e.selectClick(e.selectCategory),e.queryAllCustomResourcePatent(),e.form.resetFields(),e.visible=!1}))}))},updateResource:function(){var e=this;this.form.validateFields((function(t,a){t||c.updateTaCustomResourceByCustomResourceId(a,(function(t){e.$message.success("修改成功"),e.queryALLTaCustomOrgCategory(),e.selectClick(e.selectCategory),e.form.resetFields(),e.queryAllCustomResourcePatent(),e.visible=!1}))}))},toDeleteResource:function(e){"resource"==e.type?this.deleteResource(e):"category"==e.type&&this.deleteCategory(e)},deleteResource:function(e){var t=this;c.deleteTaCustomResourceByCustomResourceId({customResourceId:e.customResourceId},(function(e){t.$message.success("删除成功"),t.queryALLTaCustomOrgCategory(),t.selectClick(t.selectCategory),t.queryAllCustomResourcePatent()}))},deleteCategory:function(e){var t=this;c.deleteTaResourceCategoryByCategoryId({categoryId:e.customResourceId},(function(e){t.$message.success("删除成功"),t.queryALLTaCustomOrgCategory(!0),t.queryAllCustomResourcePatent(),t.gridData=[]}))},toUpdateResource:function(e){this.clickData=e;var t=e.type,a=e.customResourceId,o=e.resourceCategory,r=e.resourceName,s=e.parentId,c=e.resourceContent,l=e.code,i=e.effective,u=e.system,n=e.categoryContent;"resource"==t?(this.visible=!0,this.formData={customResourceId:a,resourceCategory:o,resourceName:r,parentId:s,resourceContent:c,code:l,effective:i,system:u}):"category"==t&&(this.categoryVisible=!0,this.formData1={categoryId:a,categoryName:r,categoryContent:n,code:l,effective:"1"==i},this.form1.resetFields())},queryAllCustomResource:function(){var e=this,t={param:this.param,selectCategory:this.selectCategory};c.queryALLTaCustomResourceTree(t,(function(t){e.gridData=t.data.customResourceTree}))}}},d=n,m=a(1001),f=(0,m.Z)(d,o,r,!1,null,"51fb55c5",null),y=f.exports},80790:function(e,t,a){var o=a(76698);a(34530);t["Z"]={activated:function(){var e=window.pageVmObj,t=e["steps_"+e._route.name];this.methods?this.methods.fnCommonGuide(t):this.fnCommonGuide(t)},deactivated:function(){this.methods?this.methods.fnCommonGuide([],!0):this.fnCommonGuide([],!0)},methods:{fnCommonGuide:function(e,t){var a=window.pageVmObj;t?(a.driver.reset(),window.fnPageGuide=null):(a["steps_"+a._route.name]=e,a.driver=new o.Z({allowClose:!1}),window.fnPageGuide=function(){a.driver.defineSteps(e),a.driver.start()})}}}}}]);
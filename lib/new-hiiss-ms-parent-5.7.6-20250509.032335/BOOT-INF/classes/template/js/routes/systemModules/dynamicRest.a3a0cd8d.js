"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[5074],{97063:function(e,t,a){a.r(t),a.d(t,{default:function(){return w}});var i=function(){var e=this,t=this,a=t.$createElement,i=t._self._c||a;return i("div",{staticClass:"fit"},[i("ta-border-layout",{attrs:{layout:{header:"70px",footer:"70px"},centerCfg:{showBar:!0},"footer-cfg":{showBorder:!1}}},[i("div",{staticClass:"center-box",attrs:{slot:"header"},slot:"header"},[i("ta-input-search",{staticClass:"search-box",attrs:{placeholder:"服务ID/服务名称/sql",enterButton:"搜索"},on:{search:t.queryDynamic},model:{value:t.search,callback:function(e){t.search=e},expression:"search"}})],1),i("div",{staticStyle:{"line-height":"44px",float:"right"},attrs:{slot:"centerExtraContent"},slot:"centerExtraContent"},[i("ta-button",{attrs:{type:"primary"},on:{click:t.openForm}},[t._v("新增")]),i("ta-dropdown",{attrs:{trigger:["click"]}},[i("ta-menu",{attrs:{slot:"overlay"},slot:"overlay"},[i("ta-menu-item",{attrs:{disabled:!t.selectedRowKeys.length}},[i("ta-popconfirm",{attrs:{title:"确认启用所选动态服务?"},on:{confirm:function(e){return t.enable(!1)}}},[i("ta-icon",{attrs:{type:"check-circle"}}),i("span",{staticClass:"mg-l12"},[t._v("启用")])],1)],1),i("ta-menu-divider"),i("ta-menu-item",{attrs:{disabled:!t.selectedRowKeys.length}},[i("ta-popconfirm",{attrs:{title:"确认禁用所选动态服务?"},on:{confirm:function(e){return t.disable(!1)}}},[i("ta-icon",{attrs:{type:"stop"}}),i("span",{staticClass:"mg-l12"},[t._v("禁用")])],1)],1),i("ta-menu-divider"),i("ta-menu-item",{attrs:{disabled:!t.selectedRowKeys.length},on:{click:function(e){t.deleteVisible=!0}}},[i("ta-icon",{staticStyle:{"margin-right":"20px"},attrs:{type:"close-circle"}}),t._v("删除 ")],1)],1),i("ta-button",[t._v(" 批量操作 "),i("ta-icon",{attrs:{type:"down"}})],1)],1)],1),i("ta-table",{attrs:{columns:t.columns,dataSource:t.tableData,rowKey:"id",rowSelection:{selectedRowKeys:t.selectedRowKeys,onChange:t.onSelectChange},pagination:!1},scopedSlots:t._u([{key:"restName",fn:function(e,a){return i("span",{class:{"disable-color":"0"===a.effective}},[t._v(t._s(e))])}},{key:"effective",fn:function(e){return i("span",{},[t._v(t._s(t.CollectionLabel("EFFECTIVE",e)))])}},{key:"operation",fn:function(e,a,s){return i("span",{},[i("ta-table-operate",{attrs:{operateMenu:t.operateMenu}})],1)}}])}),i("div",{attrs:{slot:"footer"},slot:"footer"},[i("ta-pagination",{ref:"dynamicGridPager",staticStyle:{float:"right","margin-top":"10px"},attrs:{showSizeChanger:"",showQuickJumper:"",dataSource:t.tableData,defaultPageSize:10,params:t.pageParams,url:"dynamic/rest/queryList"},on:{"update:dataSource":function(e){t.tableData=e},"update:data-source":function(e){t.tableData=e}}})],1)],1),i("dynamic-rest-edit",{attrs:{visible:t.formVisible,"row-data":t.rowData},on:{close:t.closeForm,queryTable:t.queryDynamic}}),i("ta-modal",{attrs:{centered:"",footer:null,width:"800px",destroyOnClose:!0},model:{value:t.contentVisible,callback:function(e){t.contentVisible=e},expression:"contentVisible"}},[i("ta-form",{attrs:{autoFormCreate:function(t){e.form=t},layout:t.formLayout}},[i("ta-form-item",{staticStyle:{"margin-bottom":"0px"},attrs:{label:"返回数据"}},[i("ta-textarea",{attrs:{autosize:{minRows:20,maxRows:20},readonly:"readonly"},model:{value:t.content,callback:function(e){t.content=e},expression:"content"}})],1)],1)],1),i("ta-careful-delete",{attrs:{visible:t.deleteVisible,title:"动态服务删除",description:"选中动态服务"},on:{close:function(e){t.deleteVisible=!1},delete:t.deleteBatch}})],1)},s=[],n=function(){var e=this,t=this,a=t.$createElement,i=t._self._c||a;return i("ta-drawer",{attrs:{destroyOnClose:"",title:"动态服务",width:"500",placement:"right",closable:!0,visible:t.visible},on:{close:t.closeDrawer}},[i("ta-form",{attrs:{autoFormCreate:function(t){e.form=t}}},[i("ta-form-item",{attrs:{id:"sysName",label:"服务名称",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"restName",fieldDecoratorOptions:{rules:[{required:!0,message:"请输入服务名称"},{max:10,message:"不能超过10个字符"}],initialValue:t.dynamicData.restName}}},[i("ta-input",{attrs:{placeholder:"服务名称"}})],1),i("ta-form-item",{attrs:{label:"服务ID",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"restId",fieldDecoratorOptions:{rules:[{required:!0,message:"请输入服务ID"},{max:36,message:"不能超过36个字符"},{pattern:new RegExp("^[a-z0-9]*$"),message:"输入格式错误,只能有小写字母或数字"}],initialValue:t.dynamicData.restId}}},[i("ta-input",{attrs:{placeholder:"请输入服务ID"}})],1),i("ta-form-item",{attrs:{label:"SQL",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"sql",fieldDecoratorOptions:{rules:[{required:!0,message:"请输入SQL"},{max:1024,message:"不能超过1024个字符"}],initialValue:t.dynamicData.sql}}},[i("ta-textarea")],1),i("ta-form-item",{attrs:{label:"数据源",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"dsName",fieldDecoratorOptions:{initialValue:t.dynamicData.dsName,rules:[{required:!0,message:"请选择执行数据源"}]}}},[i("ta-select",{attrs:{allowClear:"",placeholder:"请选择数据源"}},t._l(t.dsList,(function(e){return i("ta-select-option",{key:e},[t._v(t._s(e))])})),1)],1)],1),i("div",{attrs:{slot:"footer"},slot:"footer"},[i("ta-button-group",[i("ta-button",{on:{click:function(e){return t.form.resetFields()}}},[t._v("重置")]),i("ta-button",{attrs:{type:"primary"},on:{click:t.handleSubmit}},[t._v("保存")])],1)],1)],1)},o=[],r="dynamic/rest/",l="rest/",c={queryDsList:function(e,t,a){var i={url:r+"queryDsList"};Base.submit(e,i,{successCallback:function(e){return a(e)}})},update:function(e,t,a){var i={url:r+"update",data:t};Base.submit(e,i,{successCallback:function(e){return a(e)}})},save:function(e,t,a){var i={url:r+"add",data:t};Base.submit(e,i,{successCallback:function(e){return a(e)}})},deleteBatch:function(e,t,a){var i={url:r+"delete",data:t};Base.submit(e,i,{successCallback:function(e){return a(e)}})},enable:function(e,t,a){var i={url:r+"enable",data:t};Base.submit(e,i,{successCallback:function(e){return a(e)}})},disable:function(e,t,a){var i={url:r+"disable",data:t};Base.submit(e,i,{successCallback:function(e){return a(e)}})},excutor:function(e,t,a){var i={url:l+t};Base.submit(e,i,{successCallback:function(e){return a(e)}})}},u={name:"edit",props:["visible","rowData"],data:function(){return{form:null,formItemLayout:{labelCol:{span:6},wrapperCol:{span:18}},isPortal:!0,dynamicData:{},op_add:!0,dsList:[]}},watch:{visible:function(e){e&&this.initForm()}},methods:{initForm:function(){this.dynamicData=this.rowData,this.queryDsList(),null!=this.dynamicData.id&&""!==this.dynamicData.id&&(this.op_add=!1)},queryDsList:function(){var e=this;c.queryDsList(this,null,(function(t){e.dsList=t.data.dsList}))},handleSubmit:function(){this.op_add?this.save():this.update()},save:function(){var e=this;this.form.validateFields((function(t,a){t||c.save(e,a,(function(t){e.$emit("queryTable"),e.$message.success("新增成功"),e.closeDrawer()}))}))},update:function(){var e=this;this.form.validateFields((function(t,a){t||(a.id=e.dynamicData.id,c.update(e,a,(function(t){e.$emit("queryTable"),e.$message.success("更新成功"),e.closeDrawer()})))}))},closeDrawer:function(){this.$emit("close"),this.dynamicData={},this.form.resetFields()}}},d=u,m=a(1001),f=(0,m.Z)(d,n,o,!1,null,null,null),p=f.exports,h=[{title:"服务名称",dataIndex:"restName",width:"17%",overflowTooltip:!0,scopedSlots:{customRender:"restName"}},{title:"服务SQL",width:"17%",overflowTooltip:!0,dataIndex:"sql",scopedSlots:{customRender:"sql"}},{title:"服务ID",width:"20%",overflowTooltip:!0,dataIndex:"restId",scopedSlots:{customRender:"restId"}},{title:"数据源",dataIndex:"dsName",width:"16%",overflowTooltip:!0,scopedSlots:{customRender:"dsName"}},{title:"有效性",dataIndex:"effective",width:"10%",yesOrNoTag:!0,scopedSlots:{customRender:"effective"}},{title:"操作",align:"center",width:"20%",dataIndex:"operation",scopedSlots:{customRender:"operation"}}],b={name:"dynamicRest",data:function(){var e=this;return{formLayout:"vertical",content:"",contentVisible:!1,search:"",formVisible:!1,columns:h,operateMenu:[{name:"访问",disabled:function(e){return"0"==e.effective},title:function(e){return"0"==e.effective?"禁用的服务不允许访问":""},onClick:function(t){e.visit(t)}},{name:"编辑",disabled:function(e){return"0"==e.effective},title:function(e){return"0"==e.effective?"禁用的服务不允许访问":""},onClick:function(t){e.openForm(t)}},{name:"更多",type:"more",moreMenuList:[{name:"启用",type:"confirm",confirmTitle:"确定启用该动态服务?",onOk:function(t){e.enable(t)}},{name:"禁用",type:"confirm",confirmTitle:"确定禁用该动态服务?",onOk:function(t){e.disable(t)}},{name:"删除",type:"confirm",confirmTitle:"确定删除该动态服务?",onOk:function(t){e.handleDelete(t.id)}}]}],tableData:[],rowData:{},selectedRowKeys:[],selectRows:[],deleteVisible:!1}},components:{dynamicRestEdit:p},computed:{},mounted:function(){this.queryDynamic()},methods:{openForm:function(e){this.formVisible=!0,void 0===e||null==e.id?(this.rowData={},this.rowData.restId=this.generateUUID()):this.rowData=e},pageParams:function(){var e=this.search;return null===e||""===e?{}:{param:e}},closeForm:function(){this.formVisible=!1},onSelectChange:function(e,t){this.selectedRowKeys=e,this.selectedRows=t},queryDynamic:function(){this.$refs.dynamicGridPager.loadData()},deleteBatch:function(){var e=this;0===this.selectedRowKeys.length&&this.$message.warning("请选择需要删除的动态服务列表。"),this.$confirm({title:"删除服务",content:"确认删除所选服务列表吗?",cancelText:"取消",okText:"确认",onOk:function(){var t=e.selectedRowKeys.join(",");e.delete(t)}})},handleDelete:function(e){this.delete(e)},delete:function(e){var t=this;c.deleteBatch(null,{ids:e},(function(e){t.queryDynamic(),t.deleteVisible=!1,t.$message.success("删除成功")}))},enable:function(e){var t,a=this;if(e){if("1"==e.effective)return void this.$message.warning("该记录已经启用，请勿重复操作！");t=e.id}else t=this.selectedRowKeys.join(",");c.enable(this,{ids:t},(function(e){a.queryDynamic(),a.$message.success("启用成功")}))},disable:function(e){var t,a=this;if(e){if("0"==e.effective)return void this.$message.warning("该记录已经禁用，请勿重复操作！");t=e.id}else t=this.selectedRowKeys.join(",");c.disable(this,{ids:t},(function(e){a.queryDynamic(),a.$message.success("禁用成功")}))},visit:function(e){var t=this;c.excutor(this,e.restId,(function(e){t.contentVisible=!0,t.content=JSON.stringify(e,null,4)}))},generateUUID:function(){function e(){return(65536*(1+Math.random())|0).toString(16).substring(1)}return e()+e()+e()+e()+e()+e()+e()+e()}}},y=b,v=(0,m.Z)(y,i,s,!1,null,"4c742541",null),w=v.exports}}]);
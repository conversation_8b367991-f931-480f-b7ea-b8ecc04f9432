"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[6179],{50497:function(t,e,a){a.r(e),a.d(e,{default:function(){return d}});var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"fit"},[a("ta-tabs",{attrs:{tabPosition:"right",defaultActiveKey:"notice",type:"card"}},[a("ta-tab-pane",{key:"notice",staticStyle:{overflow:"hidden"}},[a("span",{attrs:{slot:"tab"},slot:"tab"},[a("ta-icon",{attrs:{type:"mail"}}),t._v("通知")],1),a("ta-tabs",{on:{change:t.noticeTabChange},model:{value:t.activeNotice,callback:function(e){t.activeNotice=e},expression:"activeNotice"}},[a("ta-tab-pane",{key:"1",attrs:{tab:"未读消息"}},[a("div",{staticClass:"msg-item msg-item-notice"},[t.noticeList1.length>0?a("div",[a("ta-collapse",{on:{change:t.noticeDetailChange}},t._l(t.noticeList1,(function(e){return a("ta-collapse-panel",{key:e.mId},[a("template",{slot:"header"},["01"==e.type?a("ta-icon",{staticStyle:{color:"#ca1064"},attrs:{type:"sound"}}):0==e.type.indexOf("02")?a("ta-icon",{staticStyle:{color:"#04b2dc"},attrs:{type:"form"}}):"03"==e.type?a("ta-icon",{staticStyle:{color:"#108ee9"},attrs:{type:"mail"}}):t._e(),a("span",{staticClass:"msg-title"},[t._v(t._s(e.title))]),a("span",{staticClass:"msg-info"},[t._v("from: "+t._s(e.senderName)+"-"+t._s(e.sendDate))])],1),a("p",{staticClass:"msg-detail-detail",domProps:{innerHTML:t._s(e.content)}},[t._v(t._s(e.content))]),a("div",{directives:[{name:"show",rawName:"v-show",value:null!=e.noticeFileList&&e.noticeFileList.length>=1,expression:"item.noticeFileList!=null && item.noticeFileList.length>=1"}]},[a("ta-divider",{attrs:{orientation:"left"}},[t._v("附件")]),a("ta-list",{attrs:{itemLayout:"horizontal",dataSource:e.noticeFileList},scopedSlots:t._u([{key:"renderItem",fn:function(e,i){return a("ta-list-item",{},[a("a",{attrs:{href:t.backUrl+"/message/downloadNoticeFile?annexId="+e.annexId}},[a("ta-icon",{staticStyle:{color:"#0f990f"},attrs:{type:"download"}}),t._v(" "+t._s(e.annexName))],1)])}}],null,!0)})],1)],2)})),1)],1):a("div",{staticStyle:{"text-align":"center"}},[t._v(" 没有通知 ")])])]),a("ta-tab-pane",{key:"2",attrs:{tab:"全部消息"}},[a("div",{staticClass:"msg-item msg-item-notice"},[t.noticeList2.length>0?a("div",[a("ta-collapse",{on:{change:t.noticeDetailChange}},t._l(t.noticeList2,(function(e){return a("ta-collapse-panel",{key:e.mId},[a("template",{slot:"header"},["01"==e.type?a("ta-icon",{staticStyle:{color:"#ca1064"},attrs:{type:"sound"}}):0==e.type.indexOf("02")?a("ta-icon",{staticStyle:{color:"#04b2dc"},attrs:{type:"form"}}):"03"==e.type?a("ta-icon",{staticStyle:{color:"#108ee9"},attrs:{type:"mail"}}):t._e(),a("span",{staticClass:"msg-title"},[t._v(t._s(e.title))]),a("span",{staticClass:"msg-info"},[t._v("from: "+t._s(e.senderName)+"-"+t._s(e.sendDate))])],1),a("p",{staticClass:"msg-detail-detail",domProps:{innerHTML:t._s(e.content)}},[t._v(t._s(e.content))]),a("div",{directives:[{name:"show",rawName:"v-show",value:null!=e.noticeFileList&&e.noticeFileList.length>=1,expression:"item.noticeFileList!=null && item.noticeFileList.length>=1"}]},[a("ta-divider",{attrs:{orientation:"left"}},[t._v("附件")]),a("ta-list",{attrs:{itemLayout:"horizontal",dataSource:e.noticeFileList},scopedSlots:t._u([{key:"renderItem",fn:function(e,i){return a("ta-list-item",{},[a("a",{attrs:{href:t.backUrl+"/message/downloadNoticeFile?annexId="+e.annexId}},[a("ta-icon",{staticStyle:{color:"#0f990f"},attrs:{type:"download"}}),t._v(" "+t._s(e.annexName))],1)])}}],null,!0)})],1)],2)})),1)],1):a("div",{staticStyle:{"text-align":"center"}},[t._v(" 没有通知 ")])])]),a("ta-tab-pane",{key:"3",attrs:{tab:"已发消息记录"}},[a("div",{staticClass:"msg-item msg-item-notice"},[t.noticeList3.length>0?a("div",[a("ta-collapse",{on:{change:t.noticeDetailChange}},t._l(t.noticeList3,(function(e){return a("ta-collapse-panel",{key:e.mId},[a("template",{slot:"header"},["01"==e.type?a("ta-icon",{staticStyle:{color:"#ca1064"},attrs:{type:"sound"}}):0==e.type.indexOf("02")?a("ta-icon",{staticStyle:{color:"#04b2dc"},attrs:{type:"form"}}):"03"==e.type?a("ta-icon",{staticStyle:{color:"#108ee9"},attrs:{type:"mail"}}):t._e(),a("span",{staticClass:"msg-title"},[t._v(t._s(e.title))]),a("span",{staticClass:"msg-info"},[t._v(t._s(e.sendDate))])],1),a("p",{staticClass:"msg-detail-detail",domProps:{innerHTML:t._s(e.content)}},[t._v(t._s(e.content))]),a("div",{directives:[{name:"show",rawName:"v-show",value:null!=e.noticeFileList&&e.noticeFileList.length>=1,expression:"item.noticeFileList!=null && item.noticeFileList.length>=1"}]},[a("ta-divider",{attrs:{orientation:"left"}},[t._v("附件")]),a("ta-list",{attrs:{itemLayout:"horizontal",dataSource:e.noticeFileList},scopedSlots:t._u([{key:"renderItem",fn:function(e,i){return a("ta-list-item",{},[a("a",{attrs:{href:t.backUrl+"/message/downloadNoticeFile?annexId="+e.annexId}},[a("ta-icon",{staticStyle:{color:"#0f990f"},attrs:{type:"download"}}),t._v(" "+t._s(e.annexName))],1)])}}],null,!0)})],1)],2)})),1)],1):a("div",{staticStyle:{"text-align":"center"}},[t._v(" 没有通知 ")])])])],1),a("div",{staticClass:"msg-type-select"},[a("ta-select",{staticStyle:{width:"120px"},attrs:{defaultValue:"00"},on:{change:t.noticeTypeChange}},[a("ta-select-option",{attrs:{value:"00"}},[t._v("全部")]),a("ta-select-option",{attrs:{value:"01"}},[t._v("系统通知")]),a("ta-select-option",{attrs:{value:"02"}},[t._v("业务通知")]),a("ta-select-option",{attrs:{value:"03"}},[t._v("普通通知")])],1),a("ta-button",{directives:[{name:"show",rawName:"v-show",value:"1"==t.activeNotice,expression:"activeNotice=='1'"}],attrs:{type:"primary"},on:{click:t.readNotices}},[t._v("全部标记已读")])],1),a("div",{staticClass:"msg-page-foot"},[a("ta-pagination",{attrs:{total:t.total,pageSize:t.pageSize},on:{change:t.pageChange,showSizeChange:t.pageSizeChange},model:{value:t.page,callback:function(e){t.page=e},expression:"page"}})],1)],1)],1)],1)},s=[],n="message/",o={queryUserMessageNoRead:function(t,e){var a={url:n+"record/queryUserMessageNoRead",data:t};Base.submit(null,a,{successCallback:function(t){return e(t)}})},queryUserMessageAll:function(t,e){var a={url:n+"record/queryUserMessageAll",data:t};Base.submit(null,a,{successCallback:function(t){return e(t)}})},queryUserMessageSend:function(t,e){var a={url:n+"record/queryUserMessageSend",data:t};Base.submit(null,a,{successCallback:function(t){return e(t)}})},readNotices:function(t,e){var a={url:n+"readNotices",data:t};Base.submit(null,a,{successCallback:function(t){return e(t)}})},queryNoticeFiles:function(t,e){var a={url:n+"queryNoticeFiles",data:t};Base.submit(null,a,{successCallback:function(t){return e(t)}})}},c={name:"recordMg",data:function(){return{backUrl:"",activeNotice:"1",type:"",page:1,pageSize:20,total:10,noticeList1:[],noticeList2:[],noticeList3:[]}},mounted:function(){this.backUrl=faceConfig.basePath,this.queryUserMessageNoRead()},methods:{queryUserMessageNoRead:function(){var t=this,e={type:this.type,page:this.page,pageSize:this.pageSize};o.queryUserMessageNoRead(e,(function(e){t.noticeList1=e.data.noticeList,-1!=e.data.total&&(t.total=e.data.total)}))},queryUserMessageAll:function(){var t=this,e={type:this.type,page:this.page,pageSize:this.pageSize};o.queryUserMessageAll(e,(function(e){t.noticeList2=e.data.noticeList,-1!=e.data.total&&(t.total=e.data.total)}))},queryUserMessageSend:function(){var t=this,e={type:this.type,page:this.page,pageSize:this.pageSize};o.queryUserMessageSend(e,(function(e){t.noticeList3=e.data.noticeList,-1!=e.data.total&&(t.total=e.data.total)}))},noticeTabChange:function(t){"1"==t?this.queryUserMessageNoRead():"2"==t?this.queryUserMessageAll():"3"==t&&this.queryUserMessageSend()},pageChange:function(t,e){this.page=t,this.noticeTabChange(this.activeNotice)},pageSizeChange:function(t,e){this.pageSize=e,this.noticeTabChange(this.activeNotice)},noticeTypeChange:function(t){this.type="00"==t?null:t,this.noticeTabChange(this.activeNotice)},noticeDetailChange:function(t){t.length>0&&("1"==this.activeNotice?this.getNoticeFiles(this.noticeList1,t[t.length-1]):"2"==this.activeNotice?this.getNoticeFiles(this.noticeList2,t[t.length-1]):"3"==this.activeNotice&&this.getNoticeFiles(this.noticeList3,t[t.length-1]))},readNotices:function(){var t=this,e=this.noticeList1.map((function(t){return t.mId})).join(","),a={mIds:e};o.readNotices(a,(function(e){t.queryUserMessageNoRead()}))},getNoticeFiles:function(t,e){var a=this,i=t.find((function(t){return t.mId==e}));if(!i.noticeFileList){var s={mId:e};o.queryNoticeFiles(s,(function(t){t.data.noticeFileList&&(i.noticeFileList=t.data.noticeFileList,a.$forceUpdate())}))}}}},l=c,r=a(1001),u=(0,r.Z)(l,i,s,!1,null,null,null),d=u.exports}}]);
"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[4921],{51291:function(e,t,a){a.r(t),a.d(t,{default:function(){return b}});var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"fit",attrs:{id:"serverDimensionality"}},[a("ta-border-layout",{attrs:{layout:{header:"55px"},centerCfg:{showBar:!0}}},[a("template",{slot:"header"},[a("ta-breadcrumb",{staticStyle:{"line-height":"29px"},attrs:{separator:">"}},[a("ta-breadcrumb-item",[a("a",{on:{click:e.fnBackToHome}},[e._v("作业管理")])]),a("ta-breadcrumb-item",[e._v("服务器维度")])],1)],1),a("div",{attrs:{slot:"centerExtraContent"},slot:"centerExtraContent"},[a("ta-alert",{attrs:{message:"当前注册中心名称："+this.zkData.appName,type:"info",showIcon:""}})],1),a("ta-tabs",{staticClass:"fit content-box"},[a("ta-tab-pane",{attrs:{tab:"服务器详情"}},[a("ta-table",{attrs:{columns:e.serverColumns,dataSource:e.serverGridData,pagination:!1},scopedSlots:e._u([{key:"action",fn:function(t,r){return[r.instancesNum>0&&0==r.disabledJobsNum?a("span",[a("ta-button",{staticClass:"detail action-button-common",attrs:{size:"small"},on:{click:function(t){return e.routeToServerDetail(r.serverIp)}}},[e._v(" 详情 ")]),a("ta-button",{staticClass:"failure action-button-common",attrs:{size:"small"},on:{click:function(t){return e.disableServer(r.serverIp)}}},[e._v(" 失效 ")]),a("ta-popconfirm",{attrs:{title:"确定要继续终止操作吗?",okText:"确定",cancelText:"取消",okType:"default"},on:{confirm:function(t){return e.shutdownServer(r.serverIp)}}},[a("ta-icon",{staticStyle:{color:"red"},attrs:{slot:"icon",type:"question-circle-o"},slot:"icon"}),a("ta-button",{staticClass:"terminate action-button-common",attrs:{size:"small"}},[e._v(" 终止 ")])],1)],1):e._e(),r.instancesNum>0&&r.disabledJobsNum>0?a("span",[a("ta-button",{staticClass:"detail action-button-common",attrs:{size:"small"},on:{click:function(t){return e.routeToServerDetail(r.serverIp)}}},[e._v(" 详情 ")]),a("ta-button",{staticClass:"effect action-button-common",attrs:{size:"small"},on:{click:function(t){return e.enableServer(r.serverIp)}}},[e._v(" 生效 ")]),a("ta-popconfirm",{attrs:{title:"确定要继续终止操作吗?",okText:"确定",cancelText:"取消",okType:"default"},on:{confirm:function(t){return e.shutdownServer(r.serverIp)}}},[a("ta-icon",{staticStyle:{color:"red"},attrs:{slot:"icon",type:"question-circle-o"},slot:"icon"}),a("ta-button",{staticClass:"terminate action-button-common",attrs:{size:"small"}},[e._v(" 终止 ")])],1)],1):e._e(),0==r.instancesNum?a("span",[a("ta-popconfirm",{attrs:{title:"确定要继续删除操作吗?",okText:"确定",cancelText:"取消",okType:"default"},on:{confirm:function(t){return e.removeServer(r.serverIp)}}},[a("ta-icon",{staticStyle:{color:"red"},attrs:{slot:"icon",type:"question-circle-o"},slot:"icon"}),a("ta-button",{staticClass:"delete action-button-common",attrs:{size:"small"}},[e._v(" 删除 ")])],1)],1):e._e()]}}])})],1),a("template",{slot:"tabBarExtraContent"},[a("ta-button",{on:{click:e.fnBackToHome}},[a("ta-icon",{attrs:{type:"rollback"}}),e._v("返回")],1)],1)],2)],2)],1)},s=[],n=a(47878),o=[{title:"服务器IP",dataIndex:"serverIp",width:"20%"},{title:"运行实例数",dataIndex:"instancesNum",width:"20%"},{title:"作业总数",dataIndex:"jobsNum",width:"20%"},{title:"禁用作业数",dataIndex:"disabledJobsNum",width:"20%"},{title:"操作",dataIndex:"action",width:"20%",align:"center",scopedSlots:{customRender:"action"}}],i={name:"serverDimensionality",components:{},data:function(){return{serverGridData:[],serverColumns:o,zkData:{}}},mounted:function(){this.$route.params.zkData instanceof Object?(this.zkData=this.$route.params.zkData||{},this.loadData()):this.$router.push({name:"zookeeperRegistryCenterConfig"})},methods:{loadData:function(){var e=this,t={zkId:this.zkData.zkId,zkAddress:this.zkData.zkAddress,appNamespace:this.zkData.appNamespace};n.Z.getServerInfo(t,(function(t){e.serverGridData=t.data.serverGridData}))},fnBackToHome:function(){this.$router.push({name:"zookeeperRegistryCenterConfig"})},routeToServerDetail:function(e){this.$router.push({name:"serverDetail",params:{serverDetailData:{zkData:this.zkData,serverIp:e}}})},disableServer:function(e){var t=this,a={zkId:this.zkData.zkId,serverIp:e};n.Z.disableServer(a,(function(e){t.$message.success("失效操作成功"),t.loadData()}))},enableServer:function(e){var t=this,a={zkId:this.zkData.zkId,serverIp:e};n.Z.enableServer(a,(function(e){t.$message.success("生效操作成功"),t.loadData()}))},shutdownServer:function(e){var t=this,a={zkId:this.zkData.zkId,serverIp:e};n.Z.shutdownServer(a,(function(e){t.$message.success("终止操作成功"),t.loadData()}))},removeServer:function(e){var t=this;n.Z.removeServer({zkId:this.zkData.zkId,serverIp:e},(function(e){t.$message.success("删除操作成功"),t.loadData()}))}}},c=i,u=a(1001),l=(0,u.Z)(c,r,s,!1,null,"7366ef14",null),b=l.exports},51054:function(e,t,a){a.r(t),a.d(t,{default:function(){return l}});var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"fit",attrs:{id:"serverDimensionality"}},[a("ta-border-layout",{attrs:{layout:{header:"70px"},centerCfg:{showBar:!1},"footer-cfg":{showBorder:!1}}},[a("div",{attrs:{slot:"header"},slot:"header"}),a("ta-table",{attrs:{columns:e.serverColumns,dataSource:e.serverGridData,pagination:!1}}),a("ta-pagination",{ref:"onlineGridPager",staticStyle:{float:"right","margin-top":"10px"},attrs:{showSizeChanger:"",showQuickJumper:"",dataSource:e.serverGridData,defaultPageSize:6,params:e.jobPageParams,url:"logmg/loginLog/loginLogAnalysisRestService/getOnlineInfo"},on:{"update:dataSource":function(t){e.serverGridData=t},"update:data-source":function(t){e.serverGridData=t}}})],1)],1)},s=[],n=(a(77722),[{title:"服务器IP",dataIndex:"serverIp",width:"20%"},{title:"运行实例数",dataIndex:"instancesNum",width:"20%"},{title:"作业总数",dataIndex:"jobsNum",width:"20%"},{title:"禁用作业数",dataIndex:"disabledJobsNum",width:"20%"},{title:"操作",dataIndex:"clientSystem",width:"20%"}]),o={name:"serverDimensionality",components:{},data:function(){return{serverGridData:[],serverColumns:n}},mounted:function(){},methods:{serverPageParams:function(){return{}},loadData:function(){}}},i=o,c=a(1001),u=(0,c.Z)(i,r,s,!1,null,null,null),l=u.exports},47878:function(e,t){t["Z"]={addZookeeperRegistryCenter:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/zookeeperAddressManagementRestService/addZookeeperRegistryCenter",data:e},{successCallback:function(e){return t(e)}})},batchDeleteZookeeperRegistryCenter:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/zookeeperAddressManagementRestService/deleteZookeeperRegistryCenter",data:e},{successCallback:function(e){return t(e)}})},connectZookeeperRegistryCenter:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/zookeeperAddressManagementRestService/connectZookeeperRegistryCenter",data:e},{successCallback:function(e){return t(e)}})},getJobInfo:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/getJobInfo",data:e},{successCallback:function(e){return t(e)}})},getJobDetailInfo:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/getJobDetailInfo",data:e},{successCallback:function(e){return t(e)}})},saveJobDetailInfo:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/saveJobDetailInfo",data:e},{successCallback:function(e){return t(e)}})},getJobServerDetail:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/getJobServerDetail",data:e},{successCallback:function(e){return t(e)}})},disabledSharding:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/disabledSharding",data:e},{successCallback:function(e){return t(e)}})},effectSharding:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/effectSharding",data:e},{successCallback:function(e){return t(e)}})},triggerJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/triggerJob",data:e},{successCallback:function(e){return t(e)}})},disableJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/disableJob",data:e},{successCallback:function(e){return t(e)}})},enableJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/enableJob",data:e},{successCallback:function(e){return t(e)}})},shutdownJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/shutdownJob",data:e},{successCallback:function(e){return t(e)}})},removeJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/removeJob",data:e},{successCallback:function(e){return t(e)}})},getServerInfo:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/getServerInfo",data:e},{successCallback:function(e){return t(e)}})},disableServer:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/disableServer",data:e},{successCallback:function(e){return t(e)}})},enableServer:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/enableServer",data:e},{successCallback:function(e){return t(e)}})},shutdownServer:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/shutdownServer",data:e},{successCallback:function(e){return t(e)}})},removeServer:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/removeServer",data:e},{successCallback:function(e){return t(e)}})},getServerJobDetail:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/getServerJobDetail",data:e},{successCallback:function(e){return t(e)}})},disabledServerJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/disabledServerJob",data:e},{successCallback:function(e){return t(e)}})},enableServerJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/enableServerJob",data:e},{successCallback:function(e){return t(e)}})},shutdownServerJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/shutdownServerJob",data:e},{successCallback:function(e){return t(e)}})},removeServerJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/removeServerJob",data:e},{successCallback:function(e){return t(e)}})},getJobNameByZkId:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/freeBusyJobManagerRestService/getJobNameByZkId",data:e},{successCallback:function(e){return t(e)}})},getServerIpsByJobName:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/freeBusyJobManagerRestService/getServerIpsByJobName",data:e},{successCallback:function(e){return t(e)}})},addFreeBusyJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/freeBusyJobManagerRestService/addFreeBusyJob",data:e},{successCallback:function(e){return t(e)}})},deleteFreeBusyJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/freeBusyJobManagerRestService/deleteFreeBusyJob",data:e},{successCallback:function(e){return t(e)}})},getAllServerIpsByJobName:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/freeBusyJobManagerRestService/getAllServerIpsByJobName",data:e},{successCallback:function(e){return t(e)}})},updateFreeBusyJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/freeBusyJobManagerRestService/updateFreeBusyJob",data:e},{successCallback:function(e){return t(e)}})}}},77722:function(e,t){var a="/jobmg/elasticjob/jobOperateRestService/";t["Z"]={getJobInfo:function(e,t){Base.submit(null,{url:a+"getJobInfo",data:e},{successCallback:function(e){return t(e)}})},getJobDetailInfo:function(e,t){Base.submit(null,{url:a+"getJobDetailInfo",data:e},{successCallback:function(e){return t(e)}})},saveJobDetailInfo:function(e,t){Base.submit(null,{url:a+"saveJobDetailInfo",data:e},{successCallback:function(e){return t(e)}})}}}}]);
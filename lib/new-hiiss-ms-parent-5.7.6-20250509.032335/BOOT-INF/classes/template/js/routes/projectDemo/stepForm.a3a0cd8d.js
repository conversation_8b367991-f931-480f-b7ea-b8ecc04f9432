"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[6286],{20753:function(t,e,a){a.r(e),a.d(e,{default:function(){return C}});var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("ta-border-layout",[a("ta-card",{staticClass:"mg-b20",attrs:{title:"分步表单"}},[a("ta-row",[a("ta-steps",{attrs:{current:t.stepIndex-1}},[a("ta-step",{attrs:{title:"填写转账信息"}}),a("ta-step",{attrs:{title:"确认转账信息"}}),a("ta-step",{attrs:{title:"完成"}})],1)],1),a("ta-row",[a("ta-divider")],1),a("ta-row",[a("step1",{directives:[{name:"show",rawName:"v-show",value:1===t.stepIndex,expression:"stepIndex === 1"}],ref:"step1"}),a("step2",{directives:[{name:"show",rawName:"v-show",value:2===t.stepIndex,expression:"stepIndex === 2"}],ref:"step2"}),a("step3",{directives:[{name:"show",rawName:"v-show",value:3===t.stepIndex,expression:"stepIndex === 3"}],ref:"step3"})],1),a("ta-row",[a("ta-divider")],1),a("ta-row",[1!==t.stepIndex&&3!==t.stepIndex?a("ta-button",{attrs:{disabled:1===t.stepIndex},on:{click:t.prevStep}},[t._v(" 返回 ")]):t._e(),t.canSubmit||3===t.stepIndex?t._e():a("ta-button",{attrs:{type:"primary"},on:{click:t.nextStep}},[t._v(" "+t._s(2===t.stepIndex?"提交":"下一步")+" ")])],1)],1)],1)},r=[],n=a(95082),o=function(){var t=this,e=this,a=e.$createElement,s=e._self._c||a;return s("ta-form",{attrs:{"auto-form-create":function(e){return t.form=e},"wrapper-col":{span:12}}},[s("ta-form-item",{attrs:{label:"付款账户","field-decorator-id":"payAccount","init-value":"aaa",required:!0}},[s("ta-select",[s("ta-select-option",{attrs:{value:"aaa"}},[e._v(" <EMAIL> ")])],1)],1),s("ta-form-item",{attrs:{label:"收款账户","field-decorator-id":"getMoneyAccount","init-value":{accountType:"icbc",cardNum:"************"},required:!0}},[s("get-money-account"),s("template",{slot:"extra"},[e._v(" 当前表单控件是一个自定义表单控件 ")])],2),s("ta-form-item",{attrs:{label:"收款人姓名","field-decorator-id":"getAccountName",required:!0,"init-value":"admin"}},[s("ta-input")],1),s("ta-form-item",{attrs:{label:"转账金额","field-decorator-id":"money",required:!0,"init-value":"99999.99"}},[s("ta-input",{attrs:{"addon-before":"¥"}})],1)],1)},i=[],c=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("ta-input-group",{attrs:{compact:""}},[a("ta-select",{staticStyle:{width:"30%"},attrs:{value:t.value.accountType}},[a("ta-select-option",{attrs:{value:"icbc"}},[t._v(" 中国工商银行 ")]),a("ta-select-option",{attrs:{value:"abc"}},[t._v(" 中国农业银行 ")])],1),a("ta-input",{staticStyle:{width:"70%"},attrs:{value:t.value.cardNum}})],1)],1)},u=[],l={name:"getMoneyAccount",props:{value:{type:Object,default:function(){return{accountType:"",cardNum:""}}}}},p=l,m=a(1001),d=(0,m.Z)(p,c,u,!1,null,"46dc1534",null),f=d.exports,v={name:"step1",components:{getMoneyAccount:f},data:function(){return{}},methods:{getFormData:function(){return this.form.getFieldsValue()}}},h=v,b=(0,m.Z)(h,o,i,!1,null,"d9d1ea40",null),y=b.exports,g=function(){var t=this,e=this,a=e.$createElement,s=e._self._c||a;return s("div",[s("ta-row",[s("ta-alert",{attrs:{message:"确认转账",description:"确认转账后,资金将会直接打入对方账户中,请确认是否转账!",type:"success","show-icon":!0}})],1),s("ta-row",[s("ta-divider")],1),s("ta-row",[s("ta-descriptions",{attrs:{title:"转账信息",bordered:""}},[s("ta-descriptions-item",{attrs:{label:"付款账户"}},[e._v(" "+e._s(e.payAccount)+" ")]),s("ta-descriptions-item",{attrs:{label:"收款账户"}},[e._v(" "+e._s(e.getMoneyAccount.cardNum)+" ")]),s("ta-descriptions-item",{attrs:{label:"收款人姓名"}},[e._v(" "+e._s(e.getAccountName)+" ")]),s("ta-descriptions-item",{attrs:{label:"转账金额"}},[e._v(" ¥"+e._s(e.money)+" ")])],1)],1),s("ta-row",[s("ta-divider")],1),s("ta-form",{attrs:{"auto-form-create":function(e){return t.form=e}}},[s("ta-form-item",{attrs:{label:"支付密码","field-decorator-id":"payPwd","init-value":"111111"}},[s("ta-input",{attrs:{type:"password"}})],1)],1)],1)},_=[],x={name:"step2",data:function(){return{payAccount:"",getMoneyAccount:{},getAccountName:"",money:""}},methods:{getFormData:function(){return this.form.getFieldsValue()},setFormData:function(t){var e=t.payAccount,a=t.getMoneyAccount,s=t.getAccountName,r=t.money;this.payAccount=e,this.getMoneyAccount=a,this.getAccountName=s,this.money=r}}},A=x,w=(0,m.Z)(A,g,_,!1,null,"123ae464",null),D=w.exports,k=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("ta-result",{attrs:{status:"success",title:"操作成功","sub-title":"预计两小时内到账"},scopedSlots:t._u([{key:"extra",fn:function(){return[a("ta-button",{key:"console",attrs:{type:"primary"},on:{click:t.refreshPage}},[t._v(" 再转一笔 ")]),a("ta-button",{key:"buy",on:{click:t.refreshPage}},[t._v(" 查看账单 ")])]},proxy:!0}])},[a("ta-descriptions",{attrs:{title:"转账信息",bordered:""}},[a("ta-descriptions-item",{attrs:{label:"付款账户"}},[t._v(" "+t._s(t.payAccount)+" ")]),a("ta-descriptions-item",{attrs:{label:"收款账户"}},[t._v(" "+t._s(t.getMoneyAccount.cardNum)+" ")]),a("ta-descriptions-item",{attrs:{label:"收款人姓名"}},[t._v(" "+t._s(t.getAccountName)+" ")]),a("ta-descriptions-item",{attrs:{label:"转账金额"}},[t._v(" ¥"+t._s(t.money)+" ")])],1)],1)],1)},F=[],$={name:"step3",data:function(){return{payAccount:"",getMoneyAccount:{},getAccountName:"",money:""}},methods:{getFormData:function(){return this.form.getFieldsValue()},setFormData:function(t){var e=t.payAccount,a=t.getMoneyAccount,s=t.getAccountName,r=t.money;this.payAccount=e,this.getMoneyAccount=a,this.getAccountName=s,this.money=r},refreshPage:function(){window.location.reload()}}},I=$,N=(0,m.Z)(I,k,F,!1,null,"7565f083",null),M=N.exports,S={components:{step1:y,step2:D,step3:M},data:function(){return{visibleModal:!1,stepIndex:1,canSubmit:!1}},methods:{openModal:function(){this.visibleModal=!0},close:function(){this.visibleModal=!1,this.stepIndex=1},prevStep:function(){this.stepIndex--},nextStep:function(){switch(this.stepIndex){case 1:this.submitStep1();break;case 2:this.submitStep2();break;case 3:this.submitStep3();break}},submitStep1:function(){var t=this;Base.submit(null,{url:"http/mock/projectDemo/stepForm/step1",data:(0,n.Z)({},this.$refs.step1.getFormData()),reqDataLevel:0},{successCallback:function(e){e.data.validate?(t.stepIndex++,t.$refs.step2.setFormData(t.$refs.step1.getFormData())):t.$message.error("用户名输入错误!请输入admin!")}})},submitStep2:function(){var t=this;Base.submit(null,{url:"http/mock/projectDemo/stepForm/step2",data:(0,n.Z)({},this.$refs.step2.getFormData()),reqDataLevel:0},{successCallback:function(e){e.data.validate?(t.stepIndex++,t.$refs.step3.setFormData(t.$refs.step1.getFormData())):t.$message.error("支付密码输入错误!")}})},submitStep3:function(){var t=this;Base.submit(null,{url:"http/mock/projectDemo/stepForm/step3",data:(0,n.Z)({},this.$refs.step3.getFormData()),reqDataLevel:0},{successCallback:function(e){e.data.validate?(t.$message.success("当前步骤数据验证成功!将提交分步表单!"),t.submit()):t.$message.error("答案输入错误;请输入1!")}})},submit:function(){var t=this,e=(0,n.Z)((0,n.Z)((0,n.Z)({},this.$refs.step1.getFormData()),this.$refs.step2.getFormData()),this.$refs.step3.getFormData());Base.submit(null,{url:"http/mock/projectDemo/stepForm/submitAll",data:e,reqDataLevel:0},{successCallback:function(e){t.$message.success("分步表单提交成功!")}}),this.close()}}},Z=S,q=(0,m.Z)(Z,s,r,!1,null,null,null),C=q.exports}}]);
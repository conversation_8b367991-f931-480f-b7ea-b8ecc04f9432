"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[5867],{7584:function(t,e,a){a.r(e),a.d(e,{default:function(){return k}});var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("ta-card",{attrs:{bordered:!1}},[a("ta-list",{attrs:{size:"large","row-key":"id","item-layout":"vertical","data-source":t.data},scopedSlots:t._u([{key:"renderItem",fn:function(e){return a("ta-list-item",{key:e.id},[t.loading?t._e():a("template",{slot:"actions"},[a("icon-text",{attrs:{type:"star-o",text:e.star}}),a("icon-text",{attrs:{type:"like-o",text:e.like}}),a("icon-text",{attrs:{type:"message",text:e.message}})],1),a("ta-skeleton",{attrs:{loading:t.loading,active:"",avatar:""}},[a("ta-list-item-meta",[a("a",{attrs:{slot:"title",href:"https://vue.ant.design/"},slot:"title"},[t._v(t._s(e.title))]),a("template",{slot:"description"},[a("span",[a("ta-tag",[t._v("Ant Design")]),a("ta-tag",[t._v("设计语言")]),a("ta-tag",[t._v("蚂蚁金服")])],1)])],2),a("article-list-content",{attrs:{description:e.description,owner:e.owner,avatar:e.avatar,href:e.href,"update-at":e.updatedAt}})],1)],2)}}])},[t.data.length>0?a("div",{staticStyle:{"text-align":"center","margin-top":"16px"},attrs:{slot:"footer"},slot:"footer"},[a("ta-button",{attrs:{loading:t.loadingMore},on:{click:t.loadMore}},[t._v(" 加载更多 ")])],1):t._e()])],1)},r=[],i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"article-list-content-index-listContent"},[a("div",{staticClass:"description"},[t._t("default",(function(){return[t._v(" "+t._s(t.description)+" ")]}))],2),a("div",{staticClass:"extra"},[a("ta-avatar",{attrs:{src:t.avatar,size:"small"}}),a("a",{attrs:{href:t.href}},[t._v(t._s(t.owner))]),t._v(" 发布在 "),a("a",{attrs:{href:t.href}},[t._v(t._s(t.href))]),a("em",[t._v(t._s(t.updateAt))])],1)])},s=[],o={name:"articleListContent",props:{prefixCls:{type:String,default:"antd-pro-components-article-list-content-index-listContent"},description:{type:String,default:""},owner:{type:String,required:!0},avatar:{type:String,required:!0},href:{type:String,required:!0},updateAt:{type:String,required:!0}}},l=o,c=a(1001),d=(0,c.Z)(l,i,s,!1,null,"34bf71e1",null),u=d.exports,p=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("span",[a("ta-icon",{staticStyle:{"margin-right":"8px"},attrs:{type:t.type}}),t._v(" "+t._s(t.text)+" ")],1)},f=[],g={name:"iconText",props:{type:{type:String,required:!0},text:{type:[String,Number],required:!0}}},m=g,_=(0,c.Z)(m,p,f,!1,null,null,null),h=_.exports,v={name:"articleList",components:{ArticleListContent:u,IconText:h},data:function(){return{loading:!0,loadingMore:!1,data:Array.from({length:5}).map((function(t,e){return{id:e}}))}},mounted:function(){this.getList()},methods:{getList:function(){var t=this;Base.submit(null,{url:"http/mock/projectDemo/list/article",showPageLoading:!1}).then((function(e){t.data=e.data.list,t.loading=!1}))},loadMore:function(){var t=this;this.loadingMore=!0,Base.submit(null,{url:"http/mock/projectDemo/list/article",showPageLoading:!1}).then((function(e){t.data=t.data.concat(e.data.list)})).finally((function(){t.loadingMore=!1}))}}},y=v,x=(0,c.Z)(y,n,r,!1,null,null,null),k=x.exports}}]);
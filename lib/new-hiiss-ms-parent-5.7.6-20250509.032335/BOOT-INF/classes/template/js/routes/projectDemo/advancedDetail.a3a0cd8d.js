"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[8497],{37988:function(t,e,a){a.r(e),a.d(e,{default:function(){return p}});var s=function(){var t=this,e=this,a=e.$createElement,s=e._self._c||a;return s("ta-border-layout",{staticClass:"noborder baseDetail",attrs:{"layout-type":"fixTop"}},[s("ta-page-header",{staticClass:"page-header",attrs:{slot:"header"},slot:"header",scopedSlots:e._u([{key:"title",fn:function(){return[s("ta-icon",{attrs:{type:"bars"}}),e._v(" 单号：234231029431 ")]},proxy:!0},{key:"extra",fn:function(){return[s("ta-button-group",{staticStyle:{"margin-right":"4px"}},[s("ta-button",[e._v("操作")]),s("ta-button",[e._v("操作")]),s("ta-button",[s("ta-icon",{attrs:{type:"ellipsis"}})],1)],1),s("ta-button",{attrs:{type:"primary"}},[e._v(" 主操作 ")])]},proxy:!0},{key:"footer",fn:function(){return[s("ta-tabs",{staticStyle:{padding:"0"},attrs:{"default-active-key":"1"}},[s("ta-tab-pane",{key:"1",attrs:{tab:"详情"}}),s("ta-tab-pane",{key:"2",attrs:{tab:"规则"}})],1)]},proxy:!0}])},[s("ta-row",[s("ta-col",{attrs:{span:18}},[s("ta-descriptions",{attrs:{size:"small",column:2}},[s("ta-descriptions-item",{attrs:{label:"创建人"}},[e._v(" 曲丽丽 ")]),s("ta-descriptions-item",{attrs:{label:"订购产品"}},[e._v(" XX 服务 ")]),s("ta-descriptions-item",{attrs:{label:"创建时间"}},[e._v(" 2017-07-07 ")]),s("ta-descriptions-item",{attrs:{label:"关联单据"}},[s("a",{attrs:{href:""}},[e._v("12421")])]),s("ta-descriptions-item",{attrs:{label:"生效日期"}},[e._v(" 2017-07-07 ~ 2017-08-08 ")]),s("ta-descriptions-item",{attrs:{label:"备注"}},[e._v(" 请于两个工作日内确认 ")])],1)],1),s("ta-col",{attrs:{span:6}},[s("ta-row",{staticClass:"status-list"},[s("ta-col",{attrs:{xs:12,sm:12}},[s("ta-statistic",{attrs:{title:"状态",value:"待审批"}})],1),s("ta-col",{attrs:{xs:12,sm:12}},[s("ta-statistic",{attrs:{title:"订单金额",prefix:"￥",precision:2,value:568.08}})],1)],1)],1)],1)],1),s("ta-card",{attrs:{bordered:!1,title:"流程进度"}},[s("ta-steps",{attrs:{direction:"horizontal",current:1,"progress-dot":""}},[s("ta-step",{scopedSlots:e._u([{key:"title",fn:function(){return[s("span",[e._v("创建项目")])]},proxy:!0},{key:"description",fn:function(){return[s("div",{staticClass:"antd-pro-pages-profile-advanced-style-stepDescription"},[e._v(" 曲丽丽"),s("ta-icon",{staticStyle:{"margin-left":"8px"},attrs:{type:"dingding"}}),s("div",[e._v("2016-12-12 12:32")])],1)]},proxy:!0}])}),s("ta-step",{scopedSlots:e._u([{key:"title",fn:function(){return[s("span",[e._v("部门初审")])]},proxy:!0},{key:"description",fn:function(){return[s("div",{staticClass:"antd-pro-pages-profile-advanced-style-stepDescription"},[e._v(" 周毛毛"),s("ta-icon",{staticStyle:{color:"rgb(0, 160, 233)","margin-left":"8px"},attrs:{type:"dingding"}}),s("div",[s("a",[e._v("催一下")])])],1)]},proxy:!0}])}),s("ta-step",{attrs:{title:"财务复核"}}),s("ta-step",{attrs:{title:"完成"}})],1)],1),s("ta-card",{staticStyle:{"margin-top":"24px"},attrs:{bordered:!1,title:"用户信息"}},[s("ta-descriptions",[s("ta-descriptions-item",{attrs:{label:"用户姓名"}},[e._v(" 付晓晓 ")]),s("ta-descriptions-item",{attrs:{label:"会员卡号"}},[e._v(" 32943898021309809422 ")]),s("ta-descriptions-item",{attrs:{label:"身份证"}},[e._v(" 3321944288191034921 ")]),s("ta-descriptions-item",{attrs:{label:"联系方式"}},[e._v(" 18112345678 ")]),s("ta-descriptions-item",{attrs:{label:"联系地址"}},[e._v(" 浙江省杭州市西湖区黄姑山路工专路交叉路口 ")])],1),s("ta-descriptions",{attrs:{title:"信息组"}},[s("ta-descriptions-item",{attrs:{label:"某某数据"}},[e._v(" 725 ")]),s("ta-descriptions-item",{attrs:{label:"该数据更新时间"}},[e._v(" 2018-08-08 ")]),s("ta-descriptions-item"),s("ta-descriptions-item",{attrs:{label:"某某数据"}},[e._v(" 725 ")]),s("ta-descriptions-item",{attrs:{label:"该数据更新时间"}},[e._v(" 2018-08-08 ")]),s("ta-descriptions-item")],1),s("ta-card",{attrs:{type:"inner",title:"多层信息组"}},[s("ta-descriptions",{attrs:{title:"组名称",size:"small"}},[s("ta-descriptions-item",{attrs:{label:"负责人"}},[e._v(" 林东东 ")]),s("ta-descriptions-item",{attrs:{label:"角色码"}},[e._v(" 1234567 ")]),s("ta-descriptions-item",{attrs:{label:"所属部门"}},[e._v(" XX公司-YY部 ")]),s("ta-descriptions-item",{attrs:{label:"过期时间"}},[e._v(" 2018-08-08 ")]),s("ta-descriptions-item",{attrs:{label:"描述"}},[e._v(" 这段描述很长很长很长很长很长很长很长很长很长很长很长很长很长很长... ")])],1),s("ta-divider",{staticStyle:{margin:"16px 0"}}),s("ta-descriptions",{attrs:{title:"组名称",size:"small",col:1}},[s("ta-descriptions-item",{attrs:{label:"学名"}},[e._v(" Citrullus lanatus (Thunb.) Matsum. et Nakai一年生蔓生藤本；茎、枝粗壮，具明显的棱。卷须较粗.. ")])],1),s("ta-divider",{staticStyle:{margin:"16px 0"}}),s("ta-descriptions",{attrs:{title:"组名称",size:"small",col:2}},[s("ta-descriptions-item",{attrs:{label:"负责人"}},[e._v(" 付小小 ")]),s("ta-descriptions-item",{attrs:{label:"角色码"}},[e._v(" 1234567 ")])],1)],1)],1),s("ta-card",{staticStyle:{"margin-top":"24px"},attrs:{bordered:!1,title:"用户近半年来电记录"}},[s("div",{staticClass:"no-data"},[s("ta-icon",{attrs:{type:"frown-o"}}),e._v("暂无数据 ")],1)]),s("ta-card",{staticStyle:{"margin-top":"24px"},attrs:{bordered:!1,"tab-list":e.operationTabList,"active-tab-key":e.operationActiveTabKey},on:{tabChange:function(e){t.operationActiveTabKey=e}}},["1"===e.operationActiveTabKey?s("ta-table",{attrs:{columns:e.operationColumns,"data-source":e.operation1,pagination:!1},scopedSlots:e._u([{key:"status",fn:function(t){return[s("ta-badge",{attrs:{status:e._f("statusTypeFilter")(t),text:e._f("statusFilter")(t)}})]}}],null,!1,3776390354)}):e._e(),"2"===e.operationActiveTabKey?s("ta-table",{attrs:{columns:e.operationColumns,"data-source":e.operation2,pagination:!1},scopedSlots:e._u([{key:"status",fn:function(t){return[s("ta-badge",{attrs:{status:e._f("statusTypeFilter")(t),text:e._f("statusFilter")(t)}})]}}],null,!1,3776390354)}):e._e(),"3"===e.operationActiveTabKey?s("ta-table",{attrs:{columns:e.operationColumns,"data-source":e.operation3,pagination:!1},scopedSlots:e._u([{key:"status",fn:function(t){return[s("ta-badge",{attrs:{status:e._f("statusTypeFilter")(t),text:e._f("statusFilter")(t)}})]}}],null,!1,3776390354)}):e._e()],1)],1)},r=[],i={name:"baseDetail",filters:{statusFilter:function(t){var e={agree:"成功",reject:"驳回"};return e[t]},statusTypeFilter:function(t){var e={agree:"success",reject:"error"};return e[t]}},data:function(){return{tabList:[{key:"detail",tab:"详情"},{key:"rule",tab:"规则"}],tabActiveKey:"detail",operationTabList:[{key:"1",tab:"操作日志一"},{key:"2",tab:"操作日志二"},{key:"3",tab:"操作日志三"}],operationActiveTabKey:"1",operationColumns:[{title:"操作类型",dataIndex:"type",key:"type"},{title:"操作人",dataIndex:"name",key:"name"},{title:"执行结果",dataIndex:"status",key:"status",scopedSlots:{customRender:"status"}},{title:"操作时间",dataIndex:"updatedAt",key:"updatedAt"},{title:"备注",dataIndex:"remark",key:"remark"}],operation1:[{key:"op1",type:"订购关系生效",name:"曲丽丽",status:"agree",updatedAt:"2017-10-03  19:23:12",remark:"-"},{key:"op2",type:"财务复审",name:"付小小",status:"reject",updatedAt:"2017-10-03  19:23:12",remark:"不通过原因"},{key:"op3",type:"部门初审",name:"周毛毛",status:"agree",updatedAt:"2017-10-03  19:23:12",remark:"-"},{key:"op4",type:"提交订单",name:"林东东",status:"agree",updatedAt:"2017-10-03  19:23:12",remark:"很棒"},{key:"op5",type:"创建订单",name:"汗牙牙",status:"agree",updatedAt:"2017-10-03  19:23:12",remark:"-"}],operation2:[{key:"op2",type:"财务复审",name:"付小小",status:"reject",updatedAt:"2017-10-03  19:23:12",remark:"不通过原因"},{key:"op3",type:"部门初审",name:"周毛毛",status:"agree",updatedAt:"2017-10-03  19:23:12",remark:"-"},{key:"op4",type:"提交订单",name:"林东东",status:"agree",updatedAt:"2017-10-03  19:23:12",remark:"很棒"}],operation3:[{key:"op2",type:"财务复审",name:"付小小",status:"reject",updatedAt:"2017-10-03  19:23:12",remark:"不通过原因"},{key:"op3",type:"部门初审",name:"周毛毛",status:"agree",updatedAt:"2017-10-03  19:23:12",remark:"-"}]}}},n=i,o=a(1001),l=(0,o.Z)(n,s,r,!1,null,"7e21e978",null),p=l.exports}}]);
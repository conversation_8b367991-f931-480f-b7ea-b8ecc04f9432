"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[9080],{11101:function(t,e,a){a.r(e),a.d(e,{default:function(){return v}});var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"fit",attrs:{id:"searchList"}},[a("ta-border-layout",{staticClass:"noborder",attrs:{"layout-type":"fixTop"}},[a("ta-page-header",{staticClass:"page-header",attrs:{slot:"header"},slot:"header"},[a("template",{slot:"footer"},[a("ta-tabs",{on:{change:t.onTabsChange},model:{value:t.tabsKey,callback:function(e){t.tabsKey=e},expression:"tabsKey"}},[a("ta-tab-pane",{key:"article",attrs:{tab:"文章"}}),a("ta-tab-pane",{key:"project",attrs:{tab:"项目"}}),a("ta-tab-pane",{key:"app",attrs:{tab:"应用"}})],1)],1),a("div",{staticClass:"header-content"},[a("ta-input-search",{staticStyle:{width:"600px"},attrs:{placeholder:"请输入查询关键字","enter-button":""}})],1),a("SearchPanel")],2),a("ta-card",{staticClass:"fit content-card",attrs:{bordered:!1}},[a("transition",{attrs:{name:"normal-fade",mode:"out-in"}},[a("router-view")],1)],1)],1)],1)},n=[],l=function(){var t=this,e=this,a=e.$createElement,r=e._self._c||a;return r("ta-form",{attrs:{layout:"inline","auto-form-create":function(e){return t.form=e}}},[r("div",{staticClass:"form-item"},[r("ta-form-item",{attrs:{label:"所属类目","label-width":75,"field-decorator-id":"categories"}},[r("ta-checkable-tag-group",{attrs:{data:e.data,"show-all":"","tag-style":{fontSize:"14px",margin:"0 12px"}}})],1)],1),r("div",{staticClass:"form-item"},[r("ta-form-item",{attrs:{label:"活跃用户","label-width":75,"field-decorator-id":"activeUser"}},[r("ta-input")],1),r("ta-form-item",{attrs:{label:"好评度","label-width":75,"field-decorator-id":"rate"}},[r("ta-input")],1),r("transition-group",{attrs:{name:"normal-fade"}},["projectSearch"===e.routeName?r("ta-form-item",{key:"owners",attrs:{label:"所有者","label-width":75,"field-decorator-id":"owner"}},[r("ta-select",{staticStyle:{width:"177px"},attrs:{options:e.owners}})],1):e._e(),"projectSearch"===e.routeName?r("ta-button",{key:"ownersButton",attrs:{type:"link"},on:{click:e.setOwner}},[e._v(" 只看自己的 ")]):e._e()],1)],1)])},s=[],o=a(18701),i=a.n(o),c={name:"ta-checkable-tag-group",model:{prop:"value",event:"change"},props:{showAll:Boolean,tagStyle:Object,data:{type:Array,required:!0},value:Array},data:function(){return{sValue:[]}},created:function(){this.sValue=this.value||[]},methods:{handleTagChange:function(t,e){t?this.sValue.push(e.value):this.sValue=this.sValue.filter((function(t){return t!==e.value})),this.$emit("change",this.sValue),this.$emit("itemChange",t,e)}},watch:{value:function(t){this.sValue=t||[]}},render:function(){var t=this,e=arguments[0],a=this.data.map((function(a){var r={props:{checked:t.sValue.includes(a.value)},on:{change:function(e){return t.handleTagChange(e,a)}},style:t.tagStyle};return e("ta-checkable-tag",i()([{},r]),[a.label])})),r=null;if(this.showAll){var n={props:{checked:this.sValue.length===this.data.length},on:{change:function(e){t.sValue=e?t.data.map((function(t){return t.value})):[],t.$emit("change",t.sValue),t.$emit("itemChange",e,"allTag")}},style:this.tagStyle};r=e("ta-checkable-tag",i()([{},n]),["全部"])}return e("div",{class:"ant-checkable-tag-group"},[r,a])}},u=[{value:"wzj",label:"我自己"},{value:"wjh",label:"吴家豪"},{value:"zxx",label:"周星星"},{value:"zly",label:"赵丽颖"},{value:"ym",label:"姚明"}],h={name:"searchPanel",components:{TaCheckableTagGroup:c},data:function(){return{owners:u,data:Array.from({length:10}).map((function(t,e){return{value:e,label:"类目".concat(e)}}))}},computed:{routeName:function(){return this.$route.name}},methods:{setOwner:function(){this.form.setFieldsValue({owner:["wzj"]})}}},d=h,f=a(1001),m=(0,f.Z)(d,l,s,!1,null,"03962069",null),p=m.exports,b={name:"searchList",components:{SearchPanel:p},data:function(){return{tabsKey:null,pageInfo:{article:"文章",project:"项目",app:"应用"}}},created:function(){this.tabsKey=this.$route.name.substr(0,this.$route.name.length-6)},methods:{onTabsChange:function(t){this.$router.push({name:"".concat(t,"Search")})},getTarget:function(){return document.querySelector(".fit.content-card").querySelector(".ant-card-body")}}},g=b,y=(0,f.Z)(g,r,n,!1,null,"65ff6534",null),v=y.exports}}]);
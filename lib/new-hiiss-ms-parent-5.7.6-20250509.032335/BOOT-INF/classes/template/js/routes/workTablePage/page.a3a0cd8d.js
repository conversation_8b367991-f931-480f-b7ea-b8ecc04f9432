(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[4877],{39444:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return u}});var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{staticStyle:{width:"50%",float:"left"}},[a("ta-e-charts",{staticStyle:{"text-align":"center"},attrs:{data:e.chartData1,"charts-type":"wordcloud"}})],1),a("div",{staticStyle:{width:"50%",float:"left"}},[a("ta-e-charts",{staticStyle:{"text-align":"center"},attrs:{data:e.chartData2,"charts-type":"ring"}})],1)])},o=[],i=a(99440),l={name:"Analysis",components:{TaECharts:i.Z},props:{moduleId:String},data:function(){return{chartData1:{columns:["word","count"],rows:[{word:"visualMap",count:22199},{word:"continuous",count:10288},{word:"controller",count:620},{word:"series",count:274470},{word:"gauge",count:12311},{word:"detail",count:1206},{word:"piecewise",count:4885},{word:"textStyle",count:32294},{word:"markPoint",count:18574},{word:"pie",count:38929},{word:"roseType",count:969},{word:"label",count:37517},{word:"emphasis",count:12053},{word:"yAxis",count:57299},{word:"name",count:15418},{word:"type",count:22905},{word:"gridIndex",count:5146},{word:"normal",count:49487},{word:"itemStyle",count:33837},{word:"min",count:4500},{word:"silent",count:5744},{word:"animation",count:4840},{word:"offsetCenter",count:232},{word:"inverse",count:3706},{word:"borderColor",count:4812},{word:"markLine",count:16578},{word:"line",count:76970},{word:"radiusAxis",count:6704},{word:"radar",count:15964},{word:"data",count:60679},{word:"dataZoom",count:24347},{word:"tooltip",count:43420},{word:"toolbox",count:25222},{word:"geo",count:16904},{word:"parallelAxis",count:4029}]},chartData2:{columns:["日期","访问用户"],rows:[{"日期":"1/1","访问用户":1935},{"日期":"1/3","访问用户":3533},{"日期":"1/2","访问用户":2608}]}}}},s=l,r=a(1001),c=(0,r.Z)(s,n,o,!1,null,"60c8d2fe",null),u=c.exports},17725:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return h}});var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{height:"255px",width:"95%",margin:"0 auto"}},[a("ta-e-charts",{attrs:{height:"255px","charts-type":"line",data:e.chartData,legend:e.chartSetting.legend,tooltip:e.chartSetting.tooltip,"x-axis":e.chartSetting.xAxis,"y-axis":e.chartSetting.yAxis,grid:e.chartSetting.grid,toolbox:e.chartSetting.toolbox}})],1)},o=[],i=(a(32564),a(99440)),l=a(36797),s=a.n(l),r={name:"Monitor",components:{TaECharts:i.Z},props:{moduleId:String},data:function(){return{BASE_URL:"/logmg/loginLog/loginLogAnalysisRestService/",chartSetting:{legend:{top:10,formatter:"时点在线人数"},tooltip:{formatter:function(e){return e.seriesName+"<br/>"+e.data[0]+" ("+e.data[1]+"人)"}},yAxis:{splitNumber:10,minInterval:1,axisLabel:{formatter:"{value}人"}},grid:{bottom:10},toolbox:{feature:{magicType:{type:["line","bar"]},saveAsImage:{}}}},chartData:{columns:["日期","时点在线人数"],rows:[]},timer:""}},created:function(){var e=this;this.timer=setInterval(this.loadData,6e4),this.$once("hook:beforeDestroy",(function(){clearInterval(e.timer)}))},mounted:function(){this.loadData()},methods:{loadData:function(){var e=this,t={};this.getSysTime(t,(function(t){e.onlineDate=s()(t.data.sysdate,"YYYY-MM-DD"),e.onlineEndTime=s()();var a=e.onlineEndTime.clone();e.onlineStartTime=a.subtract("1","hours");var n=e.onlineDate?e.onlineDate.format("YYYY-MM-DD"):"",o=e.onlineStartTime?e.onlineStartTime.format("HH:mm"):"",i=e.onlineEndTime?e.onlineEndTime.format("HH:mm"):"",l={searchDate:n,startTime:o,endTime:i};e.analysisOnlineStatInfo(l,(function(t){e.onlineChartData=t.data.onlineChartData,e.onlineXdata=e.getStatLogInfoXdata(e,"online"),e.onlineSeriesData=e.sortStatLogDataByHours(e,"online");var a=[];e.onlineXdata.map((function(t,n){a.push({"日期":t,"时点在线人数":e.onlineSeriesData[n]})})),e.chartData.rows=a}))}))},getSysTime:function(e,t){Base.submit(null,{url:this.BASE_URL+"getSysTime",data:e,_modulePartId_:this.moduleId,showPageLoading:!1},{successCallback:function(e){return t(e)}})},analysisOnlineStatInfo:function(e,t){Base.submit(null,{url:this.BASE_URL+"analysisOnlineStatInfo",data:e,_modulePartId_:this.moduleId,showPageLoading:!1},{successCallback:function(e){return t(e)}})},getStatLogInfoXdata:function(e,t){var a=[],n=[];"online"===t?(a=e.onlineStartTime.format("HH:mm").split(":"),n=e.onlineEndTime.format("HH:mm").split(":")):"login"===t&&(a=e.loginStartTime.format("HH:mm").split(":"),n=e.loginEndTime.format("HH:mm").split(":"));var o=parseInt(a[0]),i=parseInt(a[1]),l=parseInt(n[0]),s=parseInt(n[1]),r=[];do{if(r.push(c(o,i)),o===l&&i===s)break;if(i<59&&i>=0)i++;else{if(59!==i){e.$message.error("在线时点分析图表构造出错！");break}o++,i=0}}while(1);function c(e,t){var a=""+e,n=""+t;return a.length<2&&(a="0"+a),n.length<2&&(n="0"+n),a+":"+n}return r.length<=60?"online"===t?e.onlineXInterval=4:"login"===t&&(e.loginXInterval=4):r.length>60&&("online"===t?e.onlineXInterval=parseInt(r.length/10-1):"login"===t&&(e.loginXInterval=parseInt(r.length/10-1))),r},sortStatLogDataByHours:function(e,t){var a=[],n=[],o=[];"online"===t?(a=e.onlineChartData,n=e.onlineStartTime.format("HH:mm").split(":"),o=e.onlineEndTime.format("HH:mm").split(":")):"login"===t&&(a=e.loginChartData,n=e.loginStartTime.format("HH:mm").split(":"),o=e.loginEndTime.format("HH:mm").split(":"));var i=parseInt(n[0]),l=parseInt(n[1]),s=parseInt(o[0]),r=parseInt(o[1]),c=[],u=0;do{var d=""+i,h=""+l;d.length<2&&(d="0"+d),h.length<2&&(h="0"+h);var m=d+":"+h;for(var f in a)m===a[f].type&&c.push(a[f].count);if(u++,c.length<u&&c.push(0),i===s&&l===r)break;if(l<59&&l>=0)l++;else{if(59!==l){e.$message.error("时点分析图表构造出错！");break}i++,l=0}}while(1);return c}}},c=r,u=a(1001),d=(0,u.Z)(c,n,o,!1,null,"7c85dc2c",null),h=d.exports},13035:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return c}});var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"User"},[a("ta-border-layout",{attrs:{showBorder:!1,layout:{left:"130px",right:"400px"},leftCfg:Object.assign({},e.config,{layoutConStyle:Object.assign({},e.config.layoutConStyle,{display:"table"})}),rightCfg:e.config,centerCfg:Object.assign({},e.config,{layoutConStyle:Object.assign({},e.config.layoutConStyle,{display:"table","border-right":"1px solid rgb(218,218,218)"})})}},[a("div",{staticClass:"left",attrs:{slot:"left"},slot:"left"},[a("ta-avatar",{attrs:{size:72,icon:"user"}})],1),a("div",{staticClass:"main"},[a("div",{staticClass:"greeting"},[e._v(e._s(e.greeting)+"，"+e._s(e.userName)+"，祝你开心每一天！")]),a("div",{staticClass:"info"},[e._v("Tips: Today is also a day full of hope, come on!")])]),a("div",{staticClass:"right",attrs:{slot:"right"},slot:"right"},[a("div",{staticClass:"timeDiv"},[a("span",{staticClass:"time"},[e._v(e._s(e.time))])]),a("div",{staticClass:"dateDiv"},[a("span",{staticClass:"date"},[e._v(e._s(e.dayOfWeek)+" "),a("br"),e._v(" "+e._s(e.date))])])])])],1)},o=[],i=(a(32564),{name:"user",props:{moduleId:String},data:function(){var e=new Array("星期日","星期一","星期二","星期三","星期四","星期五","星期六");return{config:{showBorder:!1,layoutConStyle:{overflow:"hidden"}},arrWeek:e,greeting:"",userName:"",date:"",time:"",dayOfWeek:"",timer:""}},created:function(){var e=this;this.userName=top.indexTool.getUserInfo().userName,this.timer=setInterval(this.refresh,6e4),this.$once("hook:beforeDestroy",(function(){clearInterval(e.timer)}))},mounted:function(){this.refresh();var e=new Date,t=e.getHours(),a=e.getMinutes();t<12&&(this.greeting=a%2===0?"早上好":"早安"),t<19&&t>=12&&(this.greeting="下午好"),t<=23&&t>=19&&(this.greeting="晚上好")},methods:{refresh:function(){var e=new Date,t=e.getFullYear(),a=e.getMonth()+1,n=e.getDate(),o=e.getHours(),i=e.getMinutes();a<10&&(a="0"+a),e<10&&(e="0"+e),o<10&&(o="0"+o),i<10&&(i="0"+i),this.date=t+"年"+a+"月"+n+"日",this.time=o+" : "+i,this.dayOfWeek=this.arrWeek[e.getDay()]}}}),l=i,s=a(1001),r=(0,s.Z)(l,n,o,!1,null,"222a0f9a",null),c=r.exports},18461:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return N}});var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"page"},[e.showAll?a("work-table",{attrs:{"layout-props":e.layout,editable:e.editable,type:e.type,"is-role-template":e.isRoleTemplate},on:{deleteItem:e.deleteItem,changeTitleStatus:e.changeTitleStatus,cancel:e.cancel,add:e.add,save:e.save,swap:e.swap}}):e._e(),a("addItemModal",{attrs:{"all-components":e.allComponents,checked:e.checked,visible:e.addItemModalVisible,layout:e.layout},on:{"update:visible":function(t){e.addItemModalVisible=t},onChange:e.onCkbgChange}}),a("switchModal",{attrs:{visible:e.switchModalVisible,"confirm-loading":e.confirmLoading,"template-tags":e.templateTags,"template-selected-tag":e.templateSelectedTag},on:{"update:visible":function(t){e.switchModalVisible=t},"update:templateSelectedTag":function(t){e.templateSelectedTag=t},"update:template-selected-tag":function(t){e.templateSelectedTag=t},generateWorktable:e.generateWorktable}}),a("ta-page-tool",{attrs:{"tool-menu":e.toolMenu,"is-move":!0,"is-refresh":!1}})],1)},o=[],i=(a(32564),function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"formWork"},[a("transition",{attrs:{name:"operateTools"}},[a("div",{directives:[{name:"show",rawName:"v-show",value:e.editable,expression:"editable"}],staticClass:"operateTool"},[a("ta-button",{staticClass:"operateBtn swap",attrs:{disabled:"1"===e.type,icon:"swap"},on:{click:function(t){return e.$emit("swap")}}},[e._v(" 切换工作台 ")]),a("ta-button",{directives:[{name:"show",rawName:"v-show",value:!e.isRoleTemplate,expression:"!isRoleTemplate"}],staticClass:"operateBtn save",on:{click:function(t){return e.$emit("save")}}},[e._v(" 保存 ")]),a("ta-button",{directives:[{name:"show",rawName:"v-show",value:!e.isRoleTemplate,expression:"!isRoleTemplate"}],staticClass:"operateBtn",on:{click:function(t){return e.$emit("add")}}},[e._v(" 新增 ")]),a("ta-button",{staticClass:"operateBtn",on:{click:function(t){return e.$emit("cancel")}}},[e._v(" 取消 ")])],1)]),a("grid-layout",{attrs:{layout:e.layoutProps,"col-num":14,"row-height":25,"is-draggable":e.editable,"is-resizable":e.editable,"vertical-compact":!0,"use-css-transforms":!1}},e._l(e.layoutProps,(function(t){return a("grid-item",{key:t.i,class:{active:e.editable},staticStyle:{"touch-action":"none"},attrs:{x:t.x,y:t.y,w:t.w,h:t.h,i:t.i,"min-w":3,"min-h":4,"drag-allow-from":".vue-draggable-handle","drag-ignore-from":".no-drag"}},[a("item",{staticClass:"item",attrs:{"module-id":t.i,"module-name":t.name,"module-path":t.url,"item-fixed":t.itemFixed,editable:e.editable},on:{deleteItem:e.deleteItem,changeTitleStatus:e.changeTitleStatus}})],1)})),1)],1)}),l=[],s=a(23533),r=a.n(s),c=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"item",class:{contentHeight:!e.itemFixed,headFixed:e.editable&&!e.itemFixed}},[a("div",{staticClass:"head vue-draggable-handle",class:{headNotFixed:!e.itemFixed,moveCursor:e.editable}},[a("span",{staticClass:"title"},[e._v(e._s(e.moduleName))]),a("div",{staticStyle:{float:"right",display:"table",height:"56px"}},[a("deleteIcon",{directives:[{name:"show",rawName:"v-show",value:e.editable,expression:"editable"}],staticClass:"deleteIcon",staticStyle:{display:"table-cell","vertical-align":"middle","text-align":"center"},attrs:{title:"删除组件"},on:{deleteItem:function(t){return e.$emit("deleteItem",e.moduleId)}}}),a("ta-icon",{directives:[{name:"show",rawName:"v-show",value:e.editable,expression:"editable"}],staticClass:"fixedIcon",class:{iconColor:e.itemFixed},staticStyle:{display:"table-cell","vertical-align":"middle","text-align":"center"},attrs:{title:"固定组件标题",type:"pushpin"},on:{click:e.changeTitleStatus}})],1)]),a("div",{staticClass:"content"},[e.exists?a(this.modulePath,{tag:"component",staticClass:"component",attrs:{"module-id":e.moduleId}}):a("iframe",{attrs:{src:"./404.html"}})],1)])},u=[],d={template:'<svg width="14px" height="14px" fill="currentColor"  viewBox="0 0 1024 1024">\n                    <path d="M332.308,151.615h359.385c0.276,0,0.5-0.224,0.5-0.5V80.038c0-0.276-0.224-0.5-0.5-0.5H332.308c-0.276,0-0.5,0.224-0.5,0.5v71.077C331.808,151.392,332.032,151.615,332.308,151.615z"/>\n                    <path d="M980,223.692H836.346H643.641h-71.077H451.436h-71.077H187.654H44c-0.276,0-0.5,0.224-0.5,0.5v71.077c0,0.276,0.224,0.5,0.5,0.5h143.654v549.587c0,54.735,44.371,99.106,99.106,99.106H737.24c54.735,0,99.106-44.371,99.106-99.106V295.769H980c0.276,0,0.5-0.224,0.5-0.5v-71.077C980.5,223.916,980.276,223.692,980,223.692z M764.269,845.356c0,14.905-12.124,27.029-27.029,27.029H286.76c-14.905,0-27.029-12.124-27.029-27.029V295.769h120.128v431.962c0,0.276,0.224,0.5,0.5,0.5h71.077c0.276,0,0.5-0.224,0.5-0.5V295.769h120.128v431.962c0,0.276,0.224,0.5,0.5,0.5h71.077c0.276,0,0.5-0.224,0.5-0.5V295.769h120.128V845.356z"/>\n                </svg>'},h={template:'<ta-icon @click="$emit(\'deleteItem\')" :component="deleteSvg" />',data:function(){return{deleteSvg:d}}},m={name:"Item",props:{moduleId:String,moduleName:String,modulePath:String,editable:Boolean,itemFixed:{type:Boolean,default:!0}},data:function(){return{exists:!1}},components:{deleteIcon:h},created:function(){var e=a(28786).keys();e.includes("./".concat(this.modulePath,".vue"))&&(this.exists=!0,this.$options.components[this.modulePath]=a(29736)("./".concat(this.modulePath,".vue")).default)},methods:{changeTitleStatus:function(){this.$emit("changeTitleStatus",!this.itemFixed,this.moduleId)}}},f=m,p=a(1001),g=(0,p.Z)(f,c,u,!1,null,"555c5384",null),y=g.exports,v={name:"WorkTable",components:{GridLayout:r().GridLayout,GridItem:r().GridItem,item:y},props:{layoutProps:{type:Array,default:function(){return[]},require:!0},editable:{type:Boolean,default:!1,require:!0},type:{type:String,default:"0",require:!0},isRoleTemplate:{type:Boolean,default:!1,require:!0}},methods:{deleteItem:function(e){this.layoutProps.forEach((function(t,a,n){t.i===e&&n.splice(a,1)})),this.$emit("deleteItem",e)},changeTitleStatus:function(e,t){this.$emit("changeTitleStatus",e,t)}},computed:{},watch:{}},b=v,w=(0,p.Z)(b,i,l,!1,null,"59a451ca",null),k=w.exports,I=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("ta-modal",{attrs:{title:"添加组件","destroy-on-close":!0,closable:!1,visible:e.visible}},[a("ta-checkbox-group",{staticClass:"checkboxGroup",attrs:{options:e.getAllComponents,"default-value":e.checked},on:{change:e.onChange}}),a("template",{slot:"footer"},[a("ta-button",{on:{click:function(t){return e.$emit("update:visible",!1)}}},[e._v(" 关闭 ")])],1)],2)],1)},C=[],T={name:"AddItem",props:{checked:{type:Array,default:[],require:!0},visible:{type:Boolean,default:!1,require:!0},allComponents:{type:Array,default:[],require:!0}},computed:{getAllComponents:function(){return this.allComponents}},methods:{onChange:function(e){this.$emit("onChange",e)}}},S=T,x=(0,p.Z)(S,I,C,!1,null,"0a5ebf62",null),_=x.exports,B=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("ta-modal",{attrs:{title:"选择工作台",visible:e.visible,"destroy-on-close":!0,"on-ok":"generateWorkTable",closable:!1}},[a("ta-tag-select",{staticClass:"tagSelect",attrs:{data:e.templateTags,value:e.templateSelectedTag,required:""},on:{change:e.onTagSelectChange}}),a("template",{slot:"footer"},[a("ta-button",{on:{click:function(t){return e.$emit("update:visible",!1)}}},[e._v(" 取消 ")]),a("ta-button",{key:"submit",attrs:{type:"primary",loading:e.confirmLoading},on:{click:e.generateWorktable}},[e._v(" 确定 ")])],1)],2)],1)},L=[],E={name:"SwitchModal",props:{visible:{type:Boolean,default:!1,require:!0},confirmLoading:{type:Boolean,default:!1,require:!0},templateTags:{type:Array,require:!0},templateSelectedTag:{type:Array,require:!0}},methods:{generateWorktable:function(){this.$emit("generateWorktable")},onTagSelectChange:function(e){0!==e.length&&this.$emit("update:templateSelectedTag",e)}}},D=E,R=(0,p.Z)(D,B,L,!1,null,"1789b6f2",null),M=R.exports,$="/org/sysmg/workbenchRestService/",W={queryRoleListByUserId:function(e,t){Base.submit(null,{url:$+"queryRoleListByUserId",data:e},{successCallback:function(e){return t(e)}})},queryLastChooseWorkbench:function(e,t){Base.submit(null,{url:$+"queryLastChooseWorkbench",data:e},{successCallback:function(e){return t(e)}})},queryUserWorkbenchByUserId:function(e,t){Base.submit(null,{url:$+"queryUserWorkbenchByUserId",data:e},{successCallback:function(e){return t(e)}})},queryRoleWorkbenchByRoleId:function(e,t){Base.submit(null,{url:$+"queryRoleWorkbenchByRoleId",data:e},{successCallback:function(e){return t(e)}})},queryResourceEffectiveByUserId:function(e,t){Base.submit(null,{url:$+"queryResourceEffectiveByUserId",data:e},{successCallback:function(e){return t(e)}})},queryResourceEffectiveByRoleId:function(e,t){Base.submit(null,{url:$+"queryResourceEffectiveByRoleId",data:e},{successCallback:function(e){return t(e)}})},saveUserWorkbench:function(e,t){Base.submit(null,{url:$+"saveUserWorkbench",data:e},{successCallback:function(e){return t(e)}})},saveRoleWorkbench:function(e,t){Base.submit(null,{url:$+"saveRoleWorkbench",data:e},{successCallback:function(e){return t(e)}})},saveLastChooseWorkbenchData:function(e){Base.submit(null,{url:$+"saveLastChooseWorkbenchData",data:e})},queryCurrentUserLastLoginLog:function(e,t){Base.submit(null,{url:$+"queryCurrentUserLastLoginLog",data:e},{successCallback:function(e){return t(e)}})}},q={name:"Page",components:{workTable:k,addItemModal:_,switchModal:M},data:function(){var e=[{icon:"edit",name:"编辑",onClick:this.edit},{icon:"sync",name:"刷新",onClick:function(){top.indexTool.reload()}}];return{toolMenu:e,type:"0",roleId:"",templateTags:[{value:"user",label:"自定义"}],templateSelectedTag:["user"],oldLayout:[],layout:[],checked:[],showAll:!1,isRoleTemplate:!1,editable:!1,addItemModalVisible:!1,allComponents:[],switchModalVisible:!1,confirmLoading:!1}},watch:{$route:{handler:function(e){var t=this;this.type=e.query.type,this.roleId=e.query.roleId,"page"===e.name?"1"===this.type?W.queryRoleWorkbenchByRoleId({roleId:this.roleId},(function(e){t.layout=e.data.layout,t.checked.splice(0,t.checked.length),t.layout.forEach((function(e){t.checked.push(e.i)})),t.showAll=!0,t.$message.info("请点击右下角编辑按钮修改角色默认模板！")})):(this.type="0",W.queryLastChooseWorkbench({},(function(e){t.layout=e.data.layout,t.checked.splice(0,t.checked.length),t.layout.forEach((function(e){t.checked.push(e.i)})),null!==e.data.warn&&void 0!==e.data.warn&&t.$message.warn(e.data.warn),null!==e.data.roleId&&void 0!==e.data.roleId&&(t.isRoleTemplate=!0,t.templateSelectedTag[0]=e.data.roleId),t.showAll=!0}))):(this.editable=!1,this.addItemModalVisible=!1,this.switchModalVisible=!1)},deep:!0,immediate:!0}},mounted:function(){"0"===this.type&&setTimeout(this.openNotification(),5e3)},methods:{openNotification:function(){var e=this,t=this.$createElement,a={pageNumber:1,pageSize:1};W.queryCurrentUserLastLoginLog(a,(function(a){if(a.data.pageBean&&a.data.pageBean.list&&0!==a.data.pageBean.list.length){var n=a.data.pageBean.list[0];e.$notification.info({message:"最近登录通知",description:t("div",[t("p",["登录IP: ",n.clientIp]),t("p",["客户端: ",n.clientSystem]),t("p",["登录时间: ",n.loginTime]),t("p",["退出时间: ",n.logoutTime])]),duration:10})}}))},generateWorktable:function(){var e=this;this.confirmLoading=!0,"user"===this.templateSelectedTag[0]?W.queryUserWorkbenchByUserId({},(function(t){e.layout=t.data.layout,e.checked.splice(0,e.checked.length),e.layout.forEach((function(t){e.checked.push(t.i)})),e.isRoleTemplate=!1,e.switchModalVisible=!1,e.confirmLoading=!1,e.showAll=!0,null!==t.data.warn&&void 0!==t.data.warn&&e.$message.warn(t.data.warn);var a={isRole:0};e.editable=!1,W.saveLastChooseWorkbenchData({lastChoose:JSON.stringify(a)})})):W.queryRoleWorkbenchByRoleId({roleId:this.templateSelectedTag[0]},(function(t){e.layout=t.data.layout,e.switchModalVisible=!1,e.confirmLoading=!1,e.isRoleTemplate=!0,e.showAll=!0,null!==t.data.warn&&void 0!==t.data.warn&&e.$message.warn(t.data.warn);var a={isRole:1};a.roleId=e.templateSelectedTag[0],e.templateTags.forEach((function(t){t.value===e.templateSelectedTag[0]&&(a.roleName=t.label)})),e.editable=!1,W.saveLastChooseWorkbenchData({lastChoose:JSON.stringify(a)})}))},edit:function(){var e=this;!0!==this.editable?(this.oldLayout.splice(0,this.oldLayout.length),this.layout.forEach((function(t){e.oldLayout.push(Object.assign({},t))})),this.editable=!0):this.cancel()},cancel:function(){var e=this;this.layout=this.oldLayout.slice(0),this.checked.splice(0,this.checked.length),this.editable=!1,this.layout.forEach((function(t){e.checked.push(t.i)})),"1"===this.type&&this.$nextTick((function(){window.parent.indexTool.closeTabMenu("roleWorktableTemplateModify"),window.parent.indexTool.openTabMenu({id:"0aac95c1e73947bea41be639cc4e9036"})}))},add:function(){var e=this;"1"===this.type?W.queryResourceEffectiveByRoleId({roleId:this.roleId},(function(t){e.allComponents=t.data.components})):W.queryResourceEffectiveByUserId({},(function(t){e.allComponents=t.data.components})),this.addItemModalVisible=!0},save:function(){var e=this;"1"===this.type?this.$confirm({title:"",content:"确定保存吗？保存后该页面将关闭！",okText:"确定",cancelText:"取消",onOk:function(){W.saveRoleWorkbench({roleId:e.roleId,workbenchStr:JSON.stringify(e.layout)},(function(t){e.$message.success("保存成功！"),window.parent.indexTool.closeTabMenu("roleWorktableTemplateModify"),window.parent.indexTool.openTabMenu({id:"0aac95c1e73947bea41be639cc4e9036",exist:!0}),e.editable=!1,e.showAll=!1}))}}):W.saveUserWorkbench({workbenchStr:JSON.stringify(this.layout)},(function(t){e.$message.success("保存成功！"),e.editable=!1}))},deleteItem:function(e){this.checked.splice(this.checked.indexOf(e),1)},changeTitleStatus:function(e,t){this.layout.forEach((function(a){a.i===t&&(a.itemFixed=e)}))},onCkbgChange:function(e){var t=this,a=0;this.layout.forEach((function(e){e.y+e.h>a&&(a=e.y+e.h)}));var n=this.getArrDifference(this.checked,e);n.forEach((function(n){t.checked.includes(n)&&t.layout.forEach((function(e,t,a){e.i===n&&a.splice(t,1)})),e.includes(n)&&t.allComponents.forEach((function(e){e.value===n&&t.layout.push({x:0,y:a,w:14,h:13,minW:2,minH:4,i:n,name:e.label,url:e.url,itemFixed:!0})}))})),this.checked=e},getArrDifference:function(e,t){return e.concat(t).filter((function(e,t,a){return a.indexOf(e)===a.lastIndexOf(e)}))},swap:function(){var e=this;W.queryRoleListByUserId({},(function(t){0!==t.data.roles.length&&(e.templateTags.splice(0,e.templateTags.length-1),t.data.roles.forEach((function(t){e.templateTags.unshift({value:t.roleId,label:t.roleName})}))),e.switchModalVisible=!0}))}}},H=q,A=(0,p.Z)(H,n,o,!1,null,"2a6a8f9a",null),N=A.exports},29736:function(e,t,a){var n={"./analysis.vue":39444,"./monitor.vue":17725,"./user.vue":13035};function o(e){var t=i(e);return a(t)}function i(e){if(!a.o(n,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return n[e]}o.keys=function(){return Object.keys(n)},o.resolve=i,e.exports=o,o.id=29736},28786:function(e,t,a){var n={"./analysis.vue":39444,"./monitor.vue":17725,"./user.vue":13035};function o(e){var t=i(e);return a(t)}function i(e){if(!a.o(n,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return n[e]}o.keys=function(){return Object.keys(n)},o.resolve=i,e.exports=o,o.id=28786}}]);
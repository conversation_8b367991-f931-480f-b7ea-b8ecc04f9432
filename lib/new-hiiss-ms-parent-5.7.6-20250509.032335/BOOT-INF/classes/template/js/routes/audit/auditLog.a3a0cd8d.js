"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[3453],{28343:function(t,a,e){e.r(a),e.d(a,{default:function(){return x}});var i=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticClass:"fit",attrs:{id:"auditLog"}},[e("ta-border-layout",{attrs:{layout:{header:"70px",footer:"70px"},centerCfg:{showBar:!0,toolBarStyle:{height:"50px",lineHeight:"48px"}},"footer-cfg":{showBorder:!1}}},[e("div",{staticClass:"center-box",attrs:{slot:"header"},slot:"header"},[e("ta-input-search",{staticClass:"search-box",attrs:{placeholder:"用户搜索",enterButton:"搜索"},on:{search:t.searchQuery},model:{value:t.param,callback:function(a){t.param=a},expression:"param"}})],1),e("div",{attrs:{slot:"centerExtraContent"},slot:"centerExtraContent"},[e("ta-range-picker",{staticClass:"distance",attrs:{placeholder:["开始时间","结束时间"],"get-calendar-container":t.getContainer},on:{change:t.onChange},model:{value:t.createTime,callback:function(a){t.createTime=a},expression:"createTime"}}),e("ta-select",{staticClass:"filter-name",staticStyle:{width:"220px"},attrs:{allowClear:"",placeholder:"请选择审计类型"},on:{change:t.loadData},model:{value:t.auditType,callback:function(a){t.auditType=a},expression:"auditType"}},t._l(t.CollectionData("AUDITTYPE"),(function(a){return e("ta-select-option",{key:a.value,attrs:{value:a.value}},[t._v(t._s(a.label))])})),1),e("div",{staticStyle:{float:"right"}},[e("ta-button",{attrs:{icon:"area-chart"},on:{click:function(a){return t.showChart()}}})],1)],1),e("ta-table",{attrs:{columns:t.columns,dataSource:t.gridData,pagination:!1},scopedSlots:t._u([{key:"auditType",fn:function(a){return e("span",{},[t._v(t._s(t.CollectionLabel("AUDITTYPE",a)))])}},{key:"storeType",fn:function(a){return e("span",{},[t._v(t._s(t.CollectionLabel("STORETYPE",a)))])}}])}),e("div",{attrs:{slot:"footer"},slot:"footer"},[e("ta-pagination",{ref:"gridPager",staticStyle:{float:"right","margin-top":"10px"},attrs:{showSizeChanger:"",showQuickJumper:"",dataSource:t.gridData,params:t.pageParams,url:"audit/taAuditResService/audit"},on:{"update:dataSource":function(a){t.gridData=a},"update:data-source":function(a){t.gridData=a}}})],1)],1),e("ta-modal",{attrs:{centered:"",width:"800px",bodyStyle:{height:"500px",padding:"0"},footer:null,destroyOnClose:!0,maskClosable:!0,title:"数据分析（默认显示最近7天）",closable:!0},on:{close:function(a){return t.closeChart(!1)}},model:{value:t.showChartVisible,callback:function(a){t.showChartVisible=a},expression:"showChartVisible"}},[e("audit-chart",{ref:"auditChart",attrs:{queryData:t.queryData},on:{closeModal:t.closeChart}})],1)],1)},n=[],r=e(36797),s=e.n(r),o=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticStyle:{height:"100%","overflow-x":"auto"}},[e("div",{staticStyle:{padding:"30px 30px 0 50px"}},[e("ta-e-charts",{attrs:{chartsType:"chart",data:t.chartData,"data-empty":t.dataEmpty,settings:t.chartSettings}})],1)])},l=[],d=e(99440),c=(e(39551),{auditChart:function(t,a){Base.submit(null,{url:"audit/taAuditResService/auditChart",data:t},{successCallback:function(t){return a(t)}})}}),u={name:"auditChart",props:["queryData"],data:function(){return this.typeArr=["line","histogram","pie"],this.index=2,{chartData:{columns:["审计类型","审计类型统计数"],rows:[]},dataEmpty:!1,chartSettings:{type:this.typeArr[this.index]},chartsType:this.typeArr[this.index]}},components:{TaECharts:d.Z},mounted:function(){this.setValue()},methods:{setValue:function(){var t=this;this.dataEmpty=!1,this.chartData.rows=[],c.auditChart(this.queryData,(function(a){0==Object.keys(a.data.auditChartData).length&&(t.dataEmpty=!0);for(var e=0;e<a.data.auditChartData.length;e++){var i=[];i={"审计类型":a.data.auditChartData[e].label,"审计类型统计数":a.data.auditChartData[e].count},t.chartData.rows.push(i)}}))},changeType:function(){this.index++,this.index>=this.typeArr.length&&(this.index=0),this.chartSettings={type:this.typeArr[this.index]}}}},h=u,p=e(1001),m=(0,p.Z)(h,o,l,!1,null,null,null),f=m.exports,y=[{title:"用户",dataIndex:"name",width:"10%",scopedSlots:{customRender:"name"}},{title:"审计内容",dataIndex:"auditContent",width:"10%",overflowTooltip:!0,scopedSlots:{customRender:"auditContent"}},{title:"审计时间",dataIndex:"auditDate",width:"15%",scopedSlots:{customRender:"auditDate"}},{title:"excel名",dataIndex:"excelName",width:"15%",overflowTooltip:!0,scopedSlots:{customRender:"excelName"}},{title:"excel别名",dataIndex:"aliasExcelName",width:"10%",overflowTooltip:!0,scopedSlots:{customRender:"aliasExcelName"}},{title:"审计类型",dataIndex:"auditType",width:"10%",scopedSlots:{customRender:"auditType"}},{title:"存储类型",dataIndex:"storeType",width:"8%",scopedSlots:{customRender:"storeType"}}],D={name:"app",data:function(){return{columns:y,onlineDate:null,onlineStartTime:null,onlineEndTime:null,createTime:[],gridData:[],param:"",startDate:"",endDate:"",showChartVisible:!1,accessDenyTypeParam:"",auditType:void 0,queryData:[]}},components:{auditChart:f},mounted:function(){this.loadData()},methods:{moment:s(),pageParams:function(){var t={};return t.startDate=this.startDate,t.endDate=this.endDate,t.name=this.param,t.auditType=this.auditType,t},onChange:function(t,a){a&&a.length&&""!=a[0]&&""!=a[1]?(this.startDate=s()(a[0]).format("YYYY-MM-DD")+" 00:00:00",this.endDate=s()(a[1]).format("YYYY-MM-DD")+" 23:59:59"):(this.startDate="",this.endDate=""),this.loadData()},loadData:function(){this.$refs.gridPager.loadData()},searchQuery:function(){this.loadData()},onSelectChange:function(t,a){this.selectedRowKeys=t},closeChart:function(t){this.showChartVisible=!1},showChart:function(){this.showChartVisible=!0,this.queryData={startDate:this.startDate,endDate:this.endDate,name:this.param,auditType:this.auditType}},getContainer:function(){return document.getElementById("auditLog")}}},g=D,C=(0,p.Z)(g,i,n,!1,null,"60b5a783",null),x=C.exports}}]);
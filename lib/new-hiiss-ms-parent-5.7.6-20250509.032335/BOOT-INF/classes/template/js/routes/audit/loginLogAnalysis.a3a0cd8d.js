"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[499],{761:function(t,e,n){n.r(e),n.d(e,{default:function(){return u}});var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"fit",attrs:{id:"loginLogAnalysis"}},[n("ta-tabs",{staticClass:"fit",on:{change:t.tabChange},model:{value:t.activeKey,callback:function(e){t.activeKey=e},expression:"activeKey"}},[n("ta-tab-pane",{key:"online",attrs:{tab:"用户在线时点分析"}},[n("keep-alive",[n("router-view")],1)],1),n("ta-tab-pane",{key:"loginHistory",attrs:{tab:"用户登录时点分析"}},[n("keep-alive",[n("router-view",{attrs:{name:"loginHistory"}})],1)],1),n("ta-tab-pane",{key:"environment",attrs:{tab:"用户登录环境分析"}},[n("keep-alive",[n("router-view",{attrs:{name:"environment"}})],1)],1)],1)],1)},i=[],s={name:"loginLogAnalysis",data:function(){return{activeKey:"online"}},methods:{tabChange:function(t){"online"===t?this.$router.push({name:"online"}):"loginHistory"===t?this.$router.push({name:"loginHistory"}):"environment"===t&&this.$router.push({name:"environment"})}},activated:function(){this.activeKey&&this.$router.push({name:this.activeKey})}},r=s,o=n(1001),l=(0,o.Z)(r,a,i,!1,null,"1b8072f0",null),u=l.exports}}]);
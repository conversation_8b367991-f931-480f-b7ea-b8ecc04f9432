"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[5508],{25883:function(t,e,a){a.r(e),a.d(e,{default:function(){return b}});var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"fit",attrs:{id:"systemExceptionLog"}},[a("ta-border-layout",{attrs:{layout:{header:"70px",footer:"70px"},centerCfg:{showBar:!0,toolBarStyle:{height:"50px",lineHeight:"48px"}},"footer-cfg":{showBorder:!1}}},[a("div",{staticClass:"center-box",attrs:{slot:"header"},slot:"header"},[a("ta-input-search",{staticClass:"search-box",attrs:{placeholder:"请输入服务器异常ID、IP地址、访问端口",enterButton:"搜索"},on:{search:t.loadData},model:{value:t.serverAddress,callback:function(e){t.serverAddress=e},expression:"serverAddress"}})],1),a("div",{attrs:{slot:"centerExtraContent"},slot:"centerExtraContent"},[a("ta-range-picker",{attrs:{placeholder:["开始时间","结束时间"],"get-calendar-container":t.getContainer},on:{change:t.onChange},model:{value:t.createTime,callback:function(e){t.createTime=e},expression:"createTime"}}),a("div",{staticStyle:{float:"right"}},[a("ta-button",{attrs:{icon:"area-chart"},on:{click:function(e){return t.showChart()}}})],1)],1),a("ta-table",{attrs:{columns:t.columns,dataSource:t.gridData,rowKey:"logId",pagination:!1},scopedSlots:t._u([{key:"operate",fn:function(e,o){return a("span",{},[a("ta-table-operate",{attrs:{operateMenu:t.operateMenu}})],1)}}])}),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("ta-pagination",{ref:"gridPager",staticStyle:{float:"right","margin-top":"10px"},attrs:{showSizeChanger:"",showQuickJumper:"",dataSource:t.gridData,defaultPageSize:10,params:t.pageParams,url:"logmg/exceptionlog/serverExceptionLogRestService/getServerExceptionLog"},on:{"update:dataSource":function(e){t.gridData=e},"update:data-source":function(e){t.gridData=e}}})],1)],1),a("ta-modal",{attrs:{centered:"",footer:null,width:"1000px",destroyOnClose:!0},model:{value:t.visible,callback:function(e){t.visible=e},expression:"visible"}},[a("ta-label-con",{staticStyle:{"margin-bottom":"0px"},attrs:{label:"服务地址",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol}},[a("span",[t._v(t._s(t.detailExceptionInfo.server))])]),a("ta-label-con",{staticStyle:{"margin-bottom":"20px"},attrs:{label:"异常类型",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol}},[a("span",[t._v(t._s(t.detailExceptionInfo.exceptionName))])]),a("ta-label-con",{staticStyle:{"margin-bottom":"0px"},attrs:{label:"异常信息",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol}},[a("ta-textarea",{staticClass:"textarea",attrs:{autosize:{minRows:20,maxRows:20},readonly:"readonly"},model:{value:t.detailExceptionInfo.contentStr,callback:function(e){t.$set(t.detailExceptionInfo,"contentStr",e)},expression:"detailExceptionInfo.contentStr"}})],1)],1),a("ta-modal",{attrs:{centered:"",width:"1000px",bodyStyle:{height:"500px",padding:"0"},footer:null,destroyOnClose:!0,maskClosable:!0,title:"异常次数图",closable:!0},on:{close:function(e){return t.closeChart(!1)}},model:{value:t.showChartVisible,callback:function(e){t.showChartVisible=e},expression:"showChartVisible"}},[a("system-exception-chart",{ref:"systemExceptionChart",attrs:{queryData:t.queryData},on:{closeModal:t.closeChart}})],1)],1)},n=[],r="/logmg/exceptionlog/serverExceptionLogRestService/",i={deleteExceptionLogByLogId:function(t,e){Base.submit(null,{url:r+"deleteExceptionLogByLogId",data:t},{successCallback:function(t){return e(t)}})},getDetalExceptionLog:function(t,e){Base.submit(null,{url:r+"getDetalExceptionLog",data:t},{successCallback:function(t){return e(t)}})},batchDeleteExceptionLog:function(t,e){Base.submit(null,{url:r+"batchDeleteExceptionLog",data:t},{successCallback:function(t){return e(t)}})},exceptionChart:function(t,e){Base.submit(null,{url:r+"exceptionChart",data:t},{successCallback:function(t){return e(t)}})}},s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{height:"100%","overflow-x":"auto"}},[a("div",{staticStyle:{padding:"30px 30px 0 50px"}},[a("ta-radio-group",{attrs:{value:t.buttonValue},on:{change:t.handleChange}},[a("ta-popover",{attrs:{width:"100",trigger:"hover",content:"按当天日期绘图"}},[a("ta-radio-button",{attrs:{slot:"reference",value:"byHour"},on:{click:function(e){return t.changeType("0")}},slot:"reference"},[t._v("按小时")])],1),a("ta-radio-button",{attrs:{value:"byDay"},on:{click:function(e){return t.changeType("1")}}},[t._v("按天")])],1),a("p",{directives:[{name:"show",rawName:"v-show",value:"1"==t.type,expression:"type == '1'"}],staticClass:"defaultp"},[t._v(" 默认显示最近7天")]),a("ta-e-charts",{attrs:{"charts-type":"line",data:t.chartData,"data-empty":t.dataEmpty,settings:t.chartSettings,extend:t.chartExtend}})],1)])},l=[],c=a(99440),d=(a(39551),{name:"systemExceptionChart",props:["queryData"],data:function(){return{chartData:{columns:["日期","异常次数"],rows:[]},chartSettings:{area:!0},buttonValue:"byHour",dataEmpty:!1,chartExtend:{series:{smooth:!1}},type:"0"}},components:{TaECharts:c.Z},mounted:function(){this.setValue()},methods:{setValue:function(){var t=this;this.dataEmpty=!1,this.chartData.rows=[],this.queryData["showType"]=this.type,i.exceptionChart(this.queryData,(function(e){for(var a in 0==Object.keys(e.data.exceptionChartData).length&&(t.dataEmpty=!0),e.data.exceptionChartData){var o=[];o["日期"]=a,o["异常次数"]=e.data.exceptionChartData[a],t.chartData.rows.push(o)}}))},changeType:function(t){this.type=t,this.setValue()},handleChange:function(t){this.buttonValue=t.target.value}}}),u=d,p=a(1001),h=(0,p.Z)(u,s,l,!1,null,null,null),f=h.exports,m=[{title:"异常类型",dataIndex:"exceptionName",width:"25%",overflowTooltip:!0},{title:"访问URL",dataIndex:"url",width:"25%",overflowTooltip:!0},{title:"服务器ip地址",dataIndex:"ipAddress",width:"14%",overflowTooltip:!0},{title:"访问端口",dataIndex:"port",width:"80px"},{title:"客户端ip地址",dataIndex:"clientIp",width:"14%",overflowTooltip:!0},{title:"报错时间",dataIndex:"createTime",width:"11%",overflowTooltip:!0},{title:"数据是否被篡改",dataIndex:"isTampered",width:"10%",overflowTooltip:!0},{title:"操作选项",dataIndex:"operate",width:"120px",scopedSlots:{customRender:"operate"},align:"center"}],g={name:"systemExceptionLog",components:{systemExceptionChart:f},data:function(){var t=this;return{formItemLayout:{labelCol:{span:2},wrapperCol:{span:22}},serverAddress:"",createTime:[],startDate:"",endDate:"",visible:!1,detailExceptionInfo:{server:"",exceptionName:"",contentStr:""},columns:m,operateMenu:[{name:"详细异常信息",onClick:function(e){t.showModal(e)}}],gridData:[],showChartVisible:!1,queryData:[]}},mounted:function(){this.loadData()},methods:{loadData:function(){this.$refs.gridPager.loadData((function(t){}))},pageParams:function(){if(!(""!==this.startDate&&""!==this.endDate&&new Date(this.startDate).getTime()>new Date(this.endDate).getTime()))return{startDate:this.startDate,endDate:this.endDate,serverAddress:this.serverAddress};this.$message.error("开始日期不能大于结束日期")},onChange:function(t,e){this.startDate=e[0],this.endDate=e[1],this.loadData()},onDelete:function(t){var e=this,a={logId:t};i.deleteExceptionLogByLogId(a,(function(t){e.$message.success("删除成功"),e.loadData()}))},showModal:function(t){var e=this;this.visible=!0;var a={logId:t.logId};i.getDetalExceptionLog(a,(function(t){e.detailExceptionInfo.server=t.data.detailExceptionInfo.ipAddress+":"+t.data.detailExceptionInfo.port,e.detailExceptionInfo.exceptionName=t.data.detailExceptionInfo.exceptionName,e.detailExceptionInfo.contentStr=t.data.detailExceptionInfo.contentStr}))},showChart:function(){this.showChartVisible=!0,""!==this.startDate&&""!==this.endDate&&new Date(this.startDate).getTime()>new Date(this.endDate).getTime()?this.$message.error("开始日期不能大于结束日期"):this.queryData={startDate:this.startDate,endDate:this.endDate,serverAddress:this.serverAddress}},closeChart:function(){this.showChartVisible=!1},getContainer:function(){return document.getElementById("systemExceptionLog")}}},x=g,v=(0,p.Z)(x,o,n,!1,null,"a55c27b4",null),b=v.exports}}]);
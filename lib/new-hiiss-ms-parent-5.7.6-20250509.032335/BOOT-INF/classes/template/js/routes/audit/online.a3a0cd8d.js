"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[5629],{63352:function(t,n,e){e.r(n),e.d(n,{default:function(){return C}});var a=function(){var t=this,n=t.$createElement,e=t._self._c||n;return e("div",{staticClass:"fit",attrs:{id:"online"}},[e("ta-border-layout",{attrs:{layout:{header:"70px",footer:"70px"},centerCfg:{showBar:!0,toolBarStyle:{height:"50px",lineHeight:"48px"}},showBorder:!1,"footer-cfg":{showBorder:!1}}},[e("div",{staticClass:"center-box",attrs:{slot:"header"},slot:"header"},[e("ta-input-search",{staticClass:"search-box",attrs:{placeholder:"登录人员搜索",enterButton:"搜索"},on:{search:t.searchQuery},model:{value:t.param,callback:function(n){t.param=n},expression:"param"}})],1),e("div",{attrs:{slot:"centerExtraContent"},slot:"centerExtraContent"},[e("ta-range-picker",{attrs:{placeholder:["开始时间","结束时间"],"get-calendar-container":t.getContainer},on:{change:t.onChange},model:{value:t.createTime,callback:function(n){t.createTime=n},expression:"createTime"}}),e("div",{staticStyle:{float:"right"}},[e("ta-button",{attrs:{icon:"area-chart"},on:{click:function(n){return t.showOnlineChart()}}}),e("ta-button",{on:{click:function(n){return t.exportData()}}},[t._v("导出")])],1)],1),e("ta-table",{attrs:{columns:t.onlineColumns,dataSource:t.onlineGridData,pagination:!1}}),e("div",{attrs:{slot:"footer"},slot:"footer"},[e("ta-pagination",{ref:"onlineGridPager",staticStyle:{float:"right","margin-top":"10px"},attrs:{showSizeChanger:"",showQuickJumper:"",dataSource:t.onlineGridData,params:t.onlinePageParams,url:"logmg/loginLog/loginLogAnalysisRestService/online"},on:{"update:dataSource":function(n){t.onlineGridData=n},"update:data-source":function(n){t.onlineGridData=n}}})],1)],1),e("ta-modal",{attrs:{centered:"",width:"1000px",bodyStyle:{height:"600px",padding:"0"},footer:null,destroyOnClose:!0,maskClosable:!0,title:"数据分析",closable:!0},on:{close:function(n){return t.closeOnlineChart(!1)}},model:{value:t.showChartVisible,callback:function(n){t.showChartVisible=n},expression:"showChartVisible"}},[e("online-chart",{ref:"onlineChart",on:{closeModal:t.closeOnlineChart}})],1)],1)},i=[],o=e(35121),l=e(55437),r=e(36797),s=e.n(r),c=function(){var t=this,n=t.$createElement,e=t._self._c||n;return e("div",[e("ta-e-charts",{attrs:{height:"255px",chartsType:"line",data:t.chartData,legend:t.chartSetting.legend,tooltip:t.chartSetting.tooltip,xAxis:t.chartSetting.xAxis,yAxis:t.chartSetting.yAxis,grid:t.chartSetting.grid,toolbox:t.chartSetting.toolbox}})],1)},u=[],m=e(99440),d={name:"onlineChart",components:{TaECharts:m.Z},data:function(){return{onlineDate:null,onlineStartTime:null,onlineEndTime:null,onlineStartOpen:!1,onlineEndOpen:!1,isClickOnlineTab:!1,onlineChartData:[],onlineGridData:[],onlineXInterval:4,onlineXdata:[],onlineSeriesData:[],onlineLine:{},chartSetting:{legend:{top:10,formatter:"时点在线人数"},tooltip:{formatter:function(t){return t.seriesName+"<br/>"+t.data[0]+" ("+t.data[1]+"人)"}},yAxis:{axisLabel:{formatter:"{value}人"}},grid:{bottom:10},toolbox:{feature:{magicType:{type:["line","bar"]},saveAsImage:{}}}},chartData:{columns:["日期","时点在线人数"],rows:[]}}},mounted:function(){this.loadData()},methods:{moment:s(),onlinePageParams:function(){return{}},loadData:function(){var t=this,n={};o.Z.getSysTime(n,(function(n){t.onlineDate=s()(n.data.sysdate,"YYYY-MM-DD"),t.onlineStartTime=s()(n.data.sysStartTime,"HH:mm"),t.onlineEndTime=s()(n.data.sysEndTime,"HH:mm");var e=t.onlineDate?t.onlineDate.format("YYYY-MM-DD"):"",a=t.onlineStartTime?t.onlineStartTime.format("HH:mm"):"",i=t.onlineEndTime?t.onlineEndTime.format("HH:mm"):"",l={searchDate:e,startTime:a,endTime:i};o.Z.analysisOnlineStatInfo(l,(function(n){t.onlineChartData=n.data.onlineChartData,t.onlineXdata=o.Z.getStatLogInfoXdata(t,"online"),t.onlineSeriesData=o.Z.sortStatLogDataByHours(t,"online");var e=[];t.onlineXdata.map((function(n,a){e.push({"日期":n,"时点在线人数":t.onlineSeriesData[a]})})),t.chartData.rows=e}))}))},getOnlineInfo:function(){var t=this;if(this.onlineDate)if(this.onlineStartTime)if(this.onlineEndTime)if(new Date(this.onlineDate.format("YYYY-MM-DD")+" "+this.onlineStartTime.format("HH:mm")).getTime()>new Date(this.onlineDate.format("YYYY-MM-DD")+" "+this.onlineEndTime.format("HH:mm")).getTime())this.$message.warning("开始时间不能大于结束时间");else{var n=this.onlineDate.format("YYYY-MM-DD"),e=this.onlineStartTime.format("HH:mm"),a=this.onlineEndTime.format("HH:mm"),i={searchDate:n,startTime:e,endTime:a};o.Z.analysisOnlineStatInfo(i,(function(n){t.onlineChartData=n.data.onlineChartData,t.onlineXdata=o.Z.getStatLogInfoXdata(t,"online"),t.onlineSeriesData=o.Z.sortStatLogDataByHours(t,"online");var e=[];t.onlineXdata.map((function(n,a){e.push({"日期":n,"时点在线人数":t.onlineSeriesData[a]})})),t.chartData.rows=e})),this.$refs.onlineGridPager.loadData()}else this.$message.warning("结束时间不能为空");else this.$message.warning("开始时间不能为空");else this.$message.warning("开始日期不能为空")},handleOnlineClose:function(){this.onlineStartOpen=!1,this.onlineEndOpen=!1},onlineReload:function(){this.onlineDate=null,this.onlineStartTime=null,this.onlineEndTime=null}}},h=d,f=e(1001),g=(0,f.Z)(h,c,u,!1,null,null,null),p=g.exports,D=[{title:"登录ID",dataIndex:"loginId",key:"loginId",width:244,align:"center"},{title:"姓名",dataIndex:"name",key:"name",align:"center",width:244},{title:"登录时间",dataIndex:"loginTime",key:"loginTime",scopedSlots:{customRender:"onlineLoginTime"},align:"center",width:244},{title:"客户端ip",dataIndex:"clientIp",key:"clientIp",align:"center",width:244},{title:"客户端系统",dataIndex:"clientSystem",key:"clientSystem",align:"center",width:244},{title:"客户端浏览器",key:"clientBrowser",dataIndex:"clientBrowser",align:"center",width:244},{title:"客户端分辨率",key:"clientScreenSize",dataIndex:"clientScreenSize",align:"center",width:244},{title:"数据是否被篡改",key:"isTampered",dataIndex:"isTampered",align:"center",width:244}],S={name:"online",components:{onlineChart:p},data:function(){return{onlineDate:null,onlineStartTime:null,onlineEndTime:null,createTime:[],onlineColumns:D,onlineGridData:[],param:"",startDate:"",endDate:"",showChartVisible:!1}},mounted:function(){this.loadData()},methods:{moment:s(),onlinePageParams:function(){var t={};return t.startDate=this.startDate,t.endDate=this.endDate,t.name=this.param,t},searchQuery:function(){this.$refs.onlineGridPager.loadData()},loadData:function(){this.$refs.onlineGridPager.loadData()},onChange:function(t,n){n&&n.length&&""!=n[0]&&""!=n[1]?(this.startDate=s()(n[0]).format("YYYY-MM-DD")+" 00:00:00",this.endDate=s()(n[1]).format("YYYY-MM-DD")+" 23:59:59"):(this.startDate="",this.endDate=""),this.loadData()},closeOnlineChart:function(t){this.showChartVisible=!1},showOnlineChart:function(){this.showChartVisible=!0},exportData:function(){var t={name:this.param,startDate:this.startDate,endDate:this.endDate};l.Z.exportExcel(t,"audit/auditExportRestService/online")},getContainer:function(){return document.getElementById("online")}}},b=S,y=(0,f.Z)(b,a,i,!1,null,null,null),C=y.exports},55437:function(t,n){n["Z"]={exportExcel:function(t,n){Base.submit(null,{url:n,data:t,responseType:"blob"}).then((function(t){var n=new Blob([t.data],{type:"application/xlsx;charset=utf-8"}),e=new FileReader;e.readAsText(n,"utf-8"),e.onload=function(){try{var a=JSON.parse(e.result);null!=a.errors&&parent.window.message.error("下载失败!")}catch(l){if(window.navigator.msSaveBlob)window.navigator.msSaveBlob(n,unescape(t.headers.filename));else{var i=document.createElement("a"),o=window.URL.createObjectURL(n);i.href=o,i.download=unescape(t.headers.filename),document.body.appendChild(i),i.click(),document.body.removeChild(i),window.URL.revokeObjectURL(o)}}}}))}}},35121:function(t,n){var e="/logmg/loginLog/loginLogAnalysisRestService/";n["Z"]={getSysTime:function(t,n){Base.submit(null,{url:e+"getSysTime",data:t},{successCallback:function(t){return n(t)}})},analysisOnlineStatInfo:function(t,n){Base.submit(null,{url:e+"analysisOnlineStatInfo",data:t},{successCallback:function(t){return n(t)}})},getOnlineInfo:function(t,n){Base.submit(null,{url:e+"getOnlineInfo",data:t},{successCallback:function(t){return n(t)}})},analysisLoginStatInfo:function(t,n){Base.submit(null,{url:e+"analysisLoginStatInfo",data:t},{successCallback:function(t){return n(t)}})},getLoginInfo:function(t,n){Base.submit(null,{url:e+"getLoginInfo",data:t},{successCallback:function(t){return n(t)}})},getStatLogInfoXdata:function(t,n){var e=[],a=[];"online"===n?(e=t.onlineStartTime.format("HH:mm").split(":"),a=t.onlineEndTime.format("HH:mm").split(":")):"login"===n&&(e=t.loginStartTime.format("HH:mm").split(":"),a=t.loginEndTime.format("HH:mm").split(":"));var i=parseInt(e[0]),o=parseInt(e[1]),l=parseInt(a[0]),r=parseInt(a[1]),s=[];do{if(s.push(c(i,o)),i==l&&o==r)break;if(o<59&&o>=0)o++;else{if(59!=o){t.$message.error("在线时点分析图表构造出错！");break}i++,o=0}}while(1);function c(t,n){var e=""+t,a=""+n;return e.length<2&&(e="0"+e),a.length<2&&(a="0"+a),e+":"+a}return s.length<=60?"online"===n?t.onlineXInterval=4:"login"===n&&(t.loginXInterval=4):s.length>60&&("online"===n?t.onlineXInterval=parseInt(s.length/10-1):"login"===n&&(t.loginXInterval=parseInt(s.length/10-1))),s},sortStatLogDataByHours:function(t,n){var e=[],a=[],i=[];"online"===n?(e=t.onlineChartData,a=t.onlineStartTime.format("HH:mm").split(":"),i=t.onlineEndTime.format("HH:mm").split(":")):"login"===n&&(e=t.loginChartData,a=t.loginStartTime.format("HH:mm").split(":"),i=t.loginEndTime.format("HH:mm").split(":"));var o=parseInt(a[0]),l=parseInt(a[1]),r=parseInt(i[0]),s=parseInt(i[1]),c=[],u=0;do{var m=""+o,d=""+l;m.length<2&&(m="0"+m),d.length<2&&(d="0"+d);var h=m+":"+d;for(var f in e)h==e[f].type&&c.push(e[f].count);if(u++,c.length<u&&c.push(0),o===r&&l===s)break;if(l<59&&l>=0)l++;else{if(59!=l){t.$message.error("时点分析图表构造出错！");break}o++,l=0}}while(1);return c},analysisClientSystemInfo:function(t,n){Base.submit(null,{url:e+"analysisClientSystemInfo",data:t},{successCallback:function(t){return n(t)}})},analysisClientScreenInfo:function(t,n){Base.submit(null,{url:e+"analysisClientScreenInfo",data:t},{successCallback:function(t){return n(t)}})},analysisClientBrowserInfo:function(t,n){Base.submit(null,{url:e+"analysisClientBrowserInfo",data:t},{successCallback:function(t){return n(t)}})},queryLoginEnvironmentDetail:function(t,n){Base.submit(null,{url:e+"queryLoginEnvironmentDetail",data:t},{successCallback:function(t){return n(t)}})}}}}]);
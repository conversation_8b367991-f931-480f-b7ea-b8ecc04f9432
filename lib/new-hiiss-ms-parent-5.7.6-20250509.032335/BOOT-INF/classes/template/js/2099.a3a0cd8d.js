(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[2099],{97254:function(t,e,a){"use strict";a.r(e),a.d(e,{default:function(){return q}});var i=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("div",{staticClass:"fit"},[i("ta-border-layout",{staticStyle:{},attrs:{"layout-type":"fixTop"}},[i("div",{staticStyle:{"padding-top":"10px"},attrs:{slot:"header"},slot:"header"},[i("div",{staticClass:"query-header"},[i("div",{ref:"formBox",staticClass:"query-header-left",style:e.formBoxStyle},[i("ta-form",{attrs:{"auto-form-create":function(e){return t.form=e},layout:"horizontal","form-layout":!0,"label-width":"80px",col:{xs:1,sm:2,md:2,lg:4,xl:4,xxl:4}}},[i("ta-form-item",{attrs:{label:"清算期号","field-decorator-id":"issueNo","init-value":e.moment().format("YYYYMM"),require:!0}},[i("ta-month-picker",{staticStyle:{width:"100%"},attrs:{format:"YYYYMM","value-format":"YYYYMM","allow-clear":!1},on:{change:function(t){return e.fnGetDeptList(t,"1")}}})],1),i("ta-form-item",{attrs:{label:"参保地","field-decorator-id":"medinsuType","init-value":"1",require:!0}},[i("ta-select",{attrs:{placeholder:"参保地筛选","collection-type":"AAE141"},on:{change:e.medinsuTypeChange}})],1),i("ta-form-item",{attrs:{label:"疑点类型","field-decorator-id":"dataType","init-value":"1",require:!0}},[i("ta-select",{attrs:{placeholder:"疑点类型筛选"},on:{change:e.dataTypeChange}},[i("ta-select-option",{attrs:{value:"1"}},[e._v(" 初审疑点 ")]),i("ta-select-option",{attrs:{value:"2"}},[e._v(" 复审疑点 ")]),i("ta-select-option",{attrs:{value:"3"}},[e._v(" 病例申诉 ")]),i("ta-select-option",{attrs:{value:"4"}},[e._v(" 外部疑点 ")])],1)],1),i("ta-form-item",{attrs:{label:"医疗类别","field-decorator-id":"medTypeList"}},[i("ta-select",{attrs:{mode:"multiple",placeholder:"医疗类别筛选",options:e.aka130List,"show-search":!0,"allow-clear":!0}})],1),i("ta-form-item",{attrs:{label:"险种类型","field-decorator-id":"aae140List"}},[i("ta-select",{attrs:{mode:"multiple",placeholder:"险种类型筛选",maxTagCount:1,"collection-type":"AAE140","show-search":!0,"allow-clear":!0}})],1),i("ta-form-item",{attrs:{label:"收费类别","field-decorator-id":"costType"}},[i("ta-select",{attrs:{placeholder:"收费类别筛选",options:e.costTypeList,"show-search":!0,"allow-clear":!0}})],1),i("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:"1"===this.medinsuTypeValue||"3"===this.medinsuTypeValue,expression:"this.medinsuTypeValue === '1' || this.medinsuTypeValue === '3'"}],attrs:{label:"疑点匹配","field-decorator-id":"matchStas"}},[i("ta-select",{attrs:{placeholder:"疑点匹配筛选","allow-clear":""}},[i("ta-select-option",{attrs:{value:"0"}},[e._v(" 未匹配 ")]),i("ta-select-option",{attrs:{value:"1"}},[e._v(" 已匹配 ")])],1)],1),i("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:"1"===this.medinsuTypeValue||"3"===this.medinsuTypeValue,expression:"this.medinsuTypeValue === '1' || this.medinsuTypeValue === '3'"}],attrs:{label:"任务状态","field-decorator-id":"handStas"}},[i("ta-select",{attrs:{placeholder:"申诉任务状态筛选","allow-clear":!0,"collection-type":"APPEALHANDSTAS"}})],1),i("ta-form-item",{attrs:{label:"开单科室","field-decorator-id":"departmentName"}},[i("ta-select",{attrs:{placeholder:"开单科室筛选",options:e.deptList,"show-search":!0,"allow-clear":!0}})],1),i("ta-form-item",{attrs:{label:"出院科室","field-decorator-id":"dscgDepartmentName"}},[i("ta-select",{attrs:{placeholder:"出院科室筛选",options:e.deptListout,"show-search":!0,"allow-clear":!0}})],1),i("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:!e.fnGenerateButton&&"11"!==this.processConfig&&"3"!==this.medinsuTypeValue,expression:"!fnGenerateButton && this.processConfig !== '11' && this.medinsuTypeValue !== '3'"}],attrs:{label:"申诉材料","field-decorator-id":"fileStas"}},[i("ta-select",{attrs:{placeholder:"申诉材料文件状态筛选","allow-clear":!0}},[i("ta-select-option",{attrs:{value:"0"}},[e._v(" 有 ")]),i("ta-select-option",{attrs:{value:"1"}},[e._v(" 无 ")])],1)],1),i("ta-form-item",{attrs:{"field-decorator-id":"akb020",label:"院区标识"}},[i("ta-select",{attrs:{showSearch:"",placeholder:"院区标识筛选",allowClear:"",options:e.hosList}})],1),i("ta-form-item",{attrs:{label:"开单医生","field-decorator-id":"aaz570"}},[i("ta-suggest",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入开单医生","option-config":{value:"value",label:"label"},"data-source":e.doctorList,"table-title-map":new Map([["label",{name:"开单医生",style:{minWidth:"120px"}}]])},on:{select:function(t,a){return e.doctorChange(t,a)},search:e.doctorSearch}})],1),i("ta-form-item",{attrs:{label:"住院门诊号","field-decorator-id":"akc190"}},[i("ta-suggest",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入住院门诊号","option-config":{value:"value",label:"value"},"data-source":e.admissionNumList,"table-title-map":new Map([["label",{name:"姓名",style:{minWidth:"90px"}}],["value",{name:"住院门诊号",style:{minWidth:"180px"}}]])},on:{select:function(t,a){return e.admissionNumChange(t,a)},search:e.admissionNumSearch}})],1),i("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:"1"===this.medinsuTypeValue||"3"===this.medinsuTypeValue,expression:"this.medinsuTypeValue === '1' || this.medinsuTypeValue === '3'"}],attrs:{label:"是否申诉","field-decorator-id":"appealFlag"}},[i("ta-select",{attrs:{placeholder:"是否申诉筛选","allow-clear":""}},[i("ta-select-option",{attrs:{value:"0"}},[e._v(" 不申诉 ")]),i("ta-select-option",{attrs:{value:"1"}},[e._v(" 申诉 ")])],1)],1)],1)],1),i("div",{staticStyle:{display:"flex","margin-left":"10px",float:"right"}},[i("div",{staticClass:"ctrl-btn",on:{click:e.formShowAllChange}},[i("a",[e._v(e._s(e.formShowAll?"收起":"展开"))]),e.formShowAll?i("ta-icon",{attrs:{type:"caret-up"}}):i("ta-icon",{attrs:{type:"caret-down"}})],1),i("ta-button",{attrs:{type:"primary",icon:"search"},on:{click:e.queryTableData}},[e._v(" 查询 ")]),i("ta-button",{attrs:{type:"default",icon:"redo"},on:{click:e.resetForm}},[e._v(" 重置 ")])],1)])]),i("div",{staticClass:"fit"},[i("div",{staticClass:"part-top"},[i("ta-title",{staticStyle:{float:"left"},attrs:{title:"导入结果"}}),i("div",{staticClass:"part-operate"},[i("ta-button",{directives:[{name:"show",rawName:"v-show",value:!e.fnGenerateButton&&"11"!==this.processConfig&&"3"!==this.medinsuTypeValue,expression:"!fnGenerateButton && this.processConfig !== '11' && this.medinsuTypeValue !== '3'"}],on:{click:e.fnGenerateComplaintMaterials}},[e._v(" "+e._s(this.processConfig.endsWith("0")?"生成申诉材料":"生成申诉理由")+" ")]),i("ta-button",{directives:[{name:"show",rawName:"v-show",value:!e.appealDisabled,expression:"!appealDisabled"}],attrs:{type:"primary"},on:{click:e.analysisMedicalInsuranceRefusal}},[e._v(" 申诉任务下发 ")]),i("ta-button",{directives:[{name:"show",rawName:"v-show",value:!e.appealDisabled,expression:"!appealDisabled"}],attrs:{type:"primary"},on:{click:e.batchCompletion}},[e._v(" 批量完成 ")]),[i("ta-button",{directives:[{name:"show",rawName:"v-show",value:!e.fnGenerateButton,expression:"!fnGenerateButton"}],attrs:{type:"primary"},on:{click:function(t){return e.exportAppealMaterials()}}},[e._v(" 导出 ")])]],2)],1),"2"!=this.medinsuTypeValue&&"4"!=this.medinsuTypeValue?i("div",{staticStyle:{"margin-left":"40px"}},[i("span",[i("p",[e._v(" 中心下发疑点数据共："),i("span",{staticClass:"highlight"},[e._v(e._s(e.summaryData.totalNum||0))]),e._v(" 条。 "),"3"!=e.medinsuTypeValue?[e._v(" 已在审核系统中匹配到："),i("span",{staticClass:"highlight"},[e._v(e._s(e.summaryData.matchSuccessNum||0))]),e._v(" 条， 未匹配到："),i("span",{staticClass:"highlight"},[e._v(e._s(e.summaryData.matchFailNum||0))]),e._v(" 条。 "),"01"!=e.processConfig&&"11"!=e.processConfig?[e._v(" 材料生成状态汇总： 未生成："),i("span",{staticClass:"highlight"},[e._v(e._s(e.summaryData.fileUnCreateNum||0))]),e._v(" 条， 生成中："),i("span",{staticClass:"highlight"},[e._v(e._s(e.summaryData.fileCreatingNum||0))]),e._v(" 条， 生成成功："),i("span",{staticClass:"highlight"},[e._v(e._s(e.summaryData.fileCreateSuccessNum||0))]),e._v(" 条， 生成失败："),i("span",{staticClass:"highlight"},[e._v(e._s(e.summaryData.fileCreateFailNum||0))]),e._v(" 条。 ")]:e._e()]:e._e()],2),i("p",{staticClass:"warning"},[e._v(" 未匹配到的疑点数据系统无法自动下发任务，请手动匹配费用或任务下发时手动选择申诉处理人员 ")])])]):e._e(),i("div",{directives:[{name:"show",rawName:"v-show",value:"1"===this.medinsuTypeValue||"3"===this.medinsuTypeValue,expression:"this.medinsuTypeValue === '1' || this.medinsuTypeValue === '3'"}],staticStyle:{height:"calc(91% - 58px)","margin-top":"10px"}},[i("ta-big-table",{ref:"1"===this.medinsuTypeValue||"3"===this.medinsuTypeValue?"xTable":"xTableCopy",staticStyle:{width:"100%"},attrs:{border:"","highlight-hover-row":"","show-overflow":"","tooltip-config":{theme:"light",contentMethod:e.tooltipMethod},"checkbox-config":{trigger:"row",checkMethod:function(t){return"7"!==t.row.handStas&&"11"!==t.row.handStas}},"control-column":e.showHiddenOrSortColumn,"sort-config":{trigger:"cell"},data:e.dataSource,height:"100%",resizable:!0,"auto-resize":"",size:"mini","empty-text":"-","head-align":"center",align:"center",width:"min-width"},scopedSlots:e._u([{key:"bottomBar",fn:function(){return[i("ta-pagination",{ref:"gridPager",staticStyle:{"text-align":"right"},attrs:{size:"small","default-page-size":200,"page-size-options":["30","50","100","200","500"],"data-source":e.dataSource,url:e.pageUrl,params:e.pageParams},on:{"update:dataSource":function(t){e.dataSource=t},"update:data-source":function(t){e.dataSource=t}}})]},proxy:!0}])},[i("ta-big-table-column",{attrs:{type:"checkbox",fixed:"left",width:"40px",align:"center"}}),i("ta-big-table-column",{attrs:{type:"seq",fixed:"left",width:"50px",title:"序号",align:"center"}}),i("ta-big-table-column",{attrs:{field:"handStas","show-overflow":"",title:"申诉任务状态",fixed:"left","min-width":"140px",sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;t.column;return[i("div",{staticStyle:{position:"relative"}},[a.handStas?[e.CollectionLabel("SEX",a.handStas)?i("div",[i("ta-badge",{attrs:{color:e.statusColors[a.handStas]||"#000000"}}),i("span",{style:{color:e.statusColors[a.handStas]||"#000000"}},[e._v(e._s(e.CollectionLabel("APPEALHANDSTAS",a.handStas)))])],1):i("div",[i("ta-badge",{attrs:{color:"#000000"}}),i("span",{staticStyle:{color:"#000000"}},[e._v("未知状态")])],1)]:[i("ta-badge",{attrs:{color:"#000000"}}),i("span",{staticStyle:{color:"#000000"}},[e._v("未知状态")])]],2)]}}])}),i("ta-big-table-column",{attrs:{fixed:"left",field:"akc195","show-overflow":"",title:"申诉截止日期","min-width":"150px",align:"center",sortable:""}}),i("ta-big-table-column",{attrs:{fixed:"left",align:"center",field:"appealFlag",title:"是否申诉","min-width":"100px",sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;t.column;return[e._v(" "+e._s("1"===a.appealFlag?"申诉":"不申诉")+" ")]}}])}),i("ta-big-table-column",{attrs:{fixed:"left",align:"center",field:"appealMaterial",title:"申诉材料","min-width":"100px"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("div",{staticStyle:{position:"relative"}},[a.fileNum>0?i("div",{staticStyle:{color:"#1b65b9"}},[i("ta-tooltip",{attrs:{placement:"top"}},[i("template",{slot:"title"},[i("span",[e._v(e._s(a.fileNames))])]),i("span",{staticStyle:{cursor:"pointer"}},[e._v("附件("+e._s(a.fileNum)+")")])],2)],1):i("div",[e._v(" 无 ")])])]}}])}),i("ta-big-table-column",{attrs:{align:"center","show-overflow":"",title:"住院门诊号","min-width":"120px",field:"akc191","sort-by":"akc191",sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;t.column;return[i("div",{staticStyle:{position:"relative"}},[a.akc191?i("div",[e._v(" "+e._s(a.akc191)+" ")]):a.akc190?i("div",[e._v(" "+e._s(a.akc190)+" ")]):i("div",[e._v(" — ")])])]}}])}),i("ta-big-table-column",{attrs:{sortable:"",field:"akb021","show-overflow":"",title:"医院名称","min-width":"110px"}}),i("ta-big-table-column",{attrs:{field:"aac003",align:"center","show-overflow":"",title:"参保人","min-width":"100px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"ake002","show-overflow":"",title:"医保项目名称","min-width":"140px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"ykz018",align:"center","show-overflow":"",title:"违规内容","min-width":"120px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"ape804",align:"center","show-overflow":"",title:"初审违规金额(元)","min-width":"150px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"aac004",sortable:"","show-overflow":"",title:"开单科室","min-width":"100px"}}),i("ta-big-table-column",{attrs:{field:"aaz570",sortable:"","show-overflow":"",title:"开单医生","min-width":"100"}}),i("ta-big-table-column",{attrs:{field:"aae386","show-overflow":"",title:"出院科室","min-width":"100px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"aae030","show-overflow":"",title:"入院日期","min-width":"150px",align:"center",sortable:""}}),i("ta-big-table-column",{attrs:{field:"aae031","show-overflow":"",title:"出院日期","min-width":"150px",align:"center",sortable:""}}),i("ta-big-table-column",{attrs:{field:"ake001","show-overflow":"",title:"医保项目编码","min-width":"140px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"aaa167",align:"center","show-overflow":"",title:"规则名称","min-width":"100px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"ykc610",align:"center","show-overflow":"",title:"费用明细ID","min-width":"140px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"aae036","show-overflow":"",title:"费用明细时间","min-width":"150px",align:"center",sortable:""}}),i("ta-big-table-column",{attrs:{field:"currPersName",align:"center","show-overflow":"",title:"申诉处理人","min-width":"120px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"fileStas","show-overflow":"",title:"材料生成进度","min-width":"130px",filters:[{data:""}],"filter-method":e.filterMethod},scopedSlots:e._u([{key:"filter",fn:function(t){var a=t.$panel,l=t.column;return e._l(l.filters,(function(t,l){return i("ta-select",{key:l,staticStyle:{margin:"10px",width:"120px"},attrs:{"get-popup-container":e.setPopupContainer,options:[{value:"0",label:"生成失败"},{value:"1",label:"生成成功"},{value:"-1",label:"未生成"},{value:"2",label:"生成中"}]},on:{change:function(e){return a.changeOption(e,!!t.data,t)}},model:{value:t.data,callback:function(a){e.$set(t,"data",a)},expression:"option.data"}})}))}},{key:"default",fn:function(t){var a=t.row,l=t.column;return[i("div",{staticStyle:{position:"relative"}},["0"===a.fileStas?i("div",{staticStyle:{color:"red"},on:{mouseover:function(t){return e.showFileReason(a,l)},mouseout:function(t){return e.hideFileReason(a,l)}}},[e._v(" 生成失败 ")]):e._e(),"1"===a.fileStas?i("div",{staticStyle:{color:"#333333"}},[e._v(" 生成成功 ")]):e._e(),"-1"===a.fileStas?i("div",{staticStyle:{color:"#333333"}},[e._v(" 未生成 ")]):e._e(),"2"===a.fileStas?i("div",{staticStyle:{color:"#333333"}},[e._v(" 生成中 ")]):e._e(),i("div",{staticStyle:{position:"absolute",left:"-10px",bottom:"30px","background-color":"#fff","max-width":"250px","min-width":"100px",height:"auto"},attrs:{visible:a.falseReasonShow}},[e._v(" "+e._s(a.fileReason)+" ")])])]}}])}),i("ta-big-table-column",{attrs:{field:"aaz560",title:"申诉理由","min-width":"180px"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;t.column;return[i("ta-tooltip",[i("template",{slot:"title"},[e._v(" "+e._s(a.aaz560)+" ")]),i("div",{staticClass:"wrapShowDiv"},[e._v(" "+e._s(a.aaz560)+" ")])],2)]}}])}),i("ta-big-table-column",{attrs:{field:"dataType","show-overflow":"",title:"数据类型","min-width":"100","sort-by":"dataType",sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;t.column;return[i("div",{staticStyle:{position:"relative"}},["1"===a.dataType?i("div",[e._v(" 初审扣款 ")]):"2"===a.dataType?i("div",[e._v(" 复审扣款 ")]):"3"===a.dataType?i("div",[e._v(" 病例申诉 ")]):i("div",[e._v(" 外部疑点 ")])])]}}])}),i("ta-big-table-column",{attrs:{field:"aac001","show-overflow":"",title:"个人编码","min-width":"100",sortable:""}}),i("ta-big-table-column",{attrs:{field:"akc190V","show-overflow":"",title:"匹配住院门诊号","min-width":"140px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"ape804V","show-overflow":"",title:"匹配金额","min-width":"140px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"ake006V","show-overflow":"",title:"匹配院内项目名称","min-width":"160px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"aac004V","show-overflow":"",title:"匹配院内开单科室","min-width":"160px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"aaz570V","show-overflow":"",title:"匹配院内开单医生","min-width":"160px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"sdksmcV","show-overflow":"",title:"匹配院内受单科室","min-width":"160px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"aae386V","show-overflow":"",title:"匹配院内出院科室","min-width":"160px",sortable:""}}),i("ta-big-table-column",{attrs:{"show-overflow":"",field:"view",title:"匹配预警记录",width:"130px",align:"center","sort-by":"matchStas",sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return["1"===a.matchStas?i("div",[i("div",{staticClass:"opareteItem",on:{click:function(t){return e.viewRecords(a)}}},[e._v(" 查看 ")])]):i("div",[i("span",[e._v("无")])])]}}])}),i("ta-big-table-column",{attrs:{field:"aka121","show-overflow":"",title:"诊断名称","min-width":"100",sortable:""}}),i("ta-big-table-column",{attrs:{field:"matchStas",fixed:"right","show-overflow":"",title:"疑点匹配",align:"center","min-width":"100px",sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;t.column;return[i("div",{staticStyle:{position:"relative"}},["0"===a.matchStas?i("div",[i("ta-badge",{attrs:{color:"#f49924"}}),i("span",{staticStyle:{color:"#f49924"}},[e._v("未匹配")])],1):"1"===a.matchStas?i("div",[e._v(" 已匹配 ")]):i("div")])]}}])}),i("ta-big-table-column",{attrs:{fixed:"right","show-overflow":"",field:"operate",title:"操作",width:"180",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return["1"===a.dataType?i("div",["1"!==a.dataType||"0"!==a.matchStas||a.currPersName?"1"!==a.dataType||"1"!==a.matchStas||a.currPersName?i("div",["1"===a.dataType&&"7"!==a.handStas&&"11"!==a.handStas?i("div",{staticClass:"opareteItem",on:{click:function(t){return e.handCompleted(a)}}},[e._v(" 已完成 ")]):i("div",[e._v(" — — ")])]):i("div",{staticClass:"opareteItem"},[i("span",{on:{click:function(t){return e.handMovement(a)}}},[e._v("重新匹配")]),"1"===a.dataType&&"7"!==a.handStas&&"11"!==a.handStas?i("div",{staticClass:"opareteItem",on:{click:function(t){return e.handCompleted(a)}}},[i("ta-divider",{attrs:{type:"vertical"}}),e._v(" 已完成 ")],1):e._e()]):i("div",{staticClass:"opareteItem"},[i("span",{on:{click:function(t){return e.handMovement(a)}}},[e._v("手动匹配")]),"1"===a.dataType&&"7"!==a.handStas&&"11"!==a.handStas?i("div",{staticClass:"opareteItem",on:{click:function(t){return e.handCompleted(a)}}},[i("ta-divider",{attrs:{type:"vertical"}}),e._v(" 已完成 ")],1):e._e()])]):i("div",["3"===a.dataType&&"7"!==a.handStas&&"11"!==a.handStas?i("div",{staticClass:"opareteItem",on:{click:function(t){return e.handCompleted(a)}}},[e._v(" 已完成 ")]):i("div",[e._v(" — — ")])])]}}])})],1)],1),i("div",{directives:[{name:"show",rawName:"v-show",value:"2"==this.medinsuTypeValue,expression:"this.medinsuTypeValue == '2'"}],staticStyle:{height:"calc(98% - 54px)","margin-top":"10px"}},[i("ta-big-table",{ref:"2"===this.medinsuTypeValue?"xTable":"xTableCopy",staticStyle:{width:"100%"},attrs:{border:"","show-overflow":"","highlight-hover-row":"","tooltip-config":{theme:"light",contentMethod:e.tooltipMethod},"control-column":e.showHiddenOrSortColumn,"sort-config":{trigger:"cell"},"checkbox-config":{trigger:"row"},data:e.dataSource,height:"100%",resizable:!0,"auto-resize":"",size:"mini","empty-text":"-",width:"min-width"},scopedSlots:e._u([{key:"bottomBar",fn:function(){return[i("ta-pagination",{ref:"gridPager3",staticStyle:{"text-align":"right"},attrs:{size:"small","default-page-size":200,"page-size-options":["30","50","100","200","500"],"data-source":e.dataSource,url:e.pageUrl,params:e.pageParams},on:{"update:dataSource":function(t){e.dataSource=t},"update:data-source":function(t){e.dataSource=t}}})]},proxy:!0}])},[i("ta-big-table-column",{attrs:{type:"seq",fixed:"left",width:"50px",title:"序号",align:"center"}}),i("ta-big-table-column",{attrs:{align:"center","show-overflow":"",title:"住院门诊号","min-width":"120px",field:"akc191","sort-by":"akc191",sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;t.column;return[i("div",{staticStyle:{position:"relative"}},[a.akc191?i("div",[e._v(" "+e._s(a.akc191)+" ")]):a.akc190?i("div",[e._v(" "+e._s(a.akc190)+" ")]):i("div",[e._v(" — ")])])]}}])}),i("ta-big-table-column",{attrs:{field:"aac003",align:"center","show-overflow":"",title:"参保人","min-width":"100px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"ake002","show-overflow":"",title:"医保项目名称","min-width":"140px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"ykz018",align:"center","show-overflow":"",title:"违规内容","min-width":"120px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"ape804",align:"center","show-overflow":"",title:"初审违规金额(元)","min-width":"150px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"aac004",sortable:"","show-overflow":"",title:"开单科室","min-width":"100px"}}),i("ta-big-table-column",{attrs:{field:"aaz570",sortable:"","show-overflow":"",title:"开单医生","min-width":"100"}}),i("ta-big-table-column",{attrs:{field:"aae386","show-overflow":"",title:"出院科室","min-width":"100px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"aae030","show-overflow":"",title:"入院日期","min-width":"150px",align:"center",sortable:""}}),i("ta-big-table-column",{attrs:{field:"aae031","show-overflow":"",title:"出院日期","min-width":"150px",align:"center",sortable:""}}),i("ta-big-table-column",{attrs:{field:"ake001","show-overflow":"",title:"医保项目编码","min-width":"140px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"aaa167",align:"center","show-overflow":"",title:"规则名称","min-width":"100px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"ykc610",align:"center","show-overflow":"",title:"费用明细ID","min-width":"140px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"aae036","show-overflow":"",title:"费用明细时间","min-width":"150px",align:"center",sortable:""}}),i("ta-big-table-column",{attrs:{field:"",title:"申诉理由","min-width":"180px"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;t.column;return[i("ta-tooltip",[i("template",{slot:"title"},[e._v(" "+e._s(a.aaz560)+" ")]),i("div",{staticClass:"wrapShowDiv"},[e._v(" "+e._s(a.aaz560)+" ")])],2)]}}])}),i("ta-big-table-column",{attrs:{field:"dataType","show-overflow":"",title:"数据类型","min-width":"100","sort-by":"dataType",sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;t.column;return[i("div",{staticStyle:{position:"relative"}},["1"===a.dataType?i("div",[e._v(" 初审扣款 ")]):"2"===a.dataType?i("div",[e._v(" 复审扣款 ")]):"3"===a.dataType?i("div",[e._v(" 病例申诉 ")]):i("div",[e._v(" 外部疑点 ")])])]}}])}),i("ta-big-table-column",{attrs:{field:"aac001","show-overflow":"",title:"个人编码","min-width":"100",sortable:""}}),i("ta-big-table-column",{attrs:{field:"aaz560Re","show-overflow":"",title:"复审意见","min-width":"100",sortable:""}}),i("ta-big-table-column",{attrs:{field:"ape804Re",align:"center","show-overflow":"",title:"复审违规金额(元)","min-width":"150px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"ape804Final",align:"center","show-overflow":"",title:"终审违规金额(元)","min-width":"150px",sortable:""}})],1)],1),i("div",{directives:[{name:"show",rawName:"v-show",value:"4"===this.medinsuTypeValue,expression:"this.medinsuTypeValue === '4'"}],staticStyle:{height:"calc(98% - 54px)","margin-top":"10px"}},[i("ta-big-table",{ref:"4"===this.medinsuTypeValue?"xTable":"xTableCopy",staticStyle:{width:"100%"},attrs:{border:"","show-overflow":"","control-column":e.showHiddenOrSortColumn,"sort-config":{trigger:"cell"},data:e.dataSource,height:"100%",align:"center","header-align":"center",resizable:!0,"auto-resize":"",size:"mini","empty-text":" ",width:"min-width"},scopedSlots:e._u([{key:"bottomBar",fn:function(){return[i("ta-pagination",{ref:"gridPager4",staticStyle:{"text-align":"right"},attrs:{size:"small","default-page-size":30,"page-size-options":["30","50","100","200","500"],"data-source":e.dataSource,url:e.pageUrl,params:e.pageParams},on:{"update:dataSource":function(t){e.dataSource=t},"update:data-source":function(t){e.dataSource=t}}})]},proxy:!0}])},[i("ta-big-table-column",{attrs:{type:"seq",fixed:"left",width:"50px",title:"序号",align:"center"}}),i("ta-big-table-column",{attrs:{field:"akb020","show-overflow":"",title:"医院编码","min-width":"100px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"akb021","show-overflow":"",title:"医院名称","min-width":"110px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"akc191","show-overflow":"",title:"住院门诊号","min-width":"120px","sort-by":"akc191",sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;t.column;return[i("div",{staticStyle:{position:"relative"}},[a.akc191?i("div",[e._v(" "+e._s(a.akc191)+" ")]):a.akc190?i("div",[e._v(" "+e._s(a.akc190)+" ")]):i("div",[e._v(" — ")])])]}}])}),i("ta-big-table-column",{attrs:{field:"aac003","show-overflow":"",title:"参保人姓名","min-width":"120px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"aac001","show-overflow":"",title:"个人编码","min-width":"100px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"sex","show-overflow":"",title:"性别","min-width":"100px","collection-type":"SEX",sortable:""}}),i("ta-big-table-column",{attrs:{field:"aac017","show-overflow":"",title:"年龄","min-width":"100px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"aae030","show-overflow":"",title:"入院日期","min-width":"150px",align:"center",sortable:""}}),i("ta-big-table-column",{attrs:{field:"aae031","show-overflow":"",title:"出院日期","min-width":"150px",align:"center",sortable:""}}),i("ta-big-table-column",{attrs:{"show-overflow":"",title:"医疗类别","min-width":"120px",field:"aka130","sort-by":"aka130",sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;t.column;return[i("div",{staticStyle:{position:"relative"}},[a.aka130?i("span",e._l(a.aka130.split(",").filter((function(t){return t})),(function(t,l){return i("span",{key:l},[e._v(" "+e._s(e.CollectionLabel("AKA130",t))+" "),l<a.aka130.split(",").filter((function(t){return t})).length-1?i("span",[e._v(",")]):e._e()])})),0):e._e()])]}}])}),i("ta-big-table-column",{attrs:{field:"ake001","show-overflow":"",title:"医保项目编码","min-width":"140px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"ake002","show-overflow":"",title:"医保项目名称","min-width":"140px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"akc515","show-overflow":"",title:"医院项目编码","min-width":"140px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"ake006","show-overflow":"",title:"医院项目名称","min-width":"140px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"ake003","show-overflow":"",title:"收费类别","min-width":"110px","collection-type":"AKE003",sortable:""}}),i("ta-big-table-column",{attrs:{field:"aae036","show-overflow":"",title:"费用明细时间","min-width":"150px",align:"center",sortable:""}}),i("ta-big-table-column",{attrs:{field:"ykc610","show-overflow":"",title:"费用明细流水号","min-width":"140px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"akc194","show-overflow":"",title:"结算日期","min-width":"150px",align:"center",sortable:""}}),i("ta-big-table-column",{attrs:{field:"aac004",sortable:"","show-overflow":"",title:"开单科室","min-width":"100px"}}),i("ta-big-table-column",{attrs:{field:"sdksmc","show-overflow":"",title:"受单科室","min-width":"160px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"aae376",sortable:"","show-overflow":"",title:"入院科室","min-width":"100px"}}),i("ta-big-table-column",{attrs:{field:"aae386","show-overflow":"",title:"出院科室","min-width":"100px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"aaz570",sortable:"","show-overflow":"",title:"开单医生","min-width":"100px"}}),i("ta-big-table-column",{attrs:{field:"aae140","show-overflow":"",title:"险种类型","min-width":"110px","collection-type":"AAE140",sortable:""}}),i("ta-big-table-column",{attrs:{field:"aae141","show-overflow":"",title:"医保类型","min-width":"110px","collection-type":"AAE141",sortable:""}}),i("ta-big-table-column",{attrs:{field:"admdvs","show-overflow":"",title:"医保区划","min-width":"110px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"insuplcadmdvs","show-overflow":"",title:"参保地区划","min-width":"120px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"akc226",align:"center","show-overflow":"",title:"数量","min-width":"80px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"akc225",align:"center","show-overflow":"",title:"单价","min-width":"80px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"akb065",align:"center","show-overflow":"",title:"金额(元)","min-width":"110px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"detamt","show-overflow":"",title:"扣款金额(元)","min-width":"150px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"detrea","show-overflow":"",title:"扣款原因","min-width":"100",sortable:""}})],1)],1)]),i("ta-modal",{attrs:{title:"下载模板",visible:e.downloadVisible,height:250,width:500},on:{cancel:function(t){return e.handleClose("0")}}},[i("ta-transfer",{attrs:{"row-key":function(t){return t.columnKey},"data-source":e.columns,titles:["候选表头","模板表头"],"target-keys":e.targetKeys,"selected-keys":e.columnsChecked,render:function(t){return t.columnValue},disabled:e.disabled},on:{change:e.handleChange,selectChange:e.handleSelectChange}}),i("div",{attrs:{slot:"footer"},slot:"footer"},[i("ta-button",{on:{click:function(t){return e.handleClose("0")}}},[e._v(" 取消 ")]),i("ta-button",{staticStyle:{"margin-right":"calc(50% - 70px)"},attrs:{type:"primary"},on:{click:e.handleDownload}},[e._v(" 下载 ")])],1)],1),i("ta-modal",{attrs:{title:"疑点明细匹配",visible:e.uploadVisible3,height:505,width:1160},on:{cancel:e.handleClose3}},[i("div",{staticStyle:{height:"100%"}},[i("ta-border-layout",{attrs:{layout:{header:"150px",footer:"0px"},"show-border":!1,"header-cfg":{showBorder:!1}}},[i("div",{attrs:{slot:"header"},slot:"header"},[i("ta-title",{attrs:{title:"原始疑点信息"}}),i("ta-big-table",{attrs:{border:"","highlight-hover-row":"",resizable:!0,"auto-resize":"",size:"mini","empty-text":"-",height:"75px",data:e.doubtData}},[i("ta-big-table-column",{attrs:{field:"matchStas","show-overflow":"",title:"疑点匹配状态",align:"center","min-width":"120px"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;t.column;return[i("div",{staticStyle:{position:"relative"}},["0"===a.matchStas?i("div",[i("ta-badge",{attrs:{color:"#f49924"}}),i("span",{staticStyle:{color:"#f49924"}},[e._v("未匹配")])],1):"1"===a.matchStas?i("div",[e._v(" 已匹配 ")]):i("div")])]}}])}),i("ta-big-table-column",{attrs:{align:"center","show-overflow":"",title:"住院门诊号","min-width":"100px"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;t.column;return[i("div",{staticStyle:{position:"relative"}},[a.akc191?i("div",[e._v(" "+e._s(a.akc191)+" ")]):a.akc190?i("div",[e._v(" "+e._s(a.akc190)+" ")]):i("div")])]}}])}),i("ta-big-table-column",{attrs:{field:"aac003",align:"center","show-overflow":"",title:"参保人","min-width":"80px"}}),i("ta-big-table-column",{attrs:{field:"aac004",align:"center","show-overflow":"",title:"开单科室","min-width":"80px"}}),i("ta-big-table-column",{attrs:{field:"aaz570",align:"center","show-overflow":"",title:"开单医生","min-width":"80px"}}),i("ta-big-table-column",{attrs:{field:"ykc610",align:"center","show-overflow":"",title:"费用明细流水号","min-width":"140px"}}),i("ta-big-table-column",{attrs:{field:"ake002",align:"center","show-overflow":"",title:"医保项目名称","min-width":"120px"}}),i("ta-big-table-column",{attrs:{field:"ykz018",align:"center","show-overflow":"",title:"违规内容","min-width":"80px"}}),i("ta-big-table-column",{attrs:{field:"ape804",align:"center","show-overflow":"",title:"初审违规金额(元)","min-width":"130px"}}),i("ta-big-table-column",{attrs:{field:"aae036",align:"center","show-overflow":"",title:"费用发生时间","min-width":"160px"}})],1)],1),i("div",[i("ta-title",{attrs:{title:"符合疑点的费用明细"}}),i("ta-big-table",{ref:"costTable",attrs:{height:"280px","auto-resize":"",resizable:"","highlight-hover-row":"",border:"",size:"mini",data:e.costData}},[i("ta-big-table-column",{attrs:{type:"seq",fixed:"left",width:"50px",title:"序号",align:"center"}}),i("ta-big-table-column",{attrs:{field:"ake006",align:"center","show-overflow":"",title:"院内项目名称","min-width":"160px"}},[i("ta-big-table-column-filter",{attrs:{"filter-props":{placeholder:""}}})],1),i("ta-big-table-column",{attrs:{field:"aac004",align:"center","show-overflow":"",title:"院内开单科室","min-width":"130px"}},[i("ta-big-table-column-filter",{attrs:{"filter-props":{placeholder:""}}})],1),i("ta-big-table-column",{attrs:{field:"aaz570",align:"center","show-overflow":"",title:"院内开单医生","min-width":"130px"}},[i("ta-big-table-column-filter",{attrs:{"filter-props":{placeholder:""}}})],1),i("ta-big-table-column",{attrs:{field:"sdksmc",align:"center","show-overflow":"",title:"院内受单科室","min-width":"130px"}},[i("ta-big-table-column-filter",{attrs:{"filter-props":{placeholder:""}}})],1),i("ta-big-table-column",{attrs:{field:"aae036",sortable:"",align:"center","show-overflow":"",title:"费用发生时间","min-width":"160px"}}),i("ta-big-table-column",{attrs:{field:"akc226",align:"center","show-overflow":"",title:"数量","min-width":"80px"}},[i("ta-big-table-column-filter",{attrs:{"filter-type":"number"}})],1),i("ta-big-table-column",{attrs:{field:"ape804",align:"center","show-overflow":"",title:"金额","min-width":"80px"}},[i("ta-big-table-column-filter",{attrs:{"filter-type":"number"}})],1),i("ta-big-table-column",{attrs:{fixed:"right","show-overflow":"",field:"operate",title:"操作",width:"100",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("span",[i("ta-table-operate",{attrs:{"operate-menu":e.operateMenu,"row-info":t}})],1)]}}])}),i("template",{slot:"bottomBar"},[i("ta-pagination",{ref:"gridPager2",staticStyle:{"text-align":"right"},attrs:{"default-page-size":500,"hide-on-single-page":!0,"page-size-options":["30","50","100","200","500"],"data-source":e.costData,params:e.infoPageParams,url:"hiddscgPoint/getMtchingData"},on:{"update:dataSource":function(t){e.costData=t},"update:data-source":function(t){e.costData=t}}})],1)],2)],1)])],1),i("div",{attrs:{slot:"footer"},slot:"footer"},[i("ta-button",{staticStyle:{"margin-right":"calc(5% - 0px)"},on:{click:e.handleClose3}},[e._v(" 关闭 ")]),i("ta-button",{staticStyle:{"margin-right":"calc(40%)"},attrs:{type:"primary"},on:{click:e.handleSave}},[e._v(" 保存 ")])],1)]),i("view-record",{attrs:{visible:e.uploadVisible4,param:e.mateRow,title:"匹配预警记录"},on:{handleClose:e.handleClose4}})],1),i("distribute",{attrs:{visible:e.distributeVisible,params:e.paramsforDistrubute,"dept-list":e.deptList},on:{handleClose:e.handleClose2}})],1)},l=[],o=a(89584),s=a(48534),n=(a(36133),a(95082)),r=a(95278),u="hiddscgPoint/",c="appeal/drPoint/",d={getPageUrl:function(){return u+"queryHiddscgPoint"},getDeptList:function(t,e){Base.submit(null,{url:"miimCommonRead/getAppealQueryData",data:t,showPageLoading:!1},{successCallback:function(t){return e(t)}})},getColumns:function(t,e){Base.submit(null,{url:u+"queryTableColumn",data:t},{successCallback:function(t){return e(t)}})},getColumnsInfo:function(t,e){Base.submit(null,{url:u+"updateTableColumn",data:t},{successCallback:function(t){return e(t)}})},uploadDatas:function(t,e){Base.submit(null,{url:u+"importExcel",data:t,autoQs:!1,isFormData:!0},{successCallback:function(t){return e(t)}})},getUploadFilter:function(t,e){Base.submit(null,{url:u+"importCheck",data:t},{successCallback:function(t){return e(t)}})},handleBatchGenerate:function(t,e){Base.submit(null,{url:u+"createAppeal",data:t},{successCallback:function(t){return e(t)}})},reGenerate:function(t,e){Base.submit(null,{url:u+"generateSingleAppealAgain",data:t},{successCallback:function(t){return e(t)}})},exportZip:function(){return r.Z.basePath+"/appeal/hidAppeal/exportZipForDouble"},getDoctorList:function(t,e){Base.submit(null,{url:"appeal/common/fuzzySearchInKf38/doctorName",data:(0,n.Z)((0,n.Z)({},t),{},{igronCurrPer:!0})},{successCallback:function(t){return e(t)}})},getAdmissionNumList:function(t,e){Base.submit(null,{url:"appeal/common/fuzzySearchInKf38/patientId",data:(0,n.Z)((0,n.Z)({},t),{},{igronCurrPer:!0})},{successCallback:function(t){return e(t)}})},batchCompletion:function(t,e){Base.submit(null,{url:c+"batchCompletion",data:t,autoQs:!1},{successCallback:function(t){return e(t)}})}},f=a(36797),m=a.n(f),h=a(22722),p=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",t._l(t.tagList,(function(e){return a("ta-tag",{key:e.label,attrs:{color:e.checked?e.color2:e.color1},on:{click:function(a){return t.handleChange(e)}}},[a("span",{staticClass:"tag-icon"},[t._v(t._s(e.icon))]),a("span",{staticClass:"tag-label"},[t._v(" "+t._s(e.label)+" ")])])})),1)},b=[],g=a(7485),w={name:"tagChange",props:{tagValue:{type:String},statusArr:{type:Array,default:function(){return g.H}}},data:function(){return{tagList:this.statusArr}},watch:{tagValue:function(t){this.changeTag(t)}},mounted:function(){this.changeTag(this.tagValue)},methods:{changeTag:function(t){this.tagList=this.tagList.map((function(e){return e.value===t?e.checked=!0:e.checked=!1,e}))},handleChange:function(t){this.$emit("change",t)}}},v=w,y=a(1001),x=(0,y.Z)(v,p,b,!1,null,"14432a73",null),T=x.exports,C=a(88412);function k(){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px",fontWeight:"bold"}}}var S={columns:[{title:"住院号",dataIndex:"akc190",align:"center",width:100,overflowTooltip:!0,customHeaderCell:k},{title:"患者姓名",dataIndex:"aac003",align:"center",width:80,overflowTooltip:!0,customHeaderCell:k},{title:"项目编码",dataIndex:"ake001",align:"center",width:120,overflowTooltip:!0,customHeaderCell:k},{title:"项目名称",dataIndex:"ake006",align:"center",width:80,overflowTooltip:!0,customHeaderCell:k},{title:"违反规则",dataIndex:"aaa167",align:"center",width:200,overflowTooltip:!0,customHeaderCell:k},{title:"申诉理由",dataIndex:"aaz560",align:"center",width:120,overflowTooltip:!0,customHeaderCell:k},{title:"开单科室",dataIndex:"aae386",align:"center",width:160,sorter:function(t,e){return t.aae036>e.aae036},overflowTooltip:!0,customHeaderCell:k},{title:"开单医生",dataIndex:"aaz570",align:"center",width:90,overflowTooltip:!0,customHeaderCell:k},{title:"明细时间",dataIndex:"aae036",align:"center",width:80,overflowTooltip:!0,customHeaderCell:k},{title:"个人编码",dataIndex:"aac001",align:"center",width:150,overflowTooltip:!0,customHeaderCell:k},{title:"材料状态",dataIndex:"fileStas",align:"center",width:150,overflowTooltip:!0,customHeaderCell:k,scopedSlots:{customRender:"fileStas"}},{title:"操作",dataIndex:"ykz018",align:"center",width:100,overflowTooltip:!0,customHeaderCell:k,scopedSlots:{customRender:"operate"}}]},_=a(66670);function $(t){return t?t.akb020&&t.aaz263?"personal":t.akb020&&t.aaz309&&t.aaz307?"group":t.akb020&&t.aaz307?"department":t.akb020?"hospital":"all":"all"}function V(t){return D.apply(this,arguments)}function D(){return D=(0,s.Z)(regeneratorRuntime.mark((function t(e){var a,i,l,o,s,n,r,u,c,d,f,m;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Base.submit(null,{url:"/miimCommonRead/queryDataPermission",showPageLoading:!1,data:{jobnumber:e},autoValid:!0});case 2:return a=t.sent,i=new Set,l=new Set,a.data.permission.forEach((function(t){var e=$(t);"hospital"===e&&i.add(t.akb020),"department"===e&&l.add(t.aaz307)})),o=a.data.permission.filter((function(t){return"department"===$(t)||!l.has(t.aaz307)})).filter((function(t){return"hospital"===$(t)||!i.has(t.akb020)})),s=new Set(o.map((function(t){return t.akb020})).filter((function(t){return null!=t}))),n=new Set(o.map((function(t){return t.aaz307})).filter((function(t){return null!=t}))),r=new Set(o.map((function(t){return t.aaz309})).filter((function(t){return null!=t}))),u=new Set(o.map((function(t){return t.aaz263})).filter((function(t){return null!=t}))),c=!1,d=!1,f=!1,m=!1,1===s.size&&(c=!0),1===n.size&&1===s.size&&(d=!0),1===n.size&&1===s.size&&1===r.size&&(f=!0),1===s.size&&0===n.size&&1===u.size&&(m=!0),t.abrupt("return",{akb020Set:s,aaz307Set:n,aaz263Set:u,aaz309Set:r,akb020Disable:c,aaz307Disable:d,aaz263Disable:m,aaz309Disable:f});case 20:case"end":return t.stop()}}),t)}))),D.apply(this,arguments)}function z(t,e){Base.submit(null,{url:"miimCommonRead/getAa01AAE500StartStop",data:t},{successCallback:function(t){return e(t)}})}function N(t,e){Base.submit(null,{url:"miimCommonRead/insertTableColumShow",data:t,autoValid:!1},{successCallback:function(t){return e("success")},failCallback:function(t){return e("fail")}})}var F={zbx01:{footer:function(){return"统筹金总额"}},zbx02:{footer:function(){return"基金支出总额"}},zbx03:{footer:function(){return"总费用"}},zbx04:{footer:function(){return"总住院日"}}},B={permissionCheck:V,getAa01AAE500StartStop:z,insertTableColumShow:N,props:F,moneyNumFormat:function(t){var e=t.cellValue;return e||"—"}},L=a(39101),A=a(28170),P=S.columns,M={name:"doubtfulPointMg",components:{Approval:A.Z,ViewRecord:L.Z,Distribute:_.Z,TaTitle:C.Z,TagChange:T},data:function(){var t=this;return{lastPeriod:"",deptList:[],doctorList:[],deptListout:[],aka130List:[],costTypeList:[],medTypeList:[],admissionNumList:[],hosList:[],formShowAll:!0,fnGenerateButton:!1,tableColumns:P,dataSource:[],pageUrl:d.getPageUrl(),downloadVisible:!1,distributeVisible:!1,columns:[],targetKeys:[],columnsChecked:[],disabled:!1,uploadVisible:!1,uploadVisible2:!1,uploadVisible3:!1,uploadVisible4:!1,medicalInsuranceTypeDisabled:!0,appealDisabled:!1,getMedicalInsuranceTypeList:[],fileUploadDisabled:!0,fileList:[],paramsforDistrubute:{},mateRow:{},mateSucRow:{},aaz263:"",aaz307:"",akb020:"",issueNo:"",clientId:"",dataTypebf:"",processConfig:"",summaryData:{totalNum:0,matchSuccessNum:0,matchFailNum:0,fileCreatingNum:0,fileCreateSuccessNum:0,fileCreateFailNum:0,fileUnCreateNum:0},doubtData:[],costData:[],operateMenu:[{name:"匹配为疑点",isShow:function(t){return 0==t.doubtFlag},onClick:function(e,a){t.checkCostData(),e.doubtFlag=!0,e.pointType="1",t.mateSucRow=e,t.$refs.costTable.updateData()}},{name:"取消匹配",style:"color:#70b504",isShow:function(t){return 1==t.doubtFlag},onClick:function(e,a){e.pointType="0",e.doubtFlag=!1,t.mateSucRow=e,t.$refs.costTable.updateData()}}],showHiddenOrSortColumn:{themeColor:"red",icon:"appstore",showHiddenConfig:{open:!0,disabledHiddenColumns:function(t){return!1}},columnSortConfig:{open:!0,onMove:function(t,e){e.dragColumn,e.dropColumn;return!0},dragEnd:function(t,e){e.dragColumn,e.resultColumns}},successCallback:this.fnSaveTableTitle,disabledControlCol:function(t){}},dispatchMode:"0",medinsuTypeValue:"1",statusColors:{0:"#f49924",1:"#1890ff",2:"#faad14",3:"#a0d911",4:"#52c41a",5:"#722ed1",6:"#f5222d",7:"#13c2c2",8:"#eb2f96",9:"#bfbfbf",10:"#0050b3",11:"#979797"}}},computed:{formBoxStyle:function(){return this.formShowAll?{height:"auto"}:{height:"50px"}}},watch:{},created:function(){var t=this;return(0,s.Z)(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$nextTick((function(){t.$route.query.dataType&&"1"!==t.$route.query.dataType||t.fnQueryTableTitle()}));case 2:case"end":return e.stop()}}),e)})))()},mounted:function(){var t=this;this.$nextTick((function(){var e=t.$route.query;e&&e.aaz263&&(t.aaz263=e.aaz263,t.aaz307=e.aaz307),e.issueNo&&(t.issueNo=e.issueNo,t.form.setFieldsValue(e)),e.dataType&&(t.medinsuTypeValue=e.dataType),"2"!=e.dataType&&"4"!=e.dataType||(t.appealDisabled=!0,t.fnGenerateButton=!0,t.dataTypebf=e.dataType),"3"==e.dataType&&(t.fnGenerateButton=!1,t.dataTypebf=e.dataType),t.fnQueryHos(),t.getAKb020ByJobNumber(),t.fnGetDeptList(m()().format("YYYYMM"),"0"),t.formShowAll=!1,t.queryTableData()}))},methods:{batchCompletion:function(){var t=this,e=this.$refs.xTable.getCheckboxRecords();0!==e.length?this.$confirm({title:"申诉任务状态确认！",content:"请确认是否将所选的申诉任务状态改为“已完成”?更改后所选申诉无法再进行处理。",okType:"danger",okText:"确定",cancelText:"取消",onOk:function(){d.batchCompletion({aaz213s:e.map((function(t){return t.aaz213}))},(function(e){t.$message.success("操作成功"),t.queryTableData()}))},onCancel:function(){}}):this.$message.warning("请选择需要修改的数据")},handCompleted:function(t){var e=this;t.aaz213&&this.$confirm({title:"申诉任务状态确认！",content:"请确认是否将所选的申诉任务状态改为“已完成”?更改后所选申诉无法再进行处理。",okType:"danger",okText:"确定",cancelText:"取消",onOk:function(){d.batchCompletion({aaz213s:[t.aaz213]},(function(t){e.$message.success("操作成功"),e.queryTableData()}))},onCancel:function(){}})},admissionNumChange:function(t,e){t||(this.nameDisable=!1),this.nameDisable=!0},admissionNumSearch:function(t){var e=this;t&&d.getAdmissionNumList({aae043:this.form.getFieldsValue().issueNo,searchVal:t},(function(t){e.admissionNumList=t.data.list}))},doctorChange:function(t,e){},doctorSearch:function(t){var e=this;t&&d.getDoctorList({aae043:this.form.getFieldsValue().aae043,searchVal:t},(function(t){e.doctorList=t.data.list}))},fnQueryHos:function(){var t=this,e={url:"/miimCommonRead/queryHospRpcList",autoValid:!0},a={successCallback:function(e){t.hosList=e.data.resultData},failCallback:function(e){t.$message.error("医院数据加载失败")}};this.Base.submit(null,e,a)},viewRecords:function(t){t.ykc610||t.ykc610V||this.$message.error("当前费用流水号为空！请检查数据！"),this.uploadVisible4=!0,this.mateRow=t},fnQueryTableTitle:function(){var t=this,e=top.indexTool.getActiveTabMenuId()+"_"+this.medinsuTypeValue,a=top.indexTool.getUserInfo().loginId,i=[];this.Base.submit(null,{url:"miimCommonRead/selectTableColumShow",data:{resourceid:e,loginid:a},autoValid:!1},{successCallback:function(e){if(e.data.list.length>0&&e.data.list[0].colum){var a=JSON.parse(e.data.list[0].colum),l=t.$refs.xTable.getTableColumn().fullColumn;i=a.map((function(t){return t.title}));var o=[];l.forEach((function(t){var e=a.find((function(e){return e.title===t.title}));e&&(t.visible=e.visible);var i=t;"操作"!=i.title&&"项目引导信息"!=i.title&&(i.sortable||i.filters?i.width=20*i.title.length+30:i.width=20*i.title.length+12),o.push(i)})),i.length>0&&o.sort((function(t,e){return"序号"===t.title?-1:"序号"===e.title?1:i.indexOf(t.title)-i.indexOf(e.title)})),t.$refs.xTable.loadColumn(o)}"1"!=t.dataTypebf&&"1"!=t.form.getFieldValue("dataType")||("01"==t.processConfig||"11"==t.processConfig?t.$refs.xTable.hideColumn(t.$refs.xTable.getColumnByField("fileStas")):t.$refs.xTable.showColumn(t.$refs.xTable.getColumnByField("fileStas")))},failCallback:function(e){t.$message.error("查询表头失败")}})},fnSaveTableTitle:function(t){var e=this,a=(t.table,t.resultColumnsList);if("1"==this.medinsuTypeValue){var i=a,l=[];i.forEach((function(t){var e=t.title,a=t.visible;t.type;e&&l.push({title:e,visible:a})}));var o=top.indexTool.getActiveTabMenuId()+"_"+this.medinsuTypeValue,s=top.indexTool.getUserInfo().loginId,n={colum:JSON.stringify(l),flag:"column",resourceid:o,loginid:s};B.insertTableColumShow(n,(function(t){"success"===t?e.$message.success("保存成功"):e.$message.error("保存失败")}))}},medinsuTypeChange:function(t){this.queryaka130List("medinsuType",t)},queryaka130List:function(t,e){var a=this,i=this.form.getFieldsValue();"issueNo"===t?i.issueNo=e:"medinsuType"===t?i.medinsuType=e:i.dataType=e,this.Base.submit(null,{url:"hiddscgPoint/queryAka130List",data:i,showPageLoading:!1},{successCallback:function(t){a.aka130List=t.data.aka130List||[]},failCallback:function(t){a.aka130List=[]}})},checkCostData:function(){this.costData=this.costData.map((function(t){return t.doubtFlag=!1,t})),this.$refs.costTable.updateData()},checkCostMarch:function(){for(var t=0;t<this.costData.length;t++)if(this.costData[t].doubtFlag)return!0;return!1},infoPageParams:function(){return this.mateRow.issueNo=this.mateRow.aae043,this.mateRow},handleSave:function(){var t=this;this.mateSucRow.ykc610?(this.mateSucRow.akc222=this.mateRow.akc222,this.mateSucRow.aaz213=this.mateRow.aaz213,this.mateSucRow.pointType||(this.mateSucRow.pointType="0"),this.checkCostMarch()&&(this.mateSucRow.pointType="1"),this.Base.submit(null,{url:"hiddscgPoint/mateSinglePoint",data:this.mateSucRow},{successCallback:function(e){t.$message.success(e.data.msg),t.uploadVisible3=!1,t.queryTableData()},failCallback:function(e){t.$message.error("匹配失败")}})):this.uploadVisible3=!1},summaryPointNum:function(){var t=this;this.form.validateFields((function(e){if(!e){var a=t.form.getFieldsValue();t.Base.submit(null,{url:"hiddscgPoint/queryKf38SummaryPointNum",data:a,showPageLoading:!1},{successCallback:function(e){var a=e.data.data;t.summaryData={totalNum:a.pointNum,matchSuccessNum:a.matchSuccessNum,matchFailNum:a.matchFailNum,fileCreatingNum:a.fileCreatingNum,fileCreateSuccessNum:a.fileCreateSuccessNum,fileCreateFailNum:a.fileCreateFailNum,fileUnCreateNum:a.fileUnCreateNum}},failCallback:function(t){}})}}))},getAKb020ByJobNumber:function(){var t=this;this.Base.submit(null,{url:"/appeal/common/getAKb020ByJobNumber",data:{loginId:this.aaz263}},{successCallback:function(e){t.akb020=e.data.akb020,t.processConfig=e.data.processConfig,"1"!=t.dataTypebf&&"1"!=t.form.getFieldValue("dataType")||("01"==t.processConfig||"11"==t.processConfig?t.$refs.xTable.hideColumn(t.$refs.xTable.getColumnByField("fileStas")):t.$refs.xTable.showColumn(t.$refs.xTable.getColumnByField("fileStas")))},failCallback:function(e){t.$message.error(e.errors[0])}})},showConfirm:function(t){this.$confirm({title:"请确认"+t.ake006+"项目实际是否被拒付？",okText:"是",okType:"danger",cancelText:"否",class:"test",onOk:function(){},onCancel:function(){}})},handMovement:function(t){var e=this;this.doubtData=[],this.costData=[],this.uploadVisible3=!0,this.mateRow=t,this.mateSucRow={},this.doubtData.push(t),this.$nextTick((function(){e.$refs.gridPager2.loadData((function(a){e.costData=a.data.pageBean.list.map((function(a){return a.ykc610==t.ykc610V?(a.doubtFlag=!0,a.pointType="1",e.mateSucRow=a):a.doubtFlag=!1,a}))}))}))},exportAppealMaterials:function(){var t=this,e=m()().format("YYYY-MM-DD HH:mm:ss");Base.downloadFile({type:"application/zip",fileName:"申诉材料文件["+e+"].zip",url:"/appeal/hidAppeal/exportZipForDouble",options:this.pageParams()}).then((function(e){t.$message.success("导出成功")})).catch((function(e){t.$message.error("导出失败")}))},analysisMedicalInsuranceRefusal:function(){var t=this;this.form.validateFields((function(e){if(!e){var a=t.form.getFieldsValue();t.paramsforDistrubute.dataType=a.dataType,t.paramsforDistrubute.aae141=a.medinsuType,t.paramsforDistrubute.aka130=a.medType,t.paramsforDistrubute.aae043=a.issueNo,t.distributeVisible=!0}}))},handleClose2:function(){this.distributeVisible=!1,this.paramsforDistrubute={},this.queryTableData()},handleClose3:function(t){this.uploadVisible3=!1,this.queryTableData()},handleClose4:function(t){this.mateRow={},this.uploadVisible4=!1},handleCreate:function(){var t,e,a=this,i=this.form2.getFieldsValue();if(i.issueNo){if(!i.issueNo[0])return void this.$message.error("请选择完整日期");if(t=i.issueNo[0],!i.issueNo[1])return void this.$message.error("请选择完整日期");e=i.issueNo[1]}i.issueNum=t+"-"+e,this.form2.validateFields((function(t){t||a.Base.submit(null,{url:"refusaldata/createAnalysisReport",data:i},{successCallback:function(t){a.$message.success("执行成功"),a.uploadVisible2=!1},failCallback:function(t){a.$confirm({title:"该任务已经存在，是否重新启动该任务？",okText:"重启",okType:"danger",cancelText:"否",onOk:function(){a.Base.submit(null,{url:"refusaldata/restartAnalysisReport",data:i},{successCallback:function(t){a.$message.success("重启成功"),a.uploadVisible2=!1},failCallback:function(t){a.$message.error("重启失败")}})},onCancel:function(){}})}})}))},moment:m(),setPopupContainer:function(t){return t.parentNode},tooltipMethod:function(t){t.items;var e=t.row,a=(t.rowIndex,t.$rowIndex,t.column);t.columnIndex,t.$columnIndex,t.type,t.cell,t.$event;if("材料状态"===a.title&&"0"===e.fileStas)return"失败原因："+e.fileReason},filterMethod:function(t){var e=t.option,a=t.row;return a.fileStas===e.data},fnGetDeptList:function(t,e){var a=this;this.issueNo&&(t=this.issueNo),d.getDeptList({issueNo:t},(function(e){a.deptList=e.data.deprtNameList,a.deptListout=e.data.dscgDeprtNameList,a.costTypeList=e.data.costTypeList,a.medTypeList=e.data.medTypeList,a.lastPeriod=t})),this.queryaka130List("issueNo",t),this.issueNo="","1"===e&&this.$nextTick((function(){a.queryTableData()}))},formShowAllChange:function(){this.formShowAll=!this.formShowAll},pageParams:function(){var t,e=this.form.getFieldsValue();return t="2"==this.dataTypebf||"2"==e.dataType?this.$refs.gridPager3.getPagerInfo():"4"==this.dataTypebf||"4"==e.dataType?this.$refs.gridPager4.getPagerInfo():this.$refs.gridPager.getPagerInfo(),Object.assign(e,t),e},queryTableData:function(){var t=this;this.summaryPointNum(),this.form.validateFields((function(e){e||("3"==t.dataTypebf||"3"==t.form.getFieldValue("dataType")?(t.$refs.xTable.hideColumn(t.$refs.xTable.getColumnByField("aac001")),t.$refs.xTable.hideColumn(t.$refs.xTable.getColumnByField("akc190V")),t.$refs.xTable.hideColumn(t.$refs.xTable.getColumnByField("ape804V")),t.$refs.xTable.hideColumn(t.$refs.xTable.getColumnByField("ake006V")),t.$refs.xTable.hideColumn(t.$refs.xTable.getColumnByField("aac004V")),t.$refs.xTable.hideColumn(t.$refs.xTable.getColumnByField("sdksmcV")),t.$refs.xTable.hideColumn(t.$refs.xTable.getColumnByField("aae386V")),t.$refs.xTable.hideColumn(t.$refs.xTable.getColumnByField("view")),t.$refs.xTable.hideColumn(t.$refs.xTable.getColumnByField("matchStas")),t.$refs.xTable.hideColumn(t.$refs.xTable.getColumnByField("aaz560")),t.$refs.xTable.hideColumn(t.$refs.xTable.getColumnByField("fileStas")),t.$refs.xTable.hideColumn(t.$refs.xTable.getColumnByField("aae031"))):"1"!=t.dataTypebf&&"1"!=t.form.getFieldValue("dataType")||(t.$refs.xTable.showColumn(t.$refs.xTable.getColumnByField("aac001")),t.$refs.xTable.showColumn(t.$refs.xTable.getColumnByField("akc190V")),t.$refs.xTable.showColumn(t.$refs.xTable.getColumnByField("ape804V")),t.$refs.xTable.showColumn(t.$refs.xTable.getColumnByField("ake006V")),t.$refs.xTable.showColumn(t.$refs.xTable.getColumnByField("aac004V")),t.$refs.xTable.showColumn(t.$refs.xTable.getColumnByField("sdksmcV")),t.$refs.xTable.showColumn(t.$refs.xTable.getColumnByField("aae386V")),t.$refs.xTable.showColumn(t.$refs.xTable.getColumnByField("view")),t.$refs.xTable.showColumn(t.$refs.xTable.getColumnByField("matchStas")),t.$refs.xTable.showColumn(t.$refs.xTable.getColumnByField("aaz560")),t.$refs.xTable.showColumn(t.$refs.xTable.getColumnByField("fileStas")),t.$refs.xTable.showColumn(t.$refs.xTable.getColumnByField("aae031"))),"2"==t.dataTypebf||"2"==t.form.getFieldValue("dataType")?t.$refs.gridPager3.loadData():"4"==t.dataTypebf||"4"==t.form.getFieldValue("dataType")?t.$refs.gridPager4.loadData():t.$refs.gridPager.loadData(),t.formShowAll=!1)}))},showFileReason:function(t,e){t.falseReasonShow=!0,this.$refs.xTable.updateStatus({row:t,column:e})},hideFileReason:function(t,e){t.falseReasonShow=!1,this.$refs.xTable.updateStatus({row:t,column:e})},fnGenerateComplaintMaterials:function(){for(var t=this,e=!0,a=0;a<this.dataSource.length;a++)"0"!==this.dataSource[a].handStas&&(e=!1);e?this.generateAppealMaterials():this.$confirm({title:"提示",okText:"确认",content:"当前存在已下发的疑点明细，系统仅对未下发的疑点明细自动生成申诉材料",cancelText:"取消",class:"test",onOk:function(){t.generateAppealMaterials()},onCancel:function(){}})},generateAppealMaterials:function(){var t=this;if(0!==this.dataSource.length){var e={};e.issueNo=this.dataSource[0].aae043,e.dataType=this.form.getFieldValue("dataType"),e.medinsuType=this.form.getFieldValue("medinsuType"),e.medTypeList=this.form.getFieldValue("medTypeList"),e.akb020=this.akb020,d.handleBatchGenerate(e,(function(e){"200"==e.data.code?t.$message.success(e.data.msg):"201"==e.data.code&&t.$message.info(e.data.msg),t.queryTableData()}))}else this.$message.warning("请先进行查询确定本期是否有数据")},reGenerate:function(t){var e=this;t.issueNo=t.aae043,t.pointType=this.form.getFieldValue("pointType"),t.akb020=this.akb020,d.reGenerate(t,(function(t){e.$message.success("生成成功"),e.queryTableData()}))},resetForm:function(){this.form.resetFields(),this.fnGenerateButton=!1},handleOpen:function(t){var e=this;"0"===t?(this.downloadVisible=!0,d.getColumns({akb020:this.akb020,aaz263:this.aaz263},(function(t){e.targetKeys=t.data.resultMap.filter((function(t){return"1"===t.required||"1"===t.checkState})).map((function(t){return t.columnKey})),e.columns=t.data.resultMap.map((function(t){return t.disabled="1"===t.required,t}))}))):(this.uploadVisible=!0,this.$nextTick((function(){e.form1.setFieldsValue({issueNo:m()().format("YYYYMM")})})))},handleClose:function(t){"0"===t?this.downloadVisible=!1:(this.uploadVisible=!1,this.fileList=[],this.medicalInsuranceTypeDisabled=!0,this.fileUploadDisabled=!0,this.form1.resetFields()),this.queryTableData()},handleChange:function(t,e,a){this.targetKeys=t},handleSelectChange:function(t,e){this.columnsChecked=[].concat((0,o.Z)(t),(0,o.Z)(e))},handleDownload:function(){var t=this,e=this.columns.map((function(e){return e.checkState=-1!==t.targetKeys.indexOf(e.columnKey)?"1":"0",e}));d.getColumnsInfo({ae11ListStr:JSON.stringify(e)},(function(e){var a=t.columns.filter((function(t){return"1"===t.required})).map((function(t){return t.columnKey}));h.Z.generateExcel({fileName:"疑点管理导入模板",fileType:".xls",sheets:[{name:"sheet1",column:{complex:!1,columns:e.data.resultMap,headerStyle:{cellStyle:function(t,e,i,l,o){a.indexOf(t._column._key)>-1&&(t.font={name:"Arial Black",color:{argb:"00ff0000"},family:2})}}}}]})}))},uploadMonthchange:function(t,e){var a=this,i=this.form1.getFieldsValue();e&&(i.ape800=t),i.ape800&&d.getUploadFilter(i,(function(t){a.medicalInsuranceTypeDisabled=!1,a.getMedicalInsuranceTypeList=t.data.insuList}))},dataTypeChange:function(t){var e=this;this.dataTypebf=t,"2"==t||"4"==t?(this.appealDisabled=!0,this.fnGenerateButton=!0):(this.appealDisabled=!1,this.fnGenerateButton=!1),this.$refs.xTable.refreshColumn(),this.medinsuTypeValue=t,this.queryaka130List("dataType",t),this.$nextTick((function(){"1"==t&&e.fnQueryTableTitle(),e.queryTableData()}))},beforeUpload:function(t){if(t.size/1024>51200)return this.$message.warning("请选择不超过50M的文件"),!1;var e=t.name.split("."),a=e[e.length-1];return-1===["xls","xlsx","csv"].indexOf(a)?(this.$message.warning("只能上传excel文件"),!1):(this.fileList=[t],this.form1.setFieldsValue({fileName:t.name}),!1)},handleUpload:function(){var t=this,e=this.form1.getFieldsValue();this.form1.validateFields((function(a){a||(e.file=t.fileList[0],e.akb020=t.akb020,d.uploadDatas(e,(function(e){t.handleClose("1"),t.fnGetDeptList(t.lastPeriod,"0"),t.$message.success("导入数据成功")})))}))},queryDispatchMode:function(){var t=this;d.queryDispatchMode(null,(function(e){t.dispatchMode=e.data.model.dispatchMode}))}}},R=M,I=(0,y.Z)(R,i,l,!1,null,"563e98b4",null),q=I.exports},55382:function(){},61219:function(){}}]);
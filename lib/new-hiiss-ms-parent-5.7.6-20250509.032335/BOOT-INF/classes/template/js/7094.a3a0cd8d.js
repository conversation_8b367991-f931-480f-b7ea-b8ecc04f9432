"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[7094],{64964:function(t,e,a){a.d(e,{Z:function(){return d}});var n=function(){var t=this,e=this,a=e.$createElement,n=e._self._c||a;return n("div",{staticStyle:{height:"490px"}},[n("div",{staticClass:"title"},[e._v("新增")]),n("ta-form",{attrs:{autoFormCreate:function(e){t.addForm=e}}},[e._l(e.editColumns,(function(t,e){return[!t.collectionType?n("ta-form-item",{attrs:{fieldDecoratorId:t.dataIndex,label:t.title,require:{enable:!!t.require,message:"不能为空"}}},[n("ta-input",{staticStyle:{width:"80%"}})],1):n("ta-form-item",{attrs:{"init-value":"MAPE888"==t.collectionType?"1":void 0,fieldDecoratorId:t.dataIndex,label:t.title,require:{enable:!!t.require,message:"不能为空"}}},[n("ta-select",{staticStyle:{width:"80%"},attrs:{collectionType:t.collectionType}})],1)]})),n("ta-button",{staticStyle:{"margin-left":"45%"},attrs:{type:"success"},on:{click:e.save}},[e._v("保存")])],2)],1)},o=[],i={name:"addOne",components:{},props:{column:{type:Array}},data:function(){return{editColumns:[]}},mounted:function(){this.editColumns=this.column.filter((function(t){return"ykz042"!=t.dataIndex&&"ykz010"!=t.dataIndex&&"enduser"!=t.dataIndex&&"enddate"!=t.dataIndex&&"stadate"!=t.dataIndex&&"stauser"!=t.dataIndex&&"ykz095"!=t.dataIndex}))},methods:{save:function(){var t=this;this.addForm.validateFields((function(e){e||t.$emit("fnAddOne",t.addForm.getFieldsValue())}))}}},l=i,s=a(1001),r=(0,s.Z)(l,n,o,!1,null,"f405d334",null),d=r.exports},7094:function(t,e,a){a.r(e),a.d(e,{default:function(){return mt}});var n=function(){var t=this,e=this,a=e.$createElement,n=e._self._c||a;return n("ta-border-layout",{attrs:{layout:{left:"300px"},"show-padding":!1}},[n("template",{slot:"left"},[n("div",{staticClass:"title",staticStyle:{"margin-left":"10px"}},[e._v("节点情况")]),n("div",{staticStyle:{height:"calc(100% - 100px)"}},[n("node-type-list",{attrs:{queryUrl:"mttRuleConfig/getNodeTypeList"},on:{nodeTypeSelect:e.nodeTypeSelect}})],1)]),n("div",{staticClass:"title",staticStyle:{"margin-left":"10px"}},[e._v("查询条件")]),n("ta-card",{staticStyle:{width:"100%"},attrs:{bordered:!1}},[n("ta-form",{attrs:{"auto-form-create":function(e){return t.searchForm=e}}},[n("ta-row",[n("ta-col",{attrs:{span:8}},[n("ta-form-item",{attrs:{label:"节点名称","field-decorator-id":"ykz010"}},[n("ta-input",{attrs:{"allow-clear":!0}})],1)],1),n("ta-col",{attrs:{span:8}},[n("ta-form-item",{attrs:{label:"节点编码","field-decorator-id":"ykz042"}},[n("ta-input",{attrs:{"allow-clear":!0}})],1)],1),n("ta-col",{attrs:{span:8}},[n("ta-form-item",{attrs:{label:"节点说明","field-decorator-id":"ykz065"}},[n("ta-input",{attrs:{"allow-clear":!0}})],1)],1),n("ta-col",{attrs:{span:8}},[n("ta-form-item",{attrs:{label:"对照关系","field-decorator-id":"relative"}},[n("ta-select",{attrs:{"allow-clear":!0}},[n("ta-select-option",{attrs:{value:"1"}},[e._v("已配置")]),n("ta-select-option",{attrs:{value:"0"}},[e._v("未配置")])],1)],1)],1)],1),n("ta-row",[n("ta-col",{staticStyle:{"text-align":"center"}},[n("ta-button",{on:{click:e.onSearch}},[e._v("查询")]),n("ta-button",{on:{click:e.onDownloadTemplate}},[e._v("模板下载")]),e.ykz001?n("ta-button",{attrs:{type:"primary"},on:{click:e.openAddNode}},[e._v("新增")]):e._e()],1)],1)],1)],1),n("ta-card",{attrs:{bordered:!1}},[n("node-data",{ref:"nodeData",attrs:{blnPlatform:!0}})],1),n("ta-modal",{attrs:{width:400,height:150,"destroy-on-close":!0,footer:null},model:{value:e.importVisible,callback:function(t){e.importVisible=t},expression:"importVisible"}},[n("import-data",{attrs:{ykz001:e.ykz001,blnPlatform:!0},on:{onImportSuccess:e.onImportSuccess}})],1),n("ta-modal",{attrs:{title:"新增节点",width:900,height:250,"destroy-on-close":!0},model:{value:e.blnAddNodeVisible,callback:function(t){e.blnAddNodeVisible=t},expression:"blnAddNodeVisible"}},[n("ta-form",{attrs:{autoFormCreate:function(e){t.nodeConfForm=e}}},[n("ta-row",{staticStyle:{"margin-left":"-19%"}},[n("ta-col",{attrs:{span:this.addNodeConfStyle.nodeConfSpan}},[n("ta-form-item",{attrs:{fieldDecoratorId:"ykz010",label:"节点内容",required:!0,require:{message:"节点内容不能为空"}}},[n("ta-input")],1),n("ta-form-item",{attrs:{fieldDecoratorId:"ykz065",label:"节点说明"}},[n("ta-textarea",{attrs:{placeholder:"请输入",rows:4}})],1)],1)],1)],1),n("div",{attrs:{slot:"footer"},slot:"footer"},[n("ta-button",{attrs:{type:"primary"},on:{click:e.addNode}},[e._v("保存")])],1)],1)],2)},o=[],i=a(95082),l=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"fit"},[a("ta-input",{attrs:{placeholder:"编号或名称或简写筛选"},on:{change:t.filterTable},model:{value:t.searchText,callback:function(e){t.searchText=e},expression:"searchText"}}),a("ta-table",{attrs:{columns:t.nodeTypeColumn,"data-source":t.nodeTypeShowList,bordered:!0,size:"small",scroll:{x:"100%",y:"100%"},"custom-row":t.nodeTypeCustomRow}})],1)},s=[],r=a(66347),d={name:"nodeTypeList",props:{queryUrl:String},data:function(){return{nodeTypeColumn:[{dataIndex:"ykz001",title:"编号",width:100,align:"center",overflowTooltip:!0},{dataIndex:"showName",title:"判断节点类型",width:150,align:"center",overflowTooltip:!0}],nodeTypeList:[],nodeTypeShowList:[],searchText:null}},mounted:function(){this.getNodeTypeList()},methods:{getNodeTypeList:function(){var t=this;this.Base.submit(null,{url:this.queryUrl}).then((function(e){var a,n=(0,r.Z)(e.data.list);try{for(n.s();!(a=n.n()).done;){var o=a.value;o.showName="".concat(o.ykz002,"(").concat(o.ykz063,")")}}catch(i){n.e(i)}finally{n.f()}t.nodeTypeList=e.data.list,t.nodeTypeShowList=e.data.list}))},nodeTypeCustomRow:function(t,e,a){var n=this,o="black",i="white";return t.blnYellow?(o="#73cfb3",i="#ffffd5"):t.blnGreen&&(i="#d0ffbf"),{style:{cursor:"pointer",backgroundColor:i,color:o},on:{click:function(e){n.nodeTypeList.forEach((function(e){t.ykz001==e.ykz001?e.blnYellow=!0:e.blnYellow=!1})),n.$forceUpdate(),n.$emit("nodeTypeSelect",t.ykz001)}}}},filterTable:function(){var t=this;this.searchText?this.nodeTypeShowList=this.nodeTypeList.filter((function(e){return e.showName.toLowerCase().includes(t.searchText.toLowerCase())||e.ykz001.toLowerCase().includes(t.searchText.toLowerCase())})):this.nodeTypeShowList=this.nodeTypeList}}},c=d,u=a(1001),m=(0,u.Z)(c,l,s,!1,null,"36b58192",null),f=m.exports,p=function(){var t=this,e=this,a=e.$createElement,n=e._self._c||a;return n("div",[n("ta-form",{attrs:{"auto-form-create":function(e){return t.searchForm=e}}},[n("ta-row",[n("ta-col",{attrs:{span:8}},[n("ta-form-item",{attrs:{label:"节点名称","field-decorator-id":"ykz010"}},[n("ta-input",{attrs:{"allow-clear":!0}})],1)],1),n("ta-col",{attrs:{span:8}},[n("ta-form-item",{attrs:{label:"节点编码","field-decorator-id":"ykz042"}},[n("ta-input",{attrs:{"allow-clear":!0}})],1)],1),n("ta-col",{attrs:{span:8}},[n("ta-form-item",{attrs:{label:"节点说明","field-decorator-id":"ykz065"}},[n("ta-input",{attrs:{"allow-clear":!0}})],1)],1),n("ta-col",{attrs:{span:8}},[n("ta-form-item",{attrs:{label:"对照关系","field-decorator-id":"relative"}},[n("ta-select",{attrs:{"allow-clear":!0}},[n("ta-select-option",{attrs:{value:"1"}},[e._v("已配置")]),n("ta-select-option",{attrs:{value:"0"}},[e._v("未配置")])],1)],1)],1)],1),n("ta-row",[n("ta-col",{staticStyle:{"text-align":"center"}},[n("ta-button",{on:{click:e.onSearch}},[e._v("查询")]),n("ta-button",{on:{click:e.downloadTemplate}},[e._v("模板下载")]),n("ta-button",{attrs:{type:"primary"},on:{click:e.openAddNode}},[e._v("新增")])],1)],1)],1),n("ta-modal",{attrs:{title:"新增节点",width:900,height:250},model:{value:e.blnAddNodeVisible,callback:function(t){e.blnAddNodeVisible=t},expression:"blnAddNodeVisible"}},[n("ta-form",{attrs:{autoFormCreate:function(e){t.nodeConfForm=e}}},[n("ta-row",{staticStyle:{"margin-left":"-19%"}},[n("ta-col",{attrs:{span:this.addNodeConfStyle.nodeConfSpan}},[n("ta-form-item",{attrs:{fieldDecoratorId:"ykz010",label:"节点内容",required:!0,require:{message:"节点内容不能为空"}}},[n("ta-input")],1),n("ta-form-item",{attrs:{fieldDecoratorId:"ykz065",label:"节点说明"}},[n("ta-textarea",{attrs:{placeholder:"请输入",rows:4}})],1)],1)],1)],1),n("div",{attrs:{slot:"footer"},slot:"footer"},[n("ta-button",{attrs:{type:"primary"},on:{click:e.addNode}},[e._v("保存")])],1)],1)],1)},h=[],g={name:"nodeSearch",props:{},data:function(){return{ruleidList:[],blnAddNodeVisible:!1,addNodeConfStyle:{nodeConfSpan:23},ykz001Options:[]}},mounted:function(){this.getYkz001List()},methods:{getYkz001List:function(){var t=this,e={url:"mttRuleConfig/getNodeTypeList",data:{}};this.Base.submit(null,e).then((function(e){e.data.success&&(t.ykz001Options=e.data.list)})).catch()},onSearch:function(){var t=this.searchForm.getFieldsValue();this.$emit("onSearch",t)},downloadTemplate:function(){var t=this.searchForm.getFieldsValue();this.$emit("onDownloadTemplate",t)},exportData:function(){var t=this.searchForm.getFieldsValue();this.$emit("onExportData",t)},importData:function(){var t=this.searchForm.getFieldsValue();this.$emit("onImportData",t)},openAddNode:function(){this.blnAddNodeVisible=!0},addNode:function(){var t=this;this.nodeConfForm.validateFields((function(e){if(!e){var a=t.nodeConfForm.getFieldsValue();Base.submit(null,{url:"mttRuleConfig/addNode",data:{dtoStr:JSON.stringify(a)}}).then((function(e){t.$message.success("新增成功"),t.onSearch()}))}}))}}},y=g,v=(0,u.Z)(y,p,h,!1,null,"33cc3fab",null),b=v.exports,k=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("ta-table",{attrs:{columns:t.nodeColumn,"data-source":t.nodeData,bordered:!0,"have-sn":!0,size:"small","row-key":"yka062",scroll:{x:"100%",y:"386px"}},scopedSlots:t._u([{key:"needsync",fn:function(e,n){return["1"==n.needsync?a("ta-tag",{attrs:{color:"red"}},[t._v(" 未同步 ")]):a("ta-tag",{attrs:{color:"green"}},[t._v(" 已同步 ")])]}},{key:"relative",fn:function(e,n){return["1"==n.relative?a("a",{on:{click:function(e){return t.configNode(n)}}},[t._v("已配置")]):a("a",{staticStyle:{color:"red"},on:{click:function(e){return t.configNode(n)}}},[t._v("未配置")])]}},{key:"operate",fn:function(e,n){return[a("a",{on:{click:function(e){return t.downloadMke(n)}}},[t._v("下载")]),a("ta-popconfirm",{attrs:{title:"确定同步到引擎?"},on:{confirm:function(e){return t.syncPackage(n)}}},[a("a",{staticStyle:{"margin-left":"10px"}},[t._v("同步至引擎")])])]}},{key:"log",fn:function(e,n){return[a("a",{on:{click:function(e){return t.nodeLog(n)}}},[t._v("查看")])]}}])}),a("ta-pagination",{ref:"pager",staticClass:"page",attrs:{defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"],dataSource:t.nodeData,params:t.nodeParam,url:t.pageUrl},on:{"update:dataSource":function(e){t.nodeData=e},"update:data-source":function(e){t.nodeData=e}},model:{value:t.pageParam.pageNumber,callback:function(e){t.$set(t.pageParam,"pageNumber",e)},expression:"pageParam.pageNumber"}}),a("ta-modal",{attrs:{title:t.ykz072,visible:t.visible,footer:null,width:"90%",height:"700px",zoomable:!0,destroyOnClose:!0},on:{cancel:t.handleNodeCancel}}),a("ta-modal",{attrs:{"destroy-on-close":!0,title:"节点维护",height:600,width:1400},model:{value:t.blnNodeContentVisible,callback:function(e){t.blnNodeContentVisible=e},expression:"blnNodeContentVisible"}},[a("node-content",{attrs:{"clicked-node":t.clickNode}}),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("ta-button",{on:{click:function(e){t.blnNodeContentVisible=!1}}},[t._v("取消")])],1)],1),a("ta-modal",{attrs:{title:"包版本更新记录",visible:t.logVisible,footer:null,width:"80%",height:"500px",zoomable:!0,destroyOnClose:!0},on:{cancel:t.handleCancel}},[a("update-log",{attrs:{ykz042:t.nodeInfos.ykz042}})],1)],1)},C=[],T=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("ta-icon",{staticStyle:{color:"#1890ff"},attrs:{type:"right"}}),t._v(" 节点内容 "),a("ta-popconfirm",{staticStyle:{"margin-left":"50px"},attrs:{title:"刷新将清除页面内容，加载库中内容。"},on:{confirm:t.freshData}},[a("ta-button",[t._v("刷新")])],1),a("ta-button",{attrs:{type:"primary"},on:{click:t.doSave}},[t._v("保存")]),a("ta-row",[a("ta-col",{staticStyle:{"margin-top":"10px"},attrs:{span:24}},[a("ta-col",{attrs:{span:12}},[a("ta-button-group",{staticStyle:{float:"left"}},[a("ta-button",{on:{click:t.openAddContent}},[t._v("新增")]),a("ta-button",{on:{click:t.openDisable}},[t._v("禁用")]),a("ta-button",{on:{click:t.downloadContentTemplate}},[t._v("模板下载")]),a("ta-button",{on:{click:t.openImportContent}},[t._v("导入")]),a("ta-button",{on:{click:t.removeContent}},[t._v("移除")])],1),a("span",{staticStyle:{"margin-left":"10px","line-height":"35px"}},[t._v(t._s(t.clickedNode.ykz042+":"+t.clickedNode.ykz010))])],1),a("ta-col",{attrs:{span:12}},[a("div",{staticStyle:{float:"right"}},[t._v(" 关键字: "),a("ta-input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入编码或名称"},model:{value:t.contentSearchText,callback:function(e){t.contentSearchText=e},expression:"contentSearchText"}}),a("ta-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary"},on:{click:t.onNodeContentSearch}},[t._v("查询 ")])],1)]),a("ta-col",{staticStyle:{"margin-top":"10px"},attrs:{span:24}},[a("ta-big-table",{ref:"contentTable",attrs:{data:t.contentListCache,"big-data-checkbox":"","header-drag-style":"","use-virtual":"",border:"full",heightSwitch:!0,height:450,"row-height":40,"row-style":t.contentCustomRow}},[a("ta-big-table-column",{attrs:{type:"checkbox",width:"55"}}),a("ta-big-table-column",{attrs:{type:"seq",width:"60",title:"序号",fixed:""}}),t._l(t.contentColumns,(function(t,e){return a("ta-big-table-column",{key:e,attrs:{align:t.align,resizable:"","show-header-overflow":"","show-overflow":t.overflowTooltip,field:t.dataIndex,title:t.title,fixed:t.fixed,width:t.width,collectionType:t.collectionType}})}))],2)],1)],1)],1),a("ta-modal",{attrs:{title:"新增内容",footer:null,width:"60%",height:"600px",zoomable:!0,destroyOnClose:!0},on:{cancel:function(t){}},model:{value:t.addContentVisible,callback:function(e){t.addContentVisible=e},expression:"addContentVisible"}},[a("add-content-one",{attrs:{column:t.contentColumns},on:{fnAddOne:t.fnAddContentOneSuccess}})],1),a("ta-modal",{attrs:{title:"导入内容",destroyOnClose:!0,footer:null,width:500},model:{value:t.importContentVisible,callback:function(e){t.importContentVisible=e},expression:"importContentVisible"}},[a("importData",{attrs:{ykz001:this.clickedNode.ykz001},on:{onImportSuccess:t.onContentImportSuccess}})],1),a("ta-modal",{attrs:{title:"描述原因",footer:null,width:"60%",height:"400px",zoomable:!0,destroyOnClose:!0},on:{cancel:function(e){t.reasonVisible=!1}},model:{value:t.reasonVisible,callback:function(e){t.reasonVisible=e},expression:"reasonVisible"}},[a("save-reason",{attrs:{"dis-data":t.disData,"add-data":t.addData,nodeInfos:t.clickedNode},on:{closeSaved:t.closeSaved}})],1),a("ta-modal",{attrs:{title:"禁用说明"},model:{value:t.disableVisible,callback:function(e){t.disableVisible=e},expression:"disableVisible"}},[a("ta-input",{attrs:{placeholder:"请输入"},model:{value:t.disableDesc,callback:function(e){t.disableDesc=e},expression:"disableDesc"}}),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("ta-button",{attrs:{type:"primary"},on:{click:t.saveDisableDesc}},[t._v("确定")]),a("ta-button",{on:{click:function(e){t.disableVisible=!1}}},[t._v("取消")])],1)],1)],1)},w=[],S=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"fit"},[a("ta-upload",{attrs:{fileList:t.fileList,name:"file",accept:".xlsx, .xls",multiple:!1,action:"","show-upload-list":!1,beforeUpload:t.beforeUpload},on:{change:t.uploadChange}},[a("ta-button",{staticStyle:{"margin-left":"100px","margin-top":"50px",width:"120px"},attrs:{type:"primary"}},[a("ta-icon",{attrs:{type:"upload"}}),t._v(" 导入文件 ")],1)],1)],1)},x=[],z={name:"importData",props:{ykz001:String},computed:{importUrl:function(){return"mttRuleConfig/generateImportData"}},data:function(){return{fileList:[]}},methods:{beforeUpload:function(t){return this.fileList.push(t),!1},uploadChange:function(){var t=this;this.fileList.length<=0?this.$message.error("还未选择文件！"):Base.submit(null,{url:this.importUrl,data:{ykz001:this.ykz001,uploadFile:this.fileList[0]},isFormData:!0,autoValid:!0}).then((function(e){e.data.success&&(t.$message.success("导入表格成功"),t.$emit("onImportSuccess",e.data.list))}))}}},D=z,N=(0,u.Z)(D,S,x,!1,null,"76d54c1c",null),I=N.exports,F=a(64964),_=function(){var t=this,e=this,a=e.$createElement,n=e._self._c||a;return n("div",{staticStyle:{height:"100%"}},[n("ta-form",{attrs:{autoFormCreate:function(e){t.reasonForm=e}}},[n("ta-form-item",{attrs:{fieldDecoratorId:"ykz137",label:"更新原因",require:!0}},[n("ta-input",{staticStyle:{width:"80%"}})],1),n("ta-form-item",{attrs:{fieldDecoratorId:"ykz138",label:"更新内容说明",require:!0}},[n("ta-input",{staticStyle:{width:"80%"}})],1),n("ta-form-item",{attrs:{fieldDecoratorId:"ykz163",label:"依据",require:!0}},[n("ta-input",{staticStyle:{width:"80%"}})],1),n("ta-form-item",{attrs:{fieldDecoratorId:"ykz149",label:"是否参与打包",require:!0,initValue:["1"]}},[n("ta-select",{staticStyle:{width:"80%"},on:{change:e.fnChange}},[n("ta-select-option",{attrs:{value:"0"}},[e._v("不参与打包")]),n("ta-select-option",{attrs:{value:"1"}},[e._v("参与打包")])],1)],1),n("ta-form-item",{attrs:{fieldDecoratorId:"ykz152",label:"设置原因",require:!0}},[n("ta-input",{staticStyle:{width:"80%"},attrs:{disabled:e.flag}})],1),n("ta-button",{staticStyle:{"margin-left":"45%"},attrs:{type:"success"},on:{click:e.save}},[e._v("保存")])],1)],1)},L=[],V=a(66353),$=a(89584),P=["plChoose","_XID","blnAdd","blnDelete","rid"],R={name:"saveReason",components:{},props:{disData:{type:Array},addData:{type:Array},nodeInfos:{type:Object}},data:function(){return{flag:!0}},mounted:function(){this.reasonForm.setFieldsValue({ykz152:"默认打包"})},methods:{fnChange:function(t,e){"0"===t&&(this.flag=!1,this.reasonForm.setFieldsValue({ykz152:""})),"1"===t&&(this.flag=!0,this.reasonForm.setFieldsValue({ykz152:"默认打包"}))},save:function(){var t=this;if(null!==this.addData&&null!==this.disData){for(var e="mttRuleConfig/saveContentResult",a=this.reasonForm.getFieldsValue(),n=(0,$.Z)(this.addData),o=0;o<n.length;o++){var l=n[o],s=(0,i.Z)({},l),r=(s.plChoose,s._XID,s.blnAdd,s.blnDelete,s.rid,(0,V.Z)(s,P));n[o]=(0,i.Z)({},r)}var d={disData:JSON.stringify(this.disData),addData:JSON.stringify(n),ykz137:a.ykz137,ykz138:a.ykz138,ykz163:a.ykz163,ykz149:a.ykz149[0],ykz152:a.ykz152,ykz001:this.nodeInfos.ykz001,ykz042:this.nodeInfos.ykz042,ykz010:this.nodeInfos.ykz010},c={url:e,data:d,autoValid:!0},u={successCallback:function(e){e.data.success?(t.$emit("closeSaved"),t.$message.success("保存成功")):t.$message.error(e.errors[0].msg)}};this.Base.submit(this.reasonForm,c,u)}else this.$message.warn("请先进行数据操作！")}}},A=R,E=(0,u.Z)(A,_,L,!1,null,"46e1cb14",null),O=E.exports,M={name:"nodeContent",props:["clickedNode"],components:{addContentOne:F.Z,importData:I,saveReason:O},data:function(){return{contentColumns:[{dataIndex:"ykz002",title:"节点类型",width:100,align:"center",overflowTooltip:!0},{dataIndex:"ykz010",title:"节点名称",width:100,align:"center",overflowTooltip:!0}],contentList:[],contentSearchText:void 0,addContentVisible:!1,importContentVisible:!1,importNodeInfo:{},contentListCache:[],maxRid:0,conditionColumns:["aka120"],reasonVisible:!1,disData:[],addData:[],disableVisible:!1,disableDesc:void 0}},mounted:function(){this.queryContentColumns()},methods:{freshData:function(){this.contentSearchText=void 0,this.getContentList()},contentCustomRow:function(t){var e=t.row,a=(t.rowIndex,"white");return e.blnAdd&&(a="#d0ffbf"),e.blnDelete&&(a="#f5ccd0"),{cursor:"pointer",backgroundColor:a}},queryContentColumns:function(){var t=this;Base.submit(null,{url:"mttRuleConfig/queryContentColumns",data:{ykz001:this.clickedNode.ykz001}}).then((function(e){t.contentColumns=e.data.info.list,t.conditionColumns=e.data.info.conditionColumns,t.$nextTick((function(){t.getContentList()}))}))},contentPageParam:function(){return{ykz001:this.clickedNode.ykz001,ykz042:this.clickedNode.ykz042,text:this.contentSearchText}},getContentList:function(){var t=this;this.clickedNode?Base.submit(null,{url:"mttRuleConfig/queryNodeData",data:{ykz001:this.clickedNode.ykz001,ykz042:this.clickedNode.ykz042}}).then((function(e){t.contentList=e.data.list,t.contentListCache=e.data.list,t.findMaxRid()})):this.contentList=[]},onNodeContentSearch:function(){var t=this;this.contentSearchText?this.contentListCache=this.contentList.filter((function(e){var a,n=(0,r.Z)(t.conditionColumns);try{for(n.s();!(a=n.n()).done;){var o=a.value;return!(!e[o]||!e[o].includes(t.contentSearchText))}}catch(i){n.e(i)}finally{n.f()}})):this.contentListCache=this.contentList},findMaxRid:function(){this.maxRid=0;var t,e=(0,r.Z)(this.contentList);try{for(e.s();!(t=e.n()).done;){var a=t.value;parseInt(this.maxRid)<parseInt(a.rid)&&(this.maxRid=parseInt(a.rid))}}catch(n){e.e(n)}finally{e.f()}},downloadContentTemplate:function(){var t=this,e={url:"mttRuleConfig/downloadContentTemplate",data:{ykz001:this.clickedNode.ykz001},autoValid:!0},a={successCallback:function(e){if(e.data.success){var a=e.data.fileName,n=e.data.data,o=document.createElement("a");o.download=a,o.href="data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,"+n,o.click(),t.$message.success("下载成功")}else t.$message.error(e.errors[0].msg)}};this.Base.submit("",e,a)},openAddContent:function(){this.addContentVisible=!0},fnAddContentOneSuccess:function(t){t.ykz042=this.clickedNode.ykz042,t.ykz010=this.clickedNode.ykz010,t.rid=++this.maxRid,t.blnAdd=!0,this.contentList.push(t),this.onNodeContentSearch(),this.addContentVisible=!1,this.$message.success("新增成功")},deleteContent:function(){var t=this.$refs.contentTable.getCheckboxRecords();0!=t.length||this.$message.warn("请勾选要移除的行")},openDisable:function(){this.disableDesc=void 0,this.disableVisible=!0},saveDisableDesc:function(){var t=this.$refs.contentTable.getCheckboxRecords().map((function(t){return t.rid})),e=this.$refs.contentTable.getCheckboxRecords();if(0!=e.length){var a=e.filter((function(t){return t.blnAdd}));if(a.length>0)this.$message.warn("不能禁用未保存的记录");else if(this.disableDesc){for(var n=0;n<this.contentList.length;n++)t.includes(this.contentList[n].rid)&&(this.contentList[n].blnDelete=!0,this.contentList[n].ykz095=this.disableDesc);for(var o=0;o<this.contentListCache.length;o++)t.includes(this.contentListCache[o].rid)&&(this.contentListCache[o].blnDelete=!0,this.contentListCache[o].ykz095=this.disableDesc,this.$set(this.contentListCache,o,this.contentListCache[o]));this.disableVisible=!1}else this.$message.warn("请输入禁用原因")}else this.$message.warn("请勾选要禁用的行")},removeContent:function(){var t=this.$refs.contentTable.getCheckboxRecords().map((function(t){return t.rid})),e=this.$refs.contentTable.getCheckboxRecords();if(0!=t.length){var a=e.filter((function(t){return!t.blnAdd}));a.length>0?this.$message.warn("只能移除未保存的记录"):(this.contentList=this.contentList.filter((function(e){return!t.includes(e.rid)})),this.contentListCache=this.contentListCache.filter((function(e){return!t.includes(e.rid)})),this.findMaxRid())}else this.$message.warn("请勾选要移除的行")},openImportContent:function(){this.importContentVisible=!0},onContentImportSuccess:function(t){var e,a=(0,r.Z)(t);try{for(a.s();!(e=a.n()).done;){var n=e.value;n.ykz042=this.clickedNode.ykz042,n.ykz010=this.clickedNode.ykz010,n.rid=++this.maxRid,n.blnAdd=!0,this.contentList.push(n)}}catch(o){a.e(o)}finally{a.f()}this.importContentVisible=!1},doSave:function(){var t=this.contentList.filter((function(t){return t.blnDelete})),e=this.contentList.filter((function(t){return t.blnAdd}));0!=t.length||0!=e.length?(this.disData=t,this.addData=e,this.reasonVisible=!0):this.$message.warn("没有需要保存的数据")},closeSaved:function(){this.getContentList(),this.reasonVisible=!1}}},B=M,q=(0,u.Z)(B,T,w,!1,null,"95674552",null),U=q.exports,Z=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{height:"100%"}},[a("ta-table",{ref:"mkf49Table",attrs:{size:"small",haveSn:{fixed:!0},columns:t.columnsMkf49,dataSource:t.dataMkf49,scroll:{y:330,x:"100%"},bordered:"","row-key":"ykz136",rowSelection:t.rowSelection,headerTitleNowrap:!0},on:{"update:columns":function(e){t.columnsMkf49=e}}}),a("ta-pagination",{ref:"mkf49Pager",staticClass:"page",attrs:{dataSource:t.dataMkf49,params:t.getPageParam,defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"],url:"mttRuleConfig/queryMkf49ByPage"},on:{"update:dataSource":function(e){t.dataMkf49=e},"update:data-source":function(e){t.dataMkf49=e}}})],1)},Y=[],J={name:"updateLog",components:{},props:{ykz042:{type:String}},data:function(){var t=[{dataIndex:"ykz136",title:"更新ID",width:100,align:"center"},{dataIndex:"ykz042",title:"节点编号",width:100,align:"center",fixed:"left"},{dataIndex:"ykz137",title:"更新原因",width:150,align:"center",overflowTooltip:!0},{dataIndex:"ykz138",title:"更新内容说明",width:150,align:"center",overflowTooltip:!0},{dataIndex:"operationlog",title:"操作说明",width:150,align:"center",overflowTooltip:!0},{dataIndex:"operationtype",title:"操作类型",width:100,align:"center"},{dataIndex:"operationuser",title:"操作人员",width:100,align:"center",overflowTooltip:!0},{dataIndex:"operationtime",title:"操作时间",width:200,align:"center"},{dataIndex:"ykz149",title:"是否参与打包",width:100,align:"center",collectionType:"MYKZ149"},{dataIndex:"ykz150",title:"设置人员",width:100,align:"center",overflowTooltip:!0},{dataIndex:"ykz151",title:"设置时间",width:200,align:"center"},{dataIndex:"ykz152",title:"设置原因",width:150,align:"center",overflowTooltip:!0},{dataIndex:"ykz163",title:"依据",width:150,align:"center",overflowTooltip:!0},{dataIndex:"itemver",title:"版本号",width:100,align:"center"},{dataIndex:"ykz001",title:"节点类型",width:100,align:"center"}];return{columnsMkf49:t,dataMkf49:[],selectedRows:[],rowSelection:{selectedRowKeys:[],onChange:this.onSelectChange},visible:!1}},mounted:function(){this.$refs.mkf49Table.hideColumns(["ykz136"]),this.$refs.mkf49Pager.loadData()},methods:{handleCancel:function(t){this.visible=!1},getPageParam:function(){return{ykz042:this.ykz042}},onSelectChange:function(t,e){this.rowSelection.selectedRowKeys=t,this.selectedRows=e},isPackage:function(t){var e=this;this.selectedRows.length<1?this.$message.warning("请选择数据"):0===t?this.visible=!0:this.$confirm({title:"确认参与导包？",onOk:function(){e.save(t)},onCancel:function(){}})},save:function(t){var e,a=this,n="",o=(0,r.Z)(this.selectedRows);try{for(o.s();!(e=o.n()).done;){var i=e.value;n+=i.ykz136+","}}catch(u){o.e(u)}finally{o.f()}var l="";l=0===t?this.form.getFieldValue("ykz152"):"默认打包";var s="ruleConfig/isPackage",d={url:s,data:{ykz136:n,ykz152:l,ykz149:t}},c={successCallback:function(t){t.data.success?(a.$message.success("更新完成"),a.visible=!1,a.$refs.mkf49Pager.loadData()):a.$message.error("更新失败")}};this.Base.submit("",d,c)}}},K=J,G=(0,u.Z)(K,Z,Y,!1,null,"58634aad",null),X=G.exports,j=function(){var t=this,e=this,a=e.$createElement,n=e._self._c||a;return n("div",{staticClass:"fit",attrs:{id:"templateMg"}},[n("ta-tooltip",{attrs:{placement:"left"}},[n("template",{slot:"title"},[e._v("返回模板管理页面")]),e.showIframe?n("span",{staticClass:"backTemplateMg",on:{click:function(t){e.showIframe=!1}}},[n("ta-icon",{attrs:{type:"rollback"}})],1):e._e()],2),e.showIframe?n("iframe",{staticStyle:{height:"99.9%",width:"100%"},attrs:{src:e.showIframe?e.urlIframe:"",frameborder:"0"}}):e._e(),n("ta-border-layout",{directives:[{name:"show",rawName:"v-show",value:!e.showIframe,expression:"!showIframe"}],attrs:{layout:{left:"300px"},leftCfg:{title:"模板分类",showBar:!0},"center-cfg":{layoutConStyle:{padding:"0px"}}}},[n("div",{attrs:{slot:"left"},slot:"left"},[n("ta-e-tree",{ref:"resourceTree",attrs:{"highlight-current":"","node-key":"id",props:e.defaultProps,load:e.loadTemplateCatalogTree,lazy:"","expand-on-click-node":!1},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.node,o=t.data;return n("span",{staticClass:"custom-tree-node"},[n("span",{staticClass:"custom-name",on:{click:function(t){return e.onTreeNodeClick(a)}}},[e._v(e._s(o.name))]),n("span",{staticClass:"tree-operate"},[n("a",{staticClass:"anticon anticon-plus-circle-o",on:{click:function(t){return e.fnAddTemplateCatalog(a,o)}}}),n("a",{staticClass:"anticon anticon-edit",on:{click:function(t){return e.fnEditTemplateCatalog(a,o)}}}),n("ta-popconfirm",{attrs:{title:"确认删除该功能模块?",cancelText:"取消",okText:"确认",okType:"default"},on:{confirm:function(t){return e.fnRemoveTemplateCatalog(a,o)}}},[n("ta-icon",{staticStyle:{color:"red"},attrs:{slot:"icon",type:"question-circle-o"},slot:"icon"}),n("a",{staticClass:"anticon anticon-delete",on:{click:function(t){e.deleteVisible=!0,e.deleteId=o.serviceNameId}}})],1)],1)])}}])})],1),n("ta-border-layout",{attrs:{layout:{header:"70px"},showBorder:!1,centerCfg:{showBar:!0,toolBarStyle:{height:"50px",lineHeight:"48px"}}}},[n("div",{staticClass:"center-box",attrs:{slot:"header"},slot:"header"},[n("ta-input-search",{staticClass:"search-box",attrs:{placeholder:"请输入模板名称或编码"},on:{search:e.fnSearchTemplate},model:{value:e.searchInfo,callback:function(t){e.searchInfo=t},expression:"searchInfo"}},[n("ta-button",{attrs:{slot:"enterButton",type:"primary"},slot:"enterButton"},[e._v("搜索")])],1)],1),n("div",{attrs:{slot:"centerExtraContent"},slot:"centerExtraContent"},[n("ta-tag-select",{attrs:{title:"有效标志",data:e.CollectionData("EFFECTIVE"),"is-multi":!1},on:{change:e.fnSearchTemplate},model:{value:e.templateEffective,callback:function(t){e.templateEffective=t},expression:"templateEffective"}}),n("ta-tag-select",{attrs:{title:"模板类型",data:e.CollectionData("TEMPLATETYPE"),"is-multi":!1},on:{change:e.fnSearchTemplate},model:{value:e.templateType,callback:function(t){e.templateType=t},expression:"templateType"}}),n("ta-tag-select",{attrs:{title:"模板来源",data:e.CollectionData("TEMPLATESOURCE"),"is-multi":!1},on:{change:e.fnSearchTemplate},model:{value:e.templateSource,callback:function(t){e.templateSource=t},expression:"templateSource"}}),n("div",{staticStyle:{float:"right"}},[n("ta-dropdown",[n("ta-menu",{attrs:{slot:"overlay"},on:{click:e.handleTemplateAdd},slot:"overlay"},[n("ta-menu-item",{key:"1"},[e._v(" code模板 ")]),n("ta-menu-item",{key:"2"},[e._v(" 表单模板 ")]),n("ta-menu-item",{key:"3"},[e._v(" 在线开发 ")])],1),n("ta-button",{staticStyle:{"margin-left":"8px"}},[e._v(" 新增模板 "),n("ta-icon",{attrs:{type:"down"}})],1)],1)],1)],1),n("ta-form",{attrs:{autoFormCreate:function(e){t.form=e}}},[n("ta-table",{attrs:{dataSource:e.templateData,columns:e.templateColumns,pagination:!1},scopedSlots:e._u([{key:"templateType",fn:function(t){return n("span",{},[e._v(e._s(e.CollectionLabel("TEMPLATETYPE",t)))])}},{key:"templateSource",fn:function(t){return n("span",{},[e._v(e._s(e.CollectionLabel("TEMPLATESOURCE",t)))])}},{key:"effective",fn:function(t){return n("span",{},[e._v(e._s(e.CollectionLabel("EFFECTIVE",t)))])}},{key:"templateName",fn:function(t,a){return n("ta-table-edit",{attrs:{type:"input"},on:{change:e.onCellChange}})}},{key:"operation",fn:function(t,a){return n("span",{},[n("ta-table-operate",{attrs:{operateMenu:e.operateMenu}})],1)}}])},[n("span",{attrs:{slot:"templateNameTitle"},slot:"templateNameTitle"},[e._v("模板名称 "),n("ta-icon",{attrs:{type:"edit"}})],1)])],1),n("ta-pagination",{ref:"templateGridPager",staticStyle:{float:"right","margin-top":"10px"},attrs:{showSizeChanger:"",showQuickJumper:"",dataSource:e.templateData,defaultPageSize:10,params:e.pageParams,url:"templateMg/templateMgRestService/queryTemplateByCondition"},on:{"update:dataSource":function(t){e.templateData=t},"update:data-source":function(t){e.templateData=t}}})],1),n("ta-modal",{attrs:{title:e.editName?"修改功能模块":"新建功能模块",visible:e.addTemplateCatalog,centered:"",destroyOnClose:""},on:{cancel:function(t){e.addTemplateCatalog=!1,e.editName=""},ok:e.fnSaveAddTemplateCatalog}},[n("ta-form",{attrs:{autoFormCreate:function(e){t.addTemplateCatalogForm=e}}},[n("ta-form-item",{attrs:{label:"功能模块名称",labelCol:{span:6},wrapperCol:{span:18},fieldDecoratorId:"name",fieldDecoratorOptions:{rules:[{required:!0,message:"功能模块名称为必输"}],initialValue:e.editName}}},[n("ta-input",{attrs:{placeholder:"请输入功能模块名称"}})],1)],1)],1),n("ta-modal",{attrs:{title:"新增code模板",visible:e.addTemplate,centered:"",destroyOnClose:""},on:{cancel:e.onCloseAddTemplate}},[n("template",{slot:"footer"},[n("ta-button",{on:{click:e.onCloseAddTemplate}},[e._v("取消")]),n("ta-button",{attrs:{type:"primary"},on:{click:e.fnSaveAddCodeTemplate}},[e._v("保存")])],1),n("ta-form",{attrs:{autoFormCreate:function(e){t.addTemplateForm=e}}},[n("ta-form-item",{attrs:{label:"code模板名称",labelCol:{span:6},wrapperCol:{span:18},fieldDecoratorId:"templateName",fieldDecoratorOptions:{rules:[{required:!0,message:"code模板名称为必输"}]}}},[n("ta-input",{attrs:{placeholder:"请输入code模板名称"}})],1)],1),n("ta-radio-group",{attrs:{options:[{label:"在线创建",value:"1"},{label:"本地上传",value:"2"}],defaultValue:"1"},on:{change:e.onRadioChange}}),e.isUpload?n("ta-upload",{attrs:{name:"uploadFile",customRequest:e.fnUploadTemplate,beforeUpload:e.fnBeforeUpload,accept:".vue",withCredentials:!0,fileList:e.fileList},on:{remove:e.fnRemoveFile}},[n("ta-button",[n("ta-icon",{attrs:{type:"upload"}}),e._v(" 选择文件 ")],1)],1):e._e()],2),n("ta-modal",{attrs:{title:"创建空白表单模板",visible:e.addFormTemplate,centered:"",destroyOnClose:"",okText:"确定",cancelText:"取消"},on:{cancel:function(t){e.addFormTemplate=!1},ok:e.fnSaveFormTemplate}},[n("ta-form",{attrs:{autoFormCreate:function(e){t.formTemplateForm=e}}},[n("ta-form-item",{attrs:{label:"表单模板名称",labelCol:{span:6},wrapperCol:{span:18},fieldDecoratorId:"formName",fieldDecoratorOptions:{rules:[{required:!0,message:"表单模板名称为必输"}]}}},[n("ta-input",{attrs:{placeholder:"请输入表单模板名称"}})],1)],1)],1),n("ta-modal",{attrs:{title:"创建在线开发模板",visible:e.addOnlineDev,centered:"",destroyOnClose:"",okText:"确定",cancelText:"取消"},on:{cancel:function(t){e.addOnlineDev=!1},ok:e.fnSaveOnlineDev}},[n("ta-form",{attrs:{autoFormCreate:function(e){t.onlineDevForm=e}}},[n("ta-form-item",{attrs:{label:"在线开发模板名称",labelCol:{span:8},wrapperCol:{span:16},fieldDecoratorId:"onlineDevName",fieldDecoratorOptions:{rules:[{required:!0,message:"模板名称为必输"}]}}},[n("ta-input",{attrs:{placeholder:"请输入模板名称"}})],1)],1)],1)],1)],1)},H=[],Q="templateMg/templateMgRestService/",W={loadTemplateCatalogTree:function(t,e){Base.submit(null,{url:Q+"loadTemplateCatalogTree",data:t},{successCallback:function(t){return e(t)}})},listTemplate:function(t,e){Base.submit(null,{url:Q+"queryTemplateByCatalogId",data:t},{successCallback:function(t){return e(t)}})},addTemplateCatalog:function(t,e){Base.submit(null,{url:Q+"addTemplateCatalog",data:t},{successCallback:function(t){return e(t)}})},updateTemplateCatalog:function(t,e){Base.submit(null,{url:Q+"updateTemplateCatalog",data:t},{successCallback:function(t){return e(t)}})},removeTemplateCatalog:function(t,e){Base.submit(null,{url:Q+"removeTemplateCatalog",data:t},{successCallback:function(t){return e(t)}})},addFormTemplate:function(t,e){Base.submit(null,{url:Q+"addOnlineForm",data:t},{successCallback:function(t){return e(t)}})},addOnlineDevTemplate:function(t,e){Base.submit(null,{url:Q+"addOnlineDev",data:t},{successCallback:function(t){return e(t)}})},addTemplate:function(t,e){Base.submit(null,{url:Q+"addTemplate",data:t},{successCallback:function(t){return e(t)}})},uploadTemplate:function(t,e){Base.submit(null,{url:Q+"uploadTemplate",data:t,isFormData:!0},{successCallback:function(t){return e(t)}})},removeTempalte:function(t,e){Base.submit(null,{url:Q+"removeTemplateByTemplateId",data:t,isFormData:!0},{successCallback:function(t){return e(t)}})},updateTemplateName:function(t,e){Base.submit(null,{url:Q+"updateTemplate",data:t,isFormData:!0},{successCallback:function(t){return e(t)}})}},tt=[{dataIndex:"templateName",width:300,slots:{title:"templateNameTitle"},scopedSlots:{customRender:"templateName"}},{title:"模板类型",dataIndex:"templateType",scopedSlots:{customRender:"templateType"}},{title:"模板来源",dataIndex:"templateSource",scopedSlots:{customRender:"templateSource"}},{title:"可用标志",dataIndex:"effective",yesOrNoTag:!0,scopedSlots:{customRender:"effective"}},{title:"操作",dataIndex:"operation",scopedSlots:{customRender:"operation"},align:"center"}],et={name:"templateMg",data:function(){var t=this;return{searchInfo:"",templateEffective:[],templateType:[],templateSource:[],templateColumns:tt,operateMenu:[{name:"编辑",onClick:function(e){t.fnEditTemplate(e)}},{name:"删除",type:"confirm",confirmTitle:"确认删除该页面模板?",onOk:function(e){t.fnRemoveTemplate(e)}}],templateData:[],addTemplateCatalog:!1,currentTemplateCatalog:{},addTemplate:!1,addFormTemplate:!1,addOnlineDev:!1,currentNode:{},resolveMap:{},currentFile:{},editName:"",isUpload:!1,fileList:[],showIframe:!1,urlIframe:"",defaultProps:{children:"children",label:"label",id:"id"}}},methods:{onCloseAddTemplate:function(){this.addTemplate=!1,this.fileList=[],this.currentFile={},this.isUpload=!1},onRadioChange:function(t){this.isUpload="1"!==t.target.value},backTemplateMg:function(){},fnSaveOnlineDev:function(){var t=this;this.onlineDevForm.validateFields((function(e){e||W.addOnlineDevTemplate({formName:t.onlineDevForm.getFieldValue("onlineDevName"),catalogId:t.currentNode.data.id},(function(e){t.$message.success("新增在线开发模板成功"),t.addOnlineDev=!1,t.fnSearchTemplate(),t.fnEditTemplate(e.data)}))}))},fnSaveAddCodeTemplate:function(){this.isUpload?this.fnUploadTemplate():this.fnSaveAddTemplate()},fnSaveFormTemplate:function(){var t=this;this.formTemplateForm.validateFields((function(e){e||W.addFormTemplate({formName:t.formTemplateForm.getFieldValue("formName"),catalogId:t.currentNode.data.id},(function(e){t.$message.success("新增表单模板成功"),t.addFormTemplate=!1,t.fnSearchTemplate(),t.fnEditTemplate(e.data)}))}))},handleTemplateAdd:function(t){t.item;var e=t.key;if(this.currentNode.data)switch(e){case"1":this.addTemplate=!0;break;case"2":this.addFormTemplate=!0;break;case"3":this.addOnlineDev=!0;break}else this.$message.warn("请先在左侧功能列表中选择一个功能")},loadTemplateCatalogTree:function(t,e){var a=this;0===t.level&&e([{name:"功能列表",id:"0",idPath:"0",namePath:"功能列表"}]),t.level>=1&&W.loadTemplateCatalogTree({parentId:t.data.id},(function(n){a.resolveMap[t.data.id]=e,n.data.treeData?e(n.data.treeData):e([])}))},onTreeNodeClick:function(t){this.currentNode=t,this.fnSearchTemplate()},fnSearchTemplate:function(){this.$refs.templateGridPager.loadData()},pageParams:function(){return{catalogId:this.currentNode.data?this.currentNode.data.id:"",templateName:this.searchInfo,effective:this.templateEffective[0],templateType:this.templateType[0],templateSource:this.templateSource[0]}},fnAddTemplateCatalog:function(t,e){this.currentTemplateCatalog=e,this.addTemplateCatalog=!0},fnEditTemplateCatalog:function(t,e){this.currentTemplateCatalog=e,this.addTemplateCatalog=!0,this.editName=e.name},fnSaveAddTemplateCatalog:function(){var t=this,e=this.addTemplateCatalogForm.getFieldValue("name"),a={name:e,parentId:this.currentTemplateCatalog.id,idPath:this.currentTemplateCatalog.idPath,namePath:this.currentTemplateCatalog.namePath+"/"+e};this.editName?a.name==this.editName?(this.addTemplateCatalog=!1,this.editName=""):W.updateTemplateCatalog({name:a.name,id:a.parentId},(function(e){t.$message.success("修改模板功能名称成功"),t.addTemplateCatalog=!1,t.editName="",t.currentTemplateCatalog.name=a.name,t.refreshLazyTree()})):W.addTemplateCatalog(a,(function(e){t.$message.success("新增功能模块成功"),t.addTemplateCatalog=!1,t.editName="",t.refreshLazyTree()}))},fnRemoveTemplateCatalog:function(t,e){var a=this;W.removeTemplateCatalog({id:e.id},(function(e){a.$message.success("移除功能模块成功"),a.fnSearchTemplate(),a.refreshParentLazyTree(t)}))},fnSaveAddTemplate:function(){var t=this;this.addTemplateForm.validateFields((function(e){e||W.addTemplate({templateName:t.addTemplateForm.getFieldValue("templateName"),catalogId:t.currentNode.data.id},(function(e){t.$message.success("新增模板成功"),t.fnSearchTemplate(),t.onCloseAddTemplate(),t.fnEditTemplate(e.data)}))}))},fnRemoveTemplate:function(t){var e=this;W.removeTempalte({templateId:t.templateId},(function(t){e.$message.success("删除模板成功"),e.fnSearchTemplate()}))},fnEditTemplate:function(t){switch(t.templateType){case"1":this.showIframe=!0,this.urlIframe="onlineCodeDesign.html#/onlineCode?formId="+t.templateId;break;case"2":this.showIframe=!0,this.urlIframe="formDesign.html?formId="+t.templateId;break;case"3":this.showIframe=!0,this.urlIframe="onlineDesign.html#/?formId="+t.templateId;break}},fnBeforeUpload:function(t,e){return this.currentFile=t,this.fileList=e,!1},fnRemoveFile:function(){this.fileList=[],this.currentFile={}},fnUploadTemplate:function(){var t=this;this.fileList.length<1?this.$message.warn("请先选择文件！"):this.addTemplateForm.validateFields((function(e){e||W.uploadTemplate({uploadFile:t.currentFile,templateName:t.addTemplateForm.getFieldValue("templateName"),catalogId:t.currentNode.data.id},(function(e){t.fnSearchTemplate(),t.$message.success("上传成功"),t.onCloseAddTemplate(),t.fnEditTemplate(e.data)}))}))},refreshLazyTree:function(){this.currentNode.childNodes=[],"function"===typeof this.resolveMap[this.currentTemplateCatalog.id]&&this.loadTemplateCatalogTree(this.currentNode,this.resolveMap[this.currentTemplateCatalog.id])},refreshParentLazyTree:function(t){t.parent.childNodes=[],"function"===typeof this.resolveMap[t.parent.data.id]&&this.loadTemplateCatalogTree(t.parent,this.resolveMap[t.parent.data.id])},onCellChange:function(t){var e=this,a=t.newData,n=(t.columnKey,t.record);W.updateTemplateName({templateName:a,templateId:n.templateId},(function(){e.$message.success("修改模板名称成功"),e.fnSearchTemplate()}))}},activated:function(){}},at=et,nt=(0,u.Z)(at,j,H,!1,null,"2436296e",null),ot=nt.exports,it={name:"nodeData",props:{blnPlatform:Boolean},components:{TemplateMg:ot,nodeContent:U,updateLog:X},computed:{pageUrl:function(){return"mttRuleConfig/pageNodeData"},mkeDownloadUrl:function(){return"mttRuleConfig/downloadMke"}},data:function(){return{nodeColumn:[{dataIndex:"ykz010",title:"节点名称",width:150,align:"center",overflowTooltip:!0},{dataIndex:"ykz042",title:"节点编码",width:150,align:"center",overflowTooltip:!0},{dataIndex:"ykz065",title:"节点说明",width:250,align:"center",overflowTooltip:!0},{dataIndex:"needsync",title:"同步引擎",width:100,align:"center",overflowTooltip:!0,scopedSlots:{customRender:"needsync"}},{dataIndex:"relative",title:"对照说明",width:100,align:"center",overflowTooltip:!0,scopedSlots:{customRender:"relative"}},{dataIndex:"operate",title:"操作",width:200,align:"center",overflowTooltip:!0,scopedSlots:{customRender:"operate"}},{dataIndex:"operationuser",title:"对照人",width:150,align:"center",overflowTooltip:!0},{dataIndex:"operationtime",title:"对照时间",width:160,align:"center",overflowTooltip:!0},{dataIndex:"log",title:"查看记录",width:100,align:"center",overflowTooltip:!0,scopedSlots:{customRender:"log"}}],nodeData:[],pageParam:{pageNumber:1},searchParam:{},visible:!1,ykz072:"",nodeInfos:{},reload:!0,logVisible:!1,blnNodeContentVisible:!1,clickNode:{}}},watch:{blnNodeContentVisible:function(t){t||this.$refs.pager.loadData()}},methods:{syncPackage:function(t){var e=this;"1"==t.relative?Base.submit(null,{url:"mttRulePublish/syncPackage",data:{ykz042:t.ykz042,ykz001:this.searchParam.ykz001}}).then((function(t){e.$message.success("同步成功"),e.$refs.pager.loadData()})):this.$message.warn("包体没有数据！请配置")},nodeParam:function(){return(0,i.Z)({},this.searchParam)},loadData:function(t){this.pageParam.pageNumber=1,this.searchParam=t,this.$refs.pager.loadData()},getNodeData:function(){return this.nodeData},reloadChild:function(){var t=this;this.reload=!1,this.$nextTick((function(){t.reload=!0}))},handleCancel:function(t){this.logVisible=!1},handleNodeCancel:function(t){this.visible=!1,this.$refs.pager.loadData()},configNode:function(t){this.clickNode=t,this.clickNode.ykz001=this.searchParam.ykz001,this.blnNodeContentVisible=!0},nodeLog:function(t){this.nodeInfos=t,this.logVisible=!0},downloadMke:function(t){var e=this;Base.submit(null,{url:this.mkeDownloadUrl,data:{ykz001:this.searchParam.ykz001,ykz042:t.ykz042}}).then((function(a){if(a.data.success){var n=t.ykz042+"-包体内容.xlsx",o=a.data.data,i=document.createElement("a");i.download=n,i.href="data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,"+o,i.click()}else e.$message.error("下载数据失败")}))}}},lt=it,st=(0,u.Z)(lt,k,C,!1,null,"1778d74c",null),rt=st.exports,dt={name:"nodeContent",components:{nodeTypeList:f,nodeSearch:b,nodeData:rt,importData:I,nodeContent:U},data:function(){return{searchParam:{},ykz001:null,importVisible:!1,blnAddNodeVisible:!1,addNodeConfStyle:{nodeConfSpan:23}}},mounted:function(){},methods:{openAddNode:function(){this.blnAddNodeVisible=!0},loadData:function(){this.$refs.nodeData.loadData(this.searchParam)},nodeTypeSelect:function(t){this.ykz001=t,this.searchParam.ykz001=t,this.loadData()},addNode:function(){var t=this;this.nodeConfForm.validateFields((function(e){if(!e){var a=t.nodeConfForm.getFieldsValue();Base.submit(null,{url:"mttRuleConfig/addNode",data:{dtoStr:JSON.stringify((0,i.Z)({ykz001:t.ykz001},a))}}).then((function(e){t.$message.success("新增成功"),t.blnAddNodeVisible=!1,t.onSearch()}))}}))},onSearch:function(){var t=this.searchForm.getFieldsValue(),e=this.$refs.nodeData.getNodeData();null!=this.searchParam.ykz001||0!==e.length?(this.searchParam=t,this.searchParam.ykz001=this.ykz001,this.loadData()):this.$message.warn("请先选择节点类型")},onDownloadTemplate:function(t){var e=this,a=this.$refs.nodeData.getNodeData();if(null!=this.searchParam.ykz001||0!==a.length){var n={url:"mttRuleConfig/downloadContentTemplate",data:{ykz001:this.ykz001},autoValid:!0},o={successCallback:function(t){if(t.data.success){var a=t.data.fileName,n=t.data.data,o=document.createElement("a");o.download=a,o.href="data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,"+n,o.click(),e.$message.success("下载成功")}else e.$message.error(t.errors[0].msg)}};this.Base.submit("",n,o)}else this.$message.warn("请先选择节点类型")},onExportData:function(t){var e=this,a=this.$refs.nodeData.getNodeData();null!=this.searchParam.ykz001||0!==a.length?(this.searchParam=t,this.searchParam.ykz001=this.ykz001,Base.submit(null,{url:"nodeContent/exportData",data:(0,i.Z)({},this.searchParam)}).then((function(t){if(t.data.success){var a=t.data.fileName,n=t.data.data,o=document.createElement("a");o.download=a,o.href="data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,"+n,o.click()}else e.$message.error("下载规则路径数据失败")}))):this.$message.warn("请先选择节点类型")},onImportData:function(){null!=this.searchParam.ykz001?this.importVisible=!0:this.$message.warn("请先选择节点类型")},onImportSuccess:function(){this.importVisible=!1,this.loadData()}}},ct=dt,ut=(0,u.Z)(ct,n,o,!1,null,"fd23087e",null),mt=ut.exports}}]);
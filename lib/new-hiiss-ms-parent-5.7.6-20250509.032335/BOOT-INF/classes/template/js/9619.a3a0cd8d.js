"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[9619],{88412:function(t,a,e){var i=e(26263),s=e(36766),l=e(1001),n=(0,l.Z)(s.Z,i.s,i.x,!1,null,"5e7ef0ae",null);a["Z"]=n.exports},49619:function(t,a,e){e.r(a),e.d(a,{default:function(){return f}});var i=function(){var t=this,a=this,i=a.$createElement,s=a._self._c||i;return s("div",{staticClass:"fit"},[s("ta-border-layout",{attrs:{layout:{header:"90px"},showBorder:!1,showPadding:!1,leftCfg:{showBorder:!1}}},[s("div",{attrs:{slot:"header"},slot:"header"},[s("ta-title",{attrs:{title:"查询条件"}}),s("ta-form",{staticStyle:{position:"absolute",width:"100%"},attrs:{autoFormCreate:function(a){return t.queryForm=a},layout:"horizontal",formLayout:!0}},[s("ta-form-item",{attrs:{fieldDecoratorId:"date",labelCol:{span:8},wrapperCol:{span:16},span:8}},[s("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[a._v("入院日期")]),s("ta-range-picker",{staticStyle:{width:"105%"},attrs:{disabledDate:a.disabledDate}})],1),"1"==this.showObj?s("ta-form-item",{attrs:{fieldDecoratorId:"akc190",labelCol:{span:8},wrapperCol:{span:16},span:8}},[s("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[a._v("住院号")]),s("ta-input",{staticStyle:{width:"105%"}})],1):a._e(),s("ta-form-item",{attrs:{fieldDecoratorId:"aaz307",labelCol:{span:8},wrapperCol:{span:16},span:8}},[s("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[a._v("科室")]),s("ta-select",{staticStyle:{width:"105%"},attrs:{options:a.ksSelectData}})],1),s("ta-button",{staticStyle:{"margin-left":"calc(12.5%)"},attrs:{icon:"search",type:"primary"},on:{click:function(t){return a.handleQuery()}}},[a._v("查询")])],1)],1),s("div",{staticClass:"fit",staticStyle:{position:"absolute",width:"100%"}},[s("ta-title",{attrs:{title:a.title}},[s("ta-pagination",{ref:"cardDataPageRef",staticStyle:{float:"right"},attrs:{showSizeChanger:"",showQuickJumper:"",dataSource:a.cardData,params:a.cardDataPageParams,defaultPageSize:12,pageSizeOptions:["12"],url:"informationAggregation/queryCardData"},on:{"update:dataSource":function(t){a.cardData=t},"update:data-source":function(t){a.cardData=t}}})],1),s("div",{staticStyle:{display:"flex","justify-content":"space-between","flex-wrap":"wrap",height:"calc(100% - 35px)"}},[a._l(a.cardData,(function(t,i){return s("ta-card",{key:(new Date).getTime()+""+i,staticStyle:{width:"24%",height:"33%"}},[s("div",{staticStyle:{display:"flex","justify-content":"space-between"},attrs:{slot:"title"},slot:"title"},[s("span",{staticStyle:{"font-family":"Arial","font-size":"18px"}},[a._v(a._s(t.aae386))]),s("span",{staticStyle:{"font-family":"Arial","font-size":"16px"}},[a._v(a._s(t.jyrc)+"人")])]),s("div",{staticStyle:{height:"110px"}},[s("div",{staticStyle:{height:"100%",width:"30%",float:"left"}},[s("img",{staticStyle:{width:"100%",height:"80%"},attrs:{src:e(97554)}})]),s("div",{staticStyle:{height:"80%",width:"65%",display:"flex","flex-direction":"column","justify-content":"space-between",float:"right"}},[s("span",[a._v("市城职: "+a._s(t.scz))]),s("span",[a._v("市城乡: "+a._s(t.scx))]),s("span",[a._v("省医保: "+a._s(t.syb))])])])])})),a._l(a.cardData.length%4,(function(t){return s("div",{key:(new Date).getTime()+"bc"+t,staticStyle:{width:"24%",height:"33%"}})}))],2)],1)])],1)},s=[],l=e(88412),n=e(36797),r=e.n(n),o={name:"ksInformation",components:{TaTitle:l.Z},data:function(){return{cardData:[],showObj:"",ksSelectData:[],title:"科室信息"}},methods:{disabledDate:function(t){return t>r()().startOf("day")},cardDataPageParams:function(){var t=this.queryForm.getFieldsValue();return null!=t.date&&t.date.length>0&&(t.aae030s=t.date[0].format("YYYY-MM-DD"),t.aae030e=t.date[1].format("YYYY-MM-DD"),delete t.date),t.showObj=this.showObj,t},handleQuery:function(){this.$refs.cardDataPageRef.loadData()},queryKsSelectData:function(){var t=this;this.Base.submit(null,{url:"informationAggregation/queryKsSelectData",data:{},autoValid:!0},{successCallback:function(a){t.ksSelectData=a.data.ksSelectData},failCallback:function(a){t.$message.error("获取科室下拉框数据失败!")}})},fnInitData:function(){this.queryKsSelectData()}},beforeMount:function(){this.showObj="3"},mounted:function(){this.fnInitData()}},c=o,u=e(1001),d=(0,u.Z)(c,i,s,!1,null,"16dcbc3d",null),f=d.exports},36766:function(t,a,e){var i=e(66586);a["Z"]=i.Z},26263:function(t,a,e){e.d(a,{s:function(){return i},x:function(){return s}});var i=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("h3",{staticStyle:{"font-weight":"normal"}},[e("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),e("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},s=[]},66586:function(t,a){var e={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};a["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:e}}}},97554:function(t,a,e){t.exports=e.p+"img/ks.b6093bad.jpeg"}}]);
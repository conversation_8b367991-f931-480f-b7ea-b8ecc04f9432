"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[9793],{99793:function(t,e,a){a.r(e),a.d(e,{default:function(){return b}});var o=function(){var t=this,e=this,a=e.$createElement,o=e._self._c||a;return o("ta-border-layout",{attrs:{"layout-type":"fixTop"}},[o("ta-card",{attrs:{slot:"header",title:"查询条件"},slot:"header"},[o("ta-form",{attrs:{layout:"horizontal","form-layout":!0,"auto-form-create":function(e){t.form=e}}},[o("ta-form-item",{key:"num",attrs:{label:"业务编号","field-decorator-id":"num",require:!0}},[o("ta-input")],1),o("ta-form-item",{key:"type",attrs:{label:"申办类型","field-decorator-id":"type","init-value":"0"}},[o("ta-select",[o("ta-select-option",{attrs:{value:"0"}},[e._v(" 单位 ")]),o("ta-select-option",{attrs:{value:"1"}},[e._v(" 个人 ")]),o("ta-select-option",{attrs:{value:"2"}},[e._v(" 其他 ")])],1)],1),e.expand?o("ta-form-item",{key:"name",attrs:{label:"经办人","field-decorator-id":"name"}},[o("ta-input")],1):e._e(),e.expand?o("ta-form-item",{key:"address",attrs:{label:"经办地点","field-decorator-id":"address"}},[o("ta-input")],1):e._e(),e.expand?o("ta-form-item",{key:"more",attrs:{label:"备注","field-decorator-id":"more"}},[o("ta-input")],1):e._e(),o("ta-form-item",{attrs:{label:":"}},[o("div",{staticStyle:{width:"100%","text-align":"right"}},[o("ta-button",{on:{click:e.expandFn}},[e._v(" "+e._s(1==e.expand?"收起":"展开")+" ")]),o("ta-button",{on:{click:e.resetFn}},[e._v(" 重置 ")]),o("ta-button",{attrs:{type:"primary"},on:{click:e.searchFn}},[e._v(" 查询 ")])],1)])],1)],1),o("ta-big-table",{ref:"searchList",attrs:{border:"",stripe:"",resizable:"","auto-resize":"","highlight-hover-row":"",height:"100%","export-config":{},"import-config":{}},scopedSlots:e._u([{key:"topBar",fn:function(){return[o("ta-big-table-toolbar",{attrs:{import:!0,export:!0,print:!0,custom:!0}},[o("div",{attrs:{slot:"buttons"},slot:"buttons"},[o("span",{staticStyle:{"font-size":"16px"}},[e._v("查询表格")])]),o("div",{attrs:{slot:"tools"},slot:"tools"},[o("ta-button",[e._v("其他工具")]),o("ta-button",[e._v("其他功能")])],1)])]},proxy:!0},{key:"bottomBar",fn:function(){return[o("ta-pagination",{ref:"gridPager",staticStyle:{"text-align":"right"},attrs:{url:"http/mock/projectDemo/searchTableQuery",params:e.userPageParams},on:{loaded:e.handleLoaded}})]},proxy:!0}])},[o("ta-big-table-column",{attrs:{type:"seq",width:"60"}}),o("ta-big-table-column",{attrs:{field:"no",title:"规则编号",sortable:""}}),o("ta-big-table-column",{attrs:{field:"description",title:"描述"}}),o("ta-big-table-column",{attrs:{field:"callNo",title:"服务调用次数"}}),o("ta-big-table-column",{attrs:{field:"status",title:"状态"}}),o("ta-big-table-column",{attrs:{field:"updatedAt",title:"更新时间"}}),o("ta-big-table-column",{attrs:{fixed:"right",field:"operate",title:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){return o("span",{},[o("ta-table-operate",{attrs:{"operate-menu":e.operateMenu,"row-info":t}})],1)}}])})],1),e.modalVisible?o("ta-modal",{attrs:{visible:e.modalVisible,title:"弹出框中显示的表格数据","destroy-on-close":!0},on:{ok:e.handleOk,cancel:e.handleCancel}},[o("form-part",{attrs:{"custom-data":e.customData}})],1):e._e(),e.drawerVisible?o("ta-drawer",{attrs:{visible:e.drawerVisible,title:"抽屉中显示的表格数据",width:"350px","mask-closable":!0},on:{close:e.handleCancel}},[o("form-part",{attrs:{"custom-data":e.customData}})],1):e._e()],1)},i=[],r=function(){var t=this,e=this,a=e.$createElement,o=e._self._c||a;return o("ta-form",{attrs:{"auto-form-create":function(e){return t.form1=e},title:"抽屉"}},[o("ta-form-item",{attrs:{label:"规则编号","field-decorator-id":"no"}},[o("ta-input")],1),o("ta-form-item",{attrs:{label:"描述","field-decorator-id":"description"}},[o("ta-input")],1),o("ta-form-item",{attrs:{label:"更新时间","field-decorator-id":"updatedAt"}},[o("ta-input")],1)],1)},l=[],n={name:"formPart",props:{customData:{type:Object}},mounted:function(){var t=this;this.$nextTick((function(){t.form1.setFieldsValue(t.customData)}))}},s=n,d=a(1001),u=(0,d.Z)(s,r,l,!1,null,"3a3dda9a",null),c=u.exports,m={name:"searchTable",components:{formPart:c},data:function(){var t=this;return{expand:!1,tableData:[],operateMenu:[{name:"打开弹出框",icon:"setting",onClick:function(e){t.customData=e,t.modalVisible=!0}},{name:"打开抽屉",icon:"setting",onClick:function(e){t.customData=e,t.drawerVisible=!0}}],modalVisible:!1,drawerVisible:!1,customData:{}}},mounted:function(){this.$refs.gridPager.loadData((function(t){}))},methods:{handleLoaded:function(t){this.$refs.searchList.loadData(t.data.pageBean.list)},expandFn:function(){this.expand=!this.expand},resetFn:function(){this.form.resetFields()},searchFn:function(){},userPageParams:function(){return this.form.getFieldsValue()},handleOk:function(){this.modalVisible=!1,this.customData={}},handleCancel:function(){this.modalVisible=!1,this.drawerVisible=!1,this.customData={}}}},f=m,p=(0,d.Z)(f,o,i,!1,null,null,null),b=p.exports}}]);
(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[6258],{39507:function(t,e,r){var i=r(57847)["default"];r(39575),r(38012),r(16716);var n={userAgent:!1},s={},a=a||function(t,e){var r={},i=r.lib={},n=i.Base=function(){function t(){}return{extend:function(e){t.prototype=this;var r=new t;return e&&r.mixIn(e),r.hasOwnProperty("init")||(r.init=function(){r.$super.init.apply(this,arguments)}),r.init.prototype=r,r.$super=this,r},create:function(){var t=this.extend();return t.init.apply(t,arguments),t},init:function(){},mixIn:function(t){for(var e in t)t.hasOwnProperty(e)&&(this[e]=t[e]);t.hasOwnProperty("toString")&&(this.toString=t.toString)},clone:function(){return this.init.prototype.extend(this)}}}(),s=i.WordArray=n.extend({init:function(t,r){t=this.words=t||[],this.sigBytes=r!=e?r:4*t.length},toString:function(t){return(t||o).stringify(this)},concat:function(t){var e=this.words,r=t.words,i=this.sigBytes,n=t.sigBytes;if(this.clamp(),i%4)for(var s=0;s<n;s++){var a=r[s>>>2]>>>24-s%4*8&255;e[i+s>>>2]|=a<<24-(i+s)%4*8}else for(s=0;s<n;s+=4)e[i+s>>>2]=r[s>>>2];return this.sigBytes+=n,this},clamp:function(){var e=this.words,r=this.sigBytes;e[r>>>2]&=4294967295<<32-r%4*8,e.length=t.ceil(r/4)},clone:function(){var t=n.clone.call(this);return t.words=this.words.slice(0),t},random:function(e){for(var r=[],i=0;i<e;i+=4)r.push(4294967296*t.random()|0);return new s.init(r,e)}}),a=r.enc={},o=a.Hex={stringify:function(t){for(var e=t.words,r=t.sigBytes,i=[],n=0;n<r;n++){var s=e[n>>>2]>>>24-n%4*8&255;i.push((s>>>4).toString(16)),i.push((15&s).toString(16))}return i.join("")},parse:function(t){for(var e=t.length,r=[],i=0;i<e;i+=2)r[i>>>3]|=parseInt(t.substr(i,2),16)<<24-i%8*4;return new s.init(r,e/2)}},h=a.Latin1={stringify:function(t){for(var e=t.words,r=t.sigBytes,i=[],n=0;n<r;n++){var s=e[n>>>2]>>>24-n%4*8&255;i.push(String.fromCharCode(s))}return i.join("")},parse:function(t){for(var e=t.length,r=[],i=0;i<e;i++)r[i>>>2]|=(255&t.charCodeAt(i))<<24-i%4*8;return new s.init(r,e)}},u=a.Utf8={stringify:function(t){try{return decodeURIComponent(escape(h.stringify(t)))}catch(e){throw new Error("Malformed UTF-8 data")}},parse:function(t){return h.parse(unescape(encodeURIComponent(t)))}},c=i.BufferedBlockAlgorithm=n.extend({reset:function(){this._data=new s.init,this._nDataBytes=0},_append:function(t){"string"==typeof t&&(t=u.parse(t)),this._data.concat(t),this._nDataBytes+=t.sigBytes},_process:function(e){var r=this._data,i=r.words,n=r.sigBytes,a=this.blockSize,o=4*a,h=n/o;h=e?t.ceil(h):t.max((0|h)-this._minBufferSize,0);var u=h*a,c=t.min(4*u,n);if(u){for(var l=0;l<u;l+=a)this._doProcessBlock(i,l);var f=i.splice(0,u);r.sigBytes-=c}return new s.init(f,c)},clone:function(){var t=n.clone.call(this);return t._data=this._data.clone(),t},_minBufferSize:0}),l=(i.Hasher=c.extend({cfg:n.extend(),init:function(t){this.cfg=this.cfg.extend(t),this.reset()},reset:function(){c.reset.call(this),this._doReset()},update:function(t){return this._append(t),this._process(),this},finalize:function(t){t&&this._append(t);var e=this._doFinalize();return e},blockSize:16,_createHelper:function(t){return function(e,r){return new t.init(r).finalize(e)}},_createHmacHelper:function(t){return function(e,r){return new l.HMAC.init(t,r).finalize(e)}}}),r.algo={});return r}(Math);(function(t){var e=a,r=e.lib,i=r.Base,n=r.WordArray;e=e.x64={};e.Word=i.extend({init:function(t,e){this.high=t,this.low=e}}),e.WordArray=i.extend({init:function(e,r){e=this.words=e||[],this.sigBytes=r!=t?r:8*e.length},toX32:function(){for(var t=this.words,e=t.length,r=[],i=0;i<e;i++){var s=t[i];r.push(s.high),r.push(s.low)}return n.create(r,this.sigBytes)},clone:function(){for(var t=i.clone.call(this),e=t.words=this.words.slice(0),r=e.length,n=0;n<r;n++)e[n]=e[n].clone();return t}})})(),a.lib.Cipher||function(t){var e=a,r=e.lib,i=r.Base,n=r.WordArray,s=r.BufferedBlockAlgorithm,o=e.enc.Base64,h=e.algo.EvpKDF,u=r.Cipher=s.extend({cfg:i.extend(),createEncryptor:function(t,e){return this.create(this._ENC_XFORM_MODE,t,e)},createDecryptor:function(t,e){return this.create(this._DEC_XFORM_MODE,t,e)},init:function(t,e,r){this.cfg=this.cfg.extend(r),this._xformMode=t,this._key=e,this.reset()},reset:function(){s.reset.call(this),this._doReset()},process:function(t){return this._append(t),this._process()},finalize:function(t){return t&&this._append(t),this._doFinalize()},keySize:4,ivSize:4,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,_createHelper:function(t){return{encrypt:function(e,r,i){return("string"==typeof r?d:p).encrypt(t,e,r,i)},decrypt:function(e,r,i){return("string"==typeof r?d:p).decrypt(t,e,r,i)}}}});r.StreamCipher=u.extend({_doFinalize:function(){return this._process(!0)},blockSize:1});var c=e.mode={},l=function(e,r,i){var n=this._iv;n?this._iv=t:n=this._prevBlock;for(var s=0;s<i;s++)e[r+s]^=n[s]},f=(r.BlockCipherMode=i.extend({createEncryptor:function(t,e){return this.Encryptor.create(t,e)},createDecryptor:function(t,e){return this.Decryptor.create(t,e)},init:function(t,e){this._cipher=t,this._iv=e}})).extend();f.Encryptor=f.extend({processBlock:function(t,e){var r=this._cipher,i=r.blockSize;l.call(this,t,e,i),r.encryptBlock(t,e),this._prevBlock=t.slice(e,e+i)}}),f.Decryptor=f.extend({processBlock:function(t,e){var r=this._cipher,i=r.blockSize,n=t.slice(e,e+i);r.decryptBlock(t,e),l.call(this,t,e,i),this._prevBlock=n}}),c=c.CBC=f,f=(e.pad={}).Pkcs7={pad:function(t,e){for(var r=4*e,i=(r=r-t.sigBytes%r,r<<24|r<<16|r<<8|r),s=[],a=0;a<r;a+=4)s.push(i);r=n.create(s,r),t.concat(r)},unpad:function(t){t.sigBytes-=255&t.words[t.sigBytes-1>>>2]}},r.BlockCipher=u.extend({cfg:u.cfg.extend({mode:c,padding:f}),reset:function(){u.reset.call(this);var t=this.cfg,e=t.iv;t=t.mode;if(this._xformMode==this._ENC_XFORM_MODE)var r=t.createEncryptor;else r=t.createDecryptor,this._minBufferSize=1;this._mode=r.call(t,this,e&&e.words)},_doProcessBlock:function(t,e){this._mode.processBlock(t,e)},_doFinalize:function(){var t=this.cfg.padding;if(this._xformMode==this._ENC_XFORM_MODE){t.pad(this._data,this.blockSize);var e=this._process(!0)}else e=this._process(!0),t.unpad(e);return e},blockSize:4});var g=r.CipherParams=i.extend({init:function(t){this.mixIn(t)},toString:function(t){return(t||this.formatter).stringify(this)}}),p=(c=(e.format={}).OpenSSL={stringify:function(t){var e=t.ciphertext;return t=t.salt,(t?n.create([1398893684,1701076831]).concat(t).concat(e):e).toString(o)},parse:function(t){t=o.parse(t);var e=t.words;if(1398893684==e[0]&&1701076831==e[1]){var r=n.create(e.slice(2,4));e.splice(0,4),t.sigBytes-=16}return g.create({ciphertext:t,salt:r})}},r.SerializableCipher=i.extend({cfg:i.extend({format:c}),encrypt:function(t,e,r,i){i=this.cfg.extend(i);var n=t.createEncryptor(r,i);return e=n.finalize(e),n=n.cfg,g.create({ciphertext:e,key:r,iv:n.iv,algorithm:t,mode:n.mode,padding:n.padding,blockSize:t.blockSize,formatter:i.format})},decrypt:function(t,e,r,i){return i=this.cfg.extend(i),e=this._parse(e,i.format),t.createDecryptor(r,i).finalize(e.ciphertext)},_parse:function(t,e){return"string"==typeof t?e.parse(t,this):t}})),d=(e=(e.kdf={}).OpenSSL={execute:function(t,e,r,i){return i||(i=n.random(8)),t=h.create({keySize:e+r}).compute(t,i),r=n.create(t.words.slice(e),4*r),t.sigBytes=4*e,g.create({key:t,iv:r,salt:i})}},r.PasswordBasedCipher=p.extend({cfg:p.cfg.extend({kdf:e}),encrypt:function(t,e,r,i){return i=this.cfg.extend(i),r=i.kdf.execute(r,t.keySize,t.ivSize),i.iv=r.iv,t=p.encrypt.call(this,t,e,r.key,i),t.mixIn(r),t},decrypt:function(t,e,r,i){return i=this.cfg.extend(i),e=this._parse(e,i.format),r=i.kdf.execute(r,t.keySize,t.ivSize,e.salt),i.iv=r.iv,p.decrypt.call(this,t,e,r.key,i)}}))}(),function(){for(var t=a,e=t.lib.BlockCipher,r=t.algo,i=[],n=[],s=[],o=[],h=[],u=[],c=[],l=[],f=[],g=[],p=[],d=0;256>d;d++)p[d]=128>d?d<<1:d<<1^283;var v=0,m=0;for(d=0;256>d;d++){var y=m^m<<1^m<<2^m<<3^m<<4;y=y>>>8^255&y^99;i[v]=y,n[y]=v;var x=p[v],S=p[x],w=p[S],E=257*p[y]^16843008*y;s[v]=E<<24|E>>>8,o[v]=E<<16|E>>>16,h[v]=E<<8|E>>>24,u[v]=E,E=16843009*w^65537*S^257*x^16843008*v,c[y]=E<<24|E>>>8,l[y]=E<<16|E>>>16,f[y]=E<<8|E>>>24,g[y]=E,v?(v=x^p[p[p[w^x]]],m^=p[p[m]]):v=m=1}var F=[0,1,2,4,8,16,32,64,128,27,54];r=r.AES=e.extend({_doReset:function(){for(var t=this._key,e=t.words,r=t.sigBytes/4,n=(t=4*((this._nRounds=r+6)+1),this._keySchedule=[]),s=0;s<t;s++)if(s<r)n[s]=e[s];else{var a=n[s-1];s%r?6<r&&4==s%r&&(a=i[a>>>24]<<24|i[a>>>16&255]<<16|i[a>>>8&255]<<8|i[255&a]):(a=a<<8|a>>>24,a=i[a>>>24]<<24|i[a>>>16&255]<<16|i[a>>>8&255]<<8|i[255&a],a^=F[s/r|0]<<24),n[s]=n[s-r]^a}for(e=this._invKeySchedule=[],r=0;r<t;r++)s=t-r,a=r%4?n[s]:n[s-4],e[r]=4>r||4>=s?a:c[i[a>>>24]]^l[i[a>>>16&255]]^f[i[a>>>8&255]]^g[i[255&a]]},encryptBlock:function(t,e){this._doCryptBlock(t,e,this._keySchedule,s,o,h,u,i)},decryptBlock:function(t,e){var r=t[e+1];t[e+1]=t[e+3],t[e+3]=r,this._doCryptBlock(t,e,this._invKeySchedule,c,l,f,g,n),r=t[e+1],t[e+1]=t[e+3],t[e+3]=r},_doCryptBlock:function(t,e,r,i,n,s,a,o){for(var h=this._nRounds,u=t[e]^r[0],c=t[e+1]^r[1],l=t[e+2]^r[2],f=t[e+3]^r[3],g=4,p=1;p<h;p++){var d=i[u>>>24]^n[c>>>16&255]^s[l>>>8&255]^a[255&f]^r[g++],v=i[c>>>24]^n[l>>>16&255]^s[f>>>8&255]^a[255&u]^r[g++],m=i[l>>>24]^n[f>>>16&255]^s[u>>>8&255]^a[255&c]^r[g++];f=i[f>>>24]^n[u>>>16&255]^s[c>>>8&255]^a[255&l]^r[g++],u=d,c=v,l=m}d=(o[u>>>24]<<24|o[c>>>16&255]<<16|o[l>>>8&255]<<8|o[255&f])^r[g++],v=(o[c>>>24]<<24|o[l>>>16&255]<<16|o[f>>>8&255]<<8|o[255&u])^r[g++],m=(o[l>>>24]<<24|o[f>>>16&255]<<16|o[u>>>8&255]<<8|o[255&c])^r[g++],f=(o[f>>>24]<<24|o[u>>>16&255]<<16|o[c>>>8&255]<<8|o[255&l])^r[g++],t[e]=d,t[e+1]=v,t[e+2]=m,t[e+3]=f},keySize:8});t.AES=e._createHelper(r)}(),function(){function t(t,e){var r=(this._lBlock>>>t^this._rBlock)&e;this._rBlock^=r,this._lBlock^=r<<t}function e(t,e){var r=(this._rBlock>>>t^this._lBlock)&e;this._lBlock^=r,this._rBlock^=r<<t}var r=a,i=r.lib,n=i.WordArray,s=(i=i.BlockCipher,r.algo),o=[57,49,41,33,25,17,9,1,58,50,42,34,26,18,10,2,59,51,43,35,27,19,11,3,60,52,44,36,63,55,47,39,31,23,15,7,62,54,46,38,30,22,14,6,61,53,45,37,29,21,13,5,28,20,12,4],h=[14,17,11,24,1,5,3,28,15,6,21,10,23,19,12,4,26,8,16,7,27,20,13,2,41,52,31,37,47,55,30,40,51,45,33,48,44,49,39,56,34,53,46,42,50,36,29,32],u=[1,2,4,6,8,10,12,14,15,17,19,21,23,25,27,28],c=[{0:8421888,268435456:32768,536870912:8421378,805306368:2,1073741824:512,1342177280:8421890,1610612736:8389122,1879048192:8388608,2147483648:514,2415919104:8389120,2684354560:33280,2952790016:8421376,3221225472:32770,3489660928:8388610,3758096384:0,4026531840:33282,134217728:0,402653184:8421890,671088640:33282,939524096:32768,1207959552:8421888,1476395008:512,1744830464:8421378,2013265920:2,2281701376:8389120,2550136832:33280,2818572288:8421376,3087007744:8389122,3355443200:8388610,3623878656:32770,3892314112:514,4160749568:8388608,1:32768,268435457:2,536870913:8421888,805306369:8388608,1073741825:8421378,1342177281:33280,1610612737:512,1879048193:8389122,2147483649:8421890,2415919105:8421376,2684354561:8388610,2952790017:33282,3221225473:514,3489660929:8389120,3758096385:32770,4026531841:0,134217729:8421890,402653185:8421376,671088641:8388608,939524097:512,1207959553:32768,1476395009:8388610,1744830465:2,2013265921:33282,2281701377:32770,2550136833:8389122,2818572289:514,3087007745:8421888,3355443201:8389120,3623878657:0,3892314113:33280,4160749569:8421378},{0:1074282512,16777216:16384,33554432:524288,50331648:1074266128,67108864:1073741840,83886080:1074282496,100663296:1073758208,117440512:16,134217728:540672,150994944:1073758224,167772160:1073741824,184549376:540688,201326592:524304,218103808:0,234881024:16400,251658240:1074266112,8388608:1073758208,25165824:540688,41943040:16,58720256:1073758224,75497472:1074282512,92274688:1073741824,109051904:524288,125829120:1074266128,142606336:524304,159383552:0,176160768:16384,192937984:1074266112,209715200:1073741840,226492416:540672,243269632:1074282496,260046848:16400,268435456:0,285212672:1074266128,301989888:1073758224,318767104:1074282496,335544320:1074266112,352321536:16,369098752:540688,385875968:16384,402653184:16400,419430400:524288,436207616:524304,452984832:1073741840,469762048:540672,486539264:1073758208,503316480:1073741824,520093696:1074282512,276824064:540688,293601280:524288,310378496:1074266112,327155712:16384,343932928:1073758208,360710144:1074282512,377487360:16,394264576:1073741824,411041792:1074282496,427819008:1073741840,444596224:1073758224,461373440:524304,478150656:0,494927872:16400,511705088:1074266128,528482304:540672},{0:260,1048576:0,2097152:67109120,3145728:65796,4194304:65540,5242880:67108868,6291456:67174660,7340032:67174400,8388608:67108864,9437184:67174656,10485760:65792,11534336:67174404,12582912:67109124,13631488:65536,14680064:4,15728640:256,524288:67174656,1572864:67174404,2621440:0,3670016:67109120,4718592:67108868,5767168:65536,6815744:65540,7864320:260,8912896:4,9961472:256,11010048:67174400,12058624:65796,13107200:65792,14155776:67109124,15204352:67174660,16252928:67108864,16777216:67174656,17825792:65540,18874368:65536,19922944:67109120,20971520:256,22020096:67174660,23068672:67108868,24117248:0,25165824:67109124,26214400:67108864,27262976:4,28311552:65792,29360128:67174400,30408704:260,31457280:65796,32505856:67174404,17301504:67108864,18350080:260,19398656:67174656,20447232:0,21495808:65540,22544384:67109120,23592960:256,24641536:67174404,25690112:65536,26738688:67174660,27787264:65796,28835840:67108868,29884416:67109124,30932992:67174400,31981568:4,33030144:65792},{0:2151682048,65536:2147487808,131072:4198464,196608:2151677952,262144:0,327680:4198400,393216:2147483712,458752:4194368,524288:2147483648,589824:4194304,655360:64,720896:2147487744,786432:2151678016,851968:4160,917504:4096,983040:2151682112,32768:2147487808,98304:64,163840:2151678016,229376:2147487744,294912:4198400,360448:2151682112,425984:0,491520:2151677952,557056:4096,622592:2151682048,688128:4194304,753664:4160,819200:2147483648,884736:4194368,950272:4198464,1015808:2147483712,1048576:4194368,1114112:4198400,1179648:2147483712,1245184:0,1310720:4160,1376256:2151678016,1441792:2151682048,1507328:2147487808,1572864:2151682112,1638400:2147483648,1703936:2151677952,1769472:4198464,1835008:2147487744,1900544:4194304,1966080:64,2031616:4096,1081344:2151677952,1146880:2151682112,1212416:0,1277952:4198400,1343488:4194368,1409024:2147483648,1474560:2147487808,1540096:64,1605632:2147483712,1671168:4096,1736704:2147487744,1802240:2151678016,1867776:4160,1933312:2151682048,1998848:4194304,2064384:4198464},{0:128,4096:17039360,8192:262144,12288:536870912,16384:537133184,20480:16777344,24576:553648256,28672:262272,32768:16777216,36864:537133056,40960:536871040,45056:553910400,49152:553910272,53248:0,57344:17039488,61440:553648128,2048:17039488,6144:553648256,10240:128,14336:17039360,18432:262144,22528:537133184,26624:553910272,30720:536870912,34816:537133056,38912:0,43008:553910400,47104:16777344,51200:536871040,55296:553648128,59392:16777216,63488:262272,65536:262144,69632:128,73728:536870912,77824:553648256,81920:16777344,86016:553910272,90112:537133184,94208:16777216,98304:553910400,102400:553648128,106496:17039360,110592:537133056,114688:262272,118784:536871040,122880:0,126976:17039488,67584:553648256,71680:16777216,75776:17039360,79872:537133184,83968:536870912,88064:17039488,92160:128,96256:553910272,100352:262272,104448:553910400,108544:0,112640:553648128,116736:16777344,120832:262144,124928:537133056,129024:536871040},{0:268435464,256:8192,512:270532608,768:270540808,1024:268443648,1280:2097152,1536:2097160,1792:268435456,2048:0,2304:268443656,2560:2105344,2816:8,3072:270532616,3328:2105352,3584:8200,3840:270540800,128:270532608,384:270540808,640:8,896:2097152,1152:2105352,1408:268435464,1664:268443648,1920:8200,2176:2097160,2432:8192,2688:268443656,2944:270532616,3200:0,3456:270540800,3712:2105344,3968:268435456,4096:268443648,4352:270532616,4608:270540808,4864:8200,5120:2097152,5376:268435456,5632:268435464,5888:2105344,6144:2105352,6400:0,6656:8,6912:270532608,7168:8192,7424:268443656,7680:270540800,7936:2097160,4224:8,4480:2105344,4736:2097152,4992:268435464,5248:268443648,5504:8200,5760:270540808,6016:270532608,6272:270540800,6528:270532616,6784:8192,7040:2105352,7296:2097160,7552:0,7808:268435456,8064:268443656},{0:1048576,16:33555457,32:1024,48:1049601,64:34604033,80:0,96:1,112:34603009,128:33555456,144:1048577,160:33554433,176:34604032,192:34603008,208:1025,224:1049600,240:33554432,8:34603009,24:0,40:33555457,56:34604032,72:1048576,88:33554433,104:33554432,120:1025,136:1049601,152:33555456,168:34603008,184:1048577,200:1024,216:34604033,232:1,248:1049600,256:33554432,272:1048576,288:33555457,304:34603009,320:1048577,336:33555456,352:34604032,368:1049601,384:1025,400:34604033,416:1049600,432:1,448:0,464:34603008,480:33554433,496:1024,264:1049600,280:33555457,296:34603009,312:1,328:33554432,344:1048576,360:1025,376:34604032,392:33554433,408:34603008,424:0,440:34604033,456:1049601,472:1024,488:33555456,504:1048577},{0:134219808,1:131072,2:134217728,3:32,4:131104,5:134350880,6:134350848,7:2048,8:134348800,9:134219776,10:133120,11:134348832,12:2080,13:0,14:134217760,15:133152,2147483648:2048,2147483649:134350880,2147483650:134219808,2147483651:134217728,2147483652:134348800,2147483653:133120,2147483654:133152,2147483655:32,2147483656:134217760,2147483657:2080,2147483658:131104,2147483659:134350848,2147483660:0,2147483661:134348832,2147483662:134219776,2147483663:131072,16:133152,17:134350848,18:32,19:2048,20:134219776,21:134217760,22:134348832,23:131072,24:0,25:131104,26:134348800,27:134219808,28:134350880,29:133120,30:2080,31:134217728,2147483664:131072,2147483665:2048,2147483666:134348832,2147483667:133152,2147483668:32,2147483669:134348800,2147483670:134217728,2147483671:134219808,2147483672:134350880,2147483673:134217760,2147483674:134219776,2147483675:0,2147483676:133120,2147483677:2080,2147483678:131104,2147483679:134350848}],l=[4160749569,528482304,33030144,2064384,129024,8064,504,2147483679],f=s.DES=i.extend({_doReset:function(){for(var t=this._key.words,e=[],r=0;56>r;r++){var i=o[r]-1;e[r]=t[i>>>5]>>>31-i%32&1}for(t=this._subKeys=[],i=0;16>i;i++){var n=t[i]=[],s=u[i];for(r=0;24>r;r++)n[r/6|0]|=e[(h[r]-1+s)%28]<<31-r%6,n[4+(r/6|0)]|=e[28+(h[r+24]-1+s)%28]<<31-r%6;for(n[0]=n[0]<<1|n[0]>>>31,r=1;7>r;r++)n[r]>>>=4*(r-1)+3;n[7]=n[7]<<5|n[7]>>>27}for(e=this._invSubKeys=[],r=0;16>r;r++)e[r]=t[15-r]},encryptBlock:function(t,e){this._doCryptBlock(t,e,this._subKeys)},decryptBlock:function(t,e){this._doCryptBlock(t,e,this._invSubKeys)},_doCryptBlock:function(r,i,n){this._lBlock=r[i],this._rBlock=r[i+1],t.call(this,4,252645135),t.call(this,16,65535),e.call(this,2,858993459),e.call(this,8,16711935),t.call(this,1,1431655765);for(var s=0;16>s;s++){for(var a=n[s],o=this._lBlock,h=this._rBlock,u=0,f=0;8>f;f++)u|=c[f][((h^a[f])&l[f])>>>0];this._lBlock=h,this._rBlock=o^u}n=this._lBlock,this._lBlock=this._rBlock,this._rBlock=n,t.call(this,1,1431655765),e.call(this,8,16711935),e.call(this,2,858993459),t.call(this,16,65535),t.call(this,4,252645135),r[i]=this._lBlock,r[i+1]=this._rBlock},keySize:2,ivSize:2,blockSize:2});r.DES=i._createHelper(f),s=s.TripleDES=i.extend({_doReset:function(){var t=this._key.words;this._des1=f.createEncryptor(n.create(t.slice(0,2))),this._des2=f.createEncryptor(n.create(t.slice(2,4))),this._des3=f.createEncryptor(n.create(t.slice(4,6)))},encryptBlock:function(t,e){this._des1.encryptBlock(t,e),this._des2.decryptBlock(t,e),this._des3.encryptBlock(t,e)},decryptBlock:function(t,e){this._des3.decryptBlock(t,e),this._des2.encryptBlock(t,e),this._des1.decryptBlock(t,e)},keySize:6,ivSize:2,blockSize:2}),r.TripleDES=i._createHelper(s)}(),function(){var t=a,e=t.lib.WordArray;t.enc.Base64={stringify:function(t){var e=t.words,r=t.sigBytes,i=this._map;t.clamp(),t=[];for(var n=0;n<r;n+=3)for(var s=(e[n>>>2]>>>24-n%4*8&255)<<16|(e[n+1>>>2]>>>24-(n+1)%4*8&255)<<8|e[n+2>>>2]>>>24-(n+2)%4*8&255,a=0;4>a&&n+.75*a<r;a++)t.push(i.charAt(s>>>6*(3-a)&63));if(e=i.charAt(64))for(;t.length%4;)t.push(e);return t.join("")},parse:function(t){var r=t.length,i=this._map,n=i.charAt(64);n&&(n=t.indexOf(n),-1!=n&&(r=n));n=[];for(var s=0,a=0;a<r;a++)if(a%4){var o=i.indexOf(t.charAt(a-1))<<a%4*2,h=i.indexOf(t.charAt(a))>>>6-a%4*2;n[s>>>2]|=(o|h)<<24-s%4*8,s++}return e.create(n,s)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="}}(),function(t){function e(t,e,r,i,n,s,a){return t=t+(e&r|~e&i)+n+a,(t<<s|t>>>32-s)+e}function r(t,e,r,i,n,s,a){return t=t+(e&i|r&~i)+n+a,(t<<s|t>>>32-s)+e}function i(t,e,r,i,n,s,a){return t=t+(e^r^i)+n+a,(t<<s|t>>>32-s)+e}function n(t,e,r,i,n,s,a){return t=t+(r^(e|~i))+n+a,(t<<s|t>>>32-s)+e}for(var s=a,o=s.lib,h=o.WordArray,u=o.Hasher,c=(o=s.algo,[]),l=0;64>l;l++)c[l]=4294967296*t.abs(t.sin(l+1))|0;o=o.MD5=u.extend({_doReset:function(){this._hash=new h.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(t,s){for(var a=0;16>a;a++){var o=s+a,h=t[o];t[o]=16711935&(h<<8|h>>>24)|4278255360&(h<<24|h>>>8)}a=this._hash.words,o=t[s+0],h=t[s+1];var u=t[s+2],l=t[s+3],f=t[s+4],g=t[s+5],p=t[s+6],d=t[s+7],v=t[s+8],m=t[s+9],y=t[s+10],x=t[s+11],S=t[s+12],w=t[s+13],E=t[s+14],F=t[s+15],b=a[0],A=a[1],D=a[2],I=a[3];b=e(b,A,D,I,o,7,c[0]),I=e(I,b,A,D,h,12,c[1]),D=e(D,I,b,A,u,17,c[2]),A=e(A,D,I,b,l,22,c[3]),b=e(b,A,D,I,f,7,c[4]),I=e(I,b,A,D,g,12,c[5]),D=e(D,I,b,A,p,17,c[6]),A=e(A,D,I,b,d,22,c[7]),b=e(b,A,D,I,v,7,c[8]),I=e(I,b,A,D,m,12,c[9]),D=e(D,I,b,A,y,17,c[10]),A=e(A,D,I,b,x,22,c[11]),b=e(b,A,D,I,S,7,c[12]),I=e(I,b,A,D,w,12,c[13]),D=e(D,I,b,A,E,17,c[14]),A=e(A,D,I,b,F,22,c[15]),b=r(b,A,D,I,h,5,c[16]),I=r(I,b,A,D,p,9,c[17]),D=r(D,I,b,A,x,14,c[18]),A=r(A,D,I,b,o,20,c[19]),b=r(b,A,D,I,g,5,c[20]),I=r(I,b,A,D,y,9,c[21]),D=r(D,I,b,A,F,14,c[22]),A=r(A,D,I,b,f,20,c[23]),b=r(b,A,D,I,m,5,c[24]),I=r(I,b,A,D,E,9,c[25]),D=r(D,I,b,A,l,14,c[26]),A=r(A,D,I,b,v,20,c[27]),b=r(b,A,D,I,w,5,c[28]),I=r(I,b,A,D,u,9,c[29]),D=r(D,I,b,A,d,14,c[30]),A=r(A,D,I,b,S,20,c[31]),b=i(b,A,D,I,g,4,c[32]),I=i(I,b,A,D,v,11,c[33]),D=i(D,I,b,A,x,16,c[34]),A=i(A,D,I,b,E,23,c[35]),b=i(b,A,D,I,h,4,c[36]),I=i(I,b,A,D,f,11,c[37]),D=i(D,I,b,A,d,16,c[38]),A=i(A,D,I,b,y,23,c[39]),b=i(b,A,D,I,w,4,c[40]),I=i(I,b,A,D,o,11,c[41]),D=i(D,I,b,A,l,16,c[42]),A=i(A,D,I,b,p,23,c[43]),b=i(b,A,D,I,m,4,c[44]),I=i(I,b,A,D,S,11,c[45]),D=i(D,I,b,A,F,16,c[46]),A=i(A,D,I,b,u,23,c[47]),b=n(b,A,D,I,o,6,c[48]),I=n(I,b,A,D,d,10,c[49]),D=n(D,I,b,A,E,15,c[50]),A=n(A,D,I,b,g,21,c[51]),b=n(b,A,D,I,S,6,c[52]),I=n(I,b,A,D,l,10,c[53]),D=n(D,I,b,A,y,15,c[54]),A=n(A,D,I,b,h,21,c[55]),b=n(b,A,D,I,v,6,c[56]),I=n(I,b,A,D,F,10,c[57]),D=n(D,I,b,A,p,15,c[58]),A=n(A,D,I,b,w,21,c[59]),b=n(b,A,D,I,f,6,c[60]),I=n(I,b,A,D,x,10,c[61]),D=n(D,I,b,A,u,15,c[62]),A=n(A,D,I,b,m,21,c[63]);a[0]=a[0]+b|0,a[1]=a[1]+A|0,a[2]=a[2]+D|0,a[3]=a[3]+I|0},_doFinalize:function(){var e=this._data,r=e.words,i=8*this._nDataBytes,n=8*e.sigBytes;r[n>>>5]|=128<<24-n%32;var s=t.floor(i/4294967296);for(r[15+(n+64>>>9<<4)]=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),r[14+(n+64>>>9<<4)]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8),e.sigBytes=4*(r.length+1),this._process(),e=this._hash,r=e.words,i=0;4>i;i++)n=r[i],r[i]=16711935&(n<<8|n>>>24)|4278255360&(n<<24|n>>>8);return e},clone:function(){var t=u.clone.call(this);return t._hash=this._hash.clone(),t}}),s.MD5=u._createHelper(o),s.HmacMD5=u._createHmacHelper(o)}(Math),function(){var t=a,e=t.lib,r=e.WordArray,i=e.Hasher,n=[];e=t.algo.SHA1=i.extend({_doReset:function(){this._hash=new r.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(t,e){for(var r=this._hash.words,i=r[0],s=r[1],a=r[2],o=r[3],h=r[4],u=0;80>u;u++){if(16>u)n[u]=0|t[e+u];else{var c=n[u-3]^n[u-8]^n[u-14]^n[u-16];n[u]=c<<1|c>>>31}c=(i<<5|i>>>27)+h+n[u],c=20>u?c+(1518500249+(s&a|~s&o)):40>u?c+(1859775393+(s^a^o)):60>u?c+((s&a|s&o|a&o)-1894007588):c+((s^a^o)-899497514),h=o,o=a,a=s<<30|s>>>2,s=i,i=c}r[0]=r[0]+i|0,r[1]=r[1]+s|0,r[2]=r[2]+a|0,r[3]=r[3]+o|0,r[4]=r[4]+h|0},_doFinalize:function(){var t=this._data,e=t.words,r=8*this._nDataBytes,i=8*t.sigBytes;return e[i>>>5]|=128<<24-i%32,e[14+(i+64>>>9<<4)]=Math.floor(r/4294967296),e[15+(i+64>>>9<<4)]=r,t.sigBytes=4*e.length,this._process(),this._hash},clone:function(){var t=i.clone.call(this);return t._hash=this._hash.clone(),t}});t.SHA1=i._createHelper(e),t.HmacSHA1=i._createHmacHelper(e)}(),function(t){for(var e=a,r=e.lib,i=r.WordArray,n=r.Hasher,s=(r=e.algo,[]),o=[],h=function(t){return 4294967296*(t-(0|t))|0},u=2,c=0;64>c;){var l;t:{l=u;for(var f=t.sqrt(l),g=2;g<=f;g++)if(!(l%g)){l=!1;break t}l=!0}l&&(8>c&&(s[c]=h(t.pow(u,.5))),o[c]=h(t.pow(u,1/3)),c++),u++}var p=[];r=r.SHA256=n.extend({_doReset:function(){this._hash=new i.init(s.slice(0))},_doProcessBlock:function(t,e){for(var r=this._hash.words,i=r[0],n=r[1],s=r[2],a=r[3],h=r[4],u=r[5],c=r[6],l=r[7],f=0;64>f;f++){if(16>f)p[f]=0|t[e+f];else{var g=p[f-15],d=p[f-2];p[f]=((g<<25|g>>>7)^(g<<14|g>>>18)^g>>>3)+p[f-7]+((d<<15|d>>>17)^(d<<13|d>>>19)^d>>>10)+p[f-16]}g=l+((h<<26|h>>>6)^(h<<21|h>>>11)^(h<<7|h>>>25))+(h&u^~h&c)+o[f]+p[f],d=((i<<30|i>>>2)^(i<<19|i>>>13)^(i<<10|i>>>22))+(i&n^i&s^n&s),l=c,c=u,u=h,h=a+g|0,a=s,s=n,n=i,i=g+d|0}r[0]=r[0]+i|0,r[1]=r[1]+n|0,r[2]=r[2]+s|0,r[3]=r[3]+a|0,r[4]=r[4]+h|0,r[5]=r[5]+u|0,r[6]=r[6]+c|0,r[7]=r[7]+l|0},_doFinalize:function(){var e=this._data,r=e.words,i=8*this._nDataBytes,n=8*e.sigBytes;return r[n>>>5]|=128<<24-n%32,r[14+(n+64>>>9<<4)]=t.floor(i/4294967296),r[15+(n+64>>>9<<4)]=i,e.sigBytes=4*r.length,this._process(),this._hash},clone:function(){var t=n.clone.call(this);return t._hash=this._hash.clone(),t}});e.SHA256=n._createHelper(r),e.HmacSHA256=n._createHmacHelper(r)}(Math),function(){var t=a,e=t.lib.WordArray,r=t.algo,i=r.SHA256;r=r.SHA224=i.extend({_doReset:function(){this._hash=new e.init([3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428])},_doFinalize:function(){var t=i._doFinalize.call(this);return t.sigBytes-=4,t}});t.SHA224=i._createHelper(r),t.HmacSHA224=i._createHmacHelper(r)}(),function(){function t(){return n.create.apply(n,arguments)}for(var e=a,r=e.lib.Hasher,i=e.x64,n=i.Word,s=i.WordArray,o=(i=e.algo,[t(1116352408,3609767458),t(1899447441,602891725),t(3049323471,3964484399),t(3921009573,2173295548),t(961987163,4081628472),t(1508970993,3053834265),t(2453635748,2937671579),t(2870763221,3664609560),t(3624381080,2734883394),t(310598401,1164996542),t(607225278,1323610764),t(1426881987,3590304994),t(1925078388,4068182383),t(2162078206,991336113),t(2614888103,633803317),t(3248222580,3479774868),t(3835390401,2666613458),t(4022224774,944711139),t(264347078,2341262773),t(604807628,2007800933),t(770255983,1495990901),t(1249150122,1856431235),t(1555081692,3175218132),t(1996064986,2198950837),t(2554220882,3999719339),t(2821834349,766784016),t(2952996808,2566594879),t(3210313671,3203337956),t(3336571891,1034457026),t(3584528711,2466948901),t(113926993,3758326383),t(338241895,168717936),t(666307205,1188179964),t(773529912,1546045734),t(1294757372,1522805485),t(1396182291,2643833823),t(1695183700,2343527390),t(1986661051,1014477480),t(2177026350,1206759142),t(2456956037,344077627),t(2730485921,1290863460),t(2820302411,3158454273),t(3259730800,3505952657),t(3345764771,106217008),t(3516065817,3606008344),t(3600352804,1432725776),t(4094571909,1467031594),t(275423344,851169720),t(430227734,3100823752),t(506948616,1363258195),t(659060556,3750685593),t(883997877,3785050280),t(958139571,3318307427),t(1322822218,3812723403),t(1537002063,2003034995),t(1747873779,3602036899),t(1955562222,1575990012),t(2024104815,1125592928),t(2227730452,2716904306),t(2361852424,442776044),t(2428436474,593698344),t(2756734187,3733110249),t(3204031479,2999351573),t(3329325298,3815920427),t(3391569614,3928383900),t(3515267271,566280711),t(3940187606,3454069534),t(4118630271,4000239992),t(116418474,1914138554),t(174292421,2731055270),t(289380356,3203993006),t(460393269,320620315),t(685471733,587496836),t(852142971,1086792851),t(1017036298,365543100),t(1126000580,2618297676),t(1288033470,3409855158),t(1501505948,4234509866),t(1607167915,987167468),t(1816402316,1246189591)]),h=[],u=0;80>u;u++)h[u]=t();i=i.SHA512=r.extend({_doReset:function(){this._hash=new s.init([new n.init(1779033703,4089235720),new n.init(3144134277,2227873595),new n.init(1013904242,4271175723),new n.init(2773480762,1595750129),new n.init(1359893119,2917565137),new n.init(2600822924,725511199),new n.init(528734635,4215389547),new n.init(1541459225,327033209)])},_doProcessBlock:function(t,e){for(var r=this._hash.words,i=r[0],n=r[1],s=r[2],a=r[3],u=r[4],c=r[5],l=r[6],f=(r=r[7],i.high),g=i.low,p=n.high,d=n.low,v=s.high,m=s.low,y=a.high,x=a.low,S=u.high,w=u.low,E=c.high,F=c.low,b=l.high,A=l.low,D=r.high,I=r.low,C=f,P=g,R=p,T=d,B=v,N=m,H=y,O=x,j=S,V=w,K=E,L=F,k=b,q=A,M=D,_=I,U=0;80>U;U++){var G=h[U];if(16>U)var z=G.high=0|t[e+2*U],W=G.low=0|t[e+2*U+1];else{z=h[U-15],W=z.high;var J=z.low,X=(z=(W>>>1|J<<31)^(W>>>8|J<<24)^W>>>7,J=(J>>>1|W<<31)^(J>>>8|W<<24)^(J>>>7|W<<25),h[U-2]),$=(W=X.high,X.low),Y=(X=(W>>>19|$<<13)^(W<<3|$>>>29)^W>>>6,$=($>>>19|W<<13)^($<<3|W>>>29)^($>>>6|W<<26),W=h[U-7],W.high),Z=h[U-16],Q=Z.high;Z=Z.low,W=J+W.low,z=z+Y+(W>>>0<J>>>0?1:0),W=W+$,z=z+X+(W>>>0<$>>>0?1:0),W=W+Z,z=z+Q+(W>>>0<Z>>>0?1:0);G.high=z,G.low=W}Y=j&K^~j&k,Z=V&L^~V&q,G=C&R^C&B^R&B;var tt=P&T^P&N^T&N,et=(J=(C>>>28|P<<4)^(C<<30|P>>>2)^(C<<25|P>>>7),X=(P>>>28|C<<4)^(P<<30|C>>>2)^(P<<25|C>>>7),$=o[U],$.high),rt=$.low;$=_+((V>>>14|j<<18)^(V>>>18|j<<14)^(V<<23|j>>>9)),Q=M+((j>>>14|V<<18)^(j>>>18|V<<14)^(j<<23|V>>>9))+($>>>0<_>>>0?1:0),$=$+Z,Q=Q+Y+($>>>0<Z>>>0?1:0),$=$+rt,Q=Q+et+($>>>0<rt>>>0?1:0),$=$+W,Q=Q+z+($>>>0<W>>>0?1:0),W=X+tt,G=J+G+(W>>>0<X>>>0?1:0),M=k,_=q,k=K,q=L,K=j,L=V,V=O+$|0,j=H+Q+(V>>>0<O>>>0?1:0)|0,H=B,O=N,B=R,N=T,R=C,T=P,P=$+W|0,C=Q+G+(P>>>0<$>>>0?1:0)|0}g=i.low=g+P,i.high=f+C+(g>>>0<P>>>0?1:0),d=n.low=d+T,n.high=p+R+(d>>>0<T>>>0?1:0),m=s.low=m+N,s.high=v+B+(m>>>0<N>>>0?1:0),x=a.low=x+O,a.high=y+H+(x>>>0<O>>>0?1:0),w=u.low=w+V,u.high=S+j+(w>>>0<V>>>0?1:0),F=c.low=F+L,c.high=E+K+(F>>>0<L>>>0?1:0),A=l.low=A+q,l.high=b+k+(A>>>0<q>>>0?1:0),I=r.low=I+_,r.high=D+M+(I>>>0<_>>>0?1:0)},_doFinalize:function(){var t=this._data,e=t.words,r=8*this._nDataBytes,i=8*t.sigBytes;return e[i>>>5]|=128<<24-i%32,e[30+(i+128>>>10<<5)]=Math.floor(r/4294967296),e[31+(i+128>>>10<<5)]=r,t.sigBytes=4*e.length,this._process(),this._hash.toX32()},clone:function(){var t=r.clone.call(this);return t._hash=this._hash.clone(),t},blockSize:32}),e.SHA512=r._createHelper(i),e.HmacSHA512=r._createHmacHelper(i)}(),function(){var t=a,e=t.x64,r=e.Word,i=e.WordArray,n=(e=t.algo,e.SHA512);e=e.SHA384=n.extend({_doReset:function(){this._hash=new i.init([new r.init(3418070365,3238371032),new r.init(1654270250,914150663),new r.init(2438529370,812702999),new r.init(355462360,4144912697),new r.init(1731405415,4290775857),new r.init(2394180231,1750603025),new r.init(3675008525,1694076839),new r.init(1203062813,3204075428)])},_doFinalize:function(){var t=n._doFinalize.call(this);return t.sigBytes-=16,t}});t.SHA384=n._createHelper(e),t.HmacSHA384=n._createHmacHelper(e)}(),function(){var t=a,e=t.lib,r=e.WordArray,i=e.Hasher,n=(e=t.algo,r.create([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8,3,10,14,4,9,15,8,1,2,7,0,6,13,11,5,12,1,9,11,10,0,8,12,4,13,3,7,15,14,5,6,2,4,0,5,9,7,12,2,10,14,1,3,8,11,6,15,13])),s=r.create([5,14,7,0,9,2,11,4,13,6,15,8,1,10,3,12,6,11,3,7,0,13,5,10,14,15,8,12,4,9,1,2,15,5,1,3,7,14,6,9,11,8,12,2,10,0,4,13,8,6,4,1,3,11,15,0,5,12,2,13,9,7,10,14,12,15,10,4,1,5,8,7,6,2,13,14,0,3,9,11]),o=r.create([11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8,7,6,8,13,11,9,7,15,7,12,15,9,11,7,13,12,11,13,6,7,14,9,13,15,14,8,13,6,5,12,7,5,11,12,14,15,14,15,9,8,9,14,5,6,8,6,5,12,9,15,5,11,6,8,13,12,5,12,13,14,11,8,5,6]),h=r.create([8,9,9,11,13,15,15,5,7,7,8,11,14,14,12,6,9,13,15,7,12,8,9,11,7,7,12,7,6,15,13,11,9,7,15,11,8,6,6,14,12,13,5,14,13,13,7,5,15,5,8,11,14,14,6,14,6,9,12,9,12,5,15,8,8,5,12,9,12,5,14,6,8,13,6,5,15,13,11,11]),u=r.create([0,1518500249,1859775393,2400959708,2840853838]),c=r.create([1352829926,1548603684,1836072691,2053994217,0]);e=e.RIPEMD160=i.extend({_doReset:function(){this._hash=r.create([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(t,e){for(var r=0;16>r;r++){var i=e+r,a=t[i];t[i]=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8)}i=this._hash.words,a=u.words;var l,f,g,p,d,v,m,y,x,S,w=c.words,E=n.words,F=s.words,b=o.words,A=h.words;v=l=i[0],m=f=i[1],y=g=i[2],x=p=i[3],S=d=i[4];var D;for(r=0;80>r;r+=1)D=l+t[e+E[r]]|0,D=16>r?D+((f^g^p)+a[0]):32>r?D+((f&g|~f&p)+a[1]):48>r?D+(((f|~g)^p)+a[2]):64>r?D+((f&p|g&~p)+a[3]):D+((f^(g|~p))+a[4]),D|=0,D=D<<b[r]|D>>>32-b[r],D=D+d|0,l=d,d=p,p=g<<10|g>>>22,g=f,f=D,D=v+t[e+F[r]]|0,D=16>r?D+((m^(y|~x))+w[0]):32>r?D+((m&x|y&~x)+w[1]):48>r?D+(((m|~y)^x)+w[2]):64>r?D+((m&y|~m&x)+w[3]):D+((m^y^x)+w[4]),D|=0,D=D<<A[r]|D>>>32-A[r],D=D+S|0,v=S,S=x,x=y<<10|y>>>22,y=m,m=D;D=i[1]+g+x|0,i[1]=i[2]+p+S|0,i[2]=i[3]+d+v|0,i[3]=i[4]+l+m|0,i[4]=i[0]+f+y|0,i[0]=D},_doFinalize:function(){var t=this._data,e=t.words,r=8*this._nDataBytes,i=8*t.sigBytes;for(e[i>>>5]|=128<<24-i%32,e[14+(i+64>>>9<<4)]=16711935&(r<<8|r>>>24)|4278255360&(r<<24|r>>>8),t.sigBytes=4*(e.length+1),this._process(),t=this._hash,e=t.words,r=0;5>r;r++)i=e[r],e[r]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8);return t},clone:function(){var t=i.clone.call(this);return t._hash=this._hash.clone(),t}});t.RIPEMD160=i._createHelper(e),t.HmacRIPEMD160=i._createHmacHelper(e)}(Math),function(){var t=a,e=t.enc.Utf8;t.algo.HMAC=t.lib.Base.extend({init:function(t,r){t=this._hasher=new t.init,"string"==typeof r&&(r=e.parse(r));var i=t.blockSize,n=4*i;r.sigBytes>n&&(r=t.finalize(r)),r.clamp();for(var s=this._oKey=r.clone(),a=this._iKey=r.clone(),o=s.words,h=a.words,u=0;u<i;u++)o[u]^=1549556828,h[u]^=909522486;s.sigBytes=a.sigBytes=n,this.reset()},reset:function(){var t=this._hasher;t.reset(),t.update(this._iKey)},update:function(t){return this._hasher.update(t),this},finalize:function(t){var e=this._hasher;return t=e.finalize(t),e.reset(),e.finalize(this._oKey.clone().concat(t))}})}(),function(){var t=a,e=t.lib,r=e.Base,i=e.WordArray,n=(e=t.algo,e.HMAC),s=e.PBKDF2=r.extend({cfg:r.extend({keySize:4,hasher:e.SHA1,iterations:1}),init:function(t){this.cfg=this.cfg.extend(t)},compute:function(t,e){var r=this.cfg,s=n.create(r.hasher,t),a=i.create(),o=i.create([1]),h=a.words,u=o.words,c=r.keySize;for(r=r.iterations;h.length<c;){var l=s.update(e).finalize(o);s.reset();for(var f=l.words,g=f.length,p=l,d=1;d<r;d++){p=s.finalize(p),s.reset();for(var v=p.words,m=0;m<g;m++)f[m]^=v[m]}a.concat(l),u[0]++}return a.sigBytes=4*c,a}});t.PBKDF2=function(t,e,r){return s.create(r).compute(t,e)}}();
/*! (c) Tom Wu | http://www-cs-students.stanford.edu/~tjw/jsbn/
 */
var o,h="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",u="=";function c(t){var e,r,i="";for(e=0;e+3<=t.length;e+=3)r=parseInt(t.substring(e,e+3),16),i+=h.charAt(r>>6)+h.charAt(63&r);if(e+1==t.length?(r=parseInt(t.substring(e,e+1),16),i+=h.charAt(r<<2)):e+2==t.length&&(r=parseInt(t.substring(e,e+2),16),i+=h.charAt(r>>2)+h.charAt((3&r)<<4)),u)while((3&i.length)>0)i+=u;return i}function l(t){var e,r,i,n="",s=0;for(e=0;e<t.length;++e){if(t.charAt(e)==u)break;i=h.indexOf(t.charAt(e)),i<0||(0==s?(n+=A(i>>2),r=3&i,s=1):1==s?(n+=A(r<<2|i>>4),r=15&i,s=2):2==s?(n+=A(r),n+=A(i>>2),r=3&i,s=3):(n+=A(r<<2|i>>4),n+=A(15&i),s=0))}return 1==s&&(n+=A(r<<2)),n}function f(t){var e,r=l(t),i=new Array;for(e=0;2*e<r.length;++e)i[e]=parseInt(r.substring(2*e,2*e+2),16);return i}var g=0xdeadbeefcafe,p=15715070==(16777215&g);function d(t,e,r){null!=t&&("number"==typeof t?this.fromNumber(t,e,r):null==e&&"string"!=typeof t?this.fromString(t,256):this.fromString(t,e))}function v(){return new d(null)}function m(t,e,r,i,n,s){while(--s>=0){var a=e*this[t++]+r[i]+n;n=Math.floor(a/67108864),r[i++]=67108863&a}return n}function y(t,e,r,i,n,s){var a=32767&e,o=e>>15;while(--s>=0){var h=32767&this[t],u=this[t++]>>15,c=o*h+u*a;h=a*h+((32767&c)<<15)+r[i]+(1073741823&n),n=(h>>>30)+(c>>>15)+o*u+(n>>>30),r[i++]=1073741823&h}return n}function x(t,e,r,i,n,s){var a=16383&e,o=e>>14;while(--s>=0){var h=16383&this[t],u=this[t++]>>14,c=o*h+u*a;h=a*h+((16383&c)<<14)+r[i]+n,n=(h>>28)+(c>>14)+o*u,r[i++]=268435455&h}return n}p&&"Microsoft Internet Explorer"==n.appName?(d.prototype.am=y,o=30):p&&"Netscape"!=n.appName?(d.prototype.am=m,o=26):(d.prototype.am=x,o=28),d.prototype.DB=o,d.prototype.DM=(1<<o)-1,d.prototype.DV=1<<o;var S=52;d.prototype.FV=Math.pow(2,S),d.prototype.F1=S-o,d.prototype.F2=2*o-S;var w,E,F="0123456789abcdefghijklmnopqrstuvwxyz",b=new Array;for(w="0".charCodeAt(0),E=0;E<=9;++E)b[w++]=E;for(w="a".charCodeAt(0),E=10;E<36;++E)b[w++]=E;for(w="A".charCodeAt(0),E=10;E<36;++E)b[w++]=E;function A(t){return F.charAt(t)}function D(t,e){var r=b[t.charCodeAt(e)];return null==r?-1:r}function I(t){for(var e=this.t-1;e>=0;--e)t[e]=this[e];t.t=this.t,t.s=this.s}function C(t){this.t=1,this.s=t<0?-1:0,t>0?this[0]=t:t<-1?this[0]=t+this.DV:this.t=0}function P(t){var e=v();return e.fromInt(t),e}function R(t,e){var r;if(16==e)r=4;else if(8==e)r=3;else if(256==e)r=8;else if(2==e)r=1;else if(32==e)r=5;else{if(4!=e)return void this.fromRadix(t,e);r=2}this.t=0,this.s=0;var i=t.length,n=!1,s=0;while(--i>=0){var a=8==r?255&t[i]:D(t,i);a<0?"-"==t.charAt(i)&&(n=!0):(n=!1,0==s?this[this.t++]=a:s+r>this.DB?(this[this.t-1]|=(a&(1<<this.DB-s)-1)<<s,this[this.t++]=a>>this.DB-s):this[this.t-1]|=a<<s,s+=r,s>=this.DB&&(s-=this.DB))}8==r&&0!=(128&t[0])&&(this.s=-1,s>0&&(this[this.t-1]|=(1<<this.DB-s)-1<<s)),this.clamp(),n&&d.ZERO.subTo(this,this)}function T(){var t=this.s&this.DM;while(this.t>0&&this[this.t-1]==t)--this.t}function B(t){if(this.s<0)return"-"+this.negate().toString(t);var e;if(16==t)e=4;else if(8==t)e=3;else if(2==t)e=1;else if(32==t)e=5;else{if(4!=t)return this.toRadix(t);e=2}var r,i=(1<<e)-1,n=!1,s="",a=this.t,o=this.DB-a*this.DB%e;if(a-- >0){o<this.DB&&(r=this[a]>>o)>0&&(n=!0,s=A(r));while(a>=0)o<e?(r=(this[a]&(1<<o)-1)<<e-o,r|=this[--a]>>(o+=this.DB-e)):(r=this[a]>>(o-=e)&i,o<=0&&(o+=this.DB,--a)),r>0&&(n=!0),n&&(s+=A(r))}return n?s:"0"}function N(){var t=v();return d.ZERO.subTo(this,t),t}function H(){return this.s<0?this.negate():this}function O(t){var e=this.s-t.s;if(0!=e)return e;var r=this.t;if(e=r-t.t,0!=e)return this.s<0?-e:e;while(--r>=0)if(0!=(e=this[r]-t[r]))return e;return 0}function j(t){var e,r=1;return 0!=(e=t>>>16)&&(t=e,r+=16),0!=(e=t>>8)&&(t=e,r+=8),0!=(e=t>>4)&&(t=e,r+=4),0!=(e=t>>2)&&(t=e,r+=2),0!=(e=t>>1)&&(t=e,r+=1),r}function V(){return this.t<=0?0:this.DB*(this.t-1)+j(this[this.t-1]^this.s&this.DM)}function K(t,e){var r;for(r=this.t-1;r>=0;--r)e[r+t]=this[r];for(r=t-1;r>=0;--r)e[r]=0;e.t=this.t+t,e.s=this.s}function L(t,e){for(var r=t;r<this.t;++r)e[r-t]=this[r];e.t=Math.max(this.t-t,0),e.s=this.s}function k(t,e){var r,i=t%this.DB,n=this.DB-i,s=(1<<n)-1,a=Math.floor(t/this.DB),o=this.s<<i&this.DM;for(r=this.t-1;r>=0;--r)e[r+a+1]=this[r]>>n|o,o=(this[r]&s)<<i;for(r=a-1;r>=0;--r)e[r]=0;e[a]=o,e.t=this.t+a+1,e.s=this.s,e.clamp()}function q(t,e){e.s=this.s;var r=Math.floor(t/this.DB);if(r>=this.t)e.t=0;else{var i=t%this.DB,n=this.DB-i,s=(1<<i)-1;e[0]=this[r]>>i;for(var a=r+1;a<this.t;++a)e[a-r-1]|=(this[a]&s)<<n,e[a-r]=this[a]>>i;i>0&&(e[this.t-r-1]|=(this.s&s)<<n),e.t=this.t-r,e.clamp()}}function M(t,e){var r=0,i=0,n=Math.min(t.t,this.t);while(r<n)i+=this[r]-t[r],e[r++]=i&this.DM,i>>=this.DB;if(t.t<this.t){i-=t.s;while(r<this.t)i+=this[r],e[r++]=i&this.DM,i>>=this.DB;i+=this.s}else{i+=this.s;while(r<t.t)i-=t[r],e[r++]=i&this.DM,i>>=this.DB;i-=t.s}e.s=i<0?-1:0,i<-1?e[r++]=this.DV+i:i>0&&(e[r++]=i),e.t=r,e.clamp()}function _(t,e){var r=this.abs(),i=t.abs(),n=r.t;e.t=n+i.t;while(--n>=0)e[n]=0;for(n=0;n<i.t;++n)e[n+r.t]=r.am(0,i[n],e,n,0,r.t);e.s=0,e.clamp(),this.s!=t.s&&d.ZERO.subTo(e,e)}function U(t){var e=this.abs(),r=t.t=2*e.t;while(--r>=0)t[r]=0;for(r=0;r<e.t-1;++r){var i=e.am(r,e[r],t,2*r,0,1);(t[r+e.t]+=e.am(r+1,2*e[r],t,2*r+1,i,e.t-r-1))>=e.DV&&(t[r+e.t]-=e.DV,t[r+e.t+1]=1)}t.t>0&&(t[t.t-1]+=e.am(r,e[r],t,2*r,0,1)),t.s=0,t.clamp()}function G(t,e,r){var i=t.abs();if(!(i.t<=0)){var n=this.abs();if(n.t<i.t)return null!=e&&e.fromInt(0),void(null!=r&&this.copyTo(r));null==r&&(r=v());var s=v(),a=this.s,o=t.s,h=this.DB-j(i[i.t-1]);h>0?(i.lShiftTo(h,s),n.lShiftTo(h,r)):(i.copyTo(s),n.copyTo(r));var u=s.t,c=s[u-1];if(0!=c){var l=c*(1<<this.F1)+(u>1?s[u-2]>>this.F2:0),f=this.FV/l,g=(1<<this.F1)/l,p=1<<this.F2,m=r.t,y=m-u,x=null==e?v():e;s.dlShiftTo(y,x),r.compareTo(x)>=0&&(r[r.t++]=1,r.subTo(x,r)),d.ONE.dlShiftTo(u,x),x.subTo(s,s);while(s.t<u)s[s.t++]=0;while(--y>=0){var S=r[--m]==c?this.DM:Math.floor(r[m]*f+(r[m-1]+p)*g);if((r[m]+=s.am(0,S,r,y,0,u))<S){s.dlShiftTo(y,x),r.subTo(x,r);while(r[m]<--S)r.subTo(x,r)}}null!=e&&(r.drShiftTo(u,e),a!=o&&d.ZERO.subTo(e,e)),r.t=u,r.clamp(),h>0&&r.rShiftTo(h,r),a<0&&d.ZERO.subTo(r,r)}}}function z(t){var e=v();return this.abs().divRemTo(t,null,e),this.s<0&&e.compareTo(d.ZERO)>0&&t.subTo(e,e),e}function W(t){this.m=t}function J(t){return t.s<0||t.compareTo(this.m)>=0?t.mod(this.m):t}function X(t){return t}function $(t){t.divRemTo(this.m,null,t)}function Y(t,e,r){t.multiplyTo(e,r),this.reduce(r)}function Z(t,e){t.squareTo(e),this.reduce(e)}function Q(){if(this.t<1)return 0;var t=this[0];if(0==(1&t))return 0;var e=3&t;return e=e*(2-(15&t)*e)&15,e=e*(2-(255&t)*e)&255,e=e*(2-((65535&t)*e&65535))&65535,e=e*(2-t*e%this.DV)%this.DV,e>0?this.DV-e:-e}function tt(t){this.m=t,this.mp=t.invDigit(),this.mpl=32767&this.mp,this.mph=this.mp>>15,this.um=(1<<t.DB-15)-1,this.mt2=2*t.t}function et(t){var e=v();return t.abs().dlShiftTo(this.m.t,e),e.divRemTo(this.m,null,e),t.s<0&&e.compareTo(d.ZERO)>0&&this.m.subTo(e,e),e}function rt(t){var e=v();return t.copyTo(e),this.reduce(e),e}function it(t){while(t.t<=this.mt2)t[t.t++]=0;for(var e=0;e<this.m.t;++e){var r=32767&t[e],i=r*this.mpl+((r*this.mph+(t[e]>>15)*this.mpl&this.um)<<15)&t.DM;r=e+this.m.t,t[r]+=this.m.am(0,i,t,e,0,this.m.t);while(t[r]>=t.DV)t[r]-=t.DV,t[++r]++}t.clamp(),t.drShiftTo(this.m.t,t),t.compareTo(this.m)>=0&&t.subTo(this.m,t)}function nt(t,e){t.squareTo(e),this.reduce(e)}function st(t,e,r){t.multiplyTo(e,r),this.reduce(r)}function at(){return 0==(this.t>0?1&this[0]:this.s)}function ot(t,e){if(t>4294967295||t<1)return d.ONE;var r=v(),i=v(),n=e.convert(this),s=j(t)-1;n.copyTo(r);while(--s>=0)if(e.sqrTo(r,i),(t&1<<s)>0)e.mulTo(i,n,r);else{var a=r;r=i,i=a}return e.revert(r)}function ht(t,e){var r;return r=t<256||e.isEven()?new W(e):new tt(e),this.exp(t,r)}
/*! (c) Tom Wu | http://www-cs-students.stanford.edu/~tjw/jsbn/
 */
function ut(){var t=v();return this.copyTo(t),t}function ct(){if(this.s<0){if(1==this.t)return this[0]-this.DV;if(0==this.t)return-1}else{if(1==this.t)return this[0];if(0==this.t)return 0}return(this[1]&(1<<32-this.DB)-1)<<this.DB|this[0]}function lt(){return 0==this.t?this.s:this[0]<<24>>24}function ft(){return 0==this.t?this.s:this[0]<<16>>16}function gt(t){return Math.floor(Math.LN2*this.DB/Math.log(t))}function pt(){return this.s<0?-1:this.t<=0||1==this.t&&this[0]<=0?0:1}function dt(t){if(null==t&&(t=10),0==this.signum()||t<2||t>36)return"0";var e=this.chunkSize(t),r=Math.pow(t,e),i=P(r),n=v(),s=v(),a="";this.divRemTo(i,n,s);while(n.signum()>0)a=(r+s.intValue()).toString(t).substr(1)+a,n.divRemTo(i,n,s);return s.intValue().toString(t)+a}function vt(t,e){this.fromInt(0),null==e&&(e=10);for(var r=this.chunkSize(e),i=Math.pow(e,r),n=!1,s=0,a=0,o=0;o<t.length;++o){var h=D(t,o);h<0?"-"==t.charAt(o)&&0==this.signum()&&(n=!0):(a=e*a+h,++s>=r&&(this.dMultiply(i),this.dAddOffset(a,0),s=0,a=0))}s>0&&(this.dMultiply(Math.pow(e,s)),this.dAddOffset(a,0)),n&&d.ZERO.subTo(this,this)}function mt(t,e,r){if("number"==typeof e)if(t<2)this.fromInt(1);else{this.fromNumber(t,r),this.testBit(t-1)||this.bitwiseTo(d.ONE.shiftLeft(t-1),At,this),this.isEven()&&this.dAddOffset(1,0);while(!this.isProbablePrime(e))this.dAddOffset(2,0),this.bitLength()>t&&this.subTo(d.ONE.shiftLeft(t-1),this)}else{var i=new Array,n=7&t;i.length=1+(t>>3),e.nextBytes(i),n>0?i[0]&=(1<<n)-1:i[0]=0,this.fromString(i,256)}}function yt(){var t=this.t,e=new Array;e[0]=this.s;var r,i=this.DB-t*this.DB%8,n=0;if(t-- >0){i<this.DB&&(r=this[t]>>i)!=(this.s&this.DM)>>i&&(e[n++]=r|this.s<<this.DB-i);while(t>=0)i<8?(r=(this[t]&(1<<i)-1)<<8-i,r|=this[--t]>>(i+=this.DB-8)):(r=this[t]>>(i-=8)&255,i<=0&&(i+=this.DB,--t)),0!=(128&r)&&(r|=-256),0==n&&(128&this.s)!=(128&r)&&++n,(n>0||r!=this.s)&&(e[n++]=r)}return e}function xt(t){return 0==this.compareTo(t)}function St(t){return this.compareTo(t)<0?this:t}function wt(t){return this.compareTo(t)>0?this:t}function Et(t,e,r){var i,n,s=Math.min(t.t,this.t);for(i=0;i<s;++i)r[i]=e(this[i],t[i]);if(t.t<this.t){for(n=t.s&this.DM,i=s;i<this.t;++i)r[i]=e(this[i],n);r.t=this.t}else{for(n=this.s&this.DM,i=s;i<t.t;++i)r[i]=e(n,t[i]);r.t=t.t}r.s=e(this.s,t.s),r.clamp()}function Ft(t,e){return t&e}function bt(t){var e=v();return this.bitwiseTo(t,Ft,e),e}function At(t,e){return t|e}function Dt(t){var e=v();return this.bitwiseTo(t,At,e),e}function It(t,e){return t^e}function Ct(t){var e=v();return this.bitwiseTo(t,It,e),e}function Pt(t,e){return t&~e}function Rt(t){var e=v();return this.bitwiseTo(t,Pt,e),e}function Tt(){for(var t=v(),e=0;e<this.t;++e)t[e]=this.DM&~this[e];return t.t=this.t,t.s=~this.s,t}function Bt(t){var e=v();return t<0?this.rShiftTo(-t,e):this.lShiftTo(t,e),e}function Nt(t){var e=v();return t<0?this.lShiftTo(-t,e):this.rShiftTo(t,e),e}function Ht(t){if(0==t)return-1;var e=0;return 0==(65535&t)&&(t>>=16,e+=16),0==(255&t)&&(t>>=8,e+=8),0==(15&t)&&(t>>=4,e+=4),0==(3&t)&&(t>>=2,e+=2),0==(1&t)&&++e,e}function Ot(){for(var t=0;t<this.t;++t)if(0!=this[t])return t*this.DB+Ht(this[t]);return this.s<0?this.t*this.DB:-1}function jt(t){var e=0;while(0!=t)t&=t-1,++e;return e}function Vt(){for(var t=0,e=this.s&this.DM,r=0;r<this.t;++r)t+=jt(this[r]^e);return t}function Kt(t){var e=Math.floor(t/this.DB);return e>=this.t?0!=this.s:0!=(this[e]&1<<t%this.DB)}function Lt(t,e){var r=d.ONE.shiftLeft(t);return this.bitwiseTo(r,e,r),r}function kt(t){return this.changeBit(t,At)}function qt(t){return this.changeBit(t,Pt)}function Mt(t){return this.changeBit(t,It)}function _t(t,e){var r=0,i=0,n=Math.min(t.t,this.t);while(r<n)i+=this[r]+t[r],e[r++]=i&this.DM,i>>=this.DB;if(t.t<this.t){i+=t.s;while(r<this.t)i+=this[r],e[r++]=i&this.DM,i>>=this.DB;i+=this.s}else{i+=this.s;while(r<t.t)i+=t[r],e[r++]=i&this.DM,i>>=this.DB;i+=t.s}e.s=i<0?-1:0,i>0?e[r++]=i:i<-1&&(e[r++]=this.DV+i),e.t=r,e.clamp()}function Ut(t){var e=v();return this.addTo(t,e),e}function Gt(t){var e=v();return this.subTo(t,e),e}function zt(t){var e=v();return this.multiplyTo(t,e),e}function Wt(){var t=v();return this.squareTo(t),t}function Jt(t){var e=v();return this.divRemTo(t,e,null),e}function Xt(t){var e=v();return this.divRemTo(t,null,e),e}function $t(t){var e=v(),r=v();return this.divRemTo(t,e,r),new Array(e,r)}function Yt(t){this[this.t]=this.am(0,t-1,this,0,0,this.t),++this.t,this.clamp()}function Zt(t,e){if(0!=t){while(this.t<=e)this[this.t++]=0;this[e]+=t;while(this[e]>=this.DV)this[e]-=this.DV,++e>=this.t&&(this[this.t++]=0),++this[e]}}function Qt(){}function te(t){return t}function ee(t,e,r){t.multiplyTo(e,r)}function re(t,e){t.squareTo(e)}function ie(t){return this.exp(t,new Qt)}function ne(t,e,r){var i,n=Math.min(this.t+t.t,e);r.s=0,r.t=n;while(n>0)r[--n]=0;for(i=r.t-this.t;n<i;++n)r[n+this.t]=this.am(0,t[n],r,n,0,this.t);for(i=Math.min(t.t,e);n<i;++n)this.am(0,t[n],r,n,0,e-n);r.clamp()}function se(t,e,r){--e;var i=r.t=this.t+t.t-e;r.s=0;while(--i>=0)r[i]=0;for(i=Math.max(e-this.t,0);i<t.t;++i)r[this.t+i-e]=this.am(e-i,t[i],r,0,0,this.t+i-e);r.clamp(),r.drShiftTo(1,r)}function ae(t){this.r2=v(),this.q3=v(),d.ONE.dlShiftTo(2*t.t,this.r2),this.mu=this.r2.divide(t),this.m=t}function oe(t){if(t.s<0||t.t>2*this.m.t)return t.mod(this.m);if(t.compareTo(this.m)<0)return t;var e=v();return t.copyTo(e),this.reduce(e),e}function he(t){return t}function ue(t){t.drShiftTo(this.m.t-1,this.r2),t.t>this.m.t+1&&(t.t=this.m.t+1,t.clamp()),this.mu.multiplyUpperTo(this.r2,this.m.t+1,this.q3),this.m.multiplyLowerTo(this.q3,this.m.t+1,this.r2);while(t.compareTo(this.r2)<0)t.dAddOffset(1,this.m.t+1);t.subTo(this.r2,t);while(t.compareTo(this.m)>=0)t.subTo(this.m,t)}function ce(t,e){t.squareTo(e),this.reduce(e)}function le(t,e,r){t.multiplyTo(e,r),this.reduce(r)}function fe(t,e){var r,i,n=t.bitLength(),s=P(1);if(n<=0)return s;r=n<18?1:n<48?3:n<144?4:n<768?5:6,i=n<8?new W(e):e.isEven()?new ae(e):new tt(e);var a=new Array,o=3,h=r-1,u=(1<<r)-1;if(a[1]=i.convert(this),r>1){var c=v();i.sqrTo(a[1],c);while(o<=u)a[o]=v(),i.mulTo(c,a[o-2],a[o]),o+=2}var l,f,g=t.t-1,p=!0,d=v();n=j(t[g])-1;while(g>=0){n>=h?l=t[g]>>n-h&u:(l=(t[g]&(1<<n+1)-1)<<h-n,g>0&&(l|=t[g-1]>>this.DB+n-h)),o=r;while(0==(1&l))l>>=1,--o;if((n-=o)<0&&(n+=this.DB,--g),p)a[l].copyTo(s),p=!1;else{while(o>1)i.sqrTo(s,d),i.sqrTo(d,s),o-=2;o>0?i.sqrTo(s,d):(f=s,s=d,d=f),i.mulTo(d,a[l],s)}while(g>=0&&0==(t[g]&1<<n))i.sqrTo(s,d),f=s,s=d,d=f,--n<0&&(n=this.DB-1,--g)}return i.revert(s)}function ge(t){var e=this.s<0?this.negate():this.clone(),r=t.s<0?t.negate():t.clone();if(e.compareTo(r)<0){var i=e;e=r,r=i}var n=e.getLowestSetBit(),s=r.getLowestSetBit();if(s<0)return e;n<s&&(s=n),s>0&&(e.rShiftTo(s,e),r.rShiftTo(s,r));while(e.signum()>0)(n=e.getLowestSetBit())>0&&e.rShiftTo(n,e),(n=r.getLowestSetBit())>0&&r.rShiftTo(n,r),e.compareTo(r)>=0?(e.subTo(r,e),e.rShiftTo(1,e)):(r.subTo(e,r),r.rShiftTo(1,r));return s>0&&r.lShiftTo(s,r),r}function pe(t){if(t<=0)return 0;var e=this.DV%t,r=this.s<0?t-1:0;if(this.t>0)if(0==e)r=this[0]%t;else for(var i=this.t-1;i>=0;--i)r=(e*r+this[i])%t;return r}function de(t){var e=t.isEven();if(this.isEven()&&e||0==t.signum())return d.ZERO;var r=t.clone(),i=this.clone(),n=P(1),s=P(0),a=P(0),o=P(1);while(0!=r.signum()){while(r.isEven())r.rShiftTo(1,r),e?(n.isEven()&&s.isEven()||(n.addTo(this,n),s.subTo(t,s)),n.rShiftTo(1,n)):s.isEven()||s.subTo(t,s),s.rShiftTo(1,s);while(i.isEven())i.rShiftTo(1,i),e?(a.isEven()&&o.isEven()||(a.addTo(this,a),o.subTo(t,o)),a.rShiftTo(1,a)):o.isEven()||o.subTo(t,o),o.rShiftTo(1,o);r.compareTo(i)>=0?(r.subTo(i,r),e&&n.subTo(a,n),s.subTo(o,s)):(i.subTo(r,i),e&&a.subTo(n,a),o.subTo(s,o))}return 0!=i.compareTo(d.ONE)?d.ZERO:o.compareTo(t)>=0?o.subtract(t):o.signum()<0?(o.addTo(t,o),o.signum()<0?o.add(t):o):o}W.prototype.convert=J,W.prototype.revert=X,W.prototype.reduce=$,W.prototype.mulTo=Y,W.prototype.sqrTo=Z,tt.prototype.convert=et,tt.prototype.revert=rt,tt.prototype.reduce=it,tt.prototype.mulTo=st,tt.prototype.sqrTo=nt,d.prototype.copyTo=I,d.prototype.fromInt=C,d.prototype.fromString=R,d.prototype.clamp=T,d.prototype.dlShiftTo=K,d.prototype.drShiftTo=L,d.prototype.lShiftTo=k,d.prototype.rShiftTo=q,d.prototype.subTo=M,d.prototype.multiplyTo=_,d.prototype.squareTo=U,d.prototype.divRemTo=G,d.prototype.invDigit=Q,d.prototype.isEven=at,d.prototype.exp=ot,d.prototype.toString=B,d.prototype.negate=N,d.prototype.abs=H,d.prototype.compareTo=O,d.prototype.bitLength=V,d.prototype.mod=z,d.prototype.modPowInt=ht,d.ZERO=P(0),d.ONE=P(1),Qt.prototype.convert=te,Qt.prototype.revert=te,Qt.prototype.mulTo=ee,Qt.prototype.sqrTo=re,ae.prototype.convert=oe,ae.prototype.revert=he,ae.prototype.reduce=ue,ae.prototype.mulTo=le,ae.prototype.sqrTo=ce;var ve=[2,3,5,7,11,13,17,19,23,29,31,37,41,43,47,53,59,61,67,71,73,79,83,89,97,101,103,107,109,113,127,131,137,139,149,151,157,163,167,173,179,181,191,193,197,199,211,223,227,229,233,239,241,251,257,263,269,271,277,281,283,293,307,311,313,317,331,337,347,349,353,359,367,373,379,383,389,397,401,409,419,421,431,433,439,443,449,457,461,463,467,479,487,491,499,503,509,521,523,541,547,557,563,569,571,577,587,593,599,601,607,613,617,619,631,641,643,647,653,659,661,673,677,683,691,701,709,719,727,733,739,743,751,757,761,769,773,787,797,809,811,821,823,827,829,839,853,857,859,863,877,881,883,887,907,911,919,929,937,941,947,953,967,971,977,983,991,997],me=(1<<26)/ve[ve.length-1];function ye(t){var e,r=this.abs();if(1==r.t&&r[0]<=ve[ve.length-1]){for(e=0;e<ve.length;++e)if(r[0]==ve[e])return!0;return!1}if(r.isEven())return!1;e=1;while(e<ve.length){var i=ve[e],n=e+1;while(n<ve.length&&i<me)i*=ve[n++];i=r.modInt(i);while(e<n)if(i%ve[e++]==0)return!1}return r.millerRabin(t)}function xe(t){var e=this.subtract(d.ONE),r=e.getLowestSetBit();if(r<=0)return!1;var i=e.shiftRight(r);t=t+1>>1,t>ve.length&&(t=ve.length);for(var n=v(),s=0;s<t;++s){n.fromInt(ve[Math.floor(Math.random()*ve.length)]);var a=n.modPow(i,this);if(0!=a.compareTo(d.ONE)&&0!=a.compareTo(e)){var o=1;while(o++<r&&0!=a.compareTo(e))if(a=a.modPowInt(2,this),0==a.compareTo(d.ONE))return!1;if(0!=a.compareTo(e))return!1}}return!0}
/*! (c) Tom Wu | http://www-cs-students.stanford.edu/~tjw/jsbn/
 */
function Se(){this.i=0,this.j=0,this.S=new Array}function we(t){var e,r,i;for(e=0;e<256;++e)this.S[e]=e;for(r=0,e=0;e<256;++e)r=r+this.S[e]+t[e%t.length]&255,i=this.S[e],this.S[e]=this.S[r],this.S[r]=i;this.i=0,this.j=0}function Ee(){var t;return this.i=this.i+1&255,this.j=this.j+this.S[this.i]&255,t=this.S[this.i],this.S[this.i]=this.S[this.j],this.S[this.j]=t,this.S[t+this.S[this.i]&255]}function Fe(){return new Se}d.prototype.chunkSize=gt,d.prototype.toRadix=dt,d.prototype.fromRadix=vt,d.prototype.fromNumber=mt,d.prototype.bitwiseTo=Et,d.prototype.changeBit=Lt,d.prototype.addTo=_t,d.prototype.dMultiply=Yt,d.prototype.dAddOffset=Zt,d.prototype.multiplyLowerTo=ne,d.prototype.multiplyUpperTo=se,d.prototype.modInt=pe,d.prototype.millerRabin=xe,d.prototype.clone=ut,d.prototype.intValue=ct,d.prototype.byteValue=lt,d.prototype.shortValue=ft,d.prototype.signum=pt,d.prototype.toByteArray=yt,d.prototype.equals=xt,d.prototype.min=St,d.prototype.max=wt,d.prototype.and=bt,d.prototype.or=Dt,d.prototype.xor=Ct,d.prototype.andNot=Rt,d.prototype.not=Tt,d.prototype.shiftLeft=Bt,d.prototype.shiftRight=Nt,d.prototype.getLowestSetBit=Ot,d.prototype.bitCount=Vt,d.prototype.testBit=Kt,d.prototype.setBit=kt,d.prototype.clearBit=qt,d.prototype.flipBit=Mt,d.prototype.add=Ut,d.prototype.subtract=Gt,d.prototype.multiply=zt,d.prototype.divide=Jt,d.prototype.remainder=Xt,d.prototype.divideAndRemainder=$t,d.prototype.modPow=fe,d.prototype.modInverse=de,d.prototype.pow=ie,d.prototype.gcd=ge,d.prototype.isProbablePrime=ye,d.prototype.square=Wt,Se.prototype.init=we,Se.prototype.next=Ee;var be,Ae,De,Ie=256;
/*! (c) Tom Wu | http://www-cs-students.stanford.edu/~tjw/jsbn/
 */function Ce(t){Ae[De++]^=255&t,Ae[De++]^=t>>8&255,Ae[De++]^=t>>16&255,Ae[De++]^=t>>24&255,De>=Ie&&(De-=Ie)}function Pe(){Ce((new Date).getTime())}if(null==Ae){var Re;if(Ae=new Array,De=0,void 0!==s&&(void 0!==s.crypto||void 0!==s.msCrypto)){var Te=s.crypto||s.msCrypto;if(Te.getRandomValues){var Be=new Uint8Array(32);for(Te.getRandomValues(Be),Re=0;Re<32;++Re)Ae[De++]=Be[Re]}else if("Netscape"==n.appName&&n.appVersion<"5"){var Ne=s.crypto.random(32);for(Re=0;Re<Ne.length;++Re)Ae[De++]=255&Ne.charCodeAt(Re)}}while(De<Ie)Re=Math.floor(65536*Math.random()),Ae[De++]=Re>>>8,Ae[De++]=255&Re;De=0,Pe()}function He(){if(null==be){for(Pe(),be=Fe(),be.init(Ae),De=0;De<Ae.length;++De)Ae[De]=0;De=0}return be.next()}function Oe(t){var e;for(e=0;e<t.length;++e)t[e]=He()}function je(){}
/*! (c) Tom Wu | http://www-cs-students.stanford.edu/~tjw/jsbn/
 */
function Ve(t,e){return new d(t,e)}function Ke(t,e){if(e<t.length+11)throw"Message too long for RSA";var r=new Array,i=t.length-1;while(i>=0&&e>0){var n=t.charCodeAt(i--);n<128?r[--e]=n:n>127&&n<2048?(r[--e]=63&n|128,r[--e]=n>>6|192):(r[--e]=63&n|128,r[--e]=n>>6&63|128,r[--e]=n>>12|224)}r[--e]=0;var s=new je,a=new Array;while(e>2){a[0]=0;while(0==a[0])s.nextBytes(a);r[--e]=a[0]}return r[--e]=2,r[--e]=0,new d(r)}function Le(t,e,r){var i="",n=0;while(i.length<e)i+=r(String.fromCharCode.apply(String,t.concat([(4278190080&n)>>24,(16711680&n)>>16,(65280&n)>>8,255&n]))),n+=1;return i}function ke(t,e,r,i){var n=Pr.crypto.MessageDigest,s=Pr.crypto.Util,a=null;if(r||(r="sha1"),"string"===typeof r&&(a=n.getCanonicalAlgName(r),i=n.getHashLength(a),r=function(t){return $r(s.hashHex(Yr(t),a))}),t.length+2*i+2>e)throw"Message too long for RSA";var o,h="";for(o=0;o<e-t.length-2*i-2;o+=1)h+="\0";var u=r("")+h+""+t,c=new Array(i);(new je).nextBytes(c);var l=Le(c,u.length,r),f=[];for(o=0;o<u.length;o+=1)f[o]=u.charCodeAt(o)^l.charCodeAt(o);var g=Le(f,c.length,r),p=[0];for(o=0;o<c.length;o+=1)p[o+1]=c[o]^g.charCodeAt(o);return new d(p.concat(f))}function qe(){this.n=null,this.e=0,this.d=null,this.p=null,this.q=null,this.dmp1=null,this.dmq1=null,this.coeff=null}function Me(t,e){if(this.isPublic=!0,this.isPrivate=!1,"string"!==typeof t)this.n=t,this.e=e;else{if(!(null!=t&&null!=e&&t.length>0&&e.length>0))throw"Invalid RSA public key";this.n=Ve(t,16),this.e=parseInt(e,16)}}function _e(t){return t.modPowInt(this.e,this.n)}function Ue(t){var e=Ke(t,this.n.bitLength()+7>>3);if(null==e)return null;var r=this.doPublic(e);if(null==r)return null;var i=r.toString(16);return 0==(1&i.length)?i:"0"+i}function Ge(t,e,r){var i=ke(t,this.n.bitLength()+7>>3,e,r);if(null==i)return null;var n=this.doPublic(i);if(null==n)return null;var s=n.toString(16);return 0==(1&s.length)?s:"0"+s}
/*! (c) Tom Wu, Kenji Urushima | http://www-cs-students.stanford.edu/~tjw/jsbn/
 */
function ze(t,e){var r=t.toByteArray(),i=0;while(i<r.length&&0==r[i])++i;if(r.length-i!=e-1||2!=r[i])return null;++i;while(0!=r[i])if(++i>=r.length)return null;var n="";while(++i<r.length){var s=255&r[i];s<128?n+=String.fromCharCode(s):s>191&&s<224?(n+=String.fromCharCode((31&s)<<6|63&r[i+1]),++i):(n+=String.fromCharCode((15&s)<<12|(63&r[i+1])<<6|63&r[i+2]),i+=2)}return n}function We(t,e,r){var i="",n=0;while(i.length<e)i+=r(t+String.fromCharCode.apply(String,[(4278190080&n)>>24,(16711680&n)>>16,(65280&n)>>8,255&n])),n+=1;return i}function Je(t,e,r,i){var n=Pr.crypto.MessageDigest,s=Pr.crypto.Util,a=null;for(r||(r="sha1"),"string"===typeof r&&(a=n.getCanonicalAlgName(r),i=n.getHashLength(a),r=function(t){return $r(s.hashHex(Yr(t),a))}),t=t.toByteArray(),o=0;o<t.length;o+=1)t[o]&=255;while(t.length<e)t.unshift(0);if(t=String.fromCharCode.apply(String,t),t.length<2*i+2)throw"Cipher too short";var o,h=t.substr(1,i),u=t.substr(i+1),c=We(u,i,r),l=[];for(o=0;o<h.length;o+=1)l[o]=h.charCodeAt(o)^c.charCodeAt(o);var f=We(String.fromCharCode.apply(String,l),t.length-i,r),g=[];for(o=0;o<u.length;o+=1)g[o]=u.charCodeAt(o)^f.charCodeAt(o);if(g=String.fromCharCode.apply(String,g),g.substr(0,i)!==r(""))throw"Hash mismatch";g=g.substr(i);var p=g.indexOf(""),d=-1!=p?g.substr(0,p).lastIndexOf("\0"):-1;if(d+1!=p)throw"Malformed data";return g.substr(p+1)}function Xe(t,e,r){if(this.isPrivate=!0,"string"!==typeof t)this.n=t,this.e=e,this.d=r;else{if(!(null!=t&&null!=e&&t.length>0&&e.length>0))throw"Invalid RSA private key";this.n=Ve(t,16),this.e=parseInt(e,16),this.d=Ve(r,16)}}function $e(t,e,r,i,n,s,a,o){if(this.isPrivate=!0,this.isPublic=!1,null==t)throw"RSASetPrivateEx N == null";if(null==e)throw"RSASetPrivateEx E == null";if(0==t.length)throw"RSASetPrivateEx N.length == 0";if(0==e.length)throw"RSASetPrivateEx E.length == 0";if(!(null!=t&&null!=e&&t.length>0&&e.length>0))throw"Invalid RSA private key in RSASetPrivateEx";this.n=Ve(t,16),this.e=parseInt(e,16),this.d=Ve(r,16),this.p=Ve(i,16),this.q=Ve(n,16),this.dmp1=Ve(s,16),this.dmq1=Ve(a,16),this.coeff=Ve(o,16)}function Ye(t,e){var r=new je,i=t>>1;this.e=parseInt(e,16);for(var n=new d(e,16),s=t/2-100,a=d.ONE.shiftLeft(s);;){for(;;)if(this.p=new d(t-i,1,r),0==this.p.subtract(d.ONE).gcd(n).compareTo(d.ONE)&&this.p.isProbablePrime(10))break;for(;;)if(this.q=new d(i,1,r),0==this.q.subtract(d.ONE).gcd(n).compareTo(d.ONE)&&this.q.isProbablePrime(10))break;if(this.p.compareTo(this.q)<=0){var o=this.p;this.p=this.q,this.q=o}var h=this.q.subtract(this.p).abs();if(!(h.bitLength()<s||h.compareTo(a)<=0)){var u=this.p.subtract(d.ONE),c=this.q.subtract(d.ONE),l=u.multiply(c);if(0==l.gcd(n).compareTo(d.ONE)&&(this.n=this.p.multiply(this.q),this.n.bitLength()==t)){this.d=n.modInverse(l),this.dmp1=this.d.mod(u),this.dmq1=this.d.mod(c),this.coeff=this.q.modInverse(this.p);break}}}this.isPrivate=!0}function Ze(t){if(null==this.p||null==this.q)return t.modPow(this.d,this.n);var e=t.mod(this.p).modPow(this.dmp1,this.p),r=t.mod(this.q).modPow(this.dmq1,this.q);while(e.compareTo(r)<0)e=e.add(this.p);return e.subtract(r).multiply(this.coeff).mod(this.p).multiply(this.q).add(r)}function Qe(t){if(t.length!=Math.ceil(this.n.bitLength()/4))throw new Error("wrong ctext length");var e=Ve(t,16),r=this.doPrivate(e);return null==r?null:ze(r,this.n.bitLength()+7>>3)}function tr(t,e,r){if(t.length!=Math.ceil(this.n.bitLength()/4))throw new Error("wrong ctext length");var i=Ve(t,16),n=this.doPrivate(i);return null==n?null:Je(n,this.n.bitLength()+7>>3,e,r)}
/*! (c) Tom Wu | http://www-cs-students.stanford.edu/~tjw/jsbn/
 */
function er(t,e){this.x=e,this.q=t}function rr(t){return t==this||this.q.equals(t.q)&&this.x.equals(t.x)}function ir(){return this.x}function nr(){return new er(this.q,this.x.negate().mod(this.q))}function sr(t){return new er(this.q,this.x.add(t.toBigInteger()).mod(this.q))}function ar(t){return new er(this.q,this.x.subtract(t.toBigInteger()).mod(this.q))}function or(t){return new er(this.q,this.x.multiply(t.toBigInteger()).mod(this.q))}function hr(){return new er(this.q,this.x.square().mod(this.q))}function ur(t){return new er(this.q,this.x.multiply(t.toBigInteger().modInverse(this.q)).mod(this.q))}function cr(t,e,r,i){this.curve=t,this.x=e,this.y=r,this.z=null==i?d.ONE:i,this.zinv=null}function lr(){return null==this.zinv&&(this.zinv=this.z.modInverse(this.curve.q)),this.curve.fromBigInteger(this.x.toBigInteger().multiply(this.zinv).mod(this.curve.q))}function fr(){return null==this.zinv&&(this.zinv=this.z.modInverse(this.curve.q)),this.curve.fromBigInteger(this.y.toBigInteger().multiply(this.zinv).mod(this.curve.q))}function gr(t){return t==this||(this.isInfinity()?t.isInfinity():t.isInfinity()?this.isInfinity():(e=t.y.toBigInteger().multiply(this.z).subtract(this.y.toBigInteger().multiply(t.z)).mod(this.curve.q),!!e.equals(d.ZERO)&&(r=t.x.toBigInteger().multiply(this.z).subtract(this.x.toBigInteger().multiply(t.z)).mod(this.curve.q),r.equals(d.ZERO))));var e,r}function pr(){return null==this.x&&null==this.y||this.z.equals(d.ZERO)&&!this.y.toBigInteger().equals(d.ZERO)}function dr(){return new cr(this.curve,this.x,this.y.negate(),this.z)}function vr(t){if(this.isInfinity())return t;if(t.isInfinity())return this;var e=t.y.toBigInteger().multiply(this.z).subtract(this.y.toBigInteger().multiply(t.z)).mod(this.curve.q),r=t.x.toBigInteger().multiply(this.z).subtract(this.x.toBigInteger().multiply(t.z)).mod(this.curve.q);if(d.ZERO.equals(r))return d.ZERO.equals(e)?this.twice():this.curve.getInfinity();var i=new d("3"),n=this.x.toBigInteger(),s=this.y.toBigInteger(),a=(t.x.toBigInteger(),t.y.toBigInteger(),r.square()),o=a.multiply(r),h=n.multiply(a),u=e.square().multiply(this.z),c=u.subtract(h.shiftLeft(1)).multiply(t.z).subtract(o).multiply(r).mod(this.curve.q),l=h.multiply(i).multiply(e).subtract(s.multiply(o)).subtract(u.multiply(e)).multiply(t.z).add(e.multiply(o)).mod(this.curve.q),f=o.multiply(this.z).multiply(t.z).mod(this.curve.q);return new cr(this.curve,this.curve.fromBigInteger(c),this.curve.fromBigInteger(l),f)}function mr(){if(this.isInfinity())return this;if(0==this.y.toBigInteger().signum())return this.curve.getInfinity();var t=new d("3"),e=this.x.toBigInteger(),r=this.y.toBigInteger(),i=r.multiply(this.z),n=i.multiply(r).mod(this.curve.q),s=this.curve.a.toBigInteger(),a=e.square().multiply(t);d.ZERO.equals(s)||(a=a.add(this.z.square().multiply(s))),a=a.mod(this.curve.q);var o=a.square().subtract(e.shiftLeft(3).multiply(n)).shiftLeft(1).multiply(i).mod(this.curve.q),h=a.multiply(t).multiply(e).subtract(n.shiftLeft(1)).shiftLeft(2).multiply(n).subtract(a.square().multiply(a)).mod(this.curve.q),u=i.square().multiply(i).shiftLeft(3).mod(this.curve.q);return new cr(this.curve,this.curve.fromBigInteger(o),this.curve.fromBigInteger(h),u)}function yr(t){if(this.isInfinity())return this;if(0==t.signum())return this.curve.getInfinity();var e,r=t,i=r.multiply(new d("3")),n=this.negate(),s=this,a=this.curve.q.subtract(t),o=a.multiply(new d("3")),h=new cr(this.curve,this.x,this.y),u=h.negate();for(e=i.bitLength()-2;e>0;--e){s=s.twice();var c=i.testBit(e),l=r.testBit(e);c!=l&&(s=s.add(c?this:n))}for(e=o.bitLength()-2;e>0;--e){h=h.twice();var f=o.testBit(e),g=a.testBit(e);f!=g&&(h=h.add(f?h:u))}return s}function xr(t,e,r){var i;i=t.bitLength()>r.bitLength()?t.bitLength()-1:r.bitLength()-1;var n=this.curve.getInfinity(),s=this.add(e);while(i>=0)n=n.twice(),t.testBit(i)?n=r.testBit(i)?n.add(s):n.add(this):r.testBit(i)&&(n=n.add(e)),--i;return n}function Sr(t,e,r){this.q=t,this.a=this.fromBigInteger(e),this.b=this.fromBigInteger(r),this.infinity=new cr(this,null,null)}function wr(){return this.q}function Er(){return this.a}function Fr(){return this.b}function br(t){return t==this||this.q.equals(t.q)&&this.a.equals(t.a)&&this.b.equals(t.b)}function Ar(){return this.infinity}function Dr(t){return new er(this.q,t)}function Ir(t){switch(parseInt(t.substr(0,2),16)){case 0:return this.infinity;case 2:case 3:var e=t.substr(0,2),r=(t.substr(2),this.fromBigInteger(new d(h,16))),i=this.getA(),n=this.getB(),s=r.square().add(i).multiply(r).add(n),a=s.sqrt();return"03"==e&&(a=a.negate()),new cr(this,r,a);case 4:case 6:case 7:var o=(t.length-2)/2,h=t.substr(2,o),u=t.substr(o+2,o);return new cr(this,this.fromBigInteger(new d(h,16)),this.fromBigInteger(new d(u,16)));default:return null}}je.prototype.nextBytes=Oe,qe.prototype.doPublic=_e,qe.prototype.setPublic=Me,qe.prototype.encrypt=Ue,qe.prototype.encryptOAEP=Ge,qe.prototype.type="RSA",qe.prototype.doPrivate=Ze,qe.prototype.setPrivate=Xe,qe.prototype.setPrivateEx=$e,qe.prototype.generate=Ye,qe.prototype.decrypt=Qe,qe.prototype.decryptOAEP=tr,er.prototype.equals=rr,er.prototype.toBigInteger=ir,er.prototype.negate=nr,er.prototype.add=sr,er.prototype.subtract=ar,er.prototype.multiply=or,er.prototype.square=hr,er.prototype.divide=ur,er.prototype.sqrt=function(){return new er(this.q,this.x.sqrt().mod(this.q))},cr.prototype.getX=lr,cr.prototype.getY=fr,cr.prototype.equals=gr,cr.prototype.isInfinity=pr,cr.prototype.negate=dr,cr.prototype.add=vr,cr.prototype.twice=mr,cr.prototype.multiply=yr,cr.prototype.multiplyTwo=xr,Sr.prototype.getQ=wr,Sr.prototype.getA=Er,Sr.prototype.getB=Fr,Sr.prototype.equals=br,Sr.prototype.getInfinity=Ar,Sr.prototype.fromBigInteger=Dr,Sr.prototype.decodePointHex=Ir,
/*! (c) Stefan Thomas | https://github.com/bitcoinjs/bitcoinjs-lib
 */
er.prototype.getByteLength=function(){return Math.floor((this.toBigInteger().bitLength()+7)/8)},cr.prototype.getEncoded=function(t){var e=function(t,e){var r=t.toByteArrayUnsigned();if(e<r.length)r=r.slice(r.length-e);else while(e>r.length)r.unshift(0);return r},r=this.getX().toBigInteger(),i=this.getY().toBigInteger(),n=e(r,32);return t?i.isEven()?n.unshift(2):n.unshift(3):(n.unshift(4),n=n.concat(e(i,32))),n},cr.decodeFrom=function(t,e){e[0];var r=e.length-1,i=e.slice(1,1+r/2),n=e.slice(1+r/2,1+r);i.unshift(0),n.unshift(0);var s=new d(i),a=new d(n);return new cr(t,t.fromBigInteger(s),t.fromBigInteger(a))},cr.decodeFromHex=function(t,e){e.substr(0,2);var r=e.length-2,i=e.substr(2,r/2),n=e.substr(2+r/2,r/2),s=new d(i,16),a=new d(n,16);return new cr(t,t.fromBigInteger(s),t.fromBigInteger(a))},cr.prototype.add2D=function(t){if(this.isInfinity())return t;if(t.isInfinity())return this;if(this.x.equals(t.x))return this.y.equals(t.y)?this.twice():this.curve.getInfinity();var e=t.x.subtract(this.x),r=t.y.subtract(this.y),i=r.divide(e),n=i.square().subtract(this.x).subtract(t.x),s=i.multiply(this.x.subtract(n)).subtract(this.y);return new cr(this.curve,n,s)},cr.prototype.twice2D=function(){if(this.isInfinity())return this;if(0==this.y.toBigInteger().signum())return this.curve.getInfinity();var t=this.curve.fromBigInteger(d.valueOf(2)),e=this.curve.fromBigInteger(d.valueOf(3)),r=this.x.square().multiply(e).add(this.curve.a).divide(this.y.multiply(t)),i=r.square().subtract(this.x.multiply(t)),n=r.multiply(this.x.subtract(i)).subtract(this.y);return new cr(this.curve,i,n)},cr.prototype.multiply2D=function(t){if(this.isInfinity())return this;if(0==t.signum())return this.curve.getInfinity();var e,r=t,i=r.multiply(new d("3")),n=this.negate(),s=this;for(e=i.bitLength()-2;e>0;--e){s=s.twice();var a=i.testBit(e),o=r.testBit(e);a!=o&&(s=s.add2D(a?this:n))}return s},cr.prototype.isOnCurve=function(){var t=this.getX().toBigInteger(),e=this.getY().toBigInteger(),r=this.curve.getA().toBigInteger(),i=this.curve.getB().toBigInteger(),n=this.curve.getQ(),s=e.multiply(e).mod(n),a=t.multiply(t).multiply(t).add(r.multiply(t)).add(i).mod(n);return s.equals(a)},cr.prototype.toString=function(){return"("+this.getX().toBigInteger().toString()+","+this.getY().toBigInteger().toString()+")"},cr.prototype.validate=function(){var t=this.curve.getQ();if(this.isInfinity())throw new Error("Point is at infinity.");var e=this.getX().toBigInteger(),r=this.getY().toBigInteger();if(e.compareTo(d.ONE)<0||e.compareTo(t.subtract(d.ONE))>0)throw new Error("x coordinate out of bounds");if(r.compareTo(d.ONE)<0||r.compareTo(t.subtract(d.ONE))>0)throw new Error("y coordinate out of bounds");if(!this.isOnCurve())throw new Error("Point is not on the curve.");if(this.multiply(t).isInfinity())throw new Error("Point is not a scalar multiple of G.");return!0};
/*! Mike Samuel (c) 2009 | code.google.com/p/json-sans-eval
 */
var Cr=function(){var t="(?:-?\\b(?:0|[1-9][0-9]*)(?:\\.[0-9]+)?(?:[eE][+-]?[0-9]+)?\\b)",e='(?:[^\\0-\\x08\\x0a-\\x1f"\\\\]|\\\\(?:["/\\\\bfnrt]|u[0-9A-Fa-f]{4}))',r='(?:"'+e+'*")',n=new RegExp("(?:false|true|null|[\\{\\}\\[\\]]|"+t+"|"+r+")","g"),s=new RegExp("\\\\(?:([^u])|u(.{4}))","g"),a={'"':'"',"/":"/","\\":"\\",b:"\b",f:"\f",n:"\n",r:"\r",t:"\t"};function o(t,e,r){return e?a[e]:String.fromCharCode(parseInt(r,16))}var h=new String(""),u="\\",c=Object.hasOwnProperty;return function(t,e){var r,a,l=t.match(n),f=l[0],g=!1;"{"===f?r={}:"["===f?r=[]:(r=[],g=!0);for(var p=[r],d=1-g,v=l.length;d<v;++d){var m;switch(f=l[d],f.charCodeAt(0)){default:m=p[0],m[a||m.length]=+f,a=void 0;break;case 34:if(f=f.substring(1,f.length-1),-1!==f.indexOf(u)&&(f=f.replace(s,o)),m=p[0],!a){if(!(m instanceof Array)){a=f||h;break}a=m.length}m[a]=f,a=void 0;break;case 91:m=p[0],p.unshift(m[a||m.length]=[]),a=void 0;break;case 93:p.shift();break;case 102:m=p[0],m[a||m.length]=!1,a=void 0;break;case 110:m=p[0],m[a||m.length]=null,a=void 0;break;case 116:m=p[0],m[a||m.length]=!0,a=void 0;break;case 123:m=p[0],p.unshift(m[a||m.length]={}),a=void 0;break;case 125:p.shift();break}}if(g){if(1!==p.length)throw new Error;r=r[0]}else if(p.length)throw new Error;if(e){var y=function t(r,n){var s=r[n];if(s&&"object"===i(s)){var a=null;for(var o in s)if(c.call(s,o)&&s!==r){var h=t(s,o);void 0!==h?s[o]=h:(a||(a=[]),a.push(o))}if(a)for(var u=a.length;--u>=0;)delete s[a[u]]}return e.call(r,n,s)};r=y({"":r},"")}return r}}();"undefined"!=typeof Pr&&Pr||(Pr={}),"undefined"!=typeof Pr.asn1&&Pr.asn1||(Pr.asn1={}),Pr.asn1.ASN1Util=new function(){this.integerToByteHex=function(t){var e=t.toString(16);return e.length%2==1&&(e="0"+e),e},this.bigIntToMinTwosComplementsHex=function(t){var e=t.toString(16);if("-"!=e.substr(0,1))e.length%2==1?e="0"+e:e.match(/^[0-7]/)||(e="00"+e);else{var r=e.substr(1),i=r.length;i%2==1?i+=1:e.match(/^[0-7]/)||(i+=2);for(var n="",s=0;s<i;s++)n+="f";var a=new d(n,16),o=a.xor(t).add(d.ONE);e=o.toString(16).replace(/^-/,"")}return e},this.getPEMStringFromHex=function(t,e){return ei(t,e)},this.newObject=function(t){var e=Pr,r=e.asn1,i=r.ASN1Object,n=r.DERBoolean,s=r.DERInteger,a=r.DERBitString,o=r.DEROctetString,h=r.DERNull,u=r.DERObjectIdentifier,c=r.DEREnumerated,l=r.DERUTF8String,f=r.DERNumericString,g=r.DERPrintableString,p=r.DERTeletexString,d=r.DERIA5String,v=r.DERUTCTime,m=r.DERGeneralizedTime,y=r.DERVisibleString,x=r.DERBMPString,S=r.DERSequence,w=r.DERSet,E=r.DERTaggedObject,F=r.ASN1Util.newObject;if(t instanceof r.ASN1Object)return t;var b=Object.keys(t);if(1!=b.length)throw new Error("key of param shall be only one.");var A=b[0];if(-1==":asn1:bool:int:bitstr:octstr:null:oid:enum:utf8str:numstr:prnstr:telstr:ia5str:utctime:gentime:visstr:bmpstr:seq:set:tag:".indexOf(":"+A+":"))throw new Error("undefined key: "+A);if("bool"==A)return new n(t[A]);if("int"==A)return new s(t[A]);if("bitstr"==A)return new a(t[A]);if("octstr"==A)return new o(t[A]);if("null"==A)return new h(t[A]);if("oid"==A)return new u(t[A]);if("enum"==A)return new c(t[A]);if("utf8str"==A)return new l(t[A]);if("numstr"==A)return new f(t[A]);if("prnstr"==A)return new g(t[A]);if("telstr"==A)return new p(t[A]);if("ia5str"==A)return new d(t[A]);if("utctime"==A)return new v(t[A]);if("gentime"==A)return new m(t[A]);if("visstr"==A)return new y(t[A]);if("bmpstr"==A)return new x(t[A]);if("asn1"==A)return new i(t[A]);if("seq"==A){for(var D=t[A],I=[],C=0;C<D.length;C++){var P=F(D[C]);I.push(P)}return new S({array:I})}if("set"==A){for(D=t[A],I=[],C=0;C<D.length;C++){P=F(D[C]);I.push(P)}return new w({array:I})}if("tag"==A){var R=t[A];if("[object Array]"===Object.prototype.toString.call(R)&&3==R.length){var T=F(R[2]);return new E({tag:R[0],explicit:R[1],obj:T})}return new E(R)}},this.jsonToASN1HEX=function(t){var e=this.newObject(t);return e.tohex()}},Pr.asn1.ASN1Util.oidHexToInt=function(t){for(var e="",r=parseInt(t.substr(0,2),16),i=Math.floor(r/40),n=r%40,s=(e=i+"."+n,""),a=2;a<t.length;a+=2){var o=parseInt(t.substr(a,2),16),h=("00000000"+o.toString(2)).slice(-8);if(s+=h.substr(1,7),"0"==h.substr(0,1)){var u=new d(s,2);e=e+"."+u.toString(10),s=""}}return e},Pr.asn1.ASN1Util.oidIntToHex=function(t){var e=function(t){var e=t.toString(16);return 1==e.length&&(e="0"+e),e},r=function(t){var r="",i=new d(t,10),n=i.toString(2),s=7-n.length%7;7==s&&(s=0);for(var a="",o=0;o<s;o++)a+="0";n=a+n;for(o=0;o<n.length-1;o+=7){var h=n.substr(o,7);o!=n.length-7&&(h="1"+h),r+=e(parseInt(h,2))}return r};if(!t.match(/^[0-9.]+$/))throw"malformed oid string: "+t;var i="",n=t.split("."),s=40*parseInt(n[0])+parseInt(n[1]);i+=e(s),n.splice(0,2);for(var a=0;a<n.length;a++)i+=r(n[a]);return i},Pr.asn1.ASN1Object=function(t){var e="";this.params=null,this.getLengthHexFromValue=function(){if("undefined"==typeof this.hV||null==this.hV)throw new Error("this.hV is null or undefined");if(this.hV.length%2==1)throw new Error("value hex must be even length: n="+e.length+",v="+this.hV);var t=this.hV.length/2,r=t.toString(16);if(r.length%2==1&&(r="0"+r),t<128)return r;var i=r.length/2;if(i>15)throw new Error("ASN.1 length too long to represent by 8x: n = "+t.toString(16));var n=128+i;return n.toString(16)+r},this.tohex=function(){return(null==this.hTLV||this.isModified)&&(this.hV=this.getFreshValueHex(),this.hL=this.getLengthHexFromValue(),this.hTLV=this.hT+this.hL+this.hV,this.isModified=!1),this.hTLV},this.getEncodedHex=function(){return this.tohex()},this.getValueHex=function(){return this.tohex(),this.hV},this.getFreshValueHex=function(){return""},this.setByParam=function(t){this.params=t},void 0!=t&&void 0!=t.tlv&&(this.hTLV=t.tlv,this.isModified=!1)},Pr.asn1.DERAbstractString=function(t){Pr.asn1.DERAbstractString.superclass.constructor.call(this);this.getString=function(){return this.s},this.setString=function(t){this.hTLV=null,this.isModified=!0,this.s=t,this.hV=zr(this.s).toLowerCase()},this.setStringHex=function(t){this.hTLV=null,this.isModified=!0,this.s=null,this.hV=t},this.getFreshValueHex=function(){return this.hV},"undefined"!=typeof t&&("string"==typeof t?this.setString(t):"undefined"!=typeof t.str?this.setString(t.str):"undefined"!=typeof t.hex&&this.setStringHex(t.hex))},Ti(Pr.asn1.DERAbstractString,Pr.asn1.ASN1Object),Pr.asn1.DERAbstractTime=function(t){Pr.asn1.DERAbstractTime.superclass.constructor.call(this);this.localDateToUTC=function(t){var e=t.getTime()+6e4*t.getTimezoneOffset(),r=new Date(e);return r},this.formatDate=function(t,e,r){var i=this.zeroPadding,n=this.localDateToUTC(t),s=String(n.getFullYear());"utc"==e&&(s=s.substr(2,2));var a=i(String(n.getMonth()+1),2),o=i(String(n.getDate()),2),h=i(String(n.getHours()),2),u=i(String(n.getMinutes()),2),c=i(String(n.getSeconds()),2),l=s+a+o+h+u+c;if(!0===r){var f=n.getMilliseconds();if(0!=f){var g=i(String(f),3);g=g.replace(/[0]+$/,""),l=l+"."+g}}return l+"Z"},this.zeroPadding=function(t,e){return t.length>=e?t:new Array(e-t.length+1).join("0")+t},this.setByParam=function(t){this.hV=null,this.hTLV=null,this.params=t},this.getString=function(){},this.setString=function(t){this.hTLV=null,this.isModified=!0,void 0==this.params&&(this.params={}),this.params.str=t},this.setByDate=function(t){this.hTLV=null,this.isModified=!0,void 0==this.params&&(this.params={}),this.params.date=t},this.setByDateValue=function(t,e,r,i,n,s){var a=new Date(Date.UTC(t,e-1,r,i,n,s,0));this.setByDate(a)},this.getFreshValueHex=function(){return this.hV}},Ti(Pr.asn1.DERAbstractTime,Pr.asn1.ASN1Object),Pr.asn1.DERAbstractStructured=function(t){Pr.asn1.DERAbstractString.superclass.constructor.call(this);this.setByASN1ObjectArray=function(t){this.hTLV=null,this.isModified=!0,this.asn1Array=t},this.appendASN1Object=function(t){this.hTLV=null,this.isModified=!0,this.asn1Array.push(t)},this.asn1Array=new Array,"undefined"!=typeof t&&"undefined"!=typeof t.array&&(this.asn1Array=t.array)},Ti(Pr.asn1.DERAbstractStructured,Pr.asn1.ASN1Object),Pr.asn1.DERBoolean=function(t){Pr.asn1.DERBoolean.superclass.constructor.call(this),this.hT="01",this.hTLV=0==t?"010100":"0101ff"},Ti(Pr.asn1.DERBoolean,Pr.asn1.ASN1Object),Pr.asn1.DERInteger=function(t){Pr.asn1.DERInteger.superclass.constructor.call(this),this.hT="02",this.setByBigInteger=function(t){this.hTLV=null,this.isModified=!0,this.hV=Pr.asn1.ASN1Util.bigIntToMinTwosComplementsHex(t)},this.setByInteger=function(t){var e=new d(String(t),10);this.setByBigInteger(e)},this.setValueHex=function(t){this.hV=t},this.getFreshValueHex=function(){return this.hV},"undefined"!=typeof t&&("undefined"!=typeof t.bigint?this.setByBigInteger(t.bigint):"undefined"!=typeof t["int"]?this.setByInteger(t["int"]):"number"==typeof t?this.setByInteger(t):"undefined"!=typeof t.hex&&this.setValueHex(t.hex))},Ti(Pr.asn1.DERInteger,Pr.asn1.ASN1Object),Pr.asn1.DERBitString=function(t){if(void 0!==t&&"undefined"!==typeof t.obj){var e=Pr.asn1.ASN1Util.newObject(t.obj);t.hex="00"+e.tohex()}Pr.asn1.DERBitString.superclass.constructor.call(this),this.hT="03",this.setHexValueIncludingUnusedBits=function(t){this.hTLV=null,this.isModified=!0,this.hV=t},this.setUnusedBitsAndHexValue=function(t,e){if(t<0||7<t)throw"unused bits shall be from 0 to 7: u = "+t;var r="0"+t;this.hTLV=null,this.isModified=!0,this.hV=r+e},this.setByBinaryString=function(t){t=t.replace(/0+$/,"");var e=8-t.length%8;8==e&&(e=0);for(var r=0;r<=e;r++)t+="0";var i="";for(r=0;r<t.length-1;r+=8){var n=t.substr(r,8),s=parseInt(n,2).toString(16);1==s.length&&(s="0"+s),i+=s}this.hTLV=null,this.isModified=!0,this.hV="0"+e+i},this.setByBooleanArray=function(t){for(var e="",r=0;r<t.length;r++)1==t[r]?e+="1":e+="0";this.setByBinaryString(e)},this.newFalseArray=function(t){for(var e=new Array(t),r=0;r<t;r++)e[r]=!1;return e},this.getFreshValueHex=function(){return this.hV},"undefined"!=typeof t&&("string"==typeof t&&t.toLowerCase().match(/^[0-9a-f]+$/)?this.setHexValueIncludingUnusedBits(t):"undefined"!=typeof t.hex?this.setHexValueIncludingUnusedBits(t.hex):"undefined"!=typeof t.bin?this.setByBinaryString(t.bin):"undefined"!=typeof t.array&&this.setByBooleanArray(t.array))},Ti(Pr.asn1.DERBitString,Pr.asn1.ASN1Object),Pr.asn1.DEROctetString=function(t){if(void 0!==t&&"undefined"!==typeof t.obj){var e=Pr.asn1.ASN1Util.newObject(t.obj);t.hex=e.tohex()}Pr.asn1.DEROctetString.superclass.constructor.call(this,t),this.hT="04"},Ti(Pr.asn1.DEROctetString,Pr.asn1.DERAbstractString),Pr.asn1.DERNull=function(){Pr.asn1.DERNull.superclass.constructor.call(this),this.hT="05",this.hTLV="0500"},Ti(Pr.asn1.DERNull,Pr.asn1.ASN1Object),Pr.asn1.DERObjectIdentifier=function(t){Pr.asn1.DERObjectIdentifier.superclass.constructor.call(this),this.hT="06",this.setValueHex=function(t){this.hTLV=null,this.isModified=!0,this.s=null,this.hV=t},this.setValueOidString=function(t){var e=Ai(t);if(null==e)throw new Error("malformed oid string: "+t);this.hTLV=null,this.isModified=!0,this.s=null,this.hV=e},this.setValueName=function(t){var e=Pr.asn1.x509.OID.name2oid(t);if(""===e)throw new Error("DERObjectIdentifier oidName undefined: "+t);this.setValueOidString(e)},this.setValueNameOrOid=function(t){t.match(/^[0-2].[0-9.]+$/)?this.setValueOidString(t):this.setValueName(t)},this.getFreshValueHex=function(){return this.hV},this.setByParam=function(t){"string"===typeof t?this.setValueNameOrOid(t):void 0!==t.oid?this.setValueNameOrOid(t.oid):void 0!==t.name?this.setValueNameOrOid(t.name):void 0!==t.hex&&this.setValueHex(t.hex)},void 0!==t&&this.setByParam(t)},Ti(Pr.asn1.DERObjectIdentifier,Pr.asn1.ASN1Object),Pr.asn1.DEREnumerated=function(t){Pr.asn1.DEREnumerated.superclass.constructor.call(this),this.hT="0a",this.setByBigInteger=function(t){this.hTLV=null,this.isModified=!0,this.hV=Pr.asn1.ASN1Util.bigIntToMinTwosComplementsHex(t)},this.setByInteger=function(t){var e=new d(String(t),10);this.setByBigInteger(e)},this.setValueHex=function(t){this.hV=t},this.getFreshValueHex=function(){return this.hV},"undefined"!=typeof t&&("undefined"!=typeof t["int"]?this.setByInteger(t["int"]):"number"==typeof t?this.setByInteger(t):"undefined"!=typeof t.hex&&this.setValueHex(t.hex))},Ti(Pr.asn1.DEREnumerated,Pr.asn1.ASN1Object),Pr.asn1.DERUTF8String=function(t){Pr.asn1.DERUTF8String.superclass.constructor.call(this,t),this.hT="0c"},Ti(Pr.asn1.DERUTF8String,Pr.asn1.DERAbstractString),Pr.asn1.DERNumericString=function(t){Pr.asn1.DERNumericString.superclass.constructor.call(this,t),this.hT="12"},Ti(Pr.asn1.DERNumericString,Pr.asn1.DERAbstractString),Pr.asn1.DERPrintableString=function(t){Pr.asn1.DERPrintableString.superclass.constructor.call(this,t),this.hT="13"},Ti(Pr.asn1.DERPrintableString,Pr.asn1.DERAbstractString),Pr.asn1.DERTeletexString=function(t){Pr.asn1.DERTeletexString.superclass.constructor.call(this,t),this.hT="14"},Ti(Pr.asn1.DERTeletexString,Pr.asn1.DERAbstractString),Pr.asn1.DERIA5String=function(t){Pr.asn1.DERIA5String.superclass.constructor.call(this,t),this.hT="16"},Ti(Pr.asn1.DERIA5String,Pr.asn1.DERAbstractString),Pr.asn1.DERVisibleString=function(t){Pr.asn1.DERIA5String.superclass.constructor.call(this,t),this.hT="1a"},Ti(Pr.asn1.DERVisibleString,Pr.asn1.DERAbstractString),Pr.asn1.DERBMPString=function(t){Pr.asn1.DERBMPString.superclass.constructor.call(this,t),this.hT="1e"},Ti(Pr.asn1.DERBMPString,Pr.asn1.DERAbstractString),Pr.asn1.DERUTCTime=function(t){Pr.asn1.DERUTCTime.superclass.constructor.call(this,t),this.hT="17",this.params=void 0,this.getFreshValueHex=function(){var t=this.params;if(void 0==this.params&&(t={date:new Date}),"string"==typeof t){if(!t.match(/^[0-9]{12}Z$/)&&!t.match(/^[0-9]{12}\.[0-9]+Z$/))throw new Error("malformed string for UTCTime: "+t);this.hV=jr(t)}else if(void 0!=t.str)this.hV=jr(t.str);else if(void 0==t.date&&1==t.millis){var e=new Date;this.hV=jr(this.formatDate(e,"utc",!0))}else if(void 0!=t.date&&t.date instanceof Date){var r=!0===t.millis;this.hV=jr(this.formatDate(t.date,"utc",r))}else t instanceof Date&&(this.hV=jr(this.formatDate(t,"utc")));if(void 0==this.hV)throw new Error("parameter not specified properly for UTCTime");return this.hV},void 0!=t&&this.setByParam(t)},Ti(Pr.asn1.DERUTCTime,Pr.asn1.DERAbstractTime),Pr.asn1.DERGeneralizedTime=function(t){Pr.asn1.DERGeneralizedTime.superclass.constructor.call(this,t),this.hT="18",this.params=t,this.getFreshValueHex=function(){var t=this.params;if(void 0==this.params&&(t={date:new Date}),"string"==typeof t){if(!t.match(/^[0-9]{14}Z$/)&&!t.match(/^[0-9]{14}\.[0-9]+Z$/))throw new Error("malformed string for GeneralizedTime: "+t);this.hV=jr(t)}else if(void 0!=t.str)this.hV=jr(t.str);else if(void 0==t.date&&1==t.millis){var e=new Date;this.hV=jr(this.formatDate(e,"gen",!0))}else if(void 0!=t.date&&t.date instanceof Date){var r=!0===t.millis;this.hV=jr(this.formatDate(t.date,"gen",r))}else t instanceof Date&&(this.hV=jr(this.formatDate(t,"gen")));if(void 0==this.hV)throw new Error("parameter not specified properly for GeneralizedTime");return this.hV},void 0!=t&&this.setByParam(t)},Ti(Pr.asn1.DERGeneralizedTime,Pr.asn1.DERAbstractTime),Pr.asn1.DERSequence=function(t){Pr.asn1.DERSequence.superclass.constructor.call(this,t),this.hT="30",this.getFreshValueHex=function(){for(var t="",e=0;e<this.asn1Array.length;e++){var r=this.asn1Array[e];t+=r.tohex()}return this.hV=t,this.hV}},Ti(Pr.asn1.DERSequence,Pr.asn1.DERAbstractStructured),Pr.asn1.DERSet=function(t){Pr.asn1.DERSet.superclass.constructor.call(this,t),this.hT="31",this.sortFlag=!0,this.getFreshValueHex=function(){for(var t=new Array,e=0;e<this.asn1Array.length;e++){var r=this.asn1Array[e];t.push(r.tohex())}return 1==this.sortFlag&&t.sort(),this.hV=t.join(""),this.hV},"undefined"!=typeof t&&"undefined"!=typeof t.sortflag&&0==t.sortflag&&(this.sortFlag=!1)},Ti(Pr.asn1.DERSet,Pr.asn1.DERAbstractStructured),Pr.asn1.DERTaggedObject=function(t){Pr.asn1.DERTaggedObject.superclass.constructor.call(this);var e=Pr.asn1,r=Br,n=r.getV,s=(r.isASN1HEX,e.ASN1Util.newObject);this.hT="a0",this.hV="",this.isExplicit=!0,this.asn1Object=null,this.params={tag:"a0",explicit:!0},this.setASN1Object=function(t,e,r){this.params={tag:e,explicit:t,obj:r}},this.getFreshValueHex=function(){var t=this.params;if(void 0==t.explicit&&(t.explicit=!0),void 0!=t.tage&&(t.tag=t.tage,t.explicit=!0),void 0!=t.tagi&&(t.tag=t.tagi,t.explicit=!1),void 0!=t.str)this.hV=zr(t.str);else if(void 0!=t.hex)this.hV=t.hex;else{if(void 0==t.obj)throw new Error("str, hex nor obj not specified");var r;t.obj instanceof e.ASN1Object?r=t.obj.tohex():"object"==i(t.obj)&&(r=s(t.obj).tohex()),t.explicit?this.hV=r:this.hV=n(r,0)}return void 0==t.tag&&(t.tag="a0"),this.hT=t.tag,this.hTLV=null,this.isModified=!0,this.hV},this.setByParam=function(t){this.params=t},void 0!==t&&this.setByParam(t)},Ti(Pr.asn1.DERTaggedObject,Pr.asn1.ASN1Object);var Pr,Rr,Tr,Br=new function(){};function Nr(t){for(var e=new Array,r=0;r<t.length;r++)e[r]=t.charCodeAt(r);return e}function Hr(t){for(var e="",r=0;r<t.length;r++)e+=String.fromCharCode(t[r]);return e}function Or(t){for(var e="",r=0;r<t.length;r++){var i=t[r].toString(16);1==i.length&&(i="0"+i),e+=i}return e}function jr(t){return Or(Nr(t))}function Vr(t){return c(jr(t))}function Kr(t){return kr(c(jr(t)))}function Lr(t){return Hr(f(qr(t)))}function kr(t){return t=t.replace(/\=/g,""),t=t.replace(/\+/g,"-"),t=t.replace(/\//g,"_"),t}function qr(t){return t.length%4==2?t+="==":t.length%4==3&&(t+="="),t=t.replace(/-/g,"+"),t=t.replace(/_/g,"/"),t}function Mr(t){return t.length%2==1&&(t="0"+t),kr(c(t))}function _r(t){return l(qr(t))}function Ur(t){return c(ui(yi(t)))}function Gr(t){return decodeURIComponent(ci(l(t)))}function zr(t){return ui(yi(t)).toLowerCase()}function Wr(t){try{return decodeURIComponent(ci(t))}catch(e){return null}}function Jr(t){return Wr(Xr(t))}function Xr(t){for(var e=t.match(/.{1,2}/g),r=[],i=0;i<e.length;i++){var n=parseInt(e[i],16);161<=n&&n<=191?(r.push("c2"),r.push(e[i])):192<=n&&n<=255?(r.push("c3"),r.push((n-64).toString(16))):r.push(e[i])}return r.join("")}function $r(t){for(var e="",r=0;r<t.length-1;r+=2)e+=String.fromCharCode(parseInt(t.substr(r,2),16));return e}function Yr(t){for(var e="",r=0;r<t.length;r++)e+=("0"+t.charCodeAt(r).toString(16)).slice(-2);return e}function Zr(t){return c(t)}function Qr(t){var e=Zr(t),r=e.replace(/(.{64})/g,"$1\r\n");return r=r.replace(/\r\n$/,""),r}function ti(t){var e=t.replace(/[^0-9A-Za-z\/+=]*/g,""),r=l(e);return r}function ei(t,e){var r=Qr(t);return"-----BEGIN "+e+"-----\r\n"+r+"\r\n-----END "+e+"-----\r\n"}function ri(t,e){if(-1==t.indexOf("-----BEGIN "))throw"can't find PEM header: "+e;return void 0!==e?(t=t.replace(new RegExp("^[^]*-----BEGIN "+e+"-----"),""),t=t.replace(new RegExp("-----END "+e+"-----[^]*$"),"")):(t=t.replace(/^[^]*-----BEGIN [^-]+-----/,""),t=t.replace(/-----END [^-]+-----[^]*$/,"")),ti(t)}function ii(t){if(t.length%2!=0)throw"input is not even length";if(null==t.match(/^[0-9A-Fa-f]+$/))throw"input is not hexadecimal";for(var e=new ArrayBuffer(t.length/2),r=new DataView(e),i=0;i<t.length/2;i++)r.setUint8(i,parseInt(t.substr(2*i,2),16));return e}function ni(t){for(var e="",r=new DataView(t),i=0;i<t.byteLength;i++)e+=("00"+r.getUint8(i).toString(16)).slice(-2);return e}function si(t){var e,r,i,n,s,a,o,h,u,c,l;if(l=t.match(/^(\d{2}|\d{4})(\d\d)(\d\d)(\d\d)(\d\d)(\d\d)(|\.\d+)Z$/),l)return h=l[1],e=parseInt(h),2===h.length&&(50<=e&&e<100?e=1900+e:0<=e&&e<50&&(e=2e3+e)),r=parseInt(l[2])-1,i=parseInt(l[3]),n=parseInt(l[4]),s=parseInt(l[5]),a=parseInt(l[6]),o=0,u=l[7],""!==u&&(c=(u.substr(1)+"00").substr(0,3),o=parseInt(c)),Date.UTC(e,r,i,n,s,a,o);throw new Error("unsupported zulu format: "+t)}function ai(t){return Math.round(si(t)/1e3)}function oi(t){return new Date(si(t))}function hi(t,e,r){var i,n=t.getUTCFullYear();if(e){if(n<1950||2049<n)throw"not proper year for UTCTime: "+n;i=(""+n).slice(-2)}else i=("000"+n).slice(-4);if(i+=("0"+(t.getUTCMonth()+1)).slice(-2),i+=("0"+t.getUTCDate()).slice(-2),i+=("0"+t.getUTCHours()).slice(-2),i+=("0"+t.getUTCMinutes()).slice(-2),i+=("0"+t.getUTCSeconds()).slice(-2),r){var s=t.getUTCMilliseconds();0!==s&&(s=("00"+s).slice(-3),s=s.replace(/0+$/g,""),i+="."+s)}return i+="Z",i}function ui(t){return t.replace(/%/g,"")}function ci(t){return t.replace(/(..)/g,"%$1")}function li(t){var e="malformed IPv6 address";if(!t.match(/^[0-9A-Fa-f:]+$/))throw e;t=t.toLowerCase();var r=t.split(":").length-1;if(r<2)throw e;var i=":".repeat(7-r+2);t=t.replace("::",i);var n=t.split(":");if(8!=n.length)throw e;for(var s=0;s<8;s++)n[s]=("0000"+n[s]).slice(-4);return n.join("")}function fi(t){if(!t.match(/^[0-9A-Fa-f]{32}$/))throw new Error("malformed IPv6 address: "+t);t=t.toLowerCase();var e=t.match(/.{1,4}/g);e=e.map((function(t){return t.replace(/^0+/,"")})),e=e.map((function(t){return""==t?"0":t})),t=":"+e.join(":")+":";var r=t.match(/:(0:){2,}/g);if(null==r)return t.slice(1,-1);var i=r.sort().slice(-1)[0];return t=t.replace(i.substr(0,i.length-1),":"),"::"!=t.substr(0,2)&&(t=t.substr(1)),"::"!=t.substr(-2,2)&&(t=t.substr(0,t.length-1)),t}function gi(t){var e=new Error("malformed hex value");if(!t.match(/^([0-9A-Fa-f][0-9A-Fa-f]){1,}$/))throw e;if(8==t.length){var r;try{return r=parseInt(t.substr(0,2),16)+"."+parseInt(t.substr(2,2),16)+"."+parseInt(t.substr(4,2),16)+"."+parseInt(t.substr(6,2),16),r}catch(i){throw e}}else{if(16!=t.length){if(32==t.length)return fi(t);if(64==t.length){try{return fi(t.substr(0,32))+"/"+pi(t.substr(32))}catch(i){throw e}return}return t}try{return gi(t.substr(0,8))+"/"+pi(t.substr(8))}catch(i){throw e}}}function pi(t){var e,r=new Error("malformed mask");try{e=new d(t,16).toString(2)}catch(i){throw r}if(!e.match(/^1*0*$/))throw r;return e.replace(/0+$/,"").length}function di(t){var e=new Error("malformed IP address");if(t=t.toLowerCase(t),!t.match(/^[0-9a-f.:/]+$/))throw e;if(!t.match(/^[0-9.]+$/)){if(t.match(/^[0-9.]+\/[0-9]+$/)){var r=t.split("/");return di(r[0])+vi(parseInt(r[1]),32)}if(t.match(/^[0-9a-f:]+$/)&&-1!==t.indexOf(":"))return li(t);if(t.match(/^[0-9a-f:]+\/[0-9]+$/)&&-1!==t.indexOf(":")){r=t.split("/");return li(r[0])+vi(parseInt(r[1]),128)}throw e}var i=t.split(".");if(4!==i.length)throw e;var n="";try{for(var s=0;s<4;s++){var a=parseInt(i[s]);n+=("0"+a.toString(16)).slice(-2)}return n}catch(o){throw e}}function vi(t,e){if(32==e&&0==t)return"00000000";if(128==e&&0==t)return"00000000000000000000000000000000";var r=Array(t+1).join("1")+Array(e-t+1).join("0");return new d(r,2).toString(16)}function mi(t){function e(t){var e=parseInt(t.substr(0,2),16),r=parseInt(t.substr(2),16);if(0==e&r<128)return String.fromCharCode(r);if(e<8){var i=192|(7&e)<<3|(192&r)>>6,n=128|63&r;return Wr(i.toString(16)+n.toString(16))}i=224|(240&e)>>4,n=128|(15&e)<<2|(192&r)>>6;var s=128|63&r;return Wr(i.toString(16)+n.toString(16)+s.toString(16))}var r=t.match(/.{4}/g),i=r.map(e);return i.join("")}function yi(t){for(var e=encodeURIComponent(t),r="",i=0;i<e.length;i++)"%"==e[i]?(r+=e.substr(i,3),i+=2):r=r+"%"+jr(e[i]);return r}function xi(t){return t=t.replace(/\r\n/gm,"\n"),t}function Si(t){return t=t.replace(/\r\n/gm,"\n"),t=t.replace(/\n/gm,"\r\n"),t}function wi(t){return!(t.length%2!=0||!t.match(/^[0-9a-f]+$/)&&!t.match(/^[0-9A-F]+$/))}function Ei(t){return t.length%2==1?"0"+t:t.substr(0,1)>"7"?"00"+t:t}function Fi(t){t=t.replace(/^\s*\[\s*/,""),t=t.replace(/\s*\]\s*$/,""),t=t.replace(/\s*/g,"");try{var e=t.split(/,/).map((function(t,e,r){var i=parseInt(t);if(i<0||255<i)throw"integer not in range 0-255";var n=("00"+i.toString(16)).slice(-2);return n})).join("");return e}catch(r){throw"malformed integer array string: "+r}}Br.getLblen=function(t,e){if("8"!=t.substr(e+2,1))return 1;var r=parseInt(t.substr(e+3,1));return 0==r?-1:0<r&&r<10?r+1:-2},Br.getL=function(t,e){var r=Br.getLblen(t,e);return r<1?"":t.substr(e+2,2*r)},Br.getVblen=function(t,e){var r,i;return r=Br.getL(t,e),""==r?-1:(i="8"===r.substr(0,1)?new d(r.substr(2),16):new d(r,16),i.intValue())},Br.getVidx=function(t,e){var r=Br.getLblen(t,e);return r<0?r:e+2*(r+1)},Br.getV=function(t,e){var r=Br.getVidx(t,e),i=Br.getVblen(t,e);return t.substr(r,2*i)},Br.getTLV=function(t,e){return t.substr(e,2)+Br.getL(t,e)+Br.getV(t,e)},Br.getTLVblen=function(t,e){return 2+2*Br.getLblen(t,e)+2*Br.getVblen(t,e)},Br.getNextSiblingIdx=function(t,e){var r=Br.getVidx(t,e),i=Br.getVblen(t,e);return r+2*i},Br.getChildIdx=function(t,e){var r,i,n,s=Br,a=[];r=s.getVidx(t,e),i=2*s.getVblen(t,e),"03"==t.substr(e,2)&&(r+=2,i-=2),n=0;var o=r;while(n<=i){var h=s.getTLVblen(t,o);if(n+=h,n<=i&&a.push(o),o+=h,n>=i)break}return a},Br.getNthChildIdx=function(t,e,r){var i=Br.getChildIdx(t,e);return i[r]},Br.getIdxbyList=function(t,e,r,i){var n,s,a=Br;return 0==r.length?void 0!==i&&t.substr(e,2)!==i?-1:e:(n=r.shift(),s=a.getChildIdx(t,e),n>=s.length?-1:a.getIdxbyList(t,s[n],r,i))},Br.getIdxbyListEx=function(t,e,r,i){var n,s,a=Br;if(0==r.length)return void 0!==i&&t.substr(e,2)!==i?-1:e;n=r.shift(),s=a.getChildIdx(t,e);for(var o=0,h=0;h<s.length;h++){var u=t.substr(s[h],2);if("number"==typeof n&&!a.isContextTag(u)&&o==n||"string"==typeof n&&a.isContextTag(u,n))return a.getIdxbyListEx(t,s[h],r,i);a.isContextTag(u)||o++}return-1},Br.getTLVbyList=function(t,e,r,i){var n=Br,s=n.getIdxbyList(t,e,r,i);return-1==s||s>=t.length?null:n.getTLV(t,s)},Br.getTLVbyListEx=function(t,e,r,i){var n=Br,s=n.getIdxbyListEx(t,e,r,i);return-1==s?null:n.getTLV(t,s)},Br.getVbyList=function(t,e,r,i,n){var s,a,o=Br;return s=o.getIdxbyList(t,e,r,i),-1==s||s>=t.length?null:(a=o.getV(t,s),!0===n&&(a=a.substr(2)),a)},Br.getVbyListEx=function(t,e,r,i,n){var s,a,o=Br;return s=o.getIdxbyListEx(t,e,r,i),-1==s?null:(a=o.getV(t,s),"03"==t.substr(s,2)&&!1!==n&&(a=a.substr(2)),a)},Br.getInt=function(t,e,r){void 0==r&&(r=-1);try{var i=t.substr(e,2);if("02"!=i&&"03"!=i)return r;var n=Br.getV(t,e);return"02"==i?parseInt(n,16):Ci(n)}catch(s){return r}},Br.getOID=function(t,e,r){void 0==r&&(r=null);try{if("06"!=t.substr(e,2))return r;var i=Br.getV(t,e);return Di(i)}catch(n){return r}},Br.getOIDName=function(t,e,r){void 0==r&&(r=null);try{var i=Br.getOID(t,e,r);if(i==r)return r;var n=Pr.asn1.x509.OID.oid2name(i);return""==n?i:n}catch(s){return r}},Br.getString=function(t,e,r){void 0==r&&(r=null);try{var i=Br.getV(t,e);return $r(i)}catch(n){return r}},Br.hextooidstr=function(t){var e=function(t,e){return t.length>=e?t:new Array(e-t.length+1).join("0")+t},r=[],i=t.substr(0,2),n=parseInt(i,16);r[0]=new String(Math.floor(n/40)),r[1]=new String(n%40);for(var s=t.substr(2),a=[],o=0;o<s.length/2;o++)a.push(parseInt(s.substr(2*o,2),16));var h=[],u="";for(o=0;o<a.length;o++)128&a[o]?u+=e((127&a[o]).toString(2),7):(u+=e((127&a[o]).toString(2),7),h.push(new String(parseInt(u,2))),u="");var c=r.join(".");return h.length>0&&(c=c+"."+h.join(".")),c},Br.dump=function(t,e,r,i){var n=Br,s=n.getV,a=n.dump,o=n.getChildIdx,h=t;t instanceof Pr.asn1.ASN1Object&&(h=t.tohex());var u=function(t,e){if(t.length<=2*e)return t;var r=t.substr(0,e)+"..(total "+t.length/2+"bytes).."+t.substr(t.length-e,e);return r};void 0===e&&(e={ommit_long_octet:32}),void 0===r&&(r=0),void 0===i&&(i="");var c=e.ommit_long_octet,l=h.substr(r,2);if("01"==l){var f=s(h,r);return"00"==f?i+"BOOLEAN FALSE\n":i+"BOOLEAN TRUE\n"}if("02"==l){f=s(h,r);return i+"INTEGER "+u(f,c)+"\n"}if("03"==l){f=s(h,r);if(n.isASN1HEX(f.substr(2))){var g=i+"BITSTRING, encapsulates\n";return g+=a(f.substr(2),e,0,i+"  "),g}return i+"BITSTRING "+u(f,c)+"\n"}if("04"==l){f=s(h,r);if(n.isASN1HEX(f)){g=i+"OCTETSTRING, encapsulates\n";return g+=a(f,e,0,i+"  "),g}return i+"OCTETSTRING "+u(f,c)+"\n"}if("05"==l)return i+"NULL\n";if("06"==l){var p=s(h,r),d=Pr.asn1.ASN1Util.oidHexToInt(p),v=Pr.asn1.x509.OID.oid2name(d),m=d.replace(/\./g," ");return""!=v?i+"ObjectIdentifier "+v+" ("+m+")\n":i+"ObjectIdentifier ("+m+")\n"}if("0a"==l)return i+"ENUMERATED "+parseInt(s(h,r))+"\n";if("0c"==l)return i+"UTF8String '"+Wr(s(h,r))+"'\n";if("13"==l)return i+"PrintableString '"+Wr(s(h,r))+"'\n";if("14"==l)return i+"TeletexString '"+Wr(s(h,r))+"'\n";if("16"==l)return i+"IA5String '"+Wr(s(h,r))+"'\n";if("17"==l)return i+"UTCTime "+Wr(s(h,r))+"\n";if("18"==l)return i+"GeneralizedTime "+Wr(s(h,r))+"\n";if("1a"==l)return i+"VisualString '"+Wr(s(h,r))+"'\n";if("1e"==l)return i+"BMPString '"+mi(s(h,r))+"'\n";if("30"==l){if("3000"==h.substr(r,4))return i+"SEQUENCE {}\n";g=i+"SEQUENCE\n";var y=o(h,r),x=e;if((2==y.length||3==y.length)&&"06"==h.substr(y[0],2)&&"04"==h.substr(y[y.length-1],2)){v=n.oidname(s(h,y[0]));var S=JSON.parse(JSON.stringify(e));S.x509ExtName=v,x=S}for(var w=0;w<y.length;w++)g+=a(h,x,y[w],i+"  ");return g}if("31"==l){for(g=i+"SET\n",y=o(h,r),w=0;w<y.length;w++)g+=a(h,e,y[w],i+"  ");return g}l=parseInt(l,16);if(0!=(128&l)){var E=31&l;if(0!=(32&l)){for(g=i+"["+E+"]\n",y=o(h,r),w=0;w<y.length;w++)g+=a(h,e,y[w],i+"  ");return g}f=s(h,r);if(Br.isASN1HEX(f)){g=i+"["+E+"]\n";return g+=a(f,e,0,i+"  "),g}("68747470"==f.substr(0,8)||"subjectAltName"===e.x509ExtName&&2==E)&&(f=Wr(f));g=i+"["+E+"] "+f+"\n";return g}return i+"UNKNOWN("+l+") "+s(h,r)+"\n"},Br.parse=function(t){var e=Br,r=e.parse,i=e.isASN1HEX,n=e.getV,s=e.getTLV,a=e.getChildIdx,o=Pr.asn1,h=o.ASN1Util.oidHexToInt,u=o.x509.OID.oid2name,c=Wr,l=mi,f=Jr,g={"0c":"utf8str",12:"numstr",13:"prnstr",14:"telstr",16:"ia5str",17:"utctime",18:"gentime","1a":"visstr","1e":"bmpstr",30:"seq",31:"set"},p=function(t){for(var e=[],i=a(t,0),n=0;n<i.length;n++){var o=i[n],h=s(t,o),u=r(h);e.push(u)}return e},d=t.substr(0,2),v={},m=n(t,0);if("01"==d)return"0101ff"==t?{bool:!0}:{bool:!1};if("02"==d)return{int:{hex:m}};if("03"==d)try{if("00"!=m.substr(0,2))throw"not encap";var y=m.substr(2);if(!i(y))throw"not encap";return{bitstr:{obj:r(y)}}}catch(Ne){var x=null;return m.length<=6&&(x=Ri(m)),null==x?{bitstr:{hex:m}}:{bitstr:{bin:x}}}else if("04"==d)try{if(!i(m))throw"not encap";return{octstr:{obj:r(m)}}}catch(Ne){return{octstr:{hex:m}}}else{if("05"==d)return{null:""};if("06"==d){var S=h(m),w=u(S);return""==w?{oid:S}:{oid:w}}if("0a"==d)return m.length>4?{enum:{hex:m}}:{enum:parseInt(m,16)};if("30"==d||"31"==d)return v[g[d]]=p(t),v;if("14"==d){var E=f(m);return v[g[d]]={str:E},v}if("1e"==d){E=l(m);return v[g[d]]={str:E},v}if(-1!=":0c:12:13:16:17:18:1a:".indexOf(d)){E=c(m);return v[g[d]]={str:E},v}if(d.match(/^8[0-9]$/)){E=c(m);return null==E|""==E||null!=E.match(/[\x00-\x1F\x7F-\x9F]/)||null!=E.match(/[\u0000-\u001F\u0080–\u009F]/)?{tag:{tag:d,explicit:!1,hex:m}}:{tag:{tag:d,explicit:!1,str:E}}}if(!d.match(/^a[0-9]$/)){var F=new Pr.asn1.ASN1Object;F.hV=m;var b=F.getLengthHexFromValue();return{asn1:{tlv:d+b+m}}}try{if(!i(m))throw new Error("not encap");return{tag:{tag:d,explicit:!0,obj:r(m)}}}catch(Ne){return{tag:{tag:d,explicit:!0,hex:m}}}}},Br.isContextTag=function(t,e){var r,i;t=t.toLowerCase();try{r=parseInt(t,16)}catch(s){return-1}if(void 0===e)return 128==(192&r);try{var n=e.match(/^\[[0-9]+\]$/);return null!=n&&(i=parseInt(e.substr(1,e.length-1),10),!(i>31)&&(128==(192&r)&&(31&r)==i))}catch(s){return!1}},Br.isASN1HEX=function(t){var e=Br;if(t.length%2==1)return!1;var r=e.getVblen(t,0),i=t.substr(0,2),n=e.getL(t,0),s=t.length-i.length-n.length;return s==2*r},Br.checkStrictDER=function(t,e,r,i,n){var s=Br;if(void 0===r){if("string"!=typeof t)throw new Error("not hex string");if(t=t.toLowerCase(),!Pr.lang.String.isHex(t))throw new Error("not hex string");r=t.length,i=t.length/2,n=i<128?1:Math.ceil(i.toString(16))+1}var a=s.getL(t,e);if(a.length>2*n)throw new Error("L of TLV too long: idx="+e);var o=s.getVblen(t,e);if(o>i)throw new Error("value of L too long than hex: idx="+e);var h=s.getTLV(t,e),u=h.length-2-s.getL(t,e).length;if(u!==2*o)throw new Error("V string length and L's value not the same:"+u+"/"+2*o);if(0===e&&t.length!=h.length)throw new Error("total length and TLV length unmatch:"+t.length+"!="+h.length);var c=t.substr(e,2);if("02"===c){var l=s.getVidx(t,e);if("00"==t.substr(l,2)&&t.charCodeAt(l+2)<56)throw new Error("not least zeros for DER INTEGER")}if(32&parseInt(c,16)){for(var f=s.getVblen(t,e),g=0,p=s.getChildIdx(t,e),d=0;d<p.length;d++){var v=s.getTLV(t,p[d]);g+=v.length,s.checkStrictDER(t,p[d],r,i,n)}if(2*f!=g)throw new Error("sum of children's TLV length and L unmatch: "+2*f+"!="+g)}},Br.oidname=function(t){var e=Pr.asn1;Pr.lang.String.isHex(t)&&(t=e.ASN1Util.oidHexToInt(t));var r=e.x509.OID.oid2name(t);return""===r&&(r=t),r},"undefined"!=typeof Pr&&Pr||(Pr={}),"undefined"!=typeof Pr.asn1&&Pr.asn1||(Pr.asn1={}),"undefined"!=typeof Pr.asn1.x509&&Pr.asn1.x509||(Pr.asn1.x509={}),Pr.asn1.x509.Certificate=function(t){Pr.asn1.x509.Certificate.superclass.constructor.call(this);var e=Pr,r=e.asn1,i=r.DERBitString,n=r.DERSequence,s=r.x509,a=s.TBSCertificate,o=s.AlgorithmIdentifier;this.params=void 0,this.setByParam=function(t){this.params=t},this.sign=function(){var t=this.params,e=t.sigalg;void 0!=t.sigalg.name&&(e=t.sigalg.name);var r=t.tbsobj.tohex(),i=new Pr.crypto.Signature({alg:e});i.init(t.cakey),i.updateHex(r),t.sighex=i.sign()},this.getPEM=function(){return ei(this.tohex(),"CERTIFICATE")},this.tohex=function(){var t=this.params;if(void 0!=t.tbsobj&&null!=t.tbsobj||(t.tbsobj=new a(t)),void 0==t.sighex&&void 0!=t.cakey&&this.sign(),void 0==t.sighex)throw new Error("sighex or cakey parameter not defined");var e=[];e.push(t.tbsobj),e.push(new o({name:t.sigalg})),e.push(new i({hex:"00"+t.sighex}));var r=new n({array:e});return r.tohex()},this.getEncodedHex=function(){return this.tohex()},void 0!=t&&(this.params=t)},Ti(Pr.asn1.x509.Certificate,Pr.asn1.ASN1Object),Pr.asn1.x509.TBSCertificate=function(t){Pr.asn1.x509.TBSCertificate.superclass.constructor.call(this);var e=Pr,r=e.asn1,i=r.x509,n=r.DERTaggedObject,s=r.DERInteger,a=r.DERSequence,o=i.AlgorithmIdentifier,h=i.Time,u=i.X500Name,c=i.Extensions,l=i.SubjectPublicKeyInfo;this.params=null,this.setByParam=function(t){this.params=t},this.tohex=function(){var t=[],e=this.params;if(void 0!=e.version||1!=e.version){var r=2;void 0!=e.version&&(r=e.version-1);var i=new n({obj:new s({int:r})});t.push(i)}t.push(new s(e.serial)),t.push(new o({name:e.sigalg})),t.push(new u(e.issuer)),t.push(new a({array:[new h(e.notbefore),new h(e.notafter)]})),t.push(new u(e.subject)),t.push(new l(Bi.getKey(e.sbjpubkey))),void 0!==e.ext&&e.ext.length>0&&t.push(new n({tag:"a3",obj:new c(e.ext)}));var f=new Pr.asn1.DERSequence({array:t});return f.tohex()},this.getEncodedHex=function(){return this.tohex()},void 0!==t&&this.setByParam(t)},Ti(Pr.asn1.x509.TBSCertificate,Pr.asn1.ASN1Object),Pr.asn1.x509.Extensions=function(t){Pr.asn1.x509.Extensions.superclass.constructor.call(this);var e=Pr,r=e.asn1,i=r.DERSequence,n=r.x509;this.aParam=[],this.setByParam=function(t){this.aParam=t},this.tohex=function(){for(var t=[],e=0;e<this.aParam.length;e++){var r=this.aParam[e],s=r.extname,a=null;if(void 0!=r.extn)a=new n.PrivateExtension(r);else if("subjectKeyIdentifier"==s)a=new n.SubjectKeyIdentifier(r);else if("keyUsage"==s)a=new n.KeyUsage(r);else if("subjectAltName"==s)a=new n.SubjectAltName(r);else if("issuerAltName"==s)a=new n.IssuerAltName(r);else if("basicConstraints"==s)a=new n.BasicConstraints(r);else if("nameConstraints"==s)a=new n.NameConstraints(r);else if("cRLDistributionPoints"==s)a=new n.CRLDistributionPoints(r);else if("certificatePolicies"==s)a=new n.CertificatePolicies(r);else if("authorityKeyIdentifier"==s)a=new n.AuthorityKeyIdentifier(r);else if("extKeyUsage"==s)a=new n.ExtKeyUsage(r);else if("authorityInfoAccess"==s)a=new n.AuthorityInfoAccess(r);else if("cRLNumber"==s)a=new n.CRLNumber(r);else if("cRLReason"==s)a=new n.CRLReason(r);else if("ocspNonce"==s)a=new n.OCSPNonce(r);else if("ocspNoCheck"==s)a=new n.OCSPNoCheck(r);else if("adobeTimeStamp"==s)a=new n.AdobeTimeStamp(r);else{if("subjectDirectoryAttributes"!=s)throw new Error("extension not supported:"+JSON.stringify(r));a=new n.SubjectDirectoryAttributes(r)}null!=a&&t.push(a)}var o=new i({array:t});return o.tohex()},this.getEncodedHex=function(){return this.tohex()},void 0!=t&&this.setByParam(t)},Ti(Pr.asn1.x509.Extensions,Pr.asn1.ASN1Object),Pr.asn1.x509.Extension=function(t){Pr.asn1.x509.Extension.superclass.constructor.call(this);var e=Pr,r=e.asn1,i=r.DERObjectIdentifier,n=r.DEROctetString,s=(r.DERBitString,r.DERBoolean),a=r.DERSequence;this.tohex=function(){var t=new i({oid:this.oid}),e=new n({hex:this.getExtnValueHex()}),r=new Array;r.push(t),this.critical&&r.push(new s),r.push(e);var o=new a({array:r});return o.tohex()},this.getEncodedHex=function(){return this.tohex()},this.critical=!1,void 0!==t&&void 0!==t.critical&&(this.critical=t.critical)},Ti(Pr.asn1.x509.Extension,Pr.asn1.ASN1Object),Pr.asn1.x509.KeyUsage=function(t){Pr.asn1.x509.KeyUsage.superclass.constructor.call(this,t);var e=ji.KEYUSAGE_NAME;if(this.getExtnValueHex=function(){return this.asn1ExtnValue.tohex()},this.oid="*********",void 0!==t&&(void 0!==t.bin&&(this.asn1ExtnValue=new Pr.asn1.DERBitString(t)),void 0!==t.names&&void 0!==t.names.length)){for(var r=t.names,i="000000000",n=0;n<r.length;n++)for(var s=0;s<e.length;s++)r[n]===e[s]&&(i=i.substring(0,s)+"1"+i.substring(s+1,i.length));this.asn1ExtnValue=new Pr.asn1.DERBitString({bin:i})}},Ti(Pr.asn1.x509.KeyUsage,Pr.asn1.x509.Extension),Pr.asn1.x509.BasicConstraints=function(t){Pr.asn1.x509.BasicConstraints.superclass.constructor.call(this,t);var e=Pr.asn1,r=e.DERBoolean,i=e.DERInteger,n=e.DERSequence;this.getExtnValueHex=function(){var t=new Array;this.cA&&t.push(new r),this.pathLen>-1&&t.push(new i({int:this.pathLen}));var e=new n({array:t});return this.asn1ExtnValue=e,this.asn1ExtnValue.tohex()},this.oid="*********",this.cA=!1,this.pathLen=-1,void 0!==t&&(void 0!==t.cA&&(this.cA=t.cA),void 0!==t.pathLen&&(this.pathLen=t.pathLen))},Ti(Pr.asn1.x509.BasicConstraints,Pr.asn1.x509.Extension),Pr.asn1.x509.CRLDistributionPoints=function(t){Pr.asn1.x509.CRLDistributionPoints.superclass.constructor.call(this,t);var e=Pr,r=e.asn1,i=r.x509;this.getExtnValueHex=function(){return this.asn1ExtnValue.tohex()},this.setByDPArray=function(t){for(var e=[],n=0;n<t.length;n++)if(t[n]instanceof Pr.asn1.ASN1Object)e.push(t[n]);else{var s=new i.DistributionPoint(t[n]);e.push(s)}this.asn1ExtnValue=new r.DERSequence({array:e})},this.setByOneURI=function(t){var e=new i.DistributionPoint({fulluri:t});this.setByDPArray([e])},this.oid="*********",void 0!==t&&(void 0!==t.array?this.setByDPArray(t.array):void 0!==t.uri&&this.setByOneURI(t.uri))},Ti(Pr.asn1.x509.CRLDistributionPoints,Pr.asn1.x509.Extension),Pr.asn1.x509.DistributionPoint=function(t){Pr.asn1.x509.DistributionPoint.superclass.constructor.call(this);var e=Pr,r=e.asn1,i=r.x509.DistributionPointName;this.tohex=function(){var t=new r.DERSequence;if(null!=this.asn1DP){var e=new r.DERTaggedObject({explicit:!0,tag:"a0",obj:this.asn1DP});t.appendASN1Object(e)}return this.hTLV=t.tohex(),this.hTLV},this.getEncodedHex=function(){return this.tohex()},void 0!==t&&(void 0!==t.dpobj?this.asn1DP=t.dpobj:void 0!==t.dpname?this.asn1DP=new i(t.dpname):void 0!==t.fulluri&&(this.asn1DP=new i({full:[{uri:t.fulluri}]})))},Ti(Pr.asn1.x509.DistributionPoint,Pr.asn1.ASN1Object),Pr.asn1.x509.DistributionPointName=function(t){Pr.asn1.x509.DistributionPointName.superclass.constructor.call(this);var e=Pr,r=e.asn1,i=r.DERTaggedObject;if(this.tohex=function(){if("full"!=this.type)throw new Error("currently type shall be 'full': "+this.type);return this.asn1Obj=new i({explicit:!1,tag:this.tag,obj:this.asn1V}),this.hTLV=this.asn1Obj.tohex(),this.hTLV},this.getEncodedHex=function(){return this.tohex()},void 0!==t)if(r.x509.GeneralNames.prototype.isPrototypeOf(t))this.type="full",this.tag="a0",this.asn1V=t;else{if(void 0===t.full)throw new Error("This class supports GeneralNames only as argument");this.type="full",this.tag="a0",this.asn1V=new r.x509.GeneralNames(t.full)}},Ti(Pr.asn1.x509.DistributionPointName,Pr.asn1.ASN1Object),Pr.asn1.x509.CertificatePolicies=function(t){Pr.asn1.x509.CertificatePolicies.superclass.constructor.call(this,t);var e=Pr,r=e.asn1,i=r.x509,n=r.DERSequence,s=i.PolicyInformation;this.params=null,this.getExtnValueHex=function(){for(var t=[],e=0;e<this.params.array.length;e++)t.push(new s(this.params.array[e]));var r=new n({array:t});return this.asn1ExtnValue=r,this.asn1ExtnValue.tohex()},this.oid="*********",void 0!==t&&(this.params=t)},Ti(Pr.asn1.x509.CertificatePolicies,Pr.asn1.x509.Extension),Pr.asn1.x509.PolicyInformation=function(t){Pr.asn1.x509.PolicyInformation.superclass.constructor.call(this,t);var e=Pr.asn1,r=e.DERSequence,i=e.DERObjectIdentifier,n=e.x509.PolicyQualifierInfo;this.params=null,this.tohex=function(){if(void 0===this.params.policyoid&&void 0===this.params.array)throw new Error("parameter oid and array missing");var t=[new i(this.params.policyoid)];if(void 0!==this.params.array){for(var e=[],s=0;s<this.params.array.length;s++)e.push(new n(this.params.array[s]));e.length>0&&t.push(new r({array:e}))}var a=new r({array:t});return a.tohex()},this.getEncodedHex=function(){return this.tohex()},void 0!==t&&(this.params=t)},Ti(Pr.asn1.x509.PolicyInformation,Pr.asn1.ASN1Object),Pr.asn1.x509.PolicyQualifierInfo=function(t){Pr.asn1.x509.PolicyQualifierInfo.superclass.constructor.call(this,t);var e=Pr.asn1,r=e.DERSequence,i=e.DERIA5String,n=e.DERObjectIdentifier,s=e.x509.UserNotice;this.params=null,this.tohex=function(){if(void 0!==this.params.cps){var t=new r({array:[new n({oid:"*******.5.5.7.2.1"}),new i({str:this.params.cps})]});return t.tohex()}if(void 0!=this.params.unotice){t=new r({array:[new n({oid:"*******.5.5.7.2.2"}),new s(this.params.unotice)]});return t.tohex()}},this.getEncodedHex=function(){return this.tohex()},void 0!==t&&(this.params=t)},Ti(Pr.asn1.x509.PolicyQualifierInfo,Pr.asn1.ASN1Object),Pr.asn1.x509.UserNotice=function(t){Pr.asn1.x509.UserNotice.superclass.constructor.call(this,t);var e=Pr.asn1.DERSequence,r=(Pr.asn1.DERInteger,Pr.asn1.x509.DisplayText),i=Pr.asn1.x509.NoticeReference;this.params=null,this.tohex=function(){var t=[];void 0!==this.params.noticeref&&t.push(new i(this.params.noticeref)),void 0!==this.params.exptext&&t.push(new r(this.params.exptext));var n=new e({array:t});return n.tohex()},this.getEncodedHex=function(){return this.tohex()},void 0!==t&&(this.params=t)},Ti(Pr.asn1.x509.UserNotice,Pr.asn1.ASN1Object),Pr.asn1.x509.NoticeReference=function(t){Pr.asn1.x509.NoticeReference.superclass.constructor.call(this,t);var e=Pr.asn1.DERSequence,r=Pr.asn1.DERInteger,i=Pr.asn1.x509.DisplayText;this.params=null,this.tohex=function(){var t=[];if(void 0!==this.params.org&&t.push(new i(this.params.org)),void 0!==this.params.noticenum){for(var n=[],s=this.params.noticenum,a=0;a<s.length;a++)n.push(new r(s[a]));t.push(new e({array:n}))}if(0==t.length)throw new Error("parameter is empty");var o=new e({array:t});return o.tohex()},this.getEncodedHex=function(){return this.tohex()},void 0!==t&&(this.params=t)},Ti(Pr.asn1.x509.NoticeReference,Pr.asn1.ASN1Object),Pr.asn1.x509.DisplayText=function(t){Pr.asn1.x509.DisplayText.superclass.constructor.call(this,t),this.hT="0c",void 0!==t&&("ia5"===t.type?this.hT="16":"vis"===t.type?this.hT="1a":"bmp"===t.type&&(this.hT="1e"))},Ti(Pr.asn1.x509.DisplayText,Pr.asn1.DERAbstractString),Pr.asn1.x509.NameConstraints=function(t){Pr.asn1.x509.NameConstraints.superclass.constructor.call(this,t);var e=Pr,r=e.asn1,i=r.x509,n=r.ASN1Util.newObject,s=i.GeneralSubtree;this.params=null,this.getExtnValueHex=function(){var t=this.params,e=[];if(void 0!=t.permit&&void 0!=t.permit.length){for(var r=[],i=0;i<t.permit.length;i++)r.push(new s(t.permit[i]));e.push({tag:{tagi:"a0",obj:{seq:r}}})}if(void 0!=t.exclude&&void 0!=t.exclude.length){var a=[];for(i=0;i<t.exclude.length;i++)a.push(new s(t.exclude[i]));e.push({tag:{tagi:"a1",obj:{seq:a}}})}return this.asn1ExtnValue=n({seq:e}),this.asn1ExtnValue.tohex()},this.oid="*********",void 0!==t&&(this.params=t)},Ti(Pr.asn1.x509.NameConstraints,Pr.asn1.x509.Extension),Pr.asn1.x509.GeneralSubtree=function(t){Pr.asn1.x509.GeneralSubtree.superclass.constructor.call(this);var e=Pr.asn1,r=e.x509,i=r.GeneralName,n=e.ASN1Util.newObject;this.params=null,this.setByParam=function(t){this.params=t},this.tohex=function(){var t=this.params,e=[new i(t)];void 0!=t.min&&e.push({tag:{tagi:"80",obj:{int:t.min}}}),void 0!=t.max&&e.push({tag:{tagi:"81",obj:{int:t.max}}});var r=n({seq:e});return r.tohex()},this.getEncodedHex=function(){return this.tohex()},void 0!==t&&this.setByParam(t)},Ti(Pr.asn1.x509.GeneralSubtree,Pr.asn1.ASN1Object),Pr.asn1.x509.ExtKeyUsage=function(t){Pr.asn1.x509.ExtKeyUsage.superclass.constructor.call(this,t);var e=Pr,r=e.asn1;this.setPurposeArray=function(t){this.asn1ExtnValue=new r.DERSequence;for(var e=0;e<t.length;e++){var i=new r.DERObjectIdentifier(t[e]);this.asn1ExtnValue.appendASN1Object(i)}},this.getExtnValueHex=function(){return this.asn1ExtnValue.tohex()},this.oid="*********",void 0!==t&&void 0!==t.array&&this.setPurposeArray(t.array)},Ti(Pr.asn1.x509.ExtKeyUsage,Pr.asn1.x509.Extension),Pr.asn1.x509.AuthorityKeyIdentifier=function(t){Pr.asn1.x509.AuthorityKeyIdentifier.superclass.constructor.call(this,t);var e=Pr,r=e.asn1,n=r.DERTaggedObject,s=r.x509.GeneralNames;e.crypto.Util.isKey;this.asn1KID=null,this.asn1CertIssuer=null,this.asn1CertSN=null,this.getExtnValueHex=function(){var t=new Array;this.asn1KID&&t.push(new n({explicit:!1,tag:"80",obj:this.asn1KID})),this.asn1CertIssuer&&t.push(new n({explicit:!1,tag:"a1",obj:new s([{dn:this.asn1CertIssuer}])})),this.asn1CertSN&&t.push(new n({explicit:!1,tag:"82",obj:this.asn1CertSN}));var e=new r.DERSequence({array:t});return this.asn1ExtnValue=e,this.asn1ExtnValue.tohex()},this.setKIDByParam=function(t){if(void 0!==t.str||void 0!==t.hex)this.asn1KID=new Pr.asn1.DEROctetString(t);else if("object"===i(t)&&Pr.crypto.Util.isKey(t)||"string"===typeof t&&-1!=t.indexOf("BEGIN ")){var e=t;"string"===typeof t&&(e=Bi.getKey(t));var r=Bi.getKeyID(e);this.asn1KID=new Pr.asn1.DEROctetString({hex:r})}},this.setCertIssuerByParam=function(t){void 0!==t.str||void 0!==t.ldapstr||void 0!==t.hex||void 0!==t.certsubject||void 0!==t.certissuer?this.asn1CertIssuer=new Pr.asn1.x509.X500Name(t):"string"===typeof t&&-1!=t.indexOf("BEGIN ")&&-1!=t.indexOf("CERTIFICATE")&&(this.asn1CertIssuer=new Pr.asn1.x509.X500Name({certissuer:t}))},this.setCertSNByParam=function(t){if(void 0!==t.str||void 0!==t.bigint||void 0!==t.hex)this.asn1CertSN=new Pr.asn1.DERInteger(t);else if("string"===typeof t&&-1!=t.indexOf("BEGIN ")&&t.indexOf("CERTIFICATE")){var e=new ji;e.readCertPEM(t);var r=e.getSerialNumberHex();this.asn1CertSN=new Pr.asn1.DERInteger({hex:r})}},this.oid="*********",void 0!==t&&(void 0!==t.kid&&this.setKIDByParam(t.kid),void 0!==t.issuer&&this.setCertIssuerByParam(t.issuer),void 0!==t.sn&&this.setCertSNByParam(t.sn),void 0!==t.issuersn&&"string"===typeof t.issuersn&&-1!=t.issuersn.indexOf("BEGIN ")&&t.issuersn.indexOf("CERTIFICATE")&&(this.setCertSNByParam(t.issuersn),this.setCertIssuerByParam(t.issuersn)))},Ti(Pr.asn1.x509.AuthorityKeyIdentifier,Pr.asn1.x509.Extension),Pr.asn1.x509.SubjectKeyIdentifier=function(t){Pr.asn1.x509.SubjectKeyIdentifier.superclass.constructor.call(this,t);var e=Pr,r=e.asn1,n=r.DEROctetString;this.asn1KID=null,this.getExtnValueHex=function(){return this.asn1ExtnValue=this.asn1KID,this.asn1ExtnValue.tohex()},this.setKIDByParam=function(t){if(void 0!==t.str||void 0!==t.hex)this.asn1KID=new n(t);else if("object"===i(t)&&Pr.crypto.Util.isKey(t)||"string"===typeof t&&-1!=t.indexOf("BEGIN")){var e=t;"string"===typeof t&&(e=Bi.getKey(t));var r=Bi.getKeyID(e);this.asn1KID=new Pr.asn1.DEROctetString({hex:r})}},this.oid="*********",void 0!==t&&void 0!==t.kid&&this.setKIDByParam(t.kid)},Ti(Pr.asn1.x509.SubjectKeyIdentifier,Pr.asn1.x509.Extension),Pr.asn1.x509.AuthorityInfoAccess=function(t){Pr.asn1.x509.AuthorityInfoAccess.superclass.constructor.call(this,t),this.setAccessDescriptionArray=function(t){for(var e=new Array,r=Pr,i=r.asn1,n=i.DERSequence,s=i.DERObjectIdentifier,a=i.x509.GeneralName,o=0;o<t.length;o++){var h,u=t[o];if(void 0!==u.ocsp)h=new n({array:[new s({oid:"*******.********.1"}),new a({uri:u.ocsp})]});else{if(void 0===u.caissuer)throw new Error("unknown AccessMethod parameter: "+JSON.stringify(u));h=new n({array:[new s({oid:"*******.********.2"}),new a({uri:u.caissuer})]})}e.push(h)}this.asn1ExtnValue=new n({array:e})},this.getExtnValueHex=function(){return this.asn1ExtnValue.tohex()},this.oid="*******.*******.1",void 0!==t&&void 0!==t.array&&this.setAccessDescriptionArray(t.array)},Ti(Pr.asn1.x509.AuthorityInfoAccess,Pr.asn1.x509.Extension),Pr.asn1.x509.SubjectAltName=function(t){Pr.asn1.x509.SubjectAltName.superclass.constructor.call(this,t),this.setNameArray=function(t){this.asn1ExtnValue=new Pr.asn1.x509.GeneralNames(t)},this.getExtnValueHex=function(){return this.asn1ExtnValue.tohex()},this.oid="*********",void 0!==t&&void 0!==t.array&&this.setNameArray(t.array)},Ti(Pr.asn1.x509.SubjectAltName,Pr.asn1.x509.Extension),Pr.asn1.x509.IssuerAltName=function(t){Pr.asn1.x509.IssuerAltName.superclass.constructor.call(this,t),this.setNameArray=function(t){this.asn1ExtnValue=new Pr.asn1.x509.GeneralNames(t)},this.getExtnValueHex=function(){return this.asn1ExtnValue.tohex()},this.oid="*********",void 0!==t&&void 0!==t.array&&this.setNameArray(t.array)},Ti(Pr.asn1.x509.IssuerAltName,Pr.asn1.x509.Extension),Pr.asn1.x509.SubjectDirectoryAttributes=function(t){Pr.asn1.x509.SubjectDirectoryAttributes.superclass.constructor.call(this,t);var e=Pr.asn1,r=e.DERSequence,i=e.ASN1Util.newObject,n=e.x509.OID.name2oid;this.params=null,this.getExtnValueHex=function(){for(var t=[],e=0;e<this.params.array.length;e++){var s=this.params.array[e],a={seq:[{oid:"*******"},{set:[{utf8str:"DE"}]}]};if("dateOfBirth"==s.attr)a.seq[0].oid=n(s.attr),a.seq[1].set[0]={gentime:s.str};else if("placeOfBirth"==s.attr)a.seq[0].oid=n(s.attr),a.seq[1].set[0]={utf8str:s.str};else if("gender"==s.attr)a.seq[0].oid=n(s.attr),a.seq[1].set[0]={prnstr:s.str};else if("countryOfCitizenship"==s.attr)a.seq[0].oid=n(s.attr),a.seq[1].set[0]={prnstr:s.str};else{if("countryOfResidence"!=s.attr)throw new Error("unsupported attribute: "+s.attr);a.seq[0].oid=n(s.attr),a.seq[1].set[0]={prnstr:s.str}}t.push(new i(a))}var o=new r({array:t});return this.asn1ExtnValue=o,this.asn1ExtnValue.tohex()},this.oid="********",void 0!==t&&(this.params=t)},Ti(Pr.asn1.x509.SubjectDirectoryAttributes,Pr.asn1.x509.Extension),Pr.asn1.x509.PrivateExtension=function(t){Pr.asn1.x509.PrivateExtension.superclass.constructor.call(this,t);var e=Pr,r=e.lang.String.isHex,n=e.asn1,s=n.x509.OID.name2oid,a=n.ASN1Util.newObject;this.params=null,this.setByParam=function(t){this.oid=s(t.extname),this.params=t},this.getExtnValueHex=function(){if(void 0==this.params.extname||void 0==this.params.extn)throw new Error("extname or extnhex not specified");var t=this.params.extn;if("string"==typeof t&&r(t))return t;if("object"==i(t))try{return a(t).tohex()}catch(e){}throw new Error("unsupported extn value")},void 0!=t&&this.setByParam(t)},Ti(Pr.asn1.x509.PrivateExtension,Pr.asn1.x509.Extension),Pr.asn1.x509.CRL=function(t){Pr.asn1.x509.CRL.superclass.constructor.call(this);var e=Pr,r=e.asn1,i=r.DERSequence,n=r.DERBitString,s=r.x509,a=s.AlgorithmIdentifier,o=s.TBSCertList;this.params=void 0,this.setByParam=function(t){this.params=t},this.sign=function(){var t=new o(this.params).tohex(),e=new Pr.crypto.Signature({alg:this.params.sigalg});e.init(this.params.cakey),e.updateHex(t);var r=e.sign();this.params.sighex=r},this.getPEM=function(){return ei(this.tohex(),"X509 CRL")},this.tohex=function(){var t=this.params;if(void 0==t.tbsobj&&(t.tbsobj=new o(t)),void 0==t.sighex&&void 0!=t.cakey&&this.sign(),void 0==t.sighex)throw new Error("sighex or cakey parameter not defined");var e=[];e.push(t.tbsobj),e.push(new a({name:t.sigalg})),e.push(new n({hex:"00"+t.sighex}));var r=new i({array:e});return r.tohex()},this.getEncodedHex=function(){return this.tohex()},void 0!=t&&(this.params=t)},Ti(Pr.asn1.x509.CRL,Pr.asn1.ASN1Object),Pr.asn1.x509.TBSCertList=function(t){Pr.asn1.x509.TBSCertList.superclass.constructor.call(this);var e=Pr,r=e.asn1,i=r.DERInteger,n=r.DERSequence,s=r.DERTaggedObject,a=(r.DERObjectIdentifier,r.x509),o=a.AlgorithmIdentifier,h=a.Time,u=a.Extensions,c=a.X500Name;this.params=null,this.setByParam=function(t){this.params=t},this.getRevCertSequence=function(){for(var t=[],e=this.params.revcert,r=0;r<e.length;r++){var s=[new i(e[r].sn),new h(e[r].date)];void 0!=e[r].ext&&s.push(new u(e[r].ext)),t.push(new n({array:s}))}return new n({array:t})},this.tohex=function(){var t=[],e=this.params;if(void 0!=e.version){var r=e.version-1,a=new i({int:r});t.push(a)}if(t.push(new o({name:e.sigalg})),t.push(new c(e.issuer)),t.push(new h(e.thisupdate)),void 0!=e.nextupdate&&t.push(new h(e.nextupdate)),void 0!=e.revcert&&t.push(this.getRevCertSequence()),void 0!=e.ext){var l=new u(e.ext);t.push(new s({tag:"a0",explicit:!0,obj:l}))}var f=new n({array:t});return f.tohex()},this.getEncodedHex=function(){return this.tohex()},void 0!==t&&this.setByParam(t)},Ti(Pr.asn1.x509.TBSCertList,Pr.asn1.ASN1Object),Pr.asn1.x509.CRLEntry=function(t){Pr.asn1.x509.CRLEntry.superclass.constructor.call(this);var e=Pr,r=e.asn1;this.setCertSerial=function(t){this.sn=new r.DERInteger(t)},this.setRevocationDate=function(t){this.time=new r.x509.Time(t)},this.tohex=function(){var t=new r.DERSequence({array:[this.sn,this.time]});return this.TLV=t.tohex(),this.TLV},this.getEncodedHex=function(){return this.tohex()},void 0!==t&&(void 0!==t.time&&this.setRevocationDate(t.time),void 0!==t.sn&&this.setCertSerial(t.sn))},Ti(Pr.asn1.x509.CRLEntry,Pr.asn1.ASN1Object),Pr.asn1.x509.CRLNumber=function(t){Pr.asn1.x509.CRLNumber.superclass.constructor.call(this,t),this.params=void 0,this.getExtnValueHex=function(){return this.asn1ExtnValue=new Pr.asn1.DERInteger(this.params.num),this.asn1ExtnValue.tohex()},this.oid="*********",void 0!=t&&(this.params=t)},Ti(Pr.asn1.x509.CRLNumber,Pr.asn1.x509.Extension),Pr.asn1.x509.CRLReason=function(t){Pr.asn1.x509.CRLReason.superclass.constructor.call(this,t),this.params=void 0,this.getExtnValueHex=function(){return this.asn1ExtnValue=new Pr.asn1.DEREnumerated(this.params.code),this.asn1ExtnValue.tohex()},this.oid="*********",void 0!=t&&(this.params=t)},Ti(Pr.asn1.x509.CRLReason,Pr.asn1.x509.Extension),Pr.asn1.x509.OCSPNonce=function(t){Pr.asn1.x509.OCSPNonce.superclass.constructor.call(this,t),this.params=void 0,this.getExtnValueHex=function(){return this.asn1ExtnValue=new Pr.asn1.DEROctetString(this.params),this.asn1ExtnValue.tohex()},this.oid="*******.********.1.2",void 0!=t&&(this.params=t)},Ti(Pr.asn1.x509.OCSPNonce,Pr.asn1.x509.Extension),Pr.asn1.x509.OCSPNoCheck=function(t){Pr.asn1.x509.OCSPNoCheck.superclass.constructor.call(this,t),this.params=void 0,this.getExtnValueHex=function(){return this.asn1ExtnValue=new Pr.asn1.DERNull,this.asn1ExtnValue.tohex()},this.oid="*******.********.1.5",void 0!=t&&(this.params=t)},Ti(Pr.asn1.x509.OCSPNoCheck,Pr.asn1.x509.Extension),Pr.asn1.x509.AdobeTimeStamp=function(t){Pr.asn1.x509.AdobeTimeStamp.superclass.constructor.call(this,t);var e=Pr,r=e.asn1,i=r.DERInteger,n=r.DERBoolean,s=r.DERSequence,a=r.x509.GeneralName;this.params=null,this.getExtnValueHex=function(){var t=this.params,e=[new i(1)];return e.push(new a({uri:t.uri})),void 0!=t.reqauth&&e.push(new n(t.reqauth)),this.asn1ExtnValue=new s({array:e}),this.asn1ExtnValue.tohex()},this.oid="1.2.840.113583.*******",void 0!==t&&this.setByParam(t)},Ti(Pr.asn1.x509.AdobeTimeStamp,Pr.asn1.x509.Extension),Pr.asn1.x509.X500Name=function(t){Pr.asn1.x509.X500Name.superclass.constructor.call(this),this.asn1Array=[],this.paramArray=[],this.sRule="utf8";var e=Pr,r=e.asn1,n=r.x509,s=n.RDN;this.setByString=function(t,e){void 0!==e&&(this.sRule=e);var r=t.split("/");r.shift();for(var i=[],n=0;n<r.length;n++)if(r[n].match(/^[^=]+=.+$/))i.push(r[n]);else{var a=i.length-1;i[a]=i[a]+"/"+r[n]}for(n=0;n<i.length;n++)this.asn1Array.push(new s({str:i[n],rule:this.sRule}))},this.setByLdapString=function(t,e){void 0!==e&&(this.sRule=e);var r=n.X500Name.ldapToCompat(t);this.setByString(r,e)},this.setByObject=function(t,e){for(var r in void 0!==e&&(this.sRule=e),t)if(t.hasOwnProperty(r)){var i=new s({str:r+"="+t[r],rule:this.sRule});this.asn1Array?this.asn1Array.push(i):this.asn1Array=[i]}},this.setByParam=function(t){if(void 0!==t.rule&&(this.sRule=t.rule),void 0!==t.array)this.paramArray=t.array;else if(void 0!==t.str)this.setByString(t.str);else if(void 0!==t.ldapstr)this.setByLdapString(t.ldapstr);else if(void 0!==t.hex)this.hTLV=t.hex;else if(void 0!==t.certissuer){var e=new ji;e.readCertPEM(t.certissuer),this.hTLV=e.getIssuerHex()}else if(void 0!==t.certsubject){e=new ji;e.readCertPEM(t.certsubject),this.hTLV=e.getSubjectHex()}else"object"===i(t)&&void 0===t.certsubject&&void 0===t.certissuer&&this.setByObject(t)},this.tohex=function(){if("string"==typeof this.hTLV)return this.hTLV;if(0==this.asn1Array.length&&this.paramArray.length>0)for(var t=0;t<this.paramArray.length;t++){var e={array:this.paramArray[t]};"utf8"!=this.sRule&&(e.rule=this.sRule);var i=new s(e);this.asn1Array.push(i)}var n=new r.DERSequence({array:this.asn1Array});return this.hTLV=n.tohex(),this.hTLV},this.getEncodedHex=function(){return this.tohex()},void 0!==t&&this.setByParam(t)},Ti(Pr.asn1.x509.X500Name,Pr.asn1.ASN1Object),Pr.asn1.x509.X500Name.compatToLDAP=function(t){if("/"!==t.substr(0,1))throw"malformed input";t=t.substr(1);var e=t.split("/");return e.reverse(),e=e.map((function(t){return t.replace(/,/,"\\,")})),e.join(",")},Pr.asn1.x509.X500Name.onelineToLDAP=function(t){return Pr.asn1.x509.X500Name.compatToLDAP(t)},Pr.asn1.x509.X500Name.ldapToCompat=function(t){for(var e=t.split(","),r=!1,i=[],n=0;e.length>0;n++){var s=e.shift();if(!0===r){var a=i.pop(),o=(a+","+s).replace(/\\,/g,",");i.push(o),r=!1}else i.push(s);"\\"===s.substr(-1,1)&&(r=!0)}return i=i.map((function(t){return t.replace("/","\\/")})),i.reverse(),"/"+i.join("/")},Pr.asn1.x509.X500Name.ldapToOneline=function(t){return Pr.asn1.x509.X500Name.ldapToCompat(t)},Pr.asn1.x509.RDN=function(t){Pr.asn1.x509.RDN.superclass.constructor.call(this),this.asn1Array=[],this.paramArray=[],this.sRule="utf8";var e=Pr.asn1.x509.AttributeTypeAndValue;this.setByParam=function(t){void 0!==t.rule&&(this.sRule=t.rule),void 0!==t.str&&this.addByMultiValuedString(t.str),void 0!==t.array&&(this.paramArray=t.array)},this.addByString=function(t){this.asn1Array.push(new Pr.asn1.x509.AttributeTypeAndValue({str:t,rule:this.sRule}))},this.addByMultiValuedString=function(t){for(var e=Pr.asn1.x509.RDN.parseString(t),r=0;r<e.length;r++)this.addByString(e[r])},this.tohex=function(){if(0==this.asn1Array.length&&this.paramArray.length>0)for(var t=0;t<this.paramArray.length;t++){var r=this.paramArray[t];void 0!==r.rule&&"utf8"!=this.sRule&&(r.rule=this.sRule);var i=new e(r);this.asn1Array.push(i)}var n=new Pr.asn1.DERSet({array:this.asn1Array});return this.TLV=n.tohex(),this.TLV},this.getEncodedHex=function(){return this.tohex()},void 0!==t&&this.setByParam(t)},Ti(Pr.asn1.x509.RDN,Pr.asn1.ASN1Object),Pr.asn1.x509.RDN.parseString=function(t){for(var e=t.split(/\+/),r=!1,i=[],n=0;e.length>0;n++){var s=e.shift();if(!0===r){var a=i.pop(),o=(a+"+"+s).replace(/\\\+/g,"+");i.push(o),r=!1}else i.push(s);"\\"===s.substr(-1,1)&&(r=!0)}var h=!1,u=[];for(n=0;i.length>0;n++){s=i.shift();if(!0===h){var c=u.pop();if(s.match(/"$/)){o=(c+"+"+s).replace(/^([^=]+)="(.*)"$/,"$1=$2");u.push(o),h=!1}else u.push(c+"+"+s)}else u.push(s);s.match(/^[^=]+="/)&&(h=!0)}return u},Pr.asn1.x509.AttributeTypeAndValue=function(t){Pr.asn1.x509.AttributeTypeAndValue.superclass.constructor.call(this),this.sRule="utf8",this.sType=null,this.sValue=null,this.dsType=null;var e=Pr,r=e.asn1,i=r.DERSequence,n=r.DERUTF8String,s=r.DERPrintableString,a=r.DERTeletexString,o=r.DERIA5String,h=r.DERVisibleString,u=r.DERBMPString,c=e.lang.String.isMail,l=e.lang.String.isPrintable;this.setByParam=function(t){if(void 0!==t.rule&&(this.sRule=t.rule),void 0!==t.ds&&(this.dsType=t.ds),void 0===t.value&&void 0!==t.str){var e=t.str,r=e.match(/^([^=]+)=(.+)$/);if(!r)throw new Error("malformed attrTypeAndValueStr: "+attrTypeAndValueStr);this.sType=r[1],this.sValue=r[2]}else this.sType=t.type,this.sValue=t.value},this.setByString=function(t,e){void 0!==e&&(this.sRule=e);var r=t.match(/^([^=]+)=(.+)$/);if(!r)throw new Error("malformed attrTypeAndValueStr: "+attrTypeAndValueStr);this.setByAttrTypeAndValueStr(r[1],r[2])},this._getDsType=function(){var t=this.sType,e=this.sValue,r=this.sRule;return"prn"===r?"CN"==t&&c(e)?"ia5":l(e)?"prn":"utf8":"utf8"===r?"CN"==t&&c(e)?"ia5":"C"==t?"prn":"utf8":"utf8"},this.setByAttrTypeAndValueStr=function(t,e,r){void 0!==r&&(this.sRule=r),this.sType=t,this.sValue=e},this.getValueObj=function(t,e){if("utf8"==t)return new n({str:e});if("prn"==t)return new s({str:e});if("tel"==t)return new a({str:e});if("ia5"==t)return new o({str:e});if("vis"==t)return new h({str:e});if("bmp"==t)return new u({str:e});throw new Error("unsupported directory string type: type="+t+" value="+e)},this.tohex=function(){null==this.dsType&&(this.dsType=this._getDsType());var t=Pr.asn1.x509.OID.atype2obj(this.sType),e=this.getValueObj(this.dsType,this.sValue),r=new i({array:[t,e]});return this.TLV=r.tohex(),this.TLV},this.getEncodedHex=function(){return this.tohex()},void 0!==t&&this.setByParam(t)},Ti(Pr.asn1.x509.AttributeTypeAndValue,Pr.asn1.ASN1Object),Pr.asn1.x509.SubjectPublicKeyInfo=function(t){Pr.asn1.x509.SubjectPublicKeyInfo.superclass.constructor.call(this);var e=Pr,r=e.asn1,i=r.DERInteger,n=r.DERBitString,s=r.DERObjectIdentifier,a=r.DERSequence,o=r.ASN1Util.newObject,h=r.x509,u=h.AlgorithmIdentifier,c=e.crypto;c.ECDSA,c.DSA;this.getASN1Object=function(){if(null==this.asn1AlgId||null==this.asn1SubjPKey)throw"algId and/or subjPubKey not set";var t=new a({array:[this.asn1AlgId,this.asn1SubjPKey]});return t},this.tohex=function(){var t=this.getASN1Object();return this.hTLV=t.tohex(),this.hTLV},this.getEncodedHex=function(){return this.tohex()},this.setPubKey=function(t){try{if(t instanceof qe){var e=o({seq:[{int:{bigint:t.n}},{int:{int:t.e}}]}),r=e.tohex();this.asn1AlgId=new u({name:"rsaEncryption"}),this.asn1SubjPKey=new n({hex:"00"+r})}}catch(c){}try{if(t instanceof Pr.crypto.ECDSA){var a=new s({name:t.curveName});this.asn1AlgId=new u({name:"ecPublicKey",asn1params:a}),this.asn1SubjPKey=new n({hex:"00"+t.pubKeyHex})}}catch(c){}try{if(t instanceof Pr.crypto.DSA){a=new o({seq:[{int:{bigint:t.p}},{int:{bigint:t.q}},{int:{bigint:t.g}}]});this.asn1AlgId=new u({name:"dsa",asn1params:a});var h=new i({bigint:t.y});this.asn1SubjPKey=new n({hex:"00"+h.tohex()})}}catch(c){}},void 0!==t&&this.setPubKey(t)},Ti(Pr.asn1.x509.SubjectPublicKeyInfo,Pr.asn1.ASN1Object),Pr.asn1.x509.Time=function(t){Pr.asn1.x509.Time.superclass.constructor.call(this);var e=Pr,r=e.asn1,i=r.DERUTCTime,n=r.DERGeneralizedTime;this.params=null,this.type=null,this.setTimeParams=function(t){this.timeParams=t},this.setByParam=function(t){this.params=t},this.getType=function(t){return t.match(/^[0-9]{12}Z$/)?"utc":t.match(/^[0-9]{14}Z$/)?"gen":t.match(/^[0-9]{12}\.[0-9]+Z$/)?"utc":t.match(/^[0-9]{14}\.[0-9]+Z$/)?"gen":null},this.tohex=function(){var t=this.params,e=null;if("string"==typeof t&&(t={str:t}),null==t||!t.str||null!=t.type&&void 0!=t.type||(t.type=this.getType(t.str)),null!=t&&t.str?("utc"==t.type&&(e=new i(t.str)),"gen"==t.type&&(e=new n(t.str))):e="gen"==this.type?new n:new i,null==e)throw new Error("wrong setting for Time");return this.TLV=e.tohex(),this.TLV},this.getEncodedHex=function(){return this.tohex()},void 0!=t&&this.setByParam(t)},Pr.asn1.x509.Time_bak=function(t){Pr.asn1.x509.Time_bak.superclass.constructor.call(this);var e=Pr,r=e.asn1,i=r.DERUTCTime,n=r.DERGeneralizedTime;this.setTimeParams=function(t){this.timeParams=t},this.tohex=function(){var t=null;return t=null!=this.timeParams?"utc"==this.type?new i(this.timeParams):new n(this.timeParams):"utc"==this.type?new i:new n,this.TLV=t.tohex(),this.TLV},this.getEncodedHex=function(){return this.tohex()},this.type="utc",void 0!==t&&(void 0!==t.type?this.type=t.type:void 0!==t.str&&(t.str.match(/^[0-9]{12}Z$/)&&(this.type="utc"),t.str.match(/^[0-9]{14}Z$/)&&(this.type="gen")),this.timeParams=t)},Ti(Pr.asn1.x509.Time,Pr.asn1.ASN1Object),Pr.asn1.x509.AlgorithmIdentifier=function(t){Pr.asn1.x509.AlgorithmIdentifier.superclass.constructor.call(this),this.nameAlg=null,this.asn1Alg=null,this.asn1Params=null,this.paramEmpty=!1;var e=Pr,r=e.asn1,i=r.x509.AlgorithmIdentifier.PSSNAME2ASN1TLV;if(this.tohex=function(){if(null===this.nameAlg&&null===this.asn1Alg)throw new Error("algorithm not specified");if(null!==this.nameAlg){var t=null;for(var e in i)e===this.nameAlg&&(t=i[e]);if(null!==t)return this.hTLV=t,this.hTLV}null!==this.nameAlg&&null===this.asn1Alg&&(this.asn1Alg=r.x509.OID.name2obj(this.nameAlg));var n=[this.asn1Alg];null!==this.asn1Params&&n.push(this.asn1Params);var s=new r.DERSequence({array:n});return this.hTLV=s.tohex(),this.hTLV},this.getEncodedHex=function(){return this.tohex()},void 0!==t&&(void 0!==t.name&&(this.nameAlg=t.name),void 0!==t.asn1params&&(this.asn1Params=t.asn1params),void 0!==t.paramempty&&(this.paramEmpty=t.paramempty)),null===this.asn1Params&&!1===this.paramEmpty&&null!==this.nameAlg){void 0!==this.nameAlg.name&&(this.nameAlg=this.nameAlg.name);var n=this.nameAlg.toLowerCase();"withdsa"!==n.substr(-7,7)&&"withecdsa"!==n.substr(-9,9)&&(this.asn1Params=new r.DERNull)}},Ti(Pr.asn1.x509.AlgorithmIdentifier,Pr.asn1.ASN1Object),Pr.asn1.x509.AlgorithmIdentifier.PSSNAME2ASN1TLV={SHAwithRSAandMGF1:"300d06092a864886f70d01010a3000",SHA256withRSAandMGF1:"303d06092a864886f70d01010a3030a00d300b0609608648016503040201a11a301806092a864886f70d010108300b0609608648016503040201a203020120",SHA384withRSAandMGF1:"303d06092a864886f70d01010a3030a00d300b0609608648016503040202a11a301806092a864886f70d010108300b0609608648016503040202a203020130",SHA512withRSAandMGF1:"303d06092a864886f70d01010a3030a00d300b0609608648016503040203a11a301806092a864886f70d010108300b0609608648016503040203a203020140"},Pr.asn1.x509.GeneralName=function(t){Pr.asn1.x509.GeneralName.superclass.constructor.call(this);var e=Pr,r=e.asn1,i=r.x509,n=i.X500Name,s=i.OtherName,a=r.DERIA5String,o=(r.DERPrintableString,r.DEROctetString),h=r.DERTaggedObject,u=r.ASN1Object,c=Error;this.params=null,this.setByParam=function(t){this.params=t},this.tohex=function(){var t,e,r=this.params,i=!1;if(void 0!==r.other)t="a0",e=new s(r.other);else if(void 0!==r.rfc822)t="81",e=new a({str:r.rfc822});else if(void 0!==r.dns)t="82",e=new a({str:r.dns});else if(void 0!==r.dn)t="a4",i=!0,e="string"===typeof r.dn?new n({str:r.dn}):r.dn instanceof Pr.asn1.x509.X500Name?r.dn:new n(r.dn);else if(void 0!==r.ldapdn)t="a4",i=!0,e=new n({ldapstr:r.ldapdn});else if(void 0!==r.certissuer||void 0!==r.certsubj){var l,f;t="a4",i=!0;var g=null;if(void 0!==r.certsubj?(l=!1,f=r.certsubj):(l=!0,f=r.certissuer),f.match(/^[0-9A-Fa-f]+$/),-1!=f.indexOf("-----BEGIN ")&&(g=ri(f)),null==g)throw new Error("certsubj/certissuer not cert");var p,d=new ji;d.hex=g,p=l?d.getIssuerHex():d.getSubjectHex(),e=new u,e.hTLV=p}else if(void 0!==r.uri)t="86",e=new a({str:r.uri});else{if(void 0===r.ip)throw new c("improper params");var v;t="87";var m=r.ip;try{if(m.match(/^[0-9a-f]+$/)){var y=m.length;if(8!=y&&16!=y&&32!=y&&64!=y)throw"err";v=m}else v=di(m)}catch(S){throw new c("malformed IP address: "+r.ip+":"+S.message)}e=new o({hex:v})}var x=new h({tag:t,explicit:i,obj:e});return x.tohex()},this.getEncodedHex=function(){return this.tohex()},void 0!==t&&this.setByParam(t)},Ti(Pr.asn1.x509.GeneralName,Pr.asn1.ASN1Object),Pr.asn1.x509.GeneralNames=function(t){Pr.asn1.x509.GeneralNames.superclass.constructor.call(this);var e=Pr,r=e.asn1;this.setByParamArray=function(t){for(var e=0;e<t.length;e++){var i=new r.x509.GeneralName(t[e]);this.asn1Array.push(i)}},this.tohex=function(){var t=new r.DERSequence({array:this.asn1Array});return t.tohex()},this.getEncodedHex=function(){return this.tohex()},this.asn1Array=new Array,"undefined"!=typeof t&&this.setByParamArray(t)},Ti(Pr.asn1.x509.GeneralNames,Pr.asn1.ASN1Object),Pr.asn1.x509.OtherName=function(t){Pr.asn1.x509.OtherName.superclass.constructor.call(this);var e=Pr,r=e.asn1,i=r.DERObjectIdentifier,n=r.DERSequence,s=r.ASN1Util.newObject;this.params=null,this.setByParam=function(t){this.params=t},this.tohex=function(){var t=this.params;if(void 0==t.oid||void 0==t.value)throw new Error("oid or value not specified");var e=new i({oid:t.oid}),r=s({tag:{tag:"a0",explicit:!0,obj:t.value}}),a=new n({array:[e,r]});return a.tohex()},this.getEncodedHex=function(){return this.tohex()},void 0!==t&&this.setByParam(t)},Ti(Pr.asn1.x509.OtherName,Pr.asn1.ASN1Object),Pr.asn1.x509.OID=new function(){var t=Pr.asn1.DERObjectIdentifier;this.name2oidList={sha1:"********.2.26",sha256:"2.16.840.*********.2.1",sha384:"2.16.840.*********.2.2",sha512:"2.16.840.*********.2.3",sha224:"2.16.840.*********.2.4",md5:"1.2.840.113549.2.5",md2:"********.2.2.1",ripemd160:"********.2.1",MD2withRSA:"1.2.840.113549.1.1.2",MD4withRSA:"1.2.840.113549.1.1.3",MD5withRSA:"1.2.840.113549.1.1.4",SHA1withRSA:"1.2.840.113549.1.1.5","pkcs1-MGF":"1.2.840.113549.1.1.8",rsaPSS:"1.2.840.113549.1.1.10",SHA224withRSA:"1.2.840.113549.1.1.14",SHA256withRSA:"1.2.840.113549.1.1.11",SHA384withRSA:"1.2.840.113549.1.1.12",SHA512withRSA:"1.2.840.113549.1.1.13",SHA1withECDSA:"1.2.840.10045.4.1",SHA224withECDSA:"1.2.840.10045.4.3.1",SHA256withECDSA:"1.2.840.10045.4.3.2",SHA384withECDSA:"1.2.840.10045.4.3.3",SHA512withECDSA:"1.2.840.10045.4.3.4",dsa:"1.2.840.10040.4.1",SHA1withDSA:"1.2.840.10040.4.3",SHA224withDSA:"2.16.840.*********.3.1",SHA256withDSA:"2.16.840.*********.3.2",rsaEncryption:"1.2.840.113549.1.1.1",commonName:"*******",countryName:"*******",localityName:"*******",stateOrProvinceName:"*******",streetAddress:"*******",organizationName:"********",organizationalUnitName:"********",domainComponent:"0.9.2342.19200300.100.1.25",userId:"0.9.2342.19200300.100.1.1",surname:"*******",givenName:"*******2",title:"********",distinguishedName:"*******9",emailAddress:"1.2.840.113549.1.9.1",description:"********",businessCategory:"********",postalCode:"********",uniqueIdentifier:"*******5",organizationIdentifier:"*******7",jurisdictionOfIncorporationL:"*******.4.1.311.********",jurisdictionOfIncorporationSP:"*******.4.1.311.********",jurisdictionOfIncorporationC:"*******.4.1.311.********",subjectDirectoryAttributes:"********",subjectKeyIdentifier:"*********",keyUsage:"*********",subjectAltName:"*********",issuerAltName:"*********",basicConstraints:"*********",cRLNumber:"*********",cRLReason:"*********",nameConstraints:"*********",cRLDistributionPoints:"*********",certificatePolicies:"*********",anyPolicy:"*********.0",authorityKeyIdentifier:"*********",policyConstraints:"*********",extKeyUsage:"*********",authorityInfoAccess:"*******.*******.1",ocsp:"*******.********.1",ocspBasic:"*******.********.1.1",ocspNonce:"*******.********.1.2",ocspNoCheck:"*******.********.1.5",caIssuers:"*******.********.2",anyExtendedKeyUsage:"*********.0",serverAuth:"*******.*******.1",clientAuth:"*******.*******.2",codeSigning:"*******.*******.3",emailProtection:"*******.*******.4",timeStamping:"*******.*******.8",ocspSigning:"*******.*******.9",dateOfBirth:"*******.*******.1",placeOfBirth:"*******.*******.2",gender:"*******.*******.3",countryOfCitizenship:"*******.*******.4",countryOfResidence:"*******.*******.5",ecPublicKey:"1.2.840.10045.2.1","P-256":"1.2.840.10045.3.1.7",secp256r1:"1.2.840.10045.3.1.7",secp256k1:"*********.10",secp384r1:"*********.34",secp521r1:"*********.35",pkcs5PBES2:"1.2.840.113549.1.5.13",pkcs5PBKDF2:"1.2.840.113549.1.5.12","des-EDE3-CBC":"1.2.840.113549.3.7",data:"1.2.840.113549.1.7.1","signed-data":"1.2.840.113549.1.7.2","enveloped-data":"1.2.840.113549.1.7.3","digested-data":"1.2.840.113549.1.7.5","encrypted-data":"1.2.840.113549.1.7.6","authenticated-data":"1.2.840.113549.********.2",tstinfo:"1.2.840.113549.********.4",signingCertificate:"1.2.840.113549.********.12",timeStampToken:"1.2.840.113549.********.14",signaturePolicyIdentifier:"1.2.840.113549.********.15",etsArchiveTimeStamp:"1.2.840.113549.********.27",signingCertificateV2:"1.2.840.113549.********.47",etsArchiveTimeStampV2:"1.2.840.113549.********.48",extensionRequest:"1.2.840.113549.1.9.14",contentType:"1.2.840.113549.1.9.3",messageDigest:"1.2.840.113549.1.9.4",signingTime:"1.2.840.113549.1.9.5",counterSignature:"1.2.840.113549.1.9.6",archiveTimeStampV3:"0.4.0.1733.2.4",pdfRevocationInfoArchival:"1.2.840.113583.1.1.8",adobeTimeStamp:"1.2.840.113583.*******"},this.atype2oidList={CN:"*******",L:"*******",ST:"*******",O:"********",OU:"********",C:"*******",STREET:"*******",DC:"0.9.2342.19200300.100.1.25",UID:"0.9.2342.19200300.100.1.1",SN:"*******",T:"********",DN:"*******9",E:"1.2.840.113549.1.9.1",description:"********",businessCategory:"********",postalCode:"********",serialNumber:"2.5.4.5",uniqueIdentifier:"*******5",organizationIdentifier:"*******7",jurisdictionOfIncorporationL:"*******.4.1.311.********",jurisdictionOfIncorporationSP:"*******.4.1.311.********",jurisdictionOfIncorporationC:"*******.4.1.311.********"},this.objCache={},this.name2obj=function(e){if("undefined"!=typeof this.objCache[e])return this.objCache[e];if("undefined"==typeof this.name2oidList[e])throw"Name of ObjectIdentifier not defined: "+e;var r=this.name2oidList[e],i=new t({oid:r});return this.objCache[e]=i,i},this.atype2obj=function(e){if(void 0!==this.objCache[e])return this.objCache[e];var r;if(e.match(/^\d+\.\d+\.[0-9.]+$/))r=e;else if(void 0!==this.atype2oidList[e])r=this.atype2oidList[e];else{if(void 0===this.name2oidList[e])throw new Error("AttributeType name undefined: "+e);r=this.name2oidList[e]}var i=new t({oid:r});return this.objCache[e]=i,i},this.registerOIDs=function(t){if(this.checkOIDs(t))for(var e in t)this.name2oidList[e]=t[e]},this.checkOIDs=function(t){try{var e=Object.keys(t);return 0!=e.length&&(e.map((function(t,e,r){var i=this[t];if(!i.match(/^[0-2]\.[0-9.]+$/))throw new Error("value is not OID")}),t),!0)}catch(r){return!1}}},Pr.asn1.x509.OID.oid2name=function(t){var e=Pr.asn1.x509.OID.name2oidList;for(var r in e)if(e[r]==t)return r;return""},Pr.asn1.x509.OID.oid2atype=function(t){var e=Pr.asn1.x509.OID.atype2oidList;for(var r in e)if(e[r]==t)return r;return t},Pr.asn1.x509.OID.name2oid=function(t){if(t.match(/^[0-9.]+$/))return t;var e=Pr.asn1.x509.OID.name2oidList;return void 0===e[t]?"":e[t]},Pr.asn1.x509.X509Util={},Pr.asn1.x509.X509Util.newCertPEM=function(t){var e=Pr.asn1.x509,r=(e.TBSCertificate,e.Certificate),i=new r(t);return i.getPEM()},"undefined"!=typeof Pr&&Pr||(Pr={}),"undefined"!=typeof Pr.asn1&&Pr.asn1||(Pr.asn1={}),"undefined"!=typeof Pr.asn1.cms&&Pr.asn1.cms||(Pr.asn1.cms={}),Pr.asn1.cms.Attribute=function(t){var e=Error,r=Pr,i=r.asn1,n=i.DERSequence,s=i.DERSet,a=i.DERObjectIdentifier;this.params=null,this.typeOid=null,this.setByParam=function(t){this.params=t},this.getValueArray=function(){throw new e("not yet implemented abstract")},this.tohex=function(){var t=new a({oid:this.typeOid}),e=new s({array:this.getValueArray()}),r=new n({array:[t,e]});return r.tohex()},this.getEncodedHex=function(){return this.tohex()}},Ti(Pr.asn1.cms.Attribute,Pr.asn1.ASN1Object),Pr.asn1.cms.ContentType=function(t){var e=Pr,r=e.asn1;r.cms.ContentType.superclass.constructor.call(this),this.typeOid="1.2.840.113549.1.9.3",this.getValueArray=function(){var t=new r.DERObjectIdentifier(this.params.type);return[t]},void 0!=t&&this.setByParam(t)},Ti(Pr.asn1.cms.ContentType,Pr.asn1.cms.Attribute),Pr.asn1.cms.MessageDigest=function(t){var e=Pr,r=e.asn1,i=r.DEROctetString,n=r.cms;n.MessageDigest.superclass.constructor.call(this),this.typeOid="1.2.840.113549.1.9.4",this.getValueArray=function(){var t=new i(this.params);return[t]},void 0!=t&&this.setByParam(t)},Ti(Pr.asn1.cms.MessageDigest,Pr.asn1.cms.Attribute),Pr.asn1.cms.SigningTime=function(t){var e=Pr,r=e.asn1;r.cms.SigningTime.superclass.constructor.call(this),this.typeOid="1.2.840.113549.1.9.5",this.getValueArray=function(){var t=new r.x509.Time(this.params);return[t]},void 0!=t&&this.setByParam(t)},Ti(Pr.asn1.cms.SigningTime,Pr.asn1.cms.Attribute),Pr.asn1.cms.SigningCertificate=function(t){var e=Error,r=Pr,i=r.asn1,n=i.DERSequence,s=i.cms,a=s.ESSCertID;r.crypto;s.SigningCertificate.superclass.constructor.call(this),this.typeOid="1.2.840.113549.********.12",this.getValueArray=function(){if(null==this.params||void 0==this.params||void 0==this.params.array)throw new e("parameter 'array' not specified");for(var r=this.params.array,i=[],s=0;s<r.length;s++){var o=r[s];0!=t.hasis||"string"!=typeof o||-1==o.indexOf("-----BEGIN")&&!Br.isASN1HEX(o)||(o={cert:o}),0!=o.hasis&&0==t.hasis&&(o.hasis=!1),i.push(new a(o))}var h=new n({array:i}),u=new n({array:[h]});return[u]},void 0!=t&&this.setByParam(t)},Ti(Pr.asn1.cms.SigningCertificate,Pr.asn1.cms.Attribute),Pr.asn1.cms.ESSCertID=function(t){Pr.asn1.cms.ESSCertID.superclass.constructor.call(this);var e=Error,r=Pr,i=r.asn1,n=i.DEROctetString,s=i.DERSequence,a=i.cms.IssuerSerial;this.params=null,this.getCertHash=function(t,i){if(void 0!=t.hash)return t.hash;if("string"==typeof t&&-1==t.indexOf("-----BEGIN")&&!Br.isASN1HEX(t))return t;var n,s,a;if("string"==typeof t)n=t;else{if(void 0==t.cert)throw new e("hash nor cert unspecified");n=t.cert}if(s=-1!=n.indexOf("-----BEGIN")?ri(n):n,"string"==typeof t&&(-1!=t.indexOf("-----BEGIN")?s=ri(t):Br.isASN1HEX(t)&&(s=t)),void 0!=t.alg)a=t.alg;else{if(void 0==i)throw new e("hash alg unspecified");a=i}return r.crypto.Util.hashHex(s,a)},this.tohex=function(){var t=this.params,e=this.getCertHash(t,"sha1"),r=[];r.push(new n({hex:e})),("string"==typeof t&&-1!=t.indexOf("-----BEGIN")||void 0!=t.cert&&0!=t.hasis||void 0!=t.issuer&&void 0!=t.serial)&&r.push(new a(t));var i=new s({array:r});return i.tohex()},this.getEncodedHex=function(){return this.tohex()},void 0!=t&&this.setByParam(t)},Ti(Pr.asn1.cms.ESSCertID,Pr.asn1.ASN1Object),Pr.asn1.cms.SigningCertificateV2=function(t){var e=Error,r=Pr,i=r.asn1,n=i.DERSequence,s=(i.x509,i.cms),a=s.ESSCertIDv2;r.crypto;s.SigningCertificateV2.superclass.constructor.call(this),this.typeOid="1.2.840.113549.********.47",this.getValueArray=function(){if(null==this.params||void 0==this.params||void 0==this.params.array)throw new e("parameter 'array' not specified");for(var r=this.params.array,i=[],s=0;s<r.length;s++){var o=r[s];void 0==t.alg&&0!=t.hasis||"string"!=typeof o||-1==o.indexOf("-----BEGIN")&&!Br.isASN1HEX(o)||(o={cert:o}),void 0==o.alg&&void 0!=t.alg&&(o.alg=t.alg),0!=o.hasis&&0==t.hasis&&(o.hasis=!1),i.push(new a(o))}var h=new n({array:i}),u=new n({array:[h]});return[u]},void 0!=t&&this.setByParam(t)},Ti(Pr.asn1.cms.SigningCertificateV2,Pr.asn1.cms.Attribute),Pr.asn1.cms.ESSCertIDv2=function(t){Pr.asn1.cms.ESSCertIDv2.superclass.constructor.call(this);Error;var e=Pr,r=e.asn1,i=r.DEROctetString,n=r.DERSequence,s=r.cms.IssuerSerial,a=r.x509.AlgorithmIdentifier;this.params=null,this.tohex=function(){var t=this.params,e=this.getCertHash(t,"sha256"),r=[];void 0!=t.alg&&"sha256"!=t.alg&&r.push(new a({name:t.alg})),r.push(new i({hex:e})),("string"==typeof t&&-1!=t.indexOf("-----BEGIN")||void 0!=t.cert&&0!=t.hasis||void 0!=t.issuer&&void 0!=t.serial)&&r.push(new s(t));var o=new n({array:r});return o.tohex()},this.getEncodedHex=function(){return this.tohex()},void 0!=t&&this.setByParam(t)},Ti(Pr.asn1.cms.ESSCertIDv2,Pr.asn1.cms.ESSCertID),Pr.asn1.cms.IssuerSerial=function(t){var e=Error,r=Pr,i=r.asn1,n=i.DERInteger,s=i.DERSequence,a=i.cms,o=i.x509,h=o.GeneralNames,u=ji;a.IssuerSerial.superclass.constructor.call(this),this.setByParam=function(t){this.params=t},this.tohex=function(){var t,r,i=this.params;if("string"==typeof i&&-1!=i.indexOf("-----BEGIN")||void 0!=i.cert){var a;a=void 0!=i.cert?i.cert:i;var o=new u;o.readCertPEM(a),t=o.getIssuer(),r={hex:o.getSerialNumberHex()}}else{if(void 0==i.issuer||!i.serial)throw new e("cert or issuer and serial parameter not specified");t=i.issuer,r=i.serial}var c=new h([{dn:t}]),l=new n(r),f=new s({array:[c,l]});return f.tohex()},this.getEncodedHex=function(){return this.tohex()},void 0!=t&&this.setByParam(t)},Ti(Pr.asn1.cms.IssuerSerial,Pr.asn1.ASN1Object),Pr.asn1.cms.SignerIdentifier=function(t){var e=Pr,r=e.asn1,i=(r.DERInteger,r.DERSequence,r.cms),n=i.IssuerAndSerialNumber,s=i.SubjectKeyIdentifier,a=r.x509;a.X500Name,Error;i.SignerIdentifier.superclass.constructor.call(this),this.params=null,this.tohex=function(){var t=this.params;if("isssn"==t.type){var e=new n(t);return e.tohex()}if("skid"==t.type){var r=new s(t);return r.tohex()}throw new Error("wrong property for isssn or skid")},this.getEncodedHex=function(){return this.tohex()},void 0!=t&&this.setByParam(t)},Ti(Pr.asn1.cms.SignerIdentifier,Pr.asn1.ASN1Object),Pr.asn1.cms.IssuerAndSerialNumber=function(t){var e=Pr,r=e.asn1,i=r.DERInteger,n=r.DERSequence,s=r.cms,a=r.x509,o=a.X500Name,h=ji,u=Error;s.IssuerAndSerialNumber.superclass.constructor.call(this),this.params=null,this.tohex=function(){var t,e,r=this.params;if("string"==typeof r&&-1!=r.indexOf("-----BEGIN")||void 0!=r.cert){var s;s=void 0!=r.cert?r.cert:r;var a=new h;a.readCertPEM(s),t=a.getIssuer(),e={hex:a.getSerialNumberHex()}}else{if(void 0==r.issuer||!r.serial)throw new u("cert or issuer and serial parameter not specified");t=r.issuer,e=r.serial}var c=new o(t),l=new i(e),f=new n({array:[c,l]});return f.tohex()},this.getEncodedHex=function(){return this.tohex()},this.setByParam=function(t){this.params=t},void 0!=t&&this.setByParam(t)},Ti(Pr.asn1.cms.IssuerAndSerialNumber,Pr.asn1.ASN1Object),Pr.asn1.cms.SubjectKeyIdentifier=function(t){var e=Pr,r=e.asn1,i=(r.DERInteger,r.DERSequence,r.ASN1Util.newObject),n=r.cms,s=(n.IssuerAndSerialName,n.SubjectKeyIdentifier,r.x509),a=(s.X500Name,ji),o=Error;n.SubjectKeyIdentifier.superclass.constructor.call(this),this.tohex=function(){var t,e=this.params;if(void 0==e.cert&&void 0==e.skid)throw new o("property cert nor skid undefined");if(void 0!=e.cert){var r=new a(e.cert),n=r.getExtSubjectKeyIdentifier();t=n.kid.hex}else void 0!=e.skid&&(t=e.skid);var s=i({tag:{tage:"a0",obj:{octstr:{hex:t}}}});return s.tohex()},this.getEncodedHex=function(){return this.tohex()},void 0!=t&&this.setByParam(t)},Ti(Pr.asn1.cms.SubjectKeyIdentifier,Pr.asn1.ASN1Object),Pr.asn1.cms.AttributeList=function(t){var e=Error,r=Pr,i=r.asn1,n=i.DERSet,s=i.cms;s.AttributeList.superclass.constructor.call(this),this.params=null,this.hTLV=null,this.setByParam=function(t){this.params=t},this.tohex=function(){var t=this.params;if(null!=this.hTLV)return this.hTLV;var r=!0;void 0!=t.sortflag&&(r=t.sortflag);for(var i=t.array,a=[],o=0;o<i.length;o++){var h=i[o],u=h.attr;if("contentType"==u)a.push(new s.ContentType(h));else if("messageDigest"==u)a.push(new s.MessageDigest(h));else if("signingTime"==u)a.push(new s.SigningTime(h));else if("signingCertificate"==u)a.push(new s.SigningCertificate(h));else if("signingCertificateV2"==u)a.push(new s.SigningCertificateV2(h));else if("signaturePolicyIdentifier"==u)a.push(new Pr.asn1.cades.SignaturePolicyIdentifier(h));else{if("signatureTimeStamp"!=u&&"timeStampToken"!=u)throw new e("unknown attr: "+u);a.push(new Pr.asn1.cades.SignatureTimeStamp(h))}}var c=new n({array:a,sortflag:r});return this.hTLV=c.tohex(),this.hTLV},this.getEncodedHex=function(){return this.tohex()},void 0!=t&&this.setByParam(t)},Ti(Pr.asn1.cms.AttributeList,Pr.asn1.ASN1Object),Pr.asn1.cms.SignerInfo=function(t){var e=Error,r=Pr,i=r.asn1,n=i.DERInteger,s=i.DEROctetString,a=i.DERSequence,o=i.DERTaggedObject,h=i.cms,u=h.SignerIdentifier,c=h.AttributeList,l=(h.ContentType,h.EncapsulatedContentInfo,h.MessageDigest,h.SignedData,i.x509),f=l.AlgorithmIdentifier,g=r.crypto,p=Bi;h.SignerInfo.superclass.constructor.call(this),this.params=null,this.sign=function(){var t=this.params,e=t.sigalg,r=new c(t.sattrs).tohex(),i=p.getKey(t.signkey),n=new g.Signature({alg:e});n.init(i),n.updateHex(r);var s=n.sign();t.sighex=s},this.tohex=function(){var t=this.params,r=[];if(r.push(new n({int:t.version})),r.push(new u(t.id)),r.push(new f({name:t.hashalg})),void 0!=t.sattrs){var i=new c(t.sattrs);try{r.push(new o({tag:"a0",explicit:!1,obj:i}))}catch(l){throw new e("si sattr error: "+l)}}if(void 0!=t.sigalgfield?r.push(new f({name:t.sigalgfield})):r.push(new f({name:t.sigalg})),void 0==t.sighex&&void 0!=t.signkey&&this.sign(),r.push(new s({hex:t.sighex})),void 0!=t.uattrs){i=new c(t.uattrs);try{r.push(new o({tag:"a1",explicit:!1,obj:i}))}catch(l){throw new e("si uattr error: "+l)}}var h=new a({array:r});return h.tohex()},this.getEncodedHex=function(){return this.tohex()},void 0!=t&&this.setByParam(t)},Ti(Pr.asn1.cms.SignerInfo,Pr.asn1.ASN1Object),Pr.asn1.cms.EncapsulatedContentInfo=function(t){var e=Pr,r=e.asn1,i=r.DERTaggedObject,n=r.DERSequence,s=r.DERObjectIdentifier,a=r.DEROctetString,o=r.cms;o.EncapsulatedContentInfo.superclass.constructor.call(this),this.params=null,this.tohex=function(){var t=this.params,e=[];if(e.push(new s(t.type)),void 0!=t.content&&(void 0!=t.content.hex||void 0!=t.content.str)&&1!=t.isDetached){var r=new a(t.content),o=new i({tag:"a0",explicit:!0,obj:r});e.push(o)}var h=new n({array:e});return h.tohex()},this.getEncodedHex=function(){return this.tohex()},this.setByParam=function(t){this.params=t},void 0!=t&&this.setByParam(t)},Ti(Pr.asn1.cms.EncapsulatedContentInfo,Pr.asn1.ASN1Object),Pr.asn1.cms.ContentInfo=function(t){var e=Pr,r=e.asn1,i=r.DERTaggedObject,n=r.DERSequence,s=r.DERObjectIdentifier,a=r.x509;a.OID.name2obj;Pr.asn1.cms.ContentInfo.superclass.constructor.call(this),this.params=null,this.tohex=function(){var t=this.params,e=[];e.push(new s(t.type));var r=new i({tag:"a0",explicit:!0,obj:t.obj});e.push(r);var a=new n({array:e});return a.tohex()},this.getEncodedHex=function(){return this.tohex()},this.setByParam=function(t){this.params=t},void 0!=t&&this.setByParam(t)},Ti(Pr.asn1.cms.ContentInfo,Pr.asn1.ASN1Object),Pr.asn1.cms.SignedData=function(t){Error;var e=Pr,r=e.asn1,i=(r.ASN1Object,r.DERInteger),n=r.DERSet,s=r.DERSequence,a=(r.DERTaggedObject,r.cms),o=a.EncapsulatedContentInfo,h=a.SignerInfo,u=a.ContentInfo,c=a.CertificateSet,l=a.RevocationInfoChoices,f=r.x509,g=f.AlgorithmIdentifier;Pr.asn1.cms.SignedData.superclass.constructor.call(this),this.params=null,this.checkAndFixParam=function(){var t=this.params;this._setDigestAlgs(t),this._setContentTypeByEContent(t),this._setMessageDigestByEContent(t),this._setSignerInfoVersion(t),this._setSignedDataVersion(t)},this._setDigestAlgs=function(t){for(var e={},r=t.sinfos,i=0;i<r.length;i++){var n=r[i];e[n.hashalg]=1}t.hashalgs=Object.keys(e).sort()},this._setContentTypeByEContent=function(t){for(var e=t.econtent.type,r=t.sinfos,i=0;i<r.length;i++){var n=r[i],s=this._getAttrParamByName(n,"contentType");s.type=e}},this._setMessageDigestByEContent=function(t){var e=t.econtent,r=(t.econtent.type,e.content.hex);void 0==r&&"data"==e.type&&void 0!=e.content.str&&(r=Yr(e.content.str));for(var i=t.sinfos,n=0;n<i.length;n++){var s=i[n],a=s.hashalg,o=this._getAttrParamByName(s,"messageDigest"),h=Pr.crypto.Util.hashHex(r,a);o.hex=h}},this._getAttrParamByName=function(t,e){for(var r=t.sattrs.array,i=0;i<r.length;i++)if(r[i].attr==e)return r[i]},this._setSignerInfoVersion=function(t){for(var e=t.sinfos,r=0;r<e.length;r++){var i=e[r],n=1;"skid"==i.id.type&&(n=3),i.version=n}},this._setSignedDataVersion=function(t){var e=this._getSignedDataVersion(t);t.version=e},this._getSignedDataVersion=function(t){if(void 0!=t.revinfos)for(var e=t.revinfos,r=0;r<e.length;r++){var i=e[r];if(void 0!=i.ocsp)return 5}var n=t.sinfos;for(r=0;r<n.length;r++){var s=t.sinfos[r];if(3==s.version)return 3}return"data"!=t.econtent.type?3:1},this.tohex=function(){var t=this.params;void 0!=this.getEncodedHexPrepare&&this.getEncodedHexPrepare(),1!=t.fixed&&this.checkAndFixParam();var e=[];e.push(new i({int:t.version}));for(var r=[],a=0;a<t.hashalgs.length;a++){var u=t.hashalgs[a];r.push(new g({name:u}))}e.push(new n({array:r})),e.push(new o(t.econtent)),void 0!=t.certs&&e.push(new c(t.certs)),void 0!=t.revinfos&&e.push(new l(t.revinfos));var f=[];for(a=0;a<t.sinfos.length;a++){var p=t.sinfos[a];f.push(new h(p))}e.push(new n({array:f}));var d=new s({array:e});return d.tohex()},this.getEncodedHex=function(){return this.tohex()},this.getContentInfo=function(){var t=new u({type:"signed-data",obj:this});return t},this.getContentInfoEncodedHex=function(){return this.getContentInfo().tohex()},void 0!=t&&this.setByParam(t)},Ti(Pr.asn1.cms.SignedData,Pr.asn1.ASN1Object),Pr.asn1.cms.CertificateSet=function(t){Pr.asn1.cms.CertificateSet.superclass.constructor.call(this);var e=Error,r=Pr.asn1,i=r.DERTaggedObject,n=r.DERSet,s=r.ASN1Object;this.params=null,this.tohex=function(){var t,r=this.params,a=[];if(r instanceof Array)t=r;else{if(void 0==r.array)throw new e("cert array not specified");t=r.array}for(var o=0;o<t.length;o++){var h=t[o],u=ri(h),c=new s;c.hTLV=u,a.push(c)}var l={array:a};0==r.sortflag&&(l.sortflag=!1);var f=new n(l),g=new i({tag:"a0",explicit:!1,obj:f});return g.tohex()},this.getEncodedHex=function(){return this.tohex()},void 0!=t&&this.setByParam(t)},Ti(Pr.asn1.cms.CertificateSet,Pr.asn1.ASN1Object),Pr.asn1.cms.RevocationInfoChoices=function(t){Pr.asn1.cms.RevocationInfoChoices.superclass.constructor.call(this),this.params=null,this.tohex=function(){var t=this.params;if(!t instanceof Array)throw new Error("params is not array");for(var e=[],r=0;r<t.length;r++)e.push(new Pr.asn1.cms.RevocationInfoChoice(t[r]));var i=Pr.asn1.ASN1Util.newObject({tag:{tagi:"a1",obj:{set:e}}});return i.tohex()},this.getEncodedHex=function(){return this.tohex()},void 0!=t&&this.setByParam(t)},Ti(Pr.asn1.cms.RevocationInfoChoices,Pr.asn1.ASN1Object),Pr.asn1.cms.RevocationInfoChoice=function(t){Pr.asn1.cms.RevocationInfoChoice.superclass.constructor.call(this),this.params=null,this.tohex=function(){var t=this.params;if(void 0!=t.crl&&"string"==typeof t.crl){var e=t.crl;return-1!=t.crl.indexOf("-----BEGIN")&&(e=ri(t.crl)),e}if(void 0!=t.ocsp){var r=Pr.asn1.ASN1Util.newObject({tag:{tagi:"a1",obj:new Pr.asn1.cms.OtherRevocationFormat(t)}});return r.tohex()}throw new Error("property crl or ocsp undefined")},this.getEncodedHex=function(){return this.tohex()},void 0!=t&&this.setByParam(t)},Ti(Pr.asn1.cms.RevocationInfoChoice,Pr.asn1.ASN1Object),Pr.asn1.cms.OtherRevocationFormat=function(t){Pr.asn1.cms.OtherRevocationFormat.superclass.constructor.call(this);var e=Error,r=Pr,i=r.asn1,n=i.ASN1Util.newObject,s=r.lang.String.isHex;this.params=null,this.tohex=function(){var t=this.params;if(void 0==t.ocsp)throw new e("property ocsp not specified");if(!s(t.ocsp)||!Br.isASN1HEX(t.ocsp))throw new e("ocsp value not ASN.1 hex string");var r=n({seq:[{oid:"*******.*******6.2"},{asn1:{tlv:t.ocsp}}]});return r.tohex()},this.getEncodedHex=function(){return this.tohex()},void 0!=t&&this.setByParam(t)},Ti(Pr.asn1.cms.OtherRevocationFormat,Pr.asn1.ASN1Object),Pr.asn1.cms.CMSUtil=new function(){},Pr.asn1.cms.CMSUtil.newSignedData=function(t){return new Pr.asn1.cms.SignedData(t)},Pr.asn1.cms.CMSUtil.verifySignedData=function(t){var e=Pr,r=e.asn1,i=r.cms,n=(i.SignerInfo,i.SignedData,i.SigningTime,i.SigningCertificate,i.SigningCertificateV2,r.cades),s=(n.SignaturePolicyIdentifier,e.lang.String.isHex),a=Br,o=a.getVbyList,h=a.getTLVbyList,u=a.getIdxbyList,c=a.getChildIdx,l=a.getTLV,f=a.oidname,g=e.crypto.Util.hashHex;void 0===t.cms&&s(t.cms);var p=t.cms,d=function(t,e){for(var r,i=3;i<6;i++)if(r=u(t,0,[1,0,i]),void 0!==r){var n=t.substr(r,2);"a0"===n&&(e.certsIdx=r),"a1"===n&&(e.revinfosIdx=r),"31"===n&&(e.signerinfosIdx=r)}},v=function(t,e){var r=e.signerinfosIdx;if(void 0!==r){var i=c(t,r);e.signerInfoIdxList=i;for(var n=0;n<i.length;n++){var s=i[n],a={idx:s};m(t,a),e.signerInfos.push(a)}}},m=function(t,e){var r=e.idx;e.signerid_issuer1=h(t,r,[1,0],"30"),e.signerid_serial1=o(t,r,[1,1],"02"),e.hashalg=f(o(t,r,[2,0],"06"));var i=u(t,r,[3],"a0");e.idxSignedAttrs=i,y(t,e,i);var n=c(t,r),s=n.length;if(s<6)throw"malformed SignerInfo";e.sigalg=f(o(t,r,[s-2,0],"06")),e.sigval=o(t,r,[s-1],"04")},y=function(t,e,r){var i=c(t,r);e.signedAttrIdxList=i;for(var n=0;n<i.length;n++){var s,a=i[n],h=o(t,a,[0],"06");"2a864886f70d010905"===h?(s=Wr(o(t,a,[1,0])),e.saSigningTime=s):"2a864886f70d010904"===h&&(s=o(t,a,[1,0],"04"),e.saMessageDigest=s)}},x=function(t,e){if("2a864886f70d010702"!==o(t,0,[0],"06"))return e;e.cmsType="signedData",e.econtent=o(t,0,[1,0,2,1,0]),d(t,e),e.signerInfos=[],v(t,e)},S=function(t,e){for(var r=e.parse.signerInfos,i=r.length,n=!0,s=0;s<i;s++){var a=r[s];E(t,e,a,s),a.isValid||(n=!1)}e.isValid=n},w=function(t,e,r,i){var n,s=e.parse.certsIdx;if(void 0===e.certs){n=[],e.certkeys=[];for(var a=c(t,s),o=0;o<a.length;o++){var h=l(t,a[o]),u=new ji;u.readCertHex(h),n[o]=u,e.certkeys[o]=u.getPublicKey()}e.certs=n}else n=e.certs;e.cccc=n.length,e.cccci=a.length;for(o=0;o<n.length;o++){var f=u.getIssuerHex(),g=u.getSerialNumberHex();r.signerid_issuer1===f&&r.signerid_serial1===g&&(r.certkey_idx=o)}},E=function(t,e,r,i){r.verifyDetail={};var n=r.verifyDetail,s=e.parse.econtent,a=r.hashalg,o=r.saMessageDigest;n.validMessageDigest=!1,g(s,a)===o&&(n.validMessageDigest=!0),w(t,e,r,i),n.validSignatureValue=!1;var h=r.sigalg,u="31"+l(t,r.idxSignedAttrs).substr(2);r.signedattrshex=u;var c=e.certs[r.certkey_idx].getPublicKey(),f=new Pr.crypto.Signature({alg:h});f.init(c),f.updateHex(u);var p=f.verify(r.sigval);n.validSignatureValue_isValid=p,!0===p&&(n.validSignatureValue=!0),r.isValid=!1,n.validMessageDigest&&n.validSignatureValue&&(r.isValid=!0)},F={isValid:!1,parse:{}};return x(p,F.parse),S(p,F),F},Pr.asn1.cms.CMSParser=function(){var t=Error,e=ji,r=new e,i=Br,n=i.getV,s=i.getTLV,a=(i.getIdxbyList,i.getTLVbyList),o=i.getTLVbyListEx,h=i.getVbyList,u=i.getVbyListEx,c=i.getChildIdx;this.getCMSSignedData=function(t){var e=a(t,0,[1,0]),r=this.getSignedData(e);return r},this.getSignedData=function(t){var e=c(t,0),r={},i=n(t,e[0]),a=parseInt(i,16);r.version=a;var h=s(t,e[1]);r.hashalgs=this.getHashAlgArray(h);var u=s(t,e[2]);r.econtent=this.getEContent(u);var l=o(t,0,["[0]"]);null!=l&&(r.certs=this.getCertificateSet(l));o(t,0,["[1]"]);var f=o(t,0,[3]);return r.sinfos=this.getSignerInfos(f),r},this.getHashAlgArray=function(t){for(var r=c(t,0),i=new e,n=[],a=0;a<r.length;a++){var o=s(t,r[a]),h=i.getAlgorithmIdentifierName(o);n.push(h)}return n},this.getEContent=function(t){var e={},r=h(t,0,[0]),i=h(t,0,[1,0]);return e.type=Pr.asn1.x509.OID.oid2name(Br.hextooidstr(r)),e.content={hex:i},e},this.getSignerInfos=function(t){for(var e=[],r=c(t,0),i=0;i<r.length;i++){var n=s(t,r[i]),a=this.getSignerInfo(n);e.push(a)}return e},this.getSignerInfo=function(t){var e={},n=c(t,0),a=i.getInt(t,n[0],-1);-1!=a&&(e.version=a);var h=s(t,n[1]),l=this.getIssuerAndSerialNumber(h);e.id=l;var f=s(t,n[2]),g=r.getAlgorithmIdentifierName(f);e.hashalg=g;var p=o(t,0,["[0]"]);if(null!=p){var d=this.getAttributeList(p);e.sattrs=d}var v=o(t,0,[3]),m=r.getAlgorithmIdentifierName(v);e.sigalg=m;var y=u(t,0,[4]);e.sighex=y;var x=o(t,0,["[1]"]);if(null!=x){var S=this.getAttributeList(x);e.uattrs=S}return e},this.getSignerIdentifier=function(t){if("30"==t.substr(0,2))return this.getIssuerAndSerialNumber(t);throw new Error("SKID of signerIdentifier not supported")},this.getIssuerAndSerialNumber=function(t){var e={type:"isssn"},i=c(t,0),a=s(t,i[0]);e.issuer=r.getX500Name(a);var o=n(t,i[1]);return e.serial={hex:o},e},this.getAttributeList=function(t){for(var e=[],r=c(t,0),i=0;i<r.length;i++){var n=s(t,r[i]),a=this.getAttribute(n);e.push(a)}return{array:e}},this.getAttribute=function(t){var e={},r=c(t,0),n=i.getOID(t,r[0]),a=Pr.asn1.x509.OID.oid2name(n);e.attr=a;var o=s(t,r[1]),h=c(o,0);if(1==h.length)e.valhex=s(o,h[0]);else{for(var u=[],l=0;l<h.length;l++)u.push(s(o,h[l]));e.valhex=u}return"contentType"==a?this.setContentType(e):"messageDigest"==a?this.setMessageDigest(e):"signingTime"==a?this.setSigningTime(e):"signingCertificate"==a?this.setSigningCertificate(e):"signingCertificateV2"==a?this.setSigningCertificateV2(e):"signaturePolicyIdentifier"==a&&this.setSignaturePolicyIdentifier(e),e},this.setContentType=function(t){var e=i.getOIDName(t.valhex,0,null);null!=e&&(t.type=e,delete t.valhex)},this.setSigningTime=function(t){var e=n(t.valhex,0),r=Wr(e);t.str=r,delete t.valhex},this.setMessageDigest=function(t){var e=n(t.valhex,0);t.hex=e,delete t.valhex},this.setSigningCertificate=function(t){var e=c(t.valhex,0);if(e.length>0){for(var r=s(t.valhex,e[0]),i=c(r,0),n=[],a=0;a<i.length;a++){var o=s(r,i[a]),h=this.getESSCertID(o);n.push(h)}t.array=n}if(e.length>1){var u=s(t.valhex,e[1]);t.polhex=u}delete t.valhex},this.setSignaturePolicyIdentifier=function(t){var r=c(t.valhex,0);if(r.length>0){var a=i.getOID(t.valhex,r[0]);t.oid=a}if(r.length>1){var o=new e,h=c(t.valhex,r[1]),u=s(t.valhex,h[0]),l=o.getAlgorithmIdentifierName(u);t.alg=l;var f=n(t.valhex,h[1]);t.hash=f}delete t.valhex},this.setSigningCertificateV2=function(t){var e=c(t.valhex,0);if(e.length>0){for(var r=s(t.valhex,e[0]),i=c(r,0),n=[],a=0;a<i.length;a++){var o=s(r,i[a]),h=this.getESSCertIDv2(o);n.push(h)}t.array=n}if(e.length>1){var u=s(t.valhex,e[1]);t.polhex=u}delete t.valhex},this.getESSCertID=function(t){var e={},r=c(t,0);if(r.length>0){var i=n(t,r[0]);e.hash=i}if(r.length>1){var a=s(t,r[1]),o=this.getIssuerSerial(a);void 0!=o.serial&&(e.serial=o.serial),void 0!=o.issuer&&(e.issuer=o.issuer)}return e},this.getESSCertIDv2=function(e){var i={},a=c(e,0);if(a.length<1||3<a.length)throw new t("wrong number of elements");var o=0;if("30"==e.substr(a[0],2)){var h=s(e,a[0]);i.alg=r.getAlgorithmIdentifierName(h),o++}else i.alg="sha256";var u=n(e,a[o]);if(i.hash=u,a.length>o+1){var l=s(e,a[o+1]),f=this.getIssuerSerial(l);i.issuer=f.issuer,i.serial=f.serial}return i},this.getIssuerSerial=function(t){var e={},i=c(t,0),a=s(t,i[0]),o=r.getGeneralNames(a),h=o[0].dn;e.issuer=h;var u=n(t,i[1]);return e.serial={hex:u},e},this.getCertificateSet=function(t){for(var e=c(t,0),r=[],i=0;i<e.length;i++){var n=s(t,e[i]);if("30"==n.substr(0,2)){var a=ei(n,"CERTIFICATE");r.push(a)}}return{array:r,sortflag:!1}}},"undefined"!=typeof Pr&&Pr||(Pr={}),"undefined"!=typeof Pr.asn1&&Pr.asn1||(Pr.asn1={}),"undefined"!=typeof Pr.asn1.tsp&&Pr.asn1.tsp||(Pr.asn1.tsp={}),Pr.asn1.tsp.TimeStampToken=function(t){var e=Pr,r=e.asn1,i=r.tsp;i.TimeStampToken.superclass.constructor.call(this),this.params=null,this.getEncodedHexPrepare=function(){var t=new i.TSTInfo(this.params.econtent.content);this.params.econtent.content.hex=t.tohex()},void 0!=t&&this.setByParam(t)},Ti(Pr.asn1.tsp.TimeStampToken,Pr.asn1.cms.SignedData),Pr.asn1.tsp.TSTInfo=function(t){Error;var e=Pr,r=e.asn1,i=r.DERSequence,n=r.DERInteger,s=r.DERBoolean,a=r.DERGeneralizedTime,o=r.DERObjectIdentifier,h=r.DERTaggedObject,u=r.tsp,c=u.MessageImprint,l=u.Accuracy,f=(r.x509.X500Name,r.x509.GeneralName);if(u.TSTInfo.superclass.constructor.call(this),this.dVersion=new n({int:1}),this.dPolicy=null,this.dMessageImprint=null,this.dSerial=null,this.dGenTime=null,this.dAccuracy=null,this.dOrdering=null,this.dNonce=null,this.dTsa=null,this.tohex=function(){var t=[this.dVersion];if(null==this.dPolicy)throw new Error("policy shall be specified.");if(t.push(this.dPolicy),null==this.dMessageImprint)throw new Error("messageImprint shall be specified.");if(t.push(this.dMessageImprint),null==this.dSerial)throw new Error("serialNumber shall be specified.");if(t.push(this.dSerial),null==this.dGenTime)throw new Error("genTime shall be specified.");t.push(this.dGenTime),null!=this.dAccuracy&&t.push(this.dAccuracy),null!=this.dOrdering&&t.push(this.dOrdering),null!=this.dNonce&&t.push(this.dNonce),null!=this.dTsa&&t.push(this.dTsa);var e=new i({array:t});return this.hTLV=e.tohex(),this.hTLV},this.getEncodedHex=function(){return this.tohex()},void 0!==t){if("string"==typeof t.policy){if(!t.policy.match(/^[0-9.]+$/))throw"policy shall be oid like 0.1.4.134";this.dPolicy=new o({oid:t.policy})}void 0!==t.messageImprint&&(this.dMessageImprint=new c(t.messageImprint)),void 0!==t.serial&&(this.dSerial=new n(t.serial)),void 0!==t.genTime&&(this.dGenTime=new a(t.genTime)),void 0!==t.accuracy&&(this.dAccuracy=new l(t.accuracy)),void 0!==t.ordering&&1==t.ordering&&(this.dOrdering=new s),void 0!==t.nonce&&(this.dNonce=new n(t.nonce)),void 0!==t.tsa&&(this.dTsa=new h({tag:"a0",explicit:!0,obj:new f({dn:t.tsa})}))}},Ti(Pr.asn1.tsp.TSTInfo,Pr.asn1.ASN1Object),Pr.asn1.tsp.Accuracy=function(t){var e=Pr,r=e.asn1,i=r.ASN1Util.newObject;r.tsp.Accuracy.superclass.constructor.call(this),this.params=null,this.tohex=function(){var t=this.params,e=[];return void 0!=t.seconds&&"number"==typeof t.seconds&&e.push({int:t.seconds}),void 0!=t.millis&&"number"==typeof t.millis&&e.push({tag:{tagi:"80",obj:{int:t.millis}}}),void 0!=t.micros&&"number"==typeof t.micros&&e.push({tag:{tagi:"81",obj:{int:t.micros}}}),i({seq:e}).tohex()},this.getEncodedHex=function(){return this.tohex()},void 0!=t&&this.setByParam(t)},Ti(Pr.asn1.tsp.Accuracy,Pr.asn1.ASN1Object),Pr.asn1.tsp.MessageImprint=function(t){var e=Pr,r=e.asn1,i=r.DERSequence,n=r.DEROctetString,s=r.x509,a=s.AlgorithmIdentifier;r.tsp.MessageImprint.superclass.constructor.call(this),this.params=null,this.tohex=function(){var t=this.params,e=new a({name:t.alg}),r=new n({hex:t.hash}),s=new i({array:[e,r]});return s.tohex()},this.getEncodedHex=function(){return this.tohex()},void 0!==t&&this.setByParam(t)},Ti(Pr.asn1.tsp.MessageImprint,Pr.asn1.ASN1Object),Pr.asn1.tsp.TimeStampReq=function(t){var e=Pr,r=e.asn1,i=r.DERSequence,n=r.DERInteger,s=r.DERBoolean,a=(r.ASN1Object,r.DERObjectIdentifier),o=r.tsp,h=o.MessageImprint;o.TimeStampReq.superclass.constructor.call(this),this.params=null,this.tohex=function(){var t=this.params,e=[];e.push(new n({int:1})),t.messageImprint instanceof Pr.asn1.ASN1Object?e.push(t.messageImprint):e.push(new h(t.messageImprint)),void 0!=t.policy&&e.push(new a(t.policy)),void 0!=t.nonce&&e.push(new n(t.nonce)),1==t.certreq&&e.push(new s);var r=new i({array:e});return r.tohex()},this.getEncodedHex=function(){return this.tohex()},void 0!=t&&this.setByParam(t)},Ti(Pr.asn1.tsp.TimeStampReq,Pr.asn1.ASN1Object),Pr.asn1.tsp.TimeStampResp=function(t){var e=Pr,r=e.asn1,i=r.DERSequence,n=(r.ASN1Object,r.tsp),s=n.PKIStatusInfo;n.TimeStampResp.superclass.constructor.call(this),this.params=null,this.tohex=function(){var t=this.params,e=[];if(void 0!=t.econtent||void 0!=t.tst)if(void 0!=t.statusinfo?e.push(new s(t.statusinfo)):e.push(new s("granted")),void 0!=t.econtent)e.push(new n.TimeStampToken(t).getContentInfo());else{if(!(t.tst instanceof r.ASN1Object))throw new Error("improper member tst value");e.push(t.tst)}else{if(void 0==t.statusinfo)throw new Error("parameter for token nor statusinfo not specified");e.push(new s(t.statusinfo))}var a=new i({array:e});return a.tohex()},this.getEncodedHex=function(){return this.tohex()},void 0!=t&&this.setByParam(t)},Ti(Pr.asn1.tsp.TimeStampResp,Pr.asn1.ASN1Object),Pr.asn1.tsp.PKIStatusInfo=function(t){var e=Error,r=Pr,i=r.asn1,n=i.DERSequence,s=i.tsp,a=s.PKIStatus,o=s.PKIFreeText,h=s.PKIFailureInfo;s.PKIStatusInfo.superclass.constructor.call(this),this.params=null,this.tohex=function(){var t=this.params,r=[];if("string"==typeof t)r.push(new a(t));else{if(void 0==t.status)throw new e("property 'status' unspecified");r.push(new a(t.status)),void 0!=t.statusstr&&r.push(new o(t.statusstr)),void 0!=t.failinfo&&r.push(new h(t.failinfo))}var i=new n({array:r});return i.tohex()},this.getEncodedHex=function(){return this.tohex()},void 0!=t&&this.setByParam(t)},Ti(Pr.asn1.tsp.PKIStatusInfo,Pr.asn1.ASN1Object),Pr.asn1.tsp.PKIStatus=function(t){var e=Error,r=Pr,i=r.asn1,n=i.DERInteger,s=i.tsp;s.PKIStatus.superclass.constructor.call(this);var a={granted:0,grantedWithMods:1,rejection:2,waiting:3,revocationWarning:4,revocationNotification:5};this.params=null,this.tohex=function(){var t,r=this.params;if("string"==typeof r)try{t=a[r]}catch(i){throw new e("undefined name: "+r)}else{if("number"!=typeof r)throw new e("unsupported params");t=r}return new n({int:t}).tohex()},this.getEncodedHex=function(){return this.tohex()},void 0!=t&&this.setByParam(t)},Ti(Pr.asn1.tsp.PKIStatus,Pr.asn1.ASN1Object),Pr.asn1.tsp.PKIFreeText=function(t){var e=Error,r=Pr,i=r.asn1,n=i.DERSequence,s=i.DERUTF8String,a=i.tsp;a.PKIFreeText.superclass.constructor.call(this),this.params=null,this.tohex=function(){var t=this.params;if(!t instanceof Array)throw new e("wrong params: not array");for(var r=[],i=0;i<t.length;i++)r.push(new s({str:t[i]}));var a=new n({array:r});return a.tohex()},this.getEncodedHex=function(){return this.tohex()},void 0!=t&&this.setByParam(t)},Ti(Pr.asn1.tsp.PKIFreeText,Pr.asn1.ASN1Object),Pr.asn1.tsp.PKIFailureInfo=function(t){var e=Error,r=Pr,i=r.asn1,n=i.DERBitString,s=i.tsp,a=s.PKIFailureInfo,o={badAlg:0,badRequest:2,badDataFormat:5,timeNotAvailable:14,unacceptedPolicy:15,unacceptedExtension:16,addInfoNotAvailable:17,systemFailure:25};a.superclass.constructor.call(this),this.params=null,this.tohex=function(){var t,r=this.params;if("string"==typeof r)try{t=o[r]}catch(i){throw new e("undefined name: "+r)}else{if("number"!=typeof r)throw new e("wrong params");t=r}return new n({bin:t.toString(2)}).tohex()},this.getEncodedHex=function(){return this.tohex()},void 0!=t&&this.setByParam(t)},Ti(Pr.asn1.tsp.PKIFailureInfo,Pr.asn1.ASN1Object),Pr.asn1.tsp.AbstractTSAAdapter=function(t){this.getTSTHex=function(t,e){throw"not implemented yet"}},Pr.asn1.tsp.SimpleTSAAdapter=function(t){var e=Pr,r=e.asn1,i=r.tsp,n=e.crypto.Util.hashHex;i.SimpleTSAAdapter.superclass.constructor.call(this),this.params=null,this.serial=0,this.getTSTHex=function(t,e){var r=n(t,e);this.params.econtent.content.messageImprint={alg:e,hash:r},this.params.econtent.content.serial={int:this.serial++};var s=Math.floor(1e9*Math.random());this.params.econtent.content.nonce={int:s};var a=new i.TimeStampToken(this.params);return a.getContentInfoEncodedHex()},void 0!==t&&(this.params=t)},Ti(Pr.asn1.tsp.SimpleTSAAdapter,Pr.asn1.tsp.AbstractTSAAdapter),Pr.asn1.tsp.FixedTSAAdapter=function(t){var e=Pr,r=e.asn1,i=r.tsp,n=e.crypto.Util.hashHex;i.FixedTSAAdapter.superclass.constructor.call(this),this.params=null,this.getTSTHex=function(t,e){var r=n(t,e);this.params.econtent.content.messageImprint={alg:e,hash:r};var s=new i.TimeStampToken(this.params);return s.getContentInfoEncodedHex()},void 0!==t&&(this.params=t)},Ti(Pr.asn1.tsp.FixedTSAAdapter,Pr.asn1.tsp.AbstractTSAAdapter),Pr.asn1.tsp.TSPUtil=new function(){},Pr.asn1.tsp.TSPUtil.newTimeStampToken=function(t){return new Pr.asn1.tsp.TimeStampToken(t)},Pr.asn1.tsp.TSPUtil.parseTimeStampReq=function(t){var e=new Pr.asn1.tsp.TSPParser;return e.getTimeStampReq(t)},Pr.asn1.tsp.TSPUtil.parseMessageImprint=function(t){var e=new Pr.asn1.tsp.TSPParser;return e.getMessageImprint(t)},Pr.asn1.tsp.TSPParser=function(){Error;var t=ji,e=new t,r=Br,i=r.getV,n=r.getTLV,s=r.getIdxbyList,a=(r.getTLVbyListEx,r.getChildIdx),o=["granted","grantedWithMods","rejection","waiting","revocationWarning","revocationNotification"],h={0:"badAlg",2:"badRequest",5:"badDataFormat",14:"timeNotAvailable",15:"unacceptedPolicy",16:"unacceptedExtension",17:"addInfoNotAvailable",25:"systemFailure"};this.getResponse=function(t){var e=a(t,0);if(1==e.length)return this.getPKIStatusInfo(n(t,e[0]));if(e.length>1){var r=this.getPKIStatusInfo(n(t,e[0])),i=n(t,e[1]),s=this.getToken(i);return s.statusinfo=r,s}},this.getToken=function(t){var e=new Pr.asn1.cms.CMSParser,r=e.getCMSSignedData(t);return this.setTSTInfo(r),r},this.setTSTInfo=function(t){var e=t.econtent;if("tstinfo"==e.type){var r=e.content.hex,i=this.getTSTInfo(r);e.content=i}},this.getTSTInfo=function(t){var r={},s=a(t,0),o=i(t,s[1]);r.policy=Di(o);var h=n(t,s[2]);r.messageImprint=this.getMessageImprint(h);var u=i(t,s[3]);r.serial={hex:u};var c=i(t,s[4]);r.genTime={str:Wr(c)};var l=0;if(s.length>5&&"30"==t.substr(s[5],2)){var f=n(t,s[5]);r.accuracy=this.getAccuracy(f),l++}if(s.length>5+l&&"01"==t.substr(s[5+l],2)){var g=i(t,s[5+l]);"ff"==g&&(r.ordering=!0),l++}if(s.length>5+l&&"02"==t.substr(s[5+l],2)){var p=i(t,s[5+l]);r.nonce={hex:p},l++}if(s.length>5+l&&"a0"==t.substr(s[5+l],2)){var d=n(t,s[5+l]);d="30"+d.substr(2),pGeneralNames=e.getGeneralNames(d);var v=pGeneralNames[0].dn;r.tsa=v,l++}if(s.length>5+l&&"a1"==t.substr(s[5+l],2)){var m=n(t,s[5+l]);m="30"+m.substr(2);var y=e.getExtParamArray(m);r.ext=y,l++}return r},this.getAccuracy=function(t){for(var e={},r=a(t,0),n=0;n<r.length;n++){var s=t.substr(r[n],2),o=i(t,r[n]),h=parseInt(o,16);"02"==s?e.seconds=h:"80"==s?e.millis=h:"81"==s&&(e.micros=h)}return e},this.getMessageImprint=function(t){if("30"!=t.substr(0,2))throw new Error("head of messageImprint hex shall be x30");var e={},n=(a(t,0),s(t,0,[0,0])),o=i(t,n),h=r.hextooidstr(o),u=Pr.asn1.x509.OID.oid2name(h);if(""==u)throw new Error("hashAlg name undefined: "+h);var c=u,l=s(t,0,[1]);return e.alg=c,e.hash=i(t,l),e},this.getPKIStatusInfo=function(t){var e={},r=a(t,0),s=0;try{var h=i(t,r[0]),u=parseInt(h,16);e.status=o[u]}catch(f){}if(r.length>1&&"30"==t.substr(r[1],2)){var c=n(t,r[1]);e.statusstr=this.getPKIFreeText(c),s++}if(r.length>s&&"03"==t.substr(r[1+s],2)){var l=n(t,r[1+s]);e.failinfo=this.getPKIFailureInfo(l)}return e},this.getPKIFreeText=function(t){for(var e=[],i=a(t,0),n=0;n<i.length;n++)e.push(r.getString(t,i[n]));return e},this.getPKIFailureInfo=function(t){var e=r.getInt(t,0);return void 0!=h[e]?h[e]:e},this.getTimeStampReq=function(t){var e={certreq:!1},s=a(t,0);if(s.length<2)throw new Error("TimeStampReq must have at least 2 items");var o=n(t,s[1]);e.messageImprint=Pr.asn1.tsp.TSPUtil.parseMessageImprint(o);for(var h=2;h<s.length;h++){var u=s[h],c=t.substr(u,2);if("06"==c){var l=i(t,u);e.policy=r.hextooidstr(l)}"02"==c&&(e.nonce=i(t,u)),"01"==c&&(e.certreq=!0)}return e}},"undefined"!=typeof Pr&&Pr||(Pr={}),"undefined"!=typeof Pr.asn1&&Pr.asn1||(Pr.asn1={}),"undefined"!=typeof Pr.asn1.cades&&Pr.asn1.cades||(Pr.asn1.cades={}),Pr.asn1.cades.SignaturePolicyIdentifier=function(t){var e=Pr,r=e.asn1,i=r.cades,n=i.SignaturePolicyId;i.SignaturePolicyIdentifier.superclass.constructor.call(this),this.typeOid="1.2.840.113549.********.15",this.params=null,this.getValueArray=function(){return[new n(this.params)]},this.setByParam=function(t){this.params=t},void 0!=t&&this.setByParam(t)},Ti(Pr.asn1.cades.SignaturePolicyIdentifier,Pr.asn1.cms.Attribute),Pr.asn1.cades.SignaturePolicyId=function(t){var e=Pr,r=e.asn1,i=r.DERSequence,n=r.DERObjectIdentifier,s=r.x509,a=(s.AlgorithmIdentifier,r.cades),o=a.SignaturePolicyId,h=a.OtherHashAlgAndValue;o.superclass.constructor.call(this),this.params=null,this.tohex=function(){var t=this.params,e=[];e.push(new n(t.oid)),e.push(new h(t));var r=new i({array:e});return r.tohex()},this.getEncodedHex=function(){return this.tohex()},this.setByParam=function(t){this.params=t},void 0!=t&&this.setByParam(t)},Ti(Pr.asn1.cades.SignaturePolicyId,Pr.asn1.ASN1Object),Pr.asn1.cades.OtherHashAlgAndValue=function(t){var e=Error,r=Pr,i=r.asn1,n=i.DERSequence,s=i.DEROctetString,a=i.x509,o=a.AlgorithmIdentifier,h=i.cades,u=h.OtherHashAlgAndValue;u.superclass.constructor.call(this),this.params=null,this.tohex=function(){var t=this.params;if(void 0==t.alg)throw new e("property 'alg' not specified");if(void 0==t.hash&&void 0==t.cert)throw new e("property 'hash' nor 'cert' not specified");var r=null;if(void 0!=t.hash)r=t.hash;else if(void 0!=t.cert){if("string"!=typeof t.cert)throw new e("cert not string");var i=t.cert;-1!=t.cert.indexOf("-----BEGIN")&&(i=ri(t.cert)),r=Pr.crypto.Util.hashHex(i,t.alg)}var a=[];a.push(new o({name:t.alg})),a.push(new s({hex:r}));var h=new n({array:a});return h.tohex()},this.getEncodedHex=function(){return this.tohex()},void 0!=t&&this.setByParam(t)},Ti(Pr.asn1.cades.OtherHashAlgAndValue,Pr.asn1.ASN1Object),Pr.asn1.cades.OtherHashValue=function(t){Pr.asn1.cades.OtherHashValue.superclass.constructor.call(this);var e=Error,r=Pr,i=(r.lang.String.isHex,r.asn1),n=i.DEROctetString;r.crypto.Util.hashHex;this.params=null,this.tohex=function(){var t=this.params;if(void 0==t.hash&&void 0==t.cert)throw new e("hash or cert not specified");var r=null;if(void 0!=t.hash)r=t.hash;else if(void 0!=t.cert){if("string"!=typeof t.cert)throw new e("cert not string");var i=t.cert;-1!=t.cert.indexOf("-----BEGIN")&&(i=ri(t.cert)),r=Pr.crypto.Util.hashHex(i,"sha1")}return new n({hex:r}).tohex()},this.getEncodedHex=function(){return this.tohex()},void 0!=t&&this.setByParam(t)},Ti(Pr.asn1.cades.OtherHashValue,Pr.asn1.ASN1Object),Pr.asn1.cades.SignatureTimeStamp=function(t){var e=Error,r=Pr,i=r.lang.String.isHex,n=r.asn1,s=n.ASN1Object,a=(n.x509,n.cades);a.SignatureTimeStamp.superclass.constructor.call(this),this.typeOid="1.2.840.113549.********.14",this.params=null,this.getValueArray=function(){var t=this.params;if(void 0!=t.tst){if(i(t.tst)){var r=new s;return r.hTLV=t.tst,[r]}if(t.tst instanceof s)return[t.tst];throw new e("params.tst has wrong value")}if(void 0!=t.res){var n=t.res;if(n instanceof s&&(n=n.tohex()),"string"!=typeof n||!i(n))throw new e("params.res has wrong value");Br.getTLVbyList(n,0,[1]),r=new s;return r.hTLV=t.tst,[r]}},null!=t&&this.setByParam(t)},Ti(Pr.asn1.cades.SignatureTimeStamp,Pr.asn1.cms.Attribute),Pr.asn1.cades.CompleteCertificateRefs=function(t){var e=Error,r=Pr,i=r.asn1,n=i.DERSequence,s=i.cades,a=s.OtherCertID,o=r.lang.String.isHex;s.CompleteCertificateRefs.superclass.constructor.call(this),this.typeOid="1.2.840.113549.********.21",this.params=null,this.getValueArray=function(){for(var t=this.params,r=[],i=0;i<t.array.length;i++){var s=t.array[i];if("string"==typeof s)if(-1!=s.indexOf("-----BEGIN"))s={cert:s};else{if(!o(s))throw new e("unsupported value: "+s);s={hash:s}}void 0!=t.alg&&void 0==s.alg&&(s.alg=t.alg),void 0!=t.hasis&&void 0==s.hasis&&(s.hasis=t.hasis);var h=new a(s);r.push(h)}var u=new n({array:r});return[u]},void 0!=t&&this.setByParam(t)},Ti(Pr.asn1.cades.CompleteCertificateRefs,Pr.asn1.cms.Attribute),Pr.asn1.cades.OtherCertID=function(t){var e=Pr,r=e.asn1,i=r.DERSequence,n=r.cms,s=n.IssuerSerial,a=r.cades,o=a.OtherHashValue,h=a.OtherHashAlgAndValue;a.OtherCertID.superclass.constructor.call(this),this.params=t,this.tohex=function(){var t=this.params;"string"==typeof t&&(-1!=t.indexOf("-----BEGIN")?t={cert:t}:_isHex(t)&&(t={hash:t}));var e=[],r=null;if(r=void 0!=t.alg?new h(t):new o(t),e.push(r),void 0!=t.cert&&1==t.hasis||void 0!=t.issuer&&void 0!=t.serial){var n=new s(t);e.push(n)}var a=new i({array:e});return a.tohex()},this.getEncodedHex=function(){return this.tohex()},void 0!=t&&this.setByParam(t)},Ti(Pr.asn1.cades.OtherCertID,Pr.asn1.ASN1Object),Pr.asn1.cades.OtherHash=function(t){Error;var e=Pr,r=e.asn1,i=(r.cms,r.cades),n=i.OtherHashAlgAndValue,s=i.OtherHashValue,a=(e.crypto.Util.hashHex,e.lang.String.isHex);i.OtherHash.superclass.constructor.call(this),this.params=null,this.tohex=function(){var t=this.params;"string"==typeof t&&(-1!=t.indexOf("-----BEGIN")?t={cert:t}:a(t)&&(t={hash:t}));var e=null;return e=void 0!=t.alg?new n(t):new s(t),e.tohex()},this.getEncodedHex=function(){return this.tohex()},void 0!=t&&this.setByParam(t)},Ti(Pr.asn1.cades.OtherHash,Pr.asn1.ASN1Object),Pr.asn1.cades.CAdESUtil=new function(){},Pr.asn1.cades.CAdESUtil.parseSignedDataForAddingUnsigned=function(t){var e=new Pr.asn1.cms.CMSParser,r=e.getCMSSignedData(t);return r},Pr.asn1.cades.CAdESUtil.parseSignerInfoForAddingUnsigned=function(t,e,r){var i=Br,n=i.getChildIdx,s=i.getTLV,a=i.getV,o=Pr,h=o.asn1,u=h.ASN1Object,c=h.cms,l=c.AttributeList,f=c.SignerInfo,g={},p=n(t,e);if(6!=p.length)throw"not supported items for SignerInfo (!=6)";var d=p.shift();g.version=s(t,d);var v=p.shift();g.si=s(t,v);var m=p.shift();g.digalg=s(t,m);var y=p.shift();g.sattrs=s(t,y);var x=p.shift();g.sigalg=s(t,x);var S=p.shift();g.sig=s(t,S),g.sigval=a(t,S);var w=null;return g.obj=new f,w=new u,w.hTLV=g.version,g.obj.dCMSVersion=w,w=new u,w.hTLV=g.si,g.obj.dSignerIdentifier=w,w=new u,w.hTLV=g.digalg,g.obj.dDigestAlgorithm=w,w=new u,w.hTLV=g.sattrs,g.obj.dSignedAttrs=w,w=new u,w.hTLV=g.sigalg,g.obj.dSigAlg=w,w=new u,w.hTLV=g.sig,g.obj.dSig=w,g.obj.dUnsignedAttrs=new l,g},"undefined"!=typeof Pr.asn1.csr&&Pr.asn1.csr||(Pr.asn1.csr={}),Pr.asn1.csr.CertificationRequest=function(t){var e=Pr,r=e.asn1,i=r.DERBitString,n=r.DERSequence,s=r.csr,a=(r.x509,s.CertificationRequestInfo);s.CertificationRequest.superclass.constructor.call(this),this.setByParam=function(t){this.params=t},this.sign=function(){var t=new a(this.params).tohex(),e=new Pr.crypto.Signature({alg:this.params.sigalg});e.init(this.params.sbjprvkey),e.updateHex(t);var r=e.sign();this.params.sighex=r},this.getPEM=function(){return ei(this.tohex(),"CERTIFICATE REQUEST")},this.tohex=function(){var t=this.params,e=new Pr.asn1.csr.CertificationRequestInfo(this.params),r=new Pr.asn1.x509.AlgorithmIdentifier({name:t.sigalg});if(void 0==t.sighex&&void 0!=t.sbjprvkey&&this.sign(),void 0==t.sighex)throw new Error("sighex or sbjprvkey parameter not defined");var s=new i({hex:"00"+t.sighex}),a=new n({array:[e,r,s]});return a.tohex()},this.getEncodedHex=function(){return this.tohex()},void 0!==t&&this.setByParam(t)},Ti(Pr.asn1.csr.CertificationRequest,Pr.asn1.ASN1Object),Pr.asn1.csr.CertificationRequestInfo=function(t){var e=Pr,r=e.asn1,i=(r.DERBitString,r.DERSequence),n=r.DERInteger,s=r.DERUTF8String,a=r.DERTaggedObject,o=r.ASN1Util.newObject,h=r.csr,u=r.x509,c=u.X500Name,l=u.Extensions,f=u.SubjectPublicKeyInfo;h.CertificationRequestInfo.superclass.constructor.call(this),this.params=null,this.setByParam=function(t){void 0!=t&&(this.params=t)},this.tohex=function(){var t=this.params,e=[];if(e.push(new n({int:0})),e.push(new c(t.subject)),e.push(new f(Bi.getKey(t.sbjpubkey))),void 0!=t.extreq){var r=new l(t.extreq),h=o({tag:{tag:"a0",explict:!0,obj:{seq:[{oid:"1.2.840.113549.1.9.14"},{set:[r]}]}}});e.push(h)}else e.push(new a({tag:"a0",explicit:!1,obj:new s({str:""})}));var u=new i({array:e});return u.tohex()},this.getEncodedHex=function(){return this.tohex()},void 0!=t&&this.setByParam(t)},Ti(Pr.asn1.csr.CertificationRequestInfo,Pr.asn1.ASN1Object),Pr.asn1.csr.CSRUtil=new function(){},Pr.asn1.csr.CSRUtil.newCSRPEM=function(t){var e=Pr.asn1.csr,r=new e.CertificationRequest(t),i=r.getPEM();return i},Pr.asn1.csr.CSRUtil.getParam=function(t){var e=Br,r=e.getV,i=e.getIdxbyList,n=e.getTLVbyList,s=e.getTLVbyListEx,a=e.getVbyListEx,o=function(t){var e=i(t,0,[0,3,0,0],"06");return"2a864886f70d01090e"!=r(t,e)?null:n(t,0,[0,3,0,1,0],"30")},h={};if(-1==t.indexOf("-----BEGIN CERTIFICATE REQUEST"))throw new Error("argument is not PEM file");var u=ri(t,"CERTIFICATE REQUEST");try{var c=s(u,0,[0,1]);if("3000"==c)h.subject={};else{var l=new ji;h.subject=l.getX500Name(c)}}catch(m){}var f=s(u,0,[0,2]),g=Bi.getKey(f,null,"pkcs8pub");h.sbjpubkey=Bi.getPEM(g,"PKCS8PUB");var p=o(u);l=new ji;null!=p&&(h.extreq=l.getExtParamArray(p));try{var d=s(u,0,[1],"30");l=new ji;h.sigalg=l.getAlgorithmIdentifierName(d)}catch(m){}try{var v=a(u,0,[2]);h.sighex=v}catch(m){}return h},"undefined"!=typeof Pr&&Pr||(Pr={}),"undefined"!=typeof Pr.asn1&&Pr.asn1||(Pr.asn1={}),"undefined"!=typeof Pr.asn1.ocsp&&Pr.asn1.ocsp||(Pr.asn1.ocsp={}),Pr.asn1.ocsp.DEFAULT_HASH="sha1",Pr.asn1.ocsp.OCSPResponse=function(t){Pr.asn1.ocsp.OCSPResponse.superclass.constructor.call(this);Pr.asn1.DEREnumerated;var e=Pr.asn1.ASN1Util.newObject,r=Pr.asn1.ocsp.ResponseBytes,i=["successful","malformedRequest","internalError","tryLater","_not_used_","sigRequired","unauthorized"];this.params=null,this._getStatusCode=function(){var t=this.params.resstatus;return"number"==typeof t?t:"string"!=typeof t?-1:i.indexOf(t)},this.setByParam=function(t){this.params=t},this.tohex=function(){var t=this.params,i=this._getStatusCode();if(-1==i)throw new Error("responseStatus not supported: "+t.resstatus);if(0!=i)return e({seq:[{enum:{int:i}}]}).tohex();var n=new r(t);return e({seq:[{enum:{int:0}},{tag:{tag:"a0",explicit:!0,obj:n}}]}).tohex()},this.getEncodedHex=function(){return this.tohex()},void 0!==t&&this.setByParam(t)},Ti(Pr.asn1.ocsp.OCSPResponse,Pr.asn1.ASN1Object),Pr.asn1.ocsp.ResponseBytes=function(t){Pr.asn1.ocsp.ResponseBytes.superclass.constructor.call(this);var e=Pr.asn1,r=e.DERSequence,i=e.DERObjectIdentifier,n=e.DEROctetString,s=e.ocsp.BasicOCSPResponse;this.params=null,this.setByParam=function(t){this.params=t},this.tohex=function(){var t=this.params;if("ocspBasic"!=t.restype)throw new Error("not supported responseType: "+t.restype);var e=new s(t),a=[];a.push(new i({name:"ocspBasic"})),a.push(new n({hex:e.tohex()}));var o=new r({array:a});return o.tohex()},this.getEncodedHex=function(){return this.tohex()},void 0!==t&&this.setByParam(t)},Ti(Pr.asn1.ocsp.ResponseBytes,Pr.asn1.ASN1Object),Pr.asn1.ocsp.BasicOCSPResponse=function(t){Pr.asn1.ocsp.BasicOCSPResponse.superclass.constructor.call(this);var e=Error,r=Pr.asn1,i=r.ASN1Object,n=r.DERSequence,s=(r.DERGeneralizedTime,r.DERTaggedObject),a=r.DERBitString,o=(r.x509.Extensions,r.x509.AlgorithmIdentifier),h=r.ocsp;h.ResponderID;_SingleResponseList=h.SingleResponseList,_ResponseData=h.ResponseData,this.params=null,this.setByParam=function(t){this.params=t},this.sign=function(){var t=this.params,e=t.tbsresp.tohex(),r=new Pr.crypto.Signature({alg:t.sigalg});r.init(t.reskey),r.updateHex(e),t.sighex=r.sign()},this.tohex=function(){var t=this.params;void 0==t.tbsresp&&(t.tbsresp=new _ResponseData(t)),void 0==t.sighex&&void 0!=t.reskey&&this.sign();var r=[];if(r.push(t.tbsresp),r.push(new o({name:t.sigalg})),r.push(new a({hex:"00"+t.sighex})),void 0!=t.certs&&void 0!=t.certs.length){for(var h=[],u=0;u<t.certs.length;u++){var c=t.certs[u],l=null;if(Br.isASN1HEX(c))l=c;else{if(!c.match(/-----BEGIN/))throw new e("certs["+u+"] not hex or PEM");l=ri(c)}h.push(new i({tlv:l}))}var f=new n({array:h});r.push(new s({tag:"a0",explicit:!0,obj:f}))}var g=new n({array:r});return g.tohex()},this.getEncodedHex=function(){return this.tohex()},void 0!==t&&this.setByParam(t)},Ti(Pr.asn1.ocsp.BasicOCSPResponse,Pr.asn1.ASN1Object),Pr.asn1.ocsp.ResponseData=function(t){Pr.asn1.ocsp.ResponseData.superclass.constructor.call(this);var e=Error,r=Pr.asn1,i=r.DERSequence,n=r.DERGeneralizedTime,s=r.DERTaggedObject,a=r.x509.Extensions,o=r.ocsp,h=o.ResponderID;_SingleResponseList=o.SingleResponseList,this.params=null,this.tohex=function(){var t=this.params;void 0!=t.respid&&new e("respid not specified"),void 0!=t.prodat&&new e("prodat not specified"),void 0!=t.array&&new e("array not specified");var r=[];if(r.push(new h(t.respid)),r.push(new n(t.prodat)),r.push(new _SingleResponseList(t.array)),void 0!=t.ext){var o=new a(t.ext);r.push(new s({tag:"a1",explicit:!0,obj:o}))}var u=new i({array:r});return u.tohex()},this.getEncodedHex=function(){return this.tohex()},this.setByParam=function(t){this.params=t},void 0!==t&&this.setByParam(t)},Ti(Pr.asn1.ocsp.ResponseData,Pr.asn1.ASN1Object),Pr.asn1.ocsp.ResponderID=function(t){Pr.asn1.ocsp.ResponderID.superclass.constructor.call(this);var e=Pr,r=e.asn1,n=r.ASN1Util.newObject,s=r.x509.X500Name,a=e.lang.String.isHex,o=Error;this.params=null,this.tohex=function(){var t=this.params;if(void 0!=t.key){var e=null;if("string"==typeof t.key){if(a(t.key)&&(e=t.key),t.key.match(/-----BEGIN CERTIFICATE/)){var r=new ji(t.key),h=r.getExtSubjectKeyIdentifier();null!=h&&(e=h.kid.hex)}}else if(t.key instanceof ji){h=t.key.getExtSubjectKeyIdentifier();null!=h&&(e=h.kid.hex)}if(null==e)throw new o("wrong key member value");var u=n({tag:{tag:"a2",explicit:!0,obj:{octstr:{hex:e}}}});return u.tohex()}if(void 0!=t.name){var c=null;if("string"==typeof t.name&&t.name.match(/-----BEGIN CERTIFICATE/)){r=new ji(t.name);c=r.getSubject()}else t.name instanceof ji?c=t.name.getSubject():"object"!=i(t.name)||void 0==t.name.array&&void 0==t.name.str||(c=t.name);if(null==c)throw new o("wrong name member value");u=n({tag:{tag:"a1",explicit:!0,obj:new s(c)}});return u.tohex()}throw new o("key or name not specified")},this.getEncodedHex=function(){return this.tohex()},this.setByParam=function(t){this.params=t},void 0!==t&&this.setByParam(t)},Ti(Pr.asn1.ocsp.ResponderID,Pr.asn1.ASN1Object),Pr.asn1.ocsp.SingleResponseList=function(t){Pr.asn1.ocsp.SingleResponseList.superclass.constructor.call(this);var e=Pr.asn1,r=e.DERSequence,n=e.ocsp.SingleResponse;this.params=null,this.tohex=function(){var t=this.params;if("object"!=i(t)||void 0==t.length)throw new Error("params not specified properly");for(var e=[],s=0;s<t.length;s++)e.push(new n(t[s]));var a=new r({array:e});return a.tohex()},this.getEncodedHex=function(){return this.tohex()},this.setByParam=function(t){this.params=t},void 0!==t&&this.setByParam(t)},Ti(Pr.asn1.ocsp.SingleResponseList,Pr.asn1.ASN1Object),Pr.asn1.ocsp.SingleResponse=function(t){var e=Error,r=Pr,i=r.asn1,n=i.DERSequence,s=i.DERGeneralizedTime,a=i.DERTaggedObject,o=i.ocsp,h=o.CertID,u=o.CertStatus,c=i.x509,l=c.Extensions;o.SingleResponse.superclass.constructor.call(this),this.params=null,this.tohex=function(){var t=this.params,r=[];if(void 0==t.certid)throw new e("certid unspecified");if(void 0==t.status)throw new e("status unspecified");if(void 0==t.thisupdate)throw new e("thisupdate unspecified");if(r.push(new h(t.certid)),r.push(new u(t.status)),r.push(new s(t.thisupdate)),void 0!=t.nextupdate){var i=new s(t.nextupdate);r.push(new a({tag:"a0",explicit:!0,obj:i}))}if(void 0!=t.ext){var o=new l(t.ext);r.push(new a({tag:"a1",explicit:!0,obj:o}))}var c=new n({array:r});return c.tohex()},this.getEncodedHex=function(){return this.tohex()},this.setByParam=function(t){this.params=t},void 0!==t&&this.setByParam(t)},Ti(Pr.asn1.ocsp.SingleResponse,Pr.asn1.ASN1Object),Pr.asn1.ocsp.CertID=function(t){var e=Pr,r=e.asn1,n=r.DEROctetString,s=r.DERInteger,a=r.DERSequence,o=r.x509,h=o.AlgorithmIdentifier,u=r.ocsp,c=(u.DEFAULT_HASH,e.crypto),l=c.Util.hashHex,f=ji,g=Br,p=g.getVbyList;u.CertID.superclass.constructor.call(this),this.DEFAULT_HASH="sha1",this.params=null,this.setByValue=function(t,e,r,i){void 0==i&&(i=this.DEFAULT_HASH),this.params={alg:i,issname:t,isskey:e,sbjsn:r}},this.setByCert=function(t,e,r){void 0==r&&(r=this.DEFAULT_HASH),this.params={alg:r,issuerCert:t,subjectCert:e}},this.getParamByCerts=function(t,e,r){void 0==r&&(r=this.DEFAULT_HASH);var i=new f(t),n=new f(e),s=l(i.getSubjectHex(),r),a=i.getPublicKeyHex(),o=l(p(a,0,[1],"03",!0),r),h=n.getSerialNumberHex(),u={alg:r,issname:s,isskey:o,sbjsn:h};return u},this.tohex=function(){if("object"!=i(this.params))throw new Error("params not set");var t,e,r,o,u=this.params;if(o=void 0==u.alg?this.DEFAULT_HASH:u.alg,void 0!=u.issuerCert&&void 0!=u.subjectCert){var c=this.getParamByCerts(u.issuerCert,u.subjectCert,o);t=c.issname,e=c.isskey,r=c.sbjsn}else{if(void 0==u.issname||void 0==u.isskey||void 0==u.sbjsn)throw new Error("required param members not defined");t=u.issname,e=u.isskey,r=u.sbjsn}var l=new h({name:o}),f=new n({hex:t}),g=new n({hex:e}),p=new s({hex:r}),d=new a({array:[l,f,g,p]});return this.hTLV=d.tohex(),this.hTLV},this.getEncodedHex=function(){return this.tohex()},void 0!==t&&this.setByParam(t)},Ti(Pr.asn1.ocsp.CertID,Pr.asn1.ASN1Object),Pr.asn1.ocsp.CertStatus=function(t){Pr.asn1.ocsp.CertStatus.superclass.constructor.call(this),this.params=null,this.tohex=function(){var t=this.params;if("good"==t.status)return"8000";if("unknown"==t.status)return"8200";if("revoked"==t.status){var e=[{gentime:{str:t.time}}];void 0!=t.reason&&e.push({tag:{tag:"a0",explicit:!0,obj:{enum:{int:t.reason}}}});var r={tag:"a1",explicit:!1,obj:{seq:e}};return Pr.asn1.ASN1Util.newObject({tag:r}).tohex()}throw new Error("bad status")},this.getEncodedHex=function(){return this.tohex()},this.setByParam=function(t){this.params=t},void 0!==t&&this.setByParam(t)},Ti(Pr.asn1.ocsp.CertStatus,Pr.asn1.ASN1Object),Pr.asn1.ocsp.Request=function(t){var e=Pr,r=e.asn1,i=r.DERSequence,n=r.ocsp;if(n.Request.superclass.constructor.call(this),this.dReqCert=null,this.dExt=null,this.tohex=function(){var t=[];if(null===this.dReqCert)throw"reqCert not set";t.push(this.dReqCert);var e=new i({array:t});return this.hTLV=e.tohex(),this.hTLV},this.getEncodedHex=function(){return this.tohex()},"undefined"!==typeof t){var s=new n.CertID(t);this.dReqCert=s}},Ti(Pr.asn1.ocsp.Request,Pr.asn1.ASN1Object),Pr.asn1.ocsp.TBSRequest=function(t){var e=Pr,r=e.asn1,i=r.DERSequence,n=r.ocsp;n.TBSRequest.superclass.constructor.call(this),this.version=0,this.dRequestorName=null,this.dRequestList=[],this.dRequestExt=null,this.setRequestListByParam=function(t){for(var e=[],r=0;r<t.length;r++){var i=new n.Request(t[0]);e.push(i)}this.dRequestList=e},this.tohex=function(){var t=[];if(0!==this.version)throw"not supported version: "+this.version;if(null!==this.dRequestorName)throw"requestorName not supported";var e=new i({array:this.dRequestList});if(t.push(e),null!==this.dRequestExt)throw"requestExtensions not supported";var r=new i({array:t});return this.hTLV=r.tohex(),this.hTLV},this.getEncodedHex=function(){return this.tohex()},void 0!==t&&void 0!==t.reqList&&this.setRequestListByParam(t.reqList)},Ti(Pr.asn1.ocsp.TBSRequest,Pr.asn1.ASN1Object),Pr.asn1.ocsp.OCSPRequest=function(t){var e=Pr,r=e.asn1,i=r.DERSequence,n=r.ocsp;if(n.OCSPRequest.superclass.constructor.call(this),this.dTbsRequest=null,this.dOptionalSignature=null,this.tohex=function(){var t=[];if(null===this.dTbsRequest)throw"tbsRequest not set";if(t.push(this.dTbsRequest),null!==this.dOptionalSignature)throw"optionalSignature not supported";var e=new i({array:t});return this.hTLV=e.tohex(),this.hTLV},this.getEncodedHex=function(){return this.tohex()},void 0!==t&&void 0!==t.reqList){var s=new n.TBSRequest(t);this.dTbsRequest=s}},Ti(Pr.asn1.ocsp.OCSPRequest,Pr.asn1.ASN1Object),Pr.asn1.ocsp.OCSPUtil={},Pr.asn1.ocsp.OCSPUtil.getRequestHex=function(t,e,r){var i=Pr,n=i.asn1,s=n.ocsp;void 0===r&&(r=s.DEFAULT_HASH);var a={alg:r,issuerCert:t,subjectCert:e},o=new s.OCSPRequest({reqList:[a]});return o.tohex()},Pr.asn1.ocsp.OCSPUtil.getOCSPResponseInfo=function(t){var e=Br,r=e.getVbyList,i=e.getVbyListEx,n=e.getIdxbyList,s=(e.getIdxbyListEx,e.getV),a={};try{var o=i(t,0,[0],"0a");a.responseStatus=parseInt(o,16)}catch(l){}if(0!==a.responseStatus)return a;try{var h=n(t,0,[1,0,1,0,0,2,0,1]);"80"===t.substr(h,2)?a.certStatus="good":"a1"===t.substr(h,2)?(a.certStatus="revoked",a.revocationTime=Wr(r(t,h,[0]))):"82"===t.substr(h,2)&&(a.certStatus="unknown")}catch(l){}try{var u=n(t,0,[1,0,1,0,0,2,0,2]);a.thisUpdate=Wr(s(t,u))}catch(l){}try{var c=n(t,0,[1,0,1,0,0,2,0,3]);"a0"===t.substr(c,2)&&(a.nextUpdate=Wr(r(t,c,[0])))}catch(l){}return a},Pr.asn1.ocsp.OCSPParser=function(){var t=Error,e=ji,r=new e,i=Br,n=i.getV,s=i.getTLV,a=i.getIdxbyList,o=i.getVbyList,h=i.getTLVbyList,u=i.getVbyListEx,c=i.getTLVbyListEx,l=i.getChildIdx;this.getOCSPRequest=function(e){var r=l(e,0);if(1!=r.length&&2!=r.length)throw new t("wrong number elements: "+r.length);var i=this.getTBSRequest(s(e,r[0]));return i},this.getTBSRequest=function(t){var e={},i=c(t,0,[0],"30");e.array=this.getRequestList(i);var n=c(t,0,["[2]",0],"30");return null!=n&&(e.ext=r.getExtParamArray(n)),e},this.getRequestList=function(t){for(var e=[],r=l(t,0),i=0;i<r.length;i++){t=s(t,r[i]);e.push(this.getRequest(t))}return e},this.getRequest=function(e){var i=l(e,0);if(1!=i.length&&2!=i.length)throw new t("wrong number elements: "+i.length);var n=this.getCertID(s(e,i[0]));if(2==i.length){var o=a(e,0,[1,0]);n.ext=r.getExtParamArray(s(e,o))}return n},this.getCertID=function(r){var i=l(r,0);if(4!=i.length)throw new t("wrong number elements: "+i.length);var a=new e,o={};return o.alg=a.getAlgorithmIdentifierName(s(r,i[0])),o.issname=n(r,i[1]),o.isskey=n(r,i[2]),o.sbjsn=n(r,i[3]),o},this.getOCSPResponse=function(t){var e,r=l(t,0),i=n(t,r[0]),s=parseInt(i);if(1==r.length)return{resstatus:s};var a=h(t,0,[1,0]);return e=this.getResponseBytes(a),e.resstatus=s,e},this.getResponseBytes=function(t){var e,r=l(t,0),i=h(t,0,[1,0]);e=this.getBasicOCSPResponse(i);var s=n(t,r[0]);return e.restype=Pr.asn1.x509.OID.oid2name(Di(s)),e},this.getBasicOCSPResponse=function(t){var e,r=l(t,0);e=this.getResponseData(s(t,r[0]));var i=new ji;e.alg=i.getAlgorithmIdentifierName(s(t,r[1]));var a=n(t,r[2]);e.sighex=a.substr(2);var o=u(t,0,["[0]"]);if(null!=o){for(var h=l(o,0),c=[],f=0;f<h.length;f++){var g=s(o,h[f]);c.push(g)}e.certs=c}return e},this.getResponseData=function(t){var e=l(t,0),r=e.length,i={},a=0;"a0"==t.substr(e[0],2)&&a++,i.respid=this.getResponderID(s(t,e[a++]));var o=n(t,e[a++]);if(i.prodat=Wr(o),i.array=this.getSingleResponseList(s(t,e[a++])),"a1"==t.substr(e[r-1],2)){var u=h(t,e[r-1],[0]),c=new ji;i.ext=c.getExtParamArray(u)}return i},this.getResponderID=function(t){var e={};if("a2"==t.substr(0,2)){var r=o(t,0,[0]);e.key=r}if("a1"==t.substr(0,2)){var i=h(t,0,[0]),n=new ji;e.name=n.getX500Name(i)}return e},this.getSingleResponseList=function(t){for(var e=l(t,0),r=[],i=0;i<e.length;i++){var n=this.getSingleResponse(s(t,e[i]));r.push(n)}return r},this.getSingleResponse=function(t){var e=l(t,0),r={},i=this.getCertID(s(t,e[0]));r.certid=i;var a=this.getCertStatus(s(t,e[1]));if(r.status=a,"18"==t.substr(e[2],2)){var u=n(t,e[2]);r.thisupdate=Wr(u)}for(var c=3;c<e.length;c++){if("a0"==t.substr(e[c],2)){var f=o(t,e[c],[0],"18");r.nextupdate=Wr(f)}if("a1"==t.substr(e[c],2)){var g=new ji,p=h(t,0,[c,0]);r.ext=g.getExtParamArray(p)}}return r},this.getCertStatus=function(t){var e={};if("8000"==t)return{status:"good"};if("8200"==t)return{status:"unknown"};if("a1"==t.substr(0,2)){e.status="revoked";var r=o(t,0,[0]),i=Wr(r);e.time=i}return e}},"undefined"!=typeof Pr&&Pr||(Pr={}),"undefined"!=typeof Pr.lang&&Pr.lang||(Pr.lang={}),Pr.lang.String=function(){},"function"===typeof Buffer?(Rr=function(t){return kr(Buffer.from(t,"utf8").toString("base64"))},Tr=function(t){return Buffer.from(qr(t),"base64").toString("utf8")}):(Rr=function(t){return Mr(ui(yi(t)))},Tr=function(t){return decodeURIComponent(ci(_r(t)))}),Pr.lang.String.isInteger=function(t){return!!t.match(/^[0-9]+$/)||!!t.match(/^-[0-9]+$/)},Pr.lang.String.isHex=function(t){return wi(t)},Pr.lang.String.isBase64=function(t){return t=t.replace(/\s+/g,""),!(!t.match(/^[0-9A-Za-z+\/]+={0,3}$/)||t.length%4!=0)},Pr.lang.String.isBase64URL=function(t){return!t.match(/[+/=]/)&&(t=qr(t),Pr.lang.String.isBase64(t))},Pr.lang.String.isIntegerArray=function(t){return t=t.replace(/\s+/g,""),!!t.match(/^\[[0-9,]+\]$/)},Pr.lang.String.isPrintable=function(t){return null!==t.match(/^[0-9A-Za-z '()+,-./:=?]*$/)},Pr.lang.String.isIA5=function(t){return null!==t.match(/^[\x20-\x21\x23-\x7f]*$/)},Pr.lang.String.isMail=function(t){return null!==t.match(/^[A-Za-z0-9]{1}[A-Za-z0-9_.-]*@{1}[A-Za-z0-9_.-]{1,}\.[A-Za-z0-9]{1,}$/)};var bi=function(t,e){var r=t.length;t.length>e.length&&(r=e.length);for(var i=0;i<r;i++)if(t.charCodeAt(i)!=e.charCodeAt(i))return i;return t.length!=e.length?r:-1};function Ai(t){var e=function(t){var e=t.toString(16);return 1==e.length&&(e="0"+e),e},r=function(t){var r="",i=parseInt(t,10),n=i.toString(2),s=7-n.length%7;7==s&&(s=0);for(var a="",o=0;o<s;o++)a+="0";n=a+n;for(o=0;o<n.length-1;o+=7){var h=n.substr(o,7);o!=n.length-7&&(h="1"+h),r+=e(parseInt(h,2))}return r};try{if(!t.match(/^[0-9.]+$/))return null;var i="",n=t.split("."),s=40*parseInt(n[0],10)+parseInt(n[1],10);i+=e(s),n.splice(0,2);for(var a=0;a<n.length;a++)i+=r(n[a]);return i}catch(o){return null}}function Di(t){if(!wi(t))return null;try{var e=[],r=t.substr(0,2),i=parseInt(r,16);e[0]=new String(Math.floor(i/40)),e[1]=new String(i%40);for(var n=t.substr(2),s=[],a=0;a<n.length/2;a++)s.push(parseInt(n.substr(2*a,2),16));var o=[],h="";for(a=0;a<s.length;a++)128&s[a]?h+=Ii((127&s[a]).toString(2),7):(h+=Ii((127&s[a]).toString(2),7),o.push(new String(parseInt(h,2))),h="");var u=e.join(".");return o.length>0&&(u=u+"."+o.join(".")),u}catch(c){return null}}var Ii=function(t,e,r){return void 0==r&&(r="0"),t.length>=e?t:new Array(e-t.length+1).join(r)+t};function Ci(t){if(t.length%2!=0)return-1;if(t=t.toLowerCase(),null==t.match(/^[0-9a-f]+$/))return-1;try{var e=t.substr(0,2);if("00"==e)return parseInt(t.substr(2),16);var r=parseInt(e,16);if(r>7)return-1;var i=t.substr(2),n=parseInt(i,16).toString(2);"0"==n&&(n="00000000"),n=n.slice(0,0-r);var s=parseInt(n,2);return NaN==s?-1:s}catch(a){return-1}}function Pi(t){if("number"!=typeof t)return null;if(t<0)return null;var e=Number(t).toString(2),r=8-e.length%8;8==r&&(r=0),e+=Ii("",r,"0");var i=parseInt(e,2).toString(16);i.length%2==1&&(i="0"+i);var n="0"+r;return n+i}function Ri(t){var e=Ci(t);return-1==e?null:e.toString(2)}function Ti(t,e){var r=function(){};r.prototype=e.prototype,t.prototype=new r,t.prototype.constructor=t,t.superclass=e.prototype,e.prototype.constructor==Object.prototype.constructor&&(e.prototype.constructor=e)}"undefined"!=typeof Pr&&Pr||(Pr={}),"undefined"!=typeof Pr.crypto&&Pr.crypto||(Pr.crypto={}),Pr.crypto.Util=new function(){this.DIGESTINFOHEAD={sha1:"3021300906052b0e03021a05000414",sha224:"302d300d06096086480165030402040500041c",sha256:"3031300d060960864801650304020105000420",sha384:"3041300d060960864801650304020205000430",sha512:"3051300d060960864801650304020305000440",md2:"3020300c06082a864886f70d020205000410",md5:"3020300c06082a864886f70d020505000410",ripemd160:"3021300906052b2403020105000414"},this.DEFAULTPROVIDER={md5:"cryptojs",sha1:"cryptojs",sha224:"cryptojs",sha256:"cryptojs",sha384:"cryptojs",sha512:"cryptojs",ripemd160:"cryptojs",hmacmd5:"cryptojs",hmacsha1:"cryptojs",hmacsha224:"cryptojs",hmacsha256:"cryptojs",hmacsha384:"cryptojs",hmacsha512:"cryptojs",hmacripemd160:"cryptojs",MD5withRSA:"cryptojs/jsrsa",SHA1withRSA:"cryptojs/jsrsa",SHA224withRSA:"cryptojs/jsrsa",SHA256withRSA:"cryptojs/jsrsa",SHA384withRSA:"cryptojs/jsrsa",SHA512withRSA:"cryptojs/jsrsa",RIPEMD160withRSA:"cryptojs/jsrsa",MD5withECDSA:"cryptojs/jsrsa",SHA1withECDSA:"cryptojs/jsrsa",SHA224withECDSA:"cryptojs/jsrsa",SHA256withECDSA:"cryptojs/jsrsa",SHA384withECDSA:"cryptojs/jsrsa",SHA512withECDSA:"cryptojs/jsrsa",RIPEMD160withECDSA:"cryptojs/jsrsa",SHA1withDSA:"cryptojs/jsrsa",SHA224withDSA:"cryptojs/jsrsa",SHA256withDSA:"cryptojs/jsrsa",MD5withRSAandMGF1:"cryptojs/jsrsa",SHAwithRSAandMGF1:"cryptojs/jsrsa",SHA1withRSAandMGF1:"cryptojs/jsrsa",SHA224withRSAandMGF1:"cryptojs/jsrsa",SHA256withRSAandMGF1:"cryptojs/jsrsa",SHA384withRSAandMGF1:"cryptojs/jsrsa",SHA512withRSAandMGF1:"cryptojs/jsrsa",RIPEMD160withRSAandMGF1:"cryptojs/jsrsa"},this.CRYPTOJSMESSAGEDIGESTNAME={md5:a.algo.MD5,sha1:a.algo.SHA1,sha224:a.algo.SHA224,sha256:a.algo.SHA256,sha384:a.algo.SHA384,sha512:a.algo.SHA512,ripemd160:a.algo.RIPEMD160},this.getDigestInfoHex=function(t,e){if("undefined"==typeof this.DIGESTINFOHEAD[e])throw"alg not supported in Util.DIGESTINFOHEAD: "+e;return this.DIGESTINFOHEAD[e]+t},this.getPaddedDigestInfoHex=function(t,e,r){var i=this.getDigestInfoHex(t,e),n=r/4;if(i.length+22>n)throw"key is too short for SigAlg: keylen="+r+","+e;for(var s="0001",a="00"+i,o="",h=n-s.length-a.length,u=0;u<h;u+=2)o+="ff";var c=s+o+a;return c},this.hashString=function(t,e){var r=new Pr.crypto.MessageDigest({alg:e});return r.digestString(t)},this.hashHex=function(t,e){var r=new Pr.crypto.MessageDigest({alg:e});return r.digestHex(t)},this.sha1=function(t){return this.hashString(t,"sha1")},this.sha256=function(t){return this.hashString(t,"sha256")},this.sha256Hex=function(t){return this.hashHex(t,"sha256")},this.sha512=function(t){return this.hashString(t,"sha512")},this.sha512Hex=function(t){return this.hashHex(t,"sha512")},this.isKey=function(t){return t instanceof qe||t instanceof Pr.crypto.DSA||t instanceof Pr.crypto.ECDSA}},Pr.crypto.Util.md5=function(t){var e=new Pr.crypto.MessageDigest({alg:"md5",prov:"cryptojs"});return e.digestString(t)},Pr.crypto.Util.ripemd160=function(t){var e=new Pr.crypto.MessageDigest({alg:"ripemd160",prov:"cryptojs"});return e.digestString(t)},Pr.crypto.Util.SECURERANDOMGEN=new je,Pr.crypto.Util.getRandomHexOfNbytes=function(t){var e=new Array(t);return Pr.crypto.Util.SECURERANDOMGEN.nextBytes(e),Or(e)},Pr.crypto.Util.getRandomBigIntegerOfNbytes=function(t){return new d(Pr.crypto.Util.getRandomHexOfNbytes(t),16)},Pr.crypto.Util.getRandomHexOfNbits=function(t){var e=t%8,r=(t-e)/8,i=new Array(r+1);return Pr.crypto.Util.SECURERANDOMGEN.nextBytes(i),i[0]=(255<<e&255^255)&i[0],Or(i)},Pr.crypto.Util.getRandomBigIntegerOfNbits=function(t){return new d(Pr.crypto.Util.getRandomHexOfNbits(t),16)},Pr.crypto.Util.getRandomBigIntegerZeroToMax=function(t){var e=t.bitLength();while(1){var r=Pr.crypto.Util.getRandomBigIntegerOfNbits(e);if(-1!=t.compareTo(r))return r}},Pr.crypto.Util.getRandomBigIntegerMinToMax=function(t,e){var r=t.compareTo(e);if(1==r)throw"biMin is greater than biMax";if(0==r)return t;var i=e.subtract(t),n=Pr.crypto.Util.getRandomBigIntegerZeroToMax(i);return n.add(t)},Pr.crypto.MessageDigest=function(t){this.setAlgAndProvider=function(t,e){if(t=Pr.crypto.MessageDigest.getCanonicalAlgName(t),null!==t&&void 0===e&&(e=Pr.crypto.Util.DEFAULTPROVIDER[t]),-1!=":md5:sha1:sha224:sha256:sha384:sha512:ripemd160:".indexOf(t)&&"cryptojs"==e){try{this.md=Pr.crypto.Util.CRYPTOJSMESSAGEDIGESTNAME[t].create()}catch(r){throw"setAlgAndProvider hash alg set fail alg="+t+"/"+r}this.updateString=function(t){this.md.update(t)},this.updateHex=function(t){var e=a.enc.Hex.parse(t);this.md.update(e)},this.digest=function(){var t=this.md.finalize();return t.toString(a.enc.Hex)},this.digestString=function(t){return this.updateString(t),this.digest()},this.digestHex=function(t){return this.updateHex(t),this.digest()}}if(-1!=":sha256:".indexOf(t)&&"sjcl"==e){try{this.md=new sjcl.hash.sha256}catch(r){throw"setAlgAndProvider hash alg set fail alg="+t+"/"+r}this.updateString=function(t){this.md.update(t)},this.updateHex=function(t){var e=sjcl.codec.hex.toBits(t);this.md.update(e)},this.digest=function(){var t=this.md.finalize();return sjcl.codec.hex.fromBits(t)},this.digestString=function(t){return this.updateString(t),this.digest()},this.digestHex=function(t){return this.updateHex(t),this.digest()}}},this.updateString=function(t){throw"updateString(str) not supported for this alg/prov: "+this.algName+"/"+this.provName},this.updateHex=function(t){throw"updateHex(hex) not supported for this alg/prov: "+this.algName+"/"+this.provName},this.digest=function(){throw"digest() not supported for this alg/prov: "+this.algName+"/"+this.provName},this.digestString=function(t){throw"digestString(str) not supported for this alg/prov: "+this.algName+"/"+this.provName},this.digestHex=function(t){throw"digestHex(hex) not supported for this alg/prov: "+this.algName+"/"+this.provName},void 0!==t&&void 0!==t.alg&&(this.algName=t.alg,void 0===t.prov&&(this.provName=Pr.crypto.Util.DEFAULTPROVIDER[this.algName]),this.setAlgAndProvider(this.algName,this.provName))},Pr.crypto.MessageDigest.getCanonicalAlgName=function(t){return"string"===typeof t&&(t=t.toLowerCase(),t=t.replace(/-/,"")),t},Pr.crypto.MessageDigest.getHashLength=function(t){var e=Pr.crypto.MessageDigest,r=e.getCanonicalAlgName(t);if(void 0===e.HASHLENGTH[r])throw"not supported algorithm: "+t;return e.HASHLENGTH[r]},Pr.crypto.MessageDigest.HASHLENGTH={md5:16,sha1:20,sha224:28,sha256:32,sha384:48,sha512:64,ripemd160:20},Pr.crypto.Mac=function(t){this.setAlgAndProvider=function(t,e){if(t=t.toLowerCase(),null==t&&(t="hmacsha1"),t=t.toLowerCase(),"hmac"!=t.substr(0,4))throw"setAlgAndProvider unsupported HMAC alg: "+t;void 0===e&&(e=Pr.crypto.Util.DEFAULTPROVIDER[t]),this.algProv=t+"/"+e;var r=t.substr(4);if(-1!=":md5:sha1:sha224:sha256:sha384:sha512:ripemd160:".indexOf(r)&&"cryptojs"==e){try{var i=Pr.crypto.Util.CRYPTOJSMESSAGEDIGESTNAME[r];this.mac=a.algo.HMAC.create(i,this.pass)}catch(n){throw"setAlgAndProvider hash alg set fail hashAlg="+r+"/"+n}this.updateString=function(t){this.mac.update(t)},this.updateHex=function(t){var e=a.enc.Hex.parse(t);this.mac.update(e)},this.doFinal=function(){var t=this.mac.finalize();return t.toString(a.enc.Hex)},this.doFinalString=function(t){return this.updateString(t),this.doFinal()},this.doFinalHex=function(t){return this.updateHex(t),this.doFinal()}}},this.updateString=function(t){throw"updateString(str) not supported for this alg/prov: "+this.algProv},this.updateHex=function(t){throw"updateHex(hex) not supported for this alg/prov: "+this.algProv},this.doFinal=function(){throw"digest() not supported for this alg/prov: "+this.algProv},this.doFinalString=function(t){throw"digestString(str) not supported for this alg/prov: "+this.algProv},this.doFinalHex=function(t){throw"digestHex(hex) not supported for this alg/prov: "+this.algProv},this.setPassword=function(t){if("string"==typeof t){var e=t;return t.length%2!=1&&t.match(/^[0-9A-Fa-f]+$/)||(e=Yr(t)),void(this.pass=a.enc.Hex.parse(e))}if("object"!=i(t))throw"KJUR.crypto.Mac unsupported password type: "+t;e=null;if(void 0!==t.hex){if(t.hex.length%2!=0||!t.hex.match(/^[0-9A-Fa-f]+$/))throw"Mac: wrong hex password: "+t.hex;e=t.hex}if(void 0!==t.utf8&&(e=zr(t.utf8)),void 0!==t.rstr&&(e=Yr(t.rstr)),void 0!==t.b64&&(e=l(t.b64)),void 0!==t.b64u&&(e=_r(t.b64u)),null==e)throw"KJUR.crypto.Mac unsupported password type: "+t;this.pass=a.enc.Hex.parse(e)},void 0!==t&&(void 0!==t.pass&&this.setPassword(t.pass),void 0!==t.alg&&(this.algName=t.alg,void 0===t.prov&&(this.provName=Pr.crypto.Util.DEFAULTPROVIDER[this.algName]),this.setAlgAndProvider(this.algName,this.provName)))},Pr.crypto.Signature=function(t){var e=null;if(this._setAlgNames=function(){var t=this.algName.match(/^(.+)with(.+)$/);t&&(this.mdAlgName=t[1].toLowerCase(),this.pubkeyAlgName=t[2].toLowerCase(),"rsaandmgf1"==this.pubkeyAlgName&&"sha"==this.mdAlgName&&(this.mdAlgName="sha1"))},this._zeroPaddingOfSignature=function(t,e){for(var r="",i=e/4-t.length,n=0;n<i;n++)r+="0";return r+t},this.setAlgAndProvider=function(t,e){if(this._setAlgNames(),"cryptojs/jsrsa"!=e)throw new Error("provider not supported: "+e);if(-1!=":md5:sha1:sha224:sha256:sha384:sha512:ripemd160:".indexOf(this.mdAlgName)){try{this.md=new Pr.crypto.MessageDigest({alg:this.mdAlgName})}catch(r){throw new Error("setAlgAndProvider hash alg set fail alg="+this.mdAlgName+"/"+r)}this.init=function(t,e){var r=null;try{r=void 0===e?Bi.getKey(t):Bi.getKey(t,e)}catch(i){throw"init failed:"+i}if(!0===r.isPrivate)this.prvKey=r,this.state="SIGN";else{if(!0!==r.isPublic)throw"init failed.:"+r;this.pubKey=r,this.state="VERIFY"}},this.updateString=function(t){this.md.updateString(t)},this.updateHex=function(t){this.md.updateHex(t)},this.sign=function(){if(this.sHashHex=this.md.digest(),void 0===this.prvKey&&void 0!==this.ecprvhex&&void 0!==this.eccurvename&&void 0!==Pr.crypto.ECDSA&&(this.prvKey=new Pr.crypto.ECDSA({curve:this.eccurvename,prv:this.ecprvhex})),this.prvKey instanceof qe&&"rsaandmgf1"===this.pubkeyAlgName)this.hSign=this.prvKey.signWithMessageHashPSS(this.sHashHex,this.mdAlgName,this.pssSaltLen);else if(this.prvKey instanceof qe&&"rsa"===this.pubkeyAlgName)this.hSign=this.prvKey.signWithMessageHash(this.sHashHex,this.mdAlgName);else if(this.prvKey instanceof Pr.crypto.ECDSA)this.hSign=this.prvKey.signWithMessageHash(this.sHashHex);else{if(!(this.prvKey instanceof Pr.crypto.DSA))throw"Signature: unsupported private key alg: "+this.pubkeyAlgName;this.hSign=this.prvKey.signWithMessageHash(this.sHashHex)}return this.hSign},this.signString=function(t){return this.updateString(t),this.sign()},this.signHex=function(t){return this.updateHex(t),this.sign()},this.verify=function(t){if(this.sHashHex=this.md.digest(),void 0===this.pubKey&&void 0!==this.ecpubhex&&void 0!==this.eccurvename&&void 0!==Pr.crypto.ECDSA&&(this.pubKey=new Pr.crypto.ECDSA({curve:this.eccurvename,pub:this.ecpubhex})),this.pubKey instanceof qe&&"rsaandmgf1"===this.pubkeyAlgName)return this.pubKey.verifyWithMessageHashPSS(this.sHashHex,t,this.mdAlgName,this.pssSaltLen);if(this.pubKey instanceof qe&&"rsa"===this.pubkeyAlgName)return this.pubKey.verifyWithMessageHash(this.sHashHex,t);if(void 0!==Pr.crypto.ECDSA&&this.pubKey instanceof Pr.crypto.ECDSA)return this.pubKey.verifyWithMessageHash(this.sHashHex,t);if(void 0!==Pr.crypto.DSA&&this.pubKey instanceof Pr.crypto.DSA)return this.pubKey.verifyWithMessageHash(this.sHashHex,t);throw"Signature: unsupported public key alg: "+this.pubkeyAlgName}}},this.init=function(t,e){throw"init(key, pass) not supported for this alg:prov="+this.algProvName},this.updateString=function(t){throw"updateString(str) not supported for this alg:prov="+this.algProvName},this.updateHex=function(t){throw"updateHex(hex) not supported for this alg:prov="+this.algProvName},this.sign=function(){throw"sign() not supported for this alg:prov="+this.algProvName},this.signString=function(t){throw"digestString(str) not supported for this alg:prov="+this.algProvName},this.signHex=function(t){throw"digestHex(hex) not supported for this alg:prov="+this.algProvName},this.verify=function(t){throw"verify(hSigVal) not supported for this alg:prov="+this.algProvName},this.initParams=t,void 0!==t&&(void 0!==t.alg&&(this.algName=t.alg,void 0===t.prov?this.provName=Pr.crypto.Util.DEFAULTPROVIDER[this.algName]:this.provName=t.prov,this.algProvName=this.algName+":"+this.provName,this.setAlgAndProvider(this.algName,this.provName),this._setAlgNames()),void 0!==t.psssaltlen&&(this.pssSaltLen=t.psssaltlen),void 0!==t.prvkeypem)){if(void 0!==t.prvkeypas)throw"both prvkeypem and prvkeypas parameters not supported";try{e=Bi.getKey(t.prvkeypem);this.init(e)}catch(r){throw"fatal error to load pem private key: "+r}}},Pr.crypto.Cipher=function(t){},Pr.crypto.Cipher.encrypt=function(t,e,r){if(e instanceof qe&&e.isPublic){var i=Pr.crypto.Cipher.getAlgByKeyAndName(e,r);if("RSA"===i)return e.encrypt(t);if("RSAOAEP"===i)return e.encryptOAEP(t,"sha1");var n=i.match(/^RSAOAEP(\d+)$/);if(null!==n)return e.encryptOAEP(t,"sha"+n[1]);throw"Cipher.encrypt: unsupported algorithm for RSAKey: "+r}throw"Cipher.encrypt: unsupported key or algorithm"},Pr.crypto.Cipher.decrypt=function(t,e,r){if(e instanceof qe&&e.isPrivate){var i=Pr.crypto.Cipher.getAlgByKeyAndName(e,r);if("RSA"===i)return e.decrypt(t);if("RSAOAEP"===i)return e.decryptOAEP(t,"sha1");var n=i.match(/^RSAOAEP(\d+)$/);if(null!==n)return e.decryptOAEP(t,"sha"+n[1]);throw"Cipher.decrypt: unsupported algorithm for RSAKey: "+r}throw"Cipher.decrypt: unsupported key or algorithm"},Pr.crypto.Cipher.getAlgByKeyAndName=function(t,e){if(t instanceof qe){if(-1!=":RSA:RSAOAEP:RSAOAEP224:RSAOAEP256:RSAOAEP384:RSAOAEP512:".indexOf(e))return e;if(null===e||void 0===e)return"RSA";throw"getAlgByKeyAndName: not supported algorithm name for RSAKey: "+e}throw"getAlgByKeyAndName: not supported algorithm name: "+e},Pr.crypto.OID=new function(){this.oidhex2name={"2a864886f70d010101":"rsaEncryption","2a8648ce3d0201":"ecPublicKey","2a8648ce380401":"dsa","2a8648ce3d030107":"secp256r1","2b8104001f":"secp192k1","2b81040021":"secp224r1","2b8104000a":"secp256k1","2b81040022":"secp384r1","2b81040023":"secp521r1","2a8648ce380403":"SHA1withDSA","608648016503040301":"SHA224withDSA","608648016503040302":"SHA256withDSA"}},"undefined"!=typeof Pr&&Pr||(Pr={}),"undefined"!=typeof Pr.crypto&&Pr.crypto||(Pr.crypto={}),Pr.crypto.ECDSA=function(t){var e="secp256r1",r=Error,n=d,s=cr,a=Pr.crypto.ECDSA,o=Pr.crypto.ECParameterDB,h=a.getName,u=Br,c=u.getVbyListEx,l=u.isASN1HEX,f=new je;this.type="EC",this.isPrivate=!1,this.isPublic=!1,this.getBigRandom=function(t){return new n(t.bitLength(),f).mod(t.subtract(n.ONE)).add(n.ONE)},this.setNamedCurve=function(t){this.ecparams=o.getByName(t),this.prvKeyHex=null,this.pubKeyHex=null,this.curveName=t},this.setPrivateKeyHex=function(t){this.isPrivate=!0,this.prvKeyHex=t},this.setPublicKeyHex=function(t){this.isPublic=!0,this.pubKeyHex=t},this.getPublicKeyXYHex=function(){var t=this.pubKeyHex;if("04"!==t.substr(0,2))throw"this method supports uncompressed format(04) only";var e=this.ecparams.keycharlen;if(t.length!==2+2*e)throw"malformed public key hex length";var r={};return r.x=t.substr(2,e),r.y=t.substr(2+e),r},this.getShortNISTPCurveName=function(){var t=this.curveName;return"secp256r1"===t||"NIST P-256"===t||"P-256"===t||"prime256v1"===t?"P-256":"secp384r1"===t||"NIST P-384"===t||"P-384"===t?"P-384":"secp521r1"===t||"NIST P-521"===t||"P-521"===t?"P-521":null},this.generateKeyPairHex=function(){var t=this.ecparams.n,e=this.getBigRandom(t),r=this.ecparams.keycharlen,i=("0000000000"+e.toString(16)).slice(-r);this.setPrivateKeyHex(i);var n=this.generatePublicKeyHex();return{ecprvhex:i,ecpubhex:n}},this.generatePublicKeyHex=function(){var t=new n(this.prvKeyHex,16),e=this.ecparams.G.multiply(t),r=e.getX().toBigInteger(),i=e.getY().toBigInteger(),s=this.ecparams.keycharlen,a=("0000000000"+r.toString(16)).slice(-s),o=("0000000000"+i.toString(16)).slice(-s),h="04"+a+o;return this.setPublicKeyHex(h),h},this.signWithMessageHash=function(t){return this.signHex(t,this.prvKeyHex)},this.signHex=function(t,e){var r=new n(e,16),i=this.ecparams.n,s=new n(t.substring(0,this.ecparams.keycharlen),16);do{var o=this.getBigRandom(i),h=this.ecparams.G,u=h.multiply(o),c=u.getX().toBigInteger().mod(i)}while(c.compareTo(n.ZERO)<=0);var l=o.modInverse(i).multiply(s.add(r.multiply(c))).mod(i);return a.biRSSigToASN1Sig(c,l)},this.sign=function(t,e){var r=e,i=this.ecparams.n,s=n.fromByteArrayUnsigned(t);do{var a=this.getBigRandom(i),o=this.ecparams.G,h=o.multiply(a),u=h.getX().toBigInteger().mod(i)}while(u.compareTo(d.ZERO)<=0);var c=a.modInverse(i).multiply(s.add(r.multiply(u))).mod(i);return this.serializeSig(u,c)},this.verifyWithMessageHash=function(t,e){return this.verifyHex(t,e,this.pubKeyHex)},this.verifyHex=function(t,e,r){try{var i,o,h=a.parseSigHex(e);i=h.r,o=h.s;var u=s.decodeFromHex(this.ecparams.curve,r),c=new n(t.substring(0,this.ecparams.keycharlen),16);return this.verifyRaw(c,i,o,u)}catch(l){return!1}},this.verify=function(t,e,r){var a,o,h;if(Bitcoin.Util.isArray(e)){var u=this.parseSig(e);a=u.r,o=u.s}else{if("object"!==i(e)||!e.r||!e.s)throw"Invalid value for signature";a=e.r,o=e.s}if(r instanceof cr)h=r;else{if(!Bitcoin.Util.isArray(r))throw"Invalid format for pubkey value, must be byte array or ECPointFp";h=s.decodeFrom(this.ecparams.curve,r)}var c=n.fromByteArrayUnsigned(t);return this.verifyRaw(c,a,o,h)},this.verifyRaw=function(t,e,r,i){var s=this.ecparams.n,a=this.ecparams.G;if(e.compareTo(n.ONE)<0||e.compareTo(s)>=0)return!1;if(r.compareTo(n.ONE)<0||r.compareTo(s)>=0)return!1;var o=r.modInverse(s),h=t.multiply(o).mod(s),u=e.multiply(o).mod(s),c=a.multiply(h).add(i.multiply(u)),l=c.getX().toBigInteger().mod(s);return l.equals(e)},this.serializeSig=function(t,e){var r=t.toByteArraySigned(),i=e.toByteArraySigned(),n=[];return n.push(2),n.push(r.length),n=n.concat(r),n.push(2),n.push(i.length),n=n.concat(i),n.unshift(n.length),n.unshift(48),n},this.parseSig=function(t){var e;if(48!=t[0])throw new Error("Signature not a valid DERSequence");if(e=2,2!=t[e])throw new Error("First element in signature must be a DERInteger");var r=t.slice(e+2,e+2+t[e+1]);if(e+=2+t[e+1],2!=t[e])throw new Error("Second element in signature must be a DERInteger");var i=t.slice(e+2,e+2+t[e+1]);e+=2+t[e+1];var s=n.fromByteArrayUnsigned(r),a=n.fromByteArrayUnsigned(i);return{r:s,s:a}},this.parseSigCompact=function(t){if(65!==t.length)throw"Signature has the wrong length";var e=t[0]-27;if(e<0||e>7)throw"Invalid signature type";var r=this.ecparams.n,i=n.fromByteArrayUnsigned(t.slice(1,33)).mod(r),s=n.fromByteArrayUnsigned(t.slice(33,65)).mod(r);return{r:i,s:s,i:e}},this.readPKCS5PrvKeyHex=function(t){if(!1===l(t))throw new Error("not ASN.1 hex string");var e,r,i;try{e=c(t,0,["[0]",0],"06"),r=c(t,0,[1],"04");try{i=c(t,0,["[1]",0],"03")}catch(n){}}catch(n){throw new Error("malformed PKCS#1/5 plain ECC private key")}if(this.curveName=h(e),void 0===this.curveName)throw"unsupported curve name";this.setNamedCurve(this.curveName),this.setPublicKeyHex(i),this.setPrivateKeyHex(r),this.isPublic=!1},this.readPKCS8PrvKeyHex=function(t){if(!1===l(t))throw new r("not ASN.1 hex string");var e,i,n;try{c(t,0,[1,0],"06"),e=c(t,0,[1,1],"06"),i=c(t,0,[2,0,1],"04");try{n=c(t,0,[2,0,"[1]",0],"03")}catch(s){}}catch(s){throw new r("malformed PKCS#8 plain ECC private key")}if(this.curveName=h(e),void 0===this.curveName)throw new r("unsupported curve name");this.setNamedCurve(this.curveName),this.setPublicKeyHex(n),this.setPrivateKeyHex(i),this.isPublic=!1},this.readPKCS8PubKeyHex=function(t){if(!1===l(t))throw new r("not ASN.1 hex string");var e,i;try{c(t,0,[0,0],"06"),e=c(t,0,[0,1],"06"),i=c(t,0,[1],"03")}catch(n){throw new r("malformed PKCS#8 ECC public key")}if(this.curveName=h(e),null===this.curveName)throw new r("unsupported curve name");this.setNamedCurve(this.curveName),this.setPublicKeyHex(i)},this.readCertPubKeyHex=function(t,e){if(!1===l(t))throw new r("not ASN.1 hex string");var i,n;try{i=c(t,0,[0,5,0,1],"06"),n=c(t,0,[0,5,1],"03")}catch(s){throw new r("malformed X.509 certificate ECC public key")}if(this.curveName=h(i),null===this.curveName)throw new r("unsupported curve name");this.setNamedCurve(this.curveName),this.setPublicKeyHex(n)},void 0!==t&&void 0!==t.curve&&(this.curveName=t.curve),void 0===this.curveName&&(this.curveName=e),this.setNamedCurve(this.curveName),void 0!==t&&(void 0!==t.prv&&this.setPrivateKeyHex(t.prv),void 0!==t.pub&&this.setPublicKeyHex(t.pub))},Pr.crypto.ECDSA.parseSigHex=function(t){var e=Pr.crypto.ECDSA.parseSigHexInHexRS(t),r=new d(e.r,16),i=new d(e.s,16);return{r:r,s:i}},Pr.crypto.ECDSA.parseSigHexInHexRS=function(t){var e=Br,r=e.getChildIdx,i=e.getV;if(e.checkStrictDER(t,0),"30"!=t.substr(0,2))throw new Error("signature is not a ASN.1 sequence");var n=r(t,0);if(2!=n.length)throw new Error("signature shall have two elements");var s=n[0],a=n[1];if("02"!=t.substr(s,2))throw new Error("1st item not ASN.1 integer");if("02"!=t.substr(a,2))throw new Error("2nd item not ASN.1 integer");var o=i(t,s),h=i(t,a);return{r:o,s:h}},Pr.crypto.ECDSA.asn1SigToConcatSig=function(t){var e=Pr.crypto.ECDSA.parseSigHexInHexRS(t),r=e.r,i=e.s;if(r.length>=130&&r.length<=134){if(r.length%2!=0)throw Error("unknown ECDSA sig r length error");if(i.length%2!=0)throw Error("unknown ECDSA sig s length error");"00"==r.substr(0,2)&&(r=r.substr(2)),"00"==i.substr(0,2)&&(i=i.substr(2));var n=Math.max(r.length,i.length);return r=("000000"+r).slice(-n),i=("000000"+i).slice(-n),r+i}if("00"==r.substr(0,2)&&r.length%32==2&&(r=r.substr(2)),"00"==i.substr(0,2)&&i.length%32==2&&(i=i.substr(2)),r.length%32==30&&(r="00"+r),i.length%32==30&&(i="00"+i),r.length%32!=0)throw Error("unknown ECDSA sig r length error");if(i.length%32!=0)throw Error("unknown ECDSA sig s length error");return r+i},Pr.crypto.ECDSA.concatSigToASN1Sig=function(t){if(t.length%4!=0)throw Error("unknown ECDSA concatinated r-s sig length error");var e=t.substr(0,t.length/2),r=t.substr(t.length/2);return Pr.crypto.ECDSA.hexRSSigToASN1Sig(e,r)},Pr.crypto.ECDSA.hexRSSigToASN1Sig=function(t,e){var r=new d(t,16),i=new d(e,16);return Pr.crypto.ECDSA.biRSSigToASN1Sig(r,i)},Pr.crypto.ECDSA.biRSSigToASN1Sig=function(t,e){var r=Pr.asn1,i=new r.DERInteger({bigint:t}),n=new r.DERInteger({bigint:e}),s=new r.DERSequence({array:[i,n]});return s.tohex()},Pr.crypto.ECDSA.getName=function(t){return"2b8104001f"===t?"secp192k1":"2a8648ce3d030107"===t?"secp256r1":"2b8104000a"===t?"secp256k1":"2b81040021"===t?"secp224r1":"2b81040022"===t?"secp384r1":"2b81040023"===t?"secp521r1":-1!=="|secp256r1|NIST P-256|P-256|prime256v1|".indexOf(t)?"secp256r1":-1!=="|secp256k1|".indexOf(t)?"secp256k1":-1!=="|secp224r1|NIST P-224|P-224|".indexOf(t)?"secp224r1":-1!=="|secp384r1|NIST P-384|P-384|".indexOf(t)?"secp384r1":-1!=="|secp521r1|NIST P-521|P-521|".indexOf(t)?"secp521r1":null},"undefined"!=typeof Pr&&Pr||(Pr={}),"undefined"!=typeof Pr.crypto&&Pr.crypto||(Pr.crypto={}),Pr.crypto.ECParameterDB=new function(){var t={},e={};function r(t){return new d(t,16)}this.getByName=function(r){var i=r;if("undefined"!=typeof e[i]&&(i=e[r]),"undefined"!=typeof t[i])return t[i];throw"unregistered EC curve name: "+i},this.regist=function(i,n,s,a,o,h,u,c,l,f,g,p){t[i]={};var d=r(s),v=r(a),m=r(o),y=r(h),x=r(u),S=new Sr(d,v,m),w=S.decodePointHex("04"+c+l);t[i]["name"]=i,t[i]["keylen"]=n,t[i]["keycharlen"]=2*Math.ceil(n/8),t[i]["curve"]=S,t[i]["G"]=w,t[i]["n"]=y,t[i]["h"]=x,t[i]["oid"]=g,t[i]["info"]=p;for(var E=0;E<f.length;E++)e[f[E]]=i}},Pr.crypto.ECParameterDB.regist("secp128r1",128,"FFFFFFFDFFFFFFFFFFFFFFFFFFFFFFFF","FFFFFFFDFFFFFFFFFFFFFFFFFFFFFFFC","E87579C11079F43DD824993C2CEE5ED3","FFFFFFFE0000000075A30D1B9038A115","1","161FF7528B899B2D0C28607CA52C5B86","CF5AC8395BAFEB13C02DA292DDED7A83",[],"","secp128r1 : SECG curve over a 128 bit prime field"),Pr.crypto.ECParameterDB.regist("secp160k1",160,"FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFAC73","0","7","0100000000000000000001B8FA16DFAB9ACA16B6B3","1","3B4C382CE37AA192A4019E763036F4F5DD4D7EBB","938CF935318FDCED6BC28286531733C3F03C4FEE",[],"","secp160k1 : SECG curve over a 160 bit prime field"),Pr.crypto.ECParameterDB.regist("secp160r1",160,"FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF7FFFFFFF","FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF7FFFFFFC","****************************************","0100000000000000000001F4C8F927AED3CA752257","1","4A96B5688EF573284664698968C38BB913CBFC82","23A628553168947D59DCC912042351377AC5FB32",[],"","secp160r1 : SECG curve over a 160 bit prime field"),Pr.crypto.ECParameterDB.regist("secp192k1",192,"FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFEE37","0","3","FFFFFFFFFFFFFFFFFFFFFFFE26F2FC170F69466A74DEFD8D","1","DB4FF10EC057E9AE26B07D0280B7F4341DA5D1B1EAE06C7D","9B2F2F6D9C5628A7844163D015BE86344082AA88D95E2F9D",[]),Pr.crypto.ECParameterDB.regist("secp192r1",192,"FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFFFFFFFFFFFFF","FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFFFFFFFFFFFFC","64210519E59C80E70FA7E9AB72243049FEB8DEECC146B9B1","FFFFFFFFFFFFFFFFFFFFFFFF99DEF836146BC9B1B4D22831","1","188DA80EB03090F67CBF20EB43A18800F4FF0AFD82FF1012","07192B95FFC8DA78631011ED6B24CDD573F977A11E794811",[]),Pr.crypto.ECParameterDB.regist("secp224r1",224,"FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF000000000000000000000001","FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFE","B4050A850C04B3ABF54132565044B0B7D7BFD8BA270B39432355FFB4","FFFFFFFFFFFFFFFFFFFFFFFFFFFF16A2E0B8F03E13DD29455C5C2A3D","1","B70E0CBD6BB4BF7F321390B94A03C1D356C21122343280D6115C1D21","BD376388B5F723FB4C22DFE6CD4375A05A07476444D5819985007E34",[]),Pr.crypto.ECParameterDB.regist("secp256k1",256,"FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFFC2F","0","7","FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141","1","79BE667EF9DCBBAC55A06295CE870B07029BFCDB2DCE28D959F2815B16F81798","483ADA7726A3C4655DA4FBFC0E1108A8FD17B448A68554199C47D08FFB10D4B8",[]),Pr.crypto.ECParameterDB.regist("secp256r1",256,"FFFFFFFF00000001000000000000000000000000FFFFFFFFFFFFFFFFFFFFFFFF","FFFFFFFF00000001000000000000000000000000FFFFFFFFFFFFFFFFFFFFFFFC","5AC635D8AA3A93E7B3EBBD55769886BC651D06B0CC53B0F63BCE3C3E27D2604B","FFFFFFFF00000000FFFFFFFFFFFFFFFFBCE6FAADA7179E84F3B9CAC2FC632551","1","6B17D1F2E12C4247F8BCE6E563A440F277037D812DEB33A0F4A13945D898C296","4FE342E2FE1A7F9B8EE7EB4A7C0F9E162BCE33576B315ECECBB6406837BF51F5",["NIST P-256","P-256","prime256v1"]),Pr.crypto.ECParameterDB.regist("secp384r1",384,"FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFFFFF0000000000000000FFFFFFFF","FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFFFFF0000000000000000FFFFFFFC","B3312FA7E23EE7E4988E056BE3F82D19181D9C6EFE8141120314088F5013875AC656398D8A2ED19D2A85C8EDD3EC2AEF","FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFC7634D81F4372DDF581A0DB248B0A77AECEC196ACCC52973","1","AA87CA22BE8B05378EB1C71EF320AD746E1D3B628BA79B9859F741E082542A385502F25DBF55296C3A545E3872760AB7","3617de4a96262c6f5d9e98bf9292dc29f8f41dbd289a147ce9da3113b5f0b8c00a60b1ce1d7e819d7a431d7c90ea0e5f",["NIST P-384","P-384"]),Pr.crypto.ECParameterDB.regist("secp521r1",521,"1FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF","1FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFC","051953EB9618E1C9A1F929A21A0B68540EEA2DA725B99B315F3B8B489918EF109E156193951EC7E937B1652C0BD3BB1BF073573DF883D2C34F1EF451FD46B503F00","1FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFA51868783BF2F966B7FCC0148F709A5D03BB5C9B8899C47AEBB6FB71E91386409","1","00C6858E06B70404E9CD9E3ECB662395B4429C648139053FB521F828AF606B4D3DBAA14B5E77EFE75928FE1DC127A2FFA8DE3348B3C1856A429BF97E7E31C2E5BD66","011839296a789a3bc0045c8a5fb42c7d1bd998f54449579b446817afbd17273e662c97ee72995ef42640c550b9013fad0761353c7086a272c24088be94769fd16650",["NIST P-521","P-521"]),"undefined"!=typeof Pr&&Pr||(Pr={}),"undefined"!=typeof Pr.crypto&&Pr.crypto||(Pr.crypto={}),Pr.crypto.DSA=function(){var t=Br,e=(t.getVbyList,t.getVbyListEx),r=t.isASN1HEX,i=d;this.p=null,this.q=null,this.g=null,this.y=null,this.x=null,this.type="DSA",this.isPrivate=!1,this.isPublic=!1,this.setPrivate=function(t,e,r,i,n){this.isPrivate=!0,this.p=t,this.q=e,this.g=r,this.y=i,this.x=n},this.setPrivateHex=function(t,e,r,i,n){var s,a,o,h,u;s=new d(t,16),a=new d(e,16),o=new d(r,16),h="string"===typeof i&&i.length>1?new d(i,16):null,u=new d(n,16),this.setPrivate(s,a,o,h,u)},this.setPublic=function(t,e,r,i){this.isPublic=!0,this.p=t,this.q=e,this.g=r,this.y=i,this.x=null},this.setPublicHex=function(t,e,r,i){var n,s,a,o;n=new d(t,16),s=new d(e,16),a=new d(r,16),o=new d(i,16),this.setPublic(n,s,a,o)},this.signWithMessageHash=function(t){var e=this.p,r=this.q,i=this.g,n=(this.y,this.x),s=Pr.crypto.Util.getRandomBigIntegerMinToMax(d.ONE.add(d.ONE),r.subtract(d.ONE)),a=t.substr(0,r.bitLength()/4),o=new d(a,16),h=i.modPow(s,e).mod(r),u=s.modInverse(r).multiply(o.add(n.multiply(h))).mod(r),c=Pr.asn1.ASN1Util.jsonToASN1HEX({seq:[{int:{bigint:h}},{int:{bigint:u}}]});return c},this.verifyWithMessageHash=function(t,e){var r=this.p,i=this.q,n=this.g,s=this.y,a=this.parseASN1Signature(e),o=a[0],h=a[1],u=t.substr(0,i.bitLength()/4),c=new d(u,16);if(d.ZERO.compareTo(o)>0||o.compareTo(i)>0)throw"invalid DSA signature";if(d.ZERO.compareTo(h)>=0||h.compareTo(i)>0)throw"invalid DSA signature";var l=h.modInverse(i),f=c.multiply(l).mod(i),g=o.multiply(l).mod(i),p=n.modPow(f,r).multiply(s.modPow(g,r)).mod(r).mod(i);return 0==p.compareTo(o)},this.parseASN1Signature=function(t){try{var r=new i(e(t,0,[0],"02"),16),n=new i(e(t,0,[1],"02"),16);return[r,n]}catch(s){throw new Error("malformed ASN.1 DSA signature")}},this.readPKCS5PrvKeyHex=function(t){var i,n,s,a,o;if(!1===r(t))throw new Error("not ASN.1 hex string");try{i=e(t,0,[1],"02"),n=e(t,0,[2],"02"),s=e(t,0,[3],"02"),a=e(t,0,[4],"02"),o=e(t,0,[5],"02")}catch(h){throw new Error("malformed PKCS#1/5 plain DSA private key")}this.setPrivateHex(i,n,s,a,o)},this.readPKCS8PrvKeyHex=function(t){var i,n,s,a;if(!1===r(t))throw new Error("not ASN.1 hex string");try{i=e(t,0,[1,1,0],"02"),n=e(t,0,[1,1,1],"02"),s=e(t,0,[1,1,2],"02"),a=e(t,0,[2,0],"02")}catch(o){throw new Error("malformed PKCS#8 plain DSA private key")}this.setPrivateHex(i,n,s,null,a)},this.readPKCS8PubKeyHex=function(t){var i,n,s,a;if(!1===r(t))throw new Error("not ASN.1 hex string");try{i=e(t,0,[0,1,0],"02"),n=e(t,0,[0,1,1],"02"),s=e(t,0,[0,1,2],"02"),a=e(t,0,[1,0],"02")}catch(o){throw new Error("malformed PKCS#8 DSA public key")}this.setPublicHex(i,n,s,a)},this.readCertPubKeyHex=function(t,i){var n,s,a,o;if(!1===r(t))throw new Error("not ASN.1 hex string");try{n=e(t,0,[0,5,0,1,0],"02"),s=e(t,0,[0,5,0,1,1],"02"),a=e(t,0,[0,5,0,1,2],"02"),o=e(t,0,[0,5,1,0],"02")}catch(h){throw new Error("malformed X.509 certificate DSA public key")}this.setPublicHex(n,s,a,o)}};var Bi=function(){var t=function(t,e,r){return i(a.AES,t,e,r)},e=function(t,e,r){return i(a.TripleDES,t,e,r)},r=function(t,e,r){return i(a.DES,t,e,r)},i=function(t,e,r,i){var n=a.enc.Hex.parse(e),s=a.enc.Hex.parse(r),o=a.enc.Hex.parse(i),h={};h.key=s,h.iv=o,h.ciphertext=n;var u=t.decrypt(h,s,{iv:o});return a.enc.Hex.stringify(u)},n=function(t,e,r){return h(a.AES,t,e,r)},s=function(t,e,r){return h(a.TripleDES,t,e,r)},o=function(t,e,r){return h(a.DES,t,e,r)},h=function(t,e,r,i){var n=a.enc.Hex.parse(e),s=a.enc.Hex.parse(r),o=a.enc.Hex.parse(i),h=t.encrypt(n,s,{iv:o}),u=a.enc.Hex.parse(h.toString()),c=a.enc.Base64.stringify(u);return c},u={"AES-256-CBC":{proc:t,eproc:n,keylen:32,ivlen:16},"AES-192-CBC":{proc:t,eproc:n,keylen:24,ivlen:16},"AES-128-CBC":{proc:t,eproc:n,keylen:16,ivlen:16},"DES-EDE3-CBC":{proc:e,eproc:s,keylen:24,ivlen:8},"DES-CBC":{proc:r,eproc:o,keylen:8,ivlen:8}},c=function(t){var e=a.lib.WordArray.random(t),r=a.enc.Hex.stringify(e);return r},l=function(t){var e={},r=t.match(new RegExp("DEK-Info: ([^,]+),([0-9A-Fa-f]+)","m"));r&&(e.cipher=r[1],e.ivsalt=r[2]);var i=t.match(new RegExp("-----BEGIN ([A-Z]+) PRIVATE KEY-----"));i&&(e.type=i[1]);var n=-1,s=0;-1!=t.indexOf("\r\n\r\n")&&(n=t.indexOf("\r\n\r\n"),s=2),-1!=t.indexOf("\n\n")&&(n=t.indexOf("\n\n"),s=1);var a=t.indexOf("-----END");if(-1!=n&&-1!=a){var o=t.substring(n+2*s,a-s);o=o.replace(/\s+/g,""),e.data=o}return e},f=function(t,e,r){for(var i=r.substring(0,16),n=a.enc.Hex.parse(i),s=a.enc.Utf8.parse(e),o=u[t]["keylen"]+u[t]["ivlen"],h="",c=null;;){var l=a.algo.MD5.create();if(null!=c&&l.update(c),l.update(s),l.update(n),c=l.finalize(),h+=a.enc.Hex.stringify(c),h.length>=2*o)break}var f={};return f.keyhex=h.substr(0,2*u[t]["keylen"]),f.ivhex=h.substr(2*u[t]["keylen"],2*u[t]["ivlen"]),f},g=function(t,e,r,i){var n=a.enc.Base64.parse(t),s=a.enc.Hex.stringify(n),o=u[e]["proc"],h=o(s,r,i);return h},p=function(t,e,r,i){var n=u[e]["eproc"],s=n(t,r,i);return s};return{version:"1.0.0",parsePKCS5PEM:function(t){return l(t)},getKeyAndUnusedIvByPasscodeAndIvsalt:function(t,e,r){return f(t,e,r)},decryptKeyB64:function(t,e,r,i){return g(t,e,r,i)},getDecryptedKeyHex:function(t,e){var r=l(t),i=(r.type,r.cipher),n=r.ivsalt,s=r.data,a=f(i,e,n),o=a.keyhex,h=g(s,i,o,n);return h},getEncryptedPKCS5PEMFromPrvKeyHex:function(t,e,r,i,n){var s="";if("undefined"!=typeof i&&null!=i||(i="AES-256-CBC"),"undefined"==typeof u[i])throw new Error("KEYUTIL unsupported algorithm: "+i);if("undefined"==typeof n||null==n){var a=u[i]["ivlen"],o=c(a);n=o.toUpperCase()}var h=f(i,r,n),l=h.keyhex,g=p(e,i,l,n),d=g.replace(/(.{64})/g,"$1\r\n");s="-----BEGIN "+t+" PRIVATE KEY-----\r\n";return s+="Proc-Type: 4,ENCRYPTED\r\n",s+="DEK-Info: "+i+","+n+"\r\n",s+="\r\n",s+=d,s+="\r\n-----END "+t+" PRIVATE KEY-----\r\n",s},parseHexOfEncryptedPKCS8:function(t){var e=Br,r=e.getChildIdx,i=e.getV,n={},s=r(t,0);if(2!=s.length)throw new Error("malformed format: SEQUENCE(0).items != 2: "+s.length);n.ciphertext=i(t,s[1]);var a=r(t,s[0]);if(2!=a.length)throw new Error("malformed format: SEQUENCE(0.0).items != 2: "+a.length);if("2a864886f70d01050d"!=i(t,a[0]))throw new Error("this only supports pkcs5PBES2");var o=r(t,a[1]);if(2!=a.length)throw new Error("malformed format: SEQUENCE(0.0.1).items != 2: "+o.length);var h=r(t,o[1]);if(2!=h.length)throw new Error("malformed format: SEQUENCE(*******).items != 2: "+h.length);if("2a864886f70d0307"!=i(t,h[0]))throw"this only supports TripleDES";n.encryptionSchemeAlg="TripleDES",n.encryptionSchemeIV=i(t,h[1]);var u=r(t,o[0]);if(2!=u.length)throw new Error("malformed format: SEQUENCE(*******).items != 2: "+u.length);if("2a864886f70d01050c"!=i(t,u[0]))throw new Error("this only supports pkcs5PBKDF2");var c=r(t,u[1]);if(c.length<2)throw new Error("malformed format: SEQUENCE(*******.1).items < 2: "+c.length);n.pbkdf2Salt=i(t,c[0]);var l=i(t,c[1]);try{n.pbkdf2Iter=parseInt(l,16)}catch(f){throw new Error("malformed format pbkdf2Iter: "+l)}return n},getPBKDF2KeyHexFromParam:function(t,e){var r=a.enc.Hex.parse(t.pbkdf2Salt),i=t.pbkdf2Iter,n=a.PBKDF2(e,r,{keySize:6,iterations:i}),s=a.enc.Hex.stringify(n);return s},_getPlainPKCS8HexFromEncryptedPKCS8PEM:function(t,e){var r=ri(t,"ENCRYPTED PRIVATE KEY"),i=this.parseHexOfEncryptedPKCS8(r),n=Bi.getPBKDF2KeyHexFromParam(i,e),s={};s.ciphertext=a.enc.Hex.parse(i.ciphertext);var o=a.enc.Hex.parse(n),h=a.enc.Hex.parse(i.encryptionSchemeIV),u=a.TripleDES.decrypt(s,o,{iv:h}),c=a.enc.Hex.stringify(u);return c},getKeyFromEncryptedPKCS8PEM:function(t,e){var r=this._getPlainPKCS8HexFromEncryptedPKCS8PEM(t,e),i=this.getKeyFromPlainPrivatePKCS8Hex(r);return i},parsePlainPrivatePKCS8Hex:function(t){var e=Br,r=e.getChildIdx,i=e.getV,n={algparam:null};if("30"!=t.substr(0,2))throw new Error("malformed plain PKCS8 private key(code:001)");var s=r(t,0);if(s.length<3)throw new Error("malformed plain PKCS8 private key(code:002)");if("30"!=t.substr(s[1],2))throw new Error("malformed PKCS8 private key(code:003)");var a=r(t,s[1]);if(2!=a.length)throw new Error("malformed PKCS8 private key(code:004)");if("06"!=t.substr(a[0],2))throw new Error("malformed PKCS8 private key(code:005)");if(n.algoid=i(t,a[0]),"06"==t.substr(a[1],2)&&(n.algparam=i(t,a[1])),"04"!=t.substr(s[2],2))throw new Error("malformed PKCS8 private key(code:006)");return n.keyidx=e.getVidx(t,s[2]),n},getKeyFromPlainPrivatePKCS8PEM:function(t){var e=ri(t,"PRIVATE KEY"),r=this.getKeyFromPlainPrivatePKCS8Hex(e);return r},getKeyFromPlainPrivatePKCS8Hex:function(t){var e,r=this.parsePlainPrivatePKCS8Hex(t);if("2a864886f70d010101"==r.algoid)e=new qe;else if("2a8648ce380401"==r.algoid)e=new Pr.crypto.DSA;else{if("2a8648ce3d0201"!=r.algoid)throw new Error("unsupported private key algorithm");e=new Pr.crypto.ECDSA}return e.readPKCS8PrvKeyHex(t),e},_getKeyFromPublicPKCS8Hex:function(t){var e,r=Br.getVbyList(t,0,[0,0],"06");if("2a864886f70d010101"===r)e=new qe;else if("2a8648ce380401"===r)e=new Pr.crypto.DSA;else{if("2a8648ce3d0201"!==r)throw new Error("unsupported PKCS#8 public key hex");e=new Pr.crypto.ECDSA}return e.readPKCS8PubKeyHex(t),e},parsePublicRawRSAKeyHex:function(t){var e=Br,r=e.getChildIdx,i=e.getV,n={};if("30"!=t.substr(0,2))throw new Error("malformed RSA key(code:001)");var s=r(t,0);if(2!=s.length)throw new Error("malformed RSA key(code:002)");if("02"!=t.substr(s[0],2))throw new Error("malformed RSA key(code:003)");if(n.n=i(t,s[0]),"02"!=t.substr(s[1],2))throw new Error("malformed RSA key(code:004)");return n.e=i(t,s[1]),n},parsePublicPKCS8Hex:function(t){var e=Br,r=e.getChildIdx,i=e.getV,n={algparam:null},s=r(t,0);if(2!=s.length)throw new Error("outer DERSequence shall have 2 elements: "+s.length);var a=s[0];if("30"!=t.substr(a,2))throw new Error("malformed PKCS8 public key(code:001)");var o=r(t,a);if(2!=o.length)throw new Error("malformed PKCS8 public key(code:002)");if("06"!=t.substr(o[0],2))throw new Error("malformed PKCS8 public key(code:003)");if(n.algoid=i(t,o[0]),"06"==t.substr(o[1],2)?n.algparam=i(t,o[1]):"30"==t.substr(o[1],2)&&(n.algparam={},n.algparam.p=e.getVbyList(t,o[1],[0],"02"),n.algparam.q=e.getVbyList(t,o[1],[1],"02"),n.algparam.g=e.getVbyList(t,o[1],[2],"02")),"03"!=t.substr(s[1],2))throw new Error("malformed PKCS8 public key(code:004)");return n.key=i(t,s[1]).substr(2),n}}}();Bi.getKey=function(t,e,r){var i=Br,n=i.getChildIdx,s=(i.getV,i.getVbyList),a=Pr.crypto,o=a.ECDSA,h=a.DSA,u=qe,c=ri,l=Bi;if("undefined"!=typeof u&&t instanceof u)return t;if("undefined"!=typeof o&&t instanceof o)return t;if("undefined"!=typeof h&&t instanceof h)return t;if(void 0!==t.curve&&void 0!==t.xy&&void 0===t.d)return new o({pub:t.xy,curve:t.curve});if(void 0!==t.curve&&void 0!==t.d)return new o({prv:t.d,curve:t.curve});if(void 0===t.kty&&void 0!==t.n&&void 0!==t.e&&void 0===t.d){var f=new u;return f.setPublic(t.n,t.e),f}if(void 0===t.kty&&void 0!==t.n&&void 0!==t.e&&void 0!==t.d&&void 0!==t.p&&void 0!==t.q&&void 0!==t.dp&&void 0!==t.dq&&void 0!==t.co&&void 0===t.qi){f=new u;return f.setPrivateEx(t.n,t.e,t.d,t.p,t.q,t.dp,t.dq,t.co),f}if(void 0===t.kty&&void 0!==t.n&&void 0!==t.e&&void 0!==t.d&&void 0===t.p){f=new u;return f.setPrivate(t.n,t.e,t.d),f}if(void 0!==t.p&&void 0!==t.q&&void 0!==t.g&&void 0!==t.y&&void 0===t.x){f=new h;return f.setPublic(t.p,t.q,t.g,t.y),f}if(void 0!==t.p&&void 0!==t.q&&void 0!==t.g&&void 0!==t.y&&void 0!==t.x){f=new h;return f.setPrivate(t.p,t.q,t.g,t.y,t.x),f}if("RSA"===t.kty&&void 0!==t.n&&void 0!==t.e&&void 0===t.d){f=new u;return f.setPublic(_r(t.n),_r(t.e)),f}if("RSA"===t.kty&&void 0!==t.n&&void 0!==t.e&&void 0!==t.d&&void 0!==t.p&&void 0!==t.q&&void 0!==t.dp&&void 0!==t.dq&&void 0!==t.qi){f=new u;return f.setPrivateEx(_r(t.n),_r(t.e),_r(t.d),_r(t.p),_r(t.q),_r(t.dp),_r(t.dq),_r(t.qi)),f}if("RSA"===t.kty&&void 0!==t.n&&void 0!==t.e&&void 0!==t.d){f=new u;return f.setPrivate(_r(t.n),_r(t.e),_r(t.d)),f}if("EC"===t.kty&&void 0!==t.crv&&void 0!==t.x&&void 0!==t.y&&void 0===t.d){var g=new o({curve:t.crv}),p=g.ecparams.keycharlen,v=("0000000000"+_r(t.x)).slice(-p),m=("0000000000"+_r(t.y)).slice(-p),y="04"+v+m;return g.setPublicKeyHex(y),g}if("EC"===t.kty&&void 0!==t.crv&&void 0!==t.x&&void 0!==t.y&&void 0!==t.d){g=new o({curve:t.crv}),p=g.ecparams.keycharlen,v=("0000000000"+_r(t.x)).slice(-p),m=("0000000000"+_r(t.y)).slice(-p),y="04"+v+m;var x=("0000000000"+_r(t.d)).slice(-p);return g.setPublicKeyHex(y),g.setPrivateKeyHex(x),g}if("pkcs5prv"===r){var S,w=t;i=Br;if(S=n(w,0),9===S.length)f=new u,f.readPKCS5PrvKeyHex(w);else if(6===S.length)f=new h,f.readPKCS5PrvKeyHex(w);else{if(!(S.length>2&&"04"===w.substr(S[1],2)))throw new Error("unsupported PKCS#1/5 hexadecimal key");f=new o,f.readPKCS5PrvKeyHex(w)}return f}if("pkcs8prv"===r){f=l.getKeyFromPlainPrivatePKCS8Hex(t);return f}if("pkcs8pub"===r)return l._getKeyFromPublicPKCS8Hex(t);if("x509pub"===r)return ji.getPublicKeyFromCertHex(t);if(-1!=t.indexOf("-END CERTIFICATE-",0)||-1!=t.indexOf("-END X509 CERTIFICATE-",0)||-1!=t.indexOf("-END TRUSTED CERTIFICATE-",0))return ji.getPublicKeyFromCertPEM(t);if(-1!=t.indexOf("-END PUBLIC KEY-")){var E=ri(t,"PUBLIC KEY");return l._getKeyFromPublicPKCS8Hex(E)}if(-1!=t.indexOf("-END RSA PRIVATE KEY-")&&-1==t.indexOf("4,ENCRYPTED")){var F=c(t,"RSA PRIVATE KEY");return l.getKey(F,null,"pkcs5prv")}if(-1!=t.indexOf("-END DSA PRIVATE KEY-")&&-1==t.indexOf("4,ENCRYPTED")){var b=c(t,"DSA PRIVATE KEY"),A=s(b,0,[1],"02"),D=s(b,0,[2],"02"),I=s(b,0,[3],"02"),C=s(b,0,[4],"02"),P=s(b,0,[5],"02");f=new h;return f.setPrivate(new d(A,16),new d(D,16),new d(I,16),new d(C,16),new d(P,16)),f}if(-1!=t.indexOf("-END EC PRIVATE KEY-")&&-1==t.indexOf("4,ENCRYPTED")){F=c(t,"EC PRIVATE KEY");return l.getKey(F,null,"pkcs5prv")}if(-1!=t.indexOf("-END PRIVATE KEY-"))return l.getKeyFromPlainPrivatePKCS8PEM(t);if(-1!=t.indexOf("-END RSA PRIVATE KEY-")&&-1!=t.indexOf("4,ENCRYPTED")){var R=l.getDecryptedKeyHex(t,e),T=new qe;return T.readPKCS5PrvKeyHex(R),T}if(-1!=t.indexOf("-END EC PRIVATE KEY-")&&-1!=t.indexOf("4,ENCRYPTED")){b=l.getDecryptedKeyHex(t,e),f=s(b,0,[1],"04");var B=s(b,0,[2,0],"06"),N=s(b,0,[3,0],"03").substr(2),H="";if(void 0===Pr.crypto.OID.oidhex2name[B])throw new Error("undefined OID(hex) in KJUR.crypto.OID: "+B);H=Pr.crypto.OID.oidhex2name[B];g=new o({curve:H});return g.setPublicKeyHex(N),g.setPrivateKeyHex(f),g.isPublic=!1,g}if(-1!=t.indexOf("-END DSA PRIVATE KEY-")&&-1!=t.indexOf("4,ENCRYPTED")){b=l.getDecryptedKeyHex(t,e),A=s(b,0,[1],"02"),D=s(b,0,[2],"02"),I=s(b,0,[3],"02"),C=s(b,0,[4],"02"),P=s(b,0,[5],"02"),f=new h;return f.setPrivate(new d(A,16),new d(D,16),new d(I,16),new d(C,16),new d(P,16)),f}if(-1!=t.indexOf("-END ENCRYPTED PRIVATE KEY-"))return l.getKeyFromEncryptedPKCS8PEM(t,e);throw new Error("not supported argument")},Bi.generateKeypair=function(t,e){if("RSA"==t){var r=e,i=new qe;i.generate(r,"10001"),i.isPrivate=!0,i.isPublic=!0;var n=new qe,s=i.n.toString(16),a=i.e.toString(16);n.setPublic(s,a),n.isPrivate=!1,n.isPublic=!0;var o={};return o.prvKeyObj=i,o.pubKeyObj=n,o}if("EC"==t){var h=e,u=new Pr.crypto.ECDSA({curve:h}),c=u.generateKeyPairHex();i=new Pr.crypto.ECDSA({curve:h});i.setPublicKeyHex(c.ecpubhex),i.setPrivateKeyHex(c.ecprvhex),i.isPrivate=!0,i.isPublic=!1;n=new Pr.crypto.ECDSA({curve:h});n.setPublicKeyHex(c.ecpubhex),n.isPrivate=!1,n.isPublic=!0;o={};return o.prvKeyObj=i,o.pubKeyObj=n,o}throw new Error("unknown algorithm: "+t)},Bi.getPEM=function(t,e,r,i,n,s){var o=Pr,h=o.asn1,u=h.DERObjectIdentifier,c=h.DERInteger,l=h.ASN1Util.newObject,f=h.x509,g=f.SubjectPublicKeyInfo,p=o.crypto,d=p.DSA,v=p.ECDSA,m=qe;function y(t){var e=l({seq:[{int:0},{int:{bigint:t.n}},{int:t.e},{int:{bigint:t.d}},{int:{bigint:t.p}},{int:{bigint:t.q}},{int:{bigint:t.dmp1}},{int:{bigint:t.dmq1}},{int:{bigint:t.coeff}}]});return e}function x(t){var e=l({seq:[{int:1},{octstr:{hex:t.prvKeyHex}},{tag:["a0",!0,{oid:{name:t.curveName}}]},{tag:["a1",!0,{bitstr:{hex:"00"+t.pubKeyHex}}]}]});return e}function S(t){var e=l({seq:[{int:0},{int:{bigint:t.p}},{int:{bigint:t.q}},{int:{bigint:t.g}},{int:{bigint:t.y}},{int:{bigint:t.x}}]});return e}if((void 0!==m&&t instanceof m||void 0!==d&&t instanceof d||void 0!==v&&t instanceof v)&&1==t.isPublic&&(void 0===e||"PKCS8PUB"==e)){var w=new g(t),E=w.tohex();return ei(E,"PUBLIC KEY")}if("PKCS1PRV"==e&&void 0!==m&&t instanceof m&&(void 0===r||null==r)&&1==t.isPrivate){w=y(t),E=w.tohex();return ei(E,"RSA PRIVATE KEY")}if("PKCS1PRV"==e&&void 0!==v&&t instanceof v&&(void 0===r||null==r)&&1==t.isPrivate){var F=new u({name:t.curveName}),b=F.tohex(),A=x(t),D=A.tohex(),I="";return I+=ei(b,"EC PARAMETERS"),I+=ei(D,"EC PRIVATE KEY"),I}if("PKCS1PRV"==e&&void 0!==d&&t instanceof d&&(void 0===r||null==r)&&1==t.isPrivate){w=S(t),E=w.tohex();return ei(E,"DSA PRIVATE KEY")}if("PKCS5PRV"==e&&void 0!==m&&t instanceof m&&void 0!==r&&null!=r&&1==t.isPrivate){w=y(t),E=w.tohex();return void 0===i&&(i="DES-EDE3-CBC"),this.getEncryptedPKCS5PEMFromPrvKeyHex("RSA",E,r,i,s)}if("PKCS5PRV"==e&&void 0!==v&&t instanceof v&&void 0!==r&&null!=r&&1==t.isPrivate){w=x(t),E=w.tohex();return void 0===i&&(i="DES-EDE3-CBC"),this.getEncryptedPKCS5PEMFromPrvKeyHex("EC",E,r,i,s)}if("PKCS5PRV"==e&&void 0!==d&&t instanceof d&&void 0!==r&&null!=r&&1==t.isPrivate){w=S(t),E=w.tohex();return void 0===i&&(i="DES-EDE3-CBC"),this.getEncryptedPKCS5PEMFromPrvKeyHex("DSA",E,r,i,s)}var C=function(t,e){var r=P(t,e),i=new l({seq:[{seq:[{oid:{name:"pkcs5PBES2"}},{seq:[{seq:[{oid:{name:"pkcs5PBKDF2"}},{seq:[{octstr:{hex:r.pbkdf2Salt}},{int:r.pbkdf2Iter}]}]},{seq:[{oid:{name:"des-EDE3-CBC"}},{octstr:{hex:r.encryptionSchemeIV}}]}]}]},{octstr:{hex:r.ciphertext}}]});return i.tohex()},P=function(t,e){var r=100,i=a.lib.WordArray.random(8),n="DES-EDE3-CBC",s=a.lib.WordArray.random(8),o=a.PBKDF2(e,i,{keySize:6,iterations:r}),h=a.enc.Hex.parse(t),u=a.TripleDES.encrypt(h,o,{iv:s})+"",c={};return c.ciphertext=u,c.pbkdf2Salt=a.enc.Hex.stringify(i),c.pbkdf2Iter=r,c.encryptionSchemeAlg=n,c.encryptionSchemeIV=a.enc.Hex.stringify(s),c};if("PKCS8PRV"==e&&void 0!=m&&t instanceof m&&1==t.isPrivate){var R=y(t),T=R.tohex();w=l({seq:[{int:0},{seq:[{oid:{name:"rsaEncryption"}},{null:!0}]},{octstr:{hex:T}}]}),E=w.tohex();if(void 0===r||null==r)return ei(E,"PRIVATE KEY");D=C(E,r);return ei(D,"ENCRYPTED PRIVATE KEY")}if("PKCS8PRV"==e&&void 0!==v&&t instanceof v&&1==t.isPrivate){var B={seq:[{int:1},{octstr:{hex:t.prvKeyHex}}]};"string"==typeof t.pubKeyHex&&B.seq.push({tag:["a1",!0,{bitstr:{hex:"00"+t.pubKeyHex}}]});R=new l(B),T=R.tohex(),w=l({seq:[{int:0},{seq:[{oid:{name:"ecPublicKey"}},{oid:{name:t.curveName}}]},{octstr:{hex:T}}]}),E=w.tohex();if(void 0===r||null==r)return ei(E,"PRIVATE KEY");D=C(E,r);return ei(D,"ENCRYPTED PRIVATE KEY")}if("PKCS8PRV"==e&&void 0!==d&&t instanceof d&&1==t.isPrivate){R=new c({bigint:t.x}),T=R.tohex(),w=l({seq:[{int:0},{seq:[{oid:{name:"dsa"}},{seq:[{int:{bigint:t.p}},{int:{bigint:t.q}},{int:{bigint:t.g}}]}]},{octstr:{hex:T}}]}),E=w.tohex();if(void 0===r||null==r)return ei(E,"PRIVATE KEY");D=C(E,r);return ei(D,"ENCRYPTED PRIVATE KEY")}throw new Error("unsupported object nor format")},Bi.getKeyFromCSRPEM=function(t){var e=ri(t,"CERTIFICATE REQUEST"),r=Bi.getKeyFromCSRHex(e);return r},Bi.getKeyFromCSRHex=function(t){var e=Bi.parseCSRHex(t),r=Bi.getKey(e.p8pubkeyhex,null,"pkcs8pub");return r},Bi.parseCSRHex=function(t){var e=Br,r=e.getChildIdx,i=e.getTLV,n={},s=t;if("30"!=s.substr(0,2))throw new Error("malformed CSR(code:001)");var a=r(s,0);if(a.length<1)throw new Error("malformed CSR(code:002)");if("30"!=s.substr(a[0],2))throw new Error("malformed CSR(code:003)");var o=r(s,a[0]);if(o.length<3)throw new Error("malformed CSR(code:004)");return n.p8pubkeyhex=i(s,o[2]),n},Bi.getKeyID=function(t){var e=Bi,r=Br;"string"===typeof t&&-1!=t.indexOf("BEGIN ")&&(t=e.getKey(t));var i=ri(e.getPEM(t)),n=r.getIdxbyList(i,0,[1]),s=r.getV(i,n).substring(2);return Pr.crypto.Util.hashHex(s,"sha1")},Bi.getJWK=function(t,e,r,n,s){var a,o,h={},u=Pr.crypto.Util.hashHex;if("string"==typeof t)a=Bi.getKey(t),-1!=t.indexOf("CERTIFICATE")&&(o=ri(t));else{if("object"!=i(t))throw new Error("unsupported keyinfo type");t instanceof ji?(a=t.getPublicKey(),o=t.hex):a=t}if(a instanceof qe&&a.isPrivate)h.kty="RSA",h.n=Mr(a.n.toString(16)),h.e=Mr(a.e.toString(16)),h.d=Mr(a.d.toString(16)),h.p=Mr(a.p.toString(16)),h.q=Mr(a.q.toString(16)),h.dp=Mr(a.dmp1.toString(16)),h.dq=Mr(a.dmq1.toString(16)),h.qi=Mr(a.coeff.toString(16));else if(a instanceof qe&&a.isPublic)h.kty="RSA",h.n=Mr(a.n.toString(16)),h.e=Mr(a.e.toString(16));else if(a instanceof Pr.crypto.ECDSA&&a.isPrivate){var l=a.getShortNISTPCurveName();if("P-256"!==l&&"P-384"!==l&&"P-521"!==l)throw new Error("unsupported curve name for JWT: "+l);var f=a.getPublicKeyXYHex();h.kty="EC",h.crv=l,h.x=Mr(f.x),h.y=Mr(f.y),h.d=Mr(a.prvKeyHex)}else if(a instanceof Pr.crypto.ECDSA&&a.isPublic){l=a.getShortNISTPCurveName();if("P-256"!==l&&"P-384"!==l&&"P-521"!==l)throw new Error("unsupported curve name for JWT: "+l);f=a.getPublicKeyXYHex();h.kty="EC",h.crv=l,h.x=Mr(f.x),h.y=Mr(f.y)}if(void 0==h.kty)throw new Error("unsupported keyinfo");return a.isPrivate||1==e||(h.kid=Pr.jws.JWS.getJWKthumbprint(h)),void 0!=o&&1!=r&&(h.x5c=[c(o)]),void 0!=o&&1!=n&&(h.x5t=kr(c(u(o,"sha1")))),void 0!=o&&1!=s&&(h["x5t#S256"]=kr(c(u(o,"sha256")))),h},Bi.getJWKFromKey=function(t){return Bi.getJWK(t,!0,!0,!0,!0)},qe.getPosArrayOfChildrenFromHex=function(t){return Br.getChildIdx(t,0)},qe.getHexValueArrayOfChildrenFromHex=function(t){var e=Br,r=e.getV,i=qe.getPosArrayOfChildrenFromHex(t),n=r(t,i[0]),s=r(t,i[1]),a=r(t,i[2]),o=r(t,i[3]),h=r(t,i[4]),u=r(t,i[5]),c=r(t,i[6]),l=r(t,i[7]),f=r(t,i[8]);i=new Array;return i.push(n,s,a,o,h,u,c,l,f),i},qe.prototype.readPrivateKeyFromPEMString=function(t){var e=ri(t),r=qe.getHexValueArrayOfChildrenFromHex(e);this.setPrivateEx(r[1],r[2],r[3],r[4],r[5],r[6],r[7],r[8])},qe.prototype.readPKCS5PrvKeyHex=function(t){var e=qe.getHexValueArrayOfChildrenFromHex(t);this.setPrivateEx(e[1],e[2],e[3],e[4],e[5],e[6],e[7],e[8])},qe.prototype.readPKCS8PrvKeyHex=function(t){var e,r,i,n,s,a,o,h,u=Br,c=u.getVbyListEx;if(!1===u.isASN1HEX(t))throw new Error("not ASN.1 hex string");try{e=c(t,0,[2,0,1],"02"),r=c(t,0,[2,0,2],"02"),i=c(t,0,[2,0,3],"02"),n=c(t,0,[2,0,4],"02"),s=c(t,0,[2,0,5],"02"),a=c(t,0,[2,0,6],"02"),o=c(t,0,[2,0,7],"02"),h=c(t,0,[2,0,8],"02")}catch(l){throw new Error("malformed PKCS#8 plain RSA private key")}this.setPrivateEx(e,r,i,n,s,a,o,h)},qe.prototype.readPKCS5PubKeyHex=function(t){var e=Br,r=e.getV;if(!1===e.isASN1HEX(t))throw new Error("keyHex is not ASN.1 hex string");var i=e.getChildIdx(t,0);if(2!==i.length||"02"!==t.substr(i[0],2)||"02"!==t.substr(i[1],2))throw new Error("wrong hex for PKCS#5 public key");var n=r(t,i[0]),s=r(t,i[1]);this.setPublic(n,s)},qe.prototype.readPKCS8PubKeyHex=function(t){var e=Br;if(!1===e.isASN1HEX(t))throw new Error("not ASN.1 hex string");if("06092a864886f70d010101"!==e.getTLVbyListEx(t,0,[0,0]))throw new Error("not PKCS8 RSA public key");var r=e.getTLVbyListEx(t,0,[1,0]);this.readPKCS5PubKeyHex(r)},qe.prototype.readCertPubKeyHex=function(t,e){var r,i;r=new ji,r.readCertHex(t),i=r.getPublicKeyHex(),this.readPKCS8PubKeyHex(i)};function Ni(t,e){for(var r="",i=e/4-t.length,n=0;n<i;n++)r+="0";return r+t}function Hi(t,e,r){var i="",n=0;while(i.length<e)i+=$r(r(Yr(t+String.fromCharCode.apply(String,[(4278190080&n)>>24,(16711680&n)>>16,(65280&n)>>8,255&n])))),n+=1;return i}function Oi(t){for(var e in Pr.crypto.Util.DIGESTINFOHEAD){var r=Pr.crypto.Util.DIGESTINFOHEAD[e],i=r.length;if(t.substring(0,i)==r){var n=[e,t.substring(i)];return n}}return[]}function ji(t){var e,r=Br,n=r.getChildIdx,s=r.getV,a=(r.dump,r.parse),o=r.getTLV,h=r.getVbyList,u=r.getVbyListEx,c=r.getTLVbyList,l=r.getTLVbyListEx,f=r.getIdxbyList,g=r.getIdxbyListEx,p=r.getVidx,d=r.getInt,v=r.oidname,m=r.hextooidstr,y=ri;try{e=Pr.asn1.x509.AlgorithmIdentifier.PSSNAME2ASN1TLV}catch(E){}this.HEX2STAG={"0c":"utf8",13:"prn",16:"ia5","1a":"vis","1e":"bmp"},this.hex=null,this.version=0,this.foffset=0,this.aExtInfo=null,this.getVersion=function(){if(null===this.hex||0!==this.version)return this.version;var t=c(this.hex,0,[0,0]);if("a0"==t.substr(0,2)){var e=c(t,0,[0]),r=d(e,0);if(r<0||2<r)throw new Error("malformed version field");return this.version=r+1,this.version}return this.version=1,this.foffset=-1,1},this.getSerialNumberHex=function(){return u(this.hex,0,[0,0],"02")},this.getSignatureAlgorithmField=function(){var t=l(this.hex,0,[0,1]);return this.getAlgorithmIdentifierName(t)},this.getAlgorithmIdentifierName=function(t){for(var r in e)if(t===e[r])return r;return v(u(t,0,[0],"06"))},this.getIssuer=function(){return this.getX500Name(this.getIssuerHex())},this.getIssuerHex=function(){return c(this.hex,0,[0,3+this.foffset],"30")},this.getIssuerString=function(){var t=this.getIssuer();return t.str},this.getSubject=function(){return this.getX500Name(this.getSubjectHex())},this.getSubjectHex=function(){return c(this.hex,0,[0,5+this.foffset],"30")},this.getSubjectString=function(){var t=this.getSubject();return t.str},this.getNotBefore=function(){var t=h(this.hex,0,[0,4+this.foffset,0]);return t=t.replace(/(..)/g,"%$1"),t=decodeURIComponent(t),t},this.getNotAfter=function(){var t=h(this.hex,0,[0,4+this.foffset,1]);return t=t.replace(/(..)/g,"%$1"),t=decodeURIComponent(t),t},this.getPublicKeyHex=function(){return this.getSPKI()},this.getSPKI=function(){return c(this.hex,0,[0,6+this.foffset],"30")},this.getSPKIValue=function(){var t=this.getSPKI();return null==t?null:h(t,0,[1],"03",!0)},this.getPublicKeyIdx=function(){return f(this.hex,0,[0,6+this.foffset],"30")},this.getPublicKeyContentIdx=function(){var t=this.getPublicKeyIdx();return f(this.hex,t,[1,0],"30")},this.getPublicKey=function(){return Bi.getKey(this.getPublicKeyHex(),null,"pkcs8pub")},this.getSignatureAlgorithmName=function(){var t=c(this.hex,0,[1],"30");return this.getAlgorithmIdentifierName(t)},this.getSignatureValueHex=function(){return h(this.hex,0,[2],"03",!0)},this.verifySignature=function(t){var e=this.getSignatureAlgorithmField(),r=this.getSignatureValueHex(),i=c(this.hex,0,[0],"30"),n=new Pr.crypto.Signature({alg:e});return n.init(t),n.updateHex(i),n.verify(r)},this.parseExt=function(t){var e,i,a;if(void 0===t){if(a=this.hex,3!==this.version)return-1;e=f(a,0,[0,7,0],"30"),i=n(a,e)}else{a=ri(t);var o=f(a,0,[0,3,0,0],"06");if("2a864886f70d01090e"!=s(a,o))return void(this.aExtInfo=new Array);e=f(a,0,[0,3,0,1,0],"30"),i=n(a,e),this.hex=a}this.aExtInfo=new Array;for(var u=0;u<i.length;u++){var c={critical:!1},l=n(a,i[u]),g=0;3===l.length&&(c.critical=!0,g=1),c.oid=r.hextooidstr(h(a,i[u],[0],"06"));var d=f(a,i[u],[1+g]);c.vidx=p(a,d),this.aExtInfo.push(c)}},this.getExtInfo=function(t){var e=this.aExtInfo,r=t;if(t.match(/^[0-9.]+$/)||(r=Pr.asn1.x509.OID.name2oid(t)),""!==r)for(var i=0;i<e.length;i++)if(e[i].oid===r)return e[i]},this.getExtBasicConstraints=function(t,e){if(void 0===t&&void 0===e){var r=this.getExtInfo("basicConstraints");if(void 0===r)return;t=o(this.hex,r.vidx),e=r.critical}var i={extname:"basicConstraints"};if(e&&(i.critical=!0),"3000"===t)return i;if("30030101ff"===t)return i.cA=!0,i;if("30060101ff02"===t.substr(0,12)){var n=s(t,10),a=parseInt(n,16);return i.cA=!0,i.pathLen=a,i}throw new Error("hExtV parse error: "+t)},this.getExtNameConstraints=function(t,e){if(void 0===t&&void 0===e){var r=this.getExtInfo("nameConstraints");if(void 0===r)return;t=o(this.hex,r.vidx),e=r.critical}var i={extname:"nameConstraints"};e&&(i.critical=!0);for(var s=n(t,0),a=0;a<s.length;a++){for(var h=[],u=n(t,s[a]),c=0;c<u.length;c++){var l=o(t,u[c]),f=this.getGeneralSubtree(l);h.push(f)}var g=t.substr(s[a],2);"a0"==g?i.permit=h:"a1"==g&&(i.exclude=h)}return i},this.getGeneralSubtree=function(t){var e=n(t,0),r=e.length;if(r<1||2<r)throw new Error("wrong num elements");for(var i=this.getGeneralName(o(t,e[0])),a=1;a<r;a++){var h=t.substr(e[a],2),u=s(t,e[a]),c=parseInt(u,16);"80"==h&&(i.min=c),"81"==h&&(i.max=c)}return i},this.getExtKeyUsage=function(t,e){if(void 0===t&&void 0===e){var r=this.getExtInfo("keyUsage");if(void 0===r)return;t=o(this.hex,r.vidx),e=r.critical}var i={extname:"keyUsage"};return e&&(i.critical=!0),i.names=this.getExtKeyUsageString(t).split(","),i},this.getExtKeyUsageBin=function(t){if(void 0===t){var e=this.getExtInfo("keyUsage");if(void 0===e)return"";t=o(this.hex,e.vidx)}if(8!=t.length&&10!=t.length)throw new Error("malformed key usage value: "+t);var r="000000000000000"+parseInt(t.substr(6),16).toString(2);return 8==t.length&&(r=r.slice(-8)),10==t.length&&(r=r.slice(-16)),r=r.replace(/0+$/,""),""==r&&(r="0"),r},this.getExtKeyUsageString=function(t){for(var e=this.getExtKeyUsageBin(t),r=new Array,i=0;i<e.length;i++)"1"==e.substr(i,1)&&r.push(ji.KEYUSAGE_NAME[i]);return r.join(",")},this.getExtSubjectKeyIdentifier=function(t,e){if(void 0===t&&void 0===e){var r=this.getExtInfo("subjectKeyIdentifier");if(void 0===r)return;t=o(this.hex,r.vidx),e=r.critical}var i={extname:"subjectKeyIdentifier"};e&&(i.critical=!0);var n=s(t,0);return i.kid={hex:n},i},this.getExtAuthorityKeyIdentifier=function(t,e){if(void 0===t&&void 0===e){var r=this.getExtInfo("authorityKeyIdentifier");if(void 0===r)return;t=o(this.hex,r.vidx),e=r.critical}var i={extname:"authorityKeyIdentifier"};e&&(i.critical=!0);for(var a=n(t,0),h=0;h<a.length;h++){var u=t.substr(a[h],2);if("80"===u&&(i.kid={hex:s(t,a[h])}),"a1"===u){var c=o(t,a[h]),l=this.getGeneralNames(c);i.issuer=l[0]["dn"]}"82"===u&&(i.sn={hex:s(t,a[h])})}return i},this.getExtExtKeyUsage=function(t,e){if(void 0===t&&void 0===e){var r=this.getExtInfo("extKeyUsage");if(void 0===r)return;t=o(this.hex,r.vidx),e=r.critical}var i={extname:"extKeyUsage",array:[]};e&&(i.critical=!0);for(var a=n(t,0),h=0;h<a.length;h++)i.array.push(v(s(t,a[h])));return i},this.getExtExtKeyUsageName=function(){var t=this.getExtInfo("extKeyUsage");if(void 0===t)return t;var e=new Array,r=o(this.hex,t.vidx);if(""===r)return e;for(var i=n(r,0),a=0;a<i.length;a++)e.push(v(s(r,i[a])));return e},this.getExtSubjectAltName=function(t,e){if(void 0===t&&void 0===e){var r=this.getExtInfo("subjectAltName");if(void 0===r)return;t=o(this.hex,r.vidx),e=r.critical}var i={extname:"subjectAltName",array:[]};return e&&(i.critical=!0),i.array=this.getGeneralNames(t),i},this.getExtIssuerAltName=function(t,e){if(void 0===t&&void 0===e){var r=this.getExtInfo("issuerAltName");if(void 0===r)return;t=o(this.hex,r.vidx),e=r.critical}var i={extname:"issuerAltName",array:[]};return e&&(i.critical=!0),i.array=this.getGeneralNames(t),i},this.getGeneralNames=function(t){for(var e=n(t,0),r=[],i=0;i<e.length;i++){var s=this.getGeneralName(o(t,e[i]));void 0!==s&&r.push(s)}return r},this.getGeneralName=function(t){var e=t.substr(0,2),r=s(t,0),i=$r(r);return"81"==e?{rfc822:i}:"82"==e?{dns:i}:"86"==e?{uri:i}:"87"==e?{ip:gi(r)}:"a4"==e?{dn:this.getX500Name(r)}:"a0"==e?{other:this.getOtherName(t)}:void 0},this.getExtSubjectAltName2=function(){var t,e,r,i=this.getExtInfo("subjectAltName");if(void 0===i)return i;for(var a=new Array,h=o(this.hex,i.vidx),u=n(h,0),c=0;c<u.length;c++)r=h.substr(u[c],2),t=s(h,u[c]),"81"===r&&(e=Wr(t),a.push(["MAIL",e])),"82"===r&&(e=Wr(t),a.push(["DNS",e])),"84"===r&&(e=ji.hex2dn(t,0),a.push(["DN",e])),"86"===r&&(e=Wr(t),a.push(["URI",e])),"87"===r&&(e=gi(t),a.push(["IP",e]));return a},this.getExtCRLDistributionPoints=function(t,e){if(void 0===t&&void 0===e){var r=this.getExtInfo("cRLDistributionPoints");if(void 0===r)return;t=o(this.hex,r.vidx),e=r.critical}var i={extname:"cRLDistributionPoints",array:[]};e&&(i.critical=!0);for(var s=n(t,0),a=0;a<s.length;a++){var h=o(t,s[a]);i.array.push(this.getDistributionPoint(h))}return i},this.getDistributionPoint=function(t){for(var e={},r=n(t,0),i=0;i<r.length;i++){var s=t.substr(r[i],2),a=o(t,r[i]);"a0"==s&&(e.dpname=this.getDistributionPointName(a))}return e},this.getDistributionPointName=function(t){for(var e={},r=n(t,0),i=0;i<r.length;i++){var s=t.substr(r[i],2),a=o(t,r[i]);"a0"==s&&(e.full=this.getGeneralNames(a))}return e},this.getExtCRLDistributionPointsURI=function(){var t=this.getExtCRLDistributionPoints();if(void 0==t)return t;for(var e=t.array,r=[],i=0;i<e.length;i++)try{void 0!=e[i].dpname.full[0].uri&&r.push(e[i].dpname.full[0].uri)}catch(n){}return r},this.getExtAIAInfo=function(){var t=this.getExtInfo("authorityInfoAccess");if(void 0===t)return t;for(var e={ocsp:[],caissuer:[]},r=n(this.hex,t.vidx),i=0;i<r.length;i++){var s=h(this.hex,r[i],[0],"06"),a=h(this.hex,r[i],[1],"86");"2b06010505073001"===s&&e.ocsp.push(Wr(a)),"2b06010505073002"===s&&e.caissuer.push(Wr(a))}return e},this.getExtAuthorityInfoAccess=function(t,e){if(void 0===t&&void 0===e){var r=this.getExtInfo("authorityInfoAccess");if(void 0===r)return;t=o(this.hex,r.vidx),e=r.critical}var i={extname:"authorityInfoAccess",array:[]};e&&(i.critical=!0);for(var s=n(t,0),a=0;a<s.length;a++){var c=u(t,s[a],[0],"06"),l=h(t,s[a],[1],"86"),f=Wr(l);if("2b06010505073001"==c)i.array.push({ocsp:f});else{if("2b06010505073002"!=c)throw new Error("unknown method: "+c);i.array.push({caissuer:f})}}return i},this.getExtCertificatePolicies=function(t,e){if(void 0===t&&void 0===e){var r=this.getExtInfo("certificatePolicies");if(void 0===r)return;t=o(this.hex,r.vidx),e=r.critical}var i={extname:"certificatePolicies",array:[]};e&&(i.critical=!0);for(var s=n(t,0),a=0;a<s.length;a++){var h=o(t,s[a]),u=this.getPolicyInformation(h);i.array.push(u)}return i},this.getPolicyInformation=function(t){var e={},r=h(t,0,[0],"06");e.policyoid=v(r);var i=g(t,0,[1],"30");if(-1!=i){e.array=[];for(var s=n(t,i),a=0;a<s.length;a++){var u=o(t,s[a]),c=this.getPolicyQualifierInfo(u);e.array.push(c)}}return e},this.getOtherName=function(t){var e={},r=n(t,0),i=h(t,r[0],[],"06"),s=h(t,r[1],[]);return e.oid=Pr.asn1.ASN1Util.oidHexToInt(i),e.obj=a(s),e},this.getPolicyQualifierInfo=function(t){var e={},r=h(t,0,[0],"06");if("2b06010505070201"===r){var i=u(t,0,[1],"16");e.cps=$r(i)}else if("2b06010505070202"===r){var n=c(t,0,[1],"30");e.unotice=this.getUserNotice(n)}return e},this.getUserNotice=function(t){for(var e={},r=n(t,0),i=0;i<r.length;i++){var s=o(t,r[i]);"30"!=s.substr(0,2)&&(e.exptext=this.getDisplayText(s))}return e},this.getDisplayText=function(t){var e={"0c":"utf8",16:"ia5","1a":"vis","1e":"bmp"},r={};return r.type=e[t.substr(0,2)],r.str=$r(s(t,0)),r},this.getExtCRLNumber=function(t,e){var r={extname:"cRLNumber"};if(e&&(r.critical=!0),"02"==t.substr(0,2))return r.num={hex:s(t,0)},r;throw new Error("hExtV parse error: "+t)},this.getExtCRLReason=function(t,e){var r={extname:"cRLReason"};if(e&&(r.critical=!0),"0a"==t.substr(0,2))return r.code=parseInt(s(t,0),16),r;throw new Error("hExtV parse error: "+t)},this.getExtOcspNonce=function(t,e){var r={extname:"ocspNonce"};e&&(r.critical=!0);var i=s(t,0);return r.hex=i,r},this.getExtOcspNoCheck=function(t,e){var r={extname:"ocspNoCheck"};return e&&(r.critical=!0),r},this.getExtAdobeTimeStamp=function(t,e){if(void 0===t&&void 0===e){var r=this.getExtInfo("adobeTimeStamp");if(void 0===r)return;t=o(this.hex,r.vidx),e=r.critical}var i={extname:"adobeTimeStamp"};e&&(i.critical=!0);var s=n(t,0);if(s.length>1){var a=o(t,s[1]),h=this.getGeneralName(a);void 0!=h.uri&&(i.uri=h.uri)}if(s.length>2){var u=o(t,s[2]);"0101ff"==u&&(i.reqauth=!0),"010100"==u&&(i.reqauth=!1)}return i};var x=function(t){var e={};try{var r=t.seq[0].oid,i=Pr.asn1.x509.OID.name2oid(r);e.type=Pr.asn1.x509.OID.oid2atype(i);var n=t.seq[1];if(void 0!=n.utf8str)e.ds="utf8",e.value=n.utf8str.str;else if(void 0!=n.numstr)e.ds="num",e.value=n.numstr.str;else if(void 0!=n.telstr)e.ds="tel",e.value=n.telstr.str;else if(void 0!=n.prnstr)e.ds="prn",e.value=n.prnstr.str;else if(void 0!=n.ia5str)e.ds="ia5",e.value=n.ia5str.str;else if(void 0!=n.visstr)e.ds="vis",e.value=n.visstr.str;else{if(void 0==n.bmpstr)throw"error";e.ds="bmp",e.value=n.bmpstr.str}return e}catch(s){throw new Erorr("improper ASN.1 parsed AttrTypeAndValue")}},S=function(t){try{return t.set.map((function(t){return x(t)}))}catch(e){throw new Error("improper ASN.1 parsed RDN: "+e)}},w=function(t){try{return t.seq.map((function(t){return S(t)}))}catch(e){throw new Error("improper ASN.1 parsed X500Name: "+e)}};this.getX500NameRule=function(t){for(var e=null,r=[],i=0;i<t.length;i++)for(var n=t[i],s=0;s<n.length;s++)r.push(n[s]);for(i=0;i<r.length;i++){var a=r[i],o=a.ds,h=a.value,u=a.type;if(":"+o,"prn"!=o&&"utf8"!=o&&"ia5"!=o)return"mixed";if("ia5"==o){if("CN"!=u)return"mixed";if(Pr.lang.String.isMail(h))continue;return"mixed"}if("C"==u){if("prn"==o)continue;return"mixed"}if(":"+o,null==e)e=o;else if(e!==o)return"mixed"}return null==e?"prn":e},this.getAttrTypeAndValue=function(t){var e=a(t);return x(e)},this.getRDN=function(t){var e=a(t);return S(e)},this.getX500NameArray=function(t){var e=a(t);return w(e)},this.getX500Name=function(t){var e=this.getX500NameArray(t),r=this.dnarraytostr(e);return{array:e,str:r}},this.readCertPEM=function(t){this.readCertHex(y(t))},this.readCertHex=function(t){this.hex=t,this.getVersion();try{f(this.hex,0,[0,7],"a3"),this.parseExt()}catch(Ne){}},this.getParam=function(t){var e={};return e.version=this.getVersion(),e.serial={hex:this.getSerialNumberHex()},e.sigalg=this.getSignatureAlgorithmField(),e.issuer=this.getIssuer(),e.notbefore=this.getNotBefore(),e.notafter=this.getNotAfter(),e.subject=this.getSubject(),e.sbjpubkey=ei(this.getPublicKeyHex(),"PUBLIC KEY"),this.aExtInfo.length>0&&(e.ext=this.getExtParamArray()),e.sighex=this.getSignatureValueHex(),"object"==i(t)&&(1==t.tbshex&&(e.tbshex=c(this.hex,0,[0])),1==t.nodnarray&&(delete e.issuer.array,delete e.subject.array)),e},this.getExtParamArray=function(t){if(void 0==t){var e=g(this.hex,0,[0,"[3]"]);-1!=e&&(t=l(this.hex,0,[0,"[3]",0],"30"))}for(var r=[],i=n(t,0),s=0;s<i.length;s++){var a=o(t,i[s]),h=this.getExtParam(a);null!=h&&r.push(h)}return r},this.getExtParam=function(t){var e=n(t,0),r=e.length;if(2!=r&&3!=r)throw new Error("wrong number elements in Extension: "+r+" "+t);var i=m(h(t,0,[0],"06")),s=!1;3==r&&"0101ff"==c(t,0,[1])&&(s=!0);var a=c(t,0,[r-1,0]),o=void 0;if("*********"==i?o=this.getExtSubjectKeyIdentifier(a,s):"*********"==i?o=this.getExtKeyUsage(a,s):"*********"==i?o=this.getExtSubjectAltName(a,s):"*********"==i?o=this.getExtIssuerAltName(a,s):"*********"==i?o=this.getExtBasicConstraints(a,s):"*********"==i?o=this.getExtNameConstraints(a,s):"*********"==i?o=this.getExtCRLDistributionPoints(a,s):"*********"==i?o=this.getExtCertificatePolicies(a,s):"*********"==i?o=this.getExtAuthorityKeyIdentifier(a,s):"*********"==i?o=this.getExtExtKeyUsage(a,s):"*******.*******.1"==i?o=this.getExtAuthorityInfoAccess(a,s):"*********"==i?o=this.getExtCRLNumber(a,s):"*********"==i?o=this.getExtCRLReason(a,s):"*******.********.1.2"==i?o=this.getExtOcspNonce(a,s):"*******.********.1.5"==i?o=this.getExtOcspNoCheck(a,s):"1.2.840.113583.*******"==i&&(o=this.getExtAdobeTimeStamp(a,s)),void 0!=o)return o;var u={extname:i,extn:a};return s&&(u.critical=!0),u},this.findExt=function(t,e){for(var r=0;r<t.length;r++)if(t[r].extname==e)return t[r];return null},this.updateExtCDPFullURI=function(t,e){var r=this.findExt(t,"cRLDistributionPoints");if(null!=r&&void 0!=r.array)for(var i=r.array,n=0;n<i.length;n++)if(void 0!=i[n].dpname&&void 0!=i[n].dpname.full)for(var s=i[n].dpname.full,a=0;a<s.length;a++){var o=s[n];void 0!=o.uri&&(o.uri=e)}},this.updateExtAIAOCSP=function(t,e){var r=this.findExt(t,"authorityInfoAccess");if(null!=r&&void 0!=r.array)for(var i=r.array,n=0;n<i.length;n++)void 0!=i[n].ocsp&&(i[n].ocsp=e)},this.updateExtAIACAIssuer=function(t,e){var r=this.findExt(t,"authorityInfoAccess");if(null!=r&&void 0!=r.array)for(var i=r.array,n=0;n<i.length;n++)void 0!=i[n].caissuer&&(i[n].caissuer=e)},this.dnarraytostr=function(t){function e(t){return t.map((function(t){return r(t).replace(/\+/,"\\+")})).join("+")}function r(t){return t.type+"="+t.value}return"/"+t.map((function(t){return e(t).replace(/\//,"\\/")})).join("/")},this.getInfo=function(){var t,e,r,i=function(t){var e=JSON.stringify(t.array).replace(/[\[\]\{\}\"]/g,"");return e},n=function(t){for(var e="",r=t.array,i=0;i<r.length;i++){var n=r[i];if(e+="    policy oid: "+n.policyoid+"\n",void 0!==n.array)for(var s=0;s<n.array.length;s++){var a=n.array[s];void 0!==a.cps&&(e+="    cps: "+a.cps+"\n")}}return e},s=function(t){for(var e="",r=t.array,i=0;i<r.length;i++){var n=r[i];try{void 0!==n.dpname.full[0].uri&&(e+="    "+n.dpname.full[0].uri+"\n")}catch(s){}try{void 0!==n.dname.full[0].dn.hex&&(e+="    "+ji.hex2dn(n.dpname.full[0].dn.hex)+"\n")}catch(s){}}return e},a=function(t){for(var e="",r=t.array,i=0;i<r.length;i++){var n=r[i];void 0!==n.caissuer&&(e+="    caissuer: "+n.caissuer+"\n"),void 0!==n.ocsp&&(e+="    ocsp: "+n.ocsp+"\n")}return e};if(t="Basic Fields\n",t+="  serial number: "+this.getSerialNumberHex()+"\n",t+="  signature algorithm: "+this.getSignatureAlgorithmField()+"\n",t+="  issuer: "+this.getIssuerString()+"\n",t+="  notBefore: "+this.getNotBefore()+"\n",t+="  notAfter: "+this.getNotAfter()+"\n",t+="  subject: "+this.getSubjectString()+"\n",t+="  subject public key info: \n",e=this.getPublicKey(),t+="    key algorithm: "+e.type+"\n","RSA"===e.type&&(t+="    n="+Ei(e.n.toString(16)).substr(0,16)+"...\n",t+="    e="+Ei(e.e.toString(16))+"\n"),r=this.aExtInfo,void 0!==r&&null!==r){t+="X509v3 Extensions:\n";for(var o=0;o<r.length;o++){var h=r[o],u=Pr.asn1.x509.OID.oid2name(h.oid);""===u&&(u=h.oid);var c="";if(!0===h.critical&&(c="CRITICAL"),t+="  "+u+" "+c+":\n","basicConstraints"===u){var l=this.getExtBasicConstraints();void 0===l.cA?t+="    {}\n":(t+="    cA=true",void 0!==l.pathLen&&(t+=", pathLen="+l.pathLen),t+="\n")}else if("keyUsage"===u)t+="    "+this.getExtKeyUsageString()+"\n";else if("subjectKeyIdentifier"===u)t+="    "+this.getExtSubjectKeyIdentifier().kid.hex+"\n";else if("authorityKeyIdentifier"===u){var f=this.getExtAuthorityKeyIdentifier();void 0!==f.kid&&(t+="    kid="+f.kid.hex+"\n")}else if("extKeyUsage"===u){var g=this.getExtExtKeyUsage().array;t+="    "+g.join(", ")+"\n"}else if("subjectAltName"===u){var p=i(this.getExtSubjectAltName());t+="    "+p+"\n"}else if("cRLDistributionPoints"===u){var d=this.getExtCRLDistributionPoints();t+=s(d)}else if("authorityInfoAccess"===u){var v=this.getExtAuthorityInfoAccess();t+=a(v)}else"certificatePolicies"===u&&(t+=n(this.getExtCertificatePolicies()))}}return t+="signature algorithm: "+this.getSignatureAlgorithmName()+"\n",t+="signature: "+this.getSignatureValueHex().substr(0,16)+"...\n",t},"string"==typeof t&&(-1!=t.indexOf("-----BEGIN")?this.readCertPEM(t):Pr.lang.String.isHex(t)&&this.readCertHex(t))}qe.prototype.sign=function(t,e){var r=function(t){return Pr.crypto.Util.hashString(t,e)},i=r(t);return this.signWithMessageHash(i,e)},qe.prototype.signWithMessageHash=function(t,e){var r=Pr.crypto.Util.getPaddedDigestInfoHex(t,e,this.n.bitLength()),i=Ve(r,16),n=this.doPrivate(i),s=n.toString(16);return Ni(s,this.n.bitLength())},qe.prototype.signPSS=function(t,e,r){var i=function(t){return Pr.crypto.Util.hashHex(t,e)},n=i(Yr(t));return void 0===r&&(r=-1),this.signWithMessageHashPSS(n,e,r)},qe.prototype.signWithMessageHashPSS=function(t,e,r){var i,n=$r(t),s=n.length,a=this.n.bitLength()-1,o=Math.ceil(a/8),h=function(t){return Pr.crypto.Util.hashHex(t,e)};if(-1===r||void 0===r)r=s;else if(-2===r)r=o-s-2;else if(r<-2)throw new Error("invalid salt length");if(o<s+r+2)throw new Error("data too long");var u="";r>0&&(u=new Array(r),(new je).nextBytes(u),u=String.fromCharCode.apply(String,u));var c=$r(h(Yr("\0\0\0\0\0\0\0\0"+n+u))),l=[];for(i=0;i<o-r-s-2;i+=1)l[i]=0;var f=String.fromCharCode.apply(String,l)+""+u,g=Hi(c,f.length,h),p=[];for(i=0;i<f.length;i+=1)p[i]=f.charCodeAt(i)^g.charCodeAt(i);var v=65280>>8*o-a&255;for(p[0]&=~v,i=0;i<s;i++)p.push(c.charCodeAt(i));return p.push(188),Ni(this.doPrivate(new d(p)).toString(16),this.n.bitLength())},qe.prototype.verify=function(t,e){if(e=e.toLowerCase(),null==e.match(/^[0-9a-f]+$/))return!1;var r=Ve(e,16),i=this.n.bitLength();if(r.bitLength()>i)return!1;var n=this.doPublic(r),s=n.toString(16);if(s.length+3!=i/4)return!1;var a=s.replace(/^1f+00/,""),o=Oi(a);if(0==o.length)return!1;var h=o[0],u=o[1],c=function(t){return Pr.crypto.Util.hashString(t,h)},l=c(t);return u==l},qe.prototype.verifyWithMessageHash=function(t,e){if(e.length!=Math.ceil(this.n.bitLength()/4))return!1;var r=Ve(e,16);if(r.bitLength()>this.n.bitLength())return 0;var i=this.doPublic(r),n=i.toString(16).replace(/^1f+00/,""),s=Oi(n);if(0==s.length)return!1;s[0];var a=s[1];return a==t},qe.prototype.verifyPSS=function(t,e,r,i){var n=function(t){return Pr.crypto.Util.hashHex(t,r)},s=n(Yr(t));return void 0===i&&(i=-1),this.verifyWithMessageHashPSS(s,e,r,i)},qe.prototype.verifyWithMessageHashPSS=function(t,e,r,i){if(e.length!=Math.ceil(this.n.bitLength()/4))return!1;var n,s=new d(e,16),a=function(t){return Pr.crypto.Util.hashHex(t,r)},o=$r(t),h=o.length,u=this.n.bitLength()-1,c=Math.ceil(u/8);if(-1===i||void 0===i)i=h;else if(-2===i)i=c-h-2;else if(i<-2)throw new Error("invalid salt length");if(c<h+i+2)throw new Error("data too long");var l=this.doPublic(s).toByteArray();for(n=0;n<l.length;n+=1)l[n]&=255;while(l.length<c)l.unshift(0);if(188!==l[c-1])throw new Error("encoded message does not end in 0xbc");l=String.fromCharCode.apply(String,l);var f=l.substr(0,c-h-1),g=l.substr(f.length,h),p=65280>>8*c-u&255;if(0!==(f.charCodeAt(0)&p))throw new Error("bits beyond keysize not zero");var v=Hi(g,f.length,a),m=[];for(n=0;n<f.length;n+=1)m[n]=f.charCodeAt(n)^v.charCodeAt(n);m[0]&=~p;var y=c-h-i-2;for(n=0;n<y;n+=1)if(0!==m[n])throw new Error("leftmost octets not zero");if(1!==m[y])throw new Error("0x01 marker not found");return g===$r(a(Yr("\0\0\0\0\0\0\0\0"+o+String.fromCharCode.apply(String,m.slice(-i)))))},qe.SALT_LEN_HLEN=-1,qe.SALT_LEN_MAX=-2,qe.SALT_LEN_RECOVER=-2,ji.hex2dn=function(t,e){void 0===e&&(e=0);var r=new ji,i=(Br.getTLV(t,e),r.getX500Name(t));return i.str},ji.hex2rdn=function(t,e){if(void 0===e&&(e=0),"31"!==t.substr(e,2))throw new Error("malformed RDN");for(var r=new Array,i=Br.getChildIdx(t,e),n=0;n<i.length;n++)r.push(ji.hex2attrTypeValue(t,i[n]));return r=r.map((function(t){return t.replace("+","\\+")})),r.join("+")},ji.hex2attrTypeValue=function(t,e){var r=Br,i=r.getV;if(void 0===e&&(e=0),"30"!==t.substr(e,2))throw new Error("malformed attribute type and value");var n=r.getChildIdx(t,e);2!==n.length||t.substr(n[0],2);var s=i(t,n[0]),a=Pr.asn1.ASN1Util.oidHexToInt(s),o=Pr.asn1.x509.OID.oid2atype(a),h=i(t,n[1]),u=$r(h);return o+"="+u},ji.getPublicKeyFromCertHex=function(t){var e=new ji;return e.readCertHex(t),e.getPublicKey()},ji.getPublicKeyFromCertPEM=function(t){var e=new ji;return e.readCertPEM(t),e.getPublicKey()},ji.getPublicKeyInfoPropOfCertPEM=function(t){var e,r,i=Br,n=i.getVbyList,s={};return s.algparam=null,e=new ji,e.readCertPEM(t),r=e.getPublicKeyHex(),s.keyhex=n(r,0,[1],"03").substr(2),s.algoid=n(r,0,[0,0],"06"),"2a8648ce3d0201"===s.algoid&&(s.algparam=n(r,0,[0,1],"06")),s},ji.KEYUSAGE_NAME=["digitalSignature","nonRepudiation","keyEncipherment","dataEncipherment","keyAgreement","keyCertSign","cRLSign","encipherOnly","decipherOnly"];var Vi=function(t){var e=Pr,r=e.lang.String.isHex,n=Br,s=n.getV,a=n.getTLV,o=n.getVbyList,h=n.getTLVbyList,u=n.getTLVbyListEx,c=n.getIdxbyList,l=n.getIdxbyListEx,f=n.getChildIdx,g=new ji;this.hex=null,this.posSigAlg=null,this.posRevCert=null,this.parsed=null,this._setPos=function(){var t=c(this.hex,0,[0,0]),e=this.hex.substr(t,2);if("02"==e)this.posSigAlg=1;else{if("30"!=e)throw new Error("malformed 1st item of TBSCertList: "+e);this.posSigAlg=0}var r,i,n=c(this.hex,0,[0,this.posSigAlg+3]),s=this.hex.substr(n,2);if("17"==s||"18"==s)r=c(this.hex,0,[0,this.posSigAlg+4]),this.posRevCert=null,-1!=r&&(i=this.hex.substr(r,2),"30"==i&&(this.posRevCert=this.posSigAlg+4));else if("30"==s)this.posRevCert=this.posSigAlg+3;else{if("a0"!=s)throw new Error("malformed nextUpdate or revCert tag: "+s);this.posRevCert=null}},this.getVersion=function(){return 0==this.posSigAlg?null:parseInt(o(this.hex,0,[0,0],"02"),16)+1},this.getSignatureAlgorithmField=function(){var t=h(this.hex,0,[0,this.posSigAlg],"30");return g.getAlgorithmIdentifierName(t)},this.getIssuer=function(){return g.getX500Name(this.getIssuerHex())},this.getIssuerHex=function(){return h(this.hex,0,[0,this.posSigAlg+1],"30")},this.getThisUpdate=function(){var t=o(this.hex,0,[0,this.posSigAlg+2]);return result=$r(t)},this.getNextUpdate=function(){var t=c(this.hex,0,[0,this.posSigAlg+3]),e=this.hex.substr(t,2);return"17"!=e&&"18"!=e?null:$r(s(this.hex,t))},this.getRevCertArray=function(){if(null==this.posRevCert)return null;for(var t=[],e=c(this.hex,0,[0,this.posRevCert]),r=f(this.hex,e),i=0;i<r.length;i++){var n=a(this.hex,r[i]);t.push(this.getRevCert(n))}return t},this.getRevCert=function(t){var e={},r=f(t,0);return e.sn={hex:o(t,0,[0],"02")},e.date=$r(o(t,0,[1])),3==r.length&&(e.ext=g.getExtParamArray(h(t,0,[2]))),e},this.findRevCert=function(t){var e=new ji(t),r=e.getSerialNumberHex();return this.findRevCertBySN(r)},this.findRevCertBySN=function(t){if(null==this.parsed&&this.getParam(),null==this.parsed.revcert)return null;for(var e=this.parsed.revcert,r=0;r<e.length;r++)if(t==e[r].sn.hex)return e[r];return null},this.getSignatureValueHex=function(){return o(this.hex,0,[2],"03",!0)},this.verifySignature=function(t){var e=this.getSignatureAlgorithmField(),r=this.getSignatureValueHex(),i=h(this.hex,0,[0],"30"),n=new Pr.crypto.Signature({alg:e});return n.init(t),n.updateHex(i),n.verify(r)},this.getParam=function(t){var e={},r=this.getVersion();null!=r&&(e.version=r),e.sigalg=this.getSignatureAlgorithmField(),e.issuer=this.getIssuer(),e.thisupdate=this.getThisUpdate();var n=this.getNextUpdate();null!=n&&(e.nextupdate=n);var s=this.getRevCertArray();null!=s&&(e.revcert=s);var a=l(this.hex,0,[0,"[0]"]);if(-1!=a){var o=u(this.hex,0,[0,"[0]",0]);e.ext=g.getExtParamArray(o)}return e.sighex=this.getSignatureValueHex(),this.parsed=e,"object"==i(t)&&(1==t.tbshex&&(e.tbshex=h(this.hex,0,[0])),1==t.nodnarray&&delete e.issuer.array),e},"string"==typeof t&&(r(t)?this.hex=t:t.match(/-----BEGIN X509 CRL/)&&(this.hex=ri(t)),this._setPos())};"undefined"!=typeof Pr&&Pr||(Pr={}),"undefined"!=typeof Pr.jws&&Pr.jws||(Pr.jws={}),Pr.jws.JWS=function(){var t=Pr,e=t.jws.JWS,r=e.isSafeJSONString;this.parseJWS=function(t,e){if(void 0===this.parsedJWS||!e&&void 0===this.parsedJWS.sigvalH){var i=t.match(/^([^.]+)\.([^.]+)\.([^.]+)$/);if(null==i)throw"JWS signature is not a form of 'Head.Payload.SigValue'.";var n=i[1],s=i[2],a=i[3],o=n+"."+s;if(this.parsedJWS={},this.parsedJWS.headB64U=n,this.parsedJWS.payloadB64U=s,this.parsedJWS.sigvalB64U=a,this.parsedJWS.si=o,!e){var h=_r(a),u=Ve(h,16);this.parsedJWS.sigvalH=h,this.parsedJWS.sigvalBI=u}var c=Tr(n),l=Tr(s);if(this.parsedJWS.headS=c,this.parsedJWS.payloadS=l,!r(c,this.parsedJWS,"headP"))throw"malformed JSON string for JWS Head: "+c}}},Pr.jws.JWS.sign=function(t,e,r,n,s){var a,o,h,u=Pr,c=u.jws,l=c.JWS,f=l.readSafeJSONString,g=l.isSafeJSONString,p=u.crypto,d=(p.ECDSA,p.Mac),v=p.Signature,m=JSON;if("string"!=typeof e&&"object"!=i(e))throw"spHeader must be JSON string or object: "+e;if("object"==i(e)&&(o=e,a=m.stringify(o)),"string"==typeof e){if(a=e,!g(a))throw"JWS Head is not safe JSON string: "+a;o=f(a)}if(h=r,"object"==i(r)&&(h=m.stringify(r)),""!=t&&null!=t||void 0===o.alg||(t=o.alg),""!=t&&null!=t&&void 0===o.alg&&(o.alg=t,a=m.stringify(o)),t!==o.alg)throw"alg and sHeader.alg doesn't match: "+t+"!="+o.alg;var y=null;if(void 0===l.jwsalg2sigalg[t])throw"unsupported alg name: "+t;y=l.jwsalg2sigalg[t];var x=Rr(a),S=Rr(h),w=x+"."+S,E="";if("Hmac"==y.substr(0,4)){if(void 0===n)throw"mac key shall be specified for HS* alg";var F=new d({alg:y,prov:"cryptojs",pass:n});F.updateString(w),E=F.doFinal()}else if(-1!=y.indexOf("withECDSA")){var b=new v({alg:y});b.init(n,s),b.updateString(w);var A=b.sign();E=Pr.crypto.ECDSA.asn1SigToConcatSig(A)}else if("none"!=y){b=new v({alg:y});b.init(n,s),b.updateString(w),E=b.sign()}var D=Mr(E);return w+"."+D},Pr.jws.JWS.verify=function(t,e,r){var n,s=Pr,a=s.jws,o=a.JWS,h=o.readSafeJSONString,u=s.crypto,c=u.ECDSA,l=u.Mac,f=u.Signature;void 0!==i(qe)&&(n=qe);var g=t.split(".");if(3!==g.length)return!1;var p=g[0],d=g[1],v=p+"."+d,m=_r(g[2]),y=h(Tr(g[0])),x=null,S=null;if(void 0===y.alg)throw"algorithm not specified in header";if(x=y.alg,S=x.substr(0,2),null!=r&&"[object Array]"===Object.prototype.toString.call(r)&&r.length>0){var w=":"+r.join(":")+":";if(-1==w.indexOf(":"+x+":"))throw"algorithm '"+x+"' not accepted in the list"}if("none"!=x&&null===e)throw"key shall be specified to verify.";if("string"==typeof e&&-1!=e.indexOf("-----BEGIN ")&&(e=Bi.getKey(e)),("RS"==S||"PS"==S)&&!(e instanceof n))throw"key shall be a RSAKey obj for RS* and PS* algs";if("ES"==S&&!(e instanceof c))throw"key shall be a ECDSA obj for ES* algs";var E=null;if(void 0===o.jwsalg2sigalg[y.alg])throw"unsupported alg name: "+x;if(E=o.jwsalg2sigalg[x],"none"==E)throw"not supported";if("Hmac"==E.substr(0,4)){var F=null;if(void 0===e)throw"hexadecimal key shall be specified for HMAC";var b=new l({alg:E,pass:e});return b.updateString(v),F=b.doFinal(),m==F}if(-1!=E.indexOf("withECDSA")){var A=null;try{A=c.concatSigToASN1Sig(m)}catch(I){return!1}var D=new f({alg:E});return D.init(e),D.updateString(v),D.verify(A)}D=new f({alg:E});return D.init(e),D.updateString(v),D.verify(m)},Pr.jws.JWS.parse=function(t){var e,r,i,n=t.split("."),s={};if(2!=n.length&&3!=n.length)throw"malformed sJWS: wrong number of '.' splitted elements";return e=n[0],r=n[1],3==n.length&&(i=n[2]),s.headerObj=Pr.jws.JWS.readSafeJSONString(Tr(e)),s.payloadObj=Pr.jws.JWS.readSafeJSONString(Tr(r)),s.headerPP=JSON.stringify(s.headerObj,null,"  "),null==s.payloadObj?s.payloadPP=Tr(r):s.payloadPP=JSON.stringify(s.payloadObj,null,"  "),void 0!==i&&(s.sigHex=_r(i)),s},Pr.jws.JWS.verifyJWT=function(t,e,r){var n=Pr,s=n.jws,a=s.JWS,o=a.readSafeJSONString,h=a.inArray,u=a.includedArray,c=t.split("."),l=c[0],f=c[1],g=(_r(c[2]),o(Tr(l))),p=o(Tr(f));if(void 0===g.alg)return!1;if(void 0===r.alg)throw"acceptField.alg shall be specified";if(!h(g.alg,r.alg))return!1;if(void 0!==p.iss&&"object"===i(r.iss)&&!h(p.iss,r.iss))return!1;if(void 0!==p.sub&&"object"===i(r.sub)&&!h(p.sub,r.sub))return!1;if(void 0!==p.aud&&"object"===i(r.aud))if("string"==typeof p.aud){if(!h(p.aud,r.aud))return!1}else if("object"==i(p.aud)&&!u(p.aud,r.aud))return!1;var d=s.IntDate.getNow();return void 0!==r.verifyAt&&"number"===typeof r.verifyAt&&(d=r.verifyAt),void 0!==r.gracePeriod&&"number"===typeof r.gracePeriod||(r.gracePeriod=0),!(void 0!==p.exp&&"number"==typeof p.exp&&p.exp+r.gracePeriod<d)&&(!(void 0!==p.nbf&&"number"==typeof p.nbf&&d<p.nbf-r.gracePeriod)&&(!(void 0!==p.iat&&"number"==typeof p.iat&&d<p.iat-r.gracePeriod)&&((void 0===p.jti||void 0===r.jti||p.jti===r.jti)&&!!a.verify(t,e,r.alg))))},Pr.jws.JWS.includedArray=function(t,e){var r=Pr.jws.JWS.inArray;if(null===t)return!1;if("object"!==i(t))return!1;if("number"!==typeof t.length)return!1;for(var n=0;n<t.length;n++)if(!r(t[n],e))return!1;return!0},Pr.jws.JWS.inArray=function(t,e){if(null===e)return!1;if("object"!==i(e))return!1;if("number"!==typeof e.length)return!1;for(var r=0;r<e.length;r++)if(e[r]==t)return!0;return!1},Pr.jws.JWS.jwsalg2sigalg={HS256:"HmacSHA256",HS384:"HmacSHA384",HS512:"HmacSHA512",RS256:"SHA256withRSA",RS384:"SHA384withRSA",RS512:"SHA512withRSA",ES256:"SHA256withECDSA",ES384:"SHA384withECDSA",ES512:"SHA512withECDSA",PS256:"SHA256withRSAandMGF1",PS384:"SHA384withRSAandMGF1",PS512:"SHA512withRSAandMGF1",none:"none"},Pr.jws.JWS.isSafeJSONString=function(t,e,r){var n=null;try{return n=Cr(t),"object"!=i(n)?0:n.constructor===Array?0:(e&&(e[r]=n),1)}catch(s){return 0}},Pr.jws.JWS.readSafeJSONString=function(t){var e=null;try{return e=Cr(t),"object"!=i(e)||e.constructor===Array?null:e}catch(r){return null}},Pr.jws.JWS.getEncodedSignatureValueFromJWS=function(t){var e=t.match(/^[^.]+\.[^.]+\.([^.]+)$/);if(null==e)throw"JWS signature is not a form of 'Head.Payload.SigValue'.";return e[1]},Pr.jws.JWS.getJWKthumbprint=function(t){if("RSA"!==t.kty&&"EC"!==t.kty&&"oct"!==t.kty)throw"unsupported algorithm for JWK Thumprint";var e="{";if("RSA"===t.kty){if("string"!=typeof t.n||"string"!=typeof t.e)throw"wrong n and e value for RSA key";e+='"e":"'+t.e+'",',e+='"kty":"'+t.kty+'",',e+='"n":"'+t.n+'"}'}else if("EC"===t.kty){if("string"!=typeof t.crv||"string"!=typeof t.x||"string"!=typeof t.y)throw"wrong crv, x and y value for EC key";e+='"crv":"'+t.crv+'",',e+='"kty":"'+t.kty+'",',e+='"x":"'+t.x+'",',e+='"y":"'+t.y+'"}'}else if("oct"===t.kty){if("string"!=typeof t.k)throw"wrong k value for oct(symmetric) key";e+='"kty":"'+t.kty+'",',e+='"k":"'+t.k+'"}'}var r=Yr(e),i=Pr.crypto.Util.hashHex(r,"sha256"),n=Mr(i);return n},Pr.jws.IntDate={},Pr.jws.IntDate.get=function(t){var e=Pr.jws.IntDate,r=e.getNow,i=e.getZulu;if("now"==t)return r();if("now + 1hour"==t)return r()+3600;if("now + 1day"==t)return r()+86400;if("now + 1month"==t)return r()+2592e3;if("now + 1year"==t)return r()+31536e3;if(t.match(/Z$/))return i(t);if(t.match(/^[0-9]+$/))return parseInt(t);throw"unsupported format: "+t},Pr.jws.IntDate.getZulu=function(t){return ai(t)},Pr.jws.IntDate.getNow=function(){var t=~~(new Date/1e3);return t},Pr.jws.IntDate.intDate2UTCString=function(t){var e=new Date(1e3*t);return e.toUTCString()},Pr.jws.IntDate.intDate2Zulu=function(t){var e=new Date(1e3*t),r=("0000"+e.getUTCFullYear()).slice(-4),i=("00"+(e.getUTCMonth()+1)).slice(-2),n=("00"+e.getUTCDate()).slice(-2),s=("00"+e.getUTCHours()).slice(-2),a=("00"+e.getUTCMinutes()).slice(-2),o=("00"+e.getUTCSeconds()).slice(-2);return r+i+n+s+a+o+"Z"},"undefined"!=typeof Pr&&Pr||(Pr={}),"undefined"!=typeof Pr.jws&&Pr.jws||(Pr.jws={}),Pr.jws.JWSJS=function(){var t=Pr,e=t.jws,r=e.JWS,i=r.readSafeJSONString;this.aHeader=[],this.sPayload="",this.aSignature=[],this.init=function(){this.aHeader=[],this.sPayload=void 0,this.aSignature=[]},this.initWithJWS=function(t){this.init();var e=t.split(".");if(3!=e.length)throw"malformed input JWS";this.aHeader.push(e[0]),this.sPayload=e[1],this.aSignature.push(e[2])},this.addSignature=function(t,e,r,i){if(void 0===this.sPayload||null===this.sPayload)throw"there's no JSON-JS signature to add.";var n=this.aHeader.length;if(this.aHeader.length!=this.aSignature.length)throw"aHeader.length != aSignature.length";try{var s=Pr.jws.JWS.sign(t,e,this.sPayload,r,i),a=s.split(".");a[0],a[2];this.aHeader.push(a[0]),this.aSignature.push(a[2])}catch(o){throw this.aHeader.length>n&&this.aHeader.pop(),this.aSignature.length>n&&this.aSignature.pop(),"addSignature failed: "+o}},this.verifyAll=function(t){if(this.aHeader.length!==t.length||this.aSignature.length!==t.length)return!1;for(var e=0;e<t.length;e++){var r=t[e];if(2!==r.length)return!1;var i=this.verifyNth(e,r[0],r[1]);if(!1===i)return!1}return!0},this.verifyNth=function(t,e,i){if(this.aHeader.length<=t||this.aSignature.length<=t)return!1;var n=this.aHeader[t],s=this.aSignature[t],a=n+"."+this.sPayload+"."+s,o=!1;try{o=r.verify(a,e,i)}catch(h){return!1}return o},this.readJWSJS=function(t){if("string"===typeof t){var e=i(t);if(null==e)throw"argument is not safe JSON object string";this.aHeader=e.headers,this.sPayload=e.payload,this.aSignature=e.signatures}else try{if(!(t.headers.length>0))throw"malformed header";if(this.aHeader=t.headers,"string"!==typeof t.payload)throw"malformed signatures";if(this.sPayload=t.payload,!(t.signatures.length>0))throw"malformed signatures";this.aSignature=t.signatures}catch(r){throw"malformed JWS-JS JSON object: "+r}},this.getJSON=function(){return{headers:this.aHeader,payload:this.sPayload,signatures:this.aSignature}},this.isEmpty=function(){return 0==this.aHeader.length?1:0}},Pr.crypto.ECDSA,Pr.crypto.DSA,Pr.crypto.Signature,Pr.crypto.MessageDigest,Pr.crypto.Mac,Pr.crypto.Cipher,e.KZ=Bi,e.zs=a,e.GV=l,e.WN=f,e.d5=Or,e.EG=Gr,e.q9=Zr,e.fs=Pr,Pr.crypto,Pr.asn1,Pr.jws,Pr.lang}}]);
(typeof window=='undefined'?global:window).tc_cfg_09272470305613778={"url":"css/theme-colors-ea095f1b.css","colors":["#1b65b9","#3274c0","#4984c7","#5f93ce","#76a3d5","#8db2dc","#a4c1e3","#bbd1ea","#d1e0f1","#ebf4fa","#bbd9ed","#8dbce0","#639fd4","#3e82c7","#1b65b9","#0f4894","#052f6e","#001a47","#000b21","27,101,185"]};

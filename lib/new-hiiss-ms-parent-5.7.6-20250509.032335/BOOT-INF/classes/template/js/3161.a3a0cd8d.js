(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[3161],{88412:function(t,e,a){"use strict";var i=a(26263),r=a(36766),n=a(1001),o=(0,n.Z)(r.Z,i.s,i.x,!1,null,"5e7ef0ae",null);e["Z"]=o.exports},33198:function(t,e,a){"use strict";a.r(e),a.d(e,{default:function(){return h}});var i=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("div",{staticClass:"fit"},[i("ta-border-layout",[i("div",{attrs:{slot:"header"},slot:"header"},[i("ta-form",{staticStyle:{"padding-top":"10px"},attrs:{autoFormCreate:function(e){t.searchForm=e},layout:"horizontal",formLayout:!0}},[i("ta-form-item",{attrs:{"field-decorator-id":"indexCode",fieldDecoratorOptions:{rules:[{required:!0,message:"必须选取指标"}]},label:"指标名称",span:5}},[i("ta-select",{attrs:{placeholder:"指标名称筛选",allowClear:"",showSearch:"",options:e.indexOptions}})],1),i("ta-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary"},on:{click:e.onSearch}},[e._v("查询 ")]),i("ta-button",{staticStyle:{"margin-left":"5px"},on:{click:e.onImport}},[e._v("导入 ")])],1)],1),i("div",{staticClass:"fit content-box",staticStyle:{height:"90%"}},[i("ta-title",{staticStyle:{width:"50%"},attrs:{title:"指标配置"}},[i("span",{staticStyle:{"padding-left":"10px","font-size":"15px"}},[e._v("导入时间："+e._s(e.importDate))])]),i("ta-big-table",{ref:"ruleTable",attrs:{data:e.ruleList,"export-config":{},"import-config":{},"checkbox-config":{trigger:"row",highlight:!0,reserve:!0},border:"","empty-text":"-",height:"auto","highlight-hover-row":"","highlight-current-row":"","auto-resize":"","show-overflow":"",resizable:"","row-id":"itemCode"}},[i("ta-big-table-column",{attrs:{type:"seq",fixed:"left",width:"50px",title:" ",align:"center"}}),i("ta-big-table-column",{attrs:{field:"indexCode",title:"指标编码",visible:!1,align:"center","min-width":200}}),i("ta-big-table-column",{attrs:{field:"indexName",title:"指标名称",visible:!1,align:"center","min-width":200}}),i("ta-big-table-column",{attrs:{field:"itemCode",title:"医保项目编码",align:"center","min-width":200}}),i("ta-big-table-column",{attrs:{field:"itemName",title:"医保项目名称",align:"center","min-width":200}}),i("template",{slot:"bottomBar"},[i("ta-button",{staticStyle:{float:"right","margin-top":"20px"},attrs:{icon:"download",type:"primary"},on:{click:e.exportExcel}},[e._v("导出 ")]),i("ta-pagination",{ref:"rulePager",staticStyle:{float:"right","margin-top":"20px","margin-right":"10px"},attrs:{"data-source":e.ruleList,params:e.pageParams,url:"indexParameter/getIndexData",defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"]},on:{"update:dataSource":function(t){e.ruleList=t},"update:data-source":function(t){e.ruleList=t}}})],1)],2)],1)]),i("ta-modal",{attrs:{title:"导入",visible:e.visible,centered:"",width:"500px",destroyOnClose:!0},on:{ok:e.handleOk,cancel:e.handleCancel}},[i("ta-form",{attrs:{autoFormCreate:function(e){t.form=e}}},[i("ta-form-item",{attrs:{label:"导入模板",labelCol:{span:5},wrapperCol:{span:18}}},[i("a",{on:{click:e.downloadTemplate}},[e._v("模板下载")])]),i("ta-form-item",{attrs:{"field-decorator-id":"fileName",fieldDecoratorOptions:{rules:[{required:!0,message:"必须选取文件"}]},label:"选择文件",labelCol:{span:5},wrapperCol:{span:18}}},[i("ta-input",{staticStyle:{width:"200px"}}),i("ta-upload",{attrs:{name:"file","show-upload-list":!1,"before-upload":e.beforeUpload,accept:".xls"}},[i("ta-button",{attrs:{type:"primary"}},[e._v(" 浏览 ")])],1)],1)],1)],1)],1)},r=[],n=a(66347),o=a(89584),l=a(95082),s=a(88412),c=a(22722),u=(a(36797),a(55115));u.w3.prototype.Base=Object.assign(u.w3.prototype.Base,(0,l.Z)({},c.Z));var d={components:{TaTitle:s.Z},data:function(){return{indexOptions:[],backUrl:"",visible:!1,fileList:[],ruleList:[],importDate:""}},created:function(){this.backUrl=faceConfig.basePath,this.queryIndexOptions()},mounted:function(){},methods:{onImport:function(){var t=this.searchForm.getFieldsValue();t.indexCode?this.visible=!0:message.error("请先选择指标")},onSearch:function(){var t=this,e=this.searchForm.getFieldsValue();e.indexCode?this.$refs.rulePager.loadData((function(e){var a=e.data.pageBean.list;t.importDate=a.length>0?a[0].writeDate.slice(0,10):""})):message.error("请先选择指标")},queryIndexOptions:function(){var t=this,e={url:"/indexParameter/getIndxOptions",autoValid:!0},a={successCallback:function(e){t.indexOptions=e.data.data},failCallback:function(e){t.$message.error("指标数据数据加载失败")}};this.Base.submit(null,e,a)},pageParams:function(){var t=this.searchForm.getFieldsValue();return t},downloadTemplate:function(){var t=this;Base.downloadFile({method:"post",fileName:"指标参数导入模板.xls",url:"indexParameter/downloadIndexTemplate"}).then((function(e){t.$message.success("下载成功")})).catch((function(e){t.$message.error("下载失败")}))},handleOk:function(){var t=this,e=this.searchForm.getFieldsValue(),a=e.indexCode,i=this.indexOptions.filter((function(t){return t.value===a}))[0],r={url:"/indexParameter/importIndexFile",data:{file:this.fileList,indexCode:a,indexName:i.label,aae100:i.aae100},isFormData:!0};Base.submit(null,r).then((function(e){message.info("上传成功!!!"),t.onSearch()})).catch((function(){message.info("上传失败!!!")})),this.visible=!1},handleCancel:function(){this.visible=!1},beforeUpload:function(t){return this.fileList=[],this.form.setFieldsValue({fileName:t.name}),this.fileList=[].concat((0,o.Z)(this.fileList),[t]),!1},exportExcel:function(){var t=this,e=this.searchForm.getFieldsValue();if(e.indexCode){var a,i=e.indexCode,r=this.indexOptions.filter((function(t){return t.value===i}))[0],o=this.pageParams(),l=[],s=this.$refs.ruleTable.getColumns(),c=(this.infoTableData,(0,n.Z)(s));try{for(c.s();!(a=c.n()).done;){var u=a.value;"seq"!==u.type&&"operate"!==u.property&&l.push({header:u.title,key:u.property,width:20})}}catch(d){c.e(d)}finally{c.f()}this.Base.submit(null,{url:"indexParameter/exportExcel",data:o,autoValid:!1},{successCallback:function(e){var a=e.data.data,i={fileName:"".concat(r.label,"参数配置"),sheets:[{name:"worksheet1",column:{complex:!1,columns:l},rows:a,codeList:[]}]};t.Base.generateExcel(i)},failCallback:function(e){t.$message.error("医师数据加载失败")}})}else message.error("请先选择指标")}}},f=d,p=a(1001),m=(0,p.Z)(f,i,r,!1,null,"60b996bc",null),h=m.exports},36766:function(t,e,a){"use strict";var i=a(66586);e["Z"]=i.Z},26263:function(t,e,a){"use strict";a.d(e,{s:function(){return i},x:function(){return r}});var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},r=[]},66586:function(t,e){"use strict";var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};e["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}},55382:function(){},61219:function(){}}]);
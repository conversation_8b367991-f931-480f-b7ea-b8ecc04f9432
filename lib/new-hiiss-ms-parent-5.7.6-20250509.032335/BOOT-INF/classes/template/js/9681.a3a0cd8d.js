(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[9681],{88412:function(t,a,e){"use strict";var i=e(26263),l=e(36766),n=e(1001),r=(0,n.Z)(l.Z,i.s,i.x,!1,null,"5e7ef0ae",null);a["Z"]=r.exports},21356:function(t,a,e){"use strict";e.r(a),e.d(a,{default:function(){return $}});var i=function(){var t=this,a=this,e=a.$createElement,i=a._self._c||e;return i("div",{staticClass:"fit"},[i("ta-border-layout",{staticStyle:{},attrs:{"layout-type":"fixTop"}},[i("div",{attrs:{slot:"header"},slot:"header"},[i("div",{staticClass:"query-header"},[i("div",{ref:"formBox",staticClass:"query-header-left",style:a.formBoxStyle},[i("ta-form",{attrs:{"auto-form-create":function(a){t.form=a},col:a.col,layout:"horizontal","label-width":"80px",formLayout:!0}},[i("ta-form-item",{attrs:{span:12,label:"审核批次号","field-decorator-id":"aze001","label-width":"100px",require:{message:"请选择审核批次号!"}}},[i("ta-select",{attrs:{placeholder:"请选择审核批次号",options:a.batchLogList}})],1),i("ta-form-item",{attrs:{span:6,label:"审核场景","field-decorator-id":"aae500","label-width":"130px",require:{message:"请选择审核场景!"}}},[i("ta-select",{attrs:{placeholder:"请选择审核场景","collection-type":"AAE500","collection-filter":a.filterList,reverseFilter:!0,allowClear:""}})],1),i("ta-form-item",{attrs:{span:6,label:"医保类型","field-decorator-id":"aae141","label-width":"100px"}},[i("ta-select",{attrs:{placeholder:"请选择医保类型","collection-type":"AAE141","allow-clear":!0}})],1),i("ta-form-item",{attrs:{span:6,label:"规则变化类型","field-decorator-id":"changeDesc","label-width":"100px"}},[i("ta-select",{attrs:{placeholder:"请选择规则变化类型",options:a.changeDescList,mode:"multiple","allow-clear":!0}})],1),i("ta-form-item",{attrs:{span:6,label:"医保项目编码","field-decorator-id":"ake001","label-width":"110px"}},[i("ta-input",{attrs:{placeholder:"请输入医保项目编码"}})],1),i("ta-form-item",{attrs:{span:6,label:"医保项目名称","field-decorator-id":"ake002","label-width":"130px"}},[i("ta-input",{attrs:{placeholder:"可模糊匹配"}})],1),i("ta-form-item",{attrs:{span:6,label:"规则名称","field-decorator-id":"aaa167","label-width":"100px"}},[i("ta-input",{attrs:{placeholder:"可模糊匹配"}})],1),i("ta-form-item",{attrs:{span:6,label:"规则序号","field-decorator-id":"ykz032","label-width":"100px"}},[i("ta-input",{attrs:{placeholder:"可模糊匹配"}})],1),i("ta-form-item",{attrs:{span:6,label:"限制条件","field-decorator-id":"ykz018","label-width":"110px"}},[i("ta-input",{attrs:{placeholder:"可模糊匹配"}})],1),i("ta-form-item",{attrs:{span:6,label:"数据筛选","field-decorator-id":"dataFilterType","label-width":"130px"}},[i("ta-select",{attrs:{placeholder:"请选择数据类型",options:a.dataFilterTypeList,"allow-clear":!0}})],1)],1)],1),i("div",{staticStyle:{display:"flex","margin-left":"10px",float:"right"}},[i("div",{staticClass:"ctrl-btn",on:{click:a.formShowAllChange}},[i("a",[a._v(a._s(a.formShowAll?"收起":"展开"))]),a.formShowAll?i("ta-icon",{attrs:{type:"caret-up"}}):i("ta-icon",{attrs:{type:"caret-down"}})],1),i("ta-button",{attrs:{type:"primary",icon:"search"},on:{click:a.fnQuery}},[a._v("查询")]),i("ta-button",{attrs:{type:"default",icon:"redo"},on:{click:a.fnReset}},[a._v("重置")])],1),i("div",{directives:[{name:"show",rawName:"v-show",value:a.formShowAll,expression:"formShowAll"}],staticStyle:{display:"flex",margin:"20px 0 0 10px",float:"right"}},[i("ta-button",{attrs:{type:"warning"},on:{click:a.fnOpenAuditWin}},[a._v(" 预上线规则审核 ")])],1)])]),i("div",{staticClass:"fit content-box"},[i("ta-title",{attrs:{title:"查询结果"}}),i("div",{staticStyle:{height:"auto","margin-bottom":"5px"}},[i("div",{directives:[{name:"show",rawName:"v-show",value:a.showValidFlag,expression:"showValidFlag"}]},[i("ta-alert",{attrs:{type:"error","show-icon":!0}},[i("template",{slot:"message"},[i("span",{staticStyle:{"font-size":"16px","font-weight":"bolder"}},[a._v("本批次数据校验不通过！！")]),i("span",[a._v("数据校验规则：使用量不一致、审核量不一致（不校验条目新增、停用的数据）。")]),i("span",[a._v("仅校验审核批次号、审核场景条件，校验时包含使用量为0的数据。")])])],2),i("div",{staticStyle:{margin:"5px 0"}},[a._l(a.checkTagList,(function(t){return[i("ta-tooltip",{key:t.key,attrs:{placement:"top",title:t.tooltipTitle}},[i("ta-checkable-tag",{key:t.key,staticClass:"tag_class",style:a.fnTagStyle(t),attrs:{checked:a.validRuleTypeList.indexOf(t.key)>-1},on:{change:function(e){return a.handleTagChange(t.key,e)}}},[a._v(" "+a._s(t.content)+"("+a._s(t.num)+") ")])],1)]}))],2)],1),i("div",[a._l(a.analysisTagList,(function(t){return[i("ta-tooltip",{key:t.key,attrs:{placement:"top",title:t.tooltipTitle}},[i("ta-checkable-tag",{key:t.key,staticClass:"tag_class",style:a.fnTagStyle(t),attrs:{checked:a.validRuleTypeList.indexOf(t.key)>-1},on:{change:function(e){return a.handleTagChange(t.key,e)}}},[a._v(" "+a._s(t.content)+"("+a._s(t.num)+") ")])],1)]}))],2)]),i("div",{staticClass:"content-table"},[i("ta-big-table",{ref:"dataTable",attrs:{"export-config":{},"import-config":{},data:a.tableData,border:"",size:"small",resizable:"","highlight-hover-row":"","highlight-current-row":"",height:"auto","auto-resize":"",stripe:"","header-align":"center","show-overflow":"","keep-source":"","edit-config":{trigger:"click",mode:"row",showStatus:!0}},on:{"edit-closed":a.editClosed}},[i("ta-big-table-column",{attrs:{type:"seq",title:"序号",width:"60px",align:"center",fixed:"left"}}),i("ta-big-table-column",{attrs:{field:"changeDesc",title:"变化类型","min-width":"100px",fixed:"left"}}),i("ta-big-table-column",{attrs:{field:"remarks",title:"备注","min-width":"120px",fixed:"left"}}),i("ta-big-table-column",{attrs:{field:"ykz032",title:"规则序号","min-width":"100px",fixed:"left"}}),i("ta-big-table-column",{attrs:{field:"ake002",title:"医保项目名称","min-width":"140px",fixed:"left"}}),i("ta-big-table-column",{attrs:{field:"ykz018",title:"限制条件","min-width":"180px"}}),i("ta-big-table-column",{attrs:{field:"akc228",title:"预审审核量","min-width":"100px"},scopedSlots:a._u([{key:"default",fn:function(t){var e=t.row;return[i("a",{attrs:{href:"#"},on:{click:function(t){return a.fnOpenAuditResultPage(e,"2")}}},[a._v(a._s(e.akc228))])]}}])}),i("ta-big-table-column",{attrs:{field:"akc228Old",title:"实审审核量","min-width":"100px"},scopedSlots:a._u([{key:"default",fn:function(t){var e=t.row;return[i("a",{attrs:{href:"#"},on:{click:function(t){return a.fnOpenAuditResultPage(e,"1")}}},[a._v(a._s(e.akc228Old))])]}}])}),i("ta-big-table-column",{attrs:{field:"akc228Pass",title:"预审合规次数","min-width":"120px"}}),i("ta-big-table-column",{attrs:{field:"akc228PassOld",title:"实审合规次数","min-width":"120px"}}),i("ta-big-table-column",{attrs:{field:"akc228Fail",title:"预审违规次数","min-width":"120px"}}),i("ta-big-table-column",{attrs:{field:"akc228FailOld",title:"实审违规次数","min-width":"120px"}}),i("ta-big-table-column",{attrs:{field:"aze001",title:"审核批次号","min-width":"160px"}}),i("ta-big-table-column",{attrs:{field:"aae043Old",title:"实审规则批号","min-width":"140px"}}),i("ta-big-table-column",{attrs:{field:"aae043",title:"预审规则批号","min-width":"140px"}}),i("ta-big-table-column",{attrs:{field:"aae500",title:"审核场景","min-width":"100px","collection-type":"AAE500"}}),i("ta-big-table-column",{attrs:{field:"aae141",title:"医保类型","min-width":"100px","collection-type":"AAE141"}}),i("ta-big-table-column",{attrs:{field:"ake001",title:"医保项目编码","min-width":"140px"}}),i("ta-big-table-column",{attrs:{field:"aaa167",title:"规则名称","min-width":"180px"}}),i("ta-big-table-column",{attrs:{field:"validRuleType",title:"规则校验类型","min-width":"100px",visible:!1}}),i("ta-big-table-column",{attrs:{field:"operate",title:"操作","min-width":"200px",align:"center",fixed:"right"},scopedSlots:a._u([{key:"default",fn:function(t){var e=t.row;return[e.akc228Fail!==e.akc228FailOld?i("a",{staticStyle:{"margin-right":"10px"},attrs:{href:"#"},on:{click:function(t){return a.fnOpenDifferenceWin(e,"0")}}},[a._v("违规差异患者")]):a._e(),e.akc228Pass!==e.akc228PassOld?i("a",{staticStyle:{"margin-right":"10px"},attrs:{href:"#"},on:{click:function(t){return a.fnOpenDifferenceWin(e,"1")}}},[a._v("合规差异患者")]):a._e()]}}])})],1)],1),i("div",{staticClass:"content-box-footer"},[i("ta-pagination",{ref:"gridPager",staticStyle:{position:"absolute",right:"6px",bottom:"6px","margin-right":"100px"},attrs:{"data-source":a.tableData,params:a.tablePageParams,defaultPageSize:200,pageSizeOptions:["100","200","500","1000","1500"],url:"monitorMg/batchAudit/queryBatchCompareResultForPage"},on:{"update:dataSource":function(t){a.tableData=t},"update:data-source":function(t){a.tableData=t}}}),i("ta-button-group",{staticStyle:{position:"absolute",right:"6px",bottom:"6px"}},[i("ta-button",{attrs:{type:"primary",icon:"download"},on:{click:a.fnExportData}},[a._v("导出")])],1)],1)],1)]),i("run-pre-audit-win",{ref:"runPreAuditWin",on:{fnQueryAae043:a.fnQueryAae043}}),i("difference-win",{ref:"differenceWin"})],1)},l=[],n=e(66347),r=e(95082),o=(e(36797),function(){var t=this,a=this,e=a.$createElement,i=a._self._c||e;return i("div",[i("ta-modal",{attrs:{visible:a.modalVisible,title:"预上线规则审核","destroy-on-close":!0,"mask-closable":!1,height:"700px",width:"1600px",footer:null},on:{cancel:a.onCloseModal}},[i("ta-border-layout",{attrs:{"layout-type":"flexBorder"}},[i("div",{staticStyle:{width:"100%",height:"60px","border-bottom":"1px solid #DCE0E6"},attrs:{slot:"header"},slot:"header"},[i("ta-form",{attrs:{layout:"horizontal","form-layout":!0,col:a.col,"auto-form-create":function(a){t.form=a}}},[i("ta-form-item",{attrs:{label:"最新预审规则批次号",span:5,"field-decorator-id":"aae043Pre","label-width":"140px"}},[i("span",[a._v(a._s(a.preAae043))])]),i("ta-form-item",{attrs:{label:"预审规则批次号",span:5,"field-decorator-id":"aae043","label-width":"120px"}},[i("ta-select",{attrs:{placeholder:"请选择预审规则批次号",options:a.preAae043List,"allow-clear":!0}})],1),i("ta-form-item",{attrs:{label:"实审规则批次号",span:5,"field-decorator-id":"aae043Old","label-width":"120px"}},[i("ta-select",{attrs:{placeholder:"请选择实审规则批次号",options:a.aae043List,"allow-clear":!0}})],1),i("ta-form-item",{attrs:{span:5,"field-decorator-id":"item-btn"}},[i("ta-button",{staticStyle:{margin:"0 20px 0 20px"},attrs:{type:"primary"},on:{click:a.fnQuery}},[a._v(" 查询 ")]),i("ta-button",{on:{click:a.fnOpenImportWin}},[a._v(" 新建预审任务 ")])],1)],1)],1),i("div",{staticStyle:{height:"100%"}},[i("ta-big-table",{ref:"dataTable",attrs:{data:a.tableData,border:"",size:"small",resizable:"","highlight-hover-row":"","highlight-current-row":"",height:"auto","auto-resize":"",stripe:"","header-align":"center","show-overflow":""}},[i("ta-big-table-column",{attrs:{type:"seq",title:"序号",width:"60px",align:"center"}}),i("ta-big-table-column",{attrs:{field:"aze001",title:"审核批次号",width:"10%"}}),i("ta-big-table-column",{attrs:{field:"aae043",title:"预审规则批次号",width:"9%"}}),i("ta-big-table-column",{attrs:{field:"aae043Old",title:"实审规则批次号",width:"9%"}}),i("ta-big-table-column",{attrs:{field:"auditDays",title:"实审天数",width:"5%"}}),i("ta-big-table-column",{attrs:{field:"aze002Start",title:"实审开始时间",width:"10%"}}),i("ta-big-table-column",{attrs:{field:"aze002End",title:"实审结束时间",width:"10%"}}),i("ta-big-table-column",{attrs:{field:"beginTime",title:"预审开始时间",width:"10%"}}),i("ta-big-table-column",{attrs:{field:"finishTime",title:"预审结束时间",width:"10%"}}),i("ta-big-table-column",{attrs:{field:"status",title:"执行状态",width:"10%",formatter:function(t){var e=t.cellValue;return a.fieldFormat(e,a.auditStatusList)}}}),i("ta-big-table-column",{attrs:{field:"inparam",title:"入参数据",width:"10%"}}),i("ta-big-table-column",{attrs:{field:"remarks",title:"执行进度描述",width:"8%"},scopedSlots:a._u([{key:"default",fn:function(t){var e=t.row;return[null!==e.remarks?i("a",{on:{click:function(t){return a.fnShowFieldInfo(e.remarks)}}},[a._v("查看")]):a._e()]}}])}),i("ta-big-table-column",{attrs:{field:"msg",title:"日志信息",width:"5%"},scopedSlots:a._u([{key:"default",fn:function(t){var e=t.row;return[null!==e.msg?i("a",{on:{click:function(t){return a.fnShowFieldInfo(e.msg)}}},[a._v("查看")]):a._e()]}}])}),i("ta-big-table-column",{attrs:{field:"stack",title:"堆栈信息",width:"5%"},scopedSlots:a._u([{key:"default",fn:function(t){var e=t.row;return[null!==e.stack?i("a",{on:{click:function(t){return a.fnShowFieldInfo(e.stack)}}},[a._v("查看")]):a._e()]}}])}),i("ta-big-table-column",{attrs:{field:"aae040",title:"数据入库时间",width:"10%"}}),i("ta-big-table-column",{attrs:{field:"operate",title:"操作",align:"center",fixed:"right",width:"240px"},scopedSlots:a._u([{key:"default",fn:function(t){var e=t.row;return[i("div",{directives:[{name:"show",rawName:"v-show",value:"1"!==e.status&&"3"!==e.status,expression:"row.status !== '1' && row.status !== '3'"}],staticStyle:{display:"inline-block"}},[i("ta-popconfirm",{attrs:{placement:"left",title:"确定需要执行审核吗？"},on:{confirm:function(t){return a.fnRunBatchAudit(e)}}},[i("a",{attrs:{href:"#"}},[a._v("执行审核")])])],1),i("div",{directives:[{name:"show",rawName:"v-show",value:"1"!==e.status&&"3"!==e.status,expression:"row.status !== '1' && row.status !== '3'"}],staticStyle:{display:"inline-block","margin-left":"10px"}},[i("ta-popconfirm",{attrs:{placement:"top",title:"确定删除该审核批次号的相关所有数据吗？"},on:{confirm:function(t){return a.fnDeleteBatchData(e)}}},[i("a",{attrs:{href:"#"}},[a._v("删除")])])],1)]}}])})],1)],1),i("div",{staticStyle:{width:"100%",height:"50px",padding:"10px 0","border-top":"1px solid #DCE0E6"},attrs:{slot:"footer"},slot:"footer"},[i("ta-pagination",{ref:"gridPager",staticStyle:{"text-align":"center"},attrs:{"data-source":a.tableData,params:a.tablePageParams,"page-size-options":["10","30","50","70","100"],"default-page-size":10,url:"monitorMg/batchAudit/queryBatchLogForPage"},on:{"update:dataSource":function(t){a.tableData=t},"update:data-source":function(t){a.tableData=t}}})],1)]),i("import-win",{ref:"batchImportWin",on:{runFnQuery:a.fnQueryAae043}})],1)],1)}),s=[],c=(e(32564),[{value:"5",label:"医保办住院"},{value:"13",label:"门特医保办"},{value:"7",label:"门特医生端"}]),u=[{value:"1",label:"合规次数为0"},{value:"2",label:"违规次数为0"},{value:"3",label:"合规与违规次数都为0"}],d=[{value:"-1",label:"失败报错"},{value:"0",label:"新建预审监测任务"},{value:"1",label:"批量审核进行中"},{value:"2",label:"批量审核完成"},{value:"3",label:"聚合数据进行中"},{value:"4",label:"聚合数据完成并结束"}],f=[{value:"无变化",label:"无变化"},{value:"停用",label:"停用"},{value:"新增",label:"新增"},{value:"变宽",label:"变宽"},{value:"变严",label:"变严"},{value:"有变化",label:"有变化"},{value:"节点内容新增",label:"节点内容新增"},{value:"节点内容减少",label:"节点内容减少"},{value:"节点内容变更",label:"节点内容变更"}],h=[{value:"1",label:"违规次数不相等"},{value:"2",label:"违规次数相等"},{value:"3",label:"合规次数不相等"},{value:"4",label:"合规次数相等"}],m=function(){var t=this,a=this,e=a.$createElement,i=a._self._c||e;return i("div",[i("ta-modal",{attrs:{visible:a.modalVisible,title:"新建预审任务","destroy-on-close":!0,"mask-closable":!1,height:"340px",width:"450px"},on:{cancel:a.onCloseModal}},[i("div",[i("ta-form",{attrs:{"auto-form-create":function(a){t.form=a}}},[i("ta-form-item",{attrs:{label:"任务批次号","field-decorator-id":"aze001","label-width":"120px",require:{message:"请选择预审规则批次号!"}}},[i("ta-input",{attrs:{disabled:"",placeholder:"请输入预审规则批次号"}})],1),i("ta-form-item",{attrs:{label:"预审规则批次号","field-decorator-id":"aae043","label-width":"120px","init-value":a.preAae043,disabled:"无"!==a.preAae043,require:{message:"请选择预审规则批次号!"}}},[i("ta-input",{attrs:{placeholder:"请输入预审规则批次号"}})],1),i("ta-form-item",{attrs:{label:"实审规则批次号","field-decorator-id":"aae043Old","label-width":"120px",require:{message:"请选择实审规则批次号!"}}},[i("ta-input",{attrs:{disabled:"",placeholder:"请输入实审规则批次号"}})],1),i("ta-form-item",{attrs:{label:"审核场景","field-decorator-id":"aae500List","label-width":"120px",require:{message:"请选择审核场景!"}}},[i("ta-select",{attrs:{placeholder:"请选择审核场景",mode:"multiple",maxTagCount:3,"collection-type":"AAE500","collection-filter":a.filterList,reverseFilter:!0,allowClear:""}})],1),i("ta-form-item",{attrs:{label:"审核天数","field-decorator-id":"auditDays","init-value":14,"label-width":"120px",require:{message:"请输入审核天数!"}}},[i("ta-input-number",{attrs:{min:1,max:14,formatter:function(t){return Math.floor(t)},parser:function(t){return Math.floor(t)}}}),i("span",{staticStyle:{"margin-left":"10px"}},[a._v("天")]),i("span",{staticStyle:{"font-size":"10px","margin-left":"10px",color:"#CCCCCC"}},[a._v("最多可选14天")])],1)],1)],1),i("template",{slot:"footer"},[i("ta-button",{on:{click:a.onCloseModal}},[a._v(" 取消 ")]),i("ta-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary"},on:{click:function(t){return a.handleOk()}}},[a._v(" 确认 ")])],1)],2)],1)},p=[],b=e(83231),g={name:"importWin",props:{},data:function(){return{modalVisible:!1,filterList:"",aae043List:[],aae043info:{},preAae043:"无",fileList:[],uploading:"等待"}},mounted:function(){var t=this,a=["5","6","7","4","3","18","16","17","2"];b.Z.getAa01AAE500StartStop({aaz499s:JSON.stringify(a)},(function(a){var e=a.data.aae500List;e.includes(12)&&e.push(13),t.filterList=e.join(",")}))},methods:{onCloseModal:function(t){var a=this;this.modalVisible=!this.modalVisible,this.uploading="等待",this.fileList=[],this.aae043info={},this.aae043List=[],this.preAae043="无",this.modalVisible&&this.$nextTick((function(){a.aae043List=t.aae043List,a.preAae043=t.preAae043,a.aae043info=t.aae043info,a.form.setFieldsValue(a.aae043info)}))},downloadDataTemplate:function(){var t=this;this.Base.downloadFile({method:"post",fileName:"理论规则修改清单模板.xlsx",url:"monitorMg/batchAudit/downloadDataTemplate",options:{}}).then((function(a){t.$message.success("下载成功！")})).catch((function(a){t.$message.error("下载失败！")}))},beforeUpload:function(t){return this.fileList=[],this.fileList.push(t),!1},handleRemove:function(t){var a=this.fileList.indexOf(t),e=this.fileList.slice();e.splice(a,1),this.fileList=e},handleOk:function(){var t=this;this.form.validateFields((function(a,e){if(!a){var i=t.form.getFieldsValue();i.aae500s=i.aae500List.join(",");var l={url:"monitorMg/batchAudit/saveBatchAuditTask",data:i,autoQs:!1};t.Base.submit(null,l).then((function(a){t.$message.success("新建预审任务成功"),t.onCloseModal(),t.$emit("runFnQuery")})).catch((function(){t.$message.error("新建预审任务失败")}))}}))}}},v=g,y=e(1001),w=(0,y.Z)(v,m,p,!1,null,"c8f20836",null),k=w.exports,x={name:"runPreAuditWin",components:{importWin:k},data:function(){return{col:{xs:1,sm:2,md:3,lg:4,xl:6,xxl:12},modalVisible:!1,queryAae043:!1,sceneList:c,auditStatusList:d,aae043List:[],aae043info:{},preAae043List:[],preAae043:"无",tableData:[]}},methods:{onCloseModal:function(t){var a=this;this.queryAae043||(this.modalVisible=!this.modalVisible),this.tableData=[],this.aae043List=[],this.preAae043List=[],this.aae043info={},this.preAae043="无",this.modalVisible&&this.$nextTick((function(){a.fnQuery(),a.aae043List=t.aae043List,a.preAae043List=t.preAae043List,a.preAae043=t.preAae043,a.aae043info=t.aae043info}))},tablePageParams:function(){return this.form.getFieldsValue()},fnQueryAae043:function(){var t=this;this.queryAae043=!0,this.$emit("fnQueryAae043"),setTimeout((function(){t.queryAae043=!1}),1e3)},fnQuery:function(){this.$refs.gridPager.loadData()},fieldFormat:function(t,a){var e=t;return a.forEach((function(a,i){t===a.value&&(e=a.label)})),e},fnOpenImportWin:function(){var t={aae043List:this.aae043List,preAae043:this.preAae043,aae043info:this.aae043info};this.$refs.batchImportWin.onCloseModal(t)},fnGetPreCurrentAae043:function(){var t=this,a={url:"monitorMg/batchAudit/getPreCurrentAae043",data:{},autoQs:!1};this.Base.submit(null,a).then((function(a){t.preAae043=a.data.aae043}))},fnRunBatchAudit:function(t){var a=this,e=JSON.parse(t.inparam);e.aae500s&&(e.aae500List=e.aae500s.split(","));var i={url:"monitorMg/batchAudit/runBatchAudit",data:e,autoQs:!1};this.Base.submit(null,i).then((function(t){a.$message.success("审核批次号【"+e.aze001+"】正在异步审核中..."),a.fnQuery()}))},fnCountAuditResultData:function(t){var a=this,e={aze001:t.aze001},i={url:"monitorMg/batchAudit/countAuditResultData",data:e,autoQs:!1};this.Base.submit(null,i).then((function(t){a.$message.success("审核批次号【"+e.aze001+"】正在异步聚合数据中..."),a.fnQuery()}))},fnSetParentQueryCondition:function(t){this.$emit("fnSetQueryCondition",t),this.onCloseModal()},fnShowFieldInfo:function(t){this.$warning({title:"字段内容详情信息",content:t,width:800})},fnDeleteBatchData:function(t){var a=this,e={url:"monitorMg/batchAudit/deleteBatchData",data:{aze001:t.aze001,aae043:t.aae043,aae043Old:t.aae043Old},autoQs:!1};this.Base.submit(null,e).then((function(t){a.$message.success("删除成功"),a.fnQuery()})).catch((function(){a.$message.error("删除失败")}))}}},_=x,A=(0,y.Z)(_,o,s,!1,null,"af2a6432",null),S=A.exports,D=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",[e("ta-modal",{attrs:{visible:t.modalVisible,"destroy-on-close":!0,"mask-closable":!1,height:"700px",width:"1600px",footer:null},on:{cancel:t.onCloseModal}},[e("template",{slot:"title"},[e("div",{directives:[{name:"show",rawName:"v-show",value:"1"===t.aae101,expression:"aae101 === '1'"}]},[e("span",{staticStyle:{"margin-right":"30px","font-size":"20px","font-weight":"bolder"}},[t._v("合规差异患者")]),e("span",{staticStyle:{"margin-right":"20px"}},[t._v("变化类型: "+t._s(t.queryData.changeDesc))]),e("span",{staticStyle:{"margin-right":"20px"}},[t._v("规则序号: "+t._s(t.queryData.ykz032))]),e("span",{staticStyle:{"margin-right":"20px"}},[t._v("预审审核量: "+t._s(t.queryData.akc228))]),e("span",{staticStyle:{"margin-right":"20px"}},[t._v("实审审核量: "+t._s(t.queryData.akc228Old))]),e("span",{staticStyle:{"margin-right":"20px"}},[t._v("预审合规次数: "+t._s(t.queryData.akc228Pass))]),e("span",{staticStyle:{"margin-right":"20px"}},[t._v("实审合规次数: "+t._s(t.queryData.akc228PassOld))])]),e("div",{directives:[{name:"show",rawName:"v-show",value:"0"===t.aae101,expression:"aae101 === '0'"}]},[e("span",{staticStyle:{"margin-right":"30px","font-size":"20px","font-weight":"bolder"}},[t._v("违规差异患者")]),e("span",{staticStyle:{"margin-right":"20px"}},[t._v("变化类型: "+t._s(t.queryData.changeDesc))]),e("span",{staticStyle:{"margin-right":"20px"}},[t._v("规则序号: "+t._s(t.queryData.ykz032))]),e("span",{staticStyle:{"margin-right":"20px"}},[t._v("预审审核量: "+t._s(t.queryData.akc228))]),e("span",{staticStyle:{"margin-right":"20px"}},[t._v("实审审核量: "+t._s(t.queryData.akc228Old))]),e("span",{staticStyle:{"margin-right":"20px"}},[t._v("预审违规次数: "+t._s(t.queryData.akc228Fail))]),e("span",{staticStyle:{"margin-right":"20px"}},[t._v("实审违规次数: "+t._s(t.queryData.akc228FailOld))])])]),e("div",{staticStyle:{height:"100%"}},[e("ta-big-table",{ref:"dataTable",attrs:{data:t.tableData,border:!0,size:"small",resizable:"","highlight-hover-row":"",height:"auto","auto-resize":"",stripe:"","header-align":"center","show-overflow":!0},scopedSlots:t._u([{key:"topBar",fn:function(){return[e("ta-big-table-toolbar",[e("div",{attrs:{slot:"tools"},slot:"tools"},[t._v(" 次数一致的数据： "),e("ta-switch",{attrs:{"checked-children":"展示","un-checked-children":"隐藏",checked:t.switchFlag},on:{change:t.fnSwitchChange}})],1)])]},proxy:!0}])},[e("ta-big-table-column",{attrs:{type:"seq",title:"序号",width:"60px",align:"center"}}),e("ta-big-table-column",{attrs:{field:"aae500",title:"审核场景",width:"8%","collection-type":"AAE500"}}),e("ta-big-table-column",{attrs:{field:"akc192",title:"登记号",width:"8%"}}),e("ta-big-table-column",{attrs:{field:"akc191",title:"就诊号",width:"8%"}}),e("ta-big-table-column",{attrs:{field:"aac003",title:"患者姓名",width:"8%"}}),e("ta-big-table-column",{attrs:{field:"ake001",title:"医保项目编码",width:"15%"}}),e("ta-big-table-column",{attrs:{field:"ake002",title:"医保项目名称",width:"15%"}}),e("ta-big-table-column",{attrs:{field:"akc228Fail",title:"预审违规次数",width:"8%",visible:"0"===t.aae101},scopedSlots:t._u([{key:"default",fn:function(a){var i=a.row;return[e("a",{on:{click:function(a){return t.fnOpenPage(i,"2")}}},[t._v(t._s(i.akc228Fail))])]}}])}),e("ta-big-table-column",{attrs:{field:"akc228FailOld",title:"实审违规次数",width:"8%",visible:"0"===t.aae101},scopedSlots:t._u([{key:"default",fn:function(a){var i=a.row;return[e("a",{on:{click:function(a){return t.fnOpenPage(i,"1")}}},[t._v(t._s(i.akc228FailOld))])]}}])}),e("ta-big-table-column",{attrs:{field:"akc228Pass",title:"预审合规次数",width:"8%",visible:"1"===t.aae101},scopedSlots:t._u([{key:"default",fn:function(a){var i=a.row;return[e("a",{on:{click:function(a){return t.fnOpenPage(i,"2")}}},[t._v(t._s(i.akc228Pass))])]}}])}),e("ta-big-table-column",{attrs:{field:"akc228PassOld",title:"实审合规次数",width:"8%",visible:"1"===t.aae101},scopedSlots:t._u([{key:"default",fn:function(a){var i=a.row;return[e("a",{on:{click:function(a){return t.fnOpenPage(i,"1")}}},[t._v(t._s(i.akc228PassOld))])]}}])})],1)],1)],2)],1)},L=[],z={name:"differenceWin",components:{},data:function(){return{col:{xs:1,sm:2,md:3,lg:4,xl:6,xxl:12},modalVisible:!1,modalTitle:"差异患者",sceneList:c,tableData:[],queryData:{},aae101:"1",tableAllData:[],switchFlag:!0}},methods:{onCloseModal:function(t){var a=this;this.modalVisible=!this.modalVisible,this.tableData=[],this.queryData=t,this.aae101=t.aae101,this.modalVisible&&this.$nextTick((function(){a.fnQuery()}))},fnQuery:function(){var t=this,a={aze001:this.queryData.aze001,aae043:this.queryData.aae043,aae043Old:this.queryData.aae043Old,aae500:this.queryData.aae500,aae141:this.queryData.aae141,ake001:this.queryData.ake001,ykz032:this.queryData.ykz032,ykz108:this.queryData.ykz108,aze002Start:this.queryData.logData.aze002Start,aze002End:this.queryData.logData.aze002End},e={url:"monitorMg/batchAudit/getDifferencePersonData",data:a,autoQs:!1};this.Base.submit(null,e).then((function(a){t.tableAllData=a.data.dataVo,t.fnSwitchChange(t.switchFlag)})).catch((function(){t.$message.error("查询失败")}))},fnSwitchChange:function(t){this.switchFlag=t,t?("1"===this.aae101&&(this.tableData=this.tableAllData.filter((function(t){return t.akc228Pass!==t.akc228PassOld}))),"0"===this.aae101&&(this.tableData=this.tableAllData.filter((function(t){return t.akc228Fail!==t.akc228FailOld})))):this.tableData=this.tableAllData},fieldFormat:function(t,a){var e=t;return a.forEach((function(a,i){t===a.value&&(e=a.label)})),e},fnOpenPage:function(t,a){var e=this.queryData.aze001,i=this.queryData.aae043,l=this.queryData.aae043Old,n=this.queryData.aae500,r=this.queryData.ykz032,o=this.queryData.logData.aze002Start,s=this.queryData.logData.aze002End,c=this.queryData.logData.beginTime,u=this.queryData.logData.finishTime,d=l,f=o,h=s;"2"===a&&(d=i,f=c,h=u),this.Base.openTabMenu({id:t.aaz217+a,name:"审核疑点查询【"+t.akc191+"】",url:"querycommon.html#/auditResult?selectType=".concat(a)+"&ake001=".concat(t.ake001,"&aae500=").concat(n,"&ykz032=").concat(r)+"&aze001=".concat(e,"&startTime=").concat(f,"&endTime=").concat(h)+"&aae043=".concat(d,"&akc191=").concat(t.akc191,"&aae141=").concat(t.aae141,"&aae101=").concat(this.aae101),refresh:!1})}}},F=z,T=(0,y.Z)(F,D,L,!1,null,"175b7ffd",null),C=T.exports,E=e(88412),O=e(22722),q=e(55115);q.w3.prototype.Base=Object.assign(q.w3.prototype.Base,(0,r.Z)({},O.Z));var P={components:{TaTitle:E.Z,runPreAuditWin:S,differenceWin:C},data:function(){return{col:{xs:1,sm:2,md:2,lg:2,xl:4,xxl:4},tableData:[],aae043List:[],aae043info:{},preAae043List:[],filterList:"",akc226FlagList:u,changeDescList:f,dataFilterTypeList:h,modalVisible:!1,formShowAll:!0,batchLogList:[],logList:[],validRuleTypeList:["1","2"],showValidFlag:!1,checkTagList:[{tooltipTitle:"校验（无变化、变严、变宽、有更新记录）数据：预审审核量 ≠ 实审审核量",key:"3",color0_0:"#E8F6E1",color0_1:"#FFF2F0",color1_0:"#52C41A",color1_1:"#F2A8B1",content:"审核量不一致",num:0}],analysisTagList:[{tooltipTitle:"分析无变化的数据：预审违规次数 ≠ 实审违规次数",key:"4",color0_0:"#E8F6E1",color0_1:"#EBF5FF",color1_0:"#52C41A",color1_1:"#409EFF",content:"违规次数不一致",num:0},{tooltipTitle:"分析无变化的数据：预审合规次数 ≠ 实审合规次数",key:"5",color0_0:"#E8F6E1",color0_1:"#EBF5FF",color1_0:"#52C41A",color1_1:"#409EFF",content:"合规次数不一致",num:0},{tooltipTitle:"分析变严的数据：预审违规次数 < 实审违规次数",key:"6",color0_0:"#E8F6E1",color0_1:"#EBF5FF",color1_0:"#52C41A",color1_1:"#409EFF",content:"预审违规次数<实审违规次数",num:0},{tooltipTitle:"分析变宽的数据：预审违规次数 > 实审违规次数",key:"7",color0_0:"#E8F6E1",color0_1:"#EBF5FF",color1_0:"#52C41A",color1_1:"#409EFF",content:"预审违规次数>实审违规次数",num:0},{tooltipTitle:"分析停用的数据：预审审核量 > 0",key:"8",color0_0:"#E8F6E1",color0_1:"#EBF5FF",color1_0:"#52C41A",color1_1:"#409EFF",content:"停用规则仍有审核量",num:0},{tooltipTitle:"分析新增的数据：预审审核量 <= 0",key:"9",color0_0:"#E8F6E1",color0_1:"#EBF5FF",color1_0:"#52C41A",color1_1:"#409EFF",content:"新增规则无审核量",num:0}]}},computed:{formBoxStyle:function(){return this.formShowAll?{height:"auto"}:{height:"50px"}},tableDivStyle:function(){return this.showValidFlag?{height:"78%"}:{height:"93%"}}},mounted:function(){var t=this,a=["5","6","7","4","3","18","16","17","2"];b.Z.getAa01AAE500StartStop({aaz499s:JSON.stringify(a)},(function(a){var e=a.data.aae500List;e.includes(12)&&e.push(13),t.filterList=e.join(","),t.form.setFieldsValue({aae500:e[0].toString()})})),this.fnQueryBatchLogList()},methods:{fnReset:function(){var t=this;this.form.resetFields(),this.$nextTick((function(){t.formShowAll=!0,t.fnQuery()}))},tablePageParams:function(){var t=this.form.getFieldsValue(),a=t.aze001,e=this.logList.filter((function(t){return t.aze001===a}))[0];return t.aae043=e.aae043,t.aae043Old=e.aae043Old,!this.showValidFlag&&this.validRuleTypeList.length>0&&(this.validRuleTypeList=this.validRuleTypeList.filter((function(t){return"1"!==t&&"2"!==t&&"3"!==t}))),t.validRuleTypeList=this.validRuleTypeList,t.changeDescList=t.changeDesc,t.changeDesc=null,t},fnQuery:function(){var t=this;this.form.setFieldsValue({validRuleTypeList:void 0}),this.form.validateFields((function(a,e){a||(t.fnValidBatchResultData(),t.$refs.gridPager.loadData())}))},formShowAllChange:function(){this.formShowAll=!this.formShowAll},fieldFormat:function(t,a){var e=t;return a.forEach((function(a,i){t===a.value&&(e=a.label)})),e},fnOpenAuditWin:function(){this.fnQueryAae043()},fnQueryAae043:function(){var t=this,a={url:"monitorMg/batchAudit/getNewTaskInfo",data:{},autoQs:!1};this.Base.submit(null,a).then((function(a){t.aae043info=a.data.taskInfo,t.aae043List=t.aae043info.aae043OldList.map((function(t){return{value:t,label:t}})),t.preAae043List=t.aae043info.aae043List.map((function(t){return{value:t,label:t}}));var e={aae043List:t.aae043List,preAae043List:t.preAae043List,aae043info:t.aae043info,preAae043:t.aae043info.aae043};t.$refs.runPreAuditWin.onCloseModal(e)}))},fnQueryPreAae043List:function(){var t=this,a={url:"monitorMg/batchAudit/queryPreAae043List",data:{},autoQs:!1};this.Base.submit(null,a).then((function(a){var e=a.data.aae043List;t.preAae043List=e.map((function(t){return{value:t,label:t}}))}))},fnExportData:function(){var t=this,a=this,e=this.tablePageParams();if(e){var i,l=[],r=this.$refs.dataTable.getTableColumn().fullColumn,o=(0,n.Z)(r);try{for(o.s();!(i=o.n()).done;){var s=i.value;"seq"!==s.type&&"operate"!==s.property&&l.push({header:s.title,key:s.property,width:20})}}catch(f){o.e(f)}finally{o.f()}var c=r.map((function(t){return t.property})),u=[{codeType:"AAC004",columnKey:"aac004"},{codeType:"AAE500",columnKey:"aae500"},{codeType:"AAE141",columnKey:"aae141"},{columnKey:"validRuleType",customCollection:function(t,e){if(t.value){var i=a.analysisTagList.filter((function(a){return-1!==t.value.indexOf(a.key)}));i.length>0?t.value=i.map((function(t){return t.content})).join("、"):t.value="无"}else t.value="无"}}],d=u.filter((function(t){return c.includes(t.columnKey)}));this.Base.submit(null,{url:"monitorMg/batchAudit/exportCompareResultData",data:e,autoValid:!1},{successCallback:function(a){var e=a.data.data,i={fileName:"规则预上线监测查询结果表",sheets:[{name:"worksheet1",column:{complex:!1,columns:l},rows:e,codeList:d}]};t.Base.generateExcel(i)},failCallback:function(a){t.$message.error("导出失败")}})}},fnSetQueryCondition:function(t){var a=this;this.fnQueryBatchLogList(),this.$nextTick((function(){a.form.setFieldsValue({aze001:t.aze001})}))},fnQueryBatchLogList:function(){var t=this,a={url:"monitorMg/batchAudit/queryBatchLogList",data:{},autoQs:!1};this.Base.submit(null,a).then((function(a){var e=a.data.logList;t.logList=e,t.batchLogList=e.map((function(t){var a=t.aze001,e=t.aae043,i=t.aae043Old,l="批次号:"+a+"--预审:"+e+"--实审:"+i;return{value:a,label:l}}))}))},handleTagChange:function(t,a){var e=this;this.form.validateFields((function(i,l){i||(a?e.validRuleTypeList.push(t):e.validRuleTypeList=e.validRuleTypeList.filter((function(a){return a!==t})),e.$nextTick((function(){e.$refs.gridPager.loadData()})))}))},fnValidBatchResultData:function(){var t=this,a={url:"monitorMg/batchAudit/validBatchResultData",data:this.tablePageParams(),autoQs:!1};this.Base.submit(null,a).then((function(a){var e=a.data.dataVo;t.showValidFlag=e.validRuleType1+e.validRuleType2+e.validRuleType3>0,t.checkTagList.forEach((function(t){t.num=e["validRuleType"+t.key]})),t.analysisTagList.forEach((function(t){t.num=e["validRuleType"+t.key]}))})).catch((function(){t.$message.error("查询统计数据失败")}))},fnTagStyle:function(t){var a={backgroundColor:t.color0_0},e=t.num;return this.validRuleTypeList.indexOf(t.key)>-1?a.backgroundColor=e>0?t.color1_1:t.color1_0:a.backgroundColor=e>0?t.color0_1:t.color0_0,a},fnOpenAuditResultPage:function(t,a){var e=t.aze001,i=t.aae043,l=t.aae043Old,n=this.logList.filter((function(t){return t.aze001===e}))[0],r=n.aze002Start,o=n.aze002End,s=n.beginTime,c=n.finishTime,u=l,d=r,f=o;"2"===a&&(u=i,d=s,f=c),this.Base.openTabMenu({id:t.id+a,name:"审核疑点查询【"+t.ake002+"】",url:"querycommon.html#/auditResult?selectType=".concat(a)+"&ake001=".concat(t.ake001,"&aae500=").concat(t.aae500,"&ykz032=").concat(t.ykz032)+"&aze001=".concat(e,"&startTime=").concat(d,"&endTime=").concat(f)+"&aae043=".concat(u,"&aae141=").concat(t.aae141),refresh:!1})},editClosed:function(t){var a=this,e=t.row,i=(t.rowIndex,t.$rowIndex,t.column);t.columnIndex,t.$columnIndex;this.$refs.dataTable.validate([e]).then((function(){a.$refs.dataTable.isUpdateByRow(e)&&a.fnSaveRowData(e)})).catch((function(){a.$refs.dataTable.revertData([e],i.property)}))},fnSaveRowData:function(t){var a=this,e={url:"monitorMg/batchAudit/saveBatchResultData",data:t,autoQs:!1};this.Base.submit(null,e).then((function(t){a.$message.success("保存成功");var e=a.$refs.dataTable.getTableData().tableData;a.$refs.dataTable.loadData(e)})).catch((function(){a.$message.error("保存失败")}))},expectDescFilterMethod:function(t){t.value,t.option;var a=t.row,e=t.column;return this.filterMethod("expectDesc",a,e)},filterMethod:function(t,a,e){var i=[],l=!1;e.filters.forEach((function(t){t.checked&&i.push(t.data)}));for(var n=0;n<i.length;n++)if(a[t]&&a[t].indexOf(i[n])>-1){l=!0;break}return l},fnOpenDifferenceWin:function(t,a){var e=this.logList.filter((function(a){return a.aze001===t.aze001}))[0];t.aae101=a;var i=(0,r.Z)((0,r.Z)({},t),{},{aae101:a,logData:e});this.$refs.differenceWin.onCloseModal(i)}}},B=P,R=(0,y.Z)(B,i,l,!1,null,"0ad6287c",null),$=R.exports},36766:function(t,a,e){"use strict";var i=e(66586);a["Z"]=i.Z},26263:function(t,a,e){"use strict";e.d(a,{s:function(){return i},x:function(){return l}});var i=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("h3",{staticStyle:{"font-weight":"normal"}},[e("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),e("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},l=[]},66586:function(t,a){"use strict";var e={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};a["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:e}}}},83231:function(t,a,e){"use strict";var i=e(48534);e(36133);function l(t){return t?t.akb020&&t.aaz263?"personal":t.akb020&&t.aaz309&&t.aaz307?"group":t.akb020&&t.aaz307?"department":t.akb020?"hospital":"all":"all"}function n(t){return r.apply(this,arguments)}function r(){return r=(0,i.Z)(regeneratorRuntime.mark((function t(a){var e,i,n,r,o,s,c,u,d,f,h,m;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Base.submit(null,{url:"/miimCommonRead/queryDataPermission",showPageLoading:!1,data:{jobnumber:a},autoValid:!0});case 2:return e=t.sent,i=new Set,n=new Set,e.data.permission.forEach((function(t){var a=l(t);"hospital"===a&&i.add(t.akb020),"department"===a&&n.add(t.aaz307)})),r=e.data.permission.filter((function(t){return"department"===l(t)||!n.has(t.aaz307)})).filter((function(t){return"hospital"===l(t)||!i.has(t.akb020)})),o=new Set(r.map((function(t){return t.akb020})).filter((function(t){return null!=t}))),s=new Set(r.map((function(t){return t.aaz307})).filter((function(t){return null!=t}))),c=new Set(r.map((function(t){return t.aaz309})).filter((function(t){return null!=t}))),u=new Set(r.map((function(t){return t.aaz263})).filter((function(t){return null!=t}))),d=!1,f=!1,h=!1,m=!1,1===o.size&&(d=!0),1===s.size&&1===o.size&&(f=!0),1===s.size&&1===o.size&&1===c.size&&(h=!0),1===o.size&&0===s.size&&1===u.size&&(m=!0),t.abrupt("return",{akb020Set:o,aaz307Set:s,aaz263Set:u,aaz309Set:c,akb020Disable:d,aaz307Disable:f,aaz263Disable:m,aaz309Disable:h});case 20:case"end":return t.stop()}}),t)}))),r.apply(this,arguments)}function o(t,a){Base.submit(null,{url:"miimCommonRead/getAa01AAE500StartStop",data:t},{successCallback:function(t){return a(t)}})}function s(t,a){Base.submit(null,{url:"miimCommonRead/insertTableColumShow",data:t,autoValid:!1},{successCallback:function(t){return a("success")},failCallback:function(t){return a("fail")}})}var c={zbx01:{footer:function(){return"统筹金总额"}},zbx02:{footer:function(){return"基金支出总额"}},zbx03:{footer:function(){return"总费用"}},zbx04:{footer:function(){return"总住院日"}}};a["Z"]={permissionCheck:n,getAa01AAE500StartStop:o,insertTableColumShow:s,props:c,moneyNumFormat:function(t){var a=t.cellValue;return a||"—"}}},55382:function(){},61219:function(){}}]);
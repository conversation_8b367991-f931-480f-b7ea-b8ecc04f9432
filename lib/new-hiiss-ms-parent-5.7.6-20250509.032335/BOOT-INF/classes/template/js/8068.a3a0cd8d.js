(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[8068],{88412:function(t,e,a){"use strict";var i=a(26263),l=a(36766),r=a(1001),n=(0,r.Z)(l.Z,i.s,i.x,!1,null,"5e7ef0ae",null);e["Z"]=n.exports},54588:function(t,e,a){"use strict";a.r(e),a.d(e,{default:function(){return b}});var i=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("div",{staticClass:"fit"},[i("ta-border-layout",{attrs:{"layout-type":"fixTop"},scopedSlots:e._u([{key:"header",fn:function(){return[i("ta-form",{staticStyle:{"margin-top":"20px"},attrs:{autoFormCreate:e.autoFormCreate,col:3,formLayout:!0}},[i("ta-form-item",{attrs:{require:!1,showInfo:!0,"field-decorator-id":"patientInfo",label:"患者信息","label-col":{span:7},span:5,"wrapper-col":{span:17}}},[i("ta-input",{attrs:{allowClear:"",placeholder:"请输入就诊号或姓名"}})],1),i("ta-form-item",{attrs:{require:!1,showInfo:!0,"field-decorator-id":"ake001",label:"医保项目信息","label-col":{span:7},span:5,"wrapper-col":{span:17}}},[i("ta-input",{attrs:{allowClear:"",placeholder:"请输入医保项目编码或名称"}})],1),i("ta-form-item",{attrs:{require:!1,showInfo:!0,"field-decorator-id":"aae040",label:"添加时间","label-col":{span:7},span:5,"init-value":e.rangeValue,"wrapper-col":{span:17}}},[i("ta-range-picker",{attrs:{"allow-one":!0}})],1),i("ta-form-item",{attrs:{require:!1,showInfo:!0,"field-decorator-id":"medtype",label:"就诊类型","label-col":{span:7},span:5,"wrapper-col":{span:17}}},[i("ta-select",{attrs:{"show-search":!0,allowClear:"",options:e.typeList,placeholder:"请选择就诊类型"}})],1),i("ta-form-item",{staticStyle:{"text-align":"right"},attrs:{require:!1,showInfo:!0,label:" ","label-col":{span:6},span:4,"wrapper-col":{span:18}}},[i("ta-button",{staticStyle:{"margin-right":"10px"},attrs:{type:"primary"},on:{click:e.queryTableData}},[e._v("查询 ")]),i("ta-button",{staticStyle:{"margin-right":"10px"},on:{click:e.fnReset}},[e._v("重置 ")])],1)],1)]},proxy:!0}])},[i("ta-card",{staticClass:"fit"},[i("ta-title",{staticStyle:{flex:"none",padding:"1px"},attrs:{title:"白名单列表"}},[i("div",{staticStyle:{float:"right"}},[i("ta-button",{attrs:{type:"primary"},on:{click:function(t){return e.fnAdd()}}},[e._v("添加")]),i("ta-button",{attrs:{type:"primary"},on:{click:function(t){return e.fnDelete("batch")}}},[e._v("批量删除")])],1)]),i("ta-big-table",{ref:"infoTableRef",attrs:{data:e.tableData,height:"95%","export-config":{},"import-config":{},border:"","highlight-hover-row":"","highlight-current-row":"","auto-resize":"","show-overflow":"",resizable:"","keep-source":"","empty-text":"-"}},[i("ta-big-table-column",{attrs:{width:50,type:"checkbox"}}),i("ta-big-table-column",{attrs:{width:50,title:"序号",type:"seq"}}),i("ta-big-table-column",{attrs:{align:"center",field:"akc191",sortable:"","header-align":"center",title:"就诊号"}}),i("ta-big-table-column",{attrs:{align:"center",field:"ake001",sortable:"","header-align":"center",title:"医保项目编码"}}),i("ta-big-table-column",{attrs:{align:"center",field:"ake002",sortable:"","header-align":"center",title:"医保项目名称"}}),i("ta-big-table-column",{attrs:{align:"center",field:"medtype",sortable:"","header-align":"center",title:"就诊类型"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s("0"===a.medtype?"门诊":"住院")+" ")]}}])}),i("ta-big-table-column",{attrs:{align:"center",field:"aae040",sortable:"","header-align":"center",title:"添加时间"}}),i("ta-big-table-column",{attrs:{width:150,align:"center","header-align":"center",title:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row,l=t.rowIndex;return[[i("a",{on:{click:function(t){return e.editSave(a,l)}}},[e._v(" 编辑 ")]),i("ta-divider",{attrs:{type:"vertical"}}),i("a",{on:{click:function(t){return e.fnDelete(a)}}},[e._v(" 删除 ")])]]}}])}),i("template",{slot:"bottomBar"},[i("ta-pagination",{ref:"gridPager",staticStyle:{"text-align":"right","margin-right":"120px"},attrs:{dataSource:e.tableData,defaultPageSize:200,pageSizeOptions:["30","50","100","200","500"],params:e.getParam,url:"auditWhitelist/queryAuditWhitelistInfoPage"},on:{"update:dataSource":function(t){e.tableData=t},"update:data-source":function(t){e.tableData=t}}}),i("ta-button",{staticStyle:{position:"absolute",right:"20px",bottom:"12px"},attrs:{type:"primary",icon:"upload"},on:{click:e.exportExcel}},[e._v("导出 ")])],1)],2)],1)],1),i("ta-modal",{attrs:{title:"add"===e.modal?"白名单添加":"白名单编辑",visible:e.addWhiteList,height:280,width:500},on:{ok:e.handleOk,cancel:e.handleCancel}},[i("ta-form",{attrs:{"auto-form-create":function(e){return t.form1=e},layout:"horizontal","form-layout":!0,"label-width":"120px",col:{xs:1,sm:1,md:1,lg:1,xl:1,xxl:1}}},[i("ta-form-item",{attrs:{label:"就诊类型","init-value":"1","field-decorator-id":"medtype","label-width":"93px",require:""}},[i("ta-select",{staticStyle:{width:"100%"},attrs:{options:e.typeList,placeholder:"请选择就诊类型"},on:{select:e.medtypeChange}})],1),i("ta-form-item",{attrs:{label:"患者信息","field-decorator-id":"currPers","label-width":"93px",require:""}},[i("ta-input",{attrs:{placeholder:"请输入患者信息","allow-clear":""}})],1),i("ta-form-item",{attrs:{"field-decorator-id":"ake001",label:"医保项目编码",require:""}},[i("ta-suggest",{staticStyle:{width:"100%"},attrs:{"data-source":e.ake001List,placeholder:"请输入医保项目编码或名称","table-title-map":new Map([["value",{name:"医保项目编码",style:{minWidth:"120px"}}],["label",{name:"医保项目名称",style:{minWidth:"120px"}}]]),"option-config":e.optionConfig2,"dropdown-match-select-width":!1,"dropdown-style":{width:"450px"}},on:{select:e.onSelect2,search:e.handleSearch2},scopedSlots:e._u([{key:"body-cell",fn:function(t){t.cellValue;var a=t.column,l=(t.columnIndex,t.row);t.rowIndex;return[i("div",["医保项目编码"===a.name?i("span",[e._v(e._s(l.value))]):e._e(),"医保项目名称"===a.name?i("span",[e._v(e._s(l.label))]):e._e()])]}}])})],1),i("ta-form-item",{attrs:{label:"医保项目名称","init-value":e.ake001Select.label,require:""}},[i("ta-input",{attrs:{placeholder:"输入医保项目编码,精确匹配名称",value:e.ake001Select.label,disabled:""}})],1)],1)],1)],1)},l=[],r=a(66347),n=a(95082),o=a(88412),s=a(22722),c=a(55115),u=a(36797),d=a.n(u);c.w3.prototype.Base=Object.assign(c.w3.prototype.Base,(0,n.Z)({},s.Z));var f={name:"whiteList",components:{TaTitle:o.Z},data:function(){return{tableData:[],ake001List:[],peopleList:[],rangeValue:[this.Base.getMoment((new Date).toISOString().slice(0,8)+"-01","YYYY-MM-DD"),d()()],isAdd:!0,modal:"add",drawerVisible:!1,addWhiteList:!1,ake001Select:{},currPersSelect:{},optionConfig:{value:"value",label:function(t){return"".concat(t.akc191," ").concat(t.aac003," ").concat(t.aae376?t.aae376:"未知科室")}},optionConfig2:{value:"value",label:function(t){return"".concat(t.value)}},editRow:{},typeList:[{label:"门诊",value:"0"},{label:"住院",value:"1"}]}},mounted:function(){this.queryTableData()},methods:{moment:d(),medtypeChange:function(t){"edit"==this.modal&&(this.editRow.bfmedtype=t,this.handleSearch(this.editRow.akc191))},filterOption:function(t,e){return e.componentOptions.children[0].text.toLowerCase().indexOf(t.toLowerCase())>=0},fnQueryake001List:function(){var t=this,e={url:"/miimCommonRead/queryAke001List",autoValid:!0},a={successCallback:function(e){t.ake001List=e.data.ake001},failCallback:function(e){t.$message.error("规则大类列表数据加载失败")}};this.Base.submit(null,e,a)},handleOk:function(){var t=this;this.form1.validateFields((function(e){if(!e){var a=t.form1.getFieldsValue(),i={};"edit"===t.modal&&(i.id=t.editRow.id),i.aac003=t.currPersSelect.aac003,i.akc190=t.currPersSelect.akc190,i.akc191=a.currPers,i.ake001=t.ake001Select.value,i.ake002=t.ake001Select.label,i.medtype=a.medtype;var l=[],r=[];if(i.akc191||l.push("患者就诊号为空"),i.ake002||r.push("规则大类名称为空"),i.ake001||l.push("规则大类编码为空"),l.length>0){var n=l.join("，");return void t.$message.error("选择的数据有误：".concat(n))}if(r.length>0){var o=r.join("，");t.$message.warning("选择的数据有空的：".concat(o))}t.Base.submit(null,{url:"add"===t.modal?"auditWhitelist/addAuditWhitelistInfo":"auditWhitelist/editAuditWhitelistInfo",data:i,autoValid:!1},{successCallback:function(e){t.$message.success("edit"===t.modal?"修改成功!":"添加成功！"),t.form1.resetFields(),t.ake001List=[],t.ake001Select={},t.editRow={},t.currPersSelect={},t.addWhiteList=!1,t.queryTableData()},failCallback:function(e){t.$message.error("edit"===t.modal?"修改失败!":"添加失败！")}})}}))},handleCancel:function(){this.form1.resetFields(),this.ake001List=[],this.ake001Select={},this.editRow={},this.currPersSelect={},this.addWhiteList=!1,this.queryTableData()},onSelect:function(t,e,a){this.currPersSelect=e},onSelect2:function(t,e,a){this.ake001Select=e},onake001Select:function(t,e){this.ake001Select=t},handleSearch:function(t){var e,a=this;if("add"==this.modal){var i=this.form1.getFieldsValue();e=i.medtype}else e=this.editRow.bfmedtype;t&&this.Base.submit(null,{url:"/nightAudit/getDicDoctor",data:{doctorName:t,medType:e}},{successCallback:function(t){a.peopleList=t.data.list},failCallback:function(t){a.$message.error("查询患者信息失败！")}})},handleSearch2:function(t){var e=this;t&&this.Base.submit(null,{url:"/miimCommonRead/queryAke001List",data:{ake001:t.trim()}},{successCallback:function(t){e.ake001List=t.data.list},failCallback:function(t){e.$message.error("查询患者信息失败！")}})},fnAdd:function(){var t=this;this.modal="add",this.$nextTick((function(){t.addWhiteList=!0}))},fnDelete:function(t){var e=this,a=[];if(t.id)a=[t.id];else{var i=this.$refs.infoTableRef.getCheckboxRecords();if(0===i.length)return void this.$message.error("请选择要删除的数据！");a=i.map((function(t){return t.id}))}this.$confirm({title:"确认删除选择的数据?",okText:"删除",okType:"danger",cancelText:"取消",onOk:function(){e.Base.submit(null,{url:"auditWhitelist/deleteById",data:{ids:a},autoValid:!1},{successCallback:function(t){e.$message.success("删除成功！"),e.queryTableData()},failCallback:function(t){e.$message.error("删除失败！")}})},onCancel:function(){e.$message.info("用户取消操作")}})},editSave:function(t,e){var a=this;this.editRow=t,this.editRow.bfmedtype=t.medtype,this.modal="edit",this.handleSearch(t.akc191),this.handleSearch2(t.ake001),this.addWhiteList=!0,this.$nextTick((function(){a.form1.setFieldsValue({currPers:t.akc191,ake001:t.ake001,medtype:t.medtype}),a.currPersSelect.aac003=a.editRow.aac003,a.currPersSelect.akc190=a.editRow.akc190,a.currPersSelect.akc191=a.editRow.akc191,a.ake001Select.value=a.editRow.ake001,a.ake001Select.label=a.editRow.ake002}))},queryTableData:function(){this.$refs.gridPager.loadData()},fnReset:function(){this.baseInfoForm.resetFields(),this.queryTableData()},autoFormCreate:function(t){this.baseInfoForm=t},getParam:function(){var t=this.baseInfoForm.getFieldsValue(),e=t.aae040;if(e){if(e[0]&&!e[1])throw this.$message.error("请选择完整时间范围！"),new Error("请选择完整时间范围！");if(e[1]&&!e[0])throw this.$message.error("请选择完整时间范围！"),new Error("请选择完整时间范围！")}return e&&(e[0]&&(t.startDate=e[0].format("YYYY-MM-DD")),e[1]&&(t.endDate=e[1].format("YYYY-MM-DD"))),t},exportExcel:function(){var t,e=this,a=[],i=this.$refs.infoTableRef.getColumns(),l=(0,r.Z)(i);try{for(l.s();!(t=l.n()).done;){var n=t.value;"序号"!==n.title&&"操作"!==n.title&&"checkbox"!==n.type&&a.push({header:n.title,key:n.property,width:20})}}catch(s){l.e(s)}finally{l.f()}var o=[{columnKey:"medtype",customCollection:function(t,e){"0"==t.value?t.value="门诊":t.value="住院"}}];this.Base.submit(null,{url:"auditWhitelist/queryAuditWhitelistInfoList",data:this.getParam(),autoValid:!1},{successCallback:function(t){var i={fileName:"审核白名单数据表",sheets:[{name:"worksheet1",column:{complex:!1,columns:a},rows:t.data.AuditWhitelistList,codeList:o}]};e.Base.generateExcel(i)},failCallback:function(t){e.$message.error("审核白名单数据表导出失败")}})}}},h=f,m=a(1001),p=(0,m.Z)(h,i,l,!1,null,"6a801d0c",null),b=p.exports},36766:function(t,e,a){"use strict";var i=a(66586);e["Z"]=i.Z},26263:function(t,e,a){"use strict";a.d(e,{s:function(){return i},x:function(){return l}});var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},l=[]},66586:function(t,e){"use strict";var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};e["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}},55382:function(){},61219:function(){}}]);
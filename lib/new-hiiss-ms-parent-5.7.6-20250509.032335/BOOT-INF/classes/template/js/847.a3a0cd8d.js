(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[847],{88412:function(t,e,a){"use strict";var o=a(26263),i=a(36766),l=a(1001),r=(0,l.Z)(i.Z,o.s,o.x,!1,null,"5e7ef0ae",null);e["Z"]=r.exports},55551:function(t,e,a){"use strict";a.r(e),a.d(e,{default:function(){return C}});var o=function(){var t=this,e=this,a=e.$createElement,o=e._self._c||a;return o("div",{staticClass:"fit"},[o("ta-border-layout",{staticStyle:{},attrs:{"layout-type":"fixTop"}},[o("div",{staticStyle:{height:"auto","padding-top":"10px"},attrs:{slot:"header"},slot:"header"},[o("ta-form",{attrs:{autoFormCreate:function(e){t.form=e},col:e.col,layout:"horizontal","label-width":"80px",formLayout:!0}},[o("ta-form-item",{attrs:{label:"显示方式",fieldDecoratorId:"showMethod",hidden:!0}},[o("ta-input")],1),o("ta-form-item",{attrs:{fieldDecoratorId:"start",fieldDecoratorOptions:{initialValue:e.defaultStartDate},require:{message:"必输项!"}}},[o("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("开始时间")]),o("ta-date-picker",{attrs:{disabledDate:e.disabledStartDate}})],1),o("ta-form-item",{attrs:{fieldDecoratorId:"end",fieldDecoratorOptions:{initialValue:e.defaultDate},require:{message:"必输项!"}}},[o("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("结束时间")]),o("ta-date-picker",{attrs:{disabledDate:e.disabledEndDate}})],1),o("ta-form-item",{attrs:{fieldDecoratorId:"aae500",require:{message:"必输项!"}}},[o("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("审核场景")]),o("ta-select",{attrs:{placeholder:"请选择审核场景","collection-type":"AAE500","collection-filter":e.filterList,reverseFilter:!0}})],1),o("ta-form-item",{attrs:{fieldDecoratorId:"aae141"}},[o("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("医保类型")]),o("ta-select",{attrs:{placeholder:"医保类型筛选","collection-type":"AAE141",allowClear:""}})],1),o("ta-form-item",{attrs:{fieldDecoratorId:"ape800"}},[o("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("规则等级")]),o("ta-select",{attrs:{placeholder:"规则等级筛选",options:e.gzdj,allowClear:""}})],1),o("ta-form-item",{attrs:{fieldDecoratorId:"areaflag"}},[o("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("院区标识")]),o("ta-select",{attrs:{placeholder:"院区标识筛选",options:e.hosList,"allow-clear":""}})],1),o("ta-form-item",{attrs:{fieldDecoratorId:"akb020",hidden:!0}},[o("ta-input",{staticStyle:{width:"237px"}})],1),o("ta-form-item",{attrs:{fieldDecoratorId:"aaz307",hidden:!0}}),o("ta-form-item",{attrs:{fieldDecoratorId:"aaz263",hidden:!0}}),o("div",{staticStyle:{display:"flex","margin-left":"50px","margin-right":"10px","margin-bottom":"5px",float:"right"}},[o("ta-button",{attrs:{icon:"search",type:"primary"},on:{click:e.handleQuery}},[e._v("查询")]),o("ta-button",{attrs:{type:"default",icon:"redo"},on:{click:e.fnReset}},[e._v("重置")])],1)],1)],1),o("div",{staticClass:"fit content-box"},[o("ta-title",{attrs:{title:"审核提醒情况统计结果"}}),o("ta-button-group",{attrs:{align:"left"}},[o("ta-button",{directives:[{name:"show",rawName:"v-show",value:e.yyButtonTitleShow,expression:"yyButtonTitleShow"}],attrs:{type:e.yyButtonColor},on:{click:function(t){return e.handleChangeTjfs("yy")}}},[e._v(e._s(e.yyButtonTitle))]),o("ta-button",{attrs:{type:e.ksButtonColor},on:{click:function(t){return e.handleChangeTjfs("ks")}}},[e._v("按科室")]),o("ta-button",{attrs:{type:e.ysButtonColor},on:{click:function(t){return e.handleChangeTjfs("ys")}}},[e._v("按医生")]),o("ta-button",{attrs:{type:e.gzButtonColor},on:{click:function(t){return e.handleChangeTjfs("gz")}}},[e._v("按规则")])],1),o("div",{staticClass:"content-table"},[o("ta-big-table",{directives:[{name:"show",rawName:"v-show",value:"yy"==e.showMethod,expression:"showMethod=='yy'"}],ref:"hiddenTable0",attrs:{columns:e.tableColumns0,data:e.tableData0,pagination:!1,border:"",height:"auto","highlight-hover-row":"","auto-resize":"","show-overflow":"",resizable:""},on:{"cell-click":e.fnCustomYyRow},scopedSlots:e._u([{key:"monney",fn:function(t){var a=t.row;return[o("span",[e._v(e._s(null==a.txfy?"0":a.txfy)+" 元")])]}},{key:"akb021",fn:function(t){var a=t.row;return[o("ta-button",{attrs:{size:"small",type:"link"}},[o("span",{staticStyle:{"text-decoration":"underline"}},[e._v(e._s(a.akb021))])])]}}])}),o("ta-big-table",{directives:[{name:"show",rawName:"v-show",value:"ks"==e.showMethod,expression:"showMethod=='ks'"}],ref:"hiddenTable1",attrs:{columns:e.tableColumns1,data:e.tableData1,border:"",height:"auto","highlight-hover-row":"","auto-resize":"","show-overflow":"",resizable:""},on:{"cell-click":e.fnCustomKsRow},scopedSlots:e._u([{key:"monney",fn:function(t){var a=t.row;return[o("span",[e._v(e._s(null==a.txfy?"0":a.txfy)+" 元")])]}},{key:"aae386",fn:function(t){var a=t.row;return[o("ta-button",{attrs:{size:"small",type:"link"}},[o("span",{staticStyle:{"text-decoration":"underline"}},[e._v(e._s(a.aae386))])])]}}])}),o("ta-big-table",{directives:[{name:"show",rawName:"v-show",value:"ys"==e.showMethod,expression:"showMethod=='ys'"}],ref:"hiddenTable2",attrs:{columns:e.tableColumns2,data:e.tableData2,border:"",height:"auto","highlight-hover-row":"","auto-resize":"","show-overflow":"",resizable:""},on:{"cell-click":e.fnCustomYsRow},scopedSlots:e._u([{key:"monney",fn:function(t){var a=t.row;return[o("span",[e._v(e._s(null==a.txfy?"0":a.txfy)+" 元")])]}},{key:"aac003",fn:function(t){var a=t.row;return[o("ta-button",{attrs:{size:"small",type:"link"}},[o("span",{staticStyle:{"text-decoration":"underline"}},[e._v(e._s(a.aac003))])])]}}])}),o("ta-big-table",{directives:[{name:"show",rawName:"v-show",value:"gz"==e.showMethod,expression:"showMethod=='gz'"}],ref:"hiddenTable3",attrs:{columns:e.tableColumns3,data:e.tableData3,border:"",height:"auto","highlight-hover-row":"","auto-resize":"","show-overflow":"",resizable:""},on:{"cell-click":e.fnCustomRow},scopedSlots:e._u([{key:"monney",fn:function(t){var a=t.row;return[o("span",[e._v(e._s(null==a.txfy?"0":a.txfy)+" 元")])]}}])})],1),o("div",{staticClass:"content-box-footer"},[o("ta-button",{staticStyle:{float:"right","margin-top":"10px","margin-right":"10px"},attrs:{type:"primary",icon:"download"},on:{click:e.handleExportCurData}},[e._v("导出")]),o("ta-pagination",{directives:[{name:"show",rawName:"v-show",value:"yy"==e.showMethod,expression:"showMethod=='yy'"}],ref:"gridPager0",staticStyle:{float:"right","margin-right":"10px"},style:{marginTop:"10px"},attrs:{showSizeChanger:"",showQuickJumper:"",dataSource:e.tableData0,params:e.pageParams,defaultPageSize:100,pageSizeOptions:["100","200","500","1000","1500"],url:"statisticalReport/queryByPage"},on:{"update:dataSource":function(t){e.tableData0=t},"update:data-source":function(t){e.tableData0=t}}}),o("ta-pagination",{directives:[{name:"show",rawName:"v-show",value:"ks"==e.showMethod,expression:"showMethod=='ks'"}],ref:"gridPager1",staticStyle:{float:"right","margin-right":"10px"},style:{marginTop:"10px"},attrs:{showSizeChanger:"",showQuickJumper:"",dataSource:e.tableData1,params:e.pageParams,defaultPageSize:100,pageSizeOptions:["100","200","500","1000","1500"],url:"statisticalReport/queryByPage"},on:{"update:dataSource":function(t){e.tableData1=t},"update:data-source":function(t){e.tableData1=t}}}),o("ta-pagination",{directives:[{name:"show",rawName:"v-show",value:"ys"==e.showMethod,expression:"showMethod=='ys'"}],ref:"gridPager2",staticStyle:{float:"right","margin-right":"10px"},style:{marginTop:"10px"},attrs:{showSizeChanger:"",showQuickJumper:"",dataSource:e.tableData2,params:e.pageParams,defaultPageSize:100,pageSizeOptions:["100","200","500","1000","1500"],url:"statisticalReport/queryByPage"},on:{"update:dataSource":function(t){e.tableData2=t},"update:data-source":function(t){e.tableData2=t}}}),o("ta-pagination",{directives:[{name:"show",rawName:"v-show",value:"gz"==e.showMethod,expression:"showMethod=='gz'"}],ref:"gridPager3",staticStyle:{float:"right","margin-right":"10px"},style:{marginTop:"10px"},attrs:{showSizeChanger:"",showQuickJumper:"",dataSource:e.tableData3,params:e.pageParams,defaultPageSize:100,pageSizeOptions:["100","200","500","1000","1500"],url:"statisticalReport/queryByPage"},on:{"update:dataSource":function(t){e.tableData3=t},"update:data-source":function(t){e.tableData3=t}}})],1)],1)])],1)},i=[],l=a(66347),r=a(48534),s=a(95082),n=(a(36133),a(36797)),u=a.n(n),d=a(83231),h=a(88412),f=a(22722),c=a(55115);c.w3.prototype.Base=Object.assign(c.w3.prototype.Base,(0,s.Z)({},f.Z));var m=[],g={name:"statisticalReport",components:{TaTitle:h.Z},data:function(){var t=[{type:"seq",title:"序号",minWidth:40,align:"center"},{title:"医院名称",align:"center",field:"akb021",minWidth:100,overflowTooltip:!0,customRender:{default:"akb021"},customHeaderCell:this.fnCustomHeaderCell},{title:"科室名称",align:"center",field:"aae386",minWidth:80,overflowTooltip:!0,customRender:{default:"aae386"},customHeaderCell:this.fnCustomHeaderCell},{title:"医生名称",align:"center",field:"aac003",minWidth:65,overflowTooltip:!0,customRender:{default:"aac003"},customHeaderCell:this.fnCustomHeaderCell},{title:"规则名称",align:"center",field:"aaa167",minWidth:170,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"提醒人次",align:"right",field:"wgrc",sorter:function(t,e){return t.wgrc-e.wgrc},minWidth:55,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"提醒费用",align:"right",field:"txfy",sorter:function(t,e){return t.txfy-e.txfy},customRender:{default:"monney"},minWidth:60,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"提醒数量",align:"right",field:"txsl",sorter:function(t,e){return t.txsl-e.txsl},minWidth:50,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"继续使用数量",align:"right",field:"jxsysl",sorter:function(t,e){return t.jxsysl-e.jxsysl},minWidth:68,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"取消数量",align:"right",field:"qxsl",sorter:function(t,e){return t.qxsl-e.qxsl},minWidth:52,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"自费数量",align:"right",field:"zfsl",sorter:function(t,e){return t.zfsl-e.zfsl},minWidth:52,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"遵从率(%)",align:"right",field:"zcl",sorter:function(t,e){return t.zcl-e.zcl},minWidth:52,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell}];return{col:{xs:1,sm:2,md:2,lg:6,xl:6,xxl:6},defaultStartDate:u()().startOf("month"),defaultDate:u()().startOf("day"),tableColumns:t,hosList:[],tableColumns0:[].concat(t),tableData0:[].concat(m),tableColumns1:[].concat(t),tableData1:[].concat(m),tableColumns2:[].concat(t),tableData2:[].concat(m),tableColumns3:[].concat(t),tableData3:[].concat(m),showMethod:"yy",showArea:!1,cj:[{value:"2",label:"医嘱审核"},{value:"3",label:"计费审核"}],gzdj:[{value:"1",label:"可疑"},{value:"2",label:"违规"}],cjChangeFlag:!1,akb020:"",yyButtonColor:"primary",ksButtonColor:"default",ysButtonColor:"default",gzButtonColor:"default",yyButtonTitle:"",yyButtonTitleShow:!0,filterList:""}},mounted:function(){var t=this;return(0,r.Z)(regeneratorRuntime.mark((function e(){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,d.Z.permissionCheck();case 2:t.permissions=e.sent,t.fnQueryHos(),a=["2","3","4","16","17"],d.Z.getAa01AAE500StartStop({aaz499s:JSON.stringify(a)},(function(e){var a=e.data.aae500List;t.filterList=a.join(","),t.form.setFieldsValue({aae500:a[0].toString()}),t.fnInitData()}));case 6:case"end":return e.stop()}}),e)})))()},methods:{fnQueryHos:function(){var t=this,e={url:"/miimCommonRead/queryHospRpcList",autoValid:!0},a={successCallback:function(e){t.hosList=e.data.resultData,t.permissions.akb020Set.size>0&&(t.hosList=t.hosList.filter((function(e){return t.permissions.akb020Set.has(e.value)}))),1===t.hosList.length?(t.yyButtonTitle="全院",t.yyButtonTitleShow=!1,t.$refs.hiddenTable0.hideColumn(t.$refs.hiddenTable0.getColumnByField("akb021")),t.$refs.hiddenTable1.hideColumn(t.$refs.hiddenTable1.getColumnByField("akb021")),t.$refs.hiddenTable2.hideColumn(t.$refs.hiddenTable2.getColumnByField("akb021")),t.$refs.hiddenTable3.hideColumn(t.$refs.hiddenTable3.getColumnByField("akb021")),t.form.setFieldsValue({areaflag:t.hosList[0].value})):(t.yyButtonTitle="按医院",t.yyButtonTitleShow=!0,t.$refs.hiddenTable0.showColumn(t.$refs.hiddenTable0.getColumnByField("akb021")),t.$refs.hiddenTable1.showColumn(t.$refs.hiddenTable1.getColumnByField("akb021")),t.$refs.hiddenTable2.showColumn(t.$refs.hiddenTable2.getColumnByField("akb021")),t.$refs.hiddenTable3.showColumn(t.$refs.hiddenTable3.getColumnByField("akb021")))},failCallback:function(e){t.$message.error("医院数据加载失败")}};this.Base.submit(null,e,a)},fnReset:function(){this.form.resetFields(["ape800"]),this.form.setFieldsValue({aae500:"1"}),this.handleQuery()},fnCustomRow:function(t){t.column,t.row;return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px"}}},disabledStartDate:function(t){return t.format("YYYYMMDD")>this.form.getFieldValue("end").format("YYYYMMDD")},disabledEndDate:function(t){return t=t.format("YYYYMMDD"),t>u()().startOf("day").format("YYYYMMDD")||t<this.form.getFieldValue("start").format("YYYYMMDD")},fnCustomHeaderCell:function(t){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px",fontWeight:"bold"}}},disabledDate:function(t){return t.format("YYYYMMDD")>u()().startOf("day").format("YYYYMMDD")},handleShowOrHideTxfyColumn:function(t){this.cjChangeFlag=!0},handleChangeTjfs:function(t){"yy"===t?(this.form.setFieldsValue({akb020:""}),this.form.setFieldsValue({aaz307:""}),this.form.setFieldsValue({aaz263:""}),this.form.setFieldsValue({areaflag:""}),this.form.setFieldsValue({showMethod:t}),this.akb020&&this.form.setFieldsValue({akb020:this.akb020}),this.showMethod=t,this.showArea=!1,this.$refs.gridPager0.loadData((function(t){}))):"yy"!==t&&this.form.getFieldValue("akb020")?(this.form.setFieldsValue({showMethod:t}),this.showMethod=t,this.showArea=!0,"ks"===t?(this.form.setFieldsValue({aaz307:""}),this.form.setFieldsValue({aaz263:""}),this.$refs.gridPager1.loadData((function(t){}))):"ys"===t?(this.form.setFieldsValue({aaz263:""}),this.$refs.gridPager2.loadData((function(t){}))):(this.$refs.hiddenTable3.hideColumn(this.$refs.hiddenTable3.getColumnByField("aae386")),this.form.getFieldValue("aaz307")&&this.$refs.hiddenTable3.showColumn(this.$refs.hiddenTable3.getColumnByField("aae386")),this.$refs.hiddenTable3.hideColumn(this.$refs.hiddenTable3.getColumnByField("aac003")),this.form.getFieldValue("aaz263")&&this.$refs.hiddenTable3.showColumn(this.$refs.hiddenTable3.getColumnByField("aac003")),this.$refs.gridPager3.loadData((function(t){})))):this.$message.info("请选择医院！")},pageParams:function(){var t=this.form.getFieldsValue();return t.aae043s=t.start.format("YYYY-MM-DD"),t.aae043e=t.end.format("YYYY-MM-DD"),t.akb020=this.form.getFieldValue("akb020"),t.aaz307=this.form.getFieldValue("aaz307"),t.aaz263=this.form.getFieldValue("aaz263"),t},handleQuery:function(){"yy"===this.form.getFieldValue("showMethod")?this.$refs.gridPager0.loadData((function(t){})):"ks"===this.form.getFieldValue("showMethod")?this.$refs.gridPager1.loadData((function(t){})):"ys"===this.form.getFieldValue("showMethod")?this.$refs.gridPager2.loadData((function(t){})):this.$refs.gridPager3.loadData((function(t){}))},fnInitData:function(){var t=this;this.$nextTick((function(){t.form.setFieldsValue({akb020:t.akb020,showMethod:"yy"}),t.$refs.hiddenTable0.hideColumn(t.$refs.hiddenTable0.getColumnByField("aae386")),t.$refs.hiddenTable0.hideColumn(t.$refs.hiddenTable0.getColumnByField("aac003")),t.$refs.hiddenTable0.hideColumn(t.$refs.hiddenTable0.getColumnByField("aaa167")),t.$refs.hiddenTable1.hideColumn(t.$refs.hiddenTable1.getColumnByField("aac003")),t.$refs.hiddenTable1.hideColumn(t.$refs.hiddenTable1.getColumnByField("aaa167")),t.$refs.hiddenTable2.hideColumn(t.$refs.hiddenTable2.getColumnByField("aaa167")),t.$refs.hiddenTable3.hideColumn(t.$refs.hiddenTable3.getColumnByField("aac003")),t.$refs.hiddenTable3.hideColumn(t.$refs.hiddenTable3.getColumnByField("aae386")),t.handleQuery()}))},handleExportCurData:function(){var t=this,e=this.pageParams(),a=[],o=[];o="yy"===this.showMethod?this.$refs.hiddenTable0.getColumns():"ks"===this.showMethod?this.$refs.hiddenTable1.getColumns():"ys"===this.showMethod?this.$refs.hiddenTable2.getColumns():this.$refs.hiddenTable3.getColumns();var i,r=(0,l.Z)(o);try{for(r.s();!(i=r.n()).done;){var s=i.value;"seq"!==s.type&&"operate"!==s.property&&!1!==s.visible&&a.push({header:s.title,key:s.property,width:20})}}catch(n){r.e(n)}finally{r.f()}this.Base.submit(null,{url:"statisticalReport/exportDataByPage",data:e,autoValid:!1},{successCallback:function(e){var o=e.data.data,i={fileName:"预警统计结果表",sheets:[{name:"worksheet1",column:{complex:!1,columns:a},rows:o,codeList:[{columnKey:"txfy",customCollection:function(t){t.value||(t.value=0)}}]}]};t.Base.generateExcel(i)},failCallback:function(e){t.$message.error("导出失败")}})},handleExportAllData:function(){Base.downloadFile({url:"statisticalReport/exportData",options:(0,s.Z)({},this.pageParams()),type:"application/excel",fileName:"审核提醒情况统计结果.xls"}).then((function(t){})).catch((function(t){}))},fnCustomYyRow:function(t){t.column;var e=t.row;this.form.setFieldsValue({akb020:e.akb020}),this.activeKey="ks",this.handleChangeTjfs("ks")},fnCustomKsRow:function(t){t.column;var e=t.row;this.form.setFieldsValue({aaz307:e.aaz307}),this.activeKey="ys",this.handleChangeTjfs("ys")},fnCustomYsRow:function(t){t.column;var e=t.row;this.form.setFieldsValue({aaz307:e.aaz307}),this.form.setFieldsValue({aaz263:e.aaz263}),this.activeKey="gz",this.handleChangeTjfs("gz")}},watch:{showMethod:function(t,e){"yy"===t?(this.yyButtonColor="primary",this.ksButtonColor="default",this.ysButtonColor="default",this.gzButtonColor="default"):"yy"!==t&&this.form.getFieldValue("akb020")&&("ks"===t?(this.yyButtonColor="default",this.ksButtonColor="primary",this.ysButtonColor="default",this.gzButtonColor="default"):"ys"===t?(this.yyButtonColor="default",this.ksButtonColor="default",this.ysButtonColor="primary",this.gzButtonColor="default"):(this.yyButtonColor="default",this.ksButtonColor="default",this.ysButtonColor="default",this.gzButtonColor="primary"))}}},p=g,y=a(1001),b=(0,y.Z)(p,o,i,!1,null,"9eaff02e",null),C=b.exports},36766:function(t,e,a){"use strict";var o=a(66586);e["Z"]=o.Z},26263:function(t,e,a){"use strict";a.d(e,{s:function(){return o},x:function(){return i}});var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},i=[]},66586:function(t,e){"use strict";var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};e["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}},83231:function(t,e,a){"use strict";var o=a(48534);a(36133);function i(t){return t?t.akb020&&t.aaz263?"personal":t.akb020&&t.aaz309&&t.aaz307?"group":t.akb020&&t.aaz307?"department":t.akb020?"hospital":"all":"all"}function l(t){return r.apply(this,arguments)}function r(){return r=(0,o.Z)(regeneratorRuntime.mark((function t(e){var a,o,l,r,s,n,u,d,h,f,c,m;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Base.submit(null,{url:"/miimCommonRead/queryDataPermission",showPageLoading:!1,data:{jobnumber:e},autoValid:!0});case 2:return a=t.sent,o=new Set,l=new Set,a.data.permission.forEach((function(t){var e=i(t);"hospital"===e&&o.add(t.akb020),"department"===e&&l.add(t.aaz307)})),r=a.data.permission.filter((function(t){return"department"===i(t)||!l.has(t.aaz307)})).filter((function(t){return"hospital"===i(t)||!o.has(t.akb020)})),s=new Set(r.map((function(t){return t.akb020})).filter((function(t){return null!=t}))),n=new Set(r.map((function(t){return t.aaz307})).filter((function(t){return null!=t}))),u=new Set(r.map((function(t){return t.aaz309})).filter((function(t){return null!=t}))),d=new Set(r.map((function(t){return t.aaz263})).filter((function(t){return null!=t}))),h=!1,f=!1,c=!1,m=!1,1===s.size&&(h=!0),1===n.size&&1===s.size&&(f=!0),1===n.size&&1===s.size&&1===u.size&&(c=!0),1===s.size&&0===n.size&&1===d.size&&(m=!0),t.abrupt("return",{akb020Set:s,aaz307Set:n,aaz263Set:d,aaz309Set:u,akb020Disable:h,aaz307Disable:f,aaz263Disable:m,aaz309Disable:c});case 20:case"end":return t.stop()}}),t)}))),r.apply(this,arguments)}function s(t,e){Base.submit(null,{url:"miimCommonRead/getAa01AAE500StartStop",data:t},{successCallback:function(t){return e(t)}})}function n(t,e){Base.submit(null,{url:"miimCommonRead/insertTableColumShow",data:t,autoValid:!1},{successCallback:function(t){return e("success")},failCallback:function(t){return e("fail")}})}var u={zbx01:{footer:function(){return"统筹金总额"}},zbx02:{footer:function(){return"基金支出总额"}},zbx03:{footer:function(){return"总费用"}},zbx04:{footer:function(){return"总住院日"}}};e["Z"]={permissionCheck:l,getAa01AAE500StartStop:s,insertTableColumShow:n,props:u,moneyNumFormat:function(t){var e=t.cellValue;return e||"—"}}},55382:function(){},61219:function(){}}]);
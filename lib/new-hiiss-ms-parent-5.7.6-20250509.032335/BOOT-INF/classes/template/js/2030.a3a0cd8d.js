"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[2030],{88412:function(t,e,a){var i=a(26263),r=a(36766),n=a(1001),l=(0,n.Z)(r.Z,i.s,i.x,!1,null,"5e7ef0ae",null);e["Z"]=l.exports},28170:function(t,e,a){a.d(e,{Z:function(){return p}});var i=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("ta-drawer",{attrs:{width:"900",title:e.title,placement:"right",visible:e.visible,"mask-closable":!1,"get-container":function(){return t.$el.parentNode}},on:{close:e.handleClose}},[i("div",{attrs:{slot:"footer"},slot:"footer"},[i("div",{staticStyle:{display:"flex","justify-content":"center",width:"100%"}},[i("ta-button",{on:{click:e.handleClose}},[e._v(" "+e._s(e.inApproval?"取消":"关闭")+" ")]),i("ta-button",{directives:[{name:"show",rawName:"v-show",value:e.inApproval,expression:"inApproval"}],attrs:{type:"primary"},on:{click:e.handleSave}},[e._v(" 确定 ")])],1)]),i("ta-form",{attrs:{"auto-form-create":function(e){return t.form=e},layout:"horizontal","form-layout":!0,labelAlign:"left","label-width":"100px",col:{xs:1,sm:1,md:1,lg:1,xl:1,xxl:1}}},[i("ta-form-item",{attrs:{label:"疑点Id","field-decorator-id":"aaz213",hidden:!0}},[i("ta-input")],1),i("ta-form-item",{attrs:{label:"参保人","field-decorator-id":"aac003",span:8}},[e._v(" "+e._s(e.params.aac003||"")+" ")]),i("ta-form-item",{attrs:{label:"住院号","field-decorator-id":"akc190",span:8}},[e._v(" "+e._s(e.params.akc190||"")+" ")]),i("ta-form-item",{attrs:{label:"年龄","field-decorator-id":"age",span:8}},[e._v(" "+e._s(e.params.age||"")+" ")]),i("ta-form-item",{attrs:{label:"住院天数","field-decorator-id":"iptDays",span:8}},[e._v(" "+e._s(e.params.iptDays||"")+" ")]),i("ta-form-item",{attrs:{label:"入院日期","field-decorator-id":"aae030",span:8}},[e._v(" "+e._s(e.params.aae030||"")+" ")]),i("ta-form-item",{attrs:{label:"出院日期","field-decorator-id":"aae031",span:8}},[e._v(" "+e._s(e.params.aae031||"")+" ")]),i("ta-form-item",{attrs:{label:"性别","field-decorator-id":"aac003",span:8}},[e._v(" "+e._s(e.CollectionLabel("SEX",e.params.aac004)||"")+" ")]),i("ta-form-item",{attrs:{label:"扣款条目数","field-decorator-id":"wfsl",span:8}},[e._v(" "+e._s(e.params.failCount||"")+"条 ")]),i("ta-form-item",{attrs:{label:"规则名称","field-decorator-id":"aaa167"}},[e._v(" "+e._s(e.params.aaa167||"")+" ")]),i("ta-form-item",{attrs:{label:"违反项目","field-decorator-id":"ake002"}},[e._v(" "+e._s(e.params.ake002||"")+" ")]),i("ta-form-item",{attrs:{label:"规则描述","field-decorator-id":"ykz018"}},[e._v(" "+e._s(e.params.ykz018||"")+" ")]),i("ta-form-item",{attrs:{label:"诊断","field-decorator-id":"diagnosis"}},[e._v(" "+e._s(e.params.diagnosis||e.params.aka121||"")+" ")]),i("ta-form-item",{attrs:{label:"是否申诉","field-decorator-id":"appealFlag"}},[e._v(" "+e._s("1"===e.params.appealFlag?"申诉":"不申诉，认同并及时整改")+" ")]),i("ta-form-item",{attrs:{label:"预览附件","field-decorator-id":"files"}},[i("div",{directives:[{name:"show",rawName:"v-show",value:e.fileList.length>0,expression:"fileList.length > 0"}],staticClass:"fileListStyle"},e._l(e.fileList,(function(t){return i("div",{key:t.uid||t.fileId,staticClass:"fileItem",staticStyle:{cursor:"pointer"},on:{mouseover:function(a){return e.debounceShowPic(t)},mouseout:function(a){return e.hidePic(t)},click:function(a){return a.stopPropagation(),e.fnDownloadImg(t)}}},[i("ta-icon",{attrs:{type:"link"}}),i("span",{staticClass:"fileName"},[e._v(e._s(t.name||t.fileName))]),i("div",{staticClass:"fileActions"},[i("ta-icon",{staticClass:"downloadIcon",attrs:{type:"download"},on:{click:function(a){return a.stopPropagation(),e.downloadImage(t)}}})],1)],1)})),0),i("div",{directives:[{name:"show",rawName:"v-show",value:e.showFile,expression:"showFile"}],staticClass:"filePreview"},[i("img",{attrs:{src:e.fileUrl,height:"250",width:"100%"}})])]),i("ta-form-item",{attrs:{label:"申诉理由","field-decorator-id":"aaz560"}},[e._v(" "+e._s(e.params.aaz560||"")+" ")]),i("ta-form-item",{attrs:{label:"实际处理人","field-decorator-id":"currPersName"}},[e._v(" "+e._s(e.param.handPers||e.param.currPersName)+" ")]),i("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:["4","6","9","10"].includes(e.params.handStas),expression:"['4','6','9','10'].includes(params.handStas)"}],attrs:{label:["6","9"].includes(e.params.handStas)?"驳回人":"审批人","field-decorator-id":"rejctPers"}},[e._v(" "+e._s(["6","9"].includes(e.params.handStas)?e.params.rejctPers||"":e.params.auditPers||"")+" ")]),i("ta-form-item",{attrs:{label:"审批结果","field-decorator-id":"handStas",require:e.inApproval}},[e.inApproval?i("div",[i("ta-radio-group",{attrs:{options:[{value:"4",label:"通过"},{value:"6",label:"驳回"}]},on:{change:e.changeApprovalResult},model:{value:e.handStasModel,callback:function(t){e.handStasModel=t},expression:"handStasModel"}})],1):i("div",[i("span",[e._v(e._s(["4","10"].includes(e.approvalResult)?"医保办审批通过":"6"==e.approvalResult?"医保办已驳回":""))])])]),i("ta-form-item",{attrs:{label:"驳回原因","field-decorator-id":"rejctReas","field-decorator-options":{rules:[{max:2e3,message:"不得超过2000字符"}]},require:e.inApproval&&"6"===e.approvalResult,hidden:"6"!==e.approvalResult}},[e.inApproval?i("div",[i("ta-textarea",{attrs:{placeholder:"请输入驳回原因",rows:5,"show-length":!0},model:{value:e.rejctReasModel,callback:function(t){e.rejctReasModel=t},expression:"rejctReasModel"}})],1):i("div",[i("p",[e._v(" "+e._s(e.params.rejctReas)+" ")])])])],1),i("div",{staticStyle:{width:"100%","background-color":"#FFFFFF","text-align":"left"}},[i("ta-checkbox",{directives:[{name:"show",rawName:"v-show",value:"3"!=e.params.dataType,expression:"params.dataType != '3'"}],attrs:{disabled:!e.inApproval,checked:e.syncInfo},on:{change:e.changeSyncInfo}},[e._v(" 将附件和申诉理由应用于当前患者该违规项目(任务状态也会同步变更) ")])],1),i("ta-modal",{attrs:{title:this.fileName,height:"600px",width:"80%",bodyStyle:{textAlign:"center"},footer:null},model:{value:e.imgVisible,callback:function(t){e.imgVisible=t},expression:"imgVisible"}},[i("img",{attrs:{src:e.imgSrc},on:{click:e.downloadImage}})])],1)},r=[],n=a(48534),l=(a(32564),a(36133),a(52582)),s=a(36797),o=a.n(s),c={name:"approval",components:{},props:{visible:{type:Boolean,default:!1},param:{type:Object,default:function(){return{}}}},data:function(){return{title:"申诉审批",inApproval:!0,approvalResult:"",handStasModel:"",rejctReasModel:"",fileList:[],showFile:!1,syncInfo:!1,fileUrl:"",imgVisible:!1,imgSrc:"",fileName:"",params:{},debounceShowPic:null,basePath:l.Z.getBasePath()}},watch:{visible:{handler:function(t){var e=this;t&&this.$nextTick((function(){"3"===e.param.handStas?(e.inApproval=!0,e.title="申诉审批"):(e.inApproval=!1,e.title="申诉详情",e.approvalResult=e.param.handStas),e.param.syncInfo&&"-1"!=e.param.syncInfo&&(e.syncInfo=!0),l.Z.getFileList({aaz213:e.param.aaz213},(function(t){e.params=t.data.appeal,e.fileList=t.data.appeal.files}))}))}}},mounted:function(){},created:function(){this.debounceShowPic=this.debounce(this.showPic,1500)},methods:{moment:o(),changeSyncInfo:function(t){this.syncInfo=t.target.checked},downloadImage:function(){var t=this;return(0,n.Z)(regeneratorRuntime.mark((function e(){var a,i,r,n,l;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,fetch(t.imgSrc,{mode:"cors"});case 3:if(a=e.sent,a.ok){e.next=7;break}return t.$message.error("网络响应失败，请稍后重试，状态码:",a.status),e.abrupt("return");case 7:if(i=a.headers.get("Content-Type"),i&&i.startsWith("image/")){e.next=11;break}return t.$message.error("获取的资源不是图片，Content-Type:",i),e.abrupt("return");case 11:return e.next=13,a.blob();case 13:r=e.sent,n=URL.createObjectURL(r),l=document.createElement("a"),l.href=n,l.download=t.fileName,l.style.display="none",document.body.appendChild(l),l.click(),document.body.removeChild(l),URL.revokeObjectURL(n),e.next=28;break;case 25:e.prev=25,e.t0=e["catch"](0),t.$message.error("下载失败",e.t0);case 28:case"end":return e.stop()}}),e,null,[[0,25]])})))()},fnDownloadImg:function(t){t.fileId&&(this.imgSrc=this.basePath+"/appeal/drPoint/getFile?fileId="+t.fileId,this.fileName=t.name||t.fileName,this.imgVisible=!0)},getFiles:function(){var t=l.Z.getPicZipUrl()+"?aaz213="+this.params.aaz213;window.location.href=t},showPic:function(t){t.fileId&&(this.showFile=!0,this.fileUrl=this.basePath+"/appeal/drPoint/getFile?fileId="+t.fileId)},debounce:function(t,e){var a,i=function(){for(var i=this,r=arguments.length,n=new Array(r),l=0;l<r;l++)n[l]=arguments[l];a&&clearTimeout(a),a=setTimeout((function(){t.apply(i,n)}),e)};return i.cancel=function(){a&&clearTimeout(a)},i},hidePic:function(t){this.showFile=!1,this.debounceShowPic.cancel()},changeApprovalResult:function(t){this.approvalResult=t.target.value,this.handStas=t.target.value},handleSave:function(){var t=this;this.form.validateFields((function(e){if(!e){var a=t.form.getFieldsValue();a.aaz213=t.params.aaz213,a.aaz263=t.$route.query.aaz263,a.syncInfo=t.syncInfo?"1":"0",l.Z.singelApproval(a,(function(e){e.errors&&e.errors.length>0&&t.$message.error(e.errors[0].msg),t.$message.success("审批成功"),t.handleClose()}))}}))},handleClose:function(){this.form.resetFields(),this.showFile=!1,this.syncInfo=!1,this.fileList=[],this.handStas="",this.approvalResult="",this.handStasModel="",this.rejctReasModel="",this.$emit("handleClose")}}},u=c,d=a(1001),h=(0,d.Z)(u,i,r,!1,null,"9fb89d22",null),p=h.exports},66670:function(t,e,a){a.d(e,{Z:function(){return b}});var i=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("ta-drawer",{attrs:{title:e.title,visible:e.visible,width:"80%"},on:{close:e.handleClose}},[i("ta-border-layout",{staticStyle:{height:"calc(100%)"},attrs:{"layout-type":"fixTop"}},[i("div",{staticClass:"query-header",attrs:{slot:"header"},slot:"header"},[i("div",{ref:"formBox",staticClass:"query-header-left-3"},[i("ta-form",{attrs:{"auto-form-create":function(e){return t.form=e},layout:"horizontal","form-layout":!0,"label-width":"80px",col:{xs:1,sm:2,md:2,lg:4,xl:4,xxl:4}}},[i("ta-form-item",{attrs:{label:"任务状态","init-value":"0","field-decorator-id":"status"}},[i("ta-select",{staticStyle:{width:"90%"},attrs:{placeholder:"请选择任务状态","allow-clear":!1,"collection-type":"APPEALHANDSTAS","collection-filter":"7,11"},on:{select:e.queryTableData}})],1),i("ta-form-item",{attrs:{label:"开单科室","field-decorator-id":"aac004"}},[i("ta-suggest",{staticStyle:{width:"95%"},attrs:{"option-config":{value:"value",label:"label"},"data-source":e.deptList,placeholder:"输入科室名称","table-title-map":new Map([["label",{name:"科室名称",style:{minWidth:"120px"}}]])},on:{select:function(t,a){return e.deptChange(t,a)},search:e.deptSearch,change:e.suggestChange}})],1),i("ta-form-item",{attrs:{label:"出院科室","field-decorator-id":"aae386"}},[i("ta-suggest",{staticStyle:{width:"95%"},attrs:{"option-config":{value:"value",label:"label"},"data-source":e.deptList2,placeholder:"输入科室名称","table-title-map":new Map([["label",{name:"科室名称",style:{minWidth:"120px"}}]])},on:{select:function(t,a){return e.deptChange(t,a)},search:e.deptSearch,change:e.suggestChange}})],1),i("ta-form-item",{attrs:{"label-width":200,label:"仅查看无申诉处理人的疑点","field-decorator-id":"noneCurrPers"}},[i("ta-switch",{staticStyle:{"margin-top":"5px"},on:{change:e.onSwichChange}})],1)],1)],1),i("div",{staticClass:"query-btn-3"},[i("ta-button",{attrs:{type:"primary",icon:"search"},on:{click:e.queryTableData}},[e._v(" 查询 ")]),i("ta-button",{attrs:{type:"primary",icon:"redo",ghost:!0},on:{click:e.resetForm}},[e._v(" 重置 ")])],1),i("div",{staticStyle:{float:"right",display:"flex","justify-content":"center","margin-bottom":"5px"}},[i("ta-button",{attrs:{icon:"edit",type:"primary"},on:{click:e.openBatchModefyModel}},[e._v(" 批量修改 ")]),i("ta-button",{attrs:{icon:"down-square",type:"primary"},on:{click:e.preHandleSavleAll}},[e._v(" 下发所有数据 ")]),i("ta-button",{attrs:{icon:"check-square",type:"primary"},on:{click:e.handleSave}},[e._v(" 下发选中数据 ")]),i("ta-button",{attrs:{icon:"setting",type:"primary",ghost:!0},on:{click:e.openPlugin}},[e._v(" 下发配置 ")])],1)]),i("div",{staticClass:"fit"},[i("ta-big-table",{ref:"xTable",staticStyle:{width:"100%"},attrs:{border:"","highlight-hover-row":"","show-overflow":"","sort-config":{trigger:"cell"},data:e.dataSource,height:"auto",resizable:!0,"auto-resize":"",size:"small","empty-text":"-",width:"min-width","row-id":"aaz213","keep-source":"","mouse-config":{selected:!0},"edit-config":{trigger:"click",mode:"cell",showStatus:!0},"keyboard-config":{isArrow:!0,isDel:!0,isEnter:!0,isTab:!0,isEdit:!0},"checkbox-config":{trigger:"row",highlight:!0,reserve:!0}},on:{"cell-selected":e.handleCellSelect},scopedSlots:e._u([{key:"bottomBar",fn:function(){return[i("ta-pagination",{ref:"gridPager",staticStyle:{"text-align":"right"},attrs:{size:"small","default-page-size":200,"page-size-options":["30","50","100","200","500"],"data-source":e.dataSource,url:e.pageUrl,params:e.pageParams},on:{"update:dataSource":function(t){e.dataSource=t},"update:data-source":function(t){e.dataSource=t}}})]},proxy:!0}])},[i("ta-big-table-column",{attrs:{type:"checkbox",fixed:"left",width:"40px",align:"center"}}),i("ta-big-table-column",{attrs:{field:"aaz213",title:"管理Id",visible:!1}}),i("ta-big-table-column",{attrs:{type:"seq",fixed:"left",width:"50px",title:"序号",align:"center"}}),i("ta-big-table-column",{attrs:{field:"handStas","show-overflow":"",title:"申诉任务状态",fixed:"left","min-width":"140px",sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;t.column;return[i("div",{staticStyle:{position:"relative"}},[a.handStas?[e.CollectionLabel("SEX",a.handStas)?i("div",[i("ta-badge",{attrs:{color:e.statusColors[a.handStas]||"#000000"}}),i("span",{style:{color:e.statusColors[a.handStas]||"#000000"}},[e._v(e._s(e.CollectionLabel("APPEALHANDSTAS",a.handStas)))])],1):i("div",[i("ta-badge",{attrs:{color:"#000000"}}),i("span",{staticStyle:{color:"#000000"}},[e._v("未知状态")])],1)]:[i("ta-badge",{attrs:{color:"#000000"}}),i("span",{staticStyle:{color:"#000000"}},[e._v("未知状态")])]],2)]}}])}),i("ta-big-table-column",{attrs:{field:"akc190",title:"住院门诊号","min-width":"120px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"aac003",title:"参保人","min-width":"100px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"ake002",align:"center","show-overflow":"",title:"医保项目名称","min-width":"160px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"aaa167",title:"违反规则","min-width":"180px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"ape804",align:"center","show-overflow":"",title:"违规金额","min-width":"110px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"aae386V","show-overflow":"",title:"院内出院科室","min-width":"130px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"aac004V",align:"center","show-overflow":"",title:"院内开单科室","min-width":"130px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"aaz570V","show-overflow":"",title:"院内开单医生","min-width":"130px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"aae036",title:"明细时间","min-width":"150px",align:"center",sortable:""}}),i("ta-big-table-column",{attrs:{field:"aae386",title:"疑点出院科室","min-width":"130px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"aac004",title:"疑点开单科室","min-width":"130px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"aaz570",title:"疑点开单医生","min-width":"130px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"akc195","show-overflow":"",title:"申诉截止日期","min-width":"150px",align:"center",sortable:""}}),i("ta-big-table-column",{attrs:{field:"currPers",title:"申诉处理人",width:"100",visible:!1,sortable:""}}),i("ta-big-table-column",{attrs:{fixed:"right",field:"currPersNameInShow",title:"申诉处理人",width:"140","class-name":e.currPersStyle,"edit-render":{},sortable:""},scopedSlots:e._u([{key:"edit",fn:function(t){var a=t.row,r=t.column;return[i("ta-suggest",{staticStyle:{width:"100%"},attrs:{"auto-focus":"","data-source":e.peopleList,"table-title-map":new Map([["label",{name:"医生姓名",style:{minWidth:"350px"}}]]),"option-config":e.optionConfig,"dropdown-match-select-width":!1,"dropdown-style":{width:"100%"},"get-popup-container":e.setPopupContainer},on:{select:function(t,i){return e.handleChangeSuggest(t,a,r,i)},search:e.handleSearch,focus:function(){e.peopleList=[{label:a.currPersNameInShow,value:a.currPersInShow}],e.searchAkb020=a.akb020}},scopedSlots:e._u([{key:"body-cell",fn:function(t){t.cellValue,t.column,t.columnIndex;var a=t.row;t.rowIndex;return[i("span",[e._v(e._s(a.label)+" ("+e._s(a.value)+" "+e._s(a.aae386?a.aae386:"未知科室 "+a.aaz307)+" "+e._s(a.akb021?a.akb021:"未知医院 "+a.akb020)+")")])]}}],null,!0)})]}}])})],1)],1)]),i("plugin",{attrs:{visible:e.pluginVisible},on:{handleClose:e.closePlugin,handleOk:e.updatePlugin}}),i("ta-modal",{attrs:{title:"批量修改",visible:e.batchModefyVisible,height:250,width:500},on:{ok:e.handleBatchModefy,cancel:e.closeBatchModefyModel}},[i("p",[e._v("请选择修改后的申诉处理人")]),i("ta-form",{attrs:{"auto-form-create":function(e){return t.form1=e},layout:"horizontal","form-layout":!0,"label-width":"80px",col:{xs:1,sm:1,md:1,lg:1,xl:1,xxl:1}}},[i("ta-form-item",{attrs:{label:"申诉处理人","field-decorator-id":"currPers","label-width":"100px",require:!0}},[i("ta-suggest",{staticStyle:{width:"100%"},attrs:{"data-source":e.peopleList,"table-title-map":new Map([["label",{name:"姓名",style:{minWidth:"120px"}}]]),"option-config":e.optionConfig,"dropdown-match-select-width":!1,"dropdown-style":{width:"300px"}},on:{select:function(t,a){return e.handleChangeSuggest1(t,a)},search:e.handleSearch2},scopedSlots:e._u([{key:"body-cell",fn:function(t){t.cellValue,t.column,t.columnIndex;var a=t.row;t.rowIndex;return[i("span",[e._v(e._s(a.label)+" ("+e._s(a.value)+" "+e._s(a.aae386?a.aae386:"未知科室 "+a.aaz307)+" "+e._s(a.akb021?a.akb021:"未知医院 "+a.akb020)+")")])]}}])})],1)],1)],1)],1)},r=[],n=a(18701),l=a.n(n),s=a(95082),o=a(52582),c=a(36797),u=a.n(c),d=a(10573),h={name:"distribute",components:{Plugin:d.Z},props:{visible:{type:Boolean,default:!1},params:{type:Object,default:function(){return{period:u()()}}}},data:function(){return{title:"下发申诉",dataSource:[],batchLabel:null,modalWin:null,pageUrl:o.Z.getDistributePageUrl(),validRules:{currPersName:[{required:!0,message:"必填"}]},pluginVisible:!1,optionConfig:{value:"value",label:function(t){return"".concat(t.label," (").concat(t.value," ").concat(t.aae386?t.aae386:"未知科室 "+t.aaz307," ").concat(t.akb021?t.akb021:"未知医院 "+t.akb020,")")}},batchModefyVisible:!1,deptList:[],deptList2:[],currCache:new Map,peopleList:[],searchAkb020:"",statusColors:{0:"#f49924",1:"#1890ff",2:"#faad14",3:"#a0d911",4:"#52c41a",5:"#722ed1",6:"#f5222d",7:"#13c2c2",8:"#eb2f96",9:"#bfbfbf",10:"#0050b3",11:"#979797"}}},watch:{visible:{handler:function(t){var e=this;t&&this.$nextTick((function(){e.$refs.xTable.resetColumn(),e.$refs.xTable.clearCheckboxReserve(),e.$refs.xTable.clearCheckboxRow(),"3"===e.params.dataType&&(e.$refs.xTable.hideColumn(e.$refs.xTable.getColumnByField("aae386V")),e.$refs.xTable.hideColumn(e.$refs.xTable.getColumnByField("aac004V")),e.$refs.xTable.hideColumn(e.$refs.xTable.getColumnByField("aae036"))),e.queryTableData()}))}},dataSource:function(t){var e=this;t.forEach((function(t){if(e.currCache.has(t.aaz213)){var a=e.currCache.get(t.aaz213);return t.currPersInShow=a.currPers,void(t.currPersNameInShow=a.currPersName)}if(t.currPers)return t.currPersInShow=t.currPers,void(t.currPersNameInShow=t.currPersName);t.currPersInShow=t.currPersMatch,t.currPersNameInShow=t.currPersNameMatch}))}},mounted:function(){},methods:{moment:u(),currPersStyle:function(t){if(t.row.currPersInShow!==t.row.currPers)return"curr-cell"},onSwichChange:function(t){var e=this;this.$nextTick((function(){e.queryTableData()}))},deptChange:function(t,e){var a=this;this.$nextTick((function(){a.queryTableData()}))},suggestChange:function(t){var e=this;t||this.$nextTick((function(){e.queryTableData()}))},deptSearch:function(t){var e=this;t&&(o.Z.getDeptList((0,s.Z)((0,s.Z)({},this.params),{},{searchVal:t}),(function(t){e.deptList=t.data.list})),o.Z.getDeptList2((0,s.Z)((0,s.Z)({},this.params),{},{searchVal:t}),(function(t){e.deptList2=t.data.list})))},openBatchModefyModel:function(){this.$refs.xTable.getCheckboxReserveRecords().length>0?this.batchModefyVisible=!0:this.$message.warning("请选择需要修改的数据")},handleBatchModefy:function(){var t=this,e=this.form1.getFieldsValue(),a=this.$refs.xTable.getCheckboxReserveRecords();this.$refs.xTable.getCheckboxRecords();this.form1.validateFields((function(i){i||(a.forEach((function(a){return t.currCache.set(a.aaz213,{currPers:e.currPers,currPersName:t.batchLabel})})),t.closeBatchModefyModel())}))},closeBatchModefyModel:function(){this.form1.resetFields(),this.batchModefyVisible=!1,this.queryTableData()},handleChangeSuggest1:function(t,e){this.batchLabel=e.label},setPopupContainer:function(t){return t.parentNode},handleSearch2:function(t){var e=this;t&&o.Z.queryPeople({doctorName:t,akb020:"",sign:"restrict"},(function(t){e.peopleList=t.data.list}))},handleSearch:function(t){var e=this;t&&o.Z.queryPeople({doctorName:t,akb020:this.searchAkb020,sign:"restrict"},(function(t){e.peopleList=t.data.list}))},handleChangeSuggest:function(t,e,a,i){t&&(e.currPersInShow=t,e.currPersNameInShow=i.label,this.$refs.xTable.updateStatus({row:e,column:a}),this.currCache.set(e.aaz213,{currPers:t,currPersName:i.label}))},handleCellSelect:function(t){t.row,t.rowIndex,t.$rowIndex,t.column,t.columnIndex,t.$columnIndex},pageParams:function(){var t=this.form.getFieldsValue(),e=this.$refs.gridPager.getPagerInfo();return Object.assign(t,e,this.params),t},queryTableData:function(){var t=this;this.form.validateFields((function(e){e||t.$refs.gridPager.loadData()}))},resetForm:function(){this.form.resetFields()},openPlugin:function(){this.pluginVisible=!0},updatePlugin:function(){this.queryTableData()},closePlugin:function(){this.pluginVisible=!1},handleSave:function(){var t=this.$refs.xTable.getCheckboxReserveRecords();t=t.filter((function(t){return t.currPersInShow!==t.currPers}));var e=t.filter((function(t){return"0"!==t.handStas&&"1"!==t.handStas})).length,a=t.find((function(t){return!t.currPersInShow}));0!==t.length?a?this.$message.warn("存在已勾选但未填写处理人的数据!"):this.popConfirm(t.length,t,"2",e):this.$message.warn("未勾选任何已编辑数据")},preHandleSavleAll:function(){var t=this,e=this.form.getFieldsValue();Object.assign(e,this.params),e.handlePersons=[],this.currCache.forEach((function(t,a){e.handlePersons.push(a)})),o.Z.preSaveCheck(e,(function(a){"0"!==a.data.data.verificationPassed?a.data.data.totalCount?t.popConfirm(a.data.data.totalCount,[],"1",a.data.data.handledCount,e):t.$message.warn("未查询到申述明细数据，请核实！"):t.$message.warn("下发所有数据校验不通过，存在未指定处理人的明细数据")}))},handleSavleAll:function(){},popConfirm:function(t,e,a,i){var r=this,n=this.$createElement,s={color:"blue"},o={on:{click:function(){return r.save("all",e,a)}},props:{type:"primary"}},c={on:{click:function(){return r.save("unhandled",e,a)}},props:{type:"primary",disabled:t===i}};this.modalWin=this.$confirm({icon:!1,closable:!0,content:n("span",["已勾选",t,"条已编辑数据,当前存在",i,"条已处理数据，请选择继续下发类型",n("br"),'如果选择"',n("span",{style:s},["下发全部数据"]),'",系统将保留已添加的申诉理由和申诉材料，只变更任务申诉处理人']),okText:n("span",["仅下发未处理数据"]),okType:"success",cancelText:n("span",["下发全部数据"]),footer:n("div",[n("ta-button",l()([{},o]),["下发全部数据"]),n("ta-button",l()([{},c]),["仅下发未处理数据"])]),onOk:function(){},onCancel:function(){}})},save:function(t,e,a){var i=this;this.$refs.xTable.fullValidate(null,(function(r){var n,l=e.filter((function(e){return"all"===t||"1"===e.handStas||"0"===e.handStas})).map((function(t){return{aaz213:t.aaz213,currPers:t.currPersInShow,handStas:t.handStas,dataType:t.dataType,aae043:t.aae043,aae141:t.aae141}})),c=i.$route.query;c&&c.aaz263&&(n=c.aaz263);var u=i.form.getFieldsValue();Object.assign(u,i.params),u.handlePersons=[],i.currCache.forEach((function(t,e){u.handlePersons.push(e+"-"+t.currPers)})),o.Z.updateCurrPers((0,s.Z)((0,s.Z)({},i.params),{},{aae043:i.dataSource[0].aae043,updateList:l,saveScope:t,auditPerson:n,dispatchType:a,appealQueryVo:u}),(function(t){i.$message.success("保存成功"),i.modalWin.destroy(),i.$emit("handleClose")}))}))},handleClose:function(){this.pluginVisible=!1,this.batchModefyVisible=!1,this.currCache=new Map,this.peopleList=[],this.$refs.xTable.clearCheckboxReserve(),this.$emit("handleClose")}}},p=h,f=a(1001),m=(0,f.Z)(p,i,r,!1,null,"60d86ade",null),b=m.exports},10573:function(t,e,a){a.d(e,{Z:function(){return f}});var i=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("ta-modal",{attrs:{title:e.title,visible:e.visible,height:700,width:1e3},on:{ok:e.handleSave,cancel:e.handleClose}},[i("ta-alert",{directives:[{name:"show",rawName:"v-show",value:e.firstTime,expression:"firstTime"}],attrs:{message:"提示：请先进行下发配置，保存后可直接进入首次下发页面",banner:""}}),i("ta-row",{staticClass:"w100Box1"},[i("ta-col",{attrs:{span:"2"}},[i("span",{staticClass:"t1"},[e._v("下发方式：")])]),i("ta-col",[i("ta-radio-group",{attrs:{value:e.partType,options:e.options,"default-value":"0"},on:{change:e.changePartType}})],1)],1),i("ta-row",{staticClass:"w100Box1"},[i("ta-col",{attrs:{span:2}},[i("span",{staticClass:"t1"},[e._v("详细配置：")])]),i("ta-col",{attrs:{span:20}},["1"===e.partType?i("ta-checkbox",{attrs:{checked:e.detailChecked},on:{change:e.changeDetailChecked}},[e._v(" 没有开单医生时，下发给科室指定人员 ")]):e._e(),i("ta-row",{directives:[{name:"show",rawName:"v-show",value:"1"===e.partType&&e.detailChecked||"2"===e.partType,expression:"(partType === '1'&&detailChecked)||partType==='2'"}]},[i("ta-col",{attrs:{span:11}},[e.hosList.length>1?i("ta-tabs",{attrs:{activeKey:e.currentHospital},on:{change:e.tabKeyChange}},e._l(e.hosList,(function(t){return i("ta-tab-pane",{key:t.hospitalId,attrs:{tab:t.hospitalName}})})),1):e._e(),i("ta-card",{attrs:{bordered:"","body-style":{lineHeight:"30px"}}},[i("div",{staticStyle:{display:"flex","justify-content":"space-between"},attrs:{slot:"title"},slot:"title"},[i("ta-checkbox",{attrs:{indeterminate:e.indeterminate,checked:e.checkAll},on:{change:e.onCheckAllChange}},[e._v(" "+e._s(e.checkedList.length||0)+"/"+e._s(e.deptList.length||0)+"项 ")]),i("span",{staticClass:"t1"},[e._v("科室列表")])],1),i("ta-input-search",{staticStyle:{width:"100%"},attrs:{placeholder:""},on:{search:e.filterDeptList,change:function(t){return e.filterDeptList(t.target.value)}}}),i("ta-checkbox-group",{staticClass:"checkBoxList",attrs:{value:e.checkedList},on:{change:e.onChange}},e._l(e.deptList,(function(t){return i("ta-checkbox",{key:t.departmentId,attrs:{value:t.departmentId}},[i("span",[e._v(e._s(t.departmentName))]),i("span",{staticStyle:{float:"right",color:"#888888"}},[e._v(e._s(t.doctorName))])])})),1)],1)],1),i("ta-col",{attrs:{span:1}}),i("ta-col",{attrs:{span:12}},[i("ta-form",{attrs:{"auto-form-create":function(e){return t.form=e},layout:"horizontal","form-layout":!0,"label-width":"80px",col:{xs:1,sm:1,md:1,lg:1,xl:1,xxl:1}}},[i("ta-form-item",{staticStyle:{height:"120px"},attrs:{label:"已选科室","field-decorator-id":"orgId"}},[i("div",{staticStyle:{overflow:"scroll","text-overflow":"ellipsis",height:"110px"}},[e._v(e._s(e.deptNames))])]),i("ta-form-item",{attrs:{label:"申诉处理人","field-decorator-id":"doctorId",require:!0,"label-width":"100px",span:22}},[i("ta-suggest",{staticStyle:{width:"100%"},attrs:{"data-source":e.peopleList,"table-title-map":new Map([["label",{name:"人员姓名",style:{minWidth:"320px"}}]]),"option-config":e.optionConfig,"dropdown-match-select-width":!1},on:{select:function(t,a){return e.handleChangeSuggest(t,a)},search:e.handleSearch},scopedSlots:e._u([{key:"body-cell",fn:function(t){t.cellValue,t.column,t.columnIndex;var a=t.row;t.rowIndex;return[i("span",[e._v(e._s(a.label)+" ("+e._s(a.value)+" "+e._s(a.aae386?a.aae386:"未知科室 "+a.aaz307)+" "+e._s(a.akb021?a.akb021:"未知医院 "+a.akb020)+")")])]}}])})],1),i("ta-form-item",{attrs:{label:"已选科室","field-decorator-id":"doctorName",hidden:!0}},[i("ta-input")],1),i("ta-form-item",{attrs:{label:"","field-decorator-id":"","label-width":10,span:2}},[i("ta-button",{attrs:{type:"primary"},on:{click:e.setPeopleForDept}},[e._v(" 确定 ")])],1)],1)],1)],1)],1)],1)],1)},r=[],n=a(89584),l=a(36797),s=a.n(l),o=a(52582),c=[{value:"1",label:"下发给开单医生"},{value:"2",label:"下发给指定人员"}],u={name:"plugin",components:{},props:{visible:{type:Boolean,default:!1},firstTime:{type:Boolean,default:!1}},data:function(){return{title:"下发配置",partType:"1",options:c,optionConfig:{value:"value",label:function(t){return"".concat(t.label," (").concat(t.value," ").concat(t.aae386?t.aae386:"未知科室 "+t.aaz307," ").concat(t.akb021?t.akb021:"未知医院 "+t.akb020,")")}},detailChecked:!1,indeterminate:!1,checkAll:!1,checkedList:[],hosList:[],currentHospital:null,deptList:[],hospitalMap:new Map,deptListOrigin:[],peopleList:[]}},computed:{deptNames:function(){var t=this,e=this.deptListOrigin.filter((function(e){return t.checkedList.indexOf(e.departmentId)>-1})).map((function(t){return t.departmentName})).join(",");return e}},watch:{visible:{handler:function(t){var e=this;t&&this.$nextTick((function(){o.Z.getPluginSetting({},(function(t){e.hosList=t.data.dispatchCfg.cfgList,e.currentHospital=t.data.dispatchCfg.cfgList[0].hospitalId,e.hosList.forEach((function(t){e.hospitalMap.set(t.hospitalId,t.detailCfg)})),e.deptList=t.data.dispatchCfg.cfgList[0].detailCfg,e.deptListOrigin=(0,n.Z)(e.deptList),"0"===t.data.dispatchCfg.dispatchMode?e.partType="2":"1"===t.data.dispatchCfg.dispatchMode?e.partType="1":(e.partType="1",e.detailChecked=!0)}))}))}}},methods:{tabKeyChange:function(t){this.currentHospital=t,this.deptList=this.hospitalMap.get(t),this.deptListOrigin=(0,n.Z)(this.deptList),this.checkedList=[],this.form.resetFields("doctorId")},moment:s(),onChange:function(t){t.length<this.deptList.length&&0!==t.length?(this.checkAll=!1,this.indeterminate=!0):0===t.length?(this.checkAll=!1,this.indeterminate=!1):(this.checkAll=!0,this.indeterminate=!1),this.checkedList=t},onCheckAllChange:function(t){var e=t.target.checked;e?(this.checkAll=!0,this.checkedList=this.deptList.map((function(t){return t.departmentId}))):(this.checkAll=!1,this.checkedList=[]),this.indeterminate=!1},filterDeptList:function(t){var e=this.checkedList;if(t){this.deptList=this.deptListOrigin.filter((function(e){return e.departmentName.indexOf(t)>-1}));var a=this.deptList.map((function(t){return t.departmentId}));e=this.checkedList.filter((function(t){return a.indexOf(t)>-1}))}else this.deptList=JSON.parse(JSON.stringify(this.deptListOrigin));this.onChange(e)},changePartType:function(t){this.partType=t.target.value},changeDetailChecked:function(t){this.detailChecked=t.target.checked},handleSearch:function(t){var e=this;t&&o.Z.queryPeople({doctorName:t,akb020:this.currentHospital},(function(t){e.peopleList=t.data.list}))},handleChangeSuggest:function(t,e){this.form.setFieldsValue({doctorName:e.label})},setPeopleForDept:function(){var t=this;this.checkedList.length<=0?this.$message.warn("至少选择一个科室进行指定人员设置"):this.form.validateFields((function(e){if(!e){var a=t.form.getFieldsValue();t.deptListOrigin=t.deptListOrigin.map((function(e){return t.checkedList.indexOf(e.departmentId)>-1&&(e.doctorId=a.doctorId,e.doctorName=a.doctorName),e})),t.deptList=t.deptList.map((function(e){return t.checkedList.indexOf(e.departmentId)>-1&&(e.doctorId=a.doctorId,e.doctorName=a.doctorName),e}))}}))},handleSave:function(){var t=this,e={};e.dispatchMode="2"===this.partType?"0":!0===this.detailChecked?"2":"1",e.cfgList=this.hosList,e.doctorId=this.$route.query.aaz263,o.Z.savePluginSetting(e,(function(e){t.handleClose(),t.$emit("handleOk")}))},handleClose:function(){this.detailChecked=!1,this.partType="1",this.deptList=[],this.deptListOrigin=[],this.peopleList=[],this.indeterminate=!1,this.checkAll=!1,this.checkedList=[],this.$emit("handleClose")}}},d=u,h=a(1001),p=(0,h.Z)(d,i,r,!1,null,"c90034ac",null),f=p.exports},39101:function(t,e,a){var i=a(26557),r=a(57665),n=a(1001),l=(0,n.Z)(r.Z,i.s,i.x,!1,null,"ceb35a34",null);e["Z"]=l.exports},36766:function(t,e,a){var i=a(66586);e["Z"]=i.Z},57665:function(t,e,a){var i=a(38962);e["Z"]=i.Z},26263:function(t,e,a){a.d(e,{s:function(){return i},x:function(){return r}});var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},r=[]},26557:function(t,e,a){a.d(e,{s:function(){return i},x:function(){return r}});var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("ta-modal",{attrs:{title:t.title,visible:t.visible,height:450,width:850},on:{cancel:t.handleClose}},[a("div",{staticStyle:{height:"100%"}},[a("ta-border-layout",{attrs:{"show-border":!1,"header-cfg":{showBorder:!1}}},[a("div",[a("ta-big-table",{ref:"viewRecordTable",attrs:{height:"380px","auto-resize":"",resizable:"","highlight-hover-row":"",border:"",align:"center","header-align":"center",size:"mini",data:t.viewRecordData}},[a("ta-big-table-column",{attrs:{type:"seq",fixed:"left",width:"50px",title:"序号",align:"center"}}),a("ta-big-table-column",{attrs:{field:"aae040",align:"center",sortable:"","show-overflow":"",title:"审核时间","min-width":"100px"}}),a("ta-big-table-column",{attrs:{field:"ape800",align:"center",sortable:"","show-overflow":"",title:"审核结果","min-width":"100px"},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.row;return["0"==i.ape800?a("span",{staticStyle:{color:"#0f990f"}},[a("ta-icon",{attrs:{type:"check-circle",theme:"twoTone",twoToneColor:"#52c41a"}}),t._v("审核通过")],1):"1"==i.ape800?a("span",{staticStyle:{color:"#F59A23"}},[a("ta-icon",{attrs:{type:"question-circle",theme:"twoTone",twoToneColor:"#F59A23"}}),t._v("可疑提醒")],1):"2"==i.ape800?a("span",{staticStyle:{color:"#FF0000"}},[a("ta-icon",{attrs:{type:"exclamation-circle",theme:"twoTone",twoToneColor:"#FF0000"}}),t._v("违规提醒")],1):"3"==i.ape800?a("span",{staticStyle:{color:"#ffc49a"}},[a("ta-icon",{attrs:{type:"bell",theme:"twoTone",twoToneColor:"#e1ba91"}}),t._v("仅提醒")],1):a("div",[t._v(" — ")])]}}])}),a("ta-big-table-column",{attrs:{field:"ape893",align:"center","show-overflow":"",title:"医护操作","min-width":"80px"}},[a("ta-big-table-column-filter",{attrs:{"filter-props":{placeholder:""}}})],1),a("ta-big-table-column",{attrs:{field:"ykz041",align:"center","show-overflow":"",title:"操作人","min-width":"80px"}},[a("ta-big-table-column-filter",{attrs:{"filter-props":{placeholder:""}}})],1),a("template",{slot:"bottomBar"},[a("ta-pagination",{ref:"gridPager",staticStyle:{"text-align":"right"},attrs:{"default-page-size":500,"hide-on-single-page":!0,"page-size-options":["30","50","100","200","500"],"data-source":t.viewRecordData,params:t.infoPageParams,url:"hiddscgPoint/getViewRecordData"},on:{"update:dataSource":function(e){t.viewRecordData=e},"update:data-source":function(e){t.viewRecordData=e}}})],1)],2)],1)])],1),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("div",{staticStyle:{display:"flex","justify-content":"center"}},[a("ta-button",{staticStyle:{"text-align":"center"},on:{click:t.handleClose}},[t._v(" 关闭 ")])],1)])])},r=[]},66586:function(t,e){var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};e["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}},38962:function(t,e,a){a(52582);e["Z"]={name:"viewRecord",props:{title:{type:String,default:"匹配预警记录"},visible:{type:Boolean,default:!1},param:{type:Object,default:function(){return{}}}},data:function(){return{viewRecordData:[]}},watch:{visible:{handler:function(t){var e=this;t&&this.$nextTick((function(){e.$refs.gridPager.loadData()}))}}},mounted:function(){},methods:{infoPageParams:function(){return this.param},handleClose:function(){this.viewRecordData=[],this.$emit("handleClose")}}}},52582:function(t,e,a){var i=a(95082),r=a(95278),n="appeal/hidAppeal/",l="appeal/drPoint/";e["Z"]={getPageUrl:function(){return n+"queryListByPage"},getBasePath:function(){return r.Z.basePath},getAdmissionNumList:function(t,e){Base.submit(null,{url:"appeal/common/fuzzySearchInKf38/patientId",data:(0,i.Z)((0,i.Z)({},t),{},{igronCurrPer:!0})},{successCallback:function(t){return e(t)}})},getDeptList:function(t,e){Base.submit(null,{url:"appeal/common/fuzzySearchInKf38/OprDepartName",data:(0,i.Z)((0,i.Z)({},t),{},{igronCurrPer:!0})},{successCallback:function(t){return e(t)}})},getDeptList2:function(t,e){Base.submit(null,{url:"appeal/common/fuzzySearchInKf38/departName",data:(0,i.Z)((0,i.Z)({},t),{},{igronCurrPer:!0})},{successCallback:function(t){return e(t)}})},getCostTypeList:function(t,e){Base.submit(null,{url:"appeal/common/fuzzySearchInKf38/costType",data:(0,i.Z)((0,i.Z)({},t),{},{igronCurrPer:!0})},{successCallback:function(t){return e(t)}})},getRuleNameList:function(t,e){Base.submit(null,{url:"appeal/common/fuzzySearchInKf38/ruleName",data:(0,i.Z)((0,i.Z)({},t),{},{igronCurrPer:!0})},{successCallback:function(t){return e(t)}})},getPatintNameList:function(t,e){Base.submit(null,{url:"appeal/common/fuzzySearchInKf38/patientName",data:(0,i.Z)((0,i.Z)({},t),{},{igronCurrPer:!0})},{successCallback:function(t){return e(t)}})},getDoctorList:function(t,e){Base.submit(null,{url:"appeal/common/fuzzySearchInKf38/doctorName",data:(0,i.Z)((0,i.Z)({},t),{},{igronCurrPer:!0})},{successCallback:function(t){return e(t)}})},getObjNameList:function(t,e){Base.submit(null,{url:"appeal/common/fuzzySearchInKf38/itemName",data:(0,i.Z)((0,i.Z)({},t),{},{igronCurrPer:!0})},{successCallback:function(t){return e(t)}})},queryPeople:function(t,e){Base.submit(null,{url:"/appeal/common/getDicDoctor",data:t},{successCallback:function(t){return e(t)}})},queryHandledCount:function(t,e){Base.submit(null,{url:n+"queryHandledCount",data:t},{successCallback:function(t){return e(t)}})},getPluginSetting:function(t,e){Base.submit(null,{url:n+"getDispatchCfg",data:t},{successCallback:function(t){return e(t)}})},savePluginSetting:function(t,e){Base.submit(null,{url:n+"saveDispatchCfg",data:t,autoQs:!1},{successCallback:function(t){return e(t)}})},isPluginSetted:function(t,e){Base.submit(null,{url:n+"checkDispatchCfg",data:t},{successCallback:function(t){return e(t)}})},oneModefy:function(t,e){Base.submit(null,{url:n+"cacheDispatchRecord",data:t},{successCallback:function(t){return e(t)}})},batchModefy:function(t,e){Base.submit(null,{url:n+"cacheDispatchRecordBatch",data:t,autoQs:!1},{successCallback:function(t){return e(t)}})},distributeSave:function(t,e){Base.submit(null,{url:n+"dispatchAppeal",data:t},{successCallback:function(t){return e(t)}})},updateCurrPers:function(t,e){Base.submit(null,{url:n+"updateCurrPers",data:t,autoQs:!1},{successCallback:function(t){return e(t)}})},getDistributePageUrl:function(){return n+"getDispatchData"},reOneModefy:function(t,e){Base.submit(null,{url:n+"cacheRedispatchRecord",data:t},{successCallback:function(t){return e(t)}})},reBatchModefy:function(t,e){Base.submit(null,{url:n+"cacheReDispatchRecordBatch",data:t,autoQs:!1},{successCallback:function(t){return e(t)}})},reDistributeSave:function(t,e){Base.submit(null,{url:n+"redispatchAppeal",data:t},{successCallback:function(t){return e(t)}})},batchApproval:function(t,e){Base.submit(null,{url:n+"auditAppealBatch",data:t,autoQs:!1},{successCallback:function(t){return e(t)}})},singelApproval:function(t,e){Base.submit(null,{url:n+"auditAppeal",data:t},{successCallback:function(t){return e(t)}})},queryAae043List:function(t,e){Base.submit(null,{url:n+"queryAae043List",data:t},{successCallback:function(t){return e(t)}})},getFileList:function(t,e){Base.submit(null,{url:"appeal/drPoint/queryAppealDetail",data:t},{successCallback:function(t){return e(t)}})},getPicZipUrl:function(){return r.Z.basePath+"/appeal/drPoint/fileZipDownload"},exportZip:function(){return r.Z.basePath+"/appeal/hidAppeal/exportZip"},getFileInfo:function(t,e){Base.submit(null,{url:l+"getFileInfos",data:t},{successCallback:function(t){return e(t)}})},preSaveCheck:function(t,e){Base.submit(null,{url:n+"preSaveCheck",data:t,autoQs:!1},{successCallback:function(t){return e(t)}})},reBackRow:function(t,e){Base.submit(null,{url:n+"cancelSubmitAppeal",data:t},{successCallback:function(t){return e(t)}})}}},7485:function(t,e,a){a.d(e,{H:function(){return i}});var i=[{value:"1",label:"待审批",checked:!0,color1:"orange",color2:"#FCB76B"},{value:"2",label:"审批通过未导出",checked:!1,color1:"green",color2:"#87d068"},{value:"3",label:"审批通过已导出",checked:!1,color1:"purple",color2:"#7d02d1"},{value:"4",label:"审批驳回待提交",checked:!1,color1:"red",color2:"#fa0000"},{value:"5",label:"全部",checked:!1,color1:"blue",color2:"#2db7f5"}]}}]);
"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[5003],{93946:function(t,e,a){a.r(e),a.d(e,{default:function(){return j}});var i=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("div",{staticClass:"fit"},[i("ta-border-layout",[i("div",{staticStyle:{height:"98%"}},[i("ta-form",{staticStyle:{"margin-top":"13px"},attrs:{layout:"horizontal",formLayout:!0,"auto-form-create":function(e){return t.form=e}}},[e._l(e.defaultSearch.slice(0,4),(function(t){return[t.name.length>5?i("ta-form-item",{key:t.id,attrs:{labelWidth:"120",label:t.name,fieldDecoratorId:t.id,span:5}},["1"!=t.isTimeDimension?[t.data.length>30?i("ta-select",{attrs:{mode:"multiple",maxTagCount:1,options:t.data,"allow-clear":"",virtual:""}}):i("ta-select",{attrs:{mode:"multiple",maxTagCount:1,options:t.data,"allow-clear":""}})]:i("timePick",{attrs:{choseTime:e.choseTime,timeOptionsData:t.treeData}})],2):i("ta-form-item",{key:t.id,attrs:{label:t.name,fieldDecoratorId:t.id,span:5}},["1"!=t.isTimeDimension?[t.data.length>30?i("ta-select",{attrs:{mode:"multiple",maxTagCount:1,options:t.data,"allow-clear":"",virtual:""}}):i("ta-select",{attrs:{mode:"multiple",maxTagCount:1,options:t.data,"allow-clear":""}})]:i("timePick",{attrs:{choseTime:e.choseTime,timeOptionsData:t.treeData}})],2)]})),e.defaultSearch.length>4?[e.defaultSearch.length>4?i("ta-form-item",{staticStyle:{"text-align":"right"},attrs:{span:2}},[e.hide?i("a",{on:{click:e.hideState}},[i("ta-icon",{attrs:{slot:"suffixIcon",type:"caret-down"},slot:"suffixIcon"})],1):e._e(),e.hide?e._e():i("a",{on:{click:e.hideState}},[i("ta-icon",{attrs:{slot:"suffixIcon",type:"caret-up"},slot:"suffixIcon"})],1)]):e._e(),i("ta-form-item",{staticStyle:{"text-align":"right","margin-left":"20px"},attrs:{span:1}},[i("ta-button",{attrs:{type:"primary"},on:{click:e.searchTable}},[e._v(" 查询 ")])],1)]:[i("ta-form-item",{staticStyle:{"text-align":"right","margin-left":"20px"},attrs:{span:3}},[i("ta-button",{attrs:{type:"primary"},on:{click:e.searchTable}},[e._v(" 查询 ")])],1)],e._l(e.defaultSearch.slice(4),(function(t){return e.hide?e._e():[t.name.length>5?i("ta-form-item",{key:t.id,attrs:{labelWidth:"120",label:t.name,fieldDecoratorId:t.id,span:5}},["1"!=t.isTimeDimension?[t.data.length>30?i("ta-select",{attrs:{mode:"multiple",maxTagCount:1,options:t.data,"allow-clear":"",virtual:""}}):i("ta-select",{attrs:{mode:"multiple",maxTagCount:1,options:t.data,"allow-clear":""}})]:i("timePick",{attrs:{choseTime:e.choseTime,timeOptionsData:t.treeData}})],2):i("ta-form-item",{key:t.id,attrs:{label:t.name,fieldDecoratorId:t.id,span:5}},["1"!=t.isTimeDimension?[t.data.length>30?i("ta-select",{attrs:{mode:"multiple",maxTagCount:1,options:t.data,"allow-clear":"",virtual:""}}):i("ta-select",{attrs:{mode:"multiple",maxTagCount:1,options:t.data,"allow-clear":""}})]:i("timePick",{attrs:{choseTime:e.choseTime,timeOptionsData:t.treeData}})],2)]}))],2),i("div",{staticStyle:{height:"1px",background:"#eee","margin-top":"0px"}}),i("div",{staticClass:"tableBox",attrs:{id:"tableBox"}},[i("div",{staticClass:"tableBox_title"},[i("div",[i("span",{attrs:{id:"name"}},[e._v("维度:")]),i("el-checkbox-group",{staticStyle:{float:"right","margin-top":"2px"},on:{change:e.changeDimensionality},model:{value:e.selectList,callback:function(t){e.selectList=t},expression:"selectList"}},e._l(e.dimensionalityCheckBox,(function(t){return i("el-checkbox",{key:t.value,attrs:{label:t.value}},[e._v(e._s(t.label))])})),1)],1),i("ta-button",{attrs:{type:"primary"},on:{click:e.exportData}},[e._v(" 导出 ")])],1),i("div",{staticStyle:{"margin-top":"10px",height:"100%"},attrs:{id:"tableBox_1"}},[i("ta-big-table",{ref:"bigTable",attrs:{align:"center",border:"",data:e.tableData,"show-overflow":"",size:"small","highlight-current-row":"",height:"100%","scroll-y":{gt:-1},"cell-style":e.cellStyle,"auto-resize":"","span-method":e.objectSpanMethod}},[i("template",{slot:"empty"},[i("ta-empty")],1),e._l(e.tableColArr,(function(t,a){return["dim_18"==t.field?i("ta-big-table-column",{attrs:{field:"dim_18",title:"病组编码",width:"200"}}):e._e(),i("ta-big-table-column",{key:a+Math.random(),attrs:{field:t.field+"_desc",title:t.name,width:"201"},scopedSlots:e._u([{key:"header",fn:function(){return[e.tableColArrLen+1==1?[e._v(" "+e._s(t.name)+" ")]:[e._v(" "+e._s(t.name)+" "),0==a?i("ta-icon",{attrs:{type:"right"},on:{click:function(a){return e.moveColumn(t,"right")}}}):a==e.tableColArrLen?i("ta-icon",{attrs:{type:"left"},on:{click:function(a){return e.moveColumn(t,"left")}}}):[i("ta-icon",{attrs:{type:"left"},on:{click:function(a){return e.moveColumn(t,"left")}}}),i("ta-icon",{attrs:{type:"right"},on:{click:function(a){return e.moveColumn(t,"right")}}})]]]},proxy:!0}],null,!0)})]})),e._l(e.tableColumn,(function(t,a){return[0===t.child.length&&!0===t.fold?i("ta-big-table-column",{key:t.field,attrs:{field:t.field,title:t.name,"min-width":"150"},scopedSlots:e._u([{key:"header",fn:function(){return[e._v(" "+e._s(t.name)+" "),!0===t.sign?i("a",{staticStyle:{"margin-right":"5px"},on:{click:function(a){return e.changeFold(t.name,"open")}}},[i("ta-icon",{attrs:{type:"plus"}})],1):e._e(),i("ta-popover",{attrs:{placement:"bottom"}},[i("div",{staticClass:"referenceStyle"},[i("ta-input",{ref:"searchInput",refInFor:!0,attrs:{placeholder:"搜索内容"},on:{pressEnter:function(a){return e.filterHandle(t.field,t.value)}},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"item.value"}}),i("ta-button",{attrs:{type:"primary"},on:{click:function(a){return e.filterHandle(t.field,t.value)}}},[e._v("搜索")]),i("ta-button",{attrs:{id:"searchDrgMdcCodeButton"},on:{click:function(a){return e.filterHandleCancel(t.field,t.value)}}},[e._v("重置")])],1),i("ta-icon",{attrs:{slot:"reference",type:"filter"},slot:"reference"})],1)]},proxy:!0},!0===t.jump?{key:"default",fn:function(a){var l=a.row;return[i("a",{on:{click:function(a){return e.jumpMethod(t.jumpID,t.jumpName,t.jumpUrl,l)}}},[e._v(e._s(l[t.field]))])]}}:"diff"==t.field.substr(-4)?{key:"default",fn:function(a){var l=a.row;return[l[t.field]>0?i("div",[e._v(" "+e._s(l[t.field])+" "),i("ta-icon",{staticStyle:{color:"green"},attrs:{type:"caret-up"}})],1):l[t.field]<0?i("div",[e._v(" "+e._s(l[t.field])+" "),i("ta-icon",{staticStyle:{color:"red"},attrs:{type:"caret-down"}})],1):i("div",[e._v(" "+e._s(l[t.field])+" ")])]}}:null],null,!0)}):e._e(),t.child.length>0&&!0===t.fold&&"plus"===t.calculate?i("ta-big-table-column",{key:a,attrs:{title:t.name},scopedSlots:e._u([{key:"header",fn:function(){return[i("div",[e._v(" "+e._s(t.name)+" "),!0===t.sign&&"plus"===t.calculate?i("a",{on:{click:function(a){return e.changeFold(t.name,"open")}}},[i("ta-icon",{attrs:{type:"plus"}})],1):e._e(),!0===t.sign&&"minus"===t.calculate?i("a",{on:{click:function(a){return e.changeFold(t.name,"close")}}},[i("ta-icon",{attrs:{type:"minus"}})],1):e._e()])]},proxy:!0}],null,!0)},[e._l(t.child,(function(a,l){return[i("ta-big-table-column",{key:l,attrs:{field:a.field,title:a.name,width:"150"},scopedSlots:e._u([!0===a.jump?{key:"default",fn:function(t){var l=t.row;return[i("a",{on:{click:function(t){return e.jumpMethod(a.jumpID,a.jumpName,a.jumpUrl,l)}}},[e._v(e._s(l[a.field]))])]}}:"diff"==t.field.substr(-4)?{key:"default",fn:function(a){var l=a.row;return[l[t.field]>0?i("div",[e._v(" "+e._s(l[t.field])+" "),i("ta-icon",{staticStyle:{color:"green"},attrs:{type:"caret-up"}})],1):l[t.field]<0?i("div",[e._v(" "+e._s(l[t.field])+" "),i("ta-icon",{staticStyle:{color:"red"},attrs:{type:"caret-down"}})],1):i("div",[e._v(" "+e._s(l[t.field])+" ")])]}}:null],null,!0)})]}))],2):e._e(),t.child.length>0&&!0===t.fold&&"minus"===t.calculate?i("ta-big-table-colgroup",{key:a,attrs:{title:t.name},scopedSlots:e._u([{key:"header",fn:function(){return[i("div",[e._v(" "+e._s(t.name)+" "),!0===t.sign&&"plus"===t.calculate?i("a",{on:{click:function(a){return e.changeFold(t.name,"open")}}},[i("ta-icon",{attrs:{type:"plus"}})],1):e._e(),!0===t.sign&&"minus"===t.calculate?i("a",{on:{click:function(a){return e.changeFold(t.name,"close")}}},[i("ta-icon",{attrs:{type:"minus"}})],1):e._e()])]},proxy:!0}],null,!0)},[e._l(t.child,(function(a,l){return[i("ta-big-table-column",{key:l,attrs:{field:a.field,title:a.name,width:"150"},scopedSlots:e._u([!0===a.jump?{key:"default",fn:function(t){var l=t.row;return[i("a",{on:{click:function(t){return e.jumpMethod(a.jumpID,a.jumpName,a.jumpUrl,l)}}},[e._v(e._s(l[a.field]))])]}}:"diff"==t.field.substr(-4)?{key:"default",fn:function(a){var l=a.row;return[l[t.field]>0?i("div",[e._v(" "+e._s(l[t.field])+" "),i("ta-icon",{staticStyle:{color:"green"},attrs:{type:"caret-up"}})],1):l[t.field]<0?i("div",[e._v(" "+e._s(l[t.field])+" "),i("ta-icon",{staticStyle:{color:"red"},attrs:{type:"caret-down"}})],1):i("div",[e._v(" "+e._s(l[t.field])+" ")])]}}:null],null,!0)},[a.child?[e._l(a.child,(function(t,e){return[i("ta-big-table-column",{key:e,attrs:{field:t.field,title:t.name,width:"150"}})]}))]:e._e()],2)]}))],2):e._e()]}))],2)],1)])],1)])],1)},l=[],n=a(49699),r=a(60410),o=a(29251),s=a(27366),c=a(78683),u=a(74771),d=a(542),m=a(20675),f=a(6204),h=a(46109),p=a(11119),b=a(14415),v=a(64714),y=a(60213),g=a(31104),_=a(34209),D=(a(56265),{components:{dimensionalityMonth:n.Z,dimensionalityYear:r.Z,dimensionalityArea:o.Z,dimensionalityMiType:s.Z,dimensionalityPay:c.Z,dimensionalityDisease:u.Z,dimensionalityDepartment:d.Z,dimensionalityPatientArea:m.Z,dimensionalityStaff:f.Z,barChart:h.Z,histogram:p.Z,lineChart:b.Z,lineHistogram:v.Z,pieChart:y.Z,configTool:g.Z,timePick:_.Z},data:function(){return{resourceid:"",rowData:"",defaultDimension:"",defaultSearch:[],tableColumn:[],defaultChart:[],chartData:[],dataArr:{},pageSeach:!1,choseTime:"",buttonState:!0,current:1,dimensionalityArr:[],addDimensionState:!0,hide:!0,chartHide:!0,firstName:"",tableData:[],oldTableData:[],searchObj:{},searchLineArr:[],conflict:!0,dimensionalityCheckBox:[],selectList:[],tableColArr:[],tableColArrLen:0,tableDataFilter:[],modalBtn:!0}},watch:{pageSeach:{handler:function(t){var e=this;t&&this.$nextTick((function(){e.init()}))}}},created:function(){var t=this.$route.query;this.resourceid=t.resourceid,this.defaultDimension=t.defaultDimension,this.defaultSearch=t.defaultSearch,this.defaultChart=t.defaultChart,this.chartData=t.chartData,this.dataArr=t.dataArr},mounted:function(){var t=this,e=this.$route.query;this.rowData=void 0==e.rowData?"":JSON.parse(e.rowData),""!=this.rowData?this.$nextTick((function(){t.Base.submit(null,{url:"statisticsModal/queryStatisticsDimension",data:{resourceId:t.resourceid}},{successCallback:function(e){var a=e.data.result;a.map((function(e){t.dimensionalityCheckBox.push({label:e.name,value:e.id})})),t.searchObj.resourceid=t.resourceid;var i=[];t.dimensionalityCheckBox.forEach((function(e){for(var a in t.rowData)e.value==a&&i.push(a+"")})),t.defaultSearch.forEach((function(e){if("1"==e.isTimeDimension)for(var a in t.rowData)a==e.id&&(t.choseTime=t.rowData[a][0])})),t.form.setFieldsValue(t.rowData),t.selectList=i,t.$nextTick((function(){t.searchTable()}))}})})):this.pageSeach=!0},methods:{getPageTargetInfo:function(t){var e=[];t.forEach((function(t){if(t.children){var a={},i={};a.name=t.indexLabel,a.field=t.indexId,a.fold=!0,a.sign=!0,t.jumpResourceName&&(a.jump=!0,a.jumpName=t.jumpResourceName,a.jumpID=t.jumpResourceId,a.jumpUrl=t.jumpResourceUrl),a.value="",a.calculate="plus",a.child=[],i.name=t.indexLabel,i.field=t.indexId,i.fold=!1,i.sign=!0,t.jumpResourceName&&(i.jump=!0,i.jumpName=t.jumpResourceName,i.jumpID=t.jumpResourceId,i.jumpUrl=t.jumpResourceUrl),i.value="",i.calculate="minus",i.child=[],t.children.forEach((function(t){var e={};e.name=t.indexLabel,e.field=t.indexId,t.jumpResourceName&&(e.jump=!0,e.jumpName=t.jumpResourceName,e.jumpID=t.jumpResourceId,e.jumpUrl=t.jumpResourceUrl),e.value="",i.child.push(e)})),e.push(a),e.push(i)}else{var l={};l.name=t.indexLabel,l.field=t.indexId,l.fold=!0,l.sign=!1,t.jumpResourceName&&(l.jump=!0,l.jumpName=t.jumpResourceName,l.jumpID=t.jumpResourceId,l.jumpUrl=t.jumpResourceUrl),l.value="",l.child=[],e.push(l)}})),this.tableColumn=e},init:function(){var t=this;this.Base.submit(null,{url:"statisticsModal/queryStatisticsDimension",data:{resourceId:this.resourceid}},{successCallback:function(e){var a=e.data.result;a.map((function(e){t.dimensionalityCheckBox.push({label:e.name,value:e.id})})),t.$nextTick((function(){t.searchTable(),t.searchObj.resourceid=t.resourceid}))}})},hideState:function(){this.hide=!this.hide},changeFold:function(t,e){"open"===e?this.editFold(!0,t):"close"===e&&this.editFold(!1,t)},editFold:function(t,e){for(var a=0;a<this.tableColumn.length;a++)if(this.tableColumn[a].name===e){!0===t?(this.tableColumn[a].fold=!1,this.tableColumn[a+1].fold=!0):!1===t&&(this.tableColumn[a].fold=!0,this.tableColumn[a+1].fold=!1);break}},objectOrder:function(t){var e=Object.keys(t).sort();return e},searchTable:function(){var t=this;this.tableColArr=[];for(var e=function(e){t.dimensionalityCheckBox.map((function(a){t.selectList[e]==a.value&&t.tableColArr.push({field:"dim_"+a.value,name:a.label})}))},a=0;a<this.selectList.length;a++)e(a);this.tableColArrLen=this.tableColArr.length-1;var i=this.form.getFieldsValue();for(var l in i)void 0==i[l]&&delete i[l];var n=this.selectList.join(",");this.Base.submit(null,{url:"statisticsModal/queryStatisticsTableData",data:{condition:i,dimension:n,resourceId:this.resourceid}}).then((function(e){t.oldTableData=e.data.result.data,t.tableData=JSON.stringify(t.oldTableData),t.tableData=JSON.parse(t.tableData),t.getPageTargetInfo(e.data.result.column);var a=[];t.tableColumn.forEach((function(t){t.child.length>0?t.child.forEach((function(t){a.push(t.field+"")})):a.push(t.field+"")}));var l=Array.from(new Set(a));t.searchObj={resourceid:t.resourceid,dimensionId:"",dimensionValue:"",condition:i,filter:{},sumId:"",sumValue:"",find:"",index:l}}))},cellStyle:function(t){t.row;var e=t.rowIndex,a=t.column;t.columnIndex;if(""!=this.firstName&&""!=this.searchObj.sumId&&"dim_desc"===a.property&&[0].includes(e))return{textAlign:"left"}},getPageInfo:function(){var t=this;this.Base.submit(null,{url:"statisticsModal/queryBaseStatisticsConfig",data:{resourceId:this.resourceid}}).then((function(e){var a=e.data.result;t.chartData=[],a.defaultChartIndexList.forEach((function(e){var a={};a.name=e.indexLabel,a.indexId=e.indexId,e.indexChartType&&(a.indexChartType=e.indexChartType),a.value=["0"],t.chartData.push(a)})),t.dataArr=a}))},filterHandle:function(t,e){var a=this;this.tableDataFilter.push({id:t,value:e}),this.tableDataFilter.forEach((function(t){a.tableData=a.tableData.filter((function(e){return e[t.id]==t.value}))}))},filterHandleCancel:function(t,e){var a=this;this.tableDataFilter.forEach((function(e,i){e.id==t&&a.tableDataFilter.splice(i,1)})),this.tableColumn.forEach((function(e){e.field==t&&(e.value="")})),this.tableDataFilter.length>0?this.tableDataFilter.forEach((function(t){a.tableData=a.oldTableData.filter((function(e){return e[t.id]==t.value}))})):this.tableData=this.oldTableData},exportData:function(){},moveColumn:function(t,e){for(var a=this,i=JSON.parse(JSON.stringify(this.selectList)),l=0,n=0;n<i.length;n++){var r="dim_"+i[n];t.field==r&&(l=n)}if("right"==e){var o=i[l+1];i[l+1]=i[l],i[l]=o}else{var s=i[l-1];i[l-1]=i[l],i[l]=s}this.selectList=i,this.$nextTick((function(){a.searchTable()}))},jumpMethod:function(t,e,a,i){var l=Object.getOwnPropertyNames(i),n={};this.tableColArr.forEach((function(t){l.forEach((function(e){if(t.field==e){var a=e.indexOf("_"),i=e.substring(a+1);n[i]=[]}}))})),this.tableColArr.forEach((function(t){l.forEach((function(e){if(t.field==e){var a=e.indexOf("_"),l=e.substring(a+1);n[l].push(i[e])}}))})),n=JSON.stringify(n),this.Base.openTabMenu({id:t,name:e,url:"".concat(a,"?rowData=").concat(n)})},changeDimensionality:function(t){this.searchTable()},handleRowSpan:function(t,e,a){var i=t,l=0;for(i;i<this.tableData.length;i++){var n=this.calculateName(t,e,this.tableData,a);if(0==t)this.calculateName(i,e,this.tableData,a)==n&&(l+=1);else{if(this.calculateName(i,e,this.tableData,a)==this.calculateName(t-1,e,this.tableData,a)){l=0;break}this.calculateName(i,e,this.tableData,a)==n&&(l+=1)}}return l},calculateName:function(t,e,a,i){for(var l="",n=0;n<=e;n++)l+=a[t][i[n]];return l},objectSpanMethod:function(t){t.row;var e=t.column,a=t.rowIndex,i=t.columnIndex,l=[];this.tableColArr.map((function(t){l.push(t.field)}));var n=this.handleRowSpan(a,i,l),r=n>0?1:0;if("201"==e.width)return{rowspan:n,colspan:r}}}}),x=D,k=a(1001),C=(0,k.Z)(x,i,l,!1,null,"b2d8a8c2",null),j=C.exports}}]);
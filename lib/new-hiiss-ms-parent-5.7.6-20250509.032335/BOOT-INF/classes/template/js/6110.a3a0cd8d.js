(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[6110],{88412:function(t,e,i){"use strict";var l=i(26263),a=i(36766),o=i(1001),r=(0,o.Z)(a.Z,l.s,l.x,!1,null,"5e7ef0ae",null);e["Z"]=r.exports},41668:function(t,e,i){"use strict";i.r(e),i.d(e,{default:function(){return m}});var l=function(){var t=this,e=this,i=e.$createElement,l=e._self._c||i;return l("div",{staticClass:"fit"},[l("ta-border-layout",{attrs:{layout:{header:"180px",footer:"0px"},showPadding:!0,"footer-cfg":{showBorder:!1}}},[l("div",{attrs:{slot:"header"},slot:"header"},[l("ta-title",{attrs:{title:"查询条件"}}),l("ta-form",{staticStyle:{"padding-top":"5px"},attrs:{autoFormCreate:function(e){t.searchForm=e},layout:"horizontal",formLayout:!0}},[l("ta-form-item",{attrs:{span:6,label:"知识元",fieldDecoratorId:"ykz018"}},[l("ta-input",{attrs:{placeholder:"请输入"}})],1),l("ta-form-item",{attrs:{span:6,label:"规则类别",fieldDecoratorId:"ykz199"}},[l("ta-select",{attrs:{placeholder:"请选择",collectionType:"MYKZ199",allowClear:!0,"show-search":!0}})],1),l("ta-form-item",{attrs:{span:6,label:"违规定性",fieldDecoratorId:"volaQualCodg"}},[l("ta-select",{attrs:{showSearch:"",placeholder:"请选择",allowClear:!0,"collection-type":"MYKZ200"}})],1),l("ta-form-item",{attrs:{span:6,label:"违规程度",fieldDecoratorId:"ruleSevDeg"}},[l("ta-select",{attrs:{placeholder:"请选择",allowClear:!0,"collection-type":"MAPE800"}})],1),l("ta-form-item",{attrs:{span:6,label:"监控场景",fieldDecoratorId:"ruleid"}},[l("ta-select",{attrs:{placeholder:"请选择",allowClear:!0,"show-search":!0}},e._l(e.ruleidList,(function(t,i){return l("ta-select-option",{key:i,attrs:{value:t.ruleid}},[e._v(" "+e._s(t.ruleidlog)+" ")])})),1)],1),l("ta-form-item",{attrs:{span:6,label:"更新日期",fieldDecoratorId:"updateTimeRange"}},[l("ta-range-picker",{attrs:{"allow-clear":!0}})],1),l("ta-form-item",{attrs:{span:4,label:"医保编码",fieldDecoratorId:"ake001","label-width":"92px"}},[l("ta-input")],1),l("ta-form-item",{attrs:{span:4,label:"医保名称",fieldDecoratorId:"ake002","label-width":"92px"}},[l("ta-input")],1),l("ta-button",{staticStyle:{"margin-right":"10px",float:"right"},attrs:{type:"default",icon:"redo"},on:{click:e.resetSearch}},[e._v("重置 ")]),l("ta-button",{staticStyle:{"margin-right":"20px",float:"right"},attrs:{type:"primary",icon:"search"},on:{click:e.onSearch}},[e._v("查询 ")])],1)],1),l("div",{staticClass:"fit content-box"},[l("ta-title",{staticStyle:{flex:"none","margin-left":"5px","margin-top":"5px"},attrs:{title:"本级规则列表"}},["base"===e.ruleFindModifyType?l("div",{staticStyle:{float:"right"}},[l("ta-button",{attrs:{type:"primary"},on:{click:function(t){return e.modifyAPE800()}}},[e._v("批量修改违规程度")]),e.ruleFindExportVisible?l("ta-button",{staticStyle:{position:"absolute",right:"6px",bottom:"6px"},attrs:{type:"primary",icon:"download"},on:{click:e.goexport}},[e._v(" 导出 ")]):e._e()],1):e._e()]),l("div",{staticClass:"content-table"},[l("ta-big-table",{ref:"ruleTable",attrs:{columns:e.columns,data:e.ruleList,border:"","empty-text":"-",height:"auto","highlight-hover-row":"","auto-resize":"","show-overflow":"",resizable:"","row-key":!0,"row-id":"ykz277"},scopedSlots:e._u([{key:"supnRuleStas",fn:function(t){var i=t.row;return["3"==i.supnRuleStas||"6"==i.supnRuleStas||"7"==i.supnRuleStas?l("ta-tag",{attrs:{color:"red"}},[e._v(e._s(e.CollectionLabel("M_RULE_STAS",i.supnRuleStas))+" ")]):e._e(),"5"==i.supnRuleStas?l("ta-tag",{attrs:{color:"green"}},[e._v(" "+e._s(e.CollectionLabel("M_RULE_STAS",i.supnRuleStas))+" ")]):e._e(),"0"==i.supnRuleStas||"1"==i.supnRuleStas||"4"==i.supnRuleStas?l("ta-tag",{attrs:{color:"blue"}},[e._v(e._s(e.CollectionLabel("M_RULE_STAS",i.supnRuleStas))+" ")]):e._e()]}},{key:"modifyRecord",fn:function(t){var i=t.row;return[i.updatedtime?l("span",[e._v(" "+e._s("修改人:"+i.updatedby+",修改时间:"+i.updatedtime+",修改理由:"+i.updatereason)+" ")]):e._e()]}},{key:"operation",fn:function(t){var i=t.row;return["base"===e.ruleFindModifyType?l("div",{staticClass:"opareteItem",on:{click:function(t){return e.goModify(i)}}},[e._v(" 修改 ")]):e._e(),"base"===e.ruleFindModifyType?l("ta-divider",{attrs:{type:"vertical"}}):e._e(),l("div",{staticClass:"opareteItem",on:{click:function(t){return e.goDetail(i)}}},[e._v(" 详情 ")])]}}])})],1),l("div",{staticClass:"content-box-footer"},[l("ta-pagination",{ref:"rulePager",style:e.exportStyle,attrs:{defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"],dataSource:e.ruleList,params:e.pageParams,url:"mttRuleFind/pageRule"},on:{"update:dataSource":function(t){e.ruleList=t},"update:data-source":function(t){e.ruleList=t}}})],1)],1)]),e.detailVisible?l("div",{staticStyle:{width:"100%",height:"100%",position:"absolute",top:"0",left:"0","z-index":"10",background:"white"}},[l("rule-detail",{attrs:{params:this.params},on:{close:e.detailClose}})],1):e._e(),l("ta-modal",{attrs:{title:"批量修改违规程度",visible:e.batchModefyVisible,height:220,width:450},on:{ok:function(t){return e.handleBatchModefy()},cancel:e.closeBatchModefyModel}},[l("ta-form",{attrs:{"auto-form-create":function(e){return t.form1=e},layout:"horizontal","form-layout":!0,"label-width":"120px",col:{xs:1,sm:1,md:1,lg:1,xl:1,xxl:1}}},[l("ta-form-item",{attrs:{label:"违规程度","field-decorator-id":"ape800",require:!0,span:22}},[l("ta-select",{attrs:{placeholder:"请选择违规程度",allowClear:!0,"collection-type":"MAPE800"}})],1),l("ta-form-item",{attrs:{label:"修改原因","field-decorator-id":"reasons",require:!0,span:22}},[l("ta-textarea",{attrs:{placeholder:"请输入修改原因","max-length":100,"show-max-length":!0,rows:3}})],1)],1)],1)],1)},a=[],o=i(66347),r=i(95082),n=i(94872),s=i(22722),c=i(55115),u=i(88412);c.w3.prototype.Base=Object.assign(c.w3.prototype.Base,(0,r.Z)({},s.Z));var d={components:{TaTitle:u.Z,ruleDetail:n["default"]},data:function(){return{blnShowAll:!1,volaQualList:[],ruleidList:[],columns:[{field:"checkbox",type:"checkbox",title:"",minWidth:"40",align:"center"},{type:"seq",title:"序号",minWidth:"60",align:"center"},{field:"ykz277",title:"规则id",minWidth:100,align:"center",overflowTooltip:!0},{field:"ruletype",title:"规则分类",minWidth:120,align:"center",overflowTooltip:!0},{field:"aaa166",title:"规则大类",minWidth:150,align:"center",overflowTooltip:!1},{field:"ykz018",title:"知识元",minWidth:200,align:"center",overflowTooltip:!0},{field:"source",title:"规则依据",minWidth:200,align:"center",overflowTooltip:!0},{field:"ruleidlog",title:"监控场景",minWidth:100,align:"center",overflowTooltip:!0},{field:"ykz199",title:"规则类别",collectionType:"MYKZ199",minWidth:100,align:"center",overflowTooltip:!0},{field:"volaqualcodg",title:"违规定性",collectionType:"MYKZ200",minWidth:100,align:"center",overflowTooltip:!0},{field:"rulever",title:"规则版本",minWidth:100,align:"center",overflowTooltip:!0},{field:"rulesevdeg",title:"违规程度",collectionType:"MAPE800",minWidth:80,align:"center",overflowTooltip:!0},{field:"operationTime",title:"发布日期",minWidth:150,align:"center",overflowTooltip:!0},{field:"modifyRecord",title:"修改记录",minWidth:150,align:"center",overflowTooltip:!0,customRender:{default:"modifyRecord"}},{field:"operation",title:"操作",fixed:"right",minWidth:120,align:"center",customRender:{default:"operation"}}],columns2:[{field:"ykz277",title:"规则id",minWidth:100,align:"center",overflowTooltip:!0},{field:"aaa166",title:"规则大类",minWidth:150,align:"center",overflowTooltip:!1},{field:"ykz018",title:"知识元",minWidth:200,align:"center",overflowTooltip:!0},{field:"ruleidlog",title:"监控场景",minWidth:100,align:"center",overflowTooltip:!0},{field:"ykz199",title:"规则类别",collectionType:"MYKZ199",minWidth:100,align:"center",overflowTooltip:!0},{field:"volaQualName",title:"违规定性",minWidth:100,align:"center",overflowTooltip:!0},{field:"rulever",title:"规则版本",minWidth:100,align:"center",overflowTooltip:!0},{field:"ruleSevDeg",title:"违规程度",collectionType:"M_SEV_DEG",minWidth:70,align:"center",overflowTooltip:!0},{field:"",title:"上报日期",minWidth:150,align:"center",overflowTooltip:!0},{field:"",title:"区域划分代码",collectionType:"",minWidth:100,align:"center",overflowTooltip:!0},{field:"",title:"医保划分",collectionType:"",minWidth:70,align:"center",overflowTooltip:!0},{field:"",title:"规则级别",collectionType:"",minWidth:70,align:"center",overflowTooltip:!0},{field:"operation",title:"操作",minWidth:70,align:"center",customRender:{default:"operation"}}],ruleList:[],ruleList2:[],editVisible:!1,detailVisible:!1,batchModefyVisible:!1,ruleFindModifyType:faceConfig.ruleFindModifyType,params:{},ruleFindExportVisible:!1,exportStyle:{"text-align":"right","margin-top":"10px"}}},mounted:function(){this.listVolaQualA(),this.listRuleid(),this.$route.query.ykz010&&(this.searchForm.setFieldsValue({ykz018:this.$route.query.ykz010}),this.searchForm.setFieldsValue({ruleid:this.$route.query.ruleid})),this.exportVisible(),this.onSearch()},methods:{detailClose:function(){this.detailVisible=!1,this.onSearch()},handleBatchModefy:function(){var t=this,e=this.$refs.ruleTable.getCheckboxRecords();this.form1.validateFields((function(i){if(!i&&e.length>0){var l=t.form1.getFieldsValue();l.records=e,t.Base.submit(null,{url:"violationModify/insertBatch",data:l,autoValid:!1,autoQs:!1},{successCallback:function(e){t.$message.success("修改成功！"),t.closeBatchModefyModel()},failCallback:function(e){t.$message.error("修改失败！")}})}}))},closeBatchModefyModel:function(){this.form1.resetFields(),this.batchModefyVisible=!1,this.onSearch()},modifyAPE800:function(){var t=this.$refs.ruleTable.getCheckboxRecords();0!==t.length?(t.length>1&&this.$message.warning("批量修改，请谨慎操作！"),this.batchModefyVisible=!0,this.$nextTick((function(){}))):this.$message.error("请选择需要修改的数据！")},exportVisible:function(){var t=this;Base.submit(null,{url:"miimCommonRead/getAa01Aaa005ByAaz499",data:{aaz499:190}}).then((function(e){var i=e.data.aaa005;["Y","y"].includes(i)&&(t.ruleFindExportVisible=!0,t.exportStyle={position:"absolute",right:"6px",bottom:"6px","margin-right":"100px"})}))},onSearch:function(){var t=this;this.$refs.rulePager.loadData(),this.$nextTick((function(){"base"===t.ruleFindModifyType?(t.$refs.ruleTable.showColumn(t.$refs.ruleTable.getColumnByField("checkbox")),t.$refs.ruleTable.showColumn(t.$refs.ruleTable.getColumnByField("modifyRecord"))):(t.$refs.ruleTable.hideColumn(t.$refs.ruleTable.getColumnByField("checkbox")),t.$refs.ruleTable.hideColumn(t.$refs.ruleTable.getColumnByField("modifyRecord")))}))},listVolaQualA:function(){var t=this;Base.submit(null,{url:"mttRuleConfig/listVolaQualA"}).then((function(e){t.volaQualList=e.data.list}))},listRuleid:function(){var t=this;Base.submit(null,{url:"mttRuleConfig/listRuleid"}).then((function(e){t.ruleidList=e.data.list}))},pageParams:function(){var t=this.searchForm.getFieldsValue();if(t.updateTimeRange&&2==t.updateTimeRange.length){var e=t.updateTimeRange[0].format("YYYY-MM-DD"),i=t.updateTimeRange[1].format("YYYY-MM-DD");t.timeStart=e,t.timeEnd=i,t.updateTimeRange=null}return{dtoStr:JSON.stringify(t)}},goDetail:function(t){this.detailVisible=!0,this.params=t,this.params.modify=!0},goModify:function(t){this.detailVisible=!0,this.params=t,this.params.modify=!1},goStart:function(t){},goEnd:function(t){},goAdd:function(){alert("新增")},doDelete:function(t){alert("作废")},resetSearch:function(){var t=this;this.searchForm.resetFields(),this.$nextTick((function(){t.onSearch()}))},callback:function(t){},goexport:function(){var t=this,e=this.searchForm.getFieldsValue();if(e.updateTimeRange&&2==e.updateTimeRange.length){var i=e.updateTimeRange[0].format("YYYY-MM-DD"),l=e.updateTimeRange[1].format("YYYY-MM-DD");e.timeStart=i,e.timeEnd=l,e.updateTimeRange=null}var a,r=[],n=this.columns,s=(0,o.Z)(n);try{for(s.s();!(a=s.n()).done;){var c=a.value;"seq"!==c.type&&"操作"!==c.title&&r.push({header:c.title,key:c.field,width:20})}}catch(d){s.e(d)}finally{s.f()}var u=[{codeType:"MYKZ199",columnKey:"ykz199"},{codeType:"MYKZ200",columnKey:"volaqualcodg"},{codeType:"MAPE800",columnKey:"rulesevdeg"}];this.Base.submit(null,{url:"mttRuleFind/exportRule",data:{dtoStr:JSON.stringify(e)},autoValid:!1},{successCallback:function(e){var i={fileName:"规则查询列表导出结果表",sheets:[{name:"worksheet1",column:{complex:!1,columns:r},codeList:u,rows:e.data.data}]};t.Base.generateExcel(i)},failCallback:function(e){t.$message.error("规则查询列表导出失败")}})}}},f=d,p=i(1001),h=(0,p.Z)(f,l,a,!1,null,"6df7eb94",null),m=h.exports},36766:function(t,e,i){"use strict";var l=i(66586);e["Z"]=l.Z},26263:function(t,e,i){"use strict";i.d(e,{s:function(){return l},x:function(){return a}});var l=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("h3",{staticStyle:{"font-weight":"normal"}},[i("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),i("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},a=[]},66586:function(t,e){"use strict";var i={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};e["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:i}}}},55382:function(){},61219:function(){}}]);
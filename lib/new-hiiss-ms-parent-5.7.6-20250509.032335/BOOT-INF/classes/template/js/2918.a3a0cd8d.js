"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[2918],{88412:function(t,a,e){var s=e(26263),r=e(36766),i=e(1001),o=(0,i.Z)(r.Z,s.s,s.x,!1,null,"5e7ef0ae",null);a["Z"]=o.exports},72918:function(t,a,e){e.r(a),e.d(a,{default:function(){return h}});var s=function(){var t=this,a=this,e=a.$createElement,s=a._self._c||e;return s("div",{staticClass:"fit"},[s("div",{staticStyle:{height:"390px"}},[s("ta-border-layout",{attrs:{layout:{header:"0px",footer:"0px",left:"30%",right:"40%"}}},[s("div",[s("div",{staticClass:"fit"},[s("ta-title",{attrs:{title:"每晚预审"}},[s("span",{staticStyle:{display:"inline-block",float:"right"}},[s("div",{staticClass:"currentTime"},[a._v("更新时间 : "+a._s(a.currentDay))]),s("a",{staticClass:"more",on:{click:function(t){return a.handleNightAuditMore()}}},[a._v("查看明细>>")])])]),s("div",{attrs:{id:"main"}})],1)]),s("div",{attrs:{slot:"left"},slot:"left"},[s("div",{staticStyle:{width:"500px"}},[s("div",[s("h1",{staticClass:"name"},[a._v("你好,"+a._s(a.drName))]),s("h3",{staticClass:"title"},[a._v("欢迎使用医保智能监管系统")]),s("h2",{staticClass:"title1"},[a._v("今日")])]),s("div",{staticClass:"gutter-example"},[s("ta-row",{attrs:{gutter:[16,30]}},[s("ta-col",{staticClass:"gutter-row",attrs:{span:2}},[s("div",{staticClass:"gutter-box"})]),s("ta-col",{staticClass:"gutter-row",attrs:{span:4}},[s("div",{staticClass:"gutter-box"},[a._v("门诊开方")])]),s("ta-col",{staticClass:"gutter-row",attrs:{span:2}},[s("div",{staticClass:"gutter-box"},[a._v(a._s(a.drTodayInfo.otpTodayNum))])]),s("ta-col",{staticClass:"gutter-row",attrs:{span:3}},[s("div",{staticClass:"gutter-box"},[a._v("提醒")])]),s("ta-col",{staticClass:"gutter-row",attrs:{span:2}},[s("div",{staticClass:"gutter-box"},[a._v(a._s(a.drTodayInfo.otpAwakeTodayNum))])]),s("ta-col",{staticClass:"gutter-row",attrs:{span:3}},[s("div",{staticClass:"gutter-box"},[a._v("占比")])]),s("ta-col",{staticClass:"gutter-row",attrs:{span:2}},[s("div",{staticClass:"gutter-box"},[a._v(a._s(a.drTodayInfo.otpAwakePercen))])]),s("ta-col",{staticClass:"gutter-row",attrs:{span:3}},[s("div",{staticClass:"gutter-box"},[a._v("较昨日")])]),s("ta-col",{staticClass:"gutter-row",attrs:{span:2}},[s("div",{staticClass:"gutter-box"},[a._v(a._s(a.drTodayInfo.otpTendOfYesterday))])])],1),s("ta-row",{attrs:{gutter:[16,30]}},[s("ta-col",{staticClass:"gutter-row",attrs:{span:2}},[s("div",{staticClass:"gutter-box"})]),s("ta-col",{staticClass:"gutter-row",attrs:{span:4}},[s("div",{staticClass:"gutter-box"},[a._v("门特处方")])]),s("ta-col",{staticClass:"gutter-row",attrs:{span:2}},[s("div",{staticClass:"gutter-box"},[a._v(a._s(a.drTodayInfo.opspTodayNum))])]),s("ta-col",{staticClass:"gutter-row",attrs:{span:3}},[s("div",{staticClass:"gutter-box"},[a._v("提醒")])]),s("ta-col",{staticClass:"gutter-row",attrs:{span:2}},[s("div",{staticClass:"gutter-box"},[a._v(a._s(a.drTodayInfo.opspAwakeTodayNum))])]),s("ta-col",{staticClass:"gutter-row",attrs:{span:3}},[s("div",{staticClass:"gutter-box"},[a._v("占比")])]),s("ta-col",{staticClass:"gutter-row",attrs:{span:2}},[s("div",{staticClass:"gutter-box"},[a._v(a._s(a.drTodayInfo.opspAwakePercen))])]),s("ta-col",{staticClass:"gutter-row",attrs:{span:3}},[s("div",{staticClass:"gutter-box"},[a._v("较昨日")])]),s("ta-col",{staticClass:"gutter-row",attrs:{span:2}},[s("div",{staticClass:"gutter-box"},[a._v(a._s(a.drTodayInfo.opspTendOfYesterday))])])],1),s("ta-row",{attrs:{gutter:[16,30]}},[s("ta-col",{staticClass:"gutter-row",attrs:{span:2}},[s("div",{staticClass:"gutter-box"})]),s("ta-col",{staticClass:"gutter-row",attrs:{span:4}},[s("div",{staticClass:"gutter-box"},[a._v("住院开单")])]),s("ta-col",{staticClass:"gutter-row",attrs:{span:2}},[s("div",{staticClass:"gutter-box"},[a._v(a._s(a.drTodayInfo.drordTodayNum))])]),s("ta-col",{staticClass:"gutter-row",attrs:{span:3}},[s("div",{staticClass:"gutter-box"},[a._v("提醒")])]),s("ta-col",{staticClass:"gutter-row",attrs:{span:2}},[s("div",{staticClass:"gutter-box"},[a._v(a._s(a.drTodayInfo.drordAwakeTodayNum))])]),s("ta-col",{staticClass:"gutter-row",attrs:{span:3}},[s("div",{staticClass:"gutter-box"},[a._v("占比")])]),s("ta-col",{staticClass:"gutter-row",attrs:{span:2}},[s("div",{staticClass:"gutter-box"},[a._v(a._s(a.drTodayInfo.drordAwakePercen))])]),s("ta-col",{staticClass:"gutter-row",attrs:{span:3}},[s("div",{staticClass:"gutter-box"},[a._v("较昨日")])]),s("ta-col",{staticClass:"gutter-row",attrs:{span:2}},[s("div",{staticClass:"gutter-box"},[a._v(a._s(a.drTodayInfo.drordTendOfYesterday))])])],1)],1)])]),s("div",{directives:[{name:"show",rawName:"v-show",value:a.nightAuditTop5Show,expression:"nightAuditTop5Show"}],attrs:{slot:"right"},slot:"right"},[s("ta-title",{attrs:{title:"每晚预审top5"}}),s("div",{staticClass:"top5"},[s("ta-big-table",{staticStyle:{"font-size":"17px"},attrs:{"highlight-hover-row":"",size:"small",data:a.tableData}},[s("ta-big-table-column",{attrs:{field:"ake002",title:"项目top5",width:"220px"}}),s("ta-big-table-column",{attrs:{field:"aaa167",title:"违反规则top5",width:"300px"}})],1)],1)],1),s("div",{directives:[{name:"show",rawName:"v-show",value:a.appealShow,expression:"appealShow"}],attrs:{slot:"right"},slot:"right"},[s("ta-title",{attrs:{title:"申诉管理"}},[s("span",{staticStyle:{display:"inline-block",float:"right"}},[s("div",{staticClass:"currentTime"},[a._v("期号 : "+a._s(a.currentDN))]),s("a",{staticClass:"more",on:{click:function(t){return a.handleAppealMoreTop5()}}},[a._v("查看明细>>")])])]),s("div",{staticClass:"top5"},[s("ta-big-table",{attrs:{border:"","highlight-hover-row":"",data:a.appealTableData}},[s("ta-big-table-column",{attrs:{field:"ake002",title:"项目top5"}}),s("ta-big-table-column",{attrs:{field:"aaa167",title:"违反规则top5"}})],1)],1)],1)])],1),s("div",{staticStyle:{height:"calc(100% - 390px)","margin-top":"-20px"}},[s("ta-border-layout",{attrs:{layout:{header:"0px",footer:"0px",left:"30%",right:"0px"}}},[s("div",[s("ta-title",{attrs:{title:"医嘱提醒"}}),s("div",{staticStyle:{float:"right"}},[s("ta-form",{attrs:{"auto-form-create":function(a){return t.queryParamForm=a}}},[s("ta-form-item",{attrs:{"field-decorator-id":"rangePicker","init-value":a.rangeValue}},[s("ta-range-picker",{on:{change:a.onChange}})],1)],1)],1),s("div",{staticStyle:{height:"60px"}}),s("div",[[s("div",{staticClass:"fit"},[s("div",[s("div",{staticClass:"gutter-example"},[s("ta-row",{attrs:{gutter:[0,0]}},[s("ta-col",{staticClass:"gutter-row",attrs:{span:1}}),s("ta-col",{staticClass:"gutter-row",attrs:{span:12}},[s("div",[s("ta-radio-group",{attrs:{value:a.checked},on:{change:a.handleChange}},[s("ta-radio-button",{attrs:{value:"1"}},[a._v("提醒次数")]),s("ta-radio-button",{attrs:{value:"2"}},[a._v("提醒金额")])],1)],1),s("div",{attrs:{id:"lineChart"}})]),s("ta-col",{staticClass:"gutter-row",attrs:{span:7}},[s("div",{attrs:{id:"alertProjectTop5"}},[s("div",[s("span",{staticStyle:{display:"inline-block",float:"contour"}},[s("div",{staticStyle:{display:"flex"}},[s("div",{staticClass:"circle"}),s("div",{staticClass:"alertProject"},[a._v("提醒项目top5")])])])]),s("ta-form",{attrs:{autoFormCreate:function(a){t.form=a}}},[s("ta-form-item",{attrs:{fieldDecoratorId:"project1",wrapperCol:{span:17,offset:0}}},[s("ta-input",{attrs:{placeholder:""}})],1),s("ta-form-item",{attrs:{fieldDecoratorId:"project2",wrapperCol:{span:17,offset:30}}},[s("ta-input",{attrs:{placeholder:""}})],1),s("ta-form-item",{attrs:{fieldDecoratorId:"project3",wrapperCol:{span:17,offset:30}}},[s("ta-input",{attrs:{placeholder:""}})],1),s("ta-form-item",{attrs:{fieldDecoratorId:"project4",wrapperCol:{span:17,offset:30}}},[s("ta-input",{attrs:{placeholder:""}})],1),s("ta-form-item",{attrs:{fieldDecoratorId:"project5",wrapperCol:{span:17,offset:30}}},[s("ta-input",{attrs:{placeholder:""}})],1)],1)],1)])],1)],1)])])]],2)],1),s("div",{attrs:{slot:"left"},slot:"left"},[s("ta-title",{attrs:{title:"代办事项"}}),s("div",[s("span",{staticStyle:{display:"inline-block",float:"left"}},[s("div",{staticStyle:{display:"flex"}},[s("div",{staticClass:"circle"}),s("div",{staticClass:"alertProject"},[a._v("每晚预审"),s("ta-icon",{attrs:{type:"check-circle",theme:"twoTone",twoToneColor:"#52c41a"}})],1)])])]),s("div",{staticStyle:{height:"30px"}}),s("div",{attrs:{id:"nightProcessTable"}},[s("ta-big-table",{attrs:{"highlight-hover-row":"",height:"120",data:a.processData}},[s("ta-big-table-column",{attrs:{field:"toHandle",width:"100",title:"待处理"}}),s("ta-big-table-column",{attrs:{field:"haveHandled",width:"100",title:"已处理"}}),s("ta-big-table-column",{attrs:{field:"processPer",width:"250",title:"完成进度"},scopedSlots:a._u([{key:"default",fn:function(t){return[s("div",{staticStyle:{width:"170px"}},[s("ta-progress",{attrs:{percent:100*t.row.processPer,size:"small"}})],1)]}}])})],1)],1),s("div",{directives:[{name:"show",rawName:"v-show",value:a.appealShow,expression:"appealShow"}]},[s("div",{staticStyle:{height:"30px"}}),s("div",[s("span",{staticStyle:{display:"inline-block",float:"left"}},[s("div",{staticStyle:{display:"flex"}},[s("div",{staticClass:"circle"}),s("div",{staticClass:"alertProject"},[a._v("申诉管理")])])])]),s("div",{staticStyle:{height:"30px"}}),s("div",{attrs:{id:"appealProcessTable"}},[s("ta-big-table",{attrs:{"highlight-hover-row":"",height:"120",data:a.appealProcess}},[s("ta-big-table-column",{attrs:{field:"needSubmitCount",width:"100",title:"待提交"}}),s("ta-big-table-column",{attrs:{field:"haveSubmitCount",width:"100",title:"已提交"}}),s("ta-big-table-column",{attrs:{field:"appealProgress",width:"250",title:"完成进度"},scopedSlots:a._u([{key:"default",fn:function(t){return[s("div",{staticStyle:{width:"170px"}},[s("ta-progress",{attrs:{percent:100*t.row.appealProgress,size:"small"}})],1)]}}])})],1)],1)])],1)])],1)])},r=[],i=e(88412),o=e(1708),l=e(36797),n=e.n(l),c={name:"drWorkBenck.vue",components:{TaTitle:i.Z},data:function(){return{versionControl:"",currentDay:(new Date).getFullYear()+"-"+(new Date).getMonth()+"-"+(new Date).getDay(),currentDN:(new Date).getFullYear()+"-"+(new Date).getMonth(),checked:"1",alertTimesAndMoneyData:[],time:[],rangeValue:[n()(n()().subtract(7,"days").format("YYYY-MM-DD"),"YYYY-MM-DD"),n()((new Date).toISOString().slice(0,10),"YYYY-MM-DD")],isDisabled:"",dateFormat:"YYYY/MM/DD",loginId:"",drName:"",drTodayInfo:"",option:{tooltip:{trigger:"item"},legend:{orient:"vertical",left:330,top:"40%"},series:[{name:"",type:"pie",radius:"50%",label:{formatter:" {d}% "},data:[],emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}},itemStyle:{normal:{color:function(t){var a=["#fc8251","#5470c6","#91cd77","#ef6567","#f9c956","#75bedc"];return a[t.dataIndex]}}}}]},lineChartOption:{tooltip:{trigger:"axis"},legend:{data:["Email","Union Ads","Video Ads","Direct","Search Engine"]},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},toolbox:{feature:{saveAsImage:{}}},xAxis:{type:"category",boundaryGap:!1,data:[]},yAxis:{type:"value"},series:[{name:"Email",type:"line",stack:"Total",data:[]},{name:"Union Ads",type:"line",stack:"Total",data:[]},{name:"Video Ads",type:"line",stack:"Total",data:[]}]},pieData:[],ake002:"",tableData:[],appealTableData:[],processData:[],appealProcess:[],appealShow:"",nightAuditTop5Show:"",inputObj:{}}},mounted:function(){this.queryDrName(),this.queryVersionControl(),this.queryAlertTimesAndMoney(),this.queryAlertProjectTop5(),this.queryDrTodayInfo(),this.pieChart(),this.queryNightProcess()},methods:{queryAppealProcess:function(){var t=this;Base.submit(null,{url:"drWorkBench/queryAppealProcess",data:{loginId:this.loginId}}).then((function(a){t.appealProcess=a.data.result}))},handleNightAuditMore:function(){window.open("approvalHandle.html#/approvalHandle?loginId=${this.loginId}","每晚预审","height=750, width=1300, top=200, left=400, toolbar=no, menubar=no, scrollbars=no, resizable=no, location=no, status=no")},handleAppealMoreTop5:function(){window.open("appealForDoc.html#/appealForDoc?loginId=".concat(this.loginId),"医生申诉处理","height=750, width=1300, top=200, left=400, toolbar=no, menubar=no, scrollbars=no, resizable=no, location=no, status=no")},queryVersionControl:function(){var t=this;Base.submit(null,{url:"drWorkBench/queryVersionControl",data:{}}).then((function(a){t.versionControl=a.data.result,t.$nextTick((function(){1==t.versionControl?(t.appealShow=!1,t.nightAuditTop5Show=!0,t.queryNightBreakRuleTop5()):(t.appealShow=!0,t.queryAppealProcess(),t.nightAuditTop5Show=!1,t.queryAppealBreakRuleTop5())}))}))},handleChange:function(t){this.checked=t.target.value,this.queryAlertTimesAndMoney()},queryAlertTimesAndMoney:function(){var t=this;this.myLineChart=o.init(document.getElementById("lineChart")),this.myLineChart.setOption(this.lineChartOption),this.$nextTick((function(){var a=t.queryParamForm.getFieldsValue();t.time[0]=a.rangePicker[0].format("YYYY-MM-DD"),t.time[1]=a.rangePicker[1].format("YYYY-MM-DD"),Base.submit(null,{url:"drWorkBench/queryAlertTimesAndMoney",data:{time:t.time,loginId:t.loginId}}).then((function(a){t.alertTimesAndMoneyData=a.data.result,t.$nextTick((function(){var a=[],e=[],s=[],r=[];t.alertTimesAndMoneyData[0].forEach((function(t){a.push(t)})),1==t.checked?t.alertTimesAndMoneyData[1].forEach((function(t){e.push(t.name),s.push(t.name),r.push(t.data)})):2==t.checked&&t.alertTimesAndMoneyData[2].forEach((function(t){e.push(t.name),s.push(t.name),r.push(t.data)})),t.$nextTick((function(){var i={legend:{data:e},xAxis:{type:"category",boundaryGap:!1,data:a},yAxis:{type:"value"},series:[{name:s[0],type:"line",stack:"Total",data:r[0]},{name:s[1],type:"line",stack:"Total",data:r[1]},{name:s[2],type:"line",stack:"Total",data:r[2]}]};t.myLineChart.setOption(i)}))}))}))}))},moment:n(),onChange:function(t,a){this.queryAlertTimesAndMoney(),this.queryAlertProjectTop5()},queryDrName:function(){this.drName=localStorage.getItem("username"),this.aaz263=localStorage.getItem("aaz263"),this.loginId=localStorage.getItem("aaz263")},queryDrTodayInfo:function(){var t=this;Base.submit(null,{url:"drWorkBench/queryDrTodayInfo",data:{loginId:this.loginId}}).then((function(a){t.drTodayInfo=a.data.result}))},pieChart:function(){var t=this;this.myChart=o.init(document.getElementById("main")),this.myChart.setOption(this.option),Base.submit(null,{url:"drWorkBench/queryEveryNightAudit",data:{loginId:this.loginId}}).then((function(a){var e={series:[{data:a.data.result}]};t.myChart.setOption(e)}))},queryNightBreakRuleTop5:function(){var t=this;Base.submit(null,{url:"drWorkBench/queryNightBreakRuleTop5",data:{loginId:this.loginId}}).then((function(a){t.tableData=a.data.result}))},queryAppealBreakRuleTop5:function(){var t=this;Base.submit(null,{url:"drWorkBench/queryAppealBreakRuleTop5",data:{loginId:this.loginId}}).then((function(a){t.appealTableData=a.data.result}))},queryNightProcess:function(){var t=this;Base.submit(null,{url:"drWorkBench/queryNightProcess",data:{loginId:this.loginId}}).then((function(a){t.processData=a.data.result}))},queryAlertProjectTop5:function(){var t=this;this.$nextTick((function(){Base.submit(null,{url:"drWorkBench/queryAlertProjectTop5",data:{time:t.time,loginId:t.loginId}}).then((function(a){t.alertProjectTop5=a.data.result,t.$nextTick((function(){var a=t;if(null!=a.alertProjectTop5&&""!=a.alertProjectTop5)for(var e,s=a.alertProjectTop5.length,r=0;r<s;r++)e=("project"+(r+1)).toString(),a.inputObj[e]=new Map(Object.entries(t.alertProjectTop5[r])).get("type")+": "+new Map(Object.entries(t.alertProjectTop5[r])).get("ake002")+"  "+new Map(Object.entries(t.alertProjectTop5[r])).get("alertNum")+"次",a.form.setFieldsValue(a.inputObj);else t.form.setFieldsValue({project1:""}),t.form.setFieldsValue({project2:""}),t.form.setFieldsValue({project3:""}),t.form.setFieldsValue({project4:""}),t.form.setFieldsValue({project5:""})}))}))}))}}},d=c,u=e(1001),p=(0,u.Z)(d,s,r,!1,null,"69230838",null),h=p.exports},36766:function(t,a,e){var s=e(66586);a["Z"]=s.Z},26263:function(t,a,e){e.d(a,{s:function(){return s},x:function(){return r}});var s=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("h3",{staticStyle:{"font-weight":"normal"}},[e("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),e("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},r=[]},66586:function(t,a){var e={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};a["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:e}}}}}]);
(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[4526],{97010:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return L}});var i=function(){var e=this,t=this,a=t.$createElement,i=t._self._c||a;return i("div",{staticClass:"fit"},[i("ta-border-layout",{staticStyle:{},attrs:{"layout-type":"fixTop"}},[i("div",{attrs:{slot:"header"},slot:"header"},[i("div",{staticClass:"query-header"},[i("div",{ref:"formBox",staticClass:"query-header-left",style:t.formBoxStyle},[i("ta-form",{attrs:{autoFormCreate:function(t){e.baseInfoForm=t},col:t.col,layout:"horizontal","label-width":"80px",formLayout:!0}},[i("ta-form-item",{attrs:{"field-decorator-id":"dateFlag","init-value":t.onRadioValue}},[i("span",{attrs:{slot:"label"},slot:"label"},[t._v(" 时间类型 ")]),i("ta-radio-group",{staticStyle:{width:"100%"},on:{change:t.onRadioChange},model:{value:t.onRadioValue,callback:function(e){t.onRadioValue=e},expression:"onRadioValue"}},[i("ta-radio-button",{staticStyle:{width:"50%","text-align":"center"},attrs:{value:"audit"}},[t._v("审核时间 ")]),i("ta-radio-button",{staticStyle:{width:"50%","text-align":"center"},attrs:{value:"itemStart"}},[t._v("项目时间 ")])],1)],1),i("ta-form-item",{attrs:{"field-decorator-id":"dateSet","label-width":"120px","init-value":t.rangeValue}},[i("span",{attrs:{slot:"label"},slot:"label"},[t._v(" "+t._s(t.onRadioLable)+" ")]),i("ta-range-picker",{attrs:{"allow-one":!0}},[i("ta-icon",{attrs:{slot:"suffixIcon",type:""},slot:"suffixIcon"})],1)],1),i("ta-form-item",{attrs:{fieldDecoratorId:"aae500List",disabled:t.aae500ListFlag,label:"审核场景",initValue:t.defaultAae500}},[i("ta-select",{attrs:{placeholder:"请选择审核场景",maxTagCount:1},on:{change:t.fnQueryRuleList}},[i("ta-select-option",{attrs:{value:"2"}},[t._v("医生医嘱审核")]),i("ta-select-option",{attrs:{value:"3"}},[t._v("护士计费审核")]),i("ta-select-option",{attrs:{value:"1"}},[t._v("门诊处方审核")])],1)],1),i("ta-form-item",{attrs:{"field-decorator-id":"medinsCode",label:"院区标识",disabled:t.paramsDisable.akb020}},[i("ta-select",{attrs:{showSearch:"",placeholder:"院区标识筛选",allowClear:"",options:t.hosList},on:{change:t.fnQueryDept}})],1),i("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:this.menuConfig.patientInfo,expression:"this.menuConfig.patientInfo"}],attrs:{fieldDecoratorId:"patientInfo",label:"患者信息"}},[this.defaultPatientPlaceholder?i("ta-input",{attrs:{placeholder:"请输入住院号、就诊号或姓名"}}):i("ta-input",{attrs:{placeholder:"请输入登记号、就诊号或姓名"}})],1),i("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:this.menuConfig.aaz307,expression:"this.menuConfig.aaz307"}],attrs:{"field-decorator-id":"aaz307",label:"开单科室"}},[i("ta-select",{attrs:{showSearch:"","show-search":!0,allowClear:"",placeholder:"开单科室筛选",options:t.ksList},on:{change:t.fnQueryGroup}})],1),i("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:this.menuConfig.aaz266,expression:"this.menuConfig.aaz266"}],attrs:{"field-decorator-id":"aaz266",label:"执行科室"}},[i("ta-select",{attrs:{showSearch:"","show-search":!0,allowClear:"",placeholder:"执行科室筛选",options:t.ksList}})],1),i("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:this.menuConfig.aaz309,expression:"this.menuConfig.aaz309"}],attrs:{"field-decorator-id":"aaz309",label:"诊疗小组",disabled:t.paramsDisable.aaz309}},[i("ta-select",{attrs:{showSearch:"","show-search":!0,placeholder:"诊疗小组",filterOption:t.filterOption,options:t.groupList,"allow-clear":""},on:{change:t.fnQueryDocter}})],1),i("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:this.menuConfig.operate,expression:"this.menuConfig.operate"}],attrs:{"field-decorator-id":"operate",label:"医师操作",disabled:t.operateSelect}},[i("ta-select",{attrs:{mode:"multiple",maxTagCount:2,showSearch:"",placeholder:"医师操作筛选",allowClear:"",collectionType:"APE893"}})],1),i("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:this.menuConfig.aaz319,expression:"this.menuConfig.aaz319"}],attrs:{"field-decorator-id":"aaz319",label:"规则大类",disabled:t.aaz319Select}},[i("ta-select",{attrs:{showSearch:"",placeholder:"规则大类",allowClear:"",options:t.ruleList}})],1),i("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:this.menuConfig.ruleType,expression:"this.menuConfig.ruleType"}],attrs:{label:"规则分类",fieldDecoratorId:"ruleType"}},[i("ta-tree-select",{attrs:{allowClear:"",showSearch:"",placeholder:"规则分类",dropdownStyle:{maxHeight:"400px",overflow:"auto"},treeData:t.treeData,treeDataSimpleMode:t.treeData}})],1),i("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:this.menuConfig.aaz263,expression:"this.menuConfig.aaz263"}],attrs:{"field-decorator-id":"aaz263",label:"医师名称",disabled:t.paramsDisable.aaz263}},[i("ta-select",{attrs:{showSearch:"",optionLabelProp:"children",mode:"combobox",placeholder:"医生名称筛选",options:t.doctorList,filterOption:t.filterOption,allowClear:""}})],1),i("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:this.menuConfig.projectInfo,expression:"this.menuConfig.projectInfo"}],attrs:{fieldDecoratorId:"projectInfo",label:"医保项目"}},[i("ta-input",{attrs:{placeholder:"请输入医保项目名称或编码"}})],1),i("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:this.menuConfig.aae140,expression:"this.menuConfig.aae140"}],attrs:{fieldDecoratorId:"aae140",label:"险种类型"}},[i("ta-select",{attrs:{showSearch:"",placeholder:"险种类型筛选","collection-type":"AAE140","dropdown-match-select-width":!1,allowClear:""}})],1),i("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:this.menuConfig.ake003,expression:"this.menuConfig.ake003"}],attrs:{"field-decorator-id":"ake003",label:"三目类别"}},[i("ta-select",{attrs:{showSearch:"",placeholder:"三目类别筛选","collection-type":"AKE003","dropdown-match-select-width":!1,"allow-clear":""}})],1),i("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:this.menuConfig.inProjectInfo,expression:"this.menuConfig.inProjectInfo"}],attrs:{fieldDecoratorId:"inProjectInfo",label:"院内项目"}},[i("ta-input",{attrs:{placeholder:"请输入院内项目名称或编码"}})],1),i("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:this.menuConfig.result,expression:"this.menuConfig.result"}],staticClass:"result-form",attrs:{fieldDecoratorId:"result",label:"预审结果","init-value":["1","2","3"]}},[i("ta-select",{attrs:{showSearch:"",mode:"multiple",placeholder:"请选择",allowClear:""},on:{select:t.handleSelect,change:t.handleChange}},[i("ta-select-option",{attrs:{value:"0",disabled:t.resultDisabledOne}},[t._v("审核通过 ")]),i("ta-select-option",{attrs:{value:"1",disabled:t.resultDisabledTwo}},[t._v("可疑")]),i("ta-select-option",{attrs:{value:"2",disabled:t.resultDisabledTwo}},[t._v("违规")]),i("ta-select-option",{attrs:{value:"3",disabled:t.resultDisabledTwo}},[t._v("仅提醒")]),i("ta-select-option",{attrs:{value:"4",disabled:t.resultDisabledThree}},[t._v("无规则 ")])],1)],1)],1)],1),i("div",{staticStyle:{display:"flex","margin-left":"10px",float:"right"}},[i("div",{staticClass:"ctrl-btn",on:{click:t.formShowAllChange}},[i("a",[t._v(t._s(t.formShowAll?"收起":"展开"))]),t.formShowAll?i("ta-icon",{attrs:{type:"caret-up"}}):i("ta-icon",{attrs:{type:"caret-down"}})],1),i("div",{staticStyle:{"margin-right":"10px"}},[i("ta-icon",{staticStyle:{color:"#3382f5","font-size":"16px","margin-top":"8px",cursor:"pointer",float:"left"},attrs:{type:"setting"},on:{click:t.configMenu}})],1),i("ta-button",{attrs:{type:"primary",icon:"search"},on:{click:t.fnQuery}},[t._v("查询")]),i("ta-button",{attrs:{type:"default",icon:"redo"},on:{click:t.fnReset}},[t._v("重置 ")])],1)])]),i("div",{staticClass:"fit content-box"},[i("ta-title",{attrs:{title:"查询结果"}},[t.showDispatchBtn?i("ta-button",{staticStyle:{"margin-right":"10px",width:"90px",float:"right"},attrs:{type:"primary"},on:{click:t.fnPreBatchDispatch}},[t._v("批量下发")]):t._e()],1),i("div",{staticClass:"content-table"},[i("ta-big-table",{ref:"infoTableRef",attrs:{columns:t.infoColumns,data:t.infoTableData,"export-config":{},"import-config":{},"control-column":t.showHiddenOrSortColumn,border:"","empty-text":"-",height:"auto","highlight-hover-row":"","highlight-current-row":"","auto-resize":"","show-overflow":"",resizable:""},scopedSlots:t._u([{key:"ape800",fn:function(e){var a=e.row;return[t.rulelessShow?i("span",[t._v("——")]):"0"==a.ape800?i("span",{staticStyle:{color:"#0f990f"}},[i("ta-icon",{attrs:{type:"check-circle",theme:"twoTone",twoToneColor:"#52c41a"}}),t._v("审核通过")],1):"1"==a.ape800?i("span",{staticStyle:{color:"#F59A23"}},[i("ta-icon",{attrs:{type:"question-circle",theme:"twoTone",twoToneColor:"#F59A23"}}),t._v("可疑提醒")],1):"2"==a.ape800?i("span",{staticStyle:{color:"#FF0000"}},[i("ta-icon",{attrs:{type:"exclamation-circle",theme:"twoTone",twoToneColor:"#FF0000"}}),t._v("违规提醒")],1):"3"==a.ape800?i("span",{staticStyle:{color:"#ffc49a"}},[i("ta-icon",{attrs:{type:"bell",theme:"twoTone",twoToneColor:"#e1ba91"}}),t._v("仅提醒")],1):t._e()]}},{key:"ake002",fn:function(e){var a=e.row;return["2"==a.ape800?i("span",{staticStyle:{color:"#FF0000"}},[t._v(t._s(a.ake002))]):i("span",[t._v(t._s(a.ake002))])]}},{key:"akb065",fn:function(e){var a=e.row;return["2"==a.ape800?i("span",{staticStyle:{color:"#FF0000"}},[t._v(t._s(a.akb065))]):i("span",[t._v(t._s(a.akb065))])]}},{key:"ape896",fn:function(e){var a=e.row;return[null==a.ape896||""==a.ape896?i("span",[t._v("—")]):i("span",{domProps:{innerHTML:t._s(t.CollectionLabel("APE896",a.ape896))}})]}},{key:"operate",fn:function(e){var a=e.row;return[a.ape893?a.ape893.includes("补充诊断")?i("span",{staticStyle:{color:"#E6A23C"}},[t._v(t._s(a.aaz560?a.aaz560:a.ape893))]):a.ape893.includes("备案")?i("span",{staticStyle:{color:"#5DAF34"},domProps:{innerHTML:t._s(a.ape893)}}):a.ape893.includes("自费")?i("span",{staticStyle:{color:"#CF9236"},domProps:{innerHTML:t._s(a.ape893)}}):a.ape893.includes("无操作")?i("span",{staticStyle:{color:"#909399"},domProps:{innerHTML:t._s(a.ape893)}}):a.ape893.includes("取消")?i("span",{staticStyle:{color:"#67C23A"},domProps:{innerHTML:t._s(a.ape893)}}):i("span",{domProps:{innerHTML:t._s(a.ape893)}}):i("span",[t._v("--")])]}},{key:"ykz126",fn:function(e){var a=e.row;return["5"==a.ykz126?i("span",[t._v("中心端")]):i("span",[t._v("医院端")])]}},{key:"operate2",fn:function(e){var a=e.row;return[i("div",{directives:[{name:"show",rawName:"v-show",value:!t.rulelessShow,expression:"!rulelessShow"}],staticClass:"opareteItem",on:{click:function(e){return t.openAuditDetails(a)}}},[t._v(" 审核详情 ")])]}},{key:"ape804",fn:function(e){var a=e.row;return[null==a.ape805||""==a.ape805||null==a.akc225||""==a.akc225?i("span",[t._v("—")]):i("span",[t._v(" "+t._s(a.ape805*a.akc225)+" ")])]}},{key:"aaz267",fn:function(e){var a=e.row;return[null==a.aaz267||""==a.aaz267?i("span",{staticStyle:{color:"#FF0000"}},[t._v("-")]):i("span",[t._v(t._s(a.aaz267))])]}}])})],1),i("div",{staticClass:"content-box-footer"},[i("ta-pagination",{ref:"infoPageRef",staticStyle:{position:"absolute",right:"6px",bottom:"6px","margin-right":"100px"},attrs:{dataSource:t.infoTableData,params:t.infoPageParams,defaultPageSize:200,pageSizeOptions:["30","50","100","200","500"],url:"issueInventory/queryAuditResults"},on:{"update:dataSource":function(e){t.infoTableData=e},"update:data-source":function(e){t.infoTableData=e}}}),i("ta-button-group",{staticStyle:{position:"absolute",right:"6px",bottom:"6px"}},[i("ta-button",{attrs:{type:"primary",icon:"download"},on:{click:t.exportExcel}},[t._v("导出")])],1)],1)],1),i("div",{attrs:{id:"info"}},[i("ta-modal",{staticStyle:{top:"10px"},attrs:{width:"95%",bodyStyle:{paddingBottom:"1px"},"destroy-on-close":!0,footer:null,wrapClassName:"inp-modal-wrap"},on:{cancel:t.handleCancel},model:{value:t.visible,callback:function(e){t.visible=e},expression:"visible"}},[i("ta-icon",{staticStyle:{color:"black"},attrs:{slot:"closeIcon",type:"close"},slot:"closeIcon"}),i("span",{staticStyle:{"font-weight":"normal"},attrs:{slot:"title"},slot:"title"},[t._v(" 审核详情")]),i("atient-details",{attrs:{fyRecord:t.bfRecord}})],1)],1),i("div",[i("ta-modal",{attrs:{title:"配置菜单项",height:"200px",width:"350px"},on:{ok:t.handleConfigOk,cancel:t.handleConfigCancel},model:{value:t.configVisible,callback:function(e){t.configVisible=e},expression:"configVisible"}},[i("span",[t._v("选择需要显示的菜单项:")]),i("br"),i("ta-checkbox-group",{attrs:{value:t.checkedList},on:{change:t.onConfigChange}},[i("ta-row",t._l(t.configList,(function(e,a){return i("ta-col",{key:a,attrs:{span:8}},[i("ta-checkbox",{attrs:{value:e.value}},[t._v(t._s(e.label))])],1)})),1)],1),i("div",[i("ta-checkbox",{attrs:{indeterminate:t.indeterminate,checked:t.checkAll},on:{change:t.onCheckAllChange}},[t._v(" 全部选中 ")])],1)],1)],1),i("div",[i("ta-modal",{attrs:{title:"批量下发",height:"150px",width:"300px"},on:{ok:t.fnBatchDispatch},model:{value:t.batchDispatchVisible,callback:function(e){t.batchDispatchVisible=e},expression:"batchDispatchVisible"}},[i("span",[t._v("下发对象选择:")]),i("br"),i("br"),i("ta-radio-group",{staticStyle:{"margin-left":"50px"},model:{value:t.defaultDeptType,callback:function(e){t.defaultDeptType=e},expression:"defaultDeptType"}},[i("ta-radio",{attrs:{value:1}},[t._v("开单科室")]),i("ta-radio",{attrs:{value:2}},[t._v("执行科室")])],1)],1)],1)])],1)},l=[],o=a(66347),n=a(48534),s=a(95082),r=(a(36133),a(88412)),c=a(362),u=a(36797),f=a.n(u),d=a(22722);function h(e){return e?e.akb020&&e.aaz263?"personal":e.akb020&&e.aaz309&&e.aaz307?"group":e.akb020&&e.aaz307?"department":e.akb020?"hospital":"all":"all"}function p(e){return m.apply(this,arguments)}function m(){return m=(0,n.Z)(regeneratorRuntime.mark((function e(t){var a,i,l,o,n,s,r,c,u,f,d,p;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,Base.submit(null,{url:"/miimCommonRead/queryDataPermission",showPageLoading:!1,data:{jobnumber:t},autoValid:!0});case 2:return a=e.sent,i=new Set,l=new Set,a.data.permission.forEach((function(e){var t=h(e);"hospital"===t&&i.add(e.akb020),"department"===t&&l.add(e.aaz307)})),o=a.data.permission.filter((function(e){return"department"===h(e)||!l.has(e.aaz307)})).filter((function(e){return"hospital"===h(e)||!i.has(e.akb020)})),n=new Set(o.map((function(e){return e.akb020})).filter((function(e){return null!=e}))),s=new Set(o.map((function(e){return e.aaz307})).filter((function(e){return null!=e}))),r=new Set(o.map((function(e){return e.aaz309})).filter((function(e){return null!=e}))),c=new Set(o.map((function(e){return e.aaz263})).filter((function(e){return null!=e}))),u=!1,f=!1,d=!1,p=!1,1===n.size&&(u=!0),1===s.size&&1===n.size&&(f=!0),1===s.size&&1===n.size&&1===r.size&&(d=!0),1===n.size&&0===s.size&&1===c.size&&(p=!0),e.abrupt("return",{akb020Set:n,aaz307Set:s,aaz263Set:c,aaz309Set:r,akb020Disable:u,aaz307Disable:f,aaz263Disable:p,aaz309Disable:d});case 20:case"end":return e.stop()}}),e)}))),m.apply(this,arguments)}function b(e,t){Base.submit(null,{url:"miimCommonRead/getAa01AAE500StartStop",data:e},{successCallback:function(e){return t(e)}})}function g(e,t){Base.submit(null,{url:"miimCommonRead/insertTableColumShow",data:e,autoValid:!1},{successCallback:function(e){return t("success")},failCallback:function(e){return t("fail")}})}var v={zbx01:{footer:function(){return"统筹金总额"}},zbx02:{footer:function(){return"基金支出总额"}},zbx03:{footer:function(){return"总费用"}},zbx04:{footer:function(){return"总住院日"}}},y={permissionCheck:p,getAa01AAE500StartStop:b,insertTableColumShow:g,props:v,moneyNumFormat:function(e){var t=e.cellValue;return t||"—"}},w=a(55115);w.w3.prototype.Base=Object.assign(w.w3.prototype.Base,(0,s.Z)({},d.Z));var k=function(e){var t=e.cellValue;return t||"—"},C=[],D=[],z={name:"issueInventory",components:{TaTitle:r.Z,atientDetails:c["default"]},data:function(){var e=[{label:"开单科室",value:"aaz307"},{label:"执行科室",value:"aaz266"},{label:"诊疗小组",value:"aaz309"},{label:"医师操作",value:"operate"},{label:"规则大类",value:"aaz319"},{label:"规则分类",value:"ruleType"},{label:"医师名称",value:"aaz263"},{label:"患者信息",value:"patientInfo"},{label:"医保项目",value:"projectInfo"},{label:"险种类型",value:"aae140"},{label:"预审结果",value:"result"},{label:"三目类别",value:"ake003"},{label:"院内项目",value:"inProjectInfo"}],t=[{type:"checkbox",field:"checkbox",minwidth:"60",fixed:"left",align:"center"},{type:"seq",title:"序号",width:"60",align:"center"},{title:"医院编码",field:"akb020",align:"left",width:80},{title:"医院名称",field:"akb021",align:"left",visible:!1},{title:"审核场景",field:"aae500",align:"left",width:80,collectionType:"AAE500"},{title:"开单科室",field:"aae386",align:"left",sortable:!0,width:100},{title:"执行科室",field:"aaz267",align:"left",sortable:!0,width:100,customRender:{default:"aaz267"}},{title:"开单医师",field:"aac003",align:"left",width:80},{title:"院内项目名称",field:"ake006",sortable:!0,align:"left",width:200},{title:"医保项目名称",field:"ake002",sortable:!0,align:"left",width:130,customRender:{default:"ake002"}},{title:"预审结果",field:"ape800",sortable:!0,align:"center",width:110,customRender:{default:"ape800"}},{title:"金额",field:"akb065",align:"right",width:80,customRender:{default:"akb065"}},{title:"项目引导信息",field:"ykz018",align:"left",width:300},{title:"医师操作",field:"ape893",align:"left",width:100,customRender:{default:"operate"}},{title:"本次就诊号",field:"akc190",align:"left",width:120},{title:"住院号",field:"akc191",sortable:!0,align:"left",width:120},{title:"患者姓名",field:"name",sortable:!0,align:"left",width:100},{title:"险种类型",field:"aae140",align:"left",sortable:!0,width:100,collectionType:"AAE140"},{title:"主诊断",field:"diag_main",align:"left",visible:!1},{title:"其他诊断",field:"diag_others",align:"left",visible:!1},{title:"医保编码",field:"ake001",align:"left",width:150},{title:"院内编码",field:"akc515",align:"left",width:150},{title:"规则大类",field:"aaa167",align:"left",sortable:!0,width:130},{title:"规则分类",field:"ruletype",align:"center",visible:!1,width:130},{title:"违规来源",field:"ykz126",align:"left",width:100,customRender:{default:"ykz126"}},{title:"项目发生时间",field:"aae036",align:"center",width:160,sortable:!0},{title:"审核时间",field:"aze002",align:"center",width:160,sortable:!0},{title:"入院科室",field:"aae376",align:"left",formatter:k,sortable:!0,width:100},{title:"出院科室",field:"aae386_kc21",formatter:k,align:"left",sortable:!0,width:100},{title:"入院时间",field:"aae030",align:"center",width:160},{title:"出院时间",field:"aae031",formatter:k,align:"center",width:160},{title:"院内审批标志",field:"ape896",align:"center",width:140,customRender:{default:"ape896"},filterMethod:this.filterApeMethod,filters:[],overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"单价",field:"akc225",align:"right",width:80},{title:"数量",field:"akc226",align:"right",width:80},{title:"违规数量",field:"ape805",align:"left",visible:!1,formatter:y.moneyNumFormat},{title:"违规金额",field:"ape804",align:"left",visible:!1,customRender:{default:"ape804"}},{title:"医疗费用总额",field:"akc264",align:"left",visible:!1},{title:"操作",field:"operate",align:"center",fixed:"right",width:150,customRender:{default:"operate2"}}];return{col:{xs:1,sm:2,md:2,lg:2,xl:4,xxl:4},rangeValue:[f()().subtract(1,"week"),f()()],hosList:[],ksList:[],groupList:[],onRadioValue:"audit",onRadioLable:"审核时间范围",ruleList:[],treeData:[],doctorList:[],ksTableData:C,infoColumns:t,infoTableData:D,akb020:"",aaz307:"",expandedRowKeys:[],permissions:null,paramsDisable:{akb020:!1,aaz307:!1,aaz263:!1},menuConfig:{aaz307:!0,aaz266:!0,aaz309:!0,operate:!0,aaz319:!0,ruleType:!0,aaz263:!0,patientInfo:!0,projectInfo:!0,aae140:!0,result:!0,ake003:!0,inProjectInfo:!0},visible:!1,configVisible:!1,indeterminate:!1,checkAll:!0,checkedList:[],bfcheckedList:[],configList:e,aae500ListFlag:!1,bfRecord:{},formShowAll:!1,resultDisabledOne:!1,resultDisabledTwo:!1,rulelessShow:!1,resultDisabledThree:!1,aaz319Select:!1,operateSelect:!1,filterList:"",batchDispatchVisible:!1,defaultDeptType:1,defaultAae500:"2",curUserJobNumber:"",curUserDeptCode:"",showDispatchBtn:!1,systemName:"",showHiddenOrSortColumn:{themeColor:"red",icon:"appstore",showHiddenConfig:{open:!0,disabledHiddenColumns:function(e){return!1}},columnSortConfig:{open:!0,onMove:function(e,t){t.dragColumn,t.dropColumn;return!0},dragEnd:function(e,t){t.dragColumn,t.resultColumns}},successCallback:this.fnSaveTableTitle,disabledControlCol:function(e){}},hideOrShoeColumns:["aae376","aae386_kc21","aae030","aae031"],defaultPatientPlaceholder:!0,aaz263:"",personSign:""}},computed:{formBoxStyle:function(){return this.formShowAll?{height:"auto"}:{height:"50px"}}},created:function(){var e=this;return(0,n.Z)(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$nextTick((function(){e.fnQueryTableTitle()}));case 2:case"end":return t.stop()}}),t)})))()},mounted:function(){var e=this;return(0,n.Z)(regeneratorRuntime.mark((function t(){var a,i,l,o,n,s;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!e.$route.query.params){t.next=11;break}a=JSON.parse(e.$route.query.params),a.allDate=a.allDate.map((function(t){var a=new Date(t);return a.setDate(a.getDate()+1),e.Base.getMoment(a.toISOString(),"YYYY-MM-DD")})),e.$route.query.aaz307&&(a.aaz307=e.$route.query.aaz307,e.aaz307=e.$route.query.aaz307),e.$route.query.aaz263&&(a.aaz263=e.$route.query.aaz263,e.aaz263=e.$route.query.aaz263),e.$route.query.ake002&&(a.projectInfo=e.$route.query.ake002),e.$route.query.name&&(a.patientInfo=e.$route.query.name),a.aae500List=a.aae500,e.baseInfoForm.setFieldsValue(a),t.next=20;break;case 11:return t.next=13,Base.submit(null,{url:"miimCommonRead/initBaseInfo",data:{},autoQs:!1});case 13:i=t.sent,l={},i.data.data.personSign&&(e.personSign=i.data.data.personSign),i.data.data.orgLabelList&&i.data.data.orgLabelList.length>0&&"3"!==e.personSign&&(l.aaz307=i.data.data.orgLabelList[0].value,e.aaz307=i.data.data.orgLabelList[0].value),i.data.data.aaz263&&"3"!==e.personSign&&(l.aaz263=i.data.data.aaz263,e.aaz263=i.data.data.aaz263),i.data.data.systemName&&(e.systemName=i.data.data.systemName),e.baseInfoForm.setFieldsValue(l);case 20:return t.next=22,e.Base.asyncGetCodeData("APE896");case 22:return o=t.sent,e.$refs.infoTableRef.setFilter(e.$refs.infoTableRef.getColumnByField("ape896"),o),e.$refs.infoTableRef.updateData(),t.next=27,y.permissionCheck(e.aaz263);case 27:e.permissions=t.sent,n=["5","6","7","4","3","18"],y.getAa01AAE500StartStop({aaz499s:JSON.stringify(n)},(function(t){var a=t.data.aae500List;a.includes(12)&&a.push(13),e.filterList=a.join(",")})),s=navigator.userAgent,s.indexOf("DeepBlue")>-1?(e.isDeepBlue=!0,e.curUserJobNumber=e.aaz263,e.curUserDeptCode=e.aaz307,"1"==e.systemName?(e.defaultAae500="2",e.systemName="1"):"2"==e.systemName&&(e.defaultAae500="3",e.systemName="2"),"3"!=e.personSign?(e.showDispatchBtn=!1,e.$refs.infoTableRef.hideColumn(e.$refs.infoTableRef.getColumnByField("checkbox"))):e.showDispatchBtn=!0,QClient.DBPNSGetClientId((function(t){e.clientId=t.data}))):"3"!=e.personSign?(e.curUserJobNumber=e.aaz263,e.curUserDeptCode=e.aaz307,"1"==e.systemName?e.defaultAae500="2":"2"==e.systemName&&(e.defaultAae500="3"),e.showDispatchBtn=!1,e.$refs.infoTableRef.hideColumn(e.$refs.infoTableRef.getColumnByField("checkbox"))):e.permissions.aaz307Disable?(e.showDispatchBtn=!1,e.$refs.infoTableRef.hideColumn(e.$refs.infoTableRef.getColumnByField("checkbox"))):e.showDispatchBtn=!0,e.fnQueryHos(),e.fnQueryRuleList(e.defaultAae500),e.getTreeData();case 35:case"end":return t.stop()}}),t)})))()},methods:{handleConfigOk:function(e){var t=this;this.configVisible=!1,this.bfcheckedList=this.checkedList,this.configList.forEach((function(e){t.menuConfig[e.value]=t.checkedList.includes(e.value)})),this.fnSaveTableTitle()},handleConfigCancel:function(){this.visible=!1,this.checkedList=this.bfcheckedList,this.bfcheckedList.length<this.configList.length&&0!==this.bfcheckedList.length?(this.checkAll=!1,this.indeterminate=!0):0===this.bfcheckedList.length?(this.checkAll=!1,this.indeterminate=!1):(this.checkAll=!0,this.indeterminate=!1)},onConfigChange:function(e){e.length<this.configList.length&&0!==e.length?(this.checkAll=!1,this.indeterminate=!0):0===e.length?(this.checkAll=!1,this.indeterminate=!1):(this.checkAll=!0,this.indeterminate=!1),this.checkedList=e},onCheckAllChange:function(e){var t=e.target.checked;t?(this.checkAll=!0,this.checkedList=this.configList.map((function(e){return e.value}))):(this.checkAll=!1,this.checkedList=[]),this.indeterminate=!1},configMenu:function(){this.configVisible=!0,this.bfcheckedList===this.checkedList?this.bfcheckedList=this.checkedList:this.checkedList=this.bfcheckedList,this.onConfigChange(this.checkedList)},onRadioChange:function(e){this.onRadioValue=e.target.value,this.onRadioLable="audit"==e.target.value?"审核时间范围":"项目发生时间"},handleChange:function(e){0===e.length?(this.resultDisabledTwo=!1,this.resultDisabledThree=!1,this.resultDisabledOne=!1,this.operateSelect=!1,this.aaz319Select=!1):e.includes("0")?(this.resultDisabledTwo=!0,this.resultDisabledThree=!0,this.resultDisabledOne=!1):e.includes("4")&&(this.resultDisabledTwo=!0,this.resultDisabledThree=!1,this.resultDisabledOne=!0)},handleSelect:function(e){"0"===e?this.baseInfoForm.setFieldsValue({result:["0"]}):"4"===e&&(this.baseInfoForm.setFieldsValue({result:["4"]}),this.baseInfoForm.resetFields(["operate","aaz319"]),this.operateSelect=!0,this.aaz319Select=!0)},filterApeMethod:function(e){var t=e.value,a=(e.option,e.row);e.column;return a.ape896==t},fnReset:function(){var e=this;this.baseInfoForm.resetFields(),this.defaultPatientPlaceholder=!0;for(var t=this.$refs.infoTableRef.getTableColumn().fullColumn,a=0;a<t.length;a++)"akc191"===t[a].property&&(t[a].title="住院号");this.hideOrShoeColumns.forEach((function(t){e.$refs.infoTableRef.showColumn(e.$refs.infoTableRef.getColumnByField(t))})),this.baseInfoForm.setFieldsValue({aae500List:"2"}),this.formShowAll=!0},formShowAllChange:function(){this.formShowAll=!this.formShowAll},openAuditDetails:function(e){this.bfRecord=e,this.visible=!0},handleCancel:function(){this.visible=!1,this.showAll=!1},getModalContainer:function(){return document.getElementById("info")},fnSearch:function(e){this.baseInfoForm.getFieldValue("flag");this.Base.openTabMenu({id:e.akb020+e.aaz217,name:"【"+e.name+"】患者详情",url:"detailsQuery.html#/expenseDetailsForOther?akb020=".concat(e.akb020,"&akc190=").concat(e.akc190,"&aaz217=").concat(e.aaz217,"&flag=").concat(e.aae500),refresh:!1})},fnQueryRuleList:function(e){var t=this;if(this.defaultAae500=e,"1"===e){this.defaultDeptType=1,this.defaultPatientPlaceholder=!1;for(var a=this.$refs.infoTableRef.getTableColumn().fullColumn,i=0;i<a.length;i++)"akc191"===a[i].property&&(a[i].title="登记号");this.hideOrShoeColumns.forEach((function(e){t.$refs.infoTableRef.hideColumn(t.$refs.infoTableRef.getColumnByField(e))}))}else{this.defaultDeptType="2"===e?1:2,this.defaultPatientPlaceholder=!0,this.hideOrShoeColumns.forEach((function(e){t.$refs.infoTableRef.showColumn(t.$refs.infoTableRef.getColumnByField(e))}));for(var l=this.$refs.infoTableRef.getTableColumn().fullColumn,o=0;o<l.length;o++)"akc191"===l[o].property&&(l[o].title="住院号")}var n={url:"/miimCommonRead/queryRuleTypeDicByAae500",autoValid:!0,data:{aae500:e}},s={successCallback:function(e){t.ruleList=e.data.ruleList},failCallback:function(e){t.$message.error("医院数据加载失败")}};this.Base.submit(null,n,s)},getTreeData:function(){var e=this;Base.submit(null,{url:"mttRuleCustom/getRuleTreeData"}).then((function(t){e.treeData=t.data.list,e.treeData.length>0&&(e.treeData[0].disabled=!0,e.treeData[0].children.forEach((function(e){e.disabled=!0})))}))},fnQueryHos:function(){var e=this,t={url:"/miimCommonRead/queryHospRpcList",autoValid:!0},a={successCallback:function(t){e.hosList=t.data.resultData,e.baseInfoForm.setFieldsValue({medinsCode:e.hosList[0].value}),e.permissions&&e.permissions.akb020Set.size>0&&(e.hosList=e.hosList.filter((function(t){return e.permissions.akb020Set.has(t.value)})),e.baseInfoForm.setFieldsValue({medinsCode:e.hosList[0].value})),e.akb020=e.hosList[0].value,e.fnQueryDept(e.akb020)},failCallback:function(t){e.$message.error("医院数据加载失败")}};this.Base.submit(null,t,a)},fnQueryDept:function(e){var t=this;this.akb020=e||"",this.Base.submit(null,{url:"miimCommonRead/queryDepartRpcList",data:{akb020:this.akb020,hospDeptType:"I"},autoValid:!1},{successCallback:function(e){t.aaz307||t.baseInfoForm.resetFields("aaz307"),t.ksList=e.data.resultData,t.fnQueryGroup(null)},failCallback:function(e){t.$message.error("科室数据加载失败")}})},fnQueryGroup:function(e){var t=this,a={akb020:this.akb020};e&&(a.aaz307=e),this.permissions.aaz307Disable&&(a.aaz307=this.permissions.aaz307Set.values().next().value),a.aaz307?this.Base.submit(null,{url:"miimCommonRead/queryGroupDic",data:a,autoValid:!1},{successCallback:function(e){t.baseInfoForm.resetFields("aaz309"),t.groupList=e.data.resultData,t.fnQueryDocter()},failCallback:function(e){t.$message.error("医师数据加载失败")}}):(this.groupList=[],this.fnQueryDocter())},fnQueryDocter:function(e){var t=this,a={akb020:this.akb020};this.Base.submit(null,{url:"miimCommonRead/queryDoctorRpcList",data:a,autoValid:!1},{successCallback:function(e){t.aaz263||t.baseInfoForm.resetFields("aaz263"),t.doctorList=e.data.resultData},failCallback:function(e){t.$message.error("医师数据加载失败")}})},disabledStartDate:function(e){return e.format("YYYYMMDD")>this.baseInfoForm.getFieldValue("endDate").format("YYYYMMDD")},disabledEndDate:function(e){return e=e.format("YYYYMMDD"),e>f()().startOf("day").format("YYYYMMDD")||e<this.baseInfoForm.getFieldValue("startDate").format("YYYYMMDD")},infoPageParams:function(){var e=this.baseInfoForm.getFieldsValue();return e.dateSet&&2==e.dateSet.length&&("audit"==this.onRadioValue?(e.auditStartDate=e.dateSet[0].format("YYYY-MM-DD"),e.auditEndDate=e.dateSet[1].format("YYYY-MM-DD")):(e.startDate=e.dateSet[0].format("YYYY-MM-DD"),e.endDate=e.dateSet[1].format("YYYY-MM-DD"))),e.aaz307||(e.aaz307=this.aaz307),e.akb020=e.medinsCode,e.aae500List&&0!==e.aae500List.length||(e.aae500List=["2","3","4","5","7"]),e.jobNumber=this.curUserJobNumber,e.curUserDeptCode=this.curUserDeptCode,e.systemName=this.systemName,e},fnQuery:function(){var e=this,t=this.baseInfoForm.getFieldsValue(),a=t.allDate;!a||a[0]&&a[1]?(this.formShowAll=!1,this.$nextTick((function(){e.$refs.infoPageRef.loadData((function(a){e.rulelessShow=!!t.result.includes("4")}))}))):this.$message.error("请选择时间范围！")},fnQueryTableTitle:function(){var e=this,t=top.indexTool.getActiveTabMenuId(),a=top.indexTool.getUserInfo().loginId,i=[];this.Base.submit(null,{url:"miimCommonRead/selectTableColumShow",data:{resourceid:t,loginid:a},autoValid:!1},{successCallback:function(t){if(t.data.list.length>0&&t.data.list[0].menu?e.checkedList=JSON.parse(t.data.list[0].menu):e.checkedList=e.configList.map((function(e){return e.value})),e.bfcheckedList=e.checkedList,e.configList.forEach((function(t){e.menuConfig[t.value]=e.checkedList.includes(t.value)})),t.data.list.length>0){var a=JSON.parse(t.data.list[0].colum),l=e.$refs.infoTableRef.getTableColumn().fullColumn;i=a.map((function(e){return e.title}));var o=[];l.forEach((function(e){var i=a.find((function(t){return t.title===e.title}));i&&(e.visible=i.visible);var l=e;"操作"!=l.title&&"项目引导信息"!=l.title&&(l.sortable?l.width=20*l.title.length+27:l.width=20*l.title.length+12),t.data.akc191Title.length>0&&"akc191"===l.property&&(l.title=t.data.akc191Title[0].label),o.push(l)})),i.length>0&&o.sort((function(e,t){return"序号"===e.title?-1:"序号"===t.title?1:i.indexOf(e.title)-i.indexOf(t.title)})),e.$refs.infoTableRef.loadColumn(o)}"2"===t.data.auditEngType||"3"===t.data.auditEngType?e.$refs.infoTableRef.showColumn(e.$refs.infoTableRef.getColumnByField("ykz126")):e.$refs.infoTableRef.hideColumn(e.$refs.infoTableRef.getColumnByField("ykz126"))},failCallback:function(t){e.$message.error("查询表头失败")}})},fnSaveTableTitle:function(){var e=this,t=this.$refs.infoTableRef.getTableColumn().fullColumn,a=[];t.forEach((function(e){var t=e.title,i=e.visible;a.push({title:t,visible:i})}));var i=top.indexTool.getActiveTabMenuId(),l=top.indexTool.getUserInfo().loginId;this.Base.submit(null,{url:"miimCommonRead/insertTableColumShow",data:{colum:JSON.stringify(a),menu:JSON.stringify(this.checkedList),resourceid:i,loginid:l},autoValid:!1},{successCallback:function(t){e.$message.success("保存成功")},failCallback:function(t){e.$message.error("保存失败")}})},exportExcel:function(){var e=this,t=this.baseInfoForm.getFieldsValue(),a=t.allDate;if(!a||a[0]&&a[1]){t=this.infoPageParams(),t.aae500List&&0!==t.aae500List.length||(t.aae500List=["2","3","4","5","7"]);var i,l=[],n=this.$refs.infoTableRef.getColumns().filter((function(e){return!("2"===t.flag&&("ape805"===e.property||"ape804"===e.property))})),s=(0,o.Z)(n);try{for(s.s();!(i=s.n()).done;){var r=i.value;"seq"!==r.type&&"operate"!==r.property&&!1!==r.visible&&"checkbox"!==r.type&&(l.push({header:r.title,key:r.property,width:20}),"ape893"===r.property&&l.push({header:"操作内容",key:"aaz560_1",width:20}))}}catch(d){s.e(d)}finally{s.f()}var c=n.map((function(e){return e.property})),u=[{codeType:"AAE500",columnKey:"aae500"},{codeType:"AAE140",columnKey:"aae140"},{columnKey:"ape800",customCollection:function(e,t){"0"==e.value?e.value="审核通过":"1"==e.value?e.value="可疑提醒":"2"==e.value?e.value="违规提醒":"3"==e.value&&(e.value="仅提醒")}},{columnKey:"ape896",customCollection:function(e,t){"0"==e.value||"1"==e.value?e.value="—":"2"==e.value?e.value="自费":"3"==e.value?e.value="报销":""!=e.value&&null!=e.value||(e.value="—")}},{columnKey:"ape893",customCollection:function(e,t){var a=document.createElement("div");a.innerHTML=e.value;var i=a.innerText||a.textContent;a=null,e.value=i}},{columnKey:"ykz126",customCollection:function(e,t){"5"==e.value?e.value="中心端":e.value="医院端"}}],f=u.filter((function(e){return c.includes(e.columnKey)}));this.Base.submit(null,{url:"issueInventory/exportExcel",data:t,autoValid:!1},{successCallback:function(t){var a=t.data.data;a.forEach((function(e){e.diagnoses&&Object.assign(e,e.diagnoses),e.ape805&&e.akc225&&(e.ape804=e.ape805*e.akc225),e.ape893.indexOf("备案")>-1&&(e.aaz560_1=e.ape893.split(":")[1]||e.ape893.split("：")[1],e.ape893="备案"),e.ape893.indexOf("补充诊断")>-1&&(e.aaz560_1=e.aaz560.split(":")[1]||e.aaz560.split("：")[1],e.ape893="补充诊断")}));var i={fileName:"住院审核提醒查询结果表",sheets:[{name:"worksheet1",column:{complex:!1,columns:l},rows:a,codeList:f}]};e.Base.generateExcel(i)},failCallback:function(t){e.$message.error("医师数据加载失败")}})}else this.$message.error("请选择时间范围！")},fnCustomHeaderCell:function(e){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px",fontWeight:"bold"}}},filterOption:function(e,t){return t.componentOptions.children[0].text.indexOf(e)>=0},fnPreBatchDispatch:function(){0!=this.$refs.infoTableRef.getCheckboxRecords().length?this.batchDispatchVisible=!0:this.$message.error("请选择要下发的项目")},fnBatchDispatch:function(){var e=this,t=[],a=[],i=[];"2"===this.defaultAae500||"1"===this.defaultAae500?this.$refs.infoTableRef.getCheckboxRecords().forEach((function(a){2===e.defaultDeptType?a.aaz266&&"9999"!==a.aaz266?t.push(a.akc310):i.push(a):t.push(a.akc310)})):"3"===this.defaultAae500&&this.$refs.infoTableRef.getCheckboxRecords().forEach((function(t){2===e.defaultDeptType?t.aaz266&&"9999"!==t.aaz266?a.push(t.ykc610):i.push(t):a.push(t.ykc610)}));var l={akc310s:t,ykc610s:a,dispatchDeptType:this.defaultDeptType,aae500:this.defaultAae500};2===this.defaultDeptType&&i.length>0?this.$confirm({content:"批量下发存在"+i.length+"条无执行科室数据，仅下发有执行科室的数据。",onOk:function(){e.fnBatchDispatchSubmit(l)},onCancel:function(){}}):this.fnBatchDispatchSubmit(l)},fnBatchDispatchSubmit:function(e){var t=this;this.Base.submit(null,{url:"issueInventory/batchDispatch",data:e,autoValid:!1},{successCallback:function(e){t.$message.success("下发成功"),t.batchDispatchVisible=!1,t.fnQuery()},failCallback:function(e){t.$message.error("下发失败")}})}}},S=z,T=a(1001),x=(0,T.Z)(S,i,l,!1,null,"4ca559c8",null),L=x.exports},55382:function(){},61219:function(){}}]);
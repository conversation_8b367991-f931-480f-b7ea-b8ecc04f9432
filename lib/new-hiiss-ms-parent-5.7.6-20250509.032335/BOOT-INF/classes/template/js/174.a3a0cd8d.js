"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[174],{88412:function(t,a,e){var l=e(26263),n=e(36766),i=e(1001),o=(0,i.Z)(n.Z,l.s,l.x,!1,null,"5e7ef0ae",null);a["Z"]=o.exports},90174:function(t,a,e){e.r(a),e.d(a,{default:function(){return f}});var l=function(){var t=this,a=this,e=a.$createElement,l=a._self._c||e;return l("div",{staticClass:"fit"},[l("ta-border-layout",{attrs:{layout:{header:"120px"}}},[l("div",{attrs:{slot:"header"},slot:"header"},[l("ta-title",{attrs:{title:"查询条件"}}),l("ta-form",{attrs:{autoFormCreate:function(a){t.form1=a},formLayout:"",enctype:"multipart/form-data"}},[l("ta-form-item",{attrs:{fieldDecoratorId:"aaa027",labelCol:{span:8},wrapperCol:{span:16},span:6,disabled:a.authObj&&a.authObj.isHospType(),initValue:a.authObj&&a.authObj.isHospType()&&a.treeData[0]?a.treeData[0].value:""}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[a._v("统筹区")]),l("ta-tree-select",{attrs:{dropdownStyle:{maxHeight:"400px",overflow:"auto"},treeData:a.treeData,treeDataSimpleMode:a.treeData,"allow-clear":!0},on:{change:a.queryHospitalName}})],1),l("ta-form-item",{attrs:{fieldDecoratorId:"akb020",labelCol:{span:8},wrapperCol:{span:16},span:6,disabled:a.authObj&&a.authObj.isHospType(),initValue:a.authObj&&a.authObj.isHospType()?a.authObj.getOrgCode():""}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[a._v("医院选择")]),l("ta-select",{attrs:{"show-search":!0,placeholder:"请选择",allowClear:!0,options:a.hospitalList},on:{change:a.hospChange}})],1),l("ta-form-item",{attrs:{span:12,labelCol:{span:0},wrapperCol:{span:24}}},[l("ta-button",{staticStyle:{"margin-left":"20px"},attrs:{type:"primary",icon:"download"},on:{click:a.sampleDownload}},[a._v("医护人员信息导入模板下载")]),l("ta-upload",{attrs:{showUploadList:!1,accept:".xls,.xlsx",customRequest:a.fileRequest,action:a.handlUpload}},[l("ta-button",{attrs:{type:"success"}},[l("ta-icon",{attrs:{type:"upload"}}),a._v(" 批量导入医院信息 ")],1)],1)],1)],1)],1),l("div",{staticClass:"fit"},[l("ta-title",{attrs:{title:"医院科室信息"}}),l("ta-row",[l("ta-button",{attrs:{disabled:null===a.akb020||void 0===a.akb020||""==a.akb020,icon:"plus",type:"primary"},on:{click:a.showModal}},[l("ta-icon",{attrs:{type:"add"}}),a._v(" 新增 ")],1)],1),l("ta-modal",{attrs:{title:a.modalTitle,height:"300px",draggable:!0,zoomable:!0,"destroy-on-close":!0},on:{ok:a.handleOk},model:{value:a.visible,callback:function(t){a.visible=t},expression:"visible"}},[l("ta-form",{attrs:{autoFormCreate:function(a){t.form=a}}},[l("ta-form-item",{attrs:{label:"科室名称",fieldDecoratorId:"aae386",require:{message:"请输入名称"}}},[l("ta-input",{attrs:{placeholder:"科室名称"}})],1),l("ta-form-item",{attrs:{require:{message:"请输入编号!"},label:"科室编码",fieldDecoratorId:"aaz307",disabled:"编辑医院科室信息"===a.modalTitle}},[l("ta-input",{attrs:{placeholder:"科室编码"}})],1),l("ta-form-item",{attrs:{label:"床位数",fieldDecoratorId:"akf015"}},[l("ta-inputNumber",{staticStyle:{width:"100%"},attrs:{placeholder:"床位数"}})],1),l("ta-form-item",{attrs:{label:"是否有效",initValue:a.defaultValue,fieldDecoratorId:"aae100"}},[l("ta-select",{attrs:{options:[{value:"1",label:"有效"},{value:"0",label:"无效"}],placeholder:"请选择"}})],1)],1)],1),l("div",{staticStyle:{height:"calc(100% - 110px)"}},[l("ta-table",{attrs:{scroll:{y:"100%"},bordered:!0,size:"small",columns:a.columns,dataSource:a.tableData,customRow:a.fnCustomRow},scopedSlots:a._u([{key:"action",fn:function(t,e,n){return[l("span",[l("a",{on:{click:function(t){return a.handleShowEdit(e)}}},[l("ta-icon",{attrs:{type:"edit"}}),a._v("编辑")],1),l("ta-popconfirm",{attrs:{title:"是否删除?"},on:{confirm:function(){return a.handleDelete(e)}}},[l("a",{style:{marginLeft:"10px"}},[l("ta-icon",{attrs:{type:"delete"}}),a._v("删除")],1)])],1)]}}])})],1),l("ta-pagination",{ref:"departmentPager",staticStyle:{float:"right","margin-top":"8px","margin-right":"10px"},attrs:{showSizeChanger:"",showQuickJumper:"",dataSource:a.tableData,defaultPageSize:10,pageSizeOptions:["10","20","40"],params:a.pageParams,url:"Department/getDepartmentInfo"},on:{"update:dataSource":function(t){a.tableData=t},"update:data-source":function(t){a.tableData=t}}})],1)])],1)},n=[],i=e(48534),o=e(95082),r=(e(36133),e(32564),e(88412)),s=e(75660),u={name:"departmentInfo",components:{TaTitle:r.Z},data:function(){return{authObj:null,treeData:[],defaultValue:"1",visible:!1,akb020:null,modalTitle:"",hospitalList:[],columns:[{title:"医院名称",dataIndex:"akb021",width:200,customHeaderCell:this.fnCustomHeaderCell,align:"center"},{title:"统筹区",dataIndex:"aaa027",width:200,customHeaderCell:this.fnCustomHeaderCell,align:"center",customRender:this.aaa027Format},{title:"科室名称",dataIndex:"aae386",width:200,customHeaderCell:this.fnCustomHeaderCell,align:"center"},{title:"科室编码",dataIndex:"aaz307",width:200,align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"床位数",dataIndex:"akf015",width:200,align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"有效标识",dataIndex:"aae100",width:200,align:"center",customHeaderCell:this.fnCustomHeaderCell,collectionType:"EFFECTIVE"},{title:"操作",dataIndex:"action",width:150,align:"center",scopedSlots:{customRender:"action"},customHeaderCell:this.fnCustomHeaderCell}],tableData:[]}},methods:{handleShowEdit:function(t){var a=this;this.modalTitle="编辑医院科室信息",this.visible=!0,setTimeout((function(){a.form.setFieldsValue(t)}),3)},fnCustomRow:function(t,a){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px"}}},fnCustomHeaderCell:function(t){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px",fontWeight:"bold"}}},aaa027Format:function(t){var a=this.treeData.find((function(a){return a.value===t}));return a?a.label:""},fileRequest:function(){},sampleDownload:function(){var t=this;Base.downloadFile({method:"post",fileName:"科室信息导入模板.xls",url:"Department/download"}).then((function(a){t.$message.success("下载成功")})).catch((function(a){t.$message.error("下载失败")}))},handlUpload:function(t){var a=this;return this.Base.submit(null,{headers:{"Content-Type":"multipart/form-data"},url:"Department/uploadFile",autoValid:!0,isFormData:!0,data:{uploadFile:t}},{successCallback:function(t){a.$message.success("上传成功"),a.loadTableData()},failCallback:function(t){a.$message.error("上传失败")}}),!1},handleOk:function(t){var a=this;if("新增医院科室信息"==this.modalTitle){var e=(0,o.Z)({akb020:this.akb020},this.form.getFieldsValue());this.Base.submit(null,{url:"Department/insertDepartment",data:(0,o.Z)({},e),autoValid:!0},{successCallback:function(t){a.loadTableData(),a.visible=!1},failCallback:function(t){a.$message.error("添加数据失败，请检查数据")}})}else this.handleUpdate()},showModal:function(){var t=this;this.modalTitle="新增医院科室信息",this.visible=!0,setTimeout((function(){t.form.setFieldsValue()}),3)},loadTableData:function(){this.$refs.departmentPager.loadData()},handleUpdate:function(){var t=this,a=this.form.getFieldsValue(),e=this.tableData.find((function(t){return t.aaz307==a.aaz307}));this.Base.submit(null,{url:"Department/updateDepartment",data:(0,o.Z)({akb020:e.akb020},this.form.getFieldsValue()),autoValid:!0},{successCallback:function(a){t.loadTableData(),t.visible=!1},failCallback:function(a){t.$message.error("更新失败")}})},pageParams:function(){return this.form1.getFieldsValue()},changeData:function(t){var a=t.newData,e=t.record;t.rowKey;Object.assign(e,a)},fnTableChange:function(t){this.tableData=t},handleDelete:function(t){var a=this;this.Base.submit(null,{url:"Department/deleteDepartment",data:{aaz307:t.aaz307,akb020:t.akb020},autoValid:!0},{successCallback:function(t){a.loadTableData()},failCallback:function(t){a.$message.error("删除失败")}})},hospChange:function(t){var a=this;this.akb020=t,setTimeout((function(){a.loadTableData()}),50)},queryHospitalName:function(t){var a=this;this.Base.submit(null,{url:"miimCommonRead/queryAkb020DataByAaa027",data:{aaa027:t}}).then((function(t){a.hospitalList=t.data.akb020Data,a.form1.resetFields(["akb020"]),a.akb020=null,setTimeout((function(){a.loadTableData()}))}))},init:function(){var t=this;this.Base.submit(null,{url:"Department/initData",autoValid:!0},{successCallback:function(a){t.hospitalList=a.data.akb020Data},failCallback:function(a){t.$message.error("初始化数据失败")}}),this.Base.submit(null,{url:"miimCommonRead/getTreeData",data:{}}).then((function(a){t.treeData=a.data.treeData,t.loadTableData()})),this.authObj&&this.authObj.isHospType()&&(this.akb020=this.authObj.getOrgCode())}},mounted:function(){var t=this;return(0,i.Z)(regeneratorRuntime.mark((function a(){return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,s.Z.initAuth(t);case 2:t.authObj=a.sent,t.init();case 4:case"end":return a.stop()}}),a)})))()}},c=u,d=e(1001),m=(0,d.Z)(c,l,n,!1,null,null,null),f=m.exports},36766:function(t,a,e){var l=e(66586);a["Z"]=l.Z},26263:function(t,a,e){e.d(a,{s:function(){return l},x:function(){return n}});var l=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("h3",{staticStyle:{"font-weight":"normal"}},[e("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),e("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},n=[]},66586:function(t,a){var e={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};a["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:e}}}},75660:function(t,a,e){var l=e(48534),n=e(13087),i=e(62833),o=(e(36133),function(){function t(a){(0,n.Z)(this,t),this.authList=a.customResources,this.orgCode=a.orgCode}return(0,i.Z)(t,[{key:"getCustomAuthList",value:function(){return this.authList}},{key:"getOrgCode",value:function(){return this.orgCode}},{key:"isHospType",value:function(){return this.authList.includes("hosptype")}}]),t}());a["Z"]={initAuth:function(t){return(0,l.Z)(regeneratorRuntime.mark((function a(){var e;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,t.Base.submit(null,{url:"miimCommonRead/queryCustomResources",data:{}});case 2:return e=a.sent,a.abrupt("return",new o(e.data));case 4:case"end":return a.stop()}}),a)})))()}}}}]);
"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[4278],{64278:function(t,a,e){e.r(a),e.d(a,{default:function(){return g}});var r=function(){var t=this,a=this,e=a.$createElement,r=a._self._c||e;return r("ta-border-layout",{staticStyle:{width:"100%",height:"100vh"},attrs:{"layout-type":"fixTop"}},[r("div",{staticStyle:{padding:"24px 10px 0 10px"},attrs:{slot:"header"},slot:"header"},[r("ta-form",{attrs:{layout:"horizontal","form-layout":!0,"auto-form-create":function(a){return t.form=a},"label-width":"100px"}},[r("ta-form-item",{attrs:{label:"图形名称",span:6,"field-decorator-id":"txmc"}},[r("ta-input",{attrs:{placeholder:"请输入"}})],1),r("div",{staticStyle:{float:"right"}},[r("ta-button",{attrs:{type:"primary"},on:{click:a.fnQuery}},[a._v("查询")])],1)],1)],1),r("cardBox",{staticClass:"fit",attrs:{title:"图形列表"}},[r("template",{slot:"extra"},[r("ta-button",{attrs:{type:"primary",icon:"plus",ghost:!0},on:{click:a.fnAdd}},[a._v("创建图形")])],1),r("template",{slot:"boxContent"},[r("ta-big-table",{ref:"table_zdysgxlb",attrs:{resizable:"",border:"","auto-resize":"",height:"100%",align:"center",data:a.tableData},scopedSlots:a._u([{key:"bottomBar",fn:function(){return[r("ta-pagination",{ref:"gridPager",staticStyle:{"text-align":"right","margin-top":"10px"},attrs:{"data-source":a.tableData,"default-page-size":10,showQuickJumper:!1,params:a.getParams,showTotal:!0,total:a.total,url:"/smtrpt/chartCfg/queryPageList"},on:{"update:dataSource":function(t){a.tableData=t},"update:data-source":function(t){a.tableData=t},loaded:a.loadedCallBack}})]},proxy:!0}])},[r("ta-big-table-column",{attrs:{type:"seq",title:"序号",width:"50"}}),r("ta-big-table-column",{attrs:{field:"chartName",title:"图形名称",width:"160","show-overflow":"",sortable:""}}),r("ta-big-table-column",{attrs:{field:"chartDesc",title:"图形描述",width:"","header-align":"center",align:"left","show-overflow":"",sortable:""}}),r("ta-big-table-column",{attrs:{field:"chartType",title:"展示方式",width:"100","show-overflow":"",sortable:""},scopedSlots:a._u([{key:"default",fn:function(t){return[r("span",[a._v(a._s("01"===t.row.chartType?"线图":"02"===t.row.chartType?"柱图":"03"===t.row.chartType?"饼图":"04"===t.row.chartType?"柱线图":""))])]}}])}),r("ta-big-table-column",{attrs:{field:"crteTime",title:"创建时间",width:"160","show-overflow":"",sortable:""}}),r("ta-big-table-column",{attrs:{field:"updtTime",title:"更新时间",width:"160","show-overflow":"",sortable:""}}),r("ta-big-table-column",{attrs:{field:"modierName",title:"操作人",width:"100","show-overflow":""}}),r("ta-big-table-column",{attrs:{title:"操作",width:"140"},scopedSlots:a._u([{key:"default",fn:function(t){return[r("span",{staticStyle:{cursor:"pointer","font-weight":"600",color:"#1A65B9"},on:{click:function(e){return a.rowShow(t.row)}}},[a._v("预览")]),r("span",{staticStyle:{margin:"0 8px",cursor:"pointer","font-weight":"600",color:"#1A65B9"},on:{click:function(e){return a.rowEdit(t.row)}}},[a._v("编辑")]),r("ta-popconfirm",{attrs:{title:"是否删除?",okText:"是",cancelText:"否"},on:{confirm:function(e){return a.rowDelete(t.row,t.rowIndex)}}},[r("ta-icon",{staticStyle:{color:"red"},attrs:{slot:"icon",type:"question-circle-o"},slot:"icon"}),r("span",{staticStyle:{cursor:"pointer","font-weight":"600",color:"#1A65B9"}},[a._v("删除")])],1)]}}])})],1)],1)],2)],1)},o=[],i=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticStyle:{height:"100%"}},[e("div",{staticClass:"flex_boxs"},[e("div",{staticClass:"cardTitle"},[t._v(t._s(t.title)+" "),t._t("desc")],2),e("div",{staticStyle:{width:"50%","text-align":"right","line-height":"60px"}},[t._t("extra")],2)]),e("div",{staticStyle:{width:"100%",height:"calc(100% - 60px)",padding:"0 10px"}},[t._t("boxContent")],2)])},l=[],n={name:"cardBox",props:{title:{type:String,default:""}}},s=n,c=e(1001),u=(0,c.Z)(s,i,l,!1,null,"dd9cc506",null),d=u.exports,h={name:"tbgl",components:{cardBox:d},data:function(){return{pageNum:1,pageSize:10,total:0,tableData:[]}},mounted:function(){this.fnQuery()},methods:{getParams:function(){var t=this.form.getFieldsValue(),a={chartName:t.txmc};return a},fnQuery:function(){this.$refs.gridPager.loadData()},loadedCallBack:function(t){this.total=t.data.pageBean.recordCounts||0,this.tableData=t.data.pageBean.data||[]},fnAdd:function(){this.$router.push({name:"otherPage",params:{pageFlag:"add"}})},rowShow:function(t){var a=t.rptChartCfgId;this.$router.push({name:"showPage",params:{pageFlag:"show",rptChartCfgId:a}})},rowEdit:function(t){var a=t.rptChartCfgId;this.$router.push({name:"otherPage",params:{pageFlag:"edit",rptChartCfgId:a}})},rowDelete:function(t,a){var e=this;Base.submit(null,{url:"/smtrpt/chartCfg/deleteChartCfg",data:{rptChartCfgId:t.rptChartCfgId}}).then((function(t){t.serviceSuccess&&(e.$message.success("删除成功"),e.tableData.splice(a,1),e.fnQuery())}))}}},f=h,p=(0,c.Z)(f,r,o,!1,null,"1ecea77a",null),g=p.exports}}]);
"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[26],{88412:function(t,e,a){var l=a(26263),o=a(36766),i=a(1001),n=(0,i.Z)(o.Z,l.s,l.x,!1,null,"5e7ef0ae",null);e["Z"]=n.exports},70026:function(t,e,a){a.r(e),a.d(e,{default:function(){return m}});var l=function(){var t=this,e=this,a=e.$createElement,l=e._self._c||a;return l("div",{staticClass:"fit"},[l("ta-border-layout",{attrs:{layout:{header:"140px",footer:"55px"},showPadding:!1,"footer-cfg":{showBorder:!1}}},[l("div",{staticStyle:{"text-align":"center","margin-top":"25px"},attrs:{slot:"header"},slot:"header"},[l("ta-form",{attrs:{"auto-form-create":function(e){t.form=e},layout:"horizontal",formLayout:!0}},[l("ta-form-item",{attrs:{label:"数据聚集类型",fieldDecoratorId:"aggregatType",span:7,labelCol:{span:6},wrapperCol:{span:17},"init-value":"1",require:{message:"数据聚集类型"}}},[l("ta-radio-group",{attrs:{options:e.plainOptions}})],1),l("ta-form-item",{attrs:{label:"数据同步开关",fieldDecoratorId:"syncOnOff",span:4,"init-value":"off",labelCol:{span:10},wrapperCol:{span:12},require:{message:"请选择是否同步数据"}}},[l("ta-radio-group",[l("ta-radio",{attrs:{value:"off"}},[e._v("关")]),l("ta-radio",{attrs:{value:"on"}},[e._v("开")])],1)],1),l("ta-form-item",{attrs:{fieldDecoratorId:"qh",label:"期号",required:!0,fieldDecoratorOptions:{rules:[{required:!0,message:"请输入期号"},{pattern:/^\b[1-3]\d{3}(0[1-9]|1[0-2])$/,message:"格式有误"}]},span:4,labelCol:{span:6},wrapperCol:{span:10}}},[l("ta-input",{attrs:{placeholder:"格式yyyyMM"}})],1),l("ta-form-item",{attrs:{fieldDecoratorId:"akc190s",label:"住院号",span:5,labelCol:{span:6},wrapperCol:{span:16}}},[l("ta-input",{attrs:{placeholder:"123(或者:'zy123')多个用,隔开"}})],1),l("ta-button",{staticStyle:{float:"right","margin-right":"5%"},on:{click:e.fnExecuteJob}},[e._v("手动执行任务")]),l("ta-button-group",{staticStyle:{float:"left","margin-right":"30px"}},[l("ta-button",{staticStyle:{width:"136px",height:"32px",background:"#2290FF","border-radius":"4px 0 0 4px","font-family":"PingFangSC-Regular","font-size":"16px",color:"#FFFFFF","letter-spacing":"0"},on:{click:e.handleShowAddJob}},[e._v("新增任务")]),l("ta-button",{staticStyle:{width:"136px",height:"32px",background:"rgba(255,255,255,1)","border-radius":"0 4px 4px 0","font-family":"PingFangSC-Regular","font-size":"16px",color:"#2290FF","letter-spacing":"0"},on:{click:e.handleReload}},[e._v("刷新任务列表")])],1)],1)],1),l("div",{staticClass:"fit",staticStyle:{height:"95%"}},[l("ta-title",{attrs:{title:"JOB列表"}}),l("ta-table",{ref:"jobTable",attrs:{columns:e.tableColumns,dataSource:e.tableData,size:"small",scroll:{y:"100%"},customRow:e.fnCustomRow},on:{"update:columns":function(t){e.tableColumns=t}},scopedSlots:e._u([{key:"start",fn:function(t,a,o){return[l("ta-icon",{staticStyle:{color:"red"},attrs:{type:"check"},on:{click:function(t){return e.handleStartJob(a)}}})]}},{key:"delete",fn:function(t,a,o){return[l("span",[l("ta-popconfirm",{attrs:{title:"是否删除?"},on:{confirm:function(){return e.handleDeleteJob(a)}}},[l("a",{style:{marginLeft:"10px"}},[l("ta-icon",{staticStyle:{color:"red"},attrs:{type:"close"}})],1)])],1)]}}])})],1),l("div",{attrs:{slot:"footer"},slot:"footer"},[l("ta-pagination",{ref:"gridPager",staticStyle:{float:"right","margin-top":"8px","margin-right":"10px"},attrs:{showSizeChanger:"",showQuickJumper:"",dataSource:e.tableData,params:e.pageParams,defaultPageSize:15,pageSizeOptions:["15","30","40"],url:"job/queryJob"},on:{"update:dataSource":function(t){e.tableData=t},"update:data-source":function(t){e.tableData=t}}})],1)]),l("div",{attrs:{id:"modal"}},[l("ta-modal",{attrs:{width:"400px",height:"250px",getContainer:e.getModalContainer},on:{cancel:e.handleCancel,ok:e.handleAddJob},model:{value:e.visible,callback:function(t){e.visible=t},expression:"visible"}},[l("h3",{staticStyle:{"text-align":"center","font-weight":"bold"},attrs:{slot:"title"},slot:"title"},[e._v(e._s(e.title))]),l("ta-form",{attrs:{autoFormCreate:function(e){t.form1=e},layout:"horizontal",formLayout:!0}},[l("ta-form-item",{attrs:{fieldDecoratorId:"name",fieldDecoratorOptions:{initialValue:"总控系统定时任务"},span:24,labelCol:{span:7},wrapperCol:{span:17},disabled:!0}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("job名称")]),l("ta-input")],1),l("ta-form-item",{attrs:{fieldDecoratorId:"cronExample",span:24,labelCol:{span:7},wrapperCol:{span:17}}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("Cron例子")]),l("ta-select",{attrs:{options:e.cronExampleOptions},on:{change:e.handleSetFixedtime}})],1),l("ta-form-item",{attrs:{fieldDecoratorId:"fixedtime",span:24,labelCol:{span:7},wrapperCol:{span:17},require:{message:"必输字段"}}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("Cron表达式")]),l("ta-input")],1)],1)],1)],1)],1)},o=[],i=a(66347),n=a(88412),r=[],s=[{value:"1",label:"每5秒执行"},{value:"2",label:"每15秒，30秒，45秒执行"},{value:"3",label:"15到45秒内，每秒都执行"},{value:"4",label:"每天1点10分30秒执行"},{value:"5",label:"2011年10月每周日1点10分30秒执行"}],d={name:"job",components:{TaTitle:n.Z},data:function(){var t=[{title:"任务名称",dataIndex:"name",align:"center",width:300,customHeaderCell:this.fnCustomHeaderCell},{title:"定时时间",dataIndex:"fixedtime",align:"center",width:300,customHeaderCell:this.fnCustomHeaderCell},{title:"创建时间",dataIndex:"createtime",align:"center",width:300,customHeaderCell:this.fnCustomHeaderCell},{title:"url",dataIndex:"preurl",align:"center",width:300,customHeaderCell:this.fnCustomHeaderCell},{title:"状态",dataIndex:"aae001",align:"center",width:300,customHeaderCell:this.fnCustomHeaderCell},{title:"端口",dataIndex:"port",align:"center",width:300,customHeaderCell:this.fnCustomHeaderCell},{title:"服务ID",dataIndex:"id",align:"center",width:300,customHeaderCell:this.fnCustomHeaderCell},{title:"启动",scopedSlots:{customRender:"start"},align:"center",width:300,customHeaderCell:this.fnCustomHeaderCell},{title:"删除",align:"center",scopedSlots:{customRender:"delete"},width:300,customHeaderCell:this.fnCustomHeaderCell}],e=[{label:"月",value:"1"},{label:"季",value:"2"},{label:"年",value:"3"},{label:"月季年",value:"4"},{label:"无",value:"0"}];return{tableColumns:t,tableData:r,visible:!1,title:"新增定时任务",cronExampleOptions:s,startedJobIds:[],plainOptions:e}},methods:{fnCustomRow:function(t,e){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px"}}},fnCustomHeaderCell:function(t){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px",fontWeight:"bold"}}},pageParams:function(){},fnInitData:function(){var t=this;this.$refs.gridPager.loadData((function(e){var a,l=(0,i.Z)(e.data.pageBean.list);try{for(l.s();!(a=l.n()).done;){var o=a.value;"已启动"==o.aae001&&t.startedJobIds.push(o.id)}}catch(n){l.e(n)}finally{l.f()}}))},handleStartJob:function(t){var e=this;this.startedJobIds.push(t.id),this.Base.submit(null,{url:"job/startJob",data:t,autoValid:!0},{successCallback:function(t){e.$message.success("启动成功"),e.$refs.gridPager.loadData((function(t){}))},failCallback:function(t){e.$message.error("启动失败")}})},handleDeleteJob:function(t){var e=this;this.startedJobIds.splice(this.startedJobIds.indexOf(t.id),1),this.Base.submit(null,{url:"job/deleteJob",data:t,autoValid:!0},{successCallback:function(t){e.$message.success("删除任务成功"),e.$refs.gridPager.loadData((function(t){}))},failCallback:function(t){e.$message.error("删除任务失败")}})},handleReload:function(){location.reload()},handleShowAddJob:function(){this.visible=!0},getModalContainer:function(){return document.getElementById("modal")},handleCancel:function(){this.visible=!1,this.form1.resetFields()},handleSetFixedtime:function(t){"1"==t?this.form1.setFieldsValue({fixedtime:"*/5 * * * * ?"}):"2"==t?this.form1.setFieldsValue({fixedtime:"15,30,45 * * * * ?"}):"3"==t?this.form1.setFieldsValue({fixedtime:"15-45 * * * * ?"}):"4"==t?this.form1.setFieldsValue({fixedtime:"30 10 1 * * ?"}):"5"==t&&this.form1.setFieldsValue({fixedtime:"30 10 1 ? 10 SUN 2011"})},handleAddJob:function(){var t=this;this.Base.submit(this.form1,{url:"job/addJob",data:this.form1.getFieldsValue(),autoValid:!0},{successCallback:function(e){t.$message.success("添加成功"),t.visible=!1,t.form1.resetFields(),t.$refs.gridPager.loadData()},failCallback:function(e){t.$message.error("添加失败")}})},fnExecuteJob:function(){var t=this;this.Base.submit(this.form,{url:"job/executeJob",data:this.form.getFieldsValue(),autoValid:!0},null).then((function(e){t.$message.success("手动执行任务成功,"+e.data.result)})).catch((function(e){t.$message.error("手动执行任务失败")}))}},mounted:function(){this.$refs.jobTable.hideColumns(["id"]),this.fnInitData()}},u=d,c=a(1001),f=(0,c.Z)(u,l,o,!1,null,"75f85c62",null),m=f.exports},36766:function(t,e,a){var l=a(66586);e["Z"]=l.Z},26263:function(t,e,a){a.d(e,{s:function(){return l},x:function(){return o}});var l=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},o=[]},66586:function(t,e){var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};e["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}}}]);
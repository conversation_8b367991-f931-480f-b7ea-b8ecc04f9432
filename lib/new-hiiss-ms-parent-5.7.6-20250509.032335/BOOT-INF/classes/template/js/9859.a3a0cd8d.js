"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[9859],{68467:function(a,e,t){t.r(e),t.d(e,{default:function(){return U}});var i=function(){var a=this,e=a.$createElement,t=a._self._c||e;return t("div",{staticStyle:{height:"100vh"}},[t("ta-border-layout",{attrs:{leftCfg:a.leftCfg,"show-border":!1}},[t("div",{ref:"leftExtraContent",attrs:{slot:"leftExtraContent",cssClass:"leftExtraContent"},slot:"leftExtraContent"},[t("div",{staticClass:"part-top-patient"},[t("ta-title",{attrs:{title:"患者列表"}})],1)]),t("div",{staticStyle:{width:"100%",height:"100%"},attrs:{slot:"left"},slot:"left"},[t("div",{staticStyle:{height:"100%",display:"flex","flex-direction":"column"}},[t("div",{staticClass:"searchBox"},[t("div",{directives:[{name:"show",rawName:"v-show",value:"2"==this.roleMOCK,expression:"this.roleMOCK == '2'"}],staticStyle:{display:"flex","align-items":"center"}},[t("span",{staticStyle:{color:"red","margin-right":"2px","font-size":"16px"}},[a._v("*")]),t("ta-select",{staticStyle:{width:"180px","margin-right":"10px"},attrs:{defaultValue:"lucy",allowClear:"",placeholder:"选择科室",options:a.DepartOptions,dropdownMatchSelectWidth:!1,dropdownStyle:{width:"250px"},showSearch:"",size:"small",searchDelay:3e3},on:{change:a.changeDeaprt,search:a.handleSearch},model:{value:a.dptCode,callback:function(e){a.dptCode=e},expression:"dptCode"}})],1),t("ta-input-search",{staticStyle:{width:"230px"},attrs:{placeholder:"请输入住院号/就诊号/身份证"},on:{input:a.searchPatient},model:{value:a.searchVal,callback:function(e){a.searchVal=e},expression:"searchVal"}}),"0"==a.roleMOCK?t("div",[t("ta-button",{attrs:{type:"warning",size:"small"},on:{click:a.handelLimit}},[a._v("已授权")])],1):a._e()],1),t("div",[t("ta-tabs",{attrs:{defaultActiveKey:a.defaultActiveKey},on:{change:a.changeTAB}},[t("ta-tab-pane",{key:"1",attrs:{tab:"门诊"}},[t("div",[t("div",{staticClass:"firstLine"},[t("div",{staticStyle:{display:"flex"}},a._l(a.sceneList,(function(e,i){return t("div",{key:i},[t("ta-button",{attrs:{size:"small",type:a.currentscene===e.id?"primary":"dashed"},on:{click:function(e){a.currentscene=i,a.scene=0==i?"opt":"opsp"}}},[a._v(a._s(e.name))])],1)})),0),t("div",[t("ta-button",{attrs:{type:"warning",size:"small"},on:{click:function(e){a.medicineSearchVisible=!0}}},[a._v("靶向药用药查询")])],1)])])]),t("ta-tab-pane",{key:"2",attrs:{tab:"住院"}})],1)],1),"1"==a.roleMOCK?t("div",a._l(a.dataSource,(function(e,i){return t("div",{key:i,staticStyle:{margin:"5px 0"}},[t("div",{staticStyle:{"margin-bottom":"5px"}},[a._v(" "+a._s("@self"==e.authorizerName?"我":"【"+e.authorizerName+"】授权给我")+"的患者： ")]),t("div",[t("ta-big-table",{ref:"patientTable",refInFor:!0,staticStyle:{cursor:"pointer"},attrs:{border:"",stripe:"",resizable:"","highlight-hover-row":"","highlight-current-row":"","show-overflow":"",height:"600",size:"mini",data:e.list,"empty-text":"暂无数据"},on:{"cell-click":function(e){return a.cellClick(i,e)}}},[t("ta-big-table-column",{attrs:{width:"40px",title:" ",type:"seq",fixed:"left",align:"center"}}),t("ta-big-table-column",{attrs:{field:"mdtrtId",width:"100px",title:a.mdtrtIdTitle,align:"center"}}),t("ta-big-table-column",{attrs:{field:"bedno",title:"床号",visible:a.bednoVisible,width:"70px",align:"center"}}),t("ta-big-table-column",{attrs:{field:"patnName",title:"姓名",width:"70px",align:"center"}}),t("ta-big-table-column",{attrs:{field:"gend",title:"性别",width:"50px",align:"center","collection-type":"SEX"}}),t("ta-big-table-column",{attrs:{field:"brdy",title:"年龄",width:"50px",align:"center",formatter:a.formatterAge}}),t("ta-big-table-column",{attrs:{title:"操作",width:"50px",align:"center"},scopedSlots:a._u([{key:"default",fn:function(e){var i=e.row;return[i.latestAaz217?t("span",{staticStyle:{color:"#276cf5"},on:{click:function(e){return a.gotoPatientDetaills(i)}}},[a._v("详情")]):a._e()]}}],null,!0)})],1)],1)])})),0):t("div",{staticStyle:{flex:"1",display:"flex","flex-direction":"column"}},[t("div",{staticStyle:{flex:"1"}},[t("ta-big-table",{ref:"patientTable",staticStyle:{cursor:"pointer"},attrs:{border:"",stripe:"",resizable:"","highlight-hover-row":"","highlight-current-row":"","show-overflow":"",height:"100%",size:"mini",data:a.dataSource[0].list,"empty-text":"暂无数据"},on:{"cell-click":function(e){return a.cellClick(0,e)}}},[t("ta-big-table-column",{attrs:{width:"40px",title:" ",type:"seq",fixed:"left",align:"center"}}),t("ta-big-table-column",{attrs:{field:"mdtrtId",width:"100px",title:"0"==this.roleMOCK?"就诊号":"住院号",align:"center"}}),t("ta-big-table-column",{attrs:{field:"bedno",title:"床号",width:"70px",align:"center"}}),t("ta-big-table-column",{attrs:{field:"patnName",title:"姓名",width:"70px",align:"center"}}),t("ta-big-table-column",{attrs:{field:"gend",title:"性别",width:"50px",align:"center","collection-type":"SEX"}}),t("ta-big-table-column",{attrs:{field:"brdy",title:"年龄",width:"50px",align:"center",formatter:a.formatterAge}}),t("ta-big-table-column",{attrs:{title:"操作",width:"50px",align:"center"},scopedSlots:a._u([{key:"default",fn:function(e){var i=e.row;return[i.latestAaz217?t("span",{staticStyle:{color:"#276cf5"},on:{click:function(e){return a.gotoPatientDetaills(i)}}},[a._v("详情")]):a._e()]}}])})],1)],1)])])]),t("div",{staticClass:"content"},[t("div",{staticClass:"part-top"},[t("ta-title",{attrs:{title:"待审项目"}})],1),t("div",{staticStyle:{display:"flex","align-items":"center","flex-wrap":"wrap","margin-left":"10px","margin-bottom":"5px"}},[t("ta-input-search",{staticStyle:{width:"141px"},attrs:{placeholder:"请输入项目名称"},model:{value:a.queryForm.project_name,callback:function(e){a.$set(a.queryForm,"project_name",e)},expression:"queryForm.project_name"}}),"2"!==a.roleMOCK?t("div",{staticStyle:{display:"flex","align-items":"center","margin-left":"10px"}},[t("span",[a._v("开单科室：")]),a._l(a.departList,(function(e,i){return t("div",{key:i},[t("ta-button",{attrs:{size:"small",type:a.currentDepart===e.id?"primary":"dashed"},on:{click:function(e){return a.selectDepart(i)}}},[a._v(a._s(e.name))])],1)}))],2):a._e(),t("div",{staticStyle:{display:"flex","align-items":"center","margin-left":"10px"}},[t("span",[a._v("费用类别：")]),a._l(a.tipsType,(function(e,i){return t("div",{key:i},[t("ta-button",{attrs:{size:"small",type:a.currentTipsIndex===i?"primary":"dashed"},on:{click:function(t){return a.selectTips(e,i)}}},[a._v(a._s(e.name))])],1)})),t("ta-select",{staticStyle:{width:"120px"},attrs:{options:a.tipsOptions,size:"small"},on:{change:a.handleChange},model:{value:a.tipValue,callback:function(e){a.tipValue=e},expression:"tipValue"}})],2),t("div",{staticStyle:{display:"flex","align-items":"center","margin-left":"10px"}},[t("span",[a._v("预审结果：")]),t("ta-checkbox-group",{attrs:{options:a.excuteRes},model:{value:a.queryForm.check_res,callback:function(e){a.$set(a.queryForm,"check_res",e)},expression:"queryForm.check_res"}})],1)],1),t("div",{staticClass:"tableContent",staticStyle:{width:"97%","margin-left":"10px","margin-top":"5px"}},[t("ta-big-table",{ref:"xTable",staticStyle:{width:"100%",cursor:"pointer"},attrs:{border:"","highlight-hover-row":"","show-overflow":"",data:a.filterPatientObjects,height:"auto",resizable:!0,"auto-resize":"",size:"mini","empty-text":"暂无数据",width:"min-width","expand-config":{iconOpen:"caret-down",iconClose:"caret-right"}},on:{"cell-click":a.cellClickEvent,"toggle-row-expand":a.toggleExpandChangeEvent}},[t("ta-big-table-column",{attrs:{type:"expand",visible:!0,width:"35"},scopedSlots:a._u([{key:"content",fn:function(e){var i=e.row;return[t("ta-big-table",{ref:"dTablechildren",attrs:{data:i.subProject,border:"","highlight-hover-row":"","show-overflow":"","auto-resize":"","empty-text":"-","header-cell-style":a.headerCellStyle,size:"mini",resizable:!0}},[t("ta-big-table-column",{attrs:{width:"40px",title:" ",type:"seq",align:"center"}}),t("ta-big-table-column",{attrs:{width:"70px"},scopedSlots:a._u([{key:"default",fn:function(e){var i=e.row;return[t("div",{staticStyle:{display:"flex","align-items":"center","justify-content":"space-around"}},[t("div",{directives:[{name:"show",rawName:"v-show",value:i.aka065,expression:"row.aka065"}],style:{background:"甲"===i.aka065?"#67c23a":"乙"===i.aka065?"#e6a23c":"#ff0000",color:"#fff",padding:"0px 5px"}},[a._v(" "+a._s(i.aka065)+" ")]),t("div",{directives:[{name:"show",rawName:"v-show",value:i.ape800,expression:"row.ape800"}],style:{background:"1"==i.ape800?"#e6a23c":"2"==i.ape800?"#ff0000":"#EECD08",color:"#fff",padding:"0px 5px",marginLeft:"5px"}},[a._v(" "+a._s("1"===i.ape800?"2":"3"===i.ape800?"3":"1")+" ")])])]}}],null,!0)}),t("ta-big-table-column",{attrs:{field:"ake006","min-width":"120px",align:"left","header-align":"center",title:"项目名称"}}),t("ta-big-table-column",{attrs:{field:"ykz259","min-width":"120px",align:"left","header-align":"center",title:"限制条件"}}),t("ta-big-table-column",{attrs:{field:"aae036",align:"center","min-width":"180px",title:"发生时间"}}),t("ta-big-table-column",{attrs:{field:"akc226",align:"right","header-align":"center","min-width":"50px",title:"数量"},scopedSlots:a._u([{key:"default",fn:function(e){var i=e.row;return[i.akc226?t("span",[a._v(a._s(i.akc226))]):t("span",[a._v("--")])]}}],null,!0)}),t("ta-big-table-column",{attrs:{field:"fail",align:"center","min-width":"90px",title:"审核详情"},scopedSlots:a._u([{key:"default",fn:function(e){var i=e.row;return[t("span",{staticStyle:{color:"#276cf5"},on:{click:function(e){return a.openAuditDetails(i)}}},[a._v(a._s("0"===i.fail?"违规详情":"合规详情"))])]}}],null,!0)}),t("ta-big-table-column",{attrs:{field:"akc049",align:"center","min-width":"90px",title:"开嘱医生"}}),t("ta-big-table-column",{attrs:{field:"aae386",align:"left","min-width":"190px","header-align":"center",title:"开单科室"}}),t("ta-big-table-column",{attrs:{field:"oldape893","min-width":"90px",align:"center","header-align":"center",title:"操作历史"},scopedSlots:a._u([{key:"default",fn:function(e){var i=e.row;return[t("span",{staticStyle:{color:"#276cf5"},on:{click:function(e){return a.openHistoryModal(i)}}},[a._v(a._s("1"===i.oldape893?"备案":"2"===i.oldape893?"自费":"3"===i.oldape893?"取消/置换":""))])]}}],null,!0)}),t("ta-big-table-column",{attrs:{field:"op",align:"center",title:"操作",fixed:"right","min-width":"200px"},scopedSlots:a._u([{key:"default",fn:function(e){var i=e.row;return[t("div",{directives:[{name:"show",rawName:"v-show",value:"ipt"===a.scene||"0"===i.fail,expression:" scene === 'ipt' || row.fail==='0' "}],staticStyle:{display:"flex","justify-content":"center"}},a._l(a.oprateBtn,(function(e,n){return t("div",{key:n},[t("ta-button",{attrs:{type:i.currentOprate==n?"primary":"dashed",size:"small"},on:{click:function(e){return a.oprateEvent(i,n)}}},[a._v(a._s(e.name))])],1)})),0)]}}],null,!0)})],1)]}}])}),t("ta-big-table-column",{attrs:{width:"40px",title:" ",type:"seq",fixed:"left",align:"center"}}),t("ta-big-table-column",{attrs:{width:"70px"},scopedSlots:a._u([{key:"default",fn:function(e){var i=e.row;return[t("div",{staticStyle:{display:"flex","align-items":"center","justify-content":"space-around"}},[t("div",{directives:[{name:"show",rawName:"v-show",value:i.aka065,expression:"row.aka065"}],style:{background:"甲"===i.aka065?"#67c23a":"乙"===i.aka065?"#e6a23c":"#ff0000",color:"#fff",padding:"0px 5px"}},[a._v(" "+a._s(i.aka065)+" ")]),t("div",{directives:[{name:"show",rawName:"v-show",value:i.ape800,expression:"row.ape800"}],style:{background:"1"==i.ape800?"#e6a23c":"2"==i.ape800?"#ff0000":"#EECD08",color:"#fff",padding:"0px 5px",marginLeft:"5px"}},[a._v(" "+a._s("1"===i.ape800?"2":"3"===i.ape800?"3":"1")+" ")])])]}}])}),t("ta-big-table-column",{attrs:{field:"ake006",title:"项目名称","header-align":"center",align:"left","min-width":"120px"}}),t("ta-big-table-column",{attrs:{field:"ykz259",title:"限制条件","header-align":"center",align:"left","min-width":"180px"}}),t("ta-big-table-column",{attrs:{field:"akc226",title:"数量",align:"right","header-align":"center",width:"50px"}}),t("ta-big-table-column",{attrs:{field:"akc225",title:"单价","header-align":"center",align:"right",width:"60px",formatter:"formatAmount"}}),t("ta-big-table-column",{attrs:{field:"aaz560",title:"备案信息","header-align":"center",align:"left","min-width":"90px"}}),t("ta-big-table-column",{attrs:{field:"op",title:"操作",fixed:"right","min-width":"200px",align:"center"},scopedSlots:a._u([{key:"default",fn:function(e){var i=e.row;return[t("div",{directives:[{name:"show",rawName:"v-show",value:"ipt"===a.scene||"0"===i.fail,expression:" scene === 'ipt' || row.fail==='0' "}],staticStyle:{display:"flex","justify-content":"center"}},a._l(a.oprateBtn,(function(e,n){return t("div",{key:n},[t("ta-button",{attrs:{type:i.currentOprate==n?"primary":"dashed",size:"small"},on:{click:function(e){return a.oprateEvent(i,n)}}},[a._v(a._s(e.name))])],1)})),0)]}}])})],1)],1),"2"!==a.roleMOCK?t("div",{staticClass:"saftBox"}):a._e(),"2"!==a.roleMOCK?t("div",{staticClass:"submitBtn"},[t("ta-button",{attrs:{type:"primary",disabled:!a.patientObjects.length},on:{click:a.handleSubmitData}},[a._v("提交")])],1):a._e()])]),t("limitModal",{attrs:{visible:a.limitModalVisible},on:{handleOk:a.handleOk,handleCancel:a.handleCancel}}),t("medicineSearch",{attrs:{visible:a.medicineSearchVisible},on:{cancel:a.cancelMedicineModal}}),t("keep-on-record",{attrs:{visible:a.keepOnRecordVisible,params:a.rowData},on:{handleSave:a.handleUpdateRow,handleClose:a.handleClose}}),t("bill-audit",{attrs:{visible:a.billVisible,"bill-data":a.billData},on:{"update:billData":function(e){a.billData=e},"update:bill-data":function(e){a.billData=e},handleSave:a.billSave,handleClose:a.billClose}}),t("div",[t("ta-modal",{attrs:{title:"操作历史",width:"45%",visible:a.oprateHistoryVisible},on:{ok:a.handleHisOk,cancel:a.handleHisCancel}},[t("template",{slot:"footer"},[t("div",{staticStyle:{display:"flex","justify-content":"center"}},[t("ta-button",{on:{click:a.handleHisCancel}},[a._v("取消")]),t("ta-button",{attrs:{type:"primary"},on:{click:a.handleHisOk}},[a._v(" 确定 ")])],1)]),t("ta-big-table",{attrs:{border:"","highlight-hover-row":"",height:"300","auto-resize":"",data:a.oprateHistoryList}},[t("ta-big-table-column",{attrs:{type:"seq",align:"center",title:"",width:"60"}}),t("ta-big-table-column",{attrs:{field:"ape893",title:"操作","header-align":"center",align:"center",width:"80"},scopedSlots:a._u([{key:"default",fn:function(e){var i=e.row;return[t("span",[a._v(a._s("1"==i.ape893?"备案":"自费"))])]}}])}),t("ta-big-table-column",{attrs:{field:"aaz560","header-align":"center",align:"left",title:"操作理由"}}),t("ta-big-table-column",{attrs:{field:"aae040",align:"left","header-align":"center",title:"操作时间",width:"160"}}),t("ta-big-table-column",{attrs:{field:"ykz041",align:"center",title:"操作人",width:"80"}})],1)],2)],1),t("div",{attrs:{id:"info"}},[t("ta-modal",{staticStyle:{top:"10px"},attrs:{width:"95%","destroy-on-close":!0,footer:null,wrapClassName:"inp-modal-wrap"},on:{cancel:a.handleAuditCancel},model:{value:a.visible,callback:function(e){a.visible=e},expression:"visible"}},[t("ta-icon",{staticStyle:{color:"black"},attrs:{slot:"closeIcon",type:"close"},slot:"closeIcon"}),t("span",{staticStyle:{"font-weight":"normal"},attrs:{slot:"title"},slot:"title"},[a._v(" 审核详情")]),t("atient-details",{attrs:{fyRecord:a.bfRecord}})],1)],1)],1)},n=[],r=t(48534),l=t(66347),c=t(89584),s=(t(36133),t(32564),t(88412)),o=function(){var a=this,e=a.$createElement,t=a._self._c||e;return t("div",[t("ta-modal",{attrs:{title:"权限授予",visible:a.visible,"max-height":550,width:1e3},on:{cancel:a.handleCancel}},[t("template",{slot:"footer"},[t("div",{staticStyle:{display:"flex","justify-content":"center"}},[t("ta-button",{on:{click:a.handleCancel}},[a._v("关闭")])],1)]),t("div",{staticClass:"container"},[t("div",[a._l(a.authorityList,(function(e,i){return t("div",{key:i,staticStyle:{display:"flex","align-items":"center","justify-content":"space-around",margin:"5px 0"}},[t("span",{staticStyle:{margin:"0 5px"}},[a._v("将")]),t("ta-select",{staticStyle:{width:"30%"},attrs:{placeholder:"请选择授权科室",mode:"multiple",allowClear:"",options:a.currentUserDepart},model:{value:e.authDepart,callback:function(t){a.$set(e,"authDepart",t)},expression:"limit.authDepart"}}),t("span",{staticStyle:{margin:"0 5px"}},[a._v("的患者后台审核权限授予：")]),t("ta-select",{staticStyle:{width:"30%"},attrs:{showSearch:"",searchDelay:3e3,allowClear:"",placeholder:"请选择授予人员",optionLabelProp:"all",options:a.currentUser},on:{search:a.handleSearch},model:{value:e.commissionedId,callback:function(t){a.$set(e,"commissionedId",t)},expression:"limit.commissionedId"}}),t("ta-icon",{staticStyle:{"margin-left":"10px"},attrs:{type:"minus-circle"},on:{click:function(e){return a.deleteItem(i)}}})],1)})),t("ta-button",{staticStyle:{width:"100%","margin-top":"10px"},attrs:{type:"dashed"},on:{click:a.addform}},[t("ta-icon",{attrs:{type:"plus"}})],1)],2),t("div",{staticStyle:{margin:"10px 0"}},[a._v("权限授予历史记录: "),t("ta-button",{staticStyle:{float:"right"},attrs:{type:"primary"},on:{click:a.selectItem}},[a._v(" 取消授权")]),t("ta-button",{staticStyle:{float:"right"},attrs:{type:"primary"},on:{click:a.handleOk}},[a._v(" 新增授权")])],1),t("div",{staticStyle:{height:"300px"}},[t("ta-big-table",{ref:"xTable",staticStyle:{cursor:"pointer"},attrs:{border:"",stripe:"",size:"medium","checkbox-config":{trigger:"row",checkMethod:function(a){return"1"===a.row.effective}},resizable:"","auto-resize":"","highlight-hover-row":"",height:"auto",data:a.tableData}},[t("ta-big-table-column",{attrs:{type:"checkbox",fixed:"left",width:"40px",align:"center"}}),t("ta-big-table-column",{attrs:{title:" ",type:"seq","min-width":"60",align:"center"}}),t("ta-big-table-column",{attrs:{field:"createTime","min-width":"180",title:"开始时间",align:"center"}}),t("ta-big-table-column",{attrs:{field:"expireTime","min-width":"180",title:"结束时间",align:"center"}}),t("ta-big-table-column",{attrs:{field:"authDepartmentName",title:"科室",align:"center","min-width":"120"}}),t("ta-big-table-column",{attrs:{field:"commissionedName",title:"被授权人","min-width":"80",align:"center"}}),t("ta-big-table-column",{attrs:{title:"授权状态","min-width":"80",align:"center"},scopedSlots:a._u([{key:"default",fn:function(e){var t=e.row;return[a._v(" "+a._s("1"===t.effective?"已授权":"已取消授权")+" ")]}}])})],1)],1)])],2)],1)},u=[],d={name:"limitModal",props:{visible:{type:Boolean,default:!1},params:{type:Object,default:function(){return{}}}},data:function(){return{currentUserDepart:[],currentUser:[],authorityList:[],tableData:[]}},mounted:function(){this.getHistorLimit()},methods:{handleSearch:function(a){this.getLimitPerson(a)},getHistorLimit:function(){var a=this;this.tableData=[];var e={url:"/auditOnBackend/getAuthHistoryList",autoValid:!0},t={successCallback:function(e){a.tableData=e.data.data},failCallback:function(e){a.$message.error("数据加载失败")}};this.Base.submit(null,e,t)},handleOk:function(){this.$emit("handleOk"),this.authorityList=this.authorityList.reduce((function(a,e){return a.find((function(a){return a.authDepartment===e.authDepartment&&a.commissionedId===e.commissionedId}))||a.push(e),a}),[]).filter((function(a){return 0!==a.authDepart.length||""!==a.commissionedId})),0!==this.authorityList.length?this.giveLimit():this.$message.error("还未填写数据")},handleCancel:function(){this.$emit("handleCancel")},addform:function(){this.authorityList.length||this.currentUserDepart.length||(this.getCurentDepartMsg(),this.getLimitPerson()),this.authorityList.push({authDepart:[],authDepartment:"",commissionedId:void 0})},deleteItem:function(a){this.authorityList=this.authorityList.filter((function(e,t){return t!==a}))},giveLimit:function(){var a=this,e=!0;if(this.authorityList.forEach((function(t){if(0==t.authDepart.length||!t.commissionedId)return a.$message.error("请先完成填写"),void(e=!1);var i="";t.authDepart.forEach((function(a){i=i?"".concat(i,",").concat(a):"".concat(a)})),t.authDepartment=i})),e){var t={url:"/auditOnBackend/authToDoctor",autoValid:!0,autoQs:!1,data:{authorityList:this.authorityList}},i={successCallback:function(e){a.authorityList=[],a.getHistorLimit()}};this.Base.submit(null,t,i)}},selectItem:function(){var a=this,e=this.$refs.xTable.getCheckboxRecords().map((function(a){return a.id}));if(0!==e.length){var t={url:"/auditOnBackend/cancelAuthority",autoValid:!0,data:{id:JSON.stringify(e)}},i={successCallback:function(e){a.getHistorLimit()}};this.Base.submit(null,t,i)}else this.$message.warning("请选择需要取消授权的数据")},getLimitPerson:function(a){var e=this,t={url:"/miimCommonRead/queryDoctorRpcList",autoValid:!0,data:{doctorNo:a}},i={successCallback:function(a){e.currentUser=a.data.resultData}};this.Base.submit(null,t,i)},getCurentDepartMsg:function(){var a=this,e={url:"/auditOnBackend/getCurrentUserDptList",autoValid:!0},t={successCallback:function(e){e.data.data.forEach((function(e){a.currentUserDepart.push(e)}))}};this.Base.submit(null,e,t)}}},h=d,p=t(1001),k=(0,p.Z)(h,o,u,!1,null,"2a824df7",null),f=k.exports,m=function(){var a=this,e=a.$createElement,t=a._self._c||e;return t("div",[t("ta-modal",{attrs:{width:"65%",footer:null,title:"靶向药用药历史记录查询",visible:a.visible},on:{cancel:a.cancel}},[t("div",[t("ta-input-search",{staticStyle:{width:"40%"},attrs:{placeholder:"请输入患者身份证号码",enterButton:""},on:{search:a.onSearch}}),t("div",{staticStyle:{margin:"10px 0"}},[a._v("院内记录：")]),t("ta-big-table",{attrs:{border:"",stripe:"",resizable:"","auto-resize":"","highlight-hover-row":"",height:"40%",data:a.tableData}},[t("ta-big-table-column",{attrs:{type:"seq",width:"40",title:" ",align:"center"}}),t("ta-big-table-column",{attrs:{field:"name","min-width":"180",title:"医保项目名称",align:"center"}}),t("ta-big-table-column",{attrs:{field:"code","min-width":"200",title:"医保项目编码",align:"center"}}),t("ta-big-table-column",{attrs:{field:"num",title:"数量","min-width":"60",align:"center"}}),t("ta-big-table-column",{attrs:{field:"price",title:"单价","min-width":"60",align:"center"}}),t("ta-big-table-column",{attrs:{field:"startTime","min-width":"160",title:"费用发生时间",align:"center"}}),t("ta-big-table-column",{attrs:{field:"hosptialName",title:"定点医药机构名称","min-width":"160",align:"center"}}),t("ta-big-table-column",{attrs:{field:"doctor",title:"开单医生","min-width":"80",align:"center"}})],1),t("div",{staticStyle:{margin:"10px 0"}},[a._v("其他记录：")]),t("ta-big-table",{attrs:{border:"",stripe:"",resizable:"","auto-resize":"","highlight-hover-row":"",height:"40%",data:a.tableData}},[t("ta-big-table-column",{attrs:{type:"seq",width:"40",title:" ",align:"center"}}),t("ta-big-table-column",{attrs:{field:"name","min-width":"180",title:"医保项目名称",align:"center"}}),t("ta-big-table-column",{attrs:{field:"code","min-width":"200",title:"医保项目编码",align:"center"}}),t("ta-big-table-column",{attrs:{field:"num",title:"数量","min-width":"40",align:"center"}}),t("ta-big-table-column",{attrs:{field:"price",title:"单价","min-width":"40",align:"center"}}),t("ta-big-table-column",{attrs:{field:"startTime","min-width":"160",title:"费用发生时间",align:"center"}}),t("ta-big-table-column",{attrs:{field:"hosptialName",title:"定点医药机构名称","min-width":"160",align:"center"}})],1)],1)])],1)},b=[],g={name:"medicineSearch",props:{visible:{type:Boolean,default:!1}},data:function(){return{tableData:[{name:"甲磺酸阿帕替尼片",code:"XL01XEA288A001010101445",num:"2",price:"1.25",startTime:"2023-04-13 10:51:50",hosptialName:"哈尔滨医科大学附属肿瘤医院",doctor:"朱晶晶"}]}},methods:{onSearch:function(){},cancel:function(){this.$emit("cancel")}}},y=g,z=(0,p.Z)(y,m,b,!1,null,null,null),v=z.exports,w=function(){var a=this,e=this,t=e.$createElement,i=e._self._c||t;return i("div",[i("ta-modal",{attrs:{title:"备案理由",visible:e.visible,height:550,width:1e3,"mask-closable":!1},on:{ok:e.handleSave,cancel:e.handleClose}},[i("div",{staticStyle:{display:"flex","justify-content":"center"},attrs:{slot:"footer"},slot:"footer"},[i("ta-button",{style:{marginRight:8},attrs:{type:"primary"},on:{click:e.handleSave}},[e._v(" 保存 ")]),i("ta-button",{on:{click:e.handleClose}},[e._v(" 取消 ")]),i("ta-button",{on:{click:e.saveASTemp}},[e._v(" 保存为备案理由 ")])],1),i("ta-menu",{staticStyle:{"margin-bottom":"10px"},attrs:{mode:"horizontal"},model:{value:e.current,callback:function(a){e.current=a},expression:"current"}},[i("ta-menu-item",{key:"record",on:{click:e.queryRecords}},[e._v(" 历史备案记录 ")]),i("ta-menu-item",{key:"reason",on:{click:e.queryTemplateDatas}},[e._v(" 备案理由模板 ")])],1),i("ta-big-table",{directives:[{name:"show",rawName:"v-show",value:"record"===e.current[0],expression:"current[0] === 'record'"}],staticStyle:{width:"100%"},attrs:{border:"","highlight-hover-row":"","show-overflow":"",data:e.dataSource1,height:"300",resizable:!0,"auto-resize":"",size:"mini"}},[i("ta-big-table-column",{attrs:{type:"seq",fixed:"left",width:"50px",title:" ",align:"center"}}),i("ta-big-table-column",{attrs:{field:"aaz213","header-align":"center",title:"管理Id",visible:!1}}),i("ta-big-table-column",{attrs:{field:"aae040",title:"备案时间","header-align":"center","min-width":"100px"}}),i("ta-big-table-column",{attrs:{field:"ykz041","header-align":"center",align:"center\n      ",title:"医生名字","min-width":"60px"}}),i("ta-big-table-column",{attrs:{field:"aaz560",title:"备案理由","header-align":"center","min-width":"100px"}}),i("ta-big-table-column",{attrs:{field:"",title:"操作",width:"100px",align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){var t=a.row;return[i("div",{staticClass:"opareteItem",on:{click:function(a){return e.useRowInfo(t,"history")}}},[e._v("使用")])]}}])})],1),i("ta-big-table",{directives:[{name:"show",rawName:"v-show",value:"reason"===e.current[0],expression:"current[0] === 'reason'"}],staticStyle:{width:"100%"},attrs:{border:"","highlight-hover-row":"","show-overflow":"",data:e.dataSource2,height:"300",resizable:!0,"auto-resize":"",size:"mini"}},[i("ta-big-table-column",{attrs:{type:"seq",fixed:"left",width:"50px",title:" ",align:"center"}}),i("ta-big-table-column",{attrs:{field:"aaz213",title:"管理Id",visible:!1}}),i("ta-big-table-column",{attrs:{field:"id",title:"Id",visible:!1}}),i("ta-big-table-column",{attrs:{field:"content",title:"备案理由","header-align":"center","min-width":"500px"}}),i("ta-big-table-column",{attrs:{field:"type",title:"类型","header-align":"center","min-width":"80px",align:"center",formatter:e.formatterType}}),i("ta-big-table-column",{attrs:{field:"",title:"操作",width:"130px",align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return[i("ta-table-operate",{attrs:{"operate-menu":e.operateMenu,"row-info":a}})]}}])})],1),i("ta-form",{attrs:{"auto-form-create":function(e){return a.form=e},layout:"horizontal","form-layout":!0,"label-width":"80px",col:{xs:1,sm:1,md:1,lg:1,xl:1,xxl:1}}},[i("ta-form-item",{attrs:{label:"引导信息","field-decorator-id":"ykz018",disabled:!0}},[i("ta-textarea",{attrs:{placeholder:"请输入引导信息",rows:3,"show-length":!0}})],1),i("ta-form-item",{attrs:{label:"备案理由","field-decorator-id":"content",require:!0,"field-decorator-options":{rules:[{validator:e.validReason}]},initValue:e.changeAddContent}},[i("ta-textarea",{attrs:{placeholder:"请输入内容；不能为空；不能全是特殊字符、符号、数字或三者组合；最大长度不能超过400各字符",rows:3,"show-length":!0}})],1)],1)],1),i("ta-modal",{attrs:{title:"备案理由模板编辑",visible:e.editTempVisible},on:{ok:e.editContent,cancel:e.handleEditClose}},[i("ta-form",{attrs:{"auto-form-create":function(e){return a.formEdit=e},layout:"horizontal","form-layout":!0,"label-width":"80px",col:{xs:1,sm:1,md:1,lg:1,xl:1,xxl:1}}},[i("ta-form-item",{attrs:{fieldDecoratorId:"id",hidden:"true",initValue:e.changeId}},[i("ta-input")],1),i("ta-form-item",{attrs:{"field-decorator-id":"editContent",label:"修改内容",require:!0,"field-decorator-options":{rules:[{validator:e.validReason}]},initValue:e.changeContent}},[i("ta-textarea",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入内容；不能为空；不能全是特殊字符、符号、数字或三者组合；最大长度不能超过400各字符",rows:3,"show-length":!0}})],1)],1)],1)],1)},x=[],C="nightAudit/",_={getBasePath:function(){return faceConfig.basePath+C},queryTableData:function(a,e){Base.submit(null,{url:C+"queryPatients",data:a,autoQs:!1},{successCallback:function(a){return e(a)}})},updatePatientInfo:function(a,e){Base.submit(null,{url:C+"saveOperation",data:a,autoQs:!1},{successCallback:function(a){return e(a)}})},getRowDetails:function(a,e){Base.submit(null,{url:C+"queryAdviceDetail",data:a,autoQs:!1},{successCallback:function(a){return e(a)}})},queryKeepRecords:function(a,e){Base.submit(null,{url:"auditOnBackend/getOprHistory",data:a},{successCallback:function(a){return e(a)}})},queryTemplateDatas:function(a,e){Base.submit(null,{url:C+"getRecordFormwork",data:a},{successCallback:function(a){return e(a)}})},saveAe21:function(a,e){Base.submit(null,{url:C+"saveAe21",data:a,autoQs:!1},{successCallback:function(a){return e(a)}})},queryRuleInfo:function(a,e){Base.submit(null,{url:"/mtt/api/ruleSearch/queryRule",data:a,autoQs:!1},{successCallback:function(a){return e(a)}})},queryPagePackageRule:function(a,e){Base.submit(null,{url:"/mtt/api/ruleSearch/pagePackageContent",data:a,autoQs:!1},{successCallback:function(a){return e(a)}})}},D=t(36797),S=t.n(D),A={name:"keepOnRecord",components:{},props:{visible:{type:Boolean,default:!1},params:{type:Object,default:function(){return{}}}},data:function(){var a=this;return{current:["record"],dataSource1:[],dataSource2:[],editTempVisible:!1,changeAddContent:null,changeContent:null,changeId:null,operateMenu:[{name:"使用",onClick:function(e){a.useRowInfo(e,"beian")}},{name:"编辑",isShow:function(a){return"3"===a.type},onClick:function(e){a.editTempVisible=!0,a.changeContent=e.content,a.changeId=e.id}},{name:"删除",isShow:function(a){return"3"===a.type},onClick:function(e){a.$confirm({title:"继续操作",content:"删除不可恢复，是否继续删除?",onOk:function(){a.Base.submit(null,{url:"/nightAudit/deleteAe21",data:{id:e.id,medinscode:a.params.fixmedinsCode}}).then((function(e){a.warnText="删除成功!",a.warnVisible=!0,a.dataSource2=e.data.data})).catch((function(e){a.warnText="删除失败!",a.warnVisible=!0})),a.warnVisible=!0},onCancel:function(){}})}}]}},watch:{visible:{handler:function(a){var e=this;this.$nextTick((function(){e.queryRecords(),e.form.setFieldsValue({ykz018:e.params.ykz018,content:e.params.aaz560})}))}}},mounted:function(){this.changeAddContent=top.indexTool.getUserInfo().jobNumber},methods:{moment:S(),validReason:function(a,e,t){var i=this,n=/[^`~!@#$%^&*()_\-+=<>?:"{}|,.\/;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘'，。、\w\r\n\s]/g;e?e.match(n)?parseInt(e)<=0?t([{message:"备案理由的长度不能超过400"}]):(this.$nextTick((function(){i.form.setFieldsValue({duration:parseInt(e)})})),t()):t([{message:"备案理由不能为空；不能全是特殊字符、符号、数字或三者组合"}]):t([{message:"请输入备案理由"}])},queryRecords:function(){var a=this,e={aaz217:this.params.aaz217,currMdtrtId:this.params.currMdtrtId,ake001:this.params.ake001,scene:this.params.scene};_.queryKeepRecords(e,(function(e){a.dataSource1=e.data.data,a.dataSource1&&(a.dataSource1=a.dataSource1.filter((function(a){return"1"===a.ape893})))}))},queryTemplateDatas:function(){var a=this,e={aaz307:this.params.admDeptCodg,akb020:this.params.fixmedinsCode};_.queryTemplateDatas(e,(function(e){a.dataSource2=e.data.data}))},useRowInfo:function(a,e){"history"==e?this.form.setFieldsValue({content:a.aaz560}):this.form.setFieldsValue(a)},handleSave:function(){var a=this;this.form.validateFields((function(e){e||(a.$emit("handleSave",a.form.getFieldsValue()),a.form.resetFields())}))},editContent:function(){var a=this;this.formEdit.validateFields((function(e){e||(a.Base.submit(null,{url:"/nightAudit/updateAe21",data:{content:a.formEdit.getFieldsValue().editContent,id:a.formEdit.getFieldsValue().id,medinscode:a.params.fixmedinsCode,oprcode:a.params.drCodg}}).then((function(e){a.warnText="更新成功!",a.warnVisible=!0,a.dataSource2=e.data.data,a.formEdit.resetFields(),a.editTempVisible=!1})).catch((function(e){a.warnText="更新失败!",a.warnVisible=!0})),a.warnVisible=!0)}))},handleClose:function(){this.current=["record"],this.form.resetFields(),this.$emit("handleClose")},handleEditClose:function(){this.formEdit.resetFields(),this.editTempVisible=!1},saveASTemp:function(){var a=this;this.form.validateFields((function(e){if(!e){var t={medinscode:a.params.fixmedinsCode,content:a.form.getFieldsValue().content,type:"3"};_.saveAe21(t,(function(e){a.dataSource2=e.data.data}))}}))},formatterType:function(a){var e=a.cellValue;return"1"===e?"公共模板":"2"===e?"科室模板":"个人模板"}}},O=A,B=(0,p.Z)(O,w,x,!1,null,"1c6f71b8",null),T=B.exports,P=t(362),I={aaz217:"825530808165385177",ae03List:[{aae100:"1",aae500:"4",code:"2",name:"检查",showalone:"1"},{aae100:"1",aae500:"4",code:"4",name:"服务",showalone:"0"},{aae100:"1",aae500:"4",code:"5",name:"杂项",showalone:"0"},{aae100:"1",aae500:"4",code:"1",name:"药品1",showalone:"1"},{aae100:"1",aae500:"4",code:"3",name:"耗材",showalone:"0"}],nonReimbursableMap:[{aaa167:"违反阶梯用药约束（可疑）",aka063:"1",aka065:"乙",akb065:0,akc225:8,akc226:0,ake001:"XA02BCA084A012010100453",ake002:"艾普拉唑肠溶片",ake003:"1",ake006:"艾普拉唑肠溶片",ape800:"1",ape804:0,ape805:4,ape893:"2",fail:"0",selfPayCheck:"1",sortId:"0",subProject:[{aaa167:"违反阶梯用药约束（可疑）",aac003:"白世成",aae036:"2023-04-08 18: 36: 15",aae386:"泌尿外科病区",aaz213:"825530808165385186",aaz263:"1809",aaz307:"210",aaz319:0x27147114878760,aka063:"1",akb065:12.8,akc049:"白世成",akc220:"221332367",akc225:8,akc226:2,akc310:"221332367",akc515:"201",ake001:"XA02BCA084A012010100453",ake002:"艾普拉唑肠溶片",ake003:"1",ake006:"艾普拉唑肠溶片",ape800:"1",ape804:12.8,ape805:2,ape893:"2",checkInfo:{aaz213:"825530808165385186",aaz217:"825530808165385177",akb020:"H41030500537",akc190:"10000000744",akc191:"1000000074",engAaz217:"825530808165385177",filterFlag:"1",ykz108:"eng_01_0000_02_05_01"},fail:"0",ykc610:"100000007415",ykz018:"限有十二指肠溃疡、反流性食管炎诊断患者的二线用药",ykz040:"999",ykz041:"admin",ykz259:"限有十二指肠溃疡、反流性食管炎诊断患者的二线用药"},{aaa167:"违反阶梯用药约束（可疑）",aac003:"张栋栋",aae036:"2023-04-08 18: 37: 15",aae386:"眼耳鼻咽喉科",aaz213:"825530808165385179",aaz263:"1952",aaz307:"518",aaz319:0x27147114878760,aka063:"1",akb065:12.8,akc049:"张栋栋",akc220:"221332367",akc225:8,akc226:2,akc310:"221332367",akc515:"201",ake001:"XA02BCA084A012010100453",ake002:"艾普拉唑肠溶片",ake003:"1",ake006:"艾普拉唑肠溶片",ape800:"1",ape804:12.8,ape805:2,ape893:"2",checkInfo:{aaz213:"825530808165385179",aaz217:"825530808165385177",akb020:"H41030500537",akc190:"10000000744",akc191:"1000000074",engAaz217:"825530808165385177",filterFlag:"1",ykz108:"eng_01_0000_02_05_01"},fail:"0",ykc610:"100000007416",ykz018:"限有十二指肠溃疡、反流性食管炎诊断患者的二线用药",ykz040:"999",ykz041:"admin",ykz259:"限有十二指肠溃疡、反流性食管炎诊断患者的二线用药"}],ykz018:"限有十二指肠溃疡、反流性食管炎诊断患者的二线用药",ykz259:"限有十二指肠溃疡、反流性食管炎诊断患者的二线用药"},{aaa167:"违反阶梯用药约束（可疑）",aka063:"2",aka065:"乙",akb065:0,akc225:8,akc226:0,ake001:"XJ01XBD198B014010100651",ake002:"注射用硫酸多黏菌素B2",ake003:"1",ake006:"注射用硫酸多黏菌素B2",ape800:"1",ape804:0,ape805:8,ape893:"2",fail:"0",selfPayCheck:"1",sortId:"0",subProject:[{aaa167:"违反阶梯用药约束（可疑）",aac003:"李帅阳",aae036:"2023-04-08 18: 36: 15",aae386:"泌尿外科病区",aaz213:"825530808165385202",aaz263:"1218",aaz307:"210",aaz319:0x27147114878760,aka063:"2",akb065:12.8,akc049:"李帅阳",akc220:"221332367",akc225:8,akc226:2,akc310:"221332367",akc515:"201",ake001:"XJ01XBD198B014010100651",ake002:"注射用硫酸多黏菌素B2",ake003:"1",ake006:"注射用硫酸多黏菌素B2",ape800:"1",ape804:12.8,ape805:2,ape893:"2",checkInfo:{aaz213:"825530808165385202",aaz217:"825530808165385177",akb020:"H41030500537",akc190:"10000000744",akc191:"1000000074",engAaz217:"825530808165385177",filterFlag:"1",ykz108:"eng_01_0000_02_05_01"},fail:"0",ykc610:"10000000741",ykz018:"限有药敏试验证据支持的多重耐药细菌感染的联合治疗",ykz040:"999",ykz041:"admin",ykz259:"限有药敏试验证据支持的多重耐药细菌感染的联合治疗"},{aaa167:"违反阶梯用药约束（可疑）",aac003:"李帅阳",aae036:"2023-04-08 18: 37: 15",aae386:"泌尿外科病区",aaz213:"825530808165385200",aaz263:"1218",aaz307:"210",aaz319:0x27147114878760,aka063:"2",akb065:12.8,akc049:"李帅阳",akc220:"221332367",akc225:8,akc226:2,akc310:"221332367",akc515:"201",ake001:"XJ01XBD198B014010100651",ake002:"注射用硫酸多黏菌素B2",ake003:"1",ake006:"注射用硫酸多黏菌素B2",ape800:"1",ape804:12.8,ape805:2,ape893:"2",checkInfo:{aaz213:"825530808165385200",aaz217:"825530808165385177",akb020:"H41030500537",akc190:"10000000744",akc191:"1000000074",engAaz217:"825530808165385177",filterFlag:"1",ykz108:"eng_01_0000_02_05_01"},fail:"0",ykc610:"10000000742",ykz018:"限有药敏试验证据支持的多重耐药细菌感染的联合治疗",ykz040:"999",ykz041:"admin",ykz259:"限有药敏试验证据支持的多重耐药细菌感染的联合治疗"},{aaa167:"违反阶梯用药约束（可疑）",aac003:"张栋栋",aae036:"2023-04-08 18: 38: 15",aae386:"眼耳鼻咽喉科",aaz213:"825530808165385203",aaz263:"1952",aaz307:"518",aaz319:0x27147114878760,aka063:"2",akb065:12.8,akc049:"张栋栋",akc220:"221332367",akc225:8,akc226:2,akc310:"221332367",akc515:"201",ake001:"XJ01XBD198B014010100651",ake002:"注射用硫酸多黏菌素B2",ake003:"1",ake006:"注射用硫酸多黏菌素B2",ape800:"1",ape804:12.8,ape805:2,ape893:"2",checkInfo:{aaz213:"825530808165385203",aaz217:"825530808165385177",akb020:"H41030500537",akc190:"10000000744",akc191:"1000000074",engAaz217:"825530808165385177",filterFlag:"1",ykz108:"eng_01_0000_02_05_01"},fail:"0",ykc610:"10000000743",ykz018:"限有药敏试验证据支持的多重耐药细菌感染的联合治疗",ykz040:"999",ykz041:"admin",ykz259:"限有药敏试验证据支持的多重耐药细菌感染的联合治疗"},{aaa167:"违反阶梯用药约束（可疑）",aac003:"张栋栋",aae036:"2023-04-08 18: 39: 15",aae386:"眼耳鼻咽喉科",aaz213:"825530808165385182",aaz263:"1952",aaz307:"518",aaz319:0x27147114878760,aka063:"2",akb065:12.8,akc049:"张栋栋",akc220:"221332367",akc225:8,akc226:2,akc310:"221332367",akc515:"201",ake001:"XJ01XBD198B014010100651",ake002:"注射用硫酸多黏菌素B2",ake003:"1",ake006:"注射用硫酸多黏菌素B2",ape800:"1",ape804:12.8,ape805:2,ape893:"2",checkInfo:{aaz213:"825530808165385182",aaz217:"825530808165385177",akb020:"H41030500537",akc190:"10000000744",akc191:"1000000074",engAaz217:"825530808165385177",filterFlag:"1",ykz108:"eng_01_0000_02_05_01"},fail:"0",ykc610:"10000000744",ykz018:"限有药敏试验证据支持的多重耐药细菌感染的联合治疗",ykz040:"999",ykz041:"admin",ykz259:"限有药敏试验证据支持的多重耐药细菌感染的联合治疗"}],ykz018:"限有药敏试验证据支持的多重耐药细菌感染的联合治疗",ykz259:"限有药敏试验证据支持的多重耐药细菌感染的联合治疗"},{aaa167:"违反药品限制使用条件约束（可疑）",aaz307:"2752",aka063:"11",aka065:"乙",akb065:0,akc225:8,akc226:0,ake001:"XB05AAR021B001060100538",ake002:"人血白蛋白x",ake003:"201",ake006:"人血白蛋白",ape800:"1",ape804:0,ape805:1,ape893:"2",fail:"0",selfPayCheck:"1",sortId:"0",subProject:[{aaa167:"违反药品限制使用条件约束（可疑）",aac003:"白永杰",aae036:"2022-04-06 19: 35: 15",aae386:"神经内科二病区",aaz213:"825530808165385190",aaz263:"0655",aaz307:"2752",aaz319:0x27147114878760,aka063:"11",akb065:8,akc049:"白永杰",akc220:"221332367",akc225:8,akc226:1,akc310:"221332367",akc515:"201",ake001:"XB05AAR021B001060100538",ake002:"人血白蛋白x",ake003:"201",ake006:"人血白蛋白",ape800:"1",ape804:8,ape805:1,ape893:"2",checkInfo:{aaz213:"825530808165385190",aaz217:"825530808165385177",akb020:"H41030500537",akc190:"10000000744",akc191:"1000000074",engAaz217:"825530808165385177",filterFlag:"1",ykz108:"eng_01_0000_02_05_01"},fail:"0",ykc610:"100000007421",ykz018:"限抢救、重症或因肝硬化、癌症引起胸腹水的患者，且白蛋白低于30g/L",ykz040:"999",ykz041:"admin",ykz259:"限抢救、重症或因肝硬化、癌症引起胸腹水的患者，且白蛋白低于30g/L"}],ykz018:"限抢救、重症或因肝硬化、癌症引起胸腹水的患者，且白蛋白低于30g/L",ykz259:"限抢救、重症或因肝硬化、癌症引起胸腹水的患者，且白蛋白低于30g/L"},{aaa167:"违反分解收费约束（违规）",aaz307:"2752",aka063:"11",aka065:"",akb065:0,akc225:8,akc226:0,ake001:"003301000140000-330100014",ake002:"特殊方法气管插管术x",ake003:"201",ake006:"特殊方法气管插管术",ape800:"2",ape804:0,ape805:1,ape893:"2",fail:"0",selfPayCheck:"1",sortId:"0",subProject:[{aaa167:"违反分解收费约束（违规）",aac003:"白永杰",aae036:"2022-04-06 19: 35: 15",aae386:"神经内科二病区",aaz213:"825530808165385184",aaz263:"0655",aaz307:"2752",aaz319:0x27147114878750,aka063:"11",akb065:8,akc049:"白永杰",akc220:"221332367",akc225:8,akc226:1,akc310:"221332367",akc515:"201",ake001:"003301000140000-330100014",ake002:"特殊方法气管插管术x",ake003:"201",ake006:"特殊方法气管插管术",ape800:"2",ape804:8,ape805:1,ape893:"2",checkInfo:{aaz213:"825530808165385184",aaz217:"825530808165385177",akb020:"H41030500537",akc190:"10000000744",akc191:"1000000074",engAaz217:"825530808165385177",filterFlag:"1",ykz108:"eng_01_0000_02_05_01"},fail:"0",ykc610:"100000007423",ykz018:"同一天内同时收取“气管插管术”与“特殊方法气管插管术”，则扣“特殊方法气管插管术”。",ykz040:"999",ykz041:"admin",ykz259:"同一天内同时收取“气管插管术”与“特殊方法气管插管术”，则扣“特殊方法气管插管术”。"}],ykz018:"同一天内同时收取“气管插管术”与“特殊方法气管插管术”，则扣“特殊方法气管插管术”。",ykz259:"同一天内同时收取“气管插管术”与“特殊方法气管插管术”，则扣“特殊方法气管插管术”。"},{aaa167:"",aka063:"1",aka065:"甲",akb065:0,akc225:8,akc226:0,ake001:"XA02AHT018A001020103372",ake002:"碳酸氢钠",ake003:"1",ake006:"碳酸氢钠",ape800:"",ape804:0,ape805:0,ape893:"1",fail:"1",selfPayCheck:"0",sortId:"1",subProject:[{aac003:"张栋栋",aae036:"2023-04-08 18: 36: 15",aae386:"眼耳鼻咽喉科",aaz213:"825530808165385196",aaz263:"1952",aaz307:"518",aka063:"1",akb065:12.8,akc049:"张栋栋",akc220:"221332367",akc225:8,akc226:2,akc310:"221332367",akc515:"201",ake001:"XA02AHT018A001020103372",ake002:"碳酸氢钠",ake003:"1",ake006:"碳酸氢钠",ape804:0,ape805:0,ape893:"1",checkInfo:{aaz213:"825530808165385196",aaz217:"825530808165385177",akb020:"H41030500537",akc190:"10000000744",akc191:"1000000074",engAaz217:"825530808165385177",filterFlag:"1",ykz108:"eng_01_0000_02_05_01"},fail:"1",ykc610:"100000007419",ykz018:"“消化道和代谢方面的药物-治疗胃酸相关类疾病的药物-抗酸药”口服制剂，在同一天内不能同时使用",ykz040:"999",ykz041:"admin"}],ykz018:"“消化道和代谢方面的药物-治疗胃酸相关类疾病的药物-抗酸药”口服制剂，在同一天内不能同时使用",ykz259:"“消化道和代谢方面的药物-治疗胃酸相关类疾病的药物-抗酸药”口服制剂，在同一天内不能同时使用"},{aaa167:"",aka063:"1",aka065:"乙",akb065:0,akc225:8,akc226:0,ake001:"XV03ACD216A006030183949",ake002:"地拉罗司分散片2",ake003:"1",ake006:"地拉罗司分散片2",ape800:"",ape804:0,ape805:0,ape893:"1",fail:"1",selfPayCheck:"0",sortId:"1",subProject:[{aac003:"张栋栋",aae036:"2023-04-08 18: 36: 15",aae386:"眼耳鼻咽喉科",aaz213:"825530808165385180",aaz263:"1952",aaz307:"518",aka063:"1",akb065:12.8,akc049:"张栋栋",akc220:"221332367",akc225:8,akc226:2,akc310:"221332367",akc515:"201",ake001:"XV03ACD216A006030183949",ake002:"地拉罗司分散片2",ake003:"1",ake006:"地拉罗司分散片2",ape804:0,ape805:0,ape893:"1",checkInfo:{aaz213:"825530808165385180",aaz217:"825530808165385177",akb020:"H41030500537",akc190:"10000000744",akc191:"1000000074",engAaz217:"825530808165385177",filterFlag:"1",ykz108:"eng_01_0000_02_05_01"},fail:"1",ykc610:"10000000748",ykz018:"【单行支付药品】需人工判断是否合规。",ykz040:"999",ykz041:"admin"},{aac003:"李帅阳",aae036:"2023-04-08 18: 36: 15",aae386:"泌尿外科病区",aaz213:"825530808165385192",aaz263:"1218",aaz307:"210",aka063:"1",akb065:12.8,akc049:"李帅阳",akc220:"221332367",akc225:8,akc226:2,akc310:"221332367",akc515:"201",ake001:"XV03ACD216A006030183949",ake002:"地拉罗司分散片2",ake003:"1",ake006:"地拉罗司分散片2",ape804:0,ape805:0,ape893:"1",checkInfo:{aaz213:"825530808165385192",aaz217:"825530808165385177",akb020:"H41030500537",akc190:"10000000744",akc191:"1000000074",engAaz217:"825530808165385177",filterFlag:"1",ykz108:"eng_01_0000_02_05_01"},fail:"1",ykc610:"10000000746",ykz018:"【单行支付药品】需人工判断是否合规。",ykz040:"999",ykz041:"admin"},{aac003:"张栋栋",aae036:"2023-04-08 18: 36: 15",aae386:"眼耳鼻咽喉科",aaz213:"825530808165385193",aaz263:"1952",aaz307:"518",aka063:"1",akb065:12.8,akc049:"张栋栋",akc220:"221332367",akc225:8,akc226:2,akc310:"221332367",akc515:"201",ake001:"XV03ACD216A006030183949",ake002:"地拉罗司分散片2",ake003:"1",ake006:"地拉罗司分散片2",ape804:0,ape805:0,ape893:"1",checkInfo:{aaz213:"825530808165385193",aaz217:"825530808165385177",akb020:"H41030500537",akc190:"10000000744",akc191:"1000000074",engAaz217:"825530808165385177",filterFlag:"1",ykz108:"eng_01_0000_02_05_01"},fail:"1",ykc610:"10000000747",ykz018:"【单行支付药品】需人工判断是否合规。",ykz040:"999",ykz041:"admin"},{aac003:"李帅阳",aae036:"2023-04-08 18: 36: 15",aae386:"泌尿外科病区",aaz213:"825530808165385199",aaz263:"1218",aaz307:"210",aka063:"1",akb065:12.8,akc049:"李帅阳",akc220:"221332367",akc225:8,akc226:2,akc310:"221332367",akc515:"201",ake001:"XV03ACD216A006030183949",ake002:"地拉罗司分散片2",ake003:"1",ake006:"地拉罗司分散片2",ape804:0,ape805:0,ape893:"1",checkInfo:{aaz213:"825530808165385199",aaz217:"825530808165385177",akb020:"H41030500537",akc190:"10000000744",akc191:"1000000074",engAaz217:"825530808165385177",filterFlag:"1",ykz108:"eng_01_0000_02_05_01"},fail:"1",ykc610:"10000000745",ykz018:"【单行支付药品】需人工判断是否合规。",ykz040:"999",ykz041:"admin"}],ykz018:"【单行支付药品】需人工判断是否合规。",ykz259:"【单行支付药品】需人工判断是否合规。"},{aaa167:"",aka063:"1",aka065:"乙",akb065:0,akc225:8,akc226:0,ake001:"XM01AXA165E001010109906",ake002:"盐酸氨基葡萄糖胶囊",ake003:"1",ake006:"盐酸氨基葡萄糖胶囊",ape800:"",ape804:0,ape805:0,ape893:"1",fail:"1",selfPayCheck:"0",sortId:"1",subProject:[{aac003:"李帅阳",aae036:"2023-04-08 18: 36: 15",aae386:"泌尿外科病区",aaz213:"825530808165385194",aaz263:"1218",aaz307:"210",aka063:"1",akb065:12.8,akc049:"李帅阳",akc220:"221332367",akc225:8,akc226:2,akc310:"221332367",akc515:"201",ake001:"XM01AXA165E001010109906",ake002:"盐酸氨基葡萄糖胶囊",ake003:"1",ake006:"盐酸氨基葡萄糖胶囊",ape804:0,ape805:0,ape893:"1",checkInfo:{aaz213:"825530808165385194",aaz217:"825530808165385177",akb020:"H41030500537",akc190:"10000000744",akc191:"1000000074",engAaz217:"825530808165385177",filterFlag:"1",ykz108:"eng_01_0000_02_05_01"},fail:"1",ykc610:"100000007413",ykz018:"限工伤保险",ykz040:"999",ykz041:"admin"},{aac003:"张栋栋",aae036:"2023-04-08 18: 37: 15",aae386:"眼耳鼻咽喉科",aaz213:"825530808165385181",aaz263:"1952",aaz307:"518",aka063:"1",akb065:12.8,akc049:"张栋栋",akc220:"221332367",akc225:8,akc226:2,akc310:"221332367",akc515:"201",ake001:"XM01AXA165E001010109906",ake002:"盐酸氨基葡萄糖胶囊",ake003:"1",ake006:"盐酸氨基葡萄糖胶囊",ape804:0,ape805:0,ape893:"1",checkInfo:{aaz213:"825530808165385181",aaz217:"825530808165385177",akb020:"H41030500537",akc190:"10000000744",akc191:"1000000074",engAaz217:"825530808165385177",filterFlag:"1",ykz108:"eng_01_0000_02_05_01"},fail:"1",ykc610:"100000007414",ykz018:"限工伤保险",ykz040:"999",ykz041:"admin"}],ykz018:"限工伤保险",ykz259:"限工伤保险"},{aaa167:"",aka063:"1",aka065:"乙",akb065:0,akc225:8,akc226:0,ake001:"XA12AXX128N001010704670",ake002:"小儿碳酸钙D3颗粒2",ake003:"1",ake006:"小儿碳酸钙D3颗粒2",ape800:"",ape804:0,ape805:0,ape893:"1",fail:"1",selfPayCheck:"0",sortId:"1",subProject:[{aac003:"张栋栋",aae036:"2023-04-08 18: 36: 15",aae386:"眼耳鼻咽喉科",aaz213:"825530808165385183",aaz263:"1952",aaz307:"518",aka063:"1",akb065:12.8,akc049:"张栋栋",akc220:"221332367",akc225:8,akc226:2,akc310:"221332367",akc515:"201",ake001:"XA12AXX128N001010704670",ake002:"小儿碳酸钙D3颗粒2",ake003:"1",ake006:"小儿碳酸钙D3颗粒2",ape804:0,ape805:0,ape893:"1",checkInfo:{aaz213:"825530808165385183",aaz217:"825530808165385177",akb020:"H41030500537",akc190:"10000000744",akc191:"1000000074",engAaz217:"825530808165385177",filterFlag:"1",ykz108:"eng_01_0000_02_05_01"},fail:"1",ykc610:"100000007411",ykz018:"限小儿佝偻病",ykz040:"999",ykz041:"admin"},{aac003:"李帅阳",aae036:"2023-04-08 18: 36: 15",aae386:"泌尿外科病区",aaz213:"825530808165385188",aaz263:"1218",aaz307:"210",aka063:"1",akb065:12.8,akc049:"李帅阳",akc220:"221332367",akc225:8,akc226:2,akc310:"221332367",akc515:"201",ake001:"XA12AXX128N001010704670",ake002:"小儿碳酸钙D3颗粒2",ake003:"1",ake006:"小儿碳酸钙D3颗粒2",ape804:0,ape805:0,ape893:"1",checkInfo:{aaz213:"825530808165385188",aaz217:"825530808165385177",akb020:"H41030500537",akc190:"10000000744",akc191:"1000000074",engAaz217:"825530808165385177",filterFlag:"1",ykz108:"eng_01_0000_02_05_01"},fail:"1",ykc610:"10000000749",ykz018:"限小儿佝偻病",ykz040:"999",ykz041:"admin"},{aac003:"张栋栋",aae036:"2023-04-08 18: 36: 15",aae386:"眼耳鼻咽喉科",aaz213:"825530808165385189",aaz263:"1952",aaz307:"518",aka063:"1",akb065:12.8,akc049:"张栋栋",akc220:"221332367",akc225:8,akc226:2,akc310:"221332367",akc515:"201",ake001:"XA12AXX128N001010704670",ake002:"小儿碳酸钙D3颗粒2",ake003:"1",ake006:"小儿碳酸钙D3颗粒2",ape804:0,ape805:0,ape893:"1",checkInfo:{aaz213:"825530808165385189",aaz217:"825530808165385177",akb020:"H41030500537",akc190:"10000000744",akc191:"1000000074",engAaz217:"825530808165385177",filterFlag:"1",ykz108:"eng_01_0000_02_05_01"},fail:"1",ykc610:"100000007412",ykz018:"限小儿佝偻病",ykz040:"999",ykz041:"admin"},{aac003:"李帅阳",aae036:"2023-04-08 18: 36: 15",aae386:"泌尿外科病区",aaz213:"825530808165385195",aaz263:"1218",aaz307:"210",aka063:"1",akb065:12.8,akc049:"李帅阳",akc220:"221332367",akc225:8,akc226:2,akc310:"221332367",akc515:"201",ake001:"XA12AXX128N001010704670",ake002:"小儿碳酸钙D3颗粒2",ake003:"1",ake006:"小儿碳酸钙D3颗粒2",ape804:0,ape805:0,ape893:"1",checkInfo:{aaz213:"825530808165385195",aaz217:"825530808165385177",akb020:"H41030500537",akc190:"10000000744",akc191:"1000000074",engAaz217:"825530808165385177",filterFlag:"1",ykz108:"eng_01_0000_02_05_01"},fail:"1",ykc610:"100000007410",ykz018:"限小儿佝偻病",ykz040:"999",ykz041:"admin"}],ykz018:"限小儿佝偻病",ykz259:"限小儿佝偻病"},{aaa167:"",aaz307:"2752",aka063:"11",aka065:"",akb065:0,akc225:8,akc226:0,ake001:"003301000130000-330100013",ake002:"气管插管术x",ake003:"201",ake006:"气管插管术x",ape800:"",ape804:0,ape805:0,ape893:"1",fail:"1",selfPayCheck:"0",sortId:"1",subProject:[{aac003:"白永杰",aae036:"2022-04-06 19: 35: 15",aae386:"神经内科二病区",aaz213:"825530808165385191",aaz263:"0655",aaz307:"2752",aka063:"11",akb065:8,akc049:"白永杰",akc220:"221332367",akc225:8,akc226:1,akc310:"221332367",akc515:"201",ake001:"003301000130000-330100013",ake002:"气管插管术x",ake003:"201",ake006:"气管插管术x",ape804:0,ape805:0,ape893:"1",checkInfo:{aaz213:"825530808165385191",aaz217:"825530808165385177",akb020:"H41030500537",akc190:"10000000744",akc191:"1000000074",engAaz217:"825530808165385177",filterFlag:"1",ykz108:"eng_01_0000_02_05_01"},fail:"1",ykc610:"100000007422",ykz018:"全身麻醉含：气管插管。",ykz040:"999",ykz041:"admin"}],ykz018:"全身麻醉含：气管插管。",ykz259:"全身麻醉含：气管插管。"}]},V=I,E=function(){var a=this,e=a.$createElement,t=a._self._c||e;return t("ta-modal",{attrs:{title:"单据审核",visible:a.visible,height:574,width:1e3,"mask-closable":!1,closable:!1,"cancel-button-props":{props:{disabled:!0}}},on:{ok:a.handleSave,cancel:a.handleClose}},[t("ta-big-table",{ref:"patientTable",staticClass:"table-style",staticStyle:{cursor:"pointer"},attrs:{border:"",stripe:"",resizable:"","highlight-hover-row":"","highlight-current-row":"","show-overflow":"",height:"520",size:"mini",data:a.tableData,"empty-text":"暂无数据"}},[t("ta-big-table-column",{attrs:{field:"aaa167","min-width":"120px",align:"left","header-align":"center",title:"审查项"}}),t("ta-big-table-column",{attrs:{field:"ykz018","min-width":"220px",align:"left","header-align":"center",title:"引导信息"}}),t("ta-big-table-column",{attrs:{field:"connotation",align:"center","min-width":"40px",title:"规则内涵"},scopedSlots:a._u([{key:"default",fn:function(e){var i=e.row;return[t("a",{on:{click:function(e){return a.queryRuleInfo(i)}}},[a._v("查看")])]}}])}),t("ta-big-table-column",{attrs:{field:"ykz022",align:"center","min-width":"180px",title:"备案信息","class-name":a.ykz022ClassChoice}}),t("ta-big-table-column",{attrs:{align:"center","min-width":"180px",title:"操作"},scopedSlots:a._u([{key:"default",fn:function(e){var i=e.row;return[t("div",{staticStyle:{display:"flex","justify-content":"center"}},a._l(a.oprateBtn,(function(e,n){return t("div",{key:n},[t("ta-button",{attrs:{type:i.currentOprate===e.key?"primary":"dashed",size:"small"},on:{click:function(t){return a.oprateEvent(i,e.key)}}},[a._v(" "+a._s(e.name)+" ")])],1)})),0)]}}])})],1),t("ta-modal",{attrs:{height:"70%",width:"85%","body-style":{paddingRight:0,paddingTop:"5px"},draggable:!0,"destroy-on-close":!0,footer:null},model:{value:a.knowledgeVisible,callback:function(e){a.knowledgeVisible=e},expression:"knowledgeVisible"}},[t("span",{staticStyle:{"font-weight":"normal","font-size":"14px","text-align":"center",height:"0",width:"100%"},attrs:{slot:"title"},slot:"title"},[a._v(" 规则内涵查看")]),t("div",{staticStyle:{"margin-top":"5px",height:"auto"}},[t("div",{staticClass:"knowledgeTitle"},[a._v(" 限制条件 ")]),t("div",{staticClass:"knowledgeRuleContent"},[a._v(" "+a._s(a.ykz018)+" ")])]),t("div",{staticStyle:{width:"100%",height:"calc( 100% - 50px )","margin-top":"5px"}},[t("div",{staticClass:"knowledgeTitle"},[a._v(" 规则内涵 ")]),t("div",{staticStyle:{display:"inline-block",width:"90%","vertical-align":"top","margin-left":"8px"}},[t("ta-collapse",{attrs:{id:"appH",accordion:!0},on:{change:a.changeNodeActivekey}},a._l(a.nodeList,(function(e,i){return t("ta-collapse-panel",{key:i,staticClass:"knowledgeContentTitle",attrs:{header:a.convertYkz061(e.ykz061)}},[t("div",{staticStyle:{"text-align":"right"}},[t("ta-input-search",{staticStyle:{width:"200px","margin-bottom":"1px"},attrs:{placeholder:"输入关键字搜索","allow-clear":""},on:{search:a.searchfn}})],1),t("div",[t("ta-big-table",{attrs:{size:"small","header-cell-style":function(){return{padding:"2px"}},data:a.nodeData},scopedSlots:a._u([{key:"bottomBar",fn:function(){return[t("ta-pagination",{staticStyle:{"text-align":"right"},attrs:{"show-size-changer":"",size:"small","default-page-size":200,"page-size-options":["30","50","100","200","500"],total:a.total,"data-source":a.nodeData},on:{"update:dataSource":function(e){a.nodeData=e},"update:data-source":function(e){a.nodeData=e},showSizeChange:a.onShowSizeChange,change:a.changePage},model:{value:a.current,callback:function(e){a.current=e},expression:"current"}})]},proxy:!0}],null,!0)},a._l(a.columns,(function(a,e){return t("ta-big-table-column",{key:e,attrs:{field:a.columnField,title:a.columnName}})})),1)],1)])})),1)],1)])])],1)},F=[],M=t(94628),j="nightAudit/",L={getBasePath:function(){return faceConfig.basePath+j},queryTableData:function(a,e){Base.submit(null,{url:j+"queryPatients",data:a,autoQs:!1},{successCallback:function(a){return e(a)}})},queryTableDataForTongJi:function(a,e){Base.submit(null,{url:j+"queryPatientList",data:a,autoQs:!1},{successCallback:function(a){return e(a)}})},updatePatientInfo:function(a,e){Base.submit(null,{url:j+"saveOperation",data:a,autoQs:!1},{successCallback:function(a){return e(a)}})},getRowDetails:function(a,e){Base.submit(null,{url:j+"queryAdviceDetail",data:a,autoQs:!1},{successCallback:function(a){return e(a)}})},queryKeepRecords:function(a,e){Base.submit(null,{url:j+"getRecordFormwork",data:a},{successCallback:function(a){return e(a)}})},queryTemplateDatas:function(a,e){Base.submit(null,{url:j+"getRecordFormwork",data:a},{successCallback:function(a){return e(a)}})},saveAe21:function(a,e){Base.submit(null,{url:j+"saveAe21",data:a,autoQs:!1},{successCallback:function(a){return e(a)}})},queryRuleInfo:function(a,e){Base.submit(null,{url:"/mtt/api/ruleSearch/queryRule",data:a,autoQs:!1},{successCallback:function(a){return e(a)}})},queryPagePackageRule:function(a,e){Base.submit(null,{url:"/mtt/api/ruleSearch/pagePackageContent",data:a,autoQs:!1},{successCallback:function(a){return e(a)}})},checkPromptState:function(a,e){Base.submit(null,{url:j+"checkPromptState",data:a,autoQs:!1},{successCallback:function(a){return e(a)}})},checkHandleState:function(a,e){Base.submit(null,{url:j+"checkHandleState",data:a,autoQs:!1},{successCallback:function(a){return e(a)}})}},q={name:"billAudit",props:{visible:{type:Boolean},billData:{type:Array}},data:function(){return{oprateBtn:[{name:"备案",key:"1"},{name:"取消医保",key:"3"}],tableData:[],knowledgeVisible:!1,ykz018:"",columns:[],nodeList:[],nodeData:[],currYkz022:"",current:0,pagesize:0,total:0}},watch:{visible:function(a){a&&(this.tableData=(0,c.Z)(this.billData))}},methods:{handleSave:function(){var a,e=(0,l.Z)(this.tableData);try{for(e.s();!(a=e.n()).done;){var t=a.value;if(!t.currentOprate)return void message.warn("还有未处理的疑点")}}catch(i){e.e(i)}finally{e.f()}this.tableData.forEach((function(a){a.ape893=a.currentOprate,"3"===a.ape893&&(a.ykz022=null)})),this.$emit("handleSave",this.tableData)},handleClose:function(){},oprateEvent:function(a,e){var t=this,i=this.$createElement;"3"===e&&(a.currentOprate=e),"1"===e&&(this.currYkz022=a.ykz022,this.$confirm({title:"备案理由",content:i("ta-input",{model:{value:t.currYkz022,callback:function(a){t.currYkz022=a}}}),icon:!1,onOk:function(){a.ykz022=t.currYkz022,t.currYkz022="",a.currentOprate=e,t.tableData=(0,c.Z)(t.tableData),t.$forceUpdate()}})),this.tableData=(0,c.Z)(this.tableData),this.$forceUpdate()},convertYkz061:function(a){var e=a.match(/^<(.*)>(.*)<\/\1>$/);return"".concat(e[1],":").concat(e[2])},ykz022ClassChoice:function(a){var e=a.row;return"3"===e.currentOprate?"ykz022-disable":""},queryRuleInfo:function(a){var e=this,t=a.ykz032,i=[];void 0!==t&&(i=t.split(",")),L.queryRuleInfo({ykz032s:i},(function(a){if(!1!==a.serviceSuccess){var t=a.data.resultData.ykz018,i="";if(void 0!==t){for(var n=0;n<t.length;n++)i+="【"+t[n]+"】";e.ykz018=i,e.nodeList=a.data.resultData.nodeInfoList}}else e.$message.error(a.errors[0].msg)})),this.knowledgeVisible=!0},searchfn:function(a,e){this.searchText=a,this.changePage(1,this.pagesize)},changeNodeActivekey:function(a){var e=this;if(!(0,M.Z)(a)){var t=a;if(!(0,M.Z)(t)){var i={};i.ykz042=this.nodeList[t].ykz042,i.pageParam={},i.pageParam.pageNumber=0,i.pageParam.pageSize=10,this.ykz042=i.ykz042,L.queryPagePackageRule(i,(function(a){if(!1!==a.serviceSuccess){e.columns=a.data.resultData.columnInfo;var t=a.data.resultData.pageData;e.nodeData=t.list,e.current=t.pageNum,e.pagesize=t.pageSize,e.total=t.total}else e.$message.error(a.errors[0].msg)}))}}},onShowSizeChange:function(a,e){this.changePage(a,e)},changePage:function(a,e){var t=this,i={};i.ykz042=this.ykz042,i.searchText=this.searchText,i.pageParam={},i.pageParam.pageNumber=a,i.pageParam.pageSize=e,L.queryPagePackageRule(i,(function(a){if(!1!==a.serviceSuccess){t.columns=a.data.resultData.columnInfo;var e=a.data.resultData.pageData;t.nodeData=e.list,t.current=e.pageNum,t.pagesize=e.pageSize,t.total=e.total}else t.$message.error(a.errors[0].msg)}))}}},R=q,H=(0,p.Z)(R,E,F,!1,null,null,null),$=H.exports,X={name:"msAudit",components:{BillAudit:$,TaTitle:s.Z,limitModal:f,keepOnRecord:T,medicineSearch:v,atientDetails:P["default"]},data:function(){var a=(0,c.Z)(V.nonReimbursableMap);return{leftCfg:{title:"",expand:!0,expandText:"",showBar:!0,showBorder:!1,barHeight:"67px",expandCallback:function(a){var e=document.getElementsByClassName("expand")[0].innerHTML;a?(this.barHeight="67px",e='<span > &nbsp;</span><i  aria-label="图标: left" class="anticon anticon-left"><svg viewBox="64 64 896 896" focusable="false" data-icon="left" width="1em" height="1em" fill="currentColor" aria-hidden="true" class=""><path d="M724 218.3V141c0-6.7-7.7-10.4-12.9-6.3L260.3 486.8a31.86 31.86 0 000 50.3l450.8 352.1c5.3 4.1 12.9.4 12.9-6.3v-77.3c0-4.9-2.3-9.6-6.1-12.6l-360-281 360-281.1c3.8-3 6.1-7.7 6.1-12.6z"></path></svg></i>'):(this.barHeight="100%",e='<div style="diplay:flex;flex-direction:column;"><i  aria-label="图标: right" class="anticon anticon-right"><svg viewBox="64 64 896 896" focusable="false" data-icon="right" width="1em" height="1em" fill="currentColor" aria-hidden="true" class=""><path d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"></path></svg></i><div style=\'writing-mode: vertical-rl; text-orientation: mixed;margin-top:5px\'>患者列表</div></div>'),document.getElementsByClassName("expand")[0].innerHTML=e}},defaultActiveKey:"1",dataSource:[{list:[]}],roleMOCK:"0",departList:[{id:0,name:"本科室"},{id:1,name:"所有科室"}],sceneList:[{id:0,name:"普通门诊"},{id:1,name:"门诊特慢病"}],currentscene:0,tipsType:[],tipsOptions:[],tipValue:"",excuteRes:[{label:"违规",value:"0"},{label:"可疑",value:"1"},{label:"通过",value:"2"}],oprateBtn:[{name:"备案",key:1},{name:"自费",key:2}],checkedList:[],limitModalVisible:!1,currentDepart:1,currentTipsIndex:0,patientObjects:[],filterPatientObjects:[],originData:a,ae03List:[],billVisible:!1,billData:[],keepOnRecordVisible:!1,rowData:null,oprateHistoryVisible:!1,oprateHistoryList:[],medicineSearchVisible:!1,visible:!1,bfRecord:null,queryForm:{project_name:"",currentDeaprt:1,fee_type:"",check_res:["0","1","2"],tipValue:"",code:"all"},currentUserDepart:[],aaz217:"",bednoVisible:!1,mdtrtIdTitle:"就诊号",searchVal:"",currMdtrtId:"",scene:"opt",timer:null,hiFeesetlType:"",DepartOptions:[{label:"违规",value:"0"},{label:"可疑",value:"1"},{label:"通过",value:"2"}],dptCode:void 0,fixmedinsCode:"",admDeptCodg:"",drCodg:""}},watch:{queryForm:{deep:!0,handler:function(a,e){this.filterData()}},ae03List:{deep:!0,handler:function(){this.handleTipsData()}},scene:{deep:!0,handler:function(a,e){a&&this.getPatient()}}},methods:{formatterAge:function(a){var e=a.cellValue;if(e){var t=new Date(e),i=new Date,n=i.getFullYear()-t.getFullYear(),r=i.getMonth()>t.getMonth()||i.getMonth()===t.getMonth()&&i.getDate()>=t.getDate();return r||n--,n}},changeDeaprt:function(a){this.getPatient()},searchPatient:function(){var a=this;clearTimeout(this.timer),this.timer=setTimeout((function(){a.getPatient()}),500)},changeTAB:function(a){if(this.defaultActiveKey=a,this.scene="1"==a?"opt":"ipt","1"==a)if("1"==this.roleMOCK){this.mdtrtIdTitle="就诊号";for(var e=0;e<this.$refs.patientTable.length;e++)this.$refs.patientTable[e].hideColumn(this.$refs.patientTable[e].getColumnByField("bedno"))}else"0"==this.roleMOCK&&(this.mdtrtIdTitle="就诊号",this.$refs.patientTable.hideColumn(this.$refs.patientTable.getColumnByField("bedno")));else if("1"==this.roleMOCK){this.mdtrtIdTitle="住院号";for(var t=0;t<this.$refs.patientTable.length;t++)this.$refs.patientTable[t].showColumn(this.$refs.patientTable[t].getColumnByField("bedno"))}else"0"==this.roleMOCK&&(this.mdtrtIdTitle="住院号",this.$refs.patientTable.showColumn(this.$refs.patientTable.getColumnByField("bedno")))},handelLimit:function(){this.limitModalVisible=!0},handleOk:function(){},handleCancel:function(){this.limitModalVisible=!1},cellClick:function(a,e){var t=this,i=e.column,n=e.row;this.currMdtrtId=n.currMdtrtId,this.hiFeesetlType=n.hiFeesetlType,this.fixmedinsCode=n.fixmedinsCode,this.admDeptCodg=n.admDeptCodg,this.drCodg=n.drCodg,this.$nextTick((function(){"1"==t.roleMOCK?t.$refs.patientTable[a].setCurrentRow(n):t.$refs.patientTable.setCurrentRow(n)})),"操作"!==i.title?(this.patientObjects=[],this.filterPatientObjects=[],this.queryForm.currentDeaprt=1,this.queryForm.check_res=["0","1","2"],this.queryForm.code="all",this.ae03List=[],this.getAudit(n.mdtrtId,n.patnIptCnt)):this.gotoPatientDetaills(n)},filterData:function(){var a=this,e=JSON.stringify(this.patientObjects);e=JSON.parse(e),e=e.filter((function(e){return e.ake006.includes(a.queryForm.project_name)})),this.currentUserDepart,1!==this.queryForm.currentDeaprt&&e.forEach((function(e){e.subProject=e.subProject.filter((function(e){return a.currentUserDepart.includes(e.aaz307)}))})),"all"!==this.queryForm.code&&(e=e.filter((function(e){return e.aka063==a.queryForm.code}))),this.queryForm.check_res.length?e.forEach((function(e){e.subProject=e.subProject.filter((function(e){return a.ape800Filter(a.queryForm.check_res,e.ape800,e.fail)}))})):e=[],e=e.filter((function(a){return a.subProject.length})),e.forEach((function(a){a.akc226=0,a.subProject.forEach((function(e){a.akc226+=Number(e.akc226)}))})),this.filterPatientObjects=(0,c.Z)(e)},ape800Filter:function(a,e,t){var i;if(a.length)return i="1"===t?"2":"2"==e?"0":"1",a.includes(i)},selectDepart:function(a){this.currentDepart=a,this.queryForm.currentDeaprt=a},selectTips:function(a,e){this.tipValue="",this.currentTipsIndex=e,this.queryForm.code=a.code},handleChange:function(a){this.queryForm.code=a,this.currentTipsIndex=-1},headerCellStyle:function(a){a.column,a.columnIndex;return{backgroundColor:"#ffffff"}},oprateEvent:function(a,e){0==e?this.prepareMsg(a):(a.currentOprate=e,this.handleFee(a))},handleFee:function(a){var e=this;if(a.ape893="2",a.aaz560="",a.currentOprate=1,"subProject"in a&&a.subProject.length){a.subProject.forEach((function(a){a.ape893="2",a.aaz560="",a.currentOprate=1}));var t=a.subProject.map((function(a){return a.aaz213})),i=new Set(t);this.patientObjects.forEach((function(e){e.ake001===a.ake001&&(e.currentOprate=1,e.subProject.forEach((function(a){i.has(a.aaz213)&&(a.ape893="2",a.aaz560="",a.currentOprate=1)})))}))}else this.filterPatientObjects.forEach((function(t){if(t.ake001===a.ake001){t.subProject.forEach((function(e){a.aaz213===e.aaz213&&(e.aaz560=info.content,e.ape893="2",e.aaz560="",e.currentOprate=1,e.ape893!==t.ape893&&(t.aaz560="",t.ape893="",t.currentOprate=-1))}));var i=t.subProject.map((function(a){return a.ape893}));e.isAllElementsEqual(i)&&(t.currentOprate=1)}})),this.patientObjects.forEach((function(t){if(t.ake001===a.ake001){t.subProject.forEach((function(e){a.aaz213===e.aaz213&&(e.aaz560=info.content,e.ape893="2",e.aaz560="",e.currentOprate=1,e.ape893!==t.ape893&&(t.aaz560="",t.ape893="",t.currentOprate=-1))}));var i=t.subProject.map((function(a){return a.ape893}));e.isAllElementsEqual(i)&&(t.currentOprate=1)}}));this.$forceUpdate()},isAllElementsEqual:function(a){for(var e=1;e<a.length;e++)if(a[e]!==a[0])return!1;return!0},prepareMsg:function(a){this.keepOnRecordVisible=!0,a.fixmedinsCode=this.fixmedinsCode,a.admDeptCodg=this.admDeptCodg,a.drCodg=this.drCodg,a.aaz217=this.aaz217,a.currMdtrtId=this.currMdtrtId,a.scene=this.scene,this.rowData=a},handleUpdateRow:function(a){var e=this;if(this.keepOnRecordVisible=!1,this.rowData.ape893="1",this.rowData.aaz560=a.content,this.rowData.currentOprate=0,"subProject"in this.rowData&&this.rowData.subProject.length){this.rowData.subProject.forEach((function(e){e.aaz560=a.content,e.ape893="1",e.currentOprate=0}));var t=this.rowData.subProject.map((function(a){return a.aaz213})),i=new Set(t);this.patientObjects.forEach((function(t){t.ake001===e.rowData.ake001&&(t.currentOprate=0,t.ape893="1",t.aaz560=a.content,t.subProject.forEach((function(e){i.has(e.aaz213)&&(e.aaz560=a.content,e.ape893="1",e.currentOprate=0)})))}))}else this.filterPatientObjects.forEach((function(t){if(t.ake001===e.rowData.ake001){a.content!==t.aaz560&&(t.aaz560="",e.$forceUpdate()),t.subProject.forEach((function(i){e.rowData.aaz213===i.aaz213&&(i.aaz560=a.content,i.ape893="1",i.currentOprate=0,i.ape893!==t.ape893&&(t.ape893="",t.currentOprate=-1))}));var i=t.subProject.map((function(a){return a.ape893}));e.isAllElementsEqual(i)&&(t.currentOprate=0)}})),this.patientObjects.forEach((function(t){if(t.ake001===e.rowData.ake001){a.content!==t.aaz560&&(t.aaz560="",e.$forceUpdate()),t.subProject.forEach((function(i){e.rowData.aaz213===i.aaz213&&(i.aaz560=a.content,i.ape893="1",i.currentOprate=0,i.ape893!==t.ape893&&(t.ape893="",t.currentOprate=-1))}));var i=t.subProject.map((function(a){return a.ape893}));e.isAllElementsEqual(i)&&(t.currentOprate=0)}}))},handleClose:function(){this.keepOnRecordVisible=!1},handleHisOk:function(){this.oprateHistoryVisible=!1},handleHisCancel:function(){this.oprateHistoryVisible=!1},billSave:function(a){var e=this,t={url:"/auditOnBackend/saveBillOperation",autoValid:!0,autoQs:!1,data:{aaz217:this.aaz217,akc190:this.currMdtrtId,akb020:this.fixmedinsCode,kf59List:a,scene:this.scene}},i={successCallback:function(a){"0"===a.data.data.next&&(e.patientObjects=[],e.filterPatientObjects=[]),e.billVisible=!1}};this.Base.submit(null,t,i)},billClose:function(){this.billVisible=!1},openHistoryModal:function(a){this.oprateHistoryVisible=!0,this.getHistoryop(this.currMdtrtId,a.ake001,a.ykc610)},cancelMedicineModal:function(){this.medicineSearchVisible=!1},toggleExpandChangeEvent:function(a){var e=a.row;a.expanded;this.$refs.xTable.toggleRowExpand(e)},cellClickEvent:function(a){var e=this,t=a.column,i=a.row;"操作"!==t.title&&(this.$refs.xTable.toggleRowExpand(i),"2"==this.roleMOCK&&this.$nextTick((function(){e.$refs.dTablechildren.hideColumn(e.$refs.dTablechildren.getColumnByField("op"))})))},gotoPatientDetaills:function(a){if(a.latestAaz217){var e="";switch(this.scene){case"opt":e="1";break;case"ipt":e="4";break;case"opsp":e="0";break}this.Base.openTabMenu({id:a.fixmedinsCode+a.latestAaz217,name:"【"+a.patnName+"】患者详情",url:"detailsQuery.html#/expenseDetailsForOther?akb020=".concat(a.fixmedinsCode,"&akc190=").concat(a.currMdtrtId,"&aaz217=").concat(a.latestAaz217,"&flag=").concat(e),refresh:!1})}},openAuditDetails:function(a){var e="";switch(this.scene){case"opt":e="1";break;case"ipt":e="4";break;case"opsp":e="0";break}a.hiFeesetlType=this.hiFeesetlType,a.ykz020="1",a.aae500=e,a.aaz217=this.aaz217,this.bfRecord=a,this.visible=!0},handleAuditCancel:function(){this.visible=!1},handleTipsData:function(){var a=this;this.tipsType=[],this.tipsOptions=[];var e=this.ae03List;e.forEach((function(e){"1"===e.showalone?a.tipsType.push(e):a.tipsOptions.push(e)})),this.tipsType.push({name:"全部",code:"all"}),this.currentTipsIndex=this.tipsType.length-1,this.tipsOptions.forEach((function(a){a.label=a.name,a.value=a.code}))},handleSubmitData:function(){if(this.patientObjects.length){if(!this.checkAllItemChecked())return void message.warning("所有违规项目明细都需要操作");this.handleAuditOprate()}},checkAllItemChecked:function(){var a,e=this.patientObjects.flatMap((function(a){return a.subProject})),t=(0,l.Z)(e);try{for(t.s();!(a=t.n()).done;){var i=a.value;if("0"===i.fail&&!i.ape893)return!1}}catch(n){t.e(n)}finally{t.f()}return!0},getRoleMockDepart:function(){var a=this,e={url:"/miimCommonRead/queryDepartRpcList",autoValid:!0},t={successCallback:function(e){a.DepartOptions=e.data.resultData}};this.Base.submit(null,e,t)},getPatient:function(){var a=this,e="";switch(this.roleMOCK){case"0":e="/auditOnBackend/getPatientList";break;case"1":e="/auditOnBackend/getPatientListWithAuthed";break;case"2":if(!this.dptCode)return;e="/auditOnBackend/getPatientListAll";break}this.Base.submit(null,{url:e,data:{searchVal:this.searchVal,scene:this.scene,dptCode:this.dptCode}},{successCallback:function(e){a.dataSource=e.data.data}})},getCurentDepartMsg:function(){var a=this,e={url:"/auditOnBackend/getCurrentUserDptList",autoValid:!0},t={successCallback:function(e){e.data.data.forEach((function(e){a.currentUserDepart.push(e.value)}))}};this.Base.submit(null,e,t)},getAudit:function(a,e){var t=this;return(0,r.Z)(regeneratorRuntime.mark((function i(){var n,r;return regeneratorRuntime.wrap((function(i){while(1)switch(i.prev=i.next){case 0:return n={url:"/auditOnBackend/doAudit",autoValid:!0,data:{mdtrtId:a,patientCount:e,scene:t.scene}},i.next=3,new Promise((function(a,e){var i={successCallback:function(e){return a(e)},errorCallback:function(a){return e(a)}};t.Base.submit(null,n,i)}));case 3:if(r=i.sent,!r.data.hasSuspect){i.next=8;break}return t.aaz217=r.data.aaz217,i.next=8,t.getAuditTableData(r.data.aaz217);case 8:case"end":return i.stop()}}),i)})))()},getAuditTableData:function(a){var e=this;return(0,r.Z)(regeneratorRuntime.mark((function t(){var i,n;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return i={url:"/auditOnBackend/getAuditData",autoValid:!0,data:{akc190:e.currMdtrtId,aaz217:a,scene:e.scene}},n={successCallback:function(a){a.data.data.nonReimbursableMap.forEach((function(a){a.akc226=0;var e=new Set;a.subProject.forEach((function(t){t.ykz259||(t.ykz259=a.ykz259),a.akc226+=t.akc226,t.ape893&&e.add(t.ape893),t.oldape893=t.ape893,t.currentOprate="1"==t.ape893?0:"2"==t.ape893?1:-1})),1===e.size?a.ape893=e.values().next().value:a.ape893=null,a.currentOprate="1"==a.ape893?0:"2"==a.ape893?1:-1})),e.patientObjects=(0,c.Z)(a.data.data.nonReimbursableMap),e.filterPatientObjects=(0,c.Z)(a.data.data.nonReimbursableMap),e.ae03List=(0,c.Z)(a.data.data.ae03List);var t=a.data.data.kf59List;t&&t.length>0&&(e.billVisible=!0,e.billData=t)}},t.next=4,e.Base.submit(null,i,n);case 4:case"end":return t.stop()}}),t)})))()},handleAuditOprate:function(){var a=this,e={url:"/auditOnBackend/saveOperation",autoValid:!0,autoQs:!1,data:{aaz217:this.aaz217,akc190:this.currMdtrtId,akb020:this.fixmedinsCode,nonReimbursableMap:this.patientObjects,scene:this.scene}},t={successCallback:function(e){a.patientObjects=[],a.filterPatientObjects=[]}};this.Base.submit(null,e,t)},getHistoryop:function(a,e,t){var i=this;this.oprateHistoryList=[];var n={url:"/auditOnBackend/getOprHistory",autoValid:!0,data:{aaz217:this.aaz217,currMdtrtId:a,ake001:e,scene:this.scene}},r={successCallback:function(a){i.oprateHistoryList=a.data.data,i.oprateHistoryList&&(i.oprateHistoryList=i.oprateHistoryList.filter((function(a){return t===a.ykc610})))}};this.Base.submit(null,n,r)},handleSearch:function(a){this.getRoleMockDepart(a)}},mounted:function(){var a=this;this.$nextTick((function(){a.roleMOCK=a.$route.query.roleMOCK,"2"==a.roleMOCK&&a.$refs.xTable.hideColumn(a.$refs.xTable.getColumnByField("op")),"2"==a.roleMOCK?a.getRoleMockDepart():a.getPatient(),a.getCurentDepartMsg(),a.changeTAB(a.defaultActiveKey)}))}},N=X,K=(0,p.Z)(N,i,n,!1,null,"4f094899",null),U=K.exports}}]);
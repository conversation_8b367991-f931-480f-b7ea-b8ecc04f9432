"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[6080],{76080:function(t,e,a){a.r(e),a.d(e,{default:function(){return j}});var i=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("div",{staticClass:"fit"},[i("ta-border-layout",{staticStyle:{},attrs:{"layout-type":"fixTop"}},[i("div",{attrs:{slot:"header"},slot:"header"},[i("div",{staticClass:"processBox"},[i("div",{staticClass:"processItem"},[i("span",{staticClass:"count",style:{color:"orange"}},[e._v(e._s(e.needHandCount))]),i("span",{staticClass:"label"},[e._v("待处理申诉")])]),i("div",{staticClass:"processItem"},[i("span",{staticClass:"count",staticStyle:{color:"#FFCA33"}},[e._v(e._s(e.needSubmitCount))]),i("span",{staticClass:"label"},[e._v("待提交申诉")])]),i("div",{staticClass:"processItem"},[i("span",{staticClass:"count",staticStyle:{color:"#2db7f5"}},[e._v(" "+e._s(e.totalCount>0?((e.totalCount-e.needHandCount)/e.totalCount*100).toFixed(0):0)+"% ")]),i("span",{staticClass:"label"},[e._v("处理进度")])])]),i("div",{staticClass:"query-header"},[i("div",{ref:"formBox",staticClass:"query-header-left-2",staticStyle:{width:"calc(100% - 275px)"},style:e.formBoxStyle},[i("ta-form",{attrs:{"auto-form-create":function(e){return t.form=e},layout:"horizontal","form-layout":!0,"label-width":"80px",col:e.col}},[i("ta-form-item",{attrs:{label:"清算期号","field-decorator-id":"aae043"}},[i("ta-select",{staticStyle:{width:"100%"},attrs:{showSearch:"",placeholder:"请选择清算期号",options:e.aae043List,"allow-clear":!0}})],1),i("ta-form-item",{attrs:{label:"住院门诊号","field-decorator-id":"akc190"}},[i("ta-suggest",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入住院门诊号","option-config":{value:"value",label:"value"},"data-source":e.admissionNumList,"dropdown-match-select-width":!1,"dropdown-style":{width:"280px"},"table-title-map":new Map([["label",{name:"姓名",style:{minWidth:"120px"}}],["value",{name:"住院门诊号",style:{minWidth:"180px"}}]])},on:{select:function(t,a){return e.admissionNumChange(t,a)},search:e.admissionNumSearch}})],1),i("ta-form-item",{attrs:{label:"疑点类型","field-decorator-id":"dataType"}},[i("ta-select",{attrs:{placeholder:"疑点类型筛选","allow-clear":!0}},[i("ta-select-option",{attrs:{value:"1"}},[e._v(" 初审疑点 ")]),i("ta-select-option",{attrs:{value:"3"}},[e._v(" 病例申诉 ")])],1)],1),i("ta-form-item",{attrs:{label:"医保类型","field-decorator-id":"aae141"}},[i("ta-select",{attrs:{placeholder:"医保类型筛选","collection-type":"AAE141","allow-clear":""}})],1),i("ta-form-item",{attrs:{label:"规则名称","field-decorator-id":"aaa167"}},[i("ta-suggest",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入规则名称","option-config":{value:"value",label:"label"},"data-source":e.ruleNameList,"table-title-map":new Map([["label",{name:"规则名称",style:{minWidth:"120px"}}]])},on:{select:function(t,a){return e.ruleNameChange(t,a)},search:e.ruleNameSearch}})],1),i("ta-form-item",{attrs:{label:"医保项目","field-decorator-id":"ake001"}},[i("ta-suggest",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入医保项目名称","option-config":{value:"value",label:"label"},"data-source":e.objNameList,"table-title-map":new Map([["label",{name:"医保项目名称",style:{minWidth:"120px"}}]])},on:{select:function(t,a){return e.objNameChange(t,a)},search:e.objNameSearch}})],1),i("ta-form-item",{attrs:{label:"参保人","field-decorator-id":"aac003"}},[i("ta-suggest",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入参保人姓名","option-config":{value:"value",label:"label"},"data-source":e.patientNameList,"table-title-map":new Map([["label",{name:"参保人",style:{minWidth:"120px"}}]])},on:{select:function(t,a){return e.patientNameChange(t,a)},search:e.patientNameSearch}})],1),i("ta-form-item",{attrs:{label:"开单医生","field-decorator-id":"aaz570"}},[i("ta-suggest",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入开单医生","option-config":{value:"value",label:"label"},"data-source":e.doctorList,"table-title-map":new Map([["label",{name:"开单医生",style:{minWidth:"120px"}}]])},on:{select:function(t,a){return e.doctorChange(t,a)},search:e.doctorSearch}})],1),i("ta-form-item",{attrs:{label:"开单科室","field-decorator-id":"aac004"}},[i("ta-suggest",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入开单科室","option-config":{value:"value",label:"label"},"data-source":e.deptList,"table-title-map":new Map([["label",{name:"科室名称",style:{minWidth:"120px"}}]])},on:{select:function(t,a){return e.deptChange(t,a)},search:e.deptSearch}})],1),i("ta-form-item",{attrs:{label:"收费类别","field-decorator-id":"ake003"}},[i("ta-suggest",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入收费类别","option-config":{value:"value",label:"label"},"data-source":e.costTypeList,"table-title-map":new Map([["label",{name:"收费类别",style:{minWidth:"120px"}}]])},on:{search:e.costTypeSearch}})],1),i("ta-form-item",{attrs:{"field-decorator-id":"akb020",label:"院区标识"}},[i("ta-select",{attrs:{showSearch:"",placeholder:"院区标识筛选",allowClear:"",options:e.hosList}})],1),i("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:"Y"===this.accountGroup.toLocaleUpperCase(),expression:"this.accountGroup.toLocaleUpperCase() === 'Y'"}],attrs:{"field-decorator-id":"aaz309",label:"诊疗小组"}},[i("ta-select",{attrs:{showSearch:"","show-search":!0,placeholder:"诊疗小组",dropdownMatchSelectWidth:!1,dropdownStyle:{maxHeight:"450px",overflow:"auto"},filterOption:e.filterOption,options:e.groupList,"allow-clear":""}})],1)],1)],1),i("div",{staticClass:"query-btn-2",staticStyle:{width:"250px"}},[i("div",{staticClass:"ctrl-btn",on:{click:e.formShowAllChange}},[e._v(" "+e._s(e.formShowAll?"收起":"展开")+" "),e.formShowAll?i("ta-icon",{attrs:{type:"caret-up"}}):i("ta-icon",{attrs:{type:"caret-down"}})],1),i("ta-button",{attrs:{icon:"search",type:"primary"},on:{click:e.queryTableData}},[e._v(" 查询 ")]),i("ta-button",{attrs:{icon:"redo",type:"default"},on:{click:e.resetForm}},[e._v(" 重置 ")])],1)])]),i("div",{staticClass:"fit",staticStyle:{position:"relative",overflow:"hidden"},attrs:{id:"parentDom"}},[i("ta-drawer",{attrs:{placement:"right",width:"550",closable:!1,visible:e.drawerVisible,"get-container":e.setContainer,"wrap-style":{position:"absolute"}},on:{close:e.onClose}},[i("ta-label-con",{attrs:{label:"申诉处理历史记录"}}),e.docLogList.length>0?i("div",[i("ta-timeline",{attrs:{mode:"alternate"}},e._l(e.docLogList,(function(t,a){return i("ta-timeline-item",{key:a,attrs:{color:e.statusColors[t.type]||"#000000"}},[e._v(" "+e._s(t.combined)+" ")])})),1)],1):i("div",[i("div",{staticStyle:{"text-align":"center",padding:"20px"}},[e._v("暂无申诉处理历史记录")])])],1),i("div",{staticClass:"part-top"},[i("ta-title",{attrs:{title:"申诉处理"}})],1),i("div",{staticClass:"part-top1"},[i("div",{staticClass:"part-title"},[i("tag-change",{attrs:{"tag-values":e.tagValues,"status-arr":e.statusArr},on:{change:e.changeTag}})],1),i("div",{staticClass:"part-operate"},[i("ta-button",{directives:[{name:"show",rawName:"v-show",value:"Y"===e.accountGroup.toLocaleUpperCase(),expression:"accountGroup.toLocaleUpperCase() === 'Y'"}],attrs:{type:"primary"},on:{click:e.updateCurrPers}},[e._v(" 批量修改处理人 ")]),e.tagValues.some((function(t){return["0","1","3"].includes(t)}))?i("ta-button",{attrs:{type:"primary"},on:{click:e.batchSubmit}},[e._v(" 批量提交 ")]):e._e()],1)]),i("div",{staticStyle:{height:"calc(100% - 104px)","min-height":"300px"}},[i("ta-big-table",{ref:"xTable",staticStyle:{width:"100%"},attrs:{border:"","highlight-hover-row":"","show-overflow":"","tooltip-config":{theme:"light",contentMethod:e.tooltipMethod},"sort-config":{trigger:"cell"},"checkbox-config":{trigger:"row",checkMethod:e.tableCheckboxMethod},data:e.dataSource,height:"auto",resizable:!0,"auto-resize":"",size:"mini","empty-text":"-",width:"min-width","cell-style":e.cellStyle},scopedSlots:e._u([{key:"bottomBar",fn:function(){return[i("ta-pagination",{ref:"gridPager",staticStyle:{"text-align":"right"},attrs:{size:"small","default-page-size":200,"page-size-options":["30","50","100","200","500"],"data-source":e.dataSource,url:e.pageUrl,params:e.pageParams},on:{"update:dataSource":function(t){e.dataSource=t},"update:data-source":function(t){e.dataSource=t}}})]},proxy:!0}])},[i("ta-big-table-column",{attrs:{type:"checkbox",fixed:"left",width:"40px",align:"center"}}),i("ta-big-table-column",{attrs:{type:"seq",fixed:"left",width:"50px",title:" ",align:"center"}}),i("ta-big-table-column",{attrs:{fixed:"left",field:"aae043","min-width":"75px",title:"期号",sortable:""}}),i("ta-big-table-column",{attrs:{fixed:"left",field:"akc195","show-overflow":"",title:"申诉截止日期","min-width":"130px",sortable:""}}),i("ta-big-table-column",{attrs:{fixed:"left",align:"center",field:"appealFlag",title:"是否申诉","min-width":"100px",sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;t.column;return[e._v(" "+e._s("1"===a.appealFlag?"申诉":"不申诉")+" ")]}}])}),i("ta-big-table-column",{attrs:{fixed:"left",field:"currPersName","show-overflow":"",title:"申诉处理人","min-width":"120px",sortable:""}}),i("ta-big-table-column",{attrs:{fixed:"left",field:"aaz560",title:"申诉理由","min-width":"180px"}}),i("ta-big-table-column",{attrs:{fixed:"left",field:"",title:"申诉材料",width:"100px"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("div",{staticStyle:{position:"relative"}},[a.fileNum>0?i("div",{staticStyle:{color:"#1b65b9"},on:{mouseover:e.showFileList,mouseout:e.hideFileList}},[i("span",{staticStyle:{cursor:"pointer"},attrs:{id:"downloadButton"},on:{click:function(t){return e.fnDownloadFile(a.aaz213)}}},[e._v("附件("+e._s(a.fileNum)+")")]),e._v("   "),i("ta-icon",{attrs:{type:"search"},on:{click:function(t){return e.imageViewerHandle(a.aaz213)}}})],1):i("div",[e._v(" 无 ")]),i("div",{staticStyle:{position:"absolute",left:"100px","max-width":"250px","min-width":"100px",height:"auto"},attrs:{visible:a.fileListShow}},[e._v(" "+e._s(a.fileNames)+" ")])])]}}])}),i("ta-big-table-column",{attrs:{field:"aaz213",title:"管理Id",visible:!1}}),i("ta-big-table-column",{attrs:{field:"akc190",title:"住院门诊号","min-width":"120px",sortable:""}}),i("ta-big-table-column",{attrs:{sortable:"",field:"akb021","show-overflow":"",title:"医院名称","min-width":"110px"}}),i("ta-big-table-column",{attrs:{field:"aac003",title:"参保人","min-width":"100",sortable:""}}),i("ta-big-table-column",{attrs:{field:"ake002",title:"医保项目名称","min-width":"140px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"aaa167",title:"违反规则","min-width":"180px",sortable:""}}),i("ta-big-table-column",{attrs:{"show-overflow":"",field:"matchStas",title:"预警记录",width:"120px",align:"center","sort-by":"matchStas",sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return["1"===a.matchStas?i("div",[i("div",{staticClass:"opareteItem",on:{click:function(t){return e.viewRecords(a)}}},[e._v(" 查看 ")])]):i("div",[i("span",[e._v("无")])])]}}])}),i("ta-big-table-column",{attrs:{field:"aac004",title:"开单科室","min-width":"100px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"aaz570",title:"开单医生","min-width":"100",sortable:""}}),i("ta-big-table-column",{attrs:{field:"aae036",title:"明细时间","min-width":"150px",align:"center",sortable:""}}),i("ta-big-table-column",{attrs:{field:"ape804",title:"初审违规金额","min-width":"130",align:"right",formatter:"formatAmount","sort-by":"ape804",sortable:""}}),i("ta-big-table-column",{attrs:{"show-overflow":"",field:"operatHistory",title:"历史记录",width:"120px",align:"center","sort-by":"matchStas",sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("div",{staticClass:"opareteItem",on:{click:function(t){return e.viewHistory(a)}}},[e._v(" 查看 ")])]}}])}),i("ta-big-table-column",{attrs:{field:"handStas",title:"状态",fixed:"right","min-width":"100","collection-type":"appealhandstas",filters:[{data:""}],"filter-method":e.filterMethod,"sort-by":"handStas",sortable:""},scopedSlots:e._u([{key:"filter",fn:function(t){var a=t.$panel,s=t.column;return e._l(s.filters,(function(t,s){return i("ta-select",{key:s,staticStyle:{margin:"10px",width:"120px"},attrs:{"get-popup-container":e.setPopupContainer,"collection-type":"appealhandstas","collection-filter":e.filterStr,"reverse-filter":!0},on:{change:function(e){return a.changeOption(e,!!t.data,t)}},model:{value:t.data,callback:function(a){e.$set(t,"data",a)},expression:"option.data"}})}))}}])}),i("ta-big-table-column",{attrs:{fixed:"right",field:"operate",title:"操作",width:"200",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return["4"==a.handStas||"3"==a.handStas||"7"==a.handStas||"10"==a.handStas||"11"==a.handStas?i("div",{staticClass:"opareteItem",on:{click:function(t){return e.handleRow(a)}}},[e._v(" 详情 ")]):e._e(),"3"==a.handStas?i("div",{staticClass:"opareteItem",on:{click:function(t){return e.backRow(a)}}},[i("ta-divider",{attrs:{type:"vertical"}}),e._v(" 撤回 ")],1):e._e(),-1===["3","4","7","10","11"].indexOf(a.handStas)?i("div",[i("div",{directives:[{name:"show",rawName:"v-show",value:"Y"===e.accountGroup.toLocaleUpperCase(),expression:"accountGroup.toLocaleUpperCase() === 'Y'"}],staticClass:"opareteItem",on:{click:function(t){return e.handleModefy(a)}}},[e._v(" 修改处理人 ")]),i("ta-divider",{directives:[{name:"show",rawName:"v-show",value:"Y"===e.accountGroup.toLocaleUpperCase(),expression:"accountGroup.toLocaleUpperCase() === 'Y'"}],attrs:{type:"vertical"}}),i("div",{staticClass:"opareteItem",on:{click:function(t){return e.prehandleCheck(a)}}},[e._v(" 处理 ")]),i("ta-divider",{attrs:{type:"vertical"}}),i("div",{staticClass:"opareteItem",on:{click:function(t){return e.preSubmitCheck(a)}}},[e._v(" 提交 ")])],1):e._e()]}}])})],1),e.isShow?i("ta-image-viewer",{ref:"myViewer",attrs:{visible:e.isShow,images:e.images},on:{"update:visible":function(t){e.isShow=t},shown:e.shown},model:{value:e.viewerVal,callback:function(t){e.viewerVal=t},expression:"viewerVal"}}):e._e()],1)],1)]),i("approval",{attrs:{visible:e.approvalVisible,params:e.rowData},on:{handleClose:e.handleClose2,handleSubmit:e.handleSubmit}}),i("view-record",{attrs:{title:"预警记录",visible:e.uploadVisible4,param:e.mateRow},on:{handleClose:e.handleClose4}}),i("ta-modal",{attrs:{title:"批量修改申诉处理人",visible:e.batchModefyVisible,height:300,width:580},on:{ok:function(t){return e.handleBatchModefy()},cancel:e.closeBatchModefyModel}},[i("p",[e._v("请选择修改后的申诉处理人")]),i("ta-form",{attrs:{"auto-form-create":function(e){return t.form1=e},layout:"horizontal","form-layout":!0,"label-width":"120px",col:{xs:1,sm:1,md:1,lg:1,xl:1,xxl:1}}},[i("ta-form-item",{attrs:{label:"当前处理人","field-decorator-id":"currPersName",disabled:!0,span:22}},[i("ta-input")],1),i("ta-form-item",{attrs:{label:"申诉处理人","field-decorator-id":"doctorId",require:!0,span:22}},[i("ta-suggest",{staticStyle:{width:"100%"},attrs:{"data-source":e.peopleList,"table-title-map":new Map([["label",{name:"人员姓名",style:{minWidth:"120px"}}]]),"option-config":e.optionConfig},on:{search:e.handleSearch},scopedSlots:e._u([{key:"body-cell",fn:function(t){t.cellValue,t.column,t.columnIndex;var a=t.row;t.rowIndex;return[i("span",[e._v(e._s(a.label)+" ("+e._s(a.value)+" "+e._s(a.aae386?a.aae386:"未知科室 "+a.aaz307)+" "+e._s(a.akb021?a.akb021:"未知医院 "+a.akb020)+")")])]}}])})],1)],1),i("div",{staticStyle:{"margin-top":"10px",color:"gray"}},[i("p",[e._v("提示：选择申诉处理人后，将批量修改选中的申诉记录的处理人。")]),i("p",[e._v("请仔细核对选择的人员信息，避免误操作。")])])],1)],1)},s=[],r=a(89584),o=(a(32564),a(95278)),n="appeal/drPoint/",l={getBasePath:function(){return o.Z.basePath},getPageUrl:function(){return n+"queryListByPage"},getProcess:function(t,e){Base.submit(null,{url:n+"queryProgress",data:t},{successCallback:function(t){return e(t)}})},getAdmissionNumList:function(t,e){Base.submit(null,{url:"appeal/common/fuzzySearchInKf38/patientId",data:t},{successCallback:function(t){return e(t)}})},getDeptList:function(t,e){Base.submit(null,{url:"appeal/common/fuzzySearchInKf38/OprDepartName",data:t},{successCallback:function(t){return e(t)}})},getCostTypeList:function(t,e){Base.submit(null,{url:"appeal/common/fuzzySearchInKf38/costType",data:t},{successCallback:function(t){return e(t)}})},getRuleNameList:function(t,e){Base.submit(null,{url:"appeal/common/fuzzySearchInKf38/ruleName",data:t},{successCallback:function(t){return e(t)}})},getPatintNameList:function(t,e){Base.submit(null,{url:"appeal/common/fuzzySearchInKf38/patientName",data:t},{successCallback:function(t){return e(t)}})},getDoctorList:function(t,e){Base.submit(null,{url:"appeal/common/fuzzySearchInKf38/doctorName",data:t},{successCallback:function(t){return e(t)}})},getObjNameList:function(t,e){Base.submit(null,{url:"appeal/common/fuzzySearchInKf38/itemName",data:t},{successCallback:function(t){return e(t)}})},saveRow:function(t,e){Base.submit(null,{url:n+"saveAppealHandle",data:t,autoQs:!1,isFormData:!0},{successCallback:function(t){return e(t)}})},queryAae043List:function(t,e){Base.submit(null,{url:n+"queryAae043List",data:t},{successCallback:function(t){return e(t)}})},getRowDetails:function(t,e){Base.submit(null,{url:n+"queryAppealDetail",data:t},{successCallback:function(t){return e(t)}})},getPicZipUrl:function(){return o.Z.basePath+"/"+n+"fileZipDownload"},batchSubmit:function(t,e){Base.submit(null,{url:n+"submitAppeal",data:t,autoQs:!1},{successCallback:function(t){return e(t)}})},batchUpdateCurrPers:function(t,e){Base.submit(null,{url:n+"batchUpdateCurrPers",data:t,autoQs:!1},{successCallback:function(t){return e(t)}})},reBackRow:function(t,e){Base.submit(null,{url:n+"cancelSubmitAppeal",data:t},{successCallback:function(t){return e(t)}})},submitRow:function(t,e){Base.submit(null,{url:n+"",data:t},{successCallback:function(t){return e(t)}})},checkPromptState:function(t,e){Base.submit(null,{url:n+"checkPromptState",data:t,autoQs:!1},{successCallback:function(t){return e(t)}})},updateState:function(t,e){Base.submit(null,{url:n+"updateState",data:t},{successCallback:function(t){return e(t)}})},getFileInfo:function(t,e){Base.submit(null,{url:n+"getFileInfos",data:t},{successCallback:function(t){return e(t)}})},queryPeople:function(t,e){Base.submit(null,{url:"/appeal/common/getDicDoctor",data:t},{successCallback:function(t){return e(t)}})},queryHistory:function(t,e){Base.submit(null,{url:"/appeal/common/queryHistory",data:t},{successCallback:function(t){return e(t)}})}},c=a(36797),u=a.n(c),d=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",t._l(t.tagList,(function(e){return a("ta-tag",{key:e.label,attrs:{color:e.checked?"#1890ff":""},on:{click:function(a){return t.handleChange(e)}}},[a("span",{staticClass:"tag-icon"},[t._v(t._s(e.icon))]),a("span",{staticClass:"tag-label"},[t._v(" "+t._s(e.label)+" ")])])})),1)},h=[],f=[{value:"1",label:"待审批",checked:!0,color1:"orange",color2:"#FCB76B"},{value:"2",label:"审批通过未导出",checked:!1,color1:"green",color2:"#87d068"},{value:"3",label:"审批通过已导出",checked:!1,color1:"purple",color2:"#7d02d1"},{value:"4",label:"审批驳回待提交",checked:!1,color1:"red",color2:"#fa0000"},{value:"5",label:"全部",checked:!1,color1:"blue",color2:"#2db7f5"}],p={name:"tagChange",props:{tagValues:{type:Array,default:function(){return[]}},statusArr:{type:Array,default:function(){return f}}},data:function(){return{tagList:this.statusArr}},watch:{tagValues:{immediate:!0,handler:function(t){this.syncCheckedState(t)}}},mounted:function(){this.syncCheckedState(this.tagValues)},methods:{syncCheckedState:function(t){this.tagList=this.tagList.map((function(e){return e.checked=t.includes(e.value),e}))},handleChange:function(t){"全部"===t.label?this.tagList.forEach((function(t){t.checked="全部"===t.label})):(this.tagList.forEach((function(t){"全部"===t.label&&(t.checked=!1)})),t.checked=!t.checked,this.tagList.some((function(t){return t.checked}))||(t.checked=!0));var e=this.tagList.filter((function(t){return t.checked})).map((function(t){return t.value}));this.$emit("change",e)}}},m=p,g=a(1001),b=(0,g.Z)(m,d,h,!1,null,"7af2fefa",null),v=b.exports,w=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},y=[],C={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '},S={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:C}}},k=S,x=(0,g.Z)(k,w,y,!1,null,"5e7ef0ae",null),P=x.exports,L=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("ta-drawer",{attrs:{width:"900",title:e.title,placement:"right",visible:e.visible,"mask-closable":!1,"get-container":function(){return t.$el.parentNode}},on:{close:function(t){return e.handleClose("1")}}},[i("div",{attrs:{slot:"footer"},slot:"footer"},[i("div",{staticStyle:{display:"flex","justify-content":"center",width:"100%"}},[i("ta-button",{on:{click:function(t){return e.handleClose("1")}}},[e._v(" "+e._s(e.isShow?"关闭":"取消")+" ")]),i("ta-button",{directives:[{name:"show",rawName:"v-show",value:!e.isShow,expression:"!isShow"}],attrs:{type:"primary"},on:{click:function(t){return e.handleSave("1")}}},[e._v(" 保存 ")]),i("ta-button",{directives:[{name:"show",rawName:"v-show",value:!e.isShow,expression:"!isShow"}],attrs:{type:"danger"},on:{click:function(t){return e.handleSave("2")}}},[e._v(" 提交 ")])],1)]),i("div",[i("ta-form",{attrs:{"auto-form-create":function(e){return t.form=e},layout:"horizontal","form-layout":!0,"label-width":"80px",col:{xs:1,sm:1,md:1,lg:1,xl:1,xxl:1}}},[i("ta-form-item",{attrs:{label:"id","field-decorator-id":"aaz213",disabled:!0,hidden:!0}},[i("ta-input")],1),i("ta-form-item",{attrs:{label:"期号","field-decorator-id":"aae043",disabled:!0,hidden:!0}},[i("ta-month-picker")],1),i("ta-form-item",{attrs:{label:"参保人","field-decorator-id":"aac003",span:8}},[e._v(" "+e._s(e.paramDetails.aac003||"")+" ")]),i("ta-form-item",{attrs:{label:"住院号","field-decorator-id":"akc190",span:8}},[e._v(" "+e._s(e.paramDetails.akc190||"")+" ")]),i("ta-form-item",{attrs:{label:"年龄","field-decorator-id":"age",span:8}},[e._v(" "+e._s(e.paramDetails.age||"")+" ")]),i("ta-form-item",{attrs:{label:"住院天数","field-decorator-id":"iptDays",span:8}},[e._v(" "+e._s(e.paramDetails.iptDays||"")+" ")]),i("ta-form-item",{attrs:{label:"入院日期","field-decorator-id":"aae030",span:8}},[e._v(" "+e._s(e.paramDetails.aae030||"")+" ")]),i("ta-form-item",{attrs:{label:"出院日期","field-decorator-id":"aae031",span:8}},[e._v(" "+e._s(e.paramDetails.aae031||"")+" ")]),i("ta-form-item",{attrs:{label:"性别","field-decorator-id":"aac004",span:8}},[e._v(" "+e._s(e.CollectionLabel("SEX",e.paramDetails.aac004)||"")+" ")]),i("ta-form-item",{attrs:{label:"扣款条目数","field-decorator-id":"wfsl",span:8}},[e._v(" "+e._s(e.paramDetails.failCount||"")+"条 ")]),i("ta-form-item",{attrs:{label:"规则名称","field-decorator-id":"aaa167"}},[e._v(" "+e._s(e.paramDetails.aaa167||"")+" ")]),i("ta-form-item",{attrs:{label:"违反项目","field-decorator-id":"ake002"}},[e._v(" "+e._s(e.paramDetails.ake002||"")+" ")]),i("ta-form-item",{attrs:{label:"规则描述","field-decorator-id":"ykz018"}},[e._v(" "+e._s(e.paramDetails.ykz018||"")+" ")]),i("ta-form-item",{attrs:{label:"诊断","field-decorator-id":"diagnosis"}},[e._v(" "+e._s(e.paramDetails.diagnosis||e.paramDetails.aka121||"")+" ")]),this.emrLinkEnabled?i("div",{staticStyle:{"margin-bottom":"20px"}},[i("a",{staticStyle:{"text-decoration":"underline"},attrs:{href:"javascript:void(0);"},on:{click:e.openEmrQuery}},[e._v("电子病历入口")])]):e._e(),i("ta-form-item",{attrs:{label:"驳回原因","field-decorator-id":"rejctReas",hidden:!e.isReturnedData}},[e._v(" "+e._s(e.paramDetails.rejctReas||"")+" ")]),i("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:["4","6","9","10"].includes(e.paramDetails.handStas),expression:"['4','6','9','10'].includes(paramDetails.handStas)"}],attrs:{label:e.isReturnedData?"驳回人":"审批人","field-decorator-id":"rejctPers"}},[e._v(" "+e._s(e.isReturnedData?e.paramDetails.rejctPers||"":e.paramDetails.auditPers||"")+" ")]),i("ta-form-item",{attrs:{label:"是否申诉","field-decorator-id":"appealFlag"}},[i("ta-radio-group",{directives:[{name:"show",rawName:"v-show",value:!this.isShow,expression:"!this.isShow"}],attrs:{size:"small"},on:{change:e.appealFlagChange}},[i("ta-radio",{attrs:{value:"1"}},[e._v("申诉")]),i("ta-radio",{attrs:{value:"0"}},[e._v("不申诉，认同并及时整改")])],1),i("div",{directives:[{name:"show",rawName:"v-show",value:this.isShow,expression:"this.isShow"}]},[i("p",[e._v(" "+e._s("1"===e.params.appealFlag?"申诉":"不申诉，认同并及时整改")+" ")])])],1),i("ta-form-item",{staticStyle:{position:"relative"},attrs:{label:"上传附件","field-decorator-id":"file",require:!e.isShow&&"1"===this.appealFlag&&0===this.fileList.length}},[[i("div",[e.isShow?e._e():i("div",[i("div",[i("ta-radio-group",{attrs:{cancelChecked:!0,options:e.plainOptions},model:{value:e.fileType,callback:function(t){e.fileType=t},expression:"fileType"}})],1),i("ta-upload",{attrs:{multiple:!0,"file-list":e.addList,"before-upload":e.beforeUpload,"show-upload-list":!1,accept:"image/jpg,image/png,image/jpeg,IMAGE/JPG,IMAGE/PNG,IMAGE/JPEG"}},[i("ta-button",[i("ta-icon",{attrs:{type:"upload"}}),e._v(" 点击上传 ")],1)],1),i("span",{staticStyle:{color:"#999999","font-size":"12px"}},[e._v(" 只能上传 jpg/png/jpeg 文件 "),i("span",{directives:[{name:"show",rawName:"v-show",value:this.processConfig.configNum,expression:"this.processConfig.configNum"}]},[e._v("，且不超过"+e._s(this.processConfig.configNum+this.processConfig.configUnit))])])],1),i("div",{directives:[{name:"show",rawName:"v-show",value:e.fileList.length>0,expression:"fileList.length > 0"}],staticClass:"fileListStyle"},e._l(e.fileList,(function(t){return i("div",{key:t.uid||t.fileId,staticClass:"fileItem",staticStyle:{cursor:"pointer"},on:{mouseover:function(a){return e.debounceShowPic(t)},mouseout:function(a){return e.hidePic(t)},click:function(a){return a.stopPropagation(),e.fnDownloadImg(t)}}},[i("ta-icon",{attrs:{type:"link"}}),i("span",{staticClass:"fileName"},[e._v(e._s(t.name||t.fileName))]),i("div",{staticClass:"fileActions"},[i("ta-icon",{directives:[{name:"show",rawName:"v-show",value:t.fileId,expression:"item.fileId"}],staticClass:"downloadIcon",attrs:{type:"download"},on:{click:function(a){return a.stopPropagation(),e.downloadImage(t)}}}),i("ta-icon",{directives:[{name:"show",rawName:"v-show",value:!e.isShow,expression:"!isShow"}],staticClass:"delIcon",attrs:{type:"delete"},on:{click:function(a){return a.stopPropagation(),function(a){return e.handleRemove(t,a)}.apply(null,arguments)}}})],1)],1)})),0),i("div",{directives:[{name:"show",rawName:"v-show",value:e.showFile,expression:"showFile"}],staticClass:"filePreview"},[i("img",{ref:"imgPreview",attrs:{src:e.fileUrl,height:"250",width:"100%"}})])])]],2),i("ta-form-item",{attrs:{label:"申诉理由","field-decorator-id":"aaz560","init-value":e.paramDetails.aaz560,require:!e.isShow&&"1"===this.appealFlag}},[this.isShow?i("div",[i("p",[e._v(" "+e._s(e.paramDetails.aaz560)+" ")])]):i("div",[i("ta-textarea",{attrs:{value:e.paramDetails.aaz560,placeholder:"请输入申诉理由",rows:5,"show-length":!0},on:{change:e.textareaChange}})],1)]),i("ta-form-item",{attrs:{label:"实际处理人","init-value":"0","field-decorator-id":"currPersChoose"}},[i("ta-radio-group",{directives:[{name:"show",rawName:"v-show",value:!this.isShow,expression:"!this.isShow"}],attrs:{size:"small"},on:{change:e.handleChange}},[i("ta-radio",{attrs:{value:"0"}},[e._v("本人")]),i("ta-radio",{attrs:{value:"1"}},[e._v("其他人 "),i("ta-select",{staticStyle:{"margin-left":"10px",width:"200px"},attrs:{disabled:e.selectDisabled,virtual:!0,showSearch:"",optionLabelProp:"children",mode:"combobox",placeholder:"医生名称筛选",options:e.doctorList,filterOption:e.filterOption,allowClear:""},model:{value:e.handPersChoose,callback:function(t){e.handPersChoose=t},expression:"handPersChoose"}})],1)],1),i("div",{directives:[{name:"show",rawName:"v-show",value:this.isShow,expression:"this.isShow"}]},[i("p",[e._v(" "+e._s(e.params.handPers||e.params.currPersName)+" ")])])],1)],1),i("div",{staticStyle:{width:"100%","background-color":"#FFFFFF","text-align":"left"}},[i("ta-checkbox",{directives:[{name:"show",rawName:"v-show",value:"3"!=e.params.dataType,expression:"params.dataType != '3'"}],attrs:{disabled:e.isShow,checked:e.syncInfo},on:{change:e.changeSyncInfo}},[e._v(" 将附件和申诉理由应用于当前患者该违规项目(任务状态也会同步变更) ")])],1)],1),i("ta-modal",{attrs:{title:this.fileName,height:"600px",width:"80%",bodyStyle:{textAlign:"center"},footer:null},model:{value:e.imgVisible,callback:function(t){e.imgVisible=t},expression:"imgVisible"}},[i("img",{attrs:{src:e.imgSrc},on:{click:e.downloadImage}})])],1)},_=[],D=a(48534),z=(a(36133),{name:"approval",components:{},props:{visible:{type:Boolean,default:!1},params:{type:Object,default:function(){return{}}}},data:function(){return{title:"申诉审批",isReturnedData:!1,isShow:!1,paramDetails:{},BasePath:l.getBasePath(),fileList:[],addList:[],removeList:[],doctorList:[],fileUrl:"",basePath:l.getBasePath(),showFile:!1,syncInfo:!1,imgVisible:!1,imgSrc:"",fileName:"",handPersChoose:"",fileType:"",emrLinkEnabled:!1,emrVersion:null,hisChromeAddress:null,hisEmrApiUrl:null,selectDisabled:!0,appealFlag:"1",processConfig:{},debounceShowPic:null,plainOptions:[]}},watch:{visible:{handler:function(t){var e=this;t&&this.$nextTick((function(){e.title="申诉处理",e.queryProcessConfig(),e.fnQueryDocter(),l.getRowDetails({aaz213:e.params.aaz213},(function(t){var a,i;(e.paramDetails=t.data.appeal,"6"===e.paramDetails.handStas?e.isReturnedData=!0:"3"!==e.paramDetails.handStas&&"4"!==e.paramDetails.handStas&&"7"!==e.paramDetails.handStas&&"10"!==e.paramDetails.handStas&&"11"!==e.paramDetails.handStas||(e.isShow=!0,e.title="申诉详情"),e.params.handPers&&-1==(null===(a=e.doctorList.find((function(t){return t.value==e.params.currPers})))||void 0===a?void 0:a.label.indexOf(e.params.handPers)))?(e.paramDetails.currPersChoose="1",e.selectDisabled=!1,e.handPersChoose=(null===(i=e.doctorList.find((function(t){return-1!=t.label.indexOf(e.params.handPers)})))||void 0===i?void 0:i.value)||""):e.paramDetails.currPersChoose="0";e.form.setFieldsValue(e.paramDetails),"-1"!=e.paramDetails.syncInfo&&"3"!=e.params.dataType&&(e.syncInfo=!0),e.fileList=t.data.appeal.files,e.appealFlag=e.paramDetails.appealFlag,e.paramDetails.emrLinkEnabled&&(e.emrLinkEnabled=!0,e.emrVersion=e.paramDetails.emrVersion,e.hisChromeAddress=e.paramDetails.hisChromeAddress,e.hisEmrApiUrl=e.paramDetails.hisEmrApiUrl)}))}))}}},mounted:function(){},created:function(){this.debounceShowPic=this.debounce(this.showPic,1500)},methods:{moment:u(),textareaChange:function(t){this.paramDetails.aaz560=t.target.value},queryProcessConfig:function(){var t=this,e={akb020:this.params.partAkb020};this.Base.submit(null,{url:"miimCommonRead/queryProcessConfig",data:e},{successCallback:function(e){var a,i,s=e.data;t.processConfig=s.data,(null!==(a=t.processConfig)&&void 0!==a&&a.attachConfigTypeOne||null!==(i=t.processConfig)&&void 0!==i&&i.attachConfigTypeTwo)&&("3"==t.params.dataType?t.plainOptions=s.data.attachConfigTypeTwo.split(",").map((function(t){return{label:t,value:t}})):t.plainOptions=s.data.attachConfigTypeOne.split(",").map((function(t){return{label:t,value:t}})))},failCallback:function(e){t.$message.error("查询配置失败")}})},fnQueryDocter:function(){var t=this,e={akb020:this.params.akb020};this.Base.submit(null,{url:"miimCommonRead/queryDoctorRpcList",data:e,autoValid:!1},{successCallback:function(e){t.doctorList=e.data.resultData.map((function(t){return{value:t.value,label:t.label+"-"+t.value}}))},failCallback:function(e){t.$message.error("医师数据加载失败")}})},handleChange:function(t){this.selectDisabled="1"!=t.target.value,this.selectDisabled&&(this.handPersChoose="")},appealFlagChange:function(t){this.appealFlag=t.target.value},downloadImage:function(t){var e=this;return(0,D.Z)(regeneratorRuntime.mark((function a(){var i,s,r,o,n;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return null!==t&&void 0!==t&&t.fileId&&(e.imgSrc=e.basePath+"/appeal/drPoint/getFile?fileId="+t.fileId),a.prev=1,a.next=4,fetch(e.imgSrc,{mode:"cors"});case 4:if(i=a.sent,i.ok){a.next=8;break}return e.$message.error("网络响应失败，请稍后重试，状态码:",i.status),a.abrupt("return");case 8:if(s=i.headers.get("Content-Type"),s&&s.startsWith("image/")){a.next=12;break}return e.$message.error("获取的资源不是图片，Content-Type:",s),a.abrupt("return");case 12:return a.next=14,i.blob();case 14:r=a.sent,o=URL.createObjectURL(r),n=document.createElement("a"),n.href=o,n.download=e.fileName||t.fileName,n.style.display="none",document.body.appendChild(n),n.click(),document.body.removeChild(n),URL.revokeObjectURL(o),a.next=29;break;case 26:a.prev=26,a.t0=a["catch"](1),e.$message.error("下载图片时发生错误:",a.t0);case 29:case"end":return a.stop()}}),a,null,[[1,26]])})))()},getFiles:function(){if(this.isShow){var t=l.getPicZipUrl()+"?aaz213="+this.params.aaz213;window.location.href=t}},handleRemove:function(t,e){e.stopPropagation();var a=this.fileList.indexOf(t);t.fileId?this.removeList.push(t.fileId):this.addList=this.addList.filter((function(e){return e.uid!==t.uid}));var i=this.fileList.slice();i.splice(a,1),this.debounceShowPic&&this.debounceShowPic.cancel(),this.showFile=!1,this.fileUrl="",this.fileList=i},beforeUpload:function(t){var e="MB"===this.processConfig.configUnit?1024*this.processConfig.configNum:this.processConfig.configNum;if(t.size/1024>e)return this.$message.warning("请选择不超过"+this.processConfig.configNum+this.processConfig.configUnit+"的文件"),!1;var a=t.name.split("."),i=a[a.length-1].toLowerCase();if(-1===["jpg","png","jpeg"].indexOf(i.toLowerCase()))return this.$message.warning("只能上传jpg/png/jpeg文件"),!1;var s=this.nameRuleCommon(this.processConfig.nameRule1),r=this.nameRuleCommon(this.processConfig.nameRule2),o=this.nameRuleCommon(this.processConfig.nameRule3),n=[s,r,o].filter(Boolean),l=n.join("_"),c="0";try{if(Array.isArray(this.fileList)&&this.fileList.length>0){var u=this.fileList.filter((function(t){var e=(null===t||void 0===t?void 0:t.name)||(null===t||void 0===t?void 0:t.fileName);return"string"===typeof e&&e.substring(0,e.lastIndexOf("_"))===l}));if(Array.isArray(u)&&u.length>0){var d=u.map((function(t){var e=(null===t||void 0===t?void 0:t.name)||(null===t||void 0===t?void 0:t.fileName);if("string"!==typeof e)return 0;var a=e.substring(0,e.lastIndexOf("."));return parseInt(a.substring(a.lastIndexOf("_")+1),10)||0}));c=d.reduce((function(t,e){return Math.abs(e)>Math.abs(t)?e:t}),0)}}}catch(p){this.$message.error("错误处理文件列表:",p),c=0}var h=l?"".concat(l,"_").concat(Number(c)+1,".").concat(i):"".concat(Number(c)+1,".").concat(i),f=this.renameFile(t,h);return t.newFileName=h,f.uid=t.uid,this.fileList=this.fileList.concat(f),this.addList=this.addList.concat(t),!1},nameRuleCommon:function(t){return"fileType"===t?this.fileType:"akc190"===t||"akc191"===t?this.params["akc190"]:this.params[t]},renameFile:function(t,e){return new File([t],e,{lastModified:t.lastModified,lastModifiedDate:t.lastModifiedDate,name:e,size:t.size,type:t.type,webkitRelativePath:t.webkitRelativePath,uid:t.uid,percent:0,originFileObj:t})},renameFiles:function(t,e){return{lastModified:t.lastModified,lastModifiedDate:t.lastModifiedDate,name:e,size:t.size,type:t.type,webkitRelativePath:t.webkitRelativePath,uid:t.uid,percent:0,originFileObj:t}},showPic:function(t){t.fileId&&(this.showFile=!0,this.fileUrl=this.basePath+"/appeal/drPoint/getFile?fileId="+t.fileId)},debounce:function(t,e){var a,i=function(){for(var i=this,s=arguments.length,r=new Array(s),o=0;o<s;o++)r[o]=arguments[o];a&&clearTimeout(a),a=setTimeout((function(){t.apply(i,r)}),e)};return i.cancel=function(){a&&clearTimeout(a)},i},hidePic:function(t){this.showFile=!1,this.debounceShowPic.cancel()},fnDownloadImg:function(t){t.fileId&&(this.imgSrc=this.basePath+"/appeal/drPoint/getFile?fileId="+t.fileId,this.fileName=t.name||t.fileName,this.imgVisible=!0)},changeSyncInfo:function(t){var e=this.form.getFieldsValue(),a=e.aaz560;a&&(this.paramDetails.aaz560=a),this.syncInfo=t.target.checked},handleSave:function(t){var e=this;this.form.validateFields((function(a){var i;if(!a||(null===a||void 0===a||null===(i=a.file)||void 0===i?void 0:i.errors.length)>0&&e.fileList.length>0){var s=e.paramDetails,r=e.form.getFieldsValue();if("1"===r.currPersChoose&&!e.handPersChoose)return void e.$message.warning("请选择实际处理人");if("1"===e.appealFlag&&0===e.addList.length&&0===e.fileList.length)return void e.$message.warning("请上传申诉文件");s.aaz560=r.aaz560||"",s.syncInfo=e.syncInfo?"1":"0",e.params.syncInfo=s.syncInfo,s.files=e.addList,s.aaz263=e.params.currentUser,s.appealFlag=e.appealFlag,s.newNames=e.addList.map((function(t){return t.newFileName})).join("$$"),s.ids=JSON.stringify(e.removeList),"1"===r.currPersChoose?s.handPers=e.handPersChoose:s.handPers=e.params.currPers,l.saveRow(s,(function(a){e.addList=[],e.removeList=[],l.getRowDetails({aaz213:e.params.aaz213},(function(t){e.fileList=t.data.appeal.files})),"1"===t?e.$message.success("保存成功"):e.$nextTick((function(){var t={aaz213s:[e.params.aaz213],aaz263:e.params.currPers,opterType:"2",clientId:e.params.clientId};l.checkPromptState(t,(function(t){t.data.data&&"1"===t.data.data.promptState?e.$confirm({content:t.data.data.tip,okText:"取消",cancelText:"强制操作",onOk:function(){},onCancel:function(){e.$emit("handleSubmit",e.params),e.handleClose("2")}}):(e.$emit("handleSubmit",e.params),e.handleClose("2"))}))}))}))}}))},handleClose:function(t){this.form.resetFields(),this.isReturnedData=!1,this.isShow=!1,this.fileList=[],this.removeList=[],this.addList=[],this.syncInfo=!1,this.handPersChoose="",this.fileType="",this.$emit("handleClose",t)},openEmrQuery:function(){QClient.exec(this.hisChromeAddress,this.hisEmrApiUrl)},filterOption:function(t,e){return e.componentOptions.children[0].text.indexOf(t)>=0||e.componentOptions.propsData.value.indexOf(t)>=0}}}),I=z,N=(0,g.Z)(I,L,_,!1,null,"a473069e",null),F=N.exports,T=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("ta-modal",{attrs:{title:t.title,visible:t.visible,height:450,width:850},on:{cancel:t.handleClose}},[a("div",{staticStyle:{height:"100%"}},[a("ta-border-layout",{attrs:{"show-border":!1,"header-cfg":{showBorder:!1}}},[a("div",[a("ta-big-table",{ref:"viewRecordTable",attrs:{height:"380px","auto-resize":"",resizable:"","highlight-hover-row":"",border:"",align:"center","header-align":"center",size:"mini",data:t.viewRecordData}},[a("ta-big-table-column",{attrs:{type:"seq",fixed:"left",width:"50px",title:"序号",align:"center"}}),a("ta-big-table-column",{attrs:{field:"aae040",align:"center",sortable:"","show-overflow":"",title:"审核时间","min-width":"100px"}}),a("ta-big-table-column",{attrs:{field:"ape800",align:"center",sortable:"","show-overflow":"",title:"审核结果","min-width":"100px"},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.row;return["0"==i.ape800?a("span",{staticStyle:{color:"#0f990f"}},[a("ta-icon",{attrs:{type:"check-circle",theme:"twoTone",twoToneColor:"#52c41a"}}),t._v("审核通过")],1):"1"==i.ape800?a("span",{staticStyle:{color:"#F59A23"}},[a("ta-icon",{attrs:{type:"question-circle",theme:"twoTone",twoToneColor:"#F59A23"}}),t._v("可疑提醒")],1):"2"==i.ape800?a("span",{staticStyle:{color:"#FF0000"}},[a("ta-icon",{attrs:{type:"exclamation-circle",theme:"twoTone",twoToneColor:"#FF0000"}}),t._v("违规提醒")],1):"3"==i.ape800?a("span",{staticStyle:{color:"#ffc49a"}},[a("ta-icon",{attrs:{type:"bell",theme:"twoTone",twoToneColor:"#e1ba91"}}),t._v("仅提醒")],1):a("div",[t._v(" — ")])]}}])}),a("ta-big-table-column",{attrs:{field:"ape893",align:"center","show-overflow":"",title:"医护操作","min-width":"80px"}},[a("ta-big-table-column-filter",{attrs:{"filter-props":{placeholder:""}}})],1),a("ta-big-table-column",{attrs:{field:"ykz041",align:"center","show-overflow":"",title:"操作人","min-width":"80px"}},[a("ta-big-table-column-filter",{attrs:{"filter-props":{placeholder:""}}})],1),a("template",{slot:"bottomBar"},[a("ta-pagination",{ref:"gridPager",staticStyle:{"text-align":"right"},attrs:{"default-page-size":500,"hide-on-single-page":!0,"page-size-options":["30","50","100","200","500"],"data-source":t.viewRecordData,params:t.infoPageParams,url:"hiddscgPoint/getViewRecordData"},on:{"update:dataSource":function(e){t.viewRecordData=e},"update:data-source":function(e){t.viewRecordData=e}}})],1)],2)],1)])],1),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("div",{staticStyle:{display:"flex","justify-content":"center"}},[a("ta-button",{staticStyle:{"text-align":"center"},on:{click:t.handleClose}},[t._v(" 关闭 ")])],1)])])},R=[],V=(a(95082),{name:"viewRecord",props:{title:{type:String,default:"匹配预警记录"},visible:{type:Boolean,default:!1},param:{type:Object,default:function(){return{}}}},data:function(){return{viewRecordData:[]}},watch:{visible:{handler:function(t){var e=this;t&&this.$nextTick((function(){e.$refs.gridPager.loadData()}))}}},mounted:function(){},methods:{infoPageParams:function(){return this.param},handleClose:function(){this.viewRecordData=[],this.$emit("handleClose")}}}),B=V,$=(0,g.Z)(B,T,R,!1,null,"ceb35a34",null),A=$.exports,M=[{value:"0",label:"待处理",checked:!0,color1:"orange",color2:"#ff8d00"},{value:"1",label:"待提交",checked:!1,color1:"yellow",color2:"#FFCA33"},{value:"2",label:"已提交",checked:!1,color1:"green",color2:"#87d068"},{value:"3",label:"全部",checked:!1,color1:"blue",color2:"#2db7f5"}],O={name:"appealForDoc",components:{ViewRecord:A,Approval:F,TagChange:v,TaTitle:P},data:function(){return{col:{xs:2,sm:2,md:2,lg:3,xl:4,xxl:4},aae043List:[],lastAae043:"",needHandCount:0,needSubmitCount:0,totalCount:0,admissionNumList:[],ruleNameList:[],patientNameList:[],objNameList:[],doctorList:[],groupList:[],peopleList:[],deptList:[],costTypeList:[],nameDisable:!1,formShowAll:!0,batchModefyVisible:!1,statusArr:M,tagValues:["0","1"],hosList:[],dataSource:[],filterOptions:[],docLogList:[],uploadVisible4:!1,drawerVisible:!1,mateRow:{},optionConfig:{value:"value",label:function(t){return"".concat(t.label," (").concat(t.value," ").concat(t.aae386?t.aae386:"未知科室 "+t.aaz307," ").concat(t.akb021?t.akb021:"未知医院 "+t.akb020,")")}},pageUrl:l.getPageUrl(),approvalVisible:!1,rowData:{},currPers:"",accountGroup:"N",akb020:"",isDeepBlue:!1,clientId:"",viewerVal:0,isShow:!1,images:[],basePath:l.getBasePath()+"/complainFile/",statusColors:{1:"#f5222d",2:"#faad14",3:"#722ed1",4:"#52c41a"}}},computed:{formBoxStyle:function(){return this.formShowAll?{height:"auto"}:{height:"50px"}},filterStr:function(){if(!this.tagValues||0===this.tagValues.length)return"1,2,3,4,5,6,7,8,9,10,11";var t={0:["1","5","6","9"],1:["2"],2:["3","4","8","10"]},e=this.tagValues.map((function(e){return t[e]||[]})).flat();return 0===e.length?"1,2,3,4,5,6,7,8,9,10,11":(0,r.Z)(new Set(e)).join(",")}},watch:{},mounted:function(){var t=this;this.$nextTick((function(){t.tagValues=["0","1"];var e=t.$route.query;e&&e.aaz263&&(t.currPers=e.aaz263);var a=navigator.userAgent;a.indexOf("DeepBlue")>-1?(t.isDeepBlue=!0,QClient.DBPNSGetClientId((function(e){t.clientId=e.data,t.fnInit()}))):t.fnInit()}))},methods:{moment:u(),onClose:function(){this.drawerVisible=!1},setContainer:function(){return document.getElementById("parentDom")},viewHistory:function(t){var e=this;t.ykc610||t.ykc610V||this.$message.error("当前费用流水号为空！请检查数据！"),this.drawerVisible=!0,l.queryHistory({aaz213:t.aaz213},(function(t){e.docLogList=t.data.list}))},handleSearch:function(t){var e=this;t&&l.queryPeople({doctorName:t,aaz309:this.form.getFieldsValue().aaz309,sign:"restrict"},(function(t){e.peopleList=t.data.list}))},handleModefy:function(t){var e=this;this.mateRow=t,this.batchModefyVisible=!0,this.$nextTick((function(){e.form1.setFieldsValue({currPersName:e.mateRow.currPersName})}))},handleBatchModefy:function(){var t=this,e=[];if(this.mateRow.aaz213)e=[this.mateRow.aaz213];else{var a=this.$refs.xTable.getCheckboxRecords();e=a.map((function(t){return t.aaz213}))}this.form1.validateFields((function(a){if(!a&&e.length>0){var i=t.form1.getFieldsValue(),s={aaz213s:e,auditPers:i.doctorId,aaz263:t.currPers,origPers:i.currPersName,syncInfo:t.mateRow.aaz213?t.mateRow.syncInfo:""};l.batchUpdateCurrPers(s,(function(e){t.$message.success("修改成功"),t.closeBatchModefyModel()}))}}))},closeBatchModefyModel:function(){this.form1.resetFields(),this.mateRow={},this.batchModefyVisible=!1,this.queryTableData()},updateCurrPers:function(){var t=this,e=this.$refs.xTable.getCheckboxRecords();0!==e.length?(this.batchModefyVisible=!0,this.$nextTick((function(){t.form1.setFieldsValue({currPersName:Array.from(new Set(e.map((function(t){return t.currPersName})))).join(",")})}))):this.$message.warning("请选择需要修改的数据")},fnQueryGroup:function(){var t=this;this.Base.submit(null,{url:"miimCommonRead/queryGroupDicByAppealForDoc",data:{aaz263:this.currPers},autoValid:!1},{successCallback:function(e){var a,i;(null!==(a=e.data)&&void 0!==a&&a.accountGroup?t.accountGroup=e.data.accountGroup:t.accountGroup="N","tongji"===faceConfig.approvalHandleType)?t.Base.submit(null,{url:"nightAudit/queryGroupDicForDoc",data:{aaz263:t.currPers},autoQs:!1},{successCallback:function(e){var a=e.data;a.data.length>0?t.groupList=a.data:t.groupList=[]},failCallback:function(e){t.$message.error("分组数据加载失败")}}):(null===(i=e.data)||void 0===i?void 0:i.resultData.length)>0?t.groupList=e.data.resultData:t.groupList=[]},failCallback:function(e){t.$message.error("医师数据加载失败")}})},fnQueryHos:function(){var t=this,e={url:"/miimCommonRead/queryHospRpcList",autoValid:!0},a={successCallback:function(e){t.hosList=e.data.resultData,t.fnQueryGroup()},failCallback:function(e){t.$message.error("医院数据加载失败")}};this.Base.submit(null,e,a)},viewRecords:function(t){t.ykc610||t.ykc610V||this.$message.error("当前费用流水号为空！请检查数据！"),this.uploadVisible4=!0,this.mateRow=t},handleClose4:function(t){this.mateRow={},this.uploadVisible4=!1},fnInit:function(){var t=this;this.fnQueryHos(),this.getAKb020ByJobNumber(),this.queryAae043List((function(){t.aae043List.length>0&&t.form.setFieldsValue({aae043:t.aae043List[0].value}),t.queryTableData()}))},getAKb020ByJobNumber:function(){var t=this;this.Base.submit(null,{url:"/appeal/common/getAKb020ByJobNumber",data:{loginId:this.currPers}},{successCallback:function(e){t.akb020=e.data.akb020},failCallback:function(e){t.$message.error(e.errors[0])}})},tableCheckboxMethod:function(t){var e=t.row;return"1"===e.handStas||"2"===e.handStas||"5"===e.handStas||"6"===e.handStas},fnDownloadFile:function(t){var e=document.getElementById("downloadButton");e.disabled||(e.disabled=!0,window.location.href=l.getPicZipUrl()+"?aaz213="+t,setTimeout((function(){e.disabled=!1}),2e3))},setPopupContainer:function(t){return t.parentNode},tooltipMethod:function(t){t.items;var e=t.row,a=(t.rowIndex,t.$rowIndex,t.column,t.columnIndex);t.$columnIndex,t.type,t.cell,t.$event;if(8===a)return e.fileNames},filterMethod:function(t){var e=t.option,a=t.row;return a.handStas===e.data},getProcess:function(){var t=this,e={aae043:this.form.getFieldsValue().aae043};this.isDeepBlue&&(e.currPers=this.currPers,e.clientId=this.clientId),l.getProcess(e,(function(e){t.needHandCount=e.data.progress.needHandCount,t.needSubmitCount=e.data.progress.needSubmitCount,t.totalCount=e.data.progress.totalCount}))},admissionNumChange:function(t,e){t||(this.nameDisable=!1),this.nameDisable=!0},admissionNumSearch:function(t){var e=this;t&&l.getAdmissionNumList({aae043:this.form.getFieldsValue().aae043,currPers:this.currPers,searchVal:t,igronCurrPer:"Y"==this.accountGroup},(function(t){e.admissionNumList=t.data.list}))},deptChange:function(t,e){},deptSearch:function(t){var e=this;t&&l.getDeptList({aae043:this.form.getFieldsValue().aae043,currPers:this.currPers,searchVal:t,igronCurrPer:"Y"==this.accountGroup},(function(t){e.deptList=t.data.list}))},costTypeSearch:function(t){var e=this;t&&l.getCostTypeList({aae043:this.form.getFieldsValue().aae043,currPers:this.currPers,searchVal:t,igronCurrPer:"Y"==this.accountGroup},(function(t){e.costTypeList=t.data.list}))},ruleNameChange:function(t,e){},ruleNameSearch:function(t){var e=this;t&&l.getRuleNameList({aae043:this.form.getFieldsValue().aae043,currPers:this.currPers,searchVal:t,igronCurrPer:"Y"==this.accountGroup},(function(t){e.ruleNameList=t.data.list}))},patientNameChange:function(t,e){},patientNameSearch:function(t){var e=this;t&&l.getPatintNameList({aae043:this.form.getFieldsValue().aae043,currPers:this.currPers,searchVal:t,igronCurrPer:"Y"==this.accountGroup},(function(t){e.patientNameList=t.data.list}))},objNameChange:function(t,e){},objNameSearch:function(t){var e=this;t&&l.getObjNameList({aae043:this.form.getFieldsValue().aae043,currPers:this.currPers,searchVal:t,igronCurrPer:"Y"==this.accountGroup},(function(t){e.objNameList=t.data.list}))},doctorChange:function(t,e){},doctorSearch:function(t){var e=this;t&&l.getDoctorList({aae043:this.form.getFieldsValue().aae043,currPers:this.currPers,searchVal:t,igronCurrPer:"Y"==this.accountGroup},(function(t){e.doctorList=t.data.list}))},queryAae043List:function(t){var e=this,a=this.tagValues;l.queryAae043List({currPers:this.currPers,status:a,clientId:this.clientId},(function(a){e.aae043List=a.data.data.map((function(t){return{value:t,label:t}})),t&&t()}))},formShowAllChange:function(){this.formShowAll=!this.formShowAll},changeTag:function(t){var e=this;this.tagValues=t,this.queryAae043List((function(){e.queryTableData()}))},batchSubmit:function(){var t=this,e=this.$refs.xTable.getCheckboxRecords();0===e.length&&this.$message.warning("请选择需要提交的数据");var a=e.filter((function(t){return t.aaz560&&t.fileNum>0||"0"===t.appealFlag}));if(e.length!=a.length&&this.$message.warning("请将已选中的申诉理由或材料填写完整！为空条数："+(e.length-a.length)),a.length>0){var i={aaz213s:a.map((function(t){return t.aaz213})),aaz263:this.currPers,opterType:"3",clientId:this.clientId};l.checkPromptState(i,(function(e){e.data.data&&"1"===e.data.data.promptState?t.$confirm({content:e.data.data.tip,okText:"取消",cancelText:"强制操作",onOk:function(){},onCancel:function(){l.batchSubmit({aaz213s:a.map((function(t){return t.aaz213})),aaz263:t.currPers,clientId:t.clientId},(function(e){t.$message.success("提交成功"),t.queryTableData()}))}}):(l.batchSubmit({aaz213s:a.map((function(t){return t.aaz213})),aaz263:t.currPers,clientId:t.clientId},(function(e){t.$message.success("提交成功"),t.queryTableData()})),t.updateState("2",row.aaz213))}))}},handleRow:function(t){this.approvalVisible=!0,this.rowData=t,this.rowData.partAkb020=this.akb020,this.rowData.clientId=this.clientId,this.rowData.currentUser=this.currPers},prehandleCheck:function(t){var e=this,a={aaz213s:[t.aaz213],aaz263:this.currPers,opterType:"1",clientId:this.clientId};l.checkPromptState(a,(function(a){a.data.data&&"1"===a.data.data.promptState?e.$confirm({content:a.data.data.tip,okText:"取消",cancelText:"强制操作",onOk:function(){},onCancel:function(){e.handleRow(t)}}):(e.handleRow(t),e.updateState("1",t.aaz213))}))},updateState:function(t,e){var a={state:t,aaz213:e,clientId:this.clientId};l.updateState(a,(function(t){}))},preSubmitCheck:function(t){var e=this;if(t.aaz560||"1"!==t.appealFlag)if((null===t||void 0===t?void 0:t.fileNum)<1&&"1"===t.appealFlag)this.$message.warning("请上传申诉材料！");else{var a={aaz213s:[t.aaz213],aaz263:this.currPers,opterType:"2",clientId:this.clientId};l.checkPromptState(a,(function(a){a.data.data&&"1"===a.data.data.promptState?e.$confirm({content:a.data.data.tip,okText:"取消",cancelText:"强制操作",onOk:function(){},onCancel:function(){e.handleSubmit(t)}}):e.handleSubmit(t),e.$nextTick((function(){e.queryTableData()}))}))}else this.$message.warning("请填写申诉理由！")},handleSubmit:function(t){var e=this;l.batchSubmit({aaz213s:[t.aaz213],syncInfo:t.syncInfo,aaz263:this.currPers,clientId:this.clientId},(function(t){e.$message.success("提交成功")}))},backRow:function(t){var e=this;t.currentUser=this.currPers,l.reBackRow(t,(function(t){e.queryTableData(),e.$message.success("撤回成功")}))},showFileList:function(t){t.fileListShow=!0,this.$refs.xTable.updateStatus({row:t})},hideFileList:function(t){t.fileListShow=!1,this.$refs.xTable.updateStatus({row:t})},pageParams:function(){var t=this.form.getFieldsValue();t.status=this.tagValues,this.isDeepBlue&&(t.currPers=this.currPers,t.clientId=this.clientId);var e=this.$refs.gridPager.getPagerInfo();return Object.assign(t,e),t},queryTableData:function(){var t=this;this.form.validateFields((function(e){e||(t.getProcess(),t.$refs.gridPager.loadData((function(e){t.dataSource=e.data.pageBean.list.map((function(t){return t.fileListShow=!1,t.hospitalizationNum=u()(t.aae031).diff(u()(t.aae030),"day"),t}))})))}))},resetForm:function(){this.form.resetFields(),this.form.setFieldsValue({})},handleClose2:function(t){"1"==t&&this.updateState("0",this.rowData.aaz213),this.approvalVisible=!1,this.rowData={},this.queryTableData()},cellStyle:function(t){var e=t.row,a=(t.rowIndex,t.column);t.columnIndex;if("chainRate"===a.property){if(e.chainRate>0)return{color:"#0F990F"};if(e.chainRate<0)return{color:"#E4393C"}}},imageViewerHandle:function(t){var e=this;t&&l.getFileInfo({aaz213:t},(function(t){e.images=[],t.data.data&&(t.data.data.forEach((function(t){e.images.push({url:e.basePath+t.savePath.replace(/\\/g,"/"),title:t.fileName})})),e.isShow=!0)}))},shown:function(){},filterOption:function(t,e){return e.componentOptions.children[0].text.indexOf(t)>=0}}},q=O,U=(0,g.Z)(q,i,s,!1,null,"1d58b3f1",null),j=U.exports}}]);
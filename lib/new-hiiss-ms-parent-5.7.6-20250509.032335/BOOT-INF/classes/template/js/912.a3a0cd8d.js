"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[912],{98526:function(e,t,a){a.d(t,{Z:function(){return C}});var i=function(){var e=this,t=this,a=t.$createElement,i=t._self._c||a;return i("div",{staticClass:"fit"},[i("div",{staticClass:"query-header"},[i("div",{ref:"formBox",staticClass:"query-header-left",style:t.formBoxStyle},[i("ta-form",{attrs:{autoFormCreate:function(t){e.form=t},layout:"horizontal",col:t.col,"label-width":"80px",formLayout:!0}},[i("ta-form-item",{attrs:{fieldDecoratorId:"allDate",fieldDecoratorOptions:{rules:[{required:!0,message:"必须选取日期"}]},required:!0,"init-value":t.rangeValue}},[i("span",{attrs:{slot:"label"},slot:"label"},[t._v("时间范围")]),i("ta-range-picker",{staticStyle:{width:"100%"},attrs:{"allow-one":!0}},[i("ta-icon",{attrs:{slot:"suffixIcon",type:"test"},slot:"suffixIcon"})],1)],1),i("ta-form-item",{attrs:{fieldDecoratorId:"scene",label:"审核场景",fieldDecoratorOptions:{rules:[{required:!0,message:"必须选取审核场景"}]},required:!0}},[i("ta-select",{attrs:{placeholder:"请选择开单场景","collection-type":"AAE500","collection-filter":t.filterList,reverseFilter:!0},on:{change:t.handleChange}})],1),i("ta-form-item",{attrs:{fieldDecoratorId:"akb020"}},[i("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[t._v("院区标识")]),i("ta-select",{attrs:{placeholder:"院区选择",disabled:t.paramsDisable.akb020,allowClear:"",options:t.akb020List}})],1),i("ta-form-item",{attrs:{fieldDecoratorId:"departmentCode",placeholder:"按科室编码或名称查询"}},[i("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[t._v("科室名称")]),i("ta-select",{attrs:{"show-search":!0,allowClear:"",placeholder:"科室名称筛选",options:t.ksList}})],1),i("ta-form-item",{attrs:{fieldDecoratorId:"doctorCode"}},[i("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[t._v("医师名称")]),i("ta-select",{attrs:{showSearch:"",optionLabelProp:"children",mode:"combobox",placeholder:"医生名称筛选",options:t.doctorList,filterOption:t.filterOption,allowClear:""}})],1),i("ta-form-item",{attrs:{fieldDecoratorId:"medinsuType"}},[i("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[t._v("医保类型")]),i("ta-select",{attrs:{placeholder:"医保类型选择","collection-type":"AAE141",allowClear:""}})],1),i("ta-form-item",{attrs:{fieldDecoratorId:"insuType"}},[i("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[t._v("险种类型")]),i("ta-select",{attrs:{placeholder:"险种类型选择","collection-type":"AAE140",allowClear:""}})],1),i("ta-form-item",{attrs:{fieldDecoratorId:"result"}},[i("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[t._v("预审结果")]),i("ta-select",{attrs:{showSearch:"",mode:"multiple","collection-type":"APE800","collection-filter":"0",maxTagCount:1,placeholder:"预审结果选择",allowClear:""}})],1),i("ta-form-item",{attrs:{fieldDecoratorId:"ruleCode"}},[i("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[t._v("规则名称")]),i("ta-select",{attrs:{showSearch:"",placeholder:"规则名称筛选",options:t.ruleData,allowClear:""}})],1),i("ta-form-item",{attrs:{fieldDecoratorId:"ake001",label:"三目名称"}},[i("ta-input",{attrs:{placeholder:"请输入三目名称或编码"}})],1),i("ta-form-item",{attrs:{fieldDecoratorId:"ake003"}},[i("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[t._v("三目类型")]),i("ta-select",{attrs:{placeholder:"三目类型选择","collection-type":"AKE003",allowClear:""}})],1),i("ta-form-item",{attrs:{fieldDecoratorId:"inpatientNo"}},[i("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[t._v("住院号")]),i("ta-input",{staticStyle:{height:"32px"},attrs:{placeholder:"请输入住院号或就诊号"}})],1),i("ta-form-item",{attrs:{"field-decorator-id":"operate",label:"医护操作"}},[i("ta-select",{attrs:{mode:"multiple",maxTagCount:1,showSearch:"",placeholder:"医护操作筛选",allowClear:"",collectionType:"APE893"}})],1)],1)],1),i("div",{staticStyle:{display:"flex","margin-left":"10px",float:"right"}},[i("div",{staticClass:"ctrl-btn",on:{click:t.formShowAllChange}},[i("a",[t._v(t._s(t.formShowAll?"收起":"展开"))]),t.formShowAll?i("ta-icon",{attrs:{type:"caret-up"}}):i("ta-icon",{attrs:{type:"caret-down"}})],1),i("ta-button",{attrs:{type:"primary",icon:"search"},on:{click:t.fnDetailQuery}},[t._v("查询")]),i("ta-button",{attrs:{type:"default",icon:"redo"},on:{click:t.fnReset}},[t._v("重置")])],1)]),i("ta-tag-select",{attrs:{data:t.tagData,required:!0},on:{change:t.getResult},model:{value:t.tags1,callback:function(e){t.tags1=e},expression:"tags1"}}),i("div",{directives:[{name:"show",rawName:"v-show",value:this.dtlorRank,expression:"this.dtlorRank"}]},[i("div",{staticStyle:{height:"470px","margin-top":"5px",flex:"1"}},[i("ta-big-table",{ref:"infoTableRef2",attrs:{columns:t.tableColumns2,data:t.tableData2,border:"",height:"auto","highlight-hover-row":"","auto-resize":"","show-overflow":"",resizable:""},on:{"cell-click":t.cellClickEvent},scopedSlots:t._u([{key:"proportion",fn:function(e){var a=e.row;return[i("span",[t._v(t._s((100*a.proportion).toFixed(0))+"%")])]}},{key:"amount",fn:function(e){var a=e.row;return[i("span",[t._v(t._s((100*a.amount).toFixed(0))+"%")])]}},{key:"opennum",fn:function(e){var a=e.row;return[i("span",[t._v(t._s((100*a.opennum).toFixed(0))+"%")])]}},{key:"beian",fn:function(e){var a=e.row;return[i("span",[t._v(t._s((100*a.beian).toFixed(0))+"%")])]}}])})],1),i("div",[i("ta-button",{staticClass:"export",attrs:{type:"primary",icon:"download"},on:{click:function(){return t.handleMenuClick2({key:"1"})}}},[t._v("导出")]),i("ta-pagination",{ref:"gridPager2",staticClass:"page",attrs:{defaultPageSize:1e3,simple:"",dataSource:t.tableData2,params:t.pageParams,url:this.url},on:{"update:dataSource":function(e){t.tableData2=e},"update:data-source":function(e){t.tableData2=e}}})],1)]),i("div",{directives:[{name:"show",rawName:"v-show",value:!this.dtlorRank,expression:"!this.dtlorRank"}]},[i("div",{staticStyle:{height:"470px","margin-top":"5px",flex:"1"}},[i("ta-big-table",{ref:"infoTableRef",attrs:{columns:t.tableColumns,data:t.tableData,border:"",height:"auto","highlight-hover-row":"","auto-resize":"","show-overflow":"",resizable:""},scopedSlots:t._u([{key:"monney",fn:function(e){var a=e.row;return[i("span",[t._v(t._s(a.ape804)+" 元")])]}},{key:"aaz560",fn:function(e){var a=e.row;return[null==a.aaz560||""==a.aaz560?i("span",[t._v("--")]):i("span",[t._v(t._s(a.aaz560))])]}}])})],1),i("div",[i("ta-button",{staticClass:"export",attrs:{type:"primary",icon:"download"},on:{click:function(){return t.handleMenuClick({key:"1"})}}},[t._v("导出")]),i("ta-pagination",{ref:"gridPager",staticClass:"page",attrs:{defaultPageSize:100,pageSizeOptions:["100","200","500","1000","1500"],dataSource:t.tableData,params:t.pageParams,url:"/departmentAnalysis/queryMore"},on:{"update:dataSource":function(e){t.tableData=e},"update:data-source":function(e){t.tableData=e}}})],1)])],1)},n=[],l=a(89584),r=a(66347),o=a(48534),s=(a(36133),a(89770)),c=a.n(s),u=a(34810),f=a(73418),d=a(36797),h=a.n(d),m={fnCustomHeaderCell:function(e){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px",fontWeight:"bold"}}},fnCustomRow:function(e,t){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px"}}}},p=a(83231);c().vfs=u.I.vfs,c().vfs=Object.assign(c().vfs,f.I.vfs),c().fonts={msyh:{normal:"方正黑体简体.TTF",bold:"方正黑体简体.TTF",italics:"方正黑体简体.TTF",bolditalics:"方正黑体简体.TTF"}};var b={name:"detailInfo",props:{paramData:Object,type:String,flag:String,url:String},data:function(){var e=[{field:"seq",title:"排名",width:"60",align:"center"},{title:"预警规则",field:"aaa167",headerAlign:"left",align:"left",overflowTooltip:!0,width:"auto",customHeaderCell:m.fnCustomHeaderCell},{title:"医师名称",field:"aac003",headerAlign:"center",align:"center",pdfNoWrap:!0,overflowTooltip:!0,width:"auto",customHeaderCell:m.fnCustomHeaderCell},{title:"科室名称",field:"aae386",headerAlign:"center",pdfNoWrap:!0,align:"center",overflowTooltip:!0,width:"auto",customHeaderCell:m.fnCustomHeaderCell},{title:"三目编码",field:"ake001",headerAlign:"center",align:"center",overflowTooltip:!0,width:"auto",customHeaderCell:m.fnCustomHeaderCell},{title:"三目名称",field:"ake002",headerAlign:"center",align:"center",overflowTooltip:!0,width:"auto",customHeaderCell:m.fnCustomHeaderCell},{title:"预警次数(次)",headerAlign:"center",field:"ape800",pdfNoWrap:!0,align:"center",sortable:!0,overflowTooltip:!0,width:"auto",customHeaderCell:m.fnCustomHeaderCell},{title:"预警金额(元)",field:"ape804",headerAlign:"center",pdfNoWrap:!0,align:"center",sortable:!0,overflowTooltip:!0,width:"auto",customHeaderCell:m.fnCustomHeaderCell},{title:"预警次数占比",field:"proportion",headerAlign:"center",pdfNoWrap:!0,align:"center",sortable:!0,overflowTooltip:!0,width:"auto",scopedSlots:{customRender:"proportion"},customRender:{default:"proportion"},customHeaderCell:m.fnCustomHeaderCell},{title:"预警金额占比",field:"amount",headerAlign:"center",pdfNoWrap:!0,align:"center",sortable:!0,overflowTooltip:!0,width:"auto",scopedSlots:{customRender:"amount"},customRender:{default:"amount"},customHeaderCell:m.fnCustomHeaderCell},{title:"预警率",field:"opennum",headerAlign:"center",pdfNoWrap:!0,align:"center",sortable:!0,overflowTooltip:!0,width:"auto",scopedSlots:{customRender:"opennum"},customRender:{default:"opennum"},customHeaderCell:m.fnCustomHeaderCell},{title:"备案率",field:"beian",headerAlign:"center",pdfNoWrap:!0,align:"center",sortable:!0,overflowTooltip:!0,width:"auto",scopedSlots:{customRender:"beian"},customRender:{default:"beian"},customHeaderCell:m.fnCustomHeaderCell}],t=[{type:"seq",title:"序号",width:"60",align:"center"},{title:"三目编码",dataIndex:"ake001",field:"ake001",align:"left",overflowTooltip:!0,width:100,customHeaderCell:m.fnCustomHeaderCell},{title:"三目名称",dataIndex:"ake002",field:"ake002",align:"left",overflowTooltip:!0,width:150,customHeaderCell:m.fnCustomHeaderCell},{title:"住院号",field:"akc191",align:"left",overflowTooltip:!0,width:100,customHeaderCell:m.fnCustomHeaderCell},{title:"就诊号",field:"akc190",align:"left",overflowTooltip:!0,width:100,customHeaderCell:m.fnCustomHeaderCell},{title:"患者姓名",dataIndex:"name",pdfNoWrap:!0,field:"name",align:"left",overflowTooltip:!0,width:80,customHeaderCell:m.fnCustomHeaderCell},{title:"医师姓名",dataIndex:"aac003",pdfNoWrap:!0,field:"aac003",align:"left",overflowTooltip:!0,width:80,customHeaderCell:m.fnCustomHeaderCell},{title:"科室名称",dataIndex:"aae386",pdfNoWrap:!0,field:"aae386",align:"left",overflowTooltip:!0,width:80,customHeaderCell:m.fnCustomHeaderCell},{title:"预警规则",dataIndex:"aaa167",field:"aaa167",align:"left",overflowTooltip:!0,width:150,customHeaderCell:m.fnCustomHeaderCell},{title:"限制条件",dataIndex:"ykz018",field:"ykz018",align:"left",overflowTooltip:!0,width:150,customHeaderCell:m.fnCustomHeaderCell},{title:"医护操作",dataIndex:"ape893",pdfNoWrap:!0,field:"ape893",align:"left",overflowTooltip:!0,width:120,collectionType:"APE893",customHeaderCell:m.fnCustomHeaderCell},{title:"医护操作理由",dataIndex:"aaz560",field:"aaz560",align:"left",overflowTooltip:!0,scopedSlots:{customRender:"aaz560"},customRender:{default:"aaz560"},width:150,customHeaderCell:m.fnCustomHeaderCell},{title:"预警数量",dataIndex:"ape805",field:"ape805",align:"right",overflowTooltip:!0,width:80,customHeaderCell:m.fnCustomHeaderCell},{title:"预警金额",dataIndex:"ape804",field:"ape804",pdfNoWrap:!0,align:"right",overflowTooltip:!0,scopedSlots:{customRender:"monney"},customRender:{default:"monney"},width:80,customHeaderCell:m.fnCustomHeaderCell},{title:"明细发生时间",dataIndex:"aae036",field:"aae036",pdfNoWrap:!0,align:"center",overflowTooltip:!0,width:120,customHeaderCell:m.fnCustomHeaderCell},{title:"入院时间",dataIndex:"aae030",field:"aae030",align:"center",overflowTooltip:!0,width:90,customHeaderCell:m.fnCustomHeaderCell},{title:"医保类型",dataIndex:"aae141",field:"aae141",pdfNoWrap:!0,align:"center",overflowTooltip:!0,collectionType:"AAE141",width:120,customHeaderCell:m.fnCustomHeaderCell},{title:"险种类型",dataIndex:"aae140",field:"aae140",pdfNoWrap:!0,align:"center",collectionType:"AAE140",overflowTooltip:!0,width:120,customHeaderCell:m.fnCustomHeaderCell},{title:"规则等级",dataIndex:"ape800",field:"ape800",collectionType:"APE800",pdfNoWrap:!0,align:"center",overflowTooltip:!0,width:120,customHeaderCell:m.fnCustomHeaderCell}];return{col:{xs:1,sm:2,md:2,lg:4,xl:4,xxl:4},tagData:[{value:"1",label:"查看排名"},{value:"2",label:"查看明细"}],tags1:["1"],rangeValue:[TaUtils.getMoment(new Date((new Date).getTime()).toISOString().slice(0,8)+"01","YYYY-MM-DD"),TaUtils.getMoment((new Date).toISOString().slice(0,10),"YYYY-MM-DD")],startIssue:h()(new Date),endIssue:h()(new Date),startValue:h()(new Date),endValue:h()(new Date),deptData:[],doctorList:[],xmData:[],ksList:[],ruleData:[],selectedRowKeys:[],selectedRows:[],tableColumns:t,dtlorRank:!0,formShowAll:!0,tableColumns2:e,bftype:this.type,permissions:{},paramsDisable:{akb020:!1},filterList:"",tableData:[],tableData2:[],akb020List:[]}},computed:{formBoxStyle:function(){return this.formShowAll?{height:"auto"}:{height:"40px"}}},mounted:function(){var e=this;return(0,o.Z)(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,p.Z.permissionCheck();case 2:e.permissions=t.sent,e.fnQueryHos(),e.queryOptons(),e.form.setFieldsValue(e.paramData),e.handleSearchXm(e.paramData.ake001),e.fnQueryDocter(),e.fnSelectAae141(e.paramData.scene),e.$nextTick((function(){"item"!=e.bftype&&e.form.setFieldsValue({ake001:""}),"item"==e.bftype&&"more"==e.flag&&e.form.setFieldsValue({ake001:""}),e.$refs.infoTableRef2.resetColumn(),e.$refs.infoTableRef.resetColumn(),e.fnDetailQuery(),"dept"==e.bftype&&(e.$refs.infoTableRef2.hideColumn(e.$refs.infoTableRef2.getColumnByField("aaa167")),e.$refs.infoTableRef2.hideColumn(e.$refs.infoTableRef2.getColumnByField("ake001")),e.$refs.infoTableRef2.hideColumn(e.$refs.infoTableRef2.getColumnByField("ake002")),e.$refs.infoTableRef2.hideColumn(e.$refs.infoTableRef2.getColumnByField("aac003")),e.$refs.infoTableRef2.hideColumn(e.$refs.infoTableRef2.getColumnByField("beian")),e.$refs.infoTableRef2.hideColumn(e.$refs.infoTableRef2.getColumnByField("opennum"))),"doctor"==e.bftype&&(e.$refs.infoTableRef2.hideColumn(e.$refs.infoTableRef2.getColumnByField("aaa167")),e.$refs.infoTableRef2.hideColumn(e.$refs.infoTableRef2.getColumnByField("ake001")),e.$refs.infoTableRef2.hideColumn(e.$refs.infoTableRef2.getColumnByField("ake002")),e.$refs.infoTableRef2.hideColumn(e.$refs.infoTableRef2.getColumnByField("proportion")),e.$refs.infoTableRef2.hideColumn(e.$refs.infoTableRef2.getColumnByField("amount"))),"rule"==e.bftype&&(e.$refs.infoTableRef2.hideColumn(e.$refs.infoTableRef2.getColumnByField("ake001")),e.$refs.infoTableRef2.hideColumn(e.$refs.infoTableRef2.getColumnByField("ake002")),e.$refs.infoTableRef2.hideColumn(e.$refs.infoTableRef2.getColumnByField("aae386")),e.$refs.infoTableRef2.hideColumn(e.$refs.infoTableRef2.getColumnByField("aac003")),e.$refs.infoTableRef2.hideColumn(e.$refs.infoTableRef2.getColumnByField("beian")),e.$refs.infoTableRef2.hideColumn(e.$refs.infoTableRef2.getColumnByField("opennum"))),"item"==e.bftype&&(e.$refs.infoTableRef2.hideColumn(e.$refs.infoTableRef2.getColumnByField("aac003")),e.$refs.infoTableRef2.hideColumn(e.$refs.infoTableRef2.getColumnByField("aae386")),e.$refs.infoTableRef2.hideColumn(e.$refs.infoTableRef2.getColumnByField("aaa167")),e.$refs.infoTableRef2.hideColumn(e.$refs.infoTableRef2.getColumnByField("proportion")),e.$refs.infoTableRef2.hideColumn(e.$refs.infoTableRef2.getColumnByField("amount"))),["0","1","8","11"].indexOf(e.paramData.scene)>-1&&e.$refs.infoTableRef.hideColumn(e.$refs.infoTableRef.getColumnByField("aae030")),"2"===e.paramData.scene?(e.$refs.infoTableRef2.hideColumn(e.$refs.infoTableRef2.getColumnByField("amount")),e.$refs.infoTableRef2.hideColumn(e.$refs.infoTableRef2.getColumnByField("ape804")),e.$refs.infoTableRef.hideColumn(e.$refs.infoTableRef.getColumnByField("ape804"))):(["doctor","item"].includes(e.bftype)||e.$refs.infoTableRef2.showColumn(e.$refs.infoTableRef2.getColumnByField("amount")),e.$refs.infoTableRef2.showColumn(e.$refs.infoTableRef2.getColumnByField("ape804")),e.$refs.infoTableRef.showColumn(e.$refs.infoTableRef.getColumnByField("ape804")))})),e.fnQueryDept();case 11:case"end":return t.stop()}}),t)})))()},methods:{handleChange:function(e){["0","1","8","11"].indexOf(e)>-1?this.$refs.infoTableRef.hideColumn(this.$refs.infoTableRef.getColumnByField("aae030")):this.$refs.infoTableRef.resetColumn(),"2"===e?(this.$refs.infoTableRef2.hideColumn(this.$refs.infoTableRef2.getColumnByField("amount")),this.$refs.infoTableRef2.hideColumn(this.$refs.infoTableRef2.getColumnByField("ape804")),this.$refs.infoTableRef.hideColumn(this.$refs.infoTableRef.getColumnByField("ape804"))):(["doctor","item"].includes(this.bftype)||this.$refs.infoTableRef2.showColumn(this.$refs.infoTableRef2.getColumnByField("amount")),this.$refs.infoTableRef2.showColumn(this.$refs.infoTableRef2.getColumnByField("ape804")),this.$refs.infoTableRef.showColumn(this.$refs.infoTableRef.getColumnByField("ape804")))},queryOptons:function(){var e=this,t=[],a=["2","3","4","5","6","7","16","17","18"];this.Base.submit(null,{url:"miimCommonRead/getAa01AAE500StartStop",data:{aaz499s:JSON.stringify(a)}},{successCallback:function(a){var i=a.data.aae500List;i.includes(12)||i.includes(4)||i.includes(5)||i.includes(7)?(t=i.filter((function(e){return 12!==e&&4!==e&&5!==e&&7!==e})),t.push("dscg")):t=i,e.filterList=t.join(",")},failCallback:function(t){e.$message.error("查询场景开关失败")}})},formShowAllChange:function(){this.formShowAll=!this.formShowAll},cellClickEvent:function(e){var t=e.row;"dept"==this.bftype&&this.form.setFieldsValue({departmentCode:t.aaz307}),"doctor"==this.bftype&&this.form.setFieldsValue({doctorCode:t.aaz263}),"rule"==this.bftype&&this.form.setFieldsValue({ruleCode:t.aaz319}),"item"==this.bftype&&this.form.setFieldsValue({ake001:t.ake001}),this.tags1=["2"],this.dtlorRank=!1,this.fnDetailQuery()},getResult:function(e){e.includes("2")?this.dtlorRank=!1:this.dtlorRank=!0},filterOption:function(e,t){return t.componentOptions.children[0].text.indexOf(e)>=0},fnQueryHos:function(){var e=this,t={url:"/miimCommonRead/queryHospRpcList",autoValid:!0},a={successCallback:function(t){e.akb020List=t.data.resultData,e.permissions&&e.permissions.akb020Set.size>0&&(e.paramsDisable.akb020=!0,e.akb020List=e.akb020List.filter((function(t){return e.permissions.akb020Set.has(t.value)})),e.form.setFieldsValue({akb020:e.akb020List[0].value})),e.akb020=e.akb020List[0].value},failCallback:function(t){e.$message.error("医院数据加载失败")}};this.Base.submit(null,t,a)},fnQueryDept:function(e){var t=this;this.akb020=e||"",this.Base.submit(null,{url:"miimCommonRead/queryDepartRpcList",data:{akb020:this.akb020},autoValid:!1},{successCallback:function(e){t.ksList=e.data.resultData},failCallback:function(e){t.$message.error("科室数据加载失败")}})},fnReset:function(){this.form.resetFields(),this.form.setFieldsValue({scene:"1",akb020:this.akb020}),this.fnDetailQuery()},disabledStartDate:function(e){var t=this.endValue;return!(!e||!t)&&e.valueOf()>t.valueOf()},disabledEndDate:function(e){var t=this.startValue;return!(!e||!t)&&t.valueOf()>=e.valueOf()},fnSelectAae141:function(e){var t=this,a={url:"/miimCommonRead/queryRule",data:{scene:e},autoValid:!0},i={successCallback:function(e){t.ruleData=e.data.aaz319},failCallback:function(e){}};this.Base.submit(null,a,i)},handleSearchKs:function(e){var t=this,a={url:"/miimCommonRead/autoQueryKs",data:{name:e},autoValid:!0},i={successCallback:function(e){t.deptData=e.data.ksDataSource},failCallback:function(e){}};this.Base.submit(null,a,i)},fnQueryDocter:function(){var e=this;this.Base.submit(null,{url:"miimCommonRead/queryDoctorRpcList",data:{},autoValid:!1},{successCallback:function(t){e.form.resetFields("doctorCode"),e.doctorList=t.data.resultData},failCallback:function(t){e.$message.error("医师数据加载失败")}})},handleSearchXm:function(e){var t=this,a={url:"/miimCommonRead/autoQueryXm",data:{name:e},autoValid:!0},i={successCallback:function(e){t.xmData=e.data.data},failCallback:function(e){}};this.Base.submit(null,a,i)},fnDetailQuery:function(){var e=this;this.formShowAll=!1,this.form.validateFields((function(t){t||Promise.all([e.$refs.gridPager.loadData((function(e){})),e.$refs.gridPager2.loadData((function(e){}))])}))},fnOnChange:function(e,t){this.selectedRowKeys=e,this.selectedRows=t},handleMenuClick:function(e){var t,a=this,i=this.pageParams(),n=[],l=this.$refs.infoTableRef.getColumns(),o=(0,r.Z)(l);try{for(o.s();!(t=o.n()).done;){var s=t.value;"seq"!==s.type&&"operate"!==s.property&&!1!==s.visible&&n.push({header:s.title,key:s.property,width:20})}}catch(d){o.e(d)}finally{o.f()}["0","1","8","11"].indexOf(i.scene)>-1&&(n=n.filter((function(e){return"aae030"!==e.key}))),"2"===i.scene&&(n=n.filter((function(e){return"ape804"!==e.key})));var c=l.map((function(e){return e.property})),u=[{codeType:"APE800",columnKey:"ape800"},{codeType:"APE893",columnKey:"ape893"},{codeType:"AAE141",columnKey:"aae141"},{codeType:"AAE140",columnKey:"aae140"}],f=u.filter((function(e){return c.includes(e.columnKey)}));this.Base.submit(null,{url:"departmentAnalysis/queryMoreExport",data:i,autoValid:!1},{successCallback:function(t){var l=t.data.data,r={fileName:"查看明细查询结果表("+i.startDate+"-"+i.endDate+")",sheets:[{name:"worksheet1",column:{complex:!1,columns:n},rows:l,codeList:f}]};"1"===e.key&&a.Base.generateExcel(r),"2"===e.key&&a.exportPdf(r)},failCallback:function(e){a.$message.error("导出失败")}})},exportPdf:function(e){var t=this;return(0,o.Z)(regeneratorRuntime.mark((function a(){var i,n,r,o,s,u,f,d,h,m,p,b;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return i=e.sheets[0].codeList,n=new Map,r=i.filter((function(e){return e.codeType})),o=r.map((function(e){return t.Base.asyncGetCodeData(e.codeType)})),a.next=6,Promise.all(o);case 6:s=a.sent,r.forEach((function(e,t){n.set(e.columnKey,s[t])})),u=i.filter((function(e){return e.customCollection})),u.forEach((function(e){return n.set(e.columnKey,e.customCollection)})),f=[],d=e.sheets[0].column.columns,h=e.sheets[0].rows,m=d.map((function(e){return"number"===typeof e.pdfWidth?e.pdfWidth:70})),f.push(d.map((function(e){return e.header}))),p=m.reduce((function(e,t){return e+t}),0),f.push.apply(f,(0,l.Z)(h.map((function(e){var a=[];return d.forEach((function(i){var l=e[i.key];void 0===l&&(l="");var r=n.get(i.key);if(r)if(r instanceof Function){var o={value:l};r(o),l=o.value}else l=t.Base.getCodeLabel(r,l);a.push({text:l})})),a})))),b={pageSize:{width:p+200,height:300},pageOrientation:"landscape",content:[{table:{dontBreakRows:!0,headerRows:1,widths:m,body:f},style:"cnFont"}],styles:{cnFont:{font:"msyh"}}},c().createPdf(b).download(e.fileName+".pdf");case 19:case"end":return a.stop()}}),a)})))()},formatNumber:function(e){return Math.round((100*parseFloat(e)).toFixed(0))+"%"},handleMenuClick2:function(e){var t=this,a=this.pageParams(),i=this;if(a.allDate.length<1)this.$message.error("日期不能为空");else{a.type=this.bftype;var n=[],l=[];"dept"==this.bftype&&(l=this.tableColumns2.filter((function(e){return!("aaa167"===e.field||"ake001"===e.field||"ake002"===e.field||"aac003"===e.field||"beian"===e.field||"opennum"===e.field)}))),"doctor"==this.bftype&&(l=this.tableColumns2.filter((function(e){return!("aaa167"===e.field||"ake001"===e.field||"ake002"===e.field||"proportion"===e.field||"amount"===e.field)}))),"rule"==this.bftype&&(l=this.tableColumns2.filter((function(e){return!("aae386"===e.field||"ake001"===e.field||"ake002"===e.field||"aac003"===e.field||"beian"===e.field||"opennum"===e.field)}))),"item"==this.bftype&&(l=this.tableColumns2.filter((function(e){return!("aac003"===e.field||"aae386"===e.field||"aaa167"===e.field||"proportion"===e.field||"amount"===e.field)})));var o=[];o="item"==this.bftype||"doctor"==this.bftype?[{columnKey:"beian",customCollection:function(e,t){e.value=i.formatNumber(e.value)}},{columnKey:"opennum",customCollection:function(e,t){e.value=i.formatNumber(e.value)}}]:[{columnKey:"proportion",customCollection:function(e,t){e.value=i.formatNumber(e.value)}},{columnKey:"amount",customCollection:function(e,t){e.value=i.formatNumber(e.value)}}];var s,c=(0,r.Z)(l);try{for(c.s();!(s=c.n()).done;){var u=s.value;n.push({header:u.title,key:u.field,pdfNoWrap:u.pdfNoWrap,pdfWidth:u.width,width:20})}}catch(f){c.e(f)}finally{c.f()}"2"===a.scene&&(n=n.filter((function(e){return"ape804"!==e.key&&"amount"!==e.key}))),this.Base.submit(null,{url:"departmentAnalysis/exportExcel",data:a,autoValid:!1},{successCallback:function(a){var i={fileName:"预警排名表",sheets:[{name:"worksheet1",column:{complex:!1,columns:n},rows:a.data.data,codeList:o}]};"1"===e.key&&t.Base.generateExcel(i),"2"===e.key&&t.exportPdf(i)},failCallback:function(e){t.$message.error("预警排名数据加载失败")}})}},pageParams:function(){var e=this.form.getFieldsValue();return e.startDate=e.allDate[0].format("YYYY-MM-DD HH:mm:ss"),e.endDate=e.allDate[1].format("YYYY-MM-DD HH:mm:ss"),e.startDates=e.allDate[0].format("YYYYMMDD"),e.endDates=e.allDate[1].format("YYYYMMDD"),e}}},g=b,y=a(1001),v=(0,y.Z)(g,i,n,!1,null,"68082d00",null),C=v.exports},99566:function(e,t,a){a.d(t,{Z:function(){return d}});var i=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[i("div",{staticStyle:{"border-bottom":"1px solid rgba(153,153,153,0.14)"}},[i("ta-title",{attrs:{title:e.title}},[i("span",{staticStyle:{display:"inline-block",float:"right"}},[i("ta-radio-group",{staticStyle:{"margin-right":"10px"},attrs:{size:"small",value:e.checked},on:{change:e.handleChange}},[i("ta-radio-button",{attrs:{value:"time"}},[e._v(" 预警次数 ")]),i("ta-radio-button",{attrs:{disabled:!e.showAmount,value:"money"}},[e._v(" 预警金额 ")])],1),i("a",{staticClass:"more",on:{click:function(t){return e.handleMore()}}},[e._v("查看更多>>")]),e.showLast?i("img",{staticClass:"arrow",attrs:{src:a(53949)},on:{click:function(t){return e.topMore()}}}):i("img",{staticClass:"arrow",attrs:{src:a(89712)}}),e.showNext?i("img",{staticClass:"arrow",attrs:{src:a(23554)},on:{click:function(t){return e.bottomMore()}}}):i("img",{staticClass:"arrow",attrs:{src:a(61368)}}),i("ta-button",{staticClass:"downloadChart",attrs:{title:"下载图表",type:"primary",shape:"circle",icon:"download",size:"small"},on:{click:e.downloadChart}})],1)])],1),i("div",{attrs:{id:"main"}}),i("ta-modal",{attrs:{width:"90%",height:"650px",footer:null,destroyOnClose:!0,title:"预警项目明细"},model:{value:e.visible,callback:function(t){e.visible=t},expression:"visible"}},[i("detail-info",{attrs:{type:e.type,paramData:e.paramData,url:"/departmentAnalysis/queryDocDetails"}})],1)],1)},n=[],l=(a(39575),a(38012),a(88412)),r=a(1708),o=a(98526),s={name:"ysRankBar",components:{TaTitle:l.Z,detailInfo:o.Z},props:{paramData:Object,title_barRank:String,type:String,url:String},data:function(){return{title:"",bftitle:"",params:{},visible:!1,page:0,checked:"time",ytitle:"预警次数",unit:"次",pageSize:5,showLast:!0,showNext:!0,showAmount:!0,option:{},bfdata:{},allData:[],currData:[]}},mounted:function(){this.myChart=r.init(document.getElementById("main")),this.title=this.title_barRank},methods:{downloadChart:function(){for(var e=this.myChart.getDataURL({type:"png",pixelRatio:2,backgroundColor:"#fff"}),t=atob(e.split(",")[1]),a=e.split(",")[0].split(":")[1].split(";")[0],i=new ArrayBuffer(t.length),n=new Uint8Array(i),l=0;l<t.length;l++)n[l]=t.charCodeAt(l);var r=new Blob([i],{type:a});saveAs(r,this.title+".png")},handleChange:function(e){this.checked=e.target.value,this.fnRank(this.bftitle,this.bfdata)},handleMore:function(){this.visible=!0},fnRank:function(e,t,a){var i=this;"2"===a?(this.showAmount=!1,this.checked="time"):this.showAmount=!0,this.bftitle=e,this.title=e+this.title_barRank,this.bfdata=t,this.allData=Object.keys(t).map((function(e){return{name:t[e].aac003,warnCount:t[e].warningFre,warnPercent:Math.round(100*(t[e].warningFre/t[e].projectFre).toFixed(2)),continuePercent:Math.round(100*(t[e].continueFre/t[e].warningFre).toFixed(2)),ape804:t[e].ape804,aaz263:t[e].aaz263}})),this.page=0,"time"==this.checked?this.allData.sort((function(e,t){return t.warnCount-e.warnCount})):this.allData.sort((function(e,t){return t.ape804-e.ape804})),this.flushPage(),this.myChart.off("click"),this.myChart.on("click",(function(e){var t={aaz263:i.allData[e.dataIndex].aaz263};i.$emit("fnLinkQuery","doctor",e.name,t)}))},fnQuery:function(){},topMore:function(){0!==this.page&&(this.page--,this.flushPage())},formatAmount:function(e){!isNaN(e)&&e||(e=0);var t=String(parseFloat(e)),a=/(-?\d+)(\d{3})/;while(a.test(t))t=t.replace(a,"$1,$2");return t},bottomMore:function(){(this.page+1)*this.pageSize<this.allData.length&&(this.page++,this.flushPage())},flushPage:function(){this.showLast=this.page>0,this.showNext=this.pageSize*(this.page+1)<this.allData.length;var e=this.page*this.pageSize;this.currData=this.allData.slice(e,e+this.pageSize),this.option=this.echartsDataBuild(this.currData),this.myChart.setOption(this.option)},echartsDataBuild:function(e){var t=this,a=[];return"time"===this.checked?(this.ytitle="预警次数",this.unit="次",a=e.map((function(e){return e.warnCount}))):(this.ytitle="预警金额",this.unit="元",a=e.map((function(e){return e.ape804}))),{backgroundColor:"#fff",grid:{left:"5%",right:"7%",bottom:"2%",top:"18%",containLabel:!0},legend:{data:["预警率","备案率",this.ytitle],textStyle:{color:"#333",fontSize:12}},tooltip:{trigger:"axis",confine:!0,appendToBody:!0,backgroundColor:"#fff",borderColor:"#ccc",borderWidth:1,textStyle:{color:"#333"},formatter:function(e){for(var a=e[0].name,i=0;i<e.length;i++){var n=0===i;a+="<br/>"+e[i].marker+" "+e[i].seriesName+"："+t.formatAmount(e[i].value)+(n?t.unit:"%")}return a}},xAxis:[{type:"category",data:e.map((function(e){return e.name})),axisLabel:{interval:0,color:"#333",fontSize:12},axisLine:{lineStyle:{color:"#ccc",width:1}}}],yAxis:[{name:"单位："+this.unit,nameTextStyle:{color:"#333",fontSize:12},type:"value",axisLabel:{color:"#333",fontSize:12,formatter:function(e){return e>=1e8?e/1e8+"亿":e>=1e4?e/1e4+"万":0===e?0:e}},axisLine:{lineStyle:{color:"#ccc",width:1}},splitLine:{lineStyle:{color:"#eee"}}},{type:"value",name:"预警/备案率 (%)",position:"right",nameTextStyle:{color:"#333",fontSize:12},axisLabel:{color:"#333",fontSize:12,formatter:"{value} %"},axisLine:{lineStyle:{color:"#ccc",width:1}},splitLine:{show:!1},max:100}],series:[{type:"bar",name:this.ytitle,yAxisIndex:0,barWidth:25,barCategoryGap:"20%",itemStyle:{borderRadius:[3,3,0,0],color:"#29cdff"},data:a},{type:"line",name:"预警率",yAxisIndex:1,symbolSize:8,smooth:!0,lineStyle:{width:2},color:"#ff8100",data:e.map((function(e){return e.warnPercent}))},{type:"line",name:"备案率",yAxisIndex:1,symbolSize:8,smooth:!0,lineStyle:{width:2},color:"#148fff",data:e.map((function(e){return e.continuePercent}))}]}}}},c=s,u=a(1001),f=(0,u.Z)(c,i,n,!1,null,"70a54d46",null),d=f.exports},42375:function(e,t,a){a.d(t,{Z:function(){return d}});var i=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[i("div",{staticStyle:{"border-bottom":"1px solid rgba(153,153,153,0.14)"}},[i("ta-title",{attrs:{title:e.title}},[i("span",{staticStyle:{display:"inline-block",float:"right"}},[i("ta-radio-group",{staticStyle:{"margin-right":"10px"},attrs:{value:e.checked,size:"small"},on:{change:e.handleChange}},[i("ta-radio-button",{attrs:{value:"time"}},[e._v("预警次数")]),i("ta-radio-button",{attrs:{disabled:!e.showAmount,value:"money"}},[e._v("预警金额")])],1),i("ta-select",{staticStyle:{width:"55px","margin-right":"10px"},attrs:{title:"展示项目数量",defaultValue:"8"},on:{change:e.handleSelect}},[i("ta-select-option",{attrs:{title:"展示项目数量",value:"5"}},[i("h5",[e._v("5")])]),i("ta-select-option",{attrs:{title:"展示项目数量",value:"6"}},[i("h5",[e._v("6")])]),i("ta-select-option",{attrs:{title:"展示项目数量",value:"8"}},[i("h5",[e._v("8")])]),i("ta-select-option",{attrs:{title:"展示项目数量",value:"10"}},[i("h5",[e._v("10")])])],1),i("a",{staticClass:"more",on:{click:function(t){return e.handleMore()}}},[e._v("查看更多>>")]),e.showLast?i("img",{staticClass:"arrow",attrs:{title:"上一页",src:a(53949)},on:{click:function(t){return e.topMore()}}}):i("img",{staticClass:"arrow",attrs:{title:"上一页",src:a(89712)}}),e.showNext?i("img",{staticClass:"arrow",attrs:{title:"下一页",src:a(23554)},on:{click:function(t){return e.bottomMore()}}}):i("img",{staticClass:"arrow",attrs:{title:"下一页",src:a(61368)}}),i("ta-button",{staticClass:"downloadChart",attrs:{title:"下载图表",type:"primary",shape:"circle",icon:"download",size:"small"},on:{click:e.downloadChart}})],1)])],1),i("ta-echarts",{ref:"chart",staticStyle:{width:"100%",height:"260px"},attrs:{option:e.option}}),i("ta-modal",{attrs:{width:"90%",height:"650px",footer:null,destroyOnClose:!0,title:"预警项目明细"},model:{value:e.visible,callback:function(t){e.visible=t},expression:"visible"}},[i("detail-info",{ref:"details",attrs:{flag:e.flag,type:e.type.split("_")[0],paramData:e.paramData,url:"/departmentAnalysis/queryItemDetails"}})],1)],1)},n=[],l=(a(39575),a(38012),a(32564),a(88412)),r=a(98526),o=a(1708),s=(a(75969),{name:"itemRankBar",components:{TaTitle:l.Z,detailInfo:r.Z},props:{paramData:Object,title_barRank:String,type:String,url:String},data:function(){return{title:"",bftitle:"",params:{},visible:!1,showLast:!0,flag:"click",showNext:!0,showAmount:!0,xarray:[],checked:"time",unit:"次",ytitle:"预警次数",page:0,pageSize:8,itemDate:{},allData:[],currData:[],option:{}}},mounted:function(){this.title=this.title_barRank},methods:{downloadChart:function(){for(var e=this.$refs.chart.myChart.getDataURL({type:"png",pixelRatio:2,backgroundColor:"#fff"}),t=atob(e.split(",")[1]),a=e.split(",")[0].split(":")[1].split(";")[0],i=new ArrayBuffer(t.length),n=new Uint8Array(i),l=0;l<t.length;l++)n[l]=t.charCodeAt(l);var r=new Blob([i],{type:a});saveAs(r,this.title+".png")},handleSelect:function(e){this.pageSize=Number(e),this.flushPage()},handleChange:function(e){this.checked=e.target.value,this.fnQuery(this.bftitle,this.itemDate)},handleMore:function(){this.flag="more",this.visible=!0},fnQuery:function(e,t,a){var i=this;"2"===a?(this.showAmount=!1,this.checked="time"):this.showAmount=!0,this.showLast=!0,this.showNext=!0,this.bftitle=e,this.title=e+this.title_barRank,this.itemDate=t;var n=Object.keys(t).map((function(e){return{akc350:t[e].akc350,ake001:t[e].ake001,ake002:t[e].ake002,ape804:t[e].ape804,ape805:t[e].ape805,warningFreFX:t[e].warningFre,warningFre:Math.round(100*(t[e].warningFre/t[e].projectFre).toFixed(2)),continueFre:Math.round(100*(t[e].continueFre/t[e].warningFre).toFixed(2))}}));"time"==this.checked?n.sort((function(e,t){return t.warningFreFX-e.warningFreFX})):n.sort((function(e,t){return t.ape804-e.ape804})),this.allData=n,this.page=0,this.flushPage(),"item"===this.type?(this.$refs.chart.myChart.off("click"),this.$refs.chart.myChart.on("click",(function(e){i.paramData.ake001=i.currData.map((function(e){return e.ake001}))[e.dataIndex],i.flag="click",i.visible=!0}))):(this.$refs.chart.myChart.off("dblclick"),this.$refs.chart.myChart.off("click"),this.$refs.chart.myChart.on("dblclick",(function(e){i.click_type=!0,i.paramData.ake001=i.currData.map((function(e){return e.ake001}))[e.dataIndex],i.flag="click",i.visible=!0})),this.$refs.chart.myChart.on("click",(function(e){i.click_type=!1,setTimeout((function(){if(0==i.click_type){var t={ake001:i.currData.map((function(e){return e.ake001}))[e.dataIndex]};i.$emit("fnLinkQuery","item",e.name,t)}}),200)})))},topMore:function(){0!==this.page&&(this.page--,this.flushPage())},bottomMore:function(){(this.page+1)*this.pageSize<this.allData.length&&(this.page++,this.flushPage())},formatAmount:function(e){!isNaN(e)&&e||(e=0);var t=String(parseFloat(e)),a=/(-?\d+)(\d{3})/;while(a.test(t))t=t.replace(a,"$1,$2");return t},flushPage:function(){this.showLast=this.page>0,this.showNext=this.pageSize*(this.page+1)<this.allData.length;var e=this.page*this.pageSize;this.currData=this.allData.slice(e,e+this.pageSize),this.option=this.echartsDataBuild(this.currData),this.$refs.chart.updateOptions(this.option)},echartsDataBuild:function(e){var t=this,a=[];return"time"===this.checked?(this.ytitle="预警次数",this.unit="次",a=e.map((function(e){return e.warningFreFX}))):(this.ytitle="预警金额",this.unit="元",a=e.map((function(e){return e.ape804}))),{backgroundColor:"#fff",grid:{left:"1%",right:"1%",bottom:"1%",top:"16%",containLabel:!0},tooltip:{trigger:"axis",confine:!0,appendToBody:!0,backgroundColor:"#fff",borderColor:"#ccc",borderWidth:1,textStyle:{color:"#333"},formatter:function(e){for(var a=e[0].name,i=0;i<e.length;i++){var n=0===i?t.unit:"%";a+="<br/>"+e[i].marker+" "+e[i].seriesName+"："+t.formatAmount(e[i].value)+n}return a}},legend:{data:["预警率","备案率",this.ytitle],textStyle:{color:"#333",fontSize:12}},xAxis:[{type:"category",data:e.map((function(e){return e.ake002})),axisLabel:{color:"#333",fontSize:12,formatter:function(e){return o.format.truncateText(e,100,"14px Microsoft Yahei","...")}},axisLine:{lineStyle:{color:"#ccc",width:1}}}],yAxis:[{name:"单位："+this.unit,type:"value",nameTextStyle:{color:"#333",fontSize:12},axisLabel:{color:"#333",fontSize:12,formatter:function(e){return e>=1e8?e/1e8+"亿":e>=1e4?e/1e4+"万":0===e?0:e}},axisLine:{lineStyle:{color:"#ccc",width:1}},splitLine:{lineStyle:{color:"#eee"}}},{type:"value",name:"预警/备案率 (%)",position:"right",nameTextStyle:{color:"#333",fontSize:12},axisLabel:{color:"#333",fontSize:12,formatter:"{value} %"},axisLine:{lineStyle:{color:"#ccc",width:1}},splitLine:{show:!1},max:100}],series:[{type:"bar",name:this.ytitle,barWidth:20,itemStyle:{borderRadius:[3,3,0,0],color:"#ffcb13"},data:a},{type:"line",name:"预警率",yAxisIndex:1,symbolSize:8,smooth:!0,lineStyle:{width:2},color:"#ff8100",data:e.map((function(e){return e.warningFre}))},{type:"line",name:"备案率",yAxisIndex:1,symbolSize:8,smooth:!0,lineStyle:{width:2},color:"#148fff",data:e.map((function(e){return e.continueFre}))}]}}}}),c=s,u=a(1001),f=(0,u.Z)(c,i,n,!1,null,"416899d2",null),d=f.exports},61973:function(e,t,a){a.d(t,{Z:function(){return f}});var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{staticStyle:{"border-bottom":"1px solid rgba(153,153,153,0.14)"}},[a("ta-title",{attrs:{title:e.title}},[a("span",{staticStyle:{display:"inline-block",float:"right"}},[a("ta-radio-group",{staticClass:"more",attrs:{size:"small",value:e.month},on:{change:e.handleChange}},[a("ta-radio-button",{attrs:{value:"three"}},[e._v("近三月")]),a("ta-radio-button",{attrs:{value:"six"}},[e._v("近六月")]),a("ta-radio-button",{attrs:{value:"year"}},[e._v("近一年")])],1),a("ta-button",{staticClass:"downloadChart",attrs:{title:"下载图表",type:"primary",shape:"circle",icon:"download",size:"small"},on:{click:e.downloadChart}})],1)])],1),a("ta-echarts",{ref:"trendChart",staticStyle:{height:"250px",width:"100%","margin-top":"6px"},attrs:{option:{}}})],1)},n=[],l=(a(39575),a(38012),a(88412)),r=a(75969),o=(a(1708),{name:"lineNum",components:{TaTitle:l.Z},props:{url:String},data:function(){return{title_trend:"预警月趋势",title:"预警月趋势",month:"six",bfcodeList:[],bfcodeListValue:[],bfdata:{},bfmap:{},lineData:[{ape893:"",name:"提醒总次数",value:[{name:"",value:0}]},{ape893:"1",name:"继续次数",value:[{name:"",value:0}]},{ape893:"2",name:"自费次数",value:[{name:"",value:0}]},{ape893:"3",name:"取消次数",value:[{name:"",value:0}]},{ape893:"99",name:"忽略次数",value:[{name:"",value:0}]}]}},mounted:function(){this.getCodeData()},methods:{downloadChart:function(){for(var e=this.$refs.trendChart.myChart.getDataURL({type:"png",pixelRatio:2,backgroundColor:"#fff"}),t=atob(e.split(",")[1]),a=e.split(",")[0].split(":")[1].split(";")[0],i=new ArrayBuffer(t.length),n=new Uint8Array(i),l=0;l<t.length;l++)n[l]=t.charCodeAt(l);var r=new Blob([i],{type:a});saveAs(r,this.title+".png")},getCodeData:function(){var e=this;this.Base.asyncGetCodeData("APE893").then((function(t){e.bfcodeList=t;for(var a=0;a<t.length;a++)e.bfcodeListValue.push(t[a].value)}))},handleChange:function(e){this.month=e.target.value,this.fnTrend("",this.bfdata)},lineobjs:function(e,t,a){this.ape893=e,this.name=t,this.value=a},lineobjs2:function(e,t){this.name=e,this.value=t},changeDate:function(e){var t=new Date;this.bfdata.endDate=t.toISOString().slice(0,10)+" 23:59:59";var a=t.toISOString().slice(5,7)-e+13;a=a<10?"0"+a:a,t.setMonth(-e),this.bfdata.startDate=t.getFullYear()+"-"+a+"-01 00:00:00"},fnTrend:function(e,t){var a=this;this.bfdata=t,this.title=e+this.title_trend,"six"===this.month?this.changeDate(6):"year"===this.month?this.changeDate(12):this.changeDate(3);var i={url:this.url,data:this.bfdata,autoValid:!0},n={successCallback:function(e){var i,n,l,o,s=null===e||void 0===e||null===(i=e.data)||void 0===i||null===(n=i.resultMap)||void 0===n?void 0:n.monthPointMap;if(s){var c=[],u=[],f=[],d=[];for(var h in s)for(var m in s[h]){if("warningFre"===m){var p=new a.lineobjs2(h,s[h][m]);u.push(p)}"ape804"===m&&d.push(s[h][m])}var b=new a.lineobjs("","预警总次数",u);c.push(b);var g=new Map;a.bfcodeListValue.forEach((function(e){g.set(e,[])}));var y=function(e,t){return e.concat(t).filter((function(e,t,a){return a.indexOf(e)===a.lastIndexOf(e)}))},v=function(e){var t=function(t){if("ape893Map"===t){for(var i in f=[],s[e][t])f.push(i);var n=y(a.bfcodeListValue,f);for(var l in n.forEach((function(a){s[e][t][a]=0})),s[e][t]){var r=new a.lineobjs2(e,s[e][t][l]);a.assemblyData(e,l,r,g)}}};for(var i in s[e])t(i)};for(var C in s)v(C);for(var w=0;w<a.bfcodeListValue.length;w++){var k,D=a.bfcodeListValue[w],x=a.Base.getCodeLabel(a.bfcodeList,D)+"次数",S=a.bfmap instanceof Map?null===(k=a.bfmap)||void 0===k?void 0:k.get(D):[],T=new a.lineobjs(D,x,S);c.push(T)}if(0!==Object.keys(s).length){var R,F,$;if(a.lineData=c,t.operate&&t.operate.length>0)null===(R=a.$refs.trendChart.myChart)||void 0===R||R.clear(),a.$refs.trendChart.updateOptions(r.Z.createLine1(a.lineData.filter((function(e){return t.operate.includes(e.ape893)||!e.ape893})),d,a.bfcodeList.filter((function(e){return t.operate.includes(e.value)})).length,"name","value","ape893","次"));else a.$refs.trendChart.updateOptions(r.Z.createLine1(a.lineData,d,a.bfcodeList.length,"name","value","ape893","次"));if(null===(l=a.$refs.trendChart.myChart)||void 0===l||l.off("click"),null===(o=a.$refs.trendChart.myChart)||void 0===o||o.on("click",(function(e){var t={startDate:a.modifyParameters(e.name,"01"),endDate:a.changeDate2(a.modifyParameters(e.name,"01"))};a.$emit("fnLinkQuery","line",a.modifydate(e.name),t)})),"2"===t.scene)null===(F=a.$refs.trendChart.myChart)||void 0===F||F.dispatchAction({type:"legendUnSelect",name:"预警总金额"});else null===($=a.$refs.trendChart.myChart)||void 0===$||$.dispatchAction({type:"legendSelect",name:"预警总金额"})}else{var A,z;null!==(A=a.$refs)&&void 0!==A&&null!==(z=A.trendChart)&&void 0!==z&&z.myChart&&(a.$refs.trendChart.myChart.clear(),a.$refs.trendChart.updateOptions(r.Z.createLine1([],[],0,"name","value","ape893","次")))}}else{var _,L;null!==(_=a.$refs)&&void 0!==_&&null!==(L=_.trendChart)&&void 0!==L&&L.myChart&&(a.$refs.trendChart.myChart.clear(),a.$refs.trendChart.updateOptions(r.Z.createLine1([],[],0,"name","value","ape893","次")))}},failCallback:function(){}};this.Base.submit(null,i,n)},modifyParameters:function(e,t){return e.slice(0,4)+"-"+e.slice(4)+"-"+t+" 00:00:00"},changeDate2:function(e){var t=new Date(e);return t.setMonth(t.getMonth()+1),t.toISOString().slice(0,10)+" 00:00:00"},modifydate:function(e){return e.slice(0,4)+"年"+e.slice(4)+"月"},assemblyData:function(e,t,a,i){var n=new Map;this.bfcodeListValue.forEach((function(e){var l=i.get(e)||[];e===t&&l.push(a),n.set(e,l)})),this.bfmap=n},adddate:function(){}}}),s=o,c=a(1001),u=(0,c.Z)(s,i,n,!1,null,"b312395c",null),f=u.exports},10010:function(e,t,a){a.d(t,{Z:function(){return p}});var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{ref:"piediv"},[a("div",{staticStyle:{"border-bottom":"1px solid rgba(153,153,153,0.14)"}},[a("ta-title",{attrs:{title:e.title}},[a("span",{staticStyle:{display:"inline-block",float:"right"}},[a("ta-button",{staticClass:"downloadChart",attrs:{title:"下载图表",type:"primary",shape:"circle",icon:"download",size:"small"},on:{click:e.downloadChart}})],1)])],1),a("div",{staticStyle:{display:"flex"}},[a("div",{staticClass:"box"},[a("div",{staticStyle:{display:"flex","flex-wrap":"wrap","margin-left":"40%"}},[a("span",{staticClass:"text"},[e._v("预警总次数("+e._s(e.formatUnit(this.warningTotal,"次"))+")")]),a("span",{staticClass:"text2"},[e._v(e._s(this.formatDependingOnValue(this.warningTotal)))])])]),a("div",{staticClass:"box2"},[a("div",{staticStyle:{display:"flex","flex-wrap":"wrap","margin-left":"20%"}},[a("span",{staticClass:"text"},[e._v("预警率")]),a("span",{staticClass:"text3"},[e._v(e._s(this.warningEarly))])])])]),a("ta-echarts",{ref:"remindChart",staticStyle:{width:"100%","margin-left":"-10px"},attrs:{option:{}}})],1)},n=[],l=a(88412),r=a(75969),o=a(92269),s=a.n(o);function c(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"capture.png";s()(e).then((function(e){var a=e.toDataURL("image/png"),i=document.createElement("a");i.href=a,i.download=t,i.click()})).catch((function(e){}))}var u={saveElementAsImage:c},f={name:"pieNum",components:{TaTitle:l.Z},props:{url:String},data:function(){return{title_remindNum:"预警总构成",title:"预警总构成",bfcodeList:[],defaultOption:{pieData:[],key:"name",val:"value",id:"ape893",title:"预警总次数",showLegend:!0,showLabel:!0,labelPosition:"center",roseType:!1,orient:"vertical",legendPosition:{top:"1%",right:"auto",left:"50%",bottom:"auto"},pieCenter:["26%","50%"],pieRadius:["55%","80%"],titlePosition:{top:"center",left:"67%"},itemWidth:12,itemHeight:12},warningTotal:0,warningEarly:"0%"}},mounted:function(){this.getCodeData()},methods:{downloadChart:function(){var e=this.$refs.piediv;u.saveElementAsImage(e,this.title+".png")},formatUnit:function(e,t){return e>=1e4&&"%"!=t?"万"+t:t},formatAmount:function(e){if(isNaN(e)||!e)return"";var t=parseFloat(e),a=String(t.toFixed(2)),i=/(-?\d+)(\d{3})/;while(i.test(a))a=a.replace(i,"$1,$2");return a},formatDependingOnValue:function(e){return Number.isFinite(e)?e>=1e4?this.formatAmount(e/1e4):0==e?0:e:"-"},getCodeData:function(){var e=this;this.Base.asyncGetCodeData("APE893").then((function(t){e.bfcodeList=t}))},fnRemindNum:function(e,t){var a=this;this.title=e+this.title_remindNum;var i=t.data.resultMap,n=t.data.resultMap.ape893Map;this.warningTotal=i.warningFre,this.warningEarly=0==i.projectSize?"0%":(i.warningFre/i.projectSize*100).toFixed(2)+"%";var l=Object.keys(n).map((function(e){return{ape893:e,value:n[e],name:a.Base.getCodeLabel(a.bfcodeList,e)}}));this.defaultOption.pieData=l,this.$refs.remindChart.updateOptions(r.Z.createPieFlex(this.defaultOption)),this.$refs.remindChart.myChart.off("click"),this.$refs.remindChart.myChart.on("click",(function(e){var t={ape893:e.data.id};a.$emit("fnLinkQuery","pie",e.data.name,t)}))},ruleobjs:function(e,t,a){this.ape893=e,this.name=t,this.value=a}}},d=f,h=a(1001),m=(0,h.Z)(d,i,n,!1,null,"0b87e678",null),p=m.exports},74698:function(e,t,a){a.d(t,{Z:function(){return d}});var i=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[i("div",{staticStyle:{"border-bottom":"1px solid rgba(153,153,153,0.14)"}},[i("ta-title",{attrs:{title:e.title}},[i("span",{staticStyle:{display:"inline-block",float:"right"}},[i("ta-radio-group",{staticStyle:{"margin-right":"10px"},attrs:{size:"small",value:e.checked},on:{change:e.handleChange}},[i("ta-radio-button",{attrs:{value:"time"}},[e._v(" 次数排序 ")]),i("ta-radio-button",{attrs:{disabled:!e.showAmount,value:"money"}},[e._v(" 金额排序 ")])],1),i("a",{staticClass:"more",on:{click:function(t){return e.handleMore()}}},[e._v("查看更多>>")]),e.showLast?i("img",{staticClass:"arrow",attrs:{src:a(53949)},on:{click:function(t){return e.topMore()}}}):i("img",{staticClass:"arrow",attrs:{src:a(89712)}}),e.showNext?i("img",{staticClass:"arrow",attrs:{src:a(23554)},on:{click:function(t){return e.bottomMore()}}}):i("img",{staticClass:"arrow",attrs:{src:a(61368)}})],1)])],1),i("ta-big-table",{ref:"xTable",attrs:{"highlight-hover-row":"","show-overflow":"",size:"mini",width:"100%",height:"295",data:e.tableData,border:"inner"},on:{"cell-click":e.cellClickEvent}},[i("ta-big-table-column",{attrs:{field:"deptSeq",align:"center",title:"排名","min-width":"50"}}),i("ta-big-table-column",{attrs:{field:"deptName",title:"科室名称",align:"center","min-width":"105"}}),i("ta-big-table-column",{attrs:{field:"deptNo",title:"科室编号",align:"center","min-width":"80"}}),i("ta-big-table-column",{attrs:{field:"count",title:"次数",align:"center","min-width":"50"}}),i("ta-big-table-column",{attrs:{field:"progress",align:"center",title:"占比","min-width":"120"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return["time"==e.checked?i("div",[i("ta-progress",{staticStyle:{width:"100px"},attrs:{type:"line",status:"active",percent:Number(a.projectFre),size:"small"}})],1):i("div",[i("ta-progress",{staticStyle:{width:"100px"},attrs:{type:"line",status:"active",percent:Number(a.percent),size:"small"}})],1)]}}])}),i("ta-big-table-column",{attrs:{field:"fee",align:"center",title:"金额","show-overflow":"","min-width":"100",visible:e.showAmount},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s(a.fee)+"元 ")]}}])})],1),i("ta-modal",{attrs:{width:"90%",height:"650px",footer:null,destroyOnClose:!0,title:"预警项目明细"},model:{value:e.visible,callback:function(t){e.visible=t},expression:"visible"}},[i("detail-info",{attrs:{type:e.type,paramData:e.paramData,url:"/departmentAnalysis/queryDeptDetails"}})],1)],1)},n=[],l=a(66347),r=a(88412),o=(a(75969),a(98526)),s={name:"ksRankBar",components:{TaTitle:r.Z,detailInfo:o.Z},props:{paramData:Object,title_barRank:String,type:String,url:String},data:function(){return{title:"",bftitle:"",params:{},page:0,bftotal:0,checked:"time",pageSize:8,visible:!1,showLast:!0,showNext:!0,showAmount:!0,tableData:[],allData:[]}},mounted:function(){this.title=this.title_barRank},methods:{handleChange:function(e){this.checked=e.target.value,this.fnRank(this.bftitle,this.params,this.bftotal)},handleMore:function(){this.visible=!0},fnRank:function(e,t,a){"2"===a?(this.showAmount=!1,this.checked="time",this.$refs.xTable.hideColumn(this.$refs.xTable.getColumnByField("fee"))):(this.showAmount=!0,this.$refs.xTable.showColumn(this.$refs.xTable.getColumnByField("fee"))),this.bftitle=e,this.params=t,this.title=e+this.title_barRank;var i=0,n=0;Object.keys(t).forEach((function(e){return i+=t[e].ape804})),Object.keys(t).forEach((function(e){return n+=t[e].warningFre}));var r=Object.keys(t).map((function(e){return{deptName:t[e].aae386,deptNo:e,count:t[e].warningFre,percent:0===i?0:(100*(t[e].ape804/i).toFixed(2)).toFixed(0),fee:t[e].ape804,warningFre:t[e].warningFre,projectFre:(100*(t[e].warningFre/n).toFixed(2)).toFixed(0)}}));"time"==this.checked?r.sort((function(e,t){return t.count-e.count})):r.sort((function(e,t){return t.fee-e.fee}));var o,s=1,c=(0,l.Z)(r);try{for(c.s();!(o=c.n()).done;){var u=o.value;u.deptSeq=s,s++}}catch(f){c.e(f)}finally{c.f()}this.allData=r,this.page=0,this.flushPage(),this.$refs.xTable.hideColumn(this.$refs.xTable.getColumnByField("deptNo"))},topMore:function(){0!==this.page&&(this.page--,this.flushPage())},flushPage:function(){this.showLast=this.page>0,this.showNext=this.pageSize*(this.page+1)<this.allData.length;var e=this.page*this.pageSize;this.tableData=this.allData.slice(e,e+this.pageSize)},bottomMore:function(){(this.page+1)*this.pageSize<this.allData.length&&(this.page++,this.flushPage())},cellClickEvent:function(e){var t=e.row,a={departmentCode:t.deptNo};this.$emit("fnLinkQuery","dept",t.deptName,a)}}},c=s,u=a(1001),f=(0,u.Z)(c,i,n,!1,null,"2827cd1e",null),d=f.exports},50567:function(e,t,a){a.d(t,{Z:function(){return d}});var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{staticStyle:{"border-bottom":"1px solid rgba(153,153,153,0.14)"}},[a("ta-title",{attrs:{title:e.title}},[a("span",{staticStyle:{display:"inline-block",float:"right"}},[a("ta-radio-group",{staticStyle:{"margin-right":"10px"},attrs:{size:"small",value:e.checked},on:{change:e.handleChange}},[a("ta-radio-button",{attrs:{value:"time"}},[e._v("预警次数")]),a("ta-radio-button",{attrs:{disabled:!e.showAmount,value:"money"}},[e._v("预警金额")])],1),a("a",{staticClass:"more",on:{click:function(t){return e.handleMore()}}},[e._v("查看更多>>")]),a("ta-button",{staticClass:"downloadChart",attrs:{title:"下载图表",type:"primary",shape:"circle",icon:"download",size:"small"},on:{click:e.downloadChart}})],1)])],1),a("div",{attrs:{id:"mainchart"}}),a("ta-modal",{attrs:{width:"90%",height:"650px",footer:null,destroyOnClose:!0,title:"预警项目明细"},model:{value:e.visible,callback:function(t){e.visible=t},expression:"visible"}},[a("detail-info",{attrs:{type:e.type,paramData:e.paramData,url:"/departmentAnalysis/queryRuleDetails"}})],1)],1)},n=[],l=(a(39575),a(38012),a(88412)),r=a(98526),o=(a(75969),a(1708)),s={name:"ruleRankBar",components:{TaTitle:l.Z,detailInfo:r.Z},props:{paramData:Object,title_barRank:String,type:String,url:String},data:function(){return{title:"",bftitle:"",params:{},visible:!1,checked:"time",unit:"次",showLast:!1,showNext:!1,showAmount:!0,page:0,pageSize:5,dataCake:[],allData:[],costData:[],bfdata:{},bfcostType:{},option:{}}},mounted:function(){this.title=this.title_barRank},methods:{downloadChart:function(){for(var e=this.myChart.getDataURL({type:"png",pixelRatio:2,backgroundColor:"#fff"}),t=atob(e.split(",")[1]),a=e.split(",")[0].split(":")[1].split(";")[0],i=new ArrayBuffer(t.length),n=new Uint8Array(i),l=0;l<t.length;l++)n[l]=t.charCodeAt(l);var r=new Blob([i],{type:a});saveAs(r,this.title+".png")},formatUnit:function(e,t){return e>=1e8&&"%"!=t?"亿"+t:e>=1e4&&"%"!=t?"万"+t:t},formatAmount:function(e){if(isNaN(e)||!e)return"";var t=parseFloat(e),a=String(t.toFixed(2)),i=/(-?\d+)(\d{3})/;while(i.test(a))a=a.replace(i,"$1,$2");return a},formatDependingOnValue:function(e){return Number.isFinite(e)?e>=1e4?this.formatAmount(e/1e4):0==e?0:this.formatAmount(e):"-"},handleChange:function(e){this.checked=e.target.value,this.fnQuery(this.bftitle,this.bfdata,this.bfcostType)},handleMore:function(){this.visible=!0},fnQuery:function(e,t,a,i){"2"===i?(this.showAmount=!1,this.checked="time"):this.showAmount=!0,this.dataCake=[],this.costData=[],this.bftitle=e,this.title=e+this.title_barRank,this.bfdata=t;var n=0,l=0;this.bfcostType=a,this.allData=Object.keys(t).map((function(e){return{name:t[e].aaa167,warningFre:t[e].warningFre,ape804:t[e].ape804}}));for(var r=0;r<this.allData.length;r++)n+=this.allData[r].warningFre,l+=this.allData[r].ape804;"time"==this.checked?this.allData.sort((function(e,t){return t.warningFre-e.warningFre})):this.allData.sort((function(e,t){return t.ape804-e.ape804}));for(var s=this.allData.length<5?this.allData.length:5,c=0;c<s;c++){if("time"==this.checked){this.unit="次";var u=new this.ruleobjs(this.allData[c].name,this.allData[c].warningFre,Math.round(100*(this.allData[c].warningFre/n).toFixed(2))+"%")}else{this.unit="元";u=new this.ruleobjs(this.allData[c].name,this.allData[c].ape804,Math.round(100*(this.allData[c].ape804/l).toFixed(2))+"%")}this.dataCake.push(u)}if(this.allData.length>5){for(var f=0,d=0,h=5;h<this.allData.length;h++)f+=this.allData[h].warningFre,d+=this.allData[h].ape804;if("time"==this.checked){this.unit="次";u=new this.ruleobjs("其他",f,Math.round(100*(f/n).toFixed(2))+"%")}else{this.unit="元";u=new this.ruleobjs("其他",d,Math.round(100*(d/l).toFixed(2))+"%")}this.dataCake.push(u)}for(var m=Object.keys(a).map((function(e){return{name:a[e].costTypName,warningFre:a[e].warningFre,ape804:a[e].ape804}})),p=0;p<m.length;p++)n+=m.warningFre,l+=m.ape804;for(var b=0;b<m.length;b++){if("time"==this.checked)var g=new this.ruleobjs2(m[b].warningFre,m[b].name);else g=new this.ruleobjs2(m[b].ape804,m[b].name);this.costData.push(g)}if(0==this.dataCake.length){var y=document.getElementById("mainchart");return y.innerHTML="暂无数据",y.style.cssText="text-align:center; color: #303133; border: none;line-height: 260px",void y.removeAttribute("_echarts_instance_")}this.option=this.echartsDataBuild(this.dataCake),this.myChart=o.init(document.getElementById("mainchart")),this.myChart.setOption(this.option)},ruleobjs:function(e,t,a){this.name=e,this.value=t,this.percentage=a},ruleobjs2:function(e,t){this.value=e,this.name=t},echartsDataBuild:function(e){var t=this,a=e,i=this.costData,n=this.unit;return{backgroundColor:"#fff",tooltip:{show:!0,confine:!0,appendToBody:!0,trigger:"item",backgroundColor:"#fff",borderColor:"#ccc",borderWidth:1,textStyle:{color:"#333"},formatter:"{a} <br/>{b}: {c} "+t.unit},legend:{icon:"circle",orient:"horizontal",top:"top",padding:[5,5,0,0],data:e.map((function(e){return e.name})),textStyle:{color:"#333",fontSize:12,rich:{a:{fontSize:14},b:{verticalAlign:"right",align:"right",fontSize:14},c:{verticalAlign:"right",align:"right",fontSize:14}}},formatter:function(e){for(var i,l,r=0;r<a.length;r++)a[r].name===e&&(i=a[r].value,l=a[r]&&a[r].percentage.startsWith("NaN")?"0%":a[r].percentage);e=o.format.truncateText(e,100,"14px Microsoft Yahei","...");var s=["{a|"+e+"\t}","{b|"+(isNaN(l)?"":l)+"\t}","{c|"+t.formatDependingOnValue(i)+t.formatUnit(i,n)+"}"];return s.join("")}},series:[{name:"预警规则分布",type:"pie",radius:["40%","60%"],center:["50%","65%"],avoidLabelOverlap:!1,color:["#0086ff","#36cbcb","#4dcb73","#fad337","#94cbff","#ABF0F4"],itemStyle:{borderRadius:10,borderColor:"#fff",borderWidth:2},label:{show:!1},emphasis:{label:{show:!1,fontSize:"20",fontWeight:"bold"}},labelLine:{show:!1},data:a},{name:"预警规则分布",type:"pie",selectedMode:"single",radius:[0,"30%"],center:["50%","65%"],color:["#c9e5ff","#94cbff","#3aa0ff","#0A61D0"],label:{position:"outer",fontSize:14,color:"#333"},labelLine:{show:!0,length:55,length2:10,lineStyle:{color:"#333"}},data:i}]}}}},c=s,u=a(1001),f=(0,u.Z)(c,i,n,!1,null,"038feb60",null),d=f.exports},90357:function(e,t,a){a.d(t,{Z:function(){return f}});var i=function(){var e=this,t=this,a=t.$createElement,i=t._self._c||a;return i("div",[i("ta-form",{staticStyle:{"padding-top":"10px"},attrs:{autoFormCreate:function(t){e.form=t},layout:"horizontal",formLayout:!0}},[i("ta-form-item",{attrs:{fieldDecoratorId:"allDate",labelCol:{span:8},wrapperCol:{span:15},span:6,fieldDecoratorOptions:{rules:[{required:!0,message:"必须选取日期"}]},required:!0,"init-value":t.rangeValue}},[i("span",{attrs:{slot:"label"},slot:"label"},[t._v("时间范围")]),i("ta-range-picker",{attrs:{"allow-one":!0}})],1),i("ta-form-item",{attrs:{fieldDecoratorId:"scene",label:"审核场景",span:6,labelCol:{span:8},required:!0,wrapperCol:{span:15}}},[i("ta-select",{attrs:{placeholder:"请选择开单场景","collection-type":"AAE500","collection-filter":t.filterList,reverseFilter:!0}})],1),i("ta-form-item",{attrs:{fieldDecoratorId:"akb020",span:6,labelCol:{span:8},wrapperCol:{span:15}}},[i("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[t._v("院区标识")]),i("ta-select",{attrs:{placeholder:"院区选择",allowClear:"",options:t.akb020List,disabled:t.paramsDisable.akb020}})],1),i("ta-form-item",{attrs:{fieldDecoratorId:"medinsuType",span:6,labelCol:{span:8},wrapperCol:{span:15}}},[i("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[t._v("医保类型")]),i("ta-select",{attrs:{placeholder:"医保类型选择","collection-type":"AAE141",allowClear:""}})],1),i("ta-form-item",{attrs:{fieldDecoratorId:"insuType",span:6,labelCol:{span:8},wrapperCol:{span:15}}},[i("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[t._v("险种类型")]),i("ta-select",{attrs:{placeholder:"险种类型选择","collection-type":"AAE140",allowClear:""}})],1),i("ta-form-item",{attrs:{fieldDecoratorId:"result",span:6,"init-value":t.resultInit,labelCol:{span:8},wrapperCol:{span:15}}},[i("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[t._v("预审结果")]),i("ta-select",{attrs:{showSearch:"",mode:"multiple","collection-type":"APE800","collection-filter":"0",maxTagCount:1,placeholder:"预审结果选择",allowClear:""}})],1),i("ta-form-item",{attrs:{"field-decorator-id":"operate",span:6,labelCol:{span:8},wrapperCol:{span:16},label:"医护操作"}},[i("ta-select",{attrs:{mode:"multiple",maxTagCount:2,showSearch:"",placeholder:"医护操作筛选",allowClear:"",collectionType:"APE893"}})],1),i("div",{staticStyle:{display:"flex","margin-left":"50px","margin-right":"10px",float:"right"}},[i("ta-button",{attrs:{type:"primary",icon:"search"},on:{click:t.fnQuery}},[t._v("查询")]),i("ta-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"default",icon:"redo"},on:{click:t.fnReset}},[t._v("重置")])],1)],1)],1)},n=[],l=a(48534),r=(a(36133),a(83231)),o={name:"searchTerm",props:{},data:function(){return{filterList:"",rangeValue:[TaUtils.getMoment(new Date((new Date).getTime()).toISOString().slice(0,8)+"01","YYYY-MM-DD"),TaUtils.getMoment((new Date).toISOString().slice(0,10),"YYYY-MM-DD")],akb020List:[],resultInit:[],permissions:{},paramsDisable:{akb020:!1}}},created:function(){var e=this;return(0,l.Z)(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,r.Z.permissionCheck();case 2:e.permissions=t.sent;case 3:case"end":return t.stop()}}),t)})))()},mounted:function(){var e=this;this.fnQueryHos(),this.fnQueryAa01(),this.$nextTick((function(){e.queryOptons()}))},methods:{fnQueryAa01:function(){var e=this;this.Base.submit(null,{url:"miimCommonRead/getAa01Aaa005ByAaz499",data:{aaz499:206}},{successCallback:function(t){var a=t.data;e.resultInit="n"===a.aaa005.toLowerCase()?["1","2"]:[]},failCallback:function(t){e.$message.error("查询aa01表数据失败")}})},queryOptons:function(){var e=this,t=[],a=["2","3","4","5","6","7","16","17","18"];this.Base.submit(null,{url:"miimCommonRead/getAa01AAE500StartStop",data:{aaz499s:JSON.stringify(a)}},{successCallback:function(a){var i=a.data.aae500List;i.includes(12)||i.includes(4)||i.includes(5)||i.includes(7)?(t=i.filter((function(e){return 12!==e&&4!==e&&5!==e&&7!==e})),t.push("dscg")):t=i,e.filterList=t.join(","),e.form.setFieldsValue({scene:t[0].toString()}),e.fnQuery()},failCallback:function(t){e.$message.error("查询场景开关失败")}})},fnReset:function(){this.form.resetFields(),this.form.setFieldsValue({scene:"1",akb020:this.akb020}),this.fnQuery()},fnQuery:function(){var e=this.form.getFieldsValue();e.allDate.length<1?this.$message.error("日期不能为空"):(e.startDate=e.allDate[0].format("YYYY-MM-DD HH:mm:ss"),e.endDate=e.allDate[1].format("YYYY-MM-DD HH:mm:ss"),"night"==e.scene&&(e.startDates=e.allDate[0].format("YYYYMMDD"),e.endDates=e.allDate[1].format("YYYYMMDD")),e.pageNo=0,e.pageSize=5,this.$emit("fnQuery",e))},modifyParameters:function(e){this.form.setFieldsValue({allDate:e})},fnQueryHos:function(){var e=this,t={url:"/miimCommonRead/queryHospRpcList",showPageLoading:!1,autoValid:!0},a={successCallback:function(t){e.akb020List=t.data.resultData,e.permissions&&e.permissions.akb020Set.size>0&&(e.paramsDisable.akb020=!0,e.akb020List=e.akb020List.filter((function(t){return e.permissions.akb020Set.has(t.value)})),e.form.setFieldsValue({akb020:e.akb020List[0].value})),e.akb020=e.akb020List[0].value},failCallback:function(t){e.$message.error("医院数据加载失败")}};this.Base.submit(null,t,a)}}},s=o,c=a(1001),u=(0,c.Z)(s,i,n,!1,null,"0b8af9b2",null),f=u.exports},83231:function(e,t,a){var i=a(48534);a(36133);function n(e){return e?e.akb020&&e.aaz263?"personal":e.akb020&&e.aaz309&&e.aaz307?"group":e.akb020&&e.aaz307?"department":e.akb020?"hospital":"all":"all"}function l(e){return r.apply(this,arguments)}function r(){return r=(0,i.Z)(regeneratorRuntime.mark((function e(t){var a,i,l,r,o,s,c,u,f,d,h,m;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,Base.submit(null,{url:"/miimCommonRead/queryDataPermission",showPageLoading:!1,data:{jobnumber:t},autoValid:!0});case 2:return a=e.sent,i=new Set,l=new Set,a.data.permission.forEach((function(e){var t=n(e);"hospital"===t&&i.add(e.akb020),"department"===t&&l.add(e.aaz307)})),r=a.data.permission.filter((function(e){return"department"===n(e)||!l.has(e.aaz307)})).filter((function(e){return"hospital"===n(e)||!i.has(e.akb020)})),o=new Set(r.map((function(e){return e.akb020})).filter((function(e){return null!=e}))),s=new Set(r.map((function(e){return e.aaz307})).filter((function(e){return null!=e}))),c=new Set(r.map((function(e){return e.aaz309})).filter((function(e){return null!=e}))),u=new Set(r.map((function(e){return e.aaz263})).filter((function(e){return null!=e}))),f=!1,d=!1,h=!1,m=!1,1===o.size&&(f=!0),1===s.size&&1===o.size&&(d=!0),1===s.size&&1===o.size&&1===c.size&&(h=!0),1===o.size&&0===s.size&&1===u.size&&(m=!0),e.abrupt("return",{akb020Set:o,aaz307Set:s,aaz263Set:u,aaz309Set:c,akb020Disable:f,aaz307Disable:d,aaz263Disable:m,aaz309Disable:h});case 20:case"end":return e.stop()}}),e)}))),r.apply(this,arguments)}function o(e,t){Base.submit(null,{url:"miimCommonRead/getAa01AAE500StartStop",data:e},{successCallback:function(e){return t(e)}})}function s(e,t){Base.submit(null,{url:"miimCommonRead/insertTableColumShow",data:e,autoValid:!1},{successCallback:function(e){return t("success")},failCallback:function(e){return t("fail")}})}var c={zbx01:{footer:function(){return"统筹金总额"}},zbx02:{footer:function(){return"基金支出总额"}},zbx03:{footer:function(){return"总费用"}},zbx04:{footer:function(){return"总住院日"}}};t["Z"]={permissionCheck:l,getAa01AAE500StartStop:o,insertTableColumShow:s,props:c,moneyNumFormat:function(e){var t=e.cellValue;return t||"—"}}}}]);
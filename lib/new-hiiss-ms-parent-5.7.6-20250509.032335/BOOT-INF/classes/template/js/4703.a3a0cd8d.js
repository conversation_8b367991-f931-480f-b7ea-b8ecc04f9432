"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[4703],{88412:function(t,e,a){var i=a(26263),l=a(36766),s=a(1001),n=(0,s.Z)(l.Z,i.s,i.x,!1,null,"5e7ef0ae",null);e["Z"]=n.exports},24703:function(t,e,a){a.r(e),a.d(e,{default:function(){return f}});var i=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("div",{staticClass:"fit"},[i("ta-border-layout",{attrs:{layout:{header:"110px",footer:"0px"},showPadding:!0,"footer-cfg":{showBorder:!1}}},[i("div",{attrs:{slot:"header"},slot:"header"},[i("ta-title",{attrs:{title:"查询条件"}}),i("ta-form",{staticStyle:{"padding-top":"5px"},attrs:{autoFormCreate:function(e){t.baseInfoForm=e},layout:"horizontal",formLayout:!0}},[i("ta-form-item",{attrs:{span:5,"field-decorator-id":"msgService",labelCol:{span:8},wrapperCol:{span:14},label:"消息业务"}},[i("ta-select",{attrs:{placeholder:"消息业务筛选","allow-clear":""}},[i("ta-select-option",{attrs:{value:"1"}},[e._v("审核")]),i("ta-select-option",{attrs:{value:"2"}},[e._v("申诉")])],1)],1),i("ta-form-item",{attrs:{span:5,"field-decorator-id":"validStatus",labelCol:{span:8},wrapperCol:{span:14},label:"模板状态"}},[i("ta-select",{attrs:{placeholder:"模板状态筛选","allow-clear":""}},[i("ta-select-option",{attrs:{value:"1"}},[e._v("有效")]),i("ta-select-option",{attrs:{value:"2"}},[e._v("无效")])],1)],1),i("ta-form-item",{attrs:{span:6,"field-decorator-id":"useStatus",labelCol:{span:8},wrapperCol:{span:14},label:"是否选用"}},[i("ta-select",{attrs:{placeholder:"是否选用筛选","allow-clear":""}},[i("ta-select-option",{attrs:{value:"1"}},[e._v("是")]),i("ta-select-option",{attrs:{value:"0"}},[e._v("否")])],1)],1),i("ta-button",{staticStyle:{"margin-right":"10px",float:"right"},attrs:{type:"primary",icon:"plus"},on:{click:e.newlyadded}},[e._v("新增")]),i("ta-button",{staticStyle:{"margin-right":"20px",float:"right"},attrs:{type:"primary",icon:"search"},on:{click:e.fnQuery}},[e._v("查询")])],1)],1),i("div",{staticClass:"fit",staticStyle:{height:"95%"}},[i("ta-title",{staticStyle:{width:"50%"},attrs:{title:"查询结果"}}),i("ta-button",{staticStyle:{"margin-top":"-35px",float:"right"},on:{click:function(t){return e.ChangeTpStatus("invalid")}}},[e._v("无效")]),i("ta-button",{staticStyle:{"margin-top":"-35px",float:"right","margin-right":"70px"},on:{click:function(t){return e.ChangeTpStatus("effective")}}},[e._v("有效")]),i("span",{staticStyle:{"margin-top":"-30px",float:"right","margin-right":"140px"}},[e._v("变更模板状态:")]),i("ta-big-table",{ref:"Table",attrs:{height:"100%","auto-resize":"",resizable:"","highlight-hover-row":"",border:"",size:"mini",data:e.userList}},[i("ta-big-table-column",{attrs:{type:"seq",align:"center",title:"序号",width:"50",sortable:""}}),i("ta-big-table-column",{attrs:{align:"center",type:"checkbox",width:"40"}}),i("ta-big-table-column",{attrs:{align:"center",field:"msgService",title:"消息业务",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;t.column;return[i("div",{staticStyle:{position:"relative"}},["1"===a.msgService?i("div",[e._v(" 审核 ")]):e._e(),"2"===a.msgService?i("div",[e._v(" 申诉 ")]):e._e()])]}}])}),i("ta-big-table-column",{attrs:{align:"center",field:"msgScene",title:"消息场景",width:"140"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;t.column;return[i("div",{staticStyle:{position:"relative"}},["1"===a.msgService?i("div",["1"===a.msgScene?i("div",[e._v(" 每晚预审系统消息 ")]):"2"===a.msgScene?i("div",[e._v(" 每晚预审用户消息 ")]):i("div",[e._v(" 诊疗监测用户消息 ")])]):e._e(),"2"===a.msgService?i("div",["1"===a.msgScene?i("div",[e._v(" 申诉下发 ")]):"2"===a.msgScene?i("div",[e._v(" 申诉提交 ")]):i("div",[e._v(" 申诉短信平台 ")])]):e._e()])]}}])}),i("ta-big-table-column",{attrs:{field:"mouldId",align:"center",title:"模板ID",width:"80"}}),i("ta-big-table-column",{attrs:{field:"title",align:"center",title:"标题",width:"190"}}),i("ta-big-table-column",{attrs:{field:"content","header-align":"center",title:"内容",width:"480"}}),i("ta-big-table-column",{attrs:{field:"receiveRole",align:"center",title:"接收角色","collection-type":"ROLE_MSG",width:"190"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;t.column;return[i("div",{staticStyle:{position:"relative"}},[a.receiveRole?i("div",[e._v(" "+e._s(e.CollectionLabel("ROLE_MSG",a.receiveRole))+" ")]):i("div",[e._v(" — ")])])]}}])}),i("ta-big-table-column",{attrs:{field:"dataType",align:"center",title:"疑点类型",width:"190"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;t.column;return[i("div",{staticStyle:{position:"relative"}},["1"===a.dataType?i("div",[e._v(" 初审疑点 ")]):"3"===a.dataType?i("div",[e._v(" 病例申诉 ")]):i("div",[e._v(" — ")])])]}}])}),i("ta-big-table-column",{attrs:{field:"glurl",align:"center",title:"关联URL",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;t.column;return[i("div",{staticStyle:{position:"relative"}},["1"===a.glurl?i("div",[e._v(" 是 ")]):e._e(),"0"===a.glurl?i("div",[e._v(" 否 ")]):e._e()])]}}])}),i("ta-big-table-column",{attrs:{field:"validStatus",align:"center",title:"模板状态",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;t.column;return[i("div",{staticStyle:{position:"relative"}},["1"===a.validStatus?i("div",[e._v(" 有效 ")]):e._e(),"0"===a.validStatus?i("div",[e._v(" 无效 ")]):e._e()])]}}])}),i("ta-big-table-column",{attrs:{field:"useStatus",align:"center",title:"是否选用",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;t.column;return[i("div",{staticStyle:{position:"relative"}},["1"===a.useStatus?i("div",{staticStyle:{color:"#3FCC6F"}},[e._v(" 是 ")]):e._e(),"0"===a.useStatus?i("div",{staticStyle:{color:"red"}},[e._v(" 否 ")]):e._e()])]}}])}),i("ta-big-table-column",{attrs:{field:"createTime",align:"center",title:"创建时间",width:"150"}}),i("ta-big-table-column",{attrs:{field:"creator",align:"center",title:"创建人",width:"90"}}),i("ta-big-table-column",{attrs:{fixed:"right","show-overflow":"",field:"operate",title:"操作",width:"auto",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("div",{staticStyle:{position:"relative"}},[i("div",{staticClass:"opareteItem",on:{click:function(t){return e.showEdit(a)}}},[e._v(" 编辑 ")])])]}}])}),i("template",{slot:"bottomBar"},[i("ta-pagination",{ref:"gridPager",staticStyle:{"text-align":"right"},attrs:{defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"],"data-source":e.userList,params:e.infoPageParams,url:"messageTemplate/queryMsgMouldInfoPage"},on:{"update:dataSource":function(t){e.userList=t},"update:data-source":function(t){e.userList=t}}})],1)],2)],1),i("ta-modal",{attrs:{title:"消息模板配置",visible:e.uploadVisible,height:650,width:1e3},on:{ok:e.handleOk,cancel:function(){t.uploadVisible=!1,t.reLoad=!0}}},[i("ta-form",{staticStyle:{"margin-left":"50px"},attrs:{"auto-form-create":function(e){return t.form=e},layout:"horizontal","form-layout":!0,"label-width":"80px",col:{xs:1,sm:1,md:1}}},[i("ta-form-item",{attrs:{label:"消息业务","init-value":"1","field-decorator-id":"msgService",require:!0}},[i("ta-radio-group",{attrs:{name:"radioGroup1"},on:{change:e.onChange1}},[i("ta-radio",{attrs:{value:"1"}},[e._v("审核")]),i("ta-radio",{attrs:{value:"2"}},[e._v("申诉")])],1)],1),i("ta-form-item",{attrs:{label:"消息场景","init-value":e.msgScenario,"field-decorator-id":"msgScene",require:!0}},[i("ta-select",{staticStyle:{width:"30%"},attrs:{options:e.msgScenarioList,placeholder:"消息场景选择","allow-clear":""},on:{change:e.onChange3}}),i("span",{directives:[{name:"show",rawName:"v-show",value:this.appealSMS,expression:"this.appealSMS"}],staticStyle:{"margin-left":"5px"}},[e._v(" 接收角色: ")]),i("ta-select",{directives:[{name:"show",rawName:"v-show",value:this.appealSMS,expression:"this.appealSMS"}],staticStyle:{width:"20%"},attrs:{"default-value":"1",value:e.receiveRole,"collection-type":"ROLE_MSG",placeholder:"接收角色选择"},on:{change:e.handleChange2}}),i("span",{directives:[{name:"show",rawName:"v-show",value:this.appealSMS,expression:"this.appealSMS"}],staticStyle:{"margin-left":"5px"}},[e._v(" 疑点类型: ")]),i("ta-select",{directives:[{name:"show",rawName:"v-show",value:this.appealSMS,expression:"this.appealSMS"}],staticStyle:{width:"25%"},attrs:{"default-value":"1",value:e.dataType,placeholder:"疑点类型选择"},on:{change:e.handleChange3}},[i("ta-select-option",{attrs:{value:"1"}},[e._v("初审疑点")]),i("ta-select-option",{attrs:{value:"3"}},[e._v("病例申诉")])],1)],1),i("ta-form-item",{attrs:{label:"关联URL","init-value":"1","field-decorator-id":"glurl",require:!0}},[i("ta-radio-group",{attrs:{name:"radioGroup2"}},[i("ta-radio",{attrs:{value:"1"}},[e._v("是")]),i("ta-radio",{attrs:{value:"0"}},[e._v("否")])],1)],1),i("ta-form-item",{attrs:{label:"是否选用","init-value":"1","field-decorator-id":"useStatus",require:!0}},[i("ta-radio-group",{attrs:{name:"radioGroup3"}},[i("ta-radio",{attrs:{value:"1"}},[e._v("是")]),i("ta-radio",{attrs:{value:"0"}},[e._v("否")])],1)],1),i("ta-form-item",{attrs:{label:"发送方式","init-value":"1","field-decorator-id":"sendMod",require:!0}},[i("ta-radio-group",{attrs:{name:"radioGroup4"},on:{change:e.onChange2}},[i("ta-radio",{attrs:{value:"1"}},[e._v("实时发送")]),i("ta-radio",{attrs:{value:"2",disabled:"2-1"==this.msgScenario}},[e._v("审核场景触发")])],1),i("ta-select",{directives:[{name:"show",rawName:"v-show",value:"2-1"!=this.msgScenario,expression:"this.msgScenario != '2-1'"}],staticStyle:{width:"30%"},attrs:{"default-value":"1",value:e.selectSendValue,disabled:e.selectSend,placeholder:"审核场景选择"},on:{change:e.handleChange}},[i("ta-select-option",{attrs:{value:"1"}},[e._v("医嘱审核")]),i("ta-select-option",{attrs:{value:"2"}},[e._v("计费审核")])],1)],1),i("ta-form-item",{attrs:{label:"消息标题","field-decorator-id":"title",require:!0}},[i("ta-textarea",{staticStyle:{width:"70%"},attrs:{id:"title",placeholder:"待处理违规信息",rows:2}}),i("ta-button",{attrs:{angle:"15px",type:"link"},on:{click:e.insertMsg1}},[i("ta-icon",{attrs:{type:"plus-square",theme:"twoTone",twoToneColor:"#1B65B9"}}),e._v("插入消息变量")],1)],1),i("ta-form-item",{attrs:{label:"消息内容","field-decorator-id":"content",require:!0}},[i("ta-textarea",{staticStyle:{width:"70%"},attrs:{id:"content",placeholder:"消息内容",rows:8}}),i("ta-button",{attrs:{angle:"15px",type:"link"},on:{click:e.insertMsg2}},[i("ta-icon",{attrs:{type:"plus-square",theme:"twoTone",twoToneColor:"#1B65B9"}}),e._v("插入消息变量")],1),i("br"),i("span",{staticStyle:{color:"red"}},[e._v("插入消息变量时，请先将鼠标移到对应的位置")])],1)],1)],1),i("ta-modal",{attrs:{visible:e.uploadVisible2,height:340,width:400},on:{cancel:function(){t.uploadVisible2=!1,t.reLoad=!0}}},[i("ta-big-table",{attrs:{border:"",stripe:"",resizable:"","highlight-hover-row":"",height:"100%",data:e.tableData},on:{"cell-dblclick":e.cellDBLClickEvent}},[i("ta-big-table-column",{attrs:{field:"xxbl",title:"消息变量"}}),i("ta-big-table-column",{attrs:{field:"bds",title:"表达式"}})],1),i("div",{staticStyle:{height:"10px"},attrs:{slot:"footer"},slot:"footer"},[i("ta-button",{staticStyle:{"margin-top":"-35px"},attrs:{type:"link"}},[e._v("双击插入消息变量")])],1)],1)],1)],1)},l=[],s=a(88412),n=a(36797),r=a.n(n),o=[],c={name:"msgTemplateMg",components:{TaTitle:s.Z},data:function(){return{msgScenarioList:[],userList:[],tableData:[{xxbl:"消息发送人姓名",bds:"Message.S_Username"},{xxbl:"消息发送人科室",bds:"Message.S_DeptName"},{xxbl:"消息发送明细数量",bds:"Message.DetailCounts"},{xxbl:"消息接收人姓名",bds:"Message.R_Username"},{xxbl:"消息用户备注内容",bds:"Message.UserRemark"},{xxbl:"申诉年月",bds:"Message.aae043"},{xxbl:"医保类型",bds:"Message.aae141"}],userColumns:o,uploadVisible:!1,selectSend:!0,uploadVisible2:!1,flag:"",appealSMS:!1,msgScenario:"",selectionStart:0,selectionEnd:0,selectSendValue:"1",receiveRole:"1",dataType:"1",bfrecord:{},result:"",configflag:""}},mounted:function(){this.fnQuery()},methods:{moment:r(),handleChange:function(t){this.selectSendValue=t},handleChange2:function(t){this.receiveRole=t},handleChange3:function(t){this.dataType=t},onChange1:function(t){"1"==t.target.value?(this.msgScenarioList=[{value:"1-1",label:"每晚预审系统消息"},{value:"1-2",label:"每晚预审用户消息"},{value:"1-3",label:"诊疗监测用户消息"}],this.msgScenario="1-1"):(this.msgScenarioList=[{value:"2-1",label:"申诉下发"},{value:"2-2",label:"申诉提交"},{value:"2-3",label:"申诉短信平台"}],this.msgScenario="2-1",this.form.setFieldsValue({sendMod:"1"})),this.appealSMS=!1,this.form.setFieldsValue({msgScene:this.msgScenario})},onChange2:function(t){this.selectSend="1"==t.target.value},onChange3:function(t){this.appealSMS="2-3"==t},insertStr:function(t,e,a){return t.slice(0,e)+a+t.slice(e)},cellDBLClickEvent:function(t){var e=t.row,a=this.form.getFieldsValue();if("title"==this.flag)if(null==a.title||""==a.title)this.form.setFieldsValue({title:"{"+e.bds+"}"});else{var i=this.insertStr(a.title,this.selectionStart,"{"+e.bds+"}");this.form.setFieldsValue({title:i})}else if(null==a.content||""==a.content)this.form.setFieldsValue({content:"{"+e.bds+"}"});else{var l=this.insertStr(a.content,this.selectionStart,"{"+e.bds+"}");this.form.setFieldsValue({content:l})}this.uploadVisible2=!1},insertMsg1:function(){var t=document.getElementById("title").selectionStart,e=document.getElementById("title").selectionEnd;this.selectionStart=t,this.selectionEnd=e,t==e?(document.getElementById("title").focus(),this.flag="title",this.uploadVisible2=!0):this.$message.error("请先将鼠标移到对应的位置")},insertMsg2:function(){var t=document.getElementById("content").selectionStart,e=document.getElementById("content").selectionEnd;this.selectionStart=t,this.selectionEnd=e,t==e?(document.getElementById("content").focus(),this.flag="content",this.uploadVisible2=!0):this.$message.error("请先将鼠标移到对应的位置")},handleOk:function(){var t=this,e=this.form.getFieldsValue();"2"==e.sendMod&&(e.auditSce=this.selectSendValue),this.appealSMS&&(e.receiveRole=this.receiveRole,e.dataType=this.dataType),e.msgScene=e.msgScene.split("-")[1],"edit"==this.configflag?this.form.validateFields((function(a){a||(e.id=t.bfrecord.id,t.Base.submit(null,{url:"messageTemplate/editMsgMouldInfo",data:e,autoValid:!0},{successCallback:function(e){t.configflag="",t.bfrecord={},t.fnQuery(),t.uploadVisible=!1,t.$message.success("修改成功")},failCallback:function(e){t.$message.error("修改失败")}}))})):this.form.validateFields((function(a){a||t.Base.submit(null,{url:"messageTemplate/addMsgMouldInfo",data:e,autoValid:!0},{successCallback:function(e){t.configflag="",t.bfrecord={},t.fnQuery(),t.uploadVisible=!1,t.$message.success("新增成功")},failCallback:function(e){t.$message.error("新增失败")}})}))},showEdit:function(t){var e=this;this.configflag="edit","1"==t.msgService?this.msgScenarioList=[{value:"1-1",label:"每晚预审系统消息"},{value:"1-2",label:"每晚预审用户消息"},{value:"1-3",label:"诊疗监测用户消息"}]:this.msgScenarioList=[{value:"2-1",label:"申诉下发"},{value:"2-2",label:"申诉提交"},{value:"2-3",label:"申诉短信平台"}],"1"==t.sendMod?this.selectSend=!0:(this.selectSend=!1,this.selectSendValue=t.auditSce),this.$nextTick((function(){e.form.setFieldsValue(t),e.form.setFieldsValue({msgScene:t.msgService+"-"+t.msgScene}),e.appealSMS=t.msgService+"-"+t.msgScene=="2-3",e.appealSMS&&(t.receiveRole=e.receiveRole,t.dataType=e.dataType)})),this.bfrecord=t,this.uploadVisible=!0},ChangeTpStatus:function(t){var e=this,a=this.$refs.Table.getCheckboxRecords();if(0!=a.length){var i,l=a.map((function(t){return t.id}));i="effective"==t?"1":"0",this.Base.submit(null,{url:"messageTemplate/updateValidStatus",data:{ids:l.toString(),validStatus:i}},{successCallback:function(t){e.fnQuery(),e.$message.success("修改成功")},failCallback:function(t){e.$message.error("修改失败")}})}else this.$message.error("请至少选择一行数据！")},infoPageParams:function(){var t=this.baseInfoForm.getFieldsValue();return t},fnQuery:function(){var t=this;this.$nextTick((function(){t.$refs.gridPager.loadData((function(t){}))}))},newlyadded:function(){var t=this;this.configflag="add",this.uploadVisible=!0,this.selectSend=!0,this.appealSMS=!1,this.$nextTick((function(){t.form.resetFields();var e=t.form.getFieldsValue();"1"==e.msgService?(t.msgScenarioList=[{value:"1-1",label:"每晚预审系统消息"},{value:"1-2",label:"每晚预审用户消息"},{value:"1-3",label:"诊疗监测用户消息"}],t.msgScenario="1-1"):(t.msgScenarioList=[{value:"2-1",label:"申诉下发"},{value:"2-2",label:"申诉提交"}],t.msgScenario="2-1")}))},fnCustomHeaderCell:function(t){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px",fontWeight:"bold"}}},filterOption:function(t,e){return e.componentOptions.children[0].text.indexOf(t)>=0}}},d=c,u=a(1001),v=(0,u.Z)(d,i,l,!1,null,"059b104e",null),f=v.exports},36766:function(t,e,a){var i=a(66586);e["Z"]=i.Z},26263:function(t,e,a){a.d(e,{s:function(){return i},x:function(){return l}});var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},l=[]},66586:function(t,e){var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};e["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}}}]);
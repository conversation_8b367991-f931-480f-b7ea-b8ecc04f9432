"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[8112],{43658:function(t,e,a){a.d(e,{Z:function(){return u}});var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("ta-modal",{attrs:{title:"result"===t.dataType?"上传结果":"错误数据",visible:t.visible,height:"result"===t.dataType&&"all"===t.addType?"200px":"700px",width:"result"===t.dataType&&"all"===t.addType?"500px":"1000px",footer:!1,destroyOnClose:""},on:{cancel:t.handleCancel}},[a("ta-border-layout",{attrs:{"show-border":!1,layout:{header:"200px"},"header-cfg":{showBorder:!1}}},[a("div",{attrs:{slot:"header"},slot:"header"},["result"===t.dataType?a("div",[a("span",{staticClass:"header-info"},[t._v("新增条数："+t._s(t.resultData.successNum))]),a("ta-button",{attrs:{type:"primary",disabled:0===Number(t.resultData.successNum)&&0===Number(t.resultData.editNum)},on:{click:t.uploadFile}},[t._v("确认上传")]),a("div",[a("span",{staticStyle:{color:"red"}},[t._v("*")]),t._v("提交意见："),a("ta-textarea",{attrs:{rows:4,placeholder:"请输入"},model:{value:t.descValue,callback:function(e){t.descValue=e},expression:"descValue"}})],1)],1):t._e(),"error"===t.dataType?a("div",[a("span",{staticClass:"header-info"},[t._v("错误数据条数："+t._s(t.resultData.errorNum))])]):t._e()]),"result"===t.dataType?a("div",{staticClass:"fit"},[a("div",{staticStyle:{"margin-bottom":"10px"}},[t._v(" 新增条目 ")]),a("div",{},[a("ta-big-table",{attrs:{height:390,data:0===t.successList.length&&t.resultData.successList?t.resultData.successList.slice(0,t.pageSizeInsert):t.successList,border:"","auto-resize":!0,"show-overflow":""},scopedSlots:t._u([{key:"bottomBar",fn:function(){return[a("ta-pagination",{staticStyle:{"text-align":"right"},attrs:{size:"small",defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"],total:Number(t.resultData.successNum),showSizeChanger:""},on:{change:t.handlePageNumberChangeInsert,showSizeChange:t.handlePageSizeChangeInsert}})]},proxy:!0}],null,!1,1027493839)},[a("ta-big-table-column",{attrs:{title:"序号",type:"seq",width:"60",align:"center"}}),a("ta-big-table-column",{attrs:{field:"ake001",title:"三目编码","min-width":"100",align:"center"}}),a("ta-big-table-column",{attrs:{field:"ake002",title:"三目名称","min-width":"100",align:"center"}})],1)],1)]):t._e(),"error"===t.dataType?a("div",{staticClass:"fit"},[a("div",{staticStyle:{"margin-bottom":"10px"}},[t._v(" 错误数据列表 ")]),a("div",{staticStyle:{height:"390px"}},[a("ta-big-table",{attrs:{height:390,data:0===t.errorList.length&&t.resultData.errorList?t.resultData.errorList.slice(0,t.pageSizeError):t.errorList,border:"","auto-resize":!0,"show-overflow":""},scopedSlots:t._u([{key:"bottomBar",fn:function(){return[a("ta-pagination",{staticStyle:{"text-align":"right"},attrs:{size:"small",defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"],total:Number(t.resultData.errorNum),showSizeChanger:""},on:{change:t.handlePageNumberChangeError,showSizeChange:t.handlePageSizeChangeError}})]},proxy:!0}],null,!1,893380724)},[a("ta-big-table-column",{attrs:{title:"序号",type:"seq",width:"60",align:"center"}}),a("ta-big-table-column",{attrs:{field:"ake001",title:"三目编码","min-width":"100",align:"center"}}),a("ta-big-table-column",{attrs:{field:"ake002",title:"三目名称","min-width":"100",align:"center"}}),a("ta-big-table-column",{attrs:{field:"errorInfo",title:"原因","min-width":"200",align:"center"}})],1)],1)]):t._e()])],1)},s=[],l=a(66347),i={name:"addImport",props:{addType:{type:String,default:"add"},dataType:{type:String,default:"result"},visible:{type:Boolean,default:!0},resultData:{type:Object,default:function(){return{}}}},data:function(){return{pageSizeOptions:["10","20","30","40","50"],pageNumberInsert:1,pageSizeInsert:10,pageNumberEdit:1,pageSizeEdit:10,pageNumberError:1,pageSizeError:10,successList:[],editList:[],errorList:[],descValue:void 0}},methods:{handlePageData:function(t,e,a){this[t]=this.resultData[t].slice((this[a]-1)*this[e],this[a]*this[e])},handlePageNumberChangeInsert:function(t){this.pageNumberInsert=t,this.handlePageData("successList","pageSizeInsert","pageNumberInsert")},handlePageSizeChangeInsert:function(t,e){this.pageNumberInsert=t,this.pageSizeInsert=e,this.handlePageData("successList","pageSizeInsert","pageNumberInsert")},handlePageNumberChangeEdit:function(t){this.pageNumberEdit=t,this.handlePageData("errorList","pageSizeEdit","pageNumberEdit")},handlePageSizeChangeEdit:function(t,e){this.pageNumberEdit=t,this.pageSizeEdit=e,this.handlePageData("errorList","pageSizeEdit","pageNumberEdit")},handlePageNumberChangeError:function(t){this.pageNumberError=t,this.handlePageData("errorList","pageSizeError","pageNumberError")},handlePageSizeChangeError:function(t,e){this.pageNumberError=t,this.pageSizeError=e,this.handlePageData("errorList","pageSizeError","pageNumberError")},userPageParams:function(){},handleCancel:function(t){this.$emit("close",!1)},uploadFile:function(){var t=this;if(this.descValue&&""!=this.descValue.trim()){var e,a=(0,l.Z)(this.resultData.successList);try{for(a.s();!(e=a.n()).done;){var r=e.value;r.log=this.descValue}}catch(i){a.e(i)}finally{a.f()}var s="/mtt/localruleconfig/ruleQuery/submitFile";this.Base.submit(null,{url:s,data:this.resultData,autoQs:!1}).then((function(e){t.$message.success("导入成功"),t.$emit("close",!0)})).catch((function(e){t.$emit("close",!0)}))}else this.$message.info("请输入意见")}}},o=i,n=a(1001),c=(0,n.Z)(o,r,s,!1,null,"2624ec68",null),u=c.exports},1352:function(t,e,a){a.d(e,{Z:function(){return d}});var r=function(){var t=this,e=this,a=e.$createElement,r=e._self._c||a;return r("div",{staticStyle:{"margin-top":"-20px"}},[r("ta-card",{attrs:{bordered:!1}},[r("div",{staticClass:"title"},[e._v("查询条件")]),r("ta-form",{attrs:{"auto-form-create":function(e){return t.searchForm=e},"label-width":110}},[r("ta-row",[r("ta-col",{attrs:{span:4}},[r("ta-form-item",{attrs:{label:"三目编码","field-decorator-id":"ake001"}},[r("ta-input")],1)],1),r("ta-col",{attrs:{span:4}},[r("ta-form-item",{attrs:{label:"三目名称","field-decorator-id":"ake002"}},[r("ta-input")],1)],1),r("ta-col",{attrs:{span:4}},[r("ta-form-item",{attrs:{label:"三目类型","field-decorator-id":"ake003"}},[r("ta-select",{attrs:{"collection-type":"MAKE003","allow-clear":!0}})],1)],1),r("ta-col",{attrs:{span:6}},[r("ta-form-item",{attrs:{label:"限制条件","field-decorator-id":"yke011"}},[r("ta-input")],1)],1),r("ta-col",{attrs:{span:3}},[r("ta-form-item",{attrs:{label:"过滤"}},[r("ta-switch",{attrs:{checkedChildren:"开",unCheckedChildren:"关",defaultChecked:""},on:{change:e.doSearch},model:{value:e.blnFilter,callback:function(t){e.blnFilter=t},expression:"blnFilter"}})],1)],1),r("ta-col",{attrs:{span:3}},[r("ta-button",{on:{click:e.resetForm}},[e._v("重置")]),r("ta-button",{on:{click:e.doSearch}},[e._v("查询")])],1)],1)],1),r("div",{staticClass:"title"},[e._v("三目列表 "),r("ta-button",{staticStyle:{float:"right"},attrs:{type:"primary"},on:{click:e.openAddMka05}},[e._v("新增")])],1),r("ta-big-table",{ref:"itemTable",attrs:{size:"small",data:e.dataSource,"big-data-checkbox":"","header-drag-style":"",border:"full","use-virtual":"",autoResize:"","row-height":40,height:380}},[r("ta-big-table-column",{attrs:{type:"checkbox",width:"55"}}),r("ta-big-table-column",{attrs:{type:"seq",width:"60",title:"序号",fixed:""}}),e._l(e.columns,(function(t,e){return r("ta-big-table-column",{key:e,attrs:{align:t.align,border:"full",resizable:t.resizable,"show-overflow":t.overflowTooltip,field:t.dataIndex,title:t.title,fixed:t.fixed,width:t.width,collectionType:t.collectionType}})}))],2),r("ta-pagination",{ref:"itemPager",staticClass:"page",attrs:{size:"small","data-source":e.dataSource,url:"mtt/localruleconfig/ruleQuery/queryTheTernaryPage",params:e.getPageParam,defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"]},on:{"update:dataSource":function(t){e.dataSource=t},"update:data-source":function(t){e.dataSource=t}},model:{value:e.pageParam.pageNumber,callback:function(t){e.$set(e.pageParam,"pageNumber",t)},expression:"pageParam.pageNumber"}}),r("div",{staticStyle:{"text-align":"center"}},[r("ta-button",{attrs:{type:"primary"},on:{click:e.addItem}},[e._v("保存")])],1)],1),r("ta-modal",{attrs:{width:500,"destroy-on-close":!0,title:"'新增三目目录'"},model:{value:e.blnMka05Visible,callback:function(t){e.blnMka05Visible=t},expression:"blnMka05Visible"}},[r("ta-form",{attrs:{"auto-form-create":function(e){return t.mka05Form=e}}},[r("ta-row",[r("ta-col",{attrs:{span:24}},[r("ta-form-item",{attrs:{label:"三目编码",fieldDecoratorId:"ake001",require:{message:"三目编码不能为空"}}},[r("ta-input",{attrs:{placeholder:"请输入","allow-clear":!0}})],1)],1),r("ta-col",{attrs:{span:24}},[r("ta-form-item",{attrs:{label:"三目名称",fieldDecoratorId:"ake002",require:{message:"三目名称不能为空"}}},[r("ta-input",{attrs:{placeholder:"请输入","allow-clear":!0}})],1)],1),r("ta-col",{attrs:{span:24}},[r("ta-form-item",{attrs:{label:"三目类型",fieldDecoratorId:"ake003",require:{message:"三目类型不能为空"}}},[r("ta-select",{attrs:{placeholder:"请选择","collection-type":"MAKE003","allow-clear":!0}})],1)],1),r("ta-col",{attrs:{span:24}},[r("ta-form-item",{attrs:{label:"限制条件",fieldDecoratorId:"yke011"}},[r("ta-textarea",{attrs:{rows:4,placeholder:"请输入"}})],1)],1)],1)],1),r("div",{attrs:{slot:"footer"},slot:"footer"},[r("ta-button",{on:{click:function(t){e.blnMka05Visible=!1}}},[e._v("取消")]),r("ta-button",{attrs:{type:"primary"},on:{click:e.doSaveMka05}},[e._v("确定")])],1)],1),r("ta-modal",{attrs:{"destroy-on-close":!0,title:"添加意见"},model:{value:e.blnRuleReasonVisible,callback:function(t){e.blnRuleReasonVisible=t},expression:"blnRuleReasonVisible"}},[r("span",{staticStyle:{color:"red"}},[e._v("*")]),e._v("添加意见："),r("ta-textarea",{attrs:{rows:4,placeholder:"请输入"},model:{value:e.descValue,callback:function(t){e.descValue=t},expression:"descValue"}}),r("div",{attrs:{slot:"footer"},slot:"footer"},[r("ta-button",{attrs:{type:"primary"},on:{click:e.doAddItem}},[e._v("确定")]),r("ta-button",{on:{click:function(t){e.blnRuleReasonVisible=!1}}},[e._v("取消")])],1)],1)],1)},s=[],l=a(66347),i=a(95082),o={name:"addItem",components:{},props:{record:Object},data:function(){return{blnFilter:!0,columns:[{dataIndex:"ake001",title:"三目编码",align:"center",width:250,overflowTooltip:!0},{dataIndex:"ake002",title:"三目名称",align:"center",width:250,overflowTooltip:!0},{dataIndex:"ake003",title:"三目类型",collectionType:"MAKE003",align:"center",width:250,overflowTooltip:!0},{dataIndex:"yke011",title:"限制条件",align:"center",overflowTooltip:!0}],dataSource:[],pageParam:{pageNumber:1},blnMka05Visible:!1,blnRuleReasonVisible:!1,descValue:void 0}},methods:{openAddMka05:function(){this.blnMka05Visible=!0},doSaveMka05:function(){var t=this;this.mka05Form.validateFields((function(e){if(!e){var a=t.mka05Form.getFieldsValue();Base.submit(null,{url:"mtt/localruleconfig/ruleQuery/addTheTernary",data:(0,i.Z)({},a)}).then((function(e){t.$message.success("添加成功"),t.blnMka05Visible=!1}))}}))},resetForm:function(){this.searchForm.resetFields(),this.blnFilter=!0},doSearch:function(){this.pageParam.pageNumber=1,this.$refs.itemPager.loadData()},getPageParam:function(){return(0,i.Z)({ykz277:this.record.ykz277,filtration:this.blnFilter},this.searchForm.getFieldsValue())},addItem:function(){var t=this.$refs.itemTable.getCheckboxRecords();0!==t.length?(this.descValue=void 0,this.blnRuleReasonVisible=!0):this.$message.error("请勾选数据")},doAddItem:function(){if(this.descValue){var t={},e=this.$refs.itemTable.getCheckboxRecords();e=e.reduce((function(e,a){return!t[a.ake001]&&(t[a.ake001]=e.push(a)),e}),[]);var a,r=(0,l.Z)(e);try{for(r.s();!(a=r.n()).done;){var s=a.value;s.ykz277=this.record.ykz277,s.log=this.descValue}}catch(i){r.e(i)}finally{r.f()}this.$emit("onOk",e)}else this.$message.info("请输入添加意见")}}},n=o,c=a(1001),u=(0,c.Z)(n,r,s,!1,null,"4205455d",null),d=u.exports},72049:function(t,e,a){a.d(e,{Z:function(){return c}});var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",t._l(t.logList,(function(e,r){return a("div",{key:r,staticStyle:{"border-bottom":"1px dashed black","padding-bottom":"10px","padding-top":"10px"}},[a("ta-row",["2"==e.type?a("ta-col",{attrs:{span:6}},[t._v("停用人:")]):t._e(),"4"==e.type?a("ta-col",{attrs:{span:6}},[t._v("启用人:")]):t._e(),"3"==e.type?a("ta-col",{attrs:{span:6}},[t._v("变更人:")]):t._e(),"1"==e.type?a("ta-col",{attrs:{span:6}},[t._v("提交人:")]):t._e(),a("ta-col",{attrs:{span:18}},[t._v(t._s(e.name))])],1),a("ta-row",["2"==e.type?a("ta-col",{attrs:{span:6}},[t._v("停用时间:")]):t._e(),"4"==e.type?a("ta-col",{attrs:{span:6}},[t._v("启用时间:")]):t._e(),"3"==e.type?a("ta-col",{attrs:{span:6}},[t._v("变更时间:")]):t._e(),"1"==e.type?a("ta-col",{attrs:{span:6}},[t._v("提交时间:")]):t._e(),a("ta-col",{attrs:{span:18}},[t._v(t._s(e.date))])],1),a("ta-row",["2"==e.type?a("ta-col",{attrs:{span:6}},[t._v("停用意见:")]):t._e(),"4"==e.type?a("ta-col",{attrs:{span:6}},[t._v("启用意见:")]):t._e(),"3"==e.type?a("ta-col",{attrs:{span:6}},[t._v("修改意见:")]):t._e(),"1"==e.type?a("ta-col",{attrs:{span:6}},[t._v("提交意见:")]):t._e(),a("ta-col",{attrs:{span:18}},[t._v(t._s(e.note))])],1),a("div",{staticStyle:{height:"20px"}})],1)})),0)},s=[],l={name:"ruleLog",props:{record:Object},data:function(){return{logList:[]}},mounted:function(){this.queryRuleLog()},methods:{queryRuleLog:function(){var t=this;Base.submit(null,{url:"mtt/localruleconfig/ruleQuery/queryMkf75Log",data:{ykz277:this.record.ykz277,ake001:this.record.ake001}}).then((function(e){t.logList=e.data.data}))}}},i=l,o=a(1001),n=(0,o.Z)(i,r,s,!1,null,"36b52390",null),c=n.exports},37585:function(t,e,a){a.d(e,{Z:function(){return c}});var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",t._l(t.logList,(function(e,r){return a("div",{key:r,staticStyle:{"border-bottom":"1px dashed black","padding-bottom":"10px","padding-top":"10px"}},[a("ta-row",["2"==e.type?a("ta-col",{attrs:{span:6}},[t._v("停用人:")]):t._e(),"4"==e.type?a("ta-col",{attrs:{span:6}},[t._v("启用人:")]):t._e(),"3"==e.type?a("ta-col",{attrs:{span:6}},[t._v("明细更新人:")]):t._e(),a("ta-col",{attrs:{span:6}},[t._v(t._s(e.name))])],1),a("ta-row",["2"==e.type?a("ta-col",{attrs:{span:6}},[t._v("停用时间:")]):t._e(),"4"==e.type?a("ta-col",{attrs:{span:6}},[t._v("启用时间:")]):t._e(),"3"==e.type?a("ta-col",{attrs:{span:6}},[t._v("明细更新时间:")]):t._e(),a("ta-col",{attrs:{span:6}},[t._v(t._s(e.date))])],1),a("ta-row",["2"==e.type?a("ta-col",{attrs:{span:6}},[t._v("停用意见:")]):t._e(),"4"==e.type?a("ta-col",{attrs:{span:6}},[t._v("启用意见:")]):t._e(),"3"==e.type?a("ta-col",{attrs:{span:6}},[t._v("明细更新意见:")]):t._e(),a("ta-col",{attrs:{span:6}},[t._v(t._s(e.note))])],1)],1)})),0)},s=[],l={name:"ruleLog",props:{record:Object},data:function(){return{logList:[]}},mounted:function(){this.queryRuleLog()},methods:{queryRuleLog:function(){var t=this;Base.submit(null,{url:"mtt/localruleconfig/ruleQuery/queryLogById",data:{id:this.record.ykz276}}).then((function(e){t.logList=e.data.data}))}}},i=l,o=a(1001),n=(0,o.Z)(i,r,s,!1,null,"8218fb22",null),c=n.exports}}]);
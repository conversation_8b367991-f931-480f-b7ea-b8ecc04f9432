(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[7919],{88412:function(t,e,a){"use strict";var r=a(26263),n=a(36766),i=a(1001),o=(0,i.Z)(n.Z,r.s,r.x,!1,null,"5e7ef0ae",null);e["Z"]=o.exports},78691:function(t,e,a){"use strict";a.r(e),a.d(e,{default:function(){return y}});var r=function(){var t=this,e=this,a=e.$createElement,r=e._self._c||a;return r("div",{staticClass:"fit"},[r("ta-border-layout",{attrs:{layout:{header:"110px",footer:"0px"},showPadding:!0,"footer-cfg":{showBorder:!1}}},[r("div",{attrs:{slot:"header"},slot:"header"},[r("ta-title",{attrs:{title:"查询条件"}}),r("ta-form",{staticStyle:{"padding-top":"5px"},attrs:{autoFormCreate:function(e){t.baseInfoForm=e},col:2,formLayout:!0}},[r("ta-form-item",{attrs:{"label-col":{span:7},span:4,"wrapper-col":{span:17},fieldDecoratorId:"allDate",fieldDecoratorOptions:{rules:[{required:!0,message:"必须选取日期"}]},required:!0,"init-value":e.rangeValue,label:"出院日期"}},[r("ta-range-picker",{attrs:{"allow-one":!0}},[r("ta-icon",{attrs:{slot:"suffixIcon",type:""},slot:"suffixIcon"})],1)],1),r("ta-form-item",{attrs:{"label-col":{span:8},span:4,"wrapper-col":{span:16},"field-decorator-id":"aae500",label:"出院场景"}},[r("ta-select",{attrs:{showSearch:"",placeholder:"请选择出院场景","collection-type":"AAE500","collection-filter":e.filterList,reverseFilter:!0}})],1),r("ta-form-item",{attrs:{"label-col":{span:8},span:4,"wrapper-col":{span:16},"field-decorator-id":"akb020",label:"院区标识"}},[r("ta-select",{attrs:{placeholder:"院区选择",allowClear:"",options:e.akb020List,disabled:e.paramsDisable.akb020}})],1),r("ta-form-item",{attrs:{span:8}}),r("ta-form-item",{attrs:{label:" ","label-col":{span:8},span:4,"wrapper-col":{span:16}}},[r("ta-button",{attrs:{type:"primary",icon:"search"},on:{click:e.fnQuery}},[e._v("查询")]),r("ta-button",{attrs:{type:"default",icon:"redo"},on:{click:e.fnReset}},[e._v("重置")])],1)],1)],1),r("div",{staticClass:"fit",staticStyle:{height:"88%"}},[r("ta-title",{staticStyle:{width:"50%"},attrs:{title:"查询结果"}}),r("ta-big-table",{ref:"Table",attrs:{height:"100%","show-footer":"","footer-method":e.footerMethod,"highlight-hover-row":"","highlight-current-row":"","tooltip-config":{enabled:!0,contentMethod:e.showTooltipMethod},"auto-resize":"","show-overflow":"",resizable:"","empty-text":"-",border:"",data:e.userList},on:{"cell-click":e.cellClickEvent}},[r("ta-big-table-column",{attrs:{type:"seq","header-align":"center",align:"center",title:"序号",width:"50",sortable:""}}),r("ta-big-table-column",{attrs:{sortable:"",field:"dscgSum","header-align":"center",align:"right",title:"出院人数","min-width":"130"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[r("ta-button",{attrs:{size:"small",type:"link"}},[r("span",{staticStyle:{color:"#00bcff","text-decoration":"underline"}},[e._v(e._s(a.dscgSum))])])]}}])}),r("ta-big-table-column",{attrs:{sortable:"",field:"violationDscgSum","header-align":"center",align:"right",title:"违规出院人数","min-width":"150"}}),r("ta-big-table-column",{attrs:{sortable:"",field:"wgcyrszb","header-align":"center",align:"right",title:"违规出院人数占比","min-width":"180"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[r("span",[e._v(e._s(parseFloat(0===a.dscgSum?0:a.violationDscgSum/a.dscgSum*100).toFixed(2)+"%"))])]}}])}),r("ta-big-table-column",{attrs:{sortable:"",field:"complianceAfterOperateDscgSum","header-align":"center",align:"right",title:"修改后合规出院人数","min-width":"180"}}),r("ta-big-table-column",{attrs:{sortable:"",field:"complianceDscgSum","header-align":"center",align:"right",title:"合规出院人数","min-width":"150"}}),r("ta-big-table-column",{attrs:{sortable:"",field:"ape804","header-align":"center",align:"right",formatter:e.moneyFormat,title:"预警金额","min-width":"130"}}),r("template",{slot:"bottomBar"},[r("ta-button",{staticStyle:{float:"right","margin-top":"20px"},attrs:{icon:"download",type:"primary"},on:{click:e.exportTable}},[e._v("导出 ")]),r("ta-pagination",{ref:"gridPager",staticStyle:{float:"right","margin-top":"20px","margin-right":"10px"},attrs:{defaultPageSize:100,pageSizeOptions:["100","200","500","1000","1500"],"data-source":e.userList,params:e.infoPageParams,url:"individualDemand/dscgInfoSummary/querySummaryData"},on:{"update:dataSource":function(t){e.userList=t},"update:data-source":function(t){e.userList=t}}})],1)],2)],1)])],1)},n=[],i=a(66347),o=a(48534),s=a(95082),l=(a(36133),a(88412)),u=a(36797),c=a.n(u),f=a(22722),m=a(92566),p=a(55115),d=a(37269),h=a(83231);p.w3.prototype.Base=Object.assign(p.w3.prototype.Base,(0,s.Z)({},f.Z));var g={name:"dscgReportStatisticsHBALL",components:{TaTitle:l.Z},data:function(){return{userList:[],filterList:"",treeData:[],rangeValue:[this.Base.getMoment((new Date).toISOString().slice(0,8)+"-01","YYYY-MM-DD"),this.Base.getMoment((new Date).toISOString().slice(0,10),"YYYY-MM-DD")],akb020List:[],permissions:{},paramsDisable:{akb020:!1}}},mounted:function(){var t=this;return(0,o.Z)(regeneratorRuntime.mark((function e(){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,h.Z.permissionCheck();case 2:t.permissions=e.sent,t.fnQueryHos(),a=["5","6","7","18"],h.Z.getAa01AAE500StartStop({aaz499s:JSON.stringify(a)},(function(e){var a=e.data.aae500List;a.includes(12)&&a.push(13),t.filterList=a.join(",");var r=a[0].toString();t.baseInfoForm.setFieldsValue({aae500:r}),t.fnQuery()}));case 6:case"end":return e.stop()}}),e)})))()},methods:{moment:c(),fnQueryHos:function(){var t=this,e={url:"/miimCommonRead/queryHospRpcList",showPageLoading:!1,autoValid:!0},a={successCallback:function(e){t.akb020List=e.data.resultData,t.permissions&&t.permissions.akb020Set.size>0&&(t.paramsDisable.akb020=!0,t.akb020List=t.akb020List.filter((function(e){return t.permissions.akb020Set.has(e.value)})),t.baseInfoForm.setFieldsValue({akb020:t.akb020List[0].value})),t.akb020=t.akb020List[0].value},failCallback:function(e){t.$message.error("医院数据加载失败")}};this.Base.submit(null,e,a)},footerMethod:function(t){var e=t.columns,a=t.data;return[e.map((function(t,e){return 0===e?"合计":["dscgSum","violationDscgSum","complianceAfterOperateDscgSum","complianceDscgSum","ape804"].includes(t.property)?(0,m.Z)(a,t.property):null}))]},showTooltipMethod:function(t){var e=t.type,a=t.column,r=(t.row,t.items,t._columnIndex,a.property);if("header"===e)return d.Z.props[r]?d.Z.props[r][e]:""},cellClickEvent:function(t){t.row;var e=t.column,a=e.property;if("dscgSum"===a){var r=this.baseInfoForm.getFieldsValue();this.Base.openTabMenu({id:Math.floor(90*Math.random())+10,name:"出院情况统计-科室",url:"reportStatistics.html#/dscgReportStatisticsHBKS?params=".concat(JSON.stringify(r)),refresh:!1})}},exportTable:function(){var t=this.baseInfoForm.getFieldsValue();if(void 0===t.allDate||t.allDate.length<2)this.$message.error("请选择时间范围！");else{t=this.infoPageParams();var e,a=[],r=this.$refs.Table.getColumns(),n=(0,i.Z)(r);try{for(n.s();!(e=n.n()).done;){var o=e.value;"序号"!==o.title&&a.push({header:o.title,key:o.property,width:20})}}catch(l){n.e(l)}finally{n.f()}this.userList.forEach((function(t){t.wgcyrszb=parseFloat(0===t.dscgSum?0:t.violationDscgSum/t.dscgSum*100).toFixed(2)+"%"}));var s={fileName:"出院情况统计-全院("+t.allDate[0].format("YYYY-MM-DD")+"~"+t.allDate[1].format("YYYY-MM-DD")+")",sheets:[{name:"worksheet1",column:{complex:!1,columns:a},rows:this.userList}]};this.Base.generateExcel(s)}},formatAmount:function(t){return t.toFixed(2).replace(/\d(?=(\d{3})+\.)/g,"$&,")},moneyFormat:function(t){var e=t.cellValue;return e&&0!=e?this.formatAmount(e):"0"},ratioFormat:function(t){var e=t.cellValue;return e&&0!=e?e+"%":"0"},fnReset:function(){this.baseInfoForm.resetFields(),this.fnQuery()},infoPageParams:function(){var t=this.baseInfoForm.getFieldsValue();return t.startDateStr=t.allDate[0].format("YYYY-MM-DD HH:mm:ss"),t.endDateStr=t.allDate[1].format("YYYY-MM-DD HH:mm:ss"),t},fnQuery:function(){var t=this,e=this.baseInfoForm.getFieldValue("allDate");!e||e[0]&&e[1]?this.$nextTick((function(){t.$refs.gridPager.loadData((function(t){}))})):this.$message.error("请选择时间范围！")},fnCustomHeaderCell:function(t){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px",fontWeight:"bold"}}},filterOption:function(t,e){return e.componentOptions.children[0].text.indexOf(t)>=0}}},b=g,v=a(1001),S=(0,v.Z)(b,r,n,!1,null,"2bef47b6",null),y=S.exports},36766:function(t,e,a){"use strict";var r=a(66586);e["Z"]=r.Z},26263:function(t,e,a){"use strict";a.d(e,{s:function(){return r},x:function(){return n}});var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},n=[]},66586:function(t,e){"use strict";var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};e["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}},83231:function(t,e,a){"use strict";var r=a(48534);a(36133);function n(t){return t?t.akb020&&t.aaz263?"personal":t.akb020&&t.aaz309&&t.aaz307?"group":t.akb020&&t.aaz307?"department":t.akb020?"hospital":"all":"all"}function i(t){return o.apply(this,arguments)}function o(){return o=(0,r.Z)(regeneratorRuntime.mark((function t(e){var a,r,i,o,s,l,u,c,f,m,p,d;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Base.submit(null,{url:"/miimCommonRead/queryDataPermission",showPageLoading:!1,data:{jobnumber:e},autoValid:!0});case 2:return a=t.sent,r=new Set,i=new Set,a.data.permission.forEach((function(t){var e=n(t);"hospital"===e&&r.add(t.akb020),"department"===e&&i.add(t.aaz307)})),o=a.data.permission.filter((function(t){return"department"===n(t)||!i.has(t.aaz307)})).filter((function(t){return"hospital"===n(t)||!r.has(t.akb020)})),s=new Set(o.map((function(t){return t.akb020})).filter((function(t){return null!=t}))),l=new Set(o.map((function(t){return t.aaz307})).filter((function(t){return null!=t}))),u=new Set(o.map((function(t){return t.aaz309})).filter((function(t){return null!=t}))),c=new Set(o.map((function(t){return t.aaz263})).filter((function(t){return null!=t}))),f=!1,m=!1,p=!1,d=!1,1===s.size&&(f=!0),1===l.size&&1===s.size&&(m=!0),1===l.size&&1===s.size&&1===u.size&&(p=!0),1===s.size&&0===l.size&&1===c.size&&(d=!0),t.abrupt("return",{akb020Set:s,aaz307Set:l,aaz263Set:c,aaz309Set:u,akb020Disable:f,aaz307Disable:m,aaz263Disable:d,aaz309Disable:p});case 20:case"end":return t.stop()}}),t)}))),o.apply(this,arguments)}function s(t,e){Base.submit(null,{url:"miimCommonRead/getAa01AAE500StartStop",data:t},{successCallback:function(t){return e(t)}})}function l(t,e){Base.submit(null,{url:"miimCommonRead/insertTableColumShow",data:t,autoValid:!1},{successCallback:function(t){return e("success")},failCallback:function(t){return e("fail")}})}var u={zbx01:{footer:function(){return"统筹金总额"}},zbx02:{footer:function(){return"基金支出总额"}},zbx03:{footer:function(){return"总费用"}},zbx04:{footer:function(){return"总住院日"}}};e["Z"]={permissionCheck:i,getAa01AAE500StartStop:s,insertTableColumShow:l,props:u,moneyNumFormat:function(t){var e=t.cellValue;return e||"—"}}},37269:function(t,e){"use strict";var a=[{columnKey:"jxxmzb",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}},{columnKey:"txl",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}},{columnKey:"zfxmzb",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}}],r=[{columnKey:"jxxmzb",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}},{columnKey:"zfxmzb",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}}],n={txl:{header:function(){return"提醒率=提醒总次数/审核总次数"}},jxxmzb:{header:function(){return"继续使用占比=继续使用次数(次)/提醒总次数"}},zfxmzb:{header:function(){return"自费使用占比=自费使用次数/提醒总次数"}},wczxmzb:{header:function(){return"无操作使用占比=无操作使用次数/提醒总次数"}}},i=new Map([["4","医生站出院审核"],["5","医保办出院审核"],["7","护士站出院审核"],["12","医生站转科审核"],["13","护士站转科审核"]]);e["Z"]={codelist:a,codelist2:r,optionsMap:i,props:n}},55382:function(){},61219:function(){}}]);
"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[7353],{5990:function(t,e,a){a.r(e),a.d(e,{default:function(){return z}});var i=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("ta-layout",{staticClass:"fit",staticStyle:{"background-color":"#fff"}},[i("ta-drawer",{staticClass:"left-drawer",class:{"more-message":e.moreMessage},attrs:{placement:"left",visible:e.drawerVisible,closable:!1,mask:!1},on:{close:function(){e.drawerVisible=!1}},scopedSlots:e._u([{key:"title",fn:function(){return[i("ta-title",{attrs:{title:"患者列表"}})]},proxy:!0}])},[i("div",{staticClass:"drawer-toggler",class:{"drawer-open":e.drawerVisible},staticStyle:{color:"rgb(39, 108, 245)"},on:{click:e.toggleMoreMessage}},[e.moreMessage?i("ta-icon",{attrs:{type:"double-left"}}):i("ta-icon",{attrs:{type:"double-right"}})],1),i("ta-radio-group",{directives:[{name:"show",rawName:"v-show",value:e.permissions&&!e.permissions.aaz263Disable,expression:"permissions && !permissions.aaz263Disable"}],staticStyle:{"margin-left":"10px"},on:{change:e.queryTableData},model:{value:e.dataRange,callback:function(t){e.dataRange=t},expression:"dataRange"}},[i("ta-radio-button",{attrs:{value:"self"}},[e._v(" 本人 ")]),i("ta-radio-button",{attrs:{value:"group"}},[e._v(" 医疗组 ")])],1),i("ta-select",{directives:[{name:"show",rawName:"v-show",value:e.permissions&&!e.permissions.aaz263Disable&&!e.permissions.aaz309Disable,expression:"permissions && !permissions.aaz263Disable && !permissions.aaz309Disable"}],staticStyle:{"margin-left":"10px",width:"calc(100% - 5px)","max-width":"130px"},attrs:{disabled:"group"!==e.dataRange,options:e.aaz309List},on:{change:e.aaz309Change},model:{value:e.aaz309,callback:function(t){e.aaz309=t},expression:"aaz309"}}),i("div",{staticClass:"part-top1"},[i("div",{staticClass:"part-title"},[i("tag-change",{attrs:{"tag-value":e.tagValue,"status-arr":e.statusArr},on:{change:e.changeTag}})],1)]),i("div",{staticStyle:{height:"calc(100% - 110px)","margin-left":"10px"}},[i("ta-big-table",{ref:"xTable",staticStyle:{width:"calc(100% - 10px)",cursor:"pointer"},attrs:{border:"","highlight-hover-row":"","show-overflow":"","sort-config":{trigger:"cell"},"checkbox-config":{trigger:"row"},data:e.dataSource,height:"auto",resizable:!0,"auto-resize":"",size:"mini","empty-text":"-",width:"min-width","row-style":e.rowStyle,"cell-style":e.cellStyle},on:{"cell-click":e.getPatientInfo},scopedSlots:e._u([{key:"empty",fn:function(){return[i("ta-empty",{attrs:{description:e.emptyDescription}})]},proxy:!0}])},[i("ta-big-table-column",{attrs:{type:"seq",fixed:"left",width:"20px",title:" ",align:"center"}}),i("ta-big-table-column",{attrs:{field:"aaz217",title:"管理Id",visible:!1}}),i("ta-big-table-colgroup",{scopedSlots:e._u([{key:"header",fn:function(){return[i("ta-input",{attrs:{placeholder:"筛选患者/床位",size:"small",type:"type"},on:{keyup:e.searchEvent},model:{value:e.filterParam,callback:function(t){e.filterParam=t},expression:"filterParam"}})]},proxy:!0}])},[i("ta-big-table-column",{attrs:{sortable:"",field:"name",title:"患者",align:"left",width:e.widthConfig.name}}),i("ta-big-table-column",{attrs:{sortable:"",field:"bch",title:"床位",align:"left","min-width":e.widthConfig.bch}})],1),i("ta-big-table-column",{attrs:{sortable:"",field:"akc191",title:"就诊号",align:"center",width:"120px"}}),i("ta-big-table-column",{attrs:{field:"aze003",title:"处理进度",align:"center",width:"110px"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[a.aze003.indexOf("待处理")>-1?i("div",[i("ta-icon",{attrs:{type:"exclamation-circle"}}),e._v(" "+e._s(a.aze003)+" ")],1):i("div",[i("ta-icon",{attrs:{type:"check-circle"}}),e._v(" "+e._s(a.aze003)+" ")],1)]}}])})],1)],1),e.support.length>0?i("div",{staticClass:"supportDiv"},[e._v(" "+e._s(e.supportText)+"   "),e._l(e.support,(function(t,a){return i("span",{key:a},[i("span",{staticStyle:{display:"inline-block",width:"auto"}},[e._v(e._s(t.supportnum)+"  ( "+e._s(t.supportname)+");")]),e._v("   ")])}))],2):e._e()],1),i("ta-layout-content",{staticClass:"fit mian-content",staticStyle:{"margin-left":"28%"}},[i("div",[i("ta-form",{staticStyle:{"margin-left":"17px","margin-top":"3px"},attrs:{"auto-form-create":function(e){return t.form=e},layout:"horizontal","form-layout":!0,"label-width":"80px",col:{xs:1,sm:1,md:1,lg:1,xl:1,xxl:1}}},[i("ta-form-item",{attrs:{label:"患者信息","field-decorator-id":"opterName"}},[e._v(" "+e._s(e.patientDetails.optername||"")+" ")]),"1"===e.whetherDiagStats?i("ta-form-item",{attrs:{label:"临床诊断","field-decorator-id":"clDiagNameList"}},[e._v(" "+e._s(e.patientDetails.cldiagnamelist||"")+" ")]):e._e(),"1"===e.whetherDiagStats?i("ta-form-item",{attrs:{label:"医保诊断","field-decorator-id":"hiDiagNameList"}},[i("span",{domProps:{innerHTML:e._s(e.patientDiagName)}})]):e._e(),"0"===e.whetherDiagStats?i("ta-form-item",{attrs:{label:"诊断","field-decorator-id":"allDiagNameList"}},[i("span",{domProps:{innerHTML:e._s(e.patientDiagName+" "+e.patientDetails.cldiagnamelist)}})]):e._e()],1),e.patientObjects.length?i("div",{staticClass:"part-top",staticStyle:{"margin-top":"0 !important"}},[i("div",{staticClass:"part-title"},[i("ta-title",{staticStyle:{float:"left"},attrs:{title:"待审查项目"}}),i("ta-tooltip",{staticStyle:{"margin-left":"5px"},attrs:{placement:"top"}},[i("template",{slot:"title"},[i("span",[e._v("双击可查看规则内涵,点击行展开查看项目明细,需要点击展开行操作覆盖才会生效")])]),i("ta-icon",{attrs:{type:"question-circle"}})],2)],1)]):e._e(),e.patientObjects.length?i("div",{style:{width:"97%",marginLeft:"10px",marginTop:"5px",height:this.height+"px"}},["n"!=e.customTipsText.toLocaleLowerCase()?i("span",{staticStyle:{"font-size":"12px",height:"12px",color:"#F50"}},[e._v(e._s(this.customTipsText))]):e._e(),i("ta-big-table",{ref:"dTable",staticStyle:{width:"100%",cursor:"pointer"},attrs:{border:"","highlight-hover-row":"","show-overflow":"",data:e.patientObjects,height:"auto",resizable:!0,"auto-resize":"",size:"mini","empty-text":"-",width:"min-width"},on:{"cell-dblclick":e.queryRuleInfo,"cell-click":e.cellClickEvent,"toggle-row-expand":e.toggleExpandChangeEvent},scopedSlots:e._u([e.treeSelectShow?{key:"topBar",fn:function(){return[i("ta-tree-select",{attrs:{allowClear:"",showSearch:"",placeholder:"请选择规则分类",dropdownStyle:{maxHeight:"400px",overflow:"auto"},treeData:e.treeData,treeDataSimpleMode:e.treeData,value:e.treeSelectValue},on:{change:e.treeSelectChange}})]},proxy:!0}:null],null,!0)},[i("ta-big-table-column",{attrs:{type:"expand",visible:!0,width:"20"},scopedSlots:e._u([{key:"content",fn:function(t){var a=t.row;return[i("ta-big-table",{ref:"dTablechildren",staticStyle:{"margin-left":"20px"},attrs:{data:a.children,border:"","highlight-hover-row":"","show-overflow":"","auto-resize":"","empty-text":"-","header-cell-style":e.headerCellStyle,size:"mini",resizable:!0}},[i("ta-big-table-column",{attrs:{type:"seq",width:"20px",align:"center",title:" "}}),i("ta-big-table-column",{attrs:{field:"aaz217",title:"管理Id",visible:!1}}),i("ta-big-table-column",{attrs:{field:"aaz213",title:"管理Id",visible:!1}}),i("ta-big-table-column",{attrs:{width:"72px"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("div",["甲"===a.aka065?i("div",{staticStyle:{float:"left",background:"#67C23A",color:"#FFFFFF",width:"14px"}},[i("a",{staticClass:"tooltipContent",attrs:{href:"#",title:"甲类项目"}},[e._v("甲")])]):"乙"===a.aka065?i("div",{staticStyle:{float:"left",background:"#E6A23C",color:"#FFFFFF",width:"14px"}},[i("a",{staticClass:"tooltipContent",attrs:{href:"#",title:"乙类项目"}},[e._v("乙")])]):"丙"===a.aka065?i("div",{staticStyle:{float:"left",background:"#FF0000",color:"#FFFFFF",width:"14px"}},[i("a",{staticClass:"tooltipContent",attrs:{href:"#",title:"丙类项目"}},[e._v("丙")])]):a.aka065?e._e():i("div"),"2"===a.ape800?i("div",{staticStyle:{float:"right",background:"#FF0000",color:"#FFFFFF",width:"28px"}},[i("a",{staticClass:"tooltipContent2",attrs:{href:"#",title:"违规项目"}},[e._v(e._s(e.CollectionLabel("APE800_ADUIT_TEXT","2")))])]):"1"===a.ape800?i("div",{staticStyle:{float:"right",background:"#E6A23C",color:"#FFFFFF",width:"28px"}},[i("a",{staticClass:"tooltipContent2",attrs:{href:"#",title:"可疑项目"}},[e._v(e._s(e.CollectionLabel("APE800_ADUIT_TEXT","1")))])]):"3"===a.ape800?i("div",{staticStyle:{float:"right",background:"deepskyblue",color:"#FFFFFF",width:"28px"}},[i("a",{staticClass:"tooltipContent2",attrs:{href:"#",title:"仅提醒"}},[e._v(e._s(e.CollectionLabel("APE800_ADUIT_TEXT","3")))])]):a.ape800?e._e():i("div")])]}}],null,!0)}),i("ta-big-table-column",{attrs:{field:"ake006",align:"left",title:"项目名称","show-overflow":!1,"min-width":e.widthConfig.ake006},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("ta-tooltip",{attrs:{placement:"topLeft"}},[i("template",{slot:"title"},[e._v(" "+e._s(a.ake006)+" ")]),i("div",{staticClass:"tex-over-wrapper"},[e._v(" "+e._s(a.ake006)+" ")])],2)]}}],null,!0)}),i("ta-big-table-column",{attrs:{field:"aae036",align:"center",title:"明细发生时间","min-width":e.widthConfig.aae036}}),i("ta-big-table-column",{attrs:{field:"akc226",align:"right",title:"数量",width:e.widthConfig.akc226},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[a.akc226?i("span",[e._v(e._s(a.akc226))]):i("span",[e._v("--")])]}}],null,!0)}),i("ta-big-table-column",{attrs:{field:"aac003",align:"left",title:"开单医生","min-width":e.widthConfig.aac003}}),i("ta-big-table-column",{attrs:{field:"aaz560",align:"left",title:"备案信息","min-width":e.widthConfig.aaz560}}),i("ta-big-table-column",{attrs:{field:"",align:"center",title:"操作","min-width":e.widthConfig.oparete},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("ta-checkbox-group",{staticClass:"custom-anchor",attrs:{value:a.operate}},e._l(a.options,(function(t){return i("span",{key:t.value,staticClass:"opareteItem"},[e._v(" "+e._s(t.label)+"  "),i("ta-checkbox",{attrs:{value:t.value},on:{change:function(t){return e.checkClick2(t,a)}}})],1)})),0)]}}],null,!0)})],1)]}}],null,!1,98728173)}),i("ta-big-table-column",{attrs:{type:"seq",width:"20px",title:" ",align:"center"}}),i("ta-big-table-column",{attrs:{field:"aaz217",title:"管理Id",visible:!1}}),i("ta-big-table-column",{attrs:{field:"aaz213",title:"管理Id",visible:!1}}),i("ta-big-table-column",{attrs:{width:"72px"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("div",["甲"===a.aka065?i("div",{staticStyle:{float:"left",background:"#67C23A",color:"#FFFFFF",width:"14px"}},[i("a",{staticClass:"tooltipContent",attrs:{href:"#",title:"甲类项目"}},[e._v("甲")])]):"乙"===a.aka065?i("div",{staticStyle:{float:"left",background:"#E6A23C",color:"#FFFFFF",width:"14px"}},[i("a",{staticClass:"tooltipContent",attrs:{href:"#",title:"乙类项目"}},[e._v("乙")])]):"丙"===a.aka065?i("div",{staticStyle:{float:"left",background:"#FF0000",color:"#FFFFFF",width:"14px"}},[i("a",{staticClass:"tooltipContent",attrs:{href:"#",title:"丙类项目"}},[e._v("丙")])]):a.aka065?e._e():i("div"),"2"===a.ape800?i("div",{staticStyle:{float:"right",background:"#FF0000",color:"#FFFFFF",width:"28px"}},[i("a",{staticClass:"tooltipContent2",attrs:{href:"#",title:"违规项目"}},[e._v(e._s(e.CollectionLabel("APE800_ADUIT_TEXT","2")))])]):"1"===a.ape800?i("div",{staticStyle:{float:"right",background:"#E6A23C",color:"#FFFFFF",width:"28px"}},[i("a",{staticClass:"tooltipContent2",attrs:{href:"#",title:"可疑项目"}},[e._v(e._s(e.CollectionLabel("APE800_ADUIT_TEXT","1")))])]):"3"===a.ape800?i("div",{staticStyle:{float:"right",background:"deepskyblue",color:"#FFFFFF",width:"28px"}},[i("a",{staticClass:"tooltipContent2",attrs:{href:"#",title:"仅提醒"}},[e._v(e._s(e.CollectionLabel("APE800_ADUIT_TEXT","3")))])]):a.ape800?e._e():i("div")])]}}],null,!1,1823785440)}),i("ta-big-table-column",{attrs:{field:"ake006",title:"项目名称","show-overflow":!1,"min-width":e.widthConfig.ake006},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("ta-tooltip",{attrs:{placement:"topLeft"}},[i("template",{slot:"title"},[e._v(" "+e._s(a.ake006)+" ")]),i("div",{staticClass:"tex-over-wrapper"},[e._v(" "+e._s(a.ake006)+" ")])],2)]}}],null,!1,3034155790)}),i("ta-big-table-column",{attrs:{field:"ykz018",title:"引导信息","show-overflow":!1,"min-width":e.widthConfig.ykz018},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("ta-tooltip",{attrs:{placement:"topLeft"}},[i("template",{slot:"title"},[e._v(" "+e._s(a.ykz018)+" ")]),i("div",{staticClass:"tex-over-wrapper"},[e._v(" "+e._s(a.ykz018)+" ")])],2)]}}],null,!1,3441798542)}),i("ta-big-table-column",{attrs:{field:"akc226",title:"数量",align:"right",width:e.widthConfig.akc226}}),i("ta-big-table-column",{attrs:{field:"akc225",title:"单价",align:"right",width:e.widthConfig.akc225,formatter:"formatAmount"}}),i("ta-big-table-column",{attrs:{field:"aaz560",title:"备案信息","min-width":e.widthConfig.aaz560}}),i("ta-big-table-column",{attrs:{field:"oparete",title:"操作","min-width":e.widthConfig.oparete,align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("ta-checkbox-group",{staticClass:"custom-anchor",attrs:{value:a.operate}},e._l(a.options,(function(t){return i("span",{key:t.value,staticClass:"opareteItem"},[e._v(" "+e._s(t.label)+"  "),i("ta-checkbox",{attrs:{value:t.value},on:{change:function(t){return e.checkClick(t,a)}}})],1)})),0)]}}],null,!1,**********)})],1)],1):e._e(),e.patientBillObjects.length?i("div",{staticClass:"part-top"},[i("div",{staticClass:"part-title"},[i("ta-title",{staticStyle:{float:"left"},attrs:{title:"诊疗监测审查结果"}}),i("ta-tooltip",{staticStyle:{"margin-left":"5px"},attrs:{placement:"top"}},[i("template",{slot:"title"},[i("span",[e._v("双击可查看规则内涵")])]),i("ta-icon",{attrs:{type:"question-circle"}})],2)],1)]):e._e(),e.patientBillObjects.length?i("div",{staticStyle:{width:"97%","margin-left":"10px","margin-top":"5px",height:"250px"}},[i("ta-big-table",{ref:"bTable",staticStyle:{width:"100%",cursor:"pointer"},attrs:{border:"","highlight-hover-row":"","show-overflow":"",data:e.patientBillObjects,height:"auto",resizable:!0,"auto-resize":"",size:"mini","empty-text":"-",width:"min-width"},on:{"cell-dblclick":e.queryRuleInfo}},[i("ta-big-table-column",{attrs:{type:"seq",fixed:"left",width:"50px",title:" ",align:"center"}}),i("ta-big-table-column",{attrs:{field:"aaa167",title:"审查项","min-width":"100px"}}),i("ta-big-table-column",{attrs:{field:"ykz018",title:"引导信息","min-width":"100px"}})],1)],1):e._e(),i("div",{directives:[{name:"show",rawName:"v-show",value:e.buttonVisiable,expression:"buttonVisiable"}],style:{marginLeft:"40%",marginTop:"n"!=e.customTipsText.toLocaleLowerCase()?"27px":"15px"}},[i("ta-button",{staticClass:"ok-btn",attrs:{type:"primary"},on:{click:e.handleSaveCheck}},[e._v(" 确认 ")])],1),i("div",[i("ta-modal",{attrs:{height:"70%",width:"85%","body-style":{paddingRight:0,paddingTop:"5px"},draggable:!0,"destroy-on-close":!0,footer:null},model:{value:e.visible,callback:function(t){e.visible=t},expression:"visible"}},[i("span",{staticStyle:{"font-weight":"normal","font-size":"14px","text-align":"center",height:"0",width:"100%"},attrs:{slot:"title"},slot:"title"},[e._v(" 规则内涵查看")]),i("div",{staticStyle:{"margin-top":"5px",height:"auto"}},[i("div",{staticClass:"knowledgeTitle"},[e._v(" 限制条件 ")]),i("div",{staticClass:"knowledgeRuleContent"},[e._v(" "+e._s(e.ykz018)+" ")])]),i("div",{staticStyle:{width:"100%",height:"calc(100% - 50px)","margin-top":"5px"}},[i("div",{staticClass:"knowledgeTitle"},[e._v(" 规则内涵 ")]),i("div",{staticStyle:{display:"inline-block",width:"90%","vertical-align":"top","margin-left":"8px"}},[i("ta-collapse",{attrs:{id:"appH",accordion:!0},on:{change:e.changeNodeActivekey}},e._l(e.nodeList,(function(t,a){return i("ta-collapse-panel",{key:a,staticClass:"knowledgeContentTitle",attrs:{header:e.convertYkz061(t.ykz061)}},[i("div",{staticStyle:{"text-align":"right"}},[i("ta-input-search",{staticStyle:{width:"200px","margin-bottom":"1px"},attrs:{placeholder:"输入关键字搜索","allow-clear":""},on:{search:e.searchfn}})],1),i("div",[i("ta-big-table",{attrs:{size:"small","header-cell-style":function(){return{padding:"2px"}},data:e.nodeData},scopedSlots:e._u([{key:"bottomBar",fn:function(){return[i("ta-pagination",{staticStyle:{"text-align":"right"},attrs:{"show-size-changer":"",size:"small","default-page-size":200,"page-size-options":["30","50","100","200","500"],total:e.total,"dataSource.sync":"nodeData"},on:{showSizeChange:e.onShowSizeChange,change:e.changePage},model:{value:e.current,callback:function(t){e.current=t},expression:"current"}})]},proxy:!0}],null,!0)},e._l(e.columns,(function(t,e){return i("ta-big-table-column",{key:e,attrs:{field:t.columnField,title:t.columnName}})})),1)],1)])})),1)],1)])])],1),i("div",[i("ta-modal",{attrs:{title:"提示信息",visible:e.promptVisible,confirmLoading:e.promptConfirmLoading,okText:"继续提交",cancelText:"取消提交"},on:{ok:e.handleSave,cancel:e.handleCancel}},[e.promptMessages.length>0?i("div",[e._l(e.promptMessages,(function(t,a){return i("div",[i("p",[e._v(e._s(t))])])})),i("p",[e._v("继续提交将覆盖，系统仅保留您的操作;")]),i("p",[e._v("取消提交将刷新数据，系统将不会保留您的操作;")]),i("p",[e._v("请确认是否继续提交?")])],2):e._e()])],1)],1),i("keep-on-record",{attrs:{visible:e.keepOnRecordVisible,params:e.rowData},on:{handleSave:e.handleUpdateRow,handleClose:e.handleClose}}),i("keep-on-record-two",{ref:"keeponrecord",attrs:{visible:e.keepOnRecordVisible2,params:e.rowData,"ykz010-arr":e.ykz010Arr,"ykz042-arr":e.ykz042Arr},on:{handleSave:e.handleUpdateRow2,handleClose:e.handleClose2}})],1)],1)},n=[],r=a(89584),l=a(48534),s=(a(32564),a(36133),a(93528)),o=a(36797),c=a.n(o),u=a(80506),p=a(88412),d=a(92378),h=a(52058),f=a(94628),g=a(83231),m=[{value:"1",label:"待处理",checked:!0,color1:"orange",color2:"#FCB76B",icon:"待"},{value:"2",label:"已处理",checked:!1,color1:"green",color2:"#87d068",icon:"已"},{value:"3",label:"全部",checked:!1,color1:"blue",color2:"#2db7f5",icon:"全"}],b={normal:{ake006:"130px",ykz018:"130px",akc226:"50px",akc225:"80px",aaz560:"50px",aae036:"50px",aac003:"50px",oparete:"280px",name:"110px",bch:"140px"},mini:{ake006:"90px",ykz018:"90px",akc226:"30px",akc225:"55px",aaz560:"20px",aae036:"30px",aac003:"30px",oparete:"250px",name:"50px",bch:"110px"}},v={name:"approvalHandle",components:{keepOnRecord:d.Z,TagChange:u.Z,TaTitle:p.Z,KeepOnRecordTwo:h.Z},data:function(){return{widthConfig:b.normal,buttonVisiable:!1,moreMessage:!0,drawerVisible:!0,statusArr:m,tagValue:"1",dataSource:[],dataRange:"self",aaz217:"",selectRow:{},whetherDiagStats:"1",patientDetails:{},patientObjects:[],patientBillObjects:[],patientDiagnose:[],treeData:[],patientDiagName:"",keepOnRecordVisible:!1,keepOnRecordVisible2:!1,treeSelectValue:void 0,treeSelectShow:!0,permissions:{aaz263Disable:!0},aaz309List:[],rowData:{},filterParam:"",ykz010Arr:[],ykz042Arr:[],aaz263:"",aaz307:"",systemName:"",cainfo:"",customTipsText:"N",aaz309:null,clientId:"",clickFlag:"",height:220,visible:!1,submitCheckType:"Y",ykz018:"",nodeList:[],activeKey:[],columns:[],nodeData:[],params:{},expandedRow:{},ykz042:"",searchText:"",pageSize:10,current:1,total:0,support:[],supportText:"服务支持咨询",dataType:"",promptVisible:!1,promptConfirmLoading:!1,promptMessages:[]}},computed:{emptyDescription:function(){var t={1:"您没有待处理的疑点信息",2:"您没有已处理的疑点信息",3:"您没有已处理和待处理的疑点信息"};return t[this.tagValue]}},watch:{pageSize:function(t){},current:function(t){this.current=t}},mounted:function(){var t=this;this.getTreeData(),this.$nextTick((0,l.Z)(regeneratorRuntime.mark((function e(){var a,i,n,r;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.tagValue="1",a=t.$route.query,!a||!a.aaz263){e.next=18;break}if(t.aaz263=a.aaz263,t.aaz307=a.aaz307,t.systemName=a.systemName,t.cainfo=a.cainfo,!a.dataType){e.next=15;break}if(t.dataType=a.dataType,"2"!==t.dataType&&("1"!==t.dataType||"2"!==t.systemName)){e.next=13;break}if(t.aaz307){e.next=13;break}return t.$message.error("科室编码为空，无法查询疑点"),e.abrupt("return");case 13:e.next=18;break;case 15:if("2"!==t.systemName||t.aaz307){e.next=18;break}return t.$message.error("科室编码为空，无法查询疑点"),e.abrupt("return");case 18:if(!a||"2"!==a.msgmodule){e.next=26;break}t.aaz263=a.aaz263,t.aaz307=a.aaz307,t.systemName=a.systemName,t.cainfo=a.cainfo,a.dataType&&(t.dataType=a.dataType),e.next=38;break;case 26:return e.next=28,g.Z.permissionCheck(t.aaz263);case 28:return t.permissions=e.sent,i=t.aaz307,t.permissions.aaz307Disable&&(i=t.permissions.aaz307Set.values().next().value),e.next=33,Base.submit(null,{url:"/miimCommonRead/queryGroupDic",data:{aaz307:i},autoValid:!0});case 33:n=e.sent,t.submitCheckType=n.data.submitCheckType,t.aaz309List=n.data.resultData,t.permissions.aaz309Set.size>0&&(t.aaz309List=t.aaz309List.filter((function(e){return t.permissions.aaz309Set.has(e.value)}))),t.aaz309List&&t.aaz309List.length>0&&(t.aaz309=t.aaz309List[0].value);case 38:r=navigator.userAgent,r.indexOf("DeepBlue")>-1?(t.isDeepBlue=!0,QClient.DBPNSGetClientId((function(e){t.clientId=e.data,t.querysupport(),t.queryTableData()}))):t.queryTableData();case 40:case"end":return e.stop()}}),e)})))),this.__fixTableLayoutWhenSmallScreen(),window.addEventListener("resize",this.__fixTableLayoutWhenSmallScreen)},unmounted:function(){window.removeEventListener("resize",this.__fixTableLayoutWhenSmallScreen)},methods:{searchEvent:TaUtils.debounce((function(){this.queryTableData()}),1e3,{leading:!1,trailing:!0}),treeSelectChange:function(t){var e=this;this.treeSelectValue=t;var a={aaz307:this.aaz307,ruleType:t,aaz263:"self"===this.dataRange?this.aaz263:null,aaz217:this.selectRow.aaz217,aaz309:"group"===this.dataRange?this.aaz309:null,clientId:this.clientId,systemName:this.systemName};s.Z.getRowDetails(a,(function(t){var a=t.data.data.kf10List.map((function(t){e.formatOperate(t);var a=[];t.children.forEach((function(t){delete t.children,e.formatOperate(t),a.push.apply(a,(0,r.Z)(t.options))}));var i=a.reduce((function(t,e){return t.has(e.value)||t.set(e.value,e),t}),new Map);return t.options=Array.from(i.values()),t}));e.$refs.dTable.reloadData(a)}))},getTreeData:function(){var t=this;Base.submit(null,{url:"mttRuleCustom/getRuleTreeData"}).then((function(e){t.treeData=e.data.list,t.treeData.length>0&&(t.treeData[0].disabled=!0,t.treeData[0].children.forEach((function(t){t.disabled=!0}))),e.data.treeSelectShow&&(t.treeSelectShow="y"===e.data.treeSelectShow.toLowerCase())}))},ruleSuspiciousLogo:function(t){var e="",a="";return"甲"===t.aka065?e='<div style="float: left;background: #67C23A;color: #FFFFFF;width: 14px;"><a href="#" title="甲类项目" class="easyui-tooltip tooltipContent">甲</a></div>':"乙"===t.aka065?e='<div style="float: left;background: #E6A23C;color: #FFFFFF;width: 14px;"><a href="#" title="乙类项目" class="easyui-tooltip tooltipContent">乙</a></div>':"丙"===t.aka065&&(e='<div style="float: left;background: #FF0000;color: #FFFFFF;width: 14px;"><a href="#" title="丙类项目" class="easyui-tooltip tooltipContent">丙</a></div>'),"2"===t.ape800?a='<div style="float: right;background: #FF0000;color: #FFFFFF;width: 14px;"><a href="#" title="违规项目" class="easyui-tooltip tooltipContent">1</a></div>':"1"===t.ape800?a='<div style="float: right;background: #E6A23C;color: #FFFFFF;width: 14px;"><a href="#" title="可疑项目" class="easyui-tooltip tooltipContent">2</a></div>':"3"===t.ape800&&(a='<div style="float: right;background: deepskyblue;color: #FFFFFF;width: 14px;"><a href="#" title="仅提醒" class="easyui-tooltip tooltipContent">3</a></div>'),e+a},__fixTableLayoutWhenSmallScreen:function(){var t=this;setTimeout((function(){t.widthConfig=b[parseInt(window.innerWidth)>1024?"normal":"mini"],t.$nextTick((function(){t.__fixOperationLayout()}))}),300)},__fixOperationLayout:function(){var t=Array.from(document.getElementsByClassName("custom-anchor"));t.forEach((function(t){var e=t.parent||t.parentNode||t.parentElement;if(e){var a=e.parent||e.parentNode||e.parentElement;e.style.width=parseInt(a.clientWidth)+6+"px"}}))},isSmallScreen:function(){var t=document.getElementsByClassName("drawer-toggler");if(t){var e=t[0],a=getComputedStyle(e);return"block"===a.display}return!1},toggleMoreMessage:function(){this.moreMessage=!this.moreMessage,this.$refs.xTable.scrollTo(0)},toggleDrawer:function(){this.drawerVisible=!this.drawerVisible},cellClickEvent:function(t){var e=t.column,a=t.row;this.expandedRow=a,e.property||"oparete"==e.property||this.$refs.dTable.toggleRowExpand(a)},headerCellStyle:function(t){t.column,t.columnIndex;return{fontSize:"12px",backgroundColor:"#ffffff"}},toggleExpandChangeEvent:function(t){var e=t.row,a=t.expanded;a&&(this.expandedRow=e),this.$refs.dTable.toggleRowExpand(e)},moment:c(),changeTag:function(t){this.tagValue=t.value,this.queryTableData()},convertYkz061:function(t){var e=t.match(/^<(.*)>(.*)<\/\1>$/);return"".concat(e[1],":").concat(e[2])},aaz309Change:function(t){this.aaz309=t,this.queryTableData()},queryTableData:function(){var t=this;this.patientDetails={},this.patientObjects=[],this.patientBillObjects=[],this.patientDiagnose=[],this.buttonVisiable=!1;var e={aaz307:this.aaz307,aaz263:this.aaz263,aaz309:"group"===this.dataRange?this.aaz309:null,clientId:this.clientId,systemName:this.systemName,filterParam:this.filterParam,cainfo:this.cainfo,dataType:this.dataType};e.aze003="1"===this.tagValue?"0":"2"===this.tagValue?"1":"",s.Z.queryTableData(e,(function(e){t.dataSource=e.data.data}))},formatOperate:function(t){t.options=[];var e=t.bxczid.split(";"),a=e[0].substring(1,e[0].length-1).split(","),i=e[1].substring(1,e[1].length-1).split(",");if(a.length>0&&i.length>0&&-1===a.indexOf("")&&-1===i.indexOf("")){for(var n=new Map,r=0;r<a.length;r++)n.set(i[r],a[r]);for(var l=0;l<a.length;l++)t.bxczid.indexOf(i[l])>-1&&("0"!==i[l]&&"4"!==i[l]||"4"===i[l]&&t.ykz042)&&t.options.push({value:i[l],label:a[l]})}t.operate="0"===t.ape893?[]:t.ape893.split(","),t.aaz560="0"===t.aaz560?"":t.aaz560},getPatientInfo:function(t){var e=this,a=t.row;this.treeSelectValue=null,this.patientDetails={},this.patientObjects=[],this.patientBillObjects=[],this.patientDiagnose=[],this.buttonVisiable=!1,this.selectRow=a,this.aaz217=a.aaz217;var i={aaz307:this.aaz307,aaz263:"self"===this.dataRange?this.aaz263:null,aaz217:a.aaz217,aaz309:"group"===this.dataRange?this.aaz309:null,clientId:this.clientId,systemName:this.systemName,dataType:this.dataType};s.Z.getRowDetails(i,(function(t){t.data.customTipsText&&(e.customTipsText=t.data.customTipsText),e.whetherDiagStats=t.data.data.whetherDiagStats,void 0===e.whetherDiagStats&&(e.whetherDiagStats="1"),e.patientDiagName="",e.patientDetails=t.data.data.personInfo,e.patientObjects=t.data.data.kf10List.map((function(t){e.formatOperate(t);var a=[];t.children.forEach((function(t){delete t.children,e.formatOperate(t),a.push.apply(a,(0,r.Z)(t.options))}));var i=a.reduce((function(t,e){return t.has(e.value)||t.set(e.value,e),t}),new Map);return t.options=Array.from(i.values()),t}));var a=32*(e.patientObjects.length+6);if(e.height=a,e.patientBillObjects=t.data.data.kf59List,e.patientDiagnose=t.data.data.kc21k1Pos,e.patientDiagnose)for(var i=0;i<e.patientDiagnose.length;i++)"1"==e.patientDiagnose[i].aka016?e.patientDiagName+='<span style="color:#FF9901">'+e.patientDiagnose[i].aka121+"【"+e.patientDiagnose[i].aka120+"】; </span>":e.patientDiagName+="<span>"+e.patientDiagnose[i].aka121+"【"+e.patientDiagnose[i].aka120+"】; </span>";e.buttonVisiable=!0,e.isSmallScreen()&&(e.moreMessage=!1)}))},checkClick:function(t,e){!1===t.target.checked&&t.target.checked,"1"!=t.target.value&&"4"!=t.target.value&&(e.aaz560="",this.$refs.keeponrecord.fnReset()),"4"==t.target.value&&e.operate.includes("1")&&(e.aaz560="",this.$refs.keeponrecord.fnReset()),"1"==t.target.value&&e.operate.includes("4")&&(e.aaz560="",this.$refs.keeponrecord.fnReset()),e.operate=[t.target.value],"1"===t.target.value?(e.aaz263=this.aaz263,this.rowData=e,this.keepOnRecordVisible=!0):"4"===t.target.value&&(this.rowData=e,this.ykz010Arr=e.ykz010.split(","),this.ykz042Arr=e.ykz042.split(","),this.keepOnRecordVisible2=!0),e.children.forEach((function(t){"[];[]"===t.bxczid?(t.operate=["0"],t.aaz560=""):t.options.map((function(t){return t.value})).includes(e.operate[0])?(t.operate=e.operate,t.aaz560=e.aaz560):(t.operate=[],t.aaz560="")})),this.$refs.dTable.updateStatus(e),this.clickFlag="father"},checkClick2:function(t,e){!1===t.target.checked&&t.target.checked,"1"!=t.target.value&&"4"!=t.target.value&&(e.aaz560="",this.childrenChange(),this.$refs.keeponrecord.fnReset()),"4"==t.target.value&&e.operate.includes("1")&&(e.aaz560="",this.childrenChange(),this.$refs.keeponrecord.fnReset()),"1"==t.target.value&&e.operate.includes("4")&&(e.aaz560="",this.childrenChange(),this.$refs.keeponrecord.fnReset()),e.operate=[t.target.value],"1"===t.target.value?(e.aaz263=this.aaz263,this.rowData=e,this.keepOnRecordVisible=!0):"4"===t.target.value&&(this.rowData=e,this.ykz010Arr=e.ykz010.split(","),this.ykz042Arr=e.ykz042.split(","),this.keepOnRecordVisible2=!0),this.$refs.dTablechildren.updateStatus(e),this.fatherChange(),this.clickFlag="child"},childrenChange:function(){var t=new Set;this.expandedRow.children.forEach((function(e){t.add(e.aaz560)}));var e=Array.from(t);this.expandedRow.aaz560=e.join(";"),this.$refs.dTable.updateStatus(this.expandedRow)},fatherChange:function(){var t=new Set;this.expandedRow.children.forEach((function(e){e.operate.forEach((function(e){t.add(e)}))})),this.expandedRow.operate=(0,r.Z)(t),this.$refs.dTable.updateStatus(this.expandedRow)},fatherChangeClose:function(){var t=this,e=!1,a=new Set;"father"==this.clickFlag&&(this.expandedRow.operate=[],this.expandedRow.aaz560=""),this.expandedRow.children.forEach((function(i){i.operate.length>0&&(e=!0),"father"==t.clickFlag?(i.operate=[],i.aaz560=""):i.operate.forEach((function(t){a.add(t)}))})),e||(this.expandedRow.aaz560=""),this.expandedRow.operate=(0,r.Z)(a),this.$refs.dTable.updateStatus(this.expandedRow)},handleUpdateRow:function(t){var e=this;this.keepOnRecordVisible=!1,this.rowData.aaz560=t.content,this.rowData.children&&this.rowData.children.forEach((function(t){return t.aaz560=e.rowData.aaz560})),this.$refs.dTable.updateStatus(this.rowData),this.rowData.children||this.childrenChange()},handleUpdateRow2:function(t){var e=this;this.keepOnRecordVisible2=!1,this.rowData.aaz560=t.content,this.rowData.children&&this.rowData.children.forEach((function(a){return a.aaz560=e.rowData.aaz560,a.aka121=t.aka121,a.aka120=t.aka120})),this.$refs.dTable.updateStatus(this.rowData),this.rowData.children||this.childrenChange()},handleClose:function(){this.keepOnRecordVisible=!1,this.rowData.operate=[],this.rowData.aaz560="",this.fatherChangeClose()},handleClose2:function(){this.keepOnRecordVisible2=!1,this.rowData.operate=[],this.rowData.aaz560="",this.$refs.keeponrecord.fnReset(),this.fatherChangeClose()},handleSaveCheck:function(){var t=this,e=this.$refs.dTable.getTableData().fullData,a=new Set,i=new Set;e.forEach((function(t){t.children.forEach((function(t){t.operate.length&&(a.add(t),t.ape893=t.operate[0],i.add(t.aaz213))}))}));var n=(0,r.Z)(i),l=(0,r.Z)(a),o={aaz263:this.aaz263,aaz217:this.aaz217,clientId:this.clientId,aaz213s:n,kf10OprPos:l};s.Z.checkHandleState(o,(function(e){"0"===e.data.data.code?(t.promptVisible=!0,t.promptMessages=e.data.data.messages):t.handleSave()}))},handleCancel:function(t){var e=this;this.promptVisible=!1;var a={aaz307:this.aaz307,aaz263:"self"===this.dataRange?this.aaz263:null,aaz217:this.selectRow.aaz217,aaz309:"group"===this.dataRange?this.aaz309:null,clientId:this.clientId,systemName:this.systemName};s.Z.getRowDetails(a,(function(t){var a=t.data.data.kf10List.map((function(t){e.formatOperate(t);var a=[];t.children.forEach((function(t){delete t.children,e.formatOperate(t),a.push.apply(a,(0,r.Z)(t.options))}));var i=a.reduce((function(t,e){return t.has(e.value)||t.set(e.value,e),t}),new Map);return t.options=Array.from(i.values()),t}));e.$refs.dTable.reloadData(a)}))},handleSave:function(){var t=this,e=this.$refs.dTable.getTableData().fullData,a=new Set;e.forEach((function(t){t.children.forEach((function(t){a.add(t)}))}));for(var i=(0,r.Z)(a),n=0;n<i.length;n++)if(0===i[n].operate.length&&"[];[]"===i[n].bxczid&&(i[n].bxczid="[无操作];[0]",i[n].operate=["0"]),0===i[n].operate.length&&"[无操作];[0]"!==i[n].bxczid&&"y"===this.submitCheckType.toLowerCase())return void this.$message.warn("还有未处理的项目，请将所有项目处理完成后再提交！");this.$nextTick((function(){i=i.map((function(t){return t.ape893=t.operate[0],t}));var e={aaz217:t.aaz217,aae500:"2",kf10OprPoList:i,aaz263:t.aaz263};s.Z.updatePatientInfo(e,(function(e){t.$message.success("保存成功"),t.promptVisible=!1,t.queryTableData(),t.isSmallScreen()&&(t.moreMessage=!0)}))}))},cellStyle:function(t){var e=t.row,a=(t.rowIndex,t.column);t.columnIndex;if("aze003"===a.property){if(e.aze003.indexOf("已处理")>-1)return{color:"#0F990F"};if(e.aze003.indexOf("待处理")>-1)return{color:"#E4393C"}}},rowStyle:function(t){var e=t.row;t.rowIndex;return this.selectRow.aaz217===e.aaz217?{backgroundColor:"rgb(235,244,250)"}:{backgroundColor:"#ffffff"}},queryRuleInfo:function(t){var e=this,a=t.column,i=t.row;if(a.property)if(i.ykz032){var n=i.ykz032,r=[];void 0!==n&&(r=n.split(",")),s.Z.queryRuleInfo({ykz032s:r},(function(t){if(!1!==t.serviceSuccess){var a=t.data.resultData.ykz018,i="";if(void 0!==a){for(var n=0;n<a.length;n++)i+="【"+a[n]+"】";e.ykz018=i,e.nodeList=t.data.resultData.nodeInfoList}}else e.$message.error(t.errors[0].msg)})),this.visible=!0}else this.$message.warn("该审查项无规则内涵")},changeNodeActivekey:function(t){var e=this;if(!(0,f.Z)(t)){var a=t;if(!(0,f.Z)(a)){var i={};i.ykz042=this.nodeList[a].ykz042,i.pageParam={},i.pageParam.pageNumber=0,i.pageParam.pageSize=10,this.ykz042=i.ykz042,s.Z.queryPagePackageRule(i,(function(t){if(!1!==t.serviceSuccess){e.columns=t.data.resultData.columnInfo;var a=t.data.resultData.pageData;e.nodeData=a.list,e.current=a.pageNum,e.pagesize=a.pageSize,e.total=a.total}else e.$message.error(t.errors[0].msg)}))}}},searchfn:function(t,e){this.searchText=t,this.changePage(1,this.pagesize)},onShowSizeChange:function(t,e){this.changePage(t,e)},changePage:function(t,e){var a=this,i={};i.ykz042=this.ykz042,i.searchText=this.searchText,i.pageParam={},i.pageParam.pageNumber=t,i.pageParam.pageSize=e,s.Z.queryPagePackageRule(i,(function(t){if(!1!==t.serviceSuccess){a.columns=t.data.resultData.columnInfo;var e=t.data.resultData.pageData;a.nodeData=e.list,a.current=e.pageNum,a.pagesize=e.pageSize,a.total=e.total}else a.$message.error(t.errors[0].msg)}))},querysupport:function(){var t=this;this.Base.submit(null,{url:"assistantWindow/querySupport",data:{},autoValid:!1},{successCallback:function(e){var a=e.data;if(a.namelenth>0&&a.numlenth>0){t.supportText="服务支持咨询:";var i=1;i=a.namelenth>a.numlenth?a.namelenth:a.numlenth;for(var n=0;n<i;n++)a["supportname"+(0==n?"":n+1)]&&a["supportnum"+(0==n?"":n+1)]&&t.support.push({supportname:a["supportname"+(0==n?"":n+1)]+" ",supportnum:a["supportnum"+(0==n?"":n+1)]})}else t.supportText=""},failCallback:function(e){t.$message.error("查询失败")}})}}},w=v,k=a(1001),y=(0,k.Z)(w,i,n,!1,null,"d7f55cba",null),z=y.exports},83231:function(t,e,a){var i=a(48534);a(36133);function n(t){return t?t.akb020&&t.aaz263?"personal":t.akb020&&t.aaz309&&t.aaz307?"group":t.akb020&&t.aaz307?"department":t.akb020?"hospital":"all":"all"}function r(t){return l.apply(this,arguments)}function l(){return l=(0,i.Z)(regeneratorRuntime.mark((function t(e){var a,i,r,l,s,o,c,u,p,d,h,f;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Base.submit(null,{url:"/miimCommonRead/queryDataPermission",showPageLoading:!1,data:{jobnumber:e},autoValid:!0});case 2:return a=t.sent,i=new Set,r=new Set,a.data.permission.forEach((function(t){var e=n(t);"hospital"===e&&i.add(t.akb020),"department"===e&&r.add(t.aaz307)})),l=a.data.permission.filter((function(t){return"department"===n(t)||!r.has(t.aaz307)})).filter((function(t){return"hospital"===n(t)||!i.has(t.akb020)})),s=new Set(l.map((function(t){return t.akb020})).filter((function(t){return null!=t}))),o=new Set(l.map((function(t){return t.aaz307})).filter((function(t){return null!=t}))),c=new Set(l.map((function(t){return t.aaz309})).filter((function(t){return null!=t}))),u=new Set(l.map((function(t){return t.aaz263})).filter((function(t){return null!=t}))),p=!1,d=!1,h=!1,f=!1,1===s.size&&(p=!0),1===o.size&&1===s.size&&(d=!0),1===o.size&&1===s.size&&1===c.size&&(h=!0),1===s.size&&0===o.size&&1===u.size&&(f=!0),t.abrupt("return",{akb020Set:s,aaz307Set:o,aaz263Set:u,aaz309Set:c,akb020Disable:p,aaz307Disable:d,aaz263Disable:f,aaz309Disable:h});case 20:case"end":return t.stop()}}),t)}))),l.apply(this,arguments)}function s(t,e){Base.submit(null,{url:"miimCommonRead/getAa01AAE500StartStop",data:t},{successCallback:function(t){return e(t)}})}function o(t,e){Base.submit(null,{url:"miimCommonRead/insertTableColumShow",data:t,autoValid:!1},{successCallback:function(t){return e("success")},failCallback:function(t){return e("fail")}})}var c={zbx01:{footer:function(){return"统筹金总额"}},zbx02:{footer:function(){return"基金支出总额"}},zbx03:{footer:function(){return"总费用"}},zbx04:{footer:function(){return"总住院日"}}};e["Z"]={permissionCheck:r,getAa01AAE500StartStop:s,insertTableColumShow:o,props:c,moneyNumFormat:function(t){var e=t.cellValue;return e||"—"}}}}]);
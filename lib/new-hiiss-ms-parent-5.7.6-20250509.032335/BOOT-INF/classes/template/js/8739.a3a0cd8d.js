"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[8739],{88412:function(e,t,a){var l=a(26263),o=a(36766),i=a(1001),r=(0,i.Z)(o.Z,l.s,l.x,!1,null,"5e7ef0ae",null);t["Z"]=r.exports},38739:function(e,t,a){a.r(t),a.d(t,{default:function(){return k}});var l=function(){var e=this,t=this,a=t.$createElement,l=t._self._c||a;return l("div",{staticClass:"fit"},[l("ta-border-layout",{attrs:{"layout-type":"fixTop"}},[l("div",{attrs:{slot:"header"},slot:"header"},[l("ta-title",{attrs:{title:"患者信息"}}),l("ta-form",{attrs:{autoFormCreate:function(t){e.baseInfoForm=t},col:t.col,layout:"horizontal","label-width":"150px",formLayout:!0}},[l("ta-form-item",{attrs:{fieldDecoratorId:"aac003"}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[t._v("患者姓名")]),l("span",[t._v(t._s(this.personInfo.aac003))])]),l("ta-form-item",{attrs:{fieldDecoratorId:"akc191"}},["0"===this.flag||"1"===this.flag||"11"===this.flag?l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[t._v("就诊号")]):l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[t._v(t._s(this.akc191Title))]),l("span",[t._v(t._s(this.personInfo.akc191))])]),l("ta-form-item",{attrs:{fieldDecoratorId:"aac004"}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[t._v("性别")]),l("span",[t._v(t._s(t.CollectionLabel("SEX",this.personInfo.aac004)))])]),l("ta-form-item",{attrs:{fieldDecoratorId:"age"}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[t._v("年龄")]),l("span",[t._v(t._s(this.personInfo.age))])]),l("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:void 0!=this.personInfo.aae030,expression:"this.personInfo.aae030 != undefined"}],attrs:{fieldDecoratorId:"aae030"}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[t._v("入院时间")]),l("span",[t._v(t._s(this.personInfo.aae030))])]),l("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:void 0!=this.personInfo.aae031,expression:"this.personInfo.aae031 != undefined"}],attrs:{fieldDecoratorId:"aae031"}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[t._v("出院时间")]),l("span",[t._v(t._s(this.personInfo.aae031))])]),l("ta-form-item",{attrs:{fieldDecoratorId:"aae141"}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[t._v("医保类型")]),l("span",[t._v(t._s(t.CollectionLabel("AAE141",this.personInfo.aae141)))])]),l("ta-form-item",{attrs:{fieldDecoratorId:"aae140"}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[t._v("险种类型")]),l("span",[t._v(t._s(t.CollectionLabel("AAE140",this.personInfo.aae140)))])]),l("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:!["11","0","1"].includes(this.flag),expression:"!['11','0','1'].includes(this.flag)"}],attrs:{fieldDecoratorId:"aae140"}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[t._v("住院次数")]),l("span",[t._v(t._s(this.personInfo.akc200)+"次")])]),l("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:t.showEmrButton,expression:"showEmrButton"}]},[this.showEmrButton?l("a",{on:{click:t.openEmrPage}},[t._v("查看电子病例")]):t._e()])],1)],1),l("div",{staticClass:"content-box"},[l("ta-title",{attrs:{title:"审核明细"}}),l("ta-tabs",{staticClass:"fit content-tabs",attrs:{type:"card",tabBarGutter:10},on:{change:t.fnTabChange},model:{value:t.activeKey,callback:function(e){t.activeKey=e},expression:"activeKey"}},["3"!==this.flag?l("ta-tab-pane",{key:"Kc23",staticClass:"tab-pane-box",attrs:{tab:"医嘱信息",forceRender:!1}},[l("div",{staticClass:"tableHeight"},[l("ta-big-table",{key:(new Date).getTime()+"kc23",attrs:{size:"small",bordered:!0,columns:t.columns_kc23,scroll:{y:"100%"},border:"",height:"100%","auto-resize":"",resizable:"","highlight-hover-row":"","highlight-current-row":"","show-overflow":"",data:t.gridData}})],1)]):t._e(),"0"!==this.flag&&"1"!==this.flag?l("ta-tab-pane",{key:"fyxx",staticClass:"tab-pane-box",attrs:{tab:"费用信息"}},[l("ta-form",{attrs:{autoFormCreate:function(t){return e.fyxxForm=t},layout:"horizontal",formLayout:!0}},[l("ta-form-item",{staticStyle:{"margin-top":"10px"},attrs:{fieldDecoratorId:"allDate",span:4,wrapperCol:{span:16}}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[t._v("项目时间")]),l("ta-range-picker",{attrs:{"allow-one":!0}},[l("ta-icon",{attrs:{slot:"suffixIcon",type:""},slot:"suffixIcon"})],1)],1),l("ta-form-item",{staticStyle:{"margin-top":"10px"},attrs:{fieldDecoratorId:"projectInfo",span:4,wrapperCol:{span:15}}},[l("span",{attrs:{slot:"label"},slot:"label"},[t._v("三目信息")]),l("ta-input",{attrs:{placeholder:"请输入三目名称或编码"}})],1),l("ta-form-item",{staticStyle:{"margin-top":"10px"},attrs:{fieldDecoratorId:"inProjectInfo",span:4,wrapperCol:{span:15}}},[l("span",{attrs:{slot:"label"},slot:"label"},[t._v("院内项目")]),l("ta-input",{attrs:{placeholder:"请输入院内项目名称"}})],1),l("ta-form-item",{staticStyle:{"margin-top":"10px"},attrs:{span:2,"label-width":"80px"}},[l("span",{attrs:{slot:"label"},slot:"label"},[t._v("数量过滤")]),l("ta-switch",{attrs:{title:"开启后过滤数量为0的数据",checkedChildren:"开",unCheckedChildren:"关",defaultChecked:""},on:{change:t.onChangeSwitch}})],1),l("ta-form-item",{staticStyle:{"margin-top":"10px"},attrs:{span:4,"label-width":"90px"}},[l("span",{attrs:{slot:"label"},slot:"label"},[t._v("仅展示疑点")]),l("ta-switch",{attrs:{title:"开启后仅展示疑点数据",checkedChildren:"开",unCheckedChildren:"关"},on:{change:t.onChangeSwitch2},model:{value:t.onlyDoubts,callback:function(e){t.onlyDoubts=e},expression:"onlyDoubts"}}),l("ta-button",{staticStyle:{"margin-left":"30px"},attrs:{type:"primary",icon:"search"},on:{click:function(e){return t.fnTabChange("fyxx")}}},[t._v("查询")])],1)],1),l("div",{staticClass:"tab-table-box"},[l("ta-big-table",{ref:"infoTableRef",attrs:{"show-footer":"","footer-method":t.footerMethod,border:"",height:"100%","auto-resize":"",resizable:"","highlight-hover-row":"","highlight-current-row":"","show-overflow":"",columns:t.columns_fyxx,data:t.gridData,scroll:{y:"100%"}}})],1)],1):t._e(),l("ta-tab-pane",{key:"Kc21k1",staticClass:"tab-pane-box",attrs:{tab:"诊断信息",forceRender:!1}},[l("div",{staticClass:"tableHeight"},[l("ta-big-table",{key:(new Date).getTime()+"kc21k1",attrs:{size:"small",bordered:!0,columns:t.columns_kc21k1,scroll:{y:"100%"},border:"",height:"100%","auto-resize":"",resizable:"","highlight-hover-row":"","highlight-current-row":"","show-overflow":"",data:t.gridData}})],1)]),t.Kc37flag?l("ta-tab-pane",{key:"Kc37",staticClass:"tab-pane-box",attrs:{tab:"检验指标",forceRender:!1}},[l("div",{staticClass:"tableHeight"},[l("ta-big-table",{key:(new Date).getTime()+"kc37",attrs:{size:"small",bordered:!0,columns:t.columns_kc37,scroll:{y:"100%"},border:"",height:"100%","auto-resize":"",resizable:"","highlight-hover-row":"","highlight-current-row":"","show-overflow":"",data:t.gridData}})],1)]):t._e(),t.Kc34flag?l("ta-tab-pane",{key:"Kc34",staticClass:"tab-pane-box",attrs:{tab:"营养风险",forceRender:!1}},[l("div",{staticClass:"tableHeight"},[l("ta-big-table",{key:(new Date).getTime()+"kc34",attrs:{size:"small",bordered:!0,columns:t.columns_kc34,scroll:{y:"100%"},border:"",height:"100%","auto-resize":"",resizable:"","highlight-hover-row":"","highlight-current-row":"","show-overflow":"",data:t.gridData}})],1)]):t._e(),t.Kc39flag?l("ta-tab-pane",{key:"Kc39",staticClass:"tab-pane-box",attrs:{tab:"药敏记录",forceRender:!1}},[l("div",{staticClass:"tableHeight"},[l("ta-big-table",{key:(new Date).getTime()+"kc39",attrs:{size:"small",bordered:!0,columns:t.columns_kc39,scroll:{y:"100%"},border:"",height:"100%","auto-resize":"",resizable:"","highlight-hover-row":"","highlight-current-row":"","show-overflow":"",data:t.gridData}})],1)]):t._e(),t.Kc41flag?l("ta-tab-pane",{key:"Kc41",staticClass:"tab-pane-box",attrs:{tab:"手术记录",forceRender:!1}},[l("div",{staticClass:"tableHeight"},[l("ta-big-table",{key:(new Date).getTime()+"kc41",attrs:{size:"small",bordered:!0,columns:t.columns_kc41,scroll:{y:"100%"},border:"",height:"100%","auto-resize":"",resizable:"","highlight-hover-row":"","highlight-current-row":"","show-overflow":"",data:t.gridData}})],1)]):t._e()],1),l("div",{staticClass:"content-box-footer"},[l("ta-pagination",{ref:"infoPageRef",staticStyle:{position:"absolute",right:"6px",bottom:"6px"},attrs:{defaultPageSize:100,pageSizeOptions:["100","200","500","1000","1500"],dataSource:t.gridData,params:t.pageParam,url:t.url},on:{"update:dataSource":function(e){t.gridData=e},"update:data-source":function(e){t.gridData=e}}})],1)],1),l("emr-detail",{attrs:{visible:t.showEmrModal,config:t.emrConfig,patientInfo:t.personInfo},on:{handleClose:t.closeEmrPage}})],1)],1)},o=[],i=a(88412),r=(a(36797),function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("ta-modal",{attrs:{title:"病案查看",visible:e.visible,height:700,width:1400,bodyStyle:{padding:0},closable:"",footer:null},on:{cancel:e.handleClose}},[a("ta-border-layout",{attrs:{layout:{left:"300px"}}},[a("div",{attrs:{slot:"left"},slot:"left"},[e.config.showTypeSelectBtn?a("ta-select",{attrs:{defaultValue:e.typeDefault,"show-search":"",placeholder:"病例类型","collection-type":"EMR_TYPE"},on:{change:e.typeSelect}}):e._e(),a("div",[e._v("目录: "),a("ta-e-tree",{attrs:{data:e.treeData,"icon-open":"folder-open",props:e.props,"icon-close":"folder"},on:{"node-click":e.nodeClick}})],1)],1),a("div",{staticStyle:{display:"flex",height:"100%"}},[a("div",{staticStyle:{width:"100%","overflow-y":"scroll"}},["img"===e.currenType&&e.imageSrc?a("img",{staticStyle:{width:"100%","object-fit":"contain"},attrs:{src:e.imageSrc},on:{click:e.showImageByViewer}}):e._e(),"url"===e.currenType?a("iframe",{staticStyle:{width:"100%",height:"100%"},attrs:{id:e.iframe.id,src:e.iframe.currentURL}}):e._e()]),a("ta-image-viewer",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}],ref:"myViewer",attrs:{images:e.imagesList}})],1)])],1)}),n=[],s=a(95278),c="emr/",d={getBasePath:function(){return s.Z.basePath},getPageUrl:function(){return c+"queryListByPage"},queryEmrPageList:function(e,t){Base.submit(null,{url:c+"queryEmrPageList",data:e,autoQs:!1},{successCallback:function(e){return t(e)}})},queryEmrPageDetail:function(e,t){Base.submit(null,{url:c+"queryEmrPageDetail",data:e,autoQs:!1},{successCallback:function(e){return t(e)}})}},u=a(34116),f=(a(33240),a(55115));f.w3.use(u.Z);var h={name:"emrDetail",props:{visible:{type:Boolean},patientInfo:{type:Object},config:{type:Object},showSave:{type:Boolean}},computed:{typeDefault:function(){if("1"===this.patientInfo.aae500)return"45"}},data:function(){return{treeData:[],props:{label:"title",id:"id",children:"child"},currenType:"url",imagesList:[],imageSrc:null,base64Image:null,currentFileName:"",iframe:{id:"1",currentURL:""}}},watch:{visible:{handler:function(e){var t=this;e&&(this.base64Image=null,this.currentFileName="",this.imageSrc=null,this.config.showTypeSelectBtn&&!this.typeDefault||this.$nextTick((function(){d.queryEmrPageList({aae500:t.patientInfo.aae500,aaz217:t.patientInfo.aaz217Query},(function(e){t.treeData=e.data.data.emrPages}))})))}}},methods:{handleClose:function(){this.$emit("handleClose")},saveImgAsAttach:function(){this.$emit("saveImage",this.base64Image,this.currentFileName+".png")},showImageByViewer:function(){this.$refs.myViewer.view(0)},typeSelect:function(e){var t=this;this.$nextTick((function(){d.queryEmrPageList({aae500:t.patientInfo.aae500,aaz217:t.patientInfo.aaz217Query,emrType:e},(function(e){t.treeData=e.data.data.emrPages}))}))},nodeClick:function(e,t){var a=this;if(e.emrId){this.currenType=e.emrType;var l={aae500:this.patientInfo.aae500,aaz217:this.patientInfo.aaz217Query,emrId:e.emrId,extension:e.extension};d.queryEmrPageDetail(l,(function(l){"img"===e.emrType&&(a.base64Image=l.data.detailInfo,a.imageSrc="data:image/png;base64,"+a.base64Image,a.currentFileName=t.parent.data.text+"-"+e.text,a.imagesList.pop(),a.imagesList.push({url:a.imageSrc})),"url"===e.emrType&&(a.iframe.currentURL=d.getBasePath()+l.data.detailInfo,a.iframe.id=e.emrId)}))}}}},m=h,g=a(1001),p=(0,g.Z)(m,r,n,!1,null,"23848123",null),C=p.exports,b=a(83231),v={name:"expenseDetails",components:{EmrDetail:C,TaTitle:i.Z},data:function(){var e,t=[{title:"三目编码",field:"ake001",sortable:!0,overflowTooltip:!0,width:"16%",align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"三目名称",field:"ake002",sortable:!0,overflowTooltip:!0,width:"16%",align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"院内项目名称",field:"ake006",overflowTooltip:!0,width:"16%",align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"项目时间",field:"aae036",overflowTooltip:!0,sortable:!0,width:"16%",align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"使用数量",field:"akc226",overflowTooltip:!0,width:"8%",align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"开单科室",field:"aae386",overflowTooltip:!0,width:"14%",align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"开单医生",field:"aac003",overflowTooltip:!0,width:"14%",align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"执行科室",field:"sdksmc",overflowTooltip:!0,width:"14%",align:"center",customHeaderCell:this.fnCustomHeaderCell}];e="0"!=this.$route.query.flag&&"1"!=this.$route.query.flag;var a=[{title:"医嘱项目编码",dataIndex:"ake001",field:"ake001",overflowTooltip:!0,width:"auto",align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"医嘱项目名称",dataIndex:"ake002",field:"ake002",overflowTooltip:!0,align:"center",width:"auto",customHeaderCell:this.fnCustomHeaderCell},{title:"医嘱名称",dataIndex:"ake007",field:"ake007",overflowTooltip:!0,width:"auto",align:"center",visible:e,customHeaderCell:this.fnCustomHeaderCell},{title:"医嘱开具时间",dataIndex:"aae036",field:"aae036",overflowTooltip:!0,align:"center",width:"auto",customHeaderCell:this.fnCustomHeaderCell},{title:"开单科室编码",dataIndex:"aaz307",field:"aaz307",overflowTooltip:!0,align:"center",width:"auto",customHeaderCell:this.fnCustomHeaderCell},{title:"开单科室名称",dataIndex:"aae386",field:"aae386",overflowTooltip:!0,align:"center",width:"auto",customHeaderCell:this.fnCustomHeaderCell},{title:"医嘱开始时间",dataIndex:"aae310",field:"aae310",overflowTooltip:!0,align:"center",width:"auto",customHeaderCell:this.fnCustomHeaderCell},{title:"医嘱结束时间",dataIndex:"aae311",field:"aae311",overflowTooltip:!0,align:"center",width:"auto",customHeaderCell:this.fnCustomHeaderCell}],l=[{title:"诊断编码",dataIndex:"aka120",field:"aka120",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"诊断名称",dataIndex:"aka121",field:"aka121",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"诊断类型",dataIndex:"aka015",field:"aka015",overflowTooltip:!0,collectionType:"AKA015",align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"是否主诊断",dataIndex:"aka016",field:"aka016",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"诊断录入时间",dataIndex:"aae036",field:"aae036",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell}],o=[{title:"体重",dataIndex:"ape150",field:"ape150",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"身高",dataIndex:"ape159",field:"ape159",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"营养风险总评分",dataIndex:"dze003",field:"dze003",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"BMI值",dataIndex:"dze005",field:"dze005",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"数据来源",dataIndex:"dze006",field:"dze006",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell}],i=[{title:"检验指标代码",dataIndex:"dzg005",field:"dzg005",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"检验指标名称",dataIndex:"dzg006",field:"dzg006",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"检验时间",dataIndex:"aae036",field:"aae036",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"检验结果",dataIndex:"dzg007",field:"dzg007",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"单位",dataIndex:"dzg008",field:"dzg008",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"参考值",dataIndex:"dzg010",field:"dzg010",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell}],r=[{title:"检查编码",dataIndex:"dzf003",field:"dzf003",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"检查名称",dataIndex:"dzf004",field:"dzf004",overflowTooltip:!0,align:"left",customHeaderCell:this.fnCustomHeaderCell},{title:"菌种编码",dataIndex:"dzg012",field:"dzg012",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"菌种名称",dataIndex:"dzg013",field:"dzg013",overflowTooltip:!0,align:"left",customHeaderCell:this.fnCustomHeaderCell},{title:"抗生素编码",dataIndex:"dzg015",field:"dzg015",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"抗生素名称",dataIndex:"dzg016",field:"dzg016",overflowTooltip:!0,align:"left",customHeaderCell:this.fnCustomHeaderCell},{title:"定性结果",dataIndex:"dzg018",field:"dzg018",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"检验时间",dataIndex:"aae036",field:"aae036",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell}],n=[{title:"手术日期",dataIndex:"dzh001",field:"dzh001",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"手术类型编码",dataIndex:"dzh004",field:"dzh004",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"手术类型名称",dataIndex:"dzh005",field:"dzh005",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"手术类别编码",dataIndex:"dzh006",field:"dzh006",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"手术类别名称",dataIndex:"dzh007",field:"dzh007",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"手术名称编码",dataIndex:"dzh011",field:"dzh011",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"手术名称",dataIndex:"dzh012",field:"dzh012",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"麻醉方式",dataIndex:"dzh013",field:"dzh013",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell}];return{url:"",col:{xs:1,sm:2,md:2,lg:4,xl:4,xxl:4},activeKey:"fyxx",columns_fyxx:t,columns_kc23:a,columns_kc21k1:l,columns_kc34:o,columns_kc37:i,columns_kc39:r,columns_kc41:n,gridData:[],kc21k1Data:[],kc23Data:[],kc34Data:[],kc37Data:[],kc39Data:[],kc41Data:[],countShowFlag:!0,onlyDoubts:!1,Kc37flag:!0,Kc34flag:!0,Kc39flag:!0,Kc41flag:!0,rangeValue:[this.Base.getMoment((new Date).toISOString().slice(0,8)+"-01","YYYY-MM-DD"),this.Base.getMoment((new Date).toISOString().slice(0,10),"YYYY-MM-DD")],count:"",money:"",akc191Title:"住院号",visible:!1,fyRecord:{},doubtList:[],auditPathList:[],nodeDetail:{},colors:["red","green","blue"],aaz217:"",akb020:"",flag:"",showEmrButton:!1,emrConfig:{},showEmrModal:!1,personInfo:{}}},mounted:function(){this.$route.query.aaz217&&(this.aaz217=this.$route.query.aaz217),this.$route.query.akb020&&(this.akb020=this.$route.query.akb020),this.$route.query.flag&&(this.flag=this.$route.query.flag),"0"!=this.$route.query.flag&&"1"!=this.$route.query.flag||(this.activeKey="Kc21k1"),this.fnQueryTableTitle()},methods:{fnQueryTableTitle:function(){var e=this,t=top.indexTool.getActiveTabMenuId(),a=top.indexTool.getUserInfo().loginId;this.Base.submit(null,{url:"miimCommonRead/selectTableColumShow",data:{resourceid:t,loginid:a},autoValid:!1},{successCallback:function(t){var a,l,o,i;(null===(a=t.data)||void 0===a||null===(l=a.list)||void 0===l?void 0:l.length)>0&&null!==t&&void 0!==t&&null!==(o=t.data)&&void 0!==o&&null!==(i=o.list[0])&&void 0!==i&&i.colum&&(e.onlyDoubts=JSON.parse(t.data.list[0].colum).onlyDoubts),e.fnTabChange(e.activeKey),e.getPersonInfo()},failCallback:function(t){e.$message.error("查询标志失败")}})},queryCountMoney:function(){var e=this;this.Base.submit(null,{url:"dischargeClinic/queryCountMoney",data:this.pageParam,autoValid:!1},{successCallback:function(t){t.data.list.length>0&&t.data.list[0]?e.count=t.data.list[0].count:e.count="0",e.fnTabChange(e.activeKey)},failCallback:function(t){e.$message.error("查询金额数量失败")}})},footerMethod:function(e){var t=this,a=e.columns;return[a.map((function(e,a){return["akc226"].includes(e.property)?"总计:  "+t.count:null}))]},onChangeSwitch:function(e){this.countShowFlag=e,this.fnTabChange("fyxx")},onChangeSwitch2:function(e){this.onlyDoubts=e,this.fnSaveOnlyDoubts(),this.fnTabChange("fyxx")},fnSaveOnlyDoubts:function(){var e=this,t={onlyDoubts:this.onlyDoubts},a=top.indexTool.getActiveTabMenuId(),l=top.indexTool.getUserInfo().loginId,o={colum:JSON.stringify(t),flag:"column",resourceid:a,loginid:l};b.Z.insertTableColumShow(o,(function(t){"success"===t?e.$message.success("保存成功"):e.$message.error("保存失败")}))},tagFlagByAaa005:function(e,t){return!!e.includes(t)},getPersonInfo:function(){var e=this,t=this.$route.query.aaz217,a=this.$route.query.flag;null!=t&&""!=t&&null!=a&&""!=a&&this.Base.submit(null,{url:"miimCommonRead/queryPersonInfo",data:{aaz217:t,aae500:a},showPageLoading:!1,autoValid:!1},{successCallback:function(l){l.data.akc191Title.length>0?e.akc191Title=l.data.akc191Title[0].label:e.akc191Title="住院号",e.Kc39flag=e.tagFlagByAaa005(l.data.tagsAaa005,"kc39"),e.Kc34flag=e.tagFlagByAaa005(l.data.tagsAaa005,"kc34"),e.Kc37flag=e.tagFlagByAaa005(l.data.tagsAaa005,"kc37"),e.Kc41flag=e.tagFlagByAaa005(l.data.tagsAaa005,"kc41"),e.showEmrButton=l.data.emrConfig.emrLinkEnabled,e.emrConfig=l.data.emrConfig,e.personInfo=l.data.resultData[0],e.personInfo.aaz217Query=t,e.personInfo.aae500=a},failCallback:function(t){e.$message.error("查询个人信息失败")}})},openEmrPage:function(){if(this.emrConfig.isOutLink){var e={aae500:this.$route.query.flag,aaz217:this.aaz217};d.queryEmrPageDetail(e,(function(e){window.open(e.data.detailInfo,"newWindow","toolbar=no, menubar=no, location=no, status=no")}))}else this.showEmrModal=!0},closeEmrPage:function(){this.showEmrModal=!1},pageParam:function(){var e=this.fyxxForm.getFieldsValue(),t={akb020:this.akb020,akc190:this.personInfo.akc190,aaz217:this.aaz217,countShowFlag:this.countShowFlag,onlyDoubts:this.onlyDoubts,orderFlag:"other",flag:this.flag};return e.allDate&&(e.allDate[0]&&(t.startDate=e.allDate[0].format("YYYY-MM-DD")),e.allDate[1]&&(t.endDate=e.allDate[1].format("YYYY-MM-DD"))),"fyxx"===this.activeKey&&(t.projectInfo=e.projectInfo,t.inProjectInfo=e.inProjectInfo),t},fnTabChange:function(e){var t=this;this.gridData=[],this.url="dischargeClinic/get"+e,this.$nextTick((function(){t.$refs.infoPageRef.loadData((function(e){e.data.list&&e.data.list.length>0&&e.data.list[0]?(t.count=e.data.list[0].count+"",t.money="￥"+e.data.list[0].money.toFixed(2)):(t.count="0",t.money="￥0.00")}))}))},fnCustomRow:function(e,t){return{style:{fontFamily:"Microsoft YaHei, Hiragino Sans GB, Pingfang SC, Arial, Helvetica Neue, Helvetica",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px"}}},fnCustomHeaderCell:function(e){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"18px",fontWeight:"bold"}}}}},w=v,y=(0,g.Z)(w,l,o,!1,null,"94ca198a",null),k=y.exports},36766:function(e,t,a){var l=a(66586);t["Z"]=l.Z},26263:function(e,t,a){a.d(t,{s:function(){return l},x:function(){return o}});var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:e.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[e._v(e._s(e.title))]),e._t("default")],2)},o=[]},66586:function(e,t){var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};t["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}},83231:function(e,t,a){var l=a(48534);a(36133);function o(e){return e?e.akb020&&e.aaz263?"personal":e.akb020&&e.aaz309&&e.aaz307?"group":e.akb020&&e.aaz307?"department":e.akb020?"hospital":"all":"all"}function i(e){return r.apply(this,arguments)}function r(){return r=(0,l.Z)(regeneratorRuntime.mark((function e(t){var a,l,i,r,n,s,c,d,u,f,h,m;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,Base.submit(null,{url:"/miimCommonRead/queryDataPermission",showPageLoading:!1,data:{jobnumber:t},autoValid:!0});case 2:return a=e.sent,l=new Set,i=new Set,a.data.permission.forEach((function(e){var t=o(e);"hospital"===t&&l.add(e.akb020),"department"===t&&i.add(e.aaz307)})),r=a.data.permission.filter((function(e){return"department"===o(e)||!i.has(e.aaz307)})).filter((function(e){return"hospital"===o(e)||!l.has(e.akb020)})),n=new Set(r.map((function(e){return e.akb020})).filter((function(e){return null!=e}))),s=new Set(r.map((function(e){return e.aaz307})).filter((function(e){return null!=e}))),c=new Set(r.map((function(e){return e.aaz309})).filter((function(e){return null!=e}))),d=new Set(r.map((function(e){return e.aaz263})).filter((function(e){return null!=e}))),u=!1,f=!1,h=!1,m=!1,1===n.size&&(u=!0),1===s.size&&1===n.size&&(f=!0),1===s.size&&1===n.size&&1===c.size&&(h=!0),1===n.size&&0===s.size&&1===d.size&&(m=!0),e.abrupt("return",{akb020Set:n,aaz307Set:s,aaz263Set:d,aaz309Set:c,akb020Disable:u,aaz307Disable:f,aaz263Disable:m,aaz309Disable:h});case 20:case"end":return e.stop()}}),e)}))),r.apply(this,arguments)}function n(e,t){Base.submit(null,{url:"miimCommonRead/getAa01AAE500StartStop",data:e},{successCallback:function(e){return t(e)}})}function s(e,t){Base.submit(null,{url:"miimCommonRead/insertTableColumShow",data:e,autoValid:!1},{successCallback:function(e){return t("success")},failCallback:function(e){return t("fail")}})}var c={zbx01:{footer:function(){return"统筹金总额"}},zbx02:{footer:function(){return"基金支出总额"}},zbx03:{footer:function(){return"总费用"}},zbx04:{footer:function(){return"总住院日"}}};t["Z"]={permissionCheck:i,getAa01AAE500StartStop:n,insertTableColumShow:s,props:c,moneyNumFormat:function(e){var t=e.cellValue;return t||"—"}}}}]);
"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[1999],{51999:function(t,e,a){a.r(e),a.d(e,{default:function(){return f}});var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"fit",attrs:{id:"dictTypeMg"}},[a("ta-border-layout",{attrs:{layout:{header:"70px"},centerCfg:{showBar:!0,toolBarStyle:{height:"50px",lineHeight:"48px"}}}},[a("div",{staticClass:"center-box",attrs:{slot:"header"},slot:"header"},[a("ta-input-search",{staticClass:"search-box",attrs:{placeholder:"根据字典标签、键值查询","enter-button":"搜索"},on:{search:t.queryDictByTypeWithParam},model:{value:t.dictQueryParam.dictInfo,callback:function(e){t.$set(t.dictQueryParam,"dictInfo",e)},expression:"dictQueryParam.dictInfo"}})],1),a("div",{attrs:{slot:"centerExtraContent"},slot:"centerExtraContent"},[a("ta-tag-select",{attrs:{title:"字典状态",data:t.CollectionData("EFFECTIVE")},on:{change:t.filterClick},model:{value:t.isEffectiveList,callback:function(e){t.isEffectiveList=e},expression:"isEffectiveList"}}),a("ta-tag-select",{staticClass:"filter-name",attrs:{title:"缓存状态",data:t.CollectionData("DICTDATATYPE")},on:{change:t.filterClick},model:{value:t.cacheStatusList,callback:function(e){t.cacheStatusList=e},expression:"cacheStatusList"}}),a("div",{staticStyle:{float:"right"}},[a("ta-button",{on:{click:t.fnBackToHome}},[a("ta-icon",{attrs:{type:"rollback"}}),t._v(" 返回 ")],1),a("ta-button",{attrs:{type:"primary"},on:{click:t.fnToAddDict}},[t._v("新增字典")]),a("ta-dropdown",[a("ta-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("ta-menu-divider"),a("ta-menu-item",{attrs:{disabled:t.btnDisable}},[a("ta-popconfirm",{attrs:{title:"确认启用所选字典?",cancelText:"取消",okText:"确认"},on:{confirm:function(e){return t.fnStartDicts(!1)}}},[a("ta-icon",{attrs:{type:"check-circle"}}),a("span",{staticClass:"mg-l12"},[t._v("启用")])],1)],1),a("ta-menu-divider"),a("ta-menu-item",{attrs:{disabled:t.btnDisable}},[a("ta-popconfirm",{attrs:{title:"确认禁用所选字典?",cancelText:"取消",okText:"确认"},on:{confirm:function(e){return t.fnStopDicts(!1)}}},[a("ta-icon",{attrs:{type:"stop"}}),a("span",{staticClass:"mg-l12"},[t._v("禁用")])],1)],1),a("ta-menu-divider"),a("ta-menu-item",{attrs:{disabled:t.btnDisable},on:{click:function(e){t.deleteVisible=!0}}},[a("ta-icon",{attrs:{type:"close-circle"}}),t._v(" 删除 ")],1)],1),a("ta-button",[t._v(" 批量操作 "),a("ta-icon",{attrs:{type:"down"}})],1)],1),a("ta-button",{on:{click:t.fnRefreshCache}},[t._v("刷新缓存")])],1)],1),a("div",[a("ta-table",{ref:"dictContentGrid",attrs:{columns:t.dictContentColumns,pagination:!1,dataSource:t.dictContentGridData,rowKey:"value",rowSelection:{selectedRowKeys:t.selectedRowKeys,onSelect:t.fnOnSelect,onSelectAll:t.fnOnSelectAll},defaultExpandAllRows:t.defaultExpandAllRows},scopedSlots:t._u([{key:"status",fn:function(e){return a("span",{},[t._v(t._s(t.CollectionLabel("EFFECTIVE",e)))])}},{key:"system",fn:function(e){return a("span",{},[t._v(" "+t._s(t.CollectionLabel("YESORNO",e))+" ")])}},{key:"cacheStatus",fn:function(e){return a("span",{},["2"==e?a("ta-tag",{staticClass:"no-cursor",attrs:{type:"success"}},[t._v("已同步")]):a("ta-tag",{staticClass:"no-cursor",attrs:{type:"danger"}},"0"==e?[t._v("未同步")]:[t._v("脏数据")])],1)}},{key:"action",fn:function(e,i){return a("span",{},[a("ta-table-operate",{attrs:{operateMenu:t.operateMenu}})],1)}}])})],1)]),a("ta-drawer",{attrs:{destroyOnClose:"",title:"系统字典管理",width:"500",placement:"right",closable:"",footerHeight:"",visible:t.dictAddOrEditVisible},on:{close:function(e){return t.fnDictAddOrEditDrawerClose(!1)}}},[a("div",{attrs:{slot:"footer"},slot:"footer"},[a("ta-button-group",[a("ta-button",{on:{click:t.resetForm}},[t._v("重置")]),a("ta-button",{attrs:{type:"primary"},on:{click:t.saveDict}},[t._v("保存")])],1)],1),a("system-dict-mg",{ref:"sysDictChild",attrs:{dict:t.bindDict},on:{closeSystemDictMgDrawer:function(e){return t.fnDictAddOrEditDrawerClose(!0)}}})],1),a("ta-careful-delete",{attrs:{visible:t.deleteVisible,title:"字典类型删除",description:"所选字典(当前字典及其下级所有字典都会被删除)"},on:{close:function(e){t.deleteVisible=!1},delete:t.fnDeleteDicts}})],1)},s=[],c=a(78746),n=a(26682),r=[{title:"字典标签",dataIndex:"label",width:"20%"},{title:"字典键值",dataIndex:"value",sorter:function(t,e){return t.value-e.value},width:"13%"},{title:"字典状态",dataIndex:"status",scopedSlots:{customRender:"status"},width:"10%"},{title:"系统字典",dataIndex:"system",yesOrNoTag:!0,scopedSlots:{customRender:"system"},width:"10%"},{title:"创建时间",dataIndex:"createDate",width:"17%"},{title:"缓存状态",dataIndex:"cacheStatus",scopedSlots:{customRender:"cacheStatus"},width:"10%"},{title:"操作",dataIndex:"dictAction",align:"center",scopedSlots:{customRender:"action"},width:"20%"}],l={name:"dictTypeMg",components:{SystemDictMg:c.Z},data:function(){var t=this;return{defaultExpandAllRows:!0,dict:{},bindDict:{},dictContentColumns:r,operateMenu:[{name:"添加下级",title:function(t){return"1"==t.cacheStatus||"0"==t.system||"0"==t.status?"禁用的字典不能添加下级":""},disabled:function(t){return"1"==t.cacheStatus||"0"==t.system||"0"==t.status},onClick:function(e){t.fnToAddNextLevelDict(e)}},{name:"编辑",isShow:function(t){return"1"==t.cacheStatus||"0"==t.system},disabled:function(){return!0}},{name:"编辑",title:function(t){return"1"==t.cacheStatus||"0"==t.system||"0"==t.status?"禁用的字典不能编辑":""},disabled:function(t){return"1"==t.cacheStatus||"0"==t.system||"0"==t.status},isShow:function(t){return!("1"==t.cacheStatus||"0"==t.system)},onClick:function(e){t.fnToEditDict(e)}},{name:"更多",type:"more",moreMenuList:[{name:"启用",type:"confirm",confirmTitle:"确认启用该字典?",onOk:function(e){t.fnStartDicts(e)}},{name:"禁用",type:"confirm",confirmTitle:"确认禁用该字典?",onOk:function(e){t.fnStopDicts(e)}},{name:"删除",isShow:function(t){return"1"==t.cacheStatus||"0"==t.system},disabled:function(){return!0}},{name:"删除",type:"confirm",confirmTitle:function(t){return"2"==t.cacheStatus?"确定删除该字典数据并更新该字典类型下的缓存？":"0"==t.cacheStatus?"确定从数据库删除该字典数据并更新该字典类型下的缓存？":void 0},isShow:function(t){return!("1"==t.cacheStatus||"0"==t.system)},onOk:function(e){t.fnDeleteDict(e)}}]}],dictContentGridData:[],selectedRowKeys:[],selectedRows:[],dictQueryParam:{},dictAddOrEditVisible:!1,isEffectiveList:[],cacheStatusList:[],deleteVisible:!1}},computed:{btnDisable:function(){return!this.selectedRowKeys.length}},mounted:function(){this.dict=this.$route.params.dict,this.dict&&this.fnLoadDeFaultDict()},filters:{statusFilter:function(t){var e="";switch(t){case"1":e="有效";break;case"0":e="无效";break;default:e="--"}return e}},methods:{fnBackToHome:function(){this.$router.push({name:"dictMg"})},resetForm:function(){this.$refs.sysDictChild.resetForm()},fnOnSelect:function(t,e){var a=this.selectedRowKeys,i=this.selectedRows;e?(a.push(t.value),i.push(t)):(this.selectedRowKeys=a.filter((function(e){return e!=t.value})),this.selectedRows=i.filter((function(e){return e.value!=t.value})))},fnOnSelectAll:function(t,e){var a=this;this.selectedRows=[],this.selectedRowKeys=[],t&&(e.map((function(t){a.selectedRowKeys.push(t.value)})),this.selectedRows=e)},saveDict:function(){this.$refs.sysDictChild.saveDict()},fnDictAddOrEditDrawerClose:function(t){this.dictAddOrEditVisible=!1,this.bindDict={};this.dict;t&&this.queryDictByTypeWithParam()},fnToAddDict:function(){"0"!=this.dict.system?(this.bindDict=this.dict,this.bindDict.drawerType="add",this.bindDict.parentLabel="",this.bindDict.parentValue="",this.dictAddOrEditVisible=!0):this.$message.warning("非系统字典不能进行新增字典操作！")},fnToEditDict:function(t){this.bindDict=t,this.bindDict.parentLabel="",this.bindDict.parentValue="",this.bindDict.drawerType="edit",this.dictAddOrEditVisible=!0},fnToAddNextLevelDict:function(t){this.bindDict.drawerType="add",this.bindDict.parentLabel=t.label,this.bindDict.parentValue=t.value,this.bindDict.type=t.type,this.bindDict.name=t.name,this.bindDict.authority=t.authority,this.dictAddOrEditVisible=!0},fnRefreshCache:function(){var t=this;this.Base.submit(null,{url:"miimCommonRead/refreshAllDictCache",data:{},autoValid:!1},{successCallback:function(e){t.$message.success("Audit缓存刷新成功")},failCallback:function(e){t.$message.error("Audit缓存刷新失败")}});var e=this.dict.type+"."+this.dict.authority,a={keys:e};n.Z.refreshDictByType(a,(function(e){t.$message.success("同步成功！"),t.queryDictByTypeWithParam()}))},fnStartDicts:function(t){var e,a,i=this;if(t){if("2"==t.cacheStatus)return void this.$message.warning("该记录已经启用，请勿重复操作！");a={type:this.dict.type,authority:this.dict.authority,values:t.value}}else{if("0"==this.dict.system)return void this.$message.warning("非系统字典不能进行批量禁用操作！");e=this.selectedRows.map((function(t){return t.value})),a={type:this.dict.type,authority:this.dict.authority,values:e.join(",")}}n.Z.startBatchDict(a,(function(t){i.$message.success("启用成功！"),i.selectedRows=[],i.queryDictByTypeWithParam()}))},fnDeleteDict:function(t){var e=this,a={type:this.dict.type,authority:this.dict.authority,values:t.value};n.Z.deleteBatchDict(a,(function(t){e.$message.success("删除成功！"),e.selectedRows=[],e.queryDictByTypeWithParam()}))},fnStopDicts:function(t){var e,a,i=this;if(t){if("0"==t.cacheStatus)return void this.$message.warning("该记录已经禁用，请勿重复操作！");a={type:this.dict.type,authority:this.dict.authority,values:t.value}}else{if("0"==this.dict.system)return void this.$message.warning("非系统字典不能进行批量禁用操作！");e=this.selectedRows.map((function(t){return t.value})),a={type:this.dict.type,authority:this.dict.authority,values:e.join(",")}}n.Z.stopBatchDict(a,(function(t){i.$message.success("禁用成功！"),i.selectedRows=[],i.queryDictByTypeWithParam()}))},fnDeleteDicts:function(){var t=this;if(this.deleteVisible=!1,"0"!=this.dict.system){var e=this.selectedRows.map((function(t){return t.value})),a={type:this.dict.type,authority:this.dict.authority,values:e.join(",")};n.Z.deleteBatchDict(a,(function(e){t.$message.success("删除成功！"),t.selectedRows=[],t.queryDictByTypeWithParam()}))}else this.$message.warning("非系统字典不能进行批量删除操作！")},fnLoadDeFaultDict:function(){var t=this,e={type:this.dict.type||"",authority:this.dict.authority};n.Z.queryDictByType(e,(function(e){t.dictContentGridData=e.data.dictContentGridData}))},queryDictByTypeWithParam:function(){var t=this,e={type:this.dict.type,authority:this.dict.authority,status:this.dictQueryParam.status,dictInfo:this.dictQueryParam.dictInfo,cacheStatus:this.dictQueryParam.cacheStatus};n.Z.queryDictByType(e,(function(e){t.dictContentGridData=e.data.dictContentGridData,t.selectedRowKeys=[],t.selectedRows=[]}))},filterClick:function(){this.dictQueryParam.status=this.isEffectiveList[0],this.dictQueryParam.cacheStatus=this.cacheStatusList[0],this.queryDictByTypeWithParam()}}},o=l,u=a(1001),d=(0,u.Z)(o,i,s,!1,null,"8a6b7408",null),f=d.exports},78746:function(t,e,a){a.d(e,{Z:function(){return d}});var i=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("ta-form",{attrs:{id:"dictForm",autoFormCreate:function(e){t.form=e}}},[i("ta-form-item",{attrs:{label:"字典类型",labelCol:e.formItemLayout.labelCol,wrapperCol:e.formItemLayout.wrapperCol,fieldDecoratorId:"type",fieldDecoratorOptions:{rules:[{required:!0,message:"字典类型不能为空!"}]}}},[i("ta-input",{attrs:{disabled:!e.edit}})],1),i("ta-form-item",{attrs:{label:"字典名称",labelCol:e.formItemLayout.labelCol,wrapperCol:e.formItemLayout.wrapperCol,fieldDecoratorId:"name",fieldDecoratorOptions:{rules:[{required:!0,message:"字典名称不能为空"}]}}},[i("ta-input",{attrs:{disabled:!e.edit}})],1),e.dict.parentValue&&""!==e.dict.parentValue?i("ta-form-item",{attrs:{label:"父级字典",labelCol:e.formItemLayout.labelCol,wrapperCol:e.formItemLayout.wrapperCol}},[i("ta-input",{attrs:{disabled:!0},model:{value:e.parentLabel,callback:function(t){e.parentLabel=t},expression:"parentLabel"}})],1):e._e(),i("ta-form-item",{attrs:{label:"字典标签",labelCol:e.formItemLayout.labelCol,wrapperCol:e.formItemLayout.wrapperCol,fieldDecoratorId:"label",fieldDecoratorOptions:{rules:[{required:!0,message:"字典标签不能为空"}]}}},[i("ta-input")],1),i("ta-form-item",{attrs:{label:"字典键值",labelCol:{span:6},wrapperCol:{span:18},fieldDecoratorId:"value",fieldDecoratorOptions:{rules:[{required:!0,message:"字典键值不能为空"}]}}},[i("ta-input")],1),i("ta-form-item",{attrs:{label:"排序号",labelCol:{span:6},wrapperCol:{span:18},fieldDecoratorId:"sort",fieldDecoratorOptions:{rules:[{required:!0,message:"排序号不能为空"},{type:"number",min:0,max:2e8,message:"排序号需在0~200000000之间！"}]}}},[i("ta-input")],1),i("ta-form-item",{attrs:{label:"CSS样式",labelCol:e.formItemLayout.labelCol,wrapperCol:e.formItemLayout.wrapperCol,fieldDecoratorId:"cssStyle"}},[i("ta-input")],1),i("ta-form-item",{attrs:{label:"CSS Class",labelCol:e.formItemLayout.labelCol,wrapperCol:e.formItemLayout.wrapperCol,fieldDecoratorId:"cssClass"}},[i("ta-input")],1),i("ta-form-item",{attrs:{id:"status",label:"是否有效",labelCol:e.formItemLayout.labelCol,wrapperCol:e.formItemLayout.wrapperCol}},[i("ta-switch",{attrs:{checkedChildren:"有效",unCheckedChildren:"无效"},model:{value:e.dictStatus,callback:function(t){e.dictStatus=t},expression:"dictStatus"}})],1)],1)},s=[],c=a(26682),n={labelCol:{span:6},wrapperCol:{span:18}},r={name:"systemDictMg",props:["dict"],data:function(){return{edit:!1,formItemLayout:n,parentValueSelect:!1,parentValueList:[],parentValue:void 0,parentLabel:"",dictStatus:!0,authority:this.dict.authority}},mounted:function(){this.dict.drawerType&&"edit"!==this.dict.drawerType&&this.fnLoadParentValue(),this.fnBindForm()},methods:{fnLoadParentValue:function(){var t=this,e=this.dict,a=e.type,i=e.authority,s=e.value,n=e.parentValue,r=e.parentLabel,l=e.drawerType,o={type:a,authority:i,value:s};n&&""!==n&&(this.parentLabel=r,this.parentValue=n),c.Z.queryDictInfo(o,(function(e){"add"===l&&t.form.setFieldsValue({sort:e.data.sort})}))},resetForm:function(){this.fnBindForm()},saveDict:function(){var t=this;this.form.validateFields((function(e){if(!e){var a=t.dict.drawerType,i=t.dict.system,s=t.form.getFieldsValue(),n=s,r=t.parentValue,l=1==t.dictStatus?"1":"0";n.oldType=t.dict.type,n.oldValue=t.dict.value,n.parentValue=r,n.status=l,n.system=i,n.authority=t.authority,t.Base.submit(null,{url:"miimCommonRead/refreshDictCache",data:{label:n.label,value:n.value,type:n.type},autoValid:!1},{successCallback:function(e){t.$message.success("Audit缓存刷新成功")},failCallback:function(e){t.$message.error("Audit缓存刷新失败")}}),"add"==a?c.Z.saveDict(n,(function(e){t.$message.success("新增字典成功！"),t.$emit("closeSystemDictMgDrawer")})):c.Z.updateDict(n,(function(e){t.$message.success("更新字典成功！"),t.$emit("closeSystemDictMgDrawer")}))}}))},fnBindForm:function(){var t=this.dict,e=t.type,a=t.name,i=t.label,s=t.value,c=t.sort,n=t.cssStyle,r=t.cssClass,l=t.authority,o=t.status;this.form.setFieldsValue({type:e,name:a,label:i,value:s,sort:c,cssStyle:n,cssClass:r,authority:l}),o&&"1"==o?this.dictStatus=!0:o&&"0"==o&&(this.dictStatus=!1)}}},l=r,o=a(1001),u=(0,o.Z)(l,i,s,!1,null,null,null),d=u.exports},26682:function(t,e){var a="/dictmg/dictMgRestService/";e["Z"]={queryType:function(t,e){Base.submit(null,{url:a+"queryType",data:t},{successCallback:function(t){return e(t)}})},deleteDictByType:function(t,e){Base.submit(null,{url:a+"deleteDictByType",data:t},{successCallback:function(t){return e(t)}})},queryDictByType:function(t,e){Base.submit(null,{url:a+"queryDictByType",data:t},{successCallback:function(t){return e(t)}})},saveDict:function(t,e){Base.submit(null,{url:a+"saveDict",data:t},{successCallback:function(t){return e(t)}})},updateDict:function(t,e){Base.submit(null,{url:a+"updateDict",data:t},{successCallback:function(t){return e(t)}})},startDictByType:function(t,e){Base.submit(null,{url:a+"startDictByType",data:t},{successCallback:function(t){return e(t)}})},stopDictByType:function(t,e){Base.submit(null,{url:a+"stopDictByType",data:t},{successCallback:function(t){return e(t)}})},refreshDictCacheByType:function(t,e){Base.submit(null,{url:a+"refreshDictByType",data:t},{successCallback:function(t){return e(t)}})},saveType:function(t,e){Base.submit(null,{url:a+"saveType",data:t},{successCallback:function(t){return e(t)}})},deleteBatchDict:function(t,e){Base.submit(null,{url:a+"deleteBatchDict",data:t},{successCallback:function(t){return e(t)}})},stopBatchDict:function(t,e){Base.submit(null,{url:a+"stopBatchDict",data:t},{successCallback:function(t){return e(t)}})},startBatchDict:function(t,e){Base.submit(null,{url:a+"startBatchDict",data:t},{successCallback:function(t){return e(t)}})},refreshDictByType:function(t,e){Base.submit(null,{url:a+"refreshDictByType",data:t},{successCallback:function(t){return e(t)}})},queryDictContent:function(t,e){Base.submit(null,{url:a+"queryDictContent",data:t},{successCallback:function(t){return e(t)}})},queryDictInfo:function(t,e){Base.submit(null,{url:a+"queryDictInfo",data:t},{successCallback:function(t){return e(t)}})},queryDictAuthorityList:function(t,e){var i=a+"queryAuthorityList";Base.submit(null,{url:i,data:t},{successCallback:function(t){return e(t)}})}}}}]);
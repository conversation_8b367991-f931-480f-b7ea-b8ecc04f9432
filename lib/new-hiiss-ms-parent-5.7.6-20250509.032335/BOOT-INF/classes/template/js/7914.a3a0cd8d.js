(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[7914],{88412:function(t,e,a){"use strict";var i=a(26263),o=a(36766),r=a(1001),s=(0,r.Z)(o.Z,i.s,i.x,!1,null,"5e7ef0ae",null);e["Z"]=s.exports},50552:function(t,e,a){"use strict";a.r(e),a.d(e,{default:function(){return v}});var i=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("div",{staticClass:"fit"},[i("ta-border-layout",{staticStyle:{},attrs:{"layout-type":"fixTop"}},[i("div",{attrs:{slot:"header"},slot:"header"},[i("div",{staticClass:"query-header"},[i("div",{ref:"formBox",staticClass:"query-header-left",style:e.formBoxStyle},[i("ta-form",{attrs:{autoFormCreate:function(e){t.baseInfoForm=e},col:e.col,layout:"horizontal","label-width":"80px",formLayout:!0}},[i("ta-form-item",{attrs:{"field-decorator-id":"auditDate","init-value":e.rangeValue}},[i("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("审核时间")]),i("ta-range-picker",{staticStyle:{width:"100%"},attrs:{"allow-one":!0}},[i("ta-icon",{attrs:{slot:"suffixIcon",type:""},slot:"suffixIcon"})],1)],1),i("ta-form-item",{attrs:{"field-decorator-id":"flag",label:"出院场景",disabled:e.cjflag,"field-decorator-options":{rules:[{required:!0,message:"必须选取出院场景"}]}}},[i("ta-select",{attrs:{showSearch:"",placeholder:"请选择出院场景","collection-type":"AAE500","collection-filter":e.filterList,reverseFilter:!0}})],1),i("ta-form-item",{attrs:{fieldDecoratorId:"projectInfo",label:"医保项目"}},[i("ta-input",{attrs:{placeholder:"请输入医保项目名称或编码"}})],1)],1)],1),i("div",{staticStyle:{display:"flex","margin-left":"10px",float:"right"}},[i("div",{staticClass:"ctrl-btn",on:{click:e.formShowAllChange}},[i("a",[e._v(e._s(e.formShowAll?"收起":"展开"))]),e.formShowAll?i("ta-icon",{attrs:{type:"caret-up"}}):i("ta-icon",{attrs:{type:"caret-down"}})],1),i("ta-button",{attrs:{type:"primary",icon:"search"},on:{click:e.fnQuery}},[e._v("查询")]),i("ta-button",{attrs:{type:"default",icon:"redo"},on:{click:e.fnReset}},[e._v("重置")])],1)])]),i("div",{staticClass:"fit content-box"},[i("ta-title",{attrs:{title:"查询结果"}}),i("div",{staticClass:"content-table"},[i("ta-big-table",{ref:"infoTableRef",attrs:{columns:e.infoColumns,data:e.infoTableData,border:"",height:"auto","highlight-hover-row":"","highlight-current-row":"","auto-resize":"","show-overflow":"",resizable:""},scopedSlots:e._u([{key:"operate",fn:function(t){var a=t.row;return[i("a",{on:{click:function(t){return e.fnSearch(a)}}},[i("ta-icon",{staticStyle:{color:"blue","margin-left":"5px"},attrs:{type:"search"}}),e._v(" 查看明细")],1)]}}])})],1),i("div",{staticClass:"content-box-footer"},[i("ta-pagination",{ref:"infoPageRef",staticStyle:{position:"absolute",right:"6px",bottom:"6px","margin-right":"100px"},attrs:{dataSource:e.infoTableData,params:e.infoPageParams,defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"],url:"dischargeClinic/queryViolationPatient"},on:{"update:dataSource":function(t){e.infoTableData=t},"update:data-source":function(t){e.infoTableData=t}}}),i("ta-button",{staticStyle:{position:"absolute",right:"6px",bottom:"6px"},attrs:{type:"primary",icon:"download"},on:{click:e.exportExcel}},[e._v("导出")])],1)],1)])],1)},o=[],r=a(66347),s=a(48534),l=a(95082),n=(a(36133),a(88412)),u=a(36797),c=a.n(u),f=a(22722),d=(a(83231),a(55115));a(55067);d.w3.prototype.Base=Object.assign(d.w3.prototype.Base,(0,l.Z)({},f.Z));var m=[],h=[],p={name:"violationProgram",components:{TaTitle:n.Z},data:function(){var t=[{type:"seq",title:"序号",width:"60",align:"center"},{title:"出院科室",field:"aae386",align:"left",width:120,sortable:!0,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"档案号",field:"akc191",align:"left",sortable:!0,width:120,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"患者姓名",field:"aac003",align:"left",sortable:!0,width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"项目编码",field:"ake001",align:"left",sortable:!0,width:200,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell,customRender:{default:"aac003"}},{title:"项目名称",field:"ake002",align:"left",sortable:!0,width:200,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell,customRender:{default:"aac003"}},{title:"实收总数量",field:"akc226",align:"left",sortable:!0,width:120,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"违规总数量",field:"ape805",align:"left",sortable:!0,width:120,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"违规总金额",field:"ape804",align:"left",sortable:!0,width:120,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"性别",field:"aac004",align:"center",width:60,collectionType:"SEX",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"年龄",field:"aac017",align:"right",width:60,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"医保类型",field:"aae141",align:"center",sortable:!0,width:100,overflowTooltip:!0,collectionType:"AAE141"},{title:"入院时间",field:"aae030",align:"center",minWidth:110,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"出院时间",field:"aae031",align:"center",minWidth:130,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"审核时间",field:"aae040",align:"center",width:130,sortable:!0,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"操作",field:"operate",align:"center",fixed:"right",width:110,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell,scopedSlots:{customRender:"operate"},customRender:{default:"operate"}}];return{col:{xs:1,sm:2,md:2,lg:4,xl:4,xxl:4},rangeValue:[this.Base.getMoment((new Date).toISOString().slice(0,8)+"-01","YYYY-MM-DD"),this.Base.getMoment((new Date).toISOString().slice(0,10),"YYYY-MM-DD")],hosList:[],ksList:[],doctorList:[],ksTableData:m,infoColumns:t,infoTableData:h,akb020:"",aaz307:"",expandedRowKeys:[],showRecord:"0",permissions:null,formShowAll:!0,cjflag:!1,aaz217Params:"",akb020Params:"",ykz040:null,filterList:"",paramsDisable:{akb020:!1,aaz307:!1,ykz041:!1}}},mounted:function(){var t=this;return(0,s.Z)(regeneratorRuntime.mark((function e(){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.$route.query.params&&(a=JSON.parse(t.$route.query.params),a.auditDate=a.auditDate.map((function(e){var a=new Date(e);return a.setDate(a.getDate()+1),t.Base.getMoment(a.toISOString().slice(0,11),"YYYY-MM-DD")})),t.$route.query.aaz217&&(t.aaz217Params=t.$route.query.aaz217),t.$route.query.aaz307&&(a.aaz307=t.$route.query.aaz307),t.akb020Params=a.medinsCode,t.baseInfoForm.setFieldsValue(a),t.cjflag=!0,t.fnQuery());case 1:case"end":return e.stop()}}),e)})))()},computed:{formBoxStyle:function(){return this.formShowAll?{height:"auto"}:{height:"50px"}}},methods:{fnReset:function(){this.formShowAll=!1,this.baseInfoForm.resetFields(),this.baseInfoForm.setFieldsValue({flag:"5"}),this.fnQuery()},formShowAllChange:function(){this.formShowAll=!this.formShowAll},createDate:function(){var t=new Date;return t.setMonth(t.getMonth()-2),t},fnQueryHos:function(){var t=this,e={url:"/miimCommonRead/queryHospRpcList",autoValid:!0},a={successCallback:function(e){t.hosList=e.data.resultData,t.baseInfoForm.setFieldsValue({medinsCode:t.hosList[0].value}),t.permissions.akb020Set.size>0&&(t.hosList=t.hosList.filter((function(e){return t.permissions.akb020Set.has(e.value)})),t.baseInfoForm.setFieldsValue({medinsCode:t.hosList[0].value})),t.akb020=t.hosList[0].value,t.fnQueryDept(t.akb020)},failCallback:function(e){t.$message.error("医院数据加载失败")}};this.Base.submit(null,e,a)},fnQueryDept:function(t){var e=this;this.akb020=t||"",this.Base.submit(null,{url:"miimCommonRead/queryDepartRpcList",data:{akb020:this.akb020,hospDeptType:"I"},autoValid:!1},{successCallback:function(t){e.$route.query.params||e.baseInfoForm.resetFields("aaz307"),e.ksList=t.data.resultData,e.setPermission()},failCallback:function(t){e.$message.error("科室数据加载失败")}})},setPermission:function(){var t=this;this.paramsDisable.akb020=this.permissions.akb020Disable,this.paramsDisable.aaz307=this.permissions.aaz307Disable,this.paramsDisable.aaz263=this.permissions.aaz263Disable,this.permissions&&(this.permissions.aaz307Set.size>0&&(this.ksList=this.ksList.filter((function(e){return t.permissions.aaz307Set.has(e.value)})),this.permissions.aaz307Disable&&this.baseInfoForm.setFieldsValue({aaz307:this.ksList[0].value})),this.permissions.aaz263Set.size>0&&(this.doctorList=this.doctorList.filter((function(e){return t.permissions.aaz263Set.has(e.value)})),this.permissions.aaz263Disable&&(this.ykz040=this.doctorList[0].value)))},fnQueryDocter:function(t){var e=this,a={akb020:this.akb020};t&&(a.departCode=t),this.Base.submit(null,{url:"miimCommonRead/queryDoctorRpcList",data:a,autoValid:!1},{successCallback:function(t){e.baseInfoForm.resetFields("aaz263"),e.doctorList=t.data.resultData},failCallback:function(t){e.$message.error("医师数据加载失败")}})},disabledStartDate:function(t){return t.format("YYYYMMDD")>this.baseInfoForm.getFieldValue("endDate").format("YYYYMMDD")},disabledEndDate:function(t){return t=t.format("YYYYMMDD"),t>c()().startOf("day").format("YYYYMMDD")||t<this.baseInfoForm.getFieldValue("startDate").format("YYYYMMDD")},infoPageParams:function(){var t=this.baseInfoForm.getFieldsValue();return t.auditDate&&(t.auditDate[0]&&(t.auditStartDate=t.auditDate[0].format("YYYY-MM-DD")),t.auditDate[1]&&(t.auditEndDate=t.auditDate[1].format("YYYY-MM-DD"))),t.akb020=this.akb020Params,t.aaz217=this.aaz217Params,t.showRecord=this.showRecord,t.queryFlag="program",t},fnQuery:function(){var t=this;this.$nextTick((function(){t.$refs.infoPageRef.loadData()}))},exportExcel:function(){var t,e=this,a=this.infoPageParams(),i=[],o=this.infoColumns.filter((function(t){return!(t.type&&"seq"===t.type||"operate"===t.field)})),s=(this.infoTableData,(0,r.Z)(o));try{for(s.s();!(t=s.n()).done;){var l=t.value;i.push({header:l.title,key:l.field,width:20})}}catch(n){s.e(n)}finally{s.f()}this.Base.submit(null,{url:"dischargeClinic/exportViolationPatient",data:a,autoValid:!1},{successCallback:function(t){var a=t.data.data;a.forEach((function(t){t.diagnoses&&Object.assign(t,t.diagnoses),t.ape805&&t.akc225&&(t.ape804=t.ape805*t.akc225)}));var o={fileName:"出院患者审核查询表",sheets:[{name:"worksheet1",column:{complex:!1,columns:i},rows:a,codeList:[]}]};e.Base.generateExcel(o)},failCallback:function(t){e.$message.error("医师数据加载失败")}})},fnCustomHeaderCell:function(t){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px",fontWeight:"bold"}}},fnSearch:function(t){var e=this.baseInfoForm.getFieldValue("flag");this.Base.openTabMenu({id:t.ake001,name:"【"+t.aac003+"】患者详情",url:"detailsQuery.html#/expenseDetails?akb020=".concat(t.akb020,"&akc190=").concat(t.akc190,"&aaz217=").concat(t.aaz217,"&ake001=").concat(t.ake001,"&flag=")+e,refresh:!1})}}},b=p,g=a(1001),C=(0,g.Z)(b,i,o,!1,null,"56f08776",null),v=C.exports},36766:function(t,e,a){"use strict";var i=a(66586);e["Z"]=i.Z},26263:function(t,e,a){"use strict";a.d(e,{s:function(){return i},x:function(){return o}});var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},o=[]},66586:function(t,e){"use strict";var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};e["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}},55067:function(t,e,a){"use strict";a(95082),a(95278)},83231:function(t,e,a){"use strict";var i=a(48534);a(36133);function o(t){return t?t.akb020&&t.aaz263?"personal":t.akb020&&t.aaz309&&t.aaz307?"group":t.akb020&&t.aaz307?"department":t.akb020?"hospital":"all":"all"}function r(t){return s.apply(this,arguments)}function s(){return s=(0,i.Z)(regeneratorRuntime.mark((function t(e){var a,i,r,s,l,n,u,c,f,d,m,h;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Base.submit(null,{url:"/miimCommonRead/queryDataPermission",showPageLoading:!1,data:{jobnumber:e},autoValid:!0});case 2:return a=t.sent,i=new Set,r=new Set,a.data.permission.forEach((function(t){var e=o(t);"hospital"===e&&i.add(t.akb020),"department"===e&&r.add(t.aaz307)})),s=a.data.permission.filter((function(t){return"department"===o(t)||!r.has(t.aaz307)})).filter((function(t){return"hospital"===o(t)||!i.has(t.akb020)})),l=new Set(s.map((function(t){return t.akb020})).filter((function(t){return null!=t}))),n=new Set(s.map((function(t){return t.aaz307})).filter((function(t){return null!=t}))),u=new Set(s.map((function(t){return t.aaz309})).filter((function(t){return null!=t}))),c=new Set(s.map((function(t){return t.aaz263})).filter((function(t){return null!=t}))),f=!1,d=!1,m=!1,h=!1,1===l.size&&(f=!0),1===n.size&&1===l.size&&(d=!0),1===n.size&&1===l.size&&1===u.size&&(m=!0),1===l.size&&0===n.size&&1===c.size&&(h=!0),t.abrupt("return",{akb020Set:l,aaz307Set:n,aaz263Set:c,aaz309Set:u,akb020Disable:f,aaz307Disable:d,aaz263Disable:h,aaz309Disable:m});case 20:case"end":return t.stop()}}),t)}))),s.apply(this,arguments)}function l(t,e){Base.submit(null,{url:"miimCommonRead/getAa01AAE500StartStop",data:t},{successCallback:function(t){return e(t)}})}function n(t,e){Base.submit(null,{url:"miimCommonRead/insertTableColumShow",data:t,autoValid:!1},{successCallback:function(t){return e("success")},failCallback:function(t){return e("fail")}})}var u={zbx01:{footer:function(){return"统筹金总额"}},zbx02:{footer:function(){return"基金支出总额"}},zbx03:{footer:function(){return"总费用"}},zbx04:{footer:function(){return"总住院日"}}};e["Z"]={permissionCheck:r,getAa01AAE500StartStop:l,insertTableColumShow:n,props:u,moneyNumFormat:function(t){var e=t.cellValue;return e||"—"}}},55382:function(){},61219:function(){}}]);
(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[1134],{88412:function(t,e,a){"use strict";var i=a(26263),n=a(36766),l=a(1001),r=(0,l.Z)(n.Z,i.s,i.x,!1,null,"5e7ef0ae",null);e["Z"]=r.exports},40531:function(t,e,a){"use strict";a.r(e),a.d(e,{default:function(){return w}});var i=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("div",{staticClass:"fit"},[i("ta-border-layout",{attrs:{"footer-cfg":{showBorder:!1},layout:{header:"110px",footer:"0px"},showPadding:!0}},[i("div",{attrs:{slot:"header"},slot:"header"},[i("ta-title",{attrs:{title:"查询条件"}}),i("ta-form",{staticStyle:{"padding-top":"5px"},attrs:{autoFormCreate:function(e){t.baseInfoForm=e},col:2,formLayout:!0}},[i("ta-form-item",{attrs:{fieldDecoratorOptions:{rules:[{required:!0,message:"必须选取日期"}]},"init-value":e.defaultValue,"label-col":{span:8},required:!0,span:4,"wrapper-col":{span:16},fieldDecoratorId:"allDate",label:"审核时间"}},[i("ta-date-picker",[i("ta-icon",{attrs:{slot:"suffixIcon",type:""},slot:"suffixIcon"})],1)],1),i("ta-form-item",{attrs:{"label-col":{span:8},span:4,"wrapper-col":{span:16},"field-decorator-id":"ape800",label:"违规等级"}},[i("ta-select",{attrs:{allowClear:"",placeholder:"违规等级筛选"},on:{change:e.handleChange}},[i("ta-select-option",{attrs:{value:"1"}},[e._v("重度可疑")]),i("ta-select-option",{attrs:{value:"2"}},[e._v("明确违规")]),i("ta-select-option",{attrs:{value:"3"}},[e._v("轻度可疑")])],1)],1),i("ta-form-item",{attrs:{"label-col":{span:8},span:4,"wrapper-col":{span:16},"field-decorator-id":"akb020",label:"院区标识"}},[i("ta-select",{attrs:{options:e.akb020List,disabled:e.paramsDisable.akb020,allowClear:"",placeholder:"院区选择"}})],1),i("ta-form-item",{attrs:{"label-col":{span:8},span:4,"wrapper-col":{span:16},"field-decorator-id":"aaz319",label:"规则名称"}},[i("ta-input",{attrs:{placeholder:"请输入规则名称"}})],1),i("ta-form-item",{attrs:{span:4,"label-col":{span:8},"wrapper-col":{span:16},fieldDecoratorId:"result",label:"预审结果","init-value":e.resultInit}},[i("ta-select",{attrs:{showSearch:"",mode:"multiple","collection-type":"APE800","collection-filter":"0",maxTagCount:1,placeholder:"请选择",allowClear:""}})],1),i("ta-form-item",{attrs:{"label-col":{span:8},span:4,"wrapper-col":{span:16},label:" "}},[i("ta-button",{attrs:{icon:"search",type:"primary"},on:{click:e.fnQuery}},[e._v("查询")]),i("ta-button",{attrs:{icon:"redo",disabled:e.paramsDisable.akb020,type:"default"},on:{click:e.fnReset}},[e._v("重置")])],1)],1)],1),i("div",{staticClass:"fit",staticStyle:{height:"88%"}},[i("ta-title",{staticStyle:{width:"50%"},attrs:{title:"查询结果"}}),i("ta-big-table",{ref:"Table",attrs:{"control-column":e.showHiddenOrSortColumn,data:e.userList,"footer-method":e.footerMethod,"tooltip-config":{enabled:!0,contentMethod:e.showTooltipMethod},"auto-resize":"",border:"","empty-text":"-",height:"100%","highlight-current-row":"","highlight-hover-row":"",resizable:"","show-footer":"","show-overflow":""},on:{"cell-click":e.cellClickEvent}},[i("ta-big-table-column",{attrs:{align:"center","header-align":"center","min-width":"50",sortable:"",title:"序号",type:"seq"}}),i("ta-big-table-column",{attrs:{visible:!1,align:"left",field:"aae386","header-align":"center","min-width":"180",title:"开单科室"}}),i("ta-big-table-column",{attrs:{visible:!1,align:"left",field:"aac003","header-align":"center","min-width":"180",title:"开单医师"}}),i("ta-big-table-column",{attrs:{visible:!1,align:"left",field:"name","header-align":"center","min-width":"150",title:"患者姓名"}}),i("ta-big-table-column",{attrs:{align:"left",field:"gzfl","header-align":"center","min-width":"180",title:"规则分类"}}),i("ta-big-table-column",{attrs:{align:"left",field:"aaa167","header-align":"center","min-width":"300",title:"规则名称"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("ta-button",{attrs:{size:"small",type:"link"}},[i("span",{staticStyle:{color:"#00bcff","text-decoration":"underline"}},[e._v(e._s(a.aaa167))])])]}}])}),i("ta-big-table-column",{attrs:{visible:!1,align:"left",field:"ake002","header-align":"center","min-width":"300",title:"医保项目名称"}}),i("ta-big-table-column",{attrs:{visible:!1,align:"right",field:"shzl","header-align":"center","min-width":"140",title:"审核总次数"}}),i("ta-big-table-column",{attrs:{align:"right",field:"txzl","header-align":"center","min-width":"140",sortable:"",title:"违规次数(次)"}}),i("ta-big-table-column",{attrs:{align:"right",field:"sjzsl","header-align":"center","min-width":"140",sortable:"",title:"实收总数量"}}),i("ta-big-table-column",{attrs:{align:"right",field:"txsl","header-align":"center","min-width":"130",sortable:"",title:"违规数量"}}),i("ta-big-table-column",{attrs:{formatter:e.moneyFormat,align:"right",field:"txje","header-align":"center","min-width":"140",sortable:"",title:"违规金额(元)"}}),i("ta-big-table-column",{attrs:{align:"right",field:"jxxms","header-align":"center","min-width":"150",sortable:"",title:"继续使用次数(次)"}}),i("ta-big-table-column",{attrs:{formatter:e.ratioFormat,align:"right",field:"jxxmzb","header-align":"center","min-width":"180",sortable:"",title:"继续使用次数占比"}}),i("ta-big-table-column",{attrs:{align:"right",field:"qxxms","header-align":"center","min-width":"130",sortable:"",title:"取消次数(次)"}}),i("ta-big-table-column",{attrs:{formatter:e.ratioFormat,align:"right",field:"qxxmzb","header-align":"center","min-width":"150",sortable:"",title:"取消次数占比"}}),i("ta-big-table-column",{attrs:{align:"right",field:"zfxms","header-align":"center","min-width":"130",sortable:"",title:"自费次数(次)"}}),i("ta-big-table-column",{attrs:{formatter:e.ratioFormat,align:"right",field:"zfxmzb","header-align":"center","min-width":"150",sortable:"",title:"自费次数占比"}}),i("ta-big-table-column",{attrs:{align:"right",field:"wzcxms","header-align":"center","min-width":"140",sortable:"",title:"无操作次数(次)"}}),i("ta-big-table-column",{attrs:{formatter:e.ratioFormat,align:"right",field:"wczxmzb","header-align":"center","min-width":"160",sortable:"",title:"无操作次数占比"}}),i("template",{slot:"bottomBar"},[i("ta-button",{staticStyle:{float:"right","margin-top":"20px"},attrs:{icon:"download",type:"primary"},on:{click:e.exportTable}},[e._v("导出 ")]),i("ta-pagination",{ref:"gridPager",staticStyle:{float:"right","margin-top":"20px","margin-right":"10px"},attrs:{"data-source":e.userList,defaultPageSize:200,pageSizeOptions:["30","50","100","200","500"],params:e.infoPageParams,url:"reportStatistics/queryListNight"},on:{"update:dataSource":function(t){e.userList=t},"update:data-source":function(t){e.userList=t}}})],1)],2)],1)])],1)},n=[],l=a(66347),r=a(48534),o=a(95082),s=(a(36133),a(88412)),u=a(36797),c=a.n(u),f=a(22722),m=a(55115),d=a(18671),h=a(92566),b=a(83231);m.w3.prototype.Base=Object.assign(m.w3.prototype.Base,(0,o.Z)({},f.Z));var p={name:"nightStatisticsGZ",components:{TaTitle:s.Z},data:function(){return{userList:[],amountData:[],resultInit:[],defaultValue:TaUtils.getMoment((new Date).toISOString().slice(0,10),"YYYY-MM-DD"),akb020:"",akb020List:[],permissions:{},paramsDisable:{akb020:!1},jxzb:"",showHiddenOrSortColumn:{themeColor:"red",icon:"appstore",showHiddenConfig:{open:!0,disabledHiddenColumns:function(t){return!1}},columnSortConfig:{open:!0,onMove:function(t,e){e.dragColumn,e.dropColumn;return!0},dragEnd:function(t,e){e.dragColumn,e.resultColumns}},successCallback:this.fnSaveTableTitle,disabledControlCol:function(t){}},txl:""}},created:function(){var t=this;return(0,r.Z)(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,b.Z.permissionCheck();case 2:return t.permissions=e.sent,e.next=5,t.$nextTick((function(){t.fnQueryTableTitle()}));case 5:case"end":return e.stop()}}),e)})))()},mounted:function(){var t=this;if(this.fnQueryHos(),this.$route.query.params){var e=JSON.parse(this.$route.query.params),a=new Date(e.allDate);a.setDate(a.getDate()+1),e.allDate=this.Base.getMoment(a.toISOString().slice(0,10),"YYYY-MM-DD"),this.$route.query.aaz307&&(e.aaz307=this.$route.query.aaz307),this.$route.query.aaz263&&(e.aaz263=this.$route.query.aaz263),this.$route.query.ake002&&(e.projectInfo=this.$route.query.ake002),this.baseInfoForm.setFieldsValue(e)}this.$nextTick((function(){t.fnQueryAa01()}))},methods:{moment:c(),fnQueryTableTitle:function(){var t=this,e=top.indexTool.getActiveTabMenuId(),a=top.indexTool.getUserInfo().loginId,i=[];this.Base.submit(null,{url:"miimCommonRead/selectTableColumShow",data:{resourceid:e,loginid:a},autoValid:!1},{successCallback:function(e){if(e.data.list.length>0&&e.data.list[0].colum){var a=JSON.parse(e.data.list[0].colum),n=t.$refs.Table.getTableColumn().fullColumn;i=a.map((function(t){return t.title}));var l=[];n.forEach((function(t){if("checkbox"==t.type)l.push(t);else{var i=a.find((function(e){return e.title===t.title}));i&&(t.visible=i.visible,t.width=i.width);var n=t;n.sortable?n.minWidth=20*n.title.length+30:n.minWidth=20*n.title.length+10,"操作"==n.title&&(n.minWidth=150),"项目引导信息"==n.title&&(n.minWidth=300),e.data.akc191Title.length>0&&"akc191"===n.property&&(n.title=e.data.akc191Title[0].label),l.push(n)}})),i.length>0&&l.sort((function(t,e){return"序号"===t.title?-1:"序号"===e.title?1:i.indexOf(t.title)-i.indexOf(e.title)})),t.$refs.Table.loadColumn(l)}},failCallback:function(e){t.$message.error("查询表头失败")}})},fnSaveTableTitle:function(t){var e=this,a=(t.table,t.resultColumnsList),i=a,n=[];i.forEach((function(t){var e=t.title,a=t.visible;t.type;e&&n.push({title:e,visible:a})}));var l=top.indexTool.getActiveTabMenuId(),r=top.indexTool.getUserInfo().loginId,o={colum:JSON.stringify(n),flag:"column",resourceid:l,loginid:r};b.Z.insertTableColumShow(o,(function(t){"success"===t?e.$message.success("保存成功"):e.$message.error("保存失败")}))},fnQueryAa01:function(){var t=this;this.Base.submit(null,{url:"miimCommonRead/getAa01Aaa005ByAaz499",data:{aaz499:206}},{successCallback:function(e){var a=e.data;t.resultInit="n"===a.aaa005.toLowerCase()?["1","2"]:["1","2","3"],t.fnQuery()},failCallback:function(e){t.$message.error("查询aa01表数据失败")}})},fnQueryHos:function(){var t=this,e={url:"/miimCommonRead/queryHospRpcList",showPageLoading:!1,autoValid:!0},a={successCallback:function(e){t.akb020List=e.data.resultData,t.permissions&&t.permissions.akb020Set.size>0&&(t.paramsDisable.akb020=!0,t.akb020List=t.akb020List.filter((function(e){return t.permissions.akb020Set.has(e.value)})),t.baseInfoForm.setFieldsValue({akb020:t.akb020List[0].value})),t.akb020=t.akb020List[0].value},failCallback:function(e){t.$message.error("医院数据加载失败")}};this.Base.submit(null,e,a)},footerMethod:function(t){var e=this,a=t.columns;t.data;return[a.map((function(t,a){return 0===a?"合计":["txzl","sjzsl","txsl","txje","jxxms","qxxms","zfxms","wzcxms"].includes(t.property)?"txje"===t.property?e.formatAmount((0,h.Z)(e.amountData,t.property)):(0,h.Z)(e.amountData,t.property):null}))]},showTooltipMethod:function(t){var e=t.type,a=t.column,i=(t.row,t.items,t._columnIndex,a.property);if("header"===e)return d.Z.props[i]?d.Z.props[i][e]:""},handleChange:function(t){this.fnQuery()},cellClickEvent:function(t){var e=t.row,a=t.column,i=a.property;if("aaa167"===i){var n=this.baseInfoForm.getFieldsValue();n.flag="gz";var l="日审规则项目汇总",r="reportStatistics.html#/nightStatisticsXZ?aaa167=".concat(e.aaa167,"&params=").concat(JSON.stringify(n));this.Base.openTabMenu({id:Math.floor(9e3*Math.random()),name:l,url:r,refresh:!1})}},exportTable:function(){var t=this,e=this.baseInfoForm.getFieldsValue();if(void 0===e.allDate||e.allDate.length<2)this.$message.error("请选择时间范围！");else{e=this.infoPageParams();var a,i=[],n=this.$refs.Table.getColumns(),r=(0,l.Z)(n);try{for(r.s();!(a=r.n()).done;){var o=a.value;"序号"!==o.title&&i.push({header:o.title,key:o.property,width:20})}}catch(s){r.e(s)}finally{r.f()}this.Base.submit(null,{url:"reportStatistics/exportExcel",data:e,autoValid:!1},{successCallback:function(e){var a=e.data.data,n={fileName:"日审规则明细汇总统计结果表",sheets:[{name:"worksheet1",column:{complex:!1,columns:i},rows:a,codeList:d.Z.codelist2}]};t.Base.generateExcel(n)},failCallback:function(e){t.$message.error("数据加载失败")}})}},formatAmount:function(t){return t.toFixed(2).replace(/\d(?=(\d{3})+\.)/g,"$&,")},moneyFormat:function(t){var e=t.cellValue;return e&&0!=e?this.formatAmount(e):"0"},ratioFormat:function(t){var e=t.cellValue;return e&&0!=e?e+"%":"0"},fnReset:function(){this.baseInfoForm.resetFields(),this.txl="",this.jxzb="",this.fnQuery()},infoPageParams:function(){var t=this.baseInfoForm.getFieldsValue();return t.startDate=t.allDate.format("YYYYMMDD"),t.endDate=t.allDate.format("YYYYMMDD"),t.aae500="6",t.flag="gz",t},fnQuery:function(){var t=this,e=this.baseInfoForm.getFieldValue("allDate");e?this.$nextTick((function(){t.$refs.gridPager.loadData((function(e){t.amountData=e.data.data}))})):this.$message.error("请选择时间范围！")},fnCustomHeaderCell:function(t){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px",fontWeight:"bold"}}},filterOption:function(t,e){return e.componentOptions.children[0].text.indexOf(t)>=0}}},g=p,v=a(1001),x=(0,v.Z)(g,i,n,!1,null,"141ab1ab",null),w=x.exports},36766:function(t,e,a){"use strict";var i=a(66586);e["Z"]=i.Z},26263:function(t,e,a){"use strict";a.d(e,{s:function(){return i},x:function(){return n}});var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},n=[]},66586:function(t,e){"use strict";var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};e["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}},83231:function(t,e,a){"use strict";var i=a(48534);a(36133);function n(t){return t?t.akb020&&t.aaz263?"personal":t.akb020&&t.aaz309&&t.aaz307?"group":t.akb020&&t.aaz307?"department":t.akb020?"hospital":"all":"all"}function l(t){return r.apply(this,arguments)}function r(){return r=(0,i.Z)(regeneratorRuntime.mark((function t(e){var a,i,l,r,o,s,u,c,f,m,d,h;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Base.submit(null,{url:"/miimCommonRead/queryDataPermission",showPageLoading:!1,data:{jobnumber:e},autoValid:!0});case 2:return a=t.sent,i=new Set,l=new Set,a.data.permission.forEach((function(t){var e=n(t);"hospital"===e&&i.add(t.akb020),"department"===e&&l.add(t.aaz307)})),r=a.data.permission.filter((function(t){return"department"===n(t)||!l.has(t.aaz307)})).filter((function(t){return"hospital"===n(t)||!i.has(t.akb020)})),o=new Set(r.map((function(t){return t.akb020})).filter((function(t){return null!=t}))),s=new Set(r.map((function(t){return t.aaz307})).filter((function(t){return null!=t}))),u=new Set(r.map((function(t){return t.aaz309})).filter((function(t){return null!=t}))),c=new Set(r.map((function(t){return t.aaz263})).filter((function(t){return null!=t}))),f=!1,m=!1,d=!1,h=!1,1===o.size&&(f=!0),1===s.size&&1===o.size&&(m=!0),1===s.size&&1===o.size&&1===u.size&&(d=!0),1===o.size&&0===s.size&&1===c.size&&(h=!0),t.abrupt("return",{akb020Set:o,aaz307Set:s,aaz263Set:c,aaz309Set:u,akb020Disable:f,aaz307Disable:m,aaz263Disable:h,aaz309Disable:d});case 20:case"end":return t.stop()}}),t)}))),r.apply(this,arguments)}function o(t,e){Base.submit(null,{url:"miimCommonRead/getAa01AAE500StartStop",data:t},{successCallback:function(t){return e(t)}})}function s(t,e){Base.submit(null,{url:"miimCommonRead/insertTableColumShow",data:t,autoValid:!1},{successCallback:function(t){return e("success")},failCallback:function(t){return e("fail")}})}var u={zbx01:{footer:function(){return"统筹金总额"}},zbx02:{footer:function(){return"基金支出总额"}},zbx03:{footer:function(){return"总费用"}},zbx04:{footer:function(){return"总住院日"}}};e["Z"]={permissionCheck:l,getAa01AAE500StartStop:o,insertTableColumShow:s,props:u,moneyNumFormat:function(t){var e=t.cellValue;return e||"—"}}},18671:function(t,e){"use strict";var a=[{columnKey:"jxxmzb",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}},{columnKey:"txl",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}},{columnKey:"qxxmzb",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}},{columnKey:"zfxmzb",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}},{columnKey:"wczxmzb",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}}],i=[{columnKey:"jxxmzb",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}},{columnKey:"qxxmzb",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}},{columnKey:"zfxmzb",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}},{columnKey:"wczxmzb",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}}],n={txl:{header:function(){return"提醒率=提醒总次数/审核总次数"}},jxxmzb:{header:function(){return"继续使用占比=继续使用次数(次)/提醒总次数"}},qxxmzb:{header:function(){return"取消使用占比=取消使用次数/提醒总次数"}},zfxmzb:{header:function(){return"自费使用占比=自费使用次数/提醒总次数"}},wczxmzb:{header:function(){return"无操作使用占比=无操作使用次数/提醒总次数"}}},l=new Map([["1","门诊审核"],["0","门特审核"],["2","医嘱审核"],["3","计费审核"]]);e["Z"]={codelist:a,codelist2:i,props:n,optionsMap:l}},55382:function(){},61219:function(){}}]);
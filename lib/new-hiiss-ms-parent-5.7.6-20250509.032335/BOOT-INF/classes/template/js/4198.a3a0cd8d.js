"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[4198],{14198:function(e,t,a){a.r(t),a.d(t,{default:function(){return b}});for(var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("ta-border-layout",{attrs:{"layout-type":"fixTop"}},[a("div",{staticStyle:{padding:"10px",background:"#fff","text-align":"center"},attrs:{slot:"header"},slot:"header"},[a("ta-input-search",{staticClass:"search-box",staticStyle:{width:"40%"},attrs:{placeholder:"请输入人员姓名","enter-button":"搜索"}})],1),a("ta-card",{staticClass:"fit"},[a("div",{attrs:{slot:"extra"},slot:"extra"},[a("ta-button",{on:{click:e.insertEvent}},[e._v(" 新增 ")]),a("ta-button",{on:{click:e.getInsertEvent}},[e._v(" 获取新增 ")]),a("ta-button",{on:{click:e.getSelectEvent}},[e._v(" 获取选中 ")]),a("ta-button",{on:{click:function(t){return e.$refs.xTable.removeCheckboxRow()}}},[e._v(" 删除选中 ")]),a("ta-button",{on:{click:e.getRemoveEvent}},[e._v(" 获取删除 ")]),a("ta-button",{on:{click:e.getUpdateEvent}},[e._v(" 获取修改 ")]),a("ta-button",{on:{click:e.validEvent}},[e._v(" 校验 ")]),a("ta-button",{on:{click:e.fullValidEvent}},[e._v(" 完整校验 ")]),a("ta-button",{on:{click:e.validAllEvent}},[e._v(" 全量校验 ")]),a("ta-button",{on:{click:e.selectValidEvent}},[e._v(" 选中校验 ")]),a("ta-popconfirm",{attrs:{title:"确定还原数据吗?","ok-text":"确定","cancel-text":"取消"},on:{confirm:e.revertEvent}},[a("ta-button",[e._v("还原数据")])],1)],1),a("ta-big-table",{ref:"xTable",attrs:{border:"",height:"auto",resizable:"","auto-resize":"","show-overflow":"","keep-source":"",data:e.tableData,"edit-rules":e.validRules,"edit-config":{trigger:"click",mode:"row",showStatus:!0}}},[a("ta-big-table-column",{attrs:{type:"checkbox",width:"60",fixed:"left"}}),a("ta-big-table-column",{attrs:{type:"seq",width:"60",fixed:"left"}}),a("ta-big-table-column",{attrs:{field:"name",title:"Name",width:"150","edit-render":{name:"$input"}}}),a("ta-big-table-column",{attrs:{field:"age",title:"Age",width:"150","edit-render":{name:"$input-number",renderCell:e.renderDefaultAge,style:{color:"red",width:"100%"},props:{min:0}}}}),a("ta-big-table-column",{attrs:{field:"sex",title:"Sex",width:"150","collection-type":"SEX","edit-render":{name:"$select"}}}),a("ta-big-table-column",{attrs:{field:"hobby",width:"220px",title:"多选下拉","edit-render":{name:"$select",props:{maxTagCount:2,mode:"multiple",options:e.hobbyList}}}}),a("ta-big-table-column",{attrs:{field:"date",title:"Date",width:"150","edit-render":{name:"$date-picker",props:{format:"YYYY-MM-DD"}}}}),a("ta-big-table-column",{attrs:{field:"time",title:"Time",width:"150","edit-render":{name:"$time-picker",props:{format:"HH:mm:ss"}}}}),a("ta-big-table-column",{attrs:{field:"month",title:"Month",width:"150","edit-render":{name:"$month-picker",props:{format:"YYYY-MM"}}}}),a("ta-big-table-column",{attrs:{field:"range",title:"Range",width:"250","edit-render":{name:"$range-picker",props:{format:"YYYY-MM-DD"}}}}),a("ta-big-table-column",{attrs:{field:"week",title:"Week",width:"150","edit-render":{name:"$week-picker",props:{format:"YYYY年第ww周"}}}}),a("ta-big-table-column",{attrs:{field:"quarter",title:"Quarter",width:"150","edit-render":{name:"$quarter-picker",props:{format:"YYYY年Q季度"}}}}),a("ta-big-table-column",{attrs:{field:"year",title:"Year",width:"150","edit-render":{name:"$year-picker",props:{format:"YYYY年"}}}}),a("ta-big-table-column",{attrs:{field:"rate",title:"Rate",width:"200","edit-render":{name:"$rate"}}}),a("ta-big-table-column",{attrs:{field:"switch",title:"Switch",width:"150","edit-render":{name:"$switch",trueText:"有效",falseText:"无效"}}}),a("ta-big-table-column",{attrs:{field:"checkbox",title:"Checkbox",width:"150","edit-render":{name:"$checkbox"}}}),a("ta-big-table-column",{attrs:{field:"radio",title:"Radio",width:"150","edit-render":{name:"$radio",props:{cancelChecked:!0}}}}),a("ta-big-table-column",{attrs:{field:"radioGroup",title:"RadioGroup",width:"320","edit-render":{name:"$radio-group",props:{options:e.hobbyList}}}}),a("ta-big-table-column",{attrs:{field:"checkboxGroup",title:"CheckboxGroup",width:"300","edit-render":{name:"$checkbox-group",props:{options:e.hobbyList}}}}),a("ta-big-table-column",{attrs:{field:"cascader",title:"Cascader",width:"300","edit-render":{name:"$cascader",props:{options:e.addressList}}}}),a("ta-big-table-column",{attrs:{field:"treeSelect",title:"TreeSelect",width:"300","edit-render":{name:"$tree-select",showPath:!0,props:{treeData:e.addressList}}}}),a("ta-big-table-column",{attrs:{field:"addresses",title:"TreeSelect多选",width:"300","edit-render":{name:"$tree-select",props:{multiple:!0,maxTagCount:2,treeData:e.addressList}}}}),a("div",{attrs:{slot:"bottomBar"},slot:"bottomBar"},[a("ta-button",{on:{click:e.fnSubmit}},[e._v(" 提交修改 ")])],1)],1)],1)],1)},i=[],r=[{label:"足球",value:0},{label:"篮球",value:1},{label:"排球",value:2},{label:"乒乓球",value:3}],l=[{value:"zhejiang",label:"浙江",children:[{value:"hangzhou",label:"杭州",children:[{value:"xihu",label:"西湖"}]}]},{value:"jiangsu",label:"江苏",children:[{value:"nanjing",label:"南京",children:[{value:"zhonghuamen",label:"中华门"}]}]}],o=[],s=0;s<40;s++)o.push({key:s.toString(),name:"Name ".concat(s),age:s,sex:"".concat(s%3),hobby:[s%4],date:"2019-05-10",month:"2019-05-10",range:["2019-05-10","2019-05-19"],week:"2019-05-10",rate:s%6,switch:Boolean(s%2),checkbox:Boolean(s%2),radio:Boolean(s%2),address:["jiangsu","nanjing","zhonghuamen"],treeSelect:"nanjing",addresses:["jiangsu","nanjing","zhonghuamen"],cascader:["jiangsu","nanjing","zhonghuamen"],quarter:"2019-05-10",year:"2019-05-10",radioGroup:s%4,checkboxGroup:[s%4],time:"2019-05-10 09:50:50"});var c={name:"TableEdit",data:function(){var e=function(e){var t=e.cellValue;if(t&&(t.length<3||t.length>50))return new Error("名称长度在 3 到 50 个字符之间")};return{tableData:o,hobbyList:r,addressList:l,validRules:{name:[{required:!0,message:"姓名必须填写"},{validator:e}]}}},methods:{renderDefaultAge:function(e,t,a){var n=a.cellValue;return n+" 岁"},insertEvent:function(){var e=this;this.$refs.xTable.insert([{name:"这个是新增的行XXX "}]).then((function(t){e.$refs.xTable.validate(t.rows).catch((function(t){t&&e.$message.error("新添加的行校验不通过！(可自定义是否校验)")}))}))},getInsertEvent:function(){var e=this.$refs.xTable.getInsertRecords();this.$message.info(e.length)},getSelectEvent:function(){var e=this.$refs.xTable.getCheckboxRecords();this.$message.info(e.length)},getRemoveEvent:function(){var e=this.$refs.xTable.getRemoveRecords();this.$message.info(e.length)},getUpdateEvent:function(){var e=this.$refs.xTable.getUpdateRecords();this.$message.info(e.length)},validEvent:function(){var e=this;this.$refs.xTable.validate().then((function(){e.$message.success("校验成功！")})).catch((function(t){t&&e.$message.error("校验不通过！")}))},fullValidEvent:function(){var e=this;this.$refs.xTable.fullValidate().then((function(){e.$message.success("校验成功！")})).catch((function(t){if(t){var a=[];Object.values(t).forEach((function(e){e.forEach((function(e){var t=e.rowIndex,n=e.column,i=e.rules;i.forEach((function(e){t<0?a.push("新增行".concat(n.title," 校验错误：").concat(e.message)):a.push("第 ".concat(t+1," 行 ").concat(n.title," 校验错误：").concat(e.message))}))}))})),e.$message.error(a.join())}}))},validAllEvent:function(){var e=this;this.$refs.xTable.validate(!0).then((function(){e.$message.success("校验成功！")})).catch((function(t){t&&e.$message.error("校验不通过！")}))},selectValidEvent:function(){var e=this,t=this.$refs.xTable.getCheckboxRecords();t.length>0?this.$refs.xTable.validate(t).then((function(){e.$message.success("校验成功！")})).catch((function(t){t&&e.$message.error("校验不通过！")})):this.$message.warning("未选中数据！")},revertEvent:function(){this.$refs.xTable.revertData()},fnSubmit:function(){}}},d=c,u=a(1001),h=(0,u.Z)(d,n,i,!1,null,null,null),b=h.exports}}]);
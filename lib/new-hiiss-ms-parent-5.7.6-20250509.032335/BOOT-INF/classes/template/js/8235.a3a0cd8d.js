"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[8235],{66411:function(t,n,i){i.r(n),i.d(n,{default:function(){return o}});var e=function(){var t=this,n=t.$createElement,i=t._self._c||n;return i("div",{staticClass:"fit"},[i("div",[i("ta-button",{attrs:{icon:"search",type:"primary"},on:{click:t.fnMsgSubscribe}},[t._v(" 消息订阅 ")]),i("ta-button",{attrs:{icon:"search",type:"primary"},on:{click:t.fnGetClientId}},[t._v(" 获取clientId ")]),i("ta-button",{attrs:{icon:"search",type:"primary"},on:{click:t.fnGetSysInfo}},[t._v(" 获取设备系统信息 ")]),i("ta-button",{attrs:{icon:"search",type:"primary"},on:{click:t.fnClearMsg}},[t._v(" 清除消息列表 ")]),i("ta-button",{attrs:{icon:"search",type:"primary"},on:{click:t.fnClientLogin}},[t._v(" 浏览器登录 ")])],1),i("div",[i("span",[t._v("clientId:")]),i("div",{staticStyle:{width:"500px",height:"50px",border:"1px solid #dddddd",padding:"20px","font-size":"20px"}},[t._v(" "+t._s(t.clientId)+" ")]),i("span",[t._v("userId:")]),i("ta-input",{model:{value:t.userId,callback:function(n){t.userId=n},expression:"userId"}})],1)])},c=[],s={name:"clientInfo",data:function(){return{clientId:"",userId:""}},methods:{fnMsgSubscribe:function(){QClient.DBPNSSubscribe("liuzj",(function(t){}))},fnGetClientId:function(){var t=this;QClient.DBPNSGetClientId((function(n){t.clientId=n}))},fnGetSysInfo:function(){var t=this;QClient.invokeMethod("DeviceSysInfo",(function(n){t.clientId=n}))},fnClearMsg:function(){QClient.DBPNSClearMsg(this.userId)},fnClientLogin:function(){QClient.DBPNSUserLogin(this.userId)}}},r=s,a=i(1001),l=(0,a.Z)(r,e,c,!1,null,"7b858a50",null),o=l.exports}}]);
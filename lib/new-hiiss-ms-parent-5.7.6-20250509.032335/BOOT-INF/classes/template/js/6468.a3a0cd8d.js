(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[6468],{88412:function(e,t,a){"use strict";var i=a(26263),s=a(36766),n=a(1001),o=(0,n.Z)(s.Z,i.s,i.x,!1,null,"5e7ef0ae",null);t["Z"]=o.exports},54519:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return k}});var i=function(){var e=this,t=this,a=t.$createElement,i=t._self._c||a;return i("div",{staticClass:"fit"},[i("ta-border-layout",{staticStyle:{},attrs:{"layout-type":"fixTop"}},[i("div",{staticStyle:{"padding-top":"10px"},attrs:{slot:"header"},slot:"header"},[i("div",{staticClass:"query-header"},[i("div",{ref:"formBox",staticClass:"query-header-left",style:t.formBoxStyle},[i("ta-form",{attrs:{autoFormCreate:function(t){e.baseInfoForm=t},layout:"horizontal","label-width":"80px",formLayout:!0}},[i("ta-form-item",{attrs:{span:3,labelCol:{span:0},wrapperCol:{span:24}}},[i("ta-button",{attrs:{size:"small"},on:{click:function(e){return t.trunTime("1")}}},[t._v("近三月")]),i("ta-button",{attrs:{size:"small"},on:{click:function(e){return t.trunTime("2")}}},[t._v("近六月")]),i("ta-button",{attrs:{size:"small"},on:{click:function(e){return t.trunTime("3")}}},[t._v("近一年")])],1),i("ta-form-item",{attrs:{fieldDecoratorId:"allDate",span:5,label:"结算时间","init-value":t.rangeValue,fieldDecoratorOptions:{rules:[{required:!0,message:"必须选取日期"}]}}},[i("ta-range-picker",{attrs:{"allow-one":!0,type:"month"}})],1),i("ta-form-item",{attrs:{"field-decorator-id":"aka130",label:"医疗类别",span:5}},[i("ta-select",{attrs:{placeholder:"医疗类别筛选",allowClear:""}},[i("ta-select-option",{attrs:{value:"1"}},[t._v("门诊")]),i("ta-select-option",{attrs:{value:"2"}},[t._v("住院")])],1)],1),i("ta-form-item",{attrs:{"field-decorator-id":"statisticsDimension",label:"统计维度",span:5}},[i("ta-select",{attrs:{placeholder:"统计维度筛选",maxTagCount:5,allowClear:"",mode:"multiple",options:t.statisticsDimension},on:{change:t.fnChangeDimension}})],1),i("ta-form-item",{attrs:{"field-decorator-id":"aae140",label:"人群类别",span:5}},[i("ta-select",{attrs:{placeholder:"人群类别筛选",allowClear:"",collectionType:"AAE140"}})],1),i("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:"ks"===t.dimensionflag,expression:"dimensionflag === 'ks'"}],attrs:{"field-decorator-id":"aaz307",label:"科室",span:4,disabled:t.paramsDisable.aaz307}},[i("ta-select",{attrs:{showSearch:"","show-search":!0,allowClear:"",placeholder:"科室筛选",options:t.ksList}})],1),i("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:"ys"===t.dimensionflag,expression:"dimensionflag === 'ys'"}],attrs:{"field-decorator-id":"aaz263",label:"医师名称",span:4,disabled:t.paramsDisable.aaz263}},[i("ta-select",{attrs:{showSearch:"",optionLabelProp:"children",mode:"combobox",placeholder:"医生名称筛选",options:t.doctorList,filterOption:t.filterOption,allowClear:""}})],1),i("ta-form-item",{attrs:{"field-decorator-id":"statisticsIndex",label:"统计指标",span:12,required:!0,fieldDecoratorOptions:{rules:[{validator:t.customValidate}]}}},[i("ta-checkbox-group",{attrs:{defaultValue:t.defaultCheckedIndex,options:t.statisticsIndex},on:{change:t.fnIndexChange}})],1),i("div",{staticStyle:{display:"flex","margin-left":"20px",float:"right"}},[i("ta-button",{attrs:{type:"primary"},on:{click:t.fnQuery}},[t._v("查询")]),i("ta-button",{staticStyle:{"margin-left":"10px"},on:{click:t.showModal}},[t._v("高级查询")]),i("ta-button",{staticStyle:{"margin-left":"10px"},on:{click:t.fnReSet}},[t._v("重置")]),i("ta-button",{staticStyle:{"margin-left":"10px"},on:{click:t.fnExportExcel}},[t._v("导出")])],1)],1)],1)])]),i("div",[i("ta-modal",{attrs:{height:"400px",width:"800px"},on:{ok:t.handleOk},model:{value:t.modalVisible,callback:function(e){t.modalVisible=e},expression:"modalVisible"}},[i("div",{ref:"formBox"},[i("ta-form-model",t._b({ref:"dynamicValidateForm",attrs:{model:t.dynamicValidateForm}},"ta-form-model",t.formItemLayoutWithOutLabel,!1),[t._l(t.dynamicValidateForm.domains,(function(e,a){return i("ta-form-model-item",t._b({key:e.key,attrs:{label:0===a?"选择指标":" ",prop:"domains."+a+".value"}},"ta-form-model-item",0===a?t.formItemLayout:t.formItemLayout2,!1),[i("ta-form",{attrs:{autoFormCreate:function(a){return t.autoFormCreate(e.value+e.key,a)},layout:"horizontal","label-width":"100px",formLayout:!0}},[a>0?i("ta-form-item",{attrs:{span:3,fieldDecoratorOptions:{rules:[{required:!0,message:"必选项"}]},wrapperCol:{span:24},fieldDecoratorId:"relation"}},[i("ta-select",{on:{change:t.relationChange}},[i("ta-select-option",{attrs:{value:"and"}},[t._v("且")]),i("ta-select-option",{attrs:{value:"or"}},[t._v("或")])],1)],1):t._e(),i("ta-form-item",{attrs:{span:7,wrapperCol:{span:24},fieldDecoratorOptions:{rules:[{required:!0,message:"必须选择指标"}]},fieldDecoratorId:"index"}},[i("ta-select",{attrs:{placeholder:"选择指标",allowClear:"",options:t.selectedIndexItemList}})],1),i("ta-form-item",{attrs:{span:4,wrapperCol:{span:24},"init-value":"gt",fieldDecoratorId:"compare"}},[i("ta-select",[i("ta-select-option",{attrs:{value:"lt"}},[t._v("小于")]),i("ta-select-option",{attrs:{value:"gt"}},[t._v("大于")])],1)],1),i("ta-form-item",{attrs:{span:7,wrapperCol:{span:24},"init-value":0,fieldDecoratorId:"number"}},[i("ta-input-number",{staticStyle:{width:"90%"},attrs:{step:.1}})],1),i("ta-form-item",{attrs:{span:2,wrapperCol:{span:24}}},[t.dynamicValidateForm.domains.length>1?i("ta-icon",{staticClass:"dynamic-delete-button",attrs:{type:"minus-circle-o",disabled:1===t.dynamicValidateForm.domains.length},on:{click:function(a){return t.removeDomain(e)}}}):t._e()],1)],1)],1)})),i("ta-form-model-item",t._b({attrs:{label:" "}},"ta-form-model-item",t.formItemLayoutWithOutLabel,!1),[i("ta-button",{staticStyle:{width:"60%"},attrs:{type:"dashed"},on:{click:t.addDomain}},[i("ta-icon",{attrs:{type:"plus"}}),t._v(" 继续添加条件 ")],1)],1)],2)],1),i("template",{slot:"footer"},[i("div",{staticStyle:{display:"flex","justify-content":"center",width:"100%"}},[i("ta-button",{staticStyle:{"align-content":"center"},attrs:{type:"primary"},on:{click:t.fnHandelQuery}},[t._v("提交查询 ")])],1)])],2)],1),i("div",{staticClass:"fit content-box"},[i("ta-radio-group",{staticClass:"top-right-fixed",attrs:{size:"small"},on:{change:t.fnShowModeChange},model:{value:t.showMode,callback:function(e){t.showMode=e},expression:"showMode"}},[i("ta-tooltip",{staticStyle:{padding:"5px"},attrs:{placement:"top"}},[i("template",{slot:"title"},[i("span",{staticStyle:{"font-size":"12px"}},[t._v(" 说明:当统计维度中含医师、患者时，将隐藏同比、环比 ")])]),i("ta-icon",{attrs:{type:"question-circle"}})],2),i("ta-radio-button",{attrs:{value:"showTable"}},[i("ta-icon",{attrs:{type:"table"}})],1),i("ta-radio-button",{attrs:{value:"showCharts"}},[i("ta-icon",{attrs:{type:"area-chart"}})],1)],1),i("div",[i("div",{directives:[{name:"show",rawName:"v-show",value:"showCharts"===t.showMode,expression:"showMode==='showCharts'"}]},[i("ta-tabs",{attrs:{size:"small"},on:{change:t.fnIndexTabChange},model:{value:t.activeKey,callback:function(e){t.activeKey=e},expression:"activeKey"}},t._l(this.selectedIndexItemList,(function(e,a){return i("ta-tab-pane",{key:a,attrs:{tab:e.label,forceRender:!0}},[i("div",{staticStyle:{padding:"5px"}},[i("ta-row",{attrs:{gutter:16}},t._l(t.selectedDimensionItemList,(function(a){return i("ta-col",{attrs:{span:12}},["akb020"===a.value?i("ta-card",{attrs:{title:a.label,bordered:!0}},[i("hospital-bar",{ref:"hospitalBarRef",refInFor:!0,attrs:{paramData:t.parameters,dimensionItem:a,indexItem:e}})],1):t._e(),"aae386"===a.value?i("ta-card",{attrs:{title:a.label,bordered:!0}},[i("hospital-bar",{ref:"deptBarRef",refInFor:!0,attrs:{paramData:t.parameters,dimensionItem:a,indexItem:e}})],1):t._e(),"aaz570"===a.value?i("ta-card",{attrs:{title:a.label,bordered:!0}},[i("hospital-bar",{ref:"doctorBarRef",refInFor:!0,attrs:{paramData:t.parameters,dimensionItem:a,indexItem:e}})],1):t._e(),"aae043"===a.value?i("ta-card",{attrs:{title:a.label,bordered:!0}},[i("hospital-bar",{ref:"aae043BarRef",refInFor:!0,attrs:{paramData:t.parameters,dimensionItem:a,indexItem:e}})],1):t._e(),"aka130"===a.value?i("ta-card",{attrs:{title:a.label,bordered:!0}},[i("hospital-bar",{ref:"aka130BarRef",refInFor:!0,attrs:{paramData:t.parameters,dimensionItem:a,indexItem:e}})],1):t._e(),"aae140"===a.value?i("ta-card",{attrs:{title:a.label,bordered:!0}},[i("hospital-bar",{ref:"aae140BarRef",refInFor:!0,attrs:{paramData:t.parameters,dimensionItem:a,indexItem:e}})],1):t._e(),"aac003"===a.value?i("ta-card",{attrs:{title:a.label,bordered:!0}},[i("hospital-bar",{ref:"aac003BarRef",refInFor:!0,attrs:{paramData:t.parameters,dimensionItem:a,indexItem:e}})],1):t._e()],1)})),1)],1)])})),1)],1)]),i("div",{directives:[{name:"show",rawName:"v-show",value:"showTable"===t.showMode,expression:"showMode==='showTable'"}],staticClass:"content-table"},[i("ta-big-table",{ref:"infoTableRef",attrs:{columns:t.infoColumns,data:t.infoTableData,border:"","show-footer":"","tooltip-config":{enabled:!0,contentMethod:t.showTooltipMethod},height:"auto","highlight-hover-row":"","auto-resize":"","show-overflow":"",resizable:"","sort-config":t.sortConfig,"filter-config":t.filterConfig,"footer-method":t.footerMethod},on:{"sort-change":t.sortChangeEvent,"filter-change":t.filterChangeEvent}})],1),i("div",{directives:[{name:"show",rawName:"v-show",value:"showTable"===t.showMode,expression:"showMode==='showTable'"}],staticClass:"content-box-footer"},[i("ta-pagination",{ref:"infoPageRef",staticStyle:{position:"absolute",right:"10px",bottom:"6px"},attrs:{dataSource:t.infoTableData,params:t.infoPageParams,defaultPageSize:100,pageSizeOptions:["100","200","500","1000","1500"],url:"indexStatistics/queryData"},on:{"update:dataSource":function(e){t.infoTableData=e},"update:data-source":function(e){t.infoTableData=e}}})],1)],1)])],1)},s=[],n=a(66347),o=a(89584),r=a(48534),l=a(95082),c=(a(36133),a(88412)),u=a(83231),f=a(36797),h=a.n(f),d=a(22722),m=a(55115),p=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{staticClass:"chart-box",attrs:{id:this.indexItem.value+this.dimensionItem.value}})])},b=[],v=a(1708),y={name:"hospitalBar",components:{TaTitle:c.Z},props:{paramData:Object,indexItem:Object,dimensionItem:Object,type:String,url:String},data:function(){return{params:{},unit:"元",option:{},nodataOption:{graphic:{type:"text",left:"center",top:"middle",style:{text:"暂无数据",fontSize:20,fill:"#999"},z:100},series:[]}}},methods:{fnChartName:function(){var e;return e=["aae386","aaz570","aac003"].includes(this.dimensionItem.value)?"分布排名前十":"分布",this.indexItem.label+this.dimensionItem.label+e},changeUnit:function(){["zbx01","zbx02","zbx03"].includes(this.indexItem.value)?this.unit="元":"zbx04"==this.indexItem.value?this.unit="天":"zbx08"==this.indexItem.value?this.unit="%":this.unit="人次"},fnBuildChart:function(e){this.changeUnit();var t=this,a=v.init(document.getElementById(this.indexItem.value+this.dimensionItem.value));if(e&&0!==e.length){var i=e.reduce((function(e,t){return e+t.value}),0),s={title:{text:t.fnChartName(),x:"center"},tooltip:{trigger:"item",formatter:function(e){return e.marker+""+e.data.name+"</br>数量："+e.data.value+t.unit+"</br>占比："+e.percent+"%"}},toolbox:{show:!0,feature:{dataView:{readOnly:!1},myPie:{show:!0,title:"切换为饼状图",icon:"path://M512 98.304C282.624 98.304 98.304 282.624 98.304 512s184.32 413.696 413.696 413.696c229.376 0 413.696-184.32 413.696-413.696S741.376 98.304 512 98.304zM888.832 491.52l-331.776 0 233.472-233.472C847.872 323.584 884.736 401.408 888.832 491.52zM512 888.832c-208.896 0-376.832-167.936-376.832-376.832 0-208.896 167.936-376.832 376.832-376.832 98.304 0 184.32 36.864 253.952 98.304l-266.24 266.24c-4.096 4.096-4.096 8.192-4.096 12.288 0 12.288 8.192 20.48 20.48 20.48l376.832 0C876.544 729.088 712.704 888.832 512 888.832z",onclick:function(){t.fnBuildChart(e)}},myBar:{show:!0,title:"切换为状图",icon:"path://M163.84 880.64h122.88v-737.28h-122.88v737.28z m-40.96-778.24h204.8v819.2h-204.8v-819.2z m286.72 204.8h204.8v614.4h-204.8v-614.4z m40.96 573.44h122.88v-532.48h-122.88v532.48z m245.76-368.64h204.8v409.6h-204.8v-409.6z m40.96 368.64h122.88v-327.68h-122.88v327.68z",onclick:function(){t.fnBuildChartBar(e)}},myLine:{show:!0,title:"切换为折线图",icon:"path://M3.04761905,3.23733333 L3.04761905,12.952 L4.38209523,12.952 L13.5238095,12.951619 L13.5238095,14.0944762 L3.61904762,14.0944762 C2.67227377,14.0944762 1.90476191,13.3269643 1.90476191,12.3801905 L1.90476191,3.23733333 L3.04761905,3.23733333 Z M13.615619,2.47619047 L13.615619,6.47619047 L12.4727619,6.47619047 L12.4727619,4.49257142 L8.99619047,8.05866667 L7.89523809,6.91809523 L5.36342858,9.53980953 L4.54133333,8.74590477 L7.89485714,5.27238095 L9.00038095,6.41752381 L11.728,3.61904762 L9.89828572,3.61904762 L9.89828572,2.47619047 L13.615619,2.47619047 L13.615619,2.47619047 Z",onclick:function(){t.fnBuildChartLine(e)}},magicType:{show:!0},restore:{show:!0},saveAsImage:{name:t.fnChartName(),show:!0}}},legend:{type:"scroll",orient:"vertical",left:"39%",align:"left",top:"middle",icon:"circle",formatter:function(a){for(var s,n,o=0;o<e.length;o++)e[o].name===a&&(s=e[o].value,n=i>0?(s/i*100).toFixed(2):0);var r=["{a|"+a+"}","{b|"+s+t.unit+"}","{c|"+n+"%}"];return r.join(" ")},textStyle:{lineHeight:"30",rich:{a:{fontSize:14,color:"#444444",width:140},b:{fontSize:16,width:120,align:"right",color:"#333333",fontWeight:550},c:{fontSize:14,width:100,color:"#B7B7B7"}}}},series:[{name:t.fnChartName(),type:"pie",center:["20%","50%"],radius:["30%","50%"],avoidLabelOverlap:!1,color:["#CEF5D0","#CBE9FD","#9FCEFA","#70C87D","#57B8BB","#1890FF"],label:{show:!0,position:"center",fontSize:"25",color:"#323233",formatter:function(){return parseFloat(i.toFixed(2))}},emphasis:{label:{show:!0,fontSize:25}},labelLine:{show:!1},data:e}]};a.resize(),a.clear(),a.setOption(s,!0)}else a.setOption(this.nodataOption,!0)},fnBuildChartLine:function(e){this.changeUnit();var t=this,a=v.init(document.getElementById(this.indexItem.value+this.dimensionItem.value));if(e&&0!==e.length){var i={title:{text:t.fnChartName(),x:"center"},tooltip:{trigger:"axis",axisPointer:{type:"line"},formatter:"{b}:<br>{c} "+t.unit},grid:{left:"10%",right:"2%"},toolbox:{show:!0,feature:{dataView:{readOnly:!1},myPie:{show:!0,title:"切换为饼状图",icon:"path://M512 98.304C282.624 98.304 98.304 282.624 98.304 512s184.32 413.696 413.696 413.696c229.376 0 413.696-184.32 413.696-413.696S741.376 98.304 512 98.304zM888.832 491.52l-331.776 0 233.472-233.472C847.872 323.584 884.736 401.408 888.832 491.52zM512 888.832c-208.896 0-376.832-167.936-376.832-376.832 0-208.896 167.936-376.832 376.832-376.832 98.304 0 184.32 36.864 253.952 98.304l-266.24 266.24c-4.096 4.096-4.096 8.192-4.096 12.288 0 12.288 8.192 20.48 20.48 20.48l376.832 0C876.544 729.088 712.704 888.832 512 888.832z",onclick:function(){t.fnBuildChart(e)}},myBar:{show:!0,title:"切换为状图",icon:"path://M163.84 880.64h122.88v-737.28h-122.88v737.28z m-40.96-778.24h204.8v819.2h-204.8v-819.2z m286.72 204.8h204.8v614.4h-204.8v-614.4z m40.96 573.44h122.88v-532.48h-122.88v532.48z m245.76-368.64h204.8v409.6h-204.8v-409.6z m40.96 368.64h122.88v-327.68h-122.88v327.68z",onclick:function(){t.fnBuildChartBar(e)}},myLine:{show:!0,title:"切换为折线图",icon:"path://M3.04761905,3.23733333 L3.04761905,12.952 L4.38209523,12.952 L13.5238095,12.951619 L13.5238095,14.0944762 L3.61904762,14.0944762 C2.67227377,14.0944762 1.90476191,13.3269643 1.90476191,12.3801905 L1.90476191,3.23733333 L3.04761905,3.23733333 Z M13.615619,2.47619047 L13.615619,6.47619047 L12.4727619,6.47619047 L12.4727619,4.49257142 L8.99619047,8.05866667 L7.89523809,6.91809523 L5.36342858,9.53980953 L4.54133333,8.74590477 L7.89485714,5.27238095 L9.00038095,6.41752381 L11.728,3.61904762 L9.89828572,3.61904762 L9.89828572,2.47619047 L13.615619,2.47619047 L13.615619,2.47619047 Z",onclick:function(){t.fnBuildChartLine(e)}},magicType:{show:!0},restore:{show:!0},saveAsImage:{name:t.fnChartName(),show:!0}}},xAxis:{triggerEvent:!0,boundaryGap:!1,axisLine:{show:!0,lineStyle:{color:"#797979",width:"1"}},axisLabel:{margin:10,rotate:45,interval:0,formatter:function(e){return e.length>5?e.slice(0,5)+"...":e}},axisTick:{show:!0},type:"category",data:e.map((function(e){return e.name}))},yAxis:{type:"value",splitLine:{lineStyle:{type:"dashed"}},axisLabel:{margin:20},axisLine:{show:!1}},series:[{name:t.fnChartName(),color:["#108EE9"],data:e.map((function(e){return e.value})),type:"line",splitLine:{show:!0}}]};a.resize(),a.clear(),a.setOption(i,!0)}else a.setOption(this.nodataOption,!0)},fnBuildChartBar:function(e){this.changeUnit();var t=this,a=v.init(document.getElementById(this.indexItem.value+this.dimensionItem.value));if(e&&0!==e.length){var i={title:{text:t.fnChartName(),x:"center"},tooltip:{trigger:"axis",axisPointer:{type:"line"},formatter:"{b}:<br>{c} "+t.unit},grid:{left:"10%",right:"2%"},toolbox:{show:!0,feature:{dataView:{readOnly:!1},myPie:{show:!0,title:"切换为饼状图",icon:"path://M512 98.304C282.624 98.304 98.304 282.624 98.304 512s184.32 413.696 413.696 413.696c229.376 0 413.696-184.32 413.696-413.696S741.376 98.304 512 98.304zM888.832 491.52l-331.776 0 233.472-233.472C847.872 323.584 884.736 401.408 888.832 491.52zM512 888.832c-208.896 0-376.832-167.936-376.832-376.832 0-208.896 167.936-376.832 376.832-376.832 98.304 0 184.32 36.864 253.952 98.304l-266.24 266.24c-4.096 4.096-4.096 8.192-4.096 12.288 0 12.288 8.192 20.48 20.48 20.48l376.832 0C876.544 729.088 712.704 888.832 512 888.832z",onclick:function(){t.fnBuildChart(e)}},myBar:{show:!0,title:"切换为状图",icon:"path://M163.84 880.64h122.88v-737.28h-122.88v737.28z m-40.96-778.24h204.8v819.2h-204.8v-819.2z m286.72 204.8h204.8v614.4h-204.8v-614.4z m40.96 573.44h122.88v-532.48h-122.88v532.48z m245.76-368.64h204.8v409.6h-204.8v-409.6z m40.96 368.64h122.88v-327.68h-122.88v327.68z",onclick:function(){t.fnBuildChartBar(e)}},myLine:{show:!0,title:"切换为折线图",icon:"path://M3.04761905,3.23733333 L3.04761905,12.952 L4.38209523,12.952 L13.5238095,12.951619 L13.5238095,14.0944762 L3.61904762,14.0944762 C2.67227377,14.0944762 1.90476191,13.3269643 1.90476191,12.3801905 L1.90476191,3.23733333 L3.04761905,3.23733333 Z M13.615619,2.47619047 L13.615619,6.47619047 L12.4727619,6.47619047 L12.4727619,4.49257142 L8.99619047,8.05866667 L7.89523809,6.91809523 L5.36342858,9.53980953 L4.54133333,8.74590477 L7.89485714,5.27238095 L9.00038095,6.41752381 L11.728,3.61904762 L9.89828572,3.61904762 L9.89828572,2.47619047 L13.615619,2.47619047 L13.615619,2.47619047 Z",onclick:function(){t.fnBuildChartLine(e)}},magicType:{show:!0},restore:{show:!0},saveAsImage:{name:t.fnChartName(),show:!0}}},xAxis:{type:"category",axisLine:{show:!0,lineStyle:{color:"#797979",width:"1"}},axisLabel:{margin:10,rotate:45,interval:0,formatter:function(e){return e.length>5?e.slice(0,5)+"...":e}},data:e.map((function(e){return e.name}))},yAxis:{type:"value",splitLine:{lineStyle:{type:"dashed"}},axisLabel:{margin:20},axisLine:{show:!1}},series:[{name:t.fnChartName(),data:e.map((function(e){return e.value})),type:"bar",barWidth:"35%",itemStyle:{color:"#1890FF"},splitLine:{show:!0}}]};a.resize(),a.clear(),a.setOption(i,!0)}else a.setOption(this.nodataOption,!0)},fnQuery:function(){this.fnBuildChart()}}},g=y,x=a(1001),C=(0,x.Z)(g,p,b,!1,null,"109fa090",null),w=C.exports;m.w3.prototype.Base=Object.assign(m.w3.prototype.Base,(0,l.Z)({},d.Z));var I=[],z={name:"indexStatistics",components:{HospitalBar:w,TaTitle:c.Z},data:function(){var e=[{type:"seq",title:"序号",width:"60",align:"center"},{title:"院区",width:"150",field:"akb020",align:"center",visible:!1},{title:"科室",width:"150",field:"aae386",align:"center",visible:!1},{title:"医师",width:"150",field:"aaz570",align:"center",visible:!1},{title:"患者姓名",width:"150",field:"aac003",align:"center",visible:!1},{title:"月份",width:"150",field:"aae043",align:"center",sortable:!0,visible:!1},{title:"医疗类别",width:"150",field:"aka130",align:"center",collectionType:"AKA130",sortable:!0,visible:!1},{title:"人群类别",width:"150",field:"aae140",align:"left",collectionType:"AAE140",visible:!1},{title:"人均统筹金(元)",width:"150",field:"zbx01",align:"right",formatter:"formatFixedNumber",sortable:!0,visible:!0},{title:"人均统筹金环比",width:"150",field:"zbx01_mom",align:"right",sortable:!0,visible:!0},{title:"人均统筹金同比",width:"150",field:"zbx01_yoy",align:"right",sortable:!0,visible:!0},{title:"人均基金支出(元)",width:"150",field:"zbx02",align:"right",formatter:"formatFixedNumber",sortable:!0,visible:!0},{title:"人均基金支出环比",width:"160",field:"zbx02_mom",align:"right",sortable:!0,visible:!0},{title:"人均基金支出同比",width:"160",field:"zbx02_yoy",align:"right",sortable:!0,visible:!0},{title:"人均总费用(元)",width:"150",field:"zbx03",align:"right",formatter:"formatFixedNumber",sortable:!0,visible:!0},{title:"人均总费用环比",width:"150",field:"zbx03_mom",align:"right",sortable:!0,visible:!0},{title:"人均总费用同比",width:"150",field:"zbx03_yoy",align:"right",sortable:!0,visible:!0},{title:"平均住院日(天)",width:"150",field:"zbx04",align:"right",formatter:"formatFixedNumber",sortable:!0,visible:!0},{title:"平均住院日环比",width:"150",field:"zbx04_mom",align:"right",sortable:!0,visible:!0},{title:"平均住院日同比",width:"150",field:"zbx04_yoy",align:"right",sortable:!0,visible:!0},{title:"出院人次(人次)",width:"150",field:"zbx05",align:"right",sortable:!0,visible:!0},{title:"出院人次环比",width:"150",field:"zbx05_mom",align:"right",sortable:!0,visible:!0},{title:"出院人次同比",width:"150",field:"zbx05_yoy",align:"right",sortable:!0,visible:!0},{title:"自费占比",width:"150",field:"zbx08",align:"right",sortable:!0,visible:!0},{title:"自费占比环比",width:"150",field:"zbx08_mom",align:"right",sortable:!0,visible:!0},{title:"自费占比同比",width:"150",field:"zbx08_yoy",align:"right",sortable:!0,visible:!0}];return{col:{xs:1,sm:2,md:2,lg:6,xl:6,xxl:4},headers:{authorization:"authorization-text"},rangeValue:[this.Base.getMoment((new Date).toISOString().slice(0,4)+"-01","YYYY-MM"),this.Base.getMoment((new Date).toISOString().slice(0,7),"YYYY-MM")],aka063List:[],amountData:{},aka063Columns:[],statisticsDimension:[],statisticsIndex:[],selectedDimension:[],selectedDimensionItemList:[],checkedIndexList:[],selectedIndexItemList:[],defaultCheckedIndex:[],showKs:"0",showYs:"0",infoColumns:e,infoTableData:I,filterConfig:{remote:!0},sortConfig:{trigger:"default",remote:!0},sortColumn:"",ascOrDesc:"",hasError:!1,akb020:"",aaz307:"",selectedKs:[],selectedDoctor:[],ksColor:{},doctorColor:{},activeKey:0,showMode:"showTable",modalVisible:!1,parameters:{},forms:{},dynamicValidateForm:{domains:[]},formItemLayout:{labelCol:{span:7},wrapperCol:{span:17}},formItemLayout2:{labelCol:{span:5},wrapperCol:{span:17}},formItemLayoutWithOutLabel:{labelCol:{xs:{span:24},sm:{span:7}},wrapperCol:{xs:{span:24,offset:0},sm:{span:17,offset:0}}},permissions:null,paramsDisable:{akb020:!1,aaz307:!1,aaz263:!1},hosList:[],ksList:[],doctorList:[],dimensionflag:"",helperauthor:{aaz307:"",aaz263:""}}},created:function(){var e=this;return(0,r.Z)(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,u.Z.permissionCheck();case 2:e.permissions=t.sent;case 3:case"end":return t.stop()}}),t)})))()},mounted:function(){var e=this;this.$route.query.flag&&(this.dimensionflag=this.$route.query.flag),this.$route.query.aaz263&&(this.helperauthor.aaz263=this.$route.query.aaz263),this.$route.query.aaz307&&(this.helperauthor.aaz307=this.$route.query.aaz307),this.$nextTick((function(){e.fnQueryHos()}))},computed:{formBoxStyle:function(){return{height:"90px"}}},methods:{fnQueryHos:function(){var e=this,t={url:"/miimCommonRead/queryHospRpcList",autoValid:!0},a={successCallback:function(t){e.hosList=t.data.resultData,e.permissions&&e.permissions.akb020Set.size>0&&(e.hosList=e.hosList.filter((function(t){return e.permissions.akb020Set.has(t.value)}))),1===e.hosList.length?e.akb020=e.hosList[0].value:e.akb020="",e.fnQueryDept(e.akb020)},failCallback:function(t){e.$message.error("医院数据加载失败")}};this.Base.submit(null,t,a)},fnQueryDept:function(e){var t=this;this.akb020=e||"",this.Base.submit(null,{url:"miimCommonRead/queryDepartRpcList",data:{akb020:this.akb020,hospDeptType:"I"},autoValid:!1},{successCallback:function(e){t.ksList=e.data.resultData,t.fnQueryDocter()},failCallback:function(e){t.$message.error("科室数据加载失败")}})},fnQueryDocter:function(){var e=this,t={akb020:this.akb020};this.permissions&&this.permissions.aaz307Disable&&(t.departCode=this.permissions.aaz307Set.values().next().value),this.Base.submit(null,{url:"miimCommonRead/queryDoctorRpcList",data:t,autoValid:!1},{successCallback:function(t){e.doctorList=t.data.resultData,e.helperauthor.aaz263||e.helperauthor.aaz307?(e.helperauthor.aaz263&&(e.paramsDisable.aaz263=!0,e.baseInfoForm.setFieldsValue({aaz263:e.helperauthor.aaz263})),e.fnQueryStatisticsDimension()):e.setPermission()},failCallback:function(t){e.$message.error("医师数据加载失败")}})},setPermission:function(){var e=this;this.paramsDisable.akb020=this.permissions.akb020Disable,this.paramsDisable.aaz307=this.permissions.aaz307Disable,this.paramsDisable.aaz263=this.permissions.aaz263Disable,this.permissions&&(this.permissions&&this.permissions.aaz307Set.size>0&&(this.ksList=this.ksList.filter((function(t){return e.permissions.aaz307Set.has(t.value)})),this.permissions.aaz307Disable&&this.baseInfoForm.setFieldsValue({aaz307:this.ksList[0].value})),this.permissions&&this.permissions.aaz263Set.size>0&&(this.doctorList=this.doctorList.filter((function(t){return e.permissions.aaz263Set.has(t.value)})),this.permissions.aaz263Disable&&this.baseInfoForm.setFieldsValue({aaz263:this.doctorList[0].value}))),this.$nextTick((function(){e.fnQueryStatisticsDimension()}))},showTooltipMethod:function(e){var t=e.type,a=e.column,i=(e.row,e.items,e._columnIndex,a.property);if("footer"===t)return u.Z.props[i]?u.Z.props[i][t]:""},trunTime:function(e){switch(e){case"1":this.baseInfoForm.setFieldsValue({allDate:[h()().subtract("2","month"),h()()]});break;case"2":this.baseInfoForm.setFieldsValue({allDate:[h()().subtract("5","month"),h()()]});break;case"3":this.baseInfoForm.setFieldsValue({allDate:[h()().subtract("1","year"),h()()]});break;default:break}this.fnQuery()},fnQueryAmountData:function(){var e=this;this.Base.submit(null,{url:"indexStatistics/queryCount",data:this.infoPageParams(),autoValid:!1},{successCallback:function(t){var a=t.data;e.amountData=a.data},failCallback:function(t){e.$message.error("合计数据加载失败")}})},fnHandelQuery:function(){var e=this,t=!0;for(var a in this.forms)this.forms.hasOwnProperty(a)&&this.forms[a].validateFields((function(e){e&&(t=!1)}));t&&(this.modalVisible=!1,this.$nextTick((function(){e.fnQuery()})))},footerMethod:function(e){var t=this,a=e.columns;e.data;return[a.map((function(e,a){return 0===a?"合计":t.amountData&&["zbx01","zbx02","zbx03","zbx04","zbx05","zbx08"].includes(e.property)?t.amountData[e.property]:null}))]},relationChange:function(e){for(var t in this.forms)this.forms.hasOwnProperty(t)&&this.forms[t].setFieldsValue({relation:e})},autoFormCreate:function(e,t){this.forms[e]=t},removeDomain:function(e){var t=this.dynamicValidateForm.domains.indexOf(e);-1!==t&&this.dynamicValidateForm.domains.splice(t,1),delete this.forms[e.value+e.key]},addDomain:function(){this.dynamicValidateForm.domains.push({value:"formDemo",key:Date.now()})},setPopupContainer:function(e){return e.parentNode},fnQueryStatisticsDimension:function(){var e=this;this.Base.submit(null,{url:"indexStatistics/queryStatisticsDimension",data:{}},{successCallback:function(t){e.activeKey=0;var a=[];if("ks"===e.dimensionflag?(e.statisticsDimension=t.data.statisticsDimension.filter((function(e){return"akb020"!==e.value})),a.push("aae386"),e.fnChangeDimension(a)):"ys"===e.dimensionflag?(e.statisticsDimension=t.data.statisticsDimension.filter((function(e){return"akb020"!==e.value&&"aae386"!==e.value})),a.push("aaz570"),e.fnChangeDimension(a)):e.statisticsDimension=t.data.statisticsDimension,e.statisticsIndex=t.data.statisticsIndex,e.statisticsIndex.length>0){for(var i=0;i<e.statisticsIndex.length;i++)i<10&&(e.selectedIndexItemList.push(e.statisticsIndex[i]),e.defaultCheckedIndex.push(e.statisticsIndex[i].value));e.checkedIndexList=e.defaultCheckedIndex,e.baseInfoForm.setFieldsValue({statisticsIndex:e.defaultCheckedIndex,statisticsDimension:a})}},failCallback:function(t){e.$message.error("统计维度或指标加载失败")}})},disabledStartDate:function(e){return e.format("YYYYMMDD")>this.baseInfoForm.getFieldValue("endDate").format("YYYYMMDD")},disabledEndDate:function(e){return e=e.format("YYYYMMDD"),e>h()().startOf("day").format("YYYYMMDD")||e<this.baseInfoForm.getFieldValue("startDate").format("YYYYMMDD")},infoPageParams:function(){var e=this,t=this.baseInfoForm.getFieldsValue();if(t.allDate&&(t.allDate[0]&&(t.startDate=t.allDate[0].format("YYYYMM"),t.endDateLast=t.allDate[0].clone().subtract("1","month").format("YYYYMM")),t.allDate[1])){t.endDate=t.allDate[1].format("YYYYMM");var a=t.allDate[1].diff(t.allDate[0],"months");t.startDateLast=t.allDate[0].clone().subtract(a+1,"month").format("YYYYMM")}t.sortColumn=this.sortColumn,t.ascOrDesc=this.ascOrDesc;var i=[],s=function(t){e.forms.hasOwnProperty(t)&&e.forms[t].validateFields((function(a){a||i.push(e.forms[t].getFieldsValue())}))};for(var n in this.forms)s(n);return t.advancedSearch=i,this.parameters=t,t},fnChangeDimension:function(e){var t=this;"ks"!==this.dimensionflag||e.includes("aae386")||(e.push("aae386"),this.$message.warn("必须选择科室维度")),"ys"!==this.dimensionflag||e.includes("aaz570")||(e.push("aaz570"),this.$message.warn("必须选择医师维度")),this.selectedDimension=e;for(var a=this.$refs.infoTableRef.getTableColumn().collectColumn,i=a.splice(1,this.statisticsDimension.length),s=0;s<e.length;s++)for(var r=0;r<i.length;r++)e[s]===i[r].own.field&&(i[s]=i.splice(r,1,i[s])[0]);a.splice.apply(a,[1,0].concat((0,o.Z)(i))),this.$refs.infoTableRef.loadColumn(a),this.selectedDimensionItemList=[];var l,c=(0,n.Z)(this.statisticsDimension);try{for(c.s();!(l=c.n()).done;){var u=l.value;this.IsInArray(e,u.value)?this.$refs.infoTableRef.showColumn(this.$refs.infoTableRef.getColumnByField(u.value)):this.$refs.infoTableRef.hideColumn(this.$refs.infoTableRef.getColumnByField(u.value))}}catch(m){c.e(m)}finally{c.f()}var f,h=(0,n.Z)(this.selectedDimension);try{var d=function(){var e=f.value,a=t.statisticsDimension.filter((function(t){return t.value==e}));t.selectedDimensionItemList.push(a[0])};for(h.s();!(f=h.n()).done;)d()}catch(m){h.e(m)}finally{h.f()}this.parameters.selectedDimensionItemList=this.selectedDimensionItemList,this.$refs.infoTableRef.clearSort(),this.sortColumn="",this.ascOrDesc="",this.$nextTick((function(){if(t.selectedDimensionItemList.map((function(e){return e.value})).includes("aac003")||t.selectedDimensionItemList.map((function(e){return e.value})).includes("aaz570")){var e,a=(0,n.Z)(t.statisticsIndex);try{for(a.s();!(e=a.n()).done;){var i=e.value;t.$refs.infoTableRef.hideColumn(t.$refs.infoTableRef.getColumnByField(i.value+"_mom")),t.$refs.infoTableRef.hideColumn(t.$refs.infoTableRef.getColumnByField(i.value+"_yoy"))}}catch(m){a.e(m)}finally{a.f()}}else{var s,o=(0,n.Z)(t.statisticsIndex);try{for(o.s();!(s=o.n()).done;){var r=s.value;t.IsInArray(t.checkedIndexList,r.value)?(t.$refs.infoTableRef.showColumn(t.$refs.infoTableRef.getColumnByField(r.value+"_mom")),t.$refs.infoTableRef.showColumn(t.$refs.infoTableRef.getColumnByField(r.value+"_yoy"))):(t.$refs.infoTableRef.hideColumn(t.$refs.infoTableRef.getColumnByField(r.value+"_mom")),t.$refs.infoTableRef.hideColumn(t.$refs.infoTableRef.getColumnByField(r.value+"_yoy")))}}catch(m){o.e(m)}finally{o.f()}}t.fnQuery()}))},fnIndexChange:function(e){this.checkedIndexList=e,this.activeKey===e.length&&(this.activeKey=0===e.length?0:e.length-1),this.selectedIndexItemList=[],this.$refs.infoTableRef.clearSort(),this.sortColumn="",this.ascOrDesc="";var t,a=(0,n.Z)(this.statisticsIndex);try{for(a.s();!(t=a.n()).done;){var i=t.value;this.IsInArray(e,i.value)?(this.selectedIndexItemList.push(i),this.$refs.infoTableRef.showColumn(this.$refs.infoTableRef.getColumnByField(i.value)),this.selectedDimensionItemList.map((function(e){return e.value})).includes("aac003")||this.selectedDimensionItemList.map((function(e){return e.value})).includes("aaz570")||(this.$refs.infoTableRef.showColumn(this.$refs.infoTableRef.getColumnByField(i.value+"_mom")),this.$refs.infoTableRef.showColumn(this.$refs.infoTableRef.getColumnByField(i.value+"_yoy")))):(this.$refs.infoTableRef.hideColumn(this.$refs.infoTableRef.getColumnByField(i.value)),this.$refs.infoTableRef.hideColumn(this.$refs.infoTableRef.getColumnByField(i.value+"_mom")),this.$refs.infoTableRef.hideColumn(this.$refs.infoTableRef.getColumnByField(i.value+"_yoy")))}}catch(s){a.e(s)}finally{a.f()}},fnShowModeChange:function(){var e=this;"showCharts"===this.showMode&&0===this.selectedDimensionItemList.length&&this.$message.warn("请选择统计维度！"),("showCharts"===this.showMode&&this.selectedDimensionItemList.length>0||"showCharts"!==this.showMode)&&this.$nextTick((function(){e.fnQuery()}))},customValidate:function(e,t,a){var i=this;this.checkedIndexList.length<1?a("请至少选择一个指标！"):(this.$nextTick((function(){i.fnQuery()})),a())},showModal:function(){this.dynamicValidateForm.domains=[],this.forms={},this.modalVisible=!0},handleOk:function(){},fnIndexTabChange:function(e){var t=this;this.activeKey=e,this.parameters.activeIndexTab=e,this.$nextTick((function(){t.fnQuery()}))},fnCheckboxChange:function(e){var t=this,a=this.$refs.infoTableRef;this.IsInArray(e,"showKs")?(this.showKs="1",this.ksColor={color:"dodgerblue"},a.showColumn(a.getColumnByField("aae386"))):(this.showKs="0",this.ksColor={},a.hideColumn(a.getColumnByField("aae386"))),this.IsInArray(e,"showYs")?(this.showYs="1",this.doctorColor={color:"dodgerblue"},a.showColumn(a.getColumnByField("aaz570"))):(this.showYs="0",this.doctorColor={},a.hideColumn(a.getColumnByField("aaz570"))),this.$nextTick((function(){t.fnQuery()}))},sortChangeEvent:function(e){var t=this,a=(e.column,e.property),i=e.order;this.sortColumn=a,this.ascOrDesc=i,this.$nextTick((function(){t.fnQuery()}))},filterChangeEvent:function(e){var t=this,a=e.column,i=(e.property,e.values);e.datas,e.filters,e.$event;"医师"===a.title?this.selectedDoctor=i:this.selectedKs=i,this.$nextTick((function(){t.$refs.infoPageRef.loadData()}))},IsInArray:function(e,t){var a=","+e.join(",")+",";return-1!=a.indexOf(","+t+",")},fnQuery:function(){var e=this,t=this.baseInfoForm.getFieldsValue(),a=t.allDate;if(!a||a[0]&&a[1]){var i=a[1].diff(a[0],"months");if(i>12)this.$message.error("结算时间不能跨年！");else if(this.checkedIndexList.length<1)this.$message.error("请至少选择一个指标！");else if("showCharts"===this.showMode){if(this.selectedDimensionItemList.length>0){var s=this.infoPageParams();s.currentIndex=this.selectedIndexItemList[this.activeKey].value,this.Base.submit(null,{url:"indexStatistics/queryChartData",data:s,autoValid:!1},{successCallback:function(t){var a=t.data,i=a.data;e.IsInArray(e.selectedDimension,"akb020")&&e.$refs.hospitalBarRef[e.activeKey].fnBuildChart(i.akb020),e.IsInArray(e.selectedDimension,"aae386")&&e.$refs.deptBarRef[e.activeKey].fnBuildChartBar(i.aae386),e.IsInArray(e.selectedDimension,"aaz570")&&e.$refs.doctorBarRef[e.activeKey].fnBuildChartBar(i.aaz570),e.IsInArray(e.selectedDimension,"aka130")&&e.$refs.aka130BarRef[e.activeKey].fnBuildChart(i.aka130),e.IsInArray(e.selectedDimension,"aae140")&&e.$refs.aae140BarRef[e.activeKey].fnBuildChart(i.aae140),e.IsInArray(e.selectedDimension,"aae043")&&e.$refs.aae043BarRef[e.activeKey].fnBuildChartLine(i.aae043),e.IsInArray(e.selectedDimension,"aac003")&&e.$refs.aac003BarRef[e.activeKey].fnBuildChartLine(i.aac003)},failCallback:function(t){e.$message.error("图表数据加载失败")}})}}else this.$nextTick((function(){e.fnQueryAmountData(),e.$refs.infoPageRef.loadData()}))}else this.$message.error("请选择时间范围！")},fnReSet:function(){this.baseInfoForm.resetFields(),this.dynamicValidateForm.domains=[],this.forms={};var e=[];"ks"===this.dimensionflag&&e.push("aae386"),"ys"===this.dimensionflag&&e.push("aaz570"),this.baseInfoForm.setFieldsValue({statisticsIndex:this.defaultCheckedIndex,statisticsDimension:e}),this.fnChangeDimension(e),this.fnIndexChange(this.defaultCheckedIndex)},fnBuildColunmn:function(){var e,t=[],a=(0,n.Z)(this.selectedDimensionItemList);try{for(a.s();!(e=a.n()).done;){var i=e.value;t.push({header:i.label,key:i.value,width:20})}}catch(l){a.e(l)}finally{a.f()}var s,o=(0,n.Z)(this.selectedIndexItemList);try{for(o.s();!(s=o.n()).done;){var r=s.value;t.push({header:r.label,key:r.value,width:20})}}catch(l){o.e(l)}finally{o.f()}return t},fnExportExcel:function(){var e=this;if(this.baseInfoForm.validateFields(["allDate"],(function(t,a){e.hasError=!!t})),!this.hasError){var t,a=this.infoPageParams(),i=[],s=this.$refs.infoTableRef.getColumns(),o=(0,n.Z)(s);try{for(o.s();!(t=o.n()).done;){var r=t.value;"seq"!==r.type&&"operate"!==r.property&&!1!==r.visible&&i.push({header:r.title,key:r.property,width:20})}}catch(h){o.e(h)}finally{o.f()}var l,c=[],u=(0,n.Z)(this.selectedDimension);try{for(u.s();!(l=u.n()).done;){var f=l.value;"aka130"===f?c.push({codeType:"AKA130",columnKey:"aka130"}):"aae140"===f?c.push({codeType:"AAE140",columnKey:"aae140"}):"aae141"===f&&c.push({codeType:"AAE141",columnKey:"aae141"})}}catch(h){u.e(h)}finally{u.f()}this.Base.submit(null,{url:"indexStatistics/exportExcel",data:a,autoValid:!1},{successCallback:function(t){var a={fileName:"指标统计表",sheets:[{name:"worksheet1",column:{complex:!1,columns:i},rows:t.data.exportData,codeList:c}]};e.Base.generateExcel(a)},failCallback:function(t){e.$message.error("医师数据加载失败")}})}},fnCustomHeaderCell:function(e){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px",fontWeight:"bold"}}},filterOption:function(e,t){return t.componentOptions.children[0].text.indexOf(e)>=0}}},D=z,L=(0,x.Z)(D,i,s,!1,null,"62bf3bfb",null),k=L.exports},36766:function(e,t,a){"use strict";var i=a(66586);t["Z"]=i.Z},26263:function(e,t,a){"use strict";a.d(t,{s:function(){return i},x:function(){return s}});var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:e.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[e._v(e._s(e.title))]),e._t("default")],2)},s=[]},66586:function(e,t){"use strict";var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};t["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}},83231:function(e,t,a){"use strict";var i=a(48534);a(36133);function s(e){return e?e.akb020&&e.aaz263?"personal":e.akb020&&e.aaz309&&e.aaz307?"group":e.akb020&&e.aaz307?"department":e.akb020?"hospital":"all":"all"}function n(e){return o.apply(this,arguments)}function o(){return o=(0,i.Z)(regeneratorRuntime.mark((function e(t){var a,i,n,o,r,l,c,u,f,h,d,m;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,Base.submit(null,{url:"/miimCommonRead/queryDataPermission",showPageLoading:!1,data:{jobnumber:t},autoValid:!0});case 2:return a=e.sent,i=new Set,n=new Set,a.data.permission.forEach((function(e){var t=s(e);"hospital"===t&&i.add(e.akb020),"department"===t&&n.add(e.aaz307)})),o=a.data.permission.filter((function(e){return"department"===s(e)||!n.has(e.aaz307)})).filter((function(e){return"hospital"===s(e)||!i.has(e.akb020)})),r=new Set(o.map((function(e){return e.akb020})).filter((function(e){return null!=e}))),l=new Set(o.map((function(e){return e.aaz307})).filter((function(e){return null!=e}))),c=new Set(o.map((function(e){return e.aaz309})).filter((function(e){return null!=e}))),u=new Set(o.map((function(e){return e.aaz263})).filter((function(e){return null!=e}))),f=!1,h=!1,d=!1,m=!1,1===r.size&&(f=!0),1===l.size&&1===r.size&&(h=!0),1===l.size&&1===r.size&&1===c.size&&(d=!0),1===r.size&&0===l.size&&1===u.size&&(m=!0),e.abrupt("return",{akb020Set:r,aaz307Set:l,aaz263Set:u,aaz309Set:c,akb020Disable:f,aaz307Disable:h,aaz263Disable:m,aaz309Disable:d});case 20:case"end":return e.stop()}}),e)}))),o.apply(this,arguments)}function r(e,t){Base.submit(null,{url:"miimCommonRead/getAa01AAE500StartStop",data:e},{successCallback:function(e){return t(e)}})}function l(e,t){Base.submit(null,{url:"miimCommonRead/insertTableColumShow",data:e,autoValid:!1},{successCallback:function(e){return t("success")},failCallback:function(e){return t("fail")}})}var c={zbx01:{footer:function(){return"统筹金总额"}},zbx02:{footer:function(){return"基金支出总额"}},zbx03:{footer:function(){return"总费用"}},zbx04:{footer:function(){return"总住院日"}}};t["Z"]={permissionCheck:n,getAa01AAE500StartStop:r,insertTableColumShow:l,props:c,moneyNumFormat:function(e){var t=e.cellValue;return t||"—"}}},55382:function(){},61219:function(){}}]);
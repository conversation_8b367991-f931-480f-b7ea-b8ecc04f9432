(function(){var e={11294:function(e,t,i){var o={"./zh-cn":73644,"./zh-cn.js":73644,"moment/locale/zh-cn":73644,"moment/locale/zh-cn.js":73644};function s(e){var t=n(e);return i(t)}function n(e){if(!i.o(o,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return o[e]}s.keys=function(){return Object.keys(o)},s.resolve=n,e.exports=s,s.id=11294},18583:function(e,t,i){"use strict";i.d(t,{d:function(){return r}});var o=i(30228),s=i(95278),n=i(6442),r={methods:{changeTheme:function(e){if(s.Z.onlineTheme)return(0,o.changeTheme)(e)},updateColorWeak:function(e){return(0,n.MP)(e)}}}},30228:function(e,t,i){"use strict";i.r(t),i.d(t,{changeTheme:function(){return l},injectTheme:function(){return f},syncThemeWhenInit:function(){return u}});var o=i(17546),s=i(95278),n=i(6442),r=i(18583),a=i(97369),c=i.n(a);function l(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],i=[];if(0===e.indexOf("#"))i.push(e);else{var o=c()(e);Object.keys(o).forEach((function(e){e.indexOf("color")>0&&i.push(o[e])}))}(0,n.xz)(i,t)}function d(e){(0,n.MP)(e)}function u(){var e,t=o.Z.createWebStorage("index_theme",{isLocal:!0}),i=o.Z.getStorage("index_theme","index_theme",!0);l(null!==i&&void 0!==i?i:s.Z.defaultTheme,!1),(0,n.MP)(null!==(e=t.get("dark_mode"))&&void 0!==e?e:s.Z.defaultDarkMode)}function f(e){s.Z.onlineTheme&&(e.mixin(r.d),u())}window.addEventListener("storage",(function(e){if("index_theme"===e.key){var t=JSON.parse(e.newValue),i=t.index_theme,o="true"===t.dark_mode;i&&l(JSON.parse(i),!1),d(o)}}),!1)},68598:function(e,t,i){"use strict";var o=i(48534),s=(i(82526),i(41817),i(72443),i(92401),i(8722),i(32165),i(69007),i(16066),i(83510),i(41840),i(6982),i(32159),i(96649),i(39341),i(60543),i(21703),i(9170),i(32120),i(52262),i(92222),i(50545),i(43290),i(57327),i(69826),i(34553),i(84944),i(86535),i(91038),i(26699),i(82772),i(66992),i(69600),i(94986),i(21249),i(26572),i(85827),i(96644),i(47042),i(2707),i(38706),i(40561),i(33792),i(99244),i(18264),i(96078),i(4855),i(68309),i(35837),i(38862),i(73706),i(51532),i(99752),i(82376),i(73181),i(23484),i(2388),i(88621),i(60403),i(84755),i(25438),i(90332),i(40658),i(40197),i(44914),i(52420),i(60160),i(60970),i(10408),i(73689),i(9653),i(93299),i(35192),i(33161),i(44048),i(78285),i(44363),i(55994),i(61874),i(9494),i(31354),i(56977),i(19601),i(59595),i(35500),i(69720),i(43371),i(38559),i(38880),i(49337),i(36210),i(30489),i(46314),i(43304),i(41825),i(98410),i(72200),i(47941),i(94869),i(33952),i(57227),i(60514),i(41539),i(26833),i(88674),i(17922),i(34668),i(17727),i(36535),i(12419),i(69596),i(52586),i(74819),i(95683),i(39361),i(51037),i(5898),i(67556),i(14361),i(83593),i(39532),i(81299),i(24603),i(28450),i(74916),i(92087),i(88386),i(77601),i(39714),i(70189),i(24506),i(79841),i(27852),i(94953),i(32023),i(78783),i(4723),i(76373),i(66528),i(83112),i(38992),i(82481),i(15306),i(68757),i(64765),i(23123),i(23157),i(73210),i(48702),i(55674),i(15218),i(74475),i(57929),i(50915),i(29253),i(42125),i(78830),i(58734),i(29254),i(37268),i(7397),i(60086),i(80623),i(44197),i(76495),i(87145),i(35109),i(65125),i(82472),i(49743),i(8255),i(29135),i(48675),i(92990),i(18927),i(33105),i(35035),i(74345),i(7174),i(32846),i(98145),i(44731),i(77209),i(96319),i(58867),i(37789),i(33739),i(95206),i(29368),i(14483),i(12056),i(3462),i(30678),i(27462),i(33824),i(55021),i(12974),i(15016),i(4129),i(38478),i(19258),i(84811),i(34286),i(3048),i(77461),i(1999),i(61886),i(8e4),i(83475),i(46273),i(56882),i(78525),i(27004),i(3087),i(97391),i(66342),i(40787),i(23647),i(68216),i(88449),i(31672),i(74326),i(15581),i(78631),i(57640),i(25387),i(64211),i(12771),i(62962),i(71790),i(51568),i(26349),i(67427),i(32279),i(13384),i(2490),i(85567),i(5332),i(79433),i(59849),i(59461),i(82499),i(34514),i(26877),i(9924),i(72608),i(41874),i(66043),i(23748),i(71501),i(10072),i(23042),i(99137),i(71957),i(96306),i(103),i(8582),i(90618),i(74592),i(88440),i(58276),i(35082),i(12813),i(18222),i(24838),i(38563),i(50336),i(7512),i(74442),i(87713),i(46603),i(70100),i(10490),i(13187),i(60092),i(19041),i(30666),i(51638),i(62975),i(15728),i(46056),i(44299),i(5162),i(50292),i(29427),i(99964),i(75238),i(4987),i(1025),i(77479),i(34582),i(47896),i(12647),i(98558),i(84018),i(97507),i(61605),i(49076),i(34999),i(88921),i(96248),i(13599),i(11477),i(64362),i(15389),i(46006),i(90401),i(45164),i(91238),i(54837),i(87485),i(56767),i(69916),i(76651),i(61437),i(35285),i(39865),i(86035),i(50058),i(67501),i(609),i(21568),i(54534),i(95090),i(48824),i(44130),i(35954),i(16850),i(26182),i(8922),i(37380),i(1118),i(5835),i(23767),i(8585),i(8970),i(84444),i(68696),i(78206),i(76478),i(79715),i(12714),i(5964),i(43561),i(32049),i(86020),i(56585),i(75505),i(27479),i(54747),i(33948),i(87714),i(82801),i(1174),i(84633),i(85844),i(61295),i(60285),i(83753),i(41637),i(36133),i(28594),i(67532)),n=i(84175);if((0,n.Z)()||(0,s.Z)())throw new Error("请使用IE11以上版本或Chromium内核浏览器!");"Microsoft Internet Explorer"===navigator.appName&&navigator.appVersion.match(/9./i),navigator.userAgent.indexOf("MSIE 9")>0&&document.addEventListener("selectionchange",(function(){var e=document.activeElement;if("TEXTAREA"===e.tagName||"INPUT"===e.tagName){var t=document.createEvent("CustomEvent");t.initCustomEvent("input",!0,!0,{}),e.dispatchEvent(t)}}));var r=i(95278),a=i(3032),c=i(95082),l=i(80201),d=i(7029),u=i(28590),f=i(73502),h=i(48600),p=i(76040),m=i(40103),g=i(86472),v=i(17546),w=i(89281),y=i(794),C=i(59427),b=i(87063),S=i(30965),k=i(60011),x=i(76685),P=i(47403),T=i(11782),_=i(69872);i(63116),i(65906),i(85837),i(47215),i(26677),i(7638),i(28218),i(56357),i(21688),i(28412);a["default"].use(w.Z),a["default"].use(y.Z),a["default"].use(C.ZP),a["default"].use(b.Z),a["default"].use(S.Z),a["default"].use(k.Z),a["default"].use(x.Z),a["default"].use(P.Z),a["default"].use(T.Z),a["default"].use(_.Z),a["default"].use(w.Z),a["default"].use(C.ZP),a["default"].use(S.Z),a["default"].use(x.Z),a["default"].use(b.Z),a["default"].use(T.Z),a["default"].use(y.Z),a["default"].use(k.Z),a["default"].use(P.Z),a["default"].use(_.Z);var $={getCookie:p.Z,setCookie:m.Z,getToken:g.Z,getNowPageParam:f.Z,objectToUrlParam:h.Z,webStorage:v.Z},I=(0,c.Z)((0,c.Z)((0,c.Z)((0,c.Z)({},u.ZP),l.Z),$),d.ZP);a["default"].prototype.Base=(0,c.Z)((0,c.Z)({},I),y.Z.$mask),a["default"].prototype.$message=k.Z,a["default"].prototype.$info=x.Z.info,a["default"].prototype.$success=x.Z.success,a["default"].prototype.$error=x.Z.error,a["default"].prototype.$warning=x.Z.warning,a["default"].prototype.$confirm=x.Z.confirm,window.Base=a["default"].prototype.Base,window.message=k.Z,window.TaUtils=(0,c.Z)({},$);var O=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",{staticClass:"body",attrs:{id:"app"}},[o("header",[o("div",{staticClass:"logo"},[e._e()])]),o("img",{staticStyle:{height:"60%","margin-left":"10%","margin-top":"4%"},attrs:{src:i(20010)}}),o("div",{staticClass:"login-con"},[e.showSmsLogin?e._e():o("div",[o("div",{staticClass:"header1"},[o("img",{staticStyle:{height:"35px",width:"39px"},attrs:{src:i(90266)}}),e._v(" "+e._s(e.$t("login.systemLogin"))+" "),o("span",[e._v(e._s(e.$t("login.systemLoginTips")))])]),o("login-form",{ref:"loginForm",staticStyle:{"margin-top":"50px"},on:{modifyPasswordPaneCtr:e.modifyPasswordPaneCtr}})],1),e.showSmsLogin?o("div",[o("div",{staticClass:"header1"},[e._v(" "+e._s(e.$t("login.loginMode.SMSLogin"))+" "),o("span",[e._v(e._s(e.$t("login.SMSLoginTips")))])]),o("sms-login-form")],1):e._e(),this.openSocialLogin||this.openSmsLogin?o("div",{staticClass:"header2"},[e._v(" "+e._s(e.$t("login.loginMode.OtherLogin"))+" "),o("span",[e._v(e._s(e.$t("login.loginMode.OtherLoginTips")))])]):e._e(),o("div",[this.openSmsLogin?o("ta-button",{staticStyle:{float:"left","font-size":"12px",border:"none"},on:{click:e.showSmsLoginMethod}},[e._v(" "+e._s(e.showSmsLogin?e.$t("login.loginMode.userPwdLoginTips"):e.$t("login.loginMode.smsLoginTips"))+" ")]):e._e(),this.openSocialLogin?o("div",{staticStyle:{float:"right"}},[o("social-list",{attrs:{"pass-state":"1"}})],1):e._e()],1)]),o("ta-modal",{attrs:{title:e.$t("login.changePassword"),visible:e.showModifyPasswordPane,footer:null,"mask-closable":!1,"destroy-on-close":!0,width:"390px","body-style":{paddingBottom:"10px"}},on:{cancel:e.modifyPasswordPaneCtr}},[o("modify-password",{attrs:{show:e.showModifyPasswordPane,"pass-state":"1"},on:{"update:show":function(t){e.showModifyPasswordPane=t}}})],1),o("footer",[o("span",[e._v(e._s(e.$t("login.explain"))+" "+e._s(e.supportText)+" "),e._l(e.support,(function(t,i){return o("span",{key:i},[o("span",{staticStyle:{display:"inline-block",width:"auto"}},[e._v(e._s(t.supportname)+e._s(t.supportnum))]),e._v("   ")])})),o("ta-tooltip",{staticStyle:{"margin-left":"10px"}},[o("template",{slot:"title"},[e._v(" "+e._s(e.commitId)+" ")]),e._v(" "+e._s(e.version)+" ")],2)],2)])],1)},E=[],L=i(71746),A=(i(28743),function(){var e=this,t=this,i=t.$createElement,o=t._self._c||i;return o("ta-form",{attrs:{"auto-form-create":function(t){e.form=t},layout:"vertical"}},[o("ta-form-item",{attrs:{"field-decorator-id":"username","field-decorator-options":{rules:[{required:!0,message:t.$t("login.userNameRequire")},{max:30,message:t.$t("login.userNameLength")}]}}},[o("ta-input",{attrs:{placeholder:t.$t("login.userName")},on:{blur:t.checkUser,pressEnter:function(e){return t.gotoDeal(e,"password")}}})],1),o("ta-form-item",{staticStyle:{"margin-top":"20px"},attrs:{"field-decorator-id":"password","field-decorator-options":{rules:[{required:!0,message:t.$t("login.passwordRequire")}]}}},[o("ta-input",{attrs:{placeholder:t.$t("login.password"),type:"password",autocomplete:"new-password"},on:{pressEnter:function(e){return t.gotoDeal(e,t.simpleCheckCodeState?"checkCode":"sbbtn")}}})],1),t.simpleCheckCodeState?o("ta-form-item",{staticStyle:{"margin-top":"20px"},attrs:{"field-decorator-id":"checkCode","field-decorator-options":{rules:[{required:!0,message:t.$t("login.verificationCodeRequire")}]}}},[o("ta-input",{ref:"checkCode",staticStyle:{width:"60%"},attrs:{placeholder:t.$t("login.verificationCode")},on:{pressEnter:function(e){return t.gotoDeal(e,"sbbtn")}}}),o("img",{staticStyle:{width:"40%"},attrs:{src:t.imgSrc,title:t.$t("login.getVerificationCodeTips")},on:{click:function(e){return t.refreshCode()}}})],1):t._e(),o("div",{directives:[{name:"show",rawName:"v-show",value:t.slideCheckCodeState,expression:"slideCheckCodeState"}],attrs:{id:"loginCheckCodeDiv"}},[o("div",{staticClass:"codeDragValidate-layout-div"},[o("div",{staticClass:"codeDragBar-drag-div"},[o("div",{staticClass:"codeDrag-win-div"},[o("div",{staticClass:"codeDrag-win-div-body"},[o("div",{staticClass:"codeDrag-bg-img-div"},[o("img",{staticClass:"codeDrag-code-img",attrs:{src:"#",alt:t.$t("login.backgroundImg")}}),o("img",{staticClass:"codeDrag-darg-img",attrs:{src:"#",alt:t.$t("login.dragImg")}})]),o("div",{staticClass:"codeDrag-code-refresh"})])]),o("div",{staticClass:"dragBar"},[o("span")]),o("div",{staticClass:"dragBar-inDrag-bg"}),o("div",{staticClass:"dragBar-base-bg"},[o("span",[t._v(t._s(t.$t("login.sureToChange")))])])])])]),t.showClickWordCheckCode?o("Verify",{ref:"verify",attrs:{"captcha-type":t.captchaType,"img-size":{width:"330px",height:"155px"}},on:{success:t.success}}):t._e(),o("ta-form-item",[o("ta-button",{staticStyle:{width:"366px",height:"46px","font-size":"18px","margin-top":"50px"},attrs:{id:"sbbtn",type:"primary",block:""},on:{click:t.useVerify}},[t._v(" "+t._s(t.$t("login.login"))+" ")]),o("ta-button",{staticStyle:{"font-size":"14px",border:"none",margin:"0","box-shadow":"none",color:"#0158e7","margin-top":"10px","margin-right":"-30px",float:"right"},attrs:{tabindex:"-1"},on:{click:t.showModifyPasswordPane}},[t._v(" "+t._s(t.$t("login.changePassword"))+" ")]),o("ta-button",{staticStyle:{"font-size":"14px",border:"none",margin:"0","box-shadow":"none",color:"#0158e7","margin-top":"10px","margin-right":"-10px",float:"right"},attrs:{tabindex:"-1"},on:{click:t.assistantDownload}},[o("span",{staticStyle:{"text-decoration":"underline"}},[t._v(t._s(t.$t("login.assistantDownload")))])])],1)],1)}),B=[],Z=function(){var e=faceConfig.basePath+"/captcha/simple/getImg?r="+Math.random();return e},N=Z,z=i(3336);function j(){var e,t=navigator.userAgent.toLowerCase(),i="UNKNOW",o="";if(t.indexOf("msie")>=0){var s=/msie [\d]+/gi;i="IE",o=""+t.match(s)}else if(t.indexOf("firefox")>=0){var n=/firefox\/[\d]+/gi;i="firefox",o=""+t.match(n)}else if(t.indexOf("chrome")>=0){var r;t.indexOf("edge")>=0?(r=/edge\/[\d]+/gi,i="edge"):(r=/chrome\/[\d]+/gi,i="chrome"),o=""+t.match(r)}else if(t.indexOf("safari")>=0&&t.indexOf("chrome")<0){var a=/version\/[\d]+/gi;i="safari",o=""+t.match(a)}else if(t.indexOf("opera")>=0){var c=/version\/[\d]+/gi;i="opera",o=""+t.match(c)}else{var l=navigator.appName;if("Netscape"==l){var d=t.match(/rv:[\d]+/gi);o=d,i="IE"}}return e=(o+"").replace(/[^0-9.]/gi,""),{Browser:i,verinNum:e}}function D(){var e={win:!1,mac:!1,xll:!1,iphone:!1,ipoad:!1,ipad:!1,ios:!1,android:!1,nokiaN:!1,winMobile:!1,wii:!1,ps:!1},t=navigator.userAgent,i=navigator.platform;if(e.win=0==i.indexOf("Win"),e.mac=0==i.indexOf("Mac"),e.xll=0==i.indexOf("Xll")||0==i.indexOf("Linux"),e.win&&/Win(?:dows )?([^do]{2})\s?(\d+\.\d+)?/.test(t))if("NT"==RegExp["$1"])switch(RegExp["$2"]){case"5.0":e.win="2000";break;case"5.1":e.win="XP";break;case"6.0":e.win="Vista";break;case"6.1":e.win="7";break;case"6.2":e.win="8";break;case"6.3":case"10.0":e.win="10";break;default:e.win="NT";break}else"9x"==RegExp["$1"]?e.win="ME":e.win=RegExp["$1"];for(var o in e.iphone=t.indexOf("iPhone")>-1,e.ipod=t.indexOf("iPod")>-1,e.ipad=t.indexOf("iPad")>-1,e.nokiaN=t.indexOf("nokiaN")>-1,"CE"==e.win?e.winMobile=e.win:"Ph"==e.win&&/Windows Phone OS (\d+.\d)/i.test(t)&&(e.win="Phone",e.winMobile=parseFloat(RegExp["$1"])),e.mac&&t.indexOf("Mobile")>-1&&(/CPU (?:iPhone )?OS (\d+_\d+)/i.test(t)?e.ios=parseFloat(RegExp["$1"].replace("_",".")):e.ios=2),/Android (\d+\.\d+)/i.test(t)&&(e.android=parseFloat(RegExp["$1"])),e.wii=t.indexOf("Wii")>-1,e.ps=/PlayStation/i.test(t),e)e[o]&&(e=o+e[o]);return{System:!("object"==(0,z.Z)(e))&&e||"UNKNOW",ScreenSize:function(){return screen.width+","+screen.height}()}}var F=j(),M=D(),R={clientsystem:M.System,clientscreensize:M.ScreenSize,clientbrowser:F.Browser},W=R,V=i(96565),U=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("ta-modal",{attrs:{title:e.$t("login.verify.verifyTitle"),width:378,footer:null,"body-style":{paddingTop:"12px"}},on:{cancel:e.closeBox},model:{value:e.showBox,callback:function(t){e.showBox=t},expression:"showBox"}},[e.componentType?i(e.componentType,{ref:"instance",tag:"components",attrs:{"captcha-type":e.captchaType,type:e.verifyType,figure:e.figure,arith:e.arith,mode:e.mode,"v-space":e.vSpace,explain:e.explain,"img-size":e.imgSize,"block-size":e.blockSize,"bar-size":e.barSize,"default-img":e.defaultImg},on:{success:e.success}}):e._e()],1)},q=[],K=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticStyle:{position:"relative"}},[i("div",{staticClass:"verify-img-out"},[i("div",{staticClass:"verify-img-panel",style:{width:e.setSize.imgWidth,height:e.setSize.imgHeight,"background-size":e.setSize.imgWidth+" "+e.setSize.imgHeight,"margin-bottom":e.vSpace+"px"}},[i("div",{directives:[{name:"show",rawName:"v-show",value:e.showRefresh,expression:"showRefresh"}],staticClass:"verify-refresh",staticStyle:{"z-index":"3"},on:{click:e.refresh}},[i("span",[i("ta-icon",{attrs:{type:"sync"}}),e._v("刷新")],1)]),i("img",{ref:"canvas",staticStyle:{width:"100%",height:"100%",display:"block"},attrs:{src:e.pointBackImgBase?"data:image/png;base64,"+e.pointBackImgBase:e.defaultImg,alt:""},on:{click:function(t){e.bindingClick&&e.canvasClick(t)}}}),e._l(e.tempPoints,(function(t,o){return i("div",{key:o,staticClass:"point-area",style:{color:"#fff","z-index":9999,width:"20px",height:"20px","text-align":"center","line-height":"20px","border-radius":"50%",position:"absolute",top:parseInt(t.y-10)+"px",left:parseInt(t.x-10)+"px"}},[e._v(" "+e._s(o+1)+" ")])}))],2)]),i("div",{class:[{"verify-bar-area-default":"default"===e.status},{"verify-bar-area-success":"success"===e.status},{"verify-bar-area-failed":"failed"===e.status}],style:{width:e.setSize.imgWidth,"line-height":this.barSize.height}},[i("span",{staticClass:"verify-msg"},[e._v(e._s(e.text))])])])},H=[];i(32564);function J(e){var t,i,o,s,n=e.$el.parentNode.offsetWidth||window.offsetWidth,r=e.$el.parentNode.offsetHeight||window.offsetHeight;return t=-1!==e.imgSize.width.indexOf("%")?parseInt(this.imgSize.width)/100*n+"px":this.imgSize.width,i=-1!==e.imgSize.height.indexOf("%")?parseInt(this.imgSize.height)/100*r+"px":this.imgSize.height,o=-1!==e.barSize.width.indexOf("%")?parseInt(this.barSize.width)/100*n+"px":this.barSize.width,s=-1!==e.barSize.height.indexOf("%")?parseInt(this.barSize.height)/100*r+"px":this.barSize.height,{imgWidth:t,imgHeight:i,barWidth:o,barHeight:s}}var X={name:"VerifyPoints",props:{mode:{type:String,default:"fixed"},captchaType:{type:String},vSpace:{type:Number,default:5},imgSize:{type:Object,default:function(){return{width:"310px",height:"155px"}}},barSize:{type:Object,default:function(){return{width:"310px",height:"40px"}}},defaultImg:{type:String,default:""}},data:function(){return{checkNum:3,fontPos:[],checkPosArr:[],num:1,pointBackImgBase:"",poinTextList:[],backToken:"",setSize:{imgHeight:0,imgWidth:0,barHeight:0,barWidth:0},tempPoints:[],text:"",barAreaColor:void 0,barAreaBorderColor:void 0,showRefresh:!0,bindingClick:!0,status:"default"}},computed:{resetSize:function(){return J}},watch:{type:{immediate:!0,handler:function(){this.init()}}},mounted:function(){this.$el.onselectstart=function(){return!1}},methods:{init:function(){var e=this;this.fontPos.splice(0,this.fontPos.length),this.checkPosArr.splice(0,this.checkPosArr.length),this.num=1,this.$nextTick((function(){e.setSize=e.resetSize(e),e.$parent.$emit("ready",e)}))},canvasClick:function(e){var t=this;this.checkPosArr.push(this.getMousePos(this.$refs.canvas,e)),this.num===this.checkNum&&(this.num=this.createPoint(this.getMousePos(this.$refs.canvas,e)),this.checkPosArr=this.pointTransform(this.checkPosArr,this.setSize),setTimeout((function(){var e=Base.cryptoAsymmetricFn(t.backToken+"---"+JSON.stringify(t.checkPosArr)),i={captchaType:t.captchaType,pointJson:Base.cryptoAsymmetricFn(JSON.stringify(t.checkPosArr)),token:t.backToken};t.Base.submit(null,{url:"captcha/check",data:i}).then((function(i){var o=i.data.resultData;"0000"===o.repCode?(t.status="success",t.text=t.$t("login.verify.validationSucceeded"),t.bindingClick=!1,"pop"===t.mode&&setTimeout((function(){t.$parent.clickShow=!1,t.refresh()}),1500),t.$emit("success",{captchaVerification:e})):(t.$parent.$emit("error",t),t.status="failed",t.text=t.$t("login.verify.validationFailed"),setTimeout((function(){t.refresh()}),700))}))}),400)),this.num<this.checkNum&&(this.num=this.createPoint(this.getMousePos(this.$refs.canvas,e)))},getMousePos:function(e,t){var i=t.offsetX,o=t.offsetY;return{x:i,y:o}},createPoint:function(e){return this.tempPoints.push(Object.assign({},e)),++this.num},refresh:function(){this.tempPoints.splice(0,this.tempPoints.length),this.barAreaColor="#000",this.barAreaBorderColor="#ddd",this.bindingClick=!0,this.fontPos.splice(0,this.fontPos.length),this.checkPosArr.splice(0,this.checkPosArr.length),this.num=1,this.getPicture(),this.text=this.$t("login.verify.waitingVerification"),this.status="default",this.showRefresh=!0},getPicture:function(){var e=this,t={captchaType:this.captchaType,clientUid:localStorage.getItem("point"),ts:Date.now()};this.Base.submit(null,{url:"captcha/get",data:t}).then((function(t){var i=t.data.resultData;"0000"===(null===i||void 0===i?void 0:i.repCode)?(e.pointBackImgBase=i.repData.originalImageBase64,e.backToken=i.repData.token,e.poinTextList=i.repData.wordList,e.text="".concat(e.$t("login.verify.pointTips"),"【").concat(e.poinTextList.join(","),"】")):e.text=i.repMsg,"6201"===(null===i||void 0===i?void 0:i.repCode)&&(e.pointBackImgBase=null)}))},pointTransform:function(e,t){return e.map((function(e){var i=Math.round(310*e.x/parseInt(t.imgWidth)),o=Math.round(155*e.y/parseInt(t.imgHeight));return{x:i,y:o}}))}}},Y=X,G=i(1001),Q=(0,G.Z)(Y,K,H,!1,null,"9fc2b13a",null),ee=Q.exports,te={name:"Vue2Verify",components:{VerifyPoints:ee},props:{captchaType:{type:String,required:!0},figure:{type:Number},arith:{type:Number},mode:{type:String,default:"pop"},vSpace:{type:Number},explain:{type:String},imgSize:{type:Object,default:function(){return{width:"310px",height:"155px"}}},blockSize:{type:Object},barSize:{type:Object}},data:function(){return{clickShow:!1,verifyType:void 0,componentType:void 0,defaultImg:i(803)}},computed:{instance:function(){return this.$refs.instance||{}},showBox:{get:function(){return"pop"!==this.mode||this.clickShow},set:function(e){return e}}},watch:{captchaType:{immediate:!0,handler:function(e){switch(e.toString()){case"clickWord":this.verifyType="",this.componentType="VerifyPoints";break}}}},mounted:function(){this.uuid()},methods:{success:function(e){this.$emit("success",e)},uuid:function(){for(var e=[],t="0123456789abcdef",i=0;i<36;i++)e[i]=t.substr(Math.floor(16*Math.random()),1);e[14]="4",e[19]=t.substr(3&e[19]|8,1),e[8]=e[13]=e[18]=e[23]="-";var o="slider-"+e.join(""),s="point-"+e.join("");localStorage.getItem("slider")||localStorage.setItem("slider",o),localStorage.getItem("point")||localStorage.setItem("point",s)},refresh:function(){this.instance.refresh&&this.instance.refresh()},closeBox:function(){this.clickShow=!1},show:function(){var e=this;"pop"===this.mode&&(this.clickShow=!0,this.$nextTick((function(){e.$refs.instance.getPicture()})))}}},ie=te,oe=(0,G.Z)(ie,U,q,!1,null,"261ae080",null),se=oe.exports,ne=i(63822),re={name:"loginForm",components:{Verify:se},data:function(){return{imgSrc:N(),slideCode:null,cryptInfo:null,captchaType:"clickWord",captchaParams:null}},computed:(0,c.Z)({},(0,ne.Se)({sysState:"getSysState",showSimpleCheckCode:"showSimpleCheckCode",simpleCheckCodeState:"simpleCheckCodeState",slideCheckCodeState:"slideCheckCodeState",showSlideCheckCode:"showSlideCheckCode",passwordRSAState:"passwordRSAState",showClickWordCheckCode:"showClickWordCheckCode"})),watch:{simpleCheckCodeState:function(e,t){this.refreshCode()}},mounted:function(){document.getElementById("username").focus(),this.cryptInfo=TaUtils.webStorage.getStorage("Ta$cacheCryptInfo","Ta$cacheCryptInfo",{isLocal:!0})},methods:{assistantDownload:function(){var e=this;(0,V.Z)({url:"/appeal/common/downloadFIle",type:"application/octet-stream",fileName:"医保小助手.zip"}).then((function(t){0==t.size&&e.$message.error("医保小助手安装包不可用,请与管理员联系")})).catch((function(t){0==t.size&&e.$message.error("医保小助手安装包不可用,请与管理员联系")}))},success:function(e){this.captchaParams=e,this.handleSubmit()},useVerify:function(){var e=this;this.showClickWordCheckCode?this.form.validateFields((function(t,i){t||e.$refs.verify.show()})):this.handleSubmit()},checkUser:function(e){var t,i=this,o=e.target.value;if(!o)return!1;!0===(null===(t=this.sysState)||void 0===t?void 0:t.encryptLoginId)&&(o=Base.cryptoAsymmetricFn(o)),o&&this.Base.submit(null,{url:"loginRestService/checkUser",showPageLoading:!1,withCredentials:!0,data:{username:o}}).then((function(e){i.$store.commit("setSysCfg",{sessionPasswordErrorNumber:e.data.sessionPasswordErrorNumber})}))},refreshCode:function(){this.showSimpleCheckCode&&(this.imgSrc=N()),this.showSlideCheckCode&&null!=this.slideCode&&this.slideCode.refreshSlideCheckCode()},handleSubmit:function(){var e,t=this,i=this.form.getFieldsValue();i=Object.assign(i,W),this.passwordRSAState&&(i.password=Base.cryptoAsymmetricFn(i.password)),!0===(null===(e=this.sysState)||void 0===e?void 0:e.encryptLoginId)&&(i.username=Base.cryptoAsymmetricFn(i.username)),this.showClickWordCheckCode&&(i.captchaVerification=this.captchaParams.captchaVerification),this.Base.submit(this.form,{url:"/login",data:i,autoValid:!0},{successCallback:function(e){t.showClickWordCheckCode&&t.$refs.verify.closeBox();var i=e.data["TA-JTOKEN"],o=e.data["TA-RJTOKEN"];null!==i&&void 0!==i&&""!==i&&TaUtils.setCookie(faceConfig.basePath+"TA-JTOKEN",i,0,"/"),null!==o&&void 0!==i&&""!==o&&TaUtils.setCookie(faceConfig.basePath+"TA-RJTOKEN",o,0,"/"),window.location.href="index.html"},failCallback:function(e){t.showClickWordCheckCode&&t.$refs.verify.closeBox(),"418"===e.errors[0].errorCode||(document.getElementById("username").focus(),null!==e.data.passwordDefaultNum&&t.$store.commit("setSysCfg",{sessionPasswordErrorNumber:e.data.passwordDefaultNum})),t.refreshCode()}})},gotoDeal:function(e,t){var i=e||window.event,o=i.target||i.srcElement;o.value?document.getElementById(t).focus():o.focus()},showModifyPasswordPane:function(){this.$emit("modifyPasswordPaneCtr")}}},ae=re,ce=(0,G.Z)(ae,A,B,!1,null,"e38e97ac",null),le=ce.exports,de=function(){var e=this,t=this,i=t.$createElement,o=t._self._c||i;return o("ta-form",{attrs:{"auto-form-create":function(t){e.form=t},layout:"vertical"}},[o("ta-form-item",{attrs:{"field-decorator-id":"loginId","field-decorator-options":{rules:[{required:!0,message:t.$t("login.userNameRequire")},{max:30,message:t.$t("login.userNameLength")}]}}},[o("ta-input",{attrs:{placeholder:t.$t("login.userName")}},[o("ta-icon",{attrs:{slot:"prefix",type:"user"},slot:"prefix"})],1)],1),o("ta-form-item",{attrs:{"field-decorator-id":"oldPassword","field-decorator-options":{rules:[{required:!0,message:t.$t("login.passwordOldRequire")}]}}},[o("ta-input",{attrs:{placeholder:t.$t("login.password"),type:"password",autocomplete:"new-password"}},[o("ta-icon",{attrs:{slot:"prefix",type:"lock"},slot:"prefix"})],1)],1),o("ta-form-item",{attrs:{"field-decorator-id":"newPassword","field-decorator-options":{rules:[{required:!0,message:t.$t("login.inputNewPasswordRequire")},{validator:t.checkPasswordNext}]}}},[o("ta-input",{attrs:{placeholder:t.$t("login.newPassword"),type:"password"},on:{blur:t.handleConfirmBlur}},[o("ta-icon",{attrs:{slot:"prefix",type:"lock"},slot:"prefix"})],1)],1),o("ta-form-item",{attrs:{"field-decorator-id":"newPasswordConfirm","field-decorator-options":{rules:[{required:!0,message:t.$t("login.inputNewPasswordAgain")},{validator:t.checkPasswordPre}]}}},[o("ta-input",{attrs:{placeholder:t.$t("login.inputNewPasswordAgain"),type:"password"}},[o("ta-icon",{attrs:{slot:"prefix",type:"lock"},slot:"prefix"})],1)],1),t.showSimpleCheckCode?o("ta-form-item",{attrs:{"field-decorator-id":"checkCode","field-decorator-options":{rules:[{required:!0,message:t.$t("login.verificationCodeRequire")}]}}},[o("ta-input",{staticStyle:{width:"60%"},attrs:{placeholder:t.$t("login.verificationCode")}},[o("ta-icon",{attrs:{slot:"prefix",type:"appstore"},slot:"prefix"})],1),o("img",{staticStyle:{width:"40%"},attrs:{src:t.imgSrc,title:t.$t("login.getVerificationCodeTips")},on:{click:function(e){return t.refreshCode()}}})],1):t._e(),o("div",{directives:[{name:"show",rawName:"v-show",value:t.showSlideCheckCode,expression:"showSlideCheckCode"}],attrs:{id:"modifyCheckCodeDiv"}},[o("div",{staticClass:"codeDragValidate-layout-div"},[o("div",{staticClass:"codeDragBar-drag-div"},[o("div",{staticClass:"codeDrag-win-div"},[o("div",{staticClass:"codeDrag-win-div-body"},[o("div",{staticClass:"codeDrag-bg-img-div"},[o("img",{staticClass:"codeDrag-code-img",attrs:{src:"#",alt:t.$t("login.backgroundImg")}}),o("img",{staticClass:"codeDrag-darg-img",attrs:{src:"#",alt:t.$t("login.dragImg")}})]),o("div",{staticClass:"codeDrag-code-refresh"})])]),o("div",{staticClass:"dragBar"},[o("span")]),o("div",{staticClass:"dragBar-inDrag-bg"}),o("div",{staticClass:"dragBar-base-bg"},[o("span",[t._v(t._s(t.$t("login.sureToChange")))])])])])]),t.showClickWordCheckCode?o("Verify",{ref:"verify",attrs:{captchaType:t.captchaType,imgSize:{width:"330px",height:"155px"}},on:{success:t.success}}):t._e(),o("ta-form-item",[o("span",{staticStyle:{float:"right"}},[o("ta-button",{on:{click:t.cancelModify}},[t._v(t._s(t.$t("login.cancel")))]),o("ta-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary"},on:{click:t.useVerify}},[t._v(t._s(t.$t("login.ok")))])],1)])],1)},ue=[],fe=i(57456),he={name:"modify-password",components:{Verify:se},props:{show:{type:Boolean},passState:{type:String}},data:function(){return{confirmDirty:!1,modes:0,imgSrc:N(),modifySlideCode:null,captchaType:"clickWord",captchaParams:null}},computed:(0,c.Z)({},(0,ne.Se)({showSimpleCheckCode:"showSimpleCheckCode",showSlideCheckCode:"showSlideCheckCode",passwordRSAState:"passwordRSAState",sysState:"getSysState",passwordLevel:"passwordLevel",showClickWordCheckCode:"showClickWordCheckCode"})),watch:{show:function(e,t){!0===e&&(this.form.resetFields(),this.refreshCode())}},mounted:function(){if(this.showSlideCheckCode){var e=this;this.modifySlideCode=new SlideCheckCode("modifyCheckCodeDiv",{successCallBack:function(){e.handleSubmit()}})}},methods:{success:function(e){this.captchaParams=e,this.handleSubmit()},useVerify:function(){var e=this;this.form.validateFields((function(t){t||(e.showClickWordCheckCode?e.$refs.verify.show():e.handleSubmit())}))},handleSubmit:function(){var e,t=this,i=this.form.getFieldsValue();this.passwordRSAState&&(i.oldPassword=Base.cryptoAsymmetricFn(i.oldPassword),i.newPassword=Base.cryptoAsymmetricFn(i.newPassword)),i.newPasswordConfirm&&delete i.newPasswordConfirm,!0===(null===(e=this.sysState)||void 0===e?void 0:e.encryptLoginId)&&(i.loginId=Base.cryptoAsymmetricFn(i.loginId)),this.showClickWordCheckCode&&(i.captchaVerification=this.captchaParams.captchaVerification),i.indexChangePass=this.passState,this.Base.submit(this.form,{url:"loginRestService/changePassword",data:i,autoValid:!0},{successCallback:function(e){t.showClickWordCheckCode&&t.$refs.verify.closeBox(),t.$message.success(t.$t("login.pswdChangeSuccessTips")),t.cancelModify()},failCallback:function(e){t.showClickWordCheckCode&&t.$refs.verify.closeBox(),t.refreshCode()}})},cancelModify:function(){this.form.resetFields(),this.$emit("update:show",!1)},handleConfirmBlur:function(e){var t=e.target.value;this.confirmDirty=this.confirmDirty||!!t},checkPasswordNext:function(e,t,i){var o=this.form;t&&this.confirmDirty&&o.validateFields(["newPasswordConfirm"],{force:!0});var s=(0,fe.Z)(t);1===this.passwordLevel?s!==this.passwordLevel?i(this.$t("login.passwordStrenthSimple")):i():s<this.passwordLevel?i(this.$t("login.passwordStrenth1")+this.passwordLevel+this.$t("login.passwordStrenth2")):i()},checkPasswordPre:function(e,t,i){var o=this.form.getFieldValue("newPassword");t&&t!==o?i(this.$t("login.passwordDifferentTips")):i()},refreshCode:function(){this.showSimpleCheckCode&&(this.imgSrc=N()),this.showSlideCheckCode&&null!=this.modifySlideCode&&this.modifySlideCode.refreshSlideCheckCode()}}},pe=he,me=(0,G.Z)(pe,de,ue,!1,null,null,null),ge=me.exports,ve=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",e._l(e.providerIds,(function(t,o){return i("ta-button",{key:o,staticStyle:{"margin-right":"5px"},attrs:{type:"primary",shape:"circle"},on:{click:function(i){return e.socialLogin(t)}}},[i("ta-icon",{attrs:{type:t}})],1)})),1)},we=[],ye={name:"social-List",data:function(){return{providerIds:[]}},mounted:function(){this.queryProviderIds()},methods:{queryProviderIds:function(){var e=this;this.Base.submit(null,{url:"connect/providerIds",method:"GET"},{successCallback:function(t){e.providerIds=t.data.providerIds}})},socialLogin:function(e){window.open(faceConfig.basePath+"/login/"+e,"_blank"),window.close()}}},Ce=ye,be=(0,G.Z)(Ce,ve,we,!1,null,"34bdda74",null),Se=be.exports,ke=function(){var e=this,t=this,i=t.$createElement,o=t._self._c||i;return o("ta-form",{attrs:{"auto-form-create":function(t){e.form=t},layout:"vertical"}},[o("ta-form-item",{attrs:{"field-decorator-id":"mobile","field-decorator-options":{rules:[{required:!0,message:"请输入手机号!"}]}}},[o("ta-input",{attrs:{placeholder:"手机号",disabled:t.disablePhoneInput}},[o("ta-icon",{attrs:{slot:"prefix",type:"phone"},slot:"prefix"})],1)],1),o("ta-form-item",{attrs:{"field-decorator-id":"smsCode","field-decorator-options":{rules:[{required:!0,message:"请输入验证码!"}]}}},[o("ta-input",{staticStyle:{width:"50%"},attrs:{placeholder:"验证码"}},[o("ta-icon",{attrs:{slot:"prefix",type:"lock"},slot:"prefix"})],1),o("ta-button",{staticStyle:{width:"50%","font-size":"12px",border:"none"},attrs:{disabled:!!t.time},on:{click:t.fireSendMessage}},[t._v(" "+t._s(0==t.time?"发送验证码":t.time+"秒后可重发")+" ")])],1),t.showUserSelector?o("ta-form-item",[o("ta-select",{attrs:{id:"loginIdSelector",options:t.loginIds,mode:"default",placeholder:"请选择用户名"},on:{select:t.handleChange}})],1):t._e(),o("ta-form-item",[o("ta-button",{attrs:{disabled:t.disableSubmitButton,id:"sbbtn",type:"primary",block:""},on:{click:t.handleSubmit,keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleSubmit.apply(null,arguments)}}},[t._v(" 立即登录 ")])],1)],1)},xe=[],Pe={name:"smsLoginForm",components:{},data:function(){return{showFireSendMessageButton:!0,showUserSelector:!1,disablePhoneInput:!1,disableSubmitButton:!0,time:!1,loginIds:[],loginId:""}},methods:{fireSendMessage:function(){var e=this,t=this.form.getFieldValue("mobile");null!=t&&this.Base.submit(null,{url:"loginRestService/checkMobile",data:{mobile:t}},{successCallback:function(i){e.loginIds=i.data.loginIds,e.showUserSelector=!0,e.sendSms(t),e.showFireSendMessageButton=!e.showFireSendMessageButton},failCallback:function(e){}})},sendSms:function(e){var t=this;this.Base.submit(null,{url:"/code/sms",data:{mobile:e}},{successCallback:function(e){t.disablePhoneInput=!0;var i=e.data.canResendTime,o=setInterval((function(){0===i?(clearInterval(o),t.time=!1):t.time=i--}),1e3)}})},handleSubmit:function(){var e=this.form.getFieldValue("mobile"),t=this.form.getFieldValue("smsCode");this.Base.submit(null,{url:"/authentication/mobile",data:{mobile:e,smsCode:t,loginId:this.loginId}},{successCallback:function(e){var t=e.data["TA-JTOKEN"],i=e.data["TA-RJTOKEN"];null!=t&&""!==t&&TaUtils.setCookie(faceConfig.basePath+"TA-JTOKEN",t,0,"/"),null!=i&&""!==i&&TaUtils.setCookie(faceConfig.basePath+"TA-RJTOKEN",i,0,"/"),window.location.href="index.html"},failCallback:function(e){}})},handleChange:function(e){this.loginId=e,this.disableSubmitButton=!1},gotoDeal:function(e,t){var i=e||window.event,o=i.target||i.srcElement;o.value?document.getElementById(t).focus():o.focus()}}},Te=Pe,_e=(0,G.Z)(Te,ke,xe,!1,null,"cf4a9d98",null),$e=_e.exports;a["default"].use(L.Z);var Ie={name:"login",components:{smsLoginForm:$e,modifyPassword:ge,loginForm:le,socialList:Se,Tooltip:L.Z},data:function(){return{showModifyPasswordPane:!1,showSocialList:!0,showSmsLogin:!1,support:[],supportText:"服务支持咨询:",version:"",commitId:""}},computed:(0,c.Z)({},(0,ne.Se)({openSocialLogin:"openSocialLogin",openSmsLogin:"openSmsLogin"})),watch:{showModifyPasswordPane:function(e,t){!1===e&&this.$refs.loginForm.refreshCode()}},created:function(){this.$store.dispatch("getSysCfg"),this.querysupport()},methods:{modifyPasswordPaneCtr:function(){this.showModifyPasswordPane=!this.showModifyPasswordPane},showSmsLoginMethod:function(){this.showSmsLogin=!this.showSmsLogin},querysupport:function(){var e=this;this.Base.submit(null,{url:"assistantWindow/querySupport",data:{},autoValid:!1},{successCallback:function(t){var i=t.data;if(void 0!==i.version&&null!==i.version&&""!==i.version&&(e.version=i.version),void 0!==i.commitId&&null!==i.commitId&&""!==i.commitId&&(e.commitId=i.commitId),i.namelenth>0&&i.numlenth>0){e.supportText="　　　服务支持咨询:";var o=1;o=i.namelenth>i.numlenth?i.namelenth:i.numlenth;for(var s=0;s<o;s++)i["supportname"+(0==s?"":s+1)]&&i["supportnum"+(0==s?"":s+1)]&&e.support.push({supportname:i["supportname"+(0==s?"":s+1)]+" ",supportnum:i["supportnum"+(0==s?"":s+1)]})}else e.supportText=""},failCallback:function(t){e.$message.error("查询失败")}})}}},Oe=Ie,Ee=(0,G.Z)(Oe,O,E,!1,null,"501e0754",null),Le=Ee.exports,Ae={namespace:!0,state:{sysCfg:{passwordRSA:!0,userCheckCode:!0,checkCodeType:"simple",passwordValidationErrorNumber:2,sessionPasswordErrorNumber:0,openSocialLogin:!1,openSmsLogin:!1}},getters:{getSysState:function(e){return e.sysCfg},simpleCheckCodeState:function(e){return!!(e.sysCfg.userCheckCode&&"simple"==e.sysCfg.checkCodeType&&e.sysCfg.sessionPasswordErrorNumber>=e.sysCfg.passwordValidationErrorNumber)},slideCheckCodeState:function(e){return!!(e.sysCfg.userCheckCode&&"slide"==e.sysCfg.checkCodeType&&e.sysCfg.sessionPasswordErrorNumber>=e.sysCfg.passwordValidationErrorNumber)},showSimpleCheckCode:function(e){return!(!e.sysCfg.userCheckCode||"simple"!=e.sysCfg.checkCodeType)},showSlideCheckCode:function(e){return!(!e.sysCfg.userCheckCode||"slide"!=e.sysCfg.checkCodeType)},showClickWordCheckCode:function(e){return!(!e.sysCfg.userCheckCode||"clickWord"!=e.sysCfg.checkCodeType)},passwordRSAState:function(e){return!0},openSocialLogin:function(e){return!0===e.sysCfg.openSocialLogin||"true"===e.sysCfg.openSocialLogin},openSmsLogin:function(e){return!0===e.sysCfg.openSmsLogin||"true"===e.sysCfg.openSmsLogin},passwordLevel:function(e){return e.sysCfg.passwordLevel>4||e.sysCfg.passwordLevel<1?3:e.sysCfg.passwordLevel}},actions:{getSysCfg:function(e){var t=e.commit;Base.submit(null,{url:"loginRestService/getConfig",withCredentials:!0,method:"GET"},{successCallback:function(e){t("setSysCfg",e.data.configMap||{})}})}},mutations:{setSysCfg:function(e,t){e.sysCfg=(0,c.Z)((0,c.Z)({},e.sysCfg),t)}}},Be=Ae;a["default"].use(ne.ZP);var Ze=new ne.ZP.Store((0,c.Z)({},Be)),Ne=Ze,ze=i(99916),je=i(76018);function De(e){var t={},o=i(74053);return e.keys().forEach((function(i){var s=i.match(/([A-Za-z0-9-_]+)\./i);if(s&&s.length>1){var n=s[1];o.keys().indexOf(i)>=0&&(t[n]=o(i)),t[n]=(0,c.Z)((0,c.Z)({},t[n]),e(i))}})),t}a["default"].use(je.Z);var Fe=function(e){var t,i=v.Z.createWebStorage("locale_mode",{isLocal:!0});return new je.Z({locale:null!==(t=i.get("locale"))&&void 0!==t?t:"zh_CN",fallbackLocale:"zh_CN",messages:De(e),silentTranslationWarn:!0})},Me={created:function(){window.pageVmObj=this}},Re=i(18583),We=i(73056);function Ve(e){return Ue.apply(this,arguments)}function Ue(){return Ue=(0,o.Z)(regeneratorRuntime.mark((function e(t){var i,s;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(i=TaUtils.webStorage.getStorage("Ta$cacheCryptInfo","Ta$cacheCryptInfo",{isLocal:!0}),!0===t||null===i||"{}"===JSON.stringify(i)){e.next=3;break}return e.abrupt("return",!1);case 3:return s=function(){var e=(0,o.Z)(regeneratorRuntime.mark((function e(t){var o,s,n,r;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(s=null===t||void 0===t||null===(o=t.data)||void 0===o?void 0:o.cryptoInfo,n=s.randomKeyLength||16,s.randomKey=We.Z.creat64Key(n),i=TaUtils.webStorage.createWebStorage("Ta$cacheCryptInfo",{isLocal:!0}),i.set("Ta$cacheCryptInfo",s),!((null===s||void 0===s?void 0:s.reqDataLevel)>=1&&(null===s||void 0===s?void 0:s.randomKeyLength)>=16)){e.next=9;break}return r=(0,d.K9)(s.asymmetricAlgo,s.randomKey),e.next=9,Base.submit(null,{url:"indexRestService/getToken",data:{key:r}}).then((function(e){var t;null!==e&&void 0!==e&&null!==(t=e.data)&&void 0!==t&&t.token&&TaUtils.setCookie("Client-ID",e.data.token,0,"/")}));case 9:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),e.next=6,Base.submit(null,{url:"indexRestService/getCryptoInfo",reqDataLevel:!1}).then(function(){var e=(0,o.Z)(regeneratorRuntime.mark((function e(t){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,s(t);case 2:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}());case 6:case"end":return e.stop()}}),e)}))),Ue.apply(this,arguments)}window.faceConfig=r.Z,(0,ze.Z)()||Promise.resolve().then(i.bind(i,30228)).then((function(e){var t=e.injectTheme;t(a["default"])}));var qe=Fe(i(89538));function Ke(){return He.apply(this,arguments)}function He(){return He=(0,o.Z)(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=window.location.search,null!==t&&""!==t&&(self.location=window.location.origin+"/hiiss-backend/thirdLoginService/loginNoPassword"+window.location.search+"&cainfo=13b40d334ef1b8a6b0faab53b99985b9475b0fc2647151523ba683b2536cc53b&loginway=token"),e.next=4,Ve(!0);case 4:new a["default"]({mixins:[Re.d,Me],store:Ne,render:function(e){return e(Le)},i18n:qe}).$mount("#app");case 5:case"end":return e.stop()}}),e)}))),He.apply(this,arguments)}Ke()},6442:function(e,t,i){"use strict";i.d(t,{MP:function(){return u},xz:function(){return d}});var o=i(89584),s=i(98878),n=i.n(s),r=i(14193),a=i(3032),c=i(60011);i(7638);a["default"].use(c.Z);var l={getAntdSerials:function(e){var t=new Array(9).fill().map((function(t,i){return n().varyColor.lighten(e,i/10)})),i=(0,r.R_)(e),o=n().varyColor.toNum3(e.replace("#","")).join(",");return t.concat(i).concat(o)},changeColor:function(e){var t=this,i=[];e.forEach((function(e){i.push.apply(i,(0,o.Z)(t.getAntdSerials(e)))}));var s={newColors:i,changeUrl:function(e){var t="/";return t="/hiiss-backend/template/",t.concat(e)}};return n().changer.changeColor(s)}};function d(e,t){var i;t&&(i=c.Z.loading(window.pageVmObj.$t("theme.changing"),0)),l.changeColor(e).then((function(){i&&i()}))}function u(e){var t=document.body;e?t.classList.add("colorWeak"):t.classList.remove("colorWeak")}},89580:function(e){e.exports={"primary-color":"#13c2c2","link-color":"#13c2c2"}},42896:function(e){e.exports={"primary-color":"#1890FF"}},23379:function(e){e.exports={"primary-color":"#006dd9","link-color":"#1B65B9","success-color":"#52c41a","warning-color":"#faad14","error-color":"#fe5c65","text-color":"rgba(0, 0, 0, 0.7)","heading-color":"rgba(0, 0, 0, 0.9)","input-color":"rgba(0, 0, 0, 0.9)","shadow-color":"rgba(0, 0, 0, 0.3)","border-color-base":"#b2b2b2","disabled-bg":"#ebebeb","disabled-color":"rgba(0, 0, 0, 0.5)","modal-mask-bg":"rgba(0, 0, 0, 0.75)","input-addon-bg":"#ebebeb","border-color-split":"#b2b2b2","background-color-light":"#ebebeb","item-hover-bg":"#d6e7f9","item-active-bg":"#d6e7f9","tabs-card-head-background":"#fafafa","pagination-item-active-bg":"#006dd9","pagination-item-active-color":"#fff","table-row-hover-bg":"#d6e7f9","text-color-secondary":"#606266","background-color-base":"#F0F2F5"}},62977:function(e){e.exports={"primary-color":"#1DA57A","link-color":"#1DA57A"}},97369:function(e,t,i){var o=i(10641)["default"];e.exports=function(e){var t=i(83574)("./".concat(e,".js"));return o({},t)}},77745:function(e){e.exports={"primary-color":"#FA541C","link-color":"#FA541C"}},93344:function(e){e.exports={"primary-color":"#722ED1","link-color":"#722ED1"}},21301:function(e){e.exports={"primary-color":"#F5222D","link-color":"#F5222D","success-color":"#52c41a"}},73492:function(e){e.exports={"primary-color":"#2364F8","link-color":"#2364F8","success-color":"#67C23A","warning-color":"#E6A23C","error-color":"#F56C6C","text-color":"#303133","text-color-secondary":"#606266","border-color-base":"#DCDFE6","border-color-split":"#EBEEF5","background-color-base":"#F0F2F5","background-color-light":"#F5F7FA","font-family":"Microsoft YaHei,Hiragino Sans GB,Pingfang SC,Arial,Helvetica Neue,Helvetica","font-variant-base":"tabular-nums","font-size-base":"14px","font-size-lg":"16px","font-size-sm":"12px"}},74053:function(e,t,i){var o={"./en_US.json":31613,"./zh_CN.json":58873};function s(e){var t=n(e);return i(t)}function n(e){if(!i.o(o,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return o[e]}s.keys=function(){return Object.keys(o)},s.resolve=n,e.exports=s,s.id=74053},89538:function(e,t,i){var o={"./en_US.json":46474,"./zh_CN.json":17723};function s(e){var t=n(e);return i(t)}function n(e){if(!i.o(o,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return o[e]}s.keys=function(){return Object.keys(o)},s.resolve=n,e.exports=s,s.id=89538},83574:function(e,t,i){var o={"./dark.js":89580,"./default.js":42896,"./example.js":23379,"./green.js":62977,"./index.js":97369,"./orange.js":77745,"./purple.js":93344,"./red.js":21301,"./ybblue.js":73492};function s(e){var t=n(e);return i(t)}function n(e){if(!i.o(o,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return o[e]}s.keys=function(){return Object.keys(o)},s.resolve=n,e.exports=s,s.id=83574},90266:function(e){"use strict";e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACcAAAAjCAYAAAAXMhMjAAAC6ElEQVRYhb1XPW4TQRR+WO7jG2QtDmBzAtsnCDewkSigAdPR4VwANqKhXBpajERFw0ZQIsWWKKCzD4DkdCnRs74xk5c3b2bWNp8UKR7Pznz7fe/P9+4/vqEEPCSiiohOUjYnYI0zF9bWVsJBBRF9PCAxxikRzYmoY21KIVcejtMtMMGptSFGbkhEZ8fhtsUUzqiIkTuWag4cKrMm5CZE1DsarX8Yw6E7CJHr/AfVfKh3hcjNlOzk9L8+EBk+68L73INTUXIcoM/F2jnWWf7lnsSYVB/J8N5bL2Vp0chVxtoCBD81IMVqjUBqgzU/U09kaZHk+OKBcvDK+58P5ur+IoPYOdSqI/te+YQlOU21EEooYcUhh8ADxPDGPm6HXXL45Gao2toFIdRQRNvj1LL6p+bSmSstbSx0jFYSe+OVF+B9nDWLNfUI2MHCkSszG7umSG5d1Jr+pefGpIUAHBuHSBKsyhXezpwqAPcic2VdYgBLueZVLavxAiFbx3jL0POuy1yhyKa8CIHgNjFTRibtUoeeV/t8DLEui3noHImtym1jg4O0VdrBsfoVlX9jhEnsHIdHTrk2rFkHyghl1CdLpZRz1oi1XaF2tpoTqUAsRlMwxG8IB7WDOHJzpLEGaUdI4RhqkKoRBj306G6og/gJcWdkAVJtjaECqQGEGEE9v29zknzRyK3EjKVhH0tPodTIU9AH2/qNiH5p5Ajy+o1cWt2E3DXmti6U0iaTKeohZ/6zELmN9YMjE0uUhQIhs1IeL0D2DT6/9r/UinCJtNYQU26JOa8LmyojZt1w4CaTn7I/h4rwBMErLdDIXSLb5wF1JIYgLbP+qdwYIlcboziTWWBPnZHNfSijzXB81/dUcoQgfSLWmsRjgedCk8+fUHexyLFFLxuQcXCjjzWOMT6EYjyl8edigj/NPonffuk4FrnCI5XT3t5ZX+5DjlsNF9VUlSTexkb7puQ489xU4bI2Bz+I6LP5ABH9BSA9mSNG/bMOAAAAAElFTkSuQmCC"},20010:function(e,t,i){"use strict";e.exports=i.p+"img/taimg.65ddb3bb.png"},803:function(e,t,i){"use strict";e.exports=i.p+"img/default.910183a3.jpg"},42480:function(){}},t={};function i(o){var s=t[o];if(void 0!==s)return s.exports;var n=t[o]={id:o,loaded:!1,exports:{}};return e[o].call(n.exports,n,n.exports,i),n.loaded=!0,n.exports}i.m=e,function(){i.amdO={}}(),function(){var e=[];i.O=function(t,o,s,n){if(!o){var r=1/0;for(d=0;d<e.length;d++){o=e[d][0],s=e[d][1],n=e[d][2];for(var a=!0,c=0;c<o.length;c++)(!1&n||r>=n)&&Object.keys(i.O).every((function(e){return i.O[e](o[c])}))?o.splice(c--,1):(a=!1,n<r&&(r=n));if(a){e.splice(d--,1);var l=s();void 0!==l&&(t=l)}}return t}n=n||0;for(var d=e.length;d>0&&e[d-1][2]>n;d--)e[d]=e[d-1];e[d]=[o,s,n]}}(),function(){i.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return i.d(t,{a:t}),t}}(),function(){var e,t=Object.getPrototypeOf?function(e){return Object.getPrototypeOf(e)}:function(e){return e.__proto__};i.t=function(o,s){if(1&s&&(o=this(o)),8&s)return o;if("object"===typeof o&&o){if(4&s&&o.__esModule)return o;if(16&s&&"function"===typeof o.then)return o}var n=Object.create(null);i.r(n);var r={};e=e||[null,t({}),t([]),t(t)];for(var a=2&s&&o;"object"==typeof a&&!~e.indexOf(a);a=t(a))Object.getOwnPropertyNames(a).forEach((function(e){r[e]=function(){return o[e]}}));return r["default"]=function(){return o},i.d(n,r),n}}(),function(){i.d=function(e,t){for(var o in t)i.o(t,o)&&!i.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})}}(),function(){i.e=function(){return Promise.resolve()}}(),function(){i.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()}(),function(){i.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)}}(),function(){i.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}}(),function(){i.nmd=function(e){return e.paths=[],e.children||(e.children=[]),e}}(),function(){i.j=4535}(),function(){i.p="/hiiss-backend/template/"}(),function(){var e={4535:0,6073:0,2601:0};i.O.j=function(t){return 0===e[t]};var t=function(t,o){var s,n,r=o[0],a=o[1],c=o[2],l=0;if(r.some((function(t){return 0!==e[t]}))){for(s in a)i.o(a,s)&&(i.m[s]=a[s]);if(c)var d=c(i)}for(t&&t(o);l<r.length;l++)n=r[l],i.o(e,n)&&e[n]&&e[n][0](),e[n]=0;return i.O(d)},o=self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[];o.forEach(t.bind(null,0)),o.push=t.bind(null,o.push.bind(o))}();var o=i.O(void 0,[3736,6716,807,8350,6258,5204,5956,8155,5088,1803,856,6801,4381,910,3426,602],(function(){return i(68598)}));o=i.O(o)})();
(typeof window=='undefined'?global:window).tc_cfg_09272470305613778={"url":"css/theme-colors-ea095f1b.css","colors":["#1b65b9","#3274c0","#4984c7","#5f93ce","#76a3d5","#8db2dc","#a4c1e3","#bbd1ea","#d1e0f1","#ebf4fa","#bbd9ed","#8dbce0","#639fd4","#3e82c7","#1b65b9","#0f4894","#052f6e","#001a47","#000b21","27,101,185"]};

(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[3475],{88412:function(e,t,a){"use strict";var i=a(26263),o=a(36766),l=a(1001),n=(0,l.Z)(o.Z,i.s,i.x,!1,null,"5e7ef0ae",null);t["Z"]=n.exports},89913:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return w}});var i=function(){var e=this,t=this,a=t.$createElement,i=t._self._c||a;return i("div",{staticClass:"fit"},[i("ta-border-layout",{staticStyle:{},attrs:{"layout-type":"fixTop"}},[i("div",{attrs:{slot:"header"},slot:"header"},[i("div",{staticClass:"query-header"},[i("div",{ref:"formBox",staticClass:"query-header-left",style:t.formBoxStyle},[i("ta-form",{attrs:{autoFormCreate:function(t){e.baseInfoForm=t},col:t.col,layout:"horizontal","label-width":"80px",formLayout:!0}},[i("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:this.menuConfig.dateFlag,expression:"this.menuConfig.dateFlag"}],attrs:{"field-decorator-id":"dateFlag","init-value":t.onRadioValue}},[i("span",{attrs:{slot:"label"},slot:"label"},[t._v(" 时间类型 ")]),i("ta-radio-group",{staticStyle:{width:"100%"},on:{change:t.onRadioChange},model:{value:t.onRadioValue,callback:function(e){t.onRadioValue=e},expression:"onRadioValue"}},[i("ta-radio-button",{staticStyle:{width:"50%","text-align":"center"},attrs:{value:"audit"}},[t._v("审核时间")]),i("ta-radio-button",{staticStyle:{width:"50%","text-align":"center"},attrs:{value:"outpa"}},[t._v("出院时间")])],1)],1),i("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:this.menuConfig.dateFlag,expression:"this.menuConfig.dateFlag"}],attrs:{"field-decorator-id":"allDate","init-value":t.rangeValue}},[i("span",{attrs:{slot:"label"},slot:"label"},[t._v(" 时间范围 ")]),i("ta-range-picker",{attrs:{"allow-one":!0}})],1),i("ta-form-item",{attrs:{"field-decorator-id":"flag",label:"出院场景","field-decorator-options":{rules:[{required:!0,message:"必须选取出院场景"}]}}},[i("ta-select",{attrs:{showSearch:"",placeholder:"请选择出院场景","collection-type":"AAE500","collection-filter":t.filterList,reverseFilter:!0}})],1),i("ta-form-item",{attrs:{"field-decorator-id":"medinsCode",label:"院区标识",disabled:t.paramsDisable.akb020}},[i("ta-select",{attrs:{showSearch:"",placeholder:"院区标识筛选",options:t.hosList,"allow-clear":""},on:{change:t.fnQueryDept}})],1),i("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:this.menuConfig.patientInfo,expression:"this.menuConfig.patientInfo"}],attrs:{"field-decorator-id":"patientInfo",label:"患者信息"}},[i("ta-input",{attrs:{placeholder:"请输入住院号、就诊号或姓名"},on:{pressEnter:t.fnQuery}})],1),i("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:this.menuConfig.aaz307,expression:"this.menuConfig.aaz307"}],attrs:{"field-decorator-id":"aaz307",label:"出院科室",disabled:t.paramsDisable.aaz307}},[i("ta-select",{attrs:{showSearch:"","show-search":!0,placeholder:"出院科室筛选",options:t.ksList,"allow-clear":""}})],1),i("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:this.menuConfig.ykz041,expression:"this.menuConfig.ykz041"}],attrs:{"field-decorator-id":"ykz041",label:"审核人",disabled:t.paramsDisable.ykz041}},[i("ta-input",{attrs:{placeholder:"请输入审核人"}})],1),i("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:this.menuConfig.aae140,expression:"this.menuConfig.aae140"}],attrs:{"field-decorator-id":"aae140",label:"险种类型"}},[i("ta-select",{attrs:{showSearch:"",placeholder:"险种类型筛选","collection-type":"AAE140","dropdown-match-select-width":!1,"allow-clear":""}})],1)],1)],1),i("div",{staticStyle:{display:"flex","margin-left":"10px",float:"right"}},[i("div",{staticClass:"ctrl-btn",on:{click:t.formShowAllChange}},[i("a",[t._v(t._s(t.formShowAll?"收起":"展开"))]),t.formShowAll?i("ta-icon",{attrs:{type:"caret-up"}}):i("ta-icon",{attrs:{type:"caret-down"}})],1),i("div",{staticStyle:{"margin-right":"10px"}},[i("ta-icon",{staticStyle:{color:"#3382f5","font-size":"16px","margin-top":"8px",cursor:"pointer",float:"left"},attrs:{type:"setting"},on:{click:t.configMenu}})],1),i("ta-button",{attrs:{type:"primary",icon:"search"},on:{click:t.fnQuery}},[t._v("查询")]),i("ta-button",{attrs:{type:"default",icon:"redo"},on:{click:t.fnReset}},[t._v("重置")])],1),i("div",{directives:[{name:"show",rawName:"v-show",value:t.formShowAll,expression:"formShowAll"}],staticStyle:{"margin-top":"25px",float:"right"}},[i("ta-checkbox",{staticStyle:{"margin-left":"30px"},on:{change:t.showRecords}},[t._v(" 显示历次审核记录 ")])],1)])]),i("div",{staticClass:"fit content-box"},[i("ta-title",{attrs:{title:"查询结果"}}),i("div",{staticClass:"content-table"},[i("ta-big-table",{ref:"infoTableRef",attrs:{columns:t.infoColumns,data:t.infoTableData,"control-column":t.showHiddenOrSortColumn,border:"",height:"auto","highlight-hover-row":"","highlight-current-row":"","auto-resize":"","show-overflow":"",resizable:""},scopedSlots:t._u([{key:"yd",fn:function(e){var a=e.row;return["0"==a.yd?i("span",{staticStyle:{color:"#0f990f"}},[i("ta-icon",{attrs:{type:"check-circle",theme:"twoTone","two-tone-color":"#52c41a"}}),t._v("审核通过")],1):"1"==a.yd?i("span",{staticStyle:{color:"#F59A23"}},[i("ta-icon",{attrs:{type:"question-circle",theme:"twoTone","two-tone-color":"#F59A23"}}),t._v("可疑提醒")],1):"2"==a.yd?i("span",{staticStyle:{color:"#FF0000"}},[i("ta-icon",{attrs:{type:"exclamation-circle",theme:"twoTone","two-tone-color":"#FF0000"}}),t._v("违规提醒")],1):i("span",{staticStyle:{color:"#0f990f"}},[i("ta-icon",{attrs:{type:"check-circle",theme:"twoTone","two-tone-color":"#52c41a"}}),t._v("无限制条件")],1)]}},{key:"operate",fn:function(e){var a=e.row;return[i("a",{on:{click:function(e){return t.fnSearch(a)}}},[i("ta-icon",{staticStyle:{color:"blue","margin-left":"5px"},attrs:{type:"search"}}),t._v(" 查看明细")],1)]}}])})],1),i("div",{staticClass:"content-box-footer"},[i("ta-pagination",{ref:"infoPageRef",staticStyle:{position:"absolute",right:"6px",bottom:"6px","margin-right":"100px"},attrs:{dataSource:t.infoTableData,params:t.infoPageParams,defaultPageSize:100,pageSizeOptions:["100","200","500","1000","1500"],url:"dischargeClinic/queryAuditResults"},on:{"update:dataSource":function(e){t.infoTableData=e},"update:data-source":function(e){t.infoTableData=e}}}),i("ta-button",{staticStyle:{position:"absolute",right:"6px",bottom:"6px"},attrs:{type:"primary",icon:"download"},on:{click:t.exportExcel}},[t._v("导出")])],1),i("div",[i("ta-modal",{attrs:{title:"配置菜单项",height:"300px",width:"385px"},on:{ok:t.handleConfigOk,cancel:t.handleConfigCancel},model:{value:t.configVisible,callback:function(e){t.configVisible=e},expression:"configVisible"}},[i("span",[t._v("配置查询条件:")]),i("br"),i("ta-checkbox-group",{attrs:{value:t.checkedList},on:{change:t.onConfigChange}},[i("ta-row",t._l(t.configList,(function(e,a){return i("ta-col",{key:a,attrs:{span:8}},[i("ta-checkbox",{attrs:{value:e.value}},[t._v(t._s(e.label))])],1)})),1)],1),i("div",[i("ta-checkbox",{attrs:{indeterminate:t.indeterminate,checked:t.checkAll},on:{change:t.onCheckAllChange}},[t._v(" 全部选中 ")])],1),i("div",[i("strong",{style:{marginRight:8}},[t._v("排序模式:")]),i("br"),i("ta-radio-group",{model:{value:t.ordermod,callback:function(e){t.ordermod=e},expression:"ordermod"}},[i("ta-radio",{attrs:{value:"ASC"}},[t._v("正序")]),i("ta-radio",{attrs:{value:"DESC"}},[t._v("倒序")])],1)],1),i("div",[i("strong",{style:{marginRight:8}},[t._v("选择排序顺序:")]),i("br"),t._l(t.tags,(function(e,a){return[i("ta-checkable-tag",{key:e,attrs:{checked:t.selectedTags.indexOf(e)>-1},on:{change:function(a){return t.handleTagChange(e,a)}}},[t._v(" "+t._s(e)+" ")])]}))],2),i("div",[i("strong",{style:{marginRight:8}},[t._v("排序顺序展示:")]),i("br"),t._l(t.tags2,(function(e,a){return[i("ta-tooltip",{key:e,attrs:{title:e}},[i("ta-tag",{key:e,attrs:{closable:-1!==a,afterClose:function(){return t.handleTagClose(e)}}},[t._v(" "+t._s(e.slice(0,-1))+" ")])],1)]}))],2)],1)],1)],1)])],1)},o=[],l=a(66347),n=a(89584),s=a(48534),r=a(95082),c=(a(36133),a(88412)),u=a(36797),d=a.n(u),f=a(22722),h=a(83231),m=a(55115);a(55067);m.w3.prototype.Base=Object.assign(m.w3.prototype.Base,(0,r.Z)({},f.Z));var g=[],p=[],b={name:"dischargeClinic",components:{TaTitle:c.Z},data:function(){var e=[{label:"时间筛选",value:"dateFlag"},{label:"患者信息",value:"patientInfo"},{label:"出院科室",value:"aaz307"},{label:"审核人",value:"ykz041"},{label:"险种类型",value:"aae140"}],t=[{type:"seq",title:"序号",width:60,align:"center"},{title:"医院编码",field:"akb020",align:"left",width:80,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"医院名称",field:"akb021",align:"left",width:80,visible:!1,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"就诊号",field:"akc190",align:"left",width:120,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"住院号",field:"akc191",align:"left",sortable:!0,width:120,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"患者姓名",field:"aac003",align:"left",sortable:!0,width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"性别",field:"aac004",align:"center",width:60,collectionType:"SEX",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"年龄",field:"aac017",align:"right",width:60,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"医保类型",field:"aae141",align:"center",sortable:!0,width:100,overflowTooltip:!0,collectionType:"AAE141"},{title:"险种类型",field:"aae140",align:"left",width:100,sortable:!0,collectionType:"AAE140",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"入院时间",field:"aae030",align:"center",width:255,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"出院时间",field:"aae031",align:"center",width:255,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"主诊断名称",field:"diag_name_main",align:"left",visible:!1},{title:"主诊断编码",field:"diag_code_main",align:"left",visible:!1},{title:"其他诊断名称1",field:"diag_name_0",align:"left",visible:!1},{title:"其他诊断编码1",field:"diag_code_0",align:"left",visible:!1},{title:"其他诊断名称2",field:"diag_name_1",align:"left",visible:!1},{title:"其他诊断编码2",field:"diag_code_1",align:"left",visible:!1},{title:"其他诊断名称3",field:"diag_name_2",align:"left",visible:!1},{title:"其他诊断编码3",field:"diag_code_2",align:"left",visible:!1},{title:"其他诊断名称4",field:"diag_name_3",align:"left",visible:!1},{title:"其他诊断编码4",field:"diag_code_3",align:"left",visible:!1},{title:"其他诊断名称5",field:"diag_name_4",align:"left",visible:!1},{title:"其他诊断编码5",field:"diag_code_4",align:"left",visible:!1},{title:"出院科室",field:"aae386",align:"left",width:120,sortable:!0,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"医疗费用总额",field:"akc264",width:100,align:"left",visible:!1},{title:"审核时间",field:"aae040",align:"center",width:255,sortable:!0,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"审核人",field:"ykz041",align:"left",width:80,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"是否完成审核",field:"aze003",align:"center",width:110,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"操作",field:"operate",align:"center",fixed:"right",width:110,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell,scopedSlots:{customRender:"operate"},customRender:{default:"operate"}}];return{col:{xs:1,sm:2,md:2,lg:4,xl:4,xxl:4},rangeValue:[this.Base.getMoment((new Date).toISOString().slice(0,10),"YYYY-MM-DD"),this.Base.getMoment((new Date).toISOString().slice(0,10),"YYYY-MM-DD")],hosList:[],ksList:[],doctorList:[],ksTableData:g,infoColumns:t,infoTableData:p,akb020:"",aaz307:"",expandedRowKeys:[],showRecord:"0",permissions:null,formShowAll:!0,onRadioValue:"audit",tags:["患者住院号","出院科室"],tags2:[],selectedTags:[],ordermod:"ASC",showHiddenOrSortColumn:{themeColor:"red",icon:"appstore",showHiddenConfig:{open:!0,disabledHiddenColumns:function(e){return!1}},columnSortConfig:{open:!0,onMove:function(e,t){t.dragColumn,t.dropColumn;return!0},dragEnd:function(e,t){t.dragColumn,t.resultColumns}},successCallback:this.fnSaveTableTitle,disabledControlCol:function(e){}},ykz040:null,filterList:"",paramsDisable:{akb020:!1,aaz307:!1,ykz041:!1},menuConfig:{patientInfo:!0,aaz307:!0,ykz041:!0,dateFlag:!0,aae140:!0},configVisible:!1,indeterminate:!1,checkAll:!0,checkedList:[],allReceiveData:{},bfcheckedList:[],configList:e}},created:function(){var e=this;return(0,s.Z)(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$nextTick((function(){e.fnQueryTableTitle()}));case 2:case"end":return t.stop()}}),t)})))()},mounted:function(){var e=this;return(0,s.Z)(regeneratorRuntime.mark((function t(){var a,i;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.$route.query.params&&(a=JSON.parse(e.$route.query.params),a.allDate=a.allDate.map((function(t){var a=new Date(t);return a.setDate(a.getDate()+1),e.Base.getMoment(a.toISOString(),"YYYY-MM-DD")})),e.$route.query.aaz307&&(a.aaz307=e.$route.query.aaz307),a.flag=a.aae500,e.allReceiveData=a,e.baseInfoForm.setFieldsValue(a)),t.next=4,h.Z.permissionCheck();case 4:e.permissions=t.sent,i=["5","6","7","18"],h.Z.getAa01AAE500StartStop({aaz499s:JSON.stringify(i)},(function(t){var a=t.data.aae500List;a.includes(12)&&a.push(13),e.filterList=a.join(",");var i=e.allReceiveData.aae500?e.allReceiveData.aae500:a[0].toString();e.baseInfoForm.setFieldsValue({flag:i})})),e.fnQueryHos();case 8:case"end":return t.stop()}}),t)})))()},computed:{formBoxStyle:function(){return this.formShowAll?{height:"auto"}:{height:"50px"}}},methods:{onRadioChange:function(e){this.onRadioValue=e.target.value},handleTagClose:function(e){this.handleTagChange(e.slice(0,-1),!1)},handleTagChange:function(e,t){var a=this.selectedTags;this.selectedTags=t?[].concat((0,n.Z)(a),[e]):a.filter((function(t){return t!==e})),t?this.tags2.push(e+"x"):this.tags2=this.tags2.filter((function(t){return t!==e+"x"}))},handleConfigOk:function(e){var t=this;this.configVisible=!1,this.bfcheckedList=this.checkedList,this.configList.forEach((function(e){t.menuConfig[e.value]=t.checkedList.includes(e.value)}));var a=top.indexTool.getActiveTabMenuId(),i=top.indexTool.getUserInfo().loginId,o={flag:"menu",menu:JSON.stringify(this.checkedList),sorting:JSON.stringify(this.tags2),ordermod:this.ordermod,resourceid:a,loginid:i};h.Z.insertTableColumShow(o,(function(e){"success"===e?t.$message.success("保存成功"):t.$message.error("保存失败")}))},handleConfigCancel:function(){this.visible=!1,this.checkedList=this.bfcheckedList,this.bfcheckedList.length<this.configList.length&&0!==this.bfcheckedList.length?(this.checkAll=!1,this.indeterminate=!0):0===this.bfcheckedList.length?(this.checkAll=!1,this.indeterminate=!1):(this.checkAll=!0,this.indeterminate=!1)},onConfigChange:function(e){e.length<this.configList.length&&0!==e.length?(this.checkAll=!1,this.indeterminate=!0):0===e.length?(this.checkAll=!1,this.indeterminate=!1):(this.checkAll=!0,this.indeterminate=!1),this.checkedList=e},onCheckAllChange:function(e){var t=e.target.checked;t?(this.checkAll=!0,this.checkedList=this.configList.map((function(e){return e.value}))):(this.checkAll=!1,this.checkedList=[]),this.indeterminate=!1},configMenu:function(){this.configVisible=!0,this.bfcheckedList===this.checkedList?this.bfcheckedList=this.checkedList:this.checkedList=this.bfcheckedList,this.onConfigChange(this.checkedList)},fnQueryTableTitle:function(){var e=this,t=top.indexTool.getActiveTabMenuId(),a=top.indexTool.getUserInfo().loginId,i=[];this.Base.submit(null,{url:"miimCommonRead/selectTableColumShow",data:{resourceid:t,loginid:a},autoValid:!1},{successCallback:function(t){if(t.data.list.length>0&&t.data.list[0].ordermod&&(e.ordermod=t.data.list[0].ordermod),t.data.list.length>0&&t.data.list[0].sorting&&(e.selectedTags=JSON.parse(t.data.list[0].sorting).map((function(e){return e.slice(0,-1)})),e.tags2=JSON.parse(t.data.list[0].sorting)),t.data.list.length>0&&t.data.list[0].menu?e.checkedList=JSON.parse(t.data.list[0].menu):e.checkedList=e.configList.map((function(e){return e.value})),e.bfcheckedList=e.checkedList,e.configList.forEach((function(t){e.menuConfig[t.value]=e.checkedList.includes(t.value)})),t.data.list.length>0&&t.data.list[0].colum){var a=JSON.parse(t.data.list[0].colum);i=a.map((function(e){return e.title}));var o=e.$refs.infoTableRef.getTableColumn().fullColumn,l=[];o.forEach((function(e){var i=a.find((function(t){return t.title===e.title}));i&&(e.visible=i.visible);var o=e;t.data.akc191Title.length>0&&"akc191"===o.property&&(o.title=t.data.akc191Title[0].label),l.push(o)})),i.length>0&&l.sort((function(e,t){return"序号"===e.title?-1:"序号"===t.title?1:i.indexOf(e.title)-i.indexOf(t.title)})),e.$refs.infoTableRef.loadColumn(l)}},failCallback:function(t){e.$message.error("查询表头失败")}})},fnSaveTableTitle:function(e){var t=this,a=(e.table,e.resultColumnsList),i=a,o=[];i.forEach((function(e){var t=e.title,a=e.visible;e.type;t&&o.push({title:t,visible:a})}));var l=top.indexTool.getActiveTabMenuId(),n=top.indexTool.getUserInfo().loginId,s={colum:JSON.stringify(o),flag:"column",resourceid:l,loginid:n};h.Z.insertTableColumShow(s,(function(e){"success"===e?t.$message.success("保存成功"):t.$message.error("保存失败")}))},fnReset:function(){this.formShowAll=!1,this.baseInfoForm.resetFields(),this.baseInfoForm.setFieldsValue({flag:"5"}),this.fnQuery()},formShowAllChange:function(){this.formShowAll=!this.formShowAll},createDate:function(){var e=new Date;return e.setMonth(e.getMonth()-2),e},fnQueryHos:function(){var e=this,t={url:"/miimCommonRead/queryHospRpcList",autoValid:!0},a={successCallback:function(t){e.hosList=t.data.resultData,void 0!==e.allReceiveData.akb020?(e.allReceiveData.akb020&&(e.hosList=e.hosList.filter((function(t){return t.value==e.allReceiveData.akb020}))),e.baseInfoForm.setFieldsValue({medinsCode:e.allReceiveData.akb020}),e.akb020=e.allReceiveData.akb020):(e.baseInfoForm.setFieldsValue({medinsCode:e.hosList[0].value}),null!==e.permissions&&e.permissions.akb020Set.size>0&&(e.hosList=e.hosList.filter((function(t){return e.permissions.akb020Set.has(t.value)})),e.baseInfoForm.setFieldsValue({medinsCode:e.hosList[0].value})),e.akb020=e.hosList[0].value),e.fnQueryDept(e.akb020)},failCallback:function(t){e.$message.error("医院数据加载失败")}};this.Base.submit(null,t,a)},fnQueryDept:function(e){var t=this;this.akb020=e||"",this.Base.submit(null,{url:"miimCommonRead/queryDepartRpcList",data:{akb020:this.akb020,hospDeptType:"I"},autoValid:!1},{successCallback:function(e){var a=t.allReceiveData.aaz307;a||t.baseInfoForm.resetFields("aaz307"),t.ksList=e.data.resultData,t.setPermission()},failCallback:function(e){t.$message.error("科室数据加载失败")}})},setPermission:function(){var e=this;this.paramsDisable.akb020=this.permissions.akb020Disable,this.paramsDisable.aaz307=this.permissions.aaz307Disable,this.paramsDisable.aaz263=this.permissions.aaz263Disable,this.permissions&&(this.permissions.aaz307Set.size>0&&(this.ksList=this.ksList.filter((function(t){return e.permissions.aaz307Set.has(t.value)})),this.permissions.aaz307Disable&&this.baseInfoForm.setFieldsValue({aaz307:this.ksList[0].value})),this.permissions.aaz263Set.size>0&&(this.doctorList=this.doctorList.filter((function(t){return e.permissions.aaz263Set.has(t.value)})),this.permissions.aaz263Disable&&(this.ykz040=this.doctorList[0].value))),this.fnQuery()},fnQueryDocter:function(e){var t=this,a={akb020:this.akb020};e&&(a.departCode=e),this.Base.submit(null,{url:"miimCommonRead/queryDoctorRpcList",data:a,autoValid:!1},{successCallback:function(e){var a=t.allReceiveData.aaz263;a||t.baseInfoForm.resetFields("aaz263"),t.doctorList=e.data.resultData},failCallback:function(e){t.$message.error("医师数据加载失败")}})},disabledStartDate:function(e){return e.format("YYYYMMDD")>this.baseInfoForm.getFieldValue("endDate").format("YYYYMMDD")},disabledEndDate:function(e){return e=e.format("YYYYMMDD"),e>d()().startOf("day").format("YYYYMMDD")||e<this.baseInfoForm.getFieldValue("startDate").format("YYYYMMDD")},infoPageParams:function(){var e=this.baseInfoForm.getFieldsValue();return e.allDate&&(e.allDate[0]&&("audit"===this.onRadioValue?e.auditStartDate=e.allDate[0].format("YYYY-MM-DD"):e.startDate=e.allDate[0].format("YYYY-MM-DD")),e.allDate[1]&&("audit"===this.onRadioValue?e.auditEndDate=e.allDate[1].format("YYYY-MM-DD"):e.endDate=e.allDate[1].format("YYYY-MM-DD"))),e.aaz307=e.aaz307,e.akb020=e.medinsCode,e.showRecord=this.showRecord,e.orderParam=this.tags2,e.orderMod=this.ordermod,this.ykz040&&Object.assign(e,{ykz040:this.ykz040}),e},fnQuery:function(){var e=this;this.$nextTick((function(){e.baseInfoForm.validateFields((function(t){t||e.$refs.infoPageRef.loadData()}))}))},exportExcel:function(){var e,t=this,a=this.infoPageParams(),i=[],o=this.$refs.infoTableRef.getColumns(),n=(this.infoTableData,(0,l.Z)(o));try{for(n.s();!(e=n.n()).done;){var s=e.value;"seq"!==s.type&&"operate"!==s.property&&!1!==s.visible&&i.push({header:s.title,key:s.property,width:20})}}catch(d){n.e(d)}finally{n.f()}var r=o.map((function(e){return e.property})),c=[{codeType:"AAE140",columnKey:"aae140"},{codeType:"AAE141",columnKey:"aae141"},{codeType:"SEX",columnKey:"aac004"},{columnKey:"ape800",customCollection:function(e,t){"0"==e.value?e.value="审核通过":"1"==e.value?e.value="可疑提醒":"2"==e.value&&(e.value="违规提醒")}},{columnKey:"ape893",customCollection:function(e,t){var a=document.createElement("div");a.innerHTML=e.value;var i=a.innerText||a.textContent;a=null,e.value=i}}],u=c.filter((function(e){return r.includes(e.columnKey)}));this.Base.submit(null,{url:"dischargeClinic/exportExcel",data:a,autoValid:!1},{successCallback:function(e){var a=e.data.data;a.forEach((function(e){e.diagnoses&&Object.assign(e,e.diagnoses),e.ape805&&e.akc225&&(e.ape804=e.ape805*e.akc225)}));var o={fileName:"出院患者审核查询表",sheets:[{name:"worksheet1",column:{complex:!1,columns:i},rows:a,codeList:u}]};t.Base.generateExcel(o)},failCallback:function(e){t.$message.error("医师数据加载失败")}})},fnCustomHeaderCell:function(e){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px",fontWeight:"bold"}}},showRecords:function(e){e.target.checked?this.showRecord="1":this.showRecord="0",this.infoPageParams()},fnSearch:function(e){var t=this.baseInfoForm.getFieldValue("flag");this.Base.openTabMenu({id:e.akb020+e.aaz217,name:"【"+e.aac003+"】患者详情",url:"detailsQuery.html#/expenseDetails?akb020=".concat(e.akb020,"&akc190=").concat(e.akc190,"&aaz217=").concat(e.aaz217,"&flag=")+t,refresh:!1})}}},v=b,C=a(1001),k=(0,C.Z)(v,i,o,!1,null,"1f9742c8",null),w=k.exports},36766:function(e,t,a){"use strict";var i=a(66586);t["Z"]=i.Z},26263:function(e,t,a){"use strict";a.d(t,{s:function(){return i},x:function(){return o}});var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:e.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[e._v(e._s(e.title))]),e._t("default")],2)},o=[]},66586:function(e,t){"use strict";var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};t["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}},55067:function(e,t,a){"use strict";a(95082),a(95278)},83231:function(e,t,a){"use strict";var i=a(48534);a(36133);function o(e){return e?e.akb020&&e.aaz263?"personal":e.akb020&&e.aaz309&&e.aaz307?"group":e.akb020&&e.aaz307?"department":e.akb020?"hospital":"all":"all"}function l(e){return n.apply(this,arguments)}function n(){return n=(0,i.Z)(regeneratorRuntime.mark((function e(t){var a,i,l,n,s,r,c,u,d,f,h,m;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,Base.submit(null,{url:"/miimCommonRead/queryDataPermission",showPageLoading:!1,data:{jobnumber:t},autoValid:!0});case 2:return a=e.sent,i=new Set,l=new Set,a.data.permission.forEach((function(e){var t=o(e);"hospital"===t&&i.add(e.akb020),"department"===t&&l.add(e.aaz307)})),n=a.data.permission.filter((function(e){return"department"===o(e)||!l.has(e.aaz307)})).filter((function(e){return"hospital"===o(e)||!i.has(e.akb020)})),s=new Set(n.map((function(e){return e.akb020})).filter((function(e){return null!=e}))),r=new Set(n.map((function(e){return e.aaz307})).filter((function(e){return null!=e}))),c=new Set(n.map((function(e){return e.aaz309})).filter((function(e){return null!=e}))),u=new Set(n.map((function(e){return e.aaz263})).filter((function(e){return null!=e}))),d=!1,f=!1,h=!1,m=!1,1===s.size&&(d=!0),1===r.size&&1===s.size&&(f=!0),1===r.size&&1===s.size&&1===c.size&&(h=!0),1===s.size&&0===r.size&&1===u.size&&(m=!0),e.abrupt("return",{akb020Set:s,aaz307Set:r,aaz263Set:u,aaz309Set:c,akb020Disable:d,aaz307Disable:f,aaz263Disable:m,aaz309Disable:h});case 20:case"end":return e.stop()}}),e)}))),n.apply(this,arguments)}function s(e,t){Base.submit(null,{url:"miimCommonRead/getAa01AAE500StartStop",data:e},{successCallback:function(e){return t(e)}})}function r(e,t){Base.submit(null,{url:"miimCommonRead/insertTableColumShow",data:e,autoValid:!1},{successCallback:function(e){return t("success")},failCallback:function(e){return t("fail")}})}var c={zbx01:{footer:function(){return"统筹金总额"}},zbx02:{footer:function(){return"基金支出总额"}},zbx03:{footer:function(){return"总费用"}},zbx04:{footer:function(){return"总住院日"}}};t["Z"]={permissionCheck:l,getAa01AAE500StartStop:s,insertTableColumShow:r,props:c,moneyNumFormat:function(e){var t=e.cellValue;return t||"—"}}},55382:function(){},61219:function(){}}]);
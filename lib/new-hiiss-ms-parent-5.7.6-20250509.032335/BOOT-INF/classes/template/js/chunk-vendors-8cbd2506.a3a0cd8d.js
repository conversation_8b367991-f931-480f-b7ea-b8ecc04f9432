(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[602],{70481:function(e,t,i){"use strict";i.d(t,{Z:function(){return h}});i(32564);
/*!
 * @wcjiang/notify v2.1.0
 * JS achieve the browser title flashing , scrolling, voice prompts , chrome notice.
 * 
 * Copyright (c) 2022 kenny wang
 * http://jaywcjlove.github.io/iNotify
 * 
 * Licensed under the MIT license.
 */
window.Notification&&"granted"!==window.Notification.permission&&window.Notification.requestPermission();var n="",r=["flash","scroll"],o={title:"iNotify !",body:"You have a new message.",openurl:""};function a(e,t){for(var i in t)e[i]&&(t[i]=e[i]);return t}function s(e){return"[object Array]"===Object.prototype.toString.call(e)}function l(e){var t,i=document.createElement("audio");if(i.autoplay=!0,i.muted=!0,s(e)&&e.length>0)for(var n=0;n<e.length;n++)t=document.createElement("source"),t.src=e[n],t.type="audio/".concat(u(e[n])),i.appendChild(t);else i.src=e;return i}function c(e){var t=document.querySelectorAll("link[rel~=shortcut]")[0];return t||(t=d("O",e)),t}function u(e){return e.match(/\.([^\\.]+)$/)[1]}function d(e,t){var i=document.createElement("canvas"),r=document.getElementsByTagName("head")[0],o=document.createElement("link"),a=null;return i.height=32,i.width=32,a=i.getContext("2d"),a.fillStyle=t.backgroundColor,a.fillRect(0,0,32,32),a.textAlign="center",a.font='22px "helvetica", sans-serif',a.fillStyle=t.textColor,e&&a.fillText(e,16,24),o.setAttribute("rel","shortcut icon"),o.setAttribute("type","image/x-icon"),o.setAttribute("id","new".concat(t.id)),o.setAttribute("href",i.toDataURL("image/png")),n=i.toDataURL("image/png"),r.appendChild(o)}function h(e){e&&this.init(e)}h.prototype={init:function(e){return e||(e={}),this.interval=e.interval||100,this.effect=e.effect||"flash",this.title=e.title||document.title,this.message=e.message||this.title,this.onclick=e.onclick||this.onclick,this.openurl=e.openurl||this.openurl,this.disableFavicon=e.disableFavicon||!1,this.updateFavicon=!this.disableFavicon&&(e.updateFavicon||{id:"favicon",textColor:"#fff",backgroundColor:"#2F9A00"}),this.audio=e.audio||"",this.favicon=!this.disableFavicon&&c(this.updateFavicon),this.cloneFavicon=this.favicon&&this.favicon.cloneNode(!0),n=e.notification&&e.notification.icon?e.notification.icon:e.icon?e.icon:this.favicon.href,o.icon=n,this.notification=e.notification||o,this.audio&&this.audio.file&&this.setURL(this.audio.file),this},render:function(){if("flash"===this.effect)document.title=this.title===document.title?this.message:this.title;else if("scroll"===this.effect){var e=this.message||document.title;this.scrollTitle&&this.scrollTitle.slice(1)?(this.scrollTitle=this.scrollTitle.slice(1),document.title=this.scrollTitle):(document.title=e,this.scrollTitle=e)}return this},setTitle:function(e){if(!0===e){if(r.indexOf(this.effect)>=0)return this.addTimer()}else e?(this.message=e,this.scrollTitle="",this.addTimer()):this.clearTimer();return this},setURL:function(e){return e&&(this.audioElm&&this.audioElm.remove(),this.audioElm=l(e),document.body.appendChild(this.audioElm)),this},loopPlay:function(){return this.setURL(),this.audioElm.loop=!0,this.player(),this},stopPlay:function(){return this.audioElm&&(this.audioElm.loop=!1,this.audioElm.pause()),this},player:function(){if(this.audio&&this.audio.file){this.audioElm||(this.audioElm=l(this.audio.file),document.body.appendChild(this.audioElm)),this.audioElm.muted=!1;var e=this.audioElm.play();return void 0!==e&&e.then((function(){})).catch((function(){})),this}},notify:function(e){var t=this.notification,i=e.openurl?e.openurl:this.openurl,r=e.onclick?e.onclick:this.onclick;if(window.Notification){t=e?a(e,t):o;var s={};s.icon=e.icon?e.icon:n,s.body=t.body||e.body,e.dir&&(s.dir=e.dir);var l=new Notification(t.title||e.title,s);l.onclick=function(){r&&"function"===typeof r&&r(l),i&&window.open(i)},l.onshow=function(){e.onshow&&"function"===typeof e.onshow&&e.onshow(l)},l.onclose=function(){e.onclose&&"function"===typeof e.onclose&&e.onclose(l)},l.onerror=function(){e.onerror&&"function"===typeof e.onerror&&e.onerror(l)},this.Notifiy=l}return this},isPermission:function(){return window.Notification&&"granted"===Notification.permission},setInterval:function(e){return e&&(this.interval=e,this.addTimer()),this},setFavicon:function(e){if(!e&&0!==e)return this.faviconClear();var t=document.getElementById("new".concat(this.updateFavicon.id));return this.favicon&&this.favicon.remove(),t&&t.remove(),this.updateFavicon.num=e,d(e,this.updateFavicon),this},setFaviconColor:function(e){return e&&(this.faviconRemove(),this.updateFavicon.textColor=e,d(this.updateFavicon.num,this.updateFavicon)),this},setFaviconBackgroundColor:function(e){return e&&(this.faviconRemove(),this.updateFavicon.backgroundColor=e,d(this.updateFavicon.num,this.updateFavicon)),this},faviconRemove:function(){this.faviconClear();var e=document.getElementById("new".concat(this.updateFavicon.id));this.favicon&&this.favicon.remove(),e&&e.remove()},addTimer:function(){return this.clearTimer(),r.indexOf(this.effect)>=0&&(this.timer=setInterval(this.render.bind(this),this.interval)),this},close:function(){this.Notifiy&&this.Notifiy.close()},faviconClear:function(){var e=document.getElementById("new".concat(this.updateFavicon.id)),t=document.getElementsByTagName("head")[0],i=document.querySelectorAll("link[rel~=shortcut]");if(e&&e.remove(),i.length>0)for(var r=0;r<i.length;r++)i[r].remove();return t.appendChild(this.cloneFavicon),n=this.cloneFavicon.href,this.favicon=this.cloneFavicon,this},clearTimer:function(){return this.timer&&clearInterval(this.timer),document.title=this.title,this}}},54212:function(e,t,i){"use strict";i.d(t,{cV:function(){return n}});var n=function(e){if(e.fnOptions)return e.fnOptions;var t=e.componentOptions;return e.$vnode&&(t=e.$vnode.componentOptions),t&&t.Ctor.options||{}}},68662:function(e,t,i){"use strict";i.d(t,{Kp:function(){return n}});function n(e,t){0}},36790:function(e,t,i){i(32564);for(var n=i(50202),r="undefined"===typeof window?i.g:window,o=["moz","webkit"],a="AnimationFrame",s=r["request"+a],l=r["cancel"+a]||r["cancelRequest"+a],c=0;!s&&c<o.length;c++)s=r[o[c]+"Request"+a],l=r[o[c]+"Cancel"+a]||r[o[c]+"CancelRequest"+a];if(!s||!l){var u=0,d=0,h=[],p=1e3/60;s=function(e){if(0===h.length){var t=n(),i=Math.max(0,p-(t-u));u=i+t,setTimeout((function(){var e=h.slice(0);h.length=0;for(var t=0;t<e.length;t++)if(!e[t].cancelled)try{e[t].callback(u)}catch(i){setTimeout((function(){throw i}),0)}}),Math.round(i))}return h.push({handle:++d,callback:e,cancelled:!1}),d},l=function(e){for(var t=0;t<h.length;t++)h[t].handle===e&&(h[t].cancelled=!0)}}e.exports=function(e){return s.call(r,e)},e.exports.cancel=function(){l.apply(r,arguments)},e.exports.polyfill=function(e){e||(e=r),e.requestAnimationFrame=s,e.cancelAnimationFrame=l}},36133:function(e,t,i){e=i.nmd(e);var n=i(57847)["default"];i(68304),i(65743);var r=function(e){"use strict";var t,i=Object.prototype,r=i.hasOwnProperty,o="function"===typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",s=o.asyncIterator||"@@asyncIterator",l=o.toStringTag||"@@toStringTag";function c(e,t,i){return Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(C){c=function(e,t,i){return e[t]=i}}function u(e,t,i,n){var r=t&&t.prototype instanceof g?t:g,o=Object.create(r.prototype),a=new M(n||[]);return o._invoke=T(e,i,a),o}function d(e,t,i){try{return{type:"normal",arg:e.call(t,i)}}catch(C){return{type:"throw",arg:C}}}e.wrap=u;var h="suspendedStart",p="suspendedYield",f="executing",m="completed",v={};function g(){}function y(){}function b(){}var x={};c(x,a,(function(){return this}));var w=Object.getPrototypeOf,_=w&&w(w(D([])));_&&_!==i&&r.call(_,a)&&(x=_);var S=b.prototype=g.prototype=Object.create(x);function O(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function E(e,t){function i(o,a,s,l){var c=d(e[o],e,a);if("throw"!==c.type){var u=c.arg,h=u.value;return h&&"object"===n(h)&&r.call(h,"__await")?t.resolve(h.__await).then((function(e){i("next",e,s,l)}),(function(e){i("throw",e,s,l)})):t.resolve(h).then((function(e){u.value=e,s(u)}),(function(e){return i("throw",e,s,l)}))}l(c.arg)}var o;function a(e,n){function r(){return new t((function(t,r){i(e,n,t,r)}))}return o=o?o.then(r,r):r()}this._invoke=a}function T(e,t,i){var n=h;return function(r,o){if(n===f)throw new Error("Generator is already running");if(n===m){if("throw"===r)throw o;return N()}i.method=r,i.arg=o;while(1){var a=i.delegate;if(a){var s=k(a,i);if(s){if(s===v)continue;return s}}if("next"===i.method)i.sent=i._sent=i.arg;else if("throw"===i.method){if(n===h)throw n=m,i.arg;i.dispatchException(i.arg)}else"return"===i.method&&i.abrupt("return",i.arg);n=f;var l=d(e,t,i);if("normal"===l.type){if(n=i.done?m:p,l.arg===v)continue;return{value:l.arg,done:i.done}}"throw"===l.type&&(n=m,i.method="throw",i.arg=l.arg)}}}function k(e,i){var n=e.iterator[i.method];if(n===t){if(i.delegate=null,"throw"===i.method){if(e.iterator["return"]&&(i.method="return",i.arg=t,k(e,i),"throw"===i.method))return v;i.method="throw",i.arg=new TypeError("The iterator does not provide a 'throw' method")}return v}var r=d(n,e.iterator,i.arg);if("throw"===r.type)return i.method="throw",i.arg=r.arg,i.delegate=null,v;var o=r.arg;return o?o.done?(i[e.resultName]=o.value,i.next=e.nextLoc,"return"!==i.method&&(i.method="next",i.arg=t),i.delegate=null,v):o:(i.method="throw",i.arg=new TypeError("iterator result is not an object"),i.delegate=null,v)}function A(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function z(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function M(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(A,this),this.reset(!0)}function D(e){if(e){var i=e[a];if(i)return i.call(e);if("function"===typeof e.next)return e;if(!isNaN(e.length)){var n=-1,o=function i(){while(++n<e.length)if(r.call(e,n))return i.value=e[n],i.done=!1,i;return i.value=t,i.done=!0,i};return o.next=o}}return{next:N}}function N(){return{value:t,done:!0}}return y.prototype=b,c(S,"constructor",b),c(b,"constructor",y),y.displayName=c(b,l,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"===typeof e&&e.constructor;return!!t&&(t===y||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,b):(e.__proto__=b,c(e,l,"GeneratorFunction")),e.prototype=Object.create(S),e},e.awrap=function(e){return{__await:e}},O(E.prototype),c(E.prototype,s,(function(){return this})),e.AsyncIterator=E,e.async=function(t,i,n,r,o){void 0===o&&(o=Promise);var a=new E(u(t,i,n,r),o);return e.isGeneratorFunction(i)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},O(S),c(S,l,"Generator"),c(S,a,(function(){return this})),c(S,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=[];for(var i in e)t.push(i);return t.reverse(),function i(){while(t.length){var n=t.pop();if(n in e)return i.value=n,i.done=!1,i}return i.done=!0,i}},e.values=D,M.prototype={constructor:M,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(z),!e)for(var i in this)"t"===i.charAt(0)&&r.call(this,i)&&!isNaN(+i.slice(1))&&(this[i]=t)},stop:function(){this.done=!0;var e=this.tryEntries[0],t=e.completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var i=this;function n(n,r){return s.type="throw",s.arg=e,i.next=n,r&&(i.method="next",i.arg=t),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],s=a.completion;if("root"===a.tryLoc)return n("end");if(a.tryLoc<=this.prev){var l=r.call(a,"catchLoc"),c=r.call(a,"finallyLoc");if(l&&c){if(this.prev<a.catchLoc)return n(a.catchLoc,!0);if(this.prev<a.finallyLoc)return n(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return n(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return n(a.finallyLoc)}}}},abrupt:function(e,t){for(var i=this.tryEntries.length-1;i>=0;--i){var n=this.tryEntries[i];if(n.tryLoc<=this.prev&&r.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var o=n;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=e,a.arg=t,o?(this.method="next",this.next=o.finallyLoc,v):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var i=this.tryEntries[t];if(i.finallyLoc===e)return this.complete(i.completion,i.afterLoc),z(i),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var i=this.tryEntries[t];if(i.tryLoc===e){var n=i.completion;if("throw"===n.type){var r=n.arg;z(i)}return r}}throw new Error("illegal catch attempt")},delegateYield:function(e,i,n){return this.delegate={iterator:D(e),resultName:i,nextLoc:n},"next"===this.method&&(this.arg=t),v}},e}("object"===n(e)?e.exports:{});try{regeneratorRuntime=r}catch(o){"object"===("undefined"===typeof globalThis?"undefined":n(globalThis))?globalThis.regeneratorRuntime=r:Function("r","regeneratorRuntime = r")(r)}},70566:function(e,t,i){"use strict";i.r(t);i(32564);var n=function(){if("undefined"!==typeof Map)return Map;function e(e,t){var i=-1;return e.some((function(e,n){return e[0]===t&&(i=n,!0)})),i}return function(){function t(){this.__entries__=[]}return Object.defineProperty(t.prototype,"size",{get:function(){return this.__entries__.length},enumerable:!0,configurable:!0}),t.prototype.get=function(t){var i=e(this.__entries__,t),n=this.__entries__[i];return n&&n[1]},t.prototype.set=function(t,i){var n=e(this.__entries__,t);~n?this.__entries__[n][1]=i:this.__entries__.push([t,i])},t.prototype.delete=function(t){var i=this.__entries__,n=e(i,t);~n&&i.splice(n,1)},t.prototype.has=function(t){return!!~e(this.__entries__,t)},t.prototype.clear=function(){this.__entries__.splice(0)},t.prototype.forEach=function(e,t){void 0===t&&(t=null);for(var i=0,n=this.__entries__;i<n.length;i++){var r=n[i];e.call(t,r[1],r[0])}},t}()}(),r="undefined"!==typeof window&&"undefined"!==typeof document&&window.document===document,o=function(){return"undefined"!==typeof i.g&&i.g.Math===Math?i.g:"undefined"!==typeof self&&self.Math===Math?self:"undefined"!==typeof window&&window.Math===Math?window:Function("return this")()}(),a=function(){return"function"===typeof requestAnimationFrame?requestAnimationFrame.bind(o):function(e){return setTimeout((function(){return e(Date.now())}),1e3/60)}}(),s=2;function l(e,t){var i=!1,n=!1,r=0;function o(){i&&(i=!1,e()),n&&c()}function l(){a(o)}function c(){var e=Date.now();if(i){if(e-r<s)return;n=!0}else i=!0,n=!1,setTimeout(l,t);r=e}return c}var c=20,u=["top","right","bottom","left","width","height","size","weight"],d="undefined"!==typeof MutationObserver,h=function(){function e(){this.connected_=!1,this.mutationEventsAdded_=!1,this.mutationsObserver_=null,this.observers_=[],this.onTransitionEnd_=this.onTransitionEnd_.bind(this),this.refresh=l(this.refresh.bind(this),c)}return e.prototype.addObserver=function(e){~this.observers_.indexOf(e)||this.observers_.push(e),this.connected_||this.connect_()},e.prototype.removeObserver=function(e){var t=this.observers_,i=t.indexOf(e);~i&&t.splice(i,1),!t.length&&this.connected_&&this.disconnect_()},e.prototype.refresh=function(){var e=this.updateObservers_();e&&this.refresh()},e.prototype.updateObservers_=function(){var e=this.observers_.filter((function(e){return e.gatherActive(),e.hasActive()}));return e.forEach((function(e){return e.broadcastActive()})),e.length>0},e.prototype.connect_=function(){r&&!this.connected_&&(document.addEventListener("transitionend",this.onTransitionEnd_),window.addEventListener("resize",this.refresh),d?(this.mutationsObserver_=new MutationObserver(this.refresh),this.mutationsObserver_.observe(document,{attributes:!0,childList:!0,characterData:!0,subtree:!0})):(document.addEventListener("DOMSubtreeModified",this.refresh),this.mutationEventsAdded_=!0),this.connected_=!0)},e.prototype.disconnect_=function(){r&&this.connected_&&(document.removeEventListener("transitionend",this.onTransitionEnd_),window.removeEventListener("resize",this.refresh),this.mutationsObserver_&&this.mutationsObserver_.disconnect(),this.mutationEventsAdded_&&document.removeEventListener("DOMSubtreeModified",this.refresh),this.mutationsObserver_=null,this.mutationEventsAdded_=!1,this.connected_=!1)},e.prototype.onTransitionEnd_=function(e){var t=e.propertyName,i=void 0===t?"":t,n=u.some((function(e){return!!~i.indexOf(e)}));n&&this.refresh()},e.getInstance=function(){return this.instance_||(this.instance_=new e),this.instance_},e.instance_=null,e}(),p=function(e,t){for(var i=0,n=Object.keys(t);i<n.length;i++){var r=n[i];Object.defineProperty(e,r,{value:t[r],enumerable:!1,writable:!1,configurable:!0})}return e},f=function(e){var t=e&&e.ownerDocument&&e.ownerDocument.defaultView;return t||o},m=E(0,0,0,0);function v(e){return parseFloat(e)||0}function g(e){for(var t=[],i=1;i<arguments.length;i++)t[i-1]=arguments[i];return t.reduce((function(t,i){var n=e["border-"+i+"-width"];return t+v(n)}),0)}function y(e){for(var t=["top","right","bottom","left"],i={},n=0,r=t;n<r.length;n++){var o=r[n],a=e["padding-"+o];i[o]=v(a)}return i}function b(e){var t=e.getBBox();return E(0,0,t.width,t.height)}function x(e){var t=e.clientWidth,i=e.clientHeight;if(!t&&!i)return m;var n=f(e).getComputedStyle(e),r=y(n),o=r.left+r.right,a=r.top+r.bottom,s=v(n.width),l=v(n.height);if("border-box"===n.boxSizing&&(Math.round(s+o)!==t&&(s-=g(n,"left","right")+o),Math.round(l+a)!==i&&(l-=g(n,"top","bottom")+a)),!_(e)){var c=Math.round(s+o)-t,u=Math.round(l+a)-i;1!==Math.abs(c)&&(s-=c),1!==Math.abs(u)&&(l-=u)}return E(r.left,r.top,s,l)}var w=function(){return"undefined"!==typeof SVGGraphicsElement?function(e){return e instanceof f(e).SVGGraphicsElement}:function(e){return e instanceof f(e).SVGElement&&"function"===typeof e.getBBox}}();function _(e){return e===f(e).document.documentElement}function S(e){return r?w(e)?b(e):x(e):m}function O(e){var t=e.x,i=e.y,n=e.width,r=e.height,o="undefined"!==typeof DOMRectReadOnly?DOMRectReadOnly:Object,a=Object.create(o.prototype);return p(a,{x:t,y:i,width:n,height:r,top:i,right:t+n,bottom:r+i,left:t}),a}function E(e,t,i,n){return{x:e,y:t,width:i,height:n}}var T=function(){function e(e){this.broadcastWidth=0,this.broadcastHeight=0,this.contentRect_=E(0,0,0,0),this.target=e}return e.prototype.isActive=function(){var e=S(this.target);return this.contentRect_=e,e.width!==this.broadcastWidth||e.height!==this.broadcastHeight},e.prototype.broadcastRect=function(){var e=this.contentRect_;return this.broadcastWidth=e.width,this.broadcastHeight=e.height,e},e}(),k=function(){function e(e,t){var i=O(t);p(this,{target:e,contentRect:i})}return e}(),A=function(){function e(e,t,i){if(this.activeObservations_=[],this.observations_=new n,"function"!==typeof e)throw new TypeError("The callback provided as parameter 1 is not a function.");this.callback_=e,this.controller_=t,this.callbackCtx_=i}return e.prototype.observe=function(e){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if("undefined"!==typeof Element&&Element instanceof Object){if(!(e instanceof f(e).Element))throw new TypeError('parameter 1 is not of type "Element".');var t=this.observations_;t.has(e)||(t.set(e,new T(e)),this.controller_.addObserver(this),this.controller_.refresh())}},e.prototype.unobserve=function(e){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if("undefined"!==typeof Element&&Element instanceof Object){if(!(e instanceof f(e).Element))throw new TypeError('parameter 1 is not of type "Element".');var t=this.observations_;t.has(e)&&(t.delete(e),t.size||this.controller_.removeObserver(this))}},e.prototype.disconnect=function(){this.clearActive(),this.observations_.clear(),this.controller_.removeObserver(this)},e.prototype.gatherActive=function(){var e=this;this.clearActive(),this.observations_.forEach((function(t){t.isActive()&&e.activeObservations_.push(t)}))},e.prototype.broadcastActive=function(){if(this.hasActive()){var e=this.callbackCtx_,t=this.activeObservations_.map((function(e){return new k(e.target,e.broadcastRect())}));this.callback_.call(e,t,e),this.clearActive()}},e.prototype.clearActive=function(){this.activeObservations_.splice(0)},e.prototype.hasActive=function(){return this.activeObservations_.length>0},e}(),z="undefined"!==typeof WeakMap?new WeakMap:new n,M=function(){function e(t){if(!(this instanceof e))throw new TypeError("Cannot call a class as a function.");if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");var i=h.getInstance(),n=new A(t,i,this);z.set(this,n)}return e}();["observe","unobserve","disconnect"].forEach((function(e){M.prototype[e]=function(){var t;return(t=z.get(this))[e].apply(t,arguments)}}));var D=function(){return"undefined"!==typeof o.ResizeObserver?o.ResizeObserver:M}();t["default"]=D},14473:function(e,t,i){var n,r,o;i(57847)["default"];(function(i,a){r=[],n=a,o="function"===typeof n?n.apply(t,r):n,void 0===o||(e.exports=o)})(0,(function(){var e=/(auto|scroll)/,t=function e(t,i){return null===t.parentNode?i:e(t.parentNode,i.concat([t]))},i=function(e,t){return getComputedStyle(e,null).getPropertyValue(t)},n=function(e){return i(e,"overflow")+i(e,"overflow-y")+i(e,"overflow-x")},r=function(t){return e.test(n(t))},o=function(e){if(e instanceof HTMLElement||e instanceof SVGElement){for(var i=t(e.parentNode,[]),n=0;n<i.length;n+=1)if(r(i[n]))return i[n];return document.scrollingElement||document.documentElement}};return o}))},33879:function(e){"use strict";function t(e,t){if(e===t)return!0;if(!e||!t)return!1;var i=e.length;if(t.length!==i)return!1;for(var n=0;n<i;n++)if(e[n]!==t[n])return!1;return!0}e.exports=t},49674:function(e,t,i){var n=i(57847)["default"];e.exports=function(e,t,i,r){var o=i?i.call(r,e,t):void 0;if(void 0!==o)return!!o;if(e===t)return!0;if("object"!==n(e)||!e||"object"!==n(t)||!t)return!1;var a=Object.keys(e),s=Object.keys(t);if(a.length!==s.length)return!1;for(var l=Object.prototype.hasOwnProperty.bind(t),c=0;c<a.length;c++){var u=a[c];if(!l(u))return!1;var d=e[u],h=t[u];if(o=i?i.call(r,d,h,u):void 0,!1===o||void 0===o&&d!==h)return!1}return!0}},3413:function(e,t,i){"use strict";i.r(t),i.d(t,{MultiDrag:function(){return Pt},Sortable:function(){return Qe},Swap:function(){return kt}});var n=i(3336);i(32564);
/**!
 * Sortable 1.10.2
 * <AUTHOR>   <<EMAIL>>
 * <AUTHOR>    <<EMAIL>>
 * @license MIT
 */
function r(e){return r="function"===typeof Symbol&&"symbol"===(0,n.Z)(Symbol.iterator)?function(e){return(0,n.Z)(e)}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":(0,n.Z)(e)},r(e)}function o(e,t,i){return t in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}function a(){return a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n])}return e},a.apply(this,arguments)}function s(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{},n=Object.keys(i);"function"===typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(i).filter((function(e){return Object.getOwnPropertyDescriptor(i,e).enumerable})))),n.forEach((function(t){o(e,t,i[t])}))}return e}function l(e,t){if(null==e)return{};var i,n,r={},o=Object.keys(e);for(n=0;n<o.length;n++)i=o[n],t.indexOf(i)>=0||(r[i]=e[i]);return r}function c(e,t){if(null==e)return{};var i,n,r=l(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)i=o[n],t.indexOf(i)>=0||Object.prototype.propertyIsEnumerable.call(e,i)&&(r[i]=e[i])}return r}function u(e){return d(e)||h(e)||p()}function d(e){if(Array.isArray(e)){for(var t=0,i=new Array(e.length);t<e.length;t++)i[t]=e[t];return i}}function h(e){if(Symbol.iterator in Object(e)||"[object Arguments]"===Object.prototype.toString.call(e))return Array.from(e)}function p(){throw new TypeError("Invalid attempt to spread non-iterable instance")}var f="1.10.2";function m(e){if("undefined"!==typeof window&&window.navigator)return!!navigator.userAgent.match(e)}var v=m(/(?:Trident.*rv[ :]?11\.|msie|iemobile|Windows Phone)/i),g=m(/Edge/i),y=m(/firefox/i),b=m(/safari/i)&&!m(/chrome/i)&&!m(/android/i),x=m(/iP(ad|od|hone)/i),w=m(/chrome/i)&&m(/android/i),_={capture:!1,passive:!1};function S(e,t,i){e.addEventListener(t,i,!v&&_)}function O(e,t,i){e.removeEventListener(t,i,!v&&_)}function E(e,t){if(t){if(">"===t[0]&&(t=t.substring(1)),e)try{if(e.matches)return e.matches(t);if(e.msMatchesSelector)return e.msMatchesSelector(t);if(e.webkitMatchesSelector)return e.webkitMatchesSelector(t)}catch(i){return!1}return!1}}function T(e){return e.host&&e!==document&&e.host.nodeType?e.host:e.parentNode}function k(e,t,i,n){if(e){i=i||document;do{if(null!=t&&(">"===t[0]?e.parentNode===i&&E(e,t):E(e,t))||n&&e===i)return e;if(e===i)break}while(e=T(e))}return null}var A,z=/\s+/g;function M(e,t,i){if(e&&t)if(e.classList)e.classList[i?"add":"remove"](t);else{var n=(" "+e.className+" ").replace(z," ").replace(" "+t+" "," ");e.className=(n+(i?" "+t:"")).replace(z," ")}}function D(e,t,i){var n=e&&e.style;if(n){if(void 0===i)return document.defaultView&&document.defaultView.getComputedStyle?i=document.defaultView.getComputedStyle(e,""):e.currentStyle&&(i=e.currentStyle),void 0===t?i:i[t];t in n||-1!==t.indexOf("webkit")||(t="-webkit-"+t),n[t]=i+("string"===typeof i?"":"px")}}function N(e,t){var i="";if("string"===typeof e)i=e;else do{var n=D(e,"transform");n&&"none"!==n&&(i=n+" "+i)}while(!t&&(e=e.parentNode));var r=window.DOMMatrix||window.WebKitCSSMatrix||window.CSSMatrix||window.MSCSSMatrix;return r&&new r(i)}function C(e,t,i){if(e){var n=e.getElementsByTagName(t),r=0,o=n.length;if(i)for(;r<o;r++)i(n[r],r);return n}return[]}function j(){var e=document.scrollingElement;return e||document.documentElement}function R(e,t,i,n,r){if(e.getBoundingClientRect||e===window){var o,a,s,l,c,u,d;if(e!==window&&e!==j()?(o=e.getBoundingClientRect(),a=o.top,s=o.left,l=o.bottom,c=o.right,u=o.height,d=o.width):(a=0,s=0,l=window.innerHeight,c=window.innerWidth,u=window.innerHeight,d=window.innerWidth),(t||i)&&e!==window&&(r=r||e.parentNode,!v))do{if(r&&r.getBoundingClientRect&&("none"!==D(r,"transform")||i&&"static"!==D(r,"position"))){var h=r.getBoundingClientRect();a-=h.top+parseInt(D(r,"border-top-width")),s-=h.left+parseInt(D(r,"border-left-width")),l=a+o.height,c=s+o.width;break}}while(r=r.parentNode);if(n&&e!==window){var p=N(r||e),f=p&&p.a,m=p&&p.d;p&&(a/=m,s/=f,d/=f,u/=m,l=a+u,c=s+d)}return{top:a,left:s,bottom:l,right:c,width:d,height:u}}}function I(e,t,i){var n=H(e,!0),r=R(e)[t];while(n){var o=R(n)[i],a=void 0;if(a="top"===i||"left"===i?r>=o:r<=o,!a)return n;if(n===j())break;n=H(n,!1)}return!1}function L(e,t,i){var n=0,r=0,o=e.children;while(r<o.length){if("none"!==o[r].style.display&&o[r]!==Qe.ghost&&o[r]!==Qe.dragged&&k(o[r],i.draggable,e,!1)){if(n===t)return o[r];n++}r++}return null}function F(e,t){var i=e.lastElementChild;while(i&&(i===Qe.ghost||"none"===D(i,"display")||t&&!E(i,t)))i=i.previousElementSibling;return i||null}function P(e,t){var i=0;if(!e||!e.parentNode)return-1;while(e=e.previousElementSibling)"TEMPLATE"===e.nodeName.toUpperCase()||e===Qe.clone||t&&!E(e,t)||i++;return i}function V(e){var t=0,i=0,n=j();if(e)do{var r=N(e),o=r.a,a=r.d;t+=e.scrollLeft*o,i+=e.scrollTop*a}while(e!==n&&(e=e.parentNode));return[t,i]}function $(e,t){for(var i in e)if(e.hasOwnProperty(i))for(var n in t)if(t.hasOwnProperty(n)&&t[n]===e[i][n])return Number(i);return-1}function H(e,t){if(!e||!e.getBoundingClientRect)return j();var i=e,n=!1;do{if(i.clientWidth<i.scrollWidth||i.clientHeight<i.scrollHeight){var r=D(i);if(i.clientWidth<i.scrollWidth&&("auto"==r.overflowX||"scroll"==r.overflowX)||i.clientHeight<i.scrollHeight&&("auto"==r.overflowY||"scroll"==r.overflowY)){if(!i.getBoundingClientRect||i===document.body)return j();if(n||t)return i;n=!0}}}while(i=i.parentNode);return j()}function B(e,t){if(e&&t)for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i]);return e}function Y(e,t){return Math.round(e.top)===Math.round(t.top)&&Math.round(e.left)===Math.round(t.left)&&Math.round(e.height)===Math.round(t.height)&&Math.round(e.width)===Math.round(t.width)}function q(e,t){return function(){if(!A){var i=arguments,n=this;1===i.length?e.call(n,i[0]):e.apply(n,i),A=setTimeout((function(){A=void 0}),t)}}}function X(){clearTimeout(A),A=void 0}function U(e,t,i){e.scrollLeft+=t,e.scrollTop+=i}function W(e){var t=window.Polymer,i=window.jQuery||window.Zepto;return t&&t.dom?t.dom(e).cloneNode(!0):i?i(e).clone(!0)[0]:e.cloneNode(!0)}function G(e,t){D(e,"position","absolute"),D(e,"top",t.top),D(e,"left",t.left),D(e,"width",t.width),D(e,"height",t.height)}function K(e){D(e,"position",""),D(e,"top",""),D(e,"left",""),D(e,"width",""),D(e,"height","")}var Z="Sortable"+(new Date).getTime();function J(){var e,t=[];return{captureAnimationState:function(){if(t=[],this.options.animation){var e=[].slice.call(this.el.children);e.forEach((function(e){if("none"!==D(e,"display")&&e!==Qe.ghost){t.push({target:e,rect:R(e)});var i=s({},t[t.length-1].rect);if(e.thisAnimationDuration){var n=N(e,!0);n&&(i.top-=n.f,i.left-=n.e)}e.fromRect=i}}))}},addAnimationState:function(e){t.push(e)},removeAnimationState:function(e){t.splice($(t,{target:e}),1)},animateAll:function(i){var n=this;if(!this.options.animation)return clearTimeout(e),void("function"===typeof i&&i());var r=!1,o=0;t.forEach((function(e){var t=0,i=e.target,a=i.fromRect,s=R(i),l=i.prevFromRect,c=i.prevToRect,u=e.rect,d=N(i,!0);d&&(s.top-=d.f,s.left-=d.e),i.toRect=s,i.thisAnimationDuration&&Y(l,s)&&!Y(a,s)&&(u.top-s.top)/(u.left-s.left)===(a.top-s.top)/(a.left-s.left)&&(t=ee(u,l,c,n.options)),Y(s,a)||(i.prevFromRect=a,i.prevToRect=s,t||(t=n.options.animation),n.animate(i,u,s,t)),t&&(r=!0,o=Math.max(o,t),clearTimeout(i.animationResetTimer),i.animationResetTimer=setTimeout((function(){i.animationTime=0,i.prevFromRect=null,i.fromRect=null,i.prevToRect=null,i.thisAnimationDuration=null}),t),i.thisAnimationDuration=t)})),clearTimeout(e),r?e=setTimeout((function(){"function"===typeof i&&i()}),o):"function"===typeof i&&i(),t=[]},animate:function(e,t,i,n){if(n){D(e,"transition",""),D(e,"transform","");var r=N(this.el),o=r&&r.a,a=r&&r.d,s=(t.left-i.left)/(o||1),l=(t.top-i.top)/(a||1);e.animatingX=!!s,e.animatingY=!!l,D(e,"transform","translate3d("+s+"px,"+l+"px,0)"),Q(e),D(e,"transition","transform "+n+"ms"+(this.options.easing?" "+this.options.easing:"")),D(e,"transform","translate3d(0,0,0)"),"number"===typeof e.animated&&clearTimeout(e.animated),e.animated=setTimeout((function(){D(e,"transition",""),D(e,"transform",""),e.animated=!1,e.animatingX=!1,e.animatingY=!1}),n)}}}}function Q(e){return e.offsetWidth}function ee(e,t,i,n){return Math.sqrt(Math.pow(t.top-e.top,2)+Math.pow(t.left-e.left,2))/Math.sqrt(Math.pow(t.top-i.top,2)+Math.pow(t.left-i.left,2))*n.animation}var te=[],ie={initializeByDefault:!0},ne={mount:function(e){for(var t in ie)ie.hasOwnProperty(t)&&!(t in e)&&(e[t]=ie[t]);te.push(e)},pluginEvent:function(e,t,i){var n=this;this.eventCanceled=!1,i.cancel=function(){n.eventCanceled=!0};var r=e+"Global";te.forEach((function(n){t[n.pluginName]&&(t[n.pluginName][r]&&t[n.pluginName][r](s({sortable:t},i)),t.options[n.pluginName]&&t[n.pluginName][e]&&t[n.pluginName][e](s({sortable:t},i)))}))},initializePlugins:function(e,t,i,n){for(var r in te.forEach((function(n){var r=n.pluginName;if(e.options[r]||n.initializeByDefault){var o=new n(e,t,e.options);o.sortable=e,o.options=e.options,e[r]=o,a(i,o.defaults)}})),e.options)if(e.options.hasOwnProperty(r)){var o=this.modifyOption(e,r,e.options[r]);"undefined"!==typeof o&&(e.options[r]=o)}},getEventProperties:function(e,t){var i={};return te.forEach((function(n){"function"===typeof n.eventProperties&&a(i,n.eventProperties.call(t[n.pluginName],e))})),i},modifyOption:function(e,t,i){var n;return te.forEach((function(r){e[r.pluginName]&&r.optionListeners&&"function"===typeof r.optionListeners[t]&&(n=r.optionListeners[t].call(e[r.pluginName],i))})),n}};function re(e){var t=e.sortable,i=e.rootEl,n=e.name,r=e.targetEl,o=e.cloneEl,a=e.toEl,l=e.fromEl,c=e.oldIndex,u=e.newIndex,d=e.oldDraggableIndex,h=e.newDraggableIndex,p=e.originalEvent,f=e.putSortable,m=e.extraEventProperties;if(t=t||i&&i[Z],t){var y,b=t.options,x="on"+n.charAt(0).toUpperCase()+n.substr(1);!window.CustomEvent||v||g?(y=document.createEvent("Event"),y.initEvent(n,!0,!0)):y=new CustomEvent(n,{bubbles:!0,cancelable:!0}),y.to=a||i,y.from=l||i,y.item=r||i,y.clone=o,y.oldIndex=c,y.newIndex=u,y.oldDraggableIndex=d,y.newDraggableIndex=h,y.originalEvent=p,y.pullMode=f?f.lastPutMode:void 0;var w=s({},m,ne.getEventProperties(n,t));for(var _ in w)y[_]=w[_];i&&i.dispatchEvent(y),b[x]&&b[x].call(t,y)}}var oe=function(e,t){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=i.evt,r=c(i,["evt"]);ne.pluginEvent.bind(Qe)(e,t,s({dragEl:se,parentEl:le,ghostEl:ce,rootEl:ue,nextEl:de,lastDownEl:he,cloneEl:pe,cloneHidden:fe,dragStarted:ke,putSortable:xe,activeSortable:Qe.active,originalEvent:n,oldIndex:me,oldDraggableIndex:ge,newIndex:ve,newDraggableIndex:ye,hideGhostForTarget:Ge,unhideGhostForTarget:Ke,cloneNowHidden:function(){fe=!0},cloneNowShown:function(){fe=!1},dispatchSortableEvent:function(e){ae({sortable:t,name:e,originalEvent:n})}},r))};function ae(e){re(s({putSortable:xe,cloneEl:pe,targetEl:se,rootEl:ue,oldIndex:me,oldDraggableIndex:ge,newIndex:ve,newDraggableIndex:ye},e))}var se,le,ce,ue,de,he,pe,fe,me,ve,ge,ye,be,xe,we,_e,Se,Oe,Ee,Te,ke,Ae,ze,Me,De,Ne=!1,Ce=!1,je=[],Re=!1,Ie=!1,Le=[],Fe=!1,Pe=[],Ve="undefined"!==typeof document,$e=x,He=g||v?"cssFloat":"float",Be=Ve&&!w&&!x&&"draggable"in document.createElement("div"),Ye=function(){if(Ve){if(v)return!1;var e=document.createElement("x");return e.style.cssText="pointer-events:auto","auto"===e.style.pointerEvents}}(),qe=function(e,t){var i=D(e),n=parseInt(i.width)-parseInt(i.paddingLeft)-parseInt(i.paddingRight)-parseInt(i.borderLeftWidth)-parseInt(i.borderRightWidth),r=L(e,0,t),o=L(e,1,t),a=r&&D(r),s=o&&D(o),l=a&&parseInt(a.marginLeft)+parseInt(a.marginRight)+R(r).width,c=s&&parseInt(s.marginLeft)+parseInt(s.marginRight)+R(o).width;if("flex"===i.display)return"column"===i.flexDirection||"column-reverse"===i.flexDirection?"vertical":"horizontal";if("grid"===i.display)return i.gridTemplateColumns.split(" ").length<=1?"vertical":"horizontal";if(r&&a["float"]&&"none"!==a["float"]){var u="left"===a["float"]?"left":"right";return!o||"both"!==s.clear&&s.clear!==u?"horizontal":"vertical"}return r&&("block"===a.display||"flex"===a.display||"table"===a.display||"grid"===a.display||l>=n&&"none"===i[He]||o&&"none"===i[He]&&l+c>n)?"vertical":"horizontal"},Xe=function(e,t,i){var n=i?e.left:e.top,r=i?e.right:e.bottom,o=i?e.width:e.height,a=i?t.left:t.top,s=i?t.right:t.bottom,l=i?t.width:t.height;return n===a||r===s||n+o/2===a+l/2},Ue=function(e,t){var i;return je.some((function(n){if(!F(n)){var r=R(n),o=n[Z].options.emptyInsertThreshold,a=e>=r.left-o&&e<=r.right+o,s=t>=r.top-o&&t<=r.bottom+o;return o&&a&&s?i=n:void 0}})),i},We=function(e){function t(e,i){return function(n,r,o,a){var s=n.options.group.name&&r.options.group.name&&n.options.group.name===r.options.group.name;if(null==e&&(i||s))return!0;if(null==e||!1===e)return!1;if(i&&"clone"===e)return e;if("function"===typeof e)return t(e(n,r,o,a),i)(n,r,o,a);var l=(i?n:r).options.group.name;return!0===e||"string"===typeof e&&e===l||e.join&&e.indexOf(l)>-1}}var i={},n=e.group;n&&"object"==r(n)||(n={name:n}),i.name=n.name,i.checkPull=t(n.pull,!0),i.checkPut=t(n.put),i.revertClone=n.revertClone,e.group=i},Ge=function(){!Ye&&ce&&D(ce,"display","none")},Ke=function(){!Ye&&ce&&D(ce,"display","")};Ve&&document.addEventListener("click",(function(e){if(Ce)return e.preventDefault(),e.stopPropagation&&e.stopPropagation(),e.stopImmediatePropagation&&e.stopImmediatePropagation(),Ce=!1,!1}),!0);var Ze=function(e){if(se){e=e.touches?e.touches[0]:e;var t=Ue(e.clientX,e.clientY);if(t){var i={};for(var n in e)e.hasOwnProperty(n)&&(i[n]=e[n]);i.target=i.rootEl=t,i.preventDefault=void 0,i.stopPropagation=void 0,t[Z]._onDragOver(i)}}},Je=function(e){se&&se.parentNode[Z]._isOutsideThisEl(e.target)};function Qe(e,t){if(!e||!e.nodeType||1!==e.nodeType)throw"Sortable: `el` must be an HTMLElement, not ".concat({}.toString.call(e));this.el=e,this.options=t=a({},t),e[Z]=this;var i={group:null,sort:!0,disabled:!1,store:null,handle:null,draggable:/^[uo]l$/i.test(e.nodeName)?">li":">*",swapThreshold:1,invertSwap:!1,invertedSwapThreshold:null,removeCloneOnHide:!0,direction:function(){return qe(e,this.options)},ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",dragClass:"sortable-drag",ignore:"a, img",filter:null,preventOnFilter:!0,animation:0,easing:null,setData:function(e,t){e.setData("Text",t.textContent)},dropBubble:!1,dragoverBubble:!1,dataIdAttr:"data-id",delay:0,delayOnTouchOnly:!1,touchStartThreshold:(Number.parseInt?Number:window).parseInt(window.devicePixelRatio,10)||1,forceFallback:!1,fallbackClass:"sortable-fallback",fallbackOnBody:!1,fallbackTolerance:0,fallbackOffset:{x:0,y:0},supportPointer:!1!==Qe.supportPointer&&"PointerEvent"in window,emptyInsertThreshold:5};for(var n in ne.initializePlugins(this,e,i),i)!(n in t)&&(t[n]=i[n]);for(var r in We(t),this)"_"===r.charAt(0)&&"function"===typeof this[r]&&(this[r]=this[r].bind(this));this.nativeDraggable=!t.forceFallback&&Be,this.nativeDraggable&&(this.options.touchStartThreshold=1),t.supportPointer?S(e,"pointerdown",this._onTapStart):(S(e,"mousedown",this._onTapStart),S(e,"touchstart",this._onTapStart)),this.nativeDraggable&&(S(e,"dragover",this),S(e,"dragenter",this)),je.push(this.el),t.store&&t.store.get&&this.sort(t.store.get(this)||[]),a(this,J())}function et(e){e.dataTransfer&&(e.dataTransfer.dropEffect="move"),e.cancelable&&e.preventDefault()}function tt(e,t,i,n,r,o,a,s){var l,c,u=e[Z],d=u.options.onMove;return!window.CustomEvent||v||g?(l=document.createEvent("Event"),l.initEvent("move",!0,!0)):l=new CustomEvent("move",{bubbles:!0,cancelable:!0}),l.to=t,l.from=e,l.dragged=i,l.draggedRect=n,l.related=r||t,l.relatedRect=o||R(t),l.willInsertAfter=s,l.originalEvent=a,e.dispatchEvent(l),d&&(c=d.call(u,l,a)),c}function it(e){e.draggable=!1}function nt(){Fe=!1}function rt(e,t,i){var n=R(F(i.el,i.options.draggable)),r=10;return t?e.clientX>n.right+r||e.clientX<=n.right&&e.clientY>n.bottom&&e.clientX>=n.left:e.clientX>n.right&&e.clientY>n.top||e.clientX<=n.right&&e.clientY>n.bottom+r}function ot(e,t,i,n,r,o,a,s){var l=n?e.clientY:e.clientX,c=n?i.height:i.width,u=n?i.top:i.left,d=n?i.bottom:i.right,h=!1;if(!a)if(s&&Me<c*r){if(!Re&&(1===ze?l>u+c*o/2:l<d-c*o/2)&&(Re=!0),Re)h=!0;else if(1===ze?l<u+Me:l>d-Me)return-ze}else if(l>u+c*(1-r)/2&&l<d-c*(1-r)/2)return at(t);return h=h||a,h&&(l<u+c*o/2||l>d-c*o/2)?l>u+c/2?1:-1:0}function at(e){return P(se)<P(e)?1:-1}function st(e){var t=e.tagName+e.className+e.src+e.href+e.textContent,i=t.length,n=0;while(i--)n+=t.charCodeAt(i);return n.toString(36)}function lt(e){Pe.length=0;var t=e.getElementsByTagName("input"),i=t.length;while(i--){var n=t[i];n.checked&&Pe.push(n)}}function ct(e){return setTimeout(e,0)}function ut(e){return clearTimeout(e)}Qe.prototype={constructor:Qe,_isOutsideThisEl:function(e){this.el.contains(e)||e===this.el||(Ae=null)},_getDirection:function(e,t){return"function"===typeof this.options.direction?this.options.direction.call(this,e,t,se):this.options.direction},_onTapStart:function(e){if(e.cancelable){var t=this,i=this.el,n=this.options,r=n.preventOnFilter,o=e.type,a=e.touches&&e.touches[0]||e.pointerType&&"touch"===e.pointerType&&e,s=(a||e).target,l=e.target.shadowRoot&&(e.path&&e.path[0]||e.composedPath&&e.composedPath()[0])||s,c=n.filter;if(lt(i),!se&&!(/mousedown|pointerdown/.test(o)&&0!==e.button||n.disabled)&&!l.isContentEditable&&(s=k(s,n.draggable,i,!1),(!s||!s.animated)&&he!==s)){if(me=P(s),ge=P(s,n.draggable),"function"===typeof c){if(c.call(this,e,s,this))return ae({sortable:t,rootEl:l,name:"filter",targetEl:s,toEl:i,fromEl:i}),oe("filter",t,{evt:e}),void(r&&e.cancelable&&e.preventDefault())}else if(c&&(c=c.split(",").some((function(n){if(n=k(l,n.trim(),i,!1),n)return ae({sortable:t,rootEl:n,name:"filter",targetEl:s,fromEl:i,toEl:i}),oe("filter",t,{evt:e}),!0})),c))return void(r&&e.cancelable&&e.preventDefault());n.handle&&!k(l,n.handle,i,!1)||this._prepareDragStart(e,a,s)}}},_prepareDragStart:function(e,t,i){var n,r=this,o=r.el,a=r.options,s=o.ownerDocument;if(i&&!se&&i.parentNode===o){var l=R(i);if(ue=o,se=i,le=se.parentNode,de=se.nextSibling,he=i,be=a.group,Qe.dragged=se,we={target:se,clientX:(t||e).clientX,clientY:(t||e).clientY},Ee=we.clientX-l.left,Te=we.clientY-l.top,this._lastX=(t||e).clientX,this._lastY=(t||e).clientY,se.style["will-change"]="all",n=function(){oe("delayEnded",r,{evt:e}),Qe.eventCanceled?r._onDrop():(r._disableDelayedDragEvents(),!y&&r.nativeDraggable&&(se.draggable=!0),r._triggerDragStart(e,t),ae({sortable:r,name:"choose",originalEvent:e}),M(se,a.chosenClass,!0))},a.ignore.split(",").forEach((function(e){C(se,e.trim(),it)})),S(s,"dragover",Ze),S(s,"mousemove",Ze),S(s,"touchmove",Ze),S(s,"mouseup",r._onDrop),S(s,"touchend",r._onDrop),S(s,"touchcancel",r._onDrop),y&&this.nativeDraggable&&(this.options.touchStartThreshold=4,se.draggable=!0),oe("delayStart",this,{evt:e}),!a.delay||a.delayOnTouchOnly&&!t||this.nativeDraggable&&(g||v))n();else{if(Qe.eventCanceled)return void this._onDrop();S(s,"mouseup",r._disableDelayedDrag),S(s,"touchend",r._disableDelayedDrag),S(s,"touchcancel",r._disableDelayedDrag),S(s,"mousemove",r._delayedDragTouchMoveHandler),S(s,"touchmove",r._delayedDragTouchMoveHandler),a.supportPointer&&S(s,"pointermove",r._delayedDragTouchMoveHandler),r._dragStartTimer=setTimeout(n,a.delay)}}},_delayedDragTouchMoveHandler:function(e){var t=e.touches?e.touches[0]:e;Math.max(Math.abs(t.clientX-this._lastX),Math.abs(t.clientY-this._lastY))>=Math.floor(this.options.touchStartThreshold/(this.nativeDraggable&&window.devicePixelRatio||1))&&this._disableDelayedDrag()},_disableDelayedDrag:function(){se&&it(se),clearTimeout(this._dragStartTimer),this._disableDelayedDragEvents()},_disableDelayedDragEvents:function(){var e=this.el.ownerDocument;O(e,"mouseup",this._disableDelayedDrag),O(e,"touchend",this._disableDelayedDrag),O(e,"touchcancel",this._disableDelayedDrag),O(e,"mousemove",this._delayedDragTouchMoveHandler),O(e,"touchmove",this._delayedDragTouchMoveHandler),O(e,"pointermove",this._delayedDragTouchMoveHandler)},_triggerDragStart:function(e,t){t=t||"touch"==e.pointerType&&e,!this.nativeDraggable||t?this.options.supportPointer?S(document,"pointermove",this._onTouchMove):S(document,t?"touchmove":"mousemove",this._onTouchMove):(S(se,"dragend",this),S(ue,"dragstart",this._onDragStart));try{document.selection?ct((function(){document.selection.empty()})):window.getSelection().removeAllRanges()}catch(i){}},_dragStarted:function(e,t){if(Ne=!1,ue&&se){oe("dragStarted",this,{evt:t}),this.nativeDraggable&&S(document,"dragover",Je);var i=this.options;!e&&M(se,i.dragClass,!1),M(se,i.ghostClass,!0),Qe.active=this,e&&this._appendGhost(),ae({sortable:this,name:"start",originalEvent:t})}else this._nulling()},_emulateDragOver:function(){if(_e){this._lastX=_e.clientX,this._lastY=_e.clientY,Ge();var e=document.elementFromPoint(_e.clientX,_e.clientY),t=e;while(e&&e.shadowRoot){if(e=e.shadowRoot.elementFromPoint(_e.clientX,_e.clientY),e===t)break;t=e}if(se.parentNode[Z]._isOutsideThisEl(e),t)do{if(t[Z]){var i=void 0;if(i=t[Z]._onDragOver({clientX:_e.clientX,clientY:_e.clientY,target:e,rootEl:t}),i&&!this.options.dragoverBubble)break}e=t}while(t=t.parentNode);Ke()}},_onTouchMove:function(e){if(we){var t=this.options,i=t.fallbackTolerance,n=t.fallbackOffset,r=e.touches?e.touches[0]:e,o=ce&&N(ce,!0),a=ce&&o&&o.a,s=ce&&o&&o.d,l=$e&&De&&V(De),c=(r.clientX-we.clientX+n.x)/(a||1)+(l?l[0]-Le[0]:0)/(a||1),u=(r.clientY-we.clientY+n.y)/(s||1)+(l?l[1]-Le[1]:0)/(s||1);if(!Qe.active&&!Ne){if(i&&Math.max(Math.abs(r.clientX-this._lastX),Math.abs(r.clientY-this._lastY))<i)return;this._onDragStart(e,!0)}if(ce){o?(o.e+=c-(Se||0),o.f+=u-(Oe||0)):o={a:1,b:0,c:0,d:1,e:c,f:u};var d="matrix(".concat(o.a,",").concat(o.b,",").concat(o.c,",").concat(o.d,",").concat(o.e,",").concat(o.f,")");D(ce,"webkitTransform",d),D(ce,"mozTransform",d),D(ce,"msTransform",d),D(ce,"transform",d),Se=c,Oe=u,_e=r}e.cancelable&&e.preventDefault()}},_appendGhost:function(){if(!ce){var e=this.options.fallbackOnBody?document.body:ue,t=R(se,!0,$e,!0,e),i=this.options;if($e){De=e;while("static"===D(De,"position")&&"none"===D(De,"transform")&&De!==document)De=De.parentNode;De!==document.body&&De!==document.documentElement?(De===document&&(De=j()),t.top+=De.scrollTop,t.left+=De.scrollLeft):De=j(),Le=V(De)}ce=se.cloneNode(!0),M(ce,i.ghostClass,!1),M(ce,i.fallbackClass,!0),M(ce,i.dragClass,!0),D(ce,"transition",""),D(ce,"transform",""),D(ce,"box-sizing","border-box"),D(ce,"margin",0),D(ce,"top",t.top),D(ce,"left",t.left),D(ce,"width",t.width),D(ce,"height",t.height),D(ce,"opacity","0.8"),D(ce,"position",$e?"absolute":"fixed"),D(ce,"zIndex","100000"),D(ce,"pointerEvents","none"),Qe.ghost=ce,e.appendChild(ce),D(ce,"transform-origin",Ee/parseInt(ce.style.width)*100+"% "+Te/parseInt(ce.style.height)*100+"%")}},_onDragStart:function(e,t){var i=this,n=e.dataTransfer,r=i.options;oe("dragStart",this,{evt:e}),Qe.eventCanceled?this._onDrop():(oe("setupClone",this),Qe.eventCanceled||(pe=W(se),pe.draggable=!1,pe.style["will-change"]="",this._hideClone(),M(pe,this.options.chosenClass,!1),Qe.clone=pe),i.cloneId=ct((function(){oe("clone",i),Qe.eventCanceled||(i.options.removeCloneOnHide||ue.insertBefore(pe,se),i._hideClone(),ae({sortable:i,name:"clone"}))})),!t&&M(se,r.dragClass,!0),t?(Ce=!0,i._loopId=setInterval(i._emulateDragOver,50)):(O(document,"mouseup",i._onDrop),O(document,"touchend",i._onDrop),O(document,"touchcancel",i._onDrop),n&&(n.effectAllowed="move",r.setData&&r.setData.call(i,n,se)),S(document,"drop",i),D(se,"transform","translateZ(0)")),Ne=!0,i._dragStartId=ct(i._dragStarted.bind(i,t,e)),S(document,"selectstart",i),ke=!0,b&&D(document.body,"user-select","none"))},_onDragOver:function(e){var t,i,n,r,o=this.el,a=e.target,l=this.options,c=l.group,u=Qe.active,d=be===c,h=l.sort,p=xe||u,f=this,m=!1;if(!Fe){if(void 0!==e.preventDefault&&e.cancelable&&e.preventDefault(),a=k(a,l.draggable,o,!0),N("dragOver"),Qe.eventCanceled)return m;if(se.contains(e.target)||a.animated&&a.animatingX&&a.animatingY||f._ignoreWhileAnimating===a)return j(!1);if(Ce=!1,u&&!l.disabled&&(d?h||(n=!ue.contains(se)):xe===this||(this.lastPutMode=be.checkPull(this,u,se,e))&&c.checkPut(this,u,se,e))){if(r="vertical"===this._getDirection(e,a),t=R(se),N("dragOverValid"),Qe.eventCanceled)return m;if(n)return le=ue,C(),this._hideClone(),N("revert"),Qe.eventCanceled||(de?ue.insertBefore(se,de):ue.appendChild(se)),j(!0);var v=F(o,l.draggable);if(!v||rt(e,r,this)&&!v.animated){if(v===se)return j(!1);if(v&&o===e.target&&(a=v),a&&(i=R(a)),!1!==tt(ue,o,se,t,a,i,e,!!a))return C(),o.appendChild(se),le=o,L(),j(!0)}else if(a.parentNode===o){i=R(a);var g,y,b=0,x=se.parentNode!==o,w=!Xe(se.animated&&se.toRect||t,a.animated&&a.toRect||i,r),_=r?"top":"left",S=I(a,"top","top")||I(se,"top","top"),O=S?S.scrollTop:void 0;if(Ae!==a&&(g=i[_],Re=!1,Ie=!w&&l.invertSwap||x),b=ot(e,a,i,r,w?1:l.swapThreshold,null==l.invertedSwapThreshold?l.swapThreshold:l.invertedSwapThreshold,Ie,Ae===a),0!==b){var E=P(se);do{E-=b,y=le.children[E]}while(y&&("none"===D(y,"display")||y===ce))}if(0===b||y===a)return j(!1);Ae=a,ze=b;var T=a.nextElementSibling,A=!1;A=1===b;var z=tt(ue,o,se,t,a,i,e,A);if(!1!==z)return 1!==z&&-1!==z||(A=1===z),Fe=!0,setTimeout(nt,30),C(),A&&!T?o.appendChild(se):a.parentNode.insertBefore(se,A?T:a),S&&U(S,0,O-S.scrollTop),le=se.parentNode,void 0===g||Ie||(Me=Math.abs(g-R(a)[_])),L(),j(!0)}if(o.contains(se))return j(!1)}return!1}function N(l,c){oe(l,f,s({evt:e,isOwner:d,axis:r?"vertical":"horizontal",revert:n,dragRect:t,targetRect:i,canSort:h,fromSortable:p,target:a,completed:j,onMove:function(i,n){return tt(ue,o,se,t,i,R(i),e,n)},changed:L},c))}function C(){N("dragOverAnimationCapture"),f.captureAnimationState(),f!==p&&p.captureAnimationState()}function j(t){return N("dragOverCompleted",{insertion:t}),t&&(d?u._hideClone():u._showClone(f),f!==p&&(M(se,xe?xe.options.ghostClass:u.options.ghostClass,!1),M(se,l.ghostClass,!0)),xe!==f&&f!==Qe.active?xe=f:f===Qe.active&&xe&&(xe=null),p===f&&(f._ignoreWhileAnimating=a),f.animateAll((function(){N("dragOverAnimationComplete"),f._ignoreWhileAnimating=null})),f!==p&&(p.animateAll(),p._ignoreWhileAnimating=null)),(a===se&&!se.animated||a===o&&!a.animated)&&(Ae=null),l.dragoverBubble||e.rootEl||a===document||(se.parentNode[Z]._isOutsideThisEl(e.target),!t&&Ze(e)),!l.dragoverBubble&&e.stopPropagation&&e.stopPropagation(),m=!0}function L(){ve=P(se),ye=P(se,l.draggable),ae({sortable:f,name:"change",toEl:o,newIndex:ve,newDraggableIndex:ye,originalEvent:e})}},_ignoreWhileAnimating:null,_offMoveEvents:function(){O(document,"mousemove",this._onTouchMove),O(document,"touchmove",this._onTouchMove),O(document,"pointermove",this._onTouchMove),O(document,"dragover",Ze),O(document,"mousemove",Ze),O(document,"touchmove",Ze)},_offUpEvents:function(){var e=this.el.ownerDocument;O(e,"mouseup",this._onDrop),O(e,"touchend",this._onDrop),O(e,"pointerup",this._onDrop),O(e,"touchcancel",this._onDrop),O(document,"selectstart",this)},_onDrop:function(e){var t=this.el,i=this.options;ve=P(se),ye=P(se,i.draggable),oe("drop",this,{evt:e}),le=se&&se.parentNode,ve=P(se),ye=P(se,i.draggable),Qe.eventCanceled||(Ne=!1,Ie=!1,Re=!1,clearInterval(this._loopId),clearTimeout(this._dragStartTimer),ut(this.cloneId),ut(this._dragStartId),this.nativeDraggable&&(O(document,"drop",this),O(t,"dragstart",this._onDragStart)),this._offMoveEvents(),this._offUpEvents(),b&&D(document.body,"user-select",""),D(se,"transform",""),e&&(ke&&(e.cancelable&&e.preventDefault(),!i.dropBubble&&e.stopPropagation()),ce&&ce.parentNode&&ce.parentNode.removeChild(ce),(ue===le||xe&&"clone"!==xe.lastPutMode)&&pe&&pe.parentNode&&pe.parentNode.removeChild(pe),se&&(this.nativeDraggable&&O(se,"dragend",this),it(se),se.style["will-change"]="",ke&&!Ne&&M(se,xe?xe.options.ghostClass:this.options.ghostClass,!1),M(se,this.options.chosenClass,!1),ae({sortable:this,name:"unchoose",toEl:le,newIndex:null,newDraggableIndex:null,originalEvent:e}),ue!==le?(ve>=0&&(ae({rootEl:le,name:"add",toEl:le,fromEl:ue,originalEvent:e}),ae({sortable:this,name:"remove",toEl:le,originalEvent:e}),ae({rootEl:le,name:"sort",toEl:le,fromEl:ue,originalEvent:e}),ae({sortable:this,name:"sort",toEl:le,originalEvent:e})),xe&&xe.save()):ve!==me&&ve>=0&&(ae({sortable:this,name:"update",toEl:le,originalEvent:e}),ae({sortable:this,name:"sort",toEl:le,originalEvent:e})),Qe.active&&(null!=ve&&-1!==ve||(ve=me,ye=ge),ae({sortable:this,name:"end",toEl:le,originalEvent:e}),this.save())))),this._nulling()},_nulling:function(){oe("nulling",this),ue=se=le=ce=de=pe=he=fe=we=_e=ke=ve=ye=me=ge=Ae=ze=xe=be=Qe.dragged=Qe.ghost=Qe.clone=Qe.active=null,Pe.forEach((function(e){e.checked=!0})),Pe.length=Se=Oe=0},handleEvent:function(e){switch(e.type){case"drop":case"dragend":this._onDrop(e);break;case"dragenter":case"dragover":se&&(this._onDragOver(e),et(e));break;case"selectstart":e.preventDefault();break}},toArray:function(){for(var e,t=[],i=this.el.children,n=0,r=i.length,o=this.options;n<r;n++)e=i[n],k(e,o.draggable,this.el,!1)&&t.push(e.getAttribute(o.dataIdAttr)||st(e));return t},sort:function(e){var t={},i=this.el;this.toArray().forEach((function(e,n){var r=i.children[n];k(r,this.options.draggable,i,!1)&&(t[e]=r)}),this),e.forEach((function(e){t[e]&&(i.removeChild(t[e]),i.appendChild(t[e]))}))},save:function(){var e=this.options.store;e&&e.set&&e.set(this)},closest:function(e,t){return k(e,t||this.options.draggable,this.el,!1)},option:function(e,t){var i=this.options;if(void 0===t)return i[e];var n=ne.modifyOption(this,e,t);i[e]="undefined"!==typeof n?n:t,"group"===e&&We(i)},destroy:function(){oe("destroy",this);var e=this.el;e[Z]=null,O(e,"mousedown",this._onTapStart),O(e,"touchstart",this._onTapStart),O(e,"pointerdown",this._onTapStart),this.nativeDraggable&&(O(e,"dragover",this),O(e,"dragenter",this)),Array.prototype.forEach.call(e.querySelectorAll("[draggable]"),(function(e){e.removeAttribute("draggable")})),this._onDrop(),this._disableDelayedDragEvents(),je.splice(je.indexOf(this.el),1),this.el=e=null},_hideClone:function(){if(!fe){if(oe("hideClone",this),Qe.eventCanceled)return;D(pe,"display","none"),this.options.removeCloneOnHide&&pe.parentNode&&pe.parentNode.removeChild(pe),fe=!0}},_showClone:function(e){if("clone"===e.lastPutMode){if(fe){if(oe("showClone",this),Qe.eventCanceled)return;ue.contains(se)&&!this.options.group.revertClone?ue.insertBefore(pe,se):de?ue.insertBefore(pe,de):ue.appendChild(pe),this.options.group.revertClone&&this.animate(se,pe),D(pe,"display",""),fe=!1}}else this._hideClone()}},Ve&&S(document,"touchmove",(function(e){(Qe.active||Ne)&&e.cancelable&&e.preventDefault()})),Qe.utils={on:S,off:O,css:D,find:C,is:function(e,t){return!!k(e,t,e,!1)},extend:B,throttle:q,closest:k,toggleClass:M,clone:W,index:P,nextTick:ct,cancelNextTick:ut,detectDirection:qe,getChild:L},Qe.get=function(e){return e[Z]},Qe.mount=function(){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];t[0].constructor===Array&&(t=t[0]),t.forEach((function(e){if(!e.prototype||!e.prototype.constructor)throw"Sortable: Mounted plugin must be a constructor function, not ".concat({}.toString.call(e));e.utils&&(Qe.utils=s({},Qe.utils,e.utils)),ne.mount(e)}))},Qe.create=function(e,t){return new Qe(e,t)},Qe.version=f;var dt,ht,pt,ft,mt,vt,gt=[],yt=!1;function bt(){function e(){for(var e in this.defaults={scroll:!0,scrollSensitivity:30,scrollSpeed:10,bubbleScroll:!0},this)"_"===e.charAt(0)&&"function"===typeof this[e]&&(this[e]=this[e].bind(this))}return e.prototype={dragStarted:function(e){var t=e.originalEvent;this.sortable.nativeDraggable?S(document,"dragover",this._handleAutoScroll):this.options.supportPointer?S(document,"pointermove",this._handleFallbackAutoScroll):t.touches?S(document,"touchmove",this._handleFallbackAutoScroll):S(document,"mousemove",this._handleFallbackAutoScroll)},dragOverCompleted:function(e){var t=e.originalEvent;this.options.dragOverBubble||t.rootEl||this._handleAutoScroll(t)},drop:function(){this.sortable.nativeDraggable?O(document,"dragover",this._handleAutoScroll):(O(document,"pointermove",this._handleFallbackAutoScroll),O(document,"touchmove",this._handleFallbackAutoScroll),O(document,"mousemove",this._handleFallbackAutoScroll)),wt(),xt(),X()},nulling:function(){mt=ht=dt=yt=vt=pt=ft=null,gt.length=0},_handleFallbackAutoScroll:function(e){this._handleAutoScroll(e,!0)},_handleAutoScroll:function(e,t){var i=this,n=(e.touches?e.touches[0]:e).clientX,r=(e.touches?e.touches[0]:e).clientY,o=document.elementFromPoint(n,r);if(mt=e,t||g||v||b){St(e,this.options,o,t);var a=H(o,!0);!yt||vt&&n===pt&&r===ft||(vt&&wt(),vt=setInterval((function(){var o=H(document.elementFromPoint(n,r),!0);o!==a&&(a=o,xt()),St(e,i.options,o,t)}),10),pt=n,ft=r)}else{if(!this.options.bubbleScroll||H(o,!0)===j())return void xt();St(e,this.options,H(o,!1),!1)}}},a(e,{pluginName:"scroll",initializeByDefault:!0})}function xt(){gt.forEach((function(e){clearInterval(e.pid)})),gt=[]}function wt(){clearInterval(vt)}var _t,St=q((function(e,t,i,n){if(t.scroll){var r,o=(e.touches?e.touches[0]:e).clientX,a=(e.touches?e.touches[0]:e).clientY,s=t.scrollSensitivity,l=t.scrollSpeed,c=j(),u=!1;ht!==i&&(ht=i,xt(),dt=t.scroll,r=t.scrollFn,!0===dt&&(dt=H(i,!0)));var d=0,h=dt;do{var p=h,f=R(p),m=f.top,v=f.bottom,g=f.left,y=f.right,b=f.width,x=f.height,w=void 0,_=void 0,S=p.scrollWidth,O=p.scrollHeight,E=D(p),T=p.scrollLeft,k=p.scrollTop;p===c?(w=b<S&&("auto"===E.overflowX||"scroll"===E.overflowX||"visible"===E.overflowX),_=x<O&&("auto"===E.overflowY||"scroll"===E.overflowY||"visible"===E.overflowY)):(w=b<S&&("auto"===E.overflowX||"scroll"===E.overflowX),_=x<O&&("auto"===E.overflowY||"scroll"===E.overflowY));var A=w&&(Math.abs(y-o)<=s&&T+b<S)-(Math.abs(g-o)<=s&&!!T),z=_&&(Math.abs(v-a)<=s&&k+x<O)-(Math.abs(m-a)<=s&&!!k);if(!gt[d])for(var M=0;M<=d;M++)gt[M]||(gt[M]={});gt[d].vx==A&&gt[d].vy==z&&gt[d].el===p||(gt[d].el=p,gt[d].vx=A,gt[d].vy=z,clearInterval(gt[d].pid),0==A&&0==z||(u=!0,gt[d].pid=setInterval(function(){n&&0===this.layer&&Qe.active._onTouchMove(mt);var t=gt[this.layer].vy?gt[this.layer].vy*l:0,i=gt[this.layer].vx?gt[this.layer].vx*l:0;"function"===typeof r&&"continue"!==r.call(Qe.dragged.parentNode[Z],i,t,e,mt,gt[this.layer].el)||U(gt[this.layer].el,i,t)}.bind({layer:d}),24))),d++}while(t.bubbleScroll&&h!==c&&(h=H(h,!1)));yt=u}}),30),Ot=function(e){var t=e.originalEvent,i=e.putSortable,n=e.dragEl,r=e.activeSortable,o=e.dispatchSortableEvent,a=e.hideGhostForTarget,s=e.unhideGhostForTarget;if(t){var l=i||r;a();var c=t.changedTouches&&t.changedTouches.length?t.changedTouches[0]:t,u=document.elementFromPoint(c.clientX,c.clientY);s(),l&&!l.el.contains(u)&&(o("spill"),this.onSpill({dragEl:n,putSortable:i}))}};function Et(){}function Tt(){}function kt(){function e(){this.defaults={swapClass:"sortable-swap-highlight"}}return e.prototype={dragStart:function(e){var t=e.dragEl;_t=t},dragOverValid:function(e){var t=e.completed,i=e.target,n=e.onMove,r=e.activeSortable,o=e.changed,a=e.cancel;if(r.options.swap){var s=this.sortable.el,l=this.options;if(i&&i!==s){var c=_t;!1!==n(i)?(M(i,l.swapClass,!0),_t=i):_t=null,c&&c!==_t&&M(c,l.swapClass,!1)}o(),t(!0),a()}},drop:function(e){var t=e.activeSortable,i=e.putSortable,n=e.dragEl,r=i||this.sortable,o=this.options;_t&&M(_t,o.swapClass,!1),_t&&(o.swap||i&&i.options.swap)&&n!==_t&&(r.captureAnimationState(),r!==t&&t.captureAnimationState(),At(n,_t),r.animateAll(),r!==t&&t.animateAll())},nulling:function(){_t=null}},a(e,{pluginName:"swap",eventProperties:function(){return{swapItem:_t}}})}function At(e,t){var i,n,r=e.parentNode,o=t.parentNode;r&&o&&!r.isEqualNode(t)&&!o.isEqualNode(e)&&(i=P(e),n=P(t),r.isEqualNode(o)&&i<n&&n++,r.insertBefore(t,r.children[i]),o.insertBefore(e,o.children[n]))}Et.prototype={startIndex:null,dragStart:function(e){var t=e.oldDraggableIndex;this.startIndex=t},onSpill:function(e){var t=e.dragEl,i=e.putSortable;this.sortable.captureAnimationState(),i&&i.captureAnimationState();var n=L(this.sortable.el,this.startIndex,this.options);n?this.sortable.el.insertBefore(t,n):this.sortable.el.appendChild(t),this.sortable.animateAll(),i&&i.animateAll()},drop:Ot},a(Et,{pluginName:"revertOnSpill"}),Tt.prototype={onSpill:function(e){var t=e.dragEl,i=e.putSortable,n=i||this.sortable;n.captureAnimationState(),t.parentNode&&t.parentNode.removeChild(t),n.animateAll()},drop:Ot},a(Tt,{pluginName:"removeOnSpill"});var zt,Mt,Dt,Nt,Ct,jt=/^(4([48]26|535|564|847)|2654|5025|8555|866|9093)$/.test(i.j)?null:[],Rt=/^(4([48]26|535|564|847)|2654|5025|8555|866|9093)$/.test(i.j)?null:[],It=!1,Lt=!1,Ft=!1;function Pt(){function e(e){for(var t in this)"_"===t.charAt(0)&&"function"===typeof this[t]&&(this[t]=this[t].bind(this));e.options.supportPointer?S(document,"pointerup",this._deselectMultiDrag):(S(document,"mouseup",this._deselectMultiDrag),S(document,"touchend",this._deselectMultiDrag)),S(document,"keydown",this._checkKeyDown),S(document,"keyup",this._checkKeyUp),this.defaults={selectedClass:"sortable-selected",multiDragKey:null,setData:function(t,i){var n="";jt.length&&Mt===e?jt.forEach((function(e,t){n+=(t?", ":"")+e.textContent})):n=i.textContent,t.setData("Text",n)}}}return e.prototype={multiDragKeyDown:!1,isMultiDrag:!1,delayStartGlobal:function(e){var t=e.dragEl;Dt=t},delayEnded:function(){this.isMultiDrag=~jt.indexOf(Dt)},setupClone:function(e){var t=e.sortable,i=e.cancel;if(this.isMultiDrag){for(var n=0;n<jt.length;n++)Rt.push(W(jt[n])),Rt[n].sortableIndex=jt[n].sortableIndex,Rt[n].draggable=!1,Rt[n].style["will-change"]="",M(Rt[n],this.options.selectedClass,!1),jt[n]===Dt&&M(Rt[n],this.options.chosenClass,!1);t._hideClone(),i()}},clone:function(e){var t=e.sortable,i=e.rootEl,n=e.dispatchSortableEvent,r=e.cancel;this.isMultiDrag&&(this.options.removeCloneOnHide||jt.length&&Mt===t&&($t(!0,i),n("clone"),r()))},showClone:function(e){var t=e.cloneNowShown,i=e.rootEl,n=e.cancel;this.isMultiDrag&&($t(!1,i),Rt.forEach((function(e){D(e,"display","")})),t(),Ct=!1,n())},hideClone:function(e){var t=this,i=(e.sortable,e.cloneNowHidden),n=e.cancel;this.isMultiDrag&&(Rt.forEach((function(e){D(e,"display","none"),t.options.removeCloneOnHide&&e.parentNode&&e.parentNode.removeChild(e)})),i(),Ct=!0,n())},dragStartGlobal:function(e){e.sortable;!this.isMultiDrag&&Mt&&Mt.multiDrag._deselectMultiDrag(),jt.forEach((function(e){e.sortableIndex=P(e)})),jt=jt.sort((function(e,t){return e.sortableIndex-t.sortableIndex})),Ft=!0},dragStarted:function(e){var t=this,i=e.sortable;if(this.isMultiDrag){if(this.options.sort&&(i.captureAnimationState(),this.options.animation)){jt.forEach((function(e){e!==Dt&&D(e,"position","absolute")}));var n=R(Dt,!1,!0,!0);jt.forEach((function(e){e!==Dt&&G(e,n)})),Lt=!0,It=!0}i.animateAll((function(){Lt=!1,It=!1,t.options.animation&&jt.forEach((function(e){K(e)})),t.options.sort&&Ht()}))}},dragOver:function(e){var t=e.target,i=e.completed,n=e.cancel;Lt&&~jt.indexOf(t)&&(i(!1),n())},revert:function(e){var t=e.fromSortable,i=e.rootEl,n=e.sortable,r=e.dragRect;jt.length>1&&(jt.forEach((function(e){n.addAnimationState({target:e,rect:Lt?R(e):r}),K(e),e.fromRect=r,t.removeAnimationState(e)})),Lt=!1,Vt(!this.options.removeCloneOnHide,i))},dragOverCompleted:function(e){var t=e.sortable,i=e.isOwner,n=e.insertion,r=e.activeSortable,o=e.parentEl,a=e.putSortable,s=this.options;if(n){if(i&&r._hideClone(),It=!1,s.animation&&jt.length>1&&(Lt||!i&&!r.options.sort&&!a)){var l=R(Dt,!1,!0,!0);jt.forEach((function(e){e!==Dt&&(G(e,l),o.appendChild(e))})),Lt=!0}if(!i)if(Lt||Ht(),jt.length>1){var c=Ct;r._showClone(t),r.options.animation&&!Ct&&c&&Rt.forEach((function(e){r.addAnimationState({target:e,rect:Nt}),e.fromRect=Nt,e.thisAnimationDuration=null}))}else r._showClone(t)}},dragOverAnimationCapture:function(e){var t=e.dragRect,i=e.isOwner,n=e.activeSortable;if(jt.forEach((function(e){e.thisAnimationDuration=null})),n.options.animation&&!i&&n.multiDrag.isMultiDrag){Nt=a({},t);var r=N(Dt,!0);Nt.top-=r.f,Nt.left-=r.e}},dragOverAnimationComplete:function(){Lt&&(Lt=!1,Ht())},drop:function(e){var t=e.originalEvent,i=e.rootEl,n=e.parentEl,r=e.sortable,o=e.dispatchSortableEvent,a=e.oldIndex,s=e.putSortable,l=s||this.sortable;if(t){var c=this.options,u=n.children;if(!Ft)if(c.multiDragKey&&!this.multiDragKeyDown&&this._deselectMultiDrag(),M(Dt,c.selectedClass,!~jt.indexOf(Dt)),~jt.indexOf(Dt))jt.splice(jt.indexOf(Dt),1),zt=null,re({sortable:r,rootEl:i,name:"deselect",targetEl:Dt,originalEvt:t});else{if(jt.push(Dt),re({sortable:r,rootEl:i,name:"select",targetEl:Dt,originalEvt:t}),t.shiftKey&&zt&&r.el.contains(zt)){var d,h,p=P(zt),f=P(Dt);if(~p&&~f&&p!==f)for(f>p?(h=p,d=f):(h=f,d=p+1);h<d;h++)~jt.indexOf(u[h])||(M(u[h],c.selectedClass,!0),jt.push(u[h]),re({sortable:r,rootEl:i,name:"select",targetEl:u[h],originalEvt:t}))}else zt=Dt;Mt=l}if(Ft&&this.isMultiDrag){if((n[Z].options.sort||n!==i)&&jt.length>1){var m=R(Dt),v=P(Dt,":not(."+this.options.selectedClass+")");if(!It&&c.animation&&(Dt.thisAnimationDuration=null),l.captureAnimationState(),!It&&(c.animation&&(Dt.fromRect=m,jt.forEach((function(e){if(e.thisAnimationDuration=null,e!==Dt){var t=Lt?R(e):m;e.fromRect=t,l.addAnimationState({target:e,rect:t})}}))),Ht(),jt.forEach((function(e){u[v]?n.insertBefore(e,u[v]):n.appendChild(e),v++})),a===P(Dt))){var g=!1;jt.forEach((function(e){e.sortableIndex===P(e)||(g=!0)})),g&&o("update")}jt.forEach((function(e){K(e)})),l.animateAll()}Mt=l}(i===n||s&&"clone"!==s.lastPutMode)&&Rt.forEach((function(e){e.parentNode&&e.parentNode.removeChild(e)}))}},nullingGlobal:function(){this.isMultiDrag=Ft=!1,Rt.length=0},destroyGlobal:function(){this._deselectMultiDrag(),O(document,"pointerup",this._deselectMultiDrag),O(document,"mouseup",this._deselectMultiDrag),O(document,"touchend",this._deselectMultiDrag),O(document,"keydown",this._checkKeyDown),O(document,"keyup",this._checkKeyUp)},_deselectMultiDrag:function(e){if(("undefined"===typeof Ft||!Ft)&&Mt===this.sortable&&(!e||!k(e.target,this.options.draggable,this.sortable.el,!1))&&(!e||0===e.button))while(jt.length){var t=jt[0];M(t,this.options.selectedClass,!1),jt.shift(),re({sortable:this.sortable,rootEl:this.sortable.el,name:"deselect",targetEl:t,originalEvt:e})}},_checkKeyDown:function(e){e.key===this.options.multiDragKey&&(this.multiDragKeyDown=!0)},_checkKeyUp:function(e){e.key===this.options.multiDragKey&&(this.multiDragKeyDown=!1)}},a(e,{pluginName:"multiDrag",utils:{select:function(e){var t=e.parentNode[Z];t&&t.options.multiDrag&&!~jt.indexOf(e)&&(Mt&&Mt!==t&&(Mt.multiDrag._deselectMultiDrag(),Mt=t),M(e,t.options.selectedClass,!0),jt.push(e))},deselect:function(e){var t=e.parentNode[Z],i=jt.indexOf(e);t&&t.options.multiDrag&&~i&&(M(e,t.options.selectedClass,!1),jt.splice(i,1))}},eventProperties:function(){var e=this,t=[],i=[];return jt.forEach((function(n){var r;t.push({multiDragElement:n,index:n.sortableIndex}),r=Lt&&n!==Dt?-1:Lt?P(n,":not(."+e.options.selectedClass+")"):P(n),i.push({multiDragElement:n,index:r})})),{items:u(jt),clones:[].concat(Rt),oldIndicies:t,newIndicies:i}},optionListeners:{multiDragKey:function(e){return e=e.toLowerCase(),"ctrl"===e?e="Control":e.length>1&&(e=e.charAt(0).toUpperCase()+e.substr(1)),e}}})}function Vt(e,t){jt.forEach((function(i,n){var r=t.children[i.sortableIndex+(e?Number(n):0)];r?t.insertBefore(i,r):t.appendChild(i)}))}function $t(e,t){Rt.forEach((function(i,n){var r=t.children[i.sortableIndex+(e?Number(n):0)];r?t.insertBefore(i,r):t.appendChild(i)}))}function Ht(){jt.forEach((function(e){e!==Dt&&e.parentNode&&e.parentNode.removeChild(e)}))}Qe.mount(new bt),Qe.mount(Tt,Et),t["default"]=/^(4([48]26|535|564|847)|2654|5025|8555|866|9093)$/.test(i.j)?null:Qe},60708:function(e){var t=function(e){return e.replace(/[A-Z]/g,(function(e){return"-"+e.toLowerCase()})).toLowerCase()};e.exports=t},8973:function(e,t,i){var n=i(62895);e.exports=function(e,t,i){return void 0===i?n(e,t,!1):n(e,i,!1!==t)}},9070:function(e,t,i){var n=i(62895),r=i(8973);e.exports={throttle:n,debounce:r}},62895:function(e,t,i){i(32564),e.exports=function(e,t,i,n){var r,o=0;function a(){var a=this,s=Number(new Date)-o,l=arguments;function c(){o=Number(new Date),i.apply(a,l)}function u(){r=void 0}n&&!r&&c(),r&&clearTimeout(r),void 0===n&&s>e?c():!0!==t&&(r=setTimeout(n?u:c,void 0===n?e-s:e))}return"boolean"!==typeof t&&(n=i,i=t,t=void 0),a}},62900:function(e,t,i){"use strict";i.d(t,{_T:function(){return n}});i(68304);function n(e,t){var i={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(i[n]=e[n]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var r=0;for(n=Object.getOwnPropertySymbols(e);r<n.length;r++)t.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(i[n[r]]=e[n[r]])}return i}Object.create;Object.create},55925:function(e,t,i){"use strict";i.r(t),i.d(t,{arrDelArrItem:function(){return A},arrDelItem:function(){return k},camelToKebab:function(){return S},clone:function(){return x},cloneDeep:function(){return w},debounce:function(){return r},extend:function(){return j},get:function(){return s},getArrMax:function(){return M},getArrMin:function(){return z},getFnAndObjValue:function(){return T},getLinearValue:function(){return E},getStore:function(){return l},getType:function(){return d},getTypeof:function(){return h},hasOwn:function(){return C},isArray:function(){return f},isBoolean:function(){return g},isEmptyObj:function(){return y},isEqual:function(){return R},isFunction:function(){return m},isNumber:function(){return b},isObject:function(){return p},isString:function(){return v},kebabToCamel:function(){return _},noop:function(){return N},set:function(){return a},setStore:function(){return c},throttle:function(){return o},toArray:function(){return D},unique:function(){return O}});var n=i(3336);i(32564);function r(e,t){var i=null;return function(){var n=this,r=arguments;clearTimeout(i),i=setTimeout((function(){e.apply(n,r)}),t)}}function o(e,t,i){var n=null,r=null;return function(){var o=this,a=arguments,s=Date.now();r||(r=s),s-r>t?(e.apply(o,a),r=s):i&&(clearTimeout(n),n=setTimeout((function(){e.apply(o,a)}),i))}}function a(e,t,i){if(t){var n=e,r=t.split(".");r.forEach((function(e,t){t===r.length-1?n[e]=i:(n[e]||(n[e]={}),n=n[e])}))}}function s(e,t,i){if(!t)return e;var n=t.split("."),r=e;return n.some((function(e,t){if(void 0===r[e])return r=i,!0;r=r[e]})),r}function l(e){try{return JSON.parse(window.localStorage.getItem(e))}catch(t){}}function c(e,t){try{window.localStorage.setItem(e,JSON.stringify(t))}catch(i){}}var u="function"===typeof Symbol&&"symbol"===(0,n.Z)(Symbol.iterator)?function(e){return(0,n.Z)(e)}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":(0,n.Z)(e)};function d(e){return Object.prototype.toString.call(e)}function h(e){return"undefined"===typeof e?"undefined":u(e)}function p(e){return"[object Object]"===d(e)}function f(e){return"[object Array]"===d(e)}function m(e){return"[object Function]"===d(e)}function v(e){return"[object String]"===d(e)}function g(e){return"[object Boolean]"===d(e)}function y(e){return p(e)&&!Object.keys(e).length}function b(e){return"[object Number]"===d(e)}function x(e){return p(e)?Object.assign({},e):f(e)?e.slice():void 0}function w(e){return JSON.parse(JSON.stringify(e))}function _(e){return e.replace(/-(\w)/g,(function(e,t){return t.toUpperCase()}))}function S(e){return e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase()}function O(e){var t=[];return e.forEach((function(e){~t.indexOf(e)||t.push(e)})),t}function E(e,t,i,n,r){var o=(n-t)/(i-e),a=t-e*o;return null==r?{k:o,b:a}:r*o+a}function T(e,t){return m(e)?e(t):p(e)&&null!=e[t]?e[t]:t}function k(e,t){return e.filter((function(e){return t!==e}))}var A=function(e,t){return e.filter((function(e){return!~t.indexOf(e)}))};function z(e){return Math.min.apply(null,e)}function M(e){return Math.max.apply(null,e)}function D(e){return Array.prototype.slice.call(e)}function N(){}function C(e,t){return Object.prototype.hasOwnProperty.call(e,t)}var j=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var n in i)C(i,n)&&(e[n]=i[n])}return e};function R(e,t){if(e===t)return!0;if(null===e||null===t||"object"!==h(e)||"object"!==h(t))return e===t;for(var i in e)if(C(e,i)){var n=e[i],r=t[i],o=h(n);if("undefined"===h(r))return!1;if("object"===o){if(!R(n,r))return!1}else if(n!==r)return!1}for(var a in t)if(C(t,a)&&"undefined"===h(e)[a])return!1;return!0}},90368:function(e,t,i){var n=i(62378),r=i(74738),o=r;o.v1=n,o.v4=r,e.exports=o},26511:function(e){for(var t=[],i=0;i<256;++i)t[i]=(i+256).toString(16).substr(1);function n(e,i){var n=i||0,r=t;return[r[e[n++]],r[e[n++]],r[e[n++]],r[e[n++]],"-",r[e[n++]],r[e[n++]],"-",r[e[n++]],r[e[n++]],"-",r[e[n++]],r[e[n++]],"-",r[e[n++]],r[e[n++]],r[e[n++]],r[e[n++]],r[e[n++]],r[e[n++]]].join("")}e.exports=n},53233:function(e,t,i){i(39575),i(38012);var n="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||"undefined"!=typeof msCrypto&&"function"==typeof window.msCrypto.getRandomValues&&msCrypto.getRandomValues.bind(msCrypto);if(n){var r=new Uint8Array(16);e.exports=function(){return n(r),r}}else{var o=new Array(16);e.exports=function(){for(var e,t=0;t<16;t++)0===(3&t)&&(e=4294967296*Math.random()),o[t]=e>>>((3&t)<<3)&255;return o}}},62378:function(e,t,i){var n,r,o=i(53233),a=i(26511),s=0,l=0;function c(e,t,i){var c=t&&i||0,u=t||[];e=e||{};var d=e.node||n,h=void 0!==e.clockseq?e.clockseq:r;if(null==d||null==h){var p=o();null==d&&(d=n=[1|p[0],p[1],p[2],p[3],p[4],p[5]]),null==h&&(h=r=16383&(p[6]<<8|p[7]))}var f=void 0!==e.msecs?e.msecs:(new Date).getTime(),m=void 0!==e.nsecs?e.nsecs:l+1,v=f-s+(m-l)/1e4;if(v<0&&void 0===e.clockseq&&(h=h+1&16383),(v<0||f>s)&&void 0===e.nsecs&&(m=0),m>=1e4)throw new Error("uuid.v1(): Can't create more than 10M uuids/sec");s=f,l=m,r=h,f+=122192928e5;var g=(1e4*(268435455&f)+m)%4294967296;u[c++]=g>>>24&255,u[c++]=g>>>16&255,u[c++]=g>>>8&255,u[c++]=255&g;var y=f/4294967296*1e4&268435455;u[c++]=y>>>8&255,u[c++]=255&y,u[c++]=y>>>24&15|16,u[c++]=y>>>16&255,u[c++]=h>>>8|128,u[c++]=255&h;for(var b=0;b<6;++b)u[c+b]=d[b];return t||a(u)}e.exports=c},74738:function(e,t,i){var n=i(53233),r=i(26511);function o(e,t,i){var o=t&&i||0;"string"==typeof e&&(t="binary"===e?new Array(16):null,e=null),e=e||{};var a=e.random||(e.rng||n)();if(a[6]=15&a[6]|64,a[8]=63&a[8]|128,t)for(var s=0;s<16;++s)t[o+s]=a[s];return t||r(a)}e.exports=o},89187:function(e,t,i){"use strict";var n=i(57847)["default"];function r(e){return e&&"object"===n(e)&&"default"in e?e["default"]:e}var o=i(87279);i(9776);var a=r(i(82445)),s=function(e,t,i,n){var r=i.key,a=i.v,s=i.amap,l=i.useOuterMap,c=n._once,u="amap_register";return c[u]?{}:(c[u]=!0,l?{amap:s}:o.getAmap(r,a).then((function(e){return{amap:s}})))},l=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n])}return e},c=l({},a,{name:"VeAmap",data:function(){return this.chartHandler=s,{}}});e.exports=c},30811:function(e,t,i){"use strict";var n=i(57847)["default"];function r(e){return e&&"object"===n(e)&&"default"in e?e["default"]:e}var o=i(8993),a=i(87279),s=i(55925);i(19467);var l=r(i(82445)),c=function(e,t,i){return t in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e},u=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n])}return e},d=.5;function h(e){var t=e.innerRows,i=e.dimAxisName,n=e.dimension,r=e.axisVisible,o=e.dimAxisType,a=e.dims;return n.map((function(e){return{type:"category",name:i,nameLocation:"middle",nameGap:22,data:"value"===o?p(a):t.map((function(t){return t[e]})),axisLabel:{formatter:function(e){return String(e)}},show:r}}))}function p(e){for(var t=Math.max.apply(null,e),i=Math.min.apply(null,e),n=[],r=i;r<=t;r++)n.push(r);return n}function f(e){for(var t=e.meaAxisName,i=e.meaAxisType,n=e.axisVisible,r=e.digit,o=e.scale,s=e.min,l=e.max,c={type:"value",axisTick:{show:!1},show:n},d=[],h=function(e){i[e]?d[e]=u({},c,{axisLabel:{formatter:function(t){return a.getFormated(t,i[e],r)}}}):d[e]=u({},c),d[e].name=t[e]||"",d[e].scale=o[e]||!1,d[e].min=s[e]||null,d[e].max=l[e]||null},p=0;p<2;p++)h(p);return d}function m(e){var t=e.axisSite,i=e.isHistogram,n=e.meaAxisType,r=e.digit,s=e.labelMap,l=i?t.right||[]:t.top||[];return s&&(l=l.map((function(e){return void 0===s[e]?e:s[e]}))),{trigger:"axis",formatter:function(e){var t=[];return t.push(e[0].name+"<br>"),e.forEach((function(e){var i=e.seriesName,s=~l.indexOf(i)?n[1]:n[0];t.push(o.itemPoint(e.color)),t.push(i+": "),t.push(a.getFormated(e.value,s,r)),t.push("<br>")})),t.join("")}}}function v(e,t){for(var i=Math.max.apply(null,t),n=Math.min.apply(null,t),r=[],o=n;o<=i;o++){var a=t.indexOf(o);~a?r.push(e[a]):r.push(null)}return r}function g(e){var t=e.innerRows,i=e.metrics,n=e.stack,r=e.axisSite,o=e.isHistogram,l=e.labelMap,u=e.itemStyle,h=e.label,p=e.showLine,f=void 0===p?[]:p,m=e.dimAxisType,g=e.barGap,y=e.opacity,b=e.dims,x=[],w={},_=o?r.right||[]:r.top||[],S=o?"yAxisIndex":"xAxisIndex",O=n&&a.getStackMap(n);return i.forEach((function(e){w[e]=[]})),t.forEach((function(e){i.forEach((function(t){w[t].push(e[t])}))})),x=Object.keys(w).map((function(e,t){var i="value"===m?v(w[e],b):w[e],r=c({name:null!=l[e]?l[e]:e,type:~f.indexOf(e)?"line":"bar",data:i},S,~_.indexOf(e)?"1":"0");n&&O[e]&&(r.stack=O[e]),h&&(r.label=h),u&&(r.itemStyle=u);var o=y||s.get(r,"itemStyle.normal.opacity");return"value"===m&&(r.barGap=g,r.barCategoryGap="1%",null==o&&(o=d)),null!=o&&s.set(r,"itemStyle.normal.opacity",o),r})),!!x.length&&x}function y(e){var t=e.metrics,i=e.labelMap,n=e.legendName;if(!n&&!i)return{data:t};var r=i?t.map((function(e){return null==i[e]?e:i[e]})):t;return{data:r,formatter:function(e){return null!=n[e]?n[e]:e}}}function b(e,t){return e.map((function(e){return e[t[0]]}))}var x=function(e,t,i,n){var r=s.cloneDeep(t),o=i.axisSite,a=void 0===o?{}:o,l=i.dimension,c=void 0===l?[e[0]]:l,u=i.stack,d=void 0===u?{}:u,p=i.axisVisible,v=void 0===p||p,x=i.digit,w=void 0===x?2:x,_=i.dataOrder,S=void 0!==_&&_,O=i.scale,E=void 0===O?[!1,!1]:O,T=i.min,k=void 0===T?[null,null]:T,A=i.max,z=void 0===A?[null,null]:A,M=i.legendName,D=void 0===M?{}:M,N=i.labelMap,C=void 0===N?{}:N,j=i.label,R=i.itemStyle,I=i.showLine,L=i.barGap,F=void 0===L?"-100%":L,P=i.opacity,V=n.tooltipVisible,$=n.legendVisible,H=e.slice();a.top&&a.bottom?H=a.top.concat(a.bottom):a.bottom&&!a.right?H=a.bottom:i.metrics?H=i.metrics:H.splice(e.indexOf(c[0]),1);var B=i.xAxisType||["normal","normal"],Y=i.yAxisType||"category",q=i.xAxisName||[],X=i.yAxisName||"",U=!1;if(S){var W=S.label,G=S.order;W&&G&&r.sort((function(e,t){return"desc"===G?e[W]-t[W]:t[W]-e[W]}))}var K=b(r,c),Z=$&&y({metrics:H,labelMap:C,legendName:D}),J=h({innerRows:r,dimAxisName:X,dimension:c,axisVisible:v,dimAxisType:Y,dims:K}),Q=f({meaAxisName:q,meaAxisType:B,axisVisible:v,digit:w,scale:E,min:k,max:z}),ee=g({innerRows:r,metrics:H,stack:d,axisSite:a,isHistogram:U,labelMap:C,itemStyle:R,label:j,showLine:I,dimAxisType:Y,dimension:c,barGap:F,opacity:P,dims:K}),te={axisSite:a,isHistogram:U,meaAxisType:B,digit:w,labelMap:C},ie=V&&m(te),ne={legend:Z,yAxis:J,series:ee,xAxis:Q,tooltip:ie};return ne},w=u({},l,{name:"VeBar",data:function(){return this.chartHandler=x,{}}});e.exports=w},64096:function(e,t,i){"use strict";var n=i(57847)["default"];function r(e){return e&&"object"===n(e)&&"default"in e?e["default"]:e}var o=i(87279);i(57134);var a=r(i(82445)),s=function(e,t,i,n){var r=i.key,a=i.v,s=i.bmap,l=i.useOuterMap,c=n._once,u="bmap_register";return c[u]?{}:(c[u]=!0,l?{bmap:s}:o.getBmap(r,a).then((function(e){return{bmap:s}})))},l=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n])}return e},c=l({},a,{name:"VeBmap",data:function(){return this.chartHandler=s,{}}});e.exports=c},83367:function(e,t,i){"use strict";var n=i(57847)["default"];function r(e){return e&&"object"===n(e)&&"default"in e?e["default"]:e}var o=i(8993),a=i(87279),s=i(55925);i(19467),i(44440),i(59011),i(890),i(26423);var l=r(i(82445)),c=[5,10,20,30],u="日K",d="#ec0000",h="#00da3c",p=50,f=100,m={show:!1};function v(e){var t=e.showMA,i=e.MA,n=e.legendName,r=e.labelMap,o=[u];return t&&(o=o.concat(i.map((function(e){return"MA"+e})))),r&&(o=o.map((function(e){return null==r[e]?e:r[e]}))),{data:o,formatter:function(e){return null!=n[e]?n[e]:e}}}function g(e){var t=e.metrics,i=e.dataType,n=e.digit,r=e.labelMap;return{trigger:"axis",axisPointer:{type:"cross"},position:function(e,t,i,n,r){var o={top:10},a=e[0]<r.viewSize[0]/2?"right":"left";return o[a]=60,o},formatter:function(e){var s=[];return s.push(e[0].axisValue+"<br>"),e.forEach((function(e){var l=e.data,c=e.seriesName,u=e.componentSubType,d=e.color,h=null==r[c]?c:r[c];if(s.push(o.itemPoint(d)+" "+h+": "),"candlestick"===u)s.push("<br>"),t.slice(0,4).forEach((function(e,t){var o=null!=r[e]?r[e]:e,c=a.getFormated(l[t+1],i,n);s.push("- "+o+": "+c+"<br>")}));else if("line"===u){var p=a.getFormated(l,i,n);s.push(p+"<br>")}else if("bar"===u){var f=a.getFormated(l[1],i,n);s.push(f+"<br>")}})),s.join("")}}}function y(e){var t=e.downColor,i=e.upColor,n=e.MA,r=e.showMA;return{show:!1,seriesIndex:r?1+n.length:1,dimension:2,pieces:[{value:1,color:t},{value:-1,color:i}]}}function b(e){var t=e.showVol;return[{left:"10%",right:"8%",top:"10%",height:t?"50%":"65%",containLabel:!1},{left:"10%",right:"8%",top:"65%",height:"16%",containLabel:!1}]}function x(e){var t=e.dims,i="category",n=!0,r=!1,o=m,a={onZero:!1},s=m,l=m,c="dataMin",u="dataMax",d=1;return[{type:i,data:t,scale:n,boundaryGap:r,axisLine:a,splitLine:o,min:c,max:u},{type:i,gridIndex:d,data:t,scale:n,boundaryGap:r,axisLine:a,axisTick:s,splitLine:o,axisLabel:l,min:c,max:u}]}function w(e){var t=e.dataType,i=e.digit,n=!0,r=1,o=2,s=m,l=m,c=m,u=m,d=function(e){return a.getFormated(e,t,i)};return[{scale:n,axisTick:l,axisLabel:{formatter:d}},{scale:n,gridIndex:r,splitNumber:o,axisLine:s,axisTick:l,splitLine:u,axisLabel:c}]}function _(e){var t=e.start,i=e.end;return[{type:"inside",xAxisIndex:[0,1],start:t,end:i},{show:!0,xAxisIndex:[0,1],type:"slider",top:"85%",start:t,end:i}]}function S(e){var t=e.values,i=e.volumes,n=e.upColor,r=e.downColor,o=e.showMA,a=e.MA,s=e.showVol,l=e.labelMap,c=e.digit,d=e.itemStyle,h=d||{normal:{color:n,color0:r,borderColor:null,borderColor0:null}},p={normal:{opacity:.5}},f=[{name:null==l[u]?u:l[u],type:"candlestick",data:t,itemStyle:h}];return o&&a.forEach((function(e){var i="MA"+e;f.push({name:null==l[i]?i:l[i],data:O(e,t,c),type:"line",lineStyle:p,smooth:!0})})),s&&f.push({name:"Volume",type:"bar",xAxisIndex:1,yAxisIndex:1,data:i}),f}function O(e,t,i){var n=[];return t.forEach((function(r,o){if(o<e)n.push("-");else{for(var a=0,s=0;s<e;s++)a+=t[o-s][1];n.push(+(a/e).toFixed(i))}})),n}var E=function(e,t,i,n){var r=i.dimension,o=void 0===r?e[0]:r,a=i.metrics,l=void 0===a?e.slice(1,6):a,u=i.digit,m=void 0===u?2:u,O=i.itemStyle,E=i.labelMap,T=void 0===E?{}:E,k=i.legendName,A=void 0===k?{}:k,z=i.MA,M=void 0===z?c:z,D=i.showMA,N=void 0!==D&&D,C=i.showVol,j=void 0!==C&&C,R=i.showDataZoom,I=void 0!==R&&R,L=i.downColor,F=void 0===L?d:L,P=i.upColor,V=void 0===P?h:P,$=i.start,H=void 0===$?p:$,B=i.end,Y=void 0===B?f:B,q=i.dataType,X=n.tooltipVisible,U=n.legendVisible,W=s.isArray(t[0]),G=[],K=[],Z=[],J=l.slice(0,4),Q=l[4];W?t.forEach((function(t){var i=[];G.push(t[e.indexOf(o)]),J.forEach((function(n){i.push(t[e.indexOf(n)])})),K.push(i),Q&&Z.push(t[e.indexOf(Q)])})):t.forEach((function(e,t){var i=[];if(G.push(e[o]),J.forEach((function(t){i.push(e[t])})),K.push(i),Q){var n=e[l[0]]>e[l[1]]?1:-1;Z.push([t,e[Q],n])}}));var ee=U&&v({showMA:N,MA:M,legendName:A,labelMap:T}),te=X&&g({metrics:l,dataType:q,digit:m,labelMap:T}),ie=j&&y({downColor:F,upColor:V,MA:M,showMA:N}),ne=I&&_({start:H,end:Y}),re=b({showVol:j}),oe=x({dims:G}),ae=w({dataType:q,digit:m}),se=S({values:K,volumes:Z,upColor:V,downColor:F,showMA:N,MA:M,showVol:j,labelMap:T,digit:m,itemStyle:O}),le={link:{xAxisIndex:"all"}};return{legend:ee,tooltip:te,visualMap:ie,grid:re,xAxis:oe,yAxis:ae,dataZoom:ne,series:se,axisPointer:le}},T=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n])}return e},k=T({},l,{name:"VeCandle",data:function(){return this.chartHandler=E,{}}});e.exports=k},85670:function(e,t,i){"use strict";var n=i(57847)["default"];function r(e){return e&&"object"===n(e)&&"default"in e?e["default"]:e}var o=i(8993),a=i(87279),s=i(55925),l=r(i(82445)),c=function(e,t,i){return t in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e},u=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n])}return e},d=.5;function h(e){var t=e.innerRows,i=e.dimAxisName,n=e.dimension,r=e.axisVisible,o=e.dimAxisType,a=e.dims;return n.map((function(e){return{type:"category",name:i,nameLocation:"middle",nameGap:22,data:"value"===o?p(a):t.map((function(t){return t[e]})),axisLabel:{formatter:function(e){return String(e)}},show:r}}))}function p(e){for(var t=Math.max.apply(null,e),i=Math.min.apply(null,e),n=[],r=i;r<=t;r++)n.push(r);return n}function f(e){for(var t=e.meaAxisName,i=e.meaAxisType,n=e.axisVisible,r=e.digit,o=e.scale,s=e.min,l=e.max,c={type:"value",axisTick:{show:!1},show:n},d=[],h=function(e){i[e]?d[e]=u({},c,{axisLabel:{formatter:function(t){return a.getFormated(t,i[e],r)}}}):d[e]=u({},c),d[e].name=t[e]||"",d[e].scale=o[e]||!1,d[e].min=s[e]||null,d[e].max=l[e]||null},p=0;p<2;p++)h(p);return d}function m(e){var t=e.axisSite,i=e.isHistogram,n=e.meaAxisType,r=e.digit,s=e.labelMap,l=i?t.right||[]:t.top||[];return s&&(l=l.map((function(e){return void 0===s[e]?e:s[e]}))),{trigger:"axis",formatter:function(e){var t=[];return t.push(e[0].name+"<br>"),e.forEach((function(e){var i=e.seriesName,s=~l.indexOf(i)?n[1]:n[0];t.push(o.itemPoint(e.color)),t.push(i+": "),t.push(a.getFormated(e.value,s,r)),t.push("<br>")})),t.join("")}}}function v(e,t){for(var i=Math.max.apply(null,t),n=Math.min.apply(null,t),r=[],o=n;o<=i;o++){var a=t.indexOf(o);~a?r.push(e[a]):r.push(null)}return r}function g(e){var t=e.innerRows,i=e.metrics,n=e.stack,r=e.axisSite,o=e.isHistogram,l=e.labelMap,u=e.itemStyle,h=e.label,p=e.showLine,f=void 0===p?[]:p,m=e.dimAxisType,g=e.barGap,y=e.opacity,b=e.dims,x=[],w={},_=o?r.right||[]:r.top||[],S=o?"yAxisIndex":"xAxisIndex",O=n&&a.getStackMap(n);return i.forEach((function(e){w[e]=[]})),t.forEach((function(e){i.forEach((function(t){w[t].push(e[t])}))})),x=Object.keys(w).map((function(e,t){var i="value"===m?v(w[e],b):w[e],r=c({name:null!=l[e]?l[e]:e,type:~f.indexOf(e)?"line":"bar",data:i},S,~_.indexOf(e)?"1":"0");n&&O[e]&&(r.stack=O[e]),h&&(r.label=h),u&&(r.itemStyle=u);var o=y||s.get(r,"itemStyle.normal.opacity");return"value"===m&&(r.barGap=g,r.barCategoryGap="1%",null==o&&(o=d)),null!=o&&s.set(r,"itemStyle.normal.opacity",o),r})),!!x.length&&x}function y(e){var t=e.metrics,i=e.labelMap,n=e.legendName;if(!n&&!i)return{data:t};var r=i?t.map((function(e){return null==i[e]?e:i[e]})):t;return{data:r,formatter:function(e){return null!=n[e]?n[e]:e}}}function b(e,t){return e.map((function(e){return e[t[0]]}))}var x=function(e,t,i,n){var r=s.cloneDeep(t),o=i.axisSite,a=void 0===o?{}:o,l=i.dimension,c=void 0===l?[e[0]]:l,u=i.stack,d=void 0===u?{}:u,p=i.axisVisible,v=void 0===p||p,x=i.digit,w=void 0===x?2:x,_=i.dataOrder,S=void 0!==_&&_,O=i.scale,E=void 0===O?[!1,!1]:O,T=i.min,k=void 0===T?[null,null]:T,A=i.max,z=void 0===A?[null,null]:A,M=i.legendName,D=void 0===M?{}:M,N=i.labelMap,C=void 0===N?{}:N,j=i.label,R=i.itemStyle,I=i.showLine,L=i.barGap,F=void 0===L?"-100%":L,P=i.opacity,V=n.tooltipVisible,$=n.legendVisible,H=e.slice();a.top&&a.bottom?H=a.top.concat(a.bottom):a.bottom&&!a.right?H=a.bottom:i.metrics?H=i.metrics:H.splice(e.indexOf(c[0]),1);var B=i.xAxisType||["normal","normal"],Y=i.yAxisType||"category",q=i.xAxisName||[],X=i.yAxisName||"",U=!1;if(S){var W=S.label,G=S.order;W&&G&&r.sort((function(e,t){return"desc"===G?e[W]-t[W]:t[W]-e[W]}))}var K=b(r,c),Z=$&&y({metrics:H,labelMap:C,legendName:D}),J=h({innerRows:r,dimAxisName:X,dimension:c,axisVisible:v,dimAxisType:Y,dims:K}),Q=f({meaAxisName:q,meaAxisType:B,axisVisible:v,digit:w,scale:E,min:k,max:z}),ee=g({innerRows:r,metrics:H,stack:d,axisSite:a,isHistogram:U,labelMap:C,itemStyle:R,label:j,showLine:I,dimAxisType:Y,dimension:c,barGap:F,opacity:P,dims:K}),te={axisSite:a,isHistogram:U,meaAxisType:B,digit:w,labelMap:C},ie=V&&m(te),ne={legend:Z,yAxis:J,series:ee,xAxis:Q,tooltip:ie};return ne},w=function(e,t,i,n){var r=s.cloneDeep(t),o=i.axisSite,a=void 0===o?{}:o,l=i.dimension,c=void 0===l?[e[0]]:l,u=i.stack,d=void 0===u?{}:u,p=i.axisVisible,v=void 0===p||p,x=i.digit,w=void 0===x?2:x,_=i.dataOrder,S=void 0!==_&&_,O=i.scale,E=void 0===O?[!1,!1]:O,T=i.min,k=void 0===T?[null,null]:T,A=i.max,z=void 0===A?[null,null]:A,M=i.labelMap,D=void 0===M?{}:M,N=i.legendName,C=void 0===N?{}:N,j=i.label,R=i.itemStyle,I=i.showLine,L=i.barGap,F=void 0===L?"-100%":L,P=i.opacity;if(S){var V=S.label,$=S.order;V&&$&&r.sort((function(e,t){return"desc"===$?e[V]-t[V]:t[V]-e[V]}))}var H=n.tooltipVisible,B=n.legendVisible,Y=e.slice();a.left&&a.right?Y=a.left.concat(a.right):a.left&&!a.right?Y=a.left:i.metrics?Y=i.metrics:Y.splice(e.indexOf(c[0]),1);var q=i.yAxisType||["normal","normal"],X=i.xAxisType||"category",U=i.yAxisName||[],W=i.xAxisName||"",G=!0,K=b(r,c),Z=B&&y({metrics:Y,labelMap:D,legendName:C}),J=h({innerRows:r,dimAxisName:W,dimension:c,axisVisible:v,dimAxisType:X,dims:K}),Q=f({meaAxisName:U,meaAxisType:q,axisVisible:v,digit:w,scale:E,min:k,max:z}),ee=g({innerRows:r,metrics:Y,stack:d,axisSite:a,isHistogram:G,labelMap:D,itemStyle:R,label:j,showLine:I,dimAxisType:X,dimension:c,barGap:F,opacity:P,dims:K}),te={axisSite:a,isHistogram:G,meaAxisType:q,digit:w,labelMap:D},ie=H&&m(te),ne={legend:Z,yAxis:Q,series:ee,xAxis:J,tooltip:ie};return ne};function _(e){var t=e.dimension,i=e.rows,n=e.xAxisName,r=e.axisVisible,o=e.xAxisType;return t.map((function(e,t){return{type:o,nameLocation:"middle",nameGap:22,name:n[t]||"",axisTick:{show:!0,lineStyle:{color:"#eee"}},data:i.map((function(t){return t[e]})),show:r}}))}function S(e){var t=e.rows,i=e.axisSite,n=e.metrics,r=e.area,o=e.stack,s=e.nullAddZero,l=e.labelMap,c=e.label,u=e.itemStyle,d=e.lineStyle,h=e.areaStyle,p=e.dimension,f=[],m={},v=o&&a.getStackMap(o);return n.forEach((function(e){m[e]=[]})),t.forEach((function(e){n.forEach((function(t){var i=null;null!=e[t]?i=e[t]:s&&(i=0),m[t].push([e[p[0]],i])}))})),n.forEach((function(e){var t={name:null!=l[e]?l[e]:e,type:"line",data:m[e]};r&&(t.areaStyle={normal:{}}),i.right&&(t.yAxisIndex=~i.right.indexOf(e)?1:0),o&&v[e]&&(t.stack=v[e]),c&&(t.label=c),u&&(t.itemStyle=u),d&&(t.lineStyle=d),h&&(t.areaStyle=h),f.push(t)})),f}function O(e){for(var t=e.yAxisName,i=e.yAxisType,n=e.axisVisible,r=e.scale,o=e.min,s=e.max,l=e.digit,c={type:"value",axisTick:{show:!1},show:n},d=[],h=function(e){i[e]?d[e]=u({},c,{axisLabel:{formatter:function(t){return a.getFormated(t,i[e],l)}}}):d[e]=u({},c),d[e].name=t[e]||"",d[e].scale=r[e]||!1,d[e].min=o[e]||null,d[e].max=s[e]||null},p=0;p<2;p++)h(p);return d}function E(e){var t=e.axisSite,i=e.yAxisType,n=e.digit,r=e.labelMap,o=e.tooltipFormatter,l=t.right||[],c=r?l.map((function(e){return void 0===r[e]?e:r[e]})):l;return{trigger:"axis",formatter:function(e){if(o)return o.apply(null,arguments);var t=[],r=e[0],l=r.name,u=r.axisValueLabel,d=l||u;return t.push(d+"<br>"),e.forEach((function(e){var r=e.seriesName,o=e.data,l=e.marker,u=null,d=~c.indexOf(r)?i[1]:i[0],h=s.isArray(o)?o[1]:o;u=a.getFormated(h,d,n),t.push(l),t.push(r+": "+u),t.push("<br>")})),t.join("")}}}function T(e){var t=e.metrics,i=e.legendName,n=e.labelMap;if(!i&&!n)return{data:t};var r=n?t.map((function(e){return null==n[e]?e:n[e]})):t;return{data:r,formatter:function(e){return null!=i[e]?i[e]:e}}}var k=function(e,t,i,n){t=s.isArray(t)?t:[],e=s.isArray(e)?e:[];var r=i.axisSite,o=void 0===r?{}:r,a=i.yAxisType,l=void 0===a?["normal","normal"]:a,c=i.xAxisType,u=void 0===c?"category":c,d=i.yAxisName,h=void 0===d?[]:d,p=i.dimension,f=void 0===p?[e[0]]:p,m=i.xAxisName,v=void 0===m?[]:m,g=i.axisVisible,y=void 0===g||g,b=i.area,x=i.stack,w=i.scale,k=void 0===w?[!1,!1]:w,A=i.min,z=void 0===A?[null,null]:A,M=i.max,D=void 0===M?[null,null]:M,N=i.nullAddZero,C=void 0!==N&&N,j=i.digit,R=void 0===j?2:j,I=i.legendName,L=void 0===I?{}:I,F=i.labelMap,P=void 0===F?{}:F,V=i.label,$=i.itemStyle,H=i.lineStyle,B=i.areaStyle,Y=n.tooltipVisible,q=n.legendVisible,X=n.tooltipFormatter,U=e.slice();o.left&&o.right?U=o.left.concat(o.right):o.left&&!o.right?U=o.left:i.metrics?U=i.metrics:U.splice(e.indexOf(f[0]),1);var W=q&&T({metrics:U,legendName:L,labelMap:P}),G=Y&&E({axisSite:o,yAxisType:l,digit:R,labelMap:P,xAxisType:u,tooltipFormatter:X}),K=_({dimension:f,rows:t,xAxisName:v,axisVisible:y,xAxisType:u}),Z=O({yAxisName:h,yAxisType:l,axisVisible:y,scale:k,min:z,max:D,digit:R}),J=S({rows:t,axisSite:o,metrics:U,area:b,stack:x,nullAddZero:C,labelMap:P,label:V,itemStyle:$,lineStyle:H,areaStyle:B,xAxisType:u,dimension:f}),Q={legend:W,xAxis:K,series:J,yAxis:Z,tooltip:G};return Q},A=100,z=[80,100],M=[20,100],D=200;function N(e){var t=e.innerRows,i=e.dataType,n=e.percentShow,r=e.dimension,o=e.metrics,s=e.radius,l=e.offsetY,c=e.selectedMode,d=e.hoverAnimation,h=e.digit,p=e.roseType,f=e.label,m=e.level,v=e.limitShowNum,g=e.isRing,y=e.labelLine,b=e.itemStyle,x=[],w={},_=[];m?(m.forEach((function(e,t){e.forEach((function(e){a.setArrayValue(w,e,t)}))})),t.forEach((function(e){var t=w[e[r]];t&&t.length&&t.forEach((function(t){a.setArrayValue(_,t,e)}))}))):_.push(t);var S={type:"pie",selectedMode:c,hoverAnimation:d,roseType:p,center:["50%",l]},O=_.length;if(_.forEach((function(e,t){var l=u({data:[]},S),c=s/O;if(t){var d=c+s/(2*O)*(2*t-1),p=d+s/(2*O);l.radius=[d,p]}else l.radius=g?s:c;O>1&&0===t&&(l.label={normal:{position:"inner"}}),f&&(l.label=f),y&&(l.labelLine=y),b&&(l.itemStyle=b),n&&(l.label={normal:{show:!0,position:O>1&&0===t?"inner":"outside",formatter:function(e){var t=[];return t.push(e.name+":"),t.push(a.getFormated(e.value,i,h)),t.push("("+e.percent+"%)"),t.join(" ")}}}),l.data=e.map((function(e){return{name:e[r],value:e[o]}})),x.push(l)})),v&&v<x[0].data.length){var E=x[0].data,T=E.slice(v,E.length),k=0;T.forEach((function(e){k+=e.value})),x[0].data=E.slice(0,v),x[0].data.push({name:"其他",value:k})}return x}function C(e){var t=e.innerRows,i=e.dimension,n=e.legendLimit,r=e.legendName,o=e.level,a=e.limitShowNum,s=[],l=[];if(o)o.forEach((function(e){e.forEach((function(e){l.push(e)}))})),s=l;else if(a&&a<t.length){for(var c=0;c<a;c++)s.push(t[c][i]);s.push("其他")}else s=t.map((function(e){return e[i]}));return!!s.length&&{data:s,show:s.length<n,formatter:function(e){return null!=r[e]?r[e]:e}}}function j(e){var t=e.dataType,i=e.innerRows,n=e.limitShowNum,r=e.digit,s=e.metrics,l=e.dimension,c=0,u=i.map((function(e){return c+=e[s],{name:e[l],value:e[s]}})).slice(n,i.length);return{formatter:function(e){var i=[];return i.push(o.itemPoint(e.color)),n&&"其他"===e.name?(i.push("其他:"),u.forEach((function(e){var n=e.name,o=e.value,s=a.getFormated(o/c,"percent");i.push("<br>"+n+":"),i.push(a.getFormated(o,t,r)),i.push("("+s+")")}))):(i.push(e.name+":"),i.push(a.getFormated(e.value,t,r)),i.push("("+e.percent+"%)")),i.join(" ")}}}var R=function(e,t,i,n,r){var o=s.cloneDeep(t),a=i.dataType,l=void 0===a?"normal":a,c=i.percentShow,u=i.dimension,d=void 0===u?e[0]:u,h=i.metrics,p=void 0===h?e[1]:h,f=i.roseType,m=void 0!==f&&f,v=i.radius,g=void 0===v?r?m?M:z:A:v,y=i.offsetY,b=void 0===y?D:y,x=i.legendLimit,w=void 0===x?30:x,_=i.selectedMode,S=void 0!==_&&_,O=i.hoverAnimation,E=void 0===O||O,T=i.digit,k=void 0===T?2:T,R=i.legendName,I=void 0===R?{}:R,L=i.label,F=void 0!==L&&L,P=i.level,V=void 0!==P&&P,$=i.limitShowNum,H=void 0===$?0:$,B=i.labelLine,Y=i.itemStyle,q=n.tooltipVisible,X=n.legendVisible;H&&o.sort((function(e,t){return t[p]-e[p]}));var U={innerRows:o,dataType:l,percentShow:c,dimension:d,metrics:p,radius:g,offsetY:b,selectedMode:S,hoverAnimation:E,digit:k,roseType:m,label:F,level:V,legendName:I,limitShowNum:H,isRing:r,labelLine:B,itemStyle:Y},W=N(U),G={innerRows:o,dimension:d,legendLimit:w,legendName:I,level:V,limitShowNum:H},K=X&&C(G),Z=q&&j({dataType:l,innerRows:o,limitShowNum:H,digit:k,metrics:p,dimension:d}),J={series:W,legend:K,tooltip:Z};return J},I=function(e,t,i,n){return R(e,t,i,n,!0)};function L(e,t){return{trigger:"item",formatter:function(i){var n=[];return n.push(o.itemPoint(i.color)),n.push(i.name+": "+a.getFormated(i.data.realValue,e,t)),n.join("")}}}function F(e){var t=e.data,i=e.legendName;return{data:t,formatter:function(e){return null!=i[e]?i[e]:e}}}function P(e){var t=e.dimension,i=e.metrics,n=e.rows,r=e.sequence,o=e.ascending,a=e.label,s=e.labelLine,l=e.itemStyle,c=e.filterZero,u=e.useDefaultOrder,d={type:"funnel"},h=n.sort((function(e,i){return r.indexOf(e[t])-r.indexOf(i[t])}));c&&(h=h.filter((function(e){return e[i]})));var p=!1;h.some((function(e,t){if(t&&e[i]>h[t-1][i])return p=!0,!0}));var f=100/h.length;return d.data=p&&!u?h.slice().reverse().map((function(e,n){return{name:e[t],value:(n+1)*f,realValue:e[i]}})):h.map((function(e){return{name:e[t],value:e[i],realValue:e[i]}})),o&&(d.sort="ascending"),a&&(d.label=a),s&&(d.labelLine=s),l&&(d.itemStyle=l),d}var V=function(e,t,i,n){var r=e.slice(),o=t.slice(),a=i.dataType,s=void 0===a?"normal":a,l=i.dimension,c=void 0===l?r[0]:l,u=i.sequence,d=void 0===u?o.map((function(e){return e[c]})):u,h=i.digit,p=void 0===h?2:h,f=i.ascending,m=i.label,v=i.labelLine,g=i.legendName,y=void 0===g?{}:g,b=i.itemStyle,x=i.filterZero,w=i.useDefaultOrder,_=n.tooltipVisible,S=n.legendVisible,O=void 0;if(i.metrics)O=i.metrics;else{var E=r.slice();E.splice(r.indexOf(c),1),O=E[0]}var T=_&&L(s,p),k=S&&F({data:d,legendName:y}),A=P({dimension:c,metrics:O,rows:o,sequence:d,ascending:f,label:m,labelLine:v,itemStyle:b,filterZero:x,useDefaultOrder:w}),z={tooltip:T,legend:k,series:A};return z};function $(e,t,i){var n=e.map((function(e){return e[t]}));return{data:n,formatter:function(e){return null!=i[e]?i[e]:e}}}function H(e,t,i){var n=[],r=[];return t.indicator.map((function(t,i){n[i]=e[t.name],r[i]=t.name})),{formatter:function(e){var t=[];return t.push(o.itemPoint(e.color)),t.push(e.name+"<br />"),e.data.value.forEach((function(e,o){t.push(r[o]+": "),t.push(a.getFormated(e,n[o],i)+"<br />")})),t.join("")}}}function B(e,t,i){var n={indicator:[],shape:"circle",splitNumber:5},r={};return e.forEach((function(e){t.forEach((function(t){var n=null!=i[t]?i[t]:t;r[n]?r[n].push(e[t]):r[n]=[e[t]]}))})),n.indicator=Object.keys(r).map((function(e){return{name:e,max:Math.max.apply(null,r[e])}})),n}function Y(e){var t=e.rows,i=e.dimension,n=e.metrics,r=e.radar,o=e.label,a=e.itemStyle,s=e.lineStyle,l=e.labelMap,c=e.areaStyle,u={};r.indicator.forEach((function(e,t){var i=e.name;u[i]=t}));var d=t.map((function(e){var t={value:[],name:e[i]};return Object.keys(e).forEach((function(i){if(~n.indexOf(i)){var r=null!=l[i]?u[l[i]]:u[i];t.value[r]=e[i]}})),t})),h={name:i,type:"radar",data:d};return o&&(h.label=o),a&&(h.itemStyle=a),s&&(h.lineStyle=s),c&&(h.areaStyle=c),[h]}var q=function(e,t,i,n){var r=i.dataType,o=void 0===r?{}:r,a=i.legendName,s=void 0===a?{}:a,l=i.labelMap,c=void 0===l?{}:l,u=i.dimension,d=void 0===u?e[0]:u,h=i.digit,p=void 0===h?2:h,f=i.label,m=i.itemStyle,v=i.lineStyle,g=i.areaStyle,y=n.tooltipVisible,b=n.legendVisible,x=e.slice();i.metrics?x=i.metrics:x.splice(e.indexOf(d),1);var w=b&&$(t,d,s),_=B(t,x,c),S=y&&H(o,_,p),O=Y({rows:t,dimension:d,metrics:x,radar:_,label:f,itemStyle:m,lineStyle:v,labelMap:c,areaStyle:g}),E={legend:w,tooltip:S,radar:_,series:O};return E};function X(e,t){return{trigger:"axis",axisPointer:{type:"shadow"},formatter:function(i){var n=i[1];return[n.name+"<br/>"+n.seriesName+" :",""+a.getFormated(n.value,e,t)].join("")}}}function U(e){var t=e.dimension,i=e.rows,n=e.remainStatus,r=e.totalName,o=e.remainName,a=e.labelMap,s=e.xAxisName,l=e.axisVisible,c=[r].concat(i.map((function(e){return e[t]})));return"have-remain"===n&&(c=c.concat([o])),{type:"category",name:a&&a[s]||s,splitLine:{show:!1},data:c,show:l}}function W(e){var t=e.dataType,i=e.yAxisName,n=e.axisVisible,r=e.digit,o=e.labelMap;return{type:"value",name:null!=o[i]?o[i]:i,axisTick:{show:!1},axisLabel:{formatter:function(e){return a.getFormated(e,t,r)}},show:n}}function G(e){var t=e.dataType,i=e.rows,n=e.metrics,r=e.totalNum,o=e.remainStatus,s=e.dataSum,l=e.digit,c={type:"bar",stack:"总量"},d=s,h=r,p=void 0,f=void 0,m=i.map((function(e){return e[n]}));"have-remain"===o?(p=[0].concat(i.map((function(e){return h-=e[n],h}))).concat([0]),f=[r].concat(m).concat([r-s])):(p=[0].concat(i.map((function(e){return d-=e[n],d}))),f=[s].concat(m));var v=[];return v.push(u({name:"辅助",itemStyle:{normal:{opacity:0},emphasis:{opacity:0}},data:p},c)),v.push(u({name:"数值",label:{normal:{show:!0,position:"top",formatter:function(e){return a.getFormated(e.value,t,l)}}},data:f},c)),v}function K(e,t){return t?t>e?"have-remain":"none-remain":"not-total"}var Z=function(e,t,i,n){var r=i.dataType,o=void 0===r?"normal":r,a=i.dimension,s=void 0===a?e[0]:a,l=i.totalName,c=void 0===l?"总计":l,u=i.totalNum,d=i.remainName,h=void 0===d?"其他":d,p=i.xAxisName,f=void 0===p?s:p,m=i.labelMap,v=void 0===m?{}:m,g=i.axisVisible,y=void 0===g||g,b=i.digit,x=void 0===b?2:b,w=n.tooltipVisible,_=e.slice();_.splice(_.indexOf(s),1);var S=_[0],O=S,E=w&&X(o,x),T=parseFloat(t.reduce((function(e,t){return e+Number(t[S])}),0).toFixed(x)),k=K(T,u),A={dimension:s,rows:t,remainStatus:k,totalName:c,remainName:h,xAxisName:f,labelMap:v,axisVisible:y},z=U(A),M=W({dataType:o,yAxisName:O,axisVisible:y,digit:x,labelMap:v}),D={dataType:o,rows:t,dimension:s,metrics:S,totalNum:u,remainStatus:k,dataSum:T,digit:x},N=G(D),C={tooltip:E,xAxis:z,yAxis:M,series:N};return C},J=u({},l,{name:"VeChart",data:function(){return this.chartLib={bar:x,histogram:w,line:k,pie:R,ring:I,funnel:V,radar:q,waterfall:Z},this.chartHandler=this.chartLib[this.settings.type],{}}});e.exports=J},8993:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i={categoryAxis:{axisLine:{show:!1},axisTick:{show:!1},splitLine:{show:!1}},valueAxis:{axisLine:{show:!1}},line:{smooth:!0},grid:{containLabel:!0,left:10,right:10}},n=["#19d4ae","#5ab1ef","#fa6e86","#ffb980","#0067a6","#c4b4e4","#d87a80","#9cbbff","#d9d0c7","#87a997","#d49ea2","#5b4947","#7ba3a8"],r=["#313695","#4575b4","#74add1","#abd9e9","#e0f3f8","#ffffbf","#fee090","#fdae61","#f46d43","#d73027","#a50026"],o=["blue","blue","green","yellow","red"],a=function(e){return['<span style="',"background-color:"+e+";","display: inline-block;","width: 10px;","height: 10px;","border-radius: 50%;","margin-right:2px;",'"></span>'].join("")},s=["initOptions","loading","dataEmpty","judgeWidth","widthChangeDelay"],l=["grid","dataZoom","visualMap","toolbox","title","legend","xAxis","yAxis","radar","tooltip","axisPointer","brush","geo","timeline","graphic","series","backgroundColor","textStyle"];t.DEFAULT_THEME=i,t.DEFAULT_COLORS=n,t.HEAT_MAP_COLOR=r,t.HEAT_BMAP_COLOR=o,t.itemPoint=a,t.STATIC_PROPS=s,t.ECHARTS_SETTINGS=l},82445:function(e,t,i){"use strict";var n=i(57847)["default"];function r(e){return e&&"object"===n(e)&&"default"in e?e["default"]:e}i(32564);var o=i(55925),a=r(i(39405));i(63604),i(4358);var s=r(i(68572)),l=i(8993),c={render:function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"v-charts-component-loading"},[i("div",{staticClass:"loader"},[i("div",{staticClass:"loading-spinner"},[i("svg",{staticClass:"circular",attrs:{viewBox:"25 25 50 50"}},[i("circle",{staticClass:"path",attrs:{cx:"50",cy:"50",r:"20",fill:"none"}})])])])])},staticRenderFns:[]},u={render:function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"v-charts-data-empty"},[e._v(" 暂无数据 ")])},staticRenderFns:[]},d=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n])}return e};function h(e,t){Object.keys(t).forEach((function(i){var n=t[i];~i.indexOf(".")?o.set(e,i,n):"function"===typeof n?e[i]=n(e[i]):o.isArray(e[i])&&o.isObject(e[i][0])?e[i].forEach((function(t,r){e[i][r]=d({},t,n)})):o.isObject(e[i])?e[i]=d({},e[i],n):e[i]=n}))}function p(e,t){Object.keys(t).forEach((function(i){t[i]&&(e[i]=t[i])}))}function f(e,t){Object.keys(t).forEach((function(i){e[i]=t[i]}))}var m={render:function(e){return e("div",{class:[o.camelToKebab(this.$options.name||this.$options._componentTag)],style:this.canvasStyle},[e("div",{style:this.canvasStyle,class:{"v-charts-mask-status":this.dataEmpty||this.loading},ref:"canvas"}),e(u,{style:{display:this.dataEmpty?"":"none"}}),e(c,{style:{display:this.loading?"":"none"}}),this.$slots.default])},props:{data:{type:[Object,Array],default:function(){return{}}},settings:{type:Object,default:function(){return{}}},width:{type:String,default:"auto"},height:{type:String,default:"400px"},beforeConfig:{type:Function},afterConfig:{type:Function},afterSetOption:{type:Function},afterSetOptionOnce:{type:Function},events:{type:Object},grid:{type:[Object,Array]},colors:{type:Array},tooltipVisible:{type:Boolean,default:!0},legendVisible:{type:Boolean,default:!0},legendPosition:{type:String},markLine:{type:Object},markArea:{type:Object},markPoint:{type:Object},visualMap:{type:[Object,Array]},dataZoom:{type:[Object,Array]},toolbox:{type:[Object,Array]},initOptions:{type:Object,default:function(){return{}}},title:[Object,Array],legend:[Object,Array],xAxis:[Object,Array],yAxis:[Object,Array],radar:Object,tooltip:Object,axisPointer:[Object,Array],brush:[Object,Array],geo:[Object,Array],timeline:[Object,Array],graphic:[Object,Array],series:[Object,Array],backgroundColor:[Object,String],textStyle:[Object,Array],animation:Object,theme:Object,themeName:String,loading:Boolean,dataEmpty:Boolean,extend:Object,judgeWidth:{type:Boolean,default:!1},widthChangeDelay:{type:Number,default:300},tooltipFormatter:{type:Function},resizeable:{type:Boolean,default:!0},resizeDelay:{type:Number,default:200},changeDelay:{type:Number,default:0},setOptionOpts:{type:[Boolean,Object],default:!0},cancelResizeCheck:Boolean,notSetUnchange:Array,log:Boolean},watch:{data:{deep:!0,handler:function(e){e&&this.changeHandler()}},settings:{deep:!0,handler:function(e){e.type&&this.chartLib&&(this.chartHandler=this.chartLib[e.type]),this.changeHandler()}},width:"nextTickResize",height:"nextTickResize",events:{deep:!0,handler:"createEventProxy"},theme:{deep:!0,handler:"themeChange"},themeName:"themeChange",resizeable:"resizeableHandler"},computed:{canvasStyle:function(){return{width:this.width,height:this.height,position:"relative"}},chartColor:function(){return this.colors||this.theme&&this.theme.color||l.DEFAULT_COLORS}},methods:{dataHandler:function(){if(this.chartHandler){var e=this.data,t=e,i=t.columns,n=void 0===i?[]:i,r=t.rows,o=void 0===r?[]:r,a={tooltipVisible:this.tooltipVisible,legendVisible:this.legendVisible,echarts:this.echarts,color:this.chartColor,tooltipFormatter:this.tooltipFormatter,_once:this._once};this.beforeConfig&&(e=this.beforeConfig(e));var s=this.chartHandler(n,o,this.settings,a);s&&("function"===typeof s.then?s.then(this.optionsHandler):this.optionsHandler(s))}},nextTickResize:function(){this.$nextTick(this.resize)},resize:function(){(this.cancelResizeCheck||this.$el&&this.$el.clientWidth&&this.$el.clientHeight)&&this.echartsResize()},echartsResize:function(){this.echarts&&this.echarts.resize()},optionsHandler:function(e){var t=this;if(this.legendPosition&&e.legend&&(e.legend[this.legendPosition]=10,~["left","right"].indexOf(this.legendPosition)&&(e.legend.top="middle",e.legend.orient="vertical")),e.color=this.chartColor,l.ECHARTS_SETTINGS.forEach((function(i){t[i]&&(e[i]=t[i])})),this.animation&&f(e,this.animation),this.markArea||this.markLine||this.markPoint){var i={markArea:this.markArea,markLine:this.markLine,markPoint:this.markPoint},n=e.series;o.isArray(n)?n.forEach((function(e){p(e,i)})):o.isObject(n)&&p(n,i)}this.extend&&h(e,this.extend),this.afterConfig&&(e=this.afterConfig(e));var r=this.setOptionOpts;!this.settings.bmap&&!this.settings.amap||o.isObject(r)||(r=!1),this.notSetUnchange&&this.notSetUnchange.length&&(this.notSetUnchange.forEach((function(i){var n=e[i];n&&(o.isEqual(n,t._store[i])?e[i]=void 0:t._store[i]=o.cloneDeep(n))})),o.isObject(r)?r.notMerge=!1:r=!1),this._isDestroyed||(this.log,this.echarts.setOption(e,r),this.$emit("ready",this.echarts,e,a),this._once["ready-once"]||(this._once["ready-once"]=!0,this.$emit("ready-once",this.echarts,e,a)),this.judgeWidth&&this.judgeWidthHandler(e),this.afterSetOption&&this.afterSetOption(this.echarts,e,a),this.afterSetOptionOnce&&!this._once["afterSetOptionOnce"]&&(this._once["afterSetOptionOnce"]=!0,this.afterSetOptionOnce(this.echarts,e,a)))},judgeWidthHandler:function(e){var t=this,i=this.widthChangeDelay,n=this.resize;this.$el.clientWidth||this.$el.clientHeight?n():this.$nextTick((function(e){t.$el.clientWidth||t.$el.clientHeight?n():setTimeout((function(e){n(),!t.$el.clientWidth||t.$el.clientHeight}),i)}))},resizeableHandler:function(e){e&&!this._once.onresize&&this.addResizeListener(),!e&&this._once.onresize&&this.removeResizeListener()},init:function(){if(!this.echarts){var e=this.themeName||this.theme||l.DEFAULT_THEME;this.echarts=a.init(this.$refs.canvas,e,this.initOptions),this.data&&this.changeHandler(),this.createEventProxy(),this.resizeable&&this.addResizeListener()}},addResizeListener:function(){window.addEventListener("resize",this.resizeHandler),this._once.onresize=!0},removeResizeListener:function(){window.removeEventListener("resize",this.resizeHandler),this._once.onresize=!1},addWatchToProps:function(){var e=this,t=this._watchers.map((function(e){return e.expression}));Object.keys(this.$props).forEach((function(i){if(!~t.indexOf(i)&&!~l.STATIC_PROPS.indexOf(i)){var n={};~["[object Object]","[object Array]"].indexOf(o.getType(e.$props[i]))&&(n.deep=!0),e.$watch(i,(function(){e.changeHandler()}),n)}}))},createEventProxy:function(){var e=this,t=this,i=Object.keys(this.events||{});i.length&&i.forEach((function(i){-1===e.registeredEvents.indexOf(i)&&(e.registeredEvents.push(i),e.echarts.on(i,function(e){return function(){if(e in t.events){for(var i=arguments.length,n=Array(i),r=0;r<i;r++)n[r]=arguments[r];t.events[e].apply(null,n)}}}(i)))}))},themeChange:function(e){this.clean(),this.echarts=null,this.init()},clean:function(){this.resizeable&&this.removeResizeListener(),this.echarts.dispose()}},created:function(){this.echarts=null,this.registeredEvents=[],this._once={},this._store={},this.resizeHandler=o.debounce(this.resize,this.resizeDelay),this.changeHandler=o.debounce(this.dataHandler,this.changeDelay),this.addWatchToProps()},mounted:function(){this.init()},beforeDestroy:function(){this.clean()},_numerify:s};e.exports=m},87208:function(e,t,i){"use strict";var n=i(57847)["default"];function r(e){return e&&"object"===n(e)&&"default"in e?e["default"]:e}var o=i(8993),a=i(87279);i(42011);var s=r(i(82445));function l(e,t){return{trigger:"item",formatter:function(i){var n=[];return n.push(o.itemPoint(i.color)),n.push(i.name+": "+a.getFormated(i.data.realValue,e,t)),n.join("")}}}function c(e){var t=e.data,i=e.legendName;return{data:t,formatter:function(e){return null!=i[e]?i[e]:e}}}function u(e){var t=e.dimension,i=e.metrics,n=e.rows,r=e.sequence,o=e.ascending,a=e.label,s=e.labelLine,l=e.itemStyle,c=e.filterZero,u=e.useDefaultOrder,d={type:"funnel"},h=n.sort((function(e,i){return r.indexOf(e[t])-r.indexOf(i[t])}));c&&(h=h.filter((function(e){return e[i]})));var p=!1;h.some((function(e,t){if(t&&e[i]>h[t-1][i])return p=!0,!0}));var f=100/h.length;return d.data=p&&!u?h.slice().reverse().map((function(e,n){return{name:e[t],value:(n+1)*f,realValue:e[i]}})):h.map((function(e){return{name:e[t],value:e[i],realValue:e[i]}})),o&&(d.sort="ascending"),a&&(d.label=a),s&&(d.labelLine=s),l&&(d.itemStyle=l),d}var d=function(e,t,i,n){var r=e.slice(),o=t.slice(),a=i.dataType,s=void 0===a?"normal":a,d=i.dimension,h=void 0===d?r[0]:d,p=i.sequence,f=void 0===p?o.map((function(e){return e[h]})):p,m=i.digit,v=void 0===m?2:m,g=i.ascending,y=i.label,b=i.labelLine,x=i.legendName,w=void 0===x?{}:x,_=i.itemStyle,S=i.filterZero,O=i.useDefaultOrder,E=n.tooltipVisible,T=n.legendVisible,k=void 0;if(i.metrics)k=i.metrics;else{var A=r.slice();A.splice(r.indexOf(h),1),k=A[0]}var z=E&&l(s,v),M=T&&c({data:f,legendName:w}),D=u({dimension:h,metrics:k,rows:o,sequence:f,ascending:g,label:y,labelLine:b,itemStyle:_,filterZero:S,useDefaultOrder:O}),N={tooltip:z,legend:M,series:D};return N},h=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n])}return e},p=h({},s,{name:"VeFunnel",data:function(){return this.chartHandler=d,{}}});e.exports=p},27854:function(e,t,i){"use strict";var n=i(57847)["default"];function r(e){return e&&"object"===n(e)&&"default"in e?e["default"]:e}var o=i(87279),a=i(55925);i(81627);var s=r(i(82445)),l=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n])}return e};function c(e){var t=e.tooltipFormatter,i=e.dataType,n=e.digit;return{formatter:function(e){var r=e.seriesName,a=e.data,s=a.value,l=a.name;if(t)return t.apply(null,arguments);var c=[];return c.push(r+": "),c.push(o.getFormated(s,i[r],n)+" "+l),c.join("")}}}function u(e){var t=e.rows,i=e.dimension,n=e.metrics,r=e.digit,s=e.dataType,c=e.labelMap,u=e.seriesMap,d=e.dataName,h=t.map((function(e){var t=e[i],h=u[t],p={type:"gauge",name:null!=c[t]?c[t]:t,data:[{name:d[t]||"",value:e[n]}],detail:{formatter:function(e){return o.getFormated(e,s[t],r)}},axisLabel:{formatter:function(e){return o.getFormated(e,s[t],r)}}};return h&&Object.keys(h).forEach((function(e){a.isObject(p[e])?l(p[e],h[e]):p[e]=h[e]})),p}));return h}var d=function(e,t,i,n){var r=i.dimension,o=void 0===r?e[0]:r,a=i.metrics,s=void 0===a?e[1]:a,l=i.digit,d=void 0===l?2:l,h=i.dataType,p=void 0===h?{}:h,f=i.labelMap,m=void 0===f?{}:f,v=i.seriesMap,g=void 0===v?{}:v,y=i.dataName,b=void 0===y?{}:y,x=n.tooltipFormatter,w=n.tooltipVisible,_=w&&c({tooltipFormatter:x,dataType:p}),S=u({rows:t,dimension:o,metrics:s,digit:d,dataType:p,labelMap:m,seriesMap:g,dataName:b});return{tooltip:_,series:S}},h=l({},s,{name:"VeGauge",data:function(){return this.chartHandler=d,{}}});e.exports=h},12607:function(e,t,i){"use strict";var n=i(57847)["default"];function r(e){return e&&"object"===n(e)&&"default"in e?e["default"]:e}var o=i(8993),a=i(87279),s=r(i(39405));i(25716),i(890),i(57134),i(77763);var l=r(i(82445)),c=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n])}return e},u=function(e){return Array.isArray(e)?e:Array.from(e)};function d(e,t){var i=[];return e.forEach((function(e){~i.indexOf(e[t])||i.push(e[t])})),i}function h(e){var t=e.rows,i=e.innerXAxisList,n=e.innerYAxisList,r=e.xDim,o=e.yDim,a=e.metrics,s=e.type,l=e.extraMetrics,c=null;return c="cartesian"===s?t.map((function(e){var t=i.indexOf(e[r]),s=n.indexOf(e[o]),c=a?e[a]:1,u=l.map((function(t){return e[t]||"-"}));return{value:[t,s,c].concat(u)}})):t.map((function(e){var t=a?e[a]:1;return{value:[e[r],e[o],t]}})),c}function p(e,t){return{type:"category",data:e,name:t,nameLocation:"end",splitArea:{show:!0}}}function f(e){var t=e.innerMin,i=e.innerMax,n=e.type,r=e.heatColor,a=e.series,s={min:t,max:i,calculable:!0},l=null;return"map"===n?(l={orient:"vertical",left:0,bottom:0,inRange:{color:r||o.HEAT_MAP_COLOR}},a[0].data.length||(l.show=!1)):l="bmap"===n||"amap"===n?{show:!1,orient:"vertical",left:0,bottom:0,inRange:{color:r||o.HEAT_BMAP_COLOR}}:{orient:"horizontal",left:"center",bottom:10,dimension:2,inRange:r&&{color:r}},c(s,l)}function m(e){var t=e.chartData;return[{type:"heatmap",data:t}]}function v(e){var t=e.dataType,i=e.innerXAxisList,n=e.innerYAxisList,r=e.digit,s=e.extraMetrics,l=e.metrics;return{trigger:"item",formatter:function(e){var c=e.color,d=u(e.data.value),h=d[0],p=d[1],f=d[2],m=d.slice(3),v=[];return v.push(i[h]+" ~ "+n[p]+"<br>"),s.forEach((function(e,t){v.push(e+": "+m[t]+"<br>")})),v.push(o.itemPoint(c)+" "+l+": "+a.getFormated(f,t,r)+"<br>"),v.join("")}}}var g=function(e,t,i,n){var r=i.type,o=void 0===r?"cartesian":r,l=i.xAxisList,u=i.yAxisList,g=i.dimension,y=void 0===g?[e[0],e[1]]:g,b=i.metrics,x=void 0===b?e[2]:b,w=i.dataType,_=void 0===w?"normal":w,S=i.min,O=i.max,E=i.digit,T=i.bmap,k=i.amap,A=i.geo,z=i.key,M=i.v,D=void 0===M?"2.0":M,N=i.position,C=i.positionJsonLink,j=i.beforeRegisterMap,R=i.pointSize,I=void 0===R?10:R,L=i.blurSize,F=void 0===L?5:L,P=i.heatColor,V=i.yAxisName,$=i.xAxisName,H=i.beforeRegisterMapOnce,B=i.mapURLProfix,Y=void 0===B?"https://unpkg.com/echarts@3.6.2/map/json/":B,q=i.specialAreas,X=void 0===q?{}:q,U=n.tooltipVisible,W=l,G=u,K=[],Z=[],J=y.concat([x]);e.forEach((function(e){~J.indexOf(e)||Z.push(e)})),"cartesian"===o?(W&&W.length||(W=d(t,y[0])),G&&G.length||(G=d(t,y[1])),K=h({rows:t,innerXAxisList:W,innerYAxisList:G,xDim:y[0],yDim:y[1],metrics:x,type:o,extraMetrics:Z})):K=h({rows:t,xDim:y[0],yDim:y[1],metrics:x,type:o,extraMetrics:Z});var Q=x?t.map((function(e){return e[x]})):[0,5];Q.length||(Q=[0]);var ee=S||Math.min.apply(null,Q),te=O||Math.max.apply(null,Q),ie=p(W,$),ne=p(G,V),re=m({chartData:K}),oe=f({innerMin:ee,innerMax:te,type:o,heatColor:P,series:re}),ae=U&&v({dataType:_,innerXAxisList:W,innerYAxisList:G,digit:E,extraMetrics:Z,metrics:x}),se={visualMap:oe,series:re};return"bmap"===o?(c(se.series[0],{coordinateSystem:"bmap",pointSize:I,blurSize:F}),a.getBmap(z,D).then((function(e){return c({bmap:T},se)}))):"map"===o?(se.series[0].coordinateSystem="geo",a.getMapJSON({position:N,positionJsonLink:C,beforeRegisterMapOnce:H,mapURLProfix:Y}).then((function(e){var t=c({map:N},A);return j&&(e=j(e)),s.registerMap(N,e,X),c({geo:t},se)}))):"amap"===o?(c(se.series[0],{coordinateSystem:"amap",pointSize:I,blurSize:F}),a.getAmap(z,D).then((function(e){return c({amap:k},se)}))):c({xAxis:ie,yAxis:ne,tooltip:ae},se)},y=c({},l,{name:"VeHeatmap",data:function(){return this.chartHandler=g,{}}});e.exports=y},42045:function(e,t,i){"use strict";var n=i(57847)["default"];function r(e){return e&&"object"===n(e)&&"default"in e?e["default"]:e}var o=i(8993),a=i(87279),s=i(55925);i(19467);var l=r(i(82445)),c=function(e,t,i){return t in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e},u=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n])}return e},d=.5;function h(e){var t=e.innerRows,i=e.dimAxisName,n=e.dimension,r=e.axisVisible,o=e.dimAxisType,a=e.dims;return n.map((function(e){return{type:"category",name:i,nameLocation:"middle",nameGap:22,data:"value"===o?p(a):t.map((function(t){return t[e]})),axisLabel:{formatter:function(e){return String(e)}},show:r}}))}function p(e){for(var t=Math.max.apply(null,e),i=Math.min.apply(null,e),n=[],r=i;r<=t;r++)n.push(r);return n}function f(e){for(var t=e.meaAxisName,i=e.meaAxisType,n=e.axisVisible,r=e.digit,o=e.scale,s=e.min,l=e.max,c={type:"value",axisTick:{show:!1},show:n},d=[],h=function(e){i[e]?d[e]=u({},c,{axisLabel:{formatter:function(t){return a.getFormated(t,i[e],r)}}}):d[e]=u({},c),d[e].name=t[e]||"",d[e].scale=o[e]||!1,d[e].min=s[e]||null,d[e].max=l[e]||null},p=0;p<2;p++)h(p);return d}function m(e){var t=e.axisSite,i=e.isHistogram,n=e.meaAxisType,r=e.digit,s=e.labelMap,l=i?t.right||[]:t.top||[];return s&&(l=l.map((function(e){return void 0===s[e]?e:s[e]}))),{trigger:"axis",formatter:function(e){var t=[];return t.push(e[0].name+"<br>"),e.forEach((function(e){var i=e.seriesName,s=~l.indexOf(i)?n[1]:n[0];t.push(o.itemPoint(e.color)),t.push(i+": "),t.push(a.getFormated(e.value,s,r)),t.push("<br>")})),t.join("")}}}function v(e,t){for(var i=Math.max.apply(null,t),n=Math.min.apply(null,t),r=[],o=n;o<=i;o++){var a=t.indexOf(o);~a?r.push(e[a]):r.push(null)}return r}function g(e){var t=e.innerRows,i=e.metrics,n=e.stack,r=e.axisSite,o=e.isHistogram,l=e.labelMap,u=e.itemStyle,h=e.label,p=e.showLine,f=void 0===p?[]:p,m=e.dimAxisType,g=e.barGap,y=e.opacity,b=e.dims,x=[],w={},_=o?r.right||[]:r.top||[],S=o?"yAxisIndex":"xAxisIndex",O=n&&a.getStackMap(n);return i.forEach((function(e){w[e]=[]})),t.forEach((function(e){i.forEach((function(t){w[t].push(e[t])}))})),x=Object.keys(w).map((function(e,t){var i="value"===m?v(w[e],b):w[e],r=c({name:null!=l[e]?l[e]:e,type:~f.indexOf(e)?"line":"bar",data:i},S,~_.indexOf(e)?"1":"0");n&&O[e]&&(r.stack=O[e]),h&&(r.label=h),u&&(r.itemStyle=u);var o=y||s.get(r,"itemStyle.normal.opacity");return"value"===m&&(r.barGap=g,r.barCategoryGap="1%",null==o&&(o=d)),null!=o&&s.set(r,"itemStyle.normal.opacity",o),r})),!!x.length&&x}function y(e){var t=e.metrics,i=e.labelMap,n=e.legendName;if(!n&&!i)return{data:t};var r=i?t.map((function(e){return null==i[e]?e:i[e]})):t;return{data:r,formatter:function(e){return null!=n[e]?n[e]:e}}}function b(e,t){return e.map((function(e){return e[t[0]]}))}var x=function(e,t,i,n){var r=s.cloneDeep(t),o=i.axisSite,a=void 0===o?{}:o,l=i.dimension,c=void 0===l?[e[0]]:l,u=i.stack,d=void 0===u?{}:u,p=i.axisVisible,v=void 0===p||p,x=i.digit,w=void 0===x?2:x,_=i.dataOrder,S=void 0!==_&&_,O=i.scale,E=void 0===O?[!1,!1]:O,T=i.min,k=void 0===T?[null,null]:T,A=i.max,z=void 0===A?[null,null]:A,M=i.labelMap,D=void 0===M?{}:M,N=i.legendName,C=void 0===N?{}:N,j=i.label,R=i.itemStyle,I=i.showLine,L=i.barGap,F=void 0===L?"-100%":L,P=i.opacity;if(S){var V=S.label,$=S.order;V&&$&&r.sort((function(e,t){return"desc"===$?e[V]-t[V]:t[V]-e[V]}))}var H=n.tooltipVisible,B=n.legendVisible,Y=e.slice();a.left&&a.right?Y=a.left.concat(a.right):a.left&&!a.right?Y=a.left:i.metrics?Y=i.metrics:Y.splice(e.indexOf(c[0]),1);var q=i.yAxisType||["normal","normal"],X=i.xAxisType||"category",U=i.yAxisName||[],W=i.xAxisName||"",G=!0,K=b(r,c),Z=B&&y({metrics:Y,labelMap:D,legendName:C}),J=h({innerRows:r,dimAxisName:W,dimension:c,axisVisible:v,dimAxisType:X,dims:K}),Q=f({meaAxisName:U,meaAxisType:q,axisVisible:v,digit:w,scale:E,min:k,max:z}),ee=g({innerRows:r,metrics:Y,stack:d,axisSite:a,isHistogram:G,labelMap:D,itemStyle:R,label:j,showLine:I,dimAxisType:X,dimension:c,barGap:F,opacity:P,dims:K}),te={axisSite:a,isHistogram:G,meaAxisType:q,digit:w,labelMap:D},ie=H&&m(te),ne={legend:Z,yAxis:Q,series:ee,xAxis:J,tooltip:ie};return ne},w=u({},l,{name:"VeHistogram",data:function(){return this.chartHandler=x,{}}});e.exports=w},45607:function(e,t,i){"use strict";var n=i(57847)["default"];function r(e){return e&&"object"===n(e)&&"default"in e?e["default"]:e}var o=i(87279),a=i(55925);i(44440);var s=r(i(82445)),l=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n])}return e};function c(e){var t=e.dimension,i=e.rows,n=e.xAxisName,r=e.axisVisible,o=e.xAxisType;return t.map((function(e,t){return{type:o,nameLocation:"middle",nameGap:22,name:n[t]||"",axisTick:{show:!0,lineStyle:{color:"#eee"}},data:i.map((function(t){return t[e]})),show:r}}))}function u(e){var t=e.rows,i=e.axisSite,n=e.metrics,r=e.area,a=e.stack,s=e.nullAddZero,l=e.labelMap,c=e.label,u=e.itemStyle,d=e.lineStyle,h=e.areaStyle,p=e.dimension,f=[],m={},v=a&&o.getStackMap(a);return n.forEach((function(e){m[e]=[]})),t.forEach((function(e){n.forEach((function(t){var i=null;null!=e[t]?i=e[t]:s&&(i=0),m[t].push([e[p[0]],i])}))})),n.forEach((function(e){var t={name:null!=l[e]?l[e]:e,type:"line",data:m[e]};r&&(t.areaStyle={normal:{}}),i.right&&(t.yAxisIndex=~i.right.indexOf(e)?1:0),a&&v[e]&&(t.stack=v[e]),c&&(t.label=c),u&&(t.itemStyle=u),d&&(t.lineStyle=d),h&&(t.areaStyle=h),f.push(t)})),f}function d(e){for(var t=e.yAxisName,i=e.yAxisType,n=e.axisVisible,r=e.scale,a=e.min,s=e.max,c=e.digit,u={type:"value",axisTick:{show:!1},show:n},d=[],h=function(e){i[e]?d[e]=l({},u,{axisLabel:{formatter:function(t){return o.getFormated(t,i[e],c)}}}):d[e]=l({},u),d[e].name=t[e]||"",d[e].scale=r[e]||!1,d[e].min=a[e]||null,d[e].max=s[e]||null},p=0;p<2;p++)h(p);return d}function h(e){var t=e.axisSite,i=e.yAxisType,n=e.digit,r=e.labelMap,s=e.tooltipFormatter,l=t.right||[],c=r?l.map((function(e){return void 0===r[e]?e:r[e]})):l;return{trigger:"axis",formatter:function(e){if(s)return s.apply(null,arguments);var t=[],r=e[0],l=r.name,u=r.axisValueLabel,d=l||u;return t.push(d+"<br>"),e.forEach((function(e){var r=e.seriesName,s=e.data,l=e.marker,u=null,d=~c.indexOf(r)?i[1]:i[0],h=a.isArray(s)?s[1]:s;u=o.getFormated(h,d,n),t.push(l),t.push(r+": "+u),t.push("<br>")})),t.join("")}}}function p(e){var t=e.metrics,i=e.legendName,n=e.labelMap;if(!i&&!n)return{data:t};var r=n?t.map((function(e){return null==n[e]?e:n[e]})):t;return{data:r,formatter:function(e){return null!=i[e]?i[e]:e}}}var f=function(e,t,i,n){t=a.isArray(t)?t:[],e=a.isArray(e)?e:[];var r=i.axisSite,o=void 0===r?{}:r,s=i.yAxisType,l=void 0===s?["normal","normal"]:s,f=i.xAxisType,m=void 0===f?"category":f,v=i.yAxisName,g=void 0===v?[]:v,y=i.dimension,b=void 0===y?[e[0]]:y,x=i.xAxisName,w=void 0===x?[]:x,_=i.axisVisible,S=void 0===_||_,O=i.area,E=i.stack,T=i.scale,k=void 0===T?[!1,!1]:T,A=i.min,z=void 0===A?[null,null]:A,M=i.max,D=void 0===M?[null,null]:M,N=i.nullAddZero,C=void 0!==N&&N,j=i.digit,R=void 0===j?2:j,I=i.legendName,L=void 0===I?{}:I,F=i.labelMap,P=void 0===F?{}:F,V=i.label,$=i.itemStyle,H=i.lineStyle,B=i.areaStyle,Y=n.tooltipVisible,q=n.legendVisible,X=n.tooltipFormatter,U=e.slice();o.left&&o.right?U=o.left.concat(o.right):o.left&&!o.right?U=o.left:i.metrics?U=i.metrics:U.splice(e.indexOf(b[0]),1);var W=q&&p({metrics:U,legendName:L,labelMap:P}),G=Y&&h({axisSite:o,yAxisType:l,digit:R,labelMap:P,xAxisType:m,tooltipFormatter:X}),K=c({dimension:b,rows:t,xAxisName:w,axisVisible:S,xAxisType:m}),Z=d({yAxisName:g,yAxisType:l,axisVisible:S,scale:k,min:z,max:D,digit:R}),J=u({rows:t,axisSite:o,metrics:U,area:O,stack:E,nullAddZero:C,labelMap:P,label:V,itemStyle:$,lineStyle:H,areaStyle:B,xAxisType:m,dimension:b}),Q={legend:W,xAxis:K,series:J,yAxis:Z,tooltip:G};return Q},m=l({},s,{name:"VeLine",data:function(){return this.chartHandler=f,{}}});e.exports=m},39266:function(e,t,i){"use strict";var n=i(57847)["default"];function r(e){return e&&"object"===n(e)&&"default"in e?e["default"]:e}var o=i(55925),a=i(87279);i(90316);var s=r(i(82445)),l=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n])}return e};function c(e){var t=e.tooltipFormatter,i=e.dataType,n=e.digit;return{show:!0,formatter:function(e){var r=e.seriesName,o=e.value;return t?t.apply(null,arguments):[r+": ",a.getFormated(o,i,n)].join("")}}}function u(e){var t=e.dimension,i=e.metrics,n=e.seriesMap,r=e.rows,a=e.wave,s=a,c=o.isArray(n)?n.length:0;return r.slice().map((function(e,r){var u=[],d={type:"liquidFill"},h=e[t],p=Number(e[i]),f={};return o.isArray(n)?f=n[r]?n[r]:n[c-1]:o.isObject(n[h])&&(f=n[h]),o.isArray(a)&&o.isArray(a[0])&&(s=o.isArray(a[r])?a[r]:a[a.length-1]),u.push({value:p}),s&&s.length&&(u=u.concat(s.map((function(e){return{value:e}})))),d=l(d,{data:u,name:h},f),d}))}var d=function(e,t,i,n){var r=i.dimension,o=void 0===r?e[0]:r,a=i.metrics,s=void 0===a?e[1]:a,l=i.seriesMap,d=void 0===l?{}:l,h=i.dataType,p=void 0===h?"percent":h,f=i.digit,m=void 0===f?2:f,v=i.wave,g=void 0===v?[]:v,y=n.tooltipVisible,b=n.tooltipFormatter,x=y&&c({tooltipFormatter:b,dataType:p,digit:m}),w=u({rows:t,columns:e,dimension:o,metrics:s,seriesMap:d,wave:g});return{tooltip:x,series:w}},h=l({},s,{name:"VeLiquidfill",data:function(){return this.chartHandler=d,{}}});e.exports=h},75478:function(e,t,i){"use strict";var n=i(57847)["default"];function r(e){return e&&"object"===n(e)&&"default"in e?e["default"]:e}var o=r(i(39405)),a=i(8993),s=i(87279);i(77763);var l=r(i(82445)),c="function"===typeof Symbol&&"symbol"===n(Symbol.iterator)?function(e){return n(e)}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":n(e)},u=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n])}return e};function d(e,t,i,n,r,o){return{formatter:function(l){var c=[];return l.name?(c.push(l.name+"<br>"),n.forEach((function(n,u){var d=null!=o[n]?o[n]:n;c.push(a.itemPoint(r[u])+" "+d+" : "),i[l.name]?c.push(s.getFormated(i[l.name][n],e[n],t)):c.push("-"),c.push("<br>")})),c.join(" ")):""}}}function h(e){var t=e.position,i=e.selectData,n=e.dimension,r=e.metrics,o=e.rows,a=e.label,s=e.itemStyle,l=e.selectedMode,c=e.roam,d=e.center,h=e.aspectScale,f=e.boundingCoords,m=e.zoom,v=e.labelMap,g=e.scaleLimit,y=e.mapGrid,b=[],x={type:"map",mapType:t};return r.forEach((function(e){var t=u({name:null!=v[e]?v[e]:e,data:[],selectedMode:l,roam:c,center:d,aspectScale:h,boundingCoords:f,zoom:m,scaleLimit:g},x);y&&Object.keys(y).forEach((function(e){t[e]=y[e]})),p(s,t,"itemStyle"),p(a,t,"label"),o.forEach((function(r){t.data.push({name:r[n],value:r[e],selected:i})})),b.push(t)})),b}function p(e,t,i){"object"===("undefined"===typeof e?"undefined":c(e))?t[i]=e:e&&(t[i]={normal:{show:!0},emphasis:{show:!0}})}function f(e){var t=e.metrics,i=e.legendName,n=e.labelMap;if(!i&&!n)return{data:t};var r=n?t.map((function(e){return null==n[e]?e:n[e]})):t;return{data:r,formatter:function(e){return null!=i[e]?i[e]:e}}}function m(e,t){var i=e._once,n=e.registerSign,r=e.beforeRegisterMap,a=e.beforeRegisterMapOnce,s=e.registerSignOnce,l=e.position,c=e.specialAreas;i[n]||(r&&(t=r(t)),a&&!i[s]&&(i[s]=!0,t=a(t)),i[n]=!0,o.registerMap(l,t,c))}var v=function(e,t,i,n){var r=i.position,o=void 0===r?"china":r,a=i.selectData,l=void 0!==a&&a,c=i.selectedMode,u=i.label,p=void 0===u||u,v=i.dataType,g=void 0===v?{}:v,y=i.digit,b=void 0===y?2:y,x=i.dimension,w=void 0===x?e[0]:x,_=i.roam,S=i.center,O=i.aspectScale,E=i.boundingCoords,T=i.zoom,k=i.scaleLimit,A=i.legendName,z=void 0===A?{}:A,M=i.labelMap,D=void 0===M?{}:M,N=i.mapGrid,C=i.itemStyle,j=i.positionJsonLink,R=i.beforeRegisterMap,I=i.beforeRegisterMapOnce,L=i.mapURLProfix,F=void 0===L?"https://unpkg.com/echarts@3.6.2/map/json/":L,P=i.specialAreas,V=void 0===P?{}:P,$=i.mapOrigin,H=e.slice();i.metrics?H=i.metrics:H.splice(e.indexOf(w),1);var B=n.tooltipVisible,Y=n.legendVisible,q=n.color,X=n._once,U={};t.forEach((function(e){U[e[w]]=e}));var W=B&&d(g,b,U,H,q,D),G=Y&&f({metrics:H,legendName:z,labelMap:D}),K={position:o,selectData:l,label:p,itemStyle:C,dimension:w,metrics:H,rows:t,selectedMode:c,roam:_,center:S,aspectScale:O,boundingCoords:E,zoom:T,labelMap:D,scaleLimit:k,mapGrid:N},Z=h(K),J={_once:X,beforeRegisterMap:R,beforeRegisterMapOnce:I,registerSign:"MAP_REGISTER_"+o,registerSignOnce:"ONCE_MAP_REGISTER_"+o,position:o,specialAreas:V};return $?(m(J,$),{series:Z,tooltip:W,legend:G}):s.getMapJSON({position:o,positionJsonLink:j,beforeRegisterMapOnce:I,mapURLProfix:F}).then((function(e){return m(J,e),{series:Z,tooltip:W,legend:G}}))},g=u({},l,{name:"VeMap",data:function(){return this.chartHandler=v,{}}});e.exports=g},73737:function(e,t,i){"use strict";var n=i(57847)["default"];function r(e){return e&&"object"===n(e)&&"default"in e?e["default"]:e}var o=i(8993),a=i(87279),s=i(55925);i(89174);var l=r(i(82445)),c=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n])}return e},u=100,d=[80,100],h=[20,100],p=200;function f(e){var t=e.innerRows,i=e.dataType,n=e.percentShow,r=e.dimension,o=e.metrics,s=e.radius,l=e.offsetY,u=e.selectedMode,d=e.hoverAnimation,h=e.digit,p=e.roseType,f=e.label,m=e.level,v=e.limitShowNum,g=e.isRing,y=e.labelLine,b=e.itemStyle,x=[],w={},_=[];m?(m.forEach((function(e,t){e.forEach((function(e){a.setArrayValue(w,e,t)}))})),t.forEach((function(e){var t=w[e[r]];t&&t.length&&t.forEach((function(t){a.setArrayValue(_,t,e)}))}))):_.push(t);var S={type:"pie",selectedMode:u,hoverAnimation:d,roseType:p,center:["50%",l]},O=_.length;if(_.forEach((function(e,t){var l=c({data:[]},S),u=s/O;if(t){var d=u+s/(2*O)*(2*t-1),p=d+s/(2*O);l.radius=[d,p]}else l.radius=g?s:u;O>1&&0===t&&(l.label={normal:{position:"inner"}}),f&&(l.label=f),y&&(l.labelLine=y),b&&(l.itemStyle=b),n&&(l.label={normal:{show:!0,position:O>1&&0===t?"inner":"outside",formatter:function(e){var t=[];return t.push(e.name+":"),t.push(a.getFormated(e.value,i,h)),t.push("("+e.percent+"%)"),t.join(" ")}}}),l.data=e.map((function(e){return{name:e[r],value:e[o]}})),x.push(l)})),v&&v<x[0].data.length){var E=x[0].data,T=E.slice(v,E.length),k=0;T.forEach((function(e){k+=e.value})),x[0].data=E.slice(0,v),x[0].data.push({name:"其他",value:k})}return x}function m(e){var t=e.innerRows,i=e.dimension,n=e.legendLimit,r=e.legendName,o=e.level,a=e.limitShowNum,s=[],l=[];if(o)o.forEach((function(e){e.forEach((function(e){l.push(e)}))})),s=l;else if(a&&a<t.length){for(var c=0;c<a;c++)s.push(t[c][i]);s.push("其他")}else s=t.map((function(e){return e[i]}));return!!s.length&&{data:s,show:s.length<n,formatter:function(e){return null!=r[e]?r[e]:e}}}function v(e){var t=e.dataType,i=e.innerRows,n=e.limitShowNum,r=e.digit,s=e.metrics,l=e.dimension,c=0,u=i.map((function(e){return c+=e[s],{name:e[l],value:e[s]}})).slice(n,i.length);return{formatter:function(e){var i=[];return i.push(o.itemPoint(e.color)),n&&"其他"===e.name?(i.push("其他:"),u.forEach((function(e){var n=e.name,o=e.value,s=a.getFormated(o/c,"percent");i.push("<br>"+n+":"),i.push(a.getFormated(o,t,r)),i.push("("+s+")")}))):(i.push(e.name+":"),i.push(a.getFormated(e.value,t,r)),i.push("("+e.percent+"%)")),i.join(" ")}}}var g=function(e,t,i,n,r){var o=s.cloneDeep(t),a=i.dataType,l=void 0===a?"normal":a,c=i.percentShow,g=i.dimension,y=void 0===g?e[0]:g,b=i.metrics,x=void 0===b?e[1]:b,w=i.roseType,_=void 0!==w&&w,S=i.radius,O=void 0===S?r?_?h:d:u:S,E=i.offsetY,T=void 0===E?p:E,k=i.legendLimit,A=void 0===k?30:k,z=i.selectedMode,M=void 0!==z&&z,D=i.hoverAnimation,N=void 0===D||D,C=i.digit,j=void 0===C?2:C,R=i.legendName,I=void 0===R?{}:R,L=i.label,F=void 0!==L&&L,P=i.level,V=void 0!==P&&P,$=i.limitShowNum,H=void 0===$?0:$,B=i.labelLine,Y=i.itemStyle,q=n.tooltipVisible,X=n.legendVisible;H&&o.sort((function(e,t){return t[x]-e[x]}));var U={innerRows:o,dataType:l,percentShow:c,dimension:y,metrics:x,radius:O,offsetY:T,selectedMode:M,hoverAnimation:N,digit:j,roseType:_,label:F,level:V,legendName:I,limitShowNum:H,isRing:r,labelLine:B,itemStyle:Y},W=f(U),G={innerRows:o,dimension:y,legendLimit:A,legendName:I,level:V,limitShowNum:H},K=X&&m(G),Z=q&&v({dataType:l,innerRows:o,limitShowNum:H,digit:j,metrics:x,dimension:y}),J={series:W,legend:K,tooltip:Z};return J},y=c({},l,{name:"VePie",data:function(){return this.chartHandler=g,{}}});e.exports=y},70462:function(e,t,i){"use strict";var n=i(57847)["default"];function r(e){return e&&"object"===n(e)&&"default"in e?e["default"]:e}var o=i(8993),a=i(87279);i(76515);var s=r(i(82445));function l(e,t,i){var n=e.map((function(e){return e[t]}));return{data:n,formatter:function(e){return null!=i[e]?i[e]:e}}}function c(e,t,i){var n=[],r=[];return t.indicator.map((function(t,i){n[i]=e[t.name],r[i]=t.name})),{formatter:function(e){var t=[];return t.push(o.itemPoint(e.color)),t.push(e.name+"<br />"),e.data.value.forEach((function(e,o){t.push(r[o]+": "),t.push(a.getFormated(e,n[o],i)+"<br />")})),t.join("")}}}function u(e,t,i){var n={indicator:[],shape:"circle",splitNumber:5},r={};return e.forEach((function(e){t.forEach((function(t){var n=null!=i[t]?i[t]:t;r[n]?r[n].push(e[t]):r[n]=[e[t]]}))})),n.indicator=Object.keys(r).map((function(e){return{name:e,max:Math.max.apply(null,r[e])}})),n}function d(e){var t=e.rows,i=e.dimension,n=e.metrics,r=e.radar,o=e.label,a=e.itemStyle,s=e.lineStyle,l=e.labelMap,c=e.areaStyle,u={};r.indicator.forEach((function(e,t){var i=e.name;u[i]=t}));var d=t.map((function(e){var t={value:[],name:e[i]};return Object.keys(e).forEach((function(i){if(~n.indexOf(i)){var r=null!=l[i]?u[l[i]]:u[i];t.value[r]=e[i]}})),t})),h={name:i,type:"radar",data:d};return o&&(h.label=o),a&&(h.itemStyle=a),s&&(h.lineStyle=s),c&&(h.areaStyle=c),[h]}var h=function(e,t,i,n){var r=i.dataType,o=void 0===r?{}:r,a=i.legendName,s=void 0===a?{}:a,h=i.labelMap,p=void 0===h?{}:h,f=i.dimension,m=void 0===f?e[0]:f,v=i.digit,g=void 0===v?2:v,y=i.label,b=i.itemStyle,x=i.lineStyle,w=i.areaStyle,_=n.tooltipVisible,S=n.legendVisible,O=e.slice();i.metrics?O=i.metrics:O.splice(e.indexOf(m),1);var E=S&&l(t,m,s),T=u(t,O,p),k=_&&c(o,T,g),A=d({rows:t,dimension:m,metrics:O,radar:T,label:y,itemStyle:b,lineStyle:x,labelMap:p,areaStyle:w}),z={legend:E,tooltip:k,radar:T,series:A};return z},p=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n])}return e},f=p({},s,{name:"VeRadar",data:function(){return this.chartHandler=h,{}}});e.exports=f},85816:function(e,t,i){"use strict";var n=i(57847)["default"];function r(e){return e&&"object"===n(e)&&"default"in e?e["default"]:e}var o=i(8993),a=i(87279),s=i(55925);i(89174);var l=r(i(82445)),c=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n])}return e},u=100,d=[80,100],h=[20,100],p=200;function f(e){var t=e.innerRows,i=e.dataType,n=e.percentShow,r=e.dimension,o=e.metrics,s=e.radius,l=e.offsetY,u=e.selectedMode,d=e.hoverAnimation,h=e.digit,p=e.roseType,f=e.label,m=e.level,v=e.limitShowNum,g=e.isRing,y=e.labelLine,b=e.itemStyle,x=[],w={},_=[];m?(m.forEach((function(e,t){e.forEach((function(e){a.setArrayValue(w,e,t)}))})),t.forEach((function(e){var t=w[e[r]];t&&t.length&&t.forEach((function(t){a.setArrayValue(_,t,e)}))}))):_.push(t);var S={type:"pie",selectedMode:u,hoverAnimation:d,roseType:p,center:["50%",l]},O=_.length;if(_.forEach((function(e,t){var l=c({data:[]},S),u=s/O;if(t){var d=u+s/(2*O)*(2*t-1),p=d+s/(2*O);l.radius=[d,p]}else l.radius=g?s:u;O>1&&0===t&&(l.label={normal:{position:"inner"}}),f&&(l.label=f),y&&(l.labelLine=y),b&&(l.itemStyle=b),n&&(l.label={normal:{show:!0,position:O>1&&0===t?"inner":"outside",formatter:function(e){var t=[];return t.push(e.name+":"),t.push(a.getFormated(e.value,i,h)),t.push("("+e.percent+"%)"),t.join(" ")}}}),l.data=e.map((function(e){return{name:e[r],value:e[o]}})),x.push(l)})),v&&v<x[0].data.length){var E=x[0].data,T=E.slice(v,E.length),k=0;T.forEach((function(e){k+=e.value})),x[0].data=E.slice(0,v),x[0].data.push({name:"其他",value:k})}return x}function m(e){var t=e.innerRows,i=e.dimension,n=e.legendLimit,r=e.legendName,o=e.level,a=e.limitShowNum,s=[],l=[];if(o)o.forEach((function(e){e.forEach((function(e){l.push(e)}))})),s=l;else if(a&&a<t.length){for(var c=0;c<a;c++)s.push(t[c][i]);s.push("其他")}else s=t.map((function(e){return e[i]}));return!!s.length&&{data:s,show:s.length<n,formatter:function(e){return null!=r[e]?r[e]:e}}}function v(e){var t=e.dataType,i=e.innerRows,n=e.limitShowNum,r=e.digit,s=e.metrics,l=e.dimension,c=0,u=i.map((function(e){return c+=e[s],{name:e[l],value:e[s]}})).slice(n,i.length);return{formatter:function(e){var i=[];return i.push(o.itemPoint(e.color)),n&&"其他"===e.name?(i.push("其他:"),u.forEach((function(e){var n=e.name,o=e.value,s=a.getFormated(o/c,"percent");i.push("<br>"+n+":"),i.push(a.getFormated(o,t,r)),i.push("("+s+")")}))):(i.push(e.name+":"),i.push(a.getFormated(e.value,t,r)),i.push("("+e.percent+"%)")),i.join(" ")}}}var g=function(e,t,i,n,r){var o=s.cloneDeep(t),a=i.dataType,l=void 0===a?"normal":a,c=i.percentShow,g=i.dimension,y=void 0===g?e[0]:g,b=i.metrics,x=void 0===b?e[1]:b,w=i.roseType,_=void 0!==w&&w,S=i.radius,O=void 0===S?r?_?h:d:u:S,E=i.offsetY,T=void 0===E?p:E,k=i.legendLimit,A=void 0===k?30:k,z=i.selectedMode,M=void 0!==z&&z,D=i.hoverAnimation,N=void 0===D||D,C=i.digit,j=void 0===C?2:C,R=i.legendName,I=void 0===R?{}:R,L=i.label,F=void 0!==L&&L,P=i.level,V=void 0!==P&&P,$=i.limitShowNum,H=void 0===$?0:$,B=i.labelLine,Y=i.itemStyle,q=n.tooltipVisible,X=n.legendVisible;H&&o.sort((function(e,t){return t[x]-e[x]}));var U={innerRows:o,dataType:l,percentShow:c,dimension:y,metrics:x,radius:O,offsetY:T,selectedMode:M,hoverAnimation:N,digit:j,roseType:_,label:F,level:V,legendName:I,limitShowNum:H,isRing:r,labelLine:B,itemStyle:Y},W=f(U),G={innerRows:o,dimension:y,legendLimit:A,legendName:I,level:V,limitShowNum:H},K=X&&m(G),Z=q&&v({dataType:l,innerRows:o,limitShowNum:H,digit:j,metrics:x,dimension:y}),J={series:W,legend:K,tooltip:Z};return J},y=function(e,t,i,n){return g(e,t,i,n,!0)},b=c({},l,{name:"VeRing",data:function(){return this.chartHandler=y,{}}});e.exports=b},96599:function(e,t,i){"use strict";var n=i(57847)["default"];function r(e){return e&&"object"===n(e)&&"default"in e?e["default"]:e}var o=i(87279),a=i(8993);i(9669);var s=r(i(82445)),l=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n])}return e};function c(e){var t=e.itemDataType,i=e.linksDataType,n=e.digit;return{trigger:"item",formatter:function(e){var r=[],s=e.name,l=e.data,c=e.value,u=e.color;return r.push(a.itemPoint(u)),r.push(s+" : "),l&&l.source?r.push(o.getFormated(c,i,n)+"<br />"):r.push(o.getFormated(c,t,n)+"<br />"),r.join("")}}}function u(e){var t=e.rows,i=e.dimension,n=e.metrics,r=e.links,o=e.valueFull,a=e.useDataValue,s=e.label,c=e.itemStyle,u=e.lineStyle,d={},h=t.map((function(e){return d[e[i]]=e[n],{name:e[i],value:e[n]}})),p=null;p=a?r.map((function(e){return l({},e,{value:d[e.target]})})):o?r:r.map((function(e){return null==e.value?l({},e,{value:d[e.target]}):e}));var f={type:"sankey",data:h,links:p};return s&&(f.label=s),c&&(f.itemStyle=c),u&&(f.lineStyle=u),[f]}var d=function(e,t,i,n){var r=i.links,o=i.dimension,a=void 0===o?e[0]:o,s=i.metrics,l=void 0===s?e[1]:s,d=i.dataType,h=void 0===d?["normal","normal"]:d,p=i.digit,f=void 0===p?2:p,m=i.valueFull,v=void 0!==m&&m,g=i.useDataValue,y=void 0!==g&&g,b=i.label,x=i.itemStyle,w=i.lineStyle;if(r){var _=h[0],S=h[1],O=c({itemDataType:_,linksDataType:S,digit:f}),E=u({rows:t,dimension:a,metrics:l,links:r,valueFull:v,useDataValue:y,label:b,itemStyle:x,lineStyle:w});return{tooltip:O,series:E}}},h=l({},s,{name:"VeSankey",data:function(){return this.chartHandler=d,{}}});e.exports=h},27568:function(e,t,i){"use strict";var n=i(57847)["default"];function r(e){return e&&"object"===n(e)&&"default"in e?e["default"]:e}var o=i(87279),a=i(55925),s=i(8993);i(46413);var l=r(i(82445)),c=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n])}return e};function u(e){var t=e.dimension,i=e.rows,n=e.xAxisName,r=e.axisVisible,o=e.xAxisType;return t.map((function(e,t){return{type:o,nameLocation:"middle",nameGap:22,name:n[t]||"",axisTick:{show:!0,lineStyle:{color:"#eee"}},data:i.map((function(t){return t[e]})),show:r}}))}function d(e){var t=e.rows,i=e.axisSite,n=e.metrics,r=e.area,a=e.stack,s=e.nullAddZero,l=e.labelMap,c=e.label,u=e.itemStyle,d=e.lineStyle,h=e.areaStyle,p=e.dimension,f=[],m={},v=a&&o.getStackMap(a);return n.forEach((function(e){m[e]=[]})),t.forEach((function(e){n.forEach((function(t){var i=null;null!=e[t]?i=e[t]:s&&(i=0),m[t].push([e[p[0]],i])}))})),n.forEach((function(e){var t={name:null!=l[e]?l[e]:e,type:"line",data:m[e]};r&&(t.areaStyle={normal:{}}),i.right&&(t.yAxisIndex=~i.right.indexOf(e)?1:0),a&&v[e]&&(t.stack=v[e]),c&&(t.label=c),u&&(t.itemStyle=u),d&&(t.lineStyle=d),h&&(t.areaStyle=h),f.push(t)})),f}function h(e){for(var t=e.yAxisName,i=e.yAxisType,n=e.axisVisible,r=e.scale,a=e.min,s=e.max,l=e.digit,u={type:"value",axisTick:{show:!1},show:n},d=[],h=function(e){i[e]?d[e]=c({},u,{axisLabel:{formatter:function(t){return o.getFormated(t,i[e],l)}}}):d[e]=c({},u),d[e].name=t[e]||"",d[e].scale=r[e]||!1,d[e].min=a[e]||null,d[e].max=s[e]||null},p=0;p<2;p++)h(p);return d}function p(e){var t=e.axisSite,i=e.yAxisType,n=e.digit,r=e.labelMap,s=e.tooltipFormatter,l=t.right||[],c=r?l.map((function(e){return void 0===r[e]?e:r[e]})):l;return{trigger:"axis",formatter:function(e){if(s)return s.apply(null,arguments);var t=[],r=e[0],l=r.name,u=r.axisValueLabel,d=l||u;return t.push(d+"<br>"),e.forEach((function(e){var r=e.seriesName,s=e.data,l=e.marker,u=null,d=~c.indexOf(r)?i[1]:i[0],h=a.isArray(s)?s[1]:s;u=o.getFormated(h,d,n),t.push(l),t.push(r+": "+u),t.push("<br>")})),t.join("")}}}function f(e){var t=e.metrics,i=e.legendName,n=e.labelMap;if(!i&&!n)return{data:t};var r=n?t.map((function(e){return null==n[e]?e:n[e]})):t;return{data:r,formatter:function(e){return null!=i[e]?i[e]:e}}}var m=function(e,t,i,n){t=a.isArray(t)?t:[],e=a.isArray(e)?e:[];var r=i.axisSite,o=void 0===r?{}:r,s=i.yAxisType,l=void 0===s?["normal","normal"]:s,c=i.xAxisType,m=void 0===c?"category":c,v=i.yAxisName,g=void 0===v?[]:v,y=i.dimension,b=void 0===y?[e[0]]:y,x=i.xAxisName,w=void 0===x?[]:x,_=i.axisVisible,S=void 0===_||_,O=i.area,E=i.stack,T=i.scale,k=void 0===T?[!1,!1]:T,A=i.min,z=void 0===A?[null,null]:A,M=i.max,D=void 0===M?[null,null]:M,N=i.nullAddZero,C=void 0!==N&&N,j=i.digit,R=void 0===j?2:j,I=i.legendName,L=void 0===I?{}:I,F=i.labelMap,P=void 0===F?{}:F,V=i.label,$=i.itemStyle,H=i.lineStyle,B=i.areaStyle,Y=n.tooltipVisible,q=n.legendVisible,X=n.tooltipFormatter,U=e.slice();o.left&&o.right?U=o.left.concat(o.right):o.left&&!o.right?U=o.left:i.metrics?U=i.metrics:U.splice(e.indexOf(b[0]),1);var W=q&&f({metrics:U,legendName:L,labelMap:P}),G=Y&&p({axisSite:o,yAxisType:l,digit:R,labelMap:P,xAxisType:m,tooltipFormatter:X}),K=u({dimension:b,rows:t,xAxisName:w,axisVisible:S,xAxisType:m}),Z=h({yAxisName:g,yAxisType:l,axisVisible:S,scale:k,min:z,max:D,digit:R}),J=d({rows:t,axisSite:o,metrics:U,area:O,stack:E,nullAddZero:C,labelMap:P,label:V,itemStyle:$,lineStyle:H,areaStyle:B,xAxisType:m,dimension:b}),Q={legend:W,xAxis:K,series:J,yAxis:Z,tooltip:G};return Q};function v(e,t){return{data:e,formatter:function(e){return null!=t[e]?t[e]:e}}}function g(e){var t=e.tooltipTrigger;return{trigger:t,formatter:function(t){return a.isArray(t)?t.map((function(t){return y(t,e)})).join(""):y(t,e)}}}function y(e,t){var i=t.labelMap,n=t.columns,r=t.dataType,a=t.digit,l=[],c=e.color,u=e.seriesName,d=e.data.value;return l.push(s.itemPoint(c)+" "+u+"<br>"),d.forEach((function(e,t){var s=i[n[t]]||n[t],c=isNaN(e)?e:o.getFormated(e,r[n[t]],a);l.push(s+": "+c+"<br>")})),l.join("")}function b(e){var t=e.xAxisName,i=e.axisVisible,n=e.xAxisType,r=e.rows,o=e.dataLabels,a=e.dimension,s=[];return o.forEach((function(e){var t=r[e];t.forEach((function(e){var t=e[a];t&&!~s.indexOf(t)&&s.push(t)}))})),[{type:n,show:i,name:t,data:s}]}function x(e){var t=e.min,i=e.max,n=e.scale,r=e.yAxisName,a=e.dataType,s=e.metrics,l=e.digit,c=e.axisVisible;return{type:"value",show:c,scale:n,min:t,max:i,axisTick:{show:!1},name:r,axisLabel:{formatter:function(e){return o.getFormated(e,a[s[0]],l)}}}}function w(e){var t=e.rows,i=e.dataLabels,n=e.columns,r=e.metrics,o=e.dimension,a=e.label,s=e.itemStyle,l=e.symbol,c=e.symbolSizeMax,u=e.symbolSize,d=e.symbolRotate,h=e.symbolOffset,p=e.cursor,f=n.filter((function(e){return!~r.indexOf(e)&&e!==o})),m=[];i.forEach((function(e){t[e].forEach((function(e){m.push(e[r[1]])}))}));var v=Math.max.apply(null,m),g=[];return i.forEach((function(e){var i=[],n=t[e];n.forEach((function(e){var t={value:[]};t.value.push(e[o],e[r[0]],e[r[1]]),f.forEach((function(i){t.value.push(e[i])})),t.symbolSize=u||e[r[1]]/v*c,i.push(t)})),g.push({type:"scatter",data:i,name:e,label:a,itemStyle:s,symbol:l,symbolRotate:d,symbolOffset:h,cursor:p})})),g}var _=function(e,t,i,n){var r=i.dimension,o=void 0===r?e[0]:r,s=i.metrics,l=void 0===s?[e[1],e[2]]:s,u=i.dataType,d=void 0===u?{}:u,h=i.xAxisType,p=void 0===h?"category":h,f=i.xAxisName,y=i.yAxisName,_=i.digit,S=void 0===_?2:_,O=i.legendName,E=void 0===O?{}:O,T=i.labelMap,k=void 0===T?{}:T,A=i.tooltipTrigger,z=void 0===A?"item":A,M=i.axisVisible,D=void 0===M||M,N=i.symbolSizeMax,C=void 0===N?50:N,j=i.symbol,R=i.symbolSize,I=i.symbolRotate,L=i.symbolOffset,F=i.cursor,P=i.min,V=i.max,$=i.scale,H=i.label,B=i.itemStyle;if(a.isArray(t)){var Y=c({},i,{xAxisName:f?[f]:void 0,yAxisName:y?[y]:void 0,scale:$?[$]:void 0,min:P?[P]:void 0,max:V?[V]:void 0,dimension:o?[o]:void 0}),q=m(e,t,Y,n);return q&&q.series?(q.series.forEach((function(e){c(e,{type:"scatter",symbol:j,symbolSize:R||10,symbolRotate:I,symbolOffset:L,cursor:F,label:H,itemStyle:B})})),q):{}}var X=n.tooltipVisible,U=n.legendVisible,W=Object.keys(t),G=U&&v(W,E),K=X&&g({tooltipTrigger:z,labelMap:k,columns:e,dataType:d,digit:S}),Z=b({xAxisName:f,axisVisible:D,xAxisType:p,dataLabels:W,dimension:o,rows:t}),J=x({min:P,max:V,scale:$,yAxisName:y,dataType:d,metrics:l,digit:S,axisVisible:D}),Q=w({rows:t,dataLabels:W,columns:e,metrics:l,dimension:o,label:H,itemStyle:B,symbol:j,symbolSizeMax:C,symbolSize:R,symbolRotate:I,symbolOffset:L,cursor:F});return{legend:G,tooltip:K,xAxis:Z,yAxis:J,series:Q}},S=c({},l,{name:"VeScatter",data:function(){return this.chartHandler=_,{}}});e.exports=S},6685:function(e,t,i){"use strict";var n=i(57847)["default"];function r(e){return e&&"object"===n(e)&&"default"in e?e["default"]:e}var o=i(55925);i(12107);var a=r(i(82445)),s=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n])}return e};function l(e){var t=e.dimension,i=e.rows,n=i.map((function(e){return e[t]}));return{data:n}}function c(e){var t=e.tooltipFormatter;return{trigger:"item",triggerOn:"mousemove",formatter:t}}function u(e){var t=e.dimension,i=e.metrics,n=e.rows,r=e.seriesMap,a=[];return n.forEach((function(e){var n=e[t],l=r[n],c={type:"tree",name:e[t],data:e[i]};r[e[t]]&&Object.keys(l).forEach((function(e){o.isObject(c[e])?s(c[e],l[e]):c[e]=l[e]})),a.push(c)})),a}var d=function(e,t,i,n){var r=i.dimension,o=void 0===r?e[0]:r,a=i.metrics,s=void 0===a?e[1]:a,d=i.seriesMap,h=void 0===d?{}:d,p=n.legendVisible,f=n.tooltipFormatter,m=n.tooltipVisible,v=u({dimension:o,metrics:s,rows:t,seriesMap:h}),g=p&&t.length>1&&l({dimension:o,rows:t}),y=m&&c({tooltipFormatter:f});return{series:v,legend:g,tooltip:y}},h=s({},a,{name:"VeTree",data:function(){return this.chartHandler=d,{}}});e.exports=h},87279:function(e,t,i){"use strict";var n=i(57847)["default"];function r(e){return e&&"object"===n(e)&&"default"in e?e["default"]:e}Object.defineProperty(t,"__esModule",{value:!0});var o=r(i(68572)),a=i(55925),s=function(e,t,i){var n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"-";if(isNaN(e))return n;if(!t)return e;if(a.isFunction(t))return t(e,o);i=isNaN(i)?0:++i;var r=".["+new Array(i).join(0)+"]",s=t;switch(t){case"KMB":s=i?"0,0"+r+"a":"0,0a";break;case"normal":s=i?"0,0"+r:"0,0";break;case"percent":s=i?"0,0"+r+"%":"0,0.[00]%";break}return o(e,s)},l=function(e){var t={};return Object.keys(e).forEach((function(i){e[i].forEach((function(e){t[e]=i}))})),t},c=function(e){return new Promise((function(t,i){var n=new XMLHttpRequest;n.open("GET",e),n.send(null),n.onload=function(){t(JSON.parse(n.responseText))},n.onerror=function(){i(JSON.parse(n.responseText))}}))},u={},d=function(e){var t=e.position,i=e.positionJsonLink,n=e.beforeRegisterMapOnce,r=e.mapURLProfix,o=i||""+r+t+".json";return u[o]||(u[o]=c(o).then((function(e){return n&&(e=n(e)),e}))),u[o]},h=null,p=null,f=function(e,t){return h||(h=new Promise((function(i,n){var r="bmap"+Date.now();window[r]=i;var o=document.createElement("script");o.src=["https://api.map.baidu.com/api?v="+(t||"2.0"),"ak="+e,"callback="+r].join("&"),document.body.appendChild(o)}))),h},m=function(e,t){return p||(p=new Promise((function(i,n){var r="amap"+Date.now();window[r]=i;var o=document.createElement("script");o.src=["https://webapi.amap.com/maps?v="+(t||"1.4.3"),"key="+e,"callback="+r].join("&"),document.body.appendChild(o)}))),p};function v(e,t,i){void 0!==e[t]?e[t].push(i):e[t]=[i]}t.getFormated=s,t.getStackMap=l,t.$get=c,t.getMapJSON=d,t.getBmap=f,t.getAmap=m,t.setArrayValue=v},97577:function(e,t,i){"use strict";var n=i(57847)["default"];function r(e){return e&&"object"===n(e)&&"default"in e?e["default"]:e}var o=i(87279);i(19467);var a=r(i(82445)),s=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n])}return e};function l(e,t){return{trigger:"axis",axisPointer:{type:"shadow"},formatter:function(i){var n=i[1];return[n.name+"<br/>"+n.seriesName+" :",""+o.getFormated(n.value,e,t)].join("")}}}function c(e){var t=e.dimension,i=e.rows,n=e.remainStatus,r=e.totalName,o=e.remainName,a=e.labelMap,s=e.xAxisName,l=e.axisVisible,c=[r].concat(i.map((function(e){return e[t]})));return"have-remain"===n&&(c=c.concat([o])),{type:"category",name:a&&a[s]||s,splitLine:{show:!1},data:c,show:l}}function u(e){var t=e.dataType,i=e.yAxisName,n=e.axisVisible,r=e.digit,a=e.labelMap;return{type:"value",name:null!=a[i]?a[i]:i,axisTick:{show:!1},axisLabel:{formatter:function(e){return o.getFormated(e,t,r)}},show:n}}function d(e){var t=e.dataType,i=e.rows,n=e.metrics,r=e.totalNum,a=e.remainStatus,l=e.dataSum,c=e.digit,u={type:"bar",stack:"总量"},d=l,h=r,p=void 0,f=void 0,m=i.map((function(e){return e[n]}));"have-remain"===a?(p=[0].concat(i.map((function(e){return h-=e[n],h}))).concat([0]),f=[r].concat(m).concat([r-l])):(p=[0].concat(i.map((function(e){return d-=e[n],d}))),f=[l].concat(m));var v=[];return v.push(s({name:"辅助",itemStyle:{normal:{opacity:0},emphasis:{opacity:0}},data:p},u)),v.push(s({name:"数值",label:{normal:{show:!0,position:"top",formatter:function(e){return o.getFormated(e.value,t,c)}}},data:f},u)),v}function h(e,t){return t?t>e?"have-remain":"none-remain":"not-total"}var p=function(e,t,i,n){var r=i.dataType,o=void 0===r?"normal":r,a=i.dimension,s=void 0===a?e[0]:a,p=i.totalName,f=void 0===p?"总计":p,m=i.totalNum,v=i.remainName,g=void 0===v?"其他":v,y=i.xAxisName,b=void 0===y?s:y,x=i.labelMap,w=void 0===x?{}:x,_=i.axisVisible,S=void 0===_||_,O=i.digit,E=void 0===O?2:O,T=n.tooltipVisible,k=e.slice();k.splice(k.indexOf(s),1);var A=k[0],z=A,M=T&&l(o,E),D=parseFloat(t.reduce((function(e,t){return e+Number(t[A])}),0).toFixed(E)),N=h(D,m),C={dimension:s,rows:t,remainStatus:N,totalName:f,remainName:g,xAxisName:b,labelMap:w,axisVisible:S},j=c(C),R=u({dataType:o,yAxisName:z,axisVisible:S,digit:E,labelMap:w}),I={dataType:o,rows:t,dimension:s,metrics:A,totalNum:m,remainStatus:N,dataSum:D,digit:E},L=d(I),F={tooltip:M,xAxis:j,yAxis:R,series:L};return F},f=s({},a,{name:"VeWaterfall",data:function(){return this.chartHandler=p,{}}});e.exports=f},44469:function(e,t,i){"use strict";var n=i(57847)["default"];function r(e){return e&&"object"===n(e)&&"default"in e?e["default"]:e}var o=i(55925);i(8856);var a=r(i(82445));function s(e){var t=e.dimension,i=e.metrics,n=e.rows,r=e.color,a=e.sizeMax,s=e.sizeMin,l=e.shape,c={type:"wordCloud",textStyle:{normal:{color:!o.isArray(r)&&r?r:function(){return"rgb("+[Math.round(160*Math.random()),Math.round(160*Math.random()),Math.round(160*Math.random())].join(",")+")"}}},shape:l,sizeRange:[s,a]},u=o.isArray(r)?r.length:0,d=n.slice().map((function(e){var n={name:e[t],value:e[i]};return u>0&&(n.textStyle={normal:{color:r[Math.floor(Math.random()*u)]}}),n}));return c.data=d,[c]}function l(e){var t=e.tooltipFormatter;return{show:!0,formatter:function(e){var i=e.data,n=i.name,r=i.value;return t?t.apply(null,e):n+": "+r}}}var c=function(e,t,i,n){var r=i.dimension,o=void 0===r?e[0]:r,a=i.metrics,c=void 0===a?e[1]:a,u=i.color,d=void 0===u?"":u,h=i.sizeMax,p=void 0===h?60:h,f=i.sizeMin,m=void 0===f?12:f,v=i.shape,g=void 0===v?"circle":v,y=n.tooltipVisible,b=n.tooltipFormatter,x=s({dimension:o,metrics:c,rows:t,color:d,sizeMax:p,sizeMin:m,shape:g}),w=y&&l({tooltipFormatter:b});return{series:x,tooltip:w}},u=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n])}return e},d=u({},a,{name:"VeWordcloud",data:function(){return this.chartHandler=c,{}}});e.exports=d},43099:function(e,t,i){var n,r,o=i(57847)["default"];i(65743),i(32564),
/*!
 * Viewer.js v1.11.2
 * https://fengyuanchen.github.io/viewerjs
 *
 * Copyright 2015-present Chen Fengyuan
 * Released under the MIT license
 *
 * Date: 2023-01-01T10:14:49.638Z
 */
function(a,s){"object"===o(t)?e.exports=s():(n=s,r="function"===typeof n?n.call(t,i,t,e):n,void 0===r||(e.exports=r))}(0,(function(){"use strict";function e(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,n)}return i}function t(t){for(var i=1;i<arguments.length;i++){var n=null!=arguments[i]?arguments[i]:{};i%2?e(Object(n),!0).forEach((function(e){s(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):e(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function i(e){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function r(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,c(n.key),n)}}function a(e,t,i){return t&&r(e.prototype,t),i&&r(e,i),Object.defineProperty(e,"prototype",{writable:!1}),e}function s(e,t,i){return t=c(t),t in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}function l(e,t){if("object"!==o(e)||null===e)return e;var i=e[Symbol.toPrimitive];if(void 0!==i){var n=i.call(e,t||"default");if("object"!==o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function c(e){var t=l(e,"string");return"symbol"===o(t)?t:String(t)}var u={backdrop:!0,button:!0,navbar:!0,title:!0,toolbar:!0,className:"",container:"body",filter:null,fullscreen:!0,inheritedAttributes:["crossOrigin","decoding","isMap","loading","referrerPolicy","sizes","srcset","useMap"],initialCoverage:.9,initialViewIndex:0,inline:!1,interval:5e3,keyboard:!0,focus:!0,loading:!0,loop:!0,minWidth:200,minHeight:100,movable:!0,rotatable:!0,scalable:!0,zoomable:!0,zoomOnTouch:!0,zoomOnWheel:!0,slideOnTouch:!0,toggleOnDblclick:!0,tooltip:!0,transition:!0,zIndex:2015,zIndexInline:0,zoomRatio:.1,minZoomRatio:.01,maxZoomRatio:100,url:"src",ready:null,show:null,shown:null,hide:null,hidden:null,view:null,viewed:null,move:null,moved:null,rotate:null,rotated:null,scale:null,scaled:null,zoom:null,zoomed:null,play:null,stop:null},d='<div class="viewer-container" tabindex="-1" touch-action="none"><div class="viewer-canvas"></div><div class="viewer-footer"><div class="viewer-title"></div><div class="viewer-toolbar"></div><div class="viewer-navbar"><ul class="viewer-list" role="navigation"></ul></div></div><div class="viewer-tooltip" role="alert" aria-hidden="true"></div><div class="viewer-button" data-viewer-action="mix" role="button"></div><div class="viewer-player"></div></div>',h="undefined"!==typeof window&&"undefined"!==typeof window.document,p=h?window:{},f=!(!h||!p.document.documentElement)&&"ontouchstart"in p.document.documentElement,m=!!h&&"PointerEvent"in p,v="viewer",g="move",y="switch",b="zoom",x="".concat(v,"-active"),w="".concat(v,"-close"),_="".concat(v,"-fade"),S="".concat(v,"-fixed"),O="".concat(v,"-fullscreen"),E="".concat(v,"-fullscreen-exit"),T="".concat(v,"-hide"),k="".concat(v,"-hide-md-down"),A="".concat(v,"-hide-sm-down"),z="".concat(v,"-hide-xs-down"),M="".concat(v,"-in"),D="".concat(v,"-invisible"),N="".concat(v,"-loading"),C="".concat(v,"-move"),j="".concat(v,"-open"),R="".concat(v,"-show"),I="".concat(v,"-transition"),L="click",F="dblclick",P="dragstart",V="focusin",$="keydown",H="load",B="error",Y=f?"touchend touchcancel":"mouseup",q=f?"touchmove":"mousemove",X=f?"touchstart":"mousedown",U=m?"pointerdown":X,W=m?"pointermove":q,G=m?"pointerup pointercancel":Y,K="resize",Z="transitionend",J="wheel",Q="ready",ee="show",te="shown",ie="hide",ne="hidden",re="view",oe="viewed",ae="move",se="moved",le="rotate",ce="rotated",ue="scale",de="scaled",he="zoom",pe="zoomed",fe="play",me="stop",ve="".concat(v,"Action"),ge=/\s\s*/,ye=["zoom-in","zoom-out","one-to-one","reset","prev","play","next","rotate-left","rotate-right","flip-horizontal","flip-vertical"];function be(e){return"string"===typeof e}var xe=Number.isNaN||p.isNaN;function we(e){return"number"===typeof e&&!xe(e)}function _e(e){return"undefined"===typeof e}function Se(e){return"object"===i(e)&&null!==e}var Oe=Object.prototype.hasOwnProperty;function Ee(e){if(!Se(e))return!1;try{var t=e.constructor,i=t.prototype;return t&&i&&Oe.call(i,"isPrototypeOf")}catch(n){return!1}}function Te(e){return"function"===typeof e}function ke(e,t){if(e&&Te(t))if(Array.isArray(e)||we(e.length)){var i,n=e.length;for(i=0;i<n;i+=1)if(!1===t.call(e,e[i],i,e))break}else Se(e)&&Object.keys(e).forEach((function(i){t.call(e,e[i],i,e)}));return e}var Ae=Object.assign||function(e){for(var t=arguments.length,i=new Array(t>1?t-1:0),n=1;n<t;n++)i[n-1]=arguments[n];return Se(e)&&i.length>0&&i.forEach((function(t){Se(t)&&Object.keys(t).forEach((function(i){e[i]=t[i]}))})),e},ze=/^(?:width|height|left|top|marginLeft|marginTop)$/;function Me(e,t){var i=e.style;ke(t,(function(e,t){ze.test(t)&&we(e)&&(e+="px"),i[t]=e}))}function De(e){return be(e)?e.replace(/&(?!amp;|quot;|#39;|lt;|gt;)/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&#39;").replace(/</g,"&lt;").replace(/>/g,"&gt;"):e}function Ne(e,t){return!(!e||!t)&&(e.classList?e.classList.contains(t):e.className.indexOf(t)>-1)}function Ce(e,t){if(e&&t)if(we(e.length))ke(e,(function(e){Ce(e,t)}));else if(e.classList)e.classList.add(t);else{var i=e.className.trim();i?i.indexOf(t)<0&&(e.className="".concat(i," ").concat(t)):e.className=t}}function je(e,t){e&&t&&(we(e.length)?ke(e,(function(e){je(e,t)})):e.classList?e.classList.remove(t):e.className.indexOf(t)>=0&&(e.className=e.className.replace(t,"")))}function Re(e,t,i){t&&(we(e.length)?ke(e,(function(e){Re(e,t,i)})):i?Ce(e,t):je(e,t))}var Ie=/([a-z\d])([A-Z])/g;function Le(e){return e.replace(Ie,"$1-$2").toLowerCase()}function Fe(e,t){return Se(e[t])?e[t]:e.dataset?e.dataset[t]:e.getAttribute("data-".concat(Le(t)))}function Pe(e,t,i){Se(i)?e[t]=i:e.dataset?e.dataset[t]=i:e.setAttribute("data-".concat(Le(t)),i)}var Ve=function(){var e=!1;if(h){var t=!1,i=function(){},n=Object.defineProperty({},"once",{get:function(){return e=!0,t},set:function(e){t=e}});p.addEventListener("test",i,n),p.removeEventListener("test",i,n)}return e}();function $e(e,t,i){var n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},r=i;t.trim().split(ge).forEach((function(t){if(!Ve){var o=e.listeners;o&&o[t]&&o[t][i]&&(r=o[t][i],delete o[t][i],0===Object.keys(o[t]).length&&delete o[t],0===Object.keys(o).length&&delete e.listeners)}e.removeEventListener(t,r,n)}))}function He(e,t,i){var n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},r=i;t.trim().split(ge).forEach((function(t){if(n.once&&!Ve){var o=e.listeners,a=void 0===o?{}:o;r=function(){delete a[t][i],e.removeEventListener(t,r,n);for(var o=arguments.length,s=new Array(o),l=0;l<o;l++)s[l]=arguments[l];i.apply(e,s)},a[t]||(a[t]={}),a[t][i]&&e.removeEventListener(t,a[t][i],n),a[t][i]=r,e.listeners=a}e.addEventListener(t,r,n)}))}function Be(e,i,n,r){var o;return Te(Event)&&Te(CustomEvent)?o=new CustomEvent(i,t({bubbles:!0,cancelable:!0,detail:n},r)):(o=document.createEvent("CustomEvent"),o.initCustomEvent(i,!0,!0,n)),e.dispatchEvent(o)}function Ye(e){var t=e.getBoundingClientRect();return{left:t.left+(window.pageXOffset-document.documentElement.clientLeft),top:t.top+(window.pageYOffset-document.documentElement.clientTop)}}function qe(e){var t=e.rotate,i=e.scaleX,n=e.scaleY,r=e.translateX,o=e.translateY,a=[];we(r)&&0!==r&&a.push("translateX(".concat(r,"px)")),we(o)&&0!==o&&a.push("translateY(".concat(o,"px)")),we(t)&&0!==t&&a.push("rotate(".concat(t,"deg)")),we(i)&&1!==i&&a.push("scaleX(".concat(i,")")),we(n)&&1!==n&&a.push("scaleY(".concat(n,")"));var s=a.length?a.join(" "):"none";return{WebkitTransform:s,msTransform:s,transform:s}}function Xe(e){return be(e)?decodeURIComponent(e.replace(/^.*\//,"").replace(/[?&#].*$/,"")):""}var Ue=p.navigator&&/(Macintosh|iPhone|iPod|iPad).*AppleWebKit/i.test(p.navigator.userAgent);function We(e,t,i){var n=document.createElement("img");if(e.naturalWidth&&!Ue)return i(e.naturalWidth,e.naturalHeight),n;var r=document.body||document.documentElement;return n.onload=function(){i(n.width,n.height),Ue||r.removeChild(n)},ke(t.inheritedAttributes,(function(t){var i=e.getAttribute(t);null!==i&&n.setAttribute(t,i)})),n.src=e.src,Ue||(n.style.cssText="left:0;max-height:none!important;max-width:none!important;min-height:0!important;min-width:0!important;opacity:0;position:absolute;top:0;z-index:-1;",r.appendChild(n)),n}function Ge(e){switch(e){case 2:return z;case 3:return A;case 4:return k;default:return""}}function Ke(e){var i=t({},e),n=[];return ke(e,(function(e,t){delete i[t],ke(i,(function(t){var i=Math.abs(e.startX-t.startX),r=Math.abs(e.startY-t.startY),o=Math.abs(e.endX-t.endX),a=Math.abs(e.endY-t.endY),s=Math.sqrt(i*i+r*r),l=Math.sqrt(o*o+a*a),c=(l-s)/s;n.push(c)}))})),n.sort((function(e,t){return Math.abs(e)<Math.abs(t)})),n[0]}function Ze(e,i){var n=e.pageX,r=e.pageY,o={endX:n,endY:r};return i?o:t({timeStamp:Date.now(),startX:n,startY:r},o)}function Je(e){var t=0,i=0,n=0;return ke(e,(function(e){var r=e.startX,o=e.startY;t+=r,i+=o,n+=1})),t/=n,i/=n,{pageX:t,pageY:i}}var Qe={render:function(){this.initContainer(),this.initViewer(),this.initList(),this.renderViewer()},initBody:function(){var e=this.element.ownerDocument,t=e.body||e.documentElement;this.body=t,this.scrollbarWidth=window.innerWidth-e.documentElement.clientWidth,this.initialBodyPaddingRight=t.style.paddingRight,this.initialBodyComputedPaddingRight=window.getComputedStyle(t).paddingRight},initContainer:function(){this.containerData={width:window.innerWidth,height:window.innerHeight}},initViewer:function(){var e,t=this.options,i=this.parent;t.inline&&(e={width:Math.max(i.offsetWidth,t.minWidth),height:Math.max(i.offsetHeight,t.minHeight)},this.parentData=e),!this.fulled&&e||(e=this.containerData),this.viewerData=Ae({},e)},renderViewer:function(){this.options.inline&&!this.fulled&&Me(this.viewer,this.viewerData)},initList:function(){var e=this,t=this.element,i=this.options,n=this.list,r=[];n.innerHTML="",ke(this.images,(function(t,o){var a=t.src,s=t.alt||Xe(a),l=e.getImageURL(t);if(a||l){var c=document.createElement("li"),u=document.createElement("img");ke(i.inheritedAttributes,(function(e){var i=t.getAttribute(e);null!==i&&u.setAttribute(e,i)})),i.navbar&&(u.src=a||l),u.alt=s,u.setAttribute("data-original-url",l||a),c.setAttribute("data-index",o),c.setAttribute("data-viewer-action","view"),c.setAttribute("role","button"),i.keyboard&&c.setAttribute("tabindex",0),c.appendChild(u),n.appendChild(c),r.push(c)}})),this.items=r,ke(r,(function(t){var n,r,o=t.firstElementChild;Pe(o,"filled",!0),i.loading&&Ce(t,N),He(o,H,n=function(n){$e(o,B,r),i.loading&&je(t,N),e.loadImage(n)},{once:!0}),He(o,B,r=function(){$e(o,H,n),i.loading&&je(t,N)},{once:!0})})),i.transition&&He(t,oe,(function(){Ce(n,I)}),{once:!0})},renderList:function(){var e=this.index,t=this.items[e];if(t){var i=t.nextElementSibling,n=parseInt(window.getComputedStyle(i||t).marginLeft,10),r=t.offsetWidth,o=r+n;Me(this.list,Ae({width:o*this.length-n},qe({translateX:(this.viewerData.width-r)/2-o*e})))}},resetList:function(){var e=this.list;e.innerHTML="",je(e,I),Me(e,qe({translateX:0}))},initImage:function(e){var t,i=this,n=this.options,r=this.image,o=this.viewerData,a=this.footer.offsetHeight,s=o.width,l=Math.max(o.height-a,a),c=this.imageData||{};this.imageInitializing={abort:function(){t.onload=null}},t=We(r,n,(function(t,r){var o=t/r,a=Math.max(0,Math.min(1,n.initialCoverage)),u=s,d=l;i.imageInitializing=!1,l*o>s?d=s/o:u=l*o,a=we(a)?a:.9,u=Math.min(u*a,t),d=Math.min(d*a,r);var h=(s-u)/2,p=(l-d)/2,f={left:h,top:p,x:h,y:p,width:u,height:d,oldRatio:1,ratio:u/t,aspectRatio:o,naturalWidth:t,naturalHeight:r},m=Ae({},f);n.rotatable&&(f.rotate=c.rotate||0,m.rotate=0),n.scalable&&(f.scaleX=c.scaleX||1,f.scaleY=c.scaleY||1,m.scaleX=1,m.scaleY=1),i.imageData=f,i.initialImageData=m,e&&e()}))},renderImage:function(e){var t=this,i=this.image,n=this.imageData;if(Me(i,Ae({width:n.width,height:n.height,marginLeft:n.x,marginTop:n.y},qe(n))),e)if((this.viewing||this.moving||this.rotating||this.scaling||this.zooming)&&this.options.transition&&Ne(i,I)){var r=function(){t.imageRendering=!1,e()};this.imageRendering={abort:function(){$e(i,Z,r)}},He(i,Z,r,{once:!0})}else e()},resetImage:function(){if(this.viewing||this.viewed){var e=this.image;this.viewing&&this.viewing.abort(),e.parentNode.removeChild(e),this.image=null}}},et={bind:function(){var e=this.options,t=this.viewer,i=this.canvas,n=this.element.ownerDocument;He(t,L,this.onClick=this.click.bind(this)),He(t,P,this.onDragStart=this.dragstart.bind(this)),He(i,U,this.onPointerDown=this.pointerdown.bind(this)),He(n,W,this.onPointerMove=this.pointermove.bind(this)),He(n,G,this.onPointerUp=this.pointerup.bind(this)),He(n,$,this.onKeyDown=this.keydown.bind(this)),He(window,K,this.onResize=this.resize.bind(this)),e.zoomable&&e.zoomOnWheel&&He(t,J,this.onWheel=this.wheel.bind(this),{passive:!1,capture:!0}),e.toggleOnDblclick&&He(i,F,this.onDblclick=this.dblclick.bind(this))},unbind:function(){var e=this.options,t=this.viewer,i=this.canvas,n=this.element.ownerDocument;$e(t,L,this.onClick),$e(t,P,this.onDragStart),$e(i,U,this.onPointerDown),$e(n,W,this.onPointerMove),$e(n,G,this.onPointerUp),$e(n,$,this.onKeyDown),$e(window,K,this.onResize),e.zoomable&&e.zoomOnWheel&&$e(t,J,this.onWheel,{passive:!1,capture:!0}),e.toggleOnDblclick&&$e(i,F,this.onDblclick)}},tt={click:function(e){var t=this.options,i=this.imageData,n=e.target,r=Fe(n,ve);switch(r||"img"!==n.localName||"li"!==n.parentElement.localName||(n=n.parentElement,r=Fe(n,ve)),f&&e.isTrusted&&n===this.canvas&&clearTimeout(this.clickCanvasTimeout),r){case"mix":this.played?this.stop():t.inline?this.fulled?this.exit():this.full():this.hide();break;case"hide":this.pointerMoved||this.hide();break;case"view":this.view(Fe(n,"index"));break;case"zoom-in":this.zoom(.1,!0);break;case"zoom-out":this.zoom(-.1,!0);break;case"one-to-one":this.toggle();break;case"reset":this.reset();break;case"prev":this.prev(t.loop);break;case"play":this.play(t.fullscreen);break;case"next":this.next(t.loop);break;case"rotate-left":this.rotate(-90);break;case"rotate-right":this.rotate(90);break;case"flip-horizontal":this.scaleX(-i.scaleX||-1);break;case"flip-vertical":this.scaleY(-i.scaleY||-1);break;default:this.played&&this.stop()}},dblclick:function(e){e.preventDefault(),this.viewed&&e.target===this.image&&(f&&e.isTrusted&&clearTimeout(this.doubleClickImageTimeout),this.toggle(e.isTrusted?e:e.detail&&e.detail.originalEvent))},load:function(){var e=this;this.timeout&&(clearTimeout(this.timeout),this.timeout=!1);var t=this.element,i=this.options,n=this.image,r=this.index,o=this.viewerData;je(n,D),i.loading&&je(this.canvas,N),n.style.cssText="height:0;"+"margin-left:".concat(o.width/2,"px;")+"margin-top:".concat(o.height/2,"px;")+"max-width:none!important;position:relative;width:0;",this.initImage((function(){Re(n,C,i.movable),Re(n,I,i.transition),e.renderImage((function(){e.viewed=!0,e.viewing=!1,Te(i.viewed)&&He(t,oe,i.viewed,{once:!0}),Be(t,oe,{originalImage:e.images[r],index:r,image:n},{cancelable:!1})}))}))},loadImage:function(e){var t=e.target,i=t.parentNode,n=i.offsetWidth||30,r=i.offsetHeight||50,o=!!Fe(t,"filled");We(t,this.options,(function(e,i){var a=e/i,s=n,l=r;r*a>n?o?s=r*a:l=n/a:o?l=n/a:s=r*a,Me(t,Ae({width:s,height:l},qe({translateX:(n-s)/2,translateY:(r-l)/2})))}))},keydown:function(e){var t=this.options;if(t.keyboard){var i=e.keyCode||e.which||e.charCode;switch(i){case 13:this.viewer.contains(e.target)&&this.click(e);break}if(this.fulled)switch(i){case 27:this.played?this.stop():t.inline?this.fulled&&this.exit():this.hide();break;case 32:this.played&&this.stop();break;case 37:this.played&&this.playing?this.playing.prev():this.prev(t.loop);break;case 38:e.preventDefault(),this.zoom(t.zoomRatio,!0);break;case 39:this.played&&this.playing?this.playing.next():this.next(t.loop);break;case 40:e.preventDefault(),this.zoom(-t.zoomRatio,!0);break;case 48:case 49:e.ctrlKey&&(e.preventDefault(),this.toggle());break}}},dragstart:function(e){"img"===e.target.localName&&e.preventDefault()},pointerdown:function(e){var t=this.options,i=this.pointers,n=e.buttons,r=e.button;if(this.pointerMoved=!1,!(!this.viewed||this.showing||this.viewing||this.hiding||("mousedown"===e.type||"pointerdown"===e.type&&"mouse"===e.pointerType)&&(we(n)&&1!==n||we(r)&&0!==r||e.ctrlKey))){e.preventDefault(),e.changedTouches?ke(e.changedTouches,(function(e){i[e.identifier]=Ze(e)})):i[e.pointerId||0]=Ze(e);var o=!!t.movable&&g;t.zoomOnTouch&&t.zoomable&&Object.keys(i).length>1?o=b:t.slideOnTouch&&("touch"===e.pointerType||"touchstart"===e.type)&&this.isSwitchable()&&(o=y),!t.transition||o!==g&&o!==b||je(this.image,I),this.action=o}},pointermove:function(e){var t=this.pointers,i=this.action;this.viewed&&i&&(e.preventDefault(),this.pointerMoved=!0,e.changedTouches?ke(e.changedTouches,(function(e){Ae(t[e.identifier]||{},Ze(e,!0))})):Ae(t[e.pointerId||0]||{},Ze(e,!0)),this.change(e))},pointerup:function(e){var t,i=this,n=this.options,r=this.action,o=this.pointers;e.changedTouches?ke(e.changedTouches,(function(e){t=o[e.identifier],delete o[e.identifier]})):(t=o[e.pointerId||0],delete o[e.pointerId||0]),r&&(e.preventDefault(),!n.transition||r!==g&&r!==b||Ce(this.image,I),this.action=!1,f&&r!==b&&t&&Date.now()-t.timeStamp<500&&(clearTimeout(this.clickCanvasTimeout),clearTimeout(this.doubleClickImageTimeout),n.toggleOnDblclick&&this.viewed&&e.target===this.image?this.imageClicked?(this.imageClicked=!1,this.doubleClickImageTimeout=setTimeout((function(){Be(i.image,F,{originalEvent:e})}),50)):(this.imageClicked=!0,this.doubleClickImageTimeout=setTimeout((function(){i.imageClicked=!1}),500)):(this.imageClicked=!1,n.backdrop&&"static"!==n.backdrop&&e.target===this.canvas&&(this.clickCanvasTimeout=setTimeout((function(){Be(i.canvas,L,{originalEvent:e})}),50)))))},resize:function(){var e=this;if(this.isShown&&!this.hiding&&(this.fulled&&(this.close(),this.initBody(),this.open()),this.initContainer(),this.initViewer(),this.renderViewer(),this.renderList(),this.viewed&&this.initImage((function(){e.renderImage()})),this.played)){if(this.options.fullscreen&&this.fulled&&!(document.fullscreenElement||document.webkitFullscreenElement||document.mozFullScreenElement||document.msFullscreenElement))return void this.stop();ke(this.player.getElementsByTagName("img"),(function(t){He(t,H,e.loadImage.bind(e),{once:!0}),Be(t,H)}))}},wheel:function(e){var t=this;if(this.viewed&&(e.preventDefault(),!this.wheeling)){this.wheeling=!0,setTimeout((function(){t.wheeling=!1}),50);var i=Number(this.options.zoomRatio)||.1,n=1;e.deltaY?n=e.deltaY>0?1:-1:e.wheelDelta?n=-e.wheelDelta/120:e.detail&&(n=e.detail>0?1:-1),this.zoom(-n*i,!0,null,e)}}},it={show:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=this.element,i=this.options;if(i.inline||this.showing||this.isShown||this.showing)return this;if(!this.ready)return this.build(),this.ready&&this.show(e),this;if(Te(i.show)&&He(t,ee,i.show,{once:!0}),!1===Be(t,ee)||!this.ready)return this;this.hiding&&this.transitioning.abort(),this.showing=!0,this.open();var n=this.viewer;if(je(n,T),n.setAttribute("role","dialog"),n.setAttribute("aria-labelledby",this.title.id),n.setAttribute("aria-modal",!0),n.removeAttribute("aria-hidden"),i.transition&&!e){var r=this.shown.bind(this);this.transitioning={abort:function(){$e(n,Z,r),je(n,M)}},Ce(n,I),n.initialOffsetWidth=n.offsetWidth,He(n,Z,r,{once:!0}),Ce(n,M)}else Ce(n,M),this.shown();return this},hide:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],i=this.element,n=this.options;if(n.inline||this.hiding||!this.isShown&&!this.showing)return this;if(Te(n.hide)&&He(i,ie,n.hide,{once:!0}),!1===Be(i,ie))return this;this.showing&&this.transitioning.abort(),this.hiding=!0,this.played?this.stop():this.viewing&&this.viewing.abort();var r=this.viewer,o=this.image,a=function(){je(r,M),e.hidden()};if(n.transition&&!t){var s=function t(i){i&&i.target===r&&($e(r,Z,t),e.hidden())},l=function(){Ne(r,I)?(He(r,Z,s),je(r,M)):a()};this.transitioning={abort:function(){e.viewed&&Ne(o,I)?$e(o,Z,l):Ne(r,I)&&$e(r,Z,s)}},this.viewed&&Ne(o,I)?(He(o,Z,l,{once:!0}),this.zoomTo(0,!1,null,null,!0)):l()}else a();return this},view:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.options.initialViewIndex;if(t=Number(t)||0,this.hiding||this.played||t<0||t>=this.length||this.viewed&&t===this.index)return this;if(!this.isShown)return this.index=t,this.show();this.viewing&&this.viewing.abort();var i=this.element,n=this.options,r=this.title,o=this.canvas,a=this.items[t],s=a.querySelector("img"),l=Fe(s,"originalUrl"),c=s.getAttribute("alt"),u=document.createElement("img");if(ke(n.inheritedAttributes,(function(e){var t=s.getAttribute(e);null!==t&&u.setAttribute(e,t)})),u.src=l,u.alt=c,Te(n.view)&&He(i,re,n.view,{once:!0}),!1===Be(i,re,{originalImage:this.images[t],index:t,image:u})||!this.isShown||this.hiding||this.played)return this;var d=this.items[this.index];d&&(je(d,x),d.removeAttribute("aria-selected")),Ce(a,x),a.setAttribute("aria-selected",!0),n.focus&&a.focus(),this.image=u,this.viewed=!1,this.index=t,this.imageData={},Ce(u,D),n.loading&&Ce(o,N),o.innerHTML="",o.appendChild(u),this.renderList(),r.innerHTML="";var h,p,f=function(){var t=e.imageData,i=Array.isArray(n.title)?n.title[1]:n.title;r.innerHTML=De(Te(i)?i.call(e,u,t):"".concat(c," (").concat(t.naturalWidth," × ").concat(t.naturalHeight,")"))};return He(i,oe,f,{once:!0}),this.viewing={abort:function(){$e(i,oe,f),u.complete?e.imageRendering?e.imageRendering.abort():e.imageInitializing&&e.imageInitializing.abort():(u.src="",$e(u,H,h),e.timeout&&clearTimeout(e.timeout))}},u.complete?this.load():(He(u,H,h=function(){$e(u,B,p),e.load()},{once:!0}),He(u,B,p=function(){$e(u,H,h),e.timeout&&(clearTimeout(e.timeout),e.timeout=!1),je(u,D),n.loading&&je(e.canvas,N)},{once:!0}),this.timeout&&clearTimeout(this.timeout),this.timeout=setTimeout((function(){je(u,D),e.timeout=!1}),1e3)),this},prev:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=this.index-1;return t<0&&(t=e?this.length-1:0),this.view(t),this},next:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=this.length-1,i=this.index+1;return i>t&&(i=e?0:t),this.view(i),this},move:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,i=this.imageData;return this.moveTo(_e(e)?e:i.x+Number(e),_e(t)?t:i.y+Number(t)),this},moveTo:function(e){var t=this,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=this.element,o=this.options,a=this.imageData;if(e=Number(e),i=Number(i),this.viewed&&!this.played&&o.movable){var s=a.x,l=a.y,c=!1;if(we(e)?c=!0:e=s,we(i)?c=!0:i=l,c){if(Te(o.move)&&He(r,ae,o.move,{once:!0}),!1===Be(r,ae,{x:e,y:i,oldX:s,oldY:l,originalEvent:n}))return this;a.x=e,a.y=i,a.left=e,a.top=i,this.moving=!0,this.renderImage((function(){t.moving=!1,Te(o.moved)&&He(r,se,o.moved,{once:!0}),Be(r,se,{x:e,y:i,oldX:s,oldY:l,originalEvent:n},{cancelable:!1})}))}}return this},rotate:function(e){return this.rotateTo((this.imageData.rotate||0)+Number(e)),this},rotateTo:function(e){var t=this,i=this.element,n=this.options,r=this.imageData;if(e=Number(e),we(e)&&this.viewed&&!this.played&&n.rotatable){var o=r.rotate;if(Te(n.rotate)&&He(i,le,n.rotate,{once:!0}),!1===Be(i,le,{degree:e,oldDegree:o}))return this;r.rotate=e,this.rotating=!0,this.renderImage((function(){t.rotating=!1,Te(n.rotated)&&He(i,ce,n.rotated,{once:!0}),Be(i,ce,{degree:e,oldDegree:o},{cancelable:!1})}))}return this},scaleX:function(e){return this.scale(e,this.imageData.scaleY),this},scaleY:function(e){return this.scale(this.imageData.scaleX,e),this},scale:function(e){var t=this,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,n=this.element,r=this.options,o=this.imageData;if(e=Number(e),i=Number(i),this.viewed&&!this.played&&r.scalable){var a=o.scaleX,s=o.scaleY,l=!1;if(we(e)?l=!0:e=a,we(i)?l=!0:i=s,l){if(Te(r.scale)&&He(n,ue,r.scale,{once:!0}),!1===Be(n,ue,{scaleX:e,scaleY:i,oldScaleX:a,oldScaleY:s}))return this;o.scaleX=e,o.scaleY=i,this.scaling=!0,this.renderImage((function(){t.scaling=!1,Te(r.scaled)&&He(n,de,r.scaled,{once:!0}),Be(n,de,{scaleX:e,scaleY:i,oldScaleX:a,oldScaleY:s},{cancelable:!1})}))}}return this},zoom:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,r=this.imageData;return e=Number(e),e=e<0?1/(1-e):1+e,this.zoomTo(r.width*e/r.naturalWidth,t,i,n),this},zoomTo:function(e){var t=this,i=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,o=arguments.length>4&&void 0!==arguments[4]&&arguments[4],a=this.element,s=this.options,l=this.pointers,c=this.imageData,u=c.x,d=c.y,h=c.width,p=c.height,f=c.naturalWidth,m=c.naturalHeight;if(e=Math.max(0,e),we(e)&&this.viewed&&!this.played&&(o||s.zoomable)){if(!o){var v=Math.max(.01,s.minZoomRatio),g=Math.min(100,s.maxZoomRatio);e=Math.min(Math.max(e,v),g)}if(r)switch(r.type){case"wheel":s.zoomRatio>=.055&&e>.95&&e<1.05&&(e=1);break;case"pointermove":case"touchmove":case"mousemove":e>.99&&e<1.01&&(e=1);break}var y=f*e,b=m*e,x=y-h,w=b-p,_=c.ratio;if(Te(s.zoom)&&He(a,he,s.zoom,{once:!0}),!1===Be(a,he,{ratio:e,oldRatio:_,originalEvent:r}))return this;if(this.zooming=!0,r){var S=Ye(this.viewer),O=l&&Object.keys(l).length>0?Je(l):{pageX:r.pageX,pageY:r.pageY};c.x-=x*((O.pageX-S.left-u)/h),c.y-=w*((O.pageY-S.top-d)/p)}else Ee(n)&&we(n.x)&&we(n.y)?(c.x-=x*((n.x-u)/h),c.y-=w*((n.y-d)/p)):(c.x-=x/2,c.y-=w/2);c.left=c.x,c.top=c.y,c.width=y,c.height=b,c.oldRatio=_,c.ratio=e,this.renderImage((function(){t.zooming=!1,Te(s.zoomed)&&He(a,pe,s.zoomed,{once:!0}),Be(a,pe,{ratio:e,oldRatio:_,originalEvent:r},{cancelable:!1})})),i&&this.tooltip()}return this},play:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!this.isShown||this.played)return this;var i=this.element,n=this.options;if(Te(n.play)&&He(i,fe,n.play,{once:!0}),!1===Be(i,fe))return this;var r=this.player,o=this.loadImage.bind(this),a=[],s=0,l=0;if(this.played=!0,this.onLoadWhenPlay=o,t&&this.requestFullscreen(t),Ce(r,R),ke(this.items,(function(e,t){var i=e.querySelector("img"),c=document.createElement("img");c.src=Fe(i,"originalUrl"),c.alt=i.getAttribute("alt"),c.referrerPolicy=i.referrerPolicy,s+=1,Ce(c,_),Re(c,I,n.transition),Ne(e,x)&&(Ce(c,M),l=t),a.push(c),He(c,H,o,{once:!0}),r.appendChild(c)})),we(n.interval)&&n.interval>0){var c=function t(){clearTimeout(e.playing.timeout),je(a[l],M),l-=1,l=l>=0?l:s-1,Ce(a[l],M),e.playing.timeout=setTimeout(t,n.interval)},u=function t(){clearTimeout(e.playing.timeout),je(a[l],M),l+=1,l=l<s?l:0,Ce(a[l],M),e.playing.timeout=setTimeout(t,n.interval)};s>1&&(this.playing={prev:c,next:u,timeout:setTimeout(u,n.interval)})}return this},stop:function(){var e=this;if(!this.played)return this;var t=this.element,i=this.options;if(Te(i.stop)&&He(t,me,i.stop,{once:!0}),!1===Be(t,me))return this;var n=this.player;return clearTimeout(this.playing.timeout),this.playing=!1,this.played=!1,ke(n.getElementsByTagName("img"),(function(t){$e(t,H,e.onLoadWhenPlay)})),je(n,R),n.innerHTML="",this.exitFullscreen(),this},full:function(){var e=this,t=this.options,i=this.viewer,n=this.image,r=this.list;return!this.isShown||this.played||this.fulled||!t.inline||(this.fulled=!0,this.open(),Ce(this.button,E),t.transition&&(je(r,I),this.viewed&&je(n,I)),Ce(i,S),i.setAttribute("role","dialog"),i.setAttribute("aria-labelledby",this.title.id),i.setAttribute("aria-modal",!0),i.removeAttribute("style"),Me(i,{zIndex:t.zIndex}),t.focus&&this.enforceFocus(),this.initContainer(),this.viewerData=Ae({},this.containerData),this.renderList(),this.viewed&&this.initImage((function(){e.renderImage((function(){t.transition&&setTimeout((function(){Ce(n,I),Ce(r,I)}),0)}))}))),this},exit:function(){var e=this,t=this.options,i=this.viewer,n=this.image,r=this.list;return this.isShown&&!this.played&&this.fulled&&t.inline?(this.fulled=!1,this.close(),je(this.button,E),t.transition&&(je(r,I),this.viewed&&je(n,I)),t.focus&&this.clearEnforceFocus(),i.removeAttribute("role"),i.removeAttribute("aria-labelledby"),i.removeAttribute("aria-modal"),je(i,S),Me(i,{zIndex:t.zIndexInline}),this.viewerData=Ae({},this.parentData),this.renderViewer(),this.renderList(),this.viewed&&this.initImage((function(){e.renderImage((function(){t.transition&&setTimeout((function(){Ce(n,I),Ce(r,I)}),0)}))})),this):this},tooltip:function(){var e=this,t=this.options,i=this.tooltipBox,n=this.imageData;return this.viewed&&!this.played&&t.tooltip?(i.textContent="".concat(Math.round(100*n.ratio),"%"),this.tooltipping?clearTimeout(this.tooltipping):t.transition?(this.fading&&Be(i,Z),Ce(i,R),Ce(i,_),Ce(i,I),i.removeAttribute("aria-hidden"),i.initialOffsetWidth=i.offsetWidth,Ce(i,M)):(Ce(i,R),i.removeAttribute("aria-hidden")),this.tooltipping=setTimeout((function(){t.transition?(He(i,Z,(function(){je(i,R),je(i,_),je(i,I),i.setAttribute("aria-hidden",!0),e.fading=!1}),{once:!0}),je(i,M),e.fading=!0):(je(i,R),i.setAttribute("aria-hidden",!0)),e.tooltipping=!1}),1e3),this):this},toggle:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;return 1===this.imageData.ratio?this.zoomTo(this.imageData.oldRatio,!0,null,e):this.zoomTo(1,!0,null,e),this},reset:function(){return this.viewed&&!this.played&&(this.imageData=Ae({},this.initialImageData),this.renderImage()),this},update:function(){var e=this,t=this.element,i=this.options,n=this.isImg;if(n&&!t.parentNode)return this.destroy();var r=[];if(ke(n?[t]:t.querySelectorAll("img"),(function(t){Te(i.filter)?i.filter.call(e,t)&&r.push(t):e.getImageURL(t)&&r.push(t)})),!r.length)return this;if(this.images=r,this.length=r.length,this.ready){var o=[];if(ke(this.items,(function(e,t){var i=e.querySelector("img"),n=r[t];n&&i&&n.src===i.src&&n.alt===i.alt||o.push(t)})),Me(this.list,{width:"auto"}),this.initList(),this.isShown)if(this.length){if(this.viewed){var a=o.indexOf(this.index);if(a>=0)this.viewed=!1,this.view(Math.max(Math.min(this.index-a,this.length-1),0));else{var s=this.items[this.index];Ce(s,x),s.setAttribute("aria-selected",!0)}}}else this.image=null,this.viewed=!1,this.index=0,this.imageData={},this.canvas.innerHTML="",this.title.innerHTML=""}else this.build();return this},destroy:function(){var e=this.element,t=this.options;return e[v]?(this.destroyed=!0,this.ready?(this.played&&this.stop(),t.inline?(this.fulled&&this.exit(),this.unbind()):this.isShown?(this.viewing&&(this.imageRendering?this.imageRendering.abort():this.imageInitializing&&this.imageInitializing.abort()),this.hiding&&this.transitioning.abort(),this.hidden()):this.showing&&(this.transitioning.abort(),this.hidden()),this.ready=!1,this.viewer.parentNode.removeChild(this.viewer)):t.inline&&(this.delaying?this.delaying.abort():this.initializing&&this.initializing.abort()),t.inline||$e(e,L,this.onStart),e[v]=void 0,this):this}},nt={getImageURL:function(e){var t=this.options.url;return t=be(t)?e.getAttribute(t):Te(t)?t.call(this,e):"",t},enforceFocus:function(){var e=this;this.clearEnforceFocus(),He(document,V,this.onFocusin=function(t){var i=e.viewer,n=t.target;if(n!==document&&n!==i&&!i.contains(n)){while(n){if(null!==n.getAttribute("tabindex")||"true"===n.getAttribute("aria-modal"))return;n=n.parentElement}i.focus()}})},clearEnforceFocus:function(){this.onFocusin&&($e(document,V,this.onFocusin),this.onFocusin=null)},open:function(){var e=this.body;Ce(e,j),this.scrollbarWidth>0&&(e.style.paddingRight="".concat(this.scrollbarWidth+(parseFloat(this.initialBodyComputedPaddingRight)||0),"px"))},close:function(){var e=this.body;je(e,j),this.scrollbarWidth>0&&(e.style.paddingRight=this.initialBodyPaddingRight)},shown:function(){var e=this.element,t=this.options,i=this.viewer;this.fulled=!0,this.isShown=!0,this.render(),this.bind(),this.showing=!1,t.focus&&(i.focus(),this.enforceFocus()),Te(t.shown)&&He(e,te,t.shown,{once:!0}),!1!==Be(e,te)&&this.ready&&this.isShown&&!this.hiding&&this.view(this.index)},hidden:function(){var e=this.element,t=this.options,i=this.viewer;t.fucus&&this.clearEnforceFocus(),this.fulled=!1,this.viewed=!1,this.isShown=!1,this.close(),this.unbind(),Ce(i,T),i.removeAttribute("role"),i.removeAttribute("aria-labelledby"),i.removeAttribute("aria-modal"),i.setAttribute("aria-hidden",!0),this.resetList(),this.resetImage(),this.hiding=!1,this.destroyed||(Te(t.hidden)&&He(e,ne,t.hidden,{once:!0}),Be(e,ne,null,{cancelable:!1}))},requestFullscreen:function(e){var t=this.element.ownerDocument;if(this.fulled&&!(t.fullscreenElement||t.webkitFullscreenElement||t.mozFullScreenElement||t.msFullscreenElement)){var i=t.documentElement;i.requestFullscreen?Ee(e)?i.requestFullscreen(e):i.requestFullscreen():i.webkitRequestFullscreen?i.webkitRequestFullscreen(Element.ALLOW_KEYBOARD_INPUT):i.mozRequestFullScreen?i.mozRequestFullScreen():i.msRequestFullscreen&&i.msRequestFullscreen()}},exitFullscreen:function(){var e=this.element.ownerDocument;this.fulled&&(e.fullscreenElement||e.webkitFullscreenElement||e.mozFullScreenElement||e.msFullscreenElement)&&(e.exitFullscreen?e.exitFullscreen():e.webkitExitFullscreen?e.webkitExitFullscreen():e.mozCancelFullScreen?e.mozCancelFullScreen():e.msExitFullscreen&&e.msExitFullscreen())},change:function(e){var t=this.options,i=this.pointers,n=i[Object.keys(i)[0]];if(n){var r=n.endX-n.startX,o=n.endY-n.startY;switch(this.action){case g:this.move(r,o,e);break;case b:this.zoom(Ke(i),!1,null,e);break;case y:this.action="switched";var a=Math.abs(r);a>1&&a>Math.abs(o)&&(this.pointers={},r>1?this.prev(t.loop):r<-1&&this.next(t.loop));break}ke(i,(function(e){e.startX=e.endX,e.startY=e.endY}))}},isSwitchable:function(){var e=this.imageData,t=this.viewerData;return this.length>1&&e.x>=0&&e.y>=0&&e.width<=t.width&&e.height<=t.height}},rt=p.Viewer,ot=function(e){return function(){return e+=1,e}}(-1),at=function(){function e(t){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(n(this,e),!t||1!==t.nodeType)throw new Error("The first argument is required and must be an element.");this.element=t,this.options=Ae({},u,Ee(i)&&i),this.action=!1,this.fading=!1,this.fulled=!1,this.hiding=!1,this.imageClicked=!1,this.imageData={},this.index=this.options.initialViewIndex,this.isImg=!1,this.isShown=!1,this.length=0,this.moving=!1,this.played=!1,this.playing=!1,this.pointers={},this.ready=!1,this.rotating=!1,this.scaling=!1,this.showing=!1,this.timeout=!1,this.tooltipping=!1,this.viewed=!1,this.viewing=!1,this.wheeling=!1,this.zooming=!1,this.pointerMoved=!1,this.id=ot(),this.init()}return a(e,[{key:"init",value:function(){var e=this,t=this.element,i=this.options;if(!t[v]){t[v]=this,i.focus&&!i.keyboard&&(i.focus=!1);var n="img"===t.localName,r=[];if(ke(n?[t]:t.querySelectorAll("img"),(function(t){Te(i.filter)?i.filter.call(e,t)&&r.push(t):e.getImageURL(t)&&r.push(t)})),this.isImg=n,this.length=r.length,this.images=r,this.initBody(),_e(document.createElement(v).style.transition)&&(i.transition=!1),i.inline){var o=0,a=function(){var t;(o+=1,o===e.length)&&(e.initializing=!1,e.delaying={abort:function(){clearTimeout(t)}},t=setTimeout((function(){e.delaying=!1,e.build()}),0))};this.initializing={abort:function(){ke(r,(function(e){e.complete||($e(e,H,a),$e(e,B,a))}))}},ke(r,(function(e){var t,i;e.complete?a():(He(e,H,t=function(){$e(e,B,i),a()},{once:!0}),He(e,B,i=function(){$e(e,H,t),a()},{once:!0}))}))}else He(t,L,this.onStart=function(t){var n=t.target;"img"!==n.localName||Te(i.filter)&&!i.filter.call(e,n)||e.view(e.images.indexOf(n))})}}},{key:"build",value:function(){if(!this.ready){var e=this.element,t=this.options,i=e.parentNode,n=document.createElement("div");n.innerHTML=d;var r=n.querySelector(".".concat(v,"-container")),o=r.querySelector(".".concat(v,"-title")),a=r.querySelector(".".concat(v,"-toolbar")),s=r.querySelector(".".concat(v,"-navbar")),l=r.querySelector(".".concat(v,"-button")),c=r.querySelector(".".concat(v,"-canvas"));if(this.parent=i,this.viewer=r,this.title=o,this.toolbar=a,this.navbar=s,this.button=l,this.canvas=c,this.footer=r.querySelector(".".concat(v,"-footer")),this.tooltipBox=r.querySelector(".".concat(v,"-tooltip")),this.player=r.querySelector(".".concat(v,"-player")),this.list=r.querySelector(".".concat(v,"-list")),r.id="".concat(v).concat(this.id),o.id="".concat(v,"Title").concat(this.id),Ce(o,t.title?Ge(Array.isArray(t.title)?t.title[0]:t.title):T),Ce(s,t.navbar?Ge(t.navbar):T),Re(l,T,!t.button),t.keyboard&&l.setAttribute("tabindex",0),t.backdrop&&(Ce(r,"".concat(v,"-backdrop")),t.inline||"static"===t.backdrop||Pe(c,ve,"hide")),be(t.className)&&t.className&&t.className.split(ge).forEach((function(e){Ce(r,e)})),t.toolbar){var u=document.createElement("ul"),h=Ee(t.toolbar),p=ye.slice(0,3),f=ye.slice(7,9),m=ye.slice(9);h||Ce(a,Ge(t.toolbar)),ke(h?t.toolbar:ye,(function(e,i){var n=h&&Ee(e),r=h?Le(i):e,o=n&&!_e(e.show)?e.show:e;if(o&&(t.zoomable||-1===p.indexOf(r))&&(t.rotatable||-1===f.indexOf(r))&&(t.scalable||-1===m.indexOf(r))){var a=n&&!_e(e.size)?e.size:e,s=n&&!_e(e.click)?e.click:e,l=document.createElement("li");t.keyboard&&l.setAttribute("tabindex",0),l.setAttribute("role","button"),Ce(l,"".concat(v,"-").concat(r)),Te(s)||Pe(l,ve,r),we(o)&&Ce(l,Ge(o)),-1!==["small","large"].indexOf(a)?Ce(l,"".concat(v,"-").concat(a)):"play"===r&&Ce(l,"".concat(v,"-large")),Te(s)&&He(l,L,s),u.appendChild(l)}})),a.appendChild(u)}else Ce(a,T);if(!t.rotatable){var g=a.querySelectorAll('li[class*="rotate"]');Ce(g,D),ke(g,(function(e){a.appendChild(e)}))}if(t.inline)Ce(l,O),Me(r,{zIndex:t.zIndexInline}),"static"===window.getComputedStyle(i).position&&Me(i,{position:"relative"}),i.insertBefore(r,e.nextSibling);else{Ce(l,w),Ce(r,S),Ce(r,_),Ce(r,T),Me(r,{zIndex:t.zIndex});var y=t.container;be(y)&&(y=e.ownerDocument.querySelector(y)),y||(y=this.body),y.appendChild(r)}t.inline&&(this.render(),this.bind(),this.isShown=!0),this.ready=!0,Te(t.ready)&&He(e,Q,t.ready,{once:!0}),!1!==Be(e,Q)?this.ready&&t.inline&&this.view(this.index):this.ready=!1}}}],[{key:"noConflict",value:function(){return window.Viewer=rt,e}},{key:"setDefaults",value:function(e){Ae(u,Ee(e)&&e)}}]),e}();return Ae(at.prototype,Qe,et,tt,it,nt),at}))},75269:function(e,t,i){var n,r,o=i(57847)["default"];(function(){var i={expires:"1d",path:"; path=/",domain:"",secure:"",sameSite:"; SameSite=Lax"},a={install:function(e,t){t&&this.config(t.expires,t.path,t.domain,t.secure,t.sameSite),e.prototype&&(e.prototype.$cookies=this),e.config&&e.config.globalProperties&&(e.config.globalProperties.$cookies=this,e.provide("$cookies",this)),e.$cookies=this},config:function(e,t,n,r,o){i.expires=e||"1d",i.path=t?"; path="+t:"; path=/",i.domain=n?"; domain="+n:"",i.secure=r?"; Secure":"",i.sameSite=o?"; SameSite="+o:"; SameSite=Lax"},get:function(e){var t=decodeURIComponent(document.cookie.replace(new RegExp("(?:(?:^|.*;)\\s*"+encodeURIComponent(e).replace(/[\-\.\+\*]/g,"\\$&")+"\\s*\\=\\s*([^;]*).*$)|^.*$"),"$1"))||null;if(t&&"{"===t.substring(0,1)&&"}"===t.substring(t.length-1,t.length))try{t=JSON.parse(t)}catch(i){return t}return t},set:function(e,t,n,r,o,a,s){if(!e)throw new Error("Cookie name is not found in the first argument.");if(/^(?:expires|max\-age|path|domain|secure|SameSite)$/i.test(e))throw new Error('Cookie name illegality. Cannot be set to ["expires","max-age","path","domain","secure","SameSite"]\t current key name: '+e);t&&t.constructor===Object&&(t=JSON.stringify(t));var l="";if(n=void 0==n?i.expires:n,n&&0!=n)switch(n.constructor){case Number:l=n===1/0||-1===n?"; expires=Fri, 31 Dec 9999 23:59:59 GMT":"; max-age="+n;break;case String:if(/^(?:\d+(y|m|d|h|min|s))$/i.test(n)){var c=n.replace(/^(\d+)(?:y|m|d|h|min|s)$/i,"$1");switch(n.replace(/^(?:\d+)(y|m|d|h|min|s)$/i,"$1").toLowerCase()){case"m":l="; max-age="+2592e3*+c;break;case"d":l="; max-age="+86400*+c;break;case"h":l="; max-age="+3600*+c;break;case"min":l="; max-age="+60*+c;break;case"s":l="; max-age="+c;break;case"y":l="; max-age="+31104e3*+c;break;default:new Error('unknown exception of "set operation"')}}else l="; expires="+n;break;case Date:l="; expires="+n.toUTCString();break}return document.cookie=encodeURIComponent(e)+"="+encodeURIComponent(t)+l+(o?"; domain="+o:i.domain)+(r?"; path="+r:i.path)+(void 0==a?i.secure:a?"; Secure":"")+(void 0==s?i.sameSite:s?"; SameSite="+s:""),this},remove:function(e,t,n){return!(!e||!this.isKey(e))&&(document.cookie=encodeURIComponent(e)+"=; expires=Thu, 01 Jan 1970 00:00:00 GMT"+(n?"; domain="+n:i.domain)+(t?"; path="+t:i.path)+"; SameSite=Lax",!0)},isKey:function(e){return new RegExp("(?:^|;\\s*)"+encodeURIComponent(e).replace(/[\-\.\+\*]/g,"\\$&")+"\\s*\\=").test(document.cookie)},keys:function(){if(!document.cookie)return[];for(var e=document.cookie.replace(/((?:^|\s*;)[^\=]+)(?=;|$)|^\s*|\s*(?:\=[^;]*)?(?:\1|$)/g,"").split(/\s*(?:\=[^;]*)?;\s*/),t=0;t<e.length;t++)e[t]=decodeURIComponent(e[t]);return e}};"object"==o(t)?e.exports=a:(n=[],r=function(){return a}.apply(t,n),void 0===r||(e.exports=r)),"undefined"!==typeof window&&(window.$cookies=a)})()},43535:function(e,t){"use strict";t.Z={install:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=t.name||"ref";e.directive(i,{bind:function(e,t,i){t.value(i.componentInstance||e,i.key)},update:function(e,t,n,r){if(r.data&&r.data.directives){var o=r.data.directives.find((function(e){var t=e.name;return t===i}));if(o&&o.value!==t.value)return o&&o.value(null,r.key),void t.value(n.componentInstance||e,n.key)}n.componentInstance===r.componentInstance&&n.elm===r.elm||t.value(n.componentInstance||e,n.key)},unbind:function(e,t,i){t.value(null,i.key)}})}}},55137:function(e,t,i){var n,r,o,a=i(57847)["default"];i(32564),
/*!
 * vue-virtual-scroll-list v2.3.4
 * open source under the MIT license
 * https://github.com/tangbc/vue-virtual-scroll-list#readme
 */
function(s,l){"object"===a(t)?e.exports=l(i(3032)):(r=[i(3032)],n=l,o="function"===typeof n?n.apply(t,r):n,void 0===o||(e.exports=o))}(0,(function(e){"use strict";function t(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function n(e,t,n){return t&&i(e.prototype,t),n&&i(e,n),e}function r(e,t,i){return t in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}function o(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,n)}return i}function a(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?o(Object(i),!0).forEach((function(t){r(e,t,i[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):o(Object(i)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}function s(e){return l(e)||c(e)||u(e)||h()}function l(e){if(Array.isArray(e))return d(e)}function c(e){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}function u(e,t){if(e){if("string"===typeof e)return d(e,t);var i=Object.prototype.toString.call(e).slice(8,-1);return"Object"===i&&e.constructor&&(i=e.constructor.name),"Map"===i||"Set"===i?Array.from(i):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?d(e,t):void 0}}function d(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,n=new Array(t);i<t;i++)n[i]=e[i];return n}function h(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}e=e&&Object.prototype.hasOwnProperty.call(e,"default")?e["default"]:e;var p={FRONT:"FRONT",BEHIND:"BEHIND"},f={INIT:"INIT",FIXED:"FIXED",DYNAMIC:"DYNAMIC"},m=0,v=function(){function e(i,n){t(this,e),this.init(i,n)}return n(e,[{key:"init",value:function(e,t){this.param=e,this.callUpdate=t,this.sizes=new Map,this.firstRangeTotalSize=0,this.firstRangeAverageSize=0,this.lastCalcIndex=0,this.fixedSizeValue=0,this.calcType=f.INIT,this.offset=0,this.direction="",this.range=Object.create(null),e&&this.checkRange(0,e.keeps-1)}},{key:"destroy",value:function(){this.init(null,null)}},{key:"getRange",value:function(){var e=Object.create(null);return e.start=this.range.start,e.end=this.range.end,e.padFront=this.range.padFront,e.padBehind=this.range.padBehind,e}},{key:"isBehind",value:function(){return this.direction===p.BEHIND}},{key:"isFront",value:function(){return this.direction===p.FRONT}},{key:"getOffset",value:function(e){return(e<1?0:this.getIndexOffset(e))+this.param.slotHeaderSize}},{key:"updateParam",value:function(e,t){var i=this;this.param&&e in this.param&&("uniqueIds"===e&&this.sizes.forEach((function(e,n){t.includes(n)||i.sizes["delete"](n)})),this.param[e]=t)}},{key:"saveSize",value:function(e,t){this.sizes.set(e,t),this.calcType===f.INIT?(this.fixedSizeValue=t,this.calcType=f.FIXED):this.calcType===f.FIXED&&this.fixedSizeValue!==t&&(this.calcType=f.DYNAMIC,delete this.fixedSizeValue),this.calcType!==f.FIXED&&"undefined"!==typeof this.firstRangeTotalSize&&(this.sizes.size<Math.min(this.param.keeps,this.param.uniqueIds.length)?(this.firstRangeTotalSize=s(this.sizes.values()).reduce((function(e,t){return e+t}),0),this.firstRangeAverageSize=Math.round(this.firstRangeTotalSize/this.sizes.size)):delete this.firstRangeTotalSize)}},{key:"handleDataSourcesChange",value:function(){var e=this.range.start;this.isFront()?e-=m:this.isBehind()&&(e+=m),e=Math.max(e,0),this.updateRange(this.range.start,this.getEndByStart(e))}},{key:"handleSlotSizeChange",value:function(){this.handleDataSourcesChange()}},{key:"handleScroll",value:function(e){this.direction=e<this.offset?p.FRONT:p.BEHIND,this.offset=e,this.param&&(this.direction===p.FRONT?this.handleFront():this.direction===p.BEHIND&&this.handleBehind())}},{key:"handleFront",value:function(){var e=this.getScrollOvers();if(!(e>this.range.start)){var t=Math.max(e-this.param.buffer,0);this.checkRange(t,this.getEndByStart(t))}}},{key:"handleBehind",value:function(){var e=this.getScrollOvers();e<this.range.start+this.param.buffer||this.checkRange(e,this.getEndByStart(e))}},{key:"getScrollOvers",value:function(){var e=this.offset-this.param.slotHeaderSize;if(e<=0)return 0;if(this.isFixedType())return Math.floor(e/this.fixedSizeValue);var t=0,i=0,n=0,r=this.param.uniqueIds.length;while(t<=r){if(i=t+Math.floor((r-t)/2),n=this.getIndexOffset(i),n===e)return i;n<e?t=i+1:n>e&&(r=i-1)}return t>0?--t:0}},{key:"getIndexOffset",value:function(e){if(!e)return 0;for(var t=0,i=0,n=0;n<e;n++)i=this.sizes.get(this.param.uniqueIds[n]),t+="number"===typeof i?i:this.getEstimateSize();return this.lastCalcIndex=Math.max(this.lastCalcIndex,e-1),this.lastCalcIndex=Math.min(this.lastCalcIndex,this.getLastIndex()),t}},{key:"isFixedType",value:function(){return this.calcType===f.FIXED}},{key:"getLastIndex",value:function(){return this.param.uniqueIds.length-1}},{key:"checkRange",value:function(e,t){var i=this.param.keeps,n=this.param.uniqueIds.length;n<=i?(e=0,t=this.getLastIndex()):t-e<i-1&&(e=t-i+1),this.range.start!==e&&this.updateRange(e,t)}},{key:"updateRange",value:function(e,t){this.range.start=e,this.range.end=t,this.range.padFront=this.getPadFront(),this.range.padBehind=this.getPadBehind(),this.callUpdate(this.getRange())}},{key:"getEndByStart",value:function(e){var t=e+this.param.keeps-1,i=Math.min(t,this.getLastIndex());return i}},{key:"getPadFront",value:function(){return this.isFixedType()?this.fixedSizeValue*this.range.start:this.getIndexOffset(this.range.start)}},{key:"getPadBehind",value:function(){var e=this.range.end,t=this.getLastIndex();return this.isFixedType()?(t-e)*this.fixedSizeValue:this.lastCalcIndex===t?this.getIndexOffset(t)-this.getIndexOffset(e):(t-e)*this.getEstimateSize()}},{key:"getEstimateSize",value:function(){return this.isFixedType()?this.fixedSizeValue:this.firstRangeAverageSize||this.param.estimateSize}}]),e}(),g={dataKey:{type:[String,Function],required:!0},dataSources:{type:Array,required:!0},dataComponent:{type:[Object,Function],required:!0},keeps:{type:Number,default:30},extraProps:{type:Object},estimateSize:{type:Number,default:50},direction:{type:String,default:"vertical"},start:{type:Number,default:0},offset:{type:Number,default:0},topThreshold:{type:Number,default:0},bottomThreshold:{type:Number,default:0},pageMode:{type:Boolean,default:!1},rootTag:{type:String,default:"div"},wrapTag:{type:String,default:"div"},wrapClass:{type:String,default:""},wrapStyle:{type:Object},itemTag:{type:String,default:"div"},itemClass:{type:String,default:""},itemClassAdd:{type:Function},itemStyle:{type:Object},headerTag:{type:String,default:"div"},headerClass:{type:String,default:""},headerStyle:{type:Object},footerTag:{type:String,default:"div"},footerClass:{type:String,default:""},footerStyle:{type:Object},itemScopedSlots:{type:Object}},y={index:{type:Number},event:{type:String},tag:{type:String},horizontal:{type:Boolean},source:{type:Object},component:{type:[Object,Function]},slotComponent:{type:Function},uniqueKey:{type:[String,Number]},extraProps:{type:Object},scopedSlots:{type:Object}},b={event:{type:String},uniqueKey:{type:String},tag:{type:String},horizontal:{type:Boolean}},x={created:function(){this.shapeKey=this.horizontal?"offsetWidth":"offsetHeight"},mounted:function(){var e=this;"undefined"!==typeof ResizeObserver&&(this.resizeObserver=new ResizeObserver((function(){e.dispatchSizeChange()})),this.resizeObserver.observe(this.$el))},updated:function(){this.dispatchSizeChange()},beforeDestroy:function(){this.resizeObserver&&(this.resizeObserver.disconnect(),this.resizeObserver=null)},methods:{getCurrentSize:function(){return this.$el?this.$el[this.shapeKey]:0},dispatchSizeChange:function(){this.$parent.$emit(this.event,this.uniqueKey,this.getCurrentSize(),this.hasInitial)}}},w=e.component("virtual-list-item",{mixins:[x],props:y,render:function(e){var t=this.tag,i=this.component,n=this.extraProps,r=void 0===n?{}:n,o=this.index,s=this.source,l=this.scopedSlots,c=void 0===l?{}:l,u=this.uniqueKey,d=this.slotComponent,h=a({},r,{source:s,index:o});return e(t,{key:u,attrs:{role:"listitem"}},[d?e("div",d({item:s,index:o,scope:h})):e(i,{props:h,scopedSlots:c})])}}),_=e.component("virtual-list-slot",{mixins:[x],props:b,render:function(e){var t=this.tag,i=this.uniqueKey;return e(t,{key:i,attrs:{role:i}},this.$slots["default"])}}),S={ITEM:"item_resize",SLOT:"slot_resize"},O={HEADER:"thead",FOOTER:"tfoot"},E=e.component("virtual-list",{props:g,data:function(){return{range:null}},watch:{"dataSources.length":function(){this.virtual.updateParam("uniqueIds",this.getUniqueIdFromDataSources()),this.virtual.handleDataSourcesChange()},keeps:function(e){this.virtual.updateParam("keeps",e),this.virtual.handleSlotSizeChange()},start:function(e){this.scrollToIndex(e)},offset:function(e){this.scrollToOffset(e)}},created:function(){this.isHorizontal="horizontal"===this.direction,this.directionKey=this.isHorizontal?"scrollLeft":"scrollTop",this.installVirtual(),this.$on(S.ITEM,this.onItemResized),(this.$slots.header||this.$slots.footer)&&this.$on(S.SLOT,this.onSlotResized)},activated:function(){this.scrollToOffset(this.virtual.offset),this.pageMode&&document.addEventListener("scroll",this.onScroll,{passive:!1})},deactivated:function(){this.pageMode&&document.removeEventListener("scroll",this.onScroll)},mounted:function(){this.start?this.scrollToIndex(this.start):this.offset&&this.scrollToOffset(this.offset),this.pageMode&&(this.updatePageModeFront(),document.addEventListener("scroll",this.onScroll,{passive:!1}))},beforeDestroy:function(){this.virtual.destroy(),this.pageMode&&document.removeEventListener("scroll",this.onScroll)},methods:{getSize:function(e){return this.virtual.sizes.get(e)},getSizes:function(){return this.virtual.sizes.size},getOffset:function(){if(this.pageMode)return document.documentElement[this.directionKey]||document.body[this.directionKey];var e=this.$refs.root;return e?Math.ceil(e[this.directionKey]):0},getClientSize:function(){var e=this.isHorizontal?"clientWidth":"clientHeight";if(this.pageMode)return document.documentElement[e]||document.body[e];var t=this.$refs.root;return t?Math.ceil(t[e]):0},getScrollSize:function(){var e=this.isHorizontal?"scrollWidth":"scrollHeight";if(this.pageMode)return document.documentElement[e]||document.body[e];var t=this.$refs.root;return t?Math.ceil(t[e]):0},scrollToOffset:function(e){if(this.pageMode)document.body[this.directionKey]=e,document.documentElement[this.directionKey]=e;else{var t=this.$refs.root;t&&(t[this.directionKey]=e)}},scrollToIndex:function(e){if(e>=this.dataSources.length-1)this.scrollToBottom();else{var t=this.virtual.getOffset(e);this.scrollToOffset(t)}},scrollToBottom:function(){var e=this,t=this.$refs.shepherd;if(t){var i=t[this.isHorizontal?"offsetLeft":"offsetTop"];this.scrollToOffset(i),setTimeout((function(){e.getOffset()+e.getClientSize()+1<e.getScrollSize()&&e.scrollToBottom()}),3)}},updatePageModeFront:function(){var e=this.$refs.root;if(e){var t=e.getBoundingClientRect(),i=e.ownerDocument.defaultView,n=this.isHorizontal?t.left+i.pageXOffset:t.top+i.pageYOffset;this.virtual.updateParam("slotHeaderSize",n)}},reset:function(){this.virtual.destroy(),this.scrollToOffset(0),this.installVirtual()},installVirtual:function(){this.virtual=new v({slotHeaderSize:0,slotFooterSize:0,keeps:this.keeps,estimateSize:this.estimateSize,buffer:Math.round(this.keeps/3),uniqueIds:this.getUniqueIdFromDataSources()},this.onRangeChanged),this.range=this.virtual.getRange()},getUniqueIdFromDataSources:function(){var e=this.dataKey;return this.dataSources.map((function(t){return"function"===typeof e?e(t):t[e]}))},onItemResized:function(e,t){this.virtual.saveSize(e,t),this.$emit("resized",e,t)},onSlotResized:function(e,t,i){e===O.HEADER?this.virtual.updateParam("slotHeaderSize",t):e===O.FOOTER&&this.virtual.updateParam("slotFooterSize",t),i&&this.virtual.handleSlotSizeChange()},onRangeChanged:function(e){this.range=e},onScroll:function(e){var t=this.getOffset(),i=this.getClientSize(),n=this.getScrollSize();t<0||t+i>n+1||!n||(this.virtual.handleScroll(t),this.emitEvent(t,i,n,e))},emitEvent:function(e,t,i,n){this.$emit("scroll",n,this.virtual.getRange()),this.virtual.isFront()&&this.dataSources.length&&e-this.topThreshold<=0?this.$emit("totop"):this.virtual.isBehind()&&e+t+this.bottomThreshold>=i&&this.$emit("tobottom")},getRenderSlots:function(e){for(var t=[],i=this.range,n=i.start,r=i.end,o=this.dataSources,a=this.dataKey,s=this.itemClass,l=this.itemTag,c=this.itemStyle,u=this.isHorizontal,d=this.extraProps,h=this.dataComponent,p=this.itemScopedSlots,f=this.$scopedSlots&&this.$scopedSlots.item,m=n;m<=r;m++){var v=o[m];if(v){var g="function"===typeof a?a(v):v[a];"string"!==typeof g&&"number"!==typeof g||t.push(e(w,{props:{index:m,tag:l,event:S.ITEM,horizontal:u,uniqueKey:g,source:v,extraProps:d,component:h,slotComponent:f,scopedSlots:p},style:c,class:"".concat(s).concat(this.itemClassAdd?" "+this.itemClassAdd(m):"")}))}}return t}},render:function(e){var t=this.$slots,i=t.header,n=t.footer,r=this.range,o=r.padFront,a=r.padBehind,s=this.isHorizontal,l=this.pageMode,c=this.rootTag,u=this.wrapTag,d=this.wrapClass,h=this.wrapStyle,p=this.headerTag,f=this.headerClass,m=this.headerStyle,v=this.footerTag,g=this.footerClass,y=this.footerStyle,b={padding:s?"0px ".concat(a,"px 0px ").concat(o,"px"):"".concat(o,"px 0px ").concat(a,"px")},x=h?Object.assign({},h,b):b;return e(c,{ref:"root",on:{"&scroll":!l&&this.onScroll}},[i?e(_,{class:f,style:m,props:{tag:p,event:S.SLOT,uniqueKey:O.HEADER}},i):null,e(u,{class:d,attrs:{role:"group"},style:x},this.getRenderSlots(e)),n?e(_,{class:g,style:y,props:{tag:v,event:S.SLOT,uniqueKey:O.FOOTER}},n):null,e("div",{ref:"shepherd",style:{width:s?"0px":"100%",height:s?"100%":"0px"}})])}});return E}))},86458:function(e,t,i){"use strict";i.d(t,{EK:function(){return ee}});i(32564);function n(){var e=window.navigator.userAgent,t=e.indexOf("MSIE ");if(t>0)return parseInt(e.substring(t+5,e.indexOf(".",t)),10);var i=e.indexOf("Trident/");if(i>0){var n=e.indexOf("rv:");return parseInt(e.substring(n+3,e.indexOf(".",n)),10)}var r=e.indexOf("Edge/");return r>0?parseInt(e.substring(r+5,e.indexOf(".",r)),10):-1}var r=void 0;function o(){o.init||(o.init=!0,r=-1!==n())}var a={render:function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"resize-observer",attrs:{tabindex:"-1"}})},staticRenderFns:[],_scopeId:"data-v-b329ee4c",name:"resize-observer",methods:{compareAndNotify:function(){this._w===this.$el.offsetWidth&&this._h===this.$el.offsetHeight||(this._w=this.$el.offsetWidth,this._h=this.$el.offsetHeight,this.$emit("notify"))},addResizeHandlers:function(){this._resizeObject.contentDocument.defaultView.addEventListener("resize",this.compareAndNotify),this.compareAndNotify()},removeResizeHandlers:function(){this._resizeObject&&this._resizeObject.onload&&(!r&&this._resizeObject.contentDocument&&this._resizeObject.contentDocument.defaultView.removeEventListener("resize",this.compareAndNotify),delete this._resizeObject.onload)}},mounted:function(){var e=this;o(),this.$nextTick((function(){e._w=e.$el.offsetWidth,e._h=e.$el.offsetHeight}));var t=document.createElement("object");this._resizeObject=t,t.setAttribute("aria-hidden","true"),t.setAttribute("tabindex",-1),t.onload=this.addResizeHandlers,t.type="text/html",r&&this.$el.appendChild(t),t.data="about:blank",r||this.$el.appendChild(t)},beforeDestroy:function(){this.removeResizeHandlers()}};function s(e){e.component("resize-observer",a),e.component("ResizeObserver",a)}var l={version:"0.4.5",install:s},c=null;"undefined"!==typeof window?c=window.Vue:"undefined"!==typeof i.g&&(c=i.g.Vue),c&&c.use(l);var u=i(3336);function d(e){return d="function"===typeof Symbol&&"symbol"===(0,u.Z)(Symbol.iterator)?function(e){return(0,u.Z)(e)}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":(0,u.Z)(e)},d(e)}function h(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function p(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function f(e,t,i){return t&&p(e.prototype,t),i&&p(e,i),e}function m(e){return v(e)||g(e)||y()}function v(e){if(Array.isArray(e)){for(var t=0,i=new Array(e.length);t<e.length;t++)i[t]=e[t];return i}}function g(e){if(Symbol.iterator in Object(e)||"[object Arguments]"===Object.prototype.toString.call(e))return Array.from(e)}function y(){throw new TypeError("Invalid attempt to spread non-iterable instance")}function b(e){var t;return t="function"===typeof e?{callback:e}:e,t}function x(e,t){var i,n,r,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},a=function(a){for(var s=arguments.length,l=new Array(s>1?s-1:0),c=1;c<s;c++)l[c-1]=arguments[c];if(r=l,!i||a!==n){var u=o.leading;"function"===typeof u&&(u=u(a,n)),i&&a===n||!u||e.apply(void 0,[a].concat(m(r))),n=a,clearTimeout(i),i=setTimeout((function(){e.apply(void 0,[a].concat(m(r))),i=0}),t)}};return a._clear=function(){clearTimeout(i),i=null},a}function w(e,t){if(e===t)return!0;if("object"===d(e)){for(var i in e)if(!w(e[i],t[i]))return!1;return!0}return!1}var _=function(){function e(t,i,n){h(this,e),this.el=t,this.observer=null,this.frozen=!1,this.createObserver(i,n)}return f(e,[{key:"createObserver",value:function(e,t){var i=this;if(this.observer&&this.destroyObserver(),!this.frozen){if(this.options=b(e),this.callback=function(e,t){i.options.callback(e,t),e&&i.options.once&&(i.frozen=!0,i.destroyObserver())},this.callback&&this.options.throttle){var n=this.options.throttleOptions||{},r=n.leading;this.callback=x(this.callback,this.options.throttle,{leading:function(e){return"both"===r||"visible"===r&&e||"hidden"===r&&!e}})}this.oldResult=void 0,this.observer=new IntersectionObserver((function(e){var t=e[0];if(e.length>1){var n=e.find((function(e){return e.isIntersecting}));n&&(t=n)}if(i.callback){var r=t.isIntersecting&&t.intersectionRatio>=i.threshold;if(r===i.oldResult)return;i.oldResult=r,i.callback(r,t)}}),this.options.intersection),t.context.$nextTick((function(){i.observer&&i.observer.observe(i.el)}))}}},{key:"destroyObserver",value:function(){this.observer&&(this.observer.disconnect(),this.observer=null),this.callback&&this.callback._clear&&(this.callback._clear(),this.callback=null)}},{key:"threshold",get:function(){return this.options.intersection&&this.options.intersection.threshold||0}}]),e}();function S(e,t,i){var n=t.value;if(n)if("undefined"===typeof IntersectionObserver);else{var r=new _(e,n,i);e._vue_visibilityState=r}}function O(e,t,i){var n=t.value,r=t.oldValue;if(!w(n,r)){var o=e._vue_visibilityState;n?o?o.createObserver(n,i):S(e,{value:n},i):E(e)}}function E(e){var t=e._vue_visibilityState;t&&(t.destroyObserver(),delete e._vue_visibilityState)}var T={bind:S,update:O,unbind:E};function k(e){e.directive("observe-visibility",T)}var A={version:"0.4.6",install:k},z=null;"undefined"!==typeof window?z=window.Vue:"undefined"!==typeof i.g&&(z=i.g.Vue),z&&z.use(A);var M=i(14473),D=i.n(M),N={itemsLimit:1e3};function C(e){return C="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},C(e)}function j(e,t,i){return t in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}function R(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,n)}return i}function I(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?R(Object(i),!0).forEach((function(t){j(e,t,i[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):R(Object(i)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}function L(e,t){if(e){if("string"===typeof e)return F(e,t);var i=Object.prototype.toString.call(e).slice(8,-1);return"Object"===i&&e.constructor&&(i=e.constructor.name),"Map"===i||"Set"===i?Array.from(i):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?F(e,t):void 0}}function F(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,n=new Array(t);i<t;i++)n[i]=e[i];return n}function P(e){if("undefined"===typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(e=L(e))){var t=0,i=function(){};return{s:i,n:function(){return t>=e.length?{done:!0}:{done:!1,value:e[t++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var n,r,o=!0,a=!1;return{s:function(){n=e[Symbol.iterator]()},n:function(){var e=n.next();return o=e.done,e},e:function(e){a=!0,r=e},f:function(){try{o||null==n.return||n.return()}finally{if(a)throw r}}}}var V={items:{type:Array,required:!0},keyField:{type:String,default:"id"},direction:{type:String,default:"vertical",validator:function(e){return["vertical","horizontal"].includes(e)}}};function $(){return this.items.length&&"object"!==C(this.items[0])}var H=!1;if("undefined"!==typeof window){H=!1;try{var B=Object.defineProperty({},"passive",{get:function(){H=!0}});window.addEventListener("test",null,B)}catch(xe){}}var Y=0,q={name:"RecycleScroller",components:{ResizeObserver:a},directives:{ObserveVisibility:T},props:I({},V,{itemSize:{type:Number,default:null},minItemSize:{type:[Number,String],default:null},sizeField:{type:String,default:"size"},typeField:{type:String,default:"type"},buffer:{type:Number,default:200},pageMode:{type:Boolean,default:!1},prerender:{type:Number,default:0},emitUpdate:{type:Boolean,default:!1}}),data:function(){return{pool:[],totalSize:0,ready:!1,hoverKey:null}},computed:{sizes:function(){if(null===this.itemSize){for(var e,t={"-1":{accumulator:0}},i=this.items,n=this.sizeField,r=this.minItemSize,o=1e4,a=0,s=0,l=i.length;s<l;s++)e=i[s][n]||r,e<o&&(o=e),a+=e,t[s]={accumulator:a,size:e};return this.$_computedMinItemSize=o,t}return[]},simpleArray:$},watch:{items:function(){this.updateVisibleItems(!0)},pageMode:function(){this.applyPageMode(),this.updateVisibleItems(!1)},sizes:{handler:function(){this.updateVisibleItems(!1)},deep:!0}},created:function(){this.$_startIndex=0,this.$_endIndex=0,this.$_views=new Map,this.$_unusedViews=new Map,this.$_scrollDirty=!1,this.$_lastUpdateScrollPosition=0,this.prerender&&(this.$_prerender=!0,this.updateVisibleItems(!1))},mounted:function(){var e=this;this.applyPageMode(),this.$nextTick((function(){e.$_prerender=!1,e.updateVisibleItems(!0),e.ready=!0}))},beforeDestroy:function(){this.removeListeners()},methods:{addView:function(e,t,i,n,r){var o={item:i,position:0},a={id:Y++,index:t,used:!0,key:n,type:r};return Object.defineProperty(o,"nr",{configurable:!1,value:a}),e.push(o),o},unuseView:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=this.$_unusedViews,n=e.nr.type,r=i.get(n);r||(r=[],i.set(n,r)),r.push(e),t||(e.nr.used=!1,e.position=-9999,this.$_views.delete(e.nr.key))},handleResize:function(){this.$emit("resize"),this.ready&&this.updateVisibleItems(!1)},handleScroll:function(e){var t=this;this.$_scrollDirty||(this.$_scrollDirty=!0,requestAnimationFrame((function(){t.$_scrollDirty=!1;var e=t.updateVisibleItems(!1,!0),i=e.continuous;i||(clearTimeout(t.$_refreshTimout),t.$_refreshTimout=setTimeout(t.handleScroll,100))})))},handleVisibilityChange:function(e,t){var i=this;this.ready&&(e||0!==t.boundingClientRect.width||0!==t.boundingClientRect.height?(this.$emit("visible"),requestAnimationFrame((function(){i.updateVisibleItems(!1)}))):this.$emit("hidden"))},updateVisibleItems:function(e){var t,i,n,r,o=arguments.length>1&&void 0!==arguments[1]&&arguments[1],a=this.itemSize,s=this.$_computedMinItemSize,l=this.typeField,c=this.simpleArray?null:this.keyField,u=this.items,d=u.length,h=this.sizes,p=this.$_views,f=this.$_unusedViews,m=this.pool;if(d)if(this.$_prerender)t=0,i=this.prerender,n=null;else{var v=this.getScroll();if(o){var g=v.start-this.$_lastUpdateScrollPosition;if(g<0&&(g=-g),null===a&&g<s||g<a)return{continuous:!0}}this.$_lastUpdateScrollPosition=v.start;var y=this.buffer;if(v.start-=y,v.end+=y,null===a){var b,x,w=0,_=d-1,S=~~(d/2);do{x=S,b=h[S].accumulator,b<v.start?w=S:S<d-1&&h[S+1].accumulator>v.start&&(_=S),S=~~((w+_)/2)}while(S!==x);for(S<0&&(S=0),t=S,n=h[d-1].accumulator,i=S;i<d&&h[i].accumulator<v.end;i++);-1===i?i=u.length-1:(i++,i>d&&(i=d))}else t=~~(v.start/a),i=Math.ceil(v.end/a),t<0&&(t=0),i>d&&(i=d),n=d*a}else t=i=n=0;i-t>N.itemsLimit&&this.itemsLimitError(),this.totalSize=n;var O=t<=this.$_endIndex&&i>=this.$_startIndex;if(this.$_continuous!==O){if(O){p.clear(),f.clear();for(var E=0,T=m.length;E<T;E++)r=m[E],this.unuseView(r)}this.$_continuous=O}else if(O)for(var k=0,A=m.length;k<A;k++)r=m[k],r.nr.used&&(e&&(r.nr.index=u.findIndex((function(e){return c?e[c]===r.item[c]:e===r.item}))),(-1===r.nr.index||r.nr.index<t||r.nr.index>=i)&&this.unuseView(r));for(var z,M,D,C,j=O?null:new Map,R=t;R<i;R++){z=u[R];var I=c?z[c]:z;if(null==I)throw new Error("Key is ".concat(I," on item (keyField is '").concat(c,"')"));r=p.get(I),a||h[R].size?(r?(r.nr.used=!0,r.item=z):(M=z[l],D=f.get(M),O?D&&D.length?(r=D.pop(),r.item=z,r.nr.used=!0,r.nr.index=R,r.nr.key=I,r.nr.type=M):r=this.addView(m,R,z,I,M):(C=j.get(M)||0,(!D||C>=D.length)&&(r=this.addView(m,R,z,I,M),this.unuseView(r,!0),D=f.get(M)),r=D[C],r.item=z,r.nr.used=!0,r.nr.index=R,r.nr.key=I,r.nr.type=M,j.set(M,C+1),C++),p.set(I,r)),r.position=null===a?h[R-1].accumulator:R*a):r&&this.unuseView(r)}return this.$_startIndex=t,this.$_endIndex=i,this.emitUpdate&&this.$emit("update",t,i),clearTimeout(this.$_sortTimer),this.$_sortTimer=setTimeout(this.sortViews,300),{continuous:O}},getListenerTarget:function(){var e=D()(this.$el);return!window.document||e!==window.document.documentElement&&e!==window.document.body||(e=window),e},getScroll:function(){var e,t=this.$el,i=this.direction,n="vertical"===i;if(this.pageMode){var r=t.getBoundingClientRect(),o=n?r.height:r.width,a=-(n?r.top:r.left),s=n?window.innerHeight:window.innerWidth;a<0&&(s+=a,a=0),a+s>o&&(s=o-a),e={start:a,end:a+s}}else e=n?{start:t.scrollTop,end:t.scrollTop+t.clientHeight}:{start:t.scrollLeft,end:t.scrollLeft+t.clientWidth};return e},applyPageMode:function(){this.pageMode?this.addListeners():this.removeListeners()},addListeners:function(){this.listenerTarget=this.getListenerTarget(),this.listenerTarget.addEventListener("scroll",this.handleScroll,!!H&&{passive:!0}),this.listenerTarget.addEventListener("resize",this.handleResize)},removeListeners:function(){this.listenerTarget&&(this.listenerTarget.removeEventListener("scroll",this.handleScroll),this.listenerTarget.removeEventListener("resize",this.handleResize),this.listenerTarget=null)},scrollToItem:function(e){var t;t=null===this.itemSize?e>0?this.sizes[e-1].accumulator:0:e*this.itemSize,this.scrollToPosition(t)},scrollToPosition:function(e){"vertical"===this.direction?this.$el.scrollTop=e:this.$el.scrollLeft=e},itemsLimitError:function(){throw setTimeout((function(){})),new Error("Rendered items limit reached")},sortViews:function(){this.pool.sort((function(e,t){return e.nr.index-t.nr.index}))}}};function X(e,t,i,n,r,o,a,s,l,c){"boolean"!==typeof a&&(l=s,s=a,a=!1);var u,d="function"===typeof i?i.options:i;if(e&&e.render&&(d.render=e.render,d.staticRenderFns=e.staticRenderFns,d._compiled=!0,r&&(d.functional=!0)),n&&(d._scopeId=n),o?(u=function(e){e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext,e||"undefined"===typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),t&&t.call(this,l(e)),e&&e._registeredComponents&&e._registeredComponents.add(o)},d._ssrRegister=u):t&&(u=a?function(e){t.call(this,c(e,this.$root.$options.shadowRoot))}:function(e){t.call(this,s(e))}),u)if(d.functional){var h=d.render;d.render=function(e,t){return u.call(t),h(e,t)}}else{var p=d.beforeCreate;d.beforeCreate=p?[].concat(p,u):[u]}return i}var U=q,W=function(){var e,t,i=this,n=i.$createElement,r=i._self._c||n;return r("div",{directives:[{name:"observe-visibility",rawName:"v-observe-visibility",value:i.handleVisibilityChange,expression:"handleVisibilityChange"}],staticClass:"vue-recycle-scroller",class:(e={ready:i.ready,"page-mode":i.pageMode},e["direction-"+i.direction]=!0,e),on:{"&scroll":function(e){return i.handleScroll(e)}}},[i.$slots.before?r("div",{staticClass:"vue-recycle-scroller__slot"},[i._t("before")],2):i._e(),i._v(" "),r("div",{ref:"wrapper",staticClass:"vue-recycle-scroller__item-wrapper",style:(t={},t["vertical"===i.direction?"minHeight":"minWidth"]=i.totalSize+"px",t)},i._l(i.pool,(function(e){return r("div",{key:e.nr.id,staticClass:"vue-recycle-scroller__item-view",class:{hover:i.hoverKey===e.nr.key},style:i.ready?{transform:"translate"+("vertical"===i.direction?"Y":"X")+"("+e.position+"px)"}:null,on:{mouseenter:function(t){i.hoverKey=e.nr.key},mouseleave:function(e){i.hoverKey=null}}},[i._t("default",null,{item:e.item,index:e.nr.index,active:e.nr.used})],2)})),0),i._v(" "),i.$slots.after?r("div",{staticClass:"vue-recycle-scroller__slot"},[i._t("after")],2):i._e(),i._v(" "),r("ResizeObserver",{on:{notify:i.handleResize}})],1)},G=[];W._withStripped=!0;var K=void 0,Z=void 0,J=void 0,Q=!1,ee=X({render:W,staticRenderFns:G},K,U,Z,Q,J,!1,void 0,void 0,void 0),te={name:"DynamicScroller",components:{RecycleScroller:ee},inheritAttrs:!1,provide:function(){return"undefined"!==typeof ResizeObserver&&(this.$_resizeObserver=new ResizeObserver((function(e){var t,i=P(e);try{for(i.s();!(t=i.n()).done;){var n=t.value;if(n.target){var r=new CustomEvent("resize",{detail:{contentRect:n.contentRect}});n.target.dispatchEvent(r)}}}catch(o){i.e(o)}finally{i.f()}}))),{vscrollData:this.vscrollData,vscrollParent:this,vscrollResizeObserver:this.$_resizeObserver}},props:I({},V,{minItemSize:{type:[Number,String],required:!0}}),data:function(){return{vscrollData:{active:!0,sizes:{},validSizes:{},keyField:this.keyField,simpleArray:!1}}},computed:{simpleArray:$,itemsWithSize:function(){for(var e=[],t=this.items,i=this.keyField,n=this.simpleArray,r=this.vscrollData.sizes,o=0;o<t.length;o++){var a=t[o],s=n?o:a[i],l=r[s];"undefined"!==typeof l||this.$_undefinedMap[s]||(l=0),e.push({item:a,id:s,size:l})}return e},listeners:function(){var e={};for(var t in this.$listeners)"resize"!==t&&"visible"!==t&&(e[t]=this.$listeners[t]);return e}},watch:{items:function(){this.forceUpdate(!1)},simpleArray:{handler:function(e){this.vscrollData.simpleArray=e},immediate:!0},direction:function(e){this.forceUpdate(!0)}},created:function(){this.$_updates=[],this.$_undefinedSizes=0,this.$_undefinedMap={}},activated:function(){this.vscrollData.active=!0},deactivated:function(){this.vscrollData.active=!1},methods:{onScrollerResize:function(){var e=this.$refs.scroller;e&&this.forceUpdate(),this.$emit("resize")},onScrollerVisible:function(){this.$emit("vscroll:update",{force:!1}),this.$emit("visible")},forceUpdate:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];(e||this.simpleArray)&&(this.vscrollData.validSizes={}),this.$emit("vscroll:update",{force:!0})},scrollToItem:function(e){var t=this.$refs.scroller;t&&t.scrollToItem(e)},getItemSize:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,i=this.simpleArray?null!=t?t:this.items.indexOf(e):e[this.keyField];return this.vscrollData.sizes[i]||0},scrollToBottom:function(){var e=this;if(!this.$_scrollingToBottom){this.$_scrollingToBottom=!0;var t=this.$el;this.$nextTick((function(){t.scrollTop=t.scrollHeight+5e3;var i=function i(){t.scrollTop=t.scrollHeight+5e3,requestAnimationFrame((function(){t.scrollTop=t.scrollHeight+5e3,0===e.$_undefinedSizes?e.$_scrollingToBottom=!1:requestAnimationFrame(i)}))};requestAnimationFrame(i)}))}}}},ie=te,ne=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("RecycleScroller",e._g(e._b({ref:"scroller",attrs:{items:e.itemsWithSize,"min-item-size":e.minItemSize,direction:e.direction,"key-field":"id"},on:{resize:e.onScrollerResize,visible:e.onScrollerVisible},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.item,n=t.index,r=t.active;return[e._t("default",null,null,{item:i.item,index:n,active:r,itemWithSize:i})]}}],null,!0)},"RecycleScroller",e.$attrs,!1),e.listeners),[e._v(" "),i("template",{slot:"before"},[e._t("before")],2),e._v(" "),i("template",{slot:"after"},[e._t("after")],2)],2)},re=[];ne._withStripped=!0;var oe=void 0,ae=void 0,se=void 0,le=!1,ce=X({render:ne,staticRenderFns:re},oe,ie,ae,le,se,!1,void 0,void 0,void 0),ue={name:"DynamicScrollerItem",inject:["vscrollData","vscrollParent","vscrollResizeObserver"],props:{item:{required:!0},watchData:{type:Boolean,default:!1},active:{type:Boolean,required:!0},index:{type:Number,default:void 0},sizeDependencies:{type:[Array,Object],default:null},emitResize:{type:Boolean,default:!1},tag:{type:String,default:"div"}},computed:{id:function(){return this.vscrollData.simpleArray?this.index:this.item[this.vscrollData.keyField]},size:function(){return this.vscrollData.validSizes[this.id]&&this.vscrollData.sizes[this.id]||0},finalActive:function(){return this.active&&this.vscrollData.active}},watch:{watchData:"updateWatchData",id:function(){this.size||this.onDataUpdate()},finalActive:function(e){this.size||(e?this.vscrollParent.$_undefinedMap[this.id]||(this.vscrollParent.$_undefinedSizes++,this.vscrollParent.$_undefinedMap[this.id]=!0):this.vscrollParent.$_undefinedMap[this.id]&&(this.vscrollParent.$_undefinedSizes--,this.vscrollParent.$_undefinedMap[this.id]=!1)),this.vscrollResizeObserver?e?this.observeSize():this.unobserveSize():e&&this.$_pendingVScrollUpdate===this.id&&this.updateSize()}},created:function(){var e=this;if(!this.$isServer&&(this.$_forceNextVScrollUpdate=null,this.updateWatchData(),!this.vscrollResizeObserver)){var t=function(t){e.$watch((function(){return e.sizeDependencies[t]}),e.onDataUpdate)};for(var i in this.sizeDependencies)t(i);this.vscrollParent.$on("vscroll:update",this.onVscrollUpdate),this.vscrollParent.$on("vscroll:update-size",this.onVscrollUpdateSize)}},mounted:function(){this.vscrollData.active&&(this.updateSize(),this.observeSize())},beforeDestroy:function(){this.vscrollParent.$off("vscroll:update",this.onVscrollUpdate),this.vscrollParent.$off("vscroll:update-size",this.onVscrollUpdateSize),this.unobserveSize()},methods:{updateSize:function(){this.finalActive?this.$_pendingSizeUpdate!==this.id&&(this.$_pendingSizeUpdate=this.id,this.$_forceNextVScrollUpdate=null,this.$_pendingVScrollUpdate=null,this.computeSize(this.id)):this.$_forceNextVScrollUpdate=this.id},updateWatchData:function(){var e=this;this.watchData?this.$_watchData=this.$watch("data",(function(){e.onDataUpdate()}),{deep:!0}):this.$_watchData&&(this.$_watchData(),this.$_watchData=null)},onVscrollUpdate:function(e){var t=e.force;!this.finalActive&&t&&(this.$_pendingVScrollUpdate=this.id),this.$_forceNextVScrollUpdate!==this.id&&!t&&this.size||this.updateSize()},onDataUpdate:function(){this.updateSize()},computeSize:function(e){var t=this;this.$nextTick((function(){if(t.id===e){var i=t.$el.offsetWidth,n=t.$el.offsetHeight;t.applySize(i,n)}t.$_pendingSizeUpdate=null}))},applySize:function(e,t){var i=Math.round("vertical"===this.vscrollParent.direction?t:e);i&&this.size!==i&&(this.vscrollParent.$_undefinedMap[this.id]&&(this.vscrollParent.$_undefinedSizes--,this.vscrollParent.$_undefinedMap[this.id]=void 0),this.$set(this.vscrollData.sizes,this.id,i),this.$set(this.vscrollData.validSizes,this.id,!0),this.emitResize&&this.$emit("resize",this.id))},observeSize:function(){this.vscrollResizeObserver&&(this.vscrollResizeObserver.observe(this.$el.parentNode),this.$el.parentNode.addEventListener("resize",this.onResize))},unobserveSize:function(){this.vscrollResizeObserver&&(this.vscrollResizeObserver.unobserve(this.$el.parentNode),this.$el.parentNode.removeEventListener("resize",this.onResize))},onResize:function(e){var t=e.detail.contentRect,i=t.width,n=t.height;this.applySize(i,n)}},render:function(e){return e(this.tag,this.$slots.default)}},de=ue,he=void 0,pe=void 0,fe=void 0,me=void 0,ve=X({},he,de,pe,me,fe,!1,void 0,void 0,void 0);function ge(e,t){e.component("".concat(t,"recycle-scroller"),ee),e.component("".concat(t,"RecycleScroller"),ee),e.component("".concat(t,"dynamic-scroller"),ce),e.component("".concat(t,"DynamicScroller"),ce),e.component("".concat(t,"dynamic-scroller-item"),ve),e.component("".concat(t,"DynamicScrollerItem"),ve)}var ye={version:"1.0.10",install:function(e,t){var i=Object.assign({},{installComponents:!0,componentsPrefix:""},t);for(var n in i)"undefined"!==typeof i[n]&&(N[n]=i[n]);i.installComponents&&ge(e,i.componentsPrefix)}},be=null;"undefined"!==typeof window?be=window.Vue:"undefined"!==typeof i.g&&(be=i.g.Vue),be&&be.use(ye)},30190:function(e){"use strict";var t=function(){};e.exports=t},98878:function(e,t,i){var n=i(58418),r=i(98766);e.exports={changer:n,varyColor:r}},58418:function(e,t,i){i(32564);var n,r={};function o(){return"undefined"===typeof window?i.g:window}function a(e,t){if(e.length!==t.length)return!1;for(var i=0,n=e.length;i<n;i++)if(e[i]!==t[i])return!1;return!0}e.exports={_tryNum:0,changeColor:function(e,t){var i=t||o().Promise,s=this;if(!n){n=o()["tc_cfg_09272470305613778"];var l=h();if(l)return l}var c=e.oldColors||n.colors||[],u=e.newColors||[],d=n.url||e.cssUrl;return e.changeUrl&&(d=e.changeUrl(d)),new i((function(e,t){var i=r[d];i&&(c=i.colors),a(c,u)?e():p(i,d,e,t)}));function h(){if(!n){if(s._tryNum<9)return s._tryNum=s._tryNum+1,new i((function(i,n){setTimeout((function(){i(s.changeColor(e,t))}),100)}));n={}}}function p(t,i,n,o){var a=t&&document.getElementById(t.id);if(a&&t.colors)d(a.innerText),t.colors=u,n();else{var l="css_"+ +new Date;a=document.querySelector(e.appendToEl||"body").appendChild(document.createElement("style")),a.setAttribute("id",l),s.getCssString(i,(function(e){d(e),r[i]={id:l,colors:u},n()}),o)}function d(e){e=s.replaceCssText(e,c,u),a.innerText=e}}},replaceCssText:function(e,t,i){return t.forEach((function(t,n){var r=new RegExp(t.replace(/\s/g,"").replace(/,/g,",\\s*")+"([\\da-f]{2})?(\\b|\\)|,|\\s)","ig");e=e.replace(r,i[n]+"$1$2")})),e},getCssString:function(e,t,i){var r=n.cssCode;if(r)return n.cssCode="",void t(r);var o=new XMLHttpRequest;o.onreadystatechange=function(){4===o.readyState&&(200===o.status?t(o.responseText):i(o.status))},o.onerror=function(e){i(e)},o.ontimeout=function(e){i(e)},o.open("GET",e),o.send()}}},98766:function(e){function t(e){var t=e.toString(16);return 1===t.length&&(t="0"+t),t}function i(e,t){return r("fff",e,t)}function n(e,t){return r("000",e,t)}function r(e,i,n,r,o){e=s(e),i=s(i),void 0===n&&(n=.5),void 0===r&&(r=1),void 0===o&&(o=1);var l=2*n-1,c=r-o,u=((l*c===-1?l:(l+c)/(1+l*c))+1)/2,d=1-u,h=a(e),p=a(i),f=Math.round(u*h[0]+d*p[0]),m=Math.round(u*h[1]+d*p[1]),v=Math.round(u*h[2]+d*p[2]);return"#"+t(f)+t(m)+t(v)}function o(e,t,i){return r(e,i||"fff",.5,t,1-t)}function a(e){e=s(e),3===e.length&&(e=e[0]+e[0]+e[1]+e[1]+e[2]+e[2]);var t=parseInt(e.slice(0,2),16),i=parseInt(e.slice(2,4),16),n=parseInt(e.slice(4,6),16);return[t,i,n]}function s(e){return e.replace("#","")}function l(e){var t=a(e),i=c.apply(0,t);return[i[0].toFixed(0),(100*i[1]).toFixed(3)+"%",(100*i[2]).toFixed(3)+"%"].join(",")}function c(e,t,i){var n=e/255,r=t/255,o=i/255,a=Math.max(n,r,o),s=Math.min(n,r,o),l=a-s,c=(a+s)/2,u=0,d=0;if(Math.abs(l)>1e-5){d=c<=.5?l/(a+s):l/(2-a-s);var h=(a-n)/l,p=(a-r)/l,f=(a-o)/l;u=n==a?f-p:r==a?2+h-f:4+p-h,u*=60,u<0&&(u+=360)}return[u,d,c]}e.exports={lighten:i,darken:n,mix:r,toNum3:a,rgb:o,rgbaToRgb:o,pad2:t,rgbToHsl:c,rrggbbToHsl:l}},23152:function(e,t,i){"use strict";var n=i(89584),r=i(95082),o=i(96884),a={name:"VuePlyr",props:{options:{type:Object,required:!1,default:function(){return{}}},emit:{type:Array,required:!1,default:function(){return[]}}},data:function(){return{player:{}}},computed:{opts:function(){var e=this.options;return this.options.hasOwnProperty("hideYouTubeDOMError")||(e.hideYouTubeDOMError=!0),e}},mounted:function(){var e=this;this.player=new o(this.$el.firstChild,this.opts),this.emit.forEach((function(t){e.player.on(t,e.emitPlayerEvent)}))},beforeDestroy:function(){try{this.player.destroy()}catch(e){!this.opts.hideYouTubeDOMError||e.message}},methods:{emitPlayerEvent:function(e){this.$emit(e.type,e)}}};function s(e,t,i,n,r,o,a,s,l,c){"boolean"!==typeof a&&(l=s,s=a,a=!1);var u,d="function"===typeof i?i.options:i;if(e&&e.render&&(d.render=e.render,d.staticRenderFns=e.staticRenderFns,d._compiled=!0,r&&(d.functional=!0)),n&&(d._scopeId=n),o?(u=function(e){e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext,e||"undefined"===typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),t&&t.call(this,l(e)),e&&e._registeredComponents&&e._registeredComponents.add(o)},d._ssrRegister=u):t&&(u=a?function(e){t.call(this,c(e,this.$root.$options.shadowRoot))}:function(e){t.call(this,s(e))}),u)if(d.functional){var h=d.render;d.render=function(e,t){return u.call(t),h(e,t)}}else{var p=d.beforeCreate;d.beforeCreate=p?[].concat(p,u):[u]}return i}var l,c="undefined"!==typeof navigator&&/msie [6-9]\\b/.test(navigator.userAgent.toLowerCase());function u(e){return function(e,t){return h(e,t)}}var d={};function h(e,t){var i=c?t.media||"default":e,n=d[i]||(d[i]={ids:new Set,styles:[]});if(!n.ids.has(e)){n.ids.add(e);var r=t.source;if(t.map&&(r+="\n/*# sourceURL="+t.map.sources[0]+" */",r+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(t.map))))+" */"),n.element||(n.element=document.createElement("style"),n.element.type="text/css",t.media&&n.element.setAttribute("media",t.media),void 0===l&&(l=document.head||document.getElementsByTagName("head")[0]),l.appendChild(n.element)),"styleSheet"in n.element)n.styles.push(r),n.element.styleSheet.cssText=n.styles.filter(Boolean).join("\n");else{var o=n.ids.size-1,a=document.createTextNode(r),s=n.element.childNodes;s[o]&&n.element.removeChild(s[o]),s.length?n.element.insertBefore(a,s[o]):n.element.appendChild(a)}}}var p=a,f=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[e._t("default")],2)},m=[];f._withStripped=!0;var v=function(e){e&&e("data-v-91800632_0",{source:"@keyframes plyr-progress{to{background-position:25px 0}}@keyframes plyr-popup{0%{opacity:.5;transform:translateY(10px)}to{opacity:1;transform:translateY(0)}}@keyframes plyr-fade-in{from{opacity:0}to{opacity:1}}.plyr{-moz-osx-font-smoothing:auto;-webkit-font-smoothing:subpixel-antialiased;direction:ltr;font-family:Avenir,\"Avenir Next\",\"Helvetica Neue\",\"Segoe UI\",Helvetica,Arial,sans-serif;font-variant-numeric:tabular-nums;font-weight:500;line-height:1.7;max-width:100%;min-width:200px;position:relative;text-shadow:none;transition:box-shadow .3s ease}.plyr audio,.plyr video{border-radius:inherit;height:auto;vertical-align:middle;width:100%}.plyr button{font:inherit;line-height:inherit;width:auto}.plyr:focus{outline:0}.plyr--full-ui{box-sizing:border-box}.plyr--full-ui *,.plyr--full-ui ::after,.plyr--full-ui ::before{box-sizing:inherit}.plyr--full-ui a,.plyr--full-ui button,.plyr--full-ui input,.plyr--full-ui label{touch-action:manipulation}.plyr__badge{background:#4a5764;border-radius:2px;color:#fff;font-size:9px;line-height:1;padding:3px 4px}.plyr--full-ui ::-webkit-media-text-track-container{display:none}.plyr__captions{animation:plyr-fade-in .3s ease;bottom:0;color:#fff;display:none;font-size:14px;left:0;padding:10px;position:absolute;text-align:center;transition:transform .4s ease-in-out;width:100%}.plyr__captions .plyr__caption{background:rgba(0,0,0,.8);border-radius:2px;-webkit-box-decoration-break:clone;box-decoration-break:clone;line-height:185%;padding:.2em .5em;white-space:pre-wrap}.plyr__captions .plyr__caption div{display:inline}.plyr__captions span:empty{display:none}@media (min-width:480px){.plyr__captions{font-size:16px;padding:20px}}@media (min-width:768px){.plyr__captions{font-size:18px}}.plyr--captions-active .plyr__captions{display:block}.plyr:not(.plyr--hide-controls) .plyr__controls:not(:empty)~.plyr__captions{transform:translateY(-40px)}.plyr__control{background:0 0;border:0;border-radius:3px;color:inherit;cursor:pointer;flex-shrink:0;overflow:visible;padding:7px;position:relative;transition:all .3s ease}.plyr__control svg{display:block;fill:currentColor;height:18px;pointer-events:none;width:18px}.plyr__control:focus{outline:0}.plyr__control.plyr__tab-focus{box-shadow:0 0 0 5px rgba(0,179,255,.5);outline:0}a.plyr__control{text-decoration:none}a.plyr__control::after,a.plyr__control::before{display:none}.plyr__control.plyr__control--pressed .icon--not-pressed,.plyr__control.plyr__control--pressed .label--not-pressed,.plyr__control:not(.plyr__control--pressed) .icon--pressed,.plyr__control:not(.plyr__control--pressed) .label--pressed{display:none}.plyr--audio .plyr__control.plyr__tab-focus,.plyr--audio .plyr__control:hover,.plyr--audio .plyr__control[aria-expanded=true]{background:#00b3ff;color:#fff}.plyr--video .plyr__control.plyr__tab-focus,.plyr--video .plyr__control:hover,.plyr--video .plyr__control[aria-expanded=true]{background:#00b3ff;color:#fff}.plyr__control--overlaid{background:rgba(0,179,255,.8);border:0;border-radius:100%;color:#fff;display:none;left:50%;padding:15px;position:absolute;top:50%;transform:translate(-50%,-50%);z-index:2}.plyr__control--overlaid svg{left:2px;position:relative}.plyr__control--overlaid:focus,.plyr__control--overlaid:hover{background:#00b3ff}.plyr--playing .plyr__control--overlaid{opacity:0;visibility:hidden}.plyr--full-ui.plyr--video .plyr__control--overlaid{display:block}.plyr--full-ui ::-webkit-media-controls{display:none}.plyr__controls{align-items:center;display:flex;justify-content:flex-end;text-align:center}.plyr__controls .plyr__progress__container{flex:1;min-width:0}.plyr__controls .plyr__controls__item{margin-left:2.5px}.plyr__controls .plyr__controls__item:first-child{margin-left:0;margin-right:auto}.plyr__controls .plyr__controls__item.plyr__progress__container{padding-left:2.5px}.plyr__controls .plyr__controls__item.plyr__time{padding:0 5px}.plyr__controls .plyr__controls__item.plyr__progress__container:first-child,.plyr__controls .plyr__controls__item.plyr__time+.plyr__time,.plyr__controls .plyr__controls__item.plyr__time:first-child{padding-left:0}.plyr__controls .plyr__controls__item.plyr__volume{padding-right:5px}.plyr__controls .plyr__controls__item.plyr__volume:first-child{padding-right:0}.plyr__controls:empty{display:none}.plyr--audio .plyr__controls{background:#fff;border-radius:inherit;color:#4a5764;padding:10px}.plyr--video .plyr__controls{background:linear-gradient(rgba(0,0,0,0),rgba(0,0,0,.7));border-bottom-left-radius:inherit;border-bottom-right-radius:inherit;bottom:0;color:#fff;left:0;padding:20px 5px 5px;position:absolute;right:0;transition:opacity .4s ease-in-out,transform .4s ease-in-out;z-index:3}@media (min-width:480px){.plyr--video .plyr__controls{padding:35px 10px 10px}}.plyr--video.plyr--hide-controls .plyr__controls{opacity:0;pointer-events:none;transform:translateY(100%)}.plyr [data-plyr=airplay],.plyr [data-plyr=captions],.plyr [data-plyr=fullscreen],.plyr [data-plyr=pip]{display:none}.plyr--airplay-supported [data-plyr=airplay],.plyr--captions-enabled [data-plyr=captions],.plyr--fullscreen-enabled [data-plyr=fullscreen],.plyr--pip-supported [data-plyr=pip]{display:inline-block}.plyr__menu{display:flex;position:relative}.plyr__menu .plyr__control svg{transition:transform .3s ease}.plyr__menu .plyr__control[aria-expanded=true] svg{transform:rotate(90deg)}.plyr__menu .plyr__control[aria-expanded=true] .plyr__tooltip{display:none}.plyr__menu__container{animation:plyr-popup .2s ease;background:rgba(255,255,255,.9);border-radius:4px;bottom:100%;box-shadow:0 1px 2px rgba(0,0,0,.15);color:#4a5764;font-size:16px;margin-bottom:10px;position:absolute;right:-3px;text-align:left;white-space:nowrap;z-index:3}.plyr__menu__container>div{overflow:hidden;transition:height .35s cubic-bezier(.4,0,.2,1),width .35s cubic-bezier(.4,0,.2,1)}.plyr__menu__container::after{border:4px solid transparent;border-top-color:rgba(255,255,255,.9);content:'';height:0;position:absolute;right:15px;top:100%;width:0}.plyr__menu__container [role=menu]{padding:7px}.plyr__menu__container [role=menuitem],.plyr__menu__container [role=menuitemradio]{margin-top:2px}.plyr__menu__container [role=menuitem]:first-child,.plyr__menu__container [role=menuitemradio]:first-child{margin-top:0}.plyr__menu__container .plyr__control{align-items:center;color:#4a5764;display:flex;font-size:14px;padding:4px 11px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;width:100%}.plyr__menu__container .plyr__control>span{align-items:inherit;display:flex;width:100%}.plyr__menu__container .plyr__control::after{border:4px solid transparent;content:'';position:absolute;top:50%;transform:translateY(-50%)}.plyr__menu__container .plyr__control--forward{padding-right:28px}.plyr__menu__container .plyr__control--forward::after{border-left-color:rgba(74,87,100,.8);right:5px}.plyr__menu__container .plyr__control--forward.plyr__tab-focus::after,.plyr__menu__container .plyr__control--forward:hover::after{border-left-color:currentColor}.plyr__menu__container .plyr__control--back{font-weight:500;margin:7px;margin-bottom:3px;padding-left:28px;position:relative;width:calc(100% - 14px)}.plyr__menu__container .plyr__control--back::after{border-right-color:rgba(74,87,100,.8);left:7px}.plyr__menu__container .plyr__control--back::before{background:#c1c9d1;box-shadow:0 1px 0 #fff;content:'';height:1px;left:0;margin-top:4px;overflow:hidden;position:absolute;right:0;top:100%}.plyr__menu__container .plyr__control--back.plyr__tab-focus::after,.plyr__menu__container .plyr__control--back:hover::after{border-right-color:currentColor}.plyr__menu__container .plyr__control[role=menuitemradio]{padding-left:7px}.plyr__menu__container .plyr__control[role=menuitemradio]::after,.plyr__menu__container .plyr__control[role=menuitemradio]::before{border-radius:100%}.plyr__menu__container .plyr__control[role=menuitemradio]::before{background:rgba(0,0,0,.1);content:'';display:block;flex-shrink:0;height:16px;margin-right:10px;transition:all .3s ease;width:16px}.plyr__menu__container .plyr__control[role=menuitemradio]::after{background:#fff;border:0;height:6px;left:12px;opacity:0;top:50%;transform:translateY(-50%) scale(0);transition:transform .3s ease,opacity .3s ease;width:6px}.plyr__menu__container .plyr__control[role=menuitemradio][aria-checked=true]::before{background:#00b3ff}.plyr__menu__container .plyr__control[role=menuitemradio][aria-checked=true]::after{opacity:1;transform:translateY(-50%) scale(1)}.plyr__menu__container .plyr__control[role=menuitemradio].plyr__tab-focus::before,.plyr__menu__container .plyr__control[role=menuitemradio]:hover::before{background:rgba(0,0,0,.1)}.plyr__menu__container .plyr__menu__value{align-items:center;display:flex;margin-left:auto;margin-right:-5px;overflow:hidden;padding-left:25px;pointer-events:none}.plyr--full-ui input[type=range]{-webkit-appearance:none;background:0 0;border:0;border-radius:26px;color:#00b3ff;display:block;height:19px;margin:0;padding:0;transition:box-shadow .3s ease;width:100%}.plyr--full-ui input[type=range]::-webkit-slider-runnable-track{background:0 0;border:0;border-radius:2.5px;height:5px;transition:box-shadow .3s ease;-webkit-user-select:none;user-select:none;background-image:linear-gradient(to right,currentColor var(--value,0),transparent var(--value,0))}.plyr--full-ui input[type=range]::-webkit-slider-thumb{background:#fff;border:0;border-radius:100%;box-shadow:0 1px 1px rgba(0,0,0,.15),0 0 0 1px rgba(35,41,47,.2);height:13px;position:relative;transition:all .2s ease;width:13px;-webkit-appearance:none;margin-top:-4px}.plyr--full-ui input[type=range]::-moz-range-track{background:0 0;border:0;border-radius:2.5px;height:5px;transition:box-shadow .3s ease;-moz-user-select:none;user-select:none}.plyr--full-ui input[type=range]::-moz-range-thumb{background:#fff;border:0;border-radius:100%;box-shadow:0 1px 1px rgba(0,0,0,.15),0 0 0 1px rgba(35,41,47,.2);height:13px;position:relative;transition:all .2s ease;width:13px}.plyr--full-ui input[type=range]::-moz-range-progress{background:currentColor;border-radius:2.5px;height:5px}.plyr--full-ui input[type=range]::-ms-track{background:0 0;border:0;border-radius:2.5px;height:5px;transition:box-shadow .3s ease;-ms-user-select:none;user-select:none;color:transparent}.plyr--full-ui input[type=range]::-ms-fill-upper{background:0 0;border:0;border-radius:2.5px;height:5px;transition:box-shadow .3s ease;-ms-user-select:none;user-select:none}.plyr--full-ui input[type=range]::-ms-fill-lower{background:0 0;border:0;border-radius:2.5px;height:5px;transition:box-shadow .3s ease;-ms-user-select:none;user-select:none;background:currentColor}.plyr--full-ui input[type=range]::-ms-thumb{background:#fff;border:0;border-radius:100%;box-shadow:0 1px 1px rgba(0,0,0,.15),0 0 0 1px rgba(35,41,47,.2);height:13px;position:relative;transition:all .2s ease;width:13px;margin-top:0}.plyr--full-ui input[type=range]::-ms-tooltip{display:none}.plyr--full-ui input[type=range]:focus{outline:0}.plyr--full-ui input[type=range]::-moz-focus-outer{border:0}.plyr--full-ui input[type=range].plyr__tab-focus::-webkit-slider-runnable-track{box-shadow:0 0 0 5px rgba(0,179,255,.5);outline:0}.plyr--full-ui input[type=range].plyr__tab-focus::-moz-range-track{box-shadow:0 0 0 5px rgba(0,179,255,.5);outline:0}.plyr--full-ui input[type=range].plyr__tab-focus::-ms-track{box-shadow:0 0 0 5px rgba(0,179,255,.5);outline:0}.plyr--full-ui.plyr--video input[type=range]::-webkit-slider-runnable-track{background-color:rgba(255,255,255,.25)}.plyr--full-ui.plyr--video input[type=range]::-moz-range-track{background-color:rgba(255,255,255,.25)}.plyr--full-ui.plyr--video input[type=range]::-ms-track{background-color:rgba(255,255,255,.25)}.plyr--full-ui.plyr--video input[type=range]:active::-webkit-slider-thumb{box-shadow:0 1px 1px rgba(0,0,0,.15),0 0 0 1px rgba(35,41,47,.2),0 0 0 3px rgba(255,255,255,.5)}.plyr--full-ui.plyr--video input[type=range]:active::-moz-range-thumb{box-shadow:0 1px 1px rgba(0,0,0,.15),0 0 0 1px rgba(35,41,47,.2),0 0 0 3px rgba(255,255,255,.5)}.plyr--full-ui.plyr--video input[type=range]:active::-ms-thumb{box-shadow:0 1px 1px rgba(0,0,0,.15),0 0 0 1px rgba(35,41,47,.2),0 0 0 3px rgba(255,255,255,.5)}.plyr--full-ui.plyr--audio input[type=range]::-webkit-slider-runnable-track{background-color:rgba(193,201,209,.66)}.plyr--full-ui.plyr--audio input[type=range]::-moz-range-track{background-color:rgba(193,201,209,.66)}.plyr--full-ui.plyr--audio input[type=range]::-ms-track{background-color:rgba(193,201,209,.66)}.plyr--full-ui.plyr--audio input[type=range]:active::-webkit-slider-thumb{box-shadow:0 1px 1px rgba(0,0,0,.15),0 0 0 1px rgba(35,41,47,.2),0 0 0 3px rgba(0,0,0,.1)}.plyr--full-ui.plyr--audio input[type=range]:active::-moz-range-thumb{box-shadow:0 1px 1px rgba(0,0,0,.15),0 0 0 1px rgba(35,41,47,.2),0 0 0 3px rgba(0,0,0,.1)}.plyr--full-ui.plyr--audio input[type=range]:active::-ms-thumb{box-shadow:0 1px 1px rgba(0,0,0,.15),0 0 0 1px rgba(35,41,47,.2),0 0 0 3px rgba(0,0,0,.1)}.plyr__poster{background-color:#000;background-position:50% 50%;background-repeat:no-repeat;background-size:contain;height:100%;left:0;opacity:0;position:absolute;top:0;transition:opacity .2s ease;width:100%;z-index:1}.plyr--stopped.plyr__poster-enabled .plyr__poster{opacity:1}.plyr__time{font-size:14px}.plyr__time+.plyr__time::before{content:'\\2044';margin-right:10px}@media (max-width:767px){.plyr__time+.plyr__time{display:none}}.plyr--video .plyr__time{text-shadow:0 1px 1px rgba(0,0,0,.15)}.plyr__tooltip{background:rgba(255,255,255,.9);border-radius:3px;bottom:100%;box-shadow:0 1px 2px rgba(0,0,0,.15);color:#4a5764;font-size:14px;font-weight:500;left:50%;line-height:1.3;margin-bottom:10px;opacity:0;padding:5px 7.5px;pointer-events:none;position:absolute;transform:translate(-50%,10px) scale(.8);transform-origin:50% 100%;transition:transform .2s .1s ease,opacity .2s .1s ease;white-space:nowrap;z-index:2}.plyr__tooltip::before{border-left:4px solid transparent;border-right:4px solid transparent;border-top:4px solid rgba(255,255,255,.9);bottom:-4px;content:'';height:0;left:50%;position:absolute;transform:translateX(-50%);width:0;z-index:2}.plyr .plyr__control.plyr__tab-focus .plyr__tooltip,.plyr .plyr__control:hover .plyr__tooltip,.plyr__tooltip--visible{opacity:1;transform:translate(-50%,0) scale(1)}.plyr .plyr__control:hover .plyr__tooltip{z-index:3}.plyr__controls>.plyr__control:first-child .plyr__tooltip,.plyr__controls>.plyr__control:first-child+.plyr__control .plyr__tooltip{left:0;transform:translate(0,10px) scale(.8);transform-origin:0 100%}.plyr__controls>.plyr__control:first-child .plyr__tooltip::before,.plyr__controls>.plyr__control:first-child+.plyr__control .plyr__tooltip::before{left:16px}.plyr__controls>.plyr__control:last-child .plyr__tooltip{left:auto;right:0;transform:translate(0,10px) scale(.8);transform-origin:100% 100%}.plyr__controls>.plyr__control:last-child .plyr__tooltip::before{left:auto;right:16px;transform:translateX(50%)}.plyr__controls>.plyr__control:first-child .plyr__tooltip--visible,.plyr__controls>.plyr__control:first-child+.plyr__control .plyr__tooltip--visible,.plyr__controls>.plyr__control:first-child+.plyr__control.plyr__tab-focus .plyr__tooltip,.plyr__controls>.plyr__control:first-child+.plyr__control:hover .plyr__tooltip,.plyr__controls>.plyr__control:first-child.plyr__tab-focus .plyr__tooltip,.plyr__controls>.plyr__control:first-child:hover .plyr__tooltip,.plyr__controls>.plyr__control:last-child .plyr__tooltip--visible,.plyr__controls>.plyr__control:last-child.plyr__tab-focus .plyr__tooltip,.plyr__controls>.plyr__control:last-child:hover .plyr__tooltip{transform:translate(0,0) scale(1)}.plyr--video{background:#000;overflow:hidden}.plyr--video.plyr--menu-open{overflow:visible}.plyr__video-wrapper{background:#000;border-radius:inherit;overflow:hidden;position:relative;z-index:0}.plyr__video-embed,.plyr__video-wrapper--fixed-ratio{height:0;padding-bottom:56.25%}.plyr__video-embed iframe,.plyr__video-wrapper--fixed-ratio video{border:0;height:100%;left:0;position:absolute;top:0;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;width:100%}.plyr--full-ui .plyr__video-embed>.plyr__video-embed__container{padding-bottom:240%;position:relative;transform:translateY(-38.28125%)}.plyr__progress{left:6.5px;margin-right:13px;position:relative}.plyr__progress input[type=range],.plyr__progress__buffer{margin-left:-6.5px;margin-right:-6.5px;width:calc(100% + 13px)}.plyr__progress input[type=range]{position:relative;z-index:2}.plyr__progress .plyr__tooltip{font-size:14px;left:0}.plyr__progress__buffer{-webkit-appearance:none;background:0 0;border:0;border-radius:100px;height:5px;left:0;margin-top:-2.5px;padding:0;position:absolute;top:50%}.plyr__progress__buffer::-webkit-progress-bar{background:0 0}.plyr__progress__buffer::-webkit-progress-value{background:currentColor;border-radius:100px;min-width:5px;transition:width .2s ease}.plyr__progress__buffer::-moz-progress-bar{background:currentColor;border-radius:100px;min-width:5px;transition:width .2s ease}.plyr__progress__buffer::-ms-fill{border-radius:100px;transition:width .2s ease}.plyr--video .plyr__progress__buffer{box-shadow:0 1px 1px rgba(0,0,0,.15);color:rgba(255,255,255,.25)}.plyr--audio .plyr__progress__buffer{color:rgba(193,201,209,.66)}.plyr--loading .plyr__progress__buffer{animation:plyr-progress 1s linear infinite;background-image:linear-gradient(-45deg,rgba(35,41,47,.6) 25%,transparent 25%,transparent 50%,rgba(35,41,47,.6) 50%,rgba(35,41,47,.6) 75%,transparent 75%,transparent);background-repeat:repeat-x;background-size:25px 25px;color:transparent}.plyr--video.plyr--loading .plyr__progress__buffer{background-color:rgba(255,255,255,.25)}.plyr--audio.plyr--loading .plyr__progress__buffer{background-color:rgba(193,201,209,.66)}.plyr__volume{align-items:center;display:flex;flex:1;position:relative}.plyr__volume input[type=range]{margin-left:5px;position:relative;z-index:2}@media (min-width:480px){.plyr__volume{max-width:90px}}@media (min-width:768px){.plyr__volume{max-width:110px}}.plyr--is-ios .plyr__volume{display:none!important}.plyr--is-ios.plyr--vimeo [data-plyr=mute]{display:none!important}.plyr:-webkit-full-screen{background:#000;border-radius:0!important;height:100%;margin:0;width:100%}.plyr:-ms-fullscreen{background:#000;border-radius:0!important;height:100%;margin:0;width:100%}.plyr:fullscreen{background:#000;border-radius:0!important;height:100%;margin:0;width:100%}.plyr:-webkit-full-screen video{height:100%}.plyr:-ms-fullscreen video{height:100%}.plyr:fullscreen video{height:100%}.plyr:-webkit-full-screen .plyr__video-wrapper{height:100%;position:static}.plyr:-ms-fullscreen .plyr__video-wrapper{height:100%;position:static}.plyr:fullscreen .plyr__video-wrapper{height:100%;position:static}.plyr:-webkit-full-screen.plyr--vimeo .plyr__video-wrapper{height:0;position:relative;top:50%;transform:translateY(-50%)}.plyr:-ms-fullscreen.plyr--vimeo .plyr__video-wrapper{height:0;position:relative;top:50%;transform:translateY(-50%)}.plyr:fullscreen.plyr--vimeo .plyr__video-wrapper{height:0;position:relative;top:50%;transform:translateY(-50%)}.plyr:-webkit-full-screen .plyr__control .icon--exit-fullscreen{display:block}.plyr:-ms-fullscreen .plyr__control .icon--exit-fullscreen{display:block}.plyr:fullscreen .plyr__control .icon--exit-fullscreen{display:block}.plyr:-webkit-full-screen .plyr__control .icon--exit-fullscreen+svg{display:none}.plyr:-ms-fullscreen .plyr__control .icon--exit-fullscreen+svg{display:none}.plyr:fullscreen .plyr__control .icon--exit-fullscreen+svg{display:none}.plyr:-webkit-full-screen.plyr--hide-controls{cursor:none}.plyr:-ms-fullscreen.plyr--hide-controls{cursor:none}.plyr:fullscreen.plyr--hide-controls{cursor:none}@media (min-width:1024px){.plyr:-webkit-full-screen .plyr__captions{font-size:21px}.plyr:-ms-fullscreen .plyr__captions{font-size:21px}.plyr:fullscreen .plyr__captions{font-size:21px}}.plyr:-webkit-full-screen{background:#000;border-radius:0!important;height:100%;margin:0;width:100%}.plyr:-webkit-full-screen video{height:100%}.plyr:-webkit-full-screen .plyr__video-wrapper{height:100%;position:static}.plyr:-webkit-full-screen.plyr--vimeo .plyr__video-wrapper{height:0;position:relative;top:50%;transform:translateY(-50%)}.plyr:-webkit-full-screen .plyr__control .icon--exit-fullscreen{display:block}.plyr:-webkit-full-screen .plyr__control .icon--exit-fullscreen+svg{display:none}.plyr:-webkit-full-screen.plyr--hide-controls{cursor:none}@media (min-width:1024px){.plyr:-webkit-full-screen .plyr__captions{font-size:21px}}.plyr:-moz-full-screen{background:#000;border-radius:0!important;height:100%;margin:0;width:100%}.plyr:-moz-full-screen video{height:100%}.plyr:-moz-full-screen .plyr__video-wrapper{height:100%;position:static}.plyr:-moz-full-screen.plyr--vimeo .plyr__video-wrapper{height:0;position:relative;top:50%;transform:translateY(-50%)}.plyr:-moz-full-screen .plyr__control .icon--exit-fullscreen{display:block}.plyr:-moz-full-screen .plyr__control .icon--exit-fullscreen+svg{display:none}.plyr:-moz-full-screen.plyr--hide-controls{cursor:none}@media (min-width:1024px){.plyr:-moz-full-screen .plyr__captions{font-size:21px}}.plyr:-ms-fullscreen{background:#000;border-radius:0!important;height:100%;margin:0;width:100%}.plyr:-ms-fullscreen video{height:100%}.plyr:-ms-fullscreen .plyr__video-wrapper{height:100%;position:static}.plyr:-ms-fullscreen.plyr--vimeo .plyr__video-wrapper{height:0;position:relative;top:50%;transform:translateY(-50%)}.plyr:-ms-fullscreen .plyr__control .icon--exit-fullscreen{display:block}.plyr:-ms-fullscreen .plyr__control .icon--exit-fullscreen+svg{display:none}.plyr:-ms-fullscreen.plyr--hide-controls{cursor:none}@media (min-width:1024px){.plyr:-ms-fullscreen .plyr__captions{font-size:21px}}.plyr--fullscreen-fallback{background:#000;border-radius:0!important;height:100%;margin:0;width:100%;bottom:0;left:0;position:fixed;right:0;top:0;z-index:10000000}.plyr--fullscreen-fallback video{height:100%}.plyr--fullscreen-fallback .plyr__video-wrapper{height:100%;position:static}.plyr--fullscreen-fallback.plyr--vimeo .plyr__video-wrapper{height:0;position:relative;top:50%;transform:translateY(-50%)}.plyr--fullscreen-fallback .plyr__control .icon--exit-fullscreen{display:block}.plyr--fullscreen-fallback .plyr__control .icon--exit-fullscreen+svg{display:none}.plyr--fullscreen-fallback.plyr--hide-controls{cursor:none}@media (min-width:1024px){.plyr--fullscreen-fallback .plyr__captions{font-size:21px}}.plyr__ads{border-radius:inherit;bottom:0;cursor:pointer;left:0;overflow:hidden;position:absolute;right:0;top:0;z-index:-1}.plyr__ads>div,.plyr__ads>div iframe{height:100%;position:absolute;width:100%}.plyr__ads::after{background:rgba(35,41,47,.8);border-radius:2px;bottom:10px;color:#fff;content:attr(data-badge-text);font-size:11px;padding:2px 6px;pointer-events:none;position:absolute;right:10px;z-index:3}.plyr__ads::after:empty{display:none}.plyr__cues{background:currentColor;display:block;height:5px;left:0;margin:-2.5px 0 0;opacity:.8;position:absolute;top:50%;width:3px;z-index:3}.plyr__preview-thumb{background-color:rgba(255,255,255,.9);border-radius:3px;bottom:100%;box-shadow:0 1px 2px rgba(0,0,0,.15);margin-bottom:10px;opacity:0;padding:3px;pointer-events:none;position:absolute;transform:translate(0,10px) scale(.8);transform-origin:50% 100%;transition:transform .2s .1s ease,opacity .2s .1s ease;z-index:2}.plyr__preview-thumb--is-shown{opacity:1;transform:translate(0,0) scale(1)}.plyr__preview-thumb::before{border-left:4px solid transparent;border-right:4px solid transparent;border-top:4px solid rgba(255,255,255,.9);bottom:-4px;content:'';height:0;left:50%;position:absolute;transform:translateX(-50%);width:0;z-index:2}.plyr__preview-thumb__image-container{background:#c1c9d1;border-radius:2px;overflow:hidden;position:relative;z-index:0}.plyr__preview-thumb__image-container img{height:100%;left:0;max-height:none;max-width:none;position:absolute;top:0;width:100%}.plyr__preview-thumb__time-container{bottom:6px;left:0;position:absolute;right:0;white-space:nowrap;z-index:3}.plyr__preview-thumb__time-container span{background-color:rgba(0,0,0,.55);border-radius:2px;color:#fff;font-size:14px;padding:3px 6px}.plyr__preview-scrubbing{bottom:0;filter:blur(1px);height:100%;left:0;margin:auto;opacity:0;overflow:hidden;position:absolute;right:0;top:0;transition:opacity .3s ease;width:100%;z-index:1}.plyr__preview-scrubbing--is-shown{opacity:1}.plyr__preview-scrubbing img{height:100%;left:0;max-height:none;max-width:none;object-fit:contain;position:absolute;top:0;width:100%}.plyr--no-transition{transition:none!important}.plyr__sr-only{clip:rect(1px,1px,1px,1px);overflow:hidden;border:0!important;height:1px!important;padding:0!important;position:absolute!important;width:1px!important}.plyr [hidden]{display:none!important}",map:void 0,media:void 0})},g=void 0,y=void 0,b=!1,x=s({render:f,staticRenderFns:m},v,p,g,b,y,!1,u,void 0,void 0);x.install=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};t.plyr&&(x.props.options.default=function(){return(0,r.Z)({},t.plyr)}),t.emit&&(x.props.emit.default=function(){return(0,n.Z)(t.emit)}),e.component(x.name,x)},"undefined"!==typeof window&&window.Vue&&window.Vue.use(x),t["Z"]=/^(358|4471|8109)$/.test(i.j)?x:null}}]);
(typeof window=='undefined'?global:window).tc_cfg_09272470305613778={"url":"css/theme-colors-ea095f1b.css","colors":["#1b65b9","#3274c0","#4984c7","#5f93ce","#76a3d5","#8db2dc","#a4c1e3","#bbd1ea","#d1e0f1","#ebf4fa","#bbd9ed","#8dbce0","#639fd4","#3e82c7","#1b65b9","#0f4894","#052f6e","#001a47","#000b21","27,101,185"]};

(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[7456,362,9624],{88412:function(e,t,a){"use strict";var i=a(26263),l=a(36766),s=a(1001),o=(0,s.Z)(l.Z,i.s,i.x,!1,null,"5e7ef0ae",null);t["Z"]=o.exports},42868:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return k}});var i=function(){var e=this,t=this,a=t.$createElement,i=t._self._c||a;return i("div",{staticClass:"fit"},[i("ta-border-layout",{staticStyle:{},attrs:{"layout-type":"fixTop"}},[i("div",{attrs:{slot:"header"},slot:"header"},[i("div",{staticClass:"query-header"},[i("div",{ref:"formBox",staticClass:"query-header-left",style:t.formBoxStyle},[i("ta-form",{attrs:{autoFormCreate:function(t){e.baseInfoForm=t},col:t.col,layout:"horizontal",formLayout:!0,"label-width":"80px"}},[i("ta-form-item",{attrs:{"field-decorator-id":"dateFlag","init-value":t.onRadioValue}},[i("span",{attrs:{slot:"label"},slot:"label"},[t._v(" 时间类型 ")]),i("ta-select",{staticStyle:{width:"100%"},on:{change:t.onRadioChange}},[i("ta-select-option",{attrs:{value:"audit"}},[t._v("审核时间")]),i("ta-select-option",{attrs:{value:"itemStart"}},[t._v("项目发生时间")])],1)],1),i("ta-form-item",{attrs:{fieldDecoratorId:"allDate",fieldDecoratorOptions:{rules:[{required:!0,message:"必须选取日期"}]},required:!0,"init-value":t.rangeValue}},[i("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[t._v("时间范围")]),i("ta-range-picker",{attrs:{"allow-one":!0}})],1),i("ta-form-item",{attrs:{fieldDecoratorId:"flag",label:"开单场景",fieldDecoratorOptions:{initialValue:"普通门诊开单提醒"}}},[i("ta-input",{attrs:{disabled:!0}})],1),i("ta-form-item",{attrs:{"field-decorator-id":"medinsCode",label:"院区标识",disabled:t.paramsDisable.akb020}},[i("ta-select",{attrs:{showSearch:"",placeholder:"院区标识筛选",options:t.hosList,allowClear:""},on:{change:t.fnQueryDept}})],1),i("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:this.menuConfig.aaz307,expression:"this.menuConfig.aaz307"}],attrs:{"field-decorator-id":"aaz307",label:"开单科室",disabled:t.paramsDisable.aaz307}},[i("ta-select",{attrs:{showSearch:"","show-search":!0,placeholder:"开单科室筛选",options:t.ksList,allowClear:""},on:{change:t.fnQueryGroup}})],1),i("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:this.menuConfig.aaz309,expression:"this.menuConfig.aaz309"}],attrs:{"field-decorator-id":"aaz309",label:"诊疗小组",disabled:t.paramsDisable.aaz309}},[i("ta-select",{attrs:{showSearch:"","show-search":!0,placeholder:"诊疗小组",options:t.groupList,"allow-clear":"",filterOption:t.filterOption},on:{change:t.fnQueryDocter}})],1),i("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:this.menuConfig.operate,expression:"this.menuConfig.operate"}],attrs:{"field-decorator-id":"operate",label:"医护操作",disabled:t.operateSelect}},[i("ta-select",{attrs:{mode:"multiple",maxTagCount:2,showSearch:"",placeholder:"医护操作筛选",allowClear:"",collectionType:"APE893"}})],1),i("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:this.menuConfig.aaz319,expression:"this.menuConfig.aaz319"}],attrs:{"field-decorator-id":"aaz319",label:"规则大类",disabled:t.aaz319Select}},[i("ta-select",{attrs:{showSearch:"",placeholder:"规则大类筛选",allowClear:"",options:t.ruleList}})],1),i("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:this.menuConfig.ruleType,expression:"this.menuConfig.ruleType"}],attrs:{label:"规则分类",fieldDecoratorId:"ruleType"}},[i("ta-tree-select",{attrs:{allowClear:"",showSearch:"",placeholder:"规则分类",dropdownStyle:{maxHeight:"400px",overflow:"auto"},treeData:t.treeData,treeDataSimpleMode:t.treeData}})],1),i("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:this.menuConfig.aaz263,expression:"this.menuConfig.aaz263"}],attrs:{"field-decorator-id":"aaz263",label:"医师名称",disabled:t.paramsDisable.aaz263}},[i("ta-select",{attrs:{showSearch:"",optionLabelProp:"children",mode:"combobox",placeholder:"医生名称筛选",options:t.doctorList,filterOption:t.filterOption,allowClear:""}})],1),i("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:this.menuConfig.patientInfo,expression:"this.menuConfig.patientInfo"}],attrs:{fieldDecoratorId:"patientInfo",label:"患者信息"}},[i("ta-input",{attrs:{placeholder:"请输入就诊号、登记号或姓名"}})],1),i("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:this.menuConfig.projectInfo,expression:"this.menuConfig.projectInfo"}],attrs:{fieldDecoratorId:"projectInfo",label:"医保项目"}},[i("ta-input",{attrs:{placeholder:"请输入医保项目名称或编码"}})],1),i("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:this.menuConfig.aae140,expression:"this.menuConfig.aae140"}],attrs:{fieldDecoratorId:"aae140",label:"险种类型"}},[i("ta-select",{attrs:{showSearch:"",placeholder:"险种类型筛选","collection-type":"AAE140","dropdown-match-select-width":!1,allowClear:""}})],1),i("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:this.menuConfig.result,expression:"this.menuConfig.result"}],staticClass:"result-form",attrs:{fieldDecoratorId:"result",label:"预审结果","init-value":["1","2","3"]}},[i("ta-select",{attrs:{showSearch:"",mode:"multiple",placeholder:"请选择",allowClear:""},on:{select:t.handleSelect,change:t.handleChange}},[i("ta-select-option",{attrs:{value:"0",disabled:t.resultDisabledOne}},[t._v("审核通过")]),i("ta-select-option",{attrs:{value:"1",disabled:t.resultDisabledTwo}},[t._v("可疑")]),i("ta-select-option",{attrs:{value:"2",disabled:t.resultDisabledTwo}},[t._v("违规")]),i("ta-select-option",{attrs:{value:"3",disabled:t.resultDisabledTwo}},[t._v("仅提醒")]),i("ta-select-option",{attrs:{value:"4",disabled:t.resultDisabledThree}},[t._v("无规则")])],1)],1),i("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:this.menuConfig.ake003,expression:"this.menuConfig.ake003"}],attrs:{"field-decorator-id":"ake003",label:"三目类别"}},[i("ta-select",{attrs:{showSearch:"",placeholder:"三目类别筛选","collection-type":"AKE003","dropdown-match-select-width":!1,"allow-clear":""}})],1),i("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:this.menuConfig.inProjectInfo,expression:"this.menuConfig.inProjectInfo"}],attrs:{fieldDecoratorId:"inProjectInfo",label:"院内项目"}},[i("ta-input",{attrs:{placeholder:"请输入院内项目名称或编码"}})],1),i("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:this.menuConfig.ykz126Visible,expression:"this.menuConfig.ykz126Visible"}],attrs:{fieldDecoratorId:"ykz126",label:"违规来源"}},[i("ta-select",{attrs:{placeholder:"请选择违规来源",allowClear:""}},[i("ta-select-option",{attrs:{value:"0"}},[t._v("医院端")]),i("ta-select-option",{attrs:{value:"5"}},[t._v("中心端")])],1)],1),i("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:this.menuConfig.ykz018,expression:"this.menuConfig.ykz018"}],attrs:{"field-decorator-id":"ykz018List",label:"知识元"}},[i("ta-select",{attrs:{mode:"multiple",size:"small",dropdownMatchSelectWidth:!1,dropdownStyle:{width:"45em"},autoClearSearchValue:!1,maxTagCount:1,maxTagTextLength:10,showSearch:"",placeholder:"知识元",virtual:{dropdownHeight:"160px"},allowClear:"",options:t.ykz018RpcList}})],1)],1)],1),i("div",{staticStyle:{display:"flex","margin-left":"10px",float:"right"}},[i("div",{staticClass:"ctrl-btn",on:{click:t.formShowAllChange}},[i("a",[t._v(t._s(t.formShowAll?"收起":"展开"))]),t.formShowAll?i("ta-icon",{attrs:{type:"caret-up"}}):i("ta-icon",{attrs:{type:"caret-down"}})],1),i("div",{staticStyle:{"margin-right":"10px"}},[i("ta-icon",{staticStyle:{color:"#3382f5","font-size":"16px","margin-top":"8px",cursor:"pointer",float:"left"},attrs:{type:"setting"},on:{click:t.configMenu}})],1),i("ta-button",{attrs:{type:"primary",icon:"search"},on:{click:t.fnQuery}},[t._v("查询")]),i("ta-button",{attrs:{type:"default",icon:"redo"},on:{click:t.fnReset}},[t._v("重置")])],1)])]),i("div",{staticClass:"content-box"},[i("ta-title",{attrs:{title:"查询结果"}}),i("div",{staticClass:"content-table"},[i("ta-big-table",{ref:"infoTableRef",attrs:{columns:t.infoColumns,data:t.infoTableData,"export-config":{},"import-config":{},"control-column":t.showHiddenOrSortColumn,border:"","empty-text":"-",height:"auto","highlight-hover-row":"","highlight-current-row":"","auto-resize":"","show-overflow":"",resizable:""},scopedSlots:t._u([{key:"ape800",fn:function(e){var a=e.row;return[t.rulelessShow?i("span",[t._v("——")]):"0"==a.ape800?i("span",{staticStyle:{color:"#0f990f"}},[i("ta-icon",{attrs:{type:"check-circle",theme:"twoTone",twoToneColor:"#52c41a"}}),t._v("审核通过")],1):"1"==a.ape800?i("span",{staticStyle:{color:"#F59A23"}},[i("ta-icon",{attrs:{type:"question-circle",theme:"twoTone",twoToneColor:"#F59A23"}}),t._v("可疑提醒")],1):"2"==a.ape800?i("span",{staticStyle:{color:"#FF0000"}},[i("ta-icon",{attrs:{type:"exclamation-circle",theme:"twoTone",twoToneColor:"#FF0000"}}),t._v("违规提醒")],1):"3"==a.ape800?i("span",{staticStyle:{color:"#ffc49a"}},[i("ta-icon",{attrs:{type:"bell",theme:"twoTone",twoToneColor:"#e1ba91"}}),t._v("仅提醒")],1):t._e()]}},{key:"ake002",fn:function(e){var a=e.row;return["2"==a.ape800?i("span",{staticStyle:{color:"#FF0000"}},[t._v(t._s(a.ake002))]):i("span",[t._v(t._s(a.ake002))])]}},{key:"akb065",fn:function(e){var a=e.row;return["2"==a.ape800?i("span",{staticStyle:{color:"#FF0000"}},[t._v(t._s(a.akb065))]):i("span",[t._v(t._s(a.akb065))])]}},{key:"operate",fn:function(e){var a=e.row;return[i("div",{staticClass:"opareteItem",on:{click:function(e){return t.fnSearch(a)}}},[t._v(" 患者详情 ")]),i("div",{directives:[{name:"show",rawName:"v-show",value:!t.rulelessShow,expression:"!rulelessShow"}],staticClass:"opareteItem",on:{click:function(e){return t.openAuditDetails(a)}}},[t._v(" 审核详情 ")])]}},{key:"operate2",fn:function(e){var a=e.row;return[a.ape893real?a.ape893real.includes("4")?i("span",{staticStyle:{color:"#E6A23C"}},[t._v(t._s(a.aaz560?a.aaz560:a.ape893))]):a.ape893real.includes("1")?i("span",{staticStyle:{color:"#5DAF34"},domProps:{innerHTML:t._s(a.ape893)}}):a.ape893real.includes("2")?i("span",{staticStyle:{color:"#CF9236"},domProps:{innerHTML:t._s(a.ape893)}}):a.ape893real.includes("0")?i("span",{staticStyle:{color:"#909399"},domProps:{innerHTML:t._s(a.ape893)}}):a.ape893real.includes("3")?i("span",{staticStyle:{color:"#67C23A"},domProps:{innerHTML:t._s(a.ape893)}}):i("span",{domProps:{innerHTML:t._s(a.ape893)}}):i("span",[t._v("--")])]}},{key:"ape896",fn:function(e){var a=e.row;return[null==a.ape896||""==a.ape896?i("span",[t._v("—")]):i("span",{domProps:{innerHTML:t._s(t.CollectionLabel("APE896",a.ape896))}})]}},{key:"ykz126",fn:function(e){var a=e.row;return["5"==a.ykz126?i("span",[t._v("中心端")]):i("span",[t._v("医院端")])]}},{key:"ape804",fn:function(e){var a=e.row;return[null==a.akb065?i("span",[t._v("-")]):i("span",[t._v(" "+t._s(a.akb065)+" ")])]}}])})],1),i("div",{staticClass:"content-box-footer"},[i("ta-pagination",{ref:"infoPageRef",staticStyle:{position:"absolute",right:"6px",bottom:"6px","margin-right":"100px"},attrs:{dataSource:t.infoTableData,params:t.infoPageParams,defaultPageSize:100,pageSizeOptions:["100","200","500","1000","1500"],url:"outpatientClinic/queryAuditResults"},on:{"update:dataSource":function(e){t.infoTableData=e},"update:data-source":function(e){t.infoTableData=e}}}),i("ta-button-group",{staticStyle:{position:"absolute",right:"6px",bottom:"6px"}},[i("ta-button",{attrs:{type:"primary",icon:"download"},on:{click:t.exportExcel}},[t._v("导出")])],1)],1)],1),i("div",{attrs:{id:"info"}},[i("ta-modal",{staticStyle:{top:"10px"},attrs:{width:"95%",bodyStyle:{paddingBottom:"1px"},"destroy-on-close":!0,footer:null,getContainer:t.getModalContainer,wrapClassName:"out-modal-wrap"},on:{cancel:t.handleCancel},model:{value:t.visible,callback:function(e){t.visible=e},expression:"visible"}},[i("ta-icon",{staticStyle:{color:"black"},attrs:{slot:"closeIcon",type:"close"},slot:"closeIcon"}),i("span",{staticStyle:{"font-weight":"normal","font-size":"14px","text-align":"center",height:"0px",width:"100%"},attrs:{slot:"title"},slot:"title"},[t._v(" 审核详情")]),i("atient-details",{attrs:{fyRecord:t.bfRecord}})],1)],1),i("div",[i("ta-modal",{attrs:{title:"配置菜单项",height:"355px",width:"385px"},on:{ok:t.handleConfigOk,cancel:t.handleConfigCancel},model:{value:t.configVisible,callback:function(e){t.configVisible=e},expression:"configVisible"}},[i("span",[t._v("配置查询条件:")]),i("br"),i("ta-checkbox-group",{attrs:{value:t.checkedList},on:{change:t.onConfigChange}},[i("ta-row",t._l(t.configList,(function(e,a){return i("ta-col",{key:a,attrs:{span:8}},[i("ta-checkbox",{attrs:{value:e.value}},[t._v(t._s(e.label))])],1)})),1)],1),i("div",[i("ta-checkbox",{attrs:{indeterminate:t.indeterminate,checked:t.checkAll},on:{change:t.onCheckAllChange}},[t._v(" 全部选中 ")])],1),i("div",[i("strong",{style:{marginRight:8}},[t._v("排序模式:")]),i("br"),i("ta-radio-group",{model:{value:t.ordermod,callback:function(e){t.ordermod=e},expression:"ordermod"}},[i("ta-radio",{attrs:{value:"ASC"}},[t._v("正序")]),i("ta-radio",{attrs:{value:"DESC"}},[t._v("倒序")])],1)],1),i("div",[i("strong",{style:{marginRight:8}},[t._v("选择排序顺序:")]),i("br"),t._l(t.tags,(function(e,a){return[i("ta-checkable-tag",{key:e,attrs:{checked:t.selectedTags.indexOf(e)>-1},on:{change:function(a){return t.handleTagChange(e,a)}}},[t._v(" "+t._s(e)+" ")])]}))],2),i("div",[i("strong",{style:{marginRight:8}},[t._v("排序顺序展示:")]),i("br"),t._l(t.tags2,(function(e,a){return[i("ta-tooltip",{key:e,attrs:{title:e}},[i("ta-tag",{key:e,attrs:{closable:-1!==a,afterClose:function(){return t.handleTagClose(e)}}},[t._v(" "+t._s(e.slice(0,-1))+" ")])],1)]}))],2)],1)],1)])],1)},l=[],s=a(66347),o=a(89584),n=a(48534),r=a(95082),c=(a(36133),a(88412)),u=a(362),d=a(36797),f=a.n(d),h=a(83231),m=a(22722),p=a(55115);p.w3.prototype.Base=Object.assign(p.w3.prototype.Base,(0,r.Z)({},m.Z));var v=[],g=[],b={name:"outpatientClinic",components:{TaTitle:c.Z,atientDetails:u["default"]},data:function(){var e=[{label:"开单科室",value:"aaz307"},{label:"诊疗小组",value:"aaz309"},{label:"医护操作",value:"operate"},{label:"规则大类",value:"aaz319"},{label:"规则分类",value:"ruleType"},{label:"医师名称",value:"aaz263"},{label:"患者信息",value:"patientInfo"},{label:"医保项目",value:"projectInfo"},{label:"险种类型",value:"aae140"},{label:"预审结果",value:"result"},{label:"三目类别",value:"ake003"},{label:"院内项目",value:"inProjectInfo"},{label:"违规来源",value:"ykz126Visible"},{label:"知识元",value:"ykz018"}],t=[{type:"seq",title:"序号",width:"60",align:"center"},{title:"医院编码",field:"akb020",align:"left",width:80,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"医院名称",field:"akb021",align:"left",visible:!1},{title:"开单科室",field:"aae386",align:"left",width:100,sortable:!0,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"开单医师",field:"aac003",align:"left",width:80,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"院内项目名称",field:"ake006",align:"left",minWidth:150,sortable:!0,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"医保项目名称",field:"ake002",sortable:!0,align:"left",width:130,overflowTooltip:!0,customRender:{default:"ake002"},customHeaderCell:this.fnCustomHeaderCell},{title:"预审结果",field:"ape800",sortable:!0,align:"center",width:110,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell,scopedSlots:{customRender:"ape800"},customRender:{default:"ape800"}},{title:"金额",field:"akb065",align:"right",width:80,overflowTooltip:!0,customRender:{default:"akb065"},customHeaderCell:this.fnCustomHeaderCell},{title:"项目引导信息",field:"ydxx",align:"left",width:300,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"知识元",field:"ykz018",align:"left",width:300,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"医护操作",field:"ape893",align:"left",width:120,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell,customRender:{default:"operate2"}},{title:"操作人",field:"ykz041",align:"left",width:90,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"登记号",field:"akc190",align:"left",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"就诊号",field:"akc191",align:"left",sortable:!0,width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"患者姓名",field:"name",align:"left",width:100,sortable:!0,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"性別",field:"aac004",align:"left",width:80,sortable:!0,collectionType:"AAC004",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"年龄",field:"age",align:"left",width:80,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"主诊断",field:"diag_main",align:"left",visible:!1},{title:"其他诊断",field:"diag_others",align:"left",visible:!1},{title:"险种类型",field:"aae140",align:"left",width:100,sortable:!0,collectionType:"AAE140",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"医保编码",field:"ake001",align:"left",width:90,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"院内编码",field:"akc515",align:"left",width:90,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"规则大类",field:"aaa167",sortable:!0,align:"left",width:130},{title:"规则分类",field:"ruleType",align:"center",visible:!1,width:130},{title:"违规来源",field:"ykz126",align:"left",width:100,overflowTooltip:!0,customRender:{default:"ykz126"},customHeaderCell:this.fnCustomHeaderCell},{title:"项目发生时间",field:"aae036",align:"center",width:130,sorter:function(e,t){return e.aae036>t.aae036},sortable:!0,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"院内审批标志",field:"ape896",align:"center",width:140,customRender:{default:"ape896"},filterMethod:this.filterApeMethod,filters:[],overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"单价",field:"akc225",align:"right",width:80,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"规格",field:"aka074",align:"left",width:80,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"数量",field:"akc226",align:"right",width:80,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"数量单位",field:"ake130",align:"left",width:80,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"违规数量",field:"ape805",align:"left",visible:!1,formatter:h.Z.moneyNumFormat},{title:"违规金额",field:"ape804",align:"left",visible:!1,customRender:{default:"ape804"}},{title:"医疗费用总额",field:"akc264",align:"left",visible:!1},{title:"操作",field:"operate",align:"center",width:150,fixed:"right",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell,scopedSlots:{customRender:"operate"},customRender:{default:"operate"}}];return{col:{xs:1,sm:2,md:2,lg:4,xl:4,xxl:4},rangeValue:[this.Base.getMoment((new Date).toISOString().slice(0,10),"YYYY-MM-DD"),this.Base.getMoment((new Date).toISOString().slice(0,10),"YYYY-MM-DD")],hosList:[],onRadioValue:"audit",ksList:[],codeArray:[],groupList:[],doctorList:[],ruleList:[],treeData:[],ksTableData:v,infoColumns:t,infoTableData:g,akb020:"",aaz307:"",expandedRowKeys:[],permissions:null,ykz126Visible:!1,paramsDisable:{akb020:!1,aaz307:!1,aaz263:!1},menuConfig:{aaz307:!0,aaz309:!0,operate:!0,aaz319:!0,ruleType:!0,aaz263:!0,patientInfo:!0,projectInfo:!0,aae140:!0,result:!0,ake003:!0,inProjectInfo:!0,ykz018:!0,ykz126Visible:!1},visible:!1,configVisible:!1,indeterminate:!1,checkAll:!0,checkedList:[],bfcheckedList:[],ykz018RpcList:[],configList:e,bfRecord:{},formShowAll:!0,rulelessShow:!1,resultDisabledOne:!1,resultDisabledTwo:!1,resultDisabledThree:!1,aaz319Select:!1,operateSelect:!1,tags:["费用明细时间","项目编码","患者就诊号","开单科室"],tags2:[],selectedTags:[],ordermod:"ASC",showHiddenOrSortColumn:{themeColor:"red",icon:"appstore",showHiddenConfig:{open:!0,disabledHiddenColumns:function(e){return!1}},columnSortConfig:{open:!0,onMove:function(e,t){t.dragColumn,t.dropColumn;return!0},dragEnd:function(e,t){t.dragColumn,t.resultColumns}},successCallback:this.fnSaveTableTitle,disabledControlCol:function(e){}},allReceiveData:{}}},created:function(){var e=this;return(0,n.Z)(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$nextTick((function(){e.fnQueryTableTitle()}));case 2:case"end":return t.stop()}}),t)})))()},mounted:function(){var e=this;return(0,n.Z)(regeneratorRuntime.mark((function t(){var a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.$route.query.params&&(a=JSON.parse(e.$route.query.params),a.dateFlag&&e.onRadioChange(a.dateFlag),a.allDate=a.allDate.map((function(t){var a=new Date(t),i=new Date;return a.setHours(0,0,0,0),i.setHours(0,0,0,0),a!=i&&a.setDate(a.getDate()+1),e.Base.getMoment(a.toISOString(),"YYYY-MM-DD")})),e.$route.query.aaz307&&(a.aaz307=e.$route.query.aaz307),e.$route.query.aaz263&&(a.aaz263=e.$route.query.aaz263),e.$route.query.ake002&&(a.projectInfo=e.$route.query.ake002),e.$route.query.name&&(a.patientInfo=e.$route.query.name),e.allReceiveData=a,e.baseInfoForm.setFieldsValue(a)),t.next=4,h.Z.permissionCheck();case 4:e.permissions=t.sent,e.fnQueryHos(),e.fnQueryRuleList("1"),e.getTreeData(),e.fnQueryAPE896(),e.ykz018Search();case 10:case"end":return t.stop()}}),t)})))()},computed:{formBoxStyle:function(){return this.formShowAll?{height:"auto"}:{height:"50px"}}},methods:{onRadioChange:function(e){this.onRadioValue=e},ykz018Search:function(){var e=this;Base.submit(null,{url:"/mtt/api/ruleSearch/queryYkz018RpcList",data:{rpcVal:""}},{successCallback:function(t){e.ykz018RpcList=t.data.resultData}})},handleTagClose:function(e){this.handleTagChange(e.slice(0,-1),!1)},handleTagChange:function(e,t){var a=this.selectedTags;this.selectedTags=t?[].concat((0,o.Z)(a),[e]):a.filter((function(t){return t!==e})),t?this.tags2.push(e+"x"):this.tags2=this.tags2.filter((function(t){return t!==e+"x"}))},handleConfigOk:function(e){var t=this;this.configVisible=!1,this.bfcheckedList=this.checkedList,this.configList.forEach((function(e){t.menuConfig[e.value]=t.checkedList.includes(e.value)}));var a=top.indexTool.getActiveTabMenuId(),i=top.indexTool.getUserInfo().loginId,l={flag:"menu",menu:JSON.stringify(this.checkedList),sorting:JSON.stringify(this.tags2),ordermod:this.ordermod,resourceid:a,loginid:i};h.Z.insertTableColumShow(l,(function(e){"success"===e?t.$message.success("保存成功"):t.$message.error("保存失败")}))},handleConfigCancel:function(){this.visible=!1,this.checkedList=this.bfcheckedList,this.bfcheckedList.length<this.configList.length&&0!==this.bfcheckedList.length?(this.checkAll=!1,this.indeterminate=!0):0===this.bfcheckedList.length?(this.checkAll=!1,this.indeterminate=!1):(this.checkAll=!0,this.indeterminate=!1)},onConfigChange:function(e){e.length<this.configList.length&&0!==e.length?(this.checkAll=!1,this.indeterminate=!0):0===e.length?(this.checkAll=!1,this.indeterminate=!1):(this.checkAll=!0,this.indeterminate=!1),this.checkedList=e},onCheckAllChange:function(e){var t=e.target.checked;t?(this.checkAll=!0,this.checkedList=this.configList.map((function(e){return e.value}))):(this.checkAll=!1,this.checkedList=[]),this.indeterminate=!1},configMenu:function(){this.configVisible=!0,this.bfcheckedList===this.checkedList?this.bfcheckedList=this.checkedList:this.checkedList=this.bfcheckedList,this.onConfigChange(this.checkedList)},handleChange:function(e){0===e.length?(this.resultDisabledTwo=!1,this.resultDisabledThree=!1,this.resultDisabledOne=!1,this.operateSelect=!1,this.aaz319Select=!1):e.includes("0")?(this.resultDisabledTwo=!0,this.resultDisabledThree=!0,this.resultDisabledOne=!1):e.includes("4")&&(this.resultDisabledTwo=!0,this.resultDisabledThree=!1,this.resultDisabledOne=!0)},handleSelect:function(e){"0"===e?this.baseInfoForm.setFieldsValue({result:["0"]}):"4"===e&&(this.baseInfoForm.setFieldsValue({result:["4"]}),this.baseInfoForm.resetFields(["operate","aaz319"]),this.operateSelect=!0,this.aaz319Select=!0)},fnQueryAPE896:function(){var e=this;this.Base.submit(null,{url:"miimCommonRead/queryDict",data:{type:"APE896"},autoValid:!1},{successCallback:function(t){e.codeArray=t.data.codeList.map((function(e){return{label:e.label,value:e.value}})),e.$refs.infoTableRef.setFilter(e.$refs.infoTableRef.getColumnByField("ape896"),e.codeArray),e.$refs.infoTableRef.updateData(),e.allReceiveData.allDate&&e.fnQuery()},failCallback:function(t){e.$message.error("码表数据加载失败")}})},fnQueryTableTitle:function(){var e=this,t=top.indexTool.getActiveTabMenuId(),a=top.indexTool.getUserInfo().loginId,i=[];this.Base.submit(null,{url:"miimCommonRead/selectTableColumShow",data:{resourceid:t,loginid:a},autoValid:!1},{successCallback:function(t){if(t.data.list.length>0&&t.data.list[0].ordermod&&(e.ordermod=t.data.list[0].ordermod),t.data.list.length>0&&t.data.list[0].sorting&&(e.selectedTags=JSON.parse(t.data.list[0].sorting).map((function(e){return e.slice(0,-1)})),e.tags2=JSON.parse(t.data.list[0].sorting)),t.data.list.length>0&&t.data.list[0].menu?e.checkedList=JSON.parse(t.data.list[0].menu):e.checkedList=e.configList.map((function(e){return e.value})),e.bfcheckedList=e.checkedList,e.configList.forEach((function(t){e.menuConfig[t.value]=e.checkedList.includes(t.value)})),t.data.list.length>0&&t.data.list[0].colum){var a=JSON.parse(t.data.list[0].colum);i=a.map((function(e){return e.title}));var l=e.$refs.infoTableRef.getTableColumn().fullColumn,s=[];l.forEach((function(e){var i=a.find((function(t){return t.title===e.title}));i&&(e.visible=i.visible);var l=e;"操作"!=l.title&&"项目引导信息"!=l.title&&"知识元"!=l.title&&(l.sortable?l.width=20*l.title.length+30:l.width=20*l.title.length+12),t.data.akc191Title.length>0&&"akc191"===l.property&&(l.title=t.data.akc191Title[0].label),s.push(l)})),i.length>0&&s.sort((function(e,t){return"序号"===e.title?-1:"序号"===t.title?1:i.indexOf(e.title)-i.indexOf(t.title)})),e.$refs.infoTableRef.loadColumn(s)}"2"===t.data.auditEngType||"3"===t.data.auditEngType?(e.menuConfig.ykz126Visible=!0,e.$refs.infoTableRef.showColumn(e.$refs.infoTableRef.getColumnByField("ykz126"))):(e.menuConfig.ykz126Visible=!1,e.checkedList=e.checkedList.filter((function(e){return"ykz126Visible"!==e})),e.bfcheckedList=e.checkedList,e.$refs.infoTableRef.hideColumn(e.$refs.infoTableRef.getColumnByField("ykz126")))},failCallback:function(t){e.$message.error("查询表头失败")}})},fnSaveTableTitle:function(e){var t=this,a=(e.table,e.resultColumnsList),i=a,l=[];i.forEach((function(e){var t=e.title,a=e.visible;e.type;t&&l.push({title:t,visible:a})}));var s=top.indexTool.getActiveTabMenuId(),o=top.indexTool.getUserInfo().loginId,n={colum:JSON.stringify(l),flag:"column",resourceid:s,loginid:o};h.Z.insertTableColumShow(n,(function(e){"success"===e?t.$message.success("保存成功"):t.$message.error("保存失败")}))},filterApeMethod:function(e){var t=e.value,a=(e.option,e.row);e.column;return a.ape896==t},fnQueryRuleList:function(e){var t=this,a={url:"/miimCommonRead/queryRuleTypeDicByAae500",autoValid:!0,data:{aae500:e}},i={successCallback:function(e){t.ruleList=e.data.ruleList},failCallback:function(e){t.$message.error("医院数据加载失败")}};this.Base.submit(null,a,i)},getTreeData:function(){var e=this;Base.submit(null,{url:"mttRuleCustom/getRuleTreeData"}).then((function(t){e.treeData=t.data.list,e.treeData.length>0&&(e.treeData[0].disabled=!0,e.treeData[0].children.forEach((function(e){e.disabled=!0})))}))},fnReset:function(){this.baseInfoForm.resetFields(),this.fnQuery(),this.formShowAll=!0},formShowAllChange:function(){this.formShowAll=!this.formShowAll},openAuditDetails:function(e){e.aae500="1",this.bfRecord=e,this.visible=!0},handleCancel:function(){this.visible=!1,this.showAll=!1},getModalContainer:function(){return document.getElementById("info")},fnSearch:function(e){var t="1";this.Base.openTabMenu({id:e.akb020+e.aaz217,name:"【"+e.name+"】患者详情",url:"detailsQuery.html#/expenseDetailsForOther?akb020=".concat(e.akb020,"&akc190=").concat(e.akc190,"&aaz217=").concat(e.aaz217,"&flag=")+t,refresh:!1})},fnQueryHos:function(){var e=this,t={url:"/miimCommonRead/queryHospRpcList",autoValid:!0},a={successCallback:function(t){if(e.hosList=t.data.resultData,void 0!==e.allReceiveData.akb020){var a=e.allReceiveData.akb020;a?(e.hosList=e.hosList.filter((function(e){return e.value==a})),e.baseInfoForm.setFieldsValue({medinsCode:a})):e.baseInfoForm.resetFields("medinsCode"),e.akb020=a}else e.baseInfoForm.setFieldsValue({medinsCode:e.hosList[0].value}),e.permissions&&e.permissions.akb020Set.size>0&&(e.hosList=e.hosList.filter((function(t){return e.permissions.akb020Set.has(t.value)})),e.baseInfoForm.setFieldsValue({medinsCode:e.hosList[0].value})),e.akb020=e.hosList[0].value;e.fnQueryDept(e.akb020)},failCallback:function(t){e.$message.error("医院数据加载失败")}};this.Base.submit(null,t,a)},fnQueryDept:function(e){var t=this;this.akb020=e||"",this.Base.submit(null,{url:"miimCommonRead/queryDepartRpcList",data:{akb020:this.akb020,hospDeptType:"C"},autoValid:!1},{successCallback:function(e){var a=t.allReceiveData.aaz307;a||t.baseInfoForm.resetFields("aaz307"),t.ksList=e.data.resultData,t.fnQueryGroup()},failCallback:function(e){t.$message.error("科室数据加载失败")}})},fnQueryGroup:function(e){var t=this,a={akb020:this.akb020};e&&(a.aaz307=e),this.permissions.aaz307Disable&&(a.aaz307=this.permissions.aaz307Set.values().next().value),a.aaz307?this.Base.submit(null,{url:"miimCommonRead/queryGroupDic",data:a,autoValid:!1},{successCallback:function(e){t.baseInfoForm.resetFields("aaz309"),t.groupList=e.data.resultData,t.fnQueryDocter()},failCallback:function(e){t.groupList=[],t.$message.error("医师数据加载失败")}}):(this.groupList=[],this.fnQueryDocter())},fnQueryDocter:function(e){var t=this,a={akb020:this.akb020};this.permissions.aaz307Disable&&(a.departCode=this.permissions.aaz307Set.values().next().value),this.Base.submit(null,{url:"miimCommonRead/queryDoctorRpcList",data:a,autoValid:!1},{successCallback:function(e){var a=t.allReceiveData.aaz263;a||t.baseInfoForm.resetFields("aaz263"),t.doctorList=e.data.resultData,t.setPermission()},failCallback:function(e){t.$message.error("医师数据加载失败")}})},setPermission:function(){var e=this;this.paramsDisable.akb020=this.permissions.akb020Disable,this.paramsDisable.aaz307=this.permissions.aaz307Disable,this.paramsDisable.aaz263=this.permissions.aaz263Disable,this.permissions&&(this.permissions.aaz307Set.size>0&&(this.ksList=this.ksList.filter((function(t){return e.permissions.aaz307Set.has(t.value)})),this.permissions.aaz307Disable&&this.baseInfoForm.setFieldsValue({aaz307:this.ksList[0].value})),this.permissions.aaz309Set.size>0&&(this.groupList=this.groupList.filter((function(t){return e.permissions.aaz309Set.has(t.value)}))),this.permissions.aaz263Set.size>0&&(this.doctorList=this.doctorList.filter((function(t){return e.permissions.aaz263Set.has(t.value)})),this.permissions.aaz263Disable&&this.baseInfoForm.setFieldsValue({aaz263:this.doctorList[0].value})))},disabledStartDate:function(e){return e.format("YYYYMMDD")>this.baseInfoForm.getFieldValue("endDate").format("YYYYMMDD")},disabledEndDate:function(e){return e=e.format("YYYYMMDD"),e>f()().startOf("day").format("YYYYMMDD")||e<this.baseInfoForm.getFieldValue("startDate").format("YYYYMMDD")},infoPageParams:function(){var e=this.baseInfoForm.getFieldsValue(),t=e.allDate;if(!t||t[0]&&t[1])return e.startDate=e.allDate[0].format("YYYY-MM-DD"),e.endDate=e.allDate[1].format("YYYY-MM-DD"),e.akb020=e.medinsCode,e.orderParam=this.tags2,e.ykz018List&&(e.ykz018List=e.ykz018List.join("|")),e.orderMod=this.ordermod,e.dateFlag=this.onRadioValue,e;this.$message.error("请选择时间范围！")},fnQuery:function(){var e=this,t=this.infoPageParams();t&&(this.formShowAll=!1,this.$nextTick((function(){e.$refs.infoPageRef.loadData((function(a){e.rulelessShow=!!t.result.includes("4")}))})))},exportExcel:function(){var e=this,t=this.infoPageParams();if(t){var a,i=t.startDate,l=t.endDate,o=[],n=this.$refs.infoTableRef.getColumns(),r=(0,s.Z)(n);try{for(r.s();!(a=r.n()).done;){var c=a.value;"seq"!==c.type&&"operate"!==c.property&&!1!==c.visible&&(o.push({header:c.title,key:c.property,width:20}),"ape893"===c.property&&o.push({header:"操作内容",key:"aaz560_1",width:20}))}}catch(h){r.e(h)}finally{r.f()}var u=n.map((function(e){return e.property})),d=[{codeType:"AAC004",columnKey:"aac004"},{codeType:"AAE140",columnKey:"aae140"},{columnKey:"ape896",customCollection:function(e,t){"0"==e.value||"1"==e.value?e.value="—":"2"==e.value?e.value="自费":"3"==e.value?e.value="报销":""!=e.value&&null!=e.value||(e.value="—")}},{columnKey:"ape800",customCollection:function(e,t){"0"==e.value?e.value="审核通过":"1"==e.value?e.value="可疑提醒":"2"==e.value?e.value="违规提醒":"3"==e.value&&(e.value="仅提醒")}},{columnKey:"ape893",customCollection:function(e,t){var a=document.createElement("div");a.innerHTML=e.value;var i=a.innerText||a.textContent;a=null,e.value=i}},{columnKey:"ykz126",customCollection:function(e,t){"5"==e.value?e.value="中心端":e.value="医院端"}}],f=d.filter((function(e){return u.includes(e.columnKey)}));this.Base.submit(null,{url:"outpatientClinic/exportExcel",data:t,autoValid:!1},{successCallback:function(t){var a=t.data.data;a.forEach((function(e){e.diagnoses&&Object.assign(e,e.diagnoses),e.akb065&&(e.ape804=e.akb065),e.ape893real.indexOf("1")>-1&&(e.aaz560_1=e.ape893.split(/[:：]/)[1],e.ape893=e.ape893.split(/[:：]/)[0]),e.ape893real.indexOf("4")>-1&&(e.aaz560_1=e.ape893.split(/[:：]/)[1],e.ape893=e.ape893.split(/[:：]/)[0])}));var s={fileName:"普通门诊开单提醒查询结果表("+i+"-"+l+")",sheets:[{name:"worksheet1",column:{complex:!1,columns:o},rows:a,codeList:f}]};e.Base.generateExcel(s)},failCallback:function(t){e.$message.error("医师数据加载失败")}})}},fnCustomHeaderCell:function(e){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px",fontWeight:"bold"}}},filterOption:function(e,t){return t.componentOptions.children[0].text.indexOf(e)>=0}}},C=b,A=a(1001),y=(0,A.Z)(C,i,l,!1,null,"f3d866de",null),k=y.exports},362:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return f}});var i=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"fit"},[i("div",[i("span",{staticStyle:{"font-weight":"bold","font-size":"14px",color:"#333333","margin-left":"10px"}},[i("ta-badge",{attrs:{color:e.badgeColor,text:"项目："}}),e._v(" "+e._s(e.fyRecord.ake002)+" "),i("span",{staticStyle:{"margin-right":"80px"}}),i("ta-badge",{attrs:{color:e.badgeColor,text:"明细总数量："}}),e._v(" "+e._s(e.fyRecord.akc226)+" "),(1==e.fyRecord.ykz020&&e.fyRecord.aae500&&e.fyRecord.aae500,i("span",[i("span",{staticStyle:{"margin-right":"80px"}}),i("ta-badge",{attrs:{color:e.badgeColor,text:"建议扣除数量："}}),e._v(" "+e._s(e.fyRecord.ape805)+" ")],1))],1),i("div",{staticStyle:{"margin-top":"0px","border-top":"0px solid #cccccc","padding-top":"5px"}},[i("ta-tabs",{attrs:{defaultActiveKey:"1"}},e._l(e.doubtList,(function(t,l){return i("ta-tab-pane",{key:l+1},[i("span",{attrs:{slot:"tab"},on:{click:function(a){return e.cardChange(t.id)}},slot:"tab"},[i("img",{attrs:{src:"1"===t.ykz020?a(60037):a(92206)}}),i("span",{staticClass:"tab-title",on:{click:function(a){return e.cardChange(t.id)}}},[e._v(e._s(t.aaa167))])]),i("p",{staticClass:"tab-content",on:{click:function(a){return e.cardChange(t.id)}}},[i("span",{staticClass:"tab-text-label"},[e._v("规则来源：")]),i("span",{staticClass:"tab-text"},[e._v(e._s(t.source))]),i("br"),i("span",{staticClass:"tab-text-label"},[e._v("限制条件：")]),i("span",{staticClass:"tab-text"},[e._v(e._s(t.ykz018))]),i("a",{directives:[{name:"show",rawName:"v-show",value:e.isRuleQuery,expression:"isRuleQuery"}],staticStyle:{"margin-left":"10px"},on:{click:function(a){return e.ruleDetails(t)}}},[e._v(">>规则详情")])])])})),1)],1)]),i("div",{staticClass:"audit-container"},[e._m(0),i("div",{staticClass:"audit-content",attrs:{id:"wai"}},[i("div",{staticClass:"divleft"},[i("div",{staticClass:"header"},[e._v("审核路径")]),i("table",{staticClass:"audit-table"},[e._m(1),i("tbody",e._l(e.auditPathList,(function(t,a){return i("tr",{key:a,staticClass:"audit-row"},[i("td",{staticClass:"audit-index"},[e._v(e._s(a+1))]),i("td",{staticClass:"audit-detail"},e._l(t.nodeInfoList,(function(l,s){return i("span",{key:s,staticClass:"audit-node-container"},[s>0&&s<t.nodeInfoList.length?i("span",[e._v("——")]):e._e(),i("span",{staticClass:"audit-node",style:{color:"3"===l.ykz020?e.colors[0]:e.colors[l.ykz020]},attrs:{tabindex:a+1},on:{click:function(t){return e.nodeChange(l)}}},[e._v(" "+e._s(l.ykz010)+" "),i("span",[e._v("("+e._s(l.ykz002)+")")])])])})),0)])})),0)])]),i("div",{staticClass:"divright"},[i("div",{staticClass:"header"},[e._v("节点详情")]),i("div",{staticClass:"right-content"},[i("div",{staticClass:"right-item"},[e._m(2),i("div",{staticClass:"right-item-content",domProps:{innerHTML:e._s(e.nodeDetail.ykz006)}})]),i("div",{staticClass:"right-item"},[e._m(3),i("div",{staticClass:"right-item-content",domProps:{innerHTML:e._s(e.nodeDetail.ykz008)}})]),i("div",{staticClass:"right-item"},[e._m(4),i("div",{staticClass:"right-item-content",domProps:{innerHTML:e._s(e.nodeDetail.ykz021)}})]),i("div",{staticClass:"right-item"},[e._m(5),i("div",{staticClass:"right-item-content",domProps:{innerHTML:e._s(e.nodeDetail.ykz022)}})])])])])])])},l=[function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"audit-info"},[i("img",{staticClass:"info-icon",attrs:{src:a(67488)}}),i("span",[e._v("审核结果信息")]),i("span",{staticClass:"status-red"},[e._v("红色")]),e._v("节点表示不通过， "),i("span",{staticClass:"status-green"},[e._v("绿色")]),e._v("节点表示通过 ")])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("thead",[a("tr",[a("th",{staticClass:"audit-index"},[e._v("序号")]),a("th",{staticClass:"audit-details"},[e._v("路径审核详情")])])])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"right-item-title"},[e._v("机审"),a("br"),e._v("记录")])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"right-item-title"},[e._v("引导"),a("br"),e._v("信息")])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"right-item-title"},[e._v("人工"),a("br"),e._v("操作")])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"right-item-title"},[e._v("人工"),a("br"),e._v("审核"),a("br"),e._v("理由")])}],s=a(95082),o=a(66353),n=["id"],r={name:"atientDetails",props:{fyRecord:Object},data:function(){return{visible:!1,doubtList:[],auditPathList:[],nodeDetail:{},isRuleQuery:!1,colors:["#D75457","#46C26D","#3693F3"],aaz217:"",akb020:"",flag:"",badgeColor:"#3693F3"}},mounted:function(){this.handleShowInfo(this.fyRecord)},methods:{ruleDetails:function(e){var t,a,i=this;(null===(t=e.nodeDetailVoList)||void 0===t?void 0:t.length)>0?this.Base.submit(null,{url:"mttRuleFind/queryRuleid",data:{ykz108:e.nodeDetailVoList[0].ykz108}},{successCallback:function(t){a=t.data.data[0].ruleid,i.Base.openTabMenu({id:e.ykz018,name:"【"+e.ykz018+"】规则查询",url:"mttRuleConfig.html#/ruleFind?ykz010=".concat(e.ykz018,"&ruleid=").concat(a),refresh:!1})},failCallback:function(e){i.$message.error("规则ID失败")}}):this.$message.error("审核详情缺少节点信息！请检查审核详情！")},handleShowInfo:function(e){var t=this;this.fyRecord=e,this.visible=!0,this.Base.submit(null,{url:"miimCommonRead/queryDoubtList",data:{aaz217:e.aaz217,aaz213:e.aaz213,aae500:e.aae500,type:e.type},autoValid:!0,autoQs:!1},{successCallback:function(e){e.data.list.length>0&&("monitor"===e.data.flag?e.data.list.forEach((function(e,t){for(var a in e.auditPathList){var i=e.auditPathList[a].nodeInfoList.every((function(e){return"1"===e.ykz020}));i&&(e.ykz020="1")}})):e.data.list.forEach((function(e,t){for(var a in e.auditPathList){e.auditPathList[a].nodeInfoList.forEach((function(e,t){e.ykz020=e.ykz173,e.ykz021?e.ykz021="1"===e.ykz021?"是":"否":e.ykz021="无"}));var i=e.auditPathList[a].nodeInfoList.every((function(e){return"1"===e.ykz173}));i&&(e.ykz020="1")}})),e.data.list.sort((function(e,t){var a=parseInt(e.ykz020,10),i=parseInt(t.ykz020,10);return 0===a||3===a?-1:0===i||3===i?1:a-i}))),t.doubtList=e.data.list.map((function(e,t){e.id;var a=(0,o.Z)(e,n);return(0,s.Z)({id:t+1},a)})),t.auditPathList=[],t.nodeDetail={},t.doubtList.length>0&&(t.auditPathList=t.doubtList[0].auditPathList),e.data.ruleQuery&&(t.isRuleQuery="y"==e.data.ruleQuery.toLowerCase())},failCallback:function(e){}})},cardChange:function(e){var t=e-1;this.auditPathList=this.doubtList[t].auditPathList},nodeChange:function(e){this.nodeDetail=e}}},c=r,u=a(1001),d=(0,u.Z)(c,i,l,!1,null,"e9de457e",null),f=d.exports},36766:function(e,t,a){"use strict";var i=a(66586);t["Z"]=i.Z},26263:function(e,t,a){"use strict";a.d(t,{s:function(){return i},x:function(){return l}});var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:e.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[e._v(e._s(e.title))]),e._t("default")],2)},l=[]},66586:function(e,t){"use strict";var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};t["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}},83231:function(e,t,a){"use strict";var i=a(48534);a(36133);function l(e){return e?e.akb020&&e.aaz263?"personal":e.akb020&&e.aaz309&&e.aaz307?"group":e.akb020&&e.aaz307?"department":e.akb020?"hospital":"all":"all"}function s(e){return o.apply(this,arguments)}function o(){return o=(0,i.Z)(regeneratorRuntime.mark((function e(t){var a,i,s,o,n,r,c,u,d,f,h,m;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,Base.submit(null,{url:"/miimCommonRead/queryDataPermission",showPageLoading:!1,data:{jobnumber:t},autoValid:!0});case 2:return a=e.sent,i=new Set,s=new Set,a.data.permission.forEach((function(e){var t=l(e);"hospital"===t&&i.add(e.akb020),"department"===t&&s.add(e.aaz307)})),o=a.data.permission.filter((function(e){return"department"===l(e)||!s.has(e.aaz307)})).filter((function(e){return"hospital"===l(e)||!i.has(e.akb020)})),n=new Set(o.map((function(e){return e.akb020})).filter((function(e){return null!=e}))),r=new Set(o.map((function(e){return e.aaz307})).filter((function(e){return null!=e}))),c=new Set(o.map((function(e){return e.aaz309})).filter((function(e){return null!=e}))),u=new Set(o.map((function(e){return e.aaz263})).filter((function(e){return null!=e}))),d=!1,f=!1,h=!1,m=!1,1===n.size&&(d=!0),1===r.size&&1===n.size&&(f=!0),1===r.size&&1===n.size&&1===c.size&&(h=!0),1===n.size&&0===r.size&&1===u.size&&(m=!0),e.abrupt("return",{akb020Set:n,aaz307Set:r,aaz263Set:u,aaz309Set:c,akb020Disable:d,aaz307Disable:f,aaz263Disable:m,aaz309Disable:h});case 20:case"end":return e.stop()}}),e)}))),o.apply(this,arguments)}function n(e,t){Base.submit(null,{url:"miimCommonRead/getAa01AAE500StartStop",data:e},{successCallback:function(e){return t(e)}})}function r(e,t){Base.submit(null,{url:"miimCommonRead/insertTableColumShow",data:e,autoValid:!1},{successCallback:function(e){return t("success")},failCallback:function(e){return t("fail")}})}var c={zbx01:{footer:function(){return"统筹金总额"}},zbx02:{footer:function(){return"基金支出总额"}},zbx03:{footer:function(){return"总费用"}},zbx04:{footer:function(){return"总住院日"}}};t["Z"]={permissionCheck:s,getAa01AAE500StartStop:n,insertTableColumShow:r,props:c,moneyNumFormat:function(e){var t=e.cellValue;return t||"—"}}},92206:function(e){"use strict";e.exports="data:image/png;base64,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"},60037:function(e){"use strict";e.exports="data:image/png;base64,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"},67488:function(e){"use strict";e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAEKADAAQAAAABAAAAEAAAAAA0VXHyAAAByklEQVQ4EZVSOywEYRCe+fc8CvHIrbg7RK7xjAQVodBRaCQqhYLCm4hEIQoRWonGEYVOiUIhSgoaQYHQkHgccYsIcW7djvmPvfzn7iRmk51/v/m++eexCEmsYpJSjTvDhwB1/gW9PAkNHIkCntEX/dEfWONYAyUiKFhcAle3UW69fmwwx6vwIkd3z8MhAB5rAGPXC/qNBIVKcvcZzYC0y1icOMJDmOeKWsJAB/n9L8USQyJC78hzVigY7rAIZgGIL4i1u8VcHsW35Q8aBeGQtQ2ImO5ylglPb2An+P75ZBHNJRLbQullC8LEbA0crUBQFLo3ugQR1Kukv84EeGCCNXOzmHOECFusbY+ZwV9iGRMCl7jK2h/ePvuSfyUAi+8lfhT7VwICq5tntxfRE9VwC+eCAf5h0FSSJj2yoIqXNl4w8FjJ82gSCCvR9bh7AxNkwRT3GMXsTOoaCwcePOYn7CCgluZylkZb8Pv0adSgjUVvtvC39/QYnaaJJ0iQiUhNl5MYjCaQZP+8vqppjgZu6+q3WH7zDIb4vSlSRfWtL/dMYnHlStA7/Jr3/hFcB/pemdqCjKsWU4EduJjLuM9IcTZy/mWu5tTGE/kv0KybhGEzosQAAAAASUVORK5CYII="},55382:function(){},61219:function(){}}]);
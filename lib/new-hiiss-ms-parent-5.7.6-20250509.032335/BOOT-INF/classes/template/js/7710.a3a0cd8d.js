"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[7710],{88412:function(t,e,a){var i=a(26263),o=a(36766),l=a(1001),s=(0,l.Z)(o.Z,i.s,i.x,!1,null,"5e7ef0ae",null);e["Z"]=s.exports},13666:function(t,e,a){a.r(e),a.d(e,{default:function(){return x}});var i=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("div",{staticClass:"fit"},[i("ta-border-layout",{attrs:{layout:{footer:"50%",left:"35%"},"left-cfg":{expand:!0,expandText:"",showBar:!0}}},[i("div",{attrs:{slot:"leftExtraContent"},slot:"leftExtraContent"},[i("ta-form",{staticStyle:{"margin-top":"10px"},attrs:{autoFormCreate:function(e){t.deptForm=e},layout:"horizontal"}},[i("ta-row",[i("ta-col",{attrs:{span:12}},[i("ta-form-item",{attrs:{fieldDecoratorId:"admDeptCode",span:12,required:!0}},[i("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("科室")]),i("ta-select",{attrs:{options:e.options},on:{select:e.deptSelect}})],1)],1)],1)],1)],1),i("div",{staticClass:"fit",attrs:{slot:"left"},slot:"left"},[i("ta-title",[e._v("患者列表"),i("span",{staticStyle:{"font-weight":"normal","font-size":"14px"}},[e._v("（请先选择患者）")])]),i("div",{staticStyle:{height:"90%"}},[i("ta-table",{attrs:{columns:e.patientColumns,dataSource:e.patientData,customRow:e.fnDeptRow,bordered:!0,haveSn:!0,size:"small",scroll:{y:"100%",x:"100%"}},on:{"update:columns":function(t){e.patientColumns=t}},scopedSlots:e._u([{key:"action",fn:function(t,a){return i("span",{},[""==a.url||null==a.url?i("span",[e._v("无")]):i("a",{on:{click:function(t){return e.auditResult(a)}}},[e._v("查看")])])}}])})],1)],1),i("div",{staticClass:"fit",staticStyle:{height:"100%"}},[i("ta-tabs",{staticClass:"fit",on:{change:e.tabChange},model:{value:e.activeKey,callback:function(t){e.activeKey=t},expression:"activeKey"}},[i("ta-tab-pane",{key:"order",attrs:{tab:"医嘱"}},[i("ta-table",{ref:"orderRef",attrs:{columns:e.orderColumns,dataSource:e.orderData,customRow:e.fnCustomRow,bordered:!0,haveSn:!0,size:"small",scroll:{y:"100%",x:"100%"}},on:{"update:columns":function(t){e.orderColumns=t}},scopedSlots:e._u([{key:"action",fn:function(t,a){return i("span",{},[i("ta-table-operate",{attrs:{"operate-menu":e.operateMenu}})],1)}}])})],1),i("ta-tab-pane",{key:"diag",attrs:{tab:"诊断"}},[i("ta-table",{ref:"diagRef",attrs:{columns:e.diagColumns,dataSource:e.diagData,customRow:e.fnCustomRow,bordered:!0,haveSn:!0,size:"small",scroll:{y:"100%",x:"100%"}},on:{"update:columns":function(t){e.diagColumns=t}},scopedSlots:e._u([{key:"action",fn:function(t,a){return i("span",{},[i("ta-popconfirm",{attrs:{title:"是否删除?"},on:{confirm:function(){return e.deleteDiag(a)}}},[i("a",{style:{marginLeft:"10px"}},[i("ta-icon",{attrs:{type:"delete"}}),e._v("删除")],1)])],1)}}])})],1),i("ta-tab-pane",{key:"test",attrs:{tab:"检验"}},[e._v(" 待开发 ")]),i("ta-tab-pane",{key:"check",attrs:{tab:"检查"}},[e._v(" 待开发 ")]),i("ta-tab-pane",{key:"surgery",attrs:{tab:"手术"}},[e._v(" 待开发 ")]),i("ta-tab-pane",{key:"bodyTemp",attrs:{tab:"体温"}},[e._v(" 待开发 ")])],1)],1),i("div",{staticClass:"fit",attrs:{slot:"footer"},slot:"footer"},["order"==e.activeKey?i("new-order",{ref:"order",attrs:{patientInfo:e.patientInfo},on:{fnQuery:e.fnQuery,doCheck:e.doCheckWithUrl}}):e._e(),"diag"==e.activeKey?i("new-dise",{ref:"dise",attrs:{patientInfo:e.patientInfo},on:{fnQuery:e.fnQuery}}):e._e()],1)]),i("ta-modal",{attrs:{title:"智能提醒",footer:null,height:"750px",width:"1410px",destroyOnClose:!0},on:{cancel:e.handleCancelZntx},model:{value:e.zntxModal,callback:function(t){e.zntxModal=t},expression:"zntxModal"}},[1==e.show?i("iframe",{staticStyle:{height:"100%",width:"100%"},attrs:{src:e.url,id:"iframe"}}):e._e(),-1==e.show?i("div",{staticStyle:{height:"100%"}},[i("ta-title",{staticStyle:{color:"red"}},[e._v("MESSAGE：")]),i("div",{staticStyle:{border:"1px solid #1b65b9",width:"100%",height:"80%",padding:"15px","font-size":"16px",overflow:"auto"}},[e._v(" "+e._s(e.errMessage)+" ")])],1):e._e()]),i("ta-modal",{attrs:{title:"每晚预审结果查看",footer:null,height:"750px",width:"1410px"},on:{cancel:e.handleCancel2},model:{value:e.mwysModal,callback:function(t){e.mwysModal=t},expression:"mwysModal"}},[i("iframe",{staticStyle:{height:"100%",width:"100%"},attrs:{sandbox:"allow-scripts allow-top-navigation allow-same-origin",src:e.url2,id:"iframe2"}})])],1)},o=[],l=a(88412),s=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("div",{staticClass:"fit"},[i("ta-title",[e._v("新开医嘱")]),i("ta-form",{attrs:{autoFormCreate:function(e){t.orderForm=e},layout:"horizontal"}},[i("ta-row",[i("ta-col",{attrs:{span:5}},[i("ta-form-item",{attrs:{fieldDecoratorId:"ake003",labelCol:{span:8},wrapperCol:{span:16}}},[i("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("项目类型")]),i("ta-select",{attrs:{"collection-type":"AKE003",allowClear:""}})],1)],1),i("ta-col",{attrs:{span:11}},[i("ta-form-item",{attrs:{fieldDecoratorId:"hilist",labelCol:{span:8},wrapperCol:{span:16},required:!0,extra:"回车进行搜索"}},[i("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("项目名称")]),i("ta-input",{attrs:{placeholder:"输入项目名称或编码"},on:{pressEnter:e.handleSearch}})],1)],1),i("ta-col",{attrs:{span:5}},[i("ta-form-item",{attrs:{fieldDecoratorId:"cnt",span:12,labelCol:{span:10},wrapperCol:{span:10},required:!0,"init-value":1}},[i("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("项目数量")]),i("ta-input")],1)],1),i("ta-col",{attrs:{span:3}},[i("ta-button",{staticStyle:{"margin-left":"20px"},attrs:{type:"primary"},on:{click:e.addHilist}},[e._v("+")])],1)],1)],1),i("div",{staticStyle:{height:"calc(100% - 150px)"}},[i("ta-table",{attrs:{columns:e.newOrderColumns,dataSource:e.newOrderData,customRow:e.fnCustomRow,bordered:!0,haveSn:!0,size:"small",scroll:{x:"100%",y:"100%"}},on:{"update:columns":function(t){e.newOrderColumns=t}},scopedSlots:e._u([{key:"action",fn:function(t,a){return i("span",{},[i("ta-popconfirm",{attrs:{title:"是否删除?"},on:{confirm:function(){return e.deleteOrder(a)}}},[i("a",{style:{marginLeft:"10px"}},[i("ta-icon",{attrs:{type:"delete"}}),e._v("删除")],1)])],1)}}])})],1),i("ta-button",{staticStyle:{"margin-left":"calc(50% - 50px)",width:"100px"},attrs:{type:"primary"},on:{click:e.doCheck}},[e._v("提交")]),i("ta-drawer",{ref:"drawer",attrs:{title:"项目选择(单击进行选择）",width:"500",placement:"right",closable:!0,visible:e.visible,destroyOnClose:""},on:{close:e.closeEdit}},[i("ta-table",{staticStyle:{height:"200px"},attrs:{columns:e.projectColumns,dataSource:e.dataSource,customRow:e.fnProjectRow,size:"small"},on:{"update:columns":function(t){e.projectColumns=t}}})],1)],1)},r=[],n=(a(32564),{name:"newOrder",components:{TaTitle:l.Z},props:{patientInfo:{type:Object}},data:function(){var t=[{title:"项目id",dataIndex:"hilistCode",align:"left",width:170,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"项目类型",dataIndex:"listType",align:"center",width:70,collectionType:"AKE003",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"项目名称",dataIndex:"hilistName",align:"left",width:170,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"数量",dataIndex:"cnt",align:"right",width:60,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"单价",dataIndex:"pric",align:"right",width:60,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"开嘱时间",dataIndex:"drordBegntime",align:"center",width:140,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"操作",dataIndex:"action",align:"center",width:50,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell,scopedSlots:{customRender:"action"}}],e=[{title:"项目id",dataIndex:"hilistCode",align:"left",width:160,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"项目名称",dataIndex:"hilistName",align:"left",width:130,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"单价",dataIndex:"pric",align:"right",width:60,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell}];return{projectColumns:e,newOrderColumns:t,newOrderData:[],dataSource:[],dataObject:{flag:!0},visible:!1}},watch:{visible:function(t){var e=this;setTimeout((function(){e.$refs.drawer.$el.style.display=t?"block":"none"}),300)}},methods:{deleteOrder:function(t){this.newOrderData=this.newOrderData.filter((function(e){return e.drordBegntime!==t.drordBegntime}))},closeEdit:function(){this.visible=!1},fnCustomRow:function(t,e){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px"}}},doCheck:function(){this.$emit("doCheck",this.newOrderData)},onSelect:function(t){this.dataObject=t,this.orderForm.setFieldsValue({hilist:t.hilistCode+"--"+t.hilistName}),this.orderForm.setFieldsValue({ake003:t.listType}),this.visible=!1},handleSearch:function(){var t=this;null!=this.orderForm.getFieldValue("hilist")&&""!==this.orderForm.getFieldValue("hilist")?this.Base.submit(null,{url:"doctorOrder/queryProject",data:{ake003:this.orderForm.getFieldValue("ake003"),info:this.orderForm.getFieldValue("hilist")},autoValid:!1},{successCallback:function(e){t.visible=!0,t.dataSource=e.data.list},failCallback:function(e){t.$message.error("项目信息查询失败")}}):this.$message.error("请输入项目名称或编码进行查询！")},addHilist:function(){var t=this.orderForm.getFieldsValue();if(this.patientInfo)if(t.hilist)if(t.cnt)if(this.dataObject.flag)this.$message.error("请按回车键选择项目!");else{var e=/^([1-9][0-9]*)+(\.[0-9]{1,2})?$/;if(e.test(t.cnt)){this.dataObject.cnt=t.cnt;var a=new Date;this.dataObject.drordBegntime=a.getFullYear()+"-"+(a.getMonth()+1)+"-"+a.getDate()+" "+a.getHours()+":"+a.getMinutes()+":"+a.getSeconds(),this.newOrderData.push(this.dataObject),this.dataObject={},this.dataObject.flag=!0,this.orderForm.resetFields()}else this.$message.error("项目数量只能填入数字")}else this.$message.warn("项目数量未填写!");else this.$message.warn("项目名称未填写!");else this.$message.error("请先选择患者才能新增医嘱!")},fnFocusType:function(){this.patientInfo||this.$message.error("请先选择患者才能新增医嘱")},fnProjectRow:function(t,e){var a=this;return{style:{cursor:"pointer"},on:{click:function(e){a.onSelect(t)}}}}}}),d=n,c=a(1001),u=(0,c.Z)(d,s,r,!1,null,"f37f9458",null),f=u.exports,h=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("div",{staticClass:"fit"},[i("ta-title",[e._v("新开诊断")]),i("ta-form",{attrs:{autoFormCreate:function(e){t.diseForm=e},layout:"horizontal"}},[i("ta-row",[i("ta-col",{attrs:{span:12}},[i("ta-form-item",{attrs:{fieldDecoratorId:"diseName",labelCol:{span:8},wrapperCol:{span:16},required:!0}},[i("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("诊断名称")]),i("ta-input",{attrs:{placeholder:"输入诊断名称或编码（回车进行搜索）"},on:{pressEnter:e.handleSearch}})],1)],1),i("ta-col",{attrs:{span:3}},[i("ta-button",{staticStyle:{"margin-left":"20px"},attrs:{type:"primary"},on:{click:e.addDise}},[e._v("+")])],1)],1)],1),i("div",{staticStyle:{height:"calc(100% - 150px)"}},[i("ta-table",{attrs:{columns:e.newDiseColumns,dataSource:e.newDiseData,customRow:e.fnCustomRow,bordered:!0,haveSn:!0,size:"small",scroll:{y:"100%"}},on:{"update:columns":function(t){e.newDiseColumns=t}},scopedSlots:e._u([{key:"action",fn:function(t,a){return i("span",{},[i("ta-popconfirm",{attrs:{title:"是否删除?"},on:{confirm:function(){return e.deleteDiag(a)}}},[i("a",{style:{marginLeft:"10px"}},[i("ta-icon",{attrs:{type:"delete"}}),e._v("删除")],1)])],1)}}])})],1),i("ta-button",{staticStyle:{"margin-left":"calc(50% - 50px)",width:"100px"},attrs:{type:"primary"},on:{click:e.doSubmit}},[e._v("提交")]),i("ta-drawer",{attrs:{title:"诊断选择(单击进行选择）",width:"500",placement:"right",closable:!0,visible:e.visible,destroyOnClose:""},on:{close:e.closeEdit}},[i("ta-table",{staticStyle:{height:"200px"},attrs:{columns:e.projectColumns,dataSource:e.dataSource,customRow:e.fnProjectRow},on:{"update:columns":function(t){e.projectColumns=t}}})],1)],1)},m=[],p={name:"newOrder",components:{TaTitle:l.Z},props:{patientInfo:{type:Object}},data:function(){var t=[{title:"诊断编码体系",dataIndex:"diseSys",align:"center",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"诊断编码",dataIndex:"diseCode",align:"left",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"诊断名称",dataIndex:"diseName",align:"left",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"诊断录入时间",dataIndex:"diagTime",align:"center",width:140,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"操作",dataIndex:"action",align:"center",width:50,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell,scopedSlots:{customRender:"action"}}],e=[{title:"诊断id",dataIndex:"diseCode",align:"left",width:130,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"诊断名称",dataIndex:"diseName",align:"left",width:130,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell}];return{projectColumns:e,newDiseColumns:t,newDiseData:[],dataSource:[],dataObject:{flag:!0},visible:!1}},methods:{deleteDiag:function(t){this.newDiseData=this.newDiseData.filter((function(e){return e.drordBegntime!==t.drordBegntime}))},closeEdit:function(){this.visible=!1},fnCustomRow:function(t,e){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px"}}},doSubmit:function(){var t=this;if(0!==this.newDiseData.length){var e=this.patientInfo;e.newDise=JSON.stringify(this.newDiseData),this.Base.submit(null,{url:"doctorOrder/saveDise",data:e,autoValid:!1},{successCallback:function(e){t.$emit("fnQuery"),"0"===e.data.flag?t.$message.success("保存诊断成功"):t.$message.error("诊断信息重复")},failCallback:function(e){t.$message.error("诊断信息保存失败")}})}else this.$message.error("请先添加诊断信息")},handleSearch:function(){var t=this;this.Base.submit(null,{url:"doctorOrder/queryDise",data:{diseName:this.diseForm.getFieldValue("diseName")},autoValid:!1},{successCallback:function(e){t.visible=!0,t.dataSource=e.data.list},failCallback:function(e){t.$message.error("诊断信息查询失败")}})},addDise:function(){if(this.dataObject.flag)this.$message.warn("请先选择诊断项目");else if(this.newDiseData.map((function(t){return t.diseCode})).includes(this.dataObject.diseCode))this.$message.error("诊断添加重复,请重新选择");else if(this.patientInfo){var t=new Date;this.dataObject.diagTime=t.getFullYear()+"-"+(t.getMonth()+1)+"-"+t.getDate()+" "+t.getHours()+":"+t.getMinutes()+":"+t.getSeconds(),this.newDiseData.push(this.dataObject),this.dataObject={},this.dataObject.flag=!0,this.diseForm.resetFields()}else this.$message.error("请先选择患者才能新增诊断")},fnProjectRow:function(t,e){var a=this;return{style:{cursor:"pointer"},on:{click:function(e){a.onSelect(t)}}}},onSelect:function(t){this.dataObject=t,this.diseForm.setFieldsValue({diseName:t.diseCode+"--"+t.diseName}),this.visible=!1}}},g=p,C=(0,c.Z)(g,h,m,!1,null,"67895dd5",null),w=C.exports,v={name:"doctorOrder",components:{NewOrder:f,TaTitle:l.Z,newDise:w},data:function(){var t=this,e=[{title:"住院号",dataIndex:"iptNo",align:"left",width:80,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"住院天数",dataIndex:"iptDay",align:"right",width:75,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"患者姓名",dataIndex:"psnName",align:"left",width:75,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"医保",dataIndex:"hiFeesetlType",align:"center",width:60,overflowTooltip:!0,collectionType:"AAE141",customHeaderCell:this.fnCustomHeaderCell},{title:"性别",dataIndex:"gend",align:"center",width:50,collectionType:"SEX",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"年龄",dataIndex:"age",align:"right",width:50,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"每晚预审结果",dataIndex:"action",align:"center",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell,scopedSlots:{customRender:"action"}}],a=[{title:"医嘱明细id",dataIndex:"drordNo",align:"center",minWidth:100},{title:"项目id",dataIndex:"hilistCode",align:"left",minWidth:240,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"项目名称",dataIndex:"hilistName",align:"left",minWidth:200,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"数量",dataIndex:"cnt",align:"right",width:50,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"单价",dataIndex:"pric",align:"right",width:50,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"开嘱时间",dataIndex:"drordBegntime",align:"center",minWidth:130,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"操作",dataIndex:"action",align:"center",minWidth:90,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell,scopedSlots:{customRender:"action"}}],i=[{title:"疾病诊断编码体系",dataIndex:"diseSys",align:"center",minWidth:100},{title:"诊断编码",dataIndex:"diseCode",align:"center",minWidth:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"诊断名称",dataIndex:"diseName",align:"center",minWidth:200,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"诊断医护人员名称",dataIndex:"diseDorName",align:"center",minWidth:200,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"诊断录入时间",dataIndex:"diseTime",align:"center",minWidth:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"操作",dataIndex:"action",align:"center",minWidth:60,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell,scopedSlots:{customRender:"action"}}];return{patientColumns:e,patientData:[],activeKey:"order",orderColumns:a,orderData:[],diagColumns:i,diagData:[],options:[],iptNo:"",patientInfo:null,zntxModal:!1,mwysModal:!1,url:"",url2:"",show:"",errMessage:"",iframeWin:null,saveParam:"",engOptions:[],resultList:[],operateMenu:[{name:"删除",icon:"delete",type:"confirm",confirmTitle:"确认删除该信息？",onOk:function(e,a){t.Base.submit(null,{url:"doctorOrder/deleteOrder",data:{drordNo:e.drordNo},autoValid:!1},{successCallback:function(e){t.orderData.splice(a,1),t.$message.success("医嘱删除成功")},failCallback:function(e){t.$message.error("医嘱删除失败")}})}}]}},mounted:function(){this.$refs.orderRef.hideColumns(["drordNo"]),this.fnQueryDept(),window.addEventListener("message",this.handleMessageDr)},methods:{handleMessageDr:function(t){var e=t.data;"0"===e.infcode&&(this.zntxModal=!1,1===this.show&&this.saveInfo(JSON.stringify(e.result)))},fnCustomRow:function(t,e){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px"}}},fnCustomHeaderCell:function(t){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px",fontWeight:"bold"}}},tabChange:function(t){this.activeKey=t,this.fnQuery()},deleteOrder:function(t){var e=this;this.Base.submit(null,{url:"doctorOrder/deleteOrder",data:{drordNo:t.drordNo},autoValid:!1},{successCallback:function(t){e.fnQuery(),e.$message.success("医嘱删除成功")},failCallback:function(t){e.$message.error("医嘱删除失败")}})},deleteDiag:function(t){var e=this;this.Base.submit(null,{url:"doctorOrder/deleteDiag",data:{iptNo:this.patientInfo.iptNo,diseCode:t.diseCode},autoValid:!1},{successCallback:function(t){e.fnQuery(),e.$message.success("诊断删除成功")},failCallback:function(t){e.$message.error("诊断删除失败")}})},fnQueryDept:function(){var t=this;this.Base.submit(null,{url:"doctorOrder/queryDept",autoValid:!1},{successCallback:function(e){t.options=e.data.list},failCallback:function(e){t.$message.error("科室数据加载失败")}})},fnQueryEng:function(){var t=this;this.Base.submit(null,{url:"dischargeHis/queryEng",data:{trig_scen:2},autoValid:!1},{successCallback:function(e){t.engOptions=e.data.list},failCallback:function(e){t.$message.error("引擎数据加载失败")}})},deptSelect:function(t){var e=this;this.Base.submit(null,{url:"doctorOrder/queryPatient",data:{admDeptCode:t},autoValid:!1},{successCallback:function(t){e.patientData=t.data.list;var a=e.patientData[0];a.newOrder=JSON.stringify(e.billData),a.infno=990107,a.trig_scen=2;var i=[];e.Base.submit(null,{url:"doctorOrder/doCheck2",data:a,autoValid:!1},{successCallback:function(t){i=t.data.result.output.result;for(var a=0;a<e.patientData.length;a++)for(var o=0;o<i.length;o++)i[o].curr_mdtrt_id===e.patientData[a].currMdtrtId&&(e.patientData[a].url=i[o].url)},failCallback:function(t){e.$message.error("接口调用失败加载失败")}})},failCallback:function(t){e.$message.error("患者数据加载失败")}})},audit:function(t){var e=this,a=t;a.newOrder=JSON.stringify(this.billData),a.infno=990107,a.trig_scen=2;this.Base.submit(null,{url:"doctorOrder/doCheck2",data:a,autoValid:!1},{successCallback:function(t){t.data.result.output.result},failCallback:function(t){e.$message.error("接口调用失败加载失败")}})},auditResult:function(t){window.open(t.url,"newwindow","height=750, width=1300, top=200, left=400, toolbar=no, menubar=no, scrollbars=no, resizable=no, location=no, status=no")},handleCancel2:function(){this.visible=!1,this.showAll=!1},fnDeptRow:function(t,e){var a=this;return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",backgroundColor:t.iptNo==this.iptNo?"#7ecdf0":"",lineHeight:"22px",cursor:"pointer"},on:{click:function(e){a.iptNo=t.iptNo,a.patientInfo=t,"order"===a.activeKey&&(a.$refs.order.newOrderData=[]),"diag"===a.activeKey&&(a.$refs.dise.newDiseData=[]),a.fnQuery()}}}},fnQuery:function(){var t=this,e=this.activeKey.charAt(0).toUpperCase()+this.activeKey.slice(1);this.Base.submit(null,{url:"doctorOrder/query"+e,data:{iptNo:this.iptNo},autoValid:!1},{successCallback:function(e){"order"===t.activeKey&&(t.orderData=e.data.list),"diag"===t.activeKey&&(t.diagData=e.data.list)},failCallback:function(e){t.$message.error("表格数据加载失败")}})},doCheck:function(t){var e=this;if(0!==t.length){var a=this.patientInfo;null!==a?(a.newOrder=JSON.stringify(t),a.infno=990104,a.trig_scen=2,this.Base.submit(null,{url:"doctorOrder/doCheck",data:a,autoValid:!1},{successCallback:function(t){e.show=t.data.infcode,-1===e.show?e.errMessage=t.data.err_msg:1===e.show?(e.saveParam=t.data.saveParam,e.saveInfo(t.data.result)):(e.fnQuery(),e.$message.success(t.data.msg))},failCallback:function(t){e.$message.error("接口调用失败加载失败")}})):this.$message.error("请选择患者！")}else this.$message.error("医嘱信息不能为空！")},doCheckWithUrl:function(t){var e=this,a=this.patientInfo;null!==a?(a.newOrder=JSON.stringify(t),0!==t.length?(a.infno=990104,a.trig_scen=2,this.Base.submit(null,{url:"doctorOrder/doCheckWithUrl",data:a,autoValid:!1},{successCallback:function(t){if(e.show=t.data.infcode,-1===e.show)e.errMessage=t.data.err_msg;else if(1===e.show){var a=t.data.result;e.patientInfo.call_id=a.call_id,e.url=a.url,e.saveParam=t.data.saveParam,void 0!==e.url&&""!==e.url?e.zntxModal=!0:e.saveInfo(t.data.result)}else e.fnQuery(),e.$message.success(t.data.msg)},failCallback:function(t){e.$message.error("接口调用失败加载失败")}})):this.$message.error("医嘱信息不能为空！")):this.$message.error("请选择患者！")},handleCancelZntx:function(){this.zntxModal=!1,this.show},getOperate:function(){var t=this,e=this.patientInfo;e.infno=990108,e.trig_scen=2,e.lastInfno=990104,e.call_id=this.patientInfo.call_id,this.Base.submit(null,{url:"doctorOrder/getOperate",data:e,autoValid:!1},{successCallback:function(e){t.saveInfo(JSON.stringify(e.data.result.output.result))},failCallback:function(e){t.$message.error("保存数据失败")}})},saveInfo:function(t){var e=this;this.Base.submit(null,{url:"doctorOrder/saveInfo",data:{jsonString:this.saveParam,result:t,cj:"医嘱"},autoValid:!1},{successCallback:function(t){e.fnQuery(),e.$message.success(t.data.msg)},failCallback:function(t){e.$message.error("保存数据失败")}})}}},b=v,y=(0,c.Z)(b,i,o,!1,null,"06eb6a5d",null),x=y.exports},36766:function(t,e,a){var i=a(66586);e["Z"]=i.Z},26263:function(t,e,a){a.d(e,{s:function(){return i},x:function(){return o}});var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},o=[]},66586:function(t,e){var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};e["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}}}]);
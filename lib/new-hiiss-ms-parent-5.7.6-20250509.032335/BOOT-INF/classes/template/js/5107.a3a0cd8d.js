(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[5107],{88412:function(t,e,a){"use strict";var i=a(26263),s=a(36766),n=a(1001),o=(0,n.Z)(s.Z,i.s,i.x,!1,null,"5e7ef0ae",null);e["Z"]=o.exports},50395:function(t,e,a){"use strict";a.r(e),a.d(e,{default:function(){return it}});var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("ta-border-layout",{attrs:{layout:{header:"auto"},"show-border":!0}},[a("div",{staticClass:"query-header",attrs:{slot:"header"},slot:"header"},[a("div",{staticClass:"query-header"},[a("div",{ref:"formBox",staticClass:"query-header-left",style:t.formBoxStyle},[a("search-term",{ref:"term",attrs:{allReceiveData:t.params},on:{fnQuery:t.fnQuery}})],1),a("div",{staticStyle:{display:"flex","margin-left":"10px",float:"right"}},[a("div",{staticClass:"ctrl-btn",on:{click:t.formShowAllChange}},[a("a",[t._v(t._s(t.formShowAll?"收起":"展开"))]),t.formShowAll?a("ta-icon",{attrs:{type:"caret-up"}}):a("ta-icon",{attrs:{type:"caret-down"}})],1),a("ta-button",{attrs:{icon:"search",type:"primary"},on:{click:t.handleQuery}},[t._v("查询 ")]),a("ta-button",{attrs:{icon:"redo",type:"default"},on:{click:t.handleReset}},[t._v("重置 ")])],1)])]),a("div",{staticClass:"border",staticStyle:{width:"100%",height:"140px"}},[a("danger-stat",{ref:"danger",attrs:{allReceiveData:t.params}})],1),a("div",{staticStyle:{width:"100%",height:"800px"},attrs:{slot:"footer"},slot:"footer"},[a("ta-border-layout",{attrs:{"header-cfg":{showBorder:!0},layout:{right:"700px"},"show-border":!1}},[a("div",[a("ta-border-layout",{attrs:{"header-cfg":{showBorder:!0},layout:{header:"250px"},"show-border":!1}},[a("div",{staticClass:"border",staticStyle:{height:"250px"},attrs:{slot:"header"},slot:"header"},[a("risk-compose",{ref:"riskCompose",attrs:{allReceiveData:t.params}})],1),a("div",{staticClass:"border"},[a("div",[a("ta-title",{attrs:{title:"科室排名"}},[a("span",{staticStyle:{display:"inline-block",float:"right","margin-right":"10px"}},[a("ta-tooltip",{attrs:{placement:"top"}},[a("template",{slot:"title"},[a("span",{staticStyle:{"font-size":"12px"}},[t._v(" 风险金额占比：科室风险金额/全院风险金额"),a("br"),t._v(" 风险遗留：指预警过，但出院末次审核仍时存在疑点"),a("br"),t._v(" 报销项目：指结算时，HIS上传给医保中心标志为报销的项目，该类项目可能会是医保拒付的项目"),a("br")])]),a("ta-icon",{attrs:{type:"question-circle"}})],2)],1)])],1),a("div",{staticStyle:{height:"480px","margin-top":"10px",cursor:"pointer"}},[a("ta-big-table",{ref:"xTable1",attrs:{"cell-style":t.cellStyle,"control-column":t.showHiddenOrSortColumn,data:t.tableData,"auto-resize":"",border:"",height:"auto",align:"center","header-align":"center","highlight-hover-row":"",resizable:"","show-overflow":""},on:{"cell-click":t.cellClickEvent}},[a("ta-big-table-column",{attrs:{align:"center",width:"60",title:"排名",type:"seq"}}),a("ta-big-table-column",{attrs:{align:"center",field:"aae386","min-width":"100",sortable:"",title:"科室名称"}}),a("ta-big-table-column",{attrs:{visible:!1,align:"center",field:"aaz307","min-width":"80",title:"科室编码"}}),a("ta-big-table-column",{attrs:{align:"center",field:"totalmoney",formatter:"formatAmount","min-width":"160",sortable:"",title:"风险金额(万元)"}}),a("ta-big-table-column",{attrs:{align:"center",field:"fy01","min-width":"130",sortable:"",title:"风险金额占比"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;e.column;return[t._v(" "+t._s(t.formatPercentageValue(a.fy01,a.totalmoney))+" ")]}}])}),a("ta-big-table-column",{attrs:{align:"center",field:"fy01zb","min-width":"130",sortable:"",title:"风险金额环比"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;e.column;return[t._v(" "+t._s(t.formatPercentageValue(a.fy01,a.totalmoney))+" ")]}}])}),a("ta-big-table-column",{attrs:{align:"center",field:"fy01z1b","min-width":"140",sortable:"",title:"风险遗留率"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;e.column;return[t._v(" "+t._s(t.formatPercentageValue(a.fy01,a.totalmoney))+" ")]}}])}),a("ta-big-table-column",{attrs:{align:"center",field:"fy02","min-width":"160",sortable:"",title:"风险项目种类数"}}),a("ta-big-table-column",{attrs:{align:"center",field:"fy02zb","min-width":"120",sortable:"",title:"事前处置率"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;e.column;return[t._v(" "+t._s(t.formatPercentageValue(a.fy02,a.totalmoney))+" ")]}}])}),a("ta-big-table-column",{attrs:{align:"center",field:"fy022zb","min-width":"120",sortable:"",title:"事前备案率"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;e.column;return[t._v(" "+t._s(t.formatPercentageValue(a.fy02,a.totalmoney))+" ")]}}])}),a("ta-big-table-column",{attrs:{align:"center",field:"fy23",formatter:"formatFixedNumber","min-width":"160",sortable:"",title:"风险项目数(万次)"}}),a("ta-big-table-column",{attrs:{align:"center",field:"fy23zb","min-width":"160",sortable:"",title:"风险项目数占比"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;e.column;return[t._v(" "+t._s(t.formatPercentageValue(a.fy23,a.totalmoney))+" ")]}}])}),a("ta-big-table-column",{attrs:{align:"center",field:"fy22",formatter:"formatFixedNumber","min-width":"160",sortable:"",title:"风险病例数(次)"}}),a("ta-big-table-column",{attrs:{align:"center",field:"fy22zb","min-width":"140",sortable:"",title:"风险病例占比"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;e.column;return[t._v(" "+t._s(t.formatPercentageValue(a.fy22,a.totalmoney))+" ")]}}])}),a("ta-big-table-column",{attrs:{align:"center",field:"fy122zb","min-width":"140",sortable:"",title:"药品风险占比"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;e.column;return[t._v(" "+t._s(t.formatPercentageValue(a.fy22,a.totalmoney))+" ")]}}])}),a("ta-big-table-column",{attrs:{align:"center",field:"fy92zb","min-width":"140",sortable:"",title:"诊疗风险占比"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;e.column;return[t._v(" "+t._s(t.formatPercentageValue(a.fy22,a.totalmoney))+" ")]}}])}),a("ta-big-table-column",{attrs:{align:"center",field:"fy922zb","min-width":"140",sortable:"",title:"材料风险占比"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;e.column;return[t._v(" "+t._s(t.formatPercentageValue(a.fy22,a.totalmoney))+" ")]}}])}),a("template",{slot:"bottomBar"},[a("ta-pagination",{ref:"gridPager",attrs:{"data-source":t.tableData,defaultPageSize:1e3,hideOnSinglePage:!0,pageSizeOptions:["1000"],params:t.PageParams,simple:!0,url:"reportStatistics/querydscgErrorCostCountByAka063"},on:{"update:dataSource":function(e){t.tableData=e},"update:data-source":function(e){t.tableData=e}}})],1)],2)],1)])])],1),a("div",{attrs:{slot:"right"},slot:"right"},[a("ta-border-layout",{attrs:{"header-cfg":{showBorder:!0},layout:{header:"250px"},"show-border":!1}},[a("div",{staticClass:"border",staticStyle:{height:"250px"},attrs:{slot:"header"},slot:"header"},[a("excep-items",{attrs:{allReceiveData:t.params}})],1),a("div",{staticClass:"border"},[a("div",[a("ta-title",{attrs:{title:"科室风险分析"}})],1),a("div",{attrs:{id:"main"}})])])],1)])],1)])],1)},s=[],n=a(66347),o=a(88412),r=a(48211),l=a(1708),c=a(36797),u=a.n(c),d=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("div",[i("ta-form",{attrs:{autoFormCreate:function(e){t.baseInfoForm=e},col:e.col,formLayout:!0,"label-width":"100px",layout:"horizontal"}},[i("ta-form-item",{attrs:{"field-decorator-id":"timeType",label:"时间类型","field-decorator-options":{rules:[{required:!0,message:"必须选取时间类型"}]},required:!0,"init-value":"settleTime"}},[i("ta-select",{staticStyle:{width:"100%"},attrs:{options:e.timeType,placeholder:"时间类型筛选",showSearch:""}})],1),i("ta-form-item",{attrs:{label:"开始时间","field-decorator-id":"startDate","field-decorator-options":{rules:[{required:!0,message:"必须选取开始时间"}]},required:!0,"init-value":e.defaultValue}},[i("ta-month-picker",{staticStyle:{width:"100%"},attrs:{"allow-input":!0,"picker-options":e.monthPickerOptions,placeholder:"日期选择可以输入"}})],1),i("ta-form-item",{attrs:{label:"结束时间","field-decorator-id":"endDate","field-decorator-options":{rules:[{required:!0,message:"必须选取结束时间"}]},required:!0,"init-value":e.defaultValue}},[i("ta-month-picker",{staticStyle:{width:"100%"},attrs:{"allow-input":!0,"picker-options":e.monthPickerOptions,placeholder:"日期选择可以输入"}})],1),i("ta-form-item",{attrs:{"field-decorator-id":"aka130",label:"就诊类型"}},[i("ta-select",{staticStyle:{width:"100%"},attrs:{"collection-filter":"11,21,14","reverse-filter":!0,"collection-type":"AKA130",placeholder:"就诊类型筛选"}})],1),i("ta-form-item",{attrs:{"field-decorator-id":"akb020",label:"院区标识"}},[i("ta-select",{attrs:{showSearch:"",placeholder:"院区标识筛选",allowClear:"",options:e.hosList},on:{change:e.fnQueryDept}})],1),i("ta-form-item",{attrs:{"field-decorator-id":"aaz307",label:"出院科室"}},[i("ta-select",{attrs:{showSearch:"","show-search":!0,allowClear:"",placeholder:"出院科室筛选",options:e.ksList}})],1)],1)],1)},f=[],m=a(48534),p=(a(36133),a(96992)),h=[{value:"settleTime",label:"结算时间"},{value:"dscgTime",label:"出院时间"}];function v(t){if(isNaN(t)||!t)return 0;var e=parseFloat(t),a=String(e.toFixed(2)),i=/(-?\d+)(\d{3})/;while(i.test(a))a=a.replace(i,"$1,$2");return a}function g(t){!isNaN(t)&&t||(t=0);var e=String(parseFloat(t)),a=/(-?\d+)(\d{3})/;while(a.test(e))e=e.replace(a,"$1,$2");return e}function y(t){return Number.isFinite(t)?t>=1e4?v(t/1e4):0==t?0:v(t):"-"}function b(t,e){return t>=1e4&&"%"!=e?"万"+e:e}var x=a(83231),S={name:"searchTerm",components:{},props:{allReceiveData:{type:Object,default:function(){}}},data:function(){return{col:{xs:1,sm:4,md:4,lg:4,xl:6,xxl:6},defaultValue:u()().format("YYYY-MM"),timeType:h,hosList:[],ksList:[],permissions:null,paramsDisable:{akb020:!1,aaz307:!1,aaz263:!1},monthPickerOptions:{shortcuts:[{text:"这个月",onClick:(0,p.Z)()},{text:"上个月",onClick:(0,p.Z)().subtract(1,"month")},{text:"跳转到明年",onClick:{time:(0,p.Z)().add(1,"year")}},{text:"上半年",onClick:(0,p.Z)().startOf("year").month(0)},{text:"下半年",onClick:(0,p.Z)().startOf("year").month(6)},{text:"去年",onClick:(0,p.Z)().subtract(1,"year").startOf("year")},{text:"今年",onClick:(0,p.Z)().startOf("year")},{text:"下个月",onClick:(0,p.Z)().add(1,"month")},{text:"三个月前",onClick:(0,p.Z)().subtract(3,"months")},{text:"三个月后",onClick:(0,p.Z)().add(3,"months")},{text:"上一个季度",onClick:(0,p.Z)().subtract(1,"quarter")},{text:"下一个季度",onClick:(0,p.Z)().add(1,"quarter")}]}}},mounted:function(){var t=this;return(0,m.Z)(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,x.Z.permissionCheck();case 2:t.permissions=e.sent,t.fnQueryHos();case 4:case"end":return e.stop()}}),e)})))()},methods:{moment:u(),fnQueryHos:function(){var t=this,e={url:"/miimCommonRead/queryHospRpcList",autoValid:!0},a={successCallback:function(e){var a;if(t.hosList=e.data.resultData,null!==(a=t.allReceiveData)&&void 0!==a&&a.akb020){var i=t.allReceiveData.akb020;i?(t.hosList=t.hosList.filter((function(t){return t.value==i})),t.baseInfoForm.setFieldsValue({medinsCode:i})):t.baseInfoForm.resetFields("medinsCode"),t.akb020=i}else t.baseInfoForm.setFieldsValue({medinsCode:t.hosList[0].value}),t.permissions&&t.permissions.akb020Set.size>0&&(t.hosList=t.hosList.filter((function(e){return t.permissions.akb020Set.has(e.value)})),t.baseInfoForm.setFieldsValue({medinsCode:t.hosList[0].value})),t.akb020=t.hosList[0].value;t.fnQueryDept(t.akb020)},failCallback:function(e){t.$message.error("医院数据加载失败")}};this.Base.submit(null,e,a)},fnQueryDept:function(t){var e=this;this.akb020=t||"",this.Base.submit(null,{url:"miimCommonRead/queryDepartRpcList",data:{akb020:this.akb020,hospDeptType:"I"},autoValid:!1},{successCallback:function(t){var a,i=null===(a=e.allReceiveData)||void 0===a?void 0:a.aaz307;i||e.baseInfoForm.resetFields("aaz307"),e.ksList=t.data.resultData,e.setPermission()},failCallback:function(t){e.$message.error("科室数据加载失败")}})},setPermission:function(){var t=this;this.paramsDisable.akb020=this.permissions.akb020Disable,this.paramsDisable.aaz307=this.permissions.aaz307Disable,this.paramsDisable.aaz263=this.permissions.aaz263Disable,this.permissions&&(this.permissions&&this.permissions.aaz307Set.size>0&&(this.ksList=this.ksList.filter((function(e){return t.permissions.aaz307Set.has(e.value)})),this.permissions.aaz307Disable&&this.baseInfoForm.setFieldsValue({aaz307:this.ksList[0].value})),this.permissions.aaz309Set.size>0&&(this.groupList=this.groupList.filter((function(e){return t.permissions.aaz309Set.has(e.value)}))),this.permissions&&this.permissions.aaz263Set.size>0&&(this.doctorList=this.doctorList.filter((function(e){return t.permissions.aaz263Set.has(e.value)})),this.permissions.aaz263Disable&&this.baseInfoForm.setFieldsValue({aaz263:this.doctorList[0].value})))},fnQuery:function(){var t=this,e=this.baseInfoForm.getFieldsValue();e.startDate?e.endDate?this.baseInfoForm.validateFields((function(a,i){a||t.$emit("fnQuery",e)})):this.$message.error("请选择结束时间！"):this.$message.error("请选择开始时间！")},fnReset:function(){this.baseInfoForm.resetFields(),this.fnQuery()}}},C=S,k=a(1001),w=(0,k.Z)(C,d,f,!1,null,"3929a046",null),I=w.exports,A=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("div",{staticClass:"fit"},[i("div",{staticStyle:{height:"7%",padding:"1px"}},[i("ta-form",{attrs:{"auto-form-create":function(e){return t.searchForm=e},"form-layout":!0,"label-width":"100px",layout:"horizontal"}},[i("ta-form-item",{attrs:{fieldDecoratorOptions:{rules:[{required:!0,message:"必须选取时间"}]},"init-value":e.rangeValue,labelCol:{span:7},required:!0,span:5,wrapperCol:{span:16},fieldDecoratorId:"allDate"}},[i("span",{attrs:{slot:"label"},slot:"label"},[e._v("时间范围")]),i("ta-range-picker",{staticStyle:{width:"105%"},attrs:{"allow-one":!0,type:"month"}})],1),i("ta-form-item",{staticStyle:{"margin-left":"1%"},attrs:{labelCol:{span:7},span:4,wrapperCol:{span:15},fieldDecoratorId:"ake001"}},[i("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("项目名称")]),i("ta-select",{attrs:{maxTagCount:1,options:e.xmList,allowClear:"",mode:"multiple",placeholder:"项目名称筛选"}})],1),i("ta-form-item",{staticStyle:{"margin-left":"1%"},attrs:{labelCol:{span:7},span:4,wrapperCol:{span:15},fieldDecoratorId:"aaz307"}},[i("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("科室名称")]),i("ta-select",{attrs:{maxTagCount:1,options:e.ksList,allowClear:"",mode:"multiple",placeholder:"科室名称筛选"}})],1),i("ta-form-item",{staticStyle:{"margin-left":"1%"},attrs:{labelCol:{span:7},span:4,wrapperCol:{span:15},fieldDecoratorId:"aka063"}},[i("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("项目类别")]),i("ta-select",{attrs:{maxTagCount:1,options:e.aka063List,allowClear:"",mode:"multiple",placeholder:"项目类别筛选"}})],1),i("ta-form-item",{attrs:{span:6}},[i("ta-button",{staticStyle:{margin:"0 20px",float:"right"},attrs:{icon:"redo",type:"default"},on:{click:e.fnReset}},[e._v("重置 ")]),i("ta-button",{staticStyle:{float:"right"},attrs:{icon:"search",type:"primary"},on:{click:e.searchTable}},[e._v("查询")])],1)],1)],1),i("div",{staticStyle:{height:"95%",padding:"5px"}},[i("div",{staticStyle:{height:"90%","margin-top":"10px"}},[i("ta-big-table",{ref:"xTable1",attrs:{data:e.tableData,"auto-resize":"",border:"",height:"auto","show-overflow":""},scopedSlots:e._u([{key:"bottomBar",fn:function(){return[i("ta-button",{staticStyle:{float:"right","margin-top":"20px"},attrs:{icon:"download",type:"primary"},on:{click:e.exportTable}},[e._v("导出 ")]),i("ta-pagination",{ref:"gridPager",staticStyle:{float:"right","margin-top":"20px","margin-right":"10px"},attrs:{"data-source":e.tableData,defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"],params:e.userPageParams,url:"medicalServiceTypeCount/queryCostDetails"},on:{"update:dataSource":function(t){e.tableData=t},"update:data-source":function(t){e.tableData=t}}})]},proxy:!0}])},[i("ta-big-table-column",{attrs:{align:"center",title:"序号",type:"seq",width:"55"}}),i("ta-big-table-column",{attrs:{align:"center",field:"aae386","min-width":"120",title:"科室名称"}}),i("ta-big-table-column",{attrs:{align:"center",field:"aaa103","min-width":"160",title:"医疗服务项目类别"}}),i("ta-big-table-column",{attrs:{align:"center",field:"ake002","min-width":"160",title:"医疗服务项目名称"}}),i("ta-big-table-column",{attrs:{align:"center",field:"aae043","min-width":"120",title:"结算时间"}}),i("ta-big-table-column",{attrs:{align:"center",field:"akc226","min-width":"100",title:"数量"}}),i("ta-big-table-column",{attrs:{align:"center",field:"akb065",formatter:"formatAmount","min-width":"100",title:"金额"}})],1)],1)])])},_=[],z=a(95082),D=a(22722),M=a(55115);M.w3.prototype.Base=Object.assign(M.w3.prototype.Base,(0,z.Z)({},D.Z));var R={name:"detailInfo",props:{paramData:Object},data:function(){return{rangeValue:[TaUtils.getMoment(new Date((new Date).getTime()).toISOString().slice(0,5)+"01-01","YYYY-MM-DD"),TaUtils.getMoment((new Date).toISOString().slice(0,10),"YYYY-MM-DD")],tableData:[],condition:{},aka063List:[],ksList:[],xmList:[]}},mounted:function(){var t=this;this.$nextTick((function(){t.fnQueryks(),t.fnQueryxm(),t.fnQueryAka063(),t.paramData&&t.searchForm.setFieldsValue(t.paramData),t.searchForm.setFieldsValue({allDate:t.paramData.allDate}),t.searchTable()}))},methods:{fnReset:function(){this.paramData.aaz307="",this.paramData.aka063="",this.searchForm.resetFields(),this.searchTable()},userPageParams:function(){return this.condition},exportTable:function(){var t,e=this,a=[],i=this.$refs.xTable1.getColumns(),s=(0,n.Z)(i);try{for(s.s();!(t=s.n()).done;){var o=t.value;"序号"!==o.title&&a.push({header:o.title,key:o.property,width:20})}}catch(r){s.e(r)}finally{s.f()}this.Base.submit(null,{url:"medicalServiceTypeCount/exportExcel",data:this.condition,autoValid:!1},{successCallback:function(t){var i={fileName:"科室费用情况明细结果表",sheets:[{name:"worksheet1",column:{complex:!1,columns:a},rows:t.data.data}]};e.Base.generateExcel(i)},failCallback:function(t){e.$message.error("科室费用情况明细数据加载失败")}})},searchTable:function(){var t=this.searchForm.getFieldsValue();null!=t.allDate[0]&&""!=t.allDate[0]&&null!=t.allDate[1]&&""!=t.allDate[1]?(t.startDate=t.allDate[0].format("YYYYMM"),t.endDate=t.allDate[1].format("YYYYMM"),this.condition=t,this.$refs.gridPager.loadData((function(t){}))):this.$message.error("请选择完整日期")},fnQueryAka063:function(){var t=this;this.Base.submit(null,{url:"auditStatistics/queryAka063",data:{}},{successCallback:function(e){t.aka063List=e.data.aka063List},failCallback:function(e){t.$message.error("列表加载失败")}})},fnQueryks:function(){var t=this;this.Base.submit(null,{url:"medicalServiceTypeCount/querySelectksList",data:{}},{successCallback:function(e){t.ksList=e.data.SelectList},failCallback:function(e){t.$message.error("列表加载失败")}})},fnQueryxm:function(){var t=this;this.Base.submit(null,{url:"medicalServiceTypeCount/querySelectxmList",data:{}},{successCallback:function(e){t.xmList=e.data.SelectList},failCallback:function(e){t.$message.error("列表加载失败")}})}}},F=R,E=(0,k.Z)(F,A,_,!1,null,"30e62c00",null),T=E.exports,L=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"container"},[a("div",{staticClass:"item",staticStyle:{width:"27%"}},[a("div",{staticClass:"firstContainer"},[t._m(0),a("div",{staticClass:"right"},[a("div",{staticClass:"firstItem"},[a("div",{staticClass:"firstItemDiv"},[a("span",{staticStyle:{color:"#989898","font-size":"14px"}},[t._v("风险总金额")]),a("ta-tooltip",{staticClass:"tooltip",attrs:{placement:"top"}},[a("template",{slot:"title"},[a("span",{staticStyle:{"font-size":"12px"}},[t._v(" 当项目违反多种规则时，风险总金额取违规等级高的总金额 ")])]),a("ta-icon",{attrs:{type:"question-circle"}})],2)],1)]),a("div",{staticClass:"firstItem"},[a("div",{staticStyle:{display:"flex",height:"100%",width:"285px"}},[a("div",{staticClass:"left-div"},[a("span",{staticStyle:{color:"#f59a23","font-size":"28px","font-weight":"500","font-style":"normal","align-self":"flex-end"}},[t._v(t._s(t.dangerInfo.riskAmount))]),a("span",{staticStyle:{color:"#f59a23","font-size":"18px","font-weight":"500","line-height":"38px","margin-left":"20px","font-style":"normal","align-self":"flex-end"}},[t._v("万元")])]),a("div",{staticClass:"right-div"},[a("div",{staticClass:"centered-content"},[t.dangerInfo.riskAmountMoM?0==t.dangerInfo.riskAmountMoM?a("span",{staticStyle:{color:"#cccccc","font-size":"12px"}},[a("ta-icon",{attrs:{type:"minus"}}),t._v("0% ")],1):t.dangerInfo.riskAmountMoM>0?a("span",{staticStyle:{color:"#fe5500","font-size":"12px"}},[a("ta-icon",{attrs:{type:"caret-up"}}),t._v(" "+t._s((100*t.dangerInfo.riskAmountMoM).toFixed(2))+"% ")],1):t.dangerInfo.riskAmountMoM<0?a("span",{staticStyle:{color:"#2eba07","font-size":"12px"}},[a("ta-icon",{attrs:{type:"caret-down"}}),t._v(" "+t._s((100*t.dangerInfo.riskAmountMoM).toFixed(2))+"% ")],1):t._e():a("span",{staticStyle:{color:"#cccccc"}},[t._v(" --- ")]),a("span",{staticStyle:{color:"#cccccc","font-size":"12px","margin-left":"5px"}},[t._v("环比上期")])])])])]),t._m(1)])])]),a("div",{staticClass:"divider"},[a("ta-divider",{staticClass:"ta-divider",attrs:{type:"vertical"}})],1),a("div",{staticClass:"item",staticStyle:{width:"42%"}},[a("div",{staticClass:"item",staticStyle:{width:"100%"}},[a("div",{staticClass:"right"},[t._m(2),a("div",{staticClass:"firstItem"},[a("div",{staticStyle:{display:"flex",height:"100%",width:"100%"}},[a("div",{staticClass:"left-div"},[a("span",{staticStyle:{color:"#f59a23","font-size":"28px","font-weight":"500","font-style":"normal","align-self":"flex-end"}},[t._v(" "+t._s((100*t.dangerInfo.leaveRate).toFixed(2))+"% ")])])])]),a("div",{staticClass:"firstItem"},[a("div",{staticClass:"centered-content"},[t.dangerInfo.leaveRateMoM?0==t.dangerInfo.leaveRateMoM?a("span",{staticStyle:{color:"#cccccc","font-size":"12px"}},[a("ta-icon",{attrs:{type:"minus"}}),t._v("0% ")],1):t.dangerInfo.leaveRateMoM>0?a("span",{staticStyle:{color:"#fe5500","font-size":"12px"}},[a("ta-icon",{attrs:{type:"caret-up"}}),t._v(" "+t._s((100*t.dangerInfo.leaveRateMoM).toFixed(2))+"% ")],1):t.dangerInfo.leaveRateMoM<0?a("span",{staticStyle:{color:"#2eba07","font-size":"12px"}},[a("ta-icon",{attrs:{type:"caret-down"}}),t._v(" "+t._s((100*t.dangerInfo.leaveRateMoM).toFixed(2))+"% ")],1):t._e():a("span",{staticStyle:{color:"#cccccc"}},[t._v(" --- ")]),a("span",{staticStyle:{color:"#cccccc","font-size":"12px","margin-left":"5px"}},[t._v("环比上期")])])])])]),a("div",{staticClass:"item",staticStyle:{width:"100%"}},[a("div",{staticClass:"right"},[t._m(3),a("div",{staticClass:"firstItem"},[a("div",{staticStyle:{display:"flex",height:"100%",width:"100%"}},[a("div",{staticClass:"left-div"},[a("span",{staticStyle:{color:"#2db807","font-size":"28px","font-weight":"500","font-style":"normal","align-self":"flex-end"}},[t._v(t._s(t.formatAmountDependingOnValue(t.dangerInfo.resolveRisks)))]),a("span",{staticStyle:{color:"#2db807","font-size":"18px","font-weight":"500","line-height":"38px","margin-left":"20px","font-style":"normal","align-self":"flex-end"}},[t._v(t._s(t.formatUnit(t.dangerInfo.resolveRisks,"次")))])])])]),a("div",{staticClass:"firstItem"},[a("div",{staticClass:"centered-content"},[t.dangerInfo.resolveRisksMoM?0==t.dangerInfo.resolveRisksMoM?a("span",{staticStyle:{color:"#cccccc","font-size":"12px"}},[a("ta-icon",{attrs:{type:"minus"}}),t._v("0% ")],1):t.dangerInfo.resolveRisksMoM>0?a("span",{staticStyle:{color:"#fe5500","font-size":"12px"}},[a("ta-icon",{attrs:{type:"caret-up"}}),t._v(" "+t._s((100*t.dangerInfo.resolveRisksMoM).toFixed(2))+"% ")],1):t.dangerInfo.resolveRisksMoM<0?a("span",{staticStyle:{color:"#2eba07","font-size":"12px"}},[a("ta-icon",{attrs:{type:"caret-down"}}),t._v(" "+t._s((100*t.dangerInfo.resolveRisksMoM).toFixed(2))+"% ")],1):t._e():a("span",{staticStyle:{color:"#cccccc"}},[t._v(" --- ")]),a("span",{staticStyle:{color:"#cccccc","font-size":"12px","margin-left":"5px"}},[t._v("环比上期")])])])])]),a("div",{staticClass:"item",staticStyle:{width:"100%"}},[a("div",{staticClass:"right"},[t._m(4),a("div",{staticClass:"firstItem"},[a("div",{staticStyle:{display:"flex",height:"100%",width:"100%"}},[a("div",{staticClass:"left-div"},[a("span",{staticStyle:{color:"#2db807","font-size":"28px","font-weight":"500","font-style":"normal","align-self":"flex-end"}},[t._v(t._s((100*t.dangerInfo.preRate).toFixed(2))+"%")])])])]),a("div",{staticClass:"firstItem"},[a("div",{staticClass:"centered-content"},[t.dangerInfo.preRateMoM?0==t.dangerInfo.preRateMoM?a("span",{staticStyle:{color:"#cccccc","font-size":"12px"}},[a("ta-icon",{attrs:{type:"minus"}}),t._v("0% ")],1):t.dangerInfo.preRateMoM>0?a("span",{staticStyle:{color:"#fe5500","font-size":"12px"}},[a("ta-icon",{attrs:{type:"caret-up"}}),t._v(" "+t._s((100*t.dangerInfo.preRateMoM).toFixed(2))+"% ")],1):t.dangerInfo.preRateMoM<0?a("span",{staticStyle:{color:"#2eba07","font-size":"12px"}},[a("ta-icon",{attrs:{type:"caret-down"}}),t._v(" "+t._s((100*t.dangerInfo.preRateMoM).toFixed(2))+"% ")],1):t._e():a("span",{staticStyle:{color:"#cccccc"}},[t._v(" --- ")]),a("span",{staticStyle:{color:"#cccccc","font-size":"12px","margin-left":"5px"}},[t._v("环比上期")])])])])])]),a("div",{staticClass:"divider"},[a("ta-divider",{staticClass:"ta-divider",attrs:{type:"vertical"}})],1),a("div",{staticClass:"item",staticStyle:{width:"30%"}},[a("div",{staticClass:"item",staticStyle:{width:"100%"}},[a("div",{staticClass:"right"},[t._m(5),a("div",{staticClass:"firstItem"},[a("div",{staticStyle:{display:"flex",height:"100%",width:"100%"}},[a("div",{staticClass:"left-div"},[a("span",{staticStyle:{color:"#666666","font-size":"28px","font-weight":"500","font-style":"normal","align-self":"flex-end"}},[t._v(t._s(t.formatAmountDependingOnValue(t.dangerInfo.totalPts)))]),a("span",{staticStyle:{color:"#666666","font-size":"18px","font-weight":"500","line-height":"38px","margin-left":"20px","font-style":"normal","align-self":"flex-end"}},[t._v(t._s(t.formatUnit(t.dangerInfo.totalPts,"次")))])])])]),a("div",{staticClass:"firstItem"},[a("div",{staticClass:"centered-content"},[t.dangerInfo.totalPtsMoM?0==t.dangerInfo.totalPtsMoM?a("span",{staticStyle:{color:"#cccccc","font-size":"12px"}},[a("ta-icon",{attrs:{type:"minus"}}),t._v("0% ")],1):t.dangerInfo.totalPtsMoM>0?a("span",{staticStyle:{color:"#fe5500","font-size":"12px"}},[a("ta-icon",{attrs:{type:"caret-up"}}),t._v(" "+t._s((100*t.dangerInfo.totalPtsMoM).toFixed(2))+"% ")],1):t.dangerInfo.totalPtsMoM<0?a("span",{staticStyle:{color:"#2eba07","font-size":"12px"}},[a("ta-icon",{attrs:{type:"caret-down"}}),t._v(" "+t._s((100*t.dangerInfo.totalPtsMoM).toFixed(2))+"% ")],1):t._e():a("span",{staticStyle:{color:"#cccccc"}},[t._v(" --- ")]),a("span",{staticStyle:{color:"#cccccc","font-size":"12px","margin-left":"5px"}},[t._v("环比上期")])])])])]),a("div",{staticClass:"item",staticStyle:{width:"100%"}},[a("div",{staticClass:"right"},[t._m(6),a("div",{staticClass:"firstItem"},[a("div",{staticStyle:{display:"flex",height:"100%",width:"100%"}},[a("div",{staticClass:"left-div"},[a("span",{staticStyle:{color:"#666666","font-size":"28px","font-weight":"500","font-style":"normal","align-self":"flex-end"}},[t._v(t._s(t.formatAmountDependingOnValue(t.dangerInfo.totalAmount)))]),a("span",{staticStyle:{color:"#666666","font-size":"18px","font-weight":"500","line-height":"38px","margin-left":"20px","font-style":"normal","align-self":"flex-end"}},[t._v(t._s(t.formatUnit(t.dangerInfo.totalAmount,"元")))])])])]),a("div",{staticClass:"firstItem"},[a("div",{staticClass:"centered-content"},[t.dangerInfo.totalAmountMoM?0==t.dangerInfo.totalAmountMoM?a("span",{staticStyle:{color:"#cccccc","font-size":"12px"}},[a("ta-icon",{attrs:{type:"minus"}}),t._v("0% ")],1):t.dangerInfo.totalAmountMoM>0?a("span",{staticStyle:{color:"#fe5500","font-size":"12px"}},[a("ta-icon",{attrs:{type:"caret-up"}}),t._v(" "+t._s((100*t.dangerInfo.totalAmountMoM).toFixed(2))+"% ")],1):t.dangerInfo.totalAmountMoM<0?a("span",{staticStyle:{color:"#2eba07","font-size":"12px"}},[a("ta-icon",{attrs:{type:"caret-down"}}),t._v(" "+t._s((100*t.dangerInfo.totalAmountMoM).toFixed(2))+"% ")],1):t._e():a("span",{staticStyle:{color:"#cccccc"}},[t._v(" --- ")]),a("span",{staticStyle:{color:"#cccccc","font-size":"12px","margin-left":"5px"}},[t._v("环比上期")])])])])])])])},P=[function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"left"},[i("img",{attrs:{src:a(88001),alt:"Image"}})])},function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"firstItem"},[i("img",{attrs:{src:a(61525),alt:"Image"}})])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"firstItem"},[a("div",{staticClass:"firstItemDiv"},[a("span",{staticStyle:{color:"#989898","font-size":"14px"}},[t._v("风险遗留率")])])])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"firstItem"},[a("div",{staticClass:"firstItemDiv"},[a("span",{staticStyle:{color:"#989898","font-size":"14px"}},[t._v("解决风险数")])])])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"firstItem"},[a("div",{staticClass:"firstItemDiv"},[a("span",{staticStyle:{color:"#989898","font-size":"14px"}},[t._v("事前处置率")])])])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"firstItem"},[a("div",{staticClass:"firstItemDiv"},[a("span",{staticStyle:{color:"#989898","font-size":"14px"}},[t._v("出院总人次")])])])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"firstItem"},[a("div",{staticClass:"firstItemDiv"},[a("span",{staticStyle:{color:"#989898","font-size":"14px"}},[t._v("住院总费用")])])])}],O={name:"dangerStat",components:{},props:{allReceiveData:{type:Object,default:function(){}}},data:function(){return{formatAmountNotoFixed:g,formatAmountDependingOnValue:y,formatUnit:b,dangerInfo:{riskAmount:34.5,riskAmountMoM:"",leaveRate:.3322,leaveRateMoM:.4534,resolveRisks:1092329,resolveRisksMoM:-.4534,preRate:.3322,preRateMoM:.4534,totalPts:103020,totalPtsMoM:.4534,totalAmount:100100,totalAmountMoM:.4534}}},mounted:function(){},methods:{fnQuery:function(){var t=this,e=this.baseInfoForm.getFieldsValue();null!=e.startDate&&""!=e.startDate?null!=e.endDate&&""!=e.endDate?this.baseInfoForm.validateFields((function(a,i){a||t.$emit("fnQuery",e)})):this.$message.error("请选择结束时间！"):this.$message.error("请选择开始时间！")},fnReset:function(){this.baseInfoForm.resetFields(),this.fnQuery()}}},Q=O,Z=(0,k.Z)(Q,L,P,!1,null,"4c0b9272",null),V=Z.exports,B=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{height:"100%",display:"flex","flex-direction":"column"}},[a("div",[a("ta-title",{attrs:{title:"风险构成"}},[a("span",{staticStyle:{display:"inline-block",float:"right","margin-right":"10px"}},[a("ta-tooltip",{attrs:{placement:"top"}},[a("template",{slot:"title"},[a("span",{staticStyle:{"font-size":"12px"}},[t._v(" 按金额统计占比"),a("br"),t._v(" 报销标志：指结算时HIS上传给医保中心的项目报销标志"),a("br")])]),a("ta-icon",{attrs:{type:"question-circle"}})],2)],1)])],1),a("div",{staticStyle:{flex:"1"}},[a("div",{staticClass:"container"},[t._m(0),a("div",{staticClass:"divider"},[a("ta-divider",{staticClass:"ta-divider",attrs:{type:"vertical"}})],1),t._m(1),a("div",{staticClass:"divider"},[a("ta-divider",{staticClass:"ta-divider",attrs:{type:"vertical"}})],1),t._m(2)])])])},Y=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"item",staticStyle:{width:"100%"}},[a("div",{staticClass:"allEchar",attrs:{id:"echarts1"}})])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"item",staticStyle:{width:"100%"}},[a("div",{staticClass:"allEchar",attrs:{id:"echarts2"}})])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"item",staticStyle:{width:"100%"}},[a("div",{staticClass:"allEchar",attrs:{id:"echarts3"}})])}],N={name:"dangerStat",components:{TaTitle:o.Z},props:{allReceiveData:{type:Object,default:function(){}}},watch:{allReceiveData:{handler:function(t,e){this.searchObj=t.param,this.searchCondition=t.searchCondition},deep:!0}},data:function(){return{formatAmountNotoFixed:g,formatAmountDependingOnValue:y,formatUnit:b,riskComposeEchar:null,riskComposeEchar2:null,riskComposeEchar3:null,searchObj:{},searchCondition:{},data:[{value:0,name:"违规类"},{value:0,name:"可疑类"},{value:0,name:"提示类"}],data2:[{value:0,name:"药品"},{value:0,name:"诊疗项目"},{value:0,name:"耗材"}],data3:[{value:0,name:"自费"},{value:0,name:"报销"}]}},mounted:function(){this.riskComposeEchar=this.$echarts.init(document.getElementById("echarts1")),this.riskComposeEchar2=this.$echarts.init(document.getElementById("echarts2")),this.riskComposeEchar3=this.$echarts.init(document.getElementById("echarts3")),this.riskComposeEchar.setOption(this.generateOptions(this.data,"1")),this.riskComposeEchar2.setOption(this.generateOptions(this.data2,"2")),this.riskComposeEchar3.setOption(this.generateOptions(this.data3,"3"))},methods:{generateOptions:function(t,e){var a,i=["#FF7A85","#71B6BA","#52A2FF"];switch(e){case"1":a="违规等级占比";break;case"2":a="项目类型占比";break;case"3":a="项目报销标志占比";break;default:a="未知";break}var s=t.reduce((function(t,e){return t+e.value}),0);return{color:i,title:[{text:a,left:"center",top:"5%",textStyle:{fontSize:16,color:"#323233",fontWeight:350}},{text:s,left:22.5-s.toString().length+"%",top:"middle",textStyle:{fontSize:14,fontWeight:"bold"}}],tooltip:{trigger:"item",confine:!0,appendToBody:!0,formatter:function(t){return t.marker+""+t.data.name+"</br>数量："+t.data.value+"</br>占比："+t.percent+"%"}},legend:{type:"scroll",orient:"vertical",left:"45%",align:"left",top:"middle",formatter:function(e){for(var a,i,n=0;n<t.length;n++)t[n].name===e&&(a=t[n].value,i=s>0?(a/s*100).toFixed(2):0);var o=["{a|"+e+"\t}","{b|"+i+"%\t}"];return o.join("")},textStyle:{rich:{a:{fontSize:14,width:50},b:{verticalAlign:"right",align:"right",fontSize:14,width:65}}}},series:[{name:"1"==e?"异常违规等级占比":"异常项目类型占比",type:"pie",center:["25%","50%"],radius:["35%","50%"],avoidLabelOverlap:!1,label:{show:!1,position:"center"},emphasis:{label:{show:!1,fontSize:40,fontWeight:"bold"}},labelLine:{show:!1},data:t}]}},init:function(){var t=this;this.Base.submit(null,{url:"detailsOverviewCommunity/queryOverallPie",data:this.searchObj.conditionList,showPageLoading:!1},{successCallback:function(e){var a=e.data.result;a&&(t.data=[{value:a.violationNum,name:"违规类"},{value:a.suspiciousNum,name:"可疑类"},{value:a.promptNum,name:"提示类"}],t.data2=[{value:a.medNum,name:"药品"},{value:a.diagNum,name:"诊疗项目"},{value:a.useupNum,name:"耗材"}],t.riskComposeEchar=t.$echarts.init(document.getElementById("echarts1")),t.riskComposeEchar2=t.$echarts.init(document.getElementById("echarts2")),t.riskComposeEchar3=t.$echarts.init(document.getElementById("echarts3")),t.riskComposeEchar.setOption(t.generateOptions(t.data,"1")),t.riskComposeEchar2.setOption(t.generateOptions(t.data2,"2")),t.riskComposeEchar3.setOption(t.generateOptions(t.data3,"3")))}})},fnQuery:function(){},fnReset:function(){this.baseInfoForm.resetFields(),this.fnQuery()}}},q=N,W=(0,k.Z)(q,B,Y,!1,null,"9d2c1cec",null),H=W.exports,U=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{height:"100%",display:"flex","flex-direction":"column"}},[a("div",[a("ta-title",{attrs:{title:"异常项目TOP5"}},[a("span",{staticStyle:{display:"inline-block",float:"right","margin-right":"10px"}},[a("ta-select",{staticStyle:{width:"90px"},attrs:{value:t.ake003,placeholder:"请选择",showSearch:"",size:"small"},on:{change:t.ake003Change}},[a("ta-select-option",{attrs:{value:"0"}},[t._v("全部类型")]),a("ta-select-option",{attrs:{value:"1"}},[t._v("药品")]),a("ta-select-option",{attrs:{value:"2"}},[t._v("诊疗")]),a("ta-select-option",{attrs:{value:"4"}},[t._v("耗材")])],1),a("ta-tooltip",{staticStyle:{"margin-left":"10px"},attrs:{placement:"top"}},[a("template",{slot:"title"},[a("span",{staticStyle:{"font-size":"12px"}},[t._v(" 按风险遗留率 占比排名"),a("br"),t._v(" 风险遗留：指预警过，但出院末次审核仍时存在疑点"),a("br")])]),a("ta-icon",{attrs:{type:"question-circle"}})],2)],1)])],1),a("div",{staticStyle:{flex:"1"}},[a("div",{staticClass:"container"},[a("ta-big-table",{ref:"xTable",attrs:{height:"auto",data:t.tableData,border:"inner",width:"100%","show-header":!1,"highlight-hover-row":"","highlight-current-row":"","auto-resize":"","show-overflow":"",resizable:""}},[a("ta-big-table-column",{attrs:{type:"seq",align:"center",title:"排名","min-width":"50"}}),a("ta-big-table-column",{attrs:{field:"ake001",title:"项目名称",align:"center","min-width":"200"}}),a("ta-big-table-column",{attrs:{field:"amount",align:"center",title:"金额","show-overflow":"","min-width":"150"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[t._v(" ¥"+t._s(t.formatAmountDependingOnValue(a.amount)+t.formatUnit(a.amount,"元"))+" ")]}}])}),a("ta-big-table-column",{attrs:{field:"progress",align:"center",title:"占比","min-width":"220"},scopedSlots:t._u([{key:"default",fn:function(t){var e=t.row,i=t.rowIndex;return[a("ta-progress",i<3?{staticStyle:{width:"100%"},attrs:{type:"line",status:"exception",percent:100*e.percent,showInfo:!1}}:{staticStyle:{width:"100%"},attrs:{type:"line",status:"active",percent:100*e.percent,showInfo:!1}})]}}])}),a("ta-big-table-column",{attrs:{field:"percent",align:"center",title:"占比","min-width":"60"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[t._v(" "+t._s(100*a.percent.toFixed(2))+"% ")]}}])})],1)],1)])])},X=[],G={name:"excepItems",components:{TaTitle:o.Z},props:{allReceiveData:{type:Object,default:function(){}}},data:function(){return{formatAmountNotoFixed:g,formatAmountDependingOnValue:y,formatUnit:b,ake003:"0",tableData:[{ake001:"项目1",amount:1e5,percent:.1},{ake001:"项目2",amount:1e5,percent:.1},{ake001:"项目3",amount:1e5,percent:.1},{ake001:"项目4",amount:1e5,percent:.1},{ake001:"项目5",amount:1e5,percent:.1}]}},mounted:function(){},methods:{ake003Change:function(t){this.ake003=t},fnQuery:function(){var t=this,e=this.baseInfoForm.getFieldsValue();null!=e.startDate&&""!=e.startDate?null!=e.endDate&&""!=e.endDate?this.baseInfoForm.validateFields((function(a,i){a||t.$emit("fnQuery",e)})):this.$message.error("请选择结束时间！"):this.$message.error("请选择开始时间！")},fnReset:function(){this.baseInfoForm.resetFields(),this.fnQuery()},PageParams:function(){return this.allReceiveData}}},K=G,j=(0,k.Z)(K,U,X,!1,null,"0572f822",null),J=j.exports,$=r.Z,tt={components:{DangerStat:V,RiskCompose:H,ExcepItems:J,SearchTerm:I,TaTitle:o.Z,detailInfo:T},data:function(){return{tableData:[],isClickxm:!1,isClickks:!1,params:{},formShowAll:!1,option:{legend:{}},showHiddenOrSortColumn:{themeColor:"red",icon:"appstore",showHiddenConfig:{open:!0,disabledHiddenColumns:function(t){return!1}},columnSortConfig:{open:!0,onMove:function(t,e){e.dragColumn,e.dropColumn;return!0},dragEnd:function(t,e){e.dragColumn,e.resultColumns}},successCallback:this.successCallback,disabledControlCol:function(t){}}}},computed:{formBoxStyle:function(){return this.formShowAll?{height:"auto"}:{height:"50px"}}},mounted:function(){this.asyncLoad(this.params)},methods:{moment:u(),formShowAllChange:function(){this.formShowAll=!this.formShowAll},formatPercentageValue:function(t,e){return t&&0!=t&&e&&0!=e?(t/e*100).toFixed(2)+"%":"0%"},handleQuery:function(){this.$refs.term.fnQuery()},handleReset:function(){this.$refs.term.fnReset()},cellStyle:function(t){t.row,t.rowIndex;var e=t.column;t.columnIndex;if("aae386"!==e.property&&"seq"!==e.type)return{color:"#19abf0"}},cellClickEvent:function(t){var e=t.row;this.isClickxm=!0;var a=this.params,i=[];i.push(e.aaz307),a.aaz307=i,this.isClickks&&(a.aka063=[],a.aka063=this.params.aka063),this.visible=!0},fnQuery:function(t){this.params=t,this.asyncLoad(this.params)},PageParams:function(){return this.params},exportTable:function(){var t,e=[],a=[],i=this.$refs.xTable1.getColumns(),s=this.$refs.xTable2.getColumns(),o=(0,n.Z)(i);try{for(o.s();!(t=o.n()).done;){var r=t.value;"排名"!==r.title&&(r.visible&&e.push({header:r.title,key:r.property,width:20}))}}catch(p){o.e(p)}finally{o.f()}var l,c=(0,n.Z)(s);try{for(c.s();!(l=c.n()).done;){var u=l.value;"排名"!==u.title&&(u.visible&&a.push({header:u.title,key:u.property,width:20}))}}catch(p){c.e(p)}finally{c.f()}var d=$(this.tableData);d.forEach((function(t){var e=t.totalmoney;e>0?Object.keys(t).forEach((function(a){a.startsWith("fy")&&"number"===typeof t[a]&&(t["".concat(a,"zb")]=(t[a]/e*100).toFixed(2)+"%")})):Object.keys(t).forEach((function(e){e.startsWith("fy")&&"number"===typeof t[e]&&(t["".concat(e,"zb")]="0.00%")}))}));var f=$(this.tableData2);f.forEach((function(t){var e=t.totalcount;e>0?Object.keys(t).forEach((function(a){a.startsWith("fy")&&"number"===typeof t[a]&&(t["".concat(a,"zb")]=(t[a]/e*100).toFixed(2)+"%")})):Object.keys(t).forEach((function(e){e.startsWith("fy")&&"number"===typeof t[e]&&(t["".concat(e,"zb")]="0.00%")}))}));var m={fileName:"医疗服务费用统计",sheets:[{name:"金额统计",column:{complex:!1,columns:e},rows:d},{name:"数量统计",column:{complex:!1,columns:a},rows:f}]};this.Base.generateExcel(m)},asyncLoad:function(t){this.myChart=l.init(document.getElementById("main"));var e=[[11,55,19,"测试科室2"],[22,25,11,"测试科室121"],[32,56,71,"测试科室11"],[43,33,71,"测试科室12"],[52,42,24,"测试科室111"]],a=[{name:"amount",index:0,text:"风险项目金额"},{name:"AQIindex",index:1,text:"风险项目种类数"},{name:"PM25",index:2,text:"风险遗留率"}],i={normal:{opacity:.8,shadowBlur:10,shadowOffsetX:0,shadowOffsetY:0,shadowColor:"rgba(0, 0, 0, 0.5)"}};this.option={color:["#1890FF"],legend:{y:"top",data:["风险科室"],textStyle:{fontSize:16}},grid:{x:"10%",x2:150,y:"18%",y2:"10%"},tooltip:{padding:10,backgroundColor:"#222",borderColor:"#777",borderWidth:1,formatter:function(t){var e=t.value;return'<div style="border-bottom: 1px solid rgba(255,255,255,.3); font-size: 18px;padding-bottom: 7px;margin-bottom: 7px">'+e[3]+"：</div>"+a[0].text+"："+e[0]+"<br>"+a[1].text+"："+e[1]+"<br>"+a[2].text+"："+e[2]+"<br>"}},xAxis:{type:"value",name:"风险项目金额",nameGap:16,nameTextStyle:{fontSize:14},max:70,splitLine:{show:!1},axisLine:{lineStyle:{color:"#777"}},axisTick:{lineStyle:{color:"#777"}},axisLabel:{formatter:"{value}",textStyle:{}}},yAxis:{type:"value",name:"风险项目种类数",nameLocation:"end",nameGap:20,nameTextStyle:{fontSize:16},axisLine:{lineStyle:{color:"#777"}},axisTick:{lineStyle:{color:"#777"}},splitLine:{show:!1},axisLabel:{textStyle:{}}},visualMap:[{left:"right",top:"10%",dimension:2,min:0,max:100,itemWidth:30,itemHeight:120,calculable:!0,precision:.1,text:["气泡大小：风险遗留率"],textGap:30,textStyle:{},inRange:{symbolSize:[10,70]},outOfRange:{symbolSize:[10,70],color:["rgba(255,255,255,.2)"]},controller:{inRange:{color:["#c23531"]},outOfRange:{color:["#444"]}}}],series:[{name:"风险科室",type:"scatter",itemStyle:i,data:e}]},this.myChart.setOption(this.option)}}},et=tt,at=(0,k.Z)(et,i,s,!1,null,"6c24933e",null),it=at.exports},36766:function(t,e,a){"use strict";var i=a(66586);e["Z"]=i.Z},26263:function(t,e,a){"use strict";a.d(e,{s:function(){return i},x:function(){return s}});var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},s=[]},66586:function(t,e){"use strict";var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};e["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}},83231:function(t,e,a){"use strict";var i=a(48534);a(36133);function s(t){return t?t.akb020&&t.aaz263?"personal":t.akb020&&t.aaz309&&t.aaz307?"group":t.akb020&&t.aaz307?"department":t.akb020?"hospital":"all":"all"}function n(t){return o.apply(this,arguments)}function o(){return o=(0,i.Z)(regeneratorRuntime.mark((function t(e){var a,i,n,o,r,l,c,u,d,f,m,p;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Base.submit(null,{url:"/miimCommonRead/queryDataPermission",showPageLoading:!1,data:{jobnumber:e},autoValid:!0});case 2:return a=t.sent,i=new Set,n=new Set,a.data.permission.forEach((function(t){var e=s(t);"hospital"===e&&i.add(t.akb020),"department"===e&&n.add(t.aaz307)})),o=a.data.permission.filter((function(t){return"department"===s(t)||!n.has(t.aaz307)})).filter((function(t){return"hospital"===s(t)||!i.has(t.akb020)})),r=new Set(o.map((function(t){return t.akb020})).filter((function(t){return null!=t}))),l=new Set(o.map((function(t){return t.aaz307})).filter((function(t){return null!=t}))),c=new Set(o.map((function(t){return t.aaz309})).filter((function(t){return null!=t}))),u=new Set(o.map((function(t){return t.aaz263})).filter((function(t){return null!=t}))),d=!1,f=!1,m=!1,p=!1,1===r.size&&(d=!0),1===l.size&&1===r.size&&(f=!0),1===l.size&&1===r.size&&1===c.size&&(m=!0),1===r.size&&0===l.size&&1===u.size&&(p=!0),t.abrupt("return",{akb020Set:r,aaz307Set:l,aaz263Set:u,aaz309Set:c,akb020Disable:d,aaz307Disable:f,aaz263Disable:p,aaz309Disable:m});case 20:case"end":return t.stop()}}),t)}))),o.apply(this,arguments)}function r(t,e){Base.submit(null,{url:"miimCommonRead/getAa01AAE500StartStop",data:t},{successCallback:function(t){return e(t)}})}function l(t,e){Base.submit(null,{url:"miimCommonRead/insertTableColumShow",data:t,autoValid:!1},{successCallback:function(t){return e("success")},failCallback:function(t){return e("fail")}})}var c={zbx01:{footer:function(){return"统筹金总额"}},zbx02:{footer:function(){return"基金支出总额"}},zbx03:{footer:function(){return"总费用"}},zbx04:{footer:function(){return"总住院日"}}};e["Z"]={permissionCheck:n,getAa01AAE500StartStop:r,insertTableColumShow:l,props:c,moneyNumFormat:function(t){var e=t.cellValue;return e||"—"}}},88001:function(t){"use strict";t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADgAAAA4CAYAAACohjseAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAAEnQAABJ0Ad5mH3gAAAUxSURBVGhD3ZrNyxxFEMb3fxG8eRA8CB4Ezx4ED4I3b4IHwYPgQchB8CYEBEElSjCooCjmEIhE/IoEYgwaiQSC3xgMokTcffe7rV/N9jI7Wz3TH7NvNm/BA8NUT3c/U9VdVT0zcDuQxV9X3fTyK2564UU3+eQZNz79uBu985AbvnGPgmvuoaPN9NvX9JldSD8EF3M3//1LN/nqBTd6+0E3fPWuLIxOPeAm549pX/TZhxQRXE5uuenXx93ozXvNCRdBLI11GaNE8gjOx2525cRuiDXAGLg7Y+ZIMsHZtffUlazJ7BKjt+7XsVMlnqC8QTYFa/DDxOTTZ5OsGUVwObzpDj54xBzwduDgw0d1TjHSSXDx52V1D2ug2wnmtLj53WqWYWklCDmNXcYAewGZWxfJIEFcYB8t1wRzbHNXm6As4n1ac11gTYY2HpPgPuyWqdDd1ZAtgsQaq4M7AVac3CQoZu4tiMsGoMm0JNLz66fd/I8LFeSae+j6zoSYe9NVNwiSflkPpoD1MP/p7Hqg5X83KmI/nqkg19xTkYR6ceOiG595wuwrB3Coy5ogSW3JG6WKwDrI4p/rbnrxpapEMtoCdFQfvkyC+MH7D5ttUwCHeoK+JkhVYD0Qg4OPHtOtGkw+f84NX7/bbGdC2rJB6FY/HbrxuaftdgmAi5eKoLhKrvWYkNaDYoHRyfvMNjHgWa0DRbCs1SYW9OXrSSVIx1bDLmA5JSeumWS1EKSP2fcndWKllvQvSwnmvDHWHG6F5Xoh5yF9zX85p+5asibhtCaYc8yA1SDYdEuK09nVU3GksZi0xWob9yXELG/9XL28+v0EwEkJsotZDdqgqZGIbigN3fjsk5XbSkhoJYmlpA1txx8/taXnHkJ/TV0s4DbgjVvKNhDnCAUhAjq5NpId5DyYIHHS0sWAhGLAwY6lDELchyBOnDP1KwRJRpIDeAiSm13BbZCaWNNeB20J4h5bJBPIAb/dW0shBsx1wAGspQwBs5NqWToLdZIp5DwoaHMLALgNYixRh0+cLR3QPDRRCAtWXwDd/NfPTF0X4DZIPZKAnLqcoQNYZ3rp+BaW//6msHRtQR3rLf6+Zuo6Idx6JxiCPpcR14oJ9u2iIeQSLHbRXW8yHrkEizeZvsMEaZdfb3VoASywdLMf3jX76iVMpAZ6yiodNJCgc5833oSvFy1daA69BPqcVI30SXPYeobSgRwX5eC5OFXLSbY5Q0GoxC29hVSCmiCIFCfbdJLjAkzWKpdCSCIo2zvlkn46sPQR0BM2kargPX/MbNQGTr4pSvU0IMJVUwhqNiTrvOR0HU5rgrlHFnoeI6IFawfJWIL+2C/F/S1sHFnwtlIzGg9/NKD5ZEsfnQTlWbWcyPSbl+02sZC+4IRUBEWS68Ia1JLirqybUKWgRxmBg2We4dmSmFcHXLysCZYe/LJesBDC7sVE2zYgdLRhI/HPcEpntU1B8OAXyYmJTbCt+0ljEa5xXx/UudaPlisX0peRmE21AQ512SBIKtXXR0+2aSykpCRZpiIAXHNv8sXzRT8NWWDu/puIl02CIgxuPXwngLk3ZYsg0qfLHBaiP4CqiJmP9CdsRNOwI/sTwko0F8xMAA4FMrfs30i8QHIfLcmcusghnQQRXGCf1mSvv3KtRRbxPuyuulsGNhRL4gmuhFiTe4RQAlzSinNdkkxQRd7gkf0hti4ktUfzl+amSOJMgUltWJJfav66Tz+lh4QKAbfCAmxMHMDqOSoxVcA199DRhtMvnulfnPsfBlVy9+TxPsEAAAAASUVORK5CYII="},61525:function(t){"use strict";t.exports="data:image/png;base64,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"},55382:function(){},61219:function(){}}]);
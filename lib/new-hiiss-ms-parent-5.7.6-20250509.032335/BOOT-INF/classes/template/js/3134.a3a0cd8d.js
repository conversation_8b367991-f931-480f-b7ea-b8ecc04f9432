"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[3134],{88412:function(t,e,a){var i=a(26263),s=a(36766),l=a(1001),o=(0,l.Z)(s.Z,i.s,i.x,!1,null,"5e7ef0ae",null);e["Z"]=o.exports},93134:function(t,e,a){a.r(e),a.d(e,{default:function(){return O}});var i=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("div",{staticClass:"fit"},[i("ta-border-layout",{attrs:{layout:{footer:"50%",left:"35%"},"left-cfg":{expand:!0,expandText:"",showBar:!0}}},[i("div",{attrs:{slot:"leftExtraContent"},slot:"leftExtraContent"},[i("ta-form",{staticStyle:{"margin-top":"10px"},attrs:{autoFormCreate:function(e){t.deptForm=e},layout:"horizontal"}},[i("ta-row",[i("ta-col",{attrs:{span:12}},[i("ta-form-item",{attrs:{fieldDecoratorId:"admDeptCode",span:12,required:!0}},[i("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("科室")]),i("ta-select",{attrs:{options:e.options},on:{select:e.deptSelect}})],1)],1)],1)],1)],1),i("div",{staticClass:"fit",staticStyle:{height:"85%"},attrs:{slot:"left"},slot:"left"},[i("ta-title",[e._v("患者列表"),i("span",{staticStyle:{"font-weight":"normal","font-size":"14px"}},[e._v("（请先选择患者）")])]),i("div",{staticStyle:{height:"100%"}},[i("ta-table",{attrs:{columns:e.patientColumns,dataSource:e.patientData,customRow:e.fnDeptRow,bordered:!0,haveSn:!0,size:"small",scroll:{y:"100%"}},on:{"update:columns":function(t){e.patientColumns=t}}})],1)],1),i("div",{staticClass:"fit",staticStyle:{height:"100%"}},[i("ta-tabs",{staticClass:"fit",on:{change:e.tabChange},model:{value:e.activeKey,callback:function(t){e.activeKey=t},expression:"activeKey"}},[i("ta-tab-pane",{key:"order",attrs:{tab:"处方"}},[i("ta-table",{ref:"orderRef",attrs:{columns:e.orderColumns,dataSource:e.orderData,customRow:e.fnCustomRow,bordered:!0,size:"small",scroll:{y:"100%",x:"100%"}},on:{"update:columns":function(t){e.orderColumns=t}},scopedSlots:e._u([{key:"action",fn:function(t,a){return i("span",{},[i("ta-popconfirm",{attrs:{title:"是否删除?"},on:{confirm:function(){return e.deleteOrder(a)}}},[i("a",{style:{marginLeft:"10px"}},[i("ta-icon",{attrs:{type:"delete"}}),e._v("删除")],1)])],1)}}])})],1),i("ta-tab-pane",{key:"disease",attrs:{tab:"病种"}},[i("ta-table",{ref:"diseaseRef",attrs:{columns:e.diseaseColumns,dataSource:e.diseaseData,customRow:e.fnCustomRow,bordered:!0,size:"small",scroll:{y:"100%",x:"100%"}},on:{"update:columns":function(t){e.diseaseColumns=t}},scopedSlots:e._u([{key:"action",fn:function(t,a){return i("span",{},[i("ta-popconfirm",{attrs:{title:"是否删除?"},on:{confirm:function(){return e.deleteDisease(a)}}},[i("a",{style:{marginLeft:"10px"}},[i("ta-icon",{attrs:{type:"delete"}}),e._v("删除")],1)])],1)}}])})],1),i("ta-tab-pane",{key:"diag",attrs:{tab:"诊断"}},[i("ta-table",{ref:"diagRef",attrs:{columns:e.diagColumns,dataSource:e.diagData,customRow:e.fnCustomRow,bordered:!0,size:"small",scroll:{y:"100%",x:"100%"}},on:{"update:columns":function(t){e.diagColumns=t}},scopedSlots:e._u([{key:"action",fn:function(t,a){return i("span",{},[i("ta-popconfirm",{attrs:{title:"是否删除?"},on:{confirm:function(){return e.deleteDiag(a)}}},[i("a",{style:{marginLeft:"10px"}},[i("ta-icon",{attrs:{type:"delete"}}),e._v("删除")],1)])],1)}}])})],1)],1)],1),i("div",{staticClass:"fit",attrs:{slot:"footer"},slot:"footer"},["order"==e.activeKey?i("new-order",{ref:"order",attrs:{patientInfo:e.patientInfo},on:{fnQuery:e.fnQuery,doCheckWithUrl:e.doCheckWithUrl,doMedicalCheck:e.doMedicalCheck}}):e._e(),"disease"==e.activeKey?i("new-disease",{ref:"disease",attrs:{patientInfo:e.patientInfo},on:{fnQuery:e.fnQuery}}):e._e(),"diag"==e.activeKey?i("new-dise",{ref:"dise",attrs:{patientInfo:e.patientInfo},on:{fnQuery:e.fnQuery}}):e._e()],1)]),i("ta-modal",{attrs:{title:"智能提醒",footer:null,height:"750px",width:"1410px",destroyOnClose:!0},on:{cancel:e.handleCancel},model:{value:e.zntxModal,callback:function(t){e.zntxModal=t},expression:"zntxModal"}},[1==e.show?i("iframe",{staticStyle:{height:"100%",width:"100%"},attrs:{src:e.url,id:"iframe"}}):e._e(),-1==e.show?i("div",{staticStyle:{height:"100%"}},[i("ta-title",{staticStyle:{color:"red"}},[e._v("MESSAGE：")]),i("div",{staticStyle:{border:"1px solid #1b65b9",width:"100%",height:"80%",padding:"15px","font-size":"16px",overflow:"auto"}},[e._v(e._s(e.errMessage))])],1):e._e()])],1)},s=[],l=a(88412),o=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("div",{staticClass:"fit"},[i("ta-title",[e._v("新开处方")]),i("ta-form",{attrs:{autoFormCreate:function(e){t.orderForm=e},layout:"horizontal"}},[i("ta-row",[i("ta-col",{attrs:{span:7}},[i("ta-form-item",{attrs:{fieldDecoratorId:"diseCode",labelCol:{span:8},wrapperCol:{span:16}}},[i("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("适用病种")]),i("ta-select",{attrs:{options:e.diseaseOptions,"options-key":{value:"diseCode",label:"diseName",title:"diseName"}},on:{change:e.fnChangeDisease}})],1)],1),i("ta-col",{attrs:{span:7}},[i("ta-form-item",{attrs:{fieldDecoratorId:"ake003",labelCol:{span:8},wrapperCol:{span:16}}},[i("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("项目类型")]),i("ta-select",{attrs:{"collection-type":"AKE003"}})],1)],1),i("ta-col",{attrs:{span:7}},[i("ta-form-item",{attrs:{fieldDecoratorId:"hilist",labelCol:{span:8},wrapperCol:{span:16},required:!0,extra:"回车进行搜索"}},[i("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("项目名称")]),i("ta-input",{attrs:{placeholder:"输入项目名称或编码"},on:{pressEnter:e.handleSearch}})],1)],1),i("ta-col",{attrs:{span:7}},[i("ta-form-item",{attrs:{fieldDecoratorId:"drugUsedSdose",span:12,labelCol:{span:8},wrapperCol:{span:16},"init-value":1}},[i("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("单次剂量")]),i("ta-input")],1)],1),i("ta-col",{attrs:{span:7}},[i("ta-form-item",{attrs:{fieldDecoratorId:"cnt",span:12,labelCol:{span:8},wrapperCol:{span:16},required:!0,"init-value":1}},[i("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("总量")]),i("ta-input")],1)],1),i("ta-col",{staticStyle:{display:"flex"},attrs:{span:3}},[i("ta-button",{staticStyle:{"margin-left":"20px"},attrs:{type:"primary"},on:{click:e.addHilist}},[e._v("+")]),i("ta-button",{staticStyle:{"margin-left":"calc(50% - 50px)",width:"100px"},attrs:{type:"primary"},on:{click:e.doCheckWithUrl}},[e._v("处方审核")]),i("ta-button",{staticStyle:{"margin-left":"calc(50% - 50px)",width:"100px"},attrs:{type:"primary"},on:{click:e.doMedicalCheck}},[e._v("医保办审核")])],1)],1)],1),i("div",{staticStyle:{height:"calc(100% - 200px)"}},[i("ta-table",{attrs:{columns:e.newOrderColumns,dataSource:e.newOrderData,customRow:e.fnCustomRow,bordered:!0,size:"small",scroll:{x:"100%",y:"100%"}},on:{"update:columns":function(t){e.newOrderColumns=t}}})],1),i("ta-drawer",{ref:"drawer",attrs:{title:"项目选择(单击进行选择）",width:"500",placement:"right",closable:!0,visible:e.visible,destroyOnClose:""},on:{close:e.closeEdit}},[i("ta-table",{staticStyle:{height:"200px"},attrs:{columns:e.projectColumns,dataSource:e.dataSource,customRow:e.fnProjectRow},on:{"update:columns":function(t){e.projectColumns=t}}})],1)],1)},n=[],r=(a(32564),{name:"newOrder",components:{TaTitle:l.Z},props:{patientInfo:{type:Object}},data:function(){var t=[{title:"项目编码",dataIndex:"hilistCode",align:"center",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"项目名称",dataIndex:"hilistName",align:"center",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"数量",dataIndex:"cnt",align:"center",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"单价",dataIndex:"pric",align:"center",width:140,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"处方时间",dataIndex:"drordBegntime",align:"center",width:140,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"适用病种",dataIndex:"diseName",align:"center",width:140,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell}],e=[{title:"项目id",dataIndex:"hilistCode",align:"center",width:130,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"项目名称",dataIndex:"hilistName",align:"center",width:130,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"单位",dataIndex:"cntPrcunt",align:"center",width:60,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"单价",dataIndex:"pric",align:"center",width:60,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell}];return{projectColumns:e,newOrderColumns:t,newOrderData:[],dataSource:[],dataObject:null,visible:!1,diseaseOptions:[],diseName:""}},mounted:function(){this.fnQueryDisease()},watch:{visible:function(t){var e=this;setTimeout((function(){e.$refs.drawer.$el.style.display=t?"block":"none"}),300)}},methods:{closeEdit:function(){this.visible=!1,this.$refs.drawer.$el.style.display="none"},fnCustomRow:function(t,e){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px"}}},doCheckWithUrl:function(){this.$emit("doCheckWithUrl",this.newOrderData)},doMedicalCheck:function(){this.$emit("doMedicalCheck")},onSelect:function(t){this.visible=!1,this.dataObject=t,this.orderForm.setFieldsValue({hilist:t.hilistCode+"--"+t.hilistName})},handleSearch:function(){var t=this;this.Base.submit(null,{url:"doctorOrder/queryProject",data:{ake003:this.orderForm.getFieldValue("ake003"),info:this.orderForm.getFieldValue("hilist")},autoValid:!1},{successCallback:function(e){t.visible=!0,t.dataSource=e.data.list},failCallback:function(e){t.$message.error("项目信息查询失败")}})},addHilist:function(){if(null!=this.dataObject){var t=this.orderForm.getFieldsValue();if(void 0!=this.patientInfo&&""!=this.patientInfo&&null!=this.patientInfo)if(void 0!=t.hilist)if(void 0!=t.cnt){var e=/^([1-9][0-9]*)+(\.[0-9]{1,2})?$/;if(e.test(t.drugUsedSdose))if(e.test(t.cnt)){this.dataObject.cnt=t.cnt,this.dataObject.drugUsedSdose=t.drugUsedSdose,this.dataObject.diseCode=t.diseCode,this.dataObject.diseName=this.diseName;var a=new Date;this.dataObject.drordBegntime=a.getFullYear()+"-"+(a.getMonth()+1)+"-"+a.getDate()+" "+a.getHours()+":"+a.getMinutes()+":"+a.getSeconds(),this.newOrderData.push(this.dataObject),this.dataObject=null,this.orderForm.resetFields(),this.diseName=""}else this.$message.error("总量只能填入数字");else this.$message.error("单次剂量只能填入数字")}else this.$message.warn("总量未填写");else this.$message.warn("项目未填写");else this.$message.error("请先选择患者才能新增处方")}else this.$message.error("请选择项目")},fnFocusType:function(){void 0!=this.patientInfo&&""!=this.patientInfo&&null!=this.patientInfo||this.$message.error("请先选择患者才能新增医嘱")},fnProjectRow:function(t,e){var a=this;return{style:{cursor:"pointer"},on:{click:function(e){a.onSelect(t)}}}},fnQueryDisease:function(t){var e=this;if(t||this.patientInfo){var a="";if(t&&t.iptNo)a=t.iptNo;else{if(!this.patientInfo||!this.patientInfo.iptNo)return;a=this.patientInfo.iptNo}this.Base.submit(null,{url:"doctorOrder/queryDisease",data:{iptNo:a},autoValid:!1},{successCallback:function(t){e.diseaseOptions=t.data.list},failCallback:function(t){e.$message.error("病种数据加载失败")}})}},fnChangeDisease:function(t,e){this.diseName=e.data.props.diseName}}}),d=r,c=a(1001),u=(0,c.Z)(d,o,n,!1,null,"1d24f9b3",null),f=u.exports,h=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("div",{staticClass:"fit"},[i("ta-title",[e._v("新开诊断")]),i("ta-form",{attrs:{autoFormCreate:function(e){t.diseForm=e},layout:"horizontal"}},[i("ta-row",[i("ta-col",{attrs:{span:12}},[i("ta-form-item",{attrs:{fieldDecoratorId:"diseName",labelCol:{span:8},wrapperCol:{span:16},required:!0}},[i("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("诊断名称")]),i("ta-input",{attrs:{placeholder:"输入诊断名称或编码（回车进行搜索）"},on:{pressEnter:e.handleSearch}})],1)],1),i("ta-col",{attrs:{span:3}},[i("ta-button",{staticStyle:{"margin-left":"20px"},attrs:{type:"primary"},on:{click:e.addDise}},[e._v("+")])],1)],1)],1),i("div",{staticStyle:{height:"calc(100% - 150px)"}},[i("ta-table",{attrs:{columns:e.newDiseColumns,dataSource:e.newDiseData,customRow:e.fnCustomRow,bordered:!0,size:"small",scroll:{y:"100%"}},on:{"update:columns":function(t){e.newDiseColumns=t}}})],1),i("ta-button",{staticStyle:{"margin-left":"calc(50% - 50px)",width:"100px"},attrs:{type:"primary"},on:{click:e.doSubmit}},[e._v("提交")]),i("ta-drawer",{attrs:{title:"诊断选择(单击进行选择）",width:"500",placement:"right",closable:!0,visible:e.visible,destroyOnClose:""},on:{close:e.closeEdit}},[i("ta-table",{staticStyle:{height:"200px"},attrs:{columns:e.projectColumns,dataSource:e.dataSource,customRow:e.fnProjectRow},on:{"update:columns":function(t){e.projectColumns=t}}})],1)],1)},m=[],p={name:"newOrder",components:{TaTitle:l.Z},props:{patientInfo:{type:Object}},data:function(){var t=[{title:"诊断编码体系",dataIndex:"diseSys",align:"left",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"诊断编码",dataIndex:"diseCode",align:"left",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"诊断名称",dataIndex:"diseName",align:"left",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"诊断录入时间",dataIndex:"diagTime",align:"center",width:140,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell}],e=[{title:"诊断id",dataIndex:"diseCode",align:"left",width:130,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"诊断名称",dataIndex:"diseName",align:"left",width:130,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell}];return{projectColumns:e,newDiseColumns:t,newDiseData:[],dataSource:[],dataObject:null,visible:!1}},methods:{closeEdit:function(){this.visible=!1},fnCustomRow:function(t,e){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px"}}},doSubmit:function(){var t=this,e=this.patientInfo;e.newDise=JSON.stringify(this.newDiseData),this.Base.submit(null,{url:"doctorOrder/saveDise",data:e,autoValid:!1},{successCallback:function(e){t.$emit("fnQuery"),t.$message.success("保存诊断成功")},failCallback:function(e){t.$message.error("诊断信息保存失败")}})},handleSearch:function(){var t=this;this.Base.submit(null,{url:"doctorOrder/queryDise",data:{diseName:this.diseForm.getFieldValue("diseName")},autoValid:!1},{successCallback:function(e){t.visible=!0,t.dataSource=e.data.list},failCallback:function(e){t.$message.error("诊断信息查询失败")}})},addDise:function(){if(null!=this.dataObject)if(null!=this.patientInfo){var t=new Date;this.dataObject.diagTime=t.getFullYear()+"-"+(t.getMonth()+1)+"-"+t.getDate()+" "+t.getHours()+":"+t.getMinutes()+":"+t.getSeconds(),this.newDiseData.push(this.dataObject)}else this.$message.error("请先选择患者才能新增诊断");else this.$message.warn("请先选择诊断项目")},fnProjectRow:function(t,e){var a=this;return{style:{cursor:"pointer"},on:{click:function(e){a.onSelect(t)}}}},onSelect:function(t){this.dataObject=t,this.diseForm.setFieldsValue({diseName:t.diseCode+"--"+t.diseName}),this.visible=!1}}},C=p,g=(0,c.Z)(C,h,m,!1,null,"6da8a3ba",null),w=g.exports,v=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("div",{staticClass:"fit"},[i("ta-title",[e._v("新增病种")]),i("ta-form",{attrs:{autoFormCreate:function(e){t.diseForm=e},layout:"horizontal"}},[i("ta-row",[i("ta-col",{attrs:{span:12}},[i("ta-form-item",{attrs:{fieldDecoratorId:"diseName",labelCol:{span:8},wrapperCol:{span:16},required:!0}},[i("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("病种名称")]),i("ta-input",{attrs:{placeholder:"输入病种名称或编码（回车进行搜索）"},on:{pressEnter:e.handleSearch}})],1)],1),i("ta-col",{attrs:{span:3}},[i("ta-button",{staticStyle:{"margin-left":"20px"},attrs:{type:"primary"},on:{click:e.addDise}},[e._v("+")])],1)],1)],1),i("div",{staticStyle:{height:"calc(100% - 150px)"}},[i("ta-table",{attrs:{columns:e.newDiseColumns,dataSource:e.newDiseData,customRow:e.fnCustomRow,bordered:!0,size:"small",scroll:{y:"100%"}},on:{"update:columns":function(t){e.newDiseColumns=t}}})],1),i("ta-button",{staticStyle:{"margin-left":"calc(50% - 50px)",width:"100px"},attrs:{type:"primary"},on:{click:e.doSubmit}},[e._v("提交")]),i("ta-drawer",{attrs:{title:"病种选择(单击进行选择）",width:"500",placement:"right",closable:!0,visible:e.visible,destroyOnClose:""},on:{close:e.closeEdit}},[i("ta-table",{staticStyle:{height:"200px"},attrs:{columns:e.projectColumns,dataSource:e.dataSource,customRow:e.fnProjectRow},on:{"update:columns":function(t){e.projectColumns=t}}})],1)],1)},b=[],y={name:"newDisease",components:{TaTitle:l.Z},props:{patientInfo:{type:Object}},data:function(){var t=[{title:"诊断编码",dataIndex:"diseCode",align:"left",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"诊断名称",dataIndex:"diseName",align:"left",width:150,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"认定医生",dataIndex:"cognDoct",align:"left",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"认定时间",dataIndex:"cognDate",align:"center",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"认定状态",dataIndex:"cognState",align:"center",width:100,overflowTooltip:!0,collectionType:"CONG_STATE",customHeaderCell:this.fnCustomHeaderCell}],e=[{title:"病种编码",dataIndex:"diseCode",align:"left",width:130,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"病种名称",dataIndex:"diseName",align:"left",width:130,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell}];return{projectColumns:e,newDiseColumns:t,newDiseData:[],dataSource:[],dataObject:null,visible:!1}},methods:{closeEdit:function(){this.visible=!1},fnCustomRow:function(t,e){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px"}}},doSubmit:function(){var t=this,e={diseaseStr:JSON.stringify(this.newDiseData)};this.Base.submit(null,{url:"his/opsp/saveDisease",data:e},{successCallback:function(e){t.$emit("fnQuery"),t.$message.success("保存病种成功"),t.dataObject=null,t.diseForm.setFieldsValue({diseName:""}),t.newDiseData=[]},failCallback:function(e){t.$message.error("病种信息保存失败")}})},handleSearch:function(){var t=this;this.Base.submit(null,{url:"his/opsp/queryDisease",data:{diseName:this.diseForm.getFieldValue("diseName")},autoValid:!1},{successCallback:function(e){t.visible=!0,t.dataSource=e.data.list},failCallback:function(e){t.$message.error("病种信息查询失败")}})},addDise:function(){if(null!=this.dataObject)if(null!=this.patientInfo){var t=new Date;this.dataObject.cognDate=t.getFullYear()+"-"+(t.getMonth()+1)+"-"+t.getDate()+" "+t.getHours()+":"+t.getMinutes()+":"+t.getSeconds(),this.dataObject.cognDoct="测试医生",this.dataObject.cognState="1",this.dataObject.mdtrtId=this.patientInfo.mdtrtId,this.newDiseData.push(this.dataObject)}else this.$message.error("请先选择患者才能新增病种");else this.$message.warn("请先选择病种")},fnProjectRow:function(t,e){var a=this;return{style:{cursor:"pointer"},on:{click:function(e){a.onSelect(t)}}}},onSelect:function(t){this.dataObject=t,this.diseForm.setFieldsValue({diseName:t.diseCode+"--"+t.diseName}),this.visible=!1}}},S=y,x=(0,c.Z)(S,v,b,!1,null,"10952616",null),H=x.exports,D={name:"opsp",components:{NewOrder:f,TaTitle:l.Z,newDise:w,newDisease:H},data:function(){var t=[{title:"就诊号",dataIndex:"iptNo",align:"center",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"患者姓名",dataIndex:"psnName",align:"center",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"性别",dataIndex:"gend",align:"center",width:50,collectionType:"SEX",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"年龄",dataIndex:"age",align:"center",width:50,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell}],e=[{title:"项目编码",dataIndex:"hilistCode",align:"left",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"项目名称",dataIndex:"hilistName",align:"left",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"适用病种",dataIndex:"diseName",align:"left",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"数量",dataIndex:"cnt",align:"right",width:80,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"单价",dataIndex:"pric",align:"right",width:80,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"处方时间",dataIndex:"drordIsuTime",align:"center",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"操作",dataIndex:"action",align:"center",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell,scopedSlots:{customRender:"action"},fixed:"right"}],a=[{title:"疾病诊断编码体系",dataIndex:"diseSys",align:"center",width:100},{title:"诊断编码",dataIndex:"diseCode",align:"center",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"诊断名称",dataIndex:"diseName",align:"center",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"诊断医护人员名称",dataIndex:"diseDorName",align:"center",width:200,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"诊断录入时间",dataIndex:"diseTime",align:"center",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"操作",dataIndex:"action",align:"center",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell,scopedSlots:{customRender:"action"},fixed:"right"}],i=[{title:"病种编码",dataIndex:"diseCode",align:"center",width:120,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"病种名称",dataIndex:"diseName",align:"center",width:180,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"认定医师",dataIndex:"cognDoct",align:"center",width:120,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"认定时间",dataIndex:"cognDate",align:"center",width:120,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"认定状态",dataIndex:"cognState",align:"center",width:100,overflowTooltip:!0,collectionType:"CONG_STATE",customHeaderCell:this.fnCustomHeaderCell},{title:"操作",dataIndex:"action",align:"center",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell,scopedSlots:{customRender:"action"},fixed:"right"}];return{patientColumns:t,patientData:[],activeKey:"order",orderColumns:e,orderData:[],diseaseColumns:i,diseaseData:[],diagColumns:a,diagData:[],options:[],iptNo:"",patientInfo:null,zntxModal:!1,url:"",url2:"",show:"",errMessage:"",iframeWin:null,saveParam:"",engOptions:[],resultList:[]}},mounted:function(){this.$refs.orderRef.hideColumns(["drordNo"]),this.fnQueryDept(),window.addEventListener("message",this.handleMessageOpsp)},methods:{handleMessageOpsp:function(t){var e=t.data;"0"===e.infcode&&(this.zntxModal=!1,1===this.show&&this.saveInfo(JSON.stringify(e.result)))},fnCustomRow:function(t,e){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px"}}},fnCustomHeaderCell:function(t){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px",fontWeight:"bold"}}},tabChange:function(t){this.activeKey=t,this.fnQuery()},deleteOrder:function(t){var e=this;this.Base.submit(null,{url:"doctorOrder/deleteOrder",data:{drordNo:t.drordNo},autoValid:!1},{successCallback:function(t){e.fnQuery(),e.$message.success("医嘱删除成功")},failCallback:function(t){e.$message.error("医嘱删除失败")}})},deleteDiag:function(t){var e=this;this.Base.submit(null,{url:"doctorOrder/deleteDiag",data:{iptNo:this.patientInfo.iptNo,diseCode:t.diseCode},autoValid:!1},{successCallback:function(t){e.fnQuery(),e.$message.success("诊断删除成功")},failCallback:function(t){e.$message.error("诊断删除失败")}})},deleteDisease:function(t){var e=this;this.Base.submit(null,{url:"his/opsp/deleteDisease",data:{diseaseId:t.id},autoValid:!1},{successCallback:function(t){e.fnQuery(),e.$message.success("病种删除成功")},failCallback:function(t){e.$message.error("病种删除失败")}})},fnQueryDept:function(){var t=this;this.Base.submit(null,{url:"doctorOrder/queryDept",autoValid:!1},{successCallback:function(e){t.options=e.data.list},failCallback:function(e){t.$message.error("科室数据加载失败")}})},fnQueryEng:function(){var t=this;this.Base.submit(null,{url:"dischargeHis/queryEng",data:{trig_scen:0},autoValid:!1},{successCallback:function(e){t.engOptions=e.data.list},failCallback:function(e){t.$message.error("引擎数据加载失败")}})},deptSelect:function(t){var e=this;this.Base.submit(null,{url:"doctorOrder/queryPatient",data:{admDeptCode:t},autoValid:!1},{successCallback:function(t){e.patientData=t.data.list},failCallback:function(t){e.$message.error("患者数据加载失败")}})},audit:function(t){var e=this,a=t;a.newOrder=JSON.stringify(this.billData),a.infno=990107,a.trig_scen=2;this.Base.submit(null,{url:"doctorOrder/doCheck2",data:a,autoValid:!1},{successCallback:function(t){t.data.result.output.result},failCallback:function(t){e.$message.error("接口调用失败加载失败")}})},auditResult:function(t){window.open(t.url,"newwindow","height=750, width=1300, top=200, left=400, toolbar=no, menubar=no, scrollbars=no, resizable=no, location=no, status=no")},fnDeptRow:function(t,e){var a=this;return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",backgroundColor:t.iptNo==this.iptNo?"#7ecdf0":"",lineHeight:"22px",cursor:"pointer"},on:{click:function(e){a.iptNo=t.iptNo,a.patientInfo=t,"order"==a.activeKey&&(a.$refs.order.newOrderData=[],a.$refs.order.fnQueryDisease(a.patientInfo)),"diag"==a.activeKey&&(a.$refs.dise.newDiseData=[]),"disease"==a.activeKey&&(a.$refs.disease.newDiseData=[]),a.fnQuery()}}}},fnQuery:function(){var t=this,e=this.activeKey.charAt(0).toUpperCase()+this.activeKey.slice(1);this.Base.submit(null,{url:"doctorOrder/query"+e,data:{iptNo:this.iptNo},autoValid:!1},{successCallback:function(e){"order"===t.activeKey&&(t.orderData=e.data.list),"diag"===t.activeKey&&(t.diagData=e.data.list),"disease"===t.activeKey&&(t.diseaseData=e.data.list)},failCallback:function(e){t.$message.error("表格数据加载失败")}})},doCheck:function(t){var e=this,a=this.patientInfo;a.newOrder=JSON.stringify(t),a.infno=990104,a.trig_scen=2,this.Base.submit(null,{url:"his/opsp/doCheck",data:a,autoValid:!1},{successCallback:function(t){e.show=t.data.infcode,-1===e.show?e.errMessage=t.data.err_msg:1===e.show?(e.saveParam=t.data.saveParam,e.saveInfo(t.data.result)):(e.fnQuery(),e.$message.success(t.data.msg),e.$refs.order.newOrderData=[])},failCallback:function(t){e.$message.error("接口调用失败加载失败")}})},doCheckWithUrl:function(t){var e=this;if(0!==t.length){var a=this.patientInfo;null!==a?(a.newOrder=JSON.stringify(t),a.infno=990103,a.trig_scen=0,this.Base.submit(null,{url:"his/opsp/doCheckWithUrl",data:a,autoValid:!1},{successCallback:function(t){if(e.show=t.data.infcode,-1===e.show)e.errMessage=t.data.err_msg;else if(1===e.show){var a=t.data.result;e.patientInfo.call_id=a.call_id,e.url=a.url,e.saveParam=t.data.saveParam,void 0!==e.url&&""!==e.url?e.zntxModal=!0:e.saveInfo(t.data.result)}else e.fnQuery(),e.$message.success(t.data.msg)},failCallback:function(t){e.$message.error("接口调用失败加载失败")}})):this.$message.error("请选择患者！")}else this.$message.error("处方信息不能为空！")},doMedicalCheck:function(){var t=this,e=this.patientInfo;e.infno=990103,e.trig_scen="11",this.Base.submit(null,{url:"his/opsp/doMedicalCheckWithUrl",data:e,autoValid:!1},{successCallback:function(e){if(t.show=e.data.infcode,-1==t.show)t.$message.error(e.data.err_msg);else if(1===t.show){var a=e.data.result;t.patientInfo.call_id=a.call_id,t.url=a.url,""!==t.url&&(t.zntxModal=!0)}else t.fnQuery()},failCallback:function(e){t.$message.error("接口调用失败加载失败")}})},handleCancel:function(){this.zntxModal=!1,this.show},getOperate:function(){var t=this,e=this.patientInfo;e.infno=990108,e.trig_scen=2,e.lastInfno=990104,e.call_id=this.patientInfo.call_id,this.Base.submit(null,{url:"doctorOrder/getOperate",data:e,autoValid:!1},{successCallback:function(e){t.saveInfo(JSON.stringify(e.data.result.output.result))},failCallback:function(e){t.$message.error("保存数据失败")}})},saveInfo:function(t){var e=this;this.Base.submit(null,{url:"his/opsp/savePrescription",data:{jsonString:this.saveParam,result:t,cj:"医嘱"},autoValid:!1},{successCallback:function(t){e.fnQuery(),e.$message.success(t.data.msg)},failCallback:function(t){e.$message.error("保存数据失败")}})}}},I=D,k=(0,c.Z)(I,i,s,!1,null,"7aa34505",null),O=k.exports},36766:function(t,e,a){var i=a(66586);e["Z"]=i.Z},26263:function(t,e,a){a.d(e,{s:function(){return i},x:function(){return s}});var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},s=[]},66586:function(t,e){var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};e["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}}}]);
"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[8572],{18213:function(e,t,a){a.r(t),a.d(t,{default:function(){return y}});var r=function(){var e=this,t=this,a=t.$createElement,r=t._self._c||a;return r("div",{staticClass:"fit"},[r("ta-border-layout",{attrs:{layout:{header:"240px",footer:"90px"}}},[r("div",{attrs:{slot:"header"},slot:"header"},[r("ta-title",{attrs:{title:"查询条件"}}),r("ta-form",{attrs:{formLayout:"","auto-form-create":function(t){return e.queryParamForm=t}}},[r("ta-form-item",{attrs:{"field-decorator-id":"ykz108",label:"医保类型",span:6,required:""}},[r("ta-select",{attrs:{"allow-clear":!0,"options-key":{value:"id",label:"name"},options:t.medicalInsuranceTypeOptions},on:{select:t.searchSelectOptions}})],1),r("ta-form-item",{attrs:{"field-decorator-id":"aae043",label:"期号",span:6,required:""}},[r("ta-select",{attrs:{"options-key":{value:"id",label:"name"},options:t.batchOptions}})],1),r("ta-form-item",{attrs:{"field-decorator-id":"aaz319",label:"规则大类",span:6}},[r("ta-select",{attrs:{"options-key":{value:"id",label:"name"},options:t.ruleTypeOptions,"allow-clear":""}})],1),r("ta-form-item",{attrs:{"field-decorator-id":"ykz003",label:"是否需人工确认",span:6,"label-col":{span:10},"wrapper-col":{span:14}}},[r("ta-select",{attrs:{"collection-type":"YESORNO","allow-clear":""}})],1),r("ta-form-item",{attrs:{"field-decorator-id":"ake001",label:"三目编码",span:6}},[r("ta-input",{attrs:{"show-length":!0}})],1),r("ta-form-item",{attrs:{"field-decorator-id":"ake002",label:"三目名称",span:6}},[r("ta-input",{attrs:{"show-length":!0}})],1),r("ta-form-item",{attrs:{"field-decorator-id":"ykz018",label:"限制条件",span:6}},[r("ta-input",{attrs:{"show-length":!0}})],1),r("ta-form-item",{attrs:{"field-decorator-id":"ykz019",label:"审核标准",span:6,"label-col":{span:10},"wrapper-col":{span:14}}},[r("ta-input",{attrs:{"show-length":!0}})],1),r("ta-form-item",{attrs:{"field-decorator-id":"aae100",label:"是否启用",span:6}},[r("ta-select",{attrs:{"collection-type":"YESORNO","allow-clear":""}})],1),r("ta-form-item",[r("ta-button",{attrs:{type:"primary"},on:{click:t.queryRule}},[t._v("查询")])],1)],1)],1),r("div",{staticClass:"fit",staticStyle:{display:"flex","flex-direction":"column"}},[r("ta-title",{attrs:{title:"规则信息"}}),r("div",{staticStyle:{"flex-grow":"1"}},[r("ta-table",{attrs:{columns:t.tableColumns,"data-source":t.tableData,scroll:{y:"100%"}},scopedSlots:t._u([{key:"action",fn:function(e,a){return r("a",{on:{click:function(e){return t.showRuleConfigDetail(a)}}},[t._v("规则配置详情")])}}])})],1)],1),r("div",{attrs:{slot:"footer"},slot:"footer"},[r("ta-pagination",{ref:"pageHelper",attrs:{"data-source":t.tableData,url:t.URL.queryRule,defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"],params:t.getPageHelperParam},on:{"update:dataSource":function(e){t.tableData=e},"update:data-source":function(e){t.tableData=e}}})],1)]),r("ta-modal",{staticStyle:{"max-height":"90%","min-height":"40%"},attrs:{title:t.modalTitle,width:"95%",bodyStyle:{padding:"0"},footer:null,"destroy-on-close":!0},on:{cancel:t.modalCancel},model:{value:t.showModal,callback:function(e){t.showModal=e},expression:"showModal"}},[r("rule-config-detail",{attrs:{record:t.detailRecord}})],1)],1)},o=[],l=a(56664),i=a(95082),n=a(48534),s=(a(36133),a(88412)),c=a(66086),d=a(39744),u=[{title:"三目编码",dataIndex:"ake001",width:120,align:"left",overflowTooltip:!0},{title:"三目名称",dataIndex:"ake002",width:120,align:"left",overflowTooltip:!0},{title:"限制条件",dataIndex:"ykz018",width:180,align:"left",overflowTooltip:!0},{title:"审核标准",dataIndex:"ykz019",width:120,align:"left",overflowTooltip:!0},{title:"规则大类",dataIndex:"aaa167",width:130,align:"left",overflowTooltip:!0},{title:"是否需人工确认",dataIndex:"ykz003",width:80,align:"center",overflowTooltip:!0,collectionType:"YESORNO"},{title:"是否启用",dataIndex:"aae100",width:60,align:"center",overflowTooltip:!0,collectionType:"YESORNO"},{title:"操作",dataIndex:"action",width:80,align:"center",overflowTooltip:!0,scopedSlots:{customRender:"action"}}],p={name:"ruleDetails",components:{TaTitle:s.Z,RuleConfigDetail:c.Z},data:function(){return{URL:d.J,tableColumns:u,tableData:[],medicalInsuranceTypeOptions:[],batchOptions:[],ruleTypeOptions:[],showModal:!1,modalTitle:"",detailRecord:{}}},methods:{getPageHelperParam:function(){var e=this.queryParamForm.getFieldsValue();return Object.keys(e).forEach((function(t){""===e[t]&&delete e[t]})),e},searchSelectOptions:function(e){var t=this;return(0,n.Z)(regeneratorRuntime.mark((function a(){var r;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,d.Z.getSelectOptions({ykz108:e}).catch((function(){t.$mesage.error("医保规则下拉表失败")}));case 2:r=a.sent,t.batchOptions=r.batchOptions,t.ruleTypeOptions=r.ruleTypeOptions,t.batchOptions&&t.batchOptions.length>0?t.queryParamForm.setFieldsValue({aae043:t.batchOptions[0].id}):t.queryParamForm.setFieldsValue({aae043:null});case 6:case"end":return a.stop()}}),a)})))()},getTypeListOptions:function(){var e=this;return(0,n.Z)(regeneratorRuntime.mark((function t(){var a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,d.Z.getMedicalInsuranceTypeList();case 2:a=t.sent,e.medicalInsuranceTypeOptions=a.TypeList;case 4:case"end":return t.stop()}}),t)})))()},queryRule:function(){var e=this.getPageHelperParam();e.ykz108&&e.aae043?this.$refs.pageHelper.loadData():this.$message.error("未选择医保类型或期号")},showRuleConfigDetail:function(e){var t=this;return(0,n.Z)(regeneratorRuntime.mark((function a(){var r,o,n,s;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return t.modalTitle="规则详情【".concat(e.ake001,"——").concat(e.ake002,"】"),t.detailRecord.record=(0,i.Z)((0,i.Z)({},e),{},{aae043:t.queryParamForm.getFieldValue("aae043")}),a.next=4,Promise.all([d.Z.queryAuditStandDetail(t.detailRecord.record),d.Z.getRuleTree(t.detailRecord.record)]);case 4:r=a.sent,o=(0,l.Z)(r,2),n=o[0],s=o[1],t.detailRecord.standDetail=n.auditStand,t.detailRecord.ruleTree=s.ruleTree,t.showModal=!0;case 11:case"end":return a.stop()}}),a)})))()},modalCancel:function(){this.modalTitle="",this.showModal=!1,this.detailRecord={}}},mounted:function(){this.getTypeListOptions()}},f=p,m=a(1001),h=(0,m.Z)(f,r,o,!1,null,"ed7c385e",null),y=h.exports}}]);
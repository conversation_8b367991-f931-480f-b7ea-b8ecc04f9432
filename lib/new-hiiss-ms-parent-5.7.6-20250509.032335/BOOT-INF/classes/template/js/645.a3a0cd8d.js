(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[645],{88412:function(t,e,a){"use strict";var i=a(26263),n=a(36766),r=a(1001),l=(0,r.Z)(n.Z,i.s,i.x,!1,null,"5e7ef0ae",null);e["Z"]=l.exports},71996:function(t,e,a){"use strict";a.r(e),a.d(e,{default:function(){return z}});var i=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("div",{staticClass:"fit"},[i("ta-border-layout",{attrs:{layout:{header:"110px",footer:"0px"},showPadding:!0,"footer-cfg":{showBorder:!1}}},[i("div",{attrs:{slot:"header"},slot:"header"},[i("ta-title",{attrs:{title:"查询条件"}}),i("ta-form",{staticStyle:{"padding-top":"5px"},attrs:{autoFormCreate:function(e){t.baseInfoForm=e},layout:"horizontal",formLayout:!0}},[i("ta-form-item",{attrs:{span:5,fieldDecoratorId:"allDate",fieldDecoratorOptions:{rules:[{required:!0,message:"必须选取日期"}]},required:!0,"init-value":e.rangeValue,labelCol:{span:6},wrapperCol:{span:14},label:"开单时间"}},[i("ta-range-picker",{attrs:{"allow-one":!0}},[i("ta-icon",{attrs:{slot:"suffixIcon",type:""},slot:"suffixIcon"})],1)],1),i("ta-form-item",{attrs:{"field-decorator-id":"aaz307",span:4,labelCol:{span:6},wrapperCol:{span:12},label:"科室名称",disabled:e.paramsDisable.aaz307}},[i("ta-select",{staticStyle:{width:"120%"},attrs:{showSearch:"","show-search":!0,allowClear:"",placeholder:"科室名称筛选",options:e.ksList}})],1),i("ta-form-item",{attrs:{span:3,"field-decorator-id":"balcom",labelCol:{span:6},initValue:"up",label:"备案率"}},[i("ta-select",{staticStyle:{width:"50px"}},[i("ta-select-option",{attrs:{value:"up"}},[e._v(" >")]),i("ta-select-option",{attrs:{value:"down"}},[e._v(" <")])],1),i("ta-input-number",{staticStyle:{width:"90px"},attrs:{formatter:function(t){return""===t?"":t+"%"},parser:function(t){return t.replace("%","")},min:0,max:100},model:{value:e.bal,callback:function(t){e.bal=t},expression:"bal"}})],1),i("ta-button",{staticStyle:{"margin-right":"10px",float:"right"},attrs:{icon:"redo"},on:{click:e.fnReset}},[e._v("重置")]),i("ta-button",{staticStyle:{"margin-right":"20px",float:"right"},attrs:{type:"primary",icon:"search"},on:{click:e.fnQuery}},[e._v("查询 ")])],1)],1),i("div",{staticClass:"fit",staticStyle:{height:"90%"}},[i("ta-title",{staticStyle:{width:"50%"},attrs:{title:"查询结果"}}),i("ta-big-table",{ref:"Table",attrs:{height:"100%","highlight-hover-row":"","highlight-current-row":"","auto-resize":"","show-overflow":"",resizable:"","empty-text":"-",border:"",data:e.userList},on:{"cell-click":e.cellClickEvent}},[i("ta-big-table-column",{attrs:{type:"seq","header-align":"center",align:"center",title:"序号","min-width":"50",sortable:""}}),i("ta-big-table-column",{attrs:{field:"aae386","header-align":"center",align:"left",title:"科室名称","min-width":"180"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("ta-button",{attrs:{size:"small",type:"link"}},[i("span",{staticStyle:{color:"#00bcff","text-decoration":"underline"}},[e._v(e._s(a.aae386))])])]}}])}),i("ta-big-table-column",{attrs:{field:"bacs","header-align":"center",align:"right",title:"备案次数","min-width":"130"}}),i("ta-big-table-column",{attrs:{field:"bacszb","header-align":"center",align:"right",formatter:e.ratioFormat,title:"备案次数占比","min-width":"180"}}),i("ta-big-table-column",{attrs:{field:"shzl","header-align":"center",align:"right",title:"审核总次数","min-width":"140"}}),i("ta-big-table-column",{attrs:{field:"txzl","header-align":"center",align:"right",title:"提醒总次数","min-width":"140"}}),i("ta-big-table-column",{attrs:{field:"txl","header-align":"center",align:"right",formatter:e.ratioFormat,title:"提醒率","min-width":"80"}}),i("ta-big-table-column",{attrs:{field:"txje","header-align":"center",align:"right",formatter:e.moneyFormat,title:"提醒金额","min-width":"140"}}),i("ta-big-table-column",{attrs:{field:"txsl","header-align":"center",align:"right",title:"提醒数量","min-width":"140"}}),i("template",{slot:"bottomBar"},[i("ta-button",{staticStyle:{float:"right","margin-top":"20px"},attrs:{icon:"download",type:"primary"},on:{click:e.exportTable}},[e._v("导出 ")]),i("ta-pagination",{ref:"gridPager",staticStyle:{float:"right","margin-top":"20px","margin-right":"10px"},attrs:{defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"],"data-source":e.userList,params:e.infoPageParams,url:"reportStatistics/queryReportStatistics"},on:{"update:dataSource":function(t){e.userList=t},"update:data-source":function(t){e.userList=t}}})],1)],2)],1)])],1)},n=[],r=a(66347),l=a(48534),o=a(95082),s=(a(36133),a(88412)),u=a(36797),c=a.n(u),f=a(50848),m=a(83231),p=a(22722),h=a(55115);h.w3.prototype.Base=Object.assign(h.w3.prototype.Base,(0,o.Z)({},p.Z));var d={name:"inpatientRegistrationKs",components:{TaTitle:s.Z},data:function(){return{userList:[],rangeValue:[this.Base.getMoment((new Date).toISOString().slice(0,8)+"-01","YYYY-MM-DD"),this.Base.getMoment((new Date).toISOString().slice(0,10),"YYYY-MM-DD")],paramsDisable:{aaz307:!1},flag:"",ksList:[],jxzb:0,txl:0,bal:""}},mounted:function(){var t=this;return(0,l.Z)(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,m.Z.permissionCheck();case 2:t.permissions=e.sent,t.setPermission(),t.fnQueryDept(""),t.fnQuery();case 6:case"end":return e.stop()}}),e)})))()},methods:{moment:c(),cellClickEvent:function(t){var e=t.row,a=t.column,i=a.property;if("aae386"===i){var n=this.baseInfoForm.getFieldsValue();this.Base.openTabMenu({id:e.aaz307,name:"【"+e.aae386+"】住院备案医师统计",url:"reportStatistics.html#/inpatientRegistrationYs?aaz307=".concat(e.aaz307,"&params=").concat(JSON.stringify(n)),refresh:!1})}},exportTable:function(){var t=this,e=this.baseInfoForm.getFieldsValue();if(void 0===e.allDate||e.allDate.length<2)this.$message.error("请选择时间范围！");else{e=this.infoPageParams();var a,i=[],n=this.$refs.Table.getColumns(),l=(0,r.Z)(n);try{for(l.s();!(a=l.n()).done;){var o=a.value;"序号"!==o.title&&i.push({header:o.title,key:o.property,width:20})}}catch(s){l.e(s)}finally{l.f()}this.Base.submit(null,{url:"reportStatistics/exportExcel",data:e,autoValid:!1},{successCallback:function(e){var a=e.data.data,n={fileName:"住院备案科室统计结果表",sheets:[{name:"worksheet1",column:{complex:!1,columns:i},rows:a,codeList:f.Z.codelist}]};t.Base.generateExcel(n)},failCallback:function(e){t.$message.error("数据加载失败")}})}},formatAmount:function(t){return t.toFixed(2).replace(/\d(?=(\d{3})+\.)/g,"$&,")},moneyFormat:function(t){var e=t.cellValue;return"--"==e?e:this.formatAmount(e)},ratioFormat:function(t){var e=t.cellValue;return 0==e?"0":e+"%"},fnReset:function(){this.baseInfoForm.resetFields(),this.txl=0,this.bal="",this.jxzb=0},setPermission:function(){var t=this;this.paramsDisable.aaz307=this.permissions.aaz307Disable,this.permissions&&this.permissions&&this.permissions.aaz307Set.size>0&&(this.ksList=this.ksList.filter((function(e){return t.permissions.aaz307Set.has(e.value)})),this.permissions.aaz307Disable&&this.baseInfoForm.setFieldsValue({aaz307:this.ksList[0].value}))},fnQueryDept:function(t){var e=this;this.akb020=t||"",this.Base.submit(null,{url:"miimCommonRead/queryDepartRpcList",data:{akb020:this.akb020,hospDeptType:"I"},autoValid:!1},{successCallback:function(t){e.baseInfoForm.resetFields("aaz307"),e.ksList=t.data.resultData},failCallback:function(t){e.$message.error("科室数据加载失败")}})},infoPageParams:function(){var t=this.baseInfoForm.getFieldsValue();return t.startDate=t.allDate[0].format("YYYY-MM-DD HH:mm:ss"),t.endDate=t.allDate[1].format("YYYY-MM-DD HH:mm:ss"),t.bal=this.bal,t.balcom=""===this.bal||void 0===this.bal?"":t.balcom,t.flag="ks",t.aae500="2",t},fnQuery:function(){var t=this,e=this.baseInfoForm.getFieldValue("allDate");!e||e[0]&&e[1]?this.$nextTick((function(){t.$refs.gridPager.loadData((function(t){}))})):this.$message.error("请选择时间范围！")},fnCustomHeaderCell:function(t){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px",fontWeight:"bold"}}},filterOption:function(t,e){return e.componentOptions.children[0].text.indexOf(t)>=0}}},b=d,g=a(1001),v=(0,g.Z)(b,i,n,!1,null,"729a1f2d",null),z=v.exports},36766:function(t,e,a){"use strict";var i=a(66586);e["Z"]=i.Z},26263:function(t,e,a){"use strict";a.d(e,{s:function(){return i},x:function(){return n}});var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},n=[]},66586:function(t,e){"use strict";var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};e["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}},83231:function(t,e,a){"use strict";var i=a(48534);a(36133);function n(t){return t?t.akb020&&t.aaz263?"personal":t.akb020&&t.aaz309&&t.aaz307?"group":t.akb020&&t.aaz307?"department":t.akb020?"hospital":"all":"all"}function r(t){return l.apply(this,arguments)}function l(){return l=(0,i.Z)(regeneratorRuntime.mark((function t(e){var a,i,r,l,o,s,u,c,f,m,p,h;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Base.submit(null,{url:"/miimCommonRead/queryDataPermission",showPageLoading:!1,data:{jobnumber:e},autoValid:!0});case 2:return a=t.sent,i=new Set,r=new Set,a.data.permission.forEach((function(t){var e=n(t);"hospital"===e&&i.add(t.akb020),"department"===e&&r.add(t.aaz307)})),l=a.data.permission.filter((function(t){return"department"===n(t)||!r.has(t.aaz307)})).filter((function(t){return"hospital"===n(t)||!i.has(t.akb020)})),o=new Set(l.map((function(t){return t.akb020})).filter((function(t){return null!=t}))),s=new Set(l.map((function(t){return t.aaz307})).filter((function(t){return null!=t}))),u=new Set(l.map((function(t){return t.aaz309})).filter((function(t){return null!=t}))),c=new Set(l.map((function(t){return t.aaz263})).filter((function(t){return null!=t}))),f=!1,m=!1,p=!1,h=!1,1===o.size&&(f=!0),1===s.size&&1===o.size&&(m=!0),1===s.size&&1===o.size&&1===u.size&&(p=!0),1===o.size&&0===s.size&&1===c.size&&(h=!0),t.abrupt("return",{akb020Set:o,aaz307Set:s,aaz263Set:c,aaz309Set:u,akb020Disable:f,aaz307Disable:m,aaz263Disable:h,aaz309Disable:p});case 20:case"end":return t.stop()}}),t)}))),l.apply(this,arguments)}function o(t,e){Base.submit(null,{url:"miimCommonRead/getAa01AAE500StartStop",data:t},{successCallback:function(t){return e(t)}})}function s(t,e){Base.submit(null,{url:"miimCommonRead/insertTableColumShow",data:t,autoValid:!1},{successCallback:function(t){return e("success")},failCallback:function(t){return e("fail")}})}var u={zbx01:{footer:function(){return"统筹金总额"}},zbx02:{footer:function(){return"基金支出总额"}},zbx03:{footer:function(){return"总费用"}},zbx04:{footer:function(){return"总住院日"}}};e["Z"]={permissionCheck:r,getAa01AAE500StartStop:o,insertTableColumShow:s,props:u,moneyNumFormat:function(t){var e=t.cellValue;return e||"—"}}},50848:function(t,e){"use strict";var a=[{columnKey:"bacszb",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}},{columnKey:"txl",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}}],i=[{columnKey:"bacszb",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}},{columnKey:"txl",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}},{columnKey:"qxxmzb",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}},{columnKey:"wczxmzb",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}}],n=[{columnKey:"jxxmzb",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}},{columnKey:"zfxmzb",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}},{columnKey:"qxxmzb",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}},{columnKey:"wczxmzb",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}}];e["Z"]={codelist:a,codelist2:i,codelist3:n}},55382:function(){},61219:function(){}}]);
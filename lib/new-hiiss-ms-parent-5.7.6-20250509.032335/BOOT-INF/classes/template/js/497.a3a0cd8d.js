"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[497],{88412:function(t,e,a){var s=a(26263),i=a(36766),o=a(1001),l=(0,o.Z)(i.Z,s.s,s.x,!1,null,"5e7ef0ae",null);e["Z"]=l.exports},70497:function(t,e,a){a.r(e),a.d(e,{default:function(){return p}});var s=function(){var t=this,e=this,a=e.$createElement,s=e._self._c||a;return s("div",{staticClass:"fit"},[s("ta-border-layout",{attrs:{layout:{header:"110px",footer:"0px"},showPadding:!0,"footer-cfg":{showBorder:!1}}},[s("div",{attrs:{slot:"header"},slot:"header"},[s("ta-title",{attrs:{title:"查询条件"}}),s("ta-form",{staticStyle:{"padding-top":"5px"},attrs:{autoFormCreate:function(e){t.baseInfoForm=e},layout:"horizontal",formLayout:!0}},[s("ta-form-item",{attrs:{fieldDecoratorId:"allDate",labelCol:{span:7},wrapperCol:{span:16},span:5,fieldDecoratorOptions:{rules:[{required:!0,message:"必须选取时间"}]},required:!0,"init-value":e.rangeValue}},[s("span",{attrs:{slot:"label"},slot:"label"},[e._v("时间范围")]),s("ta-range-picker",{attrs:{"allow-one":!0}},[s("ta-icon",{attrs:{slot:"suffixIcon",type:""},slot:"suffixIcon"})],1)],1),s("ta-form-item",{staticStyle:{"margin-left":"1%"},attrs:{fieldDecoratorId:"aaz307",span:5,labelCol:{span:7},wrapperCol:{span:15}}},[s("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("科室名称")]),s("ta-select",{attrs:{mode:"multiple",maxTagCount:1,placeholder:"科室名称筛选",allowClear:"",options:e.ksList}})],1),s("ta-form-item",{staticStyle:{"margin-left":"1%"},attrs:{fieldDecoratorId:"ake001",span:5,labelCol:{span:7},wrapperCol:{span:15}}},[s("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("项目名称")]),s("ta-select",{attrs:{placeholder:"项目名称筛选",mode:"multiple",maxTagCount:1,allowClear:"",options:e.xmList}})],1),s("ta-form-item",{attrs:{span:3}},[s("ta-button",{staticStyle:{float:"right"},attrs:{icon:"search",type:"primary"},on:{click:e.fnQuery}},[e._v("查询")]),s("ta-button",{staticStyle:{float:"right"},attrs:{type:"default",icon:"redo"},on:{click:e.fnReset}},[e._v("重置 ")])],1)],1)],1),s("div",{staticClass:"fit",staticStyle:{height:"90%"}},[s("div",{staticStyle:{display:"flex","align-items":"center"}},[s("ta-title",{attrs:{title:"查询结果"}}),s("div",{staticStyle:{"margin-left":"20px","margin-bottom":"5px"}},[s("span",[e._v("展示列：")]),s("ta-checkbox-group",{attrs:{value:e.checkboxV},on:{change:e.fnCheckboxChange}},[s("ta-checkbox",{attrs:{value:"showKs"}},[e._v("科室名称")]),s("ta-checkbox",{attrs:{value:"showYs"}},[e._v("项目名称")])],1)],1)],1),s("div",{staticStyle:{float:"right","margin-top":"-25px",color:"#bfbfbf"}},[s("span",[e._v("同比计算不支持跨年日期。")])]),s("ta-big-table",{ref:"Table",attrs:{height:"100%","auto-resize":"",resizable:"","highlight-hover-row":"",border:"",size:"mini",data:e.costList}},[s("ta-big-table-column",{attrs:{type:"seq",align:"center",title:"序号",width:"50"}}),s("ta-big-table-column",{attrs:{field:"aae386",title:"科室名称","min-width":"120",align:"center"}}),s("ta-big-table-column",{attrs:{field:"itemname",title:"项目名称","min-width":"120",align:"center"}}),s("ta-big-table-column",{attrs:{field:"akc226",title:"收费数量","min-width":"120",align:"center"}}),s("ta-big-table-column",{attrs:{field:"akb065",title:"收费金额","min-width":"120",align:"center"}}),s("ta-big-table-column",{attrs:{field:"lastcountratio",title:"收费数量同比","min-width":"200",align:"center",sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return["--"!=a.lastcountratio?s("div",[s("span",[e._v(e._s(a.lastcountratio)+"%")])]):s("div",[s("span",[e._v(e._s(a.lastcountratio))])])]}}])}),s("ta-big-table-column",{attrs:{field:"lastmoneyratio",title:"收费金额同比","min-width":"200",align:"center",sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return["--"!=a.lastmoneyratio?s("div",[s("span",[e._v(e._s(a.lastmoneyratio)+"%")])]):s("div",[s("span",[e._v(e._s(a.lastmoneyratio))])])]}}])}),s("template",{slot:"bottomBar"},[s("ta-button",{staticStyle:{float:"right","margin-top":"20px"},attrs:{icon:"download",type:"primary"},on:{click:e.exportTable}},[e._v("导出 ")]),s("ta-pagination",{ref:"gridPager",staticStyle:{float:"right","margin-top":"20px","margin-right":"10px"},attrs:{defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"],"data-source":e.costList,params:e.infoPageParams,url:"medicalServiceTypeCount/queryCostStatistics"},on:{"update:dataSource":function(t){e.costList=t},"update:data-source":function(t){e.costList=t}}})],1)],2)],1)])],1)},i=[],o=a(66347),l=a(88412),n=a(36797),r=a.n(n),c=(a(55067),[]),u={name:"costStatistics",components:{TaTitle:l.Z},data:function(){return{costList:[],rangeValue:[this.Base.getMoment((new Date).toISOString().slice(0,8)+"-01","YYYY-MM-DD"),this.Base.getMoment((new Date).toISOString().slice(0,10),"YYYY-MM-DD")],userColumns:c,ksList:[],xmList:[],showKs:"0",showYs:"0",checkboxV:["showKs","showYs"],params:{}}},mounted:function(){this.fnQuery()},methods:{moment:r(),IsInArray:function(t,e){var a=","+t.join(",")+",";return-1!=a.indexOf(","+e+",")},fnCheckboxChange:function(t){t.length<1?this.$message.error("必须选择一项"):(this.IsInArray(t,"showKs")?(this.showKs="0",this.$refs.Table.showColumn(this.$refs.Table.getColumnByField("aae386"))):(this.showKs="1",this.$refs.Table.hideColumn(this.$refs.Table.getColumnByField("aae386"))),this.IsInArray(t,"showYs")?(this.showYs="0",this.$refs.Table.showColumn(this.$refs.Table.getColumnByField("itemname"))):(this.showYs="1",this.$refs.Table.hideColumn(this.$refs.Table.getColumnByField("itemname"))),this.checkboxV=t,this.params.showYs=this.showYs,this.params.showKs=this.showKs,this.fnQuery())},fnQueryks:function(){var t=this;this.Base.submit(null,{url:"medicalServiceTypeCount/querySelectksList",data:{}},{successCallback:function(e){t.ksList=e.data.SelectList},failCallback:function(e){t.$message.error("列表加载失败")}})},fnQueryxm:function(){var t=this;this.Base.submit(null,{url:"medicalServiceTypeCount/querySelectInportxmList",data:{}},{successCallback:function(e){t.xmList=e.data.SelectList},failCallback:function(e){t.$message.error("列表加载失败")}})},infoPageParams:function(){var t=this.baseInfoForm.getFieldsValue();if(null!=t.allDate[0]&&""!=t.allDate[0]){if(null!=t.allDate[1]&&""!=t.allDate[1])return t.startDate=t.allDate[0].format("YYYY-MM-DD HH:mm:ss"),t.endDate=t.allDate[1].format("YYYY-MM-DD HH:mm:ss"),this.params=t,this.params.showYs=this.showYs,this.params.showKs=this.showKs,this.params;this.$message.error("请选择完整日期")}else this.$message.error("请选择完整日期")},fnQuery:function(){var t=this;this.$nextTick((function(){t.fnQueryks(),t.fnQueryxm(),t.$refs.gridPager.loadData((function(t){}))}))},fnReset:function(){this.baseInfoForm.resetFields(),this.fnQuery()},exportTable:function(){var t,e=[],a=this.$refs.Table.getColumns(),s=(0,o.Z)(a);try{for(s.s();!(t=s.n()).done;){var i=t.value;"序号"!==i.title&&e.push({header:i.title,key:i.property,width:20})}}catch(n){s.e(n)}finally{s.f()}var l={fileName:"调价项目费用统计结果表",sheets:[{name:"worksheet1",column:{complex:!1,columns:e},rows:this.costList}]};this.Base.generateExcel(l)},fnCustomHeaderCell:function(t){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px",fontWeight:"bold"}}},filterOption:function(t,e){return e.componentOptions.children[0].text.indexOf(t)>=0}}},f=u,h=a(1001),m=(0,h.Z)(f,s,i,!1,null,"6e554989",null),p=m.exports},36766:function(t,e,a){var s=a(66586);e["Z"]=s.Z},26263:function(t,e,a){a.d(e,{s:function(){return s},x:function(){return i}});var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},i=[]},66586:function(t,e){var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};e["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}},55067:function(t,e,a){a(95082),a(95278)}}]);
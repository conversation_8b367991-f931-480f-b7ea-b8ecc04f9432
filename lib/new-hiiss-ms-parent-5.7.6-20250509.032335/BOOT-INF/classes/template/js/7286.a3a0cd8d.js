"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[7286],{3661:function(t,e,a){a.r(e),a.d(e,{default:function(){return x}});var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"layOut"},[a("div",{staticClass:"searchLine"},[a("conditionCompoent",{staticClass:"searchCondition",attrs:{overCondition:t.overCondition},on:{condition:t.getCondition,export:t.getExport}})],1),a("div",{staticClass:"totalLine"},[a("div",{staticClass:"overSituation"},[a("ta-title",{attrs:{title:"总体情况"}},[a("span",{staticStyle:{display:"inline-block",float:"right","margin-right":"10px"}},[a("ta-tooltip",{attrs:{placement:"top"}},[a("template",{slot:"title"},[a("span",{staticStyle:{"font-size":"12px"}},[t._v(" 异常总金额：同一项目违反多个规则时，取最高违规金额作为异常金额；"),a("br"),t._v(" 异常项目数：异常项目计费次数；"),a("br"),t._v(" 异常率：异常项目计费次数/总的项目计费次数；"),a("br")])]),a("ta-icon",{attrs:{type:"question-circle"}})],2)],1)]),a("overSituation",{ref:"overSituation",staticClass:"situationCard",attrs:{conditionData:t.conditionData},on:{spin:t.getSpin}})],1),a("div",{staticClass:"overTrend"},[a("ta-title",{attrs:{title:"整体趋势"}},[a("span",{staticStyle:{display:"inline-block",float:"right","margin-right":"10px"}},[a("ta-tooltip",{attrs:{placement:"top"}},[a("template",{slot:"title"},[a("span",{staticStyle:{"font-size":"12px"}},[t._v("项目数：计费项目次数")])]),a("ta-icon",{attrs:{type:"question-circle"}})],2)],1)]),a("overTrend",{ref:"overTrend",staticClass:"trendCard",attrs:{conditionData:t.conditionData},on:{spin:t.getSpin}})],1)]),a("div",{staticClass:"departRule"},[a("departmentCompoent",{ref:"department",staticClass:"departRuleBox",attrs:{nameList:t.nameList,conditionData:t.conditionData},on:{spin:t.getSpin}}),a("doctorCompoent",{ref:"doctor",staticClass:"departRuleBox",attrs:{nameList:t.nameList,conditionData:t.conditionData},on:{spin:t.getSpin}})],1),a("div",{staticClass:"departRule"},[a("ruleCompoent",{ref:"rule",staticClass:"departRuleBox",attrs:{nameList:t.nameList,conditionData:t.conditionData},on:{spin:t.getSpin}}),a("itemCompoent",{ref:"item",staticClass:"departRuleBox",attrs:{nameList:t.nameList,conditionData:t.conditionData},on:{spin:t.getSpin}})],1)])},s=[],o=a(66347),n=a(95082),r=a(84004),l=a(32717),p=a(8416),c=a(42844),d=a(64511),u=a(32307),m=a(6097),f=(a(36797),a(88412)),h=a(22722),v=a(55115);v.w3.prototype.Base=Object.assign(v.w3.prototype.Base,(0,n.Z)({},h.Z));var C={components:{conditionCompoent:r.Z,overSituation:l.Z,overTrend:p.Z,departmentCompoent:c.Z,ruleCompoent:d.Z,doctorCompoent:u.Z,itemCompoent:m.Z,TaTitle:f.Z},name:"overview",data:function(){return{nameList:[{name:"总览",value:0}],conditionData:{},overCondition:!1,overviewSpin:0}},methods:{getCondition:function(t){this.Base.pageMask({show:!0,text:"加载中"}),this.overviewSpin=0,this.conditionData=t},getSpin:function(t){this.overviewSpin++,Object.keys(this.$refs).length===this.overviewSpin&&this.Base.pageMask({show:!1})},getExport:function(){var t={fileName:"审核总览表",sheets:[]};if(this.$refs.department){var e,a=this.$refs.department.$refs.xTable.getColumns(),i=[],s=(0,o.Z)(a);try{for(s.s();!(e=s.n()).done;){var n=e.value;"operate"!==n.property&&!1!==n.visible&&i.push({header:n.title,key:n.property,width:20})}}catch(T){s.e(T)}finally{s.f()}var r={name:"科室统计",column:{complex:!1,columns:i},rows:this.$refs.department.$refs.xTable.getTableData().tableData};t.sheets.push(r)}if(this.$refs.doctor){var l,p=this.$refs.doctor.$refs.xTable.getColumns(),c=[],d=(0,o.Z)(p);try{for(d.s();!(l=d.n()).done;){var u=l.value;"operate"!==u.property&&!1!==u.visible&&c.push({header:u.title,key:u.property,width:20})}}catch(T){d.e(T)}finally{d.f()}var m={name:"医生统计",column:{complex:!1,columns:c},rows:this.$refs.doctor.$refs.xTable.getTableData().tableData};t.sheets.push(m)}if(this.$refs.rule){var f,h=this.$refs.rule.$refs.xTable.getColumns(),v=[],C=(0,o.Z)(h);try{for(C.s();!(f=C.n()).done;){var y=f.value;"operate"!==y.property&&!1!==y.visible&&v.push({header:y.title,key:y.property,width:20})}}catch(T){C.e(T)}finally{C.f()}var g={name:"规则统计",column:{complex:!1,columns:v},rows:this.$refs.rule.$refs.xTable.getTableData().tableData};t.sheets.push(g)}if(this.$refs.item){var b,x=this.$refs.item.$refs.xTable.getColumns(),D=[],$=(0,o.Z)(x);try{for($.s();!(b=$.n()).done;){var w=b.value;"operate"!==w.property&&!1!==w.visible&&D.push({header:w.title,key:w.property,width:20})}}catch(T){$.e(T)}finally{$.f()}var S={name:"项目统计",column:{complex:!1,columns:D},rows:this.$refs.item.$refs.xTable.getTableData().tableData};t.sheets.push(S)}this.Base.generateExcel(t)}}},y=C,g=a(1001),b=(0,g.Z)(y,i,s,!1,null,"9ada4c34",null),x=b.exports}}]);
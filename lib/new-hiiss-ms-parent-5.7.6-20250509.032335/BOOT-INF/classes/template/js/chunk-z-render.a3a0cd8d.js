(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[5940],{72981:function(t,e,r){"use strict";var i=r(71270),n=r(9567),a=r(43498),o=r(94886),s=r(73048),h=r(41239),l=r(89753),u=r(38689),c=r(64562),f="__zr_normal__",d=i.dN.concat(["ignore"]),p=(0,h.reduce)(i.dN,(function(t,e){return t[e]=!0,t}),{ignore:!1}),v={},g=new a.Z(0,0,0,0),y=function(){function t(t){this.id=(0,h.guid)(),this.animators=[],this.currentStates=[],this.states={},this._init(t)}return t.prototype._init=function(t){this.attr(t)},t.prototype.drift=function(t,e,r){switch(this.draggable){case"horizontal":e=0;break;case"vertical":t=0;break}var i=this.transform;i||(i=this.transform=[1,0,0,1,0,0]),i[4]+=t,i[5]+=e,this.decomposeTransform(),this.markRedraw()},t.prototype.beforeUpdate=function(){},t.prototype.afterUpdate=function(){},t.prototype.update=function(){this.updateTransform(),this.__dirty&&this.updateInnerText()},t.prototype.updateInnerText=function(t){var e=this._textContent;if(e&&(!e.ignore||t)){this.textConfig||(this.textConfig={});var r=this.textConfig,i=r.local,n=e.innerTransformable,a=void 0,o=void 0,h=!1;n.parent=i?this:null;var l=!1;if(n.copyTransform(e),null!=r.position){var u=g;r.layoutRect?u.copy(r.layoutRect):u.copy(this.getBoundingRect()),i||u.applyTransform(this.transform),this.calculateTextPosition?this.calculateTextPosition(v,r,u):(0,s.wI)(v,r,u),n.x=v.x,n.y=v.y,a=v.align,o=v.verticalAlign;var f=r.origin;if(f&&null!=r.rotation){var d=void 0,p=void 0;"center"===f?(d=.5*u.width,p=.5*u.height):(d=(0,s.GM)(f[0],u.width),p=(0,s.GM)(f[1],u.height)),l=!0,n.originX=-n.x+d+(i?0:u.x),n.originY=-n.y+p+(i?0:u.y)}}null!=r.rotation&&(n.rotation=r.rotation);var y=r.offset;y&&(n.x+=y[0],n.y+=y[1],l||(n.originX=-y[0],n.originY=-y[1]));var _=null==r.inside?"string"===typeof r.position&&r.position.indexOf("inside")>=0:r.inside,m=this._innerTextDefaultStyle||(this._innerTextDefaultStyle={}),x=void 0,w=void 0,b=void 0;_&&this.canBeInsideText()?(x=r.insideFill,w=r.insideStroke,null!=x&&"auto"!==x||(x=this.getInsideTextFill()),null!=w&&"auto"!==w||(w=this.getInsideTextStroke(x),b=!0)):(x=r.outsideFill,w=r.outsideStroke,null!=x&&"auto"!==x||(x=this.getOutsideFill()),null!=w&&"auto"!==w||(w=this.getOutsideStroke(x),b=!0)),x=x||"#000",x===m.fill&&w===m.stroke&&b===m.autoStroke&&a===m.align&&o===m.verticalAlign||(h=!0,m.fill=x,m.stroke=w,m.autoStroke=b,m.align=a,m.verticalAlign=o,e.setDefaultTextStyle(m)),e.__dirty|=c.YV,h&&e.dirtyStyle(!0)}},t.prototype.canBeInsideText=function(){return!0},t.prototype.getInsideTextFill=function(){return"#fff"},t.prototype.getInsideTextStroke=function(t){return"#000"},t.prototype.getOutsideFill=function(){return this.__zr&&this.__zr.isDarkMode()?l.GD:l.vU},t.prototype.getOutsideStroke=function(t){var e=this.__zr&&this.__zr.getBackgroundColor(),r="string"===typeof e&&(0,u.parse)(e);r||(r=[255,255,255,1]);for(var i=r[3],n=this.__zr.isDarkMode(),a=0;a<3;a++)r[a]=r[a]*i+(n?0:255)*(1-i);return r[3]=1,(0,u.stringify)(r,"rgba")},t.prototype.traverse=function(t,e){},t.prototype.attrKV=function(t,e){"textConfig"===t?this.setTextConfig(e):"textContent"===t?this.setTextContent(e):"clipPath"===t?this.setClipPath(e):"extra"===t?(this.extra=this.extra||{},(0,h.extend)(this.extra,e)):this[t]=e},t.prototype.hide=function(){this.ignore=!0,this.markRedraw()},t.prototype.show=function(){this.ignore=!1,this.markRedraw()},t.prototype.attr=function(t,e){if("string"===typeof t)this.attrKV(t,e);else if((0,h.isObject)(t))for(var r=t,i=(0,h.keys)(r),n=0;n<i.length;n++){var a=i[n];this.attrKV(a,t[a])}return this.markRedraw(),this},t.prototype.saveCurrentToNormalState=function(t){this._innerSaveToNormal(t);for(var e=this._normalState,r=0;r<this.animators.length;r++){var i=this.animators[r],n=i.__fromStateTransition;if(!(i.getLoop()||n&&n!==f)){var a=i.targetName,o=a?e[a]:e;i.saveTo(o)}}},t.prototype._innerSaveToNormal=function(t){var e=this._normalState;e||(e=this._normalState={}),t.textConfig&&!e.textConfig&&(e.textConfig=this.textConfig),this._savePrimaryToNormal(t,e,d)},t.prototype._savePrimaryToNormal=function(t,e,r){for(var i=0;i<r.length;i++){var n=r[i];null==t[n]||n in e||(e[n]=this[n])}},t.prototype.hasState=function(){return this.currentStates.length>0},t.prototype.getState=function(t){return this.states[t]},t.prototype.ensureState=function(t){var e=this.states;return e[t]||(e[t]={}),e[t]},t.prototype.clearStates=function(t){this.useState(f,!1,t)},t.prototype.useState=function(t,e,r,i){var n=t===f,a=this.hasState();if(a||!n){var o=this.currentStates,s=this.stateTransition;if(!((0,h.indexOf)(o,t)>=0)||!e&&1!==o.length){var l;if(this.stateProxy&&!n&&(l=this.stateProxy(t)),l||(l=this.states&&this.states[t]),l||n){n||this.saveCurrentToNormalState(l);var u=!!(l&&l.hoverLayer||i);u&&this._toggleHoverLayerFlag(!0),this._applyStateObj(t,l,this._normalState,e,!r&&!this.__inHover&&s&&s.duration>0,s);var d=this._textContent,p=this._textGuide;return d&&d.useState(t,e,r,u),p&&p.useState(t,e,r,u),n?(this.currentStates=[],this._normalState={}):e?this.currentStates.push(t):this.currentStates=[t],this._updateAnimationTargets(),this.markRedraw(),!u&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=~c.YV),l}(0,h.logError)("State "+t+" not exists.")}}},t.prototype.useStates=function(t,e,r){if(t.length){var i=[],n=this.currentStates,a=t.length,o=a===n.length;if(o)for(var s=0;s<a;s++)if(t[s]!==n[s]){o=!1;break}if(o)return;for(s=0;s<a;s++){var h=t[s],l=void 0;this.stateProxy&&(l=this.stateProxy(h,t)),l||(l=this.states[h]),l&&i.push(l)}var u=i[a-1],f=!!(u&&u.hoverLayer||r);f&&this._toggleHoverLayerFlag(!0);var d=this._mergeStates(i),p=this.stateTransition;this.saveCurrentToNormalState(d),this._applyStateObj(t.join(","),d,this._normalState,!1,!e&&!this.__inHover&&p&&p.duration>0,p);var v=this._textContent,g=this._textGuide;v&&v.useStates(t,e,f),g&&g.useStates(t,e,f),this._updateAnimationTargets(),this.currentStates=t.slice(),this.markRedraw(),!f&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=~c.YV)}else this.clearStates()},t.prototype._updateAnimationTargets=function(){for(var t=0;t<this.animators.length;t++){var e=this.animators[t];e.targetName&&e.changeTarget(this[e.targetName])}},t.prototype.removeState=function(t){var e=(0,h.indexOf)(this.currentStates,t);if(e>=0){var r=this.currentStates.slice();r.splice(e,1),this.useStates(r)}},t.prototype.replaceState=function(t,e,r){var i=this.currentStates.slice(),n=(0,h.indexOf)(i,t),a=(0,h.indexOf)(i,e)>=0;n>=0?a?i.splice(n,1):i[n]=e:r&&!a&&i.push(e),this.useStates(i)},t.prototype.toggleState=function(t,e){e?this.useState(t,!0):this.removeState(t)},t.prototype._mergeStates=function(t){for(var e,r={},i=0;i<t.length;i++){var n=t[i];(0,h.extend)(r,n),n.textConfig&&(e=e||{},(0,h.extend)(e,n.textConfig))}return e&&(r.textConfig=e),r},t.prototype._applyStateObj=function(t,e,r,i,n,a){var o=!(e&&i);e&&e.textConfig?(this.textConfig=(0,h.extend)({},i?this.textConfig:r.textConfig),(0,h.extend)(this.textConfig,e.textConfig)):o&&r.textConfig&&(this.textConfig=r.textConfig);for(var s={},l=!1,u=0;u<d.length;u++){var c=d[u],f=n&&p[c];e&&null!=e[c]?f?(l=!0,s[c]=e[c]):this[c]=e[c]:o&&null!=r[c]&&(f?(l=!0,s[c]=r[c]):this[c]=r[c])}if(!n)for(u=0;u<this.animators.length;u++){var v=this.animators[u],g=v.targetName;v.getLoop()||v.__changeFinalValue(g?(e||r)[g]:e||r)}l&&this._transitionState(t,s,a)},t.prototype._attachComponent=function(t){if((!t.__zr||t.__hostTarget)&&t!==this){var e=this.__zr;e&&t.addSelfToZr(e),t.__zr=e,t.__hostTarget=this}},t.prototype._detachComponent=function(t){t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__hostTarget=null},t.prototype.getClipPath=function(){return this._clipPath},t.prototype.setClipPath=function(t){this._clipPath&&this._clipPath!==t&&this.removeClipPath(),this._attachComponent(t),this._clipPath=t,this.markRedraw()},t.prototype.removeClipPath=function(){var t=this._clipPath;t&&(this._detachComponent(t),this._clipPath=null,this.markRedraw())},t.prototype.getTextContent=function(){return this._textContent},t.prototype.setTextContent=function(t){var e=this._textContent;e!==t&&(e&&e!==t&&this.removeTextContent(),t.innerTransformable=new i.ZP,this._attachComponent(t),this._textContent=t,this.markRedraw())},t.prototype.setTextConfig=function(t){this.textConfig||(this.textConfig={}),(0,h.extend)(this.textConfig,t),this.markRedraw()},t.prototype.removeTextConfig=function(){this.textConfig=null,this.markRedraw()},t.prototype.removeTextContent=function(){var t=this._textContent;t&&(t.innerTransformable=null,this._detachComponent(t),this._textContent=null,this._innerTextDefaultStyle=null,this.markRedraw())},t.prototype.getTextGuideLine=function(){return this._textGuide},t.prototype.setTextGuideLine=function(t){this._textGuide&&this._textGuide!==t&&this.removeTextGuideLine(),this._attachComponent(t),this._textGuide=t,this.markRedraw()},t.prototype.removeTextGuideLine=function(){var t=this._textGuide;t&&(this._detachComponent(t),this._textGuide=null,this.markRedraw())},t.prototype.markRedraw=function(){this.__dirty|=c.YV;var t=this.__zr;t&&(this.__inHover?t.refreshHover():t.refresh()),this.__hostTarget&&this.__hostTarget.markRedraw()},t.prototype.dirty=function(){this.markRedraw()},t.prototype._toggleHoverLayerFlag=function(t){this.__inHover=t;var e=this._textContent,r=this._textGuide;e&&(e.__inHover=t),r&&(r.__inHover=t)},t.prototype.addSelfToZr=function(t){if(this.__zr!==t){this.__zr=t;var e=this.animators;if(e)for(var r=0;r<e.length;r++)t.animation.addAnimator(e[r]);this._clipPath&&this._clipPath.addSelfToZr(t),this._textContent&&this._textContent.addSelfToZr(t),this._textGuide&&this._textGuide.addSelfToZr(t)}},t.prototype.removeSelfFromZr=function(t){if(this.__zr){this.__zr=null;var e=this.animators;if(e)for(var r=0;r<e.length;r++)t.animation.removeAnimator(e[r]);this._clipPath&&this._clipPath.removeSelfFromZr(t),this._textContent&&this._textContent.removeSelfFromZr(t),this._textGuide&&this._textGuide.removeSelfFromZr(t)}},t.prototype.animate=function(t,e,r){var i=t?this[t]:this;var a=new n.Z(i,e,r);return t&&(a.targetName=t),this.addAnimator(a,t),a},t.prototype.addAnimator=function(t,e){var r=this.__zr,i=this;t.during((function(){i.updateDuringAnimation(e)})).done((function(){var e=i.animators,r=(0,h.indexOf)(e,t);r>=0&&e.splice(r,1)})),this.animators.push(t),r&&r.animation.addAnimator(t),r&&r.wakeUp()},t.prototype.updateDuringAnimation=function(t){this.markRedraw()},t.prototype.stopAnimation=function(t,e){for(var r=this.animators,i=r.length,n=[],a=0;a<i;a++){var o=r[a];t&&t!==o.scope?n.push(o):o.stop(e)}return this.animators=n,this},t.prototype.animateTo=function(t,e,r){_(this,t,e,r)},t.prototype.animateFrom=function(t,e,r){_(this,t,e,r,!0)},t.prototype._transitionState=function(t,e,r,i){for(var n=_(this,e,r,i),a=0;a<n.length;a++)n[a].__fromStateTransition=t},t.prototype.getBoundingRect=function(){return null},t.prototype.getPaintRect=function(){return null},t.initDefaultProps=function(){var e=t.prototype;e.type="element",e.name="",e.ignore=e.silent=e.isGroup=e.draggable=e.dragging=e.ignoreClip=e.__inHover=!1,e.__dirty=c.YV;function r(t,r,i,n){function a(t,e){Object.defineProperty(e,0,{get:function(){return t[i]},set:function(e){t[i]=e}}),Object.defineProperty(e,1,{get:function(){return t[n]},set:function(e){t[n]=e}})}Object.defineProperty(e,t,{get:function(){if(!this[r]){var t=this[r]=[];a(this,t)}return this[r]},set:function(t){this[i]=t[0],this[n]=t[1],this[r]=t,a(this,t)}})}Object.defineProperty&&(r("position","_legacyPos","x","y"),r("scale","_legacyScale","scaleX","scaleY"),r("origin","_legacyOrigin","originX","originY"))}(),t}();function _(t,e,r,i,n){r=r||{};var a=[];T(t,"",t,e,r,i,a,n);var o=a.length,s=!1,h=r.done,l=r.aborted,u=function(){s=!0,o--,o<=0&&(s?h&&h():l&&l())},c=function(){o--,o<=0&&(s?h&&h():l&&l())};o||h&&h(),a.length>0&&r.during&&a[0].during((function(t,e){r.during(e)}));for(var f=0;f<a.length;f++){var d=a[f];u&&d.done(u),c&&d.aborted(c),r.force&&d.duration(r.duration),d.start(r.easing)}return a}function m(t,e,r){for(var i=0;i<r;i++)t[i]=e[i]}function x(t){return(0,h.isArrayLike)(t[0])}function w(t,e,r){if((0,h.isArrayLike)(e[r]))if((0,h.isArrayLike)(t[r])||(t[r]=[]),(0,h.isTypedArray)(e[r])){var i=e[r].length;t[r].length!==i&&(t[r]=new e[r].constructor(i),m(t[r],e[r],i))}else{var n=e[r],a=t[r],o=n.length;if(x(n))for(var s=n[0].length,l=0;l<o;l++)a[l]?m(a[l],n[l],s):a[l]=Array.prototype.slice.call(n[l]);else m(a,n,o);a.length=n.length}else t[r]=e[r]}function b(t,e){return t===e||(0,h.isArrayLike)(t)&&(0,h.isArrayLike)(e)&&k(t,e)}function k(t,e){var r=t.length;if(r!==e.length)return!1;for(var i=0;i<r;i++)if(t[i]!==e[i])return!1;return!0}function T(t,e,r,i,a,o,s,l){for(var u=(0,h.keys)(i),c=a.duration,f=a.delay,d=a.additive,p=a.setToFinal,v=!(0,h.isObject)(o),g=t.animators,y=[],_=0;_<u.length;_++){var m=u[_],x=i[m];if(null!=x&&null!=r[m]&&(v||o[m]))if(!(0,h.isObject)(x)||(0,h.isArrayLike)(x)||(0,h.isGradientObject)(x))y.push(m);else{if(e){l||(r[m]=x,t.updateDuringAnimation(e));continue}T(t,m,r[m],x,a,o&&o[m],s,l)}else l||(r[m]=x,t.updateDuringAnimation(e),y.push(m))}var k=y.length;if(!d&&k)for(var S=0;S<g.length;S++){var C=g[S];if(C.targetName===e){var P=C.stopTracks(y);if(P){var A=(0,h.indexOf)(g,C);g.splice(A,1)}}}if(a.force||(y=(0,h.filter)(y,(function(t){return!b(i[t],r[t])})),k=y.length),k>0||a.force&&!s.length){var M=void 0,L=void 0,D=void 0;if(l){L={},p&&(M={});for(S=0;S<k;S++){m=y[S];L[m]=r[m],p?M[m]=i[m]:r[m]=i[m]}}else if(p){D={};for(S=0;S<k;S++){m=y[S];D[m]=(0,n.V)(r[m]),w(r,i,m)}}C=new n.Z(r,!1,!1,d?(0,h.filter)(g,(function(t){return t.targetName===e})):null);C.targetName=e,a.scope&&(C.scope=a.scope),p&&M&&C.whenWithKeys(0,M,y),D&&C.whenWithKeys(0,D,y),C.whenWithKeys(null==c?500:c,l?L:i,y).delay(f||0),t.addAnimator(C,e),s.push(C)}}(0,h.mixin)(y,o.Z),(0,h.mixin)(y,i.ZP),e["Z"]=/^(3491|4190|5903|6241)$/.test(r.j)?y:null},9567:function(t,e,r){"use strict";r.d(e,{V:function(){return m},Z:function(){return R}});var i={linear:function(t){return t},quadraticIn:function(t){return t*t},quadraticOut:function(t){return t*(2-t)},quadraticInOut:function(t){return(t*=2)<1?.5*t*t:-.5*(--t*(t-2)-1)},cubicIn:function(t){return t*t*t},cubicOut:function(t){return--t*t*t+1},cubicInOut:function(t){return(t*=2)<1?.5*t*t*t:.5*((t-=2)*t*t+2)},quarticIn:function(t){return t*t*t*t},quarticOut:function(t){return 1- --t*t*t*t},quarticInOut:function(t){return(t*=2)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)},quinticIn:function(t){return t*t*t*t*t},quinticOut:function(t){return--t*t*t*t*t+1},quinticInOut:function(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)},sinusoidalIn:function(t){return 1-Math.cos(t*Math.PI/2)},sinusoidalOut:function(t){return Math.sin(t*Math.PI/2)},sinusoidalInOut:function(t){return.5*(1-Math.cos(Math.PI*t))},exponentialIn:function(t){return 0===t?0:Math.pow(1024,t-1)},exponentialOut:function(t){return 1===t?1:1-Math.pow(2,-10*t)},exponentialInOut:function(t){return 0===t?0:1===t?1:(t*=2)<1?.5*Math.pow(1024,t-1):.5*(2-Math.pow(2,-10*(t-1)))},circularIn:function(t){return 1-Math.sqrt(1-t*t)},circularOut:function(t){return Math.sqrt(1- --t*t)},circularInOut:function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)},elasticIn:function(t){var e,r=.1,i=.4;return 0===t?0:1===t?1:(!r||r<1?(r=1,e=i/4):e=i*Math.asin(1/r)/(2*Math.PI),-r*Math.pow(2,10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/i))},elasticOut:function(t){var e,r=.1,i=.4;return 0===t?0:1===t?1:(!r||r<1?(r=1,e=i/4):e=i*Math.asin(1/r)/(2*Math.PI),r*Math.pow(2,-10*t)*Math.sin((t-e)*(2*Math.PI)/i)+1)},elasticInOut:function(t){var e,r=.1,i=.4;return 0===t?0:1===t?1:(!r||r<1?(r=1,e=i/4):e=i*Math.asin(1/r)/(2*Math.PI),(t*=2)<1?r*Math.pow(2,10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/i)*-.5:r*Math.pow(2,-10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/i)*.5+1)},backIn:function(t){var e=1.70158;return t*t*((e+1)*t-e)},backOut:function(t){var e=1.70158;return--t*t*((e+1)*t+e)+1},backInOut:function(t){var e=2.5949095;return(t*=2)<1?t*t*((e+1)*t-e)*.5:.5*((t-=2)*t*((e+1)*t+e)+2)},bounceIn:function(t){return 1-i.bounceOut(1-t)},bounceOut:function(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},bounceInOut:function(t){return t<.5?.5*i.bounceIn(2*t):.5*i.bounceOut(2*t-1)+.5}},n=i,a=r(41239),o=r(79305),s=function(){function t(t){this._inited=!1,this._startTime=0,this._pausedTime=0,this._paused=!1,this._life=t.life||1e3,this._delay=t.delay||0,this.loop=t.loop||!1,this.onframe=t.onframe||a.noop,this.ondestroy=t.ondestroy||a.noop,this.onrestart=t.onrestart||a.noop,t.easing&&this.setEasing(t.easing)}return t.prototype.step=function(t,e){if(this._inited||(this._startTime=t+this._delay,this._inited=!0),!this._paused){var r=this._life,i=t-this._startTime-this._pausedTime,n=i/r;n<0&&(n=0),n=Math.min(n,1);var a=this.easingFunc,o=a?a(n):n;if(this.onframe(o),1===n){if(!this.loop)return!0;var s=i%r;this._startTime=t-s,this._pausedTime=0,this.onrestart()}return!1}this._pausedTime+=e},t.prototype.pause=function(){this._paused=!0},t.prototype.resume=function(){this._paused=!1},t.prototype.setEasing=function(t){this.easing=t,this.easingFunc=(0,a.isFunction)(t)?t:n[t]||(0,o.H)(t)},t}(),h=s,l=r(38689),u=r(80166),c=Array.prototype.slice;function f(t,e,r){return(e-t)*r+t}function d(t,e,r,i){for(var n=e.length,a=0;a<n;a++)t[a]=f(e[a],r[a],i);return t}function p(t,e,r,i){for(var n=e.length,a=n&&e[0].length,o=0;o<n;o++){t[o]||(t[o]=[]);for(var s=0;s<a;s++)t[o][s]=f(e[o][s],r[o][s],i)}return t}function v(t,e,r,i){for(var n=e.length,a=0;a<n;a++)t[a]=e[a]+r[a]*i;return t}function g(t,e,r,i){for(var n=e.length,a=n&&e[0].length,o=0;o<n;o++){t[o]||(t[o]=[]);for(var s=0;s<a;s++)t[o][s]=e[o][s]+r[o][s]*i}return t}function y(t,e){for(var r=t.length,i=e.length,n=r>i?e:t,a=Math.min(r,i),o=n[a-1]||{color:[0,0,0,0],offset:0},s=a;s<Math.max(r,i);s++)n.push({offset:o.offset,color:o.color.slice()})}function _(t,e,r){var i=t,n=e;if(i.push&&n.push){var a=i.length,o=n.length;if(a!==o){var s=a>o;if(s)i.length=o;else for(var h=a;h<o;h++)i.push(1===r?n[h]:c.call(n[h]))}var l=i[0]&&i[0].length;for(h=0;h<i.length;h++)if(1===r)isNaN(i[h])&&(i[h]=n[h]);else for(var u=0;u<l;u++)isNaN(i[h][u])&&(i[h][u]=n[h][u])}}function m(t){if((0,a.isArrayLike)(t)){var e=t.length;if((0,a.isArrayLike)(t[0])){for(var r=[],i=0;i<e;i++)r.push(c.call(t[i]));return r}return c.call(t)}return t}function x(t){return t[0]=Math.floor(t[0])||0,t[1]=Math.floor(t[1])||0,t[2]=Math.floor(t[2])||0,t[3]=null==t[3]?1:t[3],"rgba("+t.join(",")+")"}function w(t){return(0,a.isArrayLike)(t&&t[0])?2:1}var b=0,k=1,T=2,S=3,C=4,P=5,A=6;function M(t){return t===C||t===P}function L(t){return t===k||t===T}var D=[0,0,0,0],z=function(){function t(t){this.keyframes=[],this.discrete=!1,this._invalid=!1,this._needsSort=!1,this._lastFr=0,this._lastFrP=0,this.propName=t}return t.prototype.isFinished=function(){return this._finished},t.prototype.setFinished=function(){this._finished=!0,this._additiveTrack&&this._additiveTrack.setFinished()},t.prototype.needsAnimate=function(){return this.keyframes.length>=1},t.prototype.getAdditiveTrack=function(){return this._additiveTrack},t.prototype.addKeyframe=function(t,e,r){this._needsSort=!0;var i=this.keyframes,s=i.length,h=!1,c=A,f=e;if((0,a.isArrayLike)(e)){var d=w(e);c=d,(1===d&&!(0,a.isNumber)(e[0])||2===d&&!(0,a.isNumber)(e[0][0]))&&(h=!0)}else if((0,a.isNumber)(e)&&!(0,a.eqNaN)(e))c=b;else if((0,a.isString)(e))if(isNaN(+e)){var p=l.parse(e);p&&(f=p,c=S)}else c=b;else if((0,a.isGradientObject)(e)){var v=(0,a.extend)({},f);v.colorStops=(0,a.map)(e.colorStops,(function(t){return{offset:t.offset,color:l.parse(t.color)}})),(0,u.I1)(e)?c=C:(0,u.gO)(e)&&(c=P),f=v}0===s?this.valType=c:c===this.valType&&c!==A||(h=!0),this.discrete=this.discrete||h;var g={time:t,value:f,rawValue:e,percent:0};return r&&(g.easing=r,g.easingFunc=(0,a.isFunction)(r)?r:n[r]||(0,o.H)(r)),i.push(g),g},t.prototype.prepare=function(t,e){var r=this.keyframes;this._needsSort&&r.sort((function(t,e){return t.time-e.time}));for(var i=this.valType,n=r.length,a=r[n-1],o=this.discrete,s=L(i),h=M(i),l=0;l<n;l++){var u=r[l],c=u.value,f=a.value;u.percent=u.time/t,o||(s&&l!==n-1?_(c,f,i):h&&y(c.colorStops,f.colorStops))}if(!o&&i!==P&&e&&this.needsAnimate()&&e.needsAnimate()&&i===e.valType&&!e._finished){this._additiveTrack=e;var d=r[0].value;for(l=0;l<n;l++)i===b?r[l].additiveValue=r[l].value-d:i===S?r[l].additiveValue=v([],r[l].value,d,-1):L(i)&&(r[l].additiveValue=i===k?v([],r[l].value,d,-1):g([],r[l].value,d,-1))}},t.prototype.step=function(t,e){if(!this._finished){this._additiveTrack&&this._additiveTrack._finished&&(this._additiveTrack=null);var r,i,n,o=null!=this._additiveTrack,s=o?"additiveValue":"value",h=this.valType,l=this.keyframes,u=l.length,c=this.propName,v=h===S,g=this._lastFr,y=Math.min;if(1===u)i=n=l[0];else{if(e<0)r=0;else if(e<this._lastFrP){var _=y(g+1,u-1);for(r=_;r>=0;r--)if(l[r].percent<=e)break;r=y(r,u-2)}else{for(r=g;r<u;r++)if(l[r].percent>e)break;r=y(r-1,u-2)}n=l[r+1],i=l[r]}if(i&&n){this._lastFr=r,this._lastFrP=e;var m=n.percent-i.percent,w=0===m?1:y((e-i.percent)/m,1);n.easingFunc&&(w=n.easingFunc(w));var b=o?this._additiveValue:v?D:t[c];if(!L(h)&&!v||b||(b=this._additiveValue=[]),this.discrete)t[c]=w<1?i.rawValue:n.rawValue;else if(L(h))h===k?d(b,i[s],n[s],w):p(b,i[s],n[s],w);else if(M(h)){var T=i[s],P=n[s],A=h===C;t[c]={type:A?"linear":"radial",x:f(T.x,P.x,w),y:f(T.y,P.y,w),colorStops:(0,a.map)(T.colorStops,(function(t,e){var r=P.colorStops[e];return{offset:f(t.offset,r.offset,w),color:x(d([],t.color,r.color,w))}})),global:P.global},A?(t[c].x2=f(T.x2,P.x2,w),t[c].y2=f(T.y2,P.y2,w)):t[c].r=f(T.r,P.r,w)}else if(v)d(b,i[s],n[s],w),o||(t[c]=x(b));else{var z=f(i[s],n[s],w);o?this._additiveValue=z:t[c]=z}o&&this._addToTarget(t)}}},t.prototype._addToTarget=function(t){var e=this.valType,r=this.propName,i=this._additiveValue;e===b?t[r]=t[r]+i:e===S?(l.parse(t[r],D),v(D,D,i,1),t[r]=x(D)):e===k?v(t[r],t[r],i,1):e===T&&g(t[r],t[r],i,1)},t}(),O=function(){function t(t,e,r,i){this._tracks={},this._trackKeys=[],this._maxTime=0,this._started=0,this._clip=null,this._target=t,this._loop=e,e&&i?(0,a.logError)("Can' use additive animation on looped animation."):(this._additiveAnimators=i,this._allowDiscrete=r)}return t.prototype.getMaxTime=function(){return this._maxTime},t.prototype.getDelay=function(){return this._delay},t.prototype.getLoop=function(){return this._loop},t.prototype.getTarget=function(){return this._target},t.prototype.changeTarget=function(t){this._target=t},t.prototype.when=function(t,e,r){return this.whenWithKeys(t,e,(0,a.keys)(e),r)},t.prototype.whenWithKeys=function(t,e,r,i){for(var n=this._tracks,a=0;a<r.length;a++){var o=r[a],s=n[o];if(!s){s=n[o]=new z(o);var h=void 0,l=this._getAdditiveTrack(o);if(l){var u=l.keyframes,c=u[u.length-1];h=c&&c.value,l.valType===S&&h&&(h=x(h))}else h=this._target[o];if(null==h)continue;t>0&&s.addKeyframe(0,m(h),i),this._trackKeys.push(o)}s.addKeyframe(t,m(e[o]),i)}return this._maxTime=Math.max(this._maxTime,t),this},t.prototype.pause=function(){this._clip.pause(),this._paused=!0},t.prototype.resume=function(){this._clip.resume(),this._paused=!1},t.prototype.isPaused=function(){return!!this._paused},t.prototype.duration=function(t){return this._maxTime=t,this._force=!0,this},t.prototype._doneCallback=function(){this._setTracksFinished(),this._clip=null;var t=this._doneCbs;if(t)for(var e=t.length,r=0;r<e;r++)t[r].call(this)},t.prototype._abortedCallback=function(){this._setTracksFinished();var t=this.animation,e=this._abortedCbs;if(t&&t.removeClip(this._clip),this._clip=null,e)for(var r=0;r<e.length;r++)e[r].call(this)},t.prototype._setTracksFinished=function(){for(var t=this._tracks,e=this._trackKeys,r=0;r<e.length;r++)t[e[r]].setFinished()},t.prototype._getAdditiveTrack=function(t){var e,r=this._additiveAnimators;if(r)for(var i=0;i<r.length;i++){var n=r[i].getTrack(t);n&&(e=n)}return e},t.prototype.start=function(t){if(!(this._started>0)){this._started=1;for(var e=this,r=[],i=this._maxTime||0,n=0;n<this._trackKeys.length;n++){var a=this._trackKeys[n],o=this._tracks[a],s=this._getAdditiveTrack(a),l=o.keyframes,u=l.length;if(o.prepare(i,s),o.needsAnimate())if(!this._allowDiscrete&&o.discrete){var c=l[u-1];c&&(e._target[o.propName]=c.rawValue),o.setFinished()}else r.push(o)}if(r.length||this._force){var f=new h({life:i,loop:this._loop,delay:this._delay||0,onframe:function(t){e._started=2;var i=e._additiveAnimators;if(i){for(var n=!1,a=0;a<i.length;a++)if(i[a]._clip){n=!0;break}n||(e._additiveAnimators=null)}for(a=0;a<r.length;a++)r[a].step(e._target,t);var o=e._onframeCbs;if(o)for(a=0;a<o.length;a++)o[a](e._target,t)},ondestroy:function(){e._doneCallback()}});this._clip=f,this.animation&&this.animation.addClip(f),t&&f.setEasing(t)}else this._doneCallback();return this}},t.prototype.stop=function(t){if(this._clip){var e=this._clip;t&&e.onframe(1),this._abortedCallback()}},t.prototype.delay=function(t){return this._delay=t,this},t.prototype.during=function(t){return t&&(this._onframeCbs||(this._onframeCbs=[]),this._onframeCbs.push(t)),this},t.prototype.done=function(t){return t&&(this._doneCbs||(this._doneCbs=[]),this._doneCbs.push(t)),this},t.prototype.aborted=function(t){return t&&(this._abortedCbs||(this._abortedCbs=[]),this._abortedCbs.push(t)),this},t.prototype.getClip=function(){return this._clip},t.prototype.getTrack=function(t){return this._tracks[t]},t.prototype.getTracks=function(){var t=this;return(0,a.map)(this._trackKeys,(function(e){return t._tracks[e]}))},t.prototype.stopTracks=function(t,e){if(!t.length||!this._clip)return!0;for(var r=this._tracks,i=this._trackKeys,n=0;n<t.length;n++){var a=r[t[n]];a&&!a.isFinished()&&(e?a.step(this._target,1):1===this._started&&a.step(this._target,0),a.setFinished())}var o=!0;for(n=0;n<i.length;n++)if(!r[i[n]].isFinished()){o=!1;break}return o&&this._abortedCallback(),o},t.prototype.saveTo=function(t,e,r){if(t){e=e||this._trackKeys;for(var i=0;i<e.length;i++){var n=e[i],a=this._tracks[n];if(a&&!a.isFinished()){var o=a.keyframes,s=o[r?0:o.length-1];s&&(t[n]=m(s.rawValue))}}}},t.prototype.__changeFinalValue=function(t,e){e=e||(0,a.keys)(t);for(var r=0;r<e.length;r++){var i=e[r],n=this._tracks[i];if(n){var o=n.keyframes;if(o.length>1){var s=o.pop();n.addKeyframe(s.time,t[i]),n.prepare(this._maxTime,n.getAdditiveTrack())}}}},t}(),R=O},79305:function(t,e,r){"use strict";if(r.d(e,{H:function(){return o}}),/^(3491|4190|5903|6241)$/.test(r.j))var i=r(54410);if(/^(3491|4190|5903|6241)$/.test(r.j))var n=r(41239);var a=/cubic-bezier\(([0-9,\.e ]+)\)/;function o(t){var e=t&&a.exec(t);if(e){var r=e[1].split(","),o=+(0,n.trim)(r[0]),s=+(0,n.trim)(r[1]),h=+(0,n.trim)(r[2]),l=+(0,n.trim)(r[3]);if(isNaN(o+s+h+l))return;var u=[];return function(t){return t<=0?0:t>=1?1:(0,i.kD)(0,o,h,1,t,u)&&(0,i.af)(0,s,l,1,u[0])}}}},43207:function(t,e,r){"use strict";r(32564);var i,n=r(84754);i=n.Z.hasGlobalWindow&&(window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.msRequestAnimationFrame&&window.msRequestAnimationFrame.bind(window)||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(t){return setTimeout(t,16)},e["Z"]=/^(3491|4190|5903|6241)$/.test(r.j)?i:null},65419:function(t,e,r){"use strict";r.d(e,{Z:function(){return T}});var i=r(89753),n=r(41239),a=r(61431),o=r(94886),s=r(44380),h=r(81133),l=r(43498),u=r(64562),c=r(11693);function f(t,e,r){var i=c.qW.createCanvas(),n=e.getWidth(),a=e.getHeight(),o=i.style;return o&&(o.position="absolute",o.left="0",o.top="0",o.width=n+"px",o.height=a+"px",i.setAttribute("data-zr-dom-id",t)),i.width=n*r,i.height=a*r,i}var d=function(t){function e(e,r,a){var o,s=t.call(this)||this;s.motionBlur=!1,s.lastFrameAlpha=.7,s.dpr=1,s.virtual=!1,s.config={},s.incremental=!1,s.zlevel=0,s.maxRepaintRectCount=5,s.__dirty=!0,s.__firstTimePaint=!0,s.__used=!1,s.__drawIndex=0,s.__startIndex=0,s.__endIndex=0,s.__prevStartIndex=null,s.__prevEndIndex=null,a=a||i.KL,"string"===typeof e?o=f(e,r,a):n.isObject(e)&&(o=e,e=o.id),s.id=e,s.dom=o;var h=o.style;return h&&(n.disableUserSelect(o),o.onselectstart=function(){return!1},h.padding="0",h.margin="0",h.borderWidth="0"),s.painter=r,s.dpr=a,s}return(0,a.ZT)(e,t),e.prototype.getElementCount=function(){return this.__endIndex-this.__startIndex},e.prototype.afterBrush=function(){this.__prevStartIndex=this.__startIndex,this.__prevEndIndex=this.__endIndex},e.prototype.initContext=function(){this.ctx=this.dom.getContext("2d"),this.ctx.dpr=this.dpr},e.prototype.setUnpainted=function(){this.__firstTimePaint=!0},e.prototype.createBackBuffer=function(){var t=this.dpr;this.domBack=f("back-"+this.id,this.painter,t),this.ctxBack=this.domBack.getContext("2d"),1!==t&&this.ctxBack.scale(t,t)},e.prototype.createRepaintRects=function(t,e,r,i){if(this.__firstTimePaint)return this.__firstTimePaint=!1,null;var n,a=[],o=this.maxRepaintRectCount,s=!1,h=new l.Z(0,0,0,0);function c(t){if(t.isFinite()&&!t.isZero())if(0===a.length){var e=new l.Z(0,0,0,0);e.copy(t),a.push(e)}else{for(var r=!1,i=1/0,n=0,u=0;u<a.length;++u){var c=a[u];if(c.intersect(t)){var f=new l.Z(0,0,0,0);f.copy(c),f.union(t),a[u]=f,r=!0;break}if(s){h.copy(t),h.union(c);var d=t.width*t.height,p=c.width*c.height,v=h.width*h.height,g=v-d-p;g<i&&(i=g,n=u)}}if(s&&(a[n].union(t),r=!0),!r){e=new l.Z(0,0,0,0);e.copy(t),a.push(e)}s||(s=a.length>=o)}}for(var f=this.__startIndex;f<this.__endIndex;++f){var d=t[f];if(d){var p=d.shouldBePainted(r,i,!0,!0),v=d.__isRendered&&(d.__dirty&u.YV||!p)?d.getPrevPaintRect():null;v&&c(v);var g=p&&(d.__dirty&u.YV||!d.__isRendered)?d.getPaintRect():null;g&&c(g)}}for(f=this.__prevStartIndex;f<this.__prevEndIndex;++f){d=e[f],p=d.shouldBePainted(r,i,!0,!0);if(d&&(!p||!d.__zr)&&d.__isRendered){v=d.getPrevPaintRect();v&&c(v)}}do{n=!1;for(f=0;f<a.length;)if(a[f].isZero())a.splice(f,1);else{for(var y=f+1;y<a.length;)a[f].intersect(a[y])?(n=!0,a[f].union(a[y]),a.splice(y,1)):y++;f++}}while(n);return this._paintRects=a,a},e.prototype.debugGetPaintRects=function(){return(this._paintRects||[]).slice()},e.prototype.resize=function(t,e){var r=this.dpr,i=this.dom,n=i.style,a=this.domBack;n&&(n.width=t+"px",n.height=e+"px"),i.width=t*r,i.height=e*r,a&&(a.width=t*r,a.height=e*r,1!==r&&this.ctxBack.scale(r,r))},e.prototype.clear=function(t,e,r){var i=this.dom,a=this.ctx,o=i.width,l=i.height;e=e||this.clearColor;var u=this.motionBlur&&!t,c=this.lastFrameAlpha,f=this.dpr,d=this;u&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(i,0,0,o/f,l/f));var p=this.domBack;function v(t,r,i,o){if(a.clearRect(t,r,i,o),e&&"transparent"!==e){var l=void 0;if(n.isGradientObject(e)){var v=e.global||e.__width===i&&e.__height===o;l=v&&e.__canvasGradient||(0,s.ZF)(a,e,{x:0,y:0,width:i,height:o}),e.__canvasGradient=l,e.__width=i,e.__height=o}else n.isImagePatternObject(e)&&(e.scaleX=e.scaleX||f,e.scaleY=e.scaleY||f,l=(0,h.RZ)(a,e,{dirty:function(){d.setUnpainted(),d.__painter.refresh()}}));a.save(),a.fillStyle=l||e,a.fillRect(t,r,i,o),a.restore()}u&&(a.save(),a.globalAlpha=c,a.drawImage(p,t,r,i,o),a.restore())}!r||u?v(0,0,o,l):r.length&&n.each(r,(function(t){v(t.x*f,t.y*f,t.width*f,t.height*f)}))},e}(o.Z),p=d,v=r(43207),g=r(84754),y=1e5,_=314159,m=.01,x=.001;function w(t){return!!t&&(!!t.__builtin__||"function"===typeof t.resize&&"function"===typeof t.refresh)}function b(t,e){var r=document.createElement("div");return r.style.cssText=["position:relative","width:"+t+"px","height:"+e+"px","padding:0","margin:0","border-width:0"].join(";")+";",r}var k=function(){function t(t,e,r,a){this.type="canvas",this._zlevelList=[],this._prevDisplayList=[],this._layers={},this._layerConfig={},this._needsManuallyCompositing=!1,this.type="canvas";var o=!t.nodeName||"CANVAS"===t.nodeName.toUpperCase();this._opts=r=n.extend({},r||{}),this.dpr=r.devicePixelRatio||i.KL,this._singleCanvas=o,this.root=t;var h=t.style;h&&(n.disableUserSelect(t),t.innerHTML=""),this.storage=e;var l=this._zlevelList;this._prevDisplayList=[];var u=this._layers;if(o){var c=t,f=c.width,d=c.height;null!=r.width&&(f=r.width),null!=r.height&&(d=r.height),this.dpr=r.devicePixelRatio||1,c.width=f*this.dpr,c.height=d*this.dpr,this._width=f,this._height=d;var v=new p(c,this,this.dpr);v.__builtin__=!0,v.initContext(),u[_]=v,v.zlevel=_,l.push(_),this._domRoot=t}else{this._width=(0,s.ap)(t,0,r),this._height=(0,s.ap)(t,1,r);var g=this._domRoot=b(this._width,this._height);t.appendChild(g)}}return t.prototype.getType=function(){return"canvas"},t.prototype.isSingleCanvas=function(){return this._singleCanvas},t.prototype.getViewportRoot=function(){return this._domRoot},t.prototype.getViewportRootOffset=function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},t.prototype.refresh=function(t){var e=this.storage.getDisplayList(!0),r=this._prevDisplayList,i=this._zlevelList;this._redrawId=Math.random(),this._paintList(e,r,t,this._redrawId);for(var n=0;n<i.length;n++){var a=i[n],o=this._layers[a];if(!o.__builtin__&&o.refresh){var s=0===n?this._backgroundColor:null;o.refresh(s)}}return this._opts.useDirtyRect&&(this._prevDisplayList=e.slice()),this},t.prototype.refreshHover=function(){this._paintHoverList(this.storage.getDisplayList(!1))},t.prototype._paintHoverList=function(t){var e=t.length,r=this._hoverlayer;if(r&&r.clear(),e){for(var i,n={inHover:!0,viewWidth:this._width,viewHeight:this._height},a=0;a<e;a++){var o=t[a];o.__inHover&&(r||(r=this._hoverlayer=this.getLayer(y)),i||(i=r.ctx,i.save()),(0,h.Dm)(i,o,n,a===e-1))}i&&i.restore()}},t.prototype.getHoverLayer=function(){return this.getLayer(y)},t.prototype.paintOne=function(t,e){(0,h.RV)(t,e)},t.prototype._paintList=function(t,e,r,i){if(this._redrawId===i){r=r||!1,this._updateLayerStatus(t);var n=this._doPaintList(t,e,r),a=n.finished,o=n.needsRefreshHover;if(this._needsManuallyCompositing&&this._compositeManually(),o&&this._paintHoverList(t),a)this.eachLayer((function(t){t.afterBrush&&t.afterBrush()}));else{var s=this;(0,v.Z)((function(){s._paintList(t,e,r,i)}))}}},t.prototype._compositeManually=function(){var t=this.getLayer(_).ctx,e=this._domRoot.width,r=this._domRoot.height;t.clearRect(0,0,e,r),this.eachBuiltinLayer((function(i){i.virtual&&t.drawImage(i.dom,0,0,e,r)}))},t.prototype._doPaintList=function(t,e,r){for(var i=this,a=[],o=this._opts.useDirtyRect,s=0;s<this._zlevelList.length;s++){var h=this._zlevelList[s],l=this._layers[h];l.__builtin__&&l!==this._hoverlayer&&(l.__dirty||r)&&a.push(l)}for(var u=!0,c=!1,f=function(n){var s,h=a[n],l=h.ctx,f=o&&h.createRepaintRects(t,e,d._width,d._height),p=r?h.__startIndex:h.__drawIndex,v=!r&&h.incremental&&Date.now,g=v&&Date.now(),y=h.zlevel===d._zlevelList[0]?d._backgroundColor:null;if(h.__startIndex===h.__endIndex)h.clear(!1,y,f);else if(p===h.__startIndex){var _=t[p];_.incremental&&_.notClear&&!r||h.clear(!1,y,f)}-1===p&&(p=h.__startIndex);var m=function(e){var r={inHover:!1,allClipped:!1,prevEl:null,viewWidth:i._width,viewHeight:i._height};for(s=p;s<h.__endIndex;s++){var n=t[s];if(n.__inHover&&(c=!0),i._doPaintEl(n,h,o,e,r,s===h.__endIndex-1),v){var a=Date.now()-g;if(a>15)break}}r.prevElClipPaths&&l.restore()};if(f)if(0===f.length)s=h.__endIndex;else for(var x=d.dpr,w=0;w<f.length;++w){var b=f[w];l.save(),l.beginPath(),l.rect(b.x*x,b.y*x,b.width*x,b.height*x),l.clip(),m(b),l.restore()}else l.save(),m(),l.restore();h.__drawIndex=s,h.__drawIndex<h.__endIndex&&(u=!1)},d=this,p=0;p<a.length;p++)f(p);return g.Z.wxa&&n.each(this._layers,(function(t){t&&t.ctx&&t.ctx.draw&&t.ctx.draw()})),{finished:u,needsRefreshHover:c}},t.prototype._doPaintEl=function(t,e,r,i,n,a){var o=e.ctx;if(r){var s=t.getPaintRect();(!i||s&&s.intersect(i))&&((0,h.Dm)(o,t,n,a),t.setPrevPaintRect(s))}else(0,h.Dm)(o,t,n,a)},t.prototype.getLayer=function(t,e){this._singleCanvas&&!this._needsManuallyCompositing&&(t=_);var r=this._layers[t];return r||(r=new p("zr_"+t,this,this.dpr),r.zlevel=t,r.__builtin__=!0,this._layerConfig[t]?n.merge(r,this._layerConfig[t],!0):this._layerConfig[t-m]&&n.merge(r,this._layerConfig[t-m],!0),e&&(r.virtual=e),this.insertLayer(t,r),r.initContext()),r},t.prototype.insertLayer=function(t,e){var r=this._layers,i=this._zlevelList,n=i.length,a=this._domRoot,o=null,s=-1;if(!r[t]&&w(e)){if(n>0&&t>i[0]){for(s=0;s<n-1;s++)if(i[s]<t&&i[s+1]>t)break;o=r[i[s]]}if(i.splice(s+1,0,t),r[t]=e,!e.virtual)if(o){var h=o.dom;h.nextSibling?a.insertBefore(e.dom,h.nextSibling):a.appendChild(e.dom)}else a.firstChild?a.insertBefore(e.dom,a.firstChild):a.appendChild(e.dom);e.__painter=this}},t.prototype.eachLayer=function(t,e){for(var r=this._zlevelList,i=0;i<r.length;i++){var n=r[i];t.call(e,this._layers[n],n)}},t.prototype.eachBuiltinLayer=function(t,e){for(var r=this._zlevelList,i=0;i<r.length;i++){var n=r[i],a=this._layers[n];a.__builtin__&&t.call(e,a,n)}},t.prototype.eachOtherLayer=function(t,e){for(var r=this._zlevelList,i=0;i<r.length;i++){var n=r[i],a=this._layers[n];a.__builtin__||t.call(e,a,n)}},t.prototype.getLayers=function(){return this._layers},t.prototype._updateLayerStatus=function(t){function e(t){s&&(s.__endIndex!==t&&(s.__dirty=!0),s.__endIndex=t)}if(this.eachBuiltinLayer((function(t,e){t.__dirty=t.__used=!1})),this._singleCanvas)for(var r=1;r<t.length;r++){var i=t[r];if(i.zlevel!==t[r-1].zlevel||i.incremental){this._needsManuallyCompositing=!0;break}}var a,o,s=null,h=0;for(o=0;o<t.length;o++){i=t[o];var l=i.zlevel,c=void 0;a!==l&&(a=l,h=0),i.incremental?(c=this.getLayer(l+x,this._needsManuallyCompositing),c.incremental=!0,h=1):c=this.getLayer(l+(h>0?m:0),this._needsManuallyCompositing),c.__builtin__||n.logError("ZLevel "+l+" has been used by unkown layer "+c.id),c!==s&&(c.__used=!0,c.__startIndex!==o&&(c.__dirty=!0),c.__startIndex=o,c.incremental?c.__drawIndex=-1:c.__drawIndex=o,e(o),s=c),i.__dirty&u.YV&&!i.__inHover&&(c.__dirty=!0,c.incremental&&c.__drawIndex<0&&(c.__drawIndex=o))}e(o),this.eachBuiltinLayer((function(t,e){!t.__used&&t.getElementCount()>0&&(t.__dirty=!0,t.__startIndex=t.__endIndex=t.__drawIndex=0),t.__dirty&&t.__drawIndex<0&&(t.__drawIndex=t.__startIndex)}))},t.prototype.clear=function(){return this.eachBuiltinLayer(this._clearLayer),this},t.prototype._clearLayer=function(t){t.clear()},t.prototype.setBackgroundColor=function(t){this._backgroundColor=t,n.each(this._layers,(function(t){t.setUnpainted()}))},t.prototype.configLayer=function(t,e){if(e){var r=this._layerConfig;r[t]?n.merge(r[t],e,!0):r[t]=e;for(var i=0;i<this._zlevelList.length;i++){var a=this._zlevelList[i];if(a===t||a===t+m){var o=this._layers[a];n.merge(o,r[t],!0)}}}},t.prototype.delLayer=function(t){var e=this._layers,r=this._zlevelList,i=e[t];i&&(i.dom.parentNode.removeChild(i.dom),delete e[t],r.splice(n.indexOf(r,t),1))},t.prototype.resize=function(t,e){if(this._domRoot.style){var r=this._domRoot;r.style.display="none";var i=this._opts,n=this.root;if(null!=t&&(i.width=t),null!=e&&(i.height=e),t=(0,s.ap)(n,0,i),e=(0,s.ap)(n,1,i),r.style.display="",this._width!==t||e!==this._height){for(var a in r.style.width=t+"px",r.style.height=e+"px",this._layers)this._layers.hasOwnProperty(a)&&this._layers[a].resize(t,e);this.refresh(!0)}this._width=t,this._height=e}else{if(null==t||null==e)return;this._width=t,this._height=e,this.getLayer(_).resize(t,e)}return this},t.prototype.clearLayer=function(t){var e=this._layers[t];e&&e.clear()},t.prototype.dispose=function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},t.prototype.getRenderedCanvas=function(t){if(t=t||{},this._singleCanvas&&!this._compositeManually)return this._layers[_].dom;var e=new p("image",this,t.pixelRatio||this.dpr);e.initContext(),e.clear(!1,t.backgroundColor||this._backgroundColor);var r=e.ctx;if(t.pixelRatio<=this.dpr){this.refresh();var i=e.dom.width,n=e.dom.height;this.eachLayer((function(t){t.__builtin__?r.drawImage(t.dom,0,0,i,n):t.renderToCanvas&&(r.save(),t.renderToCanvas(r),r.restore())}))}else for(var a={inHover:!1,viewWidth:this._width,viewHeight:this._height},o=this.storage.getDisplayList(!0),s=0,l=o.length;s<l;s++){var u=o[s];(0,h.Dm)(r,u,a,s===l-1)}return e.dom},t.prototype.getWidth=function(){return this._width},t.prototype.getHeight=function(){return this._height},t}(),T=k},32689:function(t,e,r){"use strict";if(r.d(e,{a:function(){return a}}),/^(3491|4190|5903|6241)$/.test(r.j))var i=r(41239);function n(t,e){return t&&"solid"!==t&&e>0?"dashed"===t?[4*e,2*e]:"dotted"===t?[e]:(0,i.isNumber)(t)?[t]:(0,i.isArray)(t)?t:null:null}function a(t){var e=t.style,r=e.lineDash&&e.lineWidth>0&&n(e.lineDash,e.lineWidth),a=e.lineDashOffset;if(r){var o=e.strokeNoScale&&t.getLineScale?t.getLineScale():1;o&&1!==o&&(r=(0,i.map)(r,(function(t){return t/o})),a/=o)}return[r,a]}},81133:function(t,e,r){"use strict";if(r.d(e,{Dm:function(){return j},RV:function(){return Z},RZ:function(){return x}}),/^(3491|4190|5903|6241)$/.test(r.j))var i=r(67365);var n=r(72136);if(/^(3491|4190|5903|6241)$/.test(r.j))var a=r(63315);if(/^(3491|4190|5903|6241)$/.test(r.j))var o=r(44380);if(/^(3491|4190|5903|6241)$/.test(r.j))var s=r(54893);if(/^(3491|4190|5903|6241)$/.test(r.j))var h=r(34714);if(/^(3491|4190|5903|6241)$/.test(r.j))var l=r(74969);if(/^(3491|4190|5903|6241)$/.test(r.j))var u=r(41239);if(/^(3491|4190|5903|6241)$/.test(r.j))var c=r(32689);if(/^(3491|4190|5903|6241)$/.test(r.j))var f=r(64562);if(/^(3491|4190|5903|6241)$/.test(r.j))var d=r(11693);var p=new n.Z(!0);function v(t){var e=t.stroke;return!(null==e||"none"===e||!(t.lineWidth>0))}function g(t){return"string"===typeof t&&"none"!==t}function y(t){var e=t.fill;return null!=e&&"none"!==e}function _(t,e){if(null!=e.fillOpacity&&1!==e.fillOpacity){var r=t.globalAlpha;t.globalAlpha=e.fillOpacity*e.opacity,t.fill(),t.globalAlpha=r}else t.fill()}function m(t,e){if(null!=e.strokeOpacity&&1!==e.strokeOpacity){var r=t.globalAlpha;t.globalAlpha=e.strokeOpacity*e.opacity,t.stroke(),t.globalAlpha=r}else t.stroke()}function x(t,e,r){var i=(0,a.Gq)(e.image,e.__image,r);if((0,a.v5)(i)){var n=t.createPattern(i,e.repeat||"repeat");if("function"===typeof DOMMatrix&&n&&n.setTransform){var o=new DOMMatrix;o.translateSelf(e.x||0,e.y||0),o.rotateSelf(0,0,(e.rotation||0)*u.RADIAN_TO_DEGREE),o.scaleSelf(e.scaleX||1,e.scaleY||1),n.setTransform(o)}return n}}function w(t,e,r,i){var n,a=v(r),s=y(r),h=r.strokePercent,l=h<1,u=!e.path;e.silent&&!l||!u||e.createPathProxy();var d=e.path||p,g=e.__dirty;if(!i){var w=r.fill,b=r.stroke,k=s&&!!w.colorStops,T=a&&!!b.colorStops,S=s&&!!w.image,C=a&&!!b.image,P=void 0,A=void 0,M=void 0,L=void 0,D=void 0;(k||T)&&(D=e.getBoundingRect()),k&&(P=g?(0,o.ZF)(t,w,D):e.__canvasFillGradient,e.__canvasFillGradient=P),T&&(A=g?(0,o.ZF)(t,b,D):e.__canvasStrokeGradient,e.__canvasStrokeGradient=A),S&&(M=g||!e.__canvasFillPattern?x(t,w,e):e.__canvasFillPattern,e.__canvasFillPattern=M),C&&(L=g||!e.__canvasStrokePattern?x(t,b,e):e.__canvasStrokePattern,e.__canvasStrokePattern=M),k?t.fillStyle=P:S&&(M?t.fillStyle=M:s=!1),T?t.strokeStyle=A:C&&(L?t.strokeStyle=L:a=!1)}var z,O,R=e.getGlobalScale();d.setScale(R[0],R[1],e.segmentIgnoreThreshold),t.setLineDash&&r.lineDash&&(n=(0,c.a)(e),z=n[0],O=n[1]);var I=!0;(u||g&f.RH)&&(d.setDPR(t.dpr),l?d.setContext(null):(d.setContext(t),I=!1),d.reset(),e.buildPath(d,e.shape,i),d.toStatic(),e.pathUpdated()),I&&d.rebuildPath(t,l?h:1),z&&(t.setLineDash(z),t.lineDashOffset=O),i||(r.strokeFirst?(a&&m(t,r),s&&_(t,r)):(s&&_(t,r),a&&m(t,r))),z&&t.setLineDash([])}function b(t,e,r){var i=e.__image=(0,a.Gq)(r.image,e.__image,e,e.onload);if(i&&(0,a.v5)(i)){var n=r.x||0,o=r.y||0,s=e.getWidth(),h=e.getHeight(),l=i.width/i.height;if(null==s&&null!=h?s=h*l:null==h&&null!=s?h=s/l:null==s&&null==h&&(s=i.width,h=i.height),r.sWidth&&r.sHeight){var u=r.sx||0,c=r.sy||0;t.drawImage(i,u,c,r.sWidth,r.sHeight,n,o,s,h)}else if(r.sx&&r.sy){u=r.sx,c=r.sy;var f=s-u,d=h-c;t.drawImage(i,u,c,f,d,n,o,s,h)}else t.drawImage(i,n,o,s,h)}}function k(t,e,r){var i,n=r.text;if(null!=n&&(n+=""),n){t.font=r.font||d.Uo,t.textAlign=r.textAlign,t.textBaseline=r.textBaseline;var a=void 0,o=void 0;t.setLineDash&&r.lineDash&&(i=(0,c.a)(e),a=i[0],o=i[1]),a&&(t.setLineDash(a),t.lineDashOffset=o),r.strokeFirst?(v(r)&&t.strokeText(n,r.x,r.y),y(r)&&t.fillText(n,r.x,r.y)):(y(r)&&t.fillText(n,r.x,r.y),v(r)&&t.strokeText(n,r.x,r.y)),a&&t.setLineDash([])}}var T=/^(3491|4190|5903|6241)$/.test(r.j)?["shadowBlur","shadowOffsetX","shadowOffsetY"]:null,S=/^(3491|4190|5903|6241)$/.test(r.j)?[["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]]:null;function C(t,e,r,n,a){var o=!1;if(!n&&(r=r||{},e===r))return!1;if(n||e.opacity!==r.opacity){B(t,a),o=!0;var s=Math.max(Math.min(e.opacity,1),0);t.globalAlpha=isNaN(s)?i.tj.opacity:s}(n||e.blend!==r.blend)&&(o||(B(t,a),o=!0),t.globalCompositeOperation=e.blend||i.tj.blend);for(var h=0;h<T.length;h++){var l=T[h];(n||e[l]!==r[l])&&(o||(B(t,a),o=!0),t[l]=t.dpr*(e[l]||0))}return(n||e.shadowColor!==r.shadowColor)&&(o||(B(t,a),o=!0),t.shadowColor=e.shadowColor||i.tj.shadowColor),o}function P(t,e,r,i,n){var a=E(e,n.inHover),o=i?null:r&&E(r,n.inHover)||{};if(a===o)return!1;var s=C(t,a,o,i,n);if((i||a.fill!==o.fill)&&(s||(B(t,n),s=!0),g(a.fill)&&(t.fillStyle=a.fill)),(i||a.stroke!==o.stroke)&&(s||(B(t,n),s=!0),g(a.stroke)&&(t.strokeStyle=a.stroke)),(i||a.opacity!==o.opacity)&&(s||(B(t,n),s=!0),t.globalAlpha=null==a.opacity?1:a.opacity),e.hasStroke()){var h=a.lineWidth,l=h/(a.strokeNoScale&&e.getLineScale?e.getLineScale():1);t.lineWidth!==l&&(s||(B(t,n),s=!0),t.lineWidth=l)}for(var u=0;u<S.length;u++){var c=S[u],f=c[0];(i||a[f]!==o[f])&&(s||(B(t,n),s=!0),t[f]=a[f]||c[1])}return s}function A(t,e,r,i,n){return C(t,E(e,n.inHover),r&&E(r,n.inHover),i,n)}function M(t,e){var r=e.transform,i=t.dpr||1;r?t.setTransform(i*r[0],i*r[1],i*r[2],i*r[3],i*r[4],i*r[5]):t.setTransform(i,0,0,i,0,0)}function L(t,e,r){for(var i=!1,n=0;n<t.length;n++){var a=t[n];i=i||a.isZeroArea(),M(e,a),e.beginPath(),a.buildPath(e,a.shape),e.clip()}r.allClipped=i}function D(t,e){return t&&e?t[0]!==e[0]||t[1]!==e[1]||t[2]!==e[2]||t[3]!==e[3]||t[4]!==e[4]||t[5]!==e[5]:!(!t&&!e)}var z=1,O=2,R=3,I=4;function F(t){var e=y(t),r=v(t);return!(t.lineDash||!(+e^+r)||e&&"string"!==typeof t.fill||r&&"string"!==typeof t.stroke||t.strokePercent<1||t.strokeOpacity<1||t.fillOpacity<1)}function B(t,e){e.batchFill&&t.fill(),e.batchStroke&&t.stroke(),e.batchFill="",e.batchStroke=""}function E(t,e){return e&&t.__hoverStyle||t.style}function Z(t,e){j(t,e,{inHover:!1,viewWidth:0,viewHeight:0},!0)}function j(t,e,r,i){var n=e.transform;if(!e.shouldBePainted(r.viewWidth,r.viewHeight,!1,!1))return e.__dirty&=~f.YV,void(e.__isRendered=!1);var a=e.__clipPaths,u=r.prevElClipPaths,c=!1,d=!1;if(u&&!(0,o.cF)(a,u)||(u&&u.length&&(B(t,r),t.restore(),d=c=!0,r.prevElClipPaths=null,r.allClipped=!1,r.prevEl=null),a&&a.length&&(B(t,r),t.save(),L(a,t,r),c=!0),r.prevElClipPaths=a),r.allClipped)e.__isRendered=!1;else{e.beforeBrush&&e.beforeBrush(),e.innerBeforeBrush();var p=r.prevEl;p||(d=c=!0);var v=e instanceof s.ZP&&e.autoBatch&&F(e.style);c||D(n,p.transform)?(B(t,r),M(t,e)):v||B(t,r);var g=E(e,r.inHover);e instanceof s.ZP?(r.lastDrawType!==z&&(d=!0,r.lastDrawType=z),P(t,e,p,d,r),v&&(r.batchFill||r.batchStroke)||t.beginPath(),w(t,e,g,v),v&&(r.batchFill=g.fill||"",r.batchStroke=g.stroke||"")):e instanceof l.Z?(r.lastDrawType!==R&&(d=!0,r.lastDrawType=R),P(t,e,p,d,r),k(t,e,g)):e instanceof h.ZP?(r.lastDrawType!==O&&(d=!0,r.lastDrawType=O),A(t,e,p,d,r),b(t,e,g)):e.getTemporalDisplayables&&(r.lastDrawType!==I&&(d=!0,r.lastDrawType=I),H(t,e,r)),v&&i&&B(t,r),e.innerAfterBrush(),e.afterBrush&&e.afterBrush(),r.prevEl=e,e.__dirty=0,e.__isRendered=!0}}function H(t,e,r){var i=e.getDisplayables(),n=e.getTemporalDisplayables();t.save();var a,o,s={prevElClipPaths:null,prevEl:null,allClipped:!1,viewWidth:r.viewWidth,viewHeight:r.viewHeight,inHover:r.inHover};for(a=e.getCursor(),o=i.length;a<o;a++){var h=i[a];h.beforeBrush&&h.beforeBrush(),h.innerBeforeBrush(),j(t,h,s,a===o-1),h.innerAfterBrush(),h.afterBrush&&h.afterBrush(),s.prevEl=h}for(var l=0,u=n.length;l<u;l++){h=n[l];h.beforeBrush&&h.beforeBrush(),h.innerBeforeBrush(),j(t,h,s,l===u-1),h.innerAfterBrush(),h.afterBrush&&h.afterBrush(),s.prevEl=h}e.clearTemporalDisplayables(),e.notClear=!0,t.restore()}},44380:function(t,e,r){"use strict";function i(t){return isFinite(t)}function n(t,e,r){var n=null==e.x?0:e.x,a=null==e.x2?1:e.x2,o=null==e.y?0:e.y,s=null==e.y2?0:e.y2;e.global||(n=n*r.width+r.x,a=a*r.width+r.x,o=o*r.height+r.y,s=s*r.height+r.y),n=i(n)?n:0,a=i(a)?a:1,o=i(o)?o:0,s=i(s)?s:0;var h=t.createLinearGradient(n,o,a,s);return h}function a(t,e,r){var n=r.width,a=r.height,o=Math.min(n,a),s=null==e.x?.5:e.x,h=null==e.y?.5:e.y,l=null==e.r?.5:e.r;e.global||(s=s*n+r.x,h=h*a+r.y,l*=o),s=i(s)?s:.5,h=i(h)?h:.5,l=l>=0&&i(l)?l:.5;var u=t.createRadialGradient(s,h,0,s,h,l);return u}function o(t,e,r){for(var i="radial"===e.type?a(t,e,r):n(t,e,r),o=e.colorStops,s=0;s<o.length;s++)i.addColorStop(o[s].offset,o[s].color);return i}function s(t,e){if(t===e||!t&&!e)return!1;if(!t||!e||t.length!==e.length)return!0;for(var r=0;r<t.length;r++)if(t[r]!==e[r])return!0;return!1}function h(t){return parseInt(t,10)}function l(t,e,r){var i=["width","height"][e],n=["clientWidth","clientHeight"][e],a=["paddingLeft","paddingTop"][e],o=["paddingRight","paddingBottom"][e];if(null!=r[i]&&"auto"!==r[i])return parseFloat(r[i]);var s=document.defaultView.getComputedStyle(t);return(t[n]||h(s[i])||h(t.style[i]))-(h(s[a])||0)-(h(s[o])||0)|0}r.d(e,{ZF:function(){return o},ap:function(){return l},cF:function(){return s}})},89753:function(t,e,r){"use strict";r.d(e,{Ak:function(){return o},GD:function(){return h},KL:function(){return a},iv:function(){return l},vU:function(){return s}});var i=r(84754),n=1;i.Z.hasGlobalWindow&&(n=Math.max(window.devicePixelRatio||window.screen&&window.screen.deviceXDPI/window.screen.logicalXDPI||1,1));var a=/^(3491|4190|5903|6241)$/.test(r.j)?n:null,o=.4,s="#333",h="#ccc",l="#eee"},76213:function(t,e,r){"use strict";if(r.d(e,{m:function(){return a}}),/^(3491|4190|5903|6241)$/.test(r.j))var i=r(40356);var n=2*Math.PI;function a(t,e,r,a,o,s,h,l,u){if(0===h)return!1;var c=h;l-=t,u-=e;var f=Math.sqrt(l*l+u*u);if(f-c>r||f+c<r)return!1;if(Math.abs(a-o)%n<1e-4)return!0;if(s){var d=a;a=(0,i.m)(o),o=(0,i.m)(d)}else a=(0,i.m)(a),o=(0,i.m)(o);a>o&&(o+=n);var p=Math.atan2(u,l);return p<0&&(p+=n),p>=a&&p<=o||p+n>=a&&p+n<=o}},53996:function(t,e,r){"use strict";if(r.d(e,{m:function(){return n}}),/^(3491|4190|5903|6241)$/.test(r.j))var i=r(54410);function n(t,e,r,n,a,o,s,h,l,u,c){if(0===l)return!1;var f=l;if(c>e+f&&c>n+f&&c>o+f&&c>h+f||c<e-f&&c<n-f&&c<o-f&&c<h-f||u>t+f&&u>r+f&&u>a+f&&u>s+f||u<t-f&&u<r-f&&u<a-f&&u<s-f)return!1;var d=i.t1(t,e,r,n,a,o,s,h,u,c,null);return d<=f/2}},64379:function(t,e,r){"use strict";function i(t,e,r,i,n,a,o){if(0===n)return!1;var s=n,h=0,l=t;if(o>e+s&&o>i+s||o<e-s&&o<i-s||a>t+s&&a>r+s||a<t-s&&a<r-s)return!1;if(t===r)return Math.abs(a-t)<=s/2;h=(e-i)/(t-r),l=(t*i-r*e)/(t-r);var u=h*a-o+l,c=u*u/(h*h+1);return c<=s/2*s/2}r.d(e,{m:function(){return i}})},74943:function(t,e,r){"use strict";if(r.d(e,{X:function(){return o}}),/^(3491|4190|5903|6241)$/.test(r.j))var i=r(71824);var n=1e-8;function a(t,e){return Math.abs(t-e)<n}function o(t,e,r){var n=0,o=t[0];if(!o)return!1;for(var s=1;s<t.length;s++){var h=t[s];n+=(0,i.Z)(o[0],o[1],h[0],h[1],e,r),o=h}var l=t[0];return a(o[0],l[0])&&a(o[1],l[1])||(n+=(0,i.Z)(o[0],o[1],l[0],l[1],e,r)),0!==n}},85553:function(t,e,r){"use strict";if(r.d(e,{m:function(){return n}}),/^(3491|4190|5903|6241)$/.test(r.j))var i=r(54410);function n(t,e,r,n,a,o,s,h,l){if(0===s)return!1;var u=s;if(l>e+u&&l>n+u&&l>o+u||l<e-u&&l<n-u&&l<o-u||h>t+u&&h>r+u&&h>a+u||h<t-u&&h<r-u&&h<a-u)return!1;var c=(0,i.Wr)(t,e,r,n,a,o,h,l,null);return c<=u/2}},73048:function(t,e,r){"use strict";if(r.d(e,{Dp:function(){return f},GM:function(){return d},M3:function(){return u},dz:function(){return s},lP:function(){return l},mU:function(){return c},wI:function(){return p}}),/^(3491|4190|5903|6241)$/.test(r.j))var i=r(43498);if(/^(3491|4190|5903|6241)$/.test(r.j))var n=r(88339);if(/^(3491|4190|5903|6241)$/.test(r.j))var a=r(11693);var o={};function s(t,e){e=e||a.Uo;var r=o[e];r||(r=o[e]=new n.ZP(500));var i=r.get(t);return null==i&&(i=a.qW.measureText(t,e).width,r.put(t,i)),i}function h(t,e,r,n){var a=s(t,e),o=f(e),h=u(0,a,r),l=c(0,o,n),d=new i.Z(h,l,a,o);return d}function l(t,e,r,n){var a=((t||"")+"").split("\n"),o=a.length;if(1===o)return h(a[0],e,r,n);for(var s=new i.Z(0,0,0,0),l=0;l<a.length;l++){var u=h(a[l],e,r,n);0===l?s.copy(u):s.union(u)}return s}function u(t,e,r){return"right"===r?t-=e:"center"===r&&(t-=e/2),t}function c(t,e,r){return"middle"===r?t-=e/2:"bottom"===r&&(t-=e),t}function f(t){return s("国",t)}function d(t,e){return"string"===typeof t?t.lastIndexOf("%")>=0?parseFloat(t)/100*e:parseFloat(t):t}function p(t,e,r){var i=e.position||"inside",n=null!=e.distance?e.distance:5,a=r.height,o=r.width,s=a/2,h=r.x,l=r.y,u="left",c="top";if(i instanceof Array)h+=d(i[0],r.width),l+=d(i[1],r.height),u=null,c=null;else switch(i){case"left":h-=n,l+=s,u="right",c="middle";break;case"right":h+=n+o,l+=s,c="middle";break;case"top":h+=o/2,l-=n,u="center",c="bottom";break;case"bottom":h+=o/2,l+=a+n,u="center";break;case"inside":h+=o/2,l+=s,u="center",c="middle";break;case"insideLeft":h+=n,l+=s,c="middle";break;case"insideRight":h+=o-n,l+=s,u="right",c="middle";break;case"insideTop":h+=o/2,l+=n,u="center";break;case"insideBottom":h+=o/2,l+=a-n,u="center",c="bottom";break;case"insideTopLeft":h+=n,l+=n;break;case"insideTopRight":h+=o-n,l+=n,u="right";break;case"insideBottomLeft":h+=n,l+=a-n,c="bottom";break;case"insideBottomRight":h+=o-n,l+=a-n,u="right",c="bottom";break}return t=t||{},t.x=h,t.y=l,t.align=u,t.verticalAlign=c,t}},40356:function(t,e,r){"use strict";r.d(e,{m:function(){return n}});var i=2*Math.PI;function n(t){return t%=i,t<0&&(t+=i),t}},71824:function(t,e,r){"use strict";function i(t,e,r,i,n,a){if(a>e&&a>i||a<e&&a<i)return 0;if(i===e)return 0;var o=(a-e)/(i-e),s=i<e?1:-1;1!==o&&0!==o||(s=i<e?.5:-.5);var h=o*(r-t)+t;return h===n?1/0:h>n?s:0}r.d(e,{Z:function(){return i}})},43498:function(t,e,r){"use strict";var i=r(6366),n=r(41284),a=Math.min,o=Math.max,s=new n.Z,h=new n.Z,l=new n.Z,u=new n.Z,c=new n.Z,f=new n.Z,d=function(){function t(t,e,r,i){r<0&&(t+=r,r=-r),i<0&&(e+=i,i=-i),this.x=t,this.y=e,this.width=r,this.height=i}return t.prototype.union=function(t){var e=a(t.x,this.x),r=a(t.y,this.y);isFinite(this.x)&&isFinite(this.width)?this.width=o(t.x+t.width,this.x+this.width)-e:this.width=t.width,isFinite(this.y)&&isFinite(this.height)?this.height=o(t.y+t.height,this.y+this.height)-r:this.height=t.height,this.x=e,this.y=r},t.prototype.applyTransform=function(e){t.applyTransform(this,this,e)},t.prototype.calculateTransform=function(t){var e=this,r=t.width/e.width,n=t.height/e.height,a=i.create();return i.translate(a,a,[-e.x,-e.y]),i.scale(a,a,[r,n]),i.translate(a,a,[t.x,t.y]),a},t.prototype.intersect=function(e,r){if(!e)return!1;e instanceof t||(e=t.create(e));var i=this,a=i.x,o=i.x+i.width,s=i.y,h=i.y+i.height,l=e.x,u=e.x+e.width,d=e.y,p=e.y+e.height,v=!(o<l||u<a||h<d||p<s);if(r){var g=1/0,y=0,_=Math.abs(o-l),m=Math.abs(u-a),x=Math.abs(h-d),w=Math.abs(p-s),b=Math.min(_,m),k=Math.min(x,w);o<l||u<a?b>y&&(y=b,_<m?n.Z.set(f,-_,0):n.Z.set(f,m,0)):b<g&&(g=b,_<m?n.Z.set(c,_,0):n.Z.set(c,-m,0)),h<d||p<s?k>y&&(y=k,x<w?n.Z.set(f,0,-x):n.Z.set(f,0,w)):b<g&&(g=b,x<w?n.Z.set(c,0,x):n.Z.set(c,0,-w))}return r&&n.Z.copy(r,v?c:f),v},t.prototype.contain=function(t,e){var r=this;return t>=r.x&&t<=r.x+r.width&&e>=r.y&&e<=r.y+r.height},t.prototype.clone=function(){return new t(this.x,this.y,this.width,this.height)},t.prototype.copy=function(e){t.copy(this,e)},t.prototype.plain=function(){return{x:this.x,y:this.y,width:this.width,height:this.height}},t.prototype.isFinite=function(){return isFinite(this.x)&&isFinite(this.y)&&isFinite(this.width)&&isFinite(this.height)},t.prototype.isZero=function(){return 0===this.width||0===this.height},t.create=function(e){return new t(e.x,e.y,e.width,e.height)},t.copy=function(t,e){t.x=e.x,t.y=e.y,t.width=e.width,t.height=e.height},t.applyTransform=function(e,r,i){if(i){if(i[1]<1e-5&&i[1]>-1e-5&&i[2]<1e-5&&i[2]>-1e-5){var n=i[0],c=i[3],f=i[4],d=i[5];return e.x=r.x*n+f,e.y=r.y*c+d,e.width=r.width*n,e.height=r.height*c,e.width<0&&(e.x+=e.width,e.width=-e.width),void(e.height<0&&(e.y+=e.height,e.height=-e.height))}s.x=l.x=r.x,s.y=u.y=r.y,h.x=u.x=r.x+r.width,h.y=l.y=r.y+r.height,s.transform(i),u.transform(i),h.transform(i),l.transform(i),e.x=a(s.x,h.x,l.x,u.x),e.y=a(s.y,h.y,l.y,u.y);var p=o(s.x,h.x,l.x,u.x),v=o(s.y,h.y,l.y,u.y);e.width=p-e.x,e.height=v-e.y}else e!==r&&t.copy(e,r)},t}();e["Z"]=/^(3491|4190|5903|6241)$/.test(r.j)?d:null},94886:function(t,e,r){"use strict";var i=function(){function t(t){t&&(this._$eventProcessor=t)}return t.prototype.on=function(t,e,r,i){this._$handlers||(this._$handlers={});var n=this._$handlers;if("function"===typeof e&&(i=r,r=e,e=null),!r||!t)return this;var a=this._$eventProcessor;null!=e&&a&&a.normalizeQuery&&(e=a.normalizeQuery(e)),n[t]||(n[t]=[]);for(var o=0;o<n[t].length;o++)if(n[t][o].h===r)return this;var s={h:r,query:e,ctx:i||this,callAtLast:r.zrEventfulCallAtLast},h=n[t].length-1,l=n[t][h];return l&&l.callAtLast?n[t].splice(h,0,s):n[t].push(s),this},t.prototype.isSilent=function(t){var e=this._$handlers;return!e||!e[t]||!e[t].length},t.prototype.off=function(t,e){var r=this._$handlers;if(!r)return this;if(!t)return this._$handlers={},this;if(e){if(r[t]){for(var i=[],n=0,a=r[t].length;n<a;n++)r[t][n].h!==e&&i.push(r[t][n]);r[t]=i}r[t]&&0===r[t].length&&delete r[t]}else delete r[t];return this},t.prototype.trigger=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];if(!this._$handlers)return this;var i=this._$handlers[t],n=this._$eventProcessor;if(i)for(var a=e.length,o=i.length,s=0;s<o;s++){var h=i[s];if(!n||!n.filter||null==h.query||n.filter(t,h.query))switch(a){case 0:h.h.call(h.ctx);break;case 1:h.h.call(h.ctx,e[0]);break;case 2:h.h.call(h.ctx,e[0],e[1]);break;default:h.h.apply(h.ctx,e);break}}return n&&n.afterTrigger&&n.afterTrigger(t),this},t.prototype.triggerWithContext=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];if(!this._$handlers)return this;var i=this._$handlers[t],n=this._$eventProcessor;if(i)for(var a=e.length,o=e[a-1],s=i.length,h=0;h<s;h++){var l=i[h];if(!n||!n.filter||null==l.query||n.filter(t,l.query))switch(a){case 0:l.h.call(o);break;case 1:l.h.call(o,e[0]);break;case 2:l.h.call(o,e[0],e[1]);break;default:l.h.apply(o,e.slice(1,a-1));break}}return n&&n.afterTrigger&&n.afterTrigger(t),this},t}();e["Z"]=/^(3491|4190|5903|6241)$/.test(r.j)?i:null},88339:function(t,e,r){"use strict";var i=function(){function t(t){this.value=t}return t}(),n=function(){function t(){this._len=0}return t.prototype.insert=function(t){var e=new i(t);return this.insertEntry(e),e},t.prototype.insertEntry=function(t){this.head?(this.tail.next=t,t.prev=this.tail,t.next=null,this.tail=t):this.head=this.tail=t,this._len++},t.prototype.remove=function(t){var e=t.prev,r=t.next;e?e.next=r:this.head=r,r?r.prev=e:this.tail=e,t.next=t.prev=null,this._len--},t.prototype.len=function(){return this._len},t.prototype.clear=function(){this.head=this.tail=null,this._len=0},t}(),a=function(){function t(t){this._list=new n,this._maxSize=10,this._map={},this._maxSize=t}return t.prototype.put=function(t,e){var r=this._list,n=this._map,a=null;if(null==n[t]){var o=r.len(),s=this._lastRemovedEntry;if(o>=this._maxSize&&o>0){var h=r.head;r.remove(h),delete n[h.key],a=h.value,this._lastRemovedEntry=h}s?s.value=e:s=new i(e),s.key=t,r.insertEntry(s),n[t]=s}return a},t.prototype.get=function(t){var e=this._map[t],r=this._list;if(null!=e)return e!==r.tail&&(r.remove(e),r.insertEntry(e)),e.value},t.prototype.clear=function(){this._list.clear(),this._map={}},t.prototype.len=function(){return this._list.len()},t}();e["ZP"]=/^(3491|4190|5903|6241)$/.test(r.j)?a:null},85140:function(t,e,r){"use strict";var i=r(41284),n=[0,0],a=[0,0],o=new i.Z,s=new i.Z,h=function(){function t(t,e){this._corners=[],this._axes=[],this._origin=[0,0];for(var r=0;r<4;r++)this._corners[r]=new i.Z;for(r=0;r<2;r++)this._axes[r]=new i.Z;t&&this.fromBoundingRect(t,e)}return t.prototype.fromBoundingRect=function(t,e){var r=this._corners,n=this._axes,a=t.x,o=t.y,s=a+t.width,h=o+t.height;if(r[0].set(a,o),r[1].set(s,o),r[2].set(s,h),r[3].set(a,h),e)for(var l=0;l<4;l++)r[l].transform(e);i.Z.sub(n[0],r[1],r[0]),i.Z.sub(n[1],r[3],r[0]),n[0].normalize(),n[1].normalize();for(l=0;l<2;l++)this._origin[l]=n[l].dot(r[0])},t.prototype.intersect=function(t,e){var r=!0,n=!e;return o.set(1/0,1/0),s.set(0,0),!this._intersectCheckOneSide(this,t,o,s,n,1)&&(r=!1,n)||!this._intersectCheckOneSide(t,this,o,s,n,-1)&&(r=!1,n)||n||i.Z.copy(e,r?o:s),r},t.prototype._intersectCheckOneSide=function(t,e,r,o,s,h){for(var l=!0,u=0;u<2;u++){var c=this._axes[u];if(this._getProjMinMaxOnAxis(u,t._corners,n),this._getProjMinMaxOnAxis(u,e._corners,a),n[1]<a[0]||n[0]>a[1]){if(l=!1,s)return l;var f=Math.abs(a[0]-n[1]),d=Math.abs(n[0]-a[1]);Math.min(f,d)>o.len()&&(f<d?i.Z.scale(o,c,-f*h):i.Z.scale(o,c,d*h))}else if(r){f=Math.abs(a[0]-n[1]),d=Math.abs(n[0]-a[1]);Math.min(f,d)<r.len()&&(f<d?i.Z.scale(r,c,f*h):i.Z.scale(r,c,-d*h))}}return l},t.prototype._getProjMinMaxOnAxis=function(t,e,r){for(var i=this._axes[t],n=this._origin,a=e[0].dot(i)+n[t],o=a,s=a,h=1;h<e.length;h++){var l=e[h].dot(i)+n[t];o=Math.min(l,o),s=Math.max(l,s)}r[0]=o,r[1]=s},t}();e["Z"]=/^(3491|4190|5903|6241)$/.test(r.j)?h:null},72136:function(t,e,r){"use strict";r.d(e,{L:function(){return S}});r(39575),r(38012);var i=r(77324),n=r(43498),a=r(89753),o=r(85036),s=r(54410),h={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},l=[],u=[],c=[],f=[],d=[],p=[],v=Math.min,g=Math.max,y=Math.cos,_=Math.sin,m=Math.abs,x=Math.PI,w=2*x,b="undefined"!==typeof Float32Array,k=[];function T(t){var e=Math.round(t/x*1e8)/1e8;return e%2*x}function S(t,e){var r=T(t[0]);r<0&&(r+=w);var i=r-t[0],n=t[1];n+=i,!e&&n-r>=w?n=r+w:e&&r-n>=w?n=r-w:!e&&r>n?n=r+(w-T(r-n)):e&&r<n&&(n=r-(w-T(n-r))),t[0]=r,t[1]=n}var C=function(){function t(t){this.dpr=1,this._xi=0,this._yi=0,this._x0=0,this._y0=0,this._len=0,t&&(this._saveData=!1),this._saveData&&(this.data=[])}return t.prototype.increaseVersion=function(){this._version++},t.prototype.getVersion=function(){return this._version},t.prototype.setScale=function(t,e,r){r=r||0,r>0&&(this._ux=m(r/a.KL/t)||0,this._uy=m(r/a.KL/e)||0)},t.prototype.setDPR=function(t){this.dpr=t},t.prototype.setContext=function(t){this._ctx=t},t.prototype.getContext=function(){return this._ctx},t.prototype.beginPath=function(){return this._ctx&&this._ctx.beginPath(),this.reset(),this},t.prototype.reset=function(){this._saveData&&(this._len=0),this._pathSegLen&&(this._pathSegLen=null,this._pathLen=0),this._version++},t.prototype.moveTo=function(t,e){return this._drawPendingPt(),this.addData(h.M,t,e),this._ctx&&this._ctx.moveTo(t,e),this._x0=t,this._y0=e,this._xi=t,this._yi=e,this},t.prototype.lineTo=function(t,e){var r=m(t-this._xi),i=m(e-this._yi),n=r>this._ux||i>this._uy;if(this.addData(h.L,t,e),this._ctx&&n&&this._ctx.lineTo(t,e),n)this._xi=t,this._yi=e,this._pendingPtDist=0;else{var a=r*r+i*i;a>this._pendingPtDist&&(this._pendingPtX=t,this._pendingPtY=e,this._pendingPtDist=a)}return this},t.prototype.bezierCurveTo=function(t,e,r,i,n,a){return this._drawPendingPt(),this.addData(h.C,t,e,r,i,n,a),this._ctx&&this._ctx.bezierCurveTo(t,e,r,i,n,a),this._xi=n,this._yi=a,this},t.prototype.quadraticCurveTo=function(t,e,r,i){return this._drawPendingPt(),this.addData(h.Q,t,e,r,i),this._ctx&&this._ctx.quadraticCurveTo(t,e,r,i),this._xi=r,this._yi=i,this},t.prototype.arc=function(t,e,r,i,n,a){this._drawPendingPt(),k[0]=i,k[1]=n,S(k,a),i=k[0],n=k[1];var o=n-i;return this.addData(h.A,t,e,r,r,i,o,0,a?0:1),this._ctx&&this._ctx.arc(t,e,r,i,n,a),this._xi=y(n)*r+t,this._yi=_(n)*r+e,this},t.prototype.arcTo=function(t,e,r,i,n){return this._drawPendingPt(),this._ctx&&this._ctx.arcTo(t,e,r,i,n),this},t.prototype.rect=function(t,e,r,i){return this._drawPendingPt(),this._ctx&&this._ctx.rect(t,e,r,i),this.addData(h.R,t,e,r,i),this},t.prototype.closePath=function(){this._drawPendingPt(),this.addData(h.Z);var t=this._ctx,e=this._x0,r=this._y0;return t&&t.closePath(),this._xi=e,this._yi=r,this},t.prototype.fill=function(t){t&&t.fill(),this.toStatic()},t.prototype.stroke=function(t){t&&t.stroke(),this.toStatic()},t.prototype.len=function(){return this._len},t.prototype.setData=function(t){var e=t.length;this.data&&this.data.length===e||!b||(this.data=new Float32Array(e));for(var r=0;r<e;r++)this.data[r]=t[r];this._len=e},t.prototype.appendPath=function(t){t instanceof Array||(t=[t]);for(var e=t.length,r=0,i=this._len,n=0;n<e;n++)r+=t[n].len();b&&this.data instanceof Float32Array&&(this.data=new Float32Array(i+r));for(n=0;n<e;n++)for(var a=t[n].data,o=0;o<a.length;o++)this.data[i++]=a[o];this._len=i},t.prototype.addData=function(t,e,r,i,n,a,o,s,h){if(this._saveData){var l=this.data;this._len+arguments.length>l.length&&(this._expandData(),l=this.data);for(var u=0;u<arguments.length;u++)l[this._len++]=arguments[u]}},t.prototype._drawPendingPt=function(){this._pendingPtDist>0&&(this._ctx&&this._ctx.lineTo(this._pendingPtX,this._pendingPtY),this._pendingPtDist=0)},t.prototype._expandData=function(){if(!(this.data instanceof Array)){for(var t=[],e=0;e<this._len;e++)t[e]=this.data[e];this.data=t}},t.prototype.toStatic=function(){if(this._saveData){this._drawPendingPt();var t=this.data;t instanceof Array&&(t.length=this._len,b&&this._len>11&&(this.data=new Float32Array(t)))}},t.prototype.getBoundingRect=function(){c[0]=c[1]=d[0]=d[1]=Number.MAX_VALUE,f[0]=f[1]=p[0]=p[1]=-Number.MAX_VALUE;var t,e=this.data,r=0,a=0,s=0,l=0;for(t=0;t<this._len;){var u=e[t++],v=1===t;switch(v&&(r=e[t],a=e[t+1],s=r,l=a),u){case h.M:r=s=e[t++],a=l=e[t++],d[0]=s,d[1]=l,p[0]=s,p[1]=l;break;case h.L:(0,o.u4)(r,a,e[t],e[t+1],d,p),r=e[t++],a=e[t++];break;case h.C:(0,o.H9)(r,a,e[t++],e[t++],e[t++],e[t++],e[t],e[t+1],d,p),r=e[t++],a=e[t++];break;case h.Q:(0,o.mJ)(r,a,e[t++],e[t++],e[t],e[t+1],d,p),r=e[t++],a=e[t++];break;case h.A:var g=e[t++],m=e[t++],x=e[t++],w=e[t++],b=e[t++],k=e[t++]+b;t+=1;var T=!e[t++];v&&(s=y(b)*x+g,l=_(b)*w+m),(0,o.qL)(g,m,x,w,b,k,T,d,p),r=y(k)*x+g,a=_(k)*w+m;break;case h.R:s=r=e[t++],l=a=e[t++];var S=e[t++],C=e[t++];(0,o.u4)(s,l,s+S,l+C,d,p);break;case h.Z:r=s,a=l;break}i.min(c,c,d),i.max(f,f,p)}return 0===t&&(c[0]=c[1]=f[0]=f[1]=0),new n.Z(c[0],c[1],f[0]-c[0],f[1]-c[1])},t.prototype._calculateLength=function(){var t=this.data,e=this._len,r=this._ux,i=this._uy,n=0,a=0,o=0,l=0;this._pathSegLen||(this._pathSegLen=[]);for(var u=this._pathSegLen,c=0,f=0,d=0;d<e;){var p=t[d++],x=1===d;x&&(n=t[d],a=t[d+1],o=n,l=a);var b=-1;switch(p){case h.M:n=o=t[d++],a=l=t[d++];break;case h.L:var k=t[d++],T=t[d++],S=k-n,C=T-a;(m(S)>r||m(C)>i||d===e-1)&&(b=Math.sqrt(S*S+C*C),n=k,a=T);break;case h.C:var P=t[d++],A=t[d++],M=(k=t[d++],T=t[d++],t[d++]),L=t[d++];b=(0,s.Ci)(n,a,P,A,k,T,M,L,10),n=M,a=L;break;case h.Q:P=t[d++],A=t[d++],k=t[d++],T=t[d++];b=(0,s.wQ)(n,a,P,A,k,T,10),n=k,a=T;break;case h.A:var D=t[d++],z=t[d++],O=t[d++],R=t[d++],I=t[d++],F=t[d++],B=F+I;d+=1;t[d++];x&&(o=y(I)*O+D,l=_(I)*R+z),b=g(O,R)*v(w,Math.abs(F)),n=y(B)*O+D,a=_(B)*R+z;break;case h.R:o=n=t[d++],l=a=t[d++];var E=t[d++],Z=t[d++];b=2*E+2*Z;break;case h.Z:S=o-n,C=l-a;b=Math.sqrt(S*S+C*C),n=o,a=l;break}b>=0&&(u[f++]=b,c+=b)}return this._pathLen=c,c},t.prototype.rebuildPath=function(t,e){var r,i,n,a,o,c,f,d,p,x,w,b=this.data,k=this._ux,T=this._uy,S=this._len,C=e<1,P=0,A=0,M=0;if(!C||(this._pathSegLen||this._calculateLength(),f=this._pathSegLen,d=this._pathLen,p=e*d,p))t:for(var L=0;L<S;){var D=b[L++],z=1===L;switch(z&&(n=b[L],a=b[L+1],r=n,i=a),D!==h.L&&M>0&&(t.lineTo(x,w),M=0),D){case h.M:r=n=b[L++],i=a=b[L++],t.moveTo(n,a);break;case h.L:o=b[L++],c=b[L++];var O=m(o-n),R=m(c-a);if(O>k||R>T){if(C){var I=f[A++];if(P+I>p){var F=(p-P)/I;t.lineTo(n*(1-F)+o*F,a*(1-F)+c*F);break t}P+=I}t.lineTo(o,c),n=o,a=c,M=0}else{var B=O*O+R*R;B>M&&(x=o,w=c,M=B)}break;case h.C:var E=b[L++],Z=b[L++],j=b[L++],H=b[L++],N=b[L++],W=b[L++];if(C){I=f[A++];if(P+I>p){F=(p-P)/I;(0,s.Vz)(n,E,j,N,F,l),(0,s.Vz)(a,Z,H,W,F,u),t.bezierCurveTo(l[1],u[1],l[2],u[2],l[3],u[3]);break t}P+=I}t.bezierCurveTo(E,Z,j,H,N,W),n=N,a=W;break;case h.Q:E=b[L++],Z=b[L++],j=b[L++],H=b[L++];if(C){I=f[A++];if(P+I>p){F=(p-P)/I;(0,s.Lx)(n,E,j,F,l),(0,s.Lx)(a,Z,H,F,u),t.quadraticCurveTo(l[1],u[1],l[2],u[2]);break t}P+=I}t.quadraticCurveTo(E,Z,j,H),n=j,a=H;break;case h.A:var V=b[L++],q=b[L++],Y=b[L++],X=b[L++],U=b[L++],$=b[L++],G=b[L++],Q=!b[L++],K=Y>X?Y:X,J=m(Y-X)>.001,tt=U+$,et=!1;if(C){I=f[A++];P+I>p&&(tt=U+$*(p-P)/I,et=!0),P+=I}if(J&&t.ellipse?t.ellipse(V,q,Y,X,G,U,tt,Q):t.arc(V,q,K,U,tt,Q),et)break t;z&&(r=y(U)*Y+V,i=_(U)*X+q),n=y(tt)*Y+V,a=_(tt)*X+q;break;case h.R:r=n=b[L],i=a=b[L+1],o=b[L++],c=b[L++];var rt=b[L++],it=b[L++];if(C){I=f[A++];if(P+I>p){var nt=p-P;t.moveTo(o,c),t.lineTo(o+v(nt,rt),c),nt-=rt,nt>0&&t.lineTo(o+rt,c+v(nt,it)),nt-=it,nt>0&&t.lineTo(o+g(rt-nt,0),c+it),nt-=rt,nt>0&&t.lineTo(o,c+g(it-nt,0));break t}P+=I}t.rect(o,c,rt,it);break;case h.Z:if(C){I=f[A++];if(P+I>p){F=(p-P)/I;t.lineTo(n*(1-F)+r*F,a*(1-F)+i*F);break t}P+=I}t.closePath(),n=r,a=i}}},t.prototype.clone=function(){var e=new t,r=this.data;return e.data=r.slice?r.slice():Array.prototype.slice.call(r),e._len=this._len,e},t.CMD=h,t.initDefaultProps=function(){var e=t.prototype;e._saveData=!0,e._ux=0,e._uy=0,e._pendingPtDist=0,e._version=0}(),t}();e["Z"]=/^(3491|4190|5903|6241)$/.test(r.j)?C:null},41284:function(t,e,r){"use strict";var i=function(){function t(t,e){this.x=t||0,this.y=e||0}return t.prototype.copy=function(t){return this.x=t.x,this.y=t.y,this},t.prototype.clone=function(){return new t(this.x,this.y)},t.prototype.set=function(t,e){return this.x=t,this.y=e,this},t.prototype.equal=function(t){return t.x===this.x&&t.y===this.y},t.prototype.add=function(t){return this.x+=t.x,this.y+=t.y,this},t.prototype.scale=function(t){this.x*=t,this.y*=t},t.prototype.scaleAndAdd=function(t,e){this.x+=t.x*e,this.y+=t.y*e},t.prototype.sub=function(t){return this.x-=t.x,this.y-=t.y,this},t.prototype.dot=function(t){return this.x*t.x+this.y*t.y},t.prototype.len=function(){return Math.sqrt(this.x*this.x+this.y*this.y)},t.prototype.lenSquare=function(){return this.x*this.x+this.y*this.y},t.prototype.normalize=function(){var t=this.len();return this.x/=t,this.y/=t,this},t.prototype.distance=function(t){var e=this.x-t.x,r=this.y-t.y;return Math.sqrt(e*e+r*r)},t.prototype.distanceSquare=function(t){var e=this.x-t.x,r=this.y-t.y;return e*e+r*r},t.prototype.negate=function(){return this.x=-this.x,this.y=-this.y,this},t.prototype.transform=function(t){if(t){var e=this.x,r=this.y;return this.x=t[0]*e+t[2]*r+t[4],this.y=t[1]*e+t[3]*r+t[5],this}},t.prototype.toArray=function(t){return t[0]=this.x,t[1]=this.y,t},t.prototype.fromArray=function(t){this.x=t[0],this.y=t[1]},t.set=function(t,e,r){t.x=e,t.y=r},t.copy=function(t,e){t.x=e.x,t.y=e.y},t.len=function(t){return Math.sqrt(t.x*t.x+t.y*t.y)},t.lenSquare=function(t){return t.x*t.x+t.y*t.y},t.dot=function(t,e){return t.x*e.x+t.y*e.y},t.add=function(t,e,r){t.x=e.x+r.x,t.y=e.y+r.y},t.sub=function(t,e,r){t.x=e.x-r.x,t.y=e.y-r.y},t.scale=function(t,e,r){t.x=e.x*r,t.y=e.y*r},t.scaleAndAdd=function(t,e,r,i){t.x=e.x+r.x*i,t.y=e.y+r.y*i},t.lerp=function(t,e,r,i){var n=1-i;t.x=n*e.x+i*r.x,t.y=n*e.y+i*r.y},t}();e["Z"]=/^(3491|4190|5903|6241)$/.test(r.j)?i:null},71270:function(t,e,r){"use strict";r.d(e,{dN:function(){return d},kY:function(){return p}});var i=r(6366),n=r(77324),a=i.identity,o=5e-5;function s(t){return t>o||t<-o}var h=[],l=[],u=i.create(),c=Math.abs,f=function(){function t(){}return t.prototype.getLocalTransform=function(e){return t.getLocalTransform(this,e)},t.prototype.setPosition=function(t){this.x=t[0],this.y=t[1]},t.prototype.setScale=function(t){this.scaleX=t[0],this.scaleY=t[1]},t.prototype.setSkew=function(t){this.skewX=t[0],this.skewY=t[1]},t.prototype.setOrigin=function(t){this.originX=t[0],this.originY=t[1]},t.prototype.needLocalTransform=function(){return s(this.rotation)||s(this.x)||s(this.y)||s(this.scaleX-1)||s(this.scaleY-1)||s(this.skewX)||s(this.skewY)},t.prototype.updateTransform=function(){var t=this.parent&&this.parent.transform,e=this.needLocalTransform(),r=this.transform;e||t?(r=r||i.create(),e?this.getLocalTransform(r):a(r),t&&(e?i.mul(r,t,r):i.copy(r,t)),this.transform=r,this._resolveGlobalScaleRatio(r)):r&&(a(r),this.invTransform=null)},t.prototype._resolveGlobalScaleRatio=function(t){var e=this.globalScaleRatio;if(null!=e&&1!==e){this.getGlobalScale(h);var r=h[0]<0?-1:1,n=h[1]<0?-1:1,a=((h[0]-r)*e+r)/h[0]||0,o=((h[1]-n)*e+n)/h[1]||0;t[0]*=a,t[1]*=a,t[2]*=o,t[3]*=o}this.invTransform=this.invTransform||i.create(),i.invert(this.invTransform,t)},t.prototype.getComputedTransform=function(){var t=this,e=[];while(t)e.push(t),t=t.parent;while(t=e.pop())t.updateTransform();return this.transform},t.prototype.setLocalTransform=function(t){if(t){var e=t[0]*t[0]+t[1]*t[1],r=t[2]*t[2]+t[3]*t[3],i=Math.atan2(t[1],t[0]),n=Math.PI/2+i-Math.atan2(t[3],t[2]);r=Math.sqrt(r)*Math.cos(n),e=Math.sqrt(e),this.skewX=n,this.skewY=0,this.rotation=-i,this.x=+t[4],this.y=+t[5],this.scaleX=e,this.scaleY=r,this.originX=0,this.originY=0}},t.prototype.decomposeTransform=function(){if(this.transform){var t=this.parent,e=this.transform;t&&t.transform&&(i.mul(l,t.invTransform,e),e=l);var r=this.originX,n=this.originY;(r||n)&&(u[4]=r,u[5]=n,i.mul(l,e,u),l[4]-=r,l[5]-=n,e=l),this.setLocalTransform(e)}},t.prototype.getGlobalScale=function(t){var e=this.transform;return t=t||[],e?(t[0]=Math.sqrt(e[0]*e[0]+e[1]*e[1]),t[1]=Math.sqrt(e[2]*e[2]+e[3]*e[3]),e[0]<0&&(t[0]=-t[0]),e[3]<0&&(t[1]=-t[1]),t):(t[0]=1,t[1]=1,t)},t.prototype.transformCoordToLocal=function(t,e){var r=[t,e],i=this.invTransform;return i&&n.applyTransform(r,r,i),r},t.prototype.transformCoordToGlobal=function(t,e){var r=[t,e],i=this.transform;return i&&n.applyTransform(r,r,i),r},t.prototype.getLineScale=function(){var t=this.transform;return t&&c(t[0]-1)>1e-10&&c(t[3]-1)>1e-10?Math.sqrt(c(t[0]*t[3]-t[2]*t[1])):1},t.prototype.copyTransform=function(t){p(this,t)},t.getLocalTransform=function(t,e){e=e||[];var r=t.originX||0,n=t.originY||0,a=t.scaleX,o=t.scaleY,s=t.anchorX,h=t.anchorY,l=t.rotation||0,u=t.x,c=t.y,f=t.skewX?Math.tan(t.skewX):0,d=t.skewY?Math.tan(-t.skewY):0;if(r||n||s||h){var p=r+s,v=n+h;e[4]=-p*a-f*v*o,e[5]=-v*o-d*p*a}else e[4]=e[5]=0;return e[0]=a,e[3]=o,e[1]=d*a,e[2]=f*o,l&&i.rotate(e,e,l),e[4]+=r+u,e[5]+=n+c,e},t.initDefaultProps=function(){var e=t.prototype;e.scaleX=e.scaleY=e.globalScaleRatio=1,e.x=e.y=e.originX=e.originY=e.skewX=e.skewY=e.rotation=e.anchorX=e.anchorY=0}(),t}(),d=["x","y","originX","originY","anchorX","anchorY","rotation","scaleX","scaleY","skewX","skewY"];function p(t,e){for(var r=0;r<d.length;r++){var i=d[r];t[i]=e[i]}}e["ZP"]=/^(3491|4190|5903|6241)$/.test(r.j)?f:null},43289:function(t,e,r){"use strict";var i=Math.round(9*Math.random()),n="function"===typeof Object.defineProperty,a=function(){function t(){this._id="__ec_inner_"+i++}return t.prototype.get=function(t){return this._guard(t)[this._id]},t.prototype.set=function(t,e){var r=this._guard(t);return n?Object.defineProperty(r,this._id,{value:e,enumerable:!1,configurable:!0}):r[this._id]=e,this},t.prototype["delete"]=function(t){return!!this.has(t)&&(delete this._guard(t)[this._id],!0)},t.prototype.has=function(t){return!!this._guard(t)[this._id]},t.prototype._guard=function(t){if(t!==Object(t))throw TypeError("Value of WeakMap is not a non-null object.");return t},t}();e["Z"]=/^(3491|4190|5903|6241)$/.test(r.j)?a:null},85036:function(t,e,r){"use strict";r.d(e,{H9:function(){return y},mJ:function(){return _},qL:function(){return m},u4:function(){return p},zk:function(){return d}});var i=r(77324);if(/^(3491|4190|5903|6241)$/.test(r.j))var n=r(54410);var a=Math.min,o=Math.max,s=Math.sin,h=Math.cos,l=2*Math.PI,u=i.create(),c=i.create(),f=i.create();function d(t,e,r){if(0!==t.length){for(var i=t[0],n=i[0],s=i[0],h=i[1],l=i[1],u=1;u<t.length;u++)i=t[u],n=a(n,i[0]),s=o(s,i[0]),h=a(h,i[1]),l=o(l,i[1]);e[0]=n,e[1]=h,r[0]=s,r[1]=l}}function p(t,e,r,i,n,s){n[0]=a(t,r),n[1]=a(e,i),s[0]=o(t,r),s[1]=o(e,i)}var v=/^(3491|4190|5903|6241)$/.test(r.j)?[]:null,g=/^(3491|4190|5903|6241)$/.test(r.j)?[]:null;function y(t,e,r,i,s,h,l,u,c,f){var d=n.pP,p=n.af,y=d(t,r,s,l,v);c[0]=1/0,c[1]=1/0,f[0]=-1/0,f[1]=-1/0;for(var _=0;_<y;_++){var m=p(t,r,s,l,v[_]);c[0]=a(m,c[0]),f[0]=o(m,f[0])}y=d(e,i,h,u,g);for(_=0;_<y;_++){var x=p(e,i,h,u,g[_]);c[1]=a(x,c[1]),f[1]=o(x,f[1])}c[0]=a(t,c[0]),f[0]=o(t,f[0]),c[0]=a(l,c[0]),f[0]=o(l,f[0]),c[1]=a(e,c[1]),f[1]=o(e,f[1]),c[1]=a(u,c[1]),f[1]=o(u,f[1])}function _(t,e,r,i,s,h,l,u){var c=n.QC,f=n.Zm,d=o(a(c(t,r,s),1),0),p=o(a(c(e,i,h),1),0),v=f(t,r,s,d),g=f(e,i,h,p);l[0]=a(t,s,v),l[1]=a(e,h,g),u[0]=o(t,s,v),u[1]=o(e,h,g)}function m(t,e,r,n,a,o,d,p,v){var g=i.min,y=i.max,_=Math.abs(a-o);if(_%l<1e-4&&_>1e-4)return p[0]=t-r,p[1]=e-n,v[0]=t+r,void(v[1]=e+n);if(u[0]=h(a)*r+t,u[1]=s(a)*n+e,c[0]=h(o)*r+t,c[1]=s(o)*n+e,g(p,u,c),y(v,u,c),a%=l,a<0&&(a+=l),o%=l,o<0&&(o+=l),a>o&&!d?o+=l:a<o&&d&&(a+=l),d){var m=o;o=a,a=m}for(var x=0;x<o;x+=Math.PI/2)x>a&&(f[0]=h(x)*r+t,f[1]=s(x)*n+e,g(p,f,p),y(v,f,v))}},54410:function(t,e,r){"use strict";r.d(e,{AZ:function(){return k},Ci:function(){return w},Jz:function(){return T},Lx:function(){return C},QC:function(){return S},Vz:function(){return m},Wr:function(){return P},X_:function(){return g},Zm:function(){return b},af:function(){return v},kD:function(){return y},pP:function(){return _},t1:function(){return x},wQ:function(){return A}});var i=r(77324),n=Math.pow,a=Math.sqrt,o=1e-8,s=1e-4,h=a(3),l=/^(3491|4190|5903|6241)$/.test(r.j)?1/3:null,u=(0,i.create)(),c=(0,i.create)(),f=(0,i.create)();function d(t){return t>-o&&t<o}function p(t){return t>o||t<-o}function v(t,e,r,i,n){var a=1-n;return a*a*(a*t+3*n*e)+n*n*(n*i+3*a*r)}function g(t,e,r,i,n){var a=1-n;return 3*(((e-t)*a+2*(r-e)*n)*a+(i-r)*n*n)}function y(t,e,r,i,o,s){var u=i+3*(e-r)-t,c=3*(r-2*e+t),f=3*(e-t),p=t-o,v=c*c-3*u*f,g=c*f-9*u*p,y=f*f-3*c*p,_=0;if(d(v)&&d(g))if(d(c))s[0]=0;else{var m=-f/c;m>=0&&m<=1&&(s[_++]=m)}else{var x=g*g-4*v*y;if(d(x)){var w=g/v,b=(m=-c/u+w,-w/2);m>=0&&m<=1&&(s[_++]=m),b>=0&&b<=1&&(s[_++]=b)}else if(x>0){var k=a(x),T=v*c+1.5*u*(-g+k),S=v*c+1.5*u*(-g-k);T=T<0?-n(-T,l):n(T,l),S=S<0?-n(-S,l):n(S,l);m=(-c-(T+S))/(3*u);m>=0&&m<=1&&(s[_++]=m)}else{var C=(2*v*c-3*u*g)/(2*a(v*v*v)),P=Math.acos(C)/3,A=a(v),M=Math.cos(P),L=(m=(-c-2*A*M)/(3*u),b=(-c+A*(M+h*Math.sin(P)))/(3*u),(-c+A*(M-h*Math.sin(P)))/(3*u));m>=0&&m<=1&&(s[_++]=m),b>=0&&b<=1&&(s[_++]=b),L>=0&&L<=1&&(s[_++]=L)}}return _}function _(t,e,r,i,n){var o=6*r-12*e+6*t,s=9*e+3*i-3*t-9*r,h=3*e-3*t,l=0;if(d(s)){if(p(o)){var u=-h/o;u>=0&&u<=1&&(n[l++]=u)}}else{var c=o*o-4*s*h;if(d(c))n[0]=-o/(2*s);else if(c>0){var f=a(c),v=(u=(-o+f)/(2*s),(-o-f)/(2*s));u>=0&&u<=1&&(n[l++]=u),v>=0&&v<=1&&(n[l++]=v)}}return l}function m(t,e,r,i,n,a){var o=(e-t)*n+t,s=(r-e)*n+e,h=(i-r)*n+r,l=(s-o)*n+o,u=(h-s)*n+s,c=(u-l)*n+l;a[0]=t,a[1]=o,a[2]=l,a[3]=c,a[4]=c,a[5]=u,a[6]=h,a[7]=i}function x(t,e,r,n,o,h,l,d,p,g,y){var _,m,x,w,b,k=.005,T=1/0;u[0]=p,u[1]=g;for(var S=0;S<1;S+=.05)c[0]=v(t,r,o,l,S),c[1]=v(e,n,h,d,S),w=(0,i.distSquare)(u,c),w<T&&(_=S,T=w);T=1/0;for(var C=0;C<32;C++){if(k<s)break;m=_-k,x=_+k,c[0]=v(t,r,o,l,m),c[1]=v(e,n,h,d,m),w=(0,i.distSquare)(c,u),m>=0&&w<T?(_=m,T=w):(f[0]=v(t,r,o,l,x),f[1]=v(e,n,h,d,x),b=(0,i.distSquare)(f,u),x<=1&&b<T?(_=x,T=b):k*=.5)}return y&&(y[0]=v(t,r,o,l,_),y[1]=v(e,n,h,d,_)),a(T)}function w(t,e,r,i,n,a,o,s,h){for(var l=t,u=e,c=0,f=1/h,d=1;d<=h;d++){var p=d*f,g=v(t,r,n,o,p),y=v(e,i,a,s,p),_=g-l,m=y-u;c+=Math.sqrt(_*_+m*m),l=g,u=y}return c}function b(t,e,r,i){var n=1-i;return n*(n*t+2*i*e)+i*i*r}function k(t,e,r,i){return 2*((1-i)*(e-t)+i*(r-e))}function T(t,e,r,i,n){var o=t-2*e+r,s=2*(e-t),h=t-i,l=0;if(d(o)){if(p(s)){var u=-h/s;u>=0&&u<=1&&(n[l++]=u)}}else{var c=s*s-4*o*h;if(d(c)){u=-s/(2*o);u>=0&&u<=1&&(n[l++]=u)}else if(c>0){var f=a(c),v=(u=(-s+f)/(2*o),(-s-f)/(2*o));u>=0&&u<=1&&(n[l++]=u),v>=0&&v<=1&&(n[l++]=v)}}return l}function S(t,e,r){var i=t+r-2*e;return 0===i?.5:(t-e)/i}function C(t,e,r,i,n){var a=(e-t)*i+t,o=(r-e)*i+e,s=(o-a)*i+a;n[0]=t,n[1]=a,n[2]=s,n[3]=s,n[4]=o,n[5]=r}function P(t,e,r,n,o,h,l,d,p){var v,g=.005,y=1/0;u[0]=l,u[1]=d;for(var _=0;_<1;_+=.05){c[0]=b(t,r,o,_),c[1]=b(e,n,h,_);var m=(0,i.distSquare)(u,c);m<y&&(v=_,y=m)}y=1/0;for(var x=0;x<32;x++){if(g<s)break;var w=v-g,k=v+g;c[0]=b(t,r,o,w),c[1]=b(e,n,h,w);m=(0,i.distSquare)(c,u);if(w>=0&&m<y)v=w,y=m;else{f[0]=b(t,r,o,k),f[1]=b(e,n,h,k);var T=(0,i.distSquare)(f,u);k<=1&&T<y?(v=k,y=T):g*=.5}}return p&&(p[0]=b(t,r,o,v),p[1]=b(e,n,h,v)),a(y)}function A(t,e,r,i,n,a,o){for(var s=t,h=e,l=0,u=1/o,c=1;c<=o;c++){var f=c*u,d=b(t,r,n,f),p=b(e,i,a,f),v=d-s,g=p-h;l+=Math.sqrt(v*v+g*g),s=d,h=p}return l}},98249:function(t,e,r){"use strict";if(r.d(e,{A4:function(){return h},F1:function(){return p},UK:function(){return c},YB:function(){return s}}),/^(3491|4190|5903|6241)$/.test(r.j))var i=r(84754);if(/^(3491|4190|5903|6241)$/.test(r.j))var n=r(70225);var a="___zrEVENTSAVED",o=/^(3491|4190|5903|6241)$/.test(r.j)?[]:null;function s(t,e,r,i,n){return h(o,e,i,n,!0)&&h(t,r,o[0],o[1])}function h(t,e,r,n,o){if(e.getBoundingClientRect&&i.Z.domSupported&&!c(e)){var s=e[a]||(e[a]={}),h=l(e,s),f=u(h,s,o);if(f)return f(t,r,n),!0}return!1}function l(t,e){var r=e.markers;if(r)return r;r=e.markers=[];for(var i=["left","right"],n=["top","bottom"],a=0;a<4;a++){var o=document.createElement("div"),s=o.style,h=a%2,l=(a>>1)%2;s.cssText=["position: absolute","visibility: hidden","padding: 0","margin: 0","border-width: 0","user-select: none","width:0","height:0",i[h]+":0",n[l]+":0",i[1-h]+":auto",n[1-l]+":auto",""].join("!important;"),t.appendChild(o),r.push(o)}return r}function u(t,e,r){for(var i=r?"invTrans":"trans",a=e[i],o=e.srcCoords,s=[],h=[],l=!0,u=0;u<4;u++){var c=t[u].getBoundingClientRect(),f=2*u,d=c.left,p=c.top;s.push(d,p),l=l&&o&&d===o[f]&&p===o[f+1],h.push(t[u].offsetLeft,t[u].offsetTop)}return l&&a?a:(e.srcCoords=s,e[i]=r?(0,n.Q)(h,s):(0,n.Q)(s,h))}function c(t){return"CANVAS"===t.nodeName.toUpperCase()}var f=/([&<>"'])/g,d={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};function p(t){return null==t?"":(t+"").replace(f,(function(t,e){return d[e]}))}},84754:function(t,e,r){"use strict";var i=r(3336),n=function(){function t(){this.firefox=!1,this.ie=!1,this.edge=!1,this.newEdge=!1,this.weChat=!1}return t}(),a=function(){function t(){this.browser=new n,this.node=!1,this.wxa=!1,this.worker=!1,this.svgSupported=!1,this.touchEventsSupported=!1,this.pointerEventsSupported=!1,this.domSupported=!1,this.transformSupported=!1,this.transform3dSupported=!1,this.hasGlobalWindow="undefined"!==typeof window}return t}(),o=new a;function s(t,e){var r=e.browser,i=t.match(/Firefox\/([\d.]+)/),n=t.match(/MSIE\s([\d.]+)/)||t.match(/Trident\/.+?rv:(([\d.]+))/),a=t.match(/Edge?\/([\d.]+)/),o=/micromessenger/i.test(t);i&&(r.firefox=!0,r.version=i[1]),n&&(r.ie=!0,r.version=n[1]),a&&(r.edge=!0,r.version=a[1],r.newEdge=+a[1].split(".")[0]>18),o&&(r.weChat=!0),e.svgSupported="undefined"!==typeof SVGRect,e.touchEventsSupported="ontouchstart"in window&&!r.ie&&!r.edge,e.pointerEventsSupported="onpointerdown"in window&&(r.edge||r.ie&&+r.version>=11),e.domSupported="undefined"!==typeof document;var s=document.documentElement.style;e.transform3dSupported=(r.ie&&"transition"in s||r.edge||"WebKitCSSMatrix"in window&&"m11"in new WebKitCSSMatrix||"MozPerspective"in s)&&!("OTransition"in s),e.transformSupported=e.transform3dSupported||r.ie&&+r.version>=9}"object"===("undefined"===typeof wx?"undefined":(0,i.Z)(wx))&&"function"===typeof wx.getSystemInfoSync?(o.wxa=!0,o.touchEventsSupported=!0):"undefined"===typeof document&&"undefined"!==typeof self?o.worker=!0:"undefined"===typeof navigator?(o.node=!0,o.svgSupported=!0):s(navigator.userAgent,o),e["Z"]=/^(3491|4190|5903|6241)$/.test(r.j)?o:null},37914:function(t,e,r){"use strict";r.d(e,{OD:function(){return c},Oo:function(){return d},eV:function(){return h},iP:function(){return u},sT:function(){return v},x1:function(){return g},xg:function(){return p}});var i=r(84754);if(/^(3491|4190|5903|6241)$/.test(r.j))var n=r(98249);var a=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,o=/^(3491|4190|5903|6241)$/.test(r.j)?[]:null,s=i.Z.browser.firefox&&+i.Z.browser.version.split(".")[0]<39;function h(t,e,r,i){return r=r||{},i?l(t,e,r):s&&null!=e.layerX&&e.layerX!==e.offsetX?(r.zrX=e.layerX,r.zrY=e.layerY):null!=e.offsetX?(r.zrX=e.offsetX,r.zrY=e.offsetY):l(t,e,r),r}function l(t,e,r){if(i.Z.domSupported&&t.getBoundingClientRect){var a=e.clientX,s=e.clientY;if((0,n.UK)(t)){var h=t.getBoundingClientRect();return r.zrX=a-h.left,void(r.zrY=s-h.top)}if((0,n.A4)(o,t,a,s))return r.zrX=o[0],void(r.zrY=o[1])}r.zrX=r.zrY=0}function u(t){return t||window.event}function c(t,e,r){if(e=u(e),null!=e.zrX)return e;var i=e.type,n=i&&i.indexOf("touch")>=0;if(n){var o="touchend"!==i?e.targetTouches[0]:e.changedTouches[0];o&&h(t,o,e,r)}else{h(t,e,e,r);var s=f(e);e.zrDelta=s?s/120:-(e.detail||0)/3}var l=e.button;return null==e.which&&void 0!==l&&a.test(e.type)&&(e.which=1&l?1:2&l?3:4&l?2:0),e}function f(t){var e=t.wheelDelta;if(e)return e;var r=t.deltaX,i=t.deltaY;if(null==r||null==i)return e;var n=0!==i?Math.abs(i):Math.abs(r),a=i>0?-1:i<0?1:r>0?-1:1;return 3*n*a}function d(t,e,r,i){t.addEventListener(e,r,i)}function p(t,e,r,i){t.removeEventListener(e,r,i)}var v=function(t){t.preventDefault(),t.stopPropagation(),t.cancelBubble=!0};function g(t){return 2===t.which||3===t.which}},70225:function(t,e,r){"use strict";r.d(e,{Q:function(){return a}});var i=Math.log(2);function n(t,e,r,a,o,s){var h=a+"-"+o,l=t.length;if(s.hasOwnProperty(h))return s[h];if(1===e){var u=Math.round(Math.log((1<<l)-1&~o)/i);return t[r][u]}var c=a|1<<r,f=r+1;while(a&1<<f)f++;for(var d=0,p=0,v=0;p<l;p++){var g=1<<p;g&o||(d+=(v%2?-1:1)*t[r][p]*n(t,e-1,f,c,o|g,s),v++)}return s[h]=d,d}function a(t,e){var r=[[t[0],t[1],1,0,0,0,-e[0]*t[0],-e[0]*t[1]],[0,0,0,t[0],t[1],1,-e[1]*t[0],-e[1]*t[1]],[t[2],t[3],1,0,0,0,-e[2]*t[2],-e[2]*t[3]],[0,0,0,t[2],t[3],1,-e[3]*t[2],-e[3]*t[3]],[t[4],t[5],1,0,0,0,-e[4]*t[4],-e[4]*t[5]],[0,0,0,t[4],t[5],1,-e[5]*t[4],-e[5]*t[5]],[t[6],t[7],1,0,0,0,-e[6]*t[6],-e[6]*t[7]],[0,0,0,t[6],t[7],1,-e[7]*t[6],-e[7]*t[7]]],i={},a=n(r,8,0,0,0,i);if(0!==a){for(var o=[],s=0;s<8;s++)for(var h=0;h<8;h++)null==o[h]&&(o[h]=0),o[h]+=((s+h)%2?-1:1)*n(r,7,0===s?1:0,1<<s,1<<h,i)/a*e[s];return function(t,e,r){var i=e*o[6]+r*o[7]+1;t[0]=(e*o[0]+r*o[1]+o[2])/i,t[1]=(e*o[3]+r*o[4]+o[5])/i}}}},6366:function(t,e,r){"use strict";function i(){return[1,0,0,1,0,0]}function n(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,t}function a(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t}function o(t,e,r){var i=e[0]*r[0]+e[2]*r[1],n=e[1]*r[0]+e[3]*r[1],a=e[0]*r[2]+e[2]*r[3],o=e[1]*r[2]+e[3]*r[3],s=e[0]*r[4]+e[2]*r[5]+e[4],h=e[1]*r[4]+e[3]*r[5]+e[5];return t[0]=i,t[1]=n,t[2]=a,t[3]=o,t[4]=s,t[5]=h,t}function s(t,e,r){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4]+r[0],t[5]=e[5]+r[1],t}function h(t,e,r){var i=e[0],n=e[2],a=e[4],o=e[1],s=e[3],h=e[5],l=Math.sin(r),u=Math.cos(r);return t[0]=i*u+o*l,t[1]=-i*l+o*u,t[2]=n*u+s*l,t[3]=-n*l+u*s,t[4]=u*a+l*h,t[5]=u*h-l*a,t}function l(t,e,r){var i=r[0],n=r[1];return t[0]=e[0]*i,t[1]=e[1]*n,t[2]=e[2]*i,t[3]=e[3]*n,t[4]=e[4]*i,t[5]=e[5]*n,t}function u(t,e){var r=e[0],i=e[2],n=e[4],a=e[1],o=e[3],s=e[5],h=r*o-a*i;return h?(h=1/h,t[0]=o*h,t[1]=-a*h,t[2]=-i*h,t[3]=r*h,t[4]=(i*s-o*n)*h,t[5]=(a*n-r*s)*h,t):null}function c(t){var e=i();return a(e,t),e}r.r(e),r.d(e,{clone:function(){return c},copy:function(){return a},create:function(){return i},identity:function(){return n},invert:function(){return u},mul:function(){return o},rotate:function(){return h},scale:function(){return l},translate:function(){return s}})},11693:function(t,e,r){"use strict";r.d(e,{Uo:function(){return a},g2:function(){return f},n5:function(){return i},qW:function(){return c},rk:function(){return n}});var i=12,n="sans-serif",a=i+"px "+n,o=20,s=100,h="007LLmW'55;N0500LLLLLLLLLL00NNNLzWW\\\\WQb\\0FWLg\\bWb\\WQ\\WrWWQ000CL5LLFLL0LL**F*gLLLL5F0LF\\FFF5.5N";function l(t){var e={};if("undefined"===typeof JSON)return e;for(var r=0;r<t.length;r++){var i=String.fromCharCode(r+32),n=(t.charCodeAt(r)-o)/s;e[i]=n}return e}var u=l(h),c={createCanvas:function(){return"undefined"!==typeof document&&document.createElement("canvas")},measureText:function(){var t,e;return function(r,n){if(!t){var o=c.createCanvas();t=o&&o.getContext("2d")}if(t)return e!==n&&(e=t.font=n||a),t.measureText(r);r=r||"",n=n||a;var s=/(\d+)px/.exec(n),h=s&&+s[1]||i,l=0;if(n.indexOf("mono")>=0)l=h*r.length;else for(var f=0;f<r.length;f++){var d=u[r[f]];l+=null==d?h:d*h}return{width:l}}}(),loadImage:function(t,e,r){var i=new Image;return i.onload=e,i.onerror=r,i.src=t,i}};function f(t){for(var e in c)t[e]&&(c[e]=t[e])}},57795:function(t,e,r){"use strict";r.d(e,{Z:function(){return f}});var i=32,n=7;function a(t){var e=0;while(t>=i)e|=1&t,t>>=1;return t+e}function o(t,e,r,i){var n=e+1;if(n===r)return 1;if(i(t[n++],t[e])<0){while(n<r&&i(t[n],t[n-1])<0)n++;s(t,e,n)}else while(n<r&&i(t[n],t[n-1])>=0)n++;return n-e}function s(t,e,r){r--;while(e<r){var i=t[e];t[e++]=t[r],t[r--]=i}}function h(t,e,r,i,n){for(i===e&&i++;i<r;i++){var a,o=t[i],s=e,h=i;while(s<h)a=s+h>>>1,n(o,t[a])<0?h=a:s=a+1;var l=i-s;switch(l){case 3:t[s+3]=t[s+2];case 2:t[s+2]=t[s+1];case 1:t[s+1]=t[s];break;default:while(l>0)t[s+l]=t[s+l-1],l--}t[s]=o}}function l(t,e,r,i,n,a){var o=0,s=0,h=1;if(a(t,e[r+n])>0){s=i-n;while(h<s&&a(t,e[r+n+h])>0)o=h,h=1+(h<<1),h<=0&&(h=s);h>s&&(h=s),o+=n,h+=n}else{s=n+1;while(h<s&&a(t,e[r+n-h])<=0)o=h,h=1+(h<<1),h<=0&&(h=s);h>s&&(h=s);var l=o;o=n-h,h=n-l}o++;while(o<h){var u=o+(h-o>>>1);a(t,e[r+u])>0?o=u+1:h=u}return h}function u(t,e,r,i,n,a){var o=0,s=0,h=1;if(a(t,e[r+n])<0){s=n+1;while(h<s&&a(t,e[r+n-h])<0)o=h,h=1+(h<<1),h<=0&&(h=s);h>s&&(h=s);var l=o;o=n-h,h=n-l}else{s=i-n;while(h<s&&a(t,e[r+n+h])>=0)o=h,h=1+(h<<1),h<=0&&(h=s);h>s&&(h=s),o+=n,h+=n}o++;while(o<h){var u=o+(h-o>>>1);a(t,e[r+u])<0?h=u:o=u+1}return h}function c(t,e){var r,i,a=n,o=0,s=0;o=t.length;var h=[];function c(t,e){r[s]=t,i[s]=e,s+=1}function f(){while(s>1){var t=s-2;if(t>=1&&i[t-1]<=i[t]+i[t+1]||t>=2&&i[t-2]<=i[t]+i[t-1])i[t-1]<i[t+1]&&t--;else if(i[t]>i[t+1])break;p(t)}}function d(){while(s>1){var t=s-2;t>0&&i[t-1]<i[t+1]&&t--,p(t)}}function p(n){var a=r[n],o=i[n],h=r[n+1],c=i[n+1];i[n]=o+c,n===s-3&&(r[n+1]=r[n+2],i[n+1]=i[n+2]),s--;var f=u(t[h],t,a,o,0,e);a+=f,o-=f,0!==o&&(c=l(t[a+o-1],t,h,c,c-1,e),0!==c&&(o<=c?v(a,o,h,c):g(a,o,h,c)))}function v(r,i,o,s){var c=0;for(c=0;c<i;c++)h[c]=t[r+c];var f=0,d=o,p=r;if(t[p++]=t[d++],0!==--s)if(1!==i){var v,g,y,_=a;while(1){v=0,g=0,y=!1;do{if(e(t[d],h[f])<0){if(t[p++]=t[d++],g++,v=0,0===--s){y=!0;break}}else if(t[p++]=h[f++],v++,g=0,1===--i){y=!0;break}}while((v|g)<_);if(y)break;do{if(v=u(t[d],h,f,i,0,e),0!==v){for(c=0;c<v;c++)t[p+c]=h[f+c];if(p+=v,f+=v,i-=v,i<=1){y=!0;break}}if(t[p++]=t[d++],0===--s){y=!0;break}if(g=l(h[f],t,d,s,0,e),0!==g){for(c=0;c<g;c++)t[p+c]=t[d+c];if(p+=g,d+=g,s-=g,0===s){y=!0;break}}if(t[p++]=h[f++],1===--i){y=!0;break}_--}while(v>=n||g>=n);if(y)break;_<0&&(_=0),_+=2}if(a=_,a<1&&(a=1),1===i){for(c=0;c<s;c++)t[p+c]=t[d+c];t[p+s]=h[f]}else{if(0===i)throw new Error;for(c=0;c<i;c++)t[p+c]=h[f+c]}}else{for(c=0;c<s;c++)t[p+c]=t[d+c];t[p+s]=h[f]}else for(c=0;c<i;c++)t[p+c]=h[f+c]}function g(r,i,o,s){var c=0;for(c=0;c<s;c++)h[c]=t[o+c];var f=r+i-1,d=s-1,p=o+s-1,v=0,g=0;if(t[p--]=t[f--],0!==--i)if(1!==s){var y=a;while(1){var _=0,m=0,x=!1;do{if(e(h[d],t[f])<0){if(t[p--]=t[f--],_++,m=0,0===--i){x=!0;break}}else if(t[p--]=h[d--],m++,_=0,1===--s){x=!0;break}}while((_|m)<y);if(x)break;do{if(_=i-u(h[d],t,r,i,i-1,e),0!==_){for(p-=_,f-=_,i-=_,g=p+1,v=f+1,c=_-1;c>=0;c--)t[g+c]=t[v+c];if(0===i){x=!0;break}}if(t[p--]=h[d--],1===--s){x=!0;break}if(m=s-l(t[f],h,0,s,s-1,e),0!==m){for(p-=m,d-=m,s-=m,g=p+1,v=d+1,c=0;c<m;c++)t[g+c]=h[v+c];if(s<=1){x=!0;break}}if(t[p--]=t[f--],0===--i){x=!0;break}y--}while(_>=n||m>=n);if(x)break;y<0&&(y=0),y+=2}if(a=y,a<1&&(a=1),1===s){for(p-=i,f-=i,g=p+1,v=f+1,c=i-1;c>=0;c--)t[g+c]=t[v+c];t[p]=h[d]}else{if(0===s)throw new Error;for(v=p-(s-1),c=0;c<s;c++)t[v+c]=h[c]}}else{for(p-=i,f-=i,g=p+1,v=f+1,c=i-1;c>=0;c--)t[g+c]=t[v+c];t[p]=h[d]}else for(v=p-(s-1),c=0;c<s;c++)t[v+c]=h[c]}return o<120?5:o<1542?10:o<119151?19:40,r=[],i=[],{mergeRuns:f,forceMergeRuns:d,pushRun:c}}function f(t,e,r,n){r||(r=0),n||(n=t.length);var s=n-r;if(!(s<2)){var l=0;if(s<i)return l=o(t,r,n,e),void h(t,r,n,r+l,e);var u=c(t,e),f=a(s);do{if(l=o(t,r,n,e),l<f){var d=s;d>f&&(d=f),h(t,r,r+d,r+l,e),l=d}u.pushRun(r,l),u.mergeRuns(),s-=l,r+=l}while(0!==s);u.forceMergeRuns()}}},41239:function(t,e,r){"use strict";if(r.r(e),r.d(e,{HashMap:function(){return ut},RADIAN_TO_DEGREE:function(){return yt},assert:function(){return rt},bind:function(){return F},clone:function(){return m},concatArray:function(){return ft},createCanvas:function(){return T},createHashMap:function(){return ct},createObject:function(){return dt},curry:function(){return B},defaults:function(){return k},disableUserSelect:function(){return pt},each:function(){return M},eqNaN:function(){return G},extend:function(){return b},filter:function(){return z},find:function(){return O},guid:function(){return y},hasOwn:function(){return vt},indexOf:function(){return S},inherits:function(){return C},isArray:function(){return E},isArrayLike:function(){return A},isBuiltInObject:function(){return V},isDom:function(){return Y},isFunction:function(){return Z},isGradientObject:function(){return X},isImagePatternObject:function(){return U},isNumber:function(){return N},isObject:function(){return W},isPrimitive:function(){return ot},isRegExp:function(){return $},isString:function(){return j},isStringSafe:function(){return H},isTypedArray:function(){return q},keys:function(){return R},logError:function(){return _},map:function(){return L},merge:function(){return x},mergeAll:function(){return w},mixin:function(){return P},noop:function(){return gt},normalizeCssArray:function(){return et},reduce:function(){return D},retrieve:function(){return Q},retrieve2:function(){return K},retrieve3:function(){return J},setAsPrimitive:function(){return at},slice:function(){return tt},trim:function(){return it}}),/^(3491|4190|5903|6241)$/.test(r.j))var i=r(3336);var n=r(11693),a=D(["Function","RegExp","Date","Error","CanvasGradient","CanvasPattern","Image","Canvas"],(function(t,e){return t["[object "+e+"]"]=!0,t}),{}),o=D(["Int8","Uint8","Uint8Clamped","Int16","Uint16","Int32","Uint32","Float32","Float64"],(function(t,e){return t["[object "+e+"Array]"]=!0,t}),{}),s=Object.prototype.toString,h=Array.prototype,l=h.forEach,u=h.filter,c=h.slice,f=h.map,d=function(){}.constructor,p=d?d.prototype:null,v="__proto__",g=2311;function y(){return g++}function _(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e]}function m(t){if(null==t||"object"!==(0,i.Z)(t))return t;var e=t,r=s.call(t);if("[object Array]"===r){if(!ot(t)){e=[];for(var n=0,h=t.length;n<h;n++)e[n]=m(t[n])}}else if(o[r]){if(!ot(t)){var l=t.constructor;if(l.from)e=l.from(t);else{e=new l(t.length);for(n=0,h=t.length;n<h;n++)e[n]=t[n]}}}else if(!a[r]&&!ot(t)&&!Y(t))for(var u in e={},t)t.hasOwnProperty(u)&&u!==v&&(e[u]=m(t[u]));return e}function x(t,e,r){if(!W(e)||!W(t))return r?m(e):t;for(var i in e)if(e.hasOwnProperty(i)&&i!==v){var n=t[i],a=e[i];!W(a)||!W(n)||E(a)||E(n)||Y(a)||Y(n)||V(a)||V(n)||ot(a)||ot(n)?!r&&i in t||(t[i]=m(e[i])):x(n,a,r)}return t}function w(t,e){for(var r=t[0],i=1,n=t.length;i<n;i++)r=x(r,t[i],e);return r}function b(t,e){if(Object.assign)Object.assign(t,e);else for(var r in e)e.hasOwnProperty(r)&&r!==v&&(t[r]=e[r]);return t}function k(t,e,r){for(var i=R(e),n=0;n<i.length;n++){var a=i[n];(r?null!=e[a]:null==t[a])&&(t[a]=e[a])}return t}var T=n.qW.createCanvas;function S(t,e){if(t){if(t.indexOf)return t.indexOf(e);for(var r=0,i=t.length;r<i;r++)if(t[r]===e)return r}return-1}function C(t,e){var r=t.prototype;function i(){}for(var n in i.prototype=e.prototype,t.prototype=new i,r)r.hasOwnProperty(n)&&(t.prototype[n]=r[n]);t.prototype.constructor=t,t.superClass=e}function P(t,e,r){if(t="prototype"in t?t.prototype:t,e="prototype"in e?e.prototype:e,Object.getOwnPropertyNames)for(var i=Object.getOwnPropertyNames(e),n=0;n<i.length;n++){var a=i[n];"constructor"!==a&&(r?null!=e[a]:null==t[a])&&(t[a]=e[a])}else k(t,e,r)}function A(t){return!!t&&("string"!==typeof t&&"number"===typeof t.length)}function M(t,e,r){if(t&&e)if(t.forEach&&t.forEach===l)t.forEach(e,r);else if(t.length===+t.length)for(var i=0,n=t.length;i<n;i++)e.call(r,t[i],i,t);else for(var a in t)t.hasOwnProperty(a)&&e.call(r,t[a],a,t)}function L(t,e,r){if(!t)return[];if(!e)return tt(t);if(t.map&&t.map===f)return t.map(e,r);for(var i=[],n=0,a=t.length;n<a;n++)i.push(e.call(r,t[n],n,t));return i}function D(t,e,r,i){if(t&&e){for(var n=0,a=t.length;n<a;n++)r=e.call(i,r,t[n],n,t);return r}}function z(t,e,r){if(!t)return[];if(!e)return tt(t);if(t.filter&&t.filter===u)return t.filter(e,r);for(var i=[],n=0,a=t.length;n<a;n++)e.call(r,t[n],n,t)&&i.push(t[n]);return i}function O(t,e,r){if(t&&e)for(var i=0,n=t.length;i<n;i++)if(e.call(r,t[i],i,t))return t[i]}function R(t){if(!t)return[];if(Object.keys)return Object.keys(t);var e=[];for(var r in t)t.hasOwnProperty(r)&&e.push(r);return e}function I(t,e){for(var r=[],i=2;i<arguments.length;i++)r[i-2]=arguments[i];return function(){return t.apply(e,r.concat(c.call(arguments)))}}var F=p&&Z(p.bind)?p.call.bind(p.bind):I;function B(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];return function(){return t.apply(this,e.concat(c.call(arguments)))}}function E(t){return Array.isArray?Array.isArray(t):"[object Array]"===s.call(t)}function Z(t){return"function"===typeof t}function j(t){return"string"===typeof t}function H(t){return"[object String]"===s.call(t)}function N(t){return"number"===typeof t}function W(t){var e=(0,i.Z)(t);return"function"===e||!!t&&"object"===e}function V(t){return!!a[s.call(t)]}function q(t){return!!o[s.call(t)]}function Y(t){return"object"===(0,i.Z)(t)&&"number"===typeof t.nodeType&&"object"===(0,i.Z)(t.ownerDocument)}function X(t){return null!=t.colorStops}function U(t){return null!=t.image}function $(t){return"[object RegExp]"===s.call(t)}function G(t){return t!==t}function Q(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];for(var r=0,i=t.length;r<i;r++)if(null!=t[r])return t[r]}function K(t,e){return null!=t?t:e}function J(t,e,r){return null!=t?t:null!=e?e:r}function tt(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];return c.apply(t,e)}function et(t){if("number"===typeof t)return[t,t,t,t];var e=t.length;return 2===e?[t[0],t[1],t[0],t[1]]:3===e?[t[0],t[1],t[2],t[1]]:t}function rt(t,e){if(!t)throw new Error(e)}function it(t){return null==t?null:"function"===typeof t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}var nt="__ec_primitive__";function at(t){t[nt]=!0}function ot(t){return t[nt]}var st=function(){function t(){this.data={}}return t.prototype["delete"]=function(t){var e=this.has(t);return e&&delete this.data[t],e},t.prototype.has=function(t){return this.data.hasOwnProperty(t)},t.prototype.get=function(t){return this.data[t]},t.prototype.set=function(t,e){return this.data[t]=e,this},t.prototype.keys=function(){return R(this.data)},t.prototype.forEach=function(t){var e=this.data;for(var r in e)e.hasOwnProperty(r)&&t(e[r],r)},t}(),ht="function"===typeof Map;function lt(){return ht?new Map:new st}var ut=function(){function t(e){var r=E(e);this.data=lt();var i=this;function n(t,e){r?i.set(t,e):i.set(e,t)}e instanceof t?e.each(n):e&&M(e,n)}return t.prototype.hasKey=function(t){return this.data.has(t)},t.prototype.get=function(t){return this.data.get(t)},t.prototype.set=function(t,e){return this.data.set(t,e),e},t.prototype.each=function(t,e){this.data.forEach((function(r,i){t.call(e,r,i)}))},t.prototype.keys=function(){var t=this.data.keys();return ht?Array.from(t):t},t.prototype.removeKey=function(t){this.data["delete"](t)},t}();function ct(t){return new ut(t)}function ft(t,e){for(var r=new t.constructor(t.length+e.length),i=0;i<t.length;i++)r[i]=t[i];var n=t.length;for(i=0;i<e.length;i++)r[i+n]=e[i];return r}function dt(t,e){var r;if(Object.create)r=Object.create(t);else{var i=function(){};i.prototype=t,r=new i}return e&&b(r,e),r}function pt(t){var e=t.style;e.webkitUserSelect="none",e.userSelect="none",e.webkitTapHighlightColor="rgba(0,0,0,0)",e["-webkit-touch-callout"]="none"}function vt(t,e){return t.hasOwnProperty(e)}function gt(){}var yt=180/Math.PI},77324:function(t,e,r){"use strict";function i(t,e){return null==t&&(t=0),null==e&&(e=0),[t,e]}function n(t,e){return t[0]=e[0],t[1]=e[1],t}function a(t){return[t[0],t[1]]}function o(t,e,r){return t[0]=e,t[1]=r,t}function s(t,e,r){return t[0]=e[0]+r[0],t[1]=e[1]+r[1],t}function h(t,e,r,i){return t[0]=e[0]+r[0]*i,t[1]=e[1]+r[1]*i,t}function l(t,e,r){return t[0]=e[0]-r[0],t[1]=e[1]-r[1],t}function u(t){return Math.sqrt(f(t))}r.r(e),r.d(e,{add:function(){return s},applyTransform:function(){return S},clone:function(){return a},copy:function(){return n},create:function(){return i},dist:function(){return x},distSquare:function(){return b},distance:function(){return m},distanceSquare:function(){return w},div:function(){return v},dot:function(){return g},len:function(){return u},lenSquare:function(){return f},length:function(){return c},lengthSquare:function(){return d},lerp:function(){return T},max:function(){return P},min:function(){return C},mul:function(){return p},negate:function(){return k},normalize:function(){return _},scale:function(){return y},scaleAndAdd:function(){return h},set:function(){return o},sub:function(){return l}});var c=/^(3491|4190|5903|6241)$/.test(r.j)?u:null;function f(t){return t[0]*t[0]+t[1]*t[1]}var d=/^(3491|4190|5903|6241)$/.test(r.j)?f:null;function p(t,e,r){return t[0]=e[0]*r[0],t[1]=e[1]*r[1],t}function v(t,e,r){return t[0]=e[0]/r[0],t[1]=e[1]/r[1],t}function g(t,e){return t[0]*e[0]+t[1]*e[1]}function y(t,e,r){return t[0]=e[0]*r,t[1]=e[1]*r,t}function _(t,e){var r=u(e);return 0===r?(t[0]=0,t[1]=0):(t[0]=e[0]/r,t[1]=e[1]/r),t}function m(t,e){return Math.sqrt((t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1]))}var x=/^(3491|4190|5903|6241)$/.test(r.j)?m:null;function w(t,e){return(t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1])}var b=/^(3491|4190|5903|6241)$/.test(r.j)?w:null;function k(t,e){return t[0]=-e[0],t[1]=-e[1],t}function T(t,e,r,i){return t[0]=e[0]+i*(r[0]-e[0]),t[1]=e[1]+i*(r[1]-e[1]),t}function S(t,e,r){var i=e[0],n=e[1];return t[0]=r[0]*i+r[2]*n+r[4],t[1]=r[1]*i+r[3]*n+r[5],t}function C(t,e,r){return t[0]=Math.min(e[0],r[0]),t[1]=Math.min(e[1],r[1]),t}function P(t,e,r){return t[0]=Math.max(e[0],r[0]),t[1]=Math.max(e[1],r[1]),t}},46886:function(t,e,r){"use strict";var i=r(61431),n=r(54893),a=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="compound",e}return(0,i.ZT)(e,t),e.prototype._updatePathDirty=function(){for(var t=this.shape.paths,e=this.shapeChanged(),r=0;r<t.length;r++)e=e||t[r].shapeChanged();e&&this.dirtyShape()},e.prototype.beforeBrush=function(){this._updatePathDirty();for(var t=this.shape.paths||[],e=this.getGlobalScale(),r=0;r<t.length;r++)t[r].path||t[r].createPathProxy(),t[r].path.setScale(e[0],e[1],t[r].segmentIgnoreThreshold)},e.prototype.buildPath=function(t,e){for(var r=e.paths||[],i=0;i<r.length;i++)r[i].buildPath(t,r[i].shape,!0)},e.prototype.afterBrush=function(){for(var t=this.shape.paths||[],e=0;e<t.length;e++)t[e].pathUpdated()},e.prototype.getBoundingRect=function(){return this._updatePathDirty.call(this),n.ZP.prototype.getBoundingRect.call(this)},e}(n.ZP);e["Z"]=/^(3491|4190|5903|6241)$/.test(r.j)?a:null},67365:function(t,e,r){"use strict";r.d(e,{ik:function(){return u},tj:function(){return l}});var i=r(61431),n=r(72981),a=r(43498),o=r(41239),s=r(64562),h="__zr_style_"+Math.round(10*Math.random()),l={shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,shadowColor:"#000",opacity:1,blend:"source-over"},u={style:{shadowBlur:!0,shadowOffsetX:!0,shadowOffsetY:!0,shadowColor:!0,opacity:!0}};l[h]=!0;var c=["z","z2","invisible"],f=["invisible"],d=function(t){function e(e){return t.call(this,e)||this}return(0,i.ZT)(e,t),e.prototype._init=function(e){for(var r=(0,o.keys)(e),i=0;i<r.length;i++){var n=r[i];"style"===n?this.useStyle(e[n]):t.prototype.attrKV.call(this,n,e[n])}this.style||this.useStyle({})},e.prototype.beforeBrush=function(){},e.prototype.afterBrush=function(){},e.prototype.innerBeforeBrush=function(){},e.prototype.innerAfterBrush=function(){},e.prototype.shouldBePainted=function(t,e,r,i){var n=this.transform;if(this.ignore||this.invisible||0===this.style.opacity||this.culling&&g(this,t,e)||n&&!n[0]&&!n[3])return!1;if(r&&this.__clipPaths)for(var a=0;a<this.__clipPaths.length;++a)if(this.__clipPaths[a].isZeroArea())return!1;if(i&&this.parent){var o=this.parent;while(o){if(o.ignore)return!1;o=o.parent}}return!0},e.prototype.contain=function(t,e){return this.rectContain(t,e)},e.prototype.traverse=function(t,e){t.call(e,this)},e.prototype.rectContain=function(t,e){var r=this.transformCoordToLocal(t,e),i=this.getBoundingRect();return i.contain(r[0],r[1])},e.prototype.getPaintRect=function(){var t=this._paintRect;if(!this._paintRect||this.__dirty){var e=this.transform,r=this.getBoundingRect(),i=this.style,n=i.shadowBlur||0,o=i.shadowOffsetX||0,s=i.shadowOffsetY||0;t=this._paintRect||(this._paintRect=new a.Z(0,0,0,0)),e?a.Z.applyTransform(t,r,e):t.copy(r),(n||o||s)&&(t.width+=2*n+Math.abs(o),t.height+=2*n+Math.abs(s),t.x=Math.min(t.x,t.x+o-n),t.y=Math.min(t.y,t.y+s-n));var h=this.dirtyRectTolerance;t.isZero()||(t.x=Math.floor(t.x-h),t.y=Math.floor(t.y-h),t.width=Math.ceil(t.width+1+2*h),t.height=Math.ceil(t.height+1+2*h))}return t},e.prototype.setPrevPaintRect=function(t){t?(this._prevPaintRect=this._prevPaintRect||new a.Z(0,0,0,0),this._prevPaintRect.copy(t)):this._prevPaintRect=null},e.prototype.getPrevPaintRect=function(){return this._prevPaintRect},e.prototype.animateStyle=function(t){return this.animate("style",t)},e.prototype.updateDuringAnimation=function(t){"style"===t?this.dirtyStyle():this.markRedraw()},e.prototype.attrKV=function(e,r){"style"!==e?t.prototype.attrKV.call(this,e,r):this.style?this.setStyle(r):this.useStyle(r)},e.prototype.setStyle=function(t,e){return"string"===typeof t?this.style[t]=e:(0,o.extend)(this.style,t),this.dirtyStyle(),this},e.prototype.dirtyStyle=function(t){t||this.markRedraw(),this.__dirty|=s.SE,this._rect&&(this._rect=null)},e.prototype.dirty=function(){this.dirtyStyle()},e.prototype.styleChanged=function(){return!!(this.__dirty&s.SE)},e.prototype.styleUpdated=function(){this.__dirty&=~s.SE},e.prototype.createStyle=function(t){return(0,o.createObject)(l,t)},e.prototype.useStyle=function(t){t[h]||(t=this.createStyle(t)),this.__inHover?this.__hoverStyle=t:this.style=t,this.dirtyStyle()},e.prototype.isStyleObject=function(t){return t[h]},e.prototype._innerSaveToNormal=function(e){t.prototype._innerSaveToNormal.call(this,e);var r=this._normalState;e.style&&!r.style&&(r.style=this._mergeStyle(this.createStyle(),this.style)),this._savePrimaryToNormal(e,r,c)},e.prototype._applyStateObj=function(e,r,i,n,a,s){t.prototype._applyStateObj.call(this,e,r,i,n,a,s);var h,l=!(r&&n);if(r&&r.style?a?n?h=r.style:(h=this._mergeStyle(this.createStyle(),i.style),this._mergeStyle(h,r.style)):(h=this._mergeStyle(this.createStyle(),n?this.style:i.style),this._mergeStyle(h,r.style)):l&&(h=i.style),h)if(a){var u=this.style;if(this.style=this.createStyle(l?{}:u),l)for(var d=(0,o.keys)(u),p=0;p<d.length;p++){var v=d[p];v in h&&(h[v]=h[v],this.style[v]=u[v])}var g=(0,o.keys)(h);for(p=0;p<g.length;p++){v=g[p];this.style[v]=this.style[v]}this._transitionState(e,{style:h},s,this.getAnimationStyleProps())}else this.useStyle(h);var y=this.__inHover?f:c;for(p=0;p<y.length;p++){v=y[p];r&&null!=r[v]?this[v]=r[v]:l&&null!=i[v]&&(this[v]=i[v])}},e.prototype._mergeStates=function(e){for(var r,i=t.prototype._mergeStates.call(this,e),n=0;n<e.length;n++){var a=e[n];a.style&&(r=r||{},this._mergeStyle(r,a.style))}return r&&(i.style=r),i},e.prototype._mergeStyle=function(t,e){return(0,o.extend)(t,e),t},e.prototype.getAnimationStyleProps=function(){return u},e.initDefaultProps=function(){var t=e.prototype;t.type="displayable",t.invisible=!1,t.z=0,t.z2=0,t.zlevel=0,t.culling=!1,t.cursor="pointer",t.rectHover=!1,t.incremental=!1,t._rect=null,t.dirtyRectTolerance=0,t.__dirty=s.YV|s.SE}(),e}(n.Z),p=new a.Z(0,0,0,0),v=new a.Z(0,0,0,0);function g(t,e,r){return p.copy(t.getBoundingRect()),t.transform&&p.applyTransform(t.transform),v.width=e,v.height=r,!p.intersect(v)}e["ZP"]=/^(3491|4190|5903|6241)$/.test(r.j)?d:null},6046:function(t,e,r){"use strict";var i=function(){function t(t){this.colorStops=t||[]}return t.prototype.addColorStop=function(t,e){this.colorStops.push({offset:t,color:e})},t}();e["Z"]=/^(3491|4190|5903|6241)$/.test(r.j)?i:null},81747:function(t,e,r){"use strict";var i=r(61431),n=r(41239),a=r(72981),o=r(43498),s=function(t){function e(e){var r=t.call(this)||this;return r.isGroup=!0,r._children=[],r.attr(e),r}return(0,i.ZT)(e,t),e.prototype.childrenRef=function(){return this._children},e.prototype.children=function(){return this._children.slice()},e.prototype.childAt=function(t){return this._children[t]},e.prototype.childOfName=function(t){for(var e=this._children,r=0;r<e.length;r++)if(e[r].name===t)return e[r]},e.prototype.childCount=function(){return this._children.length},e.prototype.add=function(t){return t&&t!==this&&t.parent!==this&&(this._children.push(t),this._doAdd(t)),this},e.prototype.addBefore=function(t,e){if(t&&t!==this&&t.parent!==this&&e&&e.parent===this){var r=this._children,i=r.indexOf(e);i>=0&&(r.splice(i,0,t),this._doAdd(t))}return this},e.prototype.replace=function(t,e){var r=n.indexOf(this._children,t);return r>=0&&this.replaceAt(e,r),this},e.prototype.replaceAt=function(t,e){var r=this._children,i=r[e];if(t&&t!==this&&t.parent!==this&&t!==i){r[e]=t,i.parent=null;var n=this.__zr;n&&i.removeSelfFromZr(n),this._doAdd(t)}return this},e.prototype._doAdd=function(t){t.parent&&t.parent.remove(t),t.parent=this;var e=this.__zr;e&&e!==t.__zr&&t.addSelfToZr(e),e&&e.refresh()},e.prototype.remove=function(t){var e=this.__zr,r=this._children,i=n.indexOf(r,t);return i<0||(r.splice(i,1),t.parent=null,e&&t.removeSelfFromZr(e),e&&e.refresh()),this},e.prototype.removeAll=function(){for(var t=this._children,e=this.__zr,r=0;r<t.length;r++){var i=t[r];e&&i.removeSelfFromZr(e),i.parent=null}return t.length=0,this},e.prototype.eachChild=function(t,e){for(var r=this._children,i=0;i<r.length;i++){var n=r[i];t.call(e,n,i)}return this},e.prototype.traverse=function(t,e){for(var r=0;r<this._children.length;r++){var i=this._children[r],n=t.call(e,i);i.isGroup&&!n&&i.traverse(t,e)}return this},e.prototype.addSelfToZr=function(e){t.prototype.addSelfToZr.call(this,e);for(var r=0;r<this._children.length;r++){var i=this._children[r];i.addSelfToZr(e)}},e.prototype.removeSelfFromZr=function(e){t.prototype.removeSelfFromZr.call(this,e);for(var r=0;r<this._children.length;r++){var i=this._children[r];i.removeSelfFromZr(e)}},e.prototype.getBoundingRect=function(t){for(var e=new o.Z(0,0,0,0),r=t||this._children,i=[],n=null,a=0;a<r.length;a++){var s=r[a];if(!s.ignore&&!s.invisible){var h=s.getBoundingRect(),l=s.getLocalTransform(i);l?(o.Z.applyTransform(e,h,l),n=n||e.clone(),n.union(e)):(n=n||h.clone(),n.union(h))}}return n||e},e}(a.Z);s.prototype.type="group",e["Z"]=/^(3491|4190|5903|6241)$/.test(r.j)?s:null},34714:function(t,e,r){"use strict";var i=r(61431),n=r(67365),a=r(43498),o=r(41239),s=(0,o.defaults)({x:0,y:0},n.tj),h={style:(0,o.defaults)({x:!0,y:!0,width:!0,height:!0,sx:!0,sy:!0,sWidth:!0,sHeight:!0},n.ik.style)};function l(t){return!!(t&&"string"!==typeof t&&t.width&&t.height)}var u=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return(0,i.ZT)(e,t),e.prototype.createStyle=function(t){return(0,o.createObject)(s,t)},e.prototype._getSize=function(t){var e=this.style,r=e[t];if(null!=r)return r;var i=l(e.image)?e.image:this.__image;if(!i)return 0;var n="width"===t?"height":"width",a=e[n];return null==a?i[t]:i[t]/i[n]*a},e.prototype.getWidth=function(){return this._getSize("width")},e.prototype.getHeight=function(){return this._getSize("height")},e.prototype.getAnimationStyleProps=function(){return h},e.prototype.getBoundingRect=function(){var t=this.style;return this._rect||(this._rect=new a.Z(t.x||0,t.y||0,this.getWidth(),this.getHeight())),this._rect},e}(n.ZP);u.prototype.type="image",e["ZP"]=/^(3491|4190|5903|6241)$/.test(r.j)?u:null},82777:function(t,e,r){"use strict";var i=r(61431),n=r(67365),a=r(43498),o=[],s=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.notClear=!0,e.incremental=!0,e._displayables=[],e._temporaryDisplayables=[],e._cursor=0,e}return(0,i.ZT)(e,t),e.prototype.traverse=function(t,e){t.call(e,this)},e.prototype.useStyle=function(){this.style={}},e.prototype.getCursor=function(){return this._cursor},e.prototype.innerAfterBrush=function(){this._cursor=this._displayables.length},e.prototype.clearDisplaybles=function(){this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.markRedraw(),this.notClear=!1},e.prototype.clearTemporalDisplayables=function(){this._temporaryDisplayables=[]},e.prototype.addDisplayable=function(t,e){e?this._temporaryDisplayables.push(t):this._displayables.push(t),this.markRedraw()},e.prototype.addDisplayables=function(t,e){e=e||!1;for(var r=0;r<t.length;r++)this.addDisplayable(t[r],e)},e.prototype.getDisplayables=function(){return this._displayables},e.prototype.getTemporalDisplayables=function(){return this._temporaryDisplayables},e.prototype.eachPendingDisplayable=function(t){for(var e=this._cursor;e<this._displayables.length;e++)t&&t(this._displayables[e]);for(e=0;e<this._temporaryDisplayables.length;e++)t&&t(this._temporaryDisplayables[e])},e.prototype.update=function(){this.updateTransform();for(var t=this._cursor;t<this._displayables.length;t++){var e=this._displayables[t];e.parent=this,e.update(),e.parent=null}for(t=0;t<this._temporaryDisplayables.length;t++){e=this._temporaryDisplayables[t];e.parent=this,e.update(),e.parent=null}},e.prototype.getBoundingRect=function(){if(!this._rect){for(var t=new a.Z(1/0,1/0,-1/0,-1/0),e=0;e<this._displayables.length;e++){var r=this._displayables[e],i=r.getBoundingRect().clone();r.needLocalTransform()&&i.applyTransform(r.getLocalTransform(o)),t.union(i)}this._rect=t}return this._rect},e.prototype.contain=function(t,e){var r=this.transformCoordToLocal(t,e),i=this.getBoundingRect();if(i.contain(r[0],r[1]))for(var n=0;n<this._displayables.length;n++){var a=this._displayables[n];if(a.contain(t,e))return!0}return!1},e}(n.ZP);e["Z"]=/^(3491|4190|5903|6241)$/.test(r.j)?s:null},1109:function(t,e,r){"use strict";var i=r(61431),n=r(6046),a=function(t){function e(e,r,i,n,a,o){var s=t.call(this,a)||this;return s.x=null==e?0:e,s.y=null==r?0:r,s.x2=null==i?1:i,s.y2=null==n?0:n,s.type="linear",s.global=o||!1,s}return(0,i.ZT)(e,t),e}(n.Z);e["Z"]=/^(3491|4190|5903|6241)$/.test(r.j)?a:null},54893:function(t,e,r){"use strict";r.d(e,{$t:function(){return D},ZP:function(){return I}});var i=r(3336),n=r(61431),a=r(67365),o=r(72136),s=r(64379),h=r(53996),l=r(85553),u=r(76213),c=r(54410),f=r(71824),d=o.Z.CMD,p=2*Math.PI,v=1e-4;function g(t,e){return Math.abs(t-e)<v}var y=[-1,-1,-1],_=[-1,-1];function m(){var t=_[0];_[0]=_[1],_[1]=t}function x(t,e,r,i,n,a,o,s,h,l){if(l>e&&l>i&&l>a&&l>s||l<e&&l<i&&l<a&&l<s)return 0;var u=c.kD(e,i,a,s,l,y);if(0===u)return 0;for(var f=0,d=-1,p=void 0,v=void 0,g=0;g<u;g++){var x=y[g],w=0===x||1===x?.5:1,b=c.af(t,r,n,o,x);b<h||(d<0&&(d=c.pP(e,i,a,s,_),_[1]<_[0]&&d>1&&m(),p=c.af(e,i,a,s,_[0]),d>1&&(v=c.af(e,i,a,s,_[1]))),2===d?x<_[0]?f+=p<e?w:-w:x<_[1]?f+=v<p?w:-w:f+=s<v?w:-w:x<_[0]?f+=p<e?w:-w:f+=s<p?w:-w)}return f}function w(t,e,r,i,n,a,o,s){if(s>e&&s>i&&s>a||s<e&&s<i&&s<a)return 0;var h=c.Jz(e,i,a,s,y);if(0===h)return 0;var l=c.QC(e,i,a);if(l>=0&&l<=1){for(var u=0,f=c.Zm(e,i,a,l),d=0;d<h;d++){var p=0===y[d]||1===y[d]?.5:1,v=c.Zm(t,r,n,y[d]);v<o||(y[d]<l?u+=f<e?p:-p:u+=a<f?p:-p)}return u}p=0===y[0]||1===y[0]?.5:1,v=c.Zm(t,r,n,y[0]);return v<o?0:a<e?p:-p}function b(t,e,r,i,n,a,o,s){if(s-=e,s>r||s<-r)return 0;var h=Math.sqrt(r*r-s*s);y[0]=-h,y[1]=h;var l=Math.abs(i-n);if(l<1e-4)return 0;if(l>=p-1e-4){i=0,n=p;var u=a?1:-1;return o>=y[0]+t&&o<=y[1]+t?u:0}if(i>n){var c=i;i=n,n=c}i<0&&(i+=p,n+=p);for(var f=0,d=0;d<2;d++){var v=y[d];if(v+t>o){var g=Math.atan2(s,v);u=a?1:-1;g<0&&(g=p+g),(g>=i&&g<=n||g+p>=i&&g+p<=n)&&(g>Math.PI/2&&g<1.5*Math.PI&&(u=-u),f+=u)}}return f}function k(t,e,r,i,n){for(var a,o,c=t.data,p=t.len(),v=0,y=0,_=0,m=0,k=0,T=0;T<p;){var S=c[T++],C=1===T;switch(S===d.M&&T>1&&(r||(v+=(0,f.Z)(y,_,m,k,i,n))),C&&(y=c[T],_=c[T+1],m=y,k=_),S){case d.M:m=c[T++],k=c[T++],y=m,_=k;break;case d.L:if(r){if(s.m(y,_,c[T],c[T+1],e,i,n))return!0}else v+=(0,f.Z)(y,_,c[T],c[T+1],i,n)||0;y=c[T++],_=c[T++];break;case d.C:if(r){if(h.m(y,_,c[T++],c[T++],c[T++],c[T++],c[T],c[T+1],e,i,n))return!0}else v+=x(y,_,c[T++],c[T++],c[T++],c[T++],c[T],c[T+1],i,n)||0;y=c[T++],_=c[T++];break;case d.Q:if(r){if(l.m(y,_,c[T++],c[T++],c[T],c[T+1],e,i,n))return!0}else v+=w(y,_,c[T++],c[T++],c[T],c[T+1],i,n)||0;y=c[T++],_=c[T++];break;case d.A:var P=c[T++],A=c[T++],M=c[T++],L=c[T++],D=c[T++],z=c[T++];T+=1;var O=!!(1-c[T++]);a=Math.cos(D)*M+P,o=Math.sin(D)*L+A,C?(m=a,k=o):v+=(0,f.Z)(y,_,a,o,i,n);var R=(i-P)*L/M+P;if(r){if(u.m(P,A,L,D,D+z,O,e,R,n))return!0}else v+=b(P,A,L,D,D+z,O,R,n);y=Math.cos(D+z)*M+P,_=Math.sin(D+z)*L+A;break;case d.R:m=y=c[T++],k=_=c[T++];var I=c[T++],F=c[T++];if(a=m+I,o=k+F,r){if(s.m(m,k,a,k,e,i,n)||s.m(a,k,a,o,e,i,n)||s.m(a,o,m,o,e,i,n)||s.m(m,o,m,k,e,i,n))return!0}else v+=(0,f.Z)(a,k,a,o,i,n),v+=(0,f.Z)(m,o,m,k,i,n);break;case d.Z:if(r){if(s.m(y,_,m,k,e,i,n))return!0}else v+=(0,f.Z)(y,_,m,k,i,n);y=m,_=k;break}}return r||g(_,k)||(v+=(0,f.Z)(y,_,m,k,i,n)||0),0!==v}function T(t,e,r){return k(t,0,!1,e,r)}function S(t,e,r,i){return k(t,e,!0,r,i)}var C=r(41239),P=r(38689),A=r(89753),M=r(64562),L=r(71270),D=(0,C.defaults)({fill:"#000",stroke:null,strokePercent:1,fillOpacity:1,strokeOpacity:1,lineDashOffset:0,lineWidth:1,lineCap:"butt",miterLimit:10,strokeNoScale:!1,strokeFirst:!1},a.tj),z={style:(0,C.defaults)({fill:!0,stroke:!0,strokePercent:!0,fillOpacity:!0,strokeOpacity:!0,lineDashOffset:!0,lineWidth:!0,miterLimit:!0},a.ik.style)},O=L.dN.concat(["invisible","culling","z","z2","zlevel","parent"]),R=function(t){function e(e){return t.call(this,e)||this}return(0,n.ZT)(e,t),e.prototype.update=function(){var r=this;t.prototype.update.call(this);var i=this.style;if(i.decal){var n=this._decalEl=this._decalEl||new e;n.buildPath===e.prototype.buildPath&&(n.buildPath=function(t){r.buildPath(t,r.shape)}),n.silent=!0;var a=n.style;for(var o in i)a[o]!==i[o]&&(a[o]=i[o]);a.fill=i.fill?i.decal:null,a.decal=null,a.shadowColor=null,i.strokeFirst&&(a.stroke=null);for(var s=0;s<O.length;++s)n[O[s]]=this[O[s]];n.__dirty|=M.YV}else this._decalEl&&(this._decalEl=null)},e.prototype.getDecalElement=function(){return this._decalEl},e.prototype._init=function(e){var r=(0,C.keys)(e);this.shape=this.getDefaultShape();var i=this.getDefaultStyle();i&&this.useStyle(i);for(var n=0;n<r.length;n++){var a=r[n],o=e[a];"style"===a?this.style?(0,C.extend)(this.style,o):this.useStyle(o):"shape"===a?(0,C.extend)(this.shape,o):t.prototype.attrKV.call(this,a,o)}this.style||this.useStyle({})},e.prototype.getDefaultStyle=function(){return null},e.prototype.getDefaultShape=function(){return{}},e.prototype.canBeInsideText=function(){return this.hasFill()},e.prototype.getInsideTextFill=function(){var t=this.style.fill;if("none"!==t){if((0,C.isString)(t)){var e=(0,P.lum)(t,0);return e>.5?A.vU:e>.2?A.iv:A.GD}if(t)return A.GD}return A.vU},e.prototype.getInsideTextStroke=function(t){var e=this.style.fill;if((0,C.isString)(e)){var r=this.__zr,i=!(!r||!r.isDarkMode()),n=(0,P.lum)(t,0)<A.Ak;if(i===n)return e}},e.prototype.buildPath=function(t,e,r){},e.prototype.pathUpdated=function(){this.__dirty&=~M.RH},e.prototype.getUpdatedPathProxy=function(t){return!this.path&&this.createPathProxy(),this.path.beginPath(),this.buildPath(this.path,this.shape,t),this.path},e.prototype.createPathProxy=function(){this.path=new o.Z(!1)},e.prototype.hasStroke=function(){var t=this.style,e=t.stroke;return!(null==e||"none"===e||!(t.lineWidth>0))},e.prototype.hasFill=function(){var t=this.style,e=t.fill;return null!=e&&"none"!==e},e.prototype.getBoundingRect=function(){var t=this._rect,e=this.style,r=!t;if(r){var i=!1;this.path||(i=!0,this.createPathProxy());var n=this.path;(i||this.__dirty&M.RH)&&(n.beginPath(),this.buildPath(n,this.shape,!1),this.pathUpdated()),t=n.getBoundingRect()}if(this._rect=t,this.hasStroke()&&this.path&&this.path.len()>0){var a=this._rectStroke||(this._rectStroke=t.clone());if(this.__dirty||r){a.copy(t);var o=e.strokeNoScale?this.getLineScale():1,s=e.lineWidth;if(!this.hasFill()){var h=this.strokeContainThreshold;s=Math.max(s,null==h?4:h)}o>1e-10&&(a.width+=s/o,a.height+=s/o,a.x-=s/o/2,a.y-=s/o/2)}return a}return t},e.prototype.contain=function(t,e){var r=this.transformCoordToLocal(t,e),i=this.getBoundingRect(),n=this.style;if(t=r[0],e=r[1],i.contain(t,e)){var a=this.path;if(this.hasStroke()){var o=n.lineWidth,s=n.strokeNoScale?this.getLineScale():1;if(s>1e-10&&(this.hasFill()||(o=Math.max(o,this.strokeContainThreshold)),S(a,o/s,t,e)))return!0}if(this.hasFill())return T(a,t,e)}return!1},e.prototype.dirtyShape=function(){this.__dirty|=M.RH,this._rect&&(this._rect=null),this._decalEl&&this._decalEl.dirtyShape(),this.markRedraw()},e.prototype.dirty=function(){this.dirtyStyle(),this.dirtyShape()},e.prototype.animateShape=function(t){return this.animate("shape",t)},e.prototype.updateDuringAnimation=function(t){"style"===t?this.dirtyStyle():"shape"===t?this.dirtyShape():this.markRedraw()},e.prototype.attrKV=function(e,r){"shape"===e?this.setShape(r):t.prototype.attrKV.call(this,e,r)},e.prototype.setShape=function(t,e){var r=this.shape;return r||(r=this.shape={}),"string"===typeof t?r[t]=e:(0,C.extend)(r,t),this.dirtyShape(),this},e.prototype.shapeChanged=function(){return!!(this.__dirty&M.RH)},e.prototype.createStyle=function(t){return(0,C.createObject)(D,t)},e.prototype._innerSaveToNormal=function(e){t.prototype._innerSaveToNormal.call(this,e);var r=this._normalState;e.shape&&!r.shape&&(r.shape=(0,C.extend)({},this.shape))},e.prototype._applyStateObj=function(e,r,n,a,o,s){t.prototype._applyStateObj.call(this,e,r,n,a,o,s);var h,l=!(r&&a);if(r&&r.shape?o?a?h=r.shape:(h=(0,C.extend)({},n.shape),(0,C.extend)(h,r.shape)):(h=(0,C.extend)({},a?this.shape:n.shape),(0,C.extend)(h,r.shape)):l&&(h=n.shape),h)if(o){this.shape=(0,C.extend)({},this.shape);for(var u={},c=(0,C.keys)(h),f=0;f<c.length;f++){var d=c[f];"object"===(0,i.Z)(h[d])?this.shape[d]=h[d]:u[d]=h[d]}this._transitionState(e,{shape:u},s)}else this.shape=h,this.dirtyShape()},e.prototype._mergeStates=function(e){for(var r,i=t.prototype._mergeStates.call(this,e),n=0;n<e.length;n++){var a=e[n];a.shape&&(r=r||{},this._mergeStyle(r,a.shape))}return r&&(i.shape=r),i},e.prototype.getAnimationStyleProps=function(){return z},e.prototype.isZeroArea=function(){return!1},e.extend=function(t){var r=function(e){function r(r){var i=e.call(this,r)||this;return t.init&&t.init.call(i,r),i}return(0,n.ZT)(r,e),r.prototype.getDefaultStyle=function(){return(0,C.clone)(t.style)},r.prototype.getDefaultShape=function(){return(0,C.clone)(t.shape)},r}(e);for(var i in t)"function"===typeof t[i]&&(r.prototype[i]=t[i]);return r},e.initDefaultProps=function(){var t=e.prototype;t.type="path",t.strokeContainThreshold=5,t.segmentIgnoreThreshold=0,t.subPixelOptimize=!1,t.autoBatch=!1,t.__dirty=M.YV|M.SE|M.RH}(),e}(a.ZP),I=R},39555:function(t,e,r){"use strict";var i=r(61431),n=r(6046),a=function(t){function e(e,r,i,n,a){var o=t.call(this,n)||this;return o.x=null==e?.5:e,o.y=null==r?.5:r,o.r=null==i?.5:i,o.type="radial",o.global=a||!1,o}return(0,i.ZT)(e,t),e}(n.Z);e["Z"]=/^(3491|4190|5903|6241)$/.test(r.j)?a:null},74969:function(t,e,r){"use strict";var i=r(61431),n=r(67365),a=r(73048),o=r(54893),s=r(41239),h=r(11693),l=(0,s.defaults)({strokeFirst:!0,font:h.Uo,x:0,y:0,textAlign:"left",textBaseline:"top",miterLimit:2},o.$t),u=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return(0,i.ZT)(e,t),e.prototype.hasStroke=function(){var t=this.style,e=t.stroke;return null!=e&&"none"!==e&&t.lineWidth>0},e.prototype.hasFill=function(){var t=this.style,e=t.fill;return null!=e&&"none"!==e},e.prototype.createStyle=function(t){return(0,s.createObject)(l,t)},e.prototype.setBoundingRect=function(t){this._rect=t},e.prototype.getBoundingRect=function(){var t=this.style;if(!this._rect){var e=t.text;null!=e?e+="":e="";var r=(0,a.lP)(e,t.font,t.textAlign,t.textBaseline);if(r.x+=t.x||0,r.y+=t.y||0,this.hasStroke()){var i=t.lineWidth;r.x-=i/2,r.y-=i/2,r.width+=i,r.height+=i}this._rect=r}return this._rect},e.initDefaultProps=function(){var t=e.prototype;t.dirtyRectTolerance=10}(),e}(n.ZP);u.prototype.type="tspan",e["Z"]=/^(3491|4190|5903|6241)$/.test(r.j)?u:null},99743:function(t,e,r){"use strict";r.d(e,{VG:function(){return x},Y1:function(){return b}});var i=r(61431),n=r(65242),a=r(74969),o=r(41239),s=r(73048),h=r(34714),l=r(18732),u=r(43498),c=r(67365),f=r(11693),d={fill:"#000"},p=2,v={style:(0,o.defaults)({fill:!0,stroke:!0,fillOpacity:!0,strokeOpacity:!0,lineWidth:!0,fontSize:!0,lineHeight:!0,width:!0,height:!0,textShadowColor:!0,textShadowBlur:!0,textShadowOffsetX:!0,textShadowOffsetY:!0,backgroundColor:!0,padding:!0,borderColor:!0,borderWidth:!0,borderRadius:!0},c.ik.style)},g=function(t){function e(e){var r=t.call(this)||this;return r.type="text",r._children=[],r._defaultStyle=d,r.attr(e),r}return(0,i.ZT)(e,t),e.prototype.childrenRef=function(){return this._children},e.prototype.update=function(){t.prototype.update.call(this),this.styleChanged()&&this._updateSubTexts();for(var e=0;e<this._children.length;e++){var r=this._children[e];r.zlevel=this.zlevel,r.z=this.z,r.z2=this.z2,r.culling=this.culling,r.cursor=this.cursor,r.invisible=this.invisible}},e.prototype.updateTransform=function(){var e=this.innerTransformable;e?(e.updateTransform(),e.transform&&(this.transform=e.transform)):t.prototype.updateTransform.call(this)},e.prototype.getLocalTransform=function(e){var r=this.innerTransformable;return r?r.getLocalTransform(e):t.prototype.getLocalTransform.call(this,e)},e.prototype.getComputedTransform=function(){return this.__hostTarget&&(this.__hostTarget.getComputedTransform(),this.__hostTarget.updateInnerText(!0)),t.prototype.getComputedTransform.call(this)},e.prototype._updateSubTexts=function(){this._childCursor=0,k(this.style),this.style.rich?this._updateRichTexts():this._updatePlainTexts(),this._children.length=this._childCursor,this.styleUpdated()},e.prototype.addSelfToZr=function(e){t.prototype.addSelfToZr.call(this,e);for(var r=0;r<this._children.length;r++)this._children[r].__zr=e},e.prototype.removeSelfFromZr=function(e){t.prototype.removeSelfFromZr.call(this,e);for(var r=0;r<this._children.length;r++)this._children[r].__zr=null},e.prototype.getBoundingRect=function(){if(this.styleChanged()&&this._updateSubTexts(),!this._rect){for(var t=new u.Z(0,0,0,0),e=this._children,r=[],i=null,n=0;n<e.length;n++){var a=e[n],o=a.getBoundingRect(),s=a.getLocalTransform(r);s?(t.copy(o),t.applyTransform(s),i=i||t.clone(),i.union(t)):(i=i||o.clone(),i.union(o))}this._rect=i||t}return this._rect},e.prototype.setDefaultTextStyle=function(t){this._defaultStyle=t||d},e.prototype.setTextContent=function(t){0},e.prototype._mergeStyle=function(t,e){if(!e)return t;var r=e.rich,i=t.rich||r&&{};return(0,o.extend)(t,e),r&&i?(this._mergeRich(i,r),t.rich=i):i&&(t.rich=i),t},e.prototype._mergeRich=function(t,e){for(var r=(0,o.keys)(e),i=0;i<r.length;i++){var n=r[i];t[n]=t[n]||{},(0,o.extend)(t[n],e[n])}},e.prototype.getAnimationStyleProps=function(){return v},e.prototype._getOrCreateChild=function(t){var e=this._children[this._childCursor];return e&&e instanceof t||(e=new t),this._children[this._childCursor++]=e,e.__zr=this.__zr,e.parent=this,e},e.prototype._updatePlainTexts=function(){var t=this.style,e=t.font||f.Uo,r=t.padding,i=A(t),o=(0,n.NY)(i,t),h=M(t),l=!!t.backgroundColor,c=o.outerHeight,d=o.outerWidth,v=o.contentWidth,g=o.lines,y=o.lineHeight,_=this._defaultStyle,m=t.x||0,x=t.y||0,b=t.align||_.align||"left",k=t.verticalAlign||_.verticalAlign||"top",T=m,L=(0,s.mU)(x,o.contentHeight,k);if(h||r){var D=(0,s.M3)(m,d,b),z=(0,s.mU)(x,c,k);h&&this._renderBackground(t,t,D,z,d,c)}L+=y/2,r&&(T=P(m,b,r),"top"===k?L+=r[0]:"bottom"===k&&(L-=r[2]));for(var O=0,R=!1,I=(C("fill"in t?t.fill:(R=!0,_.fill))),F=(S("stroke"in t?t.stroke:l||_.autoStroke&&!R?null:(O=p,_.stroke))),B=t.textShadowBlur>0,E=null!=t.width&&("truncate"===t.overflow||"break"===t.overflow||"breakAll"===t.overflow),Z=o.calculatedLineHeight,j=0;j<g.length;j++){var H=this._getOrCreateChild(a.Z),N=H.createStyle();H.useStyle(N),N.text=g[j],N.x=T,N.y=L,b&&(N.textAlign=b),N.textBaseline="middle",N.opacity=t.opacity,N.strokeFirst=!0,B&&(N.shadowBlur=t.textShadowBlur||0,N.shadowColor=t.textShadowColor||"transparent",N.shadowOffsetX=t.textShadowOffsetX||0,N.shadowOffsetY=t.textShadowOffsetY||0),N.stroke=F,N.fill=I,F&&(N.lineWidth=t.lineWidth||O,N.lineDash=t.lineDash,N.lineDashOffset=t.lineDashOffset||0),N.font=e,w(N,t),L+=y,E&&H.setBoundingRect(new u.Z((0,s.M3)(N.x,t.width,N.textAlign),(0,s.mU)(N.y,Z,N.textBaseline),v,Z))}},e.prototype._updateRichTexts=function(){var t=this.style,e=A(t),r=(0,n.$F)(e,t),i=r.width,a=r.outerWidth,o=r.outerHeight,h=t.padding,l=t.x||0,u=t.y||0,c=this._defaultStyle,f=t.align||c.align,d=t.verticalAlign||c.verticalAlign,p=(0,s.M3)(l,a,f),v=(0,s.mU)(u,o,d),g=p,y=v;h&&(g+=h[3],y+=h[0]);var _=g+i;M(t)&&this._renderBackground(t,t,p,v,a,o);for(var m=!!t.backgroundColor,x=0;x<r.lines.length;x++){var w=r.lines[x],b=w.tokens,k=b.length,T=w.lineHeight,S=w.width,C=0,P=g,L=_,D=k-1,z=void 0;while(C<k&&(z=b[C],!z.align||"left"===z.align))this._placeToken(z,t,T,y,P,"left",m),S-=z.width,P+=z.width,C++;while(D>=0&&(z=b[D],"right"===z.align))this._placeToken(z,t,T,y,L,"right",m),S-=z.width,L-=z.width,D--;P+=(i-(P-g)-(_-L)-S)/2;while(C<=D)z=b[C],this._placeToken(z,t,T,y,P+z.width/2,"center",m),P+=z.width,C++;y+=T}},e.prototype._placeToken=function(t,e,r,i,n,h,l){var c=e.rich[t.styleName]||{};c.text=t.text;var d=t.verticalAlign,v=i+r/2;"top"===d?v=i+t.height/2:"bottom"===d&&(v=i+r-t.height/2);var g=!t.isLineHolder&&M(c);g&&this._renderBackground(c,e,"right"===h?n-t.width:"center"===h?n-t.width/2:n,v-t.height/2,t.width,t.height);var y=!!c.backgroundColor,_=t.textPadding;_&&(n=P(n,h,_),v-=t.height/2-_[0]-t.innerHeight/2);var m=this._getOrCreateChild(a.Z),x=m.createStyle();m.useStyle(x);var b=this._defaultStyle,k=!1,T=0,A=C("fill"in c?c.fill:"fill"in e?e.fill:(k=!0,b.fill)),L=S("stroke"in c?c.stroke:"stroke"in e?e.stroke:y||l||b.autoStroke&&!k?null:(T=p,b.stroke)),D=c.textShadowBlur>0||e.textShadowBlur>0;x.text=t.text,x.x=n,x.y=v,D&&(x.shadowBlur=c.textShadowBlur||e.textShadowBlur||0,x.shadowColor=c.textShadowColor||e.textShadowColor||"transparent",x.shadowOffsetX=c.textShadowOffsetX||e.textShadowOffsetX||0,x.shadowOffsetY=c.textShadowOffsetY||e.textShadowOffsetY||0),x.textAlign=h,x.textBaseline="middle",x.font=t.font||f.Uo,x.opacity=(0,o.retrieve3)(c.opacity,e.opacity,1),w(x,c),L&&(x.lineWidth=(0,o.retrieve3)(c.lineWidth,e.lineWidth,T),x.lineDash=(0,o.retrieve2)(c.lineDash,e.lineDash),x.lineDashOffset=e.lineDashOffset||0,x.stroke=L),A&&(x.fill=A);var z=t.contentWidth,O=t.contentHeight;m.setBoundingRect(new u.Z((0,s.M3)(x.x,z,x.textAlign),(0,s.mU)(x.y,O,x.textBaseline),z,O))},e.prototype._renderBackground=function(t,e,r,i,n,a){var s,u,c=t.backgroundColor,f=t.borderWidth,d=t.borderColor,p=c&&c.image,v=c&&!p,g=t.borderRadius,y=this;if(v||t.lineHeight||f&&d){s=this._getOrCreateChild(l.Z),s.useStyle(s.createStyle()),s.style.fill=null;var _=s.shape;_.x=r,_.y=i,_.width=n,_.height=a,_.r=g,s.dirtyShape()}if(v){var m=s.style;m.fill=c||null,m.fillOpacity=(0,o.retrieve2)(t.fillOpacity,1)}else if(p){u=this._getOrCreateChild(h.ZP),u.onload=function(){y.dirtyStyle()};var x=u.style;x.image=c.image,x.x=r,x.y=i,x.width=n,x.height=a}if(f&&d){m=s.style;m.lineWidth=f,m.stroke=d,m.strokeOpacity=(0,o.retrieve2)(t.strokeOpacity,1),m.lineDash=t.borderDash,m.lineDashOffset=t.borderDashOffset||0,s.strokeContainThreshold=0,s.hasFill()&&s.hasStroke()&&(m.strokeFirst=!0,m.lineWidth*=2)}var w=(s||u).style;w.shadowBlur=t.shadowBlur||0,w.shadowColor=t.shadowColor||"transparent",w.shadowOffsetX=t.shadowOffsetX||0,w.shadowOffsetY=t.shadowOffsetY||0,w.opacity=(0,o.retrieve3)(t.opacity,e.opacity,1)},e.makeFont=function(t){var e="";return b(t)&&(e=[t.fontStyle,t.fontWeight,x(t.fontSize),t.fontFamily||"sans-serif"].join(" ")),e&&(0,o.trim)(e)||t.textFont||t.font},e}(c.ZP),y={left:!0,right:1,center:1},_={top:1,bottom:1,middle:1},m=["fontStyle","fontWeight","fontSize","fontFamily"];function x(t){return"string"!==typeof t||-1===t.indexOf("px")&&-1===t.indexOf("rem")&&-1===t.indexOf("em")?isNaN(+t)?f.n5+"px":t+"px":t}function w(t,e){for(var r=0;r<m.length;r++){var i=m[r],n=e[i];null!=n&&(t[i]=n)}}function b(t){return null!=t.fontSize||t.fontFamily||t.fontWeight}function k(t){return T(t),(0,o.each)(t.rich,T),t}function T(t){if(t){t.font=g.makeFont(t);var e=t.align;"middle"===e&&(e="center"),t.align=null==e||y[e]?e:"left";var r=t.verticalAlign;"center"===r&&(r="middle"),t.verticalAlign=null==r||_[r]?r:"top";var i=t.padding;i&&(t.padding=(0,o.normalizeCssArray)(t.padding))}}function S(t,e){return null==t||e<=0||"transparent"===t||"none"===t?null:t.image||t.colorStops?"#000":t}function C(t){return null==t||"none"===t?null:t.image||t.colorStops?"#000":t}function P(t,e,r){return"right"===e?t-r[1]:"center"===e?t+r[3]/2-r[1]/2:t+r[3]}function A(t){var e=t.text;return null!=e&&(e+=""),e}function M(t){return!!(t.backgroundColor||t.lineHeight||t.borderWidth&&t.borderColor)}e["ZP"]=/^(3491|4190|5903|6241)$/.test(r.j)?g:null},64562:function(t,e,r){"use strict";r.d(e,{RH:function(){return a},SE:function(){return n},YV:function(){return i}});var i=1,n=2,a=4},63315:function(t,e,r){"use strict";r.d(e,{Gq:function(){return s},ko:function(){return o},v5:function(){return l}});var i=r(88339);if(/^(3491|4190|5903|6241)$/.test(r.j))var n=r(11693);var a=new i.ZP(50);function o(t){if("string"===typeof t){var e=a.get(t);return e&&e.image}return t}function s(t,e,r,i,o){if(t){if("string"===typeof t){if(e&&e.__zrImageSrc===t||!r)return e;var s=a.get(t),u={hostEl:r,cb:i,cbPayload:o};return s?(e=s.image,!l(e)&&s.pending.push(u)):(e=n.qW.loadImage(t,h,h),e.__zrImageSrc=t,a.put(t,e.__cachedImgObj={image:e,pending:[u]})),e}return t}return e}function h(){var t=this.__cachedImgObj;this.onload=this.onerror=this.__cachedImgObj=null;for(var e=0;e<t.pending.length;e++){var r=t.pending[e],i=r.cb;i&&i(this,r.cbPayload),r.hostEl.dirty()}t.pending.length=0}function l(t){return t&&t.width&&t.height}},65242:function(t,e,r){"use strict";if(r.d(e,{$F:function(){return v},NY:function(){return c},aF:function(){return s}}),/^(3491|4190|5903|6241)$/.test(r.j))var i=r(63315);var n=r(41239);if(/^(3491|4190|5903|6241)$/.test(r.j))var a=r(73048);var o=/\{([a-zA-Z0-9_]+)\|([^}]*)\}/g;function s(t,e,r,i,n){if(!e)return"";var a=(t+"").split("\n");n=h(e,r,i,n);for(var o=0,s=a.length;o<s;o++)a[o]=l(a[o],n);return a.join("\n")}function h(t,e,r,i){i=i||{};var o=(0,n.extend)({},i);o.font=e,r=(0,n.retrieve2)(r,"..."),o.maxIterations=(0,n.retrieve2)(i.maxIterations,2);var s=o.minChar=(0,n.retrieve2)(i.minChar,0);o.cnCharWidth=(0,a.dz)("国",e);var h=o.ascCharWidth=(0,a.dz)("a",e);o.placeholder=(0,n.retrieve2)(i.placeholder,"");for(var l=t=Math.max(0,t-1),u=0;u<s&&l>=h;u++)l-=h;var c=(0,a.dz)(r,e);return c>l&&(r="",c=0),l=t-c,o.ellipsis=r,o.ellipsisWidth=c,o.contentWidth=l,o.containerWidth=t,o}function l(t,e){var r=e.containerWidth,i=e.font,n=e.contentWidth;if(!r)return"";var o=(0,a.dz)(t,i);if(o<=r)return t;for(var s=0;;s++){if(o<=n||s>=e.maxIterations){t+=e.ellipsis;break}var h=0===s?u(t,n,e.ascCharWidth,e.cnCharWidth):o>0?Math.floor(t.length*n/o):0;t=t.substr(0,h),o=(0,a.dz)(t,i)}return""===t&&(t=e.placeholder),t}function u(t,e,r,i){for(var n=0,a=0,o=t.length;a<o&&n<e;a++){var s=t.charCodeAt(a);n+=0<=s&&s<=127?r:i}return a}function c(t,e){null!=t&&(t+="");var r,i=e.overflow,o=e.padding,s=e.font,u="truncate"===i,c=(0,a.Dp)(s),f=(0,n.retrieve2)(e.lineHeight,c),d=!!e.backgroundColor,p="truncate"===e.lineOverflow,v=e.width;r=null==v||"break"!==i&&"breakAll"!==i?t?t.split("\n"):[]:t?x(t,e.font,v,"breakAll"===i,0).lines:[];var g=r.length*f,y=(0,n.retrieve2)(e.height,g);if(g>y&&p){var _=Math.floor(y/f);r=r.slice(0,_)}if(t&&u&&null!=v)for(var m=h(v,s,e.ellipsis,{minChar:e.truncateMinChar,placeholder:e.placeholder}),w=0;w<r.length;w++)r[w]=l(r[w],m);var b=y,k=0;for(w=0;w<r.length;w++)k=Math.max((0,a.dz)(r[w],s),k);null==v&&(v=k);var T=k;return o&&(b+=o[0]+o[2],T+=o[1]+o[3],v+=o[1]+o[3]),d&&(T=v),{lines:r,height:y,outerWidth:T,outerHeight:b,lineHeight:f,calculatedLineHeight:c,contentWidth:k,contentHeight:g,width:v}}var f=function(){function t(){}return t}(),d=function(){function t(t){this.tokens=[],t&&(this.tokens=t)}return t}(),p=function(){function t(){this.width=0,this.height=0,this.contentWidth=0,this.contentHeight=0,this.outerWidth=0,this.outerHeight=0,this.lines=[]}return t}();function v(t,e){var r=new p;if(null!=t&&(t+=""),!t)return r;var h,l=e.width,u=e.height,c=e.overflow,f="break"!==c&&"breakAll"!==c||null==l?null:{width:l,accumWidth:0,breakAll:"breakAll"===c},d=o.lastIndex=0;while(null!=(h=o.exec(t))){var v=h.index;v>d&&g(r,t.substring(d,v),e,f),g(r,h[2],e,f,h[1]),d=o.lastIndex}d<t.length&&g(r,t.substring(d,t.length),e,f);var y=[],_=0,m=0,x=e.padding,w="truncate"===c,b="truncate"===e.lineOverflow;function k(t,e,r){t.width=e,t.lineHeight=r,_+=r,m=Math.max(m,e)}t:for(var T=0;T<r.lines.length;T++){for(var S=r.lines[T],C=0,P=0,A=0;A<S.tokens.length;A++){var M=S.tokens[A],L=M.styleName&&e.rich[M.styleName]||{},D=M.textPadding=L.padding,z=D?D[1]+D[3]:0,O=M.font=L.font||e.font;M.contentHeight=(0,a.Dp)(O);var R=(0,n.retrieve2)(L.height,M.contentHeight);if(M.innerHeight=R,D&&(R+=D[0]+D[2]),M.height=R,M.lineHeight=(0,n.retrieve3)(L.lineHeight,e.lineHeight,R),M.align=L&&L.align||e.align,M.verticalAlign=L&&L.verticalAlign||"middle",b&&null!=u&&_+M.lineHeight>u){A>0?(S.tokens=S.tokens.slice(0,A),k(S,P,C),r.lines=r.lines.slice(0,T+1)):r.lines=r.lines.slice(0,T);break t}var I=L.width,F=null==I||"auto"===I;if("string"===typeof I&&"%"===I.charAt(I.length-1))M.percentWidth=I,y.push(M),M.contentWidth=(0,a.dz)(M.text,O);else{if(F){var B=L.backgroundColor,E=B&&B.image;E&&(E=i.ko(E),i.v5(E)&&(M.width=Math.max(M.width,E.width*R/E.height)))}var Z=w&&null!=l?l-P:null;null!=Z&&Z<M.width?!F||Z<z?(M.text="",M.width=M.contentWidth=0):(M.text=s(M.text,Z-z,O,e.ellipsis,{minChar:e.truncateMinChar}),M.width=M.contentWidth=(0,a.dz)(M.text,O)):M.contentWidth=(0,a.dz)(M.text,O)}M.width+=z,P+=M.width,L&&(C=Math.max(C,M.lineHeight))}k(S,P,C)}r.outerWidth=r.width=(0,n.retrieve2)(l,m),r.outerHeight=r.height=(0,n.retrieve2)(u,_),r.contentHeight=_,r.contentWidth=m,x&&(r.outerWidth+=x[1]+x[3],r.outerHeight+=x[0]+x[2]);for(T=0;T<y.length;T++){M=y[T];var j=M.percentWidth;M.width=parseInt(j,10)/100*r.width}return r}function g(t,e,r,i,n){var o,s,h=""===e,l=n&&r.rich[n]||{},u=t.lines,c=l.font||r.font,p=!1;if(i){var v=l.padding,g=v?v[1]+v[3]:0;if(null!=l.width&&"auto"!==l.width){var y=(0,a.GM)(l.width,i.width)+g;u.length>0&&y+i.accumWidth>i.width&&(o=e.split("\n"),p=!0),i.accumWidth=y}else{var _=x(e,c,i.width,i.breakAll,i.accumWidth);i.accumWidth=_.accumWidth+g,s=_.linesWidths,o=_.lines}}else o=e.split("\n");for(var m=0;m<o.length;m++){var w=o[m],b=new f;if(b.styleName=n,b.text=w,b.isLineHolder=!w&&!h,"number"===typeof l.width?b.width=l.width:b.width=s?s[m]:(0,a.dz)(w,c),m||p)u.push(new d([b]));else{var k=(u[u.length-1]||(u[0]=new d)).tokens,T=k.length;1===T&&k[0].isLineHolder?k[0]=b:(w||!T||h)&&k.push(b)}}}function y(t){var e=t.charCodeAt(0);return e>=32&&e<=591||e>=880&&e<=4351||e>=4608&&e<=5119||e>=7680&&e<=8303}var _=(0,n.reduce)(",&?/;] ".split(""),(function(t,e){return t[e]=!0,t}),{});function m(t){return!y(t)||!!_[t]}function x(t,e,r,i,n){for(var o=[],s=[],h="",l="",u=0,c=0,f=0;f<t.length;f++){var d=t.charAt(f);if("\n"!==d){var p=(0,a.dz)(d,e),v=!i&&!m(d);(o.length?c+p>r:n+c+p>r)?c?(h||l)&&(v?(h||(h=l,l="",u=0,c=u),o.push(h),s.push(c-u),l+=d,u+=p,h="",c=u):(l&&(h+=l,l="",u=0),o.push(h),s.push(c),h=d,c=p)):v?(o.push(l),s.push(u),l=d,u=p):(o.push(d),s.push(p)):(c+=p,v?(l+=d,u+=p):(l&&(h+=l,l="",u=0),h+=d))}else l&&(h+=l,c+=u),o.push(h),s.push(c),h="",l="",u=0,c=0}return o.length||h||(h=t,l="",u=0),l&&(h+=l),h&&(o.push(h),s.push(c)),1===o.length&&(c+=n),{accumWidth:c,lines:o,linesWidths:s}}},89434:function(t,e,r){"use strict";if(r.d(e,{L:function(){return n}}),/^(3491|4190|5903|6241)$/.test(r.j))var i=r(9489);function n(t,e,r){var n=e.smooth,a=e.points;if(a&&a.length>=2){if(n){var o=(0,i.Z)(a,n,r,e.smoothConstraint);t.moveTo(a[0][0],a[0][1]);for(var s=a.length,h=0;h<(r?s:s-1);h++){var l=o[2*h],u=o[2*h+1],c=a[(h+1)%s];t.bezierCurveTo(l[0],l[1],u[0],u[1],c[0],c[1])}}else{t.moveTo(a[0][0],a[0][1]);h=1;for(var f=a.length;h<f;h++)t.lineTo(a[h][0],a[h][1])}r&&t.closePath()}}},9489:function(t,e,r){"use strict";if(r.d(e,{Z:function(){return n}}),/^(3491|4190|5903|6241)$/.test(r.j))var i=r(77324);function n(t,e,r,n){var a,o,s,h,l=[],u=[],c=[],f=[];if(n){s=[1/0,1/0],h=[-1/0,-1/0];for(var d=0,p=t.length;d<p;d++)(0,i.min)(s,s,t[d]),(0,i.max)(h,h,t[d]);(0,i.min)(s,s,n[0]),(0,i.max)(h,h,n[1])}for(d=0,p=t.length;d<p;d++){var v=t[d];if(r)a=t[d?d-1:p-1],o=t[(d+1)%p];else{if(0===d||d===p-1){l.push((0,i.clone)(t[d]));continue}a=t[d-1],o=t[d+1]}(0,i.sub)(u,o,a),(0,i.scale)(u,u,e);var g=(0,i.distance)(v,a),y=(0,i.distance)(v,o),_=g+y;0!==_&&(g/=_,y/=_),(0,i.scale)(c,u,-g),(0,i.scale)(f,u,y);var m=(0,i.add)([],v,c),x=(0,i.add)([],v,f);n&&((0,i.max)(m,m,s),(0,i.min)(m,m,h),(0,i.max)(x,x,s),(0,i.min)(x,x,h)),l.push(m),l.push(x)}return r&&l.push(l.shift()),l}},56059:function(t,e,r){"use strict";r.d(e,{Pw:function(){return a},_3:function(){return n},vu:function(){return o}});var i=Math.round;function n(t,e,r){if(e){var n=e.x1,a=e.x2,s=e.y1,h=e.y2;t.x1=n,t.x2=a,t.y1=s,t.y2=h;var l=r&&r.lineWidth;return l?(i(2*n)===i(2*a)&&(t.x1=t.x2=o(n,l,!0)),i(2*s)===i(2*h)&&(t.y1=t.y2=o(s,l,!0)),t):t}}function a(t,e,r){if(e){var i=e.x,n=e.y,a=e.width,s=e.height;t.x=i,t.y=n,t.width=a,t.height=s;var h=r&&r.lineWidth;return h?(t.x=o(i,h,!0),t.y=o(n,h,!0),t.width=Math.max(o(i+a,h,!1)-t.x,0===a?0:1),t.height=Math.max(o(n+s,h,!1)-t.y,0===s?0:1),t):t}}function o(t,e,r){if(!e)return t;var n=i(2*t);return(n+i(e))%2===0?n/2:(n+(r?1:-1))/2}},49938:function(t,e,r){"use strict";var i=r(61431),n=r(54893),a=function(){function t(){this.cx=0,this.cy=0,this.r=0,this.startAngle=0,this.endAngle=2*Math.PI,this.clockwise=!0}return t}(),o=function(t){function e(e){return t.call(this,e)||this}return(0,i.ZT)(e,t),e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new a},e.prototype.buildPath=function(t,e){var r=e.cx,i=e.cy,n=Math.max(e.r,0),a=e.startAngle,o=e.endAngle,s=e.clockwise,h=Math.cos(a),l=Math.sin(a);t.moveTo(h*n+r,l*n+i),t.arc(r,i,n,a,o,!s)},e}(n.ZP);o.prototype.type="arc",e["Z"]=/^(3491|4190|5903|6241)$/.test(r.j)?o:null},34003:function(t,e,r){"use strict";var i=r(61431),n=r(54893),a=r(77324),o=r(54410),s=[],h=function(){function t(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.cpx1=0,this.cpy1=0,this.percent=1}return t}();function l(t,e,r){var i=t.cpx2,n=t.cpy2;return null!=i||null!=n?[(r?o.X_:o.af)(t.x1,t.cpx1,t.cpx2,t.x2,e),(r?o.X_:o.af)(t.y1,t.cpy1,t.cpy2,t.y2,e)]:[(r?o.AZ:o.Zm)(t.x1,t.cpx1,t.x2,e),(r?o.AZ:o.Zm)(t.y1,t.cpy1,t.y2,e)]}var u=function(t){function e(e){return t.call(this,e)||this}return(0,i.ZT)(e,t),e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new h},e.prototype.buildPath=function(t,e){var r=e.x1,i=e.y1,n=e.x2,a=e.y2,h=e.cpx1,l=e.cpy1,u=e.cpx2,c=e.cpy2,f=e.percent;0!==f&&(t.moveTo(r,i),null==u||null==c?(f<1&&((0,o.Lx)(r,h,n,f,s),h=s[1],n=s[2],(0,o.Lx)(i,l,a,f,s),l=s[1],a=s[2]),t.quadraticCurveTo(h,l,n,a)):(f<1&&((0,o.Vz)(r,h,u,n,f,s),h=s[1],u=s[2],n=s[3],(0,o.Vz)(i,l,c,a,f,s),l=s[1],c=s[2],a=s[3]),t.bezierCurveTo(h,l,u,c,n,a)))},e.prototype.pointAt=function(t){return l(this.shape,t,!1)},e.prototype.tangentAt=function(t){var e=l(this.shape,t,!0);return a.normalize(e,e)},e}(n.ZP);u.prototype.type="bezier-curve",e["Z"]=/^(3491|4190|5903|6241)$/.test(r.j)?u:null},38963:function(t,e,r){"use strict";var i=r(61431),n=r(54893),a=function(){function t(){this.cx=0,this.cy=0,this.r=0}return t}(),o=function(t){function e(e){return t.call(this,e)||this}return(0,i.ZT)(e,t),e.prototype.getDefaultShape=function(){return new a},e.prototype.buildPath=function(t,e){t.moveTo(e.cx+e.r,e.cy),t.arc(e.cx,e.cy,e.r,0,2*Math.PI)},e}(n.ZP);o.prototype.type="circle",e["Z"]=/^(3491|4190|5903|6241)$/.test(r.j)?o:null},14549:function(t,e,r){"use strict";var i=r(61431),n=r(54893),a=function(){function t(){this.cx=0,this.cy=0,this.rx=0,this.ry=0}return t}(),o=function(t){function e(e){return t.call(this,e)||this}return(0,i.ZT)(e,t),e.prototype.getDefaultShape=function(){return new a},e.prototype.buildPath=function(t,e){var r=.5522848,i=e.cx,n=e.cy,a=e.rx,o=e.ry,s=a*r,h=o*r;t.moveTo(i-a,n),t.bezierCurveTo(i-a,n-h,i-s,n-o,i,n-o),t.bezierCurveTo(i+s,n-o,i+a,n-h,i+a,n),t.bezierCurveTo(i+a,n+h,i+s,n+o,i,n+o),t.bezierCurveTo(i-s,n+o,i-a,n+h,i-a,n),t.closePath()},e}(n.ZP);o.prototype.type="ellipse",e["Z"]=/^(3491|4190|5903|6241)$/.test(r.j)?o:null},26865:function(t,e,r){"use strict";var i=r(61431),n=r(54893),a=r(56059),o={},s=function(){function t(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.percent=1}return t}(),h=function(t){function e(e){return t.call(this,e)||this}return(0,i.ZT)(e,t),e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new s},e.prototype.buildPath=function(t,e){var r,i,n,s;if(this.subPixelOptimize){var h=(0,a._3)(o,e,this.style);r=h.x1,i=h.y1,n=h.x2,s=h.y2}else r=e.x1,i=e.y1,n=e.x2,s=e.y2;var l=e.percent;0!==l&&(t.moveTo(r,i),l<1&&(n=r*(1-l)+n*l,s=i*(1-l)+s*l),t.lineTo(n,s))},e.prototype.pointAt=function(t){var e=this.shape;return[e.x1*(1-t)+e.x2*t,e.y1*(1-t)+e.y2*t]},e}(n.ZP);h.prototype.type="line",e["Z"]=/^(3491|4190|5903|6241)$/.test(r.j)?h:null},70295:function(t,e,r){"use strict";var i=r(61431),n=r(54893),a=r(89434),o=function(){function t(){this.points=null,this.smooth=0,this.smoothConstraint=null}return t}(),s=function(t){function e(e){return t.call(this,e)||this}return(0,i.ZT)(e,t),e.prototype.getDefaultShape=function(){return new o},e.prototype.buildPath=function(t,e){a.L(t,e,!0)},e}(n.ZP);s.prototype.type="polygon",e["Z"]=/^(3491|4190|5903|6241)$/.test(r.j)?s:null},22734:function(t,e,r){"use strict";var i=r(61431),n=r(54893),a=r(89434),o=function(){function t(){this.points=null,this.percent=1,this.smooth=0,this.smoothConstraint=null}return t}(),s=function(t){function e(e){return t.call(this,e)||this}return(0,i.ZT)(e,t),e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new o},e.prototype.buildPath=function(t,e){a.L(t,e,!1)},e}(n.ZP);s.prototype.type="polyline",e["Z"]=/^(3491|4190|5903|6241)$/.test(r.j)?s:null},18732:function(t,e,r){"use strict";r.d(e,{Z:function(){return u}});var i=r(61431),n=r(54893);function a(t,e){var r,i,n,a,o,s=e.x,h=e.y,l=e.width,u=e.height,c=e.r;l<0&&(s+=l,l=-l),u<0&&(h+=u,u=-u),"number"===typeof c?r=i=n=a=c:c instanceof Array?1===c.length?r=i=n=a=c[0]:2===c.length?(r=n=c[0],i=a=c[1]):3===c.length?(r=c[0],i=a=c[1],n=c[2]):(r=c[0],i=c[1],n=c[2],a=c[3]):r=i=n=a=0,r+i>l&&(o=r+i,r*=l/o,i*=l/o),n+a>l&&(o=n+a,n*=l/o,a*=l/o),i+n>u&&(o=i+n,i*=u/o,n*=u/o),r+a>u&&(o=r+a,r*=u/o,a*=u/o),t.moveTo(s+r,h),t.lineTo(s+l-i,h),0!==i&&t.arc(s+l-i,h+i,i,-Math.PI/2,0),t.lineTo(s+l,h+u-n),0!==n&&t.arc(s+l-n,h+u-n,n,0,Math.PI/2),t.lineTo(s+a,h+u),0!==a&&t.arc(s+a,h+u-a,a,Math.PI/2,Math.PI),t.lineTo(s,h+r),0!==r&&t.arc(s+r,h+r,r,Math.PI,1.5*Math.PI)}var o=r(56059),s=function(){function t(){this.x=0,this.y=0,this.width=0,this.height=0}return t}(),h={},l=function(t){function e(e){return t.call(this,e)||this}return(0,i.ZT)(e,t),e.prototype.getDefaultShape=function(){return new s},e.prototype.buildPath=function(t,e){var r,i,n,s;if(this.subPixelOptimize){var l=(0,o.Pw)(h,e,this.style);r=l.x,i=l.y,n=l.width,s=l.height,l.r=e.r,e=l}else r=e.x,i=e.y,n=e.width,s=e.height;e.r?a(t,e):t.rect(r,i,n,s)},e.prototype.isZeroArea=function(){return!this.shape.width||!this.shape.height},e}(n.ZP);l.prototype.type="rect";var u=l},58138:function(t,e,r){"use strict";var i=r(61431),n=r(54893),a=function(){function t(){this.cx=0,this.cy=0,this.r=0,this.r0=0}return t}(),o=function(t){function e(e){return t.call(this,e)||this}return(0,i.ZT)(e,t),e.prototype.getDefaultShape=function(){return new a},e.prototype.buildPath=function(t,e){var r=e.cx,i=e.cy,n=2*Math.PI;t.moveTo(r+e.r,i),t.arc(r,i,e.r,0,n,!1),t.moveTo(r+e.r0,i),t.arc(r,i,e.r0,0,n,!0)},e}(n.ZP);o.prototype.type="ring",e["Z"]=/^(3491|4190|5903|6241)$/.test(r.j)?o:null},43524:function(t,e,r){"use strict";r.d(e,{C:function(){return k}});var i=r(61431),n=r(54893),a=r(41239),o=Math.PI,s=2*o,h=Math.sin,l=Math.cos,u=Math.acos,c=Math.atan2,f=Math.abs,d=Math.sqrt,p=Math.max,v=Math.min,g=1e-4;function y(t,e,r,i,n,a,o,s){var h=r-t,l=i-e,u=o-n,c=s-a,f=c*h-u*l;if(!(f*f<g))return f=(u*(e-a)-c*(t-n))/f,[t+f*h,e+f*l]}function _(t,e,r,i,n,a,o){var s=t-r,h=e-i,l=(o?a:-a)/d(s*s+h*h),u=l*h,c=-l*s,f=t+u,v=e+c,g=r+u,y=i+c,_=(f+g)/2,m=(v+y)/2,x=g-f,w=y-v,b=x*x+w*w,k=n-a,T=f*y-g*v,S=(w<0?-1:1)*d(p(0,k*k*b-T*T)),C=(T*w-x*S)/b,P=(-T*x-w*S)/b,A=(T*w+x*S)/b,M=(-T*x+w*S)/b,L=C-_,D=P-m,z=A-_,O=M-m;return L*L+D*D>z*z+O*O&&(C=A,P=M),{cx:C,cy:P,x0:-u,y0:-c,x1:C*(n/k-1),y1:P*(n/k-1)}}function m(t){var e;if((0,a.isArray)(t)){var r=t.length;if(!r)return t;e=1===r?[t[0],t[0],0,0]:2===r?[t[0],t[0],t[1],t[1]]:3===r?t.concat(t[2]):t}else e=[t,t,t,t];return e}function x(t,e){var r,i=p(e.r,0),n=p(e.r0||0,0),a=i>0,x=n>0;if(a||x){if(a||(i=n,n=0),n>i){var w=i;i=n,n=w}var b=e.startAngle,k=e.endAngle;if(!isNaN(b)&&!isNaN(k)){var T=e.cx,S=e.cy,C=!!e.clockwise,P=f(k-b),A=P>s&&P%s;if(A>g&&(P=A),i>g)if(P>s-g)t.moveTo(T+i*l(b),S+i*h(b)),t.arc(T,S,i,b,k,!C),n>g&&(t.moveTo(T+n*l(k),S+n*h(k)),t.arc(T,S,n,k,b,C));else{var M=void 0,L=void 0,D=void 0,z=void 0,O=void 0,R=void 0,I=void 0,F=void 0,B=void 0,E=void 0,Z=void 0,j=void 0,H=void 0,N=void 0,W=void 0,V=void 0,q=i*l(b),Y=i*h(b),X=n*l(k),U=n*h(k),$=P>g;if($){var G=e.cornerRadius;G&&(r=m(G),M=r[0],L=r[1],D=r[2],z=r[3]);var Q=f(i-n)/2;if(O=v(Q,D),R=v(Q,z),I=v(Q,M),F=v(Q,L),Z=B=p(O,R),j=E=p(I,F),(B>g||E>g)&&(H=i*l(k),N=i*h(k),W=n*l(b),V=n*h(b),P<o)){var K=y(q,Y,W,V,H,N,X,U);if(K){var J=q-K[0],tt=Y-K[1],et=H-K[0],rt=N-K[1],it=1/h(u((J*et+tt*rt)/(d(J*J+tt*tt)*d(et*et+rt*rt)))/2),nt=d(K[0]*K[0]+K[1]*K[1]);Z=v(B,(i-nt)/(it+1)),j=v(E,(n-nt)/(it-1))}}}if($)if(Z>g){var at=v(D,Z),ot=v(z,Z),st=_(W,V,q,Y,i,at,C),ht=_(H,N,X,U,i,ot,C);t.moveTo(T+st.cx+st.x0,S+st.cy+st.y0),Z<B&&at===ot?t.arc(T+st.cx,S+st.cy,Z,c(st.y0,st.x0),c(ht.y0,ht.x0),!C):(at>0&&t.arc(T+st.cx,S+st.cy,at,c(st.y0,st.x0),c(st.y1,st.x1),!C),t.arc(T,S,i,c(st.cy+st.y1,st.cx+st.x1),c(ht.cy+ht.y1,ht.cx+ht.x1),!C),ot>0&&t.arc(T+ht.cx,S+ht.cy,ot,c(ht.y1,ht.x1),c(ht.y0,ht.x0),!C))}else t.moveTo(T+q,S+Y),t.arc(T,S,i,b,k,!C);else t.moveTo(T+q,S+Y);if(n>g&&$)if(j>g){at=v(M,j),ot=v(L,j),st=_(X,U,H,N,n,-ot,C),ht=_(q,Y,W,V,n,-at,C);t.lineTo(T+st.cx+st.x0,S+st.cy+st.y0),j<E&&at===ot?t.arc(T+st.cx,S+st.cy,j,c(st.y0,st.x0),c(ht.y0,ht.x0),!C):(ot>0&&t.arc(T+st.cx,S+st.cy,ot,c(st.y0,st.x0),c(st.y1,st.x1),!C),t.arc(T,S,n,c(st.cy+st.y1,st.cx+st.x1),c(ht.cy+ht.y1,ht.cx+ht.x1),C),at>0&&t.arc(T+ht.cx,S+ht.cy,at,c(ht.y1,ht.x1),c(ht.y0,ht.x0),!C))}else t.lineTo(T+X,S+U),t.arc(T,S,n,k,b,C);else t.lineTo(T+X,S+U)}else t.moveTo(T,S);t.closePath()}}}var w=function(){function t(){this.cx=0,this.cy=0,this.r0=0,this.r=0,this.startAngle=0,this.endAngle=2*Math.PI,this.clockwise=!0,this.cornerRadius=0}return t}(),b=function(t){function e(e){return t.call(this,e)||this}return(0,i.ZT)(e,t),e.prototype.getDefaultShape=function(){return new w},e.prototype.buildPath=function(t,e){x(t,e)},e.prototype.isZeroArea=function(){return this.shape.startAngle===this.shape.endAngle||this.shape.r===this.shape.r0},e}(n.ZP);b.prototype.type="sector";var k=b},96463:function(t,e,r){"use strict";r.d(e,{Z:function(){return rt}});var i=r(80166),n=r(54893),a=r(34714),o=r(73048),s=r(74969),h=r(36146),l=r(78510),u=r(81953),c=r(41239),f=r(63315),d=r(80447),p=r(99743),v=r(11693),g=Math.round;function y(t){return t&&(0,c.isString)(t.src)}function _(t){return t&&(0,c.isFunction)(t.toDataURL)}function m(t,e,r,n){(0,l.Z)((function(a,o){var s="fill"===a||"stroke"===a;s&&(0,i.H3)(o)?O(e,t,a,n):s&&(0,i.R)(o)?R(r,t,a,n):t[a]=o}),e,r,!1),z(r,t,n)}function x(t){return(0,i.zT)(t[0]-1)&&(0,i.zT)(t[1])&&(0,i.zT)(t[2])&&(0,i.zT)(t[3]-1)}function w(t){return(0,i.zT)(t[4])&&(0,i.zT)(t[5])}function b(t,e,r){if(e&&(!w(e)||!x(e))){var n=r?10:1e4;t.transform=x(e)?"translate("+g(e[4]*n)/n+" "+g(e[5]*n)/n+")":(0,i.qV)(e)}}function k(t,e,r){for(var i=t.points,n=[],a=0;a<i.length;a++)n.push(g(i[a][0]*r)/r),n.push(g(i[a][1]*r)/r);e.points=n.join(" ")}function T(t){return!t.smooth}function S(t){var e=(0,c.map)(t,(function(t){return"string"===typeof t?[t,t]:t}));return function(t,r,i){for(var n=0;n<e.length;n++){var a=e[n],o=t[a[0]];null!=o&&(r[a[1]]=g(o*i)/i)}}}var C={circle:[S(["cx","cy","r"])],polyline:[k,T],polygon:[k,T]};function P(t){for(var e=t.animators,r=0;r<e.length;r++)if("shape"===e[r].targetName)return!0;return!1}function A(t,e){var r=t.style,n=t.shape,a=C[t.type],o={},s=e.animation,l="path",c=t.style.strokePercent,f=e.compress&&(0,i.Gk)(t)||4;if(!a||e.willUpdate||a[1]&&!a[1](n)||s&&P(t)||c<1){var p=!t.path||t.shapeChanged();t.path||t.createPathProxy();var v=t.path;p&&(v.beginPath(),t.buildPath(v,t.shape),t.pathUpdated());var g=v.getVersion(),y=t,_=y.__svgPathBuilder;y.__svgPathVersion===g&&_&&c===y.__svgPathStrokePercent||(_||(_=y.__svgPathBuilder=new h.Z),_.reset(f),v.rebuildPath(_,c),_.generateStr(),y.__svgPathVersion=g,y.__svgPathStrokePercent=c),o.d=_.getStr()}else{l=t.type;var x=Math.pow(10,f);a[0](n,o,x)}return b(o,t.transform),m(o,r,t,e),e.animation&&(0,d.gH)(t,o,e),(0,u.Wm)(l,t.id+"",o)}function M(t,e){var r=t.style,i=r.image;if(i&&!(0,c.isString)(i)&&(y(i)?i=i.src:_(i)&&(i=i.toDataURL())),i){var n=r.x||0,a=r.y||0,o=r.width,s=r.height,h={href:i,width:o,height:s};return n&&(h.x=n),a&&(h.y=a),b(h,t.transform),m(h,r,t,e),e.animation&&(0,d.gH)(t,h,e),(0,u.Wm)("image",t.id+"",h)}}function L(t,e){var r=t.style,n=r.text;if(null!=n&&(n+=""),n&&!isNaN(r.x)&&!isNaN(r.y)){var a=r.font||v.Uo,s=r.x||0,h=(0,i.mU)(r.y||0,(0,o.Dp)(a),r.textBaseline),l=i.jY[r.textAlign]||r.textAlign,c={"dominant-baseline":"central","text-anchor":l};if((0,p.Y1)(r)){var f="",g=r.fontStyle,y=(0,p.VG)(r.fontSize);if(!parseFloat(y))return;var _=r.fontFamily||v.rk,x=r.fontWeight;f+="font-size:"+y+";font-family:"+_+";",g&&"normal"!==g&&(f+="font-style:"+g+";"),x&&"normal"!==x&&(f+="font-weight:"+x+";"),c.style=f}else c.style="font: "+a;return n.match(/\s/)&&(c["xml:space"]="preserve"),s&&(c.x=s),h&&(c.y=h),b(c,t.transform),m(c,r,t,e),e.animation&&(0,d.gH)(t,c,e),(0,u.Wm)("text",t.id+"",c,void 0,n)}}function D(t,e){return t instanceof n.ZP?A(t,e):t instanceof a.ZP?M(t,e):t instanceof s.Z?L(t,e):void 0}function z(t,e,r){var n=t.style;if((0,i.i2)(n)){var a=(0,i.n1)(t),o=r.shadowCache,s=o[a];if(!s){var h=t.getGlobalScale(),l=h[0],c=h[1];if(!l||!c)return;var f=n.shadowOffsetX||0,d=n.shadowOffsetY||0,p=n.shadowBlur,v=(0,i.ut)(n.shadowColor),g=v.opacity,y=v.color,_=p/2/l,m=p/2/c,x=_+" "+m;s=r.zrId+"-s"+r.shadowIdx++,r.defs[s]=(0,u.Wm)("filter",s,{id:s,x:"-100%",y:"-100%",width:"300%",height:"300%"},[(0,u.Wm)("feDropShadow","",{dx:f/l,dy:d/c,stdDeviation:x,"flood-color":y,"flood-opacity":g})]),o[a]=s}e.filter=(0,i.m1)(s)}}function O(t,e,r,n){var a,o=t[r],s={gradientUnits:o.global?"userSpaceOnUse":"objectBoundingBox"};if((0,i.I1)(o))a="linearGradient",s.x1=o.x,s.y1=o.y,s.x2=o.x2,s.y2=o.y2;else{if(!(0,i.gO)(o))return void 0;a="radialGradient",s.cx=(0,c.retrieve2)(o.x,.5),s.cy=(0,c.retrieve2)(o.y,.5),s.r=(0,c.retrieve2)(o.r,.5)}for(var h=o.colorStops,l=[],f=0,d=h.length;f<d;++f){var p=100*(0,i.Pn)(h[f].offset)+"%",v=h[f].color,g=(0,i.ut)(v),y=g.color,_=g.opacity,m={offset:p};m["stop-color"]=y,_<1&&(m["stop-opacity"]=_),l.push((0,u.Wm)("stop",f+"",m))}var x=(0,u.Wm)(a,"",s,l),w=(0,u.HO)(x),b=n.gradientCache,k=b[w];k||(k=n.zrId+"-g"+n.gradientIdx++,b[w]=k,s.id=k,n.defs[k]=(0,u.Wm)(a,k,s,l)),e[r]=(0,i.m1)(k)}function R(t,e,r,n){var a,o=t.style[r],s=t.getBoundingRect(),h={},l=o.repeat,d="no-repeat"===l,p="repeat-x"===l,v="repeat-y"===l;if((0,i.Cv)(o)){var g=o.imageWidth,m=o.imageHeight,x=void 0,w=o.image;if((0,c.isString)(w)?x=w:y(w)?x=w.src:_(w)&&(x=w.toDataURL()),"undefined"===typeof Image){var b="Image width/height must been given explictly in svg-ssr renderer.";(0,c.assert)(g,b),(0,c.assert)(m,b)}else if(null==g||null==m){var k=function(t,e){if(t){var r=t.elm,i=g||e.width,n=m||e.height;"pattern"===t.tag&&(p?(n=1,i/=s.width):v&&(i=1,n/=s.height)),t.attrs.width=i,t.attrs.height=n,r&&(r.setAttribute("width",i),r.setAttribute("height",n))}},T=(0,f.Gq)(x,null,t,(function(t){d||k(A,t),k(a,t)}));T&&T.width&&T.height&&(g=g||T.width,m=m||T.height)}a=(0,u.Wm)("image","img",{href:x,width:g,height:m}),h.width=g,h.height=m}else o.svgElement&&(a=(0,c.clone)(o.svgElement),h.width=o.svgWidth,h.height=o.svgHeight);if(a){var S,C;d?S=C=1:p?(C=1,S=h.width/s.width):v?(S=1,C=h.height/s.height):h.patternUnits="userSpaceOnUse",null==S||isNaN(S)||(h.width=S),null==C||isNaN(C)||(h.height=C);var P=(0,i.gA)(o);P&&(h.patternTransform=P);var A=(0,u.Wm)("pattern","",h,[a]),M=(0,u.HO)(A),L=n.patternCache,D=L[M];D||(D=n.zrId+"-p"+n.patternIdx++,L[M]=D,h.id=D,A=n.defs[D]=(0,u.Wm)("pattern",D,h,[a])),e[r]=(0,i.m1)(D)}}function I(t,e,r){var n=r.clipPathCache,a=r.defs,o=n[t.id];if(!o){o=r.zrId+"-c"+r.clipPathIdx++;var s={id:o};n[t.id]=o,a[o]=(0,u.Wm)("clipPath",o,s,[A(t,r)])}e["clip-path"]=(0,i.m1)(o)}var F=r(21962),B=58,E=120,Z=(0,u.Wm)("","");function j(t){return void 0===t}function H(t){return void 0!==t}function N(t,e,r){for(var i={},n=e;n<=r;++n){var a=t[n].key;void 0!==a&&(i[a]=n)}return i}function W(t,e){var r=t.key===e.key,i=t.tag===e.tag;return i&&r}function V(t){var e,r=t.children,i=t.tag;if(H(i)){var n=t.elm=(0,u.az)(i);if(X(Z,t),(0,c.isArray)(r))for(e=0;e<r.length;++e){var a=r[e];null!=a&&F.jG(n,V(a))}else H(t.text)&&!(0,c.isObject)(t.text)&&F.jG(n,F.Eg(t.text))}else t.elm=F.Eg(t.text);return t.elm}function q(t,e,r,i,n){for(;i<=n;++i){var a=r[i];null!=a&&F.Vt(t,V(a),e)}}function Y(t,e,r,i){for(;r<=i;++r){var n=e[r];if(null!=n)if(H(n.tag)){var a=F.hK(n.elm);F.hr(a,n.elm)}else F.hr(t,n.elm)}}function X(t,e){var r,i=e.elm,n=t&&t.attrs||{},a=e.attrs||{};if(n!==a){for(r in a){var o=a[r],s=n[r];s!==o&&(!0===o?i.setAttribute(r,""):!1===o?i.removeAttribute(r):r.charCodeAt(0)!==E?i.setAttribute(r,o):"xmlns:xlink"===r||"xmlns"===r?i.setAttributeNS(u.rv,r,o):r.charCodeAt(3)===B?i.setAttributeNS(u.C5,r,o):r.charCodeAt(5)===B?i.setAttributeNS(u.ar,r,o):i.setAttribute(r,o))}for(r in n)r in a||i.removeAttribute(r)}}function U(t,e,r){var i,n,a,o,s=0,h=0,l=e.length-1,u=e[0],c=e[l],f=r.length-1,d=r[0],p=r[f];while(s<=l&&h<=f)null==u?u=e[++s]:null==c?c=e[--l]:null==d?d=r[++h]:null==p?p=r[--f]:W(u,d)?($(u,d),u=e[++s],d=r[++h]):W(c,p)?($(c,p),c=e[--l],p=r[--f]):W(u,p)?($(u,p),F.Vt(t,u.elm,F.AH(c.elm)),u=e[++s],p=r[--f]):W(c,d)?($(c,d),F.Vt(t,c.elm,u.elm),c=e[--l],d=r[++h]):(j(i)&&(i=N(e,s,l)),n=i[d.key],j(n)?F.Vt(t,V(d),u.elm):(a=e[n],a.tag!==d.tag?F.Vt(t,V(d),u.elm):($(a,d),e[n]=void 0,F.Vt(t,a.elm,u.elm))),d=r[++h]);(s<=l||h<=f)&&(s>l?(o=null==r[f+1]?null:r[f+1].elm,q(t,o,r,h,f)):Y(t,e,s,l))}function $(t,e){var r=e.elm=t.elm,i=t.children,n=e.children;t!==e&&(X(t,e),j(e.text)?H(i)&&H(n)?i!==n&&U(r,i,n):H(n)?(H(t.text)&&F.D5(r,""),q(r,null,n,0,n.length-1)):H(i)?Y(r,i,0,i.length-1):H(t.text)&&F.D5(r,""):t.text!==e.text&&(H(i)&&Y(r,i,0,i.length-1),F.D5(r,e.text)))}function G(t,e){if(W(t,e))$(t,e);else{var r=t.elm,i=F.hK(r);V(e),null!==i&&(F.Vt(i,e.elm,F.AH(r)),Y(i,[t],0,0))}return e}var Q=r(44380),K=0,J=function(){function t(t,e,r){if(this.type="svg",this.refreshHover=tt("refreshHover"),this.configLayer=tt("configLayer"),this.storage=e,this._opts=r=(0,c.extend)({},r),this.root=t,this._id="zr"+K++,this._oldVNode=(0,u.Wu)(r.width,r.height),t&&!r.ssr){var i=this._viewport=document.createElement("div");i.style.cssText="position:relative;overflow:hidden";var n=this._svgDom=this._oldVNode.elm=(0,u.az)("svg");X(null,this._oldVNode),i.appendChild(n),t.appendChild(i)}this.resize(r.width,r.height)}return t.prototype.getType=function(){return this.type},t.prototype.getViewportRoot=function(){return this._viewport},t.prototype.getViewportRootOffset=function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},t.prototype.getSvgDom=function(){return this._svgDom},t.prototype.refresh=function(){if(this.root){var t=this.renderToVNode({willUpdate:!0});t.attrs.style="position:absolute;left:0;top:0;user-select:none",G(this._oldVNode,t),this._oldVNode=t}},t.prototype.renderOneToVNode=function(t){return D(t,(0,u.So)(this._id))},t.prototype.renderToVNode=function(t){t=t||{};var e=this.storage.getDisplayList(!0),r=this._width,i=this._height,n=(0,u.So)(this._id);n.animation=t.animation,n.willUpdate=t.willUpdate,n.compress=t.compress;var a=[],o=this._bgVNode=et(r,i,this._backgroundColor,n);o&&a.push(o);var s=t.compress?null:this._mainVNode=(0,u.Wm)("g","main",{},[]);this._paintList(e,n,s?s.children:a),s&&a.push(s);var h=(0,c.map)((0,c.keys)(n.defs),(function(t){return n.defs[t]}));if(h.length&&a.push((0,u.Wm)("defs","defs",{},h)),t.animation){var l=(0,u.wU)(n.cssNodes,n.cssAnims,{newline:!0});if(l){var f=(0,u.Wm)("style","stl",{},[],l);a.push(f)}}return(0,u.Wu)(r,i,a,t.useViewBox)},t.prototype.renderToString=function(t){return t=t||{},(0,u.HO)(this.renderToVNode({animation:(0,c.retrieve2)(t.cssAnimation,!0),willUpdate:!1,compress:!0,useViewBox:(0,c.retrieve2)(t.useViewBox,!0)}),{newline:!0})},t.prototype.setBackgroundColor=function(t){this._backgroundColor=t},t.prototype.getSvgRoot=function(){return this._mainVNode&&this._mainVNode.elm},t.prototype._paintList=function(t,e,r){for(var i,n,a=t.length,o=[],s=0,h=0,l=0;l<a;l++){var c=t[l];if(!c.invisible){var f=c.__clipPaths,d=f&&f.length||0,p=n&&n.length||0,v=void 0;for(v=Math.max(d-1,p-1);v>=0;v--)if(f&&n&&f[v]===n[v])break;for(var g=p-1;g>v;g--)s--,i=o[s-1];for(var y=v+1;y<d;y++){var _={};I(f[y],_,e);var m=(0,u.Wm)("g","clip-g-"+h++,_,[]);(i?i.children:r).push(m),o[s++]=m,i=m}n=f;var x=D(c,e);x&&(i?i.children:r).push(x)}}},t.prototype.resize=function(t,e){var r=this._opts,n=this.root,a=this._viewport;if(null!=t&&(r.width=t),null!=e&&(r.height=e),n&&a&&(a.style.display="none",t=(0,Q.ap)(n,0,r),e=(0,Q.ap)(n,1,r),a.style.display=""),this._width!==t||this._height!==e){if(this._width=t,this._height=e,a){var o=a.style;o.width=t+"px",o.height=e+"px"}if((0,i.R)(this._backgroundColor))this.refresh();else{var s=this._svgDom;s&&(s.setAttribute("width",t),s.setAttribute("height",e));var h=this._bgVNode&&this._bgVNode.elm;h&&(h.setAttribute("width",t),h.setAttribute("height",e))}}},t.prototype.getWidth=function(){return this._width},t.prototype.getHeight=function(){return this._height},t.prototype.dispose=function(){this.root&&(this.root.innerHTML=""),this._svgDom=this._viewport=this.storage=this._oldVNode=this._bgVNode=this._mainVNode=null},t.prototype.clear=function(){this._svgDom&&(this._svgDom.innerHTML=null),this._oldVNode=null},t.prototype.toDataURL=function(t){var e=this.renderToString(),r="data:image/svg+xml;";return t?(e=(0,i.oF)(e),e&&r+"base64,"+e):r+"charset=UTF-8,"+encodeURIComponent(e)},t}();function tt(t){return function(){0}}function et(t,e,r,n){var a;if(r&&"none"!==r)if(a=(0,u.Wm)("rect","bg",{width:t,height:e,x:"0",y:"0",id:"0"}),(0,i.H3)(r))O({fill:r},a.attrs,"fill",n);else if((0,i.R)(r))R({style:{fill:r},dirty:c.noop,getBoundingRect:function(){return{width:t,height:e}}},a.attrs,"fill",n);else{var o=(0,i.ut)(r),s=o.color,h=o.opacity;a.attrs.fill=s,h<1&&(a.attrs["fill-opacity"]=h)}return a}var rt=J},36146:function(t,e,r){"use strict";var i=r(80166),n=Math.sin,a=Math.cos,o=Math.PI,s=2*Math.PI,h=180/o,l=function(){function t(){}return t.prototype.reset=function(t){this._start=!0,this._d=[],this._str="",this._p=Math.pow(10,t||4)},t.prototype.moveTo=function(t,e){this._add("M",t,e)},t.prototype.lineTo=function(t,e){this._add("L",t,e)},t.prototype.bezierCurveTo=function(t,e,r,i,n,a){this._add("C",t,e,r,i,n,a)},t.prototype.quadraticCurveTo=function(t,e,r,i){this._add("Q",t,e,r,i)},t.prototype.arc=function(t,e,r,i,n,a){this.ellipse(t,e,r,r,0,i,n,a)},t.prototype.ellipse=function(t,e,r,l,u,c,f,d){var p=f-c,v=!d,g=Math.abs(p),y=(0,i.zT)(g-s)||(v?p>=s:-p>=s),_=p>0?p%s:p%s+s,m=!1;m=!!y||!(0,i.zT)(g)&&_>=o===!!v;var x=t+r*a(c),w=e+l*n(c);this._start&&this._add("M",x,w);var b=Math.round(u*h);if(y){var k=1/this._p,T=(v?1:-1)*(s-k);this._add("A",r,l,b,1,+v,t+r*a(c+T),e+l*n(c+T)),k>.01&&this._add("A",r,l,b,0,+v,x,w)}else{var S=t+r*a(f),C=e+l*n(f);this._add("A",r,l,b,+m,+v,S,C)}},t.prototype.rect=function(t,e,r,i){this._add("M",t,e),this._add("l",r,0),this._add("l",0,i),this._add("l",-r,0),this._add("Z")},t.prototype.closePath=function(){this._d.length>0&&this._add("Z")},t.prototype._add=function(t,e,r,i,n,a,o,s,h){for(var l=[],u=this._p,c=1;c<arguments.length;c++){var f=arguments[c];if(isNaN(f))return void(this._invalid=!0);l.push(Math.round(f*u)/u)}this._d.push(t+l.join(" ")),this._start="Z"===t},t.prototype.generateStr=function(){this._str=this._invalid?"":this._d.join(""),this._d=[]},t.prototype.getStr=function(){return this._str},t}();e["Z"]=/^(3491|4190|5903|6241)$/.test(r.j)?l:null},81953:function(t,e,r){"use strict";if(r.d(e,{C5:function(){return h},HO:function(){return d},So:function(){return v},Wm:function(){return u},Wu:function(){return g},ar:function(){return o},az:function(){return l},rv:function(){return s},wU:function(){return p}}),/^(3491|4190|5903|6241)$/.test(r.j))var i=r(41239);if(/^(3491|4190|5903|6241)$/.test(r.j))var n=r(98249);var a="http://www.w3.org/2000/svg",o="http://www.w3.org/1999/xlink",s="http://www.w3.org/2000/xmlns/",h="http://www.w3.org/XML/1998/namespace";function l(t){return document.createElementNS(a,t)}function u(t,e,r,i,n){return{tag:t,attrs:r||{},children:i,text:n,key:e}}function c(t,e){var r=[];if(e)for(var i in e){var n=e[i],a=i;!1!==n&&(!0!==n&&null!=n&&(a+='="'+n+'"'),r.push(a))}return"<"+t+" "+r.join(" ")+">"}function f(t){return"</"+t+">"}function d(t,e){e=e||{};var r=e.newline?"\n":"";function a(t){var e=t.children,o=t.tag,s=t.attrs,h=t.text;return c(o,s)+("style"!==o?(0,n.F1)(h):h||"")+(e?""+r+(0,i.map)(e,(function(t){return a(t)})).join(r)+r:"")+f(o)}return a(t)}function p(t,e,r){r=r||{};var n=r.newline?"\n":"",a=" {"+n,o=n+"}",s=(0,i.map)((0,i.keys)(t),(function(e){return e+a+(0,i.map)((0,i.keys)(t[e]),(function(r){return r+":"+t[e][r]+";"})).join(n)+o})).join(n),h=(0,i.map)((0,i.keys)(e),(function(t){return"@keyframes "+t+a+(0,i.map)((0,i.keys)(e[t]),(function(r){return r+a+(0,i.map)((0,i.keys)(e[t][r]),(function(i){var n=e[t][r][i];return"d"===i&&(n='path("'+n+'")'),i+":"+n+";"})).join(n)+o})).join(n)+o})).join(n);return s||h?["<![CDATA[",s,h,"]]>"].join(n):""}function v(t){return{zrId:t,shadowCache:{},patternCache:{},gradientCache:{},clipPathCache:{},defs:{},cssNodes:{},cssAnims:{},cssClassIdx:0,cssAnimIdx:0,shadowIdx:0,gradientIdx:0,patternIdx:0,clipPathIdx:0}}function g(t,e,r,i){return u("svg","root",{width:t,height:e,xmlns:a,"xmlns:xlink":o,version:"1.1",baseProfile:"full",viewBox:!!i&&"0 0 "+t+" "+e},r)}},80447:function(t,e,r){"use strict";if(r.d(e,{gH:function(){return m}}),/^(3491|4190|5903|6241)$/.test(r.j))var i=r(71270);if(/^(3491|4190|5903|6241)$/.test(r.j))var n=r(81953);if(/^(3491|4190|5903|6241)$/.test(r.j))var a=r(36146);if(/^(3491|4190|5903|6241)$/.test(r.j))var o=r(72136);if(/^(3491|4190|5903|6241)$/.test(r.j))var s=r(80166);if(/^(3491|4190|5903|6241)$/.test(r.j))var h=r(41239);if(/^(3491|4190|5903|6241)$/.test(r.j))var l=r(46886);if(/^(3491|4190|5903|6241)$/.test(r.j))var u=r(79305);var c={cubicIn:"0.32,0,0.67,0",cubicOut:"0.33,1,0.68,1",cubicInOut:"0.65,0,0.35,1",quadraticIn:"0.11,0,0.5,0",quadraticOut:"0.5,1,0.89,1",quadraticInOut:"0.45,0,0.55,1",quarticIn:"0.5,0,0.75,0",quarticOut:"0.25,1,0.5,1",quarticInOut:"0.76,0,0.24,1",quinticIn:"0.64,0,0.78,0",quinticOut:"0.22,1,0.36,1",quinticInOut:"0.83,0,0.17,1",sinusoidalIn:"0.12,0,0.39,0",sinusoidalOut:"0.61,1,0.88,1",sinusoidalInOut:"0.37,0,0.63,1",exponentialIn:"0.7,0,0.84,0",exponentialOut:"0.16,1,0.3,1",exponentialInOut:"0.87,0,0.13,1",circularIn:"0.55,0,1,0.45",circularOut:"0,0.55,0.45,1",circularInOut:"0.85,0,0.15,1"},f="transform-origin";function d(t,e,r){var i=(0,h.extend)({},t.shape);(0,h.extend)(i,e),t.buildPath(r,i);var n=new a.Z;return n.reset((0,s.Gk)(t)),r.rebuildPath(n,1),n.generateStr(),n.getStr()}function p(t,e){var r=e.originX,i=e.originY;(r||i)&&(t[f]=r+"px "+i+"px")}var v={fill:"fill",opacity:"opacity",lineWidth:"stroke-width",lineDashOffset:"stroke-dashoffset"};function g(t,e){var r=e.zrId+"-ani-"+e.cssAnimIdx++;return e.cssAnims[r]=t,r}function y(t,e,r){var i,a,o=t.shape.paths,s={};if((0,h.each)(o,(function(t){var e=(0,n.So)(r.zrId);e.animation=!0,m(t,{},e,!0);var o=e.cssAnims,l=e.cssNodes,u=(0,h.keys)(o),c=u.length;if(c){a=u[c-1];var f=o[a];for(var d in f){var p=f[d];s[d]=s[d]||{d:""},s[d].d+=p.d||""}for(var v in l){var g=l[v].animation;g.indexOf(a)>=0&&(i=g)}}})),i){e.d=!1;var l=g(s,r);return i.replace(a,l)}}function _(t){return(0,h.isString)(t)?c[t]?"cubic-bezier("+c[t]+")":(0,u.H)(t)?t:"":""}function m(t,e,r,n){var a=t.animators,u=a.length,c=[];if(t instanceof l.Z){var m=y(t,e,r);if(m)c.push(m);else if(!u)return}else if(!u)return;for(var x={},w=0;w<u;w++){var b=a[w],k=[b.getMaxTime()/1e3+"s"],T=_(b.getClip().easing),S=b.getDelay();T?k.push(T):k.push("linear"),S&&k.push(S/1e3+"s"),b.getLoop()&&k.push("infinite");var C=k.join(" ");x[C]=x[C]||[C,[]],x[C][1].push(b)}function P(a){var l,u=a[1],c=u.length,y={},m={},x={},w="animation-timing-function";function b(t,e,r){for(var i=t.getTracks(),n=t.getMaxTime(),a=0;a<i.length;a++){var o=i[a];if(o.needsAnimate()){var s=o.keyframes,l=o.propName;if(r&&(l=r(l)),l)for(var u=0;u<s.length;u++){var c=s[u],f=Math.round(c.time/n*100)+"%",d=_(c.easing),p=c.rawValue;((0,h.isString)(p)||(0,h.isNumber)(p))&&(e[f]=e[f]||{},e[f][l]=c.rawValue,d&&(e[f][w]=d))}}}}for(var k=0;k<c;k++){var T=u[k],S=T.targetName;S?"shape"===S&&b(T,m):!n&&b(T,y)}for(var C in y){var P={};(0,i.kY)(P,t),(0,h.extend)(P,y[C]);var A=(0,s.gA)(P),M=y[C][w];x[C]=A?{transform:A}:{},p(x[C],P),M&&(x[C][w]=M)}var L=!0;for(var C in m){x[C]=x[C]||{};var D=!l;M=m[C][w];D&&(l=new o.Z);var z=l.len();l.reset(),x[C].d=d(t,m[C],l);var O=l.len();if(!D&&z!==O){L=!1;break}M&&(x[C][w]=M)}if(!L)for(var C in x)delete x[C].d;if(!n)for(k=0;k<c;k++){T=u[k],S=T.targetName;"style"===S&&b(T,x,(function(t){return v[t]}))}var R,I=(0,h.keys)(x),F=!0;for(k=1;k<I.length;k++){var B=I[k-1],E=I[k];if(x[B][f]!==x[E][f]){F=!1;break}R=x[B][f]}if(F&&R){for(var C in x)x[C][f]&&delete x[C][f];e[f]=R}if((0,h.filter)(I,(function(t){return(0,h.keys)(x[t]).length>0})).length){var Z=g(x,r);return Z+" "+a[0]+" both"}}for(var A in x){m=P(x[A]);m&&c.push(m)}if(c.length){var M=r.zrId+"-cls-"+r.cssClassIdx++;r.cssNodes["."+M]={animation:c.join(",")},e["class"]=M}}},21962:function(t,e,r){"use strict";function i(t){return document.createTextNode(t)}function n(t,e,r){t.insertBefore(e,r)}function a(t,e){t.removeChild(e)}function o(t,e){t.appendChild(e)}function s(t){return t.parentNode}function h(t){return t.nextSibling}function l(t,e){t.textContent=e}r.d(e,{AH:function(){return h},D5:function(){return l},Eg:function(){return i},Vt:function(){return n},hK:function(){return s},hr:function(){return a},jG:function(){return o}})},80166:function(t,e,r){"use strict";r.d(e,{Cv:function(){return y},Gk:function(){return T},H3:function(){return b},I1:function(){return x},Pn:function(){return c},R:function(){return m},gA:function(){return S},gO:function(){return w},i2:function(){return v},jY:function(){return d},m1:function(){return k},mU:function(){return p},n1:function(){return g},oF:function(){return C},qV:function(){return f},ut:function(){return s},zT:function(){return l}});var i=r(41239);if(/^(3491|4190|5903|6241)$/.test(r.j))var n=r(38689);var a=r(84754),o=Math.round;function s(t){var e;if(t&&"transparent"!==t){if("string"===typeof t&&t.indexOf("rgba")>-1){var r=(0,n.parse)(t);r&&(t="rgb("+r[0]+","+r[1]+","+r[2]+")",e=r[3])}}else t="none";return{color:t,opacity:null==e?1:e}}var h=1e-4;function l(t){return t<h&&t>-h}function u(t){return o(1e3*t)/1e3}function c(t){return o(1e4*t)/1e4}function f(t){return"matrix("+u(t[0])+","+u(t[1])+","+u(t[2])+","+u(t[3])+","+c(t[4])+","+c(t[5])+")"}var d={left:"start",right:"end",center:"middle",middle:"middle"};function p(t,e,r){return"top"===r?t+=e/2:"bottom"===r&&(t-=e/2),t}function v(t){return t&&(t.shadowBlur||t.shadowOffsetX||t.shadowOffsetY)}function g(t){var e=t.style,r=t.getGlobalScale();return[e.shadowColor,(e.shadowBlur||0).toFixed(2),(e.shadowOffsetX||0).toFixed(2),(e.shadowOffsetY||0).toFixed(2),r[0],r[1]].join(",")}function y(t){return t&&!!t.image}function _(t){return t&&!!t.svgElement}function m(t){return y(t)||_(t)}function x(t){return"linear"===t.type}function w(t){return"radial"===t.type}function b(t){return t&&("linear"===t.type||"radial"===t.type)}function k(t){return"url(#"+t+")"}function T(t){var e=t.getGlobalScale(),r=Math.max(e[0],e[1]);return Math.max(Math.ceil(Math.log(r)/Math.log(10)),1)}function S(t){var e=t.x||0,r=t.y||0,n=(t.rotation||0)*i.RADIAN_TO_DEGREE,a=(0,i.retrieve2)(t.scaleX,1),s=(0,i.retrieve2)(t.scaleY,1),h=t.skewX||0,l=t.skewY||0,u=[];return(e||r)&&u.push("translate("+e+"px,"+r+"px)"),n&&u.push("rotate("+n+")"),1===a&&1===s||u.push("scale("+a+","+s+")"),(h||l)&&u.push("skew("+o(h*i.RADIAN_TO_DEGREE)+"deg, "+o(l*i.RADIAN_TO_DEGREE)+"deg)"),u.join(" ")}var C=function(){return a.Z.hasGlobalWindow&&(0,i.isFunction)(window.btoa)?function(t){return window.btoa(unescape(encodeURIComponent(t)))}:"undefined"!==typeof Buffer?function(t){return Buffer.from(t).toString("base64")}:function(t){return null}}()},78510:function(t,e,r){"use strict";if(r.d(e,{Z:function(){return p}}),/^(3491|4190|5903|6241)$/.test(r.j))var i=r(54893);if(/^(3491|4190|5903|6241)$/.test(r.j))var n=r(34714);if(/^(3491|4190|5903|6241)$/.test(r.j))var a=r(32689);var o=r(41239);if(/^(3491|4190|5903|6241)$/.test(r.j))var s=r(80166);var h="none",l=Math.round;function u(t){var e=t.fill;return null!=e&&e!==h}function c(t){var e=t.stroke;return null!=e&&e!==h}var f=["lineCap","miterLimit","lineJoin"],d=(0,o.map)(f,(function(t){return"stroke-"+t.toLowerCase()}));function p(t,e,r,o){var p=null==e.opacity?1:e.opacity;if(r instanceof n.ZP)t("opacity",p);else{if(u(e)){var v=(0,s.ut)(e.fill);t("fill",v.color);var g=null!=e.fillOpacity?e.fillOpacity*v.opacity*p:v.opacity*p;(o||g<1)&&t("fill-opacity",g)}else t("fill",h);if(c(e)){var y=(0,s.ut)(e.stroke);t("stroke",y.color);var _=e.strokeNoScale?r.getLineScale():1,m=_?(e.lineWidth||0)/_:0,x=null!=e.strokeOpacity?e.strokeOpacity*y.opacity*p:y.opacity*p,w=e.strokeFirst;if((o||1!==m)&&t("stroke-width",m),(o||w)&&t("paint-order",w?"stroke":"fill"),(o||x<1)&&t("stroke-opacity",x),e.lineDash){var b=(0,a.a)(r),k=b[0],T=b[1];k&&(T=l(T||0),t("stroke-dasharray",k.join(",")),(T||o)&&t("stroke-dashoffset",T))}else o&&t("stroke-dasharray",h);for(var S=0;S<f.length;S++){var C=f[S];if(o||e[C]!==i.$t[C]){var P=e[C]||i.$t[C];P&&t(d[S],P)}}}else o&&t("stroke",h)}}},38689:function(t,e,r){"use strict";r.r(e),r.d(e,{fastLerp:function(){return b},fastMapToColor:function(){return k},lerp:function(){return T},lift:function(){return x},lum:function(){return M},mapToColor:function(){return S},modifyAlpha:function(){return P},modifyHSL:function(){return C},parse:function(){return y},random:function(){return L},stringify:function(){return A},toHex:function(){return w}});var i=r(88339),n={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]};function a(t){return t=Math.round(t),t<0?0:t>255?255:t}function o(t){return t=Math.round(t),t<0?0:t>360?360:t}function s(t){return t<0?0:t>1?1:t}function h(t){var e=t;return e.length&&"%"===e.charAt(e.length-1)?a(parseFloat(e)/100*255):a(parseInt(e,10))}function l(t){var e=t;return e.length&&"%"===e.charAt(e.length-1)?s(parseFloat(e)/100):s(parseFloat(e))}function u(t,e,r){return r<0?r+=1:r>1&&(r-=1),6*r<1?t+(e-t)*r*6:2*r<1?e:3*r<2?t+(e-t)*(2/3-r)*6:t}function c(t,e,r){return t+(e-t)*r}function f(t,e,r,i,n){return t[0]=e,t[1]=r,t[2]=i,t[3]=n,t}function d(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t}var p=new i.ZP(20),v=null;function g(t,e){v&&d(v,e),v=p.put(t,v||e.slice())}function y(t,e){if(t){e=e||[];var r=p.get(t);if(r)return d(e,r);t+="";var i=t.replace(/ /g,"").toLowerCase();if(i in n)return d(e,n[i]),g(t,e),e;var a=i.length;if("#"!==i.charAt(0)){var o=i.indexOf("("),s=i.indexOf(")");if(-1!==o&&s+1===a){var u=i.substr(0,o),c=i.substr(o+1,s-(o+1)).split(","),v=1;switch(u){case"rgba":if(4!==c.length)return 3===c.length?f(e,+c[0],+c[1],+c[2],1):f(e,0,0,0,1);v=l(c.pop());case"rgb":return c.length>=3?(f(e,h(c[0]),h(c[1]),h(c[2]),3===c.length?v:l(c[3])),g(t,e),e):void f(e,0,0,0,1);case"hsla":return 4!==c.length?void f(e,0,0,0,1):(c[3]=l(c[3]),_(c,e),g(t,e),e);case"hsl":return 3!==c.length?void f(e,0,0,0,1):(_(c,e),g(t,e),e);default:return}}f(e,0,0,0,1)}else{if(4===a||5===a){var y=parseInt(i.slice(1,4),16);return y>=0&&y<=4095?(f(e,(3840&y)>>4|(3840&y)>>8,240&y|(240&y)>>4,15&y|(15&y)<<4,5===a?parseInt(i.slice(4),16)/15:1),g(t,e),e):void f(e,0,0,0,1)}if(7===a||9===a){y=parseInt(i.slice(1,7),16);return y>=0&&y<=16777215?(f(e,(16711680&y)>>16,(65280&y)>>8,255&y,9===a?parseInt(i.slice(7),16)/255:1),g(t,e),e):void f(e,0,0,0,1)}}}}function _(t,e){var r=(parseFloat(t[0])%360+360)%360/360,i=l(t[1]),n=l(t[2]),o=n<=.5?n*(i+1):n+i-n*i,s=2*n-o;return e=e||[],f(e,a(255*u(s,o,r+1/3)),a(255*u(s,o,r)),a(255*u(s,o,r-1/3)),1),4===t.length&&(e[3]=t[3]),e}function m(t){if(t){var e,r,i=t[0]/255,n=t[1]/255,a=t[2]/255,o=Math.min(i,n,a),s=Math.max(i,n,a),h=s-o,l=(s+o)/2;if(0===h)e=0,r=0;else{r=l<.5?h/(s+o):h/(2-s-o);var u=((s-i)/6+h/2)/h,c=((s-n)/6+h/2)/h,f=((s-a)/6+h/2)/h;i===s?e=f-c:n===s?e=1/3+u-f:a===s&&(e=2/3+c-u),e<0&&(e+=1),e>1&&(e-=1)}var d=[360*e,r,l];return null!=t[3]&&d.push(t[3]),d}}function x(t,e){var r=y(t);if(r){for(var i=0;i<3;i++)r[i]=e<0?r[i]*(1-e)|0:(255-r[i])*e+r[i]|0,r[i]>255?r[i]=255:r[i]<0&&(r[i]=0);return A(r,4===r.length?"rgba":"rgb")}}function w(t){var e=y(t);if(e)return((1<<24)+(e[0]<<16)+(e[1]<<8)+ +e[2]).toString(16).slice(1)}function b(t,e,r){if(e&&e.length&&t>=0&&t<=1){r=r||[];var i=t*(e.length-1),n=Math.floor(i),o=Math.ceil(i),h=e[n],l=e[o],u=i-n;return r[0]=a(c(h[0],l[0],u)),r[1]=a(c(h[1],l[1],u)),r[2]=a(c(h[2],l[2],u)),r[3]=s(c(h[3],l[3],u)),r}}var k=/^(3491|4190|5903|6241)$/.test(r.j)?b:null;function T(t,e,r){if(e&&e.length&&t>=0&&t<=1){var i=t*(e.length-1),n=Math.floor(i),o=Math.ceil(i),h=y(e[n]),l=y(e[o]),u=i-n,f=A([a(c(h[0],l[0],u)),a(c(h[1],l[1],u)),a(c(h[2],l[2],u)),s(c(h[3],l[3],u))],"rgba");return r?{color:f,leftIndex:n,rightIndex:o,value:i}:f}}var S=/^(3491|4190|5903|6241)$/.test(r.j)?T:null;function C(t,e,r,i){var n=y(t);if(t)return n=m(n),null!=e&&(n[0]=o(e)),null!=r&&(n[1]=l(r)),null!=i&&(n[2]=l(i)),A(_(n),"rgba")}function P(t,e){var r=y(t);if(r&&null!=e)return r[3]=s(e),A(r,"rgba")}function A(t,e){if(t&&t.length){var r=t[0]+","+t[1]+","+t[2];return"rgba"!==e&&"hsva"!==e&&"hsla"!==e||(r+=","+t[3]),e+"("+r+")"}}function M(t,e){var r=y(t);return r?(.299*r[0]+.587*r[1]+.114*r[2])*r[3]/255+(1-r[3])*e:0}function L(){return A([Math.round(255*Math.random()),Math.round(255*Math.random()),Math.round(255*Math.random())],"rgb")}},80399:function(t,e,r){"use strict";if(r.d(e,{J:function(){return s},y:function(){return l}}),/^(3491|4190|5903|6241)$/.test(r.j))var i=r(54410);var n=r(72136),a=n.Z.CMD;function o(t,e){return Math.abs(t-e)<1e-5}function s(t){var e,r,i,n,s,h=t.data,l=t.len(),u=[],c=0,f=0,d=0,p=0;function v(t,r){e&&e.length>2&&u.push(e),e=[t,r]}function g(t,r,i,n){o(t,i)&&o(r,n)||e.push(t,r,i,n,i,n)}function y(t,r,i,n,a,o){var s=Math.abs(r-t),h=4*Math.tan(s/4)/3,l=r<t?-1:1,u=Math.cos(t),c=Math.sin(t),f=Math.cos(r),d=Math.sin(r),p=u*a+i,v=c*o+n,g=f*a+i,y=d*o+n,_=a*h*l,m=o*h*l;e.push(p-_*c,v+m*u,g+_*d,y-m*f,g,y)}for(var _=0;_<l;){var m=h[_++],x=1===_;switch(x&&(c=h[_],f=h[_+1],d=c,p=f,m!==a.L&&m!==a.C&&m!==a.Q||(e=[d,p])),m){case a.M:c=d=h[_++],f=p=h[_++],v(d,p);break;case a.L:r=h[_++],i=h[_++],g(c,f,r,i),c=r,f=i;break;case a.C:e.push(h[_++],h[_++],h[_++],h[_++],c=h[_++],f=h[_++]);break;case a.Q:r=h[_++],i=h[_++],n=h[_++],s=h[_++],e.push(c+2/3*(r-c),f+2/3*(i-f),n+2/3*(r-n),s+2/3*(i-s),n,s),c=n,f=s;break;case a.A:var w=h[_++],b=h[_++],k=h[_++],T=h[_++],S=h[_++],C=h[_++]+S;_+=1;var P=!h[_++];r=Math.cos(S)*k+w,i=Math.sin(S)*T+b,x?(d=r,p=i,v(d,p)):g(c,f,r,i),c=Math.cos(C)*k+w,f=Math.sin(C)*T+b;for(var A=(P?-1:1)*Math.PI/2,M=S;P?M>C:M<C;M+=A){var L=P?Math.max(M+A,C):Math.min(M+A,C);y(M,L,w,b,k,T)}break;case a.R:d=c=h[_++],p=f=h[_++],r=d+h[_++],i=p+h[_++],v(r,p),g(r,p,r,i),g(r,i,d,i),g(d,i,d,p),g(d,p,r,p);break;case a.Z:e&&g(c,f,d,p),c=d,f=p;break}}return e&&e.length>2&&u.push(e),u}function h(t,e,r,n,a,s,l,u,c,f){if(o(t,r)&&o(e,n)&&o(a,l)&&o(s,u))c.push(l,u);else{var d=2/f,p=d*d,v=l-t,g=u-e,y=Math.sqrt(v*v+g*g);v/=y,g/=y;var _=r-t,m=n-e,x=a-l,w=s-u,b=_*_+m*m,k=x*x+w*w;if(b<p&&k<p)c.push(l,u);else{var T=v*_+g*m,S=-v*x-g*w,C=b-T*T,P=k-S*S;if(C<p&&T>=0&&P<p&&S>=0)c.push(l,u);else{var A=[],M=[];(0,i.Vz)(t,r,a,l,.5,A),(0,i.Vz)(e,n,s,u,.5,M),h(A[0],M[0],A[1],M[1],A[2],M[2],A[3],M[3],c,f),h(A[4],M[4],A[5],M[5],A[6],M[6],A[7],M[7],c,f)}}}}function l(t,e){var r=s(t),i=[];e=e||1;for(var n=0;n<r.length;n++){var a=r[n],o=[],l=a[0],u=a[1];o.push(l,u);for(var c=2;c<a.length;){var f=a[c++],d=a[c++],p=a[c++],v=a[c++],g=a[c++],y=a[c++];h(l,u,f,d,p,v,g,y,o,e),l=g,u=y}i.push(o)}return i}},57859:function(t,e,r){"use strict";if(r.d(e,{V:function(){return S}}),/^(3491|4190|5903|6241)$/.test(r.j))var i=r(85036);if(/^(3491|4190|5903|6241)$/.test(r.j))var n=r(43498);if(/^(3491|4190|5903|6241)$/.test(r.j))var a=r(41284);if(/^(3491|4190|5903|6241)$/.test(r.j))var o=r(41239);if(/^(3491|4190|5903|6241)$/.test(r.j))var s=r(70295);if(/^(3491|4190|5903|6241)$/.test(r.j))var h=r(18732);if(/^(3491|4190|5903|6241)$/.test(r.j))var l=r(43524);if(/^(3491|4190|5903|6241)$/.test(r.j))var u=r(80399);if(/^(3491|4190|5903|6241)$/.test(r.j))var c=r(1514);function f(t,e,r){var i=t[e],n=t[1-e],a=Math.abs(i/n),o=Math.ceil(Math.sqrt(a*r)),s=Math.floor(r/o);0===s&&(s=1,o=r);for(var h=[],l=0;l<o;l++)h.push(s);var u=o*s,c=r-u;if(c>0)for(l=0;l<c;l++)h[l%o]+=1;return h}function d(t,e,r){for(var i=t.r0,n=t.r,a=t.startAngle,o=t.endAngle,s=Math.abs(o-a),h=s*n,l=n-i,u=h>Math.abs(l),c=f([h,l],u?0:1,e),d=(u?s:l)/c.length,p=0;p<c.length;p++)for(var v=(u?l:s)/c[p],g=0;g<c[p];g++){var y={};u?(y.startAngle=a+d*p,y.endAngle=a+d*(p+1),y.r0=i+v*g,y.r=i+v*(g+1)):(y.startAngle=a+v*g,y.endAngle=a+v*(g+1),y.r0=i+d*p,y.r=i+d*(p+1)),y.clockwise=t.clockwise,y.cx=t.cx,y.cy=t.cy,r.push(y)}}function p(t,e,r){for(var i=t.width,n=t.height,a=i>n,o=f([i,n],a?0:1,e),s=a?"width":"height",h=a?"height":"width",l=a?"x":"y",u=a?"y":"x",c=t[s]/o.length,d=0;d<o.length;d++)for(var p=t[h]/o[d],v=0;v<o[d];v++){var g={};g[l]=d*c,g[u]=v*p,g[s]=c,g[h]=p,g.x+=t.x,g.y+=t.y,r.push(g)}}function v(t,e,r,i){return t*i-r*e}function g(t,e,r,i,n,o,s,h){var l=r-t,u=i-e,c=s-n,f=h-o,d=v(c,f,l,u);if(Math.abs(d)<1e-6)return null;var p=t-n,g=e-o,y=v(p,g,c,f)/d;return y<0||y>1?null:new a.Z(y*l+t,y*u+e)}function y(t,e,r){var i=new a.Z;a.Z.sub(i,r,e),i.normalize();var n=new a.Z;a.Z.sub(n,t,e);var o=n.dot(i);return o}function _(t,e){var r=t[t.length-1];r&&r[0]===e[0]&&r[1]===e[1]||t.push(e)}function m(t,e,r){for(var i=t.length,n=[],a=0;a<i;a++){var o=t[a],s=t[(a+1)%i],h=g(o[0],o[1],s[0],s[1],e.x,e.y,r.x,r.y);h&&n.push({projPt:y(h,e,r),pt:h,idx:a})}if(n.length<2)return[{points:t},{points:t}];n.sort((function(t,e){return t.projPt-e.projPt}));var l=n[0],u=n[n.length-1];if(u.idx<l.idx){var c=l;l=u,u=c}var f=[l.pt.x,l.pt.y],d=[u.pt.x,u.pt.y],p=[f],v=[d];for(a=l.idx+1;a<=u.idx;a++)_(p,t[a].slice());_(p,d),_(p,f);for(a=u.idx+1;a<=l.idx+i;a++)_(v,t[a%i].slice());return _(v,f),_(v,d),[{points:p},{points:v}]}function x(t){var e=t.points,r=[],o=[];(0,i.zk)(e,r,o);var s=new n.Z(r[0],r[1],o[0]-r[0],o[1]-r[1]),h=s.width,l=s.height,u=s.x,c=s.y,f=new a.Z,d=new a.Z;return h>l?(f.x=d.x=u+h/2,f.y=c,d.y=c+l):(f.y=d.y=c+l/2,f.x=u,d.x=u+h),m(e,f,d)}function w(t,e,r,i){if(1===r)i.push(e);else{var n=Math.floor(r/2),a=t(e);w(t,a[0],n,i),w(t,a[1],r-n,i)}return i}function b(t,e){for(var r=[],i=0;i<e;i++)r.push((0,c.U5)(t));return r}function k(t,e){e.setStyle(t.style),e.z=t.z,e.z2=t.z2,e.zlevel=t.zlevel}function T(t){for(var e=[],r=0;r<t.length;)e.push([t[r++],t[r++]]);return e}function S(t,e){var r,n=[],a=t.shape;switch(t.type){case"rect":p(a,e,n),r=h.Z;break;case"sector":d(a,e,n),r=l.C;break;case"circle":d({r0:0,r:a.r,startAngle:0,endAngle:2*Math.PI,cx:a.cx,cy:a.cy},e,n),r=l.C;break;default:var c=t.getComputedTransform(),f=c?Math.sqrt(Math.max(c[0]*c[0]+c[1]*c[1],c[2]*c[2]+c[3]*c[3])):1,v=(0,o.map)((0,u.y)(t.getUpdatedPathProxy(),f),(function(t){return T(t)})),g=v.length;if(0===g)w(x,{points:v[0]},e,n);else if(g===e)for(var y=0;y<g;y++)n.push({points:v[y]});else{var _=0,m=(0,o.map)(v,(function(t){var e=[],r=[];(0,i.zk)(t,e,r);var n=(r[1]-e[1])*(r[0]-e[0]);return _+=n,{poly:t,area:n}}));m.sort((function(t,e){return e.area-t.area}));var S=e;for(y=0;y<g;y++){var C=m[y];if(S<=0)break;var P=y===g-1?S:Math.ceil(C.area/_*e);P<0||(w(x,{points:C.poly},P,n),S-=P)}}r=s.Z;break}if(!r)return b(t,e);var A=[];for(y=0;y<n.length;y++){var M=new r;M.setShape(n[y]),k(t,M),A.push(M)}return A}},57928:function(t,e,r){"use strict";if(r.d(e,{a4:function(){return M},j:function(){return _},nY:function(){return L},xQ:function(){return T}}),/^(3491|4190|5903|6241)$/.test(r.j))var i=r(54410);if(/^(3491|4190|5903|6241)$/.test(r.j))var n=r(54893);if(/^(3491|4190|5903|6241)$/.test(r.j))var a=r(41239);if(/^(3491|4190|5903|6241)$/.test(r.j))var o=r(77324);if(/^(3491|4190|5903|6241)$/.test(r.j))var s=r(1514);if(/^(3491|4190|5903|6241)$/.test(r.j))var h=r(71270);if(/^(3491|4190|5903|6241)$/.test(r.j))var l=r(57859);if(/^(3491|4190|5903|6241)$/.test(r.j))var u=r(80399);function c(t,e){var r=t.length,n=e.length;if(r===n)return[t,e];for(var a=[],o=[],s=r<n?t:e,h=Math.min(r,n),l=Math.abs(n-r)/6,u=(h-2)/6,c=Math.ceil(l/u)+1,f=[s[0],s[1]],d=l,p=2;p<h;){var v=s[p-2],g=s[p-1],y=s[p++],_=s[p++],m=s[p++],x=s[p++],w=s[p++],b=s[p++];if(d<=0)f.push(y,_,m,x,w,b);else{for(var k=Math.min(d,c-1)+1,T=1;T<=k;T++){var S=T/k;(0,i.Vz)(v,y,m,w,S,a),(0,i.Vz)(g,_,x,b,S,o),v=a[3],g=o[3],f.push(a[1],o[1],a[2],o[2],v,g),y=a[5],_=o[5],m=a[6],x=o[6]}d-=k-1}}return s===t?[f,e]:[t,f]}function f(t,e){for(var r=t.length,i=t[r-2],n=t[r-1],a=[],o=0;o<e.length;)a[o++]=i,a[o++]=n;return a}function d(t,e){for(var r,i,n,a=[],o=[],s=0;s<Math.max(t.length,e.length);s++){var h=t[s],l=e[s],u=void 0,d=void 0;h?l?(r=c(h,l),u=r[0],d=r[1],i=u,n=d):(d=f(n||h,h),u=h):(u=f(i||l,l),d=l),a.push(u),o.push(d)}return[a,o]}function p(t){for(var e=0,r=0,i=0,n=t.length,a=0,o=n-2;a<n;o=a,a+=2){var s=t[o],h=t[o+1],l=t[a],u=t[a+1],c=s*u-l*h;e+=c,r+=(s+l)*c,i+=(h+u)*c}return 0===e?[t[0]||0,t[1]||0]:[r/e/3,i/e/3,e]}function v(t,e,r,i){for(var n=(t.length-2)/6,a=1/0,o=0,s=t.length,h=s-2,l=0;l<n;l++){for(var u=6*l,c=0,f=0;f<s;f+=2){var d=0===f?u:(u+f-2)%h+2,p=t[d]-r[0],v=t[d+1]-r[1],g=e[f]-i[0],y=e[f+1]-i[1],_=g-p,m=y-v;c+=_*_+m*m}c<a&&(a=c,o=l)}return o}function g(t){for(var e=[],r=t.length,i=0;i<r;i+=2)e[i]=t[r-i-2],e[i+1]=t[r-i-1];return e}function y(t,e,r,i){for(var n,a=[],o=0;o<t.length;o++){var s=t[o],h=e[o],l=p(s),u=p(h);null==n&&(n=l[2]<0!==u[2]<0);var c=[],f=[],d=0,y=1/0,_=[],m=s.length;n&&(s=g(s));for(var x=6*v(s,h,l,u),w=m-2,b=0;b<w;b+=2){var k=(x+b)%w+2;c[b+2]=s[k]-l[0],c[b+3]=s[k+1]-l[1]}if(c[0]=s[x]-l[0],c[1]=s[x+1]-l[1],r>0)for(var T=i/r,S=-i/2;S<=i/2;S+=T){var C=Math.sin(S),P=Math.cos(S),A=0;for(b=0;b<s.length;b+=2){var M=c[b],L=c[b+1],D=h[b]-u[0],z=h[b+1]-u[1],O=D*P-z*C,R=D*C+z*P;_[b]=O,_[b+1]=R;var I=O-M,F=R-L;A+=I*I+F*F}if(A<y){y=A,d=S;for(var B=0;B<_.length;B++)f[B]=_[B]}}else for(var E=0;E<m;E+=2)f[E]=h[E]-u[0],f[E+1]=h[E+1]-u[1];a.push({from:c,to:f,fromCp:l,toCp:u,rotation:-d})}return a}function _(t){return t.__isCombineMorphing}var m="__mOriginal_";function x(t,e,r){var i=m+e,n=t[i]||t[e];t[i]||(t[i]=t[e]);var a=r.replace,o=r.after,s=r.before;t[e]=function(){var t,e=arguments;return s&&s.apply(this,e),t=a?a.apply(this,e):n.apply(this,e),o&&o.apply(this,e),t}}function w(t,e){var r=m+e;t[r]&&(t[e]=t[r],t[r]=null)}function b(t,e){for(var r=0;r<t.length;r++)for(var i=t[r],n=0;n<i.length;){var a=i[n],o=i[n+1];i[n++]=e[0]*a+e[2]*o+e[4],i[n++]=e[1]*a+e[3]*o+e[5]}}function k(t,e){var r=t.getUpdatedPathProxy(),i=e.getUpdatedPathProxy(),n=d((0,u.J)(r),(0,u.J)(i)),a=n[0],s=n[1],h=t.getComputedTransform(),l=e.getComputedTransform();function c(){this.transform=null}h&&b(a,h),l&&b(s,l),x(e,"updateTransform",{replace:c}),e.transform=null;var f=y(a,s,10,Math.PI),p=[];x(e,"buildPath",{replace:function(t){for(var r=e.__morphT,i=1-r,n=[],a=0;a<f.length;a++){var s=f[a],h=s.from,l=s.to,u=s.rotation*r,c=s.fromCp,d=s.toCp,v=Math.sin(u),g=Math.cos(u);(0,o.lerp)(n,c,d,r);for(var y=0;y<h.length;y+=2){var _=h[y],m=h[y+1],x=l[y],w=l[y+1],b=_*i+x*r,k=m*i+w*r;p[y]=b*g-k*v+n[0],p[y+1]=b*v+k*g+n[1]}var T=p[0],S=p[1];t.moveTo(T,S);for(y=2;y<h.length;){x=p[y++],w=p[y++];var C=p[y++],P=p[y++],A=p[y++],M=p[y++];T===x&&S===w&&C===A&&P===M?t.lineTo(A,M):t.bezierCurveTo(x,w,C,P,A,M),T=A,S=M}}}})}function T(t,e,r){if(!t||!e)return e;var i=r.done,n=r.during;function o(){w(e,"buildPath"),w(e,"updateTransform"),e.__morphT=-1,e.createPathProxy(),e.dirtyShape()}return k(t,e),e.__morphT=0,e.animateTo({__morphT:1},(0,a.defaults)({during:function(t){e.dirtyShape(),n&&n(t)},done:function(){o(),i&&i()}},r)),e}function S(t,e,r,i,n,a){var o=16;t=n===r?0:Math.round(32767*(t-r)/(n-r)),e=a===i?0:Math.round(32767*(e-i)/(a-i));for(var s,h=0,l=(1<<o)/2;l>0;l/=2){var u=0,c=0;(t&l)>0&&(u=1),(e&l)>0&&(c=1),h+=l*l*(3*u^c),0===c&&(1===u&&(t=l-1-t,e=l-1-e),s=t,t=e,e=s)}return h}function C(t){var e=1/0,r=1/0,i=-1/0,n=-1/0,o=(0,a.map)(t,(function(t){var a=t.getBoundingRect(),o=t.getComputedTransform(),s=a.x+a.width/2+(o?o[4]:0),h=a.y+a.height/2+(o?o[5]:0);return e=Math.min(s,e),r=Math.min(h,r),i=Math.max(s,i),n=Math.max(h,n),[s,h]})),s=(0,a.map)(o,(function(a,o){return{cp:a,z:S(a[0],a[1],e,r,i,n),path:t[o]}}));return s.sort((function(t,e){return t.z-e.z})).map((function(t){return t.path}))}function P(t){return(0,l.V)(t.path,t.count)}function A(){return{fromIndividuals:[],toIndividuals:[],count:0}}function M(t,e,r){var i=[];function o(t){for(var e=0;e<t.length;e++){var r=t[e];_(r)?o(r.childrenRef()):r instanceof n.ZP&&i.push(r)}}o(t);var s=i.length;if(!s)return A();var l=r.dividePath||P,u=l({path:e,count:s});if(u.length!==s)return A();i=C(i),u=C(u);for(var c=r.done,f=r.during,d=r.individualDelay,p=new h.ZP,v=0;v<s;v++){var g=i[v],y=u[v];y.parent=e,y.copyTransform(p),d||k(g,y)}function m(t){for(var e=0;e<u.length;e++)u[e].addSelfToZr(t)}function b(){e.__isCombineMorphing=!1,e.__morphT=-1,e.childrenRef=null,w(e,"addSelfToZr"),w(e,"removeSelfFromZr")}e.__isCombineMorphing=!0,e.childrenRef=function(){return u},x(e,"addSelfToZr",{after:function(t){m(t)}}),x(e,"removeSelfFromZr",{after:function(t){for(var e=0;e<u.length;e++)u[e].removeSelfFromZr(t)}});var S=u.length;if(d){var M=S,L=function(){M--,0===M&&(b(),c&&c())};for(v=0;v<S;v++){var D=d?(0,a.defaults)({delay:(r.delay||0)+d(v,S,i[v],u[v]),done:L},r):r;T(i[v],u[v],D)}}else e.__morphT=0,e.animateTo({__morphT:1},(0,a.defaults)({during:function(t){for(var r=0;r<S;r++){var i=u[r];i.__morphT=e.__morphT,i.dirtyShape()}f&&f(t)},done:function(){b();for(var e=0;e<t.length;e++)w(t[e],"updateTransform");c&&c()}},r));return e.__zr&&m(e.__zr),{fromIndividuals:i,toIndividuals:u,count:S}}function L(t,e,r){var i=e.length,o=[],h=r.dividePath||P;function l(t){for(var e=0;e<t.length;e++){var r=t[e];_(r)?l(r.childrenRef()):r instanceof n.ZP&&o.push(r)}}if(_(t)){l(t.childrenRef());var u=o.length;if(u<i)for(var c=0,f=u;f<i;f++)o.push((0,s.U5)(o[c++%u]));o.length=i}else{o=h({path:t,count:i});var d=t.getComputedTransform();for(f=0;f<o.length;f++)o[f].setLocalTransform(d);if(o.length!==i)return A()}o=C(o),e=C(e);var p=r.individualDelay;for(f=0;f<i;f++){var v=p?(0,a.defaults)({delay:(r.delay||0)+p(f,i,o[f],e[f])},r):r;T(o[f],e[f],v)}return{fromIndividuals:o,toIndividuals:e,count:e.length}}},82226:function(t,e,r){"use strict";r.d(e,{hS:function(){return W},sd:function(){return N}});var i,n=r(81747),a=r(34714),o=r(38963),s=r(18732),h=r(14549),l=r(26865),u=r(70295),c=r(22734),f=r(6366),d=r(1514),p=r(41239),v=r(1109),g=r(39555),y=r(74969),_=r(88015),m={fill:"fill",stroke:"stroke","stroke-width":"lineWidth",opacity:"opacity","fill-opacity":"fillOpacity","stroke-opacity":"strokeOpacity","stroke-dasharray":"lineDash","stroke-dashoffset":"lineDashOffset","stroke-linecap":"lineCap","stroke-linejoin":"lineJoin","stroke-miterlimit":"miterLimit","font-family":"fontFamily","font-size":"fontSize","font-style":"fontStyle","font-weight":"fontWeight","text-anchor":"textAlign",visibility:"visibility",display:"display"},x=(0,p.keys)(m),w={"alignment-baseline":"textBaseline","stop-color":"stopColor"},b=(0,p.keys)(w),k=function(){function t(){this._defs={},this._root=null}return t.prototype.parse=function(t,e){e=e||{};var r=(0,_.s)(t);this._defsUsePending=[];var i=new n.Z;this._root=i;var a=[],o=r.getAttribute("viewBox")||"",h=parseFloat(r.getAttribute("width")||e.width),l=parseFloat(r.getAttribute("height")||e.height);isNaN(h)&&(h=null),isNaN(l)&&(l=null),M(r,i,null,!0,!1);var u,c,f=r.firstChild;while(f)this._parseNode(f,i,a,null,!1,!1),f=f.nextSibling;if(O(this._defs,this._defsUsePending),this._defsUsePending=[],o){var d=I(o);d.length>=4&&(u={x:parseFloat(d[0]||0),y:parseFloat(d[1]||0),width:parseFloat(d[2]),height:parseFloat(d[3])})}if(u&&null!=h&&null!=l&&(c=N(u,{x:0,y:0,width:h,height:l}),!e.ignoreViewBox)){var p=i;i=new n.Z,i.add(p),p.scaleX=p.scaleY=c.scale,p.x=c.x,p.y=c.y}return e.ignoreRootClip||null==h||null==l||i.setClipPath(new s.Z({shape:{x:0,y:0,width:h,height:l}})),{root:i,width:h,height:l,viewBoxRect:u,viewBoxTransform:c,named:a}},t.prototype._parseNode=function(t,e,r,n,a,o){var s,h=t.nodeName.toLowerCase(),l=n;if("defs"===h&&(a=!0),"text"===h&&(o=!0),"defs"===h||"switch"===h)s=e;else{if(!a){var u=i[h];if(u&&(0,p.hasOwn)(i,h)){s=u.call(this,t,e);var c=t.getAttribute("name");if(c){var f={name:c,namedFrom:null,svgNodeTagLower:h,el:s};r.push(f),"g"===h&&(l=f)}else n&&r.push({name:n.name,namedFrom:n,svgNodeTagLower:h,el:s});e.add(s)}}var d=T[h];if(d&&(0,p.hasOwn)(T,h)){var v=d.call(this,t),g=t.getAttribute("id");g&&(this._defs[g]=v)}}if(s&&s.isGroup){var y=t.firstChild;while(y)1===y.nodeType?this._parseNode(y,s,r,l,a,o):3===y.nodeType&&o&&this._parseText(y,s),y=y.nextSibling}},t.prototype._parseText=function(t,e){var r=new y.Z({style:{text:t.textContent},silent:!0,x:this._textX||0,y:this._textY||0});P(e,r),M(t,r,this._defsUsePending,!1,!1),L(r,e);var i=r.style,n=i.fontSize;n&&n<9&&(i.fontSize=9,r.scaleX*=n/9,r.scaleY*=n/9);var a=(i.fontSize||i.fontFamily)&&[i.fontStyle,i.fontWeight,(i.fontSize||12)+"px",i.fontFamily||"sans-serif"].join(" ");i.font=a;var o=r.getBoundingRect();return this._textX+=o.width,e.add(r),r},t.internalField=function(){i={g:function(t,e){var r=new n.Z;return P(e,r),M(t,r,this._defsUsePending,!1,!1),r},rect:function(t,e){var r=new s.Z;return P(e,r),M(t,r,this._defsUsePending,!1,!1),r.setShape({x:parseFloat(t.getAttribute("x")||"0"),y:parseFloat(t.getAttribute("y")||"0"),width:parseFloat(t.getAttribute("width")||"0"),height:parseFloat(t.getAttribute("height")||"0")}),r.silent=!0,r},circle:function(t,e){var r=new o.Z;return P(e,r),M(t,r,this._defsUsePending,!1,!1),r.setShape({cx:parseFloat(t.getAttribute("cx")||"0"),cy:parseFloat(t.getAttribute("cy")||"0"),r:parseFloat(t.getAttribute("r")||"0")}),r.silent=!0,r},line:function(t,e){var r=new l.Z;return P(e,r),M(t,r,this._defsUsePending,!1,!1),r.setShape({x1:parseFloat(t.getAttribute("x1")||"0"),y1:parseFloat(t.getAttribute("y1")||"0"),x2:parseFloat(t.getAttribute("x2")||"0"),y2:parseFloat(t.getAttribute("y2")||"0")}),r.silent=!0,r},ellipse:function(t,e){var r=new h.Z;return P(e,r),M(t,r,this._defsUsePending,!1,!1),r.setShape({cx:parseFloat(t.getAttribute("cx")||"0"),cy:parseFloat(t.getAttribute("cy")||"0"),rx:parseFloat(t.getAttribute("rx")||"0"),ry:parseFloat(t.getAttribute("ry")||"0")}),r.silent=!0,r},polygon:function(t,e){var r,i=t.getAttribute("points");i&&(r=A(i));var n=new u.Z({shape:{points:r||[]},silent:!0});return P(e,n),M(t,n,this._defsUsePending,!1,!1),n},polyline:function(t,e){var r,i=t.getAttribute("points");i&&(r=A(i));var n=new c.Z({shape:{points:r||[]},silent:!0});return P(e,n),M(t,n,this._defsUsePending,!1,!1),n},image:function(t,e){var r=new a.ZP;return P(e,r),M(t,r,this._defsUsePending,!1,!1),r.setStyle({image:t.getAttribute("xlink:href")||t.getAttribute("href"),x:+t.getAttribute("x"),y:+t.getAttribute("y"),width:+t.getAttribute("width"),height:+t.getAttribute("height")}),r.silent=!0,r},text:function(t,e){var r=t.getAttribute("x")||"0",i=t.getAttribute("y")||"0",a=t.getAttribute("dx")||"0",o=t.getAttribute("dy")||"0";this._textX=parseFloat(r)+parseFloat(a),this._textY=parseFloat(i)+parseFloat(o);var s=new n.Z;return P(e,s),M(t,s,this._defsUsePending,!1,!0),s},tspan:function(t,e){var r=t.getAttribute("x"),i=t.getAttribute("y");null!=r&&(this._textX=parseFloat(r)),null!=i&&(this._textY=parseFloat(i));var a=t.getAttribute("dx")||"0",o=t.getAttribute("dy")||"0",s=new n.Z;return P(e,s),M(t,s,this._defsUsePending,!1,!0),this._textX+=parseFloat(a),this._textY+=parseFloat(o),s},path:function(t,e){var r=t.getAttribute("d")||"",i=(0,d.iR)(r);return P(e,i),M(t,i,this._defsUsePending,!1,!1),i.silent=!0,i}}}(),t}(),T={lineargradient:function(t){var e=parseInt(t.getAttribute("x1")||"0",10),r=parseInt(t.getAttribute("y1")||"0",10),i=parseInt(t.getAttribute("x2")||"10",10),n=parseInt(t.getAttribute("y2")||"0",10),a=new v.Z(e,r,i,n);return S(t,a),C(t,a),a},radialgradient:function(t){var e=parseInt(t.getAttribute("cx")||"0",10),r=parseInt(t.getAttribute("cy")||"0",10),i=parseInt(t.getAttribute("r")||"0",10),n=new g.Z(e,r,i);return S(t,n),C(t,n),n}};function S(t,e){var r=t.getAttribute("gradientUnits");"userSpaceOnUse"===r&&(e.global=!0)}function C(t,e){var r=t.firstChild;while(r){if(1===r.nodeType&&"stop"===r.nodeName.toLocaleLowerCase()){var i=r.getAttribute("offset"),n=void 0;n=i&&i.indexOf("%")>0?parseInt(i,10)/100:i?parseFloat(i):0;var a={};j(r,a,a);var o=a.stopColor||r.getAttribute("stop-color")||"#000000";e.colorStops.push({offset:n,color:o})}r=r.nextSibling}}function P(t,e){t&&t.__inheritedStyle&&(e.__inheritedStyle||(e.__inheritedStyle={}),(0,p.defaults)(e.__inheritedStyle,t.__inheritedStyle))}function A(t){for(var e=I(t),r=[],i=0;i<e.length;i+=2){var n=parseFloat(e[i]),a=parseFloat(e[i+1]);r.push([n,a])}return r}function M(t,e,r,i,n){var a=e,o=a.__inheritedStyle=a.__inheritedStyle||{},s={};1===t.nodeType&&(E(t,e),j(t,o,s),i||H(t,o,s)),a.style=a.style||{},null!=o.fill&&(a.style.fill=z(a,"fill",o.fill,r)),null!=o.stroke&&(a.style.stroke=z(a,"stroke",o.stroke,r)),(0,p.each)(["lineWidth","opacity","fillOpacity","strokeOpacity","miterLimit","fontSize"],(function(t){null!=o[t]&&(a.style[t]=parseFloat(o[t]))})),(0,p.each)(["lineDashOffset","lineCap","lineJoin","fontWeight","fontFamily","fontStyle","textAlign"],(function(t){null!=o[t]&&(a.style[t]=o[t])})),n&&(a.__selfStyle=s),o.lineDash&&(a.style.lineDash=(0,p.map)(I(o.lineDash),(function(t){return parseFloat(t)}))),"hidden"!==o.visibility&&"collapse"!==o.visibility||(a.invisible=!0),"none"===o.display&&(a.ignore=!0)}function L(t,e){var r=e.__selfStyle;if(r){var i=r.textBaseline,n=i;i&&"auto"!==i?"baseline"===i?n="alphabetic":"before-edge"===i||"text-before-edge"===i?n="top":"after-edge"===i||"text-after-edge"===i?n="bottom":"central"!==i&&"mathematical"!==i||(n="middle"):n="alphabetic",t.style.textBaseline=n}var a=e.__inheritedStyle;if(a){var o=a.textAlign,s=o;o&&("middle"===o&&(s="center"),t.style.textAlign=s)}}var D=/^url\(\s*#(.*?)\)/;function z(t,e,r,i){var n=r&&r.match(D);if(!n)return"none"===r&&(r=null),r;var a=(0,p.trim)(n[1]);i.push([t,e,a])}function O(t,e){for(var r=0;r<e.length;r++){var i=e[r];i[0].style[i[1]]=t[i[2]]}}var R=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;function I(t){return t.match(R)||[]}var F=/(translate|scale|rotate|skewX|skewY|matrix)\(([\-\s0-9\.eE,]*)\)/g,B=Math.PI/180;function E(t,e){var r=t.getAttribute("transform");if(r){r=r.replace(/,/g," ");var i=[],n=null;r.replace(F,(function(t,e,r){return i.push(e,r),""}));for(var a=i.length-1;a>0;a-=2){var o=i[a],s=i[a-1],h=I(o);switch(n=n||f.create(),s){case"translate":f.translate(n,n,[parseFloat(h[0]),parseFloat(h[1]||"0")]);break;case"scale":f.scale(n,n,[parseFloat(h[0]),parseFloat(h[1]||h[0])]);break;case"rotate":f.rotate(n,n,-parseFloat(h[0])*B);break;case"skewX":var l=Math.tan(parseFloat(h[0])*B);f.mul(n,[1,0,l,1,0,0],n);break;case"skewY":var u=Math.tan(parseFloat(h[0])*B);f.mul(n,[1,u,0,1,0,0],n);break;case"matrix":n[0]=parseFloat(h[0]),n[1]=parseFloat(h[1]),n[2]=parseFloat(h[2]),n[3]=parseFloat(h[3]),n[4]=parseFloat(h[4]),n[5]=parseFloat(h[5]);break}}e.setLocalTransform(n)}}var Z=/([^\s:;]+)\s*:\s*([^:;]+)/g;function j(t,e,r){var i=t.getAttribute("style");if(i){var n;Z.lastIndex=0;while(null!=(n=Z.exec(i))){var a=n[1],o=(0,p.hasOwn)(m,a)?m[a]:null;o&&(e[o]=n[2]);var s=(0,p.hasOwn)(w,a)?w[a]:null;s&&(r[s]=n[2])}}}function H(t,e,r){for(var i=0;i<x.length;i++){var n=x[i],a=t.getAttribute(n);null!=a&&(e[m[n]]=a)}for(i=0;i<b.length;i++){n=b[i],a=t.getAttribute(n);null!=a&&(r[w[n]]=a)}}function N(t,e){var r=e.width/t.width,i=e.height/t.height,n=Math.min(r,i);return{scale:n,x:-(t.x+t.width/2)*n+(e.x+e.width/2),y:-(t.y+t.height/2)*n+(e.y+e.height/2)}}function W(t,e){var r=new k;return r.parse(t,e)}},88015:function(t,e,r){"use strict";if(r.d(e,{s:function(){return n}}),/^(3491|4190|5903|6241)$/.test(r.j))var i=r(41239);function n(t){if((0,i.isString)(t)){var e=new DOMParser;t=e.parseFromString(t,"text/xml")}var r=t;9===r.nodeType&&(r=r.firstChild);while("svg"!==r.nodeName.toLowerCase()||1!==r.nodeType)r=r.nextSibling;return r}},1514:function(t,e,r){"use strict";r.d(e,{AA:function(){return T},Pc:function(){return k},U5:function(){return S},iR:function(){return b}});var i=r(61431),n=r(54893);if(/^(3491|4190|5903|6241)$/.test(r.j))var a=r(72136);if(/^(3491|4190|5903|6241)$/.test(r.j))var o=r(7169);if(/^(3491|4190|5903|6241)$/.test(r.j))var s=r(41239);var h=Math.sqrt,l=Math.sin,u=Math.cos,c=Math.PI;function f(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])}function d(t,e){return(t[0]*e[0]+t[1]*e[1])/(f(t)*f(e))}function p(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(d(t,e))}function v(t,e,r,i,n,a,o,s,f,v,g){var y=f*(c/180),_=u(y)*(t-r)/2+l(y)*(e-i)/2,m=-1*l(y)*(t-r)/2+u(y)*(e-i)/2,x=_*_/(o*o)+m*m/(s*s);x>1&&(o*=h(x),s*=h(x));var w=(n===a?-1:1)*h((o*o*(s*s)-o*o*(m*m)-s*s*(_*_))/(o*o*(m*m)+s*s*(_*_)))||0,b=w*o*m/s,k=w*-s*_/o,T=(t+r)/2+u(y)*b-l(y)*k,S=(e+i)/2+l(y)*b+u(y)*k,C=p([1,0],[(_-b)/o,(m-k)/s]),P=[(_-b)/o,(m-k)/s],A=[(-1*_-b)/o,(-1*m-k)/s],M=p(P,A);if(d(P,A)<=-1&&(M=c),d(P,A)>=1&&(M=0),M<0){var L=Math.round(M/c*1e6)/1e6;M=2*c+L%2*c}g.addData(v,T,S,o,s,C,M,y,a)}var g=/([mlvhzcqtsa])([^mlvhzcqtsa]*)/gi,y=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;function _(t){var e=new a.Z;if(!t)return e;var r,i=0,n=0,o=i,s=n,h=a.Z.CMD,l=t.match(g);if(!l)return e;for(var u=0;u<l.length;u++){for(var c=l[u],f=c.charAt(0),d=void 0,p=c.match(y)||[],_=p.length,m=0;m<_;m++)p[m]=parseFloat(p[m]);var x=0;while(x<_){var w=void 0,b=void 0,k=void 0,T=void 0,S=void 0,C=void 0,P=void 0,A=i,M=n,L=void 0,D=void 0;switch(f){case"l":i+=p[x++],n+=p[x++],d=h.L,e.addData(d,i,n);break;case"L":i=p[x++],n=p[x++],d=h.L,e.addData(d,i,n);break;case"m":i+=p[x++],n+=p[x++],d=h.M,e.addData(d,i,n),o=i,s=n,f="l";break;case"M":i=p[x++],n=p[x++],d=h.M,e.addData(d,i,n),o=i,s=n,f="L";break;case"h":i+=p[x++],d=h.L,e.addData(d,i,n);break;case"H":i=p[x++],d=h.L,e.addData(d,i,n);break;case"v":n+=p[x++],d=h.L,e.addData(d,i,n);break;case"V":n=p[x++],d=h.L,e.addData(d,i,n);break;case"C":d=h.C,e.addData(d,p[x++],p[x++],p[x++],p[x++],p[x++],p[x++]),i=p[x-2],n=p[x-1];break;case"c":d=h.C,e.addData(d,p[x++]+i,p[x++]+n,p[x++]+i,p[x++]+n,p[x++]+i,p[x++]+n),i+=p[x-2],n+=p[x-1];break;case"S":w=i,b=n,L=e.len(),D=e.data,r===h.C&&(w+=i-D[L-4],b+=n-D[L-3]),d=h.C,A=p[x++],M=p[x++],i=p[x++],n=p[x++],e.addData(d,w,b,A,M,i,n);break;case"s":w=i,b=n,L=e.len(),D=e.data,r===h.C&&(w+=i-D[L-4],b+=n-D[L-3]),d=h.C,A=i+p[x++],M=n+p[x++],i+=p[x++],n+=p[x++],e.addData(d,w,b,A,M,i,n);break;case"Q":A=p[x++],M=p[x++],i=p[x++],n=p[x++],d=h.Q,e.addData(d,A,M,i,n);break;case"q":A=p[x++]+i,M=p[x++]+n,i+=p[x++],n+=p[x++],d=h.Q,e.addData(d,A,M,i,n);break;case"T":w=i,b=n,L=e.len(),D=e.data,r===h.Q&&(w+=i-D[L-4],b+=n-D[L-3]),i=p[x++],n=p[x++],d=h.Q,e.addData(d,w,b,i,n);break;case"t":w=i,b=n,L=e.len(),D=e.data,r===h.Q&&(w+=i-D[L-4],b+=n-D[L-3]),i+=p[x++],n+=p[x++],d=h.Q,e.addData(d,w,b,i,n);break;case"A":k=p[x++],T=p[x++],S=p[x++],C=p[x++],P=p[x++],A=i,M=n,i=p[x++],n=p[x++],d=h.A,v(A,M,i,n,C,P,k,T,S,d,e);break;case"a":k=p[x++],T=p[x++],S=p[x++],C=p[x++],P=p[x++],A=i,M=n,i+=p[x++],n+=p[x++],d=h.A,v(A,M,i,n,C,P,k,T,S,d,e);break}}"z"!==f&&"Z"!==f||(d=h.Z,e.addData(d),i=o,n=s),r=d}return e.toStatic(),e}var m=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return(0,i.ZT)(e,t),e.prototype.applyTransform=function(t){},e}(n.ZP);function x(t){return null!=t.setData}function w(t,e){var r=_(t),i=(0,s.extend)({},e);return i.buildPath=function(t){if(x(t)){t.setData(r.data);var e=t.getContext();e&&t.rebuildPath(e,1)}else{e=t;r.rebuildPath(e,1)}},i.applyTransform=function(t){(0,o.Z)(r,t),this.dirtyShape()},i}function b(t,e){return new m(w(t,e))}function k(t,e){var r=w(t,e),n=function(t){function e(e){var i=t.call(this,e)||this;return i.applyTransform=r.applyTransform,i.buildPath=r.buildPath,i}return(0,i.ZT)(e,t),e}(m);return n}function T(t,e){for(var r=[],i=t.length,a=0;a<i;a++){var o=t[a];r.push(o.getUpdatedPathProxy(!0))}var s=new n.ZP(e);return s.createPathProxy(),s.buildPath=function(t){if(x(t)){t.appendPath(r);var e=t.getContext();e&&t.rebuildPath(e,1)}},s}function S(t,e){e=e||{};var r=new n.ZP;return t.shape&&r.setShape(t.shape),r.setStyle(t.style),e.bakeTransform?(0,o.Z)(r.path,t.getComputedTransform()):e.toLocal?r.setLocalTransform(t.getComputedTransform()):r.copyTransform(t),r.buildPath=t.buildPath,r.applyTransform=r.applyTransform,r.z=t.z,r.z2=t.z2,r.zlevel=t.zlevel,r}},7169:function(t,e,r){"use strict";r.d(e,{Z:function(){return l}});var i=r(72136);if(/^(3491|4190|5903|6241)$/.test(r.j))var n=r(77324);var a=i.Z.CMD,o=/^(3491|4190|5903|6241)$/.test(r.j)?[[],[],[]]:null,s=Math.sqrt,h=Math.atan2;function l(t,e){if(e){var r,i,l,u,c,f,d=t.data,p=t.len(),v=a.M,g=a.C,y=a.L,_=a.R,m=a.A,x=a.Q;for(l=0,u=0;l<p;){switch(r=d[l++],u=l,i=0,r){case v:i=1;break;case y:i=1;break;case g:i=3;break;case x:i=2;break;case m:var w=e[4],b=e[5],k=s(e[0]*e[0]+e[1]*e[1]),T=s(e[2]*e[2]+e[3]*e[3]),S=h(-e[1]/T,e[0]/k);d[l]*=k,d[l++]+=w,d[l]*=T,d[l++]+=b,d[l++]*=k,d[l++]*=T,d[l++]+=S,d[l++]+=S,l+=2,u=l;break;case _:f[0]=d[l++],f[1]=d[l++],(0,n.applyTransform)(f,f,e),d[u++]=f[0],d[u++]=f[1],f[0]+=d[l++],f[1]+=d[l++],(0,n.applyTransform)(f,f,e),d[u++]=f[0],d[u++]=f[1]}for(c=0;c<i;c++){var C=o[c];C[0]=d[l++],C[1]=d[l++],(0,n.applyTransform)(C,C,e),d[u++]=C[0],d[u++]=C[1]}}t.increaseVersion()}}},39403:function(t,e,r){"use strict";r.r(e),r.d(e,{dispose:function(){return gt},disposeAll:function(){return yt},getInstance:function(){return _t},init:function(){return vt},registerPainter:function(){return mt},version:function(){return xt}});var i=r(84754),n=r(41239),a=r(61431),o=r(77324),s=function(){function t(t,e){this.target=t,this.topTarget=e&&e.topTarget}return t}(),h=function(){function t(t){this.handler=t,t.on("mousedown",this._dragStart,this),t.on("mousemove",this._drag,this),t.on("mouseup",this._dragEnd,this)}return t.prototype._dragStart=function(t){var e=t.target;while(e&&!e.draggable)e=e.parent||e.__hostTarget;e&&(this._draggingTarget=e,e.dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.handler.dispatchToElement(new s(e,t),"dragstart",t.event))},t.prototype._drag=function(t){var e=this._draggingTarget;if(e){var r=t.offsetX,i=t.offsetY,n=r-this._x,a=i-this._y;this._x=r,this._y=i,e.drift(n,a,t),this.handler.dispatchToElement(new s(e,t),"drag",t.event);var o=this.handler.findHover(r,i,e).target,h=this._dropTarget;this._dropTarget=o,e!==o&&(h&&o!==h&&this.handler.dispatchToElement(new s(h,t),"dragleave",t.event),o&&o!==h&&this.handler.dispatchToElement(new s(o,t),"dragenter",t.event))}},t.prototype._dragEnd=function(t){var e=this._draggingTarget;e&&(e.dragging=!1),this.handler.dispatchToElement(new s(e,t),"dragend",t.event),this._dropTarget&&this.handler.dispatchToElement(new s(this._dropTarget,t),"drop",t.event),this._draggingTarget=null,this._dropTarget=null},t}(),l=h,u=r(94886),c=r(37914),f=function(){function t(){this._track=[]}return t.prototype.recognize=function(t,e,r){return this._doTrack(t,e,r),this._recognize(t)},t.prototype.clear=function(){return this._track.length=0,this},t.prototype._doTrack=function(t,e,r){var i=t.touches;if(i){for(var n={points:[],touches:[],target:e,event:t},a=0,o=i.length;a<o;a++){var s=i[a],h=c.eV(r,s,{});n.points.push([h.zrX,h.zrY]),n.touches.push(s)}this._track.push(n)}},t.prototype._recognize=function(t){for(var e in v)if(v.hasOwnProperty(e)){var r=v[e](this._track,t);if(r)return r}},t}();function d(t){var e=t[1][0]-t[0][0],r=t[1][1]-t[0][1];return Math.sqrt(e*e+r*r)}function p(t){return[(t[0][0]+t[1][0])/2,(t[0][1]+t[1][1])/2]}var v={pinch:function(t,e){var r=t.length;if(r){var i=(t[r-1]||{}).points,n=(t[r-2]||{}).points||i;if(n&&n.length>1&&i&&i.length>1){var a=d(i)/d(n);!isFinite(a)&&(a=1),e.pinchScale=a;var o=p(i);return e.pinchX=o[0],e.pinchY=o[1],{type:"pinch",target:t[0].target,event:e}}}}},g=r(43498),y="silent";function _(t,e,r){return{type:t,event:r,target:e.target,topTarget:e.topTarget,cancelBubble:!1,offsetX:r.zrX,offsetY:r.zrY,gestureEvent:r.gestureEvent,pinchX:r.pinchX,pinchY:r.pinchY,pinchScale:r.pinchScale,wheelDelta:r.zrDelta,zrByTouch:r.zrByTouch,which:r.which,stop:m}}function m(){c.sT(this.event)}var x=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.handler=null,e}return(0,a.ZT)(e,t),e.prototype.dispose=function(){},e.prototype.setCursor=function(){},e}(u.Z),w=function(){function t(t,e){this.x=t,this.y=e}return t}(),b=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],k=new g.Z(0,0,0,0),T=function(t){function e(e,r,i,n,a){var o=t.call(this)||this;return o._hovered=new w(0,0),o.storage=e,o.painter=r,o.painterRoot=n,o._pointerSize=a,i=i||new x,o.proxy=null,o.setHandlerProxy(i),o._draggingMgr=new l(o),o}return(0,a.ZT)(e,t),e.prototype.setHandlerProxy=function(t){this.proxy&&this.proxy.dispose(),t&&(n.each(b,(function(e){t.on&&t.on(e,this[e],this)}),this),t.handler=this),this.proxy=t},e.prototype.mousemove=function(t){var e=t.zrX,r=t.zrY,i=P(this,e,r),n=this._hovered,a=n.target;a&&!a.__zr&&(n=this.findHover(n.x,n.y),a=n.target);var o=this._hovered=i?new w(e,r):this.findHover(e,r),s=o.target,h=this.proxy;h.setCursor&&h.setCursor(s?s.cursor:"default"),a&&s!==a&&this.dispatchToElement(n,"mouseout",t),this.dispatchToElement(o,"mousemove",t),s&&s!==a&&this.dispatchToElement(o,"mouseover",t)},e.prototype.mouseout=function(t){var e=t.zrEventControl;"only_globalout"!==e&&this.dispatchToElement(this._hovered,"mouseout",t),"no_globalout"!==e&&this.trigger("globalout",{type:"globalout",event:t})},e.prototype.resize=function(){this._hovered=new w(0,0)},e.prototype.dispatch=function(t,e){var r=this[t];r&&r.call(this,e)},e.prototype.dispose=function(){this.proxy.dispose(),this.storage=null,this.proxy=null,this.painter=null},e.prototype.setCursorStyle=function(t){var e=this.proxy;e.setCursor&&e.setCursor(t)},e.prototype.dispatchToElement=function(t,e,r){t=t||{};var i=t.target;if(!i||!i.silent){var n="on"+e,a=_(e,t,r);while(i)if(i[n]&&(a.cancelBubble=!!i[n].call(i,a)),i.trigger(e,a),i=i.__hostTarget?i.__hostTarget:i.parent,a.cancelBubble)break;a.cancelBubble||(this.trigger(e,a),this.painter&&this.painter.eachOtherLayer&&this.painter.eachOtherLayer((function(t){"function"===typeof t[n]&&t[n].call(t,a),t.trigger&&t.trigger(e,a)})))}},e.prototype.findHover=function(t,e,r){var i=this.storage.getDisplayList(),n=new w(t,e);if(C(i,n,t,e,r),this._pointerSize&&!n.target){for(var a=[],o=this._pointerSize,s=o/2,h=new g.Z(t-s,e-s,o,o),l=i.length-1;l>=0;l--){var u=i[l];u===r||u.ignore||u.ignoreCoarsePointer||u.parent&&u.parent.ignoreCoarsePointer||(k.copy(u.getBoundingRect()),u.transform&&k.applyTransform(u.transform),k.intersect(h)&&a.push(u))}if(a.length)for(var c=4,f=Math.PI/12,d=2*Math.PI,p=0;p<s;p+=c)for(var v=0;v<d;v+=f){var y=t+p*Math.cos(v),_=e+p*Math.sin(v);if(C(a,n,y,_,r),n.target)return n}}return n},e.prototype.processGesture=function(t,e){this._gestureMgr||(this._gestureMgr=new f);var r=this._gestureMgr;"start"===e&&r.clear();var i=r.recognize(t,this.findHover(t.zrX,t.zrY,null).target,this.proxy.dom);if("end"===e&&r.clear(),i){var n=i.type;t.gestureEvent=n;var a=new w;a.target=i.target,this.dispatchToElement(a,n,i.event)}},e}(u.Z);function S(t,e,r){if(t[t.rectHover?"rectContain":"contain"](e,r)){var i=t,n=void 0,a=!1;while(i){if(i.ignoreClip&&(a=!0),!a){var o=i.getClipPath();if(o&&!o.contain(e,r))return!1;i.silent&&(n=!0)}var s=i.__hostTarget;i=s||i.parent}return!n||y}return!1}function C(t,e,r,i,n){for(var a=t.length-1;a>=0;a--){var o=t[a],s=void 0;if(o!==n&&!o.ignore&&(s=S(o,r,i))&&(!e.topTarget&&(e.topTarget=o),s!==y)){e.target=o;break}}}function P(t,e,r){var i=t.painter;return e<0||e>i.getWidth()||r<0||r>i.getHeight()}n.each(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],(function(t){T.prototype[t]=function(e){var r,i,n=e.zrX,a=e.zrY,s=P(this,n,a);if("mouseup"===t&&s||(r=this.findHover(n,a),i=r.target),"mousedown"===t)this._downEl=i,this._downPoint=[e.zrX,e.zrY],this._upEl=i;else if("mouseup"===t)this._upEl=i;else if("click"===t){if(this._downEl!==this._upEl||!this._downPoint||o.dist(this._downPoint,[e.zrX,e.zrY])>4)return;this._downPoint=null}this.dispatchToElement(r,t,e)}}));var A=T,M=r(57795),L=r(64562),D=!1;function z(){D||(D=!0)}function O(t,e){return t.zlevel===e.zlevel?t.z===e.z?t.z2-e.z2:t.z-e.z:t.zlevel-e.zlevel}var R=function(){function t(){this._roots=[],this._displayList=[],this._displayListLen=0,this.displayableSortFunc=O}return t.prototype.traverse=function(t,e){for(var r=0;r<this._roots.length;r++)this._roots[r].traverse(t,e)},t.prototype.getDisplayList=function(t,e){e=e||!1;var r=this._displayList;return!t&&r.length||this.updateDisplayList(e),r},t.prototype.updateDisplayList=function(t){this._displayListLen=0;for(var e=this._roots,r=this._displayList,i=0,n=e.length;i<n;i++)this._updateAndAddDisplayable(e[i],null,t);r.length=this._displayListLen,(0,M.Z)(r,O)},t.prototype._updateAndAddDisplayable=function(t,e,r){if(!t.ignore||r){t.beforeUpdate(),t.update(),t.afterUpdate();var i=t.getClipPath();if(t.ignoreClip)e=null;else if(i){e=e?e.slice():[];var n=i,a=t;while(n)n.parent=a,n.updateTransform(),e.push(n),a=n,n=n.getClipPath()}if(t.childrenRef){for(var o=t.childrenRef(),s=0;s<o.length;s++){var h=o[s];t.__dirty&&(h.__dirty|=L.YV),this._updateAndAddDisplayable(h,e,r)}t.__dirty=0}else{var l=t;e&&e.length?l.__clipPaths=e:l.__clipPaths&&l.__clipPaths.length>0&&(l.__clipPaths=[]),isNaN(l.z)&&(z(),l.z=0),isNaN(l.z2)&&(z(),l.z2=0),isNaN(l.zlevel)&&(z(),l.zlevel=0),this._displayList[this._displayListLen++]=l}var u=t.getDecalElement&&t.getDecalElement();u&&this._updateAndAddDisplayable(u,e,r);var c=t.getTextGuideLine();c&&this._updateAndAddDisplayable(c,e,r);var f=t.getTextContent();f&&this._updateAndAddDisplayable(f,e,r)}},t.prototype.addRoot=function(t){t.__zr&&t.__zr.storage===this||this._roots.push(t)},t.prototype.delRoot=function(t){if(t instanceof Array)for(var e=0,r=t.length;e<r;e++)this.delRoot(t[e]);else{var i=n.indexOf(this._roots,t);i>=0&&this._roots.splice(i,1)}},t.prototype.delAllRoots=function(){this._roots=[],this._displayList=[],this._displayListLen=0},t.prototype.getRoots=function(){return this._roots},t.prototype.dispose=function(){this._displayList=null,this._roots=null},t}(),I=R,F=r(43207),B=r(9567);function E(){return(new Date).getTime()}var Z=function(t){function e(e){var r=t.call(this)||this;return r._running=!1,r._time=0,r._pausedTime=0,r._pauseStart=0,r._paused=!1,e=e||{},r.stage=e.stage||{},r}return(0,a.ZT)(e,t),e.prototype.addClip=function(t){t.animation&&this.removeClip(t),this._head?(this._tail.next=t,t.prev=this._tail,t.next=null,this._tail=t):this._head=this._tail=t,t.animation=this},e.prototype.addAnimator=function(t){t.animation=this;var e=t.getClip();e&&this.addClip(e)},e.prototype.removeClip=function(t){if(t.animation){var e=t.prev,r=t.next;e?e.next=r:this._head=r,r?r.prev=e:this._tail=e,t.next=t.prev=t.animation=null}},e.prototype.removeAnimator=function(t){var e=t.getClip();e&&this.removeClip(e),t.animation=null},e.prototype.update=function(t){var e=E()-this._pausedTime,r=e-this._time,i=this._head;while(i){var n=i.next,a=i.step(e,r);a?(i.ondestroy(),this.removeClip(i),i=n):i=n}this._time=e,t||(this.trigger("frame",r),this.stage.update&&this.stage.update())},e.prototype._startLoop=function(){var t=this;function e(){t._running&&((0,F.Z)(e),!t._paused&&t.update())}this._running=!0,(0,F.Z)(e)},e.prototype.start=function(){this._running||(this._time=E(),this._pausedTime=0,this._startLoop())},e.prototype.stop=function(){this._running=!1},e.prototype.pause=function(){this._paused||(this._pauseStart=E(),this._paused=!0)},e.prototype.resume=function(){this._paused&&(this._pausedTime+=E()-this._pauseStart,this._paused=!1)},e.prototype.clear=function(){var t=this._head;while(t){var e=t.next;t.prev=t.next=t.animation=null,t=e}this._head=this._tail=null},e.prototype.isFinished=function(){return null==this._head},e.prototype.animate=function(t,e){e=e||{},this.start();var r=new B.Z(t,e.loop);return this.addAnimator(r),r},e}(u.Z),j=Z,H=(r(32564),300),N=i.Z.domSupported,W=function(){var t=["click","dblclick","mousewheel","wheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],e=["touchstart","touchend","touchmove"],r={pointerdown:1,pointerup:1,pointermove:1,pointerout:1},i=n.map(t,(function(t){var e=t.replace("mouse","pointer");return r.hasOwnProperty(e)?e:t}));return{mouse:t,touch:e,pointer:i}}(),V={mouse:["mousemove","mouseup"],pointer:["pointermove","pointerup"]},q=!1;function Y(t){var e=t.pointerType;return"pen"===e||"touch"===e}function X(t){t.touching=!0,null!=t.touchTimer&&(clearTimeout(t.touchTimer),t.touchTimer=null),t.touchTimer=setTimeout((function(){t.touching=!1,t.touchTimer=null}),700)}function U(t){t&&(t.zrByTouch=!0)}function $(t,e){return(0,c.OD)(t.dom,new Q(t,e),!0)}function G(t,e){var r=e,i=!1;while(r&&9!==r.nodeType&&!(i=r.domBelongToZr||r!==e&&r===t.painterRoot))r=r.parentNode;return i}var Q=function(){function t(t,e){this.stopPropagation=n.noop,this.stopImmediatePropagation=n.noop,this.preventDefault=n.noop,this.type=e.type,this.target=this.currentTarget=t.dom,this.pointerType=e.pointerType,this.clientX=e.clientX,this.clientY=e.clientY}return t}(),K={mousedown:function(t){t=(0,c.OD)(this.dom,t),this.__mayPointerCapture=[t.zrX,t.zrY],this.trigger("mousedown",t)},mousemove:function(t){t=(0,c.OD)(this.dom,t);var e=this.__mayPointerCapture;!e||t.zrX===e[0]&&t.zrY===e[1]||this.__togglePointerCapture(!0),this.trigger("mousemove",t)},mouseup:function(t){t=(0,c.OD)(this.dom,t),this.__togglePointerCapture(!1),this.trigger("mouseup",t)},mouseout:function(t){t=(0,c.OD)(this.dom,t);var e=t.toElement||t.relatedTarget;G(this,e)||(this.__pointerCapturing&&(t.zrEventControl="no_globalout"),this.trigger("mouseout",t))},wheel:function(t){q=!0,t=(0,c.OD)(this.dom,t),this.trigger("mousewheel",t)},mousewheel:function(t){q||(t=(0,c.OD)(this.dom,t),this.trigger("mousewheel",t))},touchstart:function(t){t=(0,c.OD)(this.dom,t),U(t),this.__lastTouchMoment=new Date,this.handler.processGesture(t,"start"),K.mousemove.call(this,t),K.mousedown.call(this,t)},touchmove:function(t){t=(0,c.OD)(this.dom,t),U(t),this.handler.processGesture(t,"change"),K.mousemove.call(this,t)},touchend:function(t){t=(0,c.OD)(this.dom,t),U(t),this.handler.processGesture(t,"end"),K.mouseup.call(this,t),+new Date-+this.__lastTouchMoment<H&&K.click.call(this,t)},pointerdown:function(t){K.mousedown.call(this,t)},pointermove:function(t){Y(t)||K.mousemove.call(this,t)},pointerup:function(t){K.mouseup.call(this,t)},pointerout:function(t){Y(t)||K.mouseout.call(this,t)}};n.each(["click","dblclick","contextmenu"],(function(t){K[t]=function(e){e=(0,c.OD)(this.dom,e),this.trigger(t,e)}}));var J={pointermove:function(t){Y(t)||J.mousemove.call(this,t)},pointerup:function(t){J.mouseup.call(this,t)},mousemove:function(t){this.trigger("mousemove",t)},mouseup:function(t){var e=this.__pointerCapturing;this.__togglePointerCapture(!1),this.trigger("mouseup",t),e&&(t.zrEventControl="only_globalout",this.trigger("mouseout",t))}};function tt(t,e){var r=e.domHandlers;i.Z.pointerEventsSupported?n.each(W.pointer,(function(i){rt(e,i,(function(e){r[i].call(t,e)}))})):(i.Z.touchEventsSupported&&n.each(W.touch,(function(i){rt(e,i,(function(n){r[i].call(t,n),X(e)}))})),n.each(W.mouse,(function(i){rt(e,i,(function(n){n=(0,c.iP)(n),e.touching||r[i].call(t,n)}))})))}function et(t,e){function r(r){function i(i){i=(0,c.iP)(i),G(t,i.target)||(i=$(t,i),e.domHandlers[r].call(t,i))}rt(e,r,i,{capture:!0})}i.Z.pointerEventsSupported?n.each(V.pointer,r):i.Z.touchEventsSupported||n.each(V.mouse,r)}function rt(t,e,r,i){t.mounted[e]=r,t.listenerOpts[e]=i,(0,c.Oo)(t.domTarget,e,r,i)}function it(t){var e=t.mounted;for(var r in e)e.hasOwnProperty(r)&&(0,c.xg)(t.domTarget,r,e[r],t.listenerOpts[r]);t.mounted={}}var nt=function(){function t(t,e){this.mounted={},this.listenerOpts={},this.touching=!1,this.domTarget=t,this.domHandlers=e}return t}(),at=function(t){function e(e,r){var i=t.call(this)||this;return i.__pointerCapturing=!1,i.dom=e,i.painterRoot=r,i._localHandlerScope=new nt(e,K),N&&(i._globalHandlerScope=new nt(document,J)),tt(i,i._localHandlerScope),i}return(0,a.ZT)(e,t),e.prototype.dispose=function(){it(this._localHandlerScope),N&&it(this._globalHandlerScope)},e.prototype.setCursor=function(t){this.dom.style&&(this.dom.style.cursor=t||"default")},e.prototype.__togglePointerCapture=function(t){if(this.__mayPointerCapture=null,N&&+this.__pointerCapturing^+t){this.__pointerCapturing=t;var e=this._globalHandlerScope;t?et(this,e):it(e)}},e}(u.Z),ot=at,st=r(38689),ht=r(89753),lt=r(81747),ut={},ct={};function ft(t){delete ct[t]}function dt(t){if(!t)return!1;if("string"===typeof t)return(0,st.lum)(t,1)<ht.Ak;if(t.colorStops){for(var e=t.colorStops,r=0,i=e.length,n=0;n<i;n++)r+=(0,st.lum)(e[n].color,1);return r/=i,r<ht.Ak}return!1}var pt=function(){function t(t,e,r){var a=this;this._sleepAfterStill=10,this._stillFrameAccum=0,this._needsRefresh=!0,this._needsRefreshHover=!0,this._darkMode=!1,r=r||{},this.dom=e,this.id=t;var o=new I,s=r.renderer||"canvas";ut[s]||(s=n.keys(ut)[0]),r.useDirtyRect=null!=r.useDirtyRect&&r.useDirtyRect;var h=new ut[s](e,o,r,t),l=r.ssr||h.ssrOnly;this.storage=o,this.painter=h;var u,c=i.Z.node||i.Z.worker||l?null:new ot(h.getViewportRoot(),h.root),f=r.useCoarsePointer,d=null==f||"auto"===f?i.Z.touchEventsSupported:!!f,p=44;d&&(u=n.retrieve2(r.pointerSize,p)),this.handler=new A(o,h,c,h.root,u),this.animation=new j({stage:{update:l?null:function(){return a._flush(!0)}}}),l||this.animation.start()}return t.prototype.add=function(t){t&&(this.storage.addRoot(t),t.addSelfToZr(this),this.refresh())},t.prototype.remove=function(t){t&&(this.storage.delRoot(t),t.removeSelfFromZr(this),this.refresh())},t.prototype.configLayer=function(t,e){this.painter.configLayer&&this.painter.configLayer(t,e),this.refresh()},t.prototype.setBackgroundColor=function(t){this.painter.setBackgroundColor&&this.painter.setBackgroundColor(t),this.refresh(),this._backgroundColor=t,this._darkMode=dt(t)},t.prototype.getBackgroundColor=function(){return this._backgroundColor},t.prototype.setDarkMode=function(t){this._darkMode=t},t.prototype.isDarkMode=function(){return this._darkMode},t.prototype.refreshImmediately=function(t){t||this.animation.update(!0),this._needsRefresh=!1,this.painter.refresh(),this._needsRefresh=!1},t.prototype.refresh=function(){this._needsRefresh=!0,this.animation.start()},t.prototype.flush=function(){this._flush(!1)},t.prototype._flush=function(t){var e,r=E();this._needsRefresh&&(e=!0,this.refreshImmediately(t)),this._needsRefreshHover&&(e=!0,this.refreshHoverImmediately());var i=E();e?(this._stillFrameAccum=0,this.trigger("rendered",{elapsedTime:i-r})):this._sleepAfterStill>0&&(this._stillFrameAccum++,this._stillFrameAccum>this._sleepAfterStill&&this.animation.stop())},t.prototype.setSleepAfterStill=function(t){this._sleepAfterStill=t},t.prototype.wakeUp=function(){this.animation.start(),this._stillFrameAccum=0},t.prototype.refreshHover=function(){this._needsRefreshHover=!0},t.prototype.refreshHoverImmediately=function(){this._needsRefreshHover=!1,this.painter.refreshHover&&"canvas"===this.painter.getType()&&this.painter.refreshHover()},t.prototype.resize=function(t){t=t||{},this.painter.resize(t.width,t.height),this.handler.resize()},t.prototype.clearAnimation=function(){this.animation.clear()},t.prototype.getWidth=function(){return this.painter.getWidth()},t.prototype.getHeight=function(){return this.painter.getHeight()},t.prototype.setCursorStyle=function(t){this.handler.setCursorStyle(t)},t.prototype.findHover=function(t,e){return this.handler.findHover(t,e)},t.prototype.on=function(t,e,r){return this.handler.on(t,e,r),this},t.prototype.off=function(t,e){this.handler.off(t,e)},t.prototype.trigger=function(t,e){this.handler.trigger(t,e)},t.prototype.clear=function(){for(var t=this.storage.getRoots(),e=0;e<t.length;e++)t[e]instanceof lt.Z&&t[e].removeSelfFromZr(this);this.storage.delAllRoots(),this.painter.clear()},t.prototype.dispose=function(){this.animation.stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this.animation=this.storage=this.painter=this.handler=null,ft(this.id)},t}();function vt(t,e){var r=new pt(n.guid(),t,e);return ct[r.id]=r,r}function gt(t){t.dispose()}function yt(){for(var t in ct)ct.hasOwnProperty(t)&&ct[t].dispose();ct={}}function _t(t){return ct[t]}function mt(t,e){ut[t]=e}var xt="5.4.4"},33666:function(t,e,r){var i=r(26469),n=r(65836),a=r(71082),o=r(4845),s=r(63122),h=function(t){a.call(this,t),n.call(this,t),o.call(this,t),this.id=t.id||i()};h.prototype={type:"element",name:"",__zr:null,ignore:!1,clipPath:null,isGroup:!1,drift:function(t,e){switch(this.draggable){case"horizontal":e=0;break;case"vertical":t=0;break}var r=this.transform;r||(r=this.transform=[1,0,0,1,0,0]),r[4]+=t,r[5]+=e,this.decomposeTransform(),this.dirty(!1)},beforeUpdate:function(){},afterUpdate:function(){},update:function(){this.updateTransform()},traverse:function(t,e){},attrKV:function(t,e){if("position"===t||"scale"===t||"origin"===t){if(e){var r=this[t];r||(r=this[t]=[]),r[0]=e[0],r[1]=e[1]}}else this[t]=e},hide:function(){this.ignore=!0,this.__zr&&this.__zr.refresh()},show:function(){this.ignore=!1,this.__zr&&this.__zr.refresh()},attr:function(t,e){if("string"===typeof t)this.attrKV(t,e);else if(s.isObject(t))for(var r in t)t.hasOwnProperty(r)&&this.attrKV(r,t[r]);return this.dirty(!1),this},setClipPath:function(t){var e=this.__zr;e&&t.addSelfToZr(e),this.clipPath&&this.clipPath!==t&&this.removeClipPath(),this.clipPath=t,t.__zr=e,t.__clipTarget=this,this.dirty(!1)},removeClipPath:function(){var t=this.clipPath;t&&(t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__clipTarget=null,this.clipPath=null,this.dirty(!1))},addSelfToZr:function(t){this.__zr=t;var e=this.animators;if(e)for(var r=0;r<e.length;r++)t.animation.addAnimator(e[r]);this.clipPath&&this.clipPath.addSelfToZr(t)},removeSelfFromZr:function(t){this.__zr=null;var e=this.animators;if(e)for(var r=0;r<e.length;r++)t.animation.removeAnimator(e[r]);this.clipPath&&this.clipPath.removeSelfFromZr(t)}},s.mixin(h,o),s.mixin(h,a),s.mixin(h,n);var l=h;t.exports=l},58263:function(t,e,r){var i=r(63122),n=r(78244),a=r(78035),o=r(65836),s=r(13199),h=r(40271),l="silent";function u(t,e,r){return{type:t,event:r,target:e.target,topTarget:e.topTarget,cancelBubble:!1,offsetX:r.zrX,offsetY:r.zrY,gestureEvent:r.gestureEvent,pinchX:r.pinchX,pinchY:r.pinchY,pinchScale:r.pinchScale,wheelDelta:r.zrDelta,zrByTouch:r.zrByTouch,which:r.which,stop:c}}function c(){s.stop(this.event)}function f(){}f.prototype.dispose=function(){};var d=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],p=function(t,e,r,i){o.call(this),this.storage=t,this.painter=e,this.painterRoot=i,r=r||new f,this.proxy=null,this._hovered={},this._lastTouchMoment,this._lastX,this._lastY,this._gestureMgr,a.call(this),this.setHandlerProxy(r)};function v(t,e,r){if(t[t.rectHover?"rectContain":"contain"](e,r)){var i,n=t;while(n){if(n.clipPath&&!n.clipPath.contain(e,r))return!1;n.silent&&(i=!0),n=n.parent}return!i||l}return!1}function g(t,e,r){var i=t.painter;return e<0||e>i.getWidth()||r<0||r>i.getHeight()}p.prototype={constructor:p,setHandlerProxy:function(t){this.proxy&&this.proxy.dispose(),t&&(i.each(d,(function(e){t.on&&t.on(e,this[e],this)}),this),t.handler=this),this.proxy=t},mousemove:function(t){var e=t.zrX,r=t.zrY,i=g(this,e,r),n=this._hovered,a=n.target;a&&!a.__zr&&(n=this.findHover(n.x,n.y),a=n.target);var o=this._hovered=i?{x:e,y:r}:this.findHover(e,r),s=o.target,h=this.proxy;h.setCursor&&h.setCursor(s?s.cursor:"default"),a&&s!==a&&this.dispatchToElement(n,"mouseout",t),this.dispatchToElement(o,"mousemove",t),s&&s!==a&&this.dispatchToElement(o,"mouseover",t)},mouseout:function(t){var e=t.zrEventControl,r=t.zrIsToLocalDOM;"only_globalout"!==e&&this.dispatchToElement(this._hovered,"mouseout",t),"no_globalout"!==e&&!r&&this.trigger("globalout",{type:"globalout",event:t})},resize:function(t){this._hovered={}},dispatch:function(t,e){var r=this[t];r&&r.call(this,e)},dispose:function(){this.proxy.dispose(),this.storage=this.proxy=this.painter=null},setCursorStyle:function(t){var e=this.proxy;e.setCursor&&e.setCursor(t)},dispatchToElement:function(t,e,r){t=t||{};var i=t.target;if(!i||!i.silent){var n="on"+e,a=u(e,t,r);while(i)if(i[n]&&(a.cancelBubble=i[n].call(i,a)),i.trigger(e,a),i=i.parent,a.cancelBubble)break;a.cancelBubble||(this.trigger(e,a),this.painter&&this.painter.eachOtherLayer((function(t){"function"===typeof t[n]&&t[n].call(t,a),t.trigger&&t.trigger(e,a)})))}},findHover:function(t,e,r){for(var i=this.storage.getDisplayList(),n={x:t,y:e},a=i.length-1;a>=0;a--){var o;if(i[a]!==r&&!i[a].ignore&&(o=v(i[a],t,e))&&(!n.topTarget&&(n.topTarget=i[a]),o!==l)){n.target=i[a];break}}return n},processGesture:function(t,e){this._gestureMgr||(this._gestureMgr=new h);var r=this._gestureMgr;"start"===e&&r.clear();var i=r.recognize(t,this.findHover(t.zrX,t.zrY,null).target,this.proxy.dom);if("end"===e&&r.clear(),i){var n=i.type;t.gestureEvent=n,this.dispatchToElement({target:i.target},n,i.event)}}},i.each(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],(function(t){p.prototype[t]=function(e){var r,i,a=e.zrX,o=e.zrY,s=g(this,a,o);if("mouseup"===t&&s||(r=this.findHover(a,o),i=r.target),"mousedown"===t)this._downEl=i,this._downPoint=[e.zrX,e.zrY],this._upEl=i;else if("mouseup"===t)this._upEl=i;else if("click"===t){if(this._downEl!==this._upEl||!this._downPoint||n.dist(this._downPoint,[e.zrX,e.zrY])>4)return;this._downPoint=null}this.dispatchToElement(r,t,e)}})),i.mixin(p,o),i.mixin(p,a);var y=p;t.exports=y},35110:function(t,e,r){var i=r(63122),n=r(52215),a=n.devicePixelRatio,o=r(82516),s=r(89872);function h(){return!1}function l(t,e,r){var n=i.createCanvas(),a=e.getWidth(),o=e.getHeight(),s=n.style;return s&&(s.position="absolute",s.left=0,s.top=0,s.width=a+"px",s.height=o+"px",n.setAttribute("data-zr-dom-id",t)),n.width=a*r,n.height=o*r,n}var u=function(t,e,r){var n;r=r||a,"string"===typeof t?n=l(t,e,r):i.isObject(t)&&(n=t,t=n.id),this.id=t,this.dom=n;var o=n.style;o&&(n.onselectstart=h,o["-webkit-user-select"]="none",o["user-select"]="none",o["-webkit-touch-callout"]="none",o["-webkit-tap-highlight-color"]="rgba(0,0,0,0)",o["padding"]=0,o["margin"]=0,o["border-width"]=0),this.domBack=null,this.ctxBack=null,this.painter=e,this.config=null,this.clearColor=0,this.motionBlur=!1,this.lastFrameAlpha=.7,this.dpr=r};u.prototype={constructor:u,__dirty:!0,__used:!1,__drawIndex:0,__startIndex:0,__endIndex:0,incremental:!1,getElementCount:function(){return this.__endIndex-this.__startIndex},initContext:function(){this.ctx=this.dom.getContext("2d"),this.ctx.dpr=this.dpr},createBackBuffer:function(){var t=this.dpr;this.domBack=l("back-"+this.id,this.painter,t),this.ctxBack=this.domBack.getContext("2d"),1!==t&&this.ctxBack.scale(t,t)},resize:function(t,e){var r=this.dpr,i=this.dom,n=i.style,a=this.domBack;n&&(n.width=t+"px",n.height=e+"px"),i.width=t*r,i.height=e*r,a&&(a.width=t*r,a.height=e*r,1!==r&&this.ctxBack.scale(r,r))},clear:function(t,e){var r,i=this.dom,n=this.ctx,a=i.width,h=i.height,l=(e=e||this.clearColor,this.motionBlur&&!t),u=this.lastFrameAlpha,c=this.dpr;(l&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(i,0,0,a/c,h/c)),n.clearRect(0,0,a,h),e&&"transparent"!==e)&&(e.colorStops?(r=e.__canvasGradient||o.getGradient(n,e,{x:0,y:0,width:a,height:h}),e.__canvasGradient=r):e.image&&(r=s.prototype.getCanvasPattern.call(e,n)),n.save(),n.fillStyle=r||e,n.fillRect(0,0,a,h),n.restore());if(l){var f=this.domBack;n.save(),n.globalAlpha=u,n.drawImage(f,0,0,a,h),n.restore()}}};var c=u;t.exports=c},35960:function(t,e,r){var i=r(52215),n=i.devicePixelRatio,a=r(63122),o=r(70083),s=r(46630),h=r(92757),l=r(35110),u=r(88983),c=r(45474),f=r(95160),d=1e5,p=314159,v=.01,g=.001;function y(t){return parseInt(t,10)}function _(t){return!!t&&(!!t.__builtin__||"function"===typeof t.resize&&"function"===typeof t.refresh)}var m=new s(0,0,0,0),x=new s(0,0,0,0);function w(t,e,r){return m.copy(t.getBoundingRect()),t.transform&&m.applyTransform(t.transform),x.width=e,x.height=r,!m.intersect(x)}function b(t,e){if(t===e)return!1;if(!t||!e||t.length!==e.length)return!0;for(var r=0;r<t.length;r++)if(t[r]!==e[r])return!0;return!1}function k(t,e){for(var r=0;r<t.length;r++){var i=t[r];i.setTransform(e),e.beginPath(),i.buildPath(e,i.shape),e.clip(),i.restoreTransform(e)}}function T(t,e){var r=document.createElement("div");return r.style.cssText=["position:relative","width:"+t+"px","height:"+e+"px","padding:0","margin:0","border-width:0"].join(";")+";",r}var S=function(t,e,r){this.type="canvas";var i=!t.nodeName||"CANVAS"===t.nodeName.toUpperCase();this._opts=r=a.extend({},r||{}),this.dpr=r.devicePixelRatio||n,this._singleCanvas=i,this.root=t;var o=t.style;o&&(o["-webkit-tap-highlight-color"]="transparent",o["-webkit-user-select"]=o["user-select"]=o["-webkit-touch-callout"]="none",t.innerHTML=""),this.storage=e;var s=this._zlevelList=[],h=this._layers={};if(this._layerConfig={},this._needsManuallyCompositing=!1,i){var u=t.width,c=t.height;null!=r.width&&(u=r.width),null!=r.height&&(c=r.height),this.dpr=r.devicePixelRatio||1,t.width=u*this.dpr,t.height=c*this.dpr,this._width=u,this._height=c;var f=new l(t,this,this.dpr);f.__builtin__=!0,f.initContext(),h[p]=f,f.zlevel=p,s.push(p),this._domRoot=t}else{this._width=this._getSize(0),this._height=this._getSize(1);var d=this._domRoot=T(this._width,this._height);t.appendChild(d)}this._hoverlayer=null,this._hoverElements=[]};S.prototype={constructor:S,getType:function(){return"canvas"},isSingleCanvas:function(){return this._singleCanvas},getViewportRoot:function(){return this._domRoot},getViewportRootOffset:function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},refresh:function(t){var e=this.storage.getDisplayList(!0),r=this._zlevelList;this._redrawId=Math.random(),this._paintList(e,t,this._redrawId);for(var i=0;i<r.length;i++){var n=r[i],a=this._layers[n];if(!a.__builtin__&&a.refresh){var o=0===i?this._backgroundColor:null;a.refresh(o)}}return this.refreshHover(),this},addHover:function(t,e){if(!t.__hoverMir){var r=new t.constructor({style:t.style,shape:t.shape,z:t.z,z2:t.z2,silent:t.silent});return r.__from=t,t.__hoverMir=r,e&&r.setStyle(e),this._hoverElements.push(r),r}},removeHover:function(t){var e=t.__hoverMir,r=this._hoverElements,i=a.indexOf(r,e);i>=0&&r.splice(i,1),t.__hoverMir=null},clearHover:function(t){for(var e=this._hoverElements,r=0;r<e.length;r++){var i=e[r].__from;i&&(i.__hoverMir=null)}e.length=0},refreshHover:function(){var t=this._hoverElements,e=t.length,r=this._hoverlayer;if(r&&r.clear(),e){h(t,this.storage.displayableSortFunc),r||(r=this._hoverlayer=this.getLayer(d));var i={};r.ctx.save();for(var n=0;n<e;){var a=t[n],o=a.__from;o&&o.__zr?(n++,o.invisible||(a.transform=o.transform,a.invTransform=o.invTransform,a.__clipPaths=o.__clipPaths,this._doPaintEl(a,r,!0,i))):(t.splice(n,1),o.__hoverMir=null,e--)}r.ctx.restore()}},getHoverLayer:function(){return this.getLayer(d)},_paintList:function(t,e,r){if(this._redrawId===r){e=e||!1,this._updateLayerStatus(t);var i=this._doPaintList(t,e);if(this._needsManuallyCompositing&&this._compositeManually(),!i){var n=this;u((function(){n._paintList(t,e,r)}))}}},_compositeManually:function(){var t=this.getLayer(p).ctx,e=this._domRoot.width,r=this._domRoot.height;t.clearRect(0,0,e,r),this.eachBuiltinLayer((function(i){i.virtual&&t.drawImage(i.dom,0,0,e,r)}))},_doPaintList:function(t,e){for(var r=[],i=0;i<this._zlevelList.length;i++){var n=this._zlevelList[i],o=this._layers[n];o.__builtin__&&o!==this._hoverlayer&&(o.__dirty||e)&&r.push(o)}for(var s=!0,h=0;h<r.length;h++){o=r[h];var l=o.ctx,u={};l.save();var c=e?o.__startIndex:o.__drawIndex,d=!e&&o.incremental&&Date.now,p=d&&Date.now(),v=o.zlevel===this._zlevelList[0]?this._backgroundColor:null;if(o.__startIndex===o.__endIndex)o.clear(!1,v);else if(c===o.__startIndex){var g=t[c];g.incremental&&g.notClear&&!e||o.clear(!1,v)}-1===c&&(c=o.__startIndex);for(var y=c;y<o.__endIndex;y++){var _=t[y];if(this._doPaintEl(_,o,e,u),_.__dirty=_.__dirtyText=!1,d){var m=Date.now()-p;if(m>15)break}}o.__drawIndex=y,o.__drawIndex<o.__endIndex&&(s=!1),u.prevElClipPaths&&l.restore(),l.restore()}return f.wxa&&a.each(this._layers,(function(t){t&&t.ctx&&t.ctx.draw&&t.ctx.draw()})),s},_doPaintEl:function(t,e,r,i){var n=e.ctx,a=t.transform;if((e.__dirty||r)&&!t.invisible&&0!==t.style.opacity&&(!a||a[0]||a[3])&&(!t.culling||!w(t,this._width,this._height))){var o=t.__clipPaths,s=i.prevElClipPaths;s&&!b(o,s)||(s&&(n.restore(),i.prevElClipPaths=null,i.prevEl=null),o&&(n.save(),k(o,n),i.prevElClipPaths=o)),t.beforeBrush&&t.beforeBrush(n),t.brush(n,i.prevEl||null),i.prevEl=t,t.afterBrush&&t.afterBrush(n)}},getLayer:function(t,e){this._singleCanvas&&!this._needsManuallyCompositing&&(t=p);var r=this._layers[t];return r||(r=new l("zr_"+t,this,this.dpr),r.zlevel=t,r.__builtin__=!0,this._layerConfig[t]?a.merge(r,this._layerConfig[t],!0):this._layerConfig[t-v]&&a.merge(r,this._layerConfig[t-v],!0),e&&(r.virtual=e),this.insertLayer(t,r),r.initContext()),r},insertLayer:function(t,e){var r=this._layers,i=this._zlevelList,n=i.length,a=null,s=-1,h=this._domRoot;if(r[t])o("ZLevel "+t+" has been used already");else if(_(e)){if(n>0&&t>i[0]){for(s=0;s<n-1;s++)if(i[s]<t&&i[s+1]>t)break;a=r[i[s]]}if(i.splice(s+1,0,t),r[t]=e,!e.virtual)if(a){var l=a.dom;l.nextSibling?h.insertBefore(e.dom,l.nextSibling):h.appendChild(e.dom)}else h.firstChild?h.insertBefore(e.dom,h.firstChild):h.appendChild(e.dom)}else o("Layer of zlevel "+t+" is not valid")},eachLayer:function(t,e){var r,i,n=this._zlevelList;for(i=0;i<n.length;i++)r=n[i],t.call(e,this._layers[r],r)},eachBuiltinLayer:function(t,e){var r,i,n,a=this._zlevelList;for(n=0;n<a.length;n++)i=a[n],r=this._layers[i],r.__builtin__&&t.call(e,r,i)},eachOtherLayer:function(t,e){var r,i,n,a=this._zlevelList;for(n=0;n<a.length;n++)i=a[n],r=this._layers[i],r.__builtin__||t.call(e,r,i)},getLayers:function(){return this._layers},_updateLayerStatus:function(t){function e(t){a&&(a.__endIndex!==t&&(a.__dirty=!0),a.__endIndex=t)}if(this.eachBuiltinLayer((function(t,e){t.__dirty=t.__used=!1})),this._singleCanvas)for(var r=1;r<t.length;r++){var i=t[r];if(i.zlevel!==t[r-1].zlevel||i.incremental){this._needsManuallyCompositing=!0;break}}var n,a=null,s=0;for(r=0;r<t.length;r++){i=t[r];var h,l=i.zlevel;n!==l&&(n=l,s=0),i.incremental?(h=this.getLayer(l+g,this._needsManuallyCompositing),h.incremental=!0,s=1):h=this.getLayer(l+(s>0?v:0),this._needsManuallyCompositing),h.__builtin__||o("ZLevel "+l+" has been used by unkown layer "+h.id),h!==a&&(h.__used=!0,h.__startIndex!==r&&(h.__dirty=!0),h.__startIndex=r,h.incremental?h.__drawIndex=-1:h.__drawIndex=r,e(r),a=h),i.__dirty&&(h.__dirty=!0,h.incremental&&h.__drawIndex<0&&(h.__drawIndex=r))}e(r),this.eachBuiltinLayer((function(t,e){!t.__used&&t.getElementCount()>0&&(t.__dirty=!0,t.__startIndex=t.__endIndex=t.__drawIndex=0),t.__dirty&&t.__drawIndex<0&&(t.__drawIndex=t.__startIndex)}))},clear:function(){return this.eachBuiltinLayer(this._clearLayer),this},_clearLayer:function(t){t.clear()},setBackgroundColor:function(t){this._backgroundColor=t},configLayer:function(t,e){if(e){var r=this._layerConfig;r[t]?a.merge(r[t],e,!0):r[t]=e;for(var i=0;i<this._zlevelList.length;i++){var n=this._zlevelList[i];if(n===t||n===t+v){var o=this._layers[n];a.merge(o,r[t],!0)}}}},delLayer:function(t){var e=this._layers,r=this._zlevelList,i=e[t];i&&(i.dom.parentNode.removeChild(i.dom),delete e[t],r.splice(a.indexOf(r,t),1))},resize:function(t,e){if(this._domRoot.style){var r=this._domRoot;r.style.display="none";var i=this._opts;if(null!=t&&(i.width=t),null!=e&&(i.height=e),t=this._getSize(0),e=this._getSize(1),r.style.display="",this._width!==t||e!==this._height){for(var n in r.style.width=t+"px",r.style.height=e+"px",this._layers)this._layers.hasOwnProperty(n)&&this._layers[n].resize(t,e);a.each(this._progressiveLayers,(function(r){r.resize(t,e)})),this.refresh(!0)}this._width=t,this._height=e}else{if(null==t||null==e)return;this._width=t,this._height=e,this.getLayer(p).resize(t,e)}return this},clearLayer:function(t){var e=this._layers[t];e&&e.clear()},dispose:function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},getRenderedCanvas:function(t){if(t=t||{},this._singleCanvas&&!this._compositeManually)return this._layers[p].dom;var e=new l("image",this,t.pixelRatio||this.dpr);if(e.initContext(),e.clear(!1,t.backgroundColor||this._backgroundColor),t.pixelRatio<=this.dpr){this.refresh();var r=e.dom.width,i=e.dom.height,n=e.ctx;this.eachLayer((function(t){t.__builtin__?n.drawImage(t.dom,0,0,r,i):t.renderToCanvas&&(e.ctx.save(),t.renderToCanvas(e.ctx),e.ctx.restore())}))}else for(var a={},o=this.storage.getDisplayList(!0),s=0;s<o.length;s++){var h=o[s];this._doPaintEl(h,e,!0,a)}return e.dom},getWidth:function(){return this._width},getHeight:function(){return this._height},_getSize:function(t){var e=this._opts,r=["width","height"][t],i=["clientWidth","clientHeight"][t],n=["paddingLeft","paddingTop"][t],a=["paddingRight","paddingBottom"][t];if(null!=e[r]&&"auto"!==e[r])return parseFloat(e[r]);var o=this.root,s=document.defaultView.getComputedStyle(o);return(o[i]||y(s[r])||y(o.style[r]))-(y(s[n])||0)-(y(s[a])||0)|0},pathToImage:function(t,e){e=e||this.dpr;var r=document.createElement("canvas"),i=r.getContext("2d"),n=t.getBoundingRect(),a=t.style,o=a.shadowBlur*e,s=a.shadowOffsetX*e,h=a.shadowOffsetY*e,l=a.hasStroke()?a.lineWidth:0,u=Math.max(l/2,-s+o),f=Math.max(l/2,s+o),d=Math.max(l/2,-h+o),p=Math.max(l/2,h+o),v=n.width+u+f,g=n.height+d+p;r.width=v*e,r.height=g*e,i.scale(e,e),i.clearRect(0,0,v,g),i.dpr=e;var y={position:t.position,rotation:t.rotation,scale:t.scale};t.position=[u-n.x,d-n.y],t.rotation=0,t.scale=[1,1],t.updateTransform(),t&&t.brush(i);var _=c,m=new _({style:{x:0,y:0,image:r}});return null!=y.position&&(m.position=t.position=y.position),null!=y.rotation&&(m.rotation=t.rotation=y.rotation),null!=y.scale&&(m.scale=t.scale=y.scale),m}};var C=S;t.exports=C},92494:function(t,e,r){var i=r(63122),n=r(95160),a=r(79116),o=r(92757);function s(t,e){return t.zlevel===e.zlevel?t.z===e.z?t.z2-e.z2:t.z-e.z:t.zlevel-e.zlevel}var h=function(){this._roots=[],this._displayList=[],this._displayListLen=0};h.prototype={constructor:h,traverse:function(t,e){for(var r=0;r<this._roots.length;r++)this._roots[r].traverse(t,e)},getDisplayList:function(t,e){return e=e||!1,t&&this.updateDisplayList(e),this._displayList},updateDisplayList:function(t){this._displayListLen=0;for(var e=this._roots,r=this._displayList,i=0,a=e.length;i<a;i++)this._updateAndAddDisplayable(e[i],null,t);r.length=this._displayListLen,n.canvasSupported&&o(r,s)},_updateAndAddDisplayable:function(t,e,r){if(!t.ignore||r){t.beforeUpdate(),t.__dirty&&t.update(),t.afterUpdate();var i=t.clipPath;if(i){e=e?e.slice():[];var n=i,a=t;while(n)n.parent=a,n.updateTransform(),e.push(n),a=n,n=n.clipPath}if(t.isGroup){for(var o=t._children,s=0;s<o.length;s++){var h=o[s];t.__dirty&&(h.__dirty=!0),this._updateAndAddDisplayable(h,e,r)}t.__dirty=!1}else t.__clipPaths=e,this._displayList[this._displayListLen++]=t}},addRoot:function(t){t.__storage!==this&&(t instanceof a&&t.addChildrenToStorage(this),this.addToStorage(t),this._roots.push(t))},delRoot:function(t){if(null==t){for(var e=0;e<this._roots.length;e++){var r=this._roots[e];r instanceof a&&r.delChildrenFromStorage(this)}return this._roots=[],this._displayList=[],void(this._displayListLen=0)}if(t instanceof Array){e=0;for(var n=t.length;e<n;e++)this.delRoot(t[e])}else{var o=i.indexOf(this._roots,t);o>=0&&(this.delFromStorage(t),this._roots.splice(o,1),t instanceof a&&t.delChildrenFromStorage(this))}},addToStorage:function(t){return t&&(t.__storage=this,t.dirty(!1)),this},delFromStorage:function(t){return t&&(t.__storage=null),this},dispose:function(){this._renderList=this._roots=null},displayableSortFunc:s};var l=h;t.exports=l},14204:function(t,e,r){var i=r(63122),n=r(13199),a=n.Dispatcher,o=r(88983),s=r(40925),h=function(t){t=t||{},this.stage=t.stage||{},this.onframe=t.onframe||function(){},this._clips=[],this._running=!1,this._time,this._pausedTime,this._pauseStart,this._paused=!1,a.call(this)};h.prototype={constructor:h,addClip:function(t){this._clips.push(t)},addAnimator:function(t){t.animation=this;for(var e=t.getClips(),r=0;r<e.length;r++)this.addClip(e[r])},removeClip:function(t){var e=i.indexOf(this._clips,t);e>=0&&this._clips.splice(e,1)},removeAnimator:function(t){for(var e=t.getClips(),r=0;r<e.length;r++)this.removeClip(e[r]);t.animation=null},_update:function(){for(var t=(new Date).getTime()-this._pausedTime,e=t-this._time,r=this._clips,i=r.length,n=[],a=[],o=0;o<i;o++){var s=r[o],h=s.step(t,e);h&&(n.push(h),a.push(s))}for(o=0;o<i;)r[o]._needsRemove?(r[o]=r[i-1],r.pop(),i--):o++;i=n.length;for(o=0;o<i;o++)a[o].fire(n[o]);this._time=t,this.onframe(e),this.trigger("frame",e),this.stage.update&&this.stage.update()},_startLoop:function(){var t=this;function e(){t._running&&(o(e),!t._paused&&t._update())}this._running=!0,o(e)},start:function(){this._time=(new Date).getTime(),this._pausedTime=0,this._startLoop()},stop:function(){this._running=!1},pause:function(){this._paused||(this._pauseStart=(new Date).getTime(),this._paused=!0)},resume:function(){this._paused&&(this._pausedTime+=(new Date).getTime()-this._pauseStart,this._paused=!1)},clear:function(){this._clips=[]},isFinished:function(){return!this._clips.length},animate:function(t,e){e=e||{};var r=new s(t,e.loop,e.getter,e.setter);return this.addAnimator(r),r}},i.mixin(h,a);var l=h;t.exports=l},40925:function(t,e,r){var i=r(30347),n=r(7471),a=r(63122),o=a.isArrayLike,s=Array.prototype.slice;function h(t,e){return t[e]}function l(t,e,r){t[e]=r}function u(t,e,r){return(e-t)*r+t}function c(t,e,r){return r>.5?e:t}function f(t,e,r,i,n){var a=t.length;if(1===n)for(var o=0;o<a;o++)i[o]=u(t[o],e[o],r);else{var s=a&&t[0].length;for(o=0;o<a;o++)for(var h=0;h<s;h++)i[o][h]=u(t[o][h],e[o][h],r)}}function d(t,e,r){var i=t.length,n=e.length;if(i!==n){var a=i>n;if(a)t.length=n;else for(var o=i;o<n;o++)t.push(1===r?e[o]:s.call(e[o]))}var h=t[0]&&t[0].length;for(o=0;o<t.length;o++)if(1===r)isNaN(t[o])&&(t[o]=e[o]);else for(var l=0;l<h;l++)isNaN(t[o][l])&&(t[o][l]=e[o][l])}function p(t,e,r){if(t===e)return!0;var i=t.length;if(i!==e.length)return!1;if(1===r){for(var n=0;n<i;n++)if(t[n]!==e[n])return!1}else{var a=t[0].length;for(n=0;n<i;n++)for(var o=0;o<a;o++)if(t[n][o]!==e[n][o])return!1}return!0}function v(t,e,r,i,n,a,o,s,h){var l=t.length;if(1===h)for(var u=0;u<l;u++)s[u]=g(t[u],e[u],r[u],i[u],n,a,o);else{var c=t[0].length;for(u=0;u<l;u++)for(var f=0;f<c;f++)s[u][f]=g(t[u][f],e[u][f],r[u][f],i[u][f],n,a,o)}}function g(t,e,r,i,n,a,o){var s=.5*(r-t),h=.5*(i-e);return(2*(e-r)+s+h)*o+(-3*(e-r)-2*s-h)*a+s*n+e}function y(t){if(o(t)){var e=t.length;if(o(t[0])){for(var r=[],i=0;i<e;i++)r.push(s.call(t[i]));return r}return s.call(t)}return t}function _(t){return t[0]=Math.floor(t[0]),t[1]=Math.floor(t[1]),t[2]=Math.floor(t[2]),"rgba("+t.join(",")+")"}function m(t){var e=t[t.length-1].value;return o(e&&e[0])?2:1}function x(t,e,r,a,s,h){var l=t._getter,y=t._setter,x="spline"===e,w=a.length;if(w){var b,k=a[0].value,T=o(k),S=!1,C=!1,P=T?m(a):0;a.sort((function(t,e){return t.time-e.time})),b=a[w-1].time;for(var A=[],M=[],L=a[0].value,D=!0,z=0;z<w;z++){A.push(a[z].time/b);var O=a[z].value;if(T&&p(O,L,P)||!T&&O===L||(D=!1),L=O,"string"===typeof O){var R=n.parse(O);R?(O=R,S=!0):C=!0}M.push(O)}if(h||!D){var I=M[w-1];for(z=0;z<w-1;z++)T?d(M[z],I,P):!isNaN(M[z])||isNaN(I)||C||S||(M[z]=I);T&&d(l(t._target,s),I,P);var F,B,E,Z,j,H,N=0,W=0;if(S)var V=[0,0,0,0];var q=function(t,e){var r;if(e<0)r=0;else if(e<W){for(F=Math.min(N+1,w-1),r=F;r>=0;r--)if(A[r]<=e)break;r=Math.min(r,w-2)}else{for(r=N;r<w;r++)if(A[r]>e)break;r=Math.min(r-1,w-2)}N=r,W=e;var i=A[r+1]-A[r];if(0!==i)if(B=(e-A[r])/i,x)if(Z=M[r],E=M[0===r?r:r-1],j=M[r>w-2?w-1:r+1],H=M[r>w-3?w-1:r+2],T)v(E,Z,j,H,B,B*B,B*B*B,l(t,s),P);else{if(S)n=v(E,Z,j,H,B,B*B,B*B*B,V,1),n=_(V);else{if(C)return c(Z,j,B);n=g(E,Z,j,H,B,B*B,B*B*B)}y(t,s,n)}else if(T)f(M[r],M[r+1],B,l(t,s),P);else{var n;if(S)f(M[r],M[r+1],B,V,1),n=_(V);else{if(C)return c(M[r],M[r+1],B);n=u(M[r],M[r+1],B)}y(t,s,n)}},Y=new i({target:t._target,life:b,loop:t._loop,delay:t._delay,onframe:q,ondestroy:r});return e&&"spline"!==e&&(Y.easing=e),Y}}}var w=function(t,e,r,i){this._tracks={},this._target=t,this._loop=e||!1,this._getter=r||h,this._setter=i||l,this._clipCount=0,this._delay=0,this._doneList=[],this._onframeList=[],this._clipList=[]};w.prototype={when:function(t,e){var r=this._tracks;for(var i in e)if(e.hasOwnProperty(i)){if(!r[i]){r[i]=[];var n=this._getter(this._target,i);if(null==n)continue;0!==t&&r[i].push({time:0,value:y(n)})}r[i].push({time:t,value:e[i]})}return this},during:function(t){return this._onframeList.push(t),this},pause:function(){for(var t=0;t<this._clipList.length;t++)this._clipList[t].pause();this._paused=!0},resume:function(){for(var t=0;t<this._clipList.length;t++)this._clipList[t].resume();this._paused=!1},isPaused:function(){return!!this._paused},_doneCallback:function(){this._tracks={},this._clipList.length=0;for(var t=this._doneList,e=t.length,r=0;r<e;r++)t[r].call(this)},start:function(t,e){var r,i=this,n=0,a=function(){n--,n||i._doneCallback()};for(var o in this._tracks)if(this._tracks.hasOwnProperty(o)){var s=x(this,t,a,this._tracks[o],o,e);s&&(this._clipList.push(s),n++,this.animation&&this.animation.addClip(s),r=s)}if(r){var h=r.onframe;r.onframe=function(t,e){h(t,e);for(var r=0;r<i._onframeList.length;r++)i._onframeList[r](t,e)}}return n||this._doneCallback(),this},stop:function(t){for(var e=this._clipList,r=this.animation,i=0;i<e.length;i++){var n=e[i];t&&n.onframe(this._target,1),r&&r.removeClip(n)}e.length=0},delay:function(t){return this._delay=t,this},done:function(t){return t&&this._doneList.push(t),this},getClips:function(){return this._clipList}};var b=w;t.exports=b},30347:function(t,e,r){var i=r(18052);function n(t){this._target=t.target,this._life=t.life||1e3,this._delay=t.delay||0,this._initialized=!1,this.loop=null!=t.loop&&t.loop,this.gap=t.gap||0,this.easing=t.easing||"Linear",this.onframe=t.onframe,this.ondestroy=t.ondestroy,this.onrestart=t.onrestart,this._pausedTime=0,this._paused=!1}n.prototype={constructor:n,step:function(t,e){if(this._initialized||(this._startTime=t+this._delay,this._initialized=!0),this._paused)this._pausedTime+=e;else{var r=(t-this._startTime-this._pausedTime)/this._life;if(!(r<0)){r=Math.min(r,1);var n=this.easing,a="string"===typeof n?i[n]:n,o="function"===typeof a?a(r):r;return this.fire("frame",o),1===r?this.loop?(this.restart(t),"restart"):(this._needsRemove=!0,"destroy"):null}}},restart:function(t){var e=(t-this._startTime-this._pausedTime)%this._life;this._startTime=t-e+this.gap,this._pausedTime=0,this._needsRemove=!1},fire:function(t,e){t="on"+t,this[t]&&this[t](this._target,e)},pause:function(){this._paused=!0},resume:function(){this._paused=!1}};var a=n;t.exports=a},18052:function(t){var e={linear:function(t){return t},quadraticIn:function(t){return t*t},quadraticOut:function(t){return t*(2-t)},quadraticInOut:function(t){return(t*=2)<1?.5*t*t:-.5*(--t*(t-2)-1)},cubicIn:function(t){return t*t*t},cubicOut:function(t){return--t*t*t+1},cubicInOut:function(t){return(t*=2)<1?.5*t*t*t:.5*((t-=2)*t*t+2)},quarticIn:function(t){return t*t*t*t},quarticOut:function(t){return 1- --t*t*t*t},quarticInOut:function(t){return(t*=2)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)},quinticIn:function(t){return t*t*t*t*t},quinticOut:function(t){return--t*t*t*t*t+1},quinticInOut:function(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)},sinusoidalIn:function(t){return 1-Math.cos(t*Math.PI/2)},sinusoidalOut:function(t){return Math.sin(t*Math.PI/2)},sinusoidalInOut:function(t){return.5*(1-Math.cos(Math.PI*t))},exponentialIn:function(t){return 0===t?0:Math.pow(1024,t-1)},exponentialOut:function(t){return 1===t?1:1-Math.pow(2,-10*t)},exponentialInOut:function(t){return 0===t?0:1===t?1:(t*=2)<1?.5*Math.pow(1024,t-1):.5*(2-Math.pow(2,-10*(t-1)))},circularIn:function(t){return 1-Math.sqrt(1-t*t)},circularOut:function(t){return Math.sqrt(1- --t*t)},circularInOut:function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)},elasticIn:function(t){var e,r=.1,i=.4;return 0===t?0:1===t?1:(!r||r<1?(r=1,e=i/4):e=i*Math.asin(1/r)/(2*Math.PI),-r*Math.pow(2,10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/i))},elasticOut:function(t){var e,r=.1,i=.4;return 0===t?0:1===t?1:(!r||r<1?(r=1,e=i/4):e=i*Math.asin(1/r)/(2*Math.PI),r*Math.pow(2,-10*t)*Math.sin((t-e)*(2*Math.PI)/i)+1)},elasticInOut:function(t){var e,r=.1,i=.4;return 0===t?0:1===t?1:(!r||r<1?(r=1,e=i/4):e=i*Math.asin(1/r)/(2*Math.PI),(t*=2)<1?r*Math.pow(2,10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/i)*-.5:r*Math.pow(2,-10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/i)*.5+1)},backIn:function(t){var e=1.70158;return t*t*((e+1)*t-e)},backOut:function(t){var e=1.70158;return--t*t*((e+1)*t+e)+1},backInOut:function(t){var e=2.5949095;return(t*=2)<1?t*t*((e+1)*t-e)*.5:.5*((t-=2)*t*((e+1)*t+e)+2)},bounceIn:function(t){return 1-e.bounceOut(1-t)},bounceOut:function(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},bounceInOut:function(t){return t<.5?.5*e.bounceIn(2*t):.5*e.bounceOut(2*t-1)+.5}},r=e;t.exports=r},88983:function(t,e,r){r(32564);var i="undefined"!==typeof window&&(window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.msRequestAnimationFrame&&window.msRequestAnimationFrame.bind(window)||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(t){setTimeout(t,16)};t.exports=i},52215:function(t,e){var r=1;"undefined"!==typeof window&&(r=Math.max(window.devicePixelRatio||1,1));var i=0,n=r;e.debugMode=i,e.devicePixelRatio=n},5606:function(t,e,r){var i=r(54099),n=i.normalizeRadian,a=2*Math.PI;function o(t,e,r,i,o,s,h,l,u){if(0===h)return!1;var c=h;l-=t,u-=e;var f=Math.sqrt(l*l+u*u);if(f-c>r||f+c<r)return!1;if(Math.abs(i-o)%a<1e-4)return!0;if(s){var d=i;i=n(o),o=n(d)}else i=n(i),o=n(o);i>o&&(o+=a);var p=Math.atan2(u,l);return p<0&&(p+=a),p>=i&&p<=o||p+a>=i&&p+a<=o}e.containStroke=o},29483:function(t,e,r){var i=r(85723);function n(t,e,r,n,a,o,s,h,l,u,c){if(0===l)return!1;var f=l;if(c>e+f&&c>n+f&&c>o+f&&c>h+f||c<e-f&&c<n-f&&c<o-f&&c<h-f||u>t+f&&u>r+f&&u>a+f&&u>s+f||u<t-f&&u<r-f&&u<a-f&&u<s-f)return!1;var d=i.cubicProjectPoint(t,e,r,n,a,o,s,h,u,c,null);return d<=f/2}e.containStroke=n},79537:function(t,e){function r(t,e,r,i,n,a,o){if(0===n)return!1;var s=n,h=0,l=t;if(o>e+s&&o>i+s||o<e-s&&o<i-s||a>t+s&&a>r+s||a<t-s&&a<r-s)return!1;if(t===r)return Math.abs(a-t)<=s/2;h=(e-i)/(t-r),l=(t*i-r*e)/(t-r);var u=h*a-o+l,c=u*u/(h*h+1);return c<=s/2*s/2}e.containStroke=r},17053:function(t,e,r){var i=r(11348),n=r(79537),a=r(29483),o=r(88337),s=r(5606),h=r(54099),l=h.normalizeRadian,u=r(85723),c=r(53208),f=i.CMD,d=2*Math.PI,p=1e-4;function v(t,e){return Math.abs(t-e)<p}var g=[-1,-1,-1],y=[-1,-1];function _(){var t=y[0];y[0]=y[1],y[1]=t}function m(t,e,r,i,n,a,o,s,h,l){if(l>e&&l>i&&l>a&&l>s||l<e&&l<i&&l<a&&l<s)return 0;var c=u.cubicRootAt(e,i,a,s,l,g);if(0===c)return 0;for(var f,d,p=0,v=-1,m=0;m<c;m++){var x=g[m],w=0===x||1===x?.5:1,b=u.cubicAt(t,r,n,o,x);b<h||(v<0&&(v=u.cubicExtrema(e,i,a,s,y),y[1]<y[0]&&v>1&&_(),f=u.cubicAt(e,i,a,s,y[0]),v>1&&(d=u.cubicAt(e,i,a,s,y[1]))),2===v?x<y[0]?p+=f<e?w:-w:x<y[1]?p+=d<f?w:-w:p+=s<d?w:-w:x<y[0]?p+=f<e?w:-w:p+=s<f?w:-w)}return p}function x(t,e,r,i,n,a,o,s){if(s>e&&s>i&&s>a||s<e&&s<i&&s<a)return 0;var h=u.quadraticRootAt(e,i,a,s,g);if(0===h)return 0;var l=u.quadraticExtremum(e,i,a);if(l>=0&&l<=1){for(var c=0,f=u.quadraticAt(e,i,a,l),d=0;d<h;d++){var p=0===g[d]||1===g[d]?.5:1,v=u.quadraticAt(t,r,n,g[d]);v<o||(g[d]<l?c+=f<e?p:-p:c+=a<f?p:-p)}return c}p=0===g[0]||1===g[0]?.5:1,v=u.quadraticAt(t,r,n,g[0]);return v<o?0:a<e?p:-p}function w(t,e,r,i,n,a,o,s){if(s-=e,s>r||s<-r)return 0;var h=Math.sqrt(r*r-s*s);g[0]=-h,g[1]=h;var u=Math.abs(i-n);if(u<1e-4)return 0;if(u%d<1e-4){i=0,n=d;var c=a?1:-1;return o>=g[0]+t&&o<=g[1]+t?c:0}if(a){h=i;i=l(n),n=l(h)}else i=l(i),n=l(n);i>n&&(n+=d);for(var f=0,p=0;p<2;p++){var v=g[p];if(v+t>o){var y=Math.atan2(s,v);c=a?1:-1;y<0&&(y=d+y),(y>=i&&y<=n||y+d>=i&&y+d<=n)&&(y>Math.PI/2&&y<1.5*Math.PI&&(c=-c),f+=c)}}return f}function b(t,e,r,i,h){for(var l=0,u=0,d=0,p=0,g=0,y=0;y<t.length;){var _=t[y++];switch(_===f.M&&y>1&&(r||(l+=c(u,d,p,g,i,h))),1===y&&(u=t[y],d=t[y+1],p=u,g=d),_){case f.M:p=t[y++],g=t[y++],u=p,d=g;break;case f.L:if(r){if(n.containStroke(u,d,t[y],t[y+1],e,i,h))return!0}else l+=c(u,d,t[y],t[y+1],i,h)||0;u=t[y++],d=t[y++];break;case f.C:if(r){if(a.containStroke(u,d,t[y++],t[y++],t[y++],t[y++],t[y],t[y+1],e,i,h))return!0}else l+=m(u,d,t[y++],t[y++],t[y++],t[y++],t[y],t[y+1],i,h)||0;u=t[y++],d=t[y++];break;case f.Q:if(r){if(o.containStroke(u,d,t[y++],t[y++],t[y],t[y+1],e,i,h))return!0}else l+=x(u,d,t[y++],t[y++],t[y],t[y+1],i,h)||0;u=t[y++],d=t[y++];break;case f.A:var b=t[y++],k=t[y++],T=t[y++],S=t[y++],C=t[y++],P=t[y++];y+=1;var A=1-t[y++],M=Math.cos(C)*T+b,L=Math.sin(C)*S+k;y>1?l+=c(u,d,M,L,i,h):(p=M,g=L);var D=(i-b)*S/T+b;if(r){if(s.containStroke(b,k,S,C,C+P,A,e,D,h))return!0}else l+=w(b,k,S,C,C+P,A,D,h);u=Math.cos(C+P)*T+b,d=Math.sin(C+P)*S+k;break;case f.R:p=u=t[y++],g=d=t[y++];var z=t[y++],O=t[y++];M=p+z,L=g+O;if(r){if(n.containStroke(p,g,M,g,e,i,h)||n.containStroke(M,g,M,L,e,i,h)||n.containStroke(M,L,p,L,e,i,h)||n.containStroke(p,L,p,g,e,i,h))return!0}else l+=c(M,g,M,L,i,h),l+=c(p,L,p,g,i,h);break;case f.Z:if(r){if(n.containStroke(u,d,p,g,e,i,h))return!0}else l+=c(u,d,p,g,i,h);u=p,d=g;break}}return r||v(d,g)||(l+=c(u,d,p,g,i,h)||0),0!==l}function k(t,e,r){return b(t,0,!1,e,r)}function T(t,e,r,i){return b(t,e,!0,r,i)}e.contain=k,e.containStroke=T},33:function(t,e,r){var i=r(53208),n=1e-8;function a(t,e){return Math.abs(t-e)<n}function o(t,e,r){var n=0,o=t[0];if(!o)return!1;for(var s=1;s<t.length;s++){var h=t[s];n+=i(o[0],o[1],h[0],h[1],e,r),o=h}var l=t[0];return a(o[0],l[0])&&a(o[1],l[1])||(n+=i(o[0],o[1],l[0],l[1],e,r)),0!==n}e.contain=o},88337:function(t,e,r){var i=r(85723),n=i.quadraticProjectPoint;function a(t,e,r,i,a,o,s,h,l){if(0===s)return!1;var u=s;if(l>e+u&&l>i+u&&l>o+u||l<e-u&&l<i-u&&l<o-u||h>t+u&&h>r+u&&h>a+u||h<t-u&&h<r-u&&h<a-u)return!1;var c=n(t,e,r,i,a,o,h,l,null);return c<=u/2}e.containStroke=a},2278:function(t,e,r){var i=r(46630),n=r(44949),a=r(63122),o=a.getContext,s=a.extend,h=a.retrieve2,l=a.retrieve3,u=a.trim,c={},f=0,d=5e3,p=/\{([a-zA-Z0-9_]+)\|([^}]*)\}/g,v="12px sans-serif",g={};function y(t,e){g[t]=e}function _(t,e){e=e||v;var r=t+":"+e;if(c[r])return c[r];for(var i=(t+"").split("\n"),n=0,a=0,o=i.length;a<o;a++)n=Math.max(D(i[a],e).width,n);return f>d&&(f=0,c={}),f++,c[r]=n,n}function m(t,e,r,i,n,a,o,s){return o?w(t,e,r,i,n,a,o,s):x(t,e,r,i,n,a,s)}function x(t,e,r,n,a,o,s){var h=z(t,e,a,o,s),l=_(t,e);a&&(l+=a[1]+a[3]);var u=h.outerHeight,c=b(0,l,r),f=k(0,u,n),d=new i(c,f,l,u);return d.lineHeight=h.lineHeight,d}function w(t,e,r,n,a,o,s,h){var l=O(t,{rich:s,truncate:h,font:e,textAlign:r,textPadding:a,textLineHeight:o}),u=l.outerWidth,c=l.outerHeight,f=b(0,u,r),d=k(0,c,n);return new i(f,d,u,c)}function b(t,e,r){return"right"===r?t-=e:"center"===r&&(t-=e/2),t}function k(t,e,r){return"middle"===r?t-=e/2:"bottom"===r&&(t-=e),t}function T(t,e,r){var i=e.textPosition,n=e.textDistance,a=r.x,o=r.y;n=n||0;var s=r.height,h=r.width,l=s/2,u="left",c="top";switch(i){case"left":a-=n,o+=l,u="right",c="middle";break;case"right":a+=n+h,o+=l,c="middle";break;case"top":a+=h/2,o-=n,u="center",c="bottom";break;case"bottom":a+=h/2,o+=s+n,u="center";break;case"inside":a+=h/2,o+=l,u="center",c="middle";break;case"insideLeft":a+=n,o+=l,c="middle";break;case"insideRight":a+=h-n,o+=l,u="right",c="middle";break;case"insideTop":a+=h/2,o+=n,u="center";break;case"insideBottom":a+=h/2,o+=s-n,u="center",c="bottom";break;case"insideTopLeft":a+=n,o+=n;break;case"insideTopRight":a+=h-n,o+=n,u="right";break;case"insideBottomLeft":a+=n,o+=s-n,c="bottom";break;case"insideBottomRight":a+=h-n,o+=s-n,u="right",c="bottom";break}return t=t||{},t.x=a,t.y=o,t.textAlign=u,t.textVerticalAlign=c,t}function S(t,e,r){var i={textPosition:t,textDistance:r};return T({},i,e)}function C(t,e,r,i,n){if(!e)return"";var a=(t+"").split("\n");n=P(e,r,i,n);for(var o=0,s=a.length;o<s;o++)a[o]=A(a[o],n);return a.join("\n")}function P(t,e,r,i){i=s({},i),i.font=e;r=h(r,"...");i.maxIterations=h(i.maxIterations,2);var n=i.minChar=h(i.minChar,0);i.cnCharWidth=_("国",e);var a=i.ascCharWidth=_("a",e);i.placeholder=h(i.placeholder,"");for(var o=t=Math.max(0,t-1),l=0;l<n&&o>=a;l++)o-=a;var u=_(r,e);return u>o&&(r="",u=0),o=t-u,i.ellipsis=r,i.ellipsisWidth=u,i.contentWidth=o,i.containerWidth=t,i}function A(t,e){var r=e.containerWidth,i=e.font,n=e.contentWidth;if(!r)return"";var a=_(t,i);if(a<=r)return t;for(var o=0;;o++){if(a<=n||o>=e.maxIterations){t+=e.ellipsis;break}var s=0===o?M(t,n,e.ascCharWidth,e.cnCharWidth):a>0?Math.floor(t.length*n/a):0;t=t.substr(0,s),a=_(t,i)}return""===t&&(t=e.placeholder),t}function M(t,e,r,i){for(var n=0,a=0,o=t.length;a<o&&n<e;a++){var s=t.charCodeAt(a);n+=0<=s&&s<=127?r:i}return a}function L(t){return _("国",t)}function D(t,e){return g.measureText(t,e)}function z(t,e,r,i,n){null!=t&&(t+="");var a=h(i,L(e)),o=t?t.split("\n"):[],s=o.length*a,l=s,u=!0;if(r&&(l+=r[0]+r[2]),t&&n){u=!1;var c=n.outerHeight,f=n.outerWidth;if(null!=c&&l>c)t="",o=[];else if(null!=f)for(var d=P(f-(r?r[1]+r[3]:0),e,n.ellipsis,{minChar:n.minChar,placeholder:n.placeholder}),p=0,v=o.length;p<v;p++)o[p]=A(o[p],d)}return{lines:o,height:s,outerHeight:l,lineHeight:a,canCacheByTextString:u}}function O(t,e){var r={lines:[],width:0,height:0};if(null!=t&&(t+=""),!t)return r;var i,a=p.lastIndex=0;while(null!=(i=p.exec(t))){var o=i.index;o>a&&R(r,t.substring(a,o)),R(r,i[2],i[1]),a=p.lastIndex}a<t.length&&R(r,t.substring(a,t.length));var s=r.lines,u=0,c=0,f=[],d=e.textPadding,v=e.truncate,g=v&&v.outerWidth,y=v&&v.outerHeight;d&&(null!=g&&(g-=d[1]+d[3]),null!=y&&(y-=d[0]+d[2]));for(var m=0;m<s.length;m++){for(var x=s[m],w=0,b=0,k=0;k<x.tokens.length;k++){var T=x.tokens[k],S=T.styleName&&e.rich[T.styleName]||{},P=T.textPadding=S.textPadding,A=T.font=S.font||e.font,M=T.textHeight=h(S.textHeight,L(A));if(P&&(M+=P[0]+P[2]),T.height=M,T.lineHeight=l(S.textLineHeight,e.textLineHeight,M),T.textAlign=S&&S.textAlign||e.textAlign,T.textVerticalAlign=S&&S.textVerticalAlign||"middle",null!=y&&u+T.lineHeight>y)return{lines:[],width:0,height:0};T.textWidth=_(T.text,A);var D=S.textWidth,z=null==D||"auto"===D;if("string"===typeof D&&"%"===D.charAt(D.length-1))T.percentWidth=D,f.push(T),D=0;else{if(z){D=T.textWidth;var O=S.textBackgroundColor,I=O&&O.image;I&&(I=n.findExistImage(I),n.isImageReady(I)&&(D=Math.max(D,I.width*M/I.height)))}var F=P?P[1]+P[3]:0;D+=F;var B=null!=g?g-b:null;null!=B&&B<D&&(!z||B<F?(T.text="",T.textWidth=D=0):(T.text=C(T.text,B-F,A,v.ellipsis,{minChar:v.minChar}),T.textWidth=_(T.text,A),D=T.textWidth+F))}b+=T.width=D,S&&(w=Math.max(w,T.lineHeight))}x.width=b,x.lineHeight=w,u+=w,c=Math.max(c,b)}r.outerWidth=r.width=h(e.textWidth,c),r.outerHeight=r.height=h(e.textHeight,u),d&&(r.outerWidth+=d[1]+d[3],r.outerHeight+=d[0]+d[2]);for(m=0;m<f.length;m++){T=f[m];var E=T.percentWidth;T.width=parseInt(E,10)/100*c}return r}function R(t,e,r){for(var i=""===e,n=e.split("\n"),a=t.lines,o=0;o<n.length;o++){var s=n[o],h={styleName:r,text:s,isLineHolder:!s&&!i};if(o)a.push({tokens:[h]});else{var l=(a[a.length-1]||(a[0]={tokens:[]})).tokens,u=l.length;1===u&&l[0].isLineHolder?l[0]=h:(s||!u||i)&&l.push(h)}}}function I(t){var e=(t.fontSize||t.fontFamily)&&[t.fontStyle,t.fontWeight,(t.fontSize||12)+"px",t.fontFamily||"sans-serif"].join(" ");return e&&u(e)||t.textFont||t.font}g.measureText=function(t,e){var r=o();return r.font=e||v,r.measureText(t)},e.DEFAULT_FONT=v,e.$override=y,e.getWidth=_,e.getBoundingRect=m,e.adjustTextX=b,e.adjustTextY=k,e.calculateTextPosition=T,e.adjustTextPositionOnRect=S,e.truncateText=C,e.getLineHeight=L,e.measureText=D,e.parsePlainText=z,e.parseRichText=O,e.makeFont=I},54099:function(t,e){var r=2*Math.PI;function i(t){return t%=r,t<0&&(t+=r),t}e.normalizeRadian=i},53208:function(t){function e(t,e,r,i,n,a){if(a>e&&a>i||a<e&&a<i)return 0;if(i===e)return 0;var o=i<e?1:-1,s=(a-e)/(i-e);1!==s&&0!==s||(o=i<e?.5:-.5);var h=s*(r-t)+t;return h===n?1/0:h>n?o:0}t.exports=e},79116:function(t,e,r){var i=r(63122),n=r(33666),a=r(46630),o=function(t){for(var e in t=t||{},n.call(this,t),t)t.hasOwnProperty(e)&&(this[e]=t[e]);this._children=[],this.__storage=null,this.__dirty=!0};o.prototype={constructor:o,isGroup:!0,type:"group",silent:!1,children:function(){return this._children.slice()},childAt:function(t){return this._children[t]},childOfName:function(t){for(var e=this._children,r=0;r<e.length;r++)if(e[r].name===t)return e[r]},childCount:function(){return this._children.length},add:function(t){return t&&t!==this&&t.parent!==this&&(this._children.push(t),this._doAdd(t)),this},addBefore:function(t,e){if(t&&t!==this&&t.parent!==this&&e&&e.parent===this){var r=this._children,i=r.indexOf(e);i>=0&&(r.splice(i,0,t),this._doAdd(t))}return this},_doAdd:function(t){t.parent&&t.parent.remove(t),t.parent=this;var e=this.__storage,r=this.__zr;e&&e!==t.__storage&&(e.addToStorage(t),t instanceof o&&t.addChildrenToStorage(e)),r&&r.refresh()},remove:function(t){var e=this.__zr,r=this.__storage,n=this._children,a=i.indexOf(n,t);return a<0||(n.splice(a,1),t.parent=null,r&&(r.delFromStorage(t),t instanceof o&&t.delChildrenFromStorage(r)),e&&e.refresh()),this},removeAll:function(){var t,e,r=this._children,i=this.__storage;for(e=0;e<r.length;e++)t=r[e],i&&(i.delFromStorage(t),t instanceof o&&t.delChildrenFromStorage(i)),t.parent=null;return r.length=0,this},eachChild:function(t,e){for(var r=this._children,i=0;i<r.length;i++){var n=r[i];t.call(e,n,i)}return this},traverse:function(t,e){for(var r=0;r<this._children.length;r++){var i=this._children[r];t.call(e,i),"group"===i.type&&i.traverse(t,e)}return this},addChildrenToStorage:function(t){for(var e=0;e<this._children.length;e++){var r=this._children[e];t.addToStorage(r),r instanceof o&&r.addChildrenToStorage(t)}},delChildrenFromStorage:function(t){for(var e=0;e<this._children.length;e++){var r=this._children[e];t.delFromStorage(r),r instanceof o&&r.delChildrenFromStorage(t)}},dirty:function(){return this.__dirty=!0,this.__zr&&this.__zr.refresh(),this},getBoundingRect:function(t){for(var e=null,r=new a(0,0,0,0),i=t||this._children,n=[],o=0;o<i.length;o++){var s=i[o];if(!s.ignore&&!s.invisible){var h=s.getBoundingRect(),l=s.getLocalTransform(n);l?(r.copy(h),r.applyTransform(l),e=e||r.clone(),e.union(r)):(e=e||h.clone(),e.union(h))}}return e||r}},i.inherits(o,n);var s=o;t.exports=s},46630:function(t,e,r){var i=r(78244),n=r(97333),a=i.applyTransform,o=Math.min,s=Math.max;function h(t,e,r,i){r<0&&(t+=r,r=-r),i<0&&(e+=i,i=-i),this.x=t,this.y=e,this.width=r,this.height=i}h.prototype={constructor:h,union:function(t){var e=o(t.x,this.x),r=o(t.y,this.y);this.width=s(t.x+t.width,this.x+this.width)-e,this.height=s(t.y+t.height,this.y+this.height)-r,this.x=e,this.y=r},applyTransform:function(){var t=[],e=[],r=[],i=[];return function(n){if(n){t[0]=r[0]=this.x,t[1]=i[1]=this.y,e[0]=i[0]=this.x+this.width,e[1]=r[1]=this.y+this.height,a(t,t,n),a(e,e,n),a(r,r,n),a(i,i,n),this.x=o(t[0],e[0],r[0],i[0]),this.y=o(t[1],e[1],r[1],i[1]);var h=s(t[0],e[0],r[0],i[0]),l=s(t[1],e[1],r[1],i[1]);this.width=h-this.x,this.height=l-this.y}}}(),calculateTransform:function(t){var e=this,r=t.width/e.width,i=t.height/e.height,a=n.create();return n.translate(a,a,[-e.x,-e.y]),n.scale(a,a,[r,i]),n.translate(a,a,[t.x,t.y]),a},intersect:function(t){if(!t)return!1;t instanceof h||(t=h.create(t));var e=this,r=e.x,i=e.x+e.width,n=e.y,a=e.y+e.height,o=t.x,s=t.x+t.width,l=t.y,u=t.y+t.height;return!(i<o||s<r||a<l||u<n)},contain:function(t,e){var r=this;return t>=r.x&&t<=r.x+r.width&&e>=r.y&&e<=r.y+r.height},clone:function(){return new h(this.x,this.y,this.width,this.height)},copy:function(t){this.x=t.x,this.y=t.y,this.width=t.width,this.height=t.height},plain:function(){return{x:this.x,y:this.y,width:this.width,height:this.height}}},h.create=function(t){return new h(t.x,t.y,t.width,t.height)};var l=h;t.exports=l},40271:function(t,e,r){var i=r(13199),n=function(){this._track=[]};function a(t){var e=t[1][0]-t[0][0],r=t[1][1]-t[0][1];return Math.sqrt(e*e+r*r)}function o(t){return[(t[0][0]+t[1][0])/2,(t[0][1]+t[1][1])/2]}n.prototype={constructor:n,recognize:function(t,e,r){return this._doTrack(t,e,r),this._recognize(t)},clear:function(){return this._track.length=0,this},_doTrack:function(t,e,r){var n=t.touches;if(n){for(var a={points:[],touches:[],target:e,event:t},o=0,s=n.length;o<s;o++){var h=n[o],l=i.clientToLocal(r,h,{});a.points.push([l.zrX,l.zrY]),a.touches.push(h)}this._track.push(a)}},_recognize:function(t){for(var e in s)if(s.hasOwnProperty(e)){var r=s[e](this._track,t);if(r)return r}}};var s={pinch:function(t,e){var r=t.length;if(r){var i=(t[r-1]||{}).points,n=(t[r-2]||{}).points||i;if(n&&n.length>1&&i&&i.length>1){var s=a(i)/a(n);!isFinite(s)&&(s=1),e.pinchScale=s;var h=o(i);return e.pinchX=h[0],e.pinchY=h[1],{type:"pinch",target:t[0].target,event:e}}}}},h=n;t.exports=h},45746:function(t){var e=function(){this.head=null,this.tail=null,this._len=0},r=e.prototype;r.insert=function(t){var e=new i(t);return this.insertEntry(e),e},r.insertEntry=function(t){this.head?(this.tail.next=t,t.prev=this.tail,t.next=null,this.tail=t):this.head=this.tail=t,this._len++},r.remove=function(t){var e=t.prev,r=t.next;e?e.next=r:this.head=r,r?r.prev=e:this.tail=e,t.next=t.prev=null,this._len--},r.len=function(){return this._len},r.clear=function(){this.head=this.tail=null,this._len=0};var i=function(t){this.value=t,this.next,this.prev},n=function(t){this._list=new e,this._map={},this._maxSize=t||10,this._lastRemovedEntry=null},a=n.prototype;a.put=function(t,e){var r=this._list,n=this._map,a=null;if(null==n[t]){var o=r.len(),s=this._lastRemovedEntry;if(o>=this._maxSize&&o>0){var h=r.head;r.remove(h),delete n[h.key],a=h.value,this._lastRemovedEntry=h}s?s.value=e:s=new i(e),s.key=t,r.insertEntry(s),n[t]=s}return a},a.get=function(t){var e=this._map[t],r=this._list;if(null!=e)return e!==r.tail&&(r.remove(e),r.insertEntry(e)),e.value},a.clear=function(){this._list.clear(),this._map={}};var o=n;t.exports=o},11348:function(t,e,r){r(39575),r(38012);var i=r(85723),n=r(78244),a=r(9787),o=r(46630),s=r(52215),h=s.devicePixelRatio,l={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},u=[],c=[],f=[],d=[],p=Math.min,v=Math.max,g=Math.cos,y=Math.sin,_=Math.sqrt,m=Math.abs,x="undefined"!==typeof Float32Array,w=function(t){this._saveData=!t,this._saveData&&(this.data=[]),this._ctx=null};w.prototype={constructor:w,_xi:0,_yi:0,_x0:0,_y0:0,_ux:0,_uy:0,_len:0,_lineDash:null,_dashOffset:0,_dashIdx:0,_dashSum:0,setScale:function(t,e,r){r=r||0,this._ux=m(r/h/t)||0,this._uy=m(r/h/e)||0},getContext:function(){return this._ctx},beginPath:function(t){return this._ctx=t,t&&t.beginPath(),t&&(this.dpr=t.dpr),this._saveData&&(this._len=0),this._lineDash&&(this._lineDash=null,this._dashOffset=0),this},moveTo:function(t,e){return this.addData(l.M,t,e),this._ctx&&this._ctx.moveTo(t,e),this._x0=t,this._y0=e,this._xi=t,this._yi=e,this},lineTo:function(t,e){var r=m(t-this._xi)>this._ux||m(e-this._yi)>this._uy||this._len<5;return this.addData(l.L,t,e),this._ctx&&r&&(this._needsDash()?this._dashedLineTo(t,e):this._ctx.lineTo(t,e)),r&&(this._xi=t,this._yi=e),this},bezierCurveTo:function(t,e,r,i,n,a){return this.addData(l.C,t,e,r,i,n,a),this._ctx&&(this._needsDash()?this._dashedBezierTo(t,e,r,i,n,a):this._ctx.bezierCurveTo(t,e,r,i,n,a)),this._xi=n,this._yi=a,this},quadraticCurveTo:function(t,e,r,i){return this.addData(l.Q,t,e,r,i),this._ctx&&(this._needsDash()?this._dashedQuadraticTo(t,e,r,i):this._ctx.quadraticCurveTo(t,e,r,i)),this._xi=r,this._yi=i,this},arc:function(t,e,r,i,n,a){return this.addData(l.A,t,e,r,r,i,n-i,0,a?0:1),this._ctx&&this._ctx.arc(t,e,r,i,n,a),this._xi=g(n)*r+t,this._yi=y(n)*r+e,this},arcTo:function(t,e,r,i,n){return this._ctx&&this._ctx.arcTo(t,e,r,i,n),this},rect:function(t,e,r,i){return this._ctx&&this._ctx.rect(t,e,r,i),this.addData(l.R,t,e,r,i),this},closePath:function(){this.addData(l.Z);var t=this._ctx,e=this._x0,r=this._y0;return t&&(this._needsDash()&&this._dashedLineTo(e,r),t.closePath()),this._xi=e,this._yi=r,this},fill:function(t){t&&t.fill(),this.toStatic()},stroke:function(t){t&&t.stroke(),this.toStatic()},setLineDash:function(t){if(t instanceof Array){this._lineDash=t,this._dashIdx=0;for(var e=0,r=0;r<t.length;r++)e+=t[r];this._dashSum=e}return this},setLineDashOffset:function(t){return this._dashOffset=t,this},len:function(){return this._len},setData:function(t){var e=t.length;this.data&&this.data.length===e||!x||(this.data=new Float32Array(e));for(var r=0;r<e;r++)this.data[r]=t[r];this._len=e},appendPath:function(t){t instanceof Array||(t=[t]);for(var e=t.length,r=0,i=this._len,n=0;n<e;n++)r+=t[n].len();x&&this.data instanceof Float32Array&&(this.data=new Float32Array(i+r));for(n=0;n<e;n++)for(var a=t[n].data,o=0;o<a.length;o++)this.data[i++]=a[o];this._len=i},addData:function(t){if(this._saveData){var e=this.data;this._len+arguments.length>e.length&&(this._expandData(),e=this.data);for(var r=0;r<arguments.length;r++)e[this._len++]=arguments[r];this._prevCmd=t}},_expandData:function(){if(!(this.data instanceof Array)){for(var t=[],e=0;e<this._len;e++)t[e]=this.data[e];this.data=t}},_needsDash:function(){return this._lineDash},_dashedLineTo:function(t,e){var r,i,n=this._dashSum,a=this._dashOffset,o=this._lineDash,s=this._ctx,h=this._xi,l=this._yi,u=t-h,c=e-l,f=_(u*u+c*c),d=h,g=l,y=o.length;u/=f,c/=f,a<0&&(a=n+a),a%=n,d-=a*u,g-=a*c;while(u>0&&d<=t||u<0&&d>=t||0===u&&(c>0&&g<=e||c<0&&g>=e))i=this._dashIdx,r=o[i],d+=u*r,g+=c*r,this._dashIdx=(i+1)%y,u>0&&d<h||u<0&&d>h||c>0&&g<l||c<0&&g>l||s[i%2?"moveTo":"lineTo"](u>=0?p(d,t):v(d,t),c>=0?p(g,e):v(g,e));u=d-t,c=g-e,this._dashOffset=-_(u*u+c*c)},_dashedBezierTo:function(t,e,r,n,a,o){var s,h,l,u,c,f=this._dashSum,d=this._dashOffset,p=this._lineDash,v=this._ctx,g=this._xi,y=this._yi,m=i.cubicAt,x=0,w=this._dashIdx,b=p.length,k=0;for(d<0&&(d=f+d),d%=f,s=0;s<1;s+=.1)h=m(g,t,r,a,s+.1)-m(g,t,r,a,s),l=m(y,e,n,o,s+.1)-m(y,e,n,o,s),x+=_(h*h+l*l);for(;w<b;w++)if(k+=p[w],k>d)break;s=(k-d)/x;while(s<=1)u=m(g,t,r,a,s),c=m(y,e,n,o,s),w%2?v.moveTo(u,c):v.lineTo(u,c),s+=p[w]/x,w=(w+1)%b;w%2!==0&&v.lineTo(a,o),h=a-u,l=o-c,this._dashOffset=-_(h*h+l*l)},_dashedQuadraticTo:function(t,e,r,i){var n=r,a=i;r=(r+2*t)/3,i=(i+2*e)/3,t=(this._xi+2*t)/3,e=(this._yi+2*e)/3,this._dashedBezierTo(t,e,r,i,n,a)},toStatic:function(){var t=this.data;t instanceof Array&&(t.length=this._len,x&&(this.data=new Float32Array(t)))},getBoundingRect:function(){u[0]=u[1]=f[0]=f[1]=Number.MAX_VALUE,c[0]=c[1]=d[0]=d[1]=-Number.MAX_VALUE;for(var t=this.data,e=0,r=0,i=0,s=0,h=0;h<t.length;){var p=t[h++];switch(1===h&&(e=t[h],r=t[h+1],i=e,s=r),p){case l.M:i=t[h++],s=t[h++],e=i,r=s,f[0]=i,f[1]=s,d[0]=i,d[1]=s;break;case l.L:a.fromLine(e,r,t[h],t[h+1],f,d),e=t[h++],r=t[h++];break;case l.C:a.fromCubic(e,r,t[h++],t[h++],t[h++],t[h++],t[h],t[h+1],f,d),e=t[h++],r=t[h++];break;case l.Q:a.fromQuadratic(e,r,t[h++],t[h++],t[h],t[h+1],f,d),e=t[h++],r=t[h++];break;case l.A:var v=t[h++],_=t[h++],m=t[h++],x=t[h++],w=t[h++],b=t[h++]+w;h+=1;var k=1-t[h++];1===h&&(i=g(w)*m+v,s=y(w)*x+_),a.fromArc(v,_,m,x,w,b,k,f,d),e=g(b)*m+v,r=y(b)*x+_;break;case l.R:i=e=t[h++],s=r=t[h++];var T=t[h++],S=t[h++];a.fromLine(i,s,i+T,s+S,f,d);break;case l.Z:e=i,r=s;break}n.min(u,u,f),n.max(c,c,d)}return 0===h&&(u[0]=u[1]=c[0]=c[1]=0),new o(u[0],u[1],c[0]-u[0],c[1]-u[1])},rebuildPath:function(t){for(var e,r,i,n,a,o,s=this.data,h=this._ux,u=this._uy,c=this._len,f=0;f<c;){var d=s[f++];switch(1===f&&(i=s[f],n=s[f+1],e=i,r=n),d){case l.M:e=i=s[f++],r=n=s[f++],t.moveTo(i,n);break;case l.L:a=s[f++],o=s[f++],(m(a-i)>h||m(o-n)>u||f===c-1)&&(t.lineTo(a,o),i=a,n=o);break;case l.C:t.bezierCurveTo(s[f++],s[f++],s[f++],s[f++],s[f++],s[f++]),i=s[f-2],n=s[f-1];break;case l.Q:t.quadraticCurveTo(s[f++],s[f++],s[f++],s[f++]),i=s[f-2],n=s[f-1];break;case l.A:var p=s[f++],v=s[f++],_=s[f++],x=s[f++],w=s[f++],b=s[f++],k=s[f++],T=s[f++],S=_>x?_:x,C=_>x?1:_/x,P=_>x?x/_:1,A=Math.abs(_-x)>.001,M=w+b;A?(t.translate(p,v),t.rotate(k),t.scale(C,P),t.arc(0,0,S,w,M,1-T),t.scale(1/C,1/P),t.rotate(-k),t.translate(-p,-v)):t.arc(p,v,S,w,M,1-T),1===f&&(e=g(w)*_+p,r=y(w)*x+v),i=g(M)*_+p,n=y(M)*x+v;break;case l.R:e=i=s[f],r=n=s[f+1],t.rect(s[f++],s[f++],s[f++],s[f++]);break;case l.Z:t.closePath(),i=e,n=r}}}},w.CMD=l;var b=w;t.exports=b},62732:function(t){function e(){}function r(t,e,r,i){for(var n=0,a=e.length,o=0,s=0;n<a;n++){var h=e[n];if(h.removed){for(l=[],u=s;u<s+h.count;u++)l.push(u);h.indices=l,s+=h.count}else{for(var l=[],u=o;u<o+h.count;u++)l.push(u);h.indices=l,o+=h.count,h.added||(s+=h.count)}}return e}function i(t){return{newPos:t.newPos,components:t.components.slice(0)}}e.prototype={diff:function(t,e,n){n||(n=function(t,e){return t===e}),this.equals=n;var a=this;t=t.slice(),e=e.slice();var o=e.length,s=t.length,h=1,l=o+s,u=[{newPos:-1,components:[]}],c=this.extractCommon(u[0],e,t,0);if(u[0].newPos+1>=o&&c+1>=s){for(var f=[],d=0;d<e.length;d++)f.push(d);return[{indices:f,count:e.length}]}function p(){for(var n=-1*h;n<=h;n+=2){var l,c=u[n-1],f=u[n+1],d=(f?f.newPos:0)-n;c&&(u[n-1]=void 0);var p=c&&c.newPos+1<o,v=f&&0<=d&&d<s;if(p||v){if(!p||v&&c.newPos<f.newPos?(l=i(f),a.pushComponent(l.components,void 0,!0)):(l=c,l.newPos++,a.pushComponent(l.components,!0,void 0)),d=a.extractCommon(l,e,t,n),l.newPos+1>=o&&d+1>=s)return r(a,l.components,e,t);u[n]=l}else u[n]=void 0}h++}while(h<=l){var v=p();if(v)return v}},pushComponent:function(t,e,r){var i=t[t.length-1];i&&i.added===e&&i.removed===r?t[t.length-1]={count:i.count+1,added:e,removed:r}:t.push({count:1,added:e,removed:r})},extractCommon:function(t,e,r,i){var n=e.length,a=r.length,o=t.newPos,s=o-i,h=0;while(o+1<n&&s+1<a&&this.equals(e[o+1],r[s+1]))o++,s++,h++;return h&&t.components.push({count:h}),t.newPos=o,s},tokenize:function(t){return t.slice()},join:function(t){return t.slice()}};var n=new e;function a(t,e,r){return n.diff(t,e,r)}t.exports=a},9787:function(t,e,r){var i=r(78244),n=r(85723),a=Math.min,o=Math.max,s=Math.sin,h=Math.cos,l=2*Math.PI,u=i.create(),c=i.create(),f=i.create();function d(t,e,r){if(0!==t.length){var i,n=t[0],s=n[0],h=n[0],l=n[1],u=n[1];for(i=1;i<t.length;i++)n=t[i],s=a(s,n[0]),h=o(h,n[0]),l=a(l,n[1]),u=o(u,n[1]);e[0]=s,e[1]=l,r[0]=h,r[1]=u}}function p(t,e,r,i,n,s){n[0]=a(t,r),n[1]=a(e,i),s[0]=o(t,r),s[1]=o(e,i)}var v=[],g=[];function y(t,e,r,i,s,h,l,u,c,f){var d,p=n.cubicExtrema,y=n.cubicAt,_=p(t,r,s,l,v);for(c[0]=1/0,c[1]=1/0,f[0]=-1/0,f[1]=-1/0,d=0;d<_;d++){var m=y(t,r,s,l,v[d]);c[0]=a(m,c[0]),f[0]=o(m,f[0])}for(_=p(e,i,h,u,g),d=0;d<_;d++){var x=y(e,i,h,u,g[d]);c[1]=a(x,c[1]),f[1]=o(x,f[1])}c[0]=a(t,c[0]),f[0]=o(t,f[0]),c[0]=a(l,c[0]),f[0]=o(l,f[0]),c[1]=a(e,c[1]),f[1]=o(e,f[1]),c[1]=a(u,c[1]),f[1]=o(u,f[1])}function _(t,e,r,i,s,h,l,u){var c=n.quadraticExtremum,f=n.quadraticAt,d=o(a(c(t,r,s),1),0),p=o(a(c(e,i,h),1),0),v=f(t,r,s,d),g=f(e,i,h,p);l[0]=a(t,s,v),l[1]=a(e,h,g),u[0]=o(t,s,v),u[1]=o(e,h,g)}function m(t,e,r,n,a,o,d,p,v){var g=i.min,y=i.max,_=Math.abs(a-o);if(_%l<1e-4&&_>1e-4)return p[0]=t-r,p[1]=e-n,v[0]=t+r,void(v[1]=e+n);if(u[0]=h(a)*r+t,u[1]=s(a)*n+e,c[0]=h(o)*r+t,c[1]=s(o)*n+e,g(p,u,c),y(v,u,c),a%=l,a<0&&(a+=l),o%=l,o<0&&(o+=l),a>o&&!d?o+=l:a<o&&d&&(a+=l),d){var m=o;o=a,a=m}for(var x=0;x<o;x+=Math.PI/2)x>a&&(f[0]=h(x)*r+t,f[1]=s(x)*n+e,g(p,f,p),y(v,f,v))}e.fromPoints=d,e.fromLine=p,e.fromCubic=y,e.fromQuadratic=_,e.fromArc=m},85723:function(t,e,r){var i=r(78244),n=i.create,a=i.distSquare,o=Math.pow,s=Math.sqrt,h=1e-8,l=1e-4,u=s(3),c=1/3,f=n(),d=n(),p=n();function v(t){return t>-h&&t<h}function g(t){return t>h||t<-h}function y(t,e,r,i,n){var a=1-n;return a*a*(a*t+3*n*e)+n*n*(n*i+3*a*r)}function _(t,e,r,i,n){var a=1-n;return 3*(((e-t)*a+2*(r-e)*n)*a+(i-r)*n*n)}function m(t,e,r,i,n,a){var h=i+3*(e-r)-t,l=3*(r-2*e+t),f=3*(e-t),d=t-n,p=l*l-3*h*f,g=l*f-9*h*d,y=f*f-3*l*d,_=0;if(v(p)&&v(g))if(v(l))a[0]=0;else{var m=-f/l;m>=0&&m<=1&&(a[_++]=m)}else{var x=g*g-4*p*y;if(v(x)){var w=g/p,b=(m=-l/h+w,-w/2);m>=0&&m<=1&&(a[_++]=m),b>=0&&b<=1&&(a[_++]=b)}else if(x>0){var k=s(x),T=p*l+1.5*h*(-g+k),S=p*l+1.5*h*(-g-k);T=T<0?-o(-T,c):o(T,c),S=S<0?-o(-S,c):o(S,c);m=(-l-(T+S))/(3*h);m>=0&&m<=1&&(a[_++]=m)}else{var C=(2*p*l-3*h*g)/(2*s(p*p*p)),P=Math.acos(C)/3,A=s(p),M=Math.cos(P),L=(m=(-l-2*A*M)/(3*h),b=(-l+A*(M+u*Math.sin(P)))/(3*h),(-l+A*(M-u*Math.sin(P)))/(3*h));m>=0&&m<=1&&(a[_++]=m),b>=0&&b<=1&&(a[_++]=b),L>=0&&L<=1&&(a[_++]=L)}}return _}function x(t,e,r,i,n){var a=6*r-12*e+6*t,o=9*e+3*i-3*t-9*r,h=3*e-3*t,l=0;if(v(o)){if(g(a)){var u=-h/a;u>=0&&u<=1&&(n[l++]=u)}}else{var c=a*a-4*o*h;if(v(c))n[0]=-a/(2*o);else if(c>0){var f=s(c),d=(u=(-a+f)/(2*o),(-a-f)/(2*o));u>=0&&u<=1&&(n[l++]=u),d>=0&&d<=1&&(n[l++]=d)}}return l}function w(t,e,r,i,n,a){var o=(e-t)*n+t,s=(r-e)*n+e,h=(i-r)*n+r,l=(s-o)*n+o,u=(h-s)*n+s,c=(u-l)*n+l;a[0]=t,a[1]=o,a[2]=l,a[3]=c,a[4]=c,a[5]=u,a[6]=h,a[7]=i}function b(t,e,r,i,n,o,h,u,c,v,g){var _,m,x,w,b,k=.005,T=1/0;f[0]=c,f[1]=v;for(var S=0;S<1;S+=.05)d[0]=y(t,r,n,h,S),d[1]=y(e,i,o,u,S),w=a(f,d),w<T&&(_=S,T=w);T=1/0;for(var C=0;C<32;C++){if(k<l)break;m=_-k,x=_+k,d[0]=y(t,r,n,h,m),d[1]=y(e,i,o,u,m),w=a(d,f),m>=0&&w<T?(_=m,T=w):(p[0]=y(t,r,n,h,x),p[1]=y(e,i,o,u,x),b=a(p,f),x<=1&&b<T?(_=x,T=b):k*=.5)}return g&&(g[0]=y(t,r,n,h,_),g[1]=y(e,i,o,u,_)),s(T)}function k(t,e,r,i){var n=1-i;return n*(n*t+2*i*e)+i*i*r}function T(t,e,r,i){return 2*((1-i)*(e-t)+i*(r-e))}function S(t,e,r,i,n){var a=t-2*e+r,o=2*(e-t),h=t-i,l=0;if(v(a)){if(g(o)){var u=-h/o;u>=0&&u<=1&&(n[l++]=u)}}else{var c=o*o-4*a*h;if(v(c)){u=-o/(2*a);u>=0&&u<=1&&(n[l++]=u)}else if(c>0){var f=s(c),d=(u=(-o+f)/(2*a),(-o-f)/(2*a));u>=0&&u<=1&&(n[l++]=u),d>=0&&d<=1&&(n[l++]=d)}}return l}function C(t,e,r){var i=t+r-2*e;return 0===i?.5:(t-e)/i}function P(t,e,r,i,n){var a=(e-t)*i+t,o=(r-e)*i+e,s=(o-a)*i+a;n[0]=t,n[1]=a,n[2]=s,n[3]=s,n[4]=o,n[5]=r}function A(t,e,r,i,n,o,h,u,c){var v,g=.005,y=1/0;f[0]=h,f[1]=u;for(var _=0;_<1;_+=.05){d[0]=k(t,r,n,_),d[1]=k(e,i,o,_);var m=a(f,d);m<y&&(v=_,y=m)}y=1/0;for(var x=0;x<32;x++){if(g<l)break;var w=v-g,b=v+g;d[0]=k(t,r,n,w),d[1]=k(e,i,o,w);m=a(d,f);if(w>=0&&m<y)v=w,y=m;else{p[0]=k(t,r,n,b),p[1]=k(e,i,o,b);var T=a(p,f);b<=1&&T<y?(v=b,y=T):g*=.5}}return c&&(c[0]=k(t,r,n,v),c[1]=k(e,i,o,v)),s(y)}e.cubicAt=y,e.cubicDerivativeAt=_,e.cubicRootAt=m,e.cubicExtrema=x,e.cubicSubdivide=w,e.cubicProjectPoint=b,e.quadraticAt=k,e.quadraticDerivativeAt=T,e.quadraticRootAt=S,e.quadraticExtremum=C,e.quadraticSubdivide=P,e.quadraticProjectPoint=A},87086:function(t,e,r){var i=r(95160),n=r(46093),a=n.buildTransformer,o="___zrEVENTSAVED",s=[];function h(t,e,r,i,n){return l(s,e,i,n,!0)&&l(t,r,s[0],s[1])}function l(t,e,r,n,a){if(e.getBoundingClientRect&&i.domSupported&&!f(e)){var s=e[o]||(e[o]={}),h=u(e,s),l=c(h,s,a);if(l)return l(t,r,n),!0}return!1}function u(t,e){var r=e.markers;if(r)return r;r=e.markers=[];for(var i=["left","right"],n=["top","bottom"],a=0;a<4;a++){var o=document.createElement("div"),s=o.style,h=a%2,l=(a>>1)%2;s.cssText=["position: absolute","visibility: hidden","padding: 0","margin: 0","border-width: 0","user-select: none","width:0","height:0",i[h]+":0",n[l]+":0",i[1-h]+":auto",n[1-l]+":auto",""].join("!important;"),t.appendChild(o),r.push(o)}return r}function c(t,e,r){for(var i=r?"invTrans":"trans",n=e[i],o=e.srcCoords,s=!0,h=[],l=[],u=0;u<4;u++){var c=t[u].getBoundingClientRect(),f=2*u,d=c.left,p=c.top;h.push(d,p),s=s&&o&&d===o[f]&&p===o[f+1],l.push(t[u].offsetLeft,t[u].offsetTop)}return s&&n?n:(e.srcCoords=h,e[i]=r?a(l,h):a(h,l))}function f(t){return"CANVAS"===t.nodeName.toUpperCase()}e.transformLocalCoord=h,e.transformCoordWithViewport=l,e.isCanvasEl=f},95160:function(t,e,r){var i=r(57847)["default"],n={};n="object"===("undefined"===typeof wx?"undefined":i(wx))&&"function"===typeof wx.getSystemInfoSync?{browser:{},os:{},node:!1,wxa:!0,canvasSupported:!0,svgSupported:!1,touchEventsSupported:!0,domSupported:!1}:"undefined"===typeof document&&"undefined"!==typeof self?{browser:{},os:{},node:!1,worker:!0,canvasSupported:!0,domSupported:!1}:"undefined"===typeof navigator?{browser:{},os:{},node:!0,worker:!1,canvasSupported:!0,svgSupported:!0,domSupported:!1}:o(navigator.userAgent);var a=n;function o(t){var e={},r={},i=t.match(/Firefox\/([\d.]+)/),n=t.match(/MSIE\s([\d.]+)/)||t.match(/Trident\/.+?rv:(([\d.]+))/),a=t.match(/Edge\/([\d.]+)/),o=/micromessenger/i.test(t);return i&&(r.firefox=!0,r.version=i[1]),n&&(r.ie=!0,r.version=n[1]),a&&(r.edge=!0,r.version=a[1]),o&&(r.weChat=!0),{browser:r,os:e,node:!1,canvasSupported:!!document.createElement("canvas").getContext,svgSupported:"undefined"!==typeof SVGRect,touchEventsSupported:"ontouchstart"in window&&!r.ie&&!r.edge,pointerEventsSupported:"onpointerdown"in window&&(r.edge||r.ie&&r.version>=11),domSupported:"undefined"!==typeof document}}t.exports=a},13199:function(t,e,r){var i=r(65836);e.Dispatcher=i;var n=r(95160),a=r(87086),o=a.isCanvasEl,s=a.transformCoordWithViewport,h="undefined"!==typeof window&&!!window.addEventListener,l=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,u=[];function c(t,e,r,i){return r=r||{},i||!n.canvasSupported?f(t,e,r):n.browser.firefox&&null!=e.layerX&&e.layerX!==e.offsetX?(r.zrX=e.layerX,r.zrY=e.layerY):null!=e.offsetX?(r.zrX=e.offsetX,r.zrY=e.offsetY):f(t,e,r),r}function f(t,e,r){if(n.domSupported&&t.getBoundingClientRect){var i=e.clientX,a=e.clientY;if(o(t)){var h=t.getBoundingClientRect();return r.zrX=i-h.left,void(r.zrY=a-h.top)}if(s(u,t,i,a))return r.zrX=u[0],void(r.zrY=u[1])}r.zrX=r.zrY=0}function d(t){return t||window.event}function p(t,e,r){if(e=d(e),null!=e.zrX)return e;var i=e.type,n=i&&i.indexOf("touch")>=0;if(n){var a="touchend"!==i?e.targetTouches[0]:e.changedTouches[0];a&&c(t,a,e,r)}else c(t,e,e,r),e.zrDelta=e.wheelDelta?e.wheelDelta/120:-(e.detail||0)/3;var o=e.button;return null==e.which&&void 0!==o&&l.test(e.type)&&(e.which=1&o?1:2&o?3:4&o?2:0),e}function v(t,e,r,i){h?t.addEventListener(e,r,i):t.attachEvent("on"+e,r)}function g(t,e,r,i){h?t.removeEventListener(e,r,i):t.detachEvent("on"+e,r)}var y=h?function(t){t.preventDefault(),t.stopPropagation(),t.cancelBubble=!0}:function(t){t.returnValue=!1,t.cancelBubble=!0};function _(t){return 2===t.which||3===t.which}function m(t){return t.which>1}e.clientToLocal=c,e.getNativeEvent=d,e.normalizeEvent=p,e.addEventListener=v,e.removeEventListener=g,e.stop=y,e.isMiddleOrRightButtonOnMouseUpDown=_,e.notLeftMouse=m},46093:function(t,e){var r=Math.log(2);function i(t,e,n,a,o,s){var h=a+"-"+o,l=t.length;if(s.hasOwnProperty(h))return s[h];if(1===e){var u=Math.round(Math.log((1<<l)-1&~o)/r);return t[n][u]}var c=a|1<<n,f=n+1;while(a&1<<f)f++;for(var d=0,p=0,v=0;p<l;p++){var g=1<<p;g&o||(d+=(v%2?-1:1)*t[n][p]*i(t,e-1,f,c,o|g,s),v++)}return s[h]=d,d}function n(t,e){var r=[[t[0],t[1],1,0,0,0,-e[0]*t[0],-e[0]*t[1]],[0,0,0,t[0],t[1],1,-e[1]*t[0],-e[1]*t[1]],[t[2],t[3],1,0,0,0,-e[2]*t[2],-e[2]*t[3]],[0,0,0,t[2],t[3],1,-e[3]*t[2],-e[3]*t[3]],[t[4],t[5],1,0,0,0,-e[4]*t[4],-e[4]*t[5]],[0,0,0,t[4],t[5],1,-e[5]*t[4],-e[5]*t[5]],[t[6],t[7],1,0,0,0,-e[6]*t[6],-e[6]*t[7]],[0,0,0,t[6],t[7],1,-e[7]*t[6],-e[7]*t[7]]],n={},a=i(r,8,0,0,0,n);if(0!==a){for(var o=[],s=0;s<8;s++)for(var h=0;h<8;h++)null==o[h]&&(o[h]=0),o[h]+=((s+h)%2?-1:1)*i(r,7,0===s?1:0,1<<s,1<<h,n)/a*e[s];return function(t,e,r){var i=e*o[6]+r*o[7]+1;t[0]=(e*o[0]+r*o[1]+o[2])/i,t[1]=(e*o[3]+r*o[4]+o[5])/i}}}e.buildTransformer=n},26469:function(t){var e=2311;function r(){return e++}t.exports=r},70083:function(t,e,r){var i=r(52215),n=i.debugMode,a=function(){};1===n&&(a=console.error);var o=a;t.exports=o},97333:function(t,e,r){r(39575),r(38012);var i="undefined"===typeof Float32Array?Array:Float32Array;function n(){var t=new i(6);return a(t),t}function a(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,t}function o(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t}function s(t,e,r){var i=e[0]*r[0]+e[2]*r[1],n=e[1]*r[0]+e[3]*r[1],a=e[0]*r[2]+e[2]*r[3],o=e[1]*r[2]+e[3]*r[3],s=e[0]*r[4]+e[2]*r[5]+e[4],h=e[1]*r[4]+e[3]*r[5]+e[5];return t[0]=i,t[1]=n,t[2]=a,t[3]=o,t[4]=s,t[5]=h,t}function h(t,e,r){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4]+r[0],t[5]=e[5]+r[1],t}function l(t,e,r){var i=e[0],n=e[2],a=e[4],o=e[1],s=e[3],h=e[5],l=Math.sin(r),u=Math.cos(r);return t[0]=i*u+o*l,t[1]=-i*l+o*u,t[2]=n*u+s*l,t[3]=-n*l+u*s,t[4]=u*a+l*h,t[5]=u*h-l*a,t}function u(t,e,r){var i=r[0],n=r[1];return t[0]=e[0]*i,t[1]=e[1]*n,t[2]=e[2]*i,t[3]=e[3]*n,t[4]=e[4]*i,t[5]=e[5]*n,t}function c(t,e){var r=e[0],i=e[2],n=e[4],a=e[1],o=e[3],s=e[5],h=r*o-a*i;return h?(h=1/h,t[0]=o*h,t[1]=-a*h,t[2]=-i*h,t[3]=r*h,t[4]=(i*s-o*n)*h,t[5]=(a*n-r*s)*h,t):null}function f(t){var e=n();return o(e,t),e}e.create=n,e.identity=a,e.copy=o,e.mul=s,e.translate=h,e.rotate=l,e.scale=u,e.invert=c,e.clone=f},92757:function(t){var e=32,r=7;function i(t){var r=0;while(t>=e)r|=1&t,t>>=1;return t+r}function n(t,e,r,i){var n=e+1;if(n===r)return 1;if(i(t[n++],t[e])<0){while(n<r&&i(t[n],t[n-1])<0)n++;a(t,e,n)}else while(n<r&&i(t[n],t[n-1])>=0)n++;return n-e}function a(t,e,r){r--;while(e<r){var i=t[e];t[e++]=t[r],t[r--]=i}}function o(t,e,r,i,n){for(i===e&&i++;i<r;i++){var a,o=t[i],s=e,h=i;while(s<h)a=s+h>>>1,n(o,t[a])<0?h=a:s=a+1;var l=i-s;switch(l){case 3:t[s+3]=t[s+2];case 2:t[s+2]=t[s+1];case 1:t[s+1]=t[s];break;default:while(l>0)t[s+l]=t[s+l-1],l--}t[s]=o}}function s(t,e,r,i,n,a){var o=0,s=0,h=1;if(a(t,e[r+n])>0){s=i-n;while(h<s&&a(t,e[r+n+h])>0)o=h,h=1+(h<<1),h<=0&&(h=s);h>s&&(h=s),o+=n,h+=n}else{s=n+1;while(h<s&&a(t,e[r+n-h])<=0)o=h,h=1+(h<<1),h<=0&&(h=s);h>s&&(h=s);var l=o;o=n-h,h=n-l}o++;while(o<h){var u=o+(h-o>>>1);a(t,e[r+u])>0?o=u+1:h=u}return h}function h(t,e,r,i,n,a){var o=0,s=0,h=1;if(a(t,e[r+n])<0){s=n+1;while(h<s&&a(t,e[r+n-h])<0)o=h,h=1+(h<<1),h<=0&&(h=s);h>s&&(h=s);var l=o;o=n-h,h=n-l}else{s=i-n;while(h<s&&a(t,e[r+n+h])>=0)o=h,h=1+(h<<1),h<=0&&(h=s);h>s&&(h=s),o+=n,h+=n}o++;while(o<h){var u=o+(h-o>>>1);a(t,e[r+u])<0?h=u:o=u+1}return h}function l(t,e){var i,n,a=r,o=0,l=0;o=t.length;var u=[];function c(t,e){i[l]=t,n[l]=e,l+=1}function f(){while(l>1){var t=l-2;if(t>=1&&n[t-1]<=n[t]+n[t+1]||t>=2&&n[t-2]<=n[t]+n[t-1])n[t-1]<n[t+1]&&t--;else if(n[t]>n[t+1])break;p(t)}}function d(){while(l>1){var t=l-2;t>0&&n[t-1]<n[t+1]&&t--,p(t)}}function p(r){var a=i[r],o=n[r],u=i[r+1],c=n[r+1];n[r]=o+c,r===l-3&&(i[r+1]=i[r+2],n[r+1]=n[r+2]),l--;var f=h(t[u],t,a,o,0,e);a+=f,o-=f,0!==o&&(c=s(t[a+o-1],t,u,c,c-1,e),0!==c&&(o<=c?v(a,o,u,c):g(a,o,u,c)))}function v(i,n,o,l){var c=0;for(c=0;c<n;c++)u[c]=t[i+c];var f=0,d=o,p=i;if(t[p++]=t[d++],0!==--l)if(1!==n){var v,g,y,_=a;while(1){v=0,g=0,y=!1;do{if(e(t[d],u[f])<0){if(t[p++]=t[d++],g++,v=0,0===--l){y=!0;break}}else if(t[p++]=u[f++],v++,g=0,1===--n){y=!0;break}}while((v|g)<_);if(y)break;do{if(v=h(t[d],u,f,n,0,e),0!==v){for(c=0;c<v;c++)t[p+c]=u[f+c];if(p+=v,f+=v,n-=v,n<=1){y=!0;break}}if(t[p++]=t[d++],0===--l){y=!0;break}if(g=s(u[f],t,d,l,0,e),0!==g){for(c=0;c<g;c++)t[p+c]=t[d+c];if(p+=g,d+=g,l-=g,0===l){y=!0;break}}if(t[p++]=u[f++],1===--n){y=!0;break}_--}while(v>=r||g>=r);if(y)break;_<0&&(_=0),_+=2}if(a=_,a<1&&(a=1),1===n){for(c=0;c<l;c++)t[p+c]=t[d+c];t[p+l]=u[f]}else{if(0===n)throw new Error;for(c=0;c<n;c++)t[p+c]=u[f+c]}}else{for(c=0;c<l;c++)t[p+c]=t[d+c];t[p+l]=u[f]}else for(c=0;c<n;c++)t[p+c]=u[f+c]}function g(i,n,o,l){var c=0;for(c=0;c<l;c++)u[c]=t[o+c];var f=i+n-1,d=l-1,p=o+l-1,v=0,g=0;if(t[p--]=t[f--],0!==--n)if(1!==l){var y=a;while(1){var _=0,m=0,x=!1;do{if(e(u[d],t[f])<0){if(t[p--]=t[f--],_++,m=0,0===--n){x=!0;break}}else if(t[p--]=u[d--],m++,_=0,1===--l){x=!0;break}}while((_|m)<y);if(x)break;do{if(_=n-h(u[d],t,i,n,n-1,e),0!==_){for(p-=_,f-=_,n-=_,g=p+1,v=f+1,c=_-1;c>=0;c--)t[g+c]=t[v+c];if(0===n){x=!0;break}}if(t[p--]=u[d--],1===--l){x=!0;break}if(m=l-s(t[f],u,0,l,l-1,e),0!==m){for(p-=m,d-=m,l-=m,g=p+1,v=d+1,c=0;c<m;c++)t[g+c]=u[v+c];if(l<=1){x=!0;break}}if(t[p--]=t[f--],0===--n){x=!0;break}y--}while(_>=r||m>=r);if(x)break;y<0&&(y=0),y+=2}if(a=y,a<1&&(a=1),1===l){for(p-=n,f-=n,g=p+1,v=f+1,c=n-1;c>=0;c--)t[g+c]=t[v+c];t[p]=u[d]}else{if(0===l)throw new Error;for(v=p-(l-1),c=0;c<l;c++)t[v+c]=u[c]}}else{for(p-=n,f-=n,g=p+1,v=f+1,c=n-1;c>=0;c--)t[g+c]=t[v+c];t[p]=u[d]}else for(v=p-(l-1),c=0;c<l;c++)t[v+c]=u[c]}i=[],n=[],this.mergeRuns=f,this.forceMergeRuns=d,this.pushRun=c}function u(t,r,a,s){a||(a=0),s||(s=t.length);var h=s-a;if(!(h<2)){var u=0;if(h<e)return u=n(t,a,s,r),void o(t,a,s,a+u,r);var c=new l(t,r),f=i(h);do{if(u=n(t,a,s,r),u<f){var d=h;d>f&&(d=f),o(t,a,a+d,a+u,r),u=d}c.pushRun(a,u),c.mergeRuns(),h-=u,a+=u}while(0!==h);c.forceMergeRuns()}}t.exports=u},63122:function(t,e,r){var i=r(57847)["default"],n={"[object Function]":1,"[object RegExp]":1,"[object Date]":1,"[object Error]":1,"[object CanvasGradient]":1,"[object CanvasPattern]":1,"[object Image]":1,"[object Canvas]":1},a={"[object Int8Array]":1,"[object Uint8Array]":1,"[object Uint8ClampedArray]":1,"[object Int16Array]":1,"[object Uint16Array]":1,"[object Int32Array]":1,"[object Uint32Array]":1,"[object Float32Array]":1,"[object Float64Array]":1},o=Object.prototype.toString,s=Array.prototype,h=s.forEach,l=s.filter,u=s.slice,c=s.map,f=s.reduce,d={};function p(t,e){"createCanvas"===t&&(x=null),d[t]=e}function v(t){if(null==t||"object"!==i(t))return t;var e=t,r=o.call(t);if("[object Array]"===r){if(!Q(t)){e=[];for(var s=0,h=t.length;s<h;s++)e[s]=v(t[s])}}else if(a[r]){if(!Q(t)){var l=t.constructor;if(t.constructor.from)e=l.from(t);else{e=new l(t.length);for(s=0,h=t.length;s<h;s++)e[s]=v(t[s])}}}else if(!n[r]&&!Q(t)&&!j(t))for(var u in e={},t)t.hasOwnProperty(u)&&(e[u]=v(t[u]));return e}function g(t,e,r){if(!B(e)||!B(t))return r?v(e):t;for(var i in e)if(e.hasOwnProperty(i)){var n=t[i],a=e[i];!B(a)||!B(n)||R(a)||R(n)||j(a)||j(n)||E(a)||E(n)||Q(a)||Q(n)?!r&&i in t||(t[i]=v(e[i],!0)):g(n,a,r)}return t}function y(t,e){for(var r=t[0],i=1,n=t.length;i<n;i++)r=g(r,t[i],e);return r}function _(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r]);return t}function m(t,e,r){for(var i in e)e.hasOwnProperty(i)&&(r?null!=e[i]:null==t[i])&&(t[i]=e[i]);return t}var x,w=function(){return d.createCanvas()};function b(){return x||(x=w().getContext("2d")),x}function k(t,e){if(t){if(t.indexOf)return t.indexOf(e);for(var r=0,i=t.length;r<i;r++)if(t[r]===e)return r}return-1}function T(t,e){var r=t.prototype;function i(){}for(var n in i.prototype=e.prototype,t.prototype=new i,r)r.hasOwnProperty(n)&&(t.prototype[n]=r[n]);t.prototype.constructor=t,t.superClass=e}function S(t,e,r){t="prototype"in t?t.prototype:t,e="prototype"in e?e.prototype:e,m(t,e,r)}function C(t){if(t)return"string"!==typeof t&&"number"===typeof t.length}function P(t,e,r){if(t&&e)if(t.forEach&&t.forEach===h)t.forEach(e,r);else if(t.length===+t.length)for(var i=0,n=t.length;i<n;i++)e.call(r,t[i],i,t);else for(var a in t)t.hasOwnProperty(a)&&e.call(r,t[a],a,t)}function A(t,e,r){if(t&&e){if(t.map&&t.map===c)return t.map(e,r);for(var i=[],n=0,a=t.length;n<a;n++)i.push(e.call(r,t[n],n,t));return i}}function M(t,e,r,i){if(t&&e){if(t.reduce&&t.reduce===f)return t.reduce(e,r,i);for(var n=0,a=t.length;n<a;n++)r=e.call(i,r,t[n],n,t);return r}}function L(t,e,r){if(t&&e){if(t.filter&&t.filter===l)return t.filter(e,r);for(var i=[],n=0,a=t.length;n<a;n++)e.call(r,t[n],n,t)&&i.push(t[n]);return i}}function D(t,e,r){if(t&&e)for(var i=0,n=t.length;i<n;i++)if(e.call(r,t[i],i,t))return t[i]}function z(t,e){var r=u.call(arguments,2);return function(){return t.apply(e,r.concat(u.call(arguments)))}}function O(t){var e=u.call(arguments,1);return function(){return t.apply(this,e.concat(u.call(arguments)))}}function R(t){return"[object Array]"===o.call(t)}function I(t){return"function"===typeof t}function F(t){return"[object String]"===o.call(t)}function B(t){var e=i(t);return"function"===e||!!t&&"object"===e}function E(t){return!!n[o.call(t)]}function Z(t){return!!a[o.call(t)]}function j(t){return"object"===i(t)&&"number"===typeof t.nodeType&&"object"===i(t.ownerDocument)}function H(t){return t!==t}function N(t){for(var e=0,r=arguments.length;e<r;e++)if(null!=arguments[e])return arguments[e]}function W(t,e){return null!=t?t:e}function V(t,e,r){return null!=t?t:null!=e?e:r}function q(){return Function.call.apply(u,arguments)}function Y(t){if("number"===typeof t)return[t,t,t,t];var e=t.length;return 2===e?[t[0],t[1],t[0],t[1]]:3===e?[t[0],t[1],t[2],t[1]]:t}function X(t,e){if(!t)throw new Error(e)}function U(t){return null==t?null:"function"===typeof t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}d.createCanvas=function(){return document.createElement("canvas")};var $="__ec_primitive__";function G(t){t[$]=!0}function Q(t){return t[$]}function K(t){var e=R(t);this.data={};var r=this;function i(t,i){e?r.set(t,i):r.set(i,t)}t instanceof K?t.each(i):t&&P(t,i)}function J(t){return new K(t)}function tt(t,e){for(var r=new t.constructor(t.length+e.length),i=0;i<t.length;i++)r[i]=t[i];var n=t.length;for(i=0;i<e.length;i++)r[i+n]=e[i];return r}function et(){}K.prototype={constructor:K,get:function(t){return this.data.hasOwnProperty(t)?this.data[t]:null},set:function(t,e){return this.data[t]=e},each:function(t,e){for(var r in void 0!==e&&(t=z(t,e)),this.data)this.data.hasOwnProperty(r)&&t(this.data[r],r)},removeKey:function(t){delete this.data[t]}},e.$override=p,e.clone=v,e.merge=g,e.mergeAll=y,e.extend=_,e.defaults=m,e.createCanvas=w,e.getContext=b,e.indexOf=k,e.inherits=T,e.mixin=S,e.isArrayLike=C,e.each=P,e.map=A,e.reduce=M,e.filter=L,e.find=D,e.bind=z,e.curry=O,e.isArray=R,e.isFunction=I,e.isString=F,e.isObject=B,e.isBuiltInObject=E,e.isTypedArray=Z,e.isDom=j,e.eqNaN=H,e.retrieve=N,e.retrieve2=W,e.retrieve3=V,e.slice=q,e.normalizeCssArray=Y,e.assert=X,e.trim=U,e.setAsPrimitive=G,e.isPrimitive=Q,e.createHashMap=J,e.concatArray=tt,e.noop=et},78244:function(t,e,r){r(39575),r(38012);var i="undefined"===typeof Float32Array?Array:Float32Array;function n(t,e){var r=new i(2);return null==t&&(t=0),null==e&&(e=0),r[0]=t,r[1]=e,r}function a(t,e){return t[0]=e[0],t[1]=e[1],t}function o(t){var e=new i(2);return e[0]=t[0],e[1]=t[1],e}function s(t,e,r){return t[0]=e,t[1]=r,t}function h(t,e,r){return t[0]=e[0]+r[0],t[1]=e[1]+r[1],t}function l(t,e,r,i){return t[0]=e[0]+r[0]*i,t[1]=e[1]+r[1]*i,t}function u(t,e,r){return t[0]=e[0]-r[0],t[1]=e[1]-r[1],t}function c(t){return Math.sqrt(d(t))}var f=c;function d(t){return t[0]*t[0]+t[1]*t[1]}var p=d;function v(t,e,r){return t[0]=e[0]*r[0],t[1]=e[1]*r[1],t}function g(t,e,r){return t[0]=e[0]/r[0],t[1]=e[1]/r[1],t}function y(t,e){return t[0]*e[0]+t[1]*e[1]}function _(t,e,r){return t[0]=e[0]*r,t[1]=e[1]*r,t}function m(t,e){var r=c(e);return 0===r?(t[0]=0,t[1]=0):(t[0]=e[0]/r,t[1]=e[1]/r),t}function x(t,e){return Math.sqrt((t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1]))}var w=x;function b(t,e){return(t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1])}var k=b;function T(t,e){return t[0]=-e[0],t[1]=-e[1],t}function S(t,e,r,i){return t[0]=e[0]+i*(r[0]-e[0]),t[1]=e[1]+i*(r[1]-e[1]),t}function C(t,e,r){var i=e[0],n=e[1];return t[0]=r[0]*i+r[2]*n+r[4],t[1]=r[1]*i+r[3]*n+r[5],t}function P(t,e,r){return t[0]=Math.min(e[0],r[0]),t[1]=Math.min(e[1],r[1]),t}function A(t,e,r){return t[0]=Math.max(e[0],r[0]),t[1]=Math.max(e[1],r[1]),t}e.create=n,e.copy=a,e.clone=o,e.set=s,e.add=h,e.scaleAndAdd=l,e.sub=u,e.len=c,e.length=f,e.lenSquare=d,e.lengthSquare=p,e.mul=v,e.div=g,e.dot=y,e.scale=_,e.normalize=m,e.distance=x,e.dist=w,e.distanceSquare=b,e.distSquare=k,e.negate=T,e.lerp=S,e.applyTransform=C,e.min=P,e.max=A},48107:function(t,e,r){r(32564);var i=r(13199),n=i.addEventListener,a=i.removeEventListener,o=i.normalizeEvent,s=i.getNativeEvent,h=r(63122),l=r(65836),u=r(95160),c=300,f=u.domSupported,d=function(){var t=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],e=["touchstart","touchend","touchmove"],r={pointerdown:1,pointerup:1,pointermove:1,pointerout:1},i=h.map(t,(function(t){var e=t.replace("mouse","pointer");return r.hasOwnProperty(e)?e:t}));return{mouse:t,touch:e,pointer:i}}(),p={mouse:["mousemove","mouseup"],pointer:["pointermove","pointerup"]};function v(t){return"mousewheel"===t&&u.browser.firefox?"DOMMouseScroll":t}function g(t){var e=t.pointerType;return"pen"===e||"touch"===e}function y(t){t.touching=!0,null!=t.touchTimer&&(clearTimeout(t.touchTimer),t.touchTimer=null),t.touchTimer=setTimeout((function(){t.touching=!1,t.touchTimer=null}),700)}function _(t){t&&(t.zrByTouch=!0)}function m(t,e){return o(t.dom,new w(t,e),!0)}function x(t,e){var r=e,i=!1;while(r&&9!==r.nodeType&&!(i=r.domBelongToZr||r!==e&&r===t.painterRoot))r=r.parentNode;return i}function w(t,e){this.type=e.type,this.target=this.currentTarget=t.dom,this.pointerType=e.pointerType,this.clientX=e.clientX,this.clientY=e.clientY}var b=w.prototype;b.stopPropagation=b.stopImmediatePropagation=b.preventDefault=h.noop;var k={mousedown:function(t){t=o(this.dom,t),this._mayPointerCapture=[t.zrX,t.zrY],this.trigger("mousedown",t)},mousemove:function(t){t=o(this.dom,t);var e=this._mayPointerCapture;!e||t.zrX===e[0]&&t.zrY===e[1]||M(this,!0),this.trigger("mousemove",t)},mouseup:function(t){t=o(this.dom,t),M(this,!1),this.trigger("mouseup",t)},mouseout:function(t){t=o(this.dom,t),this._pointerCapturing&&(t.zrEventControl="no_globalout");var e=t.toElement||t.relatedTarget;t.zrIsToLocalDOM=x(this,e),this.trigger("mouseout",t)},touchstart:function(t){t=o(this.dom,t),_(t),this._lastTouchMoment=new Date,this.handler.processGesture(t,"start"),k.mousemove.call(this,t),k.mousedown.call(this,t)},touchmove:function(t){t=o(this.dom,t),_(t),this.handler.processGesture(t,"change"),k.mousemove.call(this,t)},touchend:function(t){t=o(this.dom,t),_(t),this.handler.processGesture(t,"end"),k.mouseup.call(this,t),+new Date-this._lastTouchMoment<c&&k.click.call(this,t)},pointerdown:function(t){k.mousedown.call(this,t)},pointermove:function(t){g(t)||k.mousemove.call(this,t)},pointerup:function(t){k.mouseup.call(this,t)},pointerout:function(t){g(t)||k.mouseout.call(this,t)}};h.each(["click","mousewheel","dblclick","contextmenu"],(function(t){k[t]=function(e){e=o(this.dom,e),this.trigger(t,e)}}));var T={pointermove:function(t){g(t)||T.mousemove.call(this,t)},pointerup:function(t){T.mouseup.call(this,t)},mousemove:function(t){this.trigger("mousemove",t)},mouseup:function(t){var e=this._pointerCapturing;M(this,!1),this.trigger("mouseup",t),e&&(t.zrEventControl="only_globalout",this.trigger("mouseout",t))}};function S(t,e){var r=e.domHandlers;u.pointerEventsSupported?h.each(d.pointer,(function(i){P(e,i,(function(e){r[i].call(t,e)}))})):(u.touchEventsSupported&&h.each(d.touch,(function(i){P(e,i,(function(n){r[i].call(t,n),y(e)}))})),h.each(d.mouse,(function(i){P(e,i,(function(n){n=s(n),e.touching||r[i].call(t,n)}))})))}function C(t,e){function r(r){function i(i){i=s(i),x(t,i.target)||(i=m(t,i),e.domHandlers[r].call(t,i))}P(e,r,i,{capture:!0})}u.pointerEventsSupported?h.each(p.pointer,r):u.touchEventsSupported||h.each(p.mouse,r)}function P(t,e,r,i){t.mounted[e]=r,t.listenerOpts[e]=i,n(t.domTarget,v(e),r,i)}function A(t){var e=t.mounted;for(var r in e)e.hasOwnProperty(r)&&a(t.domTarget,v(r),e[r],t.listenerOpts[r]);t.mounted={}}function M(t,e){if(t._mayPointerCapture=null,f&&t._pointerCapturing^e){t._pointerCapturing=e;var r=t._globalHandlerScope;e?C(t,r):A(r)}}function L(t,e){this.domTarget=t,this.domHandlers=e,this.mounted={},this.listenerOpts={},this.touchTimer=null,this.touching=!1}function D(t,e){l.call(this),this.dom=t,this.painterRoot=e,this._localHandlerScope=new L(t,k),f&&(this._globalHandlerScope=new L(document,T)),this._pointerCapturing=!1,this._mayPointerCapture=null,S(this,this._localHandlerScope)}var z=D.prototype;z.dispose=function(){A(this._localHandlerScope),f&&A(this._globalHandlerScope)},z.setCursor=function(t){this.dom.style&&(this.dom.style.cursor=t||"default")},h.mixin(D,l);var O=D;t.exports=O},71179:function(t,e,r){var i=r(73477),n=i.extend({type:"compound",shape:{paths:null},_updatePathDirty:function(){for(var t=this.__dirtyPath,e=this.shape.paths,r=0;r<e.length;r++)t=t||e[r].__dirtyPath;this.__dirtyPath=t,this.__dirty=this.__dirty||t},beforeBrush:function(){this._updatePathDirty();for(var t=this.shape.paths||[],e=this.getGlobalScale(),r=0;r<t.length;r++)t[r].path||t[r].createPathProxy(),t[r].path.setScale(e[0],e[1],t[r].segmentIgnoreThreshold)},buildPath:function(t,e){for(var r=e.paths||[],i=0;i<r.length;i++)r[i].buildPath(t,r[i].shape,!0)},afterBrush:function(){for(var t=this.shape.paths||[],e=0;e<t.length;e++)t[e].__dirtyPath=!1},getBoundingRect:function(){return this._updatePathDirty(),i.prototype.getBoundingRect.call(this)}});t.exports=n},84093:function(t,e,r){var i=r(63122),n=r(82516),a=r(33666),o=r(73637);function s(t){for(var e in t=t||{},a.call(this,t),t)t.hasOwnProperty(e)&&"style"!==e&&(this[e]=t[e]);this.style=new n(t.style,this),this._rect=null,this.__clipPaths=null}s.prototype={constructor:s,type:"displayable",__dirty:!0,invisible:!1,z:0,z2:0,zlevel:0,draggable:!1,dragging:!1,silent:!1,culling:!1,cursor:"pointer",rectHover:!1,progressive:!1,incremental:!1,globalScaleRatio:1,beforeBrush:function(t){},afterBrush:function(t){},brush:function(t,e){},getBoundingRect:function(){},contain:function(t,e){return this.rectContain(t,e)},traverse:function(t,e){t.call(e,this)},rectContain:function(t,e){var r=this.transformCoordToLocal(t,e),i=this.getBoundingRect();return i.contain(r[0],r[1])},dirty:function(){this.__dirty=this.__dirtyText=!0,this._rect=null,this.__zr&&this.__zr.refresh()},animateStyle:function(t){return this.animate("style",t)},attrKV:function(t,e){"style"!==t?a.prototype.attrKV.call(this,t,e):this.style.set(e)},setStyle:function(t,e){return this.style.set(t,e),this.dirty(!1),this},useStyle:function(t){return this.style=new n(t,this),this.dirty(!1),this},calculateTextPosition:null},i.inherits(s,a),i.mixin(s,o);var h=s;t.exports=h},80841:function(t){var e=function(t){this.colorStops=t||[]};e.prototype={constructor:e,addColorStop:function(t,e){this.colorStops.push({offset:t,color:e})}};var r=e;t.exports=r},45474:function(t,e,r){var i=r(84093),n=r(46630),a=r(63122),o=r(44949);function s(t){i.call(this,t)}s.prototype={constructor:s,type:"image",brush:function(t,e){var r=this.style,i=r.image;r.bind(t,this,e);var n=this._image=o.createOrUpdateImage(i,this._image,this,this.onload);if(n&&o.isImageReady(n)){var a=r.x||0,s=r.y||0,h=r.width,l=r.height,u=n.width/n.height;if(null==h&&null!=l?h=l*u:null==l&&null!=h?l=h/u:null==h&&null==l&&(h=n.width,l=n.height),this.setTransform(t),r.sWidth&&r.sHeight){var c=r.sx||0,f=r.sy||0;t.drawImage(n,c,f,r.sWidth,r.sHeight,a,s,h,l)}else if(r.sx&&r.sy){c=r.sx,f=r.sy;var d=h-c,p=l-f;t.drawImage(n,c,f,d,p,a,s,h,l)}else t.drawImage(n,a,s,h,l);null!=r.text&&(this.restoreTransform(t),this.drawRectText(t,this.getBoundingRect()))}},getBoundingRect:function(){var t=this.style;return this._rect||(this._rect=new n(t.x||0,t.y||0,t.width||0,t.height||0)),this._rect}},a.inherits(s,i);var h=s;t.exports=h},36418:function(t,e,r){var i=r(63122),n=i.inherits,a=r(84093),o=r(46630);function s(t){a.call(this,t),this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.notClear=!0}s.prototype.incremental=!0,s.prototype.clearDisplaybles=function(){this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.dirty(),this.notClear=!1},s.prototype.addDisplayable=function(t,e){e?this._temporaryDisplayables.push(t):this._displayables.push(t),this.dirty()},s.prototype.addDisplayables=function(t,e){e=e||!1;for(var r=0;r<t.length;r++)this.addDisplayable(t[r],e)},s.prototype.eachPendingDisplayable=function(t){for(var e=this._cursor;e<this._displayables.length;e++)t&&t(this._displayables[e]);for(e=0;e<this._temporaryDisplayables.length;e++)t&&t(this._temporaryDisplayables[e])},s.prototype.update=function(){this.updateTransform();for(var t=this._cursor;t<this._displayables.length;t++){var e=this._displayables[t];e.parent=this,e.update(),e.parent=null}for(t=0;t<this._temporaryDisplayables.length;t++){e=this._temporaryDisplayables[t];e.parent=this,e.update(),e.parent=null}},s.prototype.brush=function(t,e){for(var r=this._cursor;r<this._displayables.length;r++){var i=this._displayables[r];i.beforeBrush&&i.beforeBrush(t),i.brush(t,r===this._cursor?null:this._displayables[r-1]),i.afterBrush&&i.afterBrush(t)}this._cursor=r;for(r=0;r<this._temporaryDisplayables.length;r++){i=this._temporaryDisplayables[r];i.beforeBrush&&i.beforeBrush(t),i.brush(t,0===r?null:this._temporaryDisplayables[r-1]),i.afterBrush&&i.afterBrush(t)}this._temporaryDisplayables=[],this.notClear=!0};var h=[];s.prototype.getBoundingRect=function(){if(!this._rect){for(var t=new o(1/0,1/0,-1/0,-1/0),e=0;e<this._displayables.length;e++){var r=this._displayables[e],i=r.getBoundingRect().clone();r.needLocalTransform()&&i.applyTransform(r.getLocalTransform(h)),t.union(i)}this._rect=t}return this._rect},s.prototype.contain=function(t,e){var r=this.transformCoordToLocal(t,e),i=this.getBoundingRect();if(i.contain(r[0],r[1]))for(var n=0;n<this._displayables.length;n++){var a=this._displayables[n];if(a.contain(t,e))return!0}return!1},n(s,a);var l=s;t.exports=l},48515:function(t,e,r){var i=r(63122),n=r(80841),a=function(t,e,r,i,a,o){this.x=null==t?0:t,this.y=null==e?0:e,this.x2=null==r?1:r,this.y2=null==i?0:i,this.type="linear",this.global=o||!1,n.call(this,a)};a.prototype={constructor:a},i.inherits(a,n);var o=a;t.exports=o},73477:function(t,e,r){var i=r(84093),n=r(63122),a=r(11348),o=r(17053),s=r(89872),h=s.prototype.getCanvasPattern,l=Math.abs,u=new a(!0);function c(t){i.call(this,t),this.path=null}c.prototype={constructor:c,type:"path",__dirtyPath:!0,strokeContainThreshold:5,segmentIgnoreThreshold:0,subPixelOptimize:!1,brush:function(t,e){var r,i=this.style,n=this.path||u,a=i.hasStroke(),o=i.hasFill(),s=i.fill,l=i.stroke,c=o&&!!s.colorStops,f=a&&!!l.colorStops,d=o&&!!s.image,p=a&&!!l.image;(i.bind(t,this,e),this.setTransform(t),this.__dirty)&&(c&&(r=r||this.getBoundingRect(),this._fillGradient=i.getGradient(t,s,r)),f&&(r=r||this.getBoundingRect(),this._strokeGradient=i.getGradient(t,l,r)));c?t.fillStyle=this._fillGradient:d&&(t.fillStyle=h.call(s,t)),f?t.strokeStyle=this._strokeGradient:p&&(t.strokeStyle=h.call(l,t));var v=i.lineDash,g=i.lineDashOffset,y=!!t.setLineDash,_=this.getGlobalScale();if(n.setScale(_[0],_[1],this.segmentIgnoreThreshold),this.__dirtyPath||v&&!y&&a?(n.beginPath(t),v&&!y&&(n.setLineDash(v),n.setLineDashOffset(g)),this.buildPath(n,this.shape,!1),this.path&&(this.__dirtyPath=!1)):(t.beginPath(),this.path.rebuildPath(t)),o)if(null!=i.fillOpacity){var m=t.globalAlpha;t.globalAlpha=i.fillOpacity*i.opacity,n.fill(t),t.globalAlpha=m}else n.fill(t);if(v&&y&&(t.setLineDash(v),t.lineDashOffset=g),a)if(null!=i.strokeOpacity){m=t.globalAlpha;t.globalAlpha=i.strokeOpacity*i.opacity,n.stroke(t),t.globalAlpha=m}else n.stroke(t);v&&y&&t.setLineDash([]),null!=i.text&&(this.restoreTransform(t),this.drawRectText(t,this.getBoundingRect()))},buildPath:function(t,e,r){},createPathProxy:function(){this.path=new a},getBoundingRect:function(){var t=this._rect,e=this.style,r=!t;if(r){var i=this.path;i||(i=this.path=new a),this.__dirtyPath&&(i.beginPath(),this.buildPath(i,this.shape,!1)),t=i.getBoundingRect()}if(this._rect=t,e.hasStroke()){var n=this._rectWithStroke||(this._rectWithStroke=t.clone());if(this.__dirty||r){n.copy(t);var o=e.lineWidth,s=e.strokeNoScale?this.getLineScale():1;e.hasFill()||(o=Math.max(o,this.strokeContainThreshold||4)),s>1e-10&&(n.width+=o/s,n.height+=o/s,n.x-=o/s/2,n.y-=o/s/2)}return n}return t},contain:function(t,e){var r=this.transformCoordToLocal(t,e),i=this.getBoundingRect(),n=this.style;if(t=r[0],e=r[1],i.contain(t,e)){var a=this.path.data;if(n.hasStroke()){var s=n.lineWidth,h=n.strokeNoScale?this.getLineScale():1;if(h>1e-10&&(n.hasFill()||(s=Math.max(s,this.strokeContainThreshold)),o.containStroke(a,s/h,t,e)))return!0}if(n.hasFill())return o.contain(a,t,e)}return!1},dirty:function(t){null==t&&(t=!0),t&&(this.__dirtyPath=t,this._rect=null),this.__dirty=this.__dirtyText=!0,this.__zr&&this.__zr.refresh(),this.__clipTarget&&this.__clipTarget.dirty()},animateShape:function(t){return this.animate("shape",t)},attrKV:function(t,e){"shape"===t?(this.setShape(e),this.__dirtyPath=!0,this._rect=null):i.prototype.attrKV.call(this,t,e)},setShape:function(t,e){var r=this.shape;if(r){if(n.isObject(t))for(var i in t)t.hasOwnProperty(i)&&(r[i]=t[i]);else r[t]=e;this.dirty(!0)}return this},getLineScale:function(){var t=this.transform;return t&&l(t[0]-1)>1e-10&&l(t[3]-1)>1e-10?Math.sqrt(l(t[0]*t[3]-t[2]*t[1])):1}},c.extend=function(t){var e=function(e){c.call(this,e),t.style&&this.style.extendFrom(t.style,!1);var r=t.shape;if(r){this.shape=this.shape||{};var i=this.shape;for(var n in r)!i.hasOwnProperty(n)&&r.hasOwnProperty(n)&&(i[n]=r[n])}t.init&&t.init.call(this,e)};for(var r in n.inherits(e,c),t)"style"!==r&&"shape"!==r&&(e.prototype[r]=t[r]);return e},n.inherits(c,i);var f=c;t.exports=f},89872:function(t){var e=function(t,e){this.image=t,this.repeat=e,this.type="pattern"};e.prototype.getCanvasPattern=function(t){return t.createPattern(this.image,this.repeat||"repeat")};var r=e;t.exports=r},58581:function(t,e,r){var i=r(63122),n=r(80841),a=function(t,e,r,i,a){this.x=null==t?.5:t,this.y=null==e?.5:e,this.r=null==r?.5:r,this.type="radial",this.global=a||!1,n.call(this,i)};a.prototype={constructor:a},i.inherits(a,n);var o=a;t.exports=o},82516:function(t,e,r){var i=r(84108),n=r(80023),a=n.ContextCachedBy,o=[["shadowBlur",0],["shadowOffsetX",0],["shadowOffsetY",0],["shadowColor","#000"],["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]],s=function(t){this.extendFrom(t,!1)};function h(t,e,r){var i=null==e.x?0:e.x,n=null==e.x2?1:e.x2,a=null==e.y?0:e.y,o=null==e.y2?0:e.y2;e.global||(i=i*r.width+r.x,n=n*r.width+r.x,a=a*r.height+r.y,o=o*r.height+r.y),i=isNaN(i)?0:i,n=isNaN(n)?1:n,a=isNaN(a)?0:a,o=isNaN(o)?0:o;var s=t.createLinearGradient(i,a,n,o);return s}function l(t,e,r){var i=r.width,n=r.height,a=Math.min(i,n),o=null==e.x?.5:e.x,s=null==e.y?.5:e.y,h=null==e.r?.5:e.r;e.global||(o=o*i+r.x,s=s*n+r.y,h*=a);var l=t.createRadialGradient(o,s,0,o,s,h);return l}s.prototype={constructor:s,fill:"#000",stroke:null,opacity:1,fillOpacity:null,strokeOpacity:null,lineDash:null,lineDashOffset:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,lineWidth:1,strokeNoScale:!1,text:null,font:null,textFont:null,fontStyle:null,fontWeight:null,fontSize:null,fontFamily:null,textTag:null,textFill:"#000",textStroke:null,textWidth:null,textHeight:null,textStrokeWidth:0,textLineHeight:null,textPosition:"inside",textRect:null,textOffset:null,textAlign:null,textVerticalAlign:null,textDistance:5,textShadowColor:"transparent",textShadowBlur:0,textShadowOffsetX:0,textShadowOffsetY:0,textBoxShadowColor:"transparent",textBoxShadowBlur:0,textBoxShadowOffsetX:0,textBoxShadowOffsetY:0,transformText:!1,textRotation:0,textOrigin:null,textBackgroundColor:null,textBorderColor:null,textBorderWidth:0,textBorderRadius:0,textPadding:null,rich:null,truncate:null,blend:null,bind:function(t,e,r){var n=this,s=r&&r.style,h=!s||t.__attrCachedBy!==a.STYLE_BIND;t.__attrCachedBy=a.STYLE_BIND;for(var l=0;l<o.length;l++){var u=o[l],c=u[0];(h||n[c]!==s[c])&&(t[c]=i(t,c,n[c]||u[1]))}if((h||n.fill!==s.fill)&&(t.fillStyle=n.fill),(h||n.stroke!==s.stroke)&&(t.strokeStyle=n.stroke),(h||n.opacity!==s.opacity)&&(t.globalAlpha=null==n.opacity?1:n.opacity),(h||n.blend!==s.blend)&&(t.globalCompositeOperation=n.blend||"source-over"),this.hasStroke()){var f=n.lineWidth;t.lineWidth=f/(this.strokeNoScale&&e&&e.getLineScale?e.getLineScale():1)}},hasFill:function(){var t=this.fill;return null!=t&&"none"!==t},hasStroke:function(){var t=this.stroke;return null!=t&&"none"!==t&&this.lineWidth>0},extendFrom:function(t,e){if(t)for(var r in t)!t.hasOwnProperty(r)||!0!==e&&(!1===e?this.hasOwnProperty(r):null==t[r])||(this[r]=t[r])},set:function(t,e){"string"===typeof t?this[t]=e:this.extendFrom(t,!0)},clone:function(){var t=new this.constructor;return t.extendFrom(this,!0),t},getGradient:function(t,e,r){for(var i="radial"===e.type?l:h,n=i(t,e,r),a=e.colorStops,o=0;o<a.length;o++)n.addColorStop(a[o].offset,a[o].color);return n}};for(var u=s.prototype,c=0;c<o.length;c++){var f=o[c];f[0]in u||(u[f[0]]=f[1])}s.getGradient=u.getGradient;var d=s;t.exports=d},84845:function(t,e,r){var i=r(84093),n=r(63122),a=r(2278),o=r(82076),s=r(80023),h=s.ContextCachedBy,l=function(t){i.call(this,t)};l.prototype={constructor:l,type:"text",brush:function(t,e){var r=this.style;this.__dirty&&o.normalizeTextStyle(r,!0),r.fill=r.stroke=r.shadowBlur=r.shadowColor=r.shadowOffsetX=r.shadowOffsetY=null;var i=r.text;null!=i&&(i+=""),o.needDrawText(i,r)?(this.setTransform(t),o.renderText(this,t,i,r,null,e),this.restoreTransform(t)):t.__attrCachedBy=h.NONE},getBoundingRect:function(){var t=this.style;if(this.__dirty&&o.normalizeTextStyle(t,!0),!this._rect){var e=t.text;null!=e?e+="":e="";var r=a.getBoundingRect(t.text+"",t.font,t.textAlign,t.textVerticalAlign,t.textPadding,t.textLineHeight,t.rich);if(r.x+=t.x||0,r.y+=t.y||0,o.getStroke(t.textStroke,t.textStrokeWidth)){var i=t.textStrokeWidth;r.x-=i/2,r.y-=i/2,r.width+=i,r.height+=i}this._rect=r}return this._rect}},n.inherits(l,i);var u=l;t.exports=u},80023:function(t,e){var r={NONE:0,STYLE_BIND:1,PLAIN_TEXT:2},i=9;e.ContextCachedBy=r,e.WILL_BE_RESTORED=i},73515:function(t,e,r){var i=r(95160),n=[["shadowBlur",0],["shadowColor","#000"],["shadowOffsetX",0],["shadowOffsetY",0]];function a(t){return i.browser.ie&&i.browser.version>=11?function(){var e,r=this.__clipPaths,i=this.style;if(r)for(var a=0;a<r.length;a++){var o=r[a],s=o&&o.shape,h=o&&o.type;if(s&&("sector"===h&&s.startAngle===s.endAngle||"rect"===h&&(!s.width||!s.height))){for(var l=0;l<n.length;l++)n[l][2]=i[n[l][0]],i[n[l][0]]=n[l][1];e=!0;break}}if(t.apply(this,arguments),e)for(l=0;l<n.length;l++)i[n[l][0]]=n[l][2]}:t}t.exports=a},84108:function(t){var e={shadowBlur:1,shadowOffsetX:1,shadowOffsetY:1,textShadowBlur:1,textShadowOffsetX:1,textShadowOffsetY:1,textBoxShadowBlur:1,textBoxShadowOffsetX:1,textBoxShadowOffsetY:1};function r(t,r,i){return e.hasOwnProperty(r)?i*t.dpr:i}t.exports=r},44949:function(t,e,r){var i=r(45746),n=new i(50);function a(t){if("string"===typeof t){var e=n.get(t);return e&&e.image}return t}function o(t,e,r,i,a){if(t){if("string"===typeof t){if(e&&e.__zrImageSrc===t||!r)return e;var o=n.get(t),l={hostEl:r,cb:i,cbPayload:a};return o?(e=o.image,!h(e)&&o.pending.push(l)):(e=new Image,e.onload=e.onerror=s,n.put(t,e.__cachedImgObj={image:e,pending:[l]}),e.src=e.__zrImageSrc=t),e}return t}return e}function s(){var t=this.__cachedImgObj;this.onload=this.onerror=this.__cachedImgObj=null;for(var e=0;e<t.pending.length;e++){var r=t.pending[e],i=r.cb;i&&i(this,r.cbPayload),r.hostEl.dirty()}t.pending.length=0}function h(t){return t&&t.width&&t.height}e.findExistImage=a,e.createOrUpdateImage=o,e.isImageReady=h},22031:function(t,e,r){var i=r(45169),n=r(30028);function a(t,e,r){var a=e.points,o=e.smooth;if(a&&a.length>=2){if(o&&"spline"!==o){var s=n(a,o,r,e.smoothConstraint);t.moveTo(a[0][0],a[0][1]);for(var h=a.length,l=0;l<(r?h:h-1);l++){var u=s[2*l],c=s[2*l+1],f=a[(l+1)%h];t.bezierCurveTo(u[0],u[1],c[0],c[1],f[0],f[1])}}else{"spline"===o&&(a=i(a,r)),t.moveTo(a[0][0],a[0][1]);l=1;for(var d=a.length;l<d;l++)t.lineTo(a[l][0],a[l][1])}r&&t.closePath()}}e.buildPath=a},91611:function(t,e){function r(t,e){var r,i,n,a,o,s=e.x,h=e.y,l=e.width,u=e.height,c=e.r;l<0&&(s+=l,l=-l),u<0&&(h+=u,u=-u),"number"===typeof c?r=i=n=a=c:c instanceof Array?1===c.length?r=i=n=a=c[0]:2===c.length?(r=n=c[0],i=a=c[1]):3===c.length?(r=c[0],i=a=c[1],n=c[2]):(r=c[0],i=c[1],n=c[2],a=c[3]):r=i=n=a=0,r+i>l&&(o=r+i,r*=l/o,i*=l/o),n+a>l&&(o=n+a,n*=l/o,a*=l/o),i+n>u&&(o=i+n,i*=u/o,n*=u/o),r+a>u&&(o=r+a,r*=u/o,a*=u/o),t.moveTo(s+r,h),t.lineTo(s+l-i,h),0!==i&&t.arc(s+l-i,h+i,i,-Math.PI/2,0),t.lineTo(s+l,h+u-n),0!==n&&t.arc(s+l-n,h+u-n,n,0,Math.PI/2),t.lineTo(s+a,h+u),0!==a&&t.arc(s+a,h+u-a,a,Math.PI/2,Math.PI),t.lineTo(s,h+r),0!==r&&t.arc(s+r,h+r,r,Math.PI,1.5*Math.PI)}e.buildPath=r},30028:function(t,e,r){var i=r(78244),n=i.min,a=i.max,o=i.scale,s=i.distance,h=i.add,l=i.clone,u=i.sub;function c(t,e,r,i){var c,f,d,p,v=[],g=[],y=[],_=[];if(i){d=[1/0,1/0],p=[-1/0,-1/0];for(var m=0,x=t.length;m<x;m++)n(d,d,t[m]),a(p,p,t[m]);n(d,d,i[0]),a(p,p,i[1])}for(m=0,x=t.length;m<x;m++){var w=t[m];if(r)c=t[m?m-1:x-1],f=t[(m+1)%x];else{if(0===m||m===x-1){v.push(l(t[m]));continue}c=t[m-1],f=t[m+1]}u(g,f,c),o(g,g,e);var b=s(w,c),k=s(w,f),T=b+k;0!==T&&(b/=T,k/=T),o(y,g,-b),o(_,g,k);var S=h([],w,y),C=h([],w,_);i&&(a(S,S,d),n(S,S,p),a(C,C,d),n(C,C,p)),v.push(S),v.push(C)}return r&&v.push(v.shift()),v}t.exports=c},45169:function(t,e,r){var i=r(78244),n=i.distance;function a(t,e,r,i,n,a,o){var s=.5*(r-t),h=.5*(i-e);return(2*(e-r)+s+h)*o+(-3*(e-r)-2*s-h)*a+s*n+e}function o(t,e){for(var r=t.length,i=[],o=0,s=1;s<r;s++)o+=n(t[s-1],t[s]);var h=o/2;h=h<r?r:h;for(s=0;s<h;s++){var l,u,c,f=s/(h-1)*(e?r:r-1),d=Math.floor(f),p=f-d,v=t[d%r];e?(l=t[(d-1+r)%r],u=t[(d+1)%r],c=t[(d+2)%r]):(l=t[0===d?d:d-1],u=t[d>r-2?r-1:d+1],c=t[d>r-3?r-1:d+2]);var g=p*p,y=p*g;i.push([a(l[0],v[0],u[0],c[0],p,g,y),a(l[1],v[1],u[1],c[1],p,g,y)])}return i}t.exports=o},88645:function(t,e){var r=Math.round;function i(t,e,i){if(e){var n=e.x1,o=e.x2,s=e.y1,h=e.y2;t.x1=n,t.x2=o,t.y1=s,t.y2=h;var l=i&&i.lineWidth;l&&(r(2*n)===r(2*o)&&(t.x1=t.x2=a(n,l,!0)),r(2*s)===r(2*h)&&(t.y1=t.y2=a(s,l,!0)))}}function n(t,e,r){if(e){var i=e.x,n=e.y,o=e.width,s=e.height;t.x=i,t.y=n,t.width=o,t.height=s;var h=r&&r.lineWidth;h&&(t.x=a(i,h,!0),t.y=a(n,h,!0),t.width=Math.max(a(i+o,h,!1)-t.x,0===o?0:1),t.height=Math.max(a(n+s,h,!1)-t.y,0===s?0:1))}}function a(t,e,i){if(!e)return t;var n=r(2*t);return(n+r(e))%2===0?n/2:(n+(i?1:-1))/2}e.subPixelOptimizeLine=i,e.subPixelOptimizeRect=n,e.subPixelOptimize=a},82076:function(t,e,r){var i=r(63122),n=i.retrieve2,a=i.retrieve3,o=i.each,s=i.normalizeCssArray,h=i.isString,l=i.isObject,u=r(2278),c=r(91611),f=r(44949),d=r(84108),p=r(80023),v=p.ContextCachedBy,g=p.WILL_BE_RESTORED,y=u.DEFAULT_FONT,_={left:1,right:1,center:1},m={top:1,bottom:1,middle:1},x=[["textShadowBlur","shadowBlur",0],["textShadowOffsetX","shadowOffsetX",0],["textShadowOffsetY","shadowOffsetY",0],["textShadowColor","shadowColor","transparent"]],w={},b={};function k(t){return T(t),o(t.rich,T),t}function T(t){if(t){t.font=u.makeFont(t);var e=t.textAlign;"middle"===e&&(e="center"),t.textAlign=null==e||_[e]?e:"left";var r=t.textVerticalAlign||t.textBaseline;"center"===r&&(r="middle"),t.textVerticalAlign=null==r||m[r]?r:"top";var i=t.textPadding;i&&(t.textPadding=s(t.textPadding))}}function S(t,e,r,i,n,a){i.rich?P(t,e,r,i,n,a):C(t,e,r,i,n,a)}function C(t,e,r,i,n,a){"use strict";var o,s=D(i),h=!1,l=e.__attrCachedBy===v.PLAIN_TEXT;a!==g?(a&&(o=a.style,h=!s&&l&&o),e.__attrCachedBy=s?v.NONE:v.PLAIN_TEXT):l&&(e.__attrCachedBy=v.NONE);var c=i.font||y;h&&c===(o.font||y)||(e.font=c);var f=t.__computedFont;t.__styleFont!==c&&(t.__styleFont=c,f=t.__computedFont=e.font);var p=i.textPadding,_=i.textLineHeight,m=t.__textCotentBlock;m&&!t.__dirtyText||(m=t.__textCotentBlock=u.parsePlainText(r,f,p,_,i.truncate));var w=m.outerHeight,k=m.lines,T=m.lineHeight,S=R(b,t,i,n),C=S.baseX,P=S.baseY,A=S.textAlign||"left",L=S.textVerticalAlign;M(e,i,n,C,P);var O=u.adjustTextY(P,w,L),I=C,E=O;if(s||p){var j=u.getWidth(r,f),H=j;p&&(H+=p[1]+p[3]);var N=u.adjustTextX(C,H,A);s&&z(t,e,i,N,O,H,w),p&&(I=Z(C,A,p),E+=p[0])}e.textAlign=A,e.textBaseline="middle",e.globalAlpha=i.opacity||1;for(var W=0;W<x.length;W++){var V=x[W],q=V[0],Y=V[1],X=i[q];h&&X===o[q]||(e[Y]=d(e,Y,X||V[2]))}E+=T/2;var U=i.textStrokeWidth,$=h?o.textStrokeWidth:null,G=!h||U!==$,Q=!h||G||i.textStroke!==o.textStroke,K=F(i.textStroke,U),J=B(i.textFill);if(K&&(G&&(e.lineWidth=U),Q&&(e.strokeStyle=K)),J&&(h&&i.textFill===o.textFill||(e.fillStyle=J)),1===k.length)K&&e.strokeText(k[0],I,E),J&&e.fillText(k[0],I,E);else for(W=0;W<k.length;W++)K&&e.strokeText(k[W],I,E),J&&e.fillText(k[W],I,E),E+=T}function P(t,e,r,i,n,a){a!==g&&(e.__attrCachedBy=v.NONE);var o=t.__textCotentBlock;o&&!t.__dirtyText||(o=t.__textCotentBlock=u.parseRichText(r,i)),A(t,e,o,i,n)}function A(t,e,r,i,n){var a=r.width,o=r.outerWidth,s=r.outerHeight,h=i.textPadding,l=R(b,t,i,n),c=l.baseX,f=l.baseY,d=l.textAlign,p=l.textVerticalAlign;M(e,i,n,c,f);var v=u.adjustTextX(c,o,d),g=u.adjustTextY(f,s,p),y=v,_=g;h&&(y+=h[3],_+=h[0]);var m=y+a;D(i)&&z(t,e,i,v,g,o,s);for(var x=0;x<r.lines.length;x++){var w,k=r.lines[x],T=k.tokens,S=T.length,C=k.lineHeight,P=k.width,A=0,O=y,I=m,F=S-1;while(A<S&&(w=T[A],!w.textAlign||"left"===w.textAlign))L(t,e,w,i,C,_,O,"left"),P-=w.width,O+=w.width,A++;while(F>=0&&(w=T[F],"right"===w.textAlign))L(t,e,w,i,C,_,I,"right"),P-=w.width,I-=w.width,F--;O+=(a-(O-y)-(m-I)-P)/2;while(A<=F)w=T[A],L(t,e,w,i,C,_,O+w.width/2,"center"),O+=w.width,A++;_+=C}}function M(t,e,r,i,n){if(r&&e.textRotation){var a=e.textOrigin;"center"===a?(i=r.width/2+r.x,n=r.height/2+r.y):a&&(i=a[0]+r.x,n=a[1]+r.y),t.translate(i,n),t.rotate(-e.textRotation),t.translate(-i,-n)}}function L(t,e,r,i,o,s,h,l){var u=i.rich[r.styleName]||{};u.text=r.text;var c=r.textVerticalAlign,f=s+o/2;"top"===c?f=s+r.height/2:"bottom"===c&&(f=s+o-r.height/2),!r.isLineHolder&&D(u)&&z(t,e,u,"right"===l?h-r.width:"center"===l?h-r.width/2:h,f-r.height/2,r.width,r.height);var d=r.textPadding;d&&(h=Z(h,l,d),f-=r.height/2-d[2]-r.textHeight/2),I(e,"shadowBlur",a(u.textShadowBlur,i.textShadowBlur,0)),I(e,"shadowColor",u.textShadowColor||i.textShadowColor||"transparent"),I(e,"shadowOffsetX",a(u.textShadowOffsetX,i.textShadowOffsetX,0)),I(e,"shadowOffsetY",a(u.textShadowOffsetY,i.textShadowOffsetY,0)),I(e,"textAlign",l),I(e,"textBaseline","middle"),I(e,"font",r.font||y);var p=F(u.textStroke||i.textStroke,g),v=B(u.textFill||i.textFill),g=n(u.textStrokeWidth,i.textStrokeWidth);p&&(I(e,"lineWidth",g),I(e,"strokeStyle",p),e.strokeText(r.text,h,f)),v&&(I(e,"fillStyle",v),e.fillText(r.text,h,f))}function D(t){return!!(t.textBackgroundColor||t.textBorderWidth&&t.textBorderColor)}function z(t,e,r,i,n,a,o){var s=r.textBackgroundColor,u=r.textBorderWidth,d=r.textBorderColor,p=h(s);if(I(e,"shadowBlur",r.textBoxShadowBlur||0),I(e,"shadowColor",r.textBoxShadowColor||"transparent"),I(e,"shadowOffsetX",r.textBoxShadowOffsetX||0),I(e,"shadowOffsetY",r.textBoxShadowOffsetY||0),p||u&&d){e.beginPath();var v=r.textBorderRadius;v?c.buildPath(e,{x:i,y:n,width:a,height:o,r:v}):e.rect(i,n,a,o),e.closePath()}if(p)if(I(e,"fillStyle",s),null!=r.fillOpacity){var g=e.globalAlpha;e.globalAlpha=r.fillOpacity*r.opacity,e.fill(),e.globalAlpha=g}else e.fill();else if(l(s)){var y=s.image;y=f.createOrUpdateImage(y,null,t,O,s),y&&f.isImageReady(y)&&e.drawImage(y,i,n,a,o)}if(u&&d)if(I(e,"lineWidth",u),I(e,"strokeStyle",d),null!=r.strokeOpacity){g=e.globalAlpha;e.globalAlpha=r.strokeOpacity*r.opacity,e.stroke(),e.globalAlpha=g}else e.stroke()}function O(t,e){e.image=t}function R(t,e,r,i){var n=r.x||0,a=r.y||0,o=r.textAlign,s=r.textVerticalAlign;if(i){var h=r.textPosition;if(h instanceof Array)n=i.x+E(h[0],i.width),a=i.y+E(h[1],i.height);else{var l=e&&e.calculateTextPosition?e.calculateTextPosition(w,r,i):u.calculateTextPosition(w,r,i);n=l.x,a=l.y,o=o||l.textAlign,s=s||l.textVerticalAlign}var c=r.textOffset;c&&(n+=c[0],a+=c[1])}return t=t||{},t.baseX=n,t.baseY=a,t.textAlign=o,t.textVerticalAlign=s,t}function I(t,e,r){return t[e]=d(t,e,r),t[e]}function F(t,e){return null==t||e<=0||"transparent"===t||"none"===t?null:t.image||t.colorStops?"#000":t}function B(t){return null==t||"none"===t?null:t.image||t.colorStops?"#000":t}function E(t,e){return"string"===typeof t?t.lastIndexOf("%")>=0?parseFloat(t)/100*e:parseFloat(t):t}function Z(t,e,r){return"right"===e?t-r[1]:"center"===e?t+r[3]/2-r[1]/2:t+r[3]}function j(t,e){return null!=t&&(t||e.textBackgroundColor||e.textBorderWidth&&e.textBorderColor||e.textPadding)}e.normalizeTextStyle=k,e.renderText=S,e.getBoxPosition=R,e.getStroke=F,e.getFill=B,e.parsePercent=E,e.needDrawText=j},73637:function(t,e,r){var i=r(82076),n=r(46630),a=r(80023),o=a.WILL_BE_RESTORED,s=new n,h=function(){};h.prototype={constructor:h,drawRectText:function(t,e){var r=this.style;e=r.textRect||e,this.__dirty&&i.normalizeTextStyle(r,!0);var n=r.text;if(null!=n&&(n+=""),i.needDrawText(n,r)){t.save();var a=this.transform;r.transformText?this.setTransform(t):a&&(s.copy(e),s.applyTransform(a),e=s),i.renderText(this,t,n,r,e,o),t.restore()}}};var l=h;t.exports=l},46941:function(t,e,r){var i=r(73477),n=i.extend({type:"arc",shape:{cx:0,cy:0,r:0,startAngle:0,endAngle:2*Math.PI,clockwise:!0},style:{stroke:"#000",fill:null},buildPath:function(t,e){var r=e.cx,i=e.cy,n=Math.max(e.r,0),a=e.startAngle,o=e.endAngle,s=e.clockwise,h=Math.cos(a),l=Math.sin(a);t.moveTo(h*n+r,l*n+i),t.arc(r,i,n,a,o,!s)}});t.exports=n},73030:function(t,e,r){var i=r(73477),n=r(78244),a=r(85723),o=a.quadraticSubdivide,s=a.cubicSubdivide,h=a.quadraticAt,l=a.cubicAt,u=a.quadraticDerivativeAt,c=a.cubicDerivativeAt,f=[];function d(t,e,r){var i=t.cpx2,n=t.cpy2;return null===i||null===n?[(r?c:l)(t.x1,t.cpx1,t.cpx2,t.x2,e),(r?c:l)(t.y1,t.cpy1,t.cpy2,t.y2,e)]:[(r?u:h)(t.x1,t.cpx1,t.x2,e),(r?u:h)(t.y1,t.cpy1,t.y2,e)]}var p=i.extend({type:"bezier-curve",shape:{x1:0,y1:0,x2:0,y2:0,cpx1:0,cpy1:0,percent:1},style:{stroke:"#000",fill:null},buildPath:function(t,e){var r=e.x1,i=e.y1,n=e.x2,a=e.y2,h=e.cpx1,l=e.cpy1,u=e.cpx2,c=e.cpy2,d=e.percent;0!==d&&(t.moveTo(r,i),null==u||null==c?(d<1&&(o(r,h,n,d,f),h=f[1],n=f[2],o(i,l,a,d,f),l=f[1],a=f[2]),t.quadraticCurveTo(h,l,n,a)):(d<1&&(s(r,h,u,n,d,f),h=f[1],u=f[2],n=f[3],s(i,l,c,a,d,f),l=f[1],c=f[2],a=f[3]),t.bezierCurveTo(h,l,u,c,n,a)))},pointAt:function(t){return d(this.shape,t,!1)},tangentAt:function(t){var e=d(this.shape,t,!0);return n.normalize(e,e)}});t.exports=p},96005:function(t,e,r){var i=r(73477),n=i.extend({type:"circle",shape:{cx:0,cy:0,r:0},buildPath:function(t,e,r){r&&t.moveTo(e.cx+e.r,e.cy),t.arc(e.cx,e.cy,e.r,0,2*Math.PI,!0)}});t.exports=n},85294:function(t,e,r){var i=r(73477),n=i.extend({type:"ellipse",shape:{cx:0,cy:0,rx:0,ry:0},buildPath:function(t,e){var r=.5522848,i=e.cx,n=e.cy,a=e.rx,o=e.ry,s=a*r,h=o*r;t.moveTo(i-a,n),t.bezierCurveTo(i-a,n-h,i-s,n-o,i,n-o),t.bezierCurveTo(i+s,n-o,i+a,n-h,i+a,n),t.bezierCurveTo(i+a,n+h,i+s,n+o,i,n+o),t.bezierCurveTo(i-s,n+o,i-a,n+h,i-a,n),t.closePath()}});t.exports=n},22923:function(t,e,r){var i=r(73477),n=r(88645),a=n.subPixelOptimizeLine,o={},s=i.extend({type:"line",shape:{x1:0,y1:0,x2:0,y2:0,percent:1},style:{stroke:"#000",fill:null},buildPath:function(t,e){var r,i,n,s;this.subPixelOptimize?(a(o,e,this.style),r=o.x1,i=o.y1,n=o.x2,s=o.y2):(r=e.x1,i=e.y1,n=e.x2,s=e.y2);var h=e.percent;0!==h&&(t.moveTo(r,i),h<1&&(n=r*(1-h)+n*h,s=i*(1-h)+s*h),t.lineTo(n,s))},pointAt:function(t){var e=this.shape;return[e.x1*(1-t)+e.x2*t,e.y1*(1-t)+e.y2*t]}});t.exports=s},83286:function(t,e,r){var i=r(73477),n=r(22031),a=i.extend({type:"polygon",shape:{points:null,smooth:!1,smoothConstraint:null},buildPath:function(t,e){n.buildPath(t,e,!0)}});t.exports=a},23074:function(t,e,r){var i=r(73477),n=r(22031),a=i.extend({type:"polyline",shape:{points:null,smooth:!1,smoothConstraint:null},style:{stroke:"#000",fill:null},buildPath:function(t,e){n.buildPath(t,e,!1)}});t.exports=a},58195:function(t,e,r){var i=r(73477),n=r(91611),a=r(88645),o=a.subPixelOptimizeRect,s={},h=i.extend({type:"rect",shape:{r:0,x:0,y:0,width:0,height:0},buildPath:function(t,e){var r,i,a,h;this.subPixelOptimize?(o(s,e,this.style),r=s.x,i=s.y,a=s.width,h=s.height,s.r=e.r,e=s):(r=e.x,i=e.y,a=e.width,h=e.height),e.r?n.buildPath(t,e):t.rect(r,i,a,h),t.closePath()}});t.exports=h},66405:function(t,e,r){var i=r(73477),n=i.extend({type:"ring",shape:{cx:0,cy:0,r:0,r0:0},buildPath:function(t,e){var r=e.cx,i=e.cy,n=2*Math.PI;t.moveTo(r+e.r,i),t.arc(r,i,e.r,0,n,!1),t.moveTo(r+e.r0,i),t.arc(r,i,e.r0,0,n,!0)}});t.exports=n},71812:function(t,e,r){var i=r(73477),n=r(73515),a=i.extend({type:"sector",shape:{cx:0,cy:0,r0:0,r:0,startAngle:0,endAngle:2*Math.PI,clockwise:!0},brush:n(i.prototype.brush),buildPath:function(t,e){var r=e.cx,i=e.cy,n=Math.max(e.r0||0,0),a=Math.max(e.r,0),o=e.startAngle,s=e.endAngle,h=e.clockwise,l=Math.cos(o),u=Math.sin(o);t.moveTo(l*n+r,u*n+i),t.lineTo(l*a+r,u*a+i),t.arc(r,i,a,o,s,!h),t.lineTo(Math.cos(s)*n+r,Math.sin(s)*n+i),0!==n&&t.arc(r,i,n,s,o,h),t.closePath()}});t.exports=a},4845:function(t,e,r){var i=r(40925),n=r(70083),a=r(63122),o=a.isString,s=a.isFunction,h=a.isObject,l=a.isArrayLike,u=a.indexOf,c=function(){this.animators=[]};function f(t,e,r,i,n,a,h,l){o(i)?(a=n,n=i,i=0):s(n)?(a=n,n="linear",i=0):s(i)?(a=i,i=0):s(r)?(a=r,r=500):r||(r=500),t.stopAnimation(),d(t,"",t,e,r,i,l);var u=t.animators.slice(),c=u.length;function f(){c--,c||a&&a()}c||a&&a();for(var p=0;p<u.length;p++)u[p].done(f).start(n,h)}function d(t,e,r,i,n,a,o){var s={},u=0;for(var c in i)i.hasOwnProperty(c)&&(null!=r[c]?h(i[c])&&!l(i[c])?d(t,e?e+"."+c:c,r[c],i[c],n,a,o):(o?(s[c]=r[c],p(t,e,c,i[c])):s[c]=i[c],u++):null==i[c]||o||p(t,e,c,i[c]));u>0&&t.animate(e,!1).when(null==n?500:n,s).delay(a||0)}function p(t,e,r,i){if(e){var n={};n[e]={},n[e][r]=i,t.attr(n)}else t.attr(r,i)}c.prototype={constructor:c,animate:function(t,e){var r,a=!1,o=this,s=this.__zr;if(t){var h=t.split("."),l=o;a="shape"===h[0];for(var c=0,f=h.length;c<f;c++)l&&(l=l[h[c]]);l&&(r=l)}else r=o;if(r){var d=o.animators,p=new i(r,e);return p.during((function(t){o.dirty(a)})).done((function(){d.splice(u(d,p),1)})),d.push(p),s&&s.animation.addAnimator(p),p}n('Property "'+t+'" is not existed in element '+o.id)},stopAnimation:function(t){for(var e=this.animators,r=e.length,i=0;i<r;i++)e[i].stop(t);return e.length=0,this},animateTo:function(t,e,r,i,n,a){f(this,t,e,r,i,n,a)},animateFrom:function(t,e,r,i,n,a){f(this,t,e,r,i,n,a,!0)}};var v=c;t.exports=v},78035:function(t){function e(){this.on("mousedown",this._dragStart,this),this.on("mousemove",this._drag,this),this.on("mouseup",this._dragEnd,this)}function r(t,e){return{target:t,topTarget:e&&e.topTarget}}e.prototype={constructor:e,_dragStart:function(t){var e=t.target;while(e&&!e.draggable)e=e.parent;e&&(this._draggingTarget=e,e.dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.dispatchToElement(r(e,t),"dragstart",t.event))},_drag:function(t){var e=this._draggingTarget;if(e){var i=t.offsetX,n=t.offsetY,a=i-this._x,o=n-this._y;this._x=i,this._y=n,e.drift(a,o,t),this.dispatchToElement(r(e,t),"drag",t.event);var s=this.findHover(i,n,e).target,h=this._dropTarget;this._dropTarget=s,e!==s&&(h&&s!==h&&this.dispatchToElement(r(h,t),"dragleave",t.event),s&&s!==h&&this.dispatchToElement(r(s,t),"dragenter",t.event))}},_dragEnd:function(t){var e=this._draggingTarget;e&&(e.dragging=!1),this.dispatchToElement(r(e,t),"dragend",t.event),this._dropTarget&&this.dispatchToElement(r(this._dropTarget,t),"drop",t.event),this._draggingTarget=null,this._dropTarget=null}};var i=e;t.exports=i},65836:function(t){var e=Array.prototype.slice,r=function(t){this._$handlers={},this._$eventProcessor=t};function i(t,e){var r=t._$eventProcessor;return null!=e&&r&&r.normalizeQuery&&(e=r.normalizeQuery(e)),e}function n(t,e,r,n,a,o){var s=t._$handlers;if("function"===typeof r&&(a=n,n=r,r=null),!n||!e)return t;r=i(t,r),s[e]||(s[e]=[]);for(var h=0;h<s[e].length;h++)if(s[e][h].h===n)return t;var l={h:n,one:o,query:r,ctx:a||t,callAtLast:n.zrEventfulCallAtLast},u=s[e].length-1,c=s[e][u];return c&&c.callAtLast?s[e].splice(u,0,l):s[e].push(l),t}r.prototype={constructor:r,one:function(t,e,r,i){return n(this,t,e,r,i,!0)},on:function(t,e,r,i){return n(this,t,e,r,i,!1)},isSilent:function(t){var e=this._$handlers;return!e[t]||!e[t].length},off:function(t,e){var r=this._$handlers;if(!t)return this._$handlers={},this;if(e){if(r[t]){for(var i=[],n=0,a=r[t].length;n<a;n++)r[t][n].h!==e&&i.push(r[t][n]);r[t]=i}r[t]&&0===r[t].length&&delete r[t]}else delete r[t];return this},trigger:function(t){var r=this._$handlers[t],i=this._$eventProcessor;if(r){var n=arguments,a=n.length;a>3&&(n=e.call(n,1));for(var o=r.length,s=0;s<o;){var h=r[s];if(i&&i.filter&&null!=h.query&&!i.filter(t,h.query))s++;else{switch(a){case 1:h.h.call(h.ctx);break;case 2:h.h.call(h.ctx,n[1]);break;case 3:h.h.call(h.ctx,n[1],n[2]);break;default:h.h.apply(h.ctx,n);break}h.one?(r.splice(s,1),o--):s++}}}return i&&i.afterTrigger&&i.afterTrigger(t),this},triggerWithContext:function(t){var r=this._$handlers[t],i=this._$eventProcessor;if(r){var n=arguments,a=n.length;a>4&&(n=e.call(n,1,n.length-1));for(var o=n[n.length-1],s=r.length,h=0;h<s;){var l=r[h];if(i&&i.filter&&null!=l.query&&!i.filter(t,l.query))h++;else{switch(a){case 1:l.h.call(o);break;case 2:l.h.call(o,n[1]);break;case 3:l.h.call(o,n[1],n[2]);break;default:l.h.apply(o,n);break}l.one?(r.splice(h,1),s--):h++}}}return i&&i.afterTrigger&&i.afterTrigger(t),this}};var a=r;t.exports=a},71082:function(t,e,r){var i=r(97333),n=r(78244),a=i.identity,o=5e-5;function s(t){return t>o||t<-o}var h=function(t){t=t||{},t.position||(this.position=[0,0]),null==t.rotation&&(this.rotation=0),t.scale||(this.scale=[1,1]),this.origin=this.origin||null},l=h.prototype;l.transform=null,l.needLocalTransform=function(){return s(this.rotation)||s(this.position[0])||s(this.position[1])||s(this.scale[0]-1)||s(this.scale[1]-1)};var u=[];l.updateTransform=function(){var t=this.parent,e=t&&t.transform,r=this.needLocalTransform(),n=this.transform;if(r||e){n=n||i.create(),r?this.getLocalTransform(n):a(n),e&&(r?i.mul(n,t.transform,n):i.copy(n,t.transform)),this.transform=n;var o=this.globalScaleRatio;if(null!=o&&1!==o){this.getGlobalScale(u);var s=u[0]<0?-1:1,h=u[1]<0?-1:1,l=((u[0]-s)*o+s)/u[0]||0,c=((u[1]-h)*o+h)/u[1]||0;n[0]*=l,n[1]*=l,n[2]*=c,n[3]*=c}this.invTransform=this.invTransform||i.create(),i.invert(this.invTransform,n)}else n&&a(n)},l.getLocalTransform=function(t){return h.getLocalTransform(this,t)},l.setTransform=function(t){var e=this.transform,r=t.dpr||1;e?t.setTransform(r*e[0],r*e[1],r*e[2],r*e[3],r*e[4],r*e[5]):t.setTransform(r,0,0,r,0,0)},l.restoreTransform=function(t){var e=t.dpr||1;t.setTransform(e,0,0,e,0,0)};var c=[],f=i.create();l.setLocalTransform=function(t){if(t){var e=t[0]*t[0]+t[1]*t[1],r=t[2]*t[2]+t[3]*t[3],i=this.position,n=this.scale;s(e-1)&&(e=Math.sqrt(e)),s(r-1)&&(r=Math.sqrt(r)),t[0]<0&&(e=-e),t[3]<0&&(r=-r),i[0]=t[4],i[1]=t[5],n[0]=e,n[1]=r,this.rotation=Math.atan2(-t[1]/r,t[0]/e)}},l.decomposeTransform=function(){if(this.transform){var t=this.parent,e=this.transform;t&&t.transform&&(i.mul(c,t.invTransform,e),e=c);var r=this.origin;r&&(r[0]||r[1])&&(f[4]=r[0],f[5]=r[1],i.mul(c,e,f),c[4]-=r[0],c[5]-=r[1],e=c),this.setLocalTransform(e)}},l.getGlobalScale=function(t){var e=this.transform;return t=t||[],e?(t[0]=Math.sqrt(e[0]*e[0]+e[1]*e[1]),t[1]=Math.sqrt(e[2]*e[2]+e[3]*e[3]),e[0]<0&&(t[0]=-t[0]),e[3]<0&&(t[1]=-t[1]),t):(t[0]=1,t[1]=1,t)},l.transformCoordToLocal=function(t,e){var r=[t,e],i=this.invTransform;return i&&n.applyTransform(r,r,i),r},l.transformCoordToGlobal=function(t,e){var r=[t,e],i=this.transform;return i&&n.applyTransform(r,r,i),r},h.getLocalTransform=function(t,e){e=e||[],a(e);var r=t.origin,n=t.scale||[1,1],o=t.rotation||0,s=t.position||[0,0];return r&&(e[4]-=r[0],e[5]-=r[1]),i.scale(e,e,n),o&&i.rotate(e,e,o),r&&(e[4]+=r[0],e[5]+=r[1]),e[4]+=s[0],e[5]+=s[1],e};var d=h;t.exports=d},23372:function(t,e,r){var i=r(18895),n=i.createElement,a=r(63122),o=r(70083),s=r(73477),h=r(45474),l=r(84845),u=r(62732),c=r(34881),f=r(82685),d=r(1465),p=r(97278),v=p.path,g=p.image,y=p.text;function _(t){return parseInt(t,10)}function m(t){return t instanceof s?v:t instanceof h?g:t instanceof l?y:v}function x(t,e){return e&&t&&e.parentNode!==t}function w(t,e,r){if(x(t,e)&&r){var i=r.nextSibling;i?t.insertBefore(e,i):t.appendChild(e)}}function b(t,e){if(x(t,e)){var r=t.firstChild;r?t.insertBefore(e,r):t.appendChild(e)}}function k(t,e){e&&t&&e.parentNode===t&&t.removeChild(e)}function T(t){return t.__textSvgEl}function S(t){return t.__svgEl}var C=function(t,e,r,i){this.root=t,this.storage=e,this._opts=r=a.extend({},r||{});var o=n("svg");o.setAttribute("xmlns","http://www.w3.org/2000/svg"),o.setAttribute("version","1.1"),o.setAttribute("baseProfile","full"),o.style.cssText="user-select:none;position:absolute;left:0;top:0;";var s=n("g");o.appendChild(s);var h=n("g");o.appendChild(h),this.gradientManager=new c(i,h),this.clipPathManager=new f(i,h),this.shadowManager=new d(i,h);var l=document.createElement("div");l.style.cssText="overflow:hidden;position:relative",this._svgDom=o,this._svgRoot=h,this._backgroundRoot=s,this._viewport=l,t.appendChild(l),l.appendChild(o),this.resize(r.width,r.height),this._visibleList=[]};function P(t){return function(){o('In SVG mode painter not support method "'+t+'"')}}C.prototype={constructor:C,getType:function(){return"svg"},getViewportRoot:function(){return this._viewport},getSvgDom:function(){return this._svgDom},getSvgRoot:function(){return this._svgRoot},getViewportRootOffset:function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},refresh:function(){var t=this.storage.getDisplayList(!0);this._paintList(t)},setBackgroundColor:function(t){this._backgroundRoot&&this._backgroundNode&&this._backgroundRoot.removeChild(this._backgroundNode);var e=n("rect");e.setAttribute("width",this.getWidth()),e.setAttribute("height",this.getHeight()),e.setAttribute("x",0),e.setAttribute("y",0),e.setAttribute("id",0),e.style.fill=t,this._backgroundRoot.appendChild(e),this._backgroundNode=e},_paintList:function(t){this.gradientManager.markAllUnused(),this.clipPathManager.markAllUnused(),this.shadowManager.markAllUnused();var e,r=this._svgRoot,i=this._visibleList,n=t.length,a=[];for(e=0;e<n;e++){var o=t[e],s=m(o),h=S(o)||T(o);o.invisible||(o.__dirty&&(s&&s.brush(o),this.clipPathManager.update(o),o.style&&(this.gradientManager.update(o.style.fill),this.gradientManager.update(o.style.stroke),this.shadowManager.update(h,o)),o.__dirty=!1),a.push(o))}var l,c=u(i,a);for(e=0;e<c.length;e++){var f=c[e];if(f.removed)for(var d=0;d<f.count;d++){o=i[f.indices[d]],h=S(o);var p=T(o);k(r,h),k(r,p)}}for(e=0;e<c.length;e++){f=c[e];if(f.added)for(d=0;d<f.count;d++){o=a[f.indices[d]],h=S(o),p=T(o);l?w(r,h,l):b(r,h),h?w(r,p,h):l?w(r,p,l):b(r,p),w(r,p,h),l=p||h||l,this.gradientManager.addWithoutUpdate(h||p,o),this.shadowManager.addWithoutUpdate(h||p,o),this.clipPathManager.markUsed(o)}else if(!f.removed)for(d=0;d<f.count;d++){o=a[f.indices[d]],h=S(o),p=T(o),h=S(o),p=T(o);this.gradientManager.markUsed(o),this.gradientManager.addWithoutUpdate(h||p,o),this.shadowManager.markUsed(o),this.shadowManager.addWithoutUpdate(h||p,o),this.clipPathManager.markUsed(o),p&&w(r,p,h),l=h||p||l}}this.gradientManager.removeUnused(),this.clipPathManager.removeUnused(),this.shadowManager.removeUnused(),this._visibleList=a},_getDefs:function(t){var e=this._svgDom,r=e.getElementsByTagName("defs");if(0===r.length){if(t){r=e.insertBefore(n("defs"),e.firstChild);return r.contains||(r.contains=function(t){var e=r.children;if(!e)return!1;for(var i=e.length-1;i>=0;--i)if(e[i]===t)return!0;return!1}),r}return null}return r[0]},resize:function(t,e){var r=this._viewport;r.style.display="none";var i=this._opts;if(null!=t&&(i.width=t),null!=e&&(i.height=e),t=this._getSize(0),e=this._getSize(1),r.style.display="",this._width!==t||this._height!==e){this._width=t,this._height=e;var n=r.style;n.width=t+"px",n.height=e+"px";var a=this._svgDom;a.setAttribute("width",t),a.setAttribute("height",e)}this._backgroundNode&&(this._backgroundNode.setAttribute("width",t),this._backgroundNode.setAttribute("height",e))},getWidth:function(){return this._width},getHeight:function(){return this._height},_getSize:function(t){var e=this._opts,r=["width","height"][t],i=["clientWidth","clientHeight"][t],n=["paddingLeft","paddingTop"][t],a=["paddingRight","paddingBottom"][t];if(null!=e[r]&&"auto"!==e[r])return parseFloat(e[r]);var o=this.root,s=document.defaultView.getComputedStyle(o);return(o[i]||_(s[r])||_(o.style[r]))-(_(s[n])||0)-(_(s[a])||0)|0},dispose:function(){this.root.innerHTML="",this._svgRoot=this._backgroundRoot=this._svgDom=this._backgroundNode=this._viewport=this.storage=null},clear:function(){this._viewport&&this.root.removeChild(this._viewport)},toDataURL:function(){this.refresh();var t=encodeURIComponent(this._svgDom.outerHTML.replace(/></g,">\n\r<"));return"data:image/svg+xml;charset=UTF-8,"+t}},a.each(["getLayer","insertLayer","eachLayer","eachBuiltinLayer","eachOtherLayer","getLayers","modLayer","delLayer","clearLayer","pathToImage"],(function(t){C.prototype[t]=P(t)}));var A=C;t.exports=A},18895:function(t,e){var r="http://www.w3.org/2000/svg";function i(t){return document.createElementNS(r,t)}e.createElement=i},97278:function(t,e,r){var i=r(18895),n=i.createElement,a=r(11348),o=r(46630),s=r(97333),h=r(2278),l=r(82076),u=r(84845),c=a.CMD,f=Array.prototype.join,d="none",p=Math.round,v=Math.sin,g=Math.cos,y=Math.PI,_=2*Math.PI,m=180/y,x=1e-4;function w(t){return p(1e4*t)/1e4}function b(t){return t<x&&t>-x}function k(t,e){var r=e?t.textFill:t.fill;return null!=r&&r!==d}function T(t,e){var r=e?t.textStroke:t.stroke;return null!=r&&r!==d}function S(t,e){e&&C(t,"transform","matrix("+f.call(e,",")+")")}function C(t,e,r){(!r||"linear"!==r.type&&"radial"!==r.type)&&t.setAttribute(e,r)}function P(t,e,r){t.setAttributeNS("http://www.w3.org/1999/xlink",e,r)}function A(t,e,r,i){if(k(e,r)){var n=r?e.textFill:e.fill;n="transparent"===n?d:n,C(t,"fill",n),C(t,"fill-opacity",null!=e.fillOpacity?e.fillOpacity*e.opacity:e.opacity)}else C(t,"fill",d);if(T(e,r)){var a=r?e.textStroke:e.stroke;a="transparent"===a?d:a,C(t,"stroke",a);var o=r?e.textStrokeWidth:e.lineWidth,s=!r&&e.strokeNoScale?i.getLineScale():1;C(t,"stroke-width",o/s),C(t,"paint-order",r?"stroke":"fill"),C(t,"stroke-opacity",null!=e.strokeOpacity?e.strokeOpacity:e.opacity);var h=e.lineDash;h?(C(t,"stroke-dasharray",e.lineDash.join(",")),C(t,"stroke-dashoffset",p(e.lineDashOffset||0))):C(t,"stroke-dasharray",""),e.lineCap&&C(t,"stroke-linecap",e.lineCap),e.lineJoin&&C(t,"stroke-linejoin",e.lineJoin),e.miterLimit&&C(t,"stroke-miterlimit",e.miterLimit)}else C(t,"stroke",d)}function M(t){for(var e=[],r=t.data,i=t.len(),n=0;n<i;){var a=r[n++],o="",s=0;switch(a){case c.M:o="M",s=2;break;case c.L:o="L",s=2;break;case c.Q:o="Q",s=4;break;case c.C:o="C",s=6;break;case c.A:var h=r[n++],l=r[n++],u=r[n++],f=r[n++],d=r[n++],x=r[n++],k=r[n++],T=r[n++],S=Math.abs(x),C=b(S-_)||(T?x>=_:-x>=_),P=x>0?x%_:x%_+_,A=!1;A=!!C||!b(S)&&P>=y===!!T;var M=w(h+u*g(d)),L=w(l+f*v(d));C&&(x=T?_-1e-4:1e-4-_,A=!0,9===n&&e.push("M",M,L));var D=w(h+u*g(d+x)),z=w(l+f*v(d+x));e.push("A",w(u),w(f),p(k*m),+A,+T,D,z);break;case c.Z:o="Z";break;case c.R:D=w(r[n++]),z=w(r[n++]);var O=w(r[n++]),R=w(r[n++]);e.push("M",D,z,"L",D+O,z,"L",D+O,z+R,"L",D,z+R,"L",D,z);break}o&&e.push(o);for(var I=0;I<s;I++)e.push(w(r[n++]))}return e.join(" ")}var L={brush:function(t){var e=t.style,r=t.__svgEl;r||(r=n("path"),t.__svgEl=r),t.path||t.createPathProxy();var i=t.path;if(t.__dirtyPath){i.beginPath(),i.subPixelOptimize=!1,t.buildPath(i,t.shape),t.__dirtyPath=!1;var a=M(i);a.indexOf("NaN")<0&&C(r,"d",a)}A(r,e,!1,t),S(r,t.transform),null!=e.text?B(t,t.getBoundingRect()):H(t)}},D={brush:function(t){var e=t.style,r=e.image;if(r instanceof HTMLImageElement){var i=r.src;r=i}if(r){var a=e.x||0,o=e.y||0,s=e.width,h=e.height,l=t.__svgEl;l||(l=n("image"),t.__svgEl=l),r!==t.__imageSrc&&(P(l,"href",r),t.__imageSrc=r),C(l,"width",s),C(l,"height",h),C(l,"x",a),C(l,"y",o),S(l,t.transform),null!=e.text?B(t,t.getBoundingRect()):H(t)}}},z={},O=new o,R={},I=[],F={left:"start",right:"end",center:"middle",middle:"middle"},B=function(t,e){var r=t.style,i=t.transform,a=t instanceof u||r.transformText;t.__dirty&&l.normalizeTextStyle(r,!0);var o=r.text;if(null!=o&&(o+=""),l.needDrawText(o,r)){null==o&&(o=""),!a&&i&&(O.copy(e),O.applyTransform(i),e=O);var s=t.__textSvgEl;s||(s=n("text"),t.__textSvgEl=s);var c=s.style,f=r.font||h.DEFAULT_FONT,d=s.__computedFont;f!==s.__styleFont&&(c.font=s.__styleFont=f,d=s.__computedFont=c.font);var p=r.textPadding,v=r.textLineHeight,g=t.__textCotentBlock;g&&!t.__dirtyText||(g=t.__textCotentBlock=h.parsePlainText(o,d,p,v,r.truncate));var y=g.outerHeight,_=g.lineHeight;l.getBoxPosition(R,t,r,e);var m=R.baseX,x=R.baseY,w=R.textAlign||"left",b=R.textVerticalAlign;E(s,a,i,r,e,m,x);var k=h.adjustTextY(x,y,b),T=m,S=k;p&&(T=Z(m,w,p),S+=p[0]),S+=_/2,A(s,r,!0,t);var C=g.canCacheByTextString,P=t.__tspanList||(t.__tspanList=[]),M=P.length;if(C&&t.__canCacheByTextString&&t.__text===o){if(t.__dirtyText&&M)for(var L=0;L<M;++L)j(P[L],w,T,S+L*_)}else{t.__text=o,t.__canCacheByTextString=C;var D=g.lines,z=D.length;for(L=0;L<z;L++){var I=P[L],F=D[L];I?I.__zrText!==F&&(I.innerHTML="",I.appendChild(document.createTextNode(F))):(I=P[L]=n("tspan"),s.appendChild(I),I.appendChild(document.createTextNode(F))),j(I,w,T,S+L*_)}if(M>z){for(;L<M;L++)s.removeChild(P[L]);P.length=z}}}};function E(t,e,r,i,n,a,o){s.identity(I),e&&r&&s.copy(I,r);var h=i.textRotation;if(n&&h){var l=i.textOrigin;"center"===l?(a=n.width/2+n.x,o=n.height/2+n.y):l&&(a=l[0]+n.x,o=l[1]+n.y),I[4]-=a,I[5]-=o,s.rotate(I,I,h),I[4]+=a,I[5]+=o}S(t,I)}function Z(t,e,r){return"right"===e?t-r[1]:"center"===e?t+r[3]/2-r[1]/2:t+r[3]}function j(t,e,r,i){C(t,"dominant-baseline","middle"),C(t,"text-anchor",F[e]),C(t,"x",r),C(t,"y",i)}function H(t){t&&t.__textSvgEl&&(t.__textSvgEl.parentNode&&t.__textSvgEl.parentNode.removeChild(t.__textSvgEl),t.__textSvgEl=null,t.__tspanList=[],t.__text=null)}z.drawRectText=B,z.brush=function(t){var e=t.style;null!=e.text?B(t,!1):H(t)},e.path=L,e.image=D,e.text=z},82685:function(t,e,r){var i=r(19184),n=r(63122),a=r(97333);function o(t,e){i.call(this,t,e,"clipPath","__clippath_in_use__")}n.inherits(o,i),o.prototype.update=function(t){var e=this.getSvgElement(t);e&&this.updateDom(e,t.__clipPaths,!1);var r=this.getTextSvgElement(t);r&&this.updateDom(r,t.__clipPaths,!0),this.markUsed(t)},o.prototype.updateDom=function(t,e,r){if(e&&e.length>0){var i,n,o=this.getDefs(!0),s=e[0],h=r?"_textDom":"_dom";s[h]?(n=s[h].getAttribute("id"),i=s[h],o.contains(i)||o.appendChild(i)):(n="zr"+this._zrId+"-clip-"+this.nextId,++this.nextId,i=this.createElement("clipPath"),i.setAttribute("id",n),o.appendChild(i),s[h]=i);var l=this.getSvgProxy(s);if(s.transform&&s.parent.invTransform&&!r){var u=Array.prototype.slice.call(s.transform);a.mul(s.transform,s.parent.invTransform,s.transform),l.brush(s),s.transform=u}else l.brush(s);var c=this.getSvgElement(s);i.innerHTML="",i.appendChild(c.cloneNode()),t.setAttribute("clip-path","url(#"+n+")"),e.length>1&&this.updateDom(i,e.slice(1),r)}else t&&t.setAttribute("clip-path","none")},o.prototype.markUsed=function(t){var e=this;t.__clipPaths&&n.each(t.__clipPaths,(function(t){t._dom&&i.prototype.markUsed.call(e,t._dom),t._textDom&&i.prototype.markUsed.call(e,t._textDom)}))};var s=o;t.exports=s},19184:function(t,e,r){var i=r(18895),n=i.createElement,a=r(63122),o=r(73477),s=r(45474),h=r(84845),l=r(97278),u=l.path,c=l.image,f=l.text,d="0",p="1";function v(t,e,r,i,n){this._zrId=t,this._svgRoot=e,this._tagNames="string"===typeof r?[r]:r,this._markLabel=i,this._domName=n||"_dom",this.nextId=0}v.prototype.createElement=n,v.prototype.getDefs=function(t){var e=this._svgRoot,r=this._svgRoot.getElementsByTagName("defs");return 0===r.length?t?(r=e.insertBefore(this.createElement("defs"),e.firstChild),r.contains||(r.contains=function(t){var e=r.children;if(!e)return!1;for(var i=e.length-1;i>=0;--i)if(e[i]===t)return!0;return!1}),r):null:r[0]},v.prototype.update=function(t,e){if(t){var r=this.getDefs(!1);if(t[this._domName]&&r.contains(t[this._domName]))"function"===typeof e&&e(t);else{var i=this.add(t);i&&(t[this._domName]=i)}}},v.prototype.addDom=function(t){var e=this.getDefs(!0);e.appendChild(t)},v.prototype.removeDom=function(t){var e=this.getDefs(!1);e&&t[this._domName]&&(e.removeChild(t[this._domName]),t[this._domName]=null)},v.prototype.getDoms=function(){var t=this.getDefs(!1);if(!t)return[];var e=[];return a.each(this._tagNames,(function(r){var i=t.getElementsByTagName(r);e=e.concat([].slice.call(i))})),e},v.prototype.markAllUnused=function(){var t=this.getDoms(),e=this;a.each(t,(function(t){t[e._markLabel]=d}))},v.prototype.markUsed=function(t){t&&(t[this._markLabel]=p)},v.prototype.removeUnused=function(){var t=this.getDefs(!1);if(t){var e=this.getDoms(),r=this;a.each(e,(function(e){e[r._markLabel]!==p&&t.removeChild(e)}))}},v.prototype.getSvgProxy=function(t){return t instanceof o?u:t instanceof s?c:t instanceof h?f:u},v.prototype.getTextSvgElement=function(t){return t.__textSvgEl},v.prototype.getSvgElement=function(t){return t.__svgEl};var g=v;t.exports=g},34881:function(t,e,r){var i=r(19184),n=r(63122),a=r(70083),o=r(7471);function s(t,e){i.call(this,t,e,["linearGradient","radialGradient"],"__gradient_in_use__")}n.inherits(s,i),s.prototype.addWithoutUpdate=function(t,e){if(e&&e.style){var r=this;n.each(["fill","stroke"],(function(i){if(e.style[i]&&("linear"===e.style[i].type||"radial"===e.style[i].type)){var n,a=e.style[i],o=r.getDefs(!0);a._dom?(n=a._dom,o.contains(a._dom)||r.addDom(n)):n=r.add(a),r.markUsed(e);var s=n.getAttribute("id");t.setAttribute(i,"url(#"+s+")")}}))}},s.prototype.add=function(t){var e;if("linear"===t.type)e=this.createElement("linearGradient");else{if("radial"!==t.type)return a("Illegal gradient type."),null;e=this.createElement("radialGradient")}return t.id=t.id||this.nextId++,e.setAttribute("id","zr"+this._zrId+"-gradient-"+t.id),this.updateDom(t,e),this.addDom(e),e},s.prototype.update=function(t){var e=this;i.prototype.update.call(this,t,(function(){var r=t.type,i=t._dom.tagName;"linear"===r&&"linearGradient"===i||"radial"===r&&"radialGradient"===i?e.updateDom(t,t._dom):(e.removeDom(t),e.add(t))}))},s.prototype.updateDom=function(t,e){if("linear"===t.type)e.setAttribute("x1",t.x),e.setAttribute("y1",t.y),e.setAttribute("x2",t.x2),e.setAttribute("y2",t.y2);else{if("radial"!==t.type)return void a("Illegal gradient type.");e.setAttribute("cx",t.x),e.setAttribute("cy",t.y),e.setAttribute("r",t.r)}t.global?e.setAttribute("gradientUnits","userSpaceOnUse"):e.setAttribute("gradientUnits","objectBoundingBox"),e.innerHTML="";for(var r=t.colorStops,i=0,n=r.length;i<n;++i){var s=this.createElement("stop");s.setAttribute("offset",100*r[i].offset+"%");var h=r[i].color;if(h.indexOf("rgba")>-1){var l=o.parse(h)[3],u=o.toHex(h);s.setAttribute("stop-color","#"+u),s.setAttribute("stop-opacity",l)}else s.setAttribute("stop-color",r[i].color);e.appendChild(s)}t._dom=e},s.prototype.markUsed=function(t){if(t.style){var e=t.style.fill;e&&e._dom&&i.prototype.markUsed.call(this,e._dom),e=t.style.stroke,e&&e._dom&&i.prototype.markUsed.call(this,e._dom)}};var h=s;t.exports=h},1465:function(t,e,r){var i=r(19184),n=r(63122);function a(t,e){i.call(this,t,e,["filter"],"__filter_in_use__","_shadowDom")}function o(t){return t&&(t.shadowBlur||t.shadowOffsetX||t.shadowOffsetY||t.textShadowBlur||t.textShadowOffsetX||t.textShadowOffsetY)}n.inherits(a,i),a.prototype.addWithoutUpdate=function(t,e){if(e&&o(e.style)){var r;if(e._shadowDom){r=e._shadowDom;var i=this.getDefs(!0);i.contains(e._shadowDom)||this.addDom(r)}else r=this.add(e);this.markUsed(e);var n=r.getAttribute("id");t.style.filter="url(#"+n+")"}},a.prototype.add=function(t){var e=this.createElement("filter");return t._shadowDomId=t._shadowDomId||this.nextId++,e.setAttribute("id","zr"+this._zrId+"-shadow-"+t._shadowDomId),this.updateDom(t,e),this.addDom(e),e},a.prototype.update=function(t,e){var r=e.style;if(o(r)){var n=this;i.prototype.update.call(this,e,(function(){n.updateDom(e,e._shadowDom)}))}else this.remove(t,e)},a.prototype.remove=function(t,e){null!=e._shadowDomId&&(this.removeDom(t),t.style.filter="")},a.prototype.updateDom=function(t,e){var r=e.getElementsByTagName("feDropShadow");r=0===r.length?this.createElement("feDropShadow"):r[0];var i,n,a,o,s=t.style,h=t.scale&&t.scale[0]||1,l=t.scale&&t.scale[1]||1;if(s.shadowBlur||s.shadowOffsetX||s.shadowOffsetY)i=s.shadowOffsetX||0,n=s.shadowOffsetY||0,a=s.shadowBlur,o=s.shadowColor;else{if(!s.textShadowBlur)return void this.removeDom(e,s);i=s.textShadowOffsetX||0,n=s.textShadowOffsetY||0,a=s.textShadowBlur,o=s.textShadowColor}r.setAttribute("dx",i/h),r.setAttribute("dy",n/l),r.setAttribute("flood-color",o);var u=a/2/h,c=a/2/l,f=u+" "+c;r.setAttribute("stdDeviation",f),e.setAttribute("x","-100%"),e.setAttribute("y","-100%"),e.setAttribute("width",Math.ceil(a/2*200)+"%"),e.setAttribute("height",Math.ceil(a/2*200)+"%"),e.appendChild(r),t._shadowDom=e},a.prototype.markUsed=function(t){t._shadowDom&&i.prototype.markUsed.call(this,t._shadowDom)};var s=a;t.exports=s},69704:function(t,e,r){r(97278);var i=r(89519),n=i.registerPainter,a=r(23372);n("svg",a)},7471:function(t,e,r){var i=r(45746),n={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]};function a(t){return t=Math.round(t),t<0?0:t>255?255:t}function o(t){return t=Math.round(t),t<0?0:t>360?360:t}function s(t){return t<0?0:t>1?1:t}function h(t){return t.length&&"%"===t.charAt(t.length-1)?a(parseFloat(t)/100*255):a(parseInt(t,10))}function l(t){return t.length&&"%"===t.charAt(t.length-1)?s(parseFloat(t)/100):s(parseFloat(t))}function u(t,e,r){return r<0?r+=1:r>1&&(r-=1),6*r<1?t+(e-t)*r*6:2*r<1?e:3*r<2?t+(e-t)*(2/3-r)*6:t}function c(t,e,r){return t+(e-t)*r}function f(t,e,r,i,n){return t[0]=e,t[1]=r,t[2]=i,t[3]=n,t}function d(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t}var p=new i(20),v=null;function g(t,e){v&&d(v,e),v=p.put(t,v||e.slice())}function y(t,e){if(t){e=e||[];var r=p.get(t);if(r)return d(e,r);t+="";var i=t.replace(/ /g,"").toLowerCase();if(i in n)return d(e,n[i]),g(t,e),e;if("#"!==i.charAt(0)){var a=i.indexOf("("),o=i.indexOf(")");if(-1!==a&&o+1===i.length){var s=i.substr(0,a),u=i.substr(a+1,o-(a+1)).split(","),c=1;switch(s){case"rgba":if(4!==u.length)return void f(e,0,0,0,1);c=l(u.pop());case"rgb":return 3!==u.length?void f(e,0,0,0,1):(f(e,h(u[0]),h(u[1]),h(u[2]),c),g(t,e),e);case"hsla":return 4!==u.length?void f(e,0,0,0,1):(u[3]=l(u[3]),_(u,e),g(t,e),e);case"hsl":return 3!==u.length?void f(e,0,0,0,1):(_(u,e),g(t,e),e);default:return}}f(e,0,0,0,1)}else{if(4===i.length){var v=parseInt(i.substr(1),16);return v>=0&&v<=4095?(f(e,(3840&v)>>4|(3840&v)>>8,240&v|(240&v)>>4,15&v|(15&v)<<4,1),g(t,e),e):void f(e,0,0,0,1)}if(7===i.length){v=parseInt(i.substr(1),16);return v>=0&&v<=16777215?(f(e,(16711680&v)>>16,(65280&v)>>8,255&v,1),g(t,e),e):void f(e,0,0,0,1)}}}}function _(t,e){var r=(parseFloat(t[0])%360+360)%360/360,i=l(t[1]),n=l(t[2]),o=n<=.5?n*(i+1):n+i-n*i,s=2*n-o;return e=e||[],f(e,a(255*u(s,o,r+1/3)),a(255*u(s,o,r)),a(255*u(s,o,r-1/3)),1),4===t.length&&(e[3]=t[3]),e}function m(t){if(t){var e,r,i=t[0]/255,n=t[1]/255,a=t[2]/255,o=Math.min(i,n,a),s=Math.max(i,n,a),h=s-o,l=(s+o)/2;if(0===h)e=0,r=0;else{r=l<.5?h/(s+o):h/(2-s-o);var u=((s-i)/6+h/2)/h,c=((s-n)/6+h/2)/h,f=((s-a)/6+h/2)/h;i===s?e=f-c:n===s?e=1/3+u-f:a===s&&(e=2/3+c-u),e<0&&(e+=1),e>1&&(e-=1)}var d=[360*e,r,l];return null!=t[3]&&d.push(t[3]),d}}function x(t,e){var r=y(t);if(r){for(var i=0;i<3;i++)r[i]=e<0?r[i]*(1-e)|0:(255-r[i])*e+r[i]|0,r[i]>255?r[i]=255:t[i]<0&&(r[i]=0);return A(r,4===r.length?"rgba":"rgb")}}function w(t){var e=y(t);if(e)return((1<<24)+(e[0]<<16)+(e[1]<<8)+ +e[2]).toString(16).slice(1)}function b(t,e,r){if(e&&e.length&&t>=0&&t<=1){r=r||[];var i=t*(e.length-1),n=Math.floor(i),o=Math.ceil(i),h=e[n],l=e[o],u=i-n;return r[0]=a(c(h[0],l[0],u)),r[1]=a(c(h[1],l[1],u)),r[2]=a(c(h[2],l[2],u)),r[3]=s(c(h[3],l[3],u)),r}}var k=b;function T(t,e,r){if(e&&e.length&&t>=0&&t<=1){var i=t*(e.length-1),n=Math.floor(i),o=Math.ceil(i),h=y(e[n]),l=y(e[o]),u=i-n,f=A([a(c(h[0],l[0],u)),a(c(h[1],l[1],u)),a(c(h[2],l[2],u)),s(c(h[3],l[3],u))],"rgba");return r?{color:f,leftIndex:n,rightIndex:o,value:i}:f}}var S=T;function C(t,e,r,i){if(t=y(t),t)return t=m(t),null!=e&&(t[0]=o(e)),null!=r&&(t[1]=l(r)),null!=i&&(t[2]=l(i)),A(_(t),"rgba")}function P(t,e){if(t=y(t),t&&null!=e)return t[3]=s(e),A(t,"rgba")}function A(t,e){if(t&&t.length){var r=t[0]+","+t[1]+","+t[2];return"rgba"!==e&&"hsva"!==e&&"hsla"!==e||(r+=","+t[3]),e+"("+r+")"}}e.parse=y,e.lift=x,e.toHex=w,e.fastLerp=b,e.fastMapToColor=k,e.lerp=T,e.mapToColor=S,e.modifyHSL=C,e.modifyAlpha=P,e.stringify=A},61234:function(t,e,r){var i=r(79116),n=r(45474),a=r(84845),o=r(96005),s=r(58195),h=r(85294),l=r(22923),u=r(73477),c=r(83286),f=r(23074),d=r(48515),p=r(82516),v=r(97333),g=r(74315),y=g.createFromString,_=r(63122),m=_.isString,x=_.extend,w=_.defaults,b=_.trim,k=_.each,T=/[\s,]+/;function S(t){if(m(t)){var e=new DOMParser;t=e.parseFromString(t,"text/xml")}9===t.nodeType&&(t=t.firstChild);while("svg"!==t.nodeName.toLowerCase()||1!==t.nodeType)t=t.nextSibling;return t}function C(){this._defs={},this._root=null,this._isDefine=!1,this._isText=!1}C.prototype.parse=function(t,e){e=e||{};var r=S(t);if(!r)throw new Error("Illegal svg");var n=new i;this._root=n;var a=r.getAttribute("viewBox")||"",o=parseFloat(r.getAttribute("width")||e.width),h=parseFloat(r.getAttribute("height")||e.height);isNaN(o)&&(o=null),isNaN(h)&&(h=null),O(r,n,null,!0);var l,u,c=r.firstChild;while(c)this._parseNode(c,n),c=c.nextSibling;if(a){var f=b(a).split(T);f.length>=4&&(l={x:parseFloat(f[0]||0),y:parseFloat(f[1]||0),width:parseFloat(f[2]),height:parseFloat(f[3])})}if(l&&null!=o&&null!=h&&(u=j(l,o,h),!e.ignoreViewBox)){var d=n;n=new i,n.add(d),d.scale=u.scale.slice(),d.position=u.position.slice()}return e.ignoreRootClip||null==o||null==h||n.setClipPath(new s({shape:{x:0,y:0,width:o,height:h}})),{root:n,width:o,height:h,viewBoxRect:l,viewBoxTransform:u}},C.prototype._parseNode=function(t,e){var r,i=t.nodeName.toLowerCase();if("defs"===i?this._isDefine=!0:"text"===i&&(this._isText=!0),this._isDefine){var n=A[i];if(n){var a=n.call(this,t),o=t.getAttribute("id");o&&(this._defs[o]=a)}}else{n=P[i];n&&(r=n.call(this,t,e),e.add(r))}var s=t.firstChild;while(s)1===s.nodeType&&this._parseNode(s,r),3===s.nodeType&&this._isText&&this._parseText(s,r),s=s.nextSibling;"defs"===i?this._isDefine=!1:"text"===i&&(this._isText=!1)},C.prototype._parseText=function(t,e){if(1===t.nodeType){var r=t.getAttribute("dx")||0,i=t.getAttribute("dy")||0;this._textX+=parseFloat(r),this._textY+=parseFloat(i)}var n=new a({style:{text:t.textContent,transformText:!0},position:[this._textX||0,this._textY||0]});L(e,n),O(t,n,this._defs);var o=n.style.fontSize;o&&o<9&&(n.style.fontSize=9,n.scale=n.scale||[1,1],n.scale[0]*=o/9,n.scale[1]*=o/9);var s=n.getBoundingRect();return this._textX+=s.width,e.add(n),n};var P={g:function(t,e){var r=new i;return L(e,r),O(t,r,this._defs),r},rect:function(t,e){var r=new s;return L(e,r),O(t,r,this._defs),r.setShape({x:parseFloat(t.getAttribute("x")||0),y:parseFloat(t.getAttribute("y")||0),width:parseFloat(t.getAttribute("width")||0),height:parseFloat(t.getAttribute("height")||0)}),r},circle:function(t,e){var r=new o;return L(e,r),O(t,r,this._defs),r.setShape({cx:parseFloat(t.getAttribute("cx")||0),cy:parseFloat(t.getAttribute("cy")||0),r:parseFloat(t.getAttribute("r")||0)}),r},line:function(t,e){var r=new l;return L(e,r),O(t,r,this._defs),r.setShape({x1:parseFloat(t.getAttribute("x1")||0),y1:parseFloat(t.getAttribute("y1")||0),x2:parseFloat(t.getAttribute("x2")||0),y2:parseFloat(t.getAttribute("y2")||0)}),r},ellipse:function(t,e){var r=new h;return L(e,r),O(t,r,this._defs),r.setShape({cx:parseFloat(t.getAttribute("cx")||0),cy:parseFloat(t.getAttribute("cy")||0),rx:parseFloat(t.getAttribute("rx")||0),ry:parseFloat(t.getAttribute("ry")||0)}),r},polygon:function(t,e){var r=t.getAttribute("points");r&&(r=D(r));var i=new c({shape:{points:r||[]}});return L(e,i),O(t,i,this._defs),i},polyline:function(t,e){var r=new u;L(e,r),O(t,r,this._defs);var i=t.getAttribute("points");i&&(i=D(i));var n=new f({shape:{points:i||[]}});return n},image:function(t,e){var r=new n;return L(e,r),O(t,r,this._defs),r.setStyle({image:t.getAttribute("xlink:href"),x:t.getAttribute("x"),y:t.getAttribute("y"),width:t.getAttribute("width"),height:t.getAttribute("height")}),r},text:function(t,e){var r=t.getAttribute("x")||0,n=t.getAttribute("y")||0,a=t.getAttribute("dx")||0,o=t.getAttribute("dy")||0;this._textX=parseFloat(r)+parseFloat(a),this._textY=parseFloat(n)+parseFloat(o);var s=new i;return L(e,s),O(t,s,this._defs),s},tspan:function(t,e){var r=t.getAttribute("x"),n=t.getAttribute("y");null!=r&&(this._textX=parseFloat(r)),null!=n&&(this._textY=parseFloat(n));var a=t.getAttribute("dx")||0,o=t.getAttribute("dy")||0,s=new i;return L(e,s),O(t,s,this._defs),this._textX+=a,this._textY+=o,s},path:function(t,e){var r=t.getAttribute("d")||"",i=y(r);return L(e,i),O(t,i,this._defs),i}},A={lineargradient:function(t){var e=parseInt(t.getAttribute("x1")||0,10),r=parseInt(t.getAttribute("y1")||0,10),i=parseInt(t.getAttribute("x2")||10,10),n=parseInt(t.getAttribute("y2")||0,10),a=new d(e,r,i,n);return M(t,a),a},radialgradient:function(t){}};function M(t,e){var r=t.firstChild;while(r){if(1===r.nodeType){var i=r.getAttribute("offset");i=i.indexOf("%")>0?parseInt(i,10)/100:i?parseFloat(i):0;var n=r.getAttribute("stop-color")||"#000000";e.addColorStop(i,n)}r=r.nextSibling}}function L(t,e){t&&t.__inheritedStyle&&(e.__inheritedStyle||(e.__inheritedStyle={}),w(e.__inheritedStyle,t.__inheritedStyle))}function D(t){for(var e=b(t).split(T),r=[],i=0;i<e.length;i+=2){var n=parseFloat(e[i]),a=parseFloat(e[i+1]);r.push([n,a])}return r}var z={fill:"fill",stroke:"stroke","stroke-width":"lineWidth",opacity:"opacity","fill-opacity":"fillOpacity","stroke-opacity":"strokeOpacity","stroke-dasharray":"lineDash","stroke-dashoffset":"lineDashOffset","stroke-linecap":"lineCap","stroke-linejoin":"lineJoin","stroke-miterlimit":"miterLimit","font-family":"fontFamily","font-size":"fontSize","font-style":"fontStyle","font-weight":"fontWeight","text-align":"textAlign","alignment-baseline":"textBaseline"};function O(t,e,r,i){var n=e.__inheritedStyle||{},a="text"===e.type;if(1===t.nodeType&&(B(t,e),x(n,Z(t)),!i))for(var o in z)if(z.hasOwnProperty(o)){var s=t.getAttribute(o);null!=s&&(n[z[o]]=s)}var h=a?"textFill":"fill",l=a?"textStroke":"stroke";e.style=e.style||new p;var u=e.style;null!=n.fill&&u.set(h,I(n.fill,r)),null!=n.stroke&&u.set(l,I(n.stroke,r)),k(["lineWidth","opacity","fillOpacity","strokeOpacity","miterLimit","fontSize"],(function(t){var e="lineWidth"===t&&a?"textStrokeWidth":t;null!=n[t]&&u.set(e,parseFloat(n[t]))})),n.textBaseline&&"auto"!==n.textBaseline||(n.textBaseline="alphabetic"),"alphabetic"===n.textBaseline&&(n.textBaseline="bottom"),"start"===n.textAlign&&(n.textAlign="left"),"end"===n.textAlign&&(n.textAlign="right"),k(["lineDashOffset","lineCap","lineJoin","fontWeight","fontFamily","fontStyle","textAlign","textBaseline"],(function(t){null!=n[t]&&u.set(t,n[t])})),n.lineDash&&(e.style.lineDash=b(n.lineDash).split(T)),u[l]&&"none"!==u[l]&&(e[l]=!0),e.__inheritedStyle=n}var R=/url\(\s*#(.*?)\)/;function I(t,e){var r=e&&t&&t.match(R);if(r){var i=b(r[1]),n=e[i];return n}return t}var F=/(translate|scale|rotate|skewX|skewY|matrix)\(([\-\s0-9\.e,]*)\)/g;function B(t,e){var r=t.getAttribute("transform");if(r){r=r.replace(/,/g," ");var i=null,n=[];r.replace(F,(function(t,e,r){n.push(e,r)}));for(var a=n.length-1;a>0;a-=2){var o=n[a],s=n[a-1];switch(i=i||v.create(),s){case"translate":o=b(o).split(T),v.translate(i,i,[parseFloat(o[0]),parseFloat(o[1]||0)]);break;case"scale":o=b(o).split(T),v.scale(i,i,[parseFloat(o[0]),parseFloat(o[1]||o[0])]);break;case"rotate":o=b(o).split(T),v.rotate(i,i,parseFloat(o[0]));break;case"skew":o=b(o).split(T);break;case"matrix":o=b(o).split(T);i[0]=parseFloat(o[0]),i[1]=parseFloat(o[1]),i[2]=parseFloat(o[2]),i[3]=parseFloat(o[3]),i[4]=parseFloat(o[4]),i[5]=parseFloat(o[5]);break}}e.setLocalTransform(i)}}var E=/([^\s:;]+)\s*:\s*([^:;]+)/g;function Z(t){var e=t.getAttribute("style"),r={};if(!e)return r;var i,n={};E.lastIndex=0;while(null!=(i=E.exec(e)))n[i[1]]=i[2];for(var a in z)z.hasOwnProperty(a)&&null!=n[a]&&(r[z[a]]=n[a]);return r}function j(t,e,r){var i=e/t.width,n=r/t.height,a=Math.min(i,n),o=[a,a],s=[-(t.x+t.width/2)*a+e/2,-(t.y+t.height/2)*a+r/2];return{scale:o,position:s}}function H(t,e){var r=new C;return r.parse(t,e)}e.parseXML=S,e.makeViewBoxTransform=j,e.parseSVG=H},74315:function(t,e,r){var i=r(73477),n=r(11348),a=r(6600),o=Math.sqrt,s=Math.sin,h=Math.cos,l=Math.PI,u=function(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])},c=function(t,e){return(t[0]*e[0]+t[1]*e[1])/(u(t)*u(e))},f=function(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(c(t,e))};function d(t,e,r,i,n,a,u,d,p,v,g){var y=p*(l/180),_=h(y)*(t-r)/2+s(y)*(e-i)/2,m=-1*s(y)*(t-r)/2+h(y)*(e-i)/2,x=_*_/(u*u)+m*m/(d*d);x>1&&(u*=o(x),d*=o(x));var w=(n===a?-1:1)*o((u*u*(d*d)-u*u*(m*m)-d*d*(_*_))/(u*u*(m*m)+d*d*(_*_)))||0,b=w*u*m/d,k=w*-d*_/u,T=(t+r)/2+h(y)*b-s(y)*k,S=(e+i)/2+s(y)*b+h(y)*k,C=f([1,0],[(_-b)/u,(m-k)/d]),P=[(_-b)/u,(m-k)/d],A=[(-1*_-b)/u,(-1*m-k)/d],M=f(P,A);c(P,A)<=-1&&(M=l),c(P,A)>=1&&(M=0),0===a&&M>0&&(M-=2*l),1===a&&M<0&&(M+=2*l),g.addData(v,T,S,u,d,C,M,y,a)}var p=/([mlvhzcqtsa])([^mlvhzcqtsa]*)/gi,v=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;function g(t){if(!t)return new n;for(var e,r=0,i=0,a=r,o=i,s=new n,h=n.CMD,l=t.match(p),u=0;u<l.length;u++){for(var c,f=l[u],g=f.charAt(0),y=f.match(v)||[],_=y.length,m=0;m<_;m++)y[m]=parseFloat(y[m]);var x=0;while(x<_){var w,b,k,T,S,C,P,A=r,M=i;switch(g){case"l":r+=y[x++],i+=y[x++],c=h.L,s.addData(c,r,i);break;case"L":r=y[x++],i=y[x++],c=h.L,s.addData(c,r,i);break;case"m":r+=y[x++],i+=y[x++],c=h.M,s.addData(c,r,i),a=r,o=i,g="l";break;case"M":r=y[x++],i=y[x++],c=h.M,s.addData(c,r,i),a=r,o=i,g="L";break;case"h":r+=y[x++],c=h.L,s.addData(c,r,i);break;case"H":r=y[x++],c=h.L,s.addData(c,r,i);break;case"v":i+=y[x++],c=h.L,s.addData(c,r,i);break;case"V":i=y[x++],c=h.L,s.addData(c,r,i);break;case"C":c=h.C,s.addData(c,y[x++],y[x++],y[x++],y[x++],y[x++],y[x++]),r=y[x-2],i=y[x-1];break;case"c":c=h.C,s.addData(c,y[x++]+r,y[x++]+i,y[x++]+r,y[x++]+i,y[x++]+r,y[x++]+i),r+=y[x-2],i+=y[x-1];break;case"S":w=r,b=i;var L=s.len(),D=s.data;e===h.C&&(w+=r-D[L-4],b+=i-D[L-3]),c=h.C,A=y[x++],M=y[x++],r=y[x++],i=y[x++],s.addData(c,w,b,A,M,r,i);break;case"s":w=r,b=i;L=s.len(),D=s.data;e===h.C&&(w+=r-D[L-4],b+=i-D[L-3]),c=h.C,A=r+y[x++],M=i+y[x++],r+=y[x++],i+=y[x++],s.addData(c,w,b,A,M,r,i);break;case"Q":A=y[x++],M=y[x++],r=y[x++],i=y[x++],c=h.Q,s.addData(c,A,M,r,i);break;case"q":A=y[x++]+r,M=y[x++]+i,r+=y[x++],i+=y[x++],c=h.Q,s.addData(c,A,M,r,i);break;case"T":w=r,b=i;L=s.len(),D=s.data;e===h.Q&&(w+=r-D[L-4],b+=i-D[L-3]),r=y[x++],i=y[x++],c=h.Q,s.addData(c,w,b,r,i);break;case"t":w=r,b=i;L=s.len(),D=s.data;e===h.Q&&(w+=r-D[L-4],b+=i-D[L-3]),r+=y[x++],i+=y[x++],c=h.Q,s.addData(c,w,b,r,i);break;case"A":k=y[x++],T=y[x++],S=y[x++],C=y[x++],P=y[x++],A=r,M=i,r=y[x++],i=y[x++],c=h.A,d(A,M,r,i,C,P,k,T,S,c,s);break;case"a":k=y[x++],T=y[x++],S=y[x++],C=y[x++],P=y[x++],A=r,M=i,r+=y[x++],i+=y[x++],c=h.A,d(A,M,r,i,C,P,k,T,S,c,s);break}}"z"!==g&&"Z"!==g||(c=h.Z,s.addData(c),r=a,i=o),e=c}return s.toStatic(),s}function y(t,e){var r=g(t);return e=e||{},e.buildPath=function(t){if(t.setData){t.setData(r.data);var e=t.getContext();e&&t.rebuildPath(e)}else{e=t;r.rebuildPath(e)}},e.applyTransform=function(t){a(r,t),this.dirty(!0)},e}function _(t,e){return new i(y(t,e))}function m(t,e){return i.extend(y(t,e))}function x(t,e){for(var r=[],n=t.length,a=0;a<n;a++){var o=t[a];o.path||o.createPathProxy(),o.__dirtyPath&&o.buildPath(o.path,o.shape,!0),r.push(o.path)}var s=new i(e);return s.createPathProxy(),s.buildPath=function(t){t.appendPath(r);var e=t.getContext();e&&t.rebuildPath(e)},s}e.createFromString=_,e.extendFromString=m,e.mergePath=x},6600:function(t,e,r){var i=r(11348),n=r(78244),a=n.applyTransform,o=i.CMD,s=[[],[],[]],h=Math.sqrt,l=Math.atan2;function u(t,e){var r,i,n,u,c,f=t.data,d=o.M,p=o.C,v=o.L,g=o.R,y=o.A,_=o.Q;for(n=0,u=0;n<f.length;){switch(r=f[n++],u=n,i=0,r){case d:i=1;break;case v:i=1;break;case p:i=3;break;case _:i=2;break;case y:var m=e[4],x=e[5],w=h(e[0]*e[0]+e[1]*e[1]),b=h(e[2]*e[2]+e[3]*e[3]),k=l(-e[1]/b,e[0]/w);f[n]*=w,f[n++]+=m,f[n]*=b,f[n++]+=x,f[n++]*=w,f[n++]*=b,f[n++]+=k,f[n++]+=k,n+=2,u=n;break;case g:T[0]=f[n++],T[1]=f[n++],a(T,T,e),f[u++]=T[0],f[u++]=T[1],T[0]+=f[n++],T[1]+=f[n++],a(T,T,e),f[u++]=T[0],f[u++]=T[1]}for(c=0;c<i;c++){var T=s[c];T[0]=f[n++],T[1]=f[n++],a(T,T,e),f[u++]=T[0],f[u++]=T[1]}}}t.exports=u},39544:function(t,e,r){var i=r(70083),n=r(88997),a=r(63122),o=a.each;function s(t){return parseInt(t,10)}function h(t,e){n.initVML(),this.root=t,this.storage=e;var r=document.createElement("div"),i=document.createElement("div");r.style.cssText="display:inline-block;overflow:hidden;position:relative;width:300px;height:150px;",i.style.cssText="position:absolute;left:0;top:0;",t.appendChild(r),this._vmlRoot=i,this._vmlViewport=r,this.resize();var a=e.delFromStorage,o=e.addToStorage;e.delFromStorage=function(t){a.call(e,t),t&&t.onRemove&&t.onRemove(i)},e.addToStorage=function(t){t.onAdd&&t.onAdd(i),o.call(e,t)},this._firstPaint=!0}function l(t){return function(){i('In IE8.0 VML mode painter not support method "'+t+'"')}}h.prototype={constructor:h,getType:function(){return"vml"},getViewportRoot:function(){return this._vmlViewport},getViewportRootOffset:function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},refresh:function(){var t=this.storage.getDisplayList(!0,!0);this._paintList(t)},_paintList:function(t){for(var e=this._vmlRoot,r=0;r<t.length;r++){var i=t[r];i.invisible||i.ignore?(i.__alreadyNotVisible||i.onRemove(e),i.__alreadyNotVisible=!0):(i.__alreadyNotVisible&&i.onAdd(e),i.__alreadyNotVisible=!1,i.__dirty&&(i.beforeBrush&&i.beforeBrush(),(i.brushVML||i.brush).call(i,e),i.afterBrush&&i.afterBrush())),i.__dirty=!1}this._firstPaint&&(this._vmlViewport.appendChild(e),this._firstPaint=!1)},resize:function(t,e){t=null==t?this._getWidth():t,e=null==e?this._getHeight():e;if(this._width!==t||this._height!==e){this._width=t,this._height=e;var r=this._vmlViewport.style;r.width=t+"px",r.height=e+"px"}},dispose:function(){this.root.innerHTML="",this._vmlRoot=this._vmlViewport=this.storage=null},getWidth:function(){return this._width},getHeight:function(){return this._height},clear:function(){this._vmlViewport&&this.root.removeChild(this._vmlViewport)},_getWidth:function(){var t=this.root,e=t.currentStyle;return(t.clientWidth||s(e.width))-s(e.paddingLeft)-s(e.paddingRight)|0},_getHeight:function(){var t=this.root,e=t.currentStyle;return(t.clientHeight||s(e.height))-s(e.paddingTop)-s(e.paddingBottom)|0}},o(["getLayer","insertLayer","eachLayer","eachBuiltinLayer","eachOtherLayer","getLayers","modLayer","delLayer","clearLayer","toDataURL","pathToImage"],(function(t){h.prototype[t]=l(t)}));var u=h;t.exports=u},88997:function(t,e,r){var i,n=r(95160),a="urn:schemas-microsoft-com:vml",o="undefined"===typeof window?null:window,s=!1,h=o&&o.document;function l(t){return i(t)}if(h&&!n.canvasSupported)try{!h.namespaces.zrvml&&h.namespaces.add("zrvml",a),i=function(t){return h.createElement("<zrvml:"+t+' class="zrvml">')}}catch(c){i=function(t){return h.createElement("<"+t+' xmlns="'+a+'" class="zrvml">')}}function u(){if(!s&&h){s=!0;var t=h.styleSheets;t.length<31?h.createStyleSheet().addRule(".zrvml","behavior:url(#default#VML)"):t[0].addRule(".zrvml","behavior:url(#default#VML)")}}e.doc=h,e.createNode=l,e.initVML=u},3861:function(t,e,r){var i=r(57847)["default"],n=r(95160),a=r(78244),o=a.applyTransform,s=r(46630),h=r(7471),l=r(2278),u=r(82076),c=r(73637),f=r(84093),d=r(45474),p=r(84845),v=r(73477),g=r(11348),y=r(80841),_=r(88997),m=g.CMD,x=Math.round,w=Math.sqrt,b=Math.abs,k=Math.cos,T=Math.sin,S=Math.max;if(!n.canvasSupported){var C=",",P="progid:DXImageTransform.Microsoft",A=21600,M=A/2,L=1e5,D=1e3,z=function(t){t.style.cssText="position:absolute;left:0;top:0;width:1px;height:1px;",t.coordsize=A+","+A,t.coordorigin="0,0"},O=function(t){return String(t).replace(/&/g,"&amp;").replace(/"/g,"&quot;")},R=function(t,e,r){return"rgb("+[t,e,r].join(",")+")"},I=function(t,e){e&&t&&e.parentNode!==t&&t.appendChild(e)},F=function(t,e){e&&t&&e.parentNode===t&&t.removeChild(e)},B=function(t,e,r){return(parseFloat(t)||0)*L+(parseFloat(e)||0)*D+r},E=u.parsePercent,Z=function(t,e,r){var i=h.parse(e);r=+r,isNaN(r)&&(r=1),i&&(t.color=R(i[0],i[1],i[2]),t.opacity=r*i[3])},j=function(t){var e=h.parse(t);return[R(e[0],e[1],e[2]),e[3]]},H=function(t,e,r){var i=e.fill;if(null!=i)if(i instanceof y){var n,a=0,s=[0,0],h=0,l=1,u=r.getBoundingRect(),c=u.width,f=u.height;if("linear"===i.type){n="gradient";var d=r.transform,p=[i.x*c,i.y*f],v=[i.x2*c,i.y2*f];d&&(o(p,p,d),o(v,v,d));var g=v[0]-p[0],_=v[1]-p[1];a=180*Math.atan2(g,_)/Math.PI,a<0&&(a+=360),a<1e-6&&(a=0)}else{n="gradientradial";p=[i.x*c,i.y*f],d=r.transform;var m=r.scale,x=c,w=f;s=[(p[0]-u.x)/x,(p[1]-u.y)/w],d&&o(p,p,d),x/=m[0]*A,w/=m[1]*A;var b=S(x,w);h=0/b,l=2*i.r/b-h}var k=i.colorStops.slice();k.sort((function(t,e){return t.offset-e.offset}));for(var T=k.length,C=[],P=[],M=0;M<T;M++){var L=k[M],D=j(L.color);P.push(L.offset*l+h+" "+D[0]),0!==M&&M!==T-1||C.push(D)}if(T>=2){var z=C[0][0],O=C[1][0],R=C[0][1]*e.opacity,I=C[1][1]*e.opacity;t.type=n,t.method="none",t.focus="100%",t.angle=a,t.color=z,t.color2=O,t.colors=P.join(","),t.opacity=I,t.opacity2=R}"radial"===n&&(t.focusposition=s.join(","))}else Z(t,i,e.opacity)},N=function(t,e){e.lineDash&&(t.dashstyle=e.lineDash.join(" ")),null==e.stroke||e.stroke instanceof y||Z(t,e.stroke,e.opacity)},W=function(t,e,r,i){var n="fill"===e,a=t.getElementsByTagName(e)[0];null!=r[e]&&"none"!==r[e]&&(n||!n&&r.lineWidth)?(t[n?"filled":"stroked"]="true",r[e]instanceof y&&F(t,a),a||(a=_.createNode(e)),n?H(a,r,i):N(a,r),I(t,a)):(t[n?"filled":"stroked"]="false",F(t,a))},V=[[],[],[]],q=function(t,e){var r,i,n,a,s,h,l=m.M,u=m.C,c=m.L,f=m.A,d=m.Q,p=[],v=t.data,g=t.len();for(a=0;a<g;){switch(n=v[a++],i="",r=0,n){case l:i=" m ",r=1,s=v[a++],h=v[a++],V[0][0]=s,V[0][1]=h;break;case c:i=" l ",r=1,s=v[a++],h=v[a++],V[0][0]=s,V[0][1]=h;break;case d:case u:i=" c ",r=3;var y,_,b=v[a++],S=v[a++],P=v[a++],L=v[a++];n===d?(y=P,_=L,P=(P+2*b)/3,L=(L+2*S)/3,b=(s+2*b)/3,S=(h+2*S)/3):(y=v[a++],_=v[a++]),V[0][0]=b,V[0][1]=S,V[1][0]=P,V[1][1]=L,V[2][0]=y,V[2][1]=_,s=y,h=_;break;case f:var D=0,z=0,O=1,R=1,I=0;e&&(D=e[4],z=e[5],O=w(e[0]*e[0]+e[1]*e[1]),R=w(e[2]*e[2]+e[3]*e[3]),I=Math.atan2(-e[1]/R,e[0]/O));var F=v[a++],B=v[a++],E=v[a++],Z=v[a++],j=v[a++]+I,H=v[a++]+j+I;a++;var N=v[a++],W=F+k(j)*E,q=B+T(j)*Z,Y=(b=F+k(H)*E,S=B+T(H)*Z,N?" wa ":" at ");Math.abs(W-b)<1e-4&&(Math.abs(H-j)>.01?N&&(W+=270/A):Math.abs(q-B)<1e-4?N&&W<F||!N&&W>F?S-=270/A:S+=270/A:N&&q<B||!N&&q>B?b+=270/A:b-=270/A),p.push(Y,x(((F-E)*O+D)*A-M),C,x(((B-Z)*R+z)*A-M),C,x(((F+E)*O+D)*A-M),C,x(((B+Z)*R+z)*A-M),C,x((W*O+D)*A-M),C,x((q*R+z)*A-M),C,x((b*O+D)*A-M),C,x((S*R+z)*A-M)),s=b,h=S;break;case m.R:var X=V[0],U=V[1];X[0]=v[a++],X[1]=v[a++],U[0]=X[0]+v[a++],U[1]=X[1]+v[a++],e&&(o(X,X,e),o(U,U,e)),X[0]=x(X[0]*A-M),U[0]=x(U[0]*A-M),X[1]=x(X[1]*A-M),U[1]=x(U[1]*A-M),p.push(" m ",X[0],C,X[1]," l ",U[0],C,X[1]," l ",U[0],C,U[1]," l ",X[0],C,U[1]);break;case m.Z:p.push(" x ")}if(r>0){p.push(i);for(var $=0;$<r;$++){var G=V[$];e&&o(G,G,e),p.push(x(G[0]*A-M),C,x(G[1]*A-M),$<r-1?C:"")}}}return p.join("")};v.prototype.brushVML=function(t){var e=this.style,r=this._vmlEl;r||(r=_.createNode("shape"),z(r),this._vmlEl=r),W(r,"fill",e,this),W(r,"stroke",e,this);var i=this.transform,n=null!=i,a=r.getElementsByTagName("stroke")[0];if(a){var o=e.lineWidth;if(n&&!e.strokeNoScale){var s=i[0]*i[3]-i[1]*i[2];o*=w(b(s))}a.weight=o+"px"}var h=this.path||(this.path=new g);this.__dirtyPath&&(h.beginPath(),h.subPixelOptimize=!1,this.buildPath(h,this.shape),h.toStatic(),this.__dirtyPath=!1),r.path=q(h,this.transform),r.style.zIndex=B(this.zlevel,this.z,this.z2),I(t,r),null!=e.text?this.drawRectText(t,this.getBoundingRect()):this.removeRectText(t)},v.prototype.onRemove=function(t){F(t,this._vmlEl),this.removeRectText(t)},v.prototype.onAdd=function(t){I(t,this._vmlEl),this.appendRectText(t)};var Y=function(t){return"object"===i(t)&&t.tagName&&"IMG"===t.tagName.toUpperCase()};d.prototype.brushVML=function(t){var e,r,i=this.style,n=i.image;if(Y(n)){var a=n.src;if(a===this._imageSrc)e=this._imageWidth,r=this._imageHeight;else{var s=n.runtimeStyle,h=s.width,l=s.height;s.width="auto",s.height="auto",e=n.width,r=n.height,s.width=h,s.height=l,this._imageSrc=a,this._imageWidth=e,this._imageHeight=r}n=a}else n===this._imageSrc&&(e=this._imageWidth,r=this._imageHeight);if(n){var u=i.x||0,c=i.y||0,f=i.width,d=i.height,p=i.sWidth,v=i.sHeight,g=i.sx||0,y=i.sy||0,m=p&&v,b=this._vmlEl;b||(b=_.doc.createElement("div"),z(b),this._vmlEl=b);var k,T=b.style,A=!1,M=1,L=1;if(this.transform&&(k=this.transform,M=w(k[0]*k[0]+k[1]*k[1]),L=w(k[2]*k[2]+k[3]*k[3]),A=k[1]||k[2]),A){var D=[u,c],O=[u+f,c],R=[u,c+d],F=[u+f,c+d];o(D,D,k),o(O,O,k),o(R,R,k),o(F,F,k);var E=S(D[0],O[0],R[0],F[0]),Z=S(D[1],O[1],R[1],F[1]),j=[];j.push("M11=",k[0]/M,C,"M12=",k[2]/L,C,"M21=",k[1]/M,C,"M22=",k[3]/L,C,"Dx=",x(u*M+k[4]),C,"Dy=",x(c*L+k[5])),T.padding="0 "+x(E)+"px "+x(Z)+"px 0",T.filter=P+".Matrix("+j.join("")+", SizingMethod=clip)"}else k&&(u=u*M+k[4],c=c*L+k[5]),T.filter="",T.left=x(u)+"px",T.top=x(c)+"px";var H=this._imageEl,N=this._cropEl;H||(H=_.doc.createElement("div"),this._imageEl=H);var W=H.style;if(m){if(e&&r)W.width=x(M*e*f/p)+"px",W.height=x(L*r*d/v)+"px";else{var V=new Image,q=this;V.onload=function(){V.onload=null,e=V.width,r=V.height,W.width=x(M*e*f/p)+"px",W.height=x(L*r*d/v)+"px",q._imageWidth=e,q._imageHeight=r,q._imageSrc=n},V.src=n}N||(N=_.doc.createElement("div"),N.style.overflow="hidden",this._cropEl=N);var X=N.style;X.width=x((f+g*f/p)*M),X.height=x((d+y*d/v)*L),X.filter=P+".Matrix(Dx="+-g*f/p*M+",Dy="+-y*d/v*L+")",N.parentNode||b.appendChild(N),H.parentNode!==N&&N.appendChild(H)}else W.width=x(M*f)+"px",W.height=x(L*d)+"px",b.appendChild(H),N&&N.parentNode&&(b.removeChild(N),this._cropEl=null);var U="",$=i.opacity;$<1&&(U+=".Alpha(opacity="+x(100*$)+") "),U+=P+".AlphaImageLoader(src="+n+", SizingMethod=scale)",W.filter=U,b.style.zIndex=B(this.zlevel,this.z,this.z2),I(t,b),null!=i.text&&this.drawRectText(t,this.getBoundingRect())}},d.prototype.onRemove=function(t){F(t,this._vmlEl),this._vmlEl=null,this._cropEl=null,this._imageEl=null,this.removeRectText(t)},d.prototype.onAdd=function(t){I(t,this._vmlEl),this.appendRectText(t)};var X,U="normal",$={},G=0,Q=100,K=document.createElement("div"),J=function(t){var e=$[t];if(!e){G>Q&&(G=0,$={});var r,i=K.style;try{i.font=t,r=i.fontFamily.split(",")[0]}catch(n){}e={style:i.fontStyle||U,variant:i.fontVariant||U,weight:i.fontWeight||U,size:0|parseFloat(i.fontSize||12),family:r||"Microsoft YaHei"},$[t]=e,G++}return e};l.$override("measureText",(function(t,e){var r=_.doc;X||(X=r.createElement("div"),X.style.cssText="position:absolute;top:-20000px;left:0;padding:0;margin:0;border:none;white-space:pre;",_.doc.body.appendChild(X));try{X.style.font=e}catch(i){}return X.innerHTML="",X.appendChild(r.createTextNode(t)),{width:X.offsetWidth}}));for(var tt=new s,et=function(t,e,r,i){var n=this.style;this.__dirty&&u.normalizeTextStyle(n,!0);var a=n.text;if(null!=a&&(a+=""),a){if(n.rich){var s=l.parseRichText(a,n);a=[];for(var h=0;h<s.lines.length;h++){for(var c=s.lines[h].tokens,f=[],d=0;d<c.length;d++)f.push(c[d].text);a.push(f.join(""))}a=a.join("\n")}var p,v,g=n.textAlign,y=n.textVerticalAlign,m=J(n.font),w=m.style+" "+m.variant+" "+m.weight+" "+m.size+'px "'+m.family+'"';r=r||l.getBoundingRect(a,w,g,y,n.textPadding,n.textLineHeight);var b=this.transform;if(b&&!i&&(tt.copy(e),tt.applyTransform(b),e=tt),i)p=e.x,v=e.y;else{var k=n.textPosition;if(k instanceof Array)p=e.x+E(k[0],e.width),v=e.y+E(k[1],e.height),g=g||"left";else{var T=this.calculateTextPosition?this.calculateTextPosition({},n,e):l.calculateTextPosition({},n,e);p=T.x,v=T.y,g=g||T.textAlign,y=y||T.textVerticalAlign}}p=l.adjustTextX(p,r.width,g),v=l.adjustTextY(v,r.height,y),v+=r.height/2;var S,P,A,M=_.createNode,L=this._textVmlEl;L?(A=L.firstChild,S=A.nextSibling,P=S.nextSibling):(L=M("line"),S=M("path"),P=M("textpath"),A=M("skew"),P.style["v-text-align"]="left",z(L),S.textpathok=!0,P.on=!0,L.from="0 0",L.to="1000 0.05",I(L,A),I(L,S),I(L,P),this._textVmlEl=L);var D=[p,v],R=L.style;b&&i?(o(D,D,b),A.on=!0,A.matrix=b[0].toFixed(3)+C+b[2].toFixed(3)+C+b[1].toFixed(3)+C+b[3].toFixed(3)+",0,0",A.offset=(x(D[0])||0)+","+(x(D[1])||0),A.origin="0 0",R.left="0px",R.top="0px"):(A.on=!1,R.left=x(p)+"px",R.top=x(v)+"px"),P.string=O(a);try{P.style.font=w}catch(F){}W(L,"fill",{fill:n.textFill,opacity:n.opacity},this),W(L,"stroke",{stroke:n.textStroke,opacity:n.opacity,lineDash:n.lineDash||null},this),L.style.zIndex=B(this.zlevel,this.z,this.z2),I(t,L)}},rt=function(t){F(t,this._textVmlEl),this._textVmlEl=null},it=function(t){I(t,this._textVmlEl)},nt=[c,f,d,v,p],at=0;at<nt.length;at++){var ot=nt[at].prototype;ot.drawRectText=et,ot.removeRectText=rt,ot.appendRectText=it}p.prototype.brushVML=function(t){var e=this.style;null!=e.text?this.drawRectText(t,{x:e.x||0,y:e.y||0,width:0,height:0},this.getBoundingRect(),!0):this.removeRectText(t)},p.prototype.onRemove=function(t){this.removeRectText(t)},p.prototype.onAdd=function(t){this.appendRectText(t)}}},39607:function(t,e,r){r(3861);var i=r(89519),n=i.registerPainter,a=r(39544);n("vml",a)},89519:function(t,e,r){var i=r(26469),n=r(95160),a=r(63122),o=r(58263),s=r(92494),h=r(35960),l=r(14204),u=r(48107),c=!n.canvasSupported,f={canvas:h},d={},p="4.3.2";function v(t,e){var r=new x(i(),t,e);return d[r.id]=r,r}function g(t){if(t)t.dispose();else{for(var e in d)d.hasOwnProperty(e)&&d[e].dispose();d={}}return this}function y(t){return d[t]}function _(t,e){f[t]=e}function m(t){delete d[t]}var x=function(t,e,r){r=r||{},this.dom=e,this.id=t;var i=this,h=new s,d=r.renderer;if(c){if(!f.vml)throw new Error("You need to require 'zrender/vml/vml' to support IE8");d="vml"}else d&&f[d]||(d="canvas");var p=new f[d](e,h,r,t);this.storage=h,this.painter=p;var v=n.node||n.worker?null:new u(p.getViewportRoot(),p.root);this.handler=new o(h,p,v,p.root),this.animation=new l({stage:{update:a.bind(this.flush,this)}}),this.animation.start(),this._needsRefresh;var g=h.delFromStorage,y=h.addToStorage;h.delFromStorage=function(t){g.call(h,t),t&&t.removeSelfFromZr(i)},h.addToStorage=function(t){y.call(h,t),t.addSelfToZr(i)}};x.prototype={constructor:x,getId:function(){return this.id},add:function(t){this.storage.addRoot(t),this._needsRefresh=!0},remove:function(t){this.storage.delRoot(t),this._needsRefresh=!0},configLayer:function(t,e){this.painter.configLayer&&this.painter.configLayer(t,e),this._needsRefresh=!0},setBackgroundColor:function(t){this.painter.setBackgroundColor&&this.painter.setBackgroundColor(t),this._needsRefresh=!0},refreshImmediately:function(){this._needsRefresh=this._needsRefreshHover=!1,this.painter.refresh(),this._needsRefresh=this._needsRefreshHover=!1},refresh:function(){this._needsRefresh=!0},flush:function(){var t;this._needsRefresh&&(t=!0,this.refreshImmediately()),this._needsRefreshHover&&(t=!0,this.refreshHoverImmediately()),t&&this.trigger("rendered")},addHover:function(t,e){if(this.painter.addHover){var r=this.painter.addHover(t,e);return this.refreshHover(),r}},removeHover:function(t){this.painter.removeHover&&(this.painter.removeHover(t),this.refreshHover())},clearHover:function(){this.painter.clearHover&&(this.painter.clearHover(),this.refreshHover())},refreshHover:function(){this._needsRefreshHover=!0},refreshHoverImmediately:function(){this._needsRefreshHover=!1,this.painter.refreshHover&&this.painter.refreshHover()},resize:function(t){t=t||{},this.painter.resize(t.width,t.height),this.handler.resize()},clearAnimation:function(){this.animation.clear()},getWidth:function(){return this.painter.getWidth()},getHeight:function(){return this.painter.getHeight()},pathToImage:function(t,e){return this.painter.pathToImage(t,e)},setCursorStyle:function(t){this.handler.setCursorStyle(t)},findHover:function(t,e){return this.handler.findHover(t,e)},on:function(t,e,r){this.handler.on(t,e,r)},off:function(t,e){this.handler.off(t,e)},trigger:function(t,e){this.handler.trigger(t,e)},clear:function(){this.storage.delRoot(),this.painter.clear()},dispose:function(){this.animation.stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this.animation=this.storage=this.painter=this.handler=null,m(this.id)}},e.version=p,e.init=v,e.dispose=g,e.getInstance=y,e.registerPainter=_}}]);
(typeof window=='undefined'?global:window).tc_cfg_09272470305613778={"url":"css/theme-colors-ea095f1b.css","colors":["#1b65b9","#3274c0","#4984c7","#5f93ce","#76a3d5","#8db2dc","#a4c1e3","#bbd1ea","#d1e0f1","#ebf4fa","#bbd9ed","#8dbce0","#639fd4","#3e82c7","#1b65b9","#0f4894","#052f6e","#001a47","#000b21","27,101,185"]};

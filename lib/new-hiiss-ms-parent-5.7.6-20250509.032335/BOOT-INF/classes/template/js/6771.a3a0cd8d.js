(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[6771],{88412:function(t,e,a){"use strict";var l=a(26263),o=a(36766),i=a(1001),s=(0,i.Z)(o.Z,l.s,l.x,!1,null,"5e7ef0ae",null);e["Z"]=s.exports},8056:function(t,e,a){"use strict";a.r(e),a.d(e,{default:function(){return x}});var l=function(){var t=this,e=this,a=e.$createElement,l=e._self._c||a;return l("div",{staticClass:"fit"},[l("ta-border-layout",{staticClass:"fit",attrs:{"footer-cfg":{showBorder:!1},layout:{footer:"55px"},"show-padding":!1}},[l("div",{staticClass:"fit"},[l("ta-card",{attrs:{"show-expand":!1}},[l("ta-title",{attrs:{title:"查询条件"}}),l("ta-form",{attrs:{"auto-form-create":function(e){return t.form=e},"form-layout":!0,layout:"horizontal"}},[l("ta-form-item",{attrs:{"field-decorator-options":{initialValue:e.defaultStartDate},"label-col":{span:8},require:{message:"必输项!"},span:6,"wrapper-col":{span:15},"field-decorator-id":"start"}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("结算日期开始")]),l("ta-date-picker",{staticStyle:{width:"100%"},attrs:{"disabled-date":e.disabledStartDate}})],1),l("ta-form-item",{attrs:{"field-decorator-options":{initialValue:e.defaultEndDate},"label-col":{span:8},require:{message:"必输项!"},span:6,"wrapper-col":{span:15},"field-decorator-id":"end"}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("结算日期结束")]),l("ta-date-picker",{staticStyle:{width:"100%"},attrs:{"disabled-date":e.disabledEndDate}})],1),l("ta-form-item",{attrs:{require:{message:"必输项!"},"label-col":{span:8},span:6,"wrapper-col":{span:15},"field-decorator-id":"aka130","init-value":e.CollectionData("AKA130ZK")&&e.CollectionData("AKA130ZK").length>0?e.CollectionData("AKA130ZK")[0].value:""}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("医疗类型")]),l("ta-select",{attrs:{"allow-clear":!1,"collection-type":"AKA130ZK"}})],1),l("ta-form-item",{attrs:{"label-col":{span:8},span:6,"wrapper-col":{span:15},"field-decorator-id":"aae141"}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("医保类型")]),l("ta-select",{attrs:{"allow-clear":!0,"collection-type":"AAE141ZK"}})],1),l("ta-form-item",{staticStyle:{"margin-top":"-10px"},attrs:{"label-col":{span:8},span:6,"wrapper-col":{span:15},hidden:!e.hiddenCX2,"field-decorator-id":"aae140"}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("险种类型")]),l("ta-select",{attrs:{"allow-clear":!0,"collection-type":"AAE140ZK"}})],1),l("ta-form-item",{staticStyle:{"margin-top":"-10px"},attrs:{hidden:!e.hiddenCX2,"label-col":{span:8},span:6,"wrapper-col":{span:15},"field-decorator-id":"aaz307"}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("科室")]),l("ta-select",{attrs:{"allow-clear":!0,"default-value":1,disabled:e.aaz307Status,options:e.aaz307Data,"show-search":!0}})],1),l("ta-form-item",{staticStyle:{"margin-top":"-10px"},attrs:{hidden:!e.hiddenCX2,"label-col":{span:8},span:6,"wrapper-col":{span:15},"field-decorator-id":"akc190"}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("患者名称")]),l("ta-auto-complete",{attrs:{"dropdown-match-select-width":!1,"option-label-prop":"text",placeholder:"患者编码或名称"},on:{search:e.handleSearchHz,select:e.handleSelectHz}},[l("template",{slot:"default"},[l("ta-input",{staticStyle:{height:"32px"}})],1),l("template",{slot:"dataSource"},e._l(e.hzDataSource,(function(t){return l("ta-select-option",{key:t.id,attrs:{text:t.name}},[l("span",{staticStyle:{display:"width: 25%"}},[e._v(e._s(t.id))]),l("span",{staticStyle:{display:"width: 25%"}},[e._v(e._s(t.name))])])})),1)],2)],1),l("ta-form-item",{staticStyle:{"margin-top":"-10px"},attrs:{hidden:!e.hiddenCX2,"label-col":{span:8},span:6,"wrapper-col":{span:15},"field-decorator-id":"aaz263"}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("医师名称")]),l("ta-auto-complete",{attrs:{"dropdown-match-select-width":!1,"option-label-prop":"text",placeholder:"医师编码或名称"},on:{search:e.handleSearchYs,select:e.handleSelectYs}},[l("template",{slot:"default"},[l("ta-input",{staticStyle:{height:"32px"}})],1),l("template",{slot:"dataSource"},e._l(e.ysDataSource,(function(t){return l("ta-select-option",{key:t.id,attrs:{text:t.name}},[l("span",{staticStyle:{display:"width: 25%"}},[e._v(e._s(t.id))]),l("span",{staticStyle:{display:"width: 25%"}},[e._v(e._s(t.name))])])})),1)],2)],1),l("div",{staticStyle:{float:"right","margin-top":"-10px"}},[l("ta-button",{staticStyle:{float:"right","margin-right":"15px"},attrs:{hidden:e.hiddenCX2,icon:"down"},on:{click:function(t){return e.handleChangeScrollY(1)}}},[e._v(" 展开 ")]),l("ta-button",{staticStyle:{float:"right","margin-right":"15px"},attrs:{hidden:!e.hiddenCX2,icon:"up"},on:{click:function(t){return e.handleChangeScrollY(2)}}},[e._v(" 收起 ")]),l("ta-button",{staticStyle:{float:"right"},attrs:{icon:"search",type:"primary"},on:{click:e.handleQueryByPage}},[e._v(" 查询 ")])],1)],1)],1),l("div",{style:{height:e.divHeight}},[l("ta-title",{staticStyle:{"margin-left":"10px"},attrs:{title:"结算汇总信息"}}),l("ta-checkbox",{attrs:{checked:e.ischecked},on:{change:e.onChange}},[e._v(" 科室 ")]),l("ta-checkbox",{on:{change:e.onChange2}},[e._v(" 医生 ")]),l("ta-checkbox",{on:{change:e.onChange3}},[e._v(" 病人 ")]),l("ta-table",{ref:"hiddenTable",attrs:{bordered:!0,columns:e.tableColumns,"custom-row":e.fnCustomRow1,"data-source":e.tableData,"have-sn":{fixed:!0},scroll:{x:2200,y:"100%"},size:"small"},on:{"update:columns":function(t){e.tableColumns=t}}})],1)],1),l("div",{attrs:{slot:"footer"},slot:"footer"},[l("ta-button",{staticStyle:{position:"absolute",right:"10px",bottom:"15px"},attrs:{type:"primary",icon:"download"},on:{click:function(t){return e.handleExportData(2)}}},[e._v(" 导出 ")]),l("ta-pagination",{ref:"gridPager",staticStyle:{float:"right","margin-top":"8px","margin-right":"120px"},attrs:{"data-source":e.tableData,"default-page-size":10,"page-size-options":["10","20","40"],params:e.pageParams,"show-quick-jumper":"","show-size-changer":"",url:"settlementDataDetail/queryDetail"},on:{"update:dataSource":function(t){e.tableData=t},"update:data-source":function(t){e.tableData=t}}})],1)])],1)},o=[],i=a(66347),s=a(95082),n=a(36797),r=a.n(n),d=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("ta-button",[t._v(t._s(t.buttonKey))])},c=[],h={name:"partComponent",props:{buttonKey:{type:String}},data:function(){return{}}},u=h,f=a(1001),p=(0,f.Z)(u,d,c,!1,null,"2b0d4602",null),m=p.exports,g=a(9063),C=a(88412),w=a(22722),b=a(55115);b.w3.prototype.Base=Object.assign(b.w3.prototype.Base,(0,s.Z)({},w.Z));var y={components:{TaFormItem:g.Z,partComponent:m,TaTitle:C.Z},data:function(){var t=[{title:"科室名称",align:"left",dataIndex:"aae386",width:80,fixed:"left",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"医生姓名",align:"left",dataIndex:"aaz570",width:80,fixed:"left",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"患者姓名",align:"left",dataIndex:"aac003",width:80,fixed:"left",scopedSlots:{customRender:"name"},overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"医保类型",align:"center",dataIndex:"aae141",width:80,fixed:"left",collectionType:"AAE141ZK",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"险种",align:"center",dataIndex:"aae140",width:80,fixed:"left",collectionType:"AAE140ZK",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"住院人次",align:"right",dataIndex:"zyrc",width:80,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"总费用",align:"right",dataIndex:"akc264",width:80,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"费用次均",align:"right",dataIndex:"fycj",width:80,overflowTooltip:!0,scopedSlots:{customRender:"fycj"},customHeaderCell:this.fnCustomHeaderCell},{title:"医保支付总费用",align:"right",dataIndex:"yke032",width:120,overflowTooltip:!0,scopedSlots:{customRender:"yke032"},customHeaderCell:this.fnCustomHeaderCell},{title:"医保支付次均费用",align:"right",dataIndex:"ybcj",width:140,overflowTooltip:!0,scopedSlots:{customRender:"ybcj"},customHeaderCell:this.fnCustomHeaderCell},{title:"统筹支付费用",align:"right",dataIndex:"ake039",overflowTooltip:!0,width:120,scopedSlots:{customRender:"ake039"},customHeaderCell:this.fnCustomHeaderCell},{title:"统筹支付次均费用",align:"right",dataIndex:"tccj",width:140,overflowTooltip:!0,scopedSlots:{customRender:"tccj"},customHeaderCell:this.fnCustomHeaderCell},{title:"自费费用",align:"right",dataIndex:"akc253",width:90,overflowTooltip:!0,scopedSlots:{customRender:"akc253"},customHeaderCell:this.fnCustomHeaderCell},{title:"自费占比%",align:"right",dataIndex:"zfb",width:90,overflowTooltip:!0,scopedSlots:{customRender:"zfb"},customHeaderCell:this.fnCustomHeaderCell},{title:"自费占比阈值",align:"right",dataIndex:"zfbyj",width:110,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"药品费",align:"right",dataIndex:"akc301",width:60,overflowTooltip:!0,scopedSlots:{customRender:"akc301"},customHeaderCell:this.fnCustomHeaderCell},{title:"药品费占比%",align:"right",dataIndex:"yzb",width:110,overflowTooltip:!0,scopedSlots:{customRender:"yzb"},customHeaderCell:this.fnCustomHeaderCell},{title:"药品费占比阈值",align:"right",dataIndex:"yzbyj",width:120,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"诊疗费",align:"right",dataIndex:"akc302",width:80,overflowTooltip:!0,scopedSlots:{customRender:"akc302"},customHeaderCell:this.fnCustomHeaderCell},{title:"诊疗费占比%",align:"right",dataIndex:"zlzb",width:100,overflowTooltip:!0,scopedSlots:{customRender:"zlzb"},customHeaderCell:this.fnCustomHeaderCell},{title:"材料费",align:"right",dataIndex:"akc304",width:60,overflowTooltip:!0,scopedSlots:{customRender:"akc304"},customHeaderCell:this.fnCustomHeaderCell},{title:"材料费占比%",align:"right",dataIndex:"clzb",width:100,overflowTooltip:!0,scopedSlots:{customRender:"clzb"},customHeaderCell:this.fnCustomHeaderCell},{title:"材料费占比阈值",align:"right",dataIndex:"clzbyj",width:120,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"服务设施费",align:"right",dataIndex:"akc303",width:100,overflowTooltip:!0,scopedSlots:{customRender:"akc303"},customHeaderCell:this.fnCustomHeaderCell},{title:"服务设施费占比%",align:"right",dataIndex:"fwzb",width:130,overflowTooltip:!0,scopedSlots:{customRender:"fwzb"},customHeaderCell:this.fnCustomHeaderCell},{title:"其他费用",align:"right",dataIndex:"akc305",width:80,overflowTooltip:!0,scopedSlots:{customRender:"akc305"},customHeaderCell:this.fnCustomHeaderCell}],e=[],a=[{label:"科室",value:"0"},{label:"医生",value:"1"},{label:"病人",value:"2"}];return{defaultStartDate:this.Base.getMoment((new Date).toISOString().slice(0,8)+"-01","YYYY-MM-DD"),defaultEndDate:this.Base.getMoment((new Date).toISOString().slice(0,10),"YYYY-MM-DD"),tableColumns:t,tableData:e,aaz307Data:[],aaz307Status:!1,hzDataSource:[],ysDataSource:[],role:1,akb020:"",akc190:"",start:"",lastHzAutoSearchTime:"",lastYsAutoSearchTime:"",visible:!1,indeterminate:!1,checkAll:!1,plainOptions:a,checkedList:[],mxid:"",hiddenCX2:!1,divHeight:"calc(100% - 190px)",selects:"1",showKs:"1",showYs:"0",showBr:"0",ischecked:!0}},watch:{},mounted:function(){},methods:{onChange:function(t){t.target.checked?(this.$refs.hiddenTable.showColumns(["aae386"]),this.showKs="1",this.ischecked=!0):(this.$refs.hiddenTable.hideColumns(["aae386"]),this.showKs="0",this.ischecked=!1),this.handleQueryByPage()},onChange2:function(t){t.target.checked?(this.$refs.hiddenTable.showColumns(["aaz570"]),this.showYs="1"):(this.$refs.hiddenTable.hideColumns(["aaz570"]),this.showYs="0"),this.handleQueryByPage()},onChange3:function(t){t.target.checked?(this.$refs.hiddenTable.showColumns(["aac003"]),this.showBr="1"):(this.$refs.hiddenTable.hideColumns(["aac003"]),this.showBr="0"),this.handleQueryByPage()},fnCustomHeaderCell:function(t){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px",fontWeight:"bold"}}},fnCustomRow1:function(t,e){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px"}}},handleChangeScrollY:function(t){1==t?(this.hiddenCX2=!0,this.divHeight="calc(100% - 250px)"):(this.hiddenCX2=!1,this.divHeight="calc(100% - 190px)")},disabledStartDate:function(t){return t.format("YYYYMMDD")>this.form.getFieldValue("end").format("YYYYMMDD")},disabledEndDate:function(t){return t.format("YYYYMMDD")>r()().startOf("day").format("YYYYMMDD")||t.format("YYYYMMDD")<this.form.getFieldValue("start").format("YYYYMMDD")},pageParams:function(){var t=(0,s.Z)({},this.form.getFieldsValue());return t.start=t.start.format("YYYY-MM-DD"),t.end=t.end.format("YYYY-MM-DD"),t.akb020=this.akb020,t.showKs=this.showKs,t.showYs=this.showYs,t.showBr=this.showBr,t},handleQueryByPage:function(){this.$refs.gridPager.loadData((function(t){}))},fnInitData:function(){var t=this;2===this.role?this.aaz307Status=!0:this.aaz307Status=!1,null!=this.$route.query.akc190&&""!=this.$route.query.akc190&&this.form.setFieldsValue({akb020:this.$route.query.akb020,yke099:this.$route.query.akc190,start:this.Base.getMoment(this.$route.query.start,"YYYY-MM-DD")}),this.aaz307Data=[],this.form.setFieldsValue({aaz307:""}),this.Base.submit(null,{url:"settlementDataDetail/initData",data:{role:this.role},autoValid:!0},{successCallback:function(e){t.akb020=e.data.akb020,t.aaz307Data=e.data.aaz307Data,t.aaz307Data.length>0&&2===t.role&&t.form.setFieldsValue({aaz307:t.aaz307Data[0].value}),t.$refs.gridPager.loadData((function(t){})),t.$refs.hiddenTable.hideColumns(["aaz570","aac003"])},failCallback:function(t){}})},handleSearchHz:function(t){var e=this;""!=this.lastHzAutoSearchTime&&(new Date).getTime()-this.lastHzAutoSearchTime<200||(this.lastHzAutoSearchTime=(new Date).getTime(),this.Base.submit(null,{url:"settlementDataDetail/queryHzIdAndName",data:{aac003:t,aaz307:this.form.getFieldValue("aaz307"),akb020:this.akb020},autoValid:!0},{successCallback:function(t){e.hzDataSource=t.data.hzDataSource},failCallback:function(t){}}))},handleSelectHz:function(t){},handleSearchYs:function(t){var e=this;""!==this.lastYsAutoSearchTime&&(new Date).getTime()-this.lastYsAutoSearchTime<200||(this.lastYsAutoSearchTime=(new Date).getTime(),this.Base.submit(null,{url:"settlementDataDetail/queryYsIdAndName",data:{aaz263:t,aaz307:this.form.getFieldValue("aaz307"),akb020:this.akb020},autoValid:!0},{successCallback:function(t){e.ysDataSource=t.data.ysDataSource},failCallback:function(t){}}))},handleSelectYs:function(t){},handleExportData:function(t){if(2!=t){var e,a=[],l=this.tableColumns,o=this.tableData,n=(0,i.Z)(l);try{for(n.s();!(e=n.n()).done;){var r=e.value;a.push({header:r.title,key:r.dataIndex,width:20})}}catch(c){n.e(c)}finally{n.f()}var d={fileName:"疑点",sheets:[{name:"worksheet1",column:{complex:!1,columns:a},rows:o,codeList:[{codeType:"APE893",columnKey:"ape893"}]}]};this.Base.generateExcel(d)}else Base.downloadFile({url:"ydmx/exportData",options:(0,s.Z)({},this.pageParams()),type:"application/excel",fileName:"疑点数据.xls"}).then((function(t){})).catch((function(t){}))},getModalContainer:function(){return document.getElementById("info")}}},S=y,v=(0,f.Z)(S,l,o,!1,null,"bd5b4352",null),x=v.exports},36766:function(t,e,a){"use strict";var l=a(66586);e["Z"]=l.Z},26263:function(t,e,a){"use strict";a.d(e,{s:function(){return l},x:function(){return o}});var l=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},o=[]},66586:function(t,e){"use strict";var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};e["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}},55382:function(){},61219:function(){}}]);
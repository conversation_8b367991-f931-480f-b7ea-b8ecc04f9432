"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[9883],{79883:function(t,a,e){e.r(a),e.d(a,{default:function(){return d}});var n=function(){var t=this,a=this,e=a.$createElement,n=a._self._c||e;return n("div",{staticStyle:{padding:"20px"}},[n("div",{staticStyle:{"margin-bottom":"20px","border-bottom":"1px solid #eee"}},[n("ta-row",[n("span",{staticStyle:{"font-size":"16px","font-weight":"600"}},[a._v("同步测试")])]),n("ta-row",[n("ta-label-con",{attrs:{label:"设置下拉框数据量"}},[n("ta-input-number",{attrs:{precision:0,min:1},model:{value:a.dataLenth,callback:function(t){a.dataLenth=t},expression:"dataLenth"}}),n("ta-button",{attrs:{type:"primary"},on:{click:function(t){return a.setData(a.dataLenth)}}},[a._v(" 确认 ")])],1)],1),n("ta-row",[n("ta-label-con",{attrs:{label:"是否开启虚拟滚动"}},[n("ta-radio-group",{attrs:{value:a.virtual},on:{change:a.handleVirtualChange}},[n("ta-radio-button",{attrs:{value:!0}},[a._v(" 是 ")]),n("ta-radio-button",{attrs:{value:!1}},[a._v(" 否 ")])],1)],1)],1),n("ta-row",[n("ta-label-con",{attrs:{label:"组件示例"}},[n("ta-tree-select",{staticStyle:{width:"300px"},attrs:{"dropdown-style":{maxHeight:"400px",overflow:"auto"},"filter-tree-node":a.fnFilterTreeNode,"show-checked-strategy":"SHOW_ALL","tree-data":a.treeData,size:"large",virtual:a.virtual},on:{"update:treeData":function(t){a.treeData=t},"update:tree-data":function(t){a.treeData=t}}})],1)],1)],1),n("div",[n("ta-row",[n("span",{staticStyle:{"font-size":"16px","font-weight":"600"}},[a._v("异步测试")])]),n("ta-row",[n("ta-label-con",{attrs:{label:"单级数据量"}},[n("ta-input",{staticStyle:{width:"200px"},model:{value:a.dataAsynChildLenth,callback:function(t){a.dataAsynChildLenth=t},expression:"dataAsynChildLenth"}}),n("ta-button",{attrs:{type:"primary"},on:{click:a.setDataLen}},[a._v(" 确认 ")])],1)],1),n("ta-row",[n("ta-label-con",{attrs:{label:"是否开启虚拟滚动"}},[n("ta-radio-group",{attrs:{value:a.asynVirtual},on:{change:a.handleVirtualChangeAsyn}},[n("ta-radio-button",{attrs:{value:!0}},[a._v(" 是 ")]),n("ta-radio-button",{attrs:{value:!1}},[a._v(" 否 ")])],1)],1)],1),n("ta-row",[n("ta-form",{attrs:{"auto-form-create":function(a){return t.form=a}}},[n("ta-form-item",{attrs:{"field-decorator-id":"tree-select1",label:"下拉树1"}},[n("ta-tree-select",{staticStyle:{width:"300px"},attrs:{"dropdown-style":{maxHeight:"400px",overflow:"auto"},"show-checked-strategy":"SHOW_ALL","tree-data":a.treeDataAsyn,size:"large","load-data":a.fnLoad,virtual:a.asynVirtual},on:{"update:treeData":function(t){a.treeDataAsyn=t},"update:tree-data":function(t){a.treeDataAsyn=t}}})],1)],1)],1)],1)])},r=[],l=(new Date).valueOf(),o=Array.from({length:20}).map((function(t,a){return{label:"节点"+a,value:l+""+a,key:l+""+a,ff:"n2"}})),i={name:"treeSelectTest",data:function(){return{treeSelectValue:void 0,treeData:o,defaultValue:["0-0-1"],defaultExpandKeys:["0-0","0-0-1","0-0-2"],allowClear:!1,dataLenth:20,virtual:!0,asynVirtual:!0,treeDataAsyn:[],i:0,dataAsynChildLenth:10}},methods:{fnTreeSelectChange:function(t,a,e){},fnTreeSelectSelect:function(t,a,e){},fnTreeSelectSearch:function(t){},fnFilterTreeNode:function(t,a){return-1!==a.componentOptions.propsData.title.indexOf(t)},fnLoad:function(t){var a=this,e="http/mock/projectDemo/bigdataTest/treeSelect";return new Promise((function(n){Base.submit(null,{url:e,data:{orgId:t?t.data.key:"",dataLength:a.dataAsynChildLenth}},{successCallback:function(t){var a=t.data;n(a)}})}))},setData:function(t){this.treeData=[],this.treeData=Array.from({length:t}).map((function(t,a){return{label:"节点"+a,value:l+""+a,key:l+""+a,ff:"n2",children:[{label:"子节点"+a,value:"子节点"+a,key:"子节点"+a,ff:"n2"}]}}))},handleVirtualChange:function(t){this.virtual=t.target.value},handleVirtualChangeAsyn:function(t){this.asynVirtual=t.target.value},setDataLen:function(){this.fnLoad("")}}},u=i,s=e(1001),c=(0,s.Z)(u,n,r,!1,null,"9a92dec6",null),d=c.exports}}]);
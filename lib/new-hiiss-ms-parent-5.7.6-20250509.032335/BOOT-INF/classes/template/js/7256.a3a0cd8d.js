(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[7256],{88412:function(t,e,a){"use strict";var r=a(26263),o=a(36766),n=a(1001),i=(0,n.Z)(o.Z,r.s,r.x,!1,null,"5e7ef0ae",null);e["Z"]=i.exports},12852:function(t,e,a){"use strict";a.r(e),a.d(e,{default:function(){return w}});var r=function(){var t=this,e=this,a=e.$createElement,r=e._self._c||a;return r("div",{staticClass:"fit"},[r("ta-border-layout",{attrs:{layout:{header:"110px",footer:"0px"},showPadding:!0,"footer-cfg":{showBorder:!1}}},[r("div",{attrs:{slot:"header"},slot:"header"},[r("ta-title",{attrs:{title:"查询条件"}}),r("ta-form",{staticStyle:{"padding-top":"5px"},attrs:{autoFormCreate:function(e){t.baseInfoForm=e},layout:"horizontal",formLayout:!0}},[r("ta-form-item",{attrs:{span:5,fieldDecoratorId:"allDate",fieldDecoratorOptions:{rules:[{required:!0,message:"必须选取日期"}]},required:!0,"init-value":e.rangeValue,labelCol:{span:6},wrapperCol:{span:18},label:"出院日期"}},[r("ta-range-picker",{attrs:{"allow-one":!0},on:{change:e.onChange}})],1),r("ta-form-item",{attrs:{"field-decorator-id":"aaz307",span:4,labelCol:{span:6},wrapperCol:{span:12},virtual:!0,label:"科室名称"}},[r("ta-select",{staticStyle:{width:"120%"},attrs:{showSearch:"","show-search":!0,allowClear:"",placeholder:"科室名称筛选",options:e.ksList}})],1),r("ta-button",{staticStyle:{float:"right"},attrs:{type:"primary",icon:"search"},on:{click:e.fnQuery}},[e._v("查询 ")])],1)],1),r("div",{staticClass:"fit",staticStyle:{height:"89%"}},[r("ta-title",{staticStyle:{width:"50%"},attrs:{title:"查询结果"}}),r("ta-big-table",{ref:"Table",attrs:{height:"100%","show-footer":"","footer-method":e.footerMethod,"highlight-hover-row":"","highlight-current-row":"","tooltip-config":{enabled:!0,contentMethod:e.showTooltipMethod},"auto-resize":"","show-overflow":"",resizable:"","empty-text":"-",border:"",data:e.dataList},on:{"cell-click":e.cellClickEvent}},[r("ta-big-table-column",{attrs:{type:"seq","header-align":"center",align:"center",title:"序号",width:"50",sortable:""}}),r("ta-big-table-column",{attrs:{sortable:"",field:"aaa167","header-align":"aaz319",align:"left",title:"规则分类","min-width":"220"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[r("ta-button",{attrs:{size:"small",type:"link"}},[r("span",{staticStyle:{color:"#00bcff","text-decoration":"underline"}},[e._v(e._s(a.aaa167))])])]}}])}),r("ta-big-table-column",{attrs:{sortable:"",field:"ykz018","header-align":"center",align:"left",title:"违规内容","min-width":"120"}}),r("ta-big-table-column",{attrs:{sortable:"",field:"wgzrc","header-align":"center",align:"right",title:"异常人数(人次)","min-width":"180"}}),r("ta-big-table-column",{attrs:{sortable:"",field:"shzrc","header-align":"center",align:"right",title:"总人数(人次)","min-width":"120"}}),r("ta-big-table-column",{attrs:{sortable:"",field:"hgl","header-align":"center",align:"right",formatter:e.ratioFormat,title:"合格率(%)","min-width":"100"}}),r("ta-big-table-column",{attrs:{sortable:"",field:"wgl","header-align":"center",align:"right",formatter:e.ratioFormat,title:"异常率(%)","min-width":"100"}}),r("ta-big-table-column",{attrs:{sortable:"",field:"ycrc","header-align":"center",align:"right",title:"异常人次(人次)","min-width":"180"}}),r("ta-big-table-column",{attrs:{sortable:"",field:"ycje","header-align":"center",align:"right",formatter:e.moneyFormat,title:"异常金额(元)","min-width":"180"}}),r("template",{slot:"bottomBar"},[r("ta-button",{staticStyle:{float:"right","margin-top":"20px"},attrs:{icon:"download",type:"primary"},on:{click:e.exportTable}},[e._v("导出")]),r("ta-pagination",{ref:"gridPager",staticStyle:{float:"right","margin-top":"20px","margin-right":"10px"},attrs:{defaultPageSize:100,pageSizeOptions:["100","200","500","1000","1500"],"data-source":e.dataList,params:e.infoPageParams,url:"NurdscgStat/queryDscgExcCostGZPage"},on:{"update:dataSource":function(t){e.dataList=t},"update:data-source":function(t){e.dataList=t}}})],1)],2)],1)])],1)},o=[],n=a(66347),i=a(48534),s=a(95082),l=(a(36133),a(88412)),c=a(36797),u=a.n(c),f=a(22722),h=a(55115),d=a(88097),g=a(92566);a(55192);h.w3.prototype.Base=Object.assign(h.w3.prototype.Base,(0,s.Z)({},f.Z));var p={name:"dscgExcCostGZ",components:{TaTitle:l.Z},data:function(){return{dataList:[],aaz307List:[],aaz307now:{ycje:0,ycrc:0,shzrc:0,wgzrc:0},ksList:[],rangeValue:[this.Base.getMoment((new Date).toISOString().slice(0,8)+"-01","YYYY-MM-DD"),this.Base.getMoment((new Date).toISOString().slice(0,10),"YYYY-MM-DD")]}},mounted:function(){var t=this;return(0,i.Z)(regeneratorRuntime.mark((function e(){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.$route.query.params&&(a=JSON.parse(t.$route.query.params),a.allDate=a.allDate.map((function(e){var a=new Date(e);return a.setDate(a.getDate()+1),t.Base.getMoment(a.toISOString(),"YYYY-MM-DD")})),t.$route.query.aaz307&&(a.aaz307=t.$route.query.aaz307),t.baseInfoForm.setFieldsValue(a)),t.fnQueryDept(""),t.$nextTick((function(){t.fnQuery()}));case 3:case"end":return e.stop()}}),e)})))()},methods:{moment:u(),onChange:function(t,e){var a=this;this.$nextTick((function(){a.fnQuery()}))},fnQueryDept:function(t){var e=this;this.akb020=t||"";var a="I";this.Base.submit(null,{url:"miimCommonRead/queryDepartRpcList",showPageLoading:!1,data:{akb020:this.akb020,hospDeptType:a},autoValid:!1},{successCallback:function(t){e.$route.query.params||e.baseInfoForm.resetFields("aaz307"),e.ksList=t.data.resultData},failCallback:function(t){e.$message.error("科室数据加载失败")}})},footerMethod:function(t){var e=this,a=t.columns;t.data;return[a.map((function(t,a){var r=e.baseInfoForm.getFieldsValue().aaz307;return 0===a?"合计":["aaa167"].includes(t.property)||["ykz018"].includes(t.property)?null:["hgl"].includes(t.property)?r?0==e.aaz307now.shzrc||0==e.aaz307now.wgzrc?0:(100-parseFloat(0===e.aaz307now.shzrc?0:e.aaz307now.wgzrc/e.aaz307now.shzrc*100)).toFixed(2)+"%":0===(0,g.Z)(e.aaz307List,"shzrc")||0===(0,g.Z)(e.aaz307List,"wgzrc")?0:(100-parseFloat(0===(0,g.Z)(e.aaz307List,"shzrc")?0:(0,g.Z)(e.aaz307List,"wgzrc")/(0,g.Z)(e.aaz307List,"shzrc")*100)).toFixed(2)+"%":["wgl"].includes(t.property)?r?0==e.aaz307now.shzrc||0==e.aaz307now.wgzrc?0:parseFloat(0===e.aaz307now.shzrc?0:e.aaz307now.wgzrc/e.aaz307now.shzrc*100).toFixed(2)+"%":0===(0,g.Z)(e.aaz307List,"shzrc")||0===(0,g.Z)(e.aaz307List,"wgzrc")?0:parseFloat(0===(0,g.Z)(e.aaz307List,"shzrc")?0:(0,g.Z)(e.aaz307List,"wgzrc")/(0,g.Z)(e.aaz307List,"shzrc")*100).toFixed(2)+"%":["wgzrc"].includes(t.property)?r?e.aaz307now.wgzrc:(0,g.Z)(e.aaz307List,t.property):["shzrc"].includes(t.property)?r?e.aaz307now.shzrc:(0,g.Z)(e.aaz307List,t.property):["ycrc"].includes(t.property)?r?e.aaz307now.ycrc:(0,g.Z)(e.aaz307List,t.property):["ycje"].includes(t.property)&&r?e.aaz307now.ycje:(0,g.Z)(e.aaz307List,t.property)}))]},showTooltipMethod:function(t){var e=t.type,a=t.column,r=(t.row,t.items,t._columnIndex,a.property);if("header"===e)return d.Z.props[r]?d.Z.props[r][e]:""},cellClickEvent:function(t){var e=t.row,a=t.column,r=a.property;if("aaa167"===r&&e.aaz319){var o=this.baseInfoForm.getFieldsValue();this.Base.openTabMenu({id:Math.floor(90*Math.random())+10,name:"出院违规患者查询",url:"querycommon.html#/violationQuery?params=".concat(JSON.stringify(o)),refresh:!1})}},exportTable:function(){var t=this.infoPageParams(),e=t.allDate;if(!e||e[0]&&e[1]){var a,r=[],o=this.$refs.Table.getColumns(),i=(0,n.Z)(o);try{for(i.s();!(a=i.n()).done;){var s=a.value;"序号"!==s.title&&r.push({header:s.title,key:s.property,width:20})}}catch(c){i.e(c)}finally{i.f()}var l={fileName:"护士站出院规则异常收费汇总结果表",sheets:[{name:"worksheet1",column:{complex:!1,columns:r},rows:this.dataList,codeList:d.Z.codelist.filter((function(t){return"shsyl"===t.type}))}]};this.Base.generateExcel(l)}else this.$message.error("请选择时间范围！")},fnReset:function(){this.baseInfoForm.resetFields()},infoPageParams:function(){var t=this.baseInfoForm.getFieldsValue();t.startDate=t.allDate[0].format("YYYY-MM-DD HH:mm:ss");var e=new Date("".concat(t.allDate[1].format("YYYY-MM-DD"),"T00:00:00")),a=new Date(e);return a.setDate(e.getDate()+1),a.setHours(0,0,0,0),t.endDate=this.Base.getMoment(a.toISOString()).format("YYYY-MM-DD HH:mm:ss"),t.cjflag="gz",t},fnQuery:function(){var t=this,e=this.baseInfoForm.getFieldValue("allDate");if(!e||e[0]&&e[1]){var a=this.infoPageParams();this.Base.submit(null,{url:"NurdscgStat/exportDscgExcCostKSPage",data:a},{successCallback:function(e){var r=e.data;if(a.aaz307){var o=r.list.filter((function(t){return t.aaz307===a.aaz307}));o.length>0?t.aaz307now=o[0]:t.aaz307now={ycje:0,ycrc:0,shzrc:0,wgzrc:0}}else t.aaz307List=r.list.length>0?r.list:[];t.$nextTick((function(){t.$refs.gridPager.loadData((function(t){}))}))},failCallback:function(e){t.$message.error("查询出院科室统计数据失败")}})}else this.$message.error("请选择时间范围！")},fnCustomHeaderCell:function(t){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px",fontWeight:"bold"}}},filterOption:function(t,e){return e.componentOptions.children[0].text.indexOf(t)>=0},formatAmount:function(t){return t.toFixed(2).replace(/\d(?=(\d{3})+\.)/g,"$&,")},moneyFormat:function(t){var e=t.cellValue;return e&&0!=e?this.formatAmount(e):"0"},ratioFormat:function(t){var e=t.cellValue;return e&&0!=e?e+"%":"0"}}},m=p,z=a(1001),y=(0,z.Z)(m,r,o,!1,null,"40435f87",null),w=y.exports},36766:function(t,e,a){"use strict";var r=a(66586);e["Z"]=r.Z},26263:function(t,e,a){"use strict";a.d(e,{s:function(){return r},x:function(){return o}});var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},o=[]},66586:function(t,e){"use strict";var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};e["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}},88097:function(t,e){"use strict";var a=[{columnKey:"shsyl",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}},{columnKey:"wgl",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}}],r={shsyl:{header:function(){return"物价审核使用率(%)=HIS出院数(人次)/物价审核数(人次)"}},wgl:{header:function(){return"违规率(%)=物价审核违规数(人次)/物价审核数(人次)"}}};e["Z"]={codelist:a,props:r}},55382:function(){},61219:function(){}}]);
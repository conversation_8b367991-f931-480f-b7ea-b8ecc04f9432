"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[9697],{88412:function(t,e,a){var i=a(26263),r=a(36766),l=a(1001),o=(0,l.Z)(r.Z,i.s,i.x,!1,null,"5e7ef0ae",null);e["Z"]=o.exports},39697:function(t,e,a){a.r(e),a.d(e,{default:function(){return v}});var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"fit"},[a("div",{staticStyle:{height:"50%"}},[a("ta-border-layout",{staticClass:"border",attrs:{"show-border":!1,layout:{left:"33.5%",right:"33.5%"}}},[a("div",{attrs:{slot:"left"},slot:"left"},[a("div",{staticStyle:{padding:"30px"}},[a("h1",[t._v("您好，李丽")]),a("h2",[t._v("欢迎使用医保智能监管系统！")])]),a("div",{staticStyle:{"margin-top":"50px",padding:"5px"}},[a("span",{staticStyle:{"text-align":"center",display:"block"}},[t._v("今日")]),a("table",{staticStyle:{width:"100%","margin-top":"10px"},attrs:{cellspacing:"1","text-align":"center"}},[a("tr",{attrs:{height:"55",align:"center"}},[a("td",[t._v("门诊开单")]),a("td",[t._v("100")]),a("td",[t._v("提醒")]),a("td",[t._v("40")]),a("td",[t._v("占比")]),a("td",[t._v("40%")]),a("td",[t._v("较昨日")]),a("td",{staticStyle:{color:"#03fc16"}},[a("ta-icon",{staticStyle:{color:"#03fc16",fontSize:"17px"},attrs:{type:"arrow-down"}}),t._v(" 3.0% ")],1)]),a("tr",{attrs:{height:"55",align:"center"}},[a("td",[t._v("门诊开单")]),a("td",[t._v("100")]),a("td",[t._v("提醒")]),a("td",[t._v("40")]),a("td",[t._v("占比")]),a("td",[t._v("40%")]),a("td",[t._v("较昨日")]),a("td",{staticStyle:{color:"red"}},[a("ta-icon",{staticStyle:{color:"red",fontSize:"17px"},attrs:{type:"arrow-up"}}),t._v(" 5.0% ")],1)]),a("tr",{attrs:{height:"55",align:"center"}},[a("td",[t._v("门诊开单")]),a("td",[t._v("100")]),a("td",[t._v("提醒")]),a("td",[t._v("40")]),a("td",[t._v("占比")]),a("td",[t._v("40%")]),a("td",[t._v("较昨日")]),a("td",{staticStyle:{color:"red"}},[a("ta-icon",{staticStyle:{color:"red",fontSize:"17px"},attrs:{type:"arrow-up"}}),t._v(" 5.0% ")],1)])])])]),a("div",[a("div",{staticStyle:{"border-bottom":"1px solid rgba(153, 153, 153, 0.14)",display:"flex"}},[a("ta-title",{attrs:{title:"每晚预审"}}),a("div",{staticStyle:{"margin-left":"10px","line-height":"28px"}},[a("span",{staticStyle:{color:"#999999"}},[t._v("更新时间：2022-12-07")])]),a("div",{staticStyle:{"margin-left":"auto","line-height":"28px"}},[a("span",{staticStyle:{color:"#999999"}},[t._v("详情 >")])])],1),a("div",[a("div",{attrs:{id:"main"}})])]),a("div",{attrs:{slot:"right"},slot:"right"},[a("div",{staticStyle:{"border-bottom":"1px solid rgba(153, 153, 153, 0.14)",display:"flex"}},[a("ta-title",{attrs:{title:"每晚预审TOP5"}}),a("div",{staticStyle:{"margin-left":"10px","line-height":"28px"}},[a("span",{staticStyle:{color:"#999999"}},[t._v("更新时间：2022-12-07")])])],1),a("table",{staticStyle:{width:"100%","margin-top":"10px","font-size":"18px"},attrs:{cellspacing:"1","text-align":"center"}},[a("tr",{staticStyle:{background:"#f6f6f6","font-weight":"500",color:"#588da4"},attrs:{height:"64",align:"center"}},[a("td",{staticStyle:{"border-radius":"10px 0px 0px 10px"}},[t._v("项目top5")]),a("td",{staticStyle:{"border-radius":"0px 10px 10px 0px"}},[t._v("违反规则top5")])]),a("tr",{attrs:{height:"65",align:"center"}},[a("td",[t._v("帕瑞昔布")]),a("td",[t._v("手术超高频次")])]),a("tr",{staticStyle:{background:"#f2fafc"},attrs:{height:"64",align:"center"}},[a("td",{staticStyle:{"border-radius":"10px 0px 0px 10px"}},[t._v("肺修补术")]),a("td",{staticStyle:{"border-radius":"0px 10px 10px 0px"}},[t._v("二线药品审核")])]),a("tr",{attrs:{height:"65",align:"center"}},[a("td",[t._v("小儿碳酸钙D3颗粒")]),a("td",[t._v("违反药品限制使用条件约束（违规）")])]),a("tr",{staticStyle:{background:"#f2fafc"},attrs:{height:"64",align:"center"}},[a("td",{staticStyle:{"border-radius":"10px 0px 0px 10px"}},[t._v("芪骨胶囊")]),a("td",{staticStyle:{"border-radius":"0px 10px 10px 0px"}},[t._v("违反医保性别限制使用约束（违规)")])]),a("tr",{attrs:{height:"64",align:"center"}},[a("td",[t._v("注射用唑来膦酸")]),a("td",[t._v("违反阶梯用药约束（可疑）")])])])])])],1),a("div",{staticStyle:{height:"50%"}},[a("ta-border-layout",{attrs:{layout:{right:"65.7%"}}},[a("div",[a("div",{staticStyle:{"border-bottom":"1px solid rgba(153, 153, 153, 0.14)",display:"flex"}},[a("ta-title",{attrs:{title:"待办事项"}})],1),a("div",[a("div",[a("div",{staticStyle:{padding:"20px 20px 10px 20px",display:"flex"}},[a("ta-badge",{attrs:{color:"black",text:"每晚预审"}}),a("div",{staticStyle:{"margin-left":"10px","margin-top":"5px"}},[a("ta-icon",{style:{fontSize:"20px",color:"#03fc16"},attrs:{type:"check-circle"}})],1)],1),a("table",{staticStyle:{width:"100%","margin-top":"10px","font-size":"18px"},attrs:{"text-align":"center"}},[a("tr",{staticStyle:{background:"#f6f6f6","font-weight":"500",color:"#588da4"},attrs:{height:"50",align:"center"}},[a("td",{staticStyle:{"border-radius":"5px 0px 0px 5px"}},[t._v("待处理")]),a("td",{staticStyle:{"border-radius":"0px 0px 0px 0px"}},[t._v("已处理")]),a("td",{staticStyle:{"border-radius":"0px 5px 5px 0px"}},[t._v("完成进度")])]),a("tr",{attrs:{height:"50"}},[a("td",{attrs:{align:"center"}},[t._v("0")]),a("td",{attrs:{align:"center"}},[t._v("100")]),a("td",[a("div",{staticStyle:{width:"90%","margin-top":"-6px"}},[a("ta-progress",{attrs:{"stroke-color":{"0%":"#108ee9","100%":"#67c13a"},strokeWidth:13,percent:99.9,status:"active"}})],1)])])])]),a("div",{staticStyle:{"margin-top":"20px"}},[a("div",{staticStyle:{padding:"20px 20px 10px 20px",display:"flex"}},[a("ta-badge",{attrs:{color:"black",text:"申诉管理"}})],1),a("table",{staticStyle:{width:"100%","margin-top":"10px","font-size":"18px"},attrs:{"text-align":"center"}},[a("tr",{staticStyle:{background:"#f6f6f6","font-weight":"500",color:"#588da4"},attrs:{height:"50",align:"center"}},[a("td",{staticStyle:{"border-radius":"5px 0px 0px 5px"}},[t._v("待处理")]),a("td",{staticStyle:{"border-radius":"0px 0px 0px 0px"}},[t._v("已处理")]),a("td",{staticStyle:{"border-radius":"0px 5px 5px 0px"}},[t._v("完成进度")])]),a("tr",{attrs:{height:"50"}},[a("td",{attrs:{align:"center"}},[t._v("0")]),a("td",{attrs:{align:"center"}},[t._v("100")]),a("td",[a("div",{staticStyle:{width:"90%","margin-top":"-6px"}},[a("ta-progress",{attrs:{type:"line",strokeWidth:13,"stroke-color":"#e5a13c",percent:10.1,status:"active"}})],1)])])])])])]),a("div",{attrs:{slot:"right"},slot:"right"},[a("div",{staticStyle:{"border-bottom":"1px solid rgba(153, 153, 153, 0.14)",display:"flex"}},[a("ta-title",{attrs:{title:"医嘱提醒"}}),a("div",{staticStyle:{"margin-left":"auto"}},[a("ta-range-picker",{attrs:{defaultValue:[t.moment("2023/01/01",t.dateFormat),t.moment("2023/01/12",t.dateFormat)],format:t.dateFormat,suffixIcon:" "}})],1)],1),a("div",[a("div",{staticStyle:{float:"left",width:"60%"}},[a("ta-tabs",{attrs:{defaultActiveKey:"1"},on:{change:t.callback}},[a("ta-tab-pane",{key:"1",attrs:{tab:"提醒次数"}},[a("div",{attrs:{id:"main2"}})]),a("ta-tab-pane",{key:"2",attrs:{tab:"提醒金额",forceRender:""}},[a("div",{attrs:{id:"main3"}})])],1)],1),a("div",{staticStyle:{float:"right",width:"40%"}},[a("div",{staticStyle:{padding:"20px 20px 10px 20px",display:"flex"}},[a("ta-badge",{attrs:{color:"black",text:"提醒项目top5"}})],1),a("div",{staticStyle:{"margin-top":"10px"}},[a("div",{staticStyle:{width:"95%",height:"50px",padding:"10px",border:"#e6e6e6 1px solid","border-radius":"5px","line-height":"25px"}},[a("div",{staticStyle:{float:"left"}},[t._v("门诊开单：动静脉置管护理")]),a("div",{staticStyle:{float:"right"}},[t._v("19次")])]),a("div",{staticStyle:{width:"95%",height:"50px",padding:"10px",border:"#e6e6e6 1px solid","border-radius":"5px","line-height":"25px","margin-top":"10px"}},[a("div",{staticStyle:{float:"left"}},[t._v("门特处方：凝血酶时间测定(TT)")]),a("div",{staticStyle:{float:"right"}},[t._v("19次")])]),a("div",{staticStyle:{width:"95%",height:"50px",padding:"10px",border:"#e6e6e6 1px solid","border-radius":"5px","line-height":"25px","margin-top":"10px"}},[a("div",{staticStyle:{float:"left"}},[t._v("住院开单：一次性使用人体静脉血样采集容器-2")]),a("div",{staticStyle:{float:"right"}},[t._v("19次")])]),a("div",{staticStyle:{width:"95%",height:"50px",padding:"10px",border:"#e6e6e6 1px solid","border-radius":"5px","line-height":"25px","margin-top":"10px"}},[a("div",{staticStyle:{float:"left"}},[t._v("住院开单：项目名称")]),a("div",{staticStyle:{float:"right"}},[t._v("19次")])]),a("div",{staticStyle:{width:"95%",height:"50px",padding:"10px",border:"#e6e6e6 1px solid","border-radius":"5px","line-height":"25px","margin-top":"10px"}},[a("div",{staticStyle:{float:"left"}},[t._v("住院开单：项目名称")]),a("div",{staticStyle:{float:"right"}},[t._v("19次")])])])])])])])],1)])},r=[],l=a(88412),o=a(1708),d=a(36797),s=a.n(d),n={name:"assistantDocWork",components:{TaTitle:l.Z},data:function(){return{dateFormat:"YYYY/MM/DD",monthFormat:"YYYY/MM",option:{legend:{}},option2:{legend:{}},option3:{legend:{}}}},mounted:function(){this.asyncLoad(),this.asyncLoad2()},methods:{moment:s(),callback:function(t){},asyncLoad:function(){this.myChart=o.init(document.getElementById("main")),this.myChart.setOption(this.option);var t={tooltip:{trigger:"item",formatter:"{a} <br/>{b} : {c} ({d}%)"},color:["#f6bf2d","#2595fd","#657796","#63d9aa"],legend:{orient:"vertical",left:"65%",top:"33%",y:"center",itemGap:18,textStyle:{fontSize:17},data:["明确违规","高度可疑","轻度可疑","审核通过"],formatter:function(e){for(var a=t.series[0].data,i=(a[0].value,a[1].value,a[2].value,a[3].value,0);i<t.series[0].data.length;i++)if(e==a[i].name)return" "+e+"     "+a[i].value}},series:[{name:"签到比例分析",type:"pie",radius:"45%",center:["35%","50%"],data:[{value:335,name:"明确违规"},{value:310,name:"高度可疑"},{value:234,name:"轻度可疑"},{value:135,name:"审核通过"}],itemStyle:{emphasis:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"},normal:{label:{show:!0,formatter:"{d}%"}},labelLine:{show:!0}}}]};this.myChart.setOption(t)},asyncLoad2:function(){this.myChart=o.init(document.getElementById("main2")),this.myChart.setOption(this.option);var t={tooltip:{trigger:"axis",axisPointer:{type:"cross",label:{backgroundColor:"#6a7985"}}},color:["#2595fd","#657796","#2fc15b"],legend:{icon:"rect",top:0,right:20,itemGap:20,data:["门诊开单","门特处方","住院开单"]},grid:{top:"12%",left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:[{type:"category",boundaryGap:!0,scale:!0,data:["12/1","12/2","12/3","12/4","12/5","12/6","12/7"],axisTick:{alignWithLabel:!0}}],yAxis:[{axisLine:{show:!1},type:"value"}],series:[{name:"门诊开单",type:"line",symbol:"circle",data:[120,132,101,134,90,230,210],symbolSize:10},{name:"门特处方",type:"line",symbol:"circle",data:[220,182,191,234,290,330,310],symbolSize:10},{name:"住院开单",type:"line",symbol:"circle",data:[150,232,201,154,190,330,410],symbolSize:10}]};this.myChart.setOption(t)}}},c=n,p=a(1001),x=(0,p.Z)(c,i,r,!1,null,"6f03de22",null),v=x.exports},36766:function(t,e,a){var i=a(66586);e["Z"]=i.Z},26263:function(t,e,a){a.d(e,{s:function(){return i},x:function(){return r}});var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},r=[]},66586:function(t,e){var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};e["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}}}]);
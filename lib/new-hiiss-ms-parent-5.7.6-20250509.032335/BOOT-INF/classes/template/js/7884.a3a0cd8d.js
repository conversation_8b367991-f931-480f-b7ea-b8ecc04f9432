"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[7884],{77884:function(t,a,s){s.r(a),s.d(a,{default:function(){return u}});var e=function(){var t=this,a=t.$createElement,s=t._self._c||a;return s("ta-border-layout",{staticClass:"noborder",attrs:{"layout-type":"fixTop"}},[s("div",{staticClass:"headerInfo",attrs:{slot:"header"},slot:"header"},[s("h2",[t._v("卡片列表")]),s("div",[t._v(" 这里是 Livebase UI 的 Vue 实现，开发和服务于企业级后台产品。 提炼自企业级中后台产品的交互语言和视觉风格。 开箱即用的高质量 Vue 组件。 ")])]),s("ta-list",{attrs:{"row-key":"id",grid:{gutter:24,lg:3,md:2,sm:1,xs:1},"data-source":t.dataSource},scopedSlots:t._u([{key:"renderItem",fn:function(a){return s("ta-list-item",{},[a&&void 0!==a.id?[s("ta-card",{attrs:{hoverable:!0}},[s("ta-card-meta",[s("a",{attrs:{slot:"title"},slot:"title"},[t._v(t._s(a.title))]),s("ta-avatar",{staticClass:"card-avatar",attrs:{slot:"avatar",src:a.avatar,size:"large"},slot:"avatar"}),s("div",{staticClass:"meta-content",attrs:{slot:"description"},slot:"description"},[t._v(" "+t._s(a.content)+" ")])],1),s("template",{staticClass:"ant-card-actions",slot:"actions"},[s("a",[t._v("操作一")]),s("a",[t._v("操作二")])])],2)]:[s("ta-button",{staticClass:"new-btn",attrs:{type:"dashed"}},[s("ta-icon",{attrs:{type:"plus"}}),t._v(" 新增产品 ")],1)]],2)}}])})],1)},r=[],i=[];i.push({});for(var n=0;n<11;n++)i.push({id:n,title:"Ta404UI",avatar:s(33157),content:"在中台产品的研发过程中，会出现不同的设计规范和实现方式，但其中往往存在很多类似的页面和组件，这些类似的组件会被抽离成一套标准规范。"});var o={name:"cardList",data:function(){return{dataSource:i}}},l=o,c=s(1001),d=(0,c.Z)(l,e,r,!1,null,"66148a7e",null),u=d.exports},33157:function(t,a,s){t.exports=s.p+"img/ODTLcjxAfvqbxHnVXCYX.8fda82de.png"}}]);
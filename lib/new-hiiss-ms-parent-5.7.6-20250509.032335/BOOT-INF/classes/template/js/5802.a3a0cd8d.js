"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[5802],{88412:function(t,e,i){var a=i(26263),r=i(36766),n=i(1001),o=(0,n.Z)(r.Z,a.s,a.x,!1,null,"5e7ef0ae",null);e["Z"]=o.exports},75802:function(t,e,i){i.r(e),i.d(e,{default:function(){return d}});var a=function(){var t=this,e=this,i=e.$createElement,a=e._self._c||i;return a("div",{staticStyle:{"background-color":"#ececec",padding:"20px"}},[a("ta-row",{attrs:{gutter:16}},[a("ta-col",{attrs:{span:6}},[a("ta-card",{attrs:{bordered:!1}},[a("ta-title",{attrs:{title:"校验条件"}}),a("ta-icon",{attrs:{type:"info-circle"}}),e._v("校验说明: 请先选择患者，再选择需要检验的视图或 接口。 执行校验后，系统将校验所选换的视图或接口 是否存在及字段是否符合接口要求。 "),a("div",[a("ta-button",{attrs:{type:"primary",shape:"circle",size:"small"}},[e._v("1")]),a("ta-button",{staticStyle:{"margin-left":"30px"},attrs:{type:"primary"},on:{click:e.selectHz}},[e._v("选择须校验患者")])],1),a("ta-card",{attrs:{title:e.title,bordered:!0}},e._l(e.selectPatientList,(function(t){return a("p",[e._v(e._s(t))])})),0),a("div",[a("ta-button",{attrs:{type:"primary",shape:"circle",size:"small"}},[e._v("2")]),a("ta-button",{staticStyle:{"margin-left":"30px"},attrs:{type:"primary"},on:{click:e.selectView}},[e._v("选择视图进行校验")])],1),a("ta-big-table",{ref:"viewTable",attrs:{data:e.viewData,"export-config":{},"import-config":{},"checkbox-config":{trigger:"row",highlight:!0,reserve:!0},border:"",height:"440","empty-text":"-","highlight-hover-row":"","highlight-current-row":"","auto-resize":"","show-overflow":"",resizable:"","row-id":"viewCode","keep-source":""}},[a("ta-big-table-column",{attrs:{type:"checkbox",width:"40"}}),a("ta-big-table-column",{attrs:{type:"seq",title:"序号",width:"60",align:"center"}}),a("ta-big-table-column",{attrs:{field:"viewName",title:"视图接口名称","min-width":"350"}})],1),a("div",[a("ta-button",{attrs:{type:"primary",shape:"circle",size:"small"}},[e._v("3")]),a("ta-button",{staticStyle:{"margin-left":"30px"},attrs:{type:"primary"},on:{click:e.selectInterface}},[e._v("选择接口进行校验")])],1),a("ta-big-table",{ref:"interfaceTable",attrs:{data:e.interfaceData,"export-config":{},"import-config":{},"checkbox-config":{trigger:"row",highlight:!0,reserve:!0},border:"",height:"440","empty-text":"-","highlight-hover-row":"","highlight-current-row":"","auto-resize":"","show-overflow":"",resizable:"","row-id":"interfaceCode","keep-source":""}},[a("ta-big-table-column",{attrs:{type:"checkbox",width:"40"}}),a("ta-big-table-column",{attrs:{type:"seq",title:"序号",width:"60",align:"center"}}),a("ta-big-table-column",{attrs:{field:"interfaceName",title:"接口名称","min-width":"350"}})],1),e.visible?a("ta-modal",{attrs:{visible:e.visible,title:"患者选择",height:"500px",footer:null,width:1e3,height:550},on:{cancel:e.cancel}},[a("ta-form",{staticStyle:{"padding-top":"5px"},attrs:{autoFormCreate:function(e){t.searchPatientForm=e},layout:"horizontal",formLayout:!0}},[a("ta-form-item",{attrs:{"field-decorator-id":"akc191",label:"就诊标识",span:8}},[a("ta-input",{attrs:{placeholder:"请输入内容"}})],1),a("ta-form-item",{attrs:{label:"入院时间","init-value":e.admDate,"field-decorator-id":"aae030"}},[a("ta-date-picker",{staticStyle:{width:"100%"},attrs:{format:"YYYY-MM-DD","allow-clear":"",placeholder:"请选择有效时间"}})],1),a("ta-button",{staticStyle:{"margin-right":"20px",float:"right"},attrs:{type:"primary",icon:"search"},on:{click:e.fnQuery}},[e._v("查询 ")])],1),a("ta-row",[a("ta-button",{staticStyle:{"margin-right":"20px",float:"right"},attrs:{type:"primary"},on:{click:e.submit}},[e._v("选择并提交 ")])],1),a("div",{staticClass:"fit content-box"},[a("div",{staticClass:"content-table"},[a("ta-big-table",{ref:"patientTable",attrs:{data:e.patientList,"export-config":{},"import-config":{},"checkbox-config":{trigger:"row",highlight:!0,reserve:!0},border:"",height:"440","empty-text":"-","highlight-hover-row":"","highlight-current-row":"","auto-resize":"","show-overflow":"",resizable:"","row-id":"mdtrtId","keep-source":""},scopedSlots:e._u([{key:"bottomBar",fn:function(){return[a("ta-pagination",{ref:"patientPager",staticStyle:{"text-align":"right","margin-top":"10px"},attrs:{dataSource:e.patientList,params:e.pageParams,url:"hisViewOrInterfaceValid/getPatientData"},on:{"update:dataSource":function(t){e.patientList=t},"update:data-source":function(t){e.patientList=t}}})]},proxy:!0}],null,!1,**********)},[a("ta-big-table-column",{attrs:{type:"checkbox",width:"40"}}),a("ta-big-table-column",{attrs:{type:"seq",title:"序号",width:"60",align:"center"}}),a("ta-big-table-column",{attrs:{field:"mdtrtId",title:"就诊标识","min-width":"350"}}),a("ta-big-table-column",{attrs:{field:"patnIptCnt",title:"住院次数","min-width":"350"}}),a("ta-big-table-column",{attrs:{field:"fixmedinsCode",title:"医疗机构编码","min-width":"350"}})],1)],1)])],1):e._e()],1)],1),a("ta-col",{attrs:{span:18}},[e.relateViews.length>0?a("ta-card",{attrs:{bordered:!1}},[a("ta-title",{attrs:{title:"视图关联校验结果"}}),e._l(e.relateViews,(function(t){return a("p",[e._v(e._s(t))])}))],2):e._e(),a("ta-card",{attrs:{bordered:!1}},[a("ta-title",{attrs:{title:"视图校验结果"}}),a("ta-tabs",{attrs:{type:"card"},on:{change:function(){return 1}}},e._l(e.selectViews,(function(t,i){return a("ta-tab-pane",{key:i,attrs:{tab:t.viewName}},[a("ta-card",{attrs:{bordered:!0}},e._l(t.validList,(function(t,i){return a("div",[t.nullNot?a("div",[e._v(e._s(i+1)+"、必填项缺失校验：")]):e._e(),e._l(t.nullNot,(function(t){return a("p",[e._v(e._s(t))])})),t.dictNot?a("div",[e._v(e._s(i+1)+"、码值范围不正确：")]):e._e(),e._l(t.dictNot,(function(t){return a("p",[e._v(e._s(t))])})),t.dateNot?a("div",[e._v(e._s(i+1)+"、日期格式不正确：")]):e._e(),e._l(t.dateNot,(function(t){return a("p",[e._v(e._s(t))])})),t.lengthNot?a("div",[e._v(e._s(i+1)+"、字符超长：")]):e._e(),e._l(t.lengthNot,(function(t){return a("p",[e._v(e._s(t))])})),t.typeNot?a("div",[e._v(e._s(i+1)+"、字符类型：")]):e._e(),e._l(t.typeNot,(function(t){return a("p",[e._v(e._s(t))])}))],2)})),0)],1)})),1)],1),e.relateInterfaces.length>0?a("ta-card",{attrs:{bordered:!1}},[a("ta-title",{attrs:{title:"接口关联校验结果"}}),e._l(e.relateInterfaces,(function(t){return a("p",[e._v(e._s(t))])}))],2):e._e(),a("ta-card",{attrs:{bordered:!1}},[a("ta-title",{attrs:{title:"接口校验结果"}}),a("ta-tabs",{attrs:{type:"card"},on:{change:function(){return 1}}},[e._l(e.selectInterfaces,(function(t,i){return a("ta-tab-pane",{key:i,attrs:{tab:t.interfaceName}},[a("ta-card",{attrs:{bordered:!0}},e._l(t.validList,(function(t,i){return a("div",[t.nullNot?a("div",[e._v(e._s(i+1)+"、必填项缺失校验：")]):e._e(),e._l(t.nullNot,(function(t){return a("p",[e._v(e._s(t))])})),t.dictNot?a("div",[e._v(e._s(i+1)+"、码值范围不正确：")]):e._e(),e._l(t.dictNot,(function(t){return a("p",[e._v(e._s(t))])})),t.dateNot?a("div",[e._v(e._s(i+1)+"、日期格式不正确：")]):e._e(),e._l(t.dateNot,(function(t){return a("p",[e._v(e._s(t))])})),t.lengthNot?a("div",[e._v(e._s(i+1)+"、字符超长：")]):e._e(),e._l(t.lengthNot,(function(t){return a("p",[e._v(e._s(t))])})),t.typeNot?a("div",[e._v(e._s(i+1)+"、字符类型：")]):e._e(),e._l(t.typeNot,(function(t){return a("p",[e._v(e._s(t))])}))],2)})),0)],1)})),e.select10021Interfaces.length>0?a("ta-tab-pane",{attrs:{tab:"【10021】输出患者辅助审核信息"}},[a("ta-tabs",{attrs:{type:"card"}},e._l(e.select10021Interfaces,(function(t,i){return a("ta-tab-pane",{key:i,attrs:{tab:t.interfaceName}},[a("ta-card",{attrs:{bordered:!0}},e._l(t.validList,(function(t,i){return a("div",[t.nullNot?a("div",[e._v(e._s(i+1)+"、必填项缺失校验：")]):e._e(),e._l(t.nullNot,(function(t){return a("p",[e._v(e._s(t))])})),t.dictNot?a("div",[e._v(e._s(i+1)+"、码值范围不正确：")]):e._e(),e._l(t.dictNot,(function(t){return a("p",[e._v(e._s(t))])})),t.dateNot?a("div",[e._v(e._s(i+1)+"、日期格式不正确：")]):e._e(),e._l(t.dateNot,(function(t){return a("p",[e._v(e._s(t))])})),t.lengthNot?a("div",[e._v(e._s(i+1)+"、字符超长：")]):e._e(),e._l(t.lengthNot,(function(t){return a("p",[e._v(e._s(t))])})),t.typeNot?a("div",[e._v(e._s(i+1)+"、字符类型：")]):e._e(),e._l(t.typeNot,(function(t){return a("p",[e._v(e._s(t))])}))],2)})),0)],1)})),1)],1):e._e()],2)],1)],1)],1)],1)},r=[],n=(i(55115),i(88412)),o={components:{TaTitle:n.Z},data:function(){return{admDate:this.Base.getMoment((new Date).toISOString().slice(0,10),"YYYY-MM-DD"),visible:!1,patientList:[],title:[],selectPatientList:[],selectPatients:[],relateViews:[],selectViews:[],relateInterfaces:[],selectInterfaces:[],select10021Interfaces:[],viewData:[{viewCode:"yh_ipt_baseinfo",viewName:"【yh_ipt_baseinfo】住院就诊信息"},{viewCode:"yh_ipt_diagnose",viewName:"【yh_ipt_diagnose】住院诊断信息"},{viewCode:"yh_ipt_orderdetail",viewName:"【yh_ipt_orderdetail】住院医嘱信息"},{viewCode:"yh_ipt_feedetail",viewName:"【yh_ipt_feedetail】住院费用信息"},{viewCode:"yh_ipt_setlinfo",viewName:"【yh_ipt_setlinfo】门诊和住院结算信息"},{viewCode:"yh_examinfo",viewName:"【yh_examinfo】检查信息"},{viewCode:"yh_labinfo",viewName:"【yh_labinfo】检验信息"},{viewCode:"yh_sstbinfo",viewName:"【yh_sstbinfo】药敏信息"},{viewCode:"yh_nrsinfo",viewName:"【yh_nrsinfo】营养风险评分与BMI值信息"},{viewCode:"yh_deptinfo",viewName:"【yh_deptinfo】科室信息"},{viewCode:"yh_medstffinfo",viewName:"【yh_medstffinfo】医执人员信息"},{viewCode:"yh_med_teaminfo",viewName:"【yh_med_teaminfo】医疗小组信息"},{viewCode:"yh_medlist_hilist",viewName:"【yh_medlist_hilist】医保目录对照信息"},{viewCode:"yh_oprninfo",viewName:"【yh_oprninfo】手术信息"}],interfaceData:[{interfaceCode:"10002",interfaceName:"【10002】输出患者基本信息和诊疗信息"},{interfaceCode:"10003",interfaceName:"【10003】输出患者有效的医嘱信息"},{interfaceCode:"10004",interfaceName:"【10004】输出患者费用信息"},{interfaceCode:"10011",interfaceName:"【10011】输出患者结算信息"},{interfaceCode:"10021",interfaceName:"【10021】输出患者辅助审核信息"}],interface10021Sub:[{code:"labinfo_dtos",name:"【labinfo_dtos】检验信息"},{code:"examinfo_dtos",name:"【examinfo_dtos】检查报告信息"},{code:"nrsinfo_dtos",name:"【nrsinfo_dtos】营养风险评分与BMI值信息"},{code:"nurscareinfo_dtos",name:"【nurscareinfo_dtos】护理操作生命体征测量记录信息"},{code:"germinfo_dtos",name:"【germinfo_dtos】细菌培养报告信息"},{code:"sstbinfo_dtos",name:"【sstbinfo_dtos】药敏记录报告记录信息"}],editType:"edit",count:0,form:null,clickRowData:null,tableData:[]}},mounted:function(){},methods:{selectHz:function(){var t=this;this.visible=!0,this.$nextTick((function(){t.fnQuery()}))},selectInterface:function(){var t=this,e=this.$refs.interfaceTable.getCheckboxReserveRecords();if(0!==e.length){var i=e.map((function(t){return t.interfaceCode}));this.Base.submit(null,{url:"hisViewOrInterfaceValid/interfaceValid",data:{interfaceCodes:i,patients:this.selectPatients},autoValid:!0,autoQs:!1},{successCallback:function(e){t.validInterface(e)},failCallback:function(e){t.$message.error("提交失败")}})}else this.$message.error("请先选择数据")},selectView:function(){var t=this,e=this.$refs.viewTable.getCheckboxReserveRecords();if(0!==e.length){var i=e.map((function(t){return t.viewCode}));this.Base.submit(null,{url:"hisViewOrInterfaceValid/viewValid",data:{viewCodes:i,patients:this.selectPatients},autoValid:!0,autoQs:!1},{successCallback:function(e){t.valid(e)},failCallback:function(e){t.$message.error("提交失败")}})}else this.$message.error("请先选择数据")},fnQuery:function(){this.$refs.patientPager.loadData()},valid:function(t){var e=this;this.relateViews=t.data.data.relate||[],this.selectViews=t.data.data.valid,this.selectViews.forEach((function(t){t.viewName=e.viewData.filter((function(e){return e.viewCode===t.viewCode}))[0].viewName}))},validInterface:function(t){var e=this;this.relateInterfaces=t.data.data.relate||[],this.selectInterfaces=t.data.data.valid.filter((function(t){return"10021"!==t.interfaceCode})),this.select10021Interfaces=t.data.data.valid.filter((function(t){return"10021"===t.interfaceCode})),this.selectInterfaces.forEach((function(t){t.interfaceName=e.interfaceData.filter((function(e){return e.interfaceCode===t.interfaceCode}))[0].interfaceName})),this.select10021Interfaces.forEach((function(t){t.interfaceName=e.interface10021Sub.filter((function(e){return e.code===t.inter}))[0].name}))},submit:function(){var t=this,e=this.$refs.patientTable.getCheckboxReserveRecords();if(0!==e.length){var i=0;e.forEach((function(e){i++;var a="就诊号："+e.mdtrtId,r="，住院次数："+e.patnIptCnt,n="，医药机构："+e.fixmedinsCode;t.selectPatientList.push(a+r+n),t.selectPatients.push({mdtrtId:e.mdtrtId,patnIptCnt:e.patnIptCnt,fixmedinsCode:e.fixmedinsCode})})),this.title="已选择患者："+i+"个",this.visible=!1}else this.$message.error("请先选择数据")},pageParams:function(){var t=this.searchPatientForm.getFieldsValue();return t.aae030=t.aae030.format("YYYY-MM-DD"),t.type="view",t.name="yh_ipt_baseinfo",t},cancel:function(){this.visible=!1}}},s=o,l=i(1001),c=(0,l.Z)(s,a,r,!1,null,null,null),d=c.exports},36766:function(t,e,i){var a=i(66586);e["Z"]=a.Z},26263:function(t,e,i){i.d(e,{s:function(){return a},x:function(){return r}});var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("h3",{staticStyle:{"font-weight":"normal"}},[i("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),i("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},r=[]},66586:function(t,e){var i={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};e["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:i}}}}}]);
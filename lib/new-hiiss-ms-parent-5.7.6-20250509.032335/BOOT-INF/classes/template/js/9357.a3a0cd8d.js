(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[9357],{88412:function(t,e,a){"use strict";var i=a(26263),r=a(36766),n=a(1001),l=(0,n.Z)(r.Z,i.s,i.x,!1,null,"5e7ef0ae",null);e["Z"]=l.exports},97848:function(t,e,a){"use strict";a.r(e),a.d(e,{default:function(){return x}});var i=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("div",{staticClass:"fit"},[i("ta-border-layout",{attrs:{"footer-cfg":{showBorder:!1},layout:{header:"165px",footer:"0px"},showPadding:!0}},[i("div",{attrs:{slot:"header"},slot:"header"},[i("ta-title",{attrs:{title:"查询条件"}}),i("ta-form",{staticStyle:{"padding-top":"5px"},attrs:{autoFormCreate:function(e){t.baseInfoForm=e},col:2,formLayout:!0}},[i("ta-form-item",{attrs:{fieldDecoratorOptions:{rules:[{required:!0,message:"必须选取日期"}]},"init-value":e.defaultValue,"label-col":{span:8},required:!0,span:4,"wrapper-col":{span:16},fieldDecoratorId:"allDate",label:"审核时间"}},[i("ta-date-picker",[i("ta-icon",{attrs:{slot:"suffixIcon",type:""},slot:"suffixIcon"})],1)],1),i("ta-form-item",{attrs:{"label-col":{span:8},span:4,"wrapper-col":{span:16},"field-decorator-id":"akb020",label:"院区标识"}},[i("ta-select",{attrs:{options:e.akb020List,disabled:e.paramsDisable.akb020,allowClear:"",placeholder:"院区选择"},on:{change:e.departSelectChange}})],1),i("ta-form-item",{attrs:{"label-col":{span:8},span:4,virtual:!0,"wrapper-col":{span:16},"field-decorator-id":"aaz307",label:"科室名称"}},[i("ta-select",{attrs:{options:e.ksList,"show-search":!0,allowClear:"",placeholder:"科室名称筛选",showSearch:""}})],1),i("ta-form-item",{attrs:{"label-col":{span:8},span:4,"wrapper-col":{span:16},"field-decorator-id":"aaz263",label:"医师姓名"}},[i("ta-select",{attrs:{options:e.doctorList,"show-search":!0,allowClear:"",placeholder:"医师姓名筛选",showSearch:""}})],1),i("ta-form-item",{attrs:{"label-col":{span:8},span:4,"wrapper-col":{span:16},"field-decorator-id":"ake003",label:"三目类别"}},[i("ta-select",{attrs:{"dropdown-match-select-width":!1,"allow-clear":"","collection-type":"AKE003",placeholder:"三目类别筛选",showSearch:""}})],1),i("ta-form-item",{attrs:{"label-col":{span:8},span:4,"wrapper-col":{span:16},"field-decorator-id":"projectInfo",label:"医保项目"}},[i("ta-input",{attrs:{placeholder:"请输入医保项目名称或编码"}})],1),i("ta-form-item",{attrs:{"label-col":{span:8},span:4,"wrapper-col":{span:16},"field-decorator-id":"aaz319",label:"规则信息"}},[i("ta-input",{attrs:{placeholder:"请输入规则分类或者名称"}})],1),i("ta-form-item",{attrs:{span:4,"label-col":{span:8},"wrapper-col":{span:16},fieldDecoratorId:"result",label:"预审结果","init-value":e.resultInit}},[i("ta-select",{attrs:{showSearch:"",mode:"multiple","collection-type":"APE800","collection-filter":"0",maxTagCount:1,placeholder:"请选择",allowClear:""}})],1),i("ta-form-item",{attrs:{span:12}}),i("ta-form-item",{attrs:{"label-col":{span:8},span:4,"wrapper-col":{span:16},label:" "}},[i("ta-button",{attrs:{icon:"search",type:"primary"},on:{click:e.fnQuery}},[e._v("查询")]),i("ta-button",{attrs:{icon:"redo",disabled:e.paramsDisable.akb020,type:"default"},on:{click:e.fnReset}},[e._v("重置")])],1)],1)],1),i("div",{staticClass:"fit",staticStyle:{height:"87%"}},[i("ta-title",{staticStyle:{width:"50%"},attrs:{title:"查询结果"}}),i("ta-big-table",{ref:"Table",attrs:{"control-column":e.showHiddenOrSortColumn,data:e.userList,"footer-method":e.footerMethod,"tooltip-config":{enabled:!0,contentMethod:e.showTooltipMethod},"auto-resize":"",border:"","empty-text":"-",height:"100%","highlight-current-row":"","highlight-hover-row":"",resizable:"","show-footer":"","show-overflow":""},on:{"cell-click":e.cellClickEvent}},[i("ta-big-table-column",{attrs:{align:"center","header-align":"center","min-width":"50",sortable:"",title:"序号",type:"seq"}}),i("ta-big-table-column",{attrs:{align:"left",field:"gzfl","header-align":"center","min-width":"220",sortable:"",title:"规则分类"}}),i("ta-big-table-column",{attrs:{align:"left",field:"aaa167","header-align":"center","min-width":"300",sortable:"",title:"规则名称"}}),i("ta-big-table-column",{attrs:{align:"left",field:"ykz018","header-align":"center","min-width":"300",sortable:"",title:"知识元"}}),i("ta-big-table-column",{attrs:{align:"left",field:"ake002","header-align":"center","min-width":"300",sortable:"",title:"医保项目名称"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("ta-button",{attrs:{size:"small",type:"link"}},[i("span",{staticStyle:{color:"#00bcff","text-decoration":"underline"}},[e._v(e._s(a.ake002))])])]}}])}),i("ta-big-table-column",{attrs:{visible:!1,align:"right",field:"shzl","header-align":"center","min-width":"140",title:"审核总次数"}}),i("ta-big-table-column",{attrs:{align:"right",field:"txzl","header-align":"center","min-width":"140",sortable:"",title:"违规次数(次)"}}),i("ta-big-table-column",{attrs:{formatter:e.moneyFormat,align:"right",field:"txje","header-align":"center","min-width":"140",sortable:"",title:"违规金额(元)"}}),i("ta-big-table-column",{attrs:{align:"right",field:"txsl","header-align":"center","min-width":"140",sortable:"",title:"违规数量"}}),i("ta-big-table-column",{attrs:{align:"right",field:"jxxms","header-align":"center","min-width":"150",sortable:"",title:"继续使用次数(次)"}}),i("ta-big-table-column",{attrs:{formatter:e.ratioFormat,align:"right",field:"jxxmzb","header-align":"center","min-width":"180",sortable:"",title:"继续使用次数占比"}}),i("ta-big-table-column",{attrs:{align:"right",field:"qxxms","header-align":"center","min-width":"130",sortable:"",title:"取消次数(次)"}}),i("ta-big-table-column",{attrs:{formatter:e.ratioFormat,align:"right",field:"qxxmzb","header-align":"center","min-width":"150",sortable:"",title:"取消次数占比"}}),i("ta-big-table-column",{attrs:{align:"right",field:"zfxms","header-align":"center","min-width":"130",sortable:"",title:"自费次数(次)"}}),i("ta-big-table-column",{attrs:{formatter:e.ratioFormat,align:"right",field:"zfxmzb","header-align":"center","min-width":"150",sortable:"",title:"自费次数占比"}}),i("ta-big-table-column",{attrs:{align:"right",field:"wzcxms","header-align":"center","min-width":"140",sortable:"",title:"无操作次数(次)"}}),i("ta-big-table-column",{attrs:{formatter:e.ratioFormat,align:"right",field:"wczxmzb","header-align":"center","min-width":"160",sortable:"",title:"无操作次数占比"}}),i("template",{slot:"bottomBar"},[i("ta-button",{staticStyle:{float:"right","margin-top":"20px"},attrs:{icon:"download",type:"primary"},on:{click:e.exportTable}},[e._v("导出 ")]),i("ta-pagination",{ref:"gridPager",staticStyle:{float:"right","margin-top":"20px","margin-right":"10px"},attrs:{"data-source":e.userList,defaultPageSize:200,pageSizeOptions:["30","50","100","200","500"],params:e.infoPageParams,url:"reportStatistics/queryListNight"},on:{"update:dataSource":function(t){e.userList=t},"update:data-source":function(t){e.userList=t}}})],1)],2)],1)])],1)},r=[],n=a(66347),l=a(48534),o=a(95082),s=(a(36133),a(88412)),u=a(36797),c=a.n(u),f=a(22722),m=a(55115),d=a(18671),h=a(92566),p=a(83231);m.w3.prototype.Base=Object.assign(m.w3.prototype.Base,(0,o.Z)({},f.Z));var b={name:"nightStatisticsXM",components:{TaTitle:s.Z},data:function(){return{userList:[],amountData:[],resultInit:[],defaultValue:TaUtils.getMoment((new Date).toISOString().slice(0,10),"YYYY-MM-DD"),akb020:"",akb020List:[],doctorList:[],ksList:[],permissions:{},paramsDisable:{akb020:!1},jxzb:"",showHiddenOrSortColumn:{themeColor:"red",icon:"appstore",showHiddenConfig:{open:!0,disabledHiddenColumns:function(t){return!1}},columnSortConfig:{open:!0,onMove:function(t,e){e.dragColumn,e.dropColumn;return!0},dragEnd:function(t,e){e.dragColumn,e.resultColumns}},successCallback:this.fnSaveTableTitle,disabledControlCol:function(t){}},txl:""}},created:function(){var t=this;return(0,l.Z)(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,p.Z.permissionCheck();case 2:return t.permissions=e.sent,e.next=5,t.$nextTick((function(){t.fnQueryTableTitle()}));case 5:case"end":return e.stop()}}),e)})))()},mounted:function(){if(this.flag=this.$route.query.flag,"undefined"!=this.$route.query.flag&&this.$route.query.flag&&(this.aae500Flag=!0,this.baseInfoForm.setFieldsValue({aae500:this.$route.query.flag})),this.fnQueryHos(),this.fnQueryDept(""),this.$route.query.params){var t=JSON.parse(this.$route.query.params);t.allDate=this.Base.getMoment(inputDate.toISOString().slice(0,10),"YYYY-MM-DD"),this.$route.query.aaz307&&(t.aaz307=this.$route.query.aaz307),this.$route.query.aaz263&&(t.aaz263=this.$route.query.aaz263),this.$route.query.aaz319&&(t.aaz319=this.$route.query.aaz319),this.baseInfoForm.setFieldsValue(t)}this.fnQueryAa01()},methods:{moment:c(),fnQueryTableTitle:function(){var t=this,e=top.indexTool.getActiveTabMenuId(),a=top.indexTool.getUserInfo().loginId,i=[];this.Base.submit(null,{url:"miimCommonRead/selectTableColumShow",data:{resourceid:e,loginid:a},autoValid:!1},{successCallback:function(e){if(e.data.list.length>0&&e.data.list[0].colum){var a=JSON.parse(e.data.list[0].colum),r=t.$refs.Table.getTableColumn().fullColumn;i=a.map((function(t){return t.title}));var n=[];r.forEach((function(t){if("checkbox"==t.type)n.push(t);else{var i=a.find((function(e){return e.title===t.title}));i&&(t.visible=i.visible,t.width=i.width);var r=t;r.sortable?r.minWidth=20*r.title.length+30:r.minWidth=20*r.title.length+10,"操作"==r.title&&(r.minWidth=150),"项目引导信息"==r.title&&(r.minWidth=300),e.data.akc191Title.length>0&&"akc191"===r.property&&(r.title=e.data.akc191Title[0].label),n.push(r)}})),i.length>0&&n.sort((function(t,e){return"序号"===t.title?-1:"序号"===e.title?1:i.indexOf(t.title)-i.indexOf(e.title)})),t.$refs.Table.loadColumn(n)}},failCallback:function(e){t.$message.error("查询表头失败")}})},fnSaveTableTitle:function(t){var e=this,a=(t.table,t.resultColumnsList),i=a,r=[];i.forEach((function(t){var e=t.title,a=t.visible;t.type;e&&r.push({title:e,visible:a})}));var n=top.indexTool.getActiveTabMenuId(),l=top.indexTool.getUserInfo().loginId,o={colum:JSON.stringify(r),flag:"column",resourceid:n,loginid:l};p.Z.insertTableColumShow(o,(function(t){"success"===t?e.$message.success("保存成功"):e.$message.error("保存失败")}))},fnQueryAa01:function(){var t=this;this.Base.submit(null,{url:"miimCommonRead/getAa01Aaa005ByAaz499",data:{aaz499:206}},{successCallback:function(e){var a=e.data;t.resultInit="n"===a.aaa005.toLowerCase()?["1","2"]:["1","2","3"],t.fnQuery()},failCallback:function(e){t.$message.error("查询aa01表数据失败")}})},fnQueryHos:function(){var t=this,e={url:"/miimCommonRead/queryHospRpcList",showPageLoading:!1,autoValid:!0},a={successCallback:function(e){t.akb020List=e.data.resultData,t.permissions&&t.permissions.akb020Set.size>0&&(t.paramsDisable.akb020=!0,t.akb020List=t.akb020List.filter((function(e){return t.permissions.akb020Set.has(e.value)})),t.baseInfoForm.setFieldsValue({akb020:t.akb020List[0].value})),t.akb020=t.akb020List[0].value},failCallback:function(e){t.$message.error("医院数据加载失败")}};this.Base.submit(null,e,a)},footerMethod:function(t){var e=this,a=t.columns;t.data;return[a.map((function(t,a){return 0===a?"合计":["txzl","txsl","txje","jxxms","qxxms","zfxms","wzcxms"].includes(t.property)?(0,h.Z)(e.amountData,t.property):null}))]},showTooltipMethod:function(t){var e=t.type,a=t.column,i=(t.row,t.items,t._columnIndex,a.property);if("header"===e)return d.Z.props[i]?d.Z.props[i][e]:""},cellClickEvent:function(t){var e=t.row,a=t.column,i=a.property;if("ake002"===i){var r=this.baseInfoForm.getFieldsValue();this.Base.openTabMenu({id:Math.floor(90*Math.random())+10,name:"日审患者明细汇总",url:"reportStatistics.html#/nightStatisticsHZSecond?flag=".concat(this.flag,"&ake002=").concat(e.ake001,"&params=").concat(JSON.stringify(r)),refresh:!1})}},exportTable:function(){var t=this,e=this.baseInfoForm.getFieldsValue();if(void 0===e.allDate||e.allDate.length<2)this.$message.error("请选择时间范围！");else{e=this.infoPageParams();var a,i=[],r=this.$refs.Table.getColumns(),l=(0,n.Z)(r);try{for(l.s();!(a=l.n()).done;){var o=a.value;"序号"!==o.title&&i.push({header:o.title,key:o.property,width:20})}}catch(s){l.e(s)}finally{l.f()}this.Base.submit(null,{url:"reportStatistics/exportExcel",data:e,autoValid:!1},{successCallback:function(e){var a=e.data.data,r={fileName:"每晚预审项目统计结果表",sheets:[{name:"worksheet1",column:{complex:!1,columns:i},rows:a,codeList:d.Z.codelist.filter((function(t){return"txl"!==t.columnKey}))}]};t.Base.generateExcel(r)},failCallback:function(e){t.$message.error("数据加载失败")}})}},formatAmount:function(t){return t.toFixed(2).replace(/\d(?=(\d{3})+\.)/g,"$&,")},moneyFormat:function(t){var e=t.cellValue;return"--"==e?e:this.formatAmount(e)},ratioFormat:function(t){var e=t.cellValue;return 0==e?"0":e+"%"},fnReset:function(){this.baseInfoForm.resetFields(),this.$route.query.flag&&this.baseInfoForm.setFieldsValue({aae500:this.$route.query.flag}),this.txl="",this.jxzb="",this.fnQuery()},fnQueryDocter:function(){var t=this,e={akb020:this.akb020};this.Base.submit(null,{url:"miimCommonRead/queryDoctorRpcList",data:e,showPageLoading:!1,autoValid:!1},{successCallback:function(e){t.doctorList=e.data.resultData},failCallback:function(e){t.$message.error("医师数据加载失败")}})},departSelectChange:function(t){this.fnQueryDept(t)},aae500SelectChange:function(t){this.hospDeptType="1"==t||"0"==t?"C":"I",this.fnQueryDept("")},fnQueryDept:function(t){var e=this;this.akb020=t||"",this.Base.submit(null,{url:"miimCommonRead/queryDepartRpcList",data:{akb020:this.akb020,hospDeptType:this.hospDeptType},showPageLoading:!1,autoValid:!1},{successCallback:function(t){e.ksList=t.data.resultData,e.fnQueryDocter()},failCallback:function(t){e.$message.error("科室数据加载失败")}})},infoPageParams:function(){var t=this.baseInfoForm.getFieldsValue();return t.startDate=t.allDate.format("YYYYMMDD"),t.endDate=t.allDate.format("YYYYMMDD"),t.aae500="6",t.flag="xm",t},fnQuery:function(){var t=this,e=this.baseInfoForm.getFieldValue("allDate");e?this.$nextTick((function(){t.$refs.gridPager.loadData((function(e){t.amountData=e.data.data}))})):this.$message.error("请选择时间范围！")},fnCustomHeaderCell:function(t){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px",fontWeight:"bold"}}},filterOption:function(t,e){return e.componentOptions.children[0].text.indexOf(t)>=0}}},g=b,v=a(1001),y=(0,v.Z)(g,i,r,!1,null,"6354efba",null),x=y.exports},36766:function(t,e,a){"use strict";var i=a(66586);e["Z"]=i.Z},26263:function(t,e,a){"use strict";a.d(e,{s:function(){return i},x:function(){return r}});var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},r=[]},66586:function(t,e){"use strict";var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};e["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}},83231:function(t,e,a){"use strict";var i=a(48534);a(36133);function r(t){return t?t.akb020&&t.aaz263?"personal":t.akb020&&t.aaz309&&t.aaz307?"group":t.akb020&&t.aaz307?"department":t.akb020?"hospital":"all":"all"}function n(t){return l.apply(this,arguments)}function l(){return l=(0,i.Z)(regeneratorRuntime.mark((function t(e){var a,i,n,l,o,s,u,c,f,m,d,h;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Base.submit(null,{url:"/miimCommonRead/queryDataPermission",showPageLoading:!1,data:{jobnumber:e},autoValid:!0});case 2:return a=t.sent,i=new Set,n=new Set,a.data.permission.forEach((function(t){var e=r(t);"hospital"===e&&i.add(t.akb020),"department"===e&&n.add(t.aaz307)})),l=a.data.permission.filter((function(t){return"department"===r(t)||!n.has(t.aaz307)})).filter((function(t){return"hospital"===r(t)||!i.has(t.akb020)})),o=new Set(l.map((function(t){return t.akb020})).filter((function(t){return null!=t}))),s=new Set(l.map((function(t){return t.aaz307})).filter((function(t){return null!=t}))),u=new Set(l.map((function(t){return t.aaz309})).filter((function(t){return null!=t}))),c=new Set(l.map((function(t){return t.aaz263})).filter((function(t){return null!=t}))),f=!1,m=!1,d=!1,h=!1,1===o.size&&(f=!0),1===s.size&&1===o.size&&(m=!0),1===s.size&&1===o.size&&1===u.size&&(d=!0),1===o.size&&0===s.size&&1===c.size&&(h=!0),t.abrupt("return",{akb020Set:o,aaz307Set:s,aaz263Set:c,aaz309Set:u,akb020Disable:f,aaz307Disable:m,aaz263Disable:h,aaz309Disable:d});case 20:case"end":return t.stop()}}),t)}))),l.apply(this,arguments)}function o(t,e){Base.submit(null,{url:"miimCommonRead/getAa01AAE500StartStop",data:t},{successCallback:function(t){return e(t)}})}function s(t,e){Base.submit(null,{url:"miimCommonRead/insertTableColumShow",data:t,autoValid:!1},{successCallback:function(t){return e("success")},failCallback:function(t){return e("fail")}})}var u={zbx01:{footer:function(){return"统筹金总额"}},zbx02:{footer:function(){return"基金支出总额"}},zbx03:{footer:function(){return"总费用"}},zbx04:{footer:function(){return"总住院日"}}};e["Z"]={permissionCheck:n,getAa01AAE500StartStop:o,insertTableColumShow:s,props:u,moneyNumFormat:function(t){var e=t.cellValue;return e||"—"}}},18671:function(t,e){"use strict";var a=[{columnKey:"jxxmzb",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}},{columnKey:"txl",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}},{columnKey:"qxxmzb",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}},{columnKey:"zfxmzb",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}},{columnKey:"wczxmzb",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}}],i=[{columnKey:"jxxmzb",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}},{columnKey:"qxxmzb",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}},{columnKey:"zfxmzb",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}},{columnKey:"wczxmzb",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}}],r={txl:{header:function(){return"提醒率=提醒总次数/审核总次数"}},jxxmzb:{header:function(){return"继续使用占比=继续使用次数(次)/提醒总次数"}},qxxmzb:{header:function(){return"取消使用占比=取消使用次数/提醒总次数"}},zfxmzb:{header:function(){return"自费使用占比=自费使用次数/提醒总次数"}},wczxmzb:{header:function(){return"无操作使用占比=无操作使用次数/提醒总次数"}}},n=new Map([["1","门诊审核"],["0","门特审核"],["2","医嘱审核"],["3","计费审核"]]);e["Z"]={codelist:a,codelist2:i,props:r,optionsMap:n}},55382:function(){},61219:function(){}}]);
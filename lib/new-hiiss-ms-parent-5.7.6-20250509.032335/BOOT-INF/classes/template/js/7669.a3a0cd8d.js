"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[7669],{88412:function(t,e,a){var i=a(26263),n=a(36766),r=a(1001),s=(0,r.Z)(n.Z,i.s,i.x,!1,null,"5e7ef0ae",null);e["Z"]=s.exports},27669:function(t,e,a){a.r(e),a.d(e,{default:function(){return f}});var i=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("div",{staticClass:"fit"},[i("ta-border-layout",{attrs:{layout:{header:"130px"}}},[i("div",{staticStyle:{height:"100%",padding:"10px"},attrs:{slot:"header"},slot:"header"},[i("ta-form",{staticStyle:{"margin-top":"10px"},attrs:{layout:"horizontal",formLayout:!0,"label-width":"130px","auto-form-create":function(e){return t.form=e}}},[i("ta-form-item",{attrs:{label:"开始日期",fieldDecoratorId:"effectiveBeginDate",span:5}},[i("ta-date-picker",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择","allow-clear":""}})],1),i("ta-form-item",{attrs:{label:"结束日期",fieldDecoratorId:"effectiveEndDate",span:5}},[i("ta-date-picker",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择","allow-clear":""}})],1),i("ta-form-item",{attrs:{label:"指标",fieldDecoratorId:"indexId",span:5}},[i("ta-select",{staticStyle:{width:"100%"},attrs:{showSearch:"",options:e.indicatorArr,"options-key":{label:"name",value:"id"},virtual:!0,placeholder:"请选择","allow-clear":""}})],1),i("ta-form-item",{attrs:{label:"状态",fieldDecoratorId:"status",span:5}},[i("ta-select",{staticStyle:{width:"100%"},attrs:{"collection-type":"STATE",placeholder:"请输入","allow-clear":""}})],1),i("ta-form-item",{attrs:{span:1}}),i("ta-form-item",{attrs:{span:3}},[i("ta-button",{on:{click:e.resetForm}},[e._v("重置")]),i("ta-button",{attrs:{type:"primary"},on:{click:e.seachForm}},[e._v("查询")])],1)],1)],1),i("div",{staticStyle:{height:"100%",padding:"10px"}},[i("div",{staticClass:"boxTitle"},[i("ta-button",{attrs:{type:"primary"},on:{click:e.addInfo}},[e._v("新增")]),i("ta-dropdown",[i("ta-menu",{attrs:{slot:"overlay"},slot:"overlay"},[i("ta-menu-item",{key:"1",on:{click:e.batchStartTable}},[i("ta-icon",{attrs:{type:"check-circle"}}),e._v(" 启用 ")],1),i("ta-menu-item",{key:"2",on:{click:e.batchStopTable}},[i("ta-icon",{attrs:{type:"stop"}}),e._v(" 停用 ")],1),i("ta-menu-item",{key:"3"},[i("ta-popconfirm",{attrs:{title:"确定要删除吗?",okText:"确定",cancelText:"取消"},on:{cancel:e.cancelTable,confirm:e.batchDeleteTable}},[i("ta-icon",{attrs:{type:"close-circle"}}),e._v(" 删除 ")],1)],1)],1),i("ta-button",[e._v(" 批量操作 "),i("ta-icon",{attrs:{type:"down"}})],1)],1)],1),i("div",{staticStyle:{height:"95%","margin-top":"10px"}},[i("ta-big-table",{ref:"xTable",attrs:{"auto-resize":!0,height:"auto",data:e.tableData,border:""},scopedSlots:e._u([{key:"bottomBar",fn:function(){return[i("ta-pagination",{ref:"gridPager",staticStyle:{"margin-top":"10px"},attrs:{url:"statisticsIndexWarn/queryStatisticsIndexWarnByPage",params:e.pageParams,"data-source":e.tableData,defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"]},on:{"update:dataSource":function(t){e.tableData=t},"update:data-source":function(t){e.tableData=t}}})]},proxy:!0}])},[i("ta-big-table-column",{attrs:{type:"checkbox",width:"60",align:"center"}}),i("ta-big-table-column",{attrs:{field:"indexId",title:"指标ID",align:"center"}}),i("ta-big-table-column",{attrs:{field:"calMethod","collection-type":"CALCULATION",title:"计算方式",align:"center"}}),i("ta-big-table-column",{attrs:{field:"warnCustomName",title:"预警自定义名称",align:"center"}}),i("ta-big-table-column",{attrs:{field:"warnValue",title:"预警值",align:"center"}}),i("ta-big-table-column",{attrs:{field:"effectiveBeginDate",title:"开始日期",align:"center"}}),i("ta-big-table-column",{attrs:{field:"effectiveEndDate",title:"结束日期",align:"center"}}),i("ta-big-table-column",{attrs:{field:"status","collection-type":"STATE",title:"有效状态",align:"center"}}),i("ta-big-table-column",{attrs:{title:"操作",align:"center",width:"200"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("ta-button",{staticStyle:{border:"none"},attrs:{size:"small",type:"link"},on:{click:function(t){return e.editTable(a)}}},[e._v("编辑")]),i("ta-popconfirm",{attrs:{title:"确定要删除吗?",okText:"确定",cancelText:"取消"},on:{cancel:function(t){return e.cancelTable(a)},confirm:function(t){return e.deleteTable(a)}}},[i("ta-button",{staticStyle:{border:"none"},attrs:{size:"small",type:"link"}},[e._v("删除")])],1),"1"==a.status?i("ta-button",{staticStyle:{border:"none"},attrs:{size:"small",type:"link"},on:{click:function(t){return e.stopTable(a)}}},[e._v("停用")]):i("ta-button",{staticStyle:{border:"none"},attrs:{size:"small",type:"link"},on:{click:function(t){return e.startTable(a)}}},[e._v("启用")])]}}])})],1)],1)])]),i("ta-modal",{attrs:{title:e.editTableTitle,width:600,visible:e.visible},on:{ok:e.handleOk,cancel:e.handleCancel}},[i("div",{staticClass:"modalBox"},[i("ta-form",{attrs:{layout:"horizontal",formLayout:!0,autoFormCreate:function(e){t.dictForm=e}}},[i("ta-form-item",{attrs:{span:24,label:"指标",labelWidth:"140",fieldDecoratorId:"indexId",require:{message:"请选择指标id!"}}},[i("ta-select",{staticStyle:{width:"100%"},attrs:{showSearch:"",options:e.indicatorArr,virtual:!0,"options-key":{label:"name",value:"id"},"allow-clear":""},on:{change:e.indexIdChange}})],1),i("ta-form-item",{attrs:{span:24,label:"计算方式",labelWidth:"140",fieldDecoratorId:"calMethod",require:{message:"请选择计算方式!"}}},[i("ta-select",{staticStyle:{width:"100%"},attrs:{showSearch:"","collection-type":"CALCULATION","allow-clear":""}})],1),i("ta-form-item",{attrs:{span:24,label:"预警自定义名称",labelWidth:"140",fieldDecoratorId:"warnCustomName"}},[i("ta-input",{staticStyle:{width:"100%"}})],1),i("ta-form-item",{attrs:{span:24,label:"预警值",labelWidth:"140",fieldDecoratorId:"warnValue",require:{message:"请输入预警值!"}}},[i("ta-input-number",{staticStyle:{width:"100%"}})],1),i("ta-form-item",{attrs:{labelWidth:"140",label:"开始日期",fieldDecoratorId:"effectiveBeginDate",span:24}},[i("ta-date-picker",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择","allow-clear":""}})],1),i("ta-form-item",{attrs:{labelWidth:"140",label:"结束日期",fieldDecoratorId:"effectiveEndDate",span:24}},[i("ta-date-picker",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择","allow-clear":""}})],1)],1),i("ta-form",{attrs:{layout:"horizontal",formLayout:!0,autoFormCreate:function(e){t.dictForm2=e}}},[e._l(e.addGroups,(function(t){return[i("ta-form-item",{attrs:{span:10,label:"维度",fieldDecoratorId:t.dimension,require:{message:"请输入维度!"}}},[i("ta-select",{staticStyle:{width:"100%"},attrs:{showSearch:"",options:e.dimensionArr,virtual:!0,"options-key":{label:"name",value:"id"},"allow-clear":""},on:{change:function(a){return e.dimensionChange(a,t)}}})],1),i("ta-form-item",{attrs:{span:12,label:"维度值",fieldDecoratorId:t.price,require:{message:"请输入维度值!"}}},[i("ta-tree-select",{attrs:{filterTreeNode:e.fnFilterTreeNode,showSearch:"",treeData:t.selectData,dropdownStyle:{maxHeight:"400px",overflow:"auto"},allowClear:""}})],1),i("ta-form-item",{attrs:{span:1}}),i("ta-form-item",{attrs:{span:1}},[i("ta-icon",{directives:[{name:"show",rawName:"v-show",value:e.addGroups.length>=1,expression:"addGroups.length >= 1"}],staticStyle:{cursor:"pointer"},attrs:{type:"minus-circle-o"},on:{click:function(a){return e.removeform(t)}}})],1)]})),i("ta-button",{staticStyle:{"margin-left":"40%"},attrs:{type:"dashed"},on:{click:e.addDictForm}},[i("ta-icon",{attrs:{type:"plus"}}),e._v(" 增加表单项 ")],1)],2)],1)])],1)},n=[],r=a(36797),s=a.n(r),o=a(88412),l={components:{TaTitle:o.Z},data:function(){return{tableData:[],searchFormData:{},editTableTitle:"新增",visible:!1,indicatorArr:[],dimensionArr:[],dimensionValueArr:[],formatData:"YYYY-MM-DD",addGroups:[],addGroupsArr:[],editTableId:""}},mounted:function(){this.init(),this.getIndicator()},methods:{moment:s(),init:function(){this.$refs.gridPager.loadData()},getIndicator:function(){var t=this;this.Base.submit(null,{url:"statisticsIndexWarn/queryIndexOptions"}).then((function(e){t.indicatorArr=e.data.result}))},deletePort:function(t){var e=this;this.Base.submit(null,{url:"statisticsIndexWarn/delBatchStatisticsIndexWarn",data:{statisticsIndexWarnIds:t}}).then((function(t){t.serviceSuccess&&e.init()}))},startStopPort:function(t,e){var a=this;this.Base.submit(null,{url:"statisticsIndexWarn/changeBatchStatisticsIndexWarnStatus",data:{statisticsIndexWarnIds:t,status:e}}).then((function(t){t.serviceSuccess&&a.init()}))},resetForm:function(){this.form.resetFields(),this.searchFormData={},this.init()},seachForm:function(){var t=this;this.searchFormData=this.form.getFieldsValue(),this.searchFormData.effectiveBeginDate=this.searchFormData.effectiveBeginDate?this.searchFormData.effectiveBeginDate.format(this.formatData):void 0,this.searchFormData.effectiveEndDate=this.searchFormData.effectiveEndDate?this.searchFormData.effectiveEndDate.format(this.formatData):void 0,this.$nextTick((function(){t.init()}))},pageParams:function(){return this.searchFormData},addInfo:function(){this.editTableTitle="新增",this.visible=!0},batchStartTable:function(){var t=this.$refs.xTable.getCheckboxRecords(),e=[];t.length>0?(t.forEach((function(t){e.push(t.statisticsIndexWarnId)})),e=e.join(","),this.startStopPort(e,"0")):this.$message.warning("请勾选相关内容后再进行操作！")},batchStopTable:function(){var t=this.$refs.xTable.getCheckboxRecords(),e=[];t.length>0?(t.forEach((function(t){e.push(t.statisticsIndexWarnId)})),e=e.join(","),this.startStopPort(e,"1")):this.$message.warning("请勾选相关内容后再进行操作！")},batchDeleteTable:function(){var t=this.$refs.xTable.getCheckboxRecords(),e=[];t.length>0?(t.forEach((function(t){e.push(t.statisticsIndexWarnId)})),e=e.join(","),this.deletePort(e)):this.$message.warning("请勾选相关内容后再进行操作！")},editTable:function(t){var e=this;this.editTableId=t.statisticsIndexWarnId,this.editTableTitle="编辑",this.Base.submit(null,{url:"statisticsIndexWarn/queryDimensionOptions",data:{indexId:t.indexId}}).then((function(a){e.dimensionArr=a.data.result,e.visible=!0,e.$nextTick((function(){if(null!=t.effectiveBeginDate&&(t.effectiveBeginDate=t.effectiveBeginDate.toString(),e.dictForm.setFieldsMomentValue({effectiveBeginDate:t.effectiveBeginDate})),null!=t.effectiveEndDate&&(t.effectiveEndDate=t.effectiveEndDate.toString(),e.dictForm.setFieldsMomentValue({effectiveEndDate:t.effectiveEndDate})),e.dictForm.setFieldsValue({indexId:t.indexId,calMethod:t.calMethod,warnCustomName:t.warnCustomName,warnValue:t.warnValue}),t.warnDimensionList.length>0){var a={};t.warnDimensionList.forEach((function(t,i){e.addDictForm(),e.Base.submit(null,{url:"statisticsIndexWarn/queryDimensionNodeOptions",data:{dimensionId:t.dimensionId}}).then((function(n){e.addGroups.forEach((function(r,s){i==s&&(r.selectData=n.data.result,a[r.dimension]=t.dimensionId+"",a[r.price]=t.dimensionValue),e.dictForm2.setFieldsValue(a)}))}))}))}}))}))},deleteTable:function(t){this.deletePort(t.statisticsIndexWarnId)},cancelTable:function(){},stopTable:function(t){this.startStopPort(t.statisticsIndexWarnId,t.status)},startTable:function(t){this.startStopPort(t.statisticsIndexWarnId,t.status)},indexIdChange:function(t){void 0!=t&&this.getDimension(t)},radomName:function(t){return Number(Math.random().toString().substr(3,t)+Date.now()).toString(36)},scrollToBottom:function(){this.$nextTick((function(){var t=document.querySelector(".modalBox");t.scrollTop=t.scrollHeight}))},addDictForm:function(){var t=this;this.dictForm2.validateFields((function(e){if(!e)if(t.dimensionArr.length>0){var a=t.radomName(36);t.addGroups.push({dimension:"dimensionId_"+a,price:"dimensionValue_"+a,formId:a,selectData:[]}),t.addGroupsArr.push(a),t.scrollToBottom()}else t.$message.warning("请先选择指标ID")}))},removeform:function(t){this.addGroups=this.addGroups.filter((function(e){return t.dimension!=e.dimension})),this.addGroupsArr=this.addGroupsArr.filter((function(e){return t.formId!=e}))},dimensionChange:function(t,e){this.dictForm2.resetFields(e.price),this.getDimensionValue(t,e)},dimensionSearch:function(t,e){},fnFilterTreeNode:function(t,e){return-1!==e.title.indexOf(t)},handleOk:function(){var t=this,e=[],a=!1,i=!1;if(this.dictForm.validateFields((function(i){if(i)a=!1;else{var n=t.dictForm.getFieldsValue();n.effectiveBeginDate=n.effectiveBeginDate?n.effectiveBeginDate.format(t.formatData):void 0,n.effectiveEndDate=n.effectiveEndDate?n.effectiveEndDate.format(t.formatData):void 0,e=n,a=!0}})),this.dictForm2.validateFields((function(a){if(a)i=!1;else{var n=t.dictForm2.getFieldsValue(),r=Object.getOwnPropertyNames(n),s=[];t.addGroupsArr.forEach((function(t){var e={};r.forEach((function(a){var i=a.indexOf("_"),r=a.substring(0,i),s=a.substring(i+1);t===s&&(e[r]=n[a])})),s.push(e)})),e.warnDimensionList=s,i=!0}})),a&&i){var n="";"编辑"==this.editTableTitle?(n="statisticsIndexWarn/updateStatisticsIndexWarn",e.statisticsIndexWarnId=this.editTableId):n="statisticsIndexWarn/addStatisticsIndexWarn",this.Base.submit(null,{url:n,data:e,autoQs:!1}).then((function(e){e.serviceSuccess&&(t.visible=!1,t.init(),t.dictForm.resetFields(),t.addGroups=[],t.addGroupsArr=[],t.dimensionArr=[])}))}},handleCancel:function(){this.dictForm.resetFields(),this.addGroups=[],this.addGroupsArr=[],this.dimensionArr=[],this.visible=!1},getDimension:function(t){var e=this;this.Base.submit(null,{url:"statisticsIndexWarn/queryDimensionOptions",data:{indexId:t}}).then((function(t){e.dimensionArr=t.data.result}))},getDimensionValue:function(t,e){var a=this;this.Base.submit(null,{url:"statisticsIndexWarn/queryDimensionNodeOptions",data:{dimensionId:t}}).then((function(t){a.addGroups.forEach((function(a){a.dimension==e.dimension&&(a.selectData=t.data.result)}))}))}}},c=l,d=a(1001),u=(0,d.Z)(c,i,n,!1,null,"6b9a1e88",null),f=u.exports},36766:function(t,e,a){var i=a(66586);e["Z"]=i.Z},26263:function(t,e,a){a.d(e,{s:function(){return i},x:function(){return n}});var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},n=[]},66586:function(t,e){var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};e["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}}}]);
(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[5169],{88412:function(t,e,a){"use strict";var r=a(26263),i=a(36766),n=a(1001),o=(0,n.Z)(i.Z,r.s,r.x,!1,null,"5e7ef0ae",null);e["Z"]=o.exports},98236:function(t,e,a){"use strict";a.r(e),a.d(e,{default:function(){return b}});var r=function(){var t=this,e=this,a=e.$createElement,r=e._self._c||a;return r("div",{staticClass:"fit"},[r("ta-border-layout",{attrs:{"footer-cfg":{showBorder:!1},layout:{header:"110px",footer:"0px"},showPadding:!0}},[r("div",{attrs:{slot:"header"},slot:"header"},[r("ta-title",{attrs:{title:"查询条件"}}),r("ta-form",{staticStyle:{"padding-top":"5px"},attrs:{autoFormCreate:function(e){t.baseInfoForm=e},col:2,formLayout:!0}},[r("ta-form-item",{attrs:{"label-col":{span:8},span:4,"wrapper-col":{span:16},"field-decorator-id":"dateFlag","init-value":"audit"}},[r("span",{attrs:{slot:"label"},slot:"label"},[e._v(" 时间类型 ")]),r("ta-radio-group",{staticStyle:{width:"100%"}},[r("ta-radio-button",{staticStyle:{width:"50%","text-align":"center"},attrs:{value:"audit"}},[e._v("审核时间")]),r("ta-radio-button",{staticStyle:{width:"50%","text-align":"center"},attrs:{value:"outpa"}},[e._v("出院时间")])],1)],1),r("ta-form-item",{attrs:{fieldDecoratorOptions:{rules:[{required:!0,message:"必须选取日期"}]},"init-value":e.rangeValue,"label-col":{span:7},required:!0,span:4,"wrapper-col":{span:17},fieldDecoratorId:"allDate",label:"时间范围"}},[r("ta-range-picker",{attrs:{"allow-one":!0,format:"YYYY-MM-DD"}},[r("ta-icon",{attrs:{slot:"suffixIcon",type:""},slot:"suffixIcon"})],1)],1),r("ta-form-item",{attrs:{"label-col":{span:8},span:4,"wrapper-col":{span:16},"field-decorator-id":"akb020",label:"院区标识"}},[r("ta-select",{attrs:{options:e.akb020List,disabled:e.paramsDisable.akb020,allowClear:"",placeholder:"院区选择"}})],1),r("ta-form-item",{attrs:{"label-col":{span:8},span:4,"wrapper-col":{span:16},"field-decorator-id":"ruleType",label:"规则分类"}},[r("ta-tree-select",{attrs:{allowClear:"",showSearch:"",placeholder:"规则分类",dropdownStyle:{maxHeight:"400px",overflow:"auto"},treeData:e.treeData,treeDataSimpleMode:e.treeData}})],1),r("ta-form-item",{attrs:{"label-col":{span:8},span:4,"wrapper-col":{span:16},fieldDecoratorId:"patientInfo",label:"患者信息"}},[r("ta-input",{attrs:{placeholder:"请输入住院号、就诊号或姓名"}})],1),r("ta-form-item",{attrs:{"label-col":{span:8},span:4,"wrapper-col":{span:16},label:" "}},[r("ta-button",{attrs:{icon:"search",type:"primary"},on:{click:e.fnQuery}},[e._v("查询")]),r("ta-button",{attrs:{icon:"redo",type:"default"},on:{click:e.fnReset}},[e._v("重置")])],1)],1)],1),r("div",{staticClass:"fit",staticStyle:{height:"88%"}},[r("ta-title",{staticStyle:{width:"50%"},attrs:{title:"查询结果"}}),r("ta-big-table",{ref:"Table",attrs:{data:e.userList,"auto-resize":"",border:"","empty-text":"-",height:"100%","highlight-current-row":"","highlight-hover-row":"",resizable:"","show-overflow":""}},[r("ta-big-table-column",{attrs:{align:"center","header-align":"center",width:"50",sortable:"",title:"序号",type:"seq"}}),r("ta-big-table-column",{attrs:{align:"left",field:"ruletype","header-align":"center","min-width":"140",sortable:"",title:"规则分类"}}),r("ta-big-table-column",{attrs:{align:"left",field:"aaa167","header-align":"center","min-width":"140",sortable:"",title:"规则名称"}}),r("ta-big-table-column",{attrs:{align:"left",field:"ykz266","header-align":"center","min-width":"300",title:"异常内容"}}),r("ta-big-table-column",{attrs:{align:"center",sortable:"",field:"akc191","header-align":"center","min-width":"140",title:"患者住院号"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[r("ta-button",{attrs:{size:"small",type:"link"},on:{click:function(t){return e.cellClickEvent(a)}}},[r("span",{staticStyle:{color:"#00bcff","text-decoration":"underline"}},[e._v(e._s(a.akc191))])])]}}])}),r("ta-big-table-column",{attrs:{align:"center",field:"aac003","header-align":"center","min-width":"140",sortable:"",title:"患者姓名"}}),r("template",{slot:"bottomBar"},[r("ta-button",{staticStyle:{float:"right","margin-top":"20px"},attrs:{icon:"download",type:"primary"},on:{click:e.exportTable}},[e._v("导出 ")]),r("ta-pagination",{ref:"gridPager",staticStyle:{float:"right","margin-top":"20px","margin-right":"10px"},attrs:{defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"],"data-source":e.userList,params:e.infoPageParams,url:"reportStatistics/queryDscgErrorCostPatientInfo"},on:{"update:dataSource":function(t){e.userList=t},"update:data-source":function(t){e.userList=t}}})],1)],2)],1)])],1)},i=[],n=a(66347),o=a(48534),l=a(95082),s=(a(36133),a(88412)),c=a(36797),u=a.n(c),f=a(22722),d=a(55115);a(92566);d.w3.prototype.Base=Object.assign(d.w3.prototype.Base,(0,l.Z)({},f.Z));var p={name:"dscgErrorCostPatientInfo",components:{TaTitle:s.Z},data:function(){return{userList:[],akb020List:[],permissions:{},paramsDisable:{akb020:!1},treeData:[],rangeValue:[this.Base.getMoment((new Date).toISOString().slice(0,8)+"-01","YYYY-MM-DD"),this.Base.getMoment((new Date).toISOString().slice(0,10),"YYYY-MM-DD")]}},mounted:function(){var t=this;return(0,o.Z)(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,PermissionJs.permissionCheck();case 2:t.permissions=e.sent,t.fnQueryHos(),t.getTreeData(),t.fnQuery();case 6:case"end":return e.stop()}}),e)})))()},methods:{moment:u(),fnQueryHos:function(){var t=this,e={url:"/miimCommonRead/queryHospRpcList",showPageLoading:!1,autoValid:!0},a={successCallback:function(e){t.akb020List=e.data.resultData,t.permissions&&t.permissions.akb020Set.size>0&&(t.paramsDisable.akb020=!0,t.akb020List=t.akb020List.filter((function(e){return t.permissions.akb020Set.has(e.value)})),t.baseInfoForm.setFieldsValue({akb020:t.akb020List[0].value})),t.akb020=t.akb020List[0].value},failCallback:function(e){t.$message.error("医院数据加载失败")}};this.Base.submit(null,e,a)},getTreeData:function(){var t=this;Base.submit(null,{url:"mttRuleCustom/getRuleTreeData",showPageLoading:!1}).then((function(e){t.treeData=e.data.list,t.treeData.length>0&&(t.treeData[0].disabled=!0,t.treeData[0].children.forEach((function(t){t.disabled=!0})))}))},cellClickEvent:function(t){var e=this.baseInfoForm.getFieldsValue();e.akb020||(e.akb020=""),this.Base.openTabMenu({id:Math.floor(901*Math.random())+100,name:"住院患者审核疑点查询",url:"querycommon.html#/inpatientGather?ruletype=".concat(t.ruletype,"&aaz319=").concat(t.aaz319,"&akc191=").concat(t.akc191,"&aae500=7&params=").concat(JSON.stringify(e)),refresh:!1})},exportTable:function(){var t=this,e=this.baseInfoForm.getFieldsValue();if(void 0===e.allDate||e.allDate.length<2)this.$message.error("请选择时间范围！");else{e=this.infoPageParams();var a,r=[],i=this.$refs.Table.getColumns(),o=(0,n.Z)(i);try{for(o.s();!(a=o.n()).done;){var l=a.value;"序号"!==l.title&&"患者明细"!==l.title&&r.push({header:l.title,key:l.property,width:20})}}catch(s){o.e(s)}finally{o.f()}this.Base.submit(null,{url:"reportStatistics/exportDscgErrorCostPatientInfo",data:e,autoValid:!1},{successCallback:function(e){var a=e.data.data,i={fileName:"出院异常收费患者明细结果表",sheets:[{name:"worksheet1",column:{complex:!1,columns:r},rows:a}]};t.Base.generateExcel(i)},failCallback:function(e){t.$message.error("数据加载失败")}})}},formatAmount:function(t){return t.toFixed(2).replace(/\d(?=(\d{3})+\.)/g,"$&,")},moneyFormat:function(t){var e=t.cellValue;return"--"==e?e:this.formatAmount(e)},ratioFormat:function(t){var e=t.cellValue;return 0==e?"0":e+"%"},fnReset:function(){this.baseInfoForm.resetFields(),this.fnQuery()},infoPageParams:function(){var t=this.baseInfoForm.getFieldsValue();return t.startDate=t.allDate[0].format("YYYY-MM-DD HH:mm:ss"),t.endDate=t.allDate[1].format("YYYY-MM-DD HH:mm:ss"),t},fnQuery:function(){var t=this,e=this.baseInfoForm.getFieldValue("allDate");!e||e[0]&&e[1]?this.$nextTick((function(){t.$refs.gridPager.loadData((function(t){}))})):this.$message.error("请选择时间范围！")},fnCustomHeaderCell:function(t){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px",fontWeight:"bold"}}},filterOption:function(t,e){return e.componentOptions.children[0].text.indexOf(t)>=0}}},m=p,h=a(1001),g=(0,h.Z)(m,r,i,!1,null,"f69a5d0e",null),b=g.exports},36766:function(t,e,a){"use strict";var r=a(66586);e["Z"]=r.Z},26263:function(t,e,a){"use strict";a.d(e,{s:function(){return r},x:function(){return i}});var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},i=[]},66586:function(t,e){"use strict";var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};e["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}},55382:function(){},61219:function(){}}]);
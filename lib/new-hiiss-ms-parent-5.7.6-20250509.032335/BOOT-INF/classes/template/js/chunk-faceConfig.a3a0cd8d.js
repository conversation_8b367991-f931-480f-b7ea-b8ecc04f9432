"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[6716],{95278:function(e,l){l["Z"]={basePath:"/hiiss-backend",indexPageConfig:{menuType:"leftTop",menuLeftStyle:"dropdown",dropdownOpenMenu:!1,headerHeight:"50px",headerTheme:"base",leftTheme:"base",leftWidth:"210px",logoWidth:"210px",leftCloseWidth:"50px",barType:"tab",logoTitle:"医保智能监管系统",logoImage:"logo.png",notPageTool:["worktable","roleWorktableTemplateModify"],closeTabCallBackModules:[],worktable:{name:"",module:"workTablePage.html",part:"page",prefix:""},srcPrefix:""},resDataConfig:{serviceSuccess:"serviceSuccess",serviceSuccessRule:!0,errors:"errors",message:"message",redirectUrl:"redirectUrl",submitParameter:{},frontUrl:null,collectionUrl:"codetable/getCode",collectionUrlList:"codetable/getCodeBatch",cryptoCfg:{reqDataLevel:0}},onlineTheme:!0,defaultTheme:"ybblue",defaultDarkMode:!1,defaultLocale:"zh_CN",bigScreen:{carouselInner:6e4,refreshOut:12e4},approvalHandleType:"base",ruleFindConfigType:"base",ruleFindModifyType:"base",tokenPath:!1}}}]);
(typeof window=='undefined'?global:window).tc_cfg_09272470305613778={"url":"css/theme-colors-ea095f1b.css","colors":["#1b65b9","#3274c0","#4984c7","#5f93ce","#76a3d5","#8db2dc","#a4c1e3","#bbd1ea","#d1e0f1","#ebf4fa","#bbd9ed","#8dbce0","#639fd4","#3e82c7","#1b65b9","#0f4894","#052f6e","#001a47","#000b21","27,101,185"]};

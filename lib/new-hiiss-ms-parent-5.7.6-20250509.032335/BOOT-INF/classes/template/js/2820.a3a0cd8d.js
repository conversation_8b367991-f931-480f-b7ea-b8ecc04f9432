"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[2820],{64964:function(t,e,a){a.d(e,{Z:function(){return c}});var i=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("div",{staticStyle:{height:"490px"}},[i("div",{staticClass:"title"},[e._v("新增")]),i("ta-form",{attrs:{autoFormCreate:function(e){t.addForm=e}}},[e._l(e.editColumns,(function(t,e){return[!t.collectionType?i("ta-form-item",{attrs:{fieldDecoratorId:t.dataIndex,label:t.title,require:{enable:!!t.require,message:"不能为空"}}},[i("ta-input",{staticStyle:{width:"80%"}})],1):i("ta-form-item",{attrs:{"init-value":"MAPE888"==t.collectionType?"1":void 0,fieldDecoratorId:t.dataIndex,label:t.title,require:{enable:!!t.require,message:"不能为空"}}},[i("ta-select",{staticStyle:{width:"80%"},attrs:{collectionType:t.collectionType}})],1)]})),i("ta-button",{staticStyle:{"margin-left":"45%"},attrs:{type:"success"},on:{click:e.save}},[e._v("保存")])],2)],1)},n=[],o={name:"addOne",components:{},props:{column:{type:Array}},data:function(){return{editColumns:[]}},mounted:function(){this.editColumns=this.column.filter((function(t){return"ykz042"!=t.dataIndex&&"ykz010"!=t.dataIndex&&"enduser"!=t.dataIndex&&"enddate"!=t.dataIndex&&"stadate"!=t.dataIndex&&"stauser"!=t.dataIndex&&"ykz095"!=t.dataIndex}))},methods:{save:function(){var t=this;this.addForm.validateFields((function(e){e||t.$emit("fnAddOne",t.addForm.getFieldsValue())}))}}},r=o,s=a(1001),l=(0,s.Z)(r,i,n,!1,null,"f405d334",null),c=l.exports},52820:function(t,e,a){a.r(e),a.d(e,{default:function(){return Y}});var i=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("ta-border-layout",{staticStyle:{position:"relative"},attrs:{"center-cfg":{cssClass:"ruleConfig"},"show-padding":!1,layout:{footer:"0px"}}},[i("ta-col",{attrs:{span:21}},[i("ta-card",{attrs:{bordered:!1,id:"ruleProperty"}},[i("div",{staticClass:"title"},[e._v("规则内容")]),i("ta-form",{attrs:{"auto-form-create":function(e){return t.rulePropertyForm=e}}},[i("ta-row",[i("ta-col",{attrs:{span:9}},[i("ta-form-item",{attrs:{"field-decorator-id":"ape801",label:"规则大类",require:{message:"请选择规则大类"}}},[i("ta-select",e._l(e.bigRuleList,(function(t,a){return i("ta-select-option",{key:a,attrs:{value:t.ape801}},[e._v(" "+e._s(t.aaa166)+" ")])})),1)],1)],1),i("ta-col",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}],attrs:{span:9}},[i("ta-form-item",{attrs:{"init-value":"C",disabled:"1"!=e.step,"field-decorator-id":"ruleLv",label:"规则级别",require:{message:"请选择规则类别"}}},[i("ta-select",{attrs:{"collection-type":"M_RULE_LV"}})],1)],1),i("ta-col",{attrs:{span:9}},[i("ta-form-item",{attrs:{disabled:"1"!=e.step,"field-decorator-id":"ykz199",label:"规则类别",require:{message:"请选择规则类别"}}},[i("ta-select",{attrs:{"collection-type":"MYKZ199"}})],1)],1),0==e.blnAdd?[i("ta-col",{attrs:{span:9}},[i("ta-form-item",{attrs:{disabled:!0,"field-decorator-id":"ykz277",label:"规则编码"}},[i("ta-input")],1)],1),i("ta-col",{attrs:{span:9}},[i("ta-form-item",{attrs:{disabled:!0,"field-decorator-id":"rulever",label:"规则版本"}},[i("ta-input")],1)],1),i("ta-col",{attrs:{span:9}},[i("ta-form-item",{attrs:{disabled:!0,"field-decorator-id":"operationTime",label:"更新日期"}},[i("ta-input")],1)],1)]:e._e(),i("ta-col",{attrs:{span:9}},[i("ta-form-item",{attrs:{require:{message:"请选择违规程度"},"field-decorator-id":"ruleSevDeg",label:"违规程度"}},[i("ta-select",{attrs:{"collection-type":"M_SEV_DEG"}})],1)],1),i("ta-col",{attrs:{span:9}},[i("ta-form-item",{attrs:{"field-decorator-id":"volaQualCodg",label:"违规定性"}},[i("ta-select",{attrs:{"show-search":!0}},e._l(e.volaQualList,(function(t,a){return i("ta-select-option",{key:a,attrs:{value:t.code}},[e._v(" "+e._s(t.label)+" ")])})),1)],1)],1),i("ta-col",{attrs:{span:9}},[i("ta-form-item",{attrs:{disabled:"1"!=e.step,"field-decorator-id":"ykz167",label:"审核对象",require:{message:"请选择审核对象"}}},[i("ta-select",{attrs:{"collection-type":"MYKZ167"}})],1)],1),i("ta-col",{attrs:{span:9}},[i("ta-form-item",{attrs:{"field-decorator-id":"ykz285",label:"规则来源"}},[i("ta-input")],1)],1),i("ta-col",{attrs:{span:18}},[i("ta-form-item",{attrs:{disabled:"1"!=e.step,require:{message:"请输入知识元"},fieldDecoratorId:"ykz018",label:"知识元","label-col":{span:3},"wrapper-col":{span:21}}},[i("ta-auto-complete",{attrs:{placeholder:"提交规则后，无法修改知识元","option-label-prop":"text"},on:{search:e.knowSearch}},[i("ta-textarea"),i("template",{slot:"dataSource"},e._l(e.knowDataSource,(function(t){return i("ta-select-option",{key:t.knowmx,attrs:{text:t.knowmx,value:t.knowmx}},[e._v(" "+e._s(t.knowmx)+" ")])})),1)],2)],1)],1)],2)],1)],1),i("ta-card",{attrs:{bordered:!1,id:"ruleid"}},[i("div",{staticClass:"title"},[e._v("启用触发场景")]),i("ta-row",[i("ta-col",{attrs:{span:18}},[i("ta-table",{ref:"ruleidTable",attrs:{showCheckbox:e.blnShowCheckbox,"row-key":"ruleid",columns:e.ruleidColumn,size:"small","data-source":e.ruleidList,bordered:"",scroll:{x:"100%",y:200}}})],1)],1)],1),"3"==e.step?[e.ykz167&&"2"!=e.ykz167?i("ta-card",{attrs:{bordered:!1,id:"item-config"}},[i("div",{staticClass:"title"},[e._v("规则条目")]),i("ta-row",[i("ta-col",{attrs:{span:18}},[i("ta-col",{attrs:{span:12}},[i("ta-button-group",[i("ta-button",{on:{click:e.openAddItem}},[e._v("新增")]),i("ta-popconfirm",{attrs:{title:"确定从规则中删除？"},on:{confirm:e.deleteItem}},[i("ta-button",[e._v("删除")])],1),i("ta-popover",{attrs:{placement:"bottom",width:"350",trigger:"click"}},[i("ta-form",{attrs:{autoFormCreate:function(e){t.itemChangeForm=e}}},[i("ta-row",[i("ta-col",{attrs:{span:this.highFormLayout.checkboxSpan}},[i("ta-form-item",{attrs:{fieldDecoratorId:"ckApe864",labelCol:e.formItemLayout.labelCol,wrapperCol:e.formItemLayout.wrapperClo}},[i("ta-checkbox")],1)],1),i("ta-col",{attrs:{span:this.highFormLayout.firstSpan}},[i("ta-form-item",{attrs:{fieldDecoratorId:"ape864",labelCol:e.formItemLayout.labelCol,wrapperCol:e.formItemLayout.wrapperClo}},[i("ta-input",{staticClass:"changeInput",attrs:{placeholder:"分组编号"}})],1)],1)],1),i("ta-row",[i("ta-col",{attrs:{span:this.highFormLayout.checkboxSpan}},[i("ta-form-item",{attrs:{fieldDecoratorId:"ckApe865",labelCol:e.formItemLayout.labelCol,wrapperCol:e.formItemLayout.wrapperClo}},[i("ta-checkbox")],1)],1),i("ta-col",{attrs:{span:this.highFormLayout.firstSpan}},[i("ta-form-item",{attrs:{fieldDecoratorId:"ape865",labelCol:e.formItemLayout.labelCol,wrapperCol:e.formItemLayout.wrapperClo}},[i("ta-input",{staticClass:"changeInput",attrs:{placeholder:"分组名称"}})],1)],1)],1),i("ta-row",[i("ta-col",{attrs:{span:this.highFormLayout.checkboxSpan}},[i("ta-form-item",{attrs:{fieldDecoratorId:"ckApe891",labelCol:e.formItemLayout.labelCol,wrapperCol:e.formItemLayout.wrapperClo}},[i("ta-checkbox")],1)],1),i("ta-col",{attrs:{span:this.highFormLayout.firstSpan}},[i("ta-form-item",{attrs:{fieldDecoratorId:"ape891",labelCol:e.formItemLayout.labelCol,wrapperCol:e.formItemLayout.wrapperClo}},[i("ta-input-number",{staticClass:"changeInput",attrs:{min:0,max:99,step:.1,placeholder:"频次倍数(乘法)"}})],1)],1)],1),i("ta-row",[i("ta-col",{attrs:{span:this.highFormLayout.checkboxSpan}},[i("ta-form-item",{attrs:{fieldDecoratorId:"ckApe892",labelCol:e.formItemLayout.labelCol,wrapperCol:e.formItemLayout.wrapperClo}},[i("ta-checkbox")],1)],1),i("ta-col",{attrs:{span:this.highFormLayout.firstSpan}},[i("ta-form-item",{attrs:{fieldDecoratorId:"ape892",labelCol:e.formItemLayout.labelCol,wrapperCol:e.formItemLayout.wrapperClo}},[i("ta-input-number",{staticClass:"changeInput",attrs:{min:0,max:99,step:.1,placeholder:"频次倍数(除法)"}})],1)],1)],1),i("ta-button",{on:{click:e.changeItem}},[e._v("确认")])],1),i("ta-button",{attrs:{slot:"reference"},slot:"reference"},[e._v("更改")])],1),i("mxTemplateButton"),i("ta-button",{on:{click:e.importMx}},[e._v("导入")])],1)],1),i("ta-col",{attrs:{span:12}},[i("div",{staticStyle:{float:"right"}},[e._v(" 关键字: "),i("ta-input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入三目名称或编码"},model:{value:e.itemSearchText,callback:function(t){e.itemSearchText=t},expression:"itemSearchText"}}),i("ta-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary"},on:{click:e.onItemSearch}},[e._v("查询")])],1)]),i("ta-col",{attrs:{span:24}},[i("ta-big-table",{ref:"itemTable",attrs:{data:e.itemCacheList,size:"small","big-data-checkbox":"","header-drag-style":"","use-virtual":"",border:"full",heightSwitch:!0,height:386,"row-height":20}},[i("ta-big-table-column",{attrs:{type:"checkbox",width:"55"}}),i("ta-big-table-column",{attrs:{type:"seq",width:"60",title:"序号",fixed:""}}),e._l(e.itemColumns,(function(t){return i("ta-big-table-column",{key:t.dataIndex,attrs:{align:t.align,resizable:"","show-header-overflow":"","show-overflow":t.overflowTooltip,field:t.dataIndex,title:t.title,fixed:t.fixed,width:t.width,collectionType:t.collectionType}})}))],2)],1)],1)],1)],1):e._e(),i("ta-card",{attrs:{bordered:!1,id:"logic-path"}},[i("ta-row",[i("ta-col",{attrs:{span:18}},[i("div",{staticClass:"title"},[e._v("审核逻辑 "),i("ta-popover",{attrs:{placement:"bottom",width:"400",trigger:"click"}},[i("div",{staticStyle:{color:"red","margin-bottom":"10px"}},[e._v(" 请输入需要复制的规则树的规则唯一id>> ")]),i("ta-form",{attrs:{"auto-form-create":function(e){return t.treeForm=e}}},[i("ta-form-item",{attrs:{require:{message:"请输入规则版本id"},"field-decorator-id":"ykz277",label:"规则唯一id"}},[i("ta-input")],1)],1),i("ta-button",{staticStyle:{"margin-left":"45%"},attrs:{type:"primary"},on:{click:e.doCopyTree}},[e._v("复制")]),i("ta-button",{staticStyle:{float:"right","margin-right":"20px"},attrs:{slot:"reference",type:"primary"},slot:"reference"},[e._v("复制树")])],1)],1)])],1),i("div",[i("span",{staticStyle:{color:"red","padding-left":"30px"}},[e._v("提示：同级关系为“或”，其他关系为“和”")]),i("a-tree",{attrs:{defaultExpandAll:!0,"show-line":!0,"show-icon":!1,treeData:e.treeData,expandedKeys:e.expandTreeKeys,draggable:!0},on:{"update:treeData":function(t){e.treeData=t},"update:tree-data":function(t){e.treeData=t},"update:expandedKeys":function(t){e.expandTreeKeys=t},"update:expanded-keys":function(t){e.expandTreeKeys=t},drop:e.onTreeDrop},scopedSlots:e._u([{key:"title",fn:function(t){return[i("div",{staticClass:"tree-title",staticStyle:{"min-width":"100px"}},["3"==t.type?[e._v(" 描述: ")]:e._e(),e._v(" "+e._s(t.title)+" "),["1"==t.ykz027?[i("ta-tag",{attrs:{color:"blue",size:"small"}},[e._v("满足则不通过")])]:e._e(),"2"==t.type&&"1"!=t.relative?[i("ta-icon",{staticStyle:{color:"red","font-size":"20px","margin-left":"5px"},attrs:{type:"info-circle",theme:"filled"}})]:e._e()],i("span",{staticClass:"tree-tips"},["2"==t.type&&"1"!=t.relative?i("span",{staticStyle:{"line-height":"20px"}},[e._v(" 该节点未配置节点内容 ")]):e._e(),i("ta-icon",{staticStyle:{color:"#67c23a","font-size":"20px","margin-left":"5px"},attrs:{size:"large",type:"plus-circle",theme:"filled"},on:{click:function(a){return e.doAdd(t)}}}),"2"==t.type||"3"==t.type?i("ta-icon",{staticStyle:{color:"#6ca3dc","font-size":"20px","margin-left":"5px"},attrs:{type:"edit",theme:"filled"},on:{click:function(a){return e.doEdit(t)}}}):e._e(),"0"!=t.type?[i("ta-icon",{staticStyle:{color:"red","font-size":"20px","margin-left":"5px"},attrs:{type:"close"},on:{click:function(a){return e.doDelete(t)}}})]:e._e()],2)],2)]}}],null,!1,911360081)},[i("a-icon",{attrs:{slot:"switcherIcon",type:"down"},slot:"switcherIcon"})],1)],1)],1),i("ta-card",{attrs:{bordered:!1,id:"logic-detail"}},[i("div",{staticClass:"title"},[e._v("详细配置")]),i("ta-row",[i("ta-col",{attrs:{span:18}},[i("ta-icon",{staticStyle:{color:"#1890ff"},attrs:{type:"right"}}),e._v(" 节点概述 "),i("div",{staticStyle:{color:"red"}},[e._v("提示：使用鼠标选中节点以配置节点内容")]),i("ta-table",{ref:"nodeTable",attrs:{"have-sn":"","row-key":"key",columns:e.nodeColumns,size:"small","data-source":e.nodeList,bordered:"",scroll:{x:"100%",y:386},"custom-row":e.nodeCustomRow},on:{"update:dataSource":function(t){e.nodeList=t},"update:data-source":function(t){e.nodeList=t}},scopedSlots:e._u([{key:"relative",fn:function(t,a){return i("div",{},["1"==a.relative?i("span",[e._v("已配置")]):i("span",{staticStyle:{color:"red"}},[e._v("未配置")])])}},{key:"attrRelative",fn:function(t,a){return i("div",{},[i("ta-popover",{attrs:{placement:"right",width:"450",trigger:"hover"},on:{show:function(t){return e.loadAttrDesc(a)}}},[i("div",{attrs:{slot:"content"},slot:"content"},e._l(a.attrList,(function(t,n){return a.attrList?i("div",{key:n,staticStyle:{"padding-top":"10px","padding-bottom":"10px"},style:{borderBottom:n!=a.attrList.length-1?"1px dashed black":""}},[i("div",[e._v("属性名: "+e._s(t.ykz013))]),i("div",[e._v("属性值: "+e._s(t.ykz048))]),i("div",[e._v("比较值: "+e._s(t.ykz049))])]):e._e()})),0),i("span",{attrs:{slot:"reference"},slot:"reference"},[e._v("已配置")])])],1)}}],null,!1,4256275776)})],1)],1),i("div",{staticStyle:{"margin-top":"10px"},attrs:{id:"节点内容"}},[i("ta-icon",{staticStyle:{color:"#1890ff"},attrs:{type:"right"}}),e._v(" 节点内容 "),i("ta-row",[i("ta-col",{staticStyle:{"margin-top":"10px"},attrs:{span:18}},[i("ta-col",{attrs:{span:12}},[e.clickedNode?i("span",{staticStyle:{"margin-left":"10px","line-height":"35px"}},[e._v(e._s(e.clickedNode.ykz002+":"+e.clickedNode.ykz010))]):e._e()]),i("ta-col",{attrs:{span:12}},[i("div",{staticStyle:{float:"right"}},[e._v(" 关键字: "),i("ta-input",{staticStyle:{width:"200px"},attrs:{disabled:!e.clickedNode,placeholder:"请输入编码或名称"},model:{value:e.contentSearchText,callback:function(t){e.contentSearchText=t},expression:"contentSearchText"}}),i("ta-button",{staticStyle:{"margin-left":"10px"},attrs:{disabled:!e.clickedNode,type:"primary"},on:{click:e.pageNodeContent}},[e._v("查询 ")])],1)]),i("ta-col",{staticStyle:{"margin-top":"10px"},attrs:{span:24}},[i("ta-table",{ref:"contentTable",attrs:{"show-checkbox":!0,"have-sn":"","row-key":"rid",columns:e.contentColumns,size:"small","data-source":e.contentList,bordered:"",scroll:{x:"100%",y:386}},on:{"update:dataSource":function(t){e.contentList=t},"update:data-source":function(t){e.contentList=t}}}),i("ta-pagination",{ref:"contentPager",staticClass:"page",attrs:{dataSource:e.contentList,params:e.contentPageParam,url:"mttRuleConfig/pageNodeContent",defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"]},on:{"update:dataSource":function(t){e.contentList=t},"update:data-source":function(t){e.contentList=t}}})],1)],1)],1)],1)],1)]:e._e(),e.blnAdd?e._e():i("ta-card",{attrs:{bordered:!1,id:"operate_log"}})],2),i("ta-col",{attrs:{span:3}},[i("ta-anchor",{attrs:{"get-container":e.getContainer,offsetTop:25}},[i("ta-anchor-link",{attrs:{href:"#ruleProperty",title:"规则内容"}}),i("ta-anchor-link",{attrs:{href:"#ruleid",title:"启用触发场景"}}),"3"==e.step?[e.ykz167&&"2"!=e.ykz167?i("ta-anchor-link",{attrs:{href:"#item-config",title:"规则条目"}}):e._e(),i("ta-anchor-link",{attrs:{href:"#logic-path",title:"审核逻辑"}}),i("ta-anchor-link",{attrs:{href:"#logic-detail",title:"详细配置"}}),e.blnAdd?e._e():i("ta-anchor-link",{attrs:{href:"operate_log",title:"操作日志"}})]:e._e()],2)],1),i("div",{staticStyle:{position:"absolute",bottom:"20px",right:"70px"}},[i("ta-button-group",{staticStyle:{width:"100%"},attrs:{align:"right"}},[i("ta-button",{on:{click:e.fnCancel}},[e._v("取消")]),i("ta-button",{on:{click:e.fnSaveCache}},[e._v("暂存"),3!=e.step?i("span",{staticStyle:{color:"green"}},[e._v("(下一步)")]):e._e()]),i("ta-popconfirm",{attrs:{title:"确定提交?"},on:{confirm:e.fnSubmit}},[i("ta-button",{attrs:{type:"primary",disabled:"3"!=e.step}},[e._v("提交")])],1)],1)],1),i("ta-modal",{attrs:{title:"导入条目",destroyOnClose:!0,footer:null,width:500},model:{value:e.importMxVisible,callback:function(t){e.importMxVisible=t},expression:"importMxVisible"}},[i("import-mx",{attrs:{ykz277List:e.ykz277List},on:{onImportSuccess:e.onMxImportSuccess}})],1),i("ta-modal",{attrs:{title:"添加三目",footer:null,width:"90%",height:"650px",destroyOnClose:!0},model:{value:e.itemDialogVisible,callback:function(t){e.itemDialogVisible=t},expression:"itemDialogVisible"}},[i("add-item",{attrs:{ykz277List:e.ykz277List},on:{onOk:e.onAddItemSuccess}})],1),i("ta-modal",{attrs:{title:e.blnAddDesc?"新增描述":"编辑描述"},model:{value:e.editDescVisible,callback:function(t){e.editDescVisible=t},expression:"editDescVisible"}},[i("ta-input",{attrs:{placeholder:"请输入"},model:{value:e.descValue,callback:function(t){e.descValue=t},expression:"descValue"}}),i("div",{attrs:{slot:"footer"},slot:"footer"},[i("ta-button",{attrs:{type:"primary"},on:{click:e.saveDesc}},[e._v("确定")]),i("ta-button",{on:{click:function(t){e.editDescVisible=!1}}},[e._v("取消")])],1)],1),i("ta-modal",{attrs:{"destroy-on-close":!0,title:e.blnAddNode?"新增节点":"编辑节点",width:1200,height:600},model:{value:e.editNodeVisible,callback:function(t){e.editNodeVisible=t},expression:"editNodeVisible"}},[i("add-node",{ref:"addNodeComponent",attrs:{blnUpdate:!e.blnAddNode,param:this.editKeyRecord,ykz167:e.ykz167},on:{closeModal:e.fnCloseAddNode,onSaveSuccess:e.onSaveNodeSuccess}}),i("div",{attrs:{slot:"footer"},slot:"footer"},[i("ta-button",{attrs:{type:"primary"},on:{click:e.saveNode}},[e._v("保存")]),i("ta-button",{on:{click:function(t){e.editNodeVisible=!1}}},[e._v("取消")])],1)],1),i("ta-modal",{attrs:{title:"新增内容",footer:null,width:"60%",height:"600px",zoomable:!0,destroyOnClose:!0},on:{cancel:function(t){}},model:{value:e.addContentVisible,callback:function(t){e.addContentVisible=t},expression:"addContentVisible"}},[i("add-content-one",{attrs:{column:e.contentColumns},on:{fnAddOne:e.fnAddContentOneSuccess}})],1),i("ta-modal",{attrs:{title:"导入内容",destroyOnClose:!0,footer:null,width:500},model:{value:e.importContentVisible,callback:function(t){e.importContentVisible=t},expression:"importContentVisible"}},[i("import-content",{attrs:{nodeInfo:e.importNodeInfo},on:{onImportSuccess:e.onContentImportSuccess}})],1)],1)},n=[],o=a(89584),r=a(48534),s=a(66347),l=a(95082),c=(a(27207),a(36133),function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("ta-button",{on:{click:t.downloadMxTemplate}},[t._v("模板下载")])}),d=[],u={name:"mxTemplateButton",methods:{downloadMxTemplate:function(){var t=this;Base.submit(null,{url:"mttRuleConfig/downloadMxTemplate"}).then((function(e){var a=e.data.fileName,i=e.data.data,n=document.createElement("a");n.download=a,n.href="data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,"+i,n.click(),t.$message.success("下载成功")}))}}},p=u,h=a(1001),f=(0,h.Z)(p,c,d,!1,null,"686aa888",null),m=f.exports,y=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"fit",staticStyle:{width:"100%"}},[a("ta-upload",{attrs:{fileList:t.fileList,name:"file",accept:".xlsx, .xls",multiple:!1,action:"","show-upload-list":!1,beforeUpload:t.beforeUpload},on:{change:t.uploadChange}},[a("ta-button",{staticStyle:{"margin-left":"190px","margin-top":"20px",width:"120px"},attrs:{type:"primary"}},[a("ta-icon",{attrs:{type:"upload"}}),t._v(" 导入文件 ")],1)],1)],1)},k=[],g={name:"importMx",props:["ykz277List"],data:function(){return{fileList:[],importUrl:"/mttRuleConfig/importItemList"}},methods:{beforeUpload:function(t){return this.fileList.push(t),!1},uploadChange:function(){var t=this;this.fileList.length<=0?this.$message.error("还未选择文件！"):Base.submit(null,{url:this.importUrl,data:{uploadFile:this.fileList[0],ykz277s:JSON.stringify(this.ykz277List)},isFormData:!0,autoValid:!0}).then((function(e){if(e.data.success){t.$message.success("导入到表格成功");var a={},i=e.data.list;i=i.reduce((function(t,e){return!a[e.ake001]&&(a[e.ake001]=t.push(e)),t}),[]);var n,o=(0,s.Z)(i);try{for(o.s();!(n=o.n()).done;){var r=n.value;r.ape891=parseFloat(r.ape891),r.ape892=parseFloat(r.ape892),isNaN(r.ape891)&&(r.ape891=1),isNaN(r.ape892)&&(r.ape892=1),r.ape891>99&&(r.ape891=99),r.ape892>99&&(r.ape892=99)}}catch(l){o.e(l)}finally{o.f()}t.$emit("onImportSuccess",i)}}))}}},v=g,b=(0,h.Z)(v,y,k,!1,null,"6c0e2dc7",null),z=b.exports,C=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("div",{staticStyle:{"margin-top":"-20px"}},[i("ta-card",{attrs:{bordered:!1}},[i("div",{staticClass:"title"},[e._v("查询条件")]),i("ta-form",{attrs:{"auto-form-create":function(e){return t.searchForm=e},"label-width":110}},[i("ta-row",[i("ta-col",{attrs:{span:6}},[i("ta-form-item",{attrs:{label:"三目编码","field-decorator-id":"ake001"}},[i("ta-input")],1)],1),i("ta-col",{attrs:{span:6}},[i("ta-form-item",{attrs:{label:"三目名称","field-decorator-id":"ake002"}},[i("ta-input")],1)],1),i("ta-col",{attrs:{span:6}},[i("ta-form-item",{attrs:{label:"支付限制条件","field-decorator-id":"yke011"}},[i("ta-input")],1)],1),i("ta-col",{attrs:{span:4}},[i("ta-form-item",{attrs:{label:"剂型","field-decorator-id":"dosage"}},[i("ta-input")],1)],1)],1),i("ta-row",[i("ta-col",{attrs:{span:6}},[i("ta-form-item",{attrs:{"field-decorator-id":"aab012",label:"批准文号"}},[i("ta-input")],1)],1),i("ta-col",{attrs:{span:1,offset:8}}),i("ta-col",{attrs:{span:1,offset:4}},[i("ta-button",{on:{click:e.doSearch}},[e._v("查询")])],1)],1)],1),i("ta-table",{ref:"itemTable",attrs:{columns:e.columns,"data-source":e.dataSource,size:"small",scroll:{y:350},"row-key":"drugssn","show-checkbox":"",bordered:""},on:{"update:dataSource":function(t){e.dataSource=t},"update:data-source":function(t){e.dataSource=t}}}),i("ta-pagination",{ref:"itemPager",staticClass:"page",attrs:{size:"small","data-source":e.dataSource,url:"mttRuleConfig/pageMka05",params:e.getPageParam,defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"]},on:{"update:dataSource":function(t){e.dataSource=t},"update:data-source":function(t){e.dataSource=t}},model:{value:e.pageParam.pageNumber,callback:function(t){e.$set(e.pageParam,"pageNumber",t)},expression:"pageParam.pageNumber"}}),i("div",{staticStyle:{"text-align":"center"}},[i("ta-button",{attrs:{type:"primary"},on:{click:e.addItem}},[e._v("保存")])],1)],1)],1)},w=[],x={name:"addItem",components:{},props:{ake001List:{type:Array,default:function(){return[]}},ykz276List:{type:Array,default:function(){return[]}}},data:function(){return{blnFilter:!0,columns:[{dataIndex:"ake001",title:"三目编码",align:"center",width:250,overflowTooltip:!0},{dataIndex:"ake002",title:"三目名称",align:"center",width:250,overflowTooltip:!0},{dataIndex:"dosage",title:"剂型",align:"center",width:100},{dataIndex:"ake126",title:"计价单位",align:"center",width:100,collectionType:"MAKE126"},{dataIndex:"aab012",title:"批准文号",align:"center",width:200,overflowTooltip:!0},{dataIndex:"yke011",title:"限制条件",align:"center",overflowTooltip:!0}],dataSource:[],pageParam:{pageNumber:1},rowSelection:{selectedRowKeys:[],onChange:this.onSelectChange,getCheckboxProps:this.fnGetCheckboxProps}}},methods:{doSearch:function(){this.pageParam.pageNumber=1,this.$refs.itemPager.loadData()},getPageParam:function(){return{dtoStr:JSON.stringify(this.searchForm.getFieldsValue())}},onSelectChange:function(t,e){this.rowSelection.selectedRowKeys=t},fnGetCheckboxProps:function(t){var e={props:{disabled:this.ake001List.includes(t.ake001)}};return e},addItem:function(){var t=this.$refs.itemTable.getChecked().selectedRows;if(0!==t.length){var e={};t=t.reduce((function(t,a){return!e[a.ake001]&&(e[a.ake001]=t.push(a)),t}),[]),this.$emit("onOk",t)}else this.$message.error("请勾选数据")}}},S=x,L=(0,h.Z)(S,C,w,!1,null,"78107c5a",null),I=L.exports,_=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("div",[i("div",{staticClass:"titleLittle"},[e._v("审核节点配置")]),i("ta-form",{attrs:{autoFormCreate:function(e){t.nodeConfForm=e}}},[i("ta-row",{staticStyle:{"margin-left":"-19%"}},[i("ta-col",{attrs:{span:this.addNodeConfStyle.nodeConfSpan}},[i("ta-form-item",{attrs:{fieldDecoratorId:"ykz001",label:"节点类型",required:!0,require:{message:"请选择节点类型"}}},[i("ta-select",{attrs:{placeholder:"请选择",options:e.ykz001Options,showSearch:!0},on:{select:e.onYkz001Select,change:e.onYkz001Change}})],1),i("ta-form-item",{attrs:{fieldDecoratorId:"ykz010",label:"节点内容",required:!0,require:{message:"节点内容不能为空"}}},[i("ta-auto-complete",{attrs:{placeholder:"输入可异步检索","allow-clear":!0,"option-label-prop":"text"},on:{search:e.ykz010Search,select:e.onYkz010Select}},[i("template",{slot:"dataSource"},[i("ta-select-option",{attrs:{disabled:!0,value:"-1"}},[i("div",{staticStyle:{display:"inline-block",width:"30%"}},[e._v("节点名称")]),i("div",{staticStyle:{display:"inline-block",width:"10%"}},[e._v("节点编号")]),i("div",{staticStyle:{display:"inline-block",width:"60%"}},[e._v("节点说明")])]),e._l(e.ykz010DataSource,(function(t,a){return i("ta-select-option",{key:a,attrs:{text:t.ykz010,value:t.ykz010+""}},[i("div",{staticStyle:{display:"inline-block",width:"30%"}},[e._v(e._s(t.ykz010))]),i("div",{staticStyle:{display:"inline-block",width:"10%"}},[e._v(e._s(t.ykz042))]),i("div",{staticStyle:{display:"inline-block",width:"60%"}},[e._v(e._s(t.ykz065))])])}))],2)],2)],1),i("ta-form-item",{attrs:{fieldDecoratorId:"ykz065",label:"节点说明"}},[i("ta-textarea",{attrs:{placeholder:"请输入",rows:4}})],1),i("ta-form-item",{attrs:{fieldDecoratorId:"ykz027",initValue:"0",label:"判断类型",require:{message:"请选择判断类型"}}},[i("ta-select",{staticStyle:{width:"500px"}},[i("ta-select-option",{attrs:{value:"0"}},[e._v(" 满足则通过 ")]),i("ta-select-option",{attrs:{value:"1"}},[e._v(" 满足则不通过 ")])],1)],1)],1)],1)],1),i("section",[i("div",{staticClass:"titleLittle"},[e._v("属性")]),i("ta-form",e._l(e.optionsPro,(function(t,a){return i("ta-row",{key:a},[i("ta-col",{attrs:{span:e.nodeConfProp.spanLeft}},[i("ta-form-item",{attrs:{label:t.ykz013,"label-width":150}},[i("ta-select",{attrs:{dropdownStyle:{maxWidth:"500px"},dropdownMatchSelectWidth:!1},on:{change:function(i){return e.onSelectValueChange(t,a)}},model:{value:t.selectedValue,callback:function(a){e.$set(t,"selectedValue",a)},expression:"item.selectedValue"}},e._l(t.options,(function(t,a){return i("ta-select-option",{key:a,attrs:{value:t.ykz014}},[e._v(" "+e._s(t.ykz048)+" ")])})),1)],1)],1),i("ta-col",{attrs:{span:e.nodeConfProp.spanCenter}},[i("ta-form-item",{attrs:{label:"比较值"}},["1"!=t.ykz156?i("ta-input",{attrs:{disabled:!0,value:t.inputValue}}):i("input",{directives:[{name:"model",rawName:"v-model",value:t.inputValue,expression:"item.inputValue"}],staticStyle:{outline:"none",height:"32px",width:"100%",border:"1px solid #e4e9f2","border-radius":"5%"},attrs:{placeholder:"多个以|分隔",disabled:!1},domProps:{value:t.inputValue},on:{input:function(a){a.target.composing||e.$set(t,"inputValue",a.target.value)}}})],1)],1),i("ta-col",{attrs:{span:e.nodeConfProp.spanRight}},[i("ta-form-item",[i("ta-button-group",[i("ta-button",{attrs:{disabled:"1"!=t.ykz134},on:{click:function(i){return e.fnPropAdd(t,a)}}},[i("ta-icon",{attrs:{type:"plus"}})],1),i("ta-button",{attrs:{disabled:"1"!=t.ykz134},on:{click:function(i){return e.fnPropCancel(t,a)}}},[i("ta-icon",{attrs:{type:"minus"}})],1)],1)],1)],1)],1)})),1)],1)],1)},N=[],V=a(41088),D=a(9063),T=a(10328),R={name:"addNode",components:{TaCol:T.ZP,TaFormItem:D.Z,TaTextarea:V.Z},props:{param:Object,ykz167:String,blnUpdate:{type:Boolean,default:!1}},data:function(){var t=[];return{ykz001Options:[],ykz010DataSource:[],titleMap:null,optionConfig:{value:"ykz010",label:"ykz010"},operationLog:"",optionsPro:t,activeKey:"1",cfSaveVisible:!1,addNodeConfStyle:{nodeConfSpan:23},nodeConfProp:{spanLeft:10,spanCenter:10,spanRight:4},compareColumns:[{dataIndex:"value",title:"比较值",width:"30%",align:"center"},{dataIndex:"log",title:"说明",width:"60%",align:"center"}],compareData:[]}},created:function(){this.titleMap=new Map,this.titleMap.set("ykz010","节点名称"),this.titleMap.set("ykz042","节点编号"),this.titleMap.set("ykz065","节点说明"),this.initData()},mounted:function(){this.blnUpdate&&this.initNodeData()},methods:{initNodeData:function(){var t=this,e=this.param,a=[];Base.submit(null,{url:"mttRuleConfig/queryThisAttribute",data:{ykz062:e.ykz062}}).then((function(e){e.data.success&&(a=JSON.parse(e.data.nodeInfo.ykz011),t.nodeConfForm.setFieldsValue((0,l.Z)({},e.data.nodeInfo)),t.$nextTick((function(){t.ykz010Search(e.data.nodeInfo.ykz010)}))),t.queryInitNode(a,e.data.nodeInfo.ykz001)}))},queryInitNode:function(t,e){var a=this,i={url:"mttRuleConfig/getNodeAttributes",data:{ykz167:this.ykz167,ykz001:e}};this.Base.submit(null,i).then((function(e){var i,n=e.data.attributes,o=(0,s.Z)(t);try{var r=function(){var t=i.value,e=n.filter((function(e){return e.ykz012==t.ykz012}));if(t.selectedValue=t.ykz014,t.inputValue=t.ykz049,e.length>0){var a=e[0].options.filter((function(e){return e.ykz014==t.ykz014})),o=e[0];t.ykz013=o.ykz013,a.length>0?t.ykz156=a[0].ykz156:t.ykz156=o.ykz156,t.options=o.options,t.match=!0}else t.match=!1};for(o.s();!(i=o.n()).done;)r()}catch(l){o.e(l)}finally{o.f()}a.optionsPro=t}))},initData:function(){var t=this,e={url:"mttRuleConfig/addNodeInitPage",data:{ykz167:this.ykz167}};this.Base.submit(null,e).then((function(e){e.data.success&&(t.ykz001Options=e.data.list)})).catch()},ykz010Search:function(t){var e=this,a={url:"mttRuleConfig/queryNodeForRpc",data:{ykz001:this.nodeConfForm.getFieldValue("ykz001"),ykz010:t}};this.Base.submit(null,a).then((function(t){t.data.success&&(e.ykz010DataSource=t.data.list)})).catch()},fnPropAdd:function(t,e){this.optionsPro.splice(e,0,(0,l.Z)({},t))},fnPropCancel:function(t,e){var a=this.optionsPro.filter((function(e){return e.ykz012===t.ykz012}));a.length<2?this.$message.warn("不同类型属性至少保留一个"):this.optionsPro.splice(e,1)},queryCompareData:function(t){var e=this,a={url:"ruleConfig/getCompareData",data:{ykz012:t.ykz012,ykz014:t.selectedValue}};Base.submit(null,a).then((function(t){e.compareData=t.data.compareData}))},fnCompareRow:function(t,e,a){var i=this,n=e.inputValue,o=[];return n&&(o=n.split("|")),{style:{cursor:"pointer",backgroundColor:o.includes(t.value)?"#b0eab0":"white"},on:{click:function(o){if(null!=n&&""!=n){var r=n.split("|");r.includes(t.value)||(e.inputValue=n+"|"+t.value)}else e.inputValue=t.value;i.$set(i.optionsPro,a,e)}}}},onYkz001Select:function(t,e){var a=this,i={url:"mttRuleConfig/getNodeAttributes",data:{ykz167:this.ykz167,ykz001:t}};this.Base.submit(null,i).then((function(t){a.optionsPro=t.data.attributes})).catch(),this.$nextTick((function(){a.ykz010Search()}))},onYkz001Change:function(){this.nodeConfForm.setFieldsValue({ykz010:null,ykz065:null}),this.ykz010DataSource.length=0},onYkz010Select:function(t,e,a){var i=this.ykz010DataSource.filter((function(e){return e.ykz010+""==t}));i.length>0&&this.nodeConfForm.setFieldsValue({ykz065:i[0].ykz065})},saveNode:function(){var t,e=this,a=[],i=(0,s.Z)(this.optionsPro);try{for(i.s();!(t=i.n()).done;){var n=t.value;if(null==n.selectedValue||""==n.selectedValue||void 0==n.selectedValue)return void this.$message.warn("有选项未选择");var o=n.ykz012+"&"+n.selectedValue;if(a.includes(o))return void this.$message.warn("[".concat(n.ykz013,"]的多个属性中，重复选择同一个选项，请检查"));a.push(o)}}catch(d){i.e(d)}finally{i.f()}var r=this.optionsPro.map((function(t){return{ykz012:t.ykz012||"",ykz014:t.selectedValue||"",ykz049:t.inputValue||""}})),c=JSON.stringify(r,"utf-8");this.nodeConfForm.validateFields((function(t){if(!t){var a=e.nodeConfForm.getFieldsValue(),i=e.ykz010DataSource.filter((function(t){return t.ykz010=a.ykz010}));if(0!=i.length){var n=a.ykz027,o=e.ykz001Options.filter((function(t){return t.value==a.ykz001}));if(o.length>0){var r=o[0].label;a.ykz061="".concat(r,"：").concat(a.ykz010),Base.submit(null,{url:"mttRuleConfig/saveNode",data:{mkf26Str:JSON.stringify((0,l.Z)({},a)),ykz011:c}}).then((function(t){t.data.nodeInfo.ykz027=n,t.data.nodeInfo.ykz011=c,e.$emit("onSaveSuccess",t.data.nodeInfo,e.param)}))}else e.$message.warn("未查询到节点类型名称,请重试")}else e.$message.info("请在节点内容中检索并选择一条存在的记录")}}))},onSelectValueChange:function(t,e){var a=t.options.filter((function(e){return e.ykz014==t.selectedValue}));a.length>0&&(t.ykz156=a[0].ykz156+"",t.inputValue=a[0].ykz184?a[0].ykz184:"")},deleteProp:function(t,e){this.optionsPro.splice(e,1)},reuseRow:function(t,e){var a=this;return{style:{cursor:"pointer"},on:{click:function(){a.reuseSelection.selectedRowKeys=[t.ykz062]}}}},onRowSelectChange:function(t,e){this.reuseSelection.selectedRowKeys=t}}},F=R,$=(0,h.Z)(F,_,N,!1,null,"6b656151",null),A=$.exports,P=a(64964),K=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"fit",staticStyle:{width:"100%"}},[a("ta-upload",{attrs:{fileList:t.fileList,name:"file",accept:".xlsx, .xls",multiple:!1,action:"","show-upload-list":!1,beforeUpload:t.beforeUpload},on:{change:t.uploadChange}},[a("ta-button",{staticStyle:{"margin-left":"190px","margin-top":"20px",width:"120px"},attrs:{type:"primary"}},[a("ta-icon",{attrs:{type:"upload"}}),t._v(" 导入文件 ")],1)],1)],1)},M=[],O={name:"importMx",props:["nodeInfo"],data:function(){return{fileList:[],importUrl:"/mttRuleConfig/importContent"}},methods:{beforeUpload:function(t){return this.fileList.push(t),!1},uploadChange:function(){var t=this;this.fileList.length<=0?this.$message.error("还未选择文件！"):Base.submit(null,{url:this.importUrl,data:{uploadFile:this.fileList[0],ykz042:this.nodeInfo.ykz042,ykz001:this.nodeInfo.ykz001,ykz010:this.nodeInfo.ykz010},isFormData:!0,autoValid:!0}).then((function(e){e.data.success&&(t.$message.success("导入成功"),t.$emit("onImportSuccess"))}))}}},Z=O,B=(0,h.Z)(Z,K,M,!1,null,"561a576a",null),U=B.exports,q={name:"ruleConfig",components:{mxTemplateButton:m,importMx:z,addItem:I,addNode:A,addContentOne:P.Z,importContent:U},props:{params:Object},data:function(){return{tabid:"ruleConfig",blnAdd:!0,step:3,ykz167:"1",bigRuleList:[],volaQualList:[],knowDataSource:[],ruleidColumn:[{dataIndex:"ruleidlog",title:"说明",width:200,align:"center",overflowTooltip:!0},{dataIndex:"ykz268",title:"目录",width:150,align:"center",collectionType:"MYKZ268",overflowTooltip:!0},{dataIndex:"ykz227",title:"险种",width:150,align:"center",collectionType:"MYKZ227",overflowTooltip:!0},{dataIndex:"aae500",title:"场景",width:200,align:"center",collectionType:"MAAE500",overflowTooltip:!0},{dataIndex:"ykz248",title:"医疗类型",width:100,align:"center",collectionType:"MYKZ248",overflowTooltip:!0}],ruleidList:[],blnShowCheckbox:!0,ykz277List:[],itemSearchText:void 0,itemColumns:[{dataIndex:"ake001",title:"三目编码",align:"center",overflowTooltip:!0},{dataIndex:"ake002",title:"三目名称",align:"center",overflowTooltip:!0},{dataIndex:"ape864",title:"分组编码",width:100,align:"center",overflowTooltip:!0},{dataIndex:"ape865",title:"分组名称",width:100,align:"center",showOverflowTooltip:!0},{dataIndex:"ape891",title:"频次(乘法)",width:100,align:"center",showOverflowTooltip:!0},{dataIndex:"ape892",title:"频次(除法)",width:100,align:"center",showOverflowTooltip:!0},{dataIndex:"ake126",title:"计价单位",width:100,showOverflowTooltip:!0}],itemList:[],itemCacheList:[],importMxVisible:!1,itemDialogVisible:!1,formItemLayout:{labelCol:{span:6},wrapperClo:{span:18}},highFormLayout:{colSpan:8,firstSpan:22,checkboxSpan:2},ykz135List:["1","2"],showLine:!0,showIcon:!1,blnMark:!1,treeData:[{key:"00",title:"根节点",type:"0",children:[{title:"层级1",key:"0_1",type:"1",ykz135:"1",children:[{title:"请输入",key:"3",type:"3",ykz135:"1"}]}]}],expandTreeKeys:[],maxKey:5,blnEditDesc:!1,editDescVisible:!1,editNodeVisible:!1,descValue:void 0,blnAddDesc:!1,editKey:void 0,editKeyRecord:{},blnAddNode:!1,nodeColumns:[{dataIndex:"ykz062",title:"节点id",width:100,align:"center",overflowTooltip:!0},{dataIndex:"ykz042",title:"节点编号",width:100,align:"center",overflowTooltip:!0},{dataIndex:"ykz002",title:"节点类型",width:100,align:"center",overflowTooltip:!0},{dataIndex:"ykz010",title:"节点名称",width:100,align:"center",overflowTooltip:!0},{dataIndex:"relative",scopedSlots:{customRender:"relative"},title:"节点内容",width:100,align:"center",overflowTooltip:!0},{dataIndex:"attrRelative",scopedSlots:{customRender:"attrRelative"},title:"节点属性",width:100,align:"center",overflowTooltip:!0}],nodeList:[],clickedNode:void 0,importNodeInfo:{},contentColumns:[{dataIndex:"ykz002",title:"节点类型",width:100,align:"center",overflowTooltip:!0},{dataIndex:"ykz010",title:"节点名称",align:"center",overflowTooltip:!0}],contentList:[],contentSearchText:void 0,addContentVisible:!1,importContentVisible:!1}},mounted:function(){this.getBigRuleList(),this.listVolaQualA(),this.initPageData()},deactivated:function(){var t=this.rulePropertyForm.getFieldValue("ykz018");Base.submit(null,{url:"mttRuleConfig/clearPurUser",data:{ykz018:t}}).then((function(t){}))},activated:function(){},methods:{initPageData:function(){var t=this,e=this.params;this.clearStatus(),e.ykz277?(this.step="3",this.blnAdd=!1,this.blnShowCheckbox=!1,this.ykz277List=[e.ykz277],Base.submit(null,{url:"mttRuleConfig/initRuleInfo",data:{ykz277:e.ykz277}}).then((function(e){var a=e.data.rule;t.itemList=a.itemList,t.itemCacheList=a.itemList,t.rulePropertyForm.setFieldsValue((0,l.Z)({},a.ruleProperty)),t.ykz167=a.ruleProperty.ykz167,t.treeData=a.logicTree,t.findMaxKey(t.treeData),t.ruleidList=a.ruleidList,t.$forceUpdate(),t.$nextTick((function(){t.scanTreeNodeList(),t.onItemSearch()}))}))):(this.step="1",this.blnAdd=!0)},findMaxKey:function(t){var e,a=(0,s.Z)(t);try{for(a.s();!(e=a.n()).done;){var i=e.value;this.expandTreeKeys.push(i.key),parseInt(i.key)>parseInt(this.maxKey)&&(this.maxKey=i.key),i.children&&i.children.length>0&&this.findMaxKey(i.children)}}catch(n){a.e(n)}finally{a.f()}},clearStatus:function(){this.rulePropertyForm.resetFields(),this.ruleidList=[],this.blnShowCheckbox=!0,this.ykz277List=[],this.itemSearchText=void 0,this.itemList=[],this.itemCacheList=[],this.importMxVisible=!1,this.itemDialogVisible=!1,this.treeData=[{key:"00",title:"根节点",type:"0",children:[{title:"层级1",key:"0_1",type:"1",ykz135:"1",children:[{title:"请输入",key:"3",type:"3",ykz135:"1"}]}]}],this.maxKey=5,this.blnEditDesc=!1,this.editDescVisible=!1,this.editNodeVisible=!1,this.descValue=void 0,this.blnAddDesc=!1,this.editKey=void 0,this.editKeyRecord={},this.blnAddNode=!1,this.nodeList=[],this.clickedNode=void 0,this.importNodeInfo={},this.contentList=[],this.contentSearchText=void 0,this.addContentVisible=!1,this.importContentVisible=!1},getContainer:function(){return document.querySelector(".ruleConfig").children[0]},getBigRuleList:function(){var t=this;Base.submit(null,{url:"mttRuleConfig/listBigRule"}).then((function(e){t.bigRuleList=e.data.list}))},listVolaQualA:function(){var t=this;Base.submit(null,{url:"mttRuleConfig/listVolaQualA"}).then((function(e){t.volaQualList=e.data.list}))},knowSearch:function(t){var e=this,a={url:"mttRuleConfig/knowRpc",data:{text:t}},i={successCallback:function(t){e.knowDataSource=t.data.list}};this.Base.submit("",a,i)},queryFreeRuleid:function(){var t=this,e=this.rulePropertyForm.getFieldValue("ykz018");e?Base.submit(null,{url:"mttRuleConfig/checkPzCurUser",data:{ykz018:e}}).then((function(a){t.queryFreeRuleid1(e)})):this.ruleidList=[]},queryFreeRuleid1:function(t){var e=this;Base.submit(null,{url:"mttRuleConfig/queryFreeRuleid",data:{ykz018:t}}).then((function(t){0!=t.data.list.length?(e.ykz167=e.rulePropertyForm.getFieldValue("ykz167"),e.step="2",e.ruleidList=t.data.list):e.$message.warn("当前知识元暂无可用情景")}))},onRuleidSelectChange:function(t,e){},itemPageParam:function(){return{searchText:this.itemSearchText,ykz277:this.ykz277List[0]}},importMx:function(){this.importMxVisible=!0},onMxImportSuccess:function(t){var e=this;return(0,r.Z)(regeneratorRuntime.mark((function a(){var i,n,o,r,l,c;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,e.getMaxApe864();case 2:i=a.sent,e.importMxVisible=!1,n=e.itemList.map((function(t){return t.ake001})),o=t.filter((function(t){return!n.includes(t.ake001)})),r=(0,s.Z)(o);try{for(r.s();!(l=r.n()).done;)c=l.value,c.ape864||(c.ape864=i++),e.itemList.push(c)}catch(d){r.e(d)}finally{r.f()}e.onItemSearch();case 9:case"end":return a.stop()}}),a)})))()},getMaxApe864:function(){var t=this;return(0,r.Z)(regeneratorRuntime.mark((function e(){var a,i,n,o;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:a=1,i=(0,s.Z)(t.itemList);try{for(i.s();!(n=i.n()).done;)o=n.value,parseInt(o.ape864)>parseInt(a)&&(a=parseInt(o.ape864))}catch(r){i.e(r)}finally{i.f()}return e.next=5,Base.submit(null,{url:"mttRuleConfig/getMaxItemApe864",data:{}}).then((function(t){parseInt(t.data.ape864)>parseInt(a)&&(a=parseInt(t.data.ape864))}));case 5:return e.abrupt("return",parseInt(a)+1);case 6:case"end":return e.stop()}}),e)})))()},onItemSearch:function(){var t=this;this.itemCacheList=this.itemList.filter((function(e){return!t.itemSearchText||!!(e.ake001&&e.ake001.includes(t.itemSearchText)||e.ake002&&e.ake002.includes(t.itemSearchText))}))},openAddItem:function(){this.itemDialogVisible=!0},onAddItemSuccess:function(t){var e=this;return(0,r.Z)(regeneratorRuntime.mark((function a(){var i,n,o,r,l,c;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,e.getMaxApe864();case 2:i=a.sent,e.itemDialogVisible=!1,e.$message.success("新增成功"),n=e.itemList.map((function(t){return t.ake001})),o=t.filter((function(t){return!n.includes(t.ake001)})),r=(0,s.Z)(o);try{for(r.s();!(l=r.n()).done;)c=l.value,c.ape864||(c.ape864=i++),c.ape891=1,c.ape892=1,e.itemList.push(c)}catch(d){r.e(d)}finally{r.f()}e.onItemSearch();case 10:case"end":return a.stop()}}),a)})))()},deleteItem:function(){var t=this.$refs.itemTable.getCheckboxRecords();if(0!=t.length){var e=t.map((function(t){return t.ake001}));this.itemList=this.itemList.filter((function(t){return!e.includes(t.ake001)})),this.onItemSearch()}else this.$message.warn("请选择要删除的明细")},renderYkz027:function(t){return"1"==t?"是":"否"},changeItem:function(){var t=this,e=this.$refs.itemTable.getCheckboxRecords();if(0!==e.length){var a=this.itemChangeForm.getFieldsValue();void 0!==a.ape864&&null!==a.ape864&&(a.ape864=a.ape864.replace(/\s/g,"")),void 0!==a.ape865&&null!==a.ape865&&(a.ape865=a.ape865.replace(/\s/g,"")),a.ckApe864||a.ckApe865||a.ckApe891||a.ckApe892?this.itemChangeForm.validateFields((function(){!a.ckApe864||void 0!==a.ape864&&null!==a.ape864&&""!==a.ape864?!a.ckApe865||void 0!==a.ape865&&null!==a.ape865&&""!==a.ape865?!a.ckApe891||void 0!==a.ape891&&null!==a.ape891&&""!==a.ape891?!a.ckApe892||void 0!==a.ape892&&null!==a.ape892&&""!==a.ape892?t.changeItemData(e,a):t.$message.warn("频次倍速(除法)不能为空"):t.$message.warn("频次倍速(乘法)不能为空"):t.$message.warn("分组名称不能为空"):t.$message.warn("分组编号不能为空")})):this.$message.warn("请至少勾选一个字段进行修改")}else this.$message.info("请先选择数据")},changeItemData:function(t,e){for(var a=0;a<t.length;a++)for(var i=0;i<this.itemList.length;i++)t[a].ake001===this.itemList[i].ake001&&(e.ckApe864&&(this.itemList[i].ape864=e.ape864),e.ckApe865&&(this.itemList[i].ape865=e.ape865),e.ckApe891&&(this.itemList[i].ape891=e.ape891),e.ckApe892&&(this.itemList[i].ape892=e.ape892)),this.$set(this.itemList,i,this.itemList[i]);this.onItemSearch()},doCopyTree:function(){var t=this,e=this.treeForm.getFieldValue("ykz277");Base.submit(null,{url:"mttRuleConfig/copyTree",data:{ykz277:e}}).then((function(e){t.treeData=e.data.list,t.scanTreeNodeList()}))},fnMouseover:function(t){this.changeHover(t.key,!0,this.treeData),this.$forceUpdate()},fnMouseleave:function(t){this.changeHover(t.key,!1,this.treeData),this.$forceUpdate()},changeHover:function(t,e,a){var i,n=(0,s.Z)(a);try{for(n.s();!(i=n.n()).done;){var o=i.value;t==o.key&&(o.blnHover=e),o.children&&o.children.length>0&&this.changeHover(t,e,o.children)}}catch(r){n.e(r)}finally{n.f()}},doAdd:function(t){if("0"==t.type){var e=this.treeData[0].children.map((function(t){return t.key.replaceAll("0_","")})),a=this.ykz135List.filter((function(t){return!e.includes(t)}));if(a.length>0){var i=a[0];this.treeData[0].children.push({title:"层级"+i,key:"0_"+i,type:"1",ykz135:i,children:[]})}}else"1"==t.type?(this.descValue=void 0,this.editKey=t.key,this.editKeyRecord=t,this.blnAddDesc=!0,this.editDescVisible=!0):"2"!=t.type&&"3"!=t.type||(this.editKey=t.key,this.editKeyRecord=t,this.blnAddNode=!0,this.editNodeVisible=!0);this.$forceUpdate()},doEdit:function(t){this.editKey=t.key,this.editKeyRecord=t,"3"==t.type?(this.blnEditDesc=!1,this.editDescVisible=!0,this.descValue=t.title,"请输入"==this.descValue&&(this.descValue=void 0)):(this.blnAddNode=!1,this.editNodeVisible=!0)},saveDesc:function(){this.descValue?(this.findUpdateDesc(this.editKey,this.treeData,this.descValue),this.editDescVisible=!1):this.$message.warn("请输入描述")},findUpdateDesc:function(t,e,a){var i,n=(0,s.Z)(e);try{for(n.s();!(i=n.n()).done;){var o=i.value;t==o.key&&(o.children||(o.children=[]),this.blnAddDesc?o.children.push({title:a,key:++this.maxKey,type:"3",ykz135:this.editKeyRecord.ykz135,children:[]}):o.title=a),o.children&&o.children.length>0&&this.findUpdateDesc(t,o.children,a)}}catch(r){n.e(r)}finally{n.f()}},saveNode:function(){this.$refs.addNodeComponent.saveNode()},fnCloseAddNode:function(){this.editNodeVisible=!1},onSaveNodeSuccess:function(t){if(this.editNodeVisible=!1,this.blnAddNode){var e={title:t.ykz061,key:++this.maxKey,type:"2",ykz135:this.editKeyRecord.ykz135,children:[],ykz062:t.ykz062,ykz027:t.ykz027};this.findAddChild(this.editKey,this.treeData,e),this.expandTreeKeys.push(e.key)}else this.findUpdateChild(this.editKey,this.treeData,t);this.scanTreeNodeList(),this.$forceUpdate()},findAddChild:function(t,e,a){var i,n=(0,s.Z)(e);try{for(n.s();!(i=n.n()).done;){var o=i.value;if(t==o.key)return o.children||(o.children=[]),void o.children.push(a);o.children&&o.children.length>0&&this.findAddChild(t,o.children,a)}}catch(r){n.e(r)}finally{n.f()}},findUpdateChild:function(t,e,a){var i,n=(0,s.Z)(e);try{for(n.s();!(i=n.n()).done;){var o=i.value;if(t==o.key)return o.children||(o.children=[]),o.title=a.ykz061,o.ykz062=a.ykz062,void(o.ykz027=a.ykz027);o.children&&o.children.length>0&&this.findUpdateChild(t,o.children,a)}}catch(r){n.e(r)}finally{n.f()}},doDelete:function(t){function e(t,a){var i=t.filter((function(t){return t.key!==a}));return i.forEach((function(t){return t.children&&(t.children=e(t.children,a))})),i}this.treeData=e(this.treeData,t.key),this.scanTreeNodeList(),this.$forceUpdate()},scanTreeNodeList:function(){var t=this;Base.submit(null,{url:"mttRuleConfig/scanTreeNodeList",data:{treeDataStr:JSON.stringify(this.treeData)}}).then((function(e){if(t.nodeList=e.data.list,t.clickedNode){var a=t.nodeList.map((function(t){return t.ykz042}));a.includes(t.clickedNode.ykz042)?t.pageNodeContent():(t.contentList=[],t.clickedNode=void 0)}var i=t.nodeList.filter((function(t){return"1"==t.relative})).map((function(t){return t.ykz062}));t.findUpdateRelative(t.treeData,i)}))},findUpdateRelative:function(t,e){var a,i=(0,s.Z)(t);try{for(i.s();!(a=i.n()).done;){var n=a.value;"2"==n.type&&(e.includes(n.ykz062)?n.relative="1":n.relative="0"),n.children&&n.children.length>0&&this.findUpdateRelative(n.children,e)}}catch(o){i.e(o)}finally{i.f()}},loadAttrDesc:function(t){var e=this;Base.submit(null,{url:"mttRuleConfig/queryAttrDesc",data:{ykz011:t.ykz011}}).then((function(a){t.attrList=a.data.list,e.$forceUpdate()}))},nodeCustomRow:function(t,e,a){var i=this,n="black",o="white";return this.clickedNode&&t.ykz042==this.clickedNode.ykz042&&(n="#73cfb3",o="#ffffd5"),{style:{cursor:"pointer",backgroundColor:o,color:n},on:{click:function(e){t.blnClick=!0,i.clickedNode=t,i.queryContentColumns(),i.pageNodeContent()}}}},pageNodeContent:function(){this.clickedNode?this.$refs.contentPager.loadData():this.contentList=[]},queryContentColumns:function(){var t=this;Base.submit(null,{url:"mttRuleConfig/queryContentColumns",data:{ykz001:this.clickedNode.ykz001}}).then((function(e){t.contentColumns=e.data.info.list}))},contentPageParam:function(){return{ykz001:this.clickedNode.ykz001,ykz042:this.clickedNode.ykz042,text:this.contentSearchText}},downloadContentTemplate:function(){var t=this,e={url:"mttRuleConfig/downloadContentTemplate",data:{ykz001:this.clickedNode.ykz001},autoValid:!0},a={successCallback:function(e){if(e.data.success){var a=e.data.fileName,i=e.data.data,n=document.createElement("a");n.download=a,n.href="data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,"+i,n.click(),t.$message.success("下载成功")}else t.$message.error(e.errors[0].msg)}};this.Base.submit("",e,a)},openAddContent:function(){this.addContentVisible=!0},fnAddContentOneSuccess:function(t){var e=this;Base.submit(null,{url:"mttRuleConfig/addContent",data:{content:JSON.stringify((0,l.Z)((0,l.Z)({},t),{},{ykz010:this.clickedNode.ykz010,ykz042:this.clickedNode.ykz042,ykz001:this.clickedNode.ykz001}))}}).then((function(t){e.addContentVisible=!1,e.$message.success("新增成功"),e.scanTreeNodeList()}))},deleteContent:function(){var t=this,e=this.$refs.contentTable.getChecked().selectedRowKeys;0!=e.length?Base.submit(null,{url:"mttRuleConfig/deleteContent",data:{rids:JSON.stringify(e),ykz001:this.clickedNode.ykz001,ykz042:this.clickedNode.ykz042}}).then((function(e){t.$message.success("删除成功"),t.scanTreeNodeList()})):this.$message.warn("请选择至少一行数据")},openImportContent:function(){this.importNodeInfo=this.clickedNode,this.importContentVisible=!0},onContentImportSuccess:function(){this.scanTreeNodeList()},fnCancel:function(){this.$emit("close")},fnSaveCache:function(){var t=this;if("1"==this.step)this.rulePropertyForm.validateFields((function(e){if(e)t.$message.warn("请检查【规则内容】数据完整性");else{var a=t.rulePropertyForm.getFieldsValue();Base.submit(null,{url:"mttRuleConfig/saveOrCheckKnowmx",data:{dtoStr:JSON.stringify(a)}}).then((function(e){t.queryFreeRuleid()}))}}));else if("2"==this.step){var e=this.$refs.ruleidTable.getChecked().selectedRows,a=this.$refs.ruleidTable.getChecked().selectedRowKeys;if(0==e.length)return void this.$message.warn("请检查【启用触发场景】需至少勾选一条数据");var i=this.rulePropertyForm.getFieldsValue();Base.submit(null,{url:"mttRuleConfig/generateRuleByRuleid",data:{ruleidListStr:JSON.stringify(a),ruleContent:JSON.stringify(i)}}).then((function(e){t.ykz277List=e.data.ykz277List,t.step="3"})),this.$forceUpdate()}else{var n=this.$refs.ruleidTable.getChecked().selectedRowKeys;if(0==n.length&&this.ruleidList.length>1)return void this.$message.warn("未选择规则批号");var o,r=(0,s.Z)(this.ruleidList);try{for(r.s();!(o=r.n()).done;){var c=o.value;n.includes(c.ruleid)?c.checked=!0:c.checked=!1}}catch(d){r.e(d)}finally{r.f()}Base.submit(null,{url:"mttRuleConfig/saveCache",data:{mkf74Str:JSON.stringify((0,l.Z)({ruleid:this.ruleidList[0].ruleid},this.rulePropertyForm.getFieldsValue())),mkf75Str:JSON.stringify(this.itemList),treeStr:JSON.stringify(this.treeData),ykz277s:JSON.stringify(this.ykz277List),ruleidListStr:JSON.stringify(this.ruleidList)}}).then((function(e){t.ykz277List=e.data.rule.list,t.rulePropertyForm.setFieldsValue({rulever:e.data.rule.rulever}),t.$message.success("暂存成功")}))}},onTreeDrop:function(t){var e,a=this,i=t.node.eventKey,n=t.dragNode.eventKey,r=t.node.pos.split("-"),s=(t.dropPosition,Number(r[r.length-1]),function t(e,a,i){e.forEach((function(e,n,o){return e.key===a?i(e,n,o):e.children?t(e.children,a,i):void 0}))}),l=(0,o.Z)(this.treeData);s(l,n,(function(t,a,i){e=t})),t.dropToGap||s(l,i,(function(t){"2"!=e.type||"2"!=t.type&&"3"!=t.type?a.$message.info("提示:只能拖动节点，且只能拖动到节点或描述节点下"):(t.children=t.children||[],s(l,n,(function(t,e,a){a.splice(e,1)})),t.children.push(e),a.refreshYkz135(e,t.ykz135))})),this.treeData=l},refreshYkz135:function(t,e){if(t.ykz135=e,t.children&&t.children.length>0){var a,i=(0,s.Z)(t.children);try{for(i.s();!(a=i.n()).done;){var n=a.value;this.refreshYkz135(n,e)}}catch(o){i.e(o)}finally{i.f()}}},fnSubmit:function(){var t=this,e=this.$refs.ruleidTable.getChecked().selectedRowKeys;if(0==e.length&&this.ruleidList.length>1)this.$message.warn("未选择规则批号");else{var a,i=(0,s.Z)(this.ruleidList);try{for(i.s();!(a=i.n()).done;){var n=a.value;e.includes(n.ruleid)?n.checked=!0:n.checked=!1}}catch(o){i.e(o)}finally{i.f()}this.rulePropertyForm.validateFields((function(e){if(e)t.$message.warn("规则信息未填写完整");else if("2"==t.ykz167||0!=t.itemList.length)if(0!=t.nodeList.length){var a=t.nodeList.filter((function(t){return"1"!=t.relative}));a.length>0?t.$message.warn("存在未配置的节点内容"):Base.submit(null,{url:"mttRuleConfig/submitRule",data:{mkf74Str:JSON.stringify((0,l.Z)({ruleid:t.ruleidList[0].ruleid},t.rulePropertyForm.getFieldsValue())),mkf75Str:JSON.stringify(t.itemList),treeStr:JSON.stringify(t.treeData),ykz277s:JSON.stringify(t.ykz277List),ruleidListStr:JSON.stringify(t.ruleidList)}}).then((function(){t.$message.success("提交成功"),t.fnCancel()}))}else t.$message.warn("审核逻辑请至少配置一个可审节点");else t.$message.warn("明细类规则请配置至少一个明细")}))}}}},E=q,J=(0,h.Z)(E,i,n,!1,null,"03041f87",null),Y=J.exports}}]);
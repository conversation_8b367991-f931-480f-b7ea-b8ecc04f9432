"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[7012],{31161:function(t,e,a){a.r(e),a.d(e,{default:function(){return P}});var i=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("div",{staticClass:"fit"},[i("ta-border-layout",{staticStyle:{},attrs:{"layout-type":"fixTop"}},[i("div",{staticStyle:{"padding-top":"10px"},attrs:{slot:"header"},slot:"header"},[i("div",{staticClass:"query-header"},[i("div",{ref:"formBox",staticClass:"query-header-left-2",staticStyle:{width:"calc(100% - 275px)"},style:e.formBoxStyle},[i("ta-form",{attrs:{"auto-form-create":function(e){return t.form=e},layout:"horizontal","form-layout":!0,"label-width":"80px",col:e.col}},[i("ta-form-item",{attrs:{label:"清算期号","field-decorator-id":"aae043"}},[i("ta-select",{staticStyle:{width:"100%"},attrs:{showSearch:"",placeholder:"请选择清算期号",options:e.aae043List,"allow-clear":!0}})],1),i("ta-form-item",{attrs:{label:"住院门诊号","field-decorator-id":"akc190"}},[i("ta-suggest",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入住院门诊号","option-config":{value:"value",label:"value"},"data-source":e.admissionNumList,"table-title-map":new Map([["label",{name:"姓名",style:{minWidth:"90px"}}],["value",{name:"住院门诊号",style:{minWidth:"180px"}}]])},on:{select:function(t,a){return e.admissionNumChange(t,a)},search:e.admissionNumSearch}})],1),i("ta-form-item",{attrs:{label:"疑点类型","field-decorator-id":"dataType"}},[i("ta-select",{attrs:{placeholder:"疑点类型筛选","allow-clear":!0}},[i("ta-select-option",{attrs:{value:"1"}},[e._v(" 初审疑点 ")]),i("ta-select-option",{attrs:{value:"3"}},[e._v(" 病例申诉 ")])],1)],1),i("ta-form-item",{attrs:{label:"医保类型","field-decorator-id":"aae141"}},[i("ta-select",{attrs:{placeholder:"医保类型筛选","collection-type":"AAE141","allow-clear":""}})],1),i("ta-form-item",{attrs:{label:"开单科室","field-decorator-id":"aac004"}},[i("ta-suggest",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入开单科室","option-config":{value:"value",label:"label"},"data-source":e.deptList,"table-title-map":new Map([["label",{name:"科室名称",style:{minWidth:"120px"}}]])},on:{select:function(t,a){return e.deptChange(t,a)},search:e.deptSearch}})],1),i("ta-form-item",{attrs:{label:"规则名称","field-decorator-id":"aaa167"}},[i("ta-suggest",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入规则名称","option-config":{value:"value",label:"label"},"data-source":e.ruleNameList,"table-title-map":new Map([["label",{name:"规则名称",style:{minWidth:"120px"}}]])},on:{select:function(t,a){return e.ruleNameChange(t,a)},search:e.ruleNameSearch}})],1),i("ta-form-item",{attrs:{label:"参保人","field-decorator-id":"aac003"}},[i("ta-suggest",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入参保人","option-config":{value:"value",label:"label"},"data-source":e.patientNameList,"table-title-map":new Map([["label",{name:"参保人",style:{minWidth:"120px"}}]])},on:{select:function(t,a){return e.patientNameChange(t,a)},search:e.patientNameSearch}})],1),i("ta-form-item",{attrs:{label:"申诉理由","field-decorator-id":"aaz560"}},[i("ta-input",{attrs:{placeholder:"请输入申诉理由"}})],1),i("ta-form-item",{attrs:{label:"医保项目","field-decorator-id":"ake001"}},[i("ta-suggest",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入医保项目名称","option-config":{value:"value",label:"label"},"data-source":e.objNameList,"table-title-map":new Map([["label",{name:"医保项目名称",style:{minWidth:"120px"}}]])},on:{select:function(t,a){return e.objNameChange(t,a)},search:e.objNameSearch}})],1),i("ta-form-item",{attrs:{label:"开单医生","field-decorator-id":"aaz570"}},[i("ta-suggest",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入开单医生","option-config":{value:"value",label:"label"},"data-source":e.doctorList,"table-title-map":new Map([["label",{name:"开单医生",style:{minWidth:"120px"}}]])},on:{select:function(t,a){return e.doctorChange(t,a)},search:e.doctorSearch}})],1),i("ta-form-item",{attrs:{label:"收费类别","field-decorator-id":"ake003"}},[i("ta-suggest",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入收费类别","option-config":{value:"value",label:"label"},"data-source":e.costTypeList,"table-title-map":new Map([["label",{name:"收费类别",style:{minWidth:"120px"}}]])},on:{search:e.costTypeSearch}})],1),i("ta-form-item",{attrs:{"field-decorator-id":"akb020",label:"院区标识"}},[i("ta-select",{attrs:{showSearch:"",placeholder:"院区标识筛选",allowClear:"",options:e.hosList}})],1)],1)],1),i("div",{staticClass:"query-btn-2",staticStyle:{width:"250px"}},[i("div",{staticClass:"ctrl-btn",on:{click:e.formShowAllChange}},[e._v(" "+e._s(e.formShowAll?"收起":"展开")+" "),e.formShowAll?i("ta-icon",{attrs:{type:"caret-up"}}):i("ta-icon",{attrs:{type:"caret-down"}})],1),i("ta-button",{attrs:{icon:"search",type:"primary"},on:{click:e.queryTableData}},[e._v(" 查询 ")]),i("ta-button",{attrs:{icon:"redo",type:"default"},on:{click:e.resetForm}},[e._v(" 重置 ")])],1)])]),i("div",{staticClass:"fit"},[i("div",{staticClass:"part-top"},[i("ta-title",{attrs:{title:"申诉处理"}})],1),i("div",{staticClass:"part-top1"},[i("div",{staticClass:"part-title"},[i("tag-change",{attrs:{"tag-values":e.tagValues},on:{change:e.changeTag}})],1),i("div",{staticClass:"part-operate"},[e.tagValues.some((function(t){return["1","5"].includes(t)}))?i("ta-button",{attrs:{type:"primary"},on:{click:e.openBatchApprovalModel}},[e._v(" 批量审批 ")]):e._e(),i("ta-button",{attrs:{slot:"reference",id:"exportDataButton",type:"primary"},on:{click:e.exportData},slot:"reference"},[e._v(" 导出 ")]),e.enabeledCenterAppeal?i("ta-button",{attrs:{slot:"reference",id:"exportDataButton",icon:"upload",type:"primary"},on:{click:e.openCenterAppealModal},slot:"reference"},[e._v(" 两定申诉上传 ")]):e._e()],1)]),i("div",{staticStyle:{height:"calc(100% - 104px)","min-height":"300px"}},[i("ta-big-table",{ref:"xTable",staticStyle:{width:"100%"},attrs:{border:"","highlight-hover-row":"","show-overflow":"","tooltip-config":{theme:"light",contentMethod:e.tooltipMethod},"sort-config":{trigger:"cell"},"checkbox-config":{trigger:"row",checkMethod:function(t){return"3"===t.row.handStas}},data:e.dataSource,height:"auto",resizable:!0,"auto-resize":"",size:"mini","empty-text":"-",width:"min-width","cell-style":e.cellStyle,"filter-config":{showIcon:e.tagValues.some((function(t){return["5"].includes(t)}))}},scopedSlots:e._u([{key:"bottomBar",fn:function(){return[i("ta-pagination",{ref:"gridPager",staticStyle:{"text-align":"right"},attrs:{size:"small",defaultPageSize:200,pageSizeOptions:["30","50","100","200","500"],"data-source":e.dataSource,url:e.pageUrl,params:e.pageParams},on:{"update:dataSource":function(t){e.dataSource=t},"update:data-source":function(t){e.dataSource=t}}})]},proxy:!0}])},[i("ta-big-table-column",{attrs:{type:"checkbox",fixed:"left",width:"40px",align:"center"}}),i("ta-big-table-column",{attrs:{type:"seq",fixed:"left",width:"50px",title:" ",align:"center"}}),i("ta-big-table-column",{attrs:{field:"aaz213",title:"管理Id",visible:!1}}),i("ta-big-table-column",{attrs:{fixed:"left",field:"aae043","min-width":"75px",title:"期号",sortable:""}}),i("ta-big-table-column",{attrs:{fixed:"left",field:"akc195","show-overflow":"",title:"申诉截止日期","min-width":"130px",sortable:""}}),i("ta-big-table-column",{attrs:{fixed:"left",align:"center",field:"appealFlag",title:"是否申诉","min-width":"100px",sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;t.column;return[e._v(" "+e._s("1"===a.appealFlag?"申诉":"不申诉")+" ")]}}])}),i("ta-big-table-column",{attrs:{fixed:"left",field:"currPersName",title:"申诉处理人","min-width":"120",sortable:""}}),i("ta-big-table-column",{attrs:{fixed:"left",field:"aaz560",title:"申诉理由","min-width":"180px"}}),i("ta-big-table-column",{attrs:{fixed:"left",field:"",title:"申诉材料","min-width":"100px"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("div",{staticStyle:{position:"relative"}},[a.fileNum>0?i("div",{staticStyle:{color:"#1B65B9",cursor:"pointer"},on:{mouseover:e.showFileList,mouseout:e.hideFileList}},[i("span",{staticStyle:{cursor:"pointer"},attrs:{id:"downloadButton"},on:{click:function(t){return e.fnDownloadFile(a.aaz213)}}},[e._v("附件("+e._s(a.fileNum)+")")]),e._v("   "),i("ta-icon",{attrs:{type:"search"},on:{click:function(t){return e.imageViewerHandle(a.aaz213)}}})],1):i("div",[e._v(" 无 ")]),i("div",{staticStyle:{position:"absolute",left:"100px","max-width":"250px","min-width":"100px",height:"auto"},attrs:{visible:a.fileListShow}},[e._v(" "+e._s(a.fileNames)+" ")])])]}}])}),i("ta-big-table-column",{attrs:{field:"akc190",title:"住院门诊号","min-width":"120px",sortable:""}}),i("ta-big-table-column",{attrs:{sortable:"",field:"akb021","show-overflow":"",title:"医院名称","min-width":"110px"}}),i("ta-big-table-column",{attrs:{field:"aac003",title:"参保人","min-width":"100px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"ake001",title:"项目编码","min-width":"100px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"ake002",title:"医保项目名称","min-width":"140px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"aaa167",title:"违反规则","min-width":"180px",sortable:""}}),i("ta-big-table-column",{attrs:{"show-overflow":"",field:"matchStas",title:"预警记录",width:"120px",align:"center","sort-by":"matchStas",sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return["1"===a.matchStas?i("div",[i("div",{staticClass:"opareteItem",on:{click:function(t){return e.viewRecords(a)}}},[e._v(" 查看 ")])]):i("div",[i("span",[e._v("无")])])]}}])}),i("ta-big-table-column",{attrs:{field:"aac004",title:"开单科室","min-width":"100px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"aaz570",title:"开单医生","min-width":"100",sortable:""}}),i("ta-big-table-column",{attrs:{field:"aae036",title:"明细时间","min-width":"150px",align:"center",sortable:""}}),i("ta-big-table-column",{attrs:{field:"ape804",title:"初审违规金额","min-width":"130",align:"right",formatter:"formatAmount",sortable:""}}),i("ta-big-table-column",{attrs:{fixed:"right",field:"handStas",title:"状态","min-width":"120","collection-type":"APPEALHANDSTAS",filters:[{data:""}],"filter-method":e.filterMethod,sortable:""},scopedSlots:e._u([{key:"filter",fn:function(t){var a=t.$panel,l=t.column;return e._l(l.filters,(function(t,l){return i("ta-select",{key:l,staticStyle:{margin:"10px",width:"120px"},attrs:{"get-popup-container":e.setPopupContainer,"collection-type":"APPEALHANDSTAS","collection-filter":e.filterStr,"reverse-filter":!0},on:{change:function(e){return a.changeOption(e,!!t.data,t)}},model:{value:t.data,callback:function(a){e.$set(t,"data",a)},expression:"option.data"}})}))}}])}),i("ta-big-table-column",{attrs:{fixed:"right",field:"operate",title:"操作",width:"100",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return["3"==a.handStas?i("a",{staticStyle:{color:"#1B65B9"},on:{click:function(t){return e.approval(a)}}},[e._v(" 审批 ")]):i("a",{staticStyle:{color:"#1B65B9"},on:{click:function(t){return e.showDetail(a)}}},[e._v(" 详情 ")]),"4"!=a.handStas&&"6"!=a.handStas&&"10"!=a.handStas||!e.fnExpired(a)?e._e():i("a",{staticStyle:{color:"#1B65B9"},on:{click:function(t){return e.backRow(a)}}},[i("ta-divider",{attrs:{type:"vertical"}}),e._v(" 撤回 ")],1)]}}])})],1)],1)])]),i("ta-modal",{attrs:{title:"批量审核",visible:e.batchApplovalVisible,height:220,width:500},on:{ok:e.handleBatchApproval,cancel:e.closeBatchApprovalModel}},[i("ta-form",{attrs:{"auto-form-create":function(e){return t.form1=e},layout:"horizontal","form-layout":!0,"label-width":"80px",col:{xs:1,sm:1,md:1,lg:1,xl:1,xxl:1}}},[i("ta-form-item",{attrs:{label:"审批结果","field-decorator-id":"handStats",require:!0}},[i("ta-radio-group",{attrs:{options:[{value:"4",label:"通过"},{value:"6",label:"驳回"}],"default-value":"1"},on:{change:e.changeApprovalResult}})],1),"6"===e.approvalResult?i("ta-form-item",{attrs:{label:"驳回原因","field-decorator-id":"rejctReas",require:"6"===e.approvalResult,fieldDecoratorOptions:{rules:[{max:2e3,message:"不得超过2000字符"}]}}},[i("ta-textarea",{attrs:{placeholder:"请输入驳回原因",rows:5,"show-length":!0}})],1):e._e()],1)],1),i("distribute",{attrs:{visible:e.distributeVisible,params:e.paramsforDistrubute,"dept-list":e.deptList},on:{handleClose:e.handleClose}}),i("plugin",{attrs:{visible:e.pluginVisible,"first-time":e.firstTime},on:{handleClose:e.handleClose1,handleOk:e.updatePlugin}}),i("approval",{attrs:{visible:e.approvalVisible,param:e.rowData},on:{handleClose:e.handleClose2}}),i("re-distribute",{attrs:{visible:e.reDistributeVisible,params:e.paramsforDistrubute},on:{handleClose:e.handleClose}}),i("view-record",{attrs:{title:"预警记录",visible:e.uploadVisible4,param:e.mateRow},on:{handleClose:e.handleClose4}}),e.isShow?i("ta-image-viewer",{ref:"myViewer",attrs:{visible:e.isShow,images:e.images},on:{"update:visible":function(t){e.isShow=t},shown:e.shown},model:{value:e.viewerVal,callback:function(t){e.viewerVal=t},expression:"viewerVal"}}):e._e(),i("ta-modal",{attrs:{title:"两定平台申诉上传",height:220,width:440},model:{value:e.centerUploadModalVisible,callback:function(t){e.centerUploadModalVisible=t},expression:"centerUploadModalVisible"}},[i("div",[i("span",{staticStyle:{"margin-left":"19px","font-size":"22px",display:"block"}},[e._v(" 请确认是否要将查询出的疑点及申诉材料自动上传至两定平台？")]),i("br"),i("ta-descriptions",{staticStyle:{"margin-left":"18px",width:"95%"},attrs:{title:""}},[i("ta-descriptions-item",{attrs:{label:" "}},[e._v(" 说明：系统自动上传时间可能较长，上传成功后系统会发送消息，请留意系统右上角消息通知。点击“接口测试”，可测试两定平台接口连接是否异常。 ")])],1)],1),i("div",{attrs:{slot:"footer"},slot:"footer"},[i("ta-button",{on:{click:e.closeVisibleModal}},[e._v(" 关闭 ")]),i("ta-button",{on:{click:function(t){return e.testCenterInterface()}}},[e._v(" 接口测试 ")]),i("ta-button",{staticStyle:{"margin-right":"20px"},attrs:{type:"primary"},on:{click:e.uploadCenterData}},[e._v(" 上传 ")])],1)])],1)},l=[],s=(a(32564),a(66670)),o=a(52582),r=a(36797),n=a.n(r),c=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",t._l(t.tagList,(function(e){return a("ta-tag",{key:e.label,attrs:{color:e.checked?"#1890ff":""},on:{click:function(a){return t.handleChange(e)}}},[a("span",{staticClass:"tag-icon"},[t._v(t._s(e.icon))]),a("span",{staticClass:"tag-label"},[t._v(" "+t._s(e.label)+" ")])])})),1)},u=[],d=a(7485),h={name:"tagChange",props:{tagValues:{type:Array,default:function(){return[]}},statusArr:{type:Array,default:function(){return d.H}}},data:function(){return{tagList:this.statusArr}},watch:{tagValues:{immediate:!0,handler:function(t){this.syncCheckedState(t)}}},mounted:function(){this.syncCheckedState(this.tagValues)},methods:{syncCheckedState:function(t){this.tagList=this.tagList.map((function(e){return e.checked=t.includes(e.value),e}))},handleChange:function(t){"全部"===t.label?this.tagList.forEach((function(t){t.checked="全部"===t.label})):(this.tagList.forEach((function(t){"全部"===t.label&&(t.checked=!1)})),t.checked=!t.checked,this.tagList.some((function(t){return t.checked}))||(t.checked=!0));var e=this.tagList.filter((function(t){return t.checked})).map((function(t){return t.value}));this.$emit("change",e)}}},f=h,p=a(1001),m=(0,p.Z)(f,c,u,!1,null,"7af2fefa",null),b=m.exports,g=a(88412),v=a(10573),w=a(28170),y=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("ta-modal",{attrs:{title:e.title,visible:e.visible,height:700,width:1e3},on:{ok:e.handleSave,cancel:e.handleClose}},[i("ta-border-layout",{staticStyle:{height:"calc(100%)"},attrs:{"layout-type":"fixTop"}},[i("div",{staticClass:"query-header",attrs:{slot:"header"},slot:"header"},[i("ta-form",{attrs:{"auto-form-create":function(e){return t.form=e},layout:"horizontal","form-layout":!0,"label-width":"80px",col:{xs:1,sm:2,md:2,lg:4,xl:4,xxl:4}}},[i("ta-form-item",{attrs:{label:"清算期号","field-decorator-id":"aae043","init-value":e.moment().format("YYYYMM"),require:!0}},[i("ta-month-picker",{attrs:{"value-format":"YYYYMM",format:"YYYYMM"}})],1),i("ta-form-item",{attrs:{label:"住院号","field-decorator-id":"akc190"}},[i("ta-suggest",{staticStyle:{width:"100%"},attrs:{"option-config":{value:"value",label:"value"},"data-source":e.admissionNumList,"table-title-map":new Map([["label",{name:"姓名",style:{minWidth:"80px"}}],["value",{name:"住院号",style:{minWidth:"180px"}}]])},on:{select:function(t,a){return e.admissionNumChange(t,a)},search:e.admissionNumSearch}})],1),i("ta-form-item",{attrs:{label:"下发医生","field-decorator-id":"currPers"}},[i("ta-suggest",{staticStyle:{width:"100%"},attrs:{"option-config":{value:"value",label:"label"},"data-source":e.doctorList,"table-title-map":new Map([["label",{name:"开单医生",style:{minWidth:"120px"}}]])},on:{select:function(t,a){return e.doctorChange(t,a)},search:e.doctorSearch}})],1),i("ta-form-item",{attrs:{label:"","field-decorator-id":"","label-width":30}},[i("ta-button",{staticStyle:{display:"inline-block","margin-right":"8px"},attrs:{type:"primary"},on:{click:e.queryTableData}},[e._v(" 查询 ")]),i("ta-button",{staticStyle:{display:"inline-block"},attrs:{icon:"redo",type:"default"},on:{click:e.resetForm}},[e._v(" 重置 ")])],1)],1)],1),i("div",{staticClass:"fit"},[i("ta-big-table",{ref:"xTable",staticStyle:{width:"100%"},attrs:{border:"","highlight-hover-row":"","show-overflow":"","sort-config":{trigger:"cell"},data:e.dataSource,height:"auto",resizable:!0,"auto-resize":"",size:"mini","empty-text":"-",width:"min-width","mouse-config":{selected:!0},"edit-config":{trigger:"click",mode:"cell",showStatus:!0},"keyboard-config":{isArrow:!0,isDel:!0,isEnter:!0,isTab:!0,isEdit:!0},"checkbox-config":{checkField:"checked"},"edit-rules":e.validRules},on:{"cell-selected":e.handleCellSelect},scopedSlots:e._u([{key:"bottomBar",fn:function(){return[i("ta-pagination",{ref:"gridPager",staticStyle:{"text-align":"right"},attrs:{size:"small",defaultPageSize:200,pageSizeOptions:["30","50","100","200","500"],"data-source":e.dataSource,url:e.pageUrl,params:e.pageParams},on:{"update:dataSource":function(t){e.dataSource=t},"update:data-source":function(t){e.dataSource=t}}})]},proxy:!0}])},[i("ta-big-table-column",{attrs:{type:"seq",fixed:"left",width:"50px",title:" ",align:"center"}}),i("ta-big-table-column",{attrs:{field:"id",title:"管理Id",visible:!1}}),i("ta-big-table-column",{attrs:{field:"aaz213",title:"管理Id",visible:!1}}),i("ta-big-table-column",{attrs:{field:"akc190",title:"住院号","min-width":"100px"}}),i("ta-big-table-column",{attrs:{field:"aac003",title:"患者姓名","min-width":"80px"}}),i("ta-big-table-column",{attrs:{field:"ake006",title:"项目名称","min-width":"140px"}}),i("ta-big-table-column",{attrs:{field:"aaa167",title:"违反规则","min-width":"180px"}}),i("ta-big-table-column",{attrs:{field:"aaz560",title:"申诉理由","min-width":"180px"}}),i("ta-big-table-column",{attrs:{field:"aae386",title:"开单科室","min-width":"100px"}}),i("ta-big-table-column",{attrs:{field:"aaz570",title:"开单医生","min-width":"100"}}),i("ta-big-table-column",{attrs:{field:"aae036",title:"明细时间","min-width":"150px",align:"center"}}),i("ta-big-table-column",{attrs:{field:"handStas",title:"申诉状态","min-width":"100","collection-type":"appealhandstas"}}),i("ta-big-table-column",{attrs:{field:"currPers",title:"下发人员",width:"100",visible:!1}}),i("ta-big-table-column",{attrs:{fixed:"right",field:"currPersName",title:"下发人员",width:"120","edit-render":{}},scopedSlots:e._u([{key:"edit",fn:function(t){var a=t.row,l=t.column;return[i("ta-suggest",{staticStyle:{width:"100%"},attrs:{"auto-focus":"","data-source":e.peopleList,"table-title-map":new Map([["label",{name:"下发人员",style:{minWidth:"120px"}}]]),"option-config":{value:"value",label:"label"},"dropdown-match-select-width":!1,"dropdown-style":{width:"100%"},"get-popup-container":e.setPopupContainer},on:{select:function(t,i){return e.handleChangeSuggest(t,a,l,i)},search:e.handleSearch,focus:function(){e.peopleList=[{label:a.currPersName,value:a.currPers}]}},model:{value:a.currPers,callback:function(t){e.$set(a,"currPers",t)},expression:"row.currPers"}})]}}])})],1)],1)])],1)},S=[],x={name:"reDistribute",components:{},props:{visible:{type:Boolean,default:!1},params:{type:Object,default:function(){return{period:n()()}}}},data:function(){return{title:"重新下发申诉",dataSource:[],pageUrl:o.Z.getDistributePageUrl(),validRules:{currPersName:[{required:!0,message:"必填"}]},doctorList:[],admissionNumList:[],peopleList:[]}},watch:{visible:{handler:function(t){var e=this;t&&this.$nextTick((function(){e.queryTableData()}))}}},mounted:function(){},methods:{moment:n(),admissionNumChange:function(t,e){},admissionNumSearch:function(t){var e=this;t&&o.Z.getAdmissionNumList({aae043:this.form.getFieldsValue().aae043,searchVal:t},(function(t){e.admissionNumList=t.data.list}))},doctorChange:function(t,e){},doctorSearch:function(t){var e=this;t&&o.Z.queryPeople({doctorName:t},(function(t){e.doctorList=t.data.list}))},setPopupContainer:function(t){return t.parentNode},handleSearch:function(t){var e=this;t&&o.Z.queryPeople({doctorName:t},(function(t){e.peopleList=t.data.list}))},handleChangeSuggest:function(t,e,a,i){e.currPers=t,e.currPersName=i.label,this.$refs.xTable.updateStatus({row:e,column:a}),o.Z.reOneModefy({aae043:e.aae043,aaz213:e.aaz213,currPers:t},(function(t){}))},handleCellSelect:function(t){t.row,t.rowIndex,t.$rowIndex,t.column,t.columnIndex,t.$columnIndex},pageParams:function(){var t=this.form.getFieldsValue();t.status="1";var e=this.$refs.gridPager.getPagerInfo();return Object.assign(t,e),t},queryTableData:function(){var t=this;this.form.validateFields((function(e){e||t.$refs.gridPager.loadData()}))},resetForm:function(){this.form.resetFields(),this.form.setFieldsValue({aae043:n()().format("YYYYMM")})},handleSave:function(){var t=this;this.$refs.xTable.fullValidate(null,(function(e){e||o.Z.reDistributeSave({aae043:t.dataSource[0].aae043},(function(e){t.$message.success("保存成功"),t.$emit("handleClose")}))}))},handleClose:function(){this.peopleList=[],this.doctorList=[],this.admissionNumList=[],this.$emit("handleClose")}}},k=x,C=(0,p.Z)(k,y,S,!1,null,"e163cb26",null),V=C.exports,L=a(39101),N={name:"appealForMedicalInsurance",components:{ViewRecord:L.Z,ReDistribute:V,Approval:w.Z,TagChange:b,Plugin:v.Z,distribute:s.Z,TaTitle:g.Z},data:function(){return{col:{xs:2,sm:2,md:2,lg:3,xl:4,xxl:4},admissionNumList:[],aae043List:[],ruleNameList:[],patientNameList:[],objNameList:[],doctorList:[],costTypeList:[],deptList:[],nameDisable:!1,formShowAll:!0,hosList:[],tagValues:["1"],uploadVisible4:!1,mateRow:{},dataSource:[],pageUrl:o.Z.getPageUrl(),editType:"add",distributeVisible:!1,reDistributeVisible:!1,paramsforDistrubute:{},pluginVisible:!1,firstTime:!1,batchApplovalVisible:!1,approvalResult:"",approvalVisible:!1,rowData:{},searchParam:{},viewerVal:0,isShow:!1,images:[],basePath:o.Z.getBasePath()+"/complainFile/",centerUploadModalVisible:!1,enabeledCenterAppeal:!1}},computed:{formBoxStyle:function(){return this.formShowAll?{height:"auto"}:{height:"50px"}},filterStr:function(){return this.tagValues.includes("5")?"3,4,6,7,10,11":""}},watch:{},mounted:function(){var t=this;this.$nextTick((function(){t.tagValues=["1"],t.fnQueryHos(),t.getAKb020ByJobNumber(),t.queryAae043List((function(){t.aae043List.length>0&&t.form.setFieldsValue({aae043:t.aae043List[0].value}),t.queryTableData()}))}))},methods:{moment:n(),fnExpired:function(t){if(t.akc195){var e=n()(t.akc195),a=n()().startOf("day");return!e.isBefore(a,"day")}return!1},backRow:function(t){var e=this;o.Z.reBackRow(t,(function(t){e.queryTableData(),e.$message.success("撤回成功")}))},fnQueryHos:function(){var t=this,e={url:"/miimCommonRead/queryHospRpcList",autoValid:!0},a={successCallback:function(e){t.hosList=e.data.resultData},failCallback:function(e){t.$message.error("医院数据加载失败")}};this.Base.submit(null,e,a)},imageViewerHandle:function(t){var e=this;t&&o.Z.getFileInfo({aaz213:t},(function(t){e.images=[],t.data.data&&(t.data.data.forEach((function(t){e.images.push({url:e.basePath+t.savePath.replace(/\\/g,"/"),title:t.fileName})})),e.isShow=!0)}))},shown:function(){},viewRecords:function(t){t.ykc610||t.ykc610V||this.$message.error("当前费用流水号为空！请检查数据！"),this.uploadVisible4=!0,this.mateRow=t},handleClose4:function(t){this.mateRow={},this.uploadVisible4=!1},queryAae043List:function(t){var e=this,a=this.tagValues;o.Z.queryAae043List({currPers:this.currPers,queryStat:a},(function(a){e.aae043List=a.data.data.map((function(t){return{value:t,label:t}})),t&&t()}))},setPopupContainer:function(t){return t.parentNode},tooltipMethod:function(t){t.items;var e=t.row,a=(t.rowIndex,t.$rowIndex,t.column,t.columnIndex);t.$columnIndex,t.type,t.cell,t.$event;if(9===a)return e.fileNames},filterMethod:function(t){var e=t.option,a=t.row;return a.handStas===e.data},admissionNumChange:function(t,e){t||(this.nameDisable=!1),this.nameDisable=!0},costTypeSearch:function(t){var e=this;t&&o.Z.getCostTypeList({aae043:this.form.getFieldsValue().aae043,searchVal:t},(function(t){e.costTypeList=t.data.list}))},admissionNumSearch:function(t){var e=this;t&&o.Z.getAdmissionNumList({aae043:this.form.getFieldsValue().aae043,searchVal:t},(function(t){e.admissionNumList=t.data.list}))},deptChange:function(t,e){},deptSearch:function(t){var e=this;t&&o.Z.getDeptList({aae043:this.form.getFieldsValue().aae043,searchVal:t},(function(t){e.deptList=t.data.list}))},ruleNameChange:function(t,e){},ruleNameSearch:function(t){var e=this;t&&o.Z.getRuleNameList({aae043:this.form.getFieldsValue().aae043,searchVal:t},(function(t){e.ruleNameList=t.data.list}))},patientNameChange:function(t,e){},patientNameSearch:function(t){var e=this;t&&o.Z.getPatintNameList({aae043:this.form.getFieldsValue().aae043,searchVal:t},(function(t){e.patientNameList=t.data.list}))},objNameChange:function(t,e){},objNameSearch:function(t){var e=this;t&&o.Z.getObjNameList({aae043:this.form.getFieldsValue().aae043,searchVal:t},(function(t){e.objNameList=t.data.list}))},doctorChange:function(t,e){},doctorSearch:function(t){var e=this;t&&o.Z.getDoctorList({aae043:this.form.getFieldsValue().aae043,searchVal:t},(function(t){e.doctorList=t.data.list}))},formShowAllChange:function(){this.formShowAll=!this.formShowAll},handleMenuClick:function(t){this.handleOpen(t.key)},changeTag:function(t){var e=this;this.tagValues=t,this.queryAae043List((function(){e.queryTableData()}))},openBatchApprovalModel:function(){this.$refs.xTable.getCheckboxRecords().length>0?this.batchApplovalVisible=!0:this.$message.warning("请选择需要审核的数据")},closeBatchApprovalModel:function(){this.batchApplovalVisible=!1,this.form1.resetFields(),this.approvalResult="",this.queryTableData()},changeApprovalResult:function(t){this.approvalResult=t.target.value},handleBatchApproval:function(){var t=this,e=this.form1.getFieldsValue();e.aae043=this.dataSource[0].aae043,e.idList=this.$refs.xTable.getCheckboxRecords().map((function(t){return t.aaz213})),e.rejctPers=this.$route.query.aaz263,e.audit_pers=this.$route.query.aaz263,this.$nextTick((function(){t.form1.validateFields((function(a){a||o.Z.batchApproval(e,(function(e){t.batchApplovalVisible=!1,t.form1.resetFields(),e.errors&&e.errors.length>0&&t.$message.error(e.errors[0].msg),t.closeBatchApprovalModel()}))}))}))},openCenterAppealModal:function(){this.centerUploadModalVisible=!this.centerUploadModalVisible},closeVisibleModal:function(){this.centerUploadModalVisible=!this.centerUploadModalVisible},testCenterInterface:function(){var t=this;this.Base.submit(null,{url:"/appeal/hidAppeal/testCenterInterface",data:this.pageParams()},{successCallback:function(e){"200"===e.data.code&&t.$message.success("连接成功")}})},uploadCenterData:function(){var t=this;Base.showMask({show:!0,text:"上传中"}),this.Base.submit(null,{url:"/appeal/hidAppeal/uploadCenterAppeal",data:this.pageParams()},{successCallback:function(t){Base.showMask({show:!1})},failCallback:function(e){t.$message.error(e.errors[0])}})},getAKb020ByJobNumber:function(){var t=this;this.Base.submit(null,{url:"/appeal/common/getAKb020ByJobNumber",data:{loginId:""}},{successCallback:function(e){var a=e.data.akb020;a&&t.Base.submit(null,{url:"miimCommonRead/queryProcessConfig",data:{akb020:a}},{successCallback:function(e){t.enabeledCenterAppeal=e.data.data.enabeledCenterAppeal}})},failCallback:function(e){t.$message.error(e.errors[0])}})},exportData:function(){var t=this,e=document.getElementById("exportDataButton");if(!e.disabled){e.disabled=!0;var a=n()().format("YYYY-MM-DD HH:mm:ss");Base.downloadFile({type:"application/zip",fileName:"申诉材料文件["+a+"].zip",url:"/appeal/hidAppeal/exportZip",options:this.pageParams()}).then((function(e){t.$message.success("导出成功")})).catch((function(e){t.$message.error("导出失败")})),setTimeout((function(){e.disabled=!1}),3e3)}},approval:function(t){this.approvalVisible=!0,this.rowData=t},showDetail:function(t){this.approvalVisible=!0,this.rowData=t},showFileList:function(t){t.fileListShow=!0,this.$refs.xTable.updateStatus({row:t})},fnDownloadFile:function(t){var e=document.getElementById("downloadButton");e.disabled||(e.disabled=!0,window.location.href=o.Z.getPicZipUrl()+"?aaz213="+t,this.queryTableData(),setTimeout((function(){e.disabled=!1}),3e3))},hideFileList:function(t){t.fileListShow=!1,this.$refs.xTable.updateStatus({row:t})},pageParams:function(){var t=this.form.getFieldsValue();t.queryStat=this.tagValues,this.param=t;var e=this.$refs.gridPager.getPagerInfo();return Object.assign(t,e),t},queryTableData:function(){var t=this;this.form.validateFields((function(e){e||t.$refs.gridPager.loadData((function(e){t.dataSource=e.data.pageBean.list.map((function(t){return t.fileListShow=!1,t.hospitalizationNum=n()(t.aae031).diff(n()(t.aae030),"day"),t}))}))}))},resetForm:function(){this.form.resetFields()},handleOpen:function(t){var e=this;switch(t){case"1":this.pluginVisible=!0;break;case"2":o.Z.isPluginSetted({},(function(t){t.data.hasConfig?e.distributeVisible=!0:(e.pluginVisible=!0,e.firstTime=!0)})),this.distributeVisible=!0;break;case"3":this.reDistributeVisible=!0;break}},updatePlugin:function(){this.distributeVisible=!0},handleClose:function(){this.distributeVisible=!1,this.reDistributeVisible=!1,this.paramsforDistrubute={},this.queryTableData()},handleClose1:function(t){this.pluginVisible=!1,this.firstTime=!1,t?this.distributeVisible=!0:this.queryTableData()},handleClose2:function(){this.approvalVisible=!1,this.rowData={},this.queryTableData()},cellStyle:function(t){var e=t.row,a=(t.rowIndex,t.column);t.columnIndex;if("chainRate"===a.property){if(e.chainRate>0)return{color:"#0F990F"};if(e.chainRate<0)return{color:"#E4393C"}}}}},A=N,_=(0,p.Z)(A,i,l,!1,null,"bc87ce76",null),P=_.exports}}]);
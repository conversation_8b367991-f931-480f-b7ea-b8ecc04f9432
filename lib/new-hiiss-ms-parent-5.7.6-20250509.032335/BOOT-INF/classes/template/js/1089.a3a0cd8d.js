"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[1089],{88412:function(t,e,a){var i=a(26263),r=a(36766),n=a(1001),o=(0,n.Z)(r.Z,i.s,i.x,!1,null,"5e7ef0ae",null);e["Z"]=o.exports},1089:function(t,e,a){a.r(e),a.d(e,{default:function(){return Ct}});var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"fit"},[a("div",{staticStyle:{height:"120px"}},[a("ta-border-layout",[a("div",{staticStyle:{overflow:"hidden",height:"100%"}},[a("search-term",{ref:"term",staticClass:"fit",on:{fnQuery:t.fnQuery}})],1)])],1),a("div",{staticStyle:{height:"320px","margin-top":"-20px"}},[a("ta-border-layout",{attrs:{layout:{left:"29%"}}},[a("div",{attrs:{slot:"left"},slot:"left"},[a("pie-num",{ref:"pieNum",attrs:{url:"refusalAnalysis/queryGeneralAnalysis"},on:{fnLinkQuery:t.fnLinkQuery}})],1),a("div",[a("line-num",{ref:"lineNum",attrs:{paramData:t.parameters,url:"refusalAnalysis/queryDeptGeneralAnalysis"},on:{fnLinkQuery:t.fnLinkQuery}})],1)])],1),a("div",{staticStyle:{height:"620px"}},[a("ta-border-layout",{staticClass:"border",attrs:{layout:{left:"25%",right:"25%"},"show-border":!1}},[a("div",{attrs:{slot:"left"},slot:"left"},[a("rule-num",{ref:"ruleNum",attrs:{paramData:t.parameters,title:"规则排名",url:"refusalAnalysis/queryRuleAnalysis"},on:{fnLinkQuery:t.fnLinkQuery}})],1),a("div",{staticStyle:{height:"100%"}},[a("ta-border-layout",{attrs:{layout:{left:"50%"},"show-border":!1}},[a("div",{attrs:{slot:"left"},slot:"left"},[a("item-num",{ref:"itemNum",attrs:{paramData:t.parameters,title:"项目排名",url:"refusalAnalysis/queryProjectAnalysis"},on:{fnLinkQuery:t.fnLinkQuery}})],1),a("div",[a("dept-num",{ref:"deptNum",attrs:{paramData:t.parameters,title:"科室排名",url:"refusalAnalysis/queryDepartmentAnalysis"},on:{fnLinkQuery:t.fnLinkQuery}})],1)])],1),a("div",{attrs:{slot:"right"},slot:"right"},[a("doc-num",{ref:"docNum",attrs:{paramData:t.parameters,title:"医生排名",url:"refusalAnalysis/queryDoctorAnalysis"},on:{fnLinkQuery:t.fnLinkQuery}})],1)])],1)])},r=[],n=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("div",[i("div",{staticClass:"titleLine"},[i("ta-title",{attrs:{title:"数据归属"}})],1),i("ta-form",{attrs:{autoFormCreate:function(e){t.form=e},layout:"horizontal",formLayout:!0}},[i("ta-form-item",{attrs:{"init-value":e.rangeValue,label:"清算期号","field-decorator-id":"issueNum",labelCol:{span:8},wrapperCol:{span:16},span:6,fieldDecoratorOptions:{rules:[{required:!0,message:"请选择期号!"}]}}},[i("ta-range-picker",{staticStyle:{width:"100%"},attrs:{type:"month","allow-one":!0}})],1),i("ta-form-item",{attrs:{fieldDecoratorId:"dataType","init-value":"1",span:4,required:"",labelCol:{span:8},wrapperCol:{span:15}}},[i("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("数据类型")]),i("ta-select",{attrs:{"collection-type":"DATAANALYSISTYPE",placeholder:"数据类型筛选"}})],1),i("ta-form-item",{staticStyle:{"margin-left":"1%"},attrs:{fieldDecoratorId:"hiFeesetlType",span:4,labelCol:{span:8},wrapperCol:{span:15}}},[i("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("医保类型")]),i("ta-select",{staticStyle:{width:"100%"},attrs:{placeholder:"医保类型选择","collection-type":"aae141",allowClear:""}})],1),i("ta-form-item",{staticStyle:{"margin-left":"1%"},attrs:{fieldDecoratorId:"medType",span:4,labelCol:{span:8},wrapperCol:{span:15}}},[i("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("医疗类别")]),i("ta-select",{attrs:{showSearch:"",placeholder:"医疗类别筛选","collection-type":"AKA130","show-search":!0,"allow-clear":!0}})],1),i("ta-button",{staticStyle:{"margin-left":"24px"},attrs:{type:"primary",icon:"search"},on:{click:e.fnQuery}},[e._v("查询")])],1)],1)},o=[],l=a(89584),s=a(48534),A=(a(36133),a(88412)),c={name:"analysisResultsDisplay",components:{TaTitle:A.Z},props:{},data:function(){return{degValue:"全部规则",aka130List:[],rangeValue:[this.Base.getMoment((new Date).toISOString().slice(0,8),"YYYY-MM"),this.Base.getMoment((new Date).toISOString().slice(0,8),"YYYY-MM")],ruleDegList:[]}},created:function(){},mounted:function(){var t=this;return(0,s.Z)(regeneratorRuntime.mark((function e(){var a,i;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.Base.asyncGetCodeData("APE800");case 2:i=e.sent,(a=t.ruleDegList).push.apply(a,(0,l.Z)(i)),t.ruleDegList.push({name:"全部规则",value:null,label:"全部规则"}),t.queryaka130List(),t.fnQuery();case 7:case"end":return e.stop()}}),e)})))()},methods:{queryaka130List:function(){var t=this,e=this.form.getFieldsValue();this.Base.submit(null,{url:"hiddscgPoint/queryAka130List",data:e},{successCallback:function(e){e.data.aka130List.length>0?t.aka130List=e.data.aka130List:t.aka130List=[]},failCallback:function(t){}})},fnQuery:function(){var t,e,a=this.form.getFieldsValue();if(a.issueNum){if(!a.issueNum[0])return void this.$message.error("请选择完整日期");t=a.issueNum[0].format("YYYYMM"),a.issueNum[1]&&(e=a.issueNum[1].format("YYYYMM"))}a.issueNum=t+"-"+e,this.$emit("fnQuery",a)},handleSelect:function(t){this.degValue=t,this.fnQuery()}}},u=c,m=a(1001),d=(0,m.Z)(u,n,o,!1,null,"d0b45e1a",null),h=d.exports,f=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"titleLine"},[a("ta-title",{attrs:{title:"拒付数据构成"}})],1),a("div",{staticStyle:{display:"flex"}},[a("div",{staticClass:"box"},[a("div",{staticStyle:{display:"flex","flex-wrap":"wrap","margin-left":"25%"}},[a("span",{staticClass:"text"},[t._v("拒付总明细数")]),a("span",{staticClass:"text2"},[t._v(t._s(this.warningTotal))])])]),a("div",{staticClass:"box2"},[a("div",{staticStyle:{width:"20px"}}),a("div",{staticStyle:{display:"flex","flex-wrap":"wrap","margin-left":"15%"}},[a("span",{staticClass:"text"},[t._v("拒付总金额")]),a("span",{staticClass:"text3"},[t._v(t._s(this.warningEarly))])])])]),a("div",{attrs:{id:"piemain"}})])},g=[],p=a(1708),b={name:"pieNum",components:{TaTitle:A.Z},props:{url:String},data:function(){return{option:{title:[],tooltip:{trigger:"item"},legend:{icon:"circle",orient:"vertical",left:"39%",top:"43%",y:"center",itemGap:15,textStyle:{fontSize:17},data:["诊疗","药品","材料","其他","可疑","违规"],formatter:function(t){},tooltip:{show:!0}},series:[{name:"扣款构成",type:"pie",radius:["0%","64%"],center:["18%","62.5%"],avoidLabelOverlap:!1,color:["#FF7272","#36cbcb","#4dcb73","#fad337"],itemStyle:{borderRadius:10,borderColor:"#fff",borderWidth:2},label:{show:!1,position:"center"},emphasis:{label:{show:!1,fontSize:"20",fontWeight:"bold"}},labelLine:{show:!1},data:[]}]},checked:"元",dataCake:[],showCake:[],showCake2:[],totalCount:0,totalAmount:0,bfparams:{},codeList:[],warningTotal:0,warningEarly:0}},mounted:function(){var t=this;this.Base.asyncGetCodeData("APE800").then((function(e){t.codeList=e}))},methods:{fnRemindNum:function(t){var e=this;this.bfparams=t;var a=this;this.myChart=p.init(document.getElementById("piemain")),this.myChart.setOption(this.option);var i={url:this.url,data:t,autoValid:!0},r={successCallback:function(t){var i,r;a.showCake=t.data.hilistType.orderSortByAmount,a.showCake2=t.data.sevDeg.orderSortByAmount,a.warningEarly=t.data.hilistType.totalAmount,a.warningTotal=t.data.warningTotal,t.data.hilistType.totalAmount,t.data.hilistType.totalAmount.toString().length>4&&t.data.hilistType.totalAmount.toString().length,a.showCake2.forEach((function(t,a){t.name=e.Base.getCodeLabel(e.codeList,t.name)})),a.dataCake=[],(i=a.dataCake).push.apply(i,(0,l.Z)(a.showCake)),(r=a.dataCake).push.apply(r,(0,l.Z)(a.showCake2));var n={title:[],legend:{data:a.dataCake,formatter:function(t){for(var e,i,r=0;r<a.dataCake.length;r++)a.dataCake[r].name===t&&(e=a.dataCake[r].value,i=(100*a.dataCake[r].percentage).toFixed(2));var n=["{a|"+t+"\t}","{b|"+i+"%\t}",e+a.checked];return n.join("   ")},textStyle:{rich:{a:{fontSize:14,width:50},b:{fontSize:15,width:65}}}},series:[{color:a.showCake.length>3?["#FF7272","#0086ff","#4dcb73","#fad337"]:["#FF7272","#36cbcb","#4dcb73"],data:a.showCake}]};e.myChart.setOption(n)},failCallback:function(t){e.$message.error("查询失败")}};this.Base.submit(null,i,r)}}},y=b,v=(0,m.Z)(y,f,g,!1,null,"51b6e53c",null),C=v.exports,x=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"titleLine"},[a("ta-title",{attrs:{title:"拒付科室构成"}}),a("div",{staticClass:"buttonClass"},[a("a",{staticClass:"more",on:{click:function(e){return t.handleMore()}}},[t._v("查看明细>>")])])],1),a("div",{attrs:{id:"linemain"}}),a("div",{attrs:{id:"content"}},[a("ta-modal",{attrs:{width:"90%",height:"700px",footer:null,destroyOnClose:!0,getContainer:t.setContainer,title:"查看明细"},model:{value:t.visible,callback:function(e){t.visible=e},expression:"visible"}},[a("detail-info",{attrs:{paramData:t.paramData}})],1)],1)])},w=[],k=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("div",{staticClass:"fit"},[i("div",{staticStyle:{height:"20%",padding:"1px"}},[i("div",{staticClass:"titleLine"}),i("ta-form",{attrs:{layout:"horizontal","form-layout":!0,"label-width":"100px","auto-form-create":function(e){return t.searchForm=e}}},[i("ta-form-item",{attrs:{label:"住院门诊号","field-decorator-id":"akc190",span:6}},[i("ta-input",{staticStyle:{width:"90%"},attrs:{placeholder:"请输入住院门诊号",allowClear:""}})],1),i("ta-form-item",{attrs:{label:"患者姓名","field-decorator-id":"aac003",span:6}},[i("ta-input",{staticStyle:{width:"90%"},attrs:{placeholder:"请输入患者姓名",allowClear:""}})],1),i("ta-form-item",{attrs:{label:"医保目录名称","field-decorator-id":"ake002",span:6}},[i("ta-input",{staticStyle:{width:"90%"},attrs:{placeholder:"请输入医保目录名称",allowClear:""}})],1),i("ta-form-item",{attrs:{label:"医保目录编码","field-decorator-id":"ake001",span:6}},[i("ta-input",{staticStyle:{width:"95%"},attrs:{placeholder:"请输入医保目录编码",allowClear:""}})],1),i("ta-form-item",{attrs:{label:"开单科室","field-decorator-id":"aac004v",span:6}},[i("ta-select",{staticStyle:{width:"90%"},attrs:{"show-search":!0,placeholder:"科室选择",allowClear:"",options:e.deptList}})],1),i("ta-form-item",{attrs:{label:"规则名称","field-decorator-id":"aaa167",span:6}},[i("ta-input",{staticStyle:{width:"90%"},attrs:{placeholder:"请输入规则名称",allowClear:""}})],1),i("ta-form-item",{attrs:{span:6}},[i("ta-button",{staticStyle:{margin:"0 20px",float:"right"},attrs:{type:"default",icon:"redo"},on:{click:e.fnReset}},[e._v("重置")]),i("ta-button",{staticStyle:{float:"right"},attrs:{icon:"search",type:"primary"},on:{click:e.searchTable}},[e._v("查询")])],1)],1)],1),i("div",{staticStyle:{height:"80%",padding:"5px"}},[i("div",{staticStyle:{height:"90%","margin-top":"10px"}},[i("ta-big-table",{ref:"xTable1",attrs:{height:"auto","show-overflow":"",data:e.tableData,border:"","auto-resize":""},scopedSlots:e._u([{key:"bottomBar",fn:function(){return[i("ta-button",{staticStyle:{float:"right","margin-top":"20px"},attrs:{icon:"download",type:"primary"},on:{click:e.exportTable}},[e._v("导出")]),i("ta-pagination",{ref:"gridPager",staticStyle:{float:"right","margin-top":"20px","margin-right":"10px"},attrs:{url:"refusaldata/queryRefusalData","data-source":e.tableData,params:e.userPageParams,defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"]},on:{"update:dataSource":function(t){e.tableData=t},"update:data-source":function(t){e.tableData=t}}})]},proxy:!0}])},[i("ta-big-table-column",{attrs:{type:"seq",title:"序号",width:"55",align:"center"}}),i("ta-big-table-column",{attrs:{field:"akc191",title:"住院门诊号",width:"120",align:"center"}}),i("ta-big-table-column",{attrs:{field:"aac003",title:"患者姓名",width:"100",align:"center"}}),i("ta-big-table-column",{attrs:{field:"aaa167",title:"规则名称",width:"250",align:"center"}}),i("ta-big-table-column",{attrs:{field:"ykz018",title:"违规内容",width:"190",align:"center"}}),i("ta-big-table-column",{attrs:{field:"aaz263",title:"开单医生编码",width:"120",align:"center"}}),i("ta-big-table-column",{attrs:{field:"aaz570",title:"开单医生",width:"120",align:"center"}}),i("ta-big-table-column",{attrs:{field:"aac004v",title:"开单科室",width:"120",align:"center"}}),i("ta-big-table-column",{attrs:{field:"ake002",title:"医保目录名称",width:"200",align:"center"}}),i("ta-big-table-column",{attrs:{field:"ake001",title:"医保目录编码",width:"190",align:"center"}}),i("ta-big-table-column",{attrs:{field:"ape804",title:"初审扣款","min-width":"100",align:"center",formatter:e.moneyFormat}}),i("ta-big-table-column",{attrs:{field:"ape804Re",title:"复审扣款","min-width":"100",align:"center",formatter:e.moneyFormat}})],1)],1)])])},S=[],B=a(66347),L=a(95082),F=a(22722),E=a(55115);E.w3.prototype.Base=Object.assign(E.w3.prototype.Base,(0,L.Z)({},F.Z));var D={name:"detailInfo",props:{paramData:Object},data:function(){return{tableData:[],deptList:[],condition:{}}},mounted:function(){var t=this;this.searchTable(),this.queryDeptList(),this.$nextTick((function(){"1"==t.paramData.dataType?(t.$refs.xTable1.showColumn(t.$refs.xTable1.getColumnByField("ape804")),t.$refs.xTable1.hideColumn(t.$refs.xTable1.getColumnByField("ape804Re"))):(t.$refs.xTable1.showColumn(t.$refs.xTable1.getColumnByField("ape804Re")),t.$refs.xTable1.showColumn(t.$refs.xTable1.getColumnByField("ape804")))}))},methods:{moneyFormat:function(t){var e=t.cellValue;return e?this.formatAmount(e):"--"},formatAmount:function(t){return t.toFixed(2).replace(/\d(?=(\d{3})+\.)/g,"$&,")},fnReset:function(){this.searchForm.resetFields(),this.searchTable()},userPageParams:function(){return this.condition},exportTable:function(){var t,e=this,a=this.userPageParams(),i=[],r=this.$refs.xTable1.getColumns(),n=(0,B.Z)(r);try{for(n.s();!(t=n.n()).done;){var o=t.value;"序号"!==o.title&&i.push({header:o.title,key:o.property,width:20})}}catch(l){n.e(l)}finally{n.f()}this.Base.submit(null,{url:"refusaldata/exportExcel",data:a,autoValid:!1},{successCallback:function(t){var a={fileName:"医保拒付分析明细结果表",sheets:[{name:"worksheet1",column:{complex:!1,columns:i},rows:t.data.data,codeList:[{columnKey:"1"==e.paramData.dataType?"ape804":"ape804Re",customCollection:function(t,e){t.value||(t.value="--")}}]}]};e.Base.generateExcel(a)},failCallback:function(t){e.$message.error("医保拒付分析明细数据加载失败")}})},searchTable:function(){var t=this.searchForm.getFieldsValue();void 0!=t.aaa167&&""!=t.aaa167&&(t.aaa167=t.aaa167.split("（")[0]),this.condition=t,this.paramData.dataType&&(this.condition.dataType=this.paramData.dataType),this.paramData.issueNum&&(this.condition.issueNum=this.paramData.issueNum),this.paramData.hiFeesetlType&&(this.condition.hiFeesetlType=this.paramData.hiFeesetlType),this.paramData.medType&&(this.condition.medType=this.paramData.medType),this.$refs.gridPager.loadData((function(t){}))},queryDeptList:function(){var t=this;this.Base.submit(null,{url:"refusaldata/queryDeptList",autoValid:!1},{successCallback:function(e){t.deptList=e.data.resultData},failCallback:function(e){t.$message.error("科室列表数据加载失败")}})}}},T=D,z=(0,m.Z)(T,k,S,!1,null,"7df70eb9",null),R=z.exports,Y={name:"lineNum",components:{detailInfo:R,TaTitle:A.Z},props:{url:String,paramData:Object},data:function(){return{option:{title:{text:"拒付科室构成",show:!1},grid:{show:!1,top:"15%",right:"6%",bottom:"19%",left:"6%"},tooltip:{trigger:"axis",axisPointer:{type:"cross",crossStyle:{color:"#999"}}},color:["#3ea7f6","#FF7272","#49CB8B","#2F71F6","#F6AE73"],toolbox:{feature:{magicType:{show:!0,type:["line","bar"]},restore:{show:!0},saveAsImage:{show:!0}},right:"120px"},legend:{data:[]},xAxis:[{type:"category",data:[],axisPointer:{type:"shadow"},axisLabel:{interval:0,formatter:function(t){var e="",a=t.length,i=6,r=Math.ceil(a/i);if(a>i)for(var n=0;n<r;n++){var o="",l=n*i,s=l+i;o=n===r-1?t.substring(l,a):t.substring(l,s)+"\n",e+=o}else e=t;return e}}}],yAxis:[{type:"value",name:"单位：千元",splitLine:{show:!0},axisTick:{show:!1},min:0,axisLabel:{formatter:"{value} 元"}},{type:"value",name:"比率",min:0,max:100,splitLine:{show:!1},axisTick:{show:!1},axisLabel:{formatter:"{value} %"}}]},checked:"元",bfparams:{},visible:!1}},mounted:function(){},methods:{setContainer:function(){return document.getElementById("content")},handleMore:function(){this.visible=!0},fnRemindNum:function(t){var e=this;this.bfparams=t;var a=this;this.myChart=p.init(document.getElementById("linemain")),this.myChart.setOption(this.option);var i={url:this.url,data:t,autoValid:!0},r={successCallback:function(t){var i=[],r=[],n=[],o=t.data.analysis.labels;i=t.data.analysis.amountValues,r=t.data.analysis.deptNamesSortByAmount;for(var l=0;l<i.length;l++)0===l?function(){var t=i[l].data.reduce((function(t,e){return t+e}),0);n.push({name:o[l],type:"line",barWidth:"10%",yAxisIndex:1,data:i[l].data.map((function(e){return Math.round(100*(e/t).toFixed(2))}))})}():n.push({name:o[l],type:"bar",barWidth:"10%",data:i[l].data});var s={legend:{data:o},tooltip:{formatter:function(t){for(var e=t[0].name,i=0,r=t.length;i<r;i++)e+="<br/>"+t[i].marker+" "+t[i].seriesName+": "+t[i].value+(0===i?"%":a.checked);return e}},xAxis:[{data:r}],yAxis:[{axisLabel:{formatter:"{value} "+a.checked}},{axisLabel:{formatter:"{value} %"}}],series:n};0!=i.length&&0!=i[0].data.length||e.myChart.clear(),e.myChart.setOption(s)},failCallback:function(t){e.$message.error("查询失败")}};this.Base.submit(null,i,r)}}},I=Y,Q=(0,m.Z)(I,x,w,!1,null,"5e57628e",null),N=Q.exports,U=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"titleLine"},[a("ta-title",{attrs:{title:t.title}}),a("div",{staticClass:"buttonClass"},[a("a",{staticClass:"more",on:{click:function(e){return t.handleMore()}}},[t._v("查看明细>>")])])],1),t._m(0),a("div",{attrs:{id:"rulemain"}}),a("div",{attrs:{id:"content"}},[a("ta-modal",{attrs:{width:"90%",height:"700px",footer:null,destroyOnClose:!0,getContainer:t.setContainer,title:"查看明细"},model:{value:t.visible,callback:function(e){t.visible=e},expression:"visible"}},[a("detail-info",{attrs:{paramData:t.paramData}})],1)],1)])},P=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{width:"458px",height:"32px",background:"#F0F0F0","margin-top":"20px","vertical-align":"middle",display:"table-cell"}},[a("span",{staticStyle:{"font-family":"PingFangSC-Regular","font-size":"15px",color:"#606266","margin-left":"0px"}},[t._v("排名")]),a("span",{staticStyle:{"font-family":"PingFangSC-Regular","font-size":"15px",color:"#606266","margin-left":"8px"}},[t._v("规则名称")]),a("span",{staticStyle:{"font-family":"PingFangSC-Regular","font-size":"15px",color:"#606266",float:"right","margin-right":"10px"}},[t._v("占比")]),a("span",{staticStyle:{"font-family":"PingFangSC-Regular","font-size":"15px",color:"#606266",float:"right","margin-right":"50px"}},[t._v("拒付金额")])])}],M="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABYAAAAYCAYAAAD+vg1LAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAFqADAAQAAAABAAAAGAAAAACjEY07AAAD1klEQVRIDZ1VzWtcVRQ/976PzAcxTYdqkzbUEJU240Loqm4M6FoQGV24t+BKunLXiCCo9A9Q3LrJQBcFN6JQN7YI4qaOxgRjEabt5GOSSebjvfvevZ7ffe++TKYWioe8OTfnnvO75/zOefcJYjHGiGbzHdnoviE3o3vSn64J2J9WksNd88LUy7o5+71uNNa0EMIIgP7y1VV/sTpfNunBtD/llyT5Hqn06cADz2hK0iRKRsKbOdzqt4eX3/8yEWtrDe/1uF6tzr94JaxduiFkcJEP8/hUVGKT5iWviaAhsGKZ7WItUpNE63F3/Vq/vXHnh/C3vi0fmYYzS19w8nWdJh7plP8SgsZjtC60Xae5jTXxY2OEtxzMLH0GLFDq0wJz2iuVDHmXLBAyyVPKE34sw8mcUZ1mZym8OqjcPGDMvx/VRK0cMVHaZ8wnijrcp3ivQ5ULLz3m4yjhunz0B83PwLhRBmWNseayGra3aPfOdzS4v0Hlc4u0sLCUleSIt8c4xlnnTZfueKPBKbjUzGm2hi6dXaAzK2+SSZQFRALgudC5v7UhLpeifLvhSOVs7ETkYyCDMHPnfYOmTorLPvfH9jFwwgH5OE3GZTSx1QIzZWOVw5fHjUk0VrvYY2AeqwJ3ItBSZCO4TRg/C5Z1AWbXPKdhOwZGiXkpYMRVByc706xBD9ZuvKCfJMfAaJzzY2DjsgZgNLLxRkW2uexpXaEhztWFw1YAo0QMOcS9ztBHm/fo8I9frX2085A6t2/RqVdeJX/6lPXDBuJcFdaRfwrg8Tl2XEFXFy/axwVYDQrAtaOCge06Tww+BXDG3YnwsX8migUAQHMgJOA8XFABbDNw1v+hXZUutAAuZhU77nhoyERKLtkxJooCXEgBfCLjiVIZYF+Yo6+1NjF501e5VTV3nj04PxvrAvj553ZNr1vS+j8uIQZMeNZukrd9Q6lBuxwGJlbbN4Wc+5DE1LtcSAAwzLebJCONBqZsHdWYBRmZVO2MX0JCxz9L1Xk79NrX4qnB76fDSmeLgm01iv5U8cZHpLbfoiS6q3EV8DuQ6XQHWMD0GvUVcW5uIERaifkLcpk97nty+Aml/3yqU2/90d5+7/zWzIg+uJ6eqa/oyq2fVGW+NBpGPNRh/1vPyL+0kRf44xkIij6Xpb27m63SSKyursr3Tu8Gz571q0kcVFCu6g2GvWdoeJ6uxNRo2K8uSnbCpQtqNiX1W0EvPaiogMrCC4QfqkHnYdL/Zq+mLNcAf41IrszNZdzPzuqPWy3DdvfWOswTGnHXl5cFdbv2Xr/94IH5kYlB3L+J7EMVi5Pb7wAAAABJRU5ErkJggg==",V="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABYAAAAYCAYAAAD+vg1LAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAFqADAAQAAAABAAAAGAAAAACjEY07AAAEQUlEQVRIDY2VT2hcRRzHfzPvvX37J0mTtAmxRCI2hDZFxURpKj3EguTQg142PXjzoHiTngoKBkShSsFLL+pBDwFJsCBSUPEiFG3ExVqFWhtr28Sw3aS72f/73syb8fd7f3bfbnLowNv5vZnf7zO/+f5m3jLAprVmi6urfOjJEt844PDmZpHR+KO21Piwfrxsq9KdIbWSzSrGmGYEfS73iTl6wEk5ddkPhkwy0zCUKx8JzhOm1tLzwDNbdsasFsp289fZ1yXLrqwYtWfzmYWBYydPDU1e7DPto1orA4C4Okw6svdfCzP0atK5dbW0fu67ys2f+34bq/vbp0xPDD7xUYLx464nDKE9EEqC35OtI5v6vY+rhJHgfHpucOICsUhSkzSFlkxaHI4JkEGGCjtKjnq/UeZR1uFQVxfMmZwfJyk3Mg43qVDGWL8hlTJjpI4KCGhIF3IP7sHR4TEYSfV3IYOXcGGtTaoPMRGGOCwUbb2LFkTAt3f/BINz2HWa8PbVr+DMkafh3MxLuAHMUu+tQVR0HsajdigE6kkPLUJ9VTRh+a9rYBsmvHLkGZgZm4Cv16/DVmO37RP4B7FCiQgHMbAHLgLpERDYwBmY3IA/djb9sWKzRkO+2gTBovlP4C98n4jsS0EvwsPVqAbUoh2i+fHprP/+zfrvcLtUgNeeegHSCdPPuOMfFA90BADogPEYReTQrR33d2kblm+uwfm5BZh9bAIetmowYKewOLEMKKFY6wbv41cTLlz55wZcePFlGLTT8NPmbTwlAk5PTCEmyLA3EeJ3wKgt3iB/Tbzmvk39pdyPqCuDz2+s4clowHppB96cOeVflLhkfmDspw2WKEV0ehhmQtB8rQq5/P2Ye2AmDB5ovGemM9AGu6RxjxQH0yn49MzZjndo0c7oSLZ3iIF+MjFAG+w7hoHEj3SjocgOhAruhT/Wk0gY7nfd4DCSJOm6WPEIsntX6p3H9w44JkWUTbiOv8GI1ebS4iFwv0TaYCpe1OKngsY8xnaL0v3MU547aiXfwFNy0PcNpehNhOZM+lupi5aiq9zVMAhLIutKXv5Xli9uV8UWT1o61Wxcnu5LvzVgWGfxs2pRDPGjHXkWUxlkmn1uET8/ScdR7o7B+KHI0dX6l7xsvb8hRK5Pi8qI2XRANmHLSFVyD73zh/vNLw8biXdsbszR6aBd4o3eSWjlENMYzc4zK5NmKZO5qMuso9S9IrjvXd+tfVBJwq2kNivPT1Vbl0be9bIj82rj0A+iVnRa+UYi/x/IKzZXdwwGE5ixVdLuh2UXrlUKTostLS3xtVeHLfyOZhwp0rRdS0KzUobm+MlxdwWCf13aSdQwO7YIq3z7bt2yG6W0EJBSTDDbtBr451o/sVwUHMGKDKdQqUyJwQeT1UxhYHq8vPB9xVlli1g3FpYowtJRZJrm5r+455LvpMwUKJYYxCLm/3G4aBfl+9PrAAAAAElFTkSuQmCC",j="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABYAAAAYCAYAAAD+vg1LAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAFqADAAQAAAABAAAAGAAAAACjEY07AAAEL0lEQVRIDY1VzW8bRRR/M7vrj3VDcAwNSZMQqopCAsohVKJCgkhU4pATQoZDxZVKnFBP3BqExAGpf0AQHEEiliqBhAoIVLhAofSAVIxalVICamI7tvNh73o/Zob3Zne9WyOhjDSet2/e+83vfcyYAQ6lFHu1VuNnulV+w7vNK2MmI/1hR/sgVE/lT8hvyjW5Ua1KxphiBHrug+vmdOmxYiDUmK/Mgsm4IULvUOCGmVehkiLHwoFlsIN7/T/d9TeWQ1bd2DAW/RdLx+fyp2cn7YuWxZ5QUhp4KkWiSScyrXqQmsRoG4Az5KFubrac83c2vR9/y33bZ+vrytostI+unCpfzhnwtHZMnA7FOT4AbYMAfr3yS3d1blBpmt4scL9pFgyAJ4WMKYwy0qf9z09MhHO2SKmkOpmdxl1m8oohpDSj+NIoE8L9gYDNLQ9mJ3NQspHCMA+xBaWM0qSkSfWh4iMYABVKiCJtxLRiCghw7cYBuJ6AfI5D7csWLJ0swerzE4hDNYjtI1ANrovOMe0xEoIrkGhHU4hIDlF35dou9B0BzyyMwfx0Aa7Xe7C94w9ttH3sSxjJ0IzpgwzSMqeMn1sahyMlQ+/v90PIWQzGCqYmQh56pOaJJstYamM6VYhYxnV5oQQK108uNwGLA6+vToKB94eiQnU0RbomyGkqEiNcZWxIK83jMza89OyDMPAkfPpVC3qYGg1K4DRDJBKv/wXWTCNDyi0ZdvcC+Oizbfh72wM7b8D8I3nouQJu3XWimqCNpCnZcE2AMzlOmpe2IrmHbUbp8wOBzgp29kOwMA1zU3n9nZQK+yP2SDQAQ2CBIQ/bLW6fhx7IwdLjR8AZSPj6p12wsIXPnCpDEQWKKrnidPWTa0+0aGSA09PS7lBwchb7G8eJY0V9B0im8KOYIp9EziKkwJoBuSWJSOFHuykCSvfjUzKKLGPMIdMZxf3R0EaQyY44D+1jOYojIjdkTG1135GaRmQ0epAugT48ux8RijRZxpi3ZIwCAeO7bqg+xMbybZOfQ76VGDJxwTX1J6U5MTmv+o2WpL69f+hgw1CoS3tSXnSc8J6VU2pPmZfKef5W3mCvoYUV+aRZV1xJwuSdXl1xw/QQYCe5adEtYj8fePDKjh+cdwb+7/b4RNMqNlqeCG61ff/tXSFf9gN5Nbp19EJKCAXsEBZhGiuLVWaVjjF8Bnys0bJU7C9XsHcbrnoPn56bu/ud/fHGzODCmyBWFh+WP3xuB8a0Pei47ja+pl+Yit9Bv0dBMcuX6n3w+VX44/aAra2t8fbEWatiHy05KrQpXCdwXdgH9/TMjF+tgv7XzaYJa8BqNeD1Plh74h/btorFwGfMZqbTdpr9SufjgBoJCBzgBT41taK/y2WQ9fo7CvX6Mc2CZmXyW1i4wLrd6JXc2voOk/29JL9/AaTkhFJAXDQ1AAAAAElFTkSuQmCC",O="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABYAAAAYCAYAAAD+vg1LAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAFqADAAQAAAABAAAAGAAAAACjEY07AAAD60lEQVRIDY1VzW8bRRR/M7trx3FSN3XsJK5LRVtUaBVRgYhUiQOCG18SQi5C3EuPVQ+IY/4Ackaq4MCBSyxVSBx6QCBFCIGiNDQghQ8VkxDUxJuUfPhjveudGd6b9Wzs2IE+y57dN29+7/d+b8bDAE0pxcrlMj93rsRd9wGvpmxG/se1CS9U+fwFWamUZalUkowxxQj09u179plnn0yJRnPUgsSQUtwKefBY4LZMKMakEBC0rPRwbWPlT+/69edDNj8/b4088Up6ImNfnRpLzTk2f1pKaWFWqkSTNs80DjL0i3Yoftvcbd2q7off1//6usGWlpSzuf93/srF3F3bhuloIQEQaAfoyGsfOBHApGGoVu7/uv3aVKbo2qSplTwxxLl6RsiIYf/CTo5jpnV+nOOcXSYpCdOmRuUFaiqEbQCp4g4J7TLPm1v/6PfJiVMmtHeUYFN/XMTUYNQoKRMaTEceKZ0hpVrDg/kvvoXpy2chN57Bygf3wDQ9ZhkKqYMJWKEkemFHGspz96sl8Fo+SPRJKkFKrWsUH61V5OtYDIw7wfj6xpWfK5DNjsL6RlXvFBEKjCHBKWVn6HolVwwscCcOske7NWg0W1CYPAXLGEBbUMjeWJJK4YdGY9w8CJSCWHd/w3YIy/cfwHNXzqM8UUUauBNLa+iLjY9Hg3fIGFmYjCb7xsMd2N2ro773UN9Ar/ljbUtX8NKL03G8JnqcFBIz0yaPLIoqTI7Bm6/OoItBZX0Lvln4Cc4UxuHqCxd1E7GMKJyWxcCRCF2Mj2/e+oYLP65UNEhlvQpjJ9Pw1PlCBNr3OwiYsmoz6SNHEVkWC1mciSqiwkhbU2BvdIRwyBgbYDQ+rMtk6qk13gHUi+OsC7iLAcYTGzoHZL2wAxwUZ4JoAVoM/F8H5P8q6J8/AhwXZv51YspsL1TJTxjIwILgfTzP2R6GurzuXYXAdK0IOy0FF4MUCyU4d4Cn5xqCPUzIIbwtgjsJfnAT/33fQaYOld2tBA8dORE2Fa/XAzyLtt+WcsecOjreqPiikJm322L8ls+HfhH50673qLotas7vvkh/6PORtzDrD3TqaJ0+fQp2CIsw7VzOlX6yWBcNe06w4AO8AqvMGv5Y+Ce+FInkbrK1473+8oW2JnbpEhEMy6vgO+7ad202/q6jam8w2byBvSuAZB85aas+YruSzc7O8pmZ9xx+Mp9u+LVhKtdrex4cgAdQDEolDMdblxCN0QVcLgPP5dacbd8eTjmQCjhj6eRoU+65jcXFz9scgSU9JP3lg7Ojp6tTmawLB8X91dVP/WvXmDgKSuDko7mFhc8CiqU7jtYSBmER5r/6iTAk0z0A0AAAAABJRU5ErkJggg==",K="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABYAAAAYCAYAAAD+vg1LAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAFqADAAQAAAABAAAAGAAAAACjEY07AAAD9klEQVRIDY1VzW9bRRCfXb/nzzhuSW3jxClNa9HUFVCpolKvcECCIoSQWyHuhSPqoeKYP4CckSo4cOASS730wIlDD0AbAkolFEJatRW0uH5OSeLv97G7nVn7PX9G6kj2vJ03+5uZ38y+ZYCilGLlcpmfPFnilvWAV2MGI/vLSrbjqUymIB8+LMtSqSQZY4oR6I0bvxuLby3FRKudDEE4qhQPedx5KXBDhhVjUghwuqFEvPHvvUedq1fPe2xtbS00c/zdRDZlXMwdja2aBl+WUoYwKlWik/afSU8TtAvXE39X9rrXqgfer81/fmqxjQ1lVg6eZM6dTv9oGPBGbyMBEGgfaGw5AU4JYFDPU/c2t2sf5FJ5yyBOQ5HZKOfqjJC9DCc39mMc8lrHx3ecs7NEJWEa1KiMQE6FMHxAqrifBDRbXUoGEvGottUbbZhNxn3XUS3BoP5YiKnBqFFShvVG7TlU+sbmDtz5bRvBe8bC0jx89P5FvZ7WA7/pQZaekP3NmC1SohuGutt14cRiFhlXsJCbg7PLx0FSOVJqXikRhc89f7T1JQDGSfBtI5pzgEvvvQ27e3VIv5IiHkF4An2IcKqir4aWZAqABU7iNKntHsDmn4+g3enCrQd34fy5ArxZXBpxZRiAKiLtyxAwleObB7p4ehFOLeW04WnlOfxydxtOnchBLDrUk4F78ISF9kRIAQJ5ph9OiNa268L2/adQqf6vbTQZHtJQeban10Sf/qmB9vGCjCUCDlLuEdbCUSMqPKE0gFXbh2QyBvOvHsU1+iCgFqo04LiXawAspjSPyi2eWYRnmPHO/SeQzR6B5cKC5vOwnuAx0bFGgSmqFj+8guLrebSMpKSz9w8QuQ+89Wb9NwBGXgddHXcdBfYngPRhMgQ8mAo/G9Iko7BTDON5oEsAfNgBIeDJYsdDTSKPAAeFTaTM9j0V+ZaBdELgfI7neS4og+Lqr9bwVGHGdK0IIyEFF9MY8ySYN4EnVluC/ReWUbwtnJthXv8Sv75XsBJzvB7umTLrtRVvNh08i4btSrnrDzyNEjK+LmTqE1ccu2bz6F8is2B1nldromHu2CLxlc1nPsaod+gw0T59qBTsEhZhGum0Je1IvilaxqpgznW8AqssFP9G2LO3RDiyF+nudi69U3A10cUiJeiVt8A2rcc/u+zYp6ZqfMhk+wtkfR4k+9pMhJozhiXZysoKv3DhM5MfySRadiNO5XbcTgfq0AHIO6USuuOtS4i+9G514On0Y7NmG/GYCTGHM5aIJNty32qtr//gcgSW9BCx/6i/llyo5lJzFtTzB1tb39mXLzMxDkrgZKN3t29/75Av3XG0lzAIizBfAPzQMRDY2uVSAAAAAElFTkSuQmCC",W="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABYAAAAYCAYAAAD+vg1LAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAFqADAAQAAAABAAAAGAAAAACjEY07AAAEAUlEQVRIDY1VzW8bRRR/Mztre+2kATV2SJMSUsJHC1UrFQUqDkj0hPiQEHIB9dBb4Yh6QBzzB5AzUkUPSHCJpV6QQAhx6AEBIQkUoTS0VYkIUHkdlMRf6/2YWd6b9awTx4E+a/xmZ9/83u/93uwuA7Q4jlmlUuHHjpW5697hVUcwWr9fG/OiuFSaUXfvVlS5XFaMsZgR6JUry+LoqWlHttrDFmRyccytiAf3BS5UJmZMSQlBxyrkGxs3fvcuXToTsYWFBWvo4XOFsRFxdvxBZ94W/EmllIVZqRJN2szJDzJcl2Ekf7u31blc3Ym+a/7xTYstLcX2vZ0/S6efKH4pBJxMNhIAgXaB+i73gRMBTBpF8Y2f12ovj49MuoI0tbKHcpzHx6VKGO7f2M1xwG2dH+9xzp4iKQlTUKNKEjWVUhhAqrhLQi/RfKfeAre2DUcnipDLZUzoXq9AUH9cxOR0hxqllASJLaCBSVIfhBF88fWP8MPyGoRRBFc//QpabQ+wD2m8mZM3TdfAGlwqUEiNhuzOyS//dBt+vbkOZ2ePg+NkQAgOQZgkNvFEhObkjaXlU7ZBtvLLHcg7WeCoT8YWcPGtcxjGNNv/am4KTBL0W6cTQKPpIUsLlpD5xl81lEPCay89C7msnYYzTBTjj7yxVApdPrIm5ma0PV/H0fWpk4/AY48e0Q1cu7XR1Zh0Rl11TxJvgHuMsXkmo8nu5DN0PMHBU0APC+lLtrXd0GAmXhOlo9gjDCljhZklMqNBDMlT3PTUQ+ChJLTWaicVTE+N4TU1uXeSzJwSk+1iPLh5zz3zuAZYXLkNTdT7zOkZKI6OdJuXgOz9T7juBU6fLFNXUv4Lzz+tpTDvCmKPymiZCLQX3UvRA8ayUs32hZIoqHyciGh6QP4g2wWMmnbFN2zIkyWwu3rTvzCAcgpM5R1s/Tv/H3kPcFrYPspsO4qzHzNQgQXBOwDqcFqGLgkTxUjMlIxrgj4rUhSU5HKQYpEC+xrwwnxLsr8zKodfi+Bahtffw7fvm4imH7/d9fDIVmNRO+bNZoDPovBDpTZJDhp0JlHxRalG3gjl6GWf527K0oTr/VOtyYZ9y5eFD3w+9Dpm/V6/gHCPfvpi2CQswhTFoqv87GRTtsS8ZMH72Pkqs/IfSf/Q5zKT3cp2Nr1XXpwJsboYTpygwqPKKvi2u/5tyEbftuPGq0y130UxjoBiH9oFqzkkXMXm5ub47OwFmz9QKrT8Rp7K9ULPgzp4AJNBuYzh+NUlRGPJVx14sbhu13yRd2xwAnz9FbLDbbXtthYXPws5AiuaZP2V+tTwRHV85LAL9cmd1dWr/vnzTPaDEjit0b3r1z8JKJa+cbSXMAiLMP8FRuF8ChSWDQYAAAAASUVORK5CYII=",G="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABYAAAAYCAYAAAD+vg1LAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAFqADAAQAAAABAAAAGAAAAACjEY07AAAD4UlEQVRIDY1VzW8bRRR/Mzvrr43jmtRO3bqFVkFAEaKiUAmBhEQREioIFeQC4l44oh4Qx/wB5IxUwYEDl1jqAQ7l0kMPqChKgR4IFEWhtKiRN6mc+CObXe/M8t6sZ73OB8pY9uy++c3v/d7HeBjgiKKINZtNfupUg7vuMm/lBSP7Qce0F0bV6oxaWWmqRqOhGGMRI9KrV2+L48+fzMv+VtGCTC6KuBXy4EDkQmUixpSUEGxbTqH74M7f3uXLZ0M2Pz9vTZw470yXxMu1cn7OFvxppZSFXikSLdo807zXQLschPLuanv7SmszvNW7f6PPFhcje3Xz3+qZpyrXhYDn4o1EQKRDoh2vu8hJADoNw+jOb3+uXaiV6q6gnFrZyRzn0TNSxQp3bxz62GdZ+8c1ztmzlEriFFSoqsScSikMIUU8FAGrrTaEgxAYZygKv/jJZAVMlScNfDQrEFQfFzk1GRVKqYwm06hU6N9fvwXtjd5oMz49ceIINN59dc8amKInKkOptCJiiDAlumA4D1DtzMkaHK9XQFgWLK88hBdfeBIk9gHlNcbHeyOl9Dv9JMTYCYkx/VCtHIILb76kSdobXfjnfguOTj8WE+vkIjoVodmbEGsFxpqa33rjLEh0qlD9jzduw/nXzuA7qk0NynuEH5rNSBFTOMY8PlM/L/66DL2eB+WSg2pj7LDNx8HDtxExqjAejXeazfj97gMoFh3A7tE4g9Hr/5cKhSpGkonQoAHWH3Wg3/cwt2WdEqxujKWZhoHqiLk2pRTvXTxCcYtDJmND7Uh5WDS9d5+fvYiTyMcVTxRy8OF7ryARG8uvqck4OvY3UjzMXWzeCR2LNemAdA12yk8Rj7rCHGdT9XFaLXzsP0rX2ICGHhLi/Q7IQSIYeUlyOX7yEvMuyWwjjLJfM1CBBcEnAGoq6QTyTMk2nWIU07UihaMkl6muHa4ChArsa8Cdub5kDzMqh7dFcC3DO5/hv+8HyGYTMl0RHtpqOtyKeK8X4FkU/kCpdUoHfel4Y8YXpCq9P5CHr/g894esHnO9R6012bX/8qXzhc8nLqLXn+nA0B6aZQTrxEWcolJxlZ+t92RfzEkWfI5XYItZha+kP/mDzGTb2e117+3XZwZa2OnTJDBsLoFvu/d+GrDDH9lR9x2mtj7FZBwFxb60Has3IVzFZmdn+blzH9v8UNXp+90ChesNPA864AHUg0YD4XjrEqMZ8a0OvFK5Z6/5opC3IR9wxpxscUttuP2Fhe8GHIkVPWT9XzqPF4+1aqUpFzr1zaWlb/xLl5jcSUrkZKO1mze/DQhLdxztJQ7iIs7/ALvDBLUzna1BAAAAAElFTkSuQmCC",X="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABYAAAAYCAYAAAD+vg1LAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAFqADAAQAAAABAAAAGAAAAACjEY07AAAEB0lEQVRIDY2V22tcRRjAv5kzZ69JN2VvpNkmtgbb1NZaxKC+qeCDN5CyFfG9+ih9EB/zB5hnoehDERGz0BcR8YZWtGpItVWIWmIbEpu4J5HNZTebs+fMjN83u+fsdk8i/ZbDNzuX33ebCwMUrTWrVCr86NEyd5wFXk0KRv33KsWmrwuFcXXrVkWVy2XFGNOMoBcvXhOHTx9JysbOoAWxhNbc8nnrnuBCxTRjSkpo7Vrp1PbyjdvN8+cf8dnMzIw1MPp0upgRjw8fTE7bgh9XSllolSIxTgdt0nsJ9kvPl3+u1nYvVDf9H+pLXzXY3Jy2Vzf/Ljx8LP+pEHCqvZAABO2A+v5G4OQAGvV9feP6H2vPDWdKjqCcWvEDCc71hFRtD6MLOzb2GTb2cYxz9iClkpiCClWQmFMpRQCkiDtOmC6lAJbuVCGdTEAumwmmRbUCQfVxkMlplAqllASJJaAPjYR6eWUd3v/oSxCCw/KdNbj04RcYsg9Yh3B+0CYdFN2ADVwqUOgmfbLTJn31p3kKEfLZITg5MQa1jTr8tbhqoMF8coTapAMJwydre8kGgmqbdfj26m9QzA+h5xYU0AhF9n/FDcHtiVH0mdP3wzff/QrXri+YwWeePAOxuAWyxxGG1dP4Ix1ID1jRjonIUCYFxcJB0Lhj/nFq8PnXv2B+NTwwfsgUOLKg0xHmWJriUUGwAKZ4ClzPg08+m4NTE6Pw4rOT8Nijx8yy20tVM4fSZz7d1YGhEKwQSOHRR5NJe54PGqOwY8L0jR8ZNuvGSjnjdbCLenUA7qYCQf1icQ5PTB6HmwsrUKvVod7YhYdO3gejhwud4vWvoP9tX+8GhyeLGpRwDaMjWfxy5t4I7gptQjenmEjh4Q+XY18XjHntVrULNis7RvAiNH+DHUB6P+kBd3dFcJxJk7R9D3dttKPfD1wTgvc7IIYcCbbfVJR8FzgMLOIy2/B1/F0GqmVB6zUAlQ3DMCGhIcx770EQ9KxIkVaSy70y5iuwLwNPTzckW4mpBL4WrcsxvvUG3r4vI80mbq+/3LdV0d/RvF5v4VkUrqfUerDhaV9ixmelypz1ZO6CyxO/y8KI0/y3uia37ZuuTL/l8oGX0OqP5gLCrWoOlYZ1YhFT5POOcuOlumyIaclab2Llq8xKvSPdAx/LWLwW311vPv/UuGccO3GCHPQr8+DazuL3Hsu9YuvtF5jaeR2TcQgUe9tOW/UB4Sg2NTXFJydftflQId1wt1MUbtNrNmELmgClVrmM0/HVJWIg7VcdeD6/aK+5IpW0IdnijKXjgztqw2nMzn7gcQQrasTdn7fGBkeqw5msA1ulzfn599xz55jshxKc+mjsypVLLZpLbxytJQaxiPkfjjlyoU4/QOEAAAAASUVORK5CYII=",q="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABYAAAAYCAYAAAD+vg1LAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAFqADAAQAAAABAAAAGAAAAACjEY07AAAD/ElEQVRIDY1VzW8bRRR/M7vrr3ViqsQ2SVNCQxG0Fd9SJMSNjwNfB4RchLgXjqgHxDF/ALmCVAESBy6x6CUHJFQEEaKUtImaA6FpQ1pomtQbp038EWc/Zpb3xp61YztSn7V+s/Pe/N7Hb2aHAUoYhqxYLPKJiQJ3nFVeSpqM5h9W8o0gzOVOyLW1oiwUCpIxFjICPX9+wTz23PGkqO8NGBBLhCE3Au49FLgpYyFjUgjw9g07Vb2zdKtx9uxLAZuZmTHSj71m5zPmyyNHktOWyZ+WUhoYlSpRSesx6X6C88IPxMrmg/1zpd3gj9p/P9fZ1auhtbm7nnv+qeyPpgnPNBcSAIG2gLpee8ApAQwaBOHStetbb49kxhyTemrEBxOchyeFbGbYu7AV4xCzio82ztlpaiVhmkRUTmBPhTA1IFXcSkJNCRHCv3dKMJBOwvBQRrv1agkm8eMgJicrESWlAIEU0INBIr3yzzp8+e0seL4PP/2yABd/XVQ25CHy12PSmnQFrMCFBIlp0iNaY9KXr1wHJAaOjz8KTzw+CgtLq7C+UW6Ct/wpEVpHWktUPkXrFs8LYPPefbBTCSB7Mmkpl9W1DchnqSWHkxsBUwu6xTAY5HNHYKu8g4wL2L5fVS47lTqIjkQYBgjxR1pLB7CkHdMjr0yehD8XbsDFuWuRzU7GVAVY/aHSBkbydEQdnXQ6HYc333iRthKs3roHKzfXcWcMqn5qf5UoBelILCJPIlFUHj2K5Zb+YfYS/HbpLzV3d2MbBgdtGD+Ww3ciub2T9FiX0JFxL3nk9OTEKGYn4criTQj8AN56/QXVz36cNEGbuR4Ejnqm6wrh2dPj6E816jmI+qs50RbSWtrAWFbUswhEux4E7uRAA3XrDuD2riC29bGmBQdh+0z0STkC7ndA2ll0r+wO1W0HOABMZiU9KbOdIIx/zUB6BngfY5eHojJogSoPyddNxymTrhVh2lJwoTqrgNt/gQTrAnB7ui7YRkwm8LbwLsR45VP8+n6AhKoz3pkvDyyZD/ZCXqt5eBZN15eyTO1QexiJxI7PC5l53xfD51ye+FvkjjqN7dKWqFo3XGF/7vL0exj1svoA4Tr1RQyhTFiEaWazjnTjYzVRN6cF8z7DK7DEjNRXwh2cFbH4g/h+ufHOqyd8LCKEU6eolqC4DK7l3P7dZ8MfWmH1XSb3PsGuj4JkX1i2UUubjmRTU1N8cvIjiz+Ss+tuNUXlNvxGAyrQABjzCgV0x1u33R26BOhWB57N3ra2XDOFH72kxxmz4wN7csepz89/73MEljSIu4uV8YGjpZHMkAOVsd3l5W/cM2eY6AalADRHtrm57zzypTuO1hIGYRHm/3aQZdu44WkzAAAAAElFTkSuQmCC",H="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABYAAAAYCAYAAAD+vg1LAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAFqADAAQAAAABAAAAGAAAAACjEY07AAAETElEQVRIDY1V3WscVRQ/987Mfs0mG5vdjWm2rYaItWorCAVfRCw+qQ8iWym+Vx+lD+Jj/gDzLBRFLSialSL4IBSLrSiVJQ2t0GQbStI0tXEnm2S/N/Nx73jOnZ3dZdNAD8zOzLn3/M7v/M6ZvQzQfN9nhUKBT0/nuWXd4+W4zsj/pDbR8fxsdkaurhZkPp+XjDGfEejFizf1I6eejYtWe0SDSMz3ueZx54nAdRnxGZNCgLOnmYnGxu21zvnzr3psfn5eSx49Y06k9Ncmn4rPGTo/LqXUMCtVokiHz3R/nKFfuJ64u7m7d6Fc8240H1xtsYUF39isPcy+8nzmV12Hl4NAAiDQLtDQ6z5wIoBJPc+/fau09fZkKmfppKkWHY1x7r8gZMBwf2A3xwHLKj+ucc5eJCkJU6dGZQVqKoQeAlLFRGLzv22Ix6MwlkoSIeWjPdV6C6ytXTj8dBqSZiwMA5CgU38sxOTkpUZJKUBgC+gqLt6FSz/8Bpd+vAqr65uAmgMmVveFWyvw089/wEgyDt98fwVKKxvKT3FqX7fpCliBCwkSadJ16qVpmH5mUjGRKE+QUEK90YJrf/4Dz81MQfrQKBzJZeDKtUWwHUfFUfLQesCULWRMz4ahqT00GSGbe2uP8FlCMhFTvtFkAjodGx7+WwHhBYz3AYeg4d1HcDKVsJu0Wm0rHzYJBPo0LZiand0mviMwJg2t1zBiMjim4QzTncDJeFAEsg18JJMyRlX1QcnXB8aMrDu3Ps4waU1GzELtktgwMsd1lc+2XfU+lkoEyYMClK8HrMroUfaxIUGQ63oYREl8OIrNikQMqNaayrdTbeAompAeH1WaB7yCtvWaR8xCfW8US7BU2lCZ7yw/gOLNFVUqSgtvvXESKtsNuLO8Do7jwZnXT6q1IL4vB/v697XYIT9ybCLllNRXrOCIIdXV1bD3HNRKhZHsvEtrcHe5Fjm+w5z1nhTENtQ4ADwYmHpAez1s4kE2ANyfCurb4Cccpgj4DhQTOgYpdzP1gMORejyD4cjhVMPrA+NGwLSsbB9lVvX86JcMpKOB8xEqPN6TnQJUeSh6b6oQmI4VoZtScNEHDuDp18OP+zJwc64l2KOIjOFp4VyO8PonqPAH2AuDNg3y5Z4hJ7y2z5tNBzuh266UFWJNFzUSFS8KmXrfFekLNo8ti+yU1dkub4mGsWIL8zObJ9/DrH+H/3p0x15WCIsw9UzGknY01xQtfU4w51M8AstMS3wh7NFfRCS6G92rdN55c4a+Fh9OnCCCXmEJbMO6/5fL0ucMv/Euk+2PUfXDINnnhqk1k7ol2ezsLD99+kODj2XNlt1IULkdt9OBOnQAck4+j9vx1CXE0PD/A0914JnMfWPL1hNxA+IOZ8yMjrRl1WoVi9+5HIElPUTtxfqxkanyZGrcgnqutrT0lX32LBPDoAROPlq7fv1bh/bSGUexhEFYhPk/KJ2ijrucGeYAAAAASUVORK5CYII=",Z={name:"ruleNum",components:{detailInfo:R,TaTitle:A.Z},props:{url:String,title:String,paramData:Object},data:function(){return{option:{},checked:"元",dataCake:[],bfparams:{},visible:!1}},mounted:function(){},methods:{setContainer:function(){return document.getElementById("content")},handleMore:function(){this.visible=!0},formatNumbers:function(t,e){var a=t.toString().split(".")[0],i=parseInt(a.slice(0,1)+Array(a.length-1).fill("0").join(""));return e>=i?parseInt(parseInt(a.slice(0,1))+1+Array(a.length-1).fill("0").join("")):i},insertStr:function(t,e,a){return t.slice(0,e)+a+t.slice(e)},fnRemindNum:function(t){var e=this;this.bfparams=t;var a=this;this.myChart=p.init(document.getElementById("rulemain")),this.myChart.setOption(this.option);var i={url:this.url,data:t,autoValid:!0},r={successCallback:function(t){a.dataCake=t.data.analysis;var i=t.data.analysis.orderSortByAmount;if(0==i.length){var r=document.getElementById("rulemain");return r.innerHTML="-暂无相关数据-",r.style.cssText="text-align:center; color: #999; border: none;line-height: 300px",void r.removeAttribute("_echarts_instance_")}var n=Array(i.length).fill(i[0].value),o=["#FF7272","#49CB8B","#5780ED","#9DA5C5"],l={tooltip:{trigger:"axis",axisPointer:{type:"none"},formatter:function(t){var e="";return t[0].name.length>18&&(t[0].name=a.insertStr(t[0].name,18,"<br/>")),e=t[0].name,e}},grid:{show:!1,top:"2%",right:"13%",bottom:98-10*i.length+"%",left:"9%"},xAxis:{show:!1,max:i[0].value},yAxis:[{show:!0,data:i.map((function(t){return t.name})),inverse:!0,axisLine:{show:!1},splitLine:{show:!1},axisTick:{show:!1},axisLabel:{color:"black",formatter:function(t,e){return"{img".concat(e,"|}")},rich:{value:{fontsize:20},img0:{height:31,backgroundColor:{image:M}},img1:{height:31,backgroundColor:{image:V}},img2:{height:31,backgroundColor:{image:j}},img3:{height:28,backgroundColor:{image:O}},img4:{height:28,backgroundColor:{image:K}},img5:{height:28,backgroundColor:{image:W}},img6:{height:28,backgroundColor:{image:G}},img7:{height:28,backgroundColor:{image:X}},img8:{height:28,backgroundColor:{image:q}},img9:{height:28,backgroundColor:{image:H}}}}},{show:!0,inverse:!0,data:i.map((function(t){return(100*t.percentage).toFixed(2)+"%"})),axisLabel:{textStyle:{verticalAlign:"middle",textAlign:"left",fontSize:12,color:"#606266"}},axisLine:{show:!1},splitLine:{show:!1},axisTick:{show:!1}},{type:"category",inverse:!0,offset:-10,position:"left",axisLine:{show:!1},axisTick:{show:!1},axisLabel:{color:"#606266",align:"left",verticalAlign:"bottom",lineHeight:32,fontSize:15,formatter:function(t){return t=p.format.truncateText(t,280,"14px Microsoft Yahei","..."),t}},data:i.map((function(t){return t.name}))}],series:[{name:"条",type:"bar",yAxisIndex:0,data:i.map((function(t){return t.value})),barWidth:18,itemStyle:{normal:{barBorderRadius:[8,8,8,8],color:function(t){var e=o.length;return t.dataIndex<=2?o[t.dataIndex%e]:o[3]}}},label:{normal:{align:"center",show:!0,position:"inside",formatter:function(t){return"元"==a.checked?"{white|¥"+t.data+"}":"{white|"+t.data+"次}"},rich:{white:{color:"#FFFFFF",fontSize:15,align:"center",padding:[0,0,4,150]}}}}},{name:"框",type:"bar",yAxisIndex:1,barGap:"-100%",data:n,barWidth:18,itemStyle:{normal:{color:"none",borderColor:"#E4E6EF",borderWidth:1,barBorderRadius:[8,8,8,8]}}}]};e.myChart.setOption(l),e.myChart.getZr().off("click"),e.myChart.getZr().on("click",(function(t){var a=[t.offsetX,t.offsetY];if(e.myChart.containPixel("grid",a)){var r=e.myChart.convertFromPixel({seriesIndex:0},a),n=r[1],o={name:i[n].name,id:i[n].id};e.$emit("fnLinkQuery","rule",o)}}))},failCallback:function(t){}};this.Base.submit(null,i,r)}}},J=Z,_=(0,m.Z)(J,U,P,!1,null,"e550ce06",null),$=_.exports,tt=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"titleLine"},[a("ta-title",{attrs:{title:t.title}}),a("div",{staticClass:"buttonClass"},[a("a",{staticClass:"more",on:{click:function(e){return t.handleMore()}}},[t._v("查看明细>>")])])],1),t._m(0),a("div",{attrs:{id:"deptmain"}}),a("div",{attrs:{id:"content"}},[a("ta-modal",{attrs:{width:"90%",height:"700px",footer:null,destroyOnClose:!0,getContainer:t.setContainer,title:"查看明细"},model:{value:t.visible,callback:function(e){t.visible=e},expression:"visible"}},[a("detail-info",{attrs:{paramData:t.paramData}})],1)],1)])},et=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{width:"458px",height:"32px",background:"#F0F0F0","margin-top":"20px","vertical-align":"middle",display:"table-cell"}},[a("span",{staticStyle:{"font-family":"PingFangSC-Regular","font-size":"15px",color:"#606266","margin-left":"0px"}},[t._v("排名")]),a("span",{staticStyle:{"font-family":"PingFangSC-Regular","font-size":"15px",color:"#606266","margin-left":"8px"}},[t._v("科室名称")]),a("span",{staticStyle:{"font-family":"PingFangSC-Regular","font-size":"15px",color:"#606266",float:"right","margin-right":"10px"}},[t._v("占比")]),a("span",{staticStyle:{"font-family":"PingFangSC-Regular","font-size":"15px",color:"#606266",float:"right","margin-right":"50px"}},[t._v("拒付金额")])])}],at={name:"ruleNum",components:{detailInfo:R,TaTitle:A.Z},props:{url:String,title:String,paramData:Object},data:function(){return{option:{},checked:"元",dataCake:[],bfparams:{},visible:!1}},mounted:function(){},methods:{setContainer:function(){return document.getElementById("content")},insertStr:function(t,e,a){return t.slice(0,e)+a+t.slice(e)},handleMore:function(){this.visible=!0},formatNumbers:function(t,e){var a=t.toString().split(".")[0],i=parseInt(a.slice(0,1)+Array(a.length-1).fill("0").join(""));return e>=i?parseInt(parseInt(a.slice(0,1))+1+Array(a.length-1).fill("0").join("")):i},fnRemindNum:function(t){var e=this;this.bfparams=t;var a=this;this.myChart=p.init(document.getElementById("deptmain")),this.myChart.setOption(this.option);var i={url:this.url,data:t,autoValid:!0},r={successCallback:function(t){a.dataCake=t.data.analysis;var i=[];if(i=t.data.analysis.orderSortByAmount,0==i.length){var r=document.getElementById("deptmain");return r.innerHTML="-暂无相关数据-",r.style.cssText="text-align:center; color: #999; border: none;line-height: 300px",void r.removeAttribute("_echarts_instance_")}var n=Array(i.length).fill(i[0].value),o=["#FF7272","#49CB8B","#5780ED","#9DA5C5"],l={tooltip:{trigger:"axis",axisPointer:{type:"none"},formatter:function(t){var e="";return t[0].name.length>18&&(t[0].name=a.insertStr(t[0].name,18,"<br/>")),e=t[0].name,e}},grid:{show:!1,top:"2%",right:"13%",bottom:98-10*i.length+"%",left:"9%"},xAxis:{show:!1,max:i[0].value},yAxis:[{show:!0,data:i.map((function(t){return t.name})),inverse:!0,axisLine:{show:!1},splitLine:{show:!1},axisTick:{show:!1},axisLabel:{color:"black",formatter:function(t,e){return"{img".concat(e,"|}")},rich:{value:{fontsize:20},img0:{height:31,backgroundColor:{image:M}},img1:{height:31,backgroundColor:{image:V}},img2:{height:31,backgroundColor:{image:j}},img3:{height:28,backgroundColor:{image:O}},img4:{height:28,backgroundColor:{image:K}},img5:{height:28,backgroundColor:{image:W}},img6:{height:28,backgroundColor:{image:G}},img7:{height:28,backgroundColor:{image:X}},img8:{height:28,backgroundColor:{image:q}},img9:{height:28,backgroundColor:{image:H}}}}},{show:!0,inverse:!0,data:i.map((function(t){return(100*t.percentage).toFixed(2)+"%"})),axisLabel:{textStyle:{verticalAlign:"middle",textAlign:"left",fontSize:11,color:"#606266"}},axisLine:{show:!1},splitLine:{show:!1},axisTick:{show:!1}},{type:"category",inverse:!0,offset:-10,position:"left",axisLine:{show:!1},axisTick:{show:!1},axisLabel:{color:"#606266",align:"left",verticalAlign:"bottom",lineHeight:32,fontSize:15,formatter:function(t){return t=p.format.truncateText(t,280,"14px Microsoft Yahei","..."),t}},data:i.map((function(t){return t.name}))}],series:[{name:"条",type:"bar",yAxisIndex:0,data:i.map((function(t){return t.value})),barWidth:18,itemStyle:{normal:{barBorderRadius:[8,8,8,8],color:function(t){var e=o.length;return t.dataIndex<=2?o[t.dataIndex%e]:o[3]}}},label:{normal:{align:"center",show:!0,position:"inside",formatter:function(t){return"元"==a.checked?"{white|¥"+t.data+"}":"{white|"+t.data+"次}"},rich:{white:{color:"#FFFFFF",fontSize:15,align:"center",padding:[0,0,4,150]}}}}},{name:"框",type:"bar",yAxisIndex:1,barGap:"-100%",data:n,barWidth:18,itemStyle:{normal:{color:"none",borderColor:"#E4E6EF",borderWidth:1,barBorderRadius:[8,8,8,8]}}}]};e.myChart.setOption(l),e.myChart.getZr().off("click"),e.myChart.getZr().on("click",(function(t){var a=[t.offsetX,t.offsetY];if(e.myChart.containPixel("grid",a)){var r=e.myChart.convertFromPixel({seriesIndex:0},a),n=r[1],o={name:i[n].name,id:i[n].id};e.$emit("fnLinkQuery","dept",o)}}))},failCallback:function(t){}};this.Base.submit(null,i,r)}}},it=at,rt=(0,m.Z)(it,tt,et,!1,null,"0f64c91b",null),nt=rt.exports,ot=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"titleLine"},[a("ta-title",{attrs:{title:t.title}}),a("div",{staticClass:"buttonClass"},[a("a",{staticClass:"more",on:{click:function(e){return t.handleMore()}}},[t._v("查看明细>>")])])],1),t._m(0),a("div",{attrs:{id:"itemmain"}}),a("div",{attrs:{id:"content"}},[a("ta-modal",{attrs:{width:"90%",height:"700px",footer:null,destroyOnClose:!0,getContainer:t.setContainer,title:"查看明细"},model:{value:t.visible,callback:function(e){t.visible=e},expression:"visible"}},[a("detail-info",{attrs:{paramData:t.paramData}})],1)],1)])},lt=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{width:"458px",height:"32px",background:"#F0F0F0","margin-top":"20px","vertical-align":"middle",display:"table-cell"}},[a("span",{staticStyle:{"font-family":"PingFangSC-Regular","font-size":"15px",color:"#606266","margin-left":"0px"}},[t._v("排名")]),a("span",{staticStyle:{"font-family":"PingFangSC-Regular","font-size":"15px",color:"#606266","margin-left":"8px"}},[t._v("项目名称")]),a("span",{staticStyle:{"font-family":"PingFangSC-Regular","font-size":"15px",color:"#606266",float:"right","margin-right":"10px"}},[t._v("占比")]),a("span",{staticStyle:{"font-family":"PingFangSC-Regular","font-size":"15px",color:"#606266",float:"right","margin-right":"50px"}},[t._v("拒付金额")])])}],st={name:"ruleNum",components:{detailInfo:R,TaTitle:A.Z},props:{url:String,title:String,paramData:Object},data:function(){return{option:{},checked:"元",dataCake:[],bfparams:{},visible:!1}},mounted:function(){},methods:{setContainer:function(){return document.getElementById("content")},insertStr:function(t,e,a){return t.slice(0,e)+a+t.slice(e)},handleMore:function(){this.visible=!0},formatNumbers:function(t,e){var a=t.toString().split(".")[0],i=parseInt(a.slice(0,1)+Array(a.length-1).fill("0").join(""));return e>=i?parseInt(parseInt(a.slice(0,1))+1+Array(a.length-1).fill("0").join("")):i},fnRemindNum:function(t){var e=this;this.bfparams=t;var a=this;this.myChart=p.init(document.getElementById("itemmain")),this.myChart.setOption(this.option);var i={url:this.url,data:t,autoValid:!0},r={successCallback:function(t){a.dataCake=t.data.analysis;var i=[];if(i=t.data.analysis.orderSortByAmount,t.data.analysis.totalAmount,0==i.length){var r=document.getElementById("itemmain");return r.innerHTML="-暂无相关数据-",r.style.cssText="text-align:center; color: #999; border: none;line-height: 300px",void r.removeAttribute("_echarts_instance_")}var n=Array(i.length).fill(i[0].value),o=["#FF7272","#49CB8B","#5780ED","#9DA5C5"],l={tooltip:{trigger:"axis",axisPointer:{type:"none"},formatter:function(t){var e="";return t[0].name.length>18&&(t[0].name=a.insertStr(t[0].name,18,"<br/>")),e=t[0].name,e}},grid:{show:!1,top:"2%",right:"13%",bottom:98-10*i.length+"%",left:"9%"},xAxis:{show:!1,max:i[0].value},yAxis:[{show:!0,data:i.map((function(t){return t.name})),inverse:!0,axisLine:{show:!1},splitLine:{show:!1},axisTick:{show:!1},axisLabel:{color:"black",formatter:function(t,e){return"{img".concat(e,"|}")},rich:{value:{fontsize:20},img0:{height:31,backgroundColor:{image:M}},img1:{height:31,backgroundColor:{image:V}},img2:{height:31,backgroundColor:{image:j}},img3:{height:28,backgroundColor:{image:O}},img4:{height:28,backgroundColor:{image:K}},img5:{height:28,backgroundColor:{image:W}},img6:{height:28,backgroundColor:{image:G}},img7:{height:28,backgroundColor:{image:X}},img8:{height:28,backgroundColor:{image:q}},img9:{height:28,backgroundColor:{image:H}}}}},{show:!0,inverse:!0,data:i.map((function(t){return(100*t.percentage).toFixed(2)+"%"})),axisLabel:{textStyle:{verticalAlign:"middle",textAlign:"left",fontSize:11,color:"#606266"}},axisLine:{show:!1},splitLine:{show:!1},axisTick:{show:!1}},{type:"category",inverse:!0,offset:-10,position:"left",axisLine:{show:!1},axisTick:{show:!1},axisLabel:{color:"#606266",align:"left",verticalAlign:"bottom",lineHeight:32,fontSize:15,formatter:function(t){return t=p.format.truncateText(t,280,"14px Microsoft Yahei","..."),t}},data:i.map((function(t){return t.name}))}],series:[{name:"条",type:"bar",yAxisIndex:0,data:i.map((function(t){return t.value})),barWidth:18,itemStyle:{normal:{barBorderRadius:[8,8,8,8],color:function(t){var e=o.length;return t.dataIndex<=2?o[t.dataIndex%e]:o[3]}}},label:{normal:{align:"center",show:!0,position:"inside",formatter:function(t){return"元"==a.checked?"{white|¥"+t.data+"}":"{white|"+t.data+"次}"},rich:{white:{color:"#FFFFFF",fontSize:15,align:"center",padding:[0,0,4,150]}}}}},{name:"框",type:"bar",yAxisIndex:1,barGap:"-100%",data:n,barWidth:18,itemStyle:{normal:{color:"none",borderColor:"#E4E6EF",borderWidth:1,barBorderRadius:[8,8,8,8]}}}]};e.myChart.setOption(l)},failCallback:function(t){}};this.Base.submit(null,i,r)}}},At=st,ct=(0,m.Z)(At,ot,lt,!1,null,"c2fdcb04",null),ut=ct.exports,mt=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"titleLine"},[a("ta-title",{attrs:{title:t.title}}),a("div",{staticClass:"buttonClass"},[a("a",{staticClass:"more",on:{click:function(e){return t.handleMore()}}},[t._v("查看明细>>")])])],1),t._m(0),a("div",{attrs:{id:"docmain"}}),a("div",{attrs:{id:"content"}},[a("ta-modal",{attrs:{width:"90%",height:"700px",footer:null,destroyOnClose:!0,getContainer:t.setContainer,title:"查看明细"},model:{value:t.visible,callback:function(e){t.visible=e},expression:"visible"}},[a("detail-info",{attrs:{paramData:t.paramData}})],1)],1)])},dt=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{width:"458px",height:"32px",background:"#F0F0F0","margin-top":"20px","vertical-align":"middle",display:"table-cell"}},[a("span",{staticStyle:{"font-family":"PingFangSC-Regular","font-size":"15px",color:"#606266","margin-left":"0px"}},[t._v("排名")]),a("span",{staticStyle:{"font-family":"PingFangSC-Regular","font-size":"15px",color:"#606266","margin-left":"8px"}},[t._v("医生名称")]),a("span",{staticStyle:{"font-family":"PingFangSC-Regular","font-size":"15px",color:"#606266",float:"right","margin-right":"10px"}},[t._v("占比")]),a("span",{staticStyle:{"font-family":"PingFangSC-Regular","font-size":"15px",color:"#606266",float:"right","margin-right":"50px"}},[t._v("拒付金额")])])}],ht={name:"ruleNum",components:{detailInfo:R,TaTitle:A.Z},props:{paramData:Object,url:String,title:String},data:function(){return{option:{},checked:"元",dataCake:[],bfparams:{},visible:!1}},mounted:function(){},methods:{setContainer:function(){return document.getElementById("content")},insertStr:function(t,e,a){return t.slice(0,e)+a+t.slice(e)},handleMore:function(){this.visible=!0},formatNumbers:function(t,e){var a=t.toString().split(".")[0],i=parseInt(a.slice(0,1)+Array(a.length-1).fill("0").join(""));return e>=i?parseInt(parseInt(a.slice(0,1))+1+Array(a.length-1).fill("0").join("")):i},fnRemindNum:function(t){var e=this;this.bfparams=t;var a=this;this.myChart=p.init(document.getElementById("docmain")),this.myChart.setOption(this.option);var i={url:this.url,data:t,autoValid:!0},r={successCallback:function(t){a.dataCake=t.data.analysis;var i=[];if(i=t.data.analysis.orderSortByAmount,0==i.length){var r=document.getElementById("docmain");return r.innerHTML="-暂无相关数据-",r.style.cssText="text-align:center; color: #999; border: none;line-height: 300px",void r.removeAttribute("_echarts_instance_")}var n=Array(i.length).fill(i[0].value),o=["#FF7272","#49CB8B","#5780ED","#9DA5C5"],l={tooltip:{trigger:"axis",axisPointer:{type:"none"},formatter:function(t){var e="";return t[0].name.length>18&&(t[0].name=a.insertStr(t[0].name,18,"<br/>")),e=t[0].name,e}},grid:{show:!1,top:"2%",right:"13%",bottom:98-10*i.length+"%",left:"9%"},xAxis:{show:!1,max:i[0].value},yAxis:[{show:!0,data:i.map((function(t){return t.name})),inverse:!0,axisLine:{show:!1},splitLine:{show:!1},axisTick:{show:!1},axisLabel:{color:"black",formatter:function(t,e){return"{img".concat(e,"|}")},rich:{value:{fontsize:20},img0:{height:31,backgroundColor:{image:M}},img1:{height:31,backgroundColor:{image:V}},img2:{height:31,backgroundColor:{image:j}},img3:{height:28,backgroundColor:{image:O}},img4:{height:28,backgroundColor:{image:K}},img5:{height:28,backgroundColor:{image:W}},img6:{height:28,backgroundColor:{image:G}},img7:{height:28,backgroundColor:{image:X}},img8:{height:28,backgroundColor:{image:q}},img9:{height:28,backgroundColor:{image:H}}}}},{show:!0,inverse:!0,data:i.map((function(t){return(100*t.percentage).toFixed(2)+"%"})),axisLabel:{textStyle:{verticalAlign:"middle",textAlign:"left",fontSize:12,color:"#606266"}},axisLine:{show:!1},splitLine:{show:!1},axisTick:{show:!1}},{type:"category",inverse:!0,offset:-10,position:"left",axisLine:{show:!1},axisTick:{show:!1},axisLabel:{color:"#606266",align:"left",verticalAlign:"bottom",lineHeight:32,fontSize:15,formatter:function(t){return t=p.format.truncateText(t,280,"14px Microsoft Yahei","..."),t}},data:i.map((function(t){return t.name}))}],series:[{name:"条",type:"bar",yAxisIndex:0,data:i.map((function(t){return t.value})),barWidth:18,itemStyle:{normal:{barBorderRadius:[8,8,8,8],color:function(t){var e=o.length;return t.dataIndex<=2?o[t.dataIndex%e]:o[3]}}},label:{normal:{align:"center",show:!0,position:"inside",formatter:function(t){return"元"==a.checked?"{white|¥"+t.data+"}":"{white|"+t.data+"次}"},rich:{white:{color:"#FFFFFF",fontSize:15,align:"center",padding:[0,0,4,150]}}}}},{name:"框",type:"bar",yAxisIndex:1,barGap:"-100%",data:n,barWidth:18,itemStyle:{normal:{color:"none",borderColor:"#E4E6EF",borderWidth:1,barBorderRadius:[8,8,8,8]}}}]};e.myChart.setOption(l)},failCallback:function(t){}};this.Base.submit(null,i,r)}}},ft=ht,gt=(0,m.Z)(ft,mt,dt,!1,null,"2f734feb",null),pt=gt.exports,bt={name:"refusalAnalysis",components:{SearchTerm:h,PieNum:C,LineNum:N,RuleNum:$,DeptNum:nt,ItemNum:ut,DocNum:pt},data:function(){return{taskRecord:{},parameters:{},t_title:"",isShow:!1}},created:function(){},mounted:function(){},methods:{fnQuery:function(t){this.parameters=t,this.$refs.pieNum.fnRemindNum(t),this.$refs.lineNum.fnRemindNum(t),this.$refs.ruleNum.fnRemindNum(t),this.$refs.itemNum.fnRemindNum(t),this.$refs.deptNum.fnRemindNum(t),this.$refs.docNum.fnRemindNum(t)},fnLinkQuery:function(t,e){switch(t){case"pie":break;case"line":break;case"dept":this.parameters.departName=e.name,this.parameters.departCode=e.id,this.$refs.docNum.fnRemindNum(this.parameters);break;case"rule":this.parameters.ruleName=e.name,this.parameters.ruleId=e.id,this.$refs.itemNum.fnRemindNum(this.parameters);break;case"doctor":break;case"item":break}}}},yt=bt,vt=(0,m.Z)(yt,i,r,!1,null,"7d56f45f",null),Ct=vt.exports},36766:function(t,e,a){var i=a(66586);e["Z"]=i.Z},26263:function(t,e,a){a.d(e,{s:function(){return i},x:function(){return r}});var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},r=[]},66586:function(t,e){var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};e["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}}}]);
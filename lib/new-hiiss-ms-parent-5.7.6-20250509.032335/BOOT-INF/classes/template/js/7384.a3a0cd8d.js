(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[7384],{9660:function(e,t,i){"use strict";i.r(t),i.d(t,{default:function(){return W}});var a=function(){var e=this,t=this,i=t.$createElement,a=t._self._c||i;return a("div",[a("ta-border-layout",{staticStyle:{},attrs:{layout:{header:"70px"}}},[a("div",{attrs:{slot:"header"},slot:"header"},[a("ta-form",{staticStyle:{"margin-top":"10px"},attrs:{autoFormCreate:function(t){e.baseInfoForm=t},layout:"horizontal","label-width":"80px",formLayout:!0}},[a("ta-form-item",{attrs:{fieldDecoratorId:"allDate",span:4,label:"月份","init-value":t.rangeValue,fieldDecoratorOptions:{rules:[{required:!0,message:"必须选取日期"}]}}},[a("ta-month-picker",{staticStyle:{width:"100%"}})],1),a("ta-form-item",{attrs:{"field-decorator-id":"akb020",label:"院区标识",span:4,fieldDecoratorOptions:{rules:[{required:!0,message:"必须选取院区"}]},disabled:t.paramsDisable.akb020}},[a("ta-select",{attrs:{showSearch:"",placeholder:"院区标识筛选",options:t.hosList},on:{change:t.fnQueryDept}})],1),a("ta-form-item",{attrs:{"field-decorator-id":"aae140",label:"人群类别",span:4}},[a("ta-select",{attrs:{placeholder:"人群类别筛选","collection-filter":"310,390",reverseFilter:!0,allowClear:"",collectionType:"AAE140"}})],1),"ks"===t.dimensionflag||"ys"===t.dimensionflag?a("ta-form-item",{attrs:{"field-decorator-id":"aaz307",label:"科室",span:4,fieldDecoratorOptions:{rules:[{required:!0,message:"必须选取科室"}]},disabled:t.paramsDisable.aaz307}},[a("ta-select",{attrs:{showSearch:"","show-search":!0,placeholder:"科室筛选",options:t.ksList}})],1):t._e(),"ys"===t.dimensionflag?a("ta-form-item",{attrs:{"field-decorator-id":"aaz263",label:"医师名称",span:4,fieldDecoratorOptions:{rules:[{required:!0,message:"必须选取医师"}]},disabled:t.paramsDisable.aaz263}},[a("ta-select",{attrs:{showSearch:"",optionLabelProp:"children",mode:"combobox",placeholder:"医生名称筛选",options:t.doctorList,filterOption:t.filterOption}})],1):t._e(),a("div",{staticStyle:{display:"flex","margin-left":"20px",float:"right"}},[a("ta-button",{attrs:{type:"primary"},on:{click:t.fnQuery}},[t._v("查询")]),a("ta-button",{staticStyle:{"margin-left":"10px"},on:{click:t.fnReSet}},[t._v("重置")])],1)],1)],1),a("div",[this.dimensionflag?a("ta-title",{attrs:{title:"ks"===this.dimensionflag?"科室总体指标":"医师总体指标"}},[a("ta-tooltip",{staticStyle:{"margin-left":"5px"},attrs:{placement:"top"}},[a("template",{slot:"title"},[a("span",{staticStyle:{"font-size":"12px"}},[t._v(" 仅展示已配置预警值的指标 ")])]),a("ta-icon",{attrs:{type:"question-circle"}})],2)],1):a("ta-title",{attrs:{title:"总体指标"}},[a("ta-tooltip",{staticStyle:{"margin-left":"5px"},attrs:{placement:"top"}},[a("template",{slot:"title"},[a("span",{staticStyle:{"font-size":"12px"}},[t._v(" 仅展示已配置预警值的指标 ")])]),a("ta-icon",{attrs:{type:"question-circle"}})],2)],1),a("div",{ref:"container",staticClass:"container"},[t._l(t.overallIndicators,(function(e){return t.overallIndicators.length>0?a("div",[a("ta-card",{style:{width:t.containerWidth/t.overallIndicators.length+"px"},attrs:{bordered:!1},scopedSlots:t._u([{key:"title",fn:function(){return[a("span",{style:{marginLeft:t.containerWidth/t.overallIndicators.length/2-8*e.indexname.length+"px"}},[t._v(t._s(e.indexname))]),a("ta-tooltip",{directives:[{name:"show",rawName:"v-show",value:"zbx06"===e.indexcode,expression:"item.indexcode === 'zbx06'"}],staticStyle:{"margin-left":"5px"},attrs:{placement:"top"}},[a("template",{slot:"title"},[a("span",{staticStyle:{"font-size":"12px"}},[t._v(" 药占比：三目类别中药品的占比 ")])]),a("ta-icon",{attrs:{type:"question-circle"}})],2)]},proxy:!0}],null,!0)},[a("div",{staticClass:"word-center"},[a("div",{staticStyle:{color:"rgb(51, 51, 51)","font-size":"22px","font-weight":"500"}},[t._v(" "+t._s(t.formatAmountDependingOnValue(e.orgindexvalue))+" "),a("span",{staticStyle:{"font-size":"12px",color:"rgb(164, 164, 164)"}},[t._v(t._s(t.formatUnit(e.orgindexvalue,e.indexcode)))])]),a("div",[a("span",{directives:[{name:"show",rawName:"v-show",value:"0"===e.mark,expression:"item.mark === '0'"}],staticStyle:{"font-size":"12px",color:"rgb(255, 255, 255)",background:"#0f990f",border:"2px solid #0f990f","border-radius":"5px"}},[t._v("正常")]),a("span",{directives:[{name:"show",rawName:"v-show",value:"1"===e.mark,expression:"item.mark === '1'"}],staticStyle:{"font-size":"12px",color:"rgb(255, 255, 255)",background:"#ff0006",border:"2px solid #ff0006","border-radius":"5px"}},[t._v("超标")]),a("span",{directives:[{name:"show",rawName:"v-show",value:"2"===e.mark,expression:"item.mark === '2'"}],staticStyle:{"font-size":"12px",color:"rgb(255, 255, 255)",background:"#ffc400",border:"2px solid #ffc400","border-radius":"5px"}},[t._v("风险")]),a("span",{staticStyle:{"font-size":"12px","margin-left":"5px",color:"rgb(164, 164, 164)"}},[t._v("标准值："+t._s(t.formatAmountDependingOnValue(e.indexvalue)+t.formatUnit(e.indexvalue,e.indexcode)))])])])])],1):t._e()})),0===t.overallIndicators.length?a("diV",{staticClass:"word-center"},[a("div",{staticClass:"warning-text"},[t._v("当前"+t._s(""===t.dimensionflag?"院区":"科室或医师")+"未分配预警指标")])]):t._e()],2)],1),a("div",{staticClass:"fit",attrs:{slot:"footer"},slot:"footer"},[a("ta-row",{attrs:{gutter:10}},t._l(t.overallIndicators,(function(e){return a("ta-col",{key:e.indexcode,attrs:{span:t.containerWidth<=1500?12:8}},[a("ta-card",{attrs:{bordered:!1}},[a("ta-title",{staticStyle:{"font-size":"16px"},attrs:{slot:"title"},slot:"title"},[t._v(" "+t._s((""===t.dimensionflag?"科室":"医师")+e.indexname+"前5排名")+" "),"zbx07"===e.indexcode?a("ta-tooltip",{staticStyle:{"margin-left":"5px"},attrs:{placement:"top"}},[a("template",{slot:"title"},[a("ul",{staticStyle:{"font-size":"12px"}},[a("li",[t._v("总收入: 所选条件下出院病例的住院费用之和;")]),a("li",[t._v("药品收入: 所选条件下出院病例住院费用中的药品费之和;")]),a("li",[t._v("药占比: (药品收入 ÷ 总收入) x 100%;")])])]),a("ta-icon",{attrs:{type:"question-circle"}})],2):t._e()],1),a(t.getComponent(e.indexcode),{ref:e.indexcode+"BarRef",refInFor:!0,tag:"component",attrs:{paramData:t.parameters,indexItem:e}})],1)],1)})),1)],1)])],1)},n=[],s=i(66347),r=i(89584),o=i(48534),l=i(95082),c=(i(36133),i(36797)),u=i.n(c),d=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("h3",{staticStyle:{"font-weight":"normal"}},[i("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:e.titleSvg}}),i("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[e._v(e._s(e.title))]),e._t("default")],2)},h=[],m={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '},f={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:m}}},p=f,x=i(1001),v=(0,x.Z)(p,d,h,!1,null,"5e7ef0ae",null),b=v.exports;function g(e){return e?e.akb020&&e.aaz263?"personal":e.akb020&&e.aaz309&&e.aaz307?"group":e.akb020&&e.aaz307?"department":e.akb020?"hospital":"all":"all"}function z(e){return L.apply(this,arguments)}function L(){return L=(0,o.Z)(regeneratorRuntime.mark((function e(t){var i,a,n,s,r,o,l,c,u,d,h,m;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,Base.submit(null,{url:"/miimCommonRead/queryDataPermission",showPageLoading:!1,data:{jobnumber:t},autoValid:!0});case 2:return i=e.sent,a=new Set,n=new Set,i.data.permission.forEach((function(e){var t=g(e);"hospital"===t&&a.add(e.akb020),"department"===t&&n.add(e.aaz307)})),s=i.data.permission.filter((function(e){return"department"===g(e)||!n.has(e.aaz307)})).filter((function(e){return"hospital"===g(e)||!a.has(e.akb020)})),r=new Set(s.map((function(e){return e.akb020})).filter((function(e){return null!=e}))),o=new Set(s.map((function(e){return e.aaz307})).filter((function(e){return null!=e}))),l=new Set(s.map((function(e){return e.aaz309})).filter((function(e){return null!=e}))),c=new Set(s.map((function(e){return e.aaz263})).filter((function(e){return null!=e}))),u=!1,d=!1,h=!1,m=!1,1===r.size&&(u=!0),1===o.size&&1===r.size&&(d=!0),1===o.size&&1===r.size&&1===l.size&&(h=!0),1===r.size&&0===o.size&&1===c.size&&(m=!0),e.abrupt("return",{akb020Set:r,aaz307Set:o,aaz263Set:c,aaz309Set:l,akb020Disable:u,aaz307Disable:d,aaz263Disable:m,aaz309Disable:h});case 20:case"end":return e.stop()}}),e)}))),L.apply(this,arguments)}function y(e,t){Base.submit(null,{url:"miimCommonRead/getAa01AAE500StartStop",data:e},{successCallback:function(e){return t(e)}})}function w(e,t){Base.submit(null,{url:"miimCommonRead/insertTableColumShow",data:e,autoValid:!1},{successCallback:function(e){return t("success")},failCallback:function(e){return t("fail")}})}var C={zbx01:{footer:function(){return"统筹金总额"}},zbx02:{footer:function(){return"基金支出总额"}},zbx03:{footer:function(){return"总费用"}},zbx04:{footer:function(){return"总住院日"}}},k={permissionCheck:z,getAa01AAE500StartStop:y,insertTableColumShow:w,props:C,moneyNumFormat:function(e){var t=e.cellValue;return t||"—"}},I=i(22722),S=i(55115),D=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[i("div",{staticClass:"chart-box",attrs:{id:this.indexItem.indexcode}})])},B=[],F=i(1708),O={name:"hospitalBar",components:{TaTitle:b},props:{paramData:Object,indexItem:Object,type:String,url:String},data:function(){return{params:{},unit:"元",option:{},nodataOption:{graphic:{type:"text",left:"center",top:"middle",style:{text:"暂无数据",fontSize:20,fill:"#999"},z:100},series:[]}}},methods:{fnChartName:function(){return this.indexItem.indexname+"前5排名"},changeUnit:function(){["zbx01","zbx02","zbx03"].includes(this.indexItem.indexcode)?this.unit="元":"zbx04"==this.indexItem.indexcode?this.unit="天":["zbx06","zbx07","zbx08"].includes(this.indexItem.indexcode)?this.unit="%":this.unit="人次"},fnBuildChart:function(e){this.changeUnit();var t=this,i=F.init(document.getElementById(this.indexItem.indexcode));if(e&&0!==e.length){var a=e.reduce((function(e,t){return e+t.value}),0),n={title:{text:this.indexItem.indexname,x:"center"},tooltip:{trigger:"item",formatter:function(e){return e.marker+""+e.data.name+"</br>数量："+e.data.value+t.unit+"</br>"}},toolbox:{show:!0,feature:{dataView:{readOnly:!1},myPie:{show:!0,title:"切换为饼状图",icon:"path://M512 98.304C282.624 98.304 98.304 282.624 98.304 512s184.32 413.696 413.696 413.696c229.376 0 413.696-184.32 413.696-413.696S741.376 98.304 512 98.304zM888.832 491.52l-331.776 0 233.472-233.472C847.872 323.584 884.736 401.408 888.832 491.52zM512 888.832c-208.896 0-376.832-167.936-376.832-376.832 0-208.896 167.936-376.832 376.832-376.832 98.304 0 184.32 36.864 253.952 98.304l-266.24 266.24c-4.096 4.096-4.096 8.192-4.096 12.288 0 12.288 8.192 20.48 20.48 20.48l376.832 0C876.544 729.088 712.704 888.832 512 888.832z",onclick:function(){t.fnBuildChart(e)}},myBar:{show:!0,title:"切换为状图",icon:"path://M163.84 880.64h122.88v-737.28h-122.88v737.28z m-40.96-778.24h204.8v819.2h-204.8v-819.2z m286.72 204.8h204.8v614.4h-204.8v-614.4z m40.96 573.44h122.88v-532.48h-122.88v532.48z m245.76-368.64h204.8v409.6h-204.8v-409.6z m40.96 368.64h122.88v-327.68h-122.88v327.68z",onclick:function(){t.fnBuildChartBar(e)}},myLine:{show:!0,title:"切换为折线图",icon:"path://M3.04761905,3.23733333 L3.04761905,12.952 L4.38209523,12.952 L13.5238095,12.951619 L13.5238095,14.0944762 L3.61904762,14.0944762 C2.67227377,14.0944762 1.90476191,13.3269643 1.90476191,12.3801905 L1.90476191,3.23733333 L3.04761905,3.23733333 Z M13.615619,2.47619047 L13.615619,6.47619047 L12.4727619,6.47619047 L12.4727619,4.49257142 L8.99619047,8.05866667 L7.89523809,6.91809523 L5.36342858,9.53980953 L4.54133333,8.74590477 L7.89485714,5.27238095 L9.00038095,6.41752381 L11.728,3.61904762 L9.89828572,3.61904762 L9.89828572,2.47619047 L13.615619,2.47619047 L13.615619,2.47619047 Z",onclick:function(){t.fnBuildChartLine(e)}},magicType:{show:!0},restore:{show:!0},saveAsImage:{name:t.fnChartName(),show:!0}}},legend:{type:"scroll",orient:"vertical",left:"39%",align:"left",top:"middle",icon:"circle",formatter:function(i){for(var n,s=0;s<e.length;s++)e[s].name===i&&(n=e[s].value,a>0?(n/a*100).toFixed(2):0);var r=i.length>7?i.slice(0,7)+"...":i,o=["{a|"+r+"}","{b|"+n+t.unit+"}"];return o.join("")},textStyle:{lineHeight:"30",rich:{a:{fontSize:12,color:"#444444",width:100},b:{fontSize:14,width:100,align:"right",color:"#333333",fontWeight:550},c:{fontSize:12,width:90,color:"#B7B7B7"}}}},series:[{name:this.indexItem.indexname,type:"pie",center:["20%","50%"],radius:["40%","20%"],avoidLabelOverlap:!1,color:["#CEF5D0","#CBE9FD","#9FCEFA","#70C87D","#57B8BB","#1890FF"],label:{show:!1,position:"center",fontSize:"14",color:"#323233",formatter:function(){return parseFloat(a.toFixed(2))}},emphasis:{label:{show:!1,fontSize:12}},labelLine:{show:!1},data:e}]};i.resize(),i.clear(),i.setOption(n,!0)}else i.setOption(this.nodataOption,!0)},fnBuildChartLine:function(e){this.changeUnit();var t=this,i=F.init(document.getElementById(this.indexItem.indexcode));if(e&&0!==e.length){var a={title:{text:this.indexItem.indexname,x:"center"},tooltip:{trigger:"axis",axisPointer:{type:"line"},formatter:"{b}:<br>{c} "+t.unit},legend:{data:[this.indexItem.indexname],top:"10%",left:"center",orient:"horizontal"},grid:{top:"18%",left:"18%",right:"2%",bottom:"20%"},toolbox:{show:!0,feature:{dataView:{readOnly:!1},myPie:{show:!0,title:"切换为饼状图",icon:"path://M512 98.304C282.624 98.304 98.304 282.624 98.304 512s184.32 413.696 413.696 413.696c229.376 0 413.696-184.32 413.696-413.696S741.376 98.304 512 98.304zM888.832 491.52l-331.776 0 233.472-233.472C847.872 323.584 884.736 401.408 888.832 491.52zM512 888.832c-208.896 0-376.832-167.936-376.832-376.832 0-208.896 167.936-376.832 376.832-376.832 98.304 0 184.32 36.864 253.952 98.304l-266.24 266.24c-4.096 4.096-4.096 8.192-4.096 12.288 0 12.288 8.192 20.48 20.48 20.48l376.832 0C876.544 729.088 712.704 888.832 512 888.832z",onclick:function(){t.fnBuildChart(e)}},myBar:{show:!0,title:"切换为状图",icon:"path://M163.84 880.64h122.88v-737.28h-122.88v737.28z m-40.96-778.24h204.8v819.2h-204.8v-819.2z m286.72 204.8h204.8v614.4h-204.8v-614.4z m40.96 573.44h122.88v-532.48h-122.88v532.48z m245.76-368.64h204.8v409.6h-204.8v-409.6z m40.96 368.64h122.88v-327.68h-122.88v327.68z",onclick:function(){t.fnBuildChartBar(e)}},myLine:{show:!0,title:"切换为折线图",icon:"path://M3.04761905,3.23733333 L3.04761905,12.952 L4.38209523,12.952 L13.5238095,12.951619 L13.5238095,14.0944762 L3.61904762,14.0944762 C2.67227377,14.0944762 1.90476191,13.3269643 1.90476191,12.3801905 L1.90476191,3.23733333 L3.04761905,3.23733333 Z M13.615619,2.47619047 L13.615619,6.47619047 L12.4727619,6.47619047 L12.4727619,4.49257142 L8.99619047,8.05866667 L7.89523809,6.91809523 L5.36342858,9.53980953 L4.54133333,8.74590477 L7.89485714,5.27238095 L9.00038095,6.41752381 L11.728,3.61904762 L9.89828572,3.61904762 L9.89828572,2.47619047 L13.615619,2.47619047 L13.615619,2.47619047 Z",onclick:function(){t.fnBuildChartLine(e)}},magicType:{show:!0},restore:{show:!0},saveAsImage:{name:t.fnChartName(),show:!0}}},xAxis:{triggerEvent:!0,boundaryGap:!1,axisLine:{show:!0,lineStyle:{color:"#797979",width:"1"}},axisLabel:{margin:10,rotate:45,interval:0,formatter:function(e){return e.length>5?e.slice(0,5)+"...":e}},axisTick:{show:!0},type:"category",data:e.map((function(e){return e.name}))},yAxis:{type:"value",splitLine:{lineStyle:{type:"dashed"}},axisLabel:{margin:20},axisLine:{show:!1}},series:[{name:this.indexItem.indexname,color:["#108EE9"],data:e.map((function(e){return e.value})),type:"line",splitLine:{show:!0}}]};i.resize(),i.clear(),i.setOption(a,!0)}else i.setOption(this.nodataOption,!0)},fnBuildChartBar:function(e){this.changeUnit();var t=this,i=F.init(document.getElementById(this.indexItem.indexcode));if(e&&0!==e.length){var a={title:{text:this.indexItem.indexname,x:"center"},tooltip:{trigger:"axis",axisPointer:{type:"line"},formatter:"{b}:<br>{c} "+t.unit},legend:{data:[this.indexItem.indexname],top:"10%",left:"center",orient:"horizontal"},grid:{top:"18%",left:"18%",right:"2%",bottom:"20%"},toolbox:{show:!0,feature:{dataView:{readOnly:!1},myPie:{show:!0,title:"切换为饼状图",icon:"path://M512 98.304C282.624 98.304 98.304 282.624 98.304 512s184.32 413.696 413.696 413.696c229.376 0 413.696-184.32 413.696-413.696S741.376 98.304 512 98.304zM888.832 491.52l-331.776 0 233.472-233.472C847.872 323.584 884.736 401.408 888.832 491.52zM512 888.832c-208.896 0-376.832-167.936-376.832-376.832 0-208.896 167.936-376.832 376.832-376.832 98.304 0 184.32 36.864 253.952 98.304l-266.24 266.24c-4.096 4.096-4.096 8.192-4.096 12.288 0 12.288 8.192 20.48 20.48 20.48l376.832 0C876.544 729.088 712.704 888.832 512 888.832z",onclick:function(){t.fnBuildChart(e)}},myBar:{show:!0,title:"切换为状图",icon:"path://M163.84 880.64h122.88v-737.28h-122.88v737.28z m-40.96-778.24h204.8v819.2h-204.8v-819.2z m286.72 204.8h204.8v614.4h-204.8v-614.4z m40.96 573.44h122.88v-532.48h-122.88v532.48z m245.76-368.64h204.8v409.6h-204.8v-409.6z m40.96 368.64h122.88v-327.68h-122.88v327.68z",onclick:function(){t.fnBuildChartBar(e)}},myLine:{show:!0,title:"切换为折线图",icon:"path://M3.04761905,3.23733333 L3.04761905,12.952 L4.38209523,12.952 L13.5238095,12.951619 L13.5238095,14.0944762 L3.61904762,14.0944762 C2.67227377,14.0944762 1.90476191,13.3269643 1.90476191,12.3801905 L1.90476191,3.23733333 L3.04761905,3.23733333 Z M13.615619,2.47619047 L13.615619,6.47619047 L12.4727619,6.47619047 L12.4727619,4.49257142 L8.99619047,8.05866667 L7.89523809,6.91809523 L5.36342858,9.53980953 L4.54133333,8.74590477 L7.89485714,5.27238095 L9.00038095,6.41752381 L11.728,3.61904762 L9.89828572,3.61904762 L9.89828572,2.47619047 L13.615619,2.47619047 L13.615619,2.47619047 Z",onclick:function(){t.fnBuildChartLine(e)}},magicType:{show:!0},restore:{show:!0},saveAsImage:{name:t.fnChartName(),show:!0}}},xAxis:{type:"category",axisLine:{show:!0,lineStyle:{color:"#797979",width:"1"}},axisLabel:{margin:10,rotate:45,interval:0,formatter:function(e){return e.length>5?e.slice(0,5)+"...":e}},data:e.map((function(e){return e.name}))},yAxis:{type:"value",splitLine:{lineStyle:{type:"dashed"}},axisLabel:{margin:20},axisLine:{show:!1}},series:[{name:this.indexItem.indexname,data:e.map((function(e){return e.value})),type:"bar",barWidth:"35%",itemStyle:{color:"#1890FF"},splitLine:{show:!0}}]};i.resize(),i.clear(),i.setOption(a,!0)}else i.setOption(this.nodataOption,!0)},fnQuery:function(){this.fnBuildChart()}}},M=O,_=(0,x.Z)(M,D,B,!1,null,"42b1df7c",null),A=_.exports,E=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[i("div",{staticClass:"chart-box",attrs:{id:this.indexItem.indexcode}})])},$=[],V={name:"doctorBar",components:{TaTitle:b},props:{paramData:Object,indexItem:Object,type:String,url:String},data:function(){return{params:{},unit:"元",option:{},nodataOption:{graphic:{type:"text",left:"center",top:"middle",style:{text:"暂无数据",fontSize:20,fill:"#999"},z:100},series:[]}}},methods:{fnChartName:function(){return this.indexItem.indexname+"前5排名"},changeUnit:function(){["zbx01","zbx02","zbx03","zbx06","zbx07"].includes(this.indexItem.indexcode)?this.unit="元":"zbx04"==this.indexItem.indexcode?this.unit="天":this.unit="人次"},fnBuildChart:function(e){this.changeUnit();var t=this,i=F.init(document.getElementById(this.indexItem.indexcode));if(e&&0!==e.length){var a=e.reduce((function(e,t){return e+t.value}),0),n={title:{text:this.indexItem.indexname,x:"center"},tooltip:{trigger:"item",formatter:function(e){return e.marker+""+e.data.name+"</br>数量："+e.data.value+t.unit+"</br>占比："+e.percent+"%"}},toolbox:{show:!0,feature:{dataView:{readOnly:!1},myPie:{show:!0,title:"切换为饼状图",icon:"path://M512 98.304C282.624 98.304 98.304 282.624 98.304 512s184.32 413.696 413.696 413.696c229.376 0 413.696-184.32 413.696-413.696S741.376 98.304 512 98.304zM888.832 491.52l-331.776 0 233.472-233.472C847.872 323.584 884.736 401.408 888.832 491.52zM512 888.832c-208.896 0-376.832-167.936-376.832-376.832 0-208.896 167.936-376.832 376.832-376.832 98.304 0 184.32 36.864 253.952 98.304l-266.24 266.24c-4.096 4.096-4.096 8.192-4.096 12.288 0 12.288 8.192 20.48 20.48 20.48l376.832 0C876.544 729.088 712.704 888.832 512 888.832z",onclick:function(){t.fnBuildChart(e)}},myBar:{show:!0,title:"切换为状图",icon:"path://M163.84 880.64h122.88v-737.28h-122.88v737.28z m-40.96-778.24h204.8v819.2h-204.8v-819.2z m286.72 204.8h204.8v614.4h-204.8v-614.4z m40.96 573.44h122.88v-532.48h-122.88v532.48z m245.76-368.64h204.8v409.6h-204.8v-409.6z m40.96 368.64h122.88v-327.68h-122.88v327.68z",onclick:function(){t.fnBuildChartBar(e)}},myLine:{show:!0,title:"切换为折线图",icon:"path://M3.04761905,3.23733333 L3.04761905,12.952 L4.38209523,12.952 L13.5238095,12.951619 L13.5238095,14.0944762 L3.61904762,14.0944762 C2.67227377,14.0944762 1.90476191,13.3269643 1.90476191,12.3801905 L1.90476191,3.23733333 L3.04761905,3.23733333 Z M13.615619,2.47619047 L13.615619,6.47619047 L12.4727619,6.47619047 L12.4727619,4.49257142 L8.99619047,8.05866667 L7.89523809,6.91809523 L5.36342858,9.53980953 L4.54133333,8.74590477 L7.89485714,5.27238095 L9.00038095,6.41752381 L11.728,3.61904762 L9.89828572,3.61904762 L9.89828572,2.47619047 L13.615619,2.47619047 L13.615619,2.47619047 Z",onclick:function(){t.fnBuildChartLine(e)}},magicType:{show:!0},restore:{show:!0},saveAsImage:{name:t.fnChartName(),show:!0}}},legend:{type:"scroll",orient:"vertical",left:"39%",align:"left",top:"middle",icon:"circle",formatter:function(i){for(var n,s,r=0;r<e.length;r++)e[r].name===i&&(n=e[r].value,s=a>0?(n/a*100).toFixed(2):0);var o=i.length>7?i.slice(0,7)+"...":i,l=["{a|"+o+"}","{b|"+n+t.unit+"}","{c|"+s+"%}"];return l.join("")},textStyle:{lineHeight:"30",rich:{a:{fontSize:12,color:"#444444",width:100},b:{fontSize:14,width:100,align:"right",color:"#333333",fontWeight:550},c:{fontSize:12,width:90,color:"#B7B7B7"}}}},series:[{name:this.indexItem.indexname,type:"pie",center:["20%","50%"],radius:["40%","20%"],avoidLabelOverlap:!1,color:["#CEF5D0","#CBE9FD","#9FCEFA","#70C87D","#57B8BB","#1890FF"],label:{show:!0,position:"center",fontSize:"14",color:"#323233",formatter:function(){return parseFloat(a.toFixed(2))}},emphasis:{label:{show:!1,fontSize:12}},labelLine:{show:!1},data:e}]};i.resize(),i.clear(),i.setOption(n,!0)}else i.setOption(this.nodataOption,!0)},fnBuildChartLine:function(e){this.changeUnit();var t=this,i=F.init(document.getElementById(this.indexItem.indexcode));if(e&&0!==e.length){var a={tooltip:{trigger:"axis",axisPointer:{type:"line"},formatter:"{b}:<br>{c} "+t.unit},legend:{data:[this.indexItem.indexname],top:"10%",left:"center",orient:"horizontal"},grid:{top:"18%",left:"18%",right:"2%",bottom:"20%"},toolbox:{show:!0,feature:{dataView:{readOnly:!1},myPie:{show:!0,title:"切换为饼状图",icon:"path://M512 98.304C282.624 98.304 98.304 282.624 98.304 512s184.32 413.696 413.696 413.696c229.376 0 413.696-184.32 413.696-413.696S741.376 98.304 512 98.304zM888.832 491.52l-331.776 0 233.472-233.472C847.872 323.584 884.736 401.408 888.832 491.52zM512 888.832c-208.896 0-376.832-167.936-376.832-376.832 0-208.896 167.936-376.832 376.832-376.832 98.304 0 184.32 36.864 253.952 98.304l-266.24 266.24c-4.096 4.096-4.096 8.192-4.096 12.288 0 12.288 8.192 20.48 20.48 20.48l376.832 0C876.544 729.088 712.704 888.832 512 888.832z",onclick:function(){t.fnBuildChart(e)}},myBar:{show:!0,title:"切换为状图",icon:"path://M163.84 880.64h122.88v-737.28h-122.88v737.28z m-40.96-778.24h204.8v819.2h-204.8v-819.2z m286.72 204.8h204.8v614.4h-204.8v-614.4z m40.96 573.44h122.88v-532.48h-122.88v532.48z m245.76-368.64h204.8v409.6h-204.8v-409.6z m40.96 368.64h122.88v-327.68h-122.88v327.68z",onclick:function(){t.fnBuildChartBar(e)}},myLine:{show:!0,title:"切换为折线图",icon:"path://M3.04761905,3.23733333 L3.04761905,12.952 L4.38209523,12.952 L13.5238095,12.951619 L13.5238095,14.0944762 L3.61904762,14.0944762 C2.67227377,14.0944762 1.90476191,13.3269643 1.90476191,12.3801905 L1.90476191,3.23733333 L3.04761905,3.23733333 Z M13.615619,2.47619047 L13.615619,6.47619047 L12.4727619,6.47619047 L12.4727619,4.49257142 L8.99619047,8.05866667 L7.89523809,6.91809523 L5.36342858,9.53980953 L4.54133333,8.74590477 L7.89485714,5.27238095 L9.00038095,6.41752381 L11.728,3.61904762 L9.89828572,3.61904762 L9.89828572,2.47619047 L13.615619,2.47619047 L13.615619,2.47619047 Z",onclick:function(){t.fnBuildChartLine(e)}},magicType:{show:!0},restore:{show:!0},saveAsImage:{name:t.fnChartName(),show:!0}}},xAxis:{triggerEvent:!0,boundaryGap:!1,axisLine:{show:!0,lineStyle:{color:"#797979",width:"1"}},axisLabel:{margin:10,rotate:45,interval:0,formatter:function(e){return e.length>5?e.slice(0,5)+"...":e}},axisTick:{show:!0},type:"category",data:e.map((function(e){return e.name}))},yAxis:{type:"value",splitLine:{lineStyle:{type:"dashed"}},axisLabel:{margin:20},axisLine:{show:!1}},series:[{name:this.indexItem.indexname,color:["#108EE9"],data:e.map((function(e){return e.value})),type:"line",splitLine:{show:!0}}]};i.resize(),i.clear(),i.setOption(a,!0)}else i.setOption(this.nodataOption,!0)},fnBuildChartBar:function(e){this.changeUnit();var t=this,i=F.init(document.getElementById(this.indexItem.indexcode));if(e&&0!==e.length){var a={title:{text:this.indexItem.indexname,x:"center"},tooltip:{trigger:"axis",axisPointer:{type:"line"},formatter:function(i){var a=i[0].name+"<br/>";return i.forEach((function(e){a+=e.marker+e.seriesName+": "+e.value+t.unit+" <br/>"})),a=a+"总收入: "+e[i[0].dataIndex].total+t.unit+" <br/>",a=a+"占比: "+e[i[0].dataIndex].value+"% <br/>",a}},legend:{data:["其他收入","zbx06"===t.indexItem.indexcode?"药品收入":"耗材收入"],top:"10%",left:"center",orient:"horizontal"},grid:{top:"18%",left:"18%",right:"2%",bottom:"20%"},toolbox:{show:!0,feature:{dataView:{readOnly:!1},magicType:{show:!0},restore:{show:!0},saveAsImage:{name:t.fnChartName(),show:!0}}},xAxis:{type:"category",axisLine:{show:!0,lineStyle:{color:"#797979",width:"1"}},axisLabel:{margin:10,rotate:45,interval:0,formatter:function(e){return e.length>5?e.slice(0,5)+"...":e}},data:e.map((function(e){return e.name}))},yAxis:{type:"value",splitLine:{lineStyle:{type:"dashed"}},axisLabel:{margin:20},axisLine:{show:!1}},series:[{name:"其他收入",data:e.map((function(e){return parseFloat(e.total-e.commonnum).toFixed(2)})),type:"bar",stack:"one",barWidth:"30px",itemStyle:{color:"#1890FF"},splitLine:{show:!0}},{name:"zbx06"===t.indexItem.indexcode?"药品收入":"耗材收入",data:e.map((function(e){return e.commonnum})),type:"bar",stack:"one",barWidth:"30px",itemStyle:{color:"#56d767"},splitLine:{show:!0}}]};i.resize(),i.clear(),i.setOption(a,!0)}else i.setOption(this.nodataOption,!0)},fnQuery:function(){this.fnBuildChart()}}},T=V,Y=(0,x.Z)(T,E,$,!1,null,"5e2b9a4e",null),q=Y.exports;S.w3.prototype.Base=Object.assign(S.w3.prototype.Base,(0,l.Z)({},I.Z));var R={name:"indexWarning",components:{HospitalBar:A,DoctorBar:q,TaTitle:b},data:function(){return{rangeValue:this.Base.getMoment((new Date).toISOString().slice(0,7),"YYYY-MM"),amountData:{},statisticsDimension:[],statisticsIndex:[],selectedDimension:[],selectedDimensionItemList:[],checkedIndexList:[],selectedIndexItemList:[],defaultCheckedIndex:[],overallIndicators:[],overallIndicators_nodata:[{indexcode:"zbx01",indexname:"人均统筹金",orgindexvalue:" ",indexvalue:" ",warnvalue:" ",mark:"0"},{indexcode:"zbx02",indexname:"人均基金支出",orgindexvalue:" ",indexvalue:" ",warnvalue:" ",mark:"0"},{indexcode:"zbx03",indexname:"人均总费用",orgindexvalue:" ",indexvalue:" ",warnvalue:" ",mark:"0"},{indexcode:"zbx04",indexname:"平均住院日",orgindexvalue:" ",indexvalue:" ",warnvalue:" ",mark:"0"},{indexcode:"zbx05",indexname:"出院人次",orgindexvalue:" ",indexvalue:" ",warnvalue:" ",mark:"0"},{indexcode:"zbx06",indexname:"药占比",orgindexvalue:" ",indexvalue:" ",warnvalue:" ",mark:"0"},{indexcode:"zbx07",indexname:"耗材比",orgindexvalue:" ",indexvalue:" ",warnvalue:" ",mark:"0"}],groupedData:[],echartsIndex:[],sortColumn:"",ascOrDesc:"",akb020:"",aaz307:"",indexItem:{label:"test",value:"test"},activeKey:0,containerWidth:0,hasRequestedAbove1500:!1,hasRequestedBelow1500:!1,showMode:"showTable",parameters:{},permissions:null,paramsDisable:{akb020:!1,aaz307:!1,aaz263:!1},hosList:[],ksList:[],doctorList:[],dimensionflag:"",helperauthor:{aaz307:"",aaz263:""}}},mounted:function(){var e=this;return(0,o.Z)(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.updateContainerWidth(),e.$route.query.flag&&(e.dimensionflag=e.$route.query.flag),e.$route.query.aaz263&&(e.helperauthor.aaz263=e.$route.query.aaz263),e.$route.query.aaz307&&(e.helperauthor.aaz307=e.$route.query.aaz307),e.helperauthor.aaz263||e.helperauthor.aaz307){t.next=8;break}return t.next=7,k.permissionCheck();case 7:e.permissions=t.sent;case 8:return t.next=10,e.fnQueryHos();case 10:window.addEventListener("resize",e.updateContainerWidth);case 11:case"end":return t.stop()}}),t)})))()},beforeDestroy:function(){window.removeEventListener("resize",this.updateContainerWidth)},computed:{formBoxStyle:function(){return{height:"90px"}}},watch:{containerWidth:function(e){e>1500&&!this.hasRequestedAbove1500?(this.fetchDataForAbove1500(),this.hasRequestedAbove1500=!0,this.hasRequestedBelow1500=!1):e<=1500&&(this.fetchDataForBelow1500(),this.hasRequestedBelow1500=!0,this.hasRequestedAbove1500=!1)}},methods:{fetchDataForAbove1500:function(){var e=this;this.$nextTick((function(){e.fnQuery()}))},fetchDataForBelow1500:function(){var e=this;this.$nextTick((function(){e.fnQuery()}))},updateContainerWidth:function(){this.$refs.container&&(this.containerWidth=this.$refs.container.clientWidth-70)},getComponent:function(e){var t={zbx01:"hospital-bar",zbx02:"hospital-bar",zbx03:"hospital-bar",zbx04:"hospital-bar",zbx05:"hospital-bar",zbx06:"doctor-bar",zbx07:"doctor-bar",zbx08:"hospital-bar"};return t[e]||"default-component"},formatAmount:function(e){if(isNaN(e)||!e)return"";var t=parseFloat(e),i=String(t.toFixed(2)),a=/(-?\d+)(\d{3})/;while(a.test(i))i=i.replace(a,"$1,$2");return i},formatPercentageValue:function(e,t){return e&&0!=e&&t&&0!=t?(e/t*100).toFixed(2)+"%":"0%"},formatAmountDependingOnValue:function(e){return Number.isFinite(e)?e>=1e4?this.formatAmount(e/1e4):0==e?0:this.formatAmount(e):"-"},formatUnit:function(e,t){var i="";return i=["zbx01","zbx02","zbx03"].includes(t)?"元":["zbx04"].includes(t)?"天":["zbx05"].includes(t)?"人次":"%",e>=1e4&&"%"!=i?"万"+i:i},fnQueryHos:function(){var e=this,t={};this.helperauthor.aaz263&&(t={aaz263Set:[this.helperauthor.aaz263]});var i={url:"/miimCommonRead/queryHospRpcList",data:t,autoValid:!0},a={successCallback:function(t){var i,a,n;e.hosList=t.data.resultData,(null===(i=e.permissions)||void 0===i||null===(a=i.akb020Set)||void 0===a?void 0:a.size)>0&&(e.hosList=e.hosList.filter((function(t){return e.permissions.akb020Set.has(t.value)})));var s=null===(n=e.hosList[0])||void 0===n?void 0:n.value;e.$nextTick((function(){e.baseInfoForm.setFieldsValue({akb020:s}),e.akb020=e.helperauthor.aaz263||e.helperauthor.aaz307?"":s,e.fnQueryDept(e.akb020)}))},failCallback:function(t){e.$message.error("医院数据加载失败")}};this.Base.submit(null,i,a)},fnQueryDept:function(e){var t=this;this.akb020=e||"";var i={akb020:this.akb020,hospDeptType:"I"};this.permissions&&this.permissions.aaz263Disable&&(i.aaz263Set=(0,r.Z)(this.permissions.aaz263Set)),this.Base.submit(null,{url:"miimCommonRead/queryDepartRpcList",data:i,autoValid:!1},{successCallback:function(e){t.ksList=e.data.resultData,t.fnQueryDocter()},failCallback:function(e){t.$message.error("科室数据加载失败")}})},fnQueryDocter:function(){var e=this,t={akb020:this.akb020};this.permissions&&this.permissions.aaz307Disable&&(t.departCode=this.permissions.aaz307Set.values().next().value),this.Base.submit(null,{url:"miimCommonRead/queryDoctorRpcList",data:t,autoValid:!1},{successCallback:function(t){e.doctorList=t.data.resultData,e.helperauthor.aaz263||e.helperauthor.aaz307?(e.helperauthor.aaz263&&(e.paramsDisable.aaz263=!0,e.baseInfoForm.setFieldsValue({aaz263:e.helperauthor.aaz263})),e.helperauthor.aaz307&&(e.paramsDisable.aaz307=!0,e.baseInfoForm.setFieldsValue({aaz307:e.helperauthor.aaz307})),e.$nextTick((function(){e.fnQuery()}))):e.setPermission()},failCallback:function(t){e.$message.error("医师数据加载失败")}})},setPermission:function(){var e=this;this.paramsDisable.akb020=this.permissions.akb020Disable,this.paramsDisable.aaz307=this.permissions.aaz307Disable,this.paramsDisable.aaz263=this.permissions.aaz263Disable,this.permissions&&(this.permissions&&this.permissions.aaz307Set.size>0?(this.ksList=this.ksList.filter((function(t){return e.permissions.aaz307Set.has(t.value)})),this.permissions.aaz307Disable&&this.baseInfoForm.setFieldsValue({aaz307:this.ksList[0].value})):this.ksList.length>0&&("ks"===this.dimensionflag||"ys"===this.dimensionflag)&&this.baseInfoForm.setFieldsValue({aaz307:this.ksList[0].value}),this.permissions&&this.permissions.aaz263Set.size>0?(this.doctorList=this.doctorList.filter((function(t){return e.permissions.aaz263Set.has(t.value)})),this.permissions.aaz263Disable&&this.baseInfoForm.setFieldsValue({aaz263:this.doctorList[0].value})):this.doctorList.length>0&&"ys"===this.dimensionflag&&this.baseInfoForm.setFieldsValue({aaz263:this.doctorList[0].value})),this.$nextTick((function(){e.fnQuery()}))},setPopupContainer:function(e){return e.parentNode},fnQueryStatisticsDimension:function(){var e=this;this.Base.submit(null,{url:"indexStatistics/queryStatisticsDimension",data:{}},{successCallback:function(t){e.activeKey=0;var i=[];if("ks"===e.dimensionflag?(e.statisticsDimension=t.data.statisticsDimension.filter((function(e){return"akb020"!==e.value})),i.push("aae386"),e.fnChangeDimension(i)):"ys"===e.dimensionflag?(e.statisticsDimension=t.data.statisticsDimension.filter((function(e){return"akb020"!==e.value&&"aae386"!==e.value})),i.push("aaz570"),e.fnChangeDimension(i)):e.statisticsDimension=t.data.statisticsDimension,e.statisticsIndex=t.data.statisticsIndex,e.statisticsIndex.length>0){for(var a=0;a<e.statisticsIndex.length;a++)a<10&&(e.selectedIndexItemList.push(e.statisticsIndex[a]),e.defaultCheckedIndex.push(e.statisticsIndex[a].value));e.checkedIndexList=e.defaultCheckedIndex,e.baseInfoForm.setFieldsValue({statisticsIndex:e.defaultCheckedIndex,statisticsDimension:i})}},failCallback:function(t){e.$message.error("统计维度或指标加载失败")}})},disabledStartDate:function(e){return e.format("YYYYMMDD")>this.baseInfoForm.getFieldValue("endDate").format("YYYYMMDD")},disabledEndDate:function(e){return e=e.format("YYYYMMDD"),e>u()().startOf("day").format("YYYYMMDD")||e<this.baseInfoForm.getFieldValue("startDate").format("YYYYMMDD")},infoPageParams:function(){var e=this.baseInfoForm.getFieldsValue();if(e.allDate){if(e.aae043=e.allDate.format("YYYYMM"),e.akb020)return e.aae140=e.aae140||"999",e.dimensionflag=this.dimensionflag,this.parameters=e,e;this.$message.error("必须选取医院！")}else this.$message.error("必须选取月份！")},fnChangeDimension:function(e){var t=this;"ks"!==this.dimensionflag||e.includes("aae386")||(e.push("aae386"),this.$message.warn("必须选择科室维度")),"ys"!==this.dimensionflag||e.includes("aaz570")||(e.push("aaz570"),this.$message.warn("必须选择医师维度")),this.selectedDimension=e;for(var i=this.$refs.infoTableRef.getTableColumn().collectColumn,a=i.splice(1,this.statisticsDimension.length),n=0;n<e.length;n++)for(var o=0;o<a.length;o++)e[n]===a[o].own.field&&(a[n]=a.splice(o,1,a[n])[0]);i.splice.apply(i,[1,0].concat((0,r.Z)(a))),this.$refs.infoTableRef.loadColumn(i),this.selectedDimensionItemList=[];var l,c=(0,s.Z)(this.selectedDimension);try{var u=function(){var e=l.value,i=t.statisticsDimension.filter((function(t){return t.value==e}));t.selectedDimensionItemList.push(i[0])};for(c.s();!(l=c.n()).done;)u()}catch(d){c.e(d)}finally{c.f()}this.parameters.selectedDimensionItemList=this.selectedDimensionItemList,this.sortColumn="",this.ascOrDesc="",this.$nextTick((function(){t.fnQuery()}))},IsInArray:function(e,t){var i=","+e.join(",")+",";return-1!=i.indexOf(","+t+",")},fnQuery:function(){var e=this;this.baseInfoForm.validateFields((function(t){if(!t){var i=e.infoPageParams();e.Base.submit(null,{url:"indexWarning/queryIndexwarningInfoList",data:i,autoValid:!1},{successCallback:function(t){var i=t.data;i.indexwarningList.length>0?e.overallIndicators=i.indexwarningList:e.overallIndicators=[];var a={zbx01:"fnBuildChart",zbx02:"fnBuildChartLine",zbx03:"fnBuildChartBar",zbx04:"fnBuildChart",zbx05:"fnBuildChartBar",zbx06:"fnBuildChartBar",zbx07:"fnBuildChartBar",zbx08:"fnBuildChartBar"};if(i.indexwarningEcharts.length>0){var n=i.indexwarningEcharts.reduce((function(e,t){return e[t.indexcode]||(e[t.indexcode]=[]),e[t.indexcode].push(t),e}),{});e.groupedData=n,e.$nextTick((function(){Object.keys(a).forEach((function(t){n.hasOwnProperty(t)&&e.overallIndicators.map((function(e){return e.indexcode})).includes(t)&&e.$refs["".concat(t,"BarRef")][0][a[t]](n[t])}))}))}},failCallback:function(t){e.$message.error("总体指标及图表数据加载失败")}})}}))},fnReSet:function(){this.baseInfoForm.resetFields(["aae140"])},fnCustomHeaderCell:function(e){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px",fontWeight:"bold"}}},filterOption:function(e,t){return t.componentOptions.children[0].text.indexOf(e)>=0}}},P=R,Z=(0,x.Z)(P,a,n,!1,null,"088f0082",null),W=Z.exports},55382:function(){},61219:function(){}}]);
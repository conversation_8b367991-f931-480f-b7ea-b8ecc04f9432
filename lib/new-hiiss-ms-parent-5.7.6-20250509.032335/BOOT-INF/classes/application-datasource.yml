ta404:
  datasource:
    datasource-list: ta404ds,syncds,mttPreload
    type: druid
    druid:
      ta404ds:
        datasource:
          #          url: *********************************************************************************************************************************
          #          url: *******************************************************************************************************************************
          #          username: root
          #          password: yh$3641
          #          url: *************************************
          #          username: root
          #          password: yh$3641
          driver-class-name: org.postgresql.Driver #可不写
          #          url: *********************************************************************************************************************************************************
          #          username: postgres
          #          password: postgres
          url: ************************************************************************************************************************************************
          username: ylfwzb_rw
          password: ENC(kXIkmEHNZb0ezlmV8P1PBuaoC7MflsnbGemICAOiFQ/yqfYaLU4z6I0LsRmbU3mQt32r0dcoJGVdaXly7289aw==)
          # 初始化连接数
          initial-size: 3
          # 连接池最大连接数量
          max-active: 10
          # 获取链接超时时间 （毫秒）
          max-wait: 60000
        mybatis:
          mapper-locations:
            - classpath*:mapper/read/*.xml
            - classpath*:mapper/write/*.xml
            - classpath*:mapper/**.xml
            - classpath*:mapper/miim/common/read/*.xml
            - classpath*:mapper/miim/common/write/*.xml
            - classpath*:mapper/client/**/read/*.xml
            - classpath*:mapper/client/**/write/*.xml
            - classpath*:mapper/audit/systemmanage/outpatientClinic/read/*.xml
            - classpath*:mapper/audit/systemmanage/outpatientClinic/write/*.xml
            - classpath*:mapper/audit/querycommon/**/write/*.xml
            - classpath*:mapper/audit/querycommon/**/read/*.xml
            - classpath*:mapper/hisshow/dischargeHis/**/write/*.xml
            - classpath*:mapper/hisshow/dischargeHis/**/read/*.xml
            - classpath*:mapper/analysis/**/read/*.xml
            - classpath*:mapper/analysis/**/write/*.xml
            - classpath*:mapper/eng/**/read/*.xml
            - classpath*:mapper/eng/**/write/*.xml
            - classpath*:mapper/costcontrol/**/read/*.xml
            - classpath*:mapper/costcontrol/**/write/*.xml
            - classpath*:mapper/indexMonitor/**/write/*.xml
            - classpath*:mapper/indexMonitor/**/read/*.xml
            - classpath*:mapper/indexWarnManage/**/read/*.xml
            - classpath*:mapper/indexWarnManage/**/write/*.xml
            - classpath*:mapper/ta404/**/**.xml
            - classpath*:mapper/mtt/**/read/*.xml
            - classpath*:mapper/mtt/**/write/*.xml
            - classpath*:mapper/fjzj/ylfwzb/**.xml
            - classpath*:mapper/ommanage/**/read/*.xml
            - classpath*:mapper/individualDemand/**/read/*.xml
            - classpath*:mapper/individualDemand/**/write/*.xml
            - classpath*:mapper/hisshow/audit/read/BackendAuditReadMapper.xml
            - classpath*:mapper/hisshow/audit/write/BackendAuditWriteMapper.xml
            - classpath*:mapper/hospDataManage/**/read/*.xml
            - classpath*:mapper/hospDataManage/**/write/*.xml
            - classpath*:mapper/whiteList/**/read/*.xml
            - classpath*:mapper/whiteList/**/write/*.xml
            - classpath*:mapper/codematch/read/*.xml
            - classpath*:mapper/codematch/write/*.xml
            - classpath*:mapper/doctorWork/read/*.xml
            - classpath*:mapper/doctorWork/write/*.xml
            - classpath*:mapper/interfaceToDb/**/*.xml
            - classpath*:mapper/hnIndexParameter/*.xml
            - classpath*:mapper/smtrpt/**/*.xml
            - classpath*:mapper/monitorManage/read/*.xml
            - classpath*:mapper/monitorManage/write/*.xml
          base-packages:
            - com.yinhai.ta404.module.mybatis.mapper
            - com.yinhai.ta404.module.**.mapper.read
            - com.yinhai.ta404.module.**.mapper.write
            - com.yinhai.ta404.component.**.mapper.read
            - com.yinhai.ta404.component.**.mapper.write
            - com.yinhai.hiiss.audit.**.mapper.read
            - com.yinhai.hiiss.audit.**.mapper.write
            - com.yinhai.hiiss.analysis.**.mapper.read
            - com.yinhai.hiiss.analysis.**.mapper.write
            - com.yinhai.hiiss.client.**.mapper.read
            - com.yinhai.hiiss.client.**.mapper.write
            - com.yinhai.miim.common.**.mapper.read
            - com.yinhai.miim.common.**.mapper.write
            - com.yinhai.miim.audit.systemmanage.**.mapper.read
            - com.yinhai.miim.audit.systemmanage.**.mapper.write
            - com.yinhai.hiiss.audit.querycommon.**.mapper.read
            - com.yinhai.hiiss.audit.querycommon.**.mapper.write
            - com.yinhai.hiiss.hisshow.dischargeHis.**.mapper.read
            - com.yinhai.hiiss.hisshow.dischargeHis.**.mapper.write
            - com.yinhai.hiiss.eng.**.mapper.read
            - com.yinhai.hiiss.eng.**.mapper.write
            - com.yinhai.hiiss.codematch.mapper.read
            - com.yinhai.hiiss.codematch.mapper.write
            - com.yinhai.hiiss.doctorWork.mapper.read
            - com.yinhai.hiiss.doctorWork.mapper.write
            - com.yinhai.hiiss.indexWarnManage.**.mapper.read
            - com.yinhai.hiiss.indexWarnManage.**.mapper.write
            - com.yinhai.hamms.statistics.**.mapper.ta404
            - com.yinhai.hiiss.mtt.**.mapper.read
            - com.yinhai.hiiss.mtt.**.mapper.write
            - com.yinhai.hiiss.fjzj.ylfwzb.mapper.read
            - com.yinhai.hiiss.costcontrol.**.mapper.read
            - com.yinhai.hiiss.costcontrol.**.mapper.write
            - com.yinhai.hiiss.indexMonitor.**.mapper.read
            - com.yinhai.hiiss.indexMonitor.**.mapper.write
            - com.yinhai.hiiss.webservice.mapper.read
            - com.yinhai.hiiss.webservice.mapper.write
            - com.yinhai.hiiss.ommanage.**.mapper.read
            - com.yinhai.hiiss.individualDemand.**.mapper.read
            - com.yinhai.hiiss.individualDemand.**.mapper.write
            - com.yinhai.hiiss.hisshow.audit.mapper.read
            - com.yinhai.hiiss.hisshow.audit.mapper.write
            - com.yinhai.hiiss.hospDataManage.**.mapper.read
            - com.yinhai.hiiss.hospDataManage.**.mapper.write
            - com.yinhai.hiiss.whiteList.**.mapper.read
            - com.yinhai.hiiss.whiteList.**.mapper.write
            - com.yinhai.hiiss.interfaceToDb.**.mapper.read
            - com.yinhai.hiiss.interfaceToDb.**.mapper.write
            - com.yinhai.hiiss.hnIndexParameter.mapper
            - com.yinhai.hiiss.smtrpt.**.mapper.write
            - com.yinhai.hiiss.monitorManage.mapper.read
            - com.yinhai.hiiss.monitorManage.mapper.write
      syncds:
        datasource:
          #          url: ${ta404.datasource.druid.ta404ds.datasource.url}
          #          url: ********************************************************************************************************************************
          #          url: *******************************************************************************************************************************
          url: ********************************************************************************************************************************
          #          username: ${ta404.datasource.druid.ta404ds.datasource.username}
          #          password: ${ta404.datasource.druid.ta404ds.datasource.password}
          username: audit_his_dev
          password: hiiss@v5
          #          driver-class-name: org.postgresql.Driver #可不写
          # 初始化连接数
          initial-size: 3
          # 连接池最大连接数量
          max-active: 10
          # 获取链接超时时间 （毫秒）
          max-wait: 60000
        mybatis:
          mapper-locations:
            - classpath*:mapper/hisshow/billing/**/write/*.xml
            - classpath*:mapper/hisshow/billing/**/read/*.xml
            - classpath*:mapper/hisshow/doctorOrder/**/write/*.xml
            - classpath*:mapper/hisshow/doctorOrder/**/read/*.xml
            - classpath*:mapper/hisshow/opsp/**/write/*.xml
            - classpath*:mapper/hisshow/opsp/**/read/*.xml
            - classpath*:mapper/hisshow/opsp/**/read/*.xml
            - classpath*:mapper/hisshow/opsp/**/read/*.xml
            - classpath*:mapper/hisshow/audit/**/his/BackendAuditHisReadMapper.xml
            - classpath*:mapper/client/**/hisRead/*.xml
            - classpath*:mapper/client/**/hisWrite/*.xml
          base-packages:
            - com.yinhai.hiiss.hisshow.billing.**.mapper.read
            - com.yinhai.hiiss.hisshow.billing.**.mapper.write
            - com.yinhai.hiiss.hisshow.doctorOrder.**.mapper.read
            - com.yinhai.hiiss.hisshow.doctorOrder.**.mapper.write
            - com.yinhai.hiiss.hisshow.opsp.**.mapper.read
            - com.yinhai.hiiss.hisshow.opsp.**.mapper.write
            - com.yinhai.hiiss.hisshow.audit.mapper.his
            - com.yinhai.hiiss.client.**.mapper.hisRead
            - com.yinhai.hiiss.client.**.mapper.hisWrite

      fjzj:
        datasource:
          #          url: *************************************************************************************************************************************
          #          username: root
          #          password: nopassword
          url: ${ta404.datasource.druid.ta404ds.datasource.url}
          username: ${ta404.datasource.druid.ta404ds.datasource.username}
          password: ${ta404.datasource.druid.ta404ds.datasource.password}
          # 初始化连接数
          initial-size: 3
          # 连接池最大连接数量
          max-active: 10
          # 获取链接超时时间 （毫秒）
          max-wait: 60000
        mybatis:
          mapper-locations:
            ## 报告模块
            - classpath*:mapper/reportcommon/read/**/*.xml
            - classpath*:mapper/reportcommon/write/**/*.xml
            - classpath*:mapper/autoreport/read/**/*.xml
            - classpath*:mapper/autoreport/write/**/*.xml
            ## 飞检自检模块
            - classpath*:mapper/fjzj/**/read/*.xml
            - classpath*:mapper/fjzj/**/write/*.xml
          base-packages:
            ## 报告
            - com.yinhai.hiiss.bgsysback.**.mapper.read
            - com.yinhai.hiiss.bgsysback.**.mapper.write
            ## 飞检自检
            - com.yinhai.hiiss.fjzj.**.mapper.read
            - com.yinhai.hiiss.fjzj.**.mapper.write
#     规则预上新功能中间库
      mttPreload:
        datasource:
          driver-class-name: org.postgresql.Driver
          url: ***************************************************************************************************************************************
          username: ylfwzb_rw
          password: Hi94P9e*u89cG%*!GeB%
          initial-size: 3
          max-active: 10
          max-wait: 60000
        mybatis:
          mapper-locations:
            - classpath*:mapper/mttpreloadread/*.xml
            - classpath*:mapper/monitorManage/preRule/read/*.xml
          base-packages:
            - com.yinhai.hiiss.mtt.**.mapper.mttpreloadread
            - com.yinhai.hiiss.monitorManage.mapper.preRule.read

--kf10聚集
CREATE OR REPLACE FUNCTION analysis_ipt_kf10_gather(startDate text, endDate text)
    returns table
            (
                akc190 varchar,
                aaz213 bigint,
                aaz307 varchar,
                aae386 varchar,
                aaz263 varchar,
                aac003 varchar,
                ake001 varchar,
                ake002 varchar,
                akb065 numeric,
                akc226 numeric,
                ape804 numeric,
                ape805 numeric,
                ykc610 varchar,
                aae040 timestamp,
                aaz319 bigint,
                aaa167 varchar
            )
    LANGUAGE plpgsql
as
$$
DECLARE
    -- 总金额
    total_amount numeric;
    --总次数
    total_count  int8;
    enableCfgNo  text;
BEGIN
    select string_agg(aa01.aaz499::VARCHAR, ',')
    into enableCfgNo
    from aa01
    where aa01.aaz499 in ('3', '4', '5', '6', '7', '18')
      and upper(aa01.aaa005) = 'Y';
    --     创建临时表存储所有场景按照aaz213聚合后的kf10数据

    return query
        select
            drkf10.akc190,
            drkf10.aaz213,
            drkf10.aaz307,
            drkf10.aae386,
            drkf10.aaz263,
            drkf10.aac003,
            drkf10.ake001,
            drkf10.ake002,
            drkf10.akb065,
            drkf10.akc226,
            drkf10.ape804,
            drkf10.ape805,
            drkf10.ykc610,
            drkf10.aae040,
            drkf10.aaz319,
            drkf10.aaa167
        from dsc_newest newest
                 right join ipt_drdscg_kf10 drkf10 on newest.aae500 = '4' and newest.aaz217 = drkf10.aaz217--医生站出院
        where enableCfgNo like '%7%'
          and newest.aae040  >= to_date(startDate, 'yyyy-MM-dd') and newest.aae040  < (to_date(endDate, 'yyyy-MM-dd') + INTERVAL '1 day')
          and newest.aae040 < CURRENT_DATE
        union all
        select nurkf10.akc190,
               nurkf10.aaz213,
               nurkf10.aaz307,
               nurkf10.aae386,
               nurkf10.aaz263,
               nurkf10.aac003,
               nurkf10.ake001,
               nurkf10.ake002,
               nurkf10.akb065,
               nurkf10.akc226,
               nurkf10.ape804,
               nurkf10.ape805,
               nurkf10.ykc610,
               nurkf10.aae040,
               nurkf10.aaz319,
               nurkf10.aaa167
        from dsc_newest newest
                 right join ipt_nurdscg_kf10 nurkf10 on newest.aae500 = '7' and newest.aaz217 = nurkf10.aaz217---护士站出院
        where enableCfgNo like '%6%'
          and newest.aae040  >= to_date(startDate, 'yyyy-MM-dd') and newest.aae040  < (to_date(endDate, 'yyyy-MM-dd') + INTERVAL '1 day')
          and newest.aae040 < CURRENT_DATE
        union all
        select hidkf10.akc190,
               hidkf10.aaz213,
               hidkf10.aaz307,
               hidkf10.aae386,
               hidkf10.aaz263,
               hidkf10.aac003,
               hidkf10.ake001,
               hidkf10.ake002,
               hidkf10.akb065,
               hidkf10.akc226,
               hidkf10.ape804,
               hidkf10.ape805,
               hidkf10.ykc610,
               hidkf10.aae040,
               hidkf10.aaz319,
               hidkf10.aaa167
        from dsc_newest newest
                 right join ipt_hiddscg_kf10 hidkf10 on newest.aae500 = '5' and newest.aaz217 = hidkf10.aaz217--医保办出院
        where enableCfgNo like '%5%'
          and newest.aae040  >= to_date(startDate, 'yyyy-MM-dd') and newest.aae040  < (to_date(endDate, 'yyyy-MM-dd') + INTERVAL '1 day')
          and newest.aae040 < CURRENT_DATE
        union all
        select drrefkf10.akc190,
               drrefkf10.aaz213,
               drrefkf10.aaz307,
               drrefkf10.aae386,
               drrefkf10.aaz263,
               drrefkf10.aac003,
               drrefkf10.ake001,
               drrefkf10.ake002,
               drrefkf10.akb065,
               drrefkf10.akc226,
               drrefkf10.ape804,
               drrefkf10.ape805,
               drrefkf10.ykc610,
               drrefkf10.aae040,
               drrefkf10.aaz319,
               drrefkf10.aaa167
        from dsc_newest newest
                 right join ipt_drrefldept_kf10 drrefkf10 on newest.aae500 = '12' and newest.aaz217 = drrefkf10.aaz217--医生站转科出院
        where enableCfgNo like '%18%'
          and newest.aae040  >= to_date(startDate, 'yyyy-MM-dd') and newest.aae040  < (to_date(endDate, 'yyyy-MM-dd') + INTERVAL '1 day')
          and newest.aae040 < CURRENT_DATE
        union all
        select nurefkf10.akc190,
               nurefkf10.aaz213,
               nurefkf10.aaz307,
               nurefkf10.aae386,
               nurefkf10.aaz263,
               nurefkf10.aac003,
               nurefkf10.ake001,
               nurefkf10.ake002,
               nurefkf10.akb065,
               nurefkf10.akc226,
               nurefkf10.ape804,
               nurefkf10.ape805,
               nurefkf10.ykc610,
               nurefkf10.aae040,
               nurefkf10.aaz319,
               nurefkf10.aaa167
        from dsc_newest newest
                 right join ipt_nurrefldept_kf10 nurefkf10 on newest.aae500 = '13' and newest.aaz217 = nurefkf10.aaz217--护士站转科出院
        where enableCfgNo like '%18%'
          and newest.aae040  >= to_date(startDate, 'yyyy-MM-dd') and newest.aae040  < (to_date(endDate, 'yyyy-MM-dd') + INTERVAL '1 day')
          and newest.aae040 < CURRENT_DATE
        union all
        select drkf10.akc190,
               drkf10.aaz213,
               drkf10.aaz307                as aaz307,
               drkf10.aae386                as aae386,
               drkf10.aaz263                as aaz263,
               drkf10.aac003                as aac003,
               drkf10.ake001                as ake001,
               drkf10.ake002                as ake002,
               coalesce(drkf10.akb065, 0.0) as akb065,
               coalesce(drkf10.akc226, 0.0) as akc226,
               coalesce(drkf10.akb065, 0.0) as ape804,
               coalesce(drkf10.akc226, 0.0) as ape805,
               drkf10.ykc610                as ykc610,
               drkf10.aae040                as aae040,
               drkf10.aaz319                as aaz319,
               drkf10.aaa167                as aaa167
        from ipt_drord_kf10 drkf10
        where enableCfgNo like '%3%'
          and drkf10.aae040  >= to_date(startDate, 'yyyy-MM-dd') and drkf10.aae040  < (to_date(endDate, 'yyyy-MM-dd') + INTERVAL '1 day')
          and drkf10.aae040 < CURRENT_DATE

        union all
        select nurfeekf10.akc190,
               nurfeekf10.aaz213,
               nurfeekf10.aaz307                as aaz307,
               nurfeekf10.aae386                as aae386,
               nurfeekf10.aaz263                as aaz263,
               nurfeekf10.aac003                as aac003,
               nurfeekf10.ake001                as ake001,
               nurfeekf10.ake002                as ake002,
               coalesce(nurfeekf10.akb065, 0.0) as akb065,
               coalesce(nurfeekf10.akc226, 0.0) as akc226,
               coalesce(nurfeekf10.ape804, 0.0) as ape804,
               coalesce(nurfeekf10.ape805, 0.0) as ape805,
               nurfeekf10.ykc610                as ykc610,
               nurfeekf10.aae040                as aae040,
               nurfeekf10.aaz319                as aaz319,
               nurfeekf10.aaa167                as aaa167
        from ipt_nurfee_kf10 nurfeekf10
        where enableCfgNo like '%4%'
          and nurfeekf10.aae040  >= to_date(startDate, 'yyyy-MM-dd') and nurfeekf10.aae040  < (to_date(endDate, 'yyyy-MM-dd') + INTERVAL '1 day')
          and nurfeekf10.aae040 < CURRENT_DATE;

end
$$;


--kf10聚集
CREATE OR REPLACE FUNCTION analysis_otp_kf10_gather(startDate text, endDate text)
    returns table
            (
                akc190 varchar,
                aaz213 bigint,
                aaz307 varchar,
                aae386 varchar,
                aaz263 varchar,
                aac003 varchar,
                ake001 varchar,
                ake002 varchar,
                akb065 numeric,
                akc226 numeric,
                ape804 numeric,
                ape805 numeric,
                ykc610 varchar,
                aae040 timestamp,
                aaz319 bigint,
                aaa167 varchar
            )
    LANGUAGE plpgsql
as
$$
DECLARE
    -- 总金额
    total_amount numeric;
    --总次数
    total_count  int8;
    enableCfgNo  text;
BEGIN
    select string_agg(aa01.aaz499::VARCHAR, ',')
    into enableCfgNo
    from aa01
    where aa01.aaz499 in ('2', '16')
      and upper(aa01.aaa005) = 'Y';
    --     创建临时表存储所有场景按照aaz213聚合后的kf10数据

    return query
        select otpkf10.akc190,
               otpkf10.aaz213,
               otpkf10.aaz307                     as aaz307,
               otpkf10.aae386                     as aae386,
               otpkf10.aaz263                     as aaz263,
               otpkf10.aac003                     as aac003,
               otpkf10.ake001                     as ake001,
               otpkf10.ake002                     as ake002,
               coalesce(kc19.akb065, 0.0)         as akb065,
               coalesce(kc19.akc226, 0.0)         as akc226,
               coalesce(kc19.akb065, 0.0)         as ape804,
               coalesce(kc19.akc226, 0.0)         as ape805,
               otpkf10.ykc610                     as ykc610,
               otpkf10.aae040                     as aae040,
               otpkf10.aaz319                     as aaz319,
               otpkf10.aaa167                     as aaa167
        from otp_kf10 otpkf10
                 left join otp_kc19 kc19 on otpkf10.aaz213 = kc19.aaz213
        where enableCfgNo like '%2%'
          and otpkf10.aae040  >= to_date(startDate, 'yyyy-MM-dd') and otpkf10.aae040  < (to_date(endDate, 'yyyy-MM-dd') + interval '1 day')
          and otpkf10.aae040 < current_date
        union all
        select opspkf10.akc190,
               opspkf10.aaz213,
               opspkf10.aaz307                      as aaz307,
               opspkf10.aae386                      as aae386,
               opspkf10.aaz263                      as aaz263,
               opspkf10.aac003                      as aac003,
               opspkf10.ake001                      as ake001,
               opspkf10.ake002                      as ake002,
               coalesce(opspkc16.akb065, 0.0)       as akb065,
               coalesce(opspkc16.akc226, 0.0)       as akc226,
               coalesce(opspkc16.akb065, 0.0)       as ape804,
               coalesce(opspkc16.akc226, 0.0)       as ape805,
               opspkf10.ykc610                      as ykc610,
               opspkf10.aae040                      as aae040,
               opspkf10.aaz319                      as aaz319,
               opspkf10.aaa167                       as aaa167
        from opsp_kf10 opspkf10
                 left join opsp_kc16 opspkc16 on opspkf10.aaz213 = opspkc16.aaz213
        where enableCfgNo like '%16%'
          and opspkf10.aae040  >= to_date(startDate, 'yyyy-MM-dd') and opspkf10.aae040  < (to_date(endDate, 'yyyy-MM-dd') + interval '1 day')
          and opspkf10.aae040 < current_date;

end
$$;

--kf10聚集
CREATE OR REPLACE FUNCTION analysis_kf10_chose_scene(startDate text, endDate text, ipt_or_otp text)
    returns table
            (
                akc190 varchar,
                aaz213 bigint,
                aaz307 varchar,
                aae386 varchar,
                aaz263 varchar,
                aac003 varchar,
                ake001 varchar,
                ake002 varchar,
                akb065 numeric,
                akc226 numeric,
                ape804 numeric,
                ape805 numeric,
                ykc610 varchar,
                aae040 timestamp,
                aaz319 bigint,
                aaa167 varchar
            )
    LANGUAGE plpgsql
as
$$
DECLARE
BEGIN
    if ipt_or_otp = 'ipt' then
        return query select * from analysis_ipt_kf10_gather(startdate, enddate);
    end if;
    if ipt_or_otp = 'otp' then
        return query select * from analysis_otp_kf10_gather(startdate, enddate);
    end if;
    if ipt_or_otp = 'all' then
        return query
            select *
            from analysis_otp_kf10_gather(startdate, enddate)
            union all
            select *
            from analysis_ipt_kf10_gather(startdate, enddate);
    end if;

END
$$;

--kf10通过aaz213去重
CREATE OR REPLACE FUNCTION analysis_kf10_groupby_aaz213(startDate text, endDate text, ipt_or_otp text)
    returns table
            (
                aaz213 bigint,
                akc190 text,
                aaz307 text,
                aae386 text,
                aaz263 text,
                aac003 text,
                ake001 text,
                ake002 text,
                akb065 numeric,
                akc226 numeric,
                ape804 numeric,
                ape805 numeric,
                ykc610 text,
                aae040 timestamp,
                aaz319 text,
                aaa167 text
            )
    LANGUAGE plpgsql
as
$$
DECLARE
BEGIN
    return query select tbRow.aaz213,
                        max(tbRow.akc190)                      as akc190,
                        max(tbRow.aaz307)                      as aaz307,
                        max(tbRow.aae386)                      as aae386,
                        max(tbRow.aaz263)                      as aaz263,
                        max(tbRow.aac003)                      as aac003,
                        max(tbRow.ake001)                      as ake001,
                        max(tbRow.ake002)                      as ake002,
                        max(coalesce(tbRow.akb065, 0.0))       as akb065,
                        max(coalesce(tbRow.akc226, 0.0))       as akc226,
                        max(coalesce(tbRow.ape804, 0.0))       as ape804,
                        max(coalesce(tbRow.ape805, 0.0))       as ape805,
                        max(tbRow.ykc610)                      as ykc610,
                        max(tbRow.aae040)                      as aae040,
                        string_agg(tbRow.aaz319::varchar, ',') as aaz319,
                        string_agg(tbRow.aaa167::varchar, ',') as aaa167
                 from analysis_kf10_chose_scene(startdate, enddate,ipt_or_otp) tbRow
                 group by tbRow.aaz213;
END
$$;

--------------------------------------------------------------------------------------------

--室违规情况分析
CREATE OR REPLACE FUNCTION analysis_dept(startDate text, endDate text, ipt_or_opt text)
    returns table
            (
                aaz307               text,
                aae386               text,
                doubt_amount         numeric,
                doubt_amount_percent text,
                doubt_count          bigint,
                doubt_count_percent  text
            )
    LANGUAGE plpgsql
as
$$
DECLARE
    -- 总金额
    total_amount numeric;
    --总次数
    total_count  int8;
BEGIN

    DROP TABLE IF EXISTS temp_analysis_result;
    CREATE TEMP TABLE IF NOT EXISTS temp_analysis_result AS
    SELECT *
    FROM analysis_kf10_groupby_aaz213(startDate, endDate, ipt_or_opt);

    --汇总数据到变量
    select sum(u_kf10.ape804), count(u_kf10.aaz213)
    into total_amount,total_count
    from temp_analysis_result u_kf10;

    return query
        select u_kf10.aaz307,
               max(u_kf10.aae386),
               sum(u_kf10.ape804),
               concat(coalesce(ROUND((sum(u_kf10.ape804) * 100.0 / NULLIF(total_amount, 0)), 2), 0)::varchar, '%'),
               count(u_kf10.aaz213),
               concat(coalesce(ROUND((count(u_kf10.aaz213) * 100.0 / NULLIF(total_count, 0)), 2), 0)::varchar, '%')
        from temp_analysis_result u_kf10
        group by u_kf10.aaz307
        order by sum(u_kf10.ape804) desc, count(u_kf10.aaz213) desc, u_kf10.aaz307 desc;

    -- 删除临时表
    DROP TABLE IF EXISTS temp_analysis_result;

END;
$$;

--室违规情况分析-汇总
-- 3.1.2.	科室违规情况分析-统计文字
CREATE OR REPLACE FUNCTION analysis_dept_remark(startDate text, endDate text, ipt_or_otp text)
    returns table
            (
                dept_count int8,
                rank_names text,
                top1name text,
                top2name text,
                top3name text
            )
    LANGUAGE plpgsql
as
$$
DECLARE
    -- 总金额
    total_amount numeric;
    --总次数
    total_count  int8;
    aae386_names text := '';
    topNames      text[];
    rec          record;
BEGIN
    total_count := 0;
    for rec in select * from analysis_dept(startDate, endDate, ipt_or_otp) func_tb order by func_tb.doubt_amount desc
        loop
            total_count := total_count + 1;
            IF total_count <= 3 THEN
                aae386_names = concat(aae386_names, rec.aae386, ',');
                topNames[total_count] = rec.aae386;
            end if;
        END loop;
    IF char_length(aae386_names) > 0 THEN
        aae386_names = substr(aae386_names, 0, char_length(aae386_names));
    end if;

    return query
        select total_count, aae386_names,topNames[1],topNames[2],topNames[3];
end
$$;

-- 3.1.2.	科室违规情况分析-排名
CREATE OR REPLACE FUNCTION analysis_dept_rank_detail(startDate text, endDate text, rank_no int8, ipt_or_otp text)
    returns table
            (
                ake001         text,
                ake002         text,
                ape804         numeric,
                ape804_percent text,
                item_count     numeric,
                amount_max_dr  text,
                count_max_dr   text
            )
    LANGUAGE plpgsql
as
$$
DECLARE
    -- 总金额
    total_amount  numeric;
    --总次数
    target_aaz307 text;
    aae386_names  text := '';
    rec           record;
BEGIN
    DROP TABLE IF EXISTS rank_analysis_temp;
    DROP TABLE IF EXISTS item_dr_temp;
    --写入临时表
    CREATE TEMP TABLE rank_analysis_temp as
    select * from analysis_kf10_groupby_aaz213(startDate, endDate, ipt_or_otp);

    --查询出指定的科室编码到 target_aaz307 中
    select u_kf10.aaz307, sum(u_kf10.ape804)
    into target_aaz307,total_amount
    from rank_analysis_temp u_kf10
    group by u_kf10.aaz307
    order by sum(u_kf10.ape804) desc, count(u_kf10.aaz213) desc, u_kf10.aaz307 desc
    limit 1 offset rank_no - 1;

    --删除非指定科室数据
    delete from rank_analysis_temp where target_aaz307 is null or aaz307 != target_aaz307;


    create temp table item_dr_temp as
    select item_dr_tb.ake001,
           item_dr_tb.aaz263,
           max(item_dr_tb.ake002)   as ake002,
           max(item_dr_tb.aac003)   as aac003,
           sum(item_dr_tb.ape804)   as ape804,
           count(item_dr_tb.aaz213) as item_count
    from rank_analysis_temp item_dr_tb
    group by item_dr_tb.ake001, item_dr_tb.aaz263;

    return query
        select item_tb.ake001,
               max(item_tb.ake002)                          as ake002,
               sum(item_tb.ape804)                          as ape804,
               concat(coalesce(round(sum(item_tb.ape804) * 100 / NULLIF(total_amount, 0), 2), 0)::varchar,
                      '%')                                  as ape804_percent,
               sum(item_tb.item_count)                      as item_count,
               (select string_agg(aac003, ',')
                from item_dr_temp amount_max_tb
                where amount_max_tb.ake001 = item_tb.ake001
                  and amount_max_tb.ape804 >= (select amount_max_tb_inner.ape804
                                               from item_dr_temp amount_max_tb_inner
                                               where amount_max_tb_inner.ake001 = item_tb.ake001
                                               order by amount_max_tb_inner.ape804 desc
                                               limit 1))    as amount_max_dr,
               (select string_agg(aac003, ',')
                from item_dr_temp count_max_tb
                where count_max_tb.ake001 = item_tb.ake001
                  and count_max_tb.item_count >= (select count_max_tb_inner.item_count
                                                  from item_dr_temp count_max_tb_inner
                                                  where count_max_tb_inner.ake001 = item_tb.ake001
                                                  order by count_max_tb_inner.item_count desc
                                                  limit 1)) as count_max_dr
        from item_dr_temp item_tb
        group by item_tb.ake001
        order by sum(item_tb.ape804) desc;
    DROP TABLE IF EXISTS item_dr_temp;
    DROP TABLE IF EXISTS rank_analysis_temp;
end
$$;

---------------------------------------------------------------------------------------------

--3.1.3-项目违规统计
CREATE OR REPLACE FUNCTION analysis_item(startDate text, endDate text, ipt_or_otp text)
    returns table
            (
                ake001               text,
                ake002               text,
                doubt_amount         numeric,
                doubt_amount_percent text,
                doubt_count          bigint,
                doubt_count_percent  text
            )
    LANGUAGE plpgsql
as
$$
DECLARE
    -- 总金额
    total_amount numeric;
    --总次数
    total_count  int8;
BEGIN
    -- 删除临时表
    DROP TABLE IF EXISTS temp_analysis_result_item;
    CREATE TEMP TABLE IF NOT EXISTS temp_analysis_result_item AS
    SELECT *
    FROM analysis_kf10_groupby_aaz213(startDate, endDate, ipt_or_otp);

    --汇总数据到变量
    select sum(u_kf10.ape804), count(u_kf10.aaz213)
    into total_amount,total_count
    from temp_analysis_result_item u_kf10;

    return query
        select u_kf10.ake001,
               max(u_kf10.ake002),
               sum(u_kf10.ape804),
               concat(coalesce(ROUND((sum(u_kf10.ape804) * 100.0 / nullif(total_amount, 0)), 2), 0)::varchar, '%'),
               count(u_kf10.aaz213),
               concat(coalesce(ROUND((count(u_kf10.aaz213) * 100.0 / nullif(total_count, 0)), 2), 0)::varchar, '%')
        from temp_analysis_result_item u_kf10
        group by u_kf10.ake001
        order by sum(u_kf10.ape804) desc, count(u_kf10.aaz213) desc, u_kf10.ake001 desc;

    -- 删除临时表
    DROP TABLE IF EXISTS temp_analysis_result_item;

END;
$$;

-- 3.1.3-项目违规统计统计文字
CREATE OR REPLACE FUNCTION analysis_item_remark(startDate text, endDate text, ipt_or_otp text)
    returns table
            (
                dept_count int8,
                rank_names text,
                top1name text,
                top2name text,
                top3name text
            )
    LANGUAGE plpgsql
as
$$
DECLARE
    -- 总金额
    total_amount numeric;
    --总次数
    total_count  int8;
    ake002_names text := '';
    rec          record;
    topNames      text[];
BEGIN
    total_count := 0;
    for rec in select * from analysis_item(startDate, endDate, ipt_or_otp) func_tb order by func_tb.doubt_amount desc
        loop
            total_count := total_count + 1;
            IF total_count <= 3 THEN
                ake002_names = concat(ake002_names, rec.ake002, ',');
                topNames[total_count] = rec.ake002;
            end if;
        END loop;
    IF char_length(ake002_names) > 0 THEN
        ake002_names = substr(ake002_names, 0, char_length(ake002_names));
    end if;

    return query
        select total_count, ake002_names,topNames[1],topNames[2],topNames[3];
end
$$;

-- 3.1.2.	项目违规情况分析-排名
CREATE OR REPLACE FUNCTION analysis_item_rank_detail(startDate text, endDate text, rank_no int8, ipt_or_otp text)
    returns table
            (
                aaz307         text,
                aae386         text,
                ape804         numeric,
                ape804_percent text,
                item_count     numeric,
                amount_max_dr  text,
                count_max_dr   text
            )
    LANGUAGE plpgsql
as
$$
DECLARE
    -- 总金额
    total_amount  numeric;
    --指定项目
    target_ake001 text;
BEGIN
    DROP TABLE IF EXISTS item_rank_analysis_temp;
    DROP TABLE IF EXISTS dept_dr_temp;
    --写入临时表
    CREATE TEMP TABLE item_rank_analysis_temp as
    select * from analysis_kf10_groupby_aaz213(startDate, endDate, ipt_or_otp);

    --查询出指定的科室编码到 target_aaz307 中
    select u_kf10.ake001, sum(u_kf10.ape804)
    into target_ake001,total_amount
    from item_rank_analysis_temp u_kf10
    group by u_kf10.ake001
    order by sum(u_kf10.ape804) desc, count(u_kf10.aaz213) desc, u_kf10.ake001 desc
    limit 1 offset rank_no - 1;

    --删除非指定科室数据
    delete from item_rank_analysis_temp where target_ake001 is null or item_rank_analysis_temp.ake001 != target_ake001;


    create temp table dept_dr_temp as
    select dept_dr_tb.aaz307,
           dept_dr_tb.aaz263,
           max(dept_dr_tb.aae386)   as aae386,
           max(dept_dr_tb.aac003)   as aac003,
           sum(dept_dr_tb.ape804)   as ape804,
           count(dept_dr_tb.aaz213) as item_count
    from item_rank_analysis_temp dept_dr_tb
    group by dept_dr_tb.aaz307, dept_dr_tb.aaz263;

    return query
        select item_tb.aaz307,
               max(item_tb.aae386)                          as aae386,
               sum(item_tb.ape804)                          as ape804,
               concat(coalesce(round(sum(item_tb.ape804) * 100 / nullif(total_amount, 0), 2), 0)::varchar,
                      '%')                                  as ape804_percent,
               sum(item_tb.item_count)                      as item_count,

               (select string_agg(aac003, ',')
                from dept_dr_temp amount_max_tb
                where amount_max_tb.aaz307 = item_tb.aaz307
                  and amount_max_tb.ape804 >= (select amount_max_tb_inner.ape804
                                               from dept_dr_temp amount_max_tb_inner
                                               where amount_max_tb_inner.aaz307 = item_tb.aaz307
                                               order by amount_max_tb_inner.ape804 desc
                                               limit 1))    as amount_max_dr,
               (select string_agg(aac003, ',')
                from dept_dr_temp count_max_tb
                where count_max_tb.aaz307 = item_tb.aaz307
                  and count_max_tb.item_count >= (select count_max_tb_inner.item_count
                                                  from dept_dr_temp count_max_tb_inner
                                                  where count_max_tb_inner.aaz307 = item_tb.aaz307
                                                  order by count_max_tb_inner.item_count desc
                                                  limit 1)) as count_max_dr
        from dept_dr_temp item_tb
        group by item_tb.aaz307
        order by sum(item_tb.ape804) desc;
    DROP TABLE IF EXISTS item_dr_temp;
    DROP TABLE IF EXISTS rank_analysis_temp;
end
$$;

----------------------------------------------------------------------------------------------

--3.1.3-项目违规统计
CREATE OR REPLACE FUNCTION analysis_rule(startDate text, endDate text, ipt_or_otp text)
    returns table
            (
                aaz319               text,
                aaa167               text,
                doubt_amount         numeric,
                doubt_amount_percent text,
                doubt_count          bigint,
                doubt_count_percent  text
            )
    LANGUAGE plpgsql
as
$$
DECLARE
    -- 总金额
    total_amount numeric;
    --总次数
    total_count  int8;
BEGIN
    -- 删除临时表
    DROP TABLE IF EXISTS temp_analysis_result_item;
    CREATE TEMP TABLE IF NOT EXISTS temp_analysis_result_item AS
    SELECT
        kf10.aaz213,
        max(kf10.aaz307) as aaz307,
        max(kf10.aae386) as aae386,
        max(kf10.aaz263) as aaz263,
        max(kf10.aac003) as aac003,
        max(kf10.ake001) as ake001,
        max(kf10.ake002) as ake002,
        max(kf10.akb065) as akb065,
        max(kf10.akc226) as akc226,
        max(kf10.ape804) as ape804,
        max(kf10.ape805) as ape805,
        max(kf10.ykc610) as ykc610,
        max(kf10.aae040) as aae040,
        max(kf10.aaz319) as aaz319,
        max(kf10.aaa167) as aaa167,
        substring(kf10.aaz319::varchar, 3) as sub_aaz319
    FROM analysis_kf10_chose_scene(startDate, endDate, ipt_or_otp) kf10
    group by kf10.aaz213, substring(kf10.aaz319::varchar, 3);

    --汇总数据到变量
    select sum(tb_group_aaz213.ape804), sum(tb_group_aaz213.d_count)
    into total_amount,total_count
    from (select max(u_kf10.ape804) as ape804, count(distinct u_kf10.aaz213) as d_count
          from temp_analysis_result_item u_kf10
          group by aaz213) tb_group_aaz213;

    return query
        select u_kf10.sub_aaz319,
               max(u_kf10.aaa167),
               sum(u_kf10.ape804),
               concat(coalesce(ROUND((sum(u_kf10.ape804) * 100.0 / nullif(total_amount, 0)), 2), 0)::varchar, '%'),
               count(u_kf10.aaz213),
               concat(coalesce(ROUND((count(u_kf10.aaz213) * 100.0 / nullif(total_count, 0)), 2), 0)::varchar, '%')
        from temp_analysis_result_item u_kf10
        group by u_kf10.sub_aaz319
        order by sum(u_kf10.ape804) desc, count(u_kf10.aaz213) desc, u_kf10.sub_aaz319 desc;

    -- 删除临时表
    DROP TABLE IF EXISTS temp_analysis_result_item;

END;
$$;

-- 3.1.3-项目违规统计统计文字
CREATE OR REPLACE FUNCTION analysis_rule_remark(startDate text, endDate text, ipt_or_otp text)
    returns table
            (
                rule_count int8,
                rank_names text,
                top1name   text,
                top2name   text,
                top3name   text
            )
    LANGUAGE plpgsql
as
$$
DECLARE
    -- 总金额
    total_amount numeric;
    --总次数
    total_count  int8;
    rule_names   text := '';
    rec          record;
    topNames     text[];
BEGIN
    total_count := 0;
    for rec in select * from analysis_rule(startDate, endDate, ipt_or_otp) func_tb order by func_tb.doubt_amount desc
        loop
            total_count := total_count + 1;
            IF total_count <= 3 THEN
                rule_names = concat(rule_names, rec.aaa167, ',');
                topNames[total_count] = rec.aaa167;
            end if;
        END loop;
    IF char_length(rule_names) > 0 THEN
        rule_names = substr(rule_names, 0, char_length(rule_names));
    end if;

    return query
        select total_count, rule_names, topNames[1], topNames[2], topNames[3];
end
$$;

-- 3.1.2.	项目违规情况分析-排名
CREATE OR REPLACE FUNCTION analysis_rule_rank_detail(startDate text, endDate text, rank_no int8, ipt_or_otp text)
    returns table
            (
                ake001          text,
                ake002          text,
                ape804          numeric,
                ape804_percent  text,
                item_count      numeric,
                amount_max_dept text,
                count_max_dept  text
            )
    LANGUAGE plpgsql
as
$$
DECLARE
    -- 总金额
    total_amount  numeric;
    --指定规则
    target_aaz319 text;
BEGIN
    DROP TABLE IF EXISTS item_dept_temp;
    DROP TABLE IF EXISTS rule_rank_analysis_temp;
    --写入临时表
    CREATE TEMP TABLE rule_rank_analysis_temp as
    SELECT kf10.aaz213,
           max(kf10.aaz307) as aaz307,
           max(kf10.aae386) as aae386,
           max(kf10.aaz263) as aaz263,
           max(kf10.aac003) as aac003,
           max(kf10.ake001) as ake001,
           max(kf10.ake002) as ake002,
           max(kf10.akb065) as akb065,
           max(kf10.akc226) as akc226,
           max(kf10.ape804) as ape804,
           max(kf10.ape805) as ape805,
           max(kf10.ykc610) as ykc610,
           max(kf10.aae040) as aae040,
           max(kf10.aaz319) as aaz319,
           max(kf10.aaa167) as aaa167,
           substring(kf10.aaz319::varchar, 3) as sub_aaz319
    FROM analysis_kf10_chose_scene(startDate, endDate, ipt_or_otp) kf10
    group by kf10.aaz213, substring(kf10.aaz319::varchar, 3);

    --查询出指定的科室编码到 target_aaz307 中
    select u_kf10.sub_aaz319, sum(u_kf10.ape804)
    into target_aaz319,total_amount
    from rule_rank_analysis_temp u_kf10
    group by u_kf10.sub_aaz319
    order by sum(u_kf10.ape804) desc, count(u_kf10.aaz213) desc, u_kf10.sub_aaz319 desc
    limit 1 offset rank_no - 1;


    --删除非指定科室数据
    delete
    from rule_rank_analysis_temp
    where target_aaz319 is null
       or rule_rank_analysis_temp.sub_aaz319 != target_aaz319;


    create temp table item_dept_temp as
    select dept_dr_tb.ake001,
           dept_dr_tb.aaz307,
           max(dept_dr_tb.ake002)   as ake002,
           max(dept_dr_tb.aae386)   as aae386,
           sum(dept_dr_tb.ape804)   as ape804,
           count(dept_dr_tb.aaz213) as item_count
    from rule_rank_analysis_temp dept_dr_tb
    group by dept_dr_tb.ake001, dept_dr_tb.aaz307;

    return query
        select item_tb.ake001,
               max(item_tb.ake002)                          as ake002,
               sum(item_tb.ape804)                          as ape804,
               concat(coalesce(round(sum(item_tb.ape804) * 100 / nullif(total_amount, 0), 2), 0)::varchar,
                      '%')                                  as ape804_percent,
               sum(item_tb.item_count)                      as item_count,

               (select string_agg(aae386, ',')
                from item_dept_temp amount_max_tb
                where amount_max_tb.ake001 = item_tb.ake001
                  and amount_max_tb.ape804 >= (select amount_max_tb_inner.ape804
                                               from item_dept_temp amount_max_tb_inner
                                               where amount_max_tb_inner.ake001 = item_tb.ake001
                                               order by amount_max_tb_inner.ape804 desc
                                               limit 1))    as amount_max_dr,
               (select string_agg(aae386, ',')
                from item_dept_temp count_max_tb
                where count_max_tb.ake001 = item_tb.ake001
                  and count_max_tb.item_count >= (select count_max_tb_inner.item_count
                                                  from item_dept_temp count_max_tb_inner
                                                  where count_max_tb_inner.ake001 = item_tb.ake001
                                                  order by count_max_tb_inner.item_count desc
                                                  limit 1)) as count_max_dr
        from item_dept_temp item_tb
        group by item_tb.ake001
        order by sum(item_tb.ape804) desc;
    DROP TABLE IF EXISTS item_dept_temp;
    DROP TABLE IF EXISTS rule_rank_analysis_temp;
end
$$;

----------------------------------------------------------------------------------------------

--科室违规情况分析前三名称
CREATE OR REPLACE FUNCTION analysis_dept_top3name(startDate text, endDate text, ipt_or_opt text)
    returns table
            (
                top1name text,
                top2name text,
                top3name text
            )
    LANGUAGE plpgsql
as
$$
DECLARE
    name        text;
    -- 总金额
    top3nameArr text[];
    i           int;
BEGIN

    DROP TABLE IF EXISTS top_dept_temp_analysis_result;
    CREATE TEMP TABLE IF NOT EXISTS top_dept_temp_analysis_result AS
    SELECT *
    FROM analysis_kf10_groupby_aaz213(startDate, endDate, ipt_or_opt);

    i := 0;

    --查询出指定的科室编码到 target_aaz307 中
    for name in select max(aae386)
                from top_dept_temp_analysis_result u_kf10
                group by u_kf10.aaz307
                order by sum(u_kf10.ape804) desc, count (u_kf10.aaz213) desc, u_kf10.aaz307 desc
                limit 3
        loop
            top3nameArr[i] := name;
            i := i + 1;
        end loop;

    return query select top3nameArr[0], top3nameArr[1], top3nameArr[2];

    -- 删除临时表
    DROP TABLE IF EXISTS top_dept_temp_analysis_result;

END;
$$;

--项目违规情况分析前三名称
CREATE OR REPLACE FUNCTION analysis_item_top3name(startDate text, endDate text, ipt_or_opt text)
    returns table
            (
                top1name text,
                top2name text,
                top3name text
            )
    LANGUAGE plpgsql
as
$$
DECLARE
    name        text;
    -- 总金额
    top3nameArr text[];
    i           int;
BEGIN

    DROP TABLE IF EXISTS top_dept_temp_analysis_result;
    CREATE TEMP TABLE IF NOT EXISTS top_dept_temp_analysis_result AS
    SELECT *
    FROM analysis_kf10_groupby_aaz213(startDate, endDate, ipt_or_opt);

    i := 0;

    --查询出指定的科室编码到 target_aaz307 中
    for name in select max(u_kf10.ake002)
                from top_dept_temp_analysis_result u_kf10
                group by u_kf10.ake001
                order by sum(u_kf10.ape804) desc, count (u_kf10.aaz213) desc, u_kf10.ake001 desc
                limit 3
        loop
            top3nameArr[i] := name;
            i := i + 1;
        end loop;

    return query select top3nameArr[0], top3nameArr[1], top3nameArr[2];

    -- 删除临时表
    DROP TABLE IF EXISTS top_dept_temp_analysis_result;

END;

$$;

--科室违规情况分析前三名称
CREATE OR REPLACE FUNCTION analysis_rule_top3name(startDate text, endDate text, ipt_or_opt text)
    returns table
            (
                top1name text,
                top2name text,
                top3name text
            )
    LANGUAGE plpgsql
as
$$
DECLARE
    name        text;
    -- 总金额
    top3nameArr text[];
    i           int;
BEGIN

    DROP TABLE IF EXISTS top_rule_temp_analysis_result;
    CREATE TEMP TABLE IF NOT EXISTS top_rule_temp_analysis_result AS
    SELECT kf10.aaz213,
           max(kf10.aaz307) as aaz307,
           max(kf10.aae386) as aae386,
           max(kf10.aaz263) as aaz263,
           max(kf10.aac003) as aac003,
           max(kf10.ake001) as ake001,
           max(kf10.ake002) as ake002,
           max(kf10.akb065) as akb065,
           max(kf10.akc226) as akc226,
           max(kf10.ape804) as ape804,
           max(kf10.ape805) as ape805,
           max(kf10.ykc610) as ykc610,
           max(kf10.aae040) as aae040,
           max(kf10.aaz319) as aaz319,
           max(kf10.aaa167) as aaa167,
           substring(kf10.aaz319::varchar, 3) as sub_aaz319
    FROM analysis_kf10_chose_scene(startDate, endDate, ipt_or_opt) kf10
    group by kf10.aaz213, substring(kf10.aaz319::varchar, 3);


    i := 0;

    --查询出指定的科室编码到 target_aaz307 中
    for name in select max(u_kf10.aaa167)
                from top_rule_temp_analysis_result u_kf10
                group by u_kf10.sub_aaz319
                order by sum(u_kf10.ape804) desc
                limit 3
        loop
            top3nameArr[i] := name;
            i := i + 1;
        end loop;

    return query select top3nameArr[0], top3nameArr[1], top3nameArr[2];

    -- 删除临时表
    DROP TABLE IF EXISTS top_rule_temp_analysis_result;

END;

$$;

---------------------------------------------------------------------------------------------

CREATE OR REPLACE FUNCTION analysis_general(startDate text, endDate text)
    returns table
            (
                ipt_patient_count       int8,
                ipt_item_count          int8,
                ipt_total_amount        numeric,
                otp_patient_count       int8,
                otp_item_count          int8,
                otp_total_amount        numeric,
                ipt_doubt_patient_count int8,
                ipt_doubt_item_count    int8,
                ipt_doubt_total_amount  numeric,
                otp_doubt_patient_count int8,
                otp_doubt_item_count    int8,
                otp_doubt_total_amount  numeric,
                rule_count              int8
            )
    LANGUAGE plpgsql
as
$$
DECLARE
    -- 总金额
    ipt_total_amount        numeric;
    -- 总人次
    ipt_patient_count       int8;
    -- 项目次数
    ipt_item_count          int8;
    -- 违规总金额
    ipt_doubt_total_amount  numeric;
    -- 违规总人次
    ipt_doubt_patient_count int8;
    -- 违规项目次数
    ipt_doubt_item_count    int8;
    -- 总金额
    otp_total_amount        numeric;
    -- 总人次
    otp_patient_count       int8;
    -- 项目次数
    otp_item_count          int8;
    -- 违规总金额
    otp_doubt_total_amount  numeric;
    -- 违规总人次
    otp_doubt_patient_count int8;
    -- 违规项目次数
    otp_doubt_item_count    int8;
    --规则个数
    all_rule_count          int8;
    -------------------------------
    -- 开启场景
    enableCfgNo             text;
    ipt_rule_arr_str        text;
    otp_rule_arr_str        text;

BEGIN
    select string_agg(aa01.aaz499::VARCHAR, ',')
    into enableCfgNo
    from aa01
    where aa01.aaz499 in ('2', '16', '3', '4', '5', '6', '7', '18')
      and upper(aa01.aaa005) = 'Y';

    --查询住院总次数总金额
    select sum(kc22_all.akb065),
           count(distinct kc22_all.akc190),
           count(distinct kc22_all.aaz213)
    into ipt_total_amount,ipt_patient_count,ipt_item_count
    from (select drkc22.aaz213,
                 drkc22.akb065,
                 newest.akc190
          from dsc_newest newest
                   left join ipt_drdscg_kc22 drkc22 on newest.aae500 = '4' and newest.aaz217 = drkc22.aaz217--医生站出院
          where enableCfgNo like '%7%'
            and newest.aae040 between to_date(startDate, 'yyyy-MM-dd') and (to_date(endDate, 'yyyy-MM-dd') + INTERVAL '1 day')
            and newest.aae040 < CURRENT_DATE
          union all
          select nurkc22.aaz213,
                 nurkc22.akb065,
                 newest.akc190
          from dsc_newest newest
                   left join ipt_nurdscg_kc22 nurkc22 on newest.aae500 = '7' and newest.aaz217 = nurkc22.aaz217---护士站出院
          where enableCfgNo like '%6%'
            and newest.aae040 between to_date(startDate, 'yyyy-MM-dd') and (to_date(endDate, 'yyyy-MM-dd') + INTERVAL '1 day')
            and newest.aae040 < CURRENT_DATE
          union all
          select hidkc22.aaz213,
                 hidkc22.akb065,
                 newest.akc190
          from dsc_newest newest
                   left join ipt_hiddscg_kc22 hidkc22 on newest.aae500 = '5' and newest.aaz217 = hidkc22.aaz217--医保办出院
          where enableCfgNo like '%5%'
            and newest.aae040 between to_date(startDate, 'yyyy-MM-dd') and (to_date(endDate, 'yyyy-MM-dd') + INTERVAL '1 day')
            and newest.aae040 < CURRENT_DATE
          union all
          select drrefkc22.aaz213,
                 drrefkc22.akb065,
                 newest.akc190
          from dsc_newest newest
                   left join ipt_drrefldept_kc22 drrefkc22 on newest.aae500 = '12' and newest.aaz217 = drrefkc22.aaz217--医生站转科出院
          where enableCfgNo like '%18%'
            and newest.aae040 between to_date(startDate, 'yyyy-MM-dd') and (to_date(endDate, 'yyyy-MM-dd') + INTERVAL '1 day')
            and newest.aae040 < CURRENT_DATE
          union all
          select nurefkc22.aaz213,
                 nurefkc22.akb065,
                 newest.akc190
          from dsc_newest newest
                   left join ipt_nurrefldept_kc22 nurefkc22 on newest.aae500 = '13' and newest.aaz217 = nurefkc22.aaz217--护士站转科出院
          where enableCfgNo like '%18%'
            and newest.aae040 between to_date(startDate, 'yyyy-MM-dd') and (to_date(endDate, 'yyyy-MM-dd') + INTERVAL '1 day')
            and newest.aae040 < CURRENT_DATE
          union all
          select drkc23.aaz213,
                 drkc23.akb065,
                 drkc23.akc190
          from ipt_drord_kc23 drkc23
          where enableCfgNo like '%3%'
            and drkc23.aae040 between to_date(startDate, 'yyyy-MM-dd') and (to_date(endDate, 'yyyy-MM-dd') + INTERVAL '1 day')
            and drkc23.aae040 < CURRENT_DATE
          union all
          select nurfeekc22.aaz213,
                 nurfeekc22.akb065,
                 nurfeekc22.akc190
          from ipt_nurfee_kc22 nurfeekc22
          where enableCfgNo like '%4%'
            and nurfeekc22.aae040 between to_date(startDate, 'yyyy-MM-dd') and (to_date(endDate, 'yyyy-MM-dd') + INTERVAL '1 day')
            and nurfeekc22.aae040 < CURRENT_DATE) kc22_all;

    --查询门诊总次数总金额
    select sum(kc22_all.akb065),
           count(distinct kc22_all.akc190),
           count(distinct kc22_all.aaz213)
    into otp_total_amount,otp_patient_count,otp_item_count
    from (select kc19.aaz213,
                 kc19.akb065,
                 kc19.akc190
          from otp_kc19 kc19
          where enableCfgNo like '%2%'
            and kc19.aae040 between to_date(startDate, 'yyyy-MM-dd') and (to_date(endDate, 'yyyy-MM-dd') + interval '1 day')
            and kc19.aae040 < current_date
          union all
          select opspkc16.aaz213,
                 opspkc16.akb065,
                 opspkc16.akc190
          from opsp_kc16 opspkc16
          where enableCfgNo like '%16%'
            and opspkc16.aae040 between to_date(startDate, 'yyyy-MM-dd') and (to_date(endDate, 'yyyy-MM-dd') + interval '1 day')
            and opspkc16.aae040 < current_date) kc22_all;

    --查询住院违规汇总数据
    select sum(ipt_all.ape804)            as total_amount,
           count(distinct ipt_all.akc190) as patinet_count,
           count(ipt_all.aaz213)          as detail_count,
           string_agg(ipt_all.aaz319, ',')
    into ipt_doubt_total_amount,ipt_doubt_patient_count,ipt_doubt_item_count,ipt_rule_arr_str
    from analysis_kf10_groupby_aaz213(startDate, endDate, 'ipt') ipt_all;

    --查询门诊违规汇总数据
    select sum(ipt_all.ape804)            as total_amount,
           count(distinct ipt_all.akc190) as patinet_count,
           count(ipt_all.aaz213)          as detail_count,
           string_agg(ipt_all.aaz319, ',')
    into otp_doubt_total_amount,otp_doubt_patient_count,otp_doubt_item_count,otp_rule_arr_str
    from analysis_kf10_groupby_aaz213(startDate, endDate, 'otp') ipt_all;

    select count(1)
    into all_rule_count
    from (select distinct substring(unnest(array_cat(string_to_array(ipt_rule_arr_str, ','),
                                                     string_to_array(otp_rule_arr_str, ','))), 3)) as tb_rule;

    return query
        select coalesce(ipt_patient_count, 0),
               coalesce(ipt_item_count, 0),
               coalesce(ipt_total_amount, 0),
               coalesce(otp_patient_count, 0),
               coalesce(otp_item_count, 0),
               coalesce(otp_total_amount, 0),
               coalesce(ipt_doubt_patient_count, 0),
               coalesce(ipt_doubt_item_count, 0),
               coalesce(ipt_doubt_total_amount, 0),
               coalesce(otp_doubt_patient_count, 0),
               coalesce(otp_doubt_item_count, 0),
               coalesce(otp_doubt_total_amount, 0),
               coalesce(all_rule_count, 0);

end
$$;

CREATE OR REPLACE FUNCTION analysis_dscgtime_general(startDate text, endDate text)
    returns table
            (
                total_amount numeric,
                patient_count int8,
                item_count int8
            )
    LANGUAGE plpgsql
as
$$
begin
    --查询住院总次数总金额
    return query
        select sum(kc22_all.akb065),
               count(distinct kc22_all.akc190),
               count(distinct kc22_all.aaz213)
        from (select drkc22.aaz213,
                     drkc22.akb065,
                     newest.akc190
              from dsc_newest newest
                       left join ipt_drdscg_kc22 drkc22 on newest.aae500 = '4' and newest.aaz217 = drkc22.aaz217--医生站出院
              where newest.aae031  >= to_date(startDate, 'yyyy-MM-dd') and newest.aae031  < (to_date(endDate, 'yyyy-MM-dd') + INTERVAL '1 day')
                and newest.aae031 < CURRENT_DATE
              union all
              select nurkc22.aaz213,
                     nurkc22.akb065,
                     newest.akc190
              from dsc_newest newest
                       left join ipt_nurdscg_kc22 nurkc22 on newest.aae500 = '7' and newest.aaz217 = nurkc22.aaz217---护士站出院
              where newest.aae031  >= to_date(startDate, 'yyyy-MM-dd') and newest.aae031  < (to_date(endDate, 'yyyy-MM-dd') + INTERVAL '1 day')
                and newest.aae031 < CURRENT_DATE
              union all
              select hidkc22.aaz213,
                     hidkc22.akb065,
                     newest.akc190
              from dsc_newest newest
                       left join ipt_hiddscg_kc22 hidkc22 on newest.aae500 = '5' and newest.aaz217 = hidkc22.aaz217--医保办出院
              where newest.aae031  >= to_date(startDate, 'yyyy-MM-dd') and newest.aae031  < (to_date(endDate, 'yyyy-MM-dd') + INTERVAL '1 day')
                and newest.aae031 < CURRENT_DATE
              union all
              select drrefkc22.aaz213,
                     drrefkc22.akb065,
                     newest.akc190
              from dsc_newest newest
                       left join ipt_drrefldept_kc22 drrefkc22 on newest.aae500 = '12' and newest.aaz217 = drrefkc22.aaz217--医生站转科出院
              where newest.aae031  >= to_date(startDate, 'yyyy-MM-dd') and newest.aae031  < (to_date(endDate, 'yyyy-MM-dd') + INTERVAL '1 day')
                and newest.aae031 < CURRENT_DATE
              union all
              select nurrfkc22.aaz213,
                     nurrfkc22.akb065,
                     newest.akc190
              from dsc_newest newest
                       left join ipt_nurrefldept_kc22 nurrfkc22 on newest.aae500 = '13' and newest.aaz217 = nurrfkc22.aaz217--医生站转科出院
              where newest.aae031  >= to_date(startDate, 'yyyy-MM-dd') and newest.aae031  < (to_date(endDate, 'yyyy-MM-dd') + INTERVAL '1 day')
                and newest.aae031 < CURRENT_DATE) kc22_all;

end
$$
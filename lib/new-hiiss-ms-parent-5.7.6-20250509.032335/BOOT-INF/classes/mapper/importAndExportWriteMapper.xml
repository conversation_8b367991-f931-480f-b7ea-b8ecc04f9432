<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yinhai.ta404.component.org.orguser.importexport.mapper.write.ImportAndExportWriteMapper">
    <sql id="Base_List">
    u.USERID as USERID, u.LOGINID as LOGINID, u.PASSWORD as PASSWORD, u.PASSWORDDEFAULTNUM as PASSWORDDEFAULTNUM, u.<PERSON><PERSON>LASTMODIFYDATE as PWDLASTMODIFYDATE, u.ISLOCK as ISLOCK, u.ORDERNO as ORDERNO,
    u.NAME as NAME, u.SEX as SEX, u.IDCARDTYPE as IDCARDTYPE, u.IDCARDNO as IDCARDNO, u.MOBI<PERSON> as MO<PERSON>LE, u.CREATEUSER as CREATEUSER, u.CREATETIME as CREATETIME, u.MODIFYTIME as MODIFY<PERSON><PERSON>, u.DESTORY as DESTORY,
    u.ACCOUNTSOURCE as ACCOUNTSOURCE, u.EFFECTIVE as EFFECTIVE, u.EFFECTIVETIME as EFFECTIVETIME, u.JOBNUMBER as JOBNUMBER, u.STATE as STATE, u.BIRTHPLACE as BIRTHPLACE, u.ADDRESS as ADDRESS, u.ZIPCODE as ZIPCODE,
    u.EMAIL as EMAIL, u.PHONE as PHONE, u.EDUCATION as EDUCATION, u.GRADUATESCHOOL as GRADUATESCHOOL, u.WORKPLACE as WORKPLACE, u.FIELD01 as FIELD01, u.FIELD02 as FIELD02, u.FIELD03 as FIELD03, u.FIELD04 as FIELD04,
    u.FIELD05 as FIELD05, u.FIELD06 as FIELD06, u.FIELD07 as FIELD07, u.FIELD08 as FIELD08, u.FIELD09 as FIELD09, u.FIELD10 as FIELD10
    </sql>

    <sql id="Base_Column_List">
            ORGID      ,
            ORGNAME    ,
            SPELL      ,
            PARENTID   ,
            IDPATH     ,
            NAMEPATH   ,
            CUSTOMNO   ,
            ORDERNO    ,
            ORGLEVEL   ,
            AREA       ,
            EFFECTIVE  ,
            ORGTYPE    ,
            CREATEUSER ,
            CREATETIME ,
            MODIFYTIME ,
            ORGMANAGER ,
            ORGCODE    ,
            CONTACTS   ,
            ADDRESS    ,
            TEL        ,
            DESTORY    ,
            FIELD01    ,
            FIELD02    ,
            FIELD03    ,
            FIELD04    ,
            FIELD05    ,
            FIELD06    ,
            FIELD07    ,
            FIELD08    ,
            FIELD09    ,
            FIELD10
  </sql>

    <!--  批量添加到taorg  -->
    <insert id="addOrgExcel">
        insert into taorg (<include refid="Base_Column_List"/>)
        values(
        #{orgId,jdbcType = VARCHAR},
        #{orgName, jdbcType = VARCHAR},
        #{spell,  jdbcType = VARCHAR},
        #{parentId, jdbcType = VARCHAR},
        #{idPath, jdbcType = VARCHAR},
        #{namePath, jdbcType = VARCHAR},
        #{customNo,  jdbcType = NUMERIC},
        #{orderNo, jdbcType = NUMERIC},
        #{orgLevel, jdbcType = NUMERIC},
        #{area, jdbcType = VARCHAR},
        #{effective, jdbcType = VARCHAR},
        #{orgType, jdbcType = VARCHAR},
        #{createUser, jdbcType = VARCHAR},
        #{createTime, jdbcType = TIMESTAMP},
        #{modifyTime, jdbcType = TIMESTAMP},
        #{orgManager, jdbcType = VARCHAR},
        #{orgCode, jdbcType = VARCHAR},
        #{contacts, jdbcType = VARCHAR},
        #{address, jdbcType = VARCHAR},
        #{tel, jdbcType = VARCHAR},
        #{destory, jdbcType = VARCHAR},
        #{field01, jdbcType = VARCHAR},
        #{field02, jdbcType = VARCHAR},
        #{field03, jdbcType = VARCHAR},
        #{field04, jdbcType = VARCHAR},
        #{field05, jdbcType = VARCHAR},
        #{field06, jdbcType = VARCHAR},
        #{field07, jdbcType = VARCHAR},
        #{field08, jdbcType = VARCHAR},
        #{field09, jdbcType = VARCHAR},
        #{field10, jdbcType = VARCHAR})
    </insert>

    <!--  excel添加人员  -->
    <insert id="addUserExcel">
        insert into tauser (USERID, LOGINID, PASSWORD,ISLOCK, NAME, SEX, DESTORY, JOBNUMBER, EFFECTIVE, CREATEUSER, CREATETIME)
        values (#{userId,jdbcType=VARCHAR}, #{loginId,jdbcType=VARCHAR}, #{password,jdbcType=VARCHAR},
        #{isLock,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{sex,jdbcType=VARCHAR}, '0', #{jobNumber,jdbcType=VARCHAR},
        #{effective, jdbcType=TIMESTAMP}, #{createUser,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP})
    </insert>

    <insert id="addUserOrg" parameterType="com.yinhai.ta404.component.org.orguser.userorg.entity.TaUserOrgPo">
        insert into TAUSERORG (USERID, ORGID, ISDIRECT)
        values (#{userId,jdbcType=VARCHAR}, #{orgId,jdbcType=VARCHAR}, #{isDirect,jdbcType=VARCHAR})
    </insert>
</mapper>

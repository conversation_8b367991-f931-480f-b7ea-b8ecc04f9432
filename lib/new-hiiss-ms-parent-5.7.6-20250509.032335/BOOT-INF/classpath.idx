- "ta404-component-audit-5.3.2-RELEASE.jar"
- "ta404-component-captcha-springboot-starter-5.3.2-RELEASE.jar"
- "ta404-component-captcha-core-5.3.2-RELEASE.jar"
- "nl.captcha.simplecaptcha-1.1.1.jar"
- "ta404-component-dictmg-5.3.2-RELEASE.jar"
- "ta404-module-dict-5.3.2-RELEASE.jar"
- "ta404-component-logmg-5.3.2-RELEASE.jar"
- "ta404-module-elasticjob-5.3.2-RELEASE.jar"
- "elastic-job-lite-spring-2.1.5.jar"
- "elastic-job-lite-core-2.1.5.jar"
- "elastic-job-common-core-2.1.5.jar"
- "curator-framework-5.1.0.jar"
- "curator-client-5.1.0.jar"
- "zookeeper-3.6.0.jar"
- "zookeeper-jute-3.6.0.jar"
- "audience-annotations-0.5.0.jar"
- "netty-transport-native-epoll-4.1.52.Final.jar"
- "netty-transport-native-unix-common-4.1.52.Final.jar"
- "curator-recipes-5.1.0.jar"
- "gson-2.8.6.jar"
- "commons-exec-1.3.jar"
- "aspectjweaver-1.9.6.jar"
- "ta404-component-messagemg-5.3.2-RELEASE.jar"
- "ta404-module-message-5.3.2-RELEASE.jar"
- "spring-boot-starter-websocket-2.3.4.RELEASE.jar"
- "spring-messaging-5.2.9.RELEASE.jar"
- "spring-websocket-5.2.9.RELEASE.jar"
- "simple-java-mail-5.5.1.jar"
- "jakarta.activation-1.2.2.jar"
- "emailaddress-rfc2822-2.1.3.jar"
- "jsr305-3.0.1.jar"
- "jakarta.mail-1.6.5.jar"
- "ta404-component-org-core-5.3.2-RELEASE.jar"
- "ta404-component-org-authority-5.3.2-RELEASE.jar"
- "ta404-component-org-orguser-5.3.2-RELEASE.jar"
- "ta404-component-examine-5.3.2-RELEASE.jar"
- "aspectjrt-1.9.6.jar"
- "spring-social-core-1.1.6.RELEASE.jar"
- "ta404-component-org-sysmg-*******-RELEASE.jar"
- "light-2.0.8-RELEASE.jar"
- "light-boot-2.0.8-RELEASE.jar"
- "nacos-client-1.4.2.jar"
- "nacos-common-1.4.2.jar"
- "httpasyncclient-4.1.4.jar"
- "httpcore-nio-4.4.13.jar"
- "nacos-api-1.4.2.jar"
- "guava-19.0.jar"
- "commons-codec-1.14.jar"
- "jackson-core-2.11.2.jar"
- "jackson-databind-2.11.2.jar"
- "simpleclient-0.5.0.jar"
- "snakeyaml-1.26.jar"
- "vertx-web-client-4.0.0.jar"
- "vertx-web-common-4.0.0.jar"
- "vertx-auth-common-4.0.0.jar"
- "vertx-core-4.0.0.jar"
- "netty-common-4.1.52.Final.jar"
- "netty-buffer-4.1.52.Final.jar"
- "netty-transport-4.1.52.Final.jar"
- "netty-handler-4.1.52.Final.jar"
- "netty-codec-4.1.52.Final.jar"
- "netty-handler-proxy-4.1.52.Final.jar"
- "netty-codec-socks-4.1.52.Final.jar"
- "netty-codec-http-4.1.52.Final.jar"
- "netty-codec-http2-4.1.52.Final.jar"
- "netty-resolver-4.1.52.Final.jar"
- "netty-resolver-dns-4.1.52.Final.jar"
- "netty-codec-dns-4.1.52.Final.jar"
- "ta404-component-security-session-5.3.2-RELEASE.jar"
- "ta404-core-*******-RELEASE.jar"
- "spring-boot-starter-data-jpa-2.3.4.RELEASE.jar"
- "spring-boot-starter-aop-2.3.4.RELEASE.jar"
- "jakarta.transaction-api-1.3.3.jar"
- "jakarta.persistence-api-2.2.3.jar"
- "hibernate-core-5.4.21.Final.jar"
- "antlr-2.7.7.jar"
- "jandex-2.1.3.Final.jar"
- "dom4j-2.1.3.jar"
- "hibernate-commons-annotations-5.1.0.Final.jar"
- "spring-data-jpa-2.3.4.RELEASE.jar"
- "spring-data-commons-2.3.4.RELEASE.jar"
- "spring-orm-5.2.9.RELEASE.jar"
- "spring-aspects-5.2.9.RELEASE.jar"
- "bcprov-jdk15on-1.65.jar"
- "commons-lang3-3.10.jar"
- "commons-beanutils-1.9.3.jar"
- "commons-collections-3.2.2.jar"
- "zip4j-2.1.3.jar"
- "disruptor-3.4.2.jar"
- "protostuff-core-1.4.0.jar"
- "protostuff-api-1.4.0.jar"
- "protostuff-runtime-1.4.0.jar"
- "protostuff-collectionschema-1.4.0.jar"
- "kryo-4.0.0.jar"
- "reflectasm-1.11.3.jar"
- "minlog-1.3.0.jar"
- "objenesis-2.5.1.jar"
- "kryo-serializers-0.42.jar"
- "pinyin4j-2.5.0.jar"
- "commons-io-2.6.jar"
- "spring-security-web-5.3.4.RELEASE.jar"
- "spring-security-core-5.3.4.RELEASE.jar"
- "spring-aop-5.2.9.RELEASE.jar"
- "spring-beans-5.2.9.RELEASE.jar"
- "spring-context-5.2.9.RELEASE.jar"
- "spring-expression-5.2.9.RELEASE.jar"
- "ta404-core-adapter-5.3.2-RELEASE.jar"
- "ta404-core-adapter-common-5.3.2-RELEASE.jar"
- "validation-api-2.0.1.Final.jar"
- "hibernate-validator-6.1.5.Final.jar"
- "jakarta.validation-api-2.0.2.jar"
- "jboss-logging-3.4.1.Final.jar"
- "classmate-1.5.1.jar"
- "spring-boot-configuration-processor-2.3.4.RELEASE.jar"
- "swagger-annotations-1.5.20.jar"
- "druid-1.2.22.jar"
- "ta404-component-security-base-*******-RELEASE.jar"
- "spring-social-security-1.1.6.RELEASE.jar"
- "spring-social-web-1.1.6.RELEASE.jar"
- "javax.inject-1.jar"
- "spring-security-config-5.3.4.RELEASE.jar"
- "ta404-module-cache-core-5.3.2-RELEASE.jar"
- "ta404-module-cache-ehcache-5.3.2-RELEASE.jar"
- "spring-boot-starter-cache-2.3.4.RELEASE.jar"
- "cache-api-1.1.1.jar"
- "ehcache-3.8.1.jar"
- "jaxb-runtime-2.3.3.jar"
- "txw2-2.3.3.jar"
- "istack-commons-runtime-3.0.11.jar"
- "ta404-module-cluster-5.3.2-RELEASE.jar"
- "ta404-module-registry-api-5.3.2-RELEASE.jar"
- "jgroups-4.0.12.Final.jar"
- "ta404-module-datasource-5.3.2-RELEASE.jar"
- "ta404-module-mybatis-5.3.2-RELEASE.jar"
- "pagehelper-5.2.0.jar"
- "mybatis-spring-2.0.6.jar"
- "commons-dbcp-1.4.jar"
- "commons-pool-1.6.jar"
- "ta404-module-encryption-5.3.2-RELEASE.jar"
- "ta404-module-websecurity-5.3.2-RELEASE.jar"
- "esapi-2.1.0.1.jar"
- "spring-boot-starter-web-2.3.4.RELEASE.jar"
- "spring-boot-starter-2.3.4.RELEASE.jar"
- "spring-boot-2.3.4.RELEASE.jar"
- "spring-boot-starter-logging-2.3.4.RELEASE.jar"
- "logback-classic-1.2.3.jar"
- "logback-core-1.2.3.jar"
- "jul-to-slf4j-1.7.30.jar"
- "jakarta.annotation-api-1.3.5.jar"
- "spring-boot-starter-json-2.3.4.RELEASE.jar"
- "jackson-datatype-jdk8-2.11.2.jar"
- "jackson-datatype-jsr310-2.11.2.jar"
- "jackson-module-parameter-names-2.11.2.jar"
- "spring-boot-starter-tomcat-2.3.4.RELEASE.jar"
- "tomcat-embed-core-9.0.38.jar"
- "jakarta.el-3.0.3.jar"
- "tomcat-embed-websocket-9.0.38.jar"
- "spring-web-5.2.9.RELEASE.jar"
- "spring-webmvc-5.2.9.RELEASE.jar"
- "log4j-api-2.17.0.jar"
- "log4j-to-slf4j-2.17.0.jar"
- "slf4j-api-1.7.30.jar"
- "postgresql-42.6.0.jar"
- "checker-qual-3.31.0.jar"
- "ojdbc6-11.2.0.3.jar"
- "jakarta.xml.bind-api-2.3.3.jar"
- "jakarta.activation-api-1.2.2.jar"
- "hamcrest-2.2.jar"
- "byte-buddy-1.10.14.jar"
- "spring-core-5.2.9.RELEASE.jar"
- "spring-jcl-5.2.9.RELEASE.jar"
- "spring-test-5.2.9.RELEASE.jar"
- "hiiss-component-audit-querycommon-0.0.1-SNAPSHOT.jar"
- "easyexcel-core-3.1.1.jar"
- "easyexcel-support-3.1.1.jar"
- "commons-csv-1.8.jar"
- "easyexcel-3.1.1.jar"
- "commons-text-1.6.jar"
- "spring-boot-starter-data-redis-2.3.4.RELEASE.jar"
- "spring-data-redis-2.3.4.RELEASE.jar"
- "spring-data-keyvalue-2.3.4.RELEASE.jar"
- "spring-oxm-5.2.9.RELEASE.jar"
- "lettuce-core-5.3.4.RELEASE.jar"
- "reactor-core-3.3.10.RELEASE.jar"
- "reactive-streams-1.0.3.jar"
- "hiiss-component-audit-analysis-0.0.1-SNAPSHOT.jar"
- "jxl-2.6.12.jar"
- "log4j-1.2.14.jar"
- "hiiss-component-client-0.0.1-SNAPSHOT.jar"
- "DBPNSPublisher-1.0.0.jar"
- "annotations-26.0.2.jar"
- "commons-httpclient-3.1.jar"
- "commons-logging-1.0.4.jar"
- "freemarker-2.3.30.jar"
- "fr.opensagres.poi.xwpf.converter.pdf-2.0.2.jar"
- "fr.opensagres.poi.xwpf.converter.core-2.0.2.jar"
- "fr.opensagres.xdocreport.itext.extension-2.0.2.jar"
- "itext-2.1.7.jar"
- "pdfbox-2.0.24.jar"
- "fontbox-2.0.24.jar"
- "hiiss-component-common-0.0.1-SNAPSHOT.jar"
- "spring-context-support-5.2.9.RELEASE.jar"
- "commons-lang-2.6.jar"
- "jackson-mapper-asl-1.9.2.jar"
- "jackson-core-asl-1.9.2.jar"
- "jackson-annotations-2.11.2.jar"
- "spring-boot-starter-jdbc-2.3.4.RELEASE.jar"
- "HikariCP-3.4.5.jar"
- "spring-jdbc-5.2.9.RELEASE.jar"
- "idcenter-1.1.1.jar"
- "aspose-words-16.8.0.jar"
- "nportal-hit-base-rpc-api-1.0-SNAPSHOT.jar"
- "spring-cloud-openfeign-core-2.1.0.RELEASE.jar"
- "spring-cloud-netflix-ribbon-2.1.0.RELEASE.jar"
- "spring-cloud-netflix-archaius-2.1.0.RELEASE.jar"
- "feign-form-spring-3.5.0.jar"
- "feign-form-3.5.0.jar"
- "aspose-cells-8.5.2.jar"
- "httpmime-4.5.12.jar"
- "commons-pool2-2.8.1.jar"
- "cxf-rt-frontend-jaxws-3.2.6.jar"
- "xml-resolver-1.2.jar"
- "asm-5.2.jar"
- "cxf-core-3.2.6.jar"
- "woodstox-core-5.1.0.jar"
- "stax2-api-4.1.jar"
- "xmlschema-core-2.2.3.jar"
- "cxf-rt-bindings-soap-3.2.6.jar"
- "cxf-rt-wsdl-3.2.6.jar"
- "wsdl4j-1.6.3.jar"
- "cxf-rt-databinding-jaxb-3.2.6.jar"
- "cxf-rt-bindings-xml-3.2.6.jar"
- "cxf-rt-frontend-simple-3.2.6.jar"
- "cxf-rt-ws-addr-3.2.6.jar"
- "cxf-rt-ws-policy-3.2.6.jar"
- "neethi-3.1.1.jar"
- "cxf-rt-transports-http-3.2.6.jar"
- "gims-common-1.0.2.jar"
- "gims-sdk-1.2.5.jar"
- "hiiss-component-costcontrol-0.0.1-SNAPSHOT.jar"
- "hiiss-component-indexMonitor-0.0.1-SNAPSHOT.jar"
- "hiiss-component-hisshow-0.0.1-SNAPSHOT.jar"
- "mysql-connector-java-8.0.21.jar"
- "hiiss-component-indexWarnManage-0.0.1-SNAPSHOT.jar"
- "hiiss-component-mtt-api-0.0.1-SNAPSHOT.jar"
- "junit-4.13.jar"
- "hamcrest-core-2.2.jar"
- "threetenbp-1.4.0.jar"
- "knife4j-spring-boot-starter-2.0.9.jar"
- "knife4j-spring-boot-autoconfigure-2.0.9.jar"
- "knife4j-spring-2.0.9.jar"
- "knife4j-annotations-2.0.9.jar"
- "knife4j-core-2.0.9.jar"
- "swagger-models-1.5.22.jar"
- "springfox-swagger2-2.10.5.jar"
- "springfox-spi-2.10.5.jar"
- "springfox-core-2.10.5.jar"
- "springfox-schema-2.10.5.jar"
- "springfox-swagger-common-2.10.5.jar"
- "springfox-spring-web-2.10.5.jar"
- "classgraph-4.1.7.jar"
- "spring-plugin-core-2.0.0.RELEASE.jar"
- "spring-plugin-metadata-2.0.0.RELEASE.jar"
- "mapstruct-1.3.1.Final.jar"
- "springfox-bean-validators-2.10.5.jar"
- "springfox-spring-webmvc-2.10.5.jar"
- "knife4j-spring-ui-2.0.9.jar"
- "spring-boot-starter-quartz-2.3.4.RELEASE.jar"
- "spring-tx-5.2.9.RELEASE.jar"
- "quartz-2.3.2.jar"
- "mchange-commons-java-0.2.15.jar"
- "retrofit-spring-boot-starter-2.0.2.jar"
- "retrofit-2.9.0.jar"
- "okhttp-3.14.9.jar"
- "okio-1.17.2.jar"
- "converter-jackson-2.9.0.jar"
- "javax.annotation-api-1.3.2.jar"
- "logging-interceptor-3.14.9.jar"
- "hiiss-component-mtt-biz-0.0.1-SNAPSHOT.jar"
- "hiiss-component-systemmanage-0.0.1-SNAPSHOT.jar"
- "jsoup-1.8.3.jar"
- "hiiss-component-individualDemand-0.0.1-SNAPSHOT.jar"
- "jasypt-spring-boot-starter-3.0.2.jar"
- "jasypt-spring-boot-3.0.2.jar"
- "jasypt-1.9.3.jar"
- "poi-4.1.2.jar"
- "commons-collections4-4.3.jar"
- "commons-math3-3.6.1.jar"
- "SparseBitSet-1.2.jar"
- "poi-ooxml-4.1.2.jar"
- "commons-compress-1.19.jar"
- "curvesapi-1.06.jar"
- "poi-ooxml-schemas-4.1.2.jar"
- "xmlbeans-3.1.0.jar"
- "ooxml-schemas-1.4.jar"
- "hutool-all-5.8.8.jar"
- "commons-fileupload-1.4.jar"
- "httpclient-4.5.12.jar"
- "httpcore-4.4.13.jar"
- "statistics-modal-1.0.0.jar"
- "license_common-1.0.0.jar"
- "truelicense-core-1.32.jar"
- "truelicense-xml-1.32.jar"
- "lombok-1.18.12.jar"
- "hiiss-component-webservice-0.0.1-SNAPSHOT.jar"
- "cxf-spring-boot-starter-jaxws-3.3.6.jar"
- "cxf-spring-boot-autoconfigure-3.3.6.jar"
- "jaxen-1.2.0.jar"
- "easypoi-base-4.2.0.jar"
- "ognl-3.2.6.jar"
- "javassist-3.20.0-GA.jar"
- "easypoi-annotation-4.2.0.jar"
- "easypoi-spring-boot-starter-3.3.0.jar"
- "easypoi-web-3.3.0.jar"
- "poi-scratchpad-4.1.1.jar"
- "spring-boot-autoconfigure-2.3.4.RELEASE.jar"
- "mybatis-plus-core-3.5.1.jar"
- "mybatis-plus-annotation-3.5.1.jar"
- "jsqlparser-4.3.jar"
- "mybatis-3.5.6.jar"
- "fastjson-1.2.83.jar"
- "ta404-component-security-cas-5.3.2-RELEASE.jar"
- "spring-security-cas-client-3.0.8.RELEASE.jar"
- "cas-client-core-3.1.10.jar"
- "ta404-component-core-5.3.2-RELEASE.jar"

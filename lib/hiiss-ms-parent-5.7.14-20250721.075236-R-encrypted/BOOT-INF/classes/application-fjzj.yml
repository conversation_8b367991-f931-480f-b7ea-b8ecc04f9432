#sftp
file:
  host: *************
  port: 22
  name: root
  ciphertext: y<PERSON><PERSON>@123
  upfiledir: /u01/bgsysfiles/bgfiles/
  #超时时间
  timeout: 120000
xxl:
  job:
    accessToken:
    admin:
      #addresses: http://*************:8688/xxl-job-admin
      addresses: http://*************:8089/xxl-job-admin/
    executor:
      address: ""
      ip:
      appName: pssp-backend
      appYQHandler: auditJobHandler
      port: 9001
      logpath: ./logs/xxl-job
      logretentiondays: -1
    ## 创建的sql任务，会默认找下面这个执行器
    sqlEngine:
      appName: pssp-backend-sql
      appHandler: fjzjJob
    mfliEngine:
      addresses: http://*************:8089/xxl-job-admin/
      appname: mfliengine
      appHandler: mfliengineAnalyzeJob
    redisTemplate:
      addresses: http://*************:8089/xxl-job-admin/
      appHandler: redisTemplateJob
#xxljob配置中 IP以及port需按实际项目配置

#引擎调度
engine:
  # 调度方式 xxl-job/webService
  dispatchType: xxl-job
  # 是否更新调度结果 true/false
  isUpdateResults: false
  # webService调用 接口地址
  webService:
    mfliAddresses: http://127.0.0.1:8082/mfliengine
    mttAddresses:

pssp:
  # 同时运行校验任务数
  check-thread-num: 5
  # 超时时间（小时）
  overtime: 3

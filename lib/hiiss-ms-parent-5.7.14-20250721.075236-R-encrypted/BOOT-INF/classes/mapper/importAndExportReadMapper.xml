<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yinhai.ta404.component.org.orguser.importexport.mapper.read.ImportAndExportReadMapper">
    <sql id="Base_List">
    u.USERID as USERID, u.LOGINID as LOGINID, u.PASSWORD as PASSWORD, u.PASSWORDDEFAULTNUM as PASSWORDDEFAULTNUM, u.<PERSON><PERSON>LASTMODIFYDATE as PWDLASTMODIFYDATE, u.ISLOCK as ISLOCK, u.ORDERNO as ORDERNO,
    u.NAME as NAME, u.SEX as SEX, u.IDCARDTYPE as IDCARDTYPE, u.IDCARDNO as IDCARDNO, u.MOBI<PERSON> as MO<PERSON>LE, u.CREATEUSER as CREATEUSER, u.CREATETIME as CREATETIME, u.MODIFYTIME as MODIFY<PERSON><PERSON>, u.DESTORY as DESTORY,
    u.ACCOUNTSOURCE as ACCOUNTSOURCE, u.EFFECTIVE as EFFECTIVE, u.EFFECTIVETIME as EFFECTIVETIME, u.JOBNUMBER as JOBNUMBER, u.STATE as STATE, u.BIRTHPLACE as BIRTHPLACE, u.ADDRESS as ADDRESS, u.ZIPCODE as ZIPCODE,
    u.EMAIL as EMAIL, u.PHONE as PHONE, u.EDUCATION as EDUCATION, u.GRADUATESCHOOL as GRADUATESCHOOL, u.WORKPLACE as WORKPLACE, u.FIELD01 as FIELD01, u.FIELD02 as FIELD02, u.FIELD03 as FIELD03, u.FIELD04 as FIELD04,
    u.FIELD05 as FIELD05, u.FIELD06 as FIELD06, u.FIELD07 as FIELD07, u.FIELD08 as FIELD08, u.FIELD09 as FIELD09, u.FIELD10 as FIELD10
    </sql>

    <sql id="Base_Column_List">
            ORGID      ,
            ORGNAME    ,
            SPELL      ,
            PARENTID   ,
            IDPATH     ,
            NAMEPATH   ,
            CUSTOMNO   ,
            ORDERNO    ,
            ORGLEVEL   ,
            AREA       ,
            EFFECTIVE  ,
            ORGTYPE    ,
            CREATEUSER ,
            CREATETIME ,
            MODIFYTIME ,
            ORGMANAGER ,
            ORGCODE    ,
            CONTACTS   ,
            ADDRESS    ,
            TEL        ,
            FIELD01    ,
            FIELD02    ,
            FIELD03    ,
            FIELD04    ,
            FIELD05    ,
            FIELD06    ,
            FIELD07    ,
            FIELD08    ,
            FIELD09    ,
            FIELD10
    </sql>

    <!--  用户按id导出为excel  -->
    <select id="queryUserExcel" resultType="com.yinhai.ta404.component.org.core.vo.TaUserExcelVo">
        SELECT
        <include refid="Base_List"/>
        from TAUSER u
        WHERE EXISTS(SELECT 1 FROM tauserorg uo WHERE u.userid = uo.USERID)
        AND u.userid IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId,jdbcType=VARCHAR}
        </foreach>
    </select>

    <!--  用户按id导出组织关系  -->
    <select id="queryUserOrgExcel" resultType="com.yinhai.ta404.component.org.core.vo.TaUserOrgExcelVo">
        SELECT
        tu.name,tu.loginid,tao.orgname, tao.namepath
        FROM tauser tu,taorg tao
        WHERE EXISTS(SELECT 1 FROM tauserorg tuo WHERE tuo.userid = tu.userid AND tuo.orgid = tao.orgid)
        AND tu.userid IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId,jdbcType=VARCHAR}
        </foreach>
    </select>

    <!--  用户全部导出为excel  -->
    <select id="queryAllUser" resultType="com.yinhai.ta404.component.org.core.vo.TaUserExcelVo">
        select
        <include refid="Base_List"/>
        from TAUSER u
        WHERE EXISTS(SELECT 1 FROM tauserorg uo WHERE u.userid = uo.USERID)
    </select>
    <!--  用户全部导出时对应组织关系  -->
    <select id="queryAllUserOrg" resultType="com.yinhai.ta404.component.org.core.vo.TaUserOrgExcelVo">
        SELECT
        tu.name,tu.loginid,tao.orgname, tao.namepath
        FROM tauser tu,taorg tao
        WHERE EXISTS(SELECT 1 FROM tauserorg tuo WHERE tuo.userid = tu.userid AND tuo.orgid = tao.orgid)
    </select>

    <!--  组织按格式导出为excel  -->
    <select id="queryAllOrgEx" resultType="com.yinhai.ta404.component.org.core.vo.TaOrgExcelVo">
        SELECT (SELECT tpo.namepath FROM taorg tpo WHERE tpo.orgid= tao.parentid) as pNamepath, tao.orgname, tao.spell, tao.orgtype, tao.effective, tao.customNo, tao.orderNo, tao.orgLevel, tao.area, tao.orgManager, tao.orgCode, tao.contacts, tao.address, tao.tel
        FROM taorg tao
        WHERE tao.EFFECTIVE = '1'
        AND tao.DESTORY = '0'
        ORDER BY tao.namepath
    </select>
    <select id="queryAllOrgExInt" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM (
            SELECT (SELECT tpo.namepath FROM taorg tpo WHERE tpo.orgid= tao.parentid) as pNamepath, tao.orgname, tao.spell, tao.orgtype, tao.effective
        FROM taorg tao
        WHERE tao.EFFECTIVE = '1'
        AND tao.DESTORY = '0'
        )AS count
    </select>
    <!--  组织按id导出为excel  -->
    <select id="querySearchOrgEx" resultType="com.yinhai.ta404.component.org.core.vo.TaOrgExcelVo">
        SELECT (SELECT tpo.namepath FROM taorg tpo WHERE tpo.orgid= tao.parentid) as
        pNamepath, tao.orgname, tao.spell, tao.orgtype, tao.effective
        FROM taorg tao
        where tao.orgid IN
        <foreach collection="orgIds" item="orgId" open="(" separator="," close=")">
            #{orgId,jdbcType=VARCHAR}
        </foreach>
        order by tao.namepath
    </select>

    <select id="queryOrgByOrgPath" resultType="com.yinhai.ta404.component.org.core.vo.TaOrgVo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM taorg
        where namepath = #{pNamePath,jdbcType=VARCHAR}
        and destory = '0'
    </select>

    <select id="queryExistOrgByParentIdAndOrgName" resultType="java.lang.Integer">
        select count(1)
        from taorg
        where destory = '0'
        and  orgName = #{orgName,jdbcType=VARCHAR}
        and  parentId = #{parentId,jdbcType=VARCHAR}
    </select>
</mapper>

ta404:
  component:
    cas:
      #单点登录服务器登录页地址
      casServerLoginUrl: http://172.20.23.80/ncasserver/login
      #单点服务器地址
      casServerUrlPrefix: http://172.20.23.80/ncasserver
      #接入单点应用地址
      serverName: http://172.20.23.80/hiiss/j_spring_cas_security_check
      #单点服务器退出地址
      ssoLogoutUrl: http://172.20.23.80/ncasserver/logout
      #固定写法，spring security集成cas单点登录地址
      filterProcessUrl: /j_spring_cas_security_check
    security:
      open-sso: true
      openSso: true
    org:
      portal:
        sys-code: miim
casserver:
  service:
    call-type: http
  apis:
    # 门户地址
    rest-url: http://172.20.23.80/nportal/
    wsdl-url:
    wsdl-namespace:
    service-keys:
      queryUserByLoginId: api/userMenuRestService/queryUserInfoByLoginId
      queryResourceEffectiveWithUserId: api/userMenuRestService/queryResourceByUserId
      queryRootChildrenMenus: api/userMenuRestService/queryRootChildrenMenus
      isDeveloper: api/userMenuRestService/isDeveloper
      getEnableRolesByUserId: api/userMenuRestService/getEnableRolesByUserId
      queryAreaWithAreaId: api/userMenuRestService/queryAreaWithAreaId
      queryAllElement: api/userMenuRestService/queryAllElement

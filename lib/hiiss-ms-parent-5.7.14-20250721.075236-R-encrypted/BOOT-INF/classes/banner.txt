
${AnsiColor.BLUE}888888    db          dP88   dP"Yb    dP88
  88     dPYb        dP 88  dP   Yb  dP 88
  88    dP__Yb      d888888 Yb   dP d888888
  88   dP""""Yb         88   YbodP      88
${AnsiColor.BRIGHT_RED}                   _ooOoo_${AnsiColor.BRIGHT_GREEN}
${AnsiColor.BRIGHT_RED}                  o8888888o${AnsiColor.BRIGHT_GREEN}
                  88" . "88
                  (| -_- |)
                  O\  =  /O
               ____/`---'\____
             .'  \\|     |//  `.
            /  \\|||  :  |||//  \
           /  _||||| -:- |||||-  \
           |   | \\\  -  /// |   |
           | \_|  ''\---/''  |   |
           \  .-\__  `-`  ___/-. /
         ___`. .'  /--.--\  `. . __
      ."" '<  `.___\_<|>_/___.'  >'"".
     | | :  `- \`.;`\ _ /`;.`/ - ` : | |
     \  \ `-.   \_ __\ /__ _/   .-` /  /
======`-.____`-.___\_____/___.-`____.-'======
                   `=---='
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
            佛祖助阵       永不宕机

${ta404.application.name}-${ta404.application.version}
Base on Spring Boot ${spring-boot.version}
==================================================

---- 5.7.13版本 2025-06-24 【工单-V5-协和、普仁】按照规则/项目自行维护违规级别并限制违规时的可选操作 -----------
ALTER TABLE mtt.mtt_violation_modify
    ADD COLUMN control_level VARCHAR(6) DEFAULT '3';

COMMENT ON COLUMN mtt.mtt_violation_modify.control_level IS '管控等级';

INSERT INTO "tadict" ("name", "type", "label", "value", "parentvalue", "sort", "authority", "cssclass", "cssstyle", "remarks", "createdate", "createuser", "version", "status", "field01", "field02", "field03", "field04", "field05", "system", "newtype") VALUES ('预审结果', 'CONTROLLEVEL', '强制管控', '1', NULL, '10', '0', NULL, NULL, NULL, '2022-07-15 09:39:08', '1', '0', '1', NULL, NULL, NULL, NULL, '1', '1', '0');
INSERT INTO "tadict" ("name", "type", "label", "value", "parentvalue", "sort", "authority", "cssclass", "cssstyle", "remarks", "createdate", "createuser", "version", "status", "field01", "field02", "field03", "field04", "field05", "system", "newtype") VALUES ('预审结果', 'CONTROLLEVEL', '条件放行', '2', NULL, '20', '0', NULL, NULL, NULL, '2022-07-15 09:39:25', '1', '0', '1', NULL, NULL, NULL, NULL, '1', '1', '0');
INSERT INTO "tadict" ("name", "type", "label", "value", "parentvalue", "sort", "authority", "cssclass", "cssstyle", "remarks", "createdate", "createuser", "version", "status", "field01", "field02", "field03", "field04", "field05", "system", "newtype") VALUES ('预审结果', 'CONTROLLEVEL', '预警提示', '3', NULL, '30', '0', NULL, NULL, NULL, '2022-07-15 09:39:48', '1', '0', '1', NULL, NULL, NULL, NULL, '1', '1', '0');

----------------- 5.7.13版本 申诉功能升级，事后审核新增字段 2025-07-15 ------------------------------------
-- poolarea_no	 参保地统筹区编号 字符型	10
-- poolarea_name	 参保地统筹区名称  字符型	50
-- bkkp_sn	 记账流水号  字符型	30

ALTER TABLE "kf38_file" ADD COLUMN "up_type" VARCHAR (5) DEFAULT '1';
COMMENT ON COLUMN "kf38_file"."up_type" IS '文件上传来源：1 申述处理；2 补充附件';

-- 添加列
ALTER TABLE settlement_audit_kc21
    ADD COLUMN poolarea_no   VARCHAR(10),
    ADD COLUMN poolarea_name VARCHAR(50),
    ADD COLUMN bkkp_sn       VARCHAR(30);

-- 添加字段注释
COMMENT ON COLUMN settlement_audit_kc21.poolarea_no   IS '参保地统筹区编号';
COMMENT ON COLUMN settlement_audit_kc21.poolarea_name IS '参保地统筹区名称';
COMMENT ON COLUMN settlement_audit_kc21.bkkp_sn       IS '记账流水号';

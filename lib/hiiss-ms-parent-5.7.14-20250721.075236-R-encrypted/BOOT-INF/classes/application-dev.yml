ta404:
  modules:
    refresh-mapper-xml:
      enabled: false  # 默认开发模式开启XML 刷新
    runqian:
      enable: false
    captcha:
      number-check-code-level: 1
      user-check-code: false
      captcha-type: "simple"
      password-validation-error-number: 0
      cacheNumber: 10000
      timingClear: 180
  component:
    security:
      db-encoder-type-target: Sm3
      password-useful-life: 30
      maximum-sessions: 1
      logoutUrl: /logout
      passwordPolicyList:
        - errorNum: 5
          lockTime: 10
          timeInterval: 90
      permit-urls:
        - /druid/**
        - /emr/queryEmrPageDetail
        - /indexRestService/getCryptoInfo
        - /indexRestService/getToken
        - /indexRestService/healthCheck
        - /captcha/**
        - /mtt/api/engLoad/**
        - /mtt/api/ruleSearch/**
        - /webjars/**
        - /v2/api-docs
        - /appeal/**
        - /codetable/getCode
        - /largeScreenMonitor/**
        - /nightAudit/**
        - /thirdLoginService/login
        - /thirdLoginService/loginNoPassword
        - /services/**
        - /doc.html
        - /webjars/**
        - /swagger-resources/**
        - /v2/**
        - /ruleQuery/**
        - /mtt/localruleconfig/**
        - /mtt/taskjob/**
        - /assistantWindow/**
        - /refusaldata/**
        - /hiddscgPoint/**
        - /diagnosticMatching/**
        - /drWorkBench/**
        - /mediSecuBureauWorkBenchRest/**
        - /assistantWindow/clientLogin
        - /auditEffectAnalysis/**
        - /departmentAnalysis/**
        - /miimCommonRead/**
        - /dischargeClinic/**
        - /inpatientGather/**
        - /detailsOverview/**
        - /assistantWindow/querySupport
        - /mttRuleCustom/*
        - /issueInventory/**
        - /individualDemand/dscgInfoSummary/**
        - /mttRuleCustom/getRuleTreeData
        - /inpatientClinic/**
        - /individualDemand/dscgInfoSummary/**
        - /diseaseManage/**
        - /dscgAudit/**
        - /indexWarning/**
        - /indexStatistics/**
        - /complainFile/**
        - /selfPayPrint/**
#        - /js/**
      # 是否加密用户名,开启会在登录或登录页修改密码时对用户名加密, 默认false
      encrypt-login-id: true
      # 密码等级,从1到4,默认等级为3
      #      4种字符类型：0-9，a-z，A-Z，特殊字符
      #      难度等级1:6位纯数字
      #      难度等级2:4选2，8-20位
      #      难度等级3:4选3，8-20位
      #      难度等级4:4选4，8-20位
      password-level: 3
#      login-permit-urls:
#        - /**
    org:
      # 多组组织模式。1启用多组织模式
      multiple-org: 1
      multiple-role: true
  log:
    output-type: console,file   # 日志输出目的地，目前支持控制台、文件、kafka，如果需要增加别的输出，可以扩展
    package-level: # 日志级别，按包名区分
      com.yinhai: debug
      org.springframework: INFO
      org.springframework.boot: INFO
      org.hibernate.SQL: ERROR
      org.apache.tomcat: TRACE
      org.apache.catalina: TRACE
      org.eclipse.jetty: TRACE
      com.codingapi: ERROR
    appender-type: #appender类型，默认提供三种appender（console、file、kafka）
      console: # 控制台配置
        appender-loader-class: com.yinhai.ta404.core.log.logback.config.ConsoleAppenderLoader    # 控制台配置加载器类名
        format: "%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr %clr(%logger{36}){cyan} %clr(:){faint} %clr(%msg%n){faint}"    #日志输出格式
        use-all-package-level: false    # 是否使用package-level所配置的包和包的日志级别（true：使用，false：不使用）
        monitor-packages: # 需要把日志输出到控制台的包（注意：在ta404.log.input.level里配置了的包，才能在这里配置）
          - com.yinhai
          - org.springframework
          - org.springframework.boot
          - com.codingapi
      file: # 文件配置
        appender-loader-class: com.yinhai.ta404.core.log.logback.config.RollingFileAppenderLoader    # 文件配置加载器类名
        file-name-pattern: ms_logs/file_%d{yyyy-MM-dd}_%i.log    # 配置日志文件的路径和命名规则
        format: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} : %msg%n"    # 日志输出格式
        max-history: 15    # 文件最大保留天数
        max-file-Size: 10mb    # 单个文件最大容量(单位可以是kb,mb,gb)
        clear-size: 10gb    # 所有文件最大容量(单位可以是kb,mb,gb)
        use-all-package-level: false    # 是否使用package-level所配置的包和包的日志级别（true：使用，false：不使用）
        monitor-packages: # 同上
          - com.yinhai
          - org.springframework
          - org.springframework.boot
          - com.codingapi

  limit:
    repeat-extra-url:
      - /demo/uploadRestService/chunk
      - /indexRestService/healthCheck
      - /demo/uploadRestService/mergeFile
      - /org/orguser/userManagementRestService/queryEffectiveUser
      - /org/authority/authorityAgentRestService/queryReAgentUsersByOrgId
      - /codetable/getCode
      - /indexRestService/getCurUserAccount
      - /org/orguser/orgManagementRestService/getOrgByAsync
      - /org/authority/roleAuthorityManagementRestService/queryCurrentAdminRoleWrapeOrgTree
      - /org/authority/examinerAuthorityRestService/queryOrgTreeByAsync
      - /statisticsModal/queryStatisticsUserChartData
      - /largeScreenMonitor/**
      - /mtt/api/engLoad/**
context:
  listener:
    classes: com.yinhai.ta404.autoconfigure.AutoConfigure



(function(e,t){"object"===typeof exports&&"object"===typeof module?module.exports=t():"function"===typeof define&&define.amd?define("pdfjs-dist/build/pdf",[],t):"object"===typeof exports?exports["pdfjs-dist/build/pdf"]=t():e["pdfjs-dist/build/pdf"]=e.pdfjsDistBuildPdf=t()})(this,(function(){return function(e){var t={};function r(n){if(t[n])return t[n].exports;var i=t[n]={i:n,l:!1,exports:{}};return e[n].call(i.exports,i,i.exports,r),i.l=!0,i.exports}return r.m=e,r.c=t,r.i=function(e){return e},r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{configurable:!1,enumerable:!0,get:n})},r.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=15)}([function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.unreachable=t.warn=t.utf8StringToString=t.stringToUTF8String=t.stringToPDFString=t.stringToBytes=t.string32=t.shadow=t.setVerbosityLevel=t.ReadableStream=t.removeNullCharacters=t.readUint32=t.readUint16=t.readInt8=t.log2=t.loadJpegStream=t.isEvalSupported=t.isLittleEndian=t.createValidAbsoluteUrl=t.isSameOrigin=t.isNodeJS=t.isSpace=t.isString=t.isNum=t.isInt=t.isEmptyObj=t.isBool=t.isArrayBuffer=t.isArray=t.info=t.globalScope=t.getVerbosityLevel=t.getLookupTableFactory=t.deprecated=t.createObjectURL=t.createPromiseCapability=t.createBlob=t.bytesToString=t.assert=t.arraysToBytes=t.arrayByteLength=t.FormatError=t.XRefParseException=t.Util=t.UnknownErrorException=t.UnexpectedResponseException=t.TextRenderingMode=t.StreamType=t.StatTimer=t.PasswordResponses=t.PasswordException=t.PageViewport=t.NotImplementedException=t.NativeImageDecoding=t.MissingPDFException=t.MissingDataException=t.MessageHandler=t.InvalidPDFException=t.AbortException=t.CMapCompressionType=t.ImageKind=t.FontType=t.AnnotationType=t.AnnotationFlag=t.AnnotationFieldFlag=t.AnnotationBorderStyleType=t.UNSUPPORTED_FEATURES=t.VERBOSITY_LEVELS=t.OPS=t.IDENTITY_MATRIX=t.FONT_IDENTITY_MATRIX=void 0;var n="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};r(16);var i=r(17),a="undefined"!==typeof window&&window.Math===Math?window:"undefined"!==typeof global&&global.Math===Math?global:"undefined"!==typeof self&&self.Math===Math?self:void 0,s=[.001,0,0,.001,0,0],o={NONE:"none",DECODE:"decode",DISPLAY:"display"},l={FILL:0,STROKE:1,FILL_STROKE:2,INVISIBLE:3,FILL_ADD_TO_PATH:4,STROKE_ADD_TO_PATH:5,FILL_STROKE_ADD_TO_PATH:6,ADD_TO_PATH:7,FILL_STROKE_MASK:3,ADD_TO_PATH_FLAG:4},c={GRAYSCALE_1BPP:1,RGB_24BPP:2,RGBA_32BPP:3},u={TEXT:1,LINK:2,FREETEXT:3,LINE:4,SQUARE:5,CIRCLE:6,POLYGON:7,POLYLINE:8,HIGHLIGHT:9,UNDERLINE:10,SQUIGGLY:11,STRIKEOUT:12,STAMP:13,CARET:14,INK:15,POPUP:16,FILEATTACHMENT:17,SOUND:18,MOVIE:19,WIDGET:20,SCREEN:21,PRINTERMARK:22,TRAPNET:23,WATERMARK:24,THREED:25,REDACT:26},h={INVISIBLE:1,HIDDEN:2,PRINT:4,NOZOOM:8,NOROTATE:16,NOVIEW:32,READONLY:64,LOCKED:128,TOGGLENOVIEW:256,LOCKEDCONTENTS:512},d={READONLY:1,REQUIRED:2,NOEXPORT:4,MULTILINE:4096,PASSWORD:8192,NOTOGGLETOOFF:16384,RADIO:32768,PUSHBUTTON:65536,COMBO:131072,EDIT:262144,SORT:524288,FILESELECT:1048576,MULTISELECT:2097152,DONOTSPELLCHECK:4194304,DONOTSCROLL:8388608,COMB:16777216,RICHTEXT:33554432,RADIOSINUNISON:33554432,COMMITONSELCHANGE:67108864},f={SOLID:1,DASHED:2,BEVELED:3,INSET:4,UNDERLINE:5},p={UNKNOWN:0,FLATE:1,LZW:2,DCT:3,JPX:4,JBIG:5,A85:6,AHX:7,CCF:8,RL:9},m={UNKNOWN:0,TYPE1:1,TYPE1C:2,CIDFONTTYPE0:3,CIDFONTTYPE0C:4,TRUETYPE:5,CIDFONTTYPE2:6,TYPE3:7,OPENTYPE:8,TYPE0:9,MMTYPE1:10},g={errors:0,warnings:1,infos:5},v={NONE:0,BINARY:1,STREAM:2},b={dependency:1,setLineWidth:2,setLineCap:3,setLineJoin:4,setMiterLimit:5,setDash:6,setRenderingIntent:7,setFlatness:8,setGState:9,save:10,restore:11,transform:12,moveTo:13,lineTo:14,curveTo:15,curveTo2:16,curveTo3:17,closePath:18,rectangle:19,stroke:20,closeStroke:21,fill:22,eoFill:23,fillStroke:24,eoFillStroke:25,closeFillStroke:26,closeEOFillStroke:27,endPath:28,clip:29,eoClip:30,beginText:31,endText:32,setCharSpacing:33,setWordSpacing:34,setHScale:35,setLeading:36,setFont:37,setTextRenderingMode:38,setTextRise:39,moveText:40,setLeadingMoveText:41,setTextMatrix:42,nextLine:43,showText:44,showSpacedText:45,nextLineShowText:46,nextLineSetSpacingShowText:47,setCharWidth:48,setCharWidthAndBounds:49,setStrokeColorSpace:50,setFillColorSpace:51,setStrokeColor:52,setStrokeColorN:53,setFillColor:54,setFillColorN:55,setStrokeGray:56,setFillGray:57,setStrokeRGBColor:58,setFillRGBColor:59,setStrokeCMYKColor:60,setFillCMYKColor:61,shadingFill:62,beginInlineImage:63,beginImageData:64,endInlineImage:65,paintXObject:66,markPoint:67,markPointProps:68,beginMarkedContent:69,beginMarkedContentProps:70,endMarkedContent:71,beginCompat:72,endCompat:73,paintFormXObjectBegin:74,paintFormXObjectEnd:75,beginGroup:76,endGroup:77,beginAnnotations:78,endAnnotations:79,beginAnnotation:80,endAnnotation:81,paintJpegXObject:82,paintImageMaskXObject:83,paintImageMaskXObjectGroup:84,paintImageXObject:85,paintInlineImageXObject:86,paintInlineImageXObjectGroup:87,paintImageXObjectRepeat:88,paintImageMaskXObjectRepeat:89,paintSolidColorImageMask:90,constructPath:91},_=g.warnings;function y(e){_=e}function A(){return _}function S(e){g.infos}function w(e){g.warnings}function P(e){}function C(e){throw new Error(e)}function R(e,t){e||C(t)}var k={unknown:"unknown",forms:"forms",javaScript:"javaScript",smask:"smask",shadingPattern:"shadingPattern",font:"font"};function x(e,t){try{var r=new URL(e);if(!r.origin||"null"===r.origin)return!1}catch(i){return!1}var n=new URL(t,r);return r.origin===n.origin}function T(e){if(!e)return!1;switch(e.protocol){case"http:":case"https:":case"ftp:":case"mailto:":case"tel:":return!0;default:return!1}}function E(e,t){if(!e)return null;try{var r=t?new URL(e,t):new URL(e);if(T(r))return r}catch(n){}return null}function I(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!1}),r}function L(e){var t;return function(){return e&&(t=Object.create(null),e(t),e=null),t}}var D={NEED_PASSWORD:1,INCORRECT_PASSWORD:2},O=function(){function e(e,t){this.name="PasswordException",this.message=e,this.code=t}return e.prototype=new Error,e.constructor=e,e}(),F=function(){function e(e,t){this.name="UnknownErrorException",this.message=e,this.details=t}return e.prototype=new Error,e.constructor=e,e}(),N=function(){function e(e){this.name="InvalidPDFException",this.message=e}return e.prototype=new Error,e.constructor=e,e}(),M=function(){function e(e){this.name="MissingPDFException",this.message=e}return e.prototype=new Error,e.constructor=e,e}(),j=function(){function e(e,t){this.name="UnexpectedResponseException",this.message=e,this.status=t}return e.prototype=new Error,e.constructor=e,e}(),q=function(){function e(e){this.message=e}return e.prototype=new Error,e.prototype.name="NotImplementedException",e.constructor=e,e}(),U=function(){function e(e,t){this.begin=e,this.end=t,this.message="Missing data ["+e+", "+t+")"}return e.prototype=new Error,e.prototype.name="MissingDataException",e.constructor=e,e}(),W=function(){function e(e){this.message=e}return e.prototype=new Error,e.prototype.name="XRefParseException",e.constructor=e,e}(),B=function(){function e(e){this.message=e}return e.prototype=new Error,e.prototype.name="FormatError",e.constructor=e,e}(),z=function(){function e(e){this.name="AbortException",this.message=e}return e.prototype=new Error,e.constructor=e,e}(),G=/\x00/g;function H(e){return"string"!==typeof e?(w("The argument for removeNullCharacters must be a string."),e):e.replace(G,"")}function X(e){R(null!==e&&"object"===("undefined"===typeof e?"undefined":n(e))&&void 0!==e.length,"Invalid argument for bytesToString");var t=e.length,r=8192;if(t<r)return String.fromCharCode.apply(null,e);for(var i=[],a=0;a<t;a+=r){var s=Math.min(a+r,t),o=e.subarray(a,s);i.push(String.fromCharCode.apply(null,o))}return i.join("")}function Y(e){R("string"===typeof e,"Invalid argument for stringToBytes");for(var t=e.length,r=new Uint8Array(t),n=0;n<t;++n)r[n]=255&e.charCodeAt(n);return r}function V(e){return void 0!==e.length?e.length:(R(void 0!==e.byteLength),e.byteLength)}function J(e){if(1===e.length&&e[0]instanceof Uint8Array)return e[0];var t,r,n,i=0,a=e.length;for(t=0;t<a;t++)r=e[t],n=V(r),i+=n;var s=0,o=new Uint8Array(i);for(t=0;t<a;t++)r=e[t],r instanceof Uint8Array||(r="string"===typeof r?Y(r):new Uint8Array(r)),n=r.byteLength,o.set(r,s),s+=n;return o}function Q(e){return String.fromCharCode(e>>24&255,e>>16&255,e>>8&255,255&e)}function K(e){var t=1,r=0;while(e>t)t<<=1,r++;return r}function Z(e,t){return e[t]<<24>>24}function $(e,t){return e[t]<<8|e[t+1]}function ee(e,t){return(e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3])>>>0}function te(){var e=new Uint8Array(4);e[0]=1;var t=new Uint32Array(e.buffer,0,1);return 1===t[0]}function re(){try{return new Function(""),!0}catch(e){return!1}}var ne=[1,0,0,1,0,0],ie=function(){function e(){}var t=["rgb(",0,",",0,",",0,")"];e.makeCssRgb=function(e,r,n){return t[1]=e,t[3]=r,t[5]=n,t.join("")},e.transform=function(e,t){return[e[0]*t[0]+e[2]*t[1],e[1]*t[0]+e[3]*t[1],e[0]*t[2]+e[2]*t[3],e[1]*t[2]+e[3]*t[3],e[0]*t[4]+e[2]*t[5]+e[4],e[1]*t[4]+e[3]*t[5]+e[5]]},e.applyTransform=function(e,t){var r=e[0]*t[0]+e[1]*t[2]+t[4],n=e[0]*t[1]+e[1]*t[3]+t[5];return[r,n]},e.applyInverseTransform=function(e,t){var r=t[0]*t[3]-t[1]*t[2],n=(e[0]*t[3]-e[1]*t[2]+t[2]*t[5]-t[4]*t[3])/r,i=(-e[0]*t[1]+e[1]*t[0]+t[4]*t[1]-t[5]*t[0])/r;return[n,i]},e.getAxialAlignedBoundingBox=function(t,r){var n=e.applyTransform(t,r),i=e.applyTransform(t.slice(2,4),r),a=e.applyTransform([t[0],t[3]],r),s=e.applyTransform([t[2],t[1]],r);return[Math.min(n[0],i[0],a[0],s[0]),Math.min(n[1],i[1],a[1],s[1]),Math.max(n[0],i[0],a[0],s[0]),Math.max(n[1],i[1],a[1],s[1])]},e.inverseTransform=function(e){var t=e[0]*e[3]-e[1]*e[2];return[e[3]/t,-e[1]/t,-e[2]/t,e[0]/t,(e[2]*e[5]-e[4]*e[3])/t,(e[4]*e[1]-e[5]*e[0])/t]},e.apply3dTransform=function(e,t){return[e[0]*t[0]+e[1]*t[1]+e[2]*t[2],e[3]*t[0]+e[4]*t[1]+e[5]*t[2],e[6]*t[0]+e[7]*t[1]+e[8]*t[2]]},e.singularValueDecompose2dScale=function(e){var t=[e[0],e[2],e[1],e[3]],r=e[0]*t[0]+e[1]*t[2],n=e[0]*t[1]+e[1]*t[3],i=e[2]*t[0]+e[3]*t[2],a=e[2]*t[1]+e[3]*t[3],s=(r+a)/2,o=Math.sqrt((r+a)*(r+a)-4*(r*a-i*n))/2,l=s+o||1,c=s-o||1;return[Math.sqrt(l),Math.sqrt(c)]},e.normalizeRect=function(e){var t=e.slice(0);return e[0]>e[2]&&(t[0]=e[2],t[2]=e[0]),e[1]>e[3]&&(t[1]=e[3],t[3]=e[1]),t},e.intersect=function(t,r){function n(e,t){return e-t}var i=[t[0],t[2],r[0],r[2]].sort(n),a=[t[1],t[3],r[1],r[3]].sort(n),s=[];return t=e.normalizeRect(t),r=e.normalizeRect(r),(i[0]===t[0]&&i[1]===r[0]||i[0]===r[0]&&i[1]===t[0])&&(s[0]=i[1],s[2]=i[2],(a[0]===t[1]&&a[1]===r[1]||a[0]===r[1]&&a[1]===t[1])&&(s[1]=a[1],s[3]=a[2],s))},e.sign=function(e){return e<0?-1:1};var r=["","C","CC","CCC","CD","D","DC","DCC","DCCC","CM","","X","XX","XXX","XL","L","LX","LXX","LXXX","XC","","I","II","III","IV","V","VI","VII","VIII","IX"];return e.toRoman=function(e,t){R(de(e)&&e>0,"The number should be a positive integer.");var n,i=[];while(e>=1e3)e-=1e3,i.push("M");n=e/100|0,e%=100,i.push(r[n]),n=e/10|0,e%=10,i.push(r[10+n]),i.push(r[20+e]);var a=i.join("");return t?a.toLowerCase():a},e.appendToArray=function(e,t){Array.prototype.push.apply(e,t)},e.prependToArray=function(e,t){Array.prototype.unshift.apply(e,t)},e.extendObj=function(e,t){for(var r in t)e[r]=t[r]},e.getInheritableProperty=function(e,t,r){while(e&&!e.has(t))e=e.get("Parent");return e?r?e.getArray(t):e.get(t):null},e.inherit=function(e,t,r){for(var n in e.prototype=Object.create(t.prototype),e.prototype.constructor=e,r)e.prototype[n]=r[n]},e.loadScript=function(e,t){var r=document.createElement("script"),n=!1;r.setAttribute("src",e),t&&(r.onload=function(){n||t(),n=!0}),document.getElementsByTagName("head")[0].appendChild(r)},e}(),ae=function(){function e(e,t,r,n,i,a){this.viewBox=e,this.scale=t,this.rotation=r,this.offsetX=n,this.offsetY=i;var s,o,l,c,u,h,d,f,p=(e[2]+e[0])/2,m=(e[3]+e[1])/2;switch(r%=360,r=r<0?r+360:r,r){case 180:s=-1,o=0,l=0,c=1;break;case 90:s=0,o=1,l=1,c=0;break;case 270:s=0,o=-1,l=-1,c=0;break;default:s=1,o=0,l=0,c=-1;break}a&&(l=-l,c=-c),0===s?(u=Math.abs(m-e[1])*t+n,h=Math.abs(p-e[0])*t+i,d=Math.abs(e[3]-e[1])*t,f=Math.abs(e[2]-e[0])*t):(u=Math.abs(p-e[0])*t+n,h=Math.abs(m-e[1])*t+i,d=Math.abs(e[2]-e[0])*t,f=Math.abs(e[3]-e[1])*t),this.transform=[s*t,o*t,l*t,c*t,u-s*t*p-l*t*m,h-o*t*p-c*t*m],this.width=d,this.height=f,this.fontScale=t}return e.prototype={clone:function(t){t=t||{};var r="scale"in t?t.scale:this.scale,n="rotation"in t?t.rotation:this.rotation;return new e(this.viewBox.slice(),r,n,this.offsetX,this.offsetY,t.dontFlip)},convertToViewportPoint:function(e,t){return ie.applyTransform([e,t],this.transform)},convertToViewportRectangle:function(e){var t=ie.applyTransform([e[0],e[1]],this.transform),r=ie.applyTransform([e[2],e[3]],this.transform);return[t[0],t[1],r[0],r[1]]},convertToPdfPoint:function(e,t){return ie.applyInverseTransform([e,t],this.transform)}},e}(),se=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,728,711,710,729,733,731,730,732,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,8226,8224,8225,8230,8212,8211,402,8260,8249,8250,8722,8240,8222,8220,8221,8216,8217,8218,8482,64257,64258,321,338,352,376,381,305,322,339,353,382,0,8364];function oe(e){var t,r=e.length,n=[];if("þ"===e[0]&&"ÿ"===e[1])for(t=2;t<r;t+=2)n.push(String.fromCharCode(e.charCodeAt(t)<<8|e.charCodeAt(t+1)));else for(t=0;t<r;++t){var i=se[e.charCodeAt(t)];n.push(i?String.fromCharCode(i):e.charAt(t))}return n.join("")}function le(e){return decodeURIComponent(escape(e))}function ce(e){return unescape(encodeURIComponent(e))}function ue(e){for(var t in e)return!1;return!0}function he(e){return"boolean"===typeof e}function de(e){return"number"===typeof e&&(0|e)===e}function fe(e){return"number"===typeof e}function pe(e){return"string"===typeof e}function me(e){return e instanceof Array}function ge(e){return"object"===("undefined"===typeof e?"undefined":n(e))&&null!==e&&void 0!==e.byteLength}function ve(e){return 32===e||9===e||13===e||10===e}function be(){return"object"===("undefined"===typeof process?"undefined":n(process))&&process+""==="[object process]"}function _e(){var e={};return e.promise=new Promise((function(t,r){e.resolve=t,e.reject=r})),e}var ye=function(){function e(e,t,r){while(e.length<r)e+=t;return e}function t(){this.started=Object.create(null),this.times=[],this.enabled=!0}return t.prototype={time:function(e){this.enabled&&(e in this.started&&w("Timer is already running for "+e),this.started[e]=Date.now())},timeEnd:function(e){this.enabled&&(e in this.started||w("Timer has not been started for "+e),this.times.push({name:e,start:this.started[e],end:Date.now()}),delete this.started[e])},toString:function(){var t,r,n=this.times,i="",a=0;for(t=0,r=n.length;t<r;++t){var s=n[t]["name"];s.length>a&&(a=s.length)}for(t=0,r=n.length;t<r;++t){var o=n[t],l=o.end-o.start;i+=e(o["name"]," ",a)+" "+l+"ms\n"}return i}},t}(),Ae=function(e,t){if("undefined"!==typeof Blob)return new Blob([e],{type:t});throw new Error('The "Blob" constructor is not supported.')},Se=function(){var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";return function(t,r){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(!n&&URL.createObjectURL){var i=Ae(t,r);return URL.createObjectURL(i)}for(var a="data:"+r+";base64,",s=0,o=t.length;s<o;s+=3){var l=255&t[s],c=255&t[s+1],u=255&t[s+2],h=l>>2,d=(3&l)<<4|c>>4,f=s+1<o?(15&c)<<2|u>>6:64,p=s+2<o?63&u:64;a+=e[h]+e[d]+e[f]+e[p]}return a}}();function we(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;return e?new Promise((function(n,i){n(e.apply(r,t))})):Promise.resolve(void 0)}function Pe(e){if("object"!==("undefined"===typeof e?"undefined":n(e)))return e;switch(e.name){case"AbortException":return new z(e.message);case"MissingPDFException":return new M(e.message);case"UnexpectedResponseException":return new j(e.message,e.status);default:return new F(e.message,e.details)}}function Ce(e,t,r){t?e.resolve():e.reject(r)}function Re(e){return Promise.resolve(e).catch((function(){}))}function ke(e,t,r){var n=this;this.sourceName=e,this.targetName=t,this.comObj=r,this.callbackId=1,this.streamId=1,this.postMessageTransfers=!0,this.streamSinks=Object.create(null),this.streamControllers=Object.create(null);var i=this.callbacksCapabilities=Object.create(null),a=this.actionHandler=Object.create(null);this._onComObjOnMessage=function(e){var t=e.data;if(t.targetName===n.sourceName)if(t.stream)n._processStreamMessage(t);else if(t.isReply){var s=t.callbackId;if(!(t.callbackId in i))throw new Error("Cannot resolve callback "+s);var o=i[s];delete i[s],"error"in t?o.reject(Pe(t.error)):o.resolve(t.data)}else{if(!(t.action in a))throw new Error("Unknown action from worker: "+t.action);var l=a[t.action];if(t.callbackId){var c=n.sourceName,u=t.sourceName;Promise.resolve().then((function(){return l[0].call(l[1],t.data)})).then((function(e){r.postMessage({sourceName:c,targetName:u,isReply:!0,callbackId:t.callbackId,data:e})}),(function(e){e instanceof Error&&(e+=""),r.postMessage({sourceName:c,targetName:u,isReply:!0,callbackId:t.callbackId,error:e})}))}else t.streamId?n._createStreamSink(t):l[0].call(l[1],t.data)}},r.addEventListener("message",this._onComObjOnMessage)}function xe(e,t,r){var n=new Image;n.onload=function(){r.resolve(e,n)},n.onerror=function(){r.resolve(e,null),w("Error during JPEG image loading")},n.src=t}ke.prototype={on:function(e,t,r){var n=this.actionHandler;if(n[e])throw new Error('There is already an actionName called "'+e+'"');n[e]=[t,r]},send:function(e,t,r){var n={sourceName:this.sourceName,targetName:this.targetName,action:e,data:t};this.postMessage(n,r)},sendWithPromise:function(e,t,r){var n=this.callbackId++,i={sourceName:this.sourceName,targetName:this.targetName,action:e,data:t,callbackId:n},a=_e();this.callbacksCapabilities[n]=a;try{this.postMessage(i,r)}catch(s){a.reject(s)}return a.promise},sendWithStream:function(e,t,r,n){var a=this,s=this.streamId++,o=this.sourceName,l=this.targetName;return new i.ReadableStream({start:function(r){var n=_e();return a.streamControllers[s]={controller:r,startCall:n,isClosed:!1},a.postMessage({sourceName:o,targetName:l,action:e,streamId:s,data:t,desiredSize:r.desiredSize}),n.promise},pull:function(e){var t=_e();return a.streamControllers[s].pullCall=t,a.postMessage({sourceName:o,targetName:l,stream:"pull",streamId:s,desiredSize:e.desiredSize}),t.promise},cancel:function(e){var t=_e();return a.streamControllers[s].cancelCall=t,a.streamControllers[s].isClosed=!0,a.postMessage({sourceName:o,targetName:l,stream:"cancel",reason:e,streamId:s}),t.promise}},r)},_createStreamSink:function(e){var t=this,r=this,n=this.actionHandler[e.action],i=e.streamId,a=e.desiredSize,s=this.sourceName,o=e.sourceName,l=_e(),c=function(e){var r=e.stream,n=e.chunk,a=e.transfers,l=e.success,c=e.reason;t.postMessage({sourceName:s,targetName:o,stream:r,streamId:i,chunk:n,success:l,reason:c},a)},u={enqueue:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,r=arguments[2];if(!this.isCancelled){var n=this.desiredSize;this.desiredSize-=t,n>0&&this.desiredSize<=0&&(this.sinkCapability=_e(),this.ready=this.sinkCapability.promise),c({stream:"enqueue",chunk:e,transfers:r})}},close:function(){this.isCancelled||(c({stream:"close"}),delete r.streamSinks[i])},error:function(e){this.isCancelled||(this.isCancelled=!0,c({stream:"error",reason:e}))},sinkCapability:l,onPull:null,onCancel:null,isCancelled:!1,desiredSize:a,ready:null};u.sinkCapability.resolve(),u.ready=u.sinkCapability.promise,this.streamSinks[i]=u,we(n[0],[e.data,u],n[1]).then((function(){c({stream:"start_complete",success:!0})}),(function(e){c({stream:"start_complete",success:!1,reason:e})}))},_processStreamMessage:function(e){var t=this,r=this.sourceName,n=e.sourceName,i=e.streamId,a=function(e){var a=e.stream,s=e.success,o=e.reason;t.comObj.postMessage({sourceName:r,targetName:n,stream:a,success:s,streamId:i,reason:o})},s=function(){Promise.all([t.streamControllers[e.streamId].startCall,t.streamControllers[e.streamId].pullCall,t.streamControllers[e.streamId].cancelCall].map((function(e){return e&&Re(e.promise)}))).then((function(){delete t.streamControllers[e.streamId]}))};switch(e.stream){case"start_complete":Ce(this.streamControllers[e.streamId].startCall,e.success,Pe(e.reason));break;case"pull_complete":Ce(this.streamControllers[e.streamId].pullCall,e.success,Pe(e.reason));break;case"pull":if(!this.streamSinks[e.streamId]){a({stream:"pull_complete",success:!0});break}this.streamSinks[e.streamId].desiredSize<=0&&e.desiredSize>0&&this.streamSinks[e.streamId].sinkCapability.resolve(),this.streamSinks[e.streamId].desiredSize=e.desiredSize,we(this.streamSinks[e.streamId].onPull).then((function(){a({stream:"pull_complete",success:!0})}),(function(e){a({stream:"pull_complete",success:!1,reason:e})}));break;case"enqueue":R(this.streamControllers[e.streamId],"enqueue should have stream controller"),this.streamControllers[e.streamId].isClosed||this.streamControllers[e.streamId].controller.enqueue(e.chunk);break;case"close":if(R(this.streamControllers[e.streamId],"close should have stream controller"),this.streamControllers[e.streamId].isClosed)break;this.streamControllers[e.streamId].isClosed=!0,this.streamControllers[e.streamId].controller.close(),s();break;case"error":R(this.streamControllers[e.streamId],"error should have stream controller"),this.streamControllers[e.streamId].controller.error(Pe(e.reason)),s();break;case"cancel_complete":Ce(this.streamControllers[e.streamId].cancelCall,e.success,Pe(e.reason)),s();break;case"cancel":if(!this.streamSinks[e.streamId])break;we(this.streamSinks[e.streamId].onCancel,[Pe(e.reason)]).then((function(){a({stream:"cancel_complete",success:!0})}),(function(e){a({stream:"cancel_complete",success:!1,reason:e})})),this.streamSinks[e.streamId].sinkCapability.reject(Pe(e.reason)),this.streamSinks[e.streamId].isCancelled=!0,delete this.streamSinks[e.streamId];break;default:throw new Error("Unexpected stream case")}},postMessage:function(e,t){t&&this.postMessageTransfers?this.comObj.postMessage(e,t):this.comObj.postMessage(e)},destroy:function(){this.comObj.removeEventListener("message",this._onComObjOnMessage)}},t.FONT_IDENTITY_MATRIX=s,t.IDENTITY_MATRIX=ne,t.OPS=b,t.VERBOSITY_LEVELS=g,t.UNSUPPORTED_FEATURES=k,t.AnnotationBorderStyleType=f,t.AnnotationFieldFlag=d,t.AnnotationFlag=h,t.AnnotationType=u,t.FontType=m,t.ImageKind=c,t.CMapCompressionType=v,t.AbortException=z,t.InvalidPDFException=N,t.MessageHandler=ke,t.MissingDataException=U,t.MissingPDFException=M,t.NativeImageDecoding=o,t.NotImplementedException=q,t.PageViewport=ae,t.PasswordException=O,t.PasswordResponses=D,t.StatTimer=ye,t.StreamType=p,t.TextRenderingMode=l,t.UnexpectedResponseException=j,t.UnknownErrorException=F,t.Util=ie,t.XRefParseException=W,t.FormatError=B,t.arrayByteLength=V,t.arraysToBytes=J,t.assert=R,t.bytesToString=X,t.createBlob=Ae,t.createPromiseCapability=_e,t.createObjectURL=Se,t.deprecated=P,t.getLookupTableFactory=L,t.getVerbosityLevel=A,t.globalScope=a,t.info=S,t.isArray=me,t.isArrayBuffer=ge,t.isBool=he,t.isEmptyObj=ue,t.isInt=de,t.isNum=fe,t.isString=pe,t.isSpace=ve,t.isNodeJS=be,t.isSameOrigin=x,t.createValidAbsoluteUrl=E,t.isLittleEndian=te,t.isEvalSupported=re,t.loadJpegStream=xe,t.log2=K,t.readInt8=Z,t.readUint16=$,t.readUint32=ee,t.removeNullCharacters=H,t.ReadableStream=i.ReadableStream,t.setVerbosityLevel=y,t.shadow=I,t.string32=Q,t.stringToBytes=Y,t.stringToPDFString=oe,t.stringToUTF8String=le,t.utf8StringToString=ce,t.warn=w,t.unreachable=C},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DOMCMapReaderFactory=t.DOMCanvasFactory=t.DEFAULT_LINK_REL=t.getDefaultSetting=t.LinkTarget=t.getFilenameFromUrl=t.isValidUrl=t.isExternalLinkTargetSet=t.addLinkAttributes=t.RenderingCancelledException=t.CustomStyle=void 0;var n=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),i=r(0);function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var s="noopener noreferrer nofollow",o=function(){function e(){a(this,e)}return n(e,[{key:"create",value:function(e,t){if(e<=0||t<=0)throw new Error("invalid canvas size");var r=document.createElement("canvas"),n=r.getContext("2d");return r.width=e,r.height=t,{canvas:r,context:n}}},{key:"reset",value:function(e,t,r){if(!e.canvas)throw new Error("canvas is not specified");if(t<=0||r<=0)throw new Error("invalid canvas size");e.canvas.width=t,e.canvas.height=r}},{key:"destroy",value:function(e){if(!e.canvas)throw new Error("canvas is not specified");e.canvas.width=0,e.canvas.height=0,e.canvas=null,e.context=null}}]),e}(),l=function(){function e(t){var r=t.baseUrl,n=void 0===r?null:r,i=t.isCompressed,s=void 0!==i&&i;a(this,e),this.baseUrl=n,this.isCompressed=s}return n(e,[{key:"fetch",value:function(e){var t=this,r=e.name;return r?new Promise((function(e,n){var a=t.baseUrl+r+(t.isCompressed?".bcmap":""),s=new XMLHttpRequest;s.open("GET",a,!0),t.isCompressed&&(s.responseType="arraybuffer"),s.onreadystatechange=function(){if(s.readyState===XMLHttpRequest.DONE){if(200===s.status||0===s.status){var r=void 0;if(t.isCompressed&&s.response?r=new Uint8Array(s.response):!t.isCompressed&&s.responseText&&(r=(0,i.stringToBytes)(s.responseText)),r)return void e({cMapData:r,compressionType:t.isCompressed?i.CMapCompressionType.BINARY:i.CMapCompressionType.NONE})}n(new Error("Unable to load "+(t.isCompressed?"binary ":"")+"CMap at: "+a))}},s.send(null)})):Promise.reject(new Error("CMap name must be specified."))}}]),e}(),c=function(){var e=["ms","Moz","Webkit","O"],t=Object.create(null);function r(){}return r.getProp=function(r,n){if(1===arguments.length&&"string"===typeof t[r])return t[r];n=n||document.documentElement;var i,a,s=n.style;if("string"===typeof s[r])return t[r]=r;a=r.charAt(0).toUpperCase()+r.slice(1);for(var o=0,l=e.length;o<l;o++)if(i=e[o]+a,"string"===typeof s[i])return t[r]=i;return t[r]="undefined"},r.setProp=function(e,t,r){var n=this.getProp(e);"undefined"!==n&&(t.style[n]=r)},r}(),u=function(){function e(e,t){this.message=e,this.type=t}return e.prototype=new Error,e.prototype.name="RenderingCancelledException",e.constructor=e,e}(),h={NONE:0,SELF:1,BLANK:2,PARENT:3,TOP:4},d=["","_self","_blank","_parent","_top"];function f(e,t){var r=t&&t.url;if(e.href=e.title=r?(0,i.removeNullCharacters)(r):"",r){var n=t.target;"undefined"===typeof n&&(n=m("externalLinkTarget")),e.target=d[n];var a=t.rel;"undefined"===typeof a&&(a=m("externalLinkRel")),e.rel=a}}function p(e){var t=e.indexOf("#"),r=e.indexOf("?"),n=Math.min(t>0?t:e.length,r>0?r:e.length);return e.substring(e.lastIndexOf("/",n)+1,n)}function m(e){var t=i.globalScope.PDFJS;switch(e){case"pdfBug":return!!t&&t.pdfBug;case"disableAutoFetch":return!!t&&t.disableAutoFetch;case"disableStream":return!!t&&t.disableStream;case"disableRange":return!!t&&t.disableRange;case"disableFontFace":return!!t&&t.disableFontFace;case"disableCreateObjectURL":return!!t&&t.disableCreateObjectURL;case"disableWebGL":return!t||t.disableWebGL;case"cMapUrl":return t?t.cMapUrl:null;case"cMapPacked":return!!t&&t.cMapPacked;case"postMessageTransfers":return!t||t.postMessageTransfers;case"workerPort":return t?t.workerPort:null;case"workerSrc":return t?t.workerSrc:null;case"disableWorker":return!!t&&t.disableWorker;case"maxImageSize":return t?t.maxImageSize:-1;case"imageResourcesPath":return t?t.imageResourcesPath:"";case"isEvalSupported":return!t||t.isEvalSupported;case"externalLinkTarget":if(!t)return h.NONE;switch(t.externalLinkTarget){case h.NONE:case h.SELF:case h.BLANK:case h.PARENT:case h.TOP:return t.externalLinkTarget}return(0,i.warn)("PDFJS.externalLinkTarget is invalid: "+t.externalLinkTarget),t.externalLinkTarget=h.NONE,h.NONE;case"externalLinkRel":return t?t.externalLinkRel:s;case"enableStats":return!(!t||!t.enableStats);case"pdfjsNext":return!(!t||!t.pdfjsNext);default:throw new Error("Unknown default setting: "+e)}}function g(){var e=m("externalLinkTarget");switch(e){case h.NONE:return!1;case h.SELF:case h.BLANK:case h.PARENT:case h.TOP:return!0}}function v(e,t){(0,i.deprecated)("isValidUrl(), please use createValidAbsoluteUrl() instead.");var r=t?"http://example.com":null;return null!==(0,i.createValidAbsoluteUrl)(e,r)}t.CustomStyle=c,t.RenderingCancelledException=u,t.addLinkAttributes=f,t.isExternalLinkTargetSet=g,t.isValidUrl=v,t.getFilenameFromUrl=p,t.LinkTarget=h,t.getDefaultSetting=m,t.DEFAULT_LINK_REL=s,t.DOMCanvasFactory=o,t.DOMCMapReaderFactory=l},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.build=t.version=t._UnsupportedManager=t.setPDFNetworkStreamClass=t.PDFPageProxy=t.PDFDocumentProxy=t.PDFWorker=t.PDFDataRangeTransport=t.LoopbackPort=t.getDocument=void 0;var n=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),i="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a=r(0),s=r(1),o=r(12),l=r(11),c=r(6),u=r(14);function h(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var d,f=65536,p=!1,m=!1,g="undefined"!==typeof document&&document.currentScript?document.currentScript.src:null,v=null,b=!1;"undefined"===typeof window?(p=!0,"undefined"===typeof require.ensure&&(require.ensure=require("node-ensure")),b=!0):"undefined"!==typeof require&&"function"===typeof require.ensure&&(b=!0),"undefined"!==typeof requirejs&&requirejs.toUrl&&(d=requirejs.toUrl("pdfjs-dist/build/pdf.worker.js"));var _,y="undefined"!==typeof requirejs&&requirejs.load;function A(e){_=e}function S(e,t,r,n){var o,l=new R;if(arguments.length>1&&(0,a.deprecated)("getDocument is called with pdfDataRangeTransport, passwordCallback or progressCallback argument"),t&&(t instanceof k||(t=Object.create(t),t.length=e.length,t.initialData=e.initialData,t.abort||(t.abort=function(){})),e=Object.create(e),e.range=t),l.onPassword=r||null,l.onProgress=n||null,"string"===typeof e)o={url:e};else if((0,a.isArrayBuffer)(e))o={data:e};else if(e instanceof k)o={range:e};else{if("object"!==("undefined"===typeof e?"undefined":i(e)))throw new Error("Invalid parameter in getDocument, need either Uint8Array, string or a parameter object");if(!e.url&&!e.data&&!e.range)throw new Error("Invalid parameter object: need either .data, .range or .url");o=e}var c={},h=null,d=null,p=s.DOMCMapReaderFactory;for(var m in o)if("url"!==m||"undefined"===typeof window)if("range"!==m)if("worker"!==m)if("data"!==m||o[m]instanceof Uint8Array)"CMapReaderFactory"!==m?c[m]=o[m]:p=o[m];else{var g=o[m];if("string"===typeof g)c[m]=(0,a.stringToBytes)(g);else if("object"!==("undefined"===typeof g?"undefined":i(g))||null===g||isNaN(g.length)){if(!(0,a.isArrayBuffer)(g))throw new Error("Invalid PDF binary data: either typed array, string or array-like object is expected in the data property.");c[m]=new Uint8Array(g)}else c[m]=new Uint8Array(g)}else d=o[m];else h=o[m];else c[m]=new URL(o[m],window.location).href;if(c.rangeChunkSize=c.rangeChunkSize||f,c.ignoreErrors=!0!==c.stopAtErrors,void 0!==c.disableNativeImageDecoder&&(0,a.deprecated)("parameter disableNativeImageDecoder, use nativeImageDecoderSupport instead"),c.nativeImageDecoderSupport=c.nativeImageDecoderSupport||(!0===c.disableNativeImageDecoder?a.NativeImageDecoding.NONE:a.NativeImageDecoding.DECODE),c.nativeImageDecoderSupport!==a.NativeImageDecoding.DECODE&&c.nativeImageDecoderSupport!==a.NativeImageDecoding.NONE&&c.nativeImageDecoderSupport!==a.NativeImageDecoding.DISPLAY&&((0,a.warn)("Invalid parameter nativeImageDecoderSupport: need a state of enum {NativeImageDecoding}"),c.nativeImageDecoderSupport=a.NativeImageDecoding.DECODE),!d){var v=(0,s.getDefaultSetting)("workerPort");d=v?I.fromPort(v):new I,l._worker=d}var b=l.docId;return d.promise.then((function(){if(l.destroyed)throw new Error("Loading aborted");return w(d,c,h,b).then((function(e){if(l.destroyed)throw new Error("Loading aborted");var t=void 0;h?t=new u.PDFDataTransportStream(c,h):c.data||(t=new _({source:c,disableRange:(0,s.getDefaultSetting)("disableRange")}));var r=new a.MessageHandler(b,e,d.port);r.postMessageTransfers=d.postMessageTransfers;var n=new L(r,l,t,p);l._transport=n,r.send("Ready",null)}))})).catch(l._capability.reject),l}function w(e,t,r,n){return e.destroyed?Promise.reject(new Error("Worker was destroyed")):(t.disableAutoFetch=(0,s.getDefaultSetting)("disableAutoFetch"),t.disableStream=(0,s.getDefaultSetting)("disableStream"),t.chunkedViewerLoading=!!r,r&&(t.length=r.length,t.initialData=r.initialData),e.messageHandler.sendWithPromise("GetDocRequest",{docId:n,source:{data:t.data,url:t.url,password:t.password,disableAutoFetch:t.disableAutoFetch,rangeChunkSize:t.rangeChunkSize,length:t.length},maxImageSize:(0,s.getDefaultSetting)("maxImageSize"),disableFontFace:(0,s.getDefaultSetting)("disableFontFace"),disableCreateObjectURL:(0,s.getDefaultSetting)("disableCreateObjectURL"),postMessageTransfers:(0,s.getDefaultSetting)("postMessageTransfers")&&!m,docBaseUrl:t.docBaseUrl,nativeImageDecoderSupport:t.nativeImageDecoderSupport,ignoreErrors:t.ignoreErrors}).then((function(t){if(e.destroyed)throw new Error("Worker was destroyed");return t})))}v=b?function(e){require.ensure([],(function(){var t;t=require("./pdf.worker.js"),e(t.WorkerMessageHandler)}))}:y?function(e){requirejs(["pdfjs-dist/build/pdf.worker"],(function(t){e(t.WorkerMessageHandler)}))}:null;var P,C,R=function(){var e=0;function t(){this._capability=(0,a.createPromiseCapability)(),this._transport=null,this._worker=null,this.docId="d"+e++,this.destroyed=!1,this.onPassword=null,this.onProgress=null,this.onUnsupportedFeature=null}return t.prototype={get promise(){return this._capability.promise},destroy:function(){var e=this;this.destroyed=!0;var t=this._transport?this._transport.destroy():Promise.resolve();return t.then((function(){e._transport=null,e._worker&&(e._worker.destroy(),e._worker=null)}))},then:function(e,t){return this.promise.then.apply(this.promise,arguments)}},t}(),k=function(){function e(e,t){this.length=e,this.initialData=t,this._rangeListeners=[],this._progressListeners=[],this._progressiveReadListeners=[],this._readyCapability=(0,a.createPromiseCapability)()}return e.prototype={addRangeListener:function(e){this._rangeListeners.push(e)},addProgressListener:function(e){this._progressListeners.push(e)},addProgressiveReadListener:function(e){this._progressiveReadListeners.push(e)},onDataRange:function(e,t){for(var r=this._rangeListeners,n=0,i=r.length;n<i;++n)r[n](e,t)},onDataProgress:function(e){var t=this;this._readyCapability.promise.then((function(){for(var r=t._progressListeners,n=0,i=r.length;n<i;++n)r[n](e)}))},onDataProgressiveRead:function(e){var t=this;this._readyCapability.promise.then((function(){for(var r=t._progressiveReadListeners,n=0,i=r.length;n<i;++n)r[n](e)}))},transportReady:function(){this._readyCapability.resolve()},requestDataRange:function(e,t){throw new Error("Abstract method PDFDataRangeTransport.requestDataRange")},abort:function(){}},e}(),x=function(){function e(e,t,r){this.pdfInfo=e,this.transport=t,this.loadingTask=r}return e.prototype={get numPages(){return this.pdfInfo.numPages},get fingerprint(){return this.pdfInfo.fingerprint},getPage:function(e){return this.transport.getPage(e)},getPageIndex:function(e){return this.transport.getPageIndex(e)},getDestinations:function(){return this.transport.getDestinations()},getDestination:function(e){return this.transport.getDestination(e)},getPageLabels:function(){return this.transport.getPageLabels()},getPageMode:function(){return this.transport.getPageMode()},getAttachments:function(){return this.transport.getAttachments()},getJavaScript:function(){return this.transport.getJavaScript()},getOutline:function(){return this.transport.getOutline()},getMetadata:function(){return this.transport.getMetadata()},getData:function(){return this.transport.getData()},getDownloadInfo:function(){return this.transport.downloadInfoCapability.promise},getStats:function(){return this.transport.getStats()},cleanup:function(){this.transport.startCleanup()},destroy:function(){return this.loadingTask.destroy()}},e}(),T=function(){function e(e,t,r){this.pageIndex=e,this.pageInfo=t,this.transport=r,this.stats=new a.StatTimer,this.stats.enabled=(0,s.getDefaultSetting)("enableStats"),this.commonObjs=r.commonObjs,this.objs=new D,this.cleanupAfterRender=!1,this.pendingCleanup=!1,this.intentStates=Object.create(null),this.destroyed=!1}return e.prototype={get pageNumber(){return this.pageIndex+1},get rotate(){return this.pageInfo.rotate},get ref(){return this.pageInfo.ref},get userUnit(){return this.pageInfo.userUnit},get view(){return this.pageInfo.view},getViewport:function(e,t){return arguments.length<2&&(t=this.rotate),new a.PageViewport(this.view,e,t,0,0)},getAnnotations:function(e){var t=e&&e.intent||null;return this.annotationsPromise&&this.annotationsIntent===t||(this.annotationsPromise=this.transport.getAnnotations(this.pageIndex,t),this.annotationsIntent=t),this.annotationsPromise},render:function(e){var t=this,r=this.stats;r.time("Overall"),this.pendingCleanup=!1;var n="print"===e.intent?"print":"display",i=e.canvasFactory||new s.DOMCanvasFactory;this.intentStates[n]||(this.intentStates[n]=Object.create(null));var o=this.intentStates[n];o.displayReadyCapability||(o.receivingOperatorList=!0,o.displayReadyCapability=(0,a.createPromiseCapability)(),o.operatorList={fnArray:[],argsArray:[],lastChunk:!1},this.stats.time("Page Request"),this.transport.messageHandler.send("RenderPageRequest",{pageIndex:this.pageNumber-1,intent:n,renderInteractiveForms:!0===e.renderInteractiveForms}));var l=function(e){var n=o.renderTasks.indexOf(c);n>=0&&o.renderTasks.splice(n,1),t.cleanupAfterRender&&(t.pendingCleanup=!0),t._tryCleanup(),e?c.capability.reject(e):c.capability.resolve(),r.timeEnd("Rendering"),r.timeEnd("Overall")},c=new F(l,e,this.objs,this.commonObjs,o.operatorList,this.pageNumber,i);c.useRequestAnimationFrame="print"!==n,o.renderTasks||(o.renderTasks=[]),o.renderTasks.push(c);var u=c.task;return e.continueCallback&&((0,a.deprecated)("render is used with continueCallback parameter"),u.onContinue=e.continueCallback),o.displayReadyCapability.promise.then((function(e){t.pendingCleanup?l():(r.time("Rendering"),c.initializeGraphics(e),c.operatorListChanged())})).catch(l),u},getOperatorList:function(){function e(){if(n.operatorList.lastChunk){n.opListReadCapability.resolve(n.operatorList);var e=n.renderTasks.indexOf(r);e>=0&&n.renderTasks.splice(e,1)}}var t="oplist";this.intentStates[t]||(this.intentStates[t]=Object.create(null));var r,n=this.intentStates[t];return n.opListReadCapability||(r={},r.operatorListChanged=e,n.receivingOperatorList=!0,n.opListReadCapability=(0,a.createPromiseCapability)(),n.renderTasks=[],n.renderTasks.push(r),n.operatorList={fnArray:[],argsArray:[],lastChunk:!1},this.transport.messageHandler.send("RenderPageRequest",{pageIndex:this.pageIndex,intent:t})),n.opListReadCapability.promise},streamTextContent:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=100;return this.transport.messageHandler.sendWithStream("GetTextContent",{pageIndex:this.pageNumber-1,normalizeWhitespace:!0===e.normalizeWhitespace,combineTextItems:!0!==e.disableCombineTextItems},{highWaterMark:t,size:function(e){return e.items.length}})},getTextContent:function(e){e=e||{};var t=this.streamTextContent(e);return new Promise((function(e,r){function n(){i.read().then((function(t){var r=t.value,i=t.done;i?e(s):(a.Util.extendObj(s.styles,r.styles),a.Util.appendToArray(s.items,r.items),n())}),r)}var i=t.getReader(),s={items:[],styles:Object.create(null)};n()}))},_destroy:function(){this.destroyed=!0,this.transport.pageCache[this.pageIndex]=null;var e=[];return Object.keys(this.intentStates).forEach((function(t){if("oplist"!==t){var r=this.intentStates[t];r.renderTasks.forEach((function(t){var r=t.capability.promise.catch((function(){}));e.push(r),t.cancel()}))}}),this),this.objs.clear(),this.annotationsPromise=null,this.pendingCleanup=!1,Promise.all(e)},destroy:function(){(0,a.deprecated)("page destroy method, use cleanup() instead"),this.cleanup()},cleanup:function(){this.pendingCleanup=!0,this._tryCleanup()},_tryCleanup:function(){this.pendingCleanup&&!Object.keys(this.intentStates).some((function(e){var t=this.intentStates[e];return 0!==t.renderTasks.length||t.receivingOperatorList}),this)&&(Object.keys(this.intentStates).forEach((function(e){delete this.intentStates[e]}),this),this.objs.clear(),this.annotationsPromise=null,this.pendingCleanup=!1)},_startRenderPage:function(e,t){var r=this.intentStates[t];r.displayReadyCapability&&r.displayReadyCapability.resolve(e)},_renderPageChunk:function(e,t){var r,n,i=this.intentStates[t];for(r=0,n=e.length;r<n;r++)i.operatorList.fnArray.push(e.fnArray[r]),i.operatorList.argsArray.push(e.argsArray[r]);for(i.operatorList.lastChunk=e.lastChunk,r=0;r<i.renderTasks.length;r++)i.renderTasks[r].operatorListChanged();e.lastChunk&&(i.receivingOperatorList=!1,this._tryCleanup())}},e}(),E=function(){function e(t){h(this,e),this._listeners=[],this._defer=t,this._deferred=Promise.resolve(void 0)}return n(e,[{key:"postMessage",value:function(e,t){var r=this;function n(e){if("object"!==("undefined"===typeof e?"undefined":i(e))||null===e)return e;if(s.has(e))return s.get(e);var r,o;if((o=e.buffer)&&(0,a.isArrayBuffer)(o)){var l=t&&t.indexOf(o)>=0;return r=e===o?e:l?new e.constructor(o,e.byteOffset,e.byteLength):new e.constructor(e),s.set(e,r),r}for(var c in r=(0,a.isArray)(e)?[]:{},s.set(e,r),e){var u,h=e;while(!(u=Object.getOwnPropertyDescriptor(h,c)))h=Object.getPrototypeOf(h);"undefined"!==typeof u.value&&"function"!==typeof u.value&&(r[c]=n(u.value))}return r}if(this._defer){var s=new WeakMap,o={data:n(e)};this._deferred.then((function(){r._listeners.forEach((function(e){e.call(this,o)}),r)}))}else this._listeners.forEach((function(t){t.call(this,{data:e})}),this)}},{key:"addEventListener",value:function(e,t){this._listeners.push(t)}},{key:"removeEventListener",value:function(e,t){var r=this._listeners.indexOf(t);this._listeners.splice(r,1)}},{key:"terminate",value:function(){this._listeners=[]}}]),e}(),I=function(){var e=0;function t(){if("undefined"!==typeof d)return d;if((0,s.getDefaultSetting)("workerSrc"))return(0,s.getDefaultSetting)("workerSrc");if(g)return g.replace(/(\.(?:min\.)?js)(\?.*)?$/i,".worker$1$2");throw new Error("No PDFJS.workerSrc specified")}var r=void 0;function n(){if(r)return r.promise;r=(0,a.createPromiseCapability)();var e=v||function(e){a.Util.loadScript(t(),(function(){e(window.pdfjsDistBuildPdfWorker.WorkerMessageHandler)}))};return e(r.resolve),r.promise}function i(e){var t="importScripts('"+e+"');";return URL.createObjectURL(new Blob([t]))}var o=new WeakMap;function l(e,t){if(t&&o.has(t))throw new Error("Cannot use more than one PDFWorker per port");if(this.name=e,this.destroyed=!1,this.postMessageTransfers=!0,this._readyCapability=(0,a.createPromiseCapability)(),this._port=null,this._webWorker=null,this._messageHandler=null,t)return o.set(t,this),void this._initializeFromPort(t);this._initialize()}return l.prototype={get promise(){return this._readyCapability.promise},get port(){return this._port},get messageHandler(){return this._messageHandler},_initializeFromPort:function(e){this._port=e,this._messageHandler=new a.MessageHandler("main","worker",e),this._messageHandler.on("ready",(function(){})),this._readyCapability.resolve()},_initialize:function(){var e=this;if(!p&&!(0,s.getDefaultSetting)("disableWorker")&&"undefined"!==typeof Worker){var r=t();try{(0,a.isSameOrigin)(window.location.href,r)||(r=i(new URL(r,window.location).href));var n=new Worker(r),o=new a.MessageHandler("main","worker",n),l=function(){n.removeEventListener("error",c),o.destroy(),n.terminate(),e.destroyed?e._readyCapability.reject(new Error("Worker was destroyed")):e._setupFakeWorker()},c=function(){e._webWorker||l()};n.addEventListener("error",c),o.on("test",(function(t){if(n.removeEventListener("error",c),e.destroyed)l();else{var r=t&&t.supportTypedArray;r?(e._messageHandler=o,e._port=n,e._webWorker=n,t.supportTransfers||(e.postMessageTransfers=!1,m=!0),e._readyCapability.resolve(),o.send("configure",{verbosity:(0,a.getVerbosityLevel)()})):(e._setupFakeWorker(),o.destroy(),n.terminate())}})),o.on("console_log",(function(e){})),o.on("console_error",(function(e){})),o.on("ready",(function(t){if(n.removeEventListener("error",c),e.destroyed)l();else try{u()}catch(r){e._setupFakeWorker()}}));var u=function(){var e=(0,s.getDefaultSetting)("postMessageTransfers")&&!m,t=new Uint8Array([e?255:0]);try{o.send("test",t,[t.buffer])}catch(r){(0,a.info)("Cannot use postMessage transfers"),t[0]=0,o.send("test",t)}};return void u()}catch(h){(0,a.info)("The worker has been disabled.")}}this._setupFakeWorker()},_setupFakeWorker:function(){var t=this;p||(0,s.getDefaultSetting)("disableWorker")||((0,a.warn)("Setting up fake worker."),p=!0),n().then((function(r){if(t.destroyed)t._readyCapability.reject(new Error("Worker was destroyed"));else{var n=Uint8Array!==Float32Array,i=new E(n);t._port=i;var s="fake"+e++,o=new a.MessageHandler(s+"_worker",s,i);r.setup(o,i);var l=new a.MessageHandler(s,s+"_worker",i);t._messageHandler=l,t._readyCapability.resolve()}}))},destroy:function(){this.destroyed=!0,this._webWorker&&(this._webWorker.terminate(),this._webWorker=null),o.delete(this._port),this._port=null,this._messageHandler&&(this._messageHandler.destroy(),this._messageHandler=null)}},l.fromPort=function(e){return o.has(e)?o.get(e):new l(null,e)},l}(),L=function(){function e(e,t,r,n){this.messageHandler=e,this.loadingTask=t,this.commonObjs=new D,this.fontLoader=new o.FontLoader(t.docId),this.CMapReaderFactory=new n({baseUrl:(0,s.getDefaultSetting)("cMapUrl"),isCompressed:(0,s.getDefaultSetting)("cMapPacked")}),this.destroyed=!1,this.destroyCapability=null,this._passwordCapability=null,this._networkStream=r,this._fullReader=null,this._lastProgress=null,this.pageCache=[],this.pagePromises=[],this.downloadInfoCapability=(0,a.createPromiseCapability)(),this.setupMessageHandler()}return e.prototype={destroy:function(){var e=this;if(this.destroyCapability)return this.destroyCapability.promise;this.destroyed=!0,this.destroyCapability=(0,a.createPromiseCapability)(),this._passwordCapability&&this._passwordCapability.reject(new Error("Worker was destroyed during onPassword callback"));var t=[];this.pageCache.forEach((function(e){e&&t.push(e._destroy())})),this.pageCache=[],this.pagePromises=[];var r=this.messageHandler.sendWithPromise("Terminate",null);return t.push(r),Promise.all(t).then((function(){e.fontLoader.clear(),e._networkStream&&e._networkStream.cancelAllRequests(),e.messageHandler&&(e.messageHandler.destroy(),e.messageHandler=null),e.destroyCapability.resolve()}),this.destroyCapability.reject),this.destroyCapability.promise},setupMessageHandler:function(){var e=this.messageHandler,t=this.loadingTask;e.on("GetReader",(function(e,t){var r=this;(0,a.assert)(this._networkStream),this._fullReader=this._networkStream.getFullReader(),this._fullReader.onProgress=function(e){r._lastProgress={loaded:e.loaded,total:e.total}},t.onPull=function(){r._fullReader.read().then((function(e){var r=e.value,n=e.done;n?t.close():((0,a.assert)((0,a.isArrayBuffer)(r)),t.enqueue(new Uint8Array(r),1,[r]))})).catch((function(e){t.error(e)}))},t.onCancel=function(e){r._fullReader.cancel(e)}}),this),e.on("ReaderHeadersReady",(function(e){var t=this,r=(0,a.createPromiseCapability)(),n=this._fullReader;return n.headersReady.then((function(){if(!n.isStreamingSupported||!n.isRangeSupported){if(t._lastProgress){var e=t.loadingTask;e.onProgress&&e.onProgress(t._lastProgress)}n.onProgress=function(e){var r=t.loadingTask;r.onProgress&&r.onProgress({loaded:e.loaded,total:e.total})}}r.resolve({isStreamingSupported:n.isStreamingSupported,isRangeSupported:n.isRangeSupported,contentLength:n.contentLength})}),r.reject),r.promise}),this),e.on("GetRangeReader",(function(e,t){(0,a.assert)(this._networkStream);var r=this._networkStream.getRangeReader(e.begin,e.end);t.onPull=function(){r.read().then((function(e){var r=e.value,n=e.done;n?t.close():((0,a.assert)((0,a.isArrayBuffer)(r)),t.enqueue(new Uint8Array(r),1,[r]))})).catch((function(e){t.error(e)}))},t.onCancel=function(e){r.cancel(e)}}),this),e.on("GetDoc",(function(e){var t=e.pdfInfo;this.numPages=e.pdfInfo.numPages;var r=this.loadingTask,n=new x(t,this,r);this.pdfDocument=n,r._capability.resolve(n)}),this),e.on("PasswordRequest",(function(e){var r=this;if(this._passwordCapability=(0,a.createPromiseCapability)(),t.onPassword){var n=function(e){r._passwordCapability.resolve({password:e})};t.onPassword(n,e.code)}else this._passwordCapability.reject(new a.PasswordException(e.message,e.code));return this._passwordCapability.promise}),this),e.on("PasswordException",(function(e){t._capability.reject(new a.PasswordException(e.message,e.code))}),this),e.on("InvalidPDF",(function(e){this.loadingTask._capability.reject(new a.InvalidPDFException(e.message))}),this),e.on("MissingPDF",(function(e){this.loadingTask._capability.reject(new a.MissingPDFException(e.message))}),this),e.on("UnexpectedResponse",(function(e){this.loadingTask._capability.reject(new a.UnexpectedResponseException(e.message,e.status))}),this),e.on("UnknownError",(function(e){this.loadingTask._capability.reject(new a.UnknownErrorException(e.message,e.details))}),this),e.on("DataLoaded",(function(e){this.downloadInfoCapability.resolve(e)}),this),e.on("PDFManagerReady",(function(e){}),this),e.on("StartRenderPage",(function(e){if(!this.destroyed){var t=this.pageCache[e.pageIndex];t.stats.timeEnd("Page Request"),t._startRenderPage(e.transparency,e.intent)}}),this),e.on("RenderPageChunk",(function(e){if(!this.destroyed){var t=this.pageCache[e.pageIndex];t._renderPageChunk(e.operatorList,e.intent)}}),this),e.on("commonobj",(function(e){var t=this;if(!this.destroyed){var r=e[0],n=e[1];if(!this.commonObjs.hasData(r))switch(n){case"Font":var i=e[2];if("error"in i){var l=i.error;(0,a.warn)("Error during font loading: "+l),this.commonObjs.resolve(r,l);break}var c=null;(0,s.getDefaultSetting)("pdfBug")&&a.globalScope.FontInspector&&a.globalScope["FontInspector"].enabled&&(c={registerFont:function(e,t){a.globalScope["FontInspector"].fontAdded(e,t)}});var u=new o.FontFaceObject(i,{isEvalSuported:(0,s.getDefaultSetting)("isEvalSupported"),disableFontFace:(0,s.getDefaultSetting)("disableFontFace"),fontRegistry:c}),h=function(e){t.commonObjs.resolve(r,u)};this.fontLoader.bind([u],h);break;case"FontPath":this.commonObjs.resolve(r,e[2]);break;default:throw new Error("Got unknown common object type "+n)}}}),this),e.on("obj",(function(e){if(!this.destroyed){var t,r=e[0],n=e[1],i=e[2],s=this.pageCache[n];if(!s.objs.hasData(r))switch(i){case"JpegStream":t=e[3],(0,a.loadJpegStream)(r,t,s.objs);break;case"Image":t=e[3],s.objs.resolve(r,t);var o=8e6;t&&"data"in t&&t.data.length>o&&(s.cleanupAfterRender=!0);break;default:throw new Error("Got unknown object type "+i)}}}),this),e.on("DocProgress",(function(e){if(!this.destroyed){var t=this.loadingTask;t.onProgress&&t.onProgress({loaded:e.loaded,total:e.total})}}),this),e.on("PageError",(function(e){if(!this.destroyed){var t=this.pageCache[e.pageNum-1],r=t.intentStates[e.intent];if(!r.displayReadyCapability)throw new Error(e.error);if(r.displayReadyCapability.reject(e.error),r.operatorList){r.operatorList.lastChunk=!0;for(var n=0;n<r.renderTasks.length;n++)r.renderTasks[n].operatorListChanged()}}}),this),e.on("UnsupportedFeature",(function(e){if(!this.destroyed){var t=e.featureId,r=this.loadingTask;r.onUnsupportedFeature&&r.onUnsupportedFeature(t),N.notify(t)}}),this),e.on("JpegDecode",(function(e){if(this.destroyed)return Promise.reject(new Error("Worker was destroyed"));if("undefined"===typeof document)return Promise.reject(new Error('"document" is not defined.'));var t=e[0],r=e[1];return 3!==r&&1!==r?Promise.reject(new Error("Only 3 components or 1 component can be returned")):new Promise((function(e,n){var i=new Image;i.onload=function(){var t=i.width,n=i.height,a=t*n,s=4*a,o=new Uint8Array(a*r),l=document.createElement("canvas");l.width=t,l.height=n;var c=l.getContext("2d");c.drawImage(i,0,0);var u,h,d=c.getImageData(0,0,t,n).data;if(3===r)for(u=0,h=0;u<s;u+=4,h+=3)o[h]=d[u],o[h+1]=d[u+1],o[h+2]=d[u+2];else if(1===r)for(u=0,h=0;u<s;u+=4,h++)o[h]=d[u];e({data:o,width:t,height:n})},i.onerror=function(){n(new Error("JpegDecode failed to load image"))},i.src=t}))}),this),e.on("FetchBuiltInCMap",(function(e){return this.destroyed?Promise.reject(new Error("Worker was destroyed")):this.CMapReaderFactory.fetch({name:e.name})}),this)},getData:function(){return this.messageHandler.sendWithPromise("GetData",null)},getPage:function(e,t){var r=this;if(!(0,a.isInt)(e)||e<=0||e>this.numPages)return Promise.reject(new Error("Invalid page request"));var n=e-1;if(n in this.pagePromises)return this.pagePromises[n];var i=this.messageHandler.sendWithPromise("GetPage",{pageIndex:n}).then((function(e){if(r.destroyed)throw new Error("Transport destroyed");var t=new T(n,e,r);return r.pageCache[n]=t,t}));return this.pagePromises[n]=i,i},getPageIndex:function(e){return this.messageHandler.sendWithPromise("GetPageIndex",{ref:e}).catch((function(e){return Promise.reject(new Error(e))}))},getAnnotations:function(e,t){return this.messageHandler.sendWithPromise("GetAnnotations",{pageIndex:e,intent:t})},getDestinations:function(){return this.messageHandler.sendWithPromise("GetDestinations",null)},getDestination:function(e){return this.messageHandler.sendWithPromise("GetDestination",{id:e})},getPageLabels:function(){return this.messageHandler.sendWithPromise("GetPageLabels",null)},getPageMode:function(){return this.messageHandler.sendWithPromise("GetPageMode",null)},getAttachments:function(){return this.messageHandler.sendWithPromise("GetAttachments",null)},getJavaScript:function(){return this.messageHandler.sendWithPromise("GetJavaScript",null)},getOutline:function(){return this.messageHandler.sendWithPromise("GetOutline",null)},getMetadata:function(){return this.messageHandler.sendWithPromise("GetMetadata",null).then((function(e){return{info:e[0],metadata:e[1]?new c.Metadata(e[1]):null}}))},getStats:function(){return this.messageHandler.sendWithPromise("GetStats",null)},startCleanup:function(){var e=this;this.messageHandler.sendWithPromise("Cleanup",null).then((function(){for(var t=0,r=e.pageCache.length;t<r;t++){var n=e.pageCache[t];n&&n.cleanup()}e.commonObjs.clear(),e.fontLoader.clear()}))}},e}(),D=function(){function e(){this.objs=Object.create(null)}return e.prototype={ensureObj:function(e){if(this.objs[e])return this.objs[e];var t={capability:(0,a.createPromiseCapability)(),data:null,resolved:!1};return this.objs[e]=t,t},get:function(e,t){if(t)return this.ensureObj(e).capability.promise.then(t),null;var r=this.objs[e];if(!r||!r.resolved)throw new Error("Requesting object that isn't resolved yet "+e);return r.data},resolve:function(e,t){var r=this.ensureObj(e);r.resolved=!0,r.data=t,r.capability.resolve(t)},isResolved:function(e){var t=this.objs;return!!t[e]&&t[e].resolved},hasData:function(e){return this.isResolved(e)},getData:function(e){var t=this.objs;return t[e]&&t[e].resolved?t[e].data:null},clear:function(){this.objs=Object.create(null)}},e}(),O=function(){function e(e){this._internalRenderTask=e,this.onContinue=null}return e.prototype={get promise(){return this._internalRenderTask.capability.promise},cancel:function(){this._internalRenderTask.cancel()},then:function(e,t){return this.promise.then.apply(this.promise,arguments)}},e}(),F=function(){var e=new WeakMap;function t(e,t,r,n,i,s,o){this.callback=e,this.params=t,this.objs=r,this.commonObjs=n,this.operatorListIdx=null,this.operatorList=i,this.pageNumber=s,this.canvasFactory=o,this.running=!1,this.graphicsReadyCallback=null,this.graphicsReady=!1,this.useRequestAnimationFrame=!1,this.cancelled=!1,this.capability=(0,a.createPromiseCapability)(),this.task=new O(this),this._continueBound=this._continue.bind(this),this._scheduleNextBound=this._scheduleNext.bind(this),this._nextBound=this._next.bind(this),this._canvas=t.canvasContext.canvas}return t.prototype={initializeGraphics:function(t){if(this._canvas){if(e.has(this._canvas))throw new Error("Cannot use the same canvas during multiple render() operations. Use different canvas or ensure previous operations were cancelled or completed.");e.set(this._canvas,this)}if(!this.cancelled){(0,s.getDefaultSetting)("pdfBug")&&a.globalScope.StepperManager&&a.globalScope.StepperManager.enabled&&(this.stepper=a.globalScope.StepperManager.create(this.pageNumber-1),this.stepper.init(this.operatorList),this.stepper.nextBreakPoint=this.stepper.getNextBreakPoint());var r=this.params;this.gfx=new l.CanvasGraphics(r.canvasContext,this.commonObjs,this.objs,this.canvasFactory,r.imageLayer),this.gfx.beginDrawing({transform:r.transform,viewport:r.viewport,transparency:t,background:r.background}),this.operatorListIdx=0,this.graphicsReady=!0,this.graphicsReadyCallback&&this.graphicsReadyCallback()}},cancel:function(){this.running=!1,this.cancelled=!0,this._canvas&&e.delete(this._canvas),(0,s.getDefaultSetting)("pdfjsNext")?this.callback(new s.RenderingCancelledException("Rendering cancelled, page "+this.pageNumber,"canvas")):this.callback("cancelled")},operatorListChanged:function(){this.graphicsReady?(this.stepper&&this.stepper.updateOperatorList(this.operatorList),this.running||this._continue()):this.graphicsReadyCallback||(this.graphicsReadyCallback=this._continueBound)},_continue:function(){this.running=!0,this.cancelled||(this.task.onContinue?this.task.onContinue(this._scheduleNextBound):this._scheduleNext())},_scheduleNext:function(){this.useRequestAnimationFrame&&"undefined"!==typeof window?window.requestAnimationFrame(this._nextBound):Promise.resolve(void 0).then(this._nextBound)},_next:function(){this.cancelled||(this.operatorListIdx=this.gfx.executeOperatorList(this.operatorList,this.operatorListIdx,this._continueBound,this.stepper),this.operatorListIdx===this.operatorList.argsArray.length&&(this.running=!1,this.operatorList.lastChunk&&(this.gfx.endDrawing(),this._canvas&&e.delete(this._canvas),this.callback())))}},t}(),N=function(){var e=[];return{listen:function(t){(0,a.deprecated)("Global UnsupportedManager.listen is used:  use PDFDocumentLoadingTask.onUnsupportedFeature instead"),e.push(t)},notify:function(t){for(var r=0,n=e.length;r<n;r++)e[r](t)}}}();t.version=P="1.9.426",t.build=C="2558a58d",t.getDocument=S,t.LoopbackPort=E,t.PDFDataRangeTransport=k,t.PDFWorker=I,t.PDFDocumentProxy=x,t.PDFPageProxy=T,t.setPDFNetworkStreamClass=A,t._UnsupportedManager=N,t.version=P,t.build=C},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.AnnotationLayer=void 0;var n=r(1),i=r(0);function a(){}a.prototype={create:function(e){var t=e.data.annotationType;switch(t){case i.AnnotationType.LINK:return new o(e);case i.AnnotationType.TEXT:return new l(e);case i.AnnotationType.WIDGET:var r=e.data.fieldType;switch(r){case"Tx":return new u(e);case"Btn":if(e.data.radioButton)return new d(e);if(e.data.checkBox)return new h(e);(0,i.warn)("Unimplemented button widget annotation: pushbutton");break;case"Ch":return new f(e)}return new c(e);case i.AnnotationType.POPUP:return new p(e);case i.AnnotationType.LINE:return new g(e);case i.AnnotationType.HIGHLIGHT:return new v(e);case i.AnnotationType.UNDERLINE:return new b(e);case i.AnnotationType.SQUIGGLY:return new _(e);case i.AnnotationType.STRIKEOUT:return new y(e);case i.AnnotationType.FILEATTACHMENT:return new A(e);default:return new s(e)}}};var s=function(){function e(e,t,r){this.isRenderable=t||!1,this.data=e.data,this.layer=e.layer,this.page=e.page,this.viewport=e.viewport,this.linkService=e.linkService,this.downloadManager=e.downloadManager,this.imageResourcesPath=e.imageResourcesPath,this.renderInteractiveForms=e.renderInteractiveForms,t&&(this.container=this._createContainer(r))}return e.prototype={_createContainer:function(e){var t=this.data,r=this.page,a=this.viewport,s=document.createElement("section"),o=t.rect[2]-t.rect[0],l=t.rect[3]-t.rect[1];s.setAttribute("data-annotation-id",t.id);var c=i.Util.normalizeRect([t.rect[0],r.view[3]-t.rect[1]+r.view[1],t.rect[2],r.view[3]-t.rect[3]+r.view[1]]);if(n.CustomStyle.setProp("transform",s,"matrix("+a.transform.join(",")+")"),n.CustomStyle.setProp("transformOrigin",s,-c[0]+"px "+-c[1]+"px"),!e&&t.borderStyle.width>0){s.style.borderWidth=t.borderStyle.width+"px",t.borderStyle.style!==i.AnnotationBorderStyleType.UNDERLINE&&(o-=2*t.borderStyle.width,l-=2*t.borderStyle.width);var u=t.borderStyle.horizontalCornerRadius,h=t.borderStyle.verticalCornerRadius;if(u>0||h>0){var d=u+"px / "+h+"px";n.CustomStyle.setProp("borderRadius",s,d)}switch(t.borderStyle.style){case i.AnnotationBorderStyleType.SOLID:s.style.borderStyle="solid";break;case i.AnnotationBorderStyleType.DASHED:s.style.borderStyle="dashed";break;case i.AnnotationBorderStyleType.BEVELED:(0,i.warn)("Unimplemented border style: beveled");break;case i.AnnotationBorderStyleType.INSET:(0,i.warn)("Unimplemented border style: inset");break;case i.AnnotationBorderStyleType.UNDERLINE:s.style.borderBottomStyle="solid";break;default:break}t.color?s.style.borderColor=i.Util.makeCssRgb(0|t.color[0],0|t.color[1],0|t.color[2]):s.style.borderWidth=0}return s.style.left=c[0]+"px",s.style.top=c[1]+"px",s.style.width=o+"px",s.style.height=l+"px",s},_createPopup:function(e,t,r){t||(t=document.createElement("div"),t.style.height=e.style.height,t.style.width=e.style.width,e.appendChild(t));var n=new m({container:e,trigger:t,color:r.color,title:r.title,contents:r.contents,hideWrapper:!0}),i=n.render();i.style.left=e.style.width,e.appendChild(i)},render:function(){throw new Error("Abstract method AnnotationElement.render called")}},e}(),o=function(){function e(e){s.call(this,e,!0)}return i.Util.inherit(e,s,{render:function(){this.container.className="linkAnnotation";var e=document.createElement("a");return(0,n.addLinkAttributes)(e,{url:this.data.url,target:this.data.newWindow?n.LinkTarget.BLANK:void 0}),this.data.url||(this.data.action?this._bindNamedAction(e,this.data.action):this._bindLink(e,this.data.dest)),this.container.appendChild(e),this.container},_bindLink:function(e,t){var r=this;e.href=this.linkService.getDestinationHash(t),e.onclick=function(){return t&&r.linkService.navigateTo(t),!1},t&&(e.className="internalLink")},_bindNamedAction:function(e,t){var r=this;e.href=this.linkService.getAnchorUrl(""),e.onclick=function(){return r.linkService.executeNamedAction(t),!1},e.className="internalLink"}}),e}(),l=function(){function e(e){var t=!!(e.data.hasPopup||e.data.title||e.data.contents);s.call(this,e,t)}return i.Util.inherit(e,s,{render:function(){this.container.className="textAnnotation";var e=document.createElement("img");return e.style.height=this.container.style.height,e.style.width=this.container.style.width,e.src=this.imageResourcesPath+"annotation-"+this.data.name.toLowerCase()+".svg",e.alt="[{{type}} Annotation]",e.dataset.l10nId="text_annotation_type",e.dataset.l10nArgs=JSON.stringify({type:this.data.name}),this.data.hasPopup||this._createPopup(this.container,e,this.data),this.container.appendChild(e),this.container}}),e}(),c=function(){function e(e,t){s.call(this,e,t)}return i.Util.inherit(e,s,{render:function(){return this.container}}),e}(),u=function(){var e=["left","center","right"];function t(e){var t=e.renderInteractiveForms||!e.data.hasAppearance&&!!e.data.fieldValue;c.call(this,e,t)}return i.Util.inherit(t,c,{render:function(){this.container.className="textWidgetAnnotation";var t=null;if(this.renderInteractiveForms){if(this.data.multiLine?(t=document.createElement("textarea"),t.textContent=this.data.fieldValue):(t=document.createElement("input"),t.type="text",t.setAttribute("value",this.data.fieldValue)),t.disabled=this.data.readOnly,null!==this.data.maxLen&&(t.maxLength=this.data.maxLen),this.data.comb){var r=this.data.rect[2]-this.data.rect[0],n=r/this.data.maxLen;t.classList.add("comb"),t.style.letterSpacing="calc("+n+"px - 1ch)"}}else{t=document.createElement("div"),t.textContent=this.data.fieldValue,t.style.verticalAlign="middle",t.style.display="table-cell";var i=null;this.data.fontRefName&&(i=this.page.commonObjs.getData(this.data.fontRefName)),this._setTextStyle(t,i)}return null!==this.data.textAlignment&&(t.style.textAlign=e[this.data.textAlignment]),this.container.appendChild(t),this.container},_setTextStyle:function(e,t){var r=e.style;if(r.fontSize=this.data.fontSize+"px",r.direction=this.data.fontDirection<0?"rtl":"ltr",t){r.fontWeight=t.black?t.bold?"900":"bold":t.bold?"bold":"normal",r.fontStyle=t.italic?"italic":"normal";var n=t.loadedName?'"'+t.loadedName+'", ':"",i=t.fallbackName||"Helvetica, sans-serif";r.fontFamily=n+i}}}),t}(),h=function(){function e(e){c.call(this,e,e.renderInteractiveForms)}return i.Util.inherit(e,c,{render:function(){this.container.className="buttonWidgetAnnotation checkBox";var e=document.createElement("input");return e.disabled=this.data.readOnly,e.type="checkbox",this.data.fieldValue&&"Off"!==this.data.fieldValue&&e.setAttribute("checked",!0),this.container.appendChild(e),this.container}}),e}(),d=function(){function e(e){c.call(this,e,e.renderInteractiveForms)}return i.Util.inherit(e,c,{render:function(){this.container.className="buttonWidgetAnnotation radioButton";var e=document.createElement("input");return e.disabled=this.data.readOnly,e.type="radio",e.name=this.data.fieldName,this.data.fieldValue===this.data.buttonValue&&e.setAttribute("checked",!0),this.container.appendChild(e),this.container}}),e}(),f=function(){function e(e){c.call(this,e,e.renderInteractiveForms)}return i.Util.inherit(e,c,{render:function(){this.container.className="choiceWidgetAnnotation";var e=document.createElement("select");e.disabled=this.data.readOnly,this.data.combo||(e.size=this.data.options.length,this.data.multiSelect&&(e.multiple=!0));for(var t=0,r=this.data.options.length;t<r;t++){var n=this.data.options[t],i=document.createElement("option");i.textContent=n.displayValue,i.value=n.exportValue,this.data.fieldValue.indexOf(n.displayValue)>=0&&i.setAttribute("selected",!0),e.appendChild(i)}return this.container.appendChild(e),this.container}}),e}(),p=function(){var e=["Line"];function t(e){var t=!(!e.data.title&&!e.data.contents);s.call(this,e,t)}return i.Util.inherit(t,s,{render:function(){if(this.container.className="popupAnnotation",e.indexOf(this.data.parentType)>=0)return this.container;var t='[data-annotation-id="'+this.data.parentId+'"]',r=this.layer.querySelector(t);if(!r)return this.container;var i=new m({container:this.container,trigger:r,color:this.data.color,title:this.data.title,contents:this.data.contents}),a=parseFloat(r.style.left),s=parseFloat(r.style.width);return n.CustomStyle.setProp("transformOrigin",this.container,-(a+s)+"px -"+r.style.top),this.container.style.left=a+s+"px",this.container.appendChild(i.render()),this.container}}),t}(),m=function(){var e=.7;function t(e){this.container=e.container,this.trigger=e.trigger,this.color=e.color,this.title=e.title,this.contents=e.contents,this.hideWrapper=e.hideWrapper||!1,this.pinned=!1}return t.prototype={render:function(){var t=document.createElement("div");t.className="popupWrapper",this.hideElement=this.hideWrapper?t:this.container,this.hideElement.setAttribute("hidden",!0);var r=document.createElement("div");r.className="popup";var n=this.color;if(n){var a=e*(255-n[0])+n[0],s=e*(255-n[1])+n[1],o=e*(255-n[2])+n[2];r.style.backgroundColor=i.Util.makeCssRgb(0|a,0|s,0|o)}var l=this._formatContents(this.contents),c=document.createElement("h1");return c.textContent=this.title,this.trigger.addEventListener("click",this._toggle.bind(this)),this.trigger.addEventListener("mouseover",this._show.bind(this,!1)),this.trigger.addEventListener("mouseout",this._hide.bind(this,!1)),r.addEventListener("click",this._hide.bind(this,!0)),r.appendChild(c),r.appendChild(l),t.appendChild(r),t},_formatContents:function(e){for(var t=document.createElement("p"),r=e.split(/(?:\r\n?|\n)/),n=0,i=r.length;n<i;++n){var a=r[n];t.appendChild(document.createTextNode(a)),n<i-1&&t.appendChild(document.createElement("br"))}return t},_toggle:function(){this.pinned?this._hide(!0):this._show(!0)},_show:function(e){e&&(this.pinned=!0),this.hideElement.hasAttribute("hidden")&&(this.hideElement.removeAttribute("hidden"),this.container.style.zIndex+=1)},_hide:function(e){e&&(this.pinned=!1),this.hideElement.hasAttribute("hidden")||this.pinned||(this.hideElement.setAttribute("hidden",!0),this.container.style.zIndex-=1)}},t}(),g=function(){var e="http://www.w3.org/2000/svg";function t(e){var t=!!(e.data.hasPopup||e.data.title||e.data.contents);s.call(this,e,t,!0)}return i.Util.inherit(t,s,{render:function(){this.container.className="lineAnnotation";var t=this.data,r=t.rect[2]-t.rect[0],n=t.rect[3]-t.rect[1],i=document.createElementNS(e,"svg:svg");i.setAttributeNS(null,"version","1.1"),i.setAttributeNS(null,"width",r+"px"),i.setAttributeNS(null,"height",n+"px"),i.setAttributeNS(null,"preserveAspectRatio","none"),i.setAttributeNS(null,"viewBox","0 0 "+r+" "+n);var a=document.createElementNS(e,"svg:line");return a.setAttributeNS(null,"x1",t.rect[2]-t.lineCoordinates[0]),a.setAttributeNS(null,"y1",t.rect[3]-t.lineCoordinates[1]),a.setAttributeNS(null,"x2",t.rect[2]-t.lineCoordinates[2]),a.setAttributeNS(null,"y2",t.rect[3]-t.lineCoordinates[3]),a.setAttributeNS(null,"stroke-width",t.borderStyle.width),a.setAttributeNS(null,"stroke","transparent"),i.appendChild(a),this.container.append(i),this._createPopup(this.container,a,this.data),this.container}}),t}(),v=function(){function e(e){var t=!!(e.data.hasPopup||e.data.title||e.data.contents);s.call(this,e,t,!0)}return i.Util.inherit(e,s,{render:function(){return this.container.className="highlightAnnotation",this.data.hasPopup||this._createPopup(this.container,null,this.data),this.container}}),e}(),b=function(){function e(e){var t=!!(e.data.hasPopup||e.data.title||e.data.contents);s.call(this,e,t,!0)}return i.Util.inherit(e,s,{render:function(){return this.container.className="underlineAnnotation",this.data.hasPopup||this._createPopup(this.container,null,this.data),this.container}}),e}(),_=function(){function e(e){var t=!!(e.data.hasPopup||e.data.title||e.data.contents);s.call(this,e,t,!0)}return i.Util.inherit(e,s,{render:function(){return this.container.className="squigglyAnnotation",this.data.hasPopup||this._createPopup(this.container,null,this.data),this.container}}),e}(),y=function(){function e(e){var t=!!(e.data.hasPopup||e.data.title||e.data.contents);s.call(this,e,t,!0)}return i.Util.inherit(e,s,{render:function(){return this.container.className="strikeoutAnnotation",this.data.hasPopup||this._createPopup(this.container,null,this.data),this.container}}),e}(),A=function(){function e(e){s.call(this,e,!0);var t=this.data.file;this.filename=(0,n.getFilenameFromUrl)(t.filename),this.content=t.content,this.linkService.onFileAttachmentAnnotation({id:(0,i.stringToPDFString)(t.filename),filename:t.filename,content:t.content})}return i.Util.inherit(e,s,{render:function(){this.container.className="fileAttachmentAnnotation";var e=document.createElement("div");return e.style.height=this.container.style.height,e.style.width=this.container.style.width,e.addEventListener("dblclick",this._download.bind(this)),this.data.hasPopup||!this.data.title&&!this.data.contents||this._createPopup(this.container,e,this.data),this.container.appendChild(e),this.container},_download:function(){this.downloadManager?this.downloadManager.downloadData(this.content,this.filename,""):(0,i.warn)("Download cannot be started due to unavailable download manager")}}),e}(),S=function(){return{render:function(e){for(var t=new a,r=0,i=e.annotations.length;r<i;r++){var s=e.annotations[r];if(s){var o=t.create({data:s,layer:e.div,page:e.page,viewport:e.viewport,linkService:e.linkService,downloadManager:e.downloadManager,imageResourcesPath:e.imageResourcesPath||(0,n.getDefaultSetting)("imageResourcesPath"),renderInteractiveForms:e.renderInteractiveForms||!1});o.isRenderable&&e.div.appendChild(o.render())}}},update:function(e){for(var t=0,r=e.annotations.length;t<r;t++){var i=e.annotations[t],a=e.div.querySelector('[data-annotation-id="'+i.id+'"]');a&&n.CustomStyle.setProp("transform",a,"matrix("+e.viewport.transform.join(",")+")")}e.div.removeAttribute("hidden")}}}();t.AnnotationLayer=S},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SVGGraphics=void 0;var n=r(0),i=function(){throw new Error("Not implemented: SVGGraphics")},a={fontStyle:"normal",fontWeight:"normal",fillColor:"#000000"},s=function(){for(var e=new Uint8Array([137,80,78,71,13,10,26,10]),t=12,r=new Int32Array(256),i=0;i<256;i++){for(var a=i,s=0;s<8;s++)a=1&a?3988292384^a>>1&2147483647:a>>1&2147483647;r[i]=a}function o(e,t,n){for(var i=-1,a=t;a<n;a++){var s=255&(i^e[a]),o=r[s];i=i>>>8^o}return-1^i}function l(e,t,r,n){var i=n,a=t.length;r[i]=a>>24&255,r[i+1]=a>>16&255,r[i+2]=a>>8&255,r[i+3]=255&a,i+=4,r[i]=255&e.charCodeAt(0),r[i+1]=255&e.charCodeAt(1),r[i+2]=255&e.charCodeAt(2),r[i+3]=255&e.charCodeAt(3),i+=4,r.set(t,i),i+=t.length;var s=o(r,n+4,i);r[i]=s>>24&255,r[i+1]=s>>16&255,r[i+2]=s>>8&255,r[i+3]=255&s}function c(e,t,r){for(var n=1,i=0,a=t;a<r;++a)n=(n+(255&e[a]))%65521,i=(i+n)%65521;return i<<16|n}function u(e){if(!(0,n.isNodeJS)())return h(e);try{var t;t=parseInt(process.versions.node)>=8?e:new Buffer(e);var r=require("zlib").deflateSync(t,{level:9});return r instanceof Uint8Array?r:new Uint8Array(r)}catch(i){(0,n.warn)("Not compressing PNG because zlib.deflateSync is unavailable: "+i)}return h(e)}function h(e){var t=e.length,r=65535,n=Math.ceil(t/r),i=new Uint8Array(2+t+5*n+4),a=0;i[a++]=120,i[a++]=156;var s=0;while(t>r)i[a++]=0,i[a++]=255,i[a++]=255,i[a++]=0,i[a++]=0,i.set(e.subarray(s,s+r),a),a+=r,s+=r,t-=r;i[a++]=1,i[a++]=255&t,i[a++]=t>>8&255,i[a++]=255&~t,i[a++]=(65535&~t)>>8&255,i.set(e.subarray(s),a),a+=e.length-s;var o=c(e,0,e.length);return i[a++]=o>>24&255,i[a++]=o>>16&255,i[a++]=o>>8&255,i[a++]=255&o,i}function d(r,i,a){var s,o,c,h=r.width,d=r.height,f=r.data;switch(i){case n.ImageKind.GRAYSCALE_1BPP:o=0,s=1,c=h+7>>3;break;case n.ImageKind.RGB_24BPP:o=2,s=8,c=3*h;break;case n.ImageKind.RGBA_32BPP:o=6,s=8,c=4*h;break;default:throw new Error("invalid format")}var p,m,g=new Uint8Array((1+c)*d),v=0,b=0;for(p=0;p<d;++p)g[v++]=0,g.set(f.subarray(b,b+c),v),b+=c,v+=c;if(i===n.ImageKind.GRAYSCALE_1BPP)for(v=0,p=0;p<d;p++)for(v++,m=0;m<c;m++)g[v++]^=255;var _=new Uint8Array([h>>24&255,h>>16&255,h>>8&255,255&h,d>>24&255,d>>16&255,d>>8&255,255&d,s,o,0,0,0]),y=u(g),A=e.length+3*t+_.length+y.length,S=new Uint8Array(A),w=0;return S.set(e,w),w+=e.length,l("IHDR",_,S,w),w+=t+_.length,l("IDATA",y,S,w),w+=t+y.length,l("IEND",new Uint8Array(0),S,w),(0,n.createObjectURL)(S,"image/png",a)}return function(e,t){var r=void 0===e.kind?n.ImageKind.GRAYSCALE_1BPP:e.kind;return d(e,r,t)}}(),o=function(){function e(){this.fontSizeScale=1,this.fontWeight=a.fontWeight,this.fontSize=0,this.textMatrix=n.IDENTITY_MATRIX,this.fontMatrix=n.FONT_IDENTITY_MATRIX,this.leading=0,this.x=0,this.y=0,this.lineX=0,this.lineY=0,this.charSpacing=0,this.wordSpacing=0,this.textHScale=1,this.textRise=0,this.fillColor=a.fillColor,this.strokeColor="#000000",this.fillAlpha=1,this.strokeAlpha=1,this.lineWidth=1,this.lineJoin="",this.lineCap="",this.miterLimit=0,this.dashArray=[],this.dashPhase=0,this.dependencies=[],this.activeClipUrl=null,this.clipGroup=null,this.maskId=""}return e.prototype={clone:function(){return Object.create(this)},setCurrentPoint:function(e,t){this.x=e,this.y=t}},e}();t.SVGGraphics=i=function(){function e(e){for(var t=[],r=[],n=e.length,i=0;i<n;i++)"save"!==e[i].fn?"restore"===e[i].fn?t=r.pop():t.push(e[i]):(t.push({fnId:92,fn:"group",items:[]}),r.push(t),t=t[t.length-1].items);return t}function t(e){if(e===(0|e))return e.toString();var t=e.toFixed(10),r=t.length-1;if("0"!==t[r])return t;do{r--}while("0"===t[r]);return t.substr(0,"."===t[r]?r:r+1)}function r(e){if(0===e[4]&&0===e[5]){if(0===e[1]&&0===e[2])return 1===e[0]&&1===e[3]?"":"scale("+t(e[0])+" "+t(e[3])+")";if(e[0]===e[3]&&e[1]===-e[2]){var r=180*Math.acos(e[0])/Math.PI;return"rotate("+t(r)+")"}}else if(1===e[0]&&0===e[1]&&0===e[2]&&1===e[3])return"translate("+t(e[4])+" "+t(e[5])+")";return"matrix("+t(e[0])+" "+t(e[1])+" "+t(e[2])+" "+t(e[3])+" "+t(e[4])+" "+t(e[5])+")"}function i(e,t,r){this.current=new o,this.transformMatrix=n.IDENTITY_MATRIX,this.transformStack=[],this.extraStack=[],this.commonObjs=e,this.objs=t,this.pendingClip=null,this.pendingEOFill=!1,this.embedFonts=!1,this.embeddedFonts=Object.create(null),this.cssStyle=null,this.forceDataSchema=!!r}var l="http://www.w3.org/2000/svg",c="http://www.w3.org/XML/1998/namespace",u="http://www.w3.org/1999/xlink",h=["butt","round","square"],d=["miter","round","bevel"],f=0,p=0;return i.prototype={save:function(){this.transformStack.push(this.transformMatrix);var e=this.current;this.extraStack.push(e),this.current=e.clone()},restore:function(){this.transformMatrix=this.transformStack.pop(),this.current=this.extraStack.pop(),this.pendingClip=null,this.tgrp=null},group:function(e){this.save(),this.executeOpTree(e),this.restore()},loadDependencies:function(e){for(var t=this,r=e.fnArray,i=r.length,a=e.argsArray,s=0;s<i;s++)if(n.OPS.dependency===r[s])for(var o=a[s],l=0,c=o.length;l<c;l++){var u,h=o[l],d="g_"===h.substring(0,2);u=new Promise(d?function(e){t.commonObjs.get(h,e)}:function(e){t.objs.get(h,e)}),this.current.dependencies.push(u)}return Promise.all(this.current.dependencies)},transform:function(e,t,r,i,a,s){var o=[e,t,r,i,a,s];this.transformMatrix=n.Util.transform(this.transformMatrix,o),this.tgrp=null},getSVG:function(e,t){var r=this;this.viewport=t;var i=this._initialize(t);return this.loadDependencies(e).then((function(){r.transformMatrix=n.IDENTITY_MATRIX;var t=r.convertOpList(e);return r.executeOpTree(t),i}))},convertOpList:function(t){var r=t.argsArray,i=t.fnArray,a=i.length,s=[],o=[];for(var l in n.OPS)s[n.OPS[l]]=l;for(var c=0;c<a;c++){var u=i[c];o.push({fnId:u,fn:s[u],args:r[c]})}return e(o)},executeOpTree:function(e){for(var t=e.length,r=0;r<t;r++){var i=e[r].fn,a=e[r].fnId,s=e[r].args;switch(0|a){case n.OPS.beginText:this.beginText();break;case n.OPS.setLeading:this.setLeading(s);break;case n.OPS.setLeadingMoveText:this.setLeadingMoveText(s[0],s[1]);break;case n.OPS.setFont:this.setFont(s);break;case n.OPS.showText:this.showText(s[0]);break;case n.OPS.showSpacedText:this.showText(s[0]);break;case n.OPS.endText:this.endText();break;case n.OPS.moveText:this.moveText(s[0],s[1]);break;case n.OPS.setCharSpacing:this.setCharSpacing(s[0]);break;case n.OPS.setWordSpacing:this.setWordSpacing(s[0]);break;case n.OPS.setHScale:this.setHScale(s[0]);break;case n.OPS.setTextMatrix:this.setTextMatrix(s[0],s[1],s[2],s[3],s[4],s[5]);break;case n.OPS.setLineWidth:this.setLineWidth(s[0]);break;case n.OPS.setLineJoin:this.setLineJoin(s[0]);break;case n.OPS.setLineCap:this.setLineCap(s[0]);break;case n.OPS.setMiterLimit:this.setMiterLimit(s[0]);break;case n.OPS.setFillRGBColor:this.setFillRGBColor(s[0],s[1],s[2]);break;case n.OPS.setStrokeRGBColor:this.setStrokeRGBColor(s[0],s[1],s[2]);break;case n.OPS.setDash:this.setDash(s[0],s[1]);break;case n.OPS.setGState:this.setGState(s[0]);break;case n.OPS.fill:this.fill();break;case n.OPS.eoFill:this.eoFill();break;case n.OPS.stroke:this.stroke();break;case n.OPS.fillStroke:this.fillStroke();break;case n.OPS.eoFillStroke:this.eoFillStroke();break;case n.OPS.clip:this.clip("nonzero");break;case n.OPS.eoClip:this.clip("evenodd");break;case n.OPS.paintSolidColorImageMask:this.paintSolidColorImageMask();break;case n.OPS.paintJpegXObject:this.paintJpegXObject(s[0],s[1],s[2]);break;case n.OPS.paintImageXObject:this.paintImageXObject(s[0]);break;case n.OPS.paintInlineImageXObject:this.paintInlineImageXObject(s[0]);break;case n.OPS.paintImageMaskXObject:this.paintImageMaskXObject(s[0]);break;case n.OPS.paintFormXObjectBegin:this.paintFormXObjectBegin(s[0],s[1]);break;case n.OPS.paintFormXObjectEnd:this.paintFormXObjectEnd();break;case n.OPS.closePath:this.closePath();break;case n.OPS.closeStroke:this.closeStroke();break;case n.OPS.closeFillStroke:this.closeFillStroke();break;case n.OPS.nextLine:this.nextLine();break;case n.OPS.transform:this.transform(s[0],s[1],s[2],s[3],s[4],s[5]);break;case n.OPS.constructPath:this.constructPath(s[0],s[1]);break;case n.OPS.endPath:this.endPath();break;case 92:this.group(e[r].items);break;default:(0,n.warn)("Unimplemented operator "+i);break}}},setWordSpacing:function(e){this.current.wordSpacing=e},setCharSpacing:function(e){this.current.charSpacing=e},nextLine:function(){this.moveText(0,this.current.leading)},setTextMatrix:function(e,r,n,i,a,s){var o=this.current;this.current.textMatrix=this.current.lineMatrix=[e,r,n,i,a,s],this.current.x=this.current.lineX=0,this.current.y=this.current.lineY=0,o.xcoords=[],o.tspan=document.createElementNS(l,"svg:tspan"),o.tspan.setAttributeNS(null,"font-family",o.fontFamily),o.tspan.setAttributeNS(null,"font-size",t(o.fontSize)+"px"),o.tspan.setAttributeNS(null,"y",t(-o.y)),o.txtElement=document.createElementNS(l,"svg:text"),o.txtElement.appendChild(o.tspan)},beginText:function(){this.current.x=this.current.lineX=0,this.current.y=this.current.lineY=0,this.current.textMatrix=n.IDENTITY_MATRIX,this.current.lineMatrix=n.IDENTITY_MATRIX,this.current.tspan=document.createElementNS(l,"svg:tspan"),this.current.txtElement=document.createElementNS(l,"svg:text"),this.current.txtgrp=document.createElementNS(l,"svg:g"),this.current.xcoords=[]},moveText:function(e,r){var n=this.current;this.current.x=this.current.lineX+=e,this.current.y=this.current.lineY+=r,n.xcoords=[],n.tspan=document.createElementNS(l,"svg:tspan"),n.tspan.setAttributeNS(null,"font-family",n.fontFamily),n.tspan.setAttributeNS(null,"font-size",t(n.fontSize)+"px"),n.tspan.setAttributeNS(null,"y",t(-n.y))},showText:function(e){var i=this.current,s=i.font,o=i.fontSize;if(0!==o){var l,u=i.charSpacing,h=i.wordSpacing,d=i.fontDirection,f=i.textHScale*d,p=e.length,m=s.vertical,g=o*i.fontMatrix[0],v=0;for(l=0;l<p;++l){var b=e[l];if(null!==b)if((0,n.isNum)(b))v+=-b*o*.001;else{i.xcoords.push(i.x+v*f);var _=b.width,y=b.fontChar,A=(b.isSpace?h:0)+u,S=_*g+A*d;v+=S,i.tspan.textContent+=y}else v+=d*h}m?i.y-=v*f:i.x+=v*f,i.tspan.setAttributeNS(null,"x",i.xcoords.map(t).join(" ")),i.tspan.setAttributeNS(null,"y",t(-i.y)),i.tspan.setAttributeNS(null,"font-family",i.fontFamily),i.tspan.setAttributeNS(null,"font-size",t(i.fontSize)+"px"),i.fontStyle!==a.fontStyle&&i.tspan.setAttributeNS(null,"font-style",i.fontStyle),i.fontWeight!==a.fontWeight&&i.tspan.setAttributeNS(null,"font-weight",i.fontWeight),i.fillColor!==a.fillColor&&i.tspan.setAttributeNS(null,"fill",i.fillColor),i.txtElement.setAttributeNS(null,"transform",r(i.textMatrix)+" scale(1, -1)"),i.txtElement.setAttributeNS(c,"xml:space","preserve"),i.txtElement.appendChild(i.tspan),i.txtgrp.appendChild(i.txtElement),this._ensureTransformGroup().appendChild(i.txtElement)}},setLeadingMoveText:function(e,t){this.setLeading(-t),this.moveText(e,t)},addFontStyle:function(e){this.cssStyle||(this.cssStyle=document.createElementNS(l,"svg:style"),this.cssStyle.setAttributeNS(null,"type","text/css"),this.defs.appendChild(this.cssStyle));var t=(0,n.createObjectURL)(e.data,e.mimetype,this.forceDataSchema);this.cssStyle.textContent+='@font-face { font-family: "'+e.loadedName+'"; src: url('+t+"); }\n"},setFont:function(e){var r=this.current,i=this.commonObjs.get(e[0]),a=e[1];this.current.font=i,this.embedFonts&&i.data&&!this.embeddedFonts[i.loadedName]&&(this.addFontStyle(i),this.embeddedFonts[i.loadedName]=i),r.fontMatrix=i.fontMatrix?i.fontMatrix:n.FONT_IDENTITY_MATRIX;var s=i.black?i.bold?"bolder":"bold":i.bold?"bold":"normal",o=i.italic?"italic":"normal";a<0?(a=-a,r.fontDirection=-1):r.fontDirection=1,r.fontSize=a,r.fontFamily=i.loadedName,r.fontWeight=s,r.fontStyle=o,r.tspan=document.createElementNS(l,"svg:tspan"),r.tspan.setAttributeNS(null,"y",t(-r.y)),r.xcoords=[]},endText:function(){},setLineWidth:function(e){this.current.lineWidth=e},setLineCap:function(e){this.current.lineCap=h[e]},setLineJoin:function(e){this.current.lineJoin=d[e]},setMiterLimit:function(e){this.current.miterLimit=e},setStrokeAlpha:function(e){this.current.strokeAlpha=e},setStrokeRGBColor:function(e,t,r){var i=n.Util.makeCssRgb(e,t,r);this.current.strokeColor=i},setFillAlpha:function(e){this.current.fillAlpha=e},setFillRGBColor:function(e,t,r){var i=n.Util.makeCssRgb(e,t,r);this.current.fillColor=i,this.current.tspan=document.createElementNS(l,"svg:tspan"),this.current.xcoords=[]},setDash:function(e,t){this.current.dashArray=e,this.current.dashPhase=t},constructPath:function(e,r){var i=this.current,a=i.x,s=i.y;i.path=document.createElementNS(l,"svg:path");for(var o=[],c=e.length,u=0,h=0;u<c;u++)switch(0|e[u]){case n.OPS.rectangle:a=r[h++],s=r[h++];var d=r[h++],f=r[h++],p=a+d,m=s+f;o.push("M",t(a),t(s),"L",t(p),t(s),"L",t(p),t(m),"L",t(a),t(m),"Z");break;case n.OPS.moveTo:a=r[h++],s=r[h++],o.push("M",t(a),t(s));break;case n.OPS.lineTo:a=r[h++],s=r[h++],o.push("L",t(a),t(s));break;case n.OPS.curveTo:a=r[h+4],s=r[h+5],o.push("C",t(r[h]),t(r[h+1]),t(r[h+2]),t(r[h+3]),t(a),t(s)),h+=6;break;case n.OPS.curveTo2:a=r[h+2],s=r[h+3],o.push("C",t(a),t(s),t(r[h]),t(r[h+1]),t(r[h+2]),t(r[h+3])),h+=4;break;case n.OPS.curveTo3:a=r[h+2],s=r[h+3],o.push("C",t(r[h]),t(r[h+1]),t(a),t(s),t(a),t(s)),h+=4;break;case n.OPS.closePath:o.push("Z");break}i.path.setAttributeNS(null,"d",o.join(" ")),i.path.setAttributeNS(null,"fill","none"),this._ensureTransformGroup().appendChild(i.path),i.element=i.path,i.setCurrentPoint(a,s)},endPath:function(){if(this.pendingClip){var e=this.current,t="clippath"+f;f++;var n=document.createElementNS(l,"svg:clipPath");n.setAttributeNS(null,"id",t),n.setAttributeNS(null,"transform",r(this.transformMatrix));var i=e.element.cloneNode();"evenodd"===this.pendingClip?i.setAttributeNS(null,"clip-rule","evenodd"):i.setAttributeNS(null,"clip-rule","nonzero"),this.pendingClip=null,n.appendChild(i),this.defs.appendChild(n),e.activeClipUrl&&(e.clipGroup=null,this.extraStack.forEach((function(e){e.clipGroup=null}))),e.activeClipUrl="url(#"+t+")",this.tgrp=null}},clip:function(e){this.pendingClip=e},closePath:function(){var e=this.current,t=e.path.getAttributeNS(null,"d");t+="Z",e.path.setAttributeNS(null,"d",t)},setLeading:function(e){this.current.leading=-e},setTextRise:function(e){this.current.textRise=e},setHScale:function(e){this.current.textHScale=e/100},setGState:function(e){for(var t=0,r=e.length;t<r;t++){var i=e[t],a=i[0],s=i[1];switch(a){case"LW":this.setLineWidth(s);break;case"LC":this.setLineCap(s);break;case"LJ":this.setLineJoin(s);break;case"ML":this.setMiterLimit(s);break;case"D":this.setDash(s[0],s[1]);break;case"Font":this.setFont(s);break;case"CA":this.setStrokeAlpha(s);break;case"ca":this.setFillAlpha(s);break;default:(0,n.warn)("Unimplemented graphic state "+a);break}}},fill:function(){var e=this.current;e.element.setAttributeNS(null,"fill",e.fillColor),e.element.setAttributeNS(null,"fill-opacity",e.fillAlpha)},stroke:function(){var e=this.current;e.element.setAttributeNS(null,"stroke",e.strokeColor),e.element.setAttributeNS(null,"stroke-opacity",e.strokeAlpha),e.element.setAttributeNS(null,"stroke-miterlimit",t(e.miterLimit)),e.element.setAttributeNS(null,"stroke-linecap",e.lineCap),e.element.setAttributeNS(null,"stroke-linejoin",e.lineJoin),e.element.setAttributeNS(null,"stroke-width",t(e.lineWidth)+"px"),e.element.setAttributeNS(null,"stroke-dasharray",e.dashArray.map(t).join(" ")),e.element.setAttributeNS(null,"stroke-dashoffset",t(e.dashPhase)+"px"),e.element.setAttributeNS(null,"fill","none")},eoFill:function(){this.current.element.setAttributeNS(null,"fill-rule","evenodd"),this.fill()},fillStroke:function(){this.stroke(),this.fill()},eoFillStroke:function(){this.current.element.setAttributeNS(null,"fill-rule","evenodd"),this.fillStroke()},closeStroke:function(){this.closePath(),this.stroke()},closeFillStroke:function(){this.closePath(),this.fillStroke()},paintSolidColorImageMask:function(){var e=this.current,t=document.createElementNS(l,"svg:rect");t.setAttributeNS(null,"x","0"),t.setAttributeNS(null,"y","0"),t.setAttributeNS(null,"width","1px"),t.setAttributeNS(null,"height","1px"),t.setAttributeNS(null,"fill",e.fillColor),this._ensureTransformGroup().appendChild(t)},paintJpegXObject:function(e,r,n){var i=this.objs.get(e),a=document.createElementNS(l,"svg:image");a.setAttributeNS(u,"xlink:href",i.src),a.setAttributeNS(null,"width",t(r)),a.setAttributeNS(null,"height",t(n)),a.setAttributeNS(null,"x","0"),a.setAttributeNS(null,"y",t(-n)),a.setAttributeNS(null,"transform","scale("+t(1/r)+" "+t(-1/n)+")"),this._ensureTransformGroup().appendChild(a)},paintImageXObject:function(e){var t=this.objs.get(e);t?this.paintInlineImageXObject(t):(0,n.warn)("Dependent image isn't ready yet")},paintInlineImageXObject:function(e,r){var n=e.width,i=e.height,a=s(e,this.forceDataSchema),o=document.createElementNS(l,"svg:rect");o.setAttributeNS(null,"x","0"),o.setAttributeNS(null,"y","0"),o.setAttributeNS(null,"width",t(n)),o.setAttributeNS(null,"height",t(i)),this.current.element=o,this.clip("nonzero");var c=document.createElementNS(l,"svg:image");c.setAttributeNS(u,"xlink:href",a),c.setAttributeNS(null,"x","0"),c.setAttributeNS(null,"y",t(-i)),c.setAttributeNS(null,"width",t(n)+"px"),c.setAttributeNS(null,"height",t(i)+"px"),c.setAttributeNS(null,"transform","scale("+t(1/n)+" "+t(-1/i)+")"),r?r.appendChild(c):this._ensureTransformGroup().appendChild(c)},paintImageMaskXObject:function(e){var r=this.current,n=e.width,i=e.height,a=r.fillColor;r.maskId="mask"+p++;var s=document.createElementNS(l,"svg:mask");s.setAttributeNS(null,"id",r.maskId);var o=document.createElementNS(l,"svg:rect");o.setAttributeNS(null,"x","0"),o.setAttributeNS(null,"y","0"),o.setAttributeNS(null,"width",t(n)),o.setAttributeNS(null,"height",t(i)),o.setAttributeNS(null,"fill",a),o.setAttributeNS(null,"mask","url(#"+r.maskId+")"),this.defs.appendChild(s),this._ensureTransformGroup().appendChild(o),this.paintInlineImageXObject(e,s)},paintFormXObjectBegin:function(e,r){if((0,n.isArray)(e)&&6===e.length&&this.transform(e[0],e[1],e[2],e[3],e[4],e[5]),(0,n.isArray)(r)&&4===r.length){var i=r[2]-r[0],a=r[3]-r[1],s=document.createElementNS(l,"svg:rect");s.setAttributeNS(null,"x",r[0]),s.setAttributeNS(null,"y",r[1]),s.setAttributeNS(null,"width",t(i)),s.setAttributeNS(null,"height",t(a)),this.current.element=s,this.clip("nonzero"),this.endPath()}},paintFormXObjectEnd:function(){},_initialize:function(e){var t=document.createElementNS(l,"svg:svg");t.setAttributeNS(null,"version","1.1"),t.setAttributeNS(null,"width",e.width+"px"),t.setAttributeNS(null,"height",e.height+"px"),t.setAttributeNS(null,"preserveAspectRatio","none"),t.setAttributeNS(null,"viewBox","0 0 "+e.width+" "+e.height);var n=document.createElementNS(l,"svg:defs");t.appendChild(n),this.defs=n;var i=document.createElementNS(l,"svg:g");return i.setAttributeNS(null,"transform",r(e.transform)),t.appendChild(i),this.svg=i,t},_ensureClipGroup:function(){if(!this.current.clipGroup){var e=document.createElementNS(l,"svg:g");e.setAttributeNS(null,"clip-path",this.current.activeClipUrl),this.svg.appendChild(e),this.current.clipGroup=e}return this.current.clipGroup},_ensureTransformGroup:function(){return this.tgrp||(this.tgrp=document.createElementNS(l,"svg:g"),this.tgrp.setAttributeNS(null,"transform",r(this.transformMatrix)),this.current.activeClipUrl?this._ensureClipGroup().appendChild(this.tgrp):this.svg.appendChild(this.tgrp)),this.tgrp}},i}(),t.SVGGraphics=i},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.renderTextLayer=void 0;var n=r(0),i=r(1),a=function(){var e=1e5,t=/\S/;function r(e){return!t.test(e)}var a=["left: ",0,"px; top: ",0,"px; font-size: ",0,"px; font-family: ","",";"];function s(e,t,s){var o=document.createElement("div"),l={style:null,angle:0,canvasWidth:0,isWhitespace:!1,originalTransform:null,paddingBottom:0,paddingLeft:0,paddingRight:0,paddingTop:0,scale:1};if(e._textDivs.push(o),r(t.str))return l.isWhitespace=!0,void e._textDivProperties.set(o,l);var c=n.Util.transform(e._viewport.transform,t.transform),u=Math.atan2(c[1],c[0]),h=s[t.fontName];h.vertical&&(u+=Math.PI/2);var d,f,p=Math.sqrt(c[2]*c[2]+c[3]*c[3]),m=p;if(h.ascent?m=h.ascent*m:h.descent&&(m=(1+h.descent)*m),0===u?(d=c[4],f=c[5]-m):(d=c[4]+m*Math.sin(u),f=c[5]-m*Math.cos(u)),a[1]=d,a[3]=f,a[5]=p,a[7]=h.fontFamily,l.style=a.join(""),o.setAttribute("style",l.style),o.textContent=t.str,(0,i.getDefaultSetting)("pdfBug")&&(o.dataset.fontName=t.fontName),0!==u&&(l.angle=u*(180/Math.PI)),t.str.length>1&&(h.vertical?l.canvasWidth=t.height*e._viewport.scale:l.canvasWidth=t.width*e._viewport.scale),e._textDivProperties.set(o,l),e._textContentStream&&e._layoutText(o),e._enhanceTextSelection){var g=1,v=0;0!==u&&(g=Math.cos(u),v=Math.sin(u));var b,_,y=(h.vertical?t.height:t.width)*e._viewport.scale,A=p;0!==u?(b=[g,v,-v,g,d,f],_=n.Util.getAxialAlignedBoundingBox([0,0,y,A],b)):_=[d,f,d+y,f+A],e._bounds.push({left:_[0],top:_[1],right:_[2],bottom:_[3],div:o,size:[y,A],m:b})}}function o(t){if(!t._canceled){var r=t._textDivs,n=t._capability,i=r.length;if(i>e)return t._renderingDone=!0,void n.resolve();if(!t._textContentStream)for(var a=0;a<i;a++)t._layoutText(r[a]);t._renderingDone=!0,n.resolve()}}function l(e){for(var t=e._bounds,r=e._viewport,i=c(r.width,r.height,t),a=0;a<i.length;a++){var s=t[a].div,o=e._textDivProperties.get(s);if(0!==o.angle){var l=i[a],u=t[a],h=u.m,d=h[0],f=h[1],p=[[0,0],[0,u.size[1]],[u.size[0],0],u.size],m=new Float64Array(64);p.forEach((function(e,t){var r=n.Util.applyTransform(e,h);m[t+0]=d&&(l.left-r[0])/d,m[t+4]=f&&(l.top-r[1])/f,m[t+8]=d&&(l.right-r[0])/d,m[t+12]=f&&(l.bottom-r[1])/f,m[t+16]=f&&(l.left-r[0])/-f,m[t+20]=d&&(l.top-r[1])/d,m[t+24]=f&&(l.right-r[0])/-f,m[t+28]=d&&(l.bottom-r[1])/d,m[t+32]=d&&(l.left-r[0])/-d,m[t+36]=f&&(l.top-r[1])/-f,m[t+40]=d&&(l.right-r[0])/-d,m[t+44]=f&&(l.bottom-r[1])/-f,m[t+48]=f&&(l.left-r[0])/f,m[t+52]=d&&(l.top-r[1])/-d,m[t+56]=f&&(l.right-r[0])/f,m[t+60]=d&&(l.bottom-r[1])/-d}));var g=function(e,t,r){for(var n=0,i=0;i<r;i++){var a=e[t++];a>0&&(n=n?Math.min(a,n):a)}return n},v=1+Math.min(Math.abs(d),Math.abs(f));o.paddingLeft=g(m,32,16)/v,o.paddingTop=g(m,48,16)/v,o.paddingRight=g(m,0,16)/v,o.paddingBottom=g(m,16,16)/v,e._textDivProperties.set(s,o)}else o.paddingLeft=t[a].left-i[a].left,o.paddingTop=t[a].top-i[a].top,o.paddingRight=i[a].right-t[a].right,o.paddingBottom=i[a].bottom-t[a].bottom,e._textDivProperties.set(s,o)}}function c(e,t,r){var n=r.map((function(e,t){return{x1:e.left,y1:e.top,x2:e.right,y2:e.bottom,index:t,x1New:void 0,x2New:void 0}}));u(e,n);var i=new Array(r.length);return n.forEach((function(e){var t=e.index;i[t]={left:e.x1New,top:0,right:e.x2New,bottom:0}})),r.map((function(t,r){var a=i[r],s=n[r];s.x1=t.top,s.y1=e-a.right,s.x2=t.bottom,s.y2=e-a.left,s.index=r,s.x1New=void 0,s.x2New=void 0})),u(t,n),n.forEach((function(e){var t=e.index;i[t].top=e.x1New,i[t].bottom=e.x2New})),i}function u(e,t){t.sort((function(e,t){return e.x1-t.x1||e.index-t.index}));var r={x1:-1/0,y1:-1/0,x2:0,y2:1/0,index:-1,x1New:0,x2New:0},n=[{start:-1/0,end:1/0,boundary:r}];t.forEach((function(e){var t=0;while(t<n.length&&n[t].end<=e.y1)t++;var r,i,a=n.length-1;while(a>=0&&n[a].start>=e.y2)a--;var s,o,l=-1/0;for(s=t;s<=a;s++){var c;r=n[s],i=r.boundary,c=i.x2>e.x1?i.index>e.index?i.x1New:e.x1:void 0===i.x2New?(i.x2+e.x1)/2:i.x2New,c>l&&(l=c)}for(e.x1New=l,s=t;s<=a;s++)r=n[s],i=r.boundary,void 0===i.x2New?i.x2>e.x1?i.index>e.index&&(i.x2New=i.x2):i.x2New=l:i.x2New>l&&(i.x2New=Math.max(l,i.x2));var u=[],h=null;for(s=t;s<=a;s++){r=n[s],i=r.boundary;var d=i.x2>e.x2?i:e;h===d?u[u.length-1].end=r.end:(u.push({start:r.start,end:r.end,boundary:d}),h=d)}for(n[t].start<e.y1&&(u[0].start=e.y1,u.unshift({start:n[t].start,end:e.y1,boundary:n[t].boundary})),e.y2<n[a].end&&(u[u.length-1].end=e.y2,u.push({start:e.y2,end:n[a].end,boundary:n[a].boundary})),s=t;s<=a;s++)if(r=n[s],i=r.boundary,void 0===i.x2New){var f=!1;for(o=t-1;!f&&o>=0&&n[o].start>=i.y1;o--)f=n[o].boundary===i;for(o=a+1;!f&&o<n.length&&n[o].end<=i.y2;o++)f=n[o].boundary===i;for(o=0;!f&&o<u.length;o++)f=u[o].boundary===i;f||(i.x2New=l)}Array.prototype.splice.apply(n,[t,a-t+1].concat(u))})),n.forEach((function(t){var r=t.boundary;void 0===r.x2New&&(r.x2New=Math.max(e,r.x2))}))}function h(e){var t=e.textContent,r=e.textContentStream,i=e.container,a=e.viewport,s=e.textDivs,o=e.textContentItemsStr,l=e.enhanceTextSelection;this._textContent=t,this._textContentStream=r,this._container=i,this._viewport=a,this._textDivs=s||[],this._textContentItemsStr=o||[],this._enhanceTextSelection=!!l,this._reader=null,this._layoutTextLastFontSize=null,this._layoutTextLastFontFamily=null,this._layoutTextCtx=null,this._textDivProperties=new WeakMap,this._renderingDone=!1,this._canceled=!1,this._capability=(0,n.createPromiseCapability)(),this._renderTimer=null,this._bounds=[]}function d(e){var t=new h({textContent:e.textContent,textContentStream:e.textContentStream,container:e.container,viewport:e.viewport,textDivs:e.textDivs,textContentItemsStr:e.textContentItemsStr,enhanceTextSelection:e.enhanceTextSelection});return t._render(e.timeout),t}return h.prototype={get promise(){return this._capability.promise},cancel:function(){this._reader&&(this._reader.cancel(new n.AbortException("text layer task cancelled")),this._reader=null),this._canceled=!0,null!==this._renderTimer&&(clearTimeout(this._renderTimer),this._renderTimer=null),this._capability.reject("canceled")},_processItems:function(e,t){for(var r=0,n=e.length;r<n;r++)this._textContentItemsStr.push(e[r].str),s(this,e[r],t)},_layoutText:function(e){var t=this._container,r=this._textDivProperties.get(e);if(!r.isWhitespace){var n=e.style.fontSize,a=e.style.fontFamily;n===this._layoutTextLastFontSize&&a===this._layoutTextLastFontFamily||(this._layoutTextCtx.font=n+" "+a,this._lastFontSize=n,this._lastFontFamily=a);var s=this._layoutTextCtx.measureText(e.textContent).width,o="";0!==r.canvasWidth&&s>0&&(r.scale=r.canvasWidth/s,o="scaleX("+r.scale+")"),0!==r.angle&&(o="rotate("+r.angle+"deg) "+o),""!==o&&(r.originalTransform=o,i.CustomStyle.setProp("transform",e,o)),this._textDivProperties.set(e,r),t.appendChild(e)}},_render:function(e){var t=this,r=(0,n.createPromiseCapability)(),i=Object.create(null),a=document.createElement("canvas");if(a.mozOpaque=!0,this._layoutTextCtx=a.getContext("2d",{alpha:!1}),this._textContent){var s=this._textContent.items,l=this._textContent.styles;this._processItems(s,l),r.resolve()}else{if(!this._textContentStream)throw new Error('Neither "textContent" nor "textContentStream" parameters specified.');var c=function e(){t._reader.read().then((function(a){var s=a.value,o=a.done;o?r.resolve():(n.Util.extendObj(i,s.styles),t._processItems(s.items,i),e())}),r.reject)};this._reader=this._textContentStream.getReader(),c()}r.promise.then((function(){i=null,e?t._renderTimer=setTimeout((function(){o(t),t._renderTimer=null}),e):o(t)}),this._capability.reject)},expandTextDivs:function(e){if(this._enhanceTextSelection&&this._renderingDone){null!==this._bounds&&(l(this),this._bounds=null);for(var t=0,r=this._textDivs.length;t<r;t++){var n=this._textDivs[t],a=this._textDivProperties.get(n);if(!a.isWhitespace)if(e){var s="",o="";1!==a.scale&&(s="scaleX("+a.scale+")"),0!==a.angle&&(s="rotate("+a.angle+"deg) "+s),0!==a.paddingLeft&&(o+=" padding-left: "+a.paddingLeft/a.scale+"px;",s+=" translateX("+-a.paddingLeft/a.scale+"px)"),0!==a.paddingTop&&(o+=" padding-top: "+a.paddingTop+"px;",s+=" translateY("+-a.paddingTop+"px)"),0!==a.paddingRight&&(o+=" padding-right: "+a.paddingRight/a.scale+"px;"),0!==a.paddingBottom&&(o+=" padding-bottom: "+a.paddingBottom+"px;"),""!==o&&n.setAttribute("style",a.style+o),""!==s&&i.CustomStyle.setProp("transform",n,s)}else n.style.padding=0,i.CustomStyle.setProp("transform",n,a.originalTransform||"")}}}},d}();t.renderTextLayer=a},function(e,t,r){"use strict";function n(e){return e.replace(/>\\376\\377([^<]+)/g,(function(e,t){for(var r=t.replace(/\\([0-3])([0-7])([0-7])/g,(function(e,t,r,n){return String.fromCharCode(64*t+8*r+1*n)})),n="",i=0;i<r.length;i+=2){var a=256*r.charCodeAt(i)+r.charCodeAt(i+1);n+=a>=32&&a<127&&60!==a&&62!==a&&38!==a?String.fromCharCode(a):"&#x"+(65536+a).toString(16).substring(1)+";"}return">"+n}))}function i(e){if("string"===typeof e){e=n(e);var t=new DOMParser;e=t.parseFromString(e,"application/xml")}else if(!(e instanceof Document))throw new Error("Metadata: Invalid metadata object");this.metaDocument=e,this.metadata=Object.create(null),this.parse()}Object.defineProperty(t,"__esModule",{value:!0}),i.prototype={parse:function(){var e=this.metaDocument,t=e.documentElement;if("rdf:rdf"!==t.nodeName.toLowerCase()){t=t.firstChild;while(t&&"rdf:rdf"!==t.nodeName.toLowerCase())t=t.nextSibling}var r=t?t.nodeName.toLowerCase():null;if(t&&"rdf:rdf"===r&&t.hasChildNodes()){var n,i,a,s,o,l,c,u=t.childNodes;for(s=0,l=u.length;s<l;s++)if(n=u[s],"rdf:description"===n.nodeName.toLowerCase())for(o=0,c=n.childNodes.length;o<c;o++)"#text"!==n.childNodes[o].nodeName.toLowerCase()&&(i=n.childNodes[o],a=i.nodeName.toLowerCase(),this.metadata[a]=i.textContent.trim())}},get:function(e){return this.metadata[e]||null},has:function(e){return"undefined"!==typeof this.metadata[e]}},t.Metadata=i},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.WebGLUtils=void 0;var n=r(1),i=r(0),a=function(){function e(e,t,r){var n=e.createShader(r);e.shaderSource(n,t),e.compileShader(n);var i=e.getShaderParameter(n,e.COMPILE_STATUS);if(!i){var a=e.getShaderInfoLog(n);throw new Error("Error during shader compilation: "+a)}return n}function t(t,r){return e(t,r,t.VERTEX_SHADER)}function r(t,r){return e(t,r,t.FRAGMENT_SHADER)}function a(e,t){for(var r=e.createProgram(),n=0,i=t.length;n<i;++n)e.attachShader(r,t[n]);e.linkProgram(r);var a=e.getProgramParameter(r,e.LINK_STATUS);if(!a){var s=e.getProgramInfoLog(r);throw new Error("Error during program linking: "+s)}return r}function s(e,t,r){e.activeTexture(r);var n=e.createTexture();return e.bindTexture(e.TEXTURE_2D,n),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_S,e.CLAMP_TO_EDGE),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_T,e.CLAMP_TO_EDGE),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MIN_FILTER,e.NEAREST),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MAG_FILTER,e.NEAREST),e.texImage2D(e.TEXTURE_2D,0,e.RGBA,e.RGBA,e.UNSIGNED_BYTE,t),n}var o,l;function c(){o||(l=document.createElement("canvas"),o=l.getContext("webgl",{premultipliedalpha:!1}))}var u="  attribute vec2 a_position;                                      attribute vec2 a_texCoord;                                                                                                      uniform vec2 u_resolution;                                                                                                      varying vec2 v_texCoord;                                                                                                        void main() {                                                     vec2 clipSpace = (a_position / u_resolution) * 2.0 - 1.0;       gl_Position = vec4(clipSpace * vec2(1, -1), 0, 1);                                                                              v_texCoord = a_texCoord;                                      }                                                             ",h="  precision mediump float;                                                                                                        uniform vec4 u_backdrop;                                        uniform int u_subtype;                                          uniform sampler2D u_image;                                      uniform sampler2D u_mask;                                                                                                       varying vec2 v_texCoord;                                                                                                        void main() {                                                     vec4 imageColor = texture2D(u_image, v_texCoord);               vec4 maskColor = texture2D(u_mask, v_texCoord);                 if (u_backdrop.a > 0.0) {                                         maskColor.rgb = maskColor.rgb * maskColor.a +                                   u_backdrop.rgb * (1.0 - maskColor.a);         }                                                               float lum;                                                      if (u_subtype == 0) {                                             lum = maskColor.a;                                            } else {                                                          lum = maskColor.r * 0.3 + maskColor.g * 0.59 +                        maskColor.b * 0.11;                                     }                                                               imageColor.a *= lum;                                            imageColor.rgb *= imageColor.a;                                 gl_FragColor = imageColor;                                    }                                                             ",d=null;function f(){var e,n;c(),e=l,l=null,n=o,o=null;var i=t(n,u),s=r(n,h),f=a(n,[i,s]);n.useProgram(f);var p={};p.gl=n,p.canvas=e,p.resolutionLocation=n.getUniformLocation(f,"u_resolution"),p.positionLocation=n.getAttribLocation(f,"a_position"),p.backdropLocation=n.getUniformLocation(f,"u_backdrop"),p.subtypeLocation=n.getUniformLocation(f,"u_subtype");var m=n.getAttribLocation(f,"a_texCoord"),g=n.getUniformLocation(f,"u_image"),v=n.getUniformLocation(f,"u_mask"),b=n.createBuffer();n.bindBuffer(n.ARRAY_BUFFER,b),n.bufferData(n.ARRAY_BUFFER,new Float32Array([0,0,1,0,0,1,0,1,1,0,1,1]),n.STATIC_DRAW),n.enableVertexAttribArray(m),n.vertexAttribPointer(m,2,n.FLOAT,!1,0,0),n.uniform1i(g,0),n.uniform1i(v,1),d=p}function p(e,t,r){var n=e.width,i=e.height;d||f();var a=d,o=a.canvas,l=a.gl;o.width=n,o.height=i,l.viewport(0,0,l.drawingBufferWidth,l.drawingBufferHeight),l.uniform2f(a.resolutionLocation,n,i),r.backdrop?l.uniform4f(a.resolutionLocation,r.backdrop[0],r.backdrop[1],r.backdrop[2],1):l.uniform4f(a.resolutionLocation,0,0,0,0),l.uniform1i(a.subtypeLocation,"Luminosity"===r.subtype?1:0);var c=s(l,e,l.TEXTURE0),u=s(l,t,l.TEXTURE1),h=l.createBuffer();return l.bindBuffer(l.ARRAY_BUFFER,h),l.bufferData(l.ARRAY_BUFFER,new Float32Array([0,0,n,0,0,i,0,i,n,0,n,i]),l.STATIC_DRAW),l.enableVertexAttribArray(a.positionLocation),l.vertexAttribPointer(a.positionLocation,2,l.FLOAT,!1,0,0),l.clearColor(0,0,0,0),l.enable(l.BLEND),l.blendFunc(l.ONE,l.ONE_MINUS_SRC_ALPHA),l.clear(l.COLOR_BUFFER_BIT),l.drawArrays(l.TRIANGLES,0,6),l.flush(),l.deleteTexture(c),l.deleteTexture(u),l.deleteBuffer(h),o}var m="  attribute vec2 a_position;                                      attribute vec3 a_color;                                                                                                         uniform vec2 u_resolution;                                      uniform vec2 u_scale;                                           uniform vec2 u_offset;                                                                                                          varying vec4 v_color;                                                                                                           void main() {                                                     vec2 position = (a_position + u_offset) * u_scale;              vec2 clipSpace = (position / u_resolution) * 2.0 - 1.0;         gl_Position = vec4(clipSpace * vec2(1, -1), 0, 1);                                                                              v_color = vec4(a_color / 255.0, 1.0);                         }                                                             ",g="  precision mediump float;                                                                                                        varying vec4 v_color;                                                                                                           void main() {                                                     gl_FragColor = v_color;                                       }                                                             ",v=null;function b(){var e,n;c(),e=l,l=null,n=o,o=null;var i=t(n,m),s=r(n,g),u=a(n,[i,s]);n.useProgram(u);var h={};h.gl=n,h.canvas=e,h.resolutionLocation=n.getUniformLocation(u,"u_resolution"),h.scaleLocation=n.getUniformLocation(u,"u_scale"),h.offsetLocation=n.getUniformLocation(u,"u_offset"),h.positionLocation=n.getAttribLocation(u,"a_position"),h.colorLocation=n.getAttribLocation(u,"a_color"),v=h}function _(e,t,r,n,i){v||b();var a=v,s=a.canvas,o=a.gl;s.width=e,s.height=t,o.viewport(0,0,o.drawingBufferWidth,o.drawingBufferHeight),o.uniform2f(a.resolutionLocation,e,t);var l,c,u,h=0;for(l=0,c=n.length;l<c;l++)switch(n[l].type){case"lattice":u=n[l].coords.length/n[l].verticesPerRow|0,h+=(u-1)*(n[l].verticesPerRow-1)*6;break;case"triangles":h+=n[l].coords.length;break}var d=new Float32Array(2*h),f=new Uint8Array(3*h),p=i.coords,m=i.colors,g=0,_=0;for(l=0,c=n.length;l<c;l++){var y=n[l],A=y.coords,S=y.colors;switch(y.type){case"lattice":var w=y.verticesPerRow;u=A.length/w|0;for(var P=1;P<u;P++)for(var C=P*w+1,R=1;R<w;R++,C++)d[g]=p[A[C-w-1]],d[g+1]=p[A[C-w-1]+1],d[g+2]=p[A[C-w]],d[g+3]=p[A[C-w]+1],d[g+4]=p[A[C-1]],d[g+5]=p[A[C-1]+1],f[_]=m[S[C-w-1]],f[_+1]=m[S[C-w-1]+1],f[_+2]=m[S[C-w-1]+2],f[_+3]=m[S[C-w]],f[_+4]=m[S[C-w]+1],f[_+5]=m[S[C-w]+2],f[_+6]=m[S[C-1]],f[_+7]=m[S[C-1]+1],f[_+8]=m[S[C-1]+2],d[g+6]=d[g+2],d[g+7]=d[g+3],d[g+8]=d[g+4],d[g+9]=d[g+5],d[g+10]=p[A[C]],d[g+11]=p[A[C]+1],f[_+9]=f[_+3],f[_+10]=f[_+4],f[_+11]=f[_+5],f[_+12]=f[_+6],f[_+13]=f[_+7],f[_+14]=f[_+8],f[_+15]=m[S[C]],f[_+16]=m[S[C]+1],f[_+17]=m[S[C]+2],g+=12,_+=18;break;case"triangles":for(var k=0,x=A.length;k<x;k++)d[g]=p[A[k]],d[g+1]=p[A[k]+1],f[_]=m[S[k]],f[_+1]=m[S[k]+1],f[_+2]=m[S[k]+2],g+=2,_+=3;break}}r?o.clearColor(r[0]/255,r[1]/255,r[2]/255,1):o.clearColor(0,0,0,0),o.clear(o.COLOR_BUFFER_BIT);var T=o.createBuffer();o.bindBuffer(o.ARRAY_BUFFER,T),o.bufferData(o.ARRAY_BUFFER,d,o.STATIC_DRAW),o.enableVertexAttribArray(a.positionLocation),o.vertexAttribPointer(a.positionLocation,2,o.FLOAT,!1,0,0);var E=o.createBuffer();return o.bindBuffer(o.ARRAY_BUFFER,E),o.bufferData(o.ARRAY_BUFFER,f,o.STATIC_DRAW),o.enableVertexAttribArray(a.colorLocation),o.vertexAttribPointer(a.colorLocation,3,o.UNSIGNED_BYTE,!1,0,0),o.uniform2f(a.scaleLocation,i.scaleX,i.scaleY),o.uniform2f(a.offsetLocation,i.offsetX,i.offsetY),o.drawArrays(o.TRIANGLES,0,h),o.flush(),o.deleteBuffer(T),o.deleteBuffer(E),s}function y(){d&&d.canvas&&(d.canvas.width=0,d.canvas.height=0),v&&v.canvas&&(v.canvas.width=0,v.canvas.height=0),d=null,v=null}return{get isEnabled(){if((0,n.getDefaultSetting)("disableWebGL"))return!1;var e=!1;try{c(),e=!!o}catch(t){}return(0,i.shadow)(this,"isEnabled",e)},composeSMask:p,drawFigures:_,clear:y}}();t.WebGLUtils=a},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PDFJS=t.isWorker=t.globalScope=void 0;var n=r(2),i=r(1),a=r(0),s=r(3),o=r(6),l=r(5),c=r(4),u="undefined"===typeof window;a.globalScope.PDFJS||(a.globalScope.PDFJS={});var h=a.globalScope.PDFJS;h.version="1.9.426",h.build="2558a58d",h.pdfBug=!1,void 0!==h.verbosity&&(0,a.setVerbosityLevel)(h.verbosity),delete h.verbosity,Object.defineProperty(h,"verbosity",{get:function(){return(0,a.getVerbosityLevel)()},set:function(e){(0,a.setVerbosityLevel)(e)},enumerable:!0,configurable:!0}),h.VERBOSITY_LEVELS=a.VERBOSITY_LEVELS,h.OPS=a.OPS,h.UNSUPPORTED_FEATURES=a.UNSUPPORTED_FEATURES,h.isValidUrl=i.isValidUrl,h.shadow=a.shadow,h.createBlob=a.createBlob,h.createObjectURL=function(e,t){return(0,a.createObjectURL)(e,t,h.disableCreateObjectURL)},Object.defineProperty(h,"isLittleEndian",{configurable:!0,get:function(){return(0,a.shadow)(h,"isLittleEndian",(0,a.isLittleEndian)())}}),h.removeNullCharacters=a.removeNullCharacters,h.PasswordResponses=a.PasswordResponses,h.PasswordException=a.PasswordException,h.UnknownErrorException=a.UnknownErrorException,h.InvalidPDFException=a.InvalidPDFException,h.MissingPDFException=a.MissingPDFException,h.UnexpectedResponseException=a.UnexpectedResponseException,h.Util=a.Util,h.PageViewport=a.PageViewport,h.createPromiseCapability=a.createPromiseCapability,h.maxImageSize=void 0===h.maxImageSize?-1:h.maxImageSize,h.cMapUrl=void 0===h.cMapUrl?null:h.cMapUrl,h.cMapPacked=void 0!==h.cMapPacked&&h.cMapPacked,h.disableFontFace=void 0!==h.disableFontFace&&h.disableFontFace,h.imageResourcesPath=void 0===h.imageResourcesPath?"":h.imageResourcesPath,h.disableWorker=void 0!==h.disableWorker&&h.disableWorker,h.workerSrc=void 0===h.workerSrc?null:h.workerSrc,h.workerPort=void 0===h.workerPort?null:h.workerPort,h.disableRange=void 0!==h.disableRange&&h.disableRange,h.disableStream=void 0!==h.disableStream&&h.disableStream,h.disableAutoFetch=void 0!==h.disableAutoFetch&&h.disableAutoFetch,h.pdfBug=void 0!==h.pdfBug&&h.pdfBug,h.postMessageTransfers=void 0===h.postMessageTransfers||h.postMessageTransfers,h.disableCreateObjectURL=void 0!==h.disableCreateObjectURL&&h.disableCreateObjectURL,h.disableWebGL=void 0===h.disableWebGL||h.disableWebGL,h.externalLinkTarget=void 0===h.externalLinkTarget?i.LinkTarget.NONE:h.externalLinkTarget,h.externalLinkRel=void 0===h.externalLinkRel?i.DEFAULT_LINK_REL:h.externalLinkRel,h.isEvalSupported=void 0===h.isEvalSupported||h.isEvalSupported,h.pdfjsNext=void 0!==h.pdfjsNext&&h.pdfjsNext;var d=h.openExternalLinksInNewWindow;delete h.openExternalLinksInNewWindow,Object.defineProperty(h,"openExternalLinksInNewWindow",{get:function(){return h.externalLinkTarget===i.LinkTarget.BLANK},set:function(e){e&&(0,a.deprecated)('PDFJS.openExternalLinksInNewWindow, please use "PDFJS.externalLinkTarget = PDFJS.LinkTarget.BLANK" instead.'),h.externalLinkTarget===i.LinkTarget.NONE?h.externalLinkTarget=e?i.LinkTarget.BLANK:i.LinkTarget.NONE:(0,a.warn)("PDFJS.externalLinkTarget is already initialized")},enumerable:!0,configurable:!0}),d&&(h.openExternalLinksInNewWindow=d),h.getDocument=n.getDocument,h.LoopbackPort=n.LoopbackPort,h.PDFDataRangeTransport=n.PDFDataRangeTransport,h.PDFWorker=n.PDFWorker,h.hasCanvasTypedArrays=!0,h.CustomStyle=i.CustomStyle,h.LinkTarget=i.LinkTarget,h.addLinkAttributes=i.addLinkAttributes,h.getFilenameFromUrl=i.getFilenameFromUrl,h.isExternalLinkTargetSet=i.isExternalLinkTargetSet,h.AnnotationLayer=s.AnnotationLayer,h.renderTextLayer=l.renderTextLayer,h.Metadata=o.Metadata,h.SVGGraphics=c.SVGGraphics,h.UnsupportedManager=n._UnsupportedManager,t.globalScope=a.globalScope,t.isWorker=u,t.PDFJS=h},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.NetworkManager=t.PDFNetworkStream=void 0;var n=r(0),i=r(2),a=200,s=206;function o(e,t){this.url=e,t=t||{},this.isHttp=/^https?:/i.test(e),this.httpHeaders=this.isHttp&&t.httpHeaders||{},this.withCredentials=t.withCredentials||!1,this.getXhr=t.getXhr||function(){return new XMLHttpRequest},this.currXhrId=0,this.pendingRequests=Object.create(null),this.loadedRequests=Object.create(null)}function l(e){var t=e.response;if("string"!==typeof t)return t;for(var r=t.length,n=new Uint8Array(r),i=0;i<r;i++)n[i]=255&t.charCodeAt(i);return n.buffer}var c=function(){try{var e=new XMLHttpRequest;return e.open("GET",n.globalScope.location.href),e.responseType="moz-chunked-arraybuffer","moz-chunked-arraybuffer"===e.responseType}catch(t){return!1}}();function u(e){this._options=e;var t=e.source;this._manager=new o(t.url,{httpHeaders:t.httpHeaders,withCredentials:t.withCredentials}),this._rangeChunkSize=t.rangeChunkSize,this._fullRequestReader=null,this._rangeRequestReaders=[]}function h(e,t){this._manager=e;var r=t.source,i={onHeadersReceived:this._onHeadersReceived.bind(this),onProgressiveData:r.disableStream?null:this._onProgressiveData.bind(this),onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)};this._url=r.url,this._fullRequestId=e.requestFull(i),this._headersReceivedCapability=(0,n.createPromiseCapability)(),this._disableRange=t.disableRange||!1,this._contentLength=r.length,this._rangeChunkSize=r.rangeChunkSize,this._rangeChunkSize||this._disableRange||(this._disableRange=!0),this._isStreamingSupported=!1,this._isRangeSupported=!1,this._cachedChunks=[],this._requests=[],this._done=!1,this._storedError=void 0,this.onProgress=null}function d(e,t,r){this._manager=e;var n={onDone:this._onDone.bind(this),onProgress:this._onProgress.bind(this)};this._requestId=e.requestRange(t,r,n),this._requests=[],this._queuedChunk=null,this._done=!1,this.onProgress=null,this.onClosed=null}o.prototype={requestRange:function(e,t,r){var n={begin:e,end:t};for(var i in r)n[i]=r[i];return this.request(n)},requestFull:function(e){return this.request(e)},request:function(e){var t=this.getXhr(),r=this.currXhrId++,n=this.pendingRequests[r]={xhr:t};for(var i in t.open("GET",this.url),t.withCredentials=this.withCredentials,this.httpHeaders){var a=this.httpHeaders[i];"undefined"!==typeof a&&t.setRequestHeader(i,a)}if(this.isHttp&&"begin"in e&&"end"in e){var s=e.begin+"-"+(e.end-1);t.setRequestHeader("Range","bytes="+s),n.expectedStatus=206}else n.expectedStatus=200;var o=c&&!!e.onProgressiveData;return o?(t.responseType="moz-chunked-arraybuffer",n.onProgressiveData=e.onProgressiveData,n.mozChunked=!0):t.responseType="arraybuffer",e.onError&&(t.onerror=function(r){e.onError(t.status)}),t.onreadystatechange=this.onStateChange.bind(this,r),t.onprogress=this.onProgress.bind(this,r),n.onHeadersReceived=e.onHeadersReceived,n.onDone=e.onDone,n.onError=e.onError,n.onProgress=e.onProgress,t.send(null),r},onProgress:function(e,t){var r=this.pendingRequests[e];if(r){if(r.mozChunked){var n=l(r.xhr);r.onProgressiveData(n)}var i=r.onProgress;i&&i(t)}},onStateChange:function(e,t){var r=this.pendingRequests[e];if(r){var n=r.xhr;if(n.readyState>=2&&r.onHeadersReceived&&(r.onHeadersReceived(),delete r.onHeadersReceived),4===n.readyState&&e in this.pendingRequests)if(delete this.pendingRequests[e],0===n.status&&this.isHttp)r.onError&&r.onError(n.status);else{var i=n.status||a,o=i===a&&r.expectedStatus===s;if(o||i===r.expectedStatus){this.loadedRequests[e]=!0;var c=l(n);if(i===s){var u=n.getResponseHeader("Content-Range"),h=/bytes (\d+)-(\d+)\/(\d+)/.exec(u),d=parseInt(h[1],10);r.onDone({begin:d,chunk:c})}else r.onProgressiveData?r.onDone(null):c?r.onDone({begin:0,chunk:c}):r.onError&&r.onError(n.status)}else r.onError&&r.onError(n.status)}}},hasPendingRequests:function(){for(var e in this.pendingRequests)return!0;return!1},getRequestXhr:function(e){return this.pendingRequests[e].xhr},isStreamingRequest:function(e){return!!this.pendingRequests[e].onProgressiveData},isPendingRequest:function(e){return e in this.pendingRequests},isLoadedRequest:function(e){return e in this.loadedRequests},abortAllRequests:function(){for(var e in this.pendingRequests)this.abortRequest(0|e)},abortRequest:function(e){var t=this.pendingRequests[e].xhr;delete this.pendingRequests[e],t.abort()}},u.prototype={_onRangeRequestReaderClosed:function(e){var t=this._rangeRequestReaders.indexOf(e);t>=0&&this._rangeRequestReaders.splice(t,1)},getFullReader:function(){return(0,n.assert)(!this._fullRequestReader),this._fullRequestReader=new h(this._manager,this._options),this._fullRequestReader},getRangeReader:function(e,t){var r=new d(this._manager,e,t);return r.onClosed=this._onRangeRequestReaderClosed.bind(this),this._rangeRequestReaders.push(r),r},cancelAllRequests:function(e){this._fullRequestReader&&this._fullRequestReader.cancel(e);var t=this._rangeRequestReaders.slice(0);t.forEach((function(t){t.cancel(e)}))}},h.prototype={_validateRangeRequestCapabilities:function(){if(this._disableRange)return!1;var e=this._manager;if(!e.isHttp)return!1;var t=this._fullRequestId,r=e.getRequestXhr(t);if("bytes"!==r.getAllResponseHeaders("Accept-Ranges"))return!1;var i=r.getResponseHeader("Content-Encoding")||"identity";if("identity"!==i)return!1;var a=r.getResponseHeader("Content-Length");return a=parseInt(a,10),!!(0,n.isInt)(a)&&(this._contentLength=a,!(a<=2*this._rangeChunkSize))},_onHeadersReceived:function(){this._validateRangeRequestCapabilities()&&(this._isRangeSupported=!0);var e=this._manager,t=this._fullRequestId;e.isStreamingRequest(t)?this._isStreamingSupported=!0:this._isRangeSupported&&e.abortRequest(t),this._headersReceivedCapability.resolve()},_onProgressiveData:function(e){if(this._requests.length>0){var t=this._requests.shift();t.resolve({value:e,done:!1})}else this._cachedChunks.push(e)},_onDone:function(e){e&&this._onProgressiveData(e.chunk),this._done=!0,this._cachedChunks.length>0||(this._requests.forEach((function(e){e.resolve({value:void 0,done:!0})})),this._requests=[])},_onError:function(e){var t,r=this._url;t=404===e||0===e&&/^file:/.test(r)?new n.MissingPDFException('Missing PDF "'+r+'".'):new n.UnexpectedResponseException("Unexpected server response ("+e+') while retrieving PDF "'+r+'".',e),this._storedError=t,this._headersReceivedCapability.reject(t),this._requests.forEach((function(e){e.reject(t)})),this._requests=[],this._cachedChunks=[]},_onProgress:function(e){this.onProgress&&this.onProgress({loaded:e.loaded,total:e.lengthComputable?e.total:this._contentLength})},get isRangeSupported(){return this._isRangeSupported},get isStreamingSupported(){return this._isStreamingSupported},get contentLength(){return this._contentLength},get headersReady(){return this._headersReceivedCapability.promise},read:function(){if(this._storedError)return Promise.reject(this._storedError);if(this._cachedChunks.length>0){var e=this._cachedChunks.shift();return Promise.resolve({value:e,done:!1})}if(this._done)return Promise.resolve({value:void 0,done:!0});var t=(0,n.createPromiseCapability)();return this._requests.push(t),t.promise},cancel:function(e){this._done=!0,this._headersReceivedCapability.reject(e),this._requests.forEach((function(e){e.resolve({value:void 0,done:!0})})),this._requests=[],this._manager.isPendingRequest(this._fullRequestId)&&this._manager.abortRequest(this._fullRequestId),this._fullRequestReader=null}},d.prototype={_close:function(){this.onClosed&&this.onClosed(this)},_onDone:function(e){var t=e.chunk;if(this._requests.length>0){var r=this._requests.shift();r.resolve({value:t,done:!1})}else this._queuedChunk=t;this._done=!0,this._requests.forEach((function(e){e.resolve({value:void 0,done:!0})})),this._requests=[],this._close()},_onProgress:function(e){!this.isStreamingSupported&&this.onProgress&&this.onProgress({loaded:e.loaded})},get isStreamingSupported(){return!1},read:function(){if(null!==this._queuedChunk){var e=this._queuedChunk;return this._queuedChunk=null,Promise.resolve({value:e,done:!1})}if(this._done)return Promise.resolve({value:void 0,done:!0});var t=(0,n.createPromiseCapability)();return this._requests.push(t),t.promise},cancel:function(e){this._done=!0,this._requests.forEach((function(e){e.resolve({value:void 0,done:!0})})),this._requests=[],this._manager.isPendingRequest(this._requestId)&&this._manager.abortRequest(this._requestId),this._close()}},(0,i.setPDFNetworkStreamClass)(u),t.PDFNetworkStream=u,t.NetworkManager=o},function(e,t,r){"use strict";var n="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};(function(e,t){for(var r in t)e[r]=t[r]})(t,function(e){var t={};function r(n){if(t[n])return t[n].exports;var i=t[n]={i:n,l:!1,exports:{}};return e[n].call(i.exports,i,i.exports,r),i.l=!0,i.exports}return r.m=e,r.c=t,r.i=function(e){return e},r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{configurable:!1,enumerable:!0,get:n})},r.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=7)}([function(e,t,r){var i="function"===typeof Symbol&&"symbol"===n(Symbol.iterator)?function(e){return"undefined"===typeof e?"undefined":n(e)}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":"undefined"===typeof e?"undefined":n(e)},a=r(1),s=a.assert;function o(e){return"string"===typeof e||"symbol"===("undefined"===typeof e?"undefined":i(e))}function l(e,t,r){if("function"!==typeof e)throw new TypeError("Argument is not a function");return Function.prototype.apply.call(e,t,r)}t.typeIsObject=function(e){return"object"===("undefined"===typeof e?"undefined":i(e))&&null!==e||"function"===typeof e},t.createDataProperty=function(e,r,n){s(t.typeIsObject(e)),Object.defineProperty(e,r,{value:n,writable:!0,enumerable:!0,configurable:!0})},t.createArrayFromList=function(e){return e.slice()},t.ArrayBufferCopy=function(e,t,r,n,i){new Uint8Array(e).set(new Uint8Array(r,n,i),t)},t.CreateIterResultObject=function(e,t){s("boolean"===typeof t);var r={};return Object.defineProperty(r,"value",{value:e,enumerable:!0,writable:!0,configurable:!0}),Object.defineProperty(r,"done",{value:t,enumerable:!0,writable:!0,configurable:!0}),r},t.IsFiniteNonNegativeNumber=function(e){return!Number.isNaN(e)&&(e!==1/0&&!(e<0))},t.InvokeOrNoop=function(e,t,r){s(void 0!==e),s(o(t)),s(Array.isArray(r));var n=e[t];if(void 0!==n)return l(n,e,r)},t.PromiseInvokeOrNoop=function(e,r,n){s(void 0!==e),s(o(r)),s(Array.isArray(n));try{return Promise.resolve(t.InvokeOrNoop(e,r,n))}catch(i){return Promise.reject(i)}},t.PromiseInvokeOrPerformFallback=function(e,t,r,n,i){s(void 0!==e),s(o(t)),s(Array.isArray(r)),s(Array.isArray(i));var a=void 0;try{a=e[t]}catch(c){return Promise.reject(c)}if(void 0===a)return n.apply(null,i);try{return Promise.resolve(l(a,e,r))}catch(u){return Promise.reject(u)}},t.TransferArrayBuffer=function(e){return e.slice()},t.ValidateAndNormalizeHighWaterMark=function(e){if(e=Number(e),Number.isNaN(e)||e<0)throw new RangeError("highWaterMark property of a queuing strategy must be non-negative and non-NaN");return e},t.ValidateAndNormalizeQueuingStrategy=function(e,r){if(void 0!==e&&"function"!==typeof e)throw new TypeError("size property of a queuing strategy must be a function");return r=t.ValidateAndNormalizeHighWaterMark(r),{size:e,highWaterMark:r}}},function(e,t,r){function n(e){e&&e.constructor===i&&setTimeout((function(){throw e}),0)}function i(e){this.name="AssertionError",this.message=e||"",this.stack=(new Error).stack}function a(e,t){if(!e)throw new i(t)}i.prototype=Object.create(Error.prototype),i.prototype.constructor=i,e.exports={rethrowAssertionErrorRejection:n,AssertionError:i,assert:a}},function(e,t,r){var n=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}();function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var a=r(0),s=a.InvokeOrNoop,o=a.PromiseInvokeOrNoop,l=a.ValidateAndNormalizeQueuingStrategy,c=a.typeIsObject,u=r(1),h=u.assert,d=u.rethrowAssertionErrorRejection,f=r(3),p=f.DequeueValue,m=f.EnqueueValueWithSize,g=f.PeekQueueValue,v=f.ResetQueue,b=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.size,a=r.highWaterMark,s=void 0===a?1:a;i(this,e),this._state="writable",this._storedError=void 0,this._writer=void 0,this._writableStreamController=void 0,this._writeRequests=[],this._inFlightWriteRequest=void 0,this._closeRequest=void 0,this._inFlightCloseRequest=void 0,this._pendingAbortRequest=void 0,this._backpressure=!1;var o=t.type;if(void 0!==o)throw new RangeError("Invalid type is specified");this._writableStreamController=new Y(this,t,n,s),this._writableStreamController.__startSteps()}return n(e,[{key:"abort",value:function(e){return!1===y(this)?Promise.reject(ae("abort")):!0===A(this)?Promise.reject(new TypeError("Cannot abort a stream that already has a writer")):S(this,e)}},{key:"getWriter",value:function(){if(!1===y(this))throw ae("getWriter");return _(this)}},{key:"locked",get:function(){if(!1===y(this))throw ae("locked");return A(this)}}]),e}();function _(e){return new M(e)}function y(e){return!!c(e)&&!!Object.prototype.hasOwnProperty.call(e,"_writableStreamController")}function A(e){return h(!0===y(e),"IsWritableStreamLocked should only be used on known writable streams"),void 0!==e._writer}function S(e,t){var r=e._state;if("closed"===r)return Promise.resolve(void 0);if("errored"===r)return Promise.reject(e._storedError);var n=new TypeError("Requested to abort");if(void 0!==e._pendingAbortRequest)return Promise.reject(n);h("writable"===r||"erroring"===r,"state must be writable or erroring");var i=!1;"erroring"===r&&(i=!0,t=void 0);var a=new Promise((function(r,n){e._pendingAbortRequest={_resolve:r,_reject:n,_reason:t,_wasAlreadyErroring:i}}));return!1===i&&C(e,n),a}function w(e){h(!0===A(e)),h("writable"===e._state);var t=new Promise((function(t,r){var n={_resolve:t,_reject:r};e._writeRequests.push(n)}));return t}function P(e,t){var r=e._state;"writable"!==r?(h("erroring"===r),R(e)):C(e,t)}function C(e,t){h(void 0===e._storedError,"stream._storedError === undefined"),h("writable"===e._state,"state must be writable");var r=e._writableStreamController;h(void 0!==r,"controller must not be undefined"),e._state="erroring",e._storedError=t;var n=e._writer;void 0!==n&&z(n,t),!1===L(e)&&!0===r._started&&R(e)}function R(e){h("erroring"===e._state,"stream._state === erroring"),h(!1===L(e),"WritableStreamHasOperationMarkedInFlight(stream) === false"),e._state="errored",e._writableStreamController.__errorSteps();for(var t=e._storedError,r=0;r<e._writeRequests.length;r++){var n=e._writeRequests[r];n._reject(t)}if(e._writeRequests=[],void 0!==e._pendingAbortRequest){var i=e._pendingAbortRequest;if(e._pendingAbortRequest=void 0,!0===i._wasAlreadyErroring)return i._reject(t),void F(e);var a=e._writableStreamController.__abortSteps(i._reason);a.then((function(){i._resolve(),F(e)}),(function(t){i._reject(t),F(e)}))}else F(e)}function k(e){h(void 0!==e._inFlightWriteRequest),e._inFlightWriteRequest._resolve(void 0),e._inFlightWriteRequest=void 0}function x(e,t){h(void 0!==e._inFlightWriteRequest),e._inFlightWriteRequest._reject(t),e._inFlightWriteRequest=void 0,h("writable"===e._state||"erroring"===e._state),P(e,t)}function T(e){h(void 0!==e._inFlightCloseRequest),e._inFlightCloseRequest._resolve(void 0),e._inFlightCloseRequest=void 0;var t=e._state;h("writable"===t||"erroring"===t),"erroring"===t&&(e._storedError=void 0,void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._resolve(),e._pendingAbortRequest=void 0)),e._state="closed";var r=e._writer;void 0!==r&&fe(r),h(void 0===e._pendingAbortRequest,"stream._pendingAbortRequest === undefined"),h(void 0===e._storedError,"stream._storedError === undefined")}function E(e,t){h(void 0!==e._inFlightCloseRequest),e._inFlightCloseRequest._reject(t),e._inFlightCloseRequest=void 0,h("writable"===e._state||"erroring"===e._state),void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._reject(t),e._pendingAbortRequest=void 0),P(e,t)}function I(e){return void 0!==e._closeRequest||void 0!==e._inFlightCloseRequest}function L(e){return void 0!==e._inFlightWriteRequest||void 0!==e._inFlightCloseRequest}function D(e){h(void 0===e._inFlightCloseRequest),h(void 0!==e._closeRequest),e._inFlightCloseRequest=e._closeRequest,e._closeRequest=void 0}function O(e){h(void 0===e._inFlightWriteRequest,"there must be no pending write request"),h(0!==e._writeRequests.length,"writeRequests must not be empty"),e._inFlightWriteRequest=e._writeRequests.shift()}function F(e){h("errored"===e._state,'_stream_.[[state]] is `"errored"`'),void 0!==e._closeRequest&&(h(void 0===e._inFlightCloseRequest),e._closeRequest._reject(e._storedError),e._closeRequest=void 0);var t=e._writer;void 0!==t&&(he(t,e._storedError),t._closedPromise.catch((function(){})))}function N(e,t){h("writable"===e._state),h(!1===I(e));var r=e._writer;void 0!==r&&t!==e._backpressure&&(!0===t?be(r):(h(!1===t),ye(r))),e._backpressure=t}e.exports={AcquireWritableStreamDefaultWriter:_,IsWritableStream:y,IsWritableStreamLocked:A,WritableStream:b,WritableStreamAbort:S,WritableStreamDefaultControllerError:ie,WritableStreamDefaultWriterCloseWithErrorPropagation:W,WritableStreamDefaultWriterRelease:H,WritableStreamDefaultWriterWrite:X,WritableStreamCloseQueuedOrInFlight:I};var M=function(){function e(t){if(i(this,e),!1===y(t))throw new TypeError("WritableStreamDefaultWriter can only be constructed with a WritableStream instance");if(!0===A(t))throw new TypeError("This stream has already been locked for exclusive writing by another writer");this._ownerWritableStream=t,t._writer=this;var r=t._state;if("writable"===r)!1===I(t)&&!0===t._backpressure?pe(this):ge(this),le(this);else if("erroring"===r)me(this,t._storedError),this._readyPromise.catch((function(){})),le(this);else if("closed"===r)ge(this),ue(this);else{h("errored"===r,"state must be errored");var n=t._storedError;me(this,n),this._readyPromise.catch((function(){})),ce(this,n),this._closedPromise.catch((function(){}))}}return n(e,[{key:"abort",value:function(e){return!1===j(this)?Promise.reject(se("abort")):void 0===this._ownerWritableStream?Promise.reject(oe("abort")):q(this,e)}},{key:"close",value:function(){if(!1===j(this))return Promise.reject(se("close"));var e=this._ownerWritableStream;return void 0===e?Promise.reject(oe("close")):!0===I(e)?Promise.reject(new TypeError("cannot close an already-closing stream")):U(this)}},{key:"releaseLock",value:function(){if(!1===j(this))throw se("releaseLock");var e=this._ownerWritableStream;void 0!==e&&(h(void 0!==e._writer),H(this))}},{key:"write",value:function(e){return!1===j(this)?Promise.reject(se("write")):void 0===this._ownerWritableStream?Promise.reject(oe("write to")):X(this,e)}},{key:"closed",get:function(){return!1===j(this)?Promise.reject(se("closed")):this._closedPromise}},{key:"desiredSize",get:function(){if(!1===j(this))throw se("desiredSize");if(void 0===this._ownerWritableStream)throw oe("desiredSize");return G(this)}},{key:"ready",get:function(){return!1===j(this)?Promise.reject(se("ready")):this._readyPromise}}]),e}();function j(e){return!!c(e)&&!!Object.prototype.hasOwnProperty.call(e,"_ownerWritableStream")}function q(e,t){var r=e._ownerWritableStream;return h(void 0!==r),S(r,t)}function U(e){var t=e._ownerWritableStream;h(void 0!==t);var r=t._state;if("closed"===r||"errored"===r)return Promise.reject(new TypeError("The stream (in "+r+" state) is not in the writable state and cannot be closed"));h("writable"===r||"erroring"===r),h(!1===I(t));var n=new Promise((function(e,r){var n={_resolve:e,_reject:r};t._closeRequest=n}));return!0===t._backpressure&&"writable"===r&&ye(e),V(t._writableStreamController),n}function W(e){var t=e._ownerWritableStream;h(void 0!==t);var r=t._state;return!0===I(t)||"closed"===r?Promise.resolve():"errored"===r?Promise.reject(t._storedError):(h("writable"===r||"erroring"===r),U(e))}function B(e,t){"pending"===e._closedPromiseState?he(e,t):de(e,t),e._closedPromise.catch((function(){}))}function z(e,t){"pending"===e._readyPromiseState?ve(e,t):_e(e,t),e._readyPromise.catch((function(){}))}function G(e){var t=e._ownerWritableStream,r=t._state;return"errored"===r||"erroring"===r?null:"closed"===r?0:Q(t._writableStreamController)}function H(e){var t=e._ownerWritableStream;h(void 0!==t),h(t._writer===e);var r=new TypeError("Writer was released and can no longer be used to monitor the stream's closedness");z(e,r),B(e,r),t._writer=void 0,e._ownerWritableStream=void 0}function X(e,t){var r=e._ownerWritableStream;h(void 0!==r);var n=r._writableStreamController,i=J(n,t);if(r!==e._ownerWritableStream)return Promise.reject(oe("write to"));var a=r._state;if("errored"===a)return Promise.reject(r._storedError);if(!0===I(r)||"closed"===a)return Promise.reject(new TypeError("The stream is closing or closed and cannot be written to"));if("erroring"===a)return Promise.reject(r._storedError);h("writable"===a);var s=w(r);return K(n,t,i),s}var Y=function(){function e(t,r,n,a){if(i(this,e),!1===y(t))throw new TypeError("WritableStreamDefaultController can only be constructed with a WritableStream instance");if(void 0!==t._writableStreamController)throw new TypeError("WritableStreamDefaultController instances can only be created by the WritableStream constructor");this._controlledWritableStream=t,this._underlyingSink=r,this._queue=void 0,this._queueTotalSize=void 0,v(this),this._started=!1;var s=l(n,a);this._strategySize=s.size,this._strategyHWM=s.highWaterMark;var o=ne(this);N(t,o)}return n(e,[{key:"error",value:function(e){if(!1===Z(this))throw new TypeError("WritableStreamDefaultController.prototype.error can only be used on a WritableStreamDefaultController");var t=this._controlledWritableStream._state;"writable"===t&&ie(this,e)}},{key:"__abortSteps",value:function(e){return o(this._underlyingSink,"abort",[e])}},{key:"__errorSteps",value:function(){v(this)}},{key:"__startSteps",value:function(){var e=this,t=s(this._underlyingSink,"start",[this]),r=this._controlledWritableStream;Promise.resolve(t).then((function(){h("writable"===r._state||"erroring"===r._state),e._started=!0,$(e)}),(function(t){h("writable"===r._state||"erroring"===r._state),e._started=!0,P(r,t)})).catch(d)}}]),e}();function V(e){m(e,"close",0),$(e)}function J(e,t){var r=e._strategySize;if(void 0===r)return 1;try{return r(t)}catch(n){return ee(e,n),1}}function Q(e){return e._strategyHWM-e._queueTotalSize}function K(e,t,r){var n={chunk:t};try{m(e,n,r)}catch(s){return void ee(e,s)}var i=e._controlledWritableStream;if(!1===I(i)&&"writable"===i._state){var a=ne(e);N(i,a)}$(e)}function Z(e){return!!c(e)&&!!Object.prototype.hasOwnProperty.call(e,"_underlyingSink")}function $(e){var t=e._controlledWritableStream;if(!1!==e._started&&void 0===t._inFlightWriteRequest){var r=t._state;if("closed"!==r&&"errored"!==r)if("erroring"!==r){if(0!==e._queue.length){var n=g(e);"close"===n?te(e):re(e,n.chunk)}}else R(t)}}function ee(e,t){"writable"===e._controlledWritableStream._state&&ie(e,t)}function te(e){var t=e._controlledWritableStream;D(t),p(e),h(0===e._queue.length,"queue must be empty once the final write record is dequeued");var r=o(e._underlyingSink,"close",[]);r.then((function(){T(t)}),(function(e){E(t,e)})).catch(d)}function re(e,t){var r=e._controlledWritableStream;O(r);var n=o(e._underlyingSink,"write",[t,e]);n.then((function(){k(r);var t=r._state;if(h("writable"===t||"erroring"===t),p(e),!1===I(r)&&"writable"===t){var n=ne(e);N(r,n)}$(e)}),(function(e){x(r,e)})).catch(d)}function ne(e){var t=Q(e);return t<=0}function ie(e,t){var r=e._controlledWritableStream;h("writable"===r._state),C(r,t)}function ae(e){return new TypeError("WritableStream.prototype."+e+" can only be used on a WritableStream")}function se(e){return new TypeError("WritableStreamDefaultWriter.prototype."+e+" can only be used on a WritableStreamDefaultWriter")}function oe(e){return new TypeError("Cannot "+e+" a stream using a released writer")}function le(e){e._closedPromise=new Promise((function(t,r){e._closedPromise_resolve=t,e._closedPromise_reject=r,e._closedPromiseState="pending"}))}function ce(e,t){e._closedPromise=Promise.reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="rejected"}function ue(e){e._closedPromise=Promise.resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="resolved"}function he(e,t){h(void 0!==e._closedPromise_resolve,"writer._closedPromise_resolve !== undefined"),h(void 0!==e._closedPromise_reject,"writer._closedPromise_reject !== undefined"),h("pending"===e._closedPromiseState,"writer._closedPromiseState is pending"),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="rejected"}function de(e,t){h(void 0===e._closedPromise_resolve,"writer._closedPromise_resolve === undefined"),h(void 0===e._closedPromise_reject,"writer._closedPromise_reject === undefined"),h("pending"!==e._closedPromiseState,"writer._closedPromiseState is not pending"),e._closedPromise=Promise.reject(t),e._closedPromiseState="rejected"}function fe(e){h(void 0!==e._closedPromise_resolve,"writer._closedPromise_resolve !== undefined"),h(void 0!==e._closedPromise_reject,"writer._closedPromise_reject !== undefined"),h("pending"===e._closedPromiseState,"writer._closedPromiseState is pending"),e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="resolved"}function pe(e){e._readyPromise=new Promise((function(t,r){e._readyPromise_resolve=t,e._readyPromise_reject=r})),e._readyPromiseState="pending"}function me(e,t){e._readyPromise=Promise.reject(t),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="rejected"}function ge(e){e._readyPromise=Promise.resolve(void 0),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="fulfilled"}function ve(e,t){h(void 0!==e._readyPromise_resolve,"writer._readyPromise_resolve !== undefined"),h(void 0!==e._readyPromise_reject,"writer._readyPromise_reject !== undefined"),e._readyPromise_reject(t),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="rejected"}function be(e){h(void 0===e._readyPromise_resolve,"writer._readyPromise_resolve === undefined"),h(void 0===e._readyPromise_reject,"writer._readyPromise_reject === undefined"),e._readyPromise=new Promise((function(t,r){e._readyPromise_resolve=t,e._readyPromise_reject=r})),e._readyPromiseState="pending"}function _e(e,t){h(void 0===e._readyPromise_resolve,"writer._readyPromise_resolve === undefined"),h(void 0===e._readyPromise_reject,"writer._readyPromise_reject === undefined"),e._readyPromise=Promise.reject(t),e._readyPromiseState="rejected"}function ye(e){h(void 0!==e._readyPromise_resolve,"writer._readyPromise_resolve !== undefined"),h(void 0!==e._readyPromise_reject,"writer._readyPromise_reject !== undefined"),e._readyPromise_resolve(void 0),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="fulfilled"}},function(e,t,r){var n=r(0),i=n.IsFiniteNonNegativeNumber,a=r(1),s=a.assert;t.DequeueValue=function(e){s("_queue"in e&&"_queueTotalSize"in e,"Spec-level failure: DequeueValue should only be used on containers with [[queue]] and [[queueTotalSize]]."),s(e._queue.length>0,"Spec-level failure: should never dequeue from an empty queue.");var t=e._queue.shift();return e._queueTotalSize-=t.size,e._queueTotalSize<0&&(e._queueTotalSize=0),t.value},t.EnqueueValueWithSize=function(e,t,r){if(s("_queue"in e&&"_queueTotalSize"in e,"Spec-level failure: EnqueueValueWithSize should only be used on containers with [[queue]] and [[queueTotalSize]]."),r=Number(r),!i(r))throw new RangeError("Size must be a finite, non-NaN, non-negative number.");e._queue.push({value:t,size:r}),e._queueTotalSize+=r},t.PeekQueueValue=function(e){s("_queue"in e&&"_queueTotalSize"in e,"Spec-level failure: PeekQueueValue should only be used on containers with [[queue]] and [[queueTotalSize]]."),s(e._queue.length>0,"Spec-level failure: should never peek at an empty queue.");var t=e._queue[0];return t.value},t.ResetQueue=function(e){s("_queue"in e&&"_queueTotalSize"in e,"Spec-level failure: ResetQueue should only be used on containers with [[queue]] and [[queueTotalSize]]."),e._queue=[],e._queueTotalSize=0}},function(e,t,r){var n=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}();function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var a=r(0),s=a.ArrayBufferCopy,o=a.CreateIterResultObject,l=a.IsFiniteNonNegativeNumber,c=a.InvokeOrNoop,u=a.PromiseInvokeOrNoop,h=a.TransferArrayBuffer,d=a.ValidateAndNormalizeQueuingStrategy,f=a.ValidateAndNormalizeHighWaterMark,p=r(0),m=p.createArrayFromList,g=p.createDataProperty,v=p.typeIsObject,b=r(1),_=b.assert,y=b.rethrowAssertionErrorRejection,A=r(3),S=A.DequeueValue,w=A.EnqueueValueWithSize,P=A.ResetQueue,C=r(2),R=C.AcquireWritableStreamDefaultWriter,k=C.IsWritableStream,x=C.IsWritableStreamLocked,T=C.WritableStreamAbort,E=C.WritableStreamDefaultWriterCloseWithErrorPropagation,I=C.WritableStreamDefaultWriterRelease,L=C.WritableStreamDefaultWriterWrite,D=C.WritableStreamCloseQueuedOrInFlight,O=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.size,a=r.highWaterMark;i(this,e),this._state="readable",this._reader=void 0,this._storedError=void 0,this._disturbed=!1,this._readableStreamController=void 0;var s=t.type,o=String(s);if("bytes"===o)void 0===a&&(a=0),this._readableStreamController=new ye(this,t,a);else{if(void 0!==s)throw new RangeError("Invalid type is specified");void 0===a&&(a=1),this._readableStreamController=new ue(this,t,n,a)}}return n(e,[{key:"cancel",value:function(e){return!1===M(this)?Promise.reject(He("cancel")):!0===q(this)?Promise.reject(new TypeError("Cannot cancel a stream that already has a reader")):X(this,e)}},{key:"getReader",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.mode;if(!1===M(this))throw He("getReader");if(void 0===t)return N(this);if(t=String(t),"byob"===t)return F(this);throw new RangeError("Invalid mode is specified")}},{key:"pipeThrough",value:function(e,t){var r=e.writable,n=e.readable,i=this.pipeTo(r,t);return it(i),n}},{key:"pipeTo",value:function(e){var t=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.preventClose,i=r.preventAbort,a=r.preventCancel;if(!1===M(this))return Promise.reject(He("pipeTo"));if(!1===k(e))return Promise.reject(new TypeError("ReadableStream.prototype.pipeTo's first argument must be a WritableStream"));if(n=Boolean(n),i=Boolean(i),a=Boolean(a),!0===q(this))return Promise.reject(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream"));if(!0===x(e))return Promise.reject(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream"));var s=N(this),o=R(e),l=!1,c=Promise.resolve();return new Promise((function(r,u){function h(){return c=Promise.resolve(),!0===l?Promise.resolve():o._readyPromise.then((function(){return ce(s).then((function(e){var t=e.value,r=e.done;!0!==r&&(c=L(o,t).catch((function(){})))}))})).then(h)}if(p(t,s._closedPromise,(function(t){!1===i?g((function(){return T(e,t)}),!0,t):v(!0,t)})),p(e,o._closedPromise,(function(e){!1===a?g((function(){return X(t,e)}),!0,e):v(!0,e)})),m(t,s._closedPromise,(function(){!1===n?g((function(){return E(o)})):v()})),!0===D(e)||"closed"===e._state){var d=new TypeError("the destination writable stream closed before all data could be piped to it");!1===a?g((function(){return X(t,d)}),!0,d):v(!0,d)}function f(){var e=c;return c.then((function(){return e!==c?f():void 0}))}function p(e,t,r){"errored"===e._state?r(e._storedError):t.catch(r).catch(y)}function m(e,t,r){"closed"===e._state?r():t.then(r).catch(y)}function g(t,r,n){function i(){t().then((function(){return b(r,n)}),(function(e){return b(!0,e)})).catch(y)}!0!==l&&(l=!0,"writable"===e._state&&!1===D(e)?f().then(i):i())}function v(t,r){!0!==l&&(l=!0,"writable"===e._state&&!1===D(e)?f().then((function(){return b(t,r)})).catch(y):b(t,r))}function b(e,t){I(o),oe(s),e?u(t):r(void 0)}h().catch((function(e){c=Promise.resolve(),y(e)}))}))}},{key:"tee",value:function(){if(!1===M(this))throw He("tee");var e=U(this,!1);return m(e)}},{key:"locked",get:function(){if(!1===M(this))throw He("locked");return q(this)}}]),e}();function F(e){return new re(e)}function N(e){return new te(e)}function M(e){return!!v(e)&&!!Object.prototype.hasOwnProperty.call(e,"_readableStreamController")}function j(e){return _(!0===M(e),"IsReadableStreamDisturbed should only be used on known readable streams"),e._disturbed}function q(e){return _(!0===M(e),"IsReadableStreamLocked should only be used on known readable streams"),void 0!==e._reader}function U(e,t){_(!0===M(e)),_("boolean"===typeof t);var r=N(e),n={closedOrErrored:!1,canceled1:!1,canceled2:!1,reason1:void 0,reason2:void 0};n.promise=new Promise((function(e){n._resolve=e}));var i=W();i._reader=r,i._teeState=n,i._cloneForBranch2=t;var a=B();a._stream=e,a._teeState=n;var s=z();s._stream=e,s._teeState=n;var o=Object.create(Object.prototype);g(o,"pull",i),g(o,"cancel",a);var l=new O(o),c=Object.create(Object.prototype);g(c,"pull",i),g(c,"cancel",s);var u=new O(c);return i._branch1=l._readableStreamController,i._branch2=u._readableStreamController,r._closedPromise.catch((function(e){!0!==n.closedOrErrored&&(ge(i._branch1,e),ge(i._branch2,e),n.closedOrErrored=!0)})),[l,u]}function W(){function e(){var t=e._reader,r=e._branch1,n=e._branch2,i=e._teeState;return ce(t).then((function(e){_(v(e));var t=e.value,a=e.done;if(_("boolean"===typeof a),!0===a&&!1===i.closedOrErrored&&(!1===i.canceled1&&pe(r),!1===i.canceled2&&pe(n),i.closedOrErrored=!0),!0!==i.closedOrErrored){var s=t,o=t;!1===i.canceled1&&me(r,s),!1===i.canceled2&&me(n,o)}}))}return e}function B(){function e(t){var r=e._stream,n=e._teeState;if(n.canceled1=!0,n.reason1=t,!0===n.canceled2){var i=m([n.reason1,n.reason2]),a=X(r,i);n._resolve(a)}return n.promise}return e}function z(){function e(t){var r=e._stream,n=e._teeState;if(n.canceled2=!0,n.reason2=t,!0===n.canceled1){var i=m([n.reason1,n.reason2]),a=X(r,i);n._resolve(a)}return n.promise}return e}function G(e){_(!0===ne(e._reader)),_("readable"===e._state||"closed"===e._state);var t=new Promise((function(t,r){var n={_resolve:t,_reject:r};e._reader._readIntoRequests.push(n)}));return t}function H(e){_(!0===ie(e._reader)),_("readable"===e._state);var t=new Promise((function(t,r){var n={_resolve:t,_reject:r};e._reader._readRequests.push(n)}));return t}function X(e,t){if(e._disturbed=!0,"closed"===e._state)return Promise.resolve(void 0);if("errored"===e._state)return Promise.reject(e._storedError);Y(e);var r=e._readableStreamController.__cancelSteps(t);return r.then((function(){}))}function Y(e){_("readable"===e._state),e._state="closed";var t=e._reader;if(void 0!==t){if(!0===ie(t)){for(var r=0;r<t._readRequests.length;r++){var n=t._readRequests[r]._resolve;n(o(void 0,!0))}t._readRequests=[]}$e(t)}}function V(e,t){_(!0===M(e),"stream must be ReadableStream"),_("readable"===e._state,"state must be readable"),e._state="errored",e._storedError=t;var r=e._reader;if(void 0!==r){if(!0===ie(r)){for(var n=0;n<r._readRequests.length;n++){var i=r._readRequests[n];i._reject(t)}r._readRequests=[]}else{_(ne(r),"reader must be ReadableStreamBYOBReader");for(var a=0;a<r._readIntoRequests.length;a++){var s=r._readIntoRequests[a];s._reject(t)}r._readIntoRequests=[]}Ke(r,t),r._closedPromise.catch((function(){}))}}function J(e,t,r){var n=e._reader;_(n._readIntoRequests.length>0);var i=n._readIntoRequests.shift();i._resolve(o(t,r))}function Q(e,t,r){var n=e._reader;_(n._readRequests.length>0);var i=n._readRequests.shift();i._resolve(o(t,r))}function K(e){return e._reader._readIntoRequests.length}function Z(e){return e._reader._readRequests.length}function $(e){var t=e._reader;return void 0!==t&&!1!==ne(t)}function ee(e){var t=e._reader;return void 0!==t&&!1!==ie(t)}e.exports={ReadableStream:O,IsReadableStreamDisturbed:j,ReadableStreamDefaultControllerClose:pe,ReadableStreamDefaultControllerEnqueue:me,ReadableStreamDefaultControllerError:ge,ReadableStreamDefaultControllerGetDesiredSize:be};var te=function(){function e(t){if(i(this,e),!1===M(t))throw new TypeError("ReadableStreamDefaultReader can only be constructed with a ReadableStream instance");if(!0===q(t))throw new TypeError("This stream has already been locked for exclusive reading by another reader");ae(this,t),this._readRequests=[]}return n(e,[{key:"cancel",value:function(e){return!1===ie(this)?Promise.reject(Ye("cancel")):void 0===this._ownerReadableStream?Promise.reject(Xe("cancel")):se(this,e)}},{key:"read",value:function(){return!1===ie(this)?Promise.reject(Ye("read")):void 0===this._ownerReadableStream?Promise.reject(Xe("read from")):ce(this)}},{key:"releaseLock",value:function(){if(!1===ie(this))throw Ye("releaseLock");if(void 0!==this._ownerReadableStream){if(this._readRequests.length>0)throw new TypeError("Tried to release a reader lock when that reader has pending read() calls un-settled");oe(this)}}},{key:"closed",get:function(){return!1===ie(this)?Promise.reject(Ye("closed")):this._closedPromise}}]),e}(),re=function(){function e(t){if(i(this,e),!M(t))throw new TypeError("ReadableStreamBYOBReader can only be constructed with a ReadableStream instance given a byte source");if(!1===Ae(t._readableStreamController))throw new TypeError("Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte source");if(q(t))throw new TypeError("This stream has already been locked for exclusive reading by another reader");ae(this,t),this._readIntoRequests=[]}return n(e,[{key:"cancel",value:function(e){return ne(this)?void 0===this._ownerReadableStream?Promise.reject(Xe("cancel")):se(this,e):Promise.reject(et("cancel"))}},{key:"read",value:function(e){return ne(this)?void 0===this._ownerReadableStream?Promise.reject(Xe("read from")):ArrayBuffer.isView(e)?0===e.byteLength?Promise.reject(new TypeError("view must have non-zero byteLength")):le(this,e):Promise.reject(new TypeError("view must be an array buffer view")):Promise.reject(et("read"))}},{key:"releaseLock",value:function(){if(!ne(this))throw et("releaseLock");if(void 0!==this._ownerReadableStream){if(this._readIntoRequests.length>0)throw new TypeError("Tried to release a reader lock when that reader has pending read() calls un-settled");oe(this)}}},{key:"closed",get:function(){return ne(this)?this._closedPromise:Promise.reject(et("closed"))}}]),e}();function ne(e){return!!v(e)&&!!Object.prototype.hasOwnProperty.call(e,"_readIntoRequests")}function ie(e){return!!v(e)&&!!Object.prototype.hasOwnProperty.call(e,"_readRequests")}function ae(e,t){e._ownerReadableStream=t,t._reader=e,"readable"===t._state?Ve(e):"closed"===t._state?Qe(e):(_("errored"===t._state,"state must be errored"),Je(e,t._storedError),e._closedPromise.catch((function(){})))}function se(e,t){var r=e._ownerReadableStream;return _(void 0!==r),X(r,t)}function oe(e){_(void 0!==e._ownerReadableStream),_(e._ownerReadableStream._reader===e),"readable"===e._ownerReadableStream._state?Ke(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")):Ze(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")),e._closedPromise.catch((function(){})),e._ownerReadableStream._reader=void 0,e._ownerReadableStream=void 0}function le(e,t){var r=e._ownerReadableStream;return _(void 0!==r),r._disturbed=!0,"errored"===r._state?Promise.reject(r._storedError):De(r._readableStreamController,t)}function ce(e){var t=e._ownerReadableStream;return _(void 0!==t),t._disturbed=!0,"closed"===t._state?Promise.resolve(o(void 0,!0)):"errored"===t._state?Promise.reject(t._storedError):(_("readable"===t._state),t._readableStreamController.__pullSteps())}var ue=function(){function e(t,r,n,a){if(i(this,e),!1===M(t))throw new TypeError("ReadableStreamDefaultController can only be constructed with a ReadableStream instance");if(void 0!==t._readableStreamController)throw new TypeError("ReadableStreamDefaultController instances can only be created by the ReadableStream constructor");this._controlledReadableStream=t,this._underlyingSource=r,this._queue=void 0,this._queueTotalSize=void 0,P(this),this._started=!1,this._closeRequested=!1,this._pullAgain=!1,this._pulling=!1;var s=d(n,a);this._strategySize=s.size,this._strategyHWM=s.highWaterMark;var o=this,l=c(r,"start",[this]);Promise.resolve(l).then((function(){o._started=!0,_(!1===o._pulling),_(!1===o._pullAgain),de(o)}),(function(e){ve(o,e)})).catch(y)}return n(e,[{key:"close",value:function(){if(!1===he(this))throw tt("close");if(!0===this._closeRequested)throw new TypeError("The stream has already been closed; do not close it again!");var e=this._controlledReadableStream._state;if("readable"!==e)throw new TypeError("The stream (in "+e+" state) is not in the readable state and cannot be closed");pe(this)}},{key:"enqueue",value:function(e){if(!1===he(this))throw tt("enqueue");if(!0===this._closeRequested)throw new TypeError("stream is closed or draining");var t=this._controlledReadableStream._state;if("readable"!==t)throw new TypeError("The stream (in "+t+" state) is not in the readable state and cannot be enqueued to");return me(this,e)}},{key:"error",value:function(e){if(!1===he(this))throw tt("error");var t=this._controlledReadableStream;if("readable"!==t._state)throw new TypeError("The stream is "+t._state+" and so cannot be errored");ge(this,e)}},{key:"__cancelSteps",value:function(e){return P(this),u(this._underlyingSource,"cancel",[e])}},{key:"__pullSteps",value:function(){var e=this._controlledReadableStream;if(this._queue.length>0){var t=S(this);return!0===this._closeRequested&&0===this._queue.length?Y(e):de(this),Promise.resolve(o(t,!1))}var r=H(e);return de(this),r}},{key:"desiredSize",get:function(){if(!1===he(this))throw tt("desiredSize");return be(this)}}]),e}();function he(e){return!!v(e)&&!!Object.prototype.hasOwnProperty.call(e,"_underlyingSource")}function de(e){var t=fe(e);if(!1!==t)if(!0!==e._pulling){_(!1===e._pullAgain),e._pulling=!0;var r=u(e._underlyingSource,"pull",[e]);r.then((function(){if(e._pulling=!1,!0===e._pullAgain)return e._pullAgain=!1,de(e)}),(function(t){ve(e,t)})).catch(y)}else e._pullAgain=!0}function fe(e){var t=e._controlledReadableStream;if("closed"===t._state||"errored"===t._state)return!1;if(!0===e._closeRequested)return!1;if(!1===e._started)return!1;if(!0===q(t)&&Z(t)>0)return!0;var r=be(e);return r>0}function pe(e){var t=e._controlledReadableStream;_(!1===e._closeRequested),_("readable"===t._state),e._closeRequested=!0,0===e._queue.length&&Y(t)}function me(e,t){var r=e._controlledReadableStream;if(_(!1===e._closeRequested),_("readable"===r._state),!0===q(r)&&Z(r)>0)Q(r,t,!1);else{var n=1;if(void 0!==e._strategySize){var i=e._strategySize;try{n=i(t)}catch(a){throw ve(e,a),a}}try{w(e,t,n)}catch(s){throw ve(e,s),s}}de(e)}function ge(e,t){var r=e._controlledReadableStream;_("readable"===r._state),P(e),V(r,t)}function ve(e,t){"readable"===e._controlledReadableStream._state&&ge(e,t)}function be(e){var t=e._controlledReadableStream,r=t._state;return"errored"===r?null:"closed"===r?0:e._strategyHWM-e._queueTotalSize}var _e=function(){function e(t,r){i(this,e),this._associatedReadableByteStreamController=t,this._view=r}return n(e,[{key:"respond",value:function(e){if(!1===Se(this))throw rt("respond");if(void 0===this._associatedReadableByteStreamController)throw new TypeError("This BYOB request has been invalidated");ze(this._associatedReadableByteStreamController,e)}},{key:"respondWithNewView",value:function(e){if(!1===Se(this))throw rt("respond");if(void 0===this._associatedReadableByteStreamController)throw new TypeError("This BYOB request has been invalidated");if(!ArrayBuffer.isView(e))throw new TypeError("You can only respond with array buffer views");Ge(this._associatedReadableByteStreamController,e)}},{key:"view",get:function(){return this._view}}]),e}(),ye=function(){function e(t,r,n){if(i(this,e),!1===M(t))throw new TypeError("ReadableByteStreamController can only be constructed with a ReadableStream instance given a byte source");if(void 0!==t._readableStreamController)throw new TypeError("ReadableByteStreamController instances can only be created by the ReadableStream constructor given a byte source");this._controlledReadableStream=t,this._underlyingByteSource=r,this._pullAgain=!1,this._pulling=!1,Pe(this),this._queue=this._queueTotalSize=void 0,P(this),this._closeRequested=!1,this._started=!1,this._strategyHWM=f(n);var a=r.autoAllocateChunkSize;if(void 0!==a&&(!1===Number.isInteger(a)||a<=0))throw new RangeError("autoAllocateChunkSize must be a positive integer");this._autoAllocateChunkSize=a,this._pendingPullIntos=[];var s=this,o=c(r,"start",[this]);Promise.resolve(o).then((function(){s._started=!0,_(!1===s._pulling),_(!1===s._pullAgain),we(s)}),(function(e){"readable"===t._state&&We(s,e)})).catch(y)}return n(e,[{key:"close",value:function(){if(!1===Ae(this))throw nt("close");if(!0===this._closeRequested)throw new TypeError("The stream has already been closed; do not close it again!");var e=this._controlledReadableStream._state;if("readable"!==e)throw new TypeError("The stream (in "+e+" state) is not in the readable state and cannot be closed");qe(this)}},{key:"enqueue",value:function(e){if(!1===Ae(this))throw nt("enqueue");if(!0===this._closeRequested)throw new TypeError("stream is closed or draining");var t=this._controlledReadableStream._state;if("readable"!==t)throw new TypeError("The stream (in "+t+" state) is not in the readable state and cannot be enqueued to");if(!ArrayBuffer.isView(e))throw new TypeError("You can only enqueue array buffer views when using a ReadableByteStreamController");Ue(this,e)}},{key:"error",value:function(e){if(!1===Ae(this))throw nt("error");var t=this._controlledReadableStream;if("readable"!==t._state)throw new TypeError("The stream is "+t._state+" and so cannot be errored");We(this,e)}},{key:"__cancelSteps",value:function(e){if(this._pendingPullIntos.length>0){var t=this._pendingPullIntos[0];t.bytesFilled=0}return P(this),u(this._underlyingByteSource,"cancel",[e])}},{key:"__pullSteps",value:function(){var e=this._controlledReadableStream;if(_(!0===ee(e)),this._queueTotalSize>0){_(0===Z(e));var t=this._queue.shift();this._queueTotalSize-=t.byteLength,Ee(this);var r=void 0;try{r=new Uint8Array(t.buffer,t.byteOffset,t.byteLength)}catch(l){return Promise.reject(l)}return Promise.resolve(o(r,!1))}var n=this._autoAllocateChunkSize;if(void 0!==n){var i=void 0;try{i=new ArrayBuffer(n)}catch(c){return Promise.reject(c)}var a={buffer:i,byteOffset:0,byteLength:n,bytesFilled:0,elementSize:1,ctor:Uint8Array,readerType:"default"};this._pendingPullIntos.push(a)}var s=H(e);return we(this),s}},{key:"byobRequest",get:function(){if(!1===Ae(this))throw nt("byobRequest");if(void 0===this._byobRequest&&this._pendingPullIntos.length>0){var e=this._pendingPullIntos[0],t=new Uint8Array(e.buffer,e.byteOffset+e.bytesFilled,e.byteLength-e.bytesFilled);this._byobRequest=new _e(this,t)}return this._byobRequest}},{key:"desiredSize",get:function(){if(!1===Ae(this))throw nt("desiredSize");return Be(this)}}]),e}();function Ae(e){return!!v(e)&&!!Object.prototype.hasOwnProperty.call(e,"_underlyingByteSource")}function Se(e){return!!v(e)&&!!Object.prototype.hasOwnProperty.call(e,"_associatedReadableByteStreamController")}function we(e){var t=je(e);if(!1!==t)if(!0!==e._pulling){_(!1===e._pullAgain),e._pulling=!0;var r=u(e._underlyingByteSource,"pull",[e]);r.then((function(){e._pulling=!1,!0===e._pullAgain&&(e._pullAgain=!1,we(e))}),(function(t){"readable"===e._controlledReadableStream._state&&We(e,t)})).catch(y)}else e._pullAgain=!0}function Pe(e){Ie(e),e._pendingPullIntos=[]}function Ce(e,t){_("errored"!==e._state,"state must not be errored");var r=!1;"closed"===e._state&&(_(0===t.bytesFilled),r=!0);var n=Re(t);"default"===t.readerType?Q(e,n,r):(_("byob"===t.readerType),J(e,n,r))}function Re(e){var t=e.bytesFilled,r=e.elementSize;return _(t<=e.byteLength),_(t%r===0),new e.ctor(e.buffer,e.byteOffset,t/r)}function ke(e,t,r,n){e._queue.push({buffer:t,byteOffset:r,byteLength:n}),e._queueTotalSize+=n}function xe(e,t){var r=t.elementSize,n=t.bytesFilled-t.bytesFilled%r,i=Math.min(e._queueTotalSize,t.byteLength-t.bytesFilled),a=t.bytesFilled+i,o=a-a%r,l=i,c=!1;o>n&&(l=o-t.bytesFilled,c=!0);var u=e._queue;while(l>0){var h=u[0],d=Math.min(l,h.byteLength),f=t.byteOffset+t.bytesFilled;s(t.buffer,f,h.buffer,h.byteOffset,d),h.byteLength===d?u.shift():(h.byteOffset+=d,h.byteLength-=d),e._queueTotalSize-=d,Te(e,d,t),l-=d}return!1===c&&(_(0===e._queueTotalSize,"queue must be empty"),_(t.bytesFilled>0),_(t.bytesFilled<t.elementSize)),c}function Te(e,t,r){_(0===e._pendingPullIntos.length||e._pendingPullIntos[0]===r),Ie(e),r.bytesFilled+=t}function Ee(e){_("readable"===e._controlledReadableStream._state),0===e._queueTotalSize&&!0===e._closeRequested?Y(e._controlledReadableStream):we(e)}function Ie(e){void 0!==e._byobRequest&&(e._byobRequest._associatedReadableByteStreamController=void 0,e._byobRequest._view=void 0,e._byobRequest=void 0)}function Le(e){_(!1===e._closeRequested);while(e._pendingPullIntos.length>0){if(0===e._queueTotalSize)return;var t=e._pendingPullIntos[0];!0===xe(e,t)&&(Me(e),Ce(e._controlledReadableStream,t))}}function De(e,t){var r=e._controlledReadableStream,n=1;t.constructor!==DataView&&(n=t.constructor.BYTES_PER_ELEMENT);var i=t.constructor,a={buffer:t.buffer,byteOffset:t.byteOffset,byteLength:t.byteLength,bytesFilled:0,elementSize:n,ctor:i,readerType:"byob"};if(e._pendingPullIntos.length>0)return a.buffer=h(a.buffer),e._pendingPullIntos.push(a),G(r);if("closed"===r._state){var s=new t.constructor(a.buffer,a.byteOffset,0);return Promise.resolve(o(s,!0))}if(e._queueTotalSize>0){if(!0===xe(e,a)){var l=Re(a);return Ee(e),Promise.resolve(o(l,!1))}if(!0===e._closeRequested){var c=new TypeError("Insufficient bytes to fill elements in the given buffer");return We(e,c),Promise.reject(c)}}a.buffer=h(a.buffer),e._pendingPullIntos.push(a);var u=G(r);return we(e),u}function Oe(e,t){t.buffer=h(t.buffer),_(0===t.bytesFilled,"bytesFilled must be 0");var r=e._controlledReadableStream;if(!0===$(r))while(K(r)>0){var n=Me(e);Ce(r,n)}}function Fe(e,t,r){if(r.bytesFilled+t>r.byteLength)throw new RangeError("bytesWritten out of range");if(Te(e,t,r),!(r.bytesFilled<r.elementSize)){Me(e);var n=r.bytesFilled%r.elementSize;if(n>0){var i=r.byteOffset+r.bytesFilled,a=r.buffer.slice(i-n,i);ke(e,a,0,a.byteLength)}r.buffer=h(r.buffer),r.bytesFilled-=n,Ce(e._controlledReadableStream,r),Le(e)}}function Ne(e,t){var r=e._pendingPullIntos[0],n=e._controlledReadableStream;if("closed"===n._state){if(0!==t)throw new TypeError("bytesWritten must be 0 when calling respond() on a closed stream");Oe(e,r)}else _("readable"===n._state),Fe(e,t,r)}function Me(e){var t=e._pendingPullIntos.shift();return Ie(e),t}function je(e){var t=e._controlledReadableStream;return"readable"===t._state&&(!0!==e._closeRequested&&(!1!==e._started&&(!0===ee(t)&&Z(t)>0||(!0===$(t)&&K(t)>0||Be(e)>0))))}function qe(e){var t=e._controlledReadableStream;if(_(!1===e._closeRequested),_("readable"===t._state),e._queueTotalSize>0)e._closeRequested=!0;else{if(e._pendingPullIntos.length>0){var r=e._pendingPullIntos[0];if(r.bytesFilled>0){var n=new TypeError("Insufficient bytes to fill elements in the given buffer");throw We(e,n),n}}Y(t)}}function Ue(e,t){var r=e._controlledReadableStream;_(!1===e._closeRequested),_("readable"===r._state);var n=t.buffer,i=t.byteOffset,a=t.byteLength,s=h(n);if(!0===ee(r))if(0===Z(r))ke(e,s,i,a);else{_(0===e._queue.length);var o=new Uint8Array(s,i,a);Q(r,o,!1)}else!0===$(r)?(ke(e,s,i,a),Le(e)):(_(!1===q(r),"stream must not be locked"),ke(e,s,i,a))}function We(e,t){var r=e._controlledReadableStream;_("readable"===r._state),Pe(e),P(e),V(r,t)}function Be(e){var t=e._controlledReadableStream,r=t._state;return"errored"===r?null:"closed"===r?0:e._strategyHWM-e._queueTotalSize}function ze(e,t){if(t=Number(t),!1===l(t))throw new RangeError("bytesWritten must be a finite");_(e._pendingPullIntos.length>0),Ne(e,t)}function Ge(e,t){_(e._pendingPullIntos.length>0);var r=e._pendingPullIntos[0];if(r.byteOffset+r.bytesFilled!==t.byteOffset)throw new RangeError("The region specified by view does not match byobRequest");if(r.byteLength!==t.byteLength)throw new RangeError("The buffer of view has different capacity than byobRequest");r.buffer=t.buffer,Ne(e,t.byteLength)}function He(e){return new TypeError("ReadableStream.prototype."+e+" can only be used on a ReadableStream")}function Xe(e){return new TypeError("Cannot "+e+" a stream using a released reader")}function Ye(e){return new TypeError("ReadableStreamDefaultReader.prototype."+e+" can only be used on a ReadableStreamDefaultReader")}function Ve(e){e._closedPromise=new Promise((function(t,r){e._closedPromise_resolve=t,e._closedPromise_reject=r}))}function Je(e,t){e._closedPromise=Promise.reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0}function Qe(e){e._closedPromise=Promise.resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0}function Ke(e,t){_(void 0!==e._closedPromise_resolve),_(void 0!==e._closedPromise_reject),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0}function Ze(e,t){_(void 0===e._closedPromise_resolve),_(void 0===e._closedPromise_reject),e._closedPromise=Promise.reject(t)}function $e(e){_(void 0!==e._closedPromise_resolve),_(void 0!==e._closedPromise_reject),e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0}function et(e){return new TypeError("ReadableStreamBYOBReader.prototype."+e+" can only be used on a ReadableStreamBYOBReader")}function tt(e){return new TypeError("ReadableStreamDefaultController.prototype."+e+" can only be used on a ReadableStreamDefaultController")}function rt(e){return new TypeError("ReadableStreamBYOBRequest.prototype."+e+" can only be used on a ReadableStreamBYOBRequest")}function nt(e){return new TypeError("ReadableByteStreamController.prototype."+e+" can only be used on a ReadableByteStreamController")}function it(e){try{Promise.prototype.then.call(e,void 0,(function(){}))}catch(t){}}},function(e,t,r){var n=r(6),i=r(4),a=r(2);t.TransformStream=n.TransformStream,t.ReadableStream=i.ReadableStream,t.IsReadableStreamDisturbed=i.IsReadableStreamDisturbed,t.ReadableStreamDefaultControllerClose=i.ReadableStreamDefaultControllerClose,t.ReadableStreamDefaultControllerEnqueue=i.ReadableStreamDefaultControllerEnqueue,t.ReadableStreamDefaultControllerError=i.ReadableStreamDefaultControllerError,t.ReadableStreamDefaultControllerGetDesiredSize=i.ReadableStreamDefaultControllerGetDesiredSize,t.AcquireWritableStreamDefaultWriter=a.AcquireWritableStreamDefaultWriter,t.IsWritableStream=a.IsWritableStream,t.IsWritableStreamLocked=a.IsWritableStreamLocked,t.WritableStream=a.WritableStream,t.WritableStreamAbort=a.WritableStreamAbort,t.WritableStreamDefaultControllerError=a.WritableStreamDefaultControllerError,t.WritableStreamDefaultWriterCloseWithErrorPropagation=a.WritableStreamDefaultWriterCloseWithErrorPropagation,t.WritableStreamDefaultWriterRelease=a.WritableStreamDefaultWriterRelease,t.WritableStreamDefaultWriterWrite=a.WritableStreamDefaultWriterWrite},function(e,t,r){var n=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}();function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var a=r(1),s=a.assert,o=r(0),l=o.InvokeOrNoop,c=o.PromiseInvokeOrPerformFallback,u=o.PromiseInvokeOrNoop,h=o.typeIsObject,d=r(4),f=d.ReadableStream,p=d.ReadableStreamDefaultControllerClose,m=d.ReadableStreamDefaultControllerEnqueue,g=d.ReadableStreamDefaultControllerError,v=d.ReadableStreamDefaultControllerGetDesiredSize,b=r(2),_=b.WritableStream,y=b.WritableStreamDefaultControllerError;function A(e){if(!0===e._errored)throw new TypeError("TransformStream is already errored");if(!0===e._readableClosed)throw new TypeError("Readable side is already closed");P(e)}function S(e,t){if(!0===e._errored)throw new TypeError("TransformStream is already errored");if(!0===e._readableClosed)throw new TypeError("Readable side is already closed");var r=e._readableController;try{m(r,t)}catch(a){throw e._readableClosed=!0,C(e,a),e._storedError}var n=v(r),i=n<=0;!0===i&&!1===e._backpressure&&x(e,!0)}function w(e,t){if(!0===e._errored)throw new TypeError("TransformStream is already errored");R(e,t)}function P(e){s(!1===e._errored),s(!1===e._readableClosed);try{p(e._readableController)}catch(t){s(!1)}e._readableClosed=!0}function C(e,t){!1===e._errored&&R(e,t)}function R(e,t){s(!1===e._errored),e._errored=!0,e._storedError=t,!1===e._writableDone&&y(e._writableController,t),!1===e._readableClosed&&g(e._readableController,t)}function k(e){return s(void 0!==e._backpressureChangePromise,"_backpressureChangePromise should have been initialized"),!1===e._backpressure?Promise.resolve():(s(!0===e._backpressure,"_backpressure should have been initialized"),e._backpressureChangePromise)}function x(e,t){s(e._backpressure!==t,"TransformStreamSetBackpressure() should be called only when backpressure is changed"),void 0!==e._backpressureChangePromise&&e._backpressureChangePromise_resolve(t),e._backpressureChangePromise=new Promise((function(t){e._backpressureChangePromise_resolve=t})),e._backpressureChangePromise.then((function(e){s(e!==t,"_backpressureChangePromise should be fulfilled only when backpressure is changed")})),e._backpressure=t}function T(e,t){var r=t._controlledTransformStream;return S(r,e),Promise.resolve()}function E(e,t){s(!1===e._errored),s(!1===e._transforming),s(!1===e._backpressure),e._transforming=!0;var r=e._transformer,n=e._transformStreamController,i=c(r,"transform",[t,n],T,[t,n]);return i.then((function(){return e._transforming=!1,k(e)}),(function(t){return C(e,t),Promise.reject(t)}))}function I(e){return!!h(e)&&!!Object.prototype.hasOwnProperty.call(e,"_controlledTransformStream")}function L(e){return!!h(e)&&!!Object.prototype.hasOwnProperty.call(e,"_transformStreamController")}var D=function(){function e(t,r){i(this,e),this._transformStream=t,this._startPromise=r}return n(e,[{key:"start",value:function(e){var t=this._transformStream;return t._writableController=e,this._startPromise.then((function(){return k(t)}))}},{key:"write",value:function(e){var t=this._transformStream;return E(t,e)}},{key:"abort",value:function(){var e=this._transformStream;e._writableDone=!0,R(e,new TypeError("Writable side aborted"))}},{key:"close",value:function(){var e=this._transformStream;s(!1===e._transforming),e._writableDone=!0;var t=u(e._transformer,"flush",[e._transformStreamController]);return t.then((function(){return!0===e._errored?Promise.reject(e._storedError):(!1===e._readableClosed&&P(e),Promise.resolve())})).catch((function(t){return C(e,t),Promise.reject(e._storedError)}))}}]),e}(),O=function(){function e(t,r){i(this,e),this._transformStream=t,this._startPromise=r}return n(e,[{key:"start",value:function(e){var t=this._transformStream;return t._readableController=e,this._startPromise.then((function(){return s(void 0!==t._backpressureChangePromise,"_backpressureChangePromise should have been initialized"),!0===t._backpressure?Promise.resolve():(s(!1===t._backpressure,"_backpressure should have been initialized"),t._backpressureChangePromise)}))}},{key:"pull",value:function(){var e=this._transformStream;return s(!0===e._backpressure,"pull() should be never called while _backpressure is false"),s(void 0!==e._backpressureChangePromise,"_backpressureChangePromise should have been initialized"),x(e,!1),e._backpressureChangePromise}},{key:"cancel",value:function(){var e=this._transformStream;e._readableClosed=!0,R(e,new TypeError("Readable side canceled"))}}]),e}(),F=function(){function e(t){if(i(this,e),!1===L(t))throw new TypeError("TransformStreamDefaultController can only be constructed with a TransformStream instance");if(void 0!==t._transformStreamController)throw new TypeError("TransformStreamDefaultController instances can only be created by the TransformStream constructor");this._controlledTransformStream=t}return n(e,[{key:"enqueue",value:function(e){if(!1===I(this))throw M("enqueue");S(this._controlledTransformStream,e)}},{key:"close",value:function(){if(!1===I(this))throw M("close");A(this._controlledTransformStream)}},{key:"error",value:function(e){if(!1===I(this))throw M("error");w(this._controlledTransformStream,e)}},{key:"desiredSize",get:function(){if(!1===I(this))throw M("desiredSize");var e=this._controlledTransformStream,t=e._readableController;return v(t)}}]),e}(),N=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};i(this,e),this._transformer=t;var r=t.readableStrategy,n=t.writableStrategy;this._transforming=!1,this._errored=!1,this._storedError=void 0,this._writableController=void 0,this._readableController=void 0,this._transformStreamController=void 0,this._writableDone=!1,this._readableClosed=!1,this._backpressure=void 0,this._backpressureChangePromise=void 0,this._backpressureChangePromise_resolve=void 0,this._transformStreamController=new F(this);var a=void 0,o=new Promise((function(e){a=e})),c=new O(this,o);this._readable=new f(c,r);var u=new D(this,o);this._writable=new _(u,n),s(void 0!==this._writableController),s(void 0!==this._readableController);var h=v(this._readableController);x(this,h<=0);var d=this,p=l(t,"start",[d._transformStreamController]);a(p),o.catch((function(e){!1===d._errored&&(d._errored=!0,d._storedError=e)}))}return n(e,[{key:"readable",get:function(){if(!1===L(this))throw j("readable");return this._readable}},{key:"writable",get:function(){if(!1===L(this))throw j("writable");return this._writable}}]),e}();function M(e){return new TypeError("TransformStreamDefaultController.prototype."+e+" can only be used on a TransformStreamDefaultController")}function j(e){return new TypeError("TransformStream.prototype."+e+" can only be used on a TransformStream")}e.exports={TransformStream:N}},function(e,t,r){e.exports=r(5)}]))},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.CanvasGraphics=void 0;var n=r(0),i=r(13),a=r(7),s=16,o=100,l=4096,c=.65,u=!0,h=1e3,d=16,f={get value(){return(0,n.shadow)(f,"value",(0,n.isLittleEndian)())}};function p(e){e.mozCurrentTransform||(e._originalSave=e.save,e._originalRestore=e.restore,e._originalRotate=e.rotate,e._originalScale=e.scale,e._originalTranslate=e.translate,e._originalTransform=e.transform,e._originalSetTransform=e.setTransform,e._transformMatrix=e._transformMatrix||[1,0,0,1,0,0],e._transformStack=[],Object.defineProperty(e,"mozCurrentTransform",{get:function(){return this._transformMatrix}}),Object.defineProperty(e,"mozCurrentTransformInverse",{get:function(){var e=this._transformMatrix,t=e[0],r=e[1],n=e[2],i=e[3],a=e[4],s=e[5],o=t*i-r*n,l=r*n-t*i;return[i/o,r/l,n/l,t/o,(i*a-n*s)/l,(r*a-t*s)/o]}}),e.save=function(){var e=this._transformMatrix;this._transformStack.push(e),this._transformMatrix=e.slice(0,6),this._originalSave()},e.restore=function(){var e=this._transformStack.pop();e&&(this._transformMatrix=e,this._originalRestore())},e.translate=function(e,t){var r=this._transformMatrix;r[4]=r[0]*e+r[2]*t+r[4],r[5]=r[1]*e+r[3]*t+r[5],this._originalTranslate(e,t)},e.scale=function(e,t){var r=this._transformMatrix;r[0]=r[0]*e,r[1]=r[1]*e,r[2]=r[2]*t,r[3]=r[3]*t,this._originalScale(e,t)},e.transform=function(t,r,n,i,a,s){var o=this._transformMatrix;this._transformMatrix=[o[0]*t+o[2]*r,o[1]*t+o[3]*r,o[0]*n+o[2]*i,o[1]*n+o[3]*i,o[0]*a+o[2]*s+o[4],o[1]*a+o[3]*s+o[5]],e._originalTransform(t,r,n,i,a,s)},e.setTransform=function(t,r,n,i,a,s){this._transformMatrix=[t,r,n,i,a,s],e._originalSetTransform(t,r,n,i,a,s)},e.rotate=function(e){var t=Math.cos(e),r=Math.sin(e),n=this._transformMatrix;this._transformMatrix=[n[0]*t+n[2]*r,n[1]*t+n[3]*r,n[0]*-r+n[2]*t,n[1]*-r+n[3]*t,n[4],n[5]],this._originalRotate(e)})}var m=function(){function e(e){this.canvasFactory=e,this.cache=Object.create(null)}return e.prototype={getCanvas:function(e,t,r,n){var i;return void 0!==this.cache[e]?(i=this.cache[e],this.canvasFactory.reset(i,t,r),i.context.setTransform(1,0,0,1,0,0)):(i=this.canvasFactory.create(t,r),this.cache[e]=i),n&&p(i.context),i},clear:function(){for(var e in this.cache){var t=this.cache[e];this.canvasFactory.destroy(t),delete this.cache[e]}}},e}();function g(e){var t,r,n,i,a=1e3,s=e.width,o=e.height,l=s+1,c=new Uint8Array(l*(o+1)),u=new Uint8Array([0,2,4,0,1,0,5,4,8,10,0,8,0,2,1,0]),h=s+7&-8,d=e.data,f=new Uint8Array(h*o),p=0;for(t=0,i=d.length;t<i;t++){var m=128,g=d[t];while(m>0)f[p++]=g&m?0:255,m>>=1}var v=0;for(p=0,0!==f[p]&&(c[0]=1,++v),r=1;r<s;r++)f[p]!==f[p+1]&&(c[r]=f[p]?2:1,++v),p++;for(0!==f[p]&&(c[r]=2,++v),t=1;t<o;t++){p=t*h,n=t*l,f[p-h]!==f[p]&&(c[n]=f[p]?1:8,++v);var b=(f[p]?4:0)+(f[p-h]?8:0);for(r=1;r<s;r++)b=(b>>2)+(f[p+1]?4:0)+(f[p-h+1]?8:0),u[b]&&(c[n+r]=u[b],++v),p++;if(f[p-h]!==f[p]&&(c[n+r]=f[p]?2:4,++v),v>a)return null}for(p=h*(o-1),n=t*l,0!==f[p]&&(c[n]=8,++v),r=1;r<s;r++)f[p]!==f[p+1]&&(c[n+r]=f[p]?4:8,++v),p++;if(0!==f[p]&&(c[n+r]=4,++v),v>a)return null;var _=new Int32Array([0,l,-1,0,-l,0,0,0,1]),y=[];for(t=0;v&&t<=o;t++){var A=t*l,S=A+s;while(A<S&&!c[A])A++;if(A!==S){var w,P=[A%l,t],C=c[A],R=A;do{var k=_[C];do{A+=k}while(!c[A]);w=c[A],5!==w&&10!==w?(C=w,c[A]=0):(C=w&51*C>>4,c[A]&=C>>2|C<<2),P.push(A%l),P.push(A/l|0),--v}while(R!==A);y.push(P),--t}}var x=function(e){e.save(),e.scale(1/s,-1/o),e.translate(0,-o),e.beginPath();for(var t=0,r=y.length;t<r;t++){var n=y[t];e.moveTo(n[0],n[1]);for(var i=2,a=n.length;i<a;i+=2)e.lineTo(n[i],n[i+1])}e.fill(),e.beginPath(),e.restore()};return x}var v=function(){function e(){this.alphaIsShape=!1,this.fontSize=0,this.fontSizeScale=1,this.textMatrix=n.IDENTITY_MATRIX,this.textMatrixScale=1,this.fontMatrix=n.FONT_IDENTITY_MATRIX,this.leading=0,this.x=0,this.y=0,this.lineX=0,this.lineY=0,this.charSpacing=0,this.wordSpacing=0,this.textHScale=1,this.textRenderingMode=n.TextRenderingMode.FILL,this.textRise=0,this.fillColor="#000000",this.strokeColor="#000000",this.patternFill=!1,this.fillAlpha=1,this.strokeAlpha=1,this.lineWidth=1,this.activeSMask=null,this.resumeSMaskCtx=null}return e.prototype={clone:function(){return Object.create(this)},setCurrentPoint:function(e,t){this.x=e,this.y=t}},e}(),b=function(){var e=15,t=10;function r(e,t,r,n,i){this.ctx=e,this.current=new v,this.stateStack=[],this.pendingClip=null,this.pendingEOFill=!1,this.res=null,this.xobjs=null,this.commonObjs=t,this.objs=r,this.canvasFactory=n,this.imageLayer=i,this.groupStack=[],this.processingType3=null,this.baseTransform=null,this.baseTransformStack=[],this.groupLevel=0,this.smaskStack=[],this.smaskCounter=0,this.tempSMask=null,this.cachedCanvases=new m(this.canvasFactory),e&&p(e),this.cachedGetSinglePixelWidth=null}function b(e,t){if("undefined"!==typeof ImageData&&t instanceof ImageData)e.putImageData(t,0,0);else{var r,i,a,s,o,l=t.height,c=t.width,u=l%d,h=(l-u)/d,p=0===u?h:h+1,m=e.createImageData(c,d),g=0,v=t.data,b=m.data;if(t.kind===n.ImageKind.GRAYSCALE_1BPP){var _=v.byteLength,y=new Uint32Array(b.buffer,0,b.byteLength>>2),A=y.length,S=c+7>>3,w=4294967295,P=f.value?4278190080:255;for(i=0;i<p;i++){for(s=i<h?d:u,r=0,a=0;a<s;a++){for(var C=_-g,R=0,k=C>S?c:8*C-7,x=-8&k,T=0,E=0;R<x;R+=8)E=v[g++],y[r++]=128&E?w:P,y[r++]=64&E?w:P,y[r++]=32&E?w:P,y[r++]=16&E?w:P,y[r++]=8&E?w:P,y[r++]=4&E?w:P,y[r++]=2&E?w:P,y[r++]=1&E?w:P;for(;R<k;R++)0===T&&(E=v[g++],T=128),y[r++]=E&T?w:P,T>>=1}while(r<A)y[r++]=0;e.putImageData(m,0,i*d)}}else if(t.kind===n.ImageKind.RGBA_32BPP){for(a=0,o=c*d*4,i=0;i<h;i++)b.set(v.subarray(g,g+o)),g+=o,e.putImageData(m,0,a),a+=d;i<p&&(o=c*u*4,b.set(v.subarray(g,g+o)),e.putImageData(m,0,a))}else{if(t.kind!==n.ImageKind.RGB_24BPP)throw new Error("bad image kind: "+t.kind);for(s=d,o=c*s,i=0;i<p;i++){for(i>=h&&(s=u,o=c*s),r=0,a=o;a--;)b[r++]=v[g++],b[r++]=v[g++],b[r++]=v[g++],b[r++]=255;e.putImageData(m,0,i*d)}}}}function _(e,t){for(var r=t.height,n=t.width,i=r%d,a=(r-i)/d,s=0===i?a:a+1,o=e.createImageData(n,d),l=0,c=t.data,u=o.data,h=0;h<s;h++){for(var f=h<a?d:i,p=3,m=0;m<f;m++)for(var g=0,v=0;v<n;v++){if(!g){var b=c[l++];g=128}u[p]=b&g?0:255,p+=4,g>>=1}e.putImageData(o,0,h*d)}}function y(e,t){for(var r=["strokeStyle","fillStyle","fillRule","globalAlpha","lineWidth","lineCap","lineJoin","miterLimit","globalCompositeOperation","font"],n=0,i=r.length;n<i;n++){var a=r[n];void 0!==e[a]&&(t[a]=e[a])}void 0!==e.setLineDash&&(t.setLineDash(e.getLineDash()),t.lineDashOffset=e.lineDashOffset)}function A(e){e.strokeStyle="#000000",e.fillStyle="#000000",e.fillRule="nonzero",e.globalAlpha=1,e.lineWidth=1,e.lineCap="butt",e.lineJoin="miter",e.miterLimit=10,e.globalCompositeOperation="source-over",e.font="10px sans-serif",void 0!==e.setLineDash&&(e.setLineDash([]),e.lineDashOffset=0)}function S(e,t,r,n){for(var i=e.length,a=3;a<i;a+=4){var s=e[a];if(0===s)e[a-3]=t,e[a-2]=r,e[a-1]=n;else if(s<255){var o=255-s;e[a-3]=e[a-3]*s+t*o>>8,e[a-2]=e[a-2]*s+r*o>>8,e[a-1]=e[a-1]*s+n*o>>8}}}function w(e,t,r){for(var n=e.length,i=1/255,a=3;a<n;a+=4){var s=r?r[e[a]]:e[a];t[a]=t[a]*s*i|0}}function P(e,t,r){for(var n=e.length,i=3;i<n;i+=4){var a=77*e[i-3]+152*e[i-2]+28*e[i-1];t[i]=r?t[i]*r[a>>8]>>8:t[i]*a>>16}}function C(e,t,r,n,i,a,s){var o,l=!!a,c=l?a[0]:0,u=l?a[1]:0,h=l?a[2]:0;o="Luminosity"===i?P:w;for(var d=1048576,f=Math.min(n,Math.ceil(d/r)),p=0;p<n;p+=f){var m=Math.min(f,n-p),g=e.getImageData(0,p,r,m),v=t.getImageData(0,p,r,m);l&&S(g.data,c,u,h),o(g.data,v.data,s),e.putImageData(v,0,p)}}function R(e,t,r){var n=t.canvas,i=t.context;e.setTransform(t.scaleX,0,0,t.scaleY,t.offsetX,t.offsetY);var s=t.backdrop||null;if(!t.transferMap&&a.WebGLUtils.isEnabled){var o=a.WebGLUtils.composeSMask(r.canvas,n,{subtype:t.subtype,backdrop:s});return e.setTransform(1,0,0,1,0,0),void e.drawImage(o,t.offsetX,t.offsetY)}C(i,r,n.width,n.height,t.subtype,s,t.transferMap),e.drawImage(n,0,0)}var k=["butt","round","square"],x=["miter","round","bevel"],T={},E={};for(var I in r.prototype={beginDrawing:function(e){var t=e.transform,r=e.viewport,n=e.transparency,i=e.background,a=void 0===i?null:i,s=this.ctx.canvas.width,o=this.ctx.canvas.height;if(this.ctx.save(),this.ctx.fillStyle=a||"rgb(255, 255, 255)",this.ctx.fillRect(0,0,s,o),this.ctx.restore(),n){var l=this.cachedCanvases.getCanvas("transparent",s,o,!0);this.compositeCtx=this.ctx,this.transparentCanvas=l.canvas,this.ctx=l.context,this.ctx.save(),this.ctx.transform.apply(this.ctx,this.compositeCtx.mozCurrentTransform)}this.ctx.save(),A(this.ctx),t&&this.ctx.transform.apply(this.ctx,t),this.ctx.transform.apply(this.ctx,r.transform),this.baseTransform=this.ctx.mozCurrentTransform.slice(),this.imageLayer&&this.imageLayer.beginLayout()},executeOperatorList:function(r,i,a,s){var o=r.argsArray,l=r.fnArray,c=i||0,u=o.length;if(u===c)return c;var h,d=u-c>t&&"function"===typeof a,f=d?Date.now()+e:0,p=0,m=this.commonObjs,g=this.objs;while(1){if(void 0!==s&&c===s.nextBreakPoint)return s.breakIt(c,a),c;if(h=l[c],h!==n.OPS.dependency)this[h].apply(this,o[c]);else for(var v=o[c],b=0,_=v.length;b<_;b++){var y=v[b],A="g"===y[0]&&"_"===y[1],S=A?m:g;if(!S.isResolved(y))return S.get(y,a),c}if(c++,c===u)return c;if(d&&++p>t){if(Date.now()>f)return a(),c;p=0}}},endDrawing:function(){null!==this.current.activeSMask&&this.endSMaskGroup(),this.ctx.restore(),this.transparentCanvas&&(this.ctx=this.compositeCtx,this.ctx.save(),this.ctx.setTransform(1,0,0,1,0,0),this.ctx.drawImage(this.transparentCanvas,0,0),this.ctx.restore(),this.transparentCanvas=null),this.cachedCanvases.clear(),a.WebGLUtils.clear(),this.imageLayer&&this.imageLayer.endLayout()},setLineWidth:function(e){this.current.lineWidth=e,this.ctx.lineWidth=e},setLineCap:function(e){this.ctx.lineCap=k[e]},setLineJoin:function(e){this.ctx.lineJoin=x[e]},setMiterLimit:function(e){this.ctx.miterLimit=e},setDash:function(e,t){var r=this.ctx;void 0!==r.setLineDash&&(r.setLineDash(e),r.lineDashOffset=t)},setRenderingIntent:function(e){},setFlatness:function(e){},setGState:function(e){for(var t=0,r=e.length;t<r;t++){var n=e[t],i=n[0],a=n[1];switch(i){case"LW":this.setLineWidth(a);break;case"LC":this.setLineCap(a);break;case"LJ":this.setLineJoin(a);break;case"ML":this.setMiterLimit(a);break;case"D":this.setDash(a[0],a[1]);break;case"RI":this.setRenderingIntent(a);break;case"FL":this.setFlatness(a);break;case"Font":this.setFont(a[0],a[1]);break;case"CA":this.current.strokeAlpha=n[1];break;case"ca":this.current.fillAlpha=n[1],this.ctx.globalAlpha=n[1];break;case"BM":this.ctx.globalCompositeOperation=a;break;case"SMask":this.current.activeSMask&&(this.stateStack.length>0&&this.stateStack[this.stateStack.length-1].activeSMask===this.current.activeSMask?this.suspendSMaskGroup():this.endSMaskGroup()),this.current.activeSMask=a?this.tempSMask:null,this.current.activeSMask&&this.beginSMaskGroup(),this.tempSMask=null;break}}},beginSMaskGroup:function(){var e=this.current.activeSMask,t=e.canvas.width,r=e.canvas.height,n="smaskGroupAt"+this.groupLevel,i=this.cachedCanvases.getCanvas(n,t,r,!0),a=this.ctx,s=a.mozCurrentTransform;this.ctx.save();var o=i.context;o.scale(1/e.scaleX,1/e.scaleY),o.translate(-e.offsetX,-e.offsetY),o.transform.apply(o,s),e.startTransformInverse=o.mozCurrentTransformInverse,y(a,o),this.ctx=o,this.setGState([["BM","source-over"],["ca",1],["CA",1]]),this.groupStack.push(a),this.groupLevel++},suspendSMaskGroup:function(){var e=this.ctx;this.groupLevel--,this.ctx=this.groupStack.pop(),R(this.ctx,this.current.activeSMask,e),this.ctx.restore(),this.ctx.save(),y(e,this.ctx),this.current.resumeSMaskCtx=e;var t=n.Util.transform(this.current.activeSMask.startTransformInverse,e.mozCurrentTransform);this.ctx.transform.apply(this.ctx,t),e.save(),e.setTransform(1,0,0,1,0,0),e.clearRect(0,0,e.canvas.width,e.canvas.height),e.restore()},resumeSMaskGroup:function(){var e=this.current.resumeSMaskCtx,t=this.ctx;this.ctx=e,this.groupStack.push(t),this.groupLevel++},endSMaskGroup:function(){var e=this.ctx;this.groupLevel--,this.ctx=this.groupStack.pop(),R(this.ctx,this.current.activeSMask,e),this.ctx.restore(),y(e,this.ctx);var t=n.Util.transform(this.current.activeSMask.startTransformInverse,e.mozCurrentTransform);this.ctx.transform.apply(this.ctx,t)},save:function(){this.ctx.save();var e=this.current;this.stateStack.push(e),this.current=e.clone(),this.current.resumeSMaskCtx=null},restore:function(){this.current.resumeSMaskCtx&&this.resumeSMaskGroup(),null===this.current.activeSMask||0!==this.stateStack.length&&this.stateStack[this.stateStack.length-1].activeSMask===this.current.activeSMask||this.endSMaskGroup(),0!==this.stateStack.length&&(this.current=this.stateStack.pop(),this.ctx.restore(),this.pendingClip=null,this.cachedGetSinglePixelWidth=null)},transform:function(e,t,r,n,i,a){this.ctx.transform(e,t,r,n,i,a),this.cachedGetSinglePixelWidth=null},constructPath:function(e,t){for(var r=this.ctx,i=this.current,a=i.x,s=i.y,o=0,l=0,c=e.length;o<c;o++)switch(0|e[o]){case n.OPS.rectangle:a=t[l++],s=t[l++];var u=t[l++],h=t[l++];0===u&&(u=this.getSinglePixelWidth()),0===h&&(h=this.getSinglePixelWidth());var d=a+u,f=s+h;this.ctx.moveTo(a,s),this.ctx.lineTo(d,s),this.ctx.lineTo(d,f),this.ctx.lineTo(a,f),this.ctx.lineTo(a,s),this.ctx.closePath();break;case n.OPS.moveTo:a=t[l++],s=t[l++],r.moveTo(a,s);break;case n.OPS.lineTo:a=t[l++],s=t[l++],r.lineTo(a,s);break;case n.OPS.curveTo:a=t[l+4],s=t[l+5],r.bezierCurveTo(t[l],t[l+1],t[l+2],t[l+3],a,s),l+=6;break;case n.OPS.curveTo2:r.bezierCurveTo(a,s,t[l],t[l+1],t[l+2],t[l+3]),a=t[l+2],s=t[l+3],l+=4;break;case n.OPS.curveTo3:a=t[l+2],s=t[l+3],r.bezierCurveTo(t[l],t[l+1],a,s,a,s),l+=4;break;case n.OPS.closePath:r.closePath();break}i.setCurrentPoint(a,s)},closePath:function(){this.ctx.closePath()},stroke:function(e){e="undefined"===typeof e||e;var t=this.ctx,r=this.current.strokeColor;t.lineWidth=Math.max(this.getSinglePixelWidth()*c,this.current.lineWidth),t.globalAlpha=this.current.strokeAlpha,r&&r.hasOwnProperty("type")&&"Pattern"===r.type?(t.save(),t.strokeStyle=r.getPattern(t,this),t.stroke(),t.restore()):t.stroke(),e&&this.consumePath(),t.globalAlpha=this.current.fillAlpha},closeStroke:function(){this.closePath(),this.stroke()},fill:function(e){e="undefined"===typeof e||e;var t=this.ctx,r=this.current.fillColor,n=this.current.patternFill,i=!1;n&&(t.save(),this.baseTransform&&t.setTransform.apply(t,this.baseTransform),t.fillStyle=r.getPattern(t,this),i=!0),this.pendingEOFill?(t.fill("evenodd"),this.pendingEOFill=!1):t.fill(),i&&t.restore(),e&&this.consumePath()},eoFill:function(){this.pendingEOFill=!0,this.fill()},fillStroke:function(){this.fill(!1),this.stroke(!1),this.consumePath()},eoFillStroke:function(){this.pendingEOFill=!0,this.fillStroke()},closeFillStroke:function(){this.closePath(),this.fillStroke()},closeEOFillStroke:function(){this.pendingEOFill=!0,this.closePath(),this.fillStroke()},endPath:function(){this.consumePath()},clip:function(){this.pendingClip=T},eoClip:function(){this.pendingClip=E},beginText:function(){this.current.textMatrix=n.IDENTITY_MATRIX,this.current.textMatrixScale=1,this.current.x=this.current.lineX=0,this.current.y=this.current.lineY=0},endText:function(){var e=this.pendingTextPaths,t=this.ctx;if(void 0!==e){t.save(),t.beginPath();for(var r=0;r<e.length;r++){var n=e[r];t.setTransform.apply(t,n.transform),t.translate(n.x,n.y),n.addToPath(t,n.fontSize)}t.restore(),t.clip(),t.beginPath(),delete this.pendingTextPaths}else t.beginPath()},setCharSpacing:function(e){this.current.charSpacing=e},setWordSpacing:function(e){this.current.wordSpacing=e},setHScale:function(e){this.current.textHScale=e/100},setLeading:function(e){this.current.leading=-e},setFont:function(e,t){var r=this.commonObjs.get(e),i=this.current;if(!r)throw new Error("Can't find font for "+e);if(i.fontMatrix=r.fontMatrix?r.fontMatrix:n.FONT_IDENTITY_MATRIX,0!==i.fontMatrix[0]&&0!==i.fontMatrix[3]||(0,n.warn)("Invalid font matrix for font "+e),t<0?(t=-t,i.fontDirection=-1):i.fontDirection=1,this.current.font=r,this.current.fontSize=t,!r.isType3Font){var a=r.loadedName||"sans-serif",l=r.black?"900":r.bold?"bold":"normal",c=r.italic?"italic":"normal",u='"'+a+'", '+r.fallbackName,h=t<s?s:t>o?o:t;this.current.fontSizeScale=t/h;var d=c+" "+l+" "+h+"px "+u;this.ctx.font=d}},setTextRenderingMode:function(e){this.current.textRenderingMode=e},setTextRise:function(e){this.current.textRise=e},moveText:function(e,t){this.current.x=this.current.lineX+=e,this.current.y=this.current.lineY+=t},setLeadingMoveText:function(e,t){this.setLeading(-t),this.moveText(e,t)},setTextMatrix:function(e,t,r,n,i,a){this.current.textMatrix=[e,t,r,n,i,a],this.current.textMatrixScale=Math.sqrt(e*e+t*t),this.current.x=this.current.lineX=0,this.current.y=this.current.lineY=0},nextLine:function(){this.moveText(0,this.current.leading)},paintChar:function(e,t,r){var i,a=this.ctx,s=this.current,o=s.font,l=s.textRenderingMode,c=s.fontSize/s.fontSizeScale,u=l&n.TextRenderingMode.FILL_STROKE_MASK,h=!!(l&n.TextRenderingMode.ADD_TO_PATH_FLAG);if((o.disableFontFace||h)&&(i=o.getPathGenerator(this.commonObjs,e)),o.disableFontFace?(a.save(),a.translate(t,r),a.beginPath(),i(a,c),u!==n.TextRenderingMode.FILL&&u!==n.TextRenderingMode.FILL_STROKE||a.fill(),u!==n.TextRenderingMode.STROKE&&u!==n.TextRenderingMode.FILL_STROKE||a.stroke(),a.restore()):(u!==n.TextRenderingMode.FILL&&u!==n.TextRenderingMode.FILL_STROKE||a.fillText(e,t,r),u!==n.TextRenderingMode.STROKE&&u!==n.TextRenderingMode.FILL_STROKE||a.strokeText(e,t,r)),h){var d=this.pendingTextPaths||(this.pendingTextPaths=[]);d.push({transform:a.mozCurrentTransform,x:t,y:r,fontSize:c,addToPath:i})}},get isFontSubpixelAAEnabled(){var e=this.canvasFactory.create(10,10).context;e.scale(1.5,1),e.fillText("I",0,10);for(var t=e.getImageData(0,0,10,10).data,r=!1,i=3;i<t.length;i+=4)if(t[i]>0&&t[i]<255){r=!0;break}return(0,n.shadow)(this,"isFontSubpixelAAEnabled",r)},showText:function(e){var t=this.current,r=t.font;if(r.isType3Font)return this.showType3Text(e);var i=t.fontSize;if(0!==i){var a=this.ctx,s=t.fontSizeScale,o=t.charSpacing,l=t.wordSpacing,u=t.fontDirection,h=t.textHScale*u,d=e.length,f=r.vertical,p=f?1:-1,m=r.defaultVMetrics,g=i*t.fontMatrix[0],v=t.textRenderingMode===n.TextRenderingMode.FILL&&!r.disableFontFace;a.save(),a.transform.apply(a,t.textMatrix),a.translate(t.x,t.y+t.textRise),t.patternFill&&(a.fillStyle=t.fillColor.getPattern(a,this)),u>0?a.scale(h,-1):a.scale(h,1);var b=t.lineWidth,_=t.textMatrixScale;if(0===_||0===b){var y=t.textRenderingMode&n.TextRenderingMode.FILL_STROKE_MASK;y!==n.TextRenderingMode.STROKE&&y!==n.TextRenderingMode.FILL_STROKE||(this.cachedGetSinglePixelWidth=null,b=this.getSinglePixelWidth()*c)}else b/=_;1!==s&&(a.scale(s,s),b/=s),a.lineWidth=b;var A,S=0;for(A=0;A<d;++A){var w=e[A];if((0,n.isNum)(w))S+=p*w*i/1e3;else{var P,C,R,k,x,T,E,I=!1,L=(w.isSpace?l:0)+o,D=w.fontChar,O=w.accent,F=w.width;if(f)x=w.vmetric||m,T=w.vmetric?x[1]:.5*F,T=-T*g,E=x[2]*g,F=x?-x[0]:F,P=T/s,C=(S+E)/s;else P=S/s,C=0;if(r.remeasure&&F>0){var N=1e3*a.measureText(D).width/i*s;if(F<N&&this.isFontSubpixelAAEnabled){var M=F/N;I=!0,a.save(),a.scale(M,1),P/=M}else F!==N&&(P+=(F-N)/2e3*i/s)}(w.isInFont||r.missingFile)&&(v&&!O?a.fillText(D,P,C):(this.paintChar(D,P,C),O&&(R=P+O.offset.x/s,k=C-O.offset.y/s,this.paintChar(O.fontChar,R,k))));var j=F*g+L*u;S+=j,I&&a.restore()}}f?t.y-=S*h:t.x+=S*h,a.restore()}},showType3Text:function(e){var t,r,i,a,s=this.ctx,o=this.current,l=o.font,c=o.fontSize,u=o.fontDirection,h=l.vertical?1:-1,d=o.charSpacing,f=o.wordSpacing,p=o.textHScale*u,m=o.fontMatrix||n.FONT_IDENTITY_MATRIX,g=e.length,v=o.textRenderingMode===n.TextRenderingMode.INVISIBLE;if(!v&&0!==c){for(this.cachedGetSinglePixelWidth=null,s.save(),s.transform.apply(s,o.textMatrix),s.translate(o.x,o.y),s.scale(p,u),t=0;t<g;++t)if(r=e[t],(0,n.isNum)(r))a=h*r*c/1e3,this.ctx.translate(a,0),o.x+=a*p;else{var b=(r.isSpace?f:0)+d,_=l.charProcOperatorList[r.operatorListId];if(_){this.processingType3=r,this.save(),s.scale(c,c),s.transform.apply(s,m),this.executeOperatorList(_),this.restore();var y=n.Util.applyTransform([r.width,0],m);i=y[0]*c+b,s.translate(i,0),o.x+=i*p}else(0,n.warn)('Type3 character "'+r.operatorListId+'" is not available.')}s.restore(),this.processingType3=null}},setCharWidth:function(e,t){},setCharWidthAndBounds:function(e,t,r,n,i,a){this.ctx.rect(r,n,i-r,a-n),this.clip(),this.endPath()},getColorN_Pattern:function(e){var t,n=this;if("TilingPattern"===e[0]){var a=e[1],s=this.baseTransform||this.ctx.mozCurrentTransform.slice(),o={createCanvasGraphics:function(e){return new r(e,n.commonObjs,n.objs,n.canvasFactory)}};t=new i.TilingPattern(e,a,this.ctx,o,s)}else t=(0,i.getShadingPatternFromIR)(e);return t},setStrokeColorN:function(){this.current.strokeColor=this.getColorN_Pattern(arguments)},setFillColorN:function(){this.current.fillColor=this.getColorN_Pattern(arguments),this.current.patternFill=!0},setStrokeRGBColor:function(e,t,r){var i=n.Util.makeCssRgb(e,t,r);this.ctx.strokeStyle=i,this.current.strokeColor=i},setFillRGBColor:function(e,t,r){var i=n.Util.makeCssRgb(e,t,r);this.ctx.fillStyle=i,this.current.fillColor=i,this.current.patternFill=!1},shadingFill:function(e){var t=this.ctx;this.save();var r=(0,i.getShadingPatternFromIR)(e);t.fillStyle=r.getPattern(t,this,!0);var a=t.mozCurrentTransformInverse;if(a){var s=t.canvas,o=s.width,l=s.height,c=n.Util.applyTransform([0,0],a),u=n.Util.applyTransform([0,l],a),h=n.Util.applyTransform([o,0],a),d=n.Util.applyTransform([o,l],a),f=Math.min(c[0],u[0],h[0],d[0]),p=Math.min(c[1],u[1],h[1],d[1]),m=Math.max(c[0],u[0],h[0],d[0]),g=Math.max(c[1],u[1],h[1],d[1]);this.ctx.fillRect(f,p,m-f,g-p)}else this.ctx.fillRect(-1e10,-1e10,2e10,2e10);this.restore()},beginInlineImage:function(){throw new Error("Should not call beginInlineImage")},beginImageData:function(){throw new Error("Should not call beginImageData")},paintFormXObjectBegin:function(e,t){if(this.save(),this.baseTransformStack.push(this.baseTransform),(0,n.isArray)(e)&&6===e.length&&this.transform.apply(this,e),this.baseTransform=this.ctx.mozCurrentTransform,(0,n.isArray)(t)&&4===t.length){var r=t[2]-t[0],i=t[3]-t[1];this.ctx.rect(t[0],t[1],r,i),this.clip(),this.endPath()}},paintFormXObjectEnd:function(){this.restore(),this.baseTransform=this.baseTransformStack.pop()},beginGroup:function(e){this.save();var t=this.ctx;e.isolated||(0,n.info)("TODO: Support non-isolated groups."),e.knockout&&(0,n.warn)("Knockout groups not supported.");var r=t.mozCurrentTransform;if(e.matrix&&t.transform.apply(t,e.matrix),!e.bbox)throw new Error("Bounding box is required.");var i=n.Util.getAxialAlignedBoundingBox(e.bbox,t.mozCurrentTransform),a=[0,0,t.canvas.width,t.canvas.height];i=n.Util.intersect(i,a)||[0,0,0,0];var s=Math.floor(i[0]),o=Math.floor(i[1]),c=Math.max(Math.ceil(i[2])-s,1),u=Math.max(Math.ceil(i[3])-o,1),h=1,d=1;c>l&&(h=c/l,c=l),u>l&&(d=u/l,u=l);var f="groupAt"+this.groupLevel;e.smask&&(f+="_smask_"+this.smaskCounter++%2);var p=this.cachedCanvases.getCanvas(f,c,u,!0),m=p.context;m.scale(1/h,1/d),m.translate(-s,-o),m.transform.apply(m,r),e.smask?this.smaskStack.push({canvas:p.canvas,context:m,offsetX:s,offsetY:o,scaleX:h,scaleY:d,subtype:e.smask.subtype,backdrop:e.smask.backdrop,transferMap:e.smask.transferMap||null,startTransformInverse:null}):(t.setTransform(1,0,0,1,0,0),t.translate(s,o),t.scale(h,d)),y(t,m),this.ctx=m,this.setGState([["BM","source-over"],["ca",1],["CA",1]]),this.groupStack.push(t),this.groupLevel++,this.current.activeSMask=null},endGroup:function(e){this.groupLevel--;var t=this.ctx;this.ctx=this.groupStack.pop(),void 0!==this.ctx.imageSmoothingEnabled?this.ctx.imageSmoothingEnabled=!1:this.ctx.mozImageSmoothingEnabled=!1,e.smask?this.tempSMask=this.smaskStack.pop():this.ctx.drawImage(t.canvas,0,0),this.restore()},beginAnnotations:function(){this.save(),this.baseTransform&&this.ctx.setTransform.apply(this.ctx,this.baseTransform)},endAnnotations:function(){this.restore()},beginAnnotation:function(e,t,r){if(this.save(),A(this.ctx),this.current=new v,(0,n.isArray)(e)&&4===e.length){var i=e[2]-e[0],a=e[3]-e[1];this.ctx.rect(e[0],e[1],i,a),this.clip(),this.endPath()}this.transform.apply(this,t),this.transform.apply(this,r)},endAnnotation:function(){this.restore()},paintJpegXObject:function(e,t,r){var i=this.objs.get(e);if(i){this.save();var a=this.ctx;if(a.scale(1/t,-1/r),a.drawImage(i,0,0,i.width,i.height,0,-r,t,r),this.imageLayer){var s=a.mozCurrentTransformInverse,o=this.getCanvasPosition(0,0);this.imageLayer.appendImage({objId:e,left:o[0],top:o[1],width:t/s[0],height:r/s[3]})}this.restore()}else(0,n.warn)("Dependent image isn't ready yet")},paintImageMaskXObject:function(e){var t=this.ctx,r=e.width,n=e.height,i=this.current.fillColor,a=this.current.patternFill,s=this.processingType3;if(u&&s&&void 0===s.compiled&&(s.compiled=r<=h&&n<=h?g({data:e.data,width:r,height:n}):null),s&&s.compiled)s.compiled(t);else{var o=this.cachedCanvases.getCanvas("maskCanvas",r,n),l=o.context;l.save(),_(l,e),l.globalCompositeOperation="source-in",l.fillStyle=a?i.getPattern(l,this):i,l.fillRect(0,0,r,n),l.restore(),this.paintInlineImageXObject(o.canvas)}},paintImageMaskXObjectRepeat:function(e,t,r,n){var i=e.width,a=e.height,s=this.current.fillColor,o=this.current.patternFill,l=this.cachedCanvases.getCanvas("maskCanvas",i,a),c=l.context;c.save(),_(c,e),c.globalCompositeOperation="source-in",c.fillStyle=o?s.getPattern(c,this):s,c.fillRect(0,0,i,a),c.restore();for(var u=this.ctx,h=0,d=n.length;h<d;h+=2)u.save(),u.transform(t,0,0,r,n[h],n[h+1]),u.scale(1,-1),u.drawImage(l.canvas,0,0,i,a,0,-1,1,1),u.restore()},paintImageMaskXObjectGroup:function(e){for(var t=this.ctx,r=this.current.fillColor,n=this.current.patternFill,i=0,a=e.length;i<a;i++){var s=e[i],o=s.width,l=s.height,c=this.cachedCanvases.getCanvas("maskCanvas",o,l),u=c.context;u.save(),_(u,s),u.globalCompositeOperation="source-in",u.fillStyle=n?r.getPattern(u,this):r,u.fillRect(0,0,o,l),u.restore(),t.save(),t.transform.apply(t,s.transform),t.scale(1,-1),t.drawImage(c.canvas,0,0,o,l,0,-1,1,1),t.restore()}},paintImageXObject:function(e){var t=this.objs.get(e);t?this.paintInlineImageXObject(t):(0,n.warn)("Dependent image isn't ready yet")},paintImageXObjectRepeat:function(e,t,r,i){var a=this.objs.get(e);if(a){for(var s=a.width,o=a.height,l=[],c=0,u=i.length;c<u;c+=2)l.push({transform:[t,0,0,r,i[c],i[c+1]],x:0,y:0,w:s,h:o});this.paintInlineImageXObjectGroup(a,l)}else(0,n.warn)("Dependent image isn't ready yet")},paintInlineImageXObject:function(e){var t=e.width,r=e.height,n=this.ctx;this.save(),n.scale(1/t,-1/r);var i,a,s=n.mozCurrentTransformInverse,o=s[0],l=s[1],c=Math.max(Math.sqrt(o*o+l*l),1),u=s[2],h=s[3],d=Math.max(Math.sqrt(u*u+h*h),1);if(e instanceof HTMLElement||!e.data)i=e;else{a=this.cachedCanvases.getCanvas("inlineImage",t,r);var f=a.context;b(f,e),i=a.canvas}var p=t,m=r,g="prescale1";while(c>2&&p>1||d>2&&m>1){var v=p,_=m;c>2&&p>1&&(v=Math.ceil(p/2),c/=p/v),d>2&&m>1&&(_=Math.ceil(m/2),d/=m/_),a=this.cachedCanvases.getCanvas(g,v,_),f=a.context,f.clearRect(0,0,v,_),f.drawImage(i,0,0,p,m,0,0,v,_),i=a.canvas,p=v,m=_,g="prescale1"===g?"prescale2":"prescale1"}if(n.drawImage(i,0,0,p,m,0,-r,t,r),this.imageLayer){var y=this.getCanvasPosition(0,-r);this.imageLayer.appendImage({imgData:e,left:y[0],top:y[1],width:t/s[0],height:r/s[3]})}this.restore()},paintInlineImageXObjectGroup:function(e,t){var r=this.ctx,n=e.width,i=e.height,a=this.cachedCanvases.getCanvas("inlineImage",n,i),s=a.context;b(s,e);for(var o=0,l=t.length;o<l;o++){var c=t[o];if(r.save(),r.transform.apply(r,c.transform),r.scale(1,-1),r.drawImage(a.canvas,c.x,c.y,c.w,c.h,0,-1,1,1),this.imageLayer){var u=this.getCanvasPosition(c.x,c.y);this.imageLayer.appendImage({imgData:e,left:u[0],top:u[1],width:n,height:i})}r.restore()}},paintSolidColorImageMask:function(){this.ctx.fillRect(0,0,1,1)},paintXObject:function(){(0,n.warn)("Unsupported 'paintXObject' command.")},markPoint:function(e){},markPointProps:function(e,t){},beginMarkedContent:function(e){},beginMarkedContentProps:function(e,t){},endMarkedContent:function(){},beginCompat:function(){},endCompat:function(){},consumePath:function(){var e=this.ctx;this.pendingClip&&(this.pendingClip===E?e.clip("evenodd"):e.clip(),this.pendingClip=null),e.beginPath()},getSinglePixelWidth:function(e){if(null===this.cachedGetSinglePixelWidth){this.ctx.save();var t=this.ctx.mozCurrentTransformInverse;this.ctx.restore(),this.cachedGetSinglePixelWidth=Math.sqrt(Math.max(t[0]*t[0]+t[1]*t[1],t[2]*t[2]+t[3]*t[3]))}return this.cachedGetSinglePixelWidth},getCanvasPosition:function(e,t){var r=this.ctx.mozCurrentTransform;return[r[0]*e+r[2]*t+r[4],r[1]*e+r[3]*t+r[5]]}},n.OPS)r.prototype[n.OPS[I]]=r.prototype[I];return r}();t.CanvasGraphics=b},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.FontLoader=t.FontFaceObject=void 0;var n=r(0);function i(e){this.docId=e,this.styleElement=null,this.nativeFontFaces=[],this.loadTestFontId=0,this.loadingContext={requests:[],nextRequestId:0}}i.prototype={insertRule:function(e){var t=this.styleElement;t||(t=this.styleElement=document.createElement("style"),t.id="PDFJS_FONT_STYLE_TAG_"+this.docId,document.documentElement.getElementsByTagName("head")[0].appendChild(t));var r=t.sheet;r.insertRule(e,r.cssRules.length)},clear:function(){this.styleElement&&(this.styleElement.remove(),this.styleElement=null),this.nativeFontFaces.forEach((function(e){document.fonts.delete(e)})),this.nativeFontFaces.length=0}};var a=function(){return atob("T1RUTwALAIAAAwAwQ0ZGIDHtZg4AAAOYAAAAgUZGVE1lkzZwAAAEHAAAABxHREVGABQAFQAABDgAAAAeT1MvMlYNYwkAAAEgAAAAYGNtYXABDQLUAAACNAAAAUJoZWFk/xVFDQAAALwAAAA2aGhlYQdkA+oAAAD0AAAAJGhtdHgD6AAAAAAEWAAAAAZtYXhwAAJQAAAAARgAAAAGbmFtZVjmdH4AAAGAAAAAsXBvc3T/hgAzAAADeAAAACAAAQAAAAEAALZRFsRfDzz1AAsD6AAAAADOBOTLAAAAAM4KHDwAAAAAA+gDIQAAAAgAAgAAAAAAAAABAAADIQAAAFoD6AAAAAAD6AABAAAAAAAAAAAAAAAAAAAAAQAAUAAAAgAAAAQD6AH0AAUAAAKKArwAAACMAooCvAAAAeAAMQECAAACAAYJAAAAAAAAAAAAAQAAAAAAAAAAAAAAAFBmRWQAwAAuAC4DIP84AFoDIQAAAAAAAQAAAAAAAAAAACAAIAABAAAADgCuAAEAAAAAAAAAAQAAAAEAAAAAAAEAAQAAAAEAAAAAAAIAAQAAAAEAAAAAAAMAAQAAAAEAAAAAAAQAAQAAAAEAAAAAAAUAAQAAAAEAAAAAAAYAAQAAAAMAAQQJAAAAAgABAAMAAQQJAAEAAgABAAMAAQQJAAIAAgABAAMAAQQJAAMAAgABAAMAAQQJAAQAAgABAAMAAQQJAAUAAgABAAMAAQQJAAYAAgABWABYAAAAAAAAAwAAAAMAAAAcAAEAAAAAADwAAwABAAAAHAAEACAAAAAEAAQAAQAAAC7//wAAAC7////TAAEAAAAAAAABBgAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMAAAAAAAD/gwAyAAAAAQAAAAAAAAAAAAAAAAAAAAABAAQEAAEBAQJYAAEBASH4DwD4GwHEAvgcA/gXBIwMAYuL+nz5tQXkD5j3CBLnEQACAQEBIVhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYAAABAQAADwACAQEEE/t3Dov6fAH6fAT+fPp8+nwHDosMCvm1Cvm1DAz6fBQAAAAAAAABAAAAAMmJbzEAAAAAzgTjFQAAAADOBOQpAAEAAAAAAAAADAAUAAQAAAABAAAAAgABAAAAAAAAAAAD6AAAAAAAAA==")};Object.defineProperty(i.prototype,"loadTestFont",{get:function(){return(0,n.shadow)(this,"loadTestFont",a())},configurable:!0}),i.prototype.addNativeFontFace=function(e){this.nativeFontFaces.push(e),document.fonts.add(e)},i.prototype.bind=function(e,t){for(var r=[],a=[],s=[],o=function(e){return e.loaded.catch((function(t){(0,n.warn)('Failed to load font "'+e.family+'": '+t)}))},l=i.isFontLoadingAPISupported&&!i.isSyncFontLoadingSupported,c=0,u=e.length;c<u;c++){var h=e[c];if(!h.attached&&!1!==h.loading)if(h.attached=!0,l){var d=h.createNativeFontFace();d&&(this.addNativeFontFace(d),s.push(o(d)))}else{var f=h.createFontFaceRule();f&&(this.insertRule(f),r.push(f),a.push(h))}}var p=this.queueLoadingCallback(t);l?Promise.all(s).then((function(){p.complete()})):r.length>0&&!i.isSyncFontLoadingSupported?this.prepareFontLoadEvent(r,a,p):p.complete()},i.prototype.queueLoadingCallback=function(e){function t(){(0,n.assert)(!a.end,"completeRequest() cannot be called twice"),a.end=Date.now();while(r.requests.length>0&&r.requests[0].end){var e=r.requests.shift();setTimeout(e.callback,0)}}var r=this.loadingContext,i="pdfjs-font-loading-"+r.nextRequestId++,a={id:i,complete:t,callback:e,started:Date.now()};return r.requests.push(a),a},i.prototype.prepareFontLoadEvent=function(e,t,r){function i(e,t){return e.charCodeAt(t)<<24|e.charCodeAt(t+1)<<16|e.charCodeAt(t+2)<<8|255&e.charCodeAt(t+3)}function a(e,t,r,n){var i=e.substr(0,t),a=e.substr(t+r);return i+n+a}var s,o,l=document.createElement("canvas");l.width=1,l.height=1;var c=l.getContext("2d"),u=0;function h(e,t){if(u++,u>30)return(0,n.warn)("Load test font never loaded."),void t();c.font="30px "+e,c.fillText(".",0,20);var r=c.getImageData(0,0,1,1);r.data[3]>0?t():setTimeout(h.bind(null,e,t))}var d="lt"+Date.now()+this.loadTestFontId++,f=this.loadTestFont,p=976;f=a(f,p,d.length,d);var m=16,g=1482184792,v=i(f,m);for(s=0,o=d.length-3;s<o;s+=4)v=v-g+i(d,s)|0;s<d.length&&(v=v-g+i(d+"XXX",s)|0),f=a(f,m,4,(0,n.string32)(v));var b="url(data:font/opentype;base64,"+btoa(f)+");",_='@font-face { font-family:"'+d+'";src:'+b+"}";this.insertRule(_);var y=[];for(s=0,o=t.length;s<o;s++)y.push(t[s].loadedName);y.push(d);var A=document.createElement("div");for(A.setAttribute("style","visibility: hidden;width: 10px; height: 10px;position: absolute; top: 0px; left: 0px;"),s=0,o=y.length;s<o;++s){var S=document.createElement("span");S.textContent="Hi",S.style.fontFamily=y[s],A.appendChild(S)}document.body.appendChild(A),h(d,(function(){document.body.removeChild(A),r.complete()}))},i.isFontLoadingAPISupported="undefined"!==typeof document&&!!document.fonts;var s=function(){if("undefined"===typeof navigator)return!0;var e=!1,t=/Mozilla\/5.0.*?rv:(\d+).*? Gecko/.exec(navigator.userAgent);return t&&t[1]>=14&&(e=!0),e};Object.defineProperty(i,"isSyncFontLoadingSupported",{get:function(){return(0,n.shadow)(i,"isSyncFontLoadingSupported",s())},enumerable:!0,configurable:!0});var o={get value(){return(0,n.shadow)(this,"value",(0,n.isEvalSupported)())}},l=function(){function e(e,t){for(var r in this.compiledGlyphs=Object.create(null),e)this[r]=e[r];this.options=t}return e.prototype={createNativeFontFace:function(){if(!this.data)return null;if(this.options.disableFontFace)return this.disableFontFace=!0,null;var e=new FontFace(this.loadedName,this.data,{});return this.options.fontRegistry&&this.options.fontRegistry.registerFont(this),e},createFontFaceRule:function(){if(!this.data)return null;if(this.options.disableFontFace)return this.disableFontFace=!0,null;var e=(0,n.bytesToString)(new Uint8Array(this.data)),t=this.loadedName,r="url(data:"+this.mimetype+";base64,"+btoa(e)+");",i='@font-face { font-family:"'+t+'";src:'+r+"}";return this.options.fontRegistry&&this.options.fontRegistry.registerFont(this,r),i},getPathGenerator:function(e,t){if(!(t in this.compiledGlyphs)){var r,n,i,a=e.get(this.loadedName+"_path_"+t);if(this.options.isEvalSupported&&o.value){var s,l="";for(n=0,i=a.length;n<i;n++)r=a[n],s=void 0!==r.args?r.args.join(","):"",l+="c."+r.cmd+"("+s+");\n";this.compiledGlyphs[t]=new Function("c","size",l)}else this.compiledGlyphs[t]=function(e,t){for(n=0,i=a.length;n<i;n++)r=a[n],"scale"===r.cmd&&(r.args=[t,-t]),e[r.cmd].apply(e,r.args)}}return this.compiledGlyphs[t]}},e}();t.FontFaceObject=l,t.FontLoader=i},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.TilingPattern=t.getShadingPatternFromIR=void 0;var n=r(0),i=r(7),a={RadialAxial:{fromIR:function(e){var t=e[1],r=e[2],n=e[3],i=e[4],a=e[5],s=e[6];return{type:"Pattern",getPattern:function(e){var o;"axial"===t?o=e.createLinearGradient(n[0],n[1],i[0],i[1]):"radial"===t&&(o=e.createRadialGradient(n[0],n[1],a,i[0],i[1],s));for(var l=0,c=r.length;l<c;++l){var u=r[l];o.addColorStop(u[0],u[1])}return o}}}}},s=function(){function e(e,t,r,n,i,a,s,o){var l,c=t.coords,u=t.colors,h=e.data,d=4*e.width;c[r+1]>c[n+1]&&(l=r,r=n,n=l,l=a,a=s,s=l),c[n+1]>c[i+1]&&(l=n,n=i,i=l,l=s,s=o,o=l),c[r+1]>c[n+1]&&(l=r,r=n,n=l,l=a,a=s,s=l);var f=(c[r]+t.offsetX)*t.scaleX,p=(c[r+1]+t.offsetY)*t.scaleY,m=(c[n]+t.offsetX)*t.scaleX,g=(c[n+1]+t.offsetY)*t.scaleY,v=(c[i]+t.offsetX)*t.scaleX,b=(c[i+1]+t.offsetY)*t.scaleY;if(!(p>=b))for(var _,y,A,S,w,P,C,R,k,x=u[a],T=u[a+1],E=u[a+2],I=u[s],L=u[s+1],D=u[s+2],O=u[o],F=u[o+1],N=u[o+2],M=Math.round(p),j=Math.round(b),q=M;q<=j;q++){q<g?(k=q<p?0:p===g?1:(p-q)/(p-g),_=f-(f-m)*k,y=x-(x-I)*k,A=T-(T-L)*k,S=E-(E-D)*k):(k=q>b?1:g===b?0:(g-q)/(g-b),_=m-(m-v)*k,y=I-(I-O)*k,A=L-(L-F)*k,S=D-(D-N)*k),k=q<p?0:q>b?1:(p-q)/(p-b),w=f-(f-v)*k,P=x-(x-O)*k,C=T-(T-F)*k,R=E-(E-N)*k;for(var U=Math.round(Math.min(_,w)),W=Math.round(Math.max(_,w)),B=d*q+4*U,z=U;z<=W;z++)k=(_-z)/(_-w),k=k<0?0:k>1?1:k,h[B++]=y-(y-P)*k|0,h[B++]=A-(A-C)*k|0,h[B++]=S-(S-R)*k|0,h[B++]=255}}function t(t,r,n){var i,a,s=r.coords,o=r.colors;switch(r.type){case"lattice":var l=r.verticesPerRow,c=Math.floor(s.length/l)-1,u=l-1;for(i=0;i<c;i++)for(var h=i*l,d=0;d<u;d++,h++)e(t,n,s[h],s[h+1],s[h+l],o[h],o[h+1],o[h+l]),e(t,n,s[h+l+1],s[h+1],s[h+l],o[h+l+1],o[h+1],o[h+l]);break;case"triangles":for(i=0,a=s.length;i<a;i+=3)e(t,n,s[i],s[i+1],s[i+2],o[i],o[i+1],o[i+2]);break;default:throw new Error("illegal figure")}}function r(e,r,n,a,s,o,l){var c,u,h,d,f=1.1,p=3e3,m=2,g=Math.floor(e[0]),v=Math.floor(e[1]),b=Math.ceil(e[2])-g,_=Math.ceil(e[3])-v,y=Math.min(Math.ceil(Math.abs(b*r[0]*f)),p),A=Math.min(Math.ceil(Math.abs(_*r[1]*f)),p),S=b/y,w=_/A,P={coords:n,colors:a,offsetX:-g,offsetY:-v,scaleX:1/S,scaleY:1/w},C=y+2*m,R=A+2*m;if(i.WebGLUtils.isEnabled)c=i.WebGLUtils.drawFigures(y,A,o,s,P),u=l.getCanvas("mesh",C,R,!1),u.context.drawImage(c,m,m),c=u.canvas;else{u=l.getCanvas("mesh",C,R,!1);var k=u.context,x=k.createImageData(y,A);if(o){var T=x.data;for(h=0,d=T.length;h<d;h+=4)T[h]=o[0],T[h+1]=o[1],T[h+2]=o[2],T[h+3]=255}for(h=0;h<s.length;h++)t(x,s[h],P);k.putImageData(x,m,m),c=u.canvas}return{canvas:c,offsetX:g-m*S,offsetY:v-m*w,scaleX:S,scaleY:w}}return r}();function o(e){var t=a[e[0]];if(!t)throw new Error("Unknown IR type: "+e[0]);return t.fromIR(e)}a.Mesh={fromIR:function(e){var t=e[2],r=e[3],i=e[4],a=e[5],o=e[6],l=e[8];return{type:"Pattern",getPattern:function(e,c,u){var h;if(u)h=n.Util.singularValueDecompose2dScale(e.mozCurrentTransform);else if(h=n.Util.singularValueDecompose2dScale(c.baseTransform),o){var d=n.Util.singularValueDecompose2dScale(o);h=[h[0]*d[0],h[1]*d[1]]}var f=s(a,h,t,r,i,u?null:l,c.cachedCanvases);return u||(e.setTransform.apply(e,c.baseTransform),o&&e.transform.apply(e,o)),e.translate(f.offsetX,f.offsetY),e.scale(f.scaleX,f.scaleY),e.createPattern(f.canvas,"no-repeat")}}}},a.Dummy={fromIR:function(){return{type:"Pattern",getPattern:function(){return"hotpink"}}}};var l=function(){var e={COLORED:1,UNCOLORED:2},t=3e3;function r(e,t,r,n,i){this.operatorList=e[2],this.matrix=e[3]||[1,0,0,1,0,0],this.bbox=e[4],this.xstep=e[5],this.ystep=e[6],this.paintType=e[7],this.tilingType=e[8],this.color=t,this.canvasGraphicsFactory=n,this.baseTransform=i,this.type="Pattern",this.ctx=r}return r.prototype={createPatternCanvas:function(e){var r=this.operatorList,i=this.bbox,a=this.xstep,s=this.ystep,o=this.paintType,l=this.tilingType,c=this.color,u=this.canvasGraphicsFactory;(0,n.info)("TilingType: "+l);var h=i[0],d=i[1],f=i[2],p=i[3],m=[h,d],g=[h+a,d+s],v=g[0]-m[0],b=g[1]-m[1],_=n.Util.singularValueDecompose2dScale(this.matrix),y=n.Util.singularValueDecompose2dScale(this.baseTransform),A=[_[0]*y[0],_[1]*y[1]];v=Math.min(Math.ceil(Math.abs(v*A[0])),t),b=Math.min(Math.ceil(Math.abs(b*A[1])),t);var S=e.cachedCanvases.getCanvas("pattern",v,b,!0),w=S.context,P=u.createCanvasGraphics(w);P.groupLevel=e.groupLevel,this.setFillAndStrokeStyleToContext(w,o,c),this.setScale(v,b,a,s),this.transformToScale(P);var C=[1,0,0,1,-m[0],-m[1]];return P.transform.apply(P,C),this.clipBbox(P,i,h,d,f,p),P.executeOperatorList(r),S.canvas},setScale:function(e,t,r,n){this.scale=[e/r,t/n]},transformToScale:function(e){var t=this.scale,r=[t[0],0,0,t[1],0,0];e.transform.apply(e,r)},scaleToContext:function(){var e=this.scale;this.ctx.scale(1/e[0],1/e[1])},clipBbox:function(e,t,r,i,a,s){if((0,n.isArray)(t)&&4===t.length){var o=a-r,l=s-i;e.ctx.rect(r,i,o,l),e.clip(),e.endPath()}},setFillAndStrokeStyleToContext:function(t,r,i){switch(r){case e.COLORED:var a=this.ctx;t.fillStyle=a.fillStyle,t.strokeStyle=a.strokeStyle;break;case e.UNCOLORED:var s=n.Util.makeCssRgb(i[0],i[1],i[2]);t.fillStyle=s,t.strokeStyle=s;break;default:throw new n.FormatError("Unsupported paint type: "+r)}},getPattern:function(e,t){var r=this.createPatternCanvas(t);return e=this.ctx,e.setTransform.apply(e,this.baseTransform),e.transform.apply(e,this.matrix),this.scaleToContext(),e.createPattern(r,"repeat")}},r}();t.getShadingPatternFromIR=o,t.TilingPattern=l},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PDFDataTransportStream=void 0;var n=r(0),i=function(){function e(e,t){var r=this;(0,n.assert)(t),this._queuedChunks=[];var i=e.initialData;if(i&&i.length>0){var a=new Uint8Array(i).buffer;this._queuedChunks.push(a)}this._pdfDataRangeTransport=t,this._isRangeSupported=!e.disableRange,this._isStreamingSupported=!e.disableStream,this._contentLength=e.length,this._fullRequestReader=null,this._rangeReaders=[],this._pdfDataRangeTransport.addRangeListener((function(e,t){r._onReceiveData({begin:e,chunk:t})})),this._pdfDataRangeTransport.addProgressListener((function(e){r._onProgress({loaded:e})})),this._pdfDataRangeTransport.addProgressiveReadListener((function(e){r._onReceiveData({chunk:e})})),this._pdfDataRangeTransport.transportReady()}function t(e,t){this._stream=e,this._done=!1,this._queuedChunks=t||[],this._requests=[],this._headersReady=Promise.resolve(),e._fullRequestReader=this,this.onProgress=null}function r(e,t,r){this._stream=e,this._begin=t,this._end=r,this._queuedChunk=null,this._requests=[],this._done=!1,this.onProgress=null}return e.prototype={_onReceiveData:function(e){var t=new Uint8Array(e.chunk).buffer;if(void 0===e.begin)this._fullRequestReader?this._fullRequestReader._enqueue(t):this._queuedChunks.push(t);else{var r=this._rangeReaders.some((function(r){return r._begin===e.begin&&(r._enqueue(t),!0)}));(0,n.assert)(r)}},_onProgress:function(e){if(this._rangeReaders.length>0){var t=this._rangeReaders[0];t.onProgress&&t.onProgress({loaded:e.loaded})}},_removeRangeReader:function(e){var t=this._rangeReaders.indexOf(e);t>=0&&this._rangeReaders.splice(t,1)},getFullReader:function(){(0,n.assert)(!this._fullRequestReader);var e=this._queuedChunks;return this._queuedChunks=null,new t(this,e)},getRangeReader:function(e,t){var n=new r(this,e,t);return this._pdfDataRangeTransport.requestDataRange(e,t),this._rangeReaders.push(n),n},cancelAllRequests:function(e){this._fullRequestReader&&this._fullRequestReader.cancel(e);var t=this._rangeReaders.slice(0);t.forEach((function(t){t.cancel(e)})),this._pdfDataRangeTransport.abort()}},t.prototype={_enqueue:function(e){if(!this._done)if(this._requests.length>0){var t=this._requests.shift();t.resolve({value:e,done:!1})}else this._queuedChunks.push(e)},get headersReady(){return this._headersReady},get isRangeSupported(){return this._stream._isRangeSupported},get isStreamingSupported(){return this._stream._isStreamingSupported},get contentLength(){return this._stream._contentLength},read:function(){if(this._queuedChunks.length>0){var e=this._queuedChunks.shift();return Promise.resolve({value:e,done:!1})}if(this._done)return Promise.resolve({value:void 0,done:!0});var t=(0,n.createPromiseCapability)();return this._requests.push(t),t.promise},cancel:function(e){this._done=!0,this._requests.forEach((function(e){e.resolve({value:void 0,done:!0})})),this._requests=[]}},r.prototype={_enqueue:function(e){if(!this._done){if(0===this._requests.length)this._queuedChunk=e;else{var t=this._requests.shift();t.resolve({value:e,done:!1}),this._requests.forEach((function(e){e.resolve({value:void 0,done:!0})})),this._requests=[]}this._done=!0,this._stream._removeRangeReader(this)}},get isStreamingSupported(){return!1},read:function(){if(this._queuedChunk){var e=this._queuedChunk;return this._queuedChunk=null,Promise.resolve({value:e,done:!1})}if(this._done)return Promise.resolve({value:void 0,done:!0});var t=(0,n.createPromiseCapability)();return this._requests.push(t),t.promise},cancel:function(e){this._done=!0,this._requests.forEach((function(e){e.resolve({value:void 0,done:!0})})),this._requests=[],this._stream._removeRangeReader(this)}},e}();t.PDFDataTransportStream=i},function(e,t,r){"use strict";var n=r(0),i=r(8),a=r(2),s=r(5),o=r(3),l=r(1),c=r(4);r(9),t.PDFJS=i.PDFJS,t.build=a.build,t.version=a.version,t.getDocument=a.getDocument,t.LoopbackPort=a.LoopbackPort,t.PDFDataRangeTransport=a.PDFDataRangeTransport,t.PDFWorker=a.PDFWorker,t.renderTextLayer=s.renderTextLayer,t.AnnotationLayer=o.AnnotationLayer,t.CustomStyle=l.CustomStyle,t.createPromiseCapability=n.createPromiseCapability,t.PasswordResponses=n.PasswordResponses,t.InvalidPDFException=n.InvalidPDFException,t.MissingPDFException=n.MissingPDFException,t.SVGGraphics=c.SVGGraphics,t.NativeImageDecoding=n.NativeImageDecoding,t.UnexpectedResponseException=n.UnexpectedResponseException,t.OPS=n.OPS,t.UNSUPPORTED_FEATURES=n.UNSUPPORTED_FEATURES,t.isValidUrl=l.isValidUrl,t.createValidAbsoluteUrl=n.createValidAbsoluteUrl,t.createObjectURL=n.createObjectURL,t.removeNullCharacters=n.removeNullCharacters,t.shadow=n.shadow,t.createBlob=n.createBlob,t.RenderingCancelledException=l.RenderingCancelledException,t.getFilenameFromUrl=l.getFilenameFromUrl,t.addLinkAttributes=l.addLinkAttributes,t.StatTimer=n.StatTimer},function(e,t,r){"use strict";var n="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};if("undefined"===typeof PDFJS||!PDFJS.compatibilityChecked){var i="undefined"!==typeof window&&window.Math===Math?window:"undefined"!==typeof global&&global.Math===Math?global:"undefined"!==typeof self&&self.Math===Math?self:{},a="undefined"!==typeof navigator&&navigator.userAgent||"",s=/Android/.test(a),o=/Android\s[0-2][^\d]/.test(a),l=/Android\s[0-4][^\d]/.test(a),c=a.indexOf("Chrom")>=0,u=/Chrome\/(39|40)\./.test(a),h=a.indexOf("CriOS")>=0,d=a.indexOf("Trident")>=0,f=/\b(iPad|iPhone|iPod)(?=;)/.test(a),p=a.indexOf("Opera")>=0,m=/Safari\//.test(a)&&!/(Chrome\/|Android\s)/.test(a),g="object"===("undefined"===typeof window?"undefined":n(window))&&"object"===("undefined"===typeof document?"undefined":n(document));"undefined"===typeof PDFJS&&(i.PDFJS={}),PDFJS.compatibilityChecked=!0,function(){if("undefined"!==typeof Uint8Array)return"undefined"===typeof Uint8Array.prototype.subarray&&(Uint8Array.prototype.subarray=function(e,t){return new Uint8Array(this.slice(e,t))},Float32Array.prototype.subarray=function(e,t){return new Float32Array(this.slice(e,t))}),void("undefined"===typeof Float64Array&&(i.Float64Array=Float32Array));function e(e,t){return new l(this.slice(e,t))}function t(e,t){arguments.length<2&&(t=0);for(var r=0,n=e.length;r<n;++r,++t)this[t]=255&e[r]}function r(e,t){this.buffer=e,this.byteLength=e.length,this.length=t,o(this.length)}r.prototype=Object.create(null);var a=0;function s(e){return{get:function(){var t=this.buffer,r=e<<2;return(t[r]|t[r+1]<<8|t[r+2]<<16|t[r+3]<<24)>>>0},set:function(t){var r=this.buffer,n=e<<2;r[n]=255&t,r[n+1]=t>>8&255,r[n+2]=t>>16&255,r[n+3]=t>>>24&255}}}function o(e){while(a<e)Object.defineProperty(r.prototype,a,s(a)),a++}function l(r){var i,a,s;if("number"===typeof r)for(i=[],a=0;a<r;++a)i[a]=0;else if("slice"in r)i=r.slice(0);else for(i=[],a=0,s=r.length;a<s;++a)i[a]=r[a];return i.subarray=e,i.buffer=i,i.byteLength=i.length,i.set=t,"object"===("undefined"===typeof r?"undefined":n(r))&&r.buffer&&(i.buffer=r.buffer),i}i.Uint8Array=l,i.Int8Array=l,i.Int32Array=l,i.Uint16Array=l,i.Float32Array=l,i.Float64Array=l,i.Uint32Array=function(){if(3===arguments.length){if(0!==arguments[1])throw new Error("offset !== 0 is not supported");return new r(arguments[0],arguments[2])}return l.apply(this,arguments)}}(),function(){if(g&&window.CanvasPixelArray){var e=window.CanvasPixelArray.prototype;"buffer"in e||(Object.defineProperty(e,"buffer",{get:function(){return this},enumerable:!1,configurable:!0}),Object.defineProperty(e,"byteLength",{get:function(){return this.length},enumerable:!1,configurable:!0}))}}(),function(){i.URL||(i.URL=i.webkitURL)}(),function(){if("undefined"!==typeof Object.defineProperty){var e=!0;try{g&&Object.defineProperty(new Image,"id",{value:"test"});var t=function(){};t.prototype={get id(){}},Object.defineProperty(new t,"id",{value:"",configurable:!0,enumerable:!0,writable:!1})}catch(r){e=!1}if(e)return}Object.defineProperty=function(e,t,r){delete e[t],"get"in r&&e.__defineGetter__(t,r["get"]),"set"in r&&e.__defineSetter__(t,r["set"]),"value"in r&&(e.__defineSetter__(t,(function(e){return this.__defineGetter__(t,(function(){return e})),e})),e[t]=r.value)}}(),function(){if("undefined"!==typeof XMLHttpRequest){var e=XMLHttpRequest.prototype,t=new XMLHttpRequest;"overrideMimeType"in t||Object.defineProperty(e,"overrideMimeType",{value:function(e){}}),"responseType"in t||(Object.defineProperty(e,"responseType",{get:function(){return this._responseType||"text"},set:function(e){"text"!==e&&"arraybuffer"!==e||(this._responseType=e,"arraybuffer"===e&&"function"===typeof this.overrideMimeType&&this.overrideMimeType("text/plain; charset=x-user-defined"))}}),"undefined"===typeof VBArray?Object.defineProperty(e,"response",{get:function(){if("arraybuffer"!==this.responseType)return this.responseText;var e,t=this.responseText,r=t.length,n=new Uint8Array(r);for(e=0;e<r;++e)n[e]=255&t.charCodeAt(e);return n.buffer}}):Object.defineProperty(e,"response",{get:function(){return"arraybuffer"===this.responseType?new Uint8Array(new VBArray(this.responseBody).toArray()):this.responseText}}))}}(),function(){if(!("btoa"in i)){var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";i.btoa=function(t){var r,n,i="";for(r=0,n=t.length;r<n;r+=3){var a=255&t.charCodeAt(r),s=255&t.charCodeAt(r+1),o=255&t.charCodeAt(r+2),l=a>>2,c=(3&a)<<4|s>>4,u=r+1<n?(15&s)<<2|o>>6:64,h=r+2<n?63&o:64;i+=e.charAt(l)+e.charAt(c)+e.charAt(u)+e.charAt(h)}return i}}}(),function(){if(!("atob"in i)){var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";i.atob=function(t){if(t=t.replace(/=+$/,""),t.length%4===1)throw new Error("bad atob input");for(var r,n,i=0,a=0,s="";n=t.charAt(a++);~n&&(r=i%4?64*r+n:n,i++%4)?s+=String.fromCharCode(255&r>>(-2*i&6)):0)n=e.indexOf(n);return s}}}(),function(){"undefined"===typeof Function.prototype.bind&&(Function.prototype.bind=function(e){var t=this,r=Array.prototype.slice.call(arguments,1),n=function(){var n=r.concat(Array.prototype.slice.call(arguments));return t.apply(e,n)};return n})}(),function(){if(g){var e=document.createElement("div");"dataset"in e||Object.defineProperty(HTMLElement.prototype,"dataset",{get:function(){if(this._dataset)return this._dataset;for(var e={},t=0,r=this.attributes.length;t<r;t++){var n=this.attributes[t];if("data-"===n.name.substring(0,5)){var i=n.name.substring(5).replace(/\-([a-z])/g,(function(e,t){return t.toUpperCase()}));e[i]=n.value}}return Object.defineProperty(this,"_dataset",{value:e,writable:!1,enumerable:!1}),e},enumerable:!0})}}(),function(){function e(e,t,r,n){var i=e.className||"",a=i.split(/\s+/g);""===a[0]&&a.shift();var s=a.indexOf(t);return s<0&&r&&a.push(t),s>=0&&n&&a.splice(s,1),e.className=a.join(" "),s>=0}if(g){var t=document.createElement("div");if(!("classList"in t)){var r={add:function(t){e(this.element,t,!0,!1)},contains:function(t){return e(this.element,t,!1,!1)},remove:function(t){e(this.element,t,!1,!0)},toggle:function(t){e(this.element,t,!0,!0)}};Object.defineProperty(HTMLElement.prototype,"classList",{get:function(){if(this._classList)return this._classList;var e=Object.create(r,{element:{value:this,writable:!1,enumerable:!0}});return Object.defineProperty(this,"_classList",{value:e,writable:!1,enumerable:!1}),e},enumerable:!0})}}}(),function(){if("undefined"!==typeof importScripts&&!("console"in i)){var e={},t={log:function(){var e=Array.prototype.slice.call(arguments);i.postMessage({targetName:"main",action:"console_log",data:e})},error:function(){var e=Array.prototype.slice.call(arguments);i.postMessage({targetName:"main",action:"console_error",data:e})},time:function(t){e[t]=Date.now()},timeEnd:function(t){var r=e[t];if(!r)throw new Error("Unknown timer name "+t);this.log("Timer:",t,Date.now()-r)}};i.console=t}}(),function(){if(g){if("console"in window)return"bind"in console.log?void 0:(console.log=function(e){return function(t){return e(t)}}(console.log),console.error=function(e){return function(t){return e(t)}}(console.error),void(console.warn=function(e){return function(t){return e(t)}}(console.warn)));window.console={log:function(){},error:function(){},warn:function(){}}}}(),function(){function e(e){t(e.target)&&e.stopPropagation()}function t(e){return e.disabled||e.parentNode&&t(e.parentNode)}p&&document.addEventListener("click",e,!0)}(),function(){(d||h)&&(PDFJS.disableCreateObjectURL=!0)}(),function(){"undefined"!==typeof navigator&&("language"in navigator||(PDFJS.locale=navigator.userLanguage||"en-US"))}(),function(){(m||o||u||f)&&(PDFJS.disableRange=!0,PDFJS.disableStream=!0)}(),function(){g&&(history.pushState&&!o||(PDFJS.disableHistory=!0))}(),function(){if(g)if(window.CanvasPixelArray)"function"!==typeof window.CanvasPixelArray.prototype.set&&(window.CanvasPixelArray.prototype.set=function(e){for(var t=0,r=this.length;t<r;t++)this[t]=e[t]});else{var e,t=!1;if(c?(e=a.match(/Chrom(e|ium)\/([0-9]+)\./),t=e&&parseInt(e[2])<21):s?t=l:m&&(e=a.match(/Version\/([0-9]+)\.([0-9]+)\.([0-9]+) Safari\//),t=e&&parseInt(e[1])<6),t){var r=window.CanvasRenderingContext2D.prototype,n=r.createImageData;r.createImageData=function(e,t){var r=n.call(this,e,t);return r.data.set=function(e){for(var t=0,r=this.length;t<r;t++)this[t]=e[t]},r},r=null}}}(),function(){function e(){window.requestAnimationFrame=function(e){return window.setTimeout(e,20)},window.cancelAnimationFrame=function(e){window.clearTimeout(e)}}g&&(f?e():"requestAnimationFrame"in window||(window.requestAnimationFrame=window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame,window.requestAnimationFrame||e()))}(),function(){(f||s)&&(PDFJS.maxCanvasPixels=5242880)}(),function(){g&&d&&window.parent!==window&&(PDFJS.disableFullscreen=!0)}(),function(){g&&("currentScript"in document||Object.defineProperty(document,"currentScript",{get:function(){var e=document.getElementsByTagName("script");return e[e.length-1]},enumerable:!0,configurable:!0}))}(),function(){if(g){var e=document.createElement("input");try{e.type="number"}catch(n){var t=e.constructor.prototype,r=Object.getOwnPropertyDescriptor(t,"type");Object.defineProperty(t,"type",{get:function(){return r.get.call(this)},set:function(e){r.set.call(this,"number"===e?"text":e)},enumerable:!0,configurable:!0})}}}(),function(){if(g&&document.attachEvent){var e=document.constructor.prototype,t=Object.getOwnPropertyDescriptor(e,"readyState");Object.defineProperty(e,"readyState",{get:function(){var e=t.get.call(this);return"interactive"===e?"loading":e},set:function(e){t.set.call(this,e)},enumerable:!0,configurable:!0})}}(),function(){g&&"undefined"===typeof Element.prototype.remove&&(Element.prototype.remove=function(){this.parentNode&&this.parentNode.removeChild(this)})}(),function(){Number.isNaN||(Number.isNaN=function(e){return"number"===typeof e&&isNaN(e)})}(),function(){Number.isInteger||(Number.isInteger=function(e){return"number"===typeof e&&isFinite(e)&&Math.floor(e)===e})}(),function(){if(i.Promise)return"function"!==typeof i.Promise.all&&(i.Promise.all=function(e){var t,r,n=0,a=[],s=new i.Promise((function(e,n){t=e,r=n}));return e.forEach((function(e,i){n++,e.then((function(e){a[i]=e,n--,0===n&&t(a)}),r)})),0===n&&t(a),s}),"function"!==typeof i.Promise.resolve&&(i.Promise.resolve=function(e){return new i.Promise((function(t){t(e)}))}),"function"!==typeof i.Promise.reject&&(i.Promise.reject=function(e){return new i.Promise((function(t,r){r(e)}))}),void("function"!==typeof i.Promise.prototype.catch&&(i.Promise.prototype.catch=function(e){return i.Promise.prototype.then(void 0,e)}));var e=0,t=1,r=2,n=500,a={handlers:[],running:!1,unhandledRejections:[],pendingRejectionCheck:!1,scheduleHandlers:function(t){t._status!==e&&(this.handlers=this.handlers.concat(t._handlers),t._handlers=[],this.running||(this.running=!0,setTimeout(this.runHandlers.bind(this),0)))},runHandlers:function(){var e=1,n=Date.now()+e;while(this.handlers.length>0){var i=this.handlers.shift(),a=i.thisPromise._status,s=i.thisPromise._value;try{a===t?"function"===typeof i.onResolve&&(s=i.onResolve(s)):"function"===typeof i.onReject&&(s=i.onReject(s),a=t,i.thisPromise._unhandledRejection&&this.removeUnhandeledRejection(i.thisPromise))}catch(o){a=r,s=o}if(i.nextPromise._updateStatus(a,s),Date.now()>=n)break}this.handlers.length>0?setTimeout(this.runHandlers.bind(this),0):this.running=!1},addUnhandledRejection:function(e){this.unhandledRejections.push({promise:e,time:Date.now()}),this.scheduleRejectionCheck()},removeUnhandeledRejection:function(e){e._unhandledRejection=!1;for(var t=0;t<this.unhandledRejections.length;t++)this.unhandledRejections[t].promise===e&&(this.unhandledRejections.splice(t),t--)},scheduleRejectionCheck:function(){var e=this;this.pendingRejectionCheck||(this.pendingRejectionCheck=!0,setTimeout((function(){e.pendingRejectionCheck=!1;for(var t=Date.now(),r=0;r<e.unhandledRejections.length;r++)if(t-e.unhandledRejections[r].time>n){var i=e.unhandledRejections[r].promise._value,a="Unhandled rejection: "+i;i.stack&&(a+="\n"+i.stack);try{throw new Error(a)}catch(s){}e.unhandledRejections.splice(r),r--}e.unhandledRejections.length&&e.scheduleRejectionCheck()}),n))}},s=function(t){this._status=e,this._handlers=[];try{t.call(this,this._resolve.bind(this),this._reject.bind(this))}catch(r){this._reject(r)}};s.all=function(e){var t,n,i=new s((function(e,r){t=e,n=r})),a=e.length,o=[];if(0===a)return t(o),i;function l(e){i._status!==r&&(o=[],n(e))}for(var c=0,u=e.length;c<u;++c){var h=e[c],d=function(e){return function(n){i._status!==r&&(o[e]=n,a--,0===a&&t(o))}}(c);s.isPromise(h)?h.then(d,l):d(h)}return i},s.isPromise=function(e){return e&&"function"===typeof e.then},s.resolve=function(e){return new s((function(t){t(e)}))},s.reject=function(e){return new s((function(t,r){r(e)}))},s.prototype={_status:null,_value:null,_handlers:null,_unhandledRejection:null,_updateStatus:function(e,n){this._status!==t&&this._status!==r&&(e===t&&s.isPromise(n)?n.then(this._updateStatus.bind(this,t),this._updateStatus.bind(this,r)):(this._status=e,this._value=n,e===r&&0===this._handlers.length&&(this._unhandledRejection=!0,a.addUnhandledRejection(this)),a.scheduleHandlers(this)))},_resolve:function(e){this._updateStatus(t,e)},_reject:function(e){this._updateStatus(r,e)},then:function(e,t){var r=new s((function(e,t){this.resolve=e,this.reject=t}));return this._handlers.push({thisPromise:this,onResolve:e,onReject:t,nextPromise:r}),a.scheduleHandlers(this),r},catch:function(e){return this.then(void 0,e)}},i.Promise=s}(),function(){if(!i.WeakMap){var e=0;t.prototype={has:function(e){return("object"===("undefined"===typeof e?"undefined":n(e))||"function"===typeof e)&&null!==e&&!!Object.getOwnPropertyDescriptor(e,this.id)},get:function(e){return this.has(e)?e[this.id]:void 0},set:function(e,t){Object.defineProperty(e,this.id,{value:t,enumerable:!1,configurable:!0})},delete:function(e){delete e[this.id]}},i.WeakMap=t}function t(){this.id="$weakmap"+e++}}(),function(){var e=!1;try{if("function"===typeof URL&&"object"===n(URL.prototype)&&"origin"in URL.prototype){var t=new URL("b","http://a");t.pathname="c%20d",e="http://a/c%20d"===t.href}}catch(b){}if(!e){var r=Object.create(null);r["ftp"]=21,r["file"]=0,r["gopher"]=70,r["http"]=80,r["https"]=443,r["ws"]=80,r["wss"]=443;var a=Object.create(null);a["%2e"]=".",a[".%2e"]="..",a["%2e."]="..",a["%2e%2e"]="..";var s,o=/[a-zA-Z]/,l=/[a-zA-Z0-9\+\-\.]/;v.prototype={toString:function(){return this.href},get href(){if(this._isInvalid)return this._url;var e="";return""===this._username&&null===this._password||(e=this._username+(null!==this._password?":"+this._password:"")+"@"),this.protocol+(this._isRelative?"//"+e+this.host:"")+this.pathname+this._query+this._fragment},set href(e){g.call(this),m.call(this,e)},get protocol(){return this._scheme+":"},set protocol(e){this._isInvalid||m.call(this,e+":","scheme start")},get host(){return this._isInvalid?"":this._port?this._host+":"+this._port:this._host},set host(e){!this._isInvalid&&this._isRelative&&m.call(this,e,"host")},get hostname(){return this._host},set hostname(e){!this._isInvalid&&this._isRelative&&m.call(this,e,"hostname")},get port(){return this._port},set port(e){!this._isInvalid&&this._isRelative&&m.call(this,e,"port")},get pathname(){return this._isInvalid?"":this._isRelative?"/"+this._path.join("/"):this._schemeData},set pathname(e){!this._isInvalid&&this._isRelative&&(this._path=[],m.call(this,e,"relative path start"))},get search(){return this._isInvalid||!this._query||"?"===this._query?"":this._query},set search(e){!this._isInvalid&&this._isRelative&&(this._query="?","?"===e[0]&&(e=e.slice(1)),m.call(this,e,"query"))},get hash(){return this._isInvalid||!this._fragment||"#"===this._fragment?"":this._fragment},set hash(e){this._isInvalid||(this._fragment="#","#"===e[0]&&(e=e.slice(1)),m.call(this,e,"fragment"))},get origin(){var e;if(this._isInvalid||!this._scheme)return"";switch(this._scheme){case"data":case"file":case"javascript":case"mailto":return"null";case"blob":try{return new v(this._schemeData).origin||"null"}catch(t){}return"null"}return e=this.host,e?this._scheme+"://"+e:""}};var c=i.URL;c&&(v.createObjectURL=function(e){return c.createObjectURL.apply(c,arguments)},v.revokeObjectURL=function(e){c.revokeObjectURL(e)}),i.URL=v}function u(e){return void 0!==r[e]}function h(){g.call(this),this._isInvalid=!0}function d(e){return""===e&&h.call(this),e.toLowerCase()}function f(e){var t=e.charCodeAt(0);return t>32&&t<127&&-1===[34,35,60,62,63,96].indexOf(t)?e:encodeURIComponent(e)}function p(e){var t=e.charCodeAt(0);return t>32&&t<127&&-1===[34,35,60,62,96].indexOf(t)?e:encodeURIComponent(e)}function m(e,t,n){function i(e){_.push(e)}var c=t||"scheme start",m=0,g="",v=!1,b=!1,_=[];e:while((e[m-1]!==s||0===m)&&!this._isInvalid){var y=e[m];switch(c){case"scheme start":if(!y||!o.test(y)){if(t){i("Invalid scheme.");break e}g="",c="no scheme";continue}g+=y.toLowerCase(),c="scheme";break;case"scheme":if(y&&l.test(y))g+=y.toLowerCase();else{if(":"!==y){if(t){if(y===s)break e;i("Code point not allowed in scheme: "+y);break e}g="",m=0,c="no scheme";continue}if(this._scheme=g,g="",t)break e;u(this._scheme)&&(this._isRelative=!0),c="file"===this._scheme?"relative":this._isRelative&&n&&n._scheme===this._scheme?"relative or authority":this._isRelative?"authority first slash":"scheme data"}break;case"scheme data":"?"===y?(this._query="?",c="query"):"#"===y?(this._fragment="#",c="fragment"):y!==s&&"\t"!==y&&"\n"!==y&&"\r"!==y&&(this._schemeData+=f(y));break;case"no scheme":if(n&&u(n._scheme)){c="relative";continue}i("Missing scheme."),h.call(this);break;case"relative or authority":if("/"!==y||"/"!==e[m+1]){i("Expected /, got: "+y),c="relative";continue}c="authority ignore slashes";break;case"relative":if(this._isRelative=!0,"file"!==this._scheme&&(this._scheme=n._scheme),y===s){this._host=n._host,this._port=n._port,this._path=n._path.slice(),this._query=n._query,this._username=n._username,this._password=n._password;break e}if("/"===y||"\\"===y)"\\"===y&&i("\\ is an invalid code point."),c="relative slash";else if("?"===y)this._host=n._host,this._port=n._port,this._path=n._path.slice(),this._query="?",this._username=n._username,this._password=n._password,c="query";else{if("#"!==y){var A=e[m+1],S=e[m+2];("file"!==this._scheme||!o.test(y)||":"!==A&&"|"!==A||S!==s&&"/"!==S&&"\\"!==S&&"?"!==S&&"#"!==S)&&(this._host=n._host,this._port=n._port,this._username=n._username,this._password=n._password,this._path=n._path.slice(),this._path.pop()),c="relative path";continue}this._host=n._host,this._port=n._port,this._path=n._path.slice(),this._query=n._query,this._fragment="#",this._username=n._username,this._password=n._password,c="fragment"}break;case"relative slash":if("/"!==y&&"\\"!==y){"file"!==this._scheme&&(this._host=n._host,this._port=n._port,this._username=n._username,this._password=n._password),c="relative path";continue}"\\"===y&&i("\\ is an invalid code point."),c="file"===this._scheme?"file host":"authority ignore slashes";break;case"authority first slash":if("/"!==y){i("Expected '/', got: "+y),c="authority ignore slashes";continue}c="authority second slash";break;case"authority second slash":if(c="authority ignore slashes","/"!==y){i("Expected '/', got: "+y);continue}break;case"authority ignore slashes":if("/"!==y&&"\\"!==y){c="authority";continue}i("Expected authority, got: "+y);break;case"authority":if("@"===y){v&&(i("@ already seen."),g+="%40"),v=!0;for(var w=0;w<g.length;w++){var P=g[w];if("\t"!==P&&"\n"!==P&&"\r"!==P)if(":"!==P||null!==this._password){var C=f(P);null!==this._password?this._password+=C:this._username+=C}else this._password="";else i("Invalid whitespace in authority.")}g=""}else{if(y===s||"/"===y||"\\"===y||"?"===y||"#"===y){m-=g.length,g="",c="host";continue}g+=y}break;case"file host":if(y===s||"/"===y||"\\"===y||"?"===y||"#"===y){2!==g.length||!o.test(g[0])||":"!==g[1]&&"|"!==g[1]?(0===g.length||(this._host=d.call(this,g),g=""),c="relative path start"):c="relative path";continue}"\t"===y||"\n"===y||"\r"===y?i("Invalid whitespace in file host."):g+=y;break;case"host":case"hostname":if(":"!==y||b){if(y===s||"/"===y||"\\"===y||"?"===y||"#"===y){if(this._host=d.call(this,g),g="",c="relative path start",t)break e;continue}"\t"!==y&&"\n"!==y&&"\r"!==y?("["===y?b=!0:"]"===y&&(b=!1),g+=y):i("Invalid code point in host/hostname: "+y)}else if(this._host=d.call(this,g),g="",c="port","hostname"===t)break e;break;case"port":if(/[0-9]/.test(y))g+=y;else{if(y===s||"/"===y||"\\"===y||"?"===y||"#"===y||t){if(""!==g){var R=parseInt(g,10);R!==r[this._scheme]&&(this._port=R+""),g=""}if(t)break e;c="relative path start";continue}"\t"===y||"\n"===y||"\r"===y?i("Invalid code point in port: "+y):h.call(this)}break;case"relative path start":if("\\"===y&&i("'\\' not allowed in path."),c="relative path","/"!==y&&"\\"!==y)continue;break;case"relative path":var k;if(y!==s&&"/"!==y&&"\\"!==y&&(t||"?"!==y&&"#"!==y))"\t"!==y&&"\n"!==y&&"\r"!==y&&(g+=f(y));else"\\"===y&&i("\\ not allowed in relative path."),(k=a[g.toLowerCase()])&&(g=k),".."===g?(this._path.pop(),"/"!==y&&"\\"!==y&&this._path.push("")):"."===g&&"/"!==y&&"\\"!==y?this._path.push(""):"."!==g&&("file"===this._scheme&&0===this._path.length&&2===g.length&&o.test(g[0])&&"|"===g[1]&&(g=g[0]+":"),this._path.push(g)),g="","?"===y?(this._query="?",c="query"):"#"===y&&(this._fragment="#",c="fragment");break;case"query":t||"#"!==y?y!==s&&"\t"!==y&&"\n"!==y&&"\r"!==y&&(this._query+=p(y)):(this._fragment="#",c="fragment");break;case"fragment":y!==s&&"\t"!==y&&"\n"!==y&&"\r"!==y&&(this._fragment+=y);break}m++}}function g(){this._scheme="",this._schemeData="",this._username="",this._password=null,this._host="",this._port="",this._path=[],this._query="",this._fragment="",this._isInvalid=!1,this._isRelative=!1}function v(e,t){void 0===t||t instanceof v||(t=new v(String(t))),this._url=e,g.call(this);var r=e.replace(/^[ \t\r\n\f]+|[ \t\r\n\f]+$/g,"");m.call(this,r,null,t)}}()}},function(e,t,r){"use strict";var n=!1;if("undefined"!==typeof ReadableStream)try{new ReadableStream({start:function(e){e.close()}}),n=!0}catch(i){}t.ReadableStream=n?ReadableStream:r(10).ReadableStream}])}));
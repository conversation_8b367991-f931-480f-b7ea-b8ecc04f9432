function webViewerExtLoad(){const e=document.location.search.substring(1),t=e.split("&"),n=Object.create(null);for(let d=0,a=t.length;d<a;++d){const e=t[d],o=e.indexOf("="),i=e.substring(0,o),a=e.substring(o+1,e.length);n[decodeURIComponent(i)]=decodeURIComponent(a)}if(!n.file){let e=n.fileUrl;const t=[];for(const i in n)"fileUrl"!==i&&t.push(i+"="+encodeURIComponent(n[i]));const o=t.join("&");e=e+"?"+o,setTimeout((function(){PDFViewerApplication.close(),PDFViewerApplication.open(e,{withCredentials:!0,scale:"page-width"})}),0)}if("true"===n.isSimpleShowMode){const e=document.createElement("link");e.type="text/css",e.rel="stylesheet",e.href="viewer-extend.css",document.getElementsByTagName("head")[0].appendChild(e),PDFViewerApplication.appConfig.toolbar.container.classList.add("hidden")}const o="false"!==n.isDownload,i="false"!==n.isPrint;o||(PDFViewerApplication.appConfig.toolbar.download.setAttribute("hidden","true"),PDFViewerApplication.appConfig.secondaryToolbar.downloadButton.setAttribute("hidden","true")),i||(PDFViewerApplication.appConfig.toolbar.print.setAttribute("hidden","true"),PDFViewerApplication.appConfig.secondaryToolbar.printButton.setAttribute("hidden","true")),window.toDownLoad=function(e){PDFViewerApplication.download(e)},window.toPrint=function(){window.print()}}"interactive"===document.readyState||"complete"===document.readyState?webViewerExtLoad():document.addEventListener("DOMContentLoaded",webViewerExtLoad,!0);
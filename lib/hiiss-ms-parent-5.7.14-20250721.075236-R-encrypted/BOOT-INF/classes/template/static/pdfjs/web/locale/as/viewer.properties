# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=à¦ªà§à§°à§à¦¬à§±à§°à§à¦¤à§ à¦ªà§à¦·à§à¦ à¦¾
previous_label=à¦ªà§à§°à§à¦¬à§±à§°à§à¦¤à§
next.title=à¦ªà§°à§±à§°à§à¦¤à§ à¦ªà§à¦·à§à¦ à¦¾
next_label=à¦ªà§°à§±à§°à§à¦¤à§

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.

zoom_out.title=à¦à§à¦® à¦à¦à¦
zoom_out_label=à¦à§à¦® à¦à¦à¦
zoom_in.title=à¦à§à¦® à¦à¦¨
zoom_in_label=à¦à§à¦® à¦à¦¨
zoom.title=à¦à§à¦® à¦à§°à¦
presentation_mode.title=à¦à¦ªà¦¸à§à¦¥à¦¾à¦ªà¦¨ à¦à§±à¦¸à§à¦¥à¦¾à¦²à§ à¦¯à¦¾à¦à¦
presentation_mode_label=à¦à¦ªà¦¸à§à¦¥à¦¾à¦ªà¦¨ à¦à§±à¦¸à§à¦¥à¦¾
open_file.title=à¦«à¦¾à¦à¦² à¦à§à¦²à¦
open_file_label=à¦à§à¦²à¦
print.title=à¦ªà§à§°à¦¿à¦¨à§à¦ à¦à§°à¦
print_label=à¦ªà§à§°à¦¿à¦¨à§à¦ à¦à§°à¦
download.title=à¦¡à¦¾à¦à¦¨à¦²'à¦¡ à¦à§°à¦
download_label=à¦¡à¦¾à¦à¦¨à¦²'à¦¡ à¦à§°à¦
bookmark.title=à¦¬à§°à§à¦¤à¦®à¦¾à¦¨ à¦¦à§à¦¶à§à¦¯ (à¦à¦ªà¦¿ à¦à§°à¦ à¦à¦¥à¦¬à¦¾ à¦¨à¦¤à§à¦¨ à¦à¦à¦¨à§à¦¡à§à¦¤ à¦à§à¦²à¦)
bookmark_label=à¦¬à§°à§à¦¤à¦®à¦¾à¦¨ à¦¦à§à¦¶à§à¦¯

# Secondary toolbar and context menu
tools.title=à¦¸à¦à¦à§à¦²à¦¿à¦¸à¦®à§à¦¹
tools_label=à¦¸à¦à¦à§à¦²à¦¿à¦¸à¦®à§à¦¹
first_page.title=à¦ªà§à§°à¦¥à¦® à¦ªà§à¦·à§à¦ à¦¾à¦¤ à¦¯à¦¾à¦à¦
first_page.label=à¦ªà§à§°à¦¥à¦® à¦ªà§à¦·à§à¦ à¦¾à¦¤ à¦¯à¦¾à¦à¦
first_page_label=à¦ªà§à§°à¦¥à¦® à¦ªà§à¦·à§à¦ à¦¾à¦¤ à¦¯à¦¾à¦à¦
last_page.title=à¦¸à§°à§à¦¬à¦¶à§à¦· à¦ªà§à¦·à§à¦ à¦¾à¦¤ à¦¯à¦¾à¦à¦
last_page.label=à¦¸à§°à§à¦¬à¦¶à§à¦· à¦ªà§à¦·à§à¦ à¦¾à¦¤ à¦¯à¦¾à¦à¦
last_page_label=à¦¸à§°à§à¦¬à¦¶à§à¦· à¦ªà§à¦·à§à¦ à¦¾à¦¤ à¦¯à¦¾à¦à¦
page_rotate_cw.title=à¦à§à§à§° à¦¦à¦¿à¦¶à¦¤ à¦à§à§°à¦¾à¦à¦
page_rotate_cw.label=à¦à§à§à§° à¦¦à¦¿à¦¶à¦¤ à¦à§à§°à¦¾à¦à¦
page_rotate_cw_label=à¦à§à§à§° à¦¦à¦¿à¦¶à¦¤ à¦à§à§°à¦¾à¦à¦
page_rotate_ccw.title=à¦à§à§à§° à¦à¦²à§à¦à¦¾ à¦¦à¦¿à¦¶à¦¤ à¦à§à§°à¦¾à¦à¦
page_rotate_ccw.label=à¦à§à§à§° à¦à¦²à§à¦à¦¾ à¦¦à¦¿à¦¶à¦¤ à¦à§à§°à¦¾à¦à¦
page_rotate_ccw_label=à¦à§à§à§° à¦à¦²à§à¦à¦¾ à¦¦à¦¿à¦¶à¦¤ à¦à§à§°à¦¾à¦à¦


# Document properties dialog box
document_properties.title=à¦¦à¦¸à§à¦¤à¦¾à¦¬à§à¦à§° à¦¬à§à¦¶à¦¿à¦·à§à¦à§à¦¯à¦¸à¦®à§à¦¹â¦
document_properties_label=à¦¦à¦¸à§à¦¤à¦¾à¦¬à§à¦à§° à¦¬à§à¦¶à¦¿à¦·à§à¦à§à¦¯à¦¸à¦®à§à¦¹â¦
document_properties_file_name=à¦«à¦¾à¦à¦² à¦¨à¦¾à¦®:
document_properties_file_size=à¦«à¦¾à¦à¦²à§° à¦à¦à¦¾à§°:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} KB ({{size_b}} bytes)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} MB ({{size_b}} bytes)
document_properties_title=à¦¶à§à§°à§à¦·à¦:
document_properties_author=à¦²à§à¦à¦:
document_properties_subject=à¦¬à¦¿à¦·à§:
document_properties_keywords=à¦à¦¿à§±à¦¾à§°à§à¦¡à¦¸à¦®à§à¦¹:
document_properties_creation_date=à¦¸à§à¦·à§à¦à¦¿à§° à¦¤à¦¾à§°à¦¿à¦:
document_properties_modification_date=à¦ªà§°à¦¿à¦¬à§°à§à¦¤à¦¨à§° à¦¤à¦¾à§°à¦¿à¦:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=à¦¸à§à¦·à§à¦à¦¿à¦à§°à§à¦¤à¦¾:
document_properties_producer=PDF à¦à§à¦ªà¦¾à¦¦à¦:
document_properties_version=PDF à¦¸à¦à¦¸à§à¦à§°à¦£:
document_properties_page_count=à¦ªà§à¦·à§à¦ à¦¾à§° à¦à¦£à¦¨à¦¾:
document_properties_close=à¦¬à¦¨à§à¦§ à¦à§°à¦

# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=à¦à¦¾à¦·à¦¬à¦¾à§° à¦à¦à¦² à¦à§°à¦
toggle_sidebar_label=à¦à¦¾à¦·à¦¬à¦¾à§° à¦à¦à¦² à¦à§°à¦
document_outline_label=à¦¦à¦¸à§à¦¤à¦¾à¦¬à§à¦ à¦à¦à¦à¦²à¦¾à¦à¦¨
attachments.title=à¦à¦à¦¾à¦à¦®à§à¦¨à§à¦à¦¸à¦®à§à¦¹ à¦¦à§à¦à§à§±à¦¾à¦à¦
attachments_label=à¦à¦à¦¾à¦à¦®à§à¦¨à§à¦à¦¸à¦®à§à¦¹
thumbs.title=à¦¥à¦¾à¦®à§à¦¬à¦¨à§à¦à¦²à¦¸à¦®à§à¦¹ à¦¦à§à¦à§à§±à¦¾à¦à¦
thumbs_label=à¦¥à¦¾à¦®à§à¦¬à¦¨à§à¦à¦²à¦¸à¦®à§à¦¹
findbar.title=à¦¦à¦¸à§à¦¤à¦¾à¦¬à§à¦à¦¤ à¦¸à¦¨à§à¦§à¦¾à¦¨ à¦à§°à¦

# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=à¦ªà§à¦·à§à¦ à¦¾ {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=à¦ªà§à¦·à§à¦ à¦¾à§° à¦¥à¦¾à¦®à§à¦¬à¦¨à§à¦à¦² {{page}}

# Find panel button title and messages
find_previous.title=à¦¬à¦¾à¦à§à¦¯à¦¾à¦à¦¶à§° à¦ªà§à§°à§à¦¬à§±à§°à§à¦¤à§ à¦à¦ªà¦¸à§à¦¥à¦¿à¦¤à¦¿ à¦¸à¦¨à§à¦§à¦¾à¦¨ à¦à§°à¦
find_previous_label=à¦ªà§à§°à§à¦¬à§±à§°à§à¦¤à§
find_next.title=à¦¬à¦¾à¦à§à¦¯à¦¾à¦à¦¶à§° à¦ªà§°à§±à§°à§à¦¤à§ à¦à¦ªà¦¸à§à¦¥à¦¿à¦¤à¦¿ à¦¸à¦¨à§à¦§à¦¾à¦¨ à¦à§°à¦
find_next_label=à¦ªà§°à§±à§°à§à¦¤à§
find_highlight=à¦¸à¦à¦²à§ à¦à¦à§à¦à§à¦¬à¦² à¦à§°à¦
find_match_case_label=à¦«à¦²à¦¾ à¦®à¦¿à¦²à¦¾à¦à¦
find_reached_top=à¦¤à¦²à§° à¦ªà§°à¦¾ à¦à§°à¦®à§à¦­ à¦à§°à¦¿, à¦¦à¦¸à§à¦¤à¦¾à¦¬à§à¦à§° à¦à¦ªà§°à¦²à§ à¦à¦¹à¦¾ à¦¹à§à¦à§
find_reached_bottom=à¦à¦ªà§°à§° à¦ªà§°à¦¾ à¦à§°à¦®à§à¦­ à¦à§°à¦¿, à¦¦à¦¸à§à¦¤à¦¾à¦¬à§à¦à§° à¦¤à¦²à¦²à§ à¦à¦¹à¦¾ à¦¹à§à¦à§
find_not_found=à¦¬à¦¾à¦à§à¦¯à¦¾à¦à¦¶ à¦ªà§à§±à¦¾ à¦¨à¦à¦²

# Error panel labels
error_more_info=à¦à¦§à¦¿à¦ à¦¤à¦¥à§à¦¯
error_less_info=à¦à¦® à¦¤à¦¥à§à¦¯
error_close=à¦¬à¦¨à§à¦§ à¦à§°à¦
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (build: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=à¦¬à¦¾à§°à§à¦¤à¦¾: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=à¦¸à§à¦à§à¦: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=à¦«à¦¾à¦à¦²: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=à¦¶à¦¾à§°à§: {{line}}
rendering_error=à¦à¦ à¦ªà§à¦·à§à¦ à¦¾ à§°à§à¦£à§à¦¡à¦¾à§° à¦à§°à§à¦¤à§ à¦à¦à¦¾ à¦¤à§à§°à§à¦à¦¿ à¦¦à§à¦à¦¾ à¦¦à¦¿à¦²à§à¥¤

# Predefined zoom values
page_scale_width=à¦ªà§à¦·à§à¦ à¦¾à§° à¦ªà§à§°à¦¸à§à¦¥
page_scale_fit=à¦ªà§à¦·à§à¦ à¦¾ à¦à¦¾à¦ª
page_scale_auto=à¦¸à§à¦¬à¦à¦¾à¦²à¦¿à¦¤ à¦à§à¦®
page_scale_actual=à¦ªà§à§°à¦à§à¦¤ à¦à¦à¦¾à§°
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.

# Loading indicator messages
loading_error_indicator=à¦¤à§à§°à§à¦à¦¿
loading_error=PDF à¦²'à¦¡ à¦à§°à§à¦¤à§ à¦à¦à¦¾ à¦¤à§à§°à§à¦à¦¿ à¦¦à§à¦à¦¾ à¦¦à¦¿à¦²à§à¥¤
invalid_file_error=à¦à¦¬à§à¦§ à¦à¦¥à¦¬à¦¾ à¦à§à¦·à¦¤à¦¿à¦à§à§°à¦¸à§à¦¥ PDF fileà¥¤
missing_file_error=à¦¸à¦¨à§à¦§à¦¾à¦¨à¦¹à¦¿à¦¨ PDF à¦«à¦¾à¦à¦²à¥¤
unexpected_response_error=à¦à¦ªà§à§°à¦¤à§à¦¯à¦¾à¦¶à¦¿à¦¤ à¦à¦¾à§°à§à¦­à¦¾à§° à¦ªà§à§°à¦¤à¦¿à¦à§à§°à¦¿à§à¦¾à¥¤

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 â Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[{{type}} à¦à§à¦à¦¾]
password_label=à¦à¦ PDF à¦«à¦¾à¦à¦² à¦à§à¦²à¦¿à¦¬à¦²à§ à¦ªà¦¾à¦à§±à§°à§à¦¡ à¦¸à§à¦®à§à§±à¦¾à¦à¦à¥¤
password_invalid=à¦à¦¬à§à¦§ à¦ªà¦¾à¦à§±à§°à§à¦¡à¥¤ à¦à¦¨à§à¦à§à§°à¦¹ à¦à§°à¦¿ à¦ªà§à¦¨à§° à¦à§à¦·à§à¦à¦¾ à¦à§°à¦à¥¤
password_ok=à¦ à¦¿à¦ à¦à¦à§

printing_not_supported=à¦¸à¦¤à§°à§à¦à¦¬à¦¾à§°à§à¦¤à¦¾: à¦ªà§à§°à¦¿à¦¨à§à¦à¦¿à¦ à¦à¦ à¦¬à§à§°à¦¾à¦à¦à¦¾à§° à¦¦à§à¦¬à¦¾à§°à¦¾ à¦¸à¦®à§à¦ªà§à§°à§à¦£à¦­à¦¾à§±à§ à¦¸à¦®à§°à§à¦¥à¦¿à¦¤ à¦¨à¦¹à§à¥¤
printing_not_ready=à¦¸à¦¤à§°à§à¦à¦¬à¦¾à§°à§à¦¤à¦¾: PDF à¦ªà§à§°à¦¿à¦¨à§à¦à¦¿à¦à§° à¦¬à¦¾à¦¬à§ à¦¸à¦®à§à¦ªà§à§°à§à¦£à¦­à¦¾à§±à§ à¦²'à¦¡à§à¦¡ à¦¨à¦¹à§à¥¤
web_fonts_disabled=à§±à§à¦¬ à¦«à¦¨à§à¦à¦¸à¦®à§à¦¹ à¦à¦¸à¦¾à¦®à§°à§à¦¥à¦¬à¦¾à¦¨ à¦à§°à¦¾ à¦à¦à§: à¦à¦¨à§à¦¤à§°à§à¦­à§à¦à§à¦¤ PDF à¦«à¦¨à§à¦à¦¸à¦®à§à¦¹ à¦¬à§à¦¯à§±à¦¹à¦¾à§° à¦à§°à¦¿à¦¬à¦²à§ à¦à¦à§à¦·à¦®à¥¤
document_colors_not_allowed=PDF à¦¦à¦¸à§à¦¤à¦¾à¦¬à§à¦à¦¸à¦®à§à¦¹à§° à¦¸à¦¿à¦¹à¦¤à§° à¦¨à¦¿à¦à¦¸à§à¦¬ à§°à¦ à¦¬à§à¦¯à§±à¦¹à¦¾à§° à¦à§°à¦¾à§° à¦à¦¨à§à¦®à¦¤à¦¿ à¦¨à¦¾à¦: à¦¬à§à§°à¦¾à¦à¦à¦¾à§°à¦¤ 'à¦ªà§à¦·à§à¦ à¦¾à¦¸à¦®à§à¦¹à¦ à¦¸à¦¿à¦¹à¦¤à§° à¦¨à¦¿à¦à¦¸à§à¦¬ à§°à¦ à¦¨à¦¿à§°à§à¦¬à¦¾à¦à¦¨ à¦à§°à¦¾à§° à¦à¦¨à§à¦®à¦¤à¦¿ à¦¦à¦¿à§à¦' à¦à¦¸à¦¾à¦®à§°à§à¦¥à¦¬à¦¾à¦¨ à¦à§°à¦¾ à¦à¦à§à¥¤

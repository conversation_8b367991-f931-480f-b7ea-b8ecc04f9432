(function(e){var t={};function n(i){if(t[i])return t[i].exports;var r=t[i]={i:i,l:!1,exports:{}};return e[i].call(r.exports,r,r.exports,n),r.l=!0,r.exports}n.m=e,n.c=t,n.i=function(e){return e},n.d=function(e,t,i){n.o(e,t)||Object.defineProperty(e,t,{configurable:!1,enumerable:!0,get:i})},n.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=33)})([function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.localized=t.animationStarted=t.normalizeWheelEventDelta=t.binarySearchFirstItem=t.watchScroll=t.scrollIntoView=t.getOutputScale=t.approximateFraction=t.roundToDivide=t.getVisibleElements=t.parseQueryString=t.noContextMenuHandler=t.getPDFFileNameFromURL=t.ProgressBar=t.EventBus=t.NullL10n=t.mozL10n=t.RendererType=t.cloneObj=t.VERTICAL_PADDING=t.SCROLLBAR_PADDING=t.MAX_AUTO_SCALE=t.UNKNOWN_SCALE=t.MAX_SCALE=t.MIN_SCALE=t.DEFAULT_SCALE=t.DEFAULT_SCALE_VALUE=t.CSS_UNITS=void 0;var i=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}(),r=n(1);function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var o=96/72,s="auto",u=1,l=.25,c=10,h=0,d=1.25,f=40,v=5,g={CANVAS:"canvas",SVG:"svg"};function p(e,t){return t?e.replace(/\{\{\s*(\w+)\s*\}\}/g,(function(e,n){return n in t?t[n]:"{{"+n+"}}"})):e}var m={get:function(e,t,n){return Promise.resolve(p(n,t))},translate:function(e){return Promise.resolve()}};function w(e){var t=window.devicePixelRatio||1,n=e.webkitBackingStorePixelRatio||e.mozBackingStorePixelRatio||e.msBackingStorePixelRatio||e.oBackingStorePixelRatio||e.backingStorePixelRatio||1,i=t/n;return{sx:i,sy:i,scaled:1!==i}}function b(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=e.offsetParent;if(i){var r=e.offsetTop+e.clientTop,a=e.offsetLeft+e.clientLeft;while(i.clientHeight===i.scrollHeight||n&&"hidden"===getComputedStyle(i).overflow)if(i.dataset._scaleY&&(r/=i.dataset._scaleY,a/=i.dataset._scaleX),r+=i.offsetTop,a+=i.offsetLeft,i=i.offsetParent,!i)return;t&&(void 0!==t.top&&(r+=t.top),void 0!==t.left&&(a+=t.left,i.scrollLeft=a)),i.scrollTop=r}}function y(e,t){var n=function(n){r||(r=window.requestAnimationFrame((function(){r=null;var n=e.scrollTop,a=i.lastY;n!==a&&(i.down=n>a),i.lastY=n,t(i)})))},i={down:!0,lastY:e.scrollTop,_eventHandler:n},r=null;return e.addEventListener("scroll",n,!0),i}function P(e){for(var t=e.split("&"),n=Object.create(null),i=0,r=t.length;i<r;++i){var a=t[i].split("="),o=a[0].toLowerCase(),s=a.length>1?a[1]:null;n[decodeURIComponent(o)]=decodeURIComponent(s)}return n}function S(e,t){var n=0,i=e.length-1;if(0===e.length||!t(e[i]))return e.length;if(t(e[n]))return n;while(n<i){var r=n+i>>1,a=e[r];t(a)?i=r:n=r+1}return n}function k(e){if(Math.floor(e)===e)return[e,1];var t=1/e,n=8;if(t>n)return[1,n];if(Math.floor(t)===t)return[1,t];var i=e>1?t:e,r=0,a=1,o=1,s=1;while(1){var u=r+o,l=a+s;if(l>n)break;i<=u/l?(o=u,s=l):(r=u,a=l)}var c=void 0;return c=i-r/a<o/s-i?i===e?[r,a]:[a,r]:i===e?[o,s]:[s,o],c}function C(e,t){var n=e%t;return 0===n?e:Math.round(e-n+t)}function _(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=e.scrollTop,r=i+e.clientHeight,a=e.scrollLeft,o=a+e.clientWidth;function s(e){var t=e.div,n=t.offsetTop+t.clientTop+t.clientHeight;return n>i}for(var u=[],l=void 0,c=void 0,h=void 0,d=void 0,f=void 0,v=void 0,g=void 0,p=void 0,m=0===t.length?0:S(t,s),w=m,b=t.length;w<b;w++){if(l=t[w],c=l.div,h=c.offsetTop+c.clientTop,d=c.clientHeight,h>r)break;g=c.offsetLeft+c.clientLeft,p=c.clientWidth,g+p<a||g>o||(f=Math.max(0,i-h)+Math.max(0,h+d-r),v=100*(d-f)/d|0,u.push({id:l.id,x:g,y:h,view:l,percent:v}))}var y=u[0],P=u[u.length-1];return n&&u.sort((function(e,t){var n=e.percent-t.percent;return Math.abs(n)>.001?-n:e.id-t.id})),{first:y,last:P,views:u}}function L(e){e.preventDefault()}function E(e){var t=0,n=e.length;while(t<n&&""===e[t].trim())t++;return"data:"===e.substr(t,5).toLowerCase()}function T(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"document.pdf";if(E(e))return t;var n=/^(?:(?:[^:]+:)?\/\/[^\/]+)?([^?#]*)(\?[^#]*)?(#.*)?$/,i=/[^\/?#=]+\.pdf\b(?!.*\.pdf\b)/i,r=n.exec(e),a=i.exec(r[1])||i.exec(r[2])||i.exec(r[3]);if(a&&(a=a[0],-1!==a.indexOf("%")))try{a=i.exec(decodeURIComponent(a))[0]}catch(o){}return a||t}function I(e){var t=Math.sqrt(e.deltaX*e.deltaX+e.deltaY*e.deltaY),n=Math.atan2(e.deltaY,e.deltaX);-.25*Math.PI<n&&n<.75*Math.PI&&(t=-t);var i=0,r=1,a=30,o=30;return e.deltaMode===i?t/=a*o:e.deltaMode===r&&(t/=o),t}function B(e){var t=Object.create(null);for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t}r.PDFJS.disableFullscreen=void 0!==r.PDFJS.disableFullscreen&&r.PDFJS.disableFullscreen,r.PDFJS.useOnlyCssZoom=void 0!==r.PDFJS.useOnlyCssZoom&&r.PDFJS.useOnlyCssZoom,r.PDFJS.maxCanvasPixels=void 0===r.PDFJS.maxCanvasPixels?16777216:r.PDFJS.maxCanvasPixels,r.PDFJS.disableHistory=void 0!==r.PDFJS.disableHistory&&r.PDFJS.disableHistory,r.PDFJS.disableTextLayer=void 0!==r.PDFJS.disableTextLayer&&r.PDFJS.disableTextLayer,r.PDFJS.ignoreCurrentPositionOnZoom=void 0!==r.PDFJS.ignoreCurrentPositionOnZoom&&r.PDFJS.ignoreCurrentPositionOnZoom,r.PDFJS.locale=void 0===r.PDFJS.locale&&"undefined"!==typeof navigator?navigator.language:r.PDFJS.locale;var x=new Promise((function(e){window.requestAnimationFrame(e)})),D=void 0,F=Promise.resolve(),N=function(){function e(){a(this,e),this._listeners=Object.create(null)}return i(e,[{key:"on",value:function(e,t){var n=this._listeners[e];n||(n=[],this._listeners[e]=n),n.push(t)}},{key:"off",value:function(e,t){var n=this._listeners[e],i=void 0;!n||(i=n.indexOf(t))<0||n.splice(i,1)}},{key:"dispatch",value:function(e){var t=this._listeners[e];if(t&&0!==t.length){var n=Array.prototype.slice.call(arguments,1);t.slice(0).forEach((function(e){e.apply(null,n)}))}}}]),e}();function V(e,t,n){return Math.min(Math.max(e,t),n)}var M=function(){function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=n.height,r=n.width,o=n.units;a(this,e),this.visible=!0,this.div=document.querySelector(t+" .progress"),this.bar=this.div.parentNode,this.height=i||100,this.width=r||100,this.units=o||"%",this.div.style.height=this.height+this.units,this.percent=0}return i(e,[{key:"_updateBar",value:function(){if(this._indeterminate)return this.div.classList.add("indeterminate"),void(this.div.style.width=this.width+this.units);this.div.classList.remove("indeterminate");var e=this.width*this._percent/100;this.div.style.width=e+this.units}},{key:"setWidth",value:function(e){if(e){var t=e.parentNode,n=t.offsetWidth-e.offsetWidth;n>0&&this.bar.setAttribute("style","width: calc(100% - "+n+"px);")}}},{key:"hide",value:function(){this.visible&&(this.visible=!1,this.bar.classList.add("hidden"),document.body.classList.remove("loadingInProgress"))}},{key:"show",value:function(){this.visible||(this.visible=!0,document.body.classList.add("loadingInProgress"),this.bar.classList.remove("hidden"))}},{key:"percent",get:function(){return this._percent},set:function(e){this._indeterminate=isNaN(e),this._percent=V(e,0,100),this._updateBar()}}]),e}();t.CSS_UNITS=o,t.DEFAULT_SCALE_VALUE=s,t.DEFAULT_SCALE=u,t.MIN_SCALE=l,t.MAX_SCALE=c,t.UNKNOWN_SCALE=h,t.MAX_AUTO_SCALE=d,t.SCROLLBAR_PADDING=f,t.VERTICAL_PADDING=v,t.cloneObj=B,t.RendererType=g,t.mozL10n=D,t.NullL10n=m,t.EventBus=N,t.ProgressBar=M,t.getPDFFileNameFromURL=T,t.noContextMenuHandler=L,t.parseQueryString=P,t.getVisibleElements=_,t.roundToDivide=C,t.approximateFraction=k,t.getOutputScale=w,t.scrollIntoView=b,t.watchScroll=y,t.binarySearchFirstItem=S,t.normalizeWheelEventDelta=I,t.animationStarted=x,t.localized=F},function(e,t,n){"use strict";var i;i="undefined"!==typeof window&&window["pdfjs-dist/build/pdf"]?window["pdfjs-dist/build/pdf"]:require("../build/pdf.js"),e.exports=i},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getGlobalEventBus=t.attachDOMEventsToEventBus=void 0;var i=n(0);function r(e){e.on("documentload",(function(){var e=document.createEvent("CustomEvent");e.initCustomEvent("documentload",!0,!0,{}),window.dispatchEvent(e)})),e.on("pagerendered",(function(e){var t=document.createEvent("CustomEvent");t.initCustomEvent("pagerendered",!0,!0,{pageNumber:e.pageNumber,cssTransform:e.cssTransform}),e.source.div.dispatchEvent(t)})),e.on("textlayerrendered",(function(e){var t=document.createEvent("CustomEvent");t.initCustomEvent("textlayerrendered",!0,!0,{pageNumber:e.pageNumber}),e.source.textLayerDiv.dispatchEvent(t)})),e.on("pagechange",(function(e){var t=document.createEvent("UIEvents");t.initUIEvent("pagechange",!0,!0,window,0),t.pageNumber=e.pageNumber,e.source.container.dispatchEvent(t)})),e.on("pagesinit",(function(e){var t=document.createEvent("CustomEvent");t.initCustomEvent("pagesinit",!0,!0,null),e.source.container.dispatchEvent(t)})),e.on("pagesloaded",(function(e){var t=document.createEvent("CustomEvent");t.initCustomEvent("pagesloaded",!0,!0,{pagesCount:e.pagesCount}),e.source.container.dispatchEvent(t)})),e.on("scalechange",(function(e){var t=document.createEvent("UIEvents");t.initUIEvent("scalechange",!0,!0,window,0),t.scale=e.scale,t.presetValue=e.presetValue,e.source.container.dispatchEvent(t)})),e.on("updateviewarea",(function(e){var t=document.createEvent("UIEvents");t.initUIEvent("updateviewarea",!0,!0,window,0),t.location=e.location,e.source.container.dispatchEvent(t)})),e.on("find",(function(e){if(e.source!==window){var t=document.createEvent("CustomEvent");t.initCustomEvent("find"+e.type,!0,!0,{query:e.query,phraseSearch:e.phraseSearch,caseSensitive:e.caseSensitive,highlightAll:e.highlightAll,findPrevious:e.findPrevious}),window.dispatchEvent(t)}})),e.on("attachmentsloaded",(function(e){var t=document.createEvent("CustomEvent");t.initCustomEvent("attachmentsloaded",!0,!0,{attachmentsCount:e.attachmentsCount}),e.source.container.dispatchEvent(t)})),e.on("sidebarviewchanged",(function(e){var t=document.createEvent("CustomEvent");t.initCustomEvent("sidebarviewchanged",!0,!0,{view:e.view}),e.source.outerContainer.dispatchEvent(t)})),e.on("pagemode",(function(e){var t=document.createEvent("CustomEvent");t.initCustomEvent("pagemode",!0,!0,{mode:e.mode}),e.source.pdfViewer.container.dispatchEvent(t)})),e.on("namedaction",(function(e){var t=document.createEvent("CustomEvent");t.initCustomEvent("namedaction",!0,!0,{action:e.action}),e.source.pdfViewer.container.dispatchEvent(t)})),e.on("presentationmodechanged",(function(e){var t=document.createEvent("CustomEvent");t.initCustomEvent("presentationmodechanged",!0,!0,{active:e.active,switchInProgress:e.switchInProgress}),window.dispatchEvent(t)})),e.on("outlineloaded",(function(e){var t=document.createEvent("CustomEvent");t.initCustomEvent("outlineloaded",!0,!0,{outlineCount:e.outlineCount}),e.source.container.dispatchEvent(t)}))}var a=null;function o(){return a||(a=new i.EventBus,r(a),a)}t.attachDOMEventsToEventBus=r,t.getGlobalEventBus=o},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}();function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var a=3e4,o={INITIAL:0,RUNNING:1,PAUSED:2,FINISHED:3},s=function(){function e(){r(this,e),this.pdfViewer=null,this.pdfThumbnailViewer=null,this.onIdle=null,this.highestPriorityPage=null,this.idleTimeout=null,this.printing=!1,this.isThumbnailViewEnabled=!1}return i(e,[{key:"setViewer",value:function(e){this.pdfViewer=e}},{key:"setThumbnailViewer",value:function(e){this.pdfThumbnailViewer=e}},{key:"isHighestPriority",value:function(e){return this.highestPriorityPage===e.renderingId}},{key:"renderHighestPriority",value:function(e){this.idleTimeout&&(clearTimeout(this.idleTimeout),this.idleTimeout=null),this.pdfViewer.forceRendering(e)||this.pdfThumbnailViewer&&this.isThumbnailViewEnabled&&this.pdfThumbnailViewer.forceRendering()||this.printing||this.onIdle&&(this.idleTimeout=setTimeout(this.onIdle.bind(this),a))}},{key:"getHighestPriority",value:function(e,t,n){var i=e.views,r=i.length;if(0===r)return!1;for(var a=0;a<r;++a){var o=i[a].view;if(!this.isViewFinished(o))return o}if(n){var s=e.last.id;if(t[s]&&!this.isViewFinished(t[s]))return t[s]}else{var u=e.first.id-2;if(t[u]&&!this.isViewFinished(t[u]))return t[u]}return null}},{key:"isViewFinished",value:function(e){return e.renderingState===o.FINISHED}},{key:"renderView",value:function(e){var t=this;switch(e.renderingState){case o.FINISHED:return!1;case o.PAUSED:this.highestPriorityPage=e.renderingId,e.resume();break;case o.RUNNING:this.highestPriorityPage=e.renderingId;break;case o.INITIAL:this.highestPriorityPage=e.renderingId;var n=function(){t.renderHighestPriority()};e.draw().then(n,n);break}return!0}}]),e}();t.RenderingStates=o,t.PDFRenderingQueue=s},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PDFPrintServiceFactory=t.DefaultExternalServices=t.PDFViewerApplication=void 0;var i=function(){function e(e,t){var n=[],i=!0,r=!1,a=void 0;try{for(var o,s=e[Symbol.iterator]();!(i=(o=s.next()).done);i=!0)if(n.push(o.value),t&&n.length===t)break}catch(u){r=!0,a=u}finally{try{!i&&s["return"]&&s["return"]()}finally{if(r)throw a}}return n}return function(t,n){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return e(t,n);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),r=n(0),a=n(1),o=n(6),s=n(3),u=n(24),l=n(27),c=n(2),h=n(15),d=n(16),f=n(17),v=n(18),g=n(19),p=n(7),m=n(20),w=n(5),b=n(21),y=n(23),P=n(26),S=n(29),k=n(31),C=n(32),_=1.1,L=5e3;function E(e){e.imageResourcesPath="./images/",e.workerSrc="../build/pdf.worker.js",e.cMapUrl="../web/cmaps/",e.cMapPacked=!0}var T={updateFindControlState:function(e){},initPassiveLoading:function(e){},fallback:function(e,t){},reportTelemetry:function(e){},createDownloadManager:function(){throw new Error("Not implemented: createDownloadManager")},createPreferences:function(){throw new Error("Not implemented: createPreferences")},createL10n:function(){throw new Error("Not implemented: createL10n")},supportsIntegratedFind:!1,supportsDocumentFonts:!0,supportsDocumentColors:!0,supportedMouseWheelZoomModifierKeys:{ctrlKey:!0,metaKey:!0}},I={initialBookmark:document.location.hash.substring(1),initialDestination:null,initialized:!1,fellback:!1,appConfig:null,pdfDocument:null,pdfLoadingTask:null,printService:null,pdfViewer:null,pdfThumbnailViewer:null,pdfRenderingQueue:null,pdfPresentationMode:null,pdfDocumentProperties:null,pdfLinkService:null,pdfHistory:null,pdfSidebar:null,pdfOutlineViewer:null,pdfAttachmentViewer:null,pdfCursorTools:null,store:null,downloadManager:null,overlayManager:null,preferences:null,toolbar:null,secondaryToolbar:null,eventBus:null,l10n:null,isInitialViewSet:!1,downloadComplete:!1,viewerPrefs:{sidebarViewOnLoad:u.SidebarView.NONE,pdfBugEnabled:!1,showPreviousViewOnLoad:!0,defaultZoomValue:"",disablePageMode:!1,disablePageLabels:!1,renderer:"canvas",enhanceTextSelection:!1,renderInteractiveForms:!1,enablePrintAutoRotate:!1},isViewerEmbedded:window.parent!==window,url:"",baseUrl:"",externalServices:T,_boundEvents:{},initialize:function(e){var t=this;return this.preferences=this.externalServices.createPreferences(),E(a.PDFJS),this.appConfig=e,this._readPreferences().then((function(){return t._initializeL10n()})).then((function(){return t._initializeViewerComponents()})).then((function(){t.bindEvents(),t.bindWindowEvents();var n=e.appContainer||document.documentElement;t.l10n.translate(n).then((function(){t.eventBus.dispatch("localized")})),t.isViewerEmbedded&&!a.PDFJS.isExternalLinkTargetSet()&&(a.PDFJS.externalLinkTarget=a.PDFJS.LinkTarget.TOP),t.initialized=!0}))},_readPreferences:function(){var e=this.preferences,t=this.viewerPrefs;return Promise.all([e.get("enableWebGL").then((function(e){a.PDFJS.disableWebGL=!e})),e.get("sidebarViewOnLoad").then((function(e){t["sidebarViewOnLoad"]=e})),e.get("pdfBugEnabled").then((function(e){t["pdfBugEnabled"]=e})),e.get("showPreviousViewOnLoad").then((function(e){t["showPreviousViewOnLoad"]=e})),e.get("defaultZoomValue").then((function(e){t["defaultZoomValue"]=e})),e.get("enhanceTextSelection").then((function(e){t["enhanceTextSelection"]=e})),e.get("disableTextLayer").then((function(e){!0!==a.PDFJS.disableTextLayer&&(a.PDFJS.disableTextLayer=e)})),e.get("disableRange").then((function(e){!0!==a.PDFJS.disableRange&&(a.PDFJS.disableRange=e)})),e.get("disableStream").then((function(e){!0!==a.PDFJS.disableStream&&(a.PDFJS.disableStream=e)})),e.get("disableAutoFetch").then((function(e){a.PDFJS.disableAutoFetch=e})),e.get("disableFontFace").then((function(e){!0!==a.PDFJS.disableFontFace&&(a.PDFJS.disableFontFace=e)})),e.get("useOnlyCssZoom").then((function(e){a.PDFJS.useOnlyCssZoom=e})),e.get("externalLinkTarget").then((function(e){a.PDFJS.isExternalLinkTargetSet()||(a.PDFJS.externalLinkTarget=e)})),e.get("renderer").then((function(e){t["renderer"]=e})),e.get("renderInteractiveForms").then((function(e){t["renderInteractiveForms"]=e})),e.get("disablePageMode").then((function(e){t["disablePageMode"]=e})),e.get("disablePageLabels").then((function(e){t["disablePageLabels"]=e})),e.get("enablePrintAutoRotate").then((function(e){t["enablePrintAutoRotate"]=e}))]).catch((function(e){}))},_initializeL10n:function(){if(this.viewerPrefs["pdfBugEnabled"]){var e=document.location.hash.substring(1),t=(0,r.parseQueryString)(e);"locale"in t&&(a.PDFJS.locale=t["locale"])}return this.l10n=this.externalServices.createL10n(),this.l10n.getDirection().then((function(e){document.getElementsByTagName("html")[0].dir=e}))},_initializeViewerComponents:function(){var e=this,t=this.appConfig;return new Promise((function(n,i){e.overlayManager=new h.OverlayManager;var r=t.eventBus||(0,c.getGlobalEventBus)();e.eventBus=r;var a=new s.PDFRenderingQueue;a.onIdle=e.cleanup.bind(e),e.pdfRenderingQueue=a;var C=new w.PDFLinkService({eventBus:r});e.pdfLinkService=C;var _=e.externalServices.createDownloadManager();e.downloadManager=_;var L=t.mainContainer,E=t.viewerContainer;e.pdfViewer=new l.PDFViewer({container:L,viewer:E,eventBus:r,renderingQueue:a,linkService:C,downloadManager:_,renderer:e.viewerPrefs["renderer"],l10n:e.l10n,enhanceTextSelection:e.viewerPrefs["enhanceTextSelection"],renderInteractiveForms:e.viewerPrefs["renderInteractiveForms"],enablePrintAutoRotate:e.viewerPrefs["enablePrintAutoRotate"]}),a.setViewer(e.pdfViewer),C.setViewer(e.pdfViewer);var T=t.sidebar.thumbnailView;e.pdfThumbnailViewer=new P.PDFThumbnailViewer({container:T,renderingQueue:a,linkService:C,l10n:e.l10n}),a.setThumbnailViewer(e.pdfThumbnailViewer),e.pdfHistory=new m.PDFHistory({linkService:C,eventBus:r}),C.setHistory(e.pdfHistory),e.findController=new p.PDFFindController({pdfViewer:e.pdfViewer}),e.findController.onUpdateResultsCount=function(t){e.supportsIntegratedFind||e.findBar.updateResultsCount(t)},e.findController.onUpdateState=function(t,n,i){e.supportsIntegratedFind?e.externalServices.updateFindControlState({result:t,findPrevious:n}):e.findBar.updateUIState(t,n,i)},e.pdfViewer.setFindController(e.findController);var I=Object.create(t.findBar);I.findController=e.findController,I.eventBus=r,e.findBar=new g.PDFFindBar(I,e.l10n),e.pdfDocumentProperties=new v.PDFDocumentProperties(t.documentProperties,e.overlayManager,e.l10n),e.pdfCursorTools=new o.PDFCursorTools({container:L,eventBus:r,preferences:e.preferences}),e.toolbar=new k.Toolbar(t.toolbar,L,r,e.l10n),e.secondaryToolbar=new S.SecondaryToolbar(t.secondaryToolbar,L,r),e.supportsFullscreen&&(e.pdfPresentationMode=new y.PDFPresentationMode({container:L,viewer:E,pdfViewer:e.pdfViewer,eventBus:r,contextMenuItems:t.fullscreen})),e.passwordPrompt=new d.PasswordPrompt(t.passwordOverlay,e.overlayManager,e.l10n),e.pdfOutlineViewer=new b.PDFOutlineViewer({container:t.sidebar.outlineView,eventBus:r,linkService:C}),e.pdfAttachmentViewer=new f.PDFAttachmentViewer({container:t.sidebar.attachmentsView,eventBus:r,downloadManager:_});var B=Object.create(t.sidebar);B.pdfViewer=e.pdfViewer,B.pdfThumbnailViewer=e.pdfThumbnailViewer,B.pdfOutlineViewer=e.pdfOutlineViewer,B.eventBus=r,e.pdfSidebar=new u.PDFSidebar(B,e.l10n),e.pdfSidebar.onToggled=e.forceRendering.bind(e),n(void 0)}))},run:function(e){this.initialize(e).then(F)},zoomIn:function(e){var t=this.pdfViewer.currentScale;do{t=(t*_).toFixed(2),t=Math.ceil(10*t)/10,t=Math.min(r.MAX_SCALE,t)}while(--e>0&&t<r.MAX_SCALE);this.pdfViewer.currentScaleValue=t},zoomOut:function(e){var t=this.pdfViewer.currentScale;do{t=(t/_).toFixed(2),t=Math.floor(10*t)/10,t=Math.max(r.MIN_SCALE,t)}while(--e>0&&t>r.MIN_SCALE);this.pdfViewer.currentScaleValue=t},get pagesCount(){return this.pdfDocument?this.pdfDocument.numPages:0},get pageRotation(){return this.pdfViewer.pagesRotation},set page(e){this.pdfViewer.currentPageNumber=e},get page(){return this.pdfViewer.currentPageNumber},get printing(){return!!this.printService},get supportsPrinting(){return pe.instance.supportsPrinting},get supportsFullscreen(){var e=void 0,t=document.documentElement;return e=!!(t.requestFullscreen||t.mozRequestFullScreen||t.webkitRequestFullScreen||t.msRequestFullscreen),!1!==document.fullscreenEnabled&&!1!==document.mozFullScreenEnabled&&!1!==document.webkitFullscreenEnabled&&!1!==document.msFullscreenEnabled||(e=!1),e&&!0===a.PDFJS.disableFullscreen&&(e=!1),(0,a.shadow)(this,"supportsFullscreen",e)},get supportsIntegratedFind(){return this.externalServices.supportsIntegratedFind},get supportsDocumentFonts(){return this.externalServices.supportsDocumentFonts},get supportsDocumentColors(){return this.externalServices.supportsDocumentColors},get loadingBar(){var e=new r.ProgressBar("#loadingBar");return(0,a.shadow)(this,"loadingBar",e)},get supportedMouseWheelZoomModifierKeys(){return this.externalServices.supportedMouseWheelZoomModifierKeys},initPassiveLoading:function(){throw new Error("Not implemented: initPassiveLoading")},setTitleUsingUrl:function(e){this.url=e,this.baseUrl=e.split("#")[0];var t=(0,r.getPDFFileNameFromURL)(e,"");if(!t)try{t=decodeURIComponent((0,a.getFilenameFromUrl)(e))||e}catch(n){t=e}this.setTitle(t)},setTitle:function(e){this.isViewerEmbedded||(document.title=e)},close:function(){var e=this.appConfig.errorWrapper.container;if(e.setAttribute("hidden","true"),!this.pdfLoadingTask)return Promise.resolve();var t=this.pdfLoadingTask.destroy();return this.pdfLoadingTask=null,this.pdfDocument&&(this.pdfDocument=null,this.pdfThumbnailViewer.setDocument(null),this.pdfViewer.setDocument(null),this.pdfLinkService.setDocument(null,null),this.pdfDocumentProperties.setDocument(null,null)),this.store=null,this.isInitialViewSet=!1,this.downloadComplete=!1,this.pdfSidebar.reset(),this.pdfOutlineViewer.reset(),this.pdfAttachmentViewer.reset(),this.findController.reset(),this.findBar.reset(),this.toolbar.reset(),this.secondaryToolbar.reset(),"undefined"!==typeof PDFBug&&PDFBug.cleanup(),t},open:function(e,t){var n=this;if(arguments.length>2||"number"===typeof t)return Promise.reject(new Error("Call of open() with obsolete signature."));if(this.pdfLoadingTask)return this.close().then((function(){return n.preferences.reload(),n.open(e,t)}));var i=Object.create(null),r=void 0;if("string"===typeof e?(this.setTitleUsingUrl(e),i.url=e):e&&"byteLength"in e?i.data=e:e.url&&e.originalUrl&&(this.setTitleUsingUrl(e.originalUrl),i.url=e.url),t){for(var o in t)i[o]=t[o];t.scale&&(r=t.scale),t.length&&this.pdfDocumentProperties.setFileSize(t.length)}var s=(0,a.getDocument)(i);return this.pdfLoadingTask=s,s.onPassword=function(e,t){n.passwordPrompt.setUpdateCallback(e,t),n.passwordPrompt.open()},s.onProgress=function(e){var t=e.loaded,i=e.total;n.progress(t/i)},s.onUnsupportedFeature=this.fallback.bind(this),s.promise.then((function(e){n.load(e,r)}),(function(e){var t=e&&e.message,i=void 0,r=void 0;return e instanceof a.InvalidPDFException?(r="invalid_file_error",i=n.l10n.get("invalid_file_error",null,"Invalid or corrupted PDF file.")):e instanceof a.MissingPDFException?(r="missing_file_error",i=n.l10n.get("missing_file_error",null,"Missing PDF file.")):e instanceof a.UnexpectedResponseException?(r="unexpected_response_error",i=n.l10n.get("unexpected_response_error",null,"Unexpected server response.")):(r="loading_error",i=n.l10n.get("loading_error",null,"An error occurred while loading the PDF.")),i.then((function(e){n.error(e,{message:t});let i=document.location.href.split("?")[1],a={};if(i){i=i.replace(/#\//g,"");let e=i.split("&");for(let t=0;t<e.length;t++)a[e[t].split("=")[0]]=decodeURIComponent(e[t].split("=")[1])}throw window.parent.cacheBus.emit(r,r,e,a.fileUrl),new Error(e)}))}))},download:function(e){e&&!e.endsWith(".pdf")&&(e+=".pdf");var t=this;function n(){s.downloadUrl(i,o)}var i=this.baseUrl,o=(0,r.getPDFFileNameFromURL)(this.url,e),s=this.downloadManager;s.onerror=function(e){t.error("PDF failed to download: "+e)},this.pdfDocument&&this.downloadComplete?this.pdfDocument.getData().then((function(e){var t=(0,a.createBlob)(e,"application/pdf");s.download(t,i,o)})).catch(n):n()},fallback:function(e){},error:function(e,t){var n=[this.l10n.get("error_version_info",{version:a.version||"?",build:a.build||"?"},"PDF.js v{{version}} (build: {{build}})")];t&&(n.push(this.l10n.get("error_message",{message:t.message},"Message: {{message}}")),t.stack?n.push(this.l10n.get("error_stack",{stack:t.stack},"Stack: {{stack}}")):(t.filename&&n.push(this.l10n.get("error_file",{file:t.filename},"File: {{file}}")),t.lineNumber&&n.push(this.l10n.get("error_line",{line:t.lineNumber},"Line: {{line}}"))));var i=this.appConfig.errorWrapper,o=i.container;o.removeAttribute("hidden");var s=i.errorMessage;s.textContent=e;var u=i.closeButton;u.onclick=function(){o.setAttribute("hidden","true")};var l=i.errorMoreInfo,c=i.moreInfoButton,h=i.lessInfoButton;c.onclick=function(){l.removeAttribute("hidden"),c.setAttribute("hidden","true"),h.removeAttribute("hidden"),l.style.height=l.scrollHeight+"px"},h.onclick=function(){l.setAttribute("hidden","true"),c.removeAttribute("hidden"),h.setAttribute("hidden","true")},c.oncontextmenu=r.noContextMenuHandler,h.oncontextmenu=r.noContextMenuHandler,u.oncontextmenu=r.noContextMenuHandler,c.removeAttribute("hidden"),h.setAttribute("hidden","true"),Promise.all(n).then((function(e){l.value=e.join("\n")}))},progress:function(e){var t=this;if(!this.downloadComplete){var n=Math.round(100*e);(n>this.loadingBar.percent||isNaN(n))&&(this.loadingBar.percent=n,a.PDFJS.disableAutoFetch&&n&&(this.disableAutoFetchLoadingBarTimeout&&(clearTimeout(this.disableAutoFetchLoadingBarTimeout),this.disableAutoFetchLoadingBarTimeout=null),this.loadingBar.show(),this.disableAutoFetchLoadingBarTimeout=setTimeout((function(){t.loadingBar.hide(),t.disableAutoFetchLoadingBarTimeout=null}),L)))}},load:function(e,t){var n=this;t=t||r.UNKNOWN_SCALE,this.pdfDocument=e,e.getDownloadInfo().then((function(){n.downloadComplete=!0,n.loadingBar.hide(),d.then((function(){n.eventBus.dispatch("documentload",{source:n})}))}));var o=e.getPageMode().catch((function(){}));this.toolbar.setPagesCount(e.numPages,!1),this.secondaryToolbar.setPagesCount(e.numPages);var s=this.documentFingerprint=e.fingerprint,l=this.store=new C.ViewHistory(s),c=void 0;c=null,this.pdfLinkService.setDocument(e,c),this.pdfDocumentProperties.setDocument(e,this.url);var h=this.pdfViewer;h.setDocument(e);var d=h.firstPagePromise,f=h.pagesPromise,v=h.onePageRendered,g=this.pdfThumbnailViewer;g.setDocument(e),d.then((function(e){n.loadingBar.setWidth(n.appConfig.viewerContainer),a.PDFJS.disableHistory||n.isViewerEmbedded||(n.viewerPrefs["showPreviousViewOnLoad"]||n.pdfHistory.clearHistoryState(),n.pdfHistory.initialize(n.documentFingerprint),n.pdfHistory.initialDestination?n.initialDestination=n.pdfHistory.initialDestination:n.pdfHistory.initialBookmark&&(n.initialBookmark=n.pdfHistory.initialBookmark));var s={destination:n.initialDestination,bookmark:n.initialBookmark,hash:null},c=l.getMultiple({exists:!1,page:"1",zoom:r.DEFAULT_SCALE_VALUE,scrollLeft:"0",scrollTop:"0",sidebarView:u.SidebarView.NONE}).catch((function(){}));Promise.all([c,o]).then((function(e){var t=i(e,2),r=t[0],a=void 0===r?{}:r,o=t[1],s=n.viewerPrefs["defaultZoomValue"]?"zoom="+n.viewerPrefs["defaultZoomValue"]:null,u=n.viewerPrefs["sidebarViewOnLoad"];return a.exists&&n.viewerPrefs["showPreviousViewOnLoad"]&&(s="page="+a.page+"&zoom="+(n.viewerPrefs["defaultZoomValue"]||a.zoom)+","+a.scrollLeft+","+a.scrollTop,u=u||0|a.sidebarView),o&&!n.viewerPrefs["disablePageMode"]&&(u=u||ge(o)),{hash:s,sidebarView:u}})).then((function(e){var i=e.hash,r=e.sidebarView;return n.setInitialView(i,{sidebarView:r,scale:t}),s.hash=i,n.isViewerEmbedded||h.focus(),f})).then((function(){(s.destination||s.bookmark||s.hash)&&(h.hasEqualPageSizes||(n.initialDestination=s.destination,n.initialBookmark=s.bookmark,h.currentScaleValue=h.currentScaleValue,n.setInitialView(s.hash)))})).then((function(){h.update()}))})),e.getPageLabels().then((function(t){if(t&&!n.viewerPrefs["disablePageLabels"]){var i=0,r=t.length;if(r===n.pagesCount){while(i<r&&t[i]===(i+1).toString())i++;i!==r&&(h.setPageLabels(t),g.setPageLabels(t),n.toolbar.setPagesCount(e.numPages,!0),n.toolbar.setPageNumber(h.currentPageNumber,h.currentPageLabel))}}})),f.then((function(){n.supportsPrinting&&e.getJavaScript().then((function(e){e.length&&n.fallback(a.UNSUPPORTED_FEATURES.javaScript);for(var t=/\bprint\s*\(/,i=0,r=e.length;i<r;i++){var o=e[i];if(o&&t.test(o))return void setTimeout((function(){window.print()}))}}))})),Promise.all([v,r.animationStarted]).then((function(){e.getOutline().then((function(e){n.pdfOutlineViewer.render({outline:e})})),e.getAttachments().then((function(e){n.pdfAttachmentViewer.render({attachments:e})}))})),e.getMetadata().then((function(e){var t=e.info,i=e.metadata;n.documentInfo=t,n.metadata=i;var r=void 0;if(i&&i.has("dc:title")){var o=i.get("dc:title");"Untitled"!==o&&(r=o)}!r&&t&&t["Title"]&&(r=t["Title"]),r&&n.setTitle(r+" - "+document.title),t.IsAcroFormPresent&&n.fallback(a.UNSUPPORTED_FEATURES.forms)}))},setInitialView:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.scale,i=void 0===n?0:n,a=t.sidebarView,o=void 0===a?u.SidebarView.NONE:a;this.isInitialViewSet=!0,this.pdfSidebar.setInitialView(o),this.initialDestination?(this.pdfLinkService.navigateTo(this.initialDestination),this.initialDestination=null):this.initialBookmark?(this.pdfLinkService.setHash(this.initialBookmark),this.pdfHistory.push({hash:this.initialBookmark},!0),this.initialBookmark=null):e?this.pdfLinkService.setHash(e):i&&(this.pdfViewer.currentScaleValue=i,this.page=1),this.toolbar.setPageNumber(this.pdfViewer.currentPageNumber,this.pdfViewer.currentPageLabel),this.secondaryToolbar.setPageNumber(this.pdfViewer.currentPageNumber),this.pdfViewer.currentScaleValue||(this.pdfViewer.currentScaleValue=r.DEFAULT_SCALE_VALUE)},cleanup:function(){this.pdfDocument&&(this.pdfViewer.cleanup(),this.pdfThumbnailViewer.cleanup(),this.pdfViewer.renderer!==r.RendererType.SVG&&this.pdfDocument.cleanup())},forceRendering:function(){this.pdfRenderingQueue.printing=this.printing,this.pdfRenderingQueue.isThumbnailViewEnabled=this.pdfSidebar.isThumbnailViewVisible,this.pdfRenderingQueue.renderHighestPriority()},beforePrint:function(){var e=this;if(!this.printService)if(this.supportsPrinting)if(this.pdfViewer.pageViewsReady){var t=this.pdfViewer.getPagesOverview(),n=this.appConfig.printContainer,i=pe.instance.createPrintService(this.pdfDocument,t,n,this.l10n);this.printService=i,this.forceRendering(),i.layout()}else this.l10n.get("printing_not_ready",null,"Warning: The PDF is not fully loaded for printing.").then((function(e){window.alert(e)}));else this.l10n.get("printing_not_supported",null,"Warning: Printing is not fully supported by this browser.").then((function(t){e.error(t)}))},afterPrint:function(){this.printService&&(this.printService.destroy(),this.printService=null),this.forceRendering()},rotatePages:function(e){if(this.pdfDocument){var t=this.pdfViewer,n=this.pdfThumbnailViewer,i=t.currentPageNumber,r=(t.pagesRotation+360+e)%360;t.pagesRotation=r,n.pagesRotation=r,this.forceRendering(),t.currentPageNumber=i}},requestPresentationMode:function(){this.pdfPresentationMode&&this.pdfPresentationMode.request()},bindEvents:function(){var e=this.eventBus,t=this._boundEvents;t.beforePrint=this.beforePrint.bind(this),t.afterPrint=this.afterPrint.bind(this),e.on("resize",z),e.on("hashchange",j),e.on("beforeprint",t.beforePrint),e.on("afterprint",t.afterPrint),e.on("pagerendered",V),e.on("textlayerrendered",M),e.on("updateviewarea",H),e.on("pagechanging",le),e.on("scalechanging",ue),e.on("sidebarviewchanged",U),e.on("pagemode",O),e.on("namedaction",A),e.on("presentationmodechanged",R),e.on("presentationmode",J),e.on("openfile",q),e.on("print",G),e.on("download",Q),e.on("firstpage",Z),e.on("lastpage",K),e.on("nextpage",X),e.on("previouspage",Y),e.on("zoomin",$),e.on("zoomout",ee),e.on("pagenumberchanged",te),e.on("scalechanged",ne),e.on("rotatecw",ie),e.on("rotateccw",re),e.on("documentproperties",ae),e.on("find",oe),e.on("findfromurlhash",se),e.on("fileinputchange",W)},bindWindowEvents:function(){var e=this.eventBus,t=this._boundEvents;t.windowResize=function(){e.dispatch("resize")},t.windowHashChange=function(){e.dispatch("hashchange",{hash:document.location.hash.substring(1)})},t.windowBeforePrint=function(){e.dispatch("beforeprint")},t.windowAfterPrint=function(){e.dispatch("afterprint")},window.addEventListener("wheel",de),window.addEventListener("click",fe),window.addEventListener("keydown",ve),window.addEventListener("resize",t.windowResize),window.addEventListener("hashchange",t.windowHashChange),window.addEventListener("beforeprint",t.windowBeforePrint),window.addEventListener("afterprint",t.windowAfterPrint),t.windowChange=function(t){var n=t.target.files;n&&0!==n.length&&e.dispatch("fileinputchange",{fileInput:t.target})},window.addEventListener("change",t.windowChange)},unbindEvents:function(){var e=this.eventBus,t=this._boundEvents;e.off("resize",z),e.off("hashchange",j),e.off("beforeprint",t.beforePrint),e.off("afterprint",t.afterPrint),e.off("pagerendered",V),e.off("textlayerrendered",M),e.off("updateviewarea",H),e.off("pagechanging",le),e.off("scalechanging",ue),e.off("sidebarviewchanged",U),e.off("pagemode",O),e.off("namedaction",A),e.off("presentationmodechanged",R),e.off("presentationmode",J),e.off("openfile",q),e.off("print",G),e.off("download",Q),e.off("firstpage",Z),e.off("lastpage",K),e.off("nextpage",X),e.off("previouspage",Y),e.off("zoomin",$),e.off("zoomout",ee),e.off("pagenumberchanged",te),e.off("scalechanged",ne),e.off("rotatecw",ie),e.off("rotateccw",re),e.off("documentproperties",ae),e.off("find",oe),e.off("findfromurlhash",se),e.off("fileinputchange",W),t.beforePrint=null,t.afterPrint=null},unbindWindowEvents:function(){var e=this._boundEvents;window.removeEventListener("wheel",de),window.removeEventListener("click",fe),window.removeEventListener("keydown",ve),window.removeEventListener("resize",e.windowResize),window.removeEventListener("hashchange",e.windowHashChange),window.removeEventListener("beforeprint",e.windowBeforePrint),window.removeEventListener("afterprint",e.windowAfterPrint),window.removeEventListener("change",e.windowChange),e.windowChange=null,e.windowResize=null,e.windowHashChange=null,e.windowBeforePrint=null,e.windowAfterPrint=null}},B=void 0,x=["null","http://mozilla.github.io","https://mozilla.github.io"];function D(e){return new Promise((function(t,n){var i=I.appConfig,r=document.createElement("script");r.src=i.debuggerScriptPath,r.onload=function(){PDFBug.enable(e),PDFBug.init({PDFJS:a.PDFJS,OPS:a.OPS},i.mainContainer),t()},r.onerror=function(){n(new Error("Cannot load debugger at "+r.src))},(document.getElementsByTagName("head")[0]||document.body).appendChild(r)}))}function F(){var e=I.appConfig,t=void 0,n=document.location.search.substring(1),i=(0,r.parseQueryString)(n);t="file"in i?i.file:e.defaultUrl,B(t);var o=[],s=document.createElement("input");if(s.id=e.openFileInputName,s.className="fileInput",s.setAttribute("type","file"),s.oncontextmenu=r.noContextMenuHandler,document.body.appendChild(s),window.File&&window.FileReader&&window.FileList&&window.Blob?s.value=null:(e.toolbar.openFile.setAttribute("hidden","true"),e.secondaryToolbar.openFileButton.setAttribute("hidden","true")),I.viewerPrefs["pdfBugEnabled"]){var u=document.location.hash.substring(1),l=(0,r.parseQueryString)(u);if("disableworker"in l&&(a.PDFJS.disableWorker="true"===l["disableworker"]),"disablerange"in l&&(a.PDFJS.disableRange="true"===l["disablerange"]),"disablestream"in l&&(a.PDFJS.disableStream="true"===l["disablestream"]),"disableautofetch"in l&&(a.PDFJS.disableAutoFetch="true"===l["disableautofetch"]),"disablefontface"in l&&(a.PDFJS.disableFontFace="true"===l["disablefontface"]),"disablehistory"in l&&(a.PDFJS.disableHistory="true"===l["disablehistory"]),"webgl"in l&&(a.PDFJS.disableWebGL="true"!==l["webgl"]),"useonlycsszoom"in l&&(a.PDFJS.useOnlyCssZoom="true"===l["useonlycsszoom"]),"verbosity"in l&&(a.PDFJS.verbosity=0|l["verbosity"]),"ignorecurrentpositiononzoom"in l&&(a.PDFJS.ignoreCurrentPositionOnZoom="true"===l["ignorecurrentpositiononzoom"]),"textlayer"in l)switch(l["textlayer"]){case"off":a.PDFJS.disableTextLayer=!0;break;case"visible":case"shadow":case"hover":var c=e.viewerContainer;c.classList.add("textLayer-"+l["textlayer"]);break}if("pdfbug"in l){a.PDFJS.pdfBug=!0;var h=l["pdfbug"],d=h.split(",");o.push(D(d))}}I.supportsPrinting||(e.toolbar.print.classList.add("hidden"),e.secondaryToolbar.printButton.classList.add("hidden")),I.supportsFullscreen||(e.toolbar.presentationModeButton.classList.add("hidden"),e.secondaryToolbar.presentationModeButton.classList.add("hidden")),I.supportsIntegratedFind&&e.toolbar.viewFind.classList.add("hidden"),e.sidebar.mainContainer.addEventListener("transitionend",(function(e){e.target===this&&I.eventBus.dispatch("resize")}),!0),e.sidebar.toggleButton.addEventListener("click",(function(){I.pdfSidebar.toggle()})),Promise.all(o).then((function(){N(t)})).catch((function(e){I.l10n.get("loading_error",null,"An error occurred while opening.").then((function(t){I.error(t,e)}))}))}B=function(e){if(void 0!==e)try{var t=new URL(window.location.href).origin||"null";if(x.indexOf(t)>=0)return;var n=new URL(e,window.location.href).origin;if(n!==t)throw new Error("file origin does not match viewer's")}catch(r){var i=r&&r.message;throw I.l10n.get("loading_error",null,"An error occurred while loading the PDF.").then((function(e){I.error(e,{message:i})})),r}};var N=void 0;function V(e){var t=e.pageNumber,n=t-1,i=I.pdfViewer.getPageView(n);if(t===I.page&&I.toolbar.updateLoadingIndicatorState(!1),i){if(I.pdfSidebar.isThumbnailViewVisible){var r=I.pdfThumbnailViewer.getThumbnail(n);r.setImage(i)}a.PDFJS.pdfBug&&Stats.enabled&&i.stats&&Stats.add(t,i.stats),i.error&&I.l10n.get("rendering_error",null,"An error occurred while rendering the page.").then((function(e){I.error(e,i.error)}))}}function M(e){}function O(e){var t=e.mode,n=void 0;switch(t){case"thumbs":n=u.SidebarView.THUMBS;break;case"bookmarks":case"outline":n=u.SidebarView.OUTLINE;break;case"attachments":n=u.SidebarView.ATTACHMENTS;break;case"none":n=u.SidebarView.NONE;break;default:return}I.pdfSidebar.switchView(n,!0)}function A(e){var t=e.action;switch(t){case"GoToPage":I.appConfig.toolbar.pageNumber.select();break;case"Find":I.supportsIntegratedFind||I.findBar.toggle();break}}function R(e){var t=e.active,n=e.switchInProgress;I.pdfViewer.presentationModeState=n?l.PresentationModeState.CHANGING:t?l.PresentationModeState.FULLSCREEN:l.PresentationModeState.NORMAL}function U(e){I.pdfRenderingQueue.isThumbnailViewEnabled=I.pdfSidebar.isThumbnailViewVisible;var t=I.store;t&&I.isInitialViewSet&&t.set("sidebarView",e.view).catch((function(){}))}function H(e){var t=e.location,n=I.store;n&&I.isInitialViewSet&&n.setMultiple({exists:!0,page:t.pageNumber,zoom:t.scale,scrollLeft:t.left,scrollTop:t.top}).catch((function(){}));var i=I.pdfLinkService.getAnchorUrl(t.pdfOpenParams);I.appConfig.toolbar.viewBookmark.href=i,I.appConfig.secondaryToolbar.viewBookmarkButton.href=i,I.pdfHistory.updateCurrentBookmark(t.pdfOpenParams,t.pageNumber);var r=I.pdfViewer.getPageView(I.page-1),a=r.renderingState!==s.RenderingStates.FINISHED;I.toolbar.updateLoadingIndicatorState(a)}function z(){var e=I.pdfDocument,t=I.pdfViewer;if(e){var n=t.currentScaleValue;"auto"!==n&&"page-fit"!==n&&"page-width"!==n||(t.currentScaleValue=n),t.update()}}function j(e){if(I.pdfHistory.isHashChangeUnlocked){var t=e.hash;if(!t)return;I.isInitialViewSet?I.pdfLinkService.setHash(t):I.initialBookmark=t}}N=function(e){if(e&&0===e.lastIndexOf("file:",0)){I.setTitleUsingUrl(e);var t=new XMLHttpRequest;t.onload=function(){I.open(new Uint8Array(t.response))};try{t.open("GET",e),t.responseType="arraybuffer",t.send()}catch(n){I.l10n.get("loading_error",null,"An error occurred while loading the PDF.").then((function(e){I.error(e,n)}))}}else e&&I.open(e)};var W=void 0;function J(){I.requestPresentationMode()}function q(){var e=I.appConfig.openFileInputName;document.getElementById(e).click()}function G(){window.print()}function Q(){I.download()}function Z(){I.pdfDocument&&(I.page=1)}function K(){I.pdfDocument&&(I.page=I.pagesCount)}function X(){I.page++}function Y(){I.page--}function $(){I.zoomIn()}function ee(){I.zoomOut()}function te(e){var t=I.pdfViewer;t.currentPageLabel=e.value,e.value!==t.currentPageNumber.toString()&&e.value!==t.currentPageLabel&&I.toolbar.setPageNumber(t.currentPageNumber,t.currentPageLabel)}function ne(e){I.pdfViewer.currentScaleValue=e.value}function ie(){I.rotatePages(90)}function re(){I.rotatePages(-90)}function ae(){I.pdfDocumentProperties.open()}function oe(e){I.findController.executeCommand("find"+e.type,{query:e.query,phraseSearch:e.phraseSearch,caseSensitive:e.caseSensitive,highlightAll:e.highlightAll,findPrevious:e.findPrevious})}function se(e){I.findController.executeCommand("find",{query:e.query,phraseSearch:e.phraseSearch,caseSensitive:!1,highlightAll:!0,findPrevious:!1})}function ue(e){I.toolbar.setPageScale(e.presetValue,e.scale),I.pdfViewer.update()}function le(e){var t=e.pageNumber;if(I.toolbar.setPageNumber(t,e.pageLabel||null),I.secondaryToolbar.setPageNumber(t),I.pdfSidebar.isThumbnailViewVisible&&I.pdfThumbnailViewer.scrollThumbnailIntoView(t),a.PDFJS.pdfBug&&Stats.enabled){var n=I.pdfViewer.getPageView(t-1);n.stats&&Stats.add(t,n.stats)}}W=function(e){var t=e.fileInput.files[0];if(!a.PDFJS.disableCreateObjectURL&&URL.createObjectURL)I.open(URL.createObjectURL(t));else{var n=new FileReader;n.onload=function(e){var t=e.target.result;I.open(new Uint8Array(t))},n.readAsArrayBuffer(t)}I.setTitleUsingUrl(t.name);var i=I.appConfig;i.toolbar.viewBookmark.setAttribute("hidden","true"),i.secondaryToolbar.viewBookmarkButton.setAttribute("hidden","true"),i.toolbar.download.setAttribute("hidden","true"),i.secondaryToolbar.downloadButton.setAttribute("hidden","true")};var ce=!1,he=void 0;function de(e){var t=I.pdfViewer;if(!t.isInPresentationMode)if(e.ctrlKey||e.metaKey){var n=I.supportedMouseWheelZoomModifierKeys;if(e.ctrlKey&&!n.ctrlKey||e.metaKey&&!n.metaKey)return;if(e.preventDefault(),ce)return;var i=t.currentScale,a=(0,r.normalizeWheelEventDelta)(e),o=3,s=a*o;s<0?I.zoomOut(-s):I.zoomIn(s);var u=t.currentScale;if(i!==u){var l=u/i-1,c=t.container.getBoundingClientRect(),h=e.clientX-c.left,d=e.clientY-c.top;t.container.scrollLeft+=h*l,t.container.scrollTop+=d*l}}else ce=!0,clearTimeout(he),he=setTimeout((function(){ce=!1}),1e3)}function fe(e){if(I.secondaryToolbar.isOpen){var t=I.appConfig;(I.pdfViewer.containsElement(e.target)||t.toolbar.container.contains(e.target)&&e.target!==t.secondaryToolbar.toggleButton)&&I.secondaryToolbar.close()}}function ve(e){if(!I.overlayManager.active){var t=!1,n=!1,i=(e.ctrlKey?1:0)|(e.altKey?2:0)|(e.shiftKey?4:0)|(e.metaKey?8:0),a=I.pdfViewer,s=a&&a.isInPresentationMode;if(1===i||8===i||5===i||12===i)switch(e.keyCode){case 70:I.supportsIntegratedFind||(I.findBar.open(),t=!0);break;case 71:if(!I.supportsIntegratedFind){var u=I.findController.state;u&&I.findController.executeCommand("findagain",{query:u.query,phraseSearch:u.phraseSearch,caseSensitive:u.caseSensitive,highlightAll:u.highlightAll,findPrevious:5===i||12===i}),t=!0}break;case 61:case 107:case 187:case 171:s||I.zoomIn(),t=!0;break;case 173:case 109:case 189:s||I.zoomOut(),t=!0;break;case 48:case 96:s||(setTimeout((function(){a.currentScaleValue=r.DEFAULT_SCALE_VALUE})),t=!1);break;case 38:(s||I.page>1)&&(I.page=1,t=!0,n=!0);break;case 40:(s||I.page<I.pagesCount)&&(I.page=I.pagesCount,t=!0,n=!0);break}if(1===i||8===i)switch(e.keyCode){case 83:I.download(),t=!0;break}if(3===i||10===i)switch(e.keyCode){case 80:I.requestPresentationMode(),t=!0;break;case 71:I.appConfig.toolbar.pageNumber.select(),t=!0;break}if(t)return n&&!s&&a.focus(),void e.preventDefault();var l=document.activeElement||document.querySelector(":focus"),c=l&&l.tagName.toUpperCase();if("INPUT"!==c&&"TEXTAREA"!==c&&"SELECT"!==c||27===e.keyCode){if(0===i)switch(e.keyCode){case 38:case 33:case 8:if(!s&&"page-fit"!==a.currentScaleValue)break;case 37:if(a.isHorizontalScrollbarEnabled)break;case 75:case 80:I.page>1&&I.page--,t=!0;break;case 27:I.secondaryToolbar.isOpen&&(I.secondaryToolbar.close(),t=!0),!I.supportsIntegratedFind&&I.findBar.opened&&(I.findBar.close(),t=!0);break;case 40:case 34:case 32:if(!s&&"page-fit"!==a.currentScaleValue)break;case 39:if(a.isHorizontalScrollbarEnabled)break;case 74:case 78:I.page<I.pagesCount&&I.page++,t=!0;break;case 36:(s||I.page>1)&&(I.page=1,t=!0,n=!0);break;case 35:(s||I.page<I.pagesCount)&&(I.page=I.pagesCount,t=!0,n=!0);break;case 83:I.pdfCursorTools.switchTool(o.CursorTool.SELECT);break;case 72:I.pdfCursorTools.switchTool(o.CursorTool.HAND);break;case 82:I.rotatePages(90);break}if(4===i)switch(e.keyCode){case 32:if(!s&&"page-fit"!==a.currentScaleValue)break;I.page>1&&I.page--,t=!0;break;case 82:I.rotatePages(-90);break}if(t||s||(e.keyCode>=33&&e.keyCode<=40||32===e.keyCode&&"BUTTON"!==c)&&(n=!0),2===i)switch(e.keyCode){case 37:s&&(I.pdfHistory.back(),t=!0);break;case 39:s&&(I.pdfHistory.forward(),t=!0);break}n&&!a.containsElement(l)&&a.focus(),t&&e.preventDefault()}}}function ge(e){switch(e){case"UseNone":return u.SidebarView.NONE;case"UseThumbs":return u.SidebarView.THUMBS;case"UseOutlines":return u.SidebarView.OUTLINE;case"UseAttachments":return u.SidebarView.ATTACHMENTS;case"UseOC":}return u.SidebarView.NONE}var pe={instance:{supportsPrinting:!1,createPrintService:function(){throw new Error("Not implemented: createPrintService")}}};t.PDFViewerApplication=I,t.DefaultExternalServices=T,t.PDFPrintServiceFactory=pe},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SimpleLinkService=t.PDFLinkService=void 0;var i="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}(),a=n(2),o=n(0);function s(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var u=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.eventBus;s(this,e),this.eventBus=n||(0,a.getGlobalEventBus)(),this.baseUrl=null,this.pdfDocument=null,this.pdfViewer=null,this.pdfHistory=null,this._pagesRefCache=null}return r(e,[{key:"setDocument",value:function(e,t){this.baseUrl=t,this.pdfDocument=e,this._pagesRefCache=Object.create(null)}},{key:"setViewer",value:function(e){this.pdfViewer=e}},{key:"setHistory",value:function(e){this.pdfHistory=e}},{key:"navigateTo",value:function(e){var t=this,n=function e(n){var i=n.namedDest,r=n.explicitDest,a=r[0],o=void 0;if(a instanceof Object){if(o=t._cachedPageNumber(a),null===o)return void t.pdfDocument.getPageIndex(a).then((function(n){t.cachePageRef(n+1,a),e({namedDest:i,explicitDest:r})})).catch((function(){}))}else{if((0|a)!==a)return;o=a+1}!o||o<1||o>t.pagesCount||(t.pdfViewer.scrollPageIntoView({pageNumber:o,destArray:r}),t.pdfHistory&&t.pdfHistory.push({dest:r,hash:i,page:o}))};new Promise((function(n,i){"string"!==typeof e?n({namedDest:"",explicitDest:e}):t.pdfDocument.getDestination(e).then((function(t){n({namedDest:e,explicitDest:t})}))})).then((function(e){e.explicitDest instanceof Array&&n(e)}))}},{key:"getDestinationHash",value:function(e){if("string"===typeof e)return this.getAnchorUrl("#"+escape(e));if(e instanceof Array){var t=JSON.stringify(e);return this.getAnchorUrl("#"+escape(t))}return this.getAnchorUrl("")}},{key:"getAnchorUrl",value:function(e){return(this.baseUrl||"")+e}},{key:"setHash",value:function(e){var t=void 0,n=void 0;if(e.indexOf("=")>=0){var i=(0,o.parseQueryString)(e);if("search"in i&&this.eventBus.dispatch("findfromurlhash",{source:this,query:i["search"].replace(/"/g,""),phraseSearch:"true"===i["phrase"]}),"nameddest"in i)return this.pdfHistory&&this.pdfHistory.updateNextHashParam(i.nameddest),void this.navigateTo(i.nameddest);if("page"in i&&(t=0|i.page||1),"zoom"in i){var r=i.zoom.split(","),a=r[0],s=parseFloat(a);-1===a.indexOf("Fit")?n=[null,{name:"XYZ"},r.length>1?0|r[1]:null,r.length>2?0|r[2]:null,s?s/100:a]:"Fit"===a||"FitB"===a?n=[null,{name:a}]:"FitH"===a||"FitBH"===a||"FitV"===a||"FitBV"===a?n=[null,{name:a},r.length>1?0|r[1]:null]:"FitR"===a&&(5!==r.length||(n=[null,{name:a},0|r[1],0|r[2],0|r[3],0|r[4]]))}n?this.pdfViewer.scrollPageIntoView({pageNumber:t||this.page,destArray:n,allowNegativeOffset:!0}):t&&(this.page=t),"pagemode"in i&&this.eventBus.dispatch("pagemode",{source:this,mode:i.pagemode})}else{/^\d+$/.test(e)&&e<=this.pagesCount&&(this.page=0|e),n=unescape(e);try{n=JSON.parse(n),n instanceof Array||(n=n.toString())}catch(u){}if("string"===typeof n||l(n))return this.pdfHistory&&this.pdfHistory.updateNextHashParam(n),void this.navigateTo(n)}}},{key:"executeNamedAction",value:function(e){switch(e){case"GoBack":this.pdfHistory&&this.pdfHistory.back();break;case"GoForward":this.pdfHistory&&this.pdfHistory.forward();break;case"NextPage":this.page<this.pagesCount&&this.page++;break;case"PrevPage":this.page>1&&this.page--;break;case"LastPage":this.page=this.pagesCount;break;case"FirstPage":this.page=1;break;default:break}this.eventBus.dispatch("namedaction",{source:this,action:e})}},{key:"onFileAttachmentAnnotation",value:function(e){var t=e.id,n=e.filename,i=e.content;this.eventBus.dispatch("fileattachmentannotation",{source:this,id:t,filename:n,content:i})}},{key:"cachePageRef",value:function(e,t){var n=t.num+" "+t.gen+" R";this._pagesRefCache[n]=e}},{key:"_cachedPageNumber",value:function(e){var t=e.num+" "+e.gen+" R";return this._pagesRefCache&&this._pagesRefCache[t]||null}},{key:"pagesCount",get:function(){return this.pdfDocument?this.pdfDocument.numPages:0}},{key:"page",get:function(){return this.pdfViewer.currentPageNumber},set:function(e){this.pdfViewer.currentPageNumber=e}}]),e}();function l(e){if(!(e instanceof Array))return!1;var t=e.length,n=!0;if(t<2)return!1;var r=e[0];if(("object"!==("undefined"===typeof r?"undefined":i(r))||"number"!==typeof r.num||(0|r.num)!==r.num||"number"!==typeof r.gen||(0|r.gen)!==r.gen)&&!("number"===typeof r&&(0|r)===r&&r>=0))return!1;var a=e[1];if("object"!==("undefined"===typeof a?"undefined":i(a))||"string"!==typeof a.name)return!1;switch(a.name){case"XYZ":if(5!==t)return!1;break;case"Fit":case"FitB":return 2===t;case"FitH":case"FitBH":case"FitV":case"FitBV":if(3!==t)return!1;break;case"FitR":if(6!==t)return!1;n=!1;break;default:return!1}for(var o=2;o<t;o++){var s=e[o];if(!("number"===typeof s||n&&null===s))return!1}return!0}var c=function(){function e(){s(this,e)}return r(e,[{key:"navigateTo",value:function(e){}},{key:"getDestinationHash",value:function(e){return"#"}},{key:"getAnchorUrl",value:function(e){return"#"}},{key:"setHash",value:function(e){}},{key:"executeNamedAction",value:function(e){}},{key:"onFileAttachmentAnnotation",value:function(e){e.id,e.filename,e.content}},{key:"cachePageRef",value:function(e,t){}},{key:"page",get:function(){return 0},set:function(e){}}]),e}();t.PDFLinkService=u,t.SimpleLinkService=c},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PDFCursorTools=t.CursorTool=void 0;var i=function(){function e(e,t){var n=[],i=!0,r=!1,a=void 0;try{for(var o,s=e[Symbol.iterator]();!(i=(o=s.next()).done);i=!0)if(n.push(o.value),t&&n.length===t)break}catch(u){r=!0,a=u}finally{try{!i&&s["return"]&&s["return"]()}finally{if(r)throw a}}return n}return function(t,n){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return e(t,n);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),r=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}(),a=n(14);function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var s={SELECT:0,HAND:1,ZOOM:2},u=function(){function e(t){var n=this,r=t.container,u=t.eventBus,l=t.preferences;o(this,e),this.container=r,this.eventBus=u,this.active=s.SELECT,this.activeBeforePresentationMode=null,this.handTool=new a.GrabToPan({element:this.container}),this._addEventListeners(),Promise.all([l.get("cursorToolOnLoad"),l.get("enableHandToolOnLoad")]).then((function(e){var t=i(e,2),r=t[0],a=t[1];!0===a&&(l.set("enableHandToolOnLoad",!1),r===s.SELECT&&(r=s.HAND,l.set("cursorToolOnLoad",r).catch((function(){})))),n.switchTool(r)})).catch((function(){}))}return r(e,[{key:"switchTool",value:function(e){var t=this;if(null===this.activeBeforePresentationMode&&e!==this.active){var n=function(){switch(t.active){case s.SELECT:break;case s.HAND:t.handTool.deactivate();break;case s.ZOOM:}};switch(e){case s.SELECT:n();break;case s.HAND:n(),this.handTool.activate();break;case s.ZOOM:default:return}this.active=e,this._dispatchEvent()}}},{key:"_dispatchEvent",value:function(){this.eventBus.dispatch("cursortoolchanged",{source:this,tool:this.active})}},{key:"_addEventListeners",value:function(){var e=this;this.eventBus.on("switchcursortool",(function(t){e.switchTool(t.tool)})),this.eventBus.on("presentationmodechanged",(function(t){if(!t.switchInProgress){var n=void 0;t.active?(n=e.active,e.switchTool(s.SELECT),e.activeBeforePresentationMode=n):(n=e.activeBeforePresentationMode,e.activeBeforePresentationMode=null,e.switchTool(n))}}))}},{key:"activeTool",get:function(){return this.active}}]),e}();t.CursorTool=s,t.PDFCursorTools=u},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PDFFindController=t.FindState=void 0;var i=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}(),r=n(1),a=n(0);function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var s={FOUND:0,NOT_FOUND:1,WRAPPED:2,PENDING:3},u=-50,l=-400,c=250,h={"‘":"'","’":"'","‚":"'","‛":"'","“":'"',"”":'"',"„":'"',"‟":'"',"¼":"1/4","½":"1/2","¾":"3/4"},d=function(){function e(t){var n=t.pdfViewer;o(this,e),this.pdfViewer=n,this.onUpdateResultsCount=null,this.onUpdateState=null,this.reset();var i=Object.keys(h).join("");this.normalizationRegex=new RegExp("["+i+"]","g")}return i(e,[{key:"reset",value:function(){var e=this;this.startedTextExtraction=!1,this.extractTextPromises=[],this.pendingFindMatches=Object.create(null),this.active=!1,this.pageContents=[],this.pageMatches=[],this.pageMatchesLength=null,this.matchCount=0,this.selected={pageIdx:-1,matchIdx:-1},this.offset={pageIdx:null,matchIdx:null},this.pagesToSearch=null,this.resumePageIdx=null,this.state=null,this.dirtyMatch=!1,this.findTimeout=null,this._firstPagePromise=new Promise((function(t){e.resolveFirstPage=t}))}},{key:"normalize",value:function(e){return e.replace(this.normalizationRegex,(function(e){return h[e]}))}},{key:"_prepareMatches",value:function(e,t,n){function i(e,t){var n=e[t],i=e[t+1];if(t<e.length-1&&n.match===i.match)return n.skipped=!0,!0;for(var r=t-1;r>=0;r--){var a=e[r];if(!a.skipped){if(a.match+a.matchLength<n.match)break;if(a.match+a.matchLength>=n.match+n.matchLength)return n.skipped=!0,!0}}return!1}e.sort((function(e,t){return e.match===t.match?e.matchLength-t.matchLength:e.match-t.match}));for(var r=0,a=e.length;r<a;r++)i(e,r)||(t.push(e[r].match),n.push(e[r].matchLength))}},{key:"calcFindPhraseMatch",value:function(e,t,n){var i=[],r=e.length,a=-r;while(1){if(a=n.indexOf(e,a+r),-1===a)break;i.push(a)}this.pageMatches[t]=i}},{key:"calcFindWordMatch",value:function(e,t,n){for(var i=[],r=e.match(/\S+/g),a=0,o=r.length;a<o;a++){var s=r[a],u=s.length,l=-u;while(1){if(l=n.indexOf(s,l+u),-1===l)break;i.push({match:l,matchLength:u,skipped:!1})}}this.pageMatchesLength||(this.pageMatchesLength=[]),this.pageMatchesLength[t]=[],this.pageMatches[t]=[],this._prepareMatches(i,this.pageMatches[t],this.pageMatchesLength[t])}},{key:"calcFindMatch",value:function(e){var t=this.normalize(this.pageContents[e]),n=this.normalize(this.state.query),i=this.state.caseSensitive,r=this.state.phraseSearch,a=n.length;0!==a&&(i||(t=t.toLowerCase(),n=n.toLowerCase()),r?this.calcFindPhraseMatch(n,e,t):this.calcFindWordMatch(n,e,t),this.updatePage(e),this.resumePageIdx===e&&(this.resumePageIdx=null,this.nextPageMatch()),this.pageMatches[e].length>0&&(this.matchCount+=this.pageMatches[e].length,this.updateUIResultsCount()))}},{key:"extractText",value:function(){var e=this;if(!this.startedTextExtraction){this.startedTextExtraction=!0,this.pageContents.length=0;for(var t=Promise.resolve(),n=function(n,i){var a=(0,r.createPromiseCapability)();e.extractTextPromises[n]=a.promise,t=t.then((function(){return e.pdfViewer.getPageTextContent(n).then((function(t){for(var i=t.items,r=[],o=0,s=i.length;o<s;o++)r.push(i[o].str);e.pageContents[n]=r.join(""),a.resolve(n)}),(function(t){e.pageContents[n]="",a.resolve(n)}))}))},i=0,a=this.pdfViewer.pagesCount;i<a;i++)n(i,a)}}},{key:"executeCommand",value:function(e,t){var n=this;null!==this.state&&"findagain"===e||(this.dirtyMatch=!0),this.state=t,this.updateUIState(s.PENDING),this._firstPagePromise.then((function(){n.extractText(),clearTimeout(n.findTimeout),"find"===e?n.findTimeout=setTimeout(n.nextMatch.bind(n),c):n.nextMatch()}))}},{key:"updatePage",value:function(e){this.selected.pageIdx===e&&(this.pdfViewer.currentPageNumber=e+1);var t=this.pdfViewer.getPageView(e);t.textLayer&&t.textLayer.updateMatches()}},{key:"nextMatch",value:function(){var e=this,t=this.state.findPrevious,n=this.pdfViewer.currentPageNumber-1,i=this.pdfViewer.pagesCount;if(this.active=!0,this.dirtyMatch){this.dirtyMatch=!1,this.selected.pageIdx=this.selected.matchIdx=-1,this.offset.pageIdx=n,this.offset.matchIdx=null,this.hadMatch=!1,this.resumePageIdx=null,this.pageMatches=[],this.matchCount=0,this.pageMatchesLength=null;for(var r=0;r<i;r++)this.updatePage(r),r in this.pendingFindMatches||(this.pendingFindMatches[r]=!0,this.extractTextPromises[r].then((function(t){delete e.pendingFindMatches[t],e.calcFindMatch(t)})))}if(""!==this.state.query){if(!this.resumePageIdx){var a=this.offset;if(this.pagesToSearch=i,null!==a.matchIdx){var o=this.pageMatches[a.pageIdx].length;if(!t&&a.matchIdx+1<o||t&&a.matchIdx>0)return this.hadMatch=!0,a.matchIdx=t?a.matchIdx-1:a.matchIdx+1,void this.updateMatch(!0);this.advanceOffsetPage(t)}this.nextPageMatch()}}else this.updateUIState(s.FOUND)}},{key:"matchesReady",value:function(e){var t=this.offset,n=e.length,i=this.state.findPrevious;return n?(this.hadMatch=!0,t.matchIdx=i?n-1:0,this.updateMatch(!0),!0):(this.advanceOffsetPage(i),!!(t.wrapped&&(t.matchIdx=null,this.pagesToSearch<0))&&(this.updateMatch(!1),!0))}},{key:"updateMatchPosition",value:function(e,t,n,i){if(this.selected.matchIdx===t&&this.selected.pageIdx===e){var r={top:u,left:l};(0,a.scrollIntoView)(n[i],r,!0)}}},{key:"nextPageMatch",value:function(){this.resumePageIdx;var e=null;do{var t=this.offset.pageIdx;if(e=this.pageMatches[t],!e){this.resumePageIdx=t;break}}while(!this.matchesReady(e))}},{key:"advanceOffsetPage",value:function(e){var t=this.offset,n=this.extractTextPromises.length;t.pageIdx=e?t.pageIdx-1:t.pageIdx+1,t.matchIdx=null,this.pagesToSearch--,(t.pageIdx>=n||t.pageIdx<0)&&(t.pageIdx=e?n-1:0,t.wrapped=!0)}},{key:"updateMatch",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=s.NOT_FOUND,n=this.offset.wrapped;if(this.offset.wrapped=!1,e){var i=this.selected.pageIdx;this.selected.pageIdx=this.offset.pageIdx,this.selected.matchIdx=this.offset.matchIdx,t=n?s.WRAPPED:s.FOUND,-1!==i&&i!==this.selected.pageIdx&&this.updatePage(i)}this.updateUIState(t,this.state.findPrevious),-1!==this.selected.pageIdx&&this.updatePage(this.selected.pageIdx)}},{key:"updateUIResultsCount",value:function(){this.onUpdateResultsCount&&this.onUpdateResultsCount(this.matchCount)}},{key:"updateUIState",value:function(e,t){this.onUpdateState&&this.onUpdateState(e,t,this.matchCount)}}]),e}();t.FindState=s,t.PDFFindController=d},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.GenericCom=void 0;var i=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}(),r=n(4),a=n(28),o=n(12),s=n(13),u=n(1);function l(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function c(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!==typeof t&&"function"!==typeof t?e:t}function h(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}var d={},f=function(e){function t(){return l(this,t),c(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return h(t,e),i(t,[{key:"_writeToStorage",value:function(e){return new Promise((function(t){localStorage.setItem("pdfjs.preferences",JSON.stringify(e)),t()}))}},{key:"_readFromStorage",value:function(e){return new Promise((function(e){var t=JSON.parse(localStorage.getItem("pdfjs.preferences"));e(t)}))}}]),t}(a.BasePreferences),v=Object.create(r.DefaultExternalServices);v.createDownloadManager=function(){return new o.DownloadManager},v.createPreferences=function(){return new f},v.createL10n=function(){return new s.GenericL10n(u.PDFJS.locale)},r.PDFViewerApplication.externalServices=v,t.GenericCom=d},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PDFPrintService=void 0;var i=n(0),r=n(4),a=n(1),o=null,s=null;function u(e,t,n,r){var a=o.scratchCanvas,s=400,u=s/72;a.width=Math.floor(r.width*u),a.height=Math.floor(r.height*u);var l=Math.floor(r.width*i.CSS_UNITS)+"px",c=Math.floor(r.height*i.CSS_UNITS)+"px",h=a.getContext("2d");return h.save(),h.fillStyle="rgb(255, 255, 255)",h.fillRect(0,0,a.width,a.height),h.restore(),t.getPage(n).then((function(e){var t={canvasContext:h,transform:[u,0,0,u,0,0],viewport:e.getViewport(1,r.rotation),intent:"print"};return e.render(t).promise})).then((function(){return{width:l,height:c}}))}function l(e,t,n,r){this.pdfDocument=e,this.pagesOverview=t,this.printContainer=n,this.l10n=r||i.NullL10n,this.currentPage=-1,this.scratchCanvas=document.createElement("canvas")}l.prototype={layout:function(){this.throwIfInactive();var e=document.querySelector("body");e.setAttribute("data-pdfjsprinting",!0);this.pagesOverview.every((function(e){return e.width===this.pagesOverview[0].width&&e.height===this.pagesOverview[0].height}),this);this.pageStyleSheet=document.createElement("style");var t=this.pagesOverview[0];this.pageStyleSheet.textContent="@supports ((size:A4) and (size:1pt 1pt)) {@page { size: "+t.width+"pt "+t.height+"pt;}}",e.appendChild(this.pageStyleSheet)},destroy:function(){o===this&&(this.printContainer.textContent="",this.pageStyleSheet&&this.pageStyleSheet.parentNode&&(this.pageStyleSheet.parentNode.removeChild(this.pageStyleSheet),this.pageStyleSheet=null),this.scratchCanvas.width=this.scratchCanvas.height=0,this.scratchCanvas=null,o=null,m().then((function(){"printServiceOverlay"===s.active&&s.close("printServiceOverlay")})))},renderPages:function(){var e=this,t=this.pagesOverview.length,n=function n(i,r){if(e.throwIfInactive(),++e.currentPage>=t)return f(t,t,e.l10n),void i();var a=e.currentPage;f(a,t,e.l10n),u(e,e.pdfDocument,a+1,e.pagesOverview[a]).then(e.useRenderedPage.bind(e)).then((function(){n(i,r)}),r)};return new Promise(n)},useRenderedPage:function(e){this.throwIfInactive();var t=document.createElement("img");t.style.width=e.width,t.style.height=e.height;var n=this.scratchCanvas;"toBlob"in n&&!a.PDFJS.disableCreateObjectURL?n.toBlob((function(e){t.src=URL.createObjectURL(e)})):t.src=n.toDataURL();var i=document.createElement("div");return i.appendChild(t),this.printContainer.appendChild(i),new Promise((function(e,n){t.onload=e,t.onerror=n}))},performPrint:function(){var e=this;return this.throwIfInactive(),new Promise((function(t){setTimeout((function(){e.active?(c.call(window),setTimeout(t,20)):t()}),0)}))},get active(){return this===o},throwIfInactive:function(){if(!this.active)throw new Error("This print request was cancelled or completed.")}};var c=window.print;function h(e){var t=document.createEvent("CustomEvent");t.initCustomEvent(e,!1,!1,"custom"),window.dispatchEvent(t)}function d(){o&&(o.destroy(),h("afterprint"))}function f(e,t,n){var i=document.getElementById("printServiceOverlay"),r=Math.round(100*e/t),a=i.querySelector("progress"),o=i.querySelector(".relative-progress");a.value=r,n.get("print_progress_percent",{progress:r},r+"%").then((function(e){o.textContent=e}))}window.print=function(){if(!o){m().then((function(){o&&s.open("printServiceOverlay")}));try{h("beforeprint")}finally{if(!o)return void m().then((function(){"printServiceOverlay"===s.active&&s.close("printServiceOverlay")}));var e=o;o.renderPages().then((function(){return e.performPrint()})).catch((function(){})).then((function(){e.active&&d()}))}}};var v,g=!!document.attachEvent;if(window.addEventListener("keydown",(function(e){if(80===e.keyCode&&(e.ctrlKey||e.metaKey)&&!e.altKey&&(!e.shiftKey||window.chrome||window.opera)){if(window.print(),g)return;return e.preventDefault(),void(e.stopImmediatePropagation?e.stopImmediatePropagation():e.stopPropagation())}}),!0),g&&document.attachEvent("onkeydown",(function(e){if(e=e||window.event,80===e.keyCode&&e.ctrlKey)return e.keyCode=0,!1})),"onbeforeprint"in window){var p=function(e){"custom"!==e.detail&&e.stopImmediatePropagation&&e.stopImmediatePropagation()};window.addEventListener("beforeprint",p),window.addEventListener("afterprint",p)}function m(){if(!v){if(s=r.PDFViewerApplication.overlayManager,!s)throw new Error("The overlay manager has not yet been initialized.");v=s.register("printServiceOverlay",document.getElementById("printServiceOverlay"),d,!0),document.getElementById("printCancel").onclick=d}return v}r.PDFPrintServiceFactory.instance={supportsPrinting:!0,createPrintService:function(e,t,n,i){if(o)throw new Error("The print service is created and active.");return o=new l(e,t,n,i),o}},t.PDFPrintService=l},function(e,t,n){"use strict";document.webL10n=function(e,t,n){var i={},r="",a="textContent",o="",s={},u="loading",l=!0;function c(){return t.querySelectorAll('link[type="application/l10n"]')}function h(){var e=t.querySelector('script[type="application/l10n"]');return e?JSON.parse(e.innerHTML):null}function d(e){return e?e.querySelectorAll("*[data-l10n-id]"):[]}function f(e){if(!e)return{};var t=e.getAttribute("data-l10n-id"),n=e.getAttribute("data-l10n-args"),i={};if(n)try{i=JSON.parse(n)}catch(r){}return{id:t,args:i}}function v(e){var n=t.createEvent("Event");n.initEvent("localized",!0,!1),n.language=e,t.dispatchEvent(n)}function g(e,t,n){t=t||function(e){},n=n||function(){};var i=new XMLHttpRequest;i.open("GET",e,l),i.overrideMimeType&&i.overrideMimeType("text/plain; charset=utf-8"),i.onreadystatechange=function(){4==i.readyState&&(200==i.status||0===i.status?t(i.responseText):n())},i.onerror=n,i.ontimeout=n;try{i.send(null)}catch(r){n()}}function p(e,t,n,o){var s=e.replace(/[^\/]*$/,"")||"./";function u(e){return e.lastIndexOf("\\")<0?e:e.replace(/\\\\/g,"\\").replace(/\\n/g,"\n").replace(/\\r/g,"\r").replace(/\\t/g,"\t").replace(/\\b/g,"\b").replace(/\\f/g,"\f").replace(/\\{/g,"{").replace(/\\}/g,"}").replace(/\\"/g,'"').replace(/\\'/g,"'")}function l(e,n){var i={},r=/^\s*|\s*$/,a=/^\s*#|^\s*$/,o=/^\s*\[(.*)\]\s*$/,l=/^\s*@import\s+url\((.*)\)\s*$/i,c=/^([^=\s]*)\s*=\s*(.+)$/;function h(e,n,h){var f=e.replace(r,"").split(/[\r\n]+/),v="*",g=t.split("-",1)[0],p=!1,m="";function w(){while(1){if(!f.length)return void h();var e=f.shift();if(!a.test(e)){if(n){if(m=o.exec(e),m){v=m[1].toLowerCase(),p="*"!==v&&v!==t&&v!==g;continue}if(p)continue;if(m=l.exec(e),m)return void d(s+m[1],w)}var r=e.match(c);r&&3==r.length&&(i[r[1]]=u(r[2]))}}}w()}function d(e,t){g(e,(function(e){h(e,!1,t)}),(function(){t()}))}h(e,!0,(function(){n(i)}))}g(e,(function(e){r+=e,l(e,(function(e){for(var t in e){var r,o,s=t.lastIndexOf(".");s>0?(r=t.substring(0,s),o=t.substr(s+1)):(r=t,o=a),i[r]||(i[r]={}),i[r][o]=e[t]}n&&n()}))}),o)}function m(e,t){e&&(e=e.toLowerCase()),t=t||function(){},w(),o=e;var n=c(),r=n.length;if(0===r){var a=h();if(a&&a.locales&&a.default_locale){if(i=a.locales[e],!i){var s=a.default_locale.toLowerCase();for(var l in a.locales){if(l=l.toLowerCase(),l===e){i=a.locales[e];break}l===s&&(i=a.locales[s])}}t()}return v(e),void(u="complete")}var d=null,f=0;function g(e){var t=e.href;this.load=function(e,n){p(t,e,n,(function(){o="",n()}))}}d=function(){f++,f>=r&&(t(),v(e),u="complete")};for(var m=0;m<r;m++){var b=new g(n[m]);b.load(e,d)}}function w(){i={},r="",o=""}function b(e){var t={af:3,ak:4,am:4,ar:1,asa:3,az:0,be:11,bem:3,bez:3,bg:3,bh:4,bm:0,bn:3,bo:0,br:20,brx:3,bs:11,ca:3,cgg:3,chr:3,cs:12,cy:17,da:3,de:3,dv:3,dz:0,ee:3,el:3,en:3,eo:3,es:3,et:3,eu:3,fa:0,ff:5,fi:3,fil:4,fo:3,fr:5,fur:3,fy:3,ga:8,gd:24,gl:3,gsw:3,gu:3,guw:4,gv:23,ha:3,haw:3,he:2,hi:4,hr:11,hu:0,id:0,ig:0,ii:0,is:3,it:3,iu:7,ja:0,jmc:3,jv:0,ka:0,kab:5,kaj:3,kcg:3,kde:0,kea:0,kk:3,kl:3,km:0,kn:0,ko:0,ksb:3,ksh:21,ku:3,kw:7,lag:18,lb:3,lg:3,ln:4,lo:0,lt:10,lv:6,mas:3,mg:4,mk:16,ml:3,mn:3,mo:9,mr:3,ms:0,mt:15,my:0,nah:3,naq:7,nb:3,nd:3,ne:3,nl:3,nn:3,no:3,nr:3,nso:4,ny:3,nyn:3,om:3,or:3,pa:3,pap:3,pl:13,ps:3,pt:3,rm:3,ro:9,rof:3,ru:11,rwk:3,sah:0,saq:3,se:7,seh:3,ses:0,sg:0,sh:11,shi:19,sk:12,sl:14,sma:7,smi:7,smj:7,smn:7,sms:7,sn:3,so:3,sq:3,sr:11,ss:3,ssy:3,st:3,sv:3,sw:3,syr:3,ta:3,te:3,teo:3,th:0,ti:4,tig:3,tk:3,tl:4,tn:3,to:0,tr:0,ts:3,tzm:22,uk:11,ur:3,ve:3,vi:0,vun:3,wa:4,wae:3,wo:0,xh:3,xog:3,yo:0,zh:0,zu:3};function n(e,t){return-1!==t.indexOf(e)}function i(e,t,n){return t<=e&&e<=n}var r={0:function(e){return"other"},1:function(e){return i(e%100,3,10)?"few":0===e?"zero":i(e%100,11,99)?"many":2==e?"two":1==e?"one":"other"},2:function(e){return 0!==e&&e%10===0?"many":2==e?"two":1==e?"one":"other"},3:function(e){return 1==e?"one":"other"},4:function(e){return i(e,0,1)?"one":"other"},5:function(e){return i(e,0,2)&&2!=e?"one":"other"},6:function(e){return 0===e?"zero":e%10==1&&e%100!=11?"one":"other"},7:function(e){return 2==e?"two":1==e?"one":"other"},8:function(e){return i(e,3,6)?"few":i(e,7,10)?"many":2==e?"two":1==e?"one":"other"},9:function(e){return 0===e||1!=e&&i(e%100,1,19)?"few":1==e?"one":"other"},10:function(e){return i(e%10,2,9)&&!i(e%100,11,19)?"few":e%10!=1||i(e%100,11,19)?"other":"one"},11:function(e){return i(e%10,2,4)&&!i(e%100,12,14)?"few":e%10===0||i(e%10,5,9)||i(e%100,11,14)?"many":e%10==1&&e%100!=11?"one":"other"},12:function(e){return i(e,2,4)?"few":1==e?"one":"other"},13:function(e){return i(e%10,2,4)&&!i(e%100,12,14)?"few":1!=e&&i(e%10,0,1)||i(e%10,5,9)||i(e%100,12,14)?"many":1==e?"one":"other"},14:function(e){return i(e%100,3,4)?"few":e%100==2?"two":e%100==1?"one":"other"},15:function(e){return 0===e||i(e%100,2,10)?"few":i(e%100,11,19)?"many":1==e?"one":"other"},16:function(e){return e%10==1&&11!=e?"one":"other"},17:function(e){return 3==e?"few":0===e?"zero":6==e?"many":2==e?"two":1==e?"one":"other"},18:function(e){return 0===e?"zero":i(e,0,2)&&0!==e&&2!=e?"one":"other"},19:function(e){return i(e,2,10)?"few":i(e,0,1)?"one":"other"},20:function(e){return!i(e%10,3,4)&&e%10!=9||i(e%100,10,19)||i(e%100,70,79)||i(e%100,90,99)?e%1e6===0&&0!==e?"many":e%10!=2||n(e%100,[12,72,92])?e%10!=1||n(e%100,[11,71,91])?"other":"one":"two":"few"},21:function(e){return 0===e?"zero":1==e?"one":"other"},22:function(e){return i(e,0,1)||i(e,11,99)?"one":"other"},23:function(e){return i(e%10,1,2)||e%20===0?"one":"other"},24:function(e){return i(e,3,10)||i(e,13,19)?"few":n(e,[2,12])?"two":n(e,[1,11])?"one":"other"}},a=t[e.replace(/-.*$/,"")];return a in r?r[a]:function(){return"other"}}function y(e,t,n){var r=i[e];if(!r){if(!n)return null;r=n}var a={};for(var o in r){var s=r[o];s=P(s,t,e,o),s=S(s,t,e),a[o]=s}return a}function P(e,t,n,r){var a=/\{\[\s*([a-zA-Z]+)\(([a-zA-Z]+)\)\s*\]\}/,o=a.exec(e);if(!o||!o.length)return e;var u,l=o[1],c=o[2];if(t&&c in t?u=t[c]:c in i&&(u=i[c]),l in s){var h=s[l];e=h(e,u,n,r)}return e}function S(e,t,n){var r=/\{\{\s*(.+?)\s*\}\}/g;return e.replace(r,(function(e,n){return t&&n in t?t[n]:n in i?i[n]:e}))}function k(e){var n=f(e);if(n.id){var i=y(n.id,n.args);if(i){if(i[a]){if(0===C(e))e[a]=i[a];else{for(var r=e.childNodes,o=!1,s=0,u=r.length;s<u;s++)3===r[s].nodeType&&/\S/.test(r[s].nodeValue)&&(o?r[s].nodeValue="":(r[s].nodeValue=i[a],o=!0));if(!o){var l=t.createTextNode(i[a]);e.insertBefore(l,e.firstChild)}}delete i[a]}for(var c in i)e[c]=i[c]}}}function C(e){if(e.children)return e.children.length;if("undefined"!==typeof e.childElementCount)return e.childElementCount;for(var t=0,n=0;n<e.childNodes.length;n++)t+=1===e.nodeType?1:0;return t}function _(e){e=e||t.documentElement;for(var n=d(e),i=n.length,r=0;r<i;r++)k(n[r]);k(e)}return s.plural=function(e,t,n,r){var u=parseFloat(t);if(isNaN(u))return e;if(r!=a)return e;s._pluralRules||(s._pluralRules=b(o));var l="["+s._pluralRules(u)+"]";return 0===u&&n+"[zero]"in i?e=i[n+"[zero]"][r]:1==u&&n+"[one]"in i?e=i[n+"[one]"][r]:2==u&&n+"[two]"in i?e=i[n+"[two]"][r]:n+l in i?e=i[n+l][r]:n+"[other]"in i&&(e=i[n+"[other]"][r]),e},{get:function(e,t,n){var i,r=e.lastIndexOf("."),o=a;r>0&&(o=e.substr(r+1),e=e.substring(0,r)),n&&(i={},i[o]=n);var s=y(e,t,i);return s&&o in s?s[o]:"{{"+e+"}}"},getData:function(){return i},getText:function(){return r},getLanguage:function(){return o},setLanguage:function(e,t){m(e,(function(){t&&t()}))},getDirection:function(){var e=["ar","he","fa","ps","ur"],t=o.split("-",1)[0];return e.indexOf(t)>=0?"rtl":"ltr"},translate:_,getReadyState:function(){return u},ready:function(n){n&&("complete"==u||"interactive"==u?e.setTimeout((function(){n()})):t.addEventListener&&t.addEventListener("localized",(function e(){t.removeEventListener("localized",e),n()})))}}}(window,document)},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DefaultAnnotationLayerFactory=t.AnnotationLayerBuilder=void 0;var i=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}(),r=n(1),a=n(0),o=n(5);function s(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var u=function(){function e(t){var n=t.pageDiv,i=t.pdfPage,r=t.linkService,o=t.downloadManager,u=t.renderInteractiveForms,l=void 0!==u&&u,c=t.l10n,h=void 0===c?a.NullL10n:c;s(this,e),this.pageDiv=n,this.pdfPage=i,this.linkService=r,this.downloadManager=o,this.renderInteractiveForms=l,this.l10n=h,this.div=null}return i(e,[{key:"render",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"display";this.pdfPage.getAnnotations({intent:n}).then((function(n){var i={viewport:e.clone({dontFlip:!0}),div:t.div,annotations:n,page:t.pdfPage,renderInteractiveForms:t.renderInteractiveForms,linkService:t.linkService,downloadManager:t.downloadManager};if(t.div)r.AnnotationLayer.update(i);else{if(0===n.length)return;t.div=document.createElement("div"),t.div.className="annotationLayer",t.pageDiv.appendChild(t.div),i.div=t.div,r.AnnotationLayer.render(i),t.l10n.translate(t.div)}}))}},{key:"hide",value:function(){this.div&&this.div.setAttribute("hidden","true")}}]),e}(),l=function(){function e(){s(this,e)}return i(e,[{key:"createAnnotationLayerBuilder",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:a.NullL10n;return new u({pageDiv:e,pdfPage:t,renderInteractiveForms:n,linkService:new o.SimpleLinkService,l10n:i})}}]),e}();t.AnnotationLayerBuilder=u,t.DefaultAnnotationLayerFactory=l},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DownloadManager=void 0;var i=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}(),r=n(1);function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function o(e,t){var n=document.createElement("a");if(n.click)n.href=e,n.target="_parent","download"in n&&(n.download=t),(document.body||document.documentElement).appendChild(n),n.click(),n.parentNode.removeChild(n);else{if(window.top===window&&e.split("#")[0]===window.location.href.split("#")[0]){var i=-1===e.indexOf("?")?"?":"&";e=e.replace(/#|$/,i+"$&")}window.open(e,"_parent")}}var s=function(){function e(){a(this,e)}return i(e,[{key:"downloadUrl",value:function(e,t){(0,r.createValidAbsoluteUrl)(e,"http://example.com")&&o(e+"#pdfjs.action=download",t)}},{key:"downloadData",value:function(e,t,n){if(navigator.msSaveBlob)return navigator.msSaveBlob(new Blob([e],{type:n}),t);var i=(0,r.createObjectURL)(e,n,r.PDFJS.disableCreateObjectURL);o(i,t)}},{key:"download",value:function(e,t,n){if(navigator.msSaveBlob)navigator.msSaveBlob(e,n)||this.downloadUrl(t,n);else if(r.PDFJS.disableCreateObjectURL)this.downloadUrl(t,n);else{var i=URL.createObjectURL(e);o(i,n)}}}]),e}();t.DownloadManager=s},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.GenericL10n=void 0;var i=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}();function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}n(10);var a=document.webL10n,o=function(){function e(t){r(this,e),this._lang=t,this._ready=new Promise((function(e,n){a.setLanguage(t,(function(){e(a)}))}))}return i(e,[{key:"getDirection",value:function(){return this._ready.then((function(e){return e.getDirection()}))}},{key:"get",value:function(e,t,n){return this._ready.then((function(i){return i.get(e,t,n)}))}},{key:"translate",value:function(e){return this._ready.then((function(t){return t.translate(e)}))}}]),e}();t.GenericL10n=o},function(e,t,n){"use strict";function i(e){this.element=e.element,this.document=e.element.ownerDocument,"function"===typeof e.ignoreTarget&&(this.ignoreTarget=e.ignoreTarget),this.onActiveChanged=e.onActiveChanged,this.activate=this.activate.bind(this),this.deactivate=this.deactivate.bind(this),this.toggle=this.toggle.bind(this),this._onmousedown=this._onmousedown.bind(this),this._onmousemove=this._onmousemove.bind(this),this._endPan=this._endPan.bind(this);var t=this.overlay=document.createElement("div");t.className="grab-to-pan-grabbing"}var r;Object.defineProperty(t,"__esModule",{value:!0}),i.prototype={CSS_CLASS_GRAB:"grab-to-pan-grab",activate:function(){this.active||(this.active=!0,this.element.addEventListener("mousedown",this._onmousedown,!0),this.element.classList.add(this.CSS_CLASS_GRAB),this.onActiveChanged&&this.onActiveChanged(!0))},deactivate:function(){this.active&&(this.active=!1,this.element.removeEventListener("mousedown",this._onmousedown,!0),this._endPan(),this.element.classList.remove(this.CSS_CLASS_GRAB),this.onActiveChanged&&this.onActiveChanged(!1))},toggle:function(){this.active?this.deactivate():this.activate()},ignoreTarget:function(e){return e[r]("a[href], a[href] *, input, textarea, button, button *, select, option")},_onmousedown:function(e){if(0===e.button&&!this.ignoreTarget(e.target)){if(e.originalTarget)try{e.originalTarget.tagName}catch(n){return}this.scrollLeftStart=this.element.scrollLeft,this.scrollTopStart=this.element.scrollTop,this.clientXStart=e.clientX,this.clientYStart=e.clientY,this.document.addEventListener("mousemove",this._onmousemove,!0),this.document.addEventListener("mouseup",this._endPan,!0),this.element.addEventListener("scroll",this._endPan,!0),e.preventDefault(),e.stopPropagation();var t=document.activeElement;t&&!t.contains(e.target)&&t.blur()}},_onmousemove:function(e){if(this.element.removeEventListener("scroll",this._endPan,!0),l(e))this._endPan();else{var t=e.clientX-this.clientXStart,n=e.clientY-this.clientYStart,i=this.scrollTopStart-n,r=this.scrollLeftStart-t;this.element.scrollTo?this.element.scrollTo({top:i,left:r,behavior:"instant"}):(this.element.scrollTop=i,this.element.scrollLeft=r),this.overlay.parentNode||document.body.appendChild(this.overlay)}},_endPan:function(){this.element.removeEventListener("scroll",this._endPan,!0),this.document.removeEventListener("mousemove",this._onmousemove,!0),this.document.removeEventListener("mouseup",this._endPan,!0),this.overlay.remove()}},["webkitM","mozM","msM","oM","m"].some((function(e){var t=e+"atches";return t in document.documentElement&&(r=t),t+="Selector",t in document.documentElement&&(r=t),r}));var a=!document.documentMode||document.documentMode>9,o=window.chrome,s=o&&(o.webstore||o.app),u=/Apple/.test(navigator.vendor)&&/Version\/([6-9]\d*|[1-5]\d+)/.test(navigator.userAgent);function l(e){return"buttons"in e&&a?!(1&e.buttons):s||u?0===e.which:void 0}t.GrabToPan=i},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}();function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var a=function(){function e(){r(this,e),this._overlays={},this._active=null,this._keyDownBound=this._keyDown.bind(this)}return i(e,[{key:"register",value:function(e,t){var n=this,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];return new Promise((function(a){var o=void 0;if(!(e&&t&&(o=t.parentNode)))throw new Error("Not enough parameters.");if(n._overlays[e])throw new Error("The overlay is already registered.");n._overlays[e]={element:t,container:o,callerCloseMethod:i,canForceClose:r},a()}))}},{key:"unregister",value:function(e){var t=this;return new Promise((function(n){if(!t._overlays[e])throw new Error("The overlay does not exist.");if(t._active===e)throw new Error("The overlay cannot be removed while it is active.");delete t._overlays[e],n()}))}},{key:"open",value:function(e){var t=this;return new Promise((function(n){if(!t._overlays[e])throw new Error("The overlay does not exist.");if(t._active){if(!t._overlays[e].canForceClose)throw t._active===e?new Error("The overlay is already active."):new Error("Another overlay is currently active.");t._closeThroughCaller()}t._active=e,t._overlays[t._active].element.classList.remove("hidden"),t._overlays[t._active].container.classList.remove("hidden"),window.addEventListener("keydown",t._keyDownBound),n()}))}},{key:"close",value:function(e){var t=this;return new Promise((function(n){if(!t._overlays[e])throw new Error("The overlay does not exist.");if(!t._active)throw new Error("The overlay is currently not active.");if(t._active!==e)throw new Error("Another overlay is currently active.");t._overlays[t._active].container.classList.add("hidden"),t._overlays[t._active].element.classList.add("hidden"),t._active=null,window.removeEventListener("keydown",t._keyDownBound),n()}))}},{key:"_keyDown",value:function(e){this._active&&27===e.keyCode&&(this._closeThroughCaller(),e.preventDefault())}},{key:"_closeThroughCaller",value:function(){this._overlays[this._active].callerCloseMethod&&this._overlays[this._active].callerCloseMethod(),this._active&&this.close(this._active)}},{key:"active",get:function(){return this._active}}]),e}();t.OverlayManager=a},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PasswordPrompt=void 0;var i=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}(),r=n(0),a=n(1);function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var s=function(){function e(t,n){var i=this,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:r.NullL10n;o(this,e),this.overlayName=t.overlayName,this.container=t.container,this.label=t.label,this.input=t.input,this.submitButton=t.submitButton,this.cancelButton=t.cancelButton,this.overlayManager=n,this.l10n=a,this.updateCallback=null,this.reason=null,this.submitButton.addEventListener("click",this.verify.bind(this)),this.cancelButton.addEventListener("click",this.close.bind(this)),this.input.addEventListener("keydown",(function(e){13===e.keyCode&&i.verify()})),this.overlayManager.register(this.overlayName,this.container,this.close.bind(this),!0)}return i(e,[{key:"open",value:function(){var e=this;this.overlayManager.open(this.overlayName).then((function(){e.input.focus();var t=void 0;t=e.reason===a.PasswordResponses.INCORRECT_PASSWORD?e.l10n.get("password_invalid",null,"Invalid password. Please try again."):e.l10n.get("password_label",null,"Enter the password to open this PDF file."),t.then((function(t){e.label.textContent=t}))}))}},{key:"close",value:function(){var e=this;this.overlayManager.close(this.overlayName).then((function(){e.input.value=""}))}},{key:"verify",value:function(){var e=this.input.value;if(e&&e.length>0)return this.close(),this.updateCallback(e)}},{key:"setUpdateCallback",value:function(e,t){this.updateCallback=e,this.reason=t}}]),e}();t.PasswordPrompt=s},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PDFAttachmentViewer=void 0;var i=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}(),r=n(1);function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var o=function(){function e(t){var n=t.container,i=t.eventBus,o=t.downloadManager;a(this,e),this.attachments=null,this.container=n,this.eventBus=i,this.downloadManager=o,this._renderedCapability=(0,r.createPromiseCapability)(),this.eventBus.on("fileattachmentannotation",this._appendAttachment.bind(this))}return i(e,[{key:"reset",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.attachments=null,this.container.textContent="",e||(this._renderedCapability=(0,r.createPromiseCapability)())}},{key:"_dispatchEvent",value:function(e){this.eventBus.dispatch("attachmentsloaded",{source:this,attachmentsCount:e}),this._renderedCapability.resolve()}},{key:"_bindPdfLink",value:function(e,t,n){if(r.PDFJS.disableCreateObjectURL)throw new Error('bindPdfLink: Unsupported "PDFJS.disableCreateObjectURL" value.');var i=void 0;e.onclick=function(){i||(i=(0,r.createObjectURL)(t,"application/pdf"));var e=void 0;return e="?file="+encodeURIComponent(i+"#"+n),window.open(e),!1}}},{key:"_bindLink",value:function(e,t,n){var i=this;e.onclick=function(){return i.downloadManager.downloadData(t,n,""),!1}}},{key:"render",value:function(e){var t=e.attachments,n=e.keepRenderedCapability,i=void 0!==n&&n,a=0;if(this.attachments&&this.reset(!0===i),this.attachments=t||null,t){var o=Object.keys(t).sort((function(e,t){return e.toLowerCase().localeCompare(t.toLowerCase())}));a=o.length;for(var s=0;s<a;s++){var u=t[o[s]],l=(0,r.removeNullCharacters)((0,r.getFilenameFromUrl)(u.filename)),c=document.createElement("div");c.className="attachmentsItem";var h=document.createElement("button");h.textContent=l,/\.pdf$/i.test(l)&&!r.PDFJS.disableCreateObjectURL?this._bindPdfLink(h,u.content,l):this._bindLink(h,u.content,l),c.appendChild(h),this.container.appendChild(c)}this._dispatchEvent(a)}else this._dispatchEvent(a)}},{key:"_appendAttachment",value:function(e){var t=this,n=e.id,i=e.filename,r=e.content;this._renderedCapability.promise.then((function(){var e=t.attachments;if(e){for(var a in e)if(n===a)return}else e=Object.create(null);e[n]={filename:i,content:r},t.render({attachments:e,keepRenderedCapability:!0})}))}}]),e}();t.PDFAttachmentViewer=o},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PDFDocumentProperties=void 0;var i=function(){function e(e,t){var n=[],i=!0,r=!1,a=void 0;try{for(var o,s=e[Symbol.iterator]();!(i=(o=s.next()).done);i=!0)if(n.push(o.value),t&&n.length===t)break}catch(u){r=!0,a=u}finally{try{!i&&s["return"]&&s["return"]()}finally{if(r)throw a}}return n}return function(t,n){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return e(t,n);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),r=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}(),a=n(0),o=n(1);function s(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var u="-",l=function(){function e(t,n){var i=t.overlayName,r=t.fields,o=t.container,u=t.closeButton,l=arguments.length>2&&void 0!==arguments[2]?arguments[2]:a.NullL10n;s(this,e),this.overlayName=i,this.fields=r,this.container=o,this.overlayManager=n,this.l10n=l,this._reset(),u&&u.addEventListener("click",this.close.bind(this)),this.overlayManager.register(this.overlayName,this.container,this.close.bind(this))}return r(e,[{key:"open",value:function(){var e=this,t=function(t){Object.defineProperty(e,"fieldData",{value:Object.freeze(t),writable:!1,enumerable:!0,configurable:!0})};Promise.all([this.overlayManager.open(this.overlayName),this._dataAvailableCapability.promise]).then((function(){e.fieldData?e._updateUI():e.pdfDocument.getMetadata().then((function(t){var n=t.info,i=t.metadata;return Promise.all([n,i,e._parseFileSize(e.maybeFileSize),e._parseDate(n.CreationDate),e._parseDate(n.ModDate)])})).then((function(n){var r=i(n,5),o=r[0],s=(r[1],r[2]),u=r[3],l=r[4];return t({fileName:(0,a.getPDFFileNameFromURL)(e.url),fileSize:s,title:o.Title,author:o.Author,subject:o.Subject,keywords:o.Keywords,creationDate:u,modificationDate:l,creator:o.Creator,producer:o.Producer,version:o.PDFFormatVersion,pageCount:e.pdfDocument.numPages}),e._updateUI(),e.pdfDocument.getDownloadInfo()})).then((function(t){var n=t.length;return e._parseFileSize(n)})).then((function(n){var i=(0,a.cloneObj)(e.fieldData);i["fileSize"]=n,t(i),e._updateUI()}))}))}},{key:"close",value:function(){this.overlayManager.close(this.overlayName)}},{key:"setDocument",value:function(e,t){this.pdfDocument&&(this._reset(),this._updateUI(!0)),e&&(this.pdfDocument=e,this.url=t,this._dataAvailableCapability.resolve())}},{key:"setFileSize",value:function(e){"number"===typeof e&&e>0&&(this.maybeFileSize=e)}},{key:"_reset",value:function(){this.pdfDocument=null,this.url=null,this.maybeFileSize=0,delete this.fieldData,this._dataAvailableCapability=(0,o.createPromiseCapability)()}},{key:"_updateUI",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!e&&this.fieldData){if(this.overlayManager.active===this.overlayName)for(var t in this.fields){var n=this.fieldData[t];this.fields[t].textContent=n||0===n?n:u}}else for(var i in this.fields)this.fields[i].textContent=u}},{key:"_parseFileSize",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=e/1024;return t?t<1024?this.l10n.get("document_properties_kb",{size_kb:(+t.toPrecision(3)).toLocaleString(),size_b:e.toLocaleString()},"{{size_kb}} KB ({{size_b}} bytes)"):this.l10n.get("document_properties_mb",{size_mb:(+(t/1024).toPrecision(3)).toLocaleString(),size_b:e.toLocaleString()},"{{size_mb}} MB ({{size_b}} bytes)"):Promise.resolve(void 0)}},{key:"_parseDate",value:function(e){if(e){var t=e;"D:"===t.substring(0,2)&&(t=t.substring(2));var n=parseInt(t.substring(0,4),10),i=parseInt(t.substring(4,6),10)-1,r=parseInt(t.substring(6,8),10),a=parseInt(t.substring(8,10),10),o=parseInt(t.substring(10,12),10),s=parseInt(t.substring(12,14),10),u=t.substring(14,15),l=parseInt(t.substring(15,17),10),c=parseInt(t.substring(18,20),10);"-"===u?(a+=l,o+=c):"+"===u&&(a-=l,o-=c);var h=new Date(Date.UTC(n,i,r,a,o,s)),d=h.toLocaleDateString(),f=h.toLocaleTimeString();return this.l10n.get("document_properties_date_string",{date:d,time:f},"{{date}}, {{time}}")}}}]),e}();t.PDFDocumentProperties=l},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PDFFindBar=void 0;var i=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}(),r=n(7),a=n(0);function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var s=function(){function e(t){var n=this,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a.NullL10n;if(o(this,e),this.opened=!1,this.bar=t.bar||null,this.toggleButton=t.toggleButton||null,this.findField=t.findField||null,this.highlightAll=t.highlightAllCheckbox||null,this.caseSensitive=t.caseSensitiveCheckbox||null,this.findMsg=t.findMsg||null,this.findResultsCount=t.findResultsCount||null,this.findStatusIcon=t.findStatusIcon||null,this.findPreviousButton=t.findPreviousButton||null,this.findNextButton=t.findNextButton||null,this.findController=t.findController||null,this.eventBus=t.eventBus,this.l10n=i,null===this.findController)throw new Error("PDFFindBar cannot be used without a PDFFindController instance.");this.toggleButton.addEventListener("click",(function(){n.toggle()})),this.findField.addEventListener("input",(function(){n.dispatchEvent("")})),this.bar.addEventListener("keydown",(function(e){switch(e.keyCode){case 13:e.target===n.findField&&n.dispatchEvent("again",e.shiftKey);break;case 27:n.close();break}})),this.findPreviousButton.addEventListener("click",(function(){n.dispatchEvent("again",!0)})),this.findNextButton.addEventListener("click",(function(){n.dispatchEvent("again",!1)})),this.highlightAll.addEventListener("click",(function(){n.dispatchEvent("highlightallchange")})),this.caseSensitive.addEventListener("click",(function(){n.dispatchEvent("casesensitivitychange")})),this.eventBus.on("resize",this._adjustWidth.bind(this))}return i(e,[{key:"reset",value:function(){this.updateUIState()}},{key:"dispatchEvent",value:function(e,t){this.eventBus.dispatch("find",{source:this,type:e,query:this.findField.value,caseSensitive:this.caseSensitive.checked,phraseSearch:!0,highlightAll:this.highlightAll.checked,findPrevious:t})}},{key:"updateUIState",value:function(e,t,n){var i=this,a=!1,o="",s="";switch(e){case r.FindState.FOUND:break;case r.FindState.PENDING:s="pending";break;case r.FindState.NOT_FOUND:o=this.l10n.get("find_not_found",null,"Phrase not found"),a=!0;break;case r.FindState.WRAPPED:o=t?this.l10n.get("find_reached_top",null,"Reached top of document, continued from bottom"):this.l10n.get("find_reached_bottom",null,"Reached end of document, continued from top");break}a?this.findField.classList.add("notFound"):this.findField.classList.remove("notFound"),this.findField.setAttribute("data-status",s),Promise.resolve(o).then((function(e){i.findMsg.textContent=e,i._adjustWidth()})),this.updateResultsCount(n)}},{key:"updateResultsCount",value:function(e){this.findResultsCount&&(e?(this.findResultsCount.textContent=e.toLocaleString(),this.findResultsCount.classList.remove("hidden")):(this.findResultsCount.classList.add("hidden"),this.findResultsCount.textContent=""),this._adjustWidth())}},{key:"open",value:function(){this.opened||(this.opened=!0,this.toggleButton.classList.add("toggled"),this.bar.classList.remove("hidden")),this.findField.select(),this.findField.focus(),this._adjustWidth()}},{key:"close",value:function(){this.opened&&(this.opened=!1,this.toggleButton.classList.remove("toggled"),this.bar.classList.add("hidden"),this.findController.active=!1)}},{key:"toggle",value:function(){this.opened?this.close():this.open()}},{key:"_adjustWidth",value:function(){if(this.opened){this.bar.classList.remove("wrapContainers");var e=this.bar.clientHeight,t=this.bar.firstElementChild.clientHeight;e>t&&this.bar.classList.add("wrapContainers")}}}]),e}();t.PDFFindBar=s},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PDFHistory=void 0;var i=n(2);function r(e){this.linkService=e.linkService,this.eventBus=e.eventBus||(0,i.getGlobalEventBus)(),this.initialized=!1,this.initialDestination=null,this.initialBookmark=null}r.prototype={initialize:function(e){this.initialized=!0,this.reInitialized=!1,this.allowHashChange=!0,this.historyUnlocked=!0,this.isViewerInPresentationMode=!1,this.previousHash=window.location.hash.substring(1),this.currentBookmark="",this.currentPage=0,this.updatePreviousBookmark=!1,this.previousBookmark="",this.previousPage=0,this.nextHashParam="",this.fingerprint=e,this.currentUid=this.uid=0,this.current={};var t=window.history.state;this._isStateObjectDefined(t)?(t.target.dest?this.initialDestination=t.target.dest:this.initialBookmark=t.target.hash,this.currentUid=t.uid,this.uid=t.uid+1,this.current=t.target):(t&&t.fingerprint&&this.fingerprint!==t.fingerprint&&(this.reInitialized=!0),this._pushOrReplaceState({fingerprint:this.fingerprint},!0));var n=this;function i(){n.previousHash=window.location.hash.slice(1),n._pushToHistory({hash:n.previousHash},!1,!0),n._updatePreviousBookmark()}function r(e,t){function i(){window.removeEventListener("popstate",i),window.addEventListener("popstate",r),n._pushToHistory(e,!1,!0),history.forward()}function r(){window.removeEventListener("popstate",r),n.allowHashChange=!0,n.historyUnlocked=!0,t()}n.historyUnlocked=!1,n.allowHashChange=!1,window.addEventListener("popstate",i),history.back()}function a(){var e=n._getPreviousParams(null,!0);if(e){var t=!n.current.dest&&n.current.hash!==n.previousHash;n._pushToHistory(e,!1,t),n._updatePreviousBookmark()}window.removeEventListener("beforeunload",a)}window.addEventListener("popstate",(function(e){if(n.historyUnlocked)if(e.state)n._goTo(e.state);else if(0===n.uid){var t=n.previousHash&&n.currentBookmark&&n.previousHash!==n.currentBookmark?{hash:n.currentBookmark,page:n.currentPage}:{page:1};r(t,(function(){i()}))}else i()})),window.addEventListener("beforeunload",a),window.addEventListener("pageshow",(function(e){window.addEventListener("beforeunload",a)})),n.eventBus.on("presentationmodechanged",(function(e){n.isViewerInPresentationMode=e.active}))},clearHistoryState:function(){this._pushOrReplaceState(null,!0)},_isStateObjectDefined:function(e){return!!(e&&e.uid>=0&&e.fingerprint&&this.fingerprint===e.fingerprint&&e.target&&e.target.hash)},_pushOrReplaceState:function(e,t){t?window.history.replaceState(e,"",document.URL):window.history.pushState(e,"",document.URL)},get isHashChangeUnlocked(){return!this.initialized||this.allowHashChange},_updatePreviousBookmark:function(){this.updatePreviousBookmark&&this.currentBookmark&&this.currentPage&&(this.previousBookmark=this.currentBookmark,this.previousPage=this.currentPage,this.updatePreviousBookmark=!1)},updateCurrentBookmark:function(e,t){this.initialized&&(this.currentBookmark=e.substring(1),this.currentPage=0|t,this._updatePreviousBookmark())},updateNextHashParam:function(e){this.initialized&&(this.nextHashParam=e)},push:function(e,t){if(this.initialized&&this.historyUnlocked){if(e.dest&&!e.hash&&(e.hash=this.current.hash&&this.current.dest&&this.current.dest===e.dest?this.current.hash:this.linkService.getDestinationHash(e.dest).split("#")[1]),e.page&&(e.page|=0),t){var n=window.history.state.target;return n||(this._pushToHistory(e,!1),this.previousHash=window.location.hash.substring(1)),this.updatePreviousBookmark=!this.nextHashParam,void(n&&this._updatePreviousBookmark())}if(this.nextHashParam){if(this.nextHashParam===e.hash)return this.nextHashParam=null,void(this.updatePreviousBookmark=!0);this.nextHashParam=null}e.hash?this.current.hash?this.current.hash!==e.hash?this._pushToHistory(e,!0):(!this.current.page&&e.page&&this._pushToHistory(e,!1,!0),this.updatePreviousBookmark=!0):this._pushToHistory(e,!0):this.current.page&&e.page&&this.current.page!==e.page&&this._pushToHistory(e,!0)}},_getPreviousParams:function(e,t){if(!this.currentBookmark||!this.currentPage)return null;if(this.updatePreviousBookmark&&(this.updatePreviousBookmark=!1),this.uid>0&&(!this.previousBookmark||!this.previousPage))return null;if(!this.current.dest&&!e||t){if(this.previousBookmark===this.currentBookmark)return null}else{if(!this.current.page&&!e)return null;if(this.previousPage===this.currentPage)return null}var n={hash:this.currentBookmark,page:this.currentPage};return this.isViewerInPresentationMode&&(n.hash=null),n},_stateObj:function(e){return{fingerprint:this.fingerprint,uid:this.uid,target:e}},_pushToHistory:function(e,t,n){if(this.initialized){if(!e.hash&&e.page&&(e.hash="page="+e.page),t&&!n){var i=this._getPreviousParams();if(i){var r=!this.current.dest&&this.current.hash!==this.previousHash;this._pushToHistory(i,!1,r)}}this._pushOrReplaceState(this._stateObj(e),n||0===this.uid),this.currentUid=this.uid++,this.current=e,this.updatePreviousBookmark=!0}},_goTo:function(e){if(this.initialized&&this.historyUnlocked&&this._isStateObjectDefined(e)){if(!this.reInitialized&&e.uid<this.currentUid){var t=this._getPreviousParams(!0);if(t)return this._pushToHistory(this.current,!1),this._pushToHistory(t,!1),this.currentUid=e.uid,void window.history.back()}this.historyUnlocked=!1,e.target.dest?this.linkService.navigateTo(e.target.dest):this.linkService.setHash(e.target.hash),this.currentUid=e.uid,e.uid>this.uid&&(this.uid=e.uid),this.current=e.target,this.updatePreviousBookmark=!0;var n=window.location.hash.substring(1);this.previousHash!==n&&(this.allowHashChange=!1),this.previousHash=n,this.historyUnlocked=!0}},back:function(){this.go(-1)},forward:function(){this.go(1)},go:function(e){if(this.initialized&&this.historyUnlocked){var t=window.history.state;-1===e&&t&&t.uid>0?window.history.back():1===e&&t&&t.uid<this.uid-1&&window.history.forward()}}},t.PDFHistory=r},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PDFOutlineViewer=void 0;var i=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}(),r=n(1);function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var o="–",s=function(){function e(t){var n=t.container,i=t.linkService,r=t.eventBus;a(this,e),this.outline=null,this.lastToggleIsShow=!0,this.container=n,this.linkService=i,this.eventBus=r}return i(e,[{key:"reset",value:function(){this.outline=null,this.lastToggleIsShow=!0,this.container.textContent="",this.container.classList.remove("outlineWithDeepNesting")}},{key:"_dispatchEvent",value:function(e){this.eventBus.dispatch("outlineloaded",{source:this,outlineCount:e})}},{key:"_bindLink",value:function(e,t){var n=this;if(t.url)(0,r.addLinkAttributes)(e,{url:t.url,target:t.newWindow?r.PDFJS.LinkTarget.BLANK:void 0});else{var i=t.dest;e.href=this.linkService.getDestinationHash(i),e.onclick=function(){return i&&n.linkService.navigateTo(i),!1}}}},{key:"_setStyles",value:function(e,t){var n="";t.bold&&(n+="font-weight: bold;"),t.italic&&(n+="font-style: italic;"),n&&e.setAttribute("style",n)}},{key:"_addToggleButton",value:function(e){var t=this,n=document.createElement("div");n.className="outlineItemToggler",n.onclick=function(i){if(i.stopPropagation(),n.classList.toggle("outlineItemsHidden"),i.shiftKey){var r=!n.classList.contains("outlineItemsHidden");t._toggleOutlineItem(e,r)}},e.insertBefore(n,e.firstChild)}},{key:"_toggleOutlineItem",value:function(e,t){this.lastToggleIsShow=t;for(var n=e.querySelectorAll(".outlineItemToggler"),i=0,r=n.length;i<r;++i)n[i].classList[t?"remove":"add"]("outlineItemsHidden")}},{key:"toggleOutlineTree",value:function(){this.outline&&this._toggleOutlineItem(this.container,!this.lastToggleIsShow)}},{key:"render",value:function(e){var t=e.outline,n=0;if(this.outline&&this.reset(),this.outline=t||null,t){var i=document.createDocumentFragment(),a=[{parent:i,items:this.outline}],s=!1;while(a.length>0)for(var u=a.shift(),l=0,c=u.items.length;l<c;l++){var h=u.items[l],d=document.createElement("div");d.className="outlineItem";var f=document.createElement("a");if(this._bindLink(f,h),this._setStyles(f,h),f.textContent=(0,r.removeNullCharacters)(h.title)||o,d.appendChild(f),h.items.length>0){s=!0,this._addToggleButton(d);var v=document.createElement("div");v.className="outlineItems",d.appendChild(v),a.push({parent:v,items:h.items})}u.parent.appendChild(d),n++}s&&this.container.classList.add("outlineWithDeepNesting"),this.container.appendChild(i),this._dispatchEvent(n)}else this._dispatchEvent(n)}}]),e}();t.PDFOutlineViewer=s},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PDFPageView=void 0;var i=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}(),r=n(0),a=n(1),o=n(2),s=n(3);function u(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var l=function(){function e(t){u(this,e);var n=t.container,i=t.defaultViewport;this.id=t.id,this.renderingId="page"+this.id,this.pdfPage=null,this.pageLabel=null,this.rotation=0,this.scale=t.scale||r.DEFAULT_SCALE,this.viewport=i,this.pdfPageRotate=i.rotation,this.hasRestrictedScaling=!1,this.enhanceTextSelection=t.enhanceTextSelection||!1,this.renderInteractiveForms=t.renderInteractiveForms||!1,this.eventBus=t.eventBus||(0,o.getGlobalEventBus)(),this.renderingQueue=t.renderingQueue,this.textLayerFactory=t.textLayerFactory,this.annotationLayerFactory=t.annotationLayerFactory,this.renderer=t.renderer||r.RendererType.CANVAS,this.l10n=t.l10n||r.NullL10n,this.paintTask=null,this.paintedViewportMap=new WeakMap,this.renderingState=s.RenderingStates.INITIAL,this.resume=null,this.error=null,this.onBeforeDraw=null,this.onAfterDraw=null,this.annotationLayer=null,this.textLayer=null,this.zoomLayer=null;var a=document.createElement("div");a.className="page",a.style.width=Math.floor(this.viewport.width)+"px",a.style.height=Math.floor(this.viewport.height)+"px",a.setAttribute("data-page-number",this.id),this.div=a,n.appendChild(a)}return i(e,[{key:"setPdfPage",value:function(e){this.pdfPage=e,this.pdfPageRotate=e.rotate;var t=(this.rotation+this.pdfPageRotate)%360;this.viewport=e.getViewport(this.scale*r.CSS_UNITS,t),this.stats=e.stats,this.reset()}},{key:"destroy",value:function(){this.reset(),this.pdfPage&&(this.pdfPage.cleanup(),this.pdfPage=null)}},{key:"_resetZoomLayer",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(this.zoomLayer){var t=this.zoomLayer.firstChild;this.paintedViewportMap.delete(t),t.width=0,t.height=0,e&&this.zoomLayer.remove(),this.zoomLayer=null}}},{key:"reset",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];this.cancelRendering();var n=this.div;n.style.width=Math.floor(this.viewport.width)+"px",n.style.height=Math.floor(this.viewport.height)+"px";for(var i=n.childNodes,r=e&&this.zoomLayer||null,a=t&&this.annotationLayer&&this.annotationLayer.div||null,o=i.length-1;o>=0;o--){var s=i[o];r!==s&&a!==s&&n.removeChild(s)}n.removeAttribute("data-loaded"),a?this.annotationLayer.hide():this.annotationLayer=null,r||(this.canvas&&(this.paintedViewportMap.delete(this.canvas),this.canvas.width=0,this.canvas.height=0,delete this.canvas),this._resetZoomLayer()),this.svg&&(this.paintedViewportMap.delete(this.svg),delete this.svg),this.loadingIconDiv=document.createElement("div"),this.loadingIconDiv.className="loadingIcon",n.appendChild(this.loadingIconDiv)}},{key:"update",value:function(e,t){this.scale=e||this.scale,"undefined"!==typeof t&&(this.rotation=t);var n=(this.rotation+this.pdfPageRotate)%360;if(this.viewport=this.viewport.clone({scale:this.scale*r.CSS_UNITS,rotation:n}),this.svg)return this.cssTransform(this.svg,!0),void this.eventBus.dispatch("pagerendered",{source:this,pageNumber:this.id,cssTransform:!0});var i=!1;if(this.canvas&&a.PDFJS.maxCanvasPixels>0){var o=this.outputScale;(Math.floor(this.viewport.width)*o.sx|0)*(Math.floor(this.viewport.height)*o.sy|0)>a.PDFJS.maxCanvasPixels&&(i=!0)}if(this.canvas){if(a.PDFJS.useOnlyCssZoom||this.hasRestrictedScaling&&i)return this.cssTransform(this.canvas,!0),void this.eventBus.dispatch("pagerendered",{source:this,pageNumber:this.id,cssTransform:!0});this.zoomLayer||this.canvas.hasAttribute("hidden")||(this.zoomLayer=this.canvas.parentNode,this.zoomLayer.style.position="absolute")}this.zoomLayer&&this.cssTransform(this.zoomLayer.firstChild),this.reset(!0,!0)}},{key:"cancelRendering",value:function(){this.paintTask&&(this.paintTask.cancel(),this.paintTask=null),this.renderingState=s.RenderingStates.INITIAL,this.resume=null,this.textLayer&&(this.textLayer.cancel(),this.textLayer=null)}},{key:"cssTransform",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=this.viewport.width,i=this.viewport.height,r=this.div;e.style.width=e.parentNode.style.width=r.style.width=Math.floor(n)+"px",e.style.height=e.parentNode.style.height=r.style.height=Math.floor(i)+"px";var o=this.viewport.rotation-this.paintedViewportMap.get(e).rotation,s=Math.abs(o),u=1,l=1;90!==s&&270!==s||(u=i/n,l=n/i);var c="rotate("+o+"deg) scale("+u+","+l+")";if(a.CustomStyle.setProp("transform",e,c),this.textLayer){var h=this.textLayer.viewport,d=this.viewport.rotation-h.rotation,f=Math.abs(d),v=n/h.width;90!==f&&270!==f||(v=n/h.height);var g=this.textLayer.textLayerDiv,p=void 0,m=void 0;switch(f){case 0:p=m=0;break;case 90:p=0,m="-"+g.style.height;break;case 180:p="-"+g.style.width,m="-"+g.style.height;break;case 270:p="-"+g.style.width,m=0;break;default:break}a.CustomStyle.setProp("transform",g,"rotate("+f+"deg) scale("+v+", "+v+") translate("+p+", "+m+")"),a.CustomStyle.setProp("transformOrigin",g,"0% 0%")}t&&this.annotationLayer&&this.annotationLayer.render(this.viewport,"display")}},{key:"getPagePoint",value:function(e,t){return this.viewport.convertToPdfPoint(e,t)}},{key:"draw",value:function(){var e=this;if(this.renderingState!==s.RenderingStates.INITIAL&&this.reset(),!this.pdfPage)return this.renderingState=s.RenderingStates.FINISHED,Promise.reject(new Error("Page is not loaded"));this.renderingState=s.RenderingStates.RUNNING;var t=this.pdfPage,n=this.div,i=document.createElement("div");i.style.width=n.style.width,i.style.height=n.style.height,i.classList.add("canvasWrapper"),this.annotationLayer&&this.annotationLayer.div?n.insertBefore(i,this.annotationLayer.div):n.appendChild(i);var o=null;if(this.textLayerFactory){var u=document.createElement("div");u.className="textLayer",u.style.width=i.style.width,u.style.height=i.style.height,this.annotationLayer&&this.annotationLayer.div?n.insertBefore(u,this.annotationLayer.div):n.appendChild(u),o=this.textLayerFactory.createTextLayerBuilder(u,this.id-1,this.viewport,this.enhanceTextSelection)}this.textLayer=o;var l=null;this.renderingQueue&&(l=function(t){if(!e.renderingQueue.isHighestPriority(e))return e.renderingState=s.RenderingStates.PAUSED,void(e.resume=function(){e.renderingState=s.RenderingStates.RUNNING,t()});t()});var c=function(i){return h===e.paintTask&&(e.paintTask=null),"cancelled"===i||i instanceof a.RenderingCancelledException?(e.error=null,Promise.resolve(void 0)):(e.renderingState=s.RenderingStates.FINISHED,e.loadingIconDiv&&(n.removeChild(e.loadingIconDiv),delete e.loadingIconDiv),e._resetZoomLayer(!0),e.error=i,e.stats=t.stats,e.onAfterDraw&&e.onAfterDraw(),e.eventBus.dispatch("pagerendered",{source:e,pageNumber:e.id,cssTransform:!1}),i?Promise.reject(i):Promise.resolve(void 0))},h=this.renderer===r.RendererType.SVG?this.paintOnSvg(i):this.paintOnCanvas(i);h.onRenderContinue=l,this.paintTask=h;var d=h.promise.then((function(){return c(null).then((function(){if(o){var e=t.streamTextContent({normalizeWhitespace:!0});o.setTextContentStream(e),o.render()}}))}),(function(e){return c(e)}));return this.annotationLayerFactory&&(this.annotationLayer||(this.annotationLayer=this.annotationLayerFactory.createAnnotationLayerBuilder(n,t,this.renderInteractiveForms,this.l10n)),this.annotationLayer.render(this.viewport,"display")),n.setAttribute("data-loaded",!0),this.onBeforeDraw&&this.onBeforeDraw(),d}},{key:"paintOnCanvas",value:function(e){var t=(0,a.createPromiseCapability)(),n={promise:t.promise,onRenderContinue:function(e){e()},cancel:function(){w.cancel()}},i=this.viewport,o=document.createElement("canvas");o.id=this.renderingId,o.setAttribute("hidden","hidden");var s=!0,u=function(){s&&(o.removeAttribute("hidden"),s=!1)};e.appendChild(o),this.canvas=o,o.mozOpaque=!0;var l=o.getContext("2d",{alpha:!1}),c=(0,r.getOutputScale)(l);if(this.outputScale=c,a.PDFJS.useOnlyCssZoom){var h=i.clone({scale:r.CSS_UNITS});c.sx*=h.width/i.width,c.sy*=h.height/i.height,c.scaled=!0}if(a.PDFJS.maxCanvasPixels>0){var d=i.width*i.height,f=Math.sqrt(a.PDFJS.maxCanvasPixels/d);c.sx>f||c.sy>f?(c.sx=f,c.sy=f,c.scaled=!0,this.hasRestrictedScaling=!0):this.hasRestrictedScaling=!1}var v=(0,r.approximateFraction)(c.sx),g=(0,r.approximateFraction)(c.sy);o.width=(0,r.roundToDivide)(i.width*c.sx,v[0]),o.height=(0,r.roundToDivide)(i.height*c.sy,g[0]),o.style.width=(0,r.roundToDivide)(i.width,v[1])+"px",o.style.height=(0,r.roundToDivide)(i.height,g[1])+"px",this.paintedViewportMap.set(o,i);var p=c.scaled?[c.sx,0,0,c.sy,0,0]:null,m={canvasContext:l,transform:p,viewport:this.viewport,renderInteractiveForms:this.renderInteractiveForms},w=this.pdfPage.render(m);return w.onContinue=function(e){u(),n.onRenderContinue?n.onRenderContinue(e):e()},w.promise.then((function(){u(),t.resolve(void 0)}),(function(e){u(),t.reject(e)})),n}},{key:"paintOnSvg",value:function(e){var t=this,n=!1,i=function(){if(n)throw a.PDFJS.pdfjsNext?new a.RenderingCancelledException("Rendering cancelled, page "+t.id,"svg"):"cancelled"},o=this.pdfPage,u=this.viewport.clone({scale:r.CSS_UNITS}),l=o.getOperatorList().then((function(n){i();var r=new a.SVGGraphics(o.commonObjs,o.objs);return r.getSVG(n,u).then((function(n){i(),t.svg=n,t.paintedViewportMap.set(n,u),n.style.width=e.style.width,n.style.height=e.style.height,t.renderingState=s.RenderingStates.FINISHED,e.appendChild(n)}))}));return{promise:l,onRenderContinue:function(e){e()},cancel:function(){n=!0}}}},{key:"setPageLabel",value:function(e){this.pageLabel="string"===typeof e?e:null,null!==this.pageLabel?this.div.setAttribute("data-page-label",this.pageLabel):this.div.removeAttribute("data-page-label")}},{key:"width",get:function(){return this.viewport.width}},{key:"height",get:function(){return this.viewport.height}}]),e}();t.PDFPageView=l},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PDFPresentationMode=void 0;var i=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}(),r=n(0);function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var o=1500,s=3e3,u="pdfPresentationMode",l="pdfPresentationModeControls",c=50,h=.1,d=50,f=Math.PI/6,v=function(){function e(t){var n=this,i=t.container,r=t.viewer,o=void 0===r?null:r,s=t.pdfViewer,u=t.eventBus,l=t.contextMenuItems,c=void 0===l?null:l;a(this,e),this.container=i,this.viewer=o||i.firstElementChild,this.pdfViewer=s,this.eventBus=u,this.active=!1,this.args=null,this.contextMenuOpen=!1,this.mouseScrollTimeStamp=0,this.mouseScrollDelta=0,this.touchSwipeState=null,c&&(c.contextFirstPage.addEventListener("click",(function(){n.contextMenuOpen=!1,n.eventBus.dispatch("firstpage")})),c.contextLastPage.addEventListener("click",(function(){n.contextMenuOpen=!1,n.eventBus.dispatch("lastpage")})),c.contextPageRotateCw.addEventListener("click",(function(){n.contextMenuOpen=!1,n.eventBus.dispatch("rotatecw")})),c.contextPageRotateCcw.addEventListener("click",(function(){n.contextMenuOpen=!1,n.eventBus.dispatch("rotateccw")})))}return i(e,[{key:"request",value:function(){if(this.switchInProgress||this.active||!this.viewer.hasChildNodes())return!1;if(this._addFullscreenChangeListeners(),this._setSwitchInProgress(),this._notifyStateChange(),this.container.requestFullscreen)this.container.requestFullscreen();else if(this.container.mozRequestFullScreen)this.container.mozRequestFullScreen();else if(this.container.webkitRequestFullscreen)this.container.webkitRequestFullscreen(Element.ALLOW_KEYBOARD_INPUT);else{if(!this.container.msRequestFullscreen)return!1;this.container.msRequestFullscreen()}return this.args={page:this.pdfViewer.currentPageNumber,previousScale:this.pdfViewer.currentScaleValue},!0}},{key:"_mouseWheel",value:function(e){if(this.active){e.preventDefault();var t=(0,r.normalizeWheelEventDelta)(e),n=(new Date).getTime(),i=this.mouseScrollTimeStamp;if(!(n>i&&n-i<c)&&((this.mouseScrollDelta>0&&t<0||this.mouseScrollDelta<0&&t>0)&&this._resetMouseScrollState(),this.mouseScrollDelta+=t,Math.abs(this.mouseScrollDelta)>=h)){var a=this.mouseScrollDelta;this._resetMouseScrollState();var o=a>0?this._goToPreviousPage():this._goToNextPage();o&&(this.mouseScrollTimeStamp=n)}}}},{key:"_goToPreviousPage",value:function(){var e=this.pdfViewer.currentPageNumber;return!(e<=1)&&(this.pdfViewer.currentPageNumber=e-1,!0)}},{key:"_goToNextPage",value:function(){var e=this.pdfViewer.currentPageNumber;return!(e>=this.pdfViewer.pagesCount)&&(this.pdfViewer.currentPageNumber=e+1,!0)}},{key:"_notifyStateChange",value:function(){this.eventBus.dispatch("presentationmodechanged",{source:this,active:this.active,switchInProgress:!!this.switchInProgress})}},{key:"_setSwitchInProgress",value:function(){var e=this;this.switchInProgress&&clearTimeout(this.switchInProgress),this.switchInProgress=setTimeout((function(){e._removeFullscreenChangeListeners(),delete e.switchInProgress,e._notifyStateChange()}),o)}},{key:"_resetSwitchInProgress",value:function(){this.switchInProgress&&(clearTimeout(this.switchInProgress),delete this.switchInProgress)}},{key:"_enter",value:function(){var e=this;this.active=!0,this._resetSwitchInProgress(),this._notifyStateChange(),this.container.classList.add(u),setTimeout((function(){e.pdfViewer.currentPageNumber=e.args.page,e.pdfViewer.currentScaleValue="page-fit"}),0),this._addWindowListeners(),this._showControls(),this.contextMenuOpen=!1,this.container.setAttribute("contextmenu","viewerContextMenu"),window.getSelection().removeAllRanges()}},{key:"_exit",value:function(){var e=this,t=this.pdfViewer.currentPageNumber;this.container.classList.remove(u),setTimeout((function(){e.active=!1,e._removeFullscreenChangeListeners(),e._notifyStateChange(),e.pdfViewer.currentScaleValue=e.args.previousScale,e.pdfViewer.currentPageNumber=t,e.args=null}),0),this._removeWindowListeners(),this._hideControls(),this._resetMouseScrollState(),this.container.removeAttribute("contextmenu"),this.contextMenuOpen=!1}},{key:"_mouseDown",value:function(e){if(this.contextMenuOpen)return this.contextMenuOpen=!1,void e.preventDefault();if(0===e.button){var t=e.target.href&&e.target.classList.contains("internalLink");t||(e.preventDefault(),e.shiftKey?this._goToPreviousPage():this._goToNextPage())}}},{key:"_contextMenu",value:function(){this.contextMenuOpen=!0}},{key:"_showControls",value:function(){var e=this;this.controlsTimeout?clearTimeout(this.controlsTimeout):this.container.classList.add(l),this.controlsTimeout=setTimeout((function(){e.container.classList.remove(l),delete e.controlsTimeout}),s)}},{key:"_hideControls",value:function(){this.controlsTimeout&&(clearTimeout(this.controlsTimeout),this.container.classList.remove(l),delete this.controlsTimeout)}},{key:"_resetMouseScrollState",value:function(){this.mouseScrollTimeStamp=0,this.mouseScrollDelta=0}},{key:"_touchSwipe",value:function(e){if(this.active)if(e.touches.length>1)this.touchSwipeState=null;else switch(e.type){case"touchstart":this.touchSwipeState={startX:e.touches[0].pageX,startY:e.touches[0].pageY,endX:e.touches[0].pageX,endY:e.touches[0].pageY};break;case"touchmove":if(null===this.touchSwipeState)return;this.touchSwipeState.endX=e.touches[0].pageX,this.touchSwipeState.endY=e.touches[0].pageY,e.preventDefault();break;case"touchend":if(null===this.touchSwipeState)return;var t=0,n=this.touchSwipeState.endX-this.touchSwipeState.startX,i=this.touchSwipeState.endY-this.touchSwipeState.startY,r=Math.abs(Math.atan2(i,n));Math.abs(n)>d&&(r<=f||r>=Math.PI-f)?t=n:Math.abs(i)>d&&Math.abs(r-Math.PI/2)<=f&&(t=i),t>0?this._goToPreviousPage():t<0&&this._goToNextPage();break}}},{key:"_addWindowListeners",value:function(){this.showControlsBind=this._showControls.bind(this),this.mouseDownBind=this._mouseDown.bind(this),this.mouseWheelBind=this._mouseWheel.bind(this),this.resetMouseScrollStateBind=this._resetMouseScrollState.bind(this),this.contextMenuBind=this._contextMenu.bind(this),this.touchSwipeBind=this._touchSwipe.bind(this),window.addEventListener("mousemove",this.showControlsBind),window.addEventListener("mousedown",this.mouseDownBind),window.addEventListener("wheel",this.mouseWheelBind),window.addEventListener("keydown",this.resetMouseScrollStateBind),window.addEventListener("contextmenu",this.contextMenuBind),window.addEventListener("touchstart",this.touchSwipeBind),window.addEventListener("touchmove",this.touchSwipeBind),window.addEventListener("touchend",this.touchSwipeBind)}},{key:"_removeWindowListeners",value:function(){window.removeEventListener("mousemove",this.showControlsBind),window.removeEventListener("mousedown",this.mouseDownBind),window.removeEventListener("wheel",this.mouseWheelBind),window.removeEventListener("keydown",this.resetMouseScrollStateBind),window.removeEventListener("contextmenu",this.contextMenuBind),window.removeEventListener("touchstart",this.touchSwipeBind),window.removeEventListener("touchmove",this.touchSwipeBind),window.removeEventListener("touchend",this.touchSwipeBind),delete this.showControlsBind,delete this.mouseDownBind,delete this.mouseWheelBind,delete this.resetMouseScrollStateBind,delete this.contextMenuBind,delete this.touchSwipeBind}},{key:"_fullscreenChange",value:function(){this.isFullscreen?this._enter():this._exit()}},{key:"_addFullscreenChangeListeners",value:function(){this.fullscreenChangeBind=this._fullscreenChange.bind(this),window.addEventListener("fullscreenchange",this.fullscreenChangeBind),window.addEventListener("mozfullscreenchange",this.fullscreenChangeBind),window.addEventListener("webkitfullscreenchange",this.fullscreenChangeBind),window.addEventListener("MSFullscreenChange",this.fullscreenChangeBind)}},{key:"_removeFullscreenChangeListeners",value:function(){window.removeEventListener("fullscreenchange",this.fullscreenChangeBind),window.removeEventListener("mozfullscreenchange",this.fullscreenChangeBind),window.removeEventListener("webkitfullscreenchange",this.fullscreenChangeBind),window.removeEventListener("MSFullscreenChange",this.fullscreenChangeBind),delete this.fullscreenChangeBind}},{key:"isFullscreen",get:function(){return!!(document.fullscreenElement||document.mozFullScreen||document.webkitIsFullScreen||document.msFullscreenElement)}}]),e}();t.PDFPresentationMode=v},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PDFSidebar=t.SidebarView=void 0;var i=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}(),r=n(0),a=n(3);function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var s="pdfSidebarNotification",u={NONE:0,THUMBS:1,OUTLINE:2,ATTACHMENTS:3},l=function(){function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:r.NullL10n;o(this,e),this.isOpen=!1,this.active=u.THUMBS,this.isInitialViewSet=!1,this.onToggled=null,this.pdfViewer=t.pdfViewer,this.pdfThumbnailViewer=t.pdfThumbnailViewer,this.pdfOutlineViewer=t.pdfOutlineViewer,this.mainContainer=t.mainContainer,this.outerContainer=t.outerContainer,this.eventBus=t.eventBus,this.toggleButton=t.toggleButton,this.thumbnailButton=t.thumbnailButton,this.outlineButton=t.outlineButton,this.attachmentsButton=t.attachmentsButton,this.thumbnailView=t.thumbnailView,this.outlineView=t.outlineView,this.attachmentsView=t.attachmentsView,this.disableNotification=t.disableNotification||!1,this.l10n=n,this._addEventListeners()}return i(e,[{key:"reset",value:function(){this.isInitialViewSet=!1,this._hideUINotification(null),this.switchView(u.THUMBS),this.outlineButton.disabled=!1,this.attachmentsButton.disabled=!1}},{key:"setInitialView",value:function(e){if(!this.isInitialViewSet)if(this.isInitialViewSet=!0,this.isOpen&&e===u.NONE)this._dispatchEvent();else{var t=e===this.visibleView;this.switchView(e,!0),t&&this._dispatchEvent()}}},{key:"switchView",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(e!==u.NONE){var n=e!==this.active,i=!1;switch(e){case u.THUMBS:this.thumbnailButton.classList.add("toggled"),this.outlineButton.classList.remove("toggled"),this.attachmentsButton.classList.remove("toggled"),this.thumbnailView.classList.remove("hidden"),this.outlineView.classList.add("hidden"),this.attachmentsView.classList.add("hidden"),this.isOpen&&n&&(this._updateThumbnailViewer(),i=!0);break;case u.OUTLINE:if(this.outlineButton.disabled)return;this.thumbnailButton.classList.remove("toggled"),this.outlineButton.classList.add("toggled"),this.attachmentsButton.classList.remove("toggled"),this.thumbnailView.classList.add("hidden"),this.outlineView.classList.remove("hidden"),this.attachmentsView.classList.add("hidden");break;case u.ATTACHMENTS:if(this.attachmentsButton.disabled)return;this.thumbnailButton.classList.remove("toggled"),this.outlineButton.classList.remove("toggled"),this.attachmentsButton.classList.add("toggled"),this.thumbnailView.classList.add("hidden"),this.outlineView.classList.add("hidden"),this.attachmentsView.classList.remove("hidden");break;default:return}this.active=0|e,!t||this.isOpen?(i&&this._forceRendering(),n&&this._dispatchEvent(),this._hideUINotification(this.active)):this.open()}else this.close()}},{key:"open",value:function(){this.isOpen||(this.isOpen=!0,this.toggleButton.classList.add("toggled"),this.outerContainer.classList.add("sidebarMoving"),this.outerContainer.classList.add("sidebarOpen"),this.active===u.THUMBS&&this._updateThumbnailViewer(),this._forceRendering(),this._dispatchEvent(),this._hideUINotification(this.active))}},{key:"close",value:function(){this.isOpen&&(this.isOpen=!1,this.toggleButton.classList.remove("toggled"),this.outerContainer.classList.add("sidebarMoving"),this.outerContainer.classList.remove("sidebarOpen"),this._forceRendering(),this._dispatchEvent())}},{key:"toggle",value:function(){this.isOpen?this.close():this.open()}},{key:"_dispatchEvent",value:function(){this.eventBus.dispatch("sidebarviewchanged",{source:this,view:this.visibleView})}},{key:"_forceRendering",value:function(){this.onToggled?this.onToggled():(this.pdfViewer.forceRendering(),this.pdfThumbnailViewer.forceRendering())}},{key:"_updateThumbnailViewer",value:function(){for(var e=this.pdfViewer,t=this.pdfThumbnailViewer,n=e.pagesCount,i=0;i<n;i++){var r=e.getPageView(i);if(r&&r.renderingState===a.RenderingStates.FINISHED){var o=t.getThumbnail(i);o.setImage(r)}}t.scrollThumbnailIntoView(e.currentPageNumber)}},{key:"_showUINotification",value:function(e){var t=this;if(!this.disableNotification){if(this.l10n.get("toggle_sidebar_notification.title",null,"Toggle Sidebar (document contains outline/attachments)").then((function(e){t.toggleButton.title=e})),this.isOpen){if(e===this.active)return}else this.toggleButton.classList.add(s);switch(e){case u.OUTLINE:this.outlineButton.classList.add(s);break;case u.ATTACHMENTS:this.attachmentsButton.classList.add(s);break}}}},{key:"_hideUINotification",value:function(e){var t=this;if(!this.disableNotification){var n=function(e){switch(e){case u.OUTLINE:t.outlineButton.classList.remove(s);break;case u.ATTACHMENTS:t.attachmentsButton.classList.remove(s);break}};if(this.isOpen||null===e)if(this.toggleButton.classList.remove(s),null===e){for(e in u)n(u[e]);this.l10n.get("toggle_sidebar.title",null,"Toggle Sidebar").then((function(e){t.toggleButton.title=e}))}else n(e)}}},{key:"_addEventListeners",value:function(){var e=this;this.mainContainer.addEventListener("transitionend",(function(t){t.target===e.mainContainer&&e.outerContainer.classList.remove("sidebarMoving")})),this.thumbnailButton.addEventListener("click",(function(){e.switchView(u.THUMBS)})),this.outlineButton.addEventListener("click",(function(){e.switchView(u.OUTLINE)})),this.outlineButton.addEventListener("dblclick",(function(){e.pdfOutlineViewer.toggleOutlineTree()})),this.attachmentsButton.addEventListener("click",(function(){e.switchView(u.ATTACHMENTS)})),this.eventBus.on("outlineloaded",(function(t){var n=t.outlineCount;e.outlineButton.disabled=!n,n?e._showUINotification(u.OUTLINE):e.active===u.OUTLINE&&e.switchView(u.THUMBS)})),this.eventBus.on("attachmentsloaded",(function(t){var n=t.attachmentsCount;e.attachmentsButton.disabled=!n,n?e._showUINotification(u.ATTACHMENTS):e.active===u.ATTACHMENTS&&e.switchView(u.THUMBS)})),this.eventBus.on("presentationmodechanged",(function(t){t.active||t.switchInProgress||!e.isThumbnailViewVisible||e._updateThumbnailViewer()}))}},{key:"visibleView",get:function(){return this.isOpen?this.active:u.NONE}},{key:"isThumbnailViewVisible",get:function(){return this.isOpen&&this.active===u.THUMBS}},{key:"isOutlineViewVisible",get:function(){return this.isOpen&&this.active===u.OUTLINE}},{key:"isAttachmentsViewVisible",get:function(){return this.isOpen&&this.active===u.ATTACHMENTS}}]),e}();t.SidebarView=u,t.PDFSidebar=l},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PDFThumbnailView=void 0;var i=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}(),r=n(1),a=n(0),o=n(3);function s(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var u=3,l=1,c=98,h=function(){var e=null;return{getCanvas:function(t,n){var i=e;i||(i=document.createElement("canvas"),e=i),i.width=t,i.height=n,i.mozOpaque=!0;var r=i.getContext("2d",{alpha:!1});return r.save(),r.fillStyle="rgb(255, 255, 255)",r.fillRect(0,0,t,n),r.restore(),i},destroyCanvas:function(){var t=e;t&&(t.width=0,t.height=0),e=null}}}(),d=function(){function e(t){var n=t.container,i=t.id,r=t.defaultViewport,u=t.linkService,h=t.renderingQueue,d=t.disableCanvasToImageConversion,f=void 0!==d&&d,v=t.l10n,g=void 0===v?a.NullL10n:v;s(this,e),this.id=i,this.renderingId="thumbnail"+i,this.pageLabel=null,this.pdfPage=null,this.rotation=0,this.viewport=r,this.pdfPageRotate=r.rotation,this.linkService=u,this.renderingQueue=h,this.renderTask=null,this.renderingState=o.RenderingStates.INITIAL,this.resume=null,this.disableCanvasToImageConversion=f,this.pageWidth=this.viewport.width,this.pageHeight=this.viewport.height,this.pageRatio=this.pageWidth/this.pageHeight,this.canvasWidth=c,this.canvasHeight=this.canvasWidth/this.pageRatio|0,this.scale=this.canvasWidth/this.pageWidth,this.l10n=g;var p=document.createElement("a");p.href=u.getAnchorUrl("#page="+i),this.l10n.get("thumb_page_title",{page:i},"Page {{page}}").then((function(e){p.title=e})),p.onclick=function(){return u.page=i,!1},this.anchor=p;var m=document.createElement("div");m.className="thumbnail",m.setAttribute("data-page-number",this.id),this.div=m,1===i&&m.classList.add("selected");var w=document.createElement("div");w.className="thumbnailSelectionRing";var b=2*l;w.style.width=this.canvasWidth+b+"px",w.style.height=this.canvasHeight+b+"px",this.ring=w,m.appendChild(w),p.appendChild(m),n.appendChild(p)}return i(e,[{key:"setPdfPage",value:function(e){this.pdfPage=e,this.pdfPageRotate=e.rotate;var t=(this.rotation+this.pdfPageRotate)%360;this.viewport=e.getViewport(1,t),this.reset()}},{key:"reset",value:function(){this.cancelRendering(),this.pageWidth=this.viewport.width,this.pageHeight=this.viewport.height,this.pageRatio=this.pageWidth/this.pageHeight,this.canvasHeight=this.canvasWidth/this.pageRatio|0,this.scale=this.canvasWidth/this.pageWidth,this.div.removeAttribute("data-loaded");for(var e=this.ring,t=e.childNodes,n=t.length-1;n>=0;n--)e.removeChild(t[n]);var i=2*l;e.style.width=this.canvasWidth+i+"px",e.style.height=this.canvasHeight+i+"px",this.canvas&&(this.canvas.width=0,this.canvas.height=0,delete this.canvas),this.image&&(this.image.removeAttribute("src"),delete this.image)}},{key:"update",value:function(e){"undefined"!==typeof e&&(this.rotation=e);var t=(this.rotation+this.pdfPageRotate)%360;this.viewport=this.viewport.clone({scale:1,rotation:t}),this.reset()}},{key:"cancelRendering",value:function(){this.renderTask&&(this.renderTask.cancel(),this.renderTask=null),this.renderingState=o.RenderingStates.INITIAL,this.resume=null}},{key:"_getPageDrawContext",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=document.createElement("canvas");this.canvas=t,t.mozOpaque=!0;var n=t.getContext("2d",{alpha:!1}),i=(0,a.getOutputScale)(n);return t.width=this.canvasWidth*i.sx|0,t.height=this.canvasHeight*i.sy|0,t.style.width=this.canvasWidth+"px",t.style.height=this.canvasHeight+"px",!e&&i.scaled&&n.scale(i.sx,i.sy),n}},{key:"_convertCanvasToImage",value:function(){var e=this;if(this.canvas&&this.renderingState===o.RenderingStates.FINISHED){var t=this.renderingId,n="thumbnailImage";if(this.disableCanvasToImageConversion)return this.canvas.id=t,this.canvas.className=n,this.l10n.get("thumb_page_canvas",{page:this.pageId},"Thumbnail of Page {{page}}").then((function(t){e.canvas.setAttribute("aria-label",t)})),this.div.setAttribute("data-loaded",!0),void this.ring.appendChild(this.canvas);var i=document.createElement("img");i.id=t,i.className=n,this.l10n.get("thumb_page_canvas",{page:this.pageId},"Thumbnail of Page {{page}}").then((function(e){i.setAttribute("aria-label",e)})),i.style.width=this.canvasWidth+"px",i.style.height=this.canvasHeight+"px",i.src=this.canvas.toDataURL(),this.image=i,this.div.setAttribute("data-loaded",!0),this.ring.appendChild(i),this.canvas.width=0,this.canvas.height=0,delete this.canvas}}},{key:"draw",value:function(){var e=this;if(this.renderingState!==o.RenderingStates.INITIAL)return Promise.resolve(void 0);this.renderingState=o.RenderingStates.RUNNING;var t=(0,r.createPromiseCapability)(),n=function(n){l===e.renderTask&&(e.renderTask=null),"cancelled"===n||n instanceof r.RenderingCancelledException?t.resolve(void 0):(e.renderingState=o.RenderingStates.FINISHED,e._convertCanvasToImage(),n?t.reject(n):t.resolve(void 0))},i=this._getPageDrawContext(),a=this.viewport.clone({scale:this.scale}),s=function(t){if(!e.renderingQueue.isHighestPriority(e))return e.renderingState=o.RenderingStates.PAUSED,void(e.resume=function(){e.renderingState=o.RenderingStates.RUNNING,t()});t()},u={canvasContext:i,viewport:a},l=this.renderTask=this.pdfPage.render(u);return l.onContinue=s,l.promise.then((function(){n(null)}),(function(e){n(e)})),t.promise}},{key:"setImage",value:function(e){if(this.renderingState===o.RenderingStates.INITIAL){var t=e.canvas;if(t){this.pdfPage||this.setPdfPage(e.pdfPage),this.renderingState=o.RenderingStates.FINISHED;var n=this._getPageDrawContext(!0),i=n.canvas;if(t.width<=2*i.width)return n.drawImage(t,0,0,t.width,t.height,0,0,i.width,i.height),void this._convertCanvasToImage();var r=i.width<<u,a=i.height<<u,s=h.getCanvas(r,a),l=s.getContext("2d");while(r>t.width||a>t.height)r>>=1,a>>=1;l.drawImage(t,0,0,t.width,t.height,0,0,r,a);while(r>2*i.width)l.drawImage(s,0,0,r,a,0,0,r>>1,a>>1),r>>=1,a>>=1;n.drawImage(s,0,0,r,a,0,0,i.width,i.height),this._convertCanvasToImage()}}}},{key:"setPageLabel",value:function(e){var t=this;this.pageLabel="string"===typeof e?e:null,this.l10n.get("thumb_page_title",{page:this.pageId},"Page {{page}}").then((function(e){t.anchor.title=e})),this.renderingState===o.RenderingStates.FINISHED&&this.l10n.get("thumb_page_canvas",{page:this.pageId},"Thumbnail of Page {{page}}").then((function(e){t.image?t.image.setAttribute("aria-label",e):t.disableCanvasToImageConversion&&t.canvas&&t.canvas.setAttribute("aria-label",e)}))}},{key:"pageId",get:function(){return null!==this.pageLabel?this.pageLabel:this.id}}],[{key:"cleanup",value:function(){h.destroyCanvas()}}]),e}();t.PDFThumbnailView=d},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PDFThumbnailViewer=void 0;var i=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}(),r=n(0),a=n(25);function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var s=-19,u=function(){function e(t){var n=t.container,i=t.linkService,a=t.renderingQueue,s=t.l10n,u=void 0===s?r.NullL10n:s;o(this,e),this.container=n,this.linkService=i,this.renderingQueue=a,this.l10n=u,this.scroll=(0,r.watchScroll)(this.container,this._scrollUpdated.bind(this)),this._resetView()}return i(e,[{key:"_scrollUpdated",value:function(){this.renderingQueue.renderHighestPriority()}},{key:"getThumbnail",value:function(e){return this._thumbnails[e]}},{key:"_getVisibleThumbs",value:function(){return(0,r.getVisibleElements)(this.container,this._thumbnails)}},{key:"scrollThumbnailIntoView",value:function(e){var t=document.querySelector(".thumbnail.selected");t&&t.classList.remove("selected");var n=document.querySelector('div.thumbnail[data-page-number="'+e+'"]');n&&n.classList.add("selected");var i=this._getVisibleThumbs(),a=i.views.length;if(a>0){var o=i.first.id,u=a>1?i.last.id:o;(e<=o||e>=u)&&(0,r.scrollIntoView)(n,{top:s})}}},{key:"cleanup",value:function(){a.PDFThumbnailView.cleanup()}},{key:"_resetView",value:function(){this._thumbnails=[],this._pageLabels=null,this._pagesRotation=0,this._pagesRequests=[],this.container.textContent=""}},{key:"setDocument",value:function(e){var t=this;this.pdfDocument&&(this._cancelRendering(),this._resetView()),this.pdfDocument=e,e&&e.getPage(1).then((function(n){for(var i=e.numPages,r=n.getViewport(1),o=1;o<=i;++o){var s=new a.PDFThumbnailView({container:t.container,id:o,defaultViewport:r.clone(),linkService:t.linkService,renderingQueue:t.renderingQueue,disableCanvasToImageConversion:!1,l10n:t.l10n});t._thumbnails.push(s)}})).catch((function(e){}))}},{key:"_cancelRendering",value:function(){for(var e=0,t=this._thumbnails.length;e<t;e++)this._thumbnails[e]&&this._thumbnails[e].cancelRendering()}},{key:"setPageLabels",value:function(e){if(this.pdfDocument){e&&e instanceof Array&&this.pdfDocument.numPages===e.length?this._pageLabels=e:this._pageLabels=null;for(var t=0,n=this._thumbnails.length;t<n;t++){var i=this._pageLabels&&this._pageLabels[t];this._thumbnails[t].setPageLabel(i)}}}},{key:"_ensurePdfPageLoaded",value:function(e){var t=this;if(e.pdfPage)return Promise.resolve(e.pdfPage);var n=e.id;if(this._pagesRequests[n])return this._pagesRequests[n];var i=this.pdfDocument.getPage(n).then((function(i){return e.setPdfPage(i),t._pagesRequests[n]=null,i})).catch((function(e){t._pagesRequests[n]=null}));return this._pagesRequests[n]=i,i}},{key:"forceRendering",value:function(){var e=this,t=this._getVisibleThumbs(),n=this.renderingQueue.getHighestPriority(t,this._thumbnails,this.scroll.down);return!!n&&(this._ensurePdfPageLoaded(n).then((function(){e.renderingQueue.renderView(n)})),!0)}},{key:"pagesRotation",get:function(){return this._pagesRotation},set:function(e){if("number"!==typeof e||e%90!==0)throw new Error("Invalid thumbnails rotation angle.");if(this.pdfDocument){this._pagesRotation=e;for(var t=0,n=this._thumbnails.length;t<n;t++)this._thumbnails[t].update(e)}}}]),e}();t.PDFThumbnailViewer=u},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PDFViewer=t.PresentationModeState=void 0;var i=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}(),r=n(1),a=n(0),o=n(3),s=n(11),u=n(2),l=n(22),c=n(5),h=n(30);function d(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var f={UNKNOWN:0,NORMAL:1,CHANGING:2,FULLSCREEN:3},v=10;function g(e){var t=[];this.push=function(n){var i=t.indexOf(n);i>=0&&t.splice(i,1),t.push(n),t.length>e&&t.shift().destroy()},this.resize=function(n){e=n;while(t.length>e)t.shift().destroy()}}function p(e,t){return t===e||Math.abs(t-e)<1e-15}function m(e){return e.width<=e.height}var w=function(){function e(t){d(this,e),this.container=t.container,this.viewer=t.viewer||t.container.firstElementChild,this.eventBus=t.eventBus||(0,u.getGlobalEventBus)(),this.linkService=t.linkService||new c.SimpleLinkService,this.downloadManager=t.downloadManager||null,this.removePageBorders=t.removePageBorders||!1,this.enhanceTextSelection=t.enhanceTextSelection||!1,this.renderInteractiveForms=t.renderInteractiveForms||!1,this.enablePrintAutoRotate=t.enablePrintAutoRotate||!1,this.renderer=t.renderer||a.RendererType.CANVAS,this.l10n=t.l10n||a.NullL10n,this.defaultRenderingQueue=!t.renderingQueue,this.defaultRenderingQueue?(this.renderingQueue=new o.PDFRenderingQueue,this.renderingQueue.setViewer(this)):this.renderingQueue=t.renderingQueue,this.scroll=(0,a.watchScroll)(this.container,this._scrollUpdate.bind(this)),this.presentationModeState=f.UNKNOWN,this._resetView(),this.removePageBorders&&this.viewer.classList.add("removePageBorders")}return i(e,[{key:"getPageView",value:function(e){return this._pages[e]}},{key:"_setCurrentPageNumber",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(this._currentPageNumber!==e){if(0<e&&e<=this.pagesCount){var n={source:this,pageNumber:e,pageLabel:this._pageLabels&&this._pageLabels[e-1]};this._currentPageNumber=e,this.eventBus.dispatch("pagechanging",n),this.eventBus.dispatch("pagechange",n),t&&this._resetCurrentPageView()}}else t&&this._resetCurrentPageView()}},{key:"setDocument",value:function(e){var t=this;if(this.pdfDocument&&(this._cancelRendering(),this._resetView()),this.pdfDocument=e,e){var n=e.numPages,i=(0,r.createPromiseCapability)();this.pagesPromise=i.promise,i.promise.then((function(){t._pageViewsReady=!0,t.eventBus.dispatch("pagesloaded",{source:t,pagesCount:n})}));var o=!1,s=(0,r.createPromiseCapability)();this.onePageRendered=s.promise;var u=function(e){e.onBeforeDraw=function(){t._buffer.push(e)},e.onAfterDraw=function(){o||(o=!0,s.resolve())}},c=e.getPage(1);this.firstPagePromise=c,c.then((function(o){for(var c=t.currentScale,h=o.getViewport(c*a.CSS_UNITS),d=1;d<=n;++d){var f=null;r.PDFJS.disableTextLayer||(f=t);var v=new l.PDFPageView({container:t.viewer,eventBus:t.eventBus,id:d,scale:c,defaultViewport:h.clone(),renderingQueue:t.renderingQueue,textLayerFactory:f,annotationLayerFactory:t,enhanceTextSelection:t.enhanceTextSelection,renderInteractiveForms:t.renderInteractiveForms,renderer:t.renderer,l10n:t.l10n});u(v),t._pages.push(v)}s.promise.then((function(){if(r.PDFJS.disableAutoFetch)i.resolve();else for(var a=n,o=function(n){e.getPage(n).then((function(e){var r=t._pages[n-1];r.pdfPage||r.setPdfPage(e),t.linkService.cachePageRef(n,e.ref),0===--a&&i.resolve()}),(function(e){0===--a&&i.resolve()}))},s=1;s<=n;++s)o(s)})),t.eventBus.dispatch("pagesinit",{source:t}),t.defaultRenderingQueue&&t.update(),t.findController&&t.findController.resolveFirstPage()})).catch((function(e){}))}}},{key:"setPageLabels",value:function(e){if(this.pdfDocument){e&&e instanceof Array&&this.pdfDocument.numPages===e.length?this._pageLabels=e:this._pageLabels=null;for(var t=0,n=this._pages.length;t<n;t++){var i=this._pages[t],r=this._pageLabels&&this._pageLabels[t];i.setPageLabel(r)}}}},{key:"_resetView",value:function(){this._pages=[],this._currentPageNumber=1,this._currentScale=a.UNKNOWN_SCALE,this._currentScaleValue=null,this._pageLabels=null,this._buffer=new g(v),this._location=null,this._pagesRotation=0,this._pagesRequests=[],this._pageViewsReady=!1,this.viewer.textContent=""}},{key:"_scrollUpdate",value:function(){0!==this.pagesCount&&this.update()}},{key:"_setScaleDispatchEvent",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i={source:this,scale:e,presetValue:n?t:void 0};this.eventBus.dispatch("scalechanging",i),this.eventBus.dispatch("scalechange",i)}},{key:"_setScaleUpdatePages",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(this._currentScaleValue=t.toString(),p(this._currentScale,e))i&&this._setScaleDispatchEvent(e,t,!0);else{for(var a=0,o=this._pages.length;a<o;a++)this._pages[a].update(e);if(this._currentScale=e,!n){var s=this._currentPageNumber,u=void 0;!this._location||r.PDFJS.ignoreCurrentPositionOnZoom||this.isInPresentationMode||this.isChangingPresentationMode||(s=this._location.pageNumber,u=[null,{name:"XYZ"},this._location.left,this._location.top,null]),this.scrollPageIntoView({pageNumber:s,destArray:u,allowNegativeOffset:!0})}this._setScaleDispatchEvent(e,t,i),this.defaultRenderingQueue&&this.update()}}},{key:"_setScale",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=parseFloat(e);if(n>0)this._setScaleUpdatePages(n,e,t,!1);else{var i=this._pages[this._currentPageNumber-1];if(!i)return;var r=this.isInPresentationMode||this.removePageBorders?0:a.SCROLLBAR_PADDING,o=this.isInPresentationMode||this.removePageBorders?0:a.VERTICAL_PADDING,s=(this.container.clientWidth-r)/i.width*i.scale,u=(this.container.clientHeight-o)/i.height*i.scale;switch(e){case"page-actual":n=1;break;case"page-width":n=s;break;case"page-height":n=u;break;case"page-fit":n=Math.min(s,u);break;case"auto":var l=i.width>i.height,c=l?Math.min(u,s):s;n=Math.min(a.MAX_AUTO_SCALE,c);break;default:return}this._setScaleUpdatePages(n,e,t,!0)}}},{key:"_resetCurrentPageView",value:function(){this.isInPresentationMode&&this._setScale(this._currentScaleValue,!0);var e=this._pages[this._currentPageNumber-1];(0,a.scrollIntoView)(e.div)}},{key:"scrollPageIntoView",value:function(e){if(this.pdfDocument){if(arguments.length>1||"number"===typeof e){var t={};"number"===typeof e&&(t.pageNumber=e),arguments[1]instanceof Array&&(t.destArray=arguments[1]),e=t}var n=e.pageNumber||0,i=e.destArray||null,r=e.allowNegativeOffset||!1;if(!this.isInPresentationMode&&i){var o=this._pages[n-1];if(o){var s=0,u=0,l=0,c=0,h=void 0,d=void 0,f=o.rotation%180!==0,v=(f?o.height:o.width)/o.scale/a.CSS_UNITS,g=(f?o.width:o.height)/o.scale/a.CSS_UNITS,p=0;switch(i[1].name){case"XYZ":s=i[2],u=i[3],p=i[4],s=null!==s?s:0,u=null!==u?u:g;break;case"Fit":case"FitB":p="page-fit";break;case"FitH":case"FitBH":u=i[2],p="page-width",null===u&&this._location&&(s=this._location.left,u=this._location.top);break;case"FitV":case"FitBV":s=i[2],l=v,c=g,p="page-height";break;case"FitR":s=i[2],u=i[3],l=i[4]-s,c=i[5]-u;var m=this.removePageBorders?0:a.SCROLLBAR_PADDING,w=this.removePageBorders?0:a.VERTICAL_PADDING;h=(this.container.clientWidth-m)/l/a.CSS_UNITS,d=(this.container.clientHeight-w)/c/a.CSS_UNITS,p=Math.min(Math.abs(h),Math.abs(d));break;default:return}if(p&&p!==this._currentScale?this.currentScaleValue=p:this._currentScale===a.UNKNOWN_SCALE&&(this.currentScaleValue=a.DEFAULT_SCALE_VALUE),"page-fit"!==p||i[4]){var b=[o.viewport.convertToViewportPoint(s,u),o.viewport.convertToViewportPoint(s+l,u+c)],y=Math.min(b[0][0],b[1][0]),P=Math.min(b[0][1],b[1][1]);r||(y=Math.max(y,0),P=Math.max(P,0)),(0,a.scrollIntoView)(o.div,{left:y,top:P})}else(0,a.scrollIntoView)(o.div)}}else this._setCurrentPageNumber(n,!0)}}},{key:"_updateLocation",value:function(e){var t=this._currentScale,n=this._currentScaleValue,i=parseFloat(n)===t?Math.round(1e4*t)/100:n,r=e.id,a="#page="+r;a+="&zoom="+i;var o=this._pages[r-1],s=this.container,u=o.getPagePoint(s.scrollLeft-e.x,s.scrollTop-e.y),l=Math.round(u[0]),c=Math.round(u[1]);a+=","+l+","+c,this._location={pageNumber:r,scale:i,top:c,left:l,pdfOpenParams:a}}},{key:"update",value:function(){var e=this._getVisiblePages(),t=e.views;if(0!==t.length){var n=Math.max(v,2*t.length+1);this._buffer.resize(n),this.renderingQueue.renderHighestPriority(e);for(var i=this._currentPageNumber,r=e.first,a=!1,o=0,s=t.length;o<s;++o){var u=t[o];if(u.percent<100)break;if(u.id===i){a=!0;break}}a||(i=t[0].id),this.isInPresentationMode||this._setCurrentPageNumber(i),this._updateLocation(r),this.eventBus.dispatch("updateviewarea",{source:this,location:this._location})}}},{key:"containsElement",value:function(e){return this.container.contains(e)}},{key:"focus",value:function(){this.container.focus()}},{key:"_getVisiblePages",value:function(){if(!this.isInPresentationMode)return(0,a.getVisibleElements)(this.container,this._pages,!0);var e=[],t=this._pages[this._currentPageNumber-1];return e.push({id:t.id,view:t}),{first:t,last:t,views:e}}},{key:"cleanup",value:function(){for(var e=0,t=this._pages.length;e<t;e++)this._pages[e]&&this._pages[e].renderingState!==o.RenderingStates.FINISHED&&this._pages[e].reset()}},{key:"_cancelRendering",value:function(){for(var e=0,t=this._pages.length;e<t;e++)this._pages[e]&&this._pages[e].cancelRendering()}},{key:"_ensurePdfPageLoaded",value:function(e){var t=this;if(e.pdfPage)return Promise.resolve(e.pdfPage);var n=e.id;if(this._pagesRequests[n])return this._pagesRequests[n];var i=this.pdfDocument.getPage(n).then((function(i){return e.pdfPage||e.setPdfPage(i),t._pagesRequests[n]=null,i})).catch((function(e){t._pagesRequests[n]=null}));return this._pagesRequests[n]=i,i}},{key:"forceRendering",value:function(e){var t=this,n=e||this._getVisiblePages(),i=this.renderingQueue.getHighestPriority(n,this._pages,this.scroll.down);return!!i&&(this._ensurePdfPageLoaded(i).then((function(){t.renderingQueue.renderView(i)})),!0)}},{key:"getPageTextContent",value:function(e){return this.pdfDocument.getPage(e+1).then((function(e){return e.getTextContent({normalizeWhitespace:!0})}))}},{key:"createTextLayerBuilder",value:function(e,t,n){var i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];return new h.TextLayerBuilder({textLayerDiv:e,eventBus:this.eventBus,pageIndex:t,viewport:n,findController:this.isInPresentationMode?null:this.findController,enhanceTextSelection:!this.isInPresentationMode&&i})}},{key:"createAnnotationLayerBuilder",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:a.NullL10n;return new s.AnnotationLayerBuilder({pageDiv:e,pdfPage:t,renderInteractiveForms:n,linkService:this.linkService,downloadManager:this.downloadManager,l10n:i})}},{key:"setFindController",value:function(e){this.findController=e}},{key:"getPagesOverview",value:function(){var e=this._pages.map((function(e){var t=e.pdfPage.getViewport(1);return{width:t.width,height:t.height,rotation:t.rotation}}));if(!this.enablePrintAutoRotate)return e;var t=m(e[0]);return e.map((function(e){return t===m(e)?e:{width:e.height,height:e.width,rotation:(e.rotation+90)%360}}))}},{key:"pagesCount",get:function(){return this._pages.length}},{key:"pageViewsReady",get:function(){return this._pageViewsReady}},{key:"currentPageNumber",get:function(){return this._currentPageNumber},set:function(e){if((0|e)!==e)throw new Error("Invalid page number.");this.pdfDocument&&this._setCurrentPageNumber(e,!0)}},{key:"currentPageLabel",get:function(){return this._pageLabels&&this._pageLabels[this._currentPageNumber-1]},set:function(e){var t=0|e;if(this._pageLabels){var n=this._pageLabels.indexOf(e);n>=0&&(t=n+1)}this.currentPageNumber=t}},{key:"currentScale",get:function(){return this._currentScale!==a.UNKNOWN_SCALE?this._currentScale:a.DEFAULT_SCALE},set:function(e){if(isNaN(e))throw new Error("Invalid numeric scale");this.pdfDocument&&this._setScale(e,!1)}},{key:"currentScaleValue",get:function(){return this._currentScaleValue},set:function(e){this.pdfDocument&&this._setScale(e,!1)}},{key:"pagesRotation",get:function(){return this._pagesRotation},set:function(e){if("number"!==typeof e||e%90!==0)throw new Error("Invalid pages rotation angle.");if(this.pdfDocument){this._pagesRotation=e;for(var t=0,n=this._pages.length;t<n;t++){var i=this._pages[t];i.update(i.scale,e)}this._setScale(this._currentScaleValue,!0),this.defaultRenderingQueue&&this.update()}}},{key:"isInPresentationMode",get:function(){return this.presentationModeState===f.FULLSCREEN}},{key:"isChangingPresentationMode",get:function(){return this.presentationModeState===f.CHANGING}},{key:"isHorizontalScrollbarEnabled",get:function(){return!this.isInPresentationMode&&this.container.scrollWidth>this.container.clientWidth}},{key:"hasEqualPageSizes",get:function(){for(var e=this._pages[0],t=1,n=this._pages.length;t<n;++t){var i=this._pages[t];if(i.width!==e.width||i.height!==e.height)return!1}return!0}}]),e}();t.PresentationModeState=f,t.PDFViewer=w},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.BasePreferences=void 0;var i="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}(),a=n(0);function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var s=null;function u(){return s||(s=Promise.resolve({showPreviousViewOnLoad:!0,defaultZoomValue:"",sidebarViewOnLoad:0,enableHandToolOnLoad:!1,cursorToolOnLoad:0,enableWebGL:!1,pdfBugEnabled:!1,disableRange:!1,disableStream:!1,disableAutoFetch:!1,disableFontFace:!1,disableTextLayer:!1,useOnlyCssZoom:!1,externalLinkTarget:0,enhanceTextSelection:!1,renderer:"canvas",renderInteractiveForms:!1,enablePrintAutoRotate:!1,disablePageMode:!1,disablePageLabels:!1})),s}var l=function(){function e(){var t=this;if(o(this,e),this.constructor===e)throw new Error("Cannot initialize BasePreferences.");this.prefs=null,this._initializedPromise=u().then((function(e){return Object.defineProperty(t,"defaults",{value:Object.freeze(e),writable:!1,enumerable:!0,configurable:!1}),t.prefs=(0,a.cloneObj)(e),t._readFromStorage(e)})).then((function(e){e&&(t.prefs=e)}))}return r(e,[{key:"_writeToStorage",value:function(e){return Promise.reject(new Error("Not implemented: _writeToStorage"))}},{key:"_readFromStorage",value:function(e){return Promise.reject(new Error("Not implemented: _readFromStorage"))}},{key:"reset",value:function(){var e=this;return this._initializedPromise.then((function(){return e.prefs=(0,a.cloneObj)(e.defaults),e._writeToStorage(e.defaults)}))}},{key:"reload",value:function(){var e=this;return this._initializedPromise.then((function(){return e._readFromStorage(e.defaults)})).then((function(t){t&&(e.prefs=t)}))}},{key:"set",value:function(e,t){var n=this;return this._initializedPromise.then((function(){if(void 0===n.defaults[e])throw new Error('Set preference: "'+e+'" is undefined.');if(void 0===t)throw new Error("Set preference: no value is specified.");var r="undefined"===typeof t?"undefined":i(t),a=i(n.defaults[e]);if(r!==a){if("number"!==r||"string"!==a)throw new Error('Set preference: "'+t+'" is a '+r+", expected a "+a+".");t=t.toString()}else if("number"===r&&(0|t)!==t)throw new Error('Set preference: "'+t+'" must be an integer.');return n.prefs[e]=t,n._writeToStorage(n.prefs)}))}},{key:"get",value:function(e){var t=this;return this._initializedPromise.then((function(){var n=t.defaults[e];if(void 0===n)throw new Error('Get preference: "'+e+'" is undefined.');var i=t.prefs[e];return void 0!==i?i:n}))}}]),e}();t.BasePreferences=l},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SecondaryToolbar=void 0;var i=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}(),r=n(6),a=n(0);function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var s=function(){function e(t,n,i){o(this,e),this.toolbar=t.toolbar,this.toggleButton=t.toggleButton,this.toolbarButtonContainer=t.toolbarButtonContainer,this.buttons=[{element:t.presentationModeButton,eventName:"presentationmode",close:!0},{element:t.openFileButton,eventName:"openfile",close:!0},{element:t.printButton,eventName:"print",close:!0},{element:t.downloadButton,eventName:"download",close:!0},{element:t.viewBookmarkButton,eventName:null,close:!0},{element:t.firstPageButton,eventName:"firstpage",close:!0},{element:t.lastPageButton,eventName:"lastpage",close:!0},{element:t.pageRotateCwButton,eventName:"rotatecw",close:!1},{element:t.pageRotateCcwButton,eventName:"rotateccw",close:!1},{element:t.cursorSelectToolButton,eventName:"switchcursortool",eventDetails:{tool:r.CursorTool.SELECT},close:!0},{element:t.cursorHandToolButton,eventName:"switchcursortool",eventDetails:{tool:r.CursorTool.HAND},close:!0},{element:t.documentPropertiesButton,eventName:"documentproperties",close:!0}],this.items={firstPage:t.firstPageButton,lastPage:t.lastPageButton,pageRotateCw:t.pageRotateCwButton,pageRotateCcw:t.pageRotateCcwButton},this.mainContainer=n,this.eventBus=i,this.opened=!1,this.containerHeight=null,this.previousContainerHeight=null,this.reset(),this._bindClickListeners(),this._bindCursorToolsListener(t),this.eventBus.on("resize",this._setMaxHeight.bind(this))}return i(e,[{key:"setPageNumber",value:function(e){this.pageNumber=e,this._updateUIState()}},{key:"setPagesCount",value:function(e){this.pagesCount=e,this._updateUIState()}},{key:"reset",value:function(){this.pageNumber=0,this.pagesCount=0,this._updateUIState()}},{key:"_updateUIState",value:function(){this.items.firstPage.disabled=this.pageNumber<=1,this.items.lastPage.disabled=this.pageNumber>=this.pagesCount,this.items.pageRotateCw.disabled=0===this.pagesCount,this.items.pageRotateCcw.disabled=0===this.pagesCount}},{key:"_bindClickListeners",value:function(){var e=this;this.toggleButton.addEventListener("click",this.toggle.bind(this));var t=function(t){var n=e.buttons[t],i=n.element,r=n.eventName,a=n.close,o=n.eventDetails;i.addEventListener("click",(function(t){if(null!==r){var n={source:e};for(var i in o)n[i]=o[i];e.eventBus.dispatch(r,n)}a&&e.close()}))};for(var n in this.buttons)t(n)}},{key:"_bindCursorToolsListener",value:function(e){this.eventBus.on("cursortoolchanged",(function(t){switch(e.cursorSelectToolButton.classList.remove("toggled"),e.cursorHandToolButton.classList.remove("toggled"),t.tool){case r.CursorTool.SELECT:e.cursorSelectToolButton.classList.add("toggled");break;case r.CursorTool.HAND:e.cursorHandToolButton.classList.add("toggled");break}}))}},{key:"open",value:function(){this.opened||(this.opened=!0,this._setMaxHeight(),this.toggleButton.classList.add("toggled"),this.toolbar.classList.remove("hidden"))}},{key:"close",value:function(){this.opened&&(this.opened=!1,this.toolbar.classList.add("hidden"),this.toggleButton.classList.remove("toggled"))}},{key:"toggle",value:function(){this.opened?this.close():this.open()}},{key:"_setMaxHeight",value:function(){this.opened&&(this.containerHeight=this.mainContainer.clientHeight,this.containerHeight!==this.previousContainerHeight&&(this.toolbarButtonContainer.setAttribute("style","max-height: "+(this.containerHeight-a.SCROLLBAR_PADDING)+"px;"),this.previousContainerHeight=this.containerHeight))}},{key:"isOpen",get:function(){return this.opened}}]),e}();t.SecondaryToolbar=s},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DefaultTextLayerFactory=t.TextLayerBuilder=void 0;var i=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}(),r=n(2),a=n(1);function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var s=300,u=function(){function e(t){var n=t.textLayerDiv,i=t.eventBus,a=t.pageIndex,s=t.viewport,u=t.findController,l=void 0===u?null:u,c=t.enhanceTextSelection,h=void 0!==c&&c;o(this,e),this.textLayerDiv=n,this.eventBus=i||(0,r.getGlobalEventBus)(),this.textContent=null,this.textContentItemsStr=[],this.textContentStream=null,this.renderingDone=!1,this.pageIdx=a,this.pageNumber=this.pageIdx+1,this.matches=[],this.viewport=s,this.textDivs=[],this.findController=l,this.textLayerRenderTask=null,this.enhanceTextSelection=h,this._bindMouse()}return i(e,[{key:"_finishRendering",value:function(){if(this.renderingDone=!0,!this.enhanceTextSelection){var e=document.createElement("div");e.className="endOfContent",this.textLayerDiv.appendChild(e)}this.eventBus.dispatch("textlayerrendered",{source:this,pageNumber:this.pageNumber,numTextDivs:this.textDivs.length})}},{key:"render",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;if((this.textContent||this.textContentStream)&&!this.renderingDone){this.cancel(),this.textDivs=[];var n=document.createDocumentFragment();this.textLayerRenderTask=(0,a.renderTextLayer)({textContent:this.textContent,textContentStream:this.textContentStream,container:n,viewport:this.viewport,textDivs:this.textDivs,textContentItemsStr:this.textContentItemsStr,timeout:t,enhanceTextSelection:this.enhanceTextSelection}),this.textLayerRenderTask.promise.then((function(){e.textLayerDiv.appendChild(n),e._finishRendering(),e.updateMatches()}),(function(e){}))}}},{key:"cancel",value:function(){this.textLayerRenderTask&&(this.textLayerRenderTask.cancel(),this.textLayerRenderTask=null)}},{key:"setTextContentStream",value:function(e){this.cancel(),this.textContentStream=e}},{key:"setTextContent",value:function(e){this.cancel(),this.textContent=e}},{key:"convertMatches",value:function(e,t){var n=0,i=0,r=this.textContentItemsStr,a=r.length-1,o=null===this.findController?0:this.findController.state.query.length,s=[];if(!e)return s;for(var u=0,l=e.length;u<l;u++){var c=e[u];while(n!==a&&c>=i+r[n].length)i+=r[n].length,n++;r.length;var h={begin:{divIdx:n,offset:c-i}};c+=t?t[u]:o;while(n!==a&&c>i+r[n].length)i+=r[n].length,n++;h.end={divIdx:n,offset:c-i},s.push(h)}return s}},{key:"renderMatches",value:function(e){if(0!==e.length){var t=this.textContentItemsStr,n=this.textDivs,i=null,r=this.pageIdx,a=null!==this.findController&&r===this.findController.selected.pageIdx,o=null===this.findController?-1:this.findController.selected.matchIdx,s=null!==this.findController&&this.findController.state.highlightAll,u={divIdx:-1,offset:void 0},l=o,c=l+1;if(s)l=0,c=e.length;else if(!a)return;for(var h=l;h<c;h++){var d=e[h],f=d.begin,v=d.end,g=a&&h===o,p=g?" selected":"";if(this.findController&&this.findController.updateMatchPosition(r,h,n,f.divIdx),i&&f.divIdx===i.divIdx?y(i.divIdx,i.offset,f.offset):(null!==i&&y(i.divIdx,i.offset,u.offset),b(f)),f.divIdx===v.divIdx)y(f.divIdx,f.offset,v.offset,"highlight"+p);else{y(f.divIdx,f.offset,u.offset,"highlight begin"+p);for(var m=f.divIdx+1,w=v.divIdx;m<w;m++)n[m].className="highlight middle"+p;b(v,"highlight end"+p)}i=v}i&&y(i.divIdx,i.offset,u.offset)}function b(e,t){var i=e.divIdx;n[i].textContent="",y(i,0,e.offset,t)}function y(e,i,r,a){var o=n[e],s=t[e].substring(i,r),u=document.createTextNode(s);if(a){var l=document.createElement("span");return l.className=a,l.appendChild(u),void o.appendChild(l)}o.appendChild(u)}}},{key:"updateMatches",value:function(){if(this.renderingDone){for(var e=this.matches,t=this.textDivs,n=this.textContentItemsStr,i=-1,r=0,a=e.length;r<a;r++){for(var o=e[r],s=Math.max(i,o.begin.divIdx),u=s,l=o.end.divIdx;u<=l;u++){var c=t[u];c.textContent=n[u],c.className=""}i=o.end.divIdx+1}if(null!==this.findController&&this.findController.active){var h=void 0,d=void 0;null!==this.findController&&(h=this.findController.pageMatches[this.pageIdx]||null,d=this.findController.pageMatchesLength&&this.findController.pageMatchesLength[this.pageIdx]||null),this.matches=this.convertMatches(h,d),this.renderMatches(this.matches)}}}},{key:"_bindMouse",value:function(){var e=this,t=this.textLayerDiv,n=null;t.addEventListener("mousedown",(function(i){if(e.enhanceTextSelection&&e.textLayerRenderTask)return e.textLayerRenderTask.expandTextDivs(!0),void(n&&(clearTimeout(n),n=null));var r=t.querySelector(".endOfContent");if(r){var a=i.target!==t;if(a=a&&"none"!==window.getComputedStyle(r).getPropertyValue("-moz-user-select"),a){var o=t.getBoundingClientRect(),s=Math.max(0,(i.pageY-o.top)/o.height);r.style.top=(100*s).toFixed(2)+"%"}r.classList.add("active")}})),t.addEventListener("mouseup",(function(){if(e.enhanceTextSelection&&e.textLayerRenderTask)n=setTimeout((function(){e.textLayerRenderTask&&e.textLayerRenderTask.expandTextDivs(!1),n=null}),s);else{var i=t.querySelector(".endOfContent");i&&(i.style.top="",i.classList.remove("active"))}}))}}]),e}(),l=function(){function e(){o(this,e)}return i(e,[{key:"createTextLayerBuilder",value:function(e,t,n){var i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];return new u({textLayerDiv:e,pageIndex:t,viewport:n,enhanceTextSelection:i})}}]),e}();t.TextLayerBuilder=u,t.DefaultTextLayerFactory=l},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Toolbar=void 0;var i=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}(),r=n(0);function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var o="visiblePageIsLoading",s=8,u=22,l=function(){function e(t,n,i){var o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:r.NullL10n;a(this,e),this.toolbar=t.container,this.mainContainer=n,this.eventBus=i,this.l10n=o,this.items=t,this._wasLocalized=!1,this.reset(),this._bindListeners()}return i(e,[{key:"setPageNumber",value:function(e,t){this.pageNumber=e,this.pageLabel=t,this._updateUIState(!1)}},{key:"setPagesCount",value:function(e,t){this.pagesCount=e,this.hasPageLabels=t,this._updateUIState(!0)}},{key:"setPageScale",value:function(e,t){this.pageScaleValue=e,this.pageScale=t,this._updateUIState(!1)}},{key:"reset",value:function(){this.pageNumber=0,this.pageLabel=null,this.hasPageLabels=!1,this.pagesCount=0,this.pageScaleValue=r.DEFAULT_SCALE_VALUE,this.pageScale=r.DEFAULT_SCALE,this._updateUIState(!0)}},{key:"_bindListeners",value:function(){var e=this,t=this.eventBus,n=this.items,i=this;n.previous.addEventListener("click",(function(){t.dispatch("previouspage")})),n.next.addEventListener("click",(function(){t.dispatch("nextpage")})),n.zoomIn.addEventListener("click",(function(){t.dispatch("zoomin")})),n.zoomOut.addEventListener("click",(function(){t.dispatch("zoomout")})),n.pageNumber.addEventListener("click",(function(){this.select()})),n.pageNumber.addEventListener("change",(function(){t.dispatch("pagenumberchanged",{source:i,value:this.value})})),n.scaleSelect.addEventListener("change",(function(){"custom"!==this.value&&t.dispatch("scalechanged",{source:i,value:this.value})})),n.presentationModeButton.addEventListener("click",(function(){t.dispatch("presentationmode")})),n.openFile.addEventListener("click",(function(){t.dispatch("openfile")})),n.print.addEventListener("click",(function(){t.dispatch("print")})),n.download.addEventListener("click",(function(){t.dispatch("download")})),n.scaleSelect.oncontextmenu=r.noContextMenuHandler,t.on("localized",(function(){e._localized()}))}},{key:"_localized",value:function(){this._wasLocalized=!0,this._adjustScaleWidth(),this._updateUIState(!0)}},{key:"_updateUIState",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(this._wasLocalized){var t=this.pageNumber,n=this.pagesCount,i=this.items,a=(this.pageScaleValue||this.pageScale).toString(),o=this.pageScale;e&&(this.hasPageLabels?i.pageNumber.type="text":(i.pageNumber.type="number",this.l10n.get("of_pages",{pagesCount:n},"of {{pagesCount}}").then((function(e){i.numPages.textContent=e}))),i.pageNumber.max=n),this.hasPageLabels?(i.pageNumber.value=this.pageLabel,this.l10n.get("page_of_pages",{pageNumber:t,pagesCount:n},"({{pageNumber}} of {{pagesCount}})").then((function(e){i.numPages.textContent=e}))):i.pageNumber.value=t,i.previous.disabled=t<=1,i.next.disabled=t>=n,i.zoomOut.disabled=o<=r.MIN_SCALE,i.zoomIn.disabled=o>=r.MAX_SCALE;var s=Math.round(1e4*o)/100;this.l10n.get("page_scale_percent",{scale:s},"{{scale}}%").then((function(e){for(var t=i.scaleSelect.options,n=!1,r=0,o=t.length;r<o;r++){var s=t[r];s.value===a?(s.selected=!0,n=!0):s.selected=!1}n||(i.customScaleOption.textContent=e,i.customScaleOption.selected=!0)}))}}},{key:"updateLoadingIndicatorState",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=this.items.pageNumber;e?t.classList.add(o):t.classList.remove(o)}},{key:"_adjustScaleWidth",value:function(){var e=this.items.scaleSelectContainer,t=this.items.scaleSelect;r.animationStarted.then((function(){if(0===e.clientWidth&&e.setAttribute("style","display: inherit;"),e.clientWidth>0){t.setAttribute("style","min-width: inherit;");var n=t.clientWidth+s;t.setAttribute("style","min-width: "+(n+u)+"px;"),e.setAttribute("style","min-width: "+n+"px; max-width: "+n+"px;")}}))}}]),e}();t.Toolbar=l},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}();function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var a=20,o=function(){function e(t){var n=this,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a;r(this,e),this.fingerprint=t,this.cacheSize=i,this._initializedPromise=this._readFromStorage().then((function(e){var t=JSON.parse(e||"{}");"files"in t||(t.files=[]),t.files.length>=n.cacheSize&&t.files.shift();for(var i=void 0,r=0,a=t.files.length;r<a;r++){var o=t.files[r];if(o.fingerprint===n.fingerprint){i=r;break}}"number"!==typeof i&&(i=t.files.push({fingerprint:n.fingerprint})-1),n.file=t.files[i],n.database=t}))}return i(e,[{key:"_writeToStorage",value:function(){var e=this;return new Promise((function(t){var n=JSON.stringify(e.database);localStorage.setItem("pdfjs.history",n),t()}))}},{key:"_readFromStorage",value:function(){return new Promise((function(e){var t=localStorage.getItem("pdfjs.history");if(!t){var n=localStorage.getItem("database");if(n)try{var i=JSON.parse(n);"string"===typeof i.files[0].fingerprint&&(localStorage.setItem("pdfjs.history",n),localStorage.removeItem("database"),t=n)}catch(r){}}e(t)}))}},{key:"set",value:function(e,t){var n=this;return this._initializedPromise.then((function(){return n.file[e]=t,n._writeToStorage()}))}},{key:"setMultiple",value:function(e){var t=this;return this._initializedPromise.then((function(){for(var n in e)t.file[n]=e[n];return t._writeToStorage()}))}},{key:"get",value:function(e,t){var n=this;return this._initializedPromise.then((function(){var i=n.file[e];return void 0!==i?i:t}))}},{key:"getMultiple",value:function(e){var t=this;return this._initializedPromise.then((function(){var n=Object.create(null);for(var i in e){var r=t.file[i];n[i]=void 0!==r?r:e[i]}return n}))}}]),e}();t.ViewHistory=o},function(e,t,n){"use strict";var i="",r=void 0;function a(){return{appContainer:document.body,mainContainer:document.getElementById("viewerContainer"),viewerContainer:document.getElementById("viewer"),eventBus:null,toolbar:{container:document.getElementById("toolbarViewer"),numPages:document.getElementById("numPages"),pageNumber:document.getElementById("pageNumber"),scaleSelectContainer:document.getElementById("scaleSelectContainer"),scaleSelect:document.getElementById("scaleSelect"),customScaleOption:document.getElementById("customScaleOption"),previous:document.getElementById("previous"),next:document.getElementById("next"),zoomIn:document.getElementById("zoomIn"),zoomOut:document.getElementById("zoomOut"),viewFind:document.getElementById("viewFind"),openFile:document.getElementById("openFile"),print:document.getElementById("print"),presentationModeButton:document.getElementById("presentationMode"),download:document.getElementById("download"),viewBookmark:document.getElementById("viewBookmark")},secondaryToolbar:{toolbar:document.getElementById("secondaryToolbar"),toggleButton:document.getElementById("secondaryToolbarToggle"),toolbarButtonContainer:document.getElementById("secondaryToolbarButtonContainer"),presentationModeButton:document.getElementById("secondaryPresentationMode"),openFileButton:document.getElementById("secondaryOpenFile"),printButton:document.getElementById("secondaryPrint"),downloadButton:document.getElementById("secondaryDownload"),viewBookmarkButton:document.getElementById("secondaryViewBookmark"),firstPageButton:document.getElementById("firstPage"),lastPageButton:document.getElementById("lastPage"),pageRotateCwButton:document.getElementById("pageRotateCw"),pageRotateCcwButton:document.getElementById("pageRotateCcw"),cursorSelectToolButton:document.getElementById("cursorSelectTool"),cursorHandToolButton:document.getElementById("cursorHandTool"),documentPropertiesButton:document.getElementById("documentProperties")},fullscreen:{contextFirstPage:document.getElementById("contextFirstPage"),contextLastPage:document.getElementById("contextLastPage"),contextPageRotateCw:document.getElementById("contextPageRotateCw"),contextPageRotateCcw:document.getElementById("contextPageRotateCcw")},sidebar:{mainContainer:document.getElementById("mainContainer"),outerContainer:document.getElementById("outerContainer"),toggleButton:document.getElementById("sidebarToggle"),thumbnailButton:document.getElementById("viewThumbnail"),outlineButton:document.getElementById("viewOutline"),attachmentsButton:document.getElementById("viewAttachments"),thumbnailView:document.getElementById("thumbnailView"),outlineView:document.getElementById("outlineView"),attachmentsView:document.getElementById("attachmentsView")},findBar:{bar:document.getElementById("findbar"),toggleButton:document.getElementById("viewFind"),findField:document.getElementById("findInput"),highlightAllCheckbox:document.getElementById("findHighlightAll"),caseSensitiveCheckbox:document.getElementById("findMatchCase"),findMsg:document.getElementById("findMsg"),findResultsCount:document.getElementById("findResultsCount"),findStatusIcon:document.getElementById("findStatusIcon"),findPreviousButton:document.getElementById("findPrevious"),findNextButton:document.getElementById("findNext")},passwordOverlay:{overlayName:"passwordOverlay",container:document.getElementById("passwordOverlay"),label:document.getElementById("passwordText"),input:document.getElementById("password"),submitButton:document.getElementById("passwordSubmit"),cancelButton:document.getElementById("passwordCancel")},documentProperties:{overlayName:"documentPropertiesOverlay",container:document.getElementById("documentPropertiesOverlay"),closeButton:document.getElementById("documentPropertiesClose"),fields:{fileName:document.getElementById("fileNameField"),fileSize:document.getElementById("fileSizeField"),title:document.getElementById("titleField"),author:document.getElementById("authorField"),subject:document.getElementById("subjectField"),keywords:document.getElementById("keywordsField"),creationDate:document.getElementById("creationDateField"),modificationDate:document.getElementById("modificationDateField"),creator:document.getElementById("creatorField"),producer:document.getElementById("producerField"),version:document.getElementById("versionField"),pageCount:document.getElementById("pageCountField")}},errorWrapper:{container:document.getElementById("errorWrapper"),errorMessage:document.getElementById("errorMessage"),closeButton:document.getElementById("errorClose"),errorMoreInfo:document.getElementById("errorMoreInfo"),moreInfoButton:document.getElementById("errorShowMore"),lessInfoButton:document.getElementById("errorShowLess")},printContainer:document.getElementById("printContainer"),openFileInputName:"fileInput",debuggerScriptPath:"./debugger.js",defaultUrl:i}}function o(){var e=a();window.PDFViewerApplication=r.PDFViewerApplication,r.PDFViewerApplication.run(e)}r=n(4),n(8),n(9),"interactive"===document.readyState||"complete"===document.readyState?o():document.addEventListener("DOMContentLoaded",o,!0)}]);
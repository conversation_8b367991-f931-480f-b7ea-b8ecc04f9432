"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[9637],{88412:function(t,e,i){var a=i(26263),o=i(36766),r=i(1001),l=(0,r.Z)(o.Z,a.s,a.x,!1,null,"5e7ef0ae",null);e["Z"]=l.exports},39637:function(t,e,i){i.r(e),i.d(e,{default:function(){return u}});var a=function(){var t=this,e=this,i=e.$createElement,a=e._self._c||i;return a("div",{staticClass:"fit"},[a("ta-border-layout",{attrs:{"layout-type":"fixTop"}},[a("div",{attrs:{slot:"header"},slot:"header"},[a("ta-title",{attrs:{title:"查询表格"}}),a("ta-form",{attrs:{formLayout:"","auto-form-create":function(e){return t.queryParamForm=e}}},[a("ta-form-item",{attrs:{"field-decorator-id":"recordTime","field-decorator-options":{rules:[{required:!0,message:"必须选取日期"}]},required:!0,"init-value":e.defaultValue,span:5}},[a("span",{attrs:{slot:"label"},slot:"label"},[e._v("请求时间")]),a("ta-range-picker",{attrs:{"allow-one":!0}})],1),a("ta-form-item",{attrs:{"field-decorator-id":"aaz217",label:"审核流水号","label-col":{span:8},span:4,"wrapper-col":{span:16}}},[a("ta-input")],1),a("ta-form-item",{attrs:{"field-decorator-id":"akc190",label:"住院号",span:4}},[a("ta-input")],1),a("ta-form-item",{attrs:{fieldDecoratorId:"trigScen",span:4}},[a("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("场景")]),a("ta-select",{attrs:{placeholder:"场景选择","collection-type":"AAE500","dropdown-match-select-width":!1,"dropdown-style":{width:"200px"},allowClear:""}})],1),a("ta-form-item",{attrs:{span:4,"wrapper-col":{span:15},"field-decorator-id":"rqtimecom",initValue:"up",label:"总耗时"}},[a("ta-select",{staticStyle:{width:"38%"}},[a("ta-select-option",{attrs:{value:"up"}},[e._v(" >")]),a("ta-select-option",{attrs:{value:"down"}},[e._v(" <")])],1),a("ta-input-number",{staticStyle:{width:"62%"},attrs:{formatter:function(t){return""===t?"":t+"ms"},max:1e5,min:0,parser:function(t){return t.replace("ms","")}},model:{value:e.rqtime,callback:function(t){e.rqtime=t},expression:"rqtime"}})],1),a("ta-button",{attrs:{type:"primary",icon:"search"},on:{click:e.fnQuery}},[e._v("查询")]),a("ta-button",{attrs:{type:"default",icon:"redo"},on:{click:e.fnReset}},[e._v("重置")])],1)],1),a("div",{staticClass:"fit content-box"},[a("div",{staticClass:"content-title"},[a("ta-title",{attrs:{title:"查询结果"}})],1),a("div",{staticClass:"table-content"},[a("ta-big-table",{ref:"Table",attrs:{border:"",height:"auto",columns:e.auditLogTable,data:e.auditLogsListList,"auto-resize":"",resizable:"","highlight-hover-row":"","show-overflow":""},on:{"cell-click":e.cellClickEvent},scopedSlots:e._u([{key:"methodTime",fn:function(t){var i=t.row;return[a("a",[a("span",{staticStyle:{color:"#1b65b9"}},[e._v(e._s(i.methodTime))])])]}}])},[a("template",{slot:"bottomBar"},[a("ta-pagination",{ref:"gridPager",staticStyle:{"text-align":"right","margin-right":"100px"},attrs:{defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"],"data-source":e.auditLogsListList,params:e.infoPageParams,url:"auditLogs/queryAuditLogs"},on:{"update:dataSource":function(t){e.auditLogsListList=t},"update:data-source":function(t){e.auditLogsListList=t}}})],1)],2)],1)])]),a("div",[a("ta-drawer",{attrs:{title:e.drawerTitle,placement:"right",visible:e.visible,width:700},on:{close:e.onClose}},[[a("ta-form",{attrs:{autoFormCreate:function(e){t.form=e}}},[a("ta-form-item",{attrs:{label:"",fieldDecoratorId:"textarea",initValue:""}},[a("ta-textarea",{attrs:{rows:27}})],1)],1)]],2)],1)],1)},o=[],r=i(88412),l=[{title:"审核流水号(aaz217)",field:"aaz217",width:180,align:"left",overflowTooltip:!0},{title:"住院号(akc190)",field:"akc190",width:200,align:"left",overflowTooltip:!0},{title:"住院次数",field:"akc200",width:80,align:"right",overflowTooltip:!0},{title:"场景",field:"trigScen",width:150,align:"left",collectionType:"AAE500",overflowTooltip:!0},{title:"流程时间记录",field:"methodTime",width:500,align:"left",overflowTooltip:!0,scopedSlots:{customRender:"methodTime"},customRender:{default:"methodTime"}},{title:"总耗时(ms)",field:"totalTime",width:100,align:"right",overflowTooltip:!0},{title:"引擎耗时(ms)",field:"checkTime",width:110,align:"right",overflowTooltip:!0},{title:"同步数据时间(ms)",field:"syncTime",width:140,align:"right",overflowTooltip:!0},{title:"请求时间",field:"recordTime",width:190,align:"center",overflowTooltip:!0}],s={components:{TaTitle:r.Z},data:function(){return{drawerTitle:"",rqtime:"",defaultValue:[this.Base.getMoment((new Date).toISOString().slice(0,10),"YYYY-MM-DD"),this.Base.getMoment((new Date).toISOString().slice(0,10),"YYYY-MM-DD")],visible:!1,auditLogTable:l,auditLogsListList:[]}},mounted:function(){this.fnQuery()},methods:{fnReset:function(){this.rqtime="",this.queryParamForm.resetFields(),this.fnQuery()},infoPageParams:function(){var t=this.queryParamForm.getFieldsValue();return t.startDate=t.recordTime[0].format("YYYY-MM-DD HH:mm:ss"),t.endDate=t.recordTime[1].format("YYYY-MM-DD HH:mm:ss"),t.rqtime=this.rqtime,t.rqtimecom=this.rqtime||0===this.rqtime?t.rqtimecom:"",t},fnQuery:function(){this.$refs.gridPager.loadData((function(t){}))},cellClickEvent:function(t){var e=this,i=t.row,a=t.column;this.drawerTitle=a.title,"流程时间记录"==a.title&&(this.visible=!0,this.$nextTick((function(){e.form.setFieldsValue({textarea:[i.methodTime]})})))},onClose:function(){this.visible=!1}}},n=s,c=i(1001),d=(0,c.Z)(n,a,o,!1,null,"35809815",null),u=d.exports},36766:function(t,e,i){var a=i(66586);e["Z"]=a.Z},26263:function(t,e,i){i.d(e,{s:function(){return a},x:function(){return o}});var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("h3",{staticStyle:{"font-weight":"normal"}},[i("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),i("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},o=[]},66586:function(t,e){var i={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};e["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:i}}}}}]);
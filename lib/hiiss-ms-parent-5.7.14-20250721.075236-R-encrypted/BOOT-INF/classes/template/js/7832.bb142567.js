"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[7832],{88412:function(e,t,a){var i=a(26263),n=a(36766),r=a(1001),l=(0,r.Z)(n.Z,i.s,i.x,!1,null,"5e7ef0ae",null);t["Z"]=l.exports},7832:function(e,t,a){a.r(t),a.d(t,{default:function(){return w}});var i=function(){var e=this,t=this,a=t.$createElement,i=t._self._c||a;return i("div",[i("ta-tabs",{attrs:{defaultActiveKey:"1",type:"card"},on:{change:t.callback}},[i("ta-tab-pane",{key:"1",attrs:{tab:"按项目配置"}},[i("div",{staticStyle:{"min-width":"800px",overflow:"auto"},style:{width:"100%",height:t.height+"px"}},[i("ta-border-layout",{attrs:{showPadding:!1,"footer-cfg":{showBorder:!1},layout:{header:"145px",footer:"60px"}}},[i("div",{staticStyle:{"padding-top":"20px"},attrs:{slot:"header"},slot:"header"},[i("ta-form",{attrs:{autoFormCreate:function(t){return e.form=t},layout:"horizontal",formLayout:!0,"label-width":"120px"}},[i("ta-form-item",{attrs:{label:"规则运行场景",fieldDecoratorId:"ykz108",span:6,require:{message:""}}},[i("ta-select",{attrs:{placeholder:"规则运行场景筛选",dropdownMatchSelectWidth:!1,dropdownStyle:{width:"250px"},allowClear:"",options:t.options,optionsKey:{value:"id",label:"name"},showSearch:"",optionFilterProp:"children"},on:{change:t.selectChange}})],1),i("ta-form-item",{attrs:{label:"项目名称",fieldDecoratorId:"ake002",span:6}},[i("ta-input",{attrs:{placeholder:"输入项目名称"}})],1),i("ta-form-item",{attrs:{label:"项目编码",fieldDecoratorId:"ake001",span:6}},[i("ta-input",{attrs:{placeholder:"输入项目编码"}})],1),i("ta-form-item",{attrs:{label:"配置状态",fieldDecoratorId:"state",span:5,"init-value":"1"}},[i("ta-select",{attrs:{placeholder:"配置状态筛选"},on:{change:t.stateChange}},[i("ta-select-option",{attrs:{value:""}},[t._v("全部")]),i("ta-select-option",{attrs:{value:"0"}},[t._v("未配置")]),i("ta-select-option",{attrs:{value:"1"}},[t._v("已配置（项目）")]),i("ta-select-option",{attrs:{value:"2"}},[t._v("已配置（大类）")])],1)],1),i("ta-form-item",{attrs:{label:"引导信息",fieldDecoratorId:"ydxx",span:6}},[i("ta-input",{attrs:{placeholder:"请输入引导信息"}})],1),i("ta-form-item",{attrs:{label:"限制条件",fieldDecoratorId:"ykz018",span:6}},[i("ta-input",{attrs:{placeholder:"请输入限制条件"}})],1),i("ta-form-item",{attrs:{label:"备选操作",fieldDecoratorId:"mode","init-value":"1",span:6}},[i("ta-select",{staticStyle:{width:"35%"}},[i("ta-select-option",{attrs:{value:"1"}},[t._v("模糊匹配")]),i("ta-select-option",{attrs:{value:"2"}},[t._v("精确匹配")])],1),i("ta-select",{staticStyle:{width:"65%"},attrs:{mode:"multiple",maxTagCount:1,showSearch:"",placeholder:"备选操作筛选",allowClear:"",collectionType:"APE893"},model:{value:t.ape893List,callback:function(e){t.ape893List=e},expression:"ape893List"}})],1),i("ta-button",{staticStyle:{"margin-right":"70px",float:"right"},attrs:{icon:"sync",type:"primary"},on:{click:t.showModal}},[t._v(" 刷新缓存 ")]),i("ta-button",{staticStyle:{float:"right"},attrs:{icon:"tool",type:"primary"},on:{click:t.toPeizhi}},[t._v("配置")]),i("ta-button",{staticStyle:{float:"right"},attrs:{icon:"search",type:"primary"},on:{click:t.fnQuery}},[t._v("查询")])],1)],1),i("div",{staticStyle:{display:"flex",height:"100%","flex-direction":"column"}},[i("ta-title",{staticStyle:{flex:"none","margin-left":"5px","margin-top":"5px"},attrs:{title:"配置信息"}},[i("div",{staticStyle:{float:"right"}},[i("ta-button",{attrs:{disabled:t.cancelConfigFlag,type:"primary"},on:{click:function(e){return t.cancelConfig("rule")}}},[t._v("取消配置")])],1)]),i("div",{staticStyle:{flex:"auto"}},[i("ta-big-table",{ref:"infoTableRef",attrs:{columns:t.Columns,data:t.tableData,border:"",height:"auto","highlight-hover-row":"","auto-resize":"","show-overflow":"",resizable:""},on:{"checkbox-all":t.selectAllEvent,"checkbox-change":t.selectChangeEvent},scopedSlots:t._u([{key:"states",fn:function(e){var a=e.row;return["1"==a.state?i("span",[t._v(" 已配置(项目) ")]):t._e(),"2"==a.state?i("span",[t._v(" 已配置(大类) ")]):t._e(),void 0==a.state?i("span",[t._v(" 未配置 ")]):t._e()]}}])})],1)],1),i("div",{attrs:{slot:"footer"},slot:"footer"},[i("ta-pagination",{ref:"paging",staticStyle:{float:"right","margin-top":"10px","margin-right":"20px"},attrs:{params:t.fnGetParams,defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"],dataSource:t.tableData,url:t.URL.queryByPage},on:{"update:dataSource":function(e){t.tableData=e},"update:data-source":function(e){t.tableData=e}}}),i("span",{staticStyle:{float:"right","margin-top":"15px","margin-right":"20px"}},[t._v(" 当前选中"+t._s(this.checkLength)+"条 ")]),i("ta-modal",{attrs:{title:"项目提示信息个性化配置",width:900,visible:t.isModal,bodyStyle:{padding:"0px"},scroll:{y:600},footer:null,"destroy-on-close":!0},on:{cancel:t.Cancel}},[i("project-notification-config",{attrs:{ydxxOptions:t.ydxxOptions,"bx-options":t.bxOptions,"bxcz-alias":t.bxczAlias,record:t.record,flag:t.flag,cjflag:t.cjflag,rowKeys:t.rowKeys},on:{close:t.Cancel}})],1)],1)])],1)]),i("ta-tab-pane",{key:"2",attrs:{tab:"按规则大类配置",forceRender:""}},[i("div",{staticStyle:{"min-width":"800px",overflow:"auto"},style:{width:"100%",height:t.height+"px"}},[i("ta-border-layout",{attrs:{"footer-cfg":{showBorder:!1},showPadding:!1,layout:{header:"100px",footer:"30px"}}},[i("div",{staticStyle:{"padding-top":"20px"},attrs:{slot:"header"},slot:"header"},[i("ta-form",{attrs:{autoFormCreate:function(t){return e.form2=t},layout:"horizontal",formLayout:!0,"label-width":"120px"}},[i("ta-form-item",{attrs:{label:"规则运行场景",fieldDecoratorId:"ykz108",span:5,require:{message:""}}},[i("ta-select",{attrs:{dropdownMatchSelectWidth:!1,dropdownStyle:{width:"250px"},allowClear:"",options:t.options,optionsKey:{value:"id",label:"name"},showSearch:"",optionFilterProp:"children"},on:{change:t.selectChange}})],1),i("ta-form-item",{attrs:{label:"规则大类名称",fieldDecoratorId:"aaa167",span:5}},[i("ta-input",{attrs:{placeholder:"请输入规则大类名称"}})],1),i("ta-form-item",{attrs:{label:"引导信息",fieldDecoratorId:"ydxx",span:5}},[i("ta-input",{attrs:{placeholder:"请输入引导信息"}})],1),i("ta-form-item",{attrs:{label:"配置状态",fieldDecoratorId:"state",span:4,"init-value":"2"}},[i("ta-select",{attrs:{placeholder:"配置状态筛选"}},[i("ta-select-option",{attrs:{value:""}},[t._v("全部")]),i("ta-select-option",{attrs:{value:"0"}},[t._v("未配置")]),i("ta-select-option",{attrs:{value:"2"}},[t._v("已配置（大类）")])],1)],1),i("ta-button",{staticStyle:{"margin-left":"30px",float:"left"},attrs:{icon:"search",type:"primary"},on:{click:t.fnQuery2}},[t._v("查询 ")]),i("ta-button",{staticStyle:{float:"left"},attrs:{icon:"tool",type:"primary"},on:{click:t.toPeizhi2}},[t._v("配置")]),i("ta-button",{staticStyle:{float:"left"},attrs:{icon:"sync",type:"primary"},on:{click:t.showModal}},[t._v("刷新缓存")])],1)],1),i("div",{staticStyle:{display:"flex",height:"100%","flex-direction":"column"}},[i("ta-title",{staticStyle:{flex:"none","margin-left":"5px","margin-top":"5px"},attrs:{title:"配置信息"}},[i("div",{staticStyle:{float:"right"}},[i("ta-button",{attrs:{type:"primary"},on:{click:function(e){return t.cancelConfig("type")}}},[t._v("取消配置")])],1)]),i("div",{staticStyle:{flex:"auto"}},[i("ta-big-table",{ref:"infoTableRef2",attrs:{data:t.tableData2,border:"",height:"auto","highlight-hover-row":"","auto-resize":"","show-overflow":"",resizable:""}},[i("ta-big-table-column",{attrs:{type:"checkbox",width:"50",align:"center"}}),i("ta-big-table-column",{attrs:{type:"seq",title:"序号",width:"60",align:"center"}}),i("ta-big-table-column",{attrs:{field:"aaa167",title:"规则大类名称",minWidth:"300",align:"left"}}),i("ta-big-table-column",{attrs:{field:"ydxx",title:"引导信息",minWidth:"500",align:"left"}}),i("ta-big-table-column",{attrs:{field:"bxcz",title:"备选操作",minWidth:"200",align:"center"}}),i("ta-big-table-column",{attrs:{field:"state",title:"配置状态",width:"auto",align:"center",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return["2"==a.state?i("span",[t._v(" 已配置(大类) ")]):t._e(),void 0==a.state?i("span",[t._v(" 未配置 ")]):t._e()]}}])}),i("ta-big-table-column",{attrs:{field:"aaz319",visible:!1,width:"60",align:"center"}})],1)],1)],1),i("div",{attrs:{slot:"footer"},slot:"footer"},[i("span",{staticStyle:{"margin-left":"48%","font-size":"18px"}},[t._v("共 "),i("span",{staticStyle:{color:"red"}},[t._v("1")]),t._v(" 页/ "),i("span",{staticStyle:{color:"red"}},[t._v(t._s(this.tableData2.length))]),t._v(" 条数据 ")])])])],1)]),i("ta-tab-pane",{key:"3",attrs:{tab:"备案理由模板配置"}},[i("div",{staticStyle:{"min-width":"800px",overflow:"auto"},style:{width:"100%",height:t.height+"px"}},[i("ta-border-layout",{attrs:{showPadding:!1,"footer-cfg":{showBorder:!1},layout:{header:"100px",footer:"60px"}}},[i("div",{staticStyle:{"padding-top":"20px"},attrs:{slot:"header"},slot:"header"},[i("ta-form",{attrs:{autoFormCreate:function(t){return e.form3=t},layout:"horizontal",formLayout:!0,"label-width":"120px"}},[i("ta-form-item",{attrs:{label:"模板类型",fieldDecoratorId:"type",span:4}},[i("ta-select",{attrs:{"show-search":!0,placeholder:"模板类型筛选",options:t.souList,allowClear:""}})],1),i("ta-form-item",{attrs:{label:"备案理由",fieldDecoratorId:"content",span:6}},[i("ta-input",{attrs:{allowClear:"",placeholder:"输入备案理由"}})],1),i("ta-form-item",{attrs:{label:"有效状态",fieldDecoratorId:"useflag",span:4}},[i("ta-select",{attrs:{allowClear:"",placeholder:"状态筛选"}},[i("ta-select-option",{attrs:{value:"1"}},[t._v("有效")]),i("ta-select-option",{attrs:{value:"2"}},[t._v("无效")])],1)],1),i("ta-button",{staticStyle:{float:"right","margin-right":"80px"},attrs:{icon:"tool",type:"primary"},on:{click:t.newAdd}},[t._v("新增 ")]),i("ta-button",{staticStyle:{float:"right","margin-right":"20px"},attrs:{icon:"search",type:"primary"},on:{click:t.fnQuery3}},[t._v("查询 ")])],1)],1),i("div",{staticStyle:{display:"flex",height:"100%","flex-direction":"column"}},[i("ta-title",{staticStyle:{flex:"none","margin-left":"5px"},attrs:{title:"配置信息"}}),i("div",{staticStyle:{flex:"auto"}},[i("ta-big-table",{ref:"infoTableRef3",attrs:{data:t.tableData3,border:"",height:"auto","highlight-hover-row":"","auto-resize":"","show-overflow":"",resizable:""}},[i("ta-big-table-column",{attrs:{type:"seq",title:"序号",width:"60",align:"center"}}),i("ta-big-table-column",{attrs:{field:"realtype",title:"模板类型",minWidth:"100",align:"center"}}),i("ta-big-table-column",{attrs:{field:"content",title:"备案理由模板",minWidth:"500",align:"center"}}),i("ta-big-table-column",{attrs:{field:"",title:"有效状态",width:"auto",align:"center",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return["1"==a.useflag?i("span",[t._v(" 有效 ")]):i("span",[t._v(" 无效 ")])]}}])}),i("ta-big-table-column",{attrs:{fixed:"right",field:"operate",title:"操作",align:"center",width:"200"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[i("ta-table-operate",{attrs:{"operate-menu":t.operateMenu,rowInfo:e}})],1)]}}])})],1)],1)],1),i("div",{attrs:{slot:"footer"},slot:"footer"},[i("ta-pagination",{ref:"paging3",staticStyle:{float:"right","margin-top":"10px","margin-right":"20px"},attrs:{params:t.fnGetParams3,defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"],dataSource:t.tableData3,url:t.URL.queryReasonTemplateInfoList},on:{"update:dataSource":function(e){t.tableData3=e},"update:data-source":function(e){t.tableData3=e}}}),i("ta-modal",{attrs:{title:"新增备案模板",visible:t.visible3,width:320},on:{ok:t.handleOk3,cancel:t.handleCancel3}},[i("ta-form",{attrs:{"auto-form-create":function(t){return e.beianform=t},layout:"horizontal","form-layout":!0,"label-width":"80px",col:{xs:1,sm:1,md:1}}},[i("ta-form-item",{attrs:{label:"备案理由","field-decorator-id":"content",require:!0,"field-decorator-options":{rules:[{validator:t.validReason}]}}},[i("ta-input",{attrs:{placeholder:"请输入内容"}})],1)],1)],1)],1)])],1)])],1),i("div",[i("ta-modal",{staticClass:"ant-modal-body",attrs:{title:"操作确认",visible:t.visible,height:"40",width:"420"},on:{ok:t.handleOk,cancel:t.handleCancel}},[i("p",[t._v(t._s(t.ModalText))])])],1)],1)},n=[],r=a(66347),l=a(48534),s=(a(36133),a(88412)),o=a(10328),c=a(39744),u=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"modal-wrapper",staticStyle:{background:"#F0F2F5"}},[a("div",[a("div",[a("span",{staticStyle:{"font-size":"18px","font-weight":"bolder"}},[e._v("当前配置涉及")]),a("span",{staticStyle:{"font-size":"16px","margin-left":"28%"}},[e._v("规则大类数："+e._s("rule"==this.cjflag?Array.from(new Set(this.rowKeys.map((function(e){return e.aaa167})))).length:this.rowKeys.length))]),a("span",{staticStyle:{"font-size":"16px","margin-left":"28%"}},[e._v("项目数："+e._s("rule"==this.cjflag?Array.from(new Set(this.rowKeys.map((function(e){return e.ake002})))).length:this.itemNum))])]),a("div",{staticStyle:{"margin-top":"10px"}},[a("p",{staticStyle:{"font-size":"18px","font-weight":"bolder"}},[e._v("操作提示预览")]),a("table",{staticStyle:{"margin-top":"-10px","text-align":"center","border-color":"#EBEBEB","border-width":"2px"},attrs:{border:"1",width:"100%",cellspacing:"0"}},[e._m(0),a("tr",[a("td",["使用默认引导信息"==this.ydxxInput||""==this.ydxxInput2||null==this.ydxxInput2||void 0==this.ydxxInput2?a("span",{staticStyle:{color:"#8d8c8c"}},[e._v("配置的引导信息预览（默认引导信息不展示）")]):a("span",[e._v(e._s(this.ydxxInput2))])]),a("td",[null==this.checkedList||0==this.checkedList.length?a("span",{staticStyle:{color:"#8d8c8c"}},[e._v("操作按钮预览")]):e._l(e.checkedList,(function(t){return a("span",{key:t,staticStyle:{background:"#00b0fb",color:"white","margin-left":"5px"}},[e._v(e._s(""!=e.bxczAlias.find((function(e){return e.id===t})).name?e.bxczAlias.find((function(e){return e.id===t})).name:e.bxOptions.find((function(e){return e.id===t})).name))])}))],2)])])])]),a("div",[a("ta-title",{attrs:{title:"引导信息设置"}}),a("ta-radio-group",{model:{value:e.ydxxInput,callback:function(t){e.ydxxInput=t},expression:"ydxxInput"}},e._l(e.ydxxOptions,(function(t){return a("ta-radio",{key:t.value,staticStyle:{display:"block"},attrs:{value:t.value},on:{change:e.onChange2}},[e._v(e._s(t.label)+" ")])})),1),a("ta-textarea",{staticStyle:{"margin-top":"5px"},attrs:{disabled:e.areaDisabled,placeholder:"输入需在系统提醒时展示的文字内容，若想自定义请直接输入",rows:3},model:{value:e.ydxxInput2,callback:function(t){e.ydxxInput2=t},expression:"ydxxInput2"}})],1),a("div",[a("ta-title",{attrs:{title:"备选操作设置"}}),a("div",{staticStyle:{"margin-left":"20px"}},[a("span",{staticStyle:{"font-size":"16px","font-weight":"bolder","margin-right":"20px"}},[e._v("选择操作：")]),a("ta-checkbox-group",{attrs:{value:e.checkedList},on:{change:e.onChange}},e._l(e.bxOptions,(function(t,i){return a("span",{key:t.value,staticStyle:{"margin-right":"30px"}},[a("ta-checkbox",{style:{width:15*(t.name.length-4>0?t.name.length-4:0)+88+"px"},attrs:{value:t.id}},[e._v(e._s(t.name)+" ")]),a("ta-input",{style:{width:18*(t.name.length-4>0?t.name.length-4:0)+88+"px"},attrs:{placeholder:"输入别名",disabled:0===i,id:"alias"+i,value:e.bxczAlias.find((function(e){return e.id==t.id})).name},on:{change:e.changeAlias}})],1)})),0)],1),a("div",{staticStyle:{display:"flex","justify-content":"center","margin-top":"100px"}},[a("ta-button",{attrs:{type:"primary"},on:{click:e.save}},[e._v("保存")])],1)],1)])},d=[function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("tr",{attrs:{bgcolor:"#EBEBEB"}},[a("th",{attrs:{width:"62%"}},[e._v("引导信息")]),a("th",[e._v("已选操作(别名)")])])}],f=a(95082),h={name:"projectNotificationConfig",components:{TaTitle:s.Z},props:{record:Object,ydxxOptions:Array,bxOptions:Array,bxczAlias:Array,rowKeys:Array,flag:String,cjflag:String},data:function(){return{ydxxInput:null,ydxxInput2:"",indeterminate:!1,checkedList:[],rulePackage:"",isAllCheck:!1,uploadVisible:!1,areaDisabled:!1,itemNum:"正在查询",baReasons:[]}},methods:{changeAlias:function(e){for(var t=0;t<this.bxczAlias.length;t++)this.bxczAlias[t].name=document.getElementById("alias"+t).value},queryRuleNum:function(){var e=this;if("type"==this.cjflag){var t=this.rowKeys.map((function(e){return e.aaz319}));Base.submit(null,{url:"ruleInfo/queryRuleNum",data:{aaz319:t.toString(),ykz108:this.rowKeys[0].ykz108}}).then((function(t){e.itemNum=t.data.ruleListSize}))}},closeModal:function(){this.uploadVisible=!1},findMore:function(){this.uploadVisible=!0},deleteReason:function(e){this.baReasons.splice(e,1)},addReason:function(e){if(""!=e&&void 0!=e&&null!=e){var t=new RegExp("[;；]");t.test(e)?this.$message.error("你输入的值有误，不能为空，不能包含;号"):(this.baReasons.push(e),this.form.setFieldsValue({input:""}),this.$message.success("添加成功"))}else this.$message.error("你输入的值有误，不能为空，不能包含;号")},onChange2:function(e){"其他,自定义"===e.target.value?(this.areaDisabled=!1,this.ydxxInput2=""):(this.areaDisabled=!0,this.ydxxInput2=e.target.value)},checkAll:function(e){var t=e.target.checked;t?(this.isAllCheck=!0,this.checkedList=this.bxOptions.map((function(e){return e.id}))):(this.isAllCheck=!1,this.checkedList=[]),this.indeterminate=!1},onChange:function(e){e.length<this.bxOptions.length&&0!==e.length?(this.isAllCheck=!1,this.indeterminate=!0):0===e.length?(this.isAllCheck=!1,this.indeterminate=!1):(this.isAllCheck=!0,this.indeterminate=!1),e.includes("0")?(this.checkedList=["0"],this.baReasons=[],e.length>1&&this.$message.error("选中无操作时，不能再选择其他操作！")):this.checkedList=e},EtoC:function(e){return e?(e=e.replace(/,/g,"，").replace(/\./g,"。").replace(/;/g,"；").replace(/:/g,"：").replace(/\?/g,"？").replace(/!/g,"！").replace(/'/g,"’").replace(/"/g,"”"),e):""},save:function(){var e=this;return(0,l.Z)(regeneratorRuntime.mark((function t(){var a,i,n,r,l,s,o,u,d;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(0!=e.checkedList.length){t.next=3;break}return e.$message.error("请必须选择一项操作"),t.abrupt("return");case 3:if(a=function(e,t){return e+","+t},i=function(e,t){return e+";"+t},n=e.checkedList.reduce(a),r="",0!=e.baReasons.length&&(r=e.baReasons.reduce(i)),l=e.checkedList.map((function(t){return""!==e.bxczAlias.find((function(e){return e.id===t})).name?e.bxczAlias.find((function(e){return e.id===t})).name:e.bxOptions.find((function(e){return e.id===t})).name})).reduce(a),"使用默认引导信息"==e.ydxxInput&&(e.ydxxInput2=""),e.ydxxInput2=e.EtoC(e.ydxxInput2),t.prev=11,s=(0,f.Z)((0,f.Z)({},e.record),{},{bxcz:l,bxczid:n,bareason:r,flag:"0",ydxx:e.ydxxInput2}),"single"!=e.flag){t.next=24;break}if("rule"!=e.cjflag){t.next=19;break}return t.next=17,c.Z.insertKf35(s);case 17:t.next=22;break;case 19:return e.$message.info("您选中的大类包含规则过多，配置加载中，请稍候！"),t.next=22,c.Z.insertKf35Type(s);case 22:t.next=38;break;case 24:e.$message.info("您选中的条数过多，配置加载中，请稍候！"),o=0;case 26:if(!(o<e.rowKeys.length)){t.next=38;break}if(u=(0,f.Z)((0,f.Z)({},e.rowKeys[o]),{},{bxcz:l,bxczid:n,bareason:r,flag:"0",ydxx:e.ydxxInput2}),"rule"!=e.cjflag){t.next=33;break}return t.next=31,c.Z.insertKf35(u);case 31:t.next=35;break;case 33:return t.next=35,c.Z.insertKf35Type(u);case 35:o++,t.next=26;break;case 38:return e.$message.success("保存成功"),t.next=41,Base.submit(null,{url:"ruleInfo/refreshKf35",data:{},autoValid:!1});case 41:d=t.sent,"1"===d.data.code?e.$message.success("刷新缓存成功"):e.$message.error("刷新缓存失败"),e.$emit("close",!0),t.next=49;break;case 46:t.prev=46,t.t0=t["catch"](11),e.$message.error("保存失败");case 49:case"end":return t.stop()}}),t,null,[[11,46]])})))()},CtoE:function(e){if(e){var t=e.replace(/，/g,",").replace(/。/g,".").replace(/；/g,";").replace(/：/g,":").replace(/！/g,"!").replace(/？/g,"?").replace(/（/g,"(").replace(/）/g,")").replace(/【/g,"[").replace(/】/g,"]").replace(/‘/g,"'").replace(/’/g,"'").replace(/“/g,'"').replace(/”/g,'"').replace(/《/g,"<").replace(/》/g,">");return t}return""}},mounted:function(){this.queryRuleNum(),this.record&&this.record.bxczid&&(this.checkedList=this.record.bxczid.split(",")),this.record.bareason&&(this.baReasons=this.record.bareason.split(";")),this.record.state||(this.checkedList=this.bxczAlias.filter((function(e){return null!==e.name&&""!==e.name})).map((function(e){return e.id})));var e=this.CtoE(this.record.ydxx);this.ydxxOptions.map((function(e){return e.value})).includes(e)?(this.ydxxInput=e,this.areaDisabled=!0,this.ydxxInput2=e):""==this.record.ydxx||null==this.record.ydxx?(this.ydxxInput="使用默认引导信息",this.areaDisabled=!0,this.ydxxInput2=""):(this.ydxxInput="其他,自定义",this.areaDisabled=!1,this.ydxxInput2=this.record.ydxx)}},p=h,g=a(1001),y=(0,g.Z)(p,u,d,!1,null,"ca21212a",null),m=y.exports,x=[{type:"checkbox",fixed:"left",width:50,align:"center"},{type:"seq",title:"序号",width:60,align:"center"},{title:"项目编号",align:"left",width:260,field:"ake001",sorter:!0},{title:"项目名称",field:"ake002",width:200,align:"left",sorter:!0},{title:"规则大类",align:"left",width:250,field:"aaa167",sorter:!0},{title:"限制条件",align:"left",width:300,field:"ykz018",sorter:!0},{title:"引导信息",align:"left",width:250,field:"ydxx",sorter:!0},{title:"备选操作",align:"center",width:200,field:"bxcz",sorter:!0},{title:"配置状态",align:"center",fixed:"right",width:120,field:"states",customRender:{default:"states"},sorter:!0}],b={components:{ProjectNotificationConfig:m,TaCol:o.ZP,TaTitle:s.Z},data:function(){var e=this;return{pageUrl:c.J.queryByPage,URL:c.J,record:{},record3:{},ydxxOptions:[],ape893List:[],bxOptions:[],bxczAlias:[],rowKeys:[],ykz018:"",flag:"",cjflag:"",isModal:!1,cancelConfigFlag:!1,tableData:[],tableData2:[],tableData3:[],souList:[],options:[],options2:[],checked:[],checkLength:0,Columns:x,height:document.documentElement.clientHeight-70,visible:!1,visible3:!1,confirmLoading:!1,isAdd:!0,ModalText:"此操作将刷新全部引导操作配置信息，是否继续？",operateMenu:[{name:"修改",onClick:function(t,a){e.visible3=!0,e.isAdd=!1,e.record3=t,e.$nextTick((function(){e.beianform.resetFields(),e.beianform.setFieldsValue(t)}))}},{name:"启用",icon:!1,type:"confirm",isShow:function(e){return"2"===e.useflag},confirmTitle:"确认启用该模板？",onOk:function(t,a){t.useflag="1",e.Base.submit(null,{url:"reasonTemplate/editAe21Info",data:t,autoValid:!1},{successCallback:function(t){e.fnQuery3(),message.info("启用模板成功")},failCallback:function(e){message.info("启用模板失败")}})}},{name:"停用",icon:!1,type:"confirm",isShow:function(e){return"1"===e.useflag},confirmTitle:"确认停用该模板？",onOk:function(t,a){t.useflag="2",e.Base.submit(null,{url:"reasonTemplate/editAe21Info",data:t,autoValid:!1},{successCallback:function(t){e.fnQuery3(),message.info("停用模板成功")},failCallback:function(e){message.info("停用模板失败")}})}},{name:"删除",icon:!1,type:"confirm",confirmTitle:"确认删除该信息？",onOk:function(t,a){e.Base.submit(null,{url:"reasonTemplate/deleteAe21Info",data:{id:t.id},autoValid:!1},{successCallback:function(t){e.fnQuery3(),message.info("删除成功")},failCallback:function(e){message.info("删除失败")}})}}]}},watch:{checked:{handler:function(e){e.length>0?this.checkLength=this.$refs.infoTableRef.getCheckboxRecords().length:this.checkLength=0}}},mounted:function(){var e=this;Base.submit(null,{url:"ruleInfo/queryYkz108b"}).then((function(t){e.options=t.data.ykz108d,e.options&&e.options.length>0&&(e.form.setFieldsValue({ykz108:"2_1_1"}),e.form2.setFieldsValue({ykz108:"2_1_1"}),e.$refs.paging.loadData(),e.fnQuery2())}))},methods:{stateChange:function(e){this.form.setFieldsValue({state:e}),"1"==e?(this.fnQuery(),this.cancelConfigFlag=!1):this.cancelConfigFlag=!0},cancelConfig:function(e){var t=this;return(0,l.Z)(regeneratorRuntime.mark((function a(){var i,n,l,s;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(i="rule"==e?t.$refs.infoTableRef.getCheckboxRecords():t.$refs.infoTableRef2.getCheckboxRecords(),0!=i.length){a.next=4;break}return t.$message.error("请选择取消配置项"),a.abrupt("return");case 4:n=(0,r.Z)(i),a.prev=5,n.s();case 7:if((l=n.n()).done){a.next=18;break}if(s=l.value,"rule"!=e){a.next=14;break}return a.next=12,c.Z.cancelConfigKf35(s);case 12:a.next=16;break;case 14:return a.next=16,c.Z.cancelConfigKf35Type(s);case 16:a.next=7;break;case 18:a.next=23;break;case 20:a.prev=20,a.t0=a["catch"](5),n.e(a.t0);case 23:return a.prev=23,n.f(),a.finish(23);case 26:t.$message.success("取消成功"),t.checkLength=0,"rule"==e?t.fnQuery():t.fnQuery2(),t.checkLength=0,t.handleOk();case 31:case"end":return a.stop()}}),a,null,[[5,20,23,26]])})))()},validReason:function(e,t,a){var i=this,n=/[^`~!@#$%^&*()_\-+=<>?:"{}|,.\/;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘'，。、\w\r\n\s]/g;t?t.match(n)?parseInt(t)<=0?a([{message:"备案理由的长度不能超过400"}]):(this.$nextTick((function(){i.form.setFieldsValue({duration:parseInt(t)})})),a()):a([{message:"备案理由不能为空；不能全是特殊字符、符号、数字或三者组合"}]):a([{message:"请输入备案理由"}])},newAdd:function(){this.isAdd=!0,this.visible3=!0,this.beianform&&this.beianform.resetFields()},showModal:function(){this.visible=!0},handleOk3:function(e){var t=this;this.beianform.validateFields((function(e){if(!e){var a=t.beianform.getFieldsValue();t.isAdd?t.Base.submit(null,{url:"reasonTemplate/addAe21Info",data:a,autoValid:!1},{successCallback:function(e){t.$message.success("添加成功"),t.visible3=!1,t.fnQuery3()},failCallback:function(e){t.$message.error("添加失败"),t.visible3=!1}}):(t.record3.content=a.content,t.Base.submit(null,{url:"reasonTemplate/editAe21Info",data:t.record3,autoValid:!1},{successCallback:function(e){t.$message.success("修改成功"),t.visible3=!1},failCallback:function(e){t.$message.error("添加失败"),t.visible3=!1}}))}}))},handleOk:function(e){var t=this;this.confirmLoading=!0,this.Base.submit(null,{url:"ruleInfo/refreshKf35",data:{},autoValid:!1},{successCallback:function(e){"1"===e.data.code?t.$message.success("刷新缓存成功"):t.$message.error("刷新缓存失败"),t.visible=!1,t.confirmLoading=!1},failCallback:function(e){t.$message.error("刷新缓存失败"),t.visible=!1,t.confirmLoading=!1}})},handleCancel:function(e){this.visible=!1},handleCancel3:function(e){this.beianform.resetFields(),this.visible3=!1},selectAllEvent:function(e){var t=e.checked,a=e.records;this.checked=t?a:[]},selectChangeEvent:function(e){var t=e.checked,a=e.records,i=(e.row,e.$rowIndex);if(this.checked=a,t){for(var n=[],r=0;r<this.tableData.length;r++)this.tableData[r].ake002==this.tableData[i].ake002&&n.push(this.tableData[r]);this.$refs.infoTableRef.setCheckboxRow(n,!0)}},callback:function(e){var t=this;"3"==e&&this.$nextTick((function(){t.fnQuery3()}))},fnQuerySource:function(){var e=this;this.Base.submit(null,{url:"reasonTemplate/querySourceList",autoValid:!1},{successCallback:function(t){e.souList=t.data.resultData},failCallback:function(t){e.$message.error("模块类型加载失败")}})},fnQuery3:function(){var e=this;this.form3.validateFields((function(t){t?e.$message.error("请选择规则运行场景"):(e.pageUrl=c.J.queryReasonTemplateInfoList,e.$refs.paging3.reset(),e.$refs.paging3.loadData(),e.fnQuerySource())}))},fnQuery2:function(){var e=this;this.form2.validateFields((function(t){if(t)e.$message.error("请选择规则运行场景");else{var a=e.form2.getFieldsValue();Base.submit(null,{url:"ruleInfo/queryKf35ByType",data:a}).then((function(t){e.tableData2=t.data.result}))}}))},fnQuery:function(){var e=this;this.form.validateFields((function(t){t?e.$message.error("请选择规则运行场景"):(e.pageUrl=c.J.queryByPage,e.$refs.paging.reset(),e.$refs.paging.loadData(),e.checkLength=0)}))},fnGetParams:function(){var e=this.form.getFieldsValue();return e.operate=this.ape893List.join(","),e},fnGetParams3:function(){return this.form3.getFieldsValue()},selectChange:function(e){var t=this;Base.submit(null,{url:"ruleInfo/queryAaz319",data:{ykz108:e}}).then((function(e){t.options2=e.data.Aaz319}))},resetForm:function(){this.form.resetFields()},click:function(){var e={fileName:"basic",sheets:[{name:"worksheet1",column:{complex:!1,columns:[{header:"项目编号",key:"ake001",width:32},{header:"项目名称",key:"ake002",width:10},{header:"规则大类",key:"aaa167",width:20},{header:"限制条件",key:"ykz018",width:15},{header:"引导信息",key:"ydxx",width:20},{header:"备选操作",key:"bxcz",width:20}]},rows:this.tableData}]};this.Base.generateExcel(e)},toPeizhi:function(){var e=this;return(0,l.Z)(regeneratorRuntime.mark((function t(){var a,i,n,r,l,s;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(a=e.$refs.infoTableRef.getCheckboxRecords(),0!=a.length){t.next=4;break}return e.$message.error("请选择配置项"),t.abrupt("return");case 4:if(a.length>1){for(e.$message.warning("您已选中多个配置项,所有配置都会被覆盖！"),i=0;i<a.length;i++)a[i].aae500=e.form.getFieldValue("ykz108").split("_")[0],a[i].aae141=e.form.getFieldValue("ykz108").split("_")[1],a[i].aaa171=e.form.getFieldValue("ykz108").split("_")[2];e.flag="multip"}else e.flag="single";return e.rowKeys=a,n=a[0],n.aae500=e.form.getFieldValue("ykz108").split("_")[0],n.aae141=e.form.getFieldValue("ykz108").split("_")[1],n.aaa171=e.form.getFieldValue("ykz108").split("_")[2],t.next=12,c.Z.queryYdxx({ydxx:"YDXX"});case 12:return e.ydxxOptions=t.sent,t.next=15,c.Z.queryBxcz({bx:"APE893",aae500:n.aae500});case 15:r=t.sent,e.bxOptions=r.bxczList,e.bxczAlias=r.bxczAlias,0==e.bxczAlias.length&&(e.bxczAlias=e.bxOptions.map((function(e){return{id:e.id,name:""}}))),"3"==n.aae500&&(e.bxOptions=e.bxOptions.filter((function(e){return"4"!==e.id})),e.bxczAlias=e.bxczAlias.filter((function(e){return"4"!==e.id}))),"6"!=n.aae500&&(e.bxOptions=e.bxOptions.filter((function(e){return"9"!==e.id})),e.bxczAlias=e.bxczAlias.filter((function(e){return"9"!==e.id}))),e.bxOptions=e.bxOptions.map((function(t){var a=e.bxczAlias.find((function(e){return e.id===t.id}));return""!==a.name&&a&&(t.name=a.name),t})),"single"===e.flag&&n.bxcz&&n.bxczid&&(l=n.bxcz.split(","),s=n.bxczid.split(","),e.bxczAlias=e.bxczAlias.map((function(e){return s.find((function(t){return t===e.id}))&&(e.name=l[s.indexOf(e.id)]),e}))),e.record=n,e.cjflag="rule",e.isModal=!0;case 26:case"end":return t.stop()}}),t)})))()},Cancel:function(e){!0===e&&(this.checkLength=0,this.$refs.paging.loadData(),this.fnQuery2()),this.isModal=!1},toPeizhi2:function(){var e=this;return(0,l.Z)(regeneratorRuntime.mark((function t(){var a,i,n,r,l,s;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(a=e.$refs.infoTableRef2.getCheckboxRecords(),0!=a.length){t.next=4;break}return e.$message.error("请选择配置项"),t.abrupt("return");case 4:if(a.length>1){for(e.$message.warning("您已选中多个配置项,所有配置都会被覆盖！"),i=0;i<a.length;i++)a[i].aae500=e.form2.getFieldValue("ykz108").split("_")[0],a[i].aae141=e.form2.getFieldValue("ykz108").split("_")[1],a[i].aaa171=e.form2.getFieldValue("ykz108").split("_")[2];e.flag="multip"}else e.flag="single";return e.rowKeys=a,n=a[0],n.aae500=e.form2.getFieldValue("ykz108").split("_")[0],n.aae141=e.form2.getFieldValue("ykz108").split("_")[1],n.aaa171=e.form2.getFieldValue("ykz108").split("_")[2],t.next=12,c.Z.queryYdxx({ydxx:"YDXX"});case 12:return e.ydxxOptions=t.sent,t.next=15,c.Z.queryBxcz({bx:"APE893",aae500:n.aae500});case 15:r=t.sent,e.bxOptions=r.bxczList,e.bxczAlias=r.bxczAlias,0==e.bxczAlias.length&&(e.bxczAlias=e.bxOptions.map((function(e){return{id:e.id,name:""}}))),"3"==n.aae500&&(e.bxOptions=e.bxOptions.filter((function(e){return"4"!==e.id})),e.bxczAlias=e.bxczAlias.filter((function(e){return"4"!==e.id}))),"6"!=n.aae500&&(e.bxOptions=e.bxOptions.filter((function(e){return"9"!==e.id})),e.bxczAlias=e.bxczAlias.filter((function(e){return"9"!==e.id}))),e.bxOptions=e.bxOptions.map((function(t){var a=e.bxczAlias.find((function(e){return e.id===t.id}));return""!==a.name&&a&&(t.name=a.name),t})),"single"===e.flag&&n.bxcz&&n.bxczid&&(l=n.bxcz.split(","),s=n.bxczid.split(","),e.bxczAlias=e.bxczAlias.map((function(e){return s.find((function(t){return t===e.id}))&&(e.name=l[s.indexOf(e.id)]),e}))),e.record=n,e.cjflag="type",e.isModal=!0;case 26:case"end":return t.stop()}}),t)})))()},clickAll:function(){var e=this,t=this.form.getFieldsValue();Base.submit(null,{url:"ruleInfo/exportAll",data:t}).then((function(t){var a={fileName:"basic",sheets:[{name:"worksheet1",column:{complex:!1,columns:[{header:"项目编号",key:"ake001",width:32},{header:"项目名称",key:"ake002",width:10},{header:"规则大类",key:"aaa167",width:20},{header:"限制条件",key:"ykz018",width:15},{header:"引导信息",key:"ydxx",width:20},{header:"备选操作",key:"bxcz",width:20}]},rows:t.data.tableData}]};e.Base.generateExcel(a)}))},noExit:function(){var e=this;this.form.validateFields((function(t){t?e.$message.error("请选择规则运行场景"):(e.pageUrl=c.J.queryNoExit,e.$refs.paging.reset(),e.$refs.paging.loadData())}))}}},v=b,k=(0,g.Z)(v,i,n,!1,null,"5c26fb59",null),w=k.exports},36766:function(e,t,a){var i=a(66586);t["Z"]=i.Z},26263:function(e,t,a){a.d(t,{s:function(){return i},x:function(){return n}});var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:e.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[e._v(e._s(e.title))]),e._t("default")],2)},n=[]},66586:function(e,t){var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};t["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}},39744:function(e,t,a){a.d(t,{J:function(){return u}});var i=a(82482),n=a(95082),r=a(66353),l=a(48534),s=(a(36133),["form"]),o="/ruleInfo",c="/reasonTemplate",u={getMedicalInsuranceTypeList:"".concat(o,"/queryMedicalInsuranceType"),getSelectOptions:"".concat(o,"/getSelectOptions"),queryAuditStandDetail:"".concat(o,"/queryAuditStandDetail"),getRuleTree:"".concat(o,"/getRuleTree"),queryNodeType:"".concat(o,"/queryNodeType"),queryNodeTypeInUpdate:"".concat(o,"/queryNodeTypeInUpdate"),getSelectOptionsWithMaxBatch:"".concat(o,"/getSelectOptionsWithMaxBatch"),getSmInfo:"".concat(o,"/getSmInfo"),queryNodeInfo:"".concat(o,"/queryNodeInfo"),queryKf32:"".concat(o,"/queryKf32"),queryKf41:"".concat(o,"/queryKf41"),auditRule:"".concat(o,"/auditRule"),queryYdxx:"".concat(o,"/queryYdxx"),queryBxcz:"".concat(o,"/queryBxcz"),getAa10ByAaa100:"miimCommonRead/getAa10ByAaa100",insertKf35:"".concat(o,"/insertKf35"),cancelConfigKf35:"".concat(o,"/cancelConfigKf35"),cancelConfigKf35Type:"".concat(o,"/cancelConfigKf35Type"),insertKf35Type:"".concat(o,"/insertKf35Type"),queryRule:"".concat(o,"/queryRuleInfo"),getSameGroupData:"".concat(o,"/getSameGroupData"),queryRuleInfoByAuditState:"".concat(o,"/queryRuleInfoByAuditState"),queryPackageUpdateRecord:"".concat(o,"/queryPackageUpdateRecord"),queryNoExit:"".concat(o,"/queryNoExit"),queryByPage:"".concat(o,"/queryByPage"),queryReasonTemplateInfoList:"".concat(c,"/queryAe21InfoPage")},d={getMedicalInsuranceTypeList:{url:u.getMedicalInsuranceTypeList},getSelectOptions:{url:u.getSelectOptions},queryAuditStandDetail:{url:u.queryAuditStandDetail},getRuleTree:{url:u.getRuleTree},queryNodeType:{url:u.queryNodeType},queryNodeTypeInUpdate:{url:u.queryNodeTypeInUpdate,returnKey:"nodeType"},getSelectOptionsWithMaxBatch:{url:u.getSelectOptionsWithMaxBatch},getSmInfo:{url:u.getSmInfo,returnKey:"smInfo"},queryNodeInfo:{url:u.queryNodeInfo,returnKey:"nodeInfo"},queryKf32:{url:u.queryKf32,returnKey:"kf32TableData"},queryKf41:{url:u.queryKf41,returnKey:"kf41TableData"},auditRule:{url:u.auditRule,config:{autoQs:!1}},queryYdxx:{url:u.queryYdxx,returnKey:"ydxx"},queryBxcz:{url:u.queryBxcz},getAa10ByAaa100:{url:u.getAa10ByAaa100},insertKf35:{url:u.insertKf35},cancelConfigKf35:{url:u.cancelConfigKf35},cancelConfigKf35Type:{url:u.cancelConfigKf35Type},insertKf35Type:{url:u.insertKf35Type}},f={baseRequest:function(){var e=(0,l.Z)(regeneratorRuntime.mark((function e(t,a,i){var l,o,c,u;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return l=null,o=null,i&&(c=i,l=c.form,o=(0,r.Z)(c,s)),e.next=5,Base.submit(l,(0,n.Z)({url:a,data:t},o)).catch((function(e){throw e}));case 5:return u=e.sent,e.abrupt("return",u.data);case 7:case"end":return e.stop()}}),e)})));function t(t,a,i){return e.apply(this,arguments)}return t}()};Object.entries(d).forEach((function(e){var t=function(){var t=(0,l.Z)(regeneratorRuntime.mark((function t(a){var i;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,this.baseRequest(a,e[1].url,e[1].config);case 2:if(i=t.sent,!e[1].returnKey||""===e[1].returnKey){t.next=5;break}return t.abrupt("return",i[e[1].returnKey]);case 5:return t.abrupt("return",i);case 6:case"end":return t.stop()}}),t,this)})));return function(e){return t.apply(this,arguments)}}();Object.assign(f,(0,i.Z)({},e[0],t))})),t["Z"]=f}}]);
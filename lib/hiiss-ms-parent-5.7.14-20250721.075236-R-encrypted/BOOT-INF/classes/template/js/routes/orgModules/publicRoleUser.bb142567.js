"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[3270],{15503:function(e,t,s){s.r(t),s.d(t,{default:function(){return d}});var r=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"fit",attrs:{id:"publicRoleUser"}},[s("ta-border-layout",{attrs:{layout:{header:"70px",footer:"70px"},"footer-cfg":{showBorder:!1}}},[s("div",{staticClass:"center-box",attrs:{slot:"header"},slot:"header"},[s("ta-breadcrumb",{staticStyle:{"line-height":"42px",float:"left"},attrs:{separator:">"}},[s("ta-breadcrumb-item",[s("a",{on:{click:e.fnBack}},[e._v("角色维度")])]),s("ta-breadcrumb-item",[e._v("人员管理")])],1),s("div",{staticClass:"divider"}),s("ta-alert",{staticClass:"notice-box",attrs:{message:"当前角色为："+e.role.roleName,type:"info","show-icon":""}}),s("ta-button",{staticStyle:{float:"right","margin-top":"8px"},on:{click:e.fnBack}},[s("ta-icon",{attrs:{type:"rollback"}}),e._v(" 返回 ")],1)],1),s("ta-tabs",{staticClass:"fit content-box"},[s("ta-tab-pane",{attrs:{tab:"角色下已分配的人员"}},[s("ta-table",{ref:"table",attrs:{columns:e.roleUserColumns,"data-source":e.roleUserData,pagination:!1,"row-key":"userId","show-checkbox":!0},scopedSlots:e._u([{key:"sex",fn:function(t){return s("span",{},[e._v(e._s(e.CollectionLabel("SEX",t)))])}},{key:"operation",fn:function(t,r){return s("span",{},[s("ta-table-operate",{attrs:{"operate-menu":e.operateMenu}})],1)}}])})],1),s("template",{slot:"tabBarExtraContent"},[s("ta-input-search",{staticClass:"search-box",attrs:{placeholder:"请输入人员姓名"},on:{search:e.fnQueryUsersByRoleId},model:{value:e.searchInfo,callback:function(t){e.searchInfo=t},expression:"searchInfo"}},[s("ta-button",{attrs:{slot:"enterButton",type:"primary"},slot:"enterButton"},[e._v(" 搜索 ")])],1),s("ta-button",{attrs:{type:"primary"},on:{click:e.fnOpenAssociate}},[e._v(" 可分配人员 ")]),s("ta-button",{on:{click:e.deleteBatchRoleUsers}},[s("ta-icon",{attrs:{type:"delete"}}),e._v(" 批量移除 ")],1)],1)],2),s("div",{attrs:{slot:"footer"},slot:"footer"},[s("ta-pagination",{ref:"roleUserPager",staticStyle:{float:"right","margin-top":"10px"},attrs:{"show-size-changer":"","show-quick-jumper":"","data-source":e.roleUserData,"default-page-size":10,params:e.roleUserPageParams,url:"org/authority/roleAuthorityManagementRestService/queryUsersByRoleId"},on:{"update:dataSource":function(t){e.roleUserData=t},"update:data-source":function(t){e.roleUserData=t}}})],1)],1),s("ta-user-select",{attrs:{id:"roleUserSelect",title:"人员选择","is-show":e.isUserShow,"user-tree-data":e.orgOptions,"user-list-data":e.userListData,"default-user-list":e.defaultUserList,props:e.selectUserProps,load:e.loadNode,"user-select-data":e.userSelectData,pagination:!0},on:{close:function(t){e.isUserShow=!1},queryUserList:e.fnQueryUserList,getUserListResult:e.fnGetUserListResult,search:e.fnSearchUser,checkAllMembers:e.checkAllMembers}})],1)},a=[],n=s(46981),u=[{title:"姓名",dataIndex:"name",width:"25%",overflowTooltip:!0},{title:"登录号",dataIndex:"loginId",width:"15%"},{title:"所属组织",dataIndex:"namePath",width:"25%",overflowTooltip:"namePath",customRender:function(e){return""!==e&&null!==e?e.slice(e.lastIndexOf("/")+1):"--"}},{title:"证件号",dataIndex:"idCardNo",width:"20%",overflowTooltip:!0},{title:"性别",dataIndex:"sex",scopedSlots:{customRender:"sex"},width:"15%"},{title:"操作",dataIndex:"operation",scopedSlots:{customRender:"operation"},align:"center",width:"200px"}],o={name:"publicRoleUser",data:function(){var e=this;return{role:{},searchInfo:"",roleUserColumns:u,operateMenu:[{name:"移除",type:"confirm",confirmTitle:"确认移除吗？",onOk:function(t){e.fnRemoveRoleUser(t)}}],roleUserData:[],collectionList:["SEX"],isUserShow:!1,orgOptions:[],userListData:[],defaultUserList:[],userSelectData:[],selectUserProps:{treeNodeKey:"value",treeLabel:"label",treeChildren:"children",listKey:"userId",listTitle:"name",listSubTitle:"mobile",listDescription:"namePath"}}},activated:function(){this.$route.params.role instanceof Object?(this.role=this.$route.params.role||{},this.fnQueryUsersByRoleId()):this.$router.push({name:"publicRoleManager"})},methods:{deleteBatchRoleUsers:function(){var e=this,t=this.$refs.table,s=t.getChecked(),r=s.selectedRowKeys,a=r.join(",");n.Z.deleteBatchRoleUsers({roleId:this.role.roleId,userIds:a},(function(t){e.$message.success("批量移除成功"),e.fnQueryUsersByRoleId()}))},fnRemoveRoleUser:function(e){var t=this;n.Z.deleteRoleUserByKey({roleId:e.roleId,userId:e.userId},(function(e){t.$message.success("移除成功"),t.fnQueryUsersByRoleId()}))},fnOpenAssociate:function(){var e=this;this.isUserShow=!0,this.userListData=[],this.defaultUserList=[],n.Z.queryUsersByRoleIdNoPage({roleId:this.role.roleId},(function(t){e.defaultUserList=t.data.list}))},fnBack:function(){this.$router.push({name:"publicRoleManager"})},loadNode:function(e,t){n.Z.queryCurrentAdminRoleWrapeOrgTree({orgId:e.data?e.data.value:null},(function(e){t(e.data.orgVos)}))},fnSearchUser:function(e){var t=this;e&&n.Z.queryUsersNoWraperByRoleId({roleId:this.role.roleId,orgId:"",includeChild:1,userName:e},(function(e){t.userSelectData=e.data.userVos.list}))},checkAllMembers:function(e,t,s){n.Z.queryUsersNoWraperByRoleIdNoPage({roleId:this.role.roleId,orgId:e,includeChild:t},(function(e){s(e.data.list)}))},fnQueryUserList:function(e,t,s,r,a){var u=this,o={roleId:this.role.roleId,orgId:e,pageNumber:s,pageSize:10,includeChild:t?1:0};r&&(o[r]=a),n.Z.queryUsersNoWraperByRoleId(o,(function(e){u.userListData=e.data.userVos.list}))},fnGetUserListResult:function(e,t){var s=this;this.Base.pageMask({show:!0,text:"加载中"});var r=this.defaultUserList.filter((function(t){var s=[];return e.forEach((function(e){s.push(e.userId)})),!s.includes(t.userId)})),a=e.filter((function(e){var t=[];return s.defaultUserList.forEach((function(e){t.push(e.userId)})),!t.includes(e.userId)})),u=[],o=[];a.length>0&&a.forEach((function(e,t){u.push(e.userId)})),r.length>0&&r.forEach((function(e,t){o.push(e.userId)})),new Promise((function(e,t){u.length?n.Z.addBatchRoleUsers({roleId:s.role.roleId,userIds:u.join(",")},(function(t){s.$message.success("关联人员成功"),e()}),(function(e){s.isUserShow=!1,s.Base.pageMask({show:!1})})):e()})).then((function(){return new Promise((function(e,t){o.length?n.Z.deleteBatchRoleUser({roleId:s.role.roleId,userIds:o.join(",")},(function(t){s.$message.success("移除人员成功"),e()}),(function(e){s.isUserShow=!1,s.Base.pageMask({show:!1})})):e()}))})).then((function(){t||(s.isUserShow=!1,s.fnQueryUsersByRoleId()),s.Base.pageMask({show:!1})}))},roleUserPageParams:function(){var e={};return e.roleId=this.role.roleId,this.searchInfo&&(e.userName=this.searchInfo),e},fnQueryUsersByRoleId:function(){this.$refs.roleUserPager.loadData()}}},l=o,i=s(1001),c=(0,i.Z)(l,r,a,!1,null,"9cd6b228",null),d=c.exports},46981:function(e,t){var s="org/authority/roleAuthorityManagementRestService/";t["Z"]={queryCurrentAdminRoleWrapeOrgTree:function(e,t){Base.submit(null,{url:s+"queryCurrentAdminRoleWrapeOrgTree",data:e,showPageLoading:!1},{successCallback:function(e){return t(e)}})},queryRolesByOrgId:function(e,t){Base.submit(null,{url:s+"queryRolesByOrgId",data:e},{successCallback:function(e){return t(e)}})},queryAuthRole:function(e,t){Base.submit(null,{url:s+"queryAuthRole",data:e},{successCallback:function(e){return t(e)}})},copyResource:function(e,t,r){Base.submit(e,{url:s+"copyResource",data:t,autoValid:!0},{successCallback:function(e){return r(e)}}).then((function(e){})).catch((function(e){}))},queryUsePermissionByRoleId:function(e,t){Base.submit(null,{url:s+"queryUsePermissionByRoleId",data:e},{successCallback:function(e){return t(e)}})},queryCustomUsePermissionByRoleId:function(e,t){Base.submit(null,{url:s+"queryCustomUsePermissionByRoleId",data:e},{successCallback:function(e){return t(e)}})},copyRole:function(e,t,r){Base.submit(e,{url:s+"copyRole",data:t,autoValid:!0},{successCallback:function(e){return r(e)}}).then((function(e){})).catch((function(e){}))},queryRePermission:function(e,t){Base.submit(null,{url:s+"queryRePermission",data:e},{successCallback:function(e){return t(e)}})},queryCustomRePermission:function(e,t){Base.submit(null,{url:s+"queryCustomRePermission",data:e},{successCallback:function(e){return t(e)}})},addBatchUsePermissionByMoreRole:function(e,t){Base.submit(null,{url:s+"addBatchUsePermissionByMoreRole",data:e},{successCallback:function(e){return t(e)}})},deleteBatchUsePermissionByMoreRole:function(e,t){Base.submit(null,{url:s+"deleteBatchUsePermissionByMoreRole",data:e},{successCallback:function(e){return t(e)}})},addRole:function(e,t,r){Base.submit(e,{url:s+"addRole",data:t,autoValid:!0},{successCallback:function(e){return r(e)}}).then((function(e){})).catch((function(e){}))},updateRoleByRoleId:function(e,t,r){Base.submit(null,{url:s+"updateRoleByRoleId",data:t,autoValid:!0},{successCallback:function(e){return r(e)}}).then((function(e){})).catch((function(e){}))},updateBatchUsePermissionEffectiveTime:function(e,t){Base.submit(null,{url:s+"updateBatchUsePermissionEffectiveTime",data:e},{successCallback:function(e){return t(e)}})},updateUsePermissionEffectiveTime:function(e,t){Base.submit(null,{url:s+"updateUsePermissionEffectiveTime",data:e},{successCallback:function(e){return t(e)}})},addUsePermission:function(e,t){Base.submit(null,{url:s+"addUsePermission",data:e},{successCallback:function(e){return t(e)}})},changeRestAuthority:function(e,t){Base.submit(null,{url:s+"changeRestAuthority",data:e},{successCallback:function(e){return t(e)}})},queryRePermissionByRoleId:function(e,t){Base.submit(null,{url:s+"queryRePermissionByRoleId",data:e},{successCallback:function(e){return t(e)}})},updateBatchCustomResourceUsePermissionEffectiveTime:function(e,t){Base.submit(null,{url:s+"updateBatchCustomResourceUsePermissionEffectiveTime",data:e},{successCallback:function(e){return t(e)}})},updateCustomResourceUsePermissionEffectiveTime:function(e,t){Base.submit(null,{url:s+"updateCustomResourceUsePermissionEffectiveTime",data:e},{successCallback:function(e){return t(e)}})},queryCustomRePermissionByRoleId:function(e,t){Base.submit(null,{url:s+"queryCustomRePermissionByRoleId",data:e},{successCallback:function(e){return t(e)}})},addCustomResourceUsePermission:function(e,t){Base.submit(null,{url:s+"addCustomResourceUsePermission",data:e},{successCallback:function(e){return t(e)}})},updateRoleEffectiveByRoleId:function(e,t){Base.submit(null,{url:s+"updateRoleEffectiveByRoleId",data:e},{successCallback:function(e){return t(e)}})},deleteBatchRole:function(e,t){Base.submit(null,{url:s+"deleteBatchRole",data:e},{successCallback:function(e){return t(e)}})},deleteRoleUserByKey:function(e,t){Base.submit(null,{url:s+"deleteRoleUserByKey",data:e},{successCallback:function(e){return t(e)}})},deleteBatchRoleUsers:function(e,t){Base.submit(null,{url:s+"deleteBatchRoleUser",data:e},{successCallback:function(e){return t(e)}})},queryUsersByRoleId:function(e,t){Base.submit(null,{url:s+"queryUsersByRoleId",data:e},{successCallback:function(e){return t(e)}})},queryUsersByRoleIdNoPage:function(e,t){Base.submit(null,{url:s+"queryUsersByRoleIdNoPage",data:e},{successCallback:function(e){return t(e)}})},queryUsersNoWraperByRoleId:function(e,t){Base.submit(null,{url:s+"queryUsersNoWraperByRoleId",data:e},{successCallback:function(e){return t(e)}})},queryUsersNoWraperByRoleIdNoPage:function(e,t){Base.submit(null,{url:s+"queryUsersNoWraperByRoleIdNoPage",data:e},{successCallback:function(e){return t(e)}})},addBatchRoleUsers:function(e,t,r){Base.submit(null,{url:s+"addBatchRoleUsers",data:e,showPageLoading:!1},{successCallback:function(e){return t(e)},failCallback:function(e){return r(e)}})},deleteBatchRoleUser:function(e,t,r){Base.submit(null,{url:s+"deleteBatchRoleUser",data:e,showPageLoading:!1},{successCallback:function(e){return t(e)},failCallback:function(e){return r(e)}})},addBatchUserRole:function(e,t){Base.submit(null,{url:s+"addBatchUserRole",data:e},{successCallback:function(e){return t(e)}})},deleteBatchUserRole:function(e,t){Base.submit(null,{url:s+"deleteBatchUserRole",data:e},{successCallback:function(e){return t(e)}})},queryCustomRePermissionAsync:function(e,t){Base.submit(null,{url:s+"queryCustomRePermissionAsync",data:e},{successCallback:function(e){return t(e)}})},addAllRoleUsersByOrgId:function(e,t){Base.submit(null,{url:s+"addAllRoleUsersByOrgId",data:e},{successCallback:function(e){return t(e)}})}}}}]);
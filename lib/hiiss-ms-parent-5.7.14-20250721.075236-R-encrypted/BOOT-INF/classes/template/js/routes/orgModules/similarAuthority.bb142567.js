"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[3852],{46365:function(e,t,o){o.r(t),o.d(t,{default:function(){return h}});var a=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("ta-border-layout",{attrs:{layout:{left:"300px",footer:"70px"},"left-cfg":{showBar:!0},"footer-cfg":{showBorder:!1}}},[o("div",{attrs:{slot:"leftExtraContent"},slot:"leftExtraContent"},[o("ta-input",{attrs:{placeholder:"输入菜单名称进行过滤",type:"primary"},model:{value:e.menuFilterText,callback:function(t){e.menuFilterText=t},expression:"menuFilterText"}})],1),o("div",{staticClass:"tree-container",attrs:{slot:"left"},slot:"left"},[o("div",{staticClass:"tree-context"},[o("ta-e-tree",{ref:"tree",attrs:{load:e.handleLoadTreeNode,"show-checkbox":"","highlight-current":"",props:e.defaultProps,"filter-node-method":e.fnFilterNode,"node-key":"resourceId","default-expanded-keys":e.defaultExpandedNode,lazy:""},on:{check:e.handleNodeCheck}})],1)]),o("div",[o("ta-alert",{attrs:{message:"请先选择左侧菜单列表，系统会自动加载拥有相应菜单权限的角色名单，这批角色即是我们要管理的相似权限角色，管理员再选择角色进行授权管理",type:"info",showIcon:""}}),o("ta-tabs",{staticClass:"define-tab-context"},[o("ta-tab-pane",{attrs:{tab:"角色列表"}},[o("ta-table",{staticStyle:{"padding-top":"10px"},attrs:{columns:e.roleColumns,dataSource:e.roleData,rowSelection:{onChange:e.handleSelected,selectedRowKeys:e.selectedRowKeys},pagination:!1},scopedSlots:e._u([{key:"roleType",fn:function(t){return o("span",{},[e._v(e._s(e.CollectionLabel("ROLETYPE",t)))])}}])})],1),o("ta-button",{staticClass:"step2",attrs:{slot:"tabBarExtraContent",type:"primary",icon:"plus-circle-o",disabled:e.authorityDisabled},on:{click:e.fnAuthorityToRoleModal},slot:"tabBarExtraContent"},[e._v("授权")])],1)],1),o("div",{attrs:{slot:"footer"},slot:"footer"},[o("ta-pagination",{ref:"rolePager",staticStyle:{float:"right","margin-top":"10px"},attrs:{showSizeChanger:"",showQuickJumper:"",dataSource:e.roleData,defaultPageSize:10,params:e.fnRolePageParams,url:"org/authority/similarAuthorityManagementRestService/queryUsePermissionRoleByResourceId"},on:{"update:dataSource":function(t){e.roleData=t},"update:data-source":function(t){e.roleData=t}}})],1)])},r=[],n=o(41538),s=o(67090),i=(o(36797),o(80790)),l=[{title:"名称",overflowTooltip:!0,dataIndex:"roleName",width:120},{title:"类型",dataIndex:"roleType",scopedSlots:{customRender:"roleType"},width:100},{title:"组织路径",dataIndex:"namePath",width:"40%",overflowTooltip:!0}],u={name:"similarAuthority",data:function(){return{menuTree:[],menuFilterText:"",defaultProps:{children:"children",label:"name",isLeaf:"isLeaf",id:"resourceId"},defaultExpandedNode:[],customRePermissions:[],roleColumns:l,roleData:[],selectedRows:[],selectedRowKeys:[],roleConfirmLoading:!1,authorityDisabled:!0}},watch:{menuFilterText:function(e){this.$refs.tree.filter(e)}},mixins:[i.Z],mounted:function(){var e=[{element:".ant-tabs-tab-active",popover:{title:"角色列表",description:"可以通过左边的功能树筛选出具有相同功能权限的角色",position:"bottom"}},{element:".step2",popover:{title:"授权",description:"对这些具有类似功能权限的角色进行批量授权",position:"left"}}];this.fnCommonGuide(e)},methods:{handleNodeCheck:function(e,t){(0,n.Z)(t.checkedKeys)&&0!=t.checkedKeys.length&&this.$refs.rolePager.loadData()},handleLoadTreeNode:function(e,t){var o=this;if(0===e.level&&s.Z.querySimilarAuthority(null,(function(e){return o.defaultExpandedNode=[e.customRePermissions[0].resourceId],o.customRePermissions=e.customRePermissions,t(e.customRePermissions)})),e.level>=1){var a=e.data.resourceId,r={resourceId:a};s.Z.querySimilarAuthority(r,(function(e){var o=e.customRePermissions;if(o[0].children)return t(o[0].children)}))}},fnRolePageParams:function(){var e=this.$refs.tree.getCheckedNodes(),t=e.map((function(e){return e.resourceId}));return{resourceIds:t.join(",")}},fnFilterNode:function(e,t,o){return!e||-1!==t.name.indexOf(e)},fnQueryRoleByMenuIds:function(){this.$refs.rolePager.loadData()},handleSelected:function(e,t){this.selectedRowKeys=e,this.selectedRows=t,0==this.selectedRows.length?this.authorityDisabled=!0:this.authorityDisabled=!1},fnAuthorityToRoleModal:function(){0!==this.selectedRows.length?this.$router.push({name:"grantAuthority",params:{roles:this.selectedRows}}):this.$message.warning("请先选择数据后再进行授权")}}},d=u,c=o(1001),f=(0,c.Z)(d,a,r,!1,null,"67a26e16",null),h=f.exports},80790:function(e,t,o){var a=o(76698);o(34530);t["Z"]={activated:function(){var e=window.pageVmObj,t=e["steps_"+e._route.name];this.methods?this.methods.fnCommonGuide(t):this.fnCommonGuide(t)},deactivated:function(){this.methods?this.methods.fnCommonGuide([],!0):this.fnCommonGuide([],!0)},methods:{fnCommonGuide:function(e,t){var o=window.pageVmObj;t?(o.driver.reset(),window.fnPageGuide=null):(o["steps_"+o._route.name]=e,o.driver=new a.Z({allowClose:!1}),window.fnPageGuide=function(){o.driver.defineSteps(e),o.driver.start()})}}}},67090:function(e,t){var o="/org/authority/similarAuthorityManagementRestService/";t["Z"]={querySimilarAuthority:function(e,t){Base.submit(null,{url:o+"queryCurrentUserRePermission",data:e},{successCallback:function(e){return t(e.data)}})},queryRoleByMenuIds:function(e,t){Base.submit(null,{url:o+"queryUsePermissionRoleByResourceId",data:e},{successCallback:function(e){return t(e.data)}})},queryRePermissionResource:function(e,t){Base.submit(null,{url:o+"queryRePermissionResource",data:e},{successCallback:function(e){return t(e.data)}})},addBatchSimilarAuthority:function(e,t){Base.submit(null,{url:o+"addBatchSimilarAuthority",data:e},{successCallback:function(e){return t(e.data)}})}}}}]);
"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[9647,4832],{82057:function(t,e,a){a.r(e),a.d(e,{default:function(){return c}});var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("ta-row",[a("ta-col",{attrs:{span:t.row.col.fullSpan}},[a("span",{staticClass:"title"},[t._v("组织权限")]),a("div",{staticClass:"modalTreeStyle"},[a("ta-e-tree",{ref:"tree",attrs:{data:t.treeData,load:t.loadData,"show-checkbox":"","highlight-current":"","check-strictly":"","node-key":"orgId",props:t.default<PERSON><PERSON>,lazy:""},on:{"check-change":t.handle<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>},scopedSlots:t._u([{key:"default",fn:function(e){e.node;var r=e.data;return a("span",{staticClass:"custom-tree-node"},[t._v(" "+t._s(r.orgName)+" "),"0"===r.isAuthority?a("span",[a("span",{staticStyle:{float:"right",color:"#ccc","font-size":"12px",cursor:"not-allowed"}},[t._v("无操作权限")])]):t._e(),r.disabled?a("span",[a("span",{staticStyle:{float:"right",color:"#ccc","font-size":"12px"}},[t._v("该组织已选择")])]):t._e()])}}])})],1)])],1),a("ta-row",[a("ta-col",[a("div",{staticClass:"ant-modal-footer",staticStyle:{"text-align":"center"}},[a("ta-button",{on:{click:function(e){return t.$emit("modalCancel")}}},[a("ta-icon",{attrs:{type:"rollback"}}),t._v("返回")],1),a("ta-button",{attrs:{type:"primary"},on:{click:t.fnSaveOrUpdate}},[t._v("保存")])],1)])],1)],1)},n=[],o=a(61109),s={name:"adminAuthorityMg",props:["item"],data:function(){return{roleId:"",adminFilterText:"",treeData:[],defaultProps:{id:"orgId",label:"orgName",isLeaf:"isLeaf"},row:{col:{span:12,fullSpan:24}}}},watch:{adminFilterText:function(t){}},created:function(){this.roleId=this.item.roleId},methods:{handleCheckNodeChange:function(t,e,a){var r=this.$refs.tree.getCheckedKeys();if(e){if("0"===t.isAuthority)return this.$message.warning("您没有该组织的操作权限"),void this.$refs.tree.setChecked(t,!1);if(r.length>=2)for(var n=0;n<r.length;n++)r[n]!==t.orgId&&this.$refs.tree.setChecked(r[n],!1,!1)}},loadData:function(t,e){if(0===t.level){var a={roleId:this.roleId};o.Z.queryOrgAuthTreeByAsync(a,(function(t){return e(t.data.orgVos)}))}if(t.level>=1){var r=t.data.orgId,n={orgId:r,roleId:this.roleId};o.Z.queryOrgAuthTreeByAsync(n,(function(a){var r=a.data.orgVos;if(r[0].children&&r[0].children instanceof Array&&r[0].children.length>0){var n=r[0].children;if(t.data.disabled){var o=n.map((function(t){return t.disabled=!0,t}));return e(o)}return e(n)}}))}},fnSaveOrUpdate:function(){var t=this,e=this.$refs.tree.getCheckedNodes();if(e.length<1)this.$message.warning("请选择组织",2.5);else{var a=e[0],r={roleId:this.roleId,orgId:a.orgId,idPath:a.idPath};o.Z.addOrgAuth(r,(function(e){t.$message.success("保存数据成功"),t.$emit("modalCancel")}))}}}},l=s,i=a(1001),u=(0,i.Z)(l,r,n,!1,null,"0062e0af",null),c=u.exports},39856:function(t,e,a){a.r(e),a.d(e,{default:function(){return m}});var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"fit"},[a("ta-border-layout",{attrs:{layout:{header:"55px"},"center-cfg":{showBar:!0},"header-cfg":{showBorder:!1},showBorder:!1,"footer-cfg":{showBorder:!1}}},[a("template",{slot:"header"},[a("ta-breadcrumb",{staticStyle:{"line-height":"29px"},attrs:{separator:">"}},[a("ta-breadcrumb-item",[a("a",{on:{click:t.fnBackToHome}},[t._v("角色维度")])]),a("ta-breadcrumb-item",[t._v("组织范围权限")])],1)],1),a("div",{attrs:{slot:"centerExtraContent"},slot:"centerExtraContent"},[a("ta-alert",{attrs:{message:"当前角色为："+this.$route.query.roleName,type:"info",showIcon:""}})],1),a("ta-tabs",{staticClass:"fit"},[a("ta-tab-pane",{attrs:{tab:"组织范围权限"}},[a("ta-row",{staticStyle:{width:"100%","padding-top":"10px"}},[a("ta-col",{attrs:{span:t.row.col.span}},[a("ta-table",{attrs:{columns:t.orgColumns,dataSource:t.orgData,pagination:!1,rowKey:"orgId",defaultExpandedRowKeys:t.defaultExpandedRowKeys},scopedSlots:t._u([{key:"effecttime",fn:function(e,r){return[1==r.isAuthority?a("span",[t._v(" "+t._s(null==r.effectTime?"永久":t.moment(r.effectTime).format("YYYY-MM-DD"))+" ")]):a("span",[t._v(" -- ")])]}},{key:"action",fn:function(e){return a("span",{},[a("ta-table-operate",{attrs:{operateMenu:t.operateMenu}})],1)}}])},[a("span",{attrs:{slot:"customOrgNameTitle"},slot:"customOrgNameTitle"},[a("ta-icon",{attrs:{type:"smile-o"}}),t._v(" Name")],1)])],1)],1)],1),a("template",{slot:"tabBarExtraContent"},[a("ta-button",{on:{click:t.fnBackToHome}},[a("ta-icon",{attrs:{type:"rollback"}}),t._v("返回")],1),a("ta-button",{attrs:{type:"primary"},on:{click:t.fnShowModal}},[t._v("组织权限管理")])],1)],2),a("ta-modal",{attrs:{visible:t.authority.visible,centered:!0,destroyOnClose:!0,width:t.authority.width,footer:null,bodyStyle:{height:"500px"}},on:{cancel:function(e){t.authority.visible=!1}}},[a("template",{slot:"title"},[a("div",{staticStyle:{"text-align":"center"}},[t._v(" 组织权限管理 ")])]),a("role-authority-mg",{attrs:{item:t.item},on:{modalCancel:t.fnReLoadAuthority}})],2)],2)],1)},n=[],o=a(61109),s=a(36797),l=a.n(s),i=a(82057),u=[{title:"组织名称",dataIndex:"orgName",slots:{title:"customOrgNameTitle"}},{title:"有效期",scopedSlots:{customRender:"effecttime"}},{title:"组织路径",dataIndex:"namePath"},{title:"操作",align:"center",scopedSlots:{customRender:"action"}}],c={name:"roleOrgAuthority",components:{roleAuthorityMg:i["default"]},data:function(){var t=this;return{row:{col:{span:24}},authority:{visible:!1,width:"500px"},item:{},orgColumns:u,operateMenu:[{name:"删除",type:"confirm",confirmTitle:"确定要移除该审核的组织权限及其所有子权限吗?",onOk:function(e){t.fnAdminOrgDelete(e)}}],orgData:[],roleId:"",defaultExpandedRowKeys:[]}},activated:function(){this.item=this.$route.query,this.item.roleId?this.fnLoadDefault():this.fnBackToHome()},methods:{moment:l(),fnReLoadAuthority:function(){this.authority.visible=!1,this.fnLoadDefault()},fnBackToHome:function(){this.$router.push({name:"roleManagement"})},fnShowModal:function(){this.authority.visible=!0},fnLoadDefault:function(){var t=this;o.Z.queryOrgAuth({roleId:this.item.roleId},(function(e){t.orgData=e.data.orgScopeList}))},fnAdminOrgDelete:function(t){var e=this,a={roleId:this.item.roleId,orgId:t.orgId};o.Z.removeOrgAuth(a,(function(t){e.$message.success("删除成功"),e.fnLoadDefault()}))}}},d=c,f=a(1001),h=(0,f.Z)(d,r,n,!1,null,null,null),m=h.exports},61109:function(t,e){var a="/org/authority/examinerAuthorityRestService/";e["Z"]={queryUserNoWrapperByRoleId:function(t,e){var r=a+"queryUserNoWrapperByRoleId";Base.submit(null,{url:r,data:t},{successCallback:function(t){return e(t)}})},addExaminer:function(t,e){var r=a+"addExaminer";Base.submit(null,{url:r,data:t},{successCallback:function(t){return e(t)}})},updateExaminer:function(t,e){var r=a+"updateExaminer";Base.submit(null,{url:r,data:t},{successCallback:function(t){return e(t)}})},unableExaminer:function(t,e){var r=a+"unableExaminer";Base.submit(null,{url:r,data:t},{successCallback:function(t){return e(t)}})},enableExaminer:function(t,e){var r=a+"enableExaminer";Base.submit(null,{url:r,data:t},{successCallback:function(t){return e(t)}})},deleteBatchExaminer:function(t,e){var r=a+"deleteBatchExaminer";Base.submit(null,{url:r,data:t},{successCallback:function(t){return e(t)}})},queryOrgAuth:function(t,e){var r=a+"queryOrgAuth";Base.submit(null,{url:r,data:t},{successCallback:function(t){return e(t)}})},queryOrgAuthTreeByAsync:function(t,e){var r=a+"queryOrgAuthTreeByAsync";Base.submit(null,{url:r,data:t},{successCallback:function(t){return e(t)}})},queryOrgTreeByAsync:function(t,e){var r=a+"queryOrgTreeByAsync";Base.submit(null,{url:r,data:t},{successCallback:function(t){return e(t)}})},removeOrgAuth:function(t,e){var r=a+"removeOrgAuth";Base.submit(null,{url:r,data:t},{successCallback:function(t){return e(t)}})},addOrgAuth:function(t,e){var r=a+"addOrgAuth";Base.submit(null,{url:r,data:t},{successCallback:function(t){return e(t)}})},addBatchExaminerUser:function(t,e){var r=a+"addBatchRoleUser";Base.submit(null,{url:r,data:t},{successCallback:function(t){return e(t)}})},deleteBatchRoleUser:function(t,e){var r=a+"deleteBatchRoleUser";Base.submit(null,{url:r,data:t},{successCallback:function(t){return e(t)}})},deleteBatchUserRole:function(t,e){var r=a+"deleteBatchUserRole";Base.submit(null,{url:r,data:t},{successCallback:function(t){return e(t)}})},addBatchUserRole:function(t,e){var r=a+"addBatchUserRole";Base.submit(null,{url:r,data:t},{successCallback:function(t){return e(t)}})}}}}]);
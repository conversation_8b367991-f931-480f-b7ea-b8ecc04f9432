"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[2567],{69006:function(e,t,s){s.r(t),s.d(t,{default:function(){return U}});var a=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{ref:"authorityAgentContainer",staticClass:"fit",attrs:{id:"authorityAgent"}},[s("ta-border-layout",{attrs:{layout:{header:"70px",footer:"70px"},"center-cfg":{showBar:!0,toolBarStyle:{height:"50px",lineHeight:"48px"}},"footer-cfg":{showBorder:!1}}},[s("div",{staticClass:"center-box",attrs:{slot:"header"},slot:"header"},[s("ta-input-search",{staticClass:"search-box",attrs:{placeholder:"请输入角色名称","enter-button":"搜索"},on:{search:e.handleSearch},model:{value:e.searchInfo,callback:function(t){e.searchInfo=t},expression:"searchInfo"}})],1),s("div",{attrs:{slot:"centerExtraContent"},slot:"centerExtraContent"},[s("ta-tree-select",{staticStyle:{width:"250px","margin-right":"10px"},attrs:{"dropdown-style":{maxHeight:"300px",overflow:"auto"},placeholder:"请选择人员的所属组织",url:"org/authority/authorityAgentRestService/getOrgByAsync","tree-id":"orgTree","tree-node-id":"orgId","tree-data":e.options,"tree-node-label-prop":"namePath","tree-data-label":"orgName","tree-data-value":"orgId","allow-clear":""},on:{"update:treeData":function(t){e.options=t},"update:tree-data":function(t){e.options=t},change:e.handleChangeOrg}}),s("ta-checkbox",{attrs:{checked:e.searchCheckedOrgChild},on:{change:e.handleChangeChildOrg}},[e._v(" 包含子组织 ")]),s("div",{staticStyle:{float:"right"}},[s("ta-button",{staticClass:"step1",attrs:{type:"primary"},on:{click:e.handleToNewAuthorityAgent}},[e._v(" 新增代理角色 ")]),s("ta-button",{on:{click:e.handleBatchDelete}},[e._v(" 批量删除 ")])],1)],1),s("ta-table",{attrs:{"row-selection":{onChange:e.handleAgentRoleSelected,selectedRowKeys:e.selectedRowKeys},columns:e.agentRoleColumns,"data-source":e.agentRoleData,pagination:!1},scopedSlots:e._u([{key:"action",fn:function(t,a){return s("span",{},[s("ta-table-operate",{attrs:{"operate-menu":e.operateMenu}})],1)}}])}),s("div",{attrs:{slot:"footer"},slot:"footer"},[s("ta-pagination",{ref:"agentRolePager",staticStyle:{float:"right","margin-top":"10px"},attrs:{"show-size-changer":"","show-quick-jumper":"","data-source":e.agentRoleData,"default-page-size":30,params:e.fnAgentRolePageParams,url:"org/authority/authorityAgentRestService/queryAllAgentRole"},on:{"update:dataSource":function(t){e.agentRoleData=t},"update:data-source":function(t){e.agentRoleData=t}}})],1)],1),s("ta-modal",{attrs:{centered:!0,width:"1000px","destroy-on-close":!0,"mask-closable":!1,"get-container":e.setContainer},model:{value:e.addAgentModelVisible,callback:function(t){e.addAgentModelVisible=t},expression:"addAgentModelVisible"}},[s("span",{attrs:{slot:"title"},slot:"title"},[e._v("新增代理角色")]),s("new-authority-agent",{ref:"newAgent",on:{refreshAgentRole:e.fnGetAllAgentRole}}),s("template",{slot:"footer"},[s("ta-button",{key:"back",on:{click:e.handleHideModel}},[e._v(" 取消 ")]),s("ta-button",{key:"submit",attrs:{type:"primary"},on:{click:e.handleAddAgentRole}},[e._v(" 保存 ")])],1)],2),s("ta-modal",{attrs:{centered:!0,width:"1000px","destroy-on-close":!0,"mask-closable":!1,"get-container":e.setContainer},model:{value:e.modifyAgentModelVisible,callback:function(t){e.modifyAgentModelVisible=t},expression:"modifyAgentModelVisible"}},[s("span",{attrs:{slot:"title"},slot:"title"},[e._v("查看/修改权限")]),s("modify-authority-agent",{ref:"modifyAgent",attrs:{"agent-role":e.modifyAgentRole},on:{refreshAgentRole:e.fnGetAllAgentRole}}),s("template",{slot:"footer"},[s("ta-button",{key:"back",on:{click:e.handleHideModel}},[e._v(" 取消 ")]),s("ta-button",{key:"submit",attrs:{type:"primary"},on:{click:e.handleSaveModifyAgent}},[e._v(" 保存 ")])],1)],2)],1)},r=[],o="/org/authority/authorityAgentRestService/",i={queryAllAgentRole:function(e,t){Base.submit(null,{url:o+"queryAllAgentRole",data:{roleType:"03"}},{successCallback:function(e){return t(e.data)}})},queryCustomUsePermissionAsync:function(e,t){Base.submit(null,{url:o+"queryCustomUsePermissionAsync",data:e},{successCallback:function(e){return t(e.data)}})},updateAgentRoleAuthority:function(e,t){Base.submit(null,{url:o+"updateAgentRoleAuthority",data:e},{successCallback:function(e){return t(e.data)}})},loadAuthorityTree:function(e,t){Base.submit(null,{url:o+"queryAgentRoleAuthority",data:e},{successCallback:function(e){return t(e.data)}})},batchDeleteAgentRole:function(e,t){Base.submit(null,{url:o+"deleteBatchAgentRole",data:e},{successCallback:function(e){return t(e.data)}})},queryUsePermissionByUserId:function(e,t){Base.submit(null,{url:o+"queryUsePermissionByUserId",data:e},{successCallback:function(e){return t(e.data)}})},queryCustomUsePermissionByUserId:function(e,t){Base.submit(null,{url:o+"queryCustomUsePermissionByUserId",data:e},{successCallback:function(e){return t(e.data)}})},loadOrgTree:function(e,t){Base.submit(null,{url:o+"getOrgByAsync",data:e},{successCallback:function(e){return t(e.data)}})},queryUserList:function(e,t){var s=e.orgId,a=e.userId,r=e.includeChild,i=e.pageSize,n=e.pageNum,l=e.searchVal,u=e.searchType,c=e.searchParam,d={orgId:s,userId:a,showChildUser:r?1:0,pageSize:i,pageNumber:n,name:l};u&&(d[u]=c),Base.submit(null,{url:o+"queryReAgentUsersByOrgId",data:d},{successCallback:function(e){return t(e.data)}})},addAuthorityAgent:function(e,t){var s={userId:e.targetUserId,resourceIds:e.authoritySelectArray.join(","),customResourceIds:e.customResourceIds,customNeedChildIds:e.customNeedChildIds,customHalfNeedIds:e.customHalfNeedIds,effectTime:e.effectiveTime,theAgent:e.theAgent};Base.submit(null,{url:o+"addAgentRole",data:s},{successCallback:function(e){return t(e.data)}})}},n=function(){var e=this,t=this,s=t.$createElement,a=t._self._c||s;return a("div",{attrs:{id:"newAuthorityAgent"}},[a("ta-form",{attrs:{autoFormCreate:function(t){e.form=t}}},[a("ta-row",{attrs:{gutter:t.row.gutter}},[a("ta-col",{attrs:{span:t.row.formCol.span}},[a("ta-form-item",{attrs:{label:"被代理人员",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"resourceUser",fieldDecoratorOptions:{rules:[{required:!0,message:"请选择被代理人员"}]}}},[a("ta-user-input",{attrs:{selectTitle:"被代理人员选择",orgLoadFn:t.handleLoadOrgNode,userLoadFn:t.handleQueryUserList,userSelectCall:t.handleGetUserListResult}})],1)],1),a("ta-col",{attrs:{span:t.row.formCol.span}},[a("ta-form-item",{attrs:{label:"委派人员",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"targetUser",fieldDecoratorOptions:{rules:[{required:!0,message:"请选择委派人员"}]}}},[a("ta-user-input",{attrs:{selectTitle:"委派人员选择",orgLoadFn:t.handleLoadOrgNode,userLoadFn:t.handleQueryTargetUserList,userSelectCall:t.handleGetTargetUserListResult}})],1)],1),a("ta-col",{attrs:{span:t.row.formCol.span}},[a("ta-form-item",{attrs:{label:"委派截止日期",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"effectiveTime",fieldDecoratorOptions:{rules:[{required:!0,message:"请选择委派截止日期"}]}}},[a("ta-date-picker",{staticStyle:{width:"100%"}})],1)],1)],1)],1),a("ta-tabs",{attrs:{defaultActiveKey:"1"}},[a("ta-tab-pane",{key:"1",attrs:{tab:"功能菜单权限"}},[a("ta-row",{staticStyle:{height:"300px"}},[a("ta-col",{staticClass:"fit",staticStyle:{"border-right":"1px solid #eee"},attrs:{span:4}},[a("ta-menu",{attrs:{mode:"inline"},on:{select:t.handleLoadUserPermission},model:{value:t.selectedKeys,callback:function(e){t.selectedKeys=e},expression:"selectedKeys"}},t._l(t.authorityMenuList,(function(e){return a("ta-menu-item",{key:e.resourceId},[a("ta-icon",{attrs:{type:"appstore"}}),t._v(" "+t._s(e.resourceName)+" ")],1)})),1)],1),a("ta-col",{staticClass:"fit row-tree-context",attrs:{span:20}},[a("ta-e-tree",{ref:"authorityTreeRef",attrs:{data:t.authorityTree,"show-checkbox":"","node-key":"resourceId","default-expand-all":"","highlight-current":"",props:t.defaultProps,"default-checked-keys":t.checkedList}})],1)],1)],1),a("ta-tab-pane",{key:"2",attrs:{tab:"自定义资源权限"}},[a("ta-row",{staticStyle:{height:"300px"}},[a("ta-col",{staticClass:"fit",staticStyle:{"border-right":"1px solid #eee"},attrs:{span:4}},[a("ta-menu",{attrs:{mode:"inline"},on:{click:t.onSelectCustom},model:{value:t.customSelectKey,callback:function(e){t.customSelectKey=e},expression:"customSelectKey"}},t._l(t.authorityCustomMenuList,(function(e){return a("ta-menu-item",{key:e.id},[a("ta-icon",{attrs:{type:"appstore"}}),t._v(" "+t._s(e.name)+" ")],1)})),1)],1),a("ta-col",{staticClass:"fit row-tree-context",attrs:{span:20}},[!0===t.customTreeVisible?a("ta-e-tree",{ref:"authorityCustomTreeRef",attrs:{load:t.loadNode,"show-checkbox":"","node-key":"id","expand-on-click-node":!0,"highlight-current":"",props:t.defaultCustomProps,"check-strictly":!0,lazy:""},on:{check:t.nodeCheck}}):t._e()],1)],1)],1)],1)],1)},l=[],u=s(89584),c={name:"newAuthorityAgent",data:function(){return{row:{gutter:16,formCol:{span:8},col:{span:12}},formItemLayout:{labelCol:{span:8},wrapperCol:{span:16}},curUserId:"",targetUserId:"",authorityMenuList:[],authorityTree:[],defaultProps:{children:"children",label:"resourceName",id:"resourceId"},halfCheckedList:[],checkedList:[],resourceIdList:[],selectedKeys:[],resourceId:"",authorityCustomMenuList:[],authorityCustomTree:[],defaultCustomProps:{children:"children",label:"name",id:"id",isLeaf:"isLeaf"},customResourceIdList:[],customResourceId:"",customSelectItem:{},customSelectKey:[],customLeafList:[],customTreeVisible:!1,expandList:[],customCache:{},customHalfCheck:[],customFullCheck:[],customChange:!1}},methods:{loadNode:function(e,t){var s=this,a=this.customSelectItem;if(0===e.level){var r=this.inArray(a.id,this.customFullCheck),o=this.inArray(a.id,this.customHalfCheck);return a.idPath=[a.id],0==a.auth&&(a.disabled=!0),t([a]),void this.$nextTick((function(){var e=s.$refs.authorityCustomTreeRef;if(o>=0){var t=e.getNode(a.id);t.isLeaf=a.isLeaf,t.indeterminate=!0}else r>=0?e.setChecked(a.id,!0,!1):e.setChecked(a.id,!1,!1)}))}var n=e.data.id;this.expandList.push(n);var l={userId:this.curUserId,resourceId:n},c=!1;i.queryCustomUsePermissionAsync(l,(function(a){var r=a.list,o=e.data.idPath;r.forEach((function(e){0==e.auth&&(e.disabled=!0)})),t(r),r.forEach((function(t){var a=t.id,r=s.$refs.authorityCustomTreeRef,i=r.getNode(a);i.data.idPath=[].concat((0,u.Z)(o),[i.data.id]);var n=s.inArray(a,s.customLeafList);i.isLeaf=n>=0,t.isLeaf=i.isLeaf,e.checked?(i.checked=!0,c=!0):e.indeterminate||(i.checked=!1,c=!0)})),c||(s.customFullCheck.forEach((function(e){var t=s.$refs.authorityCustomTreeRef;t.setChecked(e,!0,!1)})),e.checked||s.customHalfCheck.forEach((function(e){var t=s.$refs.authorityCustomTreeRef,a=t.getNode(e);null!=a&&(a.indeterminate=!0)})))}))},nodeCheck:function(e,t,s){if(s){this.customChange=!0;var a=e.idPath,r=this.$refs.authorityCustomTreeRef,o=r.getNode(e.id),i=o.checked;if(i){this.changeCheckStatus(o,i);for(var n=a.length-2;n>=0;n--){var l=a[n],u=r.getNode(l);u.checked||(u.indeterminate=!0)}}else{this.changeCheckStatus(o,i);for(var c=function(e){var t=a[e],s=r.getNode(t),o=s.childNodes;s.checked=!1,s.indeterminate=!1,null!=o&&o.length>0&&o.forEach((function(e){(e.checked||e.indeterminate)&&(s.indeterminate=!0)}))},d=a.length-2;d>=0;d--)c(d)}this.changed=!1}},changeCheckStatus:function(e,t){var s=this;e.indeterminate=!1,e.checked=t;var a=e.childNodes;null!=a&&a.length>0&&a.forEach((function(e){s.changeCheckStatus(e,t)}))},inArray:function(e,t){for(var s=0;s<t.length;s++)if(e==t[s])return s;return-1},onSelectCustom:function(e){var t=e.item,s=e.key;e.keyPath,this.customSelectItem.name;this.customSelectItem=this.authorityCustomMenuList[t.index];var a=this.$refs.authorityCustomTreeRef,r=a.getHalfCheckedKeys(),o=a.getCheckedKeys();if(this.customChange){var i=this.customSelectKey[0];(r.length>0||o.length>0)&&(this.customCache[i]={half:r,full:o,result:this.getResourceTreeParam(i)})}var n=this.customCache[s];null!=n?(this.customHalfCheck=n.half,this.customFullCheck=n.full):(this.customHalfCheck=[],this.customFullCheck=[]),this.handleLoadCustomUserPermission(s)},getResourceTreeParam:function(e){var t=this,s=this.$refs.authorityCustomTreeRef,a=s.getCheckedNodes(),r=[],o=s.getHalfCheckedNodes(),i=[],n=[];return o.forEach((function(e){s.getNode(e.id).expanded||n.push(e.id)})),a.forEach((function(e){e.isLeaf||t.inArray(e.id,t.expandList)>=0?r.push(e.id):i.push(e.id)})),{categoryId:e,resourceIds:r,needChildIds:i,halfNeedChildIds:n}},handleLoadOrgNode:function(e,t){var s=e.data&&e.data.orgId||"";i.loadOrgTree({orgId:s},(function(e){t(e.orgTree)}))},handleGetTargetUserListResult:function(e){this.targetUserId=e.userId},handleGetUserListResult:function(e){var t=this;e&&(this.curUserId=e.userId,i.queryUsePermissionByUserId({userId:e.userId},(function(e){t.authorityMenuList=e.usePermissionPos,t.authorityMenuList[0]&&t.handleLoadUserPermission(t.authorityMenuList[0])})),i.queryCustomUsePermissionByUserId({userId:e.userId},(function(e){t.authorityCustomMenuList=e.customUsePermissionPos,t.authorityCustomMenuList.length>0&&(t.customSelectItem=t.authorityCustomMenuList[0],t.handleLoadCustomUserPermission(t.customSelectItem.id))})),this.authorityTree=[],this.authorityCustomTree=[],this.resourceId="",this.customResourceId="",this.checkedList=[],this.halfCheckedList=[],this.customResourceIdList=[],this.expandList=[],this.customCache={},this.customChange=!1,this.customTreeVisible=!1)},handleLoadUserPermission:function(e){var t=this;if(null!==this.resourceId&&""!==this.resourceId){var s=this.$refs.authorityTreeRef.getCheckedKeys(),a=this.resourceIdList.indexOf(this.resourceId);if(0!==s.length){var r=this.$refs.authorityTreeRef.getHalfCheckedKeys();-1===a?(this.checkedList.push(s),this.halfCheckedList.push(r),this.resourceIdList.push(this.resourceId)):(this.checkedList.splice(a,1,s),this.halfCheckedList.splice(a,1,r))}else-1!==a&&(this.checkedList.splice(a,1),this.halfCheckedList.splice(a,1))}if(this.resourceId!==e){var o=e.key?e.key:e.resourceId;this.resourceId=o,this.selectedKeys=[o],i.queryUsePermissionByUserId({userId:this.curUserId,resourceId:o},(function(e){t.authorityTree=e.usePermissionPos;var s=t.resourceIdList.indexOf(t.resourceId);-1!==s&&t.$refs.authorityTreeRef.setCheckedKeys(t.checkedList[s])}))}},handleLoadCustomUserPermission:function(e){var t=this;this.customTreeVisible=!1,this.customChange=!1,this.customResourceId=e,this.customSelectKey=[e],i.queryCustomUsePermissionByUserId({userId:this.curUserId,resourceId:e},(function(e){t.customLeafList=e.leafList,t.customTreeVisible=!0}))},getCustomUsePermissionResult:function(e){var t=this,s=this.authorityCustomMenuList,a={resourceIds:[],needChildIds:[],halfNeedChildIds:[]};return s.forEach((function(s){var r,o=s.id;if(o==e)if(t.customChange)r=t.getResourceTreeParam(e);else{var i=t.customCache[s.id];null!=i&&(r=t.customCache[s.id].result)}else{var n=t.customCache[s.id];null!=n&&(r=t.customCache[s.id].result)}null!=r&&(a.resourceIds=[].concat((0,u.Z)(a.resourceIds),(0,u.Z)(r.resourceIds)),a.needChildIds=[].concat((0,u.Z)(a.needChildIds),(0,u.Z)(r.needChildIds)),a.halfNeedChildIds=[].concat((0,u.Z)(a.halfNeedChildIds),(0,u.Z)(r.halfNeedChildIds)))})),a},handleQueryUserList:function(e,t){var s=e.orgId,a=e.userId,r=e.includeChild,o=e.pageSize,n=e.pageNum,l=e.searchVal,u=e.searchType,c=e.searchParam;i.queryUserList({orgId:s,userId:a,includeChild:r,pageSize:o,pageNum:n,searchVal:l,searchType:u,searchParam:c},(function(e){t(e.reAgentVos.list)}))},handleQueryTargetUserList:function(e,t){var s=this,a=e.orgId,r=e.userId,o=e.includeChild,n=e.pageSize,l=e.pageNum,u=e.searchVal,c=e.searchType,d=e.searchParam;i.queryUserList({orgId:a,userId:r,includeChild:o,pageSize:n,pageNum:l,searchVal:u,searchType:c,searchParam:d},(function(e){var a=e.reAgentVos.list&&e.reAgentVos.list.filter((function(e){return s.curUserId!=e.userId}))||[];t(a)}))},fnNewAgentRole:function(){var e=this;this.form.validateFields((function(t,s){t||e.fnSave()}))},fnSave:function(){var e=this;this.authorityMenuList.forEach((function(t){t.resourceId===e.resourceId&&e.handleLoadUserPermission(e.resourceId)}));var t=this.getCustomUsePermissionResult(this.customResourceId);0!==this.checkedList.length||0!==t.resourceIds.length||0!==t.needChildIds.length?i.addAuthorityAgent({targetUserId:this.targetUserId,authoritySelectArray:[].concat((0,u.Z)([].concat.apply([],this.checkedList)),(0,u.Z)([].concat.apply([],this.halfCheckedList))),customResourceIds:t.resourceIds.join(","),customNeedChildIds:t.needChildIds.join(","),customHalfNeedIds:t.halfNeedChildIds.join(","),authorityCustomSelectArray:[],effectiveTime:this.form.getFieldValue("effectiveTime").format("YYYY-MM-DD")+" 23:59:59",theAgent:this.curUserId},(function(t){e.$message.success("新增代理角色成功"),e.$emit("refreshAgentRole")})):this.$message.error("未选择要代理的权限，不能保存！")}}},d=c,h=s(1001),f=(0,h.Z)(d,n,l,!1,null,"861e0c90",null),m=f.exports,g=function(){var e=this,t=this,s=t.$createElement,a=t._self._c||s;return a("div",{attrs:{id:"modifyAuthorityAgent"}},[a("ta-form",{attrs:{autoFormCreate:function(t){e.form=t},layout:"inline"}},[a("ta-form-item",{attrs:{label:"代理角色名称",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"agentRoleName",fieldDecoratorOptions:{rules:[{required:!0,message:"缺少代理角色名"}]}}},[a("ta-input",{attrs:{disabled:""}})],1),a("ta-form-item",{attrs:{label:"委派截止日期",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"effectiveTime",fieldDecoratorOptions:{rules:[{required:!0,message:"请选择委派截止日期"}]}}},[a("ta-date-picker")],1)],1),a("ta-row",{attrs:{gutter:t.row.gutter}},[a("ta-col",{staticStyle:{height:"360px"},attrs:{span:t.row.col.span}},[a("ta-tabs",{staticClass:"fit",attrs:{defaultActiveKey:"1"}},[a("ta-tab-pane",{key:"1",attrs:{tab:"功能菜单权限"}},[a("ta-table",{staticStyle:{"padding-top":"10px"},attrs:{columns:t.menuAuthorityColumns,dataSource:t.menuData,defaultExpandAllRows:"",size:"small",pagination:!1}})],1)],1)],1),a("ta-col",{staticStyle:{height:"360px"},attrs:{span:t.row.col.span}},[a("ta-tabs",{staticClass:"fit",attrs:{defaultActiveKey:"1"}},[a("ta-tab-pane",{key:"1",attrs:{tab:"自定义资源权限"}},[a("ta-table",{staticStyle:{"padding-top":"10px"},attrs:{columns:t.customAuthorityColumns,dataSource:t.customData,defaultExpandAllRows:"",size:"small",pagination:!1}})],1)],1)],1)],1)],1)},y=[],p=s(36797),C=s.n(p),I=[{title:"功能名称",dataIndex:"resourceName"}],A=[{title:"功能名称",dataIndex:"resourceName"}],v={name:"modifyAuthorityAgent",props:{agentRole:{type:Object,required:!0}},data:function(){return{row:{gutter:16,col:{span:12}},formItemLayout:{labelCol:{span:10},wrapperCol:{span:14}},menuAuthorityColumns:I,customAuthorityColumns:A,menuData:[],customData:[]}},methods:{moment:C(),fnSaveModifyAgentRole:function(){var e=this;this.form.validateFields((function(t,s){t||e.fnSave()}))},fnSave:function(){var e=this;i.updateAgentRoleAuthority({roleId:this.agentRole.roleId,effectTime:this.form.getFieldValue("effectiveTime").format("YYYY-MM-DD")+" 23:59:59"},(function(t){e.$message.success("修改成功"),e.$emit("refreshAgentRole")}))},fnLoadAuthorityTree:function(){var e=this;i.loadAuthorityTree({targetRoleId:this.agentRole.roleId},(function(t){e.menuData=t.repermissions,e.customData=t.customRepermissions}))}},mounted:function(){var e=C()(this.agentRole.effectiveTime||"","YYYY-MM-DD");this.form.setFieldsValue({agentRoleName:this.agentRole.roleName,effectiveTime:e._isValid?e:null}),this.fnLoadAuthorityTree()}},k=v,b=(0,h.Z)(k,g,y,!1,null,null,null),R=b.exports,L=s(80790),w=[{title:"角色名称",dataIndex:"roleName",width:"10%",overflowTooltip:!0},{title:"组织路径",dataIndex:"namePath",width:"30%",overflowTooltip:!0},{title:"委派截止日期",dataIndex:"effectiveTime",width:"10%",customRender:function(e){return e&&e.split(" ")[0]}},{title:"创建人",dataIndex:"createUserName",width:"10%",overflowTooltip:!0},{title:"查看/修改权限",key:"action",align:"center",width:160,scopedSlots:{customRender:"action"}}],S={name:"authorityAgent",components:{newAuthorityAgent:m,modifyAuthorityAgent:R},mixins:[L.Z],data:function(){var e=this;return{searchInfo:"",options:[],searchOrgIds:"",searchCheckedOrgChild:!0,agentRoleColumns:w,operateMenu:[{name:"",icon:"search",onClick:function(t){e.handleOperation(t)}}],agentRoleData:[],selectedRowKeys:[],selectedAgentRoles:[],addAgentModelVisible:!1,modifyAgentModelVisible:!1,modifyAgentRole:{}}},mounted:function(){this.fnGetAllAgentRole();var e=[{element:".step1",popover:{title:"新增代理角色",description:"权限代理能够将某人员的角色权限临时代理给另一个指定人员",position:"left"}}];this.fnCommonGuide(e)},methods:{setContainer:function(){return document.getElementById("authorityAgent")},handleSearch:function(){this.$refs.agentRolePager.loadData()},handleChangeOrg:function(e){this.searchOrgIds=e,this.handleSearch()},handleChangeChildOrg:function(e){this.searchCheckedOrgChild=e.target.checked,this.handleSearch()},handleAddAgentRole:function(){this.$refs.newAgent.fnNewAgentRole()},handleSaveModifyAgent:function(){this.$refs.modifyAgent.fnSaveModifyAgentRole()},handleHideModel:function(){this.addAgentModelVisible=!1,this.modifyAgentModelVisible=!1},handleAgentRoleSelected:function(e,t){this.selectedRowKeys=e,this.selectedAgentRoles=t},handleToNewAuthorityAgent:function(){this.addAgentModelVisible=!0},handleOperation:function(e){this.modifyAgentRole=e,this.modifyAgentModelVisible=!0},fnGetAllAgentRole:function(){this.addAgentModelVisible=!1,this.modifyAgentModelVisible=!1,this.$refs.agentRolePager.loadData()},fnAgentRolePageParams:function(){var e={},t=this.searchOrgIds;void 0!==t&&""!==t&&(e.orgId=t);var s=this.searchCheckedOrgChild?"1":"0";return e.includeChild=s,""!==this.searchInfo&&(e.roleName=this.searchInfo),e},handleBatchDelete:function(){var e=this,t=this.selectedAgentRoles.map((function(e){return e.roleId}));t.length<1?this.$message.warn("请先勾选要删除的代理角色"):this.$confirm({title:"是否删除已选中的代理角色?",okText:"删除",okType:"danger",onOk:function(){i.batchDeleteAgentRole({roleIds:t.join(",")},(function(t){e.$message.success("删除代理角色成功"),e.fnGetAllAgentRole()}))}})}}},T=S,x=(0,h.Z)(T,a,r,!1,null,"fa5f0082",null),U=x.exports},80790:function(e,t,s){var a=s(76698);s(34530);t["Z"]={activated:function(){var e=window.pageVmObj,t=e["steps_"+e._route.name];this.methods?this.methods.fnCommonGuide(t):this.fnCommonGuide(t)},deactivated:function(){this.methods?this.methods.fnCommonGuide([],!0):this.fnCommonGuide([],!0)},methods:{fnCommonGuide:function(e,t){var s=window.pageVmObj;t?(s.driver.reset(),window.fnPageGuide=null):(s["steps_"+s._route.name]=e,s.driver=new a.Z({allowClose:!1}),window.fnPageGuide=function(){s.driver.defineSteps(e),s.driver.start()})}}}}}]);
"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[1676],{46044:function(e,t,s){s.r(t),s.d(t,{default:function(){return d}});var a=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"fit",attrs:{id:"batchAuthority"}},[s("ta-border-layout",{attrs:{layout:{header:"55px"}}},[s("template",{slot:"header"},[s("ta-breadcrumb",{staticStyle:{"line-height":"29px"},attrs:{separator:">"}},[s("ta-breadcrumb-item",[s("a",{on:{click:e.fnBack}},[e._v("角色维度")])]),"add"==e.batchType?s("ta-breadcrumb-item",[e._v("批量授权")]):s("ta-breadcrumb-item",[e._v("批量回收权限")])],1)],1),s("ta-alert",{staticClass:"notice-box",attrs:{message:"当前批量操作的角色为："+e.roleNames,type:"info",showIcon:""}}),s("ta-tabs",{staticClass:"fit content-box",attrs:{defaultActiveKey:"1"},on:{change:e.onTabsChange}},[s("template",{slot:"tabBarExtraContent"},[s("ta-button-group",[s("ta-button",{on:{click:e.fnBack}},[s("ta-icon",{attrs:{type:"rollback"}}),e._v("返回")],1)],1)],1),s("ta-tab-pane",{key:"1",attrs:{tab:"功能菜单权限"}},[s("ta-row",{staticClass:"fit"},[s("ta-col",{staticClass:"fit",staticStyle:{"border-right":"1px solid #eee"},attrs:{span:4}},[s("div",{staticClass:"menu-title"},[s("ta-icon",{attrs:{type:"menu-fold"}}),s("span",{staticStyle:{"margin-left":"5px"}},[e._v("功能一级菜单")])],1),s("ta-divider",{staticStyle:{margin:"0"}}),s("ta-menu",{attrs:{mode:"inline",selectedKeys:e.menuSelectedKeys},on:{click:e.onSelectMenu}},e._l(e.menuData,(function(t){return s("ta-menu-item",{key:t.resourceId},[s("ta-icon",{attrs:{type:"appstore"}}),e._v(e._s(t.name)+" ")],1)})),1)],1),s("ta-col",{staticClass:"right-box",attrs:{span:14}},[e.authrityTree.length>0?s("div",{staticClass:"fit",staticStyle:{border:"1px solid #e8e8e8"}},[s("ta-row",{staticStyle:{height:"5%"}},[s("ta-col",{staticClass:"col-header",attrs:{span:24}},[e._v(" 可操作功能菜单权限 ")])],1),s("ta-divider"),s("div",{staticClass:"authority-box"},[s("ta-input",{staticStyle:{width:"250px","margin-left":"10px"},attrs:{placeholder:"输入关键字进行过滤"},model:{value:e.filterText,callback:function(t){e.filterText=t},expression:"filterText"}}),"add"==e.batchType?s("ta-button",{staticClass:"button-save",attrs:{type:"primary"},on:{click:e.fnSave}},[e._v("批量授予")]):s("ta-button",{staticClass:"button-save",attrs:{type:"primary"},on:{click:e.fnSave}},[e._v("批量回收")]),s("ta-e-tree",{ref:"atree",attrs:{data:e.authrityTree,"show-checkbox":"","node-key":"resourceId","highlight-current":"",props:e.defaultProps,"default-expand-all":"","expand-on-click-node":!1,"filter-node-method":e.filterNode}})],1)],1):e._e()])],1)],1)],2)],2)],1)},u=[],n=s(89584),r=s(46981),i={name:"batchAuthority",data:function(){return{roles:[],roleNames:"",batchType:"",activeKey:"1",menuData:[],menuCustomData:[],authrityTree:[],authrityCustomTree:[],defaultProps:{children:"children",label:"name",id:"resourceId"},defaultCustomProps:{children:"children",label:"name",id:"id"},filterText:"",filterCustomText:"",menuSelectedKeys:[],customMenuSelectedKeys:[]}},watch:{filterText:function(e){this.$refs.atree.filter(e)},filterCustomText:function(e){this.$refs.btree.filter(e)}},methods:{fnBack:function(){this.$router.push({name:"publicRoleManager"})},onTabsChange:function(e){this.activeKey=e},onSelectMenu:function(e){e.item;var t=e.key,s=e.keyPath;this.menuSelectedKeys=s,this.fnQueryRePermissionByResourceId(t)},onSelectCustomMenu:function(e){e.item;var t=e.key,s=e.keyPath;this.customMenuSelectedKeys=s,this.fnQueryReCustomPermissionByResourceId(t)},fnQueryRePermissionByRoleId:function(){var e=this;r.Z.queryRePermission(null,(function(t){e.menuData=t.data.rePermissions,e.$nextTick((function(){e.menuSelectedKeys=[e.menuData[0].resourceId],e.fnQueryRePermissionByResourceId(e.menuData[0].resourceId)}))}))},fnQueryRePermissionByResourceId:function(e){var t=this;r.Z.queryRePermission({resourceId:e},(function(e){t.authrityTree=e.data.rePermissions}))},fnQueryReCustomPermissionByRoleId:function(){var e=this;r.Z.queryCustomRePermission(null,(function(t){e.menuCustomData=t.data.customRePermissions,e.$nextTick((function(){e.customMenuSelectedKeys=e.menuCustomData.length?[e.menuCustomData[0].id]:[],e.fnQueryReCustomPermissionByResourceId(e.menuData[0].id)}))}))},fnQueryReCustomPermissionByResourceId:function(e){var t=this;r.Z.queryCustomRePermission({resourceId:e},(function(e){t.authrityCustomTree=e.data.customRePermissions}))},filterNode:function(e,t){return!e||-1!==t.name.indexOf(e)},fnSave:function(){"add"===this.batchType?this.fnAddBatch():this.fnDeleteBatch()},fnAddBatch:function(){var e=this,t=[];this.roles.forEach((function(e,s){t.push(e.roleId)}));var s={};if(s={roleIds:t.join(",")},"1"==this.activeKey){var a=this.$refs.atree,u=[].concat((0,n.Z)(a.getCheckedKeys()),(0,n.Z)(a.getHalfCheckedKeys()));s.resourceIds=u.join(",")}else if("2"==this.activeKey){var i=this.$refs.btree,c=[].concat((0,n.Z)(i.getCheckedKeys()),(0,n.Z)(i.getHalfCheckedKeys()));s.customResourceIds=c.join(",")}r.Z.addBatchUsePermissionByMoreRole(s,(function(t){e.$message.success("批量授权成功")}))},fnDeleteBatch:function(){var e=this,t=[];this.roles.forEach((function(e,s){t.push(e.roleId)}));var s={};if(s={roleIds:t.join(",")},"1"==this.activeKey){var a=this.$refs.atree,u=[].concat((0,n.Z)(a.getCheckedKeys()),(0,n.Z)(a.getHalfCheckedKeys()));s.resourceIds=u.join(",")}else if("2"==this.activeKey){var i=this.$refs.btree,c=[].concat((0,n.Z)(i.getCheckedKeys()),(0,n.Z)(i.getHalfCheckedKeys()));s.customResourceIds=c.join(",")}r.Z.deleteBatchUsePermissionByMoreRole(s,(function(t){e.$message.success("批量回收权限成功")}))}},activated:function(){if(this.$route.params.roles instanceof Object){this.roles=this.$route.params.roles;var e=[];this.roles.forEach((function(t,s){e.push(t.roleName)})),this.roleNames=e.join("，"),this.batchType=this.$route.params.batchType,this.fnQueryRePermissionByRoleId(),this.fnQueryReCustomPermissionByRoleId()}else this.$router.push({name:"publicRoleManager"})}},c=i,o=s(1001),l=(0,o.Z)(c,a,u,!1,null,"0d1960e0",null),d=l.exports},46981:function(e,t){var s="org/authority/roleAuthorityManagementRestService/";t["Z"]={queryCurrentAdminRoleWrapeOrgTree:function(e,t){Base.submit(null,{url:s+"queryCurrentAdminRoleWrapeOrgTree",data:e,showPageLoading:!1},{successCallback:function(e){return t(e)}})},queryRolesByOrgId:function(e,t){Base.submit(null,{url:s+"queryRolesByOrgId",data:e},{successCallback:function(e){return t(e)}})},queryAuthRole:function(e,t){Base.submit(null,{url:s+"queryAuthRole",data:e},{successCallback:function(e){return t(e)}})},copyResource:function(e,t,a){Base.submit(e,{url:s+"copyResource",data:t,autoValid:!0},{successCallback:function(e){return a(e)}}).then((function(e){})).catch((function(e){}))},queryUsePermissionByRoleId:function(e,t){Base.submit(null,{url:s+"queryUsePermissionByRoleId",data:e},{successCallback:function(e){return t(e)}})},queryCustomUsePermissionByRoleId:function(e,t){Base.submit(null,{url:s+"queryCustomUsePermissionByRoleId",data:e},{successCallback:function(e){return t(e)}})},copyRole:function(e,t,a){Base.submit(e,{url:s+"copyRole",data:t,autoValid:!0},{successCallback:function(e){return a(e)}}).then((function(e){})).catch((function(e){}))},queryRePermission:function(e,t){Base.submit(null,{url:s+"queryRePermission",data:e},{successCallback:function(e){return t(e)}})},queryCustomRePermission:function(e,t){Base.submit(null,{url:s+"queryCustomRePermission",data:e},{successCallback:function(e){return t(e)}})},addBatchUsePermissionByMoreRole:function(e,t){Base.submit(null,{url:s+"addBatchUsePermissionByMoreRole",data:e},{successCallback:function(e){return t(e)}})},deleteBatchUsePermissionByMoreRole:function(e,t){Base.submit(null,{url:s+"deleteBatchUsePermissionByMoreRole",data:e},{successCallback:function(e){return t(e)}})},addRole:function(e,t,a){Base.submit(e,{url:s+"addRole",data:t,autoValid:!0},{successCallback:function(e){return a(e)}}).then((function(e){})).catch((function(e){}))},updateRoleByRoleId:function(e,t,a){Base.submit(null,{url:s+"updateRoleByRoleId",data:t,autoValid:!0},{successCallback:function(e){return a(e)}}).then((function(e){})).catch((function(e){}))},updateBatchUsePermissionEffectiveTime:function(e,t){Base.submit(null,{url:s+"updateBatchUsePermissionEffectiveTime",data:e},{successCallback:function(e){return t(e)}})},updateUsePermissionEffectiveTime:function(e,t){Base.submit(null,{url:s+"updateUsePermissionEffectiveTime",data:e},{successCallback:function(e){return t(e)}})},addUsePermission:function(e,t){Base.submit(null,{url:s+"addUsePermission",data:e},{successCallback:function(e){return t(e)}})},changeRestAuthority:function(e,t){Base.submit(null,{url:s+"changeRestAuthority",data:e},{successCallback:function(e){return t(e)}})},queryRePermissionByRoleId:function(e,t){Base.submit(null,{url:s+"queryRePermissionByRoleId",data:e},{successCallback:function(e){return t(e)}})},updateBatchCustomResourceUsePermissionEffectiveTime:function(e,t){Base.submit(null,{url:s+"updateBatchCustomResourceUsePermissionEffectiveTime",data:e},{successCallback:function(e){return t(e)}})},updateCustomResourceUsePermissionEffectiveTime:function(e,t){Base.submit(null,{url:s+"updateCustomResourceUsePermissionEffectiveTime",data:e},{successCallback:function(e){return t(e)}})},queryCustomRePermissionByRoleId:function(e,t){Base.submit(null,{url:s+"queryCustomRePermissionByRoleId",data:e},{successCallback:function(e){return t(e)}})},addCustomResourceUsePermission:function(e,t){Base.submit(null,{url:s+"addCustomResourceUsePermission",data:e},{successCallback:function(e){return t(e)}})},updateRoleEffectiveByRoleId:function(e,t){Base.submit(null,{url:s+"updateRoleEffectiveByRoleId",data:e},{successCallback:function(e){return t(e)}})},deleteBatchRole:function(e,t){Base.submit(null,{url:s+"deleteBatchRole",data:e},{successCallback:function(e){return t(e)}})},deleteRoleUserByKey:function(e,t){Base.submit(null,{url:s+"deleteRoleUserByKey",data:e},{successCallback:function(e){return t(e)}})},deleteBatchRoleUsers:function(e,t){Base.submit(null,{url:s+"deleteBatchRoleUser",data:e},{successCallback:function(e){return t(e)}})},queryUsersByRoleId:function(e,t){Base.submit(null,{url:s+"queryUsersByRoleId",data:e},{successCallback:function(e){return t(e)}})},queryUsersByRoleIdNoPage:function(e,t){Base.submit(null,{url:s+"queryUsersByRoleIdNoPage",data:e},{successCallback:function(e){return t(e)}})},queryUsersNoWraperByRoleId:function(e,t){Base.submit(null,{url:s+"queryUsersNoWraperByRoleId",data:e},{successCallback:function(e){return t(e)}})},queryUsersNoWraperByRoleIdNoPage:function(e,t){Base.submit(null,{url:s+"queryUsersNoWraperByRoleIdNoPage",data:e},{successCallback:function(e){return t(e)}})},addBatchRoleUsers:function(e,t,a){Base.submit(null,{url:s+"addBatchRoleUsers",data:e,showPageLoading:!1},{successCallback:function(e){return t(e)},failCallback:function(e){return a(e)}})},deleteBatchRoleUser:function(e,t,a){Base.submit(null,{url:s+"deleteBatchRoleUser",data:e,showPageLoading:!1},{successCallback:function(e){return t(e)},failCallback:function(e){return a(e)}})},addBatchUserRole:function(e,t){Base.submit(null,{url:s+"addBatchUserRole",data:e},{successCallback:function(e){return t(e)}})},deleteBatchUserRole:function(e,t){Base.submit(null,{url:s+"deleteBatchUserRole",data:e},{successCallback:function(e){return t(e)}})},queryCustomRePermissionAsync:function(e,t){Base.submit(null,{url:s+"queryCustomRePermissionAsync",data:e},{successCallback:function(e){return t(e)}})},addAllRoleUsersByOrgId:function(e,t){Base.submit(null,{url:s+"addAllRoleUsersByOrgId",data:e},{successCallback:function(e){return t(e)}})}}}}]);
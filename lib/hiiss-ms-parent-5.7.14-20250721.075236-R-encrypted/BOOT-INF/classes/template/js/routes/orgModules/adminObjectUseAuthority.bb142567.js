"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[7389],{82947:function(e,t,s){s.d(t,{Z:function(){return o}});var n=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",[s("div",{staticClass:"menu-title"},[s("ta-icon",{attrs:{type:"menu-fold"}}),s("span",{staticStyle:{"margin-left":"5px","font-weight":"bold"}},[e._v(e._s(e.title))])],1),s("ta-divider",{staticStyle:{margin:"0"}}),s("ta-menu",{attrs:{mode:"inline",selectedKeys:e.selectedKeys},on:{click:e.onClick}},e._l(e.data,(function(t){return s("ta-menu-item",{key:t.resourceId},[s("ta-icon",{attrs:{type:"appstore"}}),e._v(" "+e._s(t.name)+" ")],1)})),1)],1)},a=[],r={name:"adminLeftMenu",props:{title:{type:String,required:!0},data:{type:Array,required:!0},selectedKeys:{type:Array}},data:function(){return{}},methods:{onClick:function(e){var t=e.item,s=e.key,n=e.keyPath;this.$emit("click",{item:t,key:s,keyPath:n})}}},i=r,u=s(1001),c=(0,u.Z)(i,n,a,!1,null,"8bcd3b7a",null),o=c.exports},80320:function(e,t,s){s.r(t),s.d(t,{default:function(){return h}});var n=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"fit"},[s("ta-border-layout",{attrs:{layout:{header:"55px"},"center-cfg":{showBar:!0},"header-cfg":{showBorder:!1},showBorder:!1,"footer-cfg":{showBorder:!1}}},[s("template",{slot:"header"},[s("ta-breadcrumb",{staticStyle:{"line-height":"29px"},attrs:{separator:">"}},[s("ta-breadcrumb-item",[s("a",{on:{click:e.fnBackToHome}},[e._v("角色维度")])]),s("ta-breadcrumb-item",[e._v("自定义功能使用权限")])],1)],1),s("div",{attrs:{slot:"centerExtraContent"},slot:"centerExtraContent"},[s("ta-alert",{attrs:{message:"当前角色为："+this.$route.query.roleName,type:"info",showIcon:""}})],1),s("ta-tabs",{staticClass:"fit"},[s("ta-tab-pane",{attrs:{tab:"自定义功能使用权限"}},[s("ta-row",{staticClass:"fit"},[s("ta-col",{staticClass:"fit",staticStyle:{"border-right":"1px solid #eee"},attrs:{span:4}},[s("admin-left-menu",{attrs:{title:"自定义使用一级菜单",data:e.menuData,selectedKeys:e.menuSelectedKeys},on:{click:e.onSelectMenu}})],1),s("ta-col",{staticClass:"right-box fit",attrs:{span:18}},[s("div",{staticClass:"fit",staticStyle:{border:"1px solid #e8e8e8"}},[s("div",{staticClass:"divider"}),s("ta-row",{staticStyle:{height:"5%"}},[s("ta-col",{staticClass:"col-header",attrs:{span:19}},[e._v(" 可授权的自定义功能菜单 ")]),s("ta-col",{staticClass:"col-header",attrs:{span:5}},[e._v(" 有效期 ")])],1),s("ta-divider"),s("div",{staticClass:"authority-box"},[s("ta-input",{staticStyle:{width:"250px","margin-left":"10px"},attrs:{placeholder:"输入关键字进行过滤"},model:{value:e.filterText,callback:function(t){e.filterText=t},expression:"filterText"}}),s("ta-button",{staticClass:"button-save",attrs:{type:"primary"},on:{click:function(t){return e.fnSaveAdminObjectUsePermission("refresh")}}},[e._v("权限保存 ")]),s("ta-popover",{attrs:{placement:"bottom",trigger:"manual",value:e.batchPop}},[s("div",{staticClass:"pop-calendar"},[s("ta-calendar",{attrs:{fullscreen:!1},on:{select:e.onPanelChange}})],1),s("div",{staticStyle:{float:"right","margin-top":"10px"}},[s("ta-button",{attrs:{size:"small"},on:{click:function(t){e.batchPop=!1}}},[e._v("取消")]),s("ta-button",{attrs:{size:"small"},on:{click:function(t){return e.fnSaveAuthorityEffectiveTime(!0)}}},[e._v("设为永久")]),s("ta-button",{attrs:{type:"primary",size:"small"},on:{click:function(t){return e.fnSaveAuthorityEffectiveTime(!1)}}},[e._v("确定 ")])],1),s("ta-button",{staticStyle:{float:"right","margin-right":"10px"},attrs:{slot:"reference"},on:{click:function(t){e.batchPop=!0}},slot:"reference"},[e._v("批量设置有效期 ")])],1),s("ta-e-tree",{ref:"tree",staticStyle:{"min-height":"300px"},attrs:{data:e.adminUseTree,"show-checkbox":"","node-key":"resourceId","highlight-current":"",props:e.defaultProps,"default-checked-keys":e.defaultKeys,"filter-node-method":e.filterNode,"expand-on-click-node":!1,"default-expand-all":""},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.node,a=t.data;return s("span",{staticClass:"custom-tree-node"},[s("span",[e._v(e._s(n.label))]),a.checked?s("span",{staticStyle:{float:"right"}},[e._v(" "+e._s(a.effectTime&&null!=a.effectTime?e.moment(a.effectTime).format("YYYY-MM-DD"):"永久")+" "),s("ta-popover",{attrs:{placement:"bottom",trigger:"manual",value:a.resourceId==e.indexClick}},[s("div",{staticClass:"pop-calendar"},[s("ta-calendar",{attrs:{fullscreen:!1},on:{select:e.onPanelChange}})],1),s("div",{staticStyle:{float:"right","margin-top":"10px"}},[s("ta-button",{attrs:{size:"small"},on:{click:function(t){e.indexClick=null}}},[e._v("取消")]),s("ta-button",{attrs:{size:"small"},on:{click:function(t){return e.onCellChange(n,a,!0)}}},[e._v("设为永久")]),s("ta-button",{attrs:{type:"primary",size:"small"},on:{click:function(t){return e.onCellChange(n,a,!1)}}},[e._v("确定 ")])],1),s("ta-icon",{staticClass:"icon-style",attrs:{slot:"reference",type:"edit"},on:{click:function(t){e.indexClick=a.resourceId}},slot:"reference"})],1)],1):e._e()])}}])})],1)],1)])],1)],1),s("template",{slot:"tabBarExtraContent"},[s("ta-button",{on:{click:e.fnBackToHome}},[s("ta-icon",{attrs:{type:"rollback"}}),e._v("返回")],1)],1)],2)],2)],1)},a=[],r=s(89584),i=s(16158),u=s(82947),c=s(36797),o=s.n(c),l={name:"adminObjectUseAuthority",components:{AdminLeftMenu:u.Z},data:function(){return{clickIndex:0,menuData:[],adminUseTree:[],defaultKeys:[],defaultProps:{children:"children",label:"name",id:"resourceId"},resourceItem:{},row:{col:{span:12}},item:{},roleId:"",menuSelectedKeys:[],effectiveTime:"",indexClick:null,filterText:"",batchPop:!1}},watch:{filterText:function(e){this.$refs.tree.filter(e)}},activated:function(){this.item=this.$route.query,this.item.roleId?(this.roleId=this.item.roleId,this.menuData=[],this.adminUseTree=[],this.defaultKeys=[],this.batchPop=!1,this.indexClick=null,this.fnLoadDefault()):this.fnBackToHome()},methods:{moment:o(),onPanelChange:function(e,t){this.effectiveTime=e.format("YYYY-MM-DD")},onCellChange:function(e,t,s){var n=this,a={roleId:this.roleId,resourceId:t.resourceId,effectTime:s?null:this.effectiveTime};i.Z.updateAdminObjectUsePermissionEffectiveTime(a,(function(){n.$message.success("更新有效期成功"),n.fnQueryObjectUsePermissionByResourceId(n.resourceItem),n.indexClick=null,n.batchPop=!1}))},fnSaveAuthorityEffectiveTime:function(e){var t=this,s=this.$refs.tree.getCheckedNodes(!1,!0).map((function(e){return e.resourceId})),n={roleId:this.roleId,resourceIds:s.join(","),effectTime:e?null:this.effectiveTime};i.Z.updateBatchAdminObjectUsePermissionEffectiveTime(n,(function(e){t.$message.success("批量设置有效期成功"),t.fnQueryObjectUsePermissionByResourceId(t.resourceItem),t.indexClick=null,t.batchPop=!1}))},filterNode:function(e,t){return!e||-1!==t.name.indexOf(e)},onSelectMenu:function(e){var t=this,s=(e.item,e.key),n=e.keyPath;if(this.resourceItem.resouceId!==s){var a=[],i=this.$refs.tree;a=[].concat((0,r.Z)(a),(0,r.Z)(i.getCheckedKeys(!0)));var u=(0,r.Z)(this.menuData),c=u.filter((function(e){return s===e.resourceId}))[0];this.defaultKeys.sort().toString()!==a.sort().toString()?this.$confirm({title:"提示",content:"当前自定义使用权限修改未保存，是否保存?",cancelText:"取消",okText:"确认",onOk:function(){t.fnSaveAdminObjectUsePermission(),t.menuSelectedKeys=n,t.fnQueryObjectUsePermissionByResourceId(c)},onCancel:function(){t.menuSelectedKeys=n,t.fnQueryObjectUsePermissionByResourceId(c)}}):(this.menuSelectedKeys=n,this.fnQueryObjectUsePermissionByResourceId(c))}},fnSaveAdminObjectUsePermission:function(e){var t=this,s=this.$refs.tree.getCheckedNodes(!1,!0).map((function(e){return e.resourceId})),n={roleId:this.roleId,resourceIds:s.join(","),parentResourceId:this.resourceItem.resourceId};i.Z.saveAdminObjectUsePermission(n,(function(s){t.$message.success("更新数据成功"),"refresh"==e&&t.fnQueryObjectUsePermissionByResourceId(t.resourceItem)}))},fnLoadDefault:function(){var e=this,t={roleId:this.roleId};i.Z.queryObjectUseSysPermission(t,(function(t){e.menuData=t.data.customResourceList,e.$nextTick((function(){e.menuData&&e.menuData.length>0&&(e.menuSelectedKeys=[e.menuData[0].resourceId],e.fnQueryObjectUsePermissionByResourceId(e.menuData[0]))}))}))},fnQueryObjectUsePermissionByResourceId:function(e){var t=this;this.resourceItem=e;var s={roleId:this.roleId,resourceId:e.resourceId};i.Z.queryObjectUsePermissionByResourceId(s,(function(e){t.adminUseTree=e.data.customResourceTree;var s=e.data.usePermissionList.map((function(e){return e.resourceId}));t.defaultKeys=[],t.$nextTick((function(){s.forEach((function(e){var s=t.$refs.tree.getNode(e);s&&s.isLeaf&&t.defaultKeys.push(s.data.resourceId)})),t.$refs.tree.setCheckedKeys(t.defaultKeys,!0)}))}))},fnBackToHome:function(){this.$router.push({name:"adminRoleManagement"})}}},d=l,f=s(1001),m=(0,f.Z)(d,n,a,!1,null,"31972488",null),h=m.exports},16158:function(e,t){var s="/org/authority/adminAuthorityManagementRestService/";t["Z"]={queryUsersByRoleId:function(e,t){Base.submit(null,{url:s+"queryUsersByRoleId",data:e},{successCallback:function(e){return t(e)}})},queryUsersByRoleIdNoPage:function(e,t){Base.submit(null,{url:s+"queryUsersByRoleIdNoPage",data:e},{successCallback:function(e){return t(e)}})},addBatchAdminUser:function(e,t){Base.submit(null,{url:s+"addBatchRoleUsers",data:e},{successCallback:function(e){return t(e)}})},deleteBatchAdmin:function(e,t){Base.submit(null,{url:s+"deleteBatchRole",data:e},{successCallback:function(e){return t(e)}})},queryAllTaOrg:function(e,t){Base.submit(null,{url:s+"queryCurrentAdminRoleWrapOrgTree",data:e},{successCallback:function(e){return t(e)}})},deleteRoleUserByKey:function(e,t){Base.submit(null,{url:s+"deleteRoleUserByKey",data:e},{successCallback:function(e){return t(e)}})},deleteBatchRoleUsers:function(e,t){Base.submit(null,{url:s+"deleteBatchRoleUsers",data:e},{successCallback:function(e){return t(e)}})},queryBatchUserByOrgId:function(e,t){Base.submit(null,{url:s+"queryUsersNoWraperByRoleId",data:e},{successCallback:function(e){return t(e)}})},queryBatchUserByOrgIdNoPage:function(e,t){Base.submit(null,{url:s+"queryUsersNoWraperByRoleIdNoPage",data:e},{successCallback:function(e){return t(e)}})},queryOrgPermission:function(e,t){Base.submit(null,{url:s+"selectOrgScope",data:e},{successCallback:function(e){return t(e)}})},removeOrgPermission:function(e,t){Base.submit(null,{url:s+"removeOrgScope",data:e},{successCallback:function(e){return t(e)}})},selectPermissionOrgScope:function(e,t){Base.submit(null,{url:s+"selectPermissionOrgScope",data:e},{successCallback:function(e){return t(e)}})},addOrgPermission:function(e,t){Base.submit(null,{url:s+"saveOrgScope",data:e},{successCallback:function(e){return t(e)}})},queryBatchPermissionTreeData:function(e,t){Base.submit(null,{url:s+"queryBatchPermissionTreeData",data:e},{successCallback:function(e){return t(e)}})},batchChangeAdminPermission:function(e,t){Base.submit(null,{url:s+"batchChangeAdminPermission",data:e},{successCallback:function(e){return t(e)}})},addAdminRole:function(e,t){Base.submit(null,{url:s+"addAdminRole",data:e},{successCallback:function(e){return t(e)}})},updateAdmin:function(e,t){Base.submit(null,{url:s+"updateAdmin",data:e},{successCallback:function(e){return t(e)}})},updateBatchAdminStatus:function(e,t){Base.submit(null,{url:s+"updateRoleEffectiveByRoleId",data:e},{successCallback:function(e){return t(e)}})},queryUseSysPermission:function(e,t){Base.submit(null,{url:s+"queryRootResource",data:e},{successCallback:function(e){return t(e)}})},queryUsePermissionByResourceId:function(e,t){Base.submit(null,{url:s+"queryChildResourceByRoleId",data:e},{successCallback:function(e){return t(e)}})},saveAdminUsePermission:function(e,t){Base.submit(null,{url:s+"changeResourceUsePermission",data:e},{successCallback:function(e){return t(e)}})},updateAdminUsePermissionEffectiveTime:function(e,t){Base.submit(null,{url:s+"updateUsePermissionEffectiveTime",data:e},{successCallback:function(e){return t(e)}})},saveBatchAdminUsePermissionEffectiveTime:function(e,t){Base.submit(null,{url:s+"updateBatchUsePermissionEffectiveTime",data:e},{successCallback:function(e){return t(e)}})},queryObjectUseSysPermission:function(e,t){Base.submit(null,{url:s+"queryRootCustomResource",data:e},{successCallback:function(e){return t(e)}})},queryObjectUsePermissionByResourceId:function(e,t){Base.submit(null,{url:s+"queryChildCustomResourceByRoleId",data:e},{successCallback:function(e){return t(e)}})},saveAdminObjectUsePermission:function(e,t){Base.submit(null,{url:s+"changeCustomResourceUsePermission",data:e},{successCallback:function(e){return t(e)}})},updateAdminObjectUsePermissionEffectiveTime:function(e,t){Base.submit(null,{url:s+"updateCustomResourceUsePermissionEffectiveTime",data:e},{successCallback:function(e){return t(e)}})},updateBatchAdminObjectUsePermissionEffectiveTime:function(e,t){Base.submit(null,{url:s+"updateBatchCustomResourceUsePermissionEffectiveTime",data:e},{successCallback:function(e){return t(e)}})},queryGrantSysPermission:function(e,t){Base.submit(null,{url:s+"queryRootResourceAuthority",data:e},{successCallback:function(e){return t(e)}})},queryGrantPermissionByResourceId:function(e,t){Base.submit(null,{url:s+"queryChildResourceAuthorityByRoleId",data:e},{successCallback:function(e){return t(e)}})},saveAdminGrantPermission:function(e,t){Base.submit(null,{url:s+"changeResourceAuthority",data:e},{successCallback:function(e){return t(e)}})},queryObjectGrantSysPermission:function(e,t){Base.submit(null,{url:s+"queryRootCustomResourceAuthority",data:e},{successCallback:function(e){return t(e)}})},queryObjectGrantPermissionByResourceId:function(e,t){Base.submit(null,{url:s+"queryChildCustomResourceAuthorityByRoleId",data:e},{successCallback:function(e){return t(e)}})},saveAdminObjectGrantPermission:function(e,t){Base.submit(null,{url:s+"changeCustomResourceAuthority",data:e},{successCallback:function(e){return t(e)}})},addBatchUserRole:function(e,t){Base.submit(null,{url:s+"addBatchUserRole",data:e},{successCallback:function(e){return t(e)}})},deleteBatchUserRole:function(e,t){Base.submit(null,{url:s+"deleteBatchUserRole",data:e},{successCallback:function(e){return t(e)}})},queryChildCustomResourceAsync:function(e,t){Base.submit(null,{url:s+"queryChildCustomResourceAsync",data:e},{successCallback:function(e){return t(e)}})},addAllRoleUsersByOrgId:function(e,t){Base.submit(null,{url:s+"addAllRoleUsersByOrgId",data:e},{successCallback:function(e){return t(e)}})}}}}]);
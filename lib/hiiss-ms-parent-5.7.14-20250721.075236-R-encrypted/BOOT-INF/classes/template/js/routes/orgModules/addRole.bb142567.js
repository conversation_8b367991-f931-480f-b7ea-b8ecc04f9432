"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[940],{23193:function(e,t,r){r.r(t),r.d(t,{default:function(){return m}});var a=function(){var e=this,t=this,r=t.$createElement,a=t._self._c||r;return a("ta-form",{attrs:{autoFormCreate:function(t){e.form=t}}},[a("ta-form-item",{attrs:{label:"角色名称",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"roleName",fieldDecoratorOptions:{rules:[{required:!0,message:"角色名称不能为空"}]}}},[a("ta-input",{attrs:{placeholder:"请输入角色名称"}})],1),a("ta-form-item",{attrs:{label:"所属组织",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"orgId",fieldDecoratorOptions:"add"===t.type?{rules:[{required:!0,message:"请选择所属组织"}]}:null}},[a("ta-cascader",{attrs:{url:t.casCaderOrgUrl,treeId:"orgVos",options:t.options,changeOnSelect:!0,fieldNames:{label:"orgName",value:"orgId",children:"children"},placeholder:t.orgPlaceholder},on:{"update:options":function(e){t.options=e}}})],1),a("ta-form-item",{attrs:{label:"有效期",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"effectiveTime"}},[a("ta-date-picker",{staticStyle:{width:"100%"},attrs:{format:"YYYY-MM-DD",allowClear:"",placeholder:"请选择有效时间",disabledDate:t.disabledDate}})],1),a("ta-form-item",{attrs:{label:"有效标识",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"effective"}},[a("ta-switch",{attrs:{checkedChildren:"启用",unCheckedChildren:"禁用"},model:{value:t.effective,callback:function(e){t.effective=e},expression:"effective"}})],1),a("ta-form-item",{attrs:{label:"角色描述",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"roleDesc"}},[a("ta-textarea",{attrs:{rows:4,placeholder:"角色描述"}})],1)],1)},l=[],o=r(36797),n=r.n(o),u=r(61109),s={labelCol:{span:6},wrapperCol:{span:18}},i={name:"addRole",props:["role"],data:function(){return{casCaderOrgUrl:"org/authority/examinerAuthorityRestService/queryOrgTreeByAsync",options:[],formItemLayout:s,orgOptions:[],effective:!0,roleId:"",type:"",orgId:"",orgPlaceholder:"请选择组织",effectiveTime:""}},created:function(){this.roleId=this.role.roleId?this.role.roleId:null,this.orgId=this.role.orgId?this.role.orgId:null,this.type=this.role.type},mounted:function(){this.fnInitForm()},methods:{moment:n(),fnResetForm:function(){this.fnInitForm()},fnAddRoleInfo:function(){var e=this;this.form.validateFields((function(t,r){if(!t){var a=e,l=a.effective?"1":"0",o=a.form.getFieldsValue();o.effective=l;var n=a.form.getFieldValue("orgId"),s=n&&0!==n.length?n[n.length-1]:null;"edit"!==a.type||s||(s=a.orgId),o.orgId=s;var i=a.form.getFieldValue("effectiveTime");o.effectiveTime=i?i.format("YYYY-MM-DD")+" 23:59:59":null;var c=e.type;"add"===c?u.Z.addExaminer(o,(function(e){a.$message.success("新增角色成功"),a.$emit("closeRoleDrawer")})):(o.roleType=a.role.roleType,o.roleId=a.roleId,u.Z.updateExaminer(o,(function(e){a.$message.success("修改角色成功"),a.$emit("closeRoleDrawer",o)})))}}))},fnInitForm:function(){var e=!this.role.effective||"1"===this.role.effective,t=this.type;if("edit"===t){this.orgPlaceholder="add"===t?"请选择组织路径":this.role.namePath;var r=this.role.effectiveTime;r&&this.form.setFieldsValue({effectiveTime:n()(new Date(r),"YYYY/MM/DD HH:mm:ss")}),this.form.setFieldsValue({roleName:this.role.roleName,roleDesc:this.role.roleDesc,orgId:[]})}this.effective=e},disabledDate:function(e){return e&&e<n()().endOf("day")},filter:function(e,t){return t.some((function(t){return t.label.toLowerCase().indexOf(e.toLowerCase())>-1}))},onChange:function(e,t){t&&"0"===t[t.length-1].isAuthority&&(this.$message.error("没有权限在该组织下新增角色"),this.form.setFieldsValue({orgId:[]}))}}},c=i,d=r(1001),f=(0,d.Z)(c,a,l,!1,null,null,null),m=f.exports},61109:function(e,t){var r="/org/authority/examinerAuthorityRestService/";t["Z"]={queryUserNoWrapperByRoleId:function(e,t){var a=r+"queryUserNoWrapperByRoleId";Base.submit(null,{url:a,data:e},{successCallback:function(e){return t(e)}})},addExaminer:function(e,t){var a=r+"addExaminer";Base.submit(null,{url:a,data:e},{successCallback:function(e){return t(e)}})},updateExaminer:function(e,t){var a=r+"updateExaminer";Base.submit(null,{url:a,data:e},{successCallback:function(e){return t(e)}})},unableExaminer:function(e,t){var a=r+"unableExaminer";Base.submit(null,{url:a,data:e},{successCallback:function(e){return t(e)}})},enableExaminer:function(e,t){var a=r+"enableExaminer";Base.submit(null,{url:a,data:e},{successCallback:function(e){return t(e)}})},deleteBatchExaminer:function(e,t){var a=r+"deleteBatchExaminer";Base.submit(null,{url:a,data:e},{successCallback:function(e){return t(e)}})},queryOrgAuth:function(e,t){var a=r+"queryOrgAuth";Base.submit(null,{url:a,data:e},{successCallback:function(e){return t(e)}})},queryOrgAuthTreeByAsync:function(e,t){var a=r+"queryOrgAuthTreeByAsync";Base.submit(null,{url:a,data:e},{successCallback:function(e){return t(e)}})},queryOrgTreeByAsync:function(e,t){var a=r+"queryOrgTreeByAsync";Base.submit(null,{url:a,data:e},{successCallback:function(e){return t(e)}})},removeOrgAuth:function(e,t){var a=r+"removeOrgAuth";Base.submit(null,{url:a,data:e},{successCallback:function(e){return t(e)}})},addOrgAuth:function(e,t){var a=r+"addOrgAuth";Base.submit(null,{url:a,data:e},{successCallback:function(e){return t(e)}})},addBatchExaminerUser:function(e,t){var a=r+"addBatchRoleUser";Base.submit(null,{url:a,data:e},{successCallback:function(e){return t(e)}})},deleteBatchRoleUser:function(e,t){var a=r+"deleteBatchRoleUser";Base.submit(null,{url:a,data:e},{successCallback:function(e){return t(e)}})},deleteBatchUserRole:function(e,t){var a=r+"deleteBatchUserRole";Base.submit(null,{url:a,data:e},{successCallback:function(e){return t(e)}})},addBatchUserRole:function(e,t){var a=r+"addBatchUserRole";Base.submit(null,{url:a,data:e},{successCallback:function(e){return t(e)}})}}}}]);
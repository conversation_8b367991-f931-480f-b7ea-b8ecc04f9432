"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[3371,940],{23193:function(e,t,r){r.r(t),r.d(t,{default:function(){return h}});var a=function(){var e=this,t=this,r=t.$createElement,a=t._self._c||r;return a("ta-form",{attrs:{autoFormCreate:function(t){e.form=t}}},[a("ta-form-item",{attrs:{label:"角色名称",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"roleName",fieldDecoratorOptions:{rules:[{required:!0,message:"角色名称不能为空"}]}}},[a("ta-input",{attrs:{placeholder:"请输入角色名称"}})],1),a("ta-form-item",{attrs:{label:"所属组织",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"orgId",fieldDecoratorOptions:"add"===t.type?{rules:[{required:!0,message:"请选择所属组织"}]}:null}},[a("ta-cascader",{attrs:{url:t.casCaderOrgUrl,treeId:"orgVos",options:t.options,changeOnSelect:!0,fieldNames:{label:"orgName",value:"orgId",children:"children"},placeholder:t.orgPlaceholder},on:{"update:options":function(e){t.options=e}}})],1),a("ta-form-item",{attrs:{label:"有效期",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"effectiveTime"}},[a("ta-date-picker",{staticStyle:{width:"100%"},attrs:{format:"YYYY-MM-DD",allowClear:"",placeholder:"请选择有效时间",disabledDate:t.disabledDate}})],1),a("ta-form-item",{attrs:{label:"有效标识",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"effective"}},[a("ta-switch",{attrs:{checkedChildren:"启用",unCheckedChildren:"禁用"},model:{value:t.effective,callback:function(e){t.effective=e},expression:"effective"}})],1),a("ta-form-item",{attrs:{label:"角色描述",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"roleDesc"}},[a("ta-textarea",{attrs:{rows:4,placeholder:"角色描述"}})],1)],1)},o=[],l=r(36797),n=r.n(l),i=r(61109),s={labelCol:{span:6},wrapperCol:{span:18}},c={name:"addRole",props:["role"],data:function(){return{casCaderOrgUrl:"org/authority/examinerAuthorityRestService/queryOrgTreeByAsync",options:[],formItemLayout:s,orgOptions:[],effective:!0,roleId:"",type:"",orgId:"",orgPlaceholder:"请选择组织",effectiveTime:""}},created:function(){this.roleId=this.role.roleId?this.role.roleId:null,this.orgId=this.role.orgId?this.role.orgId:null,this.type=this.role.type},mounted:function(){this.fnInitForm()},methods:{moment:n(),fnResetForm:function(){this.fnInitForm()},fnAddRoleInfo:function(){var e=this;this.form.validateFields((function(t,r){if(!t){var a=e,o=a.effective?"1":"0",l=a.form.getFieldsValue();l.effective=o;var n=a.form.getFieldValue("orgId"),s=n&&0!==n.length?n[n.length-1]:null;"edit"!==a.type||s||(s=a.orgId),l.orgId=s;var c=a.form.getFieldValue("effectiveTime");l.effectiveTime=c?c.format("YYYY-MM-DD")+" 23:59:59":null;var u=e.type;"add"===u?i.Z.addExaminer(l,(function(e){a.$message.success("新增角色成功"),a.$emit("closeRoleDrawer")})):(l.roleType=a.role.roleType,l.roleId=a.roleId,i.Z.updateExaminer(l,(function(e){a.$message.success("修改角色成功"),a.$emit("closeRoleDrawer",l)})))}}))},fnInitForm:function(){var e=!this.role.effective||"1"===this.role.effective,t=this.type;if("edit"===t){this.orgPlaceholder="add"===t?"请选择组织路径":this.role.namePath;var r=this.role.effectiveTime;r&&this.form.setFieldsValue({effectiveTime:n()(new Date(r),"YYYY/MM/DD HH:mm:ss")}),this.form.setFieldsValue({roleName:this.role.roleName,roleDesc:this.role.roleDesc,orgId:[]})}this.effective=e},disabledDate:function(e){return e&&e<n()().endOf("day")},filter:function(e,t){return t.some((function(t){return t.label.toLowerCase().indexOf(e.toLowerCase())>-1}))},onChange:function(e,t){t&&"0"===t[t.length-1].isAuthority&&(this.$message.error("没有权限在该组织下新增角色"),this.form.setFieldsValue({orgId:[]}))}}},u=c,d=r(1001),f=(0,d.Z)(u,a,o,!1,null,null,null),h=f.exports},14913:function(e,t,r){r.r(t),r.d(t,{default:function(){return m}});var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"fit",attrs:{id:"roleManagement"}},[r("ta-border-layout",{attrs:{layout:{header:"70px",footer:"70px"},centerCfg:{showBar:!0,toolBarStyle:{height:"50px",lineHeight:"48px"}},"header-cfg":{showBorder:!1},showBorder:!1,"footer-cfg":{showBorder:!1}}},[r("div",{staticClass:"center-box",attrs:{slot:"header"},slot:"header"},[r("ta-input-search",{staticClass:"search-box",attrs:{placeholder:"请输入角色名称"},on:{search:e.onSearchRole},model:{value:e.searchInfo,callback:function(t){e.searchInfo=t},expression:"searchInfo"}},[r("ta-button",{attrs:{slot:"enterButton",type:"primary"},slot:"enterButton"},[e._v("搜索")])],1)],1),r("div",{attrs:{slot:"centerExtraContent"},slot:"centerExtraContent"},[r("ta-cascader",{staticStyle:{width:"250px","margin-right":"10px"},attrs:{url:e.casCaderOrgUrl,treeId:"orgVos",expandTrigger:"hover",options:e.options,changeOnSelect:!0,fieldNames:{label:"orgName",value:"orgId",children:"children"},placeholder:"请选择组织机构",loadedDataCallBack:e.fnLoadedOrgCallBack},on:{"update:options":function(t){e.options=t},change:e.fnQueryRole},model:{value:e.orgId,callback:function(t){e.orgId=t},expression:"orgId"}}),r("ta-checkbox",{attrs:{checked:e.includeSub},on:{change:e.onChangeIsSub}},[e._v("包含子组织")]),r("ta-tag-select",{attrs:{title:"状态",data:e.CollectionData("STATE")},on:{change:e.filterClick},model:{value:e.selectFilter,callback:function(t){e.selectFilter=t},expression:"selectFilter"}}),r("div",{staticStyle:{float:"right"}},[r("ta-button",{attrs:{type:"primary"},on:{click:e.fnAddRole}},[e._v("新增角色")]),r("ta-dropdown",{attrs:{trigger:e.clickTrigger}},[r("ta-menu",{attrs:{slot:"overlay"},slot:"overlay"},[r("ta-menu-item",{attrs:{disabled:!e.selectedRowKeys.length}},[r("ta-popconfirm",{attrs:{title:"确认启用所选审核角色?",cancelText:"取消",okText:"确认"},on:{confirm:function(t){return e.fnBatchPickRole(!1)}}},[r("ta-icon",{attrs:{type:"check-circle"}}),r("span",{staticClass:"mg-l12"},[e._v("启用")])],1)],1),r("ta-menu-divider"),r("ta-menu-item",{attrs:{disabled:!e.selectedRowKeys.length}},[r("ta-popconfirm",{attrs:{title:"确认禁用所选审核角色?",cancelText:"取消",okText:"确认"},on:{confirm:function(t){return e.fnBatchBanRole(!1)}}},[r("ta-icon",{attrs:{type:"stop"}}),r("span",{staticClass:"mg-l12"},[e._v("禁用")])],1)],1),r("ta-menu-divider"),r("ta-menu-item",{attrs:{disabled:!e.selectedRowKeys.length},on:{click:function(t){e.deleteVisible=!0}}},[r("ta-icon",{attrs:{type:"close-circle"}}),e._v(" 删除 ")],1)],1),r("ta-button",[e._v("批量操作 "),r("ta-icon",{attrs:{type:"down"}})],1)],1)],1)],1),r("ta-table",{ref:"roleRef",attrs:{columns:e.roleColumns,dataSource:e.roleData,rowSelection:{selectedRowKeys:e.selectedRowKeys,onChange:e.fnOnChange},autoExpandParent:"",pagination:!1},scopedSlots:e._u([{key:"customOrgName",fn:function(t,a){return[r("span",{class:{invalidStyle:"0"==a.effective}},[e._v(e._s(t))])]}},{key:"namePath",fn:function(t){return r("span",{},[e._v(e._s(e.getLastName(t)))])}},{key:"effecttime",fn:function(t,r){return[e._v(" "+e._s(null==r.effectiveTime?"永久":e.moment(r.effectiveTime).format("YYYY-MM-DD"))+" ")]}},{key:"effective",fn:function(t){return r("span",{},[e._v(e._s(e.CollectionLabel("STATE",t)))])}},{key:"action",fn:function(t,a){return r("span",{},[r("ta-table-operate",{attrs:{operateMenu:e.operateMenu}})],1)}},{key:"roleMg",fn:function(t,a){return r("span",{},["1"==a.effective?r("span",[r("router-link",{attrs:{to:{path:"roleUserMg",query:{roleName:a.roleName,roleId:a.roleId}}}},[e._v(" 人员管理 ")])],1):r("span",{attrs:{title:"禁用的审核角色不能进行人员管理"}},[r("span",{staticClass:"invalidStyle"},[e._v("人员管理")])]),r("ta-divider",{attrs:{type:"vertical"}}),"1"==a.effective?r("span",[r("router-link",{attrs:{to:{path:"roleOrgAuthority",query:{roleName:a.roleName,roleId:a.roleId}}}},[e._v("组织范围权限")])],1):r("span",{attrs:{title:"禁用的审核角色不能进行组织范围权限操作"}},[r("span",{staticClass:"ant-dropdown-link invalidStyle"},[e._v(" 组织范围权限 ")])])],1)}}])}),r("div",{attrs:{slot:"footer"},slot:"footer"},[r("ta-pagination",{ref:"rolePager",staticStyle:{float:"right","margin-top":"10px"},attrs:{showSizeChanger:"",showQuickJumper:"",dataSource:e.roleData,defaultPageSize:10,params:e.rolePageParams,url:e.roleTableUrl},on:{"update:dataSource":function(t){e.roleData=t},"update:data-source":function(t){e.roleData=t}}})],1),r("ta-modal",{attrs:{visible:e.batch.visible,centered:!0,destroyOnClose:!0,width:"800px",bodyStyle:{height:"400px"}},on:{cancel:function(t){e.batch.visible=!1}}},[r("template",{slot:"title"},[r("div",{staticStyle:{"text-align":"center"}},[e._v(" "+e._s(e.batch.title)+" ")])]),r("template",{slot:"footer"},[r("div",{staticStyle:{"text-align":"center"}},[r("ta-button",{on:{click:function(t){e.batch.visible=!1}}},[e._v("取消")]),r("ta-button",{attrs:{type:"primary"},on:{click:e.fnSaveModalAuthority}},[e._v("保存")])],1)]),r("ta-row",{staticStyle:{width:"100%"},attrs:{gutter:10}},[r("ta-col",{attrs:{span:e.row.col.span}},[r("span",{staticClass:"title"},[e._v("使用权限")]),r("div",{staticClass:"filter"},[r("ta-input",{attrs:{placeholder:"请输入资源名称"},model:{value:e.roleLeftFilterText,callback:function(t){e.roleLeftFilterText=t},expression:"roleLeftFilterText"}})],1),r("div",{staticClass:"modalTreeStyle"},[r("ta-e-tree",{ref:"tree",attrs:{data:e.leftTreeData,props:e.modalDefaultProps,"filter-node-method":e.filterNode,"default-checked-keys":e.leftDefaultKeys,"show-checkbox":"","highlight-current":"","node-key":"resourceId"}})],1)]),r("ta-col",{attrs:{span:e.row.col.span}},[r("span",{staticClass:"title"},[e._v("对象使用权限")]),r("div",{staticClass:"filter"},[r("ta-input",{attrs:{placeholder:"请输入资源名称"},model:{value:e.roleRightFilterText,callback:function(t){e.roleRightFilterText=t},expression:"roleRightFilterText"}})],1),r("div",{staticClass:"modalTreeStyle"},[r("ta-e-tree",{ref:"rtree",attrs:{data:e.rightTreeData,props:e.modalDefaultProps,"filter-node-method":e.filterNode,"default-checked-keys":e.rightDefaultKeys,"show-checkbox":"","highlight-current":"","node-key":"resourceId"}})],1)])],1)],2),r("ta-drawer",{attrs:{destroyOnClose:"",width:"500",title:e.roleDrawerTitle,placement:"right",closable:"",visible:e.roleDrawerVisible,footerHeight:""},on:{close:e.fnCloseRoleDrawer}},[r("div",{attrs:{slot:"footer"},slot:"footer"},[r("ta-button-group",[r("ta-button",{on:{click:e.fnResetForm}},[e._v("重置")]),r("ta-button",{attrs:{type:"primary"},on:{click:e.fnAddRoleInfo}},[e._v("保存")])],1)],1),r("add-role",{ref:"addRole",attrs:{role:e.roleItem},on:{closeRoleDrawer:e.fnCloseRoleDrawer}})],1)],1),r("ta-careful-delete",{attrs:{visible:e.deleteVisible,title:"审核角色删除",description:"选中审核角色"},on:{close:function(t){e.deleteVisible=!1},delete:e.fnDeleteBatchRole}})],1)},o=[],l=r(61109),n=r(36797),i=r.n(n),s=r(23193),c=[{title:"角色名称",width:"15%",dataIndex:"roleName",overflowTooltip:!0,scopedSlots:{customRender:"customOrgName"}},{title:"组织路径",width:"10%",dataIndex:"namePath",overflowTooltip:!0,scopedSlots:{customRender:"namePath"}},{title:"有效期",width:"10%",dataIndex:"effecttime",scopedSlots:{customRender:"effecttime"}},{title:"角色描述",width:"10%",dataIndex:"roleDesc",overflowTooltip:!0},{title:"状态",width:"10%",dataIndex:"effective",yesOrNoTag:!0,scopedSlots:{customRender:"effective"}},{title:"角色操作",dataIndex:"operation",width:"15%",align:"center",scopedSlots:{customRender:"action"}},{title:"管理",dataIndex:"roleMg",width:360,align:"center",scopedSlots:{customRender:"roleMg"}}],u={name:"roleRoleManagement",components:{AddRole:s["default"]},data:function(){var e=this;return{searchInfo:"",clickTrigger:["click"],selectFilter:[],casCaderOrgUrl:"org/authority/adminAuthorityManagementRestService/queryCurrentAdminRoleWrapOrgTree",roleTableUrl:"org/authority/examinerAuthorityRestService/queryExaminer",options:[],orgId:[],includeSub:!0,roleDrawerTitle:"",roleLeftFilterText:"",roleRightFilterText:"",leftTreeData:[],rightTreeData:[],leftDefaultKeys:[],rightDefaultKeys:[],modalDefaultProps:{label:"name"},roleName:"",roleColumns:c,operateMenu:[{name:"编辑",disabled:function(e){return"0"==e.effective},title:function(e){return"0"==e.effective?"禁用的审核角色不允许编辑":""},onClick:function(t){e.fnEditRole(t)}},{name:"更多",type:"more",moreMenuList:[{name:"启用",type:"confirm",confirmTitle:"确认启用该审核角色?",onOk:function(t){e.fnBatchPickRole(t)}},{name:"禁用",type:"confirm",confirmTitle:"确认禁用该审核角色?",onOk:function(t){e.fnBatchBanRole(t)}},{name:"删除",type:"confirm",confirmTitle:"确认删除该审核角色吗？",onOk:function(t){e.fnRoleRecordDelete(t)}}]}],roleData:[],selectedRows:[],selectedRowKeys:[],roleDrawerVisible:!1,roleItem:{},batch:{title:"",visible:!1,type:"grant"},arrayData:{},isDetailShow:!1,row:{col:{span:12}},deleteVisible:!1}},watch:{roleLeftFilterText:function(e){this.$refs.tree.filter(e)},roleRightFilterText:function(e){this.$refs.rtree.filter(e)}},methods:{fnOnChange:function(e,t){this.selectedRowKeys=e,this.selectedRows=t},moment:i(),onSearchRole:function(e){this.roleName=e,this.fnLoadDefaultRole()},filterClick:function(){this.fnLoadDefaultRole()},getLastName:function(e){return e&&-1!==e.indexOf("/")?e.slice(e.lastIndexOf("/")+1):e},fnAddRoleInfo:function(){this.$refs.addRole.fnAddRoleInfo()},fnResetForm:function(){this.$refs.addRole.fnResetForm()},fnCloseRoleDrawer:function(){this.roleDrawerVisible=!1,this.fnLoadDefaultRole()},fnToPath:function(e,t){this.$router.push({path:e,query:{roleName:t.roleName,roleId:t.roleId}})},fnSaveModalAuthority:function(){var e,t,r=this;"grant"==this.batch.type?(e=this.$refs.tree.getCheckedNodes(!1,!0).map((function(e){return e.resourceId})),t=this.$refs.rtree.getCheckedNodes(!1,!0).map((function(e){return e.resourceId}))):(e=this.$refs.tree.getCheckedKeys(),t=this.$refs.rtree.getCheckedKeys());var a={isAdd:"grant"==this.batch.type?"1":"0",roles:JSON.stringify(this.selectedRows),resourceIds:JSON.stringify(e),objectResourceIds:JSON.stringify(t)};l.Z.batchChangeRolePermission(a,(function(e){r.$message.success("操作成功"),r.batch.visible=!1}))},filterNode:function(e,t,r){return!e||-1!==t.name.indexOf(e)},rolePageParams:function(){var e=this.orgId,t=e?e[e.length-1]:this.options[0].orgId,r=this.selectFilter;return r=1==r.length?r.join(","):null,{roleName:this.roleName,effective:r,orgId:t,isSub:this.includeSub}},fnLoadDefaultRole:function(){this.$refs.rolePager.loadData()},onChangeIsSub:function(e){this.includeSub=e.target.checked,this.fnLoadDefaultRole()},fnLoadedOrgCallBack:function(e){this.fnLoadDefaultRole()},fnQueryRole:function(e){this.orgId=e,this.fnLoadDefaultRole()},fnLoadModalDefault:function(){var e=this;this.batch.visible=!0,l.Z.queryBatchPermissionTreeData(null,(function(t){e.leftTreeData=t.data.batchPermissionTree,e.rightTreeData=t.data.batchCustomPermissionTree}))},fnGrantAuthority:function(){0!==this.selectedRows.length?(this.batch.title="授予使用权限",this.batch.type="grant",this.fnLoadModalDefault()):this.$message.warning("请先选择数据")},fnRevokeAuthority:function(){0!==this.selectedRows.length?(this.batch.title="回收使用权限",this.batch.type="revoke",this.fnLoadModalDefault()):this.$message.warning("请先选择数据")},fnRoleRecordDelete:function(e){var t=this,r={roleIds:e.roleId};l.Z.deleteBatchExaminer(r,(function(e){t.$message.success("移除审核角色成功"),t.fnLoadDefaultRole()}))},fnEditRole:function(e){this.roleDrawerTitle="修改审核角色",this.roleDrawerVisible=!0,this.roleItem=e,this.roleItem.type="edit"},fnDeleteBatchRole:function(){var e=this,t=this.selectedRows.map((function(e){return e.roleId})),r={roleIds:t.join(",")};l.Z.deleteBatchExaminer(r,(function(t){e.$message.success("移除审核角色成功"),e.deleteVisible=!1,e.fnLoadDefaultRole()}))},fnAddRole:function(){this.roleDrawerTitle="新增审核角色",this.roleDrawerVisible=!0,this.roleItem={},this.roleItem.type="add"},fnBatchPickRole:function(e){var t,r=this;if(e){if("1"==e.effective)return void this.$message.warning("该记录已经启用，请勿重复操作！");t=[e.roleId]}else t=this.selectedRows.map((function(e){return e.roleId}));var a={roleIds:t.join(","),effective:"1"};l.Z.enableExaminer(a,(function(e){r.$message.success("更新数据成功"),r.fnQueryRole()}))},fnBatchBanRole:function(e){var t,r=this;if(e){if("0"==e.effective)return void this.$message.warning("该记录已经禁用，请勿重复操作！");t=[e.roleId]}else t=this.selectedRows.map((function(e){return e.roleId}));var a={roleIds:t.join(","),effective:"0"};l.Z.unableExaminer(a,(function(e){r.$message.success("更新数据成功"),r.fnQueryRole()}))}}},d=u,f=r(1001),h=(0,f.Z)(d,a,o,!1,null,"2aeab4c2",null),m=h.exports},61109:function(e,t){var r="/org/authority/examinerAuthorityRestService/";t["Z"]={queryUserNoWrapperByRoleId:function(e,t){var a=r+"queryUserNoWrapperByRoleId";Base.submit(null,{url:a,data:e},{successCallback:function(e){return t(e)}})},addExaminer:function(e,t){var a=r+"addExaminer";Base.submit(null,{url:a,data:e},{successCallback:function(e){return t(e)}})},updateExaminer:function(e,t){var a=r+"updateExaminer";Base.submit(null,{url:a,data:e},{successCallback:function(e){return t(e)}})},unableExaminer:function(e,t){var a=r+"unableExaminer";Base.submit(null,{url:a,data:e},{successCallback:function(e){return t(e)}})},enableExaminer:function(e,t){var a=r+"enableExaminer";Base.submit(null,{url:a,data:e},{successCallback:function(e){return t(e)}})},deleteBatchExaminer:function(e,t){var a=r+"deleteBatchExaminer";Base.submit(null,{url:a,data:e},{successCallback:function(e){return t(e)}})},queryOrgAuth:function(e,t){var a=r+"queryOrgAuth";Base.submit(null,{url:a,data:e},{successCallback:function(e){return t(e)}})},queryOrgAuthTreeByAsync:function(e,t){var a=r+"queryOrgAuthTreeByAsync";Base.submit(null,{url:a,data:e},{successCallback:function(e){return t(e)}})},queryOrgTreeByAsync:function(e,t){var a=r+"queryOrgTreeByAsync";Base.submit(null,{url:a,data:e},{successCallback:function(e){return t(e)}})},removeOrgAuth:function(e,t){var a=r+"removeOrgAuth";Base.submit(null,{url:a,data:e},{successCallback:function(e){return t(e)}})},addOrgAuth:function(e,t){var a=r+"addOrgAuth";Base.submit(null,{url:a,data:e},{successCallback:function(e){return t(e)}})},addBatchExaminerUser:function(e,t){var a=r+"addBatchRoleUser";Base.submit(null,{url:a,data:e},{successCallback:function(e){return t(e)}})},deleteBatchRoleUser:function(e,t){var a=r+"deleteBatchRoleUser";Base.submit(null,{url:a,data:e},{successCallback:function(e){return t(e)}})},deleteBatchUserRole:function(e,t){var a=r+"deleteBatchUserRole";Base.submit(null,{url:a,data:e},{successCallback:function(e){return t(e)}})},addBatchUserRole:function(e,t){var a=r+"addBatchUserRole";Base.submit(null,{url:a,data:e},{successCallback:function(e){return t(e)}})}}}}]);
"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[7081],{83241:function(e,t,s){s.r(t),s.d(t,{default:function(){return f}});var n=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"fit"},[s("ta-border-layout",{attrs:{layout:{header:"70px",footer:"70px"},"footer-cfg":{showBorder:!1}}},[s("div",{staticClass:"center-box",attrs:{slot:"header"},slot:"header"},[s("ta-breadcrumb",{staticStyle:{"line-height":"42px",float:"left"},attrs:{separator:">"}},[s("ta-breadcrumb-item",[s("a",{on:{click:e.fnBackToHome}},[e._v("角色维度")])]),s("ta-breadcrumb-item",[e._v("人员管理")])],1),s("div",{staticClass:"divider"}),s("ta-alert",{staticClass:"notice-box",attrs:{message:"当前角色为："+this.$route.query.roleName,type:"info","show-icon":""}}),s("ta-button",{staticStyle:{float:"right","margin-top":"8px"},on:{click:e.fnBackToHome}},[s("ta-icon",{attrs:{type:"rollback"}}),e._v(" 返回 ")],1)],1),s("ta-tabs",{staticClass:"fit"},[s("div",{attrs:{slot:"tabBarExtraContent"},slot:"tabBarExtraContent"},[s("ta-button-group",[s("ta-button",{on:{click:e.fnBatchDeleteUser}},[e._v(" 批量移除 ")]),s("ta-button",{attrs:{type:"primary"},on:{click:e.fnShowUserModal}},[e._v(" 分配人员 ")])],1)],1),s("ta-tab-pane",{attrs:{tab:e.tabTitle}},[s("ta-table",{ref:"userRef",staticStyle:{"padding-top":"10px"},attrs:{columns:e.userColumns,"data-source":e.userData,pagination:!1,"row-key":"orgId","row-selection":{selectedRowKeys:e.selectedRowKeys,onChange:e.fnOnChange}},scopedSlots:e._u([{key:"sex",fn:function(t){return[e._v(" "+e._s(e.CollectionLabel("SEX",t))+" ")]}},{key:"namePath",fn:function(t){return s("span",{},[e._v(e._s(e.getLastName(t)))])}},{key:"action",fn:function(t,n){return s("span",{},[s("ta-table-operate",{attrs:{"operate-menu":e.operateMenu}})],1)}}])})],1)],1),s("div",{attrs:{slot:"footer"},slot:"footer"},[s("ta-pagination",{ref:"adminUserPager",staticStyle:{float:"right","margin-top":"10px"},attrs:{"show-size-changer":"","show-quick-jumper":"","data-source":e.userData,"default-page-size":10,params:e.adminUserParams,url:e.userPageUrl},on:{"update:dataSource":function(t){e.userData=t},"update:data-source":function(t){e.userData=t}}})],1)],1),s("ta-user-select",{attrs:{id:"adminMg",title:"新增管理员角色",props:e.defaultProps,width:"800px","is-show":e.isShow,"user-tree-data":e.orgTreeData,load:e.loadOrgTreeNode,"user-list-data":e.userListData,"default-user-list":e.result,pagination:!0},on:{close:function(t){e.isShow=!1},queryUserList:e.fnQueryUserList,getUserListResult:e.fnGetUserListResult,checkAllMembers:e.checkAllMembers}})],1)},a=[],r=s(89584),u=s(16158),i=[{title:"姓名",dataIndex:"name",width:"15%"},{title:"登录号",dataIndex:"loginId",width:"15%"},{title:"所属组织",dataIndex:"namePath",scopedSlots:{customRender:"namePath"},width:"20%"},{title:"证件号",dataIndex:"idCardNo",width:"20%"},{title:"性别",dataIndex:"sex",width:"10%",scopedSlots:{customRender:"sex"}},{title:"操作",align:"center",width:"20%",dataIndex:"action",scopedSlots:{customRender:"action"}}],o={name:"adminUserMg",data:function(){var e=this;return{item:{},tabTitle:"管理员角色人员管理列表",searchInfo:"",userData:[],userColumns:i,operateMenu:[{name:"移除",type:"confirm",confirmTitle:"确定要移除该人员吗?",onOk:function(t){e.fnAdminUserDelete(t)}}],userPageUrl:"org/authority/adminAuthorityManagementRestService/queryUsersByRoleId",isShow:!1,orgTreeData:[],userListData:[],result:[],defaultProps:{treeNodeKey:"value",treeLabel:"label",treeChildren:"children",listKey:"userId",listTitle:"name",listSubTitle:"mobile",listDescription:"namePath"},roleId:"",selectedRows:[],selectedRowKeys:[]}},activated:function(){this.item=this.$route.query,this.item.roleId?(this.roleId=this.item.roleId,this.fnLoadUserData(),this.initTableColumn(this.$refs.userRef,["sex","action"])):this.fnBackToHome()},methods:{fnOnChange:function(e,t){this.selectedRowKeys=e,this.selectedRows=t},getLastName:function(e){return e&&-1!==e.indexOf("/")?e.slice(e.lastIndexOf("/")+1):null==e||""===e||void 0===e?"--":e},fnAdminUserDelete:function(e){var t=this,s={roleId:this.roleId,userId:e.userId};u.Z.deleteRoleUserByKey(s,(function(e){t.$message.success("移除人员成功"),t.fnLoadUserData()}))},initTableColumn:function(e,t){var s=(0,r.Z)(e.columns),n=s.map((function(e){if(t&&t.length>0)for(var s in t)if(e.dataIndex===t[s]||e.key===t[s])return e;return e.overflowTooltip=!0,e}));Object.assign(n,e.columns)},adminUserParams:function(){var e={};return e.roleId=this.item.roleId,this.searchInfo&&(e.userName=this.searchInfo),e},fnLoadUserData:function(){this.$refs.adminUserPager.loadData()},fnBackToHome:function(){this.$router.push({name:"adminRoleManagement"})},fnQueryUserList:function(e,t,s,n,a){var r=this,i={roleId:this.roleId,orgId:e,pageNumber:s,pageSize:10,includeChild:t?"1":"0"};n&&(i[n]=a),u.Z.queryBatchUserByOrgId(i,(function(e){r.userListData=e.data.pageBean.list}))},fnGetUserListResult:function(e,t){var s=this,n=this.result.filter((function(t){var s=[];return e.forEach((function(e){s.push(e.userId)})),!s.includes(t.userId)})),a=e.filter((function(e){var t=[];return s.result.forEach((function(e){t.push(e.userId)})),!t.includes(e.userId)})),r=[],i=[];a.length>0&&a.forEach((function(e,t){r.push(e.userId)})),n.length>0&&n.forEach((function(e,t){i.push(e.userId)})),new Promise((function(e,t){r.length?u.Z.addBatchAdminUser({roleId:s.roleId,userIds:r.join(",")},(function(t){s.$message.success("关联人员成功"),e()})):e()})).then((function(){return new Promise((function(e,t){i.length?u.Z.deleteBatchRoleUsers({roleId:s.roleId,userIds:i.join(",")},(function(t){s.$message.success("移除人员成功"),e()})):e()}))})).then((function(){t||(s.isShow=!1,s.fnLoadUserData())}))},fnBatchDeleteUser:function(){var e=this;0!==this.selectedRows.length?this.$confirm({title:"移除人员",content:"确认移除这些人员吗?",cancelText:"取消",okText:"确认",onOk:function(){var t=e.selectedRows.map((function(e){return e.userId})),s={userIds:t.join(","),roleId:e.roleId};u.Z.deleteBatchRoleUsers(s,(function(t){e.$message.success("移除成功"),e.fnLoadUserData()}))}}):this.$message.warning("请选择需要移除的人员")},fnShowUserModal:function(){var e=this;this.isShow=!0,this.result=[],this.userListData=[],u.Z.queryUsersByRoleIdNoPage({roleId:this.item.roleId},(function(t){e.result=t.data.list}))},loadOrgTreeNode:function(e,t){if(0===e.level&&u.Z.queryAllTaOrg(null,(function(e){var s=e.data.orgVos;if(s[0].children){var n=s[0].children.map((function(e){var t=e;e.childNum>0&&(t.children=[])}));s[0].children=n}return t(s)})),e.level>=1){var s=e.data.orgId,n=e.data.isLeaf,a={orgId:s};u.Z.queryAllTaOrg(a,(function(e){var s=e.data.orgVos;return n&&(s=s.map((function(e){var t=e;t.children=[]}))),t(s)}))}},checkAllMembers:function(e,t,s){u.Z.queryBatchUserByOrgIdNoPage({roleId:this.item.roleId,orgId:e,includeChild:t},(function(e){s(e.data.list)}))}}},l=o,c=s(1001),d=(0,c.Z)(l,n,a,!1,null,"b8f6014e",null),f=d.exports},16158:function(e,t){var s="/org/authority/adminAuthorityManagementRestService/";t["Z"]={queryUsersByRoleId:function(e,t){Base.submit(null,{url:s+"queryUsersByRoleId",data:e},{successCallback:function(e){return t(e)}})},queryUsersByRoleIdNoPage:function(e,t){Base.submit(null,{url:s+"queryUsersByRoleIdNoPage",data:e},{successCallback:function(e){return t(e)}})},addBatchAdminUser:function(e,t){Base.submit(null,{url:s+"addBatchRoleUsers",data:e},{successCallback:function(e){return t(e)}})},deleteBatchAdmin:function(e,t){Base.submit(null,{url:s+"deleteBatchRole",data:e},{successCallback:function(e){return t(e)}})},queryAllTaOrg:function(e,t){Base.submit(null,{url:s+"queryCurrentAdminRoleWrapOrgTree",data:e},{successCallback:function(e){return t(e)}})},deleteRoleUserByKey:function(e,t){Base.submit(null,{url:s+"deleteRoleUserByKey",data:e},{successCallback:function(e){return t(e)}})},deleteBatchRoleUsers:function(e,t){Base.submit(null,{url:s+"deleteBatchRoleUsers",data:e},{successCallback:function(e){return t(e)}})},queryBatchUserByOrgId:function(e,t){Base.submit(null,{url:s+"queryUsersNoWraperByRoleId",data:e},{successCallback:function(e){return t(e)}})},queryBatchUserByOrgIdNoPage:function(e,t){Base.submit(null,{url:s+"queryUsersNoWraperByRoleIdNoPage",data:e},{successCallback:function(e){return t(e)}})},queryOrgPermission:function(e,t){Base.submit(null,{url:s+"selectOrgScope",data:e},{successCallback:function(e){return t(e)}})},removeOrgPermission:function(e,t){Base.submit(null,{url:s+"removeOrgScope",data:e},{successCallback:function(e){return t(e)}})},selectPermissionOrgScope:function(e,t){Base.submit(null,{url:s+"selectPermissionOrgScope",data:e},{successCallback:function(e){return t(e)}})},addOrgPermission:function(e,t){Base.submit(null,{url:s+"saveOrgScope",data:e},{successCallback:function(e){return t(e)}})},queryBatchPermissionTreeData:function(e,t){Base.submit(null,{url:s+"queryBatchPermissionTreeData",data:e},{successCallback:function(e){return t(e)}})},batchChangeAdminPermission:function(e,t){Base.submit(null,{url:s+"batchChangeAdminPermission",data:e},{successCallback:function(e){return t(e)}})},addAdminRole:function(e,t){Base.submit(null,{url:s+"addAdminRole",data:e},{successCallback:function(e){return t(e)}})},updateAdmin:function(e,t){Base.submit(null,{url:s+"updateAdmin",data:e},{successCallback:function(e){return t(e)}})},updateBatchAdminStatus:function(e,t){Base.submit(null,{url:s+"updateRoleEffectiveByRoleId",data:e},{successCallback:function(e){return t(e)}})},queryUseSysPermission:function(e,t){Base.submit(null,{url:s+"queryRootResource",data:e},{successCallback:function(e){return t(e)}})},queryUsePermissionByResourceId:function(e,t){Base.submit(null,{url:s+"queryChildResourceByRoleId",data:e},{successCallback:function(e){return t(e)}})},saveAdminUsePermission:function(e,t){Base.submit(null,{url:s+"changeResourceUsePermission",data:e},{successCallback:function(e){return t(e)}})},updateAdminUsePermissionEffectiveTime:function(e,t){Base.submit(null,{url:s+"updateUsePermissionEffectiveTime",data:e},{successCallback:function(e){return t(e)}})},saveBatchAdminUsePermissionEffectiveTime:function(e,t){Base.submit(null,{url:s+"updateBatchUsePermissionEffectiveTime",data:e},{successCallback:function(e){return t(e)}})},queryObjectUseSysPermission:function(e,t){Base.submit(null,{url:s+"queryRootCustomResource",data:e},{successCallback:function(e){return t(e)}})},queryObjectUsePermissionByResourceId:function(e,t){Base.submit(null,{url:s+"queryChildCustomResourceByRoleId",data:e},{successCallback:function(e){return t(e)}})},saveAdminObjectUsePermission:function(e,t){Base.submit(null,{url:s+"changeCustomResourceUsePermission",data:e},{successCallback:function(e){return t(e)}})},updateAdminObjectUsePermissionEffectiveTime:function(e,t){Base.submit(null,{url:s+"updateCustomResourceUsePermissionEffectiveTime",data:e},{successCallback:function(e){return t(e)}})},updateBatchAdminObjectUsePermissionEffectiveTime:function(e,t){Base.submit(null,{url:s+"updateBatchCustomResourceUsePermissionEffectiveTime",data:e},{successCallback:function(e){return t(e)}})},queryGrantSysPermission:function(e,t){Base.submit(null,{url:s+"queryRootResourceAuthority",data:e},{successCallback:function(e){return t(e)}})},queryGrantPermissionByResourceId:function(e,t){Base.submit(null,{url:s+"queryChildResourceAuthorityByRoleId",data:e},{successCallback:function(e){return t(e)}})},saveAdminGrantPermission:function(e,t){Base.submit(null,{url:s+"changeResourceAuthority",data:e},{successCallback:function(e){return t(e)}})},queryObjectGrantSysPermission:function(e,t){Base.submit(null,{url:s+"queryRootCustomResourceAuthority",data:e},{successCallback:function(e){return t(e)}})},queryObjectGrantPermissionByResourceId:function(e,t){Base.submit(null,{url:s+"queryChildCustomResourceAuthorityByRoleId",data:e},{successCallback:function(e){return t(e)}})},saveAdminObjectGrantPermission:function(e,t){Base.submit(null,{url:s+"changeCustomResourceAuthority",data:e},{successCallback:function(e){return t(e)}})},addBatchUserRole:function(e,t){Base.submit(null,{url:s+"addBatchUserRole",data:e},{successCallback:function(e){return t(e)}})},deleteBatchUserRole:function(e,t){Base.submit(null,{url:s+"deleteBatchUserRole",data:e},{successCallback:function(e){return t(e)}})},queryChildCustomResourceAsync:function(e,t){Base.submit(null,{url:s+"queryChildCustomResourceAsync",data:e},{successCallback:function(e){return t(e)}})},addAllRoleUsersByOrgId:function(e,t){Base.submit(null,{url:s+"addAllRoleUsersByOrgId",data:e},{successCallback:function(e){return t(e)}})}}}}]);
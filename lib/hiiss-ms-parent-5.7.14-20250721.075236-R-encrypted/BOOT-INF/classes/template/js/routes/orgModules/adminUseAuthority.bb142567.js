"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[2241],{82947:function(e,t,n){n.d(t,{Z:function(){return o}});var s=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("div",{staticClass:"menu-title"},[n("ta-icon",{attrs:{type:"menu-fold"}}),n("span",{staticStyle:{"margin-left":"5px","font-weight":"bold"}},[e._v(e._s(e.title))])],1),n("ta-divider",{staticStyle:{margin:"0"}}),n("ta-menu",{attrs:{mode:"inline",selectedKeys:e.selectedKeys},on:{click:e.onClick}},e._l(e.data,(function(t){return n("ta-menu-item",{key:t.resourceId},[n("ta-icon",{attrs:{type:"appstore"}}),e._v(" "+e._s(t.name)+" ")],1)})),1)],1)},a=[],r={name:"adminLeftMenu",props:{title:{type:String,required:!0},data:{type:Array,required:!0},selectedKeys:{type:Array}},data:function(){return{}},methods:{onClick:function(e){var t=e.item,n=e.key,s=e.keyPath;this.$emit("click",{item:t,key:n,keyPath:s})}}},i=r,c=n(1001),u=(0,c.Z)(i,s,a,!1,null,"8bcd3b7a",null),o=u.exports},67910:function(e,t,n){n.r(t),n.d(t,{default:function(){return p}});var s=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"fit"},[n("ta-border-layout",{attrs:{layout:{header:"70px"}}},[n("div",{staticStyle:{"text-align":"center",overflow:"hidden"},attrs:{slot:"header"},slot:"header"},[n("ta-breadcrumb",{staticStyle:{"line-height":"42px",float:"left"},attrs:{separator:">"}},[n("ta-breadcrumb-item",[n("a",{on:{click:e.fnBackToHome}},[e._v("角色维度")])]),n("ta-breadcrumb-item",[e._v("功能使用权限")])],1),n("div",{staticClass:"divider-header"}),n("ta-alert",{staticStyle:{float:"left","margin-top":"2px"},attrs:{message:"当前角色为："+this.$route.query.roleName,type:"info","show-icon":""}}),n("ta-button",{staticStyle:{float:"right","margin-top":"8px"},on:{click:e.fnBackToHome}},[n("ta-icon",{attrs:{type:"rollback"}}),e._v("返回 ")],1)],1),n("ta-tabs",{staticClass:"fit content-box"},[n("ta-tab-pane",{attrs:{tab:"功能使用权限"}},[n("ta-row",{staticClass:"fit"},[n("ta-col",{staticClass:"fit authority-box",staticStyle:{"border-right":"1px solid #eee",height:"100%"},attrs:{span:4}},[n("admin-left-menu",{attrs:{title:"功能使用一级菜单",data:e.menuData,"selected-keys":e.menuSelectedKeys},on:{click:e.onSelectMenu}})],1),n("ta-col",{staticClass:"right-box fit",attrs:{span:20}},[n("div",{staticClass:"fit",staticStyle:{border:"1px solid #e8e8e8",height:"100%"}},[n("ta-big-table",{key:e.adminUseTreeData.length,ref:"xTable",attrs:{height:"100%",resizable:"",border:"","row-id":"resourceId","cell-class-name":e.cellClassName,"tree-config":{children:"children",expandAll:!0},"expand-config":{lazy:!0},data:e.adminUseTreeData,"checkbox-config":{labelField:"name",checkField:"checked",showHeader:!1}}},[n("ta-big-table-column",{attrs:{type:"checkbox",field:"name",title:"可授权的功能菜单","tree-node":"","filter-method":e.filterAgeMethod,filters:[{data:""}]},scopedSlots:e._u([{key:"header",fn:function(t){t.row;return[n("span",[e._v("可授权的功能菜单")]),n("ta-input",{staticStyle:{width:"250px","margin-left":"10px"},attrs:{placeholder:"输入关键字进行过滤"},on:{click:function(e){e.stopPropagation(),e.preventDefault()}},model:{value:e.filterText,callback:function(t){e.filterText=t},expression:"filterText"}}),n("ta-button",{attrs:{type:"primary"},on:{click:function(t){return t.stopPropagation(),t.preventDefault(),e.fnSaveAdminUsePermission("refresh")}}},[e._v(" 权限保存 ")])]}}])}),n("ta-big-table-column",{attrs:{field:"effectTime",title:"有效期",width:"25%"},scopedSlots:e._u([{key:"header",fn:function(t){t.row;return[n("span",[e._v("有效期")]),n("ta-popover",{attrs:{placement:"bottom",trigger:"manual",value:e.batchPop}},[n("div",{staticClass:"pop-calendar"},[n("ta-calendar",{attrs:{fullscreen:!1},on:{select:e.onPanelChange}})],1),n("div",{staticStyle:{float:"right","margin-top":"10px"}},[n("ta-button",{attrs:{size:"small"},on:{click:function(t){e.batchPop=!1}}},[e._v(" 取消 ")]),n("ta-button",{attrs:{size:"small"},on:{click:function(t){return e.fnSaveAuthorityEffectiveTime(!0)}}},[e._v(" 设为永久 ")]),n("ta-button",{attrs:{type:"primary",size:"small"},on:{click:function(t){return e.fnSaveAuthorityEffectiveTime(!1)}}},[e._v(" 确定 ")])],1),n("ta-button",{staticStyle:{float:"right","margin-right":"10px"},attrs:{slot:"reference"},on:{click:function(t){e.batchPop=!0}},slot:"reference"},[e._v(" 批量设置有效期 ")])],1)]}},{key:"default",fn:function(t){var s=t.row;return[s.checked?n("span",{staticStyle:{float:"right"}},[e._v(" "+e._s(s.effectTime&&null!=s.effectTime?e.moment(s.effectTime).format("YYYY-MM-DD"):"永久")+" "),n("ta-popover",{attrs:{placement:"bottom",trigger:"manual",value:s.resourceId==e.indexClick}},[n("div",{staticClass:"pop-calendar"},[n("ta-calendar",{attrs:{fullscreen:!1},on:{select:e.onPanelChange}})],1),n("div",{staticStyle:{float:"right","margin-top":"10px"}},[n("ta-button",{attrs:{size:"small"},on:{click:function(t){e.indexClick=null}}},[e._v("取消")]),n("ta-button",{attrs:{size:"small"},on:{click:function(t){return e.onCellChange(s,!0)}}},[e._v("设为永久")]),n("ta-button",{attrs:{type:"primary",size:"small"},on:{click:function(t){return e.onCellChange(s,!1)}}},[e._v("确定 ")])],1),n("ta-icon",{staticStyle:{"margin-left":"10px"},attrs:{slot:"reference",type:"edit"},on:{click:function(t){e.indexClick=s.resourceId}},slot:"reference"})],1)],1):e._e()]}}])})],1)],1)])],1)],1)],1)],1)],1)},a=[],r=n(89584),i=n(16158),c=n(82947),u=n(36797),o=n.n(u),l=n(61543),d=n(52245),f=n(28464),m={name:"adminUseAuthority",components:{AdminLeftMenu:c.Z},data:function(){return{indexClick:null,menuSelectedKeys:[],filterText:"",batchPop:!1,effectiveTime:"",menuData:[],adminUseTreeData:[],copyAdminUseTreeData:[],defaultKeys:[],defaultProps:{children:"children",label:"name",id:"resourceId"},resourceItem:{},row:{col:{span:12}},item:{},roleId:""}},watch:{filterText:function(e){this.filterAgeMethod(e)}},activated:function(){this.item=this.$route.query,this.item.roleId?(this.roleId=this.item.roleId,this.menuData=[],this.adminUseTreeData=[],this.defaultKeys=[],this.batchPop=!1,this.indexClick=null,this.fnLoadDefault()):this.fnBackToHome()},methods:{moment:o(),cellClassName:function(e){var t=e.row,n=(e.rowIndex,e.column);e.columnIndex;return"name"!==n.property||t.children?"":"no-children-tree-node"},filterAgeMethod:function(e){var t=this,n=(0,f.Z)(e).trim();if(n){var s={children:"children"},a=["name"];this.adminUseTreeData=(0,d.Z)(this.copyAdminUseTreeData,(function(e){return a.some((function(t){return(0,f.Z)(e[t]).indexOf(n)>-1}))}),s),this.$nextTick((function(){t.$refs.xTable.setAllTreeExpand(!0)}))}else this.adminUseTreeData=this.copyAdminUseTreeData,this.$refs.xTable.setAllTreeExpand(!0)},filterNode:function(e,t){return!e||-1!==t.name.indexOf(e)},onCellChange:function(e,t){var n=this,s={roleId:this.roleId,resourceId:e.resourceId,effectTime:t?null:this.effectiveTime};i.Z.updateAdminUsePermissionEffectiveTime(s,(function(){n.$message.success("更新有效期成功"),n.fnQueryUsePermissionByResourceId(n.resourceItem),n.indexClick=null,n.batchPop=!1}))},fnSaveAuthorityEffectiveTime:function(e){var t=this,n=[],s=this.$refs.xTable;n=[].concat((0,r.Z)(s.getCheckboxRecords()),(0,r.Z)(s.getCheckboxIndeterminateRecords()));var a=n.map((function(e){return e.resourceId})),c={roleId:this.roleId,resourceIds:a.join(","),effectTime:e?null:this.effectiveTime};i.Z.saveBatchAdminUsePermissionEffectiveTime(c,(function(e){t.$message.success("批量设置有效期成功"),t.fnQueryUsePermissionByResourceId(t.resourceItem),t.indexClick=null,t.batchPop=!1}))},onPanelChange:function(e,t){this.effectiveTime=e.format("YYYY-MM-DD")},onSelectMenu:function(e){var t=this,n=(e.item,e.key),s=e.keyPath;if(this.resourceItem.resouceId!==n){var a=[],i=this.$refs.xTable;a=[].concat((0,r.Z)(a),(0,r.Z)(i.getCheckboxRecords()),(0,r.Z)(i.getCheckboxIndeterminateRecords()));var c=a.map((function(e){return e.resourceId})),u=(0,r.Z)(this.menuData),o=u.filter((function(e){return n===e.resourceId}))[0];this.defaultKeys.sort().toString()!==c.sort().toString()?this.$confirm({title:"提示",content:"当前使用授权权限修改未保存，是否保存?",cancelText:"取消",okText:"确认",onOk:function(){t.fnSaveAdminUsePermission(),t.menuSelectedKeys=s,t.fnQueryUsePermissionByResourceId(o)},onCancel:function(){t.menuSelectedKeys=s,t.fnQueryUsePermissionByResourceId(o)}}):(this.menuSelectedKeys=s,this.fnQueryUsePermissionByResourceId(o))}},fnSaveAdminUsePermission:function(e){var t=this,n=[],s=this.$refs.xTable;n=[].concat((0,r.Z)(n),(0,r.Z)(s.getCheckboxRecords()),(0,r.Z)(s.getCheckboxIndeterminateRecords(!0)));var a=n.map((function(e){return e.resourceId})),c={roleId:this.roleId,resourceIds:a.join(","),parentResourceId:this.resourceItem.resourceId,resourceType:this.resourceItem.resourceType};i.Z.saveAdminUsePermission(c,(function(n){t.$message.success("更新数据成功"),"refresh"===e&&t.fnQueryUsePermissionByResourceId(t.resourceItem)}))},fnLoadDefault:function(){var e=this,t={roleId:this.roleId};i.Z.queryUseSysPermission(t,(function(t){e.menuData=t.data.resourceList,e.$nextTick((function(){e.menuData&&e.menuData.length>0&&(e.menuSelectedKeys=[e.menuData[0].resourceId],e.fnQueryUsePermissionByResourceId(e.menuData[0]))}))}))},fnQueryUsePermissionByResourceId:function(e){var t=this;this.adminUseTreeData=[],this.resourceItem=e;var n={roleId:this.roleId,idPath:e.idPath};i.Z.queryUsePermissionByResourceId(n,(function(e){t.adminUseTreeData=e.data.resourceTree,t.copyAdminUseTreeData=(0,l.Z)(t.adminUseTreeData,!0),t.defaultKeys=[],t.$nextTick((function(){var e=t.$refs.xTable,n=[].concat((0,r.Z)(e.getCheckboxRecords()),(0,r.Z)(e.getCheckboxIndeterminateRecords(!0))),s=n.map((function(e){return e.resourceId}));t.defaultKeys=s}))}))},fnBackToHome:function(){this.$router.push({name:"adminRoleManagement"})}}},h=m,y=n(1001),b=(0,y.Z)(h,s,a,!1,null,"4a81f9d4",null),p=b.exports},16158:function(e,t){var n="/org/authority/adminAuthorityManagementRestService/";t["Z"]={queryUsersByRoleId:function(e,t){Base.submit(null,{url:n+"queryUsersByRoleId",data:e},{successCallback:function(e){return t(e)}})},queryUsersByRoleIdNoPage:function(e,t){Base.submit(null,{url:n+"queryUsersByRoleIdNoPage",data:e},{successCallback:function(e){return t(e)}})},addBatchAdminUser:function(e,t){Base.submit(null,{url:n+"addBatchRoleUsers",data:e},{successCallback:function(e){return t(e)}})},deleteBatchAdmin:function(e,t){Base.submit(null,{url:n+"deleteBatchRole",data:e},{successCallback:function(e){return t(e)}})},queryAllTaOrg:function(e,t){Base.submit(null,{url:n+"queryCurrentAdminRoleWrapOrgTree",data:e},{successCallback:function(e){return t(e)}})},deleteRoleUserByKey:function(e,t){Base.submit(null,{url:n+"deleteRoleUserByKey",data:e},{successCallback:function(e){return t(e)}})},deleteBatchRoleUsers:function(e,t){Base.submit(null,{url:n+"deleteBatchRoleUsers",data:e},{successCallback:function(e){return t(e)}})},queryBatchUserByOrgId:function(e,t){Base.submit(null,{url:n+"queryUsersNoWraperByRoleId",data:e},{successCallback:function(e){return t(e)}})},queryBatchUserByOrgIdNoPage:function(e,t){Base.submit(null,{url:n+"queryUsersNoWraperByRoleIdNoPage",data:e},{successCallback:function(e){return t(e)}})},queryOrgPermission:function(e,t){Base.submit(null,{url:n+"selectOrgScope",data:e},{successCallback:function(e){return t(e)}})},removeOrgPermission:function(e,t){Base.submit(null,{url:n+"removeOrgScope",data:e},{successCallback:function(e){return t(e)}})},selectPermissionOrgScope:function(e,t){Base.submit(null,{url:n+"selectPermissionOrgScope",data:e},{successCallback:function(e){return t(e)}})},addOrgPermission:function(e,t){Base.submit(null,{url:n+"saveOrgScope",data:e},{successCallback:function(e){return t(e)}})},queryBatchPermissionTreeData:function(e,t){Base.submit(null,{url:n+"queryBatchPermissionTreeData",data:e},{successCallback:function(e){return t(e)}})},batchChangeAdminPermission:function(e,t){Base.submit(null,{url:n+"batchChangeAdminPermission",data:e},{successCallback:function(e){return t(e)}})},addAdminRole:function(e,t){Base.submit(null,{url:n+"addAdminRole",data:e},{successCallback:function(e){return t(e)}})},updateAdmin:function(e,t){Base.submit(null,{url:n+"updateAdmin",data:e},{successCallback:function(e){return t(e)}})},updateBatchAdminStatus:function(e,t){Base.submit(null,{url:n+"updateRoleEffectiveByRoleId",data:e},{successCallback:function(e){return t(e)}})},queryUseSysPermission:function(e,t){Base.submit(null,{url:n+"queryRootResource",data:e},{successCallback:function(e){return t(e)}})},queryUsePermissionByResourceId:function(e,t){Base.submit(null,{url:n+"queryChildResourceByRoleId",data:e},{successCallback:function(e){return t(e)}})},saveAdminUsePermission:function(e,t){Base.submit(null,{url:n+"changeResourceUsePermission",data:e},{successCallback:function(e){return t(e)}})},updateAdminUsePermissionEffectiveTime:function(e,t){Base.submit(null,{url:n+"updateUsePermissionEffectiveTime",data:e},{successCallback:function(e){return t(e)}})},saveBatchAdminUsePermissionEffectiveTime:function(e,t){Base.submit(null,{url:n+"updateBatchUsePermissionEffectiveTime",data:e},{successCallback:function(e){return t(e)}})},queryObjectUseSysPermission:function(e,t){Base.submit(null,{url:n+"queryRootCustomResource",data:e},{successCallback:function(e){return t(e)}})},queryObjectUsePermissionByResourceId:function(e,t){Base.submit(null,{url:n+"queryChildCustomResourceByRoleId",data:e},{successCallback:function(e){return t(e)}})},saveAdminObjectUsePermission:function(e,t){Base.submit(null,{url:n+"changeCustomResourceUsePermission",data:e},{successCallback:function(e){return t(e)}})},updateAdminObjectUsePermissionEffectiveTime:function(e,t){Base.submit(null,{url:n+"updateCustomResourceUsePermissionEffectiveTime",data:e},{successCallback:function(e){return t(e)}})},updateBatchAdminObjectUsePermissionEffectiveTime:function(e,t){Base.submit(null,{url:n+"updateBatchCustomResourceUsePermissionEffectiveTime",data:e},{successCallback:function(e){return t(e)}})},queryGrantSysPermission:function(e,t){Base.submit(null,{url:n+"queryRootResourceAuthority",data:e},{successCallback:function(e){return t(e)}})},queryGrantPermissionByResourceId:function(e,t){Base.submit(null,{url:n+"queryChildResourceAuthorityByRoleId",data:e},{successCallback:function(e){return t(e)}})},saveAdminGrantPermission:function(e,t){Base.submit(null,{url:n+"changeResourceAuthority",data:e},{successCallback:function(e){return t(e)}})},queryObjectGrantSysPermission:function(e,t){Base.submit(null,{url:n+"queryRootCustomResourceAuthority",data:e},{successCallback:function(e){return t(e)}})},queryObjectGrantPermissionByResourceId:function(e,t){Base.submit(null,{url:n+"queryChildCustomResourceAuthorityByRoleId",data:e},{successCallback:function(e){return t(e)}})},saveAdminObjectGrantPermission:function(e,t){Base.submit(null,{url:n+"changeCustomResourceAuthority",data:e},{successCallback:function(e){return t(e)}})},addBatchUserRole:function(e,t){Base.submit(null,{url:n+"addBatchUserRole",data:e},{successCallback:function(e){return t(e)}})},deleteBatchUserRole:function(e,t){Base.submit(null,{url:n+"deleteBatchUserRole",data:e},{successCallback:function(e){return t(e)}})},queryChildCustomResourceAsync:function(e,t){Base.submit(null,{url:n+"queryChildCustomResourceAsync",data:e},{successCallback:function(e){return t(e)}})},addAllRoleUsersByOrgId:function(e,t){Base.submit(null,{url:n+"addAllRoleUsersByOrgId",data:e},{successCallback:function(e){return t(e)}})}}}}]);
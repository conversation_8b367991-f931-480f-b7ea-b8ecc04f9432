"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[7817],{50255:function(t,e,a){a.r(e),a.d(e,{default:function(){return u}});var i=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("div",{staticClass:"fit"},[i("ta-button-group",[i("ta-button",{on:{click:function(t){return e.openDrawer(1)}}},[e._v(" 一般新增/编辑 ")]),i("ta-button",{on:{click:function(t){return e.openDrawer(2)}}},[e._v(" 查看详情/编辑 ")]),i("ta-button",{on:{click:function(t){return e.openDrawer(3)}}},[e._v(" 仅查看详情 ")])],1),i("ta-drawer",{attrs:{title:e.title,width:"500",placement:"right",closable:!1,visible:e.visible},on:{close:e.onClose},scopedSlots:e._u([{key:"footer",fn:function(){return[i("ta-button",{staticStyle:{"margin-right":"10px"},on:{click:e.setFieldValue}},[e._v(" 设置值 ")]),i("ta-button",{attrs:{type:"primary"},on:{click:e.handleSubmit}},[e._v(" 提交 ")])]},proxy:!0}])},[i("ta-form",{attrs:{"auto-form-create":function(e){t.form=e},"detail-edit":e.detailEdit}},[i("ta-form-item",{attrs:{label:"姓名","field-decorator-id":"name",require:{message:"请输入姓名!"}},scopedSlots:e._u([{key:"detail",fn:function(t){t.id,t.label;var a=t.value;return[e._v(" "+e._s(a)+" ")]}}])},[i("ta-input",{attrs:{placeholder:"请输入姓名"}})],1),i("ta-form-item",{attrs:{label:"性别","field-decorator-id":"sex",require:{message:"请选择性别!"}},scopedSlots:e._u([{key:"detail",fn:function(t){t.id,t.label;var a=t.value;return[e._v(" "+e._s("1"===a?"男":"2"===a?"女":"")+" ")]}}])},[i("ta-select",{staticStyle:{width:"100%"}},[i("ta-select-option",{attrs:{value:"1"}},[e._v(" 男 ")]),i("ta-select-option",{attrs:{value:"2"}},[e._v(" 女 ")])],1)],1),i("ta-form-item",{attrs:{label:"年龄","field-decorator-id":"age"},scopedSlots:e._u([{key:"detail",fn:function(t){t.id,t.label;var a=t.value;return[e._v(" "+e._s(a)+" ")]}}])},[i("ta-input",{attrs:{placeholder:"请输入年龄"}})],1),i("ta-form-item",{attrs:{label:"民族","field-decorator-id":"mingz"},scopedSlots:e._u([{key:"detail",fn:function(t){t.id,t.label;var a=t.value;return[e._v(" "+e._s("1"===a?"汉族":"未填写")+" ")]}}])},[i("ta-select",{staticStyle:{width:"100%"}},[i("ta-select-option",{attrs:{value:"1"}},[e._v(" 汉族 ")])],1)],1),i("ta-form-item",{attrs:{label:"证件号码","field-decorator-id":"idcard",require:{message:"请输入身份证号码!"},"field-decorator-options":{rules:[{idCard:"2",message:"输入的身份证号码不合法"}]}},scopedSlots:e._u([{key:"detail",fn:function(t){t.id,t.label;var a=t.value;return[e._v(" "+e._s(a)+" ")]}}])},[i("ta-input",{attrs:{placeholder:"请输入身份证号"}})],1),i("ta-form-item",{attrs:{label:"联系电话","field-decorator-id":"phone",require:{message:"请输入电话号码!"},"field-decorator-options":{rules:[{phone:"mobile",message:"输入的手机号码不合法"}]}},scopedSlots:e._u([{key:"detail",fn:function(t){t.id,t.label;var a=t.value;return[e._v(" "+e._s(a)+" ")]}}])},[i("ta-input",{attrs:{placeholder:"请输入联系电话"}})],1)],1)],1)],1)},l=[],r={data:function(){return{title:"一般新增/编辑",visible:!1,detailEdit:!1}},methods:{openDrawer:function(t){switch(this.visible=!0,t){case 1:this.title="一般新增/编辑",this.detailEdit=!1;break;case 2:this.title="查看详情/编辑",this.detailEdit=!0;break;case 3:this.title="仅查看详情",this.$set(this.$data,"detailEdit",{edit:!1});break}},onClose:function(){this.visible=!1},setFieldValue:function(){var t={name:"Nacy",sex:"1",idcard:"5401061995106035410",phone:"13850107544"};this.form.setFieldsValue(t)},handleSubmit:function(){}}},o=r,s=a(1001),n=(0,s.Z)(o,i,l,!1,null,null,null),u=n.exports}}]);
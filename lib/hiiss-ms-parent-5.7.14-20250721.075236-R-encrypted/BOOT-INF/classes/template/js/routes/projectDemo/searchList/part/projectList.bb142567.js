"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[4437],{33464:function(t,e,i){i.r(e),i.d(e,{default:function(){return $}});var a,n,s=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",t._l(t.data,(function(e){return i("ta-card",{key:e.id,staticStyle:{display:"inline-block",width:"calc(25% - 20px)",margin:"10px"},attrs:{loading:t.loading,hoverable:""}},[i("template",{slot:"cover"},[t.loading?i("div",{staticStyle:{height:"200px","line-height":"200px","text-align":"center"}},[i("ta-spin",[i("ta-icon",{staticStyle:{"font-size":"24px"},attrs:{slot:"indicator",type:"loading",spin:""},slot:"indicator"})],1)],1):i("img",{attrs:{alt:"example",src:e.cover}})]),i("ta-card-meta",{attrs:{title:e.title}},[i("template",{slot:"description"},[i("span",{staticClass:"project-list-item-desc-text"},[t._v(t._s(e.description))])])],2),i("div",{staticClass:"cardItemContent"},[i("span",[t._v(t._s(e.updatedAt))]),i("div",{staticClass:"avatarList"},[i("avatar-list",{attrs:{size:"small","max-length":2}},t._l(e.members,(function(t,a){return i("avatar-list-item",{key:e.id+"-avatar-"+a,attrs:{src:t.avatar,tips:t.name}})})),1)],1)])],2)})),1)},r=[],l=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return t.split("").reduce((function(t,e){var i=e.charCodeAt(0);return i>=0&&i<=128?t+1:t+2}),0)},o=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1?arguments[1]:void 0,i=0;return t.split("").reduce((function(t,a){var n=a.charCodeAt(0);return i+=n>=0&&n<=128?1:2,i<=e?t+a:t}),"")},c={name:"ellipsis",props:{prefixCls:{type:String,default:"ant-pro-ellipsis"},tooltip:{type:Boolean},length:{type:Number,required:!0},lines:{type:Number,default:1},fullWidthRecognition:{type:Boolean,default:!1}},methods:{getStrDom:function(t,e){var i=this.$createElement;return i("span",[o(t,this.length)+(e>this.length?"...":"")])},getTooltip:function(t,e){var i=this.$createElement;return i("ta-tooltip",[i("template",{slot:"title"},[t]),this.getStrDom(t,e)])}},render:function(){var t=this.$props,e=t.tooltip,i=t.length,a=this.$slots.default.map((function(t){return t.text})).join(""),n=l(a),s=e&&n>i?this.getTooltip(a,n):this.getStrDom(a,n);return s}},u=c,p=i(1001),d=(0,p.Z)(u,a,n,!1,null,null,null),m=d.exports,h=i(82482),f=i(66604),g=i(28856),v=i(54212),x=i(68662),_={tips:f.Z.string.def(""),src:f.Z.string.def("")},A={__ANT_AVATAR_CHILDREN:!0,name:"avatarListItem",props:_,created:function(){(0,x.Kp)((0,v.cV)(this.$parent).__ANT_AVATAR_LIST,"AvatarListItem must be a subcomponent of AvatarList")},render:function(){var t=arguments[0],e=t("ta-avatar",{attrs:{size:this.$parent.size,src:this.src}});return this.tips&&t("ta-tooltip",{attrs:{title:this.tips}},[e])||t(e)}},L=A;function y(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t.filter((function(t){return t.tag||t.text&&""!==t.text.trim()}))}var b={prefixCls:f.Z.string.def("ta-avatar-list"),size:{validator:function(t){return"number"===typeof t||["small","large","default"].includes(t)},default:"default"},maxLength:f.Z.number.def(0),excessItemsStyle:f.Z.object.def({color:"#f56a00",backgroundColor:"#fde3cf"})},C={__ANT_AVATAR_LIST:!0,Item:L,name:"avatarList",props:b,render:function(t){var e,i=this.$props,a=i.prefixCls,n=i.size,s=(e={},(0,h.Z)(e,"".concat(a),!0),(0,h.Z)(e,"".concat(n),!0),e),r=y(this.$slots.default),l=r&&r.length?t("ul",{class:"".concat(a,"-items")},[this.getItems(r)]):null;return t("div",{class:s},[l])},methods:{getItems:function(t){var e,i=this.$createElement,a=(e={},(0,h.Z)(e,"".concat(this.prefixCls,"-item"),!0),(0,h.Z)(e,"".concat(this.size),!0),e),n=t.length;return this.maxLength>0&&(t=t.slice(0,this.maxLength),t.push(i(g.Z,{attrs:{size:this.size},style:this.excessItemsStyle},["+".concat(n-this.maxLength)]))),t.map((function(t){return i("li",{class:a},[t])}))}},install:function(t){t.component(C.name,C),t.component(C.Item.name,C.Item)}},I=C,Z={name:"projectList",components:{Ellipsis:m,AvatarList:I,AvatarListItem:L},data:function(){return{data:Array.from({length:6}).map((function(t,e){return{id:e}})),loading:!0}},mounted:function(){this.getList()},methods:{getList:function(){var t=this;Base.submit(null,{url:"http/mock/projectDemo/list/article",data:{count:8},showPageLoading:!1}).then((function(e){t.data=e.data.list,t.loading=!1}))}}},S=Z,T=(0,p.Z)(S,s,r,!1,null,null,null),$=T.exports}}]);
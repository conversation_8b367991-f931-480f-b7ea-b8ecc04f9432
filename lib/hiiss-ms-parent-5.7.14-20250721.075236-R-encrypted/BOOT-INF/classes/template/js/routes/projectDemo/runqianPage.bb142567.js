"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[5583],{53189:function(e,t,a){a.r(t),a.d(t,{default:function(){return v}});var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"fit"},[a("ta-alert",{attrs:{message:"该示例与<润乾模板管理>功能共享一个数据来源, 请查看示例时, 谨慎操作",type:"warning","show-icon":""}}),a("ta-border-layout",{attrs:{layout:{header:"70px",footer:"70px"},"center-cfg":{showBar:!0,toolBarStyle:{height:"50px",lineHeight:"48px"}},"footer-cfg":{showBorder:!1}}},[a("div",{staticClass:"center-box",attrs:{slot:"header"},slot:"header"},[a("ta-input-search",{staticClass:"search-box",attrs:{placeholder:"根据报表标志模糊查询","enter-button":"搜索"},on:{search:e.searchQuery},model:{value:e.param,callback:function(t){e.param=t},expression:"param"}})],1),a("div",{attrs:{slot:"centerExtraContent"},slot:"centerExtraContent"},[a("ta-tag-select",{attrs:{title:"报表类型",data:e.CollectionData("RAQTYPE"),"is-multi":!0},on:{change:e.searchQuery},model:{value:e.tags,callback:function(t){e.tags=t},expression:"tags"}}),a("div",{staticStyle:{float:"right"}},[a("ta-button",{key:"delete",on:{click:e.fnDelTem}},[e._v(" 删除 ")]),a("ta-button",{key:"add",on:{click:e.openAddWin}},[e._v(" 新增 ")])],1)],1),a("ta-table",{attrs:{columns:e.columns,"row-key":"raqfilename",pagination:!1,"data-source":e.gridData,"row-selection":{selectedRowKeys:e.selectedRowKeys,onChange:e.onSelectChange}}},[a("span",{attrs:{slot:"operate"},slot:"operate"},[a("ta-table-operate",{attrs:{"operate-menu":e.operateMenu}})],1)]),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("ta-pagination",{ref:"gridPager",staticStyle:{float:"right","margin-top":"10px"},attrs:{"show-size-changer":"","show-quick-jumper":"","data-source":e.gridData,"default-page-size":10,"page-size":e.pageSize,params:e.templatePageParams,url:"tarunqianresource/taRunqianResourceRestService/queryRunqianResource"},on:{"update:dataSource":function(t){e.gridData=t},"update:data-source":function(t){e.gridData=t},"update:pageSize":function(t){e.pageSize=t},"update:page-size":function(t){e.pageSize=t}}})],1),a("add-template",{attrs:{visible:e.editVisible,"raq-type":e.raqType,"edit-type":e.editType,record:e.record},on:{close:e.closeWin}})],1),a("ta-drawer",{attrs:{visible:e.printVisible,title:"打印预览",width:"800",placement:"right",closable:!0},on:{close:e.handleClosePrintPage}},[a("ta-button",{on:{click:e.handlePrint}},[e._v(" 打印 ")]),a("ta-button",{on:{click:e.handleDownload}},[e._v(" 下载 ")]),a("ta-divider"),a("pdf-viewer",{ref:"pdfViewer",staticStyle:{height:"600px"},attrs:{"is-simple-show-mode":!0}})],1)],1)},r=[],o=function(){var e=this,t=this,a=t.$createElement,i=t._self._c||a;return i("div",[i("ta-drawer",{attrs:{title:"1"===t.editType?"新增报表":"编辑报表",width:"500",placement:"right",closable:!0,visible:t.visible,"destroy-on-close":""},on:{close:t.closeEdit}},[i("ta-form",{attrs:{"auto-form-create":function(t){e.form=t}}},["2"===t.raqType?i("ta-form-item",{attrs:{label:"父报表标志","label-col":t.formItemLayout.labelCol,"wrapper-col":t.formItemLayout.wrapperCol,"field-decorator-id":"parentraqfilename","field-decorator-options":{initialValue:t.record.parentraqfilename?t.record.parentraqfilename:""}}},[i("ta-input",{attrs:{disabled:""}})],1):t._e(),i("ta-form-item",{attrs:{label:"报表类型","label-col":t.formItemLayout.labelCol,"wrapper-col":t.formItemLayout.wrapperCol,"field-decorator-id":"raqType","field-decorator-options":{rules:[{required:!0,message:"请选择报表类型"}],initialValue:t.raqType}}},[i("ta-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择报表类型",disabled:""}},t._l(t.CollectionData("RAQTYPE"),(function(e){return i("ta-select-option",{key:e.value,attrs:{value:e.value}},[t._v(" "+t._s(e.label)+" ")])})),1)],1),i("ta-form-item",{attrs:{label:"资源文件","label-col":t.formItemLayout.labelCol,"wrapper-col":t.formItemLayout.wrapperCol,"field-decorator-id":"raqSrc","field-decorator-options":{rules:[{required:!0,message:"请选择报表文件"}]}}},[i("ta-input",{ref:"file",staticStyle:{padding:"2px 1px"},attrs:{id:"upfile",type:"file"},on:{change:t.fnChooseFile}})],1),i("ta-form-item",{attrs:{label:"是否公用","label-col":t.formItemLayout.labelCol,"wrapper-col":t.formItemLayout.wrapperCol,"field-decorator-id":"yab109",initValue:t.checkAll,"value-prop-name":"checked"}},[i("ta-checkbox",{attrs:{value:"com"}})],1),i("ta-form-item",{attrs:{label:"报表标志","label-col":t.formItemLayout.labelCol,"wrapper-col":t.formItemLayout.wrapperCol,"field-decorator-id":"raqfilename","field-decorator-options":{rules:[{required:!0,message:"请填写报表标志"}],initialValue:t.record.raqfilename?t.record.raqfilename:""}}},[i("ta-input",{attrs:{disabled:"2"===t.editType}})],1),i("ta-form-item",{attrs:{label:"报表名称","label-col":t.formItemLayout.labelCol,"wrapper-col":t.formItemLayout.wrapperCol,"field-decorator-id":"raqname","field-decorator-options":{rules:[{required:!0,message:"请输入报表名称"}],initialValue:t.record.raqname?t.record.raqname:""}}},[i("ta-input")],1)],1),i("div",{attrs:{slot:"footer"},slot:"footer"},[i("ta-button-group",[i("ta-button",{on:{click:function(e){return t.form.resetFields()}}},[t._v(" 重置 ")]),"1"===t.editType?i("ta-button",{attrs:{type:"primary"},on:{click:function(e){return t.submitAddForm()}}},[t._v(" 保存 ")]):t._e(),"1"!==t.editType?i("ta-button",{attrs:{type:"primary"},on:{click:function(e){return t.submitEditForm()}}},[t._v(" 保存 ")]):t._e()],1)],1)],1)],1)},n=[],l="/tarunqianresource/taRunqianResourceRestService/",s={editRunqianResource:function(e,t){Base.submit(null,{url:l+"editRunqianResource",data:e,isFormData:!0},{successCallback:function(e){return t(e)}})},addRunqianResource:function(e,t){Base.submit(null,{url:l+"addRunqianResource",data:e,isFormData:!0},{successCallback:function(e){return t(e)}})},delRunqianResource:function(e,t){Base.submit(null,{url:l+"delRunqianResource",data:e},{successCallback:function(e){return t(e)}})}},c={name:"addTemplate",props:{visible:{type:Boolean},editType:{type:String},raqType:{type:String,default:""},record:{type:Object}},data:function(){return{form:null,checkAll:!0,formItemLayout:{labelCol:{span:6},wrapperCol:{span:18}}}},methods:{closeEdit:function(){this.$emit("close")},fnChooseFile:function(e){var t=e.srcElement?e.srcElement:e.target,a=t.files[0].name,i=a.substring(a.lastIndexOf(".")+1,a.length);if("raq"!==i)return this.$message.warning("请选择.raq结尾的报表文件"),void(this.$refs.file.value="");this.form.setFieldsValue({raqname:a})},submitEditForm:function(){var e=this;this.form.validateFields((function(t){if(!t){var a,i,r,o=e.form.getFieldsValue(),n=null===e||void 0===e||null===(a=e.$refs)||void 0===a||null===(i=a.file)||void 0===i||null===(r=i.$el)||void 0===r?void 0:r.files[0];if(null===n||void 0===n)return void e.$message.warning("请选择报表文件");var l=n.name,c=l.substring(l.lastIndexOf(".")+1,l.length);if("raq"!==c)return e.$message.warning("请选择.raq结尾的报表文件"),void(e.$refs.file.value="");o.theFile=n,s.editRunqianResource(o,(function(){e.$message.success("编辑成功"),e.$emit("close")}))}}))},submitAddForm:function(){var e=this;this.form.validateFields((function(t){if(!t){var a,i,r,o=e.form.getFieldsValue(),n=null===e||void 0===e||null===(a=e.$refs)||void 0===a||null===(i=a.file)||void 0===i||null===(r=i.$el)||void 0===r?void 0:r.files[0];if(null===n||void 0===n)return void e.$message.warning("请选择报表文件");var l=n.name,c=l.substring(l.lastIndexOf(".")+1,l.length);if("raq"!==c)return e.$message.warning("请选择.raq结尾的报表文件"),void(e.$refs.file.value="");o.theFile=n,s.addRunqianResource(o,(function(){e.$message.success("新增成功"),e.$emit("close")}))}}))}}},d=c,u=a(1001),f=(0,u.Z)(d,o,n,!1,null,"3d5989a2",null),p=f.exports,m=[{title:"报表标志",dataIndex:"raqfilename",width:"15%"},{title:"报表名称",dataIndex:"raqname",width:"15%"},{title:"父报表标志",dataIndex:"parentraqfilename",width:"10%"},{title:"报表类型",dataIndex:"raqtype",width:"10%"},{title:"上传人",dataIndex:"username",width:"10%"},{title:"上传时间",dataIndex:"uploadtime",width:"10%",overflowTooltip:!0},{title:"操作",dataIndex:"operate",align:"center",width:"340px",overflowTooltip:!1,scopedSlots:{customRender:"operate"}}],h={name:"RunqianPage",components:{addTemplate:p},data:function(){var e=this;return{param:"",tags:[],columns:m,operateMenu:[{name:"添加子表",onClick:function(t){e.openAddClidWin(t)}},{name:"编辑",onClick:function(t){e.openEditWin(t)}},{name:"下载",type:"confirm",confirmTitle:"确认下载?",onOk:function(t){e.confirmDownload(t)}},{name:"无参数预览",onClick:function(t){e.toPreviewPdf(t)}}],gridData:[],pageSize:10,editVisible:!1,editType:"",raqType:"",record:{},selectedRowKeys:[],selectedRows:[],printVisible:!1}},mounted:function(){this.searchQuery()},methods:{getCookie:function(e){var t,a=new RegExp("(^| )"+e+"=([^;]*)(;|$)");return(t=document.cookie.match(a))?unescape(t[2]):null},getToken:function(){var e=document.cookie,t=e.split("; "),a="";return t.forEach((function(e){-1!==e.indexOf("XSRF-TOKEN")&&(a=e.slice("XSRF-TOKEN".length+1))})),a},onSelectChange:function(e,t){this.selectedRowKeys=e,this.selectedRows=t},fnDelTem:function(){var e=this;if(0!==this.selectedRows.length){var t=[];this.selectedRows.forEach((function(e){t.push(e.raqfilename)})),s.delRunqianResource({raqfilenames:t.join(",")},(function(t){e.$message.success("成功删除"+t.data.rows+"条数据"),e.searchQuery(),e.selectedRows=[]}))}else this.$message.warning("需要至少选中一条数据")},closeWin:function(){this.editVisible=!1,this.searchQuery()},templatePageParams:function(){var e={};return e.raqfilename=this.param,e.raqtype=this.tags,e},searchQuery:function(){this.$refs.gridPager.loadData()},openAddWin:function(){this.editVisible=!0,this.editType="1",this.raqType="1",this.record={}},openEditWin:function(e){this.editVisible=!0,this.editType="2",this.record=e,this.raqType=e.raqtype},openAddClidWin:function(e){this.editVisible=!0,this.editType="1",this.record={},this.record.parentraqfilename=e.raqfilename,this.record.parentraqname=e.raqname,this.raqType="2"},confirmDownload:function(e){var t=faceConfig.basePath;location.href=t+"/tarunqianresource/taRunqianResourceRestService/downloadRunqianResource?raqfilename="+e.raqfilename+"&_modulePartId_="+TaUtils.getNowPageParam()._modulePartId_+"&X-XSRF-TOKEN="+this.getToken()+"&TA-JTOKEN="+this.getCookie(faceConfig.basePath+"TA-JTOKEN")},handlePrint:function(){this.$refs.pdfViewer.toPrint()},handleDownload:function(){this.$refs.pdfViewer.toDownload()},handleClosePrintPage:function(){this.printVisible=!1},toPreviewPdf:function(e){var t=this;this.printVisible=!0,this.$nextTick((function(){t.$refs.pdfViewer.showRaq(e.raqfilename,{})}))}}},g=h,b=(0,u.Z)(g,i,r,!1,null,"69c931f0",null),v=b.exports}}]);
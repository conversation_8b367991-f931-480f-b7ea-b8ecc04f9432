(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[3601],{44045:function(t,e,i){"use strict";i.r(e),i.d(e,{default:function(){return y}});var l=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"fit",attrs:{id:"importAndExport"}},[i("ta-border-layout",{attrs:{headerCfg:{expand:!0}}},[i("div",{attrs:{slot:"headerExtraContent"},slot:"headerExtraContent"},[i("h3",[i("b",[t._v("导入说明")])])]),i("div",{attrs:{slot:"header"},slot:"header"},[[i("p",{attrs:{slot:"description"},slot:"description"},[t._v(" 1.导入组织结构，导入文件格式参照《组织导入模板.xls》"),i("a",{on:{click:t.downloadOrg}},[t._v("（点此下载）")]),t._v("，上级组织、组织名称、组织类型和有效性不能为空，其余字段按照需要导入。 ")]),i("p",{attrs:{slot:"description"},slot:"description"},[t._v(" 2.导入人员和对应组织，导入文件格式参照《人员导入模板.xls》"),i("a",{on:{click:t.downloadUser}},[t._v("（点此下载）")]),t._v("，登录账号、姓名、锁定及有效标识不能为空，sheet结构需要与导入模板相同，若登录口令为空则以默认密码添加人员。 ")])]],2),i("div",{attrs:{slot:"centerExtraContent"},slot:"centerExtraContent"},[i("div",{attrs:{id:"upload"}},[i("ta-upload",{attrs:{name:"file",multiple:!0,"show-upload-list":!1,action:t.backUrl+"/org/orguser/importAndExportService/importBatchOrgExcel",accept:".xls"},on:{change:t.handleChange}},[i("ta-button",{attrs:{angle:"15px",type:"primary",disabled:t.isDisabled}},[t._v(" 导入组织 ")])],1),i("ta-upload",{attrs:{name:"file",multiple:!0,"show-upload-list":!1,action:t.backUrl+"/org/orguser/importAndExportService/importUserOrgInExcel",accept:".xls"},on:{change:t.userListChange}},[i("ta-button",{attrs:{angle:"15px",type:"success",disabled:t.isDisabled}},[t._v(" 导入人员 ")])],1)],1),i("div",{attrs:{id:"right"}},[i("ta-popconfirm",{attrs:{title:"确定导出全部组织?","cancel-text":"取消","ok-text":"确认"},on:{confirm:function(e){return t.exportAllOrg()}}},[i("ta-button",{attrs:{type:"primary"}},[t._v(" 导出全部组织 ")])],1),i("ta-popconfirm",{attrs:{title:"确定导出全部人员?","cancel-text":"取消","ok-text":"确认"},on:{confirm:function(e){return t.exportAllUser()}}},[i("ta-button",{attrs:{type:"primary"}},[t._v(" 导出全部人员 ")])],1)],1)]),[i("ta-tabs",{attrs:{type:"card"}},[i("ta-tab-pane",{key:"fail"},[i("span",{staticStyle:{color:"red"},attrs:{slot:"tab"},slot:"tab"},[t._v(" 本次导入失败的信息 "),i("ta-badge",{attrs:{count:t.failedCount}})],1),i("failed-table",{attrs:{orgData:t.orgData,userData:t.userData,userOrgData:t.userOrgData}})],1),i("ta-tab-pane",{key:"success"},[i("span",{staticStyle:{color:"#52c41a"},attrs:{slot:"tab"},slot:"tab"},[t._v(" 本次导入成功的信息 "),i("ta-badge",{attrs:{count:t.successCount,numberStyle:{backgroundColor:"#52c41a"}}})],1),i("ta-tabs",[i("ta-tab-pane",{key:"org",attrs:{tab:"组织信息"}},[i("ta-big-table",{attrs:{data:t.orgSuccess,height:"400",columns:t.orgColumns}})],1),i("ta-tab-pane",{key:"user",attrs:{tab:"人员信息"}},[i("ta-big-table",{attrs:{data:t.userSuccess,height:"400",columns:t.userColumns}})],1),i("ta-tab-pane",{key:"userOrg",attrs:{tab:"人员组织关系"}},[i("ta-big-table",{attrs:{data:t.userOrgSuccess,height:"400",columns:t.userOrgColumns}})],1)],1)],1),i("ta-button",{attrs:{slot:"tabBarExtraContent"},on:{click:t.downloadError},slot:"tabBarExtraContent"},[t._v(" 下载错误信息 ")])],1)]],2)],1)},a=[],r=(i(32564),i(22722)),o=i(96565),s=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("ta-tabs",[i("ta-tab-pane",{key:"org",attrs:{tab:"组织信息"}},[i("ta-big-table",{attrs:{"auto-resize":"",border:"",height:"400","export-config":{isPrint:!0},"import-config":{},data:t.orgData}},[i("ta-big-table-column",{attrs:{type:"seq",title:"序号",width:"60"}}),i("ta-big-table-column",{attrs:{field:"pNamePath",title:"上级组织",width:"200"}}),i("ta-big-table-column",{attrs:{field:"orgName",title:"组织名称",width:"200"}}),i("ta-big-table-column",{attrs:{field:"spell",title:"名称拼写",width:"150"}}),i("ta-big-table-column",{attrs:{field:"orgType",title:"组织类型","collection-type":"ORGTYPE",width:"100"}}),i("ta-big-table-column",{attrs:{field:"effective",title:"有效性","collection-type":"EFFECTIVE",width:"100"}}),i("ta-big-table-column",{attrs:{field:"customNo",title:"自定义编码",width:"100"}}),i("ta-big-table-column",{attrs:{field:"orderNo",title:"排序号",width:"100"}}),i("ta-big-table-column",{attrs:{field:"orgLevel",title:"组织层级",width:"100"}}),i("ta-big-table-column",{attrs:{field:"area",title:"行政区代码",width:"120"}}),i("ta-big-table-column",{attrs:{field:"orgManager",title:"组织负责人",width:"100"}}),i("ta-big-table-column",{attrs:{field:"orgCode",title:"组织代码",width:"100"}}),i("ta-big-table-column",{attrs:{field:"contacts",title:"单位联系人",width:"100"}}),i("ta-big-table-column",{attrs:{field:"address",title:"联系地址",width:"100"}}),i("ta-big-table-column",{attrs:{field:"tel",title:"联系电话",width:"100"}}),i("ta-big-table-column",{attrs:{field:"field01",title:"field01",width:"100"}}),i("ta-big-table-column",{attrs:{field:"field02",title:"field02",width:"100"}}),i("ta-big-table-column",{attrs:{field:"field03",title:"field03",width:"100"}}),i("ta-big-table-column",{attrs:{field:"field04",title:"field04",width:"100"}}),i("ta-big-table-column",{attrs:{field:"field05",title:"field05",width:"100"}}),i("ta-big-table-column",{attrs:{field:"field06",title:"field06",width:"100"}}),i("ta-big-table-column",{attrs:{field:"field07",title:"field07",width:"100"}}),i("ta-big-table-column",{attrs:{field:"field08",title:"field08",width:"100"}}),i("ta-big-table-column",{attrs:{field:"field09",title:"field09",width:"100"}}),i("ta-big-table-column",{attrs:{field:"field10",title:"field10",width:"100"}}),i("ta-big-table-column",{attrs:{field:"errorMsg",title:"错误信息",fixed:"right",width:"300","show-overflow":""}})],1)],1),i("ta-tab-pane",{key:"user",attrs:{tab:"人员信息"}},[i("ta-big-table",{attrs:{"auto-resize":"",border:"",height:"400","export-config":{isPrint:!0},"import-config":{},data:t.userData}},[i("ta-big-table-column",{attrs:{type:"seq",title:"序号",width:"60"}}),i("ta-big-table-column",{attrs:{field:"loginId",title:"登陆账号",width:"100"}}),i("ta-big-table-column",{attrs:{field:"name",title:"姓名",width:"100"}}),i("ta-big-table-column",{attrs:{field:"password",title:"登录口令",width:"100","show-overflow":""}}),i("ta-big-table-column",{attrs:{field:"sex",title:"性别","collection-type":"SEX",width:"100"}}),i("ta-big-table-column",{attrs:{field:"isLock",title:"锁定","collection-type":"YESORNO",width:"100"}}),i("ta-big-table-column",{attrs:{field:"effective",title:"有效性","collection-type":"EFFECTIVE",width:"100"}}),i("ta-big-table-column",{attrs:{field:"passwordDefaultNum",title:"密码错误次数",width:"200"}}),i("ta-big-table-column",{attrs:{field:"pwdLastModifyDate",title:"密码最后修改时间",width:"200"}}),i("ta-big-table-column",{attrs:{field:"orderNo",title:"排序号",width:"100"}}),i("ta-big-table-column",{attrs:{field:"idCardType",title:"证件类型",width:"100"}}),i("ta-big-table-column",{attrs:{field:"idCardNo",title:"证件号码",width:"120"}}),i("ta-big-table-column",{attrs:{field:"mobile",title:"手机号码",width:"100"}}),i("ta-big-table-column",{attrs:{field:"accountSource",title:"注册渠道",width:"100"}}),i("ta-big-table-column",{attrs:{field:"effectiveTime",title:"有效时间",width:"100"}}),i("ta-big-table-column",{attrs:{field:"jobNumber",title:"工号",width:"100"}}),i("ta-big-table-column",{attrs:{field:"state",title:"国家地区",width:"100"}}),i("ta-big-table-column",{attrs:{field:"birthplace",title:"户籍地",width:"100"}}),i("ta-big-table-column",{attrs:{field:"address",title:"联系地址",width:"100"}}),i("ta-big-table-column",{attrs:{field:"zipCode",title:"邮政编码",width:"100"}}),i("ta-big-table-column",{attrs:{field:"email",title:"邮箱地址",width:"100"}}),i("ta-big-table-column",{attrs:{field:"phone",title:"联系电话",width:"100"}}),i("ta-big-table-column",{attrs:{field:"education",title:"学历",width:"100"}}),i("ta-big-table-column",{attrs:{field:"graduateSchool",title:"毕业学校",width:"100"}}),i("ta-big-table-column",{attrs:{field:"workplace",title:"工作单位",width:"100"}}),i("ta-big-table-column",{attrs:{field:"field01",title:"field01",width:"100"}}),i("ta-big-table-column",{attrs:{field:"field02",title:"field02",width:"100"}}),i("ta-big-table-column",{attrs:{field:"field03",title:"field03",width:"100"}}),i("ta-big-table-column",{attrs:{field:"field04",title:"field04",width:"100"}}),i("ta-big-table-column",{attrs:{field:"field05",title:"field05",width:"100"}}),i("ta-big-table-column",{attrs:{field:"field06",title:"field06",width:"100"}}),i("ta-big-table-column",{attrs:{field:"field07",title:"field07",width:"100"}}),i("ta-big-table-column",{attrs:{field:"field08",title:"field08",width:"100"}}),i("ta-big-table-column",{attrs:{field:"field09",title:"field09",width:"100"}}),i("ta-big-table-column",{attrs:{field:"field10",title:"field10",width:"100"}}),i("ta-big-table-column",{attrs:{field:"errorMsg",title:"错误信息",fixed:"right",width:"300","show-overflow":""}})],1)],1),i("ta-tab-pane",{key:"userOrg",attrs:{tab:"人员组织关系"}},[i("ta-big-table",{attrs:{border:"",height:"400","export-config":{isPrint:!0},"import-config":{},data:t.userOrgData,"auto-resize":""}},[i("ta-big-table-column",{attrs:{type:"seq",title:"序号",width:"60"}}),i("ta-big-table-column",{attrs:{field:"loginId",title:"帐号"}}),i("ta-big-table-column",{attrs:{field:"name",title:"姓名"}}),i("ta-big-table-column",{attrs:{field:"orgName",title:"组织"}}),i("ta-big-table-column",{attrs:{field:"namePath",title:"组织路径"}}),i("ta-big-table-column",{attrs:{field:"errorMsg",title:"错误信息",fixed:"right",width:"300","show-overflow":""}})],1)],1)],1)},d=[],n={props:["orgData","userData","userOrgData"],data:function(){return{}}},c=n,f=i(1001),u=(0,f.Z)(c,s,d,!1,null,null,null),g=u.exports,b=[{title:"序号",type:"seq",width:"100"},{title:"上级组织",field:"pNamePath",width:"200"},{title:"组织名称",field:"orgName",width:"200"},{title:"名称拼写",field:"spell",width:"200"},{title:"组织类型",field:"orgType",width:"200","collection-type":"ORGTYPE"},{title:"有效性",field:"effective",width:"200","collection-type":"EFFECTIVE"}],h=[{title:"序号",type:"seq",width:"100"},{title:"登陆账号",field:"loginId",width:"200"},{title:"姓名",field:"name",width:"200"},{title:"性别",field:"sex",width:"200","collection-type":"SEX"},{title:"锁定",field:"isLock",width:"80","collection-type":"YESORNO"},{title:"有效性",field:"effective",width:"80","collection-type":"EFFECTIVE"}],m=[{title:"序号",type:"seq",width:"100"},{title:"帐号",field:"loginId",width:"150"},{title:"姓名",field:"name",width:"150"},{title:"组织",field:"orgName",width:"150"},{title:"组织路径",field:"namePath",width:"150"}],p={components:{failedTable:g},name:"importAndExport",data:function(){return{backUrl:"",failedCount:0,successCount:0,userColumns:h,orgColumns:b,userOrgColumns:m,isDisabled:!1,orgData:[],userData:[],userOrgData:[],orgSuccess:[],userSuccess:[],userOrgSuccess:[],orgXlsUrl:"org/orguser/importAndExportService/exportAllOrg",userXlsUrl:"org/orguser/importAndExportService/exportAllUser"}},mounted:function(){this.backUrl=faceConfig.basePath},methods:{handleChange:function(t){var e=this;"done"===t.file.status?(message.info("上传成功!!!添加了‘"+t.file.response.data.map.count+"’条数据"),this.userData=[],this.userOrgData=[],this.userSuccess=[],this.userOrgSuccess=[],this.orgData=t.file.response.data.map.errorList,this.orgSuccess=t.file.response.data.map.successList,this.successCount=t.file.response.data.map.count,this.failedCount=this.orgData.length):t.file.status,setTimeout((function(){e.isDisabled=!1}),3e3)},userListChange:function(t){var e=this;"done"===t.file.status?(message.info("上传成功!!!添加了‘"+t.file.response.data.map.count+"’条数据"),this.orgData=[],this.orgSuccess=[],this.userData=t.file.response.data.map.wrongUserList,this.userOrgData=t.file.response.data.map.wrongOrgList,this.userSuccess=t.file.response.data.map.userList,this.userOrgSuccess=t.file.response.data.map.orgList,this.successCount=t.file.response.data.map.count,this.failedCount=this.userData.length+this.userOrgData.length):t.file.status,setTimeout((function(){e.isDisabled=!1}),3e3)},downloadError:function(){if(0!==this.orgData.length||0!==this.userData.length||0!==this.userOrgData.length){var t={};0!==this.orgData.length?t={fileName:"组织错误信息",fileType:".xls",sheets:[{rows:this.orgData,sheetName:"Sheet1",column:{complex:!0,col:["pNamePath","orgName","spell","orgType","effective","customNo","orderNo","orgLevel","orgManager","area","orgCode","address","contacts","tel","field01","field02","field03","field04","field05","field06","field07","field08","field09","field10","errorMsg"],columns:[{pNamePath:"组织信息"},{pNamePath:"上级组织",orgName:"组织名称",spell:"名称拼写",orgType:"组织类型",effective:"有效性",customNo:"自定义编码",orderNo:"排序号",orgLevel:"组织层级",area:"行政区代码",orgManager:"组织负责人",orgCode:"组织代码",contacts:"单位联系人",address:"联系地址",tel:"联系电话",field01:"field01",field02:"field02",field03:"field03",field04:"field04",field05:"field05",field06:"field06",field07:"field07",field08:"field08",field09:"field09",field10:"field10",errorMsg:"错误信息"}],exclude:["createTime","createUser","destory","idPath","modifyTime","namePath","orgId","parentId","rowNum","_XID"]},merges:[{s:{c:0,r:0},e:{c:24,r:0}}]}]}:0===this.userData.length&&0===this.userOrgData.length||(t={fileName:"人员及组织错误信息",fileType:".xls",sheets:[{rows:this.userData,sheetName:"人员信息",column:{complex:!0,col:["loginId","name","password","sex","isLock","effective","passwordDefaultNum","pwdLastModifyDate","orderNo","idCardType","idCardNo","mobile","accountSource","effectiveTime","jobNumber","state","birthplace","address","zipCode","email","phone","education","graduateSchool","workplace","field01","field02","field03","field04","field05","field06","field07","field08","field09","field10","errorMsg"],columns:[{loginId:"人员基本信息"},{loginId:"登陆账号",name:"姓名",password:"登录口令",sex:"性别",isLock:"锁定",effective:"有效性",passwordDefaultNum:"密码错误次数",pwdLastModifyDate:"密码最后修改时间",orderNo:"排序号",idCardType:"证件类型",idCardNo:"证件号码",mobile:"手机号码",accountSource:"注册渠道",effectiveTime:"有效时间",jobNumber:"工号",state:"国家地区",birthplace:"户籍地",address:"联系地址",zipCode:"邮政编码",email:"邮箱地址",phone:"联系电话",education:"学历",graduateSchool:"毕业学校",workplace:"工作单位",field01:"field01",field02:"field02",field03:"field03",field04:"field04",field05:"field05",field06:"field06",field07:"field07",field08:"field08",field09:"field09",field10:"field10",errorMsg:"错误信息"}],exclude:["createTime","createUser","rowNum","_XID","userId"]},merges:[{s:{c:0,r:0},e:{c:34,r:0}}]},{rows:this.userOrgData,sheetName:"人员组织关系",column:{complex:!0,col:["loginId","name","orgName","namePath","errorMsg"],columns:[{loginId:"人员组织关系"},{loginId:"账号",name:"姓名",orgName:"组织名称",namePath:"组织路径",errorMsg:"错误信息"}],exclude:["rowNum","_XID"]},merges:[{s:{c:0,r:0},e:{c:4,r:0}}]}]}),r.Z.generateExcelByURL(t)}else message.info("未产生错误信息，无法导出")},downloadOrg:function(){(0,o.Z)({url:"org/orguser/importAndExportService/downloadOrgFIle",type:"application/xls",fileName:"组织导入模板.xls"}).then((function(t){})).catch((function(t){}))},downloadUser:function(){(0,o.Z)({url:"org/orguser/importAndExportService/downloadUserFIle",type:"application/xls",fileName:"人员导入模板.xls"}).then((function(t){})).catch((function(t){}))},exportAllOrg:function(){(0,o.Z)({url:this.orgXlsUrl,type:"application/xls",fileName:"组织信息.xls"}).then((function(t){})).catch((function(t){alert("导出组织信息失败")}))},exportAllUser:function(){(0,o.Z)({url:this.userXlsUrl,type:"application/xls",fileName:"人员信息.xls"}).then((function(t){})).catch((function(t){alert("导出人员信息失败")}))}}},w=p,x=(0,f.Z)(w,l,a,!1,null,"97843dd2",null),y=x.exports},55382:function(){},61219:function(){}}]);
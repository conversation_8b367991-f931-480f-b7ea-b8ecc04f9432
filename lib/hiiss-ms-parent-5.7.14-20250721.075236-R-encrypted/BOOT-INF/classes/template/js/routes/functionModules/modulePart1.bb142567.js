"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[2290],{13783:function(e,t,a){a.r(t),a.d(t,{default:function(){return f}});for(var r=function(){var e=this,t=this,a=t.$createElement,r=t._self._c||a;return r("div",[r("ta-form",{attrs:{autoFormCreate:function(t){e.form=t}}},[r("ta-form-item",{attrs:{label:"邮箱",fieldDecoratorId:"email"}},[r("ta-input",{attrs:{placeholder:""}})],1),r("ta-form-item",{attrs:{label:"邮箱",fieldDecoratorId:"cascader"}},[r("ta-cascader",{attrs:{options:t.options,placeholder:"Please select"}})],1),r("ta-form-item",{attrs:{label:"邮箱",fieldDecoratorId:"checkbox"}},[r("ta-checkbox-group",{attrs:{"collection-type":"SEX"}})],1),r("ta-form-item",{attrs:{label:"邮箱",fieldDecoratorId:"dateTimePicker"}},[r("ta-date-picker",{attrs:{mode:"time",format:"YYYY-MM-DD HH:mm:ss",showTime:""}})],1),r("ta-form-item",{attrs:{label:"邮箱",fieldDecoratorId:"inputNumber"}},[r("ta-input-number",{attrs:{min:1,max:10}})],1),r("ta-form-item",{attrs:{label:"邮箱",fieldDecoratorId:"radio"}},[r("ta-radio-group",{attrs:{"collection-type":"sex","collection-filter":"0,2",reverseFilter:!0}})],1),r("ta-form-item",{attrs:{label:"邮箱",fieldDecoratorId:"select"}},[r("ta-select",{staticStyle:{width:"120px"},attrs:{"collection-type":"YESORNO"}})],1),r("ta-form-item",{attrs:{label:"邮箱",fieldDecoratorId:"switch"}},[r("ta-switch")],1),r("ta-form-item",{attrs:{label:"邮箱",fieldDecoratorId:"timePicker"}},[r("ta-time-picker",{attrs:{use12Hours:"",format:"h:mm:ss"}})],1),r("ta-form-item",{attrs:{label:"邮箱",fieldDecoratorId:"treeSelect"}},[r("ta-tree-select",{staticStyle:{width:"300px"},attrs:{showSearch:"",dropdownStyle:{maxHeight:"400px",overflow:"auto"},placeholder:"Please select",allowClear:"",treeDefaultExpandAll:""}},[r("ta-tree-select-node",{key:"0-1",attrs:{value:"parent 1",title:"parent 1"}},[r("ta-tree-select-node",{key:"0-1-1",attrs:{value:"parent 1-0",title:"parent 1-0"}},[r("ta-tree-select-node",{key:"random",attrs:{value:"leaf1",title:"my leaf"}}),r("ta-tree-select-node",{key:"random1",attrs:{value:"leaf2",title:"your leaf"}})],1),r("ta-tree-select-node",{key:"random2",attrs:{value:"parent 1-1",title:"parent 1-1"}},[r("ta-tree-select-node",{key:"random3",attrs:{value:"sss"}},[r("b",{staticStyle:{color:"#08c"},attrs:{slot:"title"},slot:"title"},[t._v("sss")])])],1)],1)],1)],1)],1),r("ta-table",{attrs:{columns:t.columns,dataSource:t.data,scroll:{x:1500}},scopedSlots:t._u([{key:"operate",fn:function(e,a){return r("span",{},[r("ta-table-operate",{attrs:{operateMenu:t.operateMenu}})],1)}}])}),r("ta-button",{on:{click:function(e){return t.savePage()}}},[t._v("保存页面")])],1)},n=[],l=a(83999),o=[{title:"Full Name",width:100,dataIndex:"name",key:"name",fixed:"left"},{title:"Age",width:100,dataIndex:"age",key:"age",fixed:"left"},{title:"Column 1",dataIndex:"address",key:"1",width:150},{title:"Column 2",dataIndex:"address",key:"2",width:150},{title:"Column 3",dataIndex:"address",key:"3",width:150},{title:"Column 4",dataIndex:"address",key:"4",width:150},{title:"Column 5",dataIndex:"address",key:"5",width:150},{title:"Column 6",dataIndex:"address",key:"6",width:150},{title:"Column 7",dataIndex:"address",key:"7",width:150},{title:"Column 8",dataIndex:"address",key:"8"},{title:"operate",dataIndex:"operate",fixed:"right",width:100,scopedSlots:{customRender:"operate"}}],i=[],s=0;s<5;s++)i.push({key:s,name:"Edrward ".concat(s),age:32,address:"London Park no. ".concat(s)});var d={components:{},mixins:[l.Z],data:function(){var e=this;return{tagData:[{value:"0",label:"机构"},{value:"1",label:"部门"},{value:"2",label:"组"}],options:[{value:"zhejiang",label:"Zhejiang",children:[{value:"hangzhou",label:"Hangzhou",children:[{value:"xihu",label:"West Lake"}]}]},{value:"jiangsu",label:"Jiangsu",children:[{value:"nanjing",label:"Nanjing",children:[{value:"zhonghuamen",label:"Zhong Hua Men"}]}]}],data:i,columns:o,operateMenu:[{name:"action",onClick:function(t){e.$message.info(JSON.stringify(t))}}]}},methods:{savePage:function(){this.saveRePageData().then((function(e){localStorage.pageId=e}))}}},u=d,c=a(1001),m=(0,c.Z)(u,r,n,!1,null,null,null),f=m.exports},83999:function(e,t,a){var r=a(95082),n=a(3336),l=a(48534),o=(a(36133),a(3032)),i={mounted:function(){var e=this,t=TaUtils.getNowPageParam(),a=t.rePageId;a&&this.Base.submit(null,{url:"review/getPageData",data:{pageId:a}}).then((function(t){e.setRePageData(JSON.parse(t.data.data))}))},methods:{saveRePageData:function(){var e=this;return(0,l.Z)(regeneratorRuntime.mark((function t(){var a,r,l,o;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a="",r=e.$data||{},l={},o=Object.keys(e).filter((function(t){return!("_renderProxy"===t||!e[t]||"object"!==(0,n.Z)(e[t])||!e[t].getFieldsMomentValue)})),o.map((function(t){l[t]=e[t].getFieldsMomentValue()})),t.next=7,e.Base.submit(null,{url:"/review/savePage",data:{data:JSON.stringify({reData:r,reForm:l})}}).then((function(t){a=t.data.pageId,e.$message.success("保存成功")})).catch((function(t){e.message.error("保存失败")}));case 7:return t.abrupt("return",a);case 8:case"end":return t.stop()}}),t)})))()},setRePageData:function(e){var t=this,a=e.reData,n=e.reForm;Object.keys(a).map((function(e){t[e]=a[e]})),Object.keys(n).map((function(e){t[e].setFieldsMomentValue((0,r.Z)({},n[e]))}))},getRePageUrl:function(e){var t=this;return(0,l.Z)(regeneratorRuntime.mark((function a(){var r;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return r="",a.next=3,t.Base.submit(null,{url:"review/getPageUrl",data:{pageId:e}}).then((function(t){r=t.data.frontUrl,r.indexOf("?")>-1?r+="&rePageId="+e:r+="?rePageId="+e}));case 3:return a.abrupt("return",r);case 4:case"end":return a.stop()}}),a)})))()}}};o["default"].prototype.setDisabled=function(e){e.disabled=!0},o["default"].mixin({mounted:function(){this.setDisabled&&TaUtils.getNowPageParam()&&TaUtils.getNowPageParam().rePageId&&this.setDisabled(this)}}),t["Z"]=i}}]);
"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[3521],{79188:function(e,t,a){a.r(t),a.d(t,{default:function(){return x}});var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"fit",attrs:{id:"accessDenyLog"}},[a("ta-border-layout",{staticStyle:{padding:"10px"},attrs:{"layout-type":"flexBorder"}},[a("div",{staticClass:"center-box",attrs:{slot:"header"},slot:"header"},[a("ta-input-search",{staticClass:"search-box",attrs:{placeholder:"操作用户搜索","enter-button":"搜索"},on:{search:e.searchQuery},model:{value:e.param,callback:function(t){e.param=t},expression:"param"}})],1),a("ta-border-layout",{attrs:{"layout-type":"flexBorder"}},[a("div",{staticStyle:{"line-height":"40px"},attrs:{slot:"header"},slot:"header"},[a("ta-range-picker",{staticClass:"distance",attrs:{placeholder:["开始时间","结束时间"],"get-calendar-container":e.getContainer},on:{change:e.onChange},model:{value:e.createTime,callback:function(t){e.createTime=t},expression:"createTime"}}),a("ta-tag-select",{attrs:{title:"限制类型",data:e.CollectionData("ACCESSDENYTYPE"),"is-multi":!0},on:{change:e.searchQuery},model:{value:e.accessDenyTypeParam,callback:function(t){e.accessDenyTypeParam=t},expression:"accessDenyTypeParam"}}),a("div",{staticStyle:{float:"right"}},[a("ta-button",{attrs:{icon:"area-chart"},on:{click:function(t){return e.showChart()}}}),a("ta-button",{on:{click:function(t){return e.exportData()}}},[e._v(" 导出 ")])],1)],1),a("ta-table",{attrs:{columns:e.columns,"data-source":e.gridData,pagination:!1,scroll:{y:"100%"}},scopedSlots:e._u([{key:"accessDenyType",fn:function(t){return a("span",{},[e._v(e._s(e.CollectionLabel("ACCESSDENYTYPE",t)))])}},{key:"userName",fn:function(t){return a("span",{},[e._v(e._s(null==t?"匿名用户":t))])}}])})],1),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("ta-pagination",{ref:"onlineGridPager",staticStyle:{float:"right","margin-top":"10px"},attrs:{"show-size-changer":"","show-quick-jumper":"","data-source":e.gridData,params:e.pageParams,url:"logMg/accessLog/taAccessDenyLogRestService/queryAccessDenyLog"},on:{"update:dataSource":function(t){e.gridData=t},"update:data-source":function(t){e.gridData=t}}})],1)],1),a("ta-modal",{attrs:{centered:"",width:"1000px","body-style":{height:"600px",padding:"0"},footer:null,"destroy-on-close":!0,"mask-closable":!0,title:"实时分析跟踪",closable:!0},on:{close:function(t){return e.closeChart(!1)}},model:{value:e.showChartVisible,callback:function(t){e.showChartVisible=t},expression:"showChartVisible"}},[a("access-deny-chart",{ref:"accessDenyChart",on:{closeModal:e.closeChart}})],1)],1)},n=[],i=a(36797),r=a.n(i),o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("ta-e-charts",{attrs:{height:"500px",title:e.chartSetting.title,legend:e.chartSetting.legend,tooltip:e.chartSetting.tooltip,xAxis:e.chartSetting.xAxis,yAxis:e.chartSetting.yAxis,series:e.chartSetting.series,toolbox:e.chartSetting.toolbox}})],1)},c=[],l=(a(32564),a(99440)),d="logMg/accessLog/taAccessDenyLogRestService/",u={getAnalysisAccessDenyInfo:function(e,t){Base.submit(null,{url:d+"analysisAccessDenyInfoInterval",data:e},{successCallback:function(e){return t(e.data)}})}},h={name:"accessDenyChart",components:{TaECharts:l.Z},data:function(){var e=this;return{collectionList:["ACCESSDENYTYPE"],startTime:null,chartSetting:{legend:{top:10},tooltip:{trigger:"axis",formatter:function(t){t=t[0];var a=t.name.split(",");return a[0]="受限类型: "+e.CollectionLabel("ACCESSDENYTYPE",a[0]),a[1]="访问时间: "+a[1],a[2]="受限次数: "+a[2],a.join("<br/>")},axisPointer:{animation:!1}},xAxis:{type:"time",splitLine:{show:!1}},yAxis:{type:"value",boundaryGap:[0,"100%"],splitLine:{show:!1}},toolbox:{feature:{magicType:{type:["line","bar"]},saveAsImage:{}}},series:[]}}},created:function(){this.init()},destroyed:function(){window.clearInterval(window.analysisChartInterval)},methods:{init:function(){var e=this;Base.asyncGetCodeData("ACCESSDENYTYPE").then((function(t){e.chartSetting.series=t.map((function(e){return{id:e.value,name:e.label,type:"line",showSymbol:!1,hoverAnimation:!1,data:[]}})),e.startAnalysis()}))},startAnalysis:function(){var e=this;this.httpAnalysis(),window.analysisChartInterval=setInterval((function(){e.httpAnalysis()}),1e4)},httpAnalysis:function(){var e=this;u.getAnalysisAccessDenyInfo({startTime:this.startTime},(function(t){e.startTime=t.analysisData.startTime;var a=t.analysisData.accessDenyAnalysisData||[];a.forEach((function(t){var a=e.chartSetting.series.find((function(e){return e.id==t.accessDenyType}));a.data.push({name:[t.accessDenyType,t.accessAnalysisTime,t.count].join(","),value:[t.accessAnalysisTime,t.count]})}))}))}}},p=h,m=a(1001),y=(0,m.Z)(p,o,c,!1,null,null,null),f=y.exports,D=a(55437),g=[{title:"操作用户",dataIndex:"userName",width:"9%",scopedSlots:{customRender:"userName"}},{title:"限制类型",dataIndex:"accessDenyType",width:"9%",overflowTooltip:!0,scopedSlots:{customRender:"accessDenyType"}},{title:"访问URL",dataIndex:"url",width:"9%",overflowTooltip:!0,scopedSlots:{customRender:"url"}},{title:"访问时间",dataIndex:"accessTime",width:"9%",overflowTooltip:!0,scopedSlots:{customRender:"accessTime"}},{title:"客户端IP",dataIndex:"clientIp",width:"10%",scopedSlots:{customRender:"clientIp"}},{title:"服务端IP",dataIndex:"serverIp",width:"10%",scopedSlots:{customRender:"serverIp"}},{title:"客户端系统",dataIndex:"clientSystem",width:"10%",scopedSlots:{customRender:"clientSystem"}},{title:"客户端浏览器",dataIndex:"clientBrowser",width:"11%",scopedSlots:{customRender:"clientBrowser"}},{title:"浏览器分辨率",dataIndex:"clientScreenSize",width:"11%",scopedSlots:{customRender:"clientScreenSize"}},{title:"数据是否被篡改",dataIndex:"isTampered",scopedSlots:{customRender:"isTampered"}}],v={name:"accessDenyLog",components:{accessDenyChart:f},data:function(){return{columns:g,onlineDate:null,onlineStartTime:null,onlineEndTime:null,createTime:[],gridData:[],param:"",startDate:"",endDate:"",showChartVisible:!1,accessDenyTypeParam:[]}},mounted:function(){this.loadData()},methods:{moment:r(),pageParams:function(){var e={};return e.startDate=this.startDate,e.endDate=this.endDate,e.userName=this.param,e.accessDenyTypeArray=this.accessDenyTypeParam,e},onChange:function(e,t){t&&t.length&&""!==t[0]&&""!==t[1]?(this.startDate=r()(t[0]).format("YYYY-MM-DD")+" 00:00:00",this.endDate=r()(t[1]).format("YYYY-MM-DD")+" 23:59:59"):(this.startDate="",this.endDate=""),this.loadData()},loadData:function(){this.$refs.onlineGridPager.loadData()},searchQuery:function(){this.loadData()},onSelectChange:function(e,t){this.selectedRowKeys=e},closeChart:function(e){this.showChartVisible=!1},showChart:function(){this.showChartVisible=!0},exportData:function(){var e={userName:this.param,startDate:this.startDate,endDate:this.endDate,accessDenyTypeArray:this.accessDenyTypeParam};D.Z.exportExcel(e,"audit/auditExportRestService/accessDeny")},getContainer:function(){return document.getElementById("accessDenyLog")}}},S=v,w=(0,m.Z)(S,s,n,!1,null,"d93e8426",null),x=w.exports},55437:function(e,t){t["Z"]={exportExcel:function(e,t){Base.submit(null,{url:t,data:e,responseType:"blob"}).then((function(e){var t=new Blob([e.data],{type:"application/xlsx;charset=utf-8"}),a=new FileReader;a.readAsText(t,"utf-8"),a.onload=function(){try{var s=JSON.parse(a.result);null!=s.errors&&parent.window.message.error("下载失败!")}catch(r){if(window.navigator.msSaveBlob)window.navigator.msSaveBlob(t,unescape(e.headers.filename));else{var n=document.createElement("a"),i=window.URL.createObjectURL(t);n.href=i,n.download=unescape(e.headers.filename),document.body.appendChild(n),n.click(),document.body.removeChild(n),window.URL.revokeObjectURL(i)}}}}))}}}}]);
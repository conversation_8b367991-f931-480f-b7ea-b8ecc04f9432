"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[6183],{56954:function(t,e,a){a.r(e),a.d(e,{default:function(){return C}});var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"fit",attrs:{id:"loginFailLog"}},[a("ta-border-layout",{attrs:{layout:{header:"70px",footer:"70px"},centerCfg:{showBar:!0,toolBarStyle:{height:"50px",lineHeight:"48px"}},"footer-cfg":{showBorder:!1}}},[a("div",{staticClass:"center-box",attrs:{slot:"header"},slot:"header"},[a("ta-input-search",{staticClass:"search-box",attrs:{placeholder:"登录账号搜索",enterButton:"搜索"},on:{search:t.searchQuery},model:{value:t.param,callback:function(e){t.param=e},expression:"param"}})],1),a("div",{attrs:{slot:"centerExtraContent"},slot:"centerExtraContent"},[a("ta-range-picker",{staticClass:"distance",attrs:{placeholder:["开始时间","结束时间"],"get-calendar-container":t.getContainer},on:{change:t.onChange},model:{value:t.createTime,callback:function(e){t.createTime=e},expression:"createTime"}}),a("div",{staticStyle:{float:"right"}},[a("ta-button",{attrs:{icon:"area-chart"},on:{click:function(e){return t.showChart()}}}),a("ta-button",{on:{click:function(e){return t.exportData()}}},[t._v("导出")])],1)],1),a("ta-table",{attrs:{columns:t.columns,dataSource:t.gridData,pagination:!1}}),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("ta-pagination",{ref:"onlineGridPager",staticStyle:{float:"right","margin-top":"10px"},attrs:{showSizeChanger:"",showQuickJumper:"",dataSource:t.gridData,params:t.pageParams,url:"logMg/loginFailLog/taLoginFailLogRestService/queryLoginFailLog"},on:{"update:dataSource":function(e){t.gridData=e},"update:data-source":function(e){t.gridData=e}}})],1)],1),a("ta-modal",{attrs:{centered:"",width:"1000px",bodyStyle:{height:"600px",padding:"0"},footer:null,destroyOnClose:!0,maskClosable:!0,title:"登录失败-日期图",closable:!0},on:{close:function(e){return t.closeChart(!1)}},model:{value:t.showChartVisible,callback:function(e){t.showChartVisible=e},expression:"showChartVisible"}},[a("login-fail-chart",{ref:"loginFailChart",attrs:{queryData:t.queryData},on:{closeModal:t.closeChart}})],1)],1)},o=[],i=a(36797),r=a.n(i),s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{height:"100%","overflow-x":"auto"}},[a("div",{staticStyle:{padding:"30px 30px 0 50px"}},[a("ta-e-charts",{attrs:{chartsType:"chart",data:t.chartData,title:t.title,settings:t.chartSettings,"data-empty":t.dataEmpty,extend:t.chartExtend}})],1)])},l=[],c=a(99440),d=(a(39551),{loginFailChart:function(t,e){Base.submit(null,{url:"logMg/loginFailLog/taLoginFailLogRestService/loginFailChart",data:t},{successCallback:function(t){return e(t)}})}}),h={name:"systemExceptionChart",props:["queryData"],data:function(){return{chartData:{columns:["日期","登录失败次数"],rows:[]},chartSettings:{type:"line",area:!0},dataEmpty:!1,title:{subtext:"默认显示最近7天"},chartExtend:{series:{smooth:!1}}}},components:{TaECharts:c.Z},mounted:function(){this.setValue()},methods:{setValue:function(){var t=this;this.dataEmpty=!1,this.chartData.rows=[],d.loginFailChart(this.queryData,(function(e){for(var a in 0==Object.keys(e.data.loginFailChartData).length&&(t.dataEmpty=!0),e.data.loginFailChartData){var n=[];n["日期"]=a,n["登录失败次数"]=e.data.loginFailChartData[a],t.chartData.rows.push(n)}}))}}},u=h,p=a(1001),m=(0,p.Z)(u,s,l,!1,null,null,null),g=m.exports,f=a(55437),D=[{title:"登录账号",dataIndex:"loginId",width:"10%",scopedSlots:{customRender:"loginId"}},{title:"登录时间",dataIndex:"loginTime",width:"15%",overflowTooltip:!0,scopedSlots:{customRender:"loginTime"}},{title:"登录失败原因",dataIndex:"failReason",width:"15%",overflowTooltip:!0,scopedSlots:{customRender:"failReason"}},{title:"客户端IP",dataIndex:"clientIp",width:"10%",scopedSlots:{customRender:"clientIp"}},{title:"服务端IP",dataIndex:"serverIp",width:"10%",scopedSlots:{customRender:"serverIp"}},{title:"客户端系统",dataIndex:"clientSystem",width:"10%",scopedSlots:{customRender:"clientSystem"}},{title:"客户端浏览器",dataIndex:"clientBrowser",width:"10%",scopedSlots:{customRender:"clientBrowser"}},{title:"浏览器分辨率",dataIndex:"clientScreenSize",width:"15%",scopedSlots:{customRender:"clientScreenSize"}},{title:"系统路径",dataIndex:"sysPath",width:"10%",scopedSlots:{customRender:"sysPath"}},{title:"数据是否被篡改",dataIndex:"isTampered",width:"7%",scopedSlots:{customRender:"isTampered"}}],w={name:"app",data:function(){return{columns:D,onlineDate:null,onlineStartTime:null,onlineEndTime:null,createTime:[],gridData:[],param:"",startDate:"",endDate:"",showChartVisible:!1,accessDenyTypeParam:"",queryData:[]}},components:{loginFailChart:g},mounted:function(){this.loadData()},methods:{moment:r(),pageParams:function(){var t={};return t.startDate=this.startDate,t.endDate=this.endDate,t.loginId=this.param,t},onChange:function(t,e){e&&e.length&&""!=e[0]&&""!=e[1]?(this.startDate=r()(e[0]).format("YYYY-MM-DD")+" 00:00:00",this.endDate=r()(e[1]).format("YYYY-MM-DD")+" 23:59:59"):(this.startDate="",this.endDate=""),this.loadData()},loadData:function(){this.$refs.onlineGridPager.loadData()},searchQuery:function(){this.loadData()},onSelectChange:function(t,e){this.selectedRowKeys=t},closeChart:function(t){this.showChartVisible=!1},showChart:function(){this.queryData={startDate:this.startDate,endDate:this.endDate,loginId:this.param},this.showChartVisible=!0},exportData:function(){var t={startDate:this.startDate,endDate:this.endDate,loginId:this.param};f.Z.exportExcel(t,"audit/auditExportRestService/loginFail")},getContainer:function(){return document.getElementById("loginFailLog")}}},x=w,y=(0,p.Z)(x,n,o,!1,null,"0ab0cee6",null),C=y.exports},55437:function(t,e){e["Z"]={exportExcel:function(t,e){Base.submit(null,{url:e,data:t,responseType:"blob"}).then((function(t){var e=new Blob([t.data],{type:"application/xlsx;charset=utf-8"}),a=new FileReader;a.readAsText(e,"utf-8"),a.onload=function(){try{var n=JSON.parse(a.result);null!=n.errors&&parent.window.message.error("下载失败!")}catch(r){if(window.navigator.msSaveBlob)window.navigator.msSaveBlob(e,unescape(t.headers.filename));else{var o=document.createElement("a"),i=window.URL.createObjectURL(e);o.href=i,o.download=unescape(t.headers.filename),document.body.appendChild(o),o.click(),document.body.removeChild(o),window.URL.revokeObjectURL(i)}}}}))}}}}]);
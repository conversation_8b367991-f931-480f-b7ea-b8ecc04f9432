"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[7267],{39576:function(e,t,a){a.r(t),a.d(t,{default:function(){return b}});var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"fit",attrs:{id:"jobDetail"}},[a("ta-border-layout",{attrs:{layout:{header:"55px"},centerCfg:{showBar:!0,barHeight:"90px"}}},[a("template",{slot:"header"},[a("ta-breadcrumb",{staticStyle:{"line-height":"29px"},attrs:{separator:">"}},[a("ta-breadcrumb-item",[a("a",{on:{click:e.fnBackToHome}},[e._v("作业管理")])]),a("ta-breadcrumb-item",[a("a",{on:{click:e.fnBackToServer}},[e._v("服务器维度")])]),a("ta-breadcrumb-item",[e._v("服务器详情")])],1)],1),a("div",{attrs:{slot:"centerExtraContent"},slot:"centerExtraContent"},[a("ta-alert",{staticClass:"notice-box",attrs:{message:"当前服务器IP："+this.serverIp,type:"info",showIcon:""}}),a("ta-tag-select",{staticClass:"filter-name",staticStyle:{float:"left"},attrs:{title:"状态",data:[{value:"OK",label:"已启用"},{value:"DISABLED",label:"已失效"},{value:"CRASHED",label:"已下线"}],"is-multi":!1},on:{change:e.loadData},model:{value:e.statusArr,callback:function(t){e.statusArr=t},expression:"statusArr"}}),a("div",{staticStyle:{float:"right"}},[a("ta-button",{on:{click:e.fnBackToServer}},[a("ta-icon",{attrs:{type:"rollback"}}),e._v("返回")],1)],1)],1),a("ta-table",{attrs:{columns:e.detailColumns,dataSource:e.detailGridData,pagination:!1},scopedSlots:e._u([{key:"status",fn:function(t,s){return["OK"==t&&s.instanceCount>0?a("ta-tag",{staticClass:"no-cursor",attrs:{color:"#00a65a "}},[e._v("已启用")]):0==s.instanceCount?a("ta-tag",{staticClass:"no-cursor",attrs:{color:"#d2d6de"}},[e._v("已下线")]):"DISABLED"==t?a("ta-tag",{staticClass:"no-cursor",attrs:{color:"#f39c12"}},[e._v("已失效")]):"SHARDING_FLAG"==t?a("ta-tag",{staticClass:"no-cursor",attrs:{color:"#00c0ef"}},[e._v("分片待调整")]):e._e()]}},{key:"action",fn:function(t,s){return["OK"==s.status&&s.instanceCount>0?a("span",[a("ta-button",{staticClass:"failure action-button-common",attrs:{size:"small"},on:{click:function(t){return e.disabledServerJob(s.jobName)}}},[e._v(" 失效 ")]),a("ta-popconfirm",{attrs:{title:"确定要继续终止操作吗?",okText:"确定",cancelText:"取消",okType:"default"},on:{confirm:function(t){return e.shutdownServerJob(s.jobName)}}},[a("ta-icon",{staticStyle:{color:"red"},attrs:{slot:"icon",type:"question-circle-o"},slot:"icon"}),a("ta-button",{staticClass:"terminate action-button-common",attrs:{size:"small"}},[e._v(" 终止 ")])],1)],1):e._e(),0==s.instanceCount?a("span",[a("ta-popconfirm",{attrs:{title:"确定要继续删除操作吗?",okText:"确定",cancelText:"取消",okType:"default"},on:{confirm:function(t){return e.removeServerJob(s.jobName)}}},[a("ta-icon",{staticStyle:{color:"red"},attrs:{slot:"icon",type:"question-circle-o"},slot:"icon"}),a("ta-button",{staticClass:"delete action-button-common",attrs:{size:"small"}},[e._v(" 删除 ")])],1)],1):e._e(),"DISABLED"==s.status&&s.instanceCount>0?a("span",[a("ta-button",{staticClass:"effect action-button-common",attrs:{size:"small"},on:{click:function(t){return e.enableServerJob(s.jobName)}}},[e._v(" 生效 ")]),a("ta-popconfirm",{attrs:{title:"确定要继续终止操作吗?",okText:"确定",cancelText:"取消",okType:"default"},on:{confirm:function(t){return e.shutdownServerJob(s.jobName)}}},[a("ta-icon",{staticStyle:{color:"red"},attrs:{slot:"icon",type:"question-circle-o"},slot:"icon"}),a("ta-button",{staticClass:"terminate action-button-common",attrs:{size:"small"}},[e._v(" 终止 ")])],1)],1):e._e(),"SHARDING_FLAG"==s.status?a("span",[a("ta-button",{staticClass:"failure action-button-common",attrs:{size:"small"},on:{click:function(t){return e.disabledServerJob(s.jobName)}}},[e._v(" 失效 ")]),a("ta-popconfirm",{attrs:{title:"确定要继续终止操作吗?",okText:"确定",cancelText:"取消",okType:"default"},on:{confirm:function(t){return e.shutdownServerJob(s.jobName)}}},[a("ta-icon",{staticStyle:{color:"red"},attrs:{slot:"icon",type:"question-circle-o"},slot:"icon"}),a("ta-button",{staticClass:"terminate action-button-common",attrs:{size:"small"}},[e._v(" 终止 ")])],1)],1):e._e()]}}])})],2)],1)},r=[],o=a(47878),n=[{title:"作业名称",dataIndex:"jobName",width:"30%"},{title:"运行实例数",dataIndex:"instanceCount",width:"30%"},{title:"状态",dataIndex:"status",width:"25%",scopedSlots:{customRender:"status"}},{title:"操作",dataIndex:"action",width:"15%",align:"center",scopedSlots:{customRender:"action"}}],c={name:"jobDetail",components:{},data:function(){return{detailGridData:[],detailColumns:n,zkData:{},serverIp:"",statusArr:[]}},mounted:function(){this.$route.params.serverDetailData instanceof Object?(this.zkData=this.$route.params.serverDetailData.zkData||{},this.serverIp=this.$route.params.serverDetailData.serverIp,this.loadData()):this.$router.push({name:"serverDimensionality"})},methods:{loadData:function(){var e=this,t={zkId:this.zkData.zkId,serverIp:this.serverIp,runStatus:this.statusArr.join(",")};o.Z.getServerJobDetail(t,(function(t){e.detailGridData=t.data.detailGridData}))},fnBackToHome:function(){this.$router.push({name:"zookeeperRegistryCenterConfig",params:{zkData:this.zkData}})},fnBackToServer:function(){this.$router.push({name:"serverDimensionality",params:{zkData:this.zkData}})},disabledServerJob:function(e){var t=this,a={zkId:this.zkData.zkId,jobName:e,serverIp:this.serverIp};o.Z.disabledServerJob(a,(function(e){t.$message.success("失效操作成功"),t.loadData()}))},enableServerJob:function(e){var t=this,a={zkId:this.zkData.zkId,jobName:e,serverIp:this.serverIp};o.Z.enableServerJob(a,(function(e){t.$message.success("生效操作成功"),t.loadData()}))},shutdownServerJob:function(e){var t=this,a={zkId:this.zkData.zkId,jobName:e,serverIp:this.serverIp};o.Z.shutdownServerJob(a,(function(e){t.$message.success("终止操作成功"),t.loadData()}))},removeServerJob:function(e){var t=this,a={zkId:this.zkData.zkId,jobName:e,serverIp:this.serverIp};o.Z.removeServerJob(a,(function(e){t.$message.success("删除操作成功"),t.loadData()}))}}},i=c,l=a(1001),u=(0,l.Z)(i,s,r,!1,null,"1edde467",null),b=u.exports},47878:function(e,t){t["Z"]={addZookeeperRegistryCenter:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/zookeeperAddressManagementRestService/addZookeeperRegistryCenter",data:e},{successCallback:function(e){return t(e)}})},batchDeleteZookeeperRegistryCenter:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/zookeeperAddressManagementRestService/deleteZookeeperRegistryCenter",data:e},{successCallback:function(e){return t(e)}})},connectZookeeperRegistryCenter:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/zookeeperAddressManagementRestService/connectZookeeperRegistryCenter",data:e},{successCallback:function(e){return t(e)}})},getJobInfo:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/getJobInfo",data:e},{successCallback:function(e){return t(e)}})},getJobDetailInfo:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/getJobDetailInfo",data:e},{successCallback:function(e){return t(e)}})},saveJobDetailInfo:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/saveJobDetailInfo",data:e},{successCallback:function(e){return t(e)}})},getJobServerDetail:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/getJobServerDetail",data:e},{successCallback:function(e){return t(e)}})},disabledSharding:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/disabledSharding",data:e},{successCallback:function(e){return t(e)}})},effectSharding:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/effectSharding",data:e},{successCallback:function(e){return t(e)}})},triggerJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/triggerJob",data:e},{successCallback:function(e){return t(e)}})},disableJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/disableJob",data:e},{successCallback:function(e){return t(e)}})},enableJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/enableJob",data:e},{successCallback:function(e){return t(e)}})},shutdownJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/shutdownJob",data:e},{successCallback:function(e){return t(e)}})},removeJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/removeJob",data:e},{successCallback:function(e){return t(e)}})},getServerInfo:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/getServerInfo",data:e},{successCallback:function(e){return t(e)}})},disableServer:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/disableServer",data:e},{successCallback:function(e){return t(e)}})},enableServer:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/enableServer",data:e},{successCallback:function(e){return t(e)}})},shutdownServer:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/shutdownServer",data:e},{successCallback:function(e){return t(e)}})},removeServer:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/removeServer",data:e},{successCallback:function(e){return t(e)}})},getServerJobDetail:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/getServerJobDetail",data:e},{successCallback:function(e){return t(e)}})},disabledServerJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/disabledServerJob",data:e},{successCallback:function(e){return t(e)}})},enableServerJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/enableServerJob",data:e},{successCallback:function(e){return t(e)}})},shutdownServerJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/shutdownServerJob",data:e},{successCallback:function(e){return t(e)}})},removeServerJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/removeServerJob",data:e},{successCallback:function(e){return t(e)}})},getJobNameByZkId:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/freeBusyJobManagerRestService/getJobNameByZkId",data:e},{successCallback:function(e){return t(e)}})},getServerIpsByJobName:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/freeBusyJobManagerRestService/getServerIpsByJobName",data:e},{successCallback:function(e){return t(e)}})},addFreeBusyJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/freeBusyJobManagerRestService/addFreeBusyJob",data:e},{successCallback:function(e){return t(e)}})},deleteFreeBusyJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/freeBusyJobManagerRestService/deleteFreeBusyJob",data:e},{successCallback:function(e){return t(e)}})},getAllServerIpsByJobName:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/freeBusyJobManagerRestService/getAllServerIpsByJobName",data:e},{successCallback:function(e){return t(e)}})},updateFreeBusyJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/freeBusyJobManagerRestService/updateFreeBusyJob",data:e},{successCallback:function(e){return t(e)}})}}}}]);
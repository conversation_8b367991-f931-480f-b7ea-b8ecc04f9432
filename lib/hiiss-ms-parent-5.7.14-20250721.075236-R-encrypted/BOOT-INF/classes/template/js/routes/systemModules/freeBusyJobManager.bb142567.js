"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[5608],{78734:function(e,t,a){a.r(t),a.d(t,{default:function(){return y}});var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"fit",attrs:{id:"freeBusyJobManager"}},[a("ta-border-layout",{attrs:{layout:{header:"55px",footer:"70px"},centerCfg:{showBar:!0},"footer-cfg":{showBorder:!1}}},[a("template",{slot:"header"},[a("ta-breadcrumb",{staticStyle:{"line-height":"29px"},attrs:{separator:">"}},[a("ta-breadcrumb-item",[a("a",{on:{click:e.fnBackToHome}},[e._v("作业管理")])]),a("ta-breadcrumb-item",[e._v("闲忙任务管理")])],1)],1),a("div",{attrs:{slot:"centerExtraContent"},slot:"centerExtraContent"},[a("ta-alert",{attrs:{message:"当前注册中心名称："+this.zkData.appName,type:"info",showIcon:""}})],1),a("ta-tabs",{staticClass:"fit content-box"},[a("ta-tab-pane",{attrs:{tab:"闲忙任务列表"}},[a("ta-table",{attrs:{columns:e.columns,dataSource:e.gridData,rowKey:"id",rowSelection:{selectedRowKeys:e.selectedRowKeys,onSelect:e.fnOnSelect,onSelectAll:e.fnOnSelectAll},pagination:!1},scopedSlots:e._u([{key:"action",fn:function(t,r){return a("span",{},[a("ta-table-operate",{attrs:{operateMenu:e.operateMenu}})],1)}}])})],1),a("template",{slot:"tabBarExtraContent"},[a("ta-button",{on:{click:e.fnBackToHome}},[a("ta-icon",{attrs:{type:"rollback"}}),e._v("返回")],1),a("ta-button",{attrs:{type:"primary"},on:{click:e.clickAdd}},[e._v("新增")]),a("ta-button",{attrs:{disabled:e.btnDisable},on:{click:e.batchDelete}},[e._v("批量删除")])],1)],2),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("ta-pagination",{ref:"gridPager",staticStyle:{float:"right","margin-top":"10px"},attrs:{showSizeChanger:"",showQuickJumper:"",dataSource:e.gridData,defaultPageSize:10,params:e.pageParams,url:"jobmg/elasticjob/freeBusyJobManagerRestService/getFreeBusyJob"},on:{"update:dataSource":function(t){e.gridData=t},"update:data-source":function(t){e.gridData=t}}})],1)],2),a("ta-drawer",{attrs:{title:e.drawerTitle,width:"520",visible:e.visible,destroyOnClose:""},on:{close:e.handleCloseFreeBusyDrawer}},[a("add-free-busy-job",{ref:"freeBusy",attrs:{editType:e.editType,zkData:e.zkData,fbData:e.fbData,jobNameList:e.jobNameList,ipList:e.ipList},on:{close:e.handleCloseFreeBusyDrawer,queryTable:e.loadData}}),a("template",{slot:"footer"},[a("ta-button-group",[a("ta-button",{on:{click:function(t){return e.handleResetFreeBusy()}}},[e._v("重置")]),a("ta-button",{attrs:{type:"primary"},on:{click:function(t){return e.handleSaveFreeBusy()}}},[e._v("保存")])],1)],1)],2)],1)},s=[],o=a(47878),i=function(){var e=this,t=this,a=t.$createElement,r=t._self._c||a;return r("div",{attrs:{id:"jobDetail"}},[r("ta-form",{attrs:{id:"bfForm",autoFormCreate:function(t){e.form=t}}},[r("ta-form-item",{attrs:{label:"应用名称",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"appName",fieldDecoratorOptions:{rules:[{required:!0,message:"请输入应用名称"}],initialValue:t.zkData.appName}}},[r("ta-input",{attrs:{disabled:!0}})],1),r("ta-form-item",{attrs:{label:"任务名称",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"jobName",fieldDecoratorOptions:{rules:[{required:!0,message:"请输入任务名称"}],initialValue:t.jobName}}},[r("ta-select",{attrs:{allowClear:"",disabled:"edit"==t.editType,placeholder:"请选择任务名称"},on:{change:t.handleJobNameSelectChange}},t._l(t.jobNameList,(function(e){return r("ta-select-option",{key:e,attrs:{value:e}},[t._v(t._s(e)+" ")])})),1)],1),"edit"==t.editType&&t.ipList.length>0?r("ta-form-item",{attrs:{label:"服务器IP",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"serverIps",fieldDecoratorOptions:{rules:[{required:!0,message:"请输入服务器IP"}],initialValue:t.checkedList}}},[r("ta-checkbox-group",{attrs:{options:t.ipList},on:{change:t.checkboxChange}})],1):t._e(),"add"==t.editType&&t.addIpList.length>0?r("ta-form-item",{attrs:{label:"服务器IP",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"serverIps",fieldDecoratorOptions:{rules:[{required:!0,message:"请输入服务器IP"}],initialValue:t.checkedList}}},[r("ta-checkbox-group",{attrs:{options:t.addIpList},on:{change:t.checkboxChange}})],1):t._e(),r("ta-form-item",{attrs:{label:"空闲时刻",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"freeMoment",fieldDecoratorOptions:{rules:[{required:!0,message:"请输入空闲时刻"}],initialValue:t.freeMoment}}},[r("ta-time-picker",{attrs:{format:"HH:mm"}})],1),r("ta-form-item",{attrs:{label:"繁忙时刻",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"busyMoment",fieldDecoratorOptions:{rules:[{required:!0,message:"请输入繁忙时刻"}],initialValue:t.busyMoment}}},[r("ta-time-picker",{attrs:{format:"HH:mm"}})],1)],1)],1)},n=[],l=a(36797),u=a.n(l),c={name:"addFreeBusyJob",props:["editType","fbData","zkData","jobNameList","ipList"],data:function(){return{formItemLayout:{labelCol:{span:6},wrapperCol:{span:18}},checkedList:[],addIpList:[],jobName:void 0,freeMoment:void 0,busyMoment:void 0}},mounted:function(){"edit"==this.editType&&(this.checkedList=this.fbData.serverIps.split(","),this.jobName=this.fbData.jobName,this.freeMoment=u()(this.fbData.freeMoment,"HH:mm"),this.busyMoment=u()(this.fbData.busyMoment,"HH:mm"))},methods:{moment:u(),handleJobNameSelectChange:function(e){var t=this,a={zkId:this.zkData.zkId,jobName:e};o.Z.getServerIpsByJobName(a,(function(e){t.addIpList=e.data.addIpList}))},closeDrawer:function(){this.$emit("close"),this.form.resetFields()},onResetForm:function(){this.form.resetFields(),"add"==this.editType&&(this.addIpList=[])},onSubmitForm:function(){var e=this;this.form.validateFields((function(t,a){if(!t){if(e.form.getFieldValue("freeMoment").format("HH:mm")==e.form.getFieldValue("busyMoment").format("HH:mm"))return void e.$message.warn("空闲时刻和繁忙时刻不能相等");if("add"==e.editType){var r={zkId:e.zkData.zkId,jobName:e.form.getFieldValue("jobName"),serverIps:e.form.getFieldValue("serverIps").join(","),freeMoment:e.form.getFieldValue("freeMoment").format("HH:mm"),busyMoment:e.form.getFieldValue("busyMoment").format("HH:mm")};o.Z.addFreeBusyJob(r,(function(t){e.$message.success("保存成功"),e.closeDrawer(),e.$emit("queryTable")}))}else if("edit"==e.editType){var s={id:e.fbData.id,serverIps:e.form.getFieldValue("serverIps").join(","),freeMoment:e.form.getFieldValue("freeMoment").format("HH:mm"),busyMoment:e.form.getFieldValue("busyMoment").format("HH:mm")};o.Z.updateFreeBusyJob(s,(function(t){e.$message.success("更新成功"),e.closeDrawer(),e.$emit("queryTable")}))}}}))},checkboxChange:function(e){}}},b=c,d=a(1001),m=(0,d.Z)(b,i,n,!1,null,null,null),f=m.exports,p=[{title:"任务名称",dataIndex:"jobName",width:"20%",overflowTooltip:!0},{title:"空闲时刻",dataIndex:"freeMoment",width:"15%",overflowTooltip:!1},{title:"繁忙时刻",dataIndex:"busyMoment",width:"15%",overflowTooltip:!1},{title:"被操作的服务器IP",dataIndex:"serverIps",width:"40%",overflowTooltip:!0},{title:"操作",dataIndex:"action",width:"10%",scopedSlots:{customRender:"action"},align:"center"}],g={name:"app",data:function(){var e=this;return{selectedRowKeys:[],selectedRows:[],columns:p,operateMenu:[{name:"编辑",onClick:function(t){e.editFreeBusyJob(t)}},{name:"删除",type:"confirm",confirmTitle:"确定要删除吗?",isShow:function(t){return e.gridData.length},onOk:function(t){e.onDelete(t.id)}}],gridData:[],zkId:void 0,appNameList:[],visible:!1,editType:"",drawerTitle:"新增闲忙任务",zkData:{},fbData:{},jobNameList:[],ipList:[]}},components:{addFreeBusyJob:f},mounted:function(){this.$route.params.zkData instanceof Object?(this.zkData=this.$route.params.zkData||{},this.loadData()):this.$router.push({name:"zookeeperRegistryCenterConfig"})},computed:{btnDisable:function(){return!this.selectedRowKeys.length}},methods:{fnBackToHome:function(){this.$router.push({name:"zookeeperRegistryCenterConfig"})},handleResetFreeBusy:function(){this.$refs.freeBusy.onResetForm()},handleSaveFreeBusy:function(){this.$refs.freeBusy.onSubmitForm()},handleCloseFreeBusyDrawer:function(){this.visible=!1,this.fbData={},this.jobNameList=[],this.ipList=[]},loadData:function(){var e=this;this.$refs.gridPager.loadData((function(t){e.selectedRowKeys=[],e.selectedRows=[]}))},pageParams:function(){return{zkId:this.zkData.zkId}},clickAdd:function(){var e=this;this.drawerTitle="新增闲忙任务",this.editType="add";var t={zkId:this.zkData.zkId,zkAddress:this.zkData.zkAddress,appNamespace:this.zkData.appNamespace};o.Z.getJobNameByZkId(t,(function(t){!t.data.jobNameList||t.data.jobNameList.length<1?e.$message.warn("没有可用任务添加"):(e.jobNameList=t.data.jobNameList,e.visible=!0)}))},onDelete:function(e){var t=this;o.Z.deleteFreeBusyJob({ids:e},(function(e){t.$message.success("删除成功"),t.loadData()}))},batchDelete:function(){var e=this;0!==this.selectedRows.length?this.$confirm({title:"批量删除闲忙任务",content:"确认删除闲忙任务吗?",cancelText:"取消",okText:"确认",onOk:function(){var t=e.selectedRows.map((function(e){return e.id}));o.Z.deleteFreeBusyJob({ids:t.join(",")},(function(t){e.$message.success("批量删除成功"),e.loadData()}))}}):this.$message.warning("请先选择数据")},fnOnSelect:function(e,t){var a=this.selectedRowKeys,r=this.selectedRows;t?(a.push(e.id),r.push(e)):(this.selectedRowKeys=a.filter((function(t){return t!=e.id})),this.selectedRows=r.filter((function(t){return t.id!=e.id})))},fnOnSelectAll:function(e,t){var a=this;this.selectedRows=[],this.selectedRowKeys=[],e&&(t.map((function(e){a.selectedRowKeys.push(e.id)})),this.selectedRows=t)},editFreeBusyJob:function(e){var t=this;this.drawerTitle="更新闲忙任务",this.editType="edit",this.fbData=e;var a={zkId:this.zkData.zkId,zkAddress:this.zkData.zkAddress,appNamespace:this.zkData.appNamespace,jobName:this.fbData.jobName};o.Z.getAllServerIpsByJobName(a,(function(e){!e.data.ipList||e.data.ipList.length<1?t.$message.warn("任务没有启动，不可编辑"):(t.ipList=e.data.ipList,t.visible=!0)}))}}},h=g,v=(0,d.Z)(h,r,s,!1,null,"5b574275",null),y=v.exports},47878:function(e,t){t["Z"]={addZookeeperRegistryCenter:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/zookeeperAddressManagementRestService/addZookeeperRegistryCenter",data:e},{successCallback:function(e){return t(e)}})},batchDeleteZookeeperRegistryCenter:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/zookeeperAddressManagementRestService/deleteZookeeperRegistryCenter",data:e},{successCallback:function(e){return t(e)}})},connectZookeeperRegistryCenter:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/zookeeperAddressManagementRestService/connectZookeeperRegistryCenter",data:e},{successCallback:function(e){return t(e)}})},getJobInfo:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/getJobInfo",data:e},{successCallback:function(e){return t(e)}})},getJobDetailInfo:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/getJobDetailInfo",data:e},{successCallback:function(e){return t(e)}})},saveJobDetailInfo:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/saveJobDetailInfo",data:e},{successCallback:function(e){return t(e)}})},getJobServerDetail:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/getJobServerDetail",data:e},{successCallback:function(e){return t(e)}})},disabledSharding:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/disabledSharding",data:e},{successCallback:function(e){return t(e)}})},effectSharding:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/effectSharding",data:e},{successCallback:function(e){return t(e)}})},triggerJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/triggerJob",data:e},{successCallback:function(e){return t(e)}})},disableJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/disableJob",data:e},{successCallback:function(e){return t(e)}})},enableJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/enableJob",data:e},{successCallback:function(e){return t(e)}})},shutdownJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/shutdownJob",data:e},{successCallback:function(e){return t(e)}})},removeJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/removeJob",data:e},{successCallback:function(e){return t(e)}})},getServerInfo:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/getServerInfo",data:e},{successCallback:function(e){return t(e)}})},disableServer:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/disableServer",data:e},{successCallback:function(e){return t(e)}})},enableServer:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/enableServer",data:e},{successCallback:function(e){return t(e)}})},shutdownServer:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/shutdownServer",data:e},{successCallback:function(e){return t(e)}})},removeServer:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/removeServer",data:e},{successCallback:function(e){return t(e)}})},getServerJobDetail:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/getServerJobDetail",data:e},{successCallback:function(e){return t(e)}})},disabledServerJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/disabledServerJob",data:e},{successCallback:function(e){return t(e)}})},enableServerJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/enableServerJob",data:e},{successCallback:function(e){return t(e)}})},shutdownServerJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/shutdownServerJob",data:e},{successCallback:function(e){return t(e)}})},removeServerJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/removeServerJob",data:e},{successCallback:function(e){return t(e)}})},getJobNameByZkId:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/freeBusyJobManagerRestService/getJobNameByZkId",data:e},{successCallback:function(e){return t(e)}})},getServerIpsByJobName:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/freeBusyJobManagerRestService/getServerIpsByJobName",data:e},{successCallback:function(e){return t(e)}})},addFreeBusyJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/freeBusyJobManagerRestService/addFreeBusyJob",data:e},{successCallback:function(e){return t(e)}})},deleteFreeBusyJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/freeBusyJobManagerRestService/deleteFreeBusyJob",data:e},{successCallback:function(e){return t(e)}})},getAllServerIpsByJobName:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/freeBusyJobManagerRestService/getAllServerIpsByJobName",data:e},{successCallback:function(e){return t(e)}})},updateFreeBusyJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/freeBusyJobManagerRestService/updateFreeBusyJob",data:e},{successCallback:function(e){return t(e)}})}}}}]);
"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[8653],{62137:function(e,t,a){a.r(t),a.d(t,{default:function(){return S}});var o=function(){var e=this,t=this,a=t.$createElement,o=t._self._c||a;return o("div",{staticClass:"fit"},[o("ta-border-layout",{attrs:{layout:{left:"300px"},"center-cfg":{layoutConStyle:{padding:0,border:0}}}},[o("ta-border-layout",{attrs:{layout:{header:"70px"},showBorder:!1,centerCfg:{showBar:!0,toolBarStyle:{height:"50px",lineHeight:"48px"}}}},[o("div",{staticClass:"center-box",attrs:{slot:"header"},slot:"header"},[o("ta-input-search",{staticClass:"search-box",attrs:{placeholder:"字段搜索",enterButton:"搜索"},on:{search:t.searchQuery},model:{value:t.param,callback:function(e){t.param=e},expression:"param"}})],1),o("div",{attrs:{slot:"centerExtraContent"},slot:"centerExtraContent"},[o("div",{staticStyle:{float:"right"}},[o("ta-button",{attrs:{icon:"edit"},on:{click:t.addConfig}},[t._v(" 新增配置 ")]),o("ta-popover",{attrs:{width:"190",placement:"top"},model:{value:t.visiblePopover,callback:function(e){t.visiblePopover=e},expression:"visiblePopover"}},[o("ta-button",{attrs:{slot:"reference",icon:"reload"},slot:"reference"},[t._v(" 重置配置 ")]),o("p",[t._v("【功能配置、系统配置】一并重置,确定重置?")]),o("div",{staticStyle:{"text-align":"right",margin:"0"}},[o("ta-button",{on:{click:function(e){t.visiblePopover=!1}}},[t._v("取消")]),o("ta-button",{attrs:{type:"primary"},on:{click:t.refreshConfig}},[t._v("确定")])],1)],1)],1)]),o("ta-form",{attrs:{autoFormCreate:function(t){e.form=t}}},[o("ta-table",{attrs:{size:"small",columns:t.columns,dataSource:t.data,rowKey:"id",defaultExpandedRowKeys:t.defaultExpandedRowKeys,expandedRowKeys:t.defaultExpandedRowKeys,scroll:{x:1200}},on:{expand:t.loadChild},scopedSlots:t._u([{key:"fieldName",fn:function(e,a){return o("span",{},[t._v(t._s(e))])}},{key:"defaultValue",fn:function(e,a,i){return["1"==a.dataType?[o("ta-table-edit",{attrs:{type:"input",beforeChange:t.changeData}})]:"2"==a.dataType?[o("ta-table-edit",{attrs:{type:"datePicker",beforeChange:t.changeData}})]:"3"==a.dataType?[""!=a.collectionDatas&&void 0!=a.collectionDatas?[o("ta-table-edit",{attrs:{type:"select",option:t.stringToJson(a),beforeChange:t.changeData}})]:""!=a.collectionName&&void 0!=a.collectionName?[o("ta-table-edit",{attrs:{type:"select",option:t.CollectionData(a.collectionName),beforeChange:t.changeData}})]:[o("span",[t._v(t._s(e))])]]:"4"==a.dataType?[o("ta-table-edit",{attrs:{type:"inputNumber",beforeChange:t.changeData}})]:"5"==a.dataType?[o("span",{on:{click:function(a){return t.showYmlValue(e)}}},[t._v(t._s(e))])]:[o("span",[t._v(t._s(e))])]]}},{key:"configComment",fn:function(e){return o("span",{},[t._v(t._s(e))])}},{key:"updateTime",fn:function(e,a){return o("span",{},[t._v(t._s(null==e?"":t.moment(a.updateTime).format("YYYY-MM-DD")))])}},{key:"opUsername",fn:function(e){return o("span",{},[t._v(t._s(null==e?"--":e))])}},{key:"action",fn:function(e,a){return void 0==a.children?o("span",{},[o("ta-table-operate",{attrs:{operateMenu:t.operateMenu}})],1):t._e()}}],null,!0)},[o("span",{attrs:{slot:"defaultValueTitle"},slot:"defaultValueTitle"},[t._v("当前设置 "),o("ta-icon",{attrs:{type:"edit"}})],1)])],1)],1),o("div",{attrs:{slot:"left"},slot:"left"},[o("ta-card",{attrs:{bordered:!1,bodyStyle:{padding:0}}},[o("div",{attrs:{slot:"title"},slot:"title"},[t._v(" 配置类别 ")]),t._l(t.configCategory,(function(e,a){return o("a",{key:e.value,attrs:{value:e.label},on:{click:function(o){return t.categoryChange(e.value,a)}}},[o("div",{staticClass:"left-item",class:{activeClass:a==t.clickIndex}},[t._v(t._s(e.label))])])}))],2)],1),o("ta-modal",{attrs:{title:"当前配置",centered:"",width:"500px",bodyStyle:{height:"200px",padding:"0"},destroyOnClose:!0,maskClosable:!1},model:{value:t.ymlValueVisible,callback:function(e){t.ymlValueVisible=e},expression:"ymlValueVisible"}},[o("ta-textarea",{attrs:{rows:9},model:{value:t.ymlValue,callback:function(e){t.ymlValue=e},expression:"ymlValue"}}),o("template",{slot:"footer"},[o("ta-button",{on:{click:function(e){t.ymlValueVisible=!1}}},[t._v(" 取消 ")])],1)],2)],1),o("add-config",{attrs:{visible:t.addConfigVisible,rowData:t.rowData,configKey:t.configKey},on:{close:function(e){t.addConfigVisible=!1},addSuccess:t.addSuccess}})],1)},i=[],n="/tasysconfig/taSysConfigRestService/",l={updateSysConfig:function(e,t){Base.submit(null,{url:n+"updateSysConfig",data:e},{successCallback:function(e){return t(e)}})},querySysConfig:function(e,t){Base.submit(null,{url:n+"querySysConfig",data:e},{successCallback:function(e){return t(e)}})},deleteSysConfig:function(e,t){Base.submit(null,{url:n+"deleteSysConfig",data:e},{successCallback:function(e){return t(e)}})},addSysConfig:function(e,t){Base.submit(null,{url:n+"addSysConfig",data:e},{successCallback:function(e){return t(e)}})},refreshSysConfig:function(e,t){Base.submit(null,{url:n+"refreshSysConfig",data:e},{successCallback:function(e){return t(e)}})},refreshOneSysConfig:function(e,t){Base.submit(null,{url:n+"refreshOneSysConfig",data:e},{successCallback:function(e){return t(e)}})}},r=a(36797),s=a.n(r),c=function(){var e=this,t=this,a=t.$createElement,o=t._self._c||a;return o("ta-drawer",{attrs:{title:"新增系统配置",width:"500",placement:"right",closable:!0,visible:t.visible,destroyOnClose:""},on:{close:t.closeEdit}},[o("ta-form",{attrs:{autoFormCreate:function(t){e.form=t}}},[o("ta-form-item",{attrs:{label:"字段名",fieldDecoratorId:"fieldName",fieldDecoratorOptions:{rules:[{required:!0,message:"字段名不能为空!"}],initialValue:""}}},[o("ta-input")],1),o("ta-form-item",{attrs:{label:"备注",fieldDecoratorId:"configComment",fieldDecoratorOptions:{rules:[{required:!0,message:"备注不能为空!"}],initialValue:""}}},[o("ta-input")],1),o("ta-form-item",{staticStyle:{display:"none"},attrs:{label:"功能类型",fieldDecoratorId:"functionType",fieldDecoratorOptions:{rules:[{required:!0,message:"功能类型不能为空!"}],initialValue:t.functionType}}},[o("ta-input")],1),o("ta-form-item",{attrs:{label:"配置类型",fieldDecoratorId:"dataType",fieldDecoratorOptions:{rules:[{required:!0,message:"配置类型不能为空!"}],initialValue:""}}},[o("ta-select",{staticStyle:{width:"90%"},on:{change:t.dataTypeChange}},t._l(t.CollectionData("CONFIGDATATYPE"),(function(e){return o("ta-select-option",{key:e.value,attrs:{value:e.value}},[t._v(t._s(e.label))])})),1),o("ta-popover",{attrs:{width:"100",trigger:"hover",content:"1.如果配置类型是选项,则需要设置码表类型或码表值；2.配置类型应与默认值匹配；如：选择日期，则默认值为日期字符串，eg：2019-11-14"}},[o("ta-icon",{staticStyle:{cursor:"pointer","margin-left":"10px"},attrs:{slot:"reference",type:"question-circle"},slot:"reference"})],1)],1),"3"==t.type?o("ta-label-con",{attrs:{label:"选项名称"}},[o("ta-radio-group",{attrs:{options:[{label:"码表类型",value:"collectionName"},{label:"自定义码表",value:"collectionDatas"}],defaultValue:"collectionName"},on:{change:t.fnOnRadioChange}})],1):t._e(),"3"==t.type&&"1"==t.radioChecked?o("ta-form-item",{attrs:{label:"码表类型",fieldDecoratorId:"collectionName",fieldDecoratorOptions:{initialValue:"",rules:[{required:!0,message:"码表类型不能为空!"}]}}},[o("ta-input",{staticStyle:{width:"90%"},attrs:{placeholder:"输入字典类型，如BL、SEX"}}),o("ta-popover",{attrs:{width:"100",trigger:"hover",content:"如果输入BL，则默认值可填true或false"}},[o("ta-icon",{staticStyle:{cursor:"pointer","margin-left":"10px"},attrs:{slot:"reference",type:"question-circle"},slot:"reference"})],1)],1):t._e(),"3"==t.type&&"2"==t.radioChecked?o("ta-form-item",{attrs:{label:"自定义码表",fieldDecoratorId:"collectionDatas",fieldDecoratorOptions:{initialValue:"",rules:[{required:!0,message:"自定义码表不能为空!"}]}}},[o("ta-input",{staticStyle:{width:"90%"},attrs:{placeholder:'例如：[{"value":"on","label":"开启状态"}]'}}),o("ta-popover",{attrs:{width:"300",trigger:"hover",content:'输入类似：[{"value":"on","label":"开启状态"},{"value":"off","label":"关闭状态"}]， 默认值可填on或off'}},[o("ta-icon",{staticStyle:{cursor:"pointer","margin-left":"10px"},attrs:{slot:"reference",type:"question-circle"},slot:"reference"})],1)],1):t._e(),"2"==t.type?o("ta-form-item",{attrs:{label:"默认值",fieldDecoratorId:"defaultDateValue",fieldDecoratorOptions:{rules:[{required:!0,message:"默认值不能为空!"}]}}},[o("ta-date-picker",{attrs:{format:"YYYY-MM-DD"}})],1):"4"==t.type?o("ta-form-item",{attrs:{label:"默认值",fieldDecoratorId:"defaultNumValue",fieldDecoratorOptions:{rules:[{required:!0,message:"默认值不能为空/格式错误!"}]}}},[o("ta-input-number")],1):"5"==t.type?o("ta-form-item",{attrs:{label:"默认值",fieldDecoratorId:"defaultYmlValue",fieldDecoratorOptions:{rules:[{required:!0,message:"默认值不能为空!"}],initialValue:""}}},[o("ta-textarea",{attrs:{rows:9,placeholder:"server:\n port: 8081"}})],1):o("ta-form-item",{attrs:{label:"默认值",fieldDecoratorId:"defaultValue",fieldDecoratorOptions:{rules:[{required:!0,message:"默认值不能为空!"}],initialValue:""}}},[o("ta-input")],1)],1),o("div",{attrs:{slot:"footer"},slot:"footer"},[o("ta-button-group",[o("ta-button",{on:{click:function(e){return t.reset()}}},[t._v("重置")]),o("ta-button",{attrs:{type:"primary"},on:{click:function(e){return t.save()}}},[t._v("保存")])],1)],1)],1)},u=[],d=a(41088),f={name:"addConfig",props:["visible","rowData","configKey"],components:{TaTextarea:d.Z},data:function(){return{type:"",form:null,loading:!1,functionType:"1",radioChecked:"1"}},watch:{visible:function(e){}},methods:{moment:s(),closeEdit:function(){this.type="",this.$emit("close")},save:function(){var e=this;this.form.validateFields((function(t,a){if(!t)if(null!=e.configKey&&""!==e.configKey){var o=e.form.getFieldsValue();if(o.fieldName=e.configKey+"."+o.fieldName,""!==e.form.getFieldValue("fieldName").trim()){if("3"===e.type){if(""===e.form.getFieldValue("collectionName")&&""===e.form.getFieldValue("collectionDatas"))return void e.$message.error("字典类型不能同时为空");if("2"===e.radioChecked&&""!==e.form.getFieldValue("collectionDatas"))try{JSON.parse(e.form.getFieldValue("collectionDatas"))}catch(i){return void e.$message.error("自定义码表数据有误")}}"2"===e.type?o.defaultValue=s()(o.defaultDateValue).format("YYYY-MM-DD"):"4"===e.type?o.defaultValue=o.defaultNumValue:"5"===e.type&&(o.defaultValue=o.defaultYmlValue),"1"===e.radioChecked?o.collectionDatas="":o.collectionName="",l.addSysConfig(o,(function(t){e.$message.success("添加成功"),e.closeEdit(),e.radioChecked="1",e.$emit("addSuccess")}))}else e.$message.error("字段名不能为空")}else e.$message.error("未选择配置类别！")}))},dataTypeChange:function(e){this.form.resetFields("defaultValue"),this.type=e},reset:function(){this.form.resetFields(),this.type=""},fnOnRadioChange:function(e){"collectionName"===e.target.value?this.radioChecked="1":this.radioChecked="2"}}},p=f,m=a(1001),y=(0,m.Z)(p,c,u,!1,null,"411a49f7",null),g=y.exports,h=[{title:"字段名",dataIndex:"fieldName",width:"360px",overflowTooltip:!0,scopedSlots:{customRender:"fieldName"}},{title:"备注",dataIndex:"configComment",width:"20%",overflowTooltip:!0,scopedSlots:{customRender:"configComment"}},{slots:{title:"defaultValueTitle"},dataIndex:"defaultValue",width:"23%",overflowTooltip:!0,scopedSlots:{customRender:"defaultValue"}},{title:"更新时间",dataIndex:"updateTime",width:"16%",overflowTooltip:!0,scopedSlots:{customRender:"updateTime"}},{title:"操作人",dataIndex:"opUsername",width:"10%",overflowTooltip:!0,scopedSlots:{customRender:"opUsername"}},{title:"操作",align:"center",width:100,scopedSlots:{customRender:"action"}}],C={name:"systemConfigManagement",data:function(){var e=this;return{data:[],columns:h,visiblePopover:!1,defaultExpandedRowKeys:[],operateMenu:[{name:"重置",type:"confirm",confirmTitle:"重置为最初配置(若为手动添加的配置重置为新增时的配置)?",onOk:function(t){e.refreshOneConfig(t)}},{name:"删除",type:"confirm",confirmTitle:"确定删除该配置?",onOk:function(t){e.deleteConfig(t.id)}}],ymlValue:"",ymlValueVisible:!1,gridData:[],isCheckParam:[],rowData:{},selectedRowKeys:[],param:"",configCategory:[],configKey:"",searchUrl:"tasysconfig/taSysConfigRestService/querySysConfig",clickIndex:0,functionType:"1",configCategoryType:"CONFIGSYSTEMCATEGORY",addConfigVisible:!1}},components:{moment:s(),addConfig:g,TaTextarea:d.Z},mounted:function(){this.initForm()},methods:{moment:s(),pageParams:function(){var e={};return e.fieldName=this.param,e.configKey=this.configKey,e.functionType=this.functionType,e},initForm:function(){var e=this;this.Base.asyncGetCodeData(this.configCategoryType).then((function(t){e.configCategory=t,null!=e.configCategory&&(e.configKey=e.configCategory[0].value,e.querySysConfig())}))},querySysConfig:function(e){var t=this;l.querySysConfig({functionType:this.functionType,configKey:this.configKey,fieldName:e},(function(e){t.data=e.data.data}))},searchQuery:function(){this.querySysConfig(this.param)},stringToJson:function(e){return JSON.parse(e.collectionDatas)},onSelectChange:function(e,t){this.selectedRowKeys=e},loadChild:function(e,t){var a=this,o=t.id,i=t.fieldName;if(e)t.children&&t.children.length?this.defaultExpandedRowKeys.push(o):l.querySysConfig({configKey:i},(function(e){t.children=e.data.data,a.defaultExpandedRowKeys.push(o)}));else{var n=this.defaultExpandedRowKeys.indexOf(o);this.defaultExpandedRowKeys.splice(n,1)}},changeData:function(e,t){var a=this,o=e.newData,i=e.record,n=e.columnKey;this.$confirm({title:"确定修改配置？",onOk:function(){l.updateSysConfig({id:i.id,defaultValue:o,fieldName:i.fieldName,dataType:i.dataType},(function(e){i[n]=o,t()})),a.$message.success("配置已生效")},onCancel:function(){t()}})},categoryChange:function(e,t){this.configKey=e,this.clickIndex!=t&&(this.querySysConfig(),this.clickIndex=t)},addConfig:function(){this.addConfigVisible=!0},refreshConfig:function(){var e=this;l.refreshSysConfig({},(function(t){e.$message.success("重置成功"),e.querySysConfig()})),this.visiblePopover=!1},refreshOneConfig:function(e){var t=this;l.refreshOneSysConfig({id:e.id,defaultValue:"1",fieldName:e.fieldName,dataType:e.dataType},(function(e){t.$message.success("重置成功"),t.querySysConfig()}))},deleteConfig:function(e){var t=this;l.deleteSysConfig({id:e},(function(e){t.$message.success("删除成功"),t.querySysConfig()}))},addSuccess:function(){this.querySysConfig()},showYmlValue:function(e){this.ymlValue=e,this.ymlValueVisible=!0}}},v=C,b=(0,m.Z)(v,o,i,!1,null,"9f061436",null),S=b.exports}}]);
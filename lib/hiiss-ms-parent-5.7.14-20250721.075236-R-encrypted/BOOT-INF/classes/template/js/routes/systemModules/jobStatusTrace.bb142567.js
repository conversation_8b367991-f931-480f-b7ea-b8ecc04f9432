"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[6411],{29294:function(t,a,e){e.r(a),e.d(a,{default:function(){return d}});var s=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticClass:"fit",attrs:{id:"jobExecutionTrace"}},[e("ta-border-layout",{attrs:{layout:{header:"70px",footer:"70px"},centerCfg:{showBar:!0},"footer-cfg":{showBorder:!1}}},[e("div",{staticClass:"center-box",attrs:{slot:"header"},slot:"header"},[e("ta-input-search",{staticClass:"search-box",attrs:{placeholder:"请输入作业名称",enterButton:"搜索"},on:{search:t.loadData},model:{value:t.searchInfo,callback:function(a){t.searchInfo=a},expression:"searchInfo"}})],1),e("div",{attrs:{slot:"centerExtraContent"},slot:"centerExtraContent"},[e("span",[t._v("当前数据源名称：")]),e("ta-tag",{staticClass:"no-cursor",attrs:{color:"#00a65a"}},[t._v(t._s(this.dsData.datasourceName))]),e("ta-range-picker",{staticClass:"distance",attrs:{placeholder:["开始时间","结束时间"],"get-calendar-container":t.getContainer},on:{change:t.onChange}}),e("ta-tag-select",{staticClass:"filter-name",attrs:{title:"状态",data:t.state},on:{change:t.filterClick},model:{value:t.stateList,callback:function(a){t.stateList=a},expression:"stateList"}}),e("div",{staticStyle:{float:"right"}},[e("ta-button",{on:{click:t.fnBackToHome}},[e("ta-icon",{attrs:{type:"rollback"}}),t._v("返回")],1)],1)],1),e("ta-table",{attrs:{columns:t.statusColumns,dataSource:t.statusGridData,pagination:!1},scopedSlots:t._u([{key:"state",fn:function(a){return e("span",{},["TASK_FINISHED"==a?e("ta-tag",{staticClass:"no-cursor",attrs:{color:"#00a65a"}},[t._v("已完成")]):"TASK_STAGING"==a?e("ta-tag",{staticClass:"no-cursor",attrs:{color:"#d2d6de"}},[t._v("等待运行")]):"TASK_RUNNING"==a?e("ta-tag",{staticClass:"no-cursor",attrs:{color:"#3c8dbc"}},[t._v("运行中")]):"TASK_ERROR"==a?e("ta-tag",{staticClass:"no-cursor",attrs:{color:"#dd4b39"}},[t._v("启动失败")]):"TASK_KILLED"==a?e("ta-tag",{staticClass:"no-cursor",attrs:{color:"#f0ad4e"}},[t._v("主动终止")]):"TASK_FAILED"==a?e("ta-tag",{staticClass:"no-cursor",attrs:{color:"#d9534f"}},[t._v("运行失败")]):t._e()],1)}},{key:"action",fn:function(a,s){return e("span",{},[e("ta-table-operate",{attrs:{operateMenu:t.operateMenu}})],1)}}])}),e("div",{attrs:{slot:"footer"},slot:"footer"},[e("ta-pagination",{ref:"gridPager",staticStyle:{float:"right","margin-top":"10px"},attrs:{showSizeChanger:"",showQuickJumper:"",dataSource:t.statusGridData,defaultPageSize:10,params:t.pageParams,url:"jobmg/elasticjob/jobDatasourceManagementRestService/getJobStatusTrace"},on:{"update:dataSource":function(a){t.statusGridData=a},"update:data-source":function(a){t.statusGridData=a}}})],1)],1),e("ta-modal",{attrs:{centered:"",footer:null,width:"800px",destroyOnClose:!0},model:{value:t.visible,callback:function(a){t.visible=a},expression:"visible"}},[e("ta-label-con",{staticStyle:{"margin-bottom":"0px"},attrs:{label:"备注",labelCol:2,wrapperCol:22}},[e("ta-textarea",{attrs:{autosize:{minRows:15,maxRows:20},readonly:"readonly"},model:{value:t.message,callback:function(a){t.message=a},expression:"message"}})],1)],1)],1)},o=[],r=(e(95439),[{title:"作业名称",dataIndex:"job_name",width:"23%"},{title:"分片项",dataIndex:"sharding_item",width:"23%"},{title:"状态",dataIndex:"state",width:"23%",scopedSlots:{customRender:"state"}},{title:"创建时间",dataIndex:"creation_time",width:"23%"},{title:"操作选项",dataIndex:"action",width:"10%",scopedSlots:{customRender:"action"},align:"center"}]),n=[{value:"TASK_FINISHED",label:"已完成"},{value:"TASK_STAGING",label:"等待运行"},{value:"TASK_RUNNING",label:"运行中"},{value:"TASK_ERROR",label:"启动失败"},{value:"TASK_KILLED",label:"主动终止"},{value:"TASK_FAILED",label:"运行失败"}],i={name:"jobExecutionTrace",components:{},data:function(){var t=this;return{statusGridData:[],statusColumns:r,operateMenu:[{name:"备注",isShow:function(t){return t.message},onClick:function(a){t.showModel(a.message)}}],searchInfo:"",start_time_str:"",complete_time_str:"",message:"",visible:!1,dsData:{},state:n,stateList:[]}},mounted:function(){this.$route.params.dsData instanceof Object?(this.dsData=this.$route.params.dsData||{},this.loadData()):this.$router.push({name:"jobDatasourceConfig"})},methods:{loadData:function(){this.$refs.gridPager.loadData((function(t){}))},pageParams:function(){return{driver:this.dsData.driver,url:this.dsData.url,userName:this.dsData.userName,password:this.dsData.password,job_name:this.searchInfo,state:this.stateList[0],start_time_str:this.start_time_str,complete_time_str:this.complete_time_str}},onChange:function(t,a){this.start_time_str=a[0],this.complete_time_str=a[1],this.loadData()},showModel:function(t){this.visible=!0,this.message=t},fnBackToHome:function(){this.$router.push({name:"jobDatasourceConfig"})},filterClick:function(){this.loadData()},getContainer:function(){return document.getElementById("jobExecutionTrace")}}},c=i,l=e(1001),u=(0,l.Z)(c,s,o,!1,null,"5a2c5e6b",null),d=u.exports},95439:function(t,a){var e="/jobmg/elasticjob/jobDatasourceManagementRestService/";a["Z"]={addJobDatasource:function(t,a){Base.submit(null,{url:e+"addJobDatasource",data:t},{successCallback:function(t){return a(t)}})},deleteJobDatasource:function(t,a){Base.submit(null,{url:e+"deleteJobDatasource",data:t},{successCallback:function(t){return a(t)}})},getJobExecutionTrace:function(t,a){Base.submit(null,{url:e+"getJobExecutionTrace",data:t},{successCallback:function(t){return a(t)}})},getJobStatusTrace:function(t,a){Base.submit(null,{url:e+"getJobStatusTrace",data:t},{successCallback:function(t){return a(t)}})}}}}]);
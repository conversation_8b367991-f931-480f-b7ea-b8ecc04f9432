"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[1654],{12003:function(e,t,a){a.r(t),a.d(t,{default:function(){return L}});var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"fit"},[a("ta-border-layout",{attrs:{layout:{header:"70px",footer:"70px"},centerCfg:{showBar:!0,toolBarStyle:{height:"50px",lineHeight:"48px"}},"footer-cfg":{showBorder:!1}}},[a("div",{staticClass:"center-box",attrs:{slot:"header"},slot:"header"},[a("ta-input-search",{staticClass:"search-box",attrs:{placeholder:"输入系统名称/编码/拼音简写",enterButton:"搜索"},on:{search:e.querySystem},model:{value:e.search,callback:function(t){e.search=t},expression:"search"}})],1),a("div",{staticStyle:{float:"right"},attrs:{slot:"centerExtraContent"},slot:"centerExtraContent"},[a("ta-button",{attrs:{type:"primary"},on:{click:function(t){e.addVisible=!0}}},[e._v("新增")]),a("ta-button",{attrs:{disabled:!e.hasSelected},on:{click:function(t){e.deleteVisible=!0}}},[e._v("批量删除")])],1),a("ta-table",{attrs:{columns:e.columns,dataSource:e.tableData,rowKey:"id",rowSelection:{selectedRowKeys:e.selectedRowKeys,onChange:e.onSelectChange},pagination:!1},scopedSlots:e._u([{key:"effective",fn:function(t){return a("span",{},[e._v(e._s(e.CollectionLabel("EFFECTIVE",t)))])}},{key:"modifyTime",fn:function(t){return a("span",{},[e._v(e._s(e.fnDateFormate(t)))])}},e._l(["sysName","sysCode","backgroundAddress","address","portalSystem"],(function(t){return{key:t,fn:function(r,s,o){return["portalSystem"!=t?a("div",[s.editable?a("ta-input",{staticStyle:{margin:"-5px 0"},attrs:{value:r},on:{change:function(a){return e.handleChange(a.target.value,s.id,t)}}}):[e._v(e._s(r))]],2):e._e(),"portalSystem"==t?a("div",[s.editable?a("ta-select",{staticStyle:{margin:"-5px 0"},attrs:{defaultValue:"是"}},[a("ta-select-option",{attrs:{value:"1"}},[e._v("是")]),a("ta-select-option",{attrs:{value:"0"}},[e._v("否")])],1):[e._v(e._s(r))]],2):e._e()]}}})),{key:"operation",fn:function(t,r,s){return a("span",{},[a("ta-table-operate",{attrs:{operateMenu:e.operateMenu}})],1)}}],null,!0)}),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("ta-pagination",{ref:"accessGridPager",staticStyle:{float:"right","margin-top":"10px"},attrs:{showSizeChanger:"",showQuickJumper:"",dataSource:e.tableData,defaultPageSize:10,params:e.pageParams,url:"org/sysmg/accessSystemManagementRestService/queryAccessSystemByParam"},on:{"update:dataSource":function(t){e.tableData=t},"update:data-source":function(t){e.tableData=t}}})],1)],1),a("add-system",{attrs:{visible:e.addVisible},on:{close:function(t){e.addVisible=!1},queryTable:e.querySystem}}),a("edit-system",{attrs:{visible:e.editVisible,rowData:e.rowData},on:{close:function(t){e.editVisible=!1},editSuccess:e.editSuccess}}),a("ta-careful-delete",{attrs:{visible:e.deleteVisible,title:"接入系统删除",description:"所选系统"},on:{close:function(t){e.deleteVisible=!1},delete:e.deleteOrgBatch}})],1)},s=[],o=a(89584),l=a(95082),i=function(){var e=this,t=this,a=t.$createElement,r=t._self._c||a;return r("ta-drawer",{attrs:{"destroy-on-close":"",title:"新增接入系统",width:"500",placement:"right",closable:!0,visible:t.visible},on:{close:t.closeDrawer}},[r("ta-form",{attrs:{"auto-form-create":function(t){e.form=t}}},[r("ta-form-item",{attrs:{id:"sysName",label:"系统名称","label-col":t.formItemLayout.labelCol,"wrapper-col":t.formItemLayout.wrapperCol,"field-decorator-id":"sysName","field-decorator-options":{rules:[{required:!0,message:"请输入系统名称"},{max:10,message:"不能超过10个字符"}]}}},[r("ta-input",{attrs:{placeholder:"请输入接入系统名称"}})],1),r("ta-form-item",{attrs:{label:"系统自定义编码","label-col":t.formItemLayout.labelCol,"wrapper-col":t.formItemLayout.wrapperCol,"field-decorator-id":"sysCode","field-decorator-options":{rules:[{required:!0,message:"请输入系统标识"},{max:10,message:"不能超过10个字符"},{pattern:new RegExp("^[a-z0-9]*$"),message:"输入格式错误,只能有小写字母或数字"}]}}},[r("ta-input",{attrs:{placeholder:"请输入接入系统自定义编码"}})],1),r("ta-form-item",{attrs:{label:"协议","label-col":t.formItemLayout.labelCol,"wrapper-col":t.formItemLayout.wrapperCol,"field-decorator-id":"protocol","field-decorator-options":{rules:[{required:!0,message:"请输入协议"},{max:10,message:"不能超过5个字符"},{pattern:new RegExp("^http$|^https$"),message:"输入格式错误"}]}}},[r("ta-select",{attrs:{"default-value":"http",placeholder:"例如:http,https"}},[r("ta-select-option",{attrs:{value:"http"}},[t._v(" http ")]),r("ta-select-option",{attrs:{value:"https"}},[t._v(" https ")])],1)],1),r("ta-form-item",{attrs:{label:"前端地址","label-col":t.formItemLayout.labelCol,"wrapper-col":t.formItemLayout.wrapperCol,"field-decorator-id":"domain","field-decorator-options":{rules:[{required:!0,message:"请输入前端地址"},{max:100,message:"不能超过100个字符"},{pattern:new RegExp(/^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/),message:"输入格式错误"}]}}},[r("ta-input",{attrs:{placeholder:"例：127.0.0.1"}})],1),r("ta-form-item",{attrs:{label:"端口","label-col":t.formItemLayout.labelCol,"wrapper-col":t.formItemLayout.wrapperCol,"field-decorator-id":"port","field-decorator-options":{rules:[{required:!0,message:"请输入端口"},{pattern:new RegExp("^[0-9]{1,5}$"),message:"输入格式不正确"}]}}},[r("ta-input",{attrs:{placeholder:"例：8080"}})],1),r("ta-form-item",{attrs:{label:"后台地址","label-col":t.formItemLayout.labelCol,"wrapper-col":t.formItemLayout.wrapperCol,"field-decorator-id":"backgroundAddress","field-decorator-options":{rules:[{required:!0,message:"请输入后台地址"},{max:100,message:"不能超过100个字符"},{pattern:new RegExp(/^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\:([0-9]|[1-9]\d{1,3}|[1-5]\d{4}|6[0-5]{2}[0-3][0-5])$/),message:"输入格式错误"}]}}},[r("ta-input",{attrs:{placeholder:"例：127.0.0.1:8081"}})],1),r("ta-form-item",{attrs:{label:"菜单服务","label-col":t.formItemLayout.labelCol,"wrapper-col":t.formItemLayout.wrapperCol,"field-decorator-id":"menuService"}},[r("ta-input",{attrs:{placeholder:"请输入菜单服务"}})],1),r("ta-form-item",{attrs:{label:"上下文","label-col":t.formItemLayout.labelCol,"wrapper-col":t.formItemLayout.wrapperCol,"field-decorator-id":"contextPath","field-decorator-options":{rules:[{required:!0,message:"请输入上下文"},{max:10,message:"不能超过10个字符"},{pattern:new RegExp(/^[a-zA-Z][a-zA-Z\d]{0,9}$/),message:"请输入以英文字母开始的字符串"}]}}},[r("ta-input",{attrs:{placeholder:"请输入上下文"}})],1),r("ta-form-item",{attrs:{label:"系统提供者","label-col":t.formItemLayout.labelCol,"wrapper-col":t.formItemLayout.wrapperCol,"field-decorator-id":"provider","field-decorator-options":{rules:[{required:!0,message:"请输入系统提供者"},{max:10,message:"不能超过10个字符"}]}}},[r("ta-input",{attrs:{placeholder:"请输入系统提供者"}})],1),r("ta-form-item",{attrs:{label:"接入门户","label-col":t.formItemLayout.labelCol,"wrapper-col":t.formItemLayout.wrapperCol,"field-decorator-id":"portalSystem","field-decorator-options":{valuePropName:"checked",initialValue:!0}}},[r("ta-switch",{attrs:{"checked-children":"是","un-checked-children":"否"}})],1)],1),r("div",{attrs:{slot:"footer"},slot:"footer"},[r("ta-button-group",[r("ta-button",{on:{click:function(e){return t.form.resetFields()}}},[t._v(" 重置 ")]),r("ta-button",{attrs:{type:"primary"},on:{click:t.handleSave}},[t._v(" 保存 ")])],1)],1)],1)},n=[],d="/org/sysmg/accessSystemManagementRestService/",c={addTaAccessSystem:function(e,t){Base.submit(null,{url:d+"addTaAccessSystem",data:e},{successCallback:function(e){return t(e)}})},deleteTaAccessSystemById:function(e,t){Base.submit(null,{url:d+"deleteTaAccessSystemById",data:e},{successCallback:function(e){return t(e)}})},deleteBatchTaAccessSystemById:function(e,t){Base.submit(null,{url:d+"deleteBatchTaAccessSystemById",data:e},{successCallback:function(e){return t(e)}})},updateTaAccessSystemById:function(e,t){Base.submit(null,{url:d+"updateTaAccessSystemById",data:e},{successCallback:function(e){return t(e)}})}},u={name:"AddSystem",props:["visible"],data:function(){return{form:null,formItemLayout:{labelCol:{span:7},wrapperCol:{span:17}},isPortal:!0}},methods:{handleSave:function(){var e=this;this.form.validateFields((function(t,a){t||(a.effective="1",a.portalSystem=a.portalSystem?"1":"0",a.spell=TaUtils.pinyin.getCamelChars(a.sysName),c.addTaAccessSystem(a,(function(t){e.$emit("queryTable"),e.$message.success("新增成功"),e.closeDrawer()})))}))},closeDrawer:function(){this.$emit("close"),this.form.resetFields()}}},m=u,p=a(1001),f=(0,p.Z)(m,i,n,!1,null,"cbd4251a",null),h=f.exports,y=function(){var e=this,t=this,a=t.$createElement,r=t._self._c||a;return r("ta-drawer",{attrs:{destroyOnClose:"",title:"编辑接入系统",width:"500",placement:"right",closable:!0,visible:t.visible},on:{close:t.closeDrawer}},[r("ta-form",{attrs:{autoFormCreate:function(t){e.form=t}}},[r("ta-form-item",{attrs:{id:"sysName",label:"系统名称",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"sysName",fieldDecoratorOptions:{rules:[{required:!0,message:"请输入系统名称"},{max:10,message:"不能超过10个字符"}],initialValue:t.systemData.sysName}}},[r("ta-input",{attrs:{placeholder:"请输入接入系统名称"}})],1),r("ta-form-item",{attrs:{label:"系统自定义编码",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"sysCode",fieldDecoratorOptions:{rules:[{required:!0,message:"请输入系统标识"},{max:10,message:"不能超过10个字符"},{pattern:new RegExp("^[a-z0-9]*$"),message:"输入格式错误,只能有小写字母或数字"}],initialValue:t.systemData.sysCode}}},[r("ta-input",{attrs:{placeholder:"请输入接入系统自定义编码"}})],1),r("ta-form-item",{attrs:{label:"协议",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"protocol",fieldDecoratorOptions:{rules:[{required:!0,message:"请输入协议"},{max:10,message:"不能超过5个字符"},{pattern:new RegExp("^http$|^https$"),message:"输入格式错误"}],initialValue:t.systemData.protocol}}},[r("ta-select",{attrs:{defaultValue:"http",placeholder:"例如:http,https"}},[r("ta-select-option",{attrs:{value:"http"}},[t._v("http")]),r("ta-select-option",{attrs:{value:"https"}},[t._v("https")])],1)],1),r("ta-form-item",{attrs:{label:"前端地址",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"domain",fieldDecoratorOptions:{rules:[{required:!0,message:"请输入前端地址"},{max:100,message:"不能超过100个字符"},{pattern:new RegExp(/^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/),message:"输入格式错误"}],initialValue:t.systemData.domain}}},[r("ta-input",{attrs:{placeholder:"IP"}})],1),r("ta-form-item",{attrs:{label:"端口",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"port",fieldDecoratorOptions:{rules:[{required:!0,message:"请输入端口"},{pattern:new RegExp("^[0-9]{1,5}$"),message:"输入格式不正确"}],initialValue:t.systemData.port}}},[r("ta-input",{attrs:{placeholder:"请输入端口"}})],1),r("ta-form-item",{attrs:{label:"后台地址",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"backgroundAddress",fieldDecoratorOptions:{rules:[{required:!0,message:"请输入后台地址"},{max:100,message:"不能超过100个字符"},{pattern:new RegExp(/^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\:([0-9]|[1-9]\d{1,3}|[1-5]\d{4}|6[0-5]{2}[0-3][0-5])$/),message:"输入格式错误"}],initialValue:t.systemData.backgroundAddress}}},[r("ta-input",{attrs:{placeholder:"包括IP与端口"}})],1),r("ta-form-item",{attrs:{label:"上下文",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"contextPath",fieldDecoratorOptions:{rules:[{required:!0,message:"请输入上下文"},{max:10,message:"不能超过10个字符"},{pattern:new RegExp(/^[a-zA-Z][a-zA-Z\d]{0,9}$/),message:"请输入以英文字母开始的字符串"}],initialValue:t.systemData.contextPath}}},[r("ta-input",{attrs:{placeholder:"请输入上下文"}})],1),r("ta-form-item",{attrs:{label:"系统提供者",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"provider",fieldDecoratorOptions:{rules:[{required:!0,message:"请输入系统提供者"},{max:10,message:"不能超过10个字符"}],initialValue:t.systemData.provider}}},[r("ta-input",{attrs:{placeholder:"请输入系统提供者"}})],1)],1),r("div",{attrs:{slot:"footer"},slot:"footer"},[r("ta-button-group",[r("ta-button",{on:{click:function(e){return t.form.resetFields()}}},[t._v("重置")]),r("ta-button",{attrs:{type:"primary"},on:{click:t.onSubmitForm}},[t._v("保存")])],1)],1)],1)},b=[],g={name:"editSystem",props:["visible","rowData"],data:function(){return{formItemLayout:{labelCol:{span:7},wrapperCol:{span:17}},isPortal:!0,systemData:{}}},watch:{visible:function(e){e&&this.initForm()}},methods:{onResetForm:function(){this.initForm()},closeDrawer:function(){this.$emit("close"),this.systemData={}},initForm:function(){this.systemData=this.rowData},onSubmitForm:function(){var e=this;this.form.validateFields((function(t){if(!t){var a=e.form.getFieldsValue(),r=a;r.id=e.rowData.id,r.address=a.protocol+"://"+a.domain+":"+a.port+"/"+a.contextPath,c.updateTaAccessSystemById(r,(function(t){e.$message.success("编辑成功"),e.$emit("editSuccess",r)}))}}))}}},w=g,C=(0,p.Z)(w,y,b,!1,null,null,null),v=C.exports,S=[{title:"系统名称",dataIndex:"sysName",width:"20%",overflowTooltip:!0,scopedSlots:{customRender:"sysName"}},{title:"系统自定义编码",width:"15%",overflowTooltip:!0,dataIndex:"sysCode",scopedSlots:{customRender:"sysCode"}},{title:"后台地址",width:"20%",overflowTooltip:!0,dataIndex:"backgroundAddress",scopedSlots:{customRender:"backgroundAddress"}},{title:"系统地址",dataIndex:"address",width:"20%",overflowTooltip:!0,scopedSlots:{customRender:"address"},sorter:function(e,t){return e.address.length-t.address.length}},{title:"有效标识",width:"8%",dataIndex:"effective",key:"effective",yesOrNoTag:!0,scopedSlots:{customRender:"effective"}},{title:"操作",align:"center",width:"20%",dataIndex:"operation",scopedSlots:{customRender:"operation"}}],I=[],D={name:"accessSystem",data:function(){var e=this;return this.cacheData=I.map((function(e){return(0,l.Z)({},e)})),{portalSystem:"1",effective:"1",addVisible:!1,editVisible:!1,deleteVisible:!1,columns:S,operateMenu:[{name:"编辑",disabled:function(e){return"0"==e.effective},title:function(e){return"0"==e.effective?"禁用的接入系统不允许编辑":""},onClick:function(t){e.editAccessSystem(t)}},{name:"更多",type:"more",moreMenuList:[{name:"启用",type:"confirm",confirmTitle:"确定启用该接入系统?",onOk:function(t){e.editEffective(t,"1")}},{name:"禁用",type:"confirm",confirmTitle:"确定禁用该接入系统?",onOk:function(t){e.editEffective(t,"0")}},{name:"删除",type:"confirm",confirmTitle:"确定删除?",onOk:function(t){e.handleDelete(t.id)}}]}],tableData:I,selectedRowKeys:[],selectedRows:[],rowData:{},search:""}},components:{addSystem:h,editSystem:v},computed:{hasSelected:function(){return this.selectedRowKeys.length>0}},mounted:function(){this.querySystem()},methods:{editAccessSystem:function(e){this.editVisible=!0,this.rowData=e},pageParams:function(){return{sysCode:this.search,sysName:this.search,spell:this.search}},querySystem:function(){this.$refs.accessGridPager.loadData()},onSelectChange:function(e,t){this.selectedRows=t,this.selectedRowKeys=e},editSuccess:function(e){this.changeRowData(this.tableData,"id",e),this.editVisible=!1},changeRowData:function(e,t,a){for(var r=0;r<e.length;r++){if(e[r][t]==a[t]){e[r]=Object.assign({},e[r],a);break}e[r].children&&this.changeRowData(e[r].children,"id",a)}},fnCheckInput:function(e,t){if(this.$message.destroy(),"address"==e){var a=new RegExp("^(?:(?:2[0-4][0-9].)|(?:25[0-5].)|(?:1[0-9][0-9].)|(?:[1-9][0-9].)|(?:[0-9].)){3}(?:(?:2[0-5][0-5])|(?:25[0-5])|(?:1[0-9][0-9])|(?:[1-9][0-9])|(?:[0-9]))$|^(?=^.{3,255}$)(http(s)?://)?(www.)?[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+(:[0-9]+)?(/?[a-zA-Z0-9]+.?[a-zA-Z0-9]+)?$");if(!t.length)return this.$message.error("系统地址不能为空",5),!1;if(!a.test(t))return this.$message.error("系统地址格式不正确",5),!1}if("sysCode"==e){if(!/^[a-z0-9]*$/.test(t))return this.$message.error("格式不正确,接入系统标识只能为小写英文字母和数字组合",5),!1;if(!t.length)return this.$message.error("系统自定义编码不能为空",5),!1}return"backgroundAddress"!=e||t.length?!("sysName"==e&&!t.length)||(this.$message.error("系统名称不能为空",5),!1):(this.$message.error("后台地址不能为空",5),!1)},fnCheckAllInput:function(e){var t=this,a={};try{e.forEach((function(e){Object.keys(e).forEach((function(r){var s=t.fnCheckInput(r,e[r]);if(!s)throw a}))}))}catch(r){return!1}return!0},handleChange:function(e,t,a){this.fnCheckInput(a,e);var r=(0,o.Z)(this.tableData),s=r.filter((function(e){return t===e.id}))[0];s&&(s[a]=e,this.tableData=r)},handleDelete:function(e){var t=this;c.deleteTaAccessSystemById({id:e},(function(e){t.$message.success("删除成功"),t.querySystem()}))},fnDateFormate:function(e){var t=new Date(e);return t.getFullYear()+"-"+(t.getMonth()+1)+"-"+t.getDate()+" "+t.getHours()+":"+t.getMinutes()+":"+t.getSeconds()},editEffective:function(e,t){var a=this;if(e.effective!=t){var r=(0,l.Z)({},e);r.effective=t,c.updateTaAccessSystemById(r,(function(e){a.$message.success(("1"==t?"启用":"禁用")+"成功"),a.querySystem()}))}else this.$message.warning("该记录已经"+("1"==t?"启用":"禁用")+"，请勿重复操作！")},deleteOrgBatch:function(){var e=this;c.deleteBatchTaAccessSystemById({id:this.selectedRowKeys.join(",")},(function(t){e.$message.success("批量删除成功"),e.querySystem(),e.selectedRowKeys=[],e.deleteVisible=!1}))}}},x=D,k=(0,p.Z)(x,r,s,!1,null,"d773986e",null),L=k.exports}}]);
"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[3544],{39192:function(t,e,a){a.r(e),a.d(e,{default:function(){return T}});var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"fit",attrs:{id:"tagManagement"}},[a("ta-border-layout",{attrs:{layout:{footer:"70px"},centerCfg:{showBar:!0,toolBarStyle:{height:"50px"}},"footer-cfg":{showBorder:!1}}},[a("div",{attrs:{slot:"centerExtraContent"},slot:"centerExtraContent"},[a("ta-tag-select",{attrs:{title:"标签组",data:t.tagGroup<PERSON>ist,props:t.tag<PERSON><PERSON>,required:""},on:{change:t.filterClick},model:{value:t.tagFilter,callback:function(e){t.tagFilter=e},expression:"tagFilter"}}),a("ta-tag-select",{staticClass:"filter-name",attrs:{title:"有效标识",data:t.CollectionData("EFFECTIVE")},on:{change:t.filterClick},model:{value:t.effectiveFilter,callback:function(e){t.effectiveFilter=e},expression:"effectiveFilter"}}),a("div",{staticStyle:{float:"right"}},[a("ta-button",{attrs:{type:"primary"},on:{click:function(e){return t.filterClick()}}},[t._v("刷新")]),a("ta-button",{attrs:{type:"primary"},on:{click:function(e){return t.fnEditTag()}}},[t._v("新增标签")]),a("ta-dropdown",{attrs:{trigger:["click"]}},[a("ta-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("ta-menu-item",{attrs:{disabled:!t.selectedRowKeys.length}},[a("ta-popconfirm",{attrs:{title:"确认启用所选标签?",cancelText:"取消",okText:"确认"},on:{confirm:function(e){return t.changeTagStatus("1")}}},[a("ta-icon",{attrs:{type:"check-circle"}}),a("span",{staticClass:"mg-l12"},[t._v("启用")])],1)],1),a("ta-menu-divider"),a("ta-menu-item",{attrs:{disabled:!t.selectedRowKeys.length}},[a("ta-popconfirm",{attrs:{title:"确认禁用所选标签?",cancelText:"取消",okText:"确认"},on:{confirm:function(e){return t.changeTagStatus("0")}}},[a("ta-icon",{attrs:{type:"stop"}}),a("span",{staticClass:"mg-l12"},[t._v("禁用")])],1)],1),a("ta-menu-divider"),a("ta-menu-item",{attrs:{disabled:!t.selectedRowKeys.length},on:{click:function(e){t.deleteVisible=!0}}},[a("ta-icon",{staticStyle:{"margin-right":"20px"},attrs:{type:"close-circle"}}),t._v("删除 ")],1)],1),a("ta-button",[t._v("批量操作"),a("ta-icon",{attrs:{type:"down"}})],1)],1)],1)],1),a("ta-table",{attrs:{rowSelection:{selectedRowKeys:t.selectedRowKeys,onChange:t.onSelectChange},rowKey:"tagId",columns:t.tagColumns,dataSource:t.tagData,pagination:!1},scopedSlots:t._u([{key:"createTime",fn:function(e,a){return[t._v(t._s(t.moment(a.createTime).format("YYYY-MM-DD")))]}},{key:"tagName",fn:function(e,i){return[a("span",{class:{invalidStyle:"0"===i.effective}},[t._v(t._s(e))])]}},{key:"effective",fn:function(e){return a("span",{},[t._v(t._s(t.CollectionLabel("EFFECTIVE",e)))])}},{key:"action",fn:function(e,i){return a("span",{},[a("ta-table-operate",{attrs:{operateMenu:t.operateMenu}})],1)}}])}),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("ta-pagination",{ref:"tagPager",staticStyle:{float:"right","margin-top":"10px"},attrs:{showSizeChanger:"",showQuickJumper:"",dataSource:t.tagData,defaultPageSize:10,params:t.tagPageParams,url:t.tagTableUrl},on:{"update:dataSource":function(e){t.tagData=e},"update:data-source":function(e){t.tagData=e}}})],1)],1),a("edit-tag",{attrs:{tag:t.tagRowData,tagGroupList:t.tagGroupList,visible:t.visible},on:{close:function(e){t.visible=!1},search:t.filterClick}}),a("ta-careful-delete",{attrs:{visible:t.deleteVisible,title:"标签删除",description:"选中标签"},on:{close:function(e){t.deleteVisible=!1},delete:t.tagBatchDelete}})],1)},o=[],r="/org/sysmg/tagManagementRestService/",n={queryTagByTagGroupId:function(t,e){Base.submit(null,{url:r+"queryTag",data:t},{successCallback:function(t){return e(t)}})},queryTagByCondition:function(t,e){Base.submit(null,{url:r+"queryTagByCondition",data:t},{successCallback:function(t){return e(t)}})},queryAllTagGroup:function(t,e){Base.submit(null,{url:r+"queryAllTagGroup",data:t},{successCallback:function(t){return e(t)}})},updateBatchTagStatus:function(t,e){Base.submit(null,{url:r+"updateBatchTagStatus",data:t},{successCallback:function(t){return e(t)}})},deleteBatchTag:function(t,e){Base.submit(null,{url:r+"deleteBatchTag",data:t},{successCallback:function(t){return e(t)}})},addTag:function(t,e){Base.submit(null,{url:r+"addTag",data:t},{successCallback:function(t){return e(t)}})},updateTag:function(t,e){Base.submit(null,{url:r+"updateTag",data:t},{successCallback:function(t){return e(t)}})}},s=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("ta-drawer",{attrs:{"destroy-on-close":!0,width:"500",title:e.tag.tagId?"编辑标签":"新增标签",placement:"right",closable:!0,visible:e.visible,"get-container":!1},on:{close:e.closeDrawer}},[i("ta-form",{attrs:{"auto-form-create":function(e){t.form=e}}},[i("ta-form-item",{attrs:{label:"标签名称","label-col":e.formItemLayout.labelCol,"wrapper-col":e.formItemLayout.wrapperCol,"field-decorator-id":"tagName","field-decorator-options":{rules:[{required:!0,message:"标签名称不能为空"}],initialValue:e.formData.tagName}}},[i("ta-input",{attrs:{placeholder:"请输入标签名称"}})],1),i("ta-form-item",{attrs:{label:"标签组","label-col":e.formItemLayout.labelCol,"wrapper-col":e.formItemLayout.wrapperCol,"field-decorator-id":"tagGroupId","field-decorator-options":{rules:[{required:!0,message:"标签组不能为空"}],initialValue:e.formData.tagGroupId}}},[i("ta-select",{attrs:{placeholder:"请选择标签组",disabled:void 0!==e.tag.tagId}},e._l(e.tagGroupList,(function(t,a){return i("ta-select-option",{key:a,attrs:{value:t.tagGroupId}},[e._v(" "+e._s(t.tagGroupName)+" ")])})),1)],1),i("ta-form-item",{attrs:{label:"有效标识","label-col":e.formItemLayout.labelCol,"wrapper-col":e.formItemLayout.wrapperCol,"field-decorator-id":"effective","field-decorator-options":{valuePropName:"checked",initialValue:e.formData.effective}}},[i("ta-switch",{attrs:{"checked-children":"有效","un-checked-children":"无效"}})],1)],1),i("div",{attrs:{slot:"footer"},slot:"footer"},[i("ta-button-group",[i("ta-button",{on:{click:function(t){return e.form.resetFields()}}},[e._v(" 重置 ")]),i("ta-button",{attrs:{type:"primary"},on:{click:e.fnSave}},[e._v(" 保存 ")])],1)],1)],1)},l=[],c={name:"editTag",props:["visible","tag","tagGroupList"],data:function(){return{formItemLayout:{labelCol:{span:6},wrapperCol:{span:18}},formData:{}}},watch:{visible:function(t){t&&this.setValue()}},methods:{setValue:function(){var t=this.tag,e=t.tagId,a=t.tagGroupId,i=t.effective,o=t.tagName;this.formData=void 0==e?{effective:!0,tagGroupId:a}:{tagName:o,effective:"1"==i,tagGroupId:a}},fnSave:function(){var t=this;this.form.validateFields((function(e,a){e||(a.effective=a.effective?"1":"0",void 0==t.tag.tagId?n.addTag(a,(function(e){t.$message.success("新增标签成功"),t.$emit("search",{tagGroupId:a.tagGroupId}),t.closeDrawer()})):(a.tagId=t.tag.tagId,n.updateTag(a,(function(e){t.$message.success("修改标签成功"),t.$emit("search",{tagGroupId:a.tagGroupId}),t.closeDrawer()}))))}))},closeDrawer:function(){this.formData={},this.$emit("close")}}},u=c,f=a(1001),d=(0,f.Z)(u,s,l,!1,null,null,null),g=d.exports,m=a(36797),p=a.n(m),h=[{title:"标签名称",width:"20%",overflowTooltip:!0,dataIndex:"tagName",key:"tagName",scopedSlots:{customRender:"tagName"}},{title:"标签组",width:"20%",dataIndex:"tagGroupName",key:"tagGroupName"},{title:"创建时间",width:"20%",overflowTooltip:!0,dataIndex:"createTime",key:"createTime",scopedSlots:{customRender:"createTime"}},{title:"有效标识",width:"20%",dataIndex:"effective",key:"effective",yesOrNoTag:!0,scopedSlots:{customRender:"effective"}},{title:"操作",key:"operation",width:"20%",align:"center",scopedSlots:{customRender:"action"}}],v="002",y={name:"tagManagement",components:{EditTag:g},data:function(){var t=this;return{tagGroupList:[],tagColumns:h,tagTableUrl:"org/sysmg/tagManagementRestService/queryTagByCondition",operateMenu:[{name:"编辑",disabled:function(t){return"0"==t.effective},title:function(t){return"0"==t.effective?"禁用的标签不允许编辑":""},onClick:function(e){t.fnEditTag(e)}},{name:"更多",type:"more",moreMenuList:[{name:"启用",type:"confirm",confirmTitle:"确认启用该标签?",onOk:function(e){t.changeTagStatus("1",e)}},{name:"禁用",type:"confirm",confirmTitle:"确认禁用该标签?",onOk:function(e){t.changeTagStatus("0",e)}},{name:"删除",type:"confirm",confirmTitle:"确认删除该标签?",onOk:function(e){t.fnTagRecordDelete(e)}}]}],tagData:[],tagRowData:{},visible:!1,selectedRowKeys:[],selectedRows:[],tagFilter:[],effectiveFilter:[],tagProps:{value:"tagGroupId",label:"tagGroupName"},deleteVisible:!1}},mounted:function(){this.fnQueryAllTagGroup(),this.filterClick()},methods:{moment:p(),tagPageParams:function(){return{tagGroupId:this.tagFilter[0],effective:this.effectiveFilter.join(",")}},fnQueryAllTagGroup:function(){var t=this;n.queryAllTagGroup(null,(function(e){t.tagGroupList=e.data.tagGroupList,t.tagFilter=[v]}))},filterClick:function(){this.$refs.tagPager.loadData()},onSelectChange:function(t,e){this.selectedRowKeys=t,this.selectedRows=e},changeTagStatus:function(t,e){var a=this,i={};if(e){if(e.effective==t)return void this.$message.warning("该记录已经"+("0"==t?"禁用":"启用")+"，请勿重复操作！");i={tagIds:e.tagId,effective:t}}else{var o=this.selectedRows.filter((function(e){return e.effective!=t}));if(0==o.length)return void this.$message.warning("所选记录已经全部"+("0"==t?"禁用":"启用")+"，请勿重复操作！");var r=o.map((function(t){return t.tagId}));i={tagIds:r.join(","),effective:t}}n.updateBatchTagStatus(i,(function(i){e?(a.$message.success("1"==t?"启用成功":"禁用成功"),e.effective=t):(a.$message.success("1"==t?"批量启用成功":"批量禁用成功"),a.selectedRows.map((function(e){e.effective=t})),a.selectedRowKeys=[],a.selectedRows=[]),a.filterClick()}))},fnTagRecordDelete:function(t){var e=this;n.deleteBatchTag({tagIds:t.tagId},(function(a){e.$message.success("删除成功"),e.tagData=e.tagData.filter((function(e){return e.tagId!==t.tagId})),e.selectedRows=[],e.selectedRowKeys=[]}))},tagBatchDelete:function(){var t=this;n.deleteBatchTag({tagIds:this.selectedRowKeys.join(",")},(function(e){t.$message.success("批量删除成功");for(var a=function(e){t.tagData=t.tagData.filter((function(a){return a.tagId!==t.selectedRows[e].tagId}))},i=0;i<t.selectedRows.length;i++)a(i);t.deleteVisible=!1,t.selectedRowKeys=[],t.selectedRows=[]}))},fnEditTag:function(t){t?this.tagRowData=t:(this.tagRowData={},this.tagRowData.tagGroupId=this.tagFilter[0]),this.visible=!0}}},b=y,w=(0,f.Z)(b,i,o,!1,null,"9858099a",null),T=w.exports}}]);
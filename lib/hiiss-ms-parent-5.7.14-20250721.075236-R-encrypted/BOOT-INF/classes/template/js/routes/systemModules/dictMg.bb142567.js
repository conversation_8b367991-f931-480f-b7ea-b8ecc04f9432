"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[8471,9953],{41410:function(t,e,i){i.r(e),i.d(e,{default:function(){return p}});var s=function(){var t=this,e=this,i=e.$createElement,s=e._self._c||i;return s("div",{staticClass:"fit",attrs:{id:"dictMg"}},[s("ta-border-layout",{attrs:{layout:{header:"70px",footer:"70px"},"center-cfg":{showBar:!0,toolBarStyle:{height:"50px",lineHeight:"48px"}},"footer-cfg":{showBorder:!1}}},[s("div",{staticClass:"center-box",attrs:{slot:"header"},slot:"header"},[s("ta-input-search",{staticClass:"search-box",attrs:{placeholder:"根据字典名称、字典类型","enter-button":"搜索"},on:{search:e.queryDictType},model:{value:e.typeInfo,callback:function(t){e.typeInfo=t},expression:"typeInfo"}})],1),s("div",{attrs:{slot:"centerExtraContent"},slot:"centerExtraContent"},[s("div",{staticStyle:{float:"right"}},[s("ta-button",{attrs:{type:"primary"},on:{click:e.fnToAddDictType}},[e._v(" 新增类型 ")]),s("ta-dropdown",[s("ta-menu",{attrs:{slot:"overlay"},slot:"overlay"},[s("ta-menu-item",{attrs:{disabled:e.btnDisable}},[s("ta-popconfirm",{attrs:{title:"确认启用所选字典类型?","cancel-text":"取消","ok-text":"确认"},on:{confirm:function(t){return e.fnStartDictByTypes(!1)}}},[s("ta-icon",{attrs:{type:"check-circle"}}),s("span",{staticClass:"mg-l12"},[e._v("启用")])],1)],1),s("ta-menu-divider"),s("ta-menu-item",{attrs:{disabled:e.btnDisable}},[s("ta-popconfirm",{attrs:{title:"确认禁用所选字典类型?","cancel-text":"取消","ok-text":"确认"},on:{confirm:function(t){return e.fnStopDictByTypes(!1)}}},[s("ta-icon",{attrs:{type:"stop"}}),s("span",{staticClass:"mg-l12"},[e._v("禁用")])],1)],1),s("ta-menu-divider"),s("ta-menu-item",{attrs:{disabled:e.btnDisable},on:{click:function(t){e.deleteVisible=!0}}},[s("ta-icon",{staticStyle:{"margin-right":"20px"},attrs:{type:"close-circle"}}),e._v("删除 ")],1)],1),s("ta-button",[e._v(" 批量操作 "),s("ta-icon",{attrs:{type:"down"}})],1)],1),s("ta-button",{on:{click:e.fnRefreshCaches}},[e._v(" 刷新缓存 ")])],1)]),s("ta-table",{ref:"typeGrid",attrs:{columns:e.dictTypeColumns,"data-source":e.typeGridData,"row-key":e.getRowKey,"row-selection":{selectedRowKeys:e.selectedRowKeys,onChange:e.fnOnChange},pagination:!1},scopedSlots:e._u([{key:"system",fn:function(t){return s("span",{},[e._v(e._s(e.CollectionLabel("YESORNO",t)))])}},{key:"authority",fn:function(t){return s("span",{},[e._v(e._s(e.getAuthorityLabel(t)))])}},{key:"action",fn:function(t,i){return s("span",{},[s("ta-table-operate",{attrs:{"operate-menu":e.operateMenu}})],1)}}])}),s("div",{attrs:{slot:"footer"},slot:"footer"},[s("ta-pagination",{ref:"gridPager",staticStyle:{float:"right","margin-top":"10px"},attrs:{"show-size-changer":"","show-quick-jumper":"","data-source":e.typeGridData,"default-page-size":10,params:e.pageParams,url:"dictmg/dictMgRestService/queryType"},on:{"update:dataSource":function(t){e.typeGridData=t},"update:data-source":function(t){e.typeGridData=t}}})],1)],1),s("ta-drawer",{attrs:{"destroy-on-close":!0,title:"新增字典类型",width:"500",placement:"right",closable:"",visible:e.typeDrawerVisible},on:{close:e.fnTypeDrawCloseEvent}},[s("ta-form",{attrs:{"auto-form-create":function(e){t.form=e}}},[s("ta-form-item",{attrs:{label:"字典类型","label-col":e.formItemLayout.labelCol,"wrapper-col":e.formItemLayout.wrapperCol,"field-decorator-id":"type","init-value":e.typeFormData.type,"field-decorator-options":{rules:[{required:!0,message:"请输入字典类型"}]}}},[s("ta-input")],1),s("ta-form-item",{attrs:{label:"字典名称","label-col":e.formItemLayout.labelCol,"wrapper-col":e.formItemLayout.wrapperCol,"field-decorator-id":"name","init-value":e.typeFormData.name,"field-decorator-options":{rules:[{required:!0,message:"请输入字典名称"}]}}},[s("ta-input")],1),s("ta-form-item",{attrs:{label:"权限标识","label-col":e.formItemLayout.labelCol,"wrapper-col":e.formItemLayout.wrapperCol,"field-decorator-id":"authority","init-value":e.typeFormData.authority,"field-decorator-options":{rules:[{required:!0,message:"请选择所属权限标识"}]}}},[s("ta-select",{attrs:{placeholder:"请选择权限标识"}},e._l(e.authorityList,(function(t){return s("ta-select-option",{key:t.value},[e._v(" "+e._s(t.label)+" ")])})),1)],1)],1),s("div",{attrs:{slot:"footer"},slot:"footer"},[s("ta-button-group",[s("ta-button",{on:{click:function(t){return e.form.resetFields()}}},[e._v(" 重置 ")]),s("ta-button",{attrs:{type:"primary"},on:{click:e.saveType}},[e._v(" 保存 ")])],1)],1)],1),s("ta-drawer",{attrs:{"destroy-on-close":"",title:"新增字典",width:"500",placement:"right",closable:"","footer-height":"",visible:e.dictAddOrEditVisible},on:{close:e.fnDictAddOrEditDrawerClose}},[s("div",{attrs:{slot:"footer"},slot:"footer"},[s("ta-button-group",[s("ta-button",{on:{click:e.resetForm}},[e._v(" 重置 ")]),s("ta-button",{attrs:{type:"primary"},on:{click:e.saveDict}},[e._v(" 保存 ")])],1)],1),s("system-dict-mg",{ref:"sysDictMgChild",attrs:{dict:e.dict},on:{closeSystemDictMgDrawer:e.fnDictAddOrEditDrawerClose}})],1),s("ta-careful-delete",{attrs:{visible:e.deleteVisible,title:"字典类型删除",description:"所选字典类型"},on:{close:function(t){e.deleteVisible=!1},delete:e.fnDeleteDictByTypes}})],1)},o=[],a=i(78746),r=(i(51999),i(26682)),n={labelCol:{span:6},wrapperCol:{span:18}},c=[{title:"字典名称",dataIndex:"name",key:"name",width:"25%",overflowTooltip:!0},{title:"字典类型",dataIndex:"type",key:"type",width:"30%",overflowTooltip:!0},{title:"权限标识",dataIndex:"authority",width:"10%",scopedSlots:{customRender:"authority"}},{title:"系统字典",dataIndex:"system",scopedSlots:{customRender:"system"},width:"10%"},{title:"操作",dataIndex:"typeAction",align:"center",scopedSlots:{customRender:"action"},width:"20%"}],l={name:"DictMg",components:{systemDictMg:a.Z},data:function(){var t=this;return{dictTypeColumns:c,operateMenu:[{name:"新增字典",disabled:function(t){return"0"==t.system},title:function(t){return"0"==t.system?"存在系统字典，不能新增字典!":""},onClick:function(e){t.fnToAddDictByType(e)}},{name:"查看",onClick:function(e){t.fnRouteToDictTypeMg(e)}},{name:"更多",type:"more",moreMenuList:[{name:"启用",type:"confirm",confirmTitle:"确认启用该字典类型?",onOk:function(e){t.fnStartDictByTypes(e)},title:function(t){return"0"==t.system?"存在系统字典不能启用!":""},disabled:function(t){return"0"==t.system}},{name:"禁用",type:"confirm",confirmTitle:"确认禁用该字典类型?",title:function(t){return"0"==t.system?"存在系统字典不能禁用!":""},onOk:function(e){t.fnStopDictByTypes(e)},disabled:function(t){return"0"==t.system}},{name:"删除",type:"confirm",confirmTitle:"确定删除该字典类型?",onOk:function(e){t.fnDeleteDictByType(e)},title:function(t){return"0"==t.system?"存在系统字典不能删除!":""},disabled:function(t){return"0"==t.system}}]}],typeGridData:[],dict:{},dictAddOrEditVisible:!1,formItemLayout:n,typeInfo:"",dictQueryParam:{},typeDrawerVisible:!1,dictStatus:!0,dictNameDisable:!1,parentValueSelect:!0,dictContentModal:!1,selectedType:{},selectedRows:[],selectedRowKeys:[],operateType:"",keyJoin:".",keySeparater:",",typeFormData:{type:"",name:"",authority:""},dictContentGridData:[],parentValueList:[],authorityList:[],deleteVisible:!1}},mounted:function(){this.queryDictType(),this.queryDictAuthorityList()},computed:{btnDisable:function(){return!this.selectedRowKeys.length}},methods:{getRowKey:function(t){return t.name+t.type+t.authority},queryDictAuthorityList:function(){var t=this;r.Z.queryDictAuthorityList({},(function(e){t.authorityList=e.data.list}))},getAuthorityLabel:function(t){var e=null;"0"==t&&(e="默认");var i=this.authorityList;return i.forEach((function(i){t==i.value&&(e=i.label)})),null===e&&(e="其它"),e},pageParams:function(){var t={typeInfo:this.typeInfo};return t},queryDictType:function(){var t=this;this.$refs.gridPager.loadData((function(e){t.selectedRowKeys=[],t.selectedRows=[]}))},fnOnChange:function(t,e){this.selectedRowKeys=t,this.selectedRows=e},fnToAddDictByType:function(t){this.dict=t,this.dict.drawerType="add",this.dictAddOrEditVisible=!0},queryDictByType:function(t){this.dict=t,this.dictContentModal=!0},fnDeleteDictByType:function(t){var e=this,i=t.type+this.keyJoin+t.authority;r.Z.deleteDictByType({keys:i},(function(t){e.$message.success("删除成功！"),e.queryDictType()}))},resetForm:function(){this.$refs.sysDictMgChild.resetForm()},saveDict:function(){this.$refs.sysDictMgChild.saveDict()},fnToAddDictType:function(){this.typeDrawerVisible=!0},saveType:function(){var t=this,e=this.form.getFieldsValue();r.Z.saveType(e,(function(e){t.typeDrawerVisible=!1,t.$message.success("新增类型成功！"),t.queryDictType()}))},fnRefreshCaches:function(){var t=this;if(this.Base.submit(null,{url:"miimCommonRead/refreshAllDictCache",data:{},autoValid:!1},{successCallback:function(e){t.$message.success("Audit缓存刷新成功")},failCallback:function(e){t.$message.error("Audit缓存刷新失败")}}),0!=this.selectedRows.length){var e=this.selectedRows.map((function(t){return t.type+"."+t.authority})),i={keys:e.join(",")};r.Z.refreshDictCacheByType(i,(function(i){Base.removeCodeCache(e),t.$message.success("同步成功！"),t.queryDictType()}))}else this.$message.warning("请选择需要同步缓存的字典类型！")},fnDeleteDictByTypes:function(){for(var t=this,e=0;e<this.selectedRows.length;e++)if("0"==this.selectedRows[e].system)return void this.$message.warning("存在非系统字典，不能进行批量删除操作！");var i=this.selectedRows.map((function(t){return t.type+"."+t.authority})),s={keys:i.join(",")};r.Z.deleteDictByType(s,(function(e){t.$message.success("删除数据成功"),t.selectedRows=[],t.deleteVisible=!1,t.queryDictType()}))},fnStartDictByTypes:function(t){var e,i,s=this;if(t)i={keys:t.type+"."+t.authority};else{for(var o=0;o<this.selectedRows.length;o++)if("0"==this.selectedRows[o].system)return void this.$message.warning("存在非系统字典，不能进行批量启用操作！");e=this.selectedRows.map((function(t){return t.type+"."+t.authority})),i={keys:e.join(",")}}r.Z.startDictByType(i,(function(t){s.$message.success("启用数据成功"),s.selectedRows=[],s.queryDictType()}))},fnStopDictByTypes:function(t){var e,i,s=this;if(t)i={keys:t.type+"."+t.authority};else{for(var o=0;o<this.selectedRows.length;o++)if("0"==this.selectedRows[o].system)return void this.$message.warning("存在非系统字典，不能进行批量启用操作！");e=this.selectedRows.map((function(t){return t.type+"."+t.authority})),i={keys:e.join(",")}}r.Z.stopDictByType(i,(function(t){s.$message.success("禁用数据成功"),s.selectedRows=[],s.queryDictType()}))},fnDictAddOrEditDrawerClose:function(){this.operateType="",this.dictNameDisable=!1,this.dictFormData={},this.dictAddOrEditVisible=!1},fnDictModalCloseEvent:function(){this.dictContentModal=!1,this.selectedType={}},fnTypeDrawCloseEvent:function(t){this.typeDrawerVisible=!1},fnRouteToDictTypeMg:function(t){this.$router.push({name:"dictTypeMg",params:{dict:t}})}}},u=l,d=i(1001),y=(0,d.Z)(u,s,o,!1,null,"8803e706",null),p=y.exports}}]);
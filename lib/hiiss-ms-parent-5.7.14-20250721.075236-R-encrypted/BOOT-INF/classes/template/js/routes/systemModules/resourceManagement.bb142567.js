"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[7635],{80715:function(e,t,r){r.d(t,{Z:function(){return b}});var o,a,i=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("ta-select",{attrs:{mode:"multiple",placeholder:e.placeholder,options:e.CollectionData(e.collection),disabled:e.disabled,"allow-clear":e.allowClear},on:{change:e.handleChange},model:{value:e.selectValue,callback:function(t){e.selectValue=t},expression:"selectValue"}})},s=[],n=r(98754),l=r(41538),c={name:"SelectMultiple",props:["collection","value","disabled","placeholder","allowClear"],data:function(){return{selectValue:[]}},watch:{value:{immediate:!0,handler:function(e,t){(0,n.Z)(e)&&""!==e.trim()?this.selectValue=e.split(","):this.selectValue=[]}}},methods:{handleChange:function(e){(0,l.Z)(e)||(e=[]),this.$emit("input",e.join(",")),this.$emit("change",e.join(","))}}},u=c,d=r(1001),_=(0,d.Z)(u,i,s,!1,null,"5c5ae317",null),m=_.exports,f=r(72610),h=(r(26227),r(8145)),p={ADD:"ADD",EDIT:"EDIT",SHOW:"SHOW"},y={components:{selectMultiple:m,taSensitiveInput:f.Z},props:{renderType:{type:String,default:p.ADD},renderProp:{type:Object,default:function(){return{}}},showValues:{type:Object,default:function(){return{}}},simpleShowSlot:{type:Array,default:function(){return[]}},formSetting:{required:!0,type:Object,default:function(){return{}}},isShowParentItem:{type:Boolean,default:!0}},data:function(){return{}},methods:{buildItemShowContext:function(e){var t=this,r=this.$slots.default,o=this.formSetting.formItem,a=this.formSetting.formId||"",i=this.showValues[a];switch(e){case"slot":return-1===this.simpleShowSlot.indexOf(a)?r:i;case"select":case"radio":case"radioButton":return this.CollectionLabel(o.collection,i);case"select-multiple":return(0,n.Z)(i)?i.split(",").map((function(e){return t.CollectionLabel(o.collection,e)})).join(","):i;case"sensitive-input":var s=(0,n.Z)(i)?JSON.parse(i):i;return(0,h.Z)(s)?s.sensitiveField:i;default:return i}},buildItemContext:function(e){var t,r=this.$createElement,o=this.$slots.default,a=this.formSetting.disabled,i=this.formSetting.formItem,s=this.formSetting.formId,n=this.formSetting.label,l=this.formSetting.placeholder,c=this.renderType,u=this.renderProp;switch(e){case"slot":return o;case"select":return r("ta-select",{attrs:{placeholder:l,disabled:a,allowClear:!0,"collection-type":i.collection}});case"select-multiple":return r("select-multiple",{attrs:{placeholder:l,collection:i.collection,disabled:a,allowClear:!0}});case"radio":return r("ta-radio-group",{attrs:{disabled:a,"collection-type":i.collection}});case"radioButton":return r("ta-radio-group",{class:"lalal",attrs:{buttonStyle:"solid",disabled:a}},[null===(t=this.CollectionData(i.collection))||void 0===t?void 0:t.map((function(e){var t=e.label,o=e.value;return r("ta-radio-button",{key:o,attrs:{value:o}},[t])}))]);case"sensitive-input":return r("ta-sensitive-input",{attrs:{inputKey:s,placeholder:l,description:n,"auth-user":c===p.EDIT,authRequest:u.authRequest}});default:return r("ta-input",{attrs:{placeholder:l,disabled:a}})}}},render:function(){var e=arguments[0],t=this.renderType,r=this.formSetting.class||"",o=this.formSetting.formId||"",a=this.formSetting.label,i=this.formSetting.decoratorOptions;if(i&&i.rules){var s=i.rules;s.map((function(e){"number"===e.type&&(e.transform=function(e){return Number(e)},e.message="请输入数字")}))}var n=this.formSetting.formItemLayout,l=this.formSetting.formItem,c=!1!==this.formSetting.display;return c||(r+=" displayNone"),"pResourceName"!==this.formSetting.formId||this.isShowParentItem||(r+=" displayNone"),t===p.SHOW?e("ta-form-item",{attrs:{label:a,className:r,labelCol:n.labelCol,wrapperCol:n.wrapperCol}},[this.buildItemShowContext(l.type)]):e("ta-form-item",{attrs:{label:a,labelCol:n.labelCol,wrapperCol:n.wrapperCol,fieldDecoratorId:o,fieldDecoratorOptions:i},class:r},[this.buildItemContext(l.type)])}},v=y,g=(0,d.Z)(v,o,a,!1,null,"7322d522",null),b=g.exports},66360:function(e,t,r){r.d(t,{Z:function(){return _}});var o=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("span",[r("ta-popover",{attrs:{placement:"top",title:"选择图标",width:"300px",trigger:"manual",value:e.showIconList}},[r("div",{staticClass:"content",attrs:{slot:"content"},slot:"content"},[r("ta-row",e._l(e.outLineIconList,(function(t,o){return r("ta-col",{key:o,class:[{"icon-box":!0},{selected:e.value===t}],attrs:{span:8},on:{click:function(r){return e.select(t)}}},[r("ta-icon",{attrs:{type:t}})],1)})),1)],1),r("ta-input",{staticStyle:{width:"250px"},attrs:{slot:"reference","allow-clear":"",value:e.value,placeholder:"请选择图标.2.."},on:{focus:e.focus,blur:e.blur,inputChange:e.change},slot:"reference"})],1),e.value?r("ta-icon",{staticStyle:{"margin-left":"10px"},attrs:{type:e.value}}):e._e()],1)},a=[],i=r(16521),s=[];for(var n in i)"outline"===i[n].theme&&s.push(i[n].name);var l={name:"IconFormItem",props:{value:{type:String,default:""}},data:function(){return{outLineIconList:s,showIconList:!1}},methods:{select:function(e){this.$emit("change",e)},change:function(e){this.$emit("change",e.target.value)},focus:function(){this.showIconList=!0},blur:function(e){this.$emit("change",e.target.value),this.showIconList=!1}}},c=l,u=r(1001),d=(0,u.Z)(c,o,a,!1,null,"bc519500",null),_=d.exports},68168:function(e,t,r){var o=r(3790),a=r(24241),i=(r(51059),r(1001)),s=(0,i.Z)(a.Z,o.s,o.x,!1,null,"50a93f3c",null);t["Z"]=s.exports},95591:function(e,t,r){r.r(t),r.d(t,{default:function(){return F}});var o=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"fit",attrs:{id:"content"}},[r("ta-border-layout",{attrs:{layout:{header:"70px"},"center-cfg":{showBar:!0,barHeight:"100px"}}},[r("search-head",{ref:"searchHeader",attrs:{slot:"header",search:e.handleSearchResource,"search-tag-param":e.getSearchParam,"advanced-search":e.handleAdvancedSearchResource},slot:"header"}),r("div",{attrs:{slot:"centerExtraContent"},slot:"centerExtraContent"},[r("div",{staticStyle:{"margin-bottom":"10px"}},[r("ta-tag-select",{attrs:{title:"菜单类型",data:e.CollectionData("RESOURCETYPE"),"is-multi":!0},on:{change:e.handleSearchFilter},model:{value:e.filterQuery.resourceType,callback:function(t){e.$set(e.filterQuery,"resourceType",t)},expression:"filterQuery.resourceType"}})],1),r("ta-tag-select",{staticClass:"step1",attrs:{title:"安全策略",data:e.CollectionData("SECURITYPOLICY"),"is-multi":!0},on:{change:e.handleSearchFilter},model:{value:e.filterQuery.securityPolicy,callback:function(t){e.$set(e.filterQuery,"securityPolicy",t)},expression:"filterQuery.securityPolicy"}}),r("ta-tag-select",{attrs:{title:"有效性",data:e.CollectionData("EFFECTIVE"),"is-multi":!0},on:{change:e.handleSearchFilter},model:{value:e.filterQuery.effective,callback:function(t){e.$set(e.filterQuery,"effective",t)},expression:"filterQuery.effective"}}),r("div",{staticStyle:{float:"right"}},[r("ta-button",{on:{click:e.refreshResource}},[e._v(" 刷新资源 "),r("ta-icon",{attrs:{type:"reload"}})],1),r("ta-dropdown",{attrs:{trigger:["click"]}},[r("ta-menu",{attrs:{slot:"overlay"},slot:"overlay"},[r("ta-menu-item",{attrs:{disabled:!e.resourceSelectList.length}},[r("ta-popconfirm",{attrs:{title:"确认启用所选资源?","cancel-text":"取消","ok-text":"确认"},on:{confirm:function(t){return e.handleEnableResourceBatch(!1)}}},[r("ta-icon",{attrs:{type:"check-circle"}}),r("span",{staticClass:"mg-l12"},[e._v("启用")])],1)],1),r("ta-menu-divider"),r("ta-menu-item",{attrs:{disabled:!e.resourceSelectList.length}},[r("ta-popconfirm",{attrs:{title:"确认禁用所选资源?","cancel-text":"取消","ok-text":"确认"},on:{confirm:function(t){return e.handleDisableResourceBatch(!1)}}},[r("ta-icon",{attrs:{type:"stop"}}),r("span",{staticClass:"mg-l12"},[e._v("禁用")])],1)],1),r("ta-menu-divider"),r("ta-menu-item",{attrs:{disabled:!e.resourceSelectList.length},on:{click:function(t){e.deleteVisible=!0}}},[r("ta-icon",{staticStyle:{"margin-right":"20px"},attrs:{type:"close-circle"}}),e._v("删除 ")],1)],1),r("ta-button",[e._v(" 批量操作 "),r("ta-icon",{attrs:{type:"down"}})],1)],1)],1)],1),r("ta-table",{attrs:{columns:e.columns,pagination:!1,"row-key":"resourceId","default-expand-all-rows":e.defaultExpandAllRows,"data-source":e.gridData,scroll:{y:"100%"},"row-selection":{selectedRowKeys:e.resourceSelectKeysList,onChange:e.handleResourceSelected}},on:{expand:e.handleLoadChild},scopedSlots:e._u([{key:"resourceType",fn:function(t){return r("span",{},[e._v(e._s(e.CollectionLabel("RESOURCETYPE",t)))])}},{key:"effective",fn:function(t){return r("span",{},[e._v(e._s(e.CollectionLabel("EFFECTIVE",t)))])}},{key:"securityLevel",fn:function(t){return r("span",{},[e._v(e._s(e.CollectionLabel("SECURIYTLEVEL",t)))])}},{key:"securityPolicy",fn:function(t){return r("span",{},[e._v(e._s(e.CollectionLabel("SECURITYPOLICY",t)))])}},{key:"name",fn:function(t,o){return[r("a",{on:{click:function(t){return e.handleViewDetail(o)}}},[r("span",{class:{greySpan:"1"!=o.effective}},[e._v(e._s(t))])])]}},{key:"restUrl",fn:function(t,o){return r("div",{},[t&&t.length>0?r("span",[e._l(e.getFirst(e.getJsonArray(t)),(function(t,o){return r("ta-tooltip",{key:t.urlId,attrs:{placement:"topLeft"}},[r("template",{slot:"title"},[r("p",[e._v("服务路径："+e._s(t.url))]),r("p",[e._v("独立授权："+e._s(e.CollectionLabel("YESORNO",t.authorityPolicy)))])]),r("ta-tag",[e._v(e._s(t.name))])],2)})),e.getJsonArray(t).length>1?r("ta-popover",{attrs:{placement:"right",title:"服务列表",width:"360",trigger:"click"}},[r("ta-divider"),r("div",{staticStyle:{"word-break":"break-all","max-height":"420px",overflow:"auto"}},e._l(e.getJsonArray(t),(function(t){return r("div",{key:t.urlId,staticStyle:{overflow:"hidden"}},[r("p",[r("span",[e._v("服务名称："+e._s(t.name))]),r("br"),r("span",[e._v("服务路径："),r("br"),e._v(e._s(t.url))]),r("br"),r("span",[e._v("独立授权："+e._s(e.CollectionLabel("YESORNO",t.authorityPolicy)))]),r("br"),r("ta-divider")],1)])})),0),r("span",{staticStyle:{cursor:"pointer"},attrs:{slot:"reference"},slot:"reference"},[r("ta-icon",{attrs:{type:"share-alt"}}),e._v("查看")],1)],1):e._e()],2):e._e()])}},{key:"action",fn:function(t,o){return r("span",{},[r("ta-table-operate",{attrs:{"operate-menu":e.operateMenu}})],1)}}])})],1),r("ta-drawer",{attrs:{title:"功能资源管理",width:"520","destroy-on-close":!0,visible:e.resourceDrawerVisible,"get-container":e.setContainer},on:{close:e.handleCloseResourceDrawer}},[r("resource-data",{ref:"resourceSave",attrs:{"init-data":e.resourceData,"operation-type":e.operationType},on:{close:e.handleHideResourceDrawer,next:e.continueAdd}}),r("template",{slot:"footer"},[r("ta-button-group",[r("ta-button",{on:{click:function(t){return e.handleResetForm()}}},[e._v(" 重置 ")]),r("ta-button",{attrs:{type:"1"===e.operationType?"default":"primary"},on:{click:function(t){return e.handleSaveResource()}}},[e._v(" 保存 ")]),"1"===e.operationType?r("ta-button",{attrs:{type:"primary"},on:{click:function(t){return e.handleSaveResource(!0)}}},[e._v(" 保存并继续 ")]):e._e()],1)],1)],2),r("ta-drawer",{attrs:{title:"功能资源管理-查看",width:"520","destroy-on-close":!0,visible:e.resourceShowDrawerVisible,"get-container":e.setContainer},on:{close:e.handleCloseResourceShowDrawer}},[r("resource-data-show",{ref:"resourceSaveShow",attrs:{"init-data":e.resourceData}})],1),r("ta-careful-delete",{attrs:{visible:e.deleteVisible,title:"资源删除",description:"所选资源"},on:{close:function(t){e.deleteVisible=!1},delete:function(t){return e.handleConfirmDeleteResourceBatch(!1)}}})],1)},a=[],i=r(15298),s=r(98754),n=r(94550),l=r(28464),c=r(95586),u=r(41538),d=r(71411),_=function(){var e=this,t=this,r=t.$createElement,o=t._self._c||r;return o("div",{staticClass:"center-box"},[o("div",{staticClass:"center-item"},[o("ta-input-search",{staticClass:"search-box",attrs:{placeholder:"根据名称、自定义编码查询",enterButton:"查询"},on:{search:t.handleSearch},model:{value:t.searchParam,callback:function(e){t.searchParam=e},expression:"searchParam"}})],1),o("div",{staticClass:"center-item"},[o("ta-search-panel",{attrs:{form:t.form,id:"form",width:800,height:330},on:{search:t.handleAdvancedQueryResourceList}},[o("ta-button",{attrs:{slot:"target"},slot:"target"},[t._v("高级搜索")]),o("template",{slot:"formPanel"},[o("ta-form",{attrs:{autoFormCreate:function(t){e.form=t},layout:t.formLayout}},[t.form?[o("ta-row",[o("ta-col",{attrs:{span:t.row.col}},[o("ta-form-item",{attrs:{label:"功能名称",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"advanceName"}},[o("ta-input",{attrs:{placeholder:"请输入功能名称"}})],1)],1),o("ta-col",{attrs:{span:t.row.col}},[o("ta-form-item",{attrs:{label:"自定义编码",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"advanceCode"}},[o("ta-input",{attrs:{placeholder:"请输入自定义编码"}})],1)],1)],1),o("ta-row",[o("ta-col",{attrs:{span:t.row.col}},[o("ta-form-item",{attrs:{label:"菜单类型",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"resourceType"}},[o("ta-select",{attrs:{mode:"multiple",placeholder:"请选择菜单类型"}},t._l(t.CollectionData("RESOURCETYPE"),(function(e){return o("ta-select-option",{key:e.value,attrs:{value:e.value}},[t._v(t._s(e.label))])})),1)],1)],1),o("ta-col",{attrs:{span:t.row.col}},[o("ta-form-item",{attrs:{label:"安全策略",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"securityPolicy"}},[o("ta-select",{attrs:{mode:"multiple",placeholder:"请选择安全策略"}},t._l(t.CollectionData("SECURITYPOLICY"),(function(e){return o("ta-select-option",{key:e.value,attrs:{value:e.value}},[t._v(t._s(e.label))])})),1)],1)],1)],1),o("ta-row",[o("ta-col",{attrs:{span:t.row.col}},[o("ta-form-item",{attrs:{label:"认证级别",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"securityLevel"}},[o("ta-select",{attrs:{mode:"multiple",placeholder:"请选择认证级别"}},t._l(t.CollectionData("SECURIYTLEVEL"),(function(e){return o("ta-select-option",{key:e.value,attrs:{value:e.value}},[t._v(t._s(e.label))])})),1)],1)],1),o("ta-col",{attrs:{span:t.row.col}},[o("ta-form-item",{attrs:{label:"界面元素授权",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"uiAuthorityPolicy"}},[o("ta-select",{attrs:{mode:"multiple",placeholder:"请选择界面元素授权方式"}},t._l(t.CollectionData("UIAUTHORITYPOLICY"),(function(e){return o("ta-select-option",{key:e.value,attrs:{value:e.value}},[t._v(t._s(e.label))])})),1)],1)],1)],1),o("ta-row",[o("ta-col",{attrs:{span:t.row.col}},[o("ta-form-item",{attrs:{label:"所属系统",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"sysCode"}},[o("ta-select",{attrs:{mode:"multiple",placeholder:"请选择所属系统"}},t._l(t.systemList,(function(e){return o("ta-select-option",{key:e.sysCode,attrs:{value:e.sysCode}},[t._v(t._s(e.sysName))])})),1)],1)],1),o("ta-col",{attrs:{span:t.row.col}},[o("ta-form-item",{attrs:{label:"有效标识",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"effective"}},[o("ta-select",{attrs:{mode:"multiple",placeholder:"请选择有效标识"}},t._l(t.CollectionData("EFFECTIVE"),(function(e){return o("ta-select-option",{key:e.value,attrs:{value:e.value}},[t._v(t._s(e.label))])})),1)],1)],1)],1)]:t._e()],2)],1)],2)],1)])},m=[],f=r(11200),h=f.Z,p=r(1001),y=(0,p.Z)(h,_,m,!1,null,"19cbc532",null),v=y.exports,g=function(){var e=this,t=this,r=t.$createElement,o=t._self._c||r;return o("div",{attrs:{id:"restUrlSelectTagContainer"}},[o("ta-form",{attrs:{"auto-form-create":function(t){e.form=t},layout:t.formLayout}},[t._l(t.formNormalSettings,(function(e){return[o("renderFormItem",{key:e.id,attrs:{"form-setting":e,"is-show-parent-item":t.isShowParentItem,"render-type":t.renderType,"render-prop":t.renderProp}},["icon"==e.id?o("iconFormItem"):t._e(),"restUrl"==e.id?o("rest-url-select-tag",{attrs:{"new-label":"添加服务"}}):"resourceType"==e.id?o("ta-radio-group",{attrs:{"button-style":"solid"}},t._l(t.resourceTypeEditable,(function(e){return o("ta-radio-button",{key:e.value,attrs:{value:e.value,disabled:0==e.editable}},[t._v(" "+t._s(e.label)+" ")])})),1):"sysCode"==e.id?o("ta-select",t._l(t.systemList,(function(e){return o("ta-select-option",{key:e.sysCode,attrs:{value:e.sysCode}},[t._v(" "+t._s(e.sysName)+" ")])})),1):"effectiveFlag"==e.id?o("ta-switch",{attrs:{"checked-children":"有效","un-checked-children":"无效"}}):"iconColor"==e.id?o("ta-color-picker",{attrs:{"vertical-position":"top"}}):t._e()],1)]})),t.formMoreSettings.length>0?o("ta-collapse",{attrs:{bordered:!1}},[o("ta-collapse-panel",{key:"1",staticStyle:{border:"none"},attrs:{header:"更多功能信息"}},[t._l(t.formMoreSettings,(function(e){return[o("renderFormItem",{key:e.id,attrs:{"form-setting":e,"render-type":t.renderType,"render-prop":t.renderProp}},["iconColor"==e.id?o("ta-color-picker",{attrs:{"vertical-position":"top"}}):t._e()],1)]}))],2)],1):t._e()],2)],1)},b=[],I=r(21645),D=I.Z,R=(0,p.Z)(D,g,b,!1,null,"63e03dad",null),w=R.exports,S=function(){var e=this,t=this,r=t.$createElement,o=t._self._c||r;return o("div",{attrs:{id:"app"}},[o("ta-form",{attrs:{autoFormCreate:function(t){e.form=t},layout:t.formLayout}},[t._l(t.formNormalShowSettings,(function(e){return[o("renderFormItem",{key:e.id,attrs:{formSetting:e,isShow:!0,showValues:t.formData,simpleShowSlot:t.simpleShowSlot,renderType:"SHOW"}},["restUrl"==e.id?t._l(t.restUrls,(function(e){return o("div",{key:e.urlId,staticClass:"one-info"},[o("p",[t._v("服务名称："+t._s(e.name))]),o("p",[t._v("服务路径："+t._s(e.url))]),o("p",[t._v("独立授权："+t._s(t.CollectionLabel("YESORNO",e.authorityPolicy)))])])})):"resourceType"==e.id?[t._v(" "+t._s(t.resourceType.label)+" ")]:"sysCode"==e.id?[t._v(" "+t._s(t.sysCode.sysName)+" ")]:"effectiveFlag"==e.id?[t._v(" "+t._s(t.effective)+" ")]:"iconColor"==e.id?[o("ta-row",[o("ta-col",{attrs:{span:12}},[t._v(" "+t._s(t.formData.iconColor)+" ")]),o("ta-col",{attrs:{span:2,offset:1}},[o("ta-color-picker",{attrs:{value:t.formData.iconColor,disabled:!0,chooseType:"box",verticalPosition:"top"}})],1)],1)]:t._e(),"menuImage"==e.id?o("div",{staticClass:"menu-image"},[t.imageUrl?o("div",{staticClass:"menu-image-show",style:{backgroundImage:"url("+t.imageUrl+")"}}):o("i",{staticClass:"anticon anticon-setting icon-upload"})]):t._e()],2)]})),t.formMoreShowSettings.length>0?o("ta-collapse",{attrs:{bordered:!1}},[o("ta-collapse-panel",{key:"1",staticStyle:{border:"none"},attrs:{header:"更多功能信息"}},[t._l(t.formMoreShowSettings,(function(e){return[o("renderFormItem",{key:e.id,attrs:{formSetting:e,isShow:!0,showValues:t.formData,simpleShowSlot:t.simpleShowSlot,renderType:"SHOW"}},["iconColor"==e.id?[o("ta-row",[o("ta-col",{attrs:{span:12}},[t._v(" "+t._s(t.formData.iconColor)+" ")]),o("ta-col",{attrs:{span:2,offset:1}},[o("ta-color-picker",{attrs:{value:t.formData.iconColor,disabled:!0,chooseType:"box",verticalPosition:"top"}})],1)],1)]:t._e()],2)]}))],2)],1):t._e()],2)],1)},C=[],E=r(65482),L=E.Z,T=(0,p.Z)(L,S,C,!1,null,"f2d32216",null),P=T.exports,O=r(26602),k=r.n(O),x=r(71012),M=r.n(x),U={findFilterIndex:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"key";return e.findIndex((function(e){try{if(e[r]!=t[r])throw{}}catch(o){return!1}return!0}))},loopDeal:function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"key",a=arguments.length>3&&void 0!==arguments[3]&&arguments[3],i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:function(){},s=arguments.length>5&&void 0!==arguments[5]?arguments[5]:0;M()(t)&&(e=t,t=t.children);for(var n=0;n<t.length;n++){var l=t[n],c=!0;if(k()(r)&&r.length>0){var u=this.findFilterIndex(r,l,o);c=u>-1}k()(l.children)&&(a&&c?this.loopDeal(l,null,null,a,i,s):this.loopDeal(l,r,o,a,i,s)),c&&(i(e,l,n),n+=s)}},loopData:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"key",o=arguments.length>3&&void 0!==arguments[3]&&arguments[3],a=[];return this.loopDeal(e,t,r,o,(function(e,t,r){a.push(t)})),a},loopDataKey:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"key",o=arguments.length>3&&void 0!==arguments[3]&&arguments[3],a=[];return this.loopDeal(e,t,r,o,(function(e,t,o){a.push(t[r])})),a},loopParentData:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"key",o=[];return this.loopDeal(e,[t],r,!1,(function(e,t,r){o.push(e)})),o[0]},updateTreeDataDown:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"key",a=Object.keys(r);this.loopDeal(e,t,o,!0,(function(e,t,o){a.forEach((function(e){return t[e]=r[e]}))}))},removeTreeData:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"key";this.loopDeal(e,t,r,!1,(function(t,r,o){M()(t)?(t.children.splice(o,1),0==t.children.length&&(t.children=null)):e.splice(o,1)}),-1)},addTreeData:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"key",o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},a=arguments.length>4?arguments[4]:void 0,i=this.loopData(e,[t],r)[0];k()(i.children)?i.children.push(o):a.$set(i,"children",[o])}},A=r(80790),B=[{title:"功能名称",dataIndex:"name",width:"20%",overflowTooltip:!0,scopedSlots:{customRender:"name"}},{title:"功能路径",dataIndex:"url",width:"14%",overflowTooltip:!0},{title:"服务路径",dataIndex:"restUrl",width:"24%",overflowTooltip:!0,scopedSlots:{customRender:"restUrl"}},{title:"菜单类型",dataIndex:"resourceType",width:100,scopedSlots:{customRender:"resourceType"}},{title:"安全策略",dataIndex:"securityPolicy",width:140,scopedSlots:{customRender:"securityPolicy"}},{title:"操作",key:"action",width:220,align:"center",scopedSlots:{customRender:"action"}}],K={name:"resourceManagement",components:{searchHead:v,resourceData:w,resourceDataShow:P},mixins:[A.Z],data:function(){var e=this;return{gridData:[],columns:B,operateMenu:[{name:"添加下级",disabled:function(e){return"0"===e.effective},title:function(e){return"0"===e.effective?"禁用的资源不允许添加下级":""},onClick:function(t){return e.handleShowResourceDrawer(t,"1")}},{name:"编辑",disabled:function(e){return"0"===e.effective},title:function(e){return"0"===e.effective?"禁用的资源不允许编辑":""},onClick:function(t){return e.handleShowResourceDrawer(t,"2")}},{name:"更多",type:"more",moreMenuList:[{name:"启用",type:"confirm",confirmTitle:"确认启用该资源?",onOk:function(t){e.handleEnableResourceBatch(t)}},{name:"禁用",type:"confirm",confirmTitle:"确认禁用该资源?",onOk:function(t){e.handleDisableResourceBatch(t)}},{name:"删除",type:"confirm",confirmTitle:"确认删除该资源?",onOk:function(t){e.handleConfirmDeleteResourceBatch(t)}}]}],resourceSelectKeysList:[],resourceSelectList:[],defaultExpandAllRows:!0,isAsync:!0,resourceDrawerVisible:!1,operationType:"1",resourceData:{},resourceShowDrawerVisible:!1,uiModelVisible:!1,uiInitData:{},filterQuery:{name:"",code:"",resourceType:[],securityPolicy:[],effective:[]},deleteVisible:!1}},mounted:function(){this.fnToHandleQueryResource();var e=[{element:".step1",popover:{title:"安全策略",description:"【授权可访问】指该功能需要被授权给某角色权限才能被访问，正常菜单功能应该都设置此策略<br/>【无需登录可访问】指该功能可以被任何客户端匿名访问<br/>【登录均可访问】指该功能登录用户都有权限可以访问",position:"bottom"}}];this.fnCommonGuide(e)},methods:{toString:l.Z,setContainer:function(){return document.getElementById("content")},refreshResource:function(){this.fnToHandleQueryResource()},getFirst:function(e){if(e.length>0)return[e[0]]},getJsonArray:function(e){var t=[];try{t=JSON.parse(e)}catch(r){t=[]}return t},getSearchParam:function(){var e=this,t={},r="";return this.$refs.searchHeader&&(r=this.$refs.searchHeader.getSearchParam()),Object.assign(this.filterQuery,{name:r,code:r}),Object.keys(this.filterQuery).forEach((function(r){var o=e.filterQuery[r];(0,s.Z)(o)&&""!==o.trim()&&(t[r]=o),(0,u.Z)(o)&&o.length>0&&(t[r]=o.join(","))})),t},handleSearchFilter:function(){this.handleSearch(this.getSearchParam())},handleSearchResource:function(e){this.handleSearch(e)},handleSearch:function(e){var t=this;this.defaultExpandAllRows=!0,(0,n.Z)(e)&&!(0,c.Z)(e)?(this.isAsync=!1,i.Z.queryResourceList(e,(function(e){t.gridData=e.resourceList}))):(this.isAsync=!0,i.Z.queryResourceListNoParam((function(e){t.gridData=e.resourceList})))},handleAdvancedSearchResource:function(e){this.handleSearch(e)},handleLoadChild:function(e,t){var r=this;if(!1!==e&&this.isAsync){if(t.children&&t.children.length>0)return;i.Z.loadChildResource(t,(function(e){r.defaultExpandAllRows=!1,t.children=e.resourceList[0].children}))}},handleResourceSelected:function(e,t){this.resourceSelectKeysList=e,this.resourceSelectList=t},handleDisableResourceBatch:function(e){var t,r,o=this;if(e){if("0"===e.effective)return void this.$message.warning("该记录已经禁用，请勿重复操作！");t=[e.resourceId],r=[e]}else{var a=this.fnGetBatchResourceIds("禁用");t=a.resourceIds,r=a.resources}i.Z.disableResourceBatch(t,(function(e){U.updateTreeDataDown(o.gridData,r,{effective:"0"},"resourceId"),o.resourceSelectKeysList=[],o.resourceSelectList=[]}))},handleEnableResourceBatch:function(e){var t,r,o=this;if(e){if("1"===e.effective)return void this.$message.warning("该记录已经启用，请勿重复操作！");t=[e.resourceId],r=[e]}else{var a=this.fnGetBatchResourceIds("启用",!1);t=a.resourceIds,r=a.resources}i.Z.enableResourceBatch(t,(function(e){U.updateTreeDataDown(o.gridData,r,{effective:"1"},"resourceId"),o.resourceSelectList=[],o.resourceSelectKeysList=[]}))},handleConfirmDeleteResourceBatch:function(e){var t,r,o=this;if(e)t=[e.resourceId],r=[e];else{var a=this.fnGetBatchResourceIds("删除");t=a.resourceIds,r=a.resources}i.Z.deleteResourceBatch(t,(function(e){o.resourceSelectList=[],o.resourceSelectKeysList=[],U.removeTreeData(o.gridData,r,"resourceId"),o.deleteVisible=!1}))},handleShowResourceUi:function(e){"0"!==e.pResourceId?"1"===e.uiAuthorityPolicy?(this.uiInitData=e,this.uiModelVisible=!0):this.$message.warning("该菜单表单元素授权方式为继承当前菜单权限",2):this.$message.warning("顶级菜单无法添加表单元素",2)},handleHideResourceUi:function(){this.uiModelVisible=!1},handleShowResourceDrawer:function(e,t){"1"!==t||""===e||"1"===e.effective?(this.operationType=t,this.resourceData=e,this.resourceDrawerVisible=!0):this.$message.warn("禁用的功能资源节点不允许添加下级",2)},handleCloseResourceDrawer:function(){this.handleHideResourceDrawer(!1)},continueAdd:function(e){this.handleHideResourceDrawer(e),this.resourceDrawerVisible=!0,this.handleResetForm()},handleHideResourceDrawer:function(e){if(this.handleResetForm(),e){var t,r=null===(t=(0,d.Z)(this.gridData,(function(t){return t.resourceId===e.pResourceId})))||void 0===t?void 0:t.item,o=(null===r||void 0===r?void 0:r.children)&&r.children.filter((function(t){return t.resourceId===e.resourceId}));if(o&&o.length)for(var a in o[0])"children"!==a&&"childNum"!==a&&(o[0][a]=e[a]);else r.children||(r.children=[]),null===r||void 0===r||r.children.push(e),null===r||void 0===r||r.children.sort((function(e,t){return e.orderNo-t.orderNo}))}this.resourceDrawerVisible=!1},handleViewDetail:function(e){this.resourceData=e,this.resourceShowDrawerVisible=!0},handleCloseResourceShowDrawer:function(){this.resourceShowDrawerVisible=!1},handleResetForm:function(){this.$refs.resourceSave.fnResetForm()},handleSaveResource:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$refs.resourceSave.fnSaveResource(e)},fnToHandleQueryResource:function(){this.$refs.searchHeader.handleSearch()},fnGetBatchResourceIds:function(e,t){if(!1!==t&&(t=!0),e||(e="处理"),t){var r=this.resourceSelectList.findIndex((function(e){return"0"===e.pResourceId}));if(r>-1)return this.$message.warning("当前选中的功能资源包含根节点，请重新选择",2),!1}var o=this.resourceSelectList.map((function(e){return e.resourceId}));return{resourceIds:o,resources:this.resourceSelectList}}}},Z=K,q=(0,p.Z)(Z,o,a,!1,null,"2202592e",null),F=q.exports},51059:function(e,t,r){r(78910)},24241:function(e,t,r){var o=r(42934);t["Z"]=o.Z},3790:function(e,t,r){r.d(t,{s:function(){return o},x:function(){return a}});var o=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",[r("ta-collapse",{attrs:{bordered:!1},model:{value:e.activeKey,callback:function(t){e.activeKey=t},expression:"activeKey"}},[e._l(e.tagArray,(function(t,o){return r("ta-collapse-panel",{key:e.generateKey(o),attrs:{"show-arrow":!1}},[r("template",{slot:"header"},[r("span",{class:{"disabled-text":"0"===t.effective}},[e._v(e._s(t.name))]),r("div",{staticClass:"rest-input-delete"},[[e.activeKey.indexOf(e.generateKey(o))<0?r("a",{on:{click:function(t){t.stopPropagation(),e.handleViewRest(e.generateKey(o))}}},[e._v("查看")]):r("a",{on:{click:function(t){t.stopPropagation(),e.handleHiddenRest(e.generateKey(o))}}},[e._v("隐藏")])],r("ta-divider",{attrs:{type:"vertical"}}),r("a",{on:{click:function(r){r.stopPropagation(),e.handleRemoveRest(t,e.generateKey(o),o)}}},[e._v("删除")])],2)]),r("p",[e._v("状态："+e._s("0"===t.effective?"无效":"有效"))]),r("p",[e._v("服务路径："+e._s(t.url))]),r("p",[e._v("独立授权："+e._s(e.CollectionLabel("YESORNO",t.authorityPolicy)))])],2)})),r("ta-collapse-panel",{staticStyle:{display:"none"}})],2),r("a",{on:{click:function(t){return t.stopPropagation(),e.initRestUrlDrawer.apply(null,arguments)}}},[r("ta-icon",{attrs:{type:"plus"}}),e._v(" "+e._s(e.newLabel)+" ")],1),r("ta-modal",{attrs:{title:"服务列表",width:"50%",height:"500px",visible:e.restUrlDrawerVisible},on:{cancel:e.handleCloseRestUrlDrawer}},[r("ta-row",[r("ta-col",[r("ta-input-search",{attrs:{placeholder:"输入服务名称/路径回车检索"},on:{search:e.handleSearchRestUrl},model:{value:e.restSearch,callback:function(t){e.restSearch=t},expression:"restSearch"}})],1)],1),r("ta-table",{attrs:{columns:e.columns,pagination:!1,"row-key":"id","data-source":e.gridData,"row-selection":{selectedRowKeys:e.selectedRowKeys,onChange:e.handleSelectChange,getCheckboxProps:this.fnGetCheckboxProps}},on:{expand:e.handleLoadChild},scopedSlots:e._u([{key:"name",fn:function(t,o){return r("span",{class:{"disable-color":"0"===o.effective}},[e._v(e._s(t))])}},{key:"action",fn:function(t,o){return r("span",{},[r("ta-switch",{attrs:{disabled:"0"===o.effective,"checked-children":"是","un-checked-children":"否"},on:{change:function(t){return e.handleChangeDlAuth(o)}}})],1)}}])}),r("template",{slot:"footer"},[r("ta-button-group",[r("ta-button",{on:{click:function(t){return e.handleResetRestUrl()}}},[e._v(" 重置 ")]),r("ta-button",{attrs:{type:"primary"},on:{click:function(t){return e.handleSaveRestUrl()}}},[e._v(" 确定 ")])],1)],1)],2)],1)},a=[]},78910:function(){},21645:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){var D_GIT_hiiss_ms_frontend_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_7__=__webpack_require__(95082),core_js_modules_web_timers_js__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__(32564),core_js_modules_web_timers_js__WEBPACK_IMPORTED_MODULE_0___default=__webpack_require__.n(core_js_modules_web_timers_js__WEBPACK_IMPORTED_MODULE_0__),_api_index__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__(15298),_api_resourceApi__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__(96966),_restUrlSelectTag__WEBPACK_IMPORTED_MODULE_3__=__webpack_require__(68168),_projectCommon_components_renderFormItem__WEBPACK_IMPORTED_MODULE_4__=__webpack_require__(80715),_mixins__WEBPACK_IMPORTED_MODULE_5__=__webpack_require__(69442),_scopes_core_systemModules_sysmg_modulePart_resourceManagement_components_iconFormItem__WEBPACK_IMPORTED_MODULE_6__=__webpack_require__(66360);function getBase64(e,t){var r=new FileReader;r.addEventListener("load",(function(){return t(r.result)})),r.readAsDataURL(e)}__webpack_exports__["Z"]={name:"ResourceData",components:{renderFormItem:_projectCommon_components_renderFormItem__WEBPACK_IMPORTED_MODULE_4__.Z,restUrlSelectTag:_restUrlSelectTag__WEBPACK_IMPORTED_MODULE_3__.Z,iconFormItem:_scopes_core_systemModules_sysmg_modulePart_resourceManagement_components_iconFormItem__WEBPACK_IMPORTED_MODULE_6__.Z},mixins:[_mixins__WEBPACK_IMPORTED_MODULE_5__.Z],props:{initData:{type:Object,required:!0},operationType:{validator:function(e){return-1!==["1","2"].indexOf(e)}}},data:function(){return{formLayout:"horizontal",isShowParentItem:!0,operationResourceData:{},systemList:[],resourceTypeEditable:[{value:"0",label:"通用菜单",editable:!0},{value:"1",label:"管理菜单",editable:!0},{value:"2",label:"经办菜单",editable:!0}],loading:!1,imageUrl:""}},computed:{renderType:function(){return"2"===this.operationType?"EDIT":"ADD"},renderProp:function(){var e=this;return{authRequest:function(t,r){var o=e.initData.resourceId;_api_resourceApi__WEBPACK_IMPORTED_MODULE_2__.Z.authRequestForResourceInfo((0,D_GIT_hiiss_ms_frontend_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_7__.Z)((0,D_GIT_hiiss_ms_frontend_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_7__.Z)({},t),{},{resourceId:o}),(function(e){r(e.resourceInfo[t.inputKey])}))}}}},watch:{initData:{immediate:!0,handler:function(e,t){var r=this;this.loadStatus||(this.loadStatus=!0,this.fnInitResource(),setTimeout((function(){r.loadStatus=!1}),1e3))}},operationType:{immediate:!0,handler:function(e,t){var r=this;this.loadStatus||(this.loadStatus=!0,this.fnInitResource(),setTimeout((function(){r.loadStatus=!1}),1e3))}}},methods:{fnResetForm:function(){this.fnLoadResourceData(this.operationResourceData)},fnSaveResource:function(e){var t=this;this.form.validateFieldsAndScroll((function(r,o){if(!r){if("1"===t.operationType){if(!o.pResourceId)return void t.$message.warning("请选择上级功能资源!",2)}else if("2"===t.operationType&&!o.resourceId)return void t.$message.warning("功能资源唯一标识不能为空!",2);t.imageUrl.length&&(o.image=t.imageUrl),o.uiAuthorityPolicy="0",_api_resourceApi__WEBPACK_IMPORTED_MODULE_2__.Z.saveResource((0,D_GIT_hiiss_ms_frontend_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_7__.Z)((0,D_GIT_hiiss_ms_frontend_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_7__.Z)({},o),{},{effective:o.effectiveFlag?"1":"0",operationType:t.operationType}),(function(r){t.operationResourceData.image=t.imageUrl,e?t.$emit("next",r.result):t.$emit("close",r.result)}))}}))},fnInitResource:function fnInitResource(){var _this5=this;0===this.systemList.length&&_api_index__WEBPACK_IMPORTED_MODULE_1__.Z.queryAllAccessSystem((function(data){_this5.systemList=eval("("+data.systemList+")")})),_api_resourceApi__WEBPACK_IMPORTED_MODULE_2__.Z.queryResource({resourceId:this.initData.resourceId,operationType:this.operationType},(function(e){_this5.operationResourceData=e,_this5.buildForm(_this5.initData),_this5.fnLoadResourceData(e)}))},fnLoadResourceData:function(e){this.resourceTypeEditable=e.resourceTypeList,this.isShowParentItem=!0;var t=document.getElementById("uploadPhotoId");t&&(t.value=""),this.loading=!1,"2"===this.operationType?(this.imageUrl=e.image||"",this.isShowParentItem="0"!==this.initData.pResourceId,this.$nextTick((function(){this.form.resetFields(),this.form.setFieldsValue(e.taResource),this.form.setFieldsValue({effectiveFlag:"1"===e.taResource.effective,securityLevel:e.taResource.securityLevel.toString()}),this.form.setFieldsValue({icon:e.taResource.icon})}))):"1"===this.operationType&&this.$nextTick((function(){this.imageUrl="",this.form.resetFields(),this.form.setFieldsValue({pResourceId:this.initData.resourceId,pResourceName:this.initData.name,orderNo:e.taResource.orderNo});var t=this.resourceTypeEditable.filter((function(e){return e.editable}));0===t.length&&this.$message.error("未加载到可用的菜单类型"),1===t.length&&this.form.setFieldsValue({resourceType:t[0].value})}))},uploadPhoto:function(e){var t=this,r=e.target.files[0],o=document.getElementById("uploadPhotoId");if(r){if(this.loading=!0,r.size>51200)return this.$message.error("用户头像大小不能超过50kb"),this.loading=!1,o.value="",!1;getBase64(r,(function(e){t.imageUrl=e,t.loading=!1}))}}}}},65482:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){var _api_index__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__(15298),_api_resourceApi__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__(96966),_projectCommon_components_renderFormItem__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__(80715),_mixins__WEBPACK_IMPORTED_MODULE_3__=__webpack_require__(69442),showColumnFilter=["pResourceId","resourceId"],simpleShowSlot=[];__webpack_exports__["Z"]={name:"resourceDataShow",props:{initData:{type:Object,required:!0}},components:{renderFormItem:_projectCommon_components_renderFormItem__WEBPACK_IMPORTED_MODULE_2__.Z},mixins:[_mixins__WEBPACK_IMPORTED_MODULE_3__.Z],data:function(){return{formLayout:"horizontal",formData:{},simpleShowSlot:simpleShowSlot,isShowParentItem:!0,systemList:[],resourceTypeEditable:[{value:"0",label:"通用菜单",editable:!0},{value:"1",label:"管理菜单",editable:!0},{value:"2",label:"经办菜单",editable:!0}],imageUrl:""}},computed:{formNormalShowSettings:function(){return this.formNormalSettings.filter((function(e){return-1==showColumnFilter.indexOf(e.id)}))||[]},formMoreShowSettings:function(){return this.formMoreSettings.filter((function(e){return-1==showColumnFilter.indexOf(e.id)}))||[]},resourceType:function(){var e=this;return this.resourceTypeEditable.find((function(t){return t.value==e.formData.resourceType}))||{}},sysCode:function(){var e=this;return this.systemList.find((function(t){return t.sysCode==e.formData.sysCode}))||{}},effective:function(){return"1"==this.formData.effective?"有效":"无效"},restUrls:function(){return this.formData.restUrl?JSON.parse(this.formData.restUrl):[]}},methods:{fnInitResource:function fnInitResource(){var _this3=this;0==this.systemList.length&&_api_index__WEBPACK_IMPORTED_MODULE_0__.Z.queryAllAccessSystem((function(data){_this3.systemList=eval("("+data.systemList+")")})),_api_resourceApi__WEBPACK_IMPORTED_MODULE_1__.Z.queryResource({resourceId:this.initData.resourceId,operationType:"2"},(function(e){_this3.fnLoadResourceData(e)}))},fnLoadResourceData:function(e){this.resourceTypeEditable=e.resourceTypeList,this.isShowParentItem="0"!=this.initData.pResourceId,this.formData=e.taResource,this.buildForm(this.formData),this.imageUrl=e.image}},watch:{initData:{immediate:!0,handler:function(e,t){this.fnInitResource()}}}}},42934:function(e,t,r){var o=r(89541),a=(r(69191),r(31485)),i=[{title:"服务名称",dataIndex:"name",width:"40%",overflowTooltip:!0,scopedSlots:{customRender:"name"}},{title:"服务路径",dataIndex:"url",width:"40%",overflowTooltip:!0},{title:"独立授权",key:"action",width:100,align:"center",scopedSlots:{customRender:"action"}}];function s(e){if("undefined"===typeof e||null===e)return"[]";try{JSON.parse(e)}catch(t){e="[]"}return e}t["Z"]={name:"restUrlSelectTag",components:{TaTable:o.Z},props:["value","newLabel"],data:function(){var e=this.$props.value;return{formLayout:"horizontal",formItemLayout:{labelCol:{span:8},wrapperCol:{span:16}},tags:s(e),activeKey:[],keySeparator:"_",keySuffix:"key",restSearch:"",columns:i,gridData:[],selectedRowKeys:[],selectedRows:[],isAsync:!0,restUrlDrawerVisible:!1}},computed:{tagArray:function(){return JSON.parse(this.tags)||[]}},watch:{value:function(e){this.tags=s(e),this.activeKey=[]}},methods:{generateKey:function(e){return e+this.keySeparator+this.keySuffix},handleViewRest:function(e){this.activeKey.indexOf(e)>-1||this.activeKey.push(e)},handleHiddenRest:function(e){this.activeKey=this.activeKey.filter((function(t){return t!==e}))},handleRemoveRest:function(e,t,r){var o=JSON.parse(this.tags)||[];o.splice(r,1),this.tags=JSON.stringify(o),this.triggerChange()},triggerChange:function(){var e=this.tags;this.$emit("change",e)},initRestUrlDrawer:function(){this.restSearch="",this.selectedRowKeys=[],this.selectedRows=[],this.handleSearchRestUrl()},initRestUrlData:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e.forEach((function(e){e.authorityPolicy="0"})),e},handleToSelectRestUrl:function(){var e=this;this.gridData=[],this.$nextTick((function(){a.Z.queryNamespace(null,(function(t){e.gridData=e.initRestUrlData(t.urls),e.restUrlDrawerVisible=!0}))}))},handleSearchRestUrl:function(e){var t=this;if(null==e||""===e)return this.handleToSelectRestUrl(),void(this.isAsync=!0);this.isAsync=!1,a.Z.queryUrlByParam({name:e,url:e},(function(e){t.gridData=t.initRestUrlData(e.urls)}))},handleLoadChild:function(e,t){var r=this;if(e&&this.isAsync){if(t.children&&t.children.length>0)return;a.Z.queryUrlByNamespace({namespace:t.id},(function(e){t.children=r.initRestUrlData(e.urls)}))}},handleCloseRestUrlDrawer:function(){this.restUrlDrawerVisible=!1},handleResetRestUrl:function(){this.initRestUrlDrawer()},handleSaveRestUrl:function(){var e=this,t=JSON.parse(this.tags)||[];t=t.filter((function(t){return-1===e.selectedRowKeys.indexOf(t.id)})).concat(this.selectedRows),this.tags=JSON.stringify(t),this.triggerChange(),this.restUrlDrawerVisible=!1},handleChangeDlAuth:function(e){"1"===e.authorityPolicy?e.authorityPolicy="0":e.authorityPolicy="1",this.selectedRows.forEach((function(t){t.id===e.id&&(t.authorityPolicy=e.authorityPolicy)}))},handleSelectChange:function(e,t){this.selectedRowKeys=e;var r=JSON.parse(JSON.stringify(t));r.forEach((function(e){return e.authorityPolicy?void 0:e.authorityPolicy="0"})),this.selectedRows=r},fnGetCheckboxProps:function(e){var t={props:{disabled:"0"===e.effective,name:e.name}};return t}}}},11200:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){var _api_index__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__(15298),_yh_ta_utils_isObject__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__(8145);__webpack_exports__["Z"]={name:"searchHead",props:{search:{type:Function,required:!0},searchTagParam:{type:Function,required:!0},advancedSearch:{type:Function,required:!0}},data:function(){return{row:{col:12},form:null,formLayout:"horizontal",formItemLayout:{labelCol:{span:6},wrapperCol:{span:18}},searchParam:"",systemList:[]}},created:function created(){var _this=this;0==this.systemList.length&&_api_index__WEBPACK_IMPORTED_MODULE_0__.Z.queryAllAccessSystem((function(data){_this.systemList=eval("("+data.systemList+")")}))},methods:{handleSearch:function(){var e;e=this.searchTagParam(),this.search(e)},handleAdvancedQueryResourceList:function(e){var t={};Object.keys(e).map((function(r){t[r]=(0,_yh_ta_utils_isObject__WEBPACK_IMPORTED_MODULE_1__.Z)(e[r])?e[r].toString():e[r]})),this.advancedSearch(t)},getSearchParam:function(){return this.searchParam}}}},35478:function(e,t){t["Z"]={data:{formTypeSettings:{}},watch:{formSettings:function(){var e={};this.formSettings.forEach((function(t){e[t.formId]=t.formItem.type})),this.formTypeSettings=e}},methods:{formatSensitiveValue:function(e,t){return"sensitive-input"==this.formTypeSettings[e]&&t?JSON.parse(t).sensitiveField:t}}}},42358:function(e,t){var r=[{fieldId:"icon",formId:"icon",displayText:"图标名称",hide:"0",effective:"1",required:"0",unchangeable:"0",protectPrivacy:"0",orderNo:1,contentSize:30,tiText:null,validReg:null,connectAA10:null,formType:"input",more:"0"}],o={fieldId:"field",formId:"field",displayText:"扩展信息",effective:"0",hide:"1",required:"0",unchangeable:"0",protectPrivacy:"0",orderNo:1,contentSize:null,tiText:null,validReg:null,connectAA10:null,formType:"input",more:"1"};function a(e,t){return(Array(t).join(0)+e).slice(-t)}for(var i=10,s=1;s<=i;s++){var n=Object.assign({},o),l=a(s,2);n.fieldId=n.fieldId+l,n.formId=n.formId+l,n.displayText=n.displayText+l,n.orderNo=r.length+1,r.push(n)}t["Z"]=r},80790:function(e,t,r){var o=r(76698);r(34530);t["Z"]={activated:function(){var e=window.pageVmObj,t=e["steps_"+e._route.name];this.methods?this.methods.fnCommonGuide(t):this.fnCommonGuide(t)},deactivated:function(){this.methods?this.methods.fnCommonGuide([],!0):this.fnCommonGuide([],!0)},methods:{fnCommonGuide:function(e,t){var r=window.pageVmObj;t?(r.driver.reset(),window.fnPageGuide=null):(r["steps_"+r._route.name]=e,r.driver=new o.Z({allowClose:!1}),window.fnPageGuide=function(){r.driver.defineSteps(e),r.driver.start()})}}}},15298:function(e,t,r){var o=r(94550),a="/org/sysmg/resourceManagementRestService/";t["Z"]={queryResourceList:function(e,t){var r={};"string"==typeof e?""!==e.trim()&&(r.name=e,r.code=e):(0,o.Z)(e)?Object.assign(r,e):message.info("缺少参数, 操作无效"),Base.submit(null,{url:a+"queryTaResourceByParameters",data:r}).then((function(e){t(e.data)}))},queryResourceListNoParam:function(e){Base.submit(null,{url:a+"queryTaResourceByParameters"}).then((function(t){e(t.data)}))},loadChildResource:function(e,t){Base.submit(null,{url:a+"queryChildResource",data:{resourceId:e.resourceId}},{successCallback:function(e){e.data.resourceList[0].children?t(e.data):message.warning("当前功能资源不存在下级功能资源!",2)}})},disableResourceBatch:function(e,t){Base.submit(null,{url:a+"disabledResourceByResourceIds",data:{resourceIds:e.join(",")}}).then((function(e){message.success("禁用资源成功",2),t(e.data)}))},enableResourceBatch:function(e,t){Base.submit(null,{url:a+"enabledResourceByResourceIds",data:{resourceIds:e.join(",")}},{successCallback:function(e){message.success("启用资源成功",2),t(e.data)}})},deleteResourceBatch:function(e,t){Base.submit(null,{url:a+"deleteResourceByResourceIds",data:{resourceIds:e.join(",")}},{successCallback:function(e){message.success("删除成功",2),t(e.data)}})},deleteResource:function(e,t){"0"!=e.pResourceId?Base.submit(null,{url:a+"deleteResourceByResourceIds",data:{resourceIds:e.resourceId}},{successCallback:function(e){message.success("删除成功",2),t(e.data)}}):message.error("功能资源顶级节点不允许删除",2)},queryAllAccessSystem:function(e){Base.submit(null,{url:a+"queryAllAccessSystem"},{successCallback:function(t){e(t.data)}})},queryResourceSettingTable:function(e){Base.submit(null,{url:"org/sysmg/manageableFieldsRestService/queryManageableFields",data:{type:"3"}},{successCallback:function(t){e(t.data)}})}}},96966:function(e,t){var r="/org/sysmg/resourceManagementRestService/";t["Z"]={queryResource:function(e,t){Base.submit(null,{url:r+"queryResourceByResourceID",data:{resourceId:e.resourceId,operationType:e.operationType}},{successCallback:function(e){t(e.data)}})},saveResource:function(e,t){var o="";o="1"==e.operationType?r+"addResource":r+"updateResourceByResourceId",Base.submit(null,{url:o,data:e},{successCallback:function(e){message.success("保存成功",2),t(e.data)}})},authRequestForResourceInfo:function(e,t){Base.submit(null,{url:r+"getResourceByResourceId",data:e},{successCallback:function(e){return t(e.data)}})}}},31485:function(e,t){var r="/org/sysmg/resourceManagementRestService/";t["Z"]={queryResourceUi:function(e,t){Base.submit(null,{url:r+"queryTaResourceUi",data:{resourceId:e}},{successCallback:function(e){t(e.data)}})},saveResourceUi:function(e,t){var o="",a="";"1"==e.elementDataAddOrEdit?(o=r+"addTaResourceUi",a="添加"):"2"==e.elementDataAddOrEdit&&(o=r+"updateTaResourceUi",a="修改");var i={url:o,data:{pageElementId:e.pageElementId,resourceId:e.resourceId,elementName:e.elementName,elementId:e.elementId,code:e.code,authorityPolicy:e.authorityPolicy}};Base.submit(null,i,{successCallback:function(e){message.success("表单元素"+a+"成功"),t(e.data)}})},deleteResourceUi:function(e,t){Base.submit(null,{url:r+"deleteTaResourceUi",data:{pageElementIds:e.pageElementId,elementId:e.elementId,resourceId:e.resourceId}},{successCallback:function(e){message.success("表单元素删除成功"),t(e.data)}})},queryUrlByParam:function(e,t){var r={url:"org/sysmg/url/queryByParam",data:e};Base.submit(null,r,{successCallback:function(e){return t(e.data)}})},queryNamespace:function(e,t){var r={url:"org/sysmg/url/queryNamespace",data:e};Base.submit(null,r,{successCallback:function(e){return t(e.data)}})},queryUrlByNamespace:function(e,t){var r={url:"org/sysmg/url/queryUrlByNamespace",data:e};Base.submit(null,r,{successCallback:function(e){return t(e.data)}})}}},69442:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){var D_GIT_hiiss_ms_frontend_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_4__=__webpack_require__(95082),_api_index__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__(15298),_projectCommon_js_extendConfig_extendResourceSetting__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__(42358),_yh_ta_utils_isArray__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__(41538),_projectCommon_js_extendConfig_commonMixins__WEBPACK_IMPORTED_MODULE_3__=__webpack_require__(35478),formItemLayouts={default:{labelCol:{span:6},wrapperCol:{span:18}}},mixins={data:function(){return(0,D_GIT_hiiss_ms_frontend_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_4__.Z)({formItemLayouts:formItemLayouts,extendSettings:_projectCommon_js_extendConfig_extendResourceSetting__WEBPACK_IMPORTED_MODULE_1__.Z,formSettings:[]},_projectCommon_js_extendConfig_commonMixins__WEBPACK_IMPORTED_MODULE_3__.Z.data)},created:function(){var e=this;window.mixinsResource?(this.extendSettings=(0,_yh_ta_utils_isArray__WEBPACK_IMPORTED_MODULE_2__.Z)(window.mixinsResource)&&window.mixinsResource.length?window.mixinsResource:this.extendSettings,this.buildForm({})):(window.mixinsResource=!0,_api_index__WEBPACK_IMPORTED_MODULE_0__.Z.queryResourceSettingTable((function(t){if((0,_yh_ta_utils_isArray__WEBPACK_IMPORTED_MODULE_2__.Z)(t.resultData)&&t.resultData.length>0){for(var r=0;r<t.resultData.length;r++)if("icon"===t.resultData[r].formId){t.resultData[r].formType="slot";break}e.extendSettings=t.resultData}window.mixinsResource=t.resultData,e.buildForm({})})))},computed:{formNormalSettings:function(){return this.formSettings.filter((function(e){return!e.isMore&&e.exist}))||[]},formMoreSettings:function(){return this.formSettings.filter((function(e){return e.isMore&&e.exist}))||[]}},watch:(0,D_GIT_hiiss_ms_frontend_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_4__.Z)({},_projectCommon_js_extendConfig_commonMixins__WEBPACK_IMPORTED_MODULE_3__.Z.watch),methods:(0,D_GIT_hiiss_ms_frontend_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_4__.Z)((0,D_GIT_hiiss_ms_frontend_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_4__.Z)({},_projectCommon_js_extendConfig_commonMixins__WEBPACK_IMPORTED_MODULE_3__.Z.methods),{},{buildForm:function(e){this.formSettings=this.getFormSettings(e)},getFormNecessarySettings:function(e){return[{id:"pResourceId",formId:"pResourceId",formItem:{type:"input",collection:null},label:"上级功能资源ID",decoratorOptions:{rules:[{required:!0}],initialValue:e.porgId},display:!1,exist:!0,formItemLayout:formItemLayouts.default},{id:"pResourceName",formId:"pResourceName",formItem:{type:"input",collection:null},label:"上级功能名称",decoratorOptions:{},display:!0,exist:!0,disabled:!0,formItemLayout:formItemLayouts.default},{id:"resourceId",formId:"resourceId",formItem:{type:"input",collection:null},label:"功能资源ID",decoratorOptions:{},display:!1,exist:!0,formItemLayout:formItemLayouts.default},{id:"name",formId:"name",formItem:{type:"input",collection:null},label:"功能名称",decoratorOptions:{rules:[{required:!0,message:"请填写功能名称!"}]},display:!0,exist:!0,formItemLayout:formItemLayouts.default},{id:"url",formId:"url",formItem:{type:"input",collection:null},label:"功能URL",decoratorOptions:{},display:!0,exist:!0,formItemLayout:formItemLayouts.default},{id:"restUrl",formId:"restUrl",formItem:{type:"slot",collection:null},label:"后台RestURL",decoratorOptions:{},display:!0,exist:!0,formItemLayout:formItemLayouts.default},{id:"code",formId:"code",formItem:{type:"input",collection:null},label:"自定义编码",decoratorOptions:{},display:!0,exist:!0,formItemLayout:formItemLayouts.default},{id:"orderNo",formId:"orderNo",formItem:{type:"input",collection:null},label:"排序号",decoratorOptions:{rules:[{required:!0,message:"排序号不能为空!"},{type:"number"}]},display:!0,exist:!0,formItemLayout:formItemLayouts.default},{id:"resourceType",formId:"resourceType",formItem:{type:"slot",collection:null},label:"菜单类型",decoratorOptions:{initialValue:e.resourceType,rules:[{required:!0,message:"请选择菜单类型!"}]},display:!0,exist:!0,formItemLayout:formItemLayouts.default},{id:"securityPolicy",formId:"securityPolicy",formItem:{type:"radioButton",collection:"SECURITYPOLICY"},label:"安全策略",decoratorOptions:{initialValue:e.securityPolicy,rules:[{required:!0,message:"请选择安全策略!"}]},display:!0,exist:!0,formItemLayout:formItemLayouts.default},{id:"securityLevel",formId:"securityLevel",formItem:{type:"radioButton",collection:"SECURIYTLEVEL"},label:"安全认证级别",decoratorOptions:{initialValue:"".concat(e.securityLevel),rules:[{required:!0,message:"请选择安全认证级别!"}]},display:!0,exist:!0,formItemLayout:formItemLayouts.default},{id:"sysCode",formId:"sysCode",formItem:{type:"slot",collection:null},label:"功能所属系统",decoratorOptions:{initialValue:e.sysCode,rules:[{required:!0,message:"请选择功能所属系统!"}]},display:!0,exist:!0,formItemLayout:formItemLayouts.default},{id:"effectiveFlag",formId:"effectiveFlag",formItem:{type:"slot",collection:null},label:"有效性",decoratorOptions:{initialValue:!0,valuePropName:"checked"},display:!0,exist:!0,formItemLayout:formItemLayouts.default}]},getFormSettings:function getFormSettings(){var initData=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},propSettings=this.extendSettings.sort((function(e,t){return e.orderNo-t.orderNo})).map((function(setting){var formId=setting.formId,label=setting.displayText||"",placeholder=setting.tiText||"",propSetting={id:setting.fieldId,formId:formId,class:null,formItem:{type:setting.formType,collection:setting.connectAA10},label:label,display:"0"===setting.hide,exist:"1"===setting.effective,disabled:"1"===setting.unchangeable,formItemLayout:formItemLayouts[formId]||formItemLayouts.default,decoratorOptions:{},placeholder:placeholder,isMore:"1"===setting.more},rules=[];if("1"===setting.required&&rules.push({required:!0,message:label+"是必须的"}),isNaN(parseInt(setting.contentSize))||rules.push({max:setting.contentSize,message:label+"内容长度不能超过"+setting.contentSize}),setting.validReg){var isreg;try{isreg=eval(setting.validReg)instanceof RegExp}catch(e){isreg=!1}isreg&&rules.push({pattern:eval(setting.validReg),message:"请输入正确的"+label+"内容"})}return propSetting.decoratorOptions.rules=rules,initData.hasOwnProperty(formId)&&(propSetting.decoratorOptions.initialValue=initData[formId]),propSetting}));return this.getFormNecessarySettings(initData).concat(propSettings)}})};__webpack_exports__["Z"]=mixins}}]);
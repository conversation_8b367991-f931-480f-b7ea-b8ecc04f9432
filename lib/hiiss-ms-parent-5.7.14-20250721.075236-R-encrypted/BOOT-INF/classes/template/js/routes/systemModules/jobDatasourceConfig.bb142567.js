"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[150],{75554:function(e,t,a){a.r(t),a.d(t,{default:function(){return D}});var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"fit",attrs:{id:"jobDatasourceConfig"}},[a("ta-border-layout",{attrs:{layout:{header:"70px",footer:"70px"},centerCfg:{showBar:!0,toolBarStyle:{height:"50px",lineHeight:"48px"}},"footer-cfg":{showBorder:!1}}},[a("div",{staticClass:"center-box",attrs:{slot:"header"},slot:"header"},[a("ta-input-search",{staticClass:"search-box",attrs:{placeholder:"请输入数据源名称",enterButton:"搜索"},on:{search:e.loadData},model:{value:e.datasourceName,callback:function(t){e.datasourceName=t},expression:"datasourceName"}})],1),a("div",{attrs:{slot:"centerExtraContent"},slot:"centerExtraContent"},[a("div",{staticStyle:{float:"right"}},[a("ta-button",{attrs:{type:"primary"},on:{click:function(t){e.visible=!0}}},[e._v("新增")]),a("ta-button",{attrs:{disabled:e.btnDisable},on:{click:e.batchDelete}},[e._v("批量删除")])],1)]),a("ta-table",{attrs:{columns:e.columns,dataSource:e.gridData,rowKey:"datasourceId",rowSelection:{selectedRowKeys:e.selectedRowKeys,onSelect:e.fnOnSelect,onSelectAll:e.fnOnSelectAll},pagination:!1},scopedSlots:e._u([{key:"action",fn:function(t,o){return a("span",{},[a("ta-table-operate",{attrs:{operateMenu:e.operateMenu}})],1)}}])}),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("ta-pagination",{ref:"gridPager",staticStyle:{float:"right","margin-top":"10px"},attrs:{showSizeChanger:"",showQuickJumper:"",dataSource:e.gridData,defaultPageSize:10,params:e.pageParams,url:"jobmg/elasticjob/jobDatasourceManagementRestService/getJobDatasource"},on:{"update:dataSource":function(t){e.gridData=t},"update:data-source":function(t){e.gridData=t}}})],1)],1),a("add-datasource",{attrs:{visible:e.visible},on:{close:function(t){e.visible=!1},queryTable:e.loadData}})],1)},r=[],s=function(){var e=this,t=this,a=t.$createElement,o=t._self._c||a;return o("ta-drawer",{attrs:{title:"添加数据源",placement:"right",closable:!0,visible:t.visible,destroyOnClose:"",width:"500px"},on:{close:t.closeDrawer}},[o("ta-form",{attrs:{id:"userForm",autoFormCreate:function(t){e.form=t}}},[o("ta-form-item",{attrs:{label:"数据源名称",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"datasourceName",fieldDecoratorOptions:{rules:[{required:!0,message:"请输入数据源名称"}]}}},[o("ta-input")],1),o("ta-form-item",{attrs:{label:"数据源驱动",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"driver",fieldDecoratorOptions:{rules:[{required:!0,message:"请输入数据源驱动"}]}}},[o("ta-select",[o("ta-select-option",{attrs:{value:"com.informix.jdbc.IfxDriver"}},[t._v("com.informix.jdbc.IfxDriver")]),o("ta-select-option",{attrs:{value:"org.postgresql.Driver"}},[t._v("org.postgresql.Driver")]),o("ta-select-option",{attrs:{value:"oracle.jdbc.OracleDriver"}},[t._v("oracle.jdbc.OracleDriver")]),o("ta-select-option",{attrs:{value:"com.mysql.jdbc.Driver"}},[t._v("com.mysql.jdbc.Driver")])],1)],1),o("ta-form-item",{attrs:{label:"数据源url",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"url",fieldDecoratorOptions:{rules:[{required:!0,message:"请输入数据源url"}]}}},[o("ta-input")],1),o("ta-form-item",{attrs:{label:"数据源用户",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"userName",fieldDecoratorOptions:{rules:[{required:!0,message:"请输入数据源用户"}]}}},[o("ta-input")],1),o("ta-form-item",{attrs:{label:"数据源密码",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"password",fieldDecoratorOptions:{rules:[{required:!0,message:"请输入数据源密码"}]}}},[o("ta-input")],1)],1),o("div",{attrs:{slot:"footer"},slot:"footer"},[o("ta-button-group",[o("ta-button",{on:{click:function(e){return t.onResetForm()}}},[t._v("重置")]),o("ta-button",{attrs:{type:"primary"},on:{click:function(e){return t.onSubmitForm()}}},[t._v("保存")])],1)],1)],1)},n=[],l=a(95439),i={name:"addDatasource",props:["visible"],data:function(){return{formItemLayout:{labelCol:{span:6},wrapperCol:{span:18}},formData:{}}},methods:{closeDrawer:function(){this.$emit("close"),this.form.resetFields()},onResetForm:function(){this.form.resetFields()},onSubmitForm:function(){var e=this;this.form.validateFields((function(t,a){t||l.Z.addJobDatasource(a,(function(t){e.showConfirm()}))}))},showConfirm:function(){var e=this;this.$confirm({title:"保存成功",content:"保存成功。是否继续新增注册中心?",onOk:function(){e.form.resetFields(),e.$emit("queryTable")},onCancel:function(){e.closeDrawer(),e.$emit("queryTable")}})}}},c=i,u=a(1001),d=(0,u.Z)(c,s,n,!1,null,null,null),f=d.exports,m=[{title:"数据源名称",dataIndex:"datasourceName",width:"20%"},{title:"数据源驱动",dataIndex:"driver",width:"20%",overflowTooltip:!0},{title:"数据源url",dataIndex:"url",width:"30%",overflowTooltip:!0},{title:"数据源用户",dataIndex:"userName",width:"10%"},{title:"操作选项",dataIndex:"action",width:"20%",scopedSlots:{customRender:"action"},align:"center"}],p={name:"jobDatasourceConfig",data:function(){var e=this;return{selectedRowKeys:[],selectedRows:[],columns:m,operateMenu:[{name:"历史轨迹",onClick:function(t){e.routeToJobExecutionTrace(t)}},{name:"历史状态",onClick:function(t){e.routeToJobStatusTrace(t)}},{name:"删除",type:"confirm",confirmTitle:"确定要删除吗?",isShow:function(t){return e.gridData.length},onOk:function(t){e.onDelete(t.datasourceId)}}],gridData:[],datasourceName:"",visible:!1}},components:{addDatasource:f},mounted:function(){this.loadData()},computed:{btnDisable:function(){return!this.selectedRowKeys.length}},methods:{loadData:function(){var e=this;this.$refs.gridPager.loadData((function(t){e.selectedRowKeys=[],e.selectedRows=[]}))},pageParams:function(){return{datasourceName:this.datasourceName}},fnOnSelect:function(e,t){var a=this.selectedRowKeys,o=this.selectedRows;t?(a.push(e.datasourceId),o.push(e)):(this.selectedRowKeys=a.filter((function(t){return t!=e.datasourceId})),this.selectedRows=o.filter((function(t){return t.datasourceId!=e.datasourceId})))},fnOnSelectAll:function(e,t){var a=this;this.selectedRows=[],this.selectedRowKeys=[],e&&(t.map((function(e){a.selectedRowKeys.push(e.datasourceId)})),this.selectedRows=t)},onDelete:function(e){var t=this;l.Z.deleteJobDatasource({datasourceIds:e},(function(e){t.$message.success("删除成功"),t.loadData()}))},batchDelete:function(){var e=this;0!==this.selectedRows.length?this.$confirm({title:"批量删除数据源",content:"确认删除数据源吗?",cancelText:"取消",okText:"确认",onOk:function(){var t=e.selectedRows.map((function(e){return e.datasourceId}));l.Z.deleteJobDatasource({datasourceIds:t.join(",")},(function(t){e.$message.success("批量删除成功"),e.loadData()}))}}):this.$message.warning("请先选择数据")},routeToJobExecutionTrace:function(e){this.$router.push({name:"jobExecutionTrace",params:{dsData:e}})},routeToJobStatusTrace:function(e){this.$router.push({name:"jobStatusTrace",params:{dsData:e}})}}},b=p,h=(0,u.Z)(b,o,r,!1,null,"d6f121da",null),D=h.exports},95439:function(e,t){var a="/jobmg/elasticjob/jobDatasourceManagementRestService/";t["Z"]={addJobDatasource:function(e,t){Base.submit(null,{url:a+"addJobDatasource",data:e},{successCallback:function(e){return t(e)}})},deleteJobDatasource:function(e,t){Base.submit(null,{url:a+"deleteJobDatasource",data:e},{successCallback:function(e){return t(e)}})},getJobExecutionTrace:function(e,t){Base.submit(null,{url:a+"getJobExecutionTrace",data:e},{successCallback:function(e){return t(e)}})},getJobStatusTrace:function(e,t){Base.submit(null,{url:a+"getJobStatusTrace",data:e},{successCallback:function(e){return t(e)}})}}}}]);
"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[2893],{18301:function(e,t,a){a.r(t),a.d(t,{default:function(){return v}});var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"fit",attrs:{id:"zookeeperRegistryCenterConfig"}},[a("ta-border-layout",{attrs:{layout:{header:"70px",footer:"70px"},centerCfg:{showBar:!0,toolBarStyle:{height:"50px",lineHeight:"48px"}},"footer-cfg":{showBorder:!1}}},[a("div",{staticClass:"center-box",attrs:{slot:"header"},slot:"header"},[a("ta-input-search",{staticClass:"search-box",attrs:{placeholder:"请输入注册中心名称",enterButton:"搜索"},on:{search:e.loadData},model:{value:e.appName,callback:function(t){e.appName=t},expression:"appName"}})],1),a("div",{attrs:{slot:"centerExtraContent"},slot:"centerExtraContent"},[a("div",{staticStyle:{float:"right"}},[a("ta-button",{attrs:{type:"primary"},on:{click:function(t){e.editVisible=!0}}},[e._v("新增")]),a("ta-button",{attrs:{disabled:e.btnDisable},on:{click:e.batchDelete}},[e._v("批量删除")])],1)]),a("ta-table",{attrs:{columns:e.columns,dataSource:e.gridData,rowKey:"zkId",rowSelection:{selectedRowKeys:e.selectedRowKeys,onSelect:e.fnOnSelect,onSelectAll:e.fnOnSelectAll},pagination:!1},scopedSlots:e._u([{key:"action",fn:function(t,r){return a("span",{},[a("ta-table-operate",{attrs:{operateMenu:e.operateMenu}})],1)}}])}),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("ta-pagination",{ref:"gridPager",staticStyle:{float:"right","margin-top":"10px"},attrs:{showSizeChanger:"",showQuickJumper:"",dataSource:e.gridData,defaultPageSize:10,params:e.pageParams,url:"jobmg/elasticjob/zookeeperAddressManagementRestService/getZookeeperRegistryCenter"},on:{"update:dataSource":function(t){e.gridData=t},"update:data-source":function(t){e.gridData=t}}})],1)],1),a("edit-zk",{attrs:{visible:e.editVisible},on:{close:function(t){e.editVisible=!1},queryTable:e.loadData}})],1)},o=[],s=function(){var e=this,t=this,a=t.$createElement,r=t._self._c||a;return r("ta-drawer",{attrs:{title:"添加注册中心",placement:"right",closable:!0,visible:t.visible,destroyOnClose:"",width:"500px"},on:{close:t.closeEdit}},[r("ta-form",{attrs:{id:"zkForm",autoFormCreate:function(t){e.form=t}}},[r("ta-form-item",{attrs:{label:"注册中心名称",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"appName",fieldDecoratorOptions:{rules:[{required:!0,message:"请输入注册中心名称"}]}}},[r("ta-input")],1),r("ta-form-item",{attrs:{label:"注册中心地址",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"zkAddress",fieldDecoratorOptions:{rules:[{required:!0,message:"请输入注册中心地址"}]}}},[r("ta-input")],1),r("ta-form-item",{attrs:{label:"命名空间",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol,fieldDecoratorId:"appNamespace",fieldDecoratorOptions:{rules:[{required:!0,message:"请输入命名空间"}]}}},[r("ta-input")],1)],1),r("div",{attrs:{slot:"footer"},slot:"footer"},[r("ta-button-group",[r("ta-button",{on:{click:function(e){return t.onResetForm()}}},[t._v("重置")]),r("ta-button",{attrs:{type:"primary"},on:{click:function(e){return t.onSubmitForm()}}},[t._v("保存")])],1)],1)],1)},n=[],i=a(47878),l={name:"editZk",props:["visible"],data:function(){return{formItemLayout:{labelCol:{span:6},wrapperCol:{span:18}},formData:{}}},methods:{closeEdit:function(){this.$emit("close"),this.form.resetFields()},onResetForm:function(){this.form.resetFields()},onSubmitForm:function(){var e=this;this.form.validateFields((function(t,a){t||i.Z.addZookeeperRegistryCenter(a,(function(t){e.showConfirm()}))}))},showConfirm:function(){var e=this;this.$confirm({title:"保存成功",content:"保存成功。是否继续新增注册中心?",onOk:function(){e.form.resetFields(),e.$emit("queryTable")},onCancel:function(){e.closeEdit(),e.$emit("queryTable")}})}}},c=l,u=a(1001),b=(0,u.Z)(c,s,n,!1,null,null,null),d=b.exports,f=[{title:"注册中心名称",dataIndex:"appName",width:"25%",overflowTooltip:!0},{title:"注册中心地址",dataIndex:"zkAddress",width:"30%",overflowTooltip:!0},{title:"命名空间",dataIndex:"appNamespace",width:"20%",overflowTooltip:!1},{title:"操作选项",dataIndex:"action",width:"310px",scopedSlots:{customRender:"action"},align:"center"}],m={name:"app",data:function(){var e=this;return{selectedRowKeys:[],selectedRows:[],columns:f,operateMenu:[{name:"作业维度",onClick:function(t){e.routeToJobMg(t)}},{name:"服务器维度",onClick:function(t){e.routeToServerMg(t)}},{name:"闲忙任务管理",onClick:function(t){e.routeToFreeBusyJobMg(t)}}],gridData:[],appName:"",editVisible:!1}},components:{editZk:d},mounted:function(){this.loadData()},computed:{btnDisable:function(){return!this.selectedRowKeys.length}},methods:{loadData:function(){var e=this;this.$refs.gridPager.loadData((function(t){e.selectedRowKeys=[],e.selectedRows=[]}))},pageParams:function(){return{appName:this.appName}},onDelete:function(e){var t=this,a={zkIds:e};i.Z.batchDeleteZookeeperRegistryCenter(a,(function(e){t.$message.success("删除成功"),t.loadData()}))},batchDelete:function(){var e=this;0!==this.selectedRows.length?this.$confirm({title:"批量删除注册中心",content:"确认删除注册中心吗?",cancelText:"取消",okText:"确认",onOk:function(){var t=e.selectedRows.map((function(e){return e.zkId}));t=t.join(",");var a={zkIds:t};i.Z.batchDeleteZookeeperRegistryCenter(a,(function(t){e.$message.success("批量删除成功"),e.loadData()}))}}):this.$message.warning("请先选择数据")},fnOnSelect:function(e,t){var a=this.selectedRowKeys,r=this.selectedRows;t?(a.push(e.zkId),r.push(e)):(this.selectedRowKeys=a.filter((function(t){return t!=e.zkId})),this.selectedRows=r.filter((function(t){return t.zkId!=e.zkId})))},fnOnSelectAll:function(e,t){var a=this;this.selectedRows=[],this.selectedRowKeys=[],e&&(t.map((function(e){a.selectedRowKeys.push(e.zkId)})),this.selectedRows=t)},connect:function(e){var t=this;i.Z.connectZookeeperRegistryCenter({zkId:e},(function(e){t.loadData()}))},connectBtnDisable:function(e){return"1"===e},routeToJobMg:function(e){this.$router.push({name:"jobDimensionality",params:{zkData:e}})},routeToServerMg:function(e){this.$router.push({name:"serverDimensionality",params:{zkData:e}})},routeToFreeBusyJobMg:function(e){this.$router.push({name:"freeBusyJobManager",params:{zkData:e}})}}},p=m,g=(0,u.Z)(p,r,o,!1,null,"749d5da4",null),v=g.exports},47878:function(e,t){t["Z"]={addZookeeperRegistryCenter:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/zookeeperAddressManagementRestService/addZookeeperRegistryCenter",data:e},{successCallback:function(e){return t(e)}})},batchDeleteZookeeperRegistryCenter:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/zookeeperAddressManagementRestService/deleteZookeeperRegistryCenter",data:e},{successCallback:function(e){return t(e)}})},connectZookeeperRegistryCenter:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/zookeeperAddressManagementRestService/connectZookeeperRegistryCenter",data:e},{successCallback:function(e){return t(e)}})},getJobInfo:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/getJobInfo",data:e},{successCallback:function(e){return t(e)}})},getJobDetailInfo:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/getJobDetailInfo",data:e},{successCallback:function(e){return t(e)}})},saveJobDetailInfo:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/saveJobDetailInfo",data:e},{successCallback:function(e){return t(e)}})},getJobServerDetail:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/getJobServerDetail",data:e},{successCallback:function(e){return t(e)}})},disabledSharding:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/disabledSharding",data:e},{successCallback:function(e){return t(e)}})},effectSharding:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/effectSharding",data:e},{successCallback:function(e){return t(e)}})},triggerJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/triggerJob",data:e},{successCallback:function(e){return t(e)}})},disableJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/disableJob",data:e},{successCallback:function(e){return t(e)}})},enableJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/enableJob",data:e},{successCallback:function(e){return t(e)}})},shutdownJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/shutdownJob",data:e},{successCallback:function(e){return t(e)}})},removeJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/removeJob",data:e},{successCallback:function(e){return t(e)}})},getServerInfo:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/getServerInfo",data:e},{successCallback:function(e){return t(e)}})},disableServer:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/disableServer",data:e},{successCallback:function(e){return t(e)}})},enableServer:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/enableServer",data:e},{successCallback:function(e){return t(e)}})},shutdownServer:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/shutdownServer",data:e},{successCallback:function(e){return t(e)}})},removeServer:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/removeServer",data:e},{successCallback:function(e){return t(e)}})},getServerJobDetail:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/getServerJobDetail",data:e},{successCallback:function(e){return t(e)}})},disabledServerJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/disabledServerJob",data:e},{successCallback:function(e){return t(e)}})},enableServerJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/enableServerJob",data:e},{successCallback:function(e){return t(e)}})},shutdownServerJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/shutdownServerJob",data:e},{successCallback:function(e){return t(e)}})},removeServerJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/removeServerJob",data:e},{successCallback:function(e){return t(e)}})},getJobNameByZkId:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/freeBusyJobManagerRestService/getJobNameByZkId",data:e},{successCallback:function(e){return t(e)}})},getServerIpsByJobName:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/freeBusyJobManagerRestService/getServerIpsByJobName",data:e},{successCallback:function(e){return t(e)}})},addFreeBusyJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/freeBusyJobManagerRestService/addFreeBusyJob",data:e},{successCallback:function(e){return t(e)}})},deleteFreeBusyJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/freeBusyJobManagerRestService/deleteFreeBusyJob",data:e},{successCallback:function(e){return t(e)}})},getAllServerIpsByJobName:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/freeBusyJobManagerRestService/getAllServerIpsByJobName",data:e},{successCallback:function(e){return t(e)}})},updateFreeBusyJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/freeBusyJobManagerRestService/updateFreeBusyJob",data:e},{successCallback:function(e){return t(e)}})}}}}]);
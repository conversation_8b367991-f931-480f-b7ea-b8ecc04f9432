"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[4283],{51338:function(e,t,a){a.r(t),a.d(t,{default:function(){return b}});var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"fit",attrs:{id:"jobDetail"}},[a("ta-border-layout",{attrs:{layout:{header:"55px"},centerCfg:{showBar:!0,barHeight:"90px"}}},[a("template",{slot:"header"},[a("ta-breadcrumb",{staticStyle:{"line-height":"29px"},attrs:{separator:">"}},[a("ta-breadcrumb-item",[a("a",{on:{click:e.fnBackToHome}},[e._v("作业管理")])]),a("ta-breadcrumb-item",[a("a",{on:{click:e.fnBackToJob}},[e._v("作业维度")])]),a("ta-breadcrumb-item",[e._v("作业详情")])],1)],1),a("div",{attrs:{slot:"centerExtraContent"},slot:"centerExtraContent"},[a("ta-alert",{staticClass:"notice-box",attrs:{message:"当前作业名称："+this.jobName,type:"info",showIcon:""}}),a("ta-tag-select",{staticClass:"filter-name",attrs:{title:"是否支持自动失效转移",data:[{value:"1",label:"是"},{value:"0",label:"否"}]},on:{change:e.loadData},model:{value:e.isSupport,callback:function(t){e.isSupport=t},expression:"isSupport"}}),a("ta-tag-select",{staticClass:"filter-name",attrs:{title:"状态",data:[{value:"RUNNING",label:"运行中"},{value:"DISABLED",label:"已失效"},{value:"SHARDING_FLAG",label:"分片待调整"},{value:"PENDING",label:"等待运行"}],"is-multi":!0},on:{change:e.loadData},model:{value:e.statusArr,callback:function(t){e.statusArr=t},expression:"statusArr"}}),a("div",{staticStyle:{float:"right"}},[a("ta-button",{on:{click:e.fnBackToJob}},[a("ta-icon",{attrs:{type:"rollback"}}),e._v("返回")],1)],1)],1),a("ta-table",{attrs:{columns:e.detailColumns,dataSource:e.detailGridData,pagination:!1},scopedSlots:e._u([{key:"status",fn:function(t){return["RUNNING"==t?a("ta-tag",{staticClass:"no-cursor",attrs:{color:"#00a65a"}},[e._v("运行中")]):"DISABLED"==t?a("ta-tag",{staticClass:"no-cursor",attrs:{color:"#f39c12"}},[e._v("已失效")]):"SHARDING_FLAG"==t?a("ta-tag",{staticClass:"no-cursor",attrs:{color:"#00c0ef"}},[e._v("分片待调整")]):"PENDING"==t?a("ta-tag",{staticClass:"no-cursor",attrs:{color:"#00c0ef"}},[e._v("等待运行")]):e._e()]}},{key:"failover",fn:function(t){return[t?a("ta-tag",{staticClass:"no-cursor",attrs:{type:"success"}},[e._v("是")]):a("ta-tag",{staticClass:"no-cursor",attrs:{type:"danger"}},[e._v("否")])]}},{key:"action",fn:function(t,s){return["DISABLED"==s.status?a("span",[a("ta-popconfirm",{attrs:{title:"确定要继续生效操作吗?",okText:"确定",cancelText:"取消",okType:"default"},on:{confirm:function(t){return e.effectSharding(s.item)}}},[a("ta-icon",{staticStyle:{color:"red"},attrs:{slot:"icon",type:"question-circle-o"},slot:"icon"}),a("ta-button",{staticClass:"effect action-button-common",attrs:{size:"small"}},[e._v(" 生效 ")])],1)],1):a("span",[a("ta-popconfirm",{attrs:{title:"确定要继续失效操作吗?",okText:"确定",cancelText:"取消",okType:"default"},on:{confirm:function(t){return e.disabledSharding(s.item)}}},[a("ta-icon",{staticStyle:{color:"red"},attrs:{slot:"icon",type:"question-circle-o"},slot:"icon"}),a("ta-button",{staticClass:"failure action-button-common",attrs:{size:"small"}},[e._v(" 失效 ")])],1)],1)]}}])})],2)],1)},r=[],o=a(47878),n=[{title:"分片项",dataIndex:"item",width:"18%"},{title:"服务器IP",dataIndex:"serverIp",width:"18%"},{title:"进程ID",dataIndex:"instanceId",width:"18%"},{title:"状态",dataIndex:"status",width:"18%",scopedSlots:{customRender:"status"}},{title:"支持自动失效转移",dataIndex:"failover",width:"18%",scopedSlots:{customRender:"failover"}},{title:"操作",dataIndex:"action",width:"10%",align:"center",scopedSlots:{customRender:"action"}}],i={name:"jobDetail",components:{},data:function(){return{detailGridData:[],detailColumns:n,zkData:{},jobName:"",isSupport:[],statusArr:[]}},mounted:function(){this.$route.params.jobDetailData instanceof Object?(this.zkData=this.$route.params.jobDetailData.zkData||{},this.jobName=this.$route.params.jobDetailData.jobName,this.loadData()):this.$router.push({name:"jobDimensionality"})},methods:{loadData:function(){var e=this,t={zkId:this.zkData.zkId,jobName:this.jobName,isSupport:this.isSupport.join(","),shardingStatus:this.statusArr.join(",")};o.Z.getJobServerDetail(t,(function(t){e.detailGridData=t.data.detailGridData}))},fnBackToHome:function(){this.$router.push({name:"zookeeperRegistryCenterConfig",params:{zkData:this.zkData}})},fnBackToJob:function(){this.$router.push({name:"jobDimensionality",params:{zkData:this.zkData}})},disabledSharding:function(e){var t=this,a={zkId:this.zkData.zkId,jobName:this.jobName,item:e};o.Z.disabledSharding(a,(function(e){t.$message.success("失效操作成功"),t.loadData()}))},effectSharding:function(e){var t=this,a={zkId:this.zkData.zkId,jobName:this.jobName,item:e};o.Z.effectSharding(a,(function(e){t.$message.success("生效操作成功"),t.loadData()}))}}},c=i,l=a(1001),u=(0,l.Z)(c,s,r,!1,null,"37134943",null),b=u.exports},47878:function(e,t){t["Z"]={addZookeeperRegistryCenter:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/zookeeperAddressManagementRestService/addZookeeperRegistryCenter",data:e},{successCallback:function(e){return t(e)}})},batchDeleteZookeeperRegistryCenter:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/zookeeperAddressManagementRestService/deleteZookeeperRegistryCenter",data:e},{successCallback:function(e){return t(e)}})},connectZookeeperRegistryCenter:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/zookeeperAddressManagementRestService/connectZookeeperRegistryCenter",data:e},{successCallback:function(e){return t(e)}})},getJobInfo:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/getJobInfo",data:e},{successCallback:function(e){return t(e)}})},getJobDetailInfo:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/getJobDetailInfo",data:e},{successCallback:function(e){return t(e)}})},saveJobDetailInfo:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/saveJobDetailInfo",data:e},{successCallback:function(e){return t(e)}})},getJobServerDetail:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/getJobServerDetail",data:e},{successCallback:function(e){return t(e)}})},disabledSharding:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/disabledSharding",data:e},{successCallback:function(e){return t(e)}})},effectSharding:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/effectSharding",data:e},{successCallback:function(e){return t(e)}})},triggerJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/triggerJob",data:e},{successCallback:function(e){return t(e)}})},disableJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/disableJob",data:e},{successCallback:function(e){return t(e)}})},enableJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/enableJob",data:e},{successCallback:function(e){return t(e)}})},shutdownJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/shutdownJob",data:e},{successCallback:function(e){return t(e)}})},removeJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/jobOperateRestService/removeJob",data:e},{successCallback:function(e){return t(e)}})},getServerInfo:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/getServerInfo",data:e},{successCallback:function(e){return t(e)}})},disableServer:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/disableServer",data:e},{successCallback:function(e){return t(e)}})},enableServer:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/enableServer",data:e},{successCallback:function(e){return t(e)}})},shutdownServer:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/shutdownServer",data:e},{successCallback:function(e){return t(e)}})},removeServer:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/removeServer",data:e},{successCallback:function(e){return t(e)}})},getServerJobDetail:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/getServerJobDetail",data:e},{successCallback:function(e){return t(e)}})},disabledServerJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/disabledServerJob",data:e},{successCallback:function(e){return t(e)}})},enableServerJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/enableServerJob",data:e},{successCallback:function(e){return t(e)}})},shutdownServerJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/shutdownServerJob",data:e},{successCallback:function(e){return t(e)}})},removeServerJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/serverOperateRestService/removeServerJob",data:e},{successCallback:function(e){return t(e)}})},getJobNameByZkId:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/freeBusyJobManagerRestService/getJobNameByZkId",data:e},{successCallback:function(e){return t(e)}})},getServerIpsByJobName:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/freeBusyJobManagerRestService/getServerIpsByJobName",data:e},{successCallback:function(e){return t(e)}})},addFreeBusyJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/freeBusyJobManagerRestService/addFreeBusyJob",data:e},{successCallback:function(e){return t(e)}})},deleteFreeBusyJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/freeBusyJobManagerRestService/deleteFreeBusyJob",data:e},{successCallback:function(e){return t(e)}})},getAllServerIpsByJobName:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/freeBusyJobManagerRestService/getAllServerIpsByJobName",data:e},{successCallback:function(e){return t(e)}})},updateFreeBusyJob:function(e,t){Base.submit(null,{url:"jobmg/elasticjob/freeBusyJobManagerRestService/updateFreeBusyJob",data:e},{successCallback:function(e){return t(e)}})}}}}]);
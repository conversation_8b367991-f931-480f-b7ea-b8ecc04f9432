(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[5398],{88412:function(t,e,a){"use strict";var r=a(26263),n=a(36766),i=a(1001),o=(0,i.Z)(n.Z,r.s,r.x,!1,null,"5e7ef0ae",null);e["Z"]=o.exports},26547:function(t,e,a){"use strict";a.r(e),a.d(e,{default:function(){return v}});var r=function(){var t=this,e=this,a=e.$createElement,r=e._self._c||a;return r("div",{staticClass:"fit"},[r("ta-border-layout",{attrs:{"footer-cfg":{showBorder:!1},layout:{header:"110px",footer:"0px"},showPadding:!0}},[r("div",{attrs:{slot:"header"},slot:"header"},[r("ta-title",{attrs:{title:"查询条件"}}),r("ta-form",{staticStyle:{"padding-top":"5px"},attrs:{autoFormCreate:function(e){t.baseInfoForm=e},col:2,formLayout:!0}},[r("ta-form-item",{attrs:{"label-col":{span:8},span:4,"wrapper-col":{span:16},"field-decorator-id":"dateFlag","init-value":"audit"}},[r("span",{attrs:{slot:"label"},slot:"label"},[e._v(" 时间类型 ")]),r("ta-radio-group",{staticStyle:{width:"100%"}},[r("ta-radio-button",{staticStyle:{width:"50%","text-align":"center"},attrs:{value:"audit"}},[e._v("审核时间")]),r("ta-radio-button",{staticStyle:{width:"50%","text-align":"center"},attrs:{value:"outpa"}},[e._v("出院时间")])],1)],1),r("ta-form-item",{attrs:{fieldDecoratorOptions:{rules:[{required:!0,message:"必须选取日期"}]},"init-value":e.rangeValue,"label-col":{span:7},required:!0,span:4,"wrapper-col":{span:17},fieldDecoratorId:"allDate",label:"时间范围"}},[r("ta-range-picker",{attrs:{"allow-one":!0,format:"YYYY-MM-DD"}},[r("ta-icon",{attrs:{slot:"suffixIcon",type:""},slot:"suffixIcon"})],1)],1),r("ta-form-item",{attrs:{"label-col":{span:8},span:4,"wrapper-col":{span:16},"field-decorator-id":"akb020",label:"院区标识"}},[r("ta-select",{attrs:{disabled:e.paramsDisable.akb020,options:e.akb020List,allowClear:"",placeholder:"院区选择"}})],1),r("ta-form-item",{attrs:{span:8}}),r("ta-form-item",{attrs:{"label-col":{span:8},span:4,"wrapper-col":{span:16},label:" "}},[r("ta-button",{attrs:{icon:"search",type:"primary"},on:{click:e.fnQuery}},[e._v("查询")]),r("ta-button",{attrs:{icon:"redo",type:"default"},on:{click:e.fnReset}},[e._v("重置")])],1)],1)],1),r("div",{staticClass:"fit",staticStyle:{height:"88%"}},[r("ta-title",{staticStyle:{width:"50%"},attrs:{title:"查询结果"}}),r("ta-big-table",{ref:"Table",attrs:{data:e.userList,"footer-method":e.footerMethod,"auto-resize":"",border:"","empty-text":"-",height:"100%","highlight-current-row":"","highlight-hover-row":"",resizable:"","show-footer":"","show-overflow":""}},[r("ta-big-table-column",{attrs:{align:"center","header-align":"center","min-width":"50",sortable:"",title:"序号",type:"seq"}}),r("ta-big-table-column",{attrs:{align:"center",field:"fyfl","header-align":"center","min-width":"140",sortable:"",title:"费用分类"}}),r("ta-big-table-column",{attrs:{align:"center",field:"hzmx","header-align":"center","min-width":"300",title:"患者明细"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[r("ta-button",{attrs:{size:"small",type:"link"},on:{click:function(t){return e.cellClickEvent(a)}}},[r("span",{staticStyle:{color:"#00bcff","text-decoration":"underline"}},[e._v("查看患者明细")])])]}}])}),r("ta-big-table-column",{attrs:{align:"right",field:"ycrc","header-align":"center","min-width":"140",sortable:"",title:"异常人次"}}),r("ta-big-table-column",{attrs:{align:"right",field:"ycrs","header-align":"center","min-width":"140",sortable:"",title:"异常人数"}}),r("ta-big-table-column",{attrs:{formatter:e.moneyFormat,align:"right",field:"ycje","header-align":"center","min-width":"140",sortable:"",title:"异常金额"}}),r("ta-big-table-column",{attrs:{formatter:e.ratioFormat,align:"right",field:"ycjezb","header-align":"center","min-width":"180",sortable:"",title:"异常金额占比"}}),r("template",{slot:"bottomBar"},[r("ta-button",{staticStyle:{float:"right","margin-top":"20px"},attrs:{icon:"download",type:"primary"},on:{click:e.exportTable}},[e._v("导出 ")]),r("ta-pagination",{ref:"gridPager",staticStyle:{float:"right","margin-top":"20px","margin-right":"10px"},attrs:{"data-source":e.userList,defaultPageSize:1e3,hideOnSinglePage:!0,pageSizeOptions:["1000"],params:e.infoPageParams,simple:!0,url:"reportStatistics/querydscgErrorCostCountByAka063"},on:{"update:dataSource":function(t){e.userList=t},"update:data-source":function(t){e.userList=t}}})],1)],2)],1)])],1)},n=[],i=a(66347),o=a(48534),s=a(95082),l=(a(36133),a(88412)),u=a(36797),c=a.n(u),f=a(22722),m=a(55115),d=a(92566),p=a(83231);m.w3.prototype.Base=Object.assign(m.w3.prototype.Base,(0,s.Z)({},f.Z));var b={name:"dscgErrorCostCount",components:{TaTitle:l.Z},data:function(){return{userList:[],permissions:{},paramsDisable:{akb020:!1},akb020List:[],rangeValue:[this.Base.getMoment((new Date).toISOString().slice(0,8)+"-01","YYYY-MM-DD"),this.Base.getMoment((new Date).toISOString().slice(0,10),"YYYY-MM-DD")]}},mounted:function(){var t=this;return(0,o.Z)(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,p.Z.permissionCheck();case 2:t.permissions=e.sent,t.fnQueryHos(),t.fnQuery();case 5:case"end":return e.stop()}}),e)})))()},methods:{moment:c(),fnQueryHos:function(){var t=this,e={url:"/miimCommonRead/queryHospRpcList",showPageLoading:!1,autoValid:!0},a={successCallback:function(e){t.akb020List=e.data.resultData,t.permissions&&t.permissions.akb020Set.size>0&&(t.paramsDisable.akb020=!0,t.akb020List=t.akb020List.filter((function(e){return t.permissions.akb020Set.has(e.value)})),t.baseInfoForm.setFieldsValue({akb020:t.akb020List[0].value})),t.akb020=t.akb020List[0].value},failCallback:function(e){t.$message.error("医院数据加载失败")}};this.Base.submit(null,e,a)},footerMethod:function(t){var e=t.columns,a=t.data;return[e.map((function(t,e){return 0===e?"合计":["ycje","ycrc","ycrs"].includes(t.property)?(0,d.Z)(a,t.property):null}))]},cellClickEvent:function(t){var e=this.baseInfoForm.getFieldsValue();e.akb020||(e.akb020=""),e.aae500="7",this.Base.openTabMenu({id:Math.floor(901*Math.random())+100,name:"住院患者审核疑点查询",url:"querycommon.html#/inpatientGather?aka063=".concat(t.aka063,"&params=").concat(JSON.stringify(e)),refresh:!1})},exportTable:function(){var t=this,e=this.baseInfoForm.getFieldsValue();if(void 0===e.allDate||e.allDate.length<2)this.$message.error("请选择时间范围！");else{e=this.infoPageParams();var a,r=[],n=this.$refs.Table.getColumns(),o=(0,i.Z)(n);try{for(o.s();!(a=o.n()).done;){var s=a.value;"序号"!==s.title&&"患者明细"!==s.title&&r.push({header:s.title,key:s.property,width:20})}}catch(l){o.e(l)}finally{o.f()}this.Base.submit(null,{url:"reportStatistics/exportdscgErrorCostCountByAka063",data:e,autoValid:!1},{successCallback:function(e){var a=e.data.data,n={fileName:"出院异常费用分类汇总统计结果表",sheets:[{name:"worksheet1",column:{complex:!1,columns:r},rows:a,codeList:[{columnKey:"ycjezb",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}}]}]};t.Base.generateExcel(n)},failCallback:function(e){t.$message.error("数据加载失败")}})}},formatAmount:function(t){return t.toFixed(2).replace(/\d(?=(\d{3})+\.)/g,"$&,")},moneyFormat:function(t){var e=t.cellValue;return"--"==e?e:this.formatAmount(e)},ratioFormat:function(t){var e=t.cellValue;return 0==e?"0":e+"%"},fnReset:function(){this.baseInfoForm.resetFields(),this.fnQuery()},infoPageParams:function(){var t=this.baseInfoForm.getFieldsValue();return t.startDate=t.allDate[0].format("YYYY-MM-DD HH:mm:ss"),t.endDate=t.allDate[1].format("YYYY-MM-DD HH:mm:ss"),t},fnQuery:function(){var t=this,e=this.baseInfoForm.getFieldValue("allDate");!e||e[0]&&e[1]?this.$nextTick((function(){t.$refs.gridPager.loadData((function(t){}))})):this.$message.error("请选择时间范围！")},fnCustomHeaderCell:function(t){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px",fontWeight:"bold"}}},filterOption:function(t,e){return e.componentOptions.children[0].text.indexOf(t)>=0}}},h=b,g=a(1001),y=(0,g.Z)(h,r,n,!1,null,"3392d5de",null),v=y.exports},36766:function(t,e,a){"use strict";var r=a(66586);e["Z"]=r.Z},26263:function(t,e,a){"use strict";a.d(e,{s:function(){return r},x:function(){return n}});var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},n=[]},66586:function(t,e){"use strict";var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};e["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}},83231:function(t,e,a){"use strict";var r=a(48534);a(36133);function n(t){return t?t.akb020&&t.aaz263?"personal":t.akb020&&t.aaz309&&t.aaz307?"group":t.akb020&&t.aaz307?"department":t.akb020?"hospital":"all":"all"}function i(t){return o.apply(this,arguments)}function o(){return o=(0,r.Z)(regeneratorRuntime.mark((function t(e){var a,r,i,o,s,l,u,c,f,m,d,p;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Base.submit(null,{url:"/miimCommonRead/queryDataPermission",showPageLoading:!1,data:{jobnumber:e},autoValid:!0});case 2:return a=t.sent,r=new Set,i=new Set,a.data.permission.forEach((function(t){var e=n(t);"hospital"===e&&r.add(t.akb020),"department"===e&&i.add(t.aaz307)})),o=a.data.permission.filter((function(t){return"department"===n(t)||!i.has(t.aaz307)})).filter((function(t){return"hospital"===n(t)||!r.has(t.akb020)})),s=new Set(o.map((function(t){return t.akb020})).filter((function(t){return null!=t}))),l=new Set(o.map((function(t){return t.aaz307})).filter((function(t){return null!=t}))),u=new Set(o.map((function(t){return t.aaz309})).filter((function(t){return null!=t}))),c=new Set(o.map((function(t){return t.aaz263})).filter((function(t){return null!=t}))),f=!1,m=!1,d=!1,p=!1,1===s.size&&(f=!0),1===l.size&&1===s.size&&(m=!0),1===l.size&&1===s.size&&1===u.size&&(d=!0),1===s.size&&0===l.size&&1===c.size&&(p=!0),t.abrupt("return",{akb020Set:s,aaz307Set:l,aaz263Set:c,aaz309Set:u,akb020Disable:f,aaz307Disable:m,aaz263Disable:p,aaz309Disable:d});case 20:case"end":return t.stop()}}),t)}))),o.apply(this,arguments)}function s(t,e){Base.submit(null,{url:"miimCommonRead/getAa01AAE500StartStop",data:t},{successCallback:function(t){return e(t)}})}function l(t,e){Base.submit(null,{url:"miimCommonRead/insertTableColumShow",data:t,autoValid:!1},{successCallback:function(t){return e("success")},failCallback:function(t){return e("fail")}})}var u={zbx01:{footer:function(){return"统筹金总额"}},zbx02:{footer:function(){return"基金支出总额"}},zbx03:{footer:function(){return"总费用"}},zbx04:{footer:function(){return"总住院日"}}};e["Z"]={permissionCheck:i,getAa01AAE500StartStop:s,insertTableColumShow:l,props:u,moneyNumFormat:function(t){var e=t.cellValue;return e||"—"}}},55382:function(){},61219:function(){}}]);
(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[797],{88412:function(t,e,a){"use strict";var r=a(26263),n=a(36766),i=a(1001),o=(0,i.Z)(n.Z,r.s,r.x,!1,null,"5e7ef0ae",null);e["Z"]=o.exports},16442:function(t,e,a){"use strict";a.r(e),a.d(e,{default:function(){return v}});var r=function(){var t=this,e=this,a=e.$createElement,r=e._self._c||a;return r("div",{staticClass:"fit"},[r("ta-border-layout",{attrs:{layout:{header:"110px",footer:"0px"},showPadding:!0,"footer-cfg":{showBorder:!1}}},[r("div",{attrs:{slot:"header"},slot:"header"},[r("ta-title",{attrs:{title:"查询条件"}}),r("ta-form",{staticStyle:{"padding-top":"5px"},attrs:{autoFormCreate:function(e){t.baseInfoForm=e},layout:"horizontal",formLayout:!0}},[r("ta-form-item",{attrs:{span:5,fieldDecoratorId:"allDate",fieldDecoratorOptions:{rules:[{required:!0,message:"必须选取日期"}]},required:!0,"init-value":e.rangeValue,labelCol:{span:6},wrapperCol:{span:14},label:"出院日期"}},[r("ta-date-picker",{on:{change:e.onChange}})],1),r("ta-button",{staticStyle:{float:"right"},attrs:{type:"primary",icon:"search"},on:{click:e.fnQuery}},[e._v("查询 ")])],1)],1),r("div",{staticClass:"fit",staticStyle:{height:"89%"}},[r("ta-title",{staticStyle:{width:"50%"},attrs:{title:"查询结果"}}),r("ta-big-table",{ref:"Table",attrs:{height:"100%","show-footer":"","footer-method":e.footerMethod,"highlight-hover-row":"","highlight-current-row":"","tooltip-config":{enabled:!0,contentMethod:e.showTooltipMethod},"auto-resize":"","show-overflow":"",resizable:"","empty-text":"-",border:"",data:e.dataList},on:{"cell-click":e.cellClickEvent}},[r("ta-big-table-column",{attrs:{type:"seq","header-align":"center",align:"center",title:"序号","min-width":"40",sortable:""}}),r("ta-big-table-column",{attrs:{sortable:"",field:"aae386","header-align":"center",align:"left",title:"病区科室","min-width":"100"}}),r("ta-big-table-column",{attrs:{sortable:"",field:"hiscyrc","header-align":"center",align:"right",title:"HIS出院数(人次)","min-width":"120"}}),r("ta-big-table-column",{attrs:{sortable:"",field:"shzrc","header-align":"center",align:"right",title:"物价审核数(人次)","min-width":"120"}}),r("ta-big-table-column",{attrs:{sortable:"",field:"shsyl","header-align":"center",align:"right",formatter:e.ratioFormat,title:"物价审核使用率(%)","min-width":"140"}}),r("ta-big-table-column",{attrs:{sortable:"",field:"wgzrc","header-align":"center",align:"right",title:"物价审核违规数(人次)","min-width":"180"}}),r("ta-big-table-column",{attrs:{sortable:"",field:"wgl","header-align":"center",align:"right",formatter:e.ratioFormat,title:"违规率(%)","min-width":"100"}}),r("ta-big-table-column",{attrs:{sortable:"",field:"jsrc","header-align":"center",align:"right",title:"结算人数(人次)","min-width":"140"}}),r("ta-big-table-column",{attrs:{sortable:"",field:"jswshrc","header-align":"center",align:"right",title:"结算未审核人数(人次)","min-width":"120"}}),r("template",{slot:"bottomBar"},[r("ta-button",{staticStyle:{float:"right","margin-top":"20px"},attrs:{icon:"download",type:"primary"},on:{click:e.exportTable}},[e._v("导出")]),r("ta-pagination",{ref:"gridPager",staticStyle:{float:"right","margin-top":"20px","margin-right":"10px"},attrs:{defaultPageSize:100,pageSizeOptions:["100","200","500","1000","1500"],"data-source":e.dataList,params:e.infoPageParams,url:"NurdscgStat/queryNrdscgStatKSPage"},on:{"update:dataSource":function(t){e.dataList=t},"update:data-source":function(t){e.dataList=t}}})],1)],2)],1)])],1)},n=[],i=a(66347),o=a(48534),l=a(95082),s=(a(36133),a(88412)),c=a(36797),u=a.n(c),f=a(22722),h=a(55115),d=a(88097),g=a(92566);a(55192);h.w3.prototype.Base=Object.assign(h.w3.prototype.Base,(0,l.Z)({},f.Z));var m={name:"nurdscgStatKS",components:{TaTitle:s.Z},data:function(){return{dataList:[],rangeValue:this.Base.getMoment((new Date).toISOString().slice(0,10),"YYYY-MM-DD")}},mounted:function(){var t=this;return(0,o.Z)(regeneratorRuntime.mark((function e(){var a,r;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.$route.query.aae031&&(a=new Date(t.$route.query.aae031),r=t.Base.getMoment(a.toISOString().slice(0,10),"YYYY-MM-DD"),t.baseInfoForm.setFieldsValue({allDate:r})),t.$nextTick((function(){t.fnQuery()}));case 2:case"end":return e.stop()}}),e)})))()},methods:{moment:u(),onChange:function(t,e){this.fnQuery()},footerMethod:function(t){var e=t.columns,a=t.data;return[e.map((function(t,e){return 0===e?"合计":["aae386"].includes(t.property)?null:["shsyl"].includes(t.property)?parseFloat(0===(0,g.Z)(a,"hiscyrc")?0:(0,g.Z)(a,"shzrc")/(0,g.Z)(a,"hiscyrc")*100).toFixed(2)+"%":["wgl"].includes(t.property)?parseFloat(0===(0,g.Z)(a,"shzrc")?0:(0,g.Z)(a,"wgzrc")/(0,g.Z)(a,"shzrc")*100).toFixed(2)+"%":(0,g.Z)(a,t.property)}))]},showTooltipMethod:function(t){var e=t.type,a=t.column,r=(t.row,t.items,t._columnIndex,a.property);return"header"===e&&d.Z.props[r]?d.Z.props[r][e]:""},cellClickEvent:function(t){t.row,t.column},exportTable:function(){var t=this.infoPageParams();if(t.allDate){var e,a=[],r=this.$refs.Table.getColumns(),n=(0,i.Z)(r);try{for(n.s();!(e=n.n()).done;){var o=e.value;"序号"!==o.title&&a.push({header:o.title,key:o.property,width:20})}}catch(s){n.e(s)}finally{n.f()}var l={fileName:"护士站出院审核率-科室统计结果表",sheets:[{name:"worksheet1",column:{complex:!1,columns:a},rows:this.dataList,codeList:d.Z.codelist}]};this.Base.generateExcel(l)}else this.$message.error("请选择时间范围！")},fnReset:function(){this.baseInfoForm.resetFields()},infoPageParams:function(){var t=this.baseInfoForm.getFieldsValue(),e=new Date("".concat(t.allDate.format("YYYY-MM-DD"),"T00:00:00")),a=new Date(e);a.setHours(0,0,0,0);var r=new Date(e);return r.setDate(e.getDate()+1),r.setHours(0,0,0,0),t.startDate=this.Base.getMoment(a.toISOString()).format("YYYY-MM-DD HH:mm:ss"),t.endDate=this.Base.getMoment(r.toISOString()).format("YYYY-MM-DD HH:mm:ss"),t},fnQuery:function(){var t=this,e=this.baseInfoForm.getFieldValue("allDate");e?this.$nextTick((function(){t.$refs.gridPager.loadData((function(t){}))})):this.$message.error("请选择时间范围！")},fnCustomHeaderCell:function(t){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px",fontWeight:"bold"}}},filterOption:function(t,e){return e.componentOptions.children[0].text.indexOf(t)>=0},formatAmount:function(t){return t.toFixed(2).replace(/\d(?=(\d{3})+\.)/g,"$&,")},moneyFormat:function(t){var e=t.cellValue;return"--"==e?e:this.formatAmount(e)},ratioFormat:function(t){var e=t.cellValue;return 0!==e&&e?e+"%":"0%"}}},p=m,b=a(1001),y=(0,b.Z)(p,r,n,!1,null,"36a76c61",null),v=y.exports},36766:function(t,e,a){"use strict";var r=a(66586);e["Z"]=r.Z},26263:function(t,e,a){"use strict";a.d(e,{s:function(){return r},x:function(){return n}});var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},n=[]},66586:function(t,e){"use strict";var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};e["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}},88097:function(t,e){"use strict";var a=[{columnKey:"shsyl",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}},{columnKey:"wgl",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}}],r={shsyl:{header:function(){return"物价审核使用率(%)=HIS出院数(人次)/物价审核数(人次)"}},wgl:{header:function(){return"违规率(%)=物价审核违规数(人次)/物价审核数(人次)"}}};e["Z"]={codelist:a,props:r}},55382:function(){},61219:function(){}}]);
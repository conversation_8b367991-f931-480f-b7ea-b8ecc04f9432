"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[5657],{81398:function(t,e,a){a.r(e),a.d(e,{default:function(){return S}});var i=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("div",{staticClass:"fit"},[i("ta-border-layout",[i("div",{staticStyle:{height:"98%"}},[i("ta-form",{staticStyle:{"margin-top":"13px"},attrs:{layout:"horizontal",formLayout:!0,"auto-form-create":function(e){return t.form=e}}},[e._l(e.defaultSearch.slice(0,4),(function(t){return[t.name.length>5?i("ta-form-item",{key:t.id,attrs:{labelWidth:"120",label:t.name,fieldDecoratorId:t.id,span:5}},["1"!=t.isTimeDimension?[t.data.length>30?i("ta-select",{attrs:{mode:"multiple",maxTagCount:1,options:t.data,"allow-clear":"",virtual:""}}):i("ta-select",{attrs:{mode:"multiple",maxTagCount:1,options:t.data,"allow-clear":""}})]:i("timePick",{attrs:{choseTime:e.choseTime,timeOptionsData:t.treeData}})],2):i("ta-form-item",{key:t.id,attrs:{label:t.name,fieldDecoratorId:t.id,span:5}},["1"!=t.isTimeDimension?[t.data.length>30?i("ta-select",{attrs:{mode:"multiple",maxTagCount:1,options:t.data,"allow-clear":"",virtual:""}}):i("ta-select",{attrs:{mode:"multiple",maxTagCount:1,options:t.data,"allow-clear":""}})]:i("timePick",{attrs:{choseTime:e.choseTime,timeOptionsData:t.treeData}})],2)]})),e.defaultSearch.length>4?[e.defaultSearch.length>4?i("ta-form-item",{staticStyle:{"text-align":"right"},attrs:{span:2}},[e.hide?i("a",{on:{click:e.hideState}},[i("ta-icon",{attrs:{slot:"suffixIcon",type:"caret-down"},slot:"suffixIcon"})],1):e._e(),e.hide?e._e():i("a",{on:{click:e.hideState}},[i("ta-icon",{attrs:{slot:"suffixIcon",type:"caret-up"},slot:"suffixIcon"})],1)]):e._e(),i("ta-form-item",{staticStyle:{"text-align":"right"},attrs:{span:1}},[i("ta-button",{attrs:{type:"primary"},on:{click:e.searchTable}},[e._v(" 查询 ")])],1)]:[i("ta-form-item",{staticStyle:{"text-align":"right"},attrs:{span:3}},[i("ta-button",{attrs:{type:"primary"},on:{click:e.searchTable}},[e._v(" 查询 ")])],1)],e._l(e.defaultSearch.slice(4),(function(t){return e.hide?e._e():[t.name.length>5?i("ta-form-item",{key:t.id,attrs:{labelWidth:"120",label:t.name,fieldDecoratorId:t.id,span:5}},["1"!=t.isTimeDimension?[t.data.length>30?i("ta-select",{attrs:{mode:"multiple",maxTagCount:1,options:t.data,"allow-clear":"",virtual:""}}):i("ta-select",{attrs:{mode:"multiple",maxTagCount:1,options:t.data,"allow-clear":""}})]:i("timePick",{attrs:{choseTime:e.choseTime,timeOptionsData:t.treeData}})],2):i("ta-form-item",{key:t.id,attrs:{label:t.name,fieldDecoratorId:t.id,span:5}},["1"!=t.isTimeDimension?[t.data.length>30?i("ta-select",{attrs:{mode:"multiple",maxTagCount:1,options:t.data,"allow-clear":"",virtual:""}}):i("ta-select",{attrs:{mode:"multiple",maxTagCount:1,options:t.data,"allow-clear":""}})]:i("timePick",{attrs:{choseTime:e.choseTime,timeOptionsData:t.treeData}})],2)]}))],2),i("div",{staticStyle:{height:"1px",background:"#eee","margin-top":"0px"}}),i("div",{staticClass:"bottomBox"},[i("div",{staticClass:"tableBox",attrs:{id:"tableBox"}},[i("div",{staticClass:"tableBox_title"},[i("div",[i("span",{attrs:{id:"dimensionName"}},[e._v("维度:")]),i("el-checkbox-group",{staticStyle:{float:"right","margin-top":"2px"},on:{change:e.changeDimensionality},model:{value:e.selectList,callback:function(t){e.selectList=t},expression:"selectList"}},e._l(e.dimensionalityCheckBox,(function(t){return i("el-checkbox",{key:t.value,attrs:{label:t.value}},[e._v(e._s(t.label))])})),1)],1),i("ta-button",{attrs:{type:"primary"},on:{click:e.exportData}},[e._v(" 导出 ")])],1),i("div",{staticStyle:{"margin-top":"20px",height:"92%"}},[i("ta-big-table",{ref:"bigTable",attrs:{height:"auto",align:"center",border:"",data:e.tableData,size:"small","highlight-current-row":"","cell-style":e.cellStyle,"auto-resize":"","span-method":e.objectSpanMethod}},[i("template",{slot:"empty"},[i("ta-empty")],1),e._l(e.tableColArr,(function(t,a){return["dim_18"==t.field?i("ta-big-table-column",{attrs:{field:"dim_18",title:"病组编码",width:"200"}}):e._e(),i("ta-big-table-column",{key:a+Math.random(),attrs:{field:t.field+"_desc",title:t.name,width:"201"},scopedSlots:e._u([{key:"header",fn:function(){return[e.tableColArrLen+1==1?[e._v(" "+e._s(t.name)+" ")]:[e._v(" "+e._s(t.name)+" "),0==a?i("ta-icon",{attrs:{type:"right"},on:{click:function(a){return e.moveColumn(t,"right")}}}):a==e.tableColArrLen?i("ta-icon",{attrs:{type:"left"},on:{click:function(a){return e.moveColumn(t,"left")}}}):[i("ta-icon",{attrs:{type:"left"},on:{click:function(a){return e.moveColumn(t,"left")}}}),i("ta-icon",{attrs:{type:"right"},on:{click:function(a){return e.moveColumn(t,"right")}}})]]]},proxy:!0}],null,!0)})]})),e._l(e.tableColumn,(function(t,a){return[0===t.child.length&&!0===t.fold?i("ta-big-table-column",{key:t.field,attrs:{"scroll-y":{gt:-1},"scroll-x":{gt:-1},field:t.field,title:t.name,"min-width":"150"},scopedSlots:e._u([{key:"header",fn:function(){return[e._v(" "+e._s(t.name)+" "),!0===t.sign?i("a",{staticStyle:{"margin-right":"5px"},on:{click:function(a){return e.changeFold(t.name,"open")}}},[i("ta-icon",{attrs:{type:"plus"}})],1):e._e(),i("ta-popover",{attrs:{placement:"bottom"}},[i("div",{staticClass:"referenceStyle"},[i("ta-input",{ref:"searchInput",refInFor:!0,attrs:{placeholder:"搜索内容"},on:{pressEnter:function(a){return e.filterHandle(t.field,t.value)}},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"item.value"}}),i("ta-button",{attrs:{type:"primary"},on:{click:function(a){return e.filterHandle(t.field,t.value)}}},[e._v("搜索")]),i("ta-button",{attrs:{id:"searchDrgMdcCodeButton"},on:{click:function(a){return e.filterHandleCancel(t.field,t.value)}}},[e._v("重置")])],1),i("ta-icon",{attrs:{slot:"reference",type:"filter"},slot:"reference"})],1)]},proxy:!0},!0===t.jump?{key:"default",fn:function(a){var n=a.row;return[i("a",{on:{click:function(a){return e.jumpMethod(t.jumpID,t.jumpName,t.jumpUrl,n)}}},[e._v(e._s(n[t.field]))])]}}:"diff"==t.field.substr(-4)?{key:"default",fn:function(a){var n=a.row;return[n[t.field]>0?i("div",[e._v(" "+e._s(n[t.field])+" "),i("ta-icon",{staticStyle:{color:"green"},attrs:{type:"caret-up"}})],1):n[t.field]<0?i("div",[e._v(" "+e._s(n[t.field])+" "),i("ta-icon",{staticStyle:{color:"red"},attrs:{type:"caret-down"}})],1):i("div",[e._v(" "+e._s(n[t.field])+" ")])]}}:null],null,!0)}):e._e(),t.child.length>0&&!0===t.fold&&"plus"===t.calculate?i("ta-big-table-column",{key:a,attrs:{title:t.name},scopedSlots:e._u([{key:"header",fn:function(){return[i("div",[e._v(" "+e._s(t.name)+" "),!0===t.sign&&"plus"===t.calculate?i("a",{on:{click:function(a){return e.changeFold(t.name,"open")}}},[i("ta-icon",{attrs:{type:"plus"}})],1):e._e(),!0===t.sign&&"minus"===t.calculate?i("a",{on:{click:function(a){return e.changeFold(t.name,"close")}}},[i("ta-icon",{attrs:{type:"minus"}})],1):e._e()])]},proxy:!0}],null,!0)},[e._l(t.child,(function(a){return[i("ta-big-table-column",{key:a.id,attrs:{field:a.field,title:a.name,width:"150"},scopedSlots:e._u([!0===a.jump?{key:"default",fn:function(t){var n=t.row;return[i("a",{on:{click:function(t){return e.jumpMethod(a.jumpID,a.jumpName,a.jumpUrl,n)}}},[e._v(e._s(n[a.field]))])]}}:"diff"==t.field.substr(-4)?{key:"default",fn:function(a){var n=a.row;return[n[t.field]>0?i("div",[e._v(" "+e._s(n[t.field])+" "),i("ta-icon",{staticStyle:{color:"green"},attrs:{type:"caret-up"}})],1):n[t.field]<0?i("div",[e._v(" "+e._s(n[t.field])+" "),i("ta-icon",{staticStyle:{color:"red"},attrs:{type:"caret-down"}})],1):i("div",[e._v(" "+e._s(n[t.field])+" ")])]}}:null],null,!0)})]}))],2):e._e(),t.child.length>0&&!0===t.fold&&"minus"===t.calculate?i("ta-big-table-colgroup",{key:a,attrs:{title:t.name},scopedSlots:e._u([{key:"header",fn:function(){return[i("div",[e._v(" "+e._s(t.name)+" "),!0===t.sign&&"plus"===t.calculate?i("a",{on:{click:function(a){return e.changeFold(t.name,"open")}}},[i("ta-icon",{attrs:{type:"plus"}})],1):e._e(),!0===t.sign&&"minus"===t.calculate?i("a",{on:{click:function(a){return e.changeFold(t.name,"close")}}},[i("ta-icon",{attrs:{type:"minus"}})],1):e._e()])]},proxy:!0}],null,!0)},[e._l(t.child,(function(a){return[i("ta-big-table-column",{key:a.id,attrs:{field:a.field,title:a.name,width:"150"},scopedSlots:e._u([!0===a.jump?{key:"default",fn:function(t){var n=t.row;return[i("a",{on:{click:function(t){return e.jumpMethod(a.jumpID,a.jumpName,a.jumpUrl,n)}}},[e._v(e._s(n[a.field]))])]}}:"diff"==t.field.substr(-4)?{key:"default",fn:function(a){var n=a.row;return[n[t.field]>0?i("div",[e._v(" "+e._s(n[t.field])+" "),i("ta-icon",{staticStyle:{color:"green"},attrs:{type:"caret-up"}})],1):n[t.field]<0?i("div",[e._v(" "+e._s(n[t.field])+" "),i("ta-icon",{staticStyle:{color:"red"},attrs:{type:"caret-down"}})],1):i("div",[e._v(" "+e._s(n[t.field])+" ")])]}}:null],null,!0)})]}))],2):e._e()]}))],2)],1)]),e.chartHide?i("div",{staticClass:"foldBtn",on:{click:function(t){return e.changeChartState("1")}}},[e._v(" 图表收起 "),i("ta-icon",{attrs:{type:"right-circle"}})],1):e._e(),e.chartHide?e._e():i("div",{staticClass:"foldBtn",on:{click:function(t){return e.changeChartState("2")}}},[e._v(" 图表展开 "),i("ta-icon",{attrs:{type:"left-circle"}})],1),i("div",{staticClass:"echartsBox",attrs:{id:"echartsBox"}},[i("div",{staticStyle:{"text-align":"right"}},[i("ta-button",{attrs:{type:"primary"},on:{click:e.addEchart}},[e._v(" 添加图表 ")])],1),i("div",{staticStyle:{"margin-top":"20px",height:"92%",overflow:"auto"}},e._l(e.onComponentArr,(function(t,a){return i("div",{key:a,staticStyle:{"margin-top":"20px",position:"relative"}},[i("div",{staticClass:"iconStay"},[t.default?i("a",[e.onComponentArr[a].exhibit?i("ta-icon",{attrs:{type:"eye-invisible"},on:{click:function(t){e.onComponentArr[a].exhibit=!e.onComponentArr[a].exhibit}}}):i("ta-icon",{attrs:{type:"eye"},on:{click:function(t){e.onComponentArr[a].exhibit=!e.onComponentArr[a].exhibit}}})],1):e._e(),t.default?e._e():i("ta-popconfirm",{attrs:{title:"是否确认删除?",okText:"确认",cancelText:"取消"},on:{confirm:function(a){return e.deleteComponents(t)}}},[i("a",[i("ta-icon",{attrs:{type:"delete"}})],1)]),i("a",{on:{click:function(i){return e.editChart(a,t)}}},[i("ta-icon",{staticStyle:{"margin-left":"34px"},attrs:{type:"setting"}})],1)],1),i(t.name,{directives:[{name:"show",rawName:"v-show",value:t.exhibit,expression:"item.exhibit"}],tag:"component",attrs:{echartId:t.echartId,defaultChart:e.defaultChart,chartData:e.chartData,default:t.default,index:a,"search-obj":e.searchObj,"echart-tool-arr":e.echartToolArr,"send-table-data":e.sendTableData,selectList:e.selectList}})],1)})),0)])])],1)]),i("ta-modal",{attrs:{title:"图表配置工具",visible:e.toolVisible,height:"590px",width:"1000px","destroy-on-close":!0},on:{cancel:e.handleCancel}},[i("configTool",{ref:"tool",attrs:{"on-component-arr":e.onComponentArr,resourceid:e.resourceid},on:{getChart:e.getChart,success:e.success}}),i("template",{slot:"footer"},[e.modalBtn?i("div",[i("ta-button",{on:{click:e.handleCancel}},[e._v(" 取消 ")]),e.buttonState?e._e():i("ta-button",{on:{click:e.turnOn}},[e._v(" 上一步 ")]),e.buttonState?i("ta-button",{attrs:{type:"primary"},on:{click:e.turnDown}},[e._v(" 下一步 ")]):i("ta-button",{attrs:{type:"primary"},on:{click:e.turnDown}},[e._v(" 确定 ")])],1):i("div",[i("ta-button",{on:{click:e.handleCancel}},[e._v(" 取消 ")]),i("ta-button",{attrs:{type:"primary"},on:{click:e.turnDown}},[e._v(" 确定 ")])],1)])],2)],1)},n=[],r=a(49699),l=a(60410),o=a(29251),s=a(27366),c=a(78683),u=a(74771),d=a(542),h=a(20675),f=a(6204),m=a(46109),p=a(11119),b=a(14415),v=a(64714),y=a(60213),g=a(31104),C=a(34209),x=(a(56265),{components:{dimensionalityMonth:r.Z,dimensionalityYear:l.Z,dimensionalityArea:o.Z,dimensionalityMiType:s.Z,dimensionalityPay:c.Z,dimensionalityDisease:u.Z,dimensionalityDepartment:d.Z,dimensionalityPatientArea:h.Z,dimensionalityStaff:f.Z,barChart:m.Z,histogram:p.Z,lineChart:b.Z,lineHistogram:v.Z,pieChart:y.Z,configTool:g.Z,timePick:C.Z},data:function(){return{resourceid:"",rowData:"",defaultDimension:"",defaultSearch:[],tableColumn:[],defaultChart:[],chartData:[],dataArr:{},pageSeach:!1,choseTime:"",buttonState:!0,dimensionalityArr:[],addDimensionState:!0,hide:!0,chartHide:!0,firstName:"",tableData:[],oldTableData:[],searchObj:{},searchLineArr:[],conflict:!0,sendTableData:{},echartToolArr:[],toolVisible:!1,onComponentArr:[],reColumnsKey:[],indexColumnsArr:[],dimensionalityCheckBox:[],selectList:[],tableColArr:[],tableColArrLen:0,tableDataFilter:[],modalBtn:!0}},watch:{pageSeach:{handler:function(t){var e=this;t&&this.$nextTick((function(){e.init()}))}}},created:function(){var t=this.$route.query;this.resourceid=t.resourceid,this.defaultDimension=t.defaultDimension,this.defaultSearch=t.defaultSearch,this.defaultChart=t.defaultChart,this.chartData=t.chartData,this.dataArr=t.dataArr},mounted:function(){var t=this,e=this.$route.query;this.rowData=void 0==e.rowData?"":JSON.parse(e.rowData),""!=this.rowData?this.$nextTick((function(){t.Base.submit(null,{url:"statisticsModal/queryStatisticsDimension",data:{resourceId:t.resourceid}},{successCallback:function(e){var a=e.data.result;a.map((function(e){t.dimensionalityCheckBox.push({label:e.name,value:e.id})})),t.searchObj.resourceid=t.resourceid;var i=[];t.dimensionalityCheckBox.forEach((function(e){for(var a in t.rowData)e.value==a&&i.push(a+"")})),t.defaultSearch.forEach((function(e){if("1"==e.isTimeDimension)for(var a in t.rowData)a==e.id&&(t.choseTime=t.rowData[a][0])})),t.form.setFieldsValue(t.rowData),t.selectList=i,t.$nextTick((function(){t.searchTable()}))}})})):this.pageSeach=!0},methods:{getPageTargetInfo:function(t){var e=[];t.forEach((function(t){if(t.children){var a={},i={};a.name=t.indexLabel,a.field=t.indexId,a.fold=!0,a.sign=!0,t.jumpResourceName&&(a.jump=!0,a.jumpName=t.jumpResourceName,a.jumpID=t.jumpResourceId,a.jumpUrl=t.jumpResourceUrl),a.value="",a.calculate="plus",a.child=[],i.name=t.indexLabel,i.field=t.indexId,i.fold=!1,i.sign=!0,t.jumpResourceName&&(i.jump=!0,i.jumpName=t.jumpResourceName,i.jumpID=t.jumpResourceId,i.jumpUrl=t.jumpResourceUrl),i.value="",i.calculate="minus",i.child=[],t.children.forEach((function(t){var e={};e.name=t.indexLabel,e.field=t.indexId,t.jumpResourceName&&(e.jump=!0,e.jumpName=t.jumpResourceName,e.jumpID=t.jumpResourceId,e.jumpUrl=t.jumpResourceUrl),e.value="",i.child.push(e)})),e.push(a),e.push(i)}else{var n={};n.name=t.indexLabel,n.field=t.indexId,n.fold=!0,n.sign=!1,t.jumpResourceName&&(n.jump=!0,n.jumpName=t.jumpResourceName,n.jumpID=t.jumpResourceId,n.jumpUrl=t.jumpResourceUrl),n.value="",n.child=[],e.push(n)}})),this.tableColumn=e},changeDimensionality:function(t){this.searchTable()},init:function(){var t=this;this.Base.submit(null,{url:"statisticsModal/queryStatisticsDimension",data:{resourceId:this.resourceid}},{successCallback:function(e){var a=e.data.result;a.map((function(e){t.dimensionalityCheckBox.push({label:e.name,value:e.id})})),t.searchObj.resourceid=t.resourceid,t.$nextTick((function(){t.searchTable()}))}})},hideState:function(){this.hide=!this.hide},changeChartState:function(t){"1"===t?(this.chartHide=!this.chartHide,document.getElementById("tableBox").style.width="99%",document.getElementById("echartsBox").style.width="0"):(this.chartHide=!this.chartHide,document.getElementById("tableBox").style.width="54%",document.getElementById("echartsBox").style.width="45%")},changeFold:function(t,e){"open"===e?this.editFold(!0,t):"close"===e&&this.editFold(!1,t)},editFold:function(t,e){for(var a=0;a<this.tableColumn.length;a++)if(this.tableColumn[a].name===e){!0===t?(this.tableColumn[a].fold=!1,this.tableColumn[a+1].fold=!0):!1===t&&(this.tableColumn[a].fold=!0,this.tableColumn[a+1].fold=!1);break}},userPageParams:function(){return this.searchObj},objectOrder:function(t){var e=Object.keys(t).sort();return e},searchTable:function(){var t=this;this.tableColArr=[];for(var e=function(e){t.dimensionalityCheckBox.map((function(a){t.selectList[e]==a.value&&t.tableColArr.push({field:"dim_"+a.value,name:a.label})}))},a=0;a<this.selectList.length;a++)e(a);this.tableColArrLen=this.tableColArr.length-1;var i=this.form.getFieldsValue();for(var n in i)void 0==i[n]&&delete i[n];var r=this.selectList.join(",");this.Base.submit(null,{url:"statisticsModal/queryStatisticsTableData",data:{condition:i,dimension:r,resourceId:this.resourceid}},{successCallback:function(e){t.oldTableData=e.data.result.data,t.tableData=JSON.stringify(t.oldTableData),t.tableData=JSON.parse(t.tableData),t.sendTableData=e.data,t.getPageTargetInfo(e.data.result.column);var a=[];t.tableColumn.forEach((function(t){t.child.length>0?t.child.forEach((function(t){a.push(t.field+"")})):a.push(t.field+"")}));var n=Array.from(new Set(a));t.searchObj={resourceid:t.resourceid,dimensionId:"",dimensionValue:"",condition:i,defaultSearch:t.defaultSearch,filter:{},sumId:"",sumValue:"",find:"",index:n},t.searchChart()}})},cellStyle:function(t){t.row;var e=t.rowIndex,a=t.column;t.columnIndex;if(""!=this.firstName&&""!=this.searchObj.sumId&&"dim_desc"===a.property&&[0].includes(e))return{textAlign:"left"}},addEchart:function(){var t=this;this.toolVisible=!0,this.$nextTick((function(){t.$refs.tool.saveSign="新增"}))},deleteComponents:function(t){var e=this;this.echartToolArr.forEach((function(a){a.statisticsUserChartId==t.echartId&&e.Base.submit(null,{url:"statisticsModal/delStatisticsUserChart",data:{statisticsUserChartId:a.statisticsUserChartId}},{successCallback:function(t){e.searchChart()}})}))},editChart:function(t,e){var a=this;this.toolVisible=!0;var i="",n="",r=[],l=!0,o=!1,s="编辑";e.default?(this.modalBtn=!1,l=!1,o=!0,r=this.dataArr.defaultChartIndexList,n=this.dataArr.statisticsDefaultChartId,s="默认编辑"):this.echartToolArr.forEach((function(t){t.statisticsUserChartId==e.echartId&&(i=t.chartName,n=t.statisticsUserChartId,r=t.indexList)})),this.$nextTick((function(){a.$refs.tool.state=l,a.$refs.tool.inpDisabled=o,a.$refs.tool.chartName=i,a.$refs.tool.saveSign=s,a.$refs.tool.selectChart=e.name,a.$refs.tool.getChoiceSelect(r,n)}))},handleCancel:function(){var t=this;this.toolVisible=!1,this.modalBtn=!0,this.$nextTick((function(){t.buttonState=!0})),this.$refs.tool.initData()},turnOn:function(){this.$refs.tool.state=!0,this.buttonState=!0},turnDown:function(){"新增"==this.$refs.tool.saveSign?1==this.$refs.tool.state?1==this.$refs.tool.stateChose?(this.$refs.tool.state=!1,this.buttonState=!1):this.$refs.tool.onClick():0==this.$refs.tool.state&&this.$refs.tool.onClick():1==this.$refs.tool.state?(this.$refs.tool.state=!1,this.buttonState=!1):0==this.$refs.tool.state&&this.$refs.tool.onClick()},success:function(t){var e=this;t?this.toolVisible=!t:(this.toolVisible=t,this.$nextTick((function(){e.buttonState=!t})))},getPageInfo:function(){var t=this;this.Base.submit(null,{url:"statisticsModal/queryBaseStatisticsConfig",data:{resourceId:this.resourceid}}).then((function(e){var a=e.data.result;t.chartData=[],a.defaultChartIndexList.forEach((function(e){var a={};a.name=e.indexLabel,a.indexId=e.indexId,e.indexChartType&&(a.indexChartType=e.indexChartType),a.value=["0"],t.chartData.push(a)})),t.dataArr=a}))},getChart:function(t){this.getPageInfo(),this.searchChart()},searchChart:function(){var t=this;this.onComponentArr=[],this.echartToolArr=[],this.defaultChart.length>0&&this.onComponentArr.push({name:this.defaultChart[0],echartId:this.dataArr.statisticsDefaultChartId,default:!0,exhibit:!0}),this.$nextTick((function(){t.Base.submit(null,{url:"statisticsModal/queryStatisticsUserChartConfig",data:{resourceId:t.resourceid}},{successCallback:function(e){var a=e.data.result;0==t.defaultChart.length&&0==a.length&&t.$nextTick((function(){t.changeChartState("1")}));for(var i=0;i<a.length;i++)t.onComponentArr.push({name:a[i].chartType,echartId:a[i].statisticsUserChartId,default:!1,exhibit:!0}),t.echartToolArr.push(a[i])}})}))},filterHandle:function(t,e){var a=this;this.tableDataFilter.push({id:t,value:e}),this.tableDataFilter.forEach((function(t){a.tableData=a.tableData.filter((function(e){return e[t.id]==t.value}))}))},filterHandleCancel:function(t,e){var a=this;this.tableDataFilter.forEach((function(e,i){e.id==t&&a.tableDataFilter.splice(i,1)})),this.tableColumn.forEach((function(e){e.field==t&&(e.value="")})),this.tableDataFilter.length>0?this.tableDataFilter.forEach((function(t){a.tableData=a.oldTableData.filter((function(e){return e[t.id]==t.value}))})):this.tableData=this.oldTableData},exportData:function(){},moveColumn:function(t,e){for(var a=this,i=JSON.parse(JSON.stringify(this.selectList)),n=0,r=0;r<i.length;r++){var l="dim_"+i[r];t.field==l&&(n=r)}if("right"==e){var o=i[n+1];i[n+1]=i[n],i[n]=o}else{var s=i[n-1];i[n-1]=i[n],i[n]=s}this.selectList=i,this.$nextTick((function(){a.searchTable()}))},handleRowSpan:function(t,e,a){var i=t,n=0;for(i;i<this.tableData.length;i++){var r=this.calculateName(t,e,this.tableData,a);if(0==t)this.calculateName(i,e,this.tableData,a)==r&&(n+=1);else{if(this.calculateName(i,e,this.tableData,a)==this.calculateName(t-1,e,this.tableData,a)){n=0;break}this.calculateName(i,e,this.tableData,a)==r&&(n+=1)}}return n},calculateName:function(t,e,a,i){for(var n="",r=0;r<=e;r++)n+=a[t][i[r]];return n},jumpMethod:function(t,e,a,i){var n=Object.getOwnPropertyNames(i),r={};this.tableColArr.forEach((function(t){n.forEach((function(e){if(t.field==e){var a=e.indexOf("_"),n=e.substring(a+1);r[n]=i[e]}}))})),r=JSON.stringify(r),this.Base.openTabMenu({id:t,name:e,url:"".concat(a,"?rowData=").concat(r)})},objectSpanMethod:function(t){t.row;var e=t.column,a=t.rowIndex,i=t.columnIndex,n=[];this.tableColArr.map((function(t){n.push(t.field)}));var r=this.handleRowSpan(a,i,n),l=r>0?1:0;if("201"==e.width)return{rowspan:r,colspan:l}}}}),_=x,k=a(1001),D=(0,k.Z)(_,i,n,!1,null,"20b2a81d",null),S=D.exports}}]);
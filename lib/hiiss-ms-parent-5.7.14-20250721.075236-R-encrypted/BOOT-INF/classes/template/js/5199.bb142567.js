"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[5199],{95199:function(t,e,a){a.r(e),a.d(e,{default:function(){return G}});var i=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("div",{staticStyle:{height:"100%",padding:"10px","background-color":"#ffffff"}},[i("ta-border-layout",{attrs:{"layout-type":"flexBorder"}},[i("div",{staticStyle:{width:"100%",height:"120px","border-bottom":"1px solid #DCE0E6","padding-top":"10px"},attrs:{slot:"header"},slot:"header"},[i("ta-form",{attrs:{layout:"horizontal","form-layout":!0,col:e.col,"auto-form-create":function(e){t.form=e}}},[i("ta-form-item",{attrs:{label:"药品对照码",span:6,"field-decorator-id":"drug_common_code"}},[i("ta-input")],1),i("ta-form-item",{attrs:{label:"药品名称",span:6,"field-decorator-id":"drug_name"}},[i("ta-input",{attrs:{placeholder:"可模糊匹配"}})],1),i("ta-form-item",{attrs:{label:"病种编码",span:6,"field-decorator-id":"dise_code"}},[i("ta-input")],1),i("ta-form-item",{attrs:{label:"病种名称",span:6,"field-decorator-id":"dise_name"}},[i("ta-input",{attrs:{placeholder:"可模糊匹配"}})],1),i("ta-form-item",{attrs:{label:"医保类型",span:6,"field-decorator-id":"aae141List"}},[i("ta-select",{attrs:{options:e.aae141List,"allow-clear":!0,mode:"multiple",placeholder:"可多选"}})],1),i("ta-form-item",{attrs:{label:"是否对照",span:6,"field-decorator-id":"contrastFlag"}},[i("ta-select",{attrs:{options:e.contrastFlagList,"allow-clear":!0}})],1),i("ta-form-item",{attrs:{span:6,"field-decorator-id":"item-btn"}},[i("ta-button",{staticStyle:{"margin-left":"20px"},attrs:{type:"primary"},on:{click:e.fnQuery}},[e._v(" 查询 ")])],1)],1)],1),i("div",{staticStyle:{height:"100%"}},[i("ta-big-table",{ref:"dataTable",attrs:{data:e.tableData,border:!0,size:"mini",resizable:"","highlight-hover-row":"",height:"auto","auto-resize":"",stripe:"","header-align":"center","show-overflow":!0,"cell-style":e.cellStyle,"export-config":{},"import-config":{}},scopedSlots:e._u([{key:"topBar",fn:function(){return[i("ta-big-table-toolbar",[i("div",{attrs:{slot:"buttons"},slot:"buttons"},[i("ta-button",{on:{click:e.baseDataEvent}},[e._v("基础数据查看")])],1),i("div",{attrs:{slot:"tools"},slot:"tools"},[i("ta-button",{on:{click:e.importDataEvent}},[e._v(" 基础数据导入 ")]),i("ta-button",{on:{click:e.importContrastDataEvent}},[e._v(" 对照结果数据导入 ")]),i("ta-button",{on:{click:e.exportDataEvent}},[e._v(" 对照结果数据导出 ")])],1)])]},proxy:!0}])},[i("ta-big-table-column",{attrs:{type:"seq",title:"序号",width:"3%",align:"center"}}),i("ta-big-table-column",{attrs:{field:"drug_common_code",title:"药品对照码",width:"10%"}}),i("ta-big-table-column",{attrs:{field:"drug_name",title:"药品名称",width:"15%"}}),i("ta-big-table-column",{attrs:{field:"dise_code",title:"病种编码",width:"10%"}}),i("ta-big-table-column",{attrs:{field:"dise_name",title:"病种名称",width:"15%"}}),i("ta-big-table-column",{attrs:{field:"aae141s",title:"医保类型",width:"20%",formatter:e.formatterAae141s}}),i("ta-big-table-column",{attrs:{field:"contrastFlag",title:"是否对照",width:"10%",align:"center",formatter:e.formatterContrastFlag}}),i("ta-big-table-column",{attrs:{field:"operate",title:"维护操作",width:"10%",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return i("span",{},[i("ta-table-operate",{attrs:{"operate-menu":e.operateMenu,"row-info":t}})],1)}}])})],1)],1),i("div",{staticStyle:{width:"100%",height:"50px",padding:"10px 0","border-top":"1px solid #DCE0E6"},attrs:{slot:"footer"},slot:"footer"},[i("ta-pagination",{ref:"gridPager",staticStyle:{"text-align":"center"},attrs:{"data-source":e.tableData,params:e.tablePageParams,defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"],url:"individualDemand/contrastDrugDisease/queryResultDataListForPage"},on:{"update:dataSource":function(t){e.tableData=t},"update:data-source":function(t){e.tableData=t}}})],1)]),i("importWin",{attrs:{"import-visible":e.importVisible},on:{onCloseModalImport:e.onCloseModalImport}}),i("edit-win",{attrs:{"edit-drawer-visible":e.editDrawerVisible,"edit-win-data":e.editWinData},on:{onCloseDrawer:e.onCloseDrawer}}),i("base-win",{attrs:{"base-drawer-visible":e.baseDrawerVisible},on:{onCloseBaseDrawer:e.onCloseBaseDrawer}}),i("import-contrast-win",{ref:"importContrast"})],1)},s=[],o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("ta-modal",{attrs:{visible:t.importVisible,title:"基础数据导入","destroy-on-close":!0,"mask-closable":!1,height:"300px",width:"550px","ok-text":"导入"},on:{ok:t.handleOk,cancel:t.onCloseModal}},[a("div",{staticStyle:{"font-weight":"bolder",color:"red","font-size":"16px","margin-bottom":"10px"}},[t._v(" 注：可导入单份；数据为覆盖导入 ")]),a("div",{staticStyle:{height:"40px","line-height":"40px"}},[a("a",{staticStyle:{"margin-right":"20px","text-decoration":"underline","font-size":"16px"},attrs:{href:"#"},on:{click:function(e){return t.downloadDataTemplate(1)}}},[t._v("模板下载")]),a("span",[t._v("药品码表：")]),a("ta-upload",{attrs:{"file-list":t.drugFileList,"before-upload":t.drugBeforeUpload,remove:t.drugHandleRemove}},[a("div",[a("ta-button",[a("ta-icon",{attrs:{type:"upload"}}),t._v(" 选择文件 ")],1)],1)])],1),a("br"),a("br"),a("br"),a("div",{staticStyle:{height:"40px","line-height":"40px"}},[a("a",{staticStyle:{"margin-right":"20px","text-decoration":"underline","font-size":"16px"},attrs:{href:"#"},on:{click:function(e){return t.downloadDataTemplate(2)}}},[t._v("模板下载")]),t._v(" 病种码表： "),a("ta-upload",{attrs:{"file-list":t.diseaseFileList,"before-upload":t.diseaseBeforeUpload,remove:t.diseaseHandleRemove}},[a("div",[a("ta-button",[a("ta-icon",{attrs:{type:"upload"}}),t._v(" 选择文件 ")],1)],1)])],1)])],1)},r=[],l=a(89584),n={name:"importWin",props:{importVisible:{type:Boolean}},data:function(){return{drugFileList:[],diseaseFileList:[]}},watch:{importVisible:function(){this.importVisible||(this.drugFileList=[],this.diseaseFileList=[])}},methods:{onCloseModal:function(){this.$emit("onCloseModalImport")},drugHandleRemove:function(t){var e=this.drugFileList.indexOf(t),a=this.drugFileList.slice();a.splice(e,1),this.drugFileList=a},drugBeforeUpload:function(t){return this.drugFileList=[],this.drugFileList=[].concat((0,l.Z)(this.drugFileList),[t]),!1},diseaseHandleRemove:function(t){var e=this.diseaseFileList.indexOf(t),a=this.diseaseFileList.slice();a.splice(e,1),this.diseaseFileList=a},diseaseBeforeUpload:function(t){return this.diseaseFileList=[],this.diseaseFileList=[].concat((0,l.Z)(this.diseaseFileList),[t]),!1},handleOk:function(){var t=this;if(this.drugFileList.length<=0&&this.diseaseFileList.length<=0)this.$message.error("导入文件不能为空，请选择！",1);else{var e={url:"individualDemand/contrastDrugDisease/importFileData",data:{drugFile:this.drugFileList,diseaseFile:this.diseaseFileList},isFormData:!0};this.Base.submit(null,e).then((function(e){t.$message.success("导入成功"),t.onCloseModal()})).catch((function(){t.$message.error("导入失败")}))}},downloadDataTemplate:function(t){var e=this,a=1===t?"药品码表模板":2===t?"病种码表模板":"未知模板";this.Base.downloadFile({method:"post",fileName:a+".xlsx",url:"individualDemand/contrastDrugDisease/downloadDataTemplate",options:{templateType:t}}).then((function(t){e.$message.success("下载成功！")})).catch((function(t){e.$message.error("下载失败！")}))}}},d=n,c=a(1001),u=(0,c.Z)(d,o,r,!1,null,"00fe2b85",null),h=u.exports,b=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("div",[i("ta-drawer",{attrs:{title:"药品-病种对照码维护编辑",placement:"right",closable:!0,"destroy-on-close":!0,width:"99%",visible:e.editDrawerVisible},on:{close:e.onDrawerClose}},[i("ta-row",{staticStyle:{height:"100%"},attrs:{gutter:[24,16]}},[i("ta-col",{staticStyle:{height:"100%"},attrs:{span:12}},[i("ta-border-layout",{attrs:{"layout-type":"flexBorder"}},[i("div",{staticStyle:{width:"100%",height:"120px"},attrs:{slot:"header"},slot:"header"},[i("ta-form",{attrs:{layout:"horizontal","form-layout":!0,col:e.col,"auto-form-create":function(e){t.editForm=e}}},[i("ta-form-item",{attrs:{label:"药品对照码",span:12,"field-decorator-id":"drug_common_code",require:{message:"请输入药品对照码!"}}},[i("ta-input")],1),i("ta-form-item",{attrs:{"field-decorator-id":"item-btn"}},[i("ta-button",{staticStyle:{"margin-left":"20px"},attrs:{type:"primary"},on:{click:e.fnQuery}},[e._v(" 查询 ")])],1),i("ta-form-item",{attrs:{label:"药品名称",span:12,disabled:!0,"field-decorator-id":"drug_name"}},[i("ta-input")],1),i("ta-form-item",{attrs:{"field-decorator-id":"item-btn"}},[i("ta-button",{staticStyle:{"margin-left":"20px"},on:{click:e.fnOpenAddPage}},[e._v(" 新增 ")])],1)],1)],1),i("div",{staticStyle:{height:"100%"}},[i("ta-big-table",{ref:"contrastDataTable",attrs:{data:e.contrastTableData,border:!0,size:"mini",resizable:"","highlight-hover-row":"",height:"auto","auto-resize":"",stripe:"","header-align":"center","show-overflow":!0}},[i("ta-big-table-column",{attrs:{type:"seq",title:"序号",width:"60px",align:"center"}}),i("ta-big-table-column",{attrs:{field:"dise_common_code",title:"病种对照码"}}),i("ta-big-table-column",{attrs:{field:"aae141s",title:"医保类型",formatter:e.formatterAae141s}}),i("ta-big-table-column",{attrs:{field:"operate",title:"操作",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return i("span",{},[i("ta-table-operate",{attrs:{"operate-menu":e.diseOperateMenu,"row-info":t}})],1)}}])})],1)],1)])],1),i("ta-col",{staticStyle:{height:"100%","border-left":"1px solid #CCCCCC"},attrs:{span:12}},[i("ta-border-layout",{attrs:{"layout-type":"flexBorder"}},[i("div",{staticStyle:{width:"100%",height:"120px"},attrs:{slot:"header"},slot:"header"},[i("div",{staticStyle:{height:"25px","line-height":"25px","font-size":"18px"}},[e._v("病种对照码详情")]),i("div",{staticStyle:{margin:"20px 0"}},[i("ta-radio-group",{attrs:{value:e.radioAae141},on:{change:e.handleAae141Change}},[i("ta-radio-button",{attrs:{value:"1"}},[e._v("市医保")]),i("ta-radio-button",{attrs:{value:"2"}},[e._v("省医保")]),i("ta-radio-button",{attrs:{value:"3"}},[e._v("异地医保")])],1)],1)]),i("div",{staticStyle:{height:"100%"}},[i("ta-big-table",{ref:"diseaseDataTable",attrs:{data:e.diseaseTableData,border:!0,size:"mini",resizable:"","highlight-hover-row":"",height:"auto","auto-resize":"",stripe:"","header-align":"center","show-overflow":!0}},[i("ta-big-table-column",{attrs:{type:"seq",title:"序号",width:"60px",align:"center"}}),i("ta-big-table-column",{attrs:{field:"dise_common_code",title:"病种对照码"}}),i("ta-big-table-column",{attrs:{field:"dise_code",title:"病种编码"}}),i("ta-big-table-column",{attrs:{field:"dise_name",title:"病种名称"}})],1)],1)])],1)],1)],1),i("add-win",{attrs:{"add-modal-visible":e.addModalVisible,"add-win-data":e.addWinData},on:{onCloseAddModal:e.onCloseAddModal}})],1)},m=[],f=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("ta-modal",{attrs:{visible:t.addModalVisible,title:t.modalTitle,"destroy-on-close":!0,"mask-closable":!1,height:t.modalHeight,width:"95%","ok-text":"保存"},on:{ok:t.handleOk,cancel:t.onCloseModal}},[a("div",{staticStyle:{color:"red",height:"20px",position:"relative",top:"-15px"}},[t._v(' 若医保类型对应的病种信息为空，请前往"基础数据查看"按钮功能中，补充病种信息 ')]),a("ta-row",{staticStyle:{height:"99%"},attrs:{gutter:[24,16]}},[a("ta-col",{staticStyle:{height:"100%"},attrs:{span:6}},[a("div",{staticClass:"div_table_title"},[a("span",[t._v("病种对照码")])]),a("div",{staticStyle:{height:"95%"}},[a("ta-big-table",{ref:"contrastCodeDataTable",attrs:{data:t.contrastCodeTableData,border:!0,size:"mini",resizable:"","highlight-hover-row":"",height:"auto","auto-resize":"",stripe:"","header-align":"center","show-overflow":!0},on:{"checkbox-change":t.checkbox0ChangeEvent,"checkbox-all":t.checkboxAllEvent,"cell-click":t.cellClickEvent}},[a("ta-big-table-column",{attrs:{type:"checkbox",width:"50px",align:"center"}}),a("ta-big-table-column",{attrs:{type:"seq",title:"序号",width:"60px",align:"center"}}),a("ta-big-table-column",{attrs:{field:"dise_common_code",title:"病种对照码",filters:[{data:""}],"filter-method":t.diseCommonCodeFilterMethod},scopedSlots:t._u([{key:"filter",fn:function(e){var i=e.$panel,s=e.column;return[t._l(s.filters,(function(e,s){return[a("ta-input",{key:s,staticStyle:{margin:"10px",width:"180px"},attrs:{placeholder:"按回车确认筛选"},on:{change:function(t){return i.changeOption(t,!!e.data,e)},keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:i.confirmFilter()}},model:{value:e.data,callback:function(a){t.$set(e,"data",a)},expression:"option.data"}})]}))]}}])})],1)],1)]),a("ta-col",{staticStyle:{height:"100%","border-left":"1px solid #CCCCCC"},attrs:{span:18}},[a("ta-border-layout",{attrs:{"layout-type":"flexBorder"}},[a("div",{staticStyle:{width:"385px",height:"100%"},attrs:{slot:"left"},slot:"left"},[a("div",{staticClass:"div_table_title"},[a("span",[t._v("市医保")])]),a("div",{staticStyle:{height:"94%"}},[a("ta-big-table",{ref:"diseaseBase1DataTable",attrs:{data:t.diseaseBase1TableData,border:!0,size:"mini",resizable:"","highlight-hover-row":"",height:"auto","auto-resize":"",stripe:"","header-align":"center","show-overflow":!0,"row-style":t.rowStyle},on:{"checkbox-change":t.checkbox1ChangeEvent}},[a("ta-big-table-column",{attrs:{type:"checkbox",width:"50px",align:"center"}}),a("ta-big-table-column",{attrs:{field:"dise_code",title:"病种编码"}}),a("ta-big-table-column",{attrs:{field:"dise_name",title:"病种名称"}})],1)],1)]),a("div",{staticStyle:{height:"100%"}},[a("div",{staticClass:"div_table_title"},[a("span",[t._v("省医保")])]),a("div",{staticStyle:{height:"94%"}},[a("ta-big-table",{ref:"diseaseBase2DataTable",attrs:{data:t.diseaseBase2TableData,border:!0,size:"mini",resizable:"","highlight-hover-row":"",height:"auto","auto-resize":"",stripe:"","header-align":"center","show-overflow":!0,"row-style":t.rowStyle},on:{"checkbox-change":t.checkbox2ChangeEvent}},[a("ta-big-table-column",{attrs:{type:"checkbox",width:"50px",align:"center"}}),a("ta-big-table-column",{attrs:{field:"dise_code",title:"病种编码"}}),a("ta-big-table-column",{attrs:{field:"dise_name",title:"病种名称"}})],1)],1)]),a("div",{staticStyle:{width:"385px",height:"100%"},attrs:{slot:"right"},slot:"right"},[a("div",{staticClass:"div_table_title"},[a("span",[t._v("异地医保")])]),a("div",{staticStyle:{height:"94%"}},[a("ta-big-table",{ref:"diseaseBase3DataTable",attrs:{data:t.diseaseBase3TableData,border:!0,size:"mini",resizable:"","highlight-hover-row":"",height:"auto","auto-resize":"",stripe:"","header-align":"center","show-overflow":!0,"row-style":t.rowStyle},on:{"checkbox-change":t.checkbox3ChangeEvent}},[a("ta-big-table-column",{attrs:{type:"checkbox",width:"50px",align:"center"}}),a("ta-big-table-column",{attrs:{field:"dise_code",title:"病种编码"}}),a("ta-big-table-column",{attrs:{field:"dise_name",title:"病种名称"}})],1)],1)])])],1)],1)],1)],1)},g=[],p={name:"addWin",props:{addModalVisible:{type:Boolean},addWinData:{type:Object}},data:function(){return{col:{xs:1,sm:2,md:3,lg:4,xl:6,xxl:12},modalTitle:"病种对照码新增",contrastCodeTableData:[],diseaseBase1TableData:[],diseaseBase2TableData:[],diseaseBase3TableData:[],diseaseTableData:[],queryResultData:{},modalHeight:"600"}},watch:{addModalVisible:function(){var t=this;this.addModalVisible?this.$nextTick((function(){t.setModalTitle(),t.fnQuery()})):(this.contrastCodeTableData=[],this.diseaseBase1TableData=[],this.diseaseBase2TableData=[],this.diseaseBase3TableData=[],this.diseaseTableData=[],this.queryResultData={})}},methods:{setModalTitle:function(){var t=window.innerHeight-180;this.modalHeight=t<450?this.modalHeight+"px":t+"px";var e=this.addWinData.winType;this.modalTitle="add"===e?"病种对照码新增":"edit"===e?"病种对照码编辑":""},onCloseModal:function(){this.$emit("onCloseAddModal")},handleOk:function(){var t=this,e=this.$refs.contrastCodeDataTable.getCheckboxRecords();if(e<=0)this.$message.warn("请选择需要添加的病种对照码");else{var a=this.$refs.diseaseBase1DataTable.getCheckboxRecords(),i=this.$refs.diseaseBase2DataTable.getCheckboxRecords(),s=this.$refs.diseaseBase3DataTable.getCheckboxRecords();if(a<=0&&i<=0&&s<=0)this.$message.warn("请选择需要添加的病种");else{var o=[];o.push.apply(o,a),o.push.apply(o,i),o.push.apply(o,s),o.forEach((function(t){t.oper_name=top.indexTool.getUserInfo().userName}));var r={url:"individualDemand/contrastDrugDisease/saveContrastDataInfo",data:{drug_common_code:this.addWinData.drug_common_code,diseaseBasePoList:o,winType:this.addWinData.winType},autoQs:!1};this.Base.submit(null,r).then((function(e){t.$message.success("保存成功"),t.$parent.fnQuery(),t.onCloseModal()}))}}},fnQuery:function(){var t=this,e={url:"individualDemand/contrastDrugDisease/queryDiseaseBaseDataList",data:this.addWinData,autoQs:!1};this.Base.submit(null,e).then((function(e){var a=e.data.dataVo;a.dise1VoList.forEach((function(t){t.colorFlag="0"})),a.dise2VoList.forEach((function(t){t.colorFlag="0"})),a.dise3VoList.forEach((function(t){t.colorFlag="0"})),t.queryResultData=a,t.fnFillDataInfo()}))},fnFillDataInfo:function(){var t=this.queryResultData;this.contrastCodeTableData=t.diseContrastVoList,this.setContrastCodeTableInfo()},setContrastCodeTableInfo:function(){var t=this,e=[];this.contrastCodeTableData.forEach((function(t){"1"===t.contrastFlag&&e.push(t)})),this.$refs.contrastCodeDataTable.setCheckboxRow(e,!0),e.forEach((function(e){t.setAae141Data(!0,e)})),this.initAae141TableCheckbox()},checkbox0ChangeEvent:function(t){var e=t.checked,a=(t.records,t.row),i=t.rowIndex;this.contrastCodeTableData[i].contrastFlag=e?"1":"0",this.setAae141Data(e,a)},setAae141Data:function(t,e){var a=this,i=this.queryResultData.dise1VoList,s=this.queryResultData.dise2VoList,o=this.queryResultData.dise3VoList,r=e.dise_common_code;if(t)i.forEach((function(t){r===t.dise_common_code&&a.diseaseBase1TableData.unshift(t)})),s.forEach((function(t){r===t.dise_common_code&&a.diseaseBase2TableData.unshift(t)})),o.forEach((function(t){r===t.dise_common_code&&a.diseaseBase3TableData.unshift(t)}));else{var l=this.diseaseBase1TableData,n=this.diseaseBase2TableData,d=this.diseaseBase3TableData,c=[],u=[],h=[];l.forEach((function(t){r!==t.dise_common_code&&c.push(t)})),this.diseaseBase1TableData=c,n.forEach((function(t){r!==t.dise_common_code&&u.push(t)})),this.diseaseBase2TableData=u,d.forEach((function(t){r!==t.dise_common_code&&h.push(t)})),this.diseaseBase3TableData=h}},rowStyle:function(t){var e=t.row;if("1"===e.colorFlag)return{backgroundColor:"#D3EAF4"}},cellClickEvent:function(t){var e=t.row,a=e.dise_common_code;this.diseaseBase1TableData.forEach((function(t){t.colorFlag="0",a===t.dise_common_code&&(t.colorFlag="1")})),this.diseaseBase2TableData.forEach((function(t){t.colorFlag="0",a===t.dise_common_code&&(t.colorFlag="1")})),this.diseaseBase3TableData.forEach((function(t){t.colorFlag="0",a===t.dise_common_code&&(t.colorFlag="1")}))},checkbox1ChangeEvent:function(t){var e=t.checked,a=(t.records,t.row,t.rowIndex);this.diseaseBase1TableData[a].checkedFlag=e?"1":"0"},checkbox2ChangeEvent:function(t){var e=t.checked,a=(t.records,t.row,t.rowIndex);this.diseaseBase2TableData[a].checkedFlag=e?"1":"0"},checkbox3ChangeEvent:function(t){var e=t.checked,a=(t.records,t.row,t.rowIndex);this.diseaseBase3TableData[a].checkedFlag=e?"1":"0"},initAae141TableCheckbox:function(){var t=this.addWinData.winType;if("edit"===t){var e=[];this.diseaseBase1TableData.forEach((function(t){"1"===t.checkedFlag&&e.push(t)})),this.$refs.diseaseBase1DataTable.setCheckboxRow(e,!0);var a=[];this.diseaseBase2TableData.forEach((function(t){"1"===t.checkedFlag&&a.push(t)})),this.$refs.diseaseBase2DataTable.setCheckboxRow(a,!0);var i=[];this.diseaseBase3TableData.forEach((function(t){"1"===t.checkedFlag&&i.push(t)})),this.$refs.diseaseBase3DataTable.setCheckboxRow(i,!0)}},checkboxAllEvent:function(t){var e=this,a=t.checked;this.contrastCodeTableData.forEach((function(t){e.setAae141Data(a,t)}))},diseCommonCodeFilterMethod:function(t){t.value,t.option;var e=t.row,a=t.column,i=[],s=!1;a.filters.forEach((function(t){t.checked&&i.push(t.data)}));for(var o=0;o<i.length;o++)if(e.dise_common_code.indexOf(i[o])>-1){s=!0;break}return s}}},D=p,v=(0,c.Z)(D,f,g,!1,null,"1851b181",null),_=v.exports,y={name:"editWin",components:{addWin:_},props:{editDrawerVisible:{type:Boolean},editWinData:{type:Object}},data:function(){var t=this;return{col:{xs:1,sm:2,md:3,lg:4,xl:6,xxl:12},contrastTableData:[],diseaseTableData:[],diseOperateMenu:[{name:"编辑",icon:"edit",onClick:function(e,a){t.fnOpenAddPage(e)}},{name:"删除",icon:"delete",type:"confirm",confirmTitle:"确认删除该信息？",onOk:function(e){t.fnDeleteData(e)}}],radioAae141:"1",queryResultData:[],addModalVisible:!1,addWinData:{}}},watch:{editDrawerVisible:function(){var t=this;this.editDrawerVisible?this.$nextTick((function(){t.editForm.setFieldsValue(t.editWinData),t.fnQuery()})):(this.contrastTableData=[],this.diseaseTableData=[],this.queryResultData=[],this.radioAae141="1")}},methods:{onDrawerClose:function(){this.$emit("onCloseDrawer")},fnQuery:function(){var t=this;this.editForm.validateFields((function(e,a){if(!e){var i=t.editForm.getFieldsValue();i.drug_name="";var s={url:"individualDemand/contrastDrugDisease/queryDiseaseContrastDataList",data:i,autoQs:!1};t.Base.submit(null,s).then((function(e){t.queryResultData=e.data.dataVo,t.fnFillDataInfo()}))}}))},fnFillDataInfo:function(){var t=this.queryResultData;null!==t.drug_common_code&&void 0!==t.drug_common_code||this.$message.error("查询的药品对照码不存在，请重新输入"),this.editForm.setFieldsValue(t),this.contrastTableData=t.diseContrastVoList,this.setDiseaseTableData()},formatterAae141s:function(t){var e=this,a=t.cellValue,i=a.split(",");if(i.length<=0)return a;var s=[];return i.forEach((function(t){e.$parent.aae141List.forEach((function(e){t===e.value&&s.push(e.label)}))})),s.join(" | ")},setDiseaseTableData:function(){var t=this.radioAae141;"1"===t&&(this.diseaseTableData=this.queryResultData.dise1VoList),"2"===t&&(this.diseaseTableData=this.queryResultData.dise2VoList),"3"===t&&(this.diseaseTableData=this.queryResultData.dise3VoList)},fnOpenAddPage:function(t){var e=this.editForm.getFieldsValue();this.addWinData.drug_common_code=e.drug_common_code,this.addWinData.drug_name=e.drug_name,null!==t.dise_common_code&&void 0!==t.dise_common_code?(this.addWinData.dise_common_code=t.dise_common_code,this.addWinData.aae141s=t.aae141s,this.addWinData.winType="edit"):(this.addWinData.dise_common_code=null,this.addWinData.winType="add"),this.addModalVisible=!0},onCloseAddModal:function(){this.addModalVisible=!1,this.addWinData={}},handleAae141Change:function(t){this.radioAae141=t.target.value,this.setDiseaseTableData()},fnDeleteData:function(t){var e=this;this.editForm.validateFields((function(a,i){if(!a){var s=e.editForm.getFieldsValue();s.dise_common_code=t.dise_common_code;var o={url:"individualDemand/contrastDrugDisease/deleteDiseaseContrastInfo",data:s,autoQs:!1};e.Base.submit(null,o).then((function(t){e.$message.success("删除成功"),e.fnQuery()}))}}))}}},w=y,x=(0,c.Z)(w,b,m,!1,null,"012e0a9c",null),F=x.exports,E=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("div",[i("ta-drawer",{attrs:{title:"基础数据查看",placement:"right",closable:!0,"destroy-on-close":!0,width:"99%",visible:e.baseDrawerVisible},on:{close:e.onDrawerClose}},[i("ta-row",{staticStyle:{height:"100%"},attrs:{gutter:[24,16]}},[i("ta-col",{staticStyle:{height:"100%"},attrs:{span:12}},[i("ta-border-layout",{attrs:{"layout-type":"flexBorder"}},[i("div",{staticStyle:{width:"100%",height:"120px"},attrs:{slot:"header"},slot:"header"},[i("ta-form",{attrs:{layout:"horizontal","form-layout":!0,col:e.col,"auto-form-create":function(e){t.baseDrugForm=e}}},[i("ta-form-item",{attrs:{label:"药品对照码",span:12,"field-decorator-id":"drug_common_code"}},[i("ta-input")],1),i("ta-form-item",{attrs:{label:"药品编码",span:12,"field-decorator-id":"drug_code"}},[i("ta-input")],1),i("ta-form-item",{attrs:{label:"药品名称",span:12,"field-decorator-id":"drug_name"}},[i("ta-input",{attrs:{placeholder:"可模糊匹配"}})],1),i("ta-form-item",{attrs:{span:12,"field-decorator-id":"item-btn"}},[i("ta-button",{staticStyle:{"margin-left":"20px"},attrs:{type:"primary"},on:{click:e.fnDrugQuery}},[e._v(" 查询 ")])],1)],1)],1),i("div",{staticStyle:{height:"100%"}},[i("ta-big-table",{ref:"baseDrugTable",attrs:{data:e.baseDrugData,border:!0,size:"mini",resizable:"","highlight-hover-row":"",height:"auto","auto-resize":"",stripe:"","header-align":"center","show-overflow":!0},scopedSlots:e._u([{key:"topBar",fn:function(){return[i("ta-big-table-toolbar",[i("div",{attrs:{slot:"buttons"},slot:"buttons"},[i("ta-button",{on:{click:e.baseDrugAddDataEvent}},[e._v("新增")]),i("ta-popconfirm",{attrs:{title:"确定删除选中行的信息吗？"},on:{confirm:e.baseDrugDeleteConfirm}},[i("ta-button",[e._v("删除")])],1)],1)])]},proxy:!0}])},[i("ta-big-table-column",{attrs:{type:"checkbox",width:"40px",align:"center"}}),i("ta-big-table-column",{attrs:{type:"seq",title:"序号",width:"60px",align:"center"}}),i("ta-big-table-column",{attrs:{field:"drug_common_code",title:"药品对照码"}}),i("ta-big-table-column",{attrs:{field:"drug_code",title:"药品编码"}}),i("ta-big-table-column",{attrs:{field:"drug_name",title:"药品名称"}}),i("ta-big-table-column",{attrs:{field:"operate",title:"操作",align:"center",width:"12%"},scopedSlots:e._u([{key:"default",fn:function(t){return i("span",{},[i("ta-table-operate",{attrs:{"operate-menu":e.baseDrugOperateMenu,"row-info":t}})],1)}}])}),i("template",{slot:"bottomBar"},[i("ta-pagination",{ref:"baseDrugPager",staticStyle:{"text-align":"center"},attrs:{"data-source":e.baseDrugData,params:e.baseDrugPageParams,"page-size-options":["50","100","200","300","400","500"],"default-page-size":100,url:"individualDemand/contrastDrugDisease/queryDrugBaseDataForPage"},on:{"update:dataSource":function(t){e.baseDrugData=t},"update:data-source":function(t){e.baseDrugData=t}}})],1)],2)],1)])],1),i("ta-col",{staticStyle:{height:"100%","border-left":"2px solid #999999"},attrs:{span:12}},[i("ta-border-layout",{attrs:{"layout-type":"flexBorder"}},[i("div",{staticStyle:{width:"100%",height:"120px"},attrs:{slot:"header"},slot:"header"},[i("ta-form",{attrs:{layout:"horizontal","form-layout":!0,col:e.col,"auto-form-create":function(e){t.baseDiseaseForm=e}}},[i("ta-form-item",{attrs:{label:"病种对照码",span:12,"field-decorator-id":"dise_common_code"}},[i("ta-input")],1),i("ta-form-item",{attrs:{label:"病种编码",span:12,"field-decorator-id":"dise_code"}},[i("ta-input")],1),i("ta-form-item",{attrs:{label:"病种名称",span:12,"field-decorator-id":"dise_name"}},[i("ta-input",{attrs:{placeholder:"可模糊匹配"}})],1),i("ta-form-item",{attrs:{label:"医保类型",span:8,"field-decorator-id":"aae141List"}},[i("ta-select",{attrs:{options:e.$parent.aae141List,"allow-clear":!0,mode:"multiple",placeholder:"可多选"}})],1),i("ta-form-item",{attrs:{span:4,"field-decorator-id":"item-btn"}},[i("ta-button",{staticStyle:{"margin-left":"20px"},attrs:{type:"primary"},on:{click:e.fnDiseaseQuery}},[e._v(" 查询 ")])],1)],1)],1),i("div",{staticStyle:{height:"100%"}},[i("ta-big-table",{ref:"baseDiseaseTable",attrs:{data:e.baseDiseaseData,border:!0,size:"mini",resizable:"","highlight-hover-row":"",height:"auto","auto-resize":"",stripe:"","header-align":"center","show-overflow":!0},scopedSlots:e._u([{key:"topBar",fn:function(){return[i("ta-big-table-toolbar",[i("div",{attrs:{slot:"buttons"},slot:"buttons"},[i("ta-button",{on:{click:e.baseDiseaseAddDataEvent}},[e._v("新增")]),i("ta-popconfirm",{attrs:{title:"确定删除选中行的信息吗？"},on:{confirm:e.baseDiseaseDeleteConfirm}},[i("ta-button",[e._v("删除")])],1)],1)])]},proxy:!0}])},[i("ta-big-table-column",{attrs:{type:"checkbox",width:"40px",align:"center"}}),i("ta-big-table-column",{attrs:{type:"seq",title:"序号",width:"60px",align:"center"}}),i("ta-big-table-column",{attrs:{field:"dise_common_code",title:"病种对照码"}}),i("ta-big-table-column",{attrs:{field:"dise_code",title:"病种编码"}}),i("ta-big-table-column",{attrs:{field:"dise_name",title:"病种名称"}}),i("ta-big-table-column",{attrs:{field:"aae141",title:"医保类型",formatter:e.formatterAae141}}),i("ta-big-table-column",{attrs:{field:"operate",title:"操作",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return i("span",{},[i("ta-table-operate",{attrs:{"operate-menu":e.baseDiseaseOperateMenu,"row-info":t}})],1)}}])}),i("template",{slot:"bottomBar"},[i("ta-pagination",{ref:"baseDiseasePager",staticStyle:{"text-align":"center"},attrs:{"data-source":e.baseDiseaseData,params:e.baseDiseasePageParams,"page-size-options":["50","100","200","300","400","500"],"default-page-size":100,url:"individualDemand/contrastDrugDisease/queryDiseaseBaseDataForPage"},on:{"update:dataSource":function(t){e.baseDiseaseData=t},"update:data-source":function(t){e.baseDiseaseData=t}}})],1)],2)],1)])],1)],1)],1),i("base-edit-win",{attrs:{"base-edit-modal-visible":e.baseEditModalVisible,"base-edit-data":e.baseEditData},on:{onCloseBaseEditModal:e.onCloseBaseEditModal}})],1)},C=[],k=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("div",[i("ta-modal",{attrs:{visible:e.baseEditModalVisible,title:e.baseEditData.modalTitle,"destroy-on-close":!0,"mask-closable":!1,height:"300px",width:"500px","ok-text":"保存"},on:{ok:e.handleOk,cancel:e.onCloseModal}},["drug"===e.baseEditData.baseFlag?i("div",[i("ta-form",{attrs:{"auto-form-create":function(e){t.baseDrugEditForm=e}}},[i("ta-form-item",{attrs:{label:"药品对照码","field-decorator-id":"drug_common_code","init-value":e.baseEditData.drug_common_code,require:{message:"请输入药品对照码!"}}},[i("ta-input")],1),i("ta-form-item",{attrs:{label:"药品编码","field-decorator-id":"drug_code","init-value":e.baseEditData.drug_code,require:{message:"请输入药品编码!"}}},[i("ta-input")],1),i("ta-form-item",{attrs:{label:"药品名称","field-decorator-id":"drug_name","init-value":e.baseEditData.drug_name,require:{message:"请输入药品名称!"}}},[i("ta-input")],1)],1)],1):e._e(),"disease"===e.baseEditData.baseFlag?i("div",[i("ta-form",{attrs:{"auto-form-create":function(e){t.baseDiseaseEditForm=e}}},[i("ta-form-item",{attrs:{label:"病种对照码","field-decorator-id":"dise_common_code","init-value":e.baseEditData.dise_common_code,require:{message:"请输入病种对照码!"}}},[i("ta-input")],1),i("ta-form-item",{attrs:{label:"病种编码","field-decorator-id":"dise_code","init-value":e.baseEditData.dise_code,require:{message:"请输入病种编码!"}}},[i("ta-input")],1),i("ta-form-item",{attrs:{label:"病种名称","field-decorator-id":"dise_name","init-value":e.baseEditData.dise_name,require:{message:"请输入病种名称!"}}},[i("ta-input")],1),i("ta-form-item",{attrs:{label:"医保类型","field-decorator-id":"aae141","init-value":e.baseEditData.aae141,require:{message:"请选择医保类型!"}}},[i("ta-select",{attrs:{options:e.$parent.$parent.aae141List}})],1)],1)],1):e._e()])],1)},T=[],B={name:"baseEditWin",props:{baseEditModalVisible:{type:Boolean},baseEditData:{type:Object}},data:function(){return{}},watch:{},methods:{onCloseModal:function(){this.$emit("onCloseBaseEditModal")},handleOk:function(){var t=this,e=this.baseEditData.baseFlag;"drug"===e&&this.baseDrugEditForm.validateFields((function(e,a){if(!e){var i=t.baseDrugEditForm.getFieldsValue();i.id=t.baseEditData.id;var s={url:"individualDemand/contrastDrugDisease/saveDrugBaseDataInfo",data:i,autoQs:!1};t.Base.submit(null,s).then((function(e){t.$message.success("保存药品对照码基础数据成功"),t.$parent.fnDrugQuery(),t.onCloseModal()}))}})),"disease"===e&&this.baseDiseaseEditForm.validateFields((function(e,a){if(!e){var i=t.baseDiseaseEditForm.getFieldsValue();i.id=t.baseEditData.id;var s={url:"individualDemand/contrastDrugDisease/saveDiseaseBaseDataInfo",data:i,autoQs:!1};t.Base.submit(null,s).then((function(e){t.$message.success("保存病种对照码基础数据成功"),t.$parent.fnDiseaseQuery(),t.onCloseModal()}))}}))}}},V=B,S=(0,c.Z)(V,k,T,!1,null,"bac5c67e",null),$=S.exports,L={name:"baseWin",components:{baseEditWin:$},props:{baseDrawerVisible:{type:Boolean}},data:function(){var t=this;return{col:{xs:1,sm:2,md:3,lg:4,xl:6,xxl:12},baseDrugData:[],baseDiseaseData:[],baseDrugOperateMenu:[{name:"编辑",icon:"edit",onClick:function(e,a){t.fnOpenBaseDrugEditPage(e)}}],baseDiseaseOperateMenu:[{name:"编辑",icon:"edit",onClick:function(e,a){t.fnOpenBaseDiseaseEditPage(e)}}],baseEditModalVisible:!1,baseEditData:{}}},watch:{baseDrawerVisible:function(){var t=this;this.baseDrawerVisible&&this.$nextTick((function(){t.fnDrugQuery(),t.fnDiseaseQuery()}))}},methods:{onDrawerClose:function(){this.$emit("onCloseBaseDrawer")},baseDrugPageParams:function(){return this.baseDrugForm.getFieldsValue()},fnDrugQuery:function(){var t=this;this.baseDrugForm.validateFields((function(e,a){e||t.$refs.baseDrugPager.loadData()}))},baseDiseasePageParams:function(){return this.baseDiseaseForm.getFieldsValue()},fnDiseaseQuery:function(){var t=this;this.baseDiseaseForm.validateFields((function(e,a){e||t.$refs.baseDiseasePager.loadData()}))},formatterAae141:function(t){var e=t.cellValue,a=e;return this.$parent.aae141List.forEach((function(t,i){e===t.value&&(a=t.label)})),a},onCloseBaseEditModal:function(){this.baseEditModalVisible=!1,this.baseEditData={}},baseDrugAddDataEvent:function(){this.baseEditData.modalTitle="药品对照码基础数据新增",this.baseEditData.baseFlag="drug",this.baseEditData.id=null,this.baseEditModalVisible=!0},fnOpenBaseDrugEditPage:function(t){this.baseEditData.modalTitle="药品对照码基础数据编辑",this.baseEditData.baseFlag="drug",this.baseEditData.id=t.id,this.baseEditData.drug_common_code=t.drug_common_code,this.baseEditData.drug_code=t.drug_code,this.baseEditData.drug_name=t.drug_name,this.baseEditModalVisible=!0},baseDrugDeleteConfirm:function(){var t=this,e=this.$refs.baseDrugTable.getCheckboxRecords();if(e<=0)this.$message.warn("请选择需要删除的药品对照码基础数据");else{var a={url:"individualDemand/contrastDrugDisease/deleteDrugBaseDataByIds",data:{drugPoList:e},autoQs:!1};this.Base.submit(null,a).then((function(e){t.$message.success("删除药品对照码基础数据成功"),t.fnDrugQuery()}))}},baseDiseaseAddDataEvent:function(){this.baseEditData.modalTitle="病种对照码基础数据新增",this.baseEditData.baseFlag="disease",this.baseEditData.id=null,this.baseEditModalVisible=!0},fnOpenBaseDiseaseEditPage:function(t){this.baseEditData.modalTitle="病种对照码基础数据编辑",this.baseEditData.baseFlag="disease",this.baseEditData.id=t.id,this.baseEditData.dise_common_code=t.dise_common_code,this.baseEditData.dise_code=t.dise_code,this.baseEditData.dise_name=t.dise_name,this.baseEditData.aae141=t.aae141,this.baseEditModalVisible=!0},baseDiseaseDeleteConfirm:function(){var t=this,e=this.$refs.baseDiseaseTable.getCheckboxRecords();if(e<=0)this.$message.warn("请选择需要删除的病种对照码基础数据");else{var a={url:"individualDemand/contrastDrugDisease/deleteDiseaseBaseDataByIds",data:{diseasePoList:e},autoQs:!1};this.Base.submit(null,a).then((function(e){t.$message.success("删除病种对照码基础数据成功"),t.fnDiseaseQuery()}))}}}},M=L,z=(0,c.Z)(M,E,C,!1,null,"03973729",null),W=z.exports,O=a(36797),P=a.n(O),q=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("ta-modal",{attrs:{visible:t.importVisible,title:"对照结果数据导入","destroy-on-close":!0,"mask-closable":!1,height:"300px",width:"550px","ok-text":"导入"},on:{ok:t.handleOk,cancel:t.onCloseModal}},[a("div",{staticStyle:{"font-weight":"bolder",color:"red","font-size":"16px","margin-bottom":"10px"}},[t._v(" 注：数据为增量导入，与数据表的重复数据不会导入 ")]),a("div",{staticStyle:{height:"40px","line-height":"40px"}},[a("a",{staticStyle:{"margin-right":"20px","text-decoration":"underline","font-size":"16px"},attrs:{href:"#"},on:{click:function(e){return t.downloadDataTemplate(3)}}},[t._v("模板下载")]),a("span",[t._v("对照结果数据：")]),a("ta-upload",{attrs:{"file-list":t.contrastFileList,"before-upload":t.contrastBeforeUpload,remove:t.contrastHandleRemove}},[a("div",[a("ta-button",[a("ta-icon",{attrs:{type:"upload"}}),t._v(" 选择文件 ")],1)],1)])],1)])],1)},A=[],R={name:"importContrastWin",props:{},data:function(){return{importVisible:!1,contrastFileList:[]}},methods:{onCloseModal:function(){this.contrastFileList=[],this.importVisible=!1},contrastHandleRemove:function(t){var e=this.contrastFileList.indexOf(t),a=this.contrastFileList.slice();a.splice(e,1),this.contrastFileList=a},contrastBeforeUpload:function(t){return this.contrastFileList=[],this.contrastFileList=[].concat((0,l.Z)(this.contrastFileList),[t]),!1},handleOk:function(){var t=this;if(this.contrastFileList.length<=0)this.$message.error("导入文件不能为空，请选择！",1);else{var e={url:"individualDemand/contrastDrugDisease/importFileData",data:{contrastFile:this.contrastFileList},isFormData:!0};this.Base.submit(null,e).then((function(e){var a=e.data.dataVo;t.$message.info(a.message),t.onCloseModal()})).catch((function(){t.$message.error("导入失败")}))}},downloadDataTemplate:function(t){var e=this,a=1===t?"药品码表模板":2===t?"病种码表模板":3===t?"对照码结果数据表模板":"未知模板";this.Base.downloadFile({method:"post",fileName:a+".xlsx",url:"individualDemand/contrastDrugDisease/downloadDataTemplate",options:{templateType:t}}).then((function(t){e.$message.success("下载成功！")})).catch((function(t){e.$message.error("下载失败！")}))}}},Q=R,I=(0,c.Z)(Q,q,A,!1,null,"4d9dcecf",null),H=I.exports,Z=[{value:"1",label:"市医保"},{value:"2",label:"省医保"},{value:"3",label:"异地医保"}],U=[{value:"0",label:"否"},{value:"1",label:"是"}],j={name:"contrastDrugDisease",components:{importWin:h,editWin:F,baseWin:W,importContrastWin:H},data:function(){var t=this;return{col:{xs:1,sm:2,md:3,lg:4,xl:6,xxl:12},tableData:[],aae141List:Z,contrastFlagList:U,operateMenu:[{name:"维护",onClick:function(e,a){t.fnOpenMaintainPage(e)}}],importVisible:!1,editDrawerVisible:!1,editWinData:{},baseDrawerVisible:!1}},methods:{tablePageParams:function(){return this.form.getFieldsValue()},fnQuery:function(){var t=this;this.form.validateFields((function(e,a){e||t.$refs.gridPager.loadData()}))},formatterAae141s:function(t){var e=t.cellValue,a=e.split(",");if(a.length<=0)return e;var i=[];return a.forEach((function(t){Z.forEach((function(e){t===e.value&&i.push(e.label)}))})),i.join(" | ")},formatterContrastFlag:function(t){var e=t.cellValue,a="";return U.forEach((function(t,i){e===t.value&&(a=t.label)})),a},cellStyle:function(t){var e=t.row,a=(t.rowIndex,t.column);t.columnIndex;if("contrastFlag"===a.property&&"0"===e.contrastFlag)return{color:"red"}},onCloseModalImport:function(){this.importVisible=!1},importDataEvent:function(){this.importVisible=!0},exportDataEvent:function(){var t=this;this.form.validateFields((function(e,a){if(!e){var i=P()().format("YYYYMMDDHHmmss"),s=t.form.getFieldsValue();t.Base.downloadFile({method:"post",fileName:i+"_药品-病种对照码结果.xlsx",url:"individualDemand/contrastDrugDisease/exportContrastData",options:s}).then((function(e){t.$message.success("导出成功！")})).catch((function(e){t.$message.error("导出失败！")}))}}))},fnOpenMaintainPage:function(t){this.editWinData.drug_common_code=t.drug_common_code,this.editWinData.drug_name=t.drug_name,this.editDrawerVisible=!0},onCloseDrawer:function(){this.editWinData={},this.editDrawerVisible=!1},baseDataEvent:function(){this.baseDrawerVisible=!0},onCloseBaseDrawer:function(){this.baseDrawerVisible=!1},importContrastDataEvent:function(){this.$refs.importContrast.importVisible=!0}}},N=j,Y=(0,c.Z)(N,i,s,!1,null,"4813083e",null),G=Y.exports}}]);
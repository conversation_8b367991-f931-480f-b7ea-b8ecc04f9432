"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[1125],{88412:function(t,e,a){var i=a(26263),l=a(36766),n=a(1001),r=(0,n.Z)(l.Z,i.s,i.x,!1,null,"5e7ef0ae",null);e["Z"]=r.exports},21125:function(t,e,a){a.r(e),a.d(e,{default:function(){return u}});var i=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("div",{staticClass:"fit"},[i("ta-border-layout",{attrs:{"layout-type":"fixTop"}},[i("div",{attrs:{slot:"header"},slot:"header"},[i("ta-title",{attrs:{title:"查询表格"}}),i("ta-form",{attrs:{formLayout:"","auto-form-create":function(e){return t.queryParamForm=e}}},[i("ta-form-item",{attrs:{"field-decorator-id":"inSvrNme",label:"接口名称",span:6}},[i("ta-input")],1),i("ta-form-item",{attrs:{"field-decorator-id":"inObjCode",label:"接口字段编码",span:6}},[i("ta-input")],1),i("ta-form-item",{attrs:{"field-decorator-id":"dbObjCode",label:"数据库字段编码",span:6}},[i("ta-input")],1),i("ta-button",{staticStyle:{margin:"0 20px 0 60px"},attrs:{type:"primary",icon:"search"},on:{click:e.fnQuery}},[e._v("查询")]),i("ta-button",{staticStyle:{"margin-right":"20px"},attrs:{type:"default",icon:"redo"},on:{click:e.fnReset}},[e._v("重置")])],1)],1),i("div",{staticClass:"fit content-box"},[i("div",{staticClass:"content-title"},[i("ta-title",{attrs:{title:"查询结果"}}),i("ta-row",[i("ta-button",{attrs:{icon:"plus",type:"primary"},on:{click:e.addRoot}},[i("ta-icon",{attrs:{type:"add"}}),e._v(" 新增 ")],1)],1)],1),i("div",{staticClass:"table-content"},[i("ta-big-table",{ref:"xTable",attrs:{"max-height":"600","keep-source":"","row-id":"id",data:e.tableData,"tree-config":{children:"children"},"header-cell-style":e.headerCellStyle},scopedSlots:e._u([{key:"bottomBar",fn:function(){return[i("ta-pagination",{ref:"gridPager",staticStyle:{"text-align":"right","margin-top":"10px"},attrs:{"data-source":e.tableData,params:e.infoPageParams,url:"interfaceToDb/getInterfaceInfo"},on:{"update:dataSource":function(t){e.tableData=t},"update:data-source":function(t){e.tableData=t}}})]},proxy:!0}])},[i("ta-big-table-column",{attrs:{field:"inObjCode",title:"接口字段编码","tree-node":""}}),i("ta-big-table-column",{attrs:{field:"dbObjCode",title:"数据库字段编码",width:"140"}}),i("ta-big-table-column",{attrs:{field:"inObjName",title:"接口字段名称",width:"140"}}),i("ta-big-table-column",{attrs:{field:"inObjContent",title:"接口字段描述",width:"260"}}),i("ta-big-table-column",{attrs:{field:"inObjType",title:"接口字段类型",width:"140"}}),i("ta-big-table-column",{attrs:{field:"inObjNotnull",title:"接口字段是否非空",width:"140",collectionType:"YESORNO"}}),i("ta-big-table-column",{attrs:{field:"inSvrName",title:"接口名称",width:"140"}}),i("ta-big-table-column",{attrs:{field:"inVer",title:"接口版本",width:"140"}}),i("ta-big-table-column",{attrs:{field:"dbObjTableFlag",title:"数据库表名索引",width:"140"}}),i("ta-big-table-column",{attrs:{fixed:"right",field:"operate",title:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){return i("span",{},[i("ta-table-operate",{attrs:{"operate-menu":e.operateMenu,"row-info":t}})],1)}}])})],1)],1)])]),i("ta-modal",{attrs:{title:"edit"===e.editType?"编辑":"addChild"===e.editType?"新增下级":"新增",height:"500px"},model:{value:e.visible,callback:function(t){e.visible=t},expression:"visible"}},[i("ta-form",{attrs:{"auto-form-create":e.createForm}},["addChild"===e.editType?i("ta-form-item",{attrs:{disabled:"",label:"父接口编码","field-decorator-id":"parentCode"}},[i("ta-input")],1):e._e(),i("ta-form-item",{attrs:{label:"接口字段编码","field-decorator-id":"inObjCode"}},[i("ta-input")],1),i("ta-form-item",{attrs:{label:"数据库字段编码","field-decorator-id":"dbObjCode"}},[i("ta-input")],1),i("ta-form-item",{attrs:{label:"接口字段名称","field-decorator-id":"inObjName"}},[i("ta-input")],1),i("ta-form-item",{attrs:{label:"接口字段描述","field-decorator-id":"inObjContent"}},[i("ta-input")],1),i("ta-form-item",{attrs:{label:"接口字段类型","field-decorator-id":"inObjType"}},[i("ta-input")],1),i("ta-form-item",{attrs:{label:"接口字段是否非空","field-decorator-id":"inObjNotnull"}},[i("ta-select",{attrs:{options:[{value:"1",label:"是"},{value:"0",label:"否"}],placeholder:"请选择"}})],1),i("ta-form-item",{attrs:{label:"接口名称","field-decorator-id":"inSvrName"}},[i("ta-input")],1),i("ta-form-item",{attrs:{label:"接口版本","field-decorator-id":"inVer"}},[i("ta-input")],1),i("ta-form-item",{attrs:{label:"数据库表名索引","field-decorator-id":"dbObjTableFlag"}},[i("ta-input")],1)],1),i("template",{slot:"footer"},[i("ta-button",{key:"back",on:{click:e.cancel}},[e._v(" 取消 ")]),"edit"===e.editType?i("ta-button",{key:"submit",attrs:{type:"primary"},on:{click:e.saveEdit}},[e._v(" 保存编辑 ")]):i("ta-button",{key:"submit",attrs:{type:"primary"},on:{click:e.addChild}},[e._v(" 新增 ")])],1)],2)],1)},l=[],n=a(95082),r=a(88412),o={components:{TaTitle:r.Z},data:function(){var t=this;return{editType:"edit",visible:!1,count:0,form:null,clickRowData:null,tableData:[],operateMenu:[{name:"编辑",icon:"edit",onClick:function(e,a){t.editType="edit",t.visible=!0,t.clickRowData=e,t.$nextTick((function(){t.form.setFieldsValue(e)}))}},{name:"删除",icon:"delete",type:"confirm",confirmTitle:"确认删除该信息？",onOk:function(e,a){t.handDelete(e)}},{name:"添加下级",icon:"plus",onClick:function(e,a){t.editType="addChild",t.visible=!0,t.clickRowData=e,t.$nextTick((function(){t.form.resetFields(),t.form.setFieldsValue({parentCode:e.inObjCode})}))}}]}},mounted:function(){this.fnQuery()},methods:{headerCellStyle:function(t){var e=t.column,a=(t.columnIndex,e.property);if(["dbObjCode","inSvrName","dbObjTableFlag"].includes(a))return{color:"#ff3c07"}},fnReset:function(){this.queryParamForm.resetFields(),this.fnQuery()},infoPageParams:function(){var t=this.queryParamForm.getFieldsValue();return t},fnQuery:function(){this.$refs.gridPager.loadData()},createForm:function(t){this.form=t},cancel:function(){this.clickRowData=null,this.visible=!1},handDelete:function(t){var e=this;this.Base.submit(null,{url:"interfaceToDb/deleteInterfaceInfo",data:{id:t.id},autoValid:!0},{successCallback:function(t){e.fnQuery()},failCallback:function(t){e.$message.error("删除失败")}})},addRoot:function(){var t=this;this.editType="addRoot",this.visible=!0,this.$nextTick((function(){t.form.resetFields()}))},addChild:function(){var t=this,e=this.form.getFieldsValue(),a=(0,n.Z)((0,n.Z)({},this.clickRowData),e);delete a.children,this.Base.submit(null,{url:"interfaceToDb/insertInterfaceInfo",data:a,autoValid:!0},{successCallback:function(e){t.fnQuery()},failCallback:function(e){t.$message.error("添加数据失败，请检查数据")}}),this.visible=!1,this.clickRowData=null},saveEdit:function(){var t=this,e=this.form.getFieldsValue(),a=(0,n.Z)((0,n.Z)({},this.clickRowData),e);delete a.children,this.Base.submit(null,{url:"interfaceToDb/updateInterfaceInfo",data:a,autoValid:!0},{successCallback:function(e){t.fnQuery()},failCallback:function(e){t.$message.error("更新失败")}}),this.visible=!1,this.clickRowData=null}}},c=o,s=a(1001),d=(0,s.Z)(c,i,l,!1,null,null,null),u=d.exports},36766:function(t,e,a){var i=a(66586);e["Z"]=i.Z},26263:function(t,e,a){a.d(e,{s:function(){return i},x:function(){return l}});var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},l=[]},66586:function(t,e){var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};e["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}}}]);
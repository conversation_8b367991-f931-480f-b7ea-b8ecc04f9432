"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[3550],{33550:function(t,a,s){s.r(a),s.d(a,{default:function(){return u}});var i=function(){var t=this,a=t.$createElement,s=t._self._c||a;return s("div",[s("div",{staticClass:"header"},[s("div",{staticStyle:{"margin-left":"10px",display:"flex"}},[s("div",{staticStyle:{width:"20px",height:"30px","text-align":"left"}},[s("ta-icon",{staticStyle:{color:"#3382f5",fontSize:"14px","margin-top":"8px",cursor:"pointer"},attrs:{type:"reload",title:"刷新"},on:{click:t.reloadSchedule}})],1)]),s("div",{staticStyle:{position:"absolute",right:"10px",top:"0px","line-height":"30px"},attrs:{title:"退出登录"}},[s("span",{staticStyle:{color:"#333333","font-size":"12px","font-weight":"bold","margin-right":"6px"}},[t._v(t._s(t.username))]),s("div",{staticClass:"logout",on:{click:t.goLogin}},[s("ta-icon",{staticStyle:{"font-size":"10px"},attrs:{type:"logout"}})],1)])]),"1"!==this.personSign&&"4"!==this.personSign||""==this.aaz263?t._e():s("div",{staticClass:"div1"},[s("div",{staticClass:"belowSchedule"},[s("div",{directives:[{name:"show",rawName:"v-show",value:"1"===this.showMenu.dscgAuditHandle,expression:"this.showMenu.dscgAuditHandle==='1'"}]},[t._m(0),s("div",{staticClass:"toneSpacing"},[this.dscgAuditHandle>0?s("span",{staticClass:"circlepoint",on:{click:function(a){return t.auditDisposal("dscgAuditHandle.html#/dscgAuditHandle","1")}}},[t._v("待处理")]):s("span",{on:{click:function(a){return t.auditDisposal("dscgAuditHandle.html#/dscgAuditHandle","1")}}},[t._v("待处理")]),s("span",{staticStyle:{float:"right"},on:{click:function(a){return t.auditDisposal("dscgAuditHandle.html#/dscgAuditHandle","1")}}},[t._v(t._s(this.dscgAuditHandle))])])]),s("div",{directives:[{name:"show",rawName:"v-show",value:"1"===this.showMenu.nightHandle,expression:"this.showMenu.nightHandle==='1'"}]},[t._m(1),s("div",{staticClass:"toneSpacing"},[this.nightHandle>0?s("span",{staticClass:"circlepoint",on:{click:function(a){return t.auditDisposal("approvalHandle.html#/approvalHandle","1")}}},[t._v("待处理")]):s("span",{on:{click:function(a){return t.auditDisposal("approvalHandle.html#/approvalHandle","1")}}},[t._v("待处理")]),s("span",{staticStyle:{float:"right"},on:{click:function(a){return t.auditDisposal("approvalHandle.html#/approvalHandle","1")}}},[t._v(t._s(this.nightHandle))])])]),s("div",{directives:[{name:"show",rawName:"v-show",value:"1"===this.showMenu.inhosHandle,expression:"this.showMenu.inhosHandle==='1'"}]},[t._m(2),s("div",{staticClass:"toneSpacing"},[this.inhosHandle>0?s("span",{staticClass:"circlepoint",on:{click:function(a){return t.auditDisposal("appealForDoc.html#/appealForDoc","1")}}},[t._v("待处理")]):s("span",{on:{click:function(a){return t.auditDisposal("appealForDoc.html#/appealForDoc","1")}}},[t._v("待处理")]),s("span",{staticStyle:{float:"right"},on:{click:function(a){return t.auditDisposal("appealForDoc.html#/appealForDoc","1")}}},[t._v(t._s(this.inhosHandle))])]),s("div",{staticClass:"toneSpacing"},[this.inhosSubmit>0?s("span",{staticClass:"circlepoint",on:{click:function(a){return t.auditDisposal("appealForDoc.html#/appealForDoc","1")}}},[t._v("待提交")]):s("span",{on:{click:function(a){return t.auditDisposal("appealForDoc.html#/appealForDoc","1")}}},[t._v("待提交")]),s("span",{staticStyle:{float:"right"},on:{click:function(a){return t.auditDisposal("appealForDoc.html#/appealForDoc","1")}}},[t._v(t._s(this.inhosSubmit))])])]),s("div",{directives:[{name:"show",rawName:"v-show",value:"1"===this.showMenu.diagnosticMatching||"1"===this.showMenu.issueInventory||"1"===this.showMenu.indexWarning||"1"===this.showMenu.indexStatistics,expression:"this.showMenu.diagnosticMatching==='1'||this.showMenu.issueInventory==='1' || this.showMenu.indexWarning==='1' || this.showMenu.indexStatistics ==='1'"}],staticClass:"list-top"},[s("div",{staticClass:"part-title"},[t._v(" 其他工具 ")])]),s("div",{directives:[{name:"show",rawName:"v-show",value:"1"===this.showMenu.diagnosticMatching,expression:"this.showMenu.diagnosticMatching==='1'"}]},[s("div",{staticClass:"toneSpacing"},[s("span",{staticClass:"btns",on:{click:function(a){return t.auditDisposal("diagnosticMatchingQuery.html#/diagnosticMatchingQuery")}}},[t._v("辅助查询")])])]),s("div",{directives:[{name:"show",rawName:"v-show",value:"1"===this.showMenu.issueInventory,expression:"this.showMenu.issueInventory==='1'"}]},[s("div",{staticClass:"toneSpacing1"},[s("span",{staticClass:"btns",on:{click:function(a){return t.auditDisposal("issueInventory.html#/issueInventory")}}},[t._v("问题清单查询")])])]),s("div",{directives:[{name:"show",rawName:"v-show",value:"1"===this.showMenu.isdiseasetemQuery,expression:"this.showMenu.isdiseasetemQuery==='1'"}]},[s("div",{staticClass:"toneSpacing"},[s("span",{staticClass:"btns",on:{click:function(a){return t.auditDisposal("diseaseManage.html#/diseasetemQuery")}}},[t._v("病种项目查询")])])]),s("div",{directives:[{name:"show",rawName:"v-show",value:"1"===this.showMenu.indexWarning,expression:"this.showMenu.indexWarning==='1'"}]},[s("div",{staticClass:"toneSpacing"},[s("span",{staticClass:"btns",on:{click:function(a){return t.auditDisposal("indexWarning.html#/indexWarning")}}},[t._v("医师指标预警")])])]),s("div",{directives:[{name:"show",rawName:"v-show",value:"1"===this.showMenu.indexStatistics,expression:"this.showMenu.indexStatistics==='1'"}]},[s("div",{staticClass:"toneSpacing"},[s("span",{staticClass:"btns",on:{click:function(a){return t.auditDisposal("querycommon.html#/indexStatistics")}}},[t._v("医师结算指标")])])]),s("div",{directives:[{name:"show",rawName:"v-show",value:"1"===this.showMenu.isselfPayPrint,expression:"this.showMenu.isselfPayPrint==='1'"}]},[s("div",{staticClass:"toneSpacing"},[s("span",{staticClass:"btns",on:{click:function(a){return t.auditDisposal("selfPayPrint.html#/selfPayPrint")}}},[t._v("自费同意书打印")])])])]),s("div",{staticClass:"supportDiv"},[s("div",{attrs:{id:"supportmain"}},[s("div",[t._v(t._s(t.supportText))]),t._l(t.support,(function(a,i){return s("div",{key:i},[s("span",{staticStyle:{display:"inline-block",width:"55px"}},[t._v(t._s(a.supportname))]),s("span",[t._v(t._s(a.supportnum))]),s("br")])}))],2)])]),"2"===this.personSign&&""!=this.aaz263?s("div",{staticClass:"div2"},[s("div",{staticClass:"buttongroup"},t._l(this.menuList,(function(a){return s("div",[a.menuList.length>1?s("ta-dropdown",[s("ta-menu",{attrs:{slot:"overlay"},slot:"overlay"},t._l(a.menuList,(function(a,i){return s("ta-menu-item",{key:i+1,staticStyle:{"text-align":"center"},on:{click:function(s){return t.auditDisposal(a.url,a.id)}}},[t._v(" "+t._s(a.name)+" ")])})),1),s("ta-button",{attrs:{block:!0}},[t._v(" "+t._s(a.name)+" ")])],1):1==a.menuList.length?s("ta-button",{attrs:{block:!0},on:{click:function(s){return t.auditDisposal(a.menuList[0].url,a.menuList[0].id)}}},[t._v(" "+t._s(a.menuList[0].name)+" ")]):t._e()],1)})),0),s("div",{staticClass:"supportDiv"},[s("div",{attrs:{id:"supportmain"}},[s("div",[t._v(t._s(t.supportText))]),t._l(t.support,(function(a,i){return s("div",{key:i},[s("span",{staticStyle:{display:"inline-block",width:"55px"}},[t._v(t._s(a.supportname))]),s("span",[t._v(t._s(a.supportnum))]),s("br")])}))],2)])]):t._e(),"3"===this.personSign&&""!=this.aaz263?s("div",{staticClass:"div3"},[s("div",{staticClass:"belowSchedule"},[s("div",{directives:[{name:"show",rawName:"v-show",value:"1"===this.showMenu.appealApproval,expression:"this.showMenu.appealApproval==='1'"}]},[t._m(3),s("div",{staticClass:"toneSpacing"},[this.appealApproval>0?s("span",{staticClass:"circlepoint",on:{click:function(a){return t.auditDisposal("appealForDoc.html#/appealForDoc","1")}}},[t._v("待审批")]):s("span",{on:{click:function(a){return t.auditDisposal("appealForDoc.html#/appealForDoc","1")}}},[t._v("待审批")]),s("span",{staticStyle:{float:"right"},on:{click:function(a){return t.auditDisposal("appealForDoc.html#/appealForDoc","1")}}},[t._v(t._s(this.appealApproval))])])]),s("div",{directives:[{name:"show",rawName:"v-show",value:"1"===this.showMenu.diagnosticMatching,expression:"this.showMenu.diagnosticMatching==='1'"}]},[t._m(4),s("div",{staticClass:"toneSpacing"},[s("span",{staticClass:"btns",on:{click:function(a){return t.auditDisposal("diagnosticMatchingQuery.html#/diagnosticMatchingQuery")}}},[t._v("辅助查询")])])])]),s("div",{staticClass:"supportDiv"},[s("div",{attrs:{id:"supportmain"}},[s("div",[t._v(t._s(t.supportText))]),t._l(t.support,(function(a,i){return s("div",{key:i},[s("span",{staticStyle:{display:"inline-block",width:"55px"}},[t._v(t._s(a.supportname))]),s("span",[t._v(t._s(a.supportnum))]),s("br")])}))],2)])]):t._e()])},e=[function(){var t=this,a=t.$createElement,s=t._self._c||a;return s("div",{staticClass:"list-top"},[s("div",{staticClass:"part-title"},[t._v(" 出院审核 ")])])},function(){var t=this,a=t.$createElement,s=t._self._c||a;return s("div",{staticClass:"list-top"},[s("div",{staticClass:"part-title"},[t._v(" 每晚预审 ")])])},function(){var t=this,a=t.$createElement,s=t._self._c||a;return s("div",{staticClass:"uniformSpacing"},[s("div",{staticClass:"list-top"},[s("div",{staticClass:"part-title"},[t._v(" 住院申诉处理 ")])])])},function(){var t=this,a=t.$createElement,s=t._self._c||a;return s("div",{staticClass:"list-top"},[s("div",{staticClass:"part-title"},[t._v(" 申诉处理 ")])])},function(){var t=this,a=t.$createElement,s=t._self._c||a;return s("div",{staticClass:"list-top"},[s("div",{staticClass:"part-title"},[t._v(" 其他工具 ")])])}],n={name:"assistantWindow",data:function(){return{personSign:"",dscgAuditHandle:0,nightHandle:0,inhosHandle:0,inhosSubmit:0,appealApproval:0,path_ip_port:window.location.origin+"/hiiss-backend/template/".replace("//","/"),menuList:[],aaz263:"",aaz307:"",loginUsername:"",loginPassword:"",logOrOutParam:{},username:"",support:[],supportText:"服务支持咨询:",showMenu:{dscgAuditHandle:"0",nightHandle:"0",inhosHandle:"0",appealApproval:"0",otherTools:"0",diagnosticMatching:"0",issueInventory:"0",isdiseasetemQuery:"0",isselfPayPrint:"0",indexWarning:"0",indexStatistics:"0"},mtSign:""}},mounted:function(){var t=this;window.outerWidth;void 0===this.$route.query.aaz263&&null===localStorage.getItem("aaz263")||(this.$route.query.aaz263!==localStorage.getItem("aaz263")?(QClient.openUrl(this.path_ip_port+"nothing.html",2,0,65523),QClient.invokeMethod("CloseWindow",65523)):(localStorage.setItem("aaz263",this.$route.query.aaz263),localStorage.setItem("aaz307",this.$route.query.aaz307),localStorage.setItem("username",this.$route.query.username)),this.aaz263=void 0===this.$route.query.aaz263?localStorage.getItem("aaz263"):this.$route.query.aaz263,this.aaz307=void 0===this.$route.query.aaz307?localStorage.getItem("aaz307"):this.$route.query.aaz307,this.username=void 0===this.$route.query.username?localStorage.getItem("username"):this.$route.query.username,this.queryPersonSign()),this.$nextTick((function(){t.querysupport()}))},methods:{querysupport:function(){var t=this;this.Base.submit(null,{url:"assistantWindow/querySupport",data:{},autoValid:!1},{successCallback:function(a){var s=a.data;if(s.namelenth>0&&s.numlenth>0){t.supportText="服务支持咨询:";var i=1;i=s.namelenth>s.numlenth?s.namelenth:s.numlenth;for(var e=0;e<i;e++)s["supportname"+(0==e?"":e+1)]&&s["supportnum"+(0==e?"":e+1)]&&t.support.push({supportname:s["supportname"+(0==e?"":e+1)]+" ",supportnum:s["supportnum"+(0==e?"":e+1)]})}else t.supportText=""},failCallback:function(a){t.$message.error("查询失败")}})},reloadSchedule:function(){void 0===this.aaz263&&""===this.aaz263&&null===this.aaz263||this.itemsQuery()},goLogin:function(){var t=this;this.Base.submit(null,{url:"/assistantWindow/quitLogin",data:{loginId:this.loginUsername,clientId:this.clientId}},{successCallback:function(a){"1"===a.data.code&&(QClient.DBPNSUserLogin(""),QClient.invokeMethod("WindowReturnValue","type=settitle",'{"title":"医保助手","subtitle":"已退出"}'),localStorage.removeItem("aaz263"),localStorage.removeItem("aaz307"),localStorage.removeItem("username"),QClient.openUrl(t.path_ip_port+"nothing.html",2,0,65523),QClient.invokeMethod("CloseWindow",65523),QClient.openUrl(t.path_ip_port+"assistantLogin.html",0,0,65521))}})},queryPersonSign:function(){var t=this,a=new Map([["临床医务人员","1"],["科室管理员","2"],["医保办","3"],["组长","1"],["责任医生","4"]]),s=new Map([["DAYP_DOCTOR","1"],["DAYP_NURSE","2"],["EMER_NURSE","2"],["EMER_DOCTOR","1"],["INP_NURSE","2"],["INP_DOCTOR","1"],["OUTP_NURSE","2"],["OUTP_DOCTOR","1"],["MED_LAB_BILL","3"]]),i=this.aaz263,e=s.get(localStorage.getItem("systemName"));localStorage.setItem("roleType",e),"1"===e||"2"===e||"3"===e?(this.personSign="1",this.Base.submit(null,{url:"/assistantWindow/queryMenu",data:{roleNum:"1"}},{successCallback:function(a){t.menuList=a.data.data,t.fnBuildMemu()}})):this.Base.submit(null,{url:"/assistantWindow/queryPersonSign",data:{jobnumber:i}},{successCallback:function(s){if(QClient.DBPNSUserLogin(i),s.data.data.length>0){var e=a.get(s.data.data[0].rolename);t.personSign=e,t.Base.submit(null,{url:"/assistantWindow/queryMenu",data:{roleNum:e}},{successCallback:function(a){t.menuList=a.data.data,t.fnBuildMemu()}})}else t.aaz263="",t.$message.error("检查角色配置！")},failCallback:function(a){t.$message.error("登录失败，联系管理员。")},errorCallBack:function(a){t.$message.error("登录失败，联系管理员。")}})},itemsQuery:function(){var t=this,a={aze003:"0",aaz263:this.aaz263,aaz307:this.aaz307,systemName:localStorage.getItem("roleType")};"1"===this.showMenu.dscgAuditHandle&&(a.queryDscg="1","1"===localStorage.getItem("roleType")&&(a.queryType="1")),"tongji"===faceConfig.approvalHandleType&&(a.queryType="1"),this.Base.submit(null,{url:"/assistantWindow/queryDealItmeNum",data:a,autoQs:!1},{successCallback:function(a){void 0!==a.data.data.nightcount&&(t.nightHandle=a.data.data.nightcount),void 0!==a.data.data.needhandcount&&(t.inhosHandle=a.data.data.needhandcount),void 0!==a.data.data.needsubmitcount&&(t.inhosSubmit=a.data.data.needsubmitcount),void 0!==a.data.data.needapprovalcount&&(t.appealApproval=a.data.data.needapprovalcount),void 0!==a.data.data.dscgAuditCount&&(t.dscgAuditHandle=a.data.data.dscgAuditCount)}})},auditDisposal:function(t,a){var s=this.path_ip_port+t+"?aaz263="+this.aaz263+"&aaz307="+this.aaz307+"&systemName="+localStorage.getItem("roleType")+"&personSign="+this.personSign+"&username="+localStorage.getItem("username")+"&flag=ys";QClient.openUrl(s,2,0,65523),QClient.invokeMethod("ShowMaximizedWindow",65523)},fnBuildMemu:function(){if(this.menuList.length>0)for(var t=0;t<this.menuList.length;t++){var a=this.menuList[t];if(null!=a.menuList&&a.menuList.length>0)for(var s=0;s<a.menuList.length;s++){var i=a.menuList[s];"dscgAuditHandle.html#/dscgAuditHandle"===i.url&&(this.showMenu.dscgAuditHandle="1"),"approvalHandle.html#/approvalHandle"===i.url&&(this.showMenu.nightHandle="1"),"appealForDoc.html#/appealForDoc"===i.url&&("1"===this.personSign?this.showMenu.inhosHandle="1":this.showMenu.appealApproval="1"),"diagnosticMatchingQuery.html#/diagnosticMatchingQuery"===i.url&&(this.showMenu.diagnosticMatching="1"),"issueInventory.html#/issueInventory"===i.url&&(this.showMenu.issueInventory="1"),"diseaseManage.html#/diseasetemQuery"===i.url&&(this.showMenu.isdiseasetemQuery="1"),"selfPayPrint.html#/selfPayPrint"===i.url&&(this.showMenu.isselfPayPrint="1"),"indexWarning.html#/indexWarning?flag=ys"===i.url&&(this.showMenu.indexWarning="1"),"querycommon.html#/indexStatistics?flag=ys"===i.url&&(this.showMenu.indexStatistics="1")}}this.itemsQuery()}}},o=n,l=s(1001),r=(0,l.Z)(o,i,e,!1,null,"598f3d17",null),u=r.exports}}]);
"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[5319],{59624:function(t,a,e){e.r(a),e.d(a,{default:function(){return G}});var i=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticStyle:{height:"100vh"}},[e("ta-border-layout",{attrs:{leftCfg:t.leftCfg,"show-border":!1}},[e("div",{ref:"leftExtraContent",attrs:{slot:"leftExtraContent",cssClass:"leftExtraContent"},slot:"leftExtraContent"},[e("div",{staticClass:"part-top-patient"},[e("ta-title",{attrs:{title:"患者列表"}})],1)]),e("div",{staticStyle:{width:"100%",height:"100%"},attrs:{slot:"left"},slot:"left"},[e("div",{staticStyle:{height:"100%",display:"flex","flex-direction":"column"}},[e("div",{staticClass:"searchBox"},[e("div",{directives:[{name:"show",rawName:"v-show",value:"2"==this.roleMOCK,expression:"this.roleMOCK == '2'"}],staticStyle:{display:"flex","align-items":"center"}},[e("span",{staticStyle:{color:"red","margin-right":"2px","font-size":"16px"}},[t._v("*")]),e("ta-select",{staticStyle:{width:"180px","margin-right":"10px"},attrs:{defaultValue:"lucy",allowClear:"",placeholder:"选择科室",options:t.DepartOptions,dropdownMatchSelectWidth:!1,dropdownStyle:{width:"250px"},showSearch:"",size:"small",searchDelay:200},on:{change:t.changeDeaprt,search:t.handleSearch},model:{value:t.dptCode,callback:function(a){t.dptCode=a},expression:"dptCode"}})],1),e("ta-input-search",{staticStyle:{width:"230px"},attrs:{placeholder:"请输入住院号/就诊号/身份证"},on:{input:t.searchPatient},model:{value:t.searchVal,callback:function(a){t.searchVal=a},expression:"searchVal"}}),"0"==t.roleMOCK?e("div",[e("ta-button",{attrs:{type:"warning",size:"small"},on:{click:t.handelLimit}},[t._v("已授权")])],1):t._e()],1),e("div",[e("ta-tabs",{attrs:{defaultActiveKey:t.defaultActiveKey},on:{change:t.changeTAB}},[e("ta-tab-pane",{key:"2",attrs:{tab:"住院"}}),e("ta-tab-pane",{key:"1",attrs:{tab:"门诊"}},[e("div",[e("div",{staticClass:"firstLine"},[e("div",{staticStyle:{display:"flex"}},t._l(t.sceneList,(function(a,i){return e("div",{key:i},[e("ta-button",{attrs:{size:"small",type:t.currentscene===a.id?"primary":"dashed"},on:{click:function(a){t.currentscene=i,t.scene=0==i?"opt":"opsp"}}},[t._v(t._s(a.name))])],1)})),0)])])])],1)],1),"1"==t.roleMOCK?e("div",t._l(t.dataSource,(function(a,i){return e("div",{key:i,staticStyle:{margin:"5px 0"}},[e("div",{staticStyle:{"margin-bottom":"5px"}},[t._v(" "+t._s("@self"==a.authorizerName?"我":"【"+a.authorizerName+"】授权给我")+"的患者： ")]),e("div",[e("ta-big-table",{ref:"patientTable",refInFor:!0,staticStyle:{cursor:"pointer"},attrs:{border:"",stripe:"",resizable:"","highlight-hover-row":"","highlight-current-row":"","show-overflow":"",height:"600",size:"mini",data:a.list,"empty-text":"暂无数据"},on:{"cell-click":function(a){return t.cellClick(i,a)}}},[e("ta-big-table-column",{attrs:{width:"40px",title:" ",type:"seq",fixed:"left",align:"center"}}),e("ta-big-table-column",{attrs:{field:"mdtrtId",width:"100px",title:t.mdtrtIdTitle,align:"center"}}),e("ta-big-table-column",{attrs:{field:"bedno",title:"床号",visible:t.bednoVisible,width:"70px",align:"center"}}),e("ta-big-table-column",{attrs:{field:"patnName",title:"姓名",width:"70px",align:"center"}}),e("ta-big-table-column",{attrs:{field:"gend",title:"性别",width:"50px",align:"center","collection-type":"SEX"}}),e("ta-big-table-column",{attrs:{field:"brdy",title:"年龄",width:"50px",align:"center",formatter:t.formatterAge}}),e("ta-big-table-column",{attrs:{field:"setlStas",title:"结算状态",width:"80px",align:"center"},scopedSlots:t._u([{key:"default",fn:function(a){var i=a.row;return["0"==i.setlStas?e("span",{staticStyle:{color:"green"}},[t._v("正常结算")]):"1"==i.setlStas?e("span",{staticStyle:{color:"red"}},[t._v("取消结算")]):"2"==i.setlStas?e("span",{staticStyle:{color:"orange"}},[t._v(" 作废结算 ")]):"9"==i.setlStas?e("span",{staticStyle:{color:"blue"}},[t._v(" 未结算 ")]):t._e()]}}],null,!0)}),e("ta-big-table-column",{attrs:{title:"操作",width:"50px",align:"center"},scopedSlots:t._u([{key:"default",fn:function(a){var i=a.row;return[i.latestAaz217?e("span",{staticStyle:{color:"#276cf5"},on:{click:function(a){return t.gotoPatientDetaills(i)}}},[t._v("详情")]):t._e()]}}],null,!0)})],1)],1)])})),0):e("div",{staticStyle:{flex:"1",display:"flex","flex-direction":"column"}},[e("div",{staticStyle:{flex:"1"}},[e("ta-big-table",{ref:"patientTable",staticStyle:{cursor:"pointer"},attrs:{border:"",stripe:"",resizable:"","highlight-hover-row":"","highlight-current-row":"","show-overflow":"",height:"100%",size:"mini",data:t.dataSource[0].list,"empty-text":"暂无数据"},on:{"cell-click":function(a){return t.cellClick(0,a)}}},[e("ta-big-table-column",{attrs:{width:"40px",title:" ",type:"seq",fixed:"left",align:"center"}}),e("ta-big-table-column",{attrs:{field:"mdtrtId",width:"100px",title:"0"==this.roleMOCK?"就诊号":"住院号",align:"center"}}),e("ta-big-table-column",{attrs:{field:"bedno",title:"床号",width:"70px",align:"center"}}),e("ta-big-table-column",{attrs:{field:"patnName",title:"姓名","min-width":"70px",align:"center"}}),e("ta-big-table-column",{attrs:{field:"gend",title:"性别",width:"50px",align:"center","collection-type":"SEX"}}),e("ta-big-table-column",{attrs:{field:"brdy",title:"年龄",width:"50px",align:"center",formatter:t.formatterAge}}),e("ta-big-table-column",{attrs:{field:"setlStas",title:"结算状态",width:"80px",align:"center"},scopedSlots:t._u([{key:"default",fn:function(a){var i=a.row;return["0"==i.setlStas?e("span",{staticStyle:{color:"green"}},[t._v("正常结算")]):"1"==i.setlStas?e("span",{staticStyle:{color:"red"}},[t._v("取消结算")]):"2"==i.setlStas?e("span",{staticStyle:{color:"orange"}},[t._v(" 作废结算 ")]):"9"==i.setlStas?e("span",{staticStyle:{color:"blue"}},[t._v(" 未结算 ")]):t._e()]}}])}),e("ta-big-table-column",{attrs:{title:"操作",width:"50px",align:"center"},scopedSlots:t._u([{key:"default",fn:function(a){var i=a.row;return[i.latestAaz217?e("span",{staticStyle:{color:"#276cf5"},on:{click:function(a){return t.gotoPatientDetaills(i)}}},[t._v("详情")]):t._e()]}}])})],1)],1)])])]),e("div",{staticClass:"content"},[e("div",{staticClass:"part-top"},[e("ta-title",{attrs:{title:"待审项目"}})],1),e("div",{staticStyle:{display:"flex","align-items":"center","flex-wrap":"wrap","margin-left":"10px","margin-bottom":"5px"}},[e("ta-input-search",{staticStyle:{width:"141px"},attrs:{placeholder:"请输入项目名称"},model:{value:t.queryForm.project_name,callback:function(a){t.$set(t.queryForm,"project_name",a)},expression:"queryForm.project_name"}}),"2"!==t.roleMOCK?e("div",{staticStyle:{display:"flex","align-items":"center","margin-left":"10px"}},[e("span",[t._v("开单科室：")]),t._l(t.departList,(function(a,i){return e("div",{key:i},[e("ta-button",{attrs:{size:"small",type:t.currentDepart===a.id?"primary":"dashed"},on:{click:function(a){return t.selectDepart(i)}}},[t._v(t._s(a.name))])],1)}))],2):t._e(),e("div",{staticStyle:{display:"flex","align-items":"center","margin-left":"10px"}},[e("span",[t._v("费用类别：")]),t._l(t.tipsType,(function(a,i){return e("div",{key:i},[e("ta-button",{attrs:{size:"small",type:t.currentTipsIndex===i?"primary":"dashed"},on:{click:function(e){return t.selectTips(a,i)}}},[t._v(t._s(a.name))])],1)})),e("ta-select",{staticStyle:{width:"120px"},attrs:{options:t.tipsOptions,size:"small"},on:{change:t.handleChange},model:{value:t.tipValue,callback:function(a){t.tipValue=a},expression:"tipValue"}})],2),e("div",{staticStyle:{display:"flex","align-items":"center","margin-left":"10px"}},[e("span",[t._v("预审结果：")]),e("ta-checkbox-group",{attrs:{options:t.excuteRes},model:{value:t.queryForm.check_res,callback:function(a){t.$set(t.queryForm,"check_res",a)},expression:"queryForm.check_res"}})],1)],1),e("div",{staticClass:"tableContent",staticStyle:{width:"97%","margin-left":"10px","margin-top":"5px"}},[e("ta-big-table",{ref:"xTable",staticStyle:{width:"100%",cursor:"pointer"},attrs:{border:"","highlight-hover-row":"","show-overflow":"",data:t.filterPatientObjects,height:"auto",resizable:!0,"auto-resize":"",size:"mini","empty-text":"暂无数据",width:"min-width","expand-config":{iconOpen:"caret-down",iconClose:"caret-right"}},on:{"cell-click":t.cellClickEvent,"toggle-row-expand":t.toggleExpandChangeEvent}},[e("ta-big-table-column",{attrs:{type:"expand",visible:!0,width:"35"},scopedSlots:t._u([{key:"content",fn:function(a){var i=a.row;return[e("ta-big-table",{ref:"dTablechildren",attrs:{data:i.subProject,border:"","highlight-hover-row":"","show-overflow":"","auto-resize":"","empty-text":"-","header-cell-style":t.headerCellStyle,size:"mini",resizable:!0}},[e("ta-big-table-column",{attrs:{width:"40px",title:" ",type:"seq",align:"center"}}),e("ta-big-table-column",{attrs:{width:"70px"},scopedSlots:t._u([{key:"default",fn:function(a){var i=a.row;return[e("div",["甲"===i.aka065?e("div",{staticStyle:{float:"left",background:"#67C23A",color:"#FFFFFF",width:"14px"}},[e("a",{staticClass:"tooltipContent",attrs:{href:"#",title:"甲类项目"}},[t._v("甲")])]):"乙"===i.aka065?e("div",{staticStyle:{float:"left",background:"#E6A23C",color:"#FFFFFF",width:"14px"}},[e("a",{staticClass:"tooltipContent",attrs:{href:"#",title:"乙类项目"}},[t._v("乙")])]):"丙"===i.aka065?e("div",{staticStyle:{float:"left",background:"#FF0000",color:"#FFFFFF",width:"14px"}},[e("a",{staticClass:"tooltipContent",attrs:{href:"#",title:"丙类项目"}},[t._v("丙")])]):i.aka065?t._e():e("div"),"2"===i.ape800?e("div",{staticStyle:{float:"right",background:"#FF0000",color:"#FFFFFF",width:"28px"}},[e("a",{staticClass:"tooltipContent2",attrs:{href:"#",title:"违规项目"}},[t._v(t._s(t.CollectionLabel("APE800_ADUIT_TEXT","2")))])]):"1"===i.ape800?e("div",{staticStyle:{float:"right",background:"#E6A23C",color:"#FFFFFF",width:"28px"}},[e("a",{staticClass:"tooltipContent2",attrs:{href:"#",title:"可疑项目"}},[t._v(t._s(t.CollectionLabel("APE800_ADUIT_TEXT","1")))])]):"3"===i.ape800?e("div",{staticStyle:{float:"right",background:"deepskyblue",color:"#FFFFFF",width:"28px"}},[e("a",{staticClass:"tooltipContent2",attrs:{href:"#",title:"仅提醒"}},[t._v(t._s(t.CollectionLabel("APE800_ADUIT_TEXT","3")))])]):i.ape800?t._e():e("div")])]}}],null,!0)}),e("ta-big-table-column",{attrs:{field:"ake006","min-width":"120px",align:"left","header-align":"center",title:"项目名称"}}),e("ta-big-table-column",{attrs:{field:"ykz259","min-width":"120px",align:"left","header-align":"center",title:"限制条件"}}),e("ta-big-table-column",{attrs:{field:"aae036",align:"center","min-width":"180px",title:"发生时间"}}),e("ta-big-table-column",{attrs:{field:"akc226",align:"right","header-align":"center","min-width":"50px",title:"数量"},scopedSlots:t._u([{key:"default",fn:function(a){var i=a.row;return[i.akc226?e("span",[t._v(t._s(i.akc226))]):e("span",[t._v("--")])]}}],null,!0)}),e("ta-big-table-column",{attrs:{field:"fail",align:"center","min-width":"90px",title:"审核详情"},scopedSlots:t._u([{key:"default",fn:function(a){var i=a.row;return[e("span",{staticStyle:{color:"#276cf5"},on:{click:function(a){return t.openAuditDetails(i)}}},[t._v(t._s("0"===i.fail?"违规详情":"合规详情"))])]}}],null,!0)}),e("ta-big-table-column",{attrs:{field:"akc049",align:"center","min-width":"90px",title:"开嘱医生"}}),e("ta-big-table-column",{attrs:{field:"aae386",align:"left","min-width":"190px","header-align":"center",title:"开单科室"}}),e("ta-big-table-column",{attrs:{field:"oldape893","min-width":"90px",align:"center","header-align":"center",title:"操作历史"},scopedSlots:t._u([{key:"default",fn:function(a){var i=a.row;return[e("span",{staticStyle:{color:"#276cf5"},on:{click:function(a){return t.openHistoryModal(i)}}},[t._v(t._s("1"===i.oldape893?"备案":"2"===i.oldape893?"自费":"3"===i.oldape893?"取消/置换":""))])]}}],null,!0)}),e("ta-big-table-column",{attrs:{field:"op",align:"center",title:"操作",fixed:"right","min-width":"200px"},scopedSlots:t._u([{key:"default",fn:function(a){var i=a.row;return[e("div",{directives:[{name:"show",rawName:"v-show",value:"ipt"===t.scene||"0"===i.fail,expression:" scene === 'ipt' || row.fail==='0' "}],staticStyle:{display:"flex","justify-content":"center"}},t._l(t.oprateBtn,(function(a,n){return e("div",{key:n},[e("ta-button",{attrs:{type:i.currentOprate==n?"primary":"dashed",size:"small"},on:{click:function(a){return t.oprateEvent(i,n)}}},[t._v(t._s(a.name))])],1)})),0)]}}],null,!0)})],1)]}}])}),e("ta-big-table-column",{attrs:{width:"40px",title:" ",type:"seq",fixed:"left",align:"center"}}),e("ta-big-table-column",{attrs:{width:"70px"},scopedSlots:t._u([{key:"default",fn:function(a){var i=a.row;return[e("div",["甲"===i.aka065?e("div",{staticStyle:{float:"left",background:"#67C23A",color:"#FFFFFF",width:"14px"}},[e("a",{staticClass:"tooltipContent",attrs:{href:"#",title:"甲类项目"}},[t._v("甲")])]):"乙"===i.aka065?e("div",{staticStyle:{float:"left",background:"#E6A23C",color:"#FFFFFF",width:"14px"}},[e("a",{staticClass:"tooltipContent",attrs:{href:"#",title:"乙类项目"}},[t._v("乙")])]):"丙"===i.aka065?e("div",{staticStyle:{float:"left",background:"#FF0000",color:"#FFFFFF",width:"14px"}},[e("a",{staticClass:"tooltipContent",attrs:{href:"#",title:"丙类项目"}},[t._v("丙")])]):i.aka065?t._e():e("div"),"2"===i.ape800?e("div",{staticStyle:{float:"right",background:"#FF0000",color:"#FFFFFF",width:"28px"}},[e("a",{staticClass:"tooltipContent2",attrs:{href:"#",title:"违规项目"}},[t._v(t._s(t.CollectionLabel("APE800_ADUIT_TEXT","2")))])]):"1"===i.ape800?e("div",{staticStyle:{float:"right",background:"#E6A23C",color:"#FFFFFF",width:"28px"}},[e("a",{staticClass:"tooltipContent2",attrs:{href:"#",title:"可疑项目"}},[t._v(t._s(t.CollectionLabel("APE800_ADUIT_TEXT","1")))])]):"3"===i.ape800?e("div",{staticStyle:{float:"right",background:"deepskyblue",color:"#FFFFFF",width:"28px"}},[e("a",{staticClass:"tooltipContent2",attrs:{href:"#",title:"仅提醒"}},[t._v(t._s(t.CollectionLabel("APE800_ADUIT_TEXT","3")))])]):i.ape800?t._e():e("div")])]}}])}),e("ta-big-table-column",{attrs:{field:"ake006",title:"项目名称","header-align":"center",align:"left","min-width":"120px"}}),e("ta-big-table-column",{attrs:{field:"ykz259",title:"限制条件","header-align":"center",align:"left","min-width":"180px"}}),e("ta-big-table-column",{attrs:{field:"akc226",title:"数量",align:"right","header-align":"center",width:"50px"}}),e("ta-big-table-column",{attrs:{field:"akc225",title:"单价","header-align":"center",align:"right",width:"60px",formatter:"formatAmount"}}),e("ta-big-table-column",{attrs:{field:"aaz560",title:"备案信息","header-align":"center",align:"left","min-width":"90px"}}),e("ta-big-table-column",{attrs:{field:"op",title:"操作",fixed:"right","min-width":"200px",align:"center"},scopedSlots:t._u([{key:"default",fn:function(a){var i=a.row;return[e("div",{directives:[{name:"show",rawName:"v-show",value:"ipt"===t.scene||"0"===i.fail,expression:" scene === 'ipt' || row.fail==='0' "}],staticStyle:{display:"flex","justify-content":"center"}},t._l(t.oprateBtn,(function(a,n){return e("div",{key:n},[e("ta-button",{attrs:{type:i.currentOprate==n?"primary":"dashed",size:"small"},on:{click:function(a){return t.oprateEvent(i,n)}}},[t._v(t._s(a.name))])],1)})),0)]}}])})],1)],1),"2"!==t.roleMOCK?e("div",{staticClass:"saftBox"}):t._e(),"2"!==t.roleMOCK?e("div",{staticClass:"submitBtn"},[e("ta-button",{attrs:{type:"primary",disabled:!t.patientObjects.length},on:{click:t.handleSubmitData}},[t._v("提交")])],1):t._e()])]),e("limitModal",{attrs:{visible:t.limitModalVisible},on:{handleOk:t.handleOk,handleCancel:t.handleCancel}}),e("medicineSearch",{attrs:{visible:t.medicineSearchVisible},on:{cancel:t.cancelMedicineModal}}),e("keep-on-record",{attrs:{visible:t.keepOnRecordVisible,params:t.rowData},on:{handleSave:t.handleUpdateRow,handleClose:t.handleClose}}),e("bill-audit",{attrs:{visible:t.billVisible,"bill-data":t.billData},on:{"update:billData":function(a){t.billData=a},"update:bill-data":function(a){t.billData=a},handleSave:t.billSave,handleClose:t.billClose}}),e("div",[e("ta-modal",{attrs:{title:"操作历史",width:"45%",visible:t.oprateHistoryVisible},on:{ok:t.handleHisOk,cancel:t.handleHisCancel}},[e("template",{slot:"footer"},[e("div",{staticStyle:{display:"flex","justify-content":"center"}},[e("ta-button",{on:{click:t.handleHisCancel}},[t._v("取消")]),e("ta-button",{attrs:{type:"primary"},on:{click:t.handleHisOk}},[t._v(" 确定 ")])],1)]),e("ta-big-table",{attrs:{border:"","highlight-hover-row":"",height:"300","auto-resize":"",data:t.oprateHistoryList}},[e("ta-big-table-column",{attrs:{type:"seq",align:"center",title:"",width:"60"}}),e("ta-big-table-column",{attrs:{field:"ape893",title:"操作","header-align":"center",align:"center",width:"80"},scopedSlots:t._u([{key:"default",fn:function(a){var i=a.row;return[e("span",[t._v(t._s("1"==i.ape893?"备案":"自费"))])]}}])}),e("ta-big-table-column",{attrs:{field:"aaz560","header-align":"center",align:"left",title:"操作理由"}}),e("ta-big-table-column",{attrs:{field:"aae040",align:"left","header-align":"center",title:"操作时间",width:"160"}}),e("ta-big-table-column",{attrs:{field:"ykz041",align:"center",title:"操作人",width:"80"}})],1)],2)],1),e("div",{attrs:{id:"info"}},[e("ta-modal",{staticStyle:{top:"10px"},attrs:{width:"95%","destroy-on-close":!0,footer:null,wrapClassName:"inp-modal-wrap"},on:{cancel:t.handleAuditCancel},model:{value:t.visible,callback:function(a){t.visible=a},expression:"visible"}},[e("ta-icon",{staticStyle:{color:"black"},attrs:{slot:"closeIcon",type:"close"},slot:"closeIcon"}),e("span",{staticStyle:{"font-weight":"normal"},attrs:{slot:"title"},slot:"title"},[t._v(" 审核详情")]),e("atient-details",{attrs:{fyRecord:t.bfRecord}})],1),e("keep-on-record-two",{ref:"keeponrecord",attrs:{visible:t.keepOnRecordVisible2,params:t.rowData,"ykz010-arr":t.ykz010Arr,"ykz042-arr":t.ykz042Arr},on:{handleSave:t.handleUpdateRow2,handleClose:t.handleClose2}})],1)],1)},n=[],r=e(48534),l=e(66347),s=e(89584),c=(e(36133),e(32564),e(88412)),o=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",[e("ta-modal",{attrs:{title:"权限授予",visible:t.visible,"max-height":550,width:1e3},on:{cancel:t.handleCancel}},[e("template",{slot:"footer"},[e("div",{staticStyle:{display:"flex","justify-content":"center"}},[e("ta-button",{on:{click:t.handleCancel}},[t._v("关闭")])],1)]),e("div",{staticClass:"container"},[e("div",[t._l(t.authorityList,(function(a,i){return e("div",{key:i,staticStyle:{display:"flex","align-items":"center","justify-content":"space-around",margin:"5px 0"}},[e("span",{staticStyle:{margin:"0 5px"}},[t._v("将")]),e("ta-select",{staticStyle:{width:"30%"},attrs:{placeholder:"请选择授权科室",mode:"multiple",allowClear:"",options:t.currentUserDepart},model:{value:a.authDepart,callback:function(e){t.$set(a,"authDepart",e)},expression:"limit.authDepart"}}),e("span",{staticStyle:{margin:"0 5px"}},[t._v("的患者后台审核权限授予：")]),e("ta-select",{staticStyle:{width:"30%"},attrs:{showSearch:"",searchDelay:200,virtual:!0,allowClear:"",placeholder:"请选择授予人员",optionLabelProp:"all",options:t.currentUser},on:{search:t.handleSearch},model:{value:a.commissionedId,callback:function(e){t.$set(a,"commissionedId",e)},expression:"limit.commissionedId"}}),e("ta-icon",{staticStyle:{"margin-left":"10px"},attrs:{type:"minus-circle"},on:{click:function(a){return t.deleteItem(i)}}})],1)})),e("ta-button",{staticStyle:{width:"100%","margin-top":"10px"},attrs:{type:"dashed"},on:{click:t.addform}},[e("ta-icon",{attrs:{type:"plus"}})],1)],2),e("div",{staticStyle:{margin:"10px 0"}},[t._v("权限授予历史记录: "),e("ta-button",{staticStyle:{float:"right"},attrs:{type:"primary"},on:{click:t.selectItem}},[t._v(" 取消授权")]),e("ta-button",{staticStyle:{float:"right"},attrs:{type:"primary"},on:{click:t.handleOk}},[t._v(" 新增授权")])],1),e("div",{staticStyle:{height:"300px"}},[e("ta-big-table",{ref:"xTable",staticStyle:{cursor:"pointer"},attrs:{border:"",stripe:"",size:"medium","checkbox-config":{trigger:"row",checkMethod:function(t){return"1"===t.row.effective}},resizable:"","auto-resize":"","highlight-hover-row":"",height:"auto",data:t.tableData}},[e("ta-big-table-column",{attrs:{type:"checkbox",fixed:"left",width:"40px",align:"center"}}),e("ta-big-table-column",{attrs:{title:" ",type:"seq","min-width":"60",align:"center"}}),e("ta-big-table-column",{attrs:{field:"createTime","min-width":"180",title:"开始时间",align:"center"}}),e("ta-big-table-column",{attrs:{field:"expireTime","min-width":"180",title:"结束时间",align:"center"}}),e("ta-big-table-column",{attrs:{field:"authDepartmentName",title:"科室",align:"center","min-width":"120"}}),e("ta-big-table-column",{attrs:{field:"commissionedName",title:"被授权人","min-width":"80",align:"center"}}),e("ta-big-table-column",{attrs:{title:"授权状态","min-width":"80",align:"center"},scopedSlots:t._u([{key:"default",fn:function(a){var e=a.row;return[t._v(" "+t._s("1"===e.effective?"已授权":"已取消授权")+" ")]}}])})],1)],1)])],2)],1)},u=[],d={name:"limitModal",props:{visible:{type:Boolean,default:!1},params:{type:Object,default:function(){return{}}}},data:function(){return{currentUserDepart:[],currentUser:[],authorityList:[],tableData:[]}},mounted:function(){this.getHistorLimit()},methods:{handleSearch:function(t){this.getLimitPerson(t)},getHistorLimit:function(){var t=this;this.tableData=[];var a={url:"/auditOnBackend/getAuthHistoryList",autoValid:!0},e={successCallback:function(a){t.tableData=a.data.data},failCallback:function(a){t.$message.error("数据加载失败")}};this.Base.submit(null,a,e)},handleOk:function(){this.$emit("handleOk"),this.authorityList=this.authorityList.reduce((function(t,a){return t.find((function(t){return t.authDepartment===a.authDepartment&&t.commissionedId===a.commissionedId}))||t.push(a),t}),[]).filter((function(t){return 0!==t.authDepart.length||""!==t.commissionedId})),0!==this.authorityList.length?this.giveLimit():this.$message.error("还未填写数据")},handleCancel:function(){this.$emit("handleCancel")},addform:function(){this.authorityList.length||this.currentUserDepart.length||(this.getCurentDepartMsg(),this.getLimitPerson()),this.authorityList.push({authDepart:[],authDepartment:"",commissionedId:void 0})},deleteItem:function(t){this.authorityList=this.authorityList.filter((function(a,e){return e!==t}))},giveLimit:function(){var t=this,a=!0;if(this.authorityList.forEach((function(e){if(0==e.authDepart.length||!e.commissionedId)return t.$message.error("请先完成填写"),void(a=!1);var i="";e.authDepart.forEach((function(t){i=i?"".concat(i,",").concat(t):"".concat(t)})),e.authDepartment=i})),a){var e={url:"/auditOnBackend/authToDoctor",autoValid:!0,autoQs:!1,data:{authorityList:this.authorityList}},i={successCallback:function(a){t.authorityList=[],t.getHistorLimit()}};this.Base.submit(null,e,i)}},selectItem:function(){var t=this,a=this.$refs.xTable.getCheckboxRecords().map((function(t){return t.id}));if(0!==a.length){var e={url:"/auditOnBackend/cancelAuthority",autoValid:!0,data:{id:JSON.stringify(a)}},i={successCallback:function(a){t.getHistorLimit()}};this.Base.submit(null,e,i)}else this.$message.warning("请选择需要取消授权的数据")},getLimitPerson:function(t){var a=this,e={url:"/miimCommonRead/queryDoctorRpcList",autoValid:!0,data:{doctorNo:t}},i={successCallback:function(t){a.currentUser=t.data.resultData}};this.Base.submit(null,e,i)},getCurentDepartMsg:function(){var t=this,a={url:"/auditOnBackend/getCurrentUserDptList",autoValid:!0},e={successCallback:function(a){a.data.data.forEach((function(a){t.currentUserDepart.push(a)}))}};this.Base.submit(null,a,e)}}},h=d,p=e(1001),f=(0,p.Z)(h,o,u,!1,null,"74c1d2d8",null),k=f.exports,m=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",[e("ta-modal",{attrs:{width:"65%",footer:null,title:"靶向药用药历史记录查询",visible:t.visible},on:{cancel:t.cancel}},[e("div",[e("ta-input-search",{staticStyle:{width:"40%"},attrs:{placeholder:"请输入患者身份证号码",enterButton:""},on:{search:t.onSearch}}),e("div",{staticStyle:{margin:"10px 0"}},[t._v("院内记录：")]),e("ta-big-table",{attrs:{border:"",stripe:"",resizable:"","auto-resize":"","highlight-hover-row":"",height:"40%",data:t.tableData}},[e("ta-big-table-column",{attrs:{type:"seq",width:"40",title:" ",align:"center"}}),e("ta-big-table-column",{attrs:{field:"name","min-width":"180",title:"医保项目名称",align:"center"}}),e("ta-big-table-column",{attrs:{field:"code","min-width":"200",title:"医保项目编码",align:"center"}}),e("ta-big-table-column",{attrs:{field:"num",title:"数量","min-width":"60",align:"center"}}),e("ta-big-table-column",{attrs:{field:"price",title:"单价","min-width":"60",align:"center"}}),e("ta-big-table-column",{attrs:{field:"startTime","min-width":"160",title:"费用发生时间",align:"center"}}),e("ta-big-table-column",{attrs:{field:"hosptialName",title:"定点医药机构名称","min-width":"160",align:"center"}}),e("ta-big-table-column",{attrs:{field:"doctor",title:"开单医生","min-width":"80",align:"center"}})],1),e("div",{staticStyle:{margin:"10px 0"}},[t._v("其他记录：")]),e("ta-big-table",{attrs:{border:"",stripe:"",resizable:"","auto-resize":"","highlight-hover-row":"",height:"40%",data:t.tableData}},[e("ta-big-table-column",{attrs:{type:"seq",width:"40",title:" ",align:"center"}}),e("ta-big-table-column",{attrs:{field:"name","min-width":"180",title:"医保项目名称",align:"center"}}),e("ta-big-table-column",{attrs:{field:"code","min-width":"200",title:"医保项目编码",align:"center"}}),e("ta-big-table-column",{attrs:{field:"num",title:"数量","min-width":"40",align:"center"}}),e("ta-big-table-column",{attrs:{field:"price",title:"单价","min-width":"40",align:"center"}}),e("ta-big-table-column",{attrs:{field:"startTime","min-width":"160",title:"费用发生时间",align:"center"}}),e("ta-big-table-column",{attrs:{field:"hosptialName",title:"定点医药机构名称","min-width":"160",align:"center"}})],1)],1)])],1)},b=[],g={name:"medicineSearch",props:{visible:{type:Boolean,default:!1}},data:function(){return{tableData:[{name:"甲磺酸阿帕替尼片",code:"XL01XEA288A001010101445",num:"2",price:"1.25",startTime:"2023-04-13 10:51:50",hosptialName:"哈尔滨医科大学附属肿瘤医院",doctor:"朱晶晶"}]}},methods:{onSearch:function(){},cancel:function(){this.$emit("cancel")}}},y=g,v=(0,p.Z)(y,m,b,!1,null,null,null),z=v.exports,w=function(){var t=this,a=this,e=a.$createElement,i=a._self._c||e;return i("div",[i("ta-modal",{attrs:{title:"备案理由",visible:a.visible,height:550,width:1e3,"mask-closable":!1},on:{ok:a.handleSave,cancel:a.handleClose}},[i("div",{staticStyle:{display:"flex","justify-content":"center"},attrs:{slot:"footer"},slot:"footer"},[i("ta-button",{style:{marginRight:8},attrs:{type:"primary"},on:{click:a.handleSave}},[a._v(" 保存 ")]),i("ta-button",{on:{click:a.handleClose}},[a._v(" 取消 ")]),i("ta-button",{on:{click:a.saveASTemp}},[a._v(" 保存为备案理由 ")])],1),i("ta-menu",{staticStyle:{"margin-bottom":"10px"},attrs:{mode:"horizontal"},model:{value:a.current,callback:function(t){a.current=t},expression:"current"}},[i("ta-menu-item",{key:"record",on:{click:a.queryRecords}},[a._v(" 历史备案记录 ")]),i("ta-menu-item",{key:"reason",on:{click:a.queryTemplateDatas}},[a._v(" 备案理由模板 ")])],1),i("ta-big-table",{directives:[{name:"show",rawName:"v-show",value:"record"===a.current[0],expression:"current[0] === 'record'"}],staticStyle:{width:"100%"},attrs:{border:"","highlight-hover-row":"","show-overflow":"",data:a.dataSource1,height:"300",resizable:!0,"auto-resize":"",size:"mini"}},[i("ta-big-table-column",{attrs:{type:"seq",fixed:"left",width:"50px",title:" ",align:"center"}}),i("ta-big-table-column",{attrs:{field:"aaz213","header-align":"center",title:"管理Id",visible:!1}}),i("ta-big-table-column",{attrs:{field:"aae040",title:"备案时间","header-align":"center","min-width":"100px"}}),i("ta-big-table-column",{attrs:{field:"ykz041","header-align":"center",align:"center\n      ",title:"医生名字","min-width":"60px"}}),i("ta-big-table-column",{attrs:{field:"aaz560",title:"备案理由","header-align":"center","min-width":"100px"}}),i("ta-big-table-column",{attrs:{field:"",title:"操作",width:"100px",align:"center"},scopedSlots:a._u([{key:"default",fn:function(t){var e=t.row;return[i("div",{staticClass:"opareteItem",on:{click:function(t){return a.useRowInfo(e,"history")}}},[a._v("使用")])]}}])})],1),i("ta-big-table",{directives:[{name:"show",rawName:"v-show",value:"reason"===a.current[0],expression:"current[0] === 'reason'"}],staticStyle:{width:"100%"},attrs:{border:"","highlight-hover-row":"","show-overflow":"",data:a.dataSource2,height:"300",resizable:!0,"auto-resize":"",size:"mini"}},[i("ta-big-table-column",{attrs:{type:"seq",fixed:"left",width:"50px",title:" ",align:"center"}}),i("ta-big-table-column",{attrs:{field:"aaz213",title:"管理Id",visible:!1}}),i("ta-big-table-column",{attrs:{field:"id",title:"Id",visible:!1}}),i("ta-big-table-column",{attrs:{field:"content",title:"备案理由","header-align":"center","min-width":"500px"}}),i("ta-big-table-column",{attrs:{field:"type",title:"类型","header-align":"center","min-width":"80px",align:"center",formatter:a.formatterType}}),i("ta-big-table-column",{attrs:{field:"",title:"操作",width:"130px",align:"center"},scopedSlots:a._u([{key:"default",fn:function(t){return[i("ta-table-operate",{attrs:{"operate-menu":a.operateMenu,"row-info":t}})]}}])})],1),i("ta-form",{attrs:{"auto-form-create":function(a){return t.form=a},layout:"horizontal","form-layout":!0,"label-width":"80px",col:{xs:1,sm:1,md:1,lg:1,xl:1,xxl:1}}},[i("ta-form-item",{attrs:{label:"引导信息","field-decorator-id":"ykz018",disabled:!0}},[i("ta-textarea",{attrs:{placeholder:"请输入引导信息",rows:3,"show-length":!0}})],1),i("ta-form-item",{attrs:{label:"备案理由","field-decorator-id":"content",require:!0,"field-decorator-options":{rules:[{validator:a.validReason}]},initValue:a.changeAddContent}},[i("ta-textarea",{attrs:{placeholder:"请输入内容；不能为空；不能全是特殊字符、符号、数字或三者组合；最大长度不能超过400各字符",rows:3,"show-length":!0}})],1)],1)],1),i("ta-modal",{attrs:{title:"备案理由模板编辑",visible:a.editTempVisible},on:{ok:a.editContent,cancel:a.handleEditClose}},[i("ta-form",{attrs:{"auto-form-create":function(a){return t.formEdit=a},layout:"horizontal","form-layout":!0,"label-width":"80px",col:{xs:1,sm:1,md:1,lg:1,xl:1,xxl:1}}},[i("ta-form-item",{attrs:{fieldDecoratorId:"id",hidden:"true",initValue:a.changeId}},[i("ta-input")],1),i("ta-form-item",{attrs:{"field-decorator-id":"editContent",label:"修改内容",require:!0,"field-decorator-options":{rules:[{validator:a.validReason}]},initValue:a.changeContent}},[i("ta-textarea",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入内容；不能为空；不能全是特殊字符、符号、数字或三者组合；最大长度不能超过400各字符",rows:3,"show-length":!0}})],1)],1)],1)],1)},x=[],C="nightAudit/",_={getBasePath:function(){return faceConfig.basePath+C},queryTableData:function(t,a){Base.submit(null,{url:C+"queryPatients",data:t,autoQs:!1},{successCallback:function(t){return a(t)}})},updatePatientInfo:function(t,a){Base.submit(null,{url:C+"saveOperation",data:t,autoQs:!1},{successCallback:function(t){return a(t)}})},getRowDetails:function(t,a){Base.submit(null,{url:C+"queryAdviceDetail",data:t,autoQs:!1},{successCallback:function(t){return a(t)}})},queryKeepRecords:function(t,a){Base.submit(null,{url:"auditOnBackend/getOprHistory",data:t},{successCallback:function(t){return a(t)}})},queryTemplateDatas:function(t,a){Base.submit(null,{url:C+"getRecordFormwork",data:t},{successCallback:function(t){return a(t)}})},saveAe21:function(t,a){Base.submit(null,{url:C+"saveAe21",data:t,autoQs:!1},{successCallback:function(t){return a(t)}})},queryRuleInfo:function(t,a){Base.submit(null,{url:"/mtt/api/ruleSearch/queryRule",data:t,autoQs:!1},{successCallback:function(t){return a(t)}})},queryPagePackageRule:function(t,a){Base.submit(null,{url:"/mtt/api/ruleSearch/pagePackageContent",data:t,autoQs:!1},{successCallback:function(t){return a(t)}})}},S=e(36797),D=e.n(S),F={name:"keepOnRecord",components:{},props:{visible:{type:Boolean,default:!1},params:{type:Object,default:function(){return{}}}},data:function(){var t=this;return{current:["record"],dataSource1:[],dataSource2:[],editTempVisible:!1,changeAddContent:null,changeContent:null,changeId:null,operateMenu:[{name:"使用",onClick:function(a){t.useRowInfo(a,"beian")}},{name:"编辑",isShow:function(t){return"3"===t.type},onClick:function(a){t.editTempVisible=!0,t.changeContent=a.content,t.changeId=a.id}},{name:"删除",isShow:function(t){return"3"===t.type},onClick:function(a){t.$confirm({title:"继续操作",content:"删除不可恢复，是否继续删除?",onOk:function(){t.Base.submit(null,{url:"/nightAudit/deleteAe21",data:{id:a.id,medinscode:t.params.fixmedinsCode}}).then((function(a){t.warnText="删除成功!",t.warnVisible=!0,t.dataSource2=a.data.data})).catch((function(a){t.warnText="删除失败!",t.warnVisible=!0})),t.warnVisible=!0},onCancel:function(){}})}}]}},watch:{visible:{handler:function(t){var a=this;this.$nextTick((function(){a.queryRecords(),a.form.setFieldsValue({ykz018:a.params.ykz018,content:a.params.aaz560})}))}}},mounted:function(){this.changeAddContent=top.indexTool.getUserInfo().jobNumber},methods:{moment:D(),validReason:function(t,a,e){var i=this,n=/[^`~!@#$%^&*()_\-+=<>?:"{}|,.\/;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘'，。、\w\r\n\s]/g;a?a.match(n)?parseInt(a)<=0?e([{message:"备案理由的长度不能超过400"}]):(this.$nextTick((function(){i.form.setFieldsValue({duration:parseInt(a)})})),e()):e([{message:"备案理由不能为空；不能全是特殊字符、符号、数字或三者组合"}]):e([{message:"请输入备案理由"}])},queryRecords:function(){var t=this,a={aaz217:this.params.aaz217,currMdtrtId:this.params.currMdtrtId,ake001:this.params.ake001,scene:this.params.scene};_.queryKeepRecords(a,(function(a){t.dataSource1=a.data.data,t.dataSource1&&(t.dataSource1=t.dataSource1.filter((function(t){return"1"===t.ape893})))}))},queryTemplateDatas:function(){var t=this,a={aaz307:this.params.admDeptCodg,akb020:this.params.fixmedinsCode};_.queryTemplateDatas(a,(function(a){t.dataSource2=a.data.data}))},useRowInfo:function(t,a){"history"==a?this.form.setFieldsValue({content:t.aaz560}):this.form.setFieldsValue(t)},handleSave:function(){var t=this;this.form.validateFields((function(a){a||(t.$emit("handleSave",t.form.getFieldsValue()),t.form.resetFields())}))},editContent:function(){var t=this;this.formEdit.validateFields((function(a){a||(t.Base.submit(null,{url:"/nightAudit/updateAe21",data:{content:t.formEdit.getFieldsValue().editContent,id:t.formEdit.getFieldsValue().id,medinscode:t.params.fixmedinsCode,oprcode:t.params.drCodg}}).then((function(a){t.warnText="更新成功!",t.warnVisible=!0,t.dataSource2=a.data.data,t.formEdit.resetFields(),t.editTempVisible=!1})).catch((function(a){t.warnText="更新失败!",t.warnVisible=!0})),t.warnVisible=!0)}))},handleClose:function(){this.current=["record"],this.form.resetFields(),this.$emit("handleClose")},handleEditClose:function(){this.formEdit.resetFields(),this.editTempVisible=!1},saveASTemp:function(){var t=this;this.form.validateFields((function(a){if(!a){var e={medinscode:t.params.fixmedinsCode,content:t.form.getFieldsValue().content,type:"3"};_.saveAe21(e,(function(a){t.dataSource2=a.data.data}))}}))},formatterType:function(t){var a=t.cellValue;return"1"===a?"公共模板":"2"===a?"科室模板":"个人模板"}}},A=F,O=(0,p.Z)(A,w,x,!1,null,"1c6f71b8",null),T=O.exports,P=e(362),I={aaz217:"825530808165385177",ae03List:[{aae100:"1",aae500:"4",code:"2",name:"检查",showalone:"1"},{aae100:"1",aae500:"4",code:"4",name:"服务",showalone:"0"},{aae100:"1",aae500:"4",code:"5",name:"杂项",showalone:"0"},{aae100:"1",aae500:"4",code:"1",name:"药品1",showalone:"1"},{aae100:"1",aae500:"4",code:"3",name:"耗材",showalone:"0"}],nonReimbursableMap:[{aaa167:"违反阶梯用药约束（可疑）",aka063:"1",aka065:"乙",akb065:0,akc225:8,akc226:0,ake001:"XA02BCA084A012010100453",ake002:"艾普拉唑肠溶片",ake003:"1",ake006:"艾普拉唑肠溶片",ape800:"1",ape804:0,ape805:4,ape893:"2",fail:"0",selfPayCheck:"1",sortId:"0",subProject:[{aaa167:"违反阶梯用药约束（可疑）",aac003:"白世成",aae036:"2023-04-08 18: 36: 15",aae386:"泌尿外科病区",aaz213:"825530808165385186",aaz263:"1809",aaz307:"210",aaz319:0x27147114878760,aka063:"1",akb065:12.8,akc049:"白世成",akc220:"221332367",akc225:8,akc226:2,akc310:"221332367",akc515:"201",ake001:"XA02BCA084A012010100453",ake002:"艾普拉唑肠溶片",ake003:"1",ake006:"艾普拉唑肠溶片",ape800:"1",ape804:12.8,ape805:2,ape893:"2",checkInfo:{aaz213:"825530808165385186",aaz217:"825530808165385177",akb020:"H41030500537",akc190:"10000000744",akc191:"1000000074",engAaz217:"825530808165385177",filterFlag:"1",ykz108:"eng_01_0000_02_05_01"},fail:"0",ykc610:"100000007415",ykz018:"限有十二指肠溃疡、反流性食管炎诊断患者的二线用药",ykz040:"999",ykz041:"admin",ykz259:"限有十二指肠溃疡、反流性食管炎诊断患者的二线用药"},{aaa167:"违反阶梯用药约束（可疑）",aac003:"张栋栋",aae036:"2023-04-08 18: 37: 15",aae386:"眼耳鼻咽喉科",aaz213:"825530808165385179",aaz263:"1952",aaz307:"518",aaz319:0x27147114878760,aka063:"1",akb065:12.8,akc049:"张栋栋",akc220:"221332367",akc225:8,akc226:2,akc310:"221332367",akc515:"201",ake001:"XA02BCA084A012010100453",ake002:"艾普拉唑肠溶片",ake003:"1",ake006:"艾普拉唑肠溶片",ape800:"1",ape804:12.8,ape805:2,ape893:"2",checkInfo:{aaz213:"825530808165385179",aaz217:"825530808165385177",akb020:"H41030500537",akc190:"10000000744",akc191:"1000000074",engAaz217:"825530808165385177",filterFlag:"1",ykz108:"eng_01_0000_02_05_01"},fail:"0",ykc610:"100000007416",ykz018:"限有十二指肠溃疡、反流性食管炎诊断患者的二线用药",ykz040:"999",ykz041:"admin",ykz259:"限有十二指肠溃疡、反流性食管炎诊断患者的二线用药"}],ykz018:"限有十二指肠溃疡、反流性食管炎诊断患者的二线用药",ykz259:"限有十二指肠溃疡、反流性食管炎诊断患者的二线用药"},{aaa167:"违反阶梯用药约束（可疑）",aka063:"2",aka065:"乙",akb065:0,akc225:8,akc226:0,ake001:"XJ01XBD198B014010100651",ake002:"注射用硫酸多黏菌素B2",ake003:"1",ake006:"注射用硫酸多黏菌素B2",ape800:"1",ape804:0,ape805:8,ape893:"2",fail:"0",selfPayCheck:"1",sortId:"0",subProject:[{aaa167:"违反阶梯用药约束（可疑）",aac003:"李帅阳",aae036:"2023-04-08 18: 36: 15",aae386:"泌尿外科病区",aaz213:"825530808165385202",aaz263:"1218",aaz307:"210",aaz319:0x27147114878760,aka063:"2",akb065:12.8,akc049:"李帅阳",akc220:"221332367",akc225:8,akc226:2,akc310:"221332367",akc515:"201",ake001:"XJ01XBD198B014010100651",ake002:"注射用硫酸多黏菌素B2",ake003:"1",ake006:"注射用硫酸多黏菌素B2",ape800:"1",ape804:12.8,ape805:2,ape893:"2",checkInfo:{aaz213:"825530808165385202",aaz217:"825530808165385177",akb020:"H41030500537",akc190:"10000000744",akc191:"1000000074",engAaz217:"825530808165385177",filterFlag:"1",ykz108:"eng_01_0000_02_05_01"},fail:"0",ykc610:"10000000741",ykz018:"限有药敏试验证据支持的多重耐药细菌感染的联合治疗",ykz040:"999",ykz041:"admin",ykz259:"限有药敏试验证据支持的多重耐药细菌感染的联合治疗"},{aaa167:"违反阶梯用药约束（可疑）",aac003:"李帅阳",aae036:"2023-04-08 18: 37: 15",aae386:"泌尿外科病区",aaz213:"825530808165385200",aaz263:"1218",aaz307:"210",aaz319:0x27147114878760,aka063:"2",akb065:12.8,akc049:"李帅阳",akc220:"221332367",akc225:8,akc226:2,akc310:"221332367",akc515:"201",ake001:"XJ01XBD198B014010100651",ake002:"注射用硫酸多黏菌素B2",ake003:"1",ake006:"注射用硫酸多黏菌素B2",ape800:"1",ape804:12.8,ape805:2,ape893:"2",checkInfo:{aaz213:"825530808165385200",aaz217:"825530808165385177",akb020:"H41030500537",akc190:"10000000744",akc191:"1000000074",engAaz217:"825530808165385177",filterFlag:"1",ykz108:"eng_01_0000_02_05_01"},fail:"0",ykc610:"10000000742",ykz018:"限有药敏试验证据支持的多重耐药细菌感染的联合治疗",ykz040:"999",ykz041:"admin",ykz259:"限有药敏试验证据支持的多重耐药细菌感染的联合治疗"},{aaa167:"违反阶梯用药约束（可疑）",aac003:"张栋栋",aae036:"2023-04-08 18: 38: 15",aae386:"眼耳鼻咽喉科",aaz213:"825530808165385203",aaz263:"1952",aaz307:"518",aaz319:0x27147114878760,aka063:"2",akb065:12.8,akc049:"张栋栋",akc220:"221332367",akc225:8,akc226:2,akc310:"221332367",akc515:"201",ake001:"XJ01XBD198B014010100651",ake002:"注射用硫酸多黏菌素B2",ake003:"1",ake006:"注射用硫酸多黏菌素B2",ape800:"1",ape804:12.8,ape805:2,ape893:"2",checkInfo:{aaz213:"825530808165385203",aaz217:"825530808165385177",akb020:"H41030500537",akc190:"10000000744",akc191:"1000000074",engAaz217:"825530808165385177",filterFlag:"1",ykz108:"eng_01_0000_02_05_01"},fail:"0",ykc610:"10000000743",ykz018:"限有药敏试验证据支持的多重耐药细菌感染的联合治疗",ykz040:"999",ykz041:"admin",ykz259:"限有药敏试验证据支持的多重耐药细菌感染的联合治疗"},{aaa167:"违反阶梯用药约束（可疑）",aac003:"张栋栋",aae036:"2023-04-08 18: 39: 15",aae386:"眼耳鼻咽喉科",aaz213:"825530808165385182",aaz263:"1952",aaz307:"518",aaz319:0x27147114878760,aka063:"2",akb065:12.8,akc049:"张栋栋",akc220:"221332367",akc225:8,akc226:2,akc310:"221332367",akc515:"201",ake001:"XJ01XBD198B014010100651",ake002:"注射用硫酸多黏菌素B2",ake003:"1",ake006:"注射用硫酸多黏菌素B2",ape800:"1",ape804:12.8,ape805:2,ape893:"2",checkInfo:{aaz213:"825530808165385182",aaz217:"825530808165385177",akb020:"H41030500537",akc190:"10000000744",akc191:"1000000074",engAaz217:"825530808165385177",filterFlag:"1",ykz108:"eng_01_0000_02_05_01"},fail:"0",ykc610:"10000000744",ykz018:"限有药敏试验证据支持的多重耐药细菌感染的联合治疗",ykz040:"999",ykz041:"admin",ykz259:"限有药敏试验证据支持的多重耐药细菌感染的联合治疗"}],ykz018:"限有药敏试验证据支持的多重耐药细菌感染的联合治疗",ykz259:"限有药敏试验证据支持的多重耐药细菌感染的联合治疗"},{aaa167:"违反药品限制使用条件约束（可疑）",aaz307:"2752",aka063:"11",aka065:"乙",akb065:0,akc225:8,akc226:0,ake001:"XB05AAR021B001060100538",ake002:"人血白蛋白x",ake003:"201",ake006:"人血白蛋白",ape800:"1",ape804:0,ape805:1,ape893:"2",fail:"0",selfPayCheck:"1",sortId:"0",subProject:[{aaa167:"违反药品限制使用条件约束（可疑）",aac003:"白永杰",aae036:"2022-04-06 19: 35: 15",aae386:"神经内科二病区",aaz213:"825530808165385190",aaz263:"0655",aaz307:"2752",aaz319:0x27147114878760,aka063:"11",akb065:8,akc049:"白永杰",akc220:"221332367",akc225:8,akc226:1,akc310:"221332367",akc515:"201",ake001:"XB05AAR021B001060100538",ake002:"人血白蛋白x",ake003:"201",ake006:"人血白蛋白",ape800:"1",ape804:8,ape805:1,ape893:"2",checkInfo:{aaz213:"825530808165385190",aaz217:"825530808165385177",akb020:"H41030500537",akc190:"10000000744",akc191:"1000000074",engAaz217:"825530808165385177",filterFlag:"1",ykz108:"eng_01_0000_02_05_01"},fail:"0",ykc610:"100000007421",ykz018:"限抢救、重症或因肝硬化、癌症引起胸腹水的患者，且白蛋白低于30g/L",ykz040:"999",ykz041:"admin",ykz259:"限抢救、重症或因肝硬化、癌症引起胸腹水的患者，且白蛋白低于30g/L"}],ykz018:"限抢救、重症或因肝硬化、癌症引起胸腹水的患者，且白蛋白低于30g/L",ykz259:"限抢救、重症或因肝硬化、癌症引起胸腹水的患者，且白蛋白低于30g/L"},{aaa167:"违反分解收费约束（违规）",aaz307:"2752",aka063:"11",aka065:"",akb065:0,akc225:8,akc226:0,ake001:"003301000140000-330100014",ake002:"特殊方法气管插管术x",ake003:"201",ake006:"特殊方法气管插管术",ape800:"2",ape804:0,ape805:1,ape893:"2",fail:"0",selfPayCheck:"1",sortId:"0",subProject:[{aaa167:"违反分解收费约束（违规）",aac003:"白永杰",aae036:"2022-04-06 19: 35: 15",aae386:"神经内科二病区",aaz213:"825530808165385184",aaz263:"0655",aaz307:"2752",aaz319:0x27147114878750,aka063:"11",akb065:8,akc049:"白永杰",akc220:"221332367",akc225:8,akc226:1,akc310:"221332367",akc515:"201",ake001:"003301000140000-330100014",ake002:"特殊方法气管插管术x",ake003:"201",ake006:"特殊方法气管插管术",ape800:"2",ape804:8,ape805:1,ape893:"2",checkInfo:{aaz213:"825530808165385184",aaz217:"825530808165385177",akb020:"H41030500537",akc190:"10000000744",akc191:"1000000074",engAaz217:"825530808165385177",filterFlag:"1",ykz108:"eng_01_0000_02_05_01"},fail:"0",ykc610:"100000007423",ykz018:"同一天内同时收取“气管插管术”与“特殊方法气管插管术”，则扣“特殊方法气管插管术”。",ykz040:"999",ykz041:"admin",ykz259:"同一天内同时收取“气管插管术”与“特殊方法气管插管术”，则扣“特殊方法气管插管术”。"}],ykz018:"同一天内同时收取“气管插管术”与“特殊方法气管插管术”，则扣“特殊方法气管插管术”。",ykz259:"同一天内同时收取“气管插管术”与“特殊方法气管插管术”，则扣“特殊方法气管插管术”。"},{aaa167:"",aka063:"1",aka065:"甲",akb065:0,akc225:8,akc226:0,ake001:"XA02AHT018A001020103372",ake002:"碳酸氢钠",ake003:"1",ake006:"碳酸氢钠",ape800:"",ape804:0,ape805:0,ape893:"1",fail:"1",selfPayCheck:"0",sortId:"1",subProject:[{aac003:"张栋栋",aae036:"2023-04-08 18: 36: 15",aae386:"眼耳鼻咽喉科",aaz213:"825530808165385196",aaz263:"1952",aaz307:"518",aka063:"1",akb065:12.8,akc049:"张栋栋",akc220:"221332367",akc225:8,akc226:2,akc310:"221332367",akc515:"201",ake001:"XA02AHT018A001020103372",ake002:"碳酸氢钠",ake003:"1",ake006:"碳酸氢钠",ape804:0,ape805:0,ape893:"1",checkInfo:{aaz213:"825530808165385196",aaz217:"825530808165385177",akb020:"H41030500537",akc190:"10000000744",akc191:"1000000074",engAaz217:"825530808165385177",filterFlag:"1",ykz108:"eng_01_0000_02_05_01"},fail:"1",ykc610:"100000007419",ykz018:"“消化道和代谢方面的药物-治疗胃酸相关类疾病的药物-抗酸药”口服制剂，在同一天内不能同时使用",ykz040:"999",ykz041:"admin"}],ykz018:"“消化道和代谢方面的药物-治疗胃酸相关类疾病的药物-抗酸药”口服制剂，在同一天内不能同时使用",ykz259:"“消化道和代谢方面的药物-治疗胃酸相关类疾病的药物-抗酸药”口服制剂，在同一天内不能同时使用"},{aaa167:"",aka063:"1",aka065:"乙",akb065:0,akc225:8,akc226:0,ake001:"XV03ACD216A006030183949",ake002:"地拉罗司分散片2",ake003:"1",ake006:"地拉罗司分散片2",ape800:"",ape804:0,ape805:0,ape893:"1",fail:"1",selfPayCheck:"0",sortId:"1",subProject:[{aac003:"张栋栋",aae036:"2023-04-08 18: 36: 15",aae386:"眼耳鼻咽喉科",aaz213:"825530808165385180",aaz263:"1952",aaz307:"518",aka063:"1",akb065:12.8,akc049:"张栋栋",akc220:"221332367",akc225:8,akc226:2,akc310:"221332367",akc515:"201",ake001:"XV03ACD216A006030183949",ake002:"地拉罗司分散片2",ake003:"1",ake006:"地拉罗司分散片2",ape804:0,ape805:0,ape893:"1",checkInfo:{aaz213:"825530808165385180",aaz217:"825530808165385177",akb020:"H41030500537",akc190:"10000000744",akc191:"1000000074",engAaz217:"825530808165385177",filterFlag:"1",ykz108:"eng_01_0000_02_05_01"},fail:"1",ykc610:"10000000748",ykz018:"【单行支付药品】需人工判断是否合规。",ykz040:"999",ykz041:"admin"},{aac003:"李帅阳",aae036:"2023-04-08 18: 36: 15",aae386:"泌尿外科病区",aaz213:"825530808165385192",aaz263:"1218",aaz307:"210",aka063:"1",akb065:12.8,akc049:"李帅阳",akc220:"221332367",akc225:8,akc226:2,akc310:"221332367",akc515:"201",ake001:"XV03ACD216A006030183949",ake002:"地拉罗司分散片2",ake003:"1",ake006:"地拉罗司分散片2",ape804:0,ape805:0,ape893:"1",checkInfo:{aaz213:"825530808165385192",aaz217:"825530808165385177",akb020:"H41030500537",akc190:"10000000744",akc191:"1000000074",engAaz217:"825530808165385177",filterFlag:"1",ykz108:"eng_01_0000_02_05_01"},fail:"1",ykc610:"10000000746",ykz018:"【单行支付药品】需人工判断是否合规。",ykz040:"999",ykz041:"admin"},{aac003:"张栋栋",aae036:"2023-04-08 18: 36: 15",aae386:"眼耳鼻咽喉科",aaz213:"825530808165385193",aaz263:"1952",aaz307:"518",aka063:"1",akb065:12.8,akc049:"张栋栋",akc220:"221332367",akc225:8,akc226:2,akc310:"221332367",akc515:"201",ake001:"XV03ACD216A006030183949",ake002:"地拉罗司分散片2",ake003:"1",ake006:"地拉罗司分散片2",ape804:0,ape805:0,ape893:"1",checkInfo:{aaz213:"825530808165385193",aaz217:"825530808165385177",akb020:"H41030500537",akc190:"10000000744",akc191:"1000000074",engAaz217:"825530808165385177",filterFlag:"1",ykz108:"eng_01_0000_02_05_01"},fail:"1",ykc610:"10000000747",ykz018:"【单行支付药品】需人工判断是否合规。",ykz040:"999",ykz041:"admin"},{aac003:"李帅阳",aae036:"2023-04-08 18: 36: 15",aae386:"泌尿外科病区",aaz213:"825530808165385199",aaz263:"1218",aaz307:"210",aka063:"1",akb065:12.8,akc049:"李帅阳",akc220:"221332367",akc225:8,akc226:2,akc310:"221332367",akc515:"201",ake001:"XV03ACD216A006030183949",ake002:"地拉罗司分散片2",ake003:"1",ake006:"地拉罗司分散片2",ape804:0,ape805:0,ape893:"1",checkInfo:{aaz213:"825530808165385199",aaz217:"825530808165385177",akb020:"H41030500537",akc190:"10000000744",akc191:"1000000074",engAaz217:"825530808165385177",filterFlag:"1",ykz108:"eng_01_0000_02_05_01"},fail:"1",ykc610:"10000000745",ykz018:"【单行支付药品】需人工判断是否合规。",ykz040:"999",ykz041:"admin"}],ykz018:"【单行支付药品】需人工判断是否合规。",ykz259:"【单行支付药品】需人工判断是否合规。"},{aaa167:"",aka063:"1",aka065:"乙",akb065:0,akc225:8,akc226:0,ake001:"XM01AXA165E001010109906",ake002:"盐酸氨基葡萄糖胶囊",ake003:"1",ake006:"盐酸氨基葡萄糖胶囊",ape800:"",ape804:0,ape805:0,ape893:"1",fail:"1",selfPayCheck:"0",sortId:"1",subProject:[{aac003:"李帅阳",aae036:"2023-04-08 18: 36: 15",aae386:"泌尿外科病区",aaz213:"825530808165385194",aaz263:"1218",aaz307:"210",aka063:"1",akb065:12.8,akc049:"李帅阳",akc220:"221332367",akc225:8,akc226:2,akc310:"221332367",akc515:"201",ake001:"XM01AXA165E001010109906",ake002:"盐酸氨基葡萄糖胶囊",ake003:"1",ake006:"盐酸氨基葡萄糖胶囊",ape804:0,ape805:0,ape893:"1",checkInfo:{aaz213:"825530808165385194",aaz217:"825530808165385177",akb020:"H41030500537",akc190:"10000000744",akc191:"1000000074",engAaz217:"825530808165385177",filterFlag:"1",ykz108:"eng_01_0000_02_05_01"},fail:"1",ykc610:"100000007413",ykz018:"限工伤保险",ykz040:"999",ykz041:"admin"},{aac003:"张栋栋",aae036:"2023-04-08 18: 37: 15",aae386:"眼耳鼻咽喉科",aaz213:"825530808165385181",aaz263:"1952",aaz307:"518",aka063:"1",akb065:12.8,akc049:"张栋栋",akc220:"221332367",akc225:8,akc226:2,akc310:"221332367",akc515:"201",ake001:"XM01AXA165E001010109906",ake002:"盐酸氨基葡萄糖胶囊",ake003:"1",ake006:"盐酸氨基葡萄糖胶囊",ape804:0,ape805:0,ape893:"1",checkInfo:{aaz213:"825530808165385181",aaz217:"825530808165385177",akb020:"H41030500537",akc190:"10000000744",akc191:"1000000074",engAaz217:"825530808165385177",filterFlag:"1",ykz108:"eng_01_0000_02_05_01"},fail:"1",ykc610:"100000007414",ykz018:"限工伤保险",ykz040:"999",ykz041:"admin"}],ykz018:"限工伤保险",ykz259:"限工伤保险"},{aaa167:"",aka063:"1",aka065:"乙",akb065:0,akc225:8,akc226:0,ake001:"XA12AXX128N001010704670",ake002:"小儿碳酸钙D3颗粒2",ake003:"1",ake006:"小儿碳酸钙D3颗粒2",ape800:"",ape804:0,ape805:0,ape893:"1",fail:"1",selfPayCheck:"0",sortId:"1",subProject:[{aac003:"张栋栋",aae036:"2023-04-08 18: 36: 15",aae386:"眼耳鼻咽喉科",aaz213:"825530808165385183",aaz263:"1952",aaz307:"518",aka063:"1",akb065:12.8,akc049:"张栋栋",akc220:"221332367",akc225:8,akc226:2,akc310:"221332367",akc515:"201",ake001:"XA12AXX128N001010704670",ake002:"小儿碳酸钙D3颗粒2",ake003:"1",ake006:"小儿碳酸钙D3颗粒2",ape804:0,ape805:0,ape893:"1",checkInfo:{aaz213:"825530808165385183",aaz217:"825530808165385177",akb020:"H41030500537",akc190:"10000000744",akc191:"1000000074",engAaz217:"825530808165385177",filterFlag:"1",ykz108:"eng_01_0000_02_05_01"},fail:"1",ykc610:"100000007411",ykz018:"限小儿佝偻病",ykz040:"999",ykz041:"admin"},{aac003:"李帅阳",aae036:"2023-04-08 18: 36: 15",aae386:"泌尿外科病区",aaz213:"825530808165385188",aaz263:"1218",aaz307:"210",aka063:"1",akb065:12.8,akc049:"李帅阳",akc220:"221332367",akc225:8,akc226:2,akc310:"221332367",akc515:"201",ake001:"XA12AXX128N001010704670",ake002:"小儿碳酸钙D3颗粒2",ake003:"1",ake006:"小儿碳酸钙D3颗粒2",ape804:0,ape805:0,ape893:"1",checkInfo:{aaz213:"825530808165385188",aaz217:"825530808165385177",akb020:"H41030500537",akc190:"10000000744",akc191:"1000000074",engAaz217:"825530808165385177",filterFlag:"1",ykz108:"eng_01_0000_02_05_01"},fail:"1",ykc610:"10000000749",ykz018:"限小儿佝偻病",ykz040:"999",ykz041:"admin"},{aac003:"张栋栋",aae036:"2023-04-08 18: 36: 15",aae386:"眼耳鼻咽喉科",aaz213:"825530808165385189",aaz263:"1952",aaz307:"518",aka063:"1",akb065:12.8,akc049:"张栋栋",akc220:"221332367",akc225:8,akc226:2,akc310:"221332367",akc515:"201",ake001:"XA12AXX128N001010704670",ake002:"小儿碳酸钙D3颗粒2",ake003:"1",ake006:"小儿碳酸钙D3颗粒2",ape804:0,ape805:0,ape893:"1",checkInfo:{aaz213:"825530808165385189",aaz217:"825530808165385177",akb020:"H41030500537",akc190:"10000000744",akc191:"1000000074",engAaz217:"825530808165385177",filterFlag:"1",ykz108:"eng_01_0000_02_05_01"},fail:"1",ykc610:"100000007412",ykz018:"限小儿佝偻病",ykz040:"999",ykz041:"admin"},{aac003:"李帅阳",aae036:"2023-04-08 18: 36: 15",aae386:"泌尿外科病区",aaz213:"825530808165385195",aaz263:"1218",aaz307:"210",aka063:"1",akb065:12.8,akc049:"李帅阳",akc220:"221332367",akc225:8,akc226:2,akc310:"221332367",akc515:"201",ake001:"XA12AXX128N001010704670",ake002:"小儿碳酸钙D3颗粒2",ake003:"1",ake006:"小儿碳酸钙D3颗粒2",ape804:0,ape805:0,ape893:"1",checkInfo:{aaz213:"825530808165385195",aaz217:"825530808165385177",akb020:"H41030500537",akc190:"10000000744",akc191:"1000000074",engAaz217:"825530808165385177",filterFlag:"1",ykz108:"eng_01_0000_02_05_01"},fail:"1",ykc610:"100000007410",ykz018:"限小儿佝偻病",ykz040:"999",ykz041:"admin"}],ykz018:"限小儿佝偻病",ykz259:"限小儿佝偻病"},{aaa167:"",aaz307:"2752",aka063:"11",aka065:"",akb065:0,akc225:8,akc226:0,ake001:"003301000130000-330100013",ake002:"气管插管术x",ake003:"201",ake006:"气管插管术x",ape800:"",ape804:0,ape805:0,ape893:"1",fail:"1",selfPayCheck:"0",sortId:"1",subProject:[{aac003:"白永杰",aae036:"2022-04-06 19: 35: 15",aae386:"神经内科二病区",aaz213:"825530808165385191",aaz263:"0655",aaz307:"2752",aka063:"11",akb065:8,akc049:"白永杰",akc220:"221332367",akc225:8,akc226:1,akc310:"221332367",akc515:"201",ake001:"003301000130000-330100013",ake002:"气管插管术x",ake003:"201",ake006:"气管插管术x",ape804:0,ape805:0,ape893:"1",checkInfo:{aaz213:"825530808165385191",aaz217:"825530808165385177",akb020:"H41030500537",akc190:"10000000744",akc191:"1000000074",engAaz217:"825530808165385177",filterFlag:"1",ykz108:"eng_01_0000_02_05_01"},fail:"1",ykc610:"100000007422",ykz018:"全身麻醉含：气管插管。",ykz040:"999",ykz041:"admin"}],ykz018:"全身麻醉含：气管插管。",ykz259:"全身麻醉含：气管插管。"}]},B=I,E=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("ta-modal",{attrs:{title:"单据审核",visible:t.visible,height:574,width:1e3,"mask-closable":!1,closable:!1,"cancel-button-props":{props:{disabled:!0}}},on:{ok:t.handleSave,cancel:t.handleClose}},[e("ta-big-table",{ref:"patientTable",staticClass:"table-style",staticStyle:{cursor:"pointer"},attrs:{border:"",stripe:"",resizable:"","highlight-hover-row":"","highlight-current-row":"","show-overflow":"",height:"520",size:"mini",data:t.tableData,"empty-text":"暂无数据"}},[e("ta-big-table-column",{attrs:{field:"aaa167","min-width":"120px",align:"left","header-align":"center",title:"审查项"}}),e("ta-big-table-column",{attrs:{field:"ykz018","min-width":"220px",align:"left","header-align":"center",title:"引导信息"}}),e("ta-big-table-column",{attrs:{field:"connotation",align:"center","min-width":"40px",title:"规则内涵"},scopedSlots:t._u([{key:"default",fn:function(a){var i=a.row;return[e("a",{on:{click:function(a){return t.queryRuleInfo(i)}}},[t._v("查看")])]}}])}),e("ta-big-table-column",{attrs:{field:"ykz022",align:"center","min-width":"180px",title:"备案信息","class-name":t.ykz022ClassChoice}}),e("ta-big-table-column",{attrs:{align:"center","min-width":"180px",title:"操作"},scopedSlots:t._u([{key:"default",fn:function(a){var i=a.row;return[e("div",{staticStyle:{display:"flex","justify-content":"center"}},t._l(t.oprateBtn,(function(a,n){return e("div",{key:n},[e("ta-button",{attrs:{type:i.currentOprate===a.key?"primary":"dashed",size:"small"},on:{click:function(e){return t.oprateEvent(i,a.key)}}},[t._v(" "+t._s(a.name)+" ")])],1)})),0)]}}])})],1),e("ta-modal",{attrs:{height:"70%",width:"85%","body-style":{paddingRight:0,paddingTop:"5px"},draggable:!0,"destroy-on-close":!0,footer:null},model:{value:t.knowledgeVisible,callback:function(a){t.knowledgeVisible=a},expression:"knowledgeVisible"}},[e("span",{staticStyle:{"font-weight":"normal","font-size":"14px","text-align":"center",height:"0",width:"100%"},attrs:{slot:"title"},slot:"title"},[t._v(" 规则内涵查看")]),e("div",{staticStyle:{"margin-top":"5px",height:"auto"}},[e("div",{staticClass:"knowledgeTitle"},[t._v(" 限制条件 ")]),e("div",{staticClass:"knowledgeRuleContent"},[t._v(" "+t._s(t.ykz018)+" ")])]),e("div",{staticStyle:{width:"100%",height:"calc( 100% - 50px )","margin-top":"5px"}},[e("div",{staticClass:"knowledgeTitle"},[t._v(" 规则内涵 ")]),e("div",{staticStyle:{display:"inline-block",width:"90%","vertical-align":"top","margin-left":"8px"}},[e("ta-collapse",{attrs:{id:"appH",accordion:!0},on:{change:t.changeNodeActivekey}},t._l(t.nodeList,(function(a,i){return e("ta-collapse-panel",{key:i,staticClass:"knowledgeContentTitle",attrs:{header:t.convertYkz061(a.ykz061)}},[e("div",{staticStyle:{"text-align":"right"}},[e("ta-input-search",{staticStyle:{width:"200px","margin-bottom":"1px"},attrs:{placeholder:"输入关键字搜索","allow-clear":""},on:{search:t.searchfn}})],1),e("div",[e("ta-big-table",{attrs:{size:"small","header-cell-style":function(){return{padding:"2px"}},data:t.nodeData},scopedSlots:t._u([{key:"bottomBar",fn:function(){return[e("ta-pagination",{staticStyle:{"text-align":"right"},attrs:{"show-size-changer":"",size:"small","default-page-size":200,"page-size-options":["30","50","100","200","500"],total:t.total,"data-source":t.nodeData},on:{"update:dataSource":function(a){t.nodeData=a},"update:data-source":function(a){t.nodeData=a},showSizeChange:t.onShowSizeChange,change:t.changePage},model:{value:t.current,callback:function(a){t.current=a},expression:"current"}})]},proxy:!0}],null,!0)},t._l(t.columns,(function(t,a){return e("ta-big-table-column",{key:a,attrs:{field:t.columnField,title:t.columnName}})})),1)],1)])})),1)],1)])])],1)},V=[],j=e(94628),R="nightAudit/",L={getBasePath:function(){return faceConfig.basePath+R},queryTableData:function(t,a){Base.submit(null,{url:R+"queryPatients",data:t,autoQs:!1},{successCallback:function(t){return a(t)}})},queryTableDataForTongJi:function(t,a){Base.submit(null,{url:R+"queryPatientList",data:t,autoQs:!1},{successCallback:function(t){return a(t)}})},updatePatientInfo:function(t,a){Base.submit(null,{url:R+"saveOperation",data:t,autoQs:!1},{successCallback:function(t){return a(t)}})},getRowDetails:function(t,a){Base.submit(null,{url:R+"queryAdviceDetail",data:t,autoQs:!1},{successCallback:function(t){return a(t)}})},queryKeepRecords:function(t,a){Base.submit(null,{url:R+"getRecordFormwork",data:t},{successCallback:function(t){return a(t)}})},queryTemplateDatas:function(t,a){Base.submit(null,{url:R+"getRecordFormwork",data:t},{successCallback:function(t){return a(t)}})},saveAe21:function(t,a){Base.submit(null,{url:R+"saveAe21",data:t,autoQs:!1},{successCallback:function(t){return a(t)}})},queryRuleInfo:function(t,a){Base.submit(null,{url:"/mtt/api/ruleSearch/queryRule",data:t,autoQs:!1},{successCallback:function(t){return a(t)}})},queryPagePackageRule:function(t,a){Base.submit(null,{url:"/mtt/api/ruleSearch/pagePackageContent",data:t,autoQs:!1},{successCallback:function(t){return a(t)}})},checkPromptState:function(t,a){Base.submit(null,{url:R+"checkPromptState",data:t,autoQs:!1},{successCallback:function(t){return a(t)}})},checkHandleState:function(t,a){Base.submit(null,{url:R+"checkHandleState",data:t,autoQs:!1},{successCallback:function(t){return a(t)}})}},M={name:"billAudit",props:{visible:{type:Boolean},billData:{type:Array}},data:function(){return{oprateBtn:[{name:"备案",key:"1"},{name:"取消医保",key:"3"}],tableData:[],knowledgeVisible:!1,ykz018:"",columns:[],nodeList:[],nodeData:[],currYkz022:"",current:0,pagesize:0,total:0}},watch:{visible:function(t){t&&(this.tableData=(0,s.Z)(this.billData))}},methods:{handleSave:function(){var t,a=(0,l.Z)(this.tableData);try{for(a.s();!(t=a.n()).done;){var e=t.value;if(!e.currentOprate)return void message.warn("还有未处理的疑点")}}catch(i){a.e(i)}finally{a.f()}this.tableData.forEach((function(t){t.ape893=t.currentOprate,"3"===t.ape893&&(t.ykz022=null)})),this.$emit("handleSave",this.tableData)},handleClose:function(){},oprateEvent:function(t,a){var e=this,i=this.$createElement;"3"===a&&(t.currentOprate=a),"1"===a&&(this.currYkz022=t.ykz022,this.$confirm({title:"备案理由",content:i("ta-input",{model:{value:e.currYkz022,callback:function(t){e.currYkz022=t}}}),icon:!1,onOk:function(){t.ykz022=e.currYkz022,e.currYkz022="",t.currentOprate=a,e.tableData=(0,s.Z)(e.tableData),e.$forceUpdate()}})),this.tableData=(0,s.Z)(this.tableData),this.$forceUpdate()},convertYkz061:function(t){var a=t.match(/^<(.*)>(.*)<\/\1>$/);return"".concat(a[1],":").concat(a[2])},ykz022ClassChoice:function(t){var a=t.row;return"3"===a.currentOprate?"ykz022-disable":""},queryRuleInfo:function(t){var a=this,e=t.ykz032,i=[];void 0!==e&&(i=e.split(",")),L.queryRuleInfo({ykz032s:i},(function(t){if(!1!==t.serviceSuccess){var e=t.data.resultData.ykz018,i="";if(void 0!==e){for(var n=0;n<e.length;n++)i+="【"+e[n]+"】";a.ykz018=i,a.nodeList=t.data.resultData.nodeInfoList}}else a.$message.error(t.errors[0].msg)})),this.knowledgeVisible=!0},searchfn:function(t,a){this.searchText=t,this.changePage(1,this.pagesize)},changeNodeActivekey:function(t){var a=this;if(!(0,j.Z)(t)){var e=t;if(!(0,j.Z)(e)){var i={};i.ykz042=this.nodeList[e].ykz042,i.pageParam={},i.pageParam.pageNumber=0,i.pageParam.pageSize=10,this.ykz042=i.ykz042,L.queryPagePackageRule(i,(function(t){if(!1!==t.serviceSuccess){a.columns=t.data.resultData.columnInfo;var e=t.data.resultData.pageData;a.nodeData=e.list,a.current=e.pageNum,a.pagesize=e.pageSize,a.total=e.total}else a.$message.error(t.errors[0].msg)}))}}},onShowSizeChange:function(t,a){this.changePage(t,a)},changePage:function(t,a){var e=this,i={};i.ykz042=this.ykz042,i.searchText=this.searchText,i.pageParam={},i.pageParam.pageNumber=t,i.pageParam.pageSize=a,L.queryPagePackageRule(i,(function(t){if(!1!==t.serviceSuccess){e.columns=t.data.resultData.columnInfo;var a=t.data.resultData.pageData;e.nodeData=a.list,e.current=a.pageNum,e.pagesize=a.pageSize,e.total=a.total}else e.$message.error(t.errors[0].msg)}))}}},q=M,$=(0,p.Z)(q,E,V,!1,null,null,null),H=$.exports,X=function(){var t=this,a=this,e=a.$createElement,i=a._self._c||e;return i("ta-modal",{attrs:{title:"医保诊断编码补充",visible:a.visible,height:574,width:1e3,"mask-closable":!1,"destroy-on-close":!0,draggable:!0},on:{ok:a.handleSave,cancel:a.handleClose}},[i("div",{attrs:{slot:"footer"},slot:"footer"},[i("ta-button",{style:{marginRight:8},attrs:{type:"primary"},on:{click:a.handleSave}},[a._v(" 保存 ")]),i("ta-button",{on:{click:a.handleClose}},[a._v(" 取消 ")])],1),i("ta-menu",{attrs:{"default-selected-keys":[0],mode:"horizontal"}},a._l(a.ykz042Arr,(function(t,e){return i("ta-menu-item",{key:e,on:{click:function(e){return a.queryTemplateDatas(t)}}},[i("span",[a._v(a._s(a.ykz010Arr[e]))])])})),1),i("ta-input-search",{staticStyle:{width:"260px"},attrs:{placeholder:"请输入诊断名称关键字筛选",size:"small"},on:{search:a.onSearch}}),i("ta-big-table",{staticStyle:{width:"100%"},attrs:{border:"","highlight-hover-row":"","show-overflow":"",data:a.dataSource2,height:"300",resizable:!0,"auto-resize":"",size:"mini"}},[i("ta-big-table-column",{attrs:{type:"seq",fixed:"left",width:"50px",title:" ",align:"center"}}),i("ta-big-table-column",{attrs:{field:"aka120",title:"ICD编码",width:"200px"}}),i("ta-big-table-column",{attrs:{field:"aka121",title:"医保诊断名称 ","min-width":"300px"}}),i("ta-big-table-column",{attrs:{field:"",title:"操作",width:"100px",align:"center"},scopedSlots:a._u([{key:"default",fn:function(t){var e=t.row;return[i("div",{directives:[{name:"show",rawName:"v-show",value:!a.rowInfoArr.includes(e.aka120),expression:"!rowInfoArr.includes(row.aka120)"}],staticClass:"opareteItem",on:{click:function(t){return a.useRowInfo(e)}}},[i("a",[a._v("补充")])]),i("div",{directives:[{name:"show",rawName:"v-show",value:a.rowInfoArr.includes(e.aka120),expression:"rowInfoArr.includes(row.aka120)"}],staticClass:"opareteItem",on:{click:function(t){return a.cancelRowInfo(e)}}},[i("a",{staticStyle:{color:"red"}},[a._v("取消")])])]}}])}),i("template",{slot:"bottomBar"},[i("ta-pagination",{ref:"gridPager",staticStyle:{"text-align":"right"},attrs:{defaultPageSize:200,pageSizeOptions:["30","50","100","200","500"],"data-source":a.dataSource2,params:a.knowledgePageParams,url:"nightAudit/getKnowledgeContentTableData"},on:{"update:dataSource":function(t){a.dataSource2=t},"update:data-source":function(t){a.dataSource2=t}}})],1)],2),i("ta-form",{attrs:{"auto-form-create":function(a){return t.form=a},layout:"horizontal","form-layout":!0,"label-width":"80px",col:{xs:1,sm:1,md:1,lg:1,xl:1,xxl:1}}},[i("ta-form-item",{attrs:{label:"引导信息","field-decorator-id":"ykz018",disabled:!0}},[i("ta-textarea",{attrs:{placeholder:"请输入引导信息",rows:3,"show-length":!0}})],1),i("ta-form-item",{attrs:{label:"已补诊断","field-decorator-id":"content","field-decorator-options":{rules:[{validator:a.validReason}]}}},[i("ta-textarea",{attrs:{placeholder:"请输入内容；不能为空；不能全是特殊字符、符号、数字或三者组合；最大长度不能超过400各字符",rows:3,"show-length":!0}})],1)],1)],1)},N=[],K={name:"keepOnRecord",components:{},props:{visible:{type:Boolean,default:!1},params:{type:Object,default:function(){return{}}},ykz042Arr:{type:[Array],default:function(){return[]}},ykz010Arr:{type:[Array],default:function(){return[]}}},data:function(){return{dataSource1:[],dataSource2:[],ykz042:"",content:[],aka120List:[],aka121List:[],serchFil:"",timePick:0,rowInfoArr:[]}},watch:{visible:{handler:function(t){var a=this;this.$nextTick((function(){a.queryTemplateDatas(a.ykz042Arr[0]),a.form.setFieldsValue({ykz018:a.params.ykz018,content:a.params.aaz560}),a.content=[]}))}}},mounted:function(){},methods:{moment:D(),validReason:function(t,a,e){var i=this,n=/[^`~!@#$%^&*()_\-+=<>?:"{}|,.\/;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘'，。、\w\r\n\s]/g;a?a.match(n)?parseInt(a)<=0?e([{message:"备案理由的长度不能超过400"}]):(this.$nextTick((function(){i.form.setFieldsValue({duration:parseInt(a)})})),e()):e([{message:"备案理由不能为空；不能全是特殊字符、符号、数字或三者组合"}]):e([{message:"请输入备案理由"}])},onSearch:function(t){var a=this;this.serchFil=t,this.$nextTick((function(){a.$refs.gridPager.loadData((function(t){}))}))},queryRecords:function(){L.queryKeepRecords({},(function(t){}))},queryTemplateDatas:function(t){this.ykz042=t,this.$refs.gridPager.loadData((function(t){}))},knowledgePageParams:function(){var t={fillDiagFlag:"0",searchText:this.serchFil,ykz042:this.ykz042};return t},useRowInfo:function(t){0===this.content.length&&this.content.push("已补充诊断："),this.rowInfoArr.push(t.aka120),this.aka120List.push(t.aka120),this.aka121List.push(t.aka121),this.content.push(t.aka120+" 【"+t.aka121+"】;"),this.form.setFieldsValue({content:this.content.join("")})},cancelRowInfo:function(t){var a=new Set(this.rowInfoArr);a.delete(t.aka120),this.rowInfoArr=(0,s.Z)(a);var e=new Set(this.content);e.delete(t.aka120+" 【"+t.aka121+"】;"),this.content=(0,s.Z)(e),1===this.content.length&&(this.content=[]),this.form.setFieldsValue({content:this.content.join("")})},handleSave:function(){var t=this.form.getFieldsValue();t.aka121=this.aka121List,t.aka120=this.aka120List,this.$emit("handleSave",t),this.form.resetFields()},handleClose:function(){this.serchFil="",this.form.resetFields(),this.$emit("handleClose")},fnReset:function(){this.rowInfoArr=[],this.content=[]}}},U=K,Z=(0,p.Z)(U,X,N,!1,null,"44a57520",null),Q=Z.exports,Y={name:"msAudit",components:{KeepOnRecordTwo:Q,BillAudit:H,TaTitle:c.Z,limitModal:k,keepOnRecord:T,medicineSearch:z,atientDetails:P["default"]},data:function(){var t=(0,s.Z)(B.nonReimbursableMap);return{leftCfg:{title:"",expand:!0,expandText:"",showBar:!0,showBorder:!1,barHeight:"67px",expandCallback:function(t){var a=document.getElementsByClassName("expand")[0].innerHTML;t?(this.barHeight="67px",a='<span > &nbsp;</span><i  aria-label="图标: left" class="anticon anticon-left"><svg viewBox="64 64 896 896" focusable="false" data-icon="left" width="1em" height="1em" fill="currentColor" aria-hidden="true" class=""><path d="M724 218.3V141c0-6.7-7.7-10.4-12.9-6.3L260.3 486.8a31.86 31.86 0 000 50.3l450.8 352.1c5.3 4.1 12.9.4 12.9-6.3v-77.3c0-4.9-2.3-9.6-6.1-12.6l-360-281 360-281.1c3.8-3 6.1-7.7 6.1-12.6z"></path></svg></i>'):(this.barHeight="100%",a='<div style="diplay:flex;flex-direction:column;"><i  aria-label="图标: right" class="anticon anticon-right"><svg viewBox="64 64 896 896" focusable="false" data-icon="right" width="1em" height="1em" fill="currentColor" aria-hidden="true" class=""><path d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"></path></svg></i><div style=\'writing-mode: vertical-rl; text-orientation: mixed;margin-top:5px\'>患者列表</div></div>'),document.getElementsByClassName("expand")[0].innerHTML=a}},defaultActiveKey:"2",dataSource:[{list:[]}],roleMOCK:"0",departList:[{id:0,name:"本科室"},{id:1,name:"所有科室"}],sceneList:[{id:0,name:"普通门诊"},{id:1,name:"门诊特慢病"}],currentscene:0,tipsType:[],tipsOptions:[],tipValue:"",excuteRes:[{label:"违规",value:"0"},{label:"可疑",value:"1"},{label:"通过",value:"2"}],oprateBtn:[{name:"备案",key:1},{name:"自费",key:2},{name:"补充诊断",key:4}],keepOnRecordVisible2:!1,ykz010Arr:[],ykz042Arr:[],checkedList:[],limitModalVisible:!1,currentDepart:1,currentTipsIndex:0,patientObjects:[],filterPatientObjects:[],originData:t,ae03List:[],billVisible:!1,billData:[],keepOnRecordVisible:!1,rowData:null,oprateHistoryVisible:!1,oprateHistoryList:[],medicineSearchVisible:!1,visible:!1,bfRecord:null,queryForm:{project_name:"",currentDeaprt:1,fee_type:"",check_res:["0","1","2"],tipValue:"",code:"all"},currentUserDepart:[],aaz217:"",bednoVisible:!1,mdtrtIdTitle:"就诊号",searchVal:"",currMdtrtId:"",scene:"opt",timer:null,hiFeesetlType:"",DepartOptions:[{label:"违规",value:"0"},{label:"可疑",value:"1"},{label:"通过",value:"2"}],dptCode:void 0,fixmedinsCode:"",admDeptCodg:"",drCodg:"",setlStas:""}},watch:{queryForm:{deep:!0,handler:function(t,a){this.filterData()}},ae03List:{deep:!0,handler:function(){this.handleTipsData()}},scene:{deep:!0,handler:function(t,a){t&&this.getPatient()}}},methods:{handleUpdateRow2:function(t){var a=this;if(this.keepOnRecordVisible2=!1,this.rowData.ape893="4",this.rowData.aaz560=t.content,this.rowData.aka121=t.aka121,this.rowData.aka120=t.aka120,this.rowData.currentOprate=2,"subProject"in this.rowData&&this.rowData.subProject.length){this.rowData.subProject.forEach((function(a){a.aaz560=t.content,a.aka121=t.aka121,a.aka120=t.aka120,a.ape893="4",a.currentOprate=2}));var e=this.rowData.subProject.map((function(t){return t.aaz213})),i=new Set(e);this.patientObjects.forEach((function(e){e.ake001===a.rowData.ake001&&(e.currentOprate=2,e.ape893="4",e.aaz560=t.content,e.aka121=t.aka121,e.aka120=t.aka120,e.subProject.forEach((function(a){i.has(a.aaz213)&&(a.aaz560=t.content,a.aka121=t.aka121,a.aka120=t.aka120,a.ape893="4",a.currentOprate=2)})))}))}else this.filterPatientObjects.forEach((function(e){if(e.ake001===a.rowData.ake001){t.content!==e.aaz560&&(e.aaz560="",a.$forceUpdate()),e.subProject.forEach((function(i){a.rowData.aaz213===i.aaz213&&(i.aaz560=t.content,i.aka121=t.aka121,i.aka120=t.aka120,i.ape893="4",i.currentOprate=2,i.ape893!==e.ape893&&(e.ape893="",e.currentOprate=-1))}));var i=e.subProject.map((function(t){return t.ape893}));a.isAllElementsEqual(i)&&(e.currentOprate=2)}})),this.patientObjects.forEach((function(e){if(e.ake001===a.rowData.ake001){t.content!==e.aaz560&&(e.aaz560="",a.$forceUpdate()),e.subProject.forEach((function(i){a.rowData.aaz213===i.aaz213&&(i.aaz560=t.content,i.aka121=t.aka121,i.aka120=t.aka120,i.ape893="4",i.currentOprate=2,i.ape893!==e.ape893&&(e.ape893="",e.currentOprate=-1))}));var i=e.subProject.map((function(t){return t.ape893}));a.isAllElementsEqual(i)&&(e.currentOprate=2)}}))},handleClose2:function(){this.keepOnRecordVisible2=!1,this.$refs.keeponrecord.fnReset()},formatterAge:function(t){var a=t.cellValue;if(a){var e=new Date(a),i=new Date,n=i.getFullYear()-e.getFullYear(),r=i.getMonth()>e.getMonth()||i.getMonth()===e.getMonth()&&i.getDate()>=e.getDate();return r||n--,n}},changeDeaprt:function(t){this.getPatient()},searchPatient:function(){var t=this;clearTimeout(this.timer),this.timer=setTimeout((function(){t.getPatient()}),500)},changeTAB:function(t){if(this.defaultActiveKey=t,this.scene="1"==t?"opt":"ipt","1"==t)if("1"==this.roleMOCK){this.mdtrtIdTitle="就诊号";for(var a=0;a<this.$refs.patientTable.length;a++)this.$refs.patientTable[a].hideColumn(this.$refs.patientTable[a].getColumnByField("bedno")),this.$refs.patientTable[a].hideColumn(this.$refs.patientTable[a].getColumnByField("setlStas"))}else("0"==this.roleMOCK||"2"==this.roleMOCK)&&(this.mdtrtIdTitle="就诊号",this.$refs.patientTable.hideColumn(this.$refs.patientTable.getColumnByField("bedno")),this.$refs.patientTable.hideColumn(this.$refs.patientTable.getColumnByField("setlStas")));else if("1"==this.roleMOCK){this.mdtrtIdTitle="住院号";for(var e=0;e<this.$refs.patientTable.length;e++)this.$refs.patientTable[e].showColumn(this.$refs.patientTable[e].getColumnByField("bedno")),this.$refs.patientTable[e].showColumn(this.$refs.patientTable[e].getColumnByField("setlStas"))}else("0"==this.roleMOCK||"2"==this.roleMOCK)&&(this.mdtrtIdTitle="住院号",this.$refs.patientTable.showColumn(this.$refs.patientTable.getColumnByField("bedno")),this.$refs.patientTable.showColumn(this.$refs.patientTable.getColumnByField("setlStas")))},handelLimit:function(){this.limitModalVisible=!0},handleOk:function(){},handleCancel:function(){this.limitModalVisible=!1},cellClick:function(t,a){var e=this,i=a.column,n=a.row;this.currMdtrtId=n.currMdtrtId,this.hiFeesetlType=n.hiFeesetlType,this.fixmedinsCode=n.fixmedinsCode,this.admDeptCodg=n.admDeptCodg,this.drCodg=n.drCodg,this.setlStas=n.setlStas,this.$nextTick((function(){"1"==e.roleMOCK?e.$refs.patientTable[t].setCurrentRow(n):e.$refs.patientTable.setCurrentRow(n)})),"操作"!==i.title?(this.patientObjects=[],this.filterPatientObjects=[],this.queryForm.currentDeaprt=1,this.queryForm.check_res=["0","1","2"],this.queryForm.code="all",this.ae03List=[],this.getAudit(n.mdtrtId,n.patnIptCnt)):this.gotoPatientDetaills(n)},filterData:function(){var t=this,a=JSON.stringify(this.patientObjects);a=JSON.parse(a),a=a.filter((function(a){return a.ake006.includes(t.queryForm.project_name)})),this.currentUserDepart,1!==this.queryForm.currentDeaprt&&a.forEach((function(a){a.subProject=a.subProject.filter((function(a){return t.currentUserDepart.includes(a.aaz307)}))})),"all"!==this.queryForm.code&&(a=a.filter((function(a){return a.aka063==t.queryForm.code}))),this.queryForm.check_res.length?a.forEach((function(a){a.subProject=a.subProject.filter((function(a){return t.ape800Filter(t.queryForm.check_res,a.ape800,a.fail)}))})):a=[],a=a.filter((function(t){return t.subProject.length})),a.forEach((function(t){t.akc226=0,t.subProject.forEach((function(a){t.akc226+=Number(a.akc226)}))})),this.filterPatientObjects=(0,s.Z)(a)},ape800Filter:function(t,a,e){var i;if(t.length)return i="1"===e?"2":"2"==a?"0":"1",t.includes(i)},selectDepart:function(t){this.currentDepart=t,this.queryForm.currentDeaprt=t},selectTips:function(t,a){this.tipValue="",this.currentTipsIndex=a,this.queryForm.code=t.code},handleChange:function(t){this.queryForm.code=t,this.currentTipsIndex=-1},headerCellStyle:function(t){t.column,t.columnIndex;return{backgroundColor:"#ffffff"}},oprateEvent:function(t,a){if(0==a)this.prepareMsg(t);else if(1==a)t.currentOprate=a,this.handleFee(t);else if(2==a){if(!t.ykz010||!t.ykz042)return void this.$message.warn("当前明细的诊断信息不完整，补充诊断不可选中！");this.rowData=t,this.ykz010Arr=t.ykz010.split(","),this.ykz042Arr=t.ykz042.split(","),this.keepOnRecordVisible2=!0}},handleFee:function(t){var a=this;if(t.ape893="2",t.aaz560="",t.currentOprate=1,"subProject"in t&&t.subProject.length){t.subProject.forEach((function(t){t.ape893="2",t.aaz560="",t.currentOprate=1}));var e=t.subProject.map((function(t){return t.aaz213})),i=new Set(e);this.patientObjects.forEach((function(a){a.ake001===t.ake001&&(a.currentOprate=1,a.subProject.forEach((function(t){i.has(t.aaz213)&&(t.ape893="2",t.aaz560="",t.currentOprate=1)})))}))}else this.filterPatientObjects.forEach((function(e){if(e.ake001===t.ake001){e.subProject.forEach((function(a){t.aaz213===a.aaz213&&(a.aaz560=info.content,a.ape893="2",a.aaz560="",a.currentOprate=1,a.ape893!==e.ape893&&(e.aaz560="",e.ape893="",e.currentOprate=-1))}));var i=e.subProject.map((function(t){return t.ape893}));a.isAllElementsEqual(i)&&(e.currentOprate=1)}})),this.patientObjects.forEach((function(e){if(e.ake001===t.ake001){e.subProject.forEach((function(a){t.aaz213===a.aaz213&&(a.aaz560=info.content,a.ape893="2",a.aaz560="",a.currentOprate=1,a.ape893!==e.ape893&&(e.aaz560="",e.ape893="",e.currentOprate=-1))}));var i=e.subProject.map((function(t){return t.ape893}));a.isAllElementsEqual(i)&&(e.currentOprate=1)}}));this.$forceUpdate()},isAllElementsEqual:function(t){for(var a=1;a<t.length;a++)if(t[a]!==t[0])return!1;return!0},prepareMsg:function(t){this.keepOnRecordVisible=!0,t.fixmedinsCode=this.fixmedinsCode,t.admDeptCodg=this.admDeptCodg,t.drCodg=this.drCodg,t.aaz217=this.aaz217,t.currMdtrtId=this.currMdtrtId,t.scene=this.scene,this.rowData=t},handleUpdateRow:function(t){var a=this;if(this.keepOnRecordVisible=!1,this.rowData.ape893="1",this.rowData.aaz560=t.content,this.rowData.currentOprate=0,"subProject"in this.rowData&&this.rowData.subProject.length){this.rowData.subProject.forEach((function(a){a.aaz560=t.content,a.ape893="1",a.currentOprate=0}));var e=this.rowData.subProject.map((function(t){return t.aaz213})),i=new Set(e);this.patientObjects.forEach((function(e){e.ake001===a.rowData.ake001&&(e.currentOprate=0,e.ape893="1",e.aaz560=t.content,e.subProject.forEach((function(a){i.has(a.aaz213)&&(a.aaz560=t.content,a.ape893="1",a.currentOprate=0)})))}))}else this.filterPatientObjects.forEach((function(e){if(e.ake001===a.rowData.ake001){t.content!==e.aaz560&&(e.aaz560="",a.$forceUpdate()),e.subProject.forEach((function(i){a.rowData.aaz213===i.aaz213&&(i.aaz560=t.content,i.ape893="1",i.currentOprate=0,i.ape893!==e.ape893&&(e.ape893="",e.currentOprate=-1))}));var i=e.subProject.map((function(t){return t.ape893}));a.isAllElementsEqual(i)&&(e.currentOprate=0)}})),this.patientObjects.forEach((function(e){if(e.ake001===a.rowData.ake001){t.content!==e.aaz560&&(e.aaz560="",a.$forceUpdate()),e.subProject.forEach((function(i){a.rowData.aaz213===i.aaz213&&(i.aaz560=t.content,i.ape893="1",i.currentOprate=0,i.ape893!==e.ape893&&(e.ape893="",e.currentOprate=-1))}));var i=e.subProject.map((function(t){return t.ape893}));a.isAllElementsEqual(i)&&(e.currentOprate=0)}}))},handleClose:function(){this.keepOnRecordVisible=!1},handleHisOk:function(){this.oprateHistoryVisible=!1},handleHisCancel:function(){this.oprateHistoryVisible=!1},billSave:function(t){var a=this,e={url:"/auditOnBackend/saveBillOperation",autoValid:!0,autoQs:!1,data:{aaz217:this.aaz217,akc190:this.currMdtrtId,akb020:this.fixmedinsCode,kf59List:t,scene:this.scene}},i={successCallback:function(t){"0"===t.data.data.next&&(a.patientObjects=[],a.filterPatientObjects=[]),a.billVisible=!1}};this.Base.submit(null,e,i)},billClose:function(){this.billVisible=!1},openHistoryModal:function(t){this.oprateHistoryVisible=!0,this.getHistoryop(this.currMdtrtId,t.ake001,t.ykc610)},cancelMedicineModal:function(){this.medicineSearchVisible=!1},toggleExpandChangeEvent:function(t){var a=t.row;t.expanded;this.$refs.xTable.toggleRowExpand(a)},cellClickEvent:function(t){var a=this,e=t.column,i=t.row;"操作"!==e.title&&(this.$refs.xTable.toggleRowExpand(i),"2"==this.roleMOCK&&this.$nextTick((function(){a.$refs.dTablechildren.hideColumn(a.$refs.dTablechildren.getColumnByField("op"))})))},gotoPatientDetaills:function(t){if(t.latestAaz217){var a="";switch(this.scene){case"opt":a="1";break;case"ipt":a="4";break;case"opsp":a="0";break}this.Base.openTabMenu({id:t.fixmedinsCode+t.latestAaz217,name:"【"+t.patnName+"】患者详情",url:"detailsQuery.html#/expenseDetailsForOther?akb020=".concat(t.fixmedinsCode,"&akc190=").concat(t.currMdtrtId,"&aaz217=").concat(t.latestAaz217,"&flag=").concat(a),refresh:!1})}},openAuditDetails:function(t){var a="";switch(this.scene){case"opt":a="1";break;case"ipt":a="4";break;case"opsp":a="0";break}t.hiFeesetlType=this.hiFeesetlType,t.ykz020="1",t.aae500=a,t.aaz217=this.aaz217,this.bfRecord=t,this.visible=!0},handleAuditCancel:function(){this.visible=!1},handleTipsData:function(){var t=this;this.tipsType=[],this.tipsOptions=[];var a=this.ae03List;a.forEach((function(a){"1"===a.showalone?t.tipsType.push(a):t.tipsOptions.push(a)})),this.tipsType.push({name:"全部",code:"all"}),this.currentTipsIndex=this.tipsType.length-1,this.tipsOptions.forEach((function(t){t.label=t.name,t.value=t.code}))},handleSubmitData:function(){if(this.patientObjects.length){var t=this.patientObjects.flatMap((function(t){return t.subProject})),a=t.filter((function(t){return!("0"===t.fail&&!t.ape893)}));if(0===a.length)return void message.warning("至少需要操作一条数据提交！");if(this.setlStas&&"0"==this.setlStas)return void message.warning("当前病人已结算,不允许再修改费用的报销状态！");this.handleAuditOprate()}},checkAllItemChecked:function(){var t,a=this.patientObjects.flatMap((function(t){return t.subProject})),e=(0,l.Z)(a);try{for(e.s();!(t=e.n()).done;){var i=t.value;if("0"===i.fail&&!i.ape893)return!1}}catch(n){e.e(n)}finally{e.f()}return!0},getRoleMockDepart:function(){var t=this,a={url:"/miimCommonRead/queryDepartRpcList",autoValid:!0},e={successCallback:function(a){t.DepartOptions=a.data.resultData}};this.Base.submit(null,a,e)},getPatient:function(){var t=this,a="";switch(this.roleMOCK){case"0":a="/auditOnBackend/getPatientList";break;case"1":a="/auditOnBackend/getPatientListWithAuthed";break;case"2":if(!this.dptCode)return;a="/auditOnBackend/getPatientListAll";break}this.Base.submit(null,{url:a,data:{searchVal:this.searchVal,scene:this.scene,dptCode:this.dptCode}},{successCallback:function(a){t.dataSource=a.data.data}})},getCurentDepartMsg:function(){var t=this,a={url:"/auditOnBackend/getCurrentUserDptList",autoValid:!0},e={successCallback:function(a){a.data.data.forEach((function(a){t.currentUserDepart.push(a.value)}))}};this.Base.submit(null,a,e)},getAudit:function(t,a){var e=this;return(0,r.Z)(regeneratorRuntime.mark((function i(){var n,r;return regeneratorRuntime.wrap((function(i){while(1)switch(i.prev=i.next){case 0:return n={url:"/auditOnBackend/doAudit",autoValid:!0,data:{mdtrtId:t,patientCount:a,scene:e.scene}},i.next=3,new Promise((function(t,a){var i={successCallback:function(a){return t(a)},errorCallback:function(t){return a(t)}};e.Base.submit(null,n,i)}));case 3:if(r=i.sent,!r.data.hasSuspect){i.next=8;break}return e.aaz217=r.data.aaz217,i.next=8,e.getAuditTableData(r.data.aaz217);case 8:case"end":return i.stop()}}),i)})))()},getAuditTableData:function(t){var a=this;return(0,r.Z)(regeneratorRuntime.mark((function e(){var i,n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return i={url:"/auditOnBackend/getAuditData",autoValid:!0,data:{akc190:a.currMdtrtId,aaz217:t,scene:a.scene}},n={successCallback:function(t){t.data.data.nonReimbursableMap.forEach((function(t){t.akc226=0;var e=new Set;t.subProject.forEach((function(i){i.ykz259||(i.ykz259=t.ykz259),t.akc226+=i.akc226,i.ape893&&e.add(i.ape893),i.oldape893=i.ape893,i.currentOprate=a.chooseCurrentOprate(i.ape893)})),1===e.size?t.ape893=e.values().next().value:t.ape893=null,t.currentOprate=a.chooseCurrentOprate(t.ape893)})),a.patientObjects=(0,s.Z)(t.data.data.nonReimbursableMap),a.filterPatientObjects=(0,s.Z)(t.data.data.nonReimbursableMap),a.ae03List=(0,s.Z)(t.data.data.ae03List);var e=t.data.data.kf59List;e&&e.length>0&&(a.billVisible=!0,a.billData=e)}},e.next=4,a.Base.submit(null,i,n);case 4:case"end":return e.stop()}}),e)})))()},chooseCurrentOprate:function(t){var a=-1;switch(t){case"1":a=0;break;case"2":a=1;break;case"4":a=2;break;default:a=-1;break}return a},handleAuditOprate:function(){var t=this,a={url:"/auditOnBackend/saveOperation",autoValid:!0,autoQs:!1,data:{aaz217:this.aaz217,akc190:this.currMdtrtId,akb020:this.fixmedinsCode,nonReimbursableMap:this.patientObjects,scene:this.scene}},e={successCallback:function(a){t.patientObjects=[],t.filterPatientObjects=[],Modal.success({title:"消息弹窗",content:"提交成功"})}};this.Base.submit(null,a,e)},getHistoryop:function(t,a,e){var i=this;this.oprateHistoryList=[];var n={url:"/auditOnBackend/getOprHistory",autoValid:!0,data:{aaz217:this.aaz217,currMdtrtId:t,ake001:a,scene:this.scene}},r={successCallback:function(t){i.oprateHistoryList=t.data.data,i.oprateHistoryList&&(i.oprateHistoryList=i.oprateHistoryList.filter((function(t){return e===t.ykc610})))}};this.Base.submit(null,n,r)},handleSearch:function(t){this.getRoleMockDepart(t)}},mounted:function(){var t=this;this.$nextTick((function(){t.roleMOCK=t.$route.query.roleMOCK,"2"==t.roleMOCK&&(t.$refs.xTable.hideColumn(t.$refs.xTable.getColumnByField("op")),t.getRoleMockDepart()),t.getCurentDepartMsg(),t.changeTAB(t.defaultActiveKey)}))}},J=Y,W=(0,p.Z)(J,i,n,!1,null,"6aa742b9",null),G=W.exports}}]);
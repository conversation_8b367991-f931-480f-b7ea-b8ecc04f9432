"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[5650],{88412:function(t,e,a){var l=a(26263),o=a(36766),n=a(1001),i=(0,n.Z)(o.Z,l.s,l.x,!1,null,"5e7ef0ae",null);e["Z"]=i.exports},5650:function(t,e,a){a.r(e),a.d(e,{default:function(){return v}});var l=function(){var t=this,e=this,a=e.$createElement,l=e._self._c||a;return l("div",{staticClass:"fit"},[l("ta-border-layout",{attrs:{"show-padding":!1}},[l("ta-tabs",{staticClass:"fit",attrs:{type:"card","tab-bar-gutter":10},on:{change:e.handleChangeTab},model:{value:e.activeKey,callback:function(t){e.activeKey=t},expression:"activeKey"}},[l("ta-tab-pane",{key:"qy",attrs:{tab:"全院","force-render":!0}},[l("ta-title",{attrs:{title:"指标阈值配置"}}),l("ta-form",{staticStyle:{width:"100%"},attrs:{"auto-form-create":function(e){return t.qyForm=e},layout:"horizontal","form-layout":!0}},[l("ta-form-item",{attrs:{"field-decorator-id":"aae141","label-col":{span:6},"wrapper-col":{span:15},require:{message:"必输项!"},span:8,"init-value":e.CollectionData("AAE141ZK")?e.CollectionData("AAE141ZK")[0].value:""}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("医保类型")]),l("ta-select",{attrs:{"collection-type":"AAE141ZK"}})],1),l("ta-form-item",{attrs:{"field-decorator-id":"aae140","label-col":{span:6},"wrapper-col":{span:15},span:8,"init-value":e.CollectionData("AAE140ZK")?e.CollectionData("AAE140ZK")[0].value:""}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("险种类型")]),l("ta-select",{staticStyle:{width:"100%"},attrs:{"default-active-first-option":!0,"allow-clear":!0,"collection-type":"AAE140ZK"}})],1),l("ta-form-item",{attrs:{"field-decorator-id":"id","label-col":{span:7},"wrapper-col":{span:15},span:8}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("指标名称")]),l("ta-select",{staticStyle:{width:"100%"},attrs:{"allow-clear":!0,options:e.indicatorsData}})],1),l("ta-form-item",{attrs:{"field-decorator-id":"calcumethod","label-col":{span:6},"wrapper-col":{span:15},span:8}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("计算方式")]),l("ta-select",{staticStyle:{width:"100%"},attrs:{"allow-clear":!0,options:[{value:"0",label:"无限制"},{value:"1",label:"按个人"},{value:"2",label:"按月"},{value:"3",label:"按季度"},{value:"4",label:"按年"}]}})],1),l("ta-button",{staticStyle:{"margin-left":"calc(8.5%)"},attrs:{icon:"search",type:"primary"},on:{click:function(t){return e.handleQuery("qy")}}},[e._v(" 查询 ")]),l("ta-button",{attrs:{icon:"plus"},on:{click:e.handleAddIndicatorsWarnConfig}},[e._v(" 新增 ")])],1),l("div",{staticClass:"fit",staticStyle:{height:"calc(100% - 190px)"}},[l("ta-form",{staticStyle:{height:"100%"},attrs:{"auto-form-create":function(e){t.form=e}}},[l("ta-table",{ref:"qyTableRef",attrs:{columns:e.qyTableColumns,"data-source":e.qyTableData,bordered:!0,size:"small",scroll:{y:"100%"},"custom-row":e.fnCustomRow1},on:{"update:columns":function(t){e.qyTableColumns=t}},scopedSlots:e._u([{key:"calcumethod",fn:function(t,a,o){return l("span",{},[e._v(e._s(e.getCalcumethodLabel(t)))])}},{key:"warnvalue",fn:function(t,a){return l("ta-table-edit",{attrs:{"edit-form":e.form,type:"inputNumber",rules:[{validator:e.validateWarnValue}]},on:{change:e.handleChangeData}})}},{key:"aae100",fn:function(t,a){return l("ta-table-edit",{attrs:{"edit-form":e.form,type:"select",option:e.aae100List},on:{change:e.handleChangeData}})}},{key:"effectivedate",fn:function(t,a){return l("ta-table-edit",{attrs:{"edit-form":e.form,type:"rangePicker"},on:{change:e.handleChangeData1}})}}])},[l("span",{attrs:{slot:"warnvalueTitle"},slot:"warnvalueTitle"},[e._v("指标值"),l("ta-icon",{attrs:{type:"edit"}})],1),l("span",{attrs:{slot:"aae100Title"},slot:"aae100Title"},[e._v("有效标志"),l("ta-icon",{attrs:{type:"edit"}})],1),l("span",{attrs:{slot:"effectiveDateTitle"},slot:"effectiveDateTitle"},[e._v("有效日期"),l("ta-icon",{attrs:{type:"edit"}})],1)])],1)],1),l("ta-button",{staticStyle:{position:"absolute",right:"10px",bottom:"0px"},attrs:{type:"primary",icon:"download"},on:{click:function(t){return e.handleExportData(2)}}},[e._v(" 导出 ")]),l("ta-pagination",{ref:"qyPageRef",staticStyle:{"margin-top":"8px",right:"0px",position:"absolute",bottom:"0px","margin-right":"150px"},attrs:{"show-size-changer":"","show-quick-jumper":"","data-source":e.qyTableData,params:e.pageParams,"default-page-size":10,"page-size-options":["10","20","40"],url:"indicatorsWarnConfig/queryByPage"},on:{"update:dataSource":function(t){e.qyTableData=t},"update:data-source":function(t){e.qyTableData=t}}})],1),l("ta-tab-pane",{key:"ks",attrs:{tab:"科室","force-render":!1}},[l("ta-border-layout",{attrs:{layout:{left:"20%",right:"80%"},"show-border":!1,"show-padding":!1,"left-cfg":{showBorder:!1}}},[l("div",{staticClass:"fit",staticStyle:{height:"90%"},attrs:{slot:"left"},slot:"left"},[l("ta-title",{attrs:{title:"科室列表"}}),l("ta-table",{key:(new Date).getTime()+"ks",ref:"departTableRef",attrs:{columns:e.departTableColumns,"data-source":e.departTableData,bordered:!0,size:"small",scroll:{y:"100%"},"custom-row":e.fnCustomRow},on:{"update:columns":function(t){e.departTableColumns=t}},scopedSlots:e._u([{key:"filterDropdown",fn:function(t){var a=t.setSelectedKeys,o=t.selectedKeys,n=t.confirm,i=t.clearFilters;return l("div",{staticClass:"custom-filter-dropdown"},[l("ta-input",{ref:"searchInput",attrs:{value:o[0]},on:{change:function(t){return a(t.target.value?[t.target.value]:[])},pressEnter:function(){return e.handleSearch(o,n)}}}),l("ta-button",{attrs:{type:"primary"},on:{click:function(){return e.handleSearch(o,n)}}},[e._v(" 搜索 ")]),l("ta-button",{on:{click:function(){return e.handleReset(i)}}},[e._v(" 重置 ")])],1)}},{key:"filterIcon",fn:function(t){return l("ta-icon",{style:{color:t?"#108ee9":"#aaa"},attrs:{type:"smile-o"}})}},{key:"customRender",fn:function(t){return[e.searchText?l("span",[e._l(t.split(new RegExp("(?<="+e.searchText+")|(?="+e.searchText+")","i")),(function(t,a){return[t.toLowerCase()===e.searchText.toLowerCase()?l("span",{key:a,staticClass:"highlight"},[e._v(e._s(t))]):[e._v(e._s(t))]]}))],2):[e._v(" "+e._s(t)+" ")]]}}])}),l("ta-pagination",{ref:"departPageRef",staticStyle:{"margin-top":"8px",right:"0px",position:"absolute",bottom:"0px","margin-right":"50px"},attrs:{"data-source":e.departTableData,params:e.departPageParams,simple:"","default-page-size":20,url:"indicatorsWarnConfig/queryDepart",size:"small"},on:{"update:dataSource":function(t){e.departTableData=t},"update:data-source":function(t){e.departTableData=t}}})],1),l("div",{staticClass:"fit",attrs:{slot:"right"},slot:"right"},[l("ta-title",{attrs:{title:e.ksTitle}}),l("ta-form",{staticStyle:{width:"100%"},attrs:{"auto-form-create":function(e){return t.ksForm=e},layout:"horizontal","form-layout":!0}},[l("ta-form-item",{attrs:{"field-decorator-id":"aae141","label-col":{span:9},"wrapper-col":{span:15},require:{message:"必输项!"},span:8,"init-value":e.CollectionData("AAE141ZK")?e.CollectionData("AAE141ZK")[0].value:""}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("医保类型")]),l("ta-select",{staticStyle:{width:"100%"},attrs:{"collection-type":"AAE141ZK"}})],1),l("ta-form-item",{attrs:{"field-decorator-id":"aae140","label-col":{span:9},"wrapper-col":{span:15},span:8,"init-value":e.CollectionData("AAE140ZK")?e.CollectionData("AAE140ZK")[0].value:""}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("险种类型")]),l("ta-select",{staticStyle:{width:"100%"},attrs:{"allow-clear":!0,"collection-type":"AAE140ZK"}})],1),l("ta-form-item",{attrs:{"field-decorator-id":"id","label-col":{span:9},"wrapper-col":{span:15},span:8}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("指标名称")]),l("ta-select",{staticStyle:{width:"100%"},attrs:{"allow-clear":!0,options:e.indicatorsData}})],1),l("ta-form-item",{attrs:{"field-decorator-id":"calcumethod","label-col":{span:9},"wrapper-col":{span:15},span:8}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("计算方式")]),l("ta-select",{staticStyle:{width:"100%"},attrs:{"allow-clear":!0,options:[{value:"0",label:"无限制"},{value:"1",label:"按个人"},{value:"2",label:"按月"},{value:"3",label:"按季度"},{value:"4",label:"按年"}]}})],1),l("ta-button",{staticStyle:{"margin-left":"calc(12.5%)"},attrs:{icon:"search",type:"primary"},on:{click:function(t){return e.handleQuery("ks")}}},[e._v(" 查询 ")]),l("ta-button",{directives:[{name:"show",rawName:"v-show",value:e.enableKsAdd,expression:"enableKsAdd"}],attrs:{icon:"plus"},on:{click:e.handleAddIndicatorsWarnConfig}},[e._v(" 新增 ")])],1),l("div",{staticClass:"fit",staticStyle:{height:"calc(100% - 190px)"}},[l("ta-form",{staticClass:"fit",staticStyle:{height:"100%"},attrs:{"auto-form-create":function(e){t.form1=e}}},[l("ta-table",{ref:"ksTableRef",staticStyle:{height:"100%"},attrs:{"custom-row":e.fnCustomRow1,columns:e.ksTableColumns,"data-source":e.ksTableData,bordered:!0,size:"small",scroll:{y:"100%",x:1200}},on:{"update:columns":function(t){e.ksTableColumns=t}},scopedSlots:e._u([{key:"calcumethod",fn:function(t,a,o){return l("span",{},[e._v(e._s(e.getCalcumethodLabel(t)))])}},{key:"warnvalue",fn:function(t,a){return l("ta-table-edit",{attrs:{"edit-form":e.form1,rules:[{validator:e.validateWarnValue}],type:"inputNumber"},on:{change:e.handleChangeData}})}},{key:"aae100",fn:function(t,a){return l("ta-table-edit",{attrs:{type:"select",option:e.aae100List},on:{change:e.handleChangeData}})}},{key:"effectivedate",fn:function(t,a){return l("ta-table-edit",{attrs:{"edit-form":e.form1,type:"rangePicker"},on:{change:e.handleChangeData1}})}}])},[l("span",{attrs:{slot:"warnvalueTitle"},slot:"warnvalueTitle"},[e._v("指标值"),l("ta-icon",{attrs:{type:"edit"}})],1),l("span",{attrs:{slot:"aae100Title"},slot:"aae100Title"},[e._v("有效标志"),l("ta-icon",{attrs:{type:"edit"}})],1),l("span",{attrs:{slot:"effectiveDateTitle"},slot:"effectiveDateTitle"},[e._v("有效日期"),l("ta-icon",{attrs:{type:"edit"}})],1)])],1)],1),l("ta-button",{staticStyle:{position:"absolute",right:"10px",bottom:"0px"},attrs:{type:"primary",icon:"download"},on:{click:function(t){return e.handleExportData(2)}}},[e._v(" 导出 ")]),l("ta-pagination",{ref:"ksPageRef",staticStyle:{"margin-top":"8px",right:"0px",position:"absolute",bottom:"0px","margin-right":"150px"},attrs:{"show-size-changer":"","show-quick-jumper":"","data-source":e.ksTableData,params:e.pageParams,"default-page-size":10,"page-size-options":["10","20","40"],url:"indicatorsWarnConfig/queryByPage"},on:{"update:dataSource":function(t){e.ksTableData=t},"update:data-source":function(t){e.ksTableData=t}}})],1)])],1)],1)],1),l("div",{attrs:{id:"addDrawer"}},[l("ta-drawer",{staticStyle:{height:"calc(100% - 10px)",overflow:"'auto'",paddingBottom:"53px"},attrs:{"get-container":e.getContainer,width:"400",placement:"right",closable:!1,visible:e.visible},on:{close:e.handleCancel}},[l("ta-title",{staticStyle:{"margin-top":"-20px"},attrs:{title:e.addTitle}}),l("ta-form",{attrs:{"auto-form-create":function(e){return t.addForm=e},layout:"horizontal","form-layout":!0}},[l("ta-form-item",{attrs:{"field-decorator-id":"aae141","label-col":{span:9},"wrapper-col":{span:15},require:{message:"必输项!"},span:24,"init-value":e.CollectionData("AAE141ZK")?e.CollectionData("AAE141ZK")[0].value:""}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("医保类型")]),l("ta-select",{attrs:{"collection-type":"AAE141ZK","allow-clear":!0}})],1),l("ta-form-item",{attrs:{"field-decorator-id":"aae140","label-col":{span:9},"wrapper-col":{span:15},span:24,require:{message:"必输项!"},"init-value":e.CollectionData("AAE140ZK")?e.CollectionData("AAE140ZK")[0].value:""}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("险种类型")]),l("ta-select",{staticStyle:{width:"100%"},attrs:{"collection-type":"AAE140ZK"}})],1),l("ta-form-item",{attrs:{"field-decorator-id":"id","label-col":{span:9},"wrapper-col":{span:15},span:24,require:{message:"必输项!"}}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("指标名称")]),l("ta-select",{staticStyle:{width:"100%"},attrs:{"allow-clear":!1,options:e.indicatorsData}})],1),l("ta-form-item",{attrs:{"field-decorator-id":"value","label-col":{span:9},"wrapper-col":{span:15},span:24,require:{message:"必输项!"},"field-decorator-options":{rules:[{validator:e.validateWarnValue}]}}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("阈值")]),l("ta-input-number",{staticStyle:{width:"100%"}})],1),l("ta-form-item",{attrs:{"field-decorator-id":"calcumethod","label-col":{span:9},"wrapper-col":{span:15},span:24,require:{message:"必输项!"}}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("计算方式")]),l("ta-select",{staticStyle:{width:"100%"},attrs:{"allow-clear":!1,options:[{value:"0",label:"无限制"},{value:"1",label:"按个人"},{value:"2",label:"按月"},{value:"3",label:"按季度"},{value:"4",label:"按年"}]}})],1),l("ta-form-item",{attrs:{"field-decorator-id":"effectivedate","label-col":{span:9},"wrapper-col":{span:15},span:24,require:{message:"必输项!"}}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("有效日期")]),l("ta-range-picker",{staticStyle:{width:"100%"},attrs:{"disabled-date":e.disabledDateBefore}})],1)],1),l("div",{attrs:{slot:"footer"},slot:"footer"},[l("ta-button",{style:{marginRight:8},on:{click:e.handleCancel}},[e._v(" 取消 ")]),l("ta-button",{attrs:{type:"primary"},on:{click:e.handleOk}},[e._v(" 确定 ")])],1)],1)],1)],1)},o=[],n=a(95082),i=a(66347),r=(a(32564),a(88412)),s=a(36797),c=a.n(s),d=[],u=[],f=[],p={name:"keyIndicators",components:{TaTitle:r.Z},data:function(){var t=this,e=[{title:"指标预警对象类型",dataIndex:"objtype",align:"center",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"指标编码",dataIndex:"yzb001",align:"center",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"医保类型",dataIndex:"aae141",align:"center",width:100,overflowTooltip:!0,collectionType:"AAE141ZK",customHeaderCell:this.fnCustomHeaderCell},{title:"险种类型",dataIndex:"aae140",align:"center",collectionType:"AAE140ZK",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"指标名称",dataIndex:"yzb004",align:"center",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"指标类别",dataIndex:"yzb003",align:"center",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"计算方式",dataIndex:"calcumethod",align:"center",width:100,overflowTooltip:!0,scopedSlots:{customRender:"calcumethod"},customHeaderCell:this.fnCustomHeaderCell},{slots:{title:"warnvalueTitle"},dataIndex:"warnvalue",align:"center",width:100,scopedSlots:{customRender:"warnvalue"},customHeaderCell:this.fnCustomHeaderCell},{slots:{title:"aae100Title"},dataIndex:"aae100",align:"center",width:100,scopedSlots:{customRender:"aae100"},customHeaderCell:this.fnCustomHeaderCell},{slots:{title:"effectiveDateTitle"},dataIndex:"effectivedate",align:"center",width:200,scopedSlots:{customRender:"effectivedate"},customHeaderCell:this.fnCustomHeaderCell}],a=[{title:"科室编码",dataIndex:"aaz307",align:"center",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"科室名称",dataIndex:"aae386",align:"center",width:100,scopedSlots:{filterDropdown:"filterDropdown",filterIcon:"filterIcon",customRender:"customRender"},onFilter:function(t,e){return e.aae386.toLowerCase().includes(t.toLowerCase())},onFilterDropdownVisibleChange:function(e){e&&setTimeout((function(){t.$refs.searchInput.focus()}))},overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell}];return{activeKey:"qy",qyTableColumns:[].concat(e),ksTableColumns:[].concat(e),departTableColumns:a,departTableData:f,qyTableData:d,ksTableData:u,indicatorsData:[],aae100List:[{label:"有效",value:"1"},{label:"无效",value:"0"}],objtype:"all",objcode:"1",objname:"全院",rowColor:"",curClickRow:"",qyDateFlag:"M",defaultDateMonth:this.Base.getMoment((new Date).toISOString().slice(0,7),"YYYY-MM"),defaultDateQuarter:this.Base.getMoment(c()().year()+"-"+c()().quarter()+"季度","YYYY-Q季度"),defaultDateYear:this.Base.getMoment((new Date).toISOString().slice(0,4),"YYYY"),ksDateFlag:"M",addDateFlag:"M",visible:!1,addTitle:"",ksTitle:"指标阈值配置",enableKsAdd:!1,searchText:"",moment:c()}},watch:{qyTableData:function(t,e){var a,l=(0,i.Z)(t);try{for(l.s();!(a=l.n()).done;){var o=a.value;o.effectivedate=[o.begindate,o.enddate]}}catch(n){l.e(n)}finally{l.f()}},ksTableData:function(t,e){var a,l=(0,i.Z)(t);try{for(l.s();!(a=l.n()).done;){var o=a.value;o.effectivedate=[o.begindate,o.enddate]}}catch(n){l.e(n)}finally{l.f()}}},mounted:function(){this.fnInitData()},methods:{fnCustomRow1:function(t,e){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px"}}},fnCustomHeaderCell:function(t){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px",fontWeight:"bold"}}},handleChangeDateSelect:function(t){!0!==this.visible?"all"===this.objtype?this.qyDateFlag=t.target.value:this.ksDateFlag=t.target.value:this.addDateFlag=t.target.value},handleChangeTab:function(t){var e=this;"qy"===t?(this.objtype="all",this.objcode="1",this.objname="全院"):setTimeout((function(){e.$refs.ksTableRef.hideColumns(["objtype","yzb001","yzb003"]),e.$refs.departTableRef.hideColumns(["aaz307"]),e.objtype="depart",e.objname="",e.objcode="",e.curClickRow&&(e.objcode=e.curClickRow.aaz307,e.objname=e.curClickRow.aae386,e.ksTitle="指标阈值配置【"+e.curClickRow.aae386+"】"),0===e.departTableData.length&&e.$refs.departPageRef.loadData()}),30)},validateWarnValue:function(t,e,a){e<=0?a("指标值必须大于0!"):a()},handleChangeData:function(t){var e=this,a=t.newData,l=t.record,o=t.columnKey;l[o]=a;var i=(0,n.Z)({},l);i.value=i.warnvalue,this.Base.submit(null,{url:"indicatorsWarnConfig/updateIndicatorsWarnConfig",data:i,autoValid:!0},{successCallback:function(t){e.$message.success("更新成功")},failCallback:function(t){e.$message.error("更新失败!")}})},handleChangeData1:function(t){var e=this,a=t.newData,l=t.record,o=t.columnKey;l[o]=a,l.beginDate=a[0],l.endDate=a[1];var i=(0,n.Z)({},l);i.value=i.warnvalue,this.Base.submit(null,{url:"indicatorsWarnConfig/updateIndicatorsWarnConfig",data:i,autoValid:!0},{successCallback:function(t){e.$message.success("更新成功")},failCallback:function(t){e.$message.error("更新失败!")}})},handleAddIndicatorsWarnConfig:function(){this.addTitle="all"==this.objtype?"新增全院预警指标":"新增预警指标【"+this.objname+"】",this.visible=!0},fnCustomRow:function(t,e){var a=this;return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px",fontWeight:"bold",backgroundColor:t===this.curClickRow?this.rowColor:""},props:{draggable:!0},on:{click:function(e){a.enableKsAdd=!0,a.objname=t.aae386,a.ksTitle="指标阈值配置【"+t.aae386+"】"}}}},handleExportData:function(t){var e="";if(e="qy"===this.activeKey?"医院指标预警参数配置.xls":"医院指标预警参数配置-".concat(this.objname,".xls"),2!==t){var a=[],l=this.qyTableColumns,o=[];o="qy"===this.activeKey?this.qyTableData:this.ksTableData;var r,s=0,c=(0,i.Z)(l);try{for(c.s();!(r=c.n()).done;){var d=r.value;if(s++,s>5)break;a.push({header:d.title,key:d.dataIndex,width:20})}}catch(f){c.e(f)}finally{c.f()}a.push({header:"指标值",key:"warnvalue",width:20}),a.push({header:"有效标志",key:"aae100",width:20}),a.push({header:"有效日期",key:"effectivedate",width:20});var u={sheets:[{name:"worksheet1",column:{complex:!1,columns:a},rows:o,codeList:[{codeType:"AAE141ZK",columnKey:"aae141"},{codeType:"AAE140ZK",columnKey:"aae140"},{columnKey:"aae100",customCollection:function(t,e){"1"===t.value?t.value="有效":t.value="无效"}},{columnKey:"calcumethod",customCollection:function(t,e){for(var a=[{value:"0",label:"无限制"},{value:"1",label:"按个人"},{value:"2",label:"按月"},{value:"3",label:"按季度"},{value:"4",label:"按年"}],l=0,o=a;l<o.length;l++){var n=o[l];if(n.value===t.value){t.value=n.label;break}}}},{columnKey:"effectivedate",customCollection:function(t,e){t.value="".concat(t.value[0],"~").concat(t.value[1])}}]}]};this.Base.generateExcel(u)}else Base.downloadFile({url:"indicatorsWarnConfig/exportData",options:(0,n.Z)({},this.pageParams()),type:"application/excel",fileName:e}).then((function(t){})).catch((function(t){}))},disabledDate:function(t){return t>c()().startOf("day")},disabledDateBefore:function(t){return t<c()().startOf("day")},pageParams:function(){var t={};"all"===this.objtype?(t=this.qyForm.getFieldsValue(),t.dateFlag=this.qyDateFlag):(t=this.ksForm.getFieldsValue(),t.dateFlag=this.ksDateFlag);var e={};return e.objtype=this.objtype,"depart"===this.objtype&&(e.objcode=this.objcode,e.objname=this.objname),e.aae141=t.aae141,e.aae140=t.aae140,e.id=t.id,e.calcumethod=t.calcumethod,e},departPageParams:function(){},handleQuery:function(t){var e=this;"qy"===t?this.$refs.qyPageRef.loadData((function(t){var a,l=(0,i.Z)(e.qyTableData);try{for(l.s();!(a=l.n()).done;){var o=a.value;o.effectivedate=[o.begindate,o.enddate]}}catch(n){l.e(n)}finally{l.f()}})):this.$refs.ksPageRef.loadData((function(t){var a,l=(0,i.Z)(e.ksTableData);try{for(l.s();!(a=l.n()).done;){var o=a.value;o.effectivedate=[o.begindate,o.enddate]}}catch(n){l.e(n)}finally{l.f()}}))},getContainer:function(){return document.getElementById("addDrawer")},handleCancel:function(){this.visible=!1,this.addForm.resetFields()},getCalcumethodLabel:function(t){for(var e=[{value:"0",label:"无限制"},{value:"1",label:"按个人"},{value:"2",label:"按月"},{value:"3",label:"按季度"},{value:"4",label:"按年"}],a=0,l=e;a<l.length;a++){var o=l[a];if(o.value===t)return o.label}},handleOk:function(){var t=this,e=this.addForm.getFieldsValue();if(e.id){var a,l="",o=(0,i.Z)(this.indicatorsData);try{for(o.s();!(a=o.n()).done;){var r=a.value;if(r.value===e.id){l=r.label;break}}}catch(c){o.e(c)}finally{o.f()}e.yzb004=l}if("all"===this.objtype?(e.objcode="1",e.objname="全院"):(e.objcode=this.objcode,e.objname=this.objname),e.objtype=this.objtype,null!=e.effectivedate&&e.effectivedate.length>0){var s=e.effectivedate;e.beginDate=s[0].format("YYYY-MM-DD"),e.endDate=s[1].format("YYYY-MM-DD"),delete e.effectivedate}this.Base.submit(this.addForm,{url:"indicatorsWarnConfig/addIndicatorsWarnConfig",data:(0,n.Z)({},e),autoValid:!0},{successCallback:function(e){1===e.data.isRepeat?t.$message.warning("当前指标已经配置，无需再次配置!"):(t.$message.success("添加成功"),t.handleQuery(t.activeKey))},failCallback:function(e){t.$message.warning("新增失败!")}})},handleSearch:function(t,e){e(),this.searchText=t[0]},handleReset:function(t){t(),this.searchText=""},fnInitData:function(){var t=this;this.$refs.qyTableRef.hideColumns(["objtype","yzb001","yzb003"]),this.Base.submit(null,{url:"indicatorsWarnConfig/queryIndicators",data:{},autoValid:!0},{successCallback:function(e){t.indicatorsData=e.data.indicatorsData},failCallback:function(t){}}),this.handleQuery("qy")}}},h=p,b=a(1001),m=(0,b.Z)(h,l,o,!1,null,"2b52d6ba",null),v=m.exports},36766:function(t,e,a){var l=a(66586);e["Z"]=l.Z},26263:function(t,e,a){a.d(e,{s:function(){return l},x:function(){return o}});var l=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},o=[]},66586:function(t,e){var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};e["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}}}]);
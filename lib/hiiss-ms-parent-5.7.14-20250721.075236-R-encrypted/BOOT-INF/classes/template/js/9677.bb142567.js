"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[9677],{88412:function(t,e,a){var l=a(26263),i=a(36766),o=a(1001),s=(0,o.Z)(i.Z,l.s,l.x,!1,null,"5e7ef0ae",null);e["Z"]=s.exports},99677:function(t,e,a){a.r(e),a.d(e,{default:function(){return g}});var l=function(){var t=this,e=this,a=e.$createElement,l=e._self._c||a;return l("div",{staticClass:"fit"},[l("ta-border-layout",{attrs:{layout:{footer:"50%",left:"35%"},"left-cfg":{expand:!0,expandText:"",showBar:!0}}},[l("div",{attrs:{slot:"leftExtraContent"},slot:"leftExtraContent"},[l("ta-form",{staticStyle:{"margin-top":"10px"},attrs:{"auto-form-create":function(e){t.deptForm=e},layout:"horizontal"}},[l("ta-row",[l("ta-col",{attrs:{span:12}},[l("ta-form-item",{attrs:{"field-decorator-id":"admDeptCode",span:12,required:!0}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("科室")]),l("ta-select",{attrs:{options:e.options},on:{select:e.deptSelect}})],1)],1)],1)],1)],1),l("div",{staticClass:"fit",attrs:{slot:"left"},slot:"left"},[l("ta-title",[e._v("患者列表"),l("span",{staticStyle:{"font-weight":"normal","font-size":"14px"}},[e._v("（请先选择患者）")])]),l("div",{staticStyle:{height:"90%"}},[l("ta-table",{attrs:{columns:e.patientColumns,"data-source":e.patientData,"custom-row":e.fnDeptRow,bordered:!0,size:"small",scroll:{y:"100%",x:"100%"}},on:{"update:columns":function(t){e.patientColumns=t}},scopedSlots:e._u([{key:"action",fn:function(t,a){return l("span",{},[""==a.url||null==a.url?l("span",[e._v("无")]):l("a",{on:{click:function(t){return e.auditResult(a)}}},[e._v("查看")])])}}])},[e._v(' :haveSn="true" ')])],1)],1),l("div",{staticClass:"fit",staticStyle:{height:"90%"}},[l("ta-title",[e._v("医嘱信息")]),l("ta-table",{ref:"orderRef",attrs:{columns:e.orderColumns,"data-source":e.orderData,"custom-row":e.fnCustomRow,"have-sn":!0,bordered:!0,size:"small",scroll:{y:"100%",x:"100%"}},on:{"update:columns":function(t){e.orderColumns=t}},scopedSlots:e._u([{key:"action",fn:function(t,a){return l("span",{},[l("a",{on:{click:function(t){return e.billing(a)}}},[e._v("计费")])])}}])})],1),l("div",{staticClass:"fit",attrs:{slot:"footer"},slot:"footer"},[l("ta-tabs",{staticClass:"fit",on:{change:e.tabChange},model:{value:e.activeKey,callback:function(t){e.activeKey=t},expression:"activeKey"}},[l("ta-tab-pane",{key:"1",attrs:{tab:"待收费用"}},[l("ta-form",{attrs:{"auto-form-create":function(e){t.billingForm=e},layout:"horizontal"}},[l("ta-row",[l("ta-col",{attrs:{span:7}},[l("ta-form-item",{attrs:{"field-decorator-id":"listType",span:12,"label-col":{span:8},"wrapper-col":{span:16}}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("费用类型")]),l("ta-select",{attrs:{"collection-type":"AKE003","allow-clear":""}})],1)],1),l("ta-col",{attrs:{span:7}},[l("ta-form-item",{attrs:{"field-decorator-id":"listCode",span:12,required:!0,extra:"回车进行搜索"}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label",labelCol:{span:8},wrapperCol:{span:16}},slot:"label"},[e._v("费用名称")]),l("ta-input",{attrs:{placeholder:"输入项目名称或编码","allow-clear":!0},on:{pressEnter:e.handleSearch}})],1)],1),l("ta-col",{attrs:{span:7}},[l("ta-form-item",{attrs:{"field-decorator-id":"cnt",span:12,required:!0,"label-col":{span:8},"wrapper-col":{span:16},"init-value":1}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("数量")]),l("ta-input")],1)],1),l("ta-col",{attrs:{span:3}},[l("ta-button",{staticStyle:{"margin-left":"20px"},attrs:{type:"primary"},on:{click:e.addHilist}},[e._v(" + ")])],1)],1)],1),l("div",{staticStyle:{height:"calc(100% - 90px)"}},[l("ta-table",{ref:"billTable",attrs:{columns:e.billColumns,"data-source":e.billData,"custom-row":e.fnCustomRow,bordered:!0,size:"small",scroll:{y:"100%"}},on:{"update:columns":function(t){e.billColumns=t}}})],1),l("ta-button",{staticStyle:{"margin-left":"calc(50% - 50px)",width:"100px"},attrs:{type:"primary"},on:{click:e.doCheckWithUrl}},[e._v(" 提交 ")])],1),l("ta-tab-pane",{key:"2",attrs:{tab:"已收费用"}},[l("div",{staticStyle:{height:"100%"}},[l("ta-table",{ref:"billTable",attrs:{columns:e.billColumns,"data-source":e.billedData,"custom-row":e.fnCustomRow,bordered:!0,size:"small","have-sn":!0,scroll:{y:"100%"}},on:{"update:columns":function(t){e.billColumns=t}},scopedSlots:e._u([{key:"action",fn:function(t,a){return l("span",{},[l("a",{on:{click:function(t){return e.delBill(a)}}},[e._v("删除")])])}}])})],1)])],1)],1)]),l("ta-modal",{attrs:{title:"费用操作",footer:null,height:"350px",width:"500px"},model:{value:e.billModel,callback:function(t){e.billModel=t},expression:"billModel"}},[l("choose-billing")],1),l("ta-drawer",{ref:"drawer",attrs:{title:"项目选择(单击进行选择）",width:"500",placement:"right",closable:!0,visible:e.visible,"destroy-on-close":""},on:{close:e.closeEdit}},[l("ta-table",{staticStyle:{height:"200px"},attrs:{columns:e.projectColumns,"data-source":e.dataSource,"custom-row":e.fnProjectRow,size:"small"},on:{"update:columns":function(t){e.projectColumns=t}}})],1),l("ta-modal",{attrs:{title:"智能提醒",footer:null,height:"750px",width:"1410px"},on:{cancel:e.handleCancel},model:{value:e.zntxModal,callback:function(t){e.zntxModal=t},expression:"zntxModal"}},[1==e.show?l("iframe",{staticStyle:{height:"100%",width:"100%"},attrs:{id:"iframe",src:e.url}}):e._e(),-1==e.show?l("div",{staticStyle:{height:"100%"}},[l("ta-title",{staticStyle:{color:"red"}},[e._v(" MESSAGE： ")]),l("div",{staticStyle:{border:"1px solid #1b65b9",width:"100%",height:"80%",padding:"15px","font-size":"16px",overflow:"auto"}},[e._v(" "+e._s(e.errMessage)+" ")])],1):e._e()]),l("ta-modal",{attrs:{title:"每晚预审结果查看",footer:null,height:"750px",width:"1410px"},on:{cancel:e.handleCancel2},model:{value:e.mwysModal,callback:function(t){e.mwysModal=t},expression:"mwysModal"}},[l("iframe",{staticStyle:{height:"100%",width:"100%"},attrs:{id:"iframe2",sandbox:"allow-scripts allow-top-navigation allow-same-origin",src:e.url2}})])],1)},i=[],o=(a(32564),a(88412)),s=function(){var t=this,e=this,a=e.$createElement,l=e._self._c||a;return l("div",[l("ta-form",{attrs:{autoFormCreate:function(e){t.billingForm=e},layout:"horizontal"}},[l("ta-row",[l("ta-col",{attrs:{span:24}},[l("ta-form-item",{attrs:{fieldDecoratorId:"yzName",span:12,disabled:!0}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("医嘱名称")]),l("ta-input")],1)],1),l("ta-col",{attrs:{span:24}},[l("ta-form-item",{attrs:{fieldDecoratorId:"yzCnt",span:12,disabled:!0}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("医嘱数量")]),l("ta-input")],1)],1),l("ta-col",{attrs:{span:24}},[l("ta-form-item",{attrs:{fieldDecoratorId:"listType",span:12,required:!0}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("费用类型")]),l("ta-select",{attrs:{options:e.options,"collection-type":"AKE003"}})],1)],1),l("ta-col",{attrs:{span:24}},[l("ta-form-item",{attrs:{fieldDecoratorId:"listCode",span:12,required:!0}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("费用名称")]),l("ta-input")],1)],1),l("ta-col",{attrs:{span:24}},[l("ta-form-item",{attrs:{fieldDecoratorId:"cnt",span:12,required:!0}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("数量")]),l("ta-input")],1)],1),l("ta-button",{staticStyle:{"margin-left":"220px"},attrs:{type:"primary"},on:{click:e.fnSubmit}},[e._v("提交")])],1)],1)],1)},n=[],r={name:"chooseBilling",components:{},props:{},data:function(){return{options:[]}},methods:{fnSubmit:function(){this.$emit("addHilist",this.billingForm.getFieldsValue())}}},c=r,d=a(1001),u=(0,d.Z)(c,s,n,!1,null,"4cce91f8",null),f=u.exports,h={name:"doctorOrder",components:{ChooseBilling:f,TaTitle:o.Z},data:function(){var t=[{title:"住院号",dataIndex:"iptNo",align:"center",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"住院次数",dataIndex:"patnIptCnt",align:"right",width:80,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"患者姓名",dataIndex:"psnName",align:"left",width:80,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"医保",dataIndex:"hiFeesetlType",align:"center",width:60,overflowTooltip:!0,collectionType:"AAE141",customHeaderCell:this.fnCustomHeaderCell},{title:"性别",dataIndex:"gend",align:"center",width:50,overflowTooltip:!0,collectionType:"SEX",customHeaderCell:this.fnCustomHeaderCell},{title:"年龄",dataIndex:"age",align:"right",width:50,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"每晚预审结果",dataIndex:"action",align:"center",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell,scopedSlots:{customRender:"action"}}],e=[{title:"医嘱明细id",dataIndex:"drordNo",align:"center",width:100},{title:"医嘱编码",dataIndex:"hilistCode",align:"left",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"医嘱名称",dataIndex:"hilistName",align:"left",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"项目类型",dataIndex:"listType",align:"center",width:80,collectionType:"AKE003",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"开医嘱时间",dataIndex:"drordBegntime",align:"center",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"数量",dataIndex:"cnt",align:"right",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"单价",dataIndex:"pric",align:"right",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"操作",dataIndex:"action",align:"center",width:80,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell,scopedSlots:{customRender:"action"}}],a=[{title:"费用项目id",dataIndex:"hilistCode",align:"center",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"项目类型",dataIndex:"listType",align:"center",width:70,collectionType:"AKE003",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"费用名称",dataIndex:"hilistName",align:"left",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"数量",dataIndex:"cnt",align:"right",width:70,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"单价",dataIndex:"pric",align:"right",width:50,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"费用时间",dataIndex:"feeOcurTime",align:"center",width:50,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"操作",dataIndex:"action",align:"center",width:60,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell,scopedSlots:{customRender:"action"}}],l=[{title:"项目id",dataIndex:"hilistCode",align:"center",width:150,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"项目名称",dataIndex:"hilistName",align:"left",width:130,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"单价",dataIndex:"pric",align:"right",width:60,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell}];return{options:[],patientColumns:t,patientData:[],orderColumns:e,orderData:[],activeKey:"1",billColumns:a,billData:[],billedData:[],iptNo:"",billModel:!1,patientInfo:null,visible:!1,projectColumns:l,dataSource:[],zntxModal:!1,mwysModal:!1,url:"",url2:"",engOptions:[],show:"",saveParam:"",dataObject:null,resultList:[]}},mounted:function(){this.$refs.orderRef.hideColumns(["drordNo"]),this.$refs.billTable.hideColumns(["action"]),this.fnQueryDept(),window.addEventListener("message",this.handleMessageNur)},watch:{visible:function(t){var e=this;setTimeout((function(){e.$refs.drawer.$el.style.display=t?"block":"none"}),300)}},methods:{handleMessageNur:function(t){var e=t.data;"0"===e.infcode&&(this.zntxModal=!1,1===this.show&&this.saveInfo(JSON.stringify(e.result)))},fnQueryEng:function(){var t=this;this.Base.submit(null,{url:"dischargeHis/queryEng",data:{trig_scen:3},autoValid:!1},{successCallback:function(e){t.engOptions=e.data.list},failCallback:function(e){t.$message.error("引擎数据加载失败")}})},fnCustomRow:function(t,e){var a=this;return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px"},on:{click:function(e){a.billingForm.setFieldsValue({listCode:t.hilistCode})}}}},fnCustomHeaderCell:function(t){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px",fontWeight:"bold"}}},tabChange:function(t){"1"===t?this.$refs.billTable.hideColumns(["action"]):"2"===t&&this.$refs.billTable.showColumns(["action"]),this.activeKey=t},fnQueryDept:function(){var t=this;this.Base.submit(null,{url:"doctorOrder/queryDept",autoValid:!1},{successCallback:function(e){t.options=e.data.list},failCallback:function(e){t.$message.error("科室数据加载失败")}})},deptSelect:function(t){var e=this;this.Base.submit(null,{url:"doctorOrder/queryPatient",data:{admDeptCode:t},autoValid:!1},{successCallback:function(t){e.patientData=t.data.list;var a=e.patientData[0];a.newOrder=JSON.stringify(e.billData),a.infno=990107,a.trig_scen=2;var l=[];e.Base.submit(null,{url:"doctorOrder/doCheck2",data:a,autoValid:!1},{successCallback:function(t){l=t.data.result.output.result;for(var a=0;a<e.patientData.length;a++)for(var i=0;i<l.length;i++)l[i].curr_mdtrt_id===e.patientData[a].currMdtrtId&&(e.patientData[a].url=l[i].url)},failCallback:function(t){e.$message.error("接口调用失败加载失败")}})},failCallback:function(t){e.$message.error("患者数据加载失败")}})},fnDeptRow:function(t,e){var a=this;return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",backgroundColor:t.iptNo===this.iptNo?"#7ecdf0":"",lineHeight:"22px",cursor:"pointer"},on:{click:function(e){a.iptNo=t.iptNo,a.patientInfo=t,a.billData=[],a.fnQuery()}}}},fnQuery:function(){var t=this;this.Base.submit(null,{url:"doctorOrder/queryOrder",data:{iptNo:this.iptNo},autoValid:!1},{successCallback:function(e){t.orderData=e.data.list,t.fnQueryBilling()},failCallback:function(e){t.$message.error("医嘱数据加载失败")}})},billing:function(t){this.billingForm.setFieldsValue({listType:t.listType,listCode:t.hilistCode+"--"+t.hilistName,cnt:t.cnt}),this.dataObject=t},delBill:function(t){var e=this,a=t.feedetlSn;this.Base.submit(null,{url:"doctorOrder/delBill",data:{ykc610:a},autoValid:!1},{successCallback:function(t){200===t.data.infcode?(e.$message.success(t.data.result),e.fnQueryBilling()):400===t.data.infcode&&e.$message.error(t.data.result)},failCallback:function(t){e.$message.error("接口调用失败加载失败")}})},audit:function(t){var e=this,a=t;a.newOrder=JSON.stringify(this.billData),a.infno=990107,a.trig_scen=2;var l="";return this.Base.submit(null,{url:"doctorOrder/doCheck2",data:a,autoValid:!1},{successCallback:function(t){l=t.data.result.output.result},failCallback:function(t){e.$message.error("接口调用失败加载失败")}}),l},auditResult:function(t){window.open(t.url,"newwindow","height=750, width=1300, top=200, left=400, toolbar=no, menubar=no, scrollbars=no, resizable=no, location=no, status=no")},fnQueryBilling:function(){var t=this;this.Base.submit(null,{url:"billing/querybilling",data:{iptNo:this.iptNo},autoValid:!1},{successCallback:function(e){t.billedData=e.data.list},failCallback:function(e){t.$message.error("费用数据加载失败")}})},handleSearch:function(){var t=this;this.Base.submit(null,{url:"doctorOrder/queryProject",data:{ake003:this.billingForm.getFieldValue("listType"),info:this.billingForm.getFieldValue("listCode")},autoValid:!1},{successCallback:function(e){t.visible=!0,t.dataSource=e.data.list},failCallback:function(e){t.$message.error("项目信息查询失败")}})},closeEdit:function(){this.visible=!1},fnProjectRow:function(t,e){var a=this;return{style:{cursor:"pointer"},on:{click:function(e){a.onSelect(t)}}}},addHilist:function(){if(null!=this.dataObject){var t=this.billingForm.getFieldsValue();if(void 0!==this.iptNo&&""!==this.iptNo&&null!=this.iptNo)if(void 0!==t.cnt){var e=/^([1-9][0-9]*)+(\.[0-9]{1,2})?$/;if(e.test(t.cnt)){this.dataObject.cnt=t.cnt;var a=new Date;this.dataObject.feeOcurTime=a.getFullYear()+"-"+(a.getMonth()+1)+"-"+a.getDate()+" "+a.getHours()+":"+a.getMinutes()+":"+a.getSeconds(),this.billData.push(this.dataObject),this.dataObject=null,this.billingForm.resetFields()}else this.$message.error("项目数量只能填入数字")}else this.$message.warn("数量未填写");else this.$message.error("请先选择患者才能新增医嘱")}else this.$message.error("请按回车键选择项目")},onSelect:function(t){this.dataObject=t,this.billingForm.setFieldsValue({listCode:t.hilistCode+"--"+t.hilistName}),this.visible=!1},doCheck:function(){var t=this,e=this.patientInfo;e.newOrder=JSON.stringify(this.billData),e.infno=990105,e.trig_scen=3,this.Base.submit(null,{url:"doctorOrder/doCheck",data:e,autoValid:!1},{successCallback:function(e){t.show=e.data.infcode,-1===t.show?t.errMessage=e.data.err_msg:1===t.show?t.saveParam=e.data.saveParam:t.$message.success(e.data.msg)},failCallback:function(e){t.$message.error("接口调用失败加载失败")}})},doCheckWithUrl:function(t){var e=this,a=this.patientInfo;null!==a?0!==this.billData.length?(a.newOrder=JSON.stringify(this.billData),a.infno=990105,a.trig_scen=3,this.Base.submit(null,{url:"doctorOrder/doCheckWithUrl",data:a,autoValid:!1},{successCallback:function(t){if(e.show=t.data.infcode,-1===e.show)e.errMessage=t.data.err_msg;else if(1===e.show){var a=t.data.result;e.patientInfo.call_id=a.call_id,e.url=a.url,e.saveParam=t.data.saveParam,void 0!==e.url&&""!==e.url?e.zntxModal=!0:e.saveInfo(t.data.result)}else e.fnQuery(),e.$message.success(t.data.msg)},failCallback:function(t){e.$message.error("接口调用失败加载失败")}})):this.$message.error("费用信息不能为空！"):this.$message.error("请选择患者！")},getOperate:function(){var t=this,e=this.patientInfo;e.infno=990108,e.trig_scen=3,e.lastInfno=990105,e.call_id=this.patientInfo.call_id,this.Base.submit(null,{url:"doctorOrder/getOperate",data:e,autoValid:!1},{successCallback:function(e){t.saveInfo(JSON.stringify(e.data.result.output.result))},failCallback:function(e){t.$message.error("保存数据失败")}})},saveInfo:function(t){var e=this;this.Base.submit(null,{url:"billing/saveInfo",data:{param:this.saveParam,result:t,cj:"费用"},autoValid:!1},{successCallback:function(t){e.fnQueryBilling(),e.$message.success(t.data.msg)},failCallback:function(t){e.$message.error("信息保存失败")}})},handleCancel:function(){this.zntxModal=!1,this.show},handleCancel2:function(){this.visible=!1,this.showAll=!1}}},m=h,p=(0,d.Z)(m,l,i,!1,null,"3fdc80e9",null),g=p.exports},36766:function(t,e,a){var l=a(66586);e["Z"]=l.Z},26263:function(t,e,a){a.d(e,{s:function(){return l},x:function(){return i}});var l=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},i=[]},66586:function(t,e){var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};e["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}}}]);
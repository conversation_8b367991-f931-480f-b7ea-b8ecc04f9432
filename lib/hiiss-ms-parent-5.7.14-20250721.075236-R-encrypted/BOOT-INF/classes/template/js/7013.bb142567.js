"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[7013],{37292:function(t,a,e){e.r(a),e.d(a,{default:function(){return u}});var n=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticStyle:{padding:"20px"}},[e("ta-row",[e("ta-label-con",{attrs:{label:"设置下拉框数据量"}},[e("ta-input-number",{attrs:{precision:0,min:1},model:{value:t.dataLength,callback:function(a){t.dataLength=a},expression:"dataLength"}}),e("ta-button",{attrs:{type:"primary"},on:{click:t.setData}},[t._v(" 确认 ")])],1)],1),e("ta-row",[e("ta-label-con",{attrs:{label:"组件"}},[e("ta-select",{attrs:{options:t.dataList,virtual:!0}})],1)],1)],1)},s=[],i={name:"selectTest",data:function(){return{dataLength:"20",dataList:[]}},created:function(){this.setData()},methods:{setData:function(){var t=this;Base.submit(null,{url:"http/mock/projectDemo/selectData",data:{length:this.dataLength}}).then((function(a){t.dataList=a.data.list}))}}},l=i,c=e(1001),r=(0,c.Z)(l,n,s,!1,null,"5389ac27",null),u=r.exports}}]);
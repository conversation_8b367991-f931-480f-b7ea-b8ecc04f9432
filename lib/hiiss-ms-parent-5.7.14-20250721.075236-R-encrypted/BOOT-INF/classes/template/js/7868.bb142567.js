(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[7868],{88412:function(t,e,a){"use strict";var r=a(26263),n=a(36766),i=a(1001),l=(0,i.Z)(n.Z,r.s,r.x,!1,null,"5e7ef0ae",null);e["Z"]=l.exports},28546:function(t,e,a){"use strict";a.r(e),a.d(e,{default:function(){return w}});var r=function(){var t=this,e=this,a=e.$createElement,r=e._self._c||a;return r("div",{staticClass:"fit"},[r("ta-border-layout",{attrs:{"footer-cfg":{showBorder:!1},layout:{header:"165px",footer:"0px"},showPadding:!0}},[r("div",{attrs:{slot:"header"},slot:"header"},[r("ta-title",{attrs:{title:"查询条件"}}),r("ta-form",{staticStyle:{"padding-top":"5px"},attrs:{autoFormCreate:function(e){t.baseInfoForm=e},col:2,formLayout:!0}},[r("ta-form-item",{attrs:{fieldDecoratorOptions:{rules:[{required:!0,message:"必须选取日期"}]},"init-value":e.rangeValue,"label-col":{span:7},required:!0,span:4,"wrapper-col":{span:17},fieldDecoratorId:"allDate",label:"开单时间"}},[r("ta-range-picker",{attrs:{"allow-one":!0}},[r("ta-icon",{attrs:{slot:"suffixIcon",type:""},slot:"suffixIcon"})],1)],1),r("ta-form-item",{attrs:{"label-col":{span:8},span:4,"wrapper-col":{span:16},"field-decorator-id":"aae500",disabled:e.aae500Flag,initValue:"1",label:"就诊类型"}},[r("ta-select",{attrs:{placeholder:"就诊类型筛选"},on:{select:e.aae500Change,change:e.aae500SelectChange}},[r("ta-select-option",{attrs:{value:"1"}},[e._v("门诊")]),r("ta-select-option",{attrs:{value:"0"}},[e._v("门特")]),r("ta-select-option",{attrs:{value:"2"}},[e._v("医嘱")]),r("ta-select-option",{attrs:{value:"3"}},[e._v("计费")])],1)],1),r("ta-form-item",{attrs:{"label-col":{span:8},span:4,"wrapper-col":{span:16},"field-decorator-id":"akb020",label:"院区标识"}},[r("ta-select",{attrs:{disabled:e.paramsDisable.akb020,options:e.akb020List,allowClear:"",placeholder:"院区选择"},on:{change:e.departSelectChange}})],1),r("ta-form-item",{attrs:{"label-col":{span:8},span:4,virtual:!0,"wrapper-col":{span:16},"field-decorator-id":"aaz307",label:"科室名称"}},[r("ta-select",{attrs:{options:e.ksList,"show-search":!0,allowClear:"",placeholder:"科室名称筛选",showSearch:""}})],1),r("ta-form-item",{attrs:{"label-col":{span:8},span:4,"wrapper-col":{span:16},"field-decorator-id":"aaz263",label:"医师姓名"}},[r("ta-select",{attrs:{options:e.doctorList,"show-search":!0,allowClear:"",placeholder:"医师姓名筛选",showSearch:""}})],1),r("ta-form-item",{attrs:{"label-col":{span:8},span:4,"wrapper-col":{span:16},"field-decorator-id":"ake003",label:"三目类别"}},[r("ta-select",{attrs:{"dropdown-match-select-width":!1,"allow-clear":"","collection-type":"AKE003",placeholder:"三目类别筛选",showSearch:""}})],1),r("ta-form-item",{attrs:{"label-col":{span:8},span:4,"wrapper-col":{span:16},"field-decorator-id":"projectInfo",label:"医保项目"}},[r("ta-input",{attrs:{placeholder:"请输入医保项目名称或编码"}})],1),r("ta-form-item",{attrs:{span:4,"label-col":{span:8},"wrapper-col":{span:16},fieldDecoratorId:"result",label:"预审结果","init-value":e.resultInit}},[r("ta-select",{attrs:{showSearch:"",mode:"multiple","collection-type":"APE800","collection-filter":"0",maxTagCount:1,placeholder:"请选择",allowClear:""}})],1),r("ta-form-item",{attrs:{span:12}}),r("ta-form-item",{attrs:{"label-col":{span:8},span:4,"wrapper-col":{span:16},label:" "}},[r("ta-button",{attrs:{icon:"search",type:"primary"},on:{click:e.fnQuery}},[e._v("查询")]),r("ta-button",{attrs:{icon:"redo",disabled:e.paramsDisable.akb020,type:"default"},on:{click:e.fnReset}},[e._v("重置")])],1)],1)],1),r("div",{staticClass:"fit",staticStyle:{height:"87%"}},[r("ta-title",{staticStyle:{width:"50%"},attrs:{title:"查询结果"}}),r("ta-big-table",{ref:"Table",attrs:{data:e.pageData,"footer-method":e.footerMethod,"tooltip-config":{enabled:!0,contentMethod:e.showTooltipMethod},"auto-resize":"",border:"","empty-text":"-",height:"100%","highlight-current-row":"","highlight-hover-row":"",resizable:"","show-footer":"","show-overflow":""},on:{"cell-click":e.cellClickEvent}},[r("ta-big-table-column",{attrs:{align:"center","header-align":"center","min-width":"50",sortable:"",title:"序号",type:"seq"}}),r("ta-big-table-column",{attrs:{align:"left",field:"aae386","header-align":"center","min-width":"180",sortable:"",title:"科室名称"}}),r("ta-big-table-column",{attrs:{align:"left",field:"name","header-align":"center","min-width":"150",sortable:"",title:"患者姓名"}}),r("ta-big-table-column",{attrs:{align:"left",field:"gzfl","header-align":"center","min-width":"180",sortable:"",title:"规则分类"}}),r("ta-big-table-column",{attrs:{align:"left",field:"aaa167","header-align":"center","min-width":"300",sortable:"",title:"规则名称"}}),r("ta-big-table-column",{attrs:{align:"left",field:"ake002","header-align":"center","min-width":"300",sortable:"",title:"医保项目名称"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[r("ta-button",{attrs:{size:"small",type:"link"}},[r("span",{staticStyle:{color:"#00bcff","text-decoration":"underline"}},[e._v(e._s(a.ake002))])])]}}])}),r("ta-big-table-column",{attrs:{visible:!1,align:"right",field:"shzl","header-align":"center","min-width":"140",title:"审核总次数"}}),r("ta-big-table-column",{attrs:{align:"right",field:"txzl","header-align":"center","min-width":"140",sortable:"",title:"违规次数(次)"}}),r("ta-big-table-column",{attrs:{formatter:e.ratioFormat,visible:!1,align:"right",field:"txl","header-align":"center","min-width":"80",title:"提醒率"}}),r("ta-big-table-column",{attrs:{formatter:e.moneyFormat,visible:"0"!=this.$route.query.flag&&"2"!=this.$route.query.flag,align:"right",field:"txje","header-align":"center","min-width":"140",sortable:"",title:"违规金额(元)"}}),r("ta-big-table-column",{attrs:{align:"right",field:"txsl",visible:"0"!=this.$route.query.flag&&"2"!=this.$route.query.flag,"header-align":"center","min-width":"140",sortable:"",title:"违规数量"}}),r("ta-big-table-column",{attrs:{align:"right",field:"jxxms","header-align":"center","min-width":"150",sortable:"",title:"继续使用次数(次)"}}),r("ta-big-table-column",{attrs:{formatter:e.ratioFormat,align:"right",field:"jxxmzb","header-align":"center","min-width":"180",sortable:"",title:"继续使用次数占比"}}),r("ta-big-table-column",{attrs:{align:"right",field:"qxxms","header-align":"center","min-width":"130",sortable:"",title:"取消次数(次)"}}),r("ta-big-table-column",{attrs:{formatter:e.ratioFormat,align:"right",field:"qxxmzb","header-align":"center","min-width":"150",sortable:"",title:"取消次数占比"}}),r("ta-big-table-column",{attrs:{align:"right",field:"zfxms","header-align":"center","min-width":"130",sortable:"",title:"自费次数(次)"}}),r("ta-big-table-column",{attrs:{formatter:e.ratioFormat,align:"right",field:"zfxmzb","header-align":"center","min-width":"150",sortable:"",title:"自费次数占比"}}),r("ta-big-table-column",{attrs:{align:"right",field:"wzcxms","header-align":"center","min-width":"140",sortable:"",title:"无操作次数(次)"}}),r("ta-big-table-column",{attrs:{formatter:e.ratioFormat,align:"right",field:"wczxmzb","header-align":"center","min-width":"160",sortable:"",title:"无操作次数占比"}}),r("template",{slot:"bottomBar"},[r("ta-button",{staticStyle:{float:"right","margin-top":"20px"},attrs:{icon:"download",type:"primary"},on:{click:e.exportTable}},[e._v("导出 ")]),r("ta-pagination",{ref:"gridPager",staticStyle:{float:"right","margin-top":"20px","margin-right":"10px"},attrs:{"data-source":e.pageData,defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"],params:e.infoPageParams,url:"reportStatistics/queryReportStatistics"},on:{"update:dataSource":function(t){e.pageData=t},"update:data-source":function(t){e.pageData=t}}})],1)],2)],1)])],1)},n=[],i=a(66347),l=a(48534),o=a(95082),s=(a(36133),a(88412)),u=a(83231),c=a(36797),m=a.n(c),f=a(92566),h=a(22722),d=a(55115),p=a(18671);d.w3.prototype.Base=Object.assign(d.w3.prototype.Base,(0,o.Z)({},h.Z));var b={name:"reportStatisticsHZ",components:{TaTitle:s.Z},data:function(){return{pageData:[],amountData:[],resultInit:[],rangeValue:[this.Base.getMoment((new Date).toISOString().slice(0,8)+"-01","YYYY-MM-DD"),this.Base.getMoment((new Date).toISOString().slice(0,10),"YYYY-MM-DD")],akb020:"",hospDeptType:"C",ksList:[],akb020List:[],doctorList:[],permissions:{},paramsDisable:{akb020:!1},jxzb:"",txl:"",aae500Flag:!1}},created:function(){var t=this;return(0,l.Z)(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,u.Z.permissionCheck();case 2:t.permissions=e.sent;case 3:case"end":return e.stop()}}),e)})))()},mounted:function(){var t=this;if("undefined"!=this.$route.query.flag&&this.$route.query.flag&&(this.aae500Flag=!0,this.baseInfoForm.setFieldsValue({aae500:this.$route.query.flag})),this.fnQueryHos(),this.fnQueryDept(""),this.$route.query.params){var e=JSON.parse(this.$route.query.params);e.allDate=e.allDate.map((function(e){var a=new Date(e),r=new Date;return a.setHours(0,0,0,0),r.setHours(0,0,0,0),a!=r&&a.setDate(a.getDate()+1),t.Base.getMoment(a.toISOString(),"YYYY-MM-DD")})),this.$route.query.aaz307&&(e.aaz307=this.$route.query.aaz307),this.$route.query.aaz263&&(e.aaz263=this.$route.query.aaz263),this.$route.query.ake002&&(e.projectInfo=this.$route.query.ake002),this.baseInfoForm.setFieldsValue(e),this.$nextTick((function(){t.aae500Change(e.aae500)}))}this.fnQueryAa01()},methods:{moment:m(),aae500Change:function(t){["0","2"].includes(t)?(this.$refs.Table.hideColumn(this.$refs.Table.getColumnByField("txje")),this.$refs.Table.hideColumn(this.$refs.Table.getColumnByField("txsl"))):(this.$refs.Table.showColumn(this.$refs.Table.getColumnByField("txje")),this.$refs.Table.showColumn(this.$refs.Table.getColumnByField("txsl")))},fnQueryAa01:function(){var t=this;this.Base.submit(null,{url:"miimCommonRead/getAa01Aaa005ByAaz499",data:{aaz499:206}},{successCallback:function(e){var a=e.data;t.resultInit="n"===a.aaa005.toLowerCase()?["1","2"]:["1","2","3"],t.fnQuery()},failCallback:function(e){t.$message.error("查询aa01表数据失败")}})},fnQueryHos:function(){var t=this,e={url:"/miimCommonRead/queryHospRpcList",showPageLoading:!1,autoValid:!0},a={successCallback:function(e){t.akb020List=e.data.resultData,t.permissions&&t.permissions.akb020Set.size>0&&(t.paramsDisable.akb020=!0,t.akb020List=t.akb020List.filter((function(e){return t.permissions.akb020Set.has(e.value)})),t.baseInfoForm.setFieldsValue({akb020:t.akb020List[0].value})),t.akb020=t.akb020List[0].value},failCallback:function(e){t.$message.error("医院数据加载失败")}};this.Base.submit(null,e,a)},footerMethod:function(t){var e=this,a=t.columns;t.data;return[a.map((function(t,a){return 0===a?"合计":["txzl","txsl","txje","jxxms","qxxms","zfxms","wzcxms"].includes(t.property)?(0,f.Z)(e.amountData,t.property):null}))]},showTooltipMethod:function(t){var e=t.type,a=t.column,r=(t.row,t.items,t._columnIndex,a.property);if("header"===e)return p.Z.props[r]?p.Z.props[r][e]:""},handleChange:function(t){this.fnQuery()},cellClickEvent:function(t){var e=t.row,a=t.column,r=a.property;if("ake002"===r){var n,i,l=this.baseInfoForm.getFieldsValue();l.akb020||(l.akb020=""),l.dateFlag="audit","1"===l.aae500?(i="门诊审核明细查询",n="querycommon.html#/outpatientClinic?aaz307=".concat(e.aaz307,"&name=").concat(e.name,"&aaz263=").concat(e.aaz263,"&ake002=").concat(e.ake001,"&params=").concat(JSON.stringify(l))):"0"===l.aae500?(i="门特审核明细查询",n="querycommon.html#/outpatientSpecialClinic?aaz307=".concat(e.aaz307,"&name=").concat(e.name,"&aaz263=").concat(e.aaz263,"&ake002=").concat(e.ake001,"&params=").concat(JSON.stringify(l))):(i=p.Z.optionsMap.get(l.aae500)+"明细查询",n="querycommon.html#/inpatientGather?aaz307=".concat(e.aaz307,"&name=").concat(e.name,"&aaz263=").concat(e.aaz263,"&ake002=").concat(e.ake001,"&params=").concat(JSON.stringify(l))),this.Base.openTabMenu({id:Math.floor(90*Math.random())+10,name:i,url:n,refresh:!1})}},exportTable:function(){var t=this,e=this.baseInfoForm.getFieldsValue();if(void 0===e.allDate||e.allDate.length<2)this.$message.error("请选择时间范围！");else{e=this.infoPageParams();var a,r=[],n=this.$refs.Table.getColumns(),l=(0,i.Z)(n);try{for(l.s();!(a=l.n()).done;){var o=a.value;"seq"!==o.type&&"operate"!==o.property&&!1!==o.visible&&r.push({header:o.title,key:o.property,width:20})}}catch(c){l.e(c)}finally{l.f()}var s=n.map((function(t){return t.property})),u=p.Z.codelist.filter((function(t){return s.includes(t.columnKey)}));this.Base.submit(null,{url:"reportStatistics/exportExcel",data:e,autoValid:!1},{successCallback:function(a){var n=a.data.data,i={fileName:p.Z.optionsMap.get(e.aae500)+"汇总统计结果表("+e.startDate+"-"+e.endDate+")",sheets:[{name:"worksheet1",column:{complex:!1,columns:r},rows:n,codeList:u}]};t.Base.generateExcel(i)},failCallback:function(e){t.$message.error("数据加载失败")}})}},formatAmount:function(t){return t.toFixed(2).replace(/\d(?=(\d{3})+\.)/g,"$&,")},moneyFormat:function(t){var e=t.cellValue;return"--"==e?e:this.formatAmount(e)},ratioFormat:function(t){var e=t.cellValue;return 0==e?"0":e+"%"},fnReset:function(){this.baseInfoForm.resetFields(),this.$route.query.flag&&this.baseInfoForm.setFieldsValue({aae500:this.$route.query.flag}),this.txl="",this.jxzb="",this.fnQuery()},fnQueryDocter:function(){var t=this,e={akb020:this.akb020};this.Base.submit(null,{url:"miimCommonRead/queryDoctorRpcList",showPageLoading:!1,data:e,autoValid:!1},{successCallback:function(e){t.doctorList=e.data.resultData},failCallback:function(e){t.$message.error("医师数据加载失败")}})},departSelectChange:function(t){this.fnQueryDept(t)},aae500SelectChange:function(t){this.hospDeptType="1"==t||"0"==t?"C":"I",this.fnQueryDept("")},fnQueryDept:function(t){var e=this;this.akb020=t||"",this.Base.submit(null,{url:"miimCommonRead/queryDepartRpcList",showPageLoading:!1,data:{akb020:this.akb020,hospDeptType:this.hospDeptType},autoValid:!1},{successCallback:function(t){e.ksList=t.data.resultData,e.fnQueryDocter()},failCallback:function(t){e.$message.error("科室数据加载失败")}})},infoPageParams:function(){var t=this.baseInfoForm.getFieldsValue();return t.startDate=t.allDate[0].format("YYYY-MM-DD HH:mm:ss"),t.endDate=t.allDate[1].format("YYYY-MM-DD HH:mm:ss"),t.flag="hz",t},fnQuery:function(){var t=this,e=this.baseInfoForm.getFieldValue("allDate");!e||e[0]&&e[1]?this.$nextTick((function(){t.$refs.gridPager.loadData((function(e){t.amountData=e.data.data}))})):this.$message.error("请选择时间范围！")},fnCustomHeaderCell:function(t){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px",fontWeight:"bold"}}},filterOption:function(t,e){return e.componentOptions.children[0].text.indexOf(t)>=0}}},g=b,y=a(1001),v=(0,y.Z)(g,r,n,!1,null,"e52183c6",null),w=v.exports},36766:function(t,e,a){"use strict";var r=a(66586);e["Z"]=r.Z},26263:function(t,e,a){"use strict";a.d(e,{s:function(){return r},x:function(){return n}});var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},n=[]},66586:function(t,e){"use strict";var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};e["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}},83231:function(t,e,a){"use strict";var r=a(48534);a(36133);function n(t){return t?t.akb020&&t.aaz263?"personal":t.akb020&&t.aaz309&&t.aaz307?"group":t.akb020&&t.aaz307?"department":t.akb020?"hospital":"all":"all"}function i(t){return l.apply(this,arguments)}function l(){return l=(0,r.Z)(regeneratorRuntime.mark((function t(e){var a,r,i,l,o,s,u,c,m,f,h,d;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Base.submit(null,{url:"/miimCommonRead/queryDataPermission",showPageLoading:!1,data:{jobnumber:e},autoValid:!0});case 2:return a=t.sent,r=new Set,i=new Set,a.data.permission.forEach((function(t){var e=n(t);"hospital"===e&&r.add(t.akb020),"department"===e&&i.add(t.aaz307)})),l=a.data.permission.filter((function(t){return"department"===n(t)||!i.has(t.aaz307)})).filter((function(t){return"hospital"===n(t)||!r.has(t.akb020)})),o=new Set(l.map((function(t){return t.akb020})).filter((function(t){return null!=t}))),s=new Set(l.map((function(t){return t.aaz307})).filter((function(t){return null!=t}))),u=new Set(l.map((function(t){return t.aaz309})).filter((function(t){return null!=t}))),c=new Set(l.map((function(t){return t.aaz263})).filter((function(t){return null!=t}))),m=!1,f=!1,h=!1,d=!1,1===o.size&&(m=!0),1===s.size&&1===o.size&&(f=!0),1===s.size&&1===o.size&&1===u.size&&(h=!0),1===o.size&&0===s.size&&1===c.size&&(d=!0),t.abrupt("return",{akb020Set:o,aaz307Set:s,aaz263Set:c,aaz309Set:u,akb020Disable:m,aaz307Disable:f,aaz263Disable:d,aaz309Disable:h});case 20:case"end":return t.stop()}}),t)}))),l.apply(this,arguments)}function o(t,e){Base.submit(null,{url:"miimCommonRead/getAa01AAE500StartStop",data:t},{successCallback:function(t){return e(t)}})}function s(t,e){Base.submit(null,{url:"miimCommonRead/insertTableColumShow",data:t,autoValid:!1},{successCallback:function(t){return e("success")},failCallback:function(t){return e("fail")}})}var u={zbx01:{footer:function(){return"统筹金总额"}},zbx02:{footer:function(){return"基金支出总额"}},zbx03:{footer:function(){return"总费用"}},zbx04:{footer:function(){return"总住院日"}}};e["Z"]={permissionCheck:i,getAa01AAE500StartStop:o,insertTableColumShow:s,props:u,moneyNumFormat:function(t){var e=t.cellValue;return e||"—"}}},18671:function(t,e){"use strict";var a=[{columnKey:"jxxmzb",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}},{columnKey:"txl",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}},{columnKey:"qxxmzb",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}},{columnKey:"zfxmzb",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}},{columnKey:"wczxmzb",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}}],r=[{columnKey:"jxxmzb",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}},{columnKey:"qxxmzb",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}},{columnKey:"zfxmzb",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}},{columnKey:"wczxmzb",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}}],n={txl:{header:function(){return"提醒率=提醒总次数/审核总次数"}},jxxmzb:{header:function(){return"继续使用占比=继续使用次数(次)/提醒总次数"}},qxxmzb:{header:function(){return"取消使用占比=取消使用次数/提醒总次数"}},zfxmzb:{header:function(){return"自费使用占比=自费使用次数/提醒总次数"}},wczxmzb:{header:function(){return"无操作使用占比=无操作使用次数/提醒总次数"}}},i=new Map([["1","门诊审核"],["0","门特审核"],["2","医嘱审核"],["3","计费审核"]]);e["Z"]={codelist:a,codelist2:r,props:n,optionsMap:i}},55382:function(){},61219:function(){}}]);
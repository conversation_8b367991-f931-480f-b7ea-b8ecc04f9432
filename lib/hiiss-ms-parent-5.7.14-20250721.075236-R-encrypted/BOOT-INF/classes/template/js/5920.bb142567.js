"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[5920],{88412:function(t,e,a){var i=a(26263),l=a(36766),s=a(1001),o=(0,s.Z)(l.Z,i.s,i.x,!1,null,"5e7ef0ae",null);e["Z"]=o.exports},15920:function(t,e,a){a.r(e),a.d(e,{default:function(){return y}});var i=function(){var t=this,e=this,i=e.$createElement,l=e._self._c||i;return l("div",{staticClass:"center-analysis-box"},[l("ta-border-layout",{staticStyle:{height:"100%","border-bottom":"none"},attrs:{layout:{header:"90px"},"show-padding":!1}},[l("div",{staticClass:"header-box",attrs:{slot:"header"},slot:"header"},[l("div",[l("ta-title",[e._v("数据导入处理")]),l("ta-form",{attrs:{"auto-form-create":function(e){t.form=e},"label-width":"400px",layout:"inline",enctype:"multipart/form-data"}},[l("ta-form-item",{attrs:{"field-decorator-id":"note"}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("请下载《中心审核数据导入模板.xls》，再按照模板格式上传")])]),l("ta-form-item",{attrs:{"field-decorator-id":"download"}},[l("ta-button",{attrs:{type:"primary",icon:"download"},on:{click:e.fnDownload}},[e._v(" 中心审核数据导入模板下载 ")])],1),l("ta-form-item",[l("ta-button",{attrs:{type:"success"},on:{click:e.centerAnalysisUpload}},[l("ta-icon",{attrs:{type:"upload"}}),e._v(" 中心审核数据上传 ")],1)],1)],1)],1)]),l("ta-border-layout",{staticStyle:{"border-bottom":"none"},attrs:{layout:{header:"90px"},"show-padding":!1,"show-border":!1}},[l("div",{attrs:{slot:"header"},slot:"header"},[l("ta-title",[e._v("查询条件")]),l("ta-form",{staticStyle:{position:"absolute",width:"100%"},attrs:{"auto-form-create":function(e){t.formQuery=e},layout:"horizontal","form-layout":!0}},[l("ta-form-item",{attrs:{span:5,"field-decorator-id":"aae043",require:{message:"请选择期号"},"label-col":{span:8},"wrapper-col":{span:15}}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("期号选择")]),l("ta-month-picker",{staticStyle:{width:"100%"},attrs:{format:"YYYYMM","disabled-date":e.disabledDate}})],1),l("ta-form-item",{attrs:{span:5,"field-decorator-id":"aaz108","init-value":"","label-col":{span:10},"wrapper-col":{span:14}}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("院区标识")]),l("ta-select",{staticStyle:{width:"100%"},attrs:{options:e.hosPartSelection}})],1),l("ta-form-item",{attrs:{span:5,"field-decorator-id":"aae141","label-col":{span:10},"wrapper-col":{span:14},hidden:!0}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("医保类型")]),l("ta-select",{staticStyle:{width:"100%"},attrs:{"collection-type":"AAE141"}})],1),l("ta-form-item",{attrs:{span:5,"field-decorator-id":"aae140","label-col":{span:10},"wrapper-col":{span:14},hidden:!0}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("险种类型")]),l("ta-select",{staticStyle:{width:"100%"},attrs:{"collection-type":"AAE140"}})],1),l("ta-form-item",{attrs:{span:5,"field-decorator-id":"aka130","label-col":{span:10},"wrapper-col":{span:14},hidden:!0}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("医疗类型")]),l("ta-select",{staticStyle:{width:"100%"},attrs:{"collection-type":"AKA130"}})],1),l("ta-button",{staticStyle:{float:"left","margin-left":"10px"},attrs:{icon:"search",type:"primary"},on:{click:e.fnQueryDetail}},[e._v(" 查询 ")]),"1"===e.tabsActiveKey?l("ta-button",{staticStyle:{float:"left","margin-left":"10px"},attrs:{type:"primary"},on:{click:e.fnExportAllData}},[e._v(" 导出 ")]):e._e()],1)],1),l("div",{staticClass:"footer-container"},[l("ta-tabs",{staticClass:"tabs-content-box",attrs:{"active-key":e.tabsActiveKey},on:{change:e.callBack}},[l("ta-tab-pane",{key:"1",staticClass:"tabs-pane-content",attrs:{tab:"表格"}},[l("div",{staticClass:"detail-modal",attrs:{id:"detailModal"}},[e.isShowruleStastic?l("div",{staticClass:"change-statistics"},[l("ta-checkbox",{attrs:{"default-checked":e.ruleFlag},on:{change:e.onChangeRuleFlag}},[e._v(" 统计违规类别 ")])],1):e._e(),l("ta-tabs",{staticClass:"detail-modal-tabs",on:{change:e.fnTabChange},model:{value:e.activeKey,callback:function(t){e.activeKey=t},expression:"activeKey"}},[l("ta-tab-pane",{key:"detail",staticClass:"detail-modal-tabs-children",attrs:{tab:"扣款明细","force-render":!0}},[l("div",{staticClass:"tabs-children-table"},[l("ta-table",{attrs:{size:"small",bordered:"",columns:e.columns_allDetail,"data-source":e.detailData,scroll:{x:1800,y:"100%"},"custom-row":e.fnCustomRow}})],1),l("div",[l("ta-pagination",{ref:"detailPager",staticStyle:{"margin-top":"10px","text-align":"right"},attrs:{"data-source":e.detailData,params:e.detailParams,url:"centerAnalysis/detailData"},on:{"update:dataSource":function(t){e.detailData=t},"update:data-source":function(t){e.detailData=t}}})],1)]),l("ta-tab-pane",{key:"dept",staticClass:"detail-modal-tabs-children",attrs:{tab:"科室扣款统计","force-render":!0}},[l("div",{staticClass:"tabs-children-table"},[l("ta-table",{attrs:{size:"small",bordered:"",columns:e.deptTableColumn,"data-source":e.deptData,scroll:{y:"100%"},"custom-row":e.fnCustomRow},scopedSlots:e._u([{key:"ruleGroup",fn:function(t){return l("span",{},[e._v(e._s(null===t||"null"===t?"--":t))])}}])})],1),l("div",[l("ta-pagination",{ref:"deptPager",staticStyle:{"margin-top":"10px","text-align":"right"},attrs:{"data-source":e.deptData,params:e.detailParams,url:"centerAnalysis/deptDetail"},on:{"update:dataSource":function(t){e.deptData=t},"update:data-source":function(t){e.deptData=t}}})],1)]),l("ta-tab-pane",{key:"rule",staticClass:"detail-modal-tabs-children",attrs:{tab:"规则扣款统计","force-render":!0}},[l("div",{staticClass:"tabs-children-table"},[l("ta-table",{attrs:{size:"small",bordered:"",columns:e.ruleTableColumn,"data-source":e.ruleData,scroll:{y:"100%"},"custom-row":e.fnCustomRow},scopedSlots:e._u([{key:"ruleGroup",fn:function(t){return l("span",{},[e._v(e._s(null===t||"null"===t?"--":t))])}}])})],1),l("div",[l("ta-pagination",{ref:"rulePager",staticStyle:{"margin-top":"10px","text-align":"right"},attrs:{"data-source":e.ruleData,params:e.detailParams,url:"centerAnalysis/ruleDetail"},on:{"update:dataSource":function(t){e.ruleData=t},"update:data-source":function(t){e.ruleData=t}}})],1)]),l("ta-tab-pane",{key:"project",staticClass:"detail-modal-tabs-children",attrs:{tab:"项目扣款统计","force-render":!0}},[l("div",{staticClass:"tabs-children-table"},[l("ta-table",{attrs:{size:"small",bordered:"",columns:e.columns_project,"data-source":e.projectData,scroll:{y:"100%"},"custom-row":e.fnCustomRow}})],1),l("div",[l("ta-pagination",{ref:"projectPager",staticStyle:{"margin-top":"10px","text-align":"right"},attrs:{"data-source":e.projectData,params:e.detailParams,url:"centerAnalysis/projectDetail"},on:{"update:dataSource":function(t){e.projectData=t},"update:data-source":function(t){e.projectData=t}}})],1)])],1)],1)]),l("ta-tab-pane",{key:"2",attrs:{tab:"图表"}},[l("ta-border-layout",{staticStyle:{height:"300px"},attrs:{layout:{left:"50%"},"show-border":!1,"show-padding":!1}},[l("div",{attrs:{slot:"left"},slot:"left"},[l("div",{staticStyle:{"border-bottom":"1px solid rgba(153,153,153,0.14)"}},[l("ta-title",{attrs:{title:e.aae043+"医保费用支付情况"}})],1),l("ta-echarts",{ref:"costChart",staticStyle:{height:"250px",width:"100%"},attrs:{option:e.costOption}})],1),l("div",[l("div",{staticStyle:{"border-bottom":"1px solid rgba(153,153,153,0.14)"}},[l("ta-title",{attrs:{title:"医保支付违规率趋势"}})],1),l("ta-echarts",{ref:"violationChart",staticStyle:{height:"250px",width:"100%"},attrs:{option:e.violationOption}})],1)]),l("ta-border-layout",{staticStyle:{height:"300px","border-top":"14px solid #F0F2F5"},attrs:{layout:{left:"50%"},"show-border":!1}},[l("div",{attrs:{slot:"left"},slot:"left"},[l("div",{staticStyle:{"border-bottom":"1px solid rgba(153,153,153,0.14)"}},[l("ta-title",{attrs:{title:e.aae043+e.ruleName+"科室违规扣款费用排名"}},[l("span",{staticStyle:{display:"inline-block",float:"right"}},[e.showLastDept?l("img",{staticStyle:{width:"20px",height:"20px"},attrs:{src:a(53949)},on:{click:function(t){return e.deptPage("last")}}}):l("img",{staticStyle:{width:"20px",height:"20px"},attrs:{src:a(89712)}}),e.showNextDept?l("img",{staticStyle:{width:"20px",height:"20px"},attrs:{src:a(23554)},on:{click:function(t){return e.deptPage("next")}}}):l("img",{staticStyle:{width:"20px",height:"20px"},attrs:{src:a(61368)}})])])],1),l("ta-echarts",{ref:"deptChart",staticStyle:{height:"220px",width:"100%"},attrs:{option:e.deptOption}})],1),l("div",[l("div",{staticStyle:{"border-bottom":"1px solid rgba(153,153,153,0.14)"}},[l("ta-title",{attrs:{title:e.aae043+e.kdksmctitle+e.kdysmctitle+"规则违规扣款费用排名"}},[l("span",{staticStyle:{display:"inline-block",float:"right"}},[e.showLastRule?l("img",{staticStyle:{width:"20px",height:"20px"},attrs:{src:a(53949)},on:{click:function(t){return e.rulePage("last")}}}):l("img",{staticStyle:{width:"20px",height:"20px"},attrs:{src:a(89712)}}),e.showNextRule?l("img",{staticStyle:{width:"20px",height:"20px"},attrs:{src:a(23554)},on:{click:function(t){return e.rulePage("next")}}}):l("img",{staticStyle:{width:"20px",height:"20px"},attrs:{src:a(61368)}})])])],1),l("ta-echarts",{ref:"ruleChart",staticStyle:{height:"220px",width:"100%"},attrs:{option:e.ruleOption}})],1)]),l("ta-border-layout",{staticStyle:{height:"306px","border-top":"14px solid #F0F2F5"},attrs:{layout:{left:"50%"},"show-border":!1}},[l("div",{staticStyle:{height:"100%"},attrs:{slot:"left"},slot:"left"},[l("div",{staticStyle:{"border-bottom":"1px solid rgba(153,153,153,0.14)"}},[l("ta-title",{attrs:{title:e.aae043+e.kdksmctitle+e.ruleName+"医师违规扣款费用排名"}},[l("span",{staticStyle:{display:"inline-block",float:"right"}},[e.showLastDoctor?l("img",{staticStyle:{width:"20px",height:"20px"},attrs:{src:a(53949)},on:{click:function(t){return e.doctorPage("last")}}}):l("img",{staticStyle:{width:"20px",height:"20px"},attrs:{src:a(89712)}}),e.showNextDoctor?l("img",{staticStyle:{width:"20px",height:"20px"},attrs:{src:a(23554)},on:{click:function(t){return e.doctorPage("next")}}}):l("img",{staticStyle:{width:"20px",height:"20px"},attrs:{src:a(61368)}})])])],1),l("ta-echarts",{ref:"doctorChart",staticStyle:{height:"calc(100% - 30px)",width:"100%"},attrs:{option:e.doctorOption}})],1),l("div",{staticStyle:{height:"100%"}},[l("div",{staticStyle:{"border-bottom":"1px solid rgba(153,153,153,0.14)"}},[l("ta-title",{attrs:{title:e.aae043+e.kdksmctitle+e.ruleName+e.kdysmctitle+e.aaa103+"项目违规扣款费用排名"}},[l("span",{staticStyle:{display:"inline-block",float:"right"}},[e.showLastProject?l("img",{staticStyle:{width:"20px",height:"20px"},attrs:{src:a(53949)},on:{click:function(t){return e.projectPage("last")}}}):l("img",{staticStyle:{width:"20px",height:"20px"},attrs:{src:a(89712)}}),e.showNextProject?l("img",{staticStyle:{width:"20px",height:"20px"},attrs:{src:a(23554)},on:{click:function(t){return e.projectPage("next")}}}):l("img",{staticStyle:{width:"20px",height:"20px"},attrs:{src:a(61368)}})])])],1),l("ta-echarts",{ref:"projectChart",staticStyle:{height:"calc(100% - 30px)",width:"100%"},attrs:{option:e.projectOption}}),l("div",{directives:[{name:"show",rawName:"v-show",value:e.visibleClass,expression:"visibleClass"}],staticStyle:{position:"absolute",top:"7%",left:"60%",width:"40%",height:"50%"},on:{mouseout:e.mouseout}},[l("ta-echarts",{key:(new Date).getTime(),ref:"classChart",staticStyle:{height:"100%",width:"100%"},attrs:{option:e.classOption}})],1),l("div",{staticStyle:{background:"#F5FAFF",border:"1px solid #2290FF","border-radius":"2px","font-family":"PingFangSC-Regular","font-size":"14px",color:"#2290FF",width:"50px","text-align":"center",position:"absolute",top:"20%",left:"90%",cursor:"pointer"},on:{mouseover:e.mouseover}},[e._v(" 分类 ")])],1)])],1),l("ta-tab-pane",{key:"3",staticClass:"tabs-pane-content rule-content-box",attrs:{tab:"违规类别对照"}},[l("div",{staticClass:"rule-type-form"},[l("ta-radio-group",{staticStyle:{width:"200px"},on:{change:e.onChangeContrast},model:{value:e.contrastValue,callback:function(t){e.contrastValue=t},expression:"contrastValue"}},[l("ta-radio",{attrs:{value:1}},[e._v(" 对照失败 ")]),l("ta-radio",{attrs:{value:2}},[e._v(" 对照成功 ")])],1),l("ta-input-search",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入违规描述"},on:{search:e.onSearchViolationDes}}),l("ta-button",{staticStyle:{"margin-left":"10px"},attrs:{disabled:e.batchDisabled,type:"primary"},on:{click:function(t){return e.fnBatchContrastRule()}}},[e._v(" 批量对照 ")])],1),l("div",{staticClass:"rule-type-table"},[l("ta-big-table",{ref:"Table",attrs:{border:"","show-overflow":"",height:"auto",data:e.ruleTypeData,"auto-resize":""},on:{"checkbox-all":e.ruleSelectAllEvent,"checkbox-change":e.ruleSelectChangeEvent}},[l("ta-big-table-column",{attrs:{type:"checkbox",align:"center",width:"80"}}),l("ta-big-table-column",{attrs:{"show-overflow":"",field:"ruleDec",align:"center",title:"违规描述","min-width":"500"}}),l("ta-big-table-column",{attrs:{"show-overflow":"",field:"ruleGroup",align:"center",title:"违规类别","min-width":"300"}}),l("ta-big-table-column",{attrs:{field:"status",align:"center",title:"对照状态",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[a.ruleGroup?l("div",{staticClass:"constrast-status-succeed"},[l("span",{staticClass:"status-circle"}),l("span",[e._v("成功")])]):l("div",{staticClass:"constrast-status-failed"},[l("span",{staticClass:"status-circle"}),l("span",[e._v("失败")])])]}}])}),l("ta-big-table-column",{attrs:{field:"operate",title:"操作",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[l("div",[l("span",{staticClass:"oparete-item",on:{click:function(t){return e.fnContrastRule(a)}}},[e._v("对照维护")])])]}}])}),l("template",{slot:"bottomBar"},[l("ta-pagination",{ref:"ruleTablePager",staticStyle:{"text-align":"right"},attrs:{"default-page-size":10,"data-source":e.ruleTypeData,params:e.rulePageParams,url:"centerAnalysis/queryAe04"},on:{"update:dataSource":function(t){e.ruleTypeData=t},"update:data-source":function(t){e.ruleTypeData=t}}})],1)],2)],1)])],1)],1)])],1),l("div",{attrs:{id:"proModal"}},[l("ta-modal",{attrs:{footer:null,width:"95%",height:"600px","get-container":e.getModalContainer},on:{cancel:e.handleCancel},model:{value:e.proVisible,callback:function(t){e.proVisible=t},expression:"proVisible"}},[l("h3",{staticStyle:{"text-align":"center","font-weight":"bolder"},attrs:{slot:"title"},slot:"title"},[e._v(" 项目详情 ")]),l("ta-table",{ref:"tablePro",staticStyle:{height:"480px"},attrs:{size:"small",columns:e.columns_allDetail,"data-source":e.proDetailData,scroll:{y:470,x:1500},bordered:!0},on:{"update:columns":function(t){e.columns_allDetail=t}}}),l("ta-pagination",{ref:"gridPager",staticStyle:{float:"right","margin-top":"35px"},attrs:{"data-source":e.proDetailData,params:e.getParam,url:"centerAnalysis/detailData"},on:{"update:dataSource":function(t){e.proDetailData=t},"update:data-source":function(t){e.proDetailData=t}}})],1)],1),e.contrastModelVisible?l("ta-modal",{staticClass:"contrast-model-content",attrs:{title:"对照维护",width:"420px",height:"200px"},on:{cancel:e.contrastModelCancel,ok:e.contrastModelHandleOk},model:{value:e.contrastModelVisible,callback:function(t){e.contrastModelVisible=t},expression:"contrastModelVisible"}},[l("div",{attrs:{id:"contrast-model-content"}},[l("ta-form",{attrs:{"auto-form-create":function(e){t.contrastForm=e}}},[l("ta-form-item",{attrs:{"field-decorator-options":{rules:[{required:!0,message:"请选择违规类别"}]},label:"违规类别","label-width":80,"field-decorator-id":"ruleType"}},[l("ta-select",{attrs:{mode:"default",value:e.ruleTypeValue,"allow-clear":!0,"show-search":!0,options:e.violationTypeList,placeholder:"请选择违规类别","get-popup-container":e.setPopupContainer},on:{search:e.ruleTypeHandleSearch,change:e.ruleTypeHandleChange}})],1)],1)],1)]):e._e(),l("ta-modal",{staticClass:"upload-content-box",attrs:{title:"中心审核数据上传",width:"500px",height:"300px"},on:{cancel:e.uploadHandleCancel,ok:e.uploadHandleOk},model:{value:e.uploadModalVisible,callback:function(t){e.uploadModalVisible=t},expression:"uploadModalVisible"}},[l("template",{slot:"footer"},[l("div",{staticClass:"footer-button"},[l("ta-button",{on:{click:e.uploadHandleCancel}},[e._v(" 取消 ")]),l("ta-button",{attrs:{type:"primary"},on:{click:e.uploadHandleOk}},[e._v(" 上传 ")])],1)]),l("div",{attrs:{id:"upload-content-box"}},[l("ta-form",{attrs:{"auto-form-create":function(e){t.uploadForm=e},enctype:"multipart/form-data"}},[l("ta-form-item",{attrs:{"field-decorator-id":"aae043",require:{message:"请选择期号"},"label-col":{span:5},"wrapper-col":{span:16}}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("期号选择")]),l("ta-month-picker",{staticStyle:{width:"100%"},attrs:{"get-calendar-container":e.setCalendarContainer,format:"YYYYMM"}})],1),l("ta-form-item",{attrs:{"field-decorator-id":"file",require:{message:"请选择文件"},"label-col":{span:5},"wrapper-col":{span:18}}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("选择文件")]),l("ta-upload",{staticClass:"upload-list-inline",attrs:{name:"fileUpload",multiple:!1,action:"","file-list":e.fileList,accept:".xls,.xlsx","before-upload":e.beforeUpload}},[l("ta-button",[l("ta-icon",{attrs:{type:"upload"}}),e._v("浏览 ")],1)],1)],1)],1)],1)],2)],1)},l=[],s=a(66347),o=a(89584),r=a(48534),n=(a(32564),a(36133),a(88412)),c=a(9063),d=a(75969),u=a(36797),h=a.n(u),p={name:"centerAnalysisPage1",components:{TaFormItem:c.Z,TaTitle:n.Z},data:function(){var t=[{title:"科室名称",dataIndex:"aae386",width:80,align:"left",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"医师名称",dataIndex:"aaz570",width:80,align:"left",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"参保人",dataIndex:"aac003",width:70,align:"left",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"就诊流水号",dataIndex:"akc190",width:90,align:"left",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"入院日期",dataIndex:"aae030",width:90,align:"center",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"出院日期",dataIndex:"aae031",width:90,align:"center",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"三目编码",dataIndex:"ake001",width:160,align:"left",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"三目名称",dataIndex:"ake002",width:150,align:"left",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"单价",dataIndex:"akc225",width:60,align:"right",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"数量",dataIndex:"akc226",width:60,align:"right",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"总费用",dataIndex:"akb065",width:60,align:"right",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"扣款金额",dataIndex:"ape804",width:80,align:"right",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"规则名称",dataIndex:"aaa167",width:200,align:"left",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"扣款理由",dataIndex:"ykz018",width:200,align:"left",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"违规类别",dataIndex:"ruleGroup",width:140,align:"left",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"费用日期",dataIndex:"aae036",width:90,align:"center",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"结算日期",dataIndex:"akc194",width:90,align:"center",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell}],e=[{title:"科室名称",dataIndex:"kdksmc",width:120,align:"left",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"违规类别",dataIndex:"ruleGroup",width:140,align:"left",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell,scopedSlots:{customRender:"ruleGroup"}},{title:"医保应支付总费用",dataIndex:"yzffy",width:150,align:"right",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"医保支付总费用",dataIndex:"zffy",width:100,align:"right",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"扣款金额",dataIndex:"kkje",width:80,align:"right",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"违规人次",dataIndex:"wgrc",width:80,align:"right",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"医保支付扣款率",dataIndex:"kkl",width:100,align:"right",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"违规费用占比",dataIndex:"wgfyzb",width:80,align:"right",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell}],a=[{title:"规则名称",dataIndex:"gzm",width:140,align:"left",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"违规类别",dataIndex:"ruleGroup",width:140,align:"left",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell,scopedSlots:{customRender:"ruleGroup"}},{title:"医保应支付总费用",dataIndex:"yzffy",width:100,align:"right",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"医保支付总费用",dataIndex:"zffy",width:100,align:"right",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"扣款金额",dataIndex:"kkje",width:80,align:"right",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"违规人次",dataIndex:"wgrc",width:80,align:"right",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"医保支付扣款率",width:100,dataIndex:"kkl",align:"right",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"违规费用占比",width:80,dataIndex:"wgfyzb",align:"right",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell}],i=[{title:"项目名称",dataIndex:"ake002",width:120,align:"left",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"医保应支付总费用",dataIndex:"yzffy",width:100,align:"right",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"医保支付总费用",dataIndex:"zffy",width:100,align:"right",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"扣款金额",dataIndex:"kkje",width:80,align:"right",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"违规人次",dataIndex:"wgrc",width:80,align:"right",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"医保支付扣款率",dataIndex:"kkl",width:100,align:"right",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"违规费用占比",dataIndex:"wgfyzb",width:80,align:"right",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell}],l=[{title:"三目编码",dataIndex:"ake001",width:100,align:"left",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"三目名称",dataIndex:"ake002",width:80,align:"left",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"科室名称",dataIndex:"aae386",width:80,align:"left",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"医师名称",dataIndex:"aaz570",width:80,align:"left",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"参保人名称",dataIndex:"aac003",width:80,align:"left",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"发生金额",dataIndex:"akb065",width:80,align:"right",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"扣款金额",dataIndex:"ape804",width:80,align:"right",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"扣款理由",dataIndex:"ykz018",width:180,align:"left",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"费用发生日期",dataIndex:"aae036",width:120,align:"center",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell}];return{pane_id:"1",columns_allDetail:t,hosPartSelection:[{label:"全部",value:""}],columns_dept:e,columns_rule:a,columns_project:i,columns_proDetail:l,detailData:[],deptData:[],ruleData:[],projectData:[],proDetailData:[],costOption:{},violationOption:{},deptOption:{},ruleOption:{},doctorOption:{},projectOption:{},classOption:{},kdksbm:"",gzm:"",ruleName:"",kdysbm:"",ake001:"",ake003:"",aae043:"",kdksmc:"",kdysmc:"",kdksmctitle:"",kdysmctitle:"",aaa103:"",proVisible:!1,activeKey:"detail",visibleClass:!1,showLastDept:!1,showNextDept:!1,showLastRule:!1,showNextRule:!1,showLastDoctor:!1,showNextDoctor:!1,showLastProject:!1,showNextProject:!1,page:5,xmPage:10,ksstart:0,gzstart:0,ysstart:0,xmstart:0,fileList:[],ksDataSource:[],ysDataSource:[],tabsActiveKey:"1",ruleTypeData:[],ruleTypeAllData:[],contrastValue:1,contrastModelVisible:!1,contrastModelData:[],contrastForm:null,violationTypeList:[],violationTypeListBackup:[],volationDesFilter:"",ruleContrastSeleced:[],isBatch:!1,uploadModalVisible:!1,ruleTypeValue:"",ruleFlag:!1}},computed:{batchDisabled:function(){return!this.ruleContrastSeleced.length>0},isShowruleStastic:function(){var t=["dept","rule"];return"1"===this.tabsActiveKey&&t.includes(this.activeKey)},deptTableColumn:function(){return this.ruleFlag?this.columns_dept:this.columns_dept.filter((function(t){return"ruleGroup"!==t.dataIndex}))},ruleTableColumn:function(){return this.ruleFlag?this.columns_rule:this.columns_rule.filter((function(t){return"ruleGroup"!==t.dataIndex}))}},mounted:function(){var t=this;return(0,r.Z)(regeneratorRuntime.mark((function e(){var a,i;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,Base.asyncGetCodeData("AAZ108");case 2:i=e.sent,(a=t.hosPartSelection).push.apply(a,(0,o.Z)(i)),t.formQuery.setFieldsValue({aae043:h()(new Date,"YYYYMM")}),t.fnQueryDetail();case 6:case"end":return e.stop()}}),e)})))()},methods:{callBack:function(t){this.pane_id=t,this.tabsActiveKey=t,"1"===t?this.fnQueryDetail():"2"===t?this.fnQuery():"3"===t&&this.fnQueryRuleContrast()},disabledDate:function(t){var e=h()(new Date);return!(!t||!e)&&t.valueOf()>e.valueOf()},fnCustomRow:function(t,e){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px"}}},fnCustomHeaderCell:function(t){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px",fontWeight:"bold"}}},moment:h(),handleSearchKs:function(t){var e=this;this.Base.submit(null,{url:"centerAnalysis/queryKf06",data:{name:t},autoValid:!0},{successCallback:function(t){e.ksDataSource=t.data.ksDataSource},failCallback:function(t){}})},handleSearchYs:function(t){var e=this;this.Base.submit(null,{url:"centerAnalysis/queryKf05",data:{name:t},autoValid:!0},{successCallback:function(t){e.ysDataSource=t.data.ysDataSource},failCallback:function(t){}})},getDetailModal:function(){return document.getElementById("detailModal")},getModalContainer:function(){return document.getElementById("proModal")},mouseover:function(){this.visibleClass=!0},mouseout:function(){this.visibleClass=!1},detailParams:function(){var t="";void 0!=this.formQuery.getFieldValue("aae140")&&(t=-1!=this.formQuery.getFieldValue("aae140").indexOf("1")?"1":this.formQuery.getFieldValue("aae140"));var e={aae043:this.formQuery.getFieldMomentValue("aae043"),aaz108:this.formQuery.getFieldValue("aaz108"),aae141:this.formQuery.getFieldValue("aae141"),aae140:t,aka130:this.formQuery.getFieldValue("aka130")};return this.isShowruleStastic&&(e.ruleFlag=this.ruleFlag),e},deptPage:function(t){"next"==t?(this.ksstart+=this.page,this.showLastDept=!0):(this.ksstart-=this.page,0==this.ksstart&&(this.showLastDept=!1));var e=this.getParam();this.deptRanking(e)},rulePage:function(t){"next"==t?(this.gzstart+=this.page,this.showLastRule=!0):(this.gzstart-=this.page,0==this.gzstart&&(this.showLastRule=!1));var e=this.getParam();this.ruleRanking(e)},doctorPage:function(t){"next"==t?(this.ysstart+=this.page,this.showLastDoctor=!0):(this.ysstart-=this.page,0==this.ysstart&&(this.showLastDoctor=!1));var e=this.getParam();this.doctorRanking(e)},projectPage:function(t){"next"==t?(this.xmstart+=this.xmPage,this.showLastProject=!0):(this.xmstart-=this.xmPage,0==this.xmstart&&(this.showLastProject=!1));var e=this.getParam();this.projectRanking(e)},handleCancel:function(t){this.proVisible=!1},fnDownload:function(){var t=this;Base.downloadFile({method:"post",fileName:"中心审核数据导入模板.xls",url:"centerAnalysis/downLoad"}).then((function(e){t.$message.success("下载成功")})).catch((function(e){t.$message.error("下载失败")}))},beforeUpload:function(t){return this.fileList=[],this.fileList.push(t),!1},fnUpload:function(){var t=this,e={url:"centerAnalysis/upload",data:{uploadFile:this.fileList[0],aae043:this.uploadForm.getFieldMomentValue("aae043")},isFormData:!0,autoValid:!0},a={successCallback:function(e){t.fileList=[],t.$message.success("上传成功");var a=h()(t.uploadForm.getFieldMomentValue("aae043"),"YYYYMM");t.formQuery.setFieldsValue({aae043:a}),t.uploadModalVisible=!1,"0"===e.data.ruleGroupFlag?t.getMatchingRule():t.fnQueryDetail()},failCallback:function(e){t.fileList=[],t.$message.error("上传失败")}};Base.submit(this.from,e,a)},getParam:function(){var t="";void 0!=this.formQuery.getFieldValue("aae140")&&(t=-1!=this.formQuery.getFieldValue("aae140").indexOf("1")?"1":this.formQuery.getFieldValue("aae140"));var e={aae043:this.formQuery.getFieldMomentValue("aae043"),aaz108:this.formQuery.getFieldValue("aaz108"),aae141:this.formQuery.getFieldValue("aae141"),aae140:t,aka130:this.formQuery.getFieldValue("aka130"),kdksbm:this.kdksbm,kdksmc:this.kdksmc,kdysbm:this.kdysbm,kdysmc:this.kdysmc,gzm:this.gzm,ake001:this.ake001,ake003:this.ake003};return e},initParam:function(t){"all"==t?(this.kdksbm="",this.gzm="",this.ruleName="",this.kdysbm="",this.ake003="",this.aae043="",this.kdksmc="",this.kdysmc="",this.kdksmctitle="",this.kdysmctitle="",this.aaa103=""):"trend"==t?(this.kdksbm="",this.gzm="",this.ruleName="",this.kdysbm="",this.ake003="",this.kdksmc="",this.kdysmc="",this.kdksmctitle="",this.kdysmctitle="",this.aaa103=""):"dept"==t?(this.gzm="",this.ruleName="",this.kdysbm="",this.ake003="",this.kdysmc="",this.kdysmctitle="",this.aaa103=""):"rule"==t?(this.kdysbm="",this.ake003="",this.kdysmc="",this.kdysmctitle="",this.aaa103=""):"doctor"==t&&(this.ake003="",this.aaa103="")},fnQuery:function(){if(void 0!=this.formQuery.getFieldMomentValue("aae043")&&null!=this.formQuery.getFieldMomentValue("aae043")){this.initParam("all");var t=this.getParam();this.overallSituation(t),this.deductionsTrend(t),this.deptRanking(t),this.ruleRanking(t),this.doctorRanking(t),this.projectRanking(t),this.class(t)}else this.$message.error("请选择期号！！")},fnQueryDetail:function(){"1"===this.pane_id?this.fnDetailInfo():this.fnQuery()},fnDetailInfo:function(){this.$refs.detailPager.loadData(),this.$refs.deptPager.loadData(),this.$refs.rulePager.loadData(),this.$refs.projectPager.loadData()},overallSituation:function(t){var e=this;Base.submit(null,{url:"centerAnalysis/overallSituation",data:t}).then((function(t){e.costOption=d.Z.createPieHalf(t.data.feeList,"name","val","",""),e.$refs.costChart.updateOptions(e.costOption)}))},deductionsTrend:function(t){var e=this;Base.submit(null,{url:"centerAnalysis/deductionsTrend",data:t}).then((function(t){e.violationOption=d.Z.createLine(t.data.trendList,"name","val","","元"),e.$refs.violationChart.updateOptions(e.violationOption),e.$refs.violationChart.myChart.off("click"),e.$refs.violationChart.myChart.on("click",(function(t){e.deductionsTrendClick(t)}))}))},deptRanking:function(t){var e=this;t.ksPage=this.page,t.ksstart=this.ksstart,Base.submit(null,{url:"centerAnalysis/deptRanking",data:t}).then((function(t){t.data.deptList.length<e.page?e.showNextDept=!1:e.showNextDept=!0,e.deptOption=d.Z.createBar(t.data.deptList,"name","val","元"),e.$refs.deptChart.updateOptions(e.deptOption),e.$refs.deptChart.myChart.off("click"),e.$refs.deptChart.myChart.on("click",(function(t){e.deptRankingClick(t)}))}))},ruleRanking:function(t){var e=this;t.gzPage=this.page,t.gzstart=this.gzstart,Base.submit(null,{url:"centerAnalysis/ruleRanking",data:t}).then((function(t){t.data.ruleList.length<e.page?e.showNextRule=!1:e.showNextRule=!0,e.ruleOption=d.Z.createBar(t.data.ruleList,"name","val","元"),e.$refs.ruleChart.updateOptions(e.ruleOption),e.$refs.ruleChart.myChart.off("click"),e.$refs.ruleChart.myChart.on("click",(function(t){e.ruleRankingClick(t)}))}))},doctorRanking:function(t){var e=this;t.ysPage=this.page,t.ysstart=this.ysstart,Base.submit(null,{url:"centerAnalysis/doctorRanking",data:t}).then((function(t){t.data.doctorList.length<e.page?e.showNextDoctor=!1:e.showNextDoctor=!0,e.doctorOption=d.Z.createBar(t.data.doctorList,"name","val","元"),e.$refs.doctorChart.updateOptions(e.doctorOption),e.$refs.doctorChart.myChart.off("click"),e.$refs.doctorChart.myChart.on("click",(function(t){e.doctorRankingClick(t)}))}))},projectRanking:function(t){var e=this;t.xmPage=this.xmPage,t.xmstart=this.xmstart,Base.submit(null,{url:"centerAnalysis/projectRanking",data:t}).then((function(t){t.data.projectList.length<e.xmPage?e.showNextProject=!1:e.showNextProject=!0,e.projectOption=d.Z.createStandBar(t.data.projectList,"name","val","元","id"),e.$refs.projectChart.updateOptions(e.projectOption),e.$refs.projectChart.myChart.off("click"),e.$refs.projectChart.myChart.on("click",(function(t){e.projectRankingClick(t)}))}))},class:function(t){var e=this;Base.submit(null,{url:"centerAnalysis/classification",data:t}).then((function(t){e.classOption=d.Z.createPieRound(t.data.classList,"name","val","元","id"),e.$refs.classChart.updateOptions(e.classOption),e.$refs.classChart.myChart.off("click"),e.$refs.classChart.myChart.on("click",(function(t){e.classClick(t)}))}))},deductionsTrendClick:function(t){this.formQuery.setFieldsValue({aae043:this.moment(t.name,"YYYYMM")}),this.initParam("trend"),this.aae043="【"+t.name+"】";var e=this.getParam();this.overallSituation(e),this.deptRanking(e),this.ruleRanking(e),this.doctorRanking(e),this.projectRanking(e),this.class(e)},deptRankingClick:function(t){this.initParam("dept"),this.kdksbm=t.data.id,this.kdksmc=t.name,this.kdksmctitle="【"+t.name+"】";var e=this.getParam();this.ruleRanking(e),this.doctorRanking(e),this.projectRanking(e),this.class(e)},ruleRankingClick:function(t){this.initParam("rule"),this.gzm=t.name,this.ruleName="【"+t.name+"】";var e=this.getParam();this.deptRanking(e),this.doctorRanking(e),this.projectRanking(e),this.class(e)},doctorRankingClick:function(t){this.initParam("doctor"),this.kdysbm=t.data.id,this.kdysmc=t.name,this.kdysmctitle="【"+t.name+"】";var e=this.getParam();this.ruleRanking(e),this.projectRanking(e),this.class(e)},projectRankingClick:function(t){var e=this;this.proVisible=!0,this.ake001=t.data.id,setTimeout((function(){e.searchPro()}))},searchPro:function(){this.$refs.gridPager.loadData((function(t){}))},classClick:function(t){this.ake003=t.data.id,this.aaa103="【"+t.name+"】";var e=this.getParam();this.projectRanking(e)},fnTabChange:function(t){this.fnQueryData()},fnQueryData:function(){"dept"===this.activeKey?this.$refs.deptPager.loadData():"rule"===this.activeKey&&this.$refs.rulePager.loadData()},rulePageParams:function(){var t={};return t.queryText=this.volationDesFilter,t.type=1===this.contrastValue?"fail":"success",t},getMatchingRule:function(){this.tabsActiveKey="3",this.$message.info("存在无法匹配的数据,如果不修改，则无法生成扣款统计",10),this.fnQueryRuleContrast()},fnQueryRuleContrast:function(){var t=this;this.$nextTick((function(){t.$refs.ruleTablePager.loadData((function(t){}))}))},onChangeContrast:function(){this.ruleContrastSeleced=[],this.fnQueryRuleContrast()},onSearchViolationDes:function(t){this.volationDesFilter=t,this.fnQueryRuleContrast()},ruleSelectChangeEvent:function(t){t.checked;var e=t.records;this.ruleContrastSeleced=e},ruleSelectAllEvent:function(t){t.checked;var e=t.records;this.ruleContrastSeleced=e},fnContrastRule:function(t){this.contrastModelData=[t],this.isBatch=!1,this.contrastModelVisible=!0,this.getViolationTypeList()},fnBatchContrastRule:function(){this.contrastModelVisible=!0,this.isBatch=!0,this.getViolationTypeList()},getViolationTypeList:function(){var t=this;this.Base.submit(null,{url:"centerAnalysis/queryRuleGroup",autoValid:!1},{successCallback:function(e){t.violationTypeList=e.data.ruleGrouList.map((function(t){var e={value:t,label:t};return e})),t.violationTypeListBackup=JSON.parse(JSON.stringify(t.violationTypeList))},failCallback:function(e){t.$message.error("违规类别数据加载失败")}})},contrastModelCancel:function(t){this.contrastModelVisible=!1},contrastModelHandleOk:function(){var t=this;this.contrastForm.validateFields();var e=this.contrastForm.getFieldError("ruleType");if(e)return!1;var a=this.contrastForm.getFieldsValue(),i=[],l=this.isBatch?this.ruleContrastSeleced:this.contrastModelData;i=l.map((function(t){return t.ruleGroup=a.ruleType,t}));var s={ae04List:JSON.stringify(i)};this.Base.submit(null,{url:"centerAnalysis/updateAe04",data:s,autoValid:!1},{successCallback:function(e){t.contrastModelVisible=!1,t.fnQueryRuleContrast()},failCallback:function(e){t.$message.error("违规类别更新失败")}})},fnExportAllData:function(){var t=this,e={aaz108:this.formQuery.getFieldValue("aaz108")},a="",i=[],l="";"detail"===this.activeKey?(a="centerAnalysis/queryDetailByAae043",e.aae043=this.formQuery.getFieldMomentValue("aae043"),i=this.columns_allDetail,l="扣款明细"):"dept"===this.activeKey?(a="centerAnalysis/deptDetail",e=this.getParam(),i=this.ruleFlag?this.columns_dept:this.columns_dept.filter((function(t){return"ruleGroup"!==t.dataIndex})),l="科室扣款统计"):"rule"===this.activeKey?(a="centerAnalysis/ruleDetail",e=this.getParam(),i=this.ruleFlag?this.columns_rule:this.columns_rule.filter((function(t){return"ruleGroup"!==t.dataIndex})),l="规则扣款统计"):"project"===this.activeKey&&(a="centerAnalysis/projectDetail",e=this.getParam(),i=this.columns_project,l="项目扣款统计"),Base.submit(null,{url:a,params:e}).then((function(a){var o,r=[],n=(0,s.Z)(i);try{for(n.s();!(o=n.n()).done;){var c=o.value;r.push({header:c.title,key:c.dataIndex,width:20})}}catch(u){n.e(u)}finally{n.f()}var d={fileName:"".concat(l).concat(e.aae043),sheets:[{name:"worksheet1",column:{complex:!1,columns:r},rows:t.getTableExportData(a)}]};t.Base.generateExcel(d)}))},getTableExportData:function(t){return"detail"===this.activeKey?t.data.detailList:t.data.pageBean.list},centerAnalysisUpload:function(){var t=this;this.uploadModalVisible=!0,this.$nextTick((function(){t.uploadForm.setFieldsValue({aae043:h()(new Date,"YYYYMM")})}))},uploadHandleCancel:function(){this.uploadModalVisible=!1,this.fileList=[]},uploadHandleOk:function(){this.uploadForm.validateFields();var t=this.uploadForm.getFieldError("aae043")||this.uploadForm.getFieldError("file");if(t)return!1;this.fnUpload()},ruleTypeHandleSearch:function(t){if(!t)return!1;var e=this.violationTypeList.some((function(e){return e.value.includes(t)}));if(!e){var a=[],i={value:t,label:t};a.push(i),this.violationTypeList=JSON.parse(JSON.stringify(a))}},ruleTypeHandleChange:function(t){if(!t)return!1;var e=this.violationTypeListBackup.some((function(e){return e.value.includes(t)}));if(!e){var a={value:t,label:t};this.violationTypeListBackup.push(a),this.violationTypeList=JSON.parse(JSON.stringify(this.violationTypeListBackup))}},setPopupContainer:function(){return document.getElementById("contrast-model-content")},setCalendarContainer:function(){return document.getElementById("upload-content-box")},onChangeRuleFlag:function(t){this.ruleFlag=t.target.checked,this.fnQueryData()}}},f=p,m=a(1001),g=(0,m.Z)(f,i,l,!1,null,"ec58457c",null),y=g.exports},36766:function(t,e,a){var i=a(66586);e["Z"]=i.Z},26263:function(t,e,a){a.d(e,{s:function(){return i},x:function(){return l}});var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},l=[]},66586:function(t,e){var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};e["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}}}]);
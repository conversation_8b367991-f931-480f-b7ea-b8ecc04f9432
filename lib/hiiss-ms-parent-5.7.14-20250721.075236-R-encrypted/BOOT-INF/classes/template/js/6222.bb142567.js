(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[6222],{88412:function(t,e,a){"use strict";var i=a(26263),l=a(36766),n=a(1001),r=(0,n.Z)(l.Z,i.s,i.x,!1,null,"5e7ef0ae",null);e["Z"]=r.exports},60700:function(t,e,a){"use strict";a.r(e),a.d(e,{default:function(){return m}});var i=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("div",{staticClass:"fit"},[i("ta-border-layout",{attrs:{"footer-cfg":{showBorder:!1},layout:{header:"110px",footer:"0px"},showPadding:!0}},[i("div",{attrs:{slot:"header"},slot:"header"},[i("ta-title",{attrs:{title:"查询条件"}}),i("ta-form",{staticStyle:{"padding-top":"5px"},attrs:{autoFormCreate:function(e){t.baseInfoForm=e},formLayout:!0,layout:"horizontal"}},[i("ta-form-item",{attrs:{labelCol:{span:8},span:8,wrapperCol:{span:14},fieldDecoratorId:"dateType","init-value":"1",label:"监测日期"}},[i("ta-radio-group",{attrs:{defaultValue:"1",options:e.plainOptions}})],1),i("ta-button",{staticStyle:{"margin-right":"10px",float:"right"},attrs:{icon:"redo",type:"default"},on:{click:e.fnReset}},[e._v("重置")]),i("ta-button",{staticStyle:{"margin-right":"20px",float:"right"},attrs:{icon:"search",type:"primary"},on:{click:e.fnQuery}},[e._v("查询 ")])],1)],1),i("div",{staticClass:"fit",staticStyle:{height:"87%"}},[i("ta-title",{staticStyle:{width:"50%"},attrs:{title:"查询结果"}}),i("ta-big-table",{ref:"Table",attrs:{data:e.tableData,"auto-resize":"",border:"","empty-text":"-",height:"100%","highlight-current-row":"","highlight-hover-row":"",resizable:"","show-overflow":""}},[i("ta-big-table-column",{attrs:{align:"left","header-align":"left","min-width":"50",sortable:"",title:"序号",type:"seq"}}),i("ta-big-table-column",{attrs:{align:"left",field:"exDate","header-align":"left","min-width":"180",title:"日期"}}),i("ta-big-table-column",{attrs:{align:"left",field:"exType","header-align":"left","min-width":"180",title:"异常类型"}}),i("ta-big-table-column",{attrs:{align:"left",field:"auditScen","header-align":"left","min-width":"150",title:"审核业务"}}),i("ta-big-table-column",{attrs:{align:"left",field:"akc191","header-align":"left","min-width":"180",title:"就诊号"}})],1)],1)])],1)},l=[],n=a(95082),r=a(88412),s=a(36797),o=a.n(s),c=a(22722),u=a(55115);u.w3.prototype.Base=Object.assign(u.w3.prototype.Base,(0,n.Z)({},c.Z));var f=[{label:"三日内",value:"1"},{label:"一周内",value:"2"},{label:"一月内",value:"3"}],h={name:"exceptionStatistics",components:{TaTitle:r.Z},data:function(){return{tableData:[],plainOptions:f,rangeValue:[this.Base.getMoment((new Date).toISOString().slice(0,8)+"-01","YYYY-MM-DD"),this.Base.getMoment((new Date).toISOString().slice(0,10),"YYYY-MM-DD")]}},methods:{moment:o(),fnReset:function(){this.baseInfoForm.resetFields(),this.fnQuery()},fnQuery:function(){var t=this,e=this.baseInfoForm.getFieldValue("dateType");if(e){var a=this.baseInfoForm.getFieldsValue();this.Base.submit(null,{url:"reportStatistics/queryExceptionList",data:a,autoValid:!0},{successCallback:function(e){t.tableData=e.data.data},failCallback:function(e){t.$message.error("数据加载失败")}})}else this.$message.error("请选择监测日期！")},infoPageParams:function(){var t=this.baseInfoForm.getFieldsValue();return t}}},d=h,g=a(1001),p=(0,g.Z)(d,i,l,!1,null,"63103e7c",null),m=p.exports},36766:function(t,e,a){"use strict";var i=a(66586);e["Z"]=i.Z},26263:function(t,e,a){"use strict";a.d(e,{s:function(){return i},x:function(){return l}});var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},l=[]},66586:function(t,e){"use strict";var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};e["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}},55382:function(){},61219:function(){}}]);
"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[2724],{12724:function(t,e,a){a.r(e),a.d(e,{default:function(){return v}});var i=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("ta-border-layout",{attrs:{"show-padding":!1,layout:{footer:"0px"}}},[i("ta-card",{attrs:{bordered:!1}},[i("div",{staticClass:"title"},[e._v("查询条件")]),i("ta-row",[i("ta-form",{attrs:{autoFormCreate:function(e){t.searchForm=e},"form-layout":!0,require:{message:"操作时间不能为空"}}},[i("ta-form-item",{attrs:{span:4,label:"任务名称",fieldDecoratorId:"jobName"}},[i("ta-input",{attrs:{placeholder:"请输入"}})],1),i("ta-form-item",{attrs:{fieldDecoratorId:"jobType",span:4,label:"任务类型"}},[i("ta-select",{attrs:{placeholder:"请选择","collection-type":"M_JOBTYPE",allowClear:!0,"show-search":!0}})],1),i("ta-form-item",{attrs:{fieldDecoratorId:"jobStatus",span:4,label:"任务状态"}},[i("ta-select",{attrs:{placeholder:"请选择","collection-type":"M_JOBSTATUS",allowClear:!0,"show-search":!0}})],1),i("ta-form-item",{attrs:{fieldDecoratorId:"operationtime",span:4,label:"执行时间"}},[i("ta-range-picker",{attrs:{placeholder:["开始时间","结束时间"],"allow-clear":!0}})],1),i("ta-form-item",{attrs:{span:6}},[i("ta-button",{staticStyle:{"margin-left":"50px"},attrs:{span:2},on:{click:e.resetFields}},[e._v("重置")]),i("ta-button",{attrs:{type:"primary"},on:{click:e.doSearch}},[e._v("查询")])],1)],1)],1)],1),i("ta-card",{attrs:{bordered:!1}},[i("div",{staticClass:"title"},[e._v("任务列表 "),i("div",{staticStyle:{float:"right"}},[i("ta-button",{attrs:{type:""},on:{click:function(){t.blnSqlImportVisible=!0,t.fileList=[]}}},[e._v("导入基板批次")]),i("ta-dropdown",[i("ta-menu",{attrs:{slot:"overlay"},slot:"overlay"},[i("ta-menu-item",[i("span",{staticClass:"mg-l12",on:{click:e.openAddDalyJob}},[e._v("定时日任务")])]),i("ta-menu-divider"),i("ta-menu-item",[i("span",{staticClass:"mg-l12",on:{click:e.openAddOnceJob}},[e._v("日常任务")])])],1),i("ta-button",{staticStyle:{"margin-right":"10px"},attrs:{type:"success"}},[e._v(" 新增 "),i("ta-icon",{attrs:{type:"down"}})],1)],1),i("ta-button",{attrs:{type:"primary"},on:{click:e.openDalyJobTab}},[e._v("定时任务")])],1)]),i("ta-table",{attrs:{size:"small","have-sn":!0,columns:e.columns,"data-source":e.dataList,scroll:{x:"100%",y:420}},scopedSlots:e._u([{key:"operate",fn:function(t,a){return i("div",{},["1"!=a.jobType||a.cron||"0"==a.jobStatus||"1"==a.jobStatus?e._e():i("a",{on:{click:function(t){return e.openChooseProfile(a)}}},[e._v("立刻执行")]),i("a",{staticStyle:{"margin-left":"5px"},on:{click:function(t){return e.openJobDetail(a)}}},[e._v("详情")]),"0"==a.jobStatus||"1"==a.jobStatus?i("ta-popconfirm",{attrs:{title:"确认取消？"},on:{confirm:function(t){return e.cancelJob(a)}}},[i("a",{staticStyle:{"margin-left":"5px"}},[e._v("取消")])]):e._e()],1)}}])}),i("div",[i("ta-pagination",{ref:"pager",staticClass:"page",attrs:{defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"],dataSource:e.dataList,params:e.pageParams,url:e.moduleUrl+"/queryPage"},on:{"update:dataSource":function(t){e.dataList=t},"update:data-source":function(t){e.dataList=t}},model:{value:e.pageParam.pageNumber,callback:function(t){e.$set(e.pageParam,"pageNumber",t)},expression:"pageParam.pageNumber"}})],1)],1),i("ta-modal",{attrs:{width:1e3,title:"任务详情","destroy-on-close":!0},on:{ok:function(t){e.blnDetailVisible=!1}},model:{value:e.blnDetailVisible,callback:function(t){e.blnDetailVisible=t},expression:"blnDetailVisible"}},[i("ta-row",[i("ta-col",{attrs:{span:12}},[e._v("任务名称: "+e._s(e.ruleRecord.jobName))]),i("ta-col",{attrs:{span:12}},[e._v("任务状态: "+e._s(e.CollectionLabel("M_JOBSTATUS",e.ruleRecord.jobStatus)))])],1),i("ta-row",{staticStyle:{"margin-top":"10px"}},[i("ta-col",{attrs:{span:12}},[e._v("任务流水号: "+e._s(e.ruleRecord.jobId))]),i("ta-col",{attrs:{span:12}},[e._v("任务执行时间: "+e._s(e.ruleRecord.executeTimeDes))])],1),i("ta-row",{staticStyle:{"margin-top":"10px"}},[i("ta-col",{attrs:{span:12}},[e._v("任务说明: "+e._s(e.ruleRecord.note))])],1),i("ta-row",{staticStyle:{"margin-top":"10px"}},[i("ta-col",{attrs:{span:12}},[e._v("执行开始时间: "+e._s(e.ruleRecord.startTime))]),i("ta-col",{attrs:{span:12}},[e._v("执行结束时间: "+e._s(e.ruleRecord.endTime))])],1),i("ta-row",{staticStyle:{"margin-top":"10px"}},[i("ta-col",{attrs:{span:24}},[e._v("失败原因: "+e._s(e.ruleRecord.errInfo))])],1)],1),i("ta-modal",{attrs:{width:1e3,"destroy-on-close":!0,title:"新增定时日任务"},on:{ok:function(t){e.blnEditVisible=!1}},model:{value:e.blnDalyJobVisible,callback:function(t){e.blnDalyJobVisible=t},expression:"blnDalyJobVisible"}},[i("ta-form",{attrs:{"auto-form-create":function(e){return t.dalyForm=e},formLayout:!0}},[i("ta-form-item",{attrs:{span:22,label:"任务名称",fieldDecoratorId:"jobName",require:{message:"任务名称不能为空"}}},[i("ta-input",{attrs:{placeholder:"请输入","allow-clear":!0}})],1),i("ta-form-item",{attrs:{span:22,label:" ","label-width":"100px"}},[i("span",{staticStyle:{color:"red","margin-left":"42px"}},[e._v("*")]),i("span",[e._v("执行时间：每天的")]),i("ta-select",{staticStyle:{width:"100px",display:"inline-block","margin-left":"15px"},attrs:{options:e.hourList,"show-search":!0},model:{value:e.daly.hour,callback:function(t){e.$set(e.daly,"hour",t)},expression:"daly.hour"}}),i("span",{staticStyle:{"margin-left":"10px"}},[e._v("时")]),i("ta-select",{staticStyle:{width:"100px",display:"inline-block","margin-left":"10px"},attrs:{options:e.minuteList,"show-search":!0},model:{value:e.daly.minute,callback:function(t){e.$set(e.daly,"minute",t)},expression:"daly.minute"}}),i("span",{staticStyle:{"margin-left":"10px"}},[e._v("分")])],1),i("ta-form-item",{attrs:{span:22,label:"任务说明",fieldDecoratorId:"note"}},[i("ta-textarea",{attrs:{rows:4,placeholer:"请输入"}})],1)],1),i("div",{attrs:{slot:"footer"},slot:"footer"},[i("ta-button",{on:{click:function(t){e.blnDalyJobVisible=!1}}},[e._v("取消")]),i("ta-button",{attrs:{type:"primary"},on:{click:e.doSaveDalyJob}},[e._v("确定")])],1)],1),i("ta-modal",{attrs:{width:1e3,"destroy-on-close":!0,title:"新增日常任务"},on:{ok:function(t){e.blnOnceJobVisible=!1}},model:{value:e.blnOnceJobVisible,callback:function(t){e.blnOnceJobVisible=t},expression:"blnOnceJobVisible"}},[i("ta-form",{attrs:{"auto-form-create":function(e){return t.onceForm=e}}},[i("ta-form-item",{attrs:{span:22,label:"任务名称",fieldDecoratorId:"jobName",require:{message:"任务名称不能为空"}}},[i("ta-input",{attrs:{placeholder:"请输入","allow-clear":!0}})],1),i("ta-form-item",{attrs:{span:22,label:" ","label-width":"100px"}},[i("ta-radio-group",{staticStyle:{"margin-left":"138px"},on:{change:e.onTimeTypeChange},model:{value:e.once.timeType,callback:function(t){e.$set(e.once,"timeType",t)},expression:"once.timeType"}},[i("ta-radio",{attrs:{value:"1"}},[e._v("立刻执行")]),i("ta-radio",{attrs:{value:"2"}},[e._v("指定时间")])],1),"2"==e.once.timeType?i("ta-date-picker",{attrs:{format:"YYYY-MM-DD HH:mm:ss",showTime:{}},model:{value:e.once.exeTime,callback:function(t){e.$set(e.once,"exeTime",t)},expression:"once.exeTime"}}):e._e()],1),i("ta-form-item",{attrs:{span:22,label:"任务说明",fieldDecoratorId:"note"}},[i("ta-textarea",{attrs:{rows:4,placeholer:"请输入"}})],1)],1),i("div",{attrs:{slot:"footer"},slot:"footer"},[i("ta-button",{on:{click:function(t){e.blnOnceJobVisible=!1}}},[e._v("取消")]),i("ta-button",{attrs:{type:"primary"},on:{click:e.doSaveOnceJob}},[e._v("确定")])],1)],1),e.dalyJobListVisible?i("div",{staticStyle:{width:"100%",height:"100%",position:"absolute",top:"0",left:"0","z-index":"10",background:"white"}},[i("daly-job-list",{on:{close:function(t){e.dalyJobListVisible=!1}}})],1):e._e(),i("ta-modal",{attrs:{title:"上传数据",visible:e.blnSqlImportVisible,"destroy-on-close":"",width:"550px",height:"180px",destroyOnClose:""},on:{ok:e.handleImport,cancel:function(t){e.blnSqlImportVisible=!1}}},[i("ta-form",{attrs:{autoFormCreate:function(e){t.form=e},"label-width":"120px"}},[i("ta-form-item",{attrs:{"init-value":"prod",label:"上传环境",fieldDecoratorId:"profile",require:{message:"请选择上传环境!"}}},[i("ta-select",{staticStyle:{width:"70%"},attrs:{options:e.profileList}})],1),i("ta-form-item",{attrs:{label:"选择文件",fieldDecoratorId:"file",require:{message:"请选择文件!"}}},[i("ta-upload",{attrs:{"file-list":e.fileList,"before-upload":e.beforeUpload,remove:e.handleRemove}},[i("ta-button",[e._v("上传加密的zip文件")]),i("ta-tooltip",{attrs:{placement:"right"}},[i("template",{slot:"title"},[i("span",[e._v(" 上传的模式说明: "),i("br"),e._v(" 1.mtt.zip(加密)，配置文件需指定模式=mtt "),i("br"),e._v(" 2.zip中包含mtt.zip(加密)与eng.zip(加密)，配置文件需指定模式=mtt,eng 或者=mtt 都可以 ")])]),i("ta-icon",{staticStyle:{"font-size":"18px","margin-left":"10px"},attrs:{type:"question-circle"}})],2)],1)],1)],1)],1),i("ta-modal",{attrs:{title:"引擎数据重新加载","destroy-on-close":!0,width:400,height:100},model:{value:e.profileChooseVisible,callback:function(t){e.profileChooseVisible=t},expression:"profileChooseVisible"}},[i("ta-form",{attrs:{"auto-form-create":function(e){return t.profileForm=e}}},[i("ta-form-item",{attrs:{initValue:"prod","field-decorator-id":"profile",label:"执行环境"}},[i("ta-select",{staticStyle:{width:"70%"},attrs:{options:e.profileList}})],1)],1),i("div",{attrs:{slot:"footer"},slot:"footer"},[i("ta-button",{on:{click:function(t){e.profileChooseVisible=!1}}},[e._v("取消")]),i("ta-button",{attrs:{type:"primary"},on:{click:e.triggerJob}},[e._v("立刻执行")])],1)],1)],1)},o=[],l=a(95082),s=a(89584),r=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("div",{staticStyle:{padding:"10px",height:"100%",overflow:"scroll"}},[i("div",{staticClass:"title"},[e._v("定时日任务")]),i("ta-button",{on:{click:e.goBack}},[i("ta-icon",{attrs:{type:"arrow-left"}}),e._v("返回")],1),e._l(e.jobList,(function(t,a){return i("div",{key:a,staticStyle:{padding:"50px"}},[i("div",{staticClass:"title"},[e._v("任务名称: "+e._s(t.jobName)+" "),i("div",{staticStyle:{float:"right"}},[i("ta-button",{attrs:{type:"primary"},on:{click:function(a){return e.openEdit(t)}}},[e._v("修改")]),i("ta-switch",{staticStyle:{"margin-left":"10px","margin-right":"10px"},attrs:{checkedChildren:"开",unCheckedChildren:"关",defaultChecked:""},on:{change:function(a){return e.onSwitchChange(t)}},model:{value:t.jobEnable,callback:function(a){e.$set(t,"jobEnable",a)},expression:"item.jobEnable"}})],1)]),i("ta-card",[i("ta-row",[i("ta-col",{staticStyle:{"margin-bottom":"10px"},attrs:{span:24}},[e._v("任务名称: "+e._s(t.jobName))]),i("ta-col",{staticStyle:{"margin-bottom":"10px"},attrs:{span:24}},[e._v("执行时间: "+e._s(t.exeTime))]),i("ta-col",{staticStyle:{"margin-bottom":"10px"},attrs:{span:24}},[e._v("任务说明: "+e._s(t.note))])],1)],1)],1)})),i("ta-modal",{attrs:{width:1e3,"destroy-on-close":!0,title:"新增定时日任务"},on:{ok:function(t){e.blnEditVisible=!1}},model:{value:e.blnEditVisible,callback:function(t){e.blnEditVisible=t},expression:"blnEditVisible"}},[i("ta-form",{attrs:{"auto-form-create":function(e){return t.dalyForm=e},formLayout:!0}},[i("ta-form-item",{attrs:{span:22,label:"任务名称",fieldDecoratorId:"jobName",require:{message:"任务名称不能为空"}}},[i("ta-input",{attrs:{placeholder:"请输入","allow-clear":!0}})],1),i("ta-form-item",{attrs:{span:22,label:" ","label-width":"100px"}},[i("span",{staticStyle:{color:"red","margin-left":"42px"}},[e._v("*")]),i("span",[e._v("执行时间：每天的")]),i("ta-select",{staticStyle:{width:"100px",display:"inline-block","margin-left":"15px"},attrs:{options:e.hourList,"show-search":!0},model:{value:e.daly.hour,callback:function(t){e.$set(e.daly,"hour",t)},expression:"daly.hour"}}),i("span",{staticStyle:{"margin-left":"10px"}},[e._v("时")]),i("ta-select",{staticStyle:{width:"100px",display:"inline-block","margin-left":"10px"},attrs:{options:e.minuteList,"show-search":!0},model:{value:e.daly.minute,callback:function(t){e.$set(e.daly,"minute",t)},expression:"daly.minute"}}),i("span",{staticStyle:{"margin-left":"10px"}},[e._v("分")])],1),i("ta-form-item",{attrs:{span:22,label:"任务说明",fieldDecoratorId:"note"}},[i("ta-textarea",{attrs:{rows:4,placeholer:"请输入"}})],1)],1),i("div",{attrs:{slot:"footer"},slot:"footer"},[i("ta-button",{on:{click:function(t){e.blnEditVisible=!1}}},[e._v("取消")]),i("ta-button",{attrs:{type:"primary"},on:{click:e.doSaveDalyJob}},[e._v("确定")])],1)],1)],2)},n=[],c=a(66347),d={name:"dalyJob",data:function(){return{moduleUrl:"mtt/taskjob",jobList:[],blnEditVisible:!1,editRecord:void 0,hourList:[],minuteList:[],daly:{hour:void 0,minute:void 0}}},mounted:function(){this.queryList(),this.initHourAndMinute()},methods:{initHourAndMinute:function(){for(var t=0;t<=23;t++)this.hourList.push({value:t+"",label:t+""});for(var e=0;e<=59;e++)this.minuteList.push({value:e+"",label:e+""})},goBack:function(){this.$emit("close")},queryList:function(){var t=this;Base.submit(null,{url:this.moduleUrl+"/queryTimedTaskJobList",data:{}}).then((function(e){var a,i=(0,c.Z)(e.data.data);try{for(i.s();!(a=i.n()).done;){var o=a.value,l=o.cron.split(" ");o.exeTime="每天的"+l[2]+"时"+l[1]+"分",o.jobEnable="1"==o.aae100}}catch(s){i.e(s)}finally{i.f()}t.jobList=e.data.data}))},onSwitchChange:function(t){var e=this;this.$nextTick((function(){var a="/openTimedTaskJobById";t.jobEnable||(a="/closeTimedTaskJobById"),Base.submit(null,{url:e.moduleUrl+a,data:{jobId:t.jobId}}).then((function(t){e.$message.success("操作成功"),e.queryList()}))}))},openEdit:function(t){var e=this;this.blnEditVisible=!0,this.editRecord=t;var a=t.cron.split(" ");this.daly={hour:parseInt(a[2])+"",minute:parseInt(a[1])+""},this.$nextTick((function(){e.dalyForm.setFieldsValue((0,l.Z)({},e.editRecord))}))},doSaveDalyJob:function(){var t=this;this.dalyForm.validateFields((function(e){if(!e)if(t.daly.hour&&t.daly.minute){var a=t.dalyForm.getFieldsValue();a.jobId=t.editRecord.jobId,a.dailyHour=t.daly.hour,a.dailyMinutes=t.daly.minute,a.jobType="daly",Base.submit(null,{url:t.moduleUrl+"/updateTimedTaskJob",data:(0,l.Z)({},a)}).then((function(e){t.$message.success("操作成功"),t.blnEditVisible=!1,t.queryList()}))}else t.$message.info("小时或分不能为空")}))}}},u=d,m=a(1001),p=(0,m.Z)(u,r,n,!1,null,"d5817c4e",null),b=p.exports,f={name:"bigRuleManage",components:{dalyJobList:b},data:function(){return{moduleUrl:"mtt/taskjob",columns:[{dataIndex:"jobId",title:"任务流水号",overflowTooltip:!0,align:"left",width:100},{dataIndex:"jobName",title:"任务名称",overflowTooltip:!0,align:"left",width:200},{dataIndex:"jobType",title:"任务类型",collectionType:"M_JOBTYPE",overflowTooltip:!0,align:"left",width:100},{dataIndex:"jobStatus",title:"任务状态",collectionType:"M_JOBSTATUS",overflowTooltip:!0,align:"left",width:100},{dataIndex:"startTime",title:"执行开始时间",overflowTooltip:!0,align:"left",width:100},{dataIndex:"endTime",title:"执行结束时间",overflowTooltip:!0,align:"left",width:100},{dataIndex:"operate",title:"操作",overflowTooltip:!0,scopedSlots:{customRender:"operate"},align:"center",width:100}],dataList:[],bigRuleList:[],pageParam:{pageNumber:1},ruleRecord:{},blnDetailVisible:!1,blnDalyJobVisible:!1,blnOnceJobVisible:!1,hourList:[],minuteList:[],daly:{hour:void 0,minute:void 0},once:{timeType:"1",exeTime:void 0},dalyJobListVisible:!1,fileList:[],blnSqlImportVisible:!1,profileList:[],profileChooseVisible:!1,triggerRecord:{}}},mounted:function(){this.initHourAndMinute(),this.queryProfileList()},methods:{queryProfileList:function(){var t=this;Base.submit(null,{url:"mtt/localruleconfig/ruleUserCommit/queryProfileList"}).then((function(e){t.profileList=e.data.list}))},handleImport:function(){var t=this;if(this.fileList.length<=0)this.$message.error("请先选择文件");else{var e=this.form.getFieldValue("profile"),a="/mtt/localruleconfig/ruleUserCommit/doExecuteSqlFile";this.Base.submit(null,{url:a,data:{file:this.fileList[0],profile:e},isFormData:!0}).then((function(e){t.$message.success("导入mtt数据成功"),t.$confirm({title:"是否关闭窗口",content:"导入mtt数据成功",onOk:function(){t.blnSqlImportVisible=!1,t.doSearch()},onCancel:function(){t.$message.info("用户取消操作")},class:"test"})}))}},beforeUpload:function(t){return this.fileList=[],this.fileList=[].concat((0,s.Z)(this.fileList),[t]),!1},handleRemove:function(t){var e=this.fileList.indexOf(t),a=this.fileList.slice();a.splice(e,1),this.fileList=a},openChooseProfile:function(t){this.triggerRecord=t,this.profileChooseVisible=!0},triggerJob:function(){var t=this,e=this.profileForm.getFieldValue("profile");Base.submit(null,{url:this.moduleUrl+"/triggerJob",data:(0,l.Z)((0,l.Z)({},this.triggerRecord),{},{profile:e})}).then((function(e){t.$message.success("操作成功"),t.profileChooseVisible=!1,t.doSearch()}))},onTimeTypeChange:function(){this.$nextTick((function(){}))},initHourAndMinute:function(){for(var t=0;t<=23;t++)this.hourList.push({value:t+"",label:t+""});for(var e=0;e<=59;e++)this.minuteList.push({value:e+"",label:e+""})},doSearch:function(){var t=this;this.searchForm.validateFields((function(e){e||t.$refs.pager.loadData()}))},pageParams:function(){var t=this.searchForm.getFieldsValue();return t.operationtime&&2==t.operationtime.length&&(t.startTime=t.operationtime[0].format("YYYY-MM-DD"),t.endTime=t.operationtime[1].format("YYYY-MM-DD")),(0,l.Z)({},t)},resetFields:function(){this.searchForm.resetFields()},openJobDetail:function(t){var e=this;Base.submit(null,{url:this.moduleUrl+"/queryDetail",data:(0,l.Z)({},t)}).then((function(t){e.ruleRecord=t.data.data,e.blnDetailVisible=!0}))},cancelJob:function(t){var e=this;Base.submit(null,{url:this.moduleUrl+"/cancelById",data:(0,l.Z)({},t)}).then((function(t){e.$message.success("操作成功"),e.doSearch()}))},doSaveDalyJob:function(){var t=this;this.dalyForm.validateFields((function(e){if(!e)if(t.daly.hour&&t.daly.minute){var a=t.dalyForm.getFieldsValue();a.dailyHour=t.daly.hour,a.dailyMinutes=t.daly.minute,a.jobType="daly",Base.submit(null,{url:t.moduleUrl+"/addTimedTaskJob",data:(0,l.Z)({},a)}).then((function(e){t.$message.success("操作成功"),t.blnDalyJobVisible=!1,t.doSearch()}))}else t.$message.info("小时或分不能为空")}))},doSaveOnceJob:function(){var t=this;this.onceForm.validateFields((function(e){if(!e){var a=t.onceForm.getFieldsValue();a.timeType=t.once.timeType,a.exeTime=t.once.exeTime,a.jobType="once",a.executedImmediately="1"==a.timeType,a.scheduleTime=a.exeTime,a.scheduleTime&&(a.scheduleTime=a.scheduleTime.format("YYYY-MM-DD HH:MM:SS")),Base.submit(null,{url:t.moduleUrl+"/addDailyJob",data:(0,l.Z)({},a)}).then((function(e){t.$message.success("操作成功"),t.blnOnceJobVisible=!1,t.doSearch()}))}}))},openAddDalyJob:function(){this.blnDalyJobVisible=!0,this.daly={hour:void 0,minute:void 0}},openAddOnceJob:function(){this.blnOnceJobVisible=!0,this.once={timeType:"1",exeTime:void 0}},openDalyJobTab:function(){this.dalyJobListVisible=!0}}},h=f,y=(0,m.Z)(h,i,o,!1,null,"2fa2c194",null),v=y.exports}}]);
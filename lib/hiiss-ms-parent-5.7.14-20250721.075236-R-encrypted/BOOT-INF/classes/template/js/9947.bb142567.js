"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[9947],{43541:function(t,a,e){e.r(a),e.d(a,{default:function(){return v}});var n=function(){var t=this,a=this,e=a.$createElement,n=a._self._c||e;return n("div",{staticClass:"fit"},[n("ta-border-layout",{attrs:{"layout-type":"fixTop"},scopedSlots:a._u([{key:"header",fn:function(){return[n("ta-form",{staticStyle:{"margin-top":"20px"},attrs:{"auto-form-create":a.autoFormCreate_FJ8G9VGAG,"form-layout":!0,col:3}},[n("ta-form-item",{attrs:{require:!0,"show-info":!0,"field-decorator-id":"dateRange",label:"查询日期","label-col":{span:6},span:8,"init-value":a.initRange,"wrapper-col":{span:18}}},[n("ta-range-picker",{attrs:{"allow-one":!0,"allow-clear":!1,type:"date"}})],1),n("ta-form-item",{attrs:{require:!0,"show-info":!0,"field-decorator-id":"hospitalCode",label:"医院","label-col":{span:6},span:4,"wrapper-col":{span:18}}},[n("ta-select",{attrs:{"option-label-prop":"label",options:a.hospitalList,placeholder:"请选择"}})],1),n("ta-form-item",{attrs:{require:!0,"show-info":!0,"field-decorator-id":"queryType",label:"查询类型","label-col":{span:6},span:4,"init-value":"1","wrapper-col":{span:18}}},[n("ta-select",{attrs:{"option-label-prop":"label",placeholder:"请选择"}},[n("ta-select-option",{attrs:{value:"1"}},[a._v(" 申诉 ")]),n("ta-select-option",{attrs:{value:"2"}},[a._v(" 合议 ")])],1)],1),n("ta-form-item",{attrs:{"label-col":{span:6},require:!1,"show-info":!0,span:8,"wrapper-col":{span:18},"field-decorator-id":"_field-77F3O8HN",label:" "}},[n("ta-button",{attrs:{type:"primary"},on:{click:a.queryTableData}},[a._v(" 查询 ")])],1)],1)]},proxy:!0}])},[n("ta-card",{staticClass:"fit",scopedSlots:a._u([{key:"actions",fn:function(){return[n("ta-pagination",{ref:"gridPager",staticStyle:{"text-align":"right"},attrs:{"data-source":a.tableData,"default-current":1,"default-page-size":200,params:a.getParam,url:"/center/getCenterSuspect"},on:{"update:dataSource":function(t){a.tableData=t},"update:data-source":function(t){a.tableData=t}}})]},proxy:!0}])},[n("ta-big-table",{attrs:{data:a.tableData,"export-config":{},"import-config":{},height:"100%"}},[n("ta-big-table-column",{attrs:{type:"seq",width:"60"}}),n("ta-big-table-column",{attrs:{field:"docId",title:"单据id"}}),n("ta-big-table-column",{attrs:{field:"patnId",title:"患者id"}}),n("ta-big-table-column",{attrs:{field:"patnName",title:"患者姓名"}}),n("ta-big-table-column",{attrs:{field:"applDdln",title:"申诉期限"}}),n("ta-big-table-column",{attrs:{fixed:"right",title:"申诉",width:"200"},scopedSlots:a._u([{key:"default",fn:function(t){return[n("ta-table-operate",{attrs:{"operate-menu":a.operateMenu,"row-info":t}})]}}])})],1)],1)],1),n("ta-drawer",{attrs:{"destroy-on-close":!0,keyboard:!0,"show-in-canvas":!0,title:(this.isAdd,"修改配置项"),visible:a.drawerVisible,width:400,"z-index":1e3},on:{close:function(t){a.drawerVisible=!1}},scopedSlots:a._u([{key:"footer",fn:function(){return[n("div",{staticStyle:{"text-align":"right"}},[n("ta-button",{attrs:{type:"primary"},on:{click:a.saveConfig}},[a._v(" 保存 ")]),n("ta-button",{on:{click:function(t){a.drawerVisible=!1}}},[a._v(" 取消 ")])],1)]},proxy:!0}])},[n("ta-form",{attrs:{"auto-form-create":function(a){return t.form_edit=a},"form-layout":!1,"label-col":{span:9},"wrapper-col":{span:17}}},[n("ta-form-item",{attrs:{require:!0,"show-info":!0,span:24,"field-decorator-id":"susptAmt",label:"申诉金额","label-col":{span:9},"wrapper-col":{span:15}}},[n("ta-input-number",{staticClass:"number-width",attrs:{"as-amount":!0}})],1),n("ta-form-item",{attrs:{require:!0,"show-info":!0,span:24,"field-decorator-id":"attachFile",label:"文件","label-col":{span:9},"wrapper-col":{span:15}}},[n("ta-upload",{attrs:{"file-list":a.fileList,"before-upload":a.beforeUpload}},[n("ta-button",[a._v("点击上传")])],1)],1),n("ta-form-item",{attrs:{require:!0,"show-info":!0,span:24,"field-decorator-id":"auditOpinion",label:"申诉理由","label-col":{span:9},"wrapper-col":{span:15}}},[n("ta-input",{attrs:{placeholder:"请输入内容"}})],1)],1)],1)],1)},r=[],l=e(95082),i=e(95278),u="appeal/hidAppeal/",s={getPageUrl:function(){return u+"queryListByPage"},getBasePath:function(){return i.Z.basePath},getAdmissionNumList:function(t,a){Base.submit(null,{url:"appeal/common/fuzzySearchInKf38/patientId",data:(0,l.Z)((0,l.Z)({},t),{},{igronCurrPer:!0})},{successCallback:function(t){return a(t)}})},getDeptList:function(t,a){Base.submit(null,{url:"appeal/common/fuzzySearchInKf38/OprDepartName",data:(0,l.Z)((0,l.Z)({},t),{},{igronCurrPer:!0})},{successCallback:function(t){return a(t)}})},getDeptList2:function(t,a){Base.submit(null,{url:"appeal/common/fuzzySearchInKf38/departName",data:(0,l.Z)((0,l.Z)({},t),{},{igronCurrPer:!0})},{successCallback:function(t){return a(t)}})},getCostTypeList:function(t,a){Base.submit(null,{url:"appeal/common/fuzzySearchInKf38/costType",data:(0,l.Z)((0,l.Z)({},t),{},{igronCurrPer:!0})},{successCallback:function(t){return a(t)}})},getRuleNameList:function(t,a){Base.submit(null,{url:"appeal/common/fuzzySearchInKf38/ruleName",data:(0,l.Z)((0,l.Z)({},t),{},{igronCurrPer:!0})},{successCallback:function(t){return a(t)}})},getPatintNameList:function(t,a){Base.submit(null,{url:"appeal/common/fuzzySearchInKf38/patientName",data:(0,l.Z)((0,l.Z)({},t),{},{igronCurrPer:!0})},{successCallback:function(t){return a(t)}})},getDoctorList:function(t,a){Base.submit(null,{url:"appeal/common/fuzzySearchInKf38/doctorName",data:(0,l.Z)((0,l.Z)({},t),{},{igronCurrPer:!0})},{successCallback:function(t){return a(t)}})},getObjNameList:function(t,a){Base.submit(null,{url:"appeal/common/fuzzySearchInKf38/itemName",data:(0,l.Z)((0,l.Z)({},t),{},{igronCurrPer:!0})},{successCallback:function(t){return a(t)}})},queryPeople:function(t,a){Base.submit(null,{url:"/appeal/common/getDicDoctor",data:t},{successCallback:function(t){return a(t)}})},queryHandledCount:function(t,a){Base.submit(null,{url:u+"queryHandledCount",data:t},{successCallback:function(t){return a(t)}})},getPluginSetting:function(t,a){Base.submit(null,{url:u+"getDispatchCfg",data:t},{successCallback:function(t){return a(t)}})},savePluginSetting:function(t,a){Base.submit(null,{url:u+"saveDispatchCfg",data:t,autoQs:!1},{successCallback:function(t){return a(t)}})},isPluginSetted:function(t,a){Base.submit(null,{url:u+"checkDispatchCfg",data:t},{successCallback:function(t){return a(t)}})},oneModefy:function(t,a){Base.submit(null,{url:u+"cacheDispatchRecord",data:t},{successCallback:function(t){return a(t)}})},batchModefy:function(t,a){Base.submit(null,{url:u+"cacheDispatchRecordBatch",data:t,autoQs:!1},{successCallback:function(t){return a(t)}})},distributeSave:function(t,a){Base.submit(null,{url:u+"dispatchAppeal",data:t},{successCallback:function(t){return a(t)}})},updateCurrPers:function(t,a){Base.submit(null,{url:u+"updateCurrPers",data:t,autoQs:!1},{successCallback:function(t){return a(t)}})},getDistributePageUrl:function(){return u+"getDispatchData"},reOneModefy:function(t,a){Base.submit(null,{url:u+"cacheRedispatchRecord",data:t},{successCallback:function(t){return a(t)}})},reBatchModefy:function(t,a){Base.submit(null,{url:u+"cacheReDispatchRecordBatch",data:t,autoQs:!1},{successCallback:function(t){return a(t)}})},reDistributeSave:function(t,a){Base.submit(null,{url:u+"redispatchAppeal",data:t},{successCallback:function(t){return a(t)}})},batchApproval:function(t,a){Base.submit(null,{url:u+"auditAppealBatch",data:t,autoQs:!1},{successCallback:function(t){return a(t)}})},singelApproval:function(t,a){Base.submit(null,{url:u+"auditAppeal",data:t},{successCallback:function(t){return a(t)}})},queryAae043List:function(t,a){Base.submit(null,{url:u+"queryAae043List",data:t},{successCallback:function(t){return a(t)}})},queryHospital:function(t,a){Base.submit(null,{url:"/miimCommonRead/queryHospRpcList",data:t},{successCallback:function(t){return a(t)}})},doCenterAppeal:function(t,a){Base.submit(null,{url:"/center/doAppeal",data:t,isFormData:!0,autoQs:!0},{successCallback:function(t){return a(t)}})},getFileList:function(t,a){Base.submit(null,{url:"appeal/drPoint/queryAppealDetail",data:t},{successCallback:function(t){return a(t)}})},getPicZipUrl:function(){return i.Z.basePath+"/appeal/drPoint/fileZipDownload"},exportZip:function(){return i.Z.basePath+"/appeal/hidAppeal/exportZip"}},o=e(3032),c=e(36797),p=e.n(c),d=e(36429),f=(e(2874),e(6602)),m=(e(7073),e(3866)),b=(e(77596),e(74725)),h=(e(53573),e(3950)),g=(e(38423),e(75539));e(35272);o["default"].use(d.ZP),o["default"].use(f.Z),o["default"].use(m.Z),o["default"].use(b.Z),o["default"].use(h.Z),o["default"].use(g.Z);var y={name:"appealQuery",data:function(){var t=this;return{tableData:[],operateMenu:[{name:"申诉",icon:"edit",id:"1",onClick:function(a,e){t.isAdd=!1,t.drawerVisible=!0,t.selectedData=a}}],initRange:[p()(),p()()],fileList:[],isAdd:!0,hospitalList:[],drawerVisible:!1,selectedData:{}}},mounted:function(){var t=this;s.queryHospital(null,(function(a){t.hospitalList=a.data.resultData,t.hospitalList&&t.hospitalList.length>0&&t.form_query.setFieldsValue({hospitalCode:t.hospitalList[0].value})}))},methods:{beforeUpload:function(t){return this.fileList=[t],!1},autoFormCreate_FJ8G9VGAG:function(t){this.form_query=t},getParam:function(){var t=this.form_query.getFieldsValue();t.startDate=t.dateRange[0].format("YYYY-MM-DD HH:mm:ss"),t.endDate=t.dateRange[1].format("YYYY-MM-DD HH:mm:ss");var a=this.hospitalList.find((function(a){return a.value===t.hospitalCode}));return t.hospitalName=a.label,t},queryTableData:function(){var t=this;this.form_query.validateFields((function(a){a||t.$refs.gridPager.loadData()}))},saveConfig:function(){var t=this;this.form_edit.validateFields((function(a){if(!a){var e=t.form_query.getFieldsValue(),n=t.form_edit.getFieldsValue(),r={medinsName:t.selectedData.medinsName,medinsCodg:t.selectedData.medinsId,docs:[]};r.docs.push({docId:t.selectedData.docId,docSusptDTOS:[{docSusptId:t.selectedData.docSusptDTOS[0].docSusptId,applyFlag:"1",susptAmt:n.susptAmt,auditOpinion:n.auditOpinion}]}),s.doCenterAppeal({file:t.fileList[0],appealType:e.queryType,appealInfo:JSON.stringify(r)},(function(t){}))}}))}}},C=y,D=e(1001),k=(0,D.Z)(C,n,r,!1,null,"162fc3fd",null),v=k.exports}}]);
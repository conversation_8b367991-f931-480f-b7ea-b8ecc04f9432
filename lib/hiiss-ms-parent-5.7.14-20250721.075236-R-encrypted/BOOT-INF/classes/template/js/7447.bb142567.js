(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[7447],{40792:function(t,a,e){"use strict";e.r(a),e.d(a,{default:function(){return x}});var i=function(){var t=this,a=this,i=a.$createElement,r=a._self._c||i;return r("div",{staticClass:"fit"},[r("div",{staticStyle:{height:"53%"}},[r("ta-border-layout",{staticStyle:{"border-bottom":"none"},attrs:{layout:{header:"63px",left:"50%"}}},[r("div",{attrs:{slot:"header"},slot:"header"},[r("ta-form",{staticStyle:{position:"absolute",width:"100%"},attrs:{autoFormCreate:function(a){t.form=a},layout:"horizontal",formLayout:!0}},[r("ta-row",[r("ta-col",{staticStyle:{width:"290px",top:"8px"},attrs:{span:"6"}},[r("ta-radio-group",{on:{change:a.switchDataType},model:{value:a.dataRadioValue,callback:function(t){a.dataRadioValue=t},expression:"dataRadioValue"}},[r("ta-radio",{attrs:{value:1}},[a._v(a._s(a.radioTitle))]),r("ta-radio",{attrs:{value:2}},[a._v("环比增速")]),r("ta-radio",{attrs:{value:3,hidden:a.tbHidden}},[a._v("同比增速")])],1)],1),r("ta-col",{staticStyle:{width:"240px",left:"50px",top:"8px"},attrs:{span:"4"}},[r("ta-radio-group",{on:{change:a.switchDateType},model:{value:a.dateRadioValue,callback:function(t){a.dateRadioValue=t},expression:"dateRadioValue"}},[r("ta-radio",{attrs:{value:1}},[a._v("月")]),r("ta-radio",{attrs:{value:3}},[a._v("年")])],1)],1),r("ta-form-item",{staticStyle:{"margin-left":"-4%"},attrs:{fieldDecoratorId:"month",span:3,hidden:a.monthHidden,fieldDecoratorOptions:{initialValue:a.initDate}}},[r("ta-month-picker",{attrs:{format:"YYYYMM"}})],1),r("ta-form-item",{staticStyle:{"margin-left":"-4%"},attrs:{fieldDecoratorId:"quarter",span:3,hidden:a.quarterHidden,fieldDecoratorOptions:{initialValue:a.initDate}}},[r("ta-quarter-picker")],1),r("ta-form-item",{staticStyle:{"margin-left":"-4%"},attrs:{fieldDecoratorId:"year",span:3,hidden:a.yearHidden,fieldDecoratorOptions:{initialValue:a.initDate}}},[r("ta-year-picker")],1),r("ta-form-item",{attrs:{fieldDecoratorId:"yzb001",hidden:!0}},[r("ta-input")],1),r("ta-form-item",{attrs:{fieldDecoratorId:"dx",hidden:!0}},[r("ta-input")],1),4!=a.key?r("ta-form-item",{staticStyle:{"margin-left":"-3%"},attrs:{fieldDecoratorId:"aka130",labelCol:{span:9},wrapperCol:{span:12},require:{message:"必输项!"},span:4,initValue:a.CollectionData("AKA130ZK")&&a.CollectionData("AKA130ZK").length>0?a.CollectionData("AKA130ZK")[0].value:""}},[r("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[a._v("医疗类型")]),r("ta-select",{staticStyle:{width:"100%"},attrs:{"collection-type":"AKA130ZK",allowClear:!1}})],1):a._e(),r("ta-form-item",{attrs:{label:"医保类型",span:4,fieldDecoratorId:"aae141"}},[r("ta-select",{staticStyle:{width:"130px","margin-left":"5px"},attrs:{"collection-type":"AAE141ZK",allowClear:""}})],1),r("ta-button",{attrs:{type:"primary",icon:"search"},on:{click:a.fnClickQuery}},[a._v("查询")])],1)],1)],1),r("div",{staticClass:"fit",attrs:{slot:"left"},slot:"left"},[r("ta-row",[r("div",{staticStyle:{"border-bottom":"1px solid rgba(153,153,153,0.14)"}},[r("ta-title",{attrs:{title:a.leftUpperTitle}},[a._t("default",(function(){return[a._v("构成")]}))],2)],1)]),r("ta-echarts",{key:a.constituteKey,ref:"constituteChart",staticStyle:{height:"80%",width:"100%"},attrs:{option:{}}})],1),r("div",{staticClass:"fit"},[r("ta-row",[r("div",{staticStyle:{"border-bottom":"1px solid rgba(153,153,153,0.14)"}},[r("ta-title",{attrs:{title:a.rightUpperTitle}},[a._t("default",(function(){return[a._v("趋势")]}))],2)],1)]),r("ta-echarts",{key:a.trendkey,ref:"trendChart",staticStyle:{height:"80%",width:"100%"},attrs:{option:{}}})],1)])],1),r("div",{staticStyle:{height:"47%"}},[r("ta-border-layout",{attrs:{layout:{left:"50%"}}},[r("div",{staticClass:"fit",attrs:{slot:"left"},slot:"left"},[r("div",{staticStyle:{"border-bottom":"1px solid rgba(153,153,153,0.14)"}},[r("ta-title",{attrs:{title:a.leftLowerTitle}},[a._t("default",(function(){return[a._v("排名 "),r("ta-popover",{attrs:{width:"100",trigger:"hover",content:"点击更改为升序"}},[a.showAsc?r("img",{staticStyle:{width:"18px",height:"18px"},attrs:{slot:"reference",src:e(48468)},on:{click:a.fnAscClick},slot:"reference"}):r("img",{staticStyle:{width:"18px",height:"18px"},attrs:{slot:"reference",src:e(6430)},on:{click:a.fnAscClick},slot:"reference"})]),r("ta-popover",{attrs:{width:"100",trigger:"hover",content:"点击更改为降序"}},[a.showDesc?r("img",{staticStyle:{width:"18px",height:"18px"},attrs:{slot:"reference",src:e(47546)},on:{click:a.fnDescClick},slot:"reference"}):r("img",{staticStyle:{width:"18px",height:"18px"},attrs:{slot:"reference",src:e(28304)},on:{click:a.fnDescClick},slot:"reference"})])]})),r("span",{staticStyle:{display:"inline-block",float:"right"}},[r("a",{on:{click:function(){return a.openPopWin("item")}}},[a._v("查看更多>>")]),a.orderShowLast?r("img",{staticStyle:{width:"20px",height:"20px"},attrs:{src:e(53949)},on:{click:a.fnOrderLast}}):r("img",{staticStyle:{width:"20px",height:"20px"},attrs:{src:e(89712)}}),a.orderShowNext?r("img",{staticStyle:{width:"20px",height:"20px"},attrs:{src:e(23554)},on:{click:a.fnOrderNext}}):r("img",{staticStyle:{width:"20px",height:"20px"},attrs:{src:e(61368)}})])],2)],1),r("ta-echarts",{key:a.orderkey,ref:"orderChart",staticStyle:{height:"85%",width:"100%"},attrs:{option:{}}})],1),r("div",{staticClass:"fit"},[r("div",{staticStyle:{"border-bottom":"1px solid rgba(153,153,153,0.14)"}},[r("ta-title",{attrs:{title:a.rightLowerTitle}},[a._t("default",(function(){return[a._v("排名 "),r("ta-button",{style:{right:"200px",backgroundColor:a.ysColor,position:"absolute"},attrs:{size:"small"},on:{click:a.fnYsClick}},[a._v("医师")]),r("ta-button",{style:{right:"250px",backgroundColor:a.ksColor,position:"absolute"},attrs:{size:"small"},on:{click:a.fnKsClick}},[a._v("科室")])]})),r("span",{staticStyle:{display:"inline-block",float:"right"}},[r("a",{on:{click:function(){return a.openPopWin("$tabType")}}},[a._v("查看更多>>")]),a.tabShowLast?r("img",{staticStyle:{width:"20px",height:"20px"},attrs:{src:e(53949)},on:{click:a.fnTabLast}}):r("img",{staticStyle:{width:"20px",height:"20px"},attrs:{src:e(89712)}}),a.tabShowLast?r("img",{staticStyle:{width:"20px",height:"20px"},attrs:{src:e(23554)},on:{click:a.fnTabNext}}):r("img",{staticStyle:{width:"20px",height:"20px"},attrs:{src:e(61368)}})])],2)],1),r("ta-echarts",{key:a.tabskey,ref:"tabsChart",staticStyle:{height:"85%",width:"100%"},attrs:{option:{}}})],1)])],1),r("pop-window",{ref:"popWind",attrs:{visible:a.popWinVisible,popProps:a.popWinParam},on:{handleClose:a.handlePopWinClose}})],1)},r=[],s=(e(32564),e(36797)),o=e.n(s),n=e(10530),l=(e(16411),e(66993)),h=e(55115),d=e(88412),c=function(){var t=this,a=this,e=a.$createElement,i=a._self._c||e;return i("ta-modal",{attrs:{title:"费用排名",visible:a.visible,height:700,width:1400,closable:"",footer:null},on:{cancel:a.handleClose}},[i("ta-border-layout",{staticStyle:{height:"calc(100%)"},attrs:{"layout-type":"fixTop"}},[i("ta-form",{staticClass:"form-header",attrs:{slot:"header","auto-form-create":function(a){return t.form=a},layout:"horizontal","form-layout":!0},slot:"header"},[i("ta-form-item",{attrs:{"label-col":{span:0},require:!0,"wrapper-col":{span:24},span:3,"field-decorator-id":"dateType"}},[i("ta-radio-group",{attrs:{options:[{label:"月",value:"M"},{label:"年",value:"Y"}],"default-value":"M"},on:{change:a.switchDateType}})],1),i("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:"M"===a.dateType,expression:"dateType === 'M'"}],attrs:{"field-decorator-id":"dateMonth",span:3,require:"M"===a.dateType,"label-col":{span:0},"wrapper-col":{span:24}}},[i("ta-month-picker",{attrs:{"value-format":"YYYYMM",format:"YYYYMM"}})],1),i("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:"Y"===a.dateType,expression:"dateType === 'Y'"}],attrs:{"field-decorator-id":"dateYear",span:3,require:"Y"===a.dateType,"label-col":{span:0},"wrapper-col":{span:24}}},[i("ta-year-picker",{attrs:{"value-format":"YYYY",format:"YYYY"}})],1),i("ta-form-item",{attrs:{label:"三目类型",span:6,"field-decorator-id":"ake003",hidden:!0}},[i("ta-input")],1),i("ta-form-item",{attrs:{label:"医疗类别",span:6,"field-decorator-id":"aka130"}},[i("ta-select",{attrs:{"collection-type":"AKA130ZK",allowClear:""}})],1),i("ta-form-item",{attrs:{label:"医保类型",span:6,"field-decorator-id":"aae141"}},[i("ta-select",{attrs:{"collection-type":"AAE141ZK",allowClear:""}})],1),i("ta-form-item",{attrs:{label:"险种类型",span:6,"field-decorator-id":"aae140"}},[i("ta-select",{attrs:{"collection-type":"AAE140ZK",allowClear:""}})],1),i("ta-form-item",{attrs:{label:"费用项目名称",span:6,"field-decorator-id":"ake001"}},[i("ta-suggest",{staticStyle:{width:"100%"},attrs:{"data-source":a.itemNameRpcList,"table-title-map":new Map([["label",{name:"费用项目名称",style:{minWidth:"120px"}}]]),"option-config":{value:"value",label:"label"},"dropdown-match-select-width":!1,"dropdown-style":{width:"300px"}},on:{search:a.handleSearchItem}})],1),i("ta-form-item",{attrs:{label:"科室名称",span:6,"field-decorator-id":"department"}},[i("ta-suggest",{staticStyle:{width:"100%"},attrs:{"data-source":a.departmentRpcList,"table-title-map":new Map([["label",{name:"科室名称",style:{minWidth:"120px"}}]]),"option-config":{value:"value",label:"label"},"dropdown-match-select-width":!1,"dropdown-style":{width:"300px"}},on:{search:a.handleSearchDpt}})],1),i("ta-form-item",{attrs:{label:"医生姓名",span:6,"field-decorator-id":"doctor"}},[i("ta-suggest",{staticStyle:{width:"100%"},attrs:{"data-source":a.doctorRpcList,"table-title-map":new Map([["label",{name:"医生名称",style:{minWidth:"120px"}}]]),"option-config":{value:"value",label:"label"},"dropdown-match-select-width":!1,"dropdown-style":{width:"300px"}},on:{search:a.handleSearchDoc}})],1),i("ta-form-item",[i("div",{staticStyle:{display:"flex"}},[i("ta-button",{staticStyle:{"margin-right":"10px"},attrs:{hidden:"true"},on:{click:a.resetQuery}},[a._v("重置")]),i("ta-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary"},on:{click:a.queryData}},[a._v("查询")])],1)])],1),i("div",{staticClass:"fit",staticStyle:{display:"flex","flex-direction":"column"}},[i("div",{staticStyle:{flex:"0"}},[i("ta-radio-group",{on:{change:a.queryData},model:{value:a.rankType,callback:function(t){a.rankType=t},expression:"rankType"}},[i("ta-radio-button",{attrs:{value:"item"}},[a._v(" 项目排名 ")]),i("ta-radio-button",{attrs:{value:"department"}},[a._v(" 科室排名 ")]),i("ta-radio-button",{attrs:{value:"doctor"}},[a._v(" 医生排名 ")])],1)],1),i("div",{staticStyle:{flex:"1"}},[i("ta-big-table",{ref:"xTable",staticStyle:{width:"100%"},attrs:{border:"","highlight-hover-row":"","show-overflow":"",data:a.tableData,height:"auto","auto-resize":"",size:"mini","empty-text":" ",width:"min-width"},scopedSlots:a._u([{key:"bottomBar",fn:function(){return[i("div",{staticStyle:{display:"flex","justify-content":"end","margin-right":"10px","align-items":"center"}},[i("ta-pagination",{ref:"gridPager",attrs:{size:"small",url:"/rankQuery/queryRankData",params:a.getPageParam,defaultPageSize:10,pageSizeOptions:["10","50","100","200","500"],"data-source":a.tableData},on:{"update:dataSource":function(t){a.tableData=t},"update:data-source":function(t){a.tableData=t}}}),i("ta-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary"},on:{click:a.exportDataEvent}},[a._v("导出")])],1)]},proxy:!0}])},[i("ta-big-table-column",{attrs:{fixed:"left",field:"sortNum",width:"50px",title:"排名",align:"center","header-align":"center"}}),i("ta-big-table-column",{attrs:{field:"rankName","min-width":"100px",align:"left","header-align":"center",title:a.typeName}}),i("ta-big-table-column",{attrs:{field:"amount",formatter:a.formatterAmountWan,title:"金额(万元)","header-align":"center",sortable:"",align:"right","min-width":"100px"}}),i("ta-big-table-column",{attrs:{field:"momRate",formatter:a.formatterRate,title:"金额环比增速","header-align":"center",sortable:"",align:"right","min-width":"100px"}}),i("ta-big-table-column",{attrs:{field:"yoyRate",formatter:a.formatterRate,title:"金额同比增速","header-align":"center",sortable:"",align:"right","min-width":"100px"}})],1)],1)])],1)],1)},p=[],u=e(95082),m=e(22722);h.w3.prototype.Base=Object.assign(h.w3.prototype.Base,(0,u.Z)({},m.Z));var f={name:"popWindow",props:{visible:{type:Boolean},popProps:{type:Object}},watch:{visible:{handler:function(t){var a=this;t&&this.$nextTick((function(){a.form.setFieldsValue(a.popProps),a.rankType=a.popProps.rankType,a.queryData()}))}}},data:function(){return{rankType:"item",dateType:"M",itemNameRpcList:[],departmentRpcList:[],doctorRpcList:[],tableData:[],pageUrl:""}},computed:{typeName:function(){return"item"===this.rankType?"项目名称":"department"===this.rankType?"科室名称":"doctor"===this.rankType?"医生名称":void 0}},methods:{setDateType:function(t){this.dateType=t,"Y"===t?this.$refs.xTable.hideColumn(this.$refs.xTable.getColumnByField("yoyRate")):this.$refs.xTable.showColumn(this.$refs.xTable.getColumnByField("yoyRate"))},exportDataEvent:function(){var t=this;this.form.validateFields((function(a){if(!a){var e=t.getPageParam();t.Base.submit(null,{url:"/rankQuery/queryRankDataAll",data:e},{successCallback:function(a){var e=t.$refs.xTable.getColumns(),i=e.map((function(t){return{header:t.title,key:t.property}}));a.data.data.forEach((function(t){t.amount=(Number(t.amount)/1e4).toFixed(2),t.momRate=(100*Number(t.momRate)).toFixed(2)+"%",t.yoyRate=(100*Number(t.yoyRate)).toFixed(2)+"%",t.count=Number(t.count).toFixed(2)}));var r={fileName:"项目排名信息",sheets:[{name:"worksheet1",column:{complex:!1,columns:i},rows:a.data.data}]};t.Base.generateExcel(r)}})}}))},handleSave:function(){this.$emit("handleSave")},handleClose:function(){this.tableData=[],this.form.resetFields(),this.rankType="item",this.$emit("handleClose")},switchDateType:function(t){this.dateType=t.target.value,"Y"===t.target.value?this.$refs.xTable.hideColumn(this.$refs.xTable.getColumnByField("yoyRate")):this.$refs.xTable.showColumn(this.$refs.xTable.getColumnByField("yoyRate"))},formatterAmountWan:function(t){var a=Number(t.cellValue);return(a/1e4).toFixed(2)},formatterNum:function(t){var a=Number(t.cellValue);return a.toFixed(2)},formatterRate:function(t){var a=Number(t.cellValue);return(100*a).toFixed(2)+"%"},handleChangeSuggest:function(t,a){},resetQuery:function(){this.form.resetFields(),this.tableData=[]},queryData:function(){var t=this;this.form.validateFields((function(a){a||t.$refs.gridPager.loadData()}))},getPageParam:function(){var t=this.form.getFieldsValue(),a=t.dateMonth;return"Y"===this.dateType&&(a=t.dateYear),(0,u.Z)((0,u.Z)({},t),{},{date:a,rankType:this.rankType})},handleSearchItem:function(t){var a=this;t&&Base.submit(null,{url:"/rankQuery/queryItemRpcList",data:{searchVal:t}},{successCallback:function(t){a.itemNameRpcList=t.data.list}})},handleSearchDpt:function(t){var a=this;t&&Base.submit(null,{url:"/rankQuery/queryDptRpcList",data:{searchVal:t}},{successCallback:function(t){a.departmentRpcList=t.data.list}})},handleSearchDoc:function(t){var a=this;t&&Base.submit(null,{url:"/rankQuery/queryDocRpcList",data:{searchVal:t}},{successCallback:function(t){a.doctorRpcList=t.data.list}})}}},b=f,y=e(1001),g=(0,y.Z)(b,c,p,!1,null,"43a23906",null),k=g.exports;h.w3.use(n.Z);var w={components:{PopWindow:k,TaTitle:d.Z},data:function(){return{popWinParam:{},popWinVisible:!1,dataRadioValue:1,dateRadioValue:1,monthHidden:!1,quarterHidden:!0,yearHidden:!0,initDate:o()(new Date),title:"",leftUpperTitle:"",rightUpperTitle:"",leftLowerTitle:"",rightLowerTitle:"",dp03:[],aaa100Data:[],aaa100FirstValue:"",radioTitle:"",constituteOption:{},trendOption:{},orderOption:{},tabsOption:{},param:{},yzb001:"",yzb003:"",yzb004:"",dx:"",tabType:"aaz307",tabTypeName:"aae386",orderType:"desc",echartsUnit:"万元",orderTotalPages:0,orderPageNumber:1,orderPageSize:5,tabTotalPages:0,tabPageNumber:1,tabPageSize:10,akb021:"",ake002:"",aaa102:"",aaz307:null,aae387:null,ake001:"",y_val:0,y2_val:40,ksColor:"#32ADFF",ysColor:"#fff",showAsc:!1,showDesc:!0,tabShowLast:!1,tabShowNext:!1,orderShowLast:!1,orderShowNext:!1,constituteKey:1,trendkey:2,orderkey:3,key:"",tabskey:4,tbHidden:!1}},methods:{moment:o(),fnInIt:function(){var t=this;this.constituteKey+="constitute",this.trendkey+="trend",this.orderkey+="order",this.tabskey+="tabs",this.fnGetAAA100(),this.form.setFieldsValue({yzb001:this.$route.query.yzb001});var a={url:"rankAnaly/initPage",data:{yzb001:this.$route.query.yzb001},autoValid:!0},e={successCallback:function(a){t.dp03=a.data.dp03,t.form.setFieldsValue({dx:a.data.dx}),t.radioTitle=t.dp03.yzb004,t.title=t.dp03.yzb004,t.ake002=t.dp03.yzb004,t.yzb003=t.dp03.yzb003,t.leftUpperTitle=t.dp03.yzb004,t.rightUpperTitle=t.dp03.yzb004,t.leftLowerTitle=t.dp03.yzb004,t.rightLowerTitle="科室【"+t.dp03.yzb004+"】",t.param.orderPageNumber=1,t.param.tabPageNumber=1,setTimeout((function(){t.fnGetConsist(),t.fnGetTrend(),t.leftLowerTitle="【"+t.ake002+"】",t.fnGetOrder(),null==t.aae387&&t.fnGetTabs()}),100)},failCallback:function(t){}};this.Base.submit(null,a,e)},fnClickQuery:function(){this.constituteKey+="constitute",this.trendkey+="trend",this.orderkey+="order",this.tabskey+="tabs",this.fnReset(),this.fnGetConsist(),this.fnGetTrend(),this.fnGetOrder(),null==this.aae387&&this.fnGetTabs(),this.rightLowerTitle="科室【"+this.dp03.yzb004+"】",this.rightUpperTitle=this.dp03.yzb004,this.leftLowerTitle=this.dp03.yzb004},fnReset:function(){this.ake001=null,this.aaa102=null,this.tabPageNumber=1,this.orderPageNumber=1},fnGetAAA100:function(){var t=this,a={url:"rankAnaly/getDp02",data:{yzb001:this.$route.query.yzb001},autoValid:!0},e={successCallback:function(a){t.aaa100Data=a.data.aaa100Data,t.aaa100FirstValue=t.aaa100Data[0].value},failCallback:function(t){}};this.Base.submit(null,a,e)},switchDateType:function(t){1===t.target.value?(this.monthHidden=!1,this.quarterHidden=!0,this.yearHidden=!0,this.tbHidden=!1):2===t.target.value?(this.monthHidden=!0,this.quarterHidden=!1,this.yearHidden=!0,this.tbHidden=!1):(this.monthHidden=!0,this.quarterHidden=!0,this.yearHidden=!1,this.tbHidden=!0)},switchDataType:function(t){this.fnReset(),1===this.dataRadioValue?this.echartsUnit="万元":this.echartsUnit="%",this.orderPageNumber=1,this.tabPageNumber=1,this.fnInIt()},openPopWin:function(t){var a=this;"$tabType"===t&&(t="aaz263"===this.tabType?"doctor":"department");var e=this.fnGetParamter(),i=this.$route.query.yzb001;"4"===i?this.popWinParam.ake003="1":"5"===i?this.popWinParam.ake003="2":"6"===i&&(this.popWinParam.ake003="4"),this.popWinParam.rankType=t,this.popWinParam.dateType=e.ape032,this.popWinParam.dateMonth=e.month,this.popWinParam.dateQuarter=e.quarter,this.popWinParam.dateYear=e.year,this.popWinParam.aka130=e.aka130,this.popWinParam.aae140=e.aae140,this.popWinParam.aae141=e.aae141,this.popWinVisible=!0,setTimeout((function(){a.$refs.popWind.setDateType(a.popWinParam.dateType)}),300)},handlePopWinClose:function(){this.popWinVisible=!1},fnGetParamter:function(){return this.param=this.form.getFieldsValue(),this.param.month=this.form.getFieldValue("month").format("YYYYMM"),this.param.quarter=this.form.getFieldValue("quarter").format("YYYYQ"),this.param.year=this.form.getFieldValue("year").format("YYYY"),this.param.yzb003=this.yzb003,this.param.dataRadioValue=this.dataRadioValue,this.param.dateRadioValue=this.dateRadioValue,this.param.aae387=this.aae387,this.param.tabType=this.tabType,this.param.aaa102=this.aaa102,this.param.ake001=this.ake001,1==this.dateRadioValue?(this.param.aae043e=this.param.month,this.param.ape032="M"):2==this.dateRadioValue?(this.param.aae043e=this.param.quarter,this.param.ape032="S"):(this.param.aae043e=this.param.year,this.param.ape032="Y"),this.param},fnGetConsist:function(){var t=this;this.param=this.fnGetParamter();var a={url:"rankAnaly/getConsist",data:this.param,autoValid:!0},e={successCallback:function(a){t.constituteOption=a.data.constituteOption,1===t.dataRadioValue?t.$refs.constituteChart.updateOptions(l.Z.createPie(t.constituteOption,"name","val",t.echartsUnit)):t.$refs.constituteChart.updateOptions(l.Z.createStandBar(t.constituteOption,"name","val",t.echartsUnit,"id")),t.$refs.constituteChart.myChart.off("click"),t.$refs.constituteChart.myChart.on("click",(function(a){t.fnGetConsistCallback(a)}))},failCallback:function(t){}};this.Base.submit(this.form,a,e)},fnGetConsistCallback:function(t){this.orderPageNumber=1,this.tabPageNumber=1,this.akb021="",this.ake002=t.name,this.aaa102=t.data.id,this.ake001="",this.leftLowerTitle="【"+this.ake002+"】",this.fnGetOrder(),this.fnGetTrend(),null==this.aae387&&this.fnGetTabs()},fnSelectAaa100:function(){var t=this;setTimeout((function(){t.fnGetConsist()}),30)},fnGetTrend:function(){var t=this;this.param=this.fnGetParamter(),this.rightUpperTitle="【"+this.ake002+"】";var a={url:"rankAnaly/getTrend",data:this.param,autoValid:!0},e={successCallback:function(a){t.trendOption=a.data.trendOption,t.$refs.trendChart.updateOptions(l.Z.createLine(t.trendOption,"name","val",t.echartsUnit,t.title)),t.$refs.trendChart.myChart.off("click"),t.$refs.trendChart.myChart.on("click",(function(t){}))},failCallback:function(t){}};this.Base.submit(this.form,a,e)},fnGetOrder:function(){var t=this;this.param=this.fnGetParamter(),this.param.pageSize=this.orderPageSize,this.param.pageNumber=this.orderPageNumber,this.param.orderType=this.orderType,this.param.id_col="ake001",this.param.name_col="ake002",this.param.orderType=this.orderType;var a={url:"rankAnaly/getOrder",data:this.param,autoValid:!0},e={successCallback:function(a){t.orderOption=a.data.orderOption.data,t.orderTotalPages=a.data.orderOption.totalPages,t.orderPageNumber*t.orderPageSize>=t.orderTotalPages?t.orderShowNext=!1:t.orderShowNext=!0,1==t.orderPageNumber?t.orderShowLast=!1:t.orderShowLast=!0,null!=t.aae387?t.$refs.orderChart.updateOptions(l.Z.createStandBar(t.orderOption,"name","val",t.echartsUnit,"id")):t.$refs.orderChart.updateOptions(l.Z.createBar(t.orderOption,"name","val",t.echartsUnit)),t.$refs.orderChart.myChart.off("click"),t.$refs.orderChart.myChart.on("click",(function(a){t.fnGetOrderCallback(a)}))},failCallback:function(t){}};this.Base.submit(this.form,a,e)},fnGetOrderCallback:function(t){this.akb021="",this.ake001=t.data.id,this.ake002=t.name,this.aaa102="",this.fnGetTrend(),null==this.aae387&&this.fnGetTabs(),"aaz307"===this.tabType?this.rightLowerTitle="科室【"+this.ake002+"】":this.rightLowerTitle="医师【"+this.ake002+"】"},fnAscClick:function(){this.showDesc=!1,this.showAsc=!0,this.orderType="asc",this.y_val=0,this.y2_val=0,this.orderPageNumber=1,this.fnGetOrder()},fnDescClick:function(){this.showDesc=!0,this.showAsc=!1,this.orderType="desc",this.y_val=0,this.y2_val=0,this.orderPageNumber=1,this.fnGetOrder()},fnOrderLast:function(){this.orderPageNumber-=1,this.fnGetOrder(),this.orderPageNumber*this.orderPageSize>=this.orderTotalPages?(this.orderShowNext=!1,this.y2_val=0):(this.orderShowNext=!0,this.y2_val=40),1==this.orderPageNumber?(this.orderShowLast=!1,this.y_val=0):(this.orderShowLast=!0,this.y_val=40),this.param.ake001=""},fnOrderNext:function(){this.orderPageNumber+=1,this.fnGetOrder(),this.orderPageNumber*this.orderPageSize>=this.orderTotalPages?(this.orderShowNext=!1,this.y2_val=0):(this.orderShowNext=!0,this.y2_val=40),1==this.orderPageNumber?(this.orderShowLast=!1,this.y_val=0):(this.orderShowLast=!0,this.y_val=40),this.param.ake001=""},fnGetTabs:function(){var t=this;this.param=this.fnGetParamter(),this.param.pageSize=this.tabPageSize,this.param.pageNumber=this.tabPageNumber,this.param.id_col=this.tabType,this.param.name_col=this.tabTypeName,this.param.orderType="desc",this.rightLowerTitle="科室【"+this.ake002+"】";var a={url:"rankAnaly/getTabs",data:this.param,autoValid:!0},e={successCallback:function(a){t.tabTotalPages=a.data.tabsOption.totalPages,t.tabsOption=a.data.tabsOption.data,t.tabPageNumber*t.tabPageSize>=t.tabTotalPages?t.tabShowNext=!1:t.tabShowNext=!0,1==t.tabPageNumber?t.tabShowLast=!1:t.tabShowLast=!0,t.$refs.tabsChart.updateOptions(l.Z.createStandBar(t.tabsOption,"name","val",t.echartsUnit,"id")),t.$refs.tabsChart.myChart.off("click"),t.$refs.tabsChart.myChart.on("click",(function(a){t.fnGetTabsCallback(a)}))},failCallback:function(t){}};this.Base.submit(this.form,a,e)},fnGetTabsCallback:function(t){var a=t.name;if(this.aaa102=t.data.id,""!=this.aaa102&&null!=this.aaa102){var e=this.form.getFieldValue("aae141"),i=this.form.getFieldValue("aae140");if("aaz307"==this.tabType){var r="indexMonitor.html#/rankanalyKs?yzb001="+this.$route.query.yzb001+"&aae141="+e+"&aae140="+i+"&aaz307="+this.aaa102+"&ksm="+a+"&dataRadioValue="+this.dataRadioValue+"&aae043="+this.param.aae043e+"&ape032="+this.param.ape032+"&dateRadioValue="+this.dateRadioValue;top.indexTool.closeTabMenu("科室费用排名"+this.$route.query.yzb001),this.Base.openTabMenu({url:r,id:"科室费用排名"+this.$route.query.yzb001,name:a+this.title+"排名",refresh:!0})}else{var s="indexMonitor.html#/rankanalyYs?yzb001="+this.$route.query.yzb001+"&aae141="+e+"&aae140="+i+"&aaz263="+this.aaa102+"&ysm="+a+"&dataRadioValue="+this.dataRadioValue+"&aae043="+this.param.aae043e+"&ape032="+this.param.ape032+"&dateRadioValue="+this.dateRadioValue;top.indexTool.closeTabMenu("医师费用排名"+this.$route.query.yzb001),this.Base.openTabMenu({url:s,id:"医师费用排名"+this.$route.query.yzb001,name:a+this.title+"排名",refresh:!0})}}},fnKsClick:function(){this.ksColor="#32ADFF",this.ysColor="#fff",this.tabType="aaz307",this.tabTypeName="aae386",this.tabPageNumber=1,this.fnGetTabs(),this.rightLowerTitle="科室【"+this.dp03.yzb004+"】"},fnYsClick:function(){this.rightLowerTitle=this.yzb004,this.ysColor="#32ADFF",this.ksColor="#fff",this.tabType="aaz263",this.tabTypeName="aae387",this.tabPageNumber=1,this.fnGetTabs(),this.rightLowerTitle="医师【"+this.dp03.yzb004+"】"},fnTabLast:function(){this.tabPageNumber-=1,this.fnGetTabs(),this.tabPageNumber*this.tabPageSize>=this.tabTotalPages?this.tabShowNext=!1:this.tabShowNext=!0,1==this.tabPageNumber?this.tabShowNext=!1:this.tabShowNext=!0},fnTabNext:function(){this.tabPageNumber+=1,this.fnGetTabs(),this.tabPageNumber*this.tabPageSize>=this.tabTotalPages?this.tabShowNext=!1:this.tabShowNext=!0,1==this.tabPageNumber?this.tabShowNext=!1:this.tabShowNext=!0}},mounted:function(){this.fnInIt()}},v=w,T=(0,y.Z)(v,i,r,!1,null,"7b0b48d6",null),x=T.exports},55382:function(){},61219:function(){}}]);
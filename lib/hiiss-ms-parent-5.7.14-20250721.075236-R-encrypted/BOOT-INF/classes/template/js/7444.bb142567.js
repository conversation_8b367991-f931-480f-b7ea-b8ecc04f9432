"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[7444],{3078:function(t,e,a){a.r(e),a.d(e,{default:function(){return y}});var i=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("ta-layout",{staticClass:"fit",staticStyle:{"background-color":"#fff"}},[i("ta-drawer",{staticClass:"left-drawer",class:{"more-message":e.moreMessage},attrs:{placement:"left",visible:e.drawerVisible,closable:!1,mask:!1},on:{close:function(){e.drawerVisible=!1}},scopedSlots:e._u([{key:"title",fn:function(){return[i("ta-title",{attrs:{title:"患者列表"}},[i("span",{staticStyle:{float:"right","margin-left":"80px","margin-top":"-1.5px","font-size":"14px",color:"#303133","font-family":"Microsoft YaHei"}},[e._v("病区: "),i("ta-select",{attrs:{size:"small",options:e.orgLabelList},on:{change:e.aaz307Change},model:{value:e.aaz307,callback:function(t){e.aaz307=t},expression:"aaz307"}})],1)])]},proxy:!0}])},[i("div",{staticClass:"drawer-toggler",class:{"drawer-open":e.drawerVisible},staticStyle:{color:"rgb(39, 108, 245)"},on:{click:e.toggleMoreMessage}},[e.moreMessage?i("ta-icon",{attrs:{type:"double-left"}}):i("ta-icon",{attrs:{type:"double-right"}})],1),e.showPatientTab?i("ta-tabs",{attrs:{"default-active-key":e.patientActiveKey},on:{change:e.changePatientTab}},[i("ta-tab-pane",{key:"1",attrs:{tab:"我的患者"}}),i("ta-tab-pane",{key:"2",attrs:{tab:"诊疗组患者"}}),i("ta-tab-pane",{key:"3",attrs:{tab:"病区患者"}}),i("ta-tab-pane",{key:"4",attrs:{tab:"科室患者"}})],1):e._e(),i("div",{directives:[{name:"show",rawName:"v-show",value:e.showAaz309&&e.aaz309List&&e.aaz309List.length,expression:"showAaz309 && aaz309List && aaz309List.length"}],staticStyle:{height:"30px","margin-left":"10px"}},[i("ta-radio-group",{attrs:{size:"small"},model:{value:e.aaz309ActiveKey,callback:function(t){e.aaz309ActiveKey=t},expression:"aaz309ActiveKey"}},e._l(e.aaz309List,(function(t,a){return i("ta-radio-button",{key:a,staticStyle:{"font-size":"13px"},attrs:{value:t.aaz309},on:{click:function(a){return e.aaz309Change(t.aaz309)}}},[e._v(" "+e._s(t.aae387)+" ")])})),1)],1),i("div",{staticClass:"part-top1"},[i("div",{staticClass:"part-title"},[i("tag-change",{attrs:{"tag-value":e.tagValue,"status-arr":e.statusArr},on:{change:e.changeTag}})],1)]),i("div",{staticStyle:{height:"calc(100% - 160px)","margin-left":"10px","margin-top":"-10px"}},[i("ta-big-table",{ref:"xTable",staticStyle:{width:"calc(100% - 10px)",cursor:"pointer"},attrs:{border:"","highlight-hover-row":"","show-overflow":"","sort-config":{trigger:"cell"},"checkbox-config":{trigger:"row"},data:e.dataSource,height:"auto",resizable:!0,"auto-resize":"",size:"mini","empty-text":"-",width:"min-width","row-style":e.rowStyle,"cell-style":e.cellStyle},on:{"cell-click":e.checkPromptState},scopedSlots:e._u([{key:"empty",fn:function(){return[i("ta-empty",{attrs:{description:e.emptyDescription}})]},proxy:!0}])},[i("ta-big-table-column",{attrs:{type:"seq",fixed:"left",width:"20px",title:" ",align:"center"}}),i("ta-big-table-column",{attrs:{field:"aaz217",title:"管理Id",visible:!1}}),i("ta-big-table-colgroup",{scopedSlots:e._u([{key:"header",fn:function(){return[i("ta-input",{attrs:{placeholder:"筛选患者/床位",size:"small",type:"type"},on:{keyup:e.searchEvent},model:{value:e.filterParam,callback:function(t){e.filterParam=t},expression:"filterParam"}})]},proxy:!0}])},[i("ta-big-table-column",{attrs:{sortable:"",field:"name",title:"患者",align:"left",width:e.widthConfig.name}}),i("ta-big-table-column",{attrs:{sortable:"",field:"bch",title:"床位",align:"left","min-width":e.widthConfig.bch}})],1),i("ta-big-table-column",{attrs:{sortable:"",field:"akc191",title:"就诊号",align:"center",width:"120px"}}),i("ta-big-table-column",{attrs:{field:"chfpdrName",title:"主治医生",align:"center","min-width":"100px"}}),i("ta-big-table-column",{attrs:{field:"aae376",title:"科室",align:"center","min-width":"110px"}}),i("ta-big-table-column",{attrs:{field:"wardname",title:"病区",align:"center","min-width":"120px"}}),i("ta-big-table-column",{attrs:{field:"aze003",title:"处理进度",align:"center",width:"110px"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[a.aze003.indexOf("待处理")>-1?i("div",[i("ta-icon",{attrs:{type:"exclamation-circle"}}),e._v(" "+e._s(a.aze003)+" ")],1):i("div",[i("ta-icon",{attrs:{type:"check-circle"}}),e._v(" "+e._s(a.aze003)+" ")],1)]}}])})],1)],1),e.support.length>0?i("div",{staticClass:"supportDiv"},[e._v(" "+e._s(e.supportText)+"   "),e._l(e.support,(function(t,a){return i("span",{key:a},[i("span",{staticStyle:{display:"inline-block",width:"auto"}},[e._v(e._s(t.supportnum)+"  ( "+e._s(t.supportname)+");")]),e._v("   ")])}))],2):e._e()],1),i("ta-layout-content",{staticClass:"fit mian-content",staticStyle:{"margin-left":"28%"}},[i("div",[i("ta-form",{staticStyle:{"margin-left":"17px","margin-top":"3px"},attrs:{"auto-form-create":function(e){return t.form=e},layout:"horizontal","form-layout":!0,"label-width":"80px",col:{xs:1,sm:1,md:1,lg:1,xl:1,xxl:1}}},[i("ta-form-item",{attrs:{label:"患者信息","field-decorator-id":"opterName"}},[e._v(" "+e._s(e.patientDetails.optername||"")+" ")]),i("ta-form-item",{attrs:{label:"临床诊断","field-decorator-id":"clDiagNameList"}},[e._v(" "+e._s(e.patientDetails.cldiagnamelist||"")+" ")]),i("ta-form-item",{attrs:{label:"医保诊断","field-decorator-id":"hiDiagNameList"}},[i("span",{domProps:{innerHTML:e._s(e.patientDiagName)}})])],1),e.patientObjects.length?i("div",{staticClass:"part-top",staticStyle:{"margin-top":"0px !important"}},[i("div",{staticClass:"part-title"},[i("ta-title",{staticStyle:{float:"left"},attrs:{title:"待审查项目"}}),i("ta-tooltip",{staticStyle:{"margin-left":"5px"},attrs:{placement:"top"}},[i("template",{slot:"title"},[i("span",[e._v("双击可查看规则内涵，点击行展开查看项目明细")])]),i("ta-icon",{attrs:{type:"question-circle"}})],2)],1)]):e._e(),e.patientObjects.length?i("div",{style:{width:"97%",marginLeft:"10px",marginTop:"5px",height:this.height+"px"}},["n"!=e.customTipsText.toLocaleLowerCase()?i("span",{staticStyle:{"font-size":"12px",height:"12px",color:"#F50"}},[e._v(e._s(this.customTipsText))]):e._e(),i("ta-big-table",{ref:"dTable",staticStyle:{width:"100%",cursor:"pointer"},attrs:{border:"","highlight-hover-row":"","show-overflow":"",data:e.patientObjects,height:"auto",resizable:!0,"auto-resize":"",size:"mini","empty-text":"-",width:"min-width"},on:{"cell-dblclick":e.queryRuleInfo,"cell-click":e.cellClickEvent,"toggle-row-expand":e.toggleExpandChangeEvent}},[i("ta-big-table-column",{attrs:{type:"expand",visible:!0,width:"20"},scopedSlots:e._u([{key:"content",fn:function(t){var a=t.row;return[i("ta-big-table",{ref:"dTablechildren",staticStyle:{"margin-left":"20px"},attrs:{data:a.children,border:"","highlight-hover-row":"","show-overflow":"","auto-resize":"","empty-text":"-",size:"mini",resizable:!0}},[i("ta-big-table-column",{attrs:{type:"seq",width:"20px",align:"center",title:" "}}),i("ta-big-table-column",{attrs:{field:"aaz217",title:"管理Id",visible:!1}}),i("ta-big-table-column",{attrs:{field:"aaz213",title:"管理Id",visible:!1}}),i("ta-big-table-column",{attrs:{field:"ake006",align:"left",title:"项目名称","show-overflow":!1,"min-width":e.widthConfig.ake006},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("ta-tooltip",{attrs:{placement:"topLeft"}},[i("template",{slot:"title"},[e._v(" "+e._s(a.ake006)+" ")]),i("div",{staticClass:"tex-over-wrapper"},[e._v(" "+e._s(a.ake006)+" ")])],2)]}}],null,!0)}),i("ta-big-table-column",{attrs:{field:"aae036",align:"center",title:"明细发生时间","min-width":e.widthConfig.aae036}}),i("ta-big-table-column",{attrs:{field:"akc226",align:"right",title:"数量","min-width":e.widthConfig.akc226},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[a.akc226?i("span",[e._v(e._s(a.akc226))]):i("span",[e._v("--")])]}}],null,!0)}),i("ta-big-table-column",{attrs:{field:"aac003",align:"left",title:"开单医生","min-width":e.widthConfig.aac003}}),i("ta-big-table-column",{attrs:{field:"aaz560",align:"left",title:"备案信息","min-width":e.widthConfig.aaz560}}),i("ta-big-table-column",{attrs:{field:"oparete",align:"center",title:"操作","min-width":e.widthConfig.oparete},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("ta-checkbox-group",{staticClass:"custom-anchor",attrs:{value:a.operate}},e._l(a.options,(function(t){return i("span",{key:t.value,staticClass:"opareteItem"},[e._v(" "+e._s(t.label)+"  "),i("ta-checkbox",{attrs:{value:t.value},on:{change:function(t){return e.checkClick2(t,a)}}})],1)})),0)]}}],null,!0)})],1)]}}],null,!1,3133342020)}),i("ta-big-table-column",{attrs:{type:"seq",width:"20px",title:" ",align:"center"}}),i("ta-big-table-column",{attrs:{field:"aaz217",title:"管理Id",visible:!1}}),i("ta-big-table-column",{attrs:{field:"aaz213",title:"管理Id",visible:!1}}),i("ta-big-table-column",{attrs:{field:"ake006",title:"项目名称","show-overflow":!1,"min-width":e.widthConfig.ake006},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("ta-tooltip",{attrs:{placement:"topLeft"}},[i("template",{slot:"title"},[e._v(" "+e._s(a.ake006)+" ")]),i("div",{staticClass:"tex-over-wrapper"},[e._v(" "+e._s(a.ake006)+" ")])],2)]}}],null,!1,3034155790)}),i("ta-big-table-column",{attrs:{field:"ykz018",title:"引导信息","show-overflow":!1,"min-width":e.widthConfig.ykz018},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("ta-tooltip",{attrs:{placement:"topLeft"}},[i("template",{slot:"title"},[e._v(" "+e._s(a.ykz018)+" ")]),i("div",{staticClass:"tex-over-wrapper"},[e._v(" "+e._s(a.ykz018)+" ")])],2)]}}],null,!1,3441798542)}),i("ta-big-table-column",{attrs:{field:"akc226",title:"数量",align:"right","min-width":e.widthConfig.akc226}}),i("ta-big-table-column",{attrs:{field:"akc225",title:"单价",align:"right","min-width":e.widthConfig.akc225,formatter:"formatAmount"}}),i("ta-big-table-column",{attrs:{field:"aaz560",title:"备案信息","min-width":e.widthConfig.aaz560}}),i("ta-big-table-column",{attrs:{field:"oparete",title:"操作","min-width":e.widthConfig.oparete,align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("ta-checkbox-group",{staticClass:"custom-anchor",attrs:{value:a.operate}},e._l(a.options,(function(t){return i("span",{key:t.value,staticClass:"opareteItem"},[e._v(" "+e._s(t.label)+"  "),i("ta-checkbox",{attrs:{value:t.value},on:{change:function(t){return e.checkClick(t,a)}}})],1)})),0)]}}],null,!1,**********)})],1)],1):e._e(),e.patientBillObjects.length?i("div",{staticClass:"part-top"},[i("div",{staticClass:"part-title"},[i("ta-title",{staticStyle:{float:"left"},attrs:{title:"诊疗监测审查结果"}}),i("ta-tooltip",{staticStyle:{"margin-left":"5px"},attrs:{placement:"top"}},[i("template",{slot:"title"},[i("span",[e._v("双击可查看规则内涵")])]),i("ta-icon",{attrs:{type:"question-circle"}})],2)],1)]):e._e(),e.patientBillObjects.length?i("div",{staticStyle:{width:"97%","margin-left":"10px","margin-top":"5px",height:"250px"}},[i("ta-big-table",{ref:"bTable",staticStyle:{width:"100%",cursor:"pointer"},attrs:{border:"","highlight-hover-row":"","show-overflow":"",data:e.patientBillObjects,height:"auto",resizable:!0,"auto-resize":"",size:"mini","empty-text":"-",width:"min-width"},on:{"cell-dblclick":e.queryRuleInfo}},[i("ta-big-table-column",{attrs:{type:"seq",fixed:"left",width:"50px",title:" ",align:"center"}}),i("ta-big-table-column",{attrs:{field:"aaa167",title:"审查项","min-width":"100px"}}),i("ta-big-table-column",{attrs:{field:"ykz018",title:"引导信息","min-width":"100px"}})],1)],1):e._e(),i("div",{directives:[{name:"show",rawName:"v-show",value:e.buttonVisiable,expression:"buttonVisiable"}],style:{marginLeft:"40%",marginTop:"n"!=e.customTipsText.toLocaleLowerCase()?"27px":"15px"}},[i("ta-button",{staticClass:"ok-btn",attrs:{type:"primary"},on:{click:e.handleSaveCheck}},[e._v(" 确认 ")])],1),i("div",[i("ta-modal",{attrs:{height:"70%",width:"85%","body-style":{paddingRight:0,paddingTop:"5px"},draggable:!0,"destroy-on-close":!0,footer:null},model:{value:e.visible,callback:function(t){e.visible=t},expression:"visible"}},[i("span",{staticStyle:{"font-weight":"normal","font-size":"14px","text-align":"center",height:"0",width:"100%"},attrs:{slot:"title"},slot:"title"},[e._v(" 规则内涵查看")]),i("div",{staticStyle:{"margin-top":"5px",height:"auto"}},[i("div",{staticClass:"knowledgeTitle"},[e._v(" 限制条件 ")]),i("div",{staticClass:"knowledgeRuleContent"},[e._v(" "+e._s(e.ykz018)+" ")])]),i("div",{staticStyle:{width:"100%",height:"calc(100% - 50px)","margin-top":"5px"}},[i("div",{staticClass:"knowledgeTitle"},[e._v(" 规则内涵 ")]),i("div",{staticStyle:{display:"inline-block",width:"90%","vertical-align":"top","margin-left":"8px"}},[i("ta-collapse",{attrs:{id:"appH",accordion:!0},on:{change:e.changeNodeActivekey}},e._l(e.nodeList,(function(t,a){return i("ta-collapse-panel",{key:a,staticClass:"knowledgeContentTitle",attrs:{header:e.convertYkz061(t.ykz061)}},[i("div",{staticStyle:{"text-align":"right"}},[i("ta-input-search",{staticStyle:{width:"200px","margin-bottom":"1px"},attrs:{placeholder:"输入关键字搜索","allow-clear":""},on:{search:e.searchfn}})],1),i("div",[i("ta-big-table",{attrs:{size:"small","header-cell-style":function(){return{padding:"2px"}},data:e.nodeData},scopedSlots:e._u([{key:"bottomBar",fn:function(){return[i("ta-pagination",{staticStyle:{"text-align":"right"},attrs:{"show-size-changer":"",size:"small","default-page-size":200,"page-size-options":["30","50","100","200","500"],total:e.total,"dataSource.sync":"nodeData"},on:{showSizeChange:e.onShowSizeChange,change:e.changePage},model:{value:e.current,callback:function(t){e.current=t},expression:"current"}})]},proxy:!0}],null,!0)},e._l(e.columns,(function(t,e){return i("ta-big-table-column",{key:e,attrs:{field:t.columnField,title:t.columnName}})})),1)],1)])})),1)],1)])])],1),i("div",[i("ta-modal",{attrs:{title:"提示信息",visible:e.promptVisible,confirmLoading:e.promptConfirmLoading,okText:"继续提交",cancelText:"取消提交"},on:{ok:e.handleSave,cancel:e.handleCancel}},[e.promptMessages.length>0?i("div",[e._l(e.promptMessages,(function(t,a){return i("div",[i("p",[e._v(e._s(t))])])})),i("p",[e._v("继续提交将覆盖，系统仅保留您的操作，请确认是否继续提交?")])],2):e._e()])],1)],1),i("keep-on-record",{attrs:{visible:e.keepOnRecordVisible,params:e.rowData},on:{handleSave:e.handleUpdateRow,handleClose:e.handleClose}}),i("keep-on-record-two",{ref:"keeponrecord",attrs:{visible:e.keepOnRecordVisible2,params:e.rowData,"ykz010-arr":e.ykz010Arr,"ykz042-arr":e.ykz042Arr},on:{handleSave:e.handleUpdateRow2,handleClose:e.handleClose2}})],1)],1)},n=[],r=a(89584),s=a(48534),l=(a(36133),a(93528)),o=a(36797),c=a.n(o),u=a(80506),p=a(88412),d=a(92378),h=a(18338),f=a(94628),g=(a(83231),[{value:"1",label:"待处理",checked:!0,color1:"orange",color2:"#FCB76B",icon:"待"},{value:"2",label:"已处理",checked:!1,color1:"green",color2:"#87d068",icon:"已"},{value:"3",label:"全部",checked:!1,color1:"blue",color2:"#2db7f5",icon:"全"}]),m={normal:{ake006:"130px",ykz018:"130px",akc226:"30px",akc225:"30px",aaz560:"50px",aae036:"50px",aac003:"50px",oparete:"280px",name:"110px",bch:"140px"},mini:{ake006:"120px",ykz018:"120px",akc226:"20px",akc225:"20px",aaz560:"20px",aae036:"30px",aac003:"30px",oparete:"250px",name:"50px",bch:"110px"}},b={name:"approvalHandle",components:{keepOnRecord:d.Z,TagChange:u.Z,TaTitle:p.Z,KeepOnRecordTwo:h.Z},data:function(){return{widthConfig:m.normal,buttonVisiable:!1,moreMessage:!0,drawerVisible:!0,patientActiveKey:"1",statusArr:g,tagValue:"1",clickFlag:"",dataSource:[],dataRange:"self",aaz217:"",preAaz217:"",selectRow:{},patientDetails:{},patientObjects:[],patientBillObjects:[],patientDiagnose:[],patientDiagName:"",keepOnRecordVisible:!1,keepOnRecordVisible2:!1,permissions:{aaz263Disable:!0},aaz309List:[],rowData:{},filterParam:"",ykz010Arr:[],ykz042Arr:[],aaz263:"",aaz307:"",systemName:"",cainfo:"",customTipsText:"N",showAaz309:!1,aaz309:null,clientId:"",height:0,visible:!1,ykz018:"",nodeList:[],activeKey:[],aaz309ActiveKey:null,columns:[],nodeData:[],params:{},expandedRow:{},ykz042:"",searchText:"",pageSize:10,current:1,total:0,showPatientTab:!0,support:[],supportText:"",promptVisible:!1,promptConfirmLoading:!1,promptMessages:[],orgLabelList:[]}},computed:{emptyDescription:function(){var t={1:"您没有待处理的疑点信息",2:"您没有已处理的疑点信息",3:"您没有已处理和待处理的疑点信息"};return t[this.tagValue]}},watch:{pageSize:function(t){},current:function(t){this.current=t}},mounted:function(){var t=this;return(0,s.Z)(regeneratorRuntime.mark((function e(){var a,i,n,r;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.tagValue="1",a=t.$route.query,!a||!a.aaz263){e.next=10;break}t.aaz263=a.aaz263,t.aaz307=a.aaz307,t.systemName=a.systemName,t.cainfo=a.cainfo,"2"!==t.systemName&&"3"!==t.systemName||(t.showPatientTab=!1),e.next=17;break;case 10:return e.next=12,Base.submit(null,{url:"miimCommonRead/initBaseInfo",data:{},autoQs:!1});case 12:i=e.sent,t.aaz263=i.data.data.aaz263,t.systemName=i.data.data.systemName,i.data.data.orgLabelList&&i.data.data.orgLabelList.length>0&&(t.aaz307=i.data.data.orgLabelList[0].value,t.orgLabelList=i.data.data.orgLabelList),"2"!==t.systemName&&"3"!==t.systemName||(t.showPatientTab=!1);case 17:if("1"!==t.systemName){e.next=22;break}return e.next=20,Base.submit(null,{url:"nightAudit/queryGroupDic",data:{aaz263:t.aaz263},autoQs:!1});case 20:n=e.sent,t.aaz309List=n.data.data;case 22:t.aaz309List&&t.aaz309List.length>0&&(t.aaz309=t.aaz309List[0].value,t.aaz309ActiveKey=t.aaz309List[0].value),r=navigator.userAgent,r.indexOf("DeepBlue")>-1?(t.isDeepBlue=!0,QClient.DBPNSGetClientId((function(e){t.clientId=e.data,t.querysupport(),t.queryTableData()}))):t.queryTableData(),t.__fixTableLayoutWhenSmallScreen(),window.addEventListener("resize",t.__fixTableLayoutWhenSmallScreen);case 27:case"end":return e.stop()}}),e)})))()},unmounted:function(){window.removeEventListener("resize",this.__fixTableLayoutWhenSmallScreen)},methods:{__fixTableLayoutWhenSmallScreen:function(){var t=this;this.widthConfig=m[parseInt(window.innerWidth)>1024?"normal":"mini"],this.$nextTick((function(){t.__fixOperationLayout()}))},__fixOperationLayout:function(){var t=Array.from(document.getElementsByClassName("custom-anchor"));t.forEach((function(t){var e=t.parent||t.parentNode||t.parentElement;if(e){var a=e.parent||e.parentNode||e.parentElement;e.style.width=parseInt(a.clientWidth)+6+"px"}}))},searchEvent:TaUtils.debounce((function(){this.queryTableData()}),1e3,{leading:!1,trailing:!0}),isSmallScreen:function(){var t=document.getElementsByClassName("drawer-toggler");if(t){var e=t[0],a=getComputedStyle(e);return"block"===a.display}return!1},toggleMoreMessage:function(){this.moreMessage=!this.moreMessage,this.$refs.xTable.scrollTo(0)},toggleDrawer:function(){this.drawerVisible=!this.drawerVisible},cellClickEvent:function(t){var e=t.column,a=t.row;this.expandedRow=a,e.property||"oparete"==e.property||this.$refs.dTable.toggleRowExpand(a)},headerCellStyle:function(t){t.column,t.columnIndex;return{fontSize:"12px",backgroundColor:"#ffffff"}},toggleExpandChangeEvent:function(t){var e=t.row,a=t.expanded;a&&(this.expandedRow=e),this.$refs.dTable.toggleRowExpand(e)},moment:c(),changeTag:function(t){this.tagValue=t.value,this.queryTableData()},changePatientTab:function(t){this.patientActiveKey=t,this.showAaz309="2"==t,this.queryTableData()},convertYkz061:function(t){var e=t.match(/^<(.*)>(.*)<\/\1>$/);return"".concat(e[1],":").concat(e[2])},aaz307Change:function(t){this.aaz307=t,this.queryTableData()},aaz309Change:function(t){this.aaz309=t,this.queryTableData()},queryTableData:function(){var t=this;this.patientDetails={},this.patientObjects=[],this.patientBillObjects=[],this.patientDiagnose=[],this.buttonVisiable=!1,this.showPatientTab||(this.patientActiveKey="3");var e={aaz307:this.aaz307,aaz263:this.aaz263,aaz309:this.aaz309,clientId:this.clientId,systemName:this.systemName,filterParam:this.filterParam,cainfo:this.cainfo,queryType:this.patientActiveKey};e.aze003="1"===this.tagValue?"0":"2"===this.tagValue?"1":"",l.Z.queryTableDataForTongJi(e,(function(e){t.dataSource=e.data.data}))},checkPromptState:function(t){var e=this,a=t.row;if("1"===this.systemName){var i={aaz263:this.aaz263,aaz217:a.aaz217,clientId:this.clientId};l.Z.checkPromptState(i,(function(t){"1"===t.data.data?e.$confirm({content:"该患者数据有其他人正在处理，请确认是否要继续操作？继续操作将覆盖其他人的操作",onOk:function(){e.preAaz217||(e.preAaz217=a.aaz217),e.getPatientInfo(a),e.preAaz217=a.aaz217},onCancel:function(){}}):(e.getPatientInfo(a),e.preAaz217=a.aaz217)}))}else this.getPatientInfo(a)},getPatientInfo:function(t){var e=this;this.patientDetails={},this.patientObjects=[],this.patientBillObjects=[],this.patientDiagnose=[],this.buttonVisiable=!1,this.selectRow=t,this.aaz217=t.aaz217;var a={aaz307:this.aaz307,aaz263:"self"===this.dataRange?this.aaz263:null,aaz217:t.aaz217,aaz309:"group"===this.dataRange?this.aaz309:null,clientId:this.clientId,systemName:this.systemName,preAaz217:this.preAaz217,queryType:this.patientActiveKey};l.Z.getRowDetails(a,(function(t){t.data.customTipsText&&(e.customTipsText=t.data.customTipsText),e.patientDiagName="",e.patientDetails=t.data.data.personInfo,e.patientObjects=t.data.data.kf10List.map((function(t){for(var e=t.bxczid.split(";"),a=e[0].substring(1,e[0].length-1).split(","),i=e[1].substring(1,e[1].length-1).split(","),n=new Map,r=0;r<a.length;r++)n.set(i[r],a[r]);return t.options=[],t.bxczid.indexOf("1")>-1&&t.options.push({value:"1",label:n.get("1")}),t.bxczid.indexOf("2")>-1&&t.options.push({value:"2",label:n.get("2")}),t.bxczid.indexOf("3")>-1&&t.options.push({value:"3",label:n.get("3")}),t.bxczid.indexOf("4")>-1&&t.ykz042&&t.options.push({value:"4",label:n.get("4")}),t.bxczid.indexOf("5")>-1&&t.options.push({value:"5",label:n.get("5")}),t.operate="0"===t.ape893?[]:t.ape893.split(","),t.aaz560="0"===t.aaz560?"":t.aaz560,t.children.forEach((function(t){delete t.children,t.operate="0"===t.ape893?[]:t.ape893.split(","),t.options=[],t.bxczid.indexOf("1")>-1&&t.options.push({value:"1",label:n.get("1")}),t.bxczid.indexOf("2")>-1&&t.options.push({value:"2",label:n.get("2")}),t.bxczid.indexOf("3")>-1&&t.options.push({value:"3",label:n.get("3")}),t.bxczid.indexOf("4")>-1&&t.ykz042&&t.options.push({value:"4",label:n.get("4")}),t.bxczid.indexOf("5")>-1&&t.options.push({value:"5",label:n.get("5")})})),t}));var a=32*(e.patientObjects.length+6);if(e.height=a,e.patientBillObjects=t.data.data.kf59List,e.patientDiagnose=t.data.data.kc21k1Pos,e.patientDiagnose)for(var i=0;i<e.patientDiagnose.length;i++)"1"==e.patientDiagnose[i].aka016?e.patientDiagName+='<span style="color:#FF9901">'+e.patientDiagnose[i].aka121+"【"+e.patientDiagnose[i].aka120+"】; </span>":e.patientDiagName+="<span>"+e.patientDiagnose[i].aka121+"【"+e.patientDiagnose[i].aka120+"】; </span>";e.buttonVisiable=!0,e.isSmallScreen()&&(e.moreMessage=!1)}))},checkClick:function(t,e){!1===t.target.checked&&t.target.checked,"1"!=t.target.value&&"4"!=t.target.value&&(e.aaz560="",this.$refs.keeponrecord.fnReset()),"4"==t.target.value&&e.operate.includes("1")&&(e.aaz560="",this.$refs.keeponrecord.fnReset()),"1"==t.target.value&&e.operate.includes("4")&&(e.aaz560="",this.$refs.keeponrecord.fnReset()),e.operate=[t.target.value],"1"===t.target.value?(e.aaz263=this.aaz263,this.rowData=e,this.keepOnRecordVisible=!0):"4"===t.target.value&&(this.rowData=e,this.ykz010Arr=e.ykz010.split(","),this.ykz042Arr=e.ykz042.split(","),this.keepOnRecordVisible2=!0),e.children.forEach((function(t){t.operate=e.operate,t.aaz560=e.aaz560})),this.$refs.dTable.updateStatus(e),this.clickFlag="father"},checkClick2:function(t,e){!1===t.target.checked&&t.target.checked,"1"!=t.target.value&&"4"!=t.target.value&&(e.aaz560="",this.childrenChange(),this.$refs.keeponrecord.fnReset()),"4"==t.target.value&&e.operate.includes("1")&&(e.aaz560="",this.childrenChange(),this.$refs.keeponrecord.fnReset()),"1"==t.target.value&&e.operate.includes("4")&&(e.aaz560="",this.childrenChange(),this.$refs.keeponrecord.fnReset()),e.operate=[t.target.value],"1"===t.target.value?(e.aaz263=this.aaz263,this.rowData=e,this.keepOnRecordVisible=!0):"4"===t.target.value&&(this.rowData=e,this.ykz010Arr=e.ykz010.split(","),this.ykz042Arr=e.ykz042.split(","),this.keepOnRecordVisible2=!0),this.$refs.dTablechildren.updateStatus(e),this.fatherChange(),this.clickFlag="child"},childrenChange:function(){var t=new Set;this.expandedRow.children.forEach((function(e){t.add(e.aaz560)}));var e=Array.from(t);this.expandedRow.aaz560=e.join(";"),this.$refs.dTable.updateStatus(this.expandedRow)},fatherChange:function(){var t=new Set;this.expandedRow.children.forEach((function(e){e.operate.forEach((function(e){t.add(e)}))})),this.expandedRow.operate=(0,r.Z)(t),this.$refs.dTable.updateStatus(this.expandedRow)},fatherChangeClose:function(){var t=this,e=!1,a=new Set;"father"==this.clickFlag&&(this.expandedRow.operate=[],this.expandedRow.aaz560=""),this.expandedRow.children.forEach((function(i){i.operate.length>0&&(e=!0),"father"==t.clickFlag?(i.operate=[],i.aaz560=""):i.operate.forEach((function(t){a.add(t)}))})),e||(this.expandedRow.aaz560=""),this.expandedRow.operate=(0,r.Z)(a),this.$refs.dTable.updateStatus(this.expandedRow)},handleUpdateRow:function(t){var e=this;this.keepOnRecordVisible=!1,this.rowData.aaz560=t.content,this.rowData.children&&this.rowData.children.forEach((function(t){return t.aaz560=e.rowData.aaz560})),this.$refs.dTable.updateStatus(this.rowData),this.rowData.children||this.childrenChange()},handleUpdateRow2:function(t){var e=this;this.keepOnRecordVisible2=!1,this.rowData.aaz560=t.content,this.rowData.children&&this.rowData.children.forEach((function(t){return t.aaz560=e.rowData.aaz560})),this.$refs.dTable.updateStatus(this.rowData),this.rowData.children||this.childrenChange()},handleClose:function(){this.keepOnRecordVisible=!1,this.rowData.operate=[],this.rowData.aaz560="",this.fatherChangeClose()},handleClose2:function(){this.keepOnRecordVisible2=!1,this.rowData.operate=[],this.rowData.aaz560="",this.$refs.keeponrecord.fnReset(),this.fatherChangeClose()},handleSaveCheck:function(){var t=this;if("1"===this.systemName){var e={aaz263:this.aaz263,aaz217:this.aaz217,clientId:this.clientId};l.Z.checkPromptState(e,(function(e){"1"===e.data.data?t.$confirm({content:"该患者数据有其他人正在处理，请确认是否要继续操作？继续操作将覆盖其他人的操作",onOk:function(){t.handleSave()},onCancel:function(){}}):t.handleSave()}))}else if("3"===this.systemName){var a=this.$refs.dTable.getTableData().fullData,i=new Set;a.forEach((function(t){t.children.forEach((function(t){t.operate.length&&i.add(t.aaz213)}))}));var n=(0,r.Z)(i),s={aaz263:this.aaz263,aaz217:this.aaz217,clientId:this.clientId,aaz213s:n};l.Z.checkHandleState(s,(function(e){"0"===e.data.data.code?(t.promptVisible=!0,t.promptMessages=e.data.data.messages):t.handleSave()}))}else this.handleSave()},handleSave:function(){var t=this,e=this.$refs.dTable.getTableData().fullData,a=new Set;e.forEach((function(t){t.children.forEach((function(t){t.operate.length&&a.add(t)}))}));var i=(0,r.Z)(a);this.$nextTick((function(){i=i.map((function(t){return t.ape893=t.operate[0],t}));var e={aaz217:t.aaz217,aae500:"2",aaz263:t.aaz263,systemName:t.systemName,clientId:t.clientId,kf10OprPoList:i};l.Z.updatePatientInfo(e,(function(e){t.$message.success("保存成功"),t.promptVisible=!1,t.queryTableData(),t.isSmallScreen()&&(t.moreMessage=!0)}))}))},handleCancel:function(){this.promptVisible=!1},cellStyle:function(t){var e=t.row,a=(t.rowIndex,t.column);t.columnIndex;if("aze003"===a.property){if(e.aze003.indexOf("已处理")>-1)return{color:"#0F990F"};if(e.aze003.indexOf("待处理")>-1)return{color:"#E4393C"}}},rowStyle:function(t){var e=t.row;t.rowIndex;return this.selectRow.aaz217===e.aaz217?{backgroundColor:"rgb(235,244,250)"}:{backgroundColor:"#ffffff"}},queryRuleInfo:function(t){var e=this,a=t.column,i=t.row;if(a.property)if(i.ykz032){var n=i.ykz032,r=[];void 0!==n&&(r=n.split(",")),l.Z.queryRuleInfo({ykz032s:r},(function(t){if(!1!==t.serviceSuccess){var a=t.data.resultData.ykz018,i="";if(void 0!==a){for(var n=0;n<a.length;n++)i+="【"+a[n]+"】";e.ykz018=i,e.nodeList=t.data.resultData.nodeInfoList}}else e.$message.error(t.errors[0].msg)})),this.visible=!0}else this.$message.warn("该审查项无规则内涵")},changeNodeActivekey:function(t){var e=this;if(!(0,f.Z)(t)){var a=t;if(!(0,f.Z)(a)){var i={};i.ykz042=this.nodeList[a].ykz042,i.pageParam={},i.pageParam.pageNumber=0,i.pageParam.pageSize=10,this.ykz042=i.ykz042,l.Z.queryPagePackageRule(i,(function(t){if(!1!==t.serviceSuccess){e.columns=t.data.resultData.columnInfo;var a=t.data.resultData.pageData;e.nodeData=a.list,e.current=a.pageNum,e.pagesize=a.pageSize,e.total=a.total}else e.$message.error(t.errors[0].msg)}))}}},searchfn:function(t,e){this.searchText=t,this.changePage(1,this.pagesize)},onShowSizeChange:function(t,e){this.changePage(t,e)},changePage:function(t,e){var a=this,i={};i.ykz042=this.ykz042,i.searchText=this.searchText,i.pageParam={},i.pageParam.pageNumber=t,i.pageParam.pageSize=e,l.Z.queryPagePackageRule(i,(function(t){if(!1!==t.serviceSuccess){a.columns=t.data.resultData.columnInfo;var e=t.data.resultData.pageData;a.nodeData=e.list,a.current=e.pageNum,a.pagesize=e.pageSize,a.total=e.total}else a.$message.error(t.errors[0].msg)}))},querysupport:function(){var t=this;this.Base.submit(null,{url:"assistantWindow/querySupport",data:{},autoValid:!1},{successCallback:function(e){var a=e.data;if(a.namelenth>0&&a.numlenth>0){t.supportText="服务支持咨询:";var i=1;i=a.namelenth>a.numlenth?a.namelenth:a.numlenth;for(var n=0;n<i;n++)a["supportname"+(0==n?"":n+1)]&&a["supportnum"+(0==n?"":n+1)]&&t.support.push({supportname:a["supportname"+(0==n?"":n+1)]+" ",supportnum:a["supportnum"+(0==n?"":n+1)]})}else t.supportText=""},failCallback:function(e){t.$message.error("查询失败")}})}}},z=b,v=a(1001),w=(0,v.Z)(z,i,n,!1,null,"5f289950",null),y=w.exports},83231:function(t,e,a){var i=a(48534);a(36133);function n(t){return t?t.akb020&&t.aaz263?"personal":t.akb020&&t.aaz309&&t.aaz307?"group":t.akb020&&t.aaz307?"department":t.akb020?"hospital":"all":"all"}function r(t){return s.apply(this,arguments)}function s(){return s=(0,i.Z)(regeneratorRuntime.mark((function t(e){var a,i,r,s,l,o,c,u,p,d,h,f;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Base.submit(null,{url:"/miimCommonRead/queryDataPermission",showPageLoading:!1,data:{jobnumber:e},autoValid:!0});case 2:return a=t.sent,i=new Set,r=new Set,a.data.permission.forEach((function(t){var e=n(t);"hospital"===e&&i.add(t.akb020),"department"===e&&r.add(t.aaz307)})),s=a.data.permission.filter((function(t){return"department"===n(t)||!r.has(t.aaz307)})).filter((function(t){return"hospital"===n(t)||!i.has(t.akb020)})),l=new Set(s.map((function(t){return t.akb020})).filter((function(t){return null!=t}))),o=new Set(s.map((function(t){return t.aaz307})).filter((function(t){return null!=t}))),c=new Set(s.map((function(t){return t.aaz309})).filter((function(t){return null!=t}))),u=new Set(s.map((function(t){return t.aaz263})).filter((function(t){return null!=t}))),p=!1,d=!1,h=!1,f=!1,1===l.size&&(p=!0),1===o.size&&1===l.size&&(d=!0),1===o.size&&1===l.size&&1===c.size&&(h=!0),1===l.size&&0===o.size&&1===u.size&&(f=!0),t.abrupt("return",{akb020Set:l,aaz307Set:o,aaz263Set:u,aaz309Set:c,akb020Disable:p,aaz307Disable:d,aaz263Disable:f,aaz309Disable:h});case 20:case"end":return t.stop()}}),t)}))),s.apply(this,arguments)}function l(t,e){Base.submit(null,{url:"miimCommonRead/getAa01AAE500StartStop",data:t},{successCallback:function(t){return e(t)}})}function o(t,e){Base.submit(null,{url:"miimCommonRead/insertTableColumShow",data:t,autoValid:!1},{successCallback:function(t){return e("success")},failCallback:function(t){return e("fail")}})}var c={zbx01:{footer:function(){return"统筹金总额"}},zbx02:{footer:function(){return"基金支出总额"}},zbx03:{footer:function(){return"总费用"}},zbx04:{footer:function(){return"总住院日"}}};e["Z"]={permissionCheck:r,getAa01AAE500StartStop:l,insertTableColumShow:o,props:c,moneyNumFormat:function(t){var e=t.cellValue;return e||"—"}}}}]);
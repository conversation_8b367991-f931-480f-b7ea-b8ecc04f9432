"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[2286],{88412:function(t,e,a){var l=a(26263),o=a(36766),i=a(1001),n=(0,i.Z)(o.Z,l.s,l.x,!1,null,"5e7ef0ae",null);e["Z"]=n.exports},92286:function(t,e,a){a.r(e),a.d(e,{default:function(){return u}});var l=function(){var t=this,e=this,a=e.$createElement,l=e._self._c||a;return l("div",{staticClass:"fit"},[l("ta-border-layout",{attrs:{layout:{footer:"70px",left:"30%"},"left-cfg":{expand:!0,expandText:"",showBar:!0}}},[l("div",{attrs:{slot:"leftExtraContent"},slot:"leftExtraContent"},[l("ta-form",{staticStyle:{"margin-top":"10px"},attrs:{autoFormCreate:function(e){t.deptForm=e},layout:"horizontal"}},[l("ta-row",[l("ta-col",{attrs:{span:12}},[l("ta-form-item",{attrs:{fieldDecoratorId:"admDeptCode",span:12}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("科室")]),l("ta-select",{attrs:{options:e.options},on:{select:e.deptSelect}})],1)],1)],1)],1)],1),l("div",{staticClass:"fit",attrs:{slot:"left"},slot:"left"},[l("ta-title",[e._v("患者列表"),l("span",{staticStyle:{"font-weight":"normal","font-size":"14px"}},[e._v("（请先选择患者）")])]),l("div",{staticStyle:{height:"95%"}},[l("ta-table",{attrs:{columns:e.patientColumns,dataSource:e.patientData,customRow:e.fnDeptRow,bordered:!0,size:"small",haveSn:!0,scroll:{y:"100%",x:"100%"}},on:{"update:columns":function(t){e.patientColumns=t}}})],1)],1),l("div",{staticClass:"fit",staticStyle:{height:"100%"}},[l("ta-title",[e._v("费用明细")]),l("ta-table",{attrs:{columns:e.chargeColumns,dataSource:e.chargeData,haveSn:!0,customRow:e.fnCustomRow,bordered:!0,size:"small",scroll:{y:"100%",x:"100%"}},on:{"update:columns":function(t){e.chargeColumns=t}}})],1),l("div",{attrs:{slot:"footer"},slot:"footer"},[l("ta-button",{staticStyle:{float:"left","margin-left":"10%",width:"200px"},attrs:{type:"primary"},on:{click:function(t){return e.doCheckWithUrl(4)}}},[e._v("医生出院审核")]),l("ta-button",{staticStyle:{float:"left","margin-left":"100px",width:"200px"},attrs:{type:"primary"},on:{click:function(t){return e.doCheckWithUrl(7)}}},[e._v("护士出院审核")]),l("ta-button",{staticStyle:{float:"left","margin-left":"100px",width:"200px"},attrs:{type:"primary"},on:{click:function(t){return e.doCheckWithUrl(5)}}},[e._v("医保办出院审核")])],1)]),l("ta-modal",{attrs:{title:"出院审核结果",footer:null,height:"750px",width:"1410px",destroyOnClose:!0},on:{cancel:e.handleCancel},model:{value:e.cyshModal,callback:function(t){e.cyshModal=t},expression:"cyshModal"}},[l("iframe",{staticStyle:{height:"100%",width:"100%"},attrs:{src:e.url,id:"iframe"}})])],1)},o=[],i=a(88412),n={name:"doctorOrder",components:{TaTitle:i.Z},data:function(){var t=[{title:"住院号",dataIndex:"iptNo",align:"center",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"住院天数",dataIndex:"iptDay",align:"right",width:80,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"患者姓名",dataIndex:"psnName",align:"left",width:80,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"医保",dataIndex:"hiFeesetlType",align:"center",width:60,overflowTooltip:!0,collectionType:"AAE141",customHeaderCell:this.fnCustomHeaderCell},{title:"性别",dataIndex:"gend",align:"center",width:50,overflowTooltip:!0,collectionType:"SEX",customHeaderCell:this.fnCustomHeaderCell},{title:"年龄",dataIndex:"age",align:"right",width:50,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell}],e=[{title:"费用明细id",dataIndex:"feedetlSn",align:"center",width:80,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell,scopedSlots:{customRender:"action"}},{title:"医保编码",dataIndex:"hilistCode",align:"left",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell,scopedSlots:{customRender:"action"}},{title:"医保名称",dataIndex:"hilistName",align:"left",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell,scopedSlots:{customRender:"action"}},{title:"医保审核结果",dataIndex:"wgnr",align:"left",width:150,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"处理结果",dataIndex:"ape893",align:"center",width:60,overflowTooltip:!0,collectionType:"APE893",customHeaderCell:this.fnCustomHeaderCell},{title:"费用时间",dataIndex:"feeOcurTime",align:"center",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell,scopedSlots:{customRender:"action"}},{title:"数量",dataIndex:"cnt",align:"right",width:40,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell,scopedSlots:{customRender:"action"}},{title:"总额",dataIndex:"detItemFeeSumamt",align:"right",width:40,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell,scopedSlots:{customRender:"action"}}];return{patientColumns:t,patientData:[],chargeColumns:e,chargeData:[],options:[],patientInfo:null,iptNo:"",engOptions:[],show:"",cyshModal:!1,url:""}},mounted:function(){this.fnQueryDept(),window.addEventListener("message",this.handleMessagecy)},methods:{handleMessagecy:function(t){var e=t.data;"0"===e.infcode&&(this.cyshModal=!1,this.fnQueryRuleInfo(this.patientInfo.trigscen))},handleCancel:function(){this.cyshModal=!1},fnQueryEng:function(){var t=this;this.Base.submit(null,{url:"dischargeHis/queryEng",data:{trig_scen:5},autoValid:!1},{successCallback:function(e){t.engOptions=e.data.list},failCallback:function(e){t.$message.error("引擎数据加载失败")}})},fnCustomRow:function(t,e){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px"}}},fnCustomHeaderCell:function(t){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px",fontWeight:"bold"}}},fnQueryDept:function(){var t=this;this.Base.submit(null,{url:"doctorOrder/queryDept",autoValid:!1},{successCallback:function(e){t.options=e.data.list},failCallback:function(e){t.$message.error("科室数据加载失败")}})},deptSelect:function(t){var e=this;this.Base.submit(null,{url:"doctorOrder/queryPatient",data:{admDeptCode:t},autoValid:!1},{successCallback:function(t){e.patientData=t.data.list},failCallback:function(t){e.$message.error("患者数据加载失败")}})},fnDeptRow:function(t,e){var a=this;return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",backgroundColor:t.iptNo===this.iptNo?"#7ecdf0":"",lineHeight:"22px",cursor:"pointer"},on:{click:function(e){a.iptNo=t.iptNo,a.patientInfo=t,a.fnQuery()}}}},fnQuery:function(){var t=this;this.Base.submit(null,{url:"dischargeHis/queryDetail",data:{iptNo:this.iptNo},autoValid:!1},{successCallback:function(e){t.chargeData=e.data.list},failCallback:function(e){t.$message.error("费用明细加载失败")}})},fnQueryRuleInfo:function(t){var e=this;this.Base.submit(null,{url:"dischargeHis/queryDetailwithRuleInfo",data:{iptNo:this.iptNo+"-"+this.patientInfo.patnIptCnt,trig_scen:t,call_id:this.patientInfo.call_id},autoValid:!1},{successCallback:function(t){e.chargeData=t.data.list,e.$message.success("审核成功")},failCallback:function(t){e.$message.error("费用明细加载失败")}})},doCheck:function(t){var e=this,a=this.patientInfo;a.infno=990106,a.trig_scen=t,this.Base.submit(null,{url:"doctorOrder/doCheck",data:a,autoValid:!1},{successCallback:function(a){e.show=a.data.infcode,-1===e.show?e.$message.error(a.data.err_msg):e.fnQueryRuleInfo(t)},failCallback:function(t){e.$message.error("接口调用失败加载失败")}})},doCheckWithUrl:function(t){var e=this,a=this.patientInfo;null!==a?(this.patientInfo.trigscen=t,a.infno=990106,a.trig_scen=t,this.Base.submit(null,{url:"doctorOrder/doCheckWithUrl",data:a,autoValid:!1},{successCallback:function(t){if(e.show=t.data.infcode,-1===e.show)e.$message.error(t.data.err_msg);else{var a=t.data.result;e.patientInfo.call_id=a.call_id,e.url=a.url,void 0!==e.url&&""!==e.url&&(e.cyshModal=!0)}},failCallback:function(t){e.$message.error("接口调用失败加载失败")}})):this.$message.error("请选择患者！")}}},s=n,r=a(1001),c=(0,r.Z)(s,l,o,!1,null,"2060de6d",null),u=c.exports},36766:function(t,e,a){var l=a(66586);e["Z"]=l.Z},26263:function(t,e,a){a.d(e,{s:function(){return l},x:function(){return o}});var l=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},o=[]},66586:function(t,e){var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};e["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}}}]);
"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[7903],{87903:function(t,e,a){a.r(e),a.d(e,{default:function(){return c}});var r,i=function(){var t=this,e=this,a=e.$createElement,r=e._self._c||a;return r("div",{staticClass:"fit"},[r("ta-border-layout",{attrs:{layout:{footer:"60px"},"footer-cfg":{showBorder:!1}}},[r("ta-breadcrumb",{staticStyle:{"padding-left":"14px"}},[r("ta-breadcrumb-item",[r("a",{on:{click:e.backClick}},[e._v("报告列表")])]),r("ta-breadcrumb-item",[e._v(e._s("add"===e.pageFlag?"创建":"edit"===e.pageFlag?"编辑":"")+"报告【报告基本信息配置】")])],1),r("ta-card",{attrs:{title:"基本信息录入",bordered:!1}},[r("ta-form",{attrs:{layout:"horizontal","form-layout":!0,"auto-form-create":function(e){return t.form=e},"label-width":"100px"}},[r("ta-row",[r("ta-form-item",{attrs:{label:"报告名称",span:6,require:"","field-decorator-id":"bgmc"}},[r("ta-input",{attrs:{allowClear:!0}})],1)],1),r("ta-row",[r("ta-form-item",{attrs:{label:"报告标题",span:12,require:"","field-decorator-id":"bgbt"}},[r("ta-input",{attrs:{allowClear:!0},model:{value:e.bgbt_value,callback:function(t){e.bgbt_value=t},expression:"bgbt_value"}})],1),r("ta-form-item",[r("div",{staticStyle:{"margin-left":"16px"}},[e._v(" 参数列表： "),r("ta-dropdown",{directives:[{name:"show",rawName:"v-show",value:e.ifShow,expression:"ifShow"}],attrs:{trigger:["click"]}},[r("a",{attrs:{href:"javascript:;"}},[e._v("请选择"),r("ta-icon",{staticStyle:{"margin-left":"8px"},attrs:{type:"down"}})],1),r("ta-menu",{attrs:{slot:"overlay"},on:{click:e.selectCs},slot:"overlay"},e._l(e.csList,(function(t){return r("ta-sub-menu",{key:t.kpiCodg,attrs:{title:t.kpiCodg}},e._l(t.fldList,(function(t){return r("ta-menu-item",{key:t.fld},[e._v(e._s(t.fld))])})),1)})),1)],1)],1)])],1),r("ta-row",[r("ta-form-item",{attrs:{label:"报告描述",span:12,require:"","field-decorator-id":"bgms"}},[r("ta-input",{attrs:{allowClear:!0}})],1)],1)],1)],1),r("ta-card",{attrs:{title:"查询条件设置",bordered:!1}},[r("ta-big-table",{ref:"table_cxtjsz",attrs:{border:"","keep-source":"",stripe:"","auto-resize":"","highlight-hover-row":"","checkbox-config":{showHeader:!1},height:"260",align:"center",data:e.tableData},on:{"checkbox-change":e.checkBoxChange}},[r("ta-big-table-column",{attrs:{type:"checkbox",width:"50"}}),r("ta-big-table-column",{attrs:{field:"fld",title:"查询条件",width:"","show-overflow":""}}),r("ta-big-table-column",{attrs:{field:"fldName",title:"条件描述",width:"","header-align":"center",align:"left","show-overflow":""}})],1)],1),r("div",{attrs:{slot:"footer"},slot:"footer"},[r("div",{staticStyle:{display:"flex","justify-content":"center"}},["show"!==e.pageFlag?r("ta-button",{staticStyle:{margin:"0 16px"},attrs:{type:"primary"},on:{click:e.nextClick}},[e._v("下一步")]):e._e(),r("ta-button",{on:{click:e.cancelClick}},[e._v("取消")])],1)])],1)],1)},o=[],s=a(82482),l=(r={name:"otherPage1",data:function(){return{pageFlag:"",ifClear:"",rptCodg:"",rptoldCodg:"",tableData:[],quryCond:[],bgbt_value:"",ifChoose:!1,ifShow:!1,csList:[],zbList:[],cityProper:[],zbResult:[]}},watch:{bgbt_value:{handler:function(){var t=this;this.$nextTick((function(){t.bgbt_value.endsWith("${")?t.getCs():t.ifShow=!1}))}}},created:function(){},mounted:function(){var t=this;this.pageFlag=this.$route.params.pageFlag,this.ifClear=this.$route.params.ifClear,this.rptCodg=this.$route.params.rptCodg,"add"===this.pageFlag&&"true"===this.ifClear&&(this.queryCondition(),this.$nextTick((function(){t.rptCodg="",t.form.resetFields(),t.$refs.table_cxtjsz.clearCheckboxRow()}))),"edit"===this.pageFlag&&this.queryRptDetailInfo(),"copy"===this.pageFlag&&(this.queryRptDetailInfo(),this.rptoldCodg=this.rptCodg,this.rptCodg="")}},(0,s.Z)(r,"mounted",(function(){var t=this;this.pageFlag=this.$route.params.pageFlag,this.ifClear=this.$route.params.ifClear,this.rptCodg=this.$route.params.rptCodg,"add"===this.pageFlag&&"true"===this.ifClear&&(this.queryCondition(),this.$nextTick((function(){t.rptCodg="",t.form.resetFields(),t.$refs.table_cxtjsz.clearCheckboxRow()}))),"edit"===this.pageFlag&&this.queryRptDetailInfo(),"copy"===this.pageFlag&&(this.queryRptDetailInfo(),this.rptoldCodg=this.rptCodg,this.rptCodg="")})),(0,s.Z)(r,"methods",{queryAllAdmdvs:function(){var t=this;this.cityProper=[],Base.submit(null,{url:"/common/admdvs/queryAllAdmdvs",data:null,showPageLoading:!1}).then((function(e){var a=e.data.list||[];a.forEach((function(e){t.cityProper.push({label:e.admdvsName,value:e.admdvs})}))}))},queryRptDetailInfo:function(){var t=this;this.quryCond=[],Base.submit(null,{url:"/smtrpt/rptInfo/queryRptDetailInfo",data:{rptCodg:this.rptCodg}}).then((function(e){var a=e.data.rptInfo||{};t.$nextTick((function(){t.form.setFieldsValue({bgmc:a.rptName,bgbt:a.rptTtl,bgms:a.rptDesc}),t.tableData=a.quryCond,t.tableData.forEach((function(e){"1"===e.chooseFlag&&(t.$refs.table_cxtjsz.setCheckboxRow(e,!0),t.quryCond.push(e))}))}))}))},queryCondition:function(){var t=this;Base.submit(null,{url:"/smtrpt/rptInfo/queryCondition",data:null}).then((function(e){t.tableData=e.data.quryCond||[]}))},backClick:function(){this.$router.push({name:"bglb"})},checkBoxChange:function(t){t.row,t.checked;var e=t.records;this.quryCond=e},getCs:function(){var t=this;this.csList=[],Base.submit(null,{url:"/smtrpt/kpiInfo/queryAllKpi",data:null}).then((function(e){t.csList=e.data.allKpi||[],t.ifShow=!0}))},selectCs:function(t){var e=this.form.getFieldsValue();this.form.setFieldsValue({bgbt:e.bgbt+t.keyPath[1]+"."+t.keyPath[0]+"}"}),this.ifShow=!1},nextClick:function(){var t=this,e=this.form.getFieldsValue(),a={};this.quryCond=this.quryCond.map((function(t){return delete t._XID,t})),a={rptCodg:this.rptCodg?this.rptCodg:null,rptName:e.bgmc,rptDesc:e.bgms,rptTtl:e.bgbt,quryCond:this.quryCond||[]},Base.submit(this.form,{url:"/smtrpt/rptInfo/saveRptInfo",data:a,autoQs:!1,autoValid:!0}).then((function(e){var r=e.data.rptCodg;Base.submit(null,{url:"/smtrpt/rptInfo/queryRptDetailInfo",data:{rptCodg:r}}).then((function(e){var i=e.data.rptInfo||{};t.$nextTick((function(){a.quryCond=i.quryCond,t.$router.push({name:"otherPage2",params:{pageFlag:t.pageFlag,rptCodg:r,rptoldCodg:t.rptoldCodg,smtrptInfo:a}})}))}))}))},cancelClick:function(){this.$router.push({name:"bglb"})}}),r),n=l,u=a(1001),d=(0,u.Z)(n,i,o,!1,null,"524749d0",null),c=d.exports}}]);
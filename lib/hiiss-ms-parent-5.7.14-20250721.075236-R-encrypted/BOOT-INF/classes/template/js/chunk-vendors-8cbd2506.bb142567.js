(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[602],{70481:function(A,e,t){"use strict";t.d(e,{Z:function(){return I}});t(32564);
/*!
 * @wcjiang/notify v2.1.0
 * JS achieve the browser title flashing , scrolling, voice prompts , chrome notice.
 * 
 * Copyright (c) 2022 kenny wang
 * http://jaywcjlove.github.io/iNotify
 * 
 * Licensed under the MIT license.
 */
window.Notification&&"granted"!==window.Notification.permission&&window.Notification.requestPermission();var i="",n=["flash","scroll"],o={title:"iNotify !",body:"You have a new message.",openurl:""};function r(A,e){for(var t in e)A[t]&&(e[t]=A[t]);return e}function a(A){return"[object Array]"===Object.prototype.toString.call(A)}function s(A){var e,t=document.createElement("audio");if(t.autoplay=!0,t.muted=!0,a(A)&&A.length>0)for(var i=0;i<A.length;i++)e=document.createElement("source"),e.src=A[i],e.type="audio/".concat(g(A[i])),t.appendChild(e);else t.src=A;return t}function l(A){var e=document.querySelectorAll("link[rel~=shortcut]")[0];return e||(e=c("O",A)),e}function g(A){return A.match(/\.([^\\.]+)$/)[1]}function c(A,e){var t=document.createElement("canvas"),n=document.getElementsByTagName("head")[0],o=document.createElement("link"),r=null;return t.height=32,t.width=32,r=t.getContext("2d"),r.fillStyle=e.backgroundColor,r.fillRect(0,0,32,32),r.textAlign="center",r.font='22px "helvetica", sans-serif',r.fillStyle=e.textColor,A&&r.fillText(A,16,24),o.setAttribute("rel","shortcut icon"),o.setAttribute("type","image/x-icon"),o.setAttribute("id","new".concat(e.id)),o.setAttribute("href",t.toDataURL("image/png")),i=t.toDataURL("image/png"),n.appendChild(o)}function I(A){A&&this.init(A)}I.prototype={init:function(A){return A||(A={}),this.interval=A.interval||100,this.effect=A.effect||"flash",this.title=A.title||document.title,this.message=A.message||this.title,this.onclick=A.onclick||this.onclick,this.openurl=A.openurl||this.openurl,this.disableFavicon=A.disableFavicon||!1,this.updateFavicon=!this.disableFavicon&&(A.updateFavicon||{id:"favicon",textColor:"#fff",backgroundColor:"#2F9A00"}),this.audio=A.audio||"",this.favicon=!this.disableFavicon&&l(this.updateFavicon),this.cloneFavicon=this.favicon&&this.favicon.cloneNode(!0),i=A.notification&&A.notification.icon?A.notification.icon:A.icon?A.icon:this.favicon.href,o.icon=i,this.notification=A.notification||o,this.audio&&this.audio.file&&this.setURL(this.audio.file),this},render:function(){if("flash"===this.effect)document.title=this.title===document.title?this.message:this.title;else if("scroll"===this.effect){var A=this.message||document.title;this.scrollTitle&&this.scrollTitle.slice(1)?(this.scrollTitle=this.scrollTitle.slice(1),document.title=this.scrollTitle):(document.title=A,this.scrollTitle=A)}return this},setTitle:function(A){if(!0===A){if(n.indexOf(this.effect)>=0)return this.addTimer()}else A?(this.message=A,this.scrollTitle="",this.addTimer()):this.clearTimer();return this},setURL:function(A){return A&&(this.audioElm&&this.audioElm.remove(),this.audioElm=s(A),document.body.appendChild(this.audioElm)),this},loopPlay:function(){return this.setURL(),this.audioElm.loop=!0,this.player(),this},stopPlay:function(){return this.audioElm&&(this.audioElm.loop=!1,this.audioElm.pause()),this},player:function(){if(this.audio&&this.audio.file){this.audioElm||(this.audioElm=s(this.audio.file),document.body.appendChild(this.audioElm)),this.audioElm.muted=!1;var A=this.audioElm.play();return void 0!==A&&A.then((function(){})).catch((function(){})),this}},notify:function(A){var e=this.notification,t=A.openurl?A.openurl:this.openurl,n=A.onclick?A.onclick:this.onclick;if(window.Notification){e=A?r(A,e):o;var a={};a.icon=A.icon?A.icon:i,a.body=e.body||A.body,A.dir&&(a.dir=A.dir);var s=new Notification(e.title||A.title,a);s.onclick=function(){n&&"function"===typeof n&&n(s),t&&window.open(t)},s.onshow=function(){A.onshow&&"function"===typeof A.onshow&&A.onshow(s)},s.onclose=function(){A.onclose&&"function"===typeof A.onclose&&A.onclose(s)},s.onerror=function(){A.onerror&&"function"===typeof A.onerror&&A.onerror(s)},this.Notifiy=s}return this},isPermission:function(){return window.Notification&&"granted"===Notification.permission},setInterval:function(A){return A&&(this.interval=A,this.addTimer()),this},setFavicon:function(A){if(!A&&0!==A)return this.faviconClear();var e=document.getElementById("new".concat(this.updateFavicon.id));return this.favicon&&this.favicon.remove(),e&&e.remove(),this.updateFavicon.num=A,c(A,this.updateFavicon),this},setFaviconColor:function(A){return A&&(this.faviconRemove(),this.updateFavicon.textColor=A,c(this.updateFavicon.num,this.updateFavicon)),this},setFaviconBackgroundColor:function(A){return A&&(this.faviconRemove(),this.updateFavicon.backgroundColor=A,c(this.updateFavicon.num,this.updateFavicon)),this},faviconRemove:function(){this.faviconClear();var A=document.getElementById("new".concat(this.updateFavicon.id));this.favicon&&this.favicon.remove(),A&&A.remove()},addTimer:function(){return this.clearTimer(),n.indexOf(this.effect)>=0&&(this.timer=setInterval(this.render.bind(this),this.interval)),this},close:function(){this.Notifiy&&this.Notifiy.close()},faviconClear:function(){var A=document.getElementById("new".concat(this.updateFavicon.id)),e=document.getElementsByTagName("head")[0],t=document.querySelectorAll("link[rel~=shortcut]");if(A&&A.remove(),t.length>0)for(var n=0;n<t.length;n++)t[n].remove();return e.appendChild(this.cloneFavicon),i=this.cloneFavicon.href,this.favicon=this.cloneFavicon,this},clearTimer:function(){return this.timer&&clearInterval(this.timer),document.title=this.title,this}}},54212:function(A,e,t){"use strict";t.d(e,{cV:function(){return i}});var i=function(A){if(A.fnOptions)return A.fnOptions;var e=A.componentOptions;return A.$vnode&&(e=A.$vnode.componentOptions),e&&e.Ctor.options||{}}},68662:function(A,e,t){"use strict";t.d(e,{Kp:function(){return i}});function i(A,e){0}},36790:function(A,e,t){t(32564);for(var i=t(50202),n="undefined"===typeof window?t.g:window,o=["moz","webkit"],r="AnimationFrame",a=n["request"+r],s=n["cancel"+r]||n["cancelRequest"+r],l=0;!a&&l<o.length;l++)a=n[o[l]+"Request"+r],s=n[o[l]+"Cancel"+r]||n[o[l]+"CancelRequest"+r];if(!a||!s){var g=0,c=0,I=[],Q=1e3/60;a=function(A){if(0===I.length){var e=i(),t=Math.max(0,Q-(e-g));g=t+e,setTimeout((function(){var A=I.slice(0);I.length=0;for(var e=0;e<A.length;e++)if(!A[e].cancelled)try{A[e].callback(g)}catch(t){setTimeout((function(){throw t}),0)}}),Math.round(t))}return I.push({handle:++c,callback:A,cancelled:!1}),c},s=function(A){for(var e=0;e<I.length;e++)I[e].handle===A&&(I[e].cancelled=!0)}}A.exports=function(A){return a.call(n,A)},A.exports.cancel=function(){s.apply(n,arguments)},A.exports.polyfill=function(A){A||(A=n),A.requestAnimationFrame=a,A.cancelAnimationFrame=s}},36133:function(A,e,t){A=t.nmd(A);var i=t(57847)["default"];t(68304),t(65743);var n=function(A){"use strict";var e,t=Object.prototype,n=t.hasOwnProperty,o="function"===typeof Symbol?Symbol:{},r=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",s=o.toStringTag||"@@toStringTag";function l(A,e,t){return Object.defineProperty(A,e,{value:t,enumerable:!0,configurable:!0,writable:!0}),A[e]}try{l({},"")}catch(x){l=function(A,e,t){return A[e]=t}}function g(A,e,t,i){var n=e&&e.prototype instanceof d?e:d,o=Object.create(n.prototype),r=new M(i||[]);return o._invoke=w(A,t,r),o}function c(A,e,t){try{return{type:"normal",arg:A.call(e,t)}}catch(x){return{type:"throw",arg:x}}}A.wrap=g;var I="suspendedStart",Q="suspendedYield",u="executing",C="completed",h={};function d(){}function B(){}function E(){}var p={};l(p,r,(function(){return this}));var m=Object.getPrototypeOf,f=m&&m(m(k([])));f&&f!==t&&n.call(f,r)&&(p=f);var y=E.prototype=d.prototype=Object.create(p);function b(A){["next","throw","return"].forEach((function(e){l(A,e,(function(A){return this._invoke(e,A)}))}))}function v(A,e){function t(o,r,a,s){var l=c(A[o],A,r);if("throw"!==l.type){var g=l.arg,I=g.value;return I&&"object"===i(I)&&n.call(I,"__await")?e.resolve(I.__await).then((function(A){t("next",A,a,s)}),(function(A){t("throw",A,a,s)})):e.resolve(I).then((function(A){g.value=A,a(g)}),(function(A){return t("throw",A,a,s)}))}s(l.arg)}var o;function r(A,i){function n(){return new e((function(e,n){t(A,i,e,n)}))}return o=o?o.then(n,n):n()}this._invoke=r}function w(A,e,t){var i=I;return function(n,o){if(i===u)throw new Error("Generator is already running");if(i===C){if("throw"===n)throw o;return O()}t.method=n,t.arg=o;while(1){var r=t.delegate;if(r){var a=N(r,t);if(a){if(a===h)continue;return a}}if("next"===t.method)t.sent=t._sent=t.arg;else if("throw"===t.method){if(i===I)throw i=C,t.arg;t.dispatchException(t.arg)}else"return"===t.method&&t.abrupt("return",t.arg);i=u;var s=c(A,e,t);if("normal"===s.type){if(i=t.done?C:Q,s.arg===h)continue;return{value:s.arg,done:t.done}}"throw"===s.type&&(i=C,t.method="throw",t.arg=s.arg)}}}function N(A,t){var i=A.iterator[t.method];if(i===e){if(t.delegate=null,"throw"===t.method){if(A.iterator["return"]&&(t.method="return",t.arg=e,N(A,t),"throw"===t.method))return h;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return h}var n=c(i,A.iterator,t.arg);if("throw"===n.type)return t.method="throw",t.arg=n.arg,t.delegate=null,h;var o=n.arg;return o?o.done?(t[A.resultName]=o.value,t.next=A.nextLoc,"return"!==t.method&&(t.method="next",t.arg=e),t.delegate=null,h):o:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,h)}function D(A){var e={tryLoc:A[0]};1 in A&&(e.catchLoc=A[1]),2 in A&&(e.finallyLoc=A[2],e.afterLoc=A[3]),this.tryEntries.push(e)}function Y(A){var e=A.completion||{};e.type="normal",delete e.arg,A.completion=e}function M(A){this.tryEntries=[{tryLoc:"root"}],A.forEach(D,this),this.reset(!0)}function k(A){if(A){var t=A[r];if(t)return t.call(A);if("function"===typeof A.next)return A;if(!isNaN(A.length)){var i=-1,o=function t(){while(++i<A.length)if(n.call(A,i))return t.value=A[i],t.done=!1,t;return t.value=e,t.done=!0,t};return o.next=o}}return{next:O}}function O(){return{value:e,done:!0}}return B.prototype=E,l(y,"constructor",E),l(E,"constructor",B),B.displayName=l(E,s,"GeneratorFunction"),A.isGeneratorFunction=function(A){var e="function"===typeof A&&A.constructor;return!!e&&(e===B||"GeneratorFunction"===(e.displayName||e.name))},A.mark=function(A){return Object.setPrototypeOf?Object.setPrototypeOf(A,E):(A.__proto__=E,l(A,s,"GeneratorFunction")),A.prototype=Object.create(y),A},A.awrap=function(A){return{__await:A}},b(v.prototype),l(v.prototype,a,(function(){return this})),A.AsyncIterator=v,A.async=function(e,t,i,n,o){void 0===o&&(o=Promise);var r=new v(g(e,t,i,n),o);return A.isGeneratorFunction(t)?r:r.next().then((function(A){return A.done?A.value:r.next()}))},b(y),l(y,s,"Generator"),l(y,r,(function(){return this})),l(y,"toString",(function(){return"[object Generator]"})),A.keys=function(A){var e=[];for(var t in A)e.push(t);return e.reverse(),function t(){while(e.length){var i=e.pop();if(i in A)return t.value=i,t.done=!1,t}return t.done=!0,t}},A.values=k,M.prototype={constructor:M,reset:function(A){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(Y),!A)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=e)},stop:function(){this.done=!0;var A=this.tryEntries[0],e=A.completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(A){if(this.done)throw A;var t=this;function i(i,n){return a.type="throw",a.arg=A,t.next=i,n&&(t.method="next",t.arg=e),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var r=this.tryEntries[o],a=r.completion;if("root"===r.tryLoc)return i("end");if(r.tryLoc<=this.prev){var s=n.call(r,"catchLoc"),l=n.call(r,"finallyLoc");if(s&&l){if(this.prev<r.catchLoc)return i(r.catchLoc,!0);if(this.prev<r.finallyLoc)return i(r.finallyLoc)}else if(s){if(this.prev<r.catchLoc)return i(r.catchLoc,!0)}else{if(!l)throw new Error("try statement without catch or finally");if(this.prev<r.finallyLoc)return i(r.finallyLoc)}}}},abrupt:function(A,e){for(var t=this.tryEntries.length-1;t>=0;--t){var i=this.tryEntries[t];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===A||"continue"===A)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var r=o?o.completion:{};return r.type=A,r.arg=e,o?(this.method="next",this.next=o.finallyLoc,h):this.complete(r)},complete:function(A,e){if("throw"===A.type)throw A.arg;return"break"===A.type||"continue"===A.type?this.next=A.arg:"return"===A.type?(this.rval=this.arg=A.arg,this.method="return",this.next="end"):"normal"===A.type&&e&&(this.next=e),h},finish:function(A){for(var e=this.tryEntries.length-1;e>=0;--e){var t=this.tryEntries[e];if(t.finallyLoc===A)return this.complete(t.completion,t.afterLoc),Y(t),h}},catch:function(A){for(var e=this.tryEntries.length-1;e>=0;--e){var t=this.tryEntries[e];if(t.tryLoc===A){var i=t.completion;if("throw"===i.type){var n=i.arg;Y(t)}return n}}throw new Error("illegal catch attempt")},delegateYield:function(A,t,i){return this.delegate={iterator:k(A),resultName:t,nextLoc:i},"next"===this.method&&(this.arg=e),h}},A}("object"===i(A)?A.exports:{});try{regeneratorRuntime=n}catch(o){"object"===("undefined"===typeof globalThis?"undefined":i(globalThis))?globalThis.regeneratorRuntime=n:Function("r","regeneratorRuntime = r")(n)}},70566:function(A,e,t){"use strict";t.r(e);t(32564);var i=function(){if("undefined"!==typeof Map)return Map;function A(A,e){var t=-1;return A.some((function(A,i){return A[0]===e&&(t=i,!0)})),t}return function(){function e(){this.__entries__=[]}return Object.defineProperty(e.prototype,"size",{get:function(){return this.__entries__.length},enumerable:!0,configurable:!0}),e.prototype.get=function(e){var t=A(this.__entries__,e),i=this.__entries__[t];return i&&i[1]},e.prototype.set=function(e,t){var i=A(this.__entries__,e);~i?this.__entries__[i][1]=t:this.__entries__.push([e,t])},e.prototype.delete=function(e){var t=this.__entries__,i=A(t,e);~i&&t.splice(i,1)},e.prototype.has=function(e){return!!~A(this.__entries__,e)},e.prototype.clear=function(){this.__entries__.splice(0)},e.prototype.forEach=function(A,e){void 0===e&&(e=null);for(var t=0,i=this.__entries__;t<i.length;t++){var n=i[t];A.call(e,n[1],n[0])}},e}()}(),n="undefined"!==typeof window&&"undefined"!==typeof document&&window.document===document,o=function(){return"undefined"!==typeof t.g&&t.g.Math===Math?t.g:"undefined"!==typeof self&&self.Math===Math?self:"undefined"!==typeof window&&window.Math===Math?window:Function("return this")()}(),r=function(){return"function"===typeof requestAnimationFrame?requestAnimationFrame.bind(o):function(A){return setTimeout((function(){return A(Date.now())}),1e3/60)}}(),a=2;function s(A,e){var t=!1,i=!1,n=0;function o(){t&&(t=!1,A()),i&&l()}function s(){r(o)}function l(){var A=Date.now();if(t){if(A-n<a)return;i=!0}else t=!0,i=!1,setTimeout(s,e);n=A}return l}var l=20,g=["top","right","bottom","left","width","height","size","weight"],c="undefined"!==typeof MutationObserver,I=function(){function A(){this.connected_=!1,this.mutationEventsAdded_=!1,this.mutationsObserver_=null,this.observers_=[],this.onTransitionEnd_=this.onTransitionEnd_.bind(this),this.refresh=s(this.refresh.bind(this),l)}return A.prototype.addObserver=function(A){~this.observers_.indexOf(A)||this.observers_.push(A),this.connected_||this.connect_()},A.prototype.removeObserver=function(A){var e=this.observers_,t=e.indexOf(A);~t&&e.splice(t,1),!e.length&&this.connected_&&this.disconnect_()},A.prototype.refresh=function(){var A=this.updateObservers_();A&&this.refresh()},A.prototype.updateObservers_=function(){var A=this.observers_.filter((function(A){return A.gatherActive(),A.hasActive()}));return A.forEach((function(A){return A.broadcastActive()})),A.length>0},A.prototype.connect_=function(){n&&!this.connected_&&(document.addEventListener("transitionend",this.onTransitionEnd_),window.addEventListener("resize",this.refresh),c?(this.mutationsObserver_=new MutationObserver(this.refresh),this.mutationsObserver_.observe(document,{attributes:!0,childList:!0,characterData:!0,subtree:!0})):(document.addEventListener("DOMSubtreeModified",this.refresh),this.mutationEventsAdded_=!0),this.connected_=!0)},A.prototype.disconnect_=function(){n&&this.connected_&&(document.removeEventListener("transitionend",this.onTransitionEnd_),window.removeEventListener("resize",this.refresh),this.mutationsObserver_&&this.mutationsObserver_.disconnect(),this.mutationEventsAdded_&&document.removeEventListener("DOMSubtreeModified",this.refresh),this.mutationsObserver_=null,this.mutationEventsAdded_=!1,this.connected_=!1)},A.prototype.onTransitionEnd_=function(A){var e=A.propertyName,t=void 0===e?"":e,i=g.some((function(A){return!!~t.indexOf(A)}));i&&this.refresh()},A.getInstance=function(){return this.instance_||(this.instance_=new A),this.instance_},A.instance_=null,A}(),Q=function(A,e){for(var t=0,i=Object.keys(e);t<i.length;t++){var n=i[t];Object.defineProperty(A,n,{value:e[n],enumerable:!1,writable:!1,configurable:!0})}return A},u=function(A){var e=A&&A.ownerDocument&&A.ownerDocument.defaultView;return e||o},C=v(0,0,0,0);function h(A){return parseFloat(A)||0}function d(A){for(var e=[],t=1;t<arguments.length;t++)e[t-1]=arguments[t];return e.reduce((function(e,t){var i=A["border-"+t+"-width"];return e+h(i)}),0)}function B(A){for(var e=["top","right","bottom","left"],t={},i=0,n=e;i<n.length;i++){var o=n[i],r=A["padding-"+o];t[o]=h(r)}return t}function E(A){var e=A.getBBox();return v(0,0,e.width,e.height)}function p(A){var e=A.clientWidth,t=A.clientHeight;if(!e&&!t)return C;var i=u(A).getComputedStyle(A),n=B(i),o=n.left+n.right,r=n.top+n.bottom,a=h(i.width),s=h(i.height);if("border-box"===i.boxSizing&&(Math.round(a+o)!==e&&(a-=d(i,"left","right")+o),Math.round(s+r)!==t&&(s-=d(i,"top","bottom")+r)),!f(A)){var l=Math.round(a+o)-e,g=Math.round(s+r)-t;1!==Math.abs(l)&&(a-=l),1!==Math.abs(g)&&(s-=g)}return v(n.left,n.top,a,s)}var m=function(){return"undefined"!==typeof SVGGraphicsElement?function(A){return A instanceof u(A).SVGGraphicsElement}:function(A){return A instanceof u(A).SVGElement&&"function"===typeof A.getBBox}}();function f(A){return A===u(A).document.documentElement}function y(A){return n?m(A)?E(A):p(A):C}function b(A){var e=A.x,t=A.y,i=A.width,n=A.height,o="undefined"!==typeof DOMRectReadOnly?DOMRectReadOnly:Object,r=Object.create(o.prototype);return Q(r,{x:e,y:t,width:i,height:n,top:t,right:e+i,bottom:n+t,left:e}),r}function v(A,e,t,i){return{x:A,y:e,width:t,height:i}}var w=function(){function A(A){this.broadcastWidth=0,this.broadcastHeight=0,this.contentRect_=v(0,0,0,0),this.target=A}return A.prototype.isActive=function(){var A=y(this.target);return this.contentRect_=A,A.width!==this.broadcastWidth||A.height!==this.broadcastHeight},A.prototype.broadcastRect=function(){var A=this.contentRect_;return this.broadcastWidth=A.width,this.broadcastHeight=A.height,A},A}(),N=function(){function A(A,e){var t=b(e);Q(this,{target:A,contentRect:t})}return A}(),D=function(){function A(A,e,t){if(this.activeObservations_=[],this.observations_=new i,"function"!==typeof A)throw new TypeError("The callback provided as parameter 1 is not a function.");this.callback_=A,this.controller_=e,this.callbackCtx_=t}return A.prototype.observe=function(A){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if("undefined"!==typeof Element&&Element instanceof Object){if(!(A instanceof u(A).Element))throw new TypeError('parameter 1 is not of type "Element".');var e=this.observations_;e.has(A)||(e.set(A,new w(A)),this.controller_.addObserver(this),this.controller_.refresh())}},A.prototype.unobserve=function(A){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if("undefined"!==typeof Element&&Element instanceof Object){if(!(A instanceof u(A).Element))throw new TypeError('parameter 1 is not of type "Element".');var e=this.observations_;e.has(A)&&(e.delete(A),e.size||this.controller_.removeObserver(this))}},A.prototype.disconnect=function(){this.clearActive(),this.observations_.clear(),this.controller_.removeObserver(this)},A.prototype.gatherActive=function(){var A=this;this.clearActive(),this.observations_.forEach((function(e){e.isActive()&&A.activeObservations_.push(e)}))},A.prototype.broadcastActive=function(){if(this.hasActive()){var A=this.callbackCtx_,e=this.activeObservations_.map((function(A){return new N(A.target,A.broadcastRect())}));this.callback_.call(A,e,A),this.clearActive()}},A.prototype.clearActive=function(){this.activeObservations_.splice(0)},A.prototype.hasActive=function(){return this.activeObservations_.length>0},A}(),Y="undefined"!==typeof WeakMap?new WeakMap:new i,M=function(){function A(e){if(!(this instanceof A))throw new TypeError("Cannot call a class as a function.");if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");var t=I.getInstance(),i=new D(e,t,this);Y.set(this,i)}return A}();["observe","unobserve","disconnect"].forEach((function(A){M.prototype[A]=function(){var e;return(e=Y.get(this))[A].apply(e,arguments)}}));var k=function(){return"undefined"!==typeof o.ResizeObserver?o.ResizeObserver:M}();e["default"]=k},14473:function(A,e,t){var i,n,o;t(57847)["default"];(function(t,r){n=[],i=r,o="function"===typeof i?i.apply(e,n):i,void 0===o||(A.exports=o)})(0,(function(){var A=/(auto|scroll)/,e=function A(e,t){return null===e.parentNode?t:A(e.parentNode,t.concat([e]))},t=function(A,e){return getComputedStyle(A,null).getPropertyValue(e)},i=function(A){return t(A,"overflow")+t(A,"overflow-y")+t(A,"overflow-x")},n=function(e){return A.test(i(e))},o=function(A){if(A instanceof HTMLElement||A instanceof SVGElement){for(var t=e(A.parentNode,[]),i=0;i<t.length;i+=1)if(n(t[i]))return t[i];return document.scrollingElement||document.documentElement}};return o}))},33879:function(A){"use strict";function e(A,e){if(A===e)return!0;if(!A||!e)return!1;var t=A.length;if(e.length!==t)return!1;for(var i=0;i<t;i++)if(A[i]!==e[i])return!1;return!0}A.exports=e},49674:function(A,e,t){var i=t(57847)["default"];A.exports=function(A,e,t,n){var o=t?t.call(n,A,e):void 0;if(void 0!==o)return!!o;if(A===e)return!0;if("object"!==i(A)||!A||"object"!==i(e)||!e)return!1;var r=Object.keys(A),a=Object.keys(e);if(r.length!==a.length)return!1;for(var s=Object.prototype.hasOwnProperty.bind(e),l=0;l<r.length;l++){var g=r[l];if(!s(g))return!1;var c=A[g],I=e[g];if(o=t?t.call(n,c,I,g):void 0,!1===o||void 0===o&&c!==I)return!1}return!0}},3413:function(A,e,t){"use strict";t.r(e),t.d(e,{MultiDrag:function(){return Se},Sortable:function(){return $A},Swap:function(){return Ne}});var i=t(3336);t(32564);
/**!
 * Sortable 1.10.2
 * <AUTHOR>   <<EMAIL>>
 * <AUTHOR>    <<EMAIL>>
 * @license MIT
 */
function n(A){return n="function"===typeof Symbol&&"symbol"===(0,i.Z)(Symbol.iterator)?function(A){return(0,i.Z)(A)}:function(A){return A&&"function"===typeof Symbol&&A.constructor===Symbol&&A!==Symbol.prototype?"symbol":(0,i.Z)(A)},n(A)}function o(A,e,t){return e in A?Object.defineProperty(A,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):A[e]=t,A}function r(){return r=Object.assign||function(A){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(A[i]=t[i])}return A},r.apply(this,arguments)}function a(A){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{},i=Object.keys(t);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(t).filter((function(A){return Object.getOwnPropertyDescriptor(t,A).enumerable})))),i.forEach((function(e){o(A,e,t[e])}))}return A}function s(A,e){if(null==A)return{};var t,i,n={},o=Object.keys(A);for(i=0;i<o.length;i++)t=o[i],e.indexOf(t)>=0||(n[t]=A[t]);return n}function l(A,e){if(null==A)return{};var t,i,n=s(A,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(A);for(i=0;i<o.length;i++)t=o[i],e.indexOf(t)>=0||Object.prototype.propertyIsEnumerable.call(A,t)&&(n[t]=A[t])}return n}function g(A){return c(A)||I(A)||Q()}function c(A){if(Array.isArray(A)){for(var e=0,t=new Array(A.length);e<A.length;e++)t[e]=A[e];return t}}function I(A){if(Symbol.iterator in Object(A)||"[object Arguments]"===Object.prototype.toString.call(A))return Array.from(A)}function Q(){throw new TypeError("Invalid attempt to spread non-iterable instance")}var u="1.10.2";function C(A){if("undefined"!==typeof window&&window.navigator)return!!navigator.userAgent.match(A)}var h=C(/(?:Trident.*rv[ :]?11\.|msie|iemobile|Windows Phone)/i),d=C(/Edge/i),B=C(/firefox/i),E=C(/safari/i)&&!C(/chrome/i)&&!C(/android/i),p=C(/iP(ad|od|hone)/i),m=C(/chrome/i)&&C(/android/i),f={capture:!1,passive:!1};function y(A,e,t){A.addEventListener(e,t,!h&&f)}function b(A,e,t){A.removeEventListener(e,t,!h&&f)}function v(A,e){if(e){if(">"===e[0]&&(e=e.substring(1)),A)try{if(A.matches)return A.matches(e);if(A.msMatchesSelector)return A.msMatchesSelector(e);if(A.webkitMatchesSelector)return A.webkitMatchesSelector(e)}catch(t){return!1}return!1}}function w(A){return A.host&&A!==document&&A.host.nodeType?A.host:A.parentNode}function N(A,e,t,i){if(A){t=t||document;do{if(null!=e&&(">"===e[0]?A.parentNode===t&&v(A,e):v(A,e))||i&&A===t)return A;if(A===t)break}while(A=w(A))}return null}var D,Y=/\s+/g;function M(A,e,t){if(A&&e)if(A.classList)A.classList[t?"add":"remove"](e);else{var i=(" "+A.className+" ").replace(Y," ").replace(" "+e+" "," ");A.className=(i+(t?" "+e:"")).replace(Y," ")}}function k(A,e,t){var i=A&&A.style;if(i){if(void 0===t)return document.defaultView&&document.defaultView.getComputedStyle?t=document.defaultView.getComputedStyle(A,""):A.currentStyle&&(t=A.currentStyle),void 0===e?t:t[e];e in i||-1!==e.indexOf("webkit")||(e="-webkit-"+e),i[e]=t+("string"===typeof t?"":"px")}}function O(A,e){var t="";if("string"===typeof A)t=A;else do{var i=k(A,"transform");i&&"none"!==i&&(t=i+" "+t)}while(!e&&(A=A.parentNode));var n=window.DOMMatrix||window.WebKitCSSMatrix||window.CSSMatrix||window.MSCSSMatrix;return n&&new n(t)}function x(A,e,t){if(A){var i=A.getElementsByTagName(e),n=0,o=i.length;if(t)for(;n<o;n++)t(i[n],n);return i}return[]}function G(){var A=document.scrollingElement;return A||document.documentElement}function j(A,e,t,i,n){if(A.getBoundingClientRect||A===window){var o,r,a,s,l,g,c;if(A!==window&&A!==G()?(o=A.getBoundingClientRect(),r=o.top,a=o.left,s=o.bottom,l=o.right,g=o.height,c=o.width):(r=0,a=0,s=window.innerHeight,l=window.innerWidth,g=window.innerHeight,c=window.innerWidth),(e||t)&&A!==window&&(n=n||A.parentNode,!h))do{if(n&&n.getBoundingClientRect&&("none"!==k(n,"transform")||t&&"static"!==k(n,"position"))){var I=n.getBoundingClientRect();r-=I.top+parseInt(k(n,"border-top-width")),a-=I.left+parseInt(k(n,"border-left-width")),s=r+o.height,l=a+o.width;break}}while(n=n.parentNode);if(i&&A!==window){var Q=O(n||A),u=Q&&Q.a,C=Q&&Q.d;Q&&(r/=C,a/=u,c/=u,g/=C,s=r+g,l=a+c)}return{top:r,left:a,bottom:s,right:l,width:c,height:g}}}function Z(A,e,t){var i=T(A,!0),n=j(A)[e];while(i){var o=j(i)[t],r=void 0;if(r="top"===t||"left"===t?n>=o:n<=o,!r)return i;if(i===G())break;i=T(i,!1)}return!1}function F(A,e,t){var i=0,n=0,o=A.children;while(n<o.length){if("none"!==o[n].style.display&&o[n]!==$A.ghost&&o[n]!==$A.dragged&&N(o[n],t.draggable,A,!1)){if(i===e)return o[n];i++}n++}return null}function J(A,e){var t=A.lastElementChild;while(t&&(t===$A.ghost||"none"===k(t,"display")||e&&!v(t,e)))t=t.previousElementSibling;return t||null}function S(A,e){var t=0;if(!A||!A.parentNode)return-1;while(A=A.previousElementSibling)"TEMPLATE"===A.nodeName.toUpperCase()||A===$A.clone||e&&!v(A,e)||t++;return t}function R(A){var e=0,t=0,i=G();if(A)do{var n=O(A),o=n.a,r=n.d;e+=A.scrollLeft*o,t+=A.scrollTop*r}while(A!==i&&(A=A.parentNode));return[e,t]}function z(A,e){for(var t in A)if(A.hasOwnProperty(t))for(var i in e)if(e.hasOwnProperty(i)&&e[i]===A[t][i])return Number(t);return-1}function T(A,e){if(!A||!A.getBoundingClientRect)return G();var t=A,i=!1;do{if(t.clientWidth<t.scrollWidth||t.clientHeight<t.scrollHeight){var n=k(t);if(t.clientWidth<t.scrollWidth&&("auto"==n.overflowX||"scroll"==n.overflowX)||t.clientHeight<t.scrollHeight&&("auto"==n.overflowY||"scroll"==n.overflowY)){if(!t.getBoundingClientRect||t===document.body)return G();if(i||e)return t;i=!0}}}while(t=t.parentNode);return G()}function X(A,e){if(A&&e)for(var t in e)e.hasOwnProperty(t)&&(A[t]=e[t]);return A}function U(A,e){return Math.round(A.top)===Math.round(e.top)&&Math.round(A.left)===Math.round(e.left)&&Math.round(A.height)===Math.round(e.height)&&Math.round(A.width)===Math.round(e.width)}function W(A,e){return function(){if(!D){var t=arguments,i=this;1===t.length?A.call(i,t[0]):A.apply(i,t),D=setTimeout((function(){D=void 0}),e)}}}function V(){clearTimeout(D),D=void 0}function _(A,e,t){A.scrollLeft+=e,A.scrollTop+=t}function L(A){var e=window.Polymer,t=window.jQuery||window.Zepto;return e&&e.dom?e.dom(A).cloneNode(!0):t?t(A).clone(!0)[0]:A.cloneNode(!0)}function H(A,e){k(A,"position","absolute"),k(A,"top",e.top),k(A,"left",e.left),k(A,"width",e.width),k(A,"height",e.height)}function K(A){k(A,"position",""),k(A,"top",""),k(A,"left",""),k(A,"width",""),k(A,"height","")}var q="Sortable"+(new Date).getTime();function P(){var A,e=[];return{captureAnimationState:function(){if(e=[],this.options.animation){var A=[].slice.call(this.el.children);A.forEach((function(A){if("none"!==k(A,"display")&&A!==$A.ghost){e.push({target:A,rect:j(A)});var t=a({},e[e.length-1].rect);if(A.thisAnimationDuration){var i=O(A,!0);i&&(t.top-=i.f,t.left-=i.e)}A.fromRect=t}}))}},addAnimationState:function(A){e.push(A)},removeAnimationState:function(A){e.splice(z(e,{target:A}),1)},animateAll:function(t){var i=this;if(!this.options.animation)return clearTimeout(A),void("function"===typeof t&&t());var n=!1,o=0;e.forEach((function(A){var e=0,t=A.target,r=t.fromRect,a=j(t),s=t.prevFromRect,l=t.prevToRect,g=A.rect,c=O(t,!0);c&&(a.top-=c.f,a.left-=c.e),t.toRect=a,t.thisAnimationDuration&&U(s,a)&&!U(r,a)&&(g.top-a.top)/(g.left-a.left)===(r.top-a.top)/(r.left-a.left)&&(e=AA(g,s,l,i.options)),U(a,r)||(t.prevFromRect=r,t.prevToRect=a,e||(e=i.options.animation),i.animate(t,g,a,e)),e&&(n=!0,o=Math.max(o,e),clearTimeout(t.animationResetTimer),t.animationResetTimer=setTimeout((function(){t.animationTime=0,t.prevFromRect=null,t.fromRect=null,t.prevToRect=null,t.thisAnimationDuration=null}),e),t.thisAnimationDuration=e)})),clearTimeout(A),n?A=setTimeout((function(){"function"===typeof t&&t()}),o):"function"===typeof t&&t(),e=[]},animate:function(A,e,t,i){if(i){k(A,"transition",""),k(A,"transform","");var n=O(this.el),o=n&&n.a,r=n&&n.d,a=(e.left-t.left)/(o||1),s=(e.top-t.top)/(r||1);A.animatingX=!!a,A.animatingY=!!s,k(A,"transform","translate3d("+a+"px,"+s+"px,0)"),$(A),k(A,"transition","transform "+i+"ms"+(this.options.easing?" "+this.options.easing:"")),k(A,"transform","translate3d(0,0,0)"),"number"===typeof A.animated&&clearTimeout(A.animated),A.animated=setTimeout((function(){k(A,"transition",""),k(A,"transform",""),A.animated=!1,A.animatingX=!1,A.animatingY=!1}),i)}}}}function $(A){return A.offsetWidth}function AA(A,e,t,i){return Math.sqrt(Math.pow(e.top-A.top,2)+Math.pow(e.left-A.left,2))/Math.sqrt(Math.pow(e.top-t.top,2)+Math.pow(e.left-t.left,2))*i.animation}var eA=[],tA={initializeByDefault:!0},iA={mount:function(A){for(var e in tA)tA.hasOwnProperty(e)&&!(e in A)&&(A[e]=tA[e]);eA.push(A)},pluginEvent:function(A,e,t){var i=this;this.eventCanceled=!1,t.cancel=function(){i.eventCanceled=!0};var n=A+"Global";eA.forEach((function(i){e[i.pluginName]&&(e[i.pluginName][n]&&e[i.pluginName][n](a({sortable:e},t)),e.options[i.pluginName]&&e[i.pluginName][A]&&e[i.pluginName][A](a({sortable:e},t)))}))},initializePlugins:function(A,e,t,i){for(var n in eA.forEach((function(i){var n=i.pluginName;if(A.options[n]||i.initializeByDefault){var o=new i(A,e,A.options);o.sortable=A,o.options=A.options,A[n]=o,r(t,o.defaults)}})),A.options)if(A.options.hasOwnProperty(n)){var o=this.modifyOption(A,n,A.options[n]);"undefined"!==typeof o&&(A.options[n]=o)}},getEventProperties:function(A,e){var t={};return eA.forEach((function(i){"function"===typeof i.eventProperties&&r(t,i.eventProperties.call(e[i.pluginName],A))})),t},modifyOption:function(A,e,t){var i;return eA.forEach((function(n){A[n.pluginName]&&n.optionListeners&&"function"===typeof n.optionListeners[e]&&(i=n.optionListeners[e].call(A[n.pluginName],t))})),i}};function nA(A){var e=A.sortable,t=A.rootEl,i=A.name,n=A.targetEl,o=A.cloneEl,r=A.toEl,s=A.fromEl,l=A.oldIndex,g=A.newIndex,c=A.oldDraggableIndex,I=A.newDraggableIndex,Q=A.originalEvent,u=A.putSortable,C=A.extraEventProperties;if(e=e||t&&t[q],e){var B,E=e.options,p="on"+i.charAt(0).toUpperCase()+i.substr(1);!window.CustomEvent||h||d?(B=document.createEvent("Event"),B.initEvent(i,!0,!0)):B=new CustomEvent(i,{bubbles:!0,cancelable:!0}),B.to=r||t,B.from=s||t,B.item=n||t,B.clone=o,B.oldIndex=l,B.newIndex=g,B.oldDraggableIndex=c,B.newDraggableIndex=I,B.originalEvent=Q,B.pullMode=u?u.lastPutMode:void 0;var m=a({},C,iA.getEventProperties(i,e));for(var f in m)B[f]=m[f];t&&t.dispatchEvent(B),E[p]&&E[p].call(e,B)}}var oA=function(A,e){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=t.evt,n=l(t,["evt"]);iA.pluginEvent.bind($A)(A,e,a({dragEl:aA,parentEl:sA,ghostEl:lA,rootEl:gA,nextEl:cA,lastDownEl:IA,cloneEl:QA,cloneHidden:uA,dragStarted:NA,putSortable:pA,activeSortable:$A.active,originalEvent:i,oldIndex:CA,oldDraggableIndex:dA,newIndex:hA,newDraggableIndex:BA,hideGhostForTarget:HA,unhideGhostForTarget:KA,cloneNowHidden:function(){uA=!0},cloneNowShown:function(){uA=!1},dispatchSortableEvent:function(A){rA({sortable:e,name:A,originalEvent:i})}},n))};function rA(A){nA(a({putSortable:pA,cloneEl:QA,targetEl:aA,rootEl:gA,oldIndex:CA,oldDraggableIndex:dA,newIndex:hA,newDraggableIndex:BA},A))}var aA,sA,lA,gA,cA,IA,QA,uA,CA,hA,dA,BA,EA,pA,mA,fA,yA,bA,vA,wA,NA,DA,YA,MA,kA,OA=!1,xA=!1,GA=[],jA=!1,ZA=!1,FA=[],JA=!1,SA=[],RA="undefined"!==typeof document,zA=p,TA=d||h?"cssFloat":"float",XA=RA&&!m&&!p&&"draggable"in document.createElement("div"),UA=function(){if(RA){if(h)return!1;var A=document.createElement("x");return A.style.cssText="pointer-events:auto","auto"===A.style.pointerEvents}}(),WA=function(A,e){var t=k(A),i=parseInt(t.width)-parseInt(t.paddingLeft)-parseInt(t.paddingRight)-parseInt(t.borderLeftWidth)-parseInt(t.borderRightWidth),n=F(A,0,e),o=F(A,1,e),r=n&&k(n),a=o&&k(o),s=r&&parseInt(r.marginLeft)+parseInt(r.marginRight)+j(n).width,l=a&&parseInt(a.marginLeft)+parseInt(a.marginRight)+j(o).width;if("flex"===t.display)return"column"===t.flexDirection||"column-reverse"===t.flexDirection?"vertical":"horizontal";if("grid"===t.display)return t.gridTemplateColumns.split(" ").length<=1?"vertical":"horizontal";if(n&&r["float"]&&"none"!==r["float"]){var g="left"===r["float"]?"left":"right";return!o||"both"!==a.clear&&a.clear!==g?"horizontal":"vertical"}return n&&("block"===r.display||"flex"===r.display||"table"===r.display||"grid"===r.display||s>=i&&"none"===t[TA]||o&&"none"===t[TA]&&s+l>i)?"vertical":"horizontal"},VA=function(A,e,t){var i=t?A.left:A.top,n=t?A.right:A.bottom,o=t?A.width:A.height,r=t?e.left:e.top,a=t?e.right:e.bottom,s=t?e.width:e.height;return i===r||n===a||i+o/2===r+s/2},_A=function(A,e){var t;return GA.some((function(i){if(!J(i)){var n=j(i),o=i[q].options.emptyInsertThreshold,r=A>=n.left-o&&A<=n.right+o,a=e>=n.top-o&&e<=n.bottom+o;return o&&r&&a?t=i:void 0}})),t},LA=function(A){function e(A,t){return function(i,n,o,r){var a=i.options.group.name&&n.options.group.name&&i.options.group.name===n.options.group.name;if(null==A&&(t||a))return!0;if(null==A||!1===A)return!1;if(t&&"clone"===A)return A;if("function"===typeof A)return e(A(i,n,o,r),t)(i,n,o,r);var s=(t?i:n).options.group.name;return!0===A||"string"===typeof A&&A===s||A.join&&A.indexOf(s)>-1}}var t={},i=A.group;i&&"object"==n(i)||(i={name:i}),t.name=i.name,t.checkPull=e(i.pull,!0),t.checkPut=e(i.put),t.revertClone=i.revertClone,A.group=t},HA=function(){!UA&&lA&&k(lA,"display","none")},KA=function(){!UA&&lA&&k(lA,"display","")};RA&&document.addEventListener("click",(function(A){if(xA)return A.preventDefault(),A.stopPropagation&&A.stopPropagation(),A.stopImmediatePropagation&&A.stopImmediatePropagation(),xA=!1,!1}),!0);var qA=function(A){if(aA){A=A.touches?A.touches[0]:A;var e=_A(A.clientX,A.clientY);if(e){var t={};for(var i in A)A.hasOwnProperty(i)&&(t[i]=A[i]);t.target=t.rootEl=e,t.preventDefault=void 0,t.stopPropagation=void 0,e[q]._onDragOver(t)}}},PA=function(A){aA&&aA.parentNode[q]._isOutsideThisEl(A.target)};function $A(A,e){if(!A||!A.nodeType||1!==A.nodeType)throw"Sortable: `el` must be an HTMLElement, not ".concat({}.toString.call(A));this.el=A,this.options=e=r({},e),A[q]=this;var t={group:null,sort:!0,disabled:!1,store:null,handle:null,draggable:/^[uo]l$/i.test(A.nodeName)?">li":">*",swapThreshold:1,invertSwap:!1,invertedSwapThreshold:null,removeCloneOnHide:!0,direction:function(){return WA(A,this.options)},ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",dragClass:"sortable-drag",ignore:"a, img",filter:null,preventOnFilter:!0,animation:0,easing:null,setData:function(A,e){A.setData("Text",e.textContent)},dropBubble:!1,dragoverBubble:!1,dataIdAttr:"data-id",delay:0,delayOnTouchOnly:!1,touchStartThreshold:(Number.parseInt?Number:window).parseInt(window.devicePixelRatio,10)||1,forceFallback:!1,fallbackClass:"sortable-fallback",fallbackOnBody:!1,fallbackTolerance:0,fallbackOffset:{x:0,y:0},supportPointer:!1!==$A.supportPointer&&"PointerEvent"in window,emptyInsertThreshold:5};for(var i in iA.initializePlugins(this,A,t),t)!(i in e)&&(e[i]=t[i]);for(var n in LA(e),this)"_"===n.charAt(0)&&"function"===typeof this[n]&&(this[n]=this[n].bind(this));this.nativeDraggable=!e.forceFallback&&XA,this.nativeDraggable&&(this.options.touchStartThreshold=1),e.supportPointer?y(A,"pointerdown",this._onTapStart):(y(A,"mousedown",this._onTapStart),y(A,"touchstart",this._onTapStart)),this.nativeDraggable&&(y(A,"dragover",this),y(A,"dragenter",this)),GA.push(this.el),e.store&&e.store.get&&this.sort(e.store.get(this)||[]),r(this,P())}function Ae(A){A.dataTransfer&&(A.dataTransfer.dropEffect="move"),A.cancelable&&A.preventDefault()}function ee(A,e,t,i,n,o,r,a){var s,l,g=A[q],c=g.options.onMove;return!window.CustomEvent||h||d?(s=document.createEvent("Event"),s.initEvent("move",!0,!0)):s=new CustomEvent("move",{bubbles:!0,cancelable:!0}),s.to=e,s.from=A,s.dragged=t,s.draggedRect=i,s.related=n||e,s.relatedRect=o||j(e),s.willInsertAfter=a,s.originalEvent=r,A.dispatchEvent(s),c&&(l=c.call(g,s,r)),l}function te(A){A.draggable=!1}function ie(){JA=!1}function ne(A,e,t){var i=j(J(t.el,t.options.draggable)),n=10;return e?A.clientX>i.right+n||A.clientX<=i.right&&A.clientY>i.bottom&&A.clientX>=i.left:A.clientX>i.right&&A.clientY>i.top||A.clientX<=i.right&&A.clientY>i.bottom+n}function oe(A,e,t,i,n,o,r,a){var s=i?A.clientY:A.clientX,l=i?t.height:t.width,g=i?t.top:t.left,c=i?t.bottom:t.right,I=!1;if(!r)if(a&&MA<l*n){if(!jA&&(1===YA?s>g+l*o/2:s<c-l*o/2)&&(jA=!0),jA)I=!0;else if(1===YA?s<g+MA:s>c-MA)return-YA}else if(s>g+l*(1-n)/2&&s<c-l*(1-n)/2)return re(e);return I=I||r,I&&(s<g+l*o/2||s>c-l*o/2)?s>g+l/2?1:-1:0}function re(A){return S(aA)<S(A)?1:-1}function ae(A){var e=A.tagName+A.className+A.src+A.href+A.textContent,t=e.length,i=0;while(t--)i+=e.charCodeAt(t);return i.toString(36)}function se(A){SA.length=0;var e=A.getElementsByTagName("input"),t=e.length;while(t--){var i=e[t];i.checked&&SA.push(i)}}function le(A){return setTimeout(A,0)}function ge(A){return clearTimeout(A)}$A.prototype={constructor:$A,_isOutsideThisEl:function(A){this.el.contains(A)||A===this.el||(DA=null)},_getDirection:function(A,e){return"function"===typeof this.options.direction?this.options.direction.call(this,A,e,aA):this.options.direction},_onTapStart:function(A){if(A.cancelable){var e=this,t=this.el,i=this.options,n=i.preventOnFilter,o=A.type,r=A.touches&&A.touches[0]||A.pointerType&&"touch"===A.pointerType&&A,a=(r||A).target,s=A.target.shadowRoot&&(A.path&&A.path[0]||A.composedPath&&A.composedPath()[0])||a,l=i.filter;if(se(t),!aA&&!(/mousedown|pointerdown/.test(o)&&0!==A.button||i.disabled)&&!s.isContentEditable&&(a=N(a,i.draggable,t,!1),(!a||!a.animated)&&IA!==a)){if(CA=S(a),dA=S(a,i.draggable),"function"===typeof l){if(l.call(this,A,a,this))return rA({sortable:e,rootEl:s,name:"filter",targetEl:a,toEl:t,fromEl:t}),oA("filter",e,{evt:A}),void(n&&A.cancelable&&A.preventDefault())}else if(l&&(l=l.split(",").some((function(i){if(i=N(s,i.trim(),t,!1),i)return rA({sortable:e,rootEl:i,name:"filter",targetEl:a,fromEl:t,toEl:t}),oA("filter",e,{evt:A}),!0})),l))return void(n&&A.cancelable&&A.preventDefault());i.handle&&!N(s,i.handle,t,!1)||this._prepareDragStart(A,r,a)}}},_prepareDragStart:function(A,e,t){var i,n=this,o=n.el,r=n.options,a=o.ownerDocument;if(t&&!aA&&t.parentNode===o){var s=j(t);if(gA=o,aA=t,sA=aA.parentNode,cA=aA.nextSibling,IA=t,EA=r.group,$A.dragged=aA,mA={target:aA,clientX:(e||A).clientX,clientY:(e||A).clientY},vA=mA.clientX-s.left,wA=mA.clientY-s.top,this._lastX=(e||A).clientX,this._lastY=(e||A).clientY,aA.style["will-change"]="all",i=function(){oA("delayEnded",n,{evt:A}),$A.eventCanceled?n._onDrop():(n._disableDelayedDragEvents(),!B&&n.nativeDraggable&&(aA.draggable=!0),n._triggerDragStart(A,e),rA({sortable:n,name:"choose",originalEvent:A}),M(aA,r.chosenClass,!0))},r.ignore.split(",").forEach((function(A){x(aA,A.trim(),te)})),y(a,"dragover",qA),y(a,"mousemove",qA),y(a,"touchmove",qA),y(a,"mouseup",n._onDrop),y(a,"touchend",n._onDrop),y(a,"touchcancel",n._onDrop),B&&this.nativeDraggable&&(this.options.touchStartThreshold=4,aA.draggable=!0),oA("delayStart",this,{evt:A}),!r.delay||r.delayOnTouchOnly&&!e||this.nativeDraggable&&(d||h))i();else{if($A.eventCanceled)return void this._onDrop();y(a,"mouseup",n._disableDelayedDrag),y(a,"touchend",n._disableDelayedDrag),y(a,"touchcancel",n._disableDelayedDrag),y(a,"mousemove",n._delayedDragTouchMoveHandler),y(a,"touchmove",n._delayedDragTouchMoveHandler),r.supportPointer&&y(a,"pointermove",n._delayedDragTouchMoveHandler),n._dragStartTimer=setTimeout(i,r.delay)}}},_delayedDragTouchMoveHandler:function(A){var e=A.touches?A.touches[0]:A;Math.max(Math.abs(e.clientX-this._lastX),Math.abs(e.clientY-this._lastY))>=Math.floor(this.options.touchStartThreshold/(this.nativeDraggable&&window.devicePixelRatio||1))&&this._disableDelayedDrag()},_disableDelayedDrag:function(){aA&&te(aA),clearTimeout(this._dragStartTimer),this._disableDelayedDragEvents()},_disableDelayedDragEvents:function(){var A=this.el.ownerDocument;b(A,"mouseup",this._disableDelayedDrag),b(A,"touchend",this._disableDelayedDrag),b(A,"touchcancel",this._disableDelayedDrag),b(A,"mousemove",this._delayedDragTouchMoveHandler),b(A,"touchmove",this._delayedDragTouchMoveHandler),b(A,"pointermove",this._delayedDragTouchMoveHandler)},_triggerDragStart:function(A,e){e=e||"touch"==A.pointerType&&A,!this.nativeDraggable||e?this.options.supportPointer?y(document,"pointermove",this._onTouchMove):y(document,e?"touchmove":"mousemove",this._onTouchMove):(y(aA,"dragend",this),y(gA,"dragstart",this._onDragStart));try{document.selection?le((function(){document.selection.empty()})):window.getSelection().removeAllRanges()}catch(t){}},_dragStarted:function(A,e){if(OA=!1,gA&&aA){oA("dragStarted",this,{evt:e}),this.nativeDraggable&&y(document,"dragover",PA);var t=this.options;!A&&M(aA,t.dragClass,!1),M(aA,t.ghostClass,!0),$A.active=this,A&&this._appendGhost(),rA({sortable:this,name:"start",originalEvent:e})}else this._nulling()},_emulateDragOver:function(){if(fA){this._lastX=fA.clientX,this._lastY=fA.clientY,HA();var A=document.elementFromPoint(fA.clientX,fA.clientY),e=A;while(A&&A.shadowRoot){if(A=A.shadowRoot.elementFromPoint(fA.clientX,fA.clientY),A===e)break;e=A}if(aA.parentNode[q]._isOutsideThisEl(A),e)do{if(e[q]){var t=void 0;if(t=e[q]._onDragOver({clientX:fA.clientX,clientY:fA.clientY,target:A,rootEl:e}),t&&!this.options.dragoverBubble)break}A=e}while(e=e.parentNode);KA()}},_onTouchMove:function(A){if(mA){var e=this.options,t=e.fallbackTolerance,i=e.fallbackOffset,n=A.touches?A.touches[0]:A,o=lA&&O(lA,!0),r=lA&&o&&o.a,a=lA&&o&&o.d,s=zA&&kA&&R(kA),l=(n.clientX-mA.clientX+i.x)/(r||1)+(s?s[0]-FA[0]:0)/(r||1),g=(n.clientY-mA.clientY+i.y)/(a||1)+(s?s[1]-FA[1]:0)/(a||1);if(!$A.active&&!OA){if(t&&Math.max(Math.abs(n.clientX-this._lastX),Math.abs(n.clientY-this._lastY))<t)return;this._onDragStart(A,!0)}if(lA){o?(o.e+=l-(yA||0),o.f+=g-(bA||0)):o={a:1,b:0,c:0,d:1,e:l,f:g};var c="matrix(".concat(o.a,",").concat(o.b,",").concat(o.c,",").concat(o.d,",").concat(o.e,",").concat(o.f,")");k(lA,"webkitTransform",c),k(lA,"mozTransform",c),k(lA,"msTransform",c),k(lA,"transform",c),yA=l,bA=g,fA=n}A.cancelable&&A.preventDefault()}},_appendGhost:function(){if(!lA){var A=this.options.fallbackOnBody?document.body:gA,e=j(aA,!0,zA,!0,A),t=this.options;if(zA){kA=A;while("static"===k(kA,"position")&&"none"===k(kA,"transform")&&kA!==document)kA=kA.parentNode;kA!==document.body&&kA!==document.documentElement?(kA===document&&(kA=G()),e.top+=kA.scrollTop,e.left+=kA.scrollLeft):kA=G(),FA=R(kA)}lA=aA.cloneNode(!0),M(lA,t.ghostClass,!1),M(lA,t.fallbackClass,!0),M(lA,t.dragClass,!0),k(lA,"transition",""),k(lA,"transform",""),k(lA,"box-sizing","border-box"),k(lA,"margin",0),k(lA,"top",e.top),k(lA,"left",e.left),k(lA,"width",e.width),k(lA,"height",e.height),k(lA,"opacity","0.8"),k(lA,"position",zA?"absolute":"fixed"),k(lA,"zIndex","100000"),k(lA,"pointerEvents","none"),$A.ghost=lA,A.appendChild(lA),k(lA,"transform-origin",vA/parseInt(lA.style.width)*100+"% "+wA/parseInt(lA.style.height)*100+"%")}},_onDragStart:function(A,e){var t=this,i=A.dataTransfer,n=t.options;oA("dragStart",this,{evt:A}),$A.eventCanceled?this._onDrop():(oA("setupClone",this),$A.eventCanceled||(QA=L(aA),QA.draggable=!1,QA.style["will-change"]="",this._hideClone(),M(QA,this.options.chosenClass,!1),$A.clone=QA),t.cloneId=le((function(){oA("clone",t),$A.eventCanceled||(t.options.removeCloneOnHide||gA.insertBefore(QA,aA),t._hideClone(),rA({sortable:t,name:"clone"}))})),!e&&M(aA,n.dragClass,!0),e?(xA=!0,t._loopId=setInterval(t._emulateDragOver,50)):(b(document,"mouseup",t._onDrop),b(document,"touchend",t._onDrop),b(document,"touchcancel",t._onDrop),i&&(i.effectAllowed="move",n.setData&&n.setData.call(t,i,aA)),y(document,"drop",t),k(aA,"transform","translateZ(0)")),OA=!0,t._dragStartId=le(t._dragStarted.bind(t,e,A)),y(document,"selectstart",t),NA=!0,E&&k(document.body,"user-select","none"))},_onDragOver:function(A){var e,t,i,n,o=this.el,r=A.target,s=this.options,l=s.group,g=$A.active,c=EA===l,I=s.sort,Q=pA||g,u=this,C=!1;if(!JA){if(void 0!==A.preventDefault&&A.cancelable&&A.preventDefault(),r=N(r,s.draggable,o,!0),O("dragOver"),$A.eventCanceled)return C;if(aA.contains(A.target)||r.animated&&r.animatingX&&r.animatingY||u._ignoreWhileAnimating===r)return G(!1);if(xA=!1,g&&!s.disabled&&(c?I||(i=!gA.contains(aA)):pA===this||(this.lastPutMode=EA.checkPull(this,g,aA,A))&&l.checkPut(this,g,aA,A))){if(n="vertical"===this._getDirection(A,r),e=j(aA),O("dragOverValid"),$A.eventCanceled)return C;if(i)return sA=gA,x(),this._hideClone(),O("revert"),$A.eventCanceled||(cA?gA.insertBefore(aA,cA):gA.appendChild(aA)),G(!0);var h=J(o,s.draggable);if(!h||ne(A,n,this)&&!h.animated){if(h===aA)return G(!1);if(h&&o===A.target&&(r=h),r&&(t=j(r)),!1!==ee(gA,o,aA,e,r,t,A,!!r))return x(),o.appendChild(aA),sA=o,F(),G(!0)}else if(r.parentNode===o){t=j(r);var d,B,E=0,p=aA.parentNode!==o,m=!VA(aA.animated&&aA.toRect||e,r.animated&&r.toRect||t,n),f=n?"top":"left",y=Z(r,"top","top")||Z(aA,"top","top"),b=y?y.scrollTop:void 0;if(DA!==r&&(d=t[f],jA=!1,ZA=!m&&s.invertSwap||p),E=oe(A,r,t,n,m?1:s.swapThreshold,null==s.invertedSwapThreshold?s.swapThreshold:s.invertedSwapThreshold,ZA,DA===r),0!==E){var v=S(aA);do{v-=E,B=sA.children[v]}while(B&&("none"===k(B,"display")||B===lA))}if(0===E||B===r)return G(!1);DA=r,YA=E;var w=r.nextElementSibling,D=!1;D=1===E;var Y=ee(gA,o,aA,e,r,t,A,D);if(!1!==Y)return 1!==Y&&-1!==Y||(D=1===Y),JA=!0,setTimeout(ie,30),x(),D&&!w?o.appendChild(aA):r.parentNode.insertBefore(aA,D?w:r),y&&_(y,0,b-y.scrollTop),sA=aA.parentNode,void 0===d||ZA||(MA=Math.abs(d-j(r)[f])),F(),G(!0)}if(o.contains(aA))return G(!1)}return!1}function O(s,l){oA(s,u,a({evt:A,isOwner:c,axis:n?"vertical":"horizontal",revert:i,dragRect:e,targetRect:t,canSort:I,fromSortable:Q,target:r,completed:G,onMove:function(t,i){return ee(gA,o,aA,e,t,j(t),A,i)},changed:F},l))}function x(){O("dragOverAnimationCapture"),u.captureAnimationState(),u!==Q&&Q.captureAnimationState()}function G(e){return O("dragOverCompleted",{insertion:e}),e&&(c?g._hideClone():g._showClone(u),u!==Q&&(M(aA,pA?pA.options.ghostClass:g.options.ghostClass,!1),M(aA,s.ghostClass,!0)),pA!==u&&u!==$A.active?pA=u:u===$A.active&&pA&&(pA=null),Q===u&&(u._ignoreWhileAnimating=r),u.animateAll((function(){O("dragOverAnimationComplete"),u._ignoreWhileAnimating=null})),u!==Q&&(Q.animateAll(),Q._ignoreWhileAnimating=null)),(r===aA&&!aA.animated||r===o&&!r.animated)&&(DA=null),s.dragoverBubble||A.rootEl||r===document||(aA.parentNode[q]._isOutsideThisEl(A.target),!e&&qA(A)),!s.dragoverBubble&&A.stopPropagation&&A.stopPropagation(),C=!0}function F(){hA=S(aA),BA=S(aA,s.draggable),rA({sortable:u,name:"change",toEl:o,newIndex:hA,newDraggableIndex:BA,originalEvent:A})}},_ignoreWhileAnimating:null,_offMoveEvents:function(){b(document,"mousemove",this._onTouchMove),b(document,"touchmove",this._onTouchMove),b(document,"pointermove",this._onTouchMove),b(document,"dragover",qA),b(document,"mousemove",qA),b(document,"touchmove",qA)},_offUpEvents:function(){var A=this.el.ownerDocument;b(A,"mouseup",this._onDrop),b(A,"touchend",this._onDrop),b(A,"pointerup",this._onDrop),b(A,"touchcancel",this._onDrop),b(document,"selectstart",this)},_onDrop:function(A){var e=this.el,t=this.options;hA=S(aA),BA=S(aA,t.draggable),oA("drop",this,{evt:A}),sA=aA&&aA.parentNode,hA=S(aA),BA=S(aA,t.draggable),$A.eventCanceled||(OA=!1,ZA=!1,jA=!1,clearInterval(this._loopId),clearTimeout(this._dragStartTimer),ge(this.cloneId),ge(this._dragStartId),this.nativeDraggable&&(b(document,"drop",this),b(e,"dragstart",this._onDragStart)),this._offMoveEvents(),this._offUpEvents(),E&&k(document.body,"user-select",""),k(aA,"transform",""),A&&(NA&&(A.cancelable&&A.preventDefault(),!t.dropBubble&&A.stopPropagation()),lA&&lA.parentNode&&lA.parentNode.removeChild(lA),(gA===sA||pA&&"clone"!==pA.lastPutMode)&&QA&&QA.parentNode&&QA.parentNode.removeChild(QA),aA&&(this.nativeDraggable&&b(aA,"dragend",this),te(aA),aA.style["will-change"]="",NA&&!OA&&M(aA,pA?pA.options.ghostClass:this.options.ghostClass,!1),M(aA,this.options.chosenClass,!1),rA({sortable:this,name:"unchoose",toEl:sA,newIndex:null,newDraggableIndex:null,originalEvent:A}),gA!==sA?(hA>=0&&(rA({rootEl:sA,name:"add",toEl:sA,fromEl:gA,originalEvent:A}),rA({sortable:this,name:"remove",toEl:sA,originalEvent:A}),rA({rootEl:sA,name:"sort",toEl:sA,fromEl:gA,originalEvent:A}),rA({sortable:this,name:"sort",toEl:sA,originalEvent:A})),pA&&pA.save()):hA!==CA&&hA>=0&&(rA({sortable:this,name:"update",toEl:sA,originalEvent:A}),rA({sortable:this,name:"sort",toEl:sA,originalEvent:A})),$A.active&&(null!=hA&&-1!==hA||(hA=CA,BA=dA),rA({sortable:this,name:"end",toEl:sA,originalEvent:A}),this.save())))),this._nulling()},_nulling:function(){oA("nulling",this),gA=aA=sA=lA=cA=QA=IA=uA=mA=fA=NA=hA=BA=CA=dA=DA=YA=pA=EA=$A.dragged=$A.ghost=$A.clone=$A.active=null,SA.forEach((function(A){A.checked=!0})),SA.length=yA=bA=0},handleEvent:function(A){switch(A.type){case"drop":case"dragend":this._onDrop(A);break;case"dragenter":case"dragover":aA&&(this._onDragOver(A),Ae(A));break;case"selectstart":A.preventDefault();break}},toArray:function(){for(var A,e=[],t=this.el.children,i=0,n=t.length,o=this.options;i<n;i++)A=t[i],N(A,o.draggable,this.el,!1)&&e.push(A.getAttribute(o.dataIdAttr)||ae(A));return e},sort:function(A){var e={},t=this.el;this.toArray().forEach((function(A,i){var n=t.children[i];N(n,this.options.draggable,t,!1)&&(e[A]=n)}),this),A.forEach((function(A){e[A]&&(t.removeChild(e[A]),t.appendChild(e[A]))}))},save:function(){var A=this.options.store;A&&A.set&&A.set(this)},closest:function(A,e){return N(A,e||this.options.draggable,this.el,!1)},option:function(A,e){var t=this.options;if(void 0===e)return t[A];var i=iA.modifyOption(this,A,e);t[A]="undefined"!==typeof i?i:e,"group"===A&&LA(t)},destroy:function(){oA("destroy",this);var A=this.el;A[q]=null,b(A,"mousedown",this._onTapStart),b(A,"touchstart",this._onTapStart),b(A,"pointerdown",this._onTapStart),this.nativeDraggable&&(b(A,"dragover",this),b(A,"dragenter",this)),Array.prototype.forEach.call(A.querySelectorAll("[draggable]"),(function(A){A.removeAttribute("draggable")})),this._onDrop(),this._disableDelayedDragEvents(),GA.splice(GA.indexOf(this.el),1),this.el=A=null},_hideClone:function(){if(!uA){if(oA("hideClone",this),$A.eventCanceled)return;k(QA,"display","none"),this.options.removeCloneOnHide&&QA.parentNode&&QA.parentNode.removeChild(QA),uA=!0}},_showClone:function(A){if("clone"===A.lastPutMode){if(uA){if(oA("showClone",this),$A.eventCanceled)return;gA.contains(aA)&&!this.options.group.revertClone?gA.insertBefore(QA,aA):cA?gA.insertBefore(QA,cA):gA.appendChild(QA),this.options.group.revertClone&&this.animate(aA,QA),k(QA,"display",""),uA=!1}}else this._hideClone()}},RA&&y(document,"touchmove",(function(A){($A.active||OA)&&A.cancelable&&A.preventDefault()})),$A.utils={on:y,off:b,css:k,find:x,is:function(A,e){return!!N(A,e,A,!1)},extend:X,throttle:W,closest:N,toggleClass:M,clone:L,index:S,nextTick:le,cancelNextTick:ge,detectDirection:WA,getChild:F},$A.get=function(A){return A[q]},$A.mount=function(){for(var A=arguments.length,e=new Array(A),t=0;t<A;t++)e[t]=arguments[t];e[0].constructor===Array&&(e=e[0]),e.forEach((function(A){if(!A.prototype||!A.prototype.constructor)throw"Sortable: Mounted plugin must be a constructor function, not ".concat({}.toString.call(A));A.utils&&($A.utils=a({},$A.utils,A.utils)),iA.mount(A)}))},$A.create=function(A,e){return new $A(A,e)},$A.version=u;var ce,Ie,Qe,ue,Ce,he,de=[],Be=!1;function Ee(){function A(){for(var A in this.defaults={scroll:!0,scrollSensitivity:30,scrollSpeed:10,bubbleScroll:!0},this)"_"===A.charAt(0)&&"function"===typeof this[A]&&(this[A]=this[A].bind(this))}return A.prototype={dragStarted:function(A){var e=A.originalEvent;this.sortable.nativeDraggable?y(document,"dragover",this._handleAutoScroll):this.options.supportPointer?y(document,"pointermove",this._handleFallbackAutoScroll):e.touches?y(document,"touchmove",this._handleFallbackAutoScroll):y(document,"mousemove",this._handleFallbackAutoScroll)},dragOverCompleted:function(A){var e=A.originalEvent;this.options.dragOverBubble||e.rootEl||this._handleAutoScroll(e)},drop:function(){this.sortable.nativeDraggable?b(document,"dragover",this._handleAutoScroll):(b(document,"pointermove",this._handleFallbackAutoScroll),b(document,"touchmove",this._handleFallbackAutoScroll),b(document,"mousemove",this._handleFallbackAutoScroll)),me(),pe(),V()},nulling:function(){Ce=Ie=ce=Be=he=Qe=ue=null,de.length=0},_handleFallbackAutoScroll:function(A){this._handleAutoScroll(A,!0)},_handleAutoScroll:function(A,e){var t=this,i=(A.touches?A.touches[0]:A).clientX,n=(A.touches?A.touches[0]:A).clientY,o=document.elementFromPoint(i,n);if(Ce=A,e||d||h||E){ye(A,this.options,o,e);var r=T(o,!0);!Be||he&&i===Qe&&n===ue||(he&&me(),he=setInterval((function(){var o=T(document.elementFromPoint(i,n),!0);o!==r&&(r=o,pe()),ye(A,t.options,o,e)}),10),Qe=i,ue=n)}else{if(!this.options.bubbleScroll||T(o,!0)===G())return void pe();ye(A,this.options,T(o,!1),!1)}}},r(A,{pluginName:"scroll",initializeByDefault:!0})}function pe(){de.forEach((function(A){clearInterval(A.pid)})),de=[]}function me(){clearInterval(he)}var fe,ye=W((function(A,e,t,i){if(e.scroll){var n,o=(A.touches?A.touches[0]:A).clientX,r=(A.touches?A.touches[0]:A).clientY,a=e.scrollSensitivity,s=e.scrollSpeed,l=G(),g=!1;Ie!==t&&(Ie=t,pe(),ce=e.scroll,n=e.scrollFn,!0===ce&&(ce=T(t,!0)));var c=0,I=ce;do{var Q=I,u=j(Q),C=u.top,h=u.bottom,d=u.left,B=u.right,E=u.width,p=u.height,m=void 0,f=void 0,y=Q.scrollWidth,b=Q.scrollHeight,v=k(Q),w=Q.scrollLeft,N=Q.scrollTop;Q===l?(m=E<y&&("auto"===v.overflowX||"scroll"===v.overflowX||"visible"===v.overflowX),f=p<b&&("auto"===v.overflowY||"scroll"===v.overflowY||"visible"===v.overflowY)):(m=E<y&&("auto"===v.overflowX||"scroll"===v.overflowX),f=p<b&&("auto"===v.overflowY||"scroll"===v.overflowY));var D=m&&(Math.abs(B-o)<=a&&w+E<y)-(Math.abs(d-o)<=a&&!!w),Y=f&&(Math.abs(h-r)<=a&&N+p<b)-(Math.abs(C-r)<=a&&!!N);if(!de[c])for(var M=0;M<=c;M++)de[M]||(de[M]={});de[c].vx==D&&de[c].vy==Y&&de[c].el===Q||(de[c].el=Q,de[c].vx=D,de[c].vy=Y,clearInterval(de[c].pid),0==D&&0==Y||(g=!0,de[c].pid=setInterval(function(){i&&0===this.layer&&$A.active._onTouchMove(Ce);var e=de[this.layer].vy?de[this.layer].vy*s:0,t=de[this.layer].vx?de[this.layer].vx*s:0;"function"===typeof n&&"continue"!==n.call($A.dragged.parentNode[q],t,e,A,Ce,de[this.layer].el)||_(de[this.layer].el,t,e)}.bind({layer:c}),24))),c++}while(e.bubbleScroll&&I!==l&&(I=T(I,!1)));Be=g}}),30),be=function(A){var e=A.originalEvent,t=A.putSortable,i=A.dragEl,n=A.activeSortable,o=A.dispatchSortableEvent,r=A.hideGhostForTarget,a=A.unhideGhostForTarget;if(e){var s=t||n;r();var l=e.changedTouches&&e.changedTouches.length?e.changedTouches[0]:e,g=document.elementFromPoint(l.clientX,l.clientY);a(),s&&!s.el.contains(g)&&(o("spill"),this.onSpill({dragEl:i,putSortable:t}))}};function ve(){}function we(){}function Ne(){function A(){this.defaults={swapClass:"sortable-swap-highlight"}}return A.prototype={dragStart:function(A){var e=A.dragEl;fe=e},dragOverValid:function(A){var e=A.completed,t=A.target,i=A.onMove,n=A.activeSortable,o=A.changed,r=A.cancel;if(n.options.swap){var a=this.sortable.el,s=this.options;if(t&&t!==a){var l=fe;!1!==i(t)?(M(t,s.swapClass,!0),fe=t):fe=null,l&&l!==fe&&M(l,s.swapClass,!1)}o(),e(!0),r()}},drop:function(A){var e=A.activeSortable,t=A.putSortable,i=A.dragEl,n=t||this.sortable,o=this.options;fe&&M(fe,o.swapClass,!1),fe&&(o.swap||t&&t.options.swap)&&i!==fe&&(n.captureAnimationState(),n!==e&&e.captureAnimationState(),De(i,fe),n.animateAll(),n!==e&&e.animateAll())},nulling:function(){fe=null}},r(A,{pluginName:"swap",eventProperties:function(){return{swapItem:fe}}})}function De(A,e){var t,i,n=A.parentNode,o=e.parentNode;n&&o&&!n.isEqualNode(e)&&!o.isEqualNode(A)&&(t=S(A),i=S(e),n.isEqualNode(o)&&t<i&&i++,n.insertBefore(e,n.children[t]),o.insertBefore(A,o.children[i]))}ve.prototype={startIndex:null,dragStart:function(A){var e=A.oldDraggableIndex;this.startIndex=e},onSpill:function(A){var e=A.dragEl,t=A.putSortable;this.sortable.captureAnimationState(),t&&t.captureAnimationState();var i=F(this.sortable.el,this.startIndex,this.options);i?this.sortable.el.insertBefore(e,i):this.sortable.el.appendChild(e),this.sortable.animateAll(),t&&t.animateAll()},drop:be},r(ve,{pluginName:"revertOnSpill"}),we.prototype={onSpill:function(A){var e=A.dragEl,t=A.putSortable,i=t||this.sortable;i.captureAnimationState(),e.parentNode&&e.parentNode.removeChild(e),i.animateAll()},drop:be},r(we,{pluginName:"removeOnSpill"});var Ye,Me,ke,Oe,xe,Ge=/^(4([48]26|535|564|847)|2654|5025|8555|866|9093)$/.test(t.j)?null:[],je=/^(4([48]26|535|564|847)|2654|5025|8555|866|9093)$/.test(t.j)?null:[],Ze=!1,Fe=!1,Je=!1;function Se(){function A(A){for(var e in this)"_"===e.charAt(0)&&"function"===typeof this[e]&&(this[e]=this[e].bind(this));A.options.supportPointer?y(document,"pointerup",this._deselectMultiDrag):(y(document,"mouseup",this._deselectMultiDrag),y(document,"touchend",this._deselectMultiDrag)),y(document,"keydown",this._checkKeyDown),y(document,"keyup",this._checkKeyUp),this.defaults={selectedClass:"sortable-selected",multiDragKey:null,setData:function(e,t){var i="";Ge.length&&Me===A?Ge.forEach((function(A,e){i+=(e?", ":"")+A.textContent})):i=t.textContent,e.setData("Text",i)}}}return A.prototype={multiDragKeyDown:!1,isMultiDrag:!1,delayStartGlobal:function(A){var e=A.dragEl;ke=e},delayEnded:function(){this.isMultiDrag=~Ge.indexOf(ke)},setupClone:function(A){var e=A.sortable,t=A.cancel;if(this.isMultiDrag){for(var i=0;i<Ge.length;i++)je.push(L(Ge[i])),je[i].sortableIndex=Ge[i].sortableIndex,je[i].draggable=!1,je[i].style["will-change"]="",M(je[i],this.options.selectedClass,!1),Ge[i]===ke&&M(je[i],this.options.chosenClass,!1);e._hideClone(),t()}},clone:function(A){var e=A.sortable,t=A.rootEl,i=A.dispatchSortableEvent,n=A.cancel;this.isMultiDrag&&(this.options.removeCloneOnHide||Ge.length&&Me===e&&(ze(!0,t),i("clone"),n()))},showClone:function(A){var e=A.cloneNowShown,t=A.rootEl,i=A.cancel;this.isMultiDrag&&(ze(!1,t),je.forEach((function(A){k(A,"display","")})),e(),xe=!1,i())},hideClone:function(A){var e=this,t=(A.sortable,A.cloneNowHidden),i=A.cancel;this.isMultiDrag&&(je.forEach((function(A){k(A,"display","none"),e.options.removeCloneOnHide&&A.parentNode&&A.parentNode.removeChild(A)})),t(),xe=!0,i())},dragStartGlobal:function(A){A.sortable;!this.isMultiDrag&&Me&&Me.multiDrag._deselectMultiDrag(),Ge.forEach((function(A){A.sortableIndex=S(A)})),Ge=Ge.sort((function(A,e){return A.sortableIndex-e.sortableIndex})),Je=!0},dragStarted:function(A){var e=this,t=A.sortable;if(this.isMultiDrag){if(this.options.sort&&(t.captureAnimationState(),this.options.animation)){Ge.forEach((function(A){A!==ke&&k(A,"position","absolute")}));var i=j(ke,!1,!0,!0);Ge.forEach((function(A){A!==ke&&H(A,i)})),Fe=!0,Ze=!0}t.animateAll((function(){Fe=!1,Ze=!1,e.options.animation&&Ge.forEach((function(A){K(A)})),e.options.sort&&Te()}))}},dragOver:function(A){var e=A.target,t=A.completed,i=A.cancel;Fe&&~Ge.indexOf(e)&&(t(!1),i())},revert:function(A){var e=A.fromSortable,t=A.rootEl,i=A.sortable,n=A.dragRect;Ge.length>1&&(Ge.forEach((function(A){i.addAnimationState({target:A,rect:Fe?j(A):n}),K(A),A.fromRect=n,e.removeAnimationState(A)})),Fe=!1,Re(!this.options.removeCloneOnHide,t))},dragOverCompleted:function(A){var e=A.sortable,t=A.isOwner,i=A.insertion,n=A.activeSortable,o=A.parentEl,r=A.putSortable,a=this.options;if(i){if(t&&n._hideClone(),Ze=!1,a.animation&&Ge.length>1&&(Fe||!t&&!n.options.sort&&!r)){var s=j(ke,!1,!0,!0);Ge.forEach((function(A){A!==ke&&(H(A,s),o.appendChild(A))})),Fe=!0}if(!t)if(Fe||Te(),Ge.length>1){var l=xe;n._showClone(e),n.options.animation&&!xe&&l&&je.forEach((function(A){n.addAnimationState({target:A,rect:Oe}),A.fromRect=Oe,A.thisAnimationDuration=null}))}else n._showClone(e)}},dragOverAnimationCapture:function(A){var e=A.dragRect,t=A.isOwner,i=A.activeSortable;if(Ge.forEach((function(A){A.thisAnimationDuration=null})),i.options.animation&&!t&&i.multiDrag.isMultiDrag){Oe=r({},e);var n=O(ke,!0);Oe.top-=n.f,Oe.left-=n.e}},dragOverAnimationComplete:function(){Fe&&(Fe=!1,Te())},drop:function(A){var e=A.originalEvent,t=A.rootEl,i=A.parentEl,n=A.sortable,o=A.dispatchSortableEvent,r=A.oldIndex,a=A.putSortable,s=a||this.sortable;if(e){var l=this.options,g=i.children;if(!Je)if(l.multiDragKey&&!this.multiDragKeyDown&&this._deselectMultiDrag(),M(ke,l.selectedClass,!~Ge.indexOf(ke)),~Ge.indexOf(ke))Ge.splice(Ge.indexOf(ke),1),Ye=null,nA({sortable:n,rootEl:t,name:"deselect",targetEl:ke,originalEvt:e});else{if(Ge.push(ke),nA({sortable:n,rootEl:t,name:"select",targetEl:ke,originalEvt:e}),e.shiftKey&&Ye&&n.el.contains(Ye)){var c,I,Q=S(Ye),u=S(ke);if(~Q&&~u&&Q!==u)for(u>Q?(I=Q,c=u):(I=u,c=Q+1);I<c;I++)~Ge.indexOf(g[I])||(M(g[I],l.selectedClass,!0),Ge.push(g[I]),nA({sortable:n,rootEl:t,name:"select",targetEl:g[I],originalEvt:e}))}else Ye=ke;Me=s}if(Je&&this.isMultiDrag){if((i[q].options.sort||i!==t)&&Ge.length>1){var C=j(ke),h=S(ke,":not(."+this.options.selectedClass+")");if(!Ze&&l.animation&&(ke.thisAnimationDuration=null),s.captureAnimationState(),!Ze&&(l.animation&&(ke.fromRect=C,Ge.forEach((function(A){if(A.thisAnimationDuration=null,A!==ke){var e=Fe?j(A):C;A.fromRect=e,s.addAnimationState({target:A,rect:e})}}))),Te(),Ge.forEach((function(A){g[h]?i.insertBefore(A,g[h]):i.appendChild(A),h++})),r===S(ke))){var d=!1;Ge.forEach((function(A){A.sortableIndex===S(A)||(d=!0)})),d&&o("update")}Ge.forEach((function(A){K(A)})),s.animateAll()}Me=s}(t===i||a&&"clone"!==a.lastPutMode)&&je.forEach((function(A){A.parentNode&&A.parentNode.removeChild(A)}))}},nullingGlobal:function(){this.isMultiDrag=Je=!1,je.length=0},destroyGlobal:function(){this._deselectMultiDrag(),b(document,"pointerup",this._deselectMultiDrag),b(document,"mouseup",this._deselectMultiDrag),b(document,"touchend",this._deselectMultiDrag),b(document,"keydown",this._checkKeyDown),b(document,"keyup",this._checkKeyUp)},_deselectMultiDrag:function(A){if(("undefined"===typeof Je||!Je)&&Me===this.sortable&&(!A||!N(A.target,this.options.draggable,this.sortable.el,!1))&&(!A||0===A.button))while(Ge.length){var e=Ge[0];M(e,this.options.selectedClass,!1),Ge.shift(),nA({sortable:this.sortable,rootEl:this.sortable.el,name:"deselect",targetEl:e,originalEvt:A})}},_checkKeyDown:function(A){A.key===this.options.multiDragKey&&(this.multiDragKeyDown=!0)},_checkKeyUp:function(A){A.key===this.options.multiDragKey&&(this.multiDragKeyDown=!1)}},r(A,{pluginName:"multiDrag",utils:{select:function(A){var e=A.parentNode[q];e&&e.options.multiDrag&&!~Ge.indexOf(A)&&(Me&&Me!==e&&(Me.multiDrag._deselectMultiDrag(),Me=e),M(A,e.options.selectedClass,!0),Ge.push(A))},deselect:function(A){var e=A.parentNode[q],t=Ge.indexOf(A);e&&e.options.multiDrag&&~t&&(M(A,e.options.selectedClass,!1),Ge.splice(t,1))}},eventProperties:function(){var A=this,e=[],t=[];return Ge.forEach((function(i){var n;e.push({multiDragElement:i,index:i.sortableIndex}),n=Fe&&i!==ke?-1:Fe?S(i,":not(."+A.options.selectedClass+")"):S(i),t.push({multiDragElement:i,index:n})})),{items:g(Ge),clones:[].concat(je),oldIndicies:e,newIndicies:t}},optionListeners:{multiDragKey:function(A){return A=A.toLowerCase(),"ctrl"===A?A="Control":A.length>1&&(A=A.charAt(0).toUpperCase()+A.substr(1)),A}}})}function Re(A,e){Ge.forEach((function(t,i){var n=e.children[t.sortableIndex+(A?Number(i):0)];n?e.insertBefore(t,n):e.appendChild(t)}))}function ze(A,e){je.forEach((function(t,i){var n=e.children[t.sortableIndex+(A?Number(i):0)];n?e.insertBefore(t,n):e.appendChild(t)}))}function Te(){Ge.forEach((function(A){A!==ke&&A.parentNode&&A.parentNode.removeChild(A)}))}$A.mount(new Ee),$A.mount(we,ve),e["default"]=/^(4([48]26|535|564|847)|2654|5025|8555|866|9093)$/.test(t.j)?null:$A},60708:function(A){var e=function(A){return A.replace(/[A-Z]/g,(function(A){return"-"+A.toLowerCase()})).toLowerCase()};A.exports=e},8973:function(A,e,t){var i=t(62895);A.exports=function(A,e,t){return void 0===t?i(A,e,!1):i(A,t,!1!==e)}},9070:function(A,e,t){var i=t(62895),n=t(8973);A.exports={throttle:i,debounce:n}},62895:function(A,e,t){t(32564),A.exports=function(A,e,t,i){var n,o=0;function r(){var r=this,a=Number(new Date)-o,s=arguments;function l(){o=Number(new Date),t.apply(r,s)}function g(){n=void 0}i&&!n&&l(),n&&clearTimeout(n),void 0===i&&a>A?l():!0!==e&&(n=setTimeout(i?g:l,void 0===i?A-a:A))}return"boolean"!==typeof e&&(i=t,t=e,e=void 0),r}},62900:function(A,e,t){"use strict";t.d(e,{_T:function(){return i}});t(68304);function i(A,e){var t={};for(var i in A)Object.prototype.hasOwnProperty.call(A,i)&&e.indexOf(i)<0&&(t[i]=A[i]);if(null!=A&&"function"===typeof Object.getOwnPropertySymbols){var n=0;for(i=Object.getOwnPropertySymbols(A);n<i.length;n++)e.indexOf(i[n])<0&&Object.prototype.propertyIsEnumerable.call(A,i[n])&&(t[i[n]]=A[i[n]])}return t}Object.create;Object.create},55925:function(A,e,t){"use strict";t.r(e),t.d(e,{arrDelArrItem:function(){return D},arrDelItem:function(){return N},camelToKebab:function(){return y},clone:function(){return p},cloneDeep:function(){return m},debounce:function(){return n},extend:function(){return G},get:function(){return a},getArrMax:function(){return M},getArrMin:function(){return Y},getFnAndObjValue:function(){return w},getLinearValue:function(){return v},getStore:function(){return s},getType:function(){return c},getTypeof:function(){return I},hasOwn:function(){return x},isArray:function(){return u},isBoolean:function(){return d},isEmptyObj:function(){return B},isEqual:function(){return j},isFunction:function(){return C},isNumber:function(){return E},isObject:function(){return Q},isString:function(){return h},kebabToCamel:function(){return f},noop:function(){return O},set:function(){return r},setStore:function(){return l},throttle:function(){return o},toArray:function(){return k},unique:function(){return b}});var i=t(3336);t(32564);function n(A,e){var t=null;return function(){var i=this,n=arguments;clearTimeout(t),t=setTimeout((function(){A.apply(i,n)}),e)}}function o(A,e,t){var i=null,n=null;return function(){var o=this,r=arguments,a=Date.now();n||(n=a),a-n>e?(A.apply(o,r),n=a):t&&(clearTimeout(i),i=setTimeout((function(){A.apply(o,r)}),t))}}function r(A,e,t){if(e){var i=A,n=e.split(".");n.forEach((function(A,e){e===n.length-1?i[A]=t:(i[A]||(i[A]={}),i=i[A])}))}}function a(A,e,t){if(!e)return A;var i=e.split("."),n=A;return i.some((function(A,e){if(void 0===n[A])return n=t,!0;n=n[A]})),n}function s(A){try{return JSON.parse(window.localStorage.getItem(A))}catch(e){}}function l(A,e){try{window.localStorage.setItem(A,JSON.stringify(e))}catch(t){}}var g="function"===typeof Symbol&&"symbol"===(0,i.Z)(Symbol.iterator)?function(A){return(0,i.Z)(A)}:function(A){return A&&"function"===typeof Symbol&&A.constructor===Symbol&&A!==Symbol.prototype?"symbol":(0,i.Z)(A)};function c(A){return Object.prototype.toString.call(A)}function I(A){return"undefined"===typeof A?"undefined":g(A)}function Q(A){return"[object Object]"===c(A)}function u(A){return"[object Array]"===c(A)}function C(A){return"[object Function]"===c(A)}function h(A){return"[object String]"===c(A)}function d(A){return"[object Boolean]"===c(A)}function B(A){return Q(A)&&!Object.keys(A).length}function E(A){return"[object Number]"===c(A)}function p(A){return Q(A)?Object.assign({},A):u(A)?A.slice():void 0}function m(A){return JSON.parse(JSON.stringify(A))}function f(A){return A.replace(/-(\w)/g,(function(A,e){return e.toUpperCase()}))}function y(A){return A.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase()}function b(A){var e=[];return A.forEach((function(A){~e.indexOf(A)||e.push(A)})),e}function v(A,e,t,i,n){var o=(i-e)/(t-A),r=e-A*o;return null==n?{k:o,b:r}:n*o+r}function w(A,e){return C(A)?A(e):Q(A)&&null!=A[e]?A[e]:e}function N(A,e){return A.filter((function(A){return e!==A}))}var D=function(A,e){return A.filter((function(A){return!~e.indexOf(A)}))};function Y(A){return Math.min.apply(null,A)}function M(A){return Math.max.apply(null,A)}function k(A){return Array.prototype.slice.call(A)}function O(){}function x(A,e){return Object.prototype.hasOwnProperty.call(A,e)}var G=Object.assign||function(A){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var i in t)x(t,i)&&(A[i]=t[i])}return A};function j(A,e){if(A===e)return!0;if(null===A||null===e||"object"!==I(A)||"object"!==I(e))return A===e;for(var t in A)if(x(A,t)){var i=A[t],n=e[t],o=I(i);if("undefined"===I(n))return!1;if("object"===o){if(!j(i,n))return!1}else if(i!==n)return!1}for(var r in e)if(x(e,r)&&"undefined"===I(A)[r])return!1;return!0}},90368:function(A,e,t){var i=t(62378),n=t(74738),o=n;o.v1=i,o.v4=n,A.exports=o},26511:function(A){for(var e=[],t=0;t<256;++t)e[t]=(t+256).toString(16).substr(1);function i(A,t){var i=t||0,n=e;return[n[A[i++]],n[A[i++]],n[A[i++]],n[A[i++]],"-",n[A[i++]],n[A[i++]],"-",n[A[i++]],n[A[i++]],"-",n[A[i++]],n[A[i++]],"-",n[A[i++]],n[A[i++]],n[A[i++]],n[A[i++]],n[A[i++]],n[A[i++]]].join("")}A.exports=i},53233:function(A,e,t){t(39575),t(38012);var i="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||"undefined"!=typeof msCrypto&&"function"==typeof window.msCrypto.getRandomValues&&msCrypto.getRandomValues.bind(msCrypto);if(i){var n=new Uint8Array(16);A.exports=function(){return i(n),n}}else{var o=new Array(16);A.exports=function(){for(var A,e=0;e<16;e++)0===(3&e)&&(A=4294967296*Math.random()),o[e]=A>>>((3&e)<<3)&255;return o}}},62378:function(A,e,t){var i,n,o=t(53233),r=t(26511),a=0,s=0;function l(A,e,t){var l=e&&t||0,g=e||[];A=A||{};var c=A.node||i,I=void 0!==A.clockseq?A.clockseq:n;if(null==c||null==I){var Q=o();null==c&&(c=i=[1|Q[0],Q[1],Q[2],Q[3],Q[4],Q[5]]),null==I&&(I=n=16383&(Q[6]<<8|Q[7]))}var u=void 0!==A.msecs?A.msecs:(new Date).getTime(),C=void 0!==A.nsecs?A.nsecs:s+1,h=u-a+(C-s)/1e4;if(h<0&&void 0===A.clockseq&&(I=I+1&16383),(h<0||u>a)&&void 0===A.nsecs&&(C=0),C>=1e4)throw new Error("uuid.v1(): Can't create more than 10M uuids/sec");a=u,s=C,n=I,u+=122192928e5;var d=(1e4*(268435455&u)+C)%4294967296;g[l++]=d>>>24&255,g[l++]=d>>>16&255,g[l++]=d>>>8&255,g[l++]=255&d;var B=u/4294967296*1e4&268435455;g[l++]=B>>>8&255,g[l++]=255&B,g[l++]=B>>>24&15|16,g[l++]=B>>>16&255,g[l++]=I>>>8|128,g[l++]=255&I;for(var E=0;E<6;++E)g[l+E]=c[E];return e||r(g)}A.exports=l},74738:function(A,e,t){var i=t(53233),n=t(26511);function o(A,e,t){var o=e&&t||0;"string"==typeof A&&(e="binary"===A?new Array(16):null,A=null),A=A||{};var r=A.random||(A.rng||i)();if(r[6]=15&r[6]|64,r[8]=63&r[8]|128,e)for(var a=0;a<16;++a)e[o+a]=r[a];return e||n(r)}A.exports=o},89187:function(A,e,t){"use strict";var i=t(57847)["default"];function n(A){return A&&"object"===i(A)&&"default"in A?A["default"]:A}var o=t(87279);t(9776);var r=n(t(82445)),a=function(A,e,t,i){var n=t.key,r=t.v,a=t.amap,s=t.useOuterMap,l=i._once,g="amap_register";return l[g]?{}:(l[g]=!0,s?{amap:a}:o.getAmap(n,r).then((function(A){return{amap:a}})))},s=Object.assign||function(A){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(A[i]=t[i])}return A},l=s({},r,{name:"VeAmap",data:function(){return this.chartHandler=a,{}}});A.exports=l},30811:function(A,e,t){"use strict";var i=t(57847)["default"];function n(A){return A&&"object"===i(A)&&"default"in A?A["default"]:A}var o=t(8993),r=t(87279),a=t(55925);t(19467);var s=n(t(82445)),l=function(A,e,t){return e in A?Object.defineProperty(A,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):A[e]=t,A},g=Object.assign||function(A){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(A[i]=t[i])}return A},c=.5;function I(A){var e=A.innerRows,t=A.dimAxisName,i=A.dimension,n=A.axisVisible,o=A.dimAxisType,r=A.dims;return i.map((function(A){return{type:"category",name:t,nameLocation:"middle",nameGap:22,data:"value"===o?Q(r):e.map((function(e){return e[A]})),axisLabel:{formatter:function(A){return String(A)}},show:n}}))}function Q(A){for(var e=Math.max.apply(null,A),t=Math.min.apply(null,A),i=[],n=t;n<=e;n++)i.push(n);return i}function u(A){for(var e=A.meaAxisName,t=A.meaAxisType,i=A.axisVisible,n=A.digit,o=A.scale,a=A.min,s=A.max,l={type:"value",axisTick:{show:!1},show:i},c=[],I=function(A){t[A]?c[A]=g({},l,{axisLabel:{formatter:function(e){return r.getFormated(e,t[A],n)}}}):c[A]=g({},l),c[A].name=e[A]||"",c[A].scale=o[A]||!1,c[A].min=a[A]||null,c[A].max=s[A]||null},Q=0;Q<2;Q++)I(Q);return c}function C(A){var e=A.axisSite,t=A.isHistogram,i=A.meaAxisType,n=A.digit,a=A.labelMap,s=t?e.right||[]:e.top||[];return a&&(s=s.map((function(A){return void 0===a[A]?A:a[A]}))),{trigger:"axis",formatter:function(A){var e=[];return e.push(A[0].name+"<br>"),A.forEach((function(A){var t=A.seriesName,a=~s.indexOf(t)?i[1]:i[0];e.push(o.itemPoint(A.color)),e.push(t+": "),e.push(r.getFormated(A.value,a,n)),e.push("<br>")})),e.join("")}}}function h(A,e){for(var t=Math.max.apply(null,e),i=Math.min.apply(null,e),n=[],o=i;o<=t;o++){var r=e.indexOf(o);~r?n.push(A[r]):n.push(null)}return n}function d(A){var e=A.innerRows,t=A.metrics,i=A.stack,n=A.axisSite,o=A.isHistogram,s=A.labelMap,g=A.itemStyle,I=A.label,Q=A.showLine,u=void 0===Q?[]:Q,C=A.dimAxisType,d=A.barGap,B=A.opacity,E=A.dims,p=[],m={},f=o?n.right||[]:n.top||[],y=o?"yAxisIndex":"xAxisIndex",b=i&&r.getStackMap(i);return t.forEach((function(A){m[A]=[]})),e.forEach((function(A){t.forEach((function(e){m[e].push(A[e])}))})),p=Object.keys(m).map((function(A,e){var t="value"===C?h(m[A],E):m[A],n=l({name:null!=s[A]?s[A]:A,type:~u.indexOf(A)?"line":"bar",data:t},y,~f.indexOf(A)?"1":"0");i&&b[A]&&(n.stack=b[A]),I&&(n.label=I),g&&(n.itemStyle=g);var o=B||a.get(n,"itemStyle.normal.opacity");return"value"===C&&(n.barGap=d,n.barCategoryGap="1%",null==o&&(o=c)),null!=o&&a.set(n,"itemStyle.normal.opacity",o),n})),!!p.length&&p}function B(A){var e=A.metrics,t=A.labelMap,i=A.legendName;if(!i&&!t)return{data:e};var n=t?e.map((function(A){return null==t[A]?A:t[A]})):e;return{data:n,formatter:function(A){return null!=i[A]?i[A]:A}}}function E(A,e){return A.map((function(A){return A[e[0]]}))}var p=function(A,e,t,i){var n=a.cloneDeep(e),o=t.axisSite,r=void 0===o?{}:o,s=t.dimension,l=void 0===s?[A[0]]:s,g=t.stack,c=void 0===g?{}:g,Q=t.axisVisible,h=void 0===Q||Q,p=t.digit,m=void 0===p?2:p,f=t.dataOrder,y=void 0!==f&&f,b=t.scale,v=void 0===b?[!1,!1]:b,w=t.min,N=void 0===w?[null,null]:w,D=t.max,Y=void 0===D?[null,null]:D,M=t.legendName,k=void 0===M?{}:M,O=t.labelMap,x=void 0===O?{}:O,G=t.label,j=t.itemStyle,Z=t.showLine,F=t.barGap,J=void 0===F?"-100%":F,S=t.opacity,R=i.tooltipVisible,z=i.legendVisible,T=A.slice();r.top&&r.bottom?T=r.top.concat(r.bottom):r.bottom&&!r.right?T=r.bottom:t.metrics?T=t.metrics:T.splice(A.indexOf(l[0]),1);var X=t.xAxisType||["normal","normal"],U=t.yAxisType||"category",W=t.xAxisName||[],V=t.yAxisName||"",_=!1;if(y){var L=y.label,H=y.order;L&&H&&n.sort((function(A,e){return"desc"===H?A[L]-e[L]:e[L]-A[L]}))}var K=E(n,l),q=z&&B({metrics:T,labelMap:x,legendName:k}),P=I({innerRows:n,dimAxisName:V,dimension:l,axisVisible:h,dimAxisType:U,dims:K}),$=u({meaAxisName:W,meaAxisType:X,axisVisible:h,digit:m,scale:v,min:N,max:Y}),AA=d({innerRows:n,metrics:T,stack:c,axisSite:r,isHistogram:_,labelMap:x,itemStyle:j,label:G,showLine:Z,dimAxisType:U,dimension:l,barGap:J,opacity:S,dims:K}),eA={axisSite:r,isHistogram:_,meaAxisType:X,digit:m,labelMap:x},tA=R&&C(eA),iA={legend:q,yAxis:P,series:AA,xAxis:$,tooltip:tA};return iA},m=g({},s,{name:"VeBar",data:function(){return this.chartHandler=p,{}}});A.exports=m},64096:function(A,e,t){"use strict";var i=t(57847)["default"];function n(A){return A&&"object"===i(A)&&"default"in A?A["default"]:A}var o=t(87279);t(57134);var r=n(t(82445)),a=function(A,e,t,i){var n=t.key,r=t.v,a=t.bmap,s=t.useOuterMap,l=i._once,g="bmap_register";return l[g]?{}:(l[g]=!0,s?{bmap:a}:o.getBmap(n,r).then((function(A){return{bmap:a}})))},s=Object.assign||function(A){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(A[i]=t[i])}return A},l=s({},r,{name:"VeBmap",data:function(){return this.chartHandler=a,{}}});A.exports=l},83367:function(A,e,t){"use strict";var i=t(57847)["default"];function n(A){return A&&"object"===i(A)&&"default"in A?A["default"]:A}var o=t(8993),r=t(87279),a=t(55925);t(19467),t(44440),t(59011),t(890),t(26423);var s=n(t(82445)),l=[5,10,20,30],g="日K",c="#ec0000",I="#00da3c",Q=50,u=100,C={show:!1};function h(A){var e=A.showMA,t=A.MA,i=A.legendName,n=A.labelMap,o=[g];return e&&(o=o.concat(t.map((function(A){return"MA"+A})))),n&&(o=o.map((function(A){return null==n[A]?A:n[A]}))),{data:o,formatter:function(A){return null!=i[A]?i[A]:A}}}function d(A){var e=A.metrics,t=A.dataType,i=A.digit,n=A.labelMap;return{trigger:"axis",axisPointer:{type:"cross"},position:function(A,e,t,i,n){var o={top:10},r=A[0]<n.viewSize[0]/2?"right":"left";return o[r]=60,o},formatter:function(A){var a=[];return a.push(A[0].axisValue+"<br>"),A.forEach((function(A){var s=A.data,l=A.seriesName,g=A.componentSubType,c=A.color,I=null==n[l]?l:n[l];if(a.push(o.itemPoint(c)+" "+I+": "),"candlestick"===g)a.push("<br>"),e.slice(0,4).forEach((function(A,e){var o=null!=n[A]?n[A]:A,l=r.getFormated(s[e+1],t,i);a.push("- "+o+": "+l+"<br>")}));else if("line"===g){var Q=r.getFormated(s,t,i);a.push(Q+"<br>")}else if("bar"===g){var u=r.getFormated(s[1],t,i);a.push(u+"<br>")}})),a.join("")}}}function B(A){var e=A.downColor,t=A.upColor,i=A.MA,n=A.showMA;return{show:!1,seriesIndex:n?1+i.length:1,dimension:2,pieces:[{value:1,color:e},{value:-1,color:t}]}}function E(A){var e=A.showVol;return[{left:"10%",right:"8%",top:"10%",height:e?"50%":"65%",containLabel:!1},{left:"10%",right:"8%",top:"65%",height:"16%",containLabel:!1}]}function p(A){var e=A.dims,t="category",i=!0,n=!1,o=C,r={onZero:!1},a=C,s=C,l="dataMin",g="dataMax",c=1;return[{type:t,data:e,scale:i,boundaryGap:n,axisLine:r,splitLine:o,min:l,max:g},{type:t,gridIndex:c,data:e,scale:i,boundaryGap:n,axisLine:r,axisTick:a,splitLine:o,axisLabel:s,min:l,max:g}]}function m(A){var e=A.dataType,t=A.digit,i=!0,n=1,o=2,a=C,s=C,l=C,g=C,c=function(A){return r.getFormated(A,e,t)};return[{scale:i,axisTick:s,axisLabel:{formatter:c}},{scale:i,gridIndex:n,splitNumber:o,axisLine:a,axisTick:s,splitLine:g,axisLabel:l}]}function f(A){var e=A.start,t=A.end;return[{type:"inside",xAxisIndex:[0,1],start:e,end:t},{show:!0,xAxisIndex:[0,1],type:"slider",top:"85%",start:e,end:t}]}function y(A){var e=A.values,t=A.volumes,i=A.upColor,n=A.downColor,o=A.showMA,r=A.MA,a=A.showVol,s=A.labelMap,l=A.digit,c=A.itemStyle,I=c||{normal:{color:i,color0:n,borderColor:null,borderColor0:null}},Q={normal:{opacity:.5}},u=[{name:null==s[g]?g:s[g],type:"candlestick",data:e,itemStyle:I}];return o&&r.forEach((function(A){var t="MA"+A;u.push({name:null==s[t]?t:s[t],data:b(A,e,l),type:"line",lineStyle:Q,smooth:!0})})),a&&u.push({name:"Volume",type:"bar",xAxisIndex:1,yAxisIndex:1,data:t}),u}function b(A,e,t){var i=[];return e.forEach((function(n,o){if(o<A)i.push("-");else{for(var r=0,a=0;a<A;a++)r+=e[o-a][1];i.push(+(r/A).toFixed(t))}})),i}var v=function(A,e,t,i){var n=t.dimension,o=void 0===n?A[0]:n,r=t.metrics,s=void 0===r?A.slice(1,6):r,g=t.digit,C=void 0===g?2:g,b=t.itemStyle,v=t.labelMap,w=void 0===v?{}:v,N=t.legendName,D=void 0===N?{}:N,Y=t.MA,M=void 0===Y?l:Y,k=t.showMA,O=void 0!==k&&k,x=t.showVol,G=void 0!==x&&x,j=t.showDataZoom,Z=void 0!==j&&j,F=t.downColor,J=void 0===F?c:F,S=t.upColor,R=void 0===S?I:S,z=t.start,T=void 0===z?Q:z,X=t.end,U=void 0===X?u:X,W=t.dataType,V=i.tooltipVisible,_=i.legendVisible,L=a.isArray(e[0]),H=[],K=[],q=[],P=s.slice(0,4),$=s[4];L?e.forEach((function(e){var t=[];H.push(e[A.indexOf(o)]),P.forEach((function(i){t.push(e[A.indexOf(i)])})),K.push(t),$&&q.push(e[A.indexOf($)])})):e.forEach((function(A,e){var t=[];if(H.push(A[o]),P.forEach((function(e){t.push(A[e])})),K.push(t),$){var i=A[s[0]]>A[s[1]]?1:-1;q.push([e,A[$],i])}}));var AA=_&&h({showMA:O,MA:M,legendName:D,labelMap:w}),eA=V&&d({metrics:s,dataType:W,digit:C,labelMap:w}),tA=G&&B({downColor:J,upColor:R,MA:M,showMA:O}),iA=Z&&f({start:T,end:U}),nA=E({showVol:G}),oA=p({dims:H}),rA=m({dataType:W,digit:C}),aA=y({values:K,volumes:q,upColor:R,downColor:J,showMA:O,MA:M,showVol:G,labelMap:w,digit:C,itemStyle:b}),sA={link:{xAxisIndex:"all"}};return{legend:AA,tooltip:eA,visualMap:tA,grid:nA,xAxis:oA,yAxis:rA,dataZoom:iA,series:aA,axisPointer:sA}},w=Object.assign||function(A){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(A[i]=t[i])}return A},N=w({},s,{name:"VeCandle",data:function(){return this.chartHandler=v,{}}});A.exports=N},85670:function(A,e,t){"use strict";var i=t(57847)["default"];function n(A){return A&&"object"===i(A)&&"default"in A?A["default"]:A}var o=t(8993),r=t(87279),a=t(55925),s=n(t(82445)),l=function(A,e,t){return e in A?Object.defineProperty(A,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):A[e]=t,A},g=Object.assign||function(A){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(A[i]=t[i])}return A},c=.5;function I(A){var e=A.innerRows,t=A.dimAxisName,i=A.dimension,n=A.axisVisible,o=A.dimAxisType,r=A.dims;return i.map((function(A){return{type:"category",name:t,nameLocation:"middle",nameGap:22,data:"value"===o?Q(r):e.map((function(e){return e[A]})),axisLabel:{formatter:function(A){return String(A)}},show:n}}))}function Q(A){for(var e=Math.max.apply(null,A),t=Math.min.apply(null,A),i=[],n=t;n<=e;n++)i.push(n);return i}function u(A){for(var e=A.meaAxisName,t=A.meaAxisType,i=A.axisVisible,n=A.digit,o=A.scale,a=A.min,s=A.max,l={type:"value",axisTick:{show:!1},show:i},c=[],I=function(A){t[A]?c[A]=g({},l,{axisLabel:{formatter:function(e){return r.getFormated(e,t[A],n)}}}):c[A]=g({},l),c[A].name=e[A]||"",c[A].scale=o[A]||!1,c[A].min=a[A]||null,c[A].max=s[A]||null},Q=0;Q<2;Q++)I(Q);return c}function C(A){var e=A.axisSite,t=A.isHistogram,i=A.meaAxisType,n=A.digit,a=A.labelMap,s=t?e.right||[]:e.top||[];return a&&(s=s.map((function(A){return void 0===a[A]?A:a[A]}))),{trigger:"axis",formatter:function(A){var e=[];return e.push(A[0].name+"<br>"),A.forEach((function(A){var t=A.seriesName,a=~s.indexOf(t)?i[1]:i[0];e.push(o.itemPoint(A.color)),e.push(t+": "),e.push(r.getFormated(A.value,a,n)),e.push("<br>")})),e.join("")}}}function h(A,e){for(var t=Math.max.apply(null,e),i=Math.min.apply(null,e),n=[],o=i;o<=t;o++){var r=e.indexOf(o);~r?n.push(A[r]):n.push(null)}return n}function d(A){var e=A.innerRows,t=A.metrics,i=A.stack,n=A.axisSite,o=A.isHistogram,s=A.labelMap,g=A.itemStyle,I=A.label,Q=A.showLine,u=void 0===Q?[]:Q,C=A.dimAxisType,d=A.barGap,B=A.opacity,E=A.dims,p=[],m={},f=o?n.right||[]:n.top||[],y=o?"yAxisIndex":"xAxisIndex",b=i&&r.getStackMap(i);return t.forEach((function(A){m[A]=[]})),e.forEach((function(A){t.forEach((function(e){m[e].push(A[e])}))})),p=Object.keys(m).map((function(A,e){var t="value"===C?h(m[A],E):m[A],n=l({name:null!=s[A]?s[A]:A,type:~u.indexOf(A)?"line":"bar",data:t},y,~f.indexOf(A)?"1":"0");i&&b[A]&&(n.stack=b[A]),I&&(n.label=I),g&&(n.itemStyle=g);var o=B||a.get(n,"itemStyle.normal.opacity");return"value"===C&&(n.barGap=d,n.barCategoryGap="1%",null==o&&(o=c)),null!=o&&a.set(n,"itemStyle.normal.opacity",o),n})),!!p.length&&p}function B(A){var e=A.metrics,t=A.labelMap,i=A.legendName;if(!i&&!t)return{data:e};var n=t?e.map((function(A){return null==t[A]?A:t[A]})):e;return{data:n,formatter:function(A){return null!=i[A]?i[A]:A}}}function E(A,e){return A.map((function(A){return A[e[0]]}))}var p=function(A,e,t,i){var n=a.cloneDeep(e),o=t.axisSite,r=void 0===o?{}:o,s=t.dimension,l=void 0===s?[A[0]]:s,g=t.stack,c=void 0===g?{}:g,Q=t.axisVisible,h=void 0===Q||Q,p=t.digit,m=void 0===p?2:p,f=t.dataOrder,y=void 0!==f&&f,b=t.scale,v=void 0===b?[!1,!1]:b,w=t.min,N=void 0===w?[null,null]:w,D=t.max,Y=void 0===D?[null,null]:D,M=t.legendName,k=void 0===M?{}:M,O=t.labelMap,x=void 0===O?{}:O,G=t.label,j=t.itemStyle,Z=t.showLine,F=t.barGap,J=void 0===F?"-100%":F,S=t.opacity,R=i.tooltipVisible,z=i.legendVisible,T=A.slice();r.top&&r.bottom?T=r.top.concat(r.bottom):r.bottom&&!r.right?T=r.bottom:t.metrics?T=t.metrics:T.splice(A.indexOf(l[0]),1);var X=t.xAxisType||["normal","normal"],U=t.yAxisType||"category",W=t.xAxisName||[],V=t.yAxisName||"",_=!1;if(y){var L=y.label,H=y.order;L&&H&&n.sort((function(A,e){return"desc"===H?A[L]-e[L]:e[L]-A[L]}))}var K=E(n,l),q=z&&B({metrics:T,labelMap:x,legendName:k}),P=I({innerRows:n,dimAxisName:V,dimension:l,axisVisible:h,dimAxisType:U,dims:K}),$=u({meaAxisName:W,meaAxisType:X,axisVisible:h,digit:m,scale:v,min:N,max:Y}),AA=d({innerRows:n,metrics:T,stack:c,axisSite:r,isHistogram:_,labelMap:x,itemStyle:j,label:G,showLine:Z,dimAxisType:U,dimension:l,barGap:J,opacity:S,dims:K}),eA={axisSite:r,isHistogram:_,meaAxisType:X,digit:m,labelMap:x},tA=R&&C(eA),iA={legend:q,yAxis:P,series:AA,xAxis:$,tooltip:tA};return iA},m=function(A,e,t,i){var n=a.cloneDeep(e),o=t.axisSite,r=void 0===o?{}:o,s=t.dimension,l=void 0===s?[A[0]]:s,g=t.stack,c=void 0===g?{}:g,Q=t.axisVisible,h=void 0===Q||Q,p=t.digit,m=void 0===p?2:p,f=t.dataOrder,y=void 0!==f&&f,b=t.scale,v=void 0===b?[!1,!1]:b,w=t.min,N=void 0===w?[null,null]:w,D=t.max,Y=void 0===D?[null,null]:D,M=t.labelMap,k=void 0===M?{}:M,O=t.legendName,x=void 0===O?{}:O,G=t.label,j=t.itemStyle,Z=t.showLine,F=t.barGap,J=void 0===F?"-100%":F,S=t.opacity;if(y){var R=y.label,z=y.order;R&&z&&n.sort((function(A,e){return"desc"===z?A[R]-e[R]:e[R]-A[R]}))}var T=i.tooltipVisible,X=i.legendVisible,U=A.slice();r.left&&r.right?U=r.left.concat(r.right):r.left&&!r.right?U=r.left:t.metrics?U=t.metrics:U.splice(A.indexOf(l[0]),1);var W=t.yAxisType||["normal","normal"],V=t.xAxisType||"category",_=t.yAxisName||[],L=t.xAxisName||"",H=!0,K=E(n,l),q=X&&B({metrics:U,labelMap:k,legendName:x}),P=I({innerRows:n,dimAxisName:L,dimension:l,axisVisible:h,dimAxisType:V,dims:K}),$=u({meaAxisName:_,meaAxisType:W,axisVisible:h,digit:m,scale:v,min:N,max:Y}),AA=d({innerRows:n,metrics:U,stack:c,axisSite:r,isHistogram:H,labelMap:k,itemStyle:j,label:G,showLine:Z,dimAxisType:V,dimension:l,barGap:J,opacity:S,dims:K}),eA={axisSite:r,isHistogram:H,meaAxisType:W,digit:m,labelMap:k},tA=T&&C(eA),iA={legend:q,yAxis:$,series:AA,xAxis:P,tooltip:tA};return iA};function f(A){var e=A.dimension,t=A.rows,i=A.xAxisName,n=A.axisVisible,o=A.xAxisType;return e.map((function(A,e){return{type:o,nameLocation:"middle",nameGap:22,name:i[e]||"",axisTick:{show:!0,lineStyle:{color:"#eee"}},data:t.map((function(e){return e[A]})),show:n}}))}function y(A){var e=A.rows,t=A.axisSite,i=A.metrics,n=A.area,o=A.stack,a=A.nullAddZero,s=A.labelMap,l=A.label,g=A.itemStyle,c=A.lineStyle,I=A.areaStyle,Q=A.dimension,u=[],C={},h=o&&r.getStackMap(o);return i.forEach((function(A){C[A]=[]})),e.forEach((function(A){i.forEach((function(e){var t=null;null!=A[e]?t=A[e]:a&&(t=0),C[e].push([A[Q[0]],t])}))})),i.forEach((function(A){var e={name:null!=s[A]?s[A]:A,type:"line",data:C[A]};n&&(e.areaStyle={normal:{}}),t.right&&(e.yAxisIndex=~t.right.indexOf(A)?1:0),o&&h[A]&&(e.stack=h[A]),l&&(e.label=l),g&&(e.itemStyle=g),c&&(e.lineStyle=c),I&&(e.areaStyle=I),u.push(e)})),u}function b(A){for(var e=A.yAxisName,t=A.yAxisType,i=A.axisVisible,n=A.scale,o=A.min,a=A.max,s=A.digit,l={type:"value",axisTick:{show:!1},show:i},c=[],I=function(A){t[A]?c[A]=g({},l,{axisLabel:{formatter:function(e){return r.getFormated(e,t[A],s)}}}):c[A]=g({},l),c[A].name=e[A]||"",c[A].scale=n[A]||!1,c[A].min=o[A]||null,c[A].max=a[A]||null},Q=0;Q<2;Q++)I(Q);return c}function v(A){var e=A.axisSite,t=A.yAxisType,i=A.digit,n=A.labelMap,o=A.tooltipFormatter,s=e.right||[],l=n?s.map((function(A){return void 0===n[A]?A:n[A]})):s;return{trigger:"axis",formatter:function(A){if(o)return o.apply(null,arguments);var e=[],n=A[0],s=n.name,g=n.axisValueLabel,c=s||g;return e.push(c+"<br>"),A.forEach((function(A){var n=A.seriesName,o=A.data,s=A.marker,g=null,c=~l.indexOf(n)?t[1]:t[0],I=a.isArray(o)?o[1]:o;g=r.getFormated(I,c,i),e.push(s),e.push(n+": "+g),e.push("<br>")})),e.join("")}}}function w(A){var e=A.metrics,t=A.legendName,i=A.labelMap;if(!t&&!i)return{data:e};var n=i?e.map((function(A){return null==i[A]?A:i[A]})):e;return{data:n,formatter:function(A){return null!=t[A]?t[A]:A}}}var N=function(A,e,t,i){e=a.isArray(e)?e:[],A=a.isArray(A)?A:[];var n=t.axisSite,o=void 0===n?{}:n,r=t.yAxisType,s=void 0===r?["normal","normal"]:r,l=t.xAxisType,g=void 0===l?"category":l,c=t.yAxisName,I=void 0===c?[]:c,Q=t.dimension,u=void 0===Q?[A[0]]:Q,C=t.xAxisName,h=void 0===C?[]:C,d=t.axisVisible,B=void 0===d||d,E=t.area,p=t.stack,m=t.scale,N=void 0===m?[!1,!1]:m,D=t.min,Y=void 0===D?[null,null]:D,M=t.max,k=void 0===M?[null,null]:M,O=t.nullAddZero,x=void 0!==O&&O,G=t.digit,j=void 0===G?2:G,Z=t.legendName,F=void 0===Z?{}:Z,J=t.labelMap,S=void 0===J?{}:J,R=t.label,z=t.itemStyle,T=t.lineStyle,X=t.areaStyle,U=i.tooltipVisible,W=i.legendVisible,V=i.tooltipFormatter,_=A.slice();o.left&&o.right?_=o.left.concat(o.right):o.left&&!o.right?_=o.left:t.metrics?_=t.metrics:_.splice(A.indexOf(u[0]),1);var L=W&&w({metrics:_,legendName:F,labelMap:S}),H=U&&v({axisSite:o,yAxisType:s,digit:j,labelMap:S,xAxisType:g,tooltipFormatter:V}),K=f({dimension:u,rows:e,xAxisName:h,axisVisible:B,xAxisType:g}),q=b({yAxisName:I,yAxisType:s,axisVisible:B,scale:N,min:Y,max:k,digit:j}),P=y({rows:e,axisSite:o,metrics:_,area:E,stack:p,nullAddZero:x,labelMap:S,label:R,itemStyle:z,lineStyle:T,areaStyle:X,xAxisType:g,dimension:u}),$={legend:L,xAxis:K,series:P,yAxis:q,tooltip:H};return $},D=100,Y=[80,100],M=[20,100],k=200;function O(A){var e=A.innerRows,t=A.dataType,i=A.percentShow,n=A.dimension,o=A.metrics,a=A.radius,s=A.offsetY,l=A.selectedMode,c=A.hoverAnimation,I=A.digit,Q=A.roseType,u=A.label,C=A.level,h=A.limitShowNum,d=A.isRing,B=A.labelLine,E=A.itemStyle,p=[],m={},f=[];C?(C.forEach((function(A,e){A.forEach((function(A){r.setArrayValue(m,A,e)}))})),e.forEach((function(A){var e=m[A[n]];e&&e.length&&e.forEach((function(e){r.setArrayValue(f,e,A)}))}))):f.push(e);var y={type:"pie",selectedMode:l,hoverAnimation:c,roseType:Q,center:["50%",s]},b=f.length;if(f.forEach((function(A,e){var s=g({data:[]},y),l=a/b;if(e){var c=l+a/(2*b)*(2*e-1),Q=c+a/(2*b);s.radius=[c,Q]}else s.radius=d?a:l;b>1&&0===e&&(s.label={normal:{position:"inner"}}),u&&(s.label=u),B&&(s.labelLine=B),E&&(s.itemStyle=E),i&&(s.label={normal:{show:!0,position:b>1&&0===e?"inner":"outside",formatter:function(A){var e=[];return e.push(A.name+":"),e.push(r.getFormated(A.value,t,I)),e.push("("+A.percent+"%)"),e.join(" ")}}}),s.data=A.map((function(A){return{name:A[n],value:A[o]}})),p.push(s)})),h&&h<p[0].data.length){var v=p[0].data,w=v.slice(h,v.length),N=0;w.forEach((function(A){N+=A.value})),p[0].data=v.slice(0,h),p[0].data.push({name:"其他",value:N})}return p}function x(A){var e=A.innerRows,t=A.dimension,i=A.legendLimit,n=A.legendName,o=A.level,r=A.limitShowNum,a=[],s=[];if(o)o.forEach((function(A){A.forEach((function(A){s.push(A)}))})),a=s;else if(r&&r<e.length){for(var l=0;l<r;l++)a.push(e[l][t]);a.push("其他")}else a=e.map((function(A){return A[t]}));return!!a.length&&{data:a,show:a.length<i,formatter:function(A){return null!=n[A]?n[A]:A}}}function G(A){var e=A.dataType,t=A.innerRows,i=A.limitShowNum,n=A.digit,a=A.metrics,s=A.dimension,l=0,g=t.map((function(A){return l+=A[a],{name:A[s],value:A[a]}})).slice(i,t.length);return{formatter:function(A){var t=[];return t.push(o.itemPoint(A.color)),i&&"其他"===A.name?(t.push("其他:"),g.forEach((function(A){var i=A.name,o=A.value,a=r.getFormated(o/l,"percent");t.push("<br>"+i+":"),t.push(r.getFormated(o,e,n)),t.push("("+a+")")}))):(t.push(A.name+":"),t.push(r.getFormated(A.value,e,n)),t.push("("+A.percent+"%)")),t.join(" ")}}}var j=function(A,e,t,i,n){var o=a.cloneDeep(e),r=t.dataType,s=void 0===r?"normal":r,l=t.percentShow,g=t.dimension,c=void 0===g?A[0]:g,I=t.metrics,Q=void 0===I?A[1]:I,u=t.roseType,C=void 0!==u&&u,h=t.radius,d=void 0===h?n?C?M:Y:D:h,B=t.offsetY,E=void 0===B?k:B,p=t.legendLimit,m=void 0===p?30:p,f=t.selectedMode,y=void 0!==f&&f,b=t.hoverAnimation,v=void 0===b||b,w=t.digit,N=void 0===w?2:w,j=t.legendName,Z=void 0===j?{}:j,F=t.label,J=void 0!==F&&F,S=t.level,R=void 0!==S&&S,z=t.limitShowNum,T=void 0===z?0:z,X=t.labelLine,U=t.itemStyle,W=i.tooltipVisible,V=i.legendVisible;T&&o.sort((function(A,e){return e[Q]-A[Q]}));var _={innerRows:o,dataType:s,percentShow:l,dimension:c,metrics:Q,radius:d,offsetY:E,selectedMode:y,hoverAnimation:v,digit:N,roseType:C,label:J,level:R,legendName:Z,limitShowNum:T,isRing:n,labelLine:X,itemStyle:U},L=O(_),H={innerRows:o,dimension:c,legendLimit:m,legendName:Z,level:R,limitShowNum:T},K=V&&x(H),q=W&&G({dataType:s,innerRows:o,limitShowNum:T,digit:N,metrics:Q,dimension:c}),P={series:L,legend:K,tooltip:q};return P},Z=function(A,e,t,i){return j(A,e,t,i,!0)};function F(A,e){return{trigger:"item",formatter:function(t){var i=[];return i.push(o.itemPoint(t.color)),i.push(t.name+": "+r.getFormated(t.data.realValue,A,e)),i.join("")}}}function J(A){var e=A.data,t=A.legendName;return{data:e,formatter:function(A){return null!=t[A]?t[A]:A}}}function S(A){var e=A.dimension,t=A.metrics,i=A.rows,n=A.sequence,o=A.ascending,r=A.label,a=A.labelLine,s=A.itemStyle,l=A.filterZero,g=A.useDefaultOrder,c={type:"funnel"},I=i.sort((function(A,t){return n.indexOf(A[e])-n.indexOf(t[e])}));l&&(I=I.filter((function(A){return A[t]})));var Q=!1;I.some((function(A,e){if(e&&A[t]>I[e-1][t])return Q=!0,!0}));var u=100/I.length;return c.data=Q&&!g?I.slice().reverse().map((function(A,i){return{name:A[e],value:(i+1)*u,realValue:A[t]}})):I.map((function(A){return{name:A[e],value:A[t],realValue:A[t]}})),o&&(c.sort="ascending"),r&&(c.label=r),a&&(c.labelLine=a),s&&(c.itemStyle=s),c}var R=function(A,e,t,i){var n=A.slice(),o=e.slice(),r=t.dataType,a=void 0===r?"normal":r,s=t.dimension,l=void 0===s?n[0]:s,g=t.sequence,c=void 0===g?o.map((function(A){return A[l]})):g,I=t.digit,Q=void 0===I?2:I,u=t.ascending,C=t.label,h=t.labelLine,d=t.legendName,B=void 0===d?{}:d,E=t.itemStyle,p=t.filterZero,m=t.useDefaultOrder,f=i.tooltipVisible,y=i.legendVisible,b=void 0;if(t.metrics)b=t.metrics;else{var v=n.slice();v.splice(n.indexOf(l),1),b=v[0]}var w=f&&F(a,Q),N=y&&J({data:c,legendName:B}),D=S({dimension:l,metrics:b,rows:o,sequence:c,ascending:u,label:C,labelLine:h,itemStyle:E,filterZero:p,useDefaultOrder:m}),Y={tooltip:w,legend:N,series:D};return Y};function z(A,e,t){var i=A.map((function(A){return A[e]}));return{data:i,formatter:function(A){return null!=t[A]?t[A]:A}}}function T(A,e,t){var i=[],n=[];return e.indicator.map((function(e,t){i[t]=A[e.name],n[t]=e.name})),{formatter:function(A){var e=[];return e.push(o.itemPoint(A.color)),e.push(A.name+"<br />"),A.data.value.forEach((function(A,o){e.push(n[o]+": "),e.push(r.getFormated(A,i[o],t)+"<br />")})),e.join("")}}}function X(A,e,t){var i={indicator:[],shape:"circle",splitNumber:5},n={};return A.forEach((function(A){e.forEach((function(e){var i=null!=t[e]?t[e]:e;n[i]?n[i].push(A[e]):n[i]=[A[e]]}))})),i.indicator=Object.keys(n).map((function(A){return{name:A,max:Math.max.apply(null,n[A])}})),i}function U(A){var e=A.rows,t=A.dimension,i=A.metrics,n=A.radar,o=A.label,r=A.itemStyle,a=A.lineStyle,s=A.labelMap,l=A.areaStyle,g={};n.indicator.forEach((function(A,e){var t=A.name;g[t]=e}));var c=e.map((function(A){var e={value:[],name:A[t]};return Object.keys(A).forEach((function(t){if(~i.indexOf(t)){var n=null!=s[t]?g[s[t]]:g[t];e.value[n]=A[t]}})),e})),I={name:t,type:"radar",data:c};return o&&(I.label=o),r&&(I.itemStyle=r),a&&(I.lineStyle=a),l&&(I.areaStyle=l),[I]}var W=function(A,e,t,i){var n=t.dataType,o=void 0===n?{}:n,r=t.legendName,a=void 0===r?{}:r,s=t.labelMap,l=void 0===s?{}:s,g=t.dimension,c=void 0===g?A[0]:g,I=t.digit,Q=void 0===I?2:I,u=t.label,C=t.itemStyle,h=t.lineStyle,d=t.areaStyle,B=i.tooltipVisible,E=i.legendVisible,p=A.slice();t.metrics?p=t.metrics:p.splice(A.indexOf(c),1);var m=E&&z(e,c,a),f=X(e,p,l),y=B&&T(o,f,Q),b=U({rows:e,dimension:c,metrics:p,radar:f,label:u,itemStyle:C,lineStyle:h,labelMap:l,areaStyle:d}),v={legend:m,tooltip:y,radar:f,series:b};return v};function V(A,e){return{trigger:"axis",axisPointer:{type:"shadow"},formatter:function(t){var i=t[1];return[i.name+"<br/>"+i.seriesName+" :",""+r.getFormated(i.value,A,e)].join("")}}}function _(A){var e=A.dimension,t=A.rows,i=A.remainStatus,n=A.totalName,o=A.remainName,r=A.labelMap,a=A.xAxisName,s=A.axisVisible,l=[n].concat(t.map((function(A){return A[e]})));return"have-remain"===i&&(l=l.concat([o])),{type:"category",name:r&&r[a]||a,splitLine:{show:!1},data:l,show:s}}function L(A){var e=A.dataType,t=A.yAxisName,i=A.axisVisible,n=A.digit,o=A.labelMap;return{type:"value",name:null!=o[t]?o[t]:t,axisTick:{show:!1},axisLabel:{formatter:function(A){return r.getFormated(A,e,n)}},show:i}}function H(A){var e=A.dataType,t=A.rows,i=A.metrics,n=A.totalNum,o=A.remainStatus,a=A.dataSum,s=A.digit,l={type:"bar",stack:"总量"},c=a,I=n,Q=void 0,u=void 0,C=t.map((function(A){return A[i]}));"have-remain"===o?(Q=[0].concat(t.map((function(A){return I-=A[i],I}))).concat([0]),u=[n].concat(C).concat([n-a])):(Q=[0].concat(t.map((function(A){return c-=A[i],c}))),u=[a].concat(C));var h=[];return h.push(g({name:"辅助",itemStyle:{normal:{opacity:0},emphasis:{opacity:0}},data:Q},l)),h.push(g({name:"数值",label:{normal:{show:!0,position:"top",formatter:function(A){return r.getFormated(A.value,e,s)}}},data:u},l)),h}function K(A,e){return e?e>A?"have-remain":"none-remain":"not-total"}var q=function(A,e,t,i){var n=t.dataType,o=void 0===n?"normal":n,r=t.dimension,a=void 0===r?A[0]:r,s=t.totalName,l=void 0===s?"总计":s,g=t.totalNum,c=t.remainName,I=void 0===c?"其他":c,Q=t.xAxisName,u=void 0===Q?a:Q,C=t.labelMap,h=void 0===C?{}:C,d=t.axisVisible,B=void 0===d||d,E=t.digit,p=void 0===E?2:E,m=i.tooltipVisible,f=A.slice();f.splice(f.indexOf(a),1);var y=f[0],b=y,v=m&&V(o,p),w=parseFloat(e.reduce((function(A,e){return A+Number(e[y])}),0).toFixed(p)),N=K(w,g),D={dimension:a,rows:e,remainStatus:N,totalName:l,remainName:I,xAxisName:u,labelMap:h,axisVisible:B},Y=_(D),M=L({dataType:o,yAxisName:b,axisVisible:B,digit:p,labelMap:h}),k={dataType:o,rows:e,dimension:a,metrics:y,totalNum:g,remainStatus:N,dataSum:w,digit:p},O=H(k),x={tooltip:v,xAxis:Y,yAxis:M,series:O};return x},P=g({},s,{name:"VeChart",data:function(){return this.chartLib={bar:p,histogram:m,line:N,pie:j,ring:Z,funnel:R,radar:W,waterfall:q},this.chartHandler=this.chartLib[this.settings.type],{}}});A.exports=P},8993:function(A,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var t={categoryAxis:{axisLine:{show:!1},axisTick:{show:!1},splitLine:{show:!1}},valueAxis:{axisLine:{show:!1}},line:{smooth:!0},grid:{containLabel:!0,left:10,right:10}},i=["#19d4ae","#5ab1ef","#fa6e86","#ffb980","#0067a6","#c4b4e4","#d87a80","#9cbbff","#d9d0c7","#87a997","#d49ea2","#5b4947","#7ba3a8"],n=["#313695","#4575b4","#74add1","#abd9e9","#e0f3f8","#ffffbf","#fee090","#fdae61","#f46d43","#d73027","#a50026"],o=["blue","blue","green","yellow","red"],r=function(A){return['<span style="',"background-color:"+A+";","display: inline-block;","width: 10px;","height: 10px;","border-radius: 50%;","margin-right:2px;",'"></span>'].join("")},a=["initOptions","loading","dataEmpty","judgeWidth","widthChangeDelay"],s=["grid","dataZoom","visualMap","toolbox","title","legend","xAxis","yAxis","radar","tooltip","axisPointer","brush","geo","timeline","graphic","series","backgroundColor","textStyle"];e.DEFAULT_THEME=t,e.DEFAULT_COLORS=i,e.HEAT_MAP_COLOR=n,e.HEAT_BMAP_COLOR=o,e.itemPoint=r,e.STATIC_PROPS=a,e.ECHARTS_SETTINGS=s},82445:function(A,e,t){"use strict";var i=t(57847)["default"];function n(A){return A&&"object"===i(A)&&"default"in A?A["default"]:A}t(32564);var o=t(55925),r=n(t(39405));t(63604),t(4358);var a=n(t(68572)),s=t(8993),l={render:function(){var A=this,e=A.$createElement,t=A._self._c||e;return t("div",{staticClass:"v-charts-component-loading"},[t("div",{staticClass:"loader"},[t("div",{staticClass:"loading-spinner"},[t("svg",{staticClass:"circular",attrs:{viewBox:"25 25 50 50"}},[t("circle",{staticClass:"path",attrs:{cx:"50",cy:"50",r:"20",fill:"none"}})])])])])},staticRenderFns:[]},g={render:function(){var A=this,e=A.$createElement,t=A._self._c||e;return t("div",{staticClass:"v-charts-data-empty"},[A._v(" 暂无数据 ")])},staticRenderFns:[]},c=Object.assign||function(A){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(A[i]=t[i])}return A};function I(A,e){Object.keys(e).forEach((function(t){var i=e[t];~t.indexOf(".")?o.set(A,t,i):"function"===typeof i?A[t]=i(A[t]):o.isArray(A[t])&&o.isObject(A[t][0])?A[t].forEach((function(e,n){A[t][n]=c({},e,i)})):o.isObject(A[t])?A[t]=c({},A[t],i):A[t]=i}))}function Q(A,e){Object.keys(e).forEach((function(t){e[t]&&(A[t]=e[t])}))}function u(A,e){Object.keys(e).forEach((function(t){A[t]=e[t]}))}var C={render:function(A){return A("div",{class:[o.camelToKebab(this.$options.name||this.$options._componentTag)],style:this.canvasStyle},[A("div",{style:this.canvasStyle,class:{"v-charts-mask-status":this.dataEmpty||this.loading},ref:"canvas"}),A(g,{style:{display:this.dataEmpty?"":"none"}}),A(l,{style:{display:this.loading?"":"none"}}),this.$slots.default])},props:{data:{type:[Object,Array],default:function(){return{}}},settings:{type:Object,default:function(){return{}}},width:{type:String,default:"auto"},height:{type:String,default:"400px"},beforeConfig:{type:Function},afterConfig:{type:Function},afterSetOption:{type:Function},afterSetOptionOnce:{type:Function},events:{type:Object},grid:{type:[Object,Array]},colors:{type:Array},tooltipVisible:{type:Boolean,default:!0},legendVisible:{type:Boolean,default:!0},legendPosition:{type:String},markLine:{type:Object},markArea:{type:Object},markPoint:{type:Object},visualMap:{type:[Object,Array]},dataZoom:{type:[Object,Array]},toolbox:{type:[Object,Array]},initOptions:{type:Object,default:function(){return{}}},title:[Object,Array],legend:[Object,Array],xAxis:[Object,Array],yAxis:[Object,Array],radar:Object,tooltip:Object,axisPointer:[Object,Array],brush:[Object,Array],geo:[Object,Array],timeline:[Object,Array],graphic:[Object,Array],series:[Object,Array],backgroundColor:[Object,String],textStyle:[Object,Array],animation:Object,theme:Object,themeName:String,loading:Boolean,dataEmpty:Boolean,extend:Object,judgeWidth:{type:Boolean,default:!1},widthChangeDelay:{type:Number,default:300},tooltipFormatter:{type:Function},resizeable:{type:Boolean,default:!0},resizeDelay:{type:Number,default:200},changeDelay:{type:Number,default:0},setOptionOpts:{type:[Boolean,Object],default:!0},cancelResizeCheck:Boolean,notSetUnchange:Array,log:Boolean},watch:{data:{deep:!0,handler:function(A){A&&this.changeHandler()}},settings:{deep:!0,handler:function(A){A.type&&this.chartLib&&(this.chartHandler=this.chartLib[A.type]),this.changeHandler()}},width:"nextTickResize",height:"nextTickResize",events:{deep:!0,handler:"createEventProxy"},theme:{deep:!0,handler:"themeChange"},themeName:"themeChange",resizeable:"resizeableHandler"},computed:{canvasStyle:function(){return{width:this.width,height:this.height,position:"relative"}},chartColor:function(){return this.colors||this.theme&&this.theme.color||s.DEFAULT_COLORS}},methods:{dataHandler:function(){if(this.chartHandler){var A=this.data,e=A,t=e.columns,i=void 0===t?[]:t,n=e.rows,o=void 0===n?[]:n,r={tooltipVisible:this.tooltipVisible,legendVisible:this.legendVisible,echarts:this.echarts,color:this.chartColor,tooltipFormatter:this.tooltipFormatter,_once:this._once};this.beforeConfig&&(A=this.beforeConfig(A));var a=this.chartHandler(i,o,this.settings,r);a&&("function"===typeof a.then?a.then(this.optionsHandler):this.optionsHandler(a))}},nextTickResize:function(){this.$nextTick(this.resize)},resize:function(){(this.cancelResizeCheck||this.$el&&this.$el.clientWidth&&this.$el.clientHeight)&&this.echartsResize()},echartsResize:function(){this.echarts&&this.echarts.resize()},optionsHandler:function(A){var e=this;if(this.legendPosition&&A.legend&&(A.legend[this.legendPosition]=10,~["left","right"].indexOf(this.legendPosition)&&(A.legend.top="middle",A.legend.orient="vertical")),A.color=this.chartColor,s.ECHARTS_SETTINGS.forEach((function(t){e[t]&&(A[t]=e[t])})),this.animation&&u(A,this.animation),this.markArea||this.markLine||this.markPoint){var t={markArea:this.markArea,markLine:this.markLine,markPoint:this.markPoint},i=A.series;o.isArray(i)?i.forEach((function(A){Q(A,t)})):o.isObject(i)&&Q(i,t)}this.extend&&I(A,this.extend),this.afterConfig&&(A=this.afterConfig(A));var n=this.setOptionOpts;!this.settings.bmap&&!this.settings.amap||o.isObject(n)||(n=!1),this.notSetUnchange&&this.notSetUnchange.length&&(this.notSetUnchange.forEach((function(t){var i=A[t];i&&(o.isEqual(i,e._store[t])?A[t]=void 0:e._store[t]=o.cloneDeep(i))})),o.isObject(n)?n.notMerge=!1:n=!1),this._isDestroyed||(this.log,this.echarts.setOption(A,n),this.$emit("ready",this.echarts,A,r),this._once["ready-once"]||(this._once["ready-once"]=!0,this.$emit("ready-once",this.echarts,A,r)),this.judgeWidth&&this.judgeWidthHandler(A),this.afterSetOption&&this.afterSetOption(this.echarts,A,r),this.afterSetOptionOnce&&!this._once["afterSetOptionOnce"]&&(this._once["afterSetOptionOnce"]=!0,this.afterSetOptionOnce(this.echarts,A,r)))},judgeWidthHandler:function(A){var e=this,t=this.widthChangeDelay,i=this.resize;this.$el.clientWidth||this.$el.clientHeight?i():this.$nextTick((function(A){e.$el.clientWidth||e.$el.clientHeight?i():setTimeout((function(A){i(),!e.$el.clientWidth||e.$el.clientHeight}),t)}))},resizeableHandler:function(A){A&&!this._once.onresize&&this.addResizeListener(),!A&&this._once.onresize&&this.removeResizeListener()},init:function(){if(!this.echarts){var A=this.themeName||this.theme||s.DEFAULT_THEME;this.echarts=r.init(this.$refs.canvas,A,this.initOptions),this.data&&this.changeHandler(),this.createEventProxy(),this.resizeable&&this.addResizeListener()}},addResizeListener:function(){window.addEventListener("resize",this.resizeHandler),this._once.onresize=!0},removeResizeListener:function(){window.removeEventListener("resize",this.resizeHandler),this._once.onresize=!1},addWatchToProps:function(){var A=this,e=this._watchers.map((function(A){return A.expression}));Object.keys(this.$props).forEach((function(t){if(!~e.indexOf(t)&&!~s.STATIC_PROPS.indexOf(t)){var i={};~["[object Object]","[object Array]"].indexOf(o.getType(A.$props[t]))&&(i.deep=!0),A.$watch(t,(function(){A.changeHandler()}),i)}}))},createEventProxy:function(){var A=this,e=this,t=Object.keys(this.events||{});t.length&&t.forEach((function(t){-1===A.registeredEvents.indexOf(t)&&(A.registeredEvents.push(t),A.echarts.on(t,function(A){return function(){if(A in e.events){for(var t=arguments.length,i=Array(t),n=0;n<t;n++)i[n]=arguments[n];e.events[A].apply(null,i)}}}(t)))}))},themeChange:function(A){this.clean(),this.echarts=null,this.init()},clean:function(){this.resizeable&&this.removeResizeListener(),this.echarts.dispose()}},created:function(){this.echarts=null,this.registeredEvents=[],this._once={},this._store={},this.resizeHandler=o.debounce(this.resize,this.resizeDelay),this.changeHandler=o.debounce(this.dataHandler,this.changeDelay),this.addWatchToProps()},mounted:function(){this.init()},beforeDestroy:function(){this.clean()},_numerify:a};A.exports=C},87208:function(A,e,t){"use strict";var i=t(57847)["default"];function n(A){return A&&"object"===i(A)&&"default"in A?A["default"]:A}var o=t(8993),r=t(87279);t(42011);var a=n(t(82445));function s(A,e){return{trigger:"item",formatter:function(t){var i=[];return i.push(o.itemPoint(t.color)),i.push(t.name+": "+r.getFormated(t.data.realValue,A,e)),i.join("")}}}function l(A){var e=A.data,t=A.legendName;return{data:e,formatter:function(A){return null!=t[A]?t[A]:A}}}function g(A){var e=A.dimension,t=A.metrics,i=A.rows,n=A.sequence,o=A.ascending,r=A.label,a=A.labelLine,s=A.itemStyle,l=A.filterZero,g=A.useDefaultOrder,c={type:"funnel"},I=i.sort((function(A,t){return n.indexOf(A[e])-n.indexOf(t[e])}));l&&(I=I.filter((function(A){return A[t]})));var Q=!1;I.some((function(A,e){if(e&&A[t]>I[e-1][t])return Q=!0,!0}));var u=100/I.length;return c.data=Q&&!g?I.slice().reverse().map((function(A,i){return{name:A[e],value:(i+1)*u,realValue:A[t]}})):I.map((function(A){return{name:A[e],value:A[t],realValue:A[t]}})),o&&(c.sort="ascending"),r&&(c.label=r),a&&(c.labelLine=a),s&&(c.itemStyle=s),c}var c=function(A,e,t,i){var n=A.slice(),o=e.slice(),r=t.dataType,a=void 0===r?"normal":r,c=t.dimension,I=void 0===c?n[0]:c,Q=t.sequence,u=void 0===Q?o.map((function(A){return A[I]})):Q,C=t.digit,h=void 0===C?2:C,d=t.ascending,B=t.label,E=t.labelLine,p=t.legendName,m=void 0===p?{}:p,f=t.itemStyle,y=t.filterZero,b=t.useDefaultOrder,v=i.tooltipVisible,w=i.legendVisible,N=void 0;if(t.metrics)N=t.metrics;else{var D=n.slice();D.splice(n.indexOf(I),1),N=D[0]}var Y=v&&s(a,h),M=w&&l({data:u,legendName:m}),k=g({dimension:I,metrics:N,rows:o,sequence:u,ascending:d,label:B,labelLine:E,itemStyle:f,filterZero:y,useDefaultOrder:b}),O={tooltip:Y,legend:M,series:k};return O},I=Object.assign||function(A){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(A[i]=t[i])}return A},Q=I({},a,{name:"VeFunnel",data:function(){return this.chartHandler=c,{}}});A.exports=Q},27854:function(A,e,t){"use strict";var i=t(57847)["default"];function n(A){return A&&"object"===i(A)&&"default"in A?A["default"]:A}var o=t(87279),r=t(55925);t(81627);var a=n(t(82445)),s=Object.assign||function(A){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(A[i]=t[i])}return A};function l(A){var e=A.tooltipFormatter,t=A.dataType,i=A.digit;return{formatter:function(A){var n=A.seriesName,r=A.data,a=r.value,s=r.name;if(e)return e.apply(null,arguments);var l=[];return l.push(n+": "),l.push(o.getFormated(a,t[n],i)+" "+s),l.join("")}}}function g(A){var e=A.rows,t=A.dimension,i=A.metrics,n=A.digit,a=A.dataType,l=A.labelMap,g=A.seriesMap,c=A.dataName,I=e.map((function(A){var e=A[t],I=g[e],Q={type:"gauge",name:null!=l[e]?l[e]:e,data:[{name:c[e]||"",value:A[i]}],detail:{formatter:function(A){return o.getFormated(A,a[e],n)}},axisLabel:{formatter:function(A){return o.getFormated(A,a[e],n)}}};return I&&Object.keys(I).forEach((function(A){r.isObject(Q[A])?s(Q[A],I[A]):Q[A]=I[A]})),Q}));return I}var c=function(A,e,t,i){var n=t.dimension,o=void 0===n?A[0]:n,r=t.metrics,a=void 0===r?A[1]:r,s=t.digit,c=void 0===s?2:s,I=t.dataType,Q=void 0===I?{}:I,u=t.labelMap,C=void 0===u?{}:u,h=t.seriesMap,d=void 0===h?{}:h,B=t.dataName,E=void 0===B?{}:B,p=i.tooltipFormatter,m=i.tooltipVisible,f=m&&l({tooltipFormatter:p,dataType:Q}),y=g({rows:e,dimension:o,metrics:a,digit:c,dataType:Q,labelMap:C,seriesMap:d,dataName:E});return{tooltip:f,series:y}},I=s({},a,{name:"VeGauge",data:function(){return this.chartHandler=c,{}}});A.exports=I},12607:function(A,e,t){"use strict";var i=t(57847)["default"];function n(A){return A&&"object"===i(A)&&"default"in A?A["default"]:A}var o=t(8993),r=t(87279),a=n(t(39405));t(25716),t(890),t(57134),t(77763);var s=n(t(82445)),l=Object.assign||function(A){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(A[i]=t[i])}return A},g=function(A){return Array.isArray(A)?A:Array.from(A)};function c(A,e){var t=[];return A.forEach((function(A){~t.indexOf(A[e])||t.push(A[e])})),t}function I(A){var e=A.rows,t=A.innerXAxisList,i=A.innerYAxisList,n=A.xDim,o=A.yDim,r=A.metrics,a=A.type,s=A.extraMetrics,l=null;return l="cartesian"===a?e.map((function(A){var e=t.indexOf(A[n]),a=i.indexOf(A[o]),l=r?A[r]:1,g=s.map((function(e){return A[e]||"-"}));return{value:[e,a,l].concat(g)}})):e.map((function(A){var e=r?A[r]:1;return{value:[A[n],A[o],e]}})),l}function Q(A,e){return{type:"category",data:A,name:e,nameLocation:"end",splitArea:{show:!0}}}function u(A){var e=A.innerMin,t=A.innerMax,i=A.type,n=A.heatColor,r=A.series,a={min:e,max:t,calculable:!0},s=null;return"map"===i?(s={orient:"vertical",left:0,bottom:0,inRange:{color:n||o.HEAT_MAP_COLOR}},r[0].data.length||(s.show=!1)):s="bmap"===i||"amap"===i?{show:!1,orient:"vertical",left:0,bottom:0,inRange:{color:n||o.HEAT_BMAP_COLOR}}:{orient:"horizontal",left:"center",bottom:10,dimension:2,inRange:n&&{color:n}},l(a,s)}function C(A){var e=A.chartData;return[{type:"heatmap",data:e}]}function h(A){var e=A.dataType,t=A.innerXAxisList,i=A.innerYAxisList,n=A.digit,a=A.extraMetrics,s=A.metrics;return{trigger:"item",formatter:function(A){var l=A.color,c=g(A.data.value),I=c[0],Q=c[1],u=c[2],C=c.slice(3),h=[];return h.push(t[I]+" ~ "+i[Q]+"<br>"),a.forEach((function(A,e){h.push(A+": "+C[e]+"<br>")})),h.push(o.itemPoint(l)+" "+s+": "+r.getFormated(u,e,n)+"<br>"),h.join("")}}}var d=function(A,e,t,i){var n=t.type,o=void 0===n?"cartesian":n,s=t.xAxisList,g=t.yAxisList,d=t.dimension,B=void 0===d?[A[0],A[1]]:d,E=t.metrics,p=void 0===E?A[2]:E,m=t.dataType,f=void 0===m?"normal":m,y=t.min,b=t.max,v=t.digit,w=t.bmap,N=t.amap,D=t.geo,Y=t.key,M=t.v,k=void 0===M?"2.0":M,O=t.position,x=t.positionJsonLink,G=t.beforeRegisterMap,j=t.pointSize,Z=void 0===j?10:j,F=t.blurSize,J=void 0===F?5:F,S=t.heatColor,R=t.yAxisName,z=t.xAxisName,T=t.beforeRegisterMapOnce,X=t.mapURLProfix,U=void 0===X?"https://unpkg.com/echarts@3.6.2/map/json/":X,W=t.specialAreas,V=void 0===W?{}:W,_=i.tooltipVisible,L=s,H=g,K=[],q=[],P=B.concat([p]);A.forEach((function(A){~P.indexOf(A)||q.push(A)})),"cartesian"===o?(L&&L.length||(L=c(e,B[0])),H&&H.length||(H=c(e,B[1])),K=I({rows:e,innerXAxisList:L,innerYAxisList:H,xDim:B[0],yDim:B[1],metrics:p,type:o,extraMetrics:q})):K=I({rows:e,xDim:B[0],yDim:B[1],metrics:p,type:o,extraMetrics:q});var $=p?e.map((function(A){return A[p]})):[0,5];$.length||($=[0]);var AA=y||Math.min.apply(null,$),eA=b||Math.max.apply(null,$),tA=Q(L,z),iA=Q(H,R),nA=C({chartData:K}),oA=u({innerMin:AA,innerMax:eA,type:o,heatColor:S,series:nA}),rA=_&&h({dataType:f,innerXAxisList:L,innerYAxisList:H,digit:v,extraMetrics:q,metrics:p}),aA={visualMap:oA,series:nA};return"bmap"===o?(l(aA.series[0],{coordinateSystem:"bmap",pointSize:Z,blurSize:J}),r.getBmap(Y,k).then((function(A){return l({bmap:w},aA)}))):"map"===o?(aA.series[0].coordinateSystem="geo",r.getMapJSON({position:O,positionJsonLink:x,beforeRegisterMapOnce:T,mapURLProfix:U}).then((function(A){var e=l({map:O},D);return G&&(A=G(A)),a.registerMap(O,A,V),l({geo:e},aA)}))):"amap"===o?(l(aA.series[0],{coordinateSystem:"amap",pointSize:Z,blurSize:J}),r.getAmap(Y,k).then((function(A){return l({amap:N},aA)}))):l({xAxis:tA,yAxis:iA,tooltip:rA},aA)},B=l({},s,{name:"VeHeatmap",data:function(){return this.chartHandler=d,{}}});A.exports=B},42045:function(A,e,t){"use strict";var i=t(57847)["default"];function n(A){return A&&"object"===i(A)&&"default"in A?A["default"]:A}var o=t(8993),r=t(87279),a=t(55925);t(19467);var s=n(t(82445)),l=function(A,e,t){return e in A?Object.defineProperty(A,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):A[e]=t,A},g=Object.assign||function(A){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(A[i]=t[i])}return A},c=.5;function I(A){var e=A.innerRows,t=A.dimAxisName,i=A.dimension,n=A.axisVisible,o=A.dimAxisType,r=A.dims;return i.map((function(A){return{type:"category",name:t,nameLocation:"middle",nameGap:22,data:"value"===o?Q(r):e.map((function(e){return e[A]})),axisLabel:{formatter:function(A){return String(A)}},show:n}}))}function Q(A){for(var e=Math.max.apply(null,A),t=Math.min.apply(null,A),i=[],n=t;n<=e;n++)i.push(n);return i}function u(A){for(var e=A.meaAxisName,t=A.meaAxisType,i=A.axisVisible,n=A.digit,o=A.scale,a=A.min,s=A.max,l={type:"value",axisTick:{show:!1},show:i},c=[],I=function(A){t[A]?c[A]=g({},l,{axisLabel:{formatter:function(e){return r.getFormated(e,t[A],n)}}}):c[A]=g({},l),c[A].name=e[A]||"",c[A].scale=o[A]||!1,c[A].min=a[A]||null,c[A].max=s[A]||null},Q=0;Q<2;Q++)I(Q);return c}function C(A){var e=A.axisSite,t=A.isHistogram,i=A.meaAxisType,n=A.digit,a=A.labelMap,s=t?e.right||[]:e.top||[];return a&&(s=s.map((function(A){return void 0===a[A]?A:a[A]}))),{trigger:"axis",formatter:function(A){var e=[];return e.push(A[0].name+"<br>"),A.forEach((function(A){var t=A.seriesName,a=~s.indexOf(t)?i[1]:i[0];e.push(o.itemPoint(A.color)),e.push(t+": "),e.push(r.getFormated(A.value,a,n)),e.push("<br>")})),e.join("")}}}function h(A,e){for(var t=Math.max.apply(null,e),i=Math.min.apply(null,e),n=[],o=i;o<=t;o++){var r=e.indexOf(o);~r?n.push(A[r]):n.push(null)}return n}function d(A){var e=A.innerRows,t=A.metrics,i=A.stack,n=A.axisSite,o=A.isHistogram,s=A.labelMap,g=A.itemStyle,I=A.label,Q=A.showLine,u=void 0===Q?[]:Q,C=A.dimAxisType,d=A.barGap,B=A.opacity,E=A.dims,p=[],m={},f=o?n.right||[]:n.top||[],y=o?"yAxisIndex":"xAxisIndex",b=i&&r.getStackMap(i);return t.forEach((function(A){m[A]=[]})),e.forEach((function(A){t.forEach((function(e){m[e].push(A[e])}))})),p=Object.keys(m).map((function(A,e){var t="value"===C?h(m[A],E):m[A],n=l({name:null!=s[A]?s[A]:A,type:~u.indexOf(A)?"line":"bar",data:t},y,~f.indexOf(A)?"1":"0");i&&b[A]&&(n.stack=b[A]),I&&(n.label=I),g&&(n.itemStyle=g);var o=B||a.get(n,"itemStyle.normal.opacity");return"value"===C&&(n.barGap=d,n.barCategoryGap="1%",null==o&&(o=c)),null!=o&&a.set(n,"itemStyle.normal.opacity",o),n})),!!p.length&&p}function B(A){var e=A.metrics,t=A.labelMap,i=A.legendName;if(!i&&!t)return{data:e};var n=t?e.map((function(A){return null==t[A]?A:t[A]})):e;return{data:n,formatter:function(A){return null!=i[A]?i[A]:A}}}function E(A,e){return A.map((function(A){return A[e[0]]}))}var p=function(A,e,t,i){var n=a.cloneDeep(e),o=t.axisSite,r=void 0===o?{}:o,s=t.dimension,l=void 0===s?[A[0]]:s,g=t.stack,c=void 0===g?{}:g,Q=t.axisVisible,h=void 0===Q||Q,p=t.digit,m=void 0===p?2:p,f=t.dataOrder,y=void 0!==f&&f,b=t.scale,v=void 0===b?[!1,!1]:b,w=t.min,N=void 0===w?[null,null]:w,D=t.max,Y=void 0===D?[null,null]:D,M=t.labelMap,k=void 0===M?{}:M,O=t.legendName,x=void 0===O?{}:O,G=t.label,j=t.itemStyle,Z=t.showLine,F=t.barGap,J=void 0===F?"-100%":F,S=t.opacity;if(y){var R=y.label,z=y.order;R&&z&&n.sort((function(A,e){return"desc"===z?A[R]-e[R]:e[R]-A[R]}))}var T=i.tooltipVisible,X=i.legendVisible,U=A.slice();r.left&&r.right?U=r.left.concat(r.right):r.left&&!r.right?U=r.left:t.metrics?U=t.metrics:U.splice(A.indexOf(l[0]),1);var W=t.yAxisType||["normal","normal"],V=t.xAxisType||"category",_=t.yAxisName||[],L=t.xAxisName||"",H=!0,K=E(n,l),q=X&&B({metrics:U,labelMap:k,legendName:x}),P=I({innerRows:n,dimAxisName:L,dimension:l,axisVisible:h,dimAxisType:V,dims:K}),$=u({meaAxisName:_,meaAxisType:W,axisVisible:h,digit:m,scale:v,min:N,max:Y}),AA=d({innerRows:n,metrics:U,stack:c,axisSite:r,isHistogram:H,labelMap:k,itemStyle:j,label:G,showLine:Z,dimAxisType:V,dimension:l,barGap:J,opacity:S,dims:K}),eA={axisSite:r,isHistogram:H,meaAxisType:W,digit:m,labelMap:k},tA=T&&C(eA),iA={legend:q,yAxis:$,series:AA,xAxis:P,tooltip:tA};return iA},m=g({},s,{name:"VeHistogram",data:function(){return this.chartHandler=p,{}}});A.exports=m},45607:function(A,e,t){"use strict";var i=t(57847)["default"];function n(A){return A&&"object"===i(A)&&"default"in A?A["default"]:A}var o=t(87279),r=t(55925);t(44440);var a=n(t(82445)),s=Object.assign||function(A){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(A[i]=t[i])}return A};function l(A){var e=A.dimension,t=A.rows,i=A.xAxisName,n=A.axisVisible,o=A.xAxisType;return e.map((function(A,e){return{type:o,nameLocation:"middle",nameGap:22,name:i[e]||"",axisTick:{show:!0,lineStyle:{color:"#eee"}},data:t.map((function(e){return e[A]})),show:n}}))}function g(A){var e=A.rows,t=A.axisSite,i=A.metrics,n=A.area,r=A.stack,a=A.nullAddZero,s=A.labelMap,l=A.label,g=A.itemStyle,c=A.lineStyle,I=A.areaStyle,Q=A.dimension,u=[],C={},h=r&&o.getStackMap(r);return i.forEach((function(A){C[A]=[]})),e.forEach((function(A){i.forEach((function(e){var t=null;null!=A[e]?t=A[e]:a&&(t=0),C[e].push([A[Q[0]],t])}))})),i.forEach((function(A){var e={name:null!=s[A]?s[A]:A,type:"line",data:C[A]};n&&(e.areaStyle={normal:{}}),t.right&&(e.yAxisIndex=~t.right.indexOf(A)?1:0),r&&h[A]&&(e.stack=h[A]),l&&(e.label=l),g&&(e.itemStyle=g),c&&(e.lineStyle=c),I&&(e.areaStyle=I),u.push(e)})),u}function c(A){for(var e=A.yAxisName,t=A.yAxisType,i=A.axisVisible,n=A.scale,r=A.min,a=A.max,l=A.digit,g={type:"value",axisTick:{show:!1},show:i},c=[],I=function(A){t[A]?c[A]=s({},g,{axisLabel:{formatter:function(e){return o.getFormated(e,t[A],l)}}}):c[A]=s({},g),c[A].name=e[A]||"",c[A].scale=n[A]||!1,c[A].min=r[A]||null,c[A].max=a[A]||null},Q=0;Q<2;Q++)I(Q);return c}function I(A){var e=A.axisSite,t=A.yAxisType,i=A.digit,n=A.labelMap,a=A.tooltipFormatter,s=e.right||[],l=n?s.map((function(A){return void 0===n[A]?A:n[A]})):s;return{trigger:"axis",formatter:function(A){if(a)return a.apply(null,arguments);var e=[],n=A[0],s=n.name,g=n.axisValueLabel,c=s||g;return e.push(c+"<br>"),A.forEach((function(A){var n=A.seriesName,a=A.data,s=A.marker,g=null,c=~l.indexOf(n)?t[1]:t[0],I=r.isArray(a)?a[1]:a;g=o.getFormated(I,c,i),e.push(s),e.push(n+": "+g),e.push("<br>")})),e.join("")}}}function Q(A){var e=A.metrics,t=A.legendName,i=A.labelMap;if(!t&&!i)return{data:e};var n=i?e.map((function(A){return null==i[A]?A:i[A]})):e;return{data:n,formatter:function(A){return null!=t[A]?t[A]:A}}}var u=function(A,e,t,i){e=r.isArray(e)?e:[],A=r.isArray(A)?A:[];var n=t.axisSite,o=void 0===n?{}:n,a=t.yAxisType,s=void 0===a?["normal","normal"]:a,u=t.xAxisType,C=void 0===u?"category":u,h=t.yAxisName,d=void 0===h?[]:h,B=t.dimension,E=void 0===B?[A[0]]:B,p=t.xAxisName,m=void 0===p?[]:p,f=t.axisVisible,y=void 0===f||f,b=t.area,v=t.stack,w=t.scale,N=void 0===w?[!1,!1]:w,D=t.min,Y=void 0===D?[null,null]:D,M=t.max,k=void 0===M?[null,null]:M,O=t.nullAddZero,x=void 0!==O&&O,G=t.digit,j=void 0===G?2:G,Z=t.legendName,F=void 0===Z?{}:Z,J=t.labelMap,S=void 0===J?{}:J,R=t.label,z=t.itemStyle,T=t.lineStyle,X=t.areaStyle,U=i.tooltipVisible,W=i.legendVisible,V=i.tooltipFormatter,_=A.slice();o.left&&o.right?_=o.left.concat(o.right):o.left&&!o.right?_=o.left:t.metrics?_=t.metrics:_.splice(A.indexOf(E[0]),1);var L=W&&Q({metrics:_,legendName:F,labelMap:S}),H=U&&I({axisSite:o,yAxisType:s,digit:j,labelMap:S,xAxisType:C,tooltipFormatter:V}),K=l({dimension:E,rows:e,xAxisName:m,axisVisible:y,xAxisType:C}),q=c({yAxisName:d,yAxisType:s,axisVisible:y,scale:N,min:Y,max:k,digit:j}),P=g({rows:e,axisSite:o,metrics:_,area:b,stack:v,nullAddZero:x,labelMap:S,label:R,itemStyle:z,lineStyle:T,areaStyle:X,xAxisType:C,dimension:E}),$={legend:L,xAxis:K,series:P,yAxis:q,tooltip:H};return $},C=s({},a,{name:"VeLine",data:function(){return this.chartHandler=u,{}}});A.exports=C},39266:function(A,e,t){"use strict";var i=t(57847)["default"];function n(A){return A&&"object"===i(A)&&"default"in A?A["default"]:A}var o=t(55925),r=t(87279);t(90316);var a=n(t(82445)),s=Object.assign||function(A){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(A[i]=t[i])}return A};function l(A){var e=A.tooltipFormatter,t=A.dataType,i=A.digit;return{show:!0,formatter:function(A){var n=A.seriesName,o=A.value;return e?e.apply(null,arguments):[n+": ",r.getFormated(o,t,i)].join("")}}}function g(A){var e=A.dimension,t=A.metrics,i=A.seriesMap,n=A.rows,r=A.wave,a=r,l=o.isArray(i)?i.length:0;return n.slice().map((function(A,n){var g=[],c={type:"liquidFill"},I=A[e],Q=Number(A[t]),u={};return o.isArray(i)?u=i[n]?i[n]:i[l-1]:o.isObject(i[I])&&(u=i[I]),o.isArray(r)&&o.isArray(r[0])&&(a=o.isArray(r[n])?r[n]:r[r.length-1]),g.push({value:Q}),a&&a.length&&(g=g.concat(a.map((function(A){return{value:A}})))),c=s(c,{data:g,name:I},u),c}))}var c=function(A,e,t,i){var n=t.dimension,o=void 0===n?A[0]:n,r=t.metrics,a=void 0===r?A[1]:r,s=t.seriesMap,c=void 0===s?{}:s,I=t.dataType,Q=void 0===I?"percent":I,u=t.digit,C=void 0===u?2:u,h=t.wave,d=void 0===h?[]:h,B=i.tooltipVisible,E=i.tooltipFormatter,p=B&&l({tooltipFormatter:E,dataType:Q,digit:C}),m=g({rows:e,columns:A,dimension:o,metrics:a,seriesMap:c,wave:d});return{tooltip:p,series:m}},I=s({},a,{name:"VeLiquidfill",data:function(){return this.chartHandler=c,{}}});A.exports=I},75478:function(A,e,t){"use strict";var i=t(57847)["default"];function n(A){return A&&"object"===i(A)&&"default"in A?A["default"]:A}var o=n(t(39405)),r=t(8993),a=t(87279);t(77763);var s=n(t(82445)),l="function"===typeof Symbol&&"symbol"===i(Symbol.iterator)?function(A){return i(A)}:function(A){return A&&"function"===typeof Symbol&&A.constructor===Symbol&&A!==Symbol.prototype?"symbol":i(A)},g=Object.assign||function(A){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(A[i]=t[i])}return A};function c(A,e,t,i,n,o){return{formatter:function(s){var l=[];return s.name?(l.push(s.name+"<br>"),i.forEach((function(i,g){var c=null!=o[i]?o[i]:i;l.push(r.itemPoint(n[g])+" "+c+" : "),t[s.name]?l.push(a.getFormated(t[s.name][i],A[i],e)):l.push("-"),l.push("<br>")})),l.join(" ")):""}}}function I(A){var e=A.position,t=A.selectData,i=A.dimension,n=A.metrics,o=A.rows,r=A.label,a=A.itemStyle,s=A.selectedMode,l=A.roam,c=A.center,I=A.aspectScale,u=A.boundingCoords,C=A.zoom,h=A.labelMap,d=A.scaleLimit,B=A.mapGrid,E=[],p={type:"map",mapType:e};return n.forEach((function(A){var e=g({name:null!=h[A]?h[A]:A,data:[],selectedMode:s,roam:l,center:c,aspectScale:I,boundingCoords:u,zoom:C,scaleLimit:d},p);B&&Object.keys(B).forEach((function(A){e[A]=B[A]})),Q(a,e,"itemStyle"),Q(r,e,"label"),o.forEach((function(n){e.data.push({name:n[i],value:n[A],selected:t})})),E.push(e)})),E}function Q(A,e,t){"object"===("undefined"===typeof A?"undefined":l(A))?e[t]=A:A&&(e[t]={normal:{show:!0},emphasis:{show:!0}})}function u(A){var e=A.metrics,t=A.legendName,i=A.labelMap;if(!t&&!i)return{data:e};var n=i?e.map((function(A){return null==i[A]?A:i[A]})):e;return{data:n,formatter:function(A){return null!=t[A]?t[A]:A}}}function C(A,e){var t=A._once,i=A.registerSign,n=A.beforeRegisterMap,r=A.beforeRegisterMapOnce,a=A.registerSignOnce,s=A.position,l=A.specialAreas;t[i]||(n&&(e=n(e)),r&&!t[a]&&(t[a]=!0,e=r(e)),t[i]=!0,o.registerMap(s,e,l))}var h=function(A,e,t,i){var n=t.position,o=void 0===n?"china":n,r=t.selectData,s=void 0!==r&&r,l=t.selectedMode,g=t.label,Q=void 0===g||g,h=t.dataType,d=void 0===h?{}:h,B=t.digit,E=void 0===B?2:B,p=t.dimension,m=void 0===p?A[0]:p,f=t.roam,y=t.center,b=t.aspectScale,v=t.boundingCoords,w=t.zoom,N=t.scaleLimit,D=t.legendName,Y=void 0===D?{}:D,M=t.labelMap,k=void 0===M?{}:M,O=t.mapGrid,x=t.itemStyle,G=t.positionJsonLink,j=t.beforeRegisterMap,Z=t.beforeRegisterMapOnce,F=t.mapURLProfix,J=void 0===F?"https://unpkg.com/echarts@3.6.2/map/json/":F,S=t.specialAreas,R=void 0===S?{}:S,z=t.mapOrigin,T=A.slice();t.metrics?T=t.metrics:T.splice(A.indexOf(m),1);var X=i.tooltipVisible,U=i.legendVisible,W=i.color,V=i._once,_={};e.forEach((function(A){_[A[m]]=A}));var L=X&&c(d,E,_,T,W,k),H=U&&u({metrics:T,legendName:Y,labelMap:k}),K={position:o,selectData:s,label:Q,itemStyle:x,dimension:m,metrics:T,rows:e,selectedMode:l,roam:f,center:y,aspectScale:b,boundingCoords:v,zoom:w,labelMap:k,scaleLimit:N,mapGrid:O},q=I(K),P={_once:V,beforeRegisterMap:j,beforeRegisterMapOnce:Z,registerSign:"MAP_REGISTER_"+o,registerSignOnce:"ONCE_MAP_REGISTER_"+o,position:o,specialAreas:R};return z?(C(P,z),{series:q,tooltip:L,legend:H}):a.getMapJSON({position:o,positionJsonLink:G,beforeRegisterMapOnce:Z,mapURLProfix:J}).then((function(A){return C(P,A),{series:q,tooltip:L,legend:H}}))},d=g({},s,{name:"VeMap",data:function(){return this.chartHandler=h,{}}});A.exports=d},73737:function(A,e,t){"use strict";var i=t(57847)["default"];function n(A){return A&&"object"===i(A)&&"default"in A?A["default"]:A}var o=t(8993),r=t(87279),a=t(55925);t(89174);var s=n(t(82445)),l=Object.assign||function(A){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(A[i]=t[i])}return A},g=100,c=[80,100],I=[20,100],Q=200;function u(A){var e=A.innerRows,t=A.dataType,i=A.percentShow,n=A.dimension,o=A.metrics,a=A.radius,s=A.offsetY,g=A.selectedMode,c=A.hoverAnimation,I=A.digit,Q=A.roseType,u=A.label,C=A.level,h=A.limitShowNum,d=A.isRing,B=A.labelLine,E=A.itemStyle,p=[],m={},f=[];C?(C.forEach((function(A,e){A.forEach((function(A){r.setArrayValue(m,A,e)}))})),e.forEach((function(A){var e=m[A[n]];e&&e.length&&e.forEach((function(e){r.setArrayValue(f,e,A)}))}))):f.push(e);var y={type:"pie",selectedMode:g,hoverAnimation:c,roseType:Q,center:["50%",s]},b=f.length;if(f.forEach((function(A,e){var s=l({data:[]},y),g=a/b;if(e){var c=g+a/(2*b)*(2*e-1),Q=c+a/(2*b);s.radius=[c,Q]}else s.radius=d?a:g;b>1&&0===e&&(s.label={normal:{position:"inner"}}),u&&(s.label=u),B&&(s.labelLine=B),E&&(s.itemStyle=E),i&&(s.label={normal:{show:!0,position:b>1&&0===e?"inner":"outside",formatter:function(A){var e=[];return e.push(A.name+":"),e.push(r.getFormated(A.value,t,I)),e.push("("+A.percent+"%)"),e.join(" ")}}}),s.data=A.map((function(A){return{name:A[n],value:A[o]}})),p.push(s)})),h&&h<p[0].data.length){var v=p[0].data,w=v.slice(h,v.length),N=0;w.forEach((function(A){N+=A.value})),p[0].data=v.slice(0,h),p[0].data.push({name:"其他",value:N})}return p}function C(A){var e=A.innerRows,t=A.dimension,i=A.legendLimit,n=A.legendName,o=A.level,r=A.limitShowNum,a=[],s=[];if(o)o.forEach((function(A){A.forEach((function(A){s.push(A)}))})),a=s;else if(r&&r<e.length){for(var l=0;l<r;l++)a.push(e[l][t]);a.push("其他")}else a=e.map((function(A){return A[t]}));return!!a.length&&{data:a,show:a.length<i,formatter:function(A){return null!=n[A]?n[A]:A}}}function h(A){var e=A.dataType,t=A.innerRows,i=A.limitShowNum,n=A.digit,a=A.metrics,s=A.dimension,l=0,g=t.map((function(A){return l+=A[a],{name:A[s],value:A[a]}})).slice(i,t.length);return{formatter:function(A){var t=[];return t.push(o.itemPoint(A.color)),i&&"其他"===A.name?(t.push("其他:"),g.forEach((function(A){var i=A.name,o=A.value,a=r.getFormated(o/l,"percent");t.push("<br>"+i+":"),t.push(r.getFormated(o,e,n)),t.push("("+a+")")}))):(t.push(A.name+":"),t.push(r.getFormated(A.value,e,n)),t.push("("+A.percent+"%)")),t.join(" ")}}}var d=function(A,e,t,i,n){var o=a.cloneDeep(e),r=t.dataType,s=void 0===r?"normal":r,l=t.percentShow,d=t.dimension,B=void 0===d?A[0]:d,E=t.metrics,p=void 0===E?A[1]:E,m=t.roseType,f=void 0!==m&&m,y=t.radius,b=void 0===y?n?f?I:c:g:y,v=t.offsetY,w=void 0===v?Q:v,N=t.legendLimit,D=void 0===N?30:N,Y=t.selectedMode,M=void 0!==Y&&Y,k=t.hoverAnimation,O=void 0===k||k,x=t.digit,G=void 0===x?2:x,j=t.legendName,Z=void 0===j?{}:j,F=t.label,J=void 0!==F&&F,S=t.level,R=void 0!==S&&S,z=t.limitShowNum,T=void 0===z?0:z,X=t.labelLine,U=t.itemStyle,W=i.tooltipVisible,V=i.legendVisible;T&&o.sort((function(A,e){return e[p]-A[p]}));var _={innerRows:o,dataType:s,percentShow:l,dimension:B,metrics:p,radius:b,offsetY:w,selectedMode:M,hoverAnimation:O,digit:G,roseType:f,label:J,level:R,legendName:Z,limitShowNum:T,isRing:n,labelLine:X,itemStyle:U},L=u(_),H={innerRows:o,dimension:B,legendLimit:D,legendName:Z,level:R,limitShowNum:T},K=V&&C(H),q=W&&h({dataType:s,innerRows:o,limitShowNum:T,digit:G,metrics:p,dimension:B}),P={series:L,legend:K,tooltip:q};return P},B=l({},s,{name:"VePie",data:function(){return this.chartHandler=d,{}}});A.exports=B},70462:function(A,e,t){"use strict";var i=t(57847)["default"];function n(A){return A&&"object"===i(A)&&"default"in A?A["default"]:A}var o=t(8993),r=t(87279);t(76515);var a=n(t(82445));function s(A,e,t){var i=A.map((function(A){return A[e]}));return{data:i,formatter:function(A){return null!=t[A]?t[A]:A}}}function l(A,e,t){var i=[],n=[];return e.indicator.map((function(e,t){i[t]=A[e.name],n[t]=e.name})),{formatter:function(A){var e=[];return e.push(o.itemPoint(A.color)),e.push(A.name+"<br />"),A.data.value.forEach((function(A,o){e.push(n[o]+": "),e.push(r.getFormated(A,i[o],t)+"<br />")})),e.join("")}}}function g(A,e,t){var i={indicator:[],shape:"circle",splitNumber:5},n={};return A.forEach((function(A){e.forEach((function(e){var i=null!=t[e]?t[e]:e;n[i]?n[i].push(A[e]):n[i]=[A[e]]}))})),i.indicator=Object.keys(n).map((function(A){return{name:A,max:Math.max.apply(null,n[A])}})),i}function c(A){var e=A.rows,t=A.dimension,i=A.metrics,n=A.radar,o=A.label,r=A.itemStyle,a=A.lineStyle,s=A.labelMap,l=A.areaStyle,g={};n.indicator.forEach((function(A,e){var t=A.name;g[t]=e}));var c=e.map((function(A){var e={value:[],name:A[t]};return Object.keys(A).forEach((function(t){if(~i.indexOf(t)){var n=null!=s[t]?g[s[t]]:g[t];e.value[n]=A[t]}})),e})),I={name:t,type:"radar",data:c};return o&&(I.label=o),r&&(I.itemStyle=r),a&&(I.lineStyle=a),l&&(I.areaStyle=l),[I]}var I=function(A,e,t,i){var n=t.dataType,o=void 0===n?{}:n,r=t.legendName,a=void 0===r?{}:r,I=t.labelMap,Q=void 0===I?{}:I,u=t.dimension,C=void 0===u?A[0]:u,h=t.digit,d=void 0===h?2:h,B=t.label,E=t.itemStyle,p=t.lineStyle,m=t.areaStyle,f=i.tooltipVisible,y=i.legendVisible,b=A.slice();t.metrics?b=t.metrics:b.splice(A.indexOf(C),1);var v=y&&s(e,C,a),w=g(e,b,Q),N=f&&l(o,w,d),D=c({rows:e,dimension:C,metrics:b,radar:w,label:B,itemStyle:E,lineStyle:p,labelMap:Q,areaStyle:m}),Y={legend:v,tooltip:N,radar:w,series:D};return Y},Q=Object.assign||function(A){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(A[i]=t[i])}return A},u=Q({},a,{name:"VeRadar",data:function(){return this.chartHandler=I,{}}});A.exports=u},85816:function(A,e,t){"use strict";var i=t(57847)["default"];function n(A){return A&&"object"===i(A)&&"default"in A?A["default"]:A}var o=t(8993),r=t(87279),a=t(55925);t(89174);var s=n(t(82445)),l=Object.assign||function(A){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(A[i]=t[i])}return A},g=100,c=[80,100],I=[20,100],Q=200;function u(A){var e=A.innerRows,t=A.dataType,i=A.percentShow,n=A.dimension,o=A.metrics,a=A.radius,s=A.offsetY,g=A.selectedMode,c=A.hoverAnimation,I=A.digit,Q=A.roseType,u=A.label,C=A.level,h=A.limitShowNum,d=A.isRing,B=A.labelLine,E=A.itemStyle,p=[],m={},f=[];C?(C.forEach((function(A,e){A.forEach((function(A){r.setArrayValue(m,A,e)}))})),e.forEach((function(A){var e=m[A[n]];e&&e.length&&e.forEach((function(e){r.setArrayValue(f,e,A)}))}))):f.push(e);var y={type:"pie",selectedMode:g,hoverAnimation:c,roseType:Q,center:["50%",s]},b=f.length;if(f.forEach((function(A,e){var s=l({data:[]},y),g=a/b;if(e){var c=g+a/(2*b)*(2*e-1),Q=c+a/(2*b);s.radius=[c,Q]}else s.radius=d?a:g;b>1&&0===e&&(s.label={normal:{position:"inner"}}),u&&(s.label=u),B&&(s.labelLine=B),E&&(s.itemStyle=E),i&&(s.label={normal:{show:!0,position:b>1&&0===e?"inner":"outside",formatter:function(A){var e=[];return e.push(A.name+":"),e.push(r.getFormated(A.value,t,I)),e.push("("+A.percent+"%)"),e.join(" ")}}}),s.data=A.map((function(A){return{name:A[n],value:A[o]}})),p.push(s)})),h&&h<p[0].data.length){var v=p[0].data,w=v.slice(h,v.length),N=0;w.forEach((function(A){N+=A.value})),p[0].data=v.slice(0,h),p[0].data.push({name:"其他",value:N})}return p}function C(A){var e=A.innerRows,t=A.dimension,i=A.legendLimit,n=A.legendName,o=A.level,r=A.limitShowNum,a=[],s=[];if(o)o.forEach((function(A){A.forEach((function(A){s.push(A)}))})),a=s;else if(r&&r<e.length){for(var l=0;l<r;l++)a.push(e[l][t]);a.push("其他")}else a=e.map((function(A){return A[t]}));return!!a.length&&{data:a,show:a.length<i,formatter:function(A){return null!=n[A]?n[A]:A}}}function h(A){var e=A.dataType,t=A.innerRows,i=A.limitShowNum,n=A.digit,a=A.metrics,s=A.dimension,l=0,g=t.map((function(A){return l+=A[a],{name:A[s],value:A[a]}})).slice(i,t.length);return{formatter:function(A){var t=[];return t.push(o.itemPoint(A.color)),i&&"其他"===A.name?(t.push("其他:"),g.forEach((function(A){var i=A.name,o=A.value,a=r.getFormated(o/l,"percent");t.push("<br>"+i+":"),t.push(r.getFormated(o,e,n)),t.push("("+a+")")}))):(t.push(A.name+":"),t.push(r.getFormated(A.value,e,n)),t.push("("+A.percent+"%)")),t.join(" ")}}}var d=function(A,e,t,i,n){var o=a.cloneDeep(e),r=t.dataType,s=void 0===r?"normal":r,l=t.percentShow,d=t.dimension,B=void 0===d?A[0]:d,E=t.metrics,p=void 0===E?A[1]:E,m=t.roseType,f=void 0!==m&&m,y=t.radius,b=void 0===y?n?f?I:c:g:y,v=t.offsetY,w=void 0===v?Q:v,N=t.legendLimit,D=void 0===N?30:N,Y=t.selectedMode,M=void 0!==Y&&Y,k=t.hoverAnimation,O=void 0===k||k,x=t.digit,G=void 0===x?2:x,j=t.legendName,Z=void 0===j?{}:j,F=t.label,J=void 0!==F&&F,S=t.level,R=void 0!==S&&S,z=t.limitShowNum,T=void 0===z?0:z,X=t.labelLine,U=t.itemStyle,W=i.tooltipVisible,V=i.legendVisible;T&&o.sort((function(A,e){return e[p]-A[p]}));var _={innerRows:o,dataType:s,percentShow:l,dimension:B,metrics:p,radius:b,offsetY:w,selectedMode:M,hoverAnimation:O,digit:G,roseType:f,label:J,level:R,legendName:Z,limitShowNum:T,isRing:n,labelLine:X,itemStyle:U},L=u(_),H={innerRows:o,dimension:B,legendLimit:D,legendName:Z,level:R,limitShowNum:T},K=V&&C(H),q=W&&h({dataType:s,innerRows:o,limitShowNum:T,digit:G,metrics:p,dimension:B}),P={series:L,legend:K,tooltip:q};return P},B=function(A,e,t,i){return d(A,e,t,i,!0)},E=l({},s,{name:"VeRing",data:function(){return this.chartHandler=B,{}}});A.exports=E},96599:function(A,e,t){"use strict";var i=t(57847)["default"];function n(A){return A&&"object"===i(A)&&"default"in A?A["default"]:A}var o=t(87279),r=t(8993);t(9669);var a=n(t(82445)),s=Object.assign||function(A){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(A[i]=t[i])}return A};function l(A){var e=A.itemDataType,t=A.linksDataType,i=A.digit;return{trigger:"item",formatter:function(A){var n=[],a=A.name,s=A.data,l=A.value,g=A.color;return n.push(r.itemPoint(g)),n.push(a+" : "),s&&s.source?n.push(o.getFormated(l,t,i)+"<br />"):n.push(o.getFormated(l,e,i)+"<br />"),n.join("")}}}function g(A){var e=A.rows,t=A.dimension,i=A.metrics,n=A.links,o=A.valueFull,r=A.useDataValue,a=A.label,l=A.itemStyle,g=A.lineStyle,c={},I=e.map((function(A){return c[A[t]]=A[i],{name:A[t],value:A[i]}})),Q=null;Q=r?n.map((function(A){return s({},A,{value:c[A.target]})})):o?n:n.map((function(A){return null==A.value?s({},A,{value:c[A.target]}):A}));var u={type:"sankey",data:I,links:Q};return a&&(u.label=a),l&&(u.itemStyle=l),g&&(u.lineStyle=g),[u]}var c=function(A,e,t,i){var n=t.links,o=t.dimension,r=void 0===o?A[0]:o,a=t.metrics,s=void 0===a?A[1]:a,c=t.dataType,I=void 0===c?["normal","normal"]:c,Q=t.digit,u=void 0===Q?2:Q,C=t.valueFull,h=void 0!==C&&C,d=t.useDataValue,B=void 0!==d&&d,E=t.label,p=t.itemStyle,m=t.lineStyle;if(n){var f=I[0],y=I[1],b=l({itemDataType:f,linksDataType:y,digit:u}),v=g({rows:e,dimension:r,metrics:s,links:n,valueFull:h,useDataValue:B,label:E,itemStyle:p,lineStyle:m});return{tooltip:b,series:v}}},I=s({},a,{name:"VeSankey",data:function(){return this.chartHandler=c,{}}});A.exports=I},27568:function(A,e,t){"use strict";var i=t(57847)["default"];function n(A){return A&&"object"===i(A)&&"default"in A?A["default"]:A}var o=t(87279),r=t(55925),a=t(8993);t(46413);var s=n(t(82445)),l=Object.assign||function(A){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(A[i]=t[i])}return A};function g(A){var e=A.dimension,t=A.rows,i=A.xAxisName,n=A.axisVisible,o=A.xAxisType;return e.map((function(A,e){return{type:o,nameLocation:"middle",nameGap:22,name:i[e]||"",axisTick:{show:!0,lineStyle:{color:"#eee"}},data:t.map((function(e){return e[A]})),show:n}}))}function c(A){var e=A.rows,t=A.axisSite,i=A.metrics,n=A.area,r=A.stack,a=A.nullAddZero,s=A.labelMap,l=A.label,g=A.itemStyle,c=A.lineStyle,I=A.areaStyle,Q=A.dimension,u=[],C={},h=r&&o.getStackMap(r);return i.forEach((function(A){C[A]=[]})),e.forEach((function(A){i.forEach((function(e){var t=null;null!=A[e]?t=A[e]:a&&(t=0),C[e].push([A[Q[0]],t])}))})),i.forEach((function(A){var e={name:null!=s[A]?s[A]:A,type:"line",data:C[A]};n&&(e.areaStyle={normal:{}}),t.right&&(e.yAxisIndex=~t.right.indexOf(A)?1:0),r&&h[A]&&(e.stack=h[A]),l&&(e.label=l),g&&(e.itemStyle=g),c&&(e.lineStyle=c),I&&(e.areaStyle=I),u.push(e)})),u}function I(A){for(var e=A.yAxisName,t=A.yAxisType,i=A.axisVisible,n=A.scale,r=A.min,a=A.max,s=A.digit,g={type:"value",axisTick:{show:!1},show:i},c=[],I=function(A){t[A]?c[A]=l({},g,{axisLabel:{formatter:function(e){return o.getFormated(e,t[A],s)}}}):c[A]=l({},g),c[A].name=e[A]||"",c[A].scale=n[A]||!1,c[A].min=r[A]||null,c[A].max=a[A]||null},Q=0;Q<2;Q++)I(Q);return c}function Q(A){var e=A.axisSite,t=A.yAxisType,i=A.digit,n=A.labelMap,a=A.tooltipFormatter,s=e.right||[],l=n?s.map((function(A){return void 0===n[A]?A:n[A]})):s;return{trigger:"axis",formatter:function(A){if(a)return a.apply(null,arguments);var e=[],n=A[0],s=n.name,g=n.axisValueLabel,c=s||g;return e.push(c+"<br>"),A.forEach((function(A){var n=A.seriesName,a=A.data,s=A.marker,g=null,c=~l.indexOf(n)?t[1]:t[0],I=r.isArray(a)?a[1]:a;g=o.getFormated(I,c,i),e.push(s),e.push(n+": "+g),e.push("<br>")})),e.join("")}}}function u(A){var e=A.metrics,t=A.legendName,i=A.labelMap;if(!t&&!i)return{data:e};var n=i?e.map((function(A){return null==i[A]?A:i[A]})):e;return{data:n,formatter:function(A){return null!=t[A]?t[A]:A}}}var C=function(A,e,t,i){e=r.isArray(e)?e:[],A=r.isArray(A)?A:[];var n=t.axisSite,o=void 0===n?{}:n,a=t.yAxisType,s=void 0===a?["normal","normal"]:a,l=t.xAxisType,C=void 0===l?"category":l,h=t.yAxisName,d=void 0===h?[]:h,B=t.dimension,E=void 0===B?[A[0]]:B,p=t.xAxisName,m=void 0===p?[]:p,f=t.axisVisible,y=void 0===f||f,b=t.area,v=t.stack,w=t.scale,N=void 0===w?[!1,!1]:w,D=t.min,Y=void 0===D?[null,null]:D,M=t.max,k=void 0===M?[null,null]:M,O=t.nullAddZero,x=void 0!==O&&O,G=t.digit,j=void 0===G?2:G,Z=t.legendName,F=void 0===Z?{}:Z,J=t.labelMap,S=void 0===J?{}:J,R=t.label,z=t.itemStyle,T=t.lineStyle,X=t.areaStyle,U=i.tooltipVisible,W=i.legendVisible,V=i.tooltipFormatter,_=A.slice();o.left&&o.right?_=o.left.concat(o.right):o.left&&!o.right?_=o.left:t.metrics?_=t.metrics:_.splice(A.indexOf(E[0]),1);var L=W&&u({metrics:_,legendName:F,labelMap:S}),H=U&&Q({axisSite:o,yAxisType:s,digit:j,labelMap:S,xAxisType:C,tooltipFormatter:V}),K=g({dimension:E,rows:e,xAxisName:m,axisVisible:y,xAxisType:C}),q=I({yAxisName:d,yAxisType:s,axisVisible:y,scale:N,min:Y,max:k,digit:j}),P=c({rows:e,axisSite:o,metrics:_,area:b,stack:v,nullAddZero:x,labelMap:S,label:R,itemStyle:z,lineStyle:T,areaStyle:X,xAxisType:C,dimension:E}),$={legend:L,xAxis:K,series:P,yAxis:q,tooltip:H};return $};function h(A,e){return{data:A,formatter:function(A){return null!=e[A]?e[A]:A}}}function d(A){var e=A.tooltipTrigger;return{trigger:e,formatter:function(e){return r.isArray(e)?e.map((function(e){return B(e,A)})).join(""):B(e,A)}}}function B(A,e){var t=e.labelMap,i=e.columns,n=e.dataType,r=e.digit,s=[],l=A.color,g=A.seriesName,c=A.data.value;return s.push(a.itemPoint(l)+" "+g+"<br>"),c.forEach((function(A,e){var a=t[i[e]]||i[e],l=isNaN(A)?A:o.getFormated(A,n[i[e]],r);s.push(a+": "+l+"<br>")})),s.join("")}function E(A){var e=A.xAxisName,t=A.axisVisible,i=A.xAxisType,n=A.rows,o=A.dataLabels,r=A.dimension,a=[];return o.forEach((function(A){var e=n[A];e.forEach((function(A){var e=A[r];e&&!~a.indexOf(e)&&a.push(e)}))})),[{type:i,show:t,name:e,data:a}]}function p(A){var e=A.min,t=A.max,i=A.scale,n=A.yAxisName,r=A.dataType,a=A.metrics,s=A.digit,l=A.axisVisible;return{type:"value",show:l,scale:i,min:e,max:t,axisTick:{show:!1},name:n,axisLabel:{formatter:function(A){return o.getFormated(A,r[a[0]],s)}}}}function m(A){var e=A.rows,t=A.dataLabels,i=A.columns,n=A.metrics,o=A.dimension,r=A.label,a=A.itemStyle,s=A.symbol,l=A.symbolSizeMax,g=A.symbolSize,c=A.symbolRotate,I=A.symbolOffset,Q=A.cursor,u=i.filter((function(A){return!~n.indexOf(A)&&A!==o})),C=[];t.forEach((function(A){e[A].forEach((function(A){C.push(A[n[1]])}))}));var h=Math.max.apply(null,C),d=[];return t.forEach((function(A){var t=[],i=e[A];i.forEach((function(A){var e={value:[]};e.value.push(A[o],A[n[0]],A[n[1]]),u.forEach((function(t){e.value.push(A[t])})),e.symbolSize=g||A[n[1]]/h*l,t.push(e)})),d.push({type:"scatter",data:t,name:A,label:r,itemStyle:a,symbol:s,symbolRotate:c,symbolOffset:I,cursor:Q})})),d}var f=function(A,e,t,i){var n=t.dimension,o=void 0===n?A[0]:n,a=t.metrics,s=void 0===a?[A[1],A[2]]:a,g=t.dataType,c=void 0===g?{}:g,I=t.xAxisType,Q=void 0===I?"category":I,u=t.xAxisName,B=t.yAxisName,f=t.digit,y=void 0===f?2:f,b=t.legendName,v=void 0===b?{}:b,w=t.labelMap,N=void 0===w?{}:w,D=t.tooltipTrigger,Y=void 0===D?"item":D,M=t.axisVisible,k=void 0===M||M,O=t.symbolSizeMax,x=void 0===O?50:O,G=t.symbol,j=t.symbolSize,Z=t.symbolRotate,F=t.symbolOffset,J=t.cursor,S=t.min,R=t.max,z=t.scale,T=t.label,X=t.itemStyle;if(r.isArray(e)){var U=l({},t,{xAxisName:u?[u]:void 0,yAxisName:B?[B]:void 0,scale:z?[z]:void 0,min:S?[S]:void 0,max:R?[R]:void 0,dimension:o?[o]:void 0}),W=C(A,e,U,i);return W&&W.series?(W.series.forEach((function(A){l(A,{type:"scatter",symbol:G,symbolSize:j||10,symbolRotate:Z,symbolOffset:F,cursor:J,label:T,itemStyle:X})})),W):{}}var V=i.tooltipVisible,_=i.legendVisible,L=Object.keys(e),H=_&&h(L,v),K=V&&d({tooltipTrigger:Y,labelMap:N,columns:A,dataType:c,digit:y}),q=E({xAxisName:u,axisVisible:k,xAxisType:Q,dataLabels:L,dimension:o,rows:e}),P=p({min:S,max:R,scale:z,yAxisName:B,dataType:c,metrics:s,digit:y,axisVisible:k}),$=m({rows:e,dataLabels:L,columns:A,metrics:s,dimension:o,label:T,itemStyle:X,symbol:G,symbolSizeMax:x,symbolSize:j,symbolRotate:Z,symbolOffset:F,cursor:J});return{legend:H,tooltip:K,xAxis:q,yAxis:P,series:$}},y=l({},s,{name:"VeScatter",data:function(){return this.chartHandler=f,{}}});A.exports=y},6685:function(A,e,t){"use strict";var i=t(57847)["default"];function n(A){return A&&"object"===i(A)&&"default"in A?A["default"]:A}var o=t(55925);t(12107);var r=n(t(82445)),a=Object.assign||function(A){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(A[i]=t[i])}return A};function s(A){var e=A.dimension,t=A.rows,i=t.map((function(A){return A[e]}));return{data:i}}function l(A){var e=A.tooltipFormatter;return{trigger:"item",triggerOn:"mousemove",formatter:e}}function g(A){var e=A.dimension,t=A.metrics,i=A.rows,n=A.seriesMap,r=[];return i.forEach((function(A){var i=A[e],s=n[i],l={type:"tree",name:A[e],data:A[t]};n[A[e]]&&Object.keys(s).forEach((function(A){o.isObject(l[A])?a(l[A],s[A]):l[A]=s[A]})),r.push(l)})),r}var c=function(A,e,t,i){var n=t.dimension,o=void 0===n?A[0]:n,r=t.metrics,a=void 0===r?A[1]:r,c=t.seriesMap,I=void 0===c?{}:c,Q=i.legendVisible,u=i.tooltipFormatter,C=i.tooltipVisible,h=g({dimension:o,metrics:a,rows:e,seriesMap:I}),d=Q&&e.length>1&&s({dimension:o,rows:e}),B=C&&l({tooltipFormatter:u});return{series:h,legend:d,tooltip:B}},I=a({},r,{name:"VeTree",data:function(){return this.chartHandler=c,{}}});A.exports=I},87279:function(A,e,t){"use strict";var i=t(57847)["default"];function n(A){return A&&"object"===i(A)&&"default"in A?A["default"]:A}Object.defineProperty(e,"__esModule",{value:!0});var o=n(t(68572)),r=t(55925),a=function(A,e,t){var i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"-";if(isNaN(A))return i;if(!e)return A;if(r.isFunction(e))return e(A,o);t=isNaN(t)?0:++t;var n=".["+new Array(t).join(0)+"]",a=e;switch(e){case"KMB":a=t?"0,0"+n+"a":"0,0a";break;case"normal":a=t?"0,0"+n:"0,0";break;case"percent":a=t?"0,0"+n+"%":"0,0.[00]%";break}return o(A,a)},s=function(A){var e={};return Object.keys(A).forEach((function(t){A[t].forEach((function(A){e[A]=t}))})),e},l=function(A){return new Promise((function(e,t){var i=new XMLHttpRequest;i.open("GET",A),i.send(null),i.onload=function(){e(JSON.parse(i.responseText))},i.onerror=function(){t(JSON.parse(i.responseText))}}))},g={},c=function(A){var e=A.position,t=A.positionJsonLink,i=A.beforeRegisterMapOnce,n=A.mapURLProfix,o=t||""+n+e+".json";return g[o]||(g[o]=l(o).then((function(A){return i&&(A=i(A)),A}))),g[o]},I=null,Q=null,u=function(A,e){return I||(I=new Promise((function(t,i){var n="bmap"+Date.now();window[n]=t;var o=document.createElement("script");o.src=["https://api.map.baidu.com/api?v="+(e||"2.0"),"ak="+A,"callback="+n].join("&"),document.body.appendChild(o)}))),I},C=function(A,e){return Q||(Q=new Promise((function(t,i){var n="amap"+Date.now();window[n]=t;var o=document.createElement("script");o.src=["https://webapi.amap.com/maps?v="+(e||"1.4.3"),"key="+A,"callback="+n].join("&"),document.body.appendChild(o)}))),Q};function h(A,e,t){void 0!==A[e]?A[e].push(t):A[e]=[t]}e.getFormated=a,e.getStackMap=s,e.$get=l,e.getMapJSON=c,e.getBmap=u,e.getAmap=C,e.setArrayValue=h},97577:function(A,e,t){"use strict";var i=t(57847)["default"];function n(A){return A&&"object"===i(A)&&"default"in A?A["default"]:A}var o=t(87279);t(19467);var r=n(t(82445)),a=Object.assign||function(A){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(A[i]=t[i])}return A};function s(A,e){return{trigger:"axis",axisPointer:{type:"shadow"},formatter:function(t){var i=t[1];return[i.name+"<br/>"+i.seriesName+" :",""+o.getFormated(i.value,A,e)].join("")}}}function l(A){var e=A.dimension,t=A.rows,i=A.remainStatus,n=A.totalName,o=A.remainName,r=A.labelMap,a=A.xAxisName,s=A.axisVisible,l=[n].concat(t.map((function(A){return A[e]})));return"have-remain"===i&&(l=l.concat([o])),{type:"category",name:r&&r[a]||a,splitLine:{show:!1},data:l,show:s}}function g(A){var e=A.dataType,t=A.yAxisName,i=A.axisVisible,n=A.digit,r=A.labelMap;return{type:"value",name:null!=r[t]?r[t]:t,axisTick:{show:!1},axisLabel:{formatter:function(A){return o.getFormated(A,e,n)}},show:i}}function c(A){var e=A.dataType,t=A.rows,i=A.metrics,n=A.totalNum,r=A.remainStatus,s=A.dataSum,l=A.digit,g={type:"bar",stack:"总量"},c=s,I=n,Q=void 0,u=void 0,C=t.map((function(A){return A[i]}));"have-remain"===r?(Q=[0].concat(t.map((function(A){return I-=A[i],I}))).concat([0]),u=[n].concat(C).concat([n-s])):(Q=[0].concat(t.map((function(A){return c-=A[i],c}))),u=[s].concat(C));var h=[];return h.push(a({name:"辅助",itemStyle:{normal:{opacity:0},emphasis:{opacity:0}},data:Q},g)),h.push(a({name:"数值",label:{normal:{show:!0,position:"top",formatter:function(A){return o.getFormated(A.value,e,l)}}},data:u},g)),h}function I(A,e){return e?e>A?"have-remain":"none-remain":"not-total"}var Q=function(A,e,t,i){var n=t.dataType,o=void 0===n?"normal":n,r=t.dimension,a=void 0===r?A[0]:r,Q=t.totalName,u=void 0===Q?"总计":Q,C=t.totalNum,h=t.remainName,d=void 0===h?"其他":h,B=t.xAxisName,E=void 0===B?a:B,p=t.labelMap,m=void 0===p?{}:p,f=t.axisVisible,y=void 0===f||f,b=t.digit,v=void 0===b?2:b,w=i.tooltipVisible,N=A.slice();N.splice(N.indexOf(a),1);var D=N[0],Y=D,M=w&&s(o,v),k=parseFloat(e.reduce((function(A,e){return A+Number(e[D])}),0).toFixed(v)),O=I(k,C),x={dimension:a,rows:e,remainStatus:O,totalName:u,remainName:d,xAxisName:E,labelMap:m,axisVisible:y},G=l(x),j=g({dataType:o,yAxisName:Y,axisVisible:y,digit:v,labelMap:m}),Z={dataType:o,rows:e,dimension:a,metrics:D,totalNum:C,remainStatus:O,dataSum:k,digit:v},F=c(Z),J={tooltip:M,xAxis:G,yAxis:j,series:F};return J},u=a({},r,{name:"VeWaterfall",data:function(){return this.chartHandler=Q,{}}});A.exports=u},44469:function(A,e,t){"use strict";var i=t(57847)["default"];function n(A){return A&&"object"===i(A)&&"default"in A?A["default"]:A}var o=t(55925);t(8856);var r=n(t(82445));function a(A){var e=A.dimension,t=A.metrics,i=A.rows,n=A.color,r=A.sizeMax,a=A.sizeMin,s=A.shape,l={type:"wordCloud",textStyle:{normal:{color:!o.isArray(n)&&n?n:function(){return"rgb("+[Math.round(160*Math.random()),Math.round(160*Math.random()),Math.round(160*Math.random())].join(",")+")"}}},shape:s,sizeRange:[a,r]},g=o.isArray(n)?n.length:0,c=i.slice().map((function(A){var i={name:A[e],value:A[t]};return g>0&&(i.textStyle={normal:{color:n[Math.floor(Math.random()*g)]}}),i}));return l.data=c,[l]}function s(A){var e=A.tooltipFormatter;return{show:!0,formatter:function(A){var t=A.data,i=t.name,n=t.value;return e?e.apply(null,A):i+": "+n}}}var l=function(A,e,t,i){var n=t.dimension,o=void 0===n?A[0]:n,r=t.metrics,l=void 0===r?A[1]:r,g=t.color,c=void 0===g?"":g,I=t.sizeMax,Q=void 0===I?60:I,u=t.sizeMin,C=void 0===u?12:u,h=t.shape,d=void 0===h?"circle":h,B=i.tooltipVisible,E=i.tooltipFormatter,p=a({dimension:o,metrics:l,rows:e,color:c,sizeMax:Q,sizeMin:C,shape:d}),m=B&&s({tooltipFormatter:E});return{series:p,tooltip:m}},g=Object.assign||function(A){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(A[i]=t[i])}return A},c=g({},r,{name:"VeWordcloud",data:function(){return this.chartHandler=l,{}}});A.exports=c},43099:function(A,e,t){var i,n,o=t(57847)["default"];t(65743),t(32564),
/*!
 * Viewer.js v1.11.2
 * https://fengyuanchen.github.io/viewerjs
 *
 * Copyright 2015-present Chen Fengyuan
 * Released under the MIT license
 *
 * Date: 2023-01-01T10:14:49.638Z
 */
function(r,a){"object"===o(e)?A.exports=a():(i=a,n="function"===typeof i?i.call(e,t,e,A):i,void 0===n||(A.exports=n))}(0,(function(){"use strict";function A(A,e){var t=Object.keys(A);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(A);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(A,e).enumerable}))),t.push.apply(t,i)}return t}function e(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?A(Object(i),!0).forEach((function(A){a(e,A,i[A])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):A(Object(i)).forEach((function(A){Object.defineProperty(e,A,Object.getOwnPropertyDescriptor(i,A))}))}return e}function t(A){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(A){return typeof A}:function(A){return A&&"function"==typeof Symbol&&A.constructor===Symbol&&A!==Symbol.prototype?"symbol":typeof A},t(A)}function i(A,e){if(!(A instanceof e))throw new TypeError("Cannot call a class as a function")}function n(A,e){for(var t=0;t<e.length;t++){var i=e[t];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(A,l(i.key),i)}}function r(A,e,t){return e&&n(A.prototype,e),t&&n(A,t),Object.defineProperty(A,"prototype",{writable:!1}),A}function a(A,e,t){return e=l(e),e in A?Object.defineProperty(A,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):A[e]=t,A}function s(A,e){if("object"!==o(A)||null===A)return A;var t=A[Symbol.toPrimitive];if(void 0!==t){var i=t.call(A,e||"default");if("object"!==o(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(A)}function l(A){var e=s(A,"string");return"symbol"===o(e)?e:String(e)}var g={backdrop:!0,button:!0,navbar:!0,title:!0,toolbar:!0,className:"",container:"body",filter:null,fullscreen:!0,inheritedAttributes:["crossOrigin","decoding","isMap","loading","referrerPolicy","sizes","srcset","useMap"],initialCoverage:.9,initialViewIndex:0,inline:!1,interval:5e3,keyboard:!0,focus:!0,loading:!0,loop:!0,minWidth:200,minHeight:100,movable:!0,rotatable:!0,scalable:!0,zoomable:!0,zoomOnTouch:!0,zoomOnWheel:!0,slideOnTouch:!0,toggleOnDblclick:!0,tooltip:!0,transition:!0,zIndex:2015,zIndexInline:0,zoomRatio:.1,minZoomRatio:.01,maxZoomRatio:100,url:"src",ready:null,show:null,shown:null,hide:null,hidden:null,view:null,viewed:null,move:null,moved:null,rotate:null,rotated:null,scale:null,scaled:null,zoom:null,zoomed:null,play:null,stop:null},c='<div class="viewer-container" tabindex="-1" touch-action="none"><div class="viewer-canvas"></div><div class="viewer-footer"><div class="viewer-title"></div><div class="viewer-toolbar"></div><div class="viewer-navbar"><ul class="viewer-list" role="navigation"></ul></div></div><div class="viewer-tooltip" role="alert" aria-hidden="true"></div><div class="viewer-button" data-viewer-action="mix" role="button"></div><div class="viewer-player"></div></div>',I="undefined"!==typeof window&&"undefined"!==typeof window.document,Q=I?window:{},u=!(!I||!Q.document.documentElement)&&"ontouchstart"in Q.document.documentElement,C=!!I&&"PointerEvent"in Q,h="viewer",d="move",B="switch",E="zoom",p="".concat(h,"-active"),m="".concat(h,"-close"),f="".concat(h,"-fade"),y="".concat(h,"-fixed"),b="".concat(h,"-fullscreen"),v="".concat(h,"-fullscreen-exit"),w="".concat(h,"-hide"),N="".concat(h,"-hide-md-down"),D="".concat(h,"-hide-sm-down"),Y="".concat(h,"-hide-xs-down"),M="".concat(h,"-in"),k="".concat(h,"-invisible"),O="".concat(h,"-loading"),x="".concat(h,"-move"),G="".concat(h,"-open"),j="".concat(h,"-show"),Z="".concat(h,"-transition"),F="click",J="dblclick",S="dragstart",R="focusin",z="keydown",T="load",X="error",U=u?"touchend touchcancel":"mouseup",W=u?"touchmove":"mousemove",V=u?"touchstart":"mousedown",_=C?"pointerdown":V,L=C?"pointermove":W,H=C?"pointerup pointercancel":U,K="resize",q="transitionend",P="wheel",$="ready",AA="show",eA="shown",tA="hide",iA="hidden",nA="view",oA="viewed",rA="move",aA="moved",sA="rotate",lA="rotated",gA="scale",cA="scaled",IA="zoom",QA="zoomed",uA="play",CA="stop",hA="".concat(h,"Action"),dA=/\s\s*/,BA=["zoom-in","zoom-out","one-to-one","reset","prev","play","next","rotate-left","rotate-right","flip-horizontal","flip-vertical"];function EA(A){return"string"===typeof A}var pA=Number.isNaN||Q.isNaN;function mA(A){return"number"===typeof A&&!pA(A)}function fA(A){return"undefined"===typeof A}function yA(A){return"object"===t(A)&&null!==A}var bA=Object.prototype.hasOwnProperty;function vA(A){if(!yA(A))return!1;try{var e=A.constructor,t=e.prototype;return e&&t&&bA.call(t,"isPrototypeOf")}catch(i){return!1}}function wA(A){return"function"===typeof A}function NA(A,e){if(A&&wA(e))if(Array.isArray(A)||mA(A.length)){var t,i=A.length;for(t=0;t<i;t+=1)if(!1===e.call(A,A[t],t,A))break}else yA(A)&&Object.keys(A).forEach((function(t){e.call(A,A[t],t,A)}));return A}var DA=Object.assign||function(A){for(var e=arguments.length,t=new Array(e>1?e-1:0),i=1;i<e;i++)t[i-1]=arguments[i];return yA(A)&&t.length>0&&t.forEach((function(e){yA(e)&&Object.keys(e).forEach((function(t){A[t]=e[t]}))})),A},YA=/^(?:width|height|left|top|marginLeft|marginTop)$/;function MA(A,e){var t=A.style;NA(e,(function(A,e){YA.test(e)&&mA(A)&&(A+="px"),t[e]=A}))}function kA(A){return EA(A)?A.replace(/&(?!amp;|quot;|#39;|lt;|gt;)/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&#39;").replace(/</g,"&lt;").replace(/>/g,"&gt;"):A}function OA(A,e){return!(!A||!e)&&(A.classList?A.classList.contains(e):A.className.indexOf(e)>-1)}function xA(A,e){if(A&&e)if(mA(A.length))NA(A,(function(A){xA(A,e)}));else if(A.classList)A.classList.add(e);else{var t=A.className.trim();t?t.indexOf(e)<0&&(A.className="".concat(t," ").concat(e)):A.className=e}}function GA(A,e){A&&e&&(mA(A.length)?NA(A,(function(A){GA(A,e)})):A.classList?A.classList.remove(e):A.className.indexOf(e)>=0&&(A.className=A.className.replace(e,"")))}function jA(A,e,t){e&&(mA(A.length)?NA(A,(function(A){jA(A,e,t)})):t?xA(A,e):GA(A,e))}var ZA=/([a-z\d])([A-Z])/g;function FA(A){return A.replace(ZA,"$1-$2").toLowerCase()}function JA(A,e){return yA(A[e])?A[e]:A.dataset?A.dataset[e]:A.getAttribute("data-".concat(FA(e)))}function SA(A,e,t){yA(t)?A[e]=t:A.dataset?A.dataset[e]=t:A.setAttribute("data-".concat(FA(e)),t)}var RA=function(){var A=!1;if(I){var e=!1,t=function(){},i=Object.defineProperty({},"once",{get:function(){return A=!0,e},set:function(A){e=A}});Q.addEventListener("test",t,i),Q.removeEventListener("test",t,i)}return A}();function zA(A,e,t){var i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},n=t;e.trim().split(dA).forEach((function(e){if(!RA){var o=A.listeners;o&&o[e]&&o[e][t]&&(n=o[e][t],delete o[e][t],0===Object.keys(o[e]).length&&delete o[e],0===Object.keys(o).length&&delete A.listeners)}A.removeEventListener(e,n,i)}))}function TA(A,e,t){var i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},n=t;e.trim().split(dA).forEach((function(e){if(i.once&&!RA){var o=A.listeners,r=void 0===o?{}:o;n=function(){delete r[e][t],A.removeEventListener(e,n,i);for(var o=arguments.length,a=new Array(o),s=0;s<o;s++)a[s]=arguments[s];t.apply(A,a)},r[e]||(r[e]={}),r[e][t]&&A.removeEventListener(e,r[e][t],i),r[e][t]=n,A.listeners=r}A.addEventListener(e,n,i)}))}function XA(A,t,i,n){var o;return wA(Event)&&wA(CustomEvent)?o=new CustomEvent(t,e({bubbles:!0,cancelable:!0,detail:i},n)):(o=document.createEvent("CustomEvent"),o.initCustomEvent(t,!0,!0,i)),A.dispatchEvent(o)}function UA(A){var e=A.getBoundingClientRect();return{left:e.left+(window.pageXOffset-document.documentElement.clientLeft),top:e.top+(window.pageYOffset-document.documentElement.clientTop)}}function WA(A){var e=A.rotate,t=A.scaleX,i=A.scaleY,n=A.translateX,o=A.translateY,r=[];mA(n)&&0!==n&&r.push("translateX(".concat(n,"px)")),mA(o)&&0!==o&&r.push("translateY(".concat(o,"px)")),mA(e)&&0!==e&&r.push("rotate(".concat(e,"deg)")),mA(t)&&1!==t&&r.push("scaleX(".concat(t,")")),mA(i)&&1!==i&&r.push("scaleY(".concat(i,")"));var a=r.length?r.join(" "):"none";return{WebkitTransform:a,msTransform:a,transform:a}}function VA(A){return EA(A)?decodeURIComponent(A.replace(/^.*\//,"").replace(/[?&#].*$/,"")):""}var _A=Q.navigator&&/(Macintosh|iPhone|iPod|iPad).*AppleWebKit/i.test(Q.navigator.userAgent);function LA(A,e,t){var i=document.createElement("img");if(A.naturalWidth&&!_A)return t(A.naturalWidth,A.naturalHeight),i;var n=document.body||document.documentElement;return i.onload=function(){t(i.width,i.height),_A||n.removeChild(i)},NA(e.inheritedAttributes,(function(e){var t=A.getAttribute(e);null!==t&&i.setAttribute(e,t)})),i.src=A.src,_A||(i.style.cssText="left:0;max-height:none!important;max-width:none!important;min-height:0!important;min-width:0!important;opacity:0;position:absolute;top:0;z-index:-1;",n.appendChild(i)),i}function HA(A){switch(A){case 2:return Y;case 3:return D;case 4:return N;default:return""}}function KA(A){var t=e({},A),i=[];return NA(A,(function(A,e){delete t[e],NA(t,(function(e){var t=Math.abs(A.startX-e.startX),n=Math.abs(A.startY-e.startY),o=Math.abs(A.endX-e.endX),r=Math.abs(A.endY-e.endY),a=Math.sqrt(t*t+n*n),s=Math.sqrt(o*o+r*r),l=(s-a)/a;i.push(l)}))})),i.sort((function(A,e){return Math.abs(A)<Math.abs(e)})),i[0]}function qA(A,t){var i=A.pageX,n=A.pageY,o={endX:i,endY:n};return t?o:e({timeStamp:Date.now(),startX:i,startY:n},o)}function PA(A){var e=0,t=0,i=0;return NA(A,(function(A){var n=A.startX,o=A.startY;e+=n,t+=o,i+=1})),e/=i,t/=i,{pageX:e,pageY:t}}var $A={render:function(){this.initContainer(),this.initViewer(),this.initList(),this.renderViewer()},initBody:function(){var A=this.element.ownerDocument,e=A.body||A.documentElement;this.body=e,this.scrollbarWidth=window.innerWidth-A.documentElement.clientWidth,this.initialBodyPaddingRight=e.style.paddingRight,this.initialBodyComputedPaddingRight=window.getComputedStyle(e).paddingRight},initContainer:function(){this.containerData={width:window.innerWidth,height:window.innerHeight}},initViewer:function(){var A,e=this.options,t=this.parent;e.inline&&(A={width:Math.max(t.offsetWidth,e.minWidth),height:Math.max(t.offsetHeight,e.minHeight)},this.parentData=A),!this.fulled&&A||(A=this.containerData),this.viewerData=DA({},A)},renderViewer:function(){this.options.inline&&!this.fulled&&MA(this.viewer,this.viewerData)},initList:function(){var A=this,e=this.element,t=this.options,i=this.list,n=[];i.innerHTML="",NA(this.images,(function(e,o){var r=e.src,a=e.alt||VA(r),s=A.getImageURL(e);if(r||s){var l=document.createElement("li"),g=document.createElement("img");NA(t.inheritedAttributes,(function(A){var t=e.getAttribute(A);null!==t&&g.setAttribute(A,t)})),t.navbar&&(g.src=r||s),g.alt=a,g.setAttribute("data-original-url",s||r),l.setAttribute("data-index",o),l.setAttribute("data-viewer-action","view"),l.setAttribute("role","button"),t.keyboard&&l.setAttribute("tabindex",0),l.appendChild(g),i.appendChild(l),n.push(l)}})),this.items=n,NA(n,(function(e){var i,n,o=e.firstElementChild;SA(o,"filled",!0),t.loading&&xA(e,O),TA(o,T,i=function(i){zA(o,X,n),t.loading&&GA(e,O),A.loadImage(i)},{once:!0}),TA(o,X,n=function(){zA(o,T,i),t.loading&&GA(e,O)},{once:!0})})),t.transition&&TA(e,oA,(function(){xA(i,Z)}),{once:!0})},renderList:function(){var A=this.index,e=this.items[A];if(e){var t=e.nextElementSibling,i=parseInt(window.getComputedStyle(t||e).marginLeft,10),n=e.offsetWidth,o=n+i;MA(this.list,DA({width:o*this.length-i},WA({translateX:(this.viewerData.width-n)/2-o*A})))}},resetList:function(){var A=this.list;A.innerHTML="",GA(A,Z),MA(A,WA({translateX:0}))},initImage:function(A){var e,t=this,i=this.options,n=this.image,o=this.viewerData,r=this.footer.offsetHeight,a=o.width,s=Math.max(o.height-r,r),l=this.imageData||{};this.imageInitializing={abort:function(){e.onload=null}},e=LA(n,i,(function(e,n){var o=e/n,r=Math.max(0,Math.min(1,i.initialCoverage)),g=a,c=s;t.imageInitializing=!1,s*o>a?c=a/o:g=s*o,r=mA(r)?r:.9,g=Math.min(g*r,e),c=Math.min(c*r,n);var I=(a-g)/2,Q=(s-c)/2,u={left:I,top:Q,x:I,y:Q,width:g,height:c,oldRatio:1,ratio:g/e,aspectRatio:o,naturalWidth:e,naturalHeight:n},C=DA({},u);i.rotatable&&(u.rotate=l.rotate||0,C.rotate=0),i.scalable&&(u.scaleX=l.scaleX||1,u.scaleY=l.scaleY||1,C.scaleX=1,C.scaleY=1),t.imageData=u,t.initialImageData=C,A&&A()}))},renderImage:function(A){var e=this,t=this.image,i=this.imageData;if(MA(t,DA({width:i.width,height:i.height,marginLeft:i.x,marginTop:i.y},WA(i))),A)if((this.viewing||this.moving||this.rotating||this.scaling||this.zooming)&&this.options.transition&&OA(t,Z)){var n=function(){e.imageRendering=!1,A()};this.imageRendering={abort:function(){zA(t,q,n)}},TA(t,q,n,{once:!0})}else A()},resetImage:function(){if(this.viewing||this.viewed){var A=this.image;this.viewing&&this.viewing.abort(),A.parentNode.removeChild(A),this.image=null}}},Ae={bind:function(){var A=this.options,e=this.viewer,t=this.canvas,i=this.element.ownerDocument;TA(e,F,this.onClick=this.click.bind(this)),TA(e,S,this.onDragStart=this.dragstart.bind(this)),TA(t,_,this.onPointerDown=this.pointerdown.bind(this)),TA(i,L,this.onPointerMove=this.pointermove.bind(this)),TA(i,H,this.onPointerUp=this.pointerup.bind(this)),TA(i,z,this.onKeyDown=this.keydown.bind(this)),TA(window,K,this.onResize=this.resize.bind(this)),A.zoomable&&A.zoomOnWheel&&TA(e,P,this.onWheel=this.wheel.bind(this),{passive:!1,capture:!0}),A.toggleOnDblclick&&TA(t,J,this.onDblclick=this.dblclick.bind(this))},unbind:function(){var A=this.options,e=this.viewer,t=this.canvas,i=this.element.ownerDocument;zA(e,F,this.onClick),zA(e,S,this.onDragStart),zA(t,_,this.onPointerDown),zA(i,L,this.onPointerMove),zA(i,H,this.onPointerUp),zA(i,z,this.onKeyDown),zA(window,K,this.onResize),A.zoomable&&A.zoomOnWheel&&zA(e,P,this.onWheel,{passive:!1,capture:!0}),A.toggleOnDblclick&&zA(t,J,this.onDblclick)}},ee={click:function(A){var e=this.options,t=this.imageData,i=A.target,n=JA(i,hA);switch(n||"img"!==i.localName||"li"!==i.parentElement.localName||(i=i.parentElement,n=JA(i,hA)),u&&A.isTrusted&&i===this.canvas&&clearTimeout(this.clickCanvasTimeout),n){case"mix":this.played?this.stop():e.inline?this.fulled?this.exit():this.full():this.hide();break;case"hide":this.pointerMoved||this.hide();break;case"view":this.view(JA(i,"index"));break;case"zoom-in":this.zoom(.1,!0);break;case"zoom-out":this.zoom(-.1,!0);break;case"one-to-one":this.toggle();break;case"reset":this.reset();break;case"prev":this.prev(e.loop);break;case"play":this.play(e.fullscreen);break;case"next":this.next(e.loop);break;case"rotate-left":this.rotate(-90);break;case"rotate-right":this.rotate(90);break;case"flip-horizontal":this.scaleX(-t.scaleX||-1);break;case"flip-vertical":this.scaleY(-t.scaleY||-1);break;default:this.played&&this.stop()}},dblclick:function(A){A.preventDefault(),this.viewed&&A.target===this.image&&(u&&A.isTrusted&&clearTimeout(this.doubleClickImageTimeout),this.toggle(A.isTrusted?A:A.detail&&A.detail.originalEvent))},load:function(){var A=this;this.timeout&&(clearTimeout(this.timeout),this.timeout=!1);var e=this.element,t=this.options,i=this.image,n=this.index,o=this.viewerData;GA(i,k),t.loading&&GA(this.canvas,O),i.style.cssText="height:0;"+"margin-left:".concat(o.width/2,"px;")+"margin-top:".concat(o.height/2,"px;")+"max-width:none!important;position:relative;width:0;",this.initImage((function(){jA(i,x,t.movable),jA(i,Z,t.transition),A.renderImage((function(){A.viewed=!0,A.viewing=!1,wA(t.viewed)&&TA(e,oA,t.viewed,{once:!0}),XA(e,oA,{originalImage:A.images[n],index:n,image:i},{cancelable:!1})}))}))},loadImage:function(A){var e=A.target,t=e.parentNode,i=t.offsetWidth||30,n=t.offsetHeight||50,o=!!JA(e,"filled");LA(e,this.options,(function(A,t){var r=A/t,a=i,s=n;n*r>i?o?a=n*r:s=i/r:o?s=i/r:a=n*r,MA(e,DA({width:a,height:s},WA({translateX:(i-a)/2,translateY:(n-s)/2})))}))},keydown:function(A){var e=this.options;if(e.keyboard){var t=A.keyCode||A.which||A.charCode;switch(t){case 13:this.viewer.contains(A.target)&&this.click(A);break}if(this.fulled)switch(t){case 27:this.played?this.stop():e.inline?this.fulled&&this.exit():this.hide();break;case 32:this.played&&this.stop();break;case 37:this.played&&this.playing?this.playing.prev():this.prev(e.loop);break;case 38:A.preventDefault(),this.zoom(e.zoomRatio,!0);break;case 39:this.played&&this.playing?this.playing.next():this.next(e.loop);break;case 40:A.preventDefault(),this.zoom(-e.zoomRatio,!0);break;case 48:case 49:A.ctrlKey&&(A.preventDefault(),this.toggle());break}}},dragstart:function(A){"img"===A.target.localName&&A.preventDefault()},pointerdown:function(A){var e=this.options,t=this.pointers,i=A.buttons,n=A.button;if(this.pointerMoved=!1,!(!this.viewed||this.showing||this.viewing||this.hiding||("mousedown"===A.type||"pointerdown"===A.type&&"mouse"===A.pointerType)&&(mA(i)&&1!==i||mA(n)&&0!==n||A.ctrlKey))){A.preventDefault(),A.changedTouches?NA(A.changedTouches,(function(A){t[A.identifier]=qA(A)})):t[A.pointerId||0]=qA(A);var o=!!e.movable&&d;e.zoomOnTouch&&e.zoomable&&Object.keys(t).length>1?o=E:e.slideOnTouch&&("touch"===A.pointerType||"touchstart"===A.type)&&this.isSwitchable()&&(o=B),!e.transition||o!==d&&o!==E||GA(this.image,Z),this.action=o}},pointermove:function(A){var e=this.pointers,t=this.action;this.viewed&&t&&(A.preventDefault(),this.pointerMoved=!0,A.changedTouches?NA(A.changedTouches,(function(A){DA(e[A.identifier]||{},qA(A,!0))})):DA(e[A.pointerId||0]||{},qA(A,!0)),this.change(A))},pointerup:function(A){var e,t=this,i=this.options,n=this.action,o=this.pointers;A.changedTouches?NA(A.changedTouches,(function(A){e=o[A.identifier],delete o[A.identifier]})):(e=o[A.pointerId||0],delete o[A.pointerId||0]),n&&(A.preventDefault(),!i.transition||n!==d&&n!==E||xA(this.image,Z),this.action=!1,u&&n!==E&&e&&Date.now()-e.timeStamp<500&&(clearTimeout(this.clickCanvasTimeout),clearTimeout(this.doubleClickImageTimeout),i.toggleOnDblclick&&this.viewed&&A.target===this.image?this.imageClicked?(this.imageClicked=!1,this.doubleClickImageTimeout=setTimeout((function(){XA(t.image,J,{originalEvent:A})}),50)):(this.imageClicked=!0,this.doubleClickImageTimeout=setTimeout((function(){t.imageClicked=!1}),500)):(this.imageClicked=!1,i.backdrop&&"static"!==i.backdrop&&A.target===this.canvas&&(this.clickCanvasTimeout=setTimeout((function(){XA(t.canvas,F,{originalEvent:A})}),50)))))},resize:function(){var A=this;if(this.isShown&&!this.hiding&&(this.fulled&&(this.close(),this.initBody(),this.open()),this.initContainer(),this.initViewer(),this.renderViewer(),this.renderList(),this.viewed&&this.initImage((function(){A.renderImage()})),this.played)){if(this.options.fullscreen&&this.fulled&&!(document.fullscreenElement||document.webkitFullscreenElement||document.mozFullScreenElement||document.msFullscreenElement))return void this.stop();NA(this.player.getElementsByTagName("img"),(function(e){TA(e,T,A.loadImage.bind(A),{once:!0}),XA(e,T)}))}},wheel:function(A){var e=this;if(this.viewed&&(A.preventDefault(),!this.wheeling)){this.wheeling=!0,setTimeout((function(){e.wheeling=!1}),50);var t=Number(this.options.zoomRatio)||.1,i=1;A.deltaY?i=A.deltaY>0?1:-1:A.wheelDelta?i=-A.wheelDelta/120:A.detail&&(i=A.detail>0?1:-1),this.zoom(-i*t,!0,null,A)}}},te={show:function(){var A=arguments.length>0&&void 0!==arguments[0]&&arguments[0],e=this.element,t=this.options;if(t.inline||this.showing||this.isShown||this.showing)return this;if(!this.ready)return this.build(),this.ready&&this.show(A),this;if(wA(t.show)&&TA(e,AA,t.show,{once:!0}),!1===XA(e,AA)||!this.ready)return this;this.hiding&&this.transitioning.abort(),this.showing=!0,this.open();var i=this.viewer;if(GA(i,w),i.setAttribute("role","dialog"),i.setAttribute("aria-labelledby",this.title.id),i.setAttribute("aria-modal",!0),i.removeAttribute("aria-hidden"),t.transition&&!A){var n=this.shown.bind(this);this.transitioning={abort:function(){zA(i,q,n),GA(i,M)}},xA(i,Z),i.initialOffsetWidth=i.offsetWidth,TA(i,q,n,{once:!0}),xA(i,M)}else xA(i,M),this.shown();return this},hide:function(){var A=this,e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=this.element,i=this.options;if(i.inline||this.hiding||!this.isShown&&!this.showing)return this;if(wA(i.hide)&&TA(t,tA,i.hide,{once:!0}),!1===XA(t,tA))return this;this.showing&&this.transitioning.abort(),this.hiding=!0,this.played?this.stop():this.viewing&&this.viewing.abort();var n=this.viewer,o=this.image,r=function(){GA(n,M),A.hidden()};if(i.transition&&!e){var a=function e(t){t&&t.target===n&&(zA(n,q,e),A.hidden())},s=function(){OA(n,Z)?(TA(n,q,a),GA(n,M)):r()};this.transitioning={abort:function(){A.viewed&&OA(o,Z)?zA(o,q,s):OA(n,Z)&&zA(n,q,a)}},this.viewed&&OA(o,Z)?(TA(o,q,s,{once:!0}),this.zoomTo(0,!1,null,null,!0)):s()}else r();return this},view:function(){var A=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.options.initialViewIndex;if(e=Number(e)||0,this.hiding||this.played||e<0||e>=this.length||this.viewed&&e===this.index)return this;if(!this.isShown)return this.index=e,this.show();this.viewing&&this.viewing.abort();var t=this.element,i=this.options,n=this.title,o=this.canvas,r=this.items[e],a=r.querySelector("img"),s=JA(a,"originalUrl"),l=a.getAttribute("alt"),g=document.createElement("img");if(NA(i.inheritedAttributes,(function(A){var e=a.getAttribute(A);null!==e&&g.setAttribute(A,e)})),g.src=s,g.alt=l,wA(i.view)&&TA(t,nA,i.view,{once:!0}),!1===XA(t,nA,{originalImage:this.images[e],index:e,image:g})||!this.isShown||this.hiding||this.played)return this;var c=this.items[this.index];c&&(GA(c,p),c.removeAttribute("aria-selected")),xA(r,p),r.setAttribute("aria-selected",!0),i.focus&&r.focus(),this.image=g,this.viewed=!1,this.index=e,this.imageData={},xA(g,k),i.loading&&xA(o,O),o.innerHTML="",o.appendChild(g),this.renderList(),n.innerHTML="";var I,Q,u=function(){var e=A.imageData,t=Array.isArray(i.title)?i.title[1]:i.title;n.innerHTML=kA(wA(t)?t.call(A,g,e):"".concat(l," (").concat(e.naturalWidth," × ").concat(e.naturalHeight,")"))};return TA(t,oA,u,{once:!0}),this.viewing={abort:function(){zA(t,oA,u),g.complete?A.imageRendering?A.imageRendering.abort():A.imageInitializing&&A.imageInitializing.abort():(g.src="",zA(g,T,I),A.timeout&&clearTimeout(A.timeout))}},g.complete?this.load():(TA(g,T,I=function(){zA(g,X,Q),A.load()},{once:!0}),TA(g,X,Q=function(){zA(g,T,I),A.timeout&&(clearTimeout(A.timeout),A.timeout=!1),GA(g,k),i.loading&&GA(A.canvas,O)},{once:!0}),this.timeout&&clearTimeout(this.timeout),this.timeout=setTimeout((function(){GA(g,k),A.timeout=!1}),1e3)),this},prev:function(){var A=arguments.length>0&&void 0!==arguments[0]&&arguments[0],e=this.index-1;return e<0&&(e=A?this.length-1:0),this.view(e),this},next:function(){var A=arguments.length>0&&void 0!==arguments[0]&&arguments[0],e=this.length-1,t=this.index+1;return t>e&&(t=A?0:e),this.view(t),this},move:function(A){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:A,t=this.imageData;return this.moveTo(fA(A)?A:t.x+Number(A),fA(e)?e:t.y+Number(e)),this},moveTo:function(A){var e=this,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:A,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,n=this.element,o=this.options,r=this.imageData;if(A=Number(A),t=Number(t),this.viewed&&!this.played&&o.movable){var a=r.x,s=r.y,l=!1;if(mA(A)?l=!0:A=a,mA(t)?l=!0:t=s,l){if(wA(o.move)&&TA(n,rA,o.move,{once:!0}),!1===XA(n,rA,{x:A,y:t,oldX:a,oldY:s,originalEvent:i}))return this;r.x=A,r.y=t,r.left=A,r.top=t,this.moving=!0,this.renderImage((function(){e.moving=!1,wA(o.moved)&&TA(n,aA,o.moved,{once:!0}),XA(n,aA,{x:A,y:t,oldX:a,oldY:s,originalEvent:i},{cancelable:!1})}))}}return this},rotate:function(A){return this.rotateTo((this.imageData.rotate||0)+Number(A)),this},rotateTo:function(A){var e=this,t=this.element,i=this.options,n=this.imageData;if(A=Number(A),mA(A)&&this.viewed&&!this.played&&i.rotatable){var o=n.rotate;if(wA(i.rotate)&&TA(t,sA,i.rotate,{once:!0}),!1===XA(t,sA,{degree:A,oldDegree:o}))return this;n.rotate=A,this.rotating=!0,this.renderImage((function(){e.rotating=!1,wA(i.rotated)&&TA(t,lA,i.rotated,{once:!0}),XA(t,lA,{degree:A,oldDegree:o},{cancelable:!1})}))}return this},scaleX:function(A){return this.scale(A,this.imageData.scaleY),this},scaleY:function(A){return this.scale(this.imageData.scaleX,A),this},scale:function(A){var e=this,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:A,i=this.element,n=this.options,o=this.imageData;if(A=Number(A),t=Number(t),this.viewed&&!this.played&&n.scalable){var r=o.scaleX,a=o.scaleY,s=!1;if(mA(A)?s=!0:A=r,mA(t)?s=!0:t=a,s){if(wA(n.scale)&&TA(i,gA,n.scale,{once:!0}),!1===XA(i,gA,{scaleX:A,scaleY:t,oldScaleX:r,oldScaleY:a}))return this;o.scaleX=A,o.scaleY=t,this.scaling=!0,this.renderImage((function(){e.scaling=!1,wA(n.scaled)&&TA(i,cA,n.scaled,{once:!0}),XA(i,cA,{scaleX:A,scaleY:t,oldScaleX:r,oldScaleY:a},{cancelable:!1})}))}}return this},zoom:function(A){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,n=this.imageData;return A=Number(A),A=A<0?1/(1-A):1+A,this.zoomTo(n.width*A/n.naturalWidth,e,t,i),this},zoomTo:function(A){var e=this,t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,o=arguments.length>4&&void 0!==arguments[4]&&arguments[4],r=this.element,a=this.options,s=this.pointers,l=this.imageData,g=l.x,c=l.y,I=l.width,Q=l.height,u=l.naturalWidth,C=l.naturalHeight;if(A=Math.max(0,A),mA(A)&&this.viewed&&!this.played&&(o||a.zoomable)){if(!o){var h=Math.max(.01,a.minZoomRatio),d=Math.min(100,a.maxZoomRatio);A=Math.min(Math.max(A,h),d)}if(n)switch(n.type){case"wheel":a.zoomRatio>=.055&&A>.95&&A<1.05&&(A=1);break;case"pointermove":case"touchmove":case"mousemove":A>.99&&A<1.01&&(A=1);break}var B=u*A,E=C*A,p=B-I,m=E-Q,f=l.ratio;if(wA(a.zoom)&&TA(r,IA,a.zoom,{once:!0}),!1===XA(r,IA,{ratio:A,oldRatio:f,originalEvent:n}))return this;if(this.zooming=!0,n){var y=UA(this.viewer),b=s&&Object.keys(s).length>0?PA(s):{pageX:n.pageX,pageY:n.pageY};l.x-=p*((b.pageX-y.left-g)/I),l.y-=m*((b.pageY-y.top-c)/Q)}else vA(i)&&mA(i.x)&&mA(i.y)?(l.x-=p*((i.x-g)/I),l.y-=m*((i.y-c)/Q)):(l.x-=p/2,l.y-=m/2);l.left=l.x,l.top=l.y,l.width=B,l.height=E,l.oldRatio=f,l.ratio=A,this.renderImage((function(){e.zooming=!1,wA(a.zoomed)&&TA(r,QA,a.zoomed,{once:!0}),XA(r,QA,{ratio:A,oldRatio:f,originalEvent:n},{cancelable:!1})})),t&&this.tooltip()}return this},play:function(){var A=this,e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!this.isShown||this.played)return this;var t=this.element,i=this.options;if(wA(i.play)&&TA(t,uA,i.play,{once:!0}),!1===XA(t,uA))return this;var n=this.player,o=this.loadImage.bind(this),r=[],a=0,s=0;if(this.played=!0,this.onLoadWhenPlay=o,e&&this.requestFullscreen(e),xA(n,j),NA(this.items,(function(A,e){var t=A.querySelector("img"),l=document.createElement("img");l.src=JA(t,"originalUrl"),l.alt=t.getAttribute("alt"),l.referrerPolicy=t.referrerPolicy,a+=1,xA(l,f),jA(l,Z,i.transition),OA(A,p)&&(xA(l,M),s=e),r.push(l),TA(l,T,o,{once:!0}),n.appendChild(l)})),mA(i.interval)&&i.interval>0){var l=function e(){clearTimeout(A.playing.timeout),GA(r[s],M),s-=1,s=s>=0?s:a-1,xA(r[s],M),A.playing.timeout=setTimeout(e,i.interval)},g=function e(){clearTimeout(A.playing.timeout),GA(r[s],M),s+=1,s=s<a?s:0,xA(r[s],M),A.playing.timeout=setTimeout(e,i.interval)};a>1&&(this.playing={prev:l,next:g,timeout:setTimeout(g,i.interval)})}return this},stop:function(){var A=this;if(!this.played)return this;var e=this.element,t=this.options;if(wA(t.stop)&&TA(e,CA,t.stop,{once:!0}),!1===XA(e,CA))return this;var i=this.player;return clearTimeout(this.playing.timeout),this.playing=!1,this.played=!1,NA(i.getElementsByTagName("img"),(function(e){zA(e,T,A.onLoadWhenPlay)})),GA(i,j),i.innerHTML="",this.exitFullscreen(),this},full:function(){var A=this,e=this.options,t=this.viewer,i=this.image,n=this.list;return!this.isShown||this.played||this.fulled||!e.inline||(this.fulled=!0,this.open(),xA(this.button,v),e.transition&&(GA(n,Z),this.viewed&&GA(i,Z)),xA(t,y),t.setAttribute("role","dialog"),t.setAttribute("aria-labelledby",this.title.id),t.setAttribute("aria-modal",!0),t.removeAttribute("style"),MA(t,{zIndex:e.zIndex}),e.focus&&this.enforceFocus(),this.initContainer(),this.viewerData=DA({},this.containerData),this.renderList(),this.viewed&&this.initImage((function(){A.renderImage((function(){e.transition&&setTimeout((function(){xA(i,Z),xA(n,Z)}),0)}))}))),this},exit:function(){var A=this,e=this.options,t=this.viewer,i=this.image,n=this.list;return this.isShown&&!this.played&&this.fulled&&e.inline?(this.fulled=!1,this.close(),GA(this.button,v),e.transition&&(GA(n,Z),this.viewed&&GA(i,Z)),e.focus&&this.clearEnforceFocus(),t.removeAttribute("role"),t.removeAttribute("aria-labelledby"),t.removeAttribute("aria-modal"),GA(t,y),MA(t,{zIndex:e.zIndexInline}),this.viewerData=DA({},this.parentData),this.renderViewer(),this.renderList(),this.viewed&&this.initImage((function(){A.renderImage((function(){e.transition&&setTimeout((function(){xA(i,Z),xA(n,Z)}),0)}))})),this):this},tooltip:function(){var A=this,e=this.options,t=this.tooltipBox,i=this.imageData;return this.viewed&&!this.played&&e.tooltip?(t.textContent="".concat(Math.round(100*i.ratio),"%"),this.tooltipping?clearTimeout(this.tooltipping):e.transition?(this.fading&&XA(t,q),xA(t,j),xA(t,f),xA(t,Z),t.removeAttribute("aria-hidden"),t.initialOffsetWidth=t.offsetWidth,xA(t,M)):(xA(t,j),t.removeAttribute("aria-hidden")),this.tooltipping=setTimeout((function(){e.transition?(TA(t,q,(function(){GA(t,j),GA(t,f),GA(t,Z),t.setAttribute("aria-hidden",!0),A.fading=!1}),{once:!0}),GA(t,M),A.fading=!0):(GA(t,j),t.setAttribute("aria-hidden",!0)),A.tooltipping=!1}),1e3),this):this},toggle:function(){var A=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;return 1===this.imageData.ratio?this.zoomTo(this.imageData.oldRatio,!0,null,A):this.zoomTo(1,!0,null,A),this},reset:function(){return this.viewed&&!this.played&&(this.imageData=DA({},this.initialImageData),this.renderImage()),this},update:function(){var A=this,e=this.element,t=this.options,i=this.isImg;if(i&&!e.parentNode)return this.destroy();var n=[];if(NA(i?[e]:e.querySelectorAll("img"),(function(e){wA(t.filter)?t.filter.call(A,e)&&n.push(e):A.getImageURL(e)&&n.push(e)})),!n.length)return this;if(this.images=n,this.length=n.length,this.ready){var o=[];if(NA(this.items,(function(A,e){var t=A.querySelector("img"),i=n[e];i&&t&&i.src===t.src&&i.alt===t.alt||o.push(e)})),MA(this.list,{width:"auto"}),this.initList(),this.isShown)if(this.length){if(this.viewed){var r=o.indexOf(this.index);if(r>=0)this.viewed=!1,this.view(Math.max(Math.min(this.index-r,this.length-1),0));else{var a=this.items[this.index];xA(a,p),a.setAttribute("aria-selected",!0)}}}else this.image=null,this.viewed=!1,this.index=0,this.imageData={},this.canvas.innerHTML="",this.title.innerHTML=""}else this.build();return this},destroy:function(){var A=this.element,e=this.options;return A[h]?(this.destroyed=!0,this.ready?(this.played&&this.stop(),e.inline?(this.fulled&&this.exit(),this.unbind()):this.isShown?(this.viewing&&(this.imageRendering?this.imageRendering.abort():this.imageInitializing&&this.imageInitializing.abort()),this.hiding&&this.transitioning.abort(),this.hidden()):this.showing&&(this.transitioning.abort(),this.hidden()),this.ready=!1,this.viewer.parentNode.removeChild(this.viewer)):e.inline&&(this.delaying?this.delaying.abort():this.initializing&&this.initializing.abort()),e.inline||zA(A,F,this.onStart),A[h]=void 0,this):this}},ie={getImageURL:function(A){var e=this.options.url;return e=EA(e)?A.getAttribute(e):wA(e)?e.call(this,A):"",e},enforceFocus:function(){var A=this;this.clearEnforceFocus(),TA(document,R,this.onFocusin=function(e){var t=A.viewer,i=e.target;if(i!==document&&i!==t&&!t.contains(i)){while(i){if(null!==i.getAttribute("tabindex")||"true"===i.getAttribute("aria-modal"))return;i=i.parentElement}t.focus()}})},clearEnforceFocus:function(){this.onFocusin&&(zA(document,R,this.onFocusin),this.onFocusin=null)},open:function(){var A=this.body;xA(A,G),this.scrollbarWidth>0&&(A.style.paddingRight="".concat(this.scrollbarWidth+(parseFloat(this.initialBodyComputedPaddingRight)||0),"px"))},close:function(){var A=this.body;GA(A,G),this.scrollbarWidth>0&&(A.style.paddingRight=this.initialBodyPaddingRight)},shown:function(){var A=this.element,e=this.options,t=this.viewer;this.fulled=!0,this.isShown=!0,this.render(),this.bind(),this.showing=!1,e.focus&&(t.focus(),this.enforceFocus()),wA(e.shown)&&TA(A,eA,e.shown,{once:!0}),!1!==XA(A,eA)&&this.ready&&this.isShown&&!this.hiding&&this.view(this.index)},hidden:function(){var A=this.element,e=this.options,t=this.viewer;e.fucus&&this.clearEnforceFocus(),this.fulled=!1,this.viewed=!1,this.isShown=!1,this.close(),this.unbind(),xA(t,w),t.removeAttribute("role"),t.removeAttribute("aria-labelledby"),t.removeAttribute("aria-modal"),t.setAttribute("aria-hidden",!0),this.resetList(),this.resetImage(),this.hiding=!1,this.destroyed||(wA(e.hidden)&&TA(A,iA,e.hidden,{once:!0}),XA(A,iA,null,{cancelable:!1}))},requestFullscreen:function(A){var e=this.element.ownerDocument;if(this.fulled&&!(e.fullscreenElement||e.webkitFullscreenElement||e.mozFullScreenElement||e.msFullscreenElement)){var t=e.documentElement;t.requestFullscreen?vA(A)?t.requestFullscreen(A):t.requestFullscreen():t.webkitRequestFullscreen?t.webkitRequestFullscreen(Element.ALLOW_KEYBOARD_INPUT):t.mozRequestFullScreen?t.mozRequestFullScreen():t.msRequestFullscreen&&t.msRequestFullscreen()}},exitFullscreen:function(){var A=this.element.ownerDocument;this.fulled&&(A.fullscreenElement||A.webkitFullscreenElement||A.mozFullScreenElement||A.msFullscreenElement)&&(A.exitFullscreen?A.exitFullscreen():A.webkitExitFullscreen?A.webkitExitFullscreen():A.mozCancelFullScreen?A.mozCancelFullScreen():A.msExitFullscreen&&A.msExitFullscreen())},change:function(A){var e=this.options,t=this.pointers,i=t[Object.keys(t)[0]];if(i){var n=i.endX-i.startX,o=i.endY-i.startY;switch(this.action){case d:this.move(n,o,A);break;case E:this.zoom(KA(t),!1,null,A);break;case B:this.action="switched";var r=Math.abs(n);r>1&&r>Math.abs(o)&&(this.pointers={},n>1?this.prev(e.loop):n<-1&&this.next(e.loop));break}NA(t,(function(A){A.startX=A.endX,A.startY=A.endY}))}},isSwitchable:function(){var A=this.imageData,e=this.viewerData;return this.length>1&&A.x>=0&&A.y>=0&&A.width<=e.width&&A.height<=e.height}},ne=Q.Viewer,oe=function(A){return function(){return A+=1,A}}(-1),re=function(){function A(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(i(this,A),!e||1!==e.nodeType)throw new Error("The first argument is required and must be an element.");this.element=e,this.options=DA({},g,vA(t)&&t),this.action=!1,this.fading=!1,this.fulled=!1,this.hiding=!1,this.imageClicked=!1,this.imageData={},this.index=this.options.initialViewIndex,this.isImg=!1,this.isShown=!1,this.length=0,this.moving=!1,this.played=!1,this.playing=!1,this.pointers={},this.ready=!1,this.rotating=!1,this.scaling=!1,this.showing=!1,this.timeout=!1,this.tooltipping=!1,this.viewed=!1,this.viewing=!1,this.wheeling=!1,this.zooming=!1,this.pointerMoved=!1,this.id=oe(),this.init()}return r(A,[{key:"init",value:function(){var A=this,e=this.element,t=this.options;if(!e[h]){e[h]=this,t.focus&&!t.keyboard&&(t.focus=!1);var i="img"===e.localName,n=[];if(NA(i?[e]:e.querySelectorAll("img"),(function(e){wA(t.filter)?t.filter.call(A,e)&&n.push(e):A.getImageURL(e)&&n.push(e)})),this.isImg=i,this.length=n.length,this.images=n,this.initBody(),fA(document.createElement(h).style.transition)&&(t.transition=!1),t.inline){var o=0,r=function(){var e;(o+=1,o===A.length)&&(A.initializing=!1,A.delaying={abort:function(){clearTimeout(e)}},e=setTimeout((function(){A.delaying=!1,A.build()}),0))};this.initializing={abort:function(){NA(n,(function(A){A.complete||(zA(A,T,r),zA(A,X,r))}))}},NA(n,(function(A){var e,t;A.complete?r():(TA(A,T,e=function(){zA(A,X,t),r()},{once:!0}),TA(A,X,t=function(){zA(A,T,e),r()},{once:!0}))}))}else TA(e,F,this.onStart=function(e){var i=e.target;"img"!==i.localName||wA(t.filter)&&!t.filter.call(A,i)||A.view(A.images.indexOf(i))})}}},{key:"build",value:function(){if(!this.ready){var A=this.element,e=this.options,t=A.parentNode,i=document.createElement("div");i.innerHTML=c;var n=i.querySelector(".".concat(h,"-container")),o=n.querySelector(".".concat(h,"-title")),r=n.querySelector(".".concat(h,"-toolbar")),a=n.querySelector(".".concat(h,"-navbar")),s=n.querySelector(".".concat(h,"-button")),l=n.querySelector(".".concat(h,"-canvas"));if(this.parent=t,this.viewer=n,this.title=o,this.toolbar=r,this.navbar=a,this.button=s,this.canvas=l,this.footer=n.querySelector(".".concat(h,"-footer")),this.tooltipBox=n.querySelector(".".concat(h,"-tooltip")),this.player=n.querySelector(".".concat(h,"-player")),this.list=n.querySelector(".".concat(h,"-list")),n.id="".concat(h).concat(this.id),o.id="".concat(h,"Title").concat(this.id),xA(o,e.title?HA(Array.isArray(e.title)?e.title[0]:e.title):w),xA(a,e.navbar?HA(e.navbar):w),jA(s,w,!e.button),e.keyboard&&s.setAttribute("tabindex",0),e.backdrop&&(xA(n,"".concat(h,"-backdrop")),e.inline||"static"===e.backdrop||SA(l,hA,"hide")),EA(e.className)&&e.className&&e.className.split(dA).forEach((function(A){xA(n,A)})),e.toolbar){var g=document.createElement("ul"),I=vA(e.toolbar),Q=BA.slice(0,3),u=BA.slice(7,9),C=BA.slice(9);I||xA(r,HA(e.toolbar)),NA(I?e.toolbar:BA,(function(A,t){var i=I&&vA(A),n=I?FA(t):A,o=i&&!fA(A.show)?A.show:A;if(o&&(e.zoomable||-1===Q.indexOf(n))&&(e.rotatable||-1===u.indexOf(n))&&(e.scalable||-1===C.indexOf(n))){var r=i&&!fA(A.size)?A.size:A,a=i&&!fA(A.click)?A.click:A,s=document.createElement("li");e.keyboard&&s.setAttribute("tabindex",0),s.setAttribute("role","button"),xA(s,"".concat(h,"-").concat(n)),wA(a)||SA(s,hA,n),mA(o)&&xA(s,HA(o)),-1!==["small","large"].indexOf(r)?xA(s,"".concat(h,"-").concat(r)):"play"===n&&xA(s,"".concat(h,"-large")),wA(a)&&TA(s,F,a),g.appendChild(s)}})),r.appendChild(g)}else xA(r,w);if(!e.rotatable){var d=r.querySelectorAll('li[class*="rotate"]');xA(d,k),NA(d,(function(A){r.appendChild(A)}))}if(e.inline)xA(s,b),MA(n,{zIndex:e.zIndexInline}),"static"===window.getComputedStyle(t).position&&MA(t,{position:"relative"}),t.insertBefore(n,A.nextSibling);else{xA(s,m),xA(n,y),xA(n,f),xA(n,w),MA(n,{zIndex:e.zIndex});var B=e.container;EA(B)&&(B=A.ownerDocument.querySelector(B)),B||(B=this.body),B.appendChild(n)}e.inline&&(this.render(),this.bind(),this.isShown=!0),this.ready=!0,wA(e.ready)&&TA(A,$,e.ready,{once:!0}),!1!==XA(A,$)?this.ready&&e.inline&&this.view(this.index):this.ready=!1}}}],[{key:"noConflict",value:function(){return window.Viewer=ne,A}},{key:"setDefaults",value:function(A){DA(g,vA(A)&&A)}}]),A}();return DA(re.prototype,$A,Ae,ee,te,ie),re}))},75269:function(A,e,t){var i,n,o=t(57847)["default"];(function(){var t={expires:"1d",path:"; path=/",domain:"",secure:"",sameSite:"; SameSite=Lax"},r={install:function(A,e){e&&this.config(e.expires,e.path,e.domain,e.secure,e.sameSite),A.prototype&&(A.prototype.$cookies=this),A.config&&A.config.globalProperties&&(A.config.globalProperties.$cookies=this,A.provide("$cookies",this)),A.$cookies=this},config:function(A,e,i,n,o){t.expires=A||"1d",t.path=e?"; path="+e:"; path=/",t.domain=i?"; domain="+i:"",t.secure=n?"; Secure":"",t.sameSite=o?"; SameSite="+o:"; SameSite=Lax"},get:function(A){var e=decodeURIComponent(document.cookie.replace(new RegExp("(?:(?:^|.*;)\\s*"+encodeURIComponent(A).replace(/[\-\.\+\*]/g,"\\$&")+"\\s*\\=\\s*([^;]*).*$)|^.*$"),"$1"))||null;if(e&&"{"===e.substring(0,1)&&"}"===e.substring(e.length-1,e.length))try{e=JSON.parse(e)}catch(t){return e}return e},set:function(A,e,i,n,o,r,a){if(!A)throw new Error("Cookie name is not found in the first argument.");if(/^(?:expires|max\-age|path|domain|secure|SameSite)$/i.test(A))throw new Error('Cookie name illegality. Cannot be set to ["expires","max-age","path","domain","secure","SameSite"]\t current key name: '+A);e&&e.constructor===Object&&(e=JSON.stringify(e));var s="";if(i=void 0==i?t.expires:i,i&&0!=i)switch(i.constructor){case Number:s=i===1/0||-1===i?"; expires=Fri, 31 Dec 9999 23:59:59 GMT":"; max-age="+i;break;case String:if(/^(?:\d+(y|m|d|h|min|s))$/i.test(i)){var l=i.replace(/^(\d+)(?:y|m|d|h|min|s)$/i,"$1");switch(i.replace(/^(?:\d+)(y|m|d|h|min|s)$/i,"$1").toLowerCase()){case"m":s="; max-age="+2592e3*+l;break;case"d":s="; max-age="+86400*+l;break;case"h":s="; max-age="+3600*+l;break;case"min":s="; max-age="+60*+l;break;case"s":s="; max-age="+l;break;case"y":s="; max-age="+31104e3*+l;break;default:new Error('unknown exception of "set operation"')}}else s="; expires="+i;break;case Date:s="; expires="+i.toUTCString();break}return document.cookie=encodeURIComponent(A)+"="+encodeURIComponent(e)+s+(o?"; domain="+o:t.domain)+(n?"; path="+n:t.path)+(void 0==r?t.secure:r?"; Secure":"")+(void 0==a?t.sameSite:a?"; SameSite="+a:""),this},remove:function(A,e,i){return!(!A||!this.isKey(A))&&(document.cookie=encodeURIComponent(A)+"=; expires=Thu, 01 Jan 1970 00:00:00 GMT"+(i?"; domain="+i:t.domain)+(e?"; path="+e:t.path)+"; SameSite=Lax",!0)},isKey:function(A){return new RegExp("(?:^|;\\s*)"+encodeURIComponent(A).replace(/[\-\.\+\*]/g,"\\$&")+"\\s*\\=").test(document.cookie)},keys:function(){if(!document.cookie)return[];for(var A=document.cookie.replace(/((?:^|\s*;)[^\=]+)(?=;|$)|^\s*|\s*(?:\=[^;]*)?(?:\1|$)/g,"").split(/\s*(?:\=[^;]*)?;\s*/),e=0;e<A.length;e++)A[e]=decodeURIComponent(A[e]);return A}};"object"==o(e)?A.exports=r:(i=[],n=function(){return r}.apply(e,i),void 0===n||(A.exports=n)),"undefined"!==typeof window&&(window.$cookies=r)})()},43535:function(A,e){"use strict";e.Z={install:function(A){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=e.name||"ref";A.directive(t,{bind:function(A,e,t){e.value(t.componentInstance||A,t.key)},update:function(A,e,i,n){if(n.data&&n.data.directives){var o=n.data.directives.find((function(A){var e=A.name;return e===t}));if(o&&o.value!==e.value)return o&&o.value(null,n.key),void e.value(i.componentInstance||A,i.key)}i.componentInstance===n.componentInstance&&i.elm===n.elm||e.value(i.componentInstance||A,i.key)},unbind:function(A,e,t){e.value(null,t.key)}})}}},55137:function(A,e,t){var i,n,o,r=t(57847)["default"];t(32564),
/*!
 * vue-virtual-scroll-list v2.3.4
 * open source under the MIT license
 * https://github.com/tangbc/vue-virtual-scroll-list#readme
 */
function(a,s){"object"===r(e)?A.exports=s(t(3032)):(n=[t(3032)],i=s,o="function"===typeof i?i.apply(e,n):i,void 0===o||(A.exports=o))}(0,(function(A){"use strict";function e(A,e){if(!(A instanceof e))throw new TypeError("Cannot call a class as a function")}function t(A,e){for(var t=0;t<e.length;t++){var i=e[t];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(A,i.key,i)}}function i(A,e,i){return e&&t(A.prototype,e),i&&t(A,i),A}function n(A,e,t){return e in A?Object.defineProperty(A,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):A[e]=t,A}function o(A,e){var t=Object.keys(A);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(A);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(A,e).enumerable}))),t.push.apply(t,i)}return t}function r(A){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{};e%2?o(Object(t),!0).forEach((function(e){n(A,e,t[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(A,Object.getOwnPropertyDescriptors(t)):o(Object(t)).forEach((function(e){Object.defineProperty(A,e,Object.getOwnPropertyDescriptor(t,e))}))}return A}function a(A){return s(A)||l(A)||g(A)||I()}function s(A){if(Array.isArray(A))return c(A)}function l(A){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(A))return Array.from(A)}function g(A,e){if(A){if("string"===typeof A)return c(A,e);var t=Object.prototype.toString.call(A).slice(8,-1);return"Object"===t&&A.constructor&&(t=A.constructor.name),"Map"===t||"Set"===t?Array.from(t):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?c(A,e):void 0}}function c(A,e){(null==e||e>A.length)&&(e=A.length);for(var t=0,i=new Array(e);t<e;t++)i[t]=A[t];return i}function I(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}A=A&&Object.prototype.hasOwnProperty.call(A,"default")?A["default"]:A;var Q={FRONT:"FRONT",BEHIND:"BEHIND"},u={INIT:"INIT",FIXED:"FIXED",DYNAMIC:"DYNAMIC"},C=0,h=function(){function A(t,i){e(this,A),this.init(t,i)}return i(A,[{key:"init",value:function(A,e){this.param=A,this.callUpdate=e,this.sizes=new Map,this.firstRangeTotalSize=0,this.firstRangeAverageSize=0,this.lastCalcIndex=0,this.fixedSizeValue=0,this.calcType=u.INIT,this.offset=0,this.direction="",this.range=Object.create(null),A&&this.checkRange(0,A.keeps-1)}},{key:"destroy",value:function(){this.init(null,null)}},{key:"getRange",value:function(){var A=Object.create(null);return A.start=this.range.start,A.end=this.range.end,A.padFront=this.range.padFront,A.padBehind=this.range.padBehind,A}},{key:"isBehind",value:function(){return this.direction===Q.BEHIND}},{key:"isFront",value:function(){return this.direction===Q.FRONT}},{key:"getOffset",value:function(A){return(A<1?0:this.getIndexOffset(A))+this.param.slotHeaderSize}},{key:"updateParam",value:function(A,e){var t=this;this.param&&A in this.param&&("uniqueIds"===A&&this.sizes.forEach((function(A,i){e.includes(i)||t.sizes["delete"](i)})),this.param[A]=e)}},{key:"saveSize",value:function(A,e){this.sizes.set(A,e),this.calcType===u.INIT?(this.fixedSizeValue=e,this.calcType=u.FIXED):this.calcType===u.FIXED&&this.fixedSizeValue!==e&&(this.calcType=u.DYNAMIC,delete this.fixedSizeValue),this.calcType!==u.FIXED&&"undefined"!==typeof this.firstRangeTotalSize&&(this.sizes.size<Math.min(this.param.keeps,this.param.uniqueIds.length)?(this.firstRangeTotalSize=a(this.sizes.values()).reduce((function(A,e){return A+e}),0),this.firstRangeAverageSize=Math.round(this.firstRangeTotalSize/this.sizes.size)):delete this.firstRangeTotalSize)}},{key:"handleDataSourcesChange",value:function(){var A=this.range.start;this.isFront()?A-=C:this.isBehind()&&(A+=C),A=Math.max(A,0),this.updateRange(this.range.start,this.getEndByStart(A))}},{key:"handleSlotSizeChange",value:function(){this.handleDataSourcesChange()}},{key:"handleScroll",value:function(A){this.direction=A<this.offset?Q.FRONT:Q.BEHIND,this.offset=A,this.param&&(this.direction===Q.FRONT?this.handleFront():this.direction===Q.BEHIND&&this.handleBehind())}},{key:"handleFront",value:function(){var A=this.getScrollOvers();if(!(A>this.range.start)){var e=Math.max(A-this.param.buffer,0);this.checkRange(e,this.getEndByStart(e))}}},{key:"handleBehind",value:function(){var A=this.getScrollOvers();A<this.range.start+this.param.buffer||this.checkRange(A,this.getEndByStart(A))}},{key:"getScrollOvers",value:function(){var A=this.offset-this.param.slotHeaderSize;if(A<=0)return 0;if(this.isFixedType())return Math.floor(A/this.fixedSizeValue);var e=0,t=0,i=0,n=this.param.uniqueIds.length;while(e<=n){if(t=e+Math.floor((n-e)/2),i=this.getIndexOffset(t),i===A)return t;i<A?e=t+1:i>A&&(n=t-1)}return e>0?--e:0}},{key:"getIndexOffset",value:function(A){if(!A)return 0;for(var e=0,t=0,i=0;i<A;i++)t=this.sizes.get(this.param.uniqueIds[i]),e+="number"===typeof t?t:this.getEstimateSize();return this.lastCalcIndex=Math.max(this.lastCalcIndex,A-1),this.lastCalcIndex=Math.min(this.lastCalcIndex,this.getLastIndex()),e}},{key:"isFixedType",value:function(){return this.calcType===u.FIXED}},{key:"getLastIndex",value:function(){return this.param.uniqueIds.length-1}},{key:"checkRange",value:function(A,e){var t=this.param.keeps,i=this.param.uniqueIds.length;i<=t?(A=0,e=this.getLastIndex()):e-A<t-1&&(A=e-t+1),this.range.start!==A&&this.updateRange(A,e)}},{key:"updateRange",value:function(A,e){this.range.start=A,this.range.end=e,this.range.padFront=this.getPadFront(),this.range.padBehind=this.getPadBehind(),this.callUpdate(this.getRange())}},{key:"getEndByStart",value:function(A){var e=A+this.param.keeps-1,t=Math.min(e,this.getLastIndex());return t}},{key:"getPadFront",value:function(){return this.isFixedType()?this.fixedSizeValue*this.range.start:this.getIndexOffset(this.range.start)}},{key:"getPadBehind",value:function(){var A=this.range.end,e=this.getLastIndex();return this.isFixedType()?(e-A)*this.fixedSizeValue:this.lastCalcIndex===e?this.getIndexOffset(e)-this.getIndexOffset(A):(e-A)*this.getEstimateSize()}},{key:"getEstimateSize",value:function(){return this.isFixedType()?this.fixedSizeValue:this.firstRangeAverageSize||this.param.estimateSize}}]),A}(),d={dataKey:{type:[String,Function],required:!0},dataSources:{type:Array,required:!0},dataComponent:{type:[Object,Function],required:!0},keeps:{type:Number,default:30},extraProps:{type:Object},estimateSize:{type:Number,default:50},direction:{type:String,default:"vertical"},start:{type:Number,default:0},offset:{type:Number,default:0},topThreshold:{type:Number,default:0},bottomThreshold:{type:Number,default:0},pageMode:{type:Boolean,default:!1},rootTag:{type:String,default:"div"},wrapTag:{type:String,default:"div"},wrapClass:{type:String,default:""},wrapStyle:{type:Object},itemTag:{type:String,default:"div"},itemClass:{type:String,default:""},itemClassAdd:{type:Function},itemStyle:{type:Object},headerTag:{type:String,default:"div"},headerClass:{type:String,default:""},headerStyle:{type:Object},footerTag:{type:String,default:"div"},footerClass:{type:String,default:""},footerStyle:{type:Object},itemScopedSlots:{type:Object}},B={index:{type:Number},event:{type:String},tag:{type:String},horizontal:{type:Boolean},source:{type:Object},component:{type:[Object,Function]},slotComponent:{type:Function},uniqueKey:{type:[String,Number]},extraProps:{type:Object},scopedSlots:{type:Object}},E={event:{type:String},uniqueKey:{type:String},tag:{type:String},horizontal:{type:Boolean}},p={created:function(){this.shapeKey=this.horizontal?"offsetWidth":"offsetHeight"},mounted:function(){var A=this;"undefined"!==typeof ResizeObserver&&(this.resizeObserver=new ResizeObserver((function(){A.dispatchSizeChange()})),this.resizeObserver.observe(this.$el))},updated:function(){this.dispatchSizeChange()},beforeDestroy:function(){this.resizeObserver&&(this.resizeObserver.disconnect(),this.resizeObserver=null)},methods:{getCurrentSize:function(){return this.$el?this.$el[this.shapeKey]:0},dispatchSizeChange:function(){this.$parent.$emit(this.event,this.uniqueKey,this.getCurrentSize(),this.hasInitial)}}},m=A.component("virtual-list-item",{mixins:[p],props:B,render:function(A){var e=this.tag,t=this.component,i=this.extraProps,n=void 0===i?{}:i,o=this.index,a=this.source,s=this.scopedSlots,l=void 0===s?{}:s,g=this.uniqueKey,c=this.slotComponent,I=r({},n,{source:a,index:o});return A(e,{key:g,attrs:{role:"listitem"}},[c?A("div",c({item:a,index:o,scope:I})):A(t,{props:I,scopedSlots:l})])}}),f=A.component("virtual-list-slot",{mixins:[p],props:E,render:function(A){var e=this.tag,t=this.uniqueKey;return A(e,{key:t,attrs:{role:t}},this.$slots["default"])}}),y={ITEM:"item_resize",SLOT:"slot_resize"},b={HEADER:"thead",FOOTER:"tfoot"},v=A.component("virtual-list",{props:d,data:function(){return{range:null}},watch:{"dataSources.length":function(){this.virtual.updateParam("uniqueIds",this.getUniqueIdFromDataSources()),this.virtual.handleDataSourcesChange()},keeps:function(A){this.virtual.updateParam("keeps",A),this.virtual.handleSlotSizeChange()},start:function(A){this.scrollToIndex(A)},offset:function(A){this.scrollToOffset(A)}},created:function(){this.isHorizontal="horizontal"===this.direction,this.directionKey=this.isHorizontal?"scrollLeft":"scrollTop",this.installVirtual(),this.$on(y.ITEM,this.onItemResized),(this.$slots.header||this.$slots.footer)&&this.$on(y.SLOT,this.onSlotResized)},activated:function(){this.scrollToOffset(this.virtual.offset),this.pageMode&&document.addEventListener("scroll",this.onScroll,{passive:!1})},deactivated:function(){this.pageMode&&document.removeEventListener("scroll",this.onScroll)},mounted:function(){this.start?this.scrollToIndex(this.start):this.offset&&this.scrollToOffset(this.offset),this.pageMode&&(this.updatePageModeFront(),document.addEventListener("scroll",this.onScroll,{passive:!1}))},beforeDestroy:function(){this.virtual.destroy(),this.pageMode&&document.removeEventListener("scroll",this.onScroll)},methods:{getSize:function(A){return this.virtual.sizes.get(A)},getSizes:function(){return this.virtual.sizes.size},getOffset:function(){if(this.pageMode)return document.documentElement[this.directionKey]||document.body[this.directionKey];var A=this.$refs.root;return A?Math.ceil(A[this.directionKey]):0},getClientSize:function(){var A=this.isHorizontal?"clientWidth":"clientHeight";if(this.pageMode)return document.documentElement[A]||document.body[A];var e=this.$refs.root;return e?Math.ceil(e[A]):0},getScrollSize:function(){var A=this.isHorizontal?"scrollWidth":"scrollHeight";if(this.pageMode)return document.documentElement[A]||document.body[A];var e=this.$refs.root;return e?Math.ceil(e[A]):0},scrollToOffset:function(A){if(this.pageMode)document.body[this.directionKey]=A,document.documentElement[this.directionKey]=A;else{var e=this.$refs.root;e&&(e[this.directionKey]=A)}},scrollToIndex:function(A){if(A>=this.dataSources.length-1)this.scrollToBottom();else{var e=this.virtual.getOffset(A);this.scrollToOffset(e)}},scrollToBottom:function(){var A=this,e=this.$refs.shepherd;if(e){var t=e[this.isHorizontal?"offsetLeft":"offsetTop"];this.scrollToOffset(t),setTimeout((function(){A.getOffset()+A.getClientSize()+1<A.getScrollSize()&&A.scrollToBottom()}),3)}},updatePageModeFront:function(){var A=this.$refs.root;if(A){var e=A.getBoundingClientRect(),t=A.ownerDocument.defaultView,i=this.isHorizontal?e.left+t.pageXOffset:e.top+t.pageYOffset;this.virtual.updateParam("slotHeaderSize",i)}},reset:function(){this.virtual.destroy(),this.scrollToOffset(0),this.installVirtual()},installVirtual:function(){this.virtual=new h({slotHeaderSize:0,slotFooterSize:0,keeps:this.keeps,estimateSize:this.estimateSize,buffer:Math.round(this.keeps/3),uniqueIds:this.getUniqueIdFromDataSources()},this.onRangeChanged),this.range=this.virtual.getRange()},getUniqueIdFromDataSources:function(){var A=this.dataKey;return this.dataSources.map((function(e){return"function"===typeof A?A(e):e[A]}))},onItemResized:function(A,e){this.virtual.saveSize(A,e),this.$emit("resized",A,e)},onSlotResized:function(A,e,t){A===b.HEADER?this.virtual.updateParam("slotHeaderSize",e):A===b.FOOTER&&this.virtual.updateParam("slotFooterSize",e),t&&this.virtual.handleSlotSizeChange()},onRangeChanged:function(A){this.range=A},onScroll:function(A){var e=this.getOffset(),t=this.getClientSize(),i=this.getScrollSize();e<0||e+t>i+1||!i||(this.virtual.handleScroll(e),this.emitEvent(e,t,i,A))},emitEvent:function(A,e,t,i){this.$emit("scroll",i,this.virtual.getRange()),this.virtual.isFront()&&this.dataSources.length&&A-this.topThreshold<=0?this.$emit("totop"):this.virtual.isBehind()&&A+e+this.bottomThreshold>=t&&this.$emit("tobottom")},getRenderSlots:function(A){for(var e=[],t=this.range,i=t.start,n=t.end,o=this.dataSources,r=this.dataKey,a=this.itemClass,s=this.itemTag,l=this.itemStyle,g=this.isHorizontal,c=this.extraProps,I=this.dataComponent,Q=this.itemScopedSlots,u=this.$scopedSlots&&this.$scopedSlots.item,C=i;C<=n;C++){var h=o[C];if(h){var d="function"===typeof r?r(h):h[r];"string"!==typeof d&&"number"!==typeof d||e.push(A(m,{props:{index:C,tag:s,event:y.ITEM,horizontal:g,uniqueKey:d,source:h,extraProps:c,component:I,slotComponent:u,scopedSlots:Q},style:l,class:"".concat(a).concat(this.itemClassAdd?" "+this.itemClassAdd(C):"")}))}}return e}},render:function(A){var e=this.$slots,t=e.header,i=e.footer,n=this.range,o=n.padFront,r=n.padBehind,a=this.isHorizontal,s=this.pageMode,l=this.rootTag,g=this.wrapTag,c=this.wrapClass,I=this.wrapStyle,Q=this.headerTag,u=this.headerClass,C=this.headerStyle,h=this.footerTag,d=this.footerClass,B=this.footerStyle,E={padding:a?"0px ".concat(r,"px 0px ").concat(o,"px"):"".concat(o,"px 0px ").concat(r,"px")},p=I?Object.assign({},I,E):E;return A(l,{ref:"root",on:{"&scroll":!s&&this.onScroll}},[t?A(f,{class:u,style:C,props:{tag:Q,event:y.SLOT,uniqueKey:b.HEADER}},t):null,A(g,{class:c,attrs:{role:"group"},style:p},this.getRenderSlots(A)),i?A(f,{class:d,style:B,props:{tag:h,event:y.SLOT,uniqueKey:b.FOOTER}},i):null,A("div",{ref:"shepherd",style:{width:a?"0px":"100%",height:a?"100%":"0px"}})])}});return v}))},86458:function(A,e,t){"use strict";t.d(e,{EK:function(){return AA}});t(32564);function i(){var A=window.navigator.userAgent,e=A.indexOf("MSIE ");if(e>0)return parseInt(A.substring(e+5,A.indexOf(".",e)),10);var t=A.indexOf("Trident/");if(t>0){var i=A.indexOf("rv:");return parseInt(A.substring(i+3,A.indexOf(".",i)),10)}var n=A.indexOf("Edge/");return n>0?parseInt(A.substring(n+5,A.indexOf(".",n)),10):-1}var n=void 0;function o(){o.init||(o.init=!0,n=-1!==i())}var r={render:function(){var A=this,e=A.$createElement,t=A._self._c||e;return t("div",{staticClass:"resize-observer",attrs:{tabindex:"-1"}})},staticRenderFns:[],_scopeId:"data-v-b329ee4c",name:"resize-observer",methods:{compareAndNotify:function(){this._w===this.$el.offsetWidth&&this._h===this.$el.offsetHeight||(this._w=this.$el.offsetWidth,this._h=this.$el.offsetHeight,this.$emit("notify"))},addResizeHandlers:function(){this._resizeObject.contentDocument.defaultView.addEventListener("resize",this.compareAndNotify),this.compareAndNotify()},removeResizeHandlers:function(){this._resizeObject&&this._resizeObject.onload&&(!n&&this._resizeObject.contentDocument&&this._resizeObject.contentDocument.defaultView.removeEventListener("resize",this.compareAndNotify),delete this._resizeObject.onload)}},mounted:function(){var A=this;o(),this.$nextTick((function(){A._w=A.$el.offsetWidth,A._h=A.$el.offsetHeight}));var e=document.createElement("object");this._resizeObject=e,e.setAttribute("aria-hidden","true"),e.setAttribute("tabindex",-1),e.onload=this.addResizeHandlers,e.type="text/html",n&&this.$el.appendChild(e),e.data="about:blank",n||this.$el.appendChild(e)},beforeDestroy:function(){this.removeResizeHandlers()}};function a(A){A.component("resize-observer",r),A.component("ResizeObserver",r)}var s={version:"0.4.5",install:a},l=null;"undefined"!==typeof window?l=window.Vue:"undefined"!==typeof t.g&&(l=t.g.Vue),l&&l.use(s);var g=t(3336);function c(A){return c="function"===typeof Symbol&&"symbol"===(0,g.Z)(Symbol.iterator)?function(A){return(0,g.Z)(A)}:function(A){return A&&"function"===typeof Symbol&&A.constructor===Symbol&&A!==Symbol.prototype?"symbol":(0,g.Z)(A)},c(A)}function I(A,e){if(!(A instanceof e))throw new TypeError("Cannot call a class as a function")}function Q(A,e){for(var t=0;t<e.length;t++){var i=e[t];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(A,i.key,i)}}function u(A,e,t){return e&&Q(A.prototype,e),t&&Q(A,t),A}function C(A){return h(A)||d(A)||B()}function h(A){if(Array.isArray(A)){for(var e=0,t=new Array(A.length);e<A.length;e++)t[e]=A[e];return t}}function d(A){if(Symbol.iterator in Object(A)||"[object Arguments]"===Object.prototype.toString.call(A))return Array.from(A)}function B(){throw new TypeError("Invalid attempt to spread non-iterable instance")}function E(A){var e;return e="function"===typeof A?{callback:A}:A,e}function p(A,e){var t,i,n,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=function(r){for(var a=arguments.length,s=new Array(a>1?a-1:0),l=1;l<a;l++)s[l-1]=arguments[l];if(n=s,!t||r!==i){var g=o.leading;"function"===typeof g&&(g=g(r,i)),t&&r===i||!g||A.apply(void 0,[r].concat(C(n))),i=r,clearTimeout(t),t=setTimeout((function(){A.apply(void 0,[r].concat(C(n))),t=0}),e)}};return r._clear=function(){clearTimeout(t),t=null},r}function m(A,e){if(A===e)return!0;if("object"===c(A)){for(var t in A)if(!m(A[t],e[t]))return!1;return!0}return!1}var f=function(){function A(e,t,i){I(this,A),this.el=e,this.observer=null,this.frozen=!1,this.createObserver(t,i)}return u(A,[{key:"createObserver",value:function(A,e){var t=this;if(this.observer&&this.destroyObserver(),!this.frozen){if(this.options=E(A),this.callback=function(A,e){t.options.callback(A,e),A&&t.options.once&&(t.frozen=!0,t.destroyObserver())},this.callback&&this.options.throttle){var i=this.options.throttleOptions||{},n=i.leading;this.callback=p(this.callback,this.options.throttle,{leading:function(A){return"both"===n||"visible"===n&&A||"hidden"===n&&!A}})}this.oldResult=void 0,this.observer=new IntersectionObserver((function(A){var e=A[0];if(A.length>1){var i=A.find((function(A){return A.isIntersecting}));i&&(e=i)}if(t.callback){var n=e.isIntersecting&&e.intersectionRatio>=t.threshold;if(n===t.oldResult)return;t.oldResult=n,t.callback(n,e)}}),this.options.intersection),e.context.$nextTick((function(){t.observer&&t.observer.observe(t.el)}))}}},{key:"destroyObserver",value:function(){this.observer&&(this.observer.disconnect(),this.observer=null),this.callback&&this.callback._clear&&(this.callback._clear(),this.callback=null)}},{key:"threshold",get:function(){return this.options.intersection&&this.options.intersection.threshold||0}}]),A}();function y(A,e,t){var i=e.value;if(i)if("undefined"===typeof IntersectionObserver);else{var n=new f(A,i,t);A._vue_visibilityState=n}}function b(A,e,t){var i=e.value,n=e.oldValue;if(!m(i,n)){var o=A._vue_visibilityState;i?o?o.createObserver(i,t):y(A,{value:i},t):v(A)}}function v(A){var e=A._vue_visibilityState;e&&(e.destroyObserver(),delete A._vue_visibilityState)}var w={bind:y,update:b,unbind:v};function N(A){A.directive("observe-visibility",w)}var D={version:"0.4.6",install:N},Y=null;"undefined"!==typeof window?Y=window.Vue:"undefined"!==typeof t.g&&(Y=t.g.Vue),Y&&Y.use(D);var M=t(14473),k=t.n(M),O={itemsLimit:1e3};function x(A){return x="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(A){return typeof A}:function(A){return A&&"function"===typeof Symbol&&A.constructor===Symbol&&A!==Symbol.prototype?"symbol":typeof A},x(A)}function G(A,e,t){return e in A?Object.defineProperty(A,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):A[e]=t,A}function j(A,e){var t=Object.keys(A);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(A);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(A,e).enumerable}))),t.push.apply(t,i)}return t}function Z(A){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{};e%2?j(Object(t),!0).forEach((function(e){G(A,e,t[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(A,Object.getOwnPropertyDescriptors(t)):j(Object(t)).forEach((function(e){Object.defineProperty(A,e,Object.getOwnPropertyDescriptor(t,e))}))}return A}function F(A,e){if(A){if("string"===typeof A)return J(A,e);var t=Object.prototype.toString.call(A).slice(8,-1);return"Object"===t&&A.constructor&&(t=A.constructor.name),"Map"===t||"Set"===t?Array.from(t):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?J(A,e):void 0}}function J(A,e){(null==e||e>A.length)&&(e=A.length);for(var t=0,i=new Array(e);t<e;t++)i[t]=A[t];return i}function S(A){if("undefined"===typeof Symbol||null==A[Symbol.iterator]){if(Array.isArray(A)||(A=F(A))){var e=0,t=function(){};return{s:t,n:function(){return e>=A.length?{done:!0}:{done:!1,value:A[e++]}},e:function(A){throw A},f:t}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,n,o=!0,r=!1;return{s:function(){i=A[Symbol.iterator]()},n:function(){var A=i.next();return o=A.done,A},e:function(A){r=!0,n=A},f:function(){try{o||null==i.return||i.return()}finally{if(r)throw n}}}}var R={items:{type:Array,required:!0},keyField:{type:String,default:"id"},direction:{type:String,default:"vertical",validator:function(A){return["vertical","horizontal"].includes(A)}}};function z(){return this.items.length&&"object"!==x(this.items[0])}var T=!1;if("undefined"!==typeof window){T=!1;try{var X=Object.defineProperty({},"passive",{get:function(){T=!0}});window.addEventListener("test",null,X)}catch(pA){}}var U=0,W={name:"RecycleScroller",components:{ResizeObserver:r},directives:{ObserveVisibility:w},props:Z({},R,{itemSize:{type:Number,default:null},minItemSize:{type:[Number,String],default:null},sizeField:{type:String,default:"size"},typeField:{type:String,default:"type"},buffer:{type:Number,default:200},pageMode:{type:Boolean,default:!1},prerender:{type:Number,default:0},emitUpdate:{type:Boolean,default:!1}}),data:function(){return{pool:[],totalSize:0,ready:!1,hoverKey:null}},computed:{sizes:function(){if(null===this.itemSize){for(var A,e={"-1":{accumulator:0}},t=this.items,i=this.sizeField,n=this.minItemSize,o=1e4,r=0,a=0,s=t.length;a<s;a++)A=t[a][i]||n,A<o&&(o=A),r+=A,e[a]={accumulator:r,size:A};return this.$_computedMinItemSize=o,e}return[]},simpleArray:z},watch:{items:function(){this.updateVisibleItems(!0)},pageMode:function(){this.applyPageMode(),this.updateVisibleItems(!1)},sizes:{handler:function(){this.updateVisibleItems(!1)},deep:!0}},created:function(){this.$_startIndex=0,this.$_endIndex=0,this.$_views=new Map,this.$_unusedViews=new Map,this.$_scrollDirty=!1,this.$_lastUpdateScrollPosition=0,this.prerender&&(this.$_prerender=!0,this.updateVisibleItems(!1))},mounted:function(){var A=this;this.applyPageMode(),this.$nextTick((function(){A.$_prerender=!1,A.updateVisibleItems(!0),A.ready=!0}))},beforeDestroy:function(){this.removeListeners()},methods:{addView:function(A,e,t,i,n){var o={item:t,position:0},r={id:U++,index:e,used:!0,key:i,type:n};return Object.defineProperty(o,"nr",{configurable:!1,value:r}),A.push(o),o},unuseView:function(A){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],t=this.$_unusedViews,i=A.nr.type,n=t.get(i);n||(n=[],t.set(i,n)),n.push(A),e||(A.nr.used=!1,A.position=-9999,this.$_views.delete(A.nr.key))},handleResize:function(){this.$emit("resize"),this.ready&&this.updateVisibleItems(!1)},handleScroll:function(A){var e=this;this.$_scrollDirty||(this.$_scrollDirty=!0,requestAnimationFrame((function(){e.$_scrollDirty=!1;var A=e.updateVisibleItems(!1,!0),t=A.continuous;t||(clearTimeout(e.$_refreshTimout),e.$_refreshTimout=setTimeout(e.handleScroll,100))})))},handleVisibilityChange:function(A,e){var t=this;this.ready&&(A||0!==e.boundingClientRect.width||0!==e.boundingClientRect.height?(this.$emit("visible"),requestAnimationFrame((function(){t.updateVisibleItems(!1)}))):this.$emit("hidden"))},updateVisibleItems:function(A){var e,t,i,n,o=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=this.itemSize,a=this.$_computedMinItemSize,s=this.typeField,l=this.simpleArray?null:this.keyField,g=this.items,c=g.length,I=this.sizes,Q=this.$_views,u=this.$_unusedViews,C=this.pool;if(c)if(this.$_prerender)e=0,t=this.prerender,i=null;else{var h=this.getScroll();if(o){var d=h.start-this.$_lastUpdateScrollPosition;if(d<0&&(d=-d),null===r&&d<a||d<r)return{continuous:!0}}this.$_lastUpdateScrollPosition=h.start;var B=this.buffer;if(h.start-=B,h.end+=B,null===r){var E,p,m=0,f=c-1,y=~~(c/2);do{p=y,E=I[y].accumulator,E<h.start?m=y:y<c-1&&I[y+1].accumulator>h.start&&(f=y),y=~~((m+f)/2)}while(y!==p);for(y<0&&(y=0),e=y,i=I[c-1].accumulator,t=y;t<c&&I[t].accumulator<h.end;t++);-1===t?t=g.length-1:(t++,t>c&&(t=c))}else e=~~(h.start/r),t=Math.ceil(h.end/r),e<0&&(e=0),t>c&&(t=c),i=c*r}else e=t=i=0;t-e>O.itemsLimit&&this.itemsLimitError(),this.totalSize=i;var b=e<=this.$_endIndex&&t>=this.$_startIndex;if(this.$_continuous!==b){if(b){Q.clear(),u.clear();for(var v=0,w=C.length;v<w;v++)n=C[v],this.unuseView(n)}this.$_continuous=b}else if(b)for(var N=0,D=C.length;N<D;N++)n=C[N],n.nr.used&&(A&&(n.nr.index=g.findIndex((function(A){return l?A[l]===n.item[l]:A===n.item}))),(-1===n.nr.index||n.nr.index<e||n.nr.index>=t)&&this.unuseView(n));for(var Y,M,k,x,G=b?null:new Map,j=e;j<t;j++){Y=g[j];var Z=l?Y[l]:Y;if(null==Z)throw new Error("Key is ".concat(Z," on item (keyField is '").concat(l,"')"));n=Q.get(Z),r||I[j].size?(n?(n.nr.used=!0,n.item=Y):(M=Y[s],k=u.get(M),b?k&&k.length?(n=k.pop(),n.item=Y,n.nr.used=!0,n.nr.index=j,n.nr.key=Z,n.nr.type=M):n=this.addView(C,j,Y,Z,M):(x=G.get(M)||0,(!k||x>=k.length)&&(n=this.addView(C,j,Y,Z,M),this.unuseView(n,!0),k=u.get(M)),n=k[x],n.item=Y,n.nr.used=!0,n.nr.index=j,n.nr.key=Z,n.nr.type=M,G.set(M,x+1),x++),Q.set(Z,n)),n.position=null===r?I[j-1].accumulator:j*r):n&&this.unuseView(n)}return this.$_startIndex=e,this.$_endIndex=t,this.emitUpdate&&this.$emit("update",e,t),clearTimeout(this.$_sortTimer),this.$_sortTimer=setTimeout(this.sortViews,300),{continuous:b}},getListenerTarget:function(){var A=k()(this.$el);return!window.document||A!==window.document.documentElement&&A!==window.document.body||(A=window),A},getScroll:function(){var A,e=this.$el,t=this.direction,i="vertical"===t;if(this.pageMode){var n=e.getBoundingClientRect(),o=i?n.height:n.width,r=-(i?n.top:n.left),a=i?window.innerHeight:window.innerWidth;r<0&&(a+=r,r=0),r+a>o&&(a=o-r),A={start:r,end:r+a}}else A=i?{start:e.scrollTop,end:e.scrollTop+e.clientHeight}:{start:e.scrollLeft,end:e.scrollLeft+e.clientWidth};return A},applyPageMode:function(){this.pageMode?this.addListeners():this.removeListeners()},addListeners:function(){this.listenerTarget=this.getListenerTarget(),this.listenerTarget.addEventListener("scroll",this.handleScroll,!!T&&{passive:!0}),this.listenerTarget.addEventListener("resize",this.handleResize)},removeListeners:function(){this.listenerTarget&&(this.listenerTarget.removeEventListener("scroll",this.handleScroll),this.listenerTarget.removeEventListener("resize",this.handleResize),this.listenerTarget=null)},scrollToItem:function(A){var e;e=null===this.itemSize?A>0?this.sizes[A-1].accumulator:0:A*this.itemSize,this.scrollToPosition(e)},scrollToPosition:function(A){"vertical"===this.direction?this.$el.scrollTop=A:this.$el.scrollLeft=A},itemsLimitError:function(){throw setTimeout((function(){})),new Error("Rendered items limit reached")},sortViews:function(){this.pool.sort((function(A,e){return A.nr.index-e.nr.index}))}}};function V(A,e,t,i,n,o,r,a,s,l){"boolean"!==typeof r&&(s=a,a=r,r=!1);var g,c="function"===typeof t?t.options:t;if(A&&A.render&&(c.render=A.render,c.staticRenderFns=A.staticRenderFns,c._compiled=!0,n&&(c.functional=!0)),i&&(c._scopeId=i),o?(g=function(A){A=A||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext,A||"undefined"===typeof __VUE_SSR_CONTEXT__||(A=__VUE_SSR_CONTEXT__),e&&e.call(this,s(A)),A&&A._registeredComponents&&A._registeredComponents.add(o)},c._ssrRegister=g):e&&(g=r?function(A){e.call(this,l(A,this.$root.$options.shadowRoot))}:function(A){e.call(this,a(A))}),g)if(c.functional){var I=c.render;c.render=function(A,e){return g.call(e),I(A,e)}}else{var Q=c.beforeCreate;c.beforeCreate=Q?[].concat(Q,g):[g]}return t}var _=W,L=function(){var A,e,t=this,i=t.$createElement,n=t._self._c||i;return n("div",{directives:[{name:"observe-visibility",rawName:"v-observe-visibility",value:t.handleVisibilityChange,expression:"handleVisibilityChange"}],staticClass:"vue-recycle-scroller",class:(A={ready:t.ready,"page-mode":t.pageMode},A["direction-"+t.direction]=!0,A),on:{"&scroll":function(A){return t.handleScroll(A)}}},[t.$slots.before?n("div",{staticClass:"vue-recycle-scroller__slot"},[t._t("before")],2):t._e(),t._v(" "),n("div",{ref:"wrapper",staticClass:"vue-recycle-scroller__item-wrapper",style:(e={},e["vertical"===t.direction?"minHeight":"minWidth"]=t.totalSize+"px",e)},t._l(t.pool,(function(A){return n("div",{key:A.nr.id,staticClass:"vue-recycle-scroller__item-view",class:{hover:t.hoverKey===A.nr.key},style:t.ready?{transform:"translate"+("vertical"===t.direction?"Y":"X")+"("+A.position+"px)"}:null,on:{mouseenter:function(e){t.hoverKey=A.nr.key},mouseleave:function(A){t.hoverKey=null}}},[t._t("default",null,{item:A.item,index:A.nr.index,active:A.nr.used})],2)})),0),t._v(" "),t.$slots.after?n("div",{staticClass:"vue-recycle-scroller__slot"},[t._t("after")],2):t._e(),t._v(" "),n("ResizeObserver",{on:{notify:t.handleResize}})],1)},H=[];L._withStripped=!0;var K=void 0,q=void 0,P=void 0,$=!1,AA=V({render:L,staticRenderFns:H},K,_,q,$,P,!1,void 0,void 0,void 0),eA={name:"DynamicScroller",components:{RecycleScroller:AA},inheritAttrs:!1,provide:function(){return"undefined"!==typeof ResizeObserver&&(this.$_resizeObserver=new ResizeObserver((function(A){var e,t=S(A);try{for(t.s();!(e=t.n()).done;){var i=e.value;if(i.target){var n=new CustomEvent("resize",{detail:{contentRect:i.contentRect}});i.target.dispatchEvent(n)}}}catch(o){t.e(o)}finally{t.f()}}))),{vscrollData:this.vscrollData,vscrollParent:this,vscrollResizeObserver:this.$_resizeObserver}},props:Z({},R,{minItemSize:{type:[Number,String],required:!0}}),data:function(){return{vscrollData:{active:!0,sizes:{},validSizes:{},keyField:this.keyField,simpleArray:!1}}},computed:{simpleArray:z,itemsWithSize:function(){for(var A=[],e=this.items,t=this.keyField,i=this.simpleArray,n=this.vscrollData.sizes,o=0;o<e.length;o++){var r=e[o],a=i?o:r[t],s=n[a];"undefined"!==typeof s||this.$_undefinedMap[a]||(s=0),A.push({item:r,id:a,size:s})}return A},listeners:function(){var A={};for(var e in this.$listeners)"resize"!==e&&"visible"!==e&&(A[e]=this.$listeners[e]);return A}},watch:{items:function(){this.forceUpdate(!1)},simpleArray:{handler:function(A){this.vscrollData.simpleArray=A},immediate:!0},direction:function(A){this.forceUpdate(!0)}},created:function(){this.$_updates=[],this.$_undefinedSizes=0,this.$_undefinedMap={}},activated:function(){this.vscrollData.active=!0},deactivated:function(){this.vscrollData.active=!1},methods:{onScrollerResize:function(){var A=this.$refs.scroller;A&&this.forceUpdate(),this.$emit("resize")},onScrollerVisible:function(){this.$emit("vscroll:update",{force:!1}),this.$emit("visible")},forceUpdate:function(){var A=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];(A||this.simpleArray)&&(this.vscrollData.validSizes={}),this.$emit("vscroll:update",{force:!0})},scrollToItem:function(A){var e=this.$refs.scroller;e&&e.scrollToItem(A)},getItemSize:function(A){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,t=this.simpleArray?null!=e?e:this.items.indexOf(A):A[this.keyField];return this.vscrollData.sizes[t]||0},scrollToBottom:function(){var A=this;if(!this.$_scrollingToBottom){this.$_scrollingToBottom=!0;var e=this.$el;this.$nextTick((function(){e.scrollTop=e.scrollHeight+5e3;var t=function t(){e.scrollTop=e.scrollHeight+5e3,requestAnimationFrame((function(){e.scrollTop=e.scrollHeight+5e3,0===A.$_undefinedSizes?A.$_scrollingToBottom=!1:requestAnimationFrame(t)}))};requestAnimationFrame(t)}))}}}},tA=eA,iA=function(){var A=this,e=A.$createElement,t=A._self._c||e;return t("RecycleScroller",A._g(A._b({ref:"scroller",attrs:{items:A.itemsWithSize,"min-item-size":A.minItemSize,direction:A.direction,"key-field":"id"},on:{resize:A.onScrollerResize,visible:A.onScrollerVisible},scopedSlots:A._u([{key:"default",fn:function(e){var t=e.item,i=e.index,n=e.active;return[A._t("default",null,null,{item:t.item,index:i,active:n,itemWithSize:t})]}}],null,!0)},"RecycleScroller",A.$attrs,!1),A.listeners),[A._v(" "),t("template",{slot:"before"},[A._t("before")],2),A._v(" "),t("template",{slot:"after"},[A._t("after")],2)],2)},nA=[];iA._withStripped=!0;var oA=void 0,rA=void 0,aA=void 0,sA=!1,lA=V({render:iA,staticRenderFns:nA},oA,tA,rA,sA,aA,!1,void 0,void 0,void 0),gA={name:"DynamicScrollerItem",inject:["vscrollData","vscrollParent","vscrollResizeObserver"],props:{item:{required:!0},watchData:{type:Boolean,default:!1},active:{type:Boolean,required:!0},index:{type:Number,default:void 0},sizeDependencies:{type:[Array,Object],default:null},emitResize:{type:Boolean,default:!1},tag:{type:String,default:"div"}},computed:{id:function(){return this.vscrollData.simpleArray?this.index:this.item[this.vscrollData.keyField]},size:function(){return this.vscrollData.validSizes[this.id]&&this.vscrollData.sizes[this.id]||0},finalActive:function(){return this.active&&this.vscrollData.active}},watch:{watchData:"updateWatchData",id:function(){this.size||this.onDataUpdate()},finalActive:function(A){this.size||(A?this.vscrollParent.$_undefinedMap[this.id]||(this.vscrollParent.$_undefinedSizes++,this.vscrollParent.$_undefinedMap[this.id]=!0):this.vscrollParent.$_undefinedMap[this.id]&&(this.vscrollParent.$_undefinedSizes--,this.vscrollParent.$_undefinedMap[this.id]=!1)),this.vscrollResizeObserver?A?this.observeSize():this.unobserveSize():A&&this.$_pendingVScrollUpdate===this.id&&this.updateSize()}},created:function(){var A=this;if(!this.$isServer&&(this.$_forceNextVScrollUpdate=null,this.updateWatchData(),!this.vscrollResizeObserver)){var e=function(e){A.$watch((function(){return A.sizeDependencies[e]}),A.onDataUpdate)};for(var t in this.sizeDependencies)e(t);this.vscrollParent.$on("vscroll:update",this.onVscrollUpdate),this.vscrollParent.$on("vscroll:update-size",this.onVscrollUpdateSize)}},mounted:function(){this.vscrollData.active&&(this.updateSize(),this.observeSize())},beforeDestroy:function(){this.vscrollParent.$off("vscroll:update",this.onVscrollUpdate),this.vscrollParent.$off("vscroll:update-size",this.onVscrollUpdateSize),this.unobserveSize()},methods:{updateSize:function(){this.finalActive?this.$_pendingSizeUpdate!==this.id&&(this.$_pendingSizeUpdate=this.id,this.$_forceNextVScrollUpdate=null,this.$_pendingVScrollUpdate=null,this.computeSize(this.id)):this.$_forceNextVScrollUpdate=this.id},updateWatchData:function(){var A=this;this.watchData?this.$_watchData=this.$watch("data",(function(){A.onDataUpdate()}),{deep:!0}):this.$_watchData&&(this.$_watchData(),this.$_watchData=null)},onVscrollUpdate:function(A){var e=A.force;!this.finalActive&&e&&(this.$_pendingVScrollUpdate=this.id),this.$_forceNextVScrollUpdate!==this.id&&!e&&this.size||this.updateSize()},onDataUpdate:function(){this.updateSize()},computeSize:function(A){var e=this;this.$nextTick((function(){if(e.id===A){var t=e.$el.offsetWidth,i=e.$el.offsetHeight;e.applySize(t,i)}e.$_pendingSizeUpdate=null}))},applySize:function(A,e){var t=Math.round("vertical"===this.vscrollParent.direction?e:A);t&&this.size!==t&&(this.vscrollParent.$_undefinedMap[this.id]&&(this.vscrollParent.$_undefinedSizes--,this.vscrollParent.$_undefinedMap[this.id]=void 0),this.$set(this.vscrollData.sizes,this.id,t),this.$set(this.vscrollData.validSizes,this.id,!0),this.emitResize&&this.$emit("resize",this.id))},observeSize:function(){this.vscrollResizeObserver&&(this.vscrollResizeObserver.observe(this.$el.parentNode),this.$el.parentNode.addEventListener("resize",this.onResize))},unobserveSize:function(){this.vscrollResizeObserver&&(this.vscrollResizeObserver.unobserve(this.$el.parentNode),this.$el.parentNode.removeEventListener("resize",this.onResize))},onResize:function(A){var e=A.detail.contentRect,t=e.width,i=e.height;this.applySize(t,i)}},render:function(A){return A(this.tag,this.$slots.default)}},cA=gA,IA=void 0,QA=void 0,uA=void 0,CA=void 0,hA=V({},IA,cA,QA,CA,uA,!1,void 0,void 0,void 0);function dA(A,e){A.component("".concat(e,"recycle-scroller"),AA),A.component("".concat(e,"RecycleScroller"),AA),A.component("".concat(e,"dynamic-scroller"),lA),A.component("".concat(e,"DynamicScroller"),lA),A.component("".concat(e,"dynamic-scroller-item"),hA),A.component("".concat(e,"DynamicScrollerItem"),hA)}var BA={version:"1.0.10",install:function(A,e){var t=Object.assign({},{installComponents:!0,componentsPrefix:""},e);for(var i in t)"undefined"!==typeof t[i]&&(O[i]=t[i]);t.installComponents&&dA(A,t.componentsPrefix)}},EA=null;"undefined"!==typeof window?EA=window.Vue:"undefined"!==typeof t.g&&(EA=t.g.Vue),EA&&EA.use(BA)},30190:function(A){"use strict";var e=function(){};A.exports=e},98878:function(A,e,t){var i=t(58418),n=t(98766);A.exports={changer:i,varyColor:n}},58418:function(A,e,t){t(32564);var i,n={};function o(){return"undefined"===typeof window?t.g:window}function r(A,e){if(A.length!==e.length)return!1;for(var t=0,i=A.length;t<i;t++)if(A[t]!==e[t])return!1;return!0}A.exports={_tryNum:0,changeColor:function(A,e){var t=e||o().Promise,a=this;if(!i){i=o()["tc_cfg_5582378869106086"];var s=I();if(s)return s}var l=A.oldColors||i.colors||[],g=A.newColors||[],c=i.url||A.cssUrl;return A.changeUrl&&(c=A.changeUrl(c)),new t((function(A,e){var t=n[c];t&&(l=t.colors),r(l,g)?A():Q(t,c,A,e)}));function I(){if(!i){if(a._tryNum<9)return a._tryNum=a._tryNum+1,new t((function(t,i){setTimeout((function(){t(a.changeColor(A,e))}),100)}));i={}}}function Q(e,t,i,o){var r=e&&document.getElementById(e.id);if(r&&e.colors)c(r.innerText),e.colors=g,i();else{var s="css_"+ +new Date;r=document.querySelector(A.appendToEl||"body").appendChild(document.createElement("style")),r.setAttribute("id",s),a.getCssString(t,(function(A){c(A),n[t]={id:s,colors:g},i()}),o)}function c(A){A=a.replaceCssText(A,l,g),r.innerText=A}}},replaceCssText:function(A,e,t){return e.forEach((function(e,i){var n=new RegExp(e.replace(/\s/g,"").replace(/,/g,",\\s*")+"([\\da-f]{2})?(\\b|\\)|,|\\s)","ig");A=A.replace(n,t[i]+"$1$2")})),A},getCssString:function(A,e,t){var n=i.cssCode;if(n)return i.cssCode="",void e(n);var o=new XMLHttpRequest;o.onreadystatechange=function(){4===o.readyState&&(200===o.status?e(o.responseText):t(o.status))},o.onerror=function(A){t(A)},o.ontimeout=function(A){t(A)},o.open("GET",A),o.send()}}},98766:function(A){function e(A){var e=A.toString(16);return 1===e.length&&(e="0"+e),e}function t(A,e){return n("fff",A,e)}function i(A,e){return n("000",A,e)}function n(A,t,i,n,o){A=a(A),t=a(t),void 0===i&&(i=.5),void 0===n&&(n=1),void 0===o&&(o=1);var s=2*i-1,l=n-o,g=((s*l===-1?s:(s+l)/(1+s*l))+1)/2,c=1-g,I=r(A),Q=r(t),u=Math.round(g*I[0]+c*Q[0]),C=Math.round(g*I[1]+c*Q[1]),h=Math.round(g*I[2]+c*Q[2]);return"#"+e(u)+e(C)+e(h)}function o(A,e,t){return n(A,t||"fff",.5,e,1-e)}function r(A){A=a(A),3===A.length&&(A=A[0]+A[0]+A[1]+A[1]+A[2]+A[2]);var e=parseInt(A.slice(0,2),16),t=parseInt(A.slice(2,4),16),i=parseInt(A.slice(4,6),16);return[e,t,i]}function a(A){return A.replace("#","")}function s(A){var e=r(A),t=l.apply(0,e);return[t[0].toFixed(0),(100*t[1]).toFixed(3)+"%",(100*t[2]).toFixed(3)+"%"].join(",")}function l(A,e,t){var i=A/255,n=e/255,o=t/255,r=Math.max(i,n,o),a=Math.min(i,n,o),s=r-a,l=(r+a)/2,g=0,c=0;if(Math.abs(s)>1e-5){c=l<=.5?s/(r+a):s/(2-r-a);var I=(r-i)/s,Q=(r-n)/s,u=(r-o)/s;g=i==r?u-Q:n==r?2+I-u:4+Q-I,g*=60,g<0&&(g+=360)}return[g,c,l]}A.exports={lighten:t,darken:i,mix:n,toNum3:r,rgb:o,rgbaToRgb:o,pad2:e,rgbToHsl:l,rrggbbToHsl:s}},28204:function(A,e,t){"use strict";t.d(e,{Z:function(){return X}});var i,n=t(48534),o=t(13087),r=t(62833),a=t(3336);t(36133),t(39575),t(38012),t(16716),t(65743),t(32564);function s(A){var e=i.__externref_table_alloc();return i.__wbindgen_export_2.set(e,A),e}function l(A,e){try{return A.apply(this,e)}catch(n){var t=s(n);i.__wbindgen_exn_store(t)}}var g="undefined"!==typeof TextDecoder?new TextDecoder("utf-8",{ignoreBOM:!0,fatal:!0}):{decode:function(){throw Error("TextDecoder not available")}};"undefined"!==typeof TextDecoder&&g.decode();var c=null;function I(){return null!==c&&0!==c.byteLength||(c=new Uint8Array(i.memory.buffer)),c}function Q(A,e){return A>>>=0,g.decode(I().subarray(A,A+e))}function u(A){return void 0===A||null===A}var C="undefined"===typeof FinalizationRegistry?{register:function(){},unregister:function(){}}:new FinalizationRegistry((function(A){i.__wbindgen_export_3.get(A.dtor)(A.a,A.b)}));function h(A,e,t,n){var o={a:A,b:e,cnt:1,dtor:t},r=function(){o.cnt++;var A=o.a;o.a=0;try{for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return n.apply(void 0,[A,o.b].concat(t))}finally{0===--o.cnt?(i.__wbindgen_export_3.get(o.dtor)(A,o.b),C.unregister(o)):o.a=A}};return r.original=o,C.register(r,o,o),r}function d(A){var e=(0,a.Z)(A);if("number"==e||"boolean"==e||null==A)return"".concat(A);if("string"==e)return'"'.concat(A,'"');if("symbol"==e){var t=A.description;return null==t?"Symbol":"Symbol(".concat(t,")")}if("function"==e){var i=A.name;return"string"==typeof i&&i.length>0?"Function(".concat(i,")"):"Function"}if(Array.isArray(A)){var n=A.length,o="[";n>0&&(o+=d(A[0]));for(var r=1;r<n;r++)o+=", "+d(A[r]);return o+="]",o}var s,l=/\[object ([^\]]+)\]/.exec(toString.call(A));if(!(l&&l.length>1))return toString.call(A);if(s=l[1],"Object"==s)try{return"Object("+JSON.stringify(A)+")"}catch(g){return"Object"}return A instanceof Error?"".concat(A.name,": ").concat(A.message,"\n").concat(A.stack):s}var B=0,E="undefined"!==typeof TextEncoder?new TextEncoder("utf-8"):{encode:function(){throw Error("TextEncoder not available")}},p="function"===typeof E.encodeInto?function(A,e){return E.encodeInto(A,e)}:function(A,e){var t=E.encode(A);return e.set(t),{read:A.length,written:t.length}};function m(A,e,t){if(void 0===t){var i=E.encode(A),n=e(i.length,1)>>>0;return I().subarray(n,n+i.length).set(i),B=i.length,n}for(var o=A.length,r=e(o,1)>>>0,a=I(),s=0;s<o;s++){var l=A.charCodeAt(s);if(l>127)break;a[r+s]=l}if(s!==o){0!==s&&(A=A.slice(s)),r=t(r,o,o=s+3*A.length,1)>>>0;var g=I().subarray(r+s,r+o),c=p(A,g);s+=c.written,r=t(r,o,s,1)>>>0}return B=s,r}var f=null;function y(){return(null===f||!0===f.buffer.detached||void 0===f.buffer.detached&&f.buffer!==i.memory.buffer)&&(f=new DataView(i.memory.buffer)),f}function b(A){var e=i.__wbindgen_export_2.get(A);return i.__externref_table_dealloc(A),e}function v(){i.start()}function w(A,e){i._dyn_core__ops__function__FnMut_____Output___R_as_wasm_bindgen__closure__WasmClosure___describe__invoke__ha1cc8fa1edd8ae4f(A,e)}function N(A,e,t){i.closure40_externref_shim(A,e,t)}function D(A,e,t,n){i.closure52_externref_shim(A,e,t,n)}var Y="undefined"===typeof FinalizationRegistry?{register:function(){},unregister:function(){}}:new FinalizationRegistry((function(A){return i.__wbg_noctuacore_free(A>>>0,1)})),M=function(){function A(e,t,n,r,a){(0,o.Z)(this,A);var s=i.noctuacore_new(e,t,n,r,a);return this.__wbg_ptr=s>>>0,Y.register(this,this.__wbg_ptr,this),this}return(0,r.Z)(A,[{key:"__destroy_into_raw",value:function(){var A=this.__wbg_ptr;return this.__wbg_ptr=0,Y.unregister(this),A}},{key:"free",value:function(){var A=this.__destroy_into_raw();i.__wbg_noctuacore_free(A,0)}},{key:"start",value:function(){var A=i.noctuacore_start(this.__wbg_ptr);if(A[1])throw b(A[0])}},{key:"is_success",value:function(){var A=i.noctuacore_is_success(this.__wbg_ptr);return 0!==A}},{key:"get_collected_data",value:function(){var A=i.noctuacore_get_collected_data(this.__wbg_ptr);return A}}]),A}();function k(A,e){return O.apply(this,arguments)}function O(){return O=(0,n.Z)(regeneratorRuntime.mark((function A(e,t){var i,n;return regeneratorRuntime.wrap((function(A){while(1)switch(A.prev=A.next){case 0:if(!("function"===typeof Response&&e instanceof Response)){A.next=23;break}if("function"!==typeof WebAssembly.instantiateStreaming){A.next=15;break}return A.prev=2,A.next=5,WebAssembly.instantiateStreaming(e,t);case 5:return A.abrupt("return",A.sent);case 8:if(A.prev=8,A.t0=A["catch"](2),"application/wasm"==e.headers.get("Content-Type")){A.next=14;break}A.next=15;break;case 14:throw A.t0;case 15:return A.next=17,e.arrayBuffer();case 17:return i=A.sent,A.next=20,WebAssembly.instantiate(i,t);case 20:return A.abrupt("return",A.sent);case 23:return A.next=25,WebAssembly.instantiate(e,t);case 25:if(n=A.sent,!(n instanceof WebAssembly.Instance)){A.next=30;break}return A.abrupt("return",{instance:n,module:e});case 30:return A.abrupt("return",n);case 31:case"end":return A.stop()}}),A,null,[[2,8]])}))),O.apply(this,arguments)}function x(){var A={wbg:{}};return A.wbg.__wbg_buffer_609cc3eee51ed158=function(A){var e=A.buffer;return e},A.wbg.__wbg_call_672a4d21634d4a24=function(){return l((function(A,e){var t=A.call(e);return t}),arguments)},A.wbg.__wbg_call_7cccdd69e0791ae2=function(){return l((function(A,e,t){var i=A.call(e,t);return i}),arguments)},A.wbg.__wbg_call_833bed5770ea2041=function(){return l((function(A,e,t,i){var n=A.call(e,t,i);return n}),arguments)},A.wbg.__wbg_crypto_574e78ad8b13b65f=function(A){var e=A.crypto;return e},A.wbg.__wbg_getRandomValues_b8f5dbd5f3995a9e=function(){return l((function(A,e){A.getRandomValues(e)}),arguments)},A.wbg.__wbg_instanceof_Promise_935168b8f4b49db3=function(A){var e;try{e=A instanceof Promise}catch(i){e=!1}var t=e;return t},A.wbg.__wbg_msCrypto_a61aeb35a24c1329=function(A){var e=A.msCrypto;return e},A.wbg.__wbg_new_23a2665fac83c611=function(A,e){try{var t={a:A,b:e},i=function(A,e){var i=t.a;t.a=0;try{return D(i,t.b,A,e)}finally{t.a=i}},n=new Promise(i);return n}finally{t.a=t.b=0}},A.wbg.__wbg_new_405e22f390576ce2=function(){var A=new Object;return A},A.wbg.__wbg_new_a12002a7f91c75be=function(A){var e=new Uint8Array(A);return e},A.wbg.__wbg_newnoargs_105ed471475aaf50=function(A,e){var t=new Function(Q(A,e));return t},A.wbg.__wbg_newwithbyteoffsetandlength_d97e637ebe145a9a=function(A,e,t){var i=new Uint8Array(A,e>>>0,t>>>0);return i},A.wbg.__wbg_newwithlength_a381634e90c276d4=function(A){var e=new Uint8Array(A>>>0);return e},A.wbg.__wbg_node_905d3e251edff8a2=function(A){var e=A.node;return e},A.wbg.__wbg_process_dc0fbacc7c1c06f7=function(A){var e=A.process;return e},A.wbg.__wbg_queueMicrotask_97d92b4fcc8a61c5=function(A){queueMicrotask(A)},A.wbg.__wbg_queueMicrotask_d3219def82552485=function(A){var e=A.queueMicrotask;return e},A.wbg.__wbg_randomFillSync_ac0988aba3254290=function(){return l((function(A,e){A.randomFillSync(e)}),arguments)},A.wbg.__wbg_require_60cc747a6bc5215a=function(){return l((function(){var A=module.require;return A}),arguments)},A.wbg.__wbg_resolve_4851785c9c5f573d=function(A){var e=Promise.resolve(A);return e},A.wbg.__wbg_set_65595bdd868b3009=function(A,e,t){A.set(e,t>>>0)},A.wbg.__wbg_set_bb8cecf6a62b9f46=function(){return l((function(A,e,t){var i=Reflect.set(A,e,t);return i}),arguments)},A.wbg.__wbg_static_accessor_GLOBAL_88a902d13a557d07=function(){var A="undefined"===typeof global?null:global;return u(A)?0:s(A)},A.wbg.__wbg_static_accessor_GLOBAL_THIS_56578be7e9f832b0=function(){var A="undefined"===typeof globalThis?null:globalThis;return u(A)?0:s(A)},A.wbg.__wbg_static_accessor_SELF_37c5d418e4bf5819=function(){var A="undefined"===typeof self?null:self;return u(A)?0:s(A)},A.wbg.__wbg_static_accessor_WINDOW_5de37043a91a9c40=function(){var A="undefined"===typeof window?null:window;return u(A)?0:s(A)},A.wbg.__wbg_subarray_aa9065fa9dc5df96=function(A,e,t){var i=A.subarray(e>>>0,t>>>0);return i},A.wbg.__wbg_then_44b73946d2fb3e7d=function(A,e){var t=A.then(e);return t},A.wbg.__wbg_then_48b406749878a531=function(A,e,t){var i=A.then(e,t);return i},A.wbg.__wbg_versions_c01dfd4722a88165=function(A){var e=A.versions;return e},A.wbg.__wbindgen_cb_drop=function(A){var e=A.original;if(1==e.cnt--)return e.a=0,!0;var t=!1;return t},A.wbg.__wbindgen_closure_wrapper111=function(A,e,t){var i=h(A,e,22,w);return i},A.wbg.__wbindgen_closure_wrapper187=function(A,e,t){var i=h(A,e,41,N);return i},A.wbg.__wbindgen_debug_string=function(A,e){var t=d(e),n=m(t,i.__wbindgen_malloc,i.__wbindgen_realloc),o=B;y().setInt32(A+4,o,!0),y().setInt32(A+0,n,!0)},A.wbg.__wbindgen_init_externref_table=function(){var A=i.__wbindgen_export_2,e=A.grow(4);A.set(0,void 0),A.set(e+0,void 0),A.set(e+1,null),A.set(e+2,!0),A.set(e+3,!1)},A.wbg.__wbindgen_is_function=function(A){var e="function"===typeof A;return e},A.wbg.__wbindgen_is_null=function(A){var e=null===A;return e},A.wbg.__wbindgen_is_object=function(A){var e=A,t="object"===(0,a.Z)(e)&&null!==e;return t},A.wbg.__wbindgen_is_string=function(A){var e="string"===typeof A;return e},A.wbg.__wbindgen_is_undefined=function(A){var e=void 0===A;return e},A.wbg.__wbindgen_memory=function(){var A=i.memory;return A},A.wbg.__wbindgen_number_get=function(A,e){var t=e,i="number"===typeof t?t:void 0;y().setFloat64(A+8,u(i)?0:i,!0),y().setInt32(A+0,!u(i),!0)},A.wbg.__wbindgen_number_new=function(A){var e=A;return e},A.wbg.__wbindgen_string_get=function(A,e){var t=e,n="string"===typeof t?t:void 0,o=u(n)?0:m(n,i.__wbindgen_malloc,i.__wbindgen_realloc),r=B;y().setInt32(A+4,r,!0),y().setInt32(A+0,o,!0)},A.wbg.__wbindgen_string_new=function(A,e){var t=Q(A,e);return t},A.wbg.__wbindgen_throw=function(A,e){throw new Error(Q(A,e))},A}function G(A,e){return i=A.exports,Z.__wbindgen_wasm_module=e,f=null,c=null,i.__wbindgen_start(),i}function j(A){if(void 0!==i)return i;if("undefined"!==typeof A&&Object.getPrototypeOf(A)===Object.prototype){var e=A;A=e.module}var t=x();A instanceof WebAssembly.Module||(A=new WebAssembly.Module(A));var n=new WebAssembly.Instance(A,t);return G(n,A)}function Z(A){return F.apply(this,arguments)}function F(){return F=(0,n.Z)(regeneratorRuntime.mark((function A(e){var t,n,o,r,a;return regeneratorRuntime.wrap((function(A){while(1)switch(A.prev=A.next){case 0:if(void 0===i){A.next=2;break}return A.abrupt("return",i);case 2:return"undefined"!==typeof e&&Object.getPrototypeOf(e)===Object.prototype&&(t=e,e=t.module_or_path),"undefined"===typeof e&&(e=new URL("data:application/wasm;base64,AGFzbQEAAAAByAIyYAAAYAABf2AAAW9gAX8AYAF/AX9gAX8Cf39gAX8Bb2ACf38AYAJ/fwF/YAJ/fwFvYAN/f38AYAN/f38Bf2ADf39/AW9gBH9/f38AYAR/f39/AX9gBX9/f39/AGAFf39/f38Bf2AGf39/f39/AGAGf39/f39/AX9gB39/f39/f38AYAd/f39/f39/AX9gBX9/fn9/AGAFf399f38AYAN/f3wAYAV/f3x/fwBgA39/bwBgBH9/b28AYAJ/fgBgBH9+f38AYAR/fn5/AGAFf35+fn4AYAR/fX9/AGACf3wAYAR/fH9/AGACf28AYAN+f38AYAR+fn9/AX9gAXwBf2ABfAFvYAFvAGABbwF/YAFvAW9gA29/fwFvYAJvbwBgAm9vAW9gA29vfwBgA29vbwF/YANvb28Bb2AEb29vbwFvYAVvb29vbwF/AqcNLwN3YmcVX193YmluZGdlbl9zdHJpbmdfZ2V0ACIDd2JnFV9fd2JpbmRnZW5fc3RyaW5nX25ldwAJA3diZxRfX3diaW5kZ2VuX2lzX3N0cmluZwAoA3diZxVfX3diaW5kZ2VuX251bWJlcl9nZXQAIgN3YmcSX193YmluZGdlbl9pc19udWxsACgDd2JnF19fd2JpbmRnZW5faXNfdW5kZWZpbmVkACgDd2JnFV9fd2JpbmRnZW5fbnVtYmVyX25ldwAmA3diZx1fX3diZ19jcnlwdG9fNTc0ZTc4YWQ4YjEzYjY1ZgApA3diZxRfX3diaW5kZ2VuX2lzX29iamVjdAAoA3diZx5fX3diZ19wcm9jZXNzX2RjMGZiYWNjN2MxYzA2ZjcAKQN3YmcfX193YmdfdmVyc2lvbnNfYzAxZGZkNDcyMmE4ODE2NQApA3diZxtfX3diZ19ub2RlXzkwNWQzZTI1MWVkZmY4YTIAKQN3YmceX193YmdfcmVxdWlyZV82MGNjNzQ3YTZiYzUyMTVhAAIDd2JnH19fd2JnX21zQ3J5cHRvX2E2MWFlYjM1YTI0YzEzMjkAKQN3YmclX193YmdfcmFuZG9tRmlsbFN5bmNfYWMwOTg4YWJhMzI1NDI5MAArA3diZyZfX3diZ19nZXRSYW5kb21WYWx1ZXNfYjhmNWRiZDVmMzk5NWE5ZQArA3diZyVfX3diZ19xdWV1ZU1pY3JvdGFza185N2Q5MmI0ZmNjOGE2MWM1ACcDd2JnJV9fd2JnX3F1ZXVlTWljcm90YXNrX2QzMjE5ZGVmODI1NTI0ODUAKQN3YmcWX193YmluZGdlbl9pc19mdW5jdGlvbgAoA3diZxJfX3diaW5kZ2VuX2NiX2Ryb3AAKAN3YmcgX193YmdfbmV3bm9hcmdzXzEwNWVkNDcxNDc1YWFmNTAACQN3YmcbX193YmdfY2FsbF82NzJhNGQyMTYzNGQ0YTI0ACwDd2JnGl9fd2JnX25ld180MDVlMjJmMzkwNTc2Y2UyAAIDd2JnG19fd2JnX2NhbGxfN2NjY2RkNjllMDc5MWFlMgAvA3diZxtfX3diZ19jYWxsXzgzM2JlZDU3NzBlYTIwNDEAMAN3YmcpX193YmdfaW5zdGFuY2VvZl9Qcm9taXNlXzkzNTE2OGI4ZjRiNDlkYjMAKAN3YmcaX193YmdfbmV3XzIzYTI2NjVmYWM4M2M2MTEACQN3YmceX193YmdfcmVzb2x2ZV80ODUxNzg1YzljNWY1NzNkACkDd2JnG19fd2JnX3RoZW5fNDRiNzM5NDZkMmZiM2U3ZAAsA3diZxtfX3diZ190aGVuXzQ4YjQwNjc0OTg3OGE1MzEALwN3YmcyX193Ymdfc3RhdGljX2FjY2Vzc29yX0dMT0JBTF9USElTXzU2NTc4YmU3ZTlmODMyYjAAAQN3YmcrX193Ymdfc3RhdGljX2FjY2Vzc29yX1NFTEZfMzdjNWQ0MThlNGJmNTgxOQABA3diZy1fX3diZ19zdGF0aWNfYWNjZXNzb3JfV0lORE9XXzVkZTM3MDQzYTkxYTljNDAAAQN3YmctX193Ymdfc3RhdGljX2FjY2Vzc29yX0dMT0JBTF84OGE5MDJkMTNhNTU3ZDA3AAEDd2JnHV9fd2JnX2J1ZmZlcl82MDljYzNlZWU1MWVkMTU4ACkDd2JnMV9fd2JnX25ld3dpdGhieXRlb2Zmc2V0YW5kbGVuZ3RoX2Q5N2U2MzdlYmUxNDVhOWEAKgN3YmcaX193YmdfbmV3X2ExMjAwMmE3ZjkxYzc1YmUAKQN3YmcaX193Ymdfc2V0XzY1NTk1YmRkODY4YjMwMDkALQN3YmckX193YmdfbmV3d2l0aGxlbmd0aF9hMzgxNjM0ZTkwYzI3NmQ0AAYDd2JnH19fd2JnX3N1YmFycmF5X2FhOTA2NWZhOWRjNWRmOTYAKgN3YmcaX193Ymdfc2V0X2JiOGNlY2Y2YTYyYjlmNDYALgN3YmcXX193YmluZGdlbl9kZWJ1Z19zdHJpbmcAIgN3YmcQX193YmluZGdlbl90aHJvdwAHA3diZxFfX3diaW5kZ2VuX21lbW9yeQACA3diZx1fX3diaW5kZ2VuX2Nsb3N1cmVfd3JhcHBlcjExMQAMA3diZx1fX3diaW5kZ2VuX2Nsb3N1cmVfd3JhcHBlcjE4NwAMA3diZx9fX3diaW5kZ2VuX2luaXRfZXh0ZXJucmVmX3RhYmxlAAAD+QL3AgQICAoPDQsPCA4SCyMKCAsPCgMNCwgLJAcTEwMHCAsECgoIBwELCgQUCAcKAwgIBxAIEQ0HCAMDCg8HBBEIAw8ICA0KCgQbBwMHBwoHBA8HAw8ICgMHBw8NDQcDBwgIAwogCB4PAwoNBwcDEQcHCgoKBAoKBAcKCgcHBwMdAwoKCgcHCg0ECgoHBwcEBA8QDQMDAwsNBwMHAwMKCwgDBwcHCA8HBAcLCgQDBwMKCgMDCAcEBwoxBwQEAwMQAwQHBAcIAwMDAwMICAcHDggHBwoKAwcDDQMDCAEBBRcSCA4DEA8PGBUQFg8OAwMKCgoDDQsLGgMHBwcLBAQEAwcDAwgDCAgLCwcKCwsDAwMDAwMKAwgIBAQEBAQECAgEBAQOAwMDCAgLJQQZCwgPAQcBCwEICAgICAgKBwcHCAgECgYKAQQICwcKCggDBwQIBwgIBwMAAAgHCAgHCAsLBwcHCAQHAwQEBAQDBAQEBAEAAAABBAQBAAgAAwQJAnABYmJvAIABBQMBABEGCQF/AUGAgMAACwf9AxIGbWVtb3J5AgAVX193Ymdfbm9jdHVhY29yZV9mcmVlAJsBDm5vY3R1YWNvcmVfbmV3AOQBEG5vY3R1YWNvcmVfc3RhcnQAiQIVbm9jdHVhY29yZV9pc19zdWNjZXNzAOYBHW5vY3R1YWNvcmVfZ2V0X2NvbGxlY3RlZF9kYXRhAO0CBXN0YXJ0AJ0DFF9fd2JpbmRnZW5fZXhuX3N0b3JlANACF19fZXh0ZXJucmVmX3RhYmxlX2FsbG9jAJoDE19fd2JpbmRnZW5fZXhwb3J0XzIBARNfX3diaW5kZ2VuX2V4cG9ydF8zAQARX193YmluZGdlbl9tYWxsb2MA3wESX193YmluZGdlbl9yZWFsbG9jAPoBGV9fZXh0ZXJucmVmX3RhYmxlX2RlYWxsb2MA6QF6X2R5bl9jb3JlX19vcHNfX2Z1bmN0aW9uX19Gbk11dF9fX19fT3V0cHV0X19fUl9hc193YXNtX2JpbmRnZW5fX2Nsb3N1cmVfX1dhc21DbG9zdXJlX19fZGVzY3JpYmVfX2ludm9rZV9faGExY2M4ZmExZWRkOGFlNGYApAIYY2xvc3VyZTQwX2V4dGVybnJlZl9zaGltANYCGGNsb3N1cmU1Ml9leHRlcm5yZWZfc2hpbQChAhBfX3diaW5kZ2VuX3N0YXJ0AJwDCcsBBABBAQsT4QLAAvYC3wKiAjC4AowBmQKDAX+nAv4BZ+ACuQKoAv8BWgBBFQsSpALtAaMCb/YB/AKGAqkChAKxAagBsAGnAXf9AaoBzAHYAQBBKAs4nALvAZsCjgK3AaUCU7oCuwK8Ar0CpgKeApQCkQKQApoBlgKTApICjwKVAosC5AK/AvsBpAGtAsoBXYID5gLnAukC1AHoAoMDmAKRAWN7jwOvAtUBXIQDhQPSAtEC2ALqAkSNAUXLAYcDBEHgAAsC0tYCC9KhAgsKtIAE9wLNIwIJfwF+IwBBEGsiASQAAkACQAJAAkACQAJAAkACQCAAQfUBSQ0AAkAgAEHM/3tNDQBBACEADAgLIABBC2oiAkF4cSEDQQAoAsDYQCIERQ0EQR8hBQJAIABB9P//B0sNACADQQYgAkEIdmciAGt2QQFxIABBAXRrQT5qIQULQQAgA2shAgJAIAVBAnRBpNXAAGooAgAiBg0AQQAhAEEAIQcMAgtBACEAIANBAEEZIAVBAXZrIAVBH0YbdCEIQQAhBwNAAkAgBiIGKAIEQXhxIgkgA0kNACAJIANrIgkgAk8NACAJIQIgBiEHIAkNAEEAIQIgBiEHIAYhAAwECyAGKAIUIgkgACAJIAYgCEEddkEEcWpBEGooAgAiBkcbIAAgCRshACAIQQF0IQggBkUNAgwACwsCQEEAKAK82EAiBkEQIABBC2pB+ANxIABBC0kbIgNBA3YiAnYiAEEDcUUNAAJAAkAgAEF/c0EBcSACaiIIQQN0IgNBtNbAAGoiACADQbzWwABqKAIAIgIoAggiB0YNACAHIAA2AgwgACAHNgIIDAELQQAgBkF+IAh3cTYCvNhACyACQQhqIQAgAiADQQNyNgIEIAIgA2oiAyADKAIEQQFyNgIEDAcLIANBACgCxNhATQ0DAkACQAJAIAANAEEAKALA2EAiAEUNBiAAaEECdEGk1cAAaigCACIHKAIEQXhxIANrIQIgByEGA0ACQCAHKAIQIgANACAHKAIUIgANACAGKAIYIQUCQAJAAkAgBigCDCIAIAZHDQAgBkEUQRAgBigCFCIAG2ooAgAiBw0BQQAhAAwCCyAGKAIIIgcgADYCDCAAIAc2AggMAQsgBkEUaiAGQRBqIAAbIQgDQCAIIQkgByIAQRRqIABBEGogACgCFCIHGyEIIABBFEEQIAcbaigCACIHDQALIAlBADYCAAsgBUUNBAJAIAYoAhxBAnRBpNXAAGoiBygCACAGRg0AIAVBEEEUIAUoAhAgBkYbaiAANgIAIABFDQUMBAsgByAANgIAIAANA0EAQQAoAsDYQEF+IAYoAhx3cTYCwNhADAQLIAAoAgRBeHEgA2siByACIAcgAkkiBxshAiAAIAYgBxshBiAAIQcMAAsLAkACQCAAIAJ0QQIgAnQiAEEAIABrcnFoIglBA3QiAkG01sAAaiIHIAJBvNbAAGooAgAiACgCCCIIRg0AIAggBzYCDCAHIAg2AggMAQtBACAGQX4gCXdxNgK82EALIAAgA0EDcjYCBCAAIANqIgggAiADayIHQQFyNgIEIAAgAmogBzYCAAJAQQAoAsTYQCIGRQ0AIAZBeHFBtNbAAGohAkEAKALM2EAhAwJAAkBBACgCvNhAIglBASAGQQN2dCIGcQ0AQQAgCSAGcjYCvNhAIAIhBgwBCyACKAIIIQYLIAIgAzYCCCAGIAM2AgwgAyACNgIMIAMgBjYCCAsgAEEIaiEAQQAgCDYCzNhAQQAgBzYCxNhADAgLIAAgBTYCGAJAIAYoAhAiB0UNACAAIAc2AhAgByAANgIYCyAGKAIUIgdFDQAgACAHNgIUIAcgADYCGAsCQAJAAkAgAkEQSQ0AIAYgA0EDcjYCBCAGIANqIgMgAkEBcjYCBCADIAJqIAI2AgBBACgCxNhAIghFDQEgCEF4cUG01sAAaiEHQQAoAszYQCEAAkACQEEAKAK82EAiCUEBIAhBA3Z0IghxDQBBACAJIAhyNgK82EAgByEIDAELIAcoAgghCAsgByAANgIIIAggADYCDCAAIAc2AgwgACAINgIIDAELIAYgAiADaiIAQQNyNgIEIAYgAGoiACAAKAIEQQFyNgIEDAELQQAgAzYCzNhAQQAgAjYCxNhACyAGQQhqIQAMBgsCQCAAIAdyDQBBACEHQQIgBXQiAEEAIABrciAEcSIARQ0DIABoQQJ0QaTVwABqKAIAIQALIABFDQELA0AgACAHIAAoAgRBeHEiBiADayIJIAJJIgUbIQQgBiADSSEIIAkgAiAFGyEJAkAgACgCECIGDQAgACgCFCEGCyAHIAQgCBshByACIAkgCBshAiAGIQAgBg0ACwsgB0UNAAJAQQAoAsTYQCIAIANJDQAgAiAAIANrTw0BCyAHKAIYIQUCQAJAAkAgBygCDCIAIAdHDQAgB0EUQRAgBygCFCIAG2ooAgAiBg0BQQAhAAwCCyAHKAIIIgYgADYCDCAAIAY2AggMAQsgB0EUaiAHQRBqIAAbIQgDQCAIIQkgBiIAQRRqIABBEGogACgCFCIGGyEIIABBFEEQIAYbaigCACIGDQALIAlBADYCAAsgBUUNAgJAIAcoAhxBAnRBpNXAAGoiBigCACAHRg0AIAVBEEEUIAUoAhAgB0YbaiAANgIAIABFDQMMAgsgBiAANgIAIAANAUEAQQAoAsDYQEF+IAcoAhx3cTYCwNhADAILAkACQAJAAkACQAJAQQAoAsTYQCIAIANPDQACQEEAKALI2EAiACADSw0AIAFBBGpB6NjAACADQa+ABGpBgIB8cRDjAQJAIAEoAgQiBg0AQQAhAAwKCyABKAIMIQVBAEEAKALU2EAgASgCCCIJaiIANgLU2EBBACAAQQAoAtjYQCICIAAgAksbNgLY2EACQAJAAkBBACgC0NhAIgJFDQBBpNbAACEAA0AgBiAAKAIAIgcgACgCBCIIakYNAiAAKAIIIgANAAwDCwsCQAJAQQAoAuDYQCIARQ0AIAYgAE8NAQtBACAGNgLg2EALQQBB/x82AuTYQEEAIAU2ArDWQEEAIAk2AqjWQEEAIAY2AqTWQEEAQbTWwAA2AsDWQEEAQbzWwAA2AsjWQEEAQbTWwAA2ArzWQEEAQcTWwAA2AtDWQEEAQbzWwAA2AsTWQEEAQczWwAA2AtjWQEEAQcTWwAA2AszWQEEAQdTWwAA2AuDWQEEAQczWwAA2AtTWQEEAQdzWwAA2AujWQEEAQdTWwAA2AtzWQEEAQeTWwAA2AvDWQEEAQdzWwAA2AuTWQEEAQezWwAA2AvjWQEEAQeTWwAA2AuzWQEEAQfTWwAA2AoDXQEEAQezWwAA2AvTWQEEAQfTWwAA2AvzWQEEAQfzWwAA2AojXQEEAQfzWwAA2AoTXQEEAQYTXwAA2ApDXQEEAQYTXwAA2AozXQEEAQYzXwAA2ApjXQEEAQYzXwAA2ApTXQEEAQZTXwAA2AqDXQEEAQZTXwAA2ApzXQEEAQZzXwAA2AqjXQEEAQZzXwAA2AqTXQEEAQaTXwAA2ArDXQEEAQaTXwAA2AqzXQEEAQazXwAA2ArjXQEEAQazXwAA2ArTXQEEAQbTXwAA2AsDXQEEAQbzXwAA2AsjXQEEAQbTXwAA2ArzXQEEAQcTXwAA2AtDXQEEAQbzXwAA2AsTXQEEAQczXwAA2AtjXQEEAQcTXwAA2AszXQEEAQdTXwAA2AuDXQEEAQczXwAA2AtTXQEEAQdzXwAA2AujXQEEAQdTXwAA2AtzXQEEAQeTXwAA2AvDXQEEAQdzXwAA2AuTXQEEAQezXwAA2AvjXQEEAQeTXwAA2AuzXQEEAQfTXwAA2AoDYQEEAQezXwAA2AvTXQEEAQfzXwAA2AojYQEEAQfTXwAA2AvzXQEEAQYTYwAA2ApDYQEEAQfzXwAA2AoTYQEEAQYzYwAA2ApjYQEEAQYTYwAA2AozYQEEAQZTYwAA2AqDYQEEAQYzYwAA2ApTYQEEAQZzYwAA2AqjYQEEAQZTYwAA2ApzYQEEAQaTYwAA2ArDYQEEAQZzYwAA2AqTYQEEAQazYwAA2ArjYQEEAQaTYwAA2AqzYQEEAIAZBD2pBeHEiAEF4aiICNgLQ2EBBAEGs2MAANgK02EBBACAGIABrIAlBWGoiAGpBCGoiBzYCyNhAIAIgB0EBcjYCBCAGIABqQSg2AgRBAEGAgIABNgLc2EAMCAsgAiAGTw0AIAcgAksNACAAKAIMIgdBAXENACAHQQF2IAVGDQMLQQBBACgC4NhAIgAgBiAAIAZJGzYC4NhAIAYgCWohB0Gk1sAAIQACQAJAAkADQCAAKAIAIgggB0YNASAAKAIIIgANAAwCCwsgACgCDCIHQQFxDQAgB0EBdiAFRg0BC0Gk1sAAIQACQANAAkAgACgCACIHIAJLDQAgAiAHIAAoAgRqIgdJDQILIAAoAgghAAwACwtBACAGQQ9qQXhxIgBBeGoiCDYC0NhAQQAgBiAAayAJQVhqIgBqQQhqIgQ2AsjYQCAIIARBAXI2AgQgBiAAakEoNgIEQQBBgICAATYC3NhAIAIgB0FgakF4cUF4aiIAIAAgAkEQakkbIghBGzYCBEEAKQKk1kAhCiAIQRBqQQApAqzWQDcCACAIIAo3AghBACAFNgKw1kBBACAJNgKo1kBBACAGNgKk1kBBACAIQQhqNgKs1kAgCEEcaiEAA0AgAEEHNgIAIABBBGoiACAHSQ0ACyAIIAJGDQcgCCAIKAIEQX5xNgIEIAIgCCACayIAQQFyNgIEIAggADYCAAJAIABBgAJJDQAgAiAAEF4MCAsgAEH4AXFBtNbAAGohBwJAAkBBACgCvNhAIgZBASAAQQN2dCIAcQ0AQQAgBiAAcjYCvNhAIAchAAwBCyAHKAIIIQALIAcgAjYCCCAAIAI2AgwgAiAHNgIMIAIgADYCCAwHCyAAIAY2AgAgACAAKAIEIAlqNgIEIAZBD2pBeHFBeGoiByADQQNyNgIEIAhBD2pBeHFBeGoiAiAHIANqIgBrIQMgAkEAKALQ2EBGDQMgAkEAKALM2EBGDQQCQCACKAIEIgZBA3FBAUcNACACIAZBeHEiBhBZIAYgA2ohAyACIAZqIgIoAgQhBgsgAiAGQX5xNgIEIAAgA0EBcjYCBCAAIANqIAM2AgACQCADQYACSQ0AIAAgAxBeDAYLIANB+AFxQbTWwABqIQICQAJAQQAoArzYQCIGQQEgA0EDdnQiA3ENAEEAIAYgA3I2ArzYQCACIQMMAQsgAigCCCEDCyACIAA2AgggAyAANgIMIAAgAjYCDCAAIAM2AggMBQtBACAAIANrIgI2AsjYQEEAQQAoAtDYQCIAIANqIgc2AtDYQCAHIAJBAXI2AgQgACADQQNyNgIEIABBCGohAAwIC0EAKALM2EAhAgJAAkAgACADayIHQQ9LDQBBAEEANgLM2EBBAEEANgLE2EAgAiAAQQNyNgIEIAIgAGoiACAAKAIEQQFyNgIEDAELQQAgBzYCxNhAQQAgAiADaiIGNgLM2EAgBiAHQQFyNgIEIAIgAGogBzYCACACIANBA3I2AgQLIAJBCGohAAwHCyAAIAggCWo2AgRBAEEAKALQ2EAiAEEPakF4cSICQXhqIgc2AtDYQEEAIAAgAmtBACgCyNhAIAlqIgJqQQhqIgY2AsjYQCAHIAZBAXI2AgQgACACakEoNgIEQQBBgICAATYC3NhADAMLQQAgADYC0NhAQQBBACgCyNhAIANqIgM2AsjYQCAAIANBAXI2AgQMAQtBACAANgLM2EBBAEEAKALE2EAgA2oiAzYCxNhAIAAgA0EBcjYCBCAAIANqIAM2AgALIAdBCGohAAwDC0EAIQBBACgCyNhAIgIgA00NAkEAIAIgA2siAjYCyNhAQQBBACgC0NhAIgAgA2oiBzYC0NhAIAcgAkEBcjYCBCAAIANBA3I2AgQgAEEIaiEADAILIAAgBTYCGAJAIAcoAhAiBkUNACAAIAY2AhAgBiAANgIYCyAHKAIUIgZFDQAgACAGNgIUIAYgADYCGAsCQAJAIAJBEEkNACAHIANBA3I2AgQgByADaiIAIAJBAXI2AgQgACACaiACNgIAAkAgAkGAAkkNACAAIAIQXgwCCyACQfgBcUG01sAAaiEDAkACQEEAKAK82EAiBkEBIAJBA3Z0IgJxDQBBACAGIAJyNgK82EAgAyECDAELIAMoAgghAgsgAyAANgIIIAIgADYCDCAAIAM2AgwgACACNgIIDAELIAcgAiADaiIAQQNyNgIEIAcgAGoiACAAKAIEQQFyNgIECyAHQQhqIQALIAFBEGokACAAC6caAQ9/IwBBwAJrIgIkAAJAAkACQAJAAkACQAJAAkACQAJAAkACQAJAAkACQAJAAkACQAJAAkACQAJAAkACQAJAAkACQAJAAkACQAJAAkACQAJAAkACQAJAAkACQAJAAkACQAJAAkACQCAALQCAAQ4EARwFAAELIABBFGohAyAAQSRqIQQCQCAALQAkDgUAGAUDBAALIAMoAgAhBQwBCyAAQQA6ACQgACAANgIUIABBJGohBCAAQRRqIQMgACEFCyAAIAU2AhggAkGQAWpBrIfAACAFEFUgACACKQOQATcCHCACIABBHGo2AoQCIAIgACgCGDYCgAIgACACQYACakHAh8AAEI0DEFY2AigLIAJBiAFqIABBKGoiBiABEIIBQQIhBSACKAKIASIHQQJGDRUgAigCjAEhCCAGKAIAIgUgBSgCAEF/aiIFNgIAAkAgBQ0AIAYQlAELQQEhBSAHDSUCQCAIQYQBSQ0AIAgQ6QELIABBADoAdSAAIAAoAhgiBTYCcCAAIAAoAiAiCDYCKCAAQfUAaiEHDAMLIABBKGohBiAAQfUAaiEHIAAtAHUOBwEEAAMJBw0BCwALIAAoAighCCAAKAJwIQULIAAgCDYCMCAAIAU2AixBACEFIABBADoAdAJAQayHwAAQ4QENAAwgC0Gsh8AAEPQBIAJBAjYChAIgAkGkiMAANgKAAiACQgI3AowCIAJBAzYC4AEgAiAAQTBqNgLcASACQQE2AtgBIAJBmIjAADYC1AEgAiACQdQBajYCiAIgAkGcAWogAkGAAmoQUiAAIAIpApwBNwI0IABBPGogAkGkAWooAgAiBTYCACACQYABaiAAKAIsIAAoAjggBRBCQQEhBSACKAKEASEIIAIoAoABDR4gACAIEFY2AngLIAJB+ABqIABB+ABqIgkgARCCAUECIQUgAigCeCIKQQJGDQ0gAigCfCEIIAkoAgAiBSAFKAIAQX9qIgU2AgACQCAFDQAgCRCUAQtBASEFIAoNGyAAIAg2AkAgAEEBOgB0IAJB8ABqIAgQigMCQCACKAJwIgVFDQAgAkHoAGogBSACKAJ0ENYBIAJB1AFqIAIoAmggAigCbBC+AiACKALUAUGAgICAeEcNAgsgAEEAOgB0IAAgACgCQCIFNgJEDAILQbSIwAAQ8wEACyACQYACakEIaiACQdQBakEIaigCACIFNgIAIAIgAikC1AE3A4ACIAAgAigChAIgBRDBAjYCRCACQYACahClAyACQYACahD/AiAAKAJEIQULIAUQkgNBAUYNASAAQcQAaiIFKAIAEJMDQQFGDQEgAkGoAWogBRC4AQJAIAIoAqgBQYCAgIB4Rw0AQQEhCkHEiMAAQSQQwQIhCAwYCyAAIAIpAqgBNwJIIABB0ABqIAJBsAFqKAIAIgU2AgAgAkHgAGogACgCTCAFEE8gAigCYEUNFSACQdgAaiAAQcgAaiIFIAIoAmQiCBC1AUEBIQogAigCXCELIAIoAlghDCACQdAAaiAFIAhBAWoQrQEgAigCVCEFIAIoAlAhCCACQeQBaiAAIAwgCxA0IAIoAuQBQYCAgIB4Rw0OIAItAOgBIQogAkG0AWogCCAFEKUBIABB3ABqIAJBtAFqQQhqKAIANgIAIAAgAikCtAE3AlQCQCAKDQBBrIfAABD1AQwVCyAAIAAoAlggACgCXBDBAjYCYCACQQI2AoQCIAJBpIjAADYCgAIgAkICNwKMAiACQQM2AuABIAIgAEEwajYC3AEgAkEBNgLYASACQeiJwAA2AtQBIAIgAkHUAWo2AogCIAJByAFqIAJBgAJqEIQBIAAgAikCyAE3AmQgAEHsAGogAkHIAWpBCGooAgAiBTYCACACQcgAaiAAKAIsIAAoAmggBSAAQeAAahA/IAIoAkwhCCACKAJIDRIgCSAIEFY2AgALIAJBwABqIABB+ABqIgogARCCAUECIQUgAigCQCIJQQJGDQIgAigCRCEIIAooAgAiBSAFKAIAQX9qIgU2AgACQCAFDQAgChCUAQsgCQ0RIAJBOGogCBCKAwJAIAIoAjgiBUUNACACQTBqIAUgAigCPBDWASACQdQBaiACKAIwIAIoAjQQvgIgAigC1AFBgICAgHhHDQQLIAogCDYCAAwECwJAQayHwAAQ1wENAEEAIQoMFgtBAC0AgdVAGiAAKAIsIQhB7ABBBBDiAiIFRQ0NIAVBADoAECAFIAg2AgAgCSAFNgIACyACQQhqIABB+ABqIgkgARAyQQIhBQJAIAIoAggiCkECRg0AIAIoAgwhCCAJKAIAIgUQ3QEgBUHsAEEEEO4CDBULIAdBBDoAAAwICyAHQQU6AAAMBwsgAkGAAmpBCGogAkHUAWpBCGooAgAiBTYCACACIAIpAtQBNwOAAiAKIAIoAoQCIAUQwQI2AgAgAkGAAmoQpQMgAkGAAmoQ/wIgCEGEAUkNACAIEOkBCyACQShqIAooAgAQigMCQCACKAIoIgVFDQAgAkEgaiAFIAIoAiwQ1gEgAkHkAWogAigCICACKAIkEL4CIAIoAuQBQYCAgIB4Rg0AIAJB8AFqQQhqIAJB5AFqQQhqKAIAIgU2AgAgAiACKQLkATcD8AEgAigC9AEhCAJAAkAgBUEHSw0AQZSKwABBByAIIAUQlwIhBQwBCyACQYACaiAIIAVBlIrAAEEHEDMCQAJAIAIoAoACDQBBACENAkAgAi0AjgINACACLQCMAiELIAIoArQCIQwgAigCsAIhDiACKAKEAiEFAkADQAJAIAVFDQACQCAFIAxJDQAgBSAMRg0BDBQLIA4gBWosAABBv39MDRMLAkAgBSAMRg0AAkACQCAOIAVqIgksAAAiCEF/TA0AIAhB/wFxIQgMAQsgCS0AAUE/cSEPIAhBH3EhEAJAIAhBX0sNACAQQQZ0IA9yIQgMAQsgD0EGdCAJLQACQT9xciEPAkAgCEFwTw0AIA8gEEEMdHIhCAwBCyAPQQZ0IAktAANBP3FyIBBBEnRBgIDwAHFyIQgLIAtBAXENAkEBIQtBASEJAkAgCEGAAUkNAEECIQkgCEGAEEkNAEEDQQQgCEGAgARJGyEJCyAJIAVqIQUMAQsLIAtBAXFFDQELQQEhDQsgAiANNgLUAQwBCyACQYgCaiEFIAIoArwCIQggAigCuAIhCSACKAK0AiELIAIoArACIQwCQCACKAKkAkF/Rg0AIAJB1AFqIAUgDCALIAkgCEEAEEgMAQsgAkHUAWogBSAMIAsgCSAIQQEQSAsgAigC1AEhBQsgAkHwAWoQpQMgAkHwAWoQ/wIgBQ0CCyACQRhqQayHwAAQxAEgAi0AGEUNAiAAKAIsIQgQhwIiBUEAOgAQIAUgCDYCACAAIAU2AnwLIAJBEGogAEH8AGogARAyQQIhBSACKAIQIgpBAkYNAiACKAIUIQggACgCfCIFEN0BIAVB7ABBBBDuAiAAKAJ4IgVBhAFJDQwgBRDpAQwMC0Gsh8AAIABB4ABqEJgBIAAoAixBARAxGgsCQCAKKAIAIgVBhAFJDQAgBRDpAQsgAEHkAGoiBRClAyAFEP8CIAAoAmAiBUGEAUkNCyAFEOkBDAsLIAdBBjoAAAwBCyAHQQM6AAALDBALQdSHwAAQ8wEACyAEQQM6AAAMEQsgAkHwAWpBCGogAkHkAWpBCGooAgA2AgAgAiACKQLkATcD8AEgAkEBNgKEAiACQYyKwAA2AoACIAJCATcCjAIgAkEENgLEASACIAJBwAFqNgKIAiACIAJB8AFqNgLAASACQdQBaiACQYACahCEASACKALYASACKALcARDBAiEIIAJB1AFqEKUDIAJB1AFqEP8CIAJB8AFqEKUDIAJB8AFqEP8CDAcLQQRB7AAQhgMAC0Gwh8AAEPMBAAsgDiAMIAUgDEGMhMAAENkCAAtBASEKCyAAQeQAaiIFEKUDIAUQ/wICQCAAKAJgIgVBhAFJDQAgBRDpAQsgAEHUAGoiBRClAyAFEP8CDAILIABB1ABqIgUQpQMgBRD/AiAAQcgAaiIFEKUDIAUQ/wICQCAAKAJEIgVBhAFJDQAgBRDpAQsgAEH0AGohAUEAIQUCQCAALQB0RQ0AIAAoAkAiCEGEAUkNACAIEOkBDAULDAQLQQEhCkHoiMAAQSoQwQIhCAsgAEHIAGoiBRClAyAFEP8CCwJAIAAoAkQiBUGEAUkNACAFEOkBCwJAIAAtAHRFDQAgACgCQCIFQYQBSQ0AIAUQ6QELIAohBQsgAEH0AGohAQsgAUEAOgAACyAAQTRqIgEQpQMgARD/AgsgB0EBOgAACyAFQQJGDQEgBhBlCyAEQQE6AAAMAgsgBEEEOgAAQQIhBQsLQQMhAQJAIAVBAkYiBA0AIAMQ3QECQAJAAkAgBUEBcUUNACAIQYMBSw0BDAILIAVFDQEgCEGEAUkNAQsgCBDpAQsgABCsAUEBIQELIAAgAToAgAEgAkHAAmokACAEC44ZAgt/BH4jAEHQA2siAiQAIAJBgAFqIAAQaSACQQE2AnQgAkG4hsAAQbeGwAAgARs2AnAgAkEDNgKgASACQdSGwAA2ApwBIAJCAzcCqAEgAkEBNgLUASACQciGwAA2AtABIAJBAjYCzAEgAkEBNgLEASACIAJBwAFqNgKkASACIAJBgAFqNgLIASACIAJB8ABqNgLAASACQbQBaiACQZwBahBSIAIoArgBIQEgAigCvAEhAyACQQA2AswDIAJCgICAgBA3AsQDQQAhBEEBIQUCQCADRQ0AIAJBwAFqQQJyIQYDQCABLAAAIgRBzgBzIQcCQAJAIARBf0oNACACQQA2AsABIAIgB0G/AXE6AMEBIAIgB0HAAXFBBnZBwAFyOgDAASACQcQDaiACQcABaiAGQeyDwAAQvgEMAQsCQCACKALMAyIEIAIoAsQDRw0AIAJBxANqQfyDwAAQfgsgAigCyAMgBGogBzoAACACIARBAWo2AswDCyABQQFqIQEgA0F/aiIDDQALIAIoAswDIQQgAigCyAMhBQsgAkHAAWogBCAEQQNuIgNBA2xrIgFBA3NBACABGyAEakEDbkECdEEAQQFBARCAASACKALEASEHAkACQAJAAkACQAJAAkAgAigCwAFBAUYNACACIAIoAsgBNgKgASACIAc2ApwBIAJBADYCpAECQCADIAFBAEdqIghFDQAgAkHAAWpBAnIhCUEAIQEDQAJAAkACQCABIARPDQAgBSABai0AAEEQdCEKIAFBAWoiByAESQ0BDAILIAEgBEHohMAAEJ8BAAsgBSAHai0AAEEIdCAKciEKCwJAIAFBAmoiByAETw0AIAogBSAHai0AAHIhCgsgCEF/aiEIIAFBA2ohC0ESIQcDQAJAAkAgASAETQ0AAkAgAigCpAEiAyACKAKcAUcNACACQZwBakH8g8AAEH4LIAIoAqABIANqQT06AAAgAiADQQFqNgKkAQwBCwJAIAogB0EecXZBP3FB+ITAAGosAAAiA0F/Sg0AIAJBADYCwAEgAiADQb8BcToAwQEgAiADQcABcUEGdkHAAXI6AMABIAJBnAFqIAJBwAFqIAlB7IPAABC+AQwBCwJAIAIoAqQBIgYgAigCnAFHDQAgAkGcAWpB/IPAABB+CyACKAKgASAGaiADOgAAIAIgBkEBajYCpAELIAFBAWohASAHQXpqIgdBekcNAAsgCyEBIAgNAAsLIAJB2ABqQQhqIgEgAkGcAWpBCGooAgA2AgAgAiACKQKcATcDWCACQcQDahClAyACQcQDahD/AiACQbQBahClAyACQbQBahD/AiACQYEBNgKAASACQYyHwABBIBDBAjYCnAEgAiACKAJcIAEoAgAQwQI2AsABIAJB0ABqIABBDGogAkGAAWogAkGcAWogAkHAAWoQvAEgAigCVCEBAkACQAJAIAIoAlAiBw0AAkACQCABEJEDQQFGDQAgAkGAgICAeDYCZAwBCyACQcgAaiABEIoDAkAgAigCSCIHRQ0AIAJBwABqIAcgAigCTBDWASACQeQAaiACKAJAIAIoAkQQvgIMAQsgAkGAgICAeDYCZAsgAUGDAUsNAQwCCyACQYCAgIB4NgJkIAdBAXFFDQEgAUGEAUkNAQsgARDpAQsCQCACKALAASIBQYQBSQ0AIAEQ6QELAkAgAigCnAEiAUGEAUkNACABEOkBCwJAIAIoAoABIgFBhAFJDQAgARDpAQtBACEBIAIoAmRBgICAgHhGDQYgAkHwAGpBCGogAkHkAGpBCGooAgAiBzYCACACIAIpAmQ3A3AgAigCdCEDQQAhASACQcABakEAQYACEE0aA0AgAkHAAWogAUH4hMAAai0AAGogAToAACABQQFqIgFBwABHDQALIAJBOGogAyAHQT0QcSACKAI8IgNBAnENBCACKAI4IQhBACELIAJBnAFqIANBA2xBAnZBAEEBQQEQgAEgAigCoAEhAQJAIAIoApwBQQFGDQAgAkEANgLMAyACIAIoAqQBIgc2AsgDIAIgATYCxAMgA0ECdiADQQFxaiIFRQ0DQQAhC0EAIQwDQCAFQX9qIQUgDCIGQQRqIQxBACEKQQAhCUEAIQEDQAJAIAYgAWogA08NACAIIAFqLAAAIgdBf0wNByACQcABaiAHai0AACEEAkAgB0HBAEYNACAERQ0ICyAKQQFqIQogCUEGdCAEciEJCyABQQFqIgFBBEcNAAsCQCAKQQFMDQACQCALIAIoAsQDRw0AIAJBxANqQciFwAAQfgsgAigCyAMgC2ogCUEQdjoAACACIAtBAWoiATYCzAMCQCAKQQJHDQAgASELDAELAkAgASACKALEA0cNACACQcQDakHYhcAAEH4LIAIoAsgDIAFqIAlBCHY6AAAgAiALQQJqIgE2AswDAkAgCkEETw0AIAEhCwwBCwJAIAEgAigCxANHDQAgAkHEA2pB6IXAABB+CyACKALIAyABaiAJOgAAIAIgC0EDaiILNgLMAwsgCEEEaiEIIAVFDQMMAAsLIAEgAigCpAFBuIXAABC1AgALIAcgAigCyAFB3IPAABC1AgALIAIoAsgDIQcgAigCxAMhAQsgAkGAAWogByALEEACQAJAAkAgAigCgAENACACIAs2AqgBIAIgBzYCpAEgASEHDAELIAIgAikChAE3AqgBIAIgCzYCpAEgAiAHNgKgASACIAE2ApwBIAFBgICAgHhHDQELIAdBgICAgHhGDQIgAikCpAEhDSACIAc2ArQBIAIgDTcCuAEgAkEANgKIASACQoCAgIAQNwKAAUEAIQFBASEHAkAgDUKAgICAEFQNACANQiCIpyEDIA2nIQEgAkHAAWpBAnIhBgNAIAEsAAAiBEHOAHMhBwJAAkAgBEF/Sg0AIAJBADYCwAEgAiAHQb8BcToAwQEgAiAHQcABcUEGdkHAAXI6AMABIAJBgAFqIAJBwAFqIAZB7IPAABC+AQwBCwJAIAIoAogBIgQgAigCgAFHDQAgAkGAAWpB/IPAABB+CyACKAKEASAEaiAHOgAAIAIgBEEBajYCiAELIAFBAWohASADQX9qIgMNAAsgAigCiAEhASACKAKEASEHCyACQQE7AeQBIAIgATYC4AEgAkEANgLcASACQQE6ANgBIAJBOjYC1AEgAiABNgLQASACQQA2AswBIAIgATYCyAEgAiAHNgLEASACQTo2AsABIAJBnAFqIAJBwAFqQeiBwAAQcgJAIAIoAqQBQQNHDQAgAigCoAEiASgCECABKAIUQbmGwABBDhCXAkUNAAJAAkACQAJAIAIoAqQBRQ0AAkAgAigCoAEiBygCACAHKAIEQbiGwABBARCXAiIBDQAgBygCACAHKAIEQbeGwABBARCXAkUNBQsgAigCpAEiB0EBTQ0BIAIoAqABIgMoAgghBwJAAkACQCADKAIMIgQOAgcAAQtBASEDIActAABBVWoOAwYBBgELAkAgBy0AAEErRw0AIARBf2ohAyAHQQFqIQcgBEEiSQ0BDAQLIAQhAyAEQSFPDQMLIAJBEGohBkIAIQ1CACEOA0AgBy0AAEFQaiIEQQlLDQUgAkEIaiANIA5CCkIAEJIBIAdBAWohByAGKQMAIAIpAwgiDiAErXwiDSAOVK18IQ4gA0F/aiIDDQAMBAsLQQBBAEHshsAAEJ8BAAtBASAHQfyGwAAQnwEACyACQTBqIQZCACENQgAhDgNAIANFDQEgAkEYaiAOQgBCCkIAEJIBIAJBKGogDUIAQgpCABCSASAHLQAAQVBqIgRBCUsNAiACKQMgQgBSIAYpAwAiDSACKQMYfCIPIA1Ucg0CIAdBAWohByADQX9qIQMgAikDKCIQIAStfCINIBBUIgQgDyAErXwiDiAPVCANIBBaG0UNAAwCCwsgAkHAAWogABBpIAIpA8ABIhAgAikDyAEiD4RQDQAgECANVCIHIA8gDlQgDyAOURsNACAQIA19QoCfyQBWIA8gDn0gB619Ig1CAFIgDVAbDQAgAkGcAWpBBEEIEJUBIAJBgAFqEKUDIAJBgAFqEP8CIAJBtAFqEKUDIAJBtAFqEP8CDAQLIAJBnAFqQQRBCBCVASACQYABahClAyACQYABahD/AiACQbQBahClAyACQbQBahD/AgwCCyACQZwBahClAyACQZwBahD/AgwBCyACQcQDahClAyACQcQDahD/AgtBACEBCyACQfAAahClAyACQfAAahD/AgsgAkHYAGoQpQMgAkHYAGoQ/wIgAkHQA2okACABC5MZAQ1/IwBBwAJrIgMkAAJAAkACQAJAAkACQAJAAkACQAJAAkACQAJAAkACQAJAAkACQAJAAkACQAJAAkACQAJAAkACQAJAAkACQAJAAkACQAJAAkACQAJAAkACQAJAAkACQCABKAIAIgEtABAOBQAWAwECAAsgASABKAIAIgQ2AgQgA0GQAWpB0I7AACAEEFUgASADKQOQATcCCCABKAIEIQQgAyABQQhqNgKEAiADIAQ2AoACIAEgA0GAAmpB1I7AABCNAxBWNgIUCyADQYgBaiABQRRqIgUgAhCCAUECIQQgAygCiAEiBkECRg0VIAMoAowBIQcgBSgCACIEIAQoAgBBf2oiBDYCAAJAIAQNACAFEJQBC0EBIQQgBg0kAkAgB0GEAUkNACAHEOkBCyABQQA6AGEgASABKAIEIgQ2AlwgASABKAIMIgc2AhQgAUHhAGohBgwDCyABQRRqIQUgAUHhAGohBiABLQBhDgcBBAADCQcNAQsACyABKAIUIQcgASgCXCEECyABIAc2AhwgASAENgIYQQAhBCABQQA6AGACQEHQjsAAEOEBDQAMHwtB0I7AABD0ASADQQI2AoQCIANBtI/AADYCgAIgA0ICNwKMAiADQQM2AuABIAMgAUEcajYC3AEgA0EBNgLYASADQaiPwAA2AtQBIAMgA0HUAWo2AogCIANBnAFqIANBgAJqEFIgASADKQKcATcCICABQShqIANBpAFqKAIAIgQ2AgAgA0GAAWogASgCGCABKAIkIAQQQkEBIQQgAygChAEhByADKAKAAQ0dIAEgBxBWNgJkCyADQfgAaiABQeQAaiIIIAIQggFBAiEEIAMoAngiCUECRg0NIAMoAnwhByAIKAIAIgQgBCgCAEF/aiIENgIAAkAgBA0AIAgQlAELQQEhBCAJDRogASAHNgIsIAFBAToAYCADQfAAaiAHEIoDAkAgAygCcCIERQ0AIANB6ABqIAQgAygCdBDWASADQdQBaiADKAJoIAMoAmwQvgIgAygC1AFBgICAgHhHDQILIAFBADoAYCABIAEoAiwiBDYCMAwCC0HEj8AAEPMBAAsgA0GAAmpBCGogA0HUAWpBCGooAgAiBDYCACADIAMpAtQBNwOAAiABIAMoAoQCIAQQwQI2AjAgA0GAAmoQpQMgA0GAAmoQ/wIgASgCMCEECyAEEJIDQQFGDQEgAUEwaiIEKAIAEJMDQQFGDQEgA0GoAWogBBC5AQJAIAMoAqgBQYCAgIB4Rw0AQQEhCUHUj8AAQSQQwQIhBwwXCyABIAMpAqgBNwI0IAFBPGogA0GwAWooAgAiBDYCACADQeAAaiABKAI4IAQQUCADKAJgRQ0UIANB2ABqIAFBNGoiBCADKAJkIgcQtgFBASEJIAMoAlwhCiADKAJYIQsgA0HQAGogBCAHQQFqEK4BIAMoAlQhBCADKAJQIQcgA0HkAWogASgCGCALIAoQNCADKALkAUGAgICAeEcNDiADLQDoASEJIANBtAFqIAcgBBCmASABQcgAaiADQbQBakEIaigCADYCACABIAMpArQBNwJAAkAgCQ0AQdCOwAAQ9QEMFAsgASABKAJEIAEoAkgQwQI2AkwgA0ECNgKEAiADQbSPwAA2AoACIANCAjcCjAIgA0EDNgLgASADIAFBHGo2AtwBIANBATYC2AEgA0H4kMAANgLUASADIANB1AFqNgKIAiADQcgBaiADQYACahCFASABIAMpAsgBNwJQIAFB2ABqIANByAFqQQhqKAIAIgQ2AgAgA0HIAGogASgCGCABKAJUIAQgAUHMAGoQPyADKAJMIQcgAygCSA0RIAggBxBWNgIACyADQcAAaiABQeQAaiIJIAIQggFBAiEEIAMoAkAiCEECRg0CIAMoAkQhByAJKAIAIgQgBCgCAEF/aiIENgIAAkAgBA0AIAkQlAELIAgNECADQThqIAcQigMCQCADKAI4IgRFDQAgA0EwaiAEIAMoAjwQ1gEgA0HUAWogAygCMCADKAI0EL4CIAMoAtQBQYCAgIB4Rw0ECyAJIAc2AgAMBAsCQEHQjsAAENcBDQBBACEJDBULQQAtAIHVQBogASgCGCEHQewAQQQQ4gIiBEUNDSAEQQA6ABAgBCAHNgIAIAggBDYCAAsgA0EIaiABQeQAaiIIIAIQMkECIQQCQCADKAIIIglBAkYNACADKAIMIQcgCCgCACIEEN4BIARB7ABBBBDuAgwUCyAGQQQ6AAAMCAsgBkEFOgAADAcLIANBgAJqQQhqIANB1AFqQQhqKAIAIgQ2AgAgAyADKQLUATcDgAIgCSADKAKEAiAEEMECNgIAIANBgAJqEKUDIANBgAJqEP8CIAdBhAFJDQAgBxDpAQsgA0EoaiAJKAIAEIoDAkAgAygCKCIERQ0AIANBIGogBCADKAIsENYBIANB5AFqIAMoAiAgAygCJBC+AiADKALkAUGAgICAeEYNACADQfABakEIaiADQeQBakEIaigCACIENgIAIAMgAykC5AE3A/ABIAMoAvQBIQcCQAJAIARBB0sNAEGkkcAAQQcgByAEEJcCIQQMAQsgA0GAAmogByAEQaSRwABBBxAzAkACQCADKAKAAg0AQQAhDAJAIAMtAI4CDQAgAy0AjAIhCiADKAK0AiELIAMoArACIQ0gAygChAIhBAJAA0ACQCAERQ0AAkAgBCALSQ0AIAQgC0YNAQwTCyANIARqLAAAQb9/TA0SCwJAIAQgC0YNAAJAAkAgDSAEaiIILAAAIgdBf0wNACAHQf8BcSEHDAELIAgtAAFBP3EhDiAHQR9xIQ8CQCAHQV9LDQAgD0EGdCAOciEHDAELIA5BBnQgCC0AAkE/cXIhDgJAIAdBcE8NACAOIA9BDHRyIQcMAQsgDkEGdCAILQADQT9xciAPQRJ0QYCA8ABxciEHCyAKQQFxDQJBASEKQQEhCAJAIAdBgAFJDQBBAiEIIAdBgBBJDQBBA0EEIAdBgIAESRshCAsgCCAEaiEEDAELCyAKQQFxRQ0BC0EBIQwLIAMgDDYC1AEMAQsgA0GIAmohBCADKAK8AiEHIAMoArgCIQggAygCtAIhCiADKAKwAiELAkAgAygCpAJBf0YNACADQdQBaiAEIAsgCiAIIAdBABBJDAELIANB1AFqIAQgCyAKIAggB0EBEEkLIAMoAtQBIQQLIANB8AFqEKUDIANB8AFqEP8CIAQNAgsgA0EYakHQjsAAEMQBIAMtABhFDQIgASgCGCEHEIgCIgRBADoAECAEIAc2AgAgASAENgJoCyADQRBqIAFB6ABqIAIQMkECIQQgAygCECIJQQJGDQIgAygCFCEHIAEoAmgiBBDeASAEQewAQQQQ7gIgASgCZCIEQYQBSQ0LIAQQ6QEMCwtB0I7AACABQcwAahCYASABKAIYQQEQMRoLAkAgCSgCACIEQYQBSQ0AIAQQ6QELIAFB0ABqIgQQpQMgBBD/AiABKAJMIgRBhAFJDQogBBDpAQwKCyAGQQY6AAAMAQsgBkEDOgAACwwPC0H0jsAAEPMBAAsgAUEDOgAQDBALIANB8AFqQQhqIANB5AFqQQhqKAIANgIAIAMgAykC5AE3A/ABIANBATYChAIgA0GckcAANgKAAiADQgE3AowCIANBDzYCxAEgAyADQcABajYCiAIgAyADQfABajYCwAEgA0HUAWogA0GAAmoQhQEgAygC2AEgAygC3AEQwQIhByADQdQBahClAyADQdQBahD/AiADQfABahClAyADQfABahD/AgwGC0EEQewAEIYDAAsgDSALIAQgC0GwjsAAENkCAAtBASEJCyABQdAAaiIEEKUDIAQQ/wICQCABKAJMIgRBhAFJDQAgBBDpAQsgAUHAAGoiBBClAyAEEP8CDAILIAFBwABqIgQQpQMgBBD/AiABQTRqIgQQpQMgBBD/AgJAIAEoAjAiBEGEAUkNACAEEOkBCyABQeAAaiECQQAhBAJAIAEtAGBFDQAgASgCLCIHQYQBSQ0AIAcQ6QEMBQsMBAtBASEJQfiPwABBKhDBAiEHCyABQTRqIgQQpQMgBBD/AgsCQCABKAIwIgRBhAFJDQAgBBDpAQsCQCABLQBgRQ0AIAEoAiwiBEGEAUkNACAEEOkBCyAJIQQLIAFB4ABqIQILIAJBADoAAAsgAUEgaiICEKUDIAIQ/wILIAZBAToAAAsgBEECRg0BIAUQZgsgAUEBOgAQDAILIAFBBDoAEEECIQQLCyAAIAQ2AgAgACAHNgIEIANBwAJqJAALgQwCCn8BfgJAIAQNACAAQQA2AjwgACADNgI4IAAgAjYCNCAAIAE2AjAgAEEAOgAOIABBgQI7AQwgACACNgIIIABCADcDAA8LQQEhBUEAIQYCQAJAAkACQAJAAkACQAJAAkACQAJAIARBAUcNAEEBIQdBACEIDAELQQEhCUEAIQpBASELQQAhBkEBIQUDQCALIQwgBiAKaiILIARPDQICQAJAIAMgCWotAABB/wFxIgkgAyALai0AACILTw0AIAwgBmpBAWoiCyAKayEFQQAhBgwBCwJAIAkgC0YNAEEBIQUgDEEBaiELQQAhBiAMIQoMAQtBACAGQQFqIgsgCyAFRiIJGyEGIAtBACAJGyAMaiELCyALIAZqIgkgBEkNAAtBASEJQQAhCEEBIQtBACEGQQEhBwNAIAshDCAGIAhqIgsgBE8NAwJAAkAgAyAJai0AAEH/AXEiCSADIAtqLQAAIgtNDQAgDCAGakEBaiILIAhrIQdBACEGDAELAkAgCSALRg0AQQEhByAMQQFqIQtBACEGIAwhCAwBC0EAIAZBAWoiCyALIAdGIgkbIQYgC0EAIAkbIAxqIQsLIAsgBmoiCSAESQ0ACyAKIQYLIAQgBiAIIAYgCEsiCxsiDUkNAiAFIAcgCxsiCyANaiIGIAtJDQMgBiAESw0EAkACQCADIAMgC2ogDRDCAUUNACAEIA1rIgggDUshCiAEQQNxIQwCQAJAIARBf2pBA08NAEEAIQtCACEPDAELIARBfHEhCUEAIQtCACEPA0BCASADIAtqIgZBA2oxAACGQgEgBkECajEAAIZCASAGQQFqMQAAhkIBIAYxAACGIA+EhISEIQ8gCSALQQRqIgtHDQALCyAIIA0gChshCQJAIAxFDQAgAyALaiEGA0BCASAGMQAAhiAPhCEPIAZBAWohBiAMQX9qIgwNAAsLIAlBAWohC0F/IQogDSEFQX8hBgwBC0EBIQhBACEGQQEhCUEAIQUCQANAIAkiDCAGaiIHIARPDQEgBCAGayAMQX9zaiIJIARPDQggBkF/cyAEaiAFayIKIARPDQkCQAJAIAMgCWotAABB/wFxIgkgAyAKai0AACIKTw0AIAdBAWoiCSAFayEIQQAhBgwBCwJAIAkgCkYNACAMQQFqIQlBACEGQQEhCCAMIQUMAQtBACAGQQFqIgkgCSAIRiIKGyEGIAlBACAKGyAMaiEJCyAIIAtHDQALC0EBIQhBACEGQQEhCUEAIQcCQANAIAkiDCAGaiIOIARPDQEgBCAGayAMQX9zaiIJIARPDQogBkF/cyAEaiAHayIKIARPDQsCQAJAIAMgCWotAABB/wFxIgkgAyAKai0AACIKTQ0AIA5BAWoiCSAHayEIQQAhBgwBCwJAIAkgCkYNACAMQQFqIQlBACEGQQEhCCAMIQcMAQtBACAGQQFqIgkgCSAIRiIKGyEGIAlBACAKGyAMaiEJCyAIIAtHDQALCyAEIAcgBSAHIAVLG2shBQJAAkAgCw0AQgAhD0EAIQtBACEKDAELIAtBA3EhCUEAIQoCQAJAIAtBBE8NAEIAIQ9BACEMDAELIAtBfHEhCEEAIQxCACEPA0BCASADIAxqIgZBA2oxAACGQgEgBkECajEAAIZCASAGQQFqMQAAhkIBIAYxAACGIA+EhISEIQ8gCCAMQQRqIgxHDQALCyAJRQ0AIAMgDGohBgNAQgEgBjEAAIYgD4QhDyAGQQFqIQYgCUF/aiIJDQALCyAEIQYLIAAgBDYCPCAAIAM2AjggACACNgI0IAAgATYCMCAAIAY2AiggACAKNgIkIAAgAjYCICAAQQA2AhwgACALNgIYIAAgBTYCFCAAIA02AhAgACAPNwMIIABBATYCAA8LIAsgBEHAvMAAEJ8BAAsgCyAEQcC8wAAQnwEACyANIARBoLzAABD0AgALIAsgBkGwvMAAEPUCAAsgBiAEQbC8wAAQ9AIACyAJIARB0LzAABCfAQALIAogBEHgvMAAEJ8BAAsgCSAEQdC8wAAQnwEACyAKIARB4LzAABCfAQALngsCBX8BfiMAQRBrIgQkAAJAAkACQAJAAkACQAJAAkACQAJAAkACQAJAAkACQAJAIANBAkkNAAJAAkAgAiwAACIFQX9MDQAgBUH/AXEhBgwBCyACLQABQT9xIQcgBUEfcSEGAkAgBUFfSw0AIAZBBnQgB3IhBgwBCyAHQQZ0IAItAAJBP3FyIQcCQCAFQXBPDQAgByAGQQx0ciEGDAELIAdBBnQgAi0AA0E/cXIgBkESdEGAgPAAcXIiBkGAgMQARg0FCwJAIAZBxgBGDQAgBkHUAEcNBAsgAiwAASIHQb9/TA0GIAJBAWohBSAHQf8BcSEIIANBf2oiB0EBRw0BQQEhByAIQVVqDgMJAgkCCyAEQQRqQRJBAEEBQQEQgAEgBCgCCCEDIAQoAgRBAUYNBCAEKAIMIgVBEGpBAC8A6YtAOwAAIAVBCGpBACkA4YtANwAAIAVBACkA2YtANwAAIABBEjYCCCAAIAU2AgQgACADNgIADAoLAkACQAJAIAhBVWoOAwACAQILIANBfmohByACQQJqIQUgA0EKSQ0CDAgLIANBfmohByACQQJqIQUCQCADQQpJDQBBACEDA0AgB0UNCyAFLQAAQVBqIgJBCUsNCiADrEIKfiIJQiCIpyAJpyIIQR91Rw0KIAVBAWohBSAHQX9qIQcgAkEASiAIIAJrIgMgCEhzRQ0ADAoLC0EAIQMDQCAFLQAAQVBqIgJBCUsNCSAFQQFqIQUgA0EKbCACayEDIAdBf2oiBw0ADAoLCyADQQhLDQYLQQAhAwNAIAUtAABBUGoiAkEJSw0HIAVBAWohBSACIANBCmxqIQMgB0F/aiIHRQ0IDAALCyAEQQRqQRpBAEEBQQEQgAEgBCgCCCEDIAQoAgRBAUYNAyAEKAIMIgVBGGpBAC8A14tAOwAAIAVBEGpBACkAz4tANwAAIAVBCGpBACkAx4tANwAAIAVBACkAv4tANwAAIABBGjYCCCAAIAU2AgQgACADNgIADAcLQZyKwAAQ9wIACyADIAQoAgxBgIPAABC1AgALIAIgA0EBIANBrIrAABDZAgALIAMgBCgCDEGAg8AAELUCAAtBACEDA0AgB0UNAiAFLQAAQVBqIgJBCUsNASADrEIKfiIJQiCIpyAJpyIIQR91Rw0BIAVBAWohBSAHQX9qIQcgAkEASCAIIAJqIgMgCEhzRQ0ACwsgBEEEakEYQQBBAUEBEIABIAQoAgghAyAEKAIEQQFGDQIgBCgCDCIFQRBqQQApALeLQDcAACAFQQhqQQApAK+LQDcAACAFQQApAKeLQDcAACAAQRg2AgggACAFNgIEIAAgAzYCAAwBCwJAAkACQCAGQcYARg0AAkAgBkHUAEcNACADQQFxDQIgA0F+akHjAE8NAiAAQYCAgIB4NgIAIABBAToABAwECyAEQQRqQRJBAEEBQQEQgAEgBCgCCCEDIAQoAgRBAUYNBSAEKAIMIgVBEGpBAC8ApYtAOwAAIAVBCGpBACkAnYtANwAAIAVBACkAlYtANwAAIABBEjYCCCAAIAU2AgQgACADNgIADAMLIANBgYCAgHhxQQFHDQEgA0F/akHjAE8NASAAQYCAgIB4NgIAIABBADoABAwCCyAEQQRqQS1BAEEBQQEQgAEgBCgCCCEFIAQoAgRBAUYNBCAEKAIMQbyKwABBLRA+IQMgAEEtNgIIIAAgAzYCBCAAIAU2AgAMAQsgBEEEakEsQQBBAUEBEIABIAQoAgghBSAEKAIEQQFGDQQgBCgCDEHpisAAQSwQPiEDIABBLDYCCCAAIAM2AgQgACAFNgIACyAEQRBqJAAPCyADIAQoAgxBgIPAABC1AgALIAMgBCgCDEGAg8AAELUCAAsgBSAEKAIMQYCDwAAQtQIACyAFIAQoAgxBgIPAABC1AgAL6gkBCH8CQAJAAkAgACABayACTw0AIAEgAmohAyAAIAJqIQQgAkEQSQ0BQQAgBEEDcSIFayEGAkAgBEF8cSIHIARPDQAgBUF/aiEIAkACQCAFDQAgAyEJDAELIAUhCiADIQkDQCAEQX9qIgQgCUF/aiIJLQAAOgAAIApBf2oiCg0ACwsgCEEDSQ0AIAlBfGohCQNAIARBf2ogCUEDai0AADoAACAEQX5qIAlBAmotAAA6AAAgBEF9aiAJQQFqLQAAOgAAIARBfGoiBCAJLQAAOgAAIAlBfGohCSAHIARJDQALCyAHIAIgBWsiCUF8cSICayEEQQAgAmshCgJAAkAgAyAGaiIDQQNxDQAgBCAHTw0BIAkgAWpBfGohAQNAIAdBfGoiByABKAIANgIAIAFBfGohASAEIAdJDQAMAgsLIAQgB08NACADQQN0IgJBGHEhBSADQXxxIghBfGohAUEAIAJrQRhxIQYgCCgCACECA0AgB0F8aiIHIAIgBnQgASgCACICIAV2cjYCACABQXxqIQEgBCAHSQ0ACwsgCUEDcSECIAMgCmohAwwBCwJAAkAgAkEQTw0AIAAhBAwBCwJAIABBACAAa0EDcSIKaiIJIABNDQAgCkF/aiEFIAAhBCABIQcCQCAKRQ0AIAohAyAAIQQgASEHA0AgBCAHLQAAOgAAIAdBAWohByAEQQFqIQQgA0F/aiIDDQALCyAFQQdJDQADQCAEIActAAA6AAAgBEEBaiAHQQFqLQAAOgAAIARBAmogB0ECai0AADoAACAEQQNqIAdBA2otAAA6AAAgBEEEaiAHQQRqLQAAOgAAIARBBWogB0EFai0AADoAACAEQQZqIAdBBmotAAA6AAAgBEEHaiAHQQdqLQAAOgAAIAdBCGohByAEQQhqIgQgCUcNAAsLIAkgAiAKayIDQXxxIgVqIQQCQAJAIAEgCmoiB0EDcQ0AIAkgBE8NASAHIQEDQCAJIAEoAgA2AgAgAUEEaiEBIAlBBGoiCSAESQ0ADAILCyAJIARPDQAgB0EDdCICQRhxIQogB0F8cSIIQQRqIQFBACACa0EYcSEGIAgoAgAhAgNAIAkgAiAKdiABKAIAIgIgBnRyNgIAIAFBBGohASAJQQRqIgkgBEkNAAsLIANBA3EhAiAHIAVqIQELIAQgBCACaiIJTw0BIAJBf2ohAwJAIAJBB3EiB0UNAANAIAQgAS0AADoAACABQQFqIQEgBEEBaiEEIAdBf2oiBw0ACwsgA0EHSQ0BA0AgBCABLQAAOgAAIARBAWogAUEBai0AADoAACAEQQJqIAFBAmotAAA6AAAgBEEDaiABQQNqLQAAOgAAIARBBGogAUEEai0AADoAACAEQQVqIAFBBWotAAA6AAAgBEEGaiABQQZqLQAAOgAAIARBB2ogAUEHai0AADoAACABQQhqIQEgBEEIaiIEIAlHDQAMAgsLIAQgAmsiByAETw0AIAJBf2ohCQJAIAJBA3EiAUUNAANAIARBf2oiBCADQX9qIgMtAAA6AAAgAUF/aiIBDQALCyAJQQNJDQAgA0F8aiEBA0AgBEF/aiABQQNqLQAAOgAAIARBfmogAUECai0AADoAACAEQX1qIAFBAWotAAA6AAAgBEF8aiIEIAEtAAA6AAAgAUF8aiEBIAcgBEkNAAsLIAALnQkCBX8BfiMAQfAAayIFJAAgBSADNgIMIAUgAjYCCAJAAkACQAJAAkACQAJAAkACQCABQYECSQ0AAkAgACwAgAJBv39MDQBBAyEGDAMLIAAsAP8BQb9/TA0BQQIhBgwCCyAFIAE2AhQgBSAANgIQQQAhBkEBIQcMAgsgACwA/gFBv39KIQYLIAAgBkH9AWoiBmosAABBv39MDQEgBSAGNgIUIAUgADYCEEEFIQZB8LzAACEHCyAFIAY2AhwgBSAHNgIYAkAgAiABSyIGDQAgAyABSw0AIAIgA0sNAgJAIAJFDQAgAiABTw0AIAVBDGogBUEIaiAAIAJqLAAAQb9/ShsoAgAhAwsgBSADNgIgIAEhAgJAIAMgAU8NACADQQFqIgdBACADQX1qIgIgAiADSxsiAkkNBAJAIAcgAkYNACAHIAJrIQgCQCAAIANqLAAAQb9/TA0AIAhBf2ohBgwBCyACIANGDQACQCAAIAdqIgdBfmoiAywAAEG/f0wNACAIQX5qIQYMAQsgACACaiIJIANGDQACQCAHQX1qIgMsAABBv39MDQAgCEF9aiEGDAELIAkgA0YNAAJAIAdBfGoiAywAAEG/f0wNACAIQXxqIQYMAQsgCSADRg0AIAhBe2ohBgsgBiACaiECCwJAIAJFDQACQCACIAFJDQAgAiABRg0BDAcLIAAgAmosAABBv39MDQYLIAIgAUYNBAJAAkACQAJAIAAgAmoiAywAACIBQX9KDQAgAy0AAUE/cSEAIAFBH3EhBiABQV9LDQEgBkEGdCAAciEBDAILIAUgAUH/AXE2AiRBASEBDAILIABBBnQgAy0AAkE/cXIhAAJAIAFBcE8NACAAIAZBDHRyIQEMAQsgAEEGdCADLQADQT9xciAGQRJ0QYCA8ABxciIBQYCAxABGDQYLIAUgATYCJAJAIAFBgAFPDQBBASEBDAELAkAgAUGAEE8NAEECIQEMAQtBA0EEIAFBgIAESRshAQsgBSACNgIoIAUgASACajYCLCAFQQU2AjQgBUH4vcAANgIwIAVCBTcCPCAFQdoArUIghiIKIAVBGGqthDcDaCAFIAogBUEQaq2ENwNgIAVB2wCtQiCGIAVBKGqthDcDWCAFQdwArUIghiAFQSRqrYQ3A1AgBUEDrUIghiAFQSBqrYQ3A0ggBSAFQcgAajYCOCAFQTBqIAQQgQIACyAFIAIgAyAGGzYCKCAFQQM2AjQgBUG4vsAANgIwIAVCAzcCPCAFQdoArUIghiIKIAVBGGqthDcDWCAFIAogBUEQaq2ENwNQIAVBA61CIIYgBUEoaq2ENwNIIAUgBUHIAGo2AjggBUEwaiAEEIECAAsgACABQQAgBiAEENkCAAsgBUEENgI0IAVBmL3AADYCMCAFQgQ3AjwgBUHaAK1CIIYiCiAFQRhqrYQ3A2AgBSAKIAVBEGqthDcDWCAFQQOtQiCGIgogBUEMaq2ENwNQIAUgCiAFQQhqrYQ3A0ggBSAFQcgAajYCOCAFQTBqIAQQgQIACyACIAdB0L7AABD1AgALIAQQ9wIACyAAIAEgAiABIAQQ2QIAC+kGAQh/AkACQCABIABBA2pBfHEiAiAAayIDSQ0AIAEgA2siBEEESQ0AIARBA3EhBUEAIQZBACEBAkAgAiAARiIHDQBBACEBAkACQCAAIAJrIghBfE0NAEEAIQkMAQtBACEJA0AgASAAIAlqIgIsAABBv39KaiACQQFqLAAAQb9/SmogAkECaiwAAEG/f0pqIAJBA2osAABBv39KaiEBIAlBBGoiCQ0ACwsgBw0AIAAgCWohAgNAIAEgAiwAAEG/f0pqIQEgAkEBaiECIAhBAWoiCA0ACwsgACADaiEAAkAgBUUNACAAIARBfHFqIgIsAABBv39KIQYgBUEBRg0AIAYgAiwAAUG/f0pqIQYgBUECRg0AIAYgAiwAAkG/f0pqIQYLIARBAnYhCCAGIAFqIQMDQCAAIQQgCEUNAiAIQcABIAhBwAFJGyIGQQNxIQcgBkECdCEFQQAhAgJAIAhBBEkNACAEIAVB8AdxaiEJQQAhAiAEIQEDQCABKAIMIgBBf3NBB3YgAEEGdnJBgYKECHEgASgCCCIAQX9zQQd2IABBBnZyQYGChAhxIAEoAgQiAEF/c0EHdiAAQQZ2ckGBgoQIcSABKAIAIgBBf3NBB3YgAEEGdnJBgYKECHEgAmpqamohAiABQRBqIgEgCUcNAAsLIAggBmshCCAEIAVqIQAgAkEIdkH/gfwHcSACQf+B/AdxakGBgARsQRB2IANqIQMgB0UNAAsgBCAGQfwBcUECdGoiAigCACIBQX9zQQd2IAFBBnZyQYGChAhxIQECQCAHQQFGDQAgAigCBCIAQX9zQQd2IABBBnZyQYGChAhxIAFqIQEgB0ECRg0AIAIoAggiAkF/c0EHdiACQQZ2ckGBgoQIcSABaiEBCyABQQh2Qf+BHHEgAUH/gfwHcWpBgYAEbEEQdiADag8LAkAgAQ0AQQAPCyABQQNxIQkCQAJAIAFBBE8NAEEAIQNBACECDAELIAFBfHEhCEEAIQNBACECA0AgAyAAIAJqIgEsAABBv39KaiABQQFqLAAAQb9/SmogAUECaiwAAEG/f0pqIAFBA2osAABBv39KaiEDIAggAkEEaiICRw0ACwsgCUUNACAAIAJqIQEDQCADIAEsAABBv39KaiEDIAFBAWohASAJQX9qIgkNAAsLIAML5gYBBn8CQAJAAkACQAJAIABBfGoiBCgCACIFQXhxIgZBBEEIIAVBA3EiBxsgAWpJDQAgAUEnaiEIAkAgB0UNACAGIAhLDQILAkACQAJAIAJBCUkNACACIAMQUSICDQFBAA8LQQAhAiADQcz/e0sNAUEQIANBC2pBeHEgA0ELSRshAQJAAkAgBw0AIAFBgAJJDQEgBiABQQRySQ0BIAYgAWtBgYAITw0BIAAPCyAAQXhqIgggBmohBwJAAkACQAJAAkAgBiABTw0AIAdBACgC0NhARg0EIAdBACgCzNhARg0CIAcoAgQiBUECcQ0FIAVBeHEiCSAGaiIFIAFJDQUgByAJEFkgBSABayIDQRBJDQEgBCABIAQoAgBBAXFyQQJyNgIAIAggAWoiASADQQNyNgIEIAggBWoiAiACKAIEQQFyNgIEIAEgAxBLIAAPCyAGIAFrIgNBD0sNAiAADwsgBCAFIAQoAgBBAXFyQQJyNgIAIAggBWoiASABKAIEQQFyNgIEIAAPC0EAKALE2EAgBmoiByABSQ0CAkACQCAHIAFrIgNBD0sNACAEIAVBAXEgB3JBAnI2AgAgCCAHaiIBIAEoAgRBAXI2AgRBACEDQQAhAQwBCyAEIAEgBUEBcXJBAnI2AgAgCCABaiIBIANBAXI2AgQgCCAHaiICIAM2AgAgAiACKAIEQX5xNgIEC0EAIAE2AszYQEEAIAM2AsTYQCAADwsgBCABIAVBAXFyQQJyNgIAIAggAWoiASADQQNyNgIEIAcgBygCBEEBcjYCBCABIAMQSyAADwtBACgCyNhAIAZqIgcgAUsNBwsgAxAvIgFFDQEgASAAQXxBeCAEKAIAIgJBA3EbIAJBeHFqIgIgAyACIANJGxA+IQEgABBBIAEPCyACIAAgAyABIAMgAUkbED4aIAQoAgAiA0F4cSIHQQRBCCADQQNxIgMbIAFqSQ0DAkAgA0UNACAHIAhLDQULIAAQQQsgAg8LQfmvwABBLkGosMAAENwBAAtBuLDAAEEuQeiwwAAQ3AEAC0H5r8AAQS5BqLDAABDcAQALQbiwwABBLkHosMAAENwBAAsgBCABIAVBAXFyQQJyNgIAIAggAWoiAyAHIAFrIgFBAXI2AgRBACABNgLI2EBBACADNgLQ2EAgAAuWBgEHfwJAAkAgAQ0AIAVBAWohBiAAKAIUIQdBLSEIDAELQStBgIDEACAAKAIUIgdBAXEiARshCCABIAVqIQYLAkACQCAHQQRxDQBBACECDAELAkAgA0EQSQ0AIAIgAxA3IAZqIQYMAQsCQCADDQBBACAGaiEGDAELIANBA3EhCQJAAkAgA0EETw0AQQAhAUEAIQoMAQsgA0EMcSELQQAhAUEAIQoDQCABIAIgCmoiDCwAAEG/f0pqIAxBAWosAABBv39KaiAMQQJqLAAAQb9/SmogDEEDaiwAAEG/f0pqIQEgCyAKQQRqIgpHDQALCwJAIAlFDQAgAiAKaiEMA0AgASAMLAAAQb9/SmohASAMQQFqIQwgCUF/aiIJDQALCyABIAZqIQYLAkAgACgCAA0AAkAgACgCHCIBIAAoAiAiDCAIIAIgAxDqAUUNAEEBDwsgASAEIAUgDCgCDBELAA8LAkACQAJAAkAgACgCBCIBIAZLDQAgACgCHCIBIAAoAiAiDCAIIAIgAxDqAUUNAUEBDwsgB0EIcUUNASAAKAIQIQkgAEEwNgIQIAAtABghB0EBIQsgAEEBOgAYIAAoAhwiDCAAKAIgIgogCCACIAMQ6gENAiABIAZrQQFqIQECQANAIAFBf2oiAUUNASAMQTAgCigCEBEIAEUNAAtBAQ8LAkAgDCAEIAUgCigCDBELAEUNAEEBDwsgACAHOgAYIAAgCTYCEEEADwsgASAEIAUgDCgCDBELACELDAELIAEgBmshBgJAAkACQEEBIAAtABgiASABQQNGGyIBDgMCAAECCyAGIQFBACEGDAELIAZBAXYhASAGQQFqQQF2IQYLIAFBAWohASAAKAIQIQkgACgCICEMIAAoAhwhCgJAA0AgAUF/aiIBRQ0BIAogCSAMKAIQEQgARQ0AC0EBDwtBASELIAogDCAIIAIgAxDqAQ0AIAogBCAFIAwoAgwRCwANAEEAIQEDQAJAIAYgAUcNACAGIAZJDwsgAUEBaiEBIAogCSAMKAIQEQgARQ0ACyABQX9qIAZJDwsgCwuEBgEGfwJAIAAoAgAiAyAAKAIIIgRyRQ0AAkAgBEEBcUUNACABIAJqIQUCQAJAIAAoAgwiBg0AQQAhByABIQgMAQtBACEHIAEhCANAIAgiBCAFRg0CAkACQCAELAAAIghBf0wNACAEQQFqIQgMAQsCQCAIQWBPDQAgBEECaiEIDAELAkAgCEFwTw0AIARBA2ohCAwBCyAEQQRqIQgLIAggBGsgB2ohByAGQX9qIgYNAAsLIAggBUYNAAJAIAgsAAAiBEF/Sg0AIARBYEkaCwJAAkAgB0UNAAJAIAcgAkkNACAHIAJGDQFBACEEDAILIAEgB2osAABBQE4NAEEAIQQMAQsgASEECyAHIAIgBBshAiAEIAEgBBshAQsCQCADDQAgACgCHCABIAIgACgCICgCDBELAA8LIAAoAgQhAwJAAkAgAkEQSQ0AIAEgAhA3IQQMAQsCQCACDQBBACEEDAELIAJBA3EhBgJAAkAgAkEETw0AQQAhBEEAIQcMAQsgAkEMcSEFQQAhBEEAIQcDQCAEIAEgB2oiCCwAAEG/f0pqIAhBAWosAABBv39KaiAIQQJqLAAAQb9/SmogCEEDaiwAAEG/f0pqIQQgBSAHQQRqIgdHDQALCyAGRQ0AIAEgB2ohCANAIAQgCCwAAEG/f0pqIQQgCEEBaiEIIAZBf2oiBg0ACwsCQAJAIAMgBE0NACADIARrIQYCQAJAAkBBACAALQAYIgQgBEEDRhsiBA4DAgABAgsgBiEEQQAhBgwBCyAGQQF2IQQgBkEBakEBdiEGCyAEQQFqIQQgACgCECEHIAAoAiAhCCAAKAIcIQADQCAEQX9qIgRFDQIgACAHIAgoAhARCABFDQALQQEPCyAAKAIcIAEgAiAAKAIgKAIMEQsADwsCQCAAIAEgAiAIKAIMEQsARQ0AQQEPC0EAIQQDQAJAIAYgBEcNACAGIAZJDwsgBEEBaiEEIAAgByAIKAIQEQgARQ0ACyAEQX9qIAZJDwsgACgCHCABIAIgACgCICgCDBELAAuyBgIFfwJ+AkAgAigCACIDQRNNDQACQAJAIABCgICE/qbe4RFUDQAgAiADQXBqIgQ2AgAgASAEaiIFIAAgAEKAgIT+pt7hEYAiCEKAgIT+pt7hEX59IgBCgIDpg7HeFoCnQQF0Qe63wABqLwAAOwAAIAVBDGogAELkAIAiCULkAIKnQQF0Qe63wABqLwAAOwAAIAVBCmogAEKQzgCAQuQAgqdBAXRB7rfAAGovAAA7AAAgBUEIaiAAQsCEPYBC5ACCp0EBdEHut8AAai8AADsAACAFQQZqIABCgMLXL4CnQeQAcEEBdEHut8AAai8AADsAACAFQQRqIABCgMivoCWAp0HkAHBBAXRB7rfAAGovAAA7AAAgBUEOaiAAIAlC5AB+fadBAXRB7rfAAGovAAA7AAAgBUECaiAAQoCglKWNHYCnQf//A3FB5ABwQQF0Qe63wABqLwAAOwAAIAinIQUMAQsCQAJAIABCgMLXL1oNACAAIQgMAQsgAiADQXhqIgM2AgAgASADaiIFIAAgAEKAwtcvgCIIQoDC1y9+faciBEHAhD1uQQF0Qe63wABqLwAAOwAAIAVBBGogBEHkAG4iBkHkAHBBAXRB7rfAAGovAAA7AAAgBUEGaiAEIAZB5ABsa0EBdEHut8AAai8AADsAACAFQQJqIARBkM4AbkH//wNxQeQAcEEBdEHut8AAai8AADsAAAsgCKchBgJAIAhCkM4AWg0AIAMhBCAGIQUMAQsgASADQXxqIgRqIAYgBkGQzgBuIgVBkM4AbGsiBkH//wNxQeQAbiIHQQF0Qe63wABqLwAAOwAAIAEgA2pBfmogBiAHQeQAbGtB//8DcUEBdEHut8AAai8AADsAAAsCQAJAIAVB5ABPDQAgBSEDDAELIAEgBEF+aiIEaiAFIAVB//8DcUHkAG4iA0HkAGxrQf//A3FBAXRB7rfAAGovAAA7AAALAkAgA0H//wNxIgVBCkkNACABIARBfmoiBGogBUEBdEHut8AAai8AADsAACACIAQ2AgAPCyABIARBf2oiBWogA0EwcjoAACACIAU2AgAPC0G2ucAAQRxB1LnAABDcAQAL2AYBAn8jAEEgayIDJAACQAJAAkACQAJAAkACQAJAAkACQAJAAkAgAQ4oBgEBAQEBAQEBAgQBAQMBAQEBAQEBAQEBAQEBAQEBAQEBAQgBAQEBBwALIAFB3ABGDQQLIAFBgAZJDQcgAkEBcUUNByABEE5FDQcgA0EAOgAKIANBADsBCCADIAFBFHZBv7XAAGotAAA6AAsgAyABQQR2QQ9xQb+1wABqLQAAOgAPIAMgAUEIdkEPcUG/tcAAai0AADoADiADIAFBDHZBD3FBv7XAAGotAAA6AA0gAyABQRB2QQ9xQb+1wABqLQAAOgAMIANBCGogAUEBcmdBAnYiAmoiBEH7ADoAACAEQX9qQfUAOgAAIANBCGogAkF+aiICakHcADoAACADQQhqQQhqIgQgAUEPcUG/tcAAai0AADoAACAAQQo6AAsgACACOgAKIAAgAykCCDcCACADQf0AOgARIABBCGogBC8BADsBAAwJCyAAQYAEOwEKIABCADcBAiAAQdzoATsBAAwICyAAQYAEOwEKIABCADcBAiAAQdzkATsBAAwHCyAAQYAEOwEKIABCADcBAiAAQdzcATsBAAwGCyAAQYAEOwEKIABCADcBAiAAQdy4ATsBAAwFCyAAQYAEOwEKIABCADcBAiAAQdzgADsBAAwECyACQYACcUUNASAAQYAEOwEKIABCADcBAiAAQdzOADsBAAwDCyACQYCABHENAQsCQCABEHQNACADQQA6ABYgA0EAOwEUIAMgAUEUdkG/tcAAai0AADoAFyADIAFBBHZBD3FBv7XAAGotAAA6ABsgAyABQQh2QQ9xQb+1wABqLQAAOgAaIAMgAUEMdkEPcUG/tcAAai0AADoAGSADIAFBEHZBD3FBv7XAAGotAAA6ABggA0EUaiABQQFyZ0ECdiICaiIEQfsAOgAAIARBf2pB9QA6AAAgA0EUaiACQX5qIgJqQdwAOgAAIANBFGpBCGoiBCABQQ9xQb+1wABqLQAAOgAAIABBCjoACyAAIAI6AAogACADKQIUNwIAIANB/QA6AB0gAEEIaiAELwEAOwEADAILIAAgATYCBCAAQYABOgAADAELIABBgAQ7AQogAEIANwECIABB3MQAOwEACyADQSBqJAALpQYCBn8BfiMAQTBrIgIkAAJAAkACQAJAAkACQAJAAkACQCABRQ0AIAEoAgAhAyABQQM2AgAgA0EDRw0BCyACEO8CIgE2AiQCQCABEMICEJ8DIgQQlANBAUcNACAEIQUMBwsgARDDAhCfAyIDEJQDQQFHDQICQCADEMQCEJ8DIgUQlANBAUYNACAFQYQBSQ0DIAUQ6QEMAwsgBRDFAhCfAyIGEJEDIQcCQCAGQYQBSQ0AIAYQ6QELAkAgBUGEAUkNACAFEOkBCwJAIANBhAFJDQAgAxDpASAHQQFHDQQMAgsgB0EBRg0BDAMLIAEpAgQiCEIgiKchBCAIpyEFDAYLENoCIQEgAkEYahCFAgJAAkACQAJAAkAgAigCGCIDDQAgARCfAyEBDAELIAIoAhwhASADQQFxDQELIAJBEGogARCrAiACKAIUIQECQCACKAIQIgMNACACIAE2AiggAkHQn8AAQQYQwQIiATYCLCACQQhqIAJBKGogAkEkaiACQSxqEMMBIAIoAgwhBQJAIAIoAggiBg0AQQAhAwwEC0ECIQMgBkEBcUUNAiAFQYMBTQ0CIAUQ6QEgAigCLCEBDAILIANBAXENAEECIQNBjoCAgHghBQwFC0ECIQNBjoCAgHghBSABQYQBSQ0EIAEQ6QEMBAtBjICAgHghBQsCQCABQYQBSQ0AIAEQ6QELIAIoAigiAUGEAUkNAiABEOkBDAILIANBhAFJDQAgAxDpAQsgARDGAhCfAyIGEJQDQQFGDQFBAiEDQYeAgIB4IQUgBkGEAUkNACAGEOkBCwJAIARBhAFJDQAgBBDpAQsCQCACKAIkIgFBhAFJDQAgARDpAQsMAgsCQCAEQYQBSQ0AIAQQ6QELIAYhBQtBgAIQmAMhBAJAIAFBhAFJDQAgARDpAQtBASEDCyAAKAIIIQYgACAENgIIIAAoAgQhBCAAIAU2AgQgACgCACEBIAAgAzYCAAJAAkACQAJAIAEOBAABAwMBCyAEIQYgBEGDAUsNAQwCCwJAIARBhAFJDQAgBBDpAQsgBkGEAUkNAQsgBhDpAQsgAkEwaiQAIAALpQUBCH8CQAJAIAJBEE8NACAAIQMMAQsCQCAAQQAgAGtBA3EiBGoiBSAATQ0AIARBf2ohBiAAIQMgASEHAkAgBEUNACAEIQggACEDIAEhBwNAIAMgBy0AADoAACAHQQFqIQcgA0EBaiEDIAhBf2oiCA0ACwsgBkEHSQ0AA0AgAyAHLQAAOgAAIANBAWogB0EBai0AADoAACADQQJqIAdBAmotAAA6AAAgA0EDaiAHQQNqLQAAOgAAIANBBGogB0EEai0AADoAACADQQVqIAdBBWotAAA6AAAgA0EGaiAHQQZqLQAAOgAAIANBB2ogB0EHai0AADoAACAHQQhqIQcgA0EIaiIDIAVHDQALCyAFIAIgBGsiCEF8cSIGaiEDAkACQCABIARqIgdBA3ENACAFIANPDQEgByEBA0AgBSABKAIANgIAIAFBBGohASAFQQRqIgUgA0kNAAwCCwsgBSADTw0AIAdBA3QiAkEYcSEEIAdBfHEiCUEEaiEBQQAgAmtBGHEhCiAJKAIAIQIDQCAFIAIgBHYgASgCACICIAp0cjYCACABQQRqIQEgBUEEaiIFIANJDQALCyAIQQNxIQIgByAGaiEBCwJAIAMgAyACaiIFTw0AIAJBf2ohCAJAIAJBB3EiB0UNAANAIAMgAS0AADoAACABQQFqIQEgA0EBaiEDIAdBf2oiBw0ACwsgCEEHSQ0AA0AgAyABLQAAOgAAIANBAWogAUEBai0AADoAACADQQJqIAFBAmotAAA6AAAgA0EDaiABQQNqLQAAOgAAIANBBGogAUEEai0AADoAACADQQVqIAFBBWotAAA6AAAgA0EGaiABQQZqLQAAOgAAIANBB2ogAUEHai0AADoAACABQQhqIQEgA0EIaiIDIAVHDQALCyAAC48GAQJ/IwBBIGsiBSQAIAUQngM2AgggBUHri8AAQQYQwQI2AhAgBUGRjMAAQQQQwQI2AhwgBUEUaiAFQQhqIAVBEGogBUEcahCzAQJAAkACQAJAAkACQAJAAkAgBS0AFA0AAkAgBSgCHCIGQYQBSQ0AIAYQ6QELAkAgBSgCECIGQYQBSQ0AIAYQ6QELIAUQngM2AgwgBUH0i8AAQQwQwQI2AhAgBUGAjMAAQQoQwQI2AhwgBUEUaiAFQQxqIAVBEGogBUEcahCzASAFLQAUDQECQCAFKAIcIgZBhAFJDQAgBhDpAQsCQCAFKAIQIgZBhAFJDQAgBhDpAQsgBUGKjMAAQQcQwQI2AhwgBUEUaiAFQQhqIAVBHGogBUEMahCzASAFLQAUDQICQCAFKAIcIgZBhAFJDQAgBhDpAQsgBUGVjMAAQQQQwQIiBjYCHCAFQRRqIAVBCGogBUEcaiAEELMBIAUtABQNAwJAIAZBhAFJDQAgBhDpAQsgBUGBATYCHCAFIAIgAxDBAiIDNgIUIAUgASAFQRxqIAVBFGogBUEIahC8ASAFKAIEIQQgBSgCAA0EAkAgA0GDAU0NACADEOkBIAUoAhwiA0GEAUkNACADEOkBCyAFIAQ2AhQgBUEUahDwAiEDAkAgBSgCDCICQYQBSQ0AIAIQ6QELAkAgBSgCCCICQYQBSQ0AIAIQ6QELIANBAXMhAwwHCyAFKAIYIQQCQCAFKAIcIgNBhAFJDQAgAxDpAQsgBSgCECIDQYQBSQ0FIAMQ6QEMBQsgBSgCGCEEAkAgBSgCHCIDQYQBSQ0AIAMQ6QELIAUoAhAiA0GEAUkNAyADEOkBDAMLIAUoAhghBCAFKAIcIgNBhAFJDQIgAxDpAQwCCyAFKAIYIQQgBkGEAUkNASAGEOkBDAELIANBgwFNDQAgAxDpASAFKAIcIgNBhAFJDQAgAxDpAQsgBSgCDCIDQYQBSQ0AIAMQ6QELAkAgBSgCCCIDQYQBSQ0AIAMQ6QELQQEhAwsgACAENgIEIAAgAzYCACAFQSBqJAAL6wUCBn8CfgJAIAJFDQBBACACQXlqIgMgAyACSxshBCABQQNqQXxxIAFrIQVBACEDA0ACQAJAAkACQCABIANqLQAAIgbAIgdBAEgNACAFIANrQQNxDQEgAyAETw0CA0AgASADaiIGKAIEIAYoAgByQYCBgoR4cQ0DIANBCGoiAyAESQ0ADAMLC0KAgICAgCAhCUKAgICAECEKAkACQAJAAkACQAJAAkACQAJAAkACQAJAIAZB/7nAAGotAABBfmoOAwABAgoLIANBAWoiBiACSQ0CQgAhCUIAIQoMCQtCACEJIANBAWoiCCACSQ0CQgAhCgwIC0IAIQkgA0EBaiIIIAJJDQJCACEKDAcLQoCAgICAICEJQoCAgIAQIQogASAGaiwAAEG/f0oNBgwHCyABIAhqLAAAIQgCQAJAAkAgBkGgfmoODgACAgICAgICAgICAgIBAgsgCEFgcUGgf0YNBAwDCyAIQZ9/Sg0CDAMLAkAgB0EfakH/AXFBDEkNACAHQX5xQW5HDQIgCEFASA0DDAILIAhBQEgNAgwBCyABIAhqLAAAIQgCQAJAAkACQCAGQZB+ag4FAQAAAAIACyAHQQ9qQf8BcUECSw0DIAhBQE4NAwwCCyAIQfAAakH/AXFBME8NAgwBCyAIQY9/Sg0BCwJAIANBAmoiBiACSQ0AQgAhCgwFCyABIAZqLAAAQb9/Sg0CQgAhCiADQQNqIgYgAk8NBCABIAZqLAAAQb9/TA0FQoCAgICA4AAhCQwDC0KAgICAgCAhCQwCC0IAIQogA0ECaiIGIAJPDQIgASAGaiwAAEG/f0wNAwtCgICAgIDAACEJC0KAgICAECEKCyAAIAkgA62EIAqENwIEIABBATYCAA8LIAZBAWohAwwCCyADQQFqIQMMAQsgAyACTw0AA0AgASADaiwAAEEASA0BIAIgA0EBaiIDRw0ADAMLCyADIAJJDQALCyAAIAI2AgggACABNgIEIABBADYCAAv6BQEFfyAAQXhqIgEgAEF8aigCACICQXhxIgBqIQMCQAJAIAJBAXENACACQQJxRQ0BIAEoAgAiAiAAaiEAAkAgASACayIBQQAoAszYQEcNACADKAIEQQNxQQNHDQFBACAANgLE2EAgAyADKAIEQX5xNgIEIAEgAEEBcjYCBCADIAA2AgAPCyABIAIQWQsCQAJAAkACQAJAAkAgAygCBCICQQJxDQAgA0EAKALQ2EBGDQIgA0EAKALM2EBGDQMgAyACQXhxIgIQWSABIAIgAGoiAEEBcjYCBCABIABqIAA2AgAgAUEAKALM2EBHDQFBACAANgLE2EAPCyADIAJBfnE2AgQgASAAQQFyNgIEIAEgAGogADYCAAsgAEGAAkkNAiABIAAQXkEAIQFBAEEAKALk2EBBf2oiADYC5NhAIAANBAJAQQAoAqzWQCIARQ0AQQAhAQNAIAFBAWohASAAKAIIIgANAAsLQQAgAUH/HyABQf8fSxs2AuTYQA8LQQAgATYC0NhAQQBBACgCyNhAIABqIgA2AsjYQCABIABBAXI2AgQCQCABQQAoAszYQEcNAEEAQQA2AsTYQEEAQQA2AszYQAsgAEEAKALc2EAiBE0NA0EAKALQ2EAiAEUNA0EAIQJBACgCyNhAIgVBKUkNAkGk1sAAIQEDQAJAIAEoAgAiAyAASw0AIAAgAyABKAIEakkNBAsgASgCCCEBDAALC0EAIAE2AszYQEEAQQAoAsTYQCAAaiIANgLE2EAgASAAQQFyNgIEIAEgAGogADYCAA8LIABB+AFxQbTWwABqIQMCQAJAQQAoArzYQCICQQEgAEEDdnQiAHENAEEAIAIgAHI2ArzYQCADIQAMAQsgAygCCCEACyADIAE2AgggACABNgIMIAEgAzYCDCABIAA2AggPCwJAQQAoAqzWQCIBRQ0AQQAhAgNAIAJBAWohAiABKAIIIgENAAsLQQAgAkH/HyACQf8fSxs2AuTYQCAFIARNDQBBAEF/NgLc2EALC7IFAQJ/IwBBIGsiBCQAIAQQngM2AgggBEHri8AAQQYQwQI2AhAgBEHxi8AAQQMQwQI2AhwgBEEUaiAEQQhqIARBEGogBEEcahCzAQJAAkACQAJAAkACQAJAIAQtABQNAAJAIAQoAhwiBUGEAUkNACAFEOkBCwJAIAQoAhAiBUGEAUkNACAFEOkBCyAEEJ4DNgIMIARB9IvAAEEMEMECNgIQIARBgIzAAEEKEMECNgIcIARBFGogBEEMaiAEQRBqIARBHGoQswEgBC0AFA0BAkAgBCgCHCIFQYQBSQ0AIAUQ6QELAkAgBCgCECIFQYQBSQ0AIAUQ6QELIARBiozAAEEHEMECIgU2AhwgBEEUaiAEQQhqIARBHGogBEEMahCzASAELQAUDQICQCAFQYQBSQ0AIAUQ6QELIARBgQE2AhwgBCACIAMQwQIiAjYCFCAEIAEgBEEcaiAEQRRqIARBCGoQvAEgBCgCBCEDIAQoAgANAwJAIAJBgwFNDQAgAhDpASAEKAIcIgJBhAFJDQAgAhDpAQsgBCADNgIUIARBFGoQ8AIhAgJAIAQoAgwiAUGEAUkNACABEOkBCwJAIAQoAggiAUGEAUkNACABEOkBCyACQQFzIQIMBgsgBCgCGCEDAkAgBCgCHCICQYQBSQ0AIAIQ6QELIAQoAhAiAkGEAUkNBCACEOkBDAQLIAQoAhghAwJAIAQoAhwiAkGEAUkNACACEOkBCyAEKAIQIgJBhAFJDQIgAhDpAQwCCyAEKAIYIQMgBUGEAUkNASAFEOkBDAELIAJBgwFNDQAgAhDpASAEKAIcIgJBhAFJDQAgAhDpAQsgBCgCDCICQYQBSQ0AIAIQ6QELAkAgBCgCCCICQYQBSQ0AIAIQ6QELQQEhAgsgACADNgIEIAAgAjYCACAEQSBqJAALhwUBCn8jAEEwayIDJAAgAyABNgIsIAMgADYCKCADQQM6ACQgA0IgNwIcQQAhBCADQQA2AhQgA0EANgIMAkACQAJAAkACQCACKAIQIgUNACACKAIMIgBFDQEgAigCCCIBIABBA3RqIQYgAEF/akH/////AXFBAWohBCACKAIAIQADQAJAIABBBGooAgAiB0UNACADKAIoIAAoAgAgByADKAIsKAIMEQsADQQLIAEoAgAgA0EMaiABQQRqKAIAEQgADQMgAEEIaiEAIAFBCGoiASAGRw0ADAILCyACKAIUIgFFDQAgAUEFdCEIIAFBf2pB////P3FBAWohBCACKAIIIQkgAigCACEAQQAhBwNAAkAgAEEEaigCACIBRQ0AIAMoAiggACgCACABIAMoAiwoAgwRCwANAwsgAyAFIAdqIgFBEGooAgA2AhwgAyABQRxqLQAAOgAkIAMgAUEYaigCADYCICABQQxqKAIAIQZBACEKQQAhCwJAAkACQCABQQhqKAIADgMBAAIBCyAGQQN0IQxBACELIAkgDGoiDCgCAA0BIAwoAgQhBgtBASELCyADIAY2AhAgAyALNgIMIAFBBGooAgAhBgJAAkACQCABKAIADgMBAAIBCyAGQQN0IQsgCSALaiILKAIADQEgCygCBCEGC0EBIQoLIAMgBjYCGCADIAo2AhQgCSABQRRqKAIAQQN0aiIBKAIAIANBDGogAUEEaigCABEIAA0CIABBCGohACAIIAdBIGoiB0cNAAsLIAQgAigCBE8NASADKAIoIAIoAgAgBEEDdGoiASgCACABKAIEIAMoAiwoAgwRCwBFDQELQQEhAQwBC0EAIQELIANBMGokACABC7QEAQR/IwBBgAFrIgIkAAJAAkACQAJAIAEoAhQiA0EQcQ0AIANBIHENAUEBIQMgACgCAEEBIAEQVEUNAgwDCyAAKAIAIQNBACEEA0AgAiAEakH/AGogA0EPcSIFQTByIAVB1wBqIAVBCkkbOgAAIARBf2ohBCADQRBJIQUgA0EEdiEDIAVFDQALQQEhAyABQQFB7LfAAEECIAIgBGpBgAFqQQAgBGsQOUUNAQwCCyAAKAIAIQNBACEEA0AgAiAEakH/AGogA0EPcSIFQTByIAVBN2ogBUEKSRs6AAAgBEF/aiEEIANBD0shBSADQQR2IQMgBQ0AC0EBIQMgAUEBQey3wABBAiACIARqQYABakEAIARrEDkNAQtBASEDIAEoAhxBvbXAAEECIAEoAiAoAgwRCwANAAJAAkAgASgCFCIDQRBxDQAgA0EgcQ0BIAAoAgRBASABEFQhAwwCCyAAKAIEIQNBACEEA0AgAiAEakH/AGogA0EPcSIFQTByIAVB1wBqIAVBCkkbOgAAIARBf2ohBCADQQ9LIQUgA0EEdiEDIAUNAAsgAUEBQey3wABBAiACIARqQYABakEAIARrEDkhAwwBCyAAKAIEIQNBACEEA0AgAiAEakH/AGogA0EPcSIFQTByIAVBN2ogBUEKSRs6AAAgBEF/aiEEIANBD0shBSADQQR2IQMgBQ0ACyABQQFB7LfAAEECIAIgBGpBgAFqQQAgBGsQOSEDCyACQYABaiQAIAMLtAQBDX8gAUF/aiEDIAAoAgQhBCAAKAIAIQUgACgCCCEGQQAhB0EAIQhBACEJQQAhCgJAA0AgCkEBcQ0BAkACQCAJIAJLDQADQCABIAlqIQsCQAJAAkACQCACIAlrIgxBB0sNACACIAlHDQEgAiEJDAULAkACQCALQQNqQXxxIg0gC2siDkUNAEEAIQADQCALIABqLQAAQQpGDQUgDiAAQQFqIgBHDQALIA4gDEF4aiIPTQ0BDAMLIAxBeGohDwsDQEGAgoQIIA0oAgAiAEGKlKjQAHNrIAByQYCChAggDUEEaigCACIAQYqUqNAAc2sgAHJxQYCBgoR4cUGAgYKEeEcNAiANQQhqIQ0gDkEIaiIOIA9NDQAMAgsLQQAhAANAIAsgAGotAABBCkYNAiAMIABBAWoiAEcNAAsgAiEJDAMLAkAgDiAMRw0AIAIhCQwDCwNAAkAgCyAOai0AAEEKRw0AIA4hAAwCCyAMIA5BAWoiDkcNAAsgAiEJDAILIAAgCWoiDkEBaiEJAkAgDiACTw0AIAsgAGotAABBCkcNACAJIQsgCSEADAMLIAkgAk0NAAsLQQEhCiAIIQsgAiEAIAggAkYNAgsCQAJAIAYtAABFDQAgBUHEt8AAQQQgBCgCDBELAA0BCyAAIAhrIQ1BACEOAkAgACAIRg0AIAMgAGotAABBCkYhDgsgASAIaiEAIAYgDjoAACALIQggBSAAIA0gBCgCDBELAEUNAQsLQQEhBwsgBwvKBAICfwJ+IwBBoAFrIgQkACAEQSc2ApwBAkACQCABQoCAIFQNACAEQTBqIABCAELzstjBnp69zJV/QgAQkgEgBEHAAGogAUIAQvOy2MGenr3MlX9CABCSASAEQSBqIABCAELS4ara7afJh/YAQgAQkgEgBEHQAGogAUIAQtLhqtrtp8mH9gBCABCSASAEQeAAaiAAIAFCAEIAEJIBIARB0ABqQQhqKQMAIARBwABqQQhqKQMAIAQpA0AiASAEQTBqQQhqKQMAfCIGIAFUrXwiASAEQSBqQQhqKQMAIAQpAyAiByAGfCAHVK18fCIGIAFUrXwgBCkDUCIHIAZ8IgEgB1StfCAEQeAAakEIaikDAHwgASAEKQNgfCIHIAFUrXwiAUI+iCEGIAdCPoggAUIChoQhAQwBCyAAQhOIIAFCLYaEQr2igqOOqwSAIQFCACEGCyAEQRBqIAEgBkKAgOCwt5+3nPUAQgAQkgEgBCkDECAAfCAEQfUAaiAEQZwBahA7IAQoApwBIQUCQCABIAaEQgBRDQAgBEH1AGpBFGpBMCAFQWxqEE0aIARBFDYCnAEgBCABQhOIIAZCLYaEIgBCvaKCo46rBIAiBiABQoCA4LC3n7ec9QBCABCSASAEKQMAIAF8IARB9QBqIARBnAFqEDsgBCgCnAEhBSAAQr2igqOOqwRUDQAgBEH2AGpBMCAFQX9qEE0aIAQgBqdBMGo6AHVBACEFCyADIAJBAUEAIARB9QBqIAVqQScgBWsQOSEFIARBoAFqJAAgBQuEBAENfyMAQRBrIgIkAEEAIQMCQAJAIAEtACUNAAJAIAEoAhAiBCABKAIMIgVJDQAgBCABKAIIIgZLDQAgASgCBCEHIAFBE2ohCCABQRRqIQkDQCABKAIEIAVqIQogCCABLQAYIgtqLQAAIQwCQAJAAkACQAJAAkAgBCAFayINQQdLDQACQCAEIAVGDQBBACEOIAxB/wFxIQwDQCAKIA5qLQAAIAxGDQMgDSAOQQFqIg5HDQALCyABIAQ2AgwMBwsgAkEIaiAMIAogDRBiIAIoAggiDkEBRw0BIAIoAgwhDiABLQAYIQsgASgCCCEGIAEoAgwhBQsgASAFIA5qQQFqIgU2AgwgBSALSQ0DIAUgBksNAyALQQVPDQIgASgCBCAFIAtrIgpqIAsgCSALEJcCIQ4gASgCDCEFIA4NASABKAIIIQYMAwsgASABKAIQNgIMIA5BAXFFDQQLIAEoAhwhDiABIAU2AhwgByAOaiEDIAogDmshDgwFCyALQQRBgJnAABD0AgALIAEoAhAiBCAFSQ0BIAQgBk0NAAsLIAEtACUNACABQQE6ACUCQAJAIAEtACRBAUcNACABKAIgIQ0gASgCHCEKDAELIAEoAiAiDSABKAIcIgpGDQILIA0gCmshDiABKAIEIApqIQMMAQsLIAAgDjYCBCAAIAM2AgAgAkEQaiQAC9oDAgx/AX4CQAJAIAEoAhQiByAFQX9qIghqIgkgA08NACAFIAEoAhAiCmshCyABKAIcIQwgASgCCCENIAEpAwAhEwNAAkACQAJAIBMgAiAJajEAAIhCAYNQRQ0AIAEgByAFaiIHNgIUQQAhCSAGDQIMAQsgDSAMIA0gDCANSxsgBhsiDiAFIA4gBUsbIQ8gAiAHaiEQIA4hCQJAAkACQANAAkAgDyAJRw0AQQAgDCAGGyERIA0hCQNAAkAgESAJSQ0AIAEgByAFaiIJNgIUAkAgBg0AIAFBADYCHAsgACAJNgIIIAAgBzYCBEEBIQkMCwsgCUF/aiIJIAVPDQUgCSAHaiISIANPDQMgBCAJai0AACACIBJqLQAARg0ACyABIAcgCmoiBzYCFCALIQkgBkUNBQwGCyAHIAlqIANPDQIgECAJaiESIAQgCWohESAJQQFqIQkgES0AACASLQAARg0ACyAHIA1rIAlqIQcgBg0EQQAhCQwDCyASIANB8IDAABCfAQALIAMgDiAHaiIJIAMgCUsbIANBgIHAABCfAQALIAkgBUHggMAAEJ8BAAsgASAJNgIcIAkhDAsgByAIaiIJIANJDQALCyABIAM2AhRBACEJCyAAIAk2AgAL2gMCDH8BfgJAAkAgASgCFCIHIAVBf2oiCGoiCSADTw0AIAUgASgCECIKayELIAEoAhwhDCABKAIIIQ0gASkDACETA0ACQAJAAkAgEyACIAlqMQAAiEIBg1BFDQAgASAHIAVqIgc2AhRBACEJIAYNAgwBCyANIAwgDSAMIA1LGyAGGyIOIAUgDiAFSxshDyACIAdqIRAgDiEJAkACQAJAA0ACQCAPIAlHDQBBACAMIAYbIREgDSEJA0ACQCARIAlJDQAgASAHIAVqIgk2AhQCQCAGDQAgAUEANgIcCyAAIAk2AgggACAHNgIEQQEhCQwLCyAJQX9qIgkgBU8NBSAJIAdqIhIgA08NAyAEIAlqLQAAIAIgEmotAABGDQALIAEgByAKaiIHNgIUIAshCSAGRQ0FDAYLIAcgCWogA08NAiAQIAlqIRIgBCAJaiERIAlBAWohCSARLQAAIBItAABGDQALIAcgDWsgCWohByAGDQRBACEJDAMLIBIgA0H4jMAAEJ8BAAsgAyAOIAdqIgkgAyAJSxsgA0GIjcAAEJ8BAAsgCSAFQeiMwAAQnwEACyABIAk2AhwgCSEMCyAHIAhqIgkgA0kNAAsLIAEgAzYCFEEAIQkLIAAgCTYCAAvzAwEFfyMAQYABayIBJAAgAUEQahDOAgJAAkAgAUEQakEQEPcBIgINACABQSBqQQhqIAFBEGpBCGopAAA3AwAgASABKQAQNwMgIAFBIGpBEGohA0EAIQICQAJAA0AgAkEQRg0BIAFBIGogAmohBCACQQFqIQIgBC0AAEUNAAsgAUEwakEIaiIFQgA3AwAgAUIANwMwIAFCgICAgMAANwJ0IAEgAzYCcCABIAFBIGo2AmggAUEQNgJsIAFBwABqIAFBMGogAUEwakEQaiABQegAahCWAQJAIAEoAlwiAiABKAJgTw0AIAFBwABqQQhqIQMDQCABIAJBAWo2AlwgASgCQCEEIAFBCGogAyACEJoCIAEoAgxBBEcNBSAEIAJBAnRqIAEoAggoAAA2AgAgASgCXCICIAEoAmBJDQALCyABQegAakEIaiAFKQMANwMAIAEgASkDMDcDaAwBCyABQegAakIAEHULIAAgASkDaDcCACAAQQhqIAFB6ABqQQhqKQMANwIAIAFBgAFqJAAPCyABIAI2AjAgAUEBNgJEIAFBkJXAADYCQCABQgE3AkwgAUEYNgJsIAEgAUHoAGo2AkggASABQTBqNgJoIAFBwABqQfiVwAAQgQIAC0Gsk8AAQSsgAUHoAGpBnJPAAEHklsAAEJMBAAvxAwECfyAAIAFqIQICQAJAIAAoAgQiA0EBcQ0AIANBAnFFDQEgACgCACIDIAFqIQECQCAAIANrIgBBACgCzNhARw0AIAIoAgRBA3FBA0cNAUEAIAE2AsTYQCACIAIoAgRBfnE2AgQgACABQQFyNgIEIAIgATYCAAwCCyAAIAMQWQsCQAJAAkACQCACKAIEIgNBAnENACACQQAoAtDYQEYNAiACQQAoAszYQEYNAyACIANBeHEiAxBZIAAgAyABaiIBQQFyNgIEIAAgAWogATYCACAAQQAoAszYQEcNAUEAIAE2AsTYQA8LIAIgA0F+cTYCBCAAIAFBAXI2AgQgACABaiABNgIACwJAIAFBgAJJDQAgACABEF4PCyABQfgBcUG01sAAaiECAkACQEEAKAK82EAiA0EBIAFBA3Z0IgFxDQBBACADIAFyNgK82EAgAiEBDAELIAIoAgghAQsgAiAANgIIIAEgADYCDCAAIAI2AgwgACABNgIIDwtBACAANgLQ2EBBAEEAKALI2EAgAWoiATYCyNhAIAAgAUEBcjYCBCAAQQAoAszYQEcNAUEAQQA2AsTYQEEAQQA2AszYQA8LQQAgADYCzNhAQQBBACgCxNhAIAFqIgE2AsTYQCAAIAFBAXI2AgQgACABaiABNgIADwsL0wMBBH8jAEHAAGsiAiQAAkACQAJAIAAtABRBAkcNACABKAIYIQMgAUEANgIYIANFDQEgAkEoaiADEQMAIAJBEGpBEGoiAyACQShqQRBqKAIANgIAIAJBEGpBCGoiBCACQShqQQhqKQIANwMAIAJBDGpBAmoiBSACQShqQRdqLQAAOgAAIAIgAikCKDcDECACIAIvAD07AQwgAi0APCEBAkACQCAALQAUQQJHDQAgACACKQMQNwIAIAAgAToAFCAAIAIvAQw7ABUgAEEQaiADKAIANgIAIABBCGogBCkDADcCACAAQRdqIAUtAAA6AAAMAQsgAUH/AXFBAkcNAwsgAkECOgA8IAJBKGoQyAELIAJBwABqJAAgAA8LIAJBADYCOCACQQE2AiwgAkH4pcAANgIoIAJCBDcCMCACQShqQeCmwAAQgQIACyACQT9qIAJBDmotAAA6AAAgAkEoakEIaiACQRBqQQhqKQMANwMAIAJBKGpBEGogAkEQakEQaigCADYCACACIAIvAQw7AD0gAiACKQMQNwMoIAIgAToAPCACQShqEMgBIAJBADYCOCACQQE2AiwgAkGAp8AANgIoIAJCBDcCMCACQShqQYinwAAQgQIAC6oDAQV/AkACQCACQRBPDQAgACEDDAELAkAgAEEAIABrQQNxIgRqIgUgAE0NACAEQX9qIQYgACEDAkAgBEUNACAEIQcgACEDA0AgAyABOgAAIANBAWohAyAHQX9qIgcNAAsLIAZBB0kNAANAIAMgAToAACADQQdqIAE6AAAgA0EGaiABOgAAIANBBWogAToAACADQQRqIAE6AAAgA0EDaiABOgAAIANBAmogAToAACADQQFqIAE6AAAgA0EIaiIDIAVHDQALCwJAIAUgBSACIARrIgJBfHFqIgNPDQAgAUH/AXFBgYKECGwhBwNAIAUgBzYCACAFQQRqIgUgA0kNAAsLIAJBA3EhAgsCQCADIAMgAmoiB08NACACQX9qIQQCQCACQQdxIgVFDQADQCADIAE6AAAgA0EBaiEDIAVBf2oiBQ0ACwsgBEEHSQ0AA0AgAyABOgAAIANBB2ogAToAACADQQZqIAE6AAAgA0EFaiABOgAAIANBBGogAToAACADQQNqIAE6AAAgA0ECaiABOgAAIANBAWogAToAACADQQhqIgMgB0cNAAsLIAALoAMBBH8CQAJAQQBBESAAQa+wBEkbIgEgAUEIciIBIAFBAnRB5MzAAGooAgBBC3QgAEELdCIBSxsiAiACQQRyIgIgAkECdEHkzMAAaigCAEELdCABSxsiAiACQQJyIgIgAkECdEHkzMAAaigCAEELdCABSxsiAiACQQFqIgIgAkECdEHkzMAAaigCAEELdCABSxsiAiACQQFqIgIgAkECdEHkzMAAaigCAEELdCABSxsiAkECdEHkzMAAaigCAEELdCIDIAFGIAMgAUlqIAJqIgJBIUsNACACQQJ0QeTMwABqIgMoAgBBFXYhAUHvBSEEAkACQCACQSFGDQAgAygCBEEVdiEEIAINAEEAIQIMAQsgA0F8aigCAEH///8AcSECCwJAIAQgAUF/c2pFDQAgACACayEDIAFB7wUgAUHvBUsbIQIgBEF/aiEEQQAhAANAIAIgAUYNAyAAIAFB7M3AAGotAABqIgAgA0sNASAEIAFBAWoiAUcNAAsgBCEBCyABQQFxDwsgAkEiQfzKwAAQnwEACyACQe8FQYzLwAAQnwEAC5oDAQh/IwBBMGsiAyQAIAMgAjYCJEEAIQQgA0EANgIgIAMgAjYCHCADIAE2AhggA0EBOgAsIANBJTYCFCADQSU2AiggA0EnaiEFIANBKGohBiACIQECQAJAA0AgAygCGCAEaiEHIAUgAy0ALCIIai0AACEJAkACQCABIARrIgpBB0sNACABIARGDQNBACEBIAlB/wFxIQkDQCAHIAFqLQAAIAlGDQIgCiABQQFqIgFGDQQMAAsLIANBCGogCSAHIAoQYgJAIAMoAggiAUEBRg0AIAFBAXEhCgwECyADKAIMIQEgAy0ALCEIIAMoAhwhAiADKAIgIQQLIAMgBCABakEBaiIENgIgAkAgBCAISQ0AIAQgAksNAAJAIAhBBU8NAAJAIAMoAhggBCAIayIHaiAIIAYgCBCXAkUNAEEBIQoMBQsgAygCHCECIAMoAiAhBAwBCyAIQQRBnITAABD0AgALQQAhCgJAIAMoAiQiASAETw0ADAMLIAEgAk0NAAwCCwtBACEKCyAAIAc2AgQgACAKNgIAIANBMGokAAuaAwEIfyMAQTBrIgMkACADIAI2AiRBACEEIANBADYCICADIAI2AhwgAyABNgIYIANBAToALCADQSU2AhQgA0ElNgIoIANBJ2ohBSADQShqIQYgAiEBAkACQANAIAMoAhggBGohByAFIAMtACwiCGotAAAhCQJAAkAgASAEayIKQQdLDQAgASAERg0DQQAhASAJQf8BcSEJA0AgByABai0AACAJRg0CIAogAUEBaiIBRg0EDAALCyADQQhqIAkgByAKEGICQCADKAIIIgFBAUYNACABQQFxIQoMBAsgAygCDCEBIAMtACwhCCADKAIcIQIgAygCICEECyADIAQgAWpBAWoiBDYCIAJAIAQgCEkNACAEIAJLDQACQCAIQQVPDQACQCADKAIYIAQgCGsiB2ogCCAGIAgQlwJFDQBBASEKDAULIAMoAhwhAiADKAIgIQQMAQsgCEEEQcCOwAAQ9AIAC0EAIQoCQCADKAIkIgEgBE8NAAwDCyABIAJNDQAMAgsLQQAhCgsgACAHNgIEIAAgCjYCACADQTBqJAAL7wIBBX9BACECAkBBzf97IABBECAAQRBLGyIAayABTQ0AIABBECABQQtqQXhxIAFBC0kbIgNqQQxqEC8iAUUNACABQXhqIQICQAJAIABBf2oiBCABcQ0AIAIhAAwBCyABQXxqIgUoAgAiBkF4cSAEIAFqQQAgAGtxQXhqIgFBACAAIAEgAmtBEEsbaiIAIAJrIgFrIQQCQCAGQQNxRQ0AIAAgBCAAKAIEQQFxckECcjYCBCAAIARqIgQgBCgCBEEBcjYCBCAFIAEgBSgCAEEBcXJBAnI2AgAgAiABaiIEIAQoAgRBAXI2AgQgAiABEEsMAQsgAigCACECIAAgBDYCBCAAIAIgAWo2AgALAkAgACgCBCIBQQNxRQ0AIAFBeHEiAiADQRBqTQ0AIAAgAyABQQFxckECcjYCBCAAIANqIgEgAiADayIDQQNyNgIEIAAgAmoiAiACKAIEQQFyNgIEIAEgAxBLCyAAQQhqIQILIAILkgMBB38jAEEQayICJAACQAJAAkACQAJAIAEoAgQiA0UNACABKAIAIQQgA0EDcSEFAkACQCADQQRPDQBBACEDQQAhBgwBCyAEQRxqIQcgA0F8cSEIQQAhA0EAIQYDQCAHKAIAIAdBeGooAgAgB0FwaigCACAHQWhqKAIAIANqampqIQMgB0EgaiEHIAggBkEEaiIGRw0ACwsCQCAFRQ0AIAZBA3QgBGpBBGohBwNAIAcoAgAgA2ohAyAHQQhqIQcgBUF/aiIFDQALCwJAIAEoAgxFDQAgA0EASA0BIANBEEkgBCgCBEVxDQEgA0EBdCEDC0EAIQUgA0EASA0DIAMNAQtBASEHQQAhAwwBC0EALQCB1UAaQQEhBSADQQEQ4gIiB0UNAQsgAkEANgIIIAIgBzYCBCACIAM2AgAgAkGQs8AAIAEQQ0UNAUGstMAAQdYAIAJBD2pBnLTAAEGctcAAEJMBAAsgBSADQYy0wAAQtQIACyAAIAIpAgA3AgAgAEEIaiACQQhqKAIANgIAIAJBEGokAAubAwEHfyMAQTBrIgAkACAAQShqQainwAAQ4gEgACgCLCEBAkACQAJAIAAoAigiAkEBcQ0AIABBIGpBrKfAABDiASAAKAIkIQMgACgCICEEAkAgAkUNACABQYQBSQ0AIAEQ6QELAkAgBEEBcUUNACADIQEMAQsgAEEYakGkp8AAEOIBIAAoAhwhBSAAKAIYIQICQCAERQ0AIANBhAFJDQAgAxDpAQsCQCACQQFxRQ0AIAUhAQwBCyAAQRBqQbCnwAAQ4gEgACgCFCEBIAAoAhAhBgJAIAJFDQAgBUGEAUkNACAFEOkBC0EBIQUgBkEBRw0BCwJAIAEQkwNBAUYNACABIQIMAgtBACEFQQEhBiABQYQBSQ0AIAEQ6QELQZinwABBCxDIAiIEQYABELACIQMgAEEIahCFAgJAIAAoAgwgAyAAKAIIIgIbIgNBhAFJDQAgAkEBcUUNACADEOkBCwJAIARBhAFJDQAgBBDpAQtBgAEgAyACQQFGGyECIAUgBnFBAUcNACABQYMBTQ0AIAEQ6QELIABBMGokACACC4ADAQh/IwBBEGsiAyQAQQohBCAAIQUCQCAAQegHSQ0AQQohBCAAIQYDQCADQQZqIARqIgdBfWogBiAGQZDOAG4iBUGQzgBsayIIQf//A3FB5ABuIglBAXQiCkHvt8AAai0AADoAACAHQXxqIApB7rfAAGotAAA6AAAgB0F/aiAIIAlB5ABsa0H//wNxQQF0IghB77fAAGotAAA6AAAgB0F+aiAIQe63wABqLQAAOgAAIARBfGohBCAGQf+s4gRLIQcgBSEGIAcNAAsLAkACQCAFQQlLDQAgBSEGDAELIANBBmogBGpBf2ogBSAFQf//A3FB5ABuIgZB5ABsa0H//wNxQQF0IgdB77fAAGotAAA6AAAgA0EGaiAEQX5qIgRqIAdB7rfAAGotAAA6AAALAkACQCAARQ0AIAZFDQELIANBBmogBEF/aiIEaiAGQQF0QR5xQe+3wABqLQAAOgAACyACIAFBAUEAIANBBmogBGpBCiAEaxA5IQYgA0EQaiQAIAYLjQMCB38BfiMAQTBrIgMkAAJAAkBBACABKAIAEQQAIgRFDQAgBCgCAA0BIARBfzYCAAJAIAQoAgRBAUcNACAEKAIIIQEgA0GBATYCJCADIAG3ENQCIgE2AhQgA0EIaiACQQhqIANBJGogA0EUahDDAQJAIAMoAgwiAkGEAUkNACACEOkBIAMoAhQhAQsCQCABQYQBSQ0AIAEQ6QELAkAgAygCJCIBQYQBSQ0AIAEQ6QELIARBADYCBAsgA0EUahBKIANBADoALCADQuDUg4CA+MkENwIkIANBFGogA0EkahBgIQUgAygCHCEGIAMoAhghAiADKAIgIQcgAygCFCEBA0AgASAGcyIIIAJBCXRzIQYgByACcyEJIAggAnMhAiABIAdqQQd3IAFqIQggCUELdyEHIAkgAXMhASAIrUKPvwV+IgqnQf//nf56Sw0ACyAEIAQoAgBBAWo2AgAgACAKQiCIp0GQzgBqNgIEIAAgBTYCACADQTBqJAAPC0GMk8AAEL8BAAtBkJfAABDAAQALowMBBH8jAEHwAGsiASQAIAEgADYCECABQQA2AiggAUEANgIgIAFCgICAgCA3AhQgAUEUahDsASIAIAAoAgBBAWoiAjYCAAJAAkAgAkUNACABQQhqIAAQ+QEgASgCCCICQeSgwAAQ/QIhAyAAIAAoAgBBAWoiBDYCACABQeSgwAA2AkQgASACNgJAIAEgAzYCSCAERQ0AIAEgABD4ASABKAIAIgJB0KDAABD9AiEDIAFB0KDAADYCUCABIAI2AkwgASADNgJUAkAgAUEQaiABQcAAaiABQcwAahDXAiICQYQBSQ0AIAIQ6QELIAFB2ABqQQhqIgIgAUHAAGpBCGooAgA2AgAgAUHsAGogAUHMAGpBCGooAgA2AgAgASABKQJANwNYIAEgASkCTDcCZCAAKAIIDQEgAEF/NgIIIABBHGoQqgIgAEEsaiABQegAaikDADcCACAAQSRqIAIpAwA3AgAgACABKQNYNwIcIAAgACgCCEEBajYCCAJAIAEoAhAiAkGEAUkNACACEOkBCyABQfAAaiQAIAAPCwALQayjwAAQwAEAC+oCAQd/QQEhBwJAAkAgAkUNACABIAJBAXRqIQggAEGA/gNxQQh2IQlBACEKIABB/wFxIQsDQCABQQJqIQwgCiABLQABIgJqIQ0CQCABLQAAIgEgCUYNACABIAlLDQIgDSEKIAwhASAMIAhGDQIMAQsCQAJAAkAgDSAKSQ0AIA0gBEsNASADIApqIQEDQCACRQ0DIAJBf2ohAiABLQAAIQogAUEBaiEBIAogC0cNAAtBACEHDAULIAogDUGYv8AAEPUCAAsgDSAEQZi/wAAQ9AIACyANIQogDCEBIAwgCEcNAAsLIAZFDQAgBSAGaiELIABB//8DcSEBQQEhBwNAIAVBAWohCgJAAkAgBSwAACICQQBIDQAgCiEFDAELAkAgCiALRg0AIAJB/wBxQQh0IAUtAAFyIQIgBUECaiEFDAELQYi/wAAQ9wIACyABIAJrIgFBAEgNASAHQQFzIQcgBSALRw0ACwsgB0EBcQuLAwEFfyMAQSBrIgIkAAJAAkACQEEAIAAoAgARBAAiAEUNACAAIAAoAgBBAkYiA0ECdGoiBCgCACEFIAMNAiABKAIEIQACQCAFQQFxDQAgASgCACEFA0AgAEUNAyAFIABB/////wcgAEH/////B0kbIgMQ8AEhASAEKAIEIAEQ2wIgACADayEAIAUgA2ohBSACQRBqEIUCIAIoAhBFDQALQY2AgIB4IQUgAigCFCIAQYQBSQ0DIAAQ6QEMAwsgAEUNASAEQQhqIQYgASgCACEFAkADQCACIAZBACAAQYACIABBgAJJGyIDEPICIgE2AhwgBCgCBCABEPgCIAJBCGoQhQIgAigCCA0BIAAgA2shACACQRxqIAUQzgECQCACKAIcIgFBhAFJDQAgARDpAQsgBSADaiEFIABFDQMMAAsLAkAgAigCDCIAQYQBSQ0AIAAQ6QELAkAgAigCHCIAQYQBSQ0AIAAQ6QELQYiAgIB4IQUMAgtBwJrAABC/AQALQQAhBQsgAkEgaiQAIAUL+QIBBH8gACgCDCECAkACQAJAIAFBgAJJDQAgACgCGCEDAkACQAJAIAIgAEcNACAAQRRBECAAKAIUIgIbaigCACIBDQFBACECDAILIAAoAggiASACNgIMIAIgATYCCAwBCyAAQRRqIABBEGogAhshBANAIAQhBSABIgJBFGogAkEQaiACKAIUIgEbIQQgAkEUQRAgARtqKAIAIgENAAsgBUEANgIACyADRQ0CAkAgACgCHEECdEGk1cAAaiIBKAIAIABGDQAgA0EQQRQgAygCECAARhtqIAI2AgAgAkUNAwwCCyABIAI2AgAgAg0BQQBBACgCwNhAQX4gACgCHHdxNgLA2EAMAgsCQCACIAAoAggiBEYNACAEIAI2AgwgAiAENgIIDwtBAEEAKAK82EBBfiABQQN2d3E2ArzYQA8LIAIgAzYCGAJAIAAoAhAiAUUNACACIAE2AhAgASACNgIYCyAAKAIUIgFFDQAgAiABNgIUIAEgAjYCGA8LC4oDAQN/IwBBwABrIgMkACADQRxqIAAoAgAiBBDGASADQRBqIAEQ5QEgAygCECADKAIUEPoCIQEgA0GBATYCMCADIAE2AjQgAyAAKAIEKAIAuBDUAiIFNgI4IANBCGogBEEEaiADQTBqIANBNGogA0E4ahC8ASADKAIMIQACQCADKAIIQQFxDQAgAyAANgI8AkAgBUGEAUkNACAFEOkBIAMoAjQhAQsCQCABQYQBSQ0AIAEQ6QELAkAgAygCMCIBQYQBSQ0AIAEQ6QELQdCOwAAgA0E8ahB4AkAgAEGEAUkNACAAEOkBCwJAIAMoAhwiAUGEAUkNACABEOkBCwJAIAMoAiAiAUGEAUkNACABEOkBCwJAIAMoAiQiAUGEAUkNACABEOkBCwJAIAMoAigiAUGEAUkNACABEOkBCwJAIAMoAiwiAUGEAUkNACABEOkBCwJAIAJBhAFJDQAgAhDpAQsgA0HAAGokAA8LIAMgADYCPEGojcAAQSsgA0E8akGYjcAAQYSPwAAQkwEAC/kCAQV/IwBBIGsiASQAIABBADoAFAJAAkACQCAAKAIAQf////8HTw0AAkAgACgCECICRQ0AA0AgACgCAA0DIABBfzYCAAJAIAAoAhAiAw0AIABBADYCAAwCCyAAIANBf2o2AhAgACgCCCAAKAIMIgRBAnRqKAIAIQMgAEEANgIAIAAgBEEBaiIEQQAgACgCBCIFIAQgBUkbazYCDCABIAM2AgwgAygCCA0EIANBfzYCCAJAAkAgAygCDCIEDQBBACEFIAMhBAwBCyADQQA6ABwgAUEANgIYIAEgA0EUaiIFNgIUIAEgBTYCEAJAIAQgAUEQaiADKAIQKAIMEQgADQAgA0EMaiIEEMUBIARBADYCAAsgAygCCEEBaiEFIAEoAgwhBAsgAyAFNgIIIAQgBCgCAEF/aiIDNgIAAkAgAw0AIAFBDGoQmQELIAJBf2oiAg0ACwsgAUEgaiQADwtBgKLAABDBAQALQZCiwAAQwAEAC0H0pMAAEMABAAvSAgEDfyMAQRBrIgIkAAJAAkAgAUGAAUkNACACQQA2AgwCQAJAIAFBgBBJDQACQCABQYCABEkNACACQQxqQQNyIQMgAiABQRJ2QfABcjoADCACIAFBBnZBP3FBgAFyOgAOIAIgAUEMdkE/cUGAAXI6AA1BBCEEDAILIAJBDGpBAnIhAyACIAFBDHZB4AFyOgAMIAIgAUEGdkE/cUGAAXI6AA1BAyEEDAELIAJBDGpBAXIhAyACIAFBBnZBwAFyOgAMQQIhBAsgAyABQT9xQYABcjoAAAJAIAAoAgAgACgCCCIBayAETw0AIAAgASAEEHMgACgCCCEBCyAAKAIEIAFqIAJBDGogBBA+GiAAIAEgBGo2AggMAQsCQCAAKAIIIgQgACgCAEcNACAAQay1wAAQfgsgACAEQQFqNgIIIAAoAgQgBGogAToAAAsgAkEQaiQAQQAL1AIBAn8jAEEQayICJAACQAJAIAFBgAFJDQAgAkEANgIMAkACQCABQYAQSQ0AAkAgAUGAgARJDQAgAiABQT9xQYABcjoADyACIAFBEnZB8AFyOgAMIAIgAUEGdkE/cUGAAXI6AA4gAiABQQx2QT9xQYABcjoADUEEIQEMAgsgAiABQT9xQYABcjoADiACIAFBDHZB4AFyOgAMIAIgAUEGdkE/cUGAAXI6AA1BAyEBDAELIAIgAUE/cUGAAXI6AA0gAiABQQZ2QcABcjoADEECIQELAkAgACgCACAAKAIIIgNrIAFPDQAgACADIAFBAUEBEGggACgCCCEDCyAAKAIEIANqIAJBDGogARA+GiAAIAMgAWo2AggMAQsCQCAAKAIIIgMgACgCAEcNACAAQcyuwAAQfgsgACgCBCADaiABOgAAIAAgA0EBajYCCAsgAkEQaiQAQQALyAIBBH9BACECAkAgAUGAAkkNAEEfIQIgAUH///8HSw0AIAFBBiABQQh2ZyICa3ZBAXEgAkEBdGtBPmohAgsgAEIANwIQIAAgAjYCHCACQQJ0QaTVwABqIQMCQEEAKALA2EBBASACdCIEcQ0AIAMgADYCACAAIAM2AhggACAANgIMIAAgADYCCEEAQQAoAsDYQCAEcjYCwNhADwsCQAJAAkAgAygCACIEKAIEQXhxIAFHDQAgBCECDAELIAFBAEEZIAJBAXZrIAJBH0YbdCEDA0AgBCADQR12QQRxakEQaiIFKAIAIgJFDQIgA0EBdCEDIAIhBCACKAIEQXhxIAFHDQALCyACKAIIIgMgADYCDCACIAA2AgggAEEANgIYIAAgAjYCDCAAIAM2AggPCyAFIAA2AgAgACAENgIYIAAgADYCDCAAIAA2AggL6wIBBX8jAEHAAGsiBSQAQQEhBgJAIAAoAhwiByABIAIgACgCICIIKAIMIgkRCwANAAJAAkAgAC0AFEEEcQ0AQQEhBiAHQc23wABBASAJEQsADQIgAyAAIAQoAgwRCABFDQEMAgsgB0HOt8AAQQIgCRELAA0BQQEhBiAFQQE6ABcgBUEYakEIaiAAQQhqKQIANwMAIAVBGGpBEGogAEEQaikCADcDACAFQRhqQRhqIABBGGooAgA2AgAgBSAINgIMIAUgBzYCCCAFQay3wAA2AjggBSAAKQIANwMYIAUgBUEXajYCECAFIAVBCGo2AjQgAyAFQRhqIAQoAgwRCAANASAFKAI0Qci3wABBAiAFKAI4KAIMEQsADQELAkAgAg0AIAAtABRBBHENAEEBIQYgACgCHEHQt8AAQQEgACgCICgCDBELAA0BCyAAKAIcQby1wABBASAAKAIgKAIMEQsAIQYLIAVBwABqJAAgBgurAgIIfwJ+AkAgASgCBCICIAEoAgAiA0kNAAJAIAIgA2tBAWoiBA0AIAAgACgCCCAAKAIAIgFzIgUgACgCBCICczYCBCAAIAIgACgCDCIEcyIGQQt3NgIMIAAgBSACQQl0czYCCCAAIAYgAXM2AgAgASAEIAFqQQd3ag8LIAQgBGd0QX9qIQcgACgCCCEGIAAoAgQhAiAAKAIMIQUgACgCACEBIAStIQoDQCABIAZzIgggAkEJdHMhBiAFIAJzIQQgCCACcyECIAEgBWpBB3cgAWohCCAEQQt3IgkhBSAEIAFzIgQhASAHIAitIAp+IgunSQ0ACyAAIAk2AgwgACAENgIAIAAgBjYCCCAAIAI2AgQgAyALQiCIp2oPC0HXk8AAQRlByJTAABDcAQALqQICBH8BfiMAQSBrIgYkAEEAIQcCQAJAIAVFDQAgAiADaiIDIAJJDQBBACEHIAQgBWpBf2pBACAEa3GtIAMgASgCACIIQQF0IgIgAyACSxsiAkEIQQRBASAFQYEISRsgBUEBRhsiAyACIANLGyIDrX4iCkIgiKcNACAKpyIJQYCAgIB4IARrSw0BAkACQCAIDQAgBkEYaiEHQQAhBQwBCyAGQRxqIQcgBiAENgIYIAYgASgCBDYCFCAIIAVsIQULIAcgBTYCACAGQQhqIAQgCSAGQRRqIAUQhgECQCAGKAIIDQAgBigCDCEFIAEgAzYCACABIAU2AgRBgYCAgHghBwwBCyAGKAIQIQIgBigCDCEHDAELCyAAIAI2AgQgACAHNgIAIAZBIGokAAuhAgEFfwJAAkACQAJAIAJBA2pBfHEiBCACRg0AIAQgAmsiBCADIAQgA0kbIgRFDQBBACEFIAFB/wFxIQZBASEHA0AgAiAFai0AACAGRg0EIAQgBUEBaiIFRw0ACyAEIANBeGoiCEsNAgwBCyADQXhqIQhBACEECyABQf8BcUGBgoQIbCEFA0BBgIKECCACIARqIgYoAgAgBXMiB2sgB3JBgIKECCAGQQRqKAIAIAVzIgZrIAZycUGAgYKEeHFBgIGChHhHDQEgBEEIaiIEIAhNDQALCwJAIAQgA0YNACABQf8BcSEFQQEhBwNAAkAgAiAEai0AACAFRw0AIAQhBQwDCyADIARBAWoiBEcNAAsLQQAhBwsgACAFNgIEIAAgBzYCAAu6AgIDfwF+IwBBwABrIgIkAAJAIAEoAgBBgICAgHhHDQAgASgCDCEDIAJBHGpBCGoiBEEANgIAIAJCgICAgBA3AhwgAkEoakEIaiADKAIAIgNBCGopAgA3AwAgAkEoakEQaiADQRBqKQIANwMAIAIgAykCADcDKCACQRxqQbivwAAgAkEoahBDGiACQRBqQQhqIAQoAgAiAzYCACACIAIpAhwiBTcDECABQQhqIAM2AgAgASAFNwIACyABKQIAIQUgAUKAgICAEDcCACACQQhqIgMgAUEIaiIBKAIANgIAIAFBADYCAEEALQCB1UAaIAIgBTcDAAJAQQxBBBDiAiIBDQBBBEEMEIYDAAsgASACKQMANwIAIAFBCGogAygCADYCACAAQbCywAA2AgQgACABNgIAIAJBwABqJAALrAIBAn8jAEEwayICJAACQAJAAkAgACgCAA0AIAEoAhghAyABQQA2AhggA0UNASACQQRqIAMRAwAgACgCACIDDQIgAEEEaiEBAkAgA0UNACABQQRBBBCVAQsgAEEBNgIAIAEgAikCBDcCACABQQhqIAJBBGpBCGopAgA3AgAgAUEQaiACQQRqQRBqKAIANgIACyACQTBqJAAgAEEEag8LIAJBADYCKCACQQE2AhwgAkH4qsAANgIYIAJCBDcCICACQRhqQeCrwAAQgQIACyACQShqIAJBEGopAgA3AgAgAiACKQIINwIgIAIgAigCBDYCHCACQQE2AhggAkEYahCsAiACQQA2AiggAkEBNgIcIAJBgKzAADYCGCACQgQ3AiAgAkEYakGIrMAAEIECAAu4AgEBfwJAAkACQAJAAkACQAJAAkAgAC0ATUF9ag4EAAMBAgcLIAAoAlAiASABKAIAQX9qIgE2AgAgAQ0FIABB0ABqEJQBDAULIAAoAlAiASABKAIAQX9qIgE2AgAgAQ0CIABB0ABqEJQBDAILIAAoAlQQ3QEgACgCVEHsAEEEEO4CIAAoAlAiAUGEAUkNASABEOkBDAELIAAoAlAQ3QEgACgCUEHsAEEEEO4CDAELIABBPGoiARClAyABEP8CAkAgACgCOCIBQYQBSQ0AIAEQ6QELIABBLGoiARClAyABEP8CIABBIGoiARClAyABEP8CCwJAIAAoAhwiAUGEAUkNACABEOkBCyAALQBMRQ0AIAAoAhgiAUGEAUkNACABEOkBCyAAQQA6AEwgAEEMaiIAEKUDIAAQ/wILC7gCAQF/AkACQAJAAkACQAJAAkACQCAALQBNQX1qDgQAAwECBwsgACgCUCIBIAEoAgBBf2oiATYCACABDQUgAEHQAGoQlAEMBQsgACgCUCIBIAEoAgBBf2oiATYCACABDQIgAEHQAGoQlAEMAgsgACgCVBDeASAAKAJUQewAQQQQ7gIgACgCUCIBQYQBSQ0BIAEQ6QEMAQsgACgCUBDeASAAKAJQQewAQQQQ7gIMAQsgAEE8aiIBEKUDIAEQ/wICQCAAKAI4IgFBhAFJDQAgARDpAQsgAEEsaiIBEKUDIAEQ/wIgAEEgaiIBEKUDIAEQ/wILAkAgACgCHCIBQYQBSQ0AIAEQ6QELIAAtAExFDQAgACgCGCIBQYQBSQ0AIAEQ6QELIABBADoATCAAQQxqIgAQpQMgABD/AgsLqQIBA38jAEHAAGsiAyQAIANBHGogACgCACIEEMYBIANBEGogARDlASADKAIQQayEwAAQ+gIhASADQYEBNgIwIAMgATYCNCADIAAoAgQoAgC4ENQCIgU2AjggA0EIaiAEQQRqIANBMGogA0E0aiADQThqELwBIAMoAgwhAAJAIAMoAghBAXENACADIAA2AjwCQCAFQYQBSQ0AIAUQ6QEgAygCNCEBCwJAIAFBhAFJDQAgARDpAQsCQCADKAIwIgFBhAFJDQAgARDpAQtBrIfAACADQTxqEHgCQCAAQYQBSQ0AIAAQ6QELIANBHGoQrAECQCACQYQBSQ0AIAIQ6QELIANBwABqJAAPCyADIAA2AjxBiILAAEErIANBPGpB+IHAAEHkh8AAEJMBAAuFAgIEfwF+IwBBIGsiBSQAAkACQAJAIAEgAmoiAiABTw0AQQAhBgwBC0EAIQYCQCADIARqQX9qQQAgA2txrSACIAAoAgAiAUEBdCIHIAIgB0sbIgJBCEEEIARBAUYbIgcgAiAHSxsiB61+IglCIIinRQ0ADAELIAmnIghBgICAgHggA2tLDQBBACECAkAgAUUNACAFIAEgBGw2AhwgBSAAKAIENgIUIAMhAgsgBSACNgIYIAVBCGogAyAIIAVBFGoQhwEgBSgCCEEBRw0BIAUoAhAhAiAFKAIMIQYLIAYgAkGor8AAELUCAAsgBSgCDCEDIAAgBzYCACAAIAM2AgQgBUEgaiQAC5wCAwN/An4BfCMAQcAAayICJAAgAkGBATYCLCACQSBqIAFBEGogAkEsahDJASACKAIkIQECQAJAAkAgAigCICIDDQAgAkEQaiABEIsDIAJBMGogAigCECACKwMYEIoCAkACQCACKAIwQQFHDQAgAiACKwM4IgcQkAFCfyACQQhqKQMAQgAgB0QAAAAAAAAAAGYiAxsgB0T////////vR2QiBBshBUJ/IAIpAwBCACADGyAEGyEGDAELQgAhBkIAIQULIAAgBjcDACAAIAU3AwggAUGDAUsNAQwCCyAAQgA3AwggAEIANwMAIANBAXFFDQEgAUGEAUkNAQsgARDpAQsCQCACKAIsIgBBhAFJDQAgABDpAQsgAkHAAGokAAv8AQEGfyMAQRBrIgEkAAJAAkAgACgCDCICIAAoAggiA0cNACACIQQCQCACIAAoAgAiA0cNACACQYABIAJBgAFLGyIEIQbQbyAG/A8BIgNBf0YNAgJAAkAgACgCECIFDQAgACADNgIQDAELIAUgAmogA0cNAwsgAUEIaiAAIAIgBEEEQQQQayABKAIIQYGAgIB4Rw0CIAAoAgAhAyAAKAIIIQQLIAQgA08NASAAIARBAWoiAzYCCCAAKAIEIARBAnRqIAJBAWo2AgALIAIgA08NACAAIAAoAgQgAkECdGooAgA2AgwgACgCECEAIAFBEGokACAAIAJqDwsQogMAC/0BAgN/AX4jAEEgayIGJABBACEHAkACQCAFRQ0AIAIgA2oiAyACSQ0AQQAhByAEIAVqQX9qQQAgBGtxrSADrX4iCUIgiKcNACAJpyIIQYCAgIB4IARrSw0BAkACQCABKAIAIgINACAGQRhqIQdBACEFDAELIAZBHGohByAGIAQ2AhggBiABKAIENgIUIAIgBWwhBQsgByAFNgIAIAZBCGogBCAIIAZBFGogABCGAQJAIAYoAggNACAGKAIMIQcgASADNgIAIAEgBzYCBEGBgICAeCEHDAELIAYoAhAhAiAGKAIMIQcMAQsLIAAgAjYCBCAAIAc2AgAgBkEgaiQAC4YCAQh/AkACQAJAAkAgAQ0AQQAhAgwBCyABKAIAIQNBACECIAFBADYCACADQQFxDQELQQAhBEEAIQVBACEGQQAhB0EAIQEMAQsgAS8BHiEDIAEtAB0hAiABLQAcIQQgASgCGCEFIAEoAhQhCCABKAIQIQYgASgCDCEJIAEoAgghByABKAIEIQELIAAgAzsBHiAAIAI6AB0gACAEOgAcIAAgBTYCGCAAIAk2AgwgACAHNgIIIAAgATYCBCAAKAIUIQEgACAINgIUIAAoAhAhAyAAIAY2AhAgACgCACECIABBATYCACAAQQRqIQACQCACRQ0AIANFDQAgAUGEAUkNACABEOkBCyAAC9oBAQZ/IwBBEGsiASQAAkACQCAAKAIMIgINACAAKAIEIQAgAUEANgIMIAEgADYCCAwBCyAAKAIAIQMgACgCCCEEIAEgACgCBCIFNgIIIAFBACACIAMgBEEAIAMgBCADSRtrIgBrIgRrIgYgBiACSxs2AgwgAyAAIAJqIAIgBEsbIgIgAEYNACACIABrIQIgBSAAQQJ0aiEAA0AgACgCACIDIAMoAgBBf2oiAzYCAAJAIAMNACAAEJkBCyAAQQRqIQAgAkF/aiICDQALCyABQQhqEMcBIAFBEGokAAvqAQEFfyMAQRBrIgUkAEEAIQYgBUEMaiEHAkACQCAERQ0AIAEoAgAiCEUNACAFIAM2AgwgCCAEbCEGIAEoAgQhCSAFQQhqIQcMAQsLIAcgBjYCAAJAAkAgBSgCDCIGRQ0AIAUoAgghCAJAAkAgAg0AIAhFDQEgCSAIIAYQ7gIMAQsgBCACbCEHAkACQAJAIAQNACAIRQ0BIAkgCCAGEO4CDAELIAkgCCAGIAcQzQIhAwwBCyAGIQMLIANFDQILIAEgAjYCACABIAM2AgQLQYGAgIB4IQYLIAAgBzYCBCAAIAY2AgAgBUEQaiQAC+gBAQJ/IwBBMGsiAiQAAkACQCAAKAIAIgBBAEgNACACIAA2AgggAkEBNgIQIAJB3JrAADYCDCACQgE3AhggAkEbNgIoIAIgAkEkajYCFCACIAJBCGo2AiQgASgCHCABKAIgIAJBDGoQQyEBDAELIAIgABDSAQJAIAIoAgAiA0UNACABIAMgAigCBBDTAiEBDAELIAJBATYCECACQfSawAA2AgwgAkIBNwIYIAJBAzYCKCACIAA2AiwgAiACQSRqNgIUIAIgAkEsajYCJCABKAIcIAEoAiAgAkEMahBDIQELIAJBMGokACABC+YBAQJ/IwBBIGsiAiQAAkACQAJAIAAoAgBBAkcNACABKAIIIQMgAUEANgIIIANFDQEgAiADEQMAIAIoAgQhAyACKAIAIQECQCAAKAIAQQJHDQAgACADNgIEIAAgATYCAAwBCyACIAM2AgwgAiABNgIIIAFBAkcNAgsgAkEgaiQAIAAPCyACQQA2AhggAkEBNgIMIAJB4KfAADYCCCACQgQ3AhAgAkEIakHIqMAAEIECAAsgAkEIahCCAiACQQA2AhggAkEBNgIMIAJB6KjAADYCCCACQgQ3AhAgAkEIakHwqMAAEIECAAu+AQEGfwJAA0AgAiIERQ0BAkAgASAEaiIFQX9qIgIsAAAiBkF/Sg0AAkACQCAFQX5qIgItAAAiB8AiCEFASA0AIAdBH3EhBQwBCwJAAkAgBUF9aiICLQAAIgfAIglBQEgNACAHQQ9xIQUMAQsgBUF8aiICLQAAQQdxQQZ0IAlBP3FyIQULIAVBBnQgCEE/cXIhBQsgBUEGdCAGQT9xciEGCyACIAFrIQIgBiADRg0ACwsgACAENgIEIAAgATYCAAvbAQEEfyMAQcAAayIDJAAgAyABEEcCQAJAAkAgAygCACIEDQAgAEEANgIIIABCgICAgMAANwIADAELIAMoAgQhBSADQRhqQQRBAEEEQQgQgAEgAygCHCEGIAMoAhhBAUYNASADKAIgIgIgBTYCBCACIAQ2AgAgA0EMakEIaiIEQQE2AgAgAyACNgIQIAMgBjYCDCADQRhqIAFBKBA+GiADQQxqIANBGGogAxCPASAAQQhqIAQoAgA2AgAgACADKQIMNwIACyADQcAAaiQADwsgBiADKAIgIAIQtQIAC9ABAQN/IwBBIGsiAyQAAkACQAJAIAEgAmoiAiABTw0AQQAhBAwBC0EAIQQCQCACIAAoAgAiBUEBdCIBIAIgAUsbIgFBCCABQQhLGyIBQQBODQAMAQtBACECAkAgBUUNACADIAU2AhwgAyAAKAIENgIUQQEhAgsgAyACNgIYIANBCGpBASABIANBFGoQiAEgAygCCEEBRw0BIAMoAhAhACADKAIMIQQLIAQgAEHgs8AAELUCAAsgAygCDCECIAAgATYCACAAIAI2AgQgA0EgaiQAC+EBAAJAIABBIE8NAEEADwsCQCAAQf8ATw0AQQEPCwJAIABBgIAESQ0AAkAgAEGAgAhJDQAgAEHg//8AcUHgzQpHIABB/v//AHFBnvAKR3EgAEHAkXVqQXpJcSAAQdDidGpBcklxIABBkKh0akFxSXEgAEGAkHRqQd5sSXEgAEGAgHRqQZ50SXEgAEGw2XNqQXtJcSAAQYD+R2pBsMVUSXEgAEHwgzhJcQ8LIABBqL/AAEEsQYDAwABB0AFB0MHAAEHmAxBXDwsgAEG2xcAAQShBhsbAAEGiAkGoyMAAQakCEFcL5AECBX8BfiMAQRBrIgJBCGohAwNAIANCADcDACACQgA3AwAgAUKV+Kn6l7fem55/fCEBIAIhBEEQIQUDQCAEIAFCHoggAYVCucuT59Htkay/f34iB0IbiCAHhULro8SZsbeS6JR/fiIHQh+IIAeFNwAAIAFClfip+pe33puef3whASAEIAVBCCAFQQhJGyIGaiEEIAUgBmsiBQ0AC0EAIQUDQAJAIAVBEEcNAEIAIQEMAgsgAiAFaiEEIAVBAWohBSAELQAARQ0ACwsgACACKQMANwAAIABBCGogAkEIaikDADcAAAvNAQICfwF+IwBBIGsiAiQAAkACQCABRQ0AIAFBeGoiAygCAEEBRw0BIAJBGGogAUEQaikCADcCACACQRBqIAFBCGopAgA3AgAgASkCACEEIANBADYCACACIAQ3AggCQCADQX9GDQAgAUF8aiIBIAEoAgBBf2oiATYCACABDQAgA0EgQQQQ7gILIAAgAikCDDcCACAAQRBqIAJBHGooAgA2AgAgAEEIaiACQQRqQRBqKQIANwIAIAJBIGokAA8LEIADAAtB+IXAAEE/EP4CAAvRAQEFfyMAQSBrIgEkACABQRxqQQA6AAAgAUIANwIUIAFBBDYCECABQgA3AgggAUEIahC7ASECEO8CIgMQxwIQnwMiBBCWAyEFAkAgBEGEAUkNACAEEOkBCwJAIANBhAFJDQAgAxDpAQsgAUGAATYCCCABQQhqEI4DIQQgAiACKAIAQQFqIgM2AgACQCADRQ0AIABBBGogAhDZASAAIAVBAUY6ABQgACAENgIQIAAgAjYCAAJAIAEoAggiAkGEAUkNACACEOkBCyABQSBqJAAPCwAL7AECAn8BfCMAQSBrIgIkAAJAAkBBACAAKAIAEQQAIgBFDQAgACgCAA0BIABBfzYCACACIAEoAgAQiwMgAkEQaiACKAIAIAIrAwgQigICQCACKAIQQQFHDQAgAEEBNgIEIAIrAxgiBEQAAAAAAADgwWYhAQJAAkAgBJlEAAAAAAAA4EFjRQ0AIASqIQMMAQtBgICAgHghAwsgAEEAQf////8HIANBgICAgHggARsgBEQAAMD////fQWQbIAQgBGIbNgIICyAAIAAoAgBBAWo2AgAgAkEgaiQADwtBjJPAABC/AQALQaCXwAAQwAEAC84BAgV/AX4jAEEwayICJAAgAkEQaiIDIAAgABBkIgBBEGoiBCgCADYCACACQQhqIgUgAEEIaiIGKQIANwMAIARBADYCACAGQgA3AgAgACkCACEHIABCgICAgMAANwIAIAIgBzcDACACIAEoAgAQzwEgAkEYakEQaiAEKAIANgIAIAJBGGpBCGogBikCADcDACAAKQIAIQcgACACKQMANwIAIAYgBSkDADcCACAEIAMoAgA2AgAgAiAHNwMYIAJBGGpBBEEEEJUBIAJBMGokAAvLAQICfwF+IwBBIGsiAyQAAkAgACgCAA0AIABBfzYCACADQRhqIABBJGopAgA3AwAgA0EQaiAAQRxqKQIANwMAIAApAhQhBSAAQQA2AhQgAyAFNwMIIANBCGoQqgICQCAAKAIEQQJGDQAgACgCCCIEQYQBSQ0AIAQQ6QELIAAgAjYCCCAAIAE2AgQgACgCDCECIABBADYCDCAAIAAoAgBBAWo2AgACQCACRQ0AIAAoAhAgAigCBBEDAAsgA0EgaiQADwtBvKPAABDAAQALzAECA38BfiMAQTBrIgIkAAJAIAEoAgBBgICAgHhHDQAgASgCDCEDIAJBDGpBCGoiBEEANgIAIAJCgICAgBA3AgwgAkEYakEIaiADKAIAIgNBCGopAgA3AwAgAkEYakEQaiADQRBqKQIANwMAIAIgAykCADcDGCACQQxqQbivwAAgAkEYahBDGiACQQhqIAQoAgAiAzYCACACIAIpAgwiBTcDACABQQhqIAM2AgAgASAFNwIACyAAQbCywAA2AgQgACABNgIAIAJBMGokAAvIAQIGfwF+IwBBMGsiASQAIAFBEGoiAiAAIAAQZCIAQRBqIgMoAgA2AgAgAUEIaiIEIABBCGoiBSkCADcDACADQQA2AgAgBUIANwIAIAEgACkCADcDACAAQoCAgIDAADcCACABEGohBiABQRhqQRBqIAMoAgA2AgAgAUEYakEIaiAFKQIANwMAIAApAgAhByAAIAEpAwA3AgAgBSAEKQMANwIAIAMgAigCADYCACABIAc3AxggAUEYakEEQQQQlQEgAUEwaiQAIAYL0QEBAn8jAEEgayIFJAACQAJAQQEQ0wFB/wFxIgZBAkYNACAGQQFxRQ0BIAVBCGogACABKAIYEQcAAAtBACgClNVAIgZBf0wNAEEAIAZBAWo2ApTVQAJAQQAoApjVQEUNACAFIAAgASgCFBEHACAFIAQ6AB0gBSADOgAcIAUgAjYCGCAFIAUpAwA3AhBBACgCmNVAIAVBEGpBACgCnNVAKAIUEQcAQQAoApTVQEF/aiEGC0EAIAY2ApTVQEEAQQA6AOzYQCADRQ0AIAAgARDzAgsAC7YBAQR/IwBBIGsiAiQAAkAgACgCACIDQQFqIgQgA0EBdCIFIAQgBUsbIgRBCCAEQQhLGyIEQQBODQBBAEEAIAEQtQIAC0EAIQUCQCADRQ0AIAIgAzYCHCACIAAoAgQ2AhRBASEFCyACIAU2AhggAkEIakEBIAQgAkEUahCIAQJAIAIoAghBAUcNACACKAIMIAIoAhAgARC1AgALIAIoAgwhAyAAIAQ2AgAgACADNgIEIAJBIGokAAvGAQECfyMAQSBrIgEkACAAKAIAIQIgAEEANgIAAkACQCACRQ0AIAEgACgCBDYCFCABQYEBNgIYIAFBCGogAUEUaiABQRhqEMkBIAEoAgwhACABKAIIQQFxDQECQCAAQYQBSQ0AIAAQ6QEgASgCGCIAQYQBSQ0AIAAQ6QELAkAgASgCFCIAQYQBSQ0AIAAQ6QELIAFBIGokAA8LQcCEwABBHBD+AgALIAEgADYCHEGIgsAAQSsgAUEcakH4gcAAQfSHwAAQkwEAC7kBAQF+AkACQCADIARqQX9qQQAgA2txrSABrX4iBUIgiKcNACAFpyIEQYCAgIB4IANrSw0AAkAgBA0AIAAgAzYCCEEAIQMgAEEANgIEDAILQQAtAIHVQBoCQAJAIAINACAEIAMQ4gIhAgwBCyAEIAMQ4wIhAgsCQCACRQ0AIAAgAjYCCCAAIAE2AgRBACEDDAILIAAgBDYCCCAAIAM2AgRBASEDDAELIABBADYCBEEBIQMLIAAgAzYCAAvFAQECfyMAQSBrIgIkAAJAAkACQCAAKAIADQAgASgCCCEDIAFBADYCCCADRQ0BIAIgACADEQEAENsBIAIgAigCBDYCDCACIAIoAgAiATYCCCABDQILIAJBIGokACAAQQRqDwsgAkEANgIYIAJBATYCDCACQeCnwAA2AgggAkIENwIQIAJBCGpByKjAABCBAgALIAJBCGoQnQIgAkEANgIYIAJBATYCDCACQeiowAA2AgggAkIENwIQIAJBCGpB8KjAABCBAgALwwEBBX8jAEEQayIDJAACQCABKAIAIgEoAggNACABKAIMIQQgAUL/////LzcCCCABKAIQIQVBACEGAkAgBEECRw0AIANBCGogAigCACIGKAIEIAYoAgAoAgARBwAgAygCDCEGIAMoAgghAgJAIAEoAhQiB0UNACABKAIYIAcoAgwRAwALIAEgBjYCGCABIAI2AhQgASgCCEEBaiEGCyABIAY2AgggACAFNgIEIAAgBDYCACADQRBqJAAPC0HMo8AAEMABAAu7AQEBfyMAQSBrIgEkAAJAAkAgACgCAEUNACABIAAoAgQ2AhQgAUGBATYCGCABQQhqIAFBFGogAUEYahDJASABKAIMIQAgASgCCEEBcQ0BAkAgAEGEAUkNACAAEOkBIAEoAhgiAEGEAUkNACAAEOkBCwJAIAEoAhQiAEGEAUkNACAAEOkBCyABQSBqJAAPC0HAhMAAQRwQ/gIACyABIAA2AhxBiILAAEErIAFBHGpB+IHAAEH0h8AAEJMBAAu6AQEDfyMAQRBrIgIkACABKAIMIQMCQAJAAkACQAJAAkAgASgCBA4CAAECCyADDQFBASEEQQAhAQwCCyADDQAgASgCACIDKAIEIQEgAygCACEEDAELIAAgARBSDAELIAJBBGogAUEAQQFBARCAASACKAIIIQMgAigCBEEBRg0BIAIoAgwgBCABED4hBCAAIAE2AgggACAENgIEIAAgAzYCAAsgAkEQaiQADwsgAyACKAIMQYCDwAAQtQIAC7oBAQN/IwBBEGsiAiQAIAEoAgwhAwJAAkACQAJAAkACQCABKAIEDgIAAQILIAMNAUEBIQRBACEBDAILIAMNACABKAIAIgMoAgQhASADKAIAIQQMAQsgACABEFIMAQsgAkEEaiABQQBBAUEBEIABIAIoAgghAyACKAIEQQFGDQEgAigCDCAEIAEQPiEEIAAgATYCCCAAIAQ2AgQgACADNgIACyACQRBqJAAPCyADIAIoAgxBoI7AABC1AgALrwEBAX8CQAJAAkACQAJAIAJBAEgNAAJAAkAgAygCBEUNAAJAIAMoAggiBQ0AIAJFDQRBAC0AgdVAGgwCCyADKAIAIAUgASACEM0CIQMMBAsgAkUNAkEALQCB1UAaCyACIAEQ4gIhAwwCCyAAQQA2AgQMAgsgASEDCwJAIANFDQAgACACNgIIIAAgAzYCBEEAIQIMAgsgACACNgIIIAAgATYCBAtBASECCyAAIAI2AgALsQEBAX8CQAJAIAJBAEgNAAJAAkACQCADKAIERQ0AAkAgAygCCCIEDQACQCACDQAgASEDDAQLQQAtAIHVQBoMAgsgAygCACAEIAEgAhDNAiEDDAILAkAgAg0AIAEhAwwCC0EALQCB1UAaCyACIAEQ4gIhAwsCQCADRQ0AIAAgAjYCCCAAIAM2AgQgAEEANgIADwsgACACNgIIIAAgATYCBAwBCyAAQQA2AgQLIABBATYCAAuxAQEBfwJAAkAgAkEASA0AAkACQAJAIAMoAgRFDQACQCADKAIIIgQNAAJAIAINACABIQMMBAtBAC0AgdVAGgwCCyADKAIAIAQgASACEM0CIQMMAgsCQCACDQAgASEDDAILQQAtAIHVQBoLIAIgARDiAiEDCwJAIANFDQAgACACNgIIIAAgAzYCBCAAQQA2AgAPCyAAIAI2AgggACABNgIEDAELIABBADYCBAsgAEEBNgIAC5MBAQR/IAAoAgAhAiAAIAEQzQECQCAAKAIIIgEgAiAAKAIMIgNrTQ0AIAAoAgAhBAJAAkAgAiABayIFIAMgBWsiA00NACAEIAJrIANPDQELIAAoAgQiAiAEIAVrIgNBAnRqIAIgAUECdGogBUECdBCJAxogACADNgIIDwsgACgCBCIAIAJBAnRqIAAgA0ECdBA+GgsLsgEBA38jAEEQayIBJAAgACgCACICKAIMIQMCQAJAAkACQCACKAIEDgIAAQILIAMNAUEBIQJBACEDDAILIAMNACACKAIAIgIoAgQhAyACKAIAIQIMAQsgAUGAgICAeDYCACABIAA2AgwgAUHsssAAIAAoAgQgACgCCCIALQAIIAAtAAkQfQALIAEgAzYCBCABIAI2AgAgAUHQssAAIAAoAgQgACgCCCIALQAIIAAtAAkQfQALpQEBA38jAEEgayICJAAgAkIANwIIIAJBAToAHCACQQhqELsBIgMgAygCAEEBaiIENgIAAkACQCAERQ0AIAMoAggNASADQX82AgggA0EMahDFASADIANBCGo2AhggA0HkpMAANgIUIAMgATYCECADIAA2AgwgA0EANgIIQejTwABB6NPAABBMGkHo08AAIAMQlwEgAkEgaiQADwsAC0HUpMAAEMABAAubAQEBfyMAQcAAayICJAAgAkIANwM4IAJBOGogACgCABCMAyACIAIoAjwiADYCNCACIAIoAjg2AjAgAiAANgIsIAJBPzYCKCACQQI2AhAgAkG8qsAANgIMIAJCATcCGCACIAJBLGo2AiQgAiACQSRqNgIUIAEoAhwgASgCICACQQxqEEMhASACQSxqQQFBARCVASACQcAAaiQAIAELlAEBBH8jAEEQayICJABBASEDAkAgASgCHCIEQScgASgCICIFKAIQIgERCAANACACQQRqIAAoAgBBgQIQPAJAAkAgAi0ABEGAAUcNACAEIAIoAgggAREIAEUNAQwCCyAEIAJBBGogAi0ADiIAaiACLQAPIABrIAUoAgwRCwANAQsgBEEnIAERCAAhAwsgAkEQaiQAIAMLmAEBAX8CQCAAKAIAIgAoAgwiAUGEAUkNACABEOkBCwJAIAAoAhAiAUGEAUkNACABEOkBCwJAIAAoAhQiAUGEAUkNACABEOkBCwJAIAAoAhgiAUGEAUkNACABEOkBCwJAIAAoAhwiAUGEAUkNACABEOkBCwJAIABBf0YNACAAIAAoAgRBf2oiATYCBCABDQAgAEEgQQQQ7gILC4wBAQV/IwBBEGsiAyQAIANBCGogARBHAkAgAygCCCIERQ0AIAMoAgwhBQNAAkAgACgCCCIGIAAoAgBHDQAgACAGQQFBBEEIENEBCyAAKAIEIAZBA3RqIgcgBTYCBCAHIAQ2AgAgACAGQQFqNgIIIAMgARBHIAMoAgQhBSADKAIAIgQNAAsLIANBEGokAAukAQIBfwN+IwBBEGsiAiQAQgAhA0IAIQQCQCABvSIFQoCAgICAgID4P1QNAAJAIAVCgICAgICAgPjHAFQNAEJ/QgAgBUKBgICAgICA+P8AVBsiAyEEDAELIAJCACAFQguGQoCAgICAgICAgH+EQf4AIAVCNIina0H/AHEQqwEgAkEIaikDACEEIAIpAwAhAwsgACADNwMAIAAgBDcDCCACQRBqJAALiAEBAX8jAEEgayICJAACQAJAIAAoAgBBgICAgHhGDQAgASAAKAIEIAAoAggQ0wIhAAwBCyACQQhqQQhqIAAoAgwoAgAiAEEIaikCADcDACACQQhqQRBqIABBEGopAgA3AwAgAiAAKQIANwMIIAEoAhwgASgCICACQQhqEEMhAAsgAkEgaiQAIAALbgEGfiAAIANC/////w+DIgUgAUL/////D4MiBn4iByADQiCIIgggBn4iBiAFIAFCIIgiCX58IgVCIIZ8Igo3AwAgACAIIAl+IAUgBlStQiCGIAVCIIiEfCAKIAdUrXwgBCABfiADIAJ+fHw3AwgLfQEBfyMAQcAAayIFJAAgBSABNgIMIAUgADYCCCAFIAM2AhQgBSACNgIQIAVBAjYCHCAFQZy3wAA2AhggBUICNwIkIAVB2QCtQiCGIAVBEGqthDcDOCAFQdoArUIghiAFQQhqrYQ3AzAgBSAFQTBqNgIgIAVBGGogBBCBAgALgwEBAX8CQCAAKAIAIgAoAgxBAkYNACAAKAIQIgFBhAFJDQAgARDpAQsCQCAAKAIUIgFFDQAgACgCGCABKAIMEQMACwJAIAAoAhxFDQAgAEEcahDPAiAAQShqEM8CCwJAIABBf0YNACAAIAAoAgRBf2oiATYCBCABDQAgAEE0QQQQ7gILC3kBBH8jAEEQayIDJABBACEEIANBDGohBQJAAkAgAkUNACAAKAIAIgZFDQAgAyABNgIMIAYgAmwhBCAAKAIEIQIgA0EIaiEFDAELCyAFIAQ2AgACQCADKAIMIgRFDQAgAygCCCIFRQ0AIAIgBSAEEO4CCyADQRBqJAALfgEBfwJAIAMoAhAiBA0AQeCZwAAQ8gEACyAAIAI2AgQgACABNgIAIABBADYCHCAAIAMpAgA3AgggAEEQaiADQQhqKQIANwIAIABBGGogA0EQaigCADYCACAAIAIgAWtBAnYiAjYCJCAAIAMoAgQgBG4iAyACIAMgAkkbNgIgC4UBAQF/AkAgACgCACICKAIIDQAgAkF/NgIIIAJBDGogAUGwosAAELIBIAItABwhASACQQE6ABwgAiACKAIIQQFqNgIIAkAgAQ0AAkAgAC0AFA0AIABBEGogAEEEahDxAiICQYQBSQ0BIAIQ6QEPCyAAQQRqEJkDEJUDCw8LQaCiwAAQwAEAC4MBAQF/AkACQEEAIAAoAgARBAAiAEUNACAAKAIADQEgAEEBOgAZIABBfzYCACABKAIAEOsCIQECQCAAKAIMRQ0AIAAoAhAiAkGEAUkNACACEOkBCyAAIAE2AhAgAEEBNgIMIAAgACgCAEEBajYCAA8LQYyTwAAQvwEAC0Hgl8AAEMABAAt+AQN/AkAgACgCACIAKAIMIgFFDQACQCAAKAIQIgIoAgAiA0UNACABIAMRAwALAkAgAigCBCIDRQ0AIAEgAyACKAIIEO4CCyAAKAIYIAAoAhQoAgwRAwALAkAgAEF/Rg0AIAAgACgCBEF/aiIBNgIEIAENACAAQSBBBBDuAgsLdAEBfyMAQSBrIgYkAAJAIAENAEGAqsAAQTIQ/gIACyAGQRRqIAEgAyAEIAUgAigCEBEPACAGQQhqIAZBFGpB8KnAABCeASAGIAYoAgggBigCDBDlAiAGKAIEIQEgACAGKAIANgIAIAAgATYCBCAGQSBqJAALZQEBfyMAQSBrIgIkAAJAAkACQCABDQAgAkEMaiAAEHYgAkEMahCsAQwBCyAARQ0BIAIgAEF4aiIANgIMIAAgACgCAEF/aiIBNgIAIAENACACQQxqEI4BCyACQSBqJAAPCxCAAwALdwEBfyMAQYABayICJAACQAJAIAFBARAxDQBBrIfAABC0AUUNACACIAEQxgFBAC0AgdVAGkGEAUEEEOICIgFFDQEgASACQYABED4iAUEAOgCAASABQYCAwAAQiwELIABBADYCACACQYABaiQADwtBBEGEARCGAwALbgECfyMAQRBrIgMkAAJAAkAgASgCACABKAIIIgRNDQAgA0EIaiABIARBAUEBEG4gAygCCCIEQYGAgIB4Rw0BIAEoAgghBAsgACAENgIEIAAgASgCBDYCACADQRBqJAAPCyAEIAMoAgwgAhC1AgALbgECfyMAQRBrIgMkAAJAAkAgASgCACABKAIIIgRNDQAgA0EIaiABIARBBEEEEG4gAygCCCIEQYGAgIB4Rw0BIAEoAgghBAsgACAENgIEIAAgASgCBDYCACADQRBqJAAPCyAEIAMoAgwgAhC1AgALaQIBfwF+IwBBMGsiAyQAIAMgATYCBCADIAA2AgAgA0ECNgIMIANBiLfAADYCCCADQgI3AhQgA0EDrUIghiIEIAOthDcDKCADIAQgA0EEaq2ENwMgIAMgA0EgajYCECADQQhqIAIQgQIAC3gBAn8gAC0ABCIBIQICQCAALQAFRQ0AQQEhAgJAIAFBAXENAAJAIAAoAgAiAi0AFEEEcQ0AIAIoAhxBy7fAAEECIAIoAiAoAgwRCwAhAgwBCyACKAIcQcq3wABBASACKAIgKAIMEQsAIQILIAAgAjoABAsgAkEBcQtpAgF/AX4jAEEwayIDJAAgAyABNgIEIAMgADYCACADQQI2AgwgA0GgzMAANgIIIANCAjcCFCADQQOtQiCGIgQgA0EEaq2ENwMoIAMgBCADrYQ3AyAgAyADQSBqNgIQIANBCGogAhCBAgALaQIBfwF+IwBBMGsiAyQAIAMgATYCBCADIAA2AgAgA0ECNgIMIANB1MzAADYCCCADQgI3AhQgA0EDrUIghiIEIANBBGqthDcDKCADIAQgA62ENwMgIAMgA0EgajYCECADQQhqIAIQgQIAC3UBAn8CQAJAQQAgACgCABEEACIARQ0AIAAoAgAiAUH/////B08NASAAIAFBAWo2AgBBgQEhAgJAIAAoAgxBAUcNACAAKAIQEOsCIQIgACgCAEF/aiEBCyAAIAE2AgAgAg8LQYyTwAAQvwEAC0GgmMAAEMEBAAtqAQF/IwBBMGsiAiQAAkBBAC0AgNVARQ0AIAJBAjYCDCACQfixwAA2AgggAkIBNwIUIAIgATYCLCACQQOtQiCGIAJBLGqthDcDICACIAJBIGo2AhAgAkEIakGgssAAEIECAAsgAkEwaiQAC2oBAn8jAEEQayIDJAAgA0EEaiACQQBBAUEBEIABIAMoAgghBAJAIAMoAgRBAUcNACAEIAMoAgxBgIPAABC1AgALIAMoAgwgASACED4hASAAIAI2AgggACABNgIEIAAgBDYCACADQRBqJAALagECfyMAQRBrIgMkACADQQRqIAJBAEEBQQEQgAEgAygCCCEEAkAgAygCBEEBRw0AIAQgAygCDEGgjsAAELUCAAsgAygCDCABIAIQPiEBIAAgAjYCCCAAIAE2AgQgACAENgIAIANBEGokAAtmAQJ/IwBBEGsiAiQAIAAoAgAhAyAAQQA2AgACQCADRQ0AIAIgAzYCDCADQQhqQQAgARB6IAMgAygCAEF/aiIANgIAAkAgAA0AIAJBDGoQlAELIAJBEGokAA8LQfigwABBHBD+AgALZgECfyMAQRBrIgIkACAAKAIAIQMgAEEANgIAAkAgA0UNACACIAM2AgwgA0EIakEBIAEQeiADIAMoAgBBf2oiADYCAAJAIAANACACQQxqEJQBCyACQRBqJAAPC0H4oMAAQRwQ/gIAC2IBAn8CQAJAAkAgAUUNACABQXhqIgIgAigCAEEBaiIDNgIAIANFDQEgASgCACIDQX9GDQIgACACNgIIIAAgATYCBCAAIAFBBGo2AgAgASADQQFqNgIADwsQgAMLAAsQgQMAC2wBAn8jAEEQayIBJAAgAC0AFCECIABBAToAFCABIABBeGoiADYCDAJAAkAgAg0AQejTwABB6NPAABBMGkHo08AAIAAQlwEMAQsgACAAKAIAQX9qIgI2AgAgAg0AIAFBDGoQmQELIAFBEGokAAtXAQF+AkACQCADQcAAcQ0AIANFDQEgAkEAIANrQT9xrYYgASADQT9xrSIEiIQhASACIASIIQIMAQsgAiADQT9xrYghAUIAIQILIAAgATcDACAAIAI3AwgLbQEBfwJAIAAoAgAiAUGEAUkNACABEOkBCwJAIAAoAgQiAUGEAUkNACABEOkBCwJAIAAoAggiAUGEAUkNACABEOkBCwJAIAAoAgwiAUGEAUkNACABEOkBCwJAIAAoAhAiAEGEAUkNACAAEOkBCwtjAQF/IAEoAgghAyABKAIEIQECQCACRQ0AAkACQCADIAJLDQAgAyACRw0BDAILIAEgAmosAABBv39KDQELIAEgAyACIANBpInAABDZAgALIAAgAyACazYCBCAAIAEgAmo2AgALYwEBfyABKAIIIQMgASgCBCEBAkAgAkUNAAJAAkAgAyACSw0AIAMgAkcNAQwCCyABIAJqLAAAQb9/Sg0BCyABIAMgAiADQbSQwAAQ2QIACyAAIAMgAms2AgQgACABIAJqNgIAC2IBAn8CQAJAIABBfGooAgAiA0F4cSIEQQRBCCADQQNxIgMbIAFqSQ0AAkAgA0UNACAEIAFBJ2pLDQILIAAQQQ8LQfmvwABBLkGosMAAENwBAAtBuLDAAEEuQeiwwAAQ3AEAC10BAX8jAEEQayICJAACQCAAKAIAIgBFDQAgAiAANgIMIABBCGpBACABEHogACAAKAIAQX9qIgE2AgACQCABDQAgAkEMahCUAQsgAkEQaiQADwtB+KDAAEEcEP4CAAtdAQF/IwBBEGsiAiQAAkAgACgCACIARQ0AIAIgADYCDCAAQQhqQQEgARB6IAAgACgCAEF/aiIBNgIAAkAgAQ0AIAJBDGoQlAELIAJBEGokAA8LQfigwABBHBD+AgALXQECfwJAIAAoAgwiAyAAKAIAIgRHDQAgACACEIkBIAAoAgAhBCAAKAIMIQMLIAAoAgQgACgCCCADaiIDQQAgBCADIARJG2tBAnRqIAE2AgAgACAAKAIMQQFqNgIMC2IBAX8jAEEQayIEJAAgASgCACACKAIAIAMoAgAQ3QIhAiAEQQhqEIUCAkACQCAEKAIIDQBBACEDIAAgAkEARzoAAQwBCyAAIAQoAgw2AgRBASEDCyAAIAM6AAAgBEEQaiQAC2cBAX8CQAJAQQAgACgCABEEACIARQ0AIAAoAgANAUEAIQECQCAALQAYDQBBACEBIAAtABkNACAAQQA2AhRBASEBIABBAToAGAsgAEEANgIAIAEPC0GMk8AAEL8BAAtBgJfAABDAAQALWwEBfyABKAIEIQMCQCACRQ0AAkACQCABKAIIIgEgAksNACABIAJHDQEMAgsgAyACaiwAAEG/f0oNAQsgAyABQQAgAkGUicAAENkCAAsgACACNgIEIAAgAzYCAAtbAQF/IAEoAgQhAwJAIAJFDQACQAJAIAEoAggiASACSw0AIAEgAkcNAQwCCyADIAJqLAAAQb9/Sg0BCyADIAFBACACQaSQwAAQ2QIACyAAIAI2AgQgACADNgIAC1UBAX8jAEEQayICJAAgAiAAKAIAIgA2AgwgAEEIahBbAkAgAUGEAUkNACABEOkBCyAAIAAoAgBBf2oiATYCAAJAIAENACACQQxqENoBCyACQRBqJAALWQEBfyMAQRBrIgIkACACQQhqIAEoAgAQigMCQAJAIAIoAggiAUUNACACIAEgAigCDBDWASAAIAIoAgAgAigCBBC+AgwBCyAAQYCAgIB4NgIACyACQRBqJAALWQEBfyMAQRBrIgIkACACQQhqIAEoAgAQigMCQAJAIAIoAggiAUUNACACIAEgAigCDBDWASAAIAIoAgAgAigCBBC+AgwBCyAAQYCAgIB4NgIACyACQRBqJAALWQEBf0EALQCB1UAaAkBBIEEEEOICIgENAEEEQSAQhgMACyABQoGAgIAQNwIAIAEgACkCADcCCCABQRBqIABBCGopAgA3AgAgAUEYaiAAQRBqKQIANwIAIAELWQEBf0EALQCB1UAaAkBBIEEEEOICIgENAEEEQSAQhgMACyABQoGAgIAQNwIAIAEgACkCADcCCCABQRBqIABBCGopAgA3AgAgAUEYaiAAQRBqKQIANwIAIAELVAEBfyMAQRBrIgUkACABKAIAIAIoAgAgAygCACAEKAIAEI0CIQQgBUEIahCFAiAFKAIMIQMgACAFKAIIIgI2AgAgACADIAQgAhs2AgQgBUEQaiQAC1ABAX8jAEEgayIFJAAgBSAENgIcIAUgAzYCGCAFIAI2AhQgBSABNgIQIAUgADYCDCAFQQA2AgggBUEIahC6AUEIahCgAyEEIAVBIGokACAEC04BAX8CQCAAKAIAIAAoAggiBGsgAiABayICTw0AIAAgBCACQQFBARDRASAAKAIIIQQLIAAoAgQgBGogASACED4aIAAgACgCCCACajYCCAtNAQF/IwBBMGsiASQAIAFBATYCDCABQcyxwAA2AgggAUIBNwIUIAFBwQCtQiCGIAFBL2qthDcDICABIAFBIGo2AhAgAUEIaiAAEIECAAtNAQF/IwBBMGsiASQAIAFBATYCDCABQfy1wAA2AgggAUIBNwIUIAFB1wCtQiCGIAFBL2qthDcDICABIAFBIGo2AhAgAUEIaiAAEIECAAtNAQF/IwBBMGsiASQAIAFBATYCDCABQaC2wAA2AgggAUIBNwIUIAFB2ACtQiCGIAFBL2qthDcDICABIAFBIGo2AhAgAUEIaiAAEIECAAtKAQN/QQAhAwJAIAJFDQACQANAIAAtAAAiBCABLQAAIgVHDQEgAEEBaiEAIAFBAWohASACQX9qIgJFDQIMAAsLIAQgBWshAwsgAwtPAQF/IwBBEGsiBCQAIAEoAgAgAigCACADKAIAEJ8CIQMgBEEIahCFAiAEKAIMIQIgACAEKAIIIgE2AgAgACACIAMgARs2AgQgBEEQaiQAC1MBAX8CQAJAQQAgASgCABEEACIBRQ0AIAEoAgANASABIAEoAhRBAWoiAjYCFCAAIAI2AgQgACACQQVJOgAADwtBjJPAABC/AQALQfCXwAAQwAEAC1MBA38CQCAAKAIAIgFFDQACQCAAKAIEIgIoAgAiA0UNACABIAMRAwALAkAgAigCBCIDRQ0AIAEgAyACKAIIEO4CCyAAKAIMIAAoAggoAgwRAwALC1UBBH8gASgCABDrAiECIAEoAgQQ6wIhAyABKAIIEOsCIQQgASgCDBDrAiEFIAAgASgCEBDrAjYCECAAIAU2AgwgACAENgIIIAAgAzYCBCAAIAI2AgALSwECfwJAIAAoAgQiAUUNACAAKAIAIQADQCAAKAIAIgIgAigCAEF/aiICNgIAAkAgAg0AIAAQmQELIABBBGohACABQX9qIgENAAsLC04BAX8CQCAALQAUQQJGDQAgACgCACIBIAEoAgBBf2oiATYCAAJAIAENACAAENoBCwJAIAAoAhAiAUGEAUkNACABEOkBCyAAQQRqEM8CCwtKAQJ/IwBBEGsiAyQAIAEoAgAgAigCABCwAiECIANBCGoQhQIgAygCDCEBIAAgAygCCCIENgIAIAAgASACIAQbNgIEIANBEGokAAtHAQF/AkAgACgCACAAKAIIIgNrIAJPDQAgACADIAJBAUEBEGggACgCCCEDCyAAKAIEIANqIAEgAhA+GiAAIAMgAmo2AghBAAtPAQJ/IAAoAgQhAiAAKAIAIQMCQCAAKAIIIgAtAABFDQAgA0HEt8AAQQQgAigCDBELAEUNAEEBDwsgACABQQpGOgAAIAMgASACKAIQEQgAC04BAX8gAC0AFCEBIABBAToAFAJAAkAgAQ0AIABBeGoiACAAKAIAQQFqIgE2AgAgAUUNAUHo08AAQejTwAAQTBpB6NPAACAAEJcBCw8LAAtIAQF/IwBBEGsiAiQAIAJBCGogACAAKAIAQQFBBEEEEGECQCACKAIIIgBBgYCAgHhGDQAgACACKAIMIAEQtQIACyACQRBqJAALTwEDfxChAyICEMsCIgMQzAIhBAJAIANBhAFJDQAgAxDpAQsgBCAAKAIAIAEQ7AICQCAEQYQBSQ0AIAQQ6QELAkAgAkGEAUkNACACEOkBCwtHAQF/AkACQCABIAAoAhAiAkkNACABIAJrIgEgACgCCEkNAQsQogMACyAAKAIMIQIgACABNgIMIAAoAgQgAUECdGogAjYCAAtAAAJAAkAgAUEJSQ0AIAEgABBRIQEMAQsgABAvIQELAkAgAUUNACABQXxqLQAAQQNxRQ0AIAFBACAAEE0aCyABC0gBAX8jAEEQayIFJAAgBUEIaiAAIAEgAiADIAQQYQJAIAUoAggiBEGBgICAeEYNACAEIAUoAgxBrJLAABC1AgALIAVBEGokAAtNAQF/AkACQCABQYCAgIB4cyIBQQ5NDQBBACEBDAELIAFBAnQiAkGUoMAAaigCACEBIAJB2J/AAGooAgAhAgsgACACNgIEIAAgATYCAAtSAQJ/QQAhAUEAQQAoAqDVQCICQQFqNgKg1UACQCACQQBIDQBBASEBQQAtAOzYQA0AQQAgADoA7NhAQQBBACgC6NhAQQFqNgLo2EBBAiEBCyABC08BAn9BAC0AgdVAGiABKAIEIQIgASgCACEDAkBBCEEEEOICIgENAEEEQQgQhgMACyABIAI2AgQgASADNgIAIABBwLLAADYCBCAAIAE2AgALQwEBfwJAIAAoAgAgACgCCCIDayACTw0AIAAgAyACEHMgACgCCCEDCyAAKAIEIANqIAEgAhA+GiAAIAMgAmo2AghBAAtFAQF/IwBBIGsiAyQAIAMgAjYCHCADIAE2AhggAyACNgIUIANBCGogA0EUakHwrcAAEJ0BIAAgAykDCDcDACADQSBqJAALRwEBfwJAAkBBACAAKAIAEQQAIgBFDQAgACgCAA0BIAAgACgCFEEBaiIBNgIUIAFBBUkPC0GMk8AAEL8BAAtB0JfAABDAAQALPgECfyMAQRBrIgEkACAAQXhqIgAgACgCAEF/aiICNgIAIAEgADYCDAJAIAINACABQQxqEJkBCyABQRBqJAALTQEBf0EALQCB1UAaAkBBBEEEEOICIgINAEEEQQQQhgMACyACIAE2AgAgAkG4pcAAEP0CIQEgAEG4pcAANgIEIAAgAjYCACAAIAE2AggLQQEBfyAAKAIAIgBBDGoiARBtIAFBBEEEEJUBAkAgAEF/Rg0AIAAgACgCBEF/aiIBNgIEIAENACAAQSBBBBDuAgsLRgECfwJAIAEoAgAiAw0AAkAgA0UNACABKAIEIgRBhAFJDQAgBBDpAQsgASACNgIEIAFBATYCAAsgACACNgIEIAAgAzYCAAtCAQF/IwBBIGsiAyQAIANBADYCECADQQE2AgQgA0IENwIIIAMgATYCHCADIAA2AhggAyADQRhqNgIAIAMgAhCBAgALQgEBfwJAAkACQCAALQAQQX1qDgIAAgELIAAoAhQiASABKAIAQX9qIgE2AgAgAQ0AIABBFGoQlAELDwsgAEEUahBlC0IBAX8CQAJAAkAgAC0AEEF9ag4CAAIBCyAAKAIUIgEgASgCAEF/aiIBNgIAIAENACAAQRRqEJQBCw8LIABBFGoQZgs9AQF/IAAgARCMAiECAkAgAUUNACACRQ0AAkAgAEUNAEEALQCB1UAaIAAgARDiAiIBRQ0BCyABDwsQmwMACzoBAX8jAEEgayICJAAgAkEUaiABEKkBIAJBCGogAigCFBCcASACQRRqEOsBIABCADcDACACQSBqJAALQwACQAJAQQAgACgCABEEACIARQ0AIAAoAgBB/////wdPDQEgAC0AGUF/c0EBcQ8LQYyTwAAQvwEAC0Gwl8AAEMEBAAtBAQF/QQEhAgJAAkAgASgCACIBIAEQcCIBKAIAQQFGDQBBACECDAELIAEoAgQQ6wIhAQsgACABNgIEIAAgAjYCAAs5AQF/IAJBEHZAACEDIABBADYCCCAAQQAgAkGAgHxxIANBf0YiAhs2AgQgAEEAIANBEHQgAhs2AgALPgEBfxCaAyIFIAAmASAFEJoDIgUgASYBIAUQmgMiBSACJgEgBRCaAyIFIAMmASAFEJoDIgUgBCYBIAUQvQELQQEBf0EALQCB1UAaAkBBCEEEEOICIgINAEEEQQgQhgMACyACIAE2AgQgAkEBNgIAIABBrITAADYCBCAAIAI2AgALOQEBfyMAQRBrIgEkACABQQRqIAAQqQFBrIfAABDuASEAIAFBBGoQ6wEgABCgAyEAIAFBEGokACAACzkBAX8jAEEQayIBJAAgAUEEaiAAEKkBQayHwAAQowEhACABQQRqEOsBIAAQoAMhACABQRBqJAAgAAs9AQJ/IAAoAgAhAQJAIAAoAgQiACgCACICRQ0AIAEgAhEDAAsCQCAAKAIEIgJFDQAgASACIAAoAggQ7gILCzgBAX8jAEEQayIBJAAgASAANgIMAkAgAEGEAUkNACAA0G8mAUHA1MAAIAFBDGoQeQsgAUEQaiQACzkAAkAgAkGAgMQARg0AIAAgAiABKAIQEQgARQ0AQQEPCwJAIAMNAEEADwsgACADIAQgASgCDBELAAs5AQF/IAAoAgQiASABKAIAQX9qNgIAIAAoAggiASABKAIAQX9qIgE2AgACQCABDQAgAEEIahCOAQsLOwEBf0EALQCB1UAaAkBBNEEEEOICIgENAEEEQTQQhgMACyABQoGAgIAQNwIAIAFBCGogAEEsED4aIAELNgEBfwJAIABFDQACQCABKAIAIgJFDQAgACACEQMACyABKAIEIgJFDQAgACACIAEoAggQ7gILCz0AAkACQEEAIAAoAgARBAAiAEUNACAAKAIAQf////8HTw0BIAAtABkPC0GMk8AAEL8BAAtBkJjAABDBAQALMgEBfyMAQRBrIgIkAAJAIABFDQAgAiABNgIMIAIgADYCCCACQQhqEOgBCyACQRBqJAALOQECfxChAyICEMsCIgMgACABELICIQECQCACQYQBSQ0AIAIQ6QELAkAgA0GEAUkNACADEOkBCyABCzcBAX8jAEEgayIBJAAgAUEANgIYIAFBATYCDCABQbyzwAA2AgggAUIENwIQIAFBCGogABCBAgALNwEBfyMAQSBrIgEkACABQQA2AhggAUEBNgIMIAFBuMvAADYCCCABQgQ3AhAgAUEIaiAAEIECAAs3AQF/IwBBIGsiASQAIAFBADYCGCABQQE2AgwgAUHky8AANgIIIAFCBDcCECABQQhqIAAQgQIACzgAAkACQEEAIAAoAgARBAAiAEUNACAAKAIADQEgAEEANgIEDwtBjJPAABC/AQALQcCXwAAQwAEACzgAAkACQEEAIAAoAgARBAAiAEUNACAAKAIADQEgAEEBOgAZDwtBjJPAABC/AQALQYCYwAAQwAEACzQBAX8jAEEQayICJAAgAiAANgIMIAFB6JTAAEERIAJBDGpB2JTAABBfIQAgAkEQaiQAIAALMgEBfyMAQRBrIgIkACACIAE2AgwgAiAANgIIQcyfwAAgAkEIahBYIQEgAkEQaiQAIAELOgEBf0EALQCB1UAaAkBBBEEEEOICIgINAEEEQQQQhgMACyACIAE2AgAgAEHQoMAANgIEIAAgAjYCAAs6AQF/QQAtAIHVQBoCQEEEQQQQ4gIiAg0AQQRBBBCGAwALIAIgATYCACAAQeSgwAA2AgQgACACNgIACzIBAX8gASADEIwCIQQCQAJAIANFDQAgBEUNACAAIAEgAyACEM0CIgMNAQsQmwMACyADCzEBAX8jAEEQayICJAAgAkEIaiABQfiwwABBCxCDAiACQQhqEKABIQEgAkEQaiQAIAELLAACQCABRQ0AIAFBAnQhAQNAIAAoAgAQ6QEgAEEEaiEAIAFBfGoiAQ0ACwsLMQEBfyABQXhqIgIgAigCAEEBaiICNgIAAkAgAg0AAAsgACABNgIEIABB5KTAADYCAAspAQF/IwBBEGsiAyQAIAMgACkCADcCCCADQQhqIAEgAhBnIANBEGokAAspAQF/IwBBEGsiAyQAIAMgACkCADcCCCADQQhqIAEgAhBaIANBEGokAAstAgF/AX4jAEEQayIBJAAgACkCACECIAEgADYCDCABIAI3AgQgAUEEahCQAwALKwEBfyMAQRBrIgIkACACQQE7AQwgAiABNgIIIAIgADYCBCACQQRqEIACAAsqAQF/AkAgACgCACIBQQJGDQAgAUUNACAAKAIEIgBBhAFJDQAgABDpAQsLLQAgASgCHCACIAMgASgCICgCDBELACEDIABBADoABSAAIAM6AAQgACABNgIACykBAn8CQCAAKAIAIgFFDQAgASABKAIAQX9qIgI2AgAgAg0AIAAQlAELCysBAX5BACkDiNVAIQFBAEIANwOI1UAgACABQiCIPgIEIAAgAadBAUY2AgALIgEBfyAAKAIAIgAgAEEfdSICcyACayAAQX9zQR92IAEQVAsoAQF/QQAtAIHVQBoCQEHsAEEEEOICIgBFDQAgAA8LQQRB7AAQhgMACygBAX9BAC0AgdVAGgJAQewAQQQQ4gIiAEUNACAADwtBBEHsABCGAwALJQEBfyMAQRBrIgEkACABIAAQ4AEgASgCACABKAIEIAFBEGokAAsmAQF+AkACQCABDQBCACEDDAELIAAgAjkDCEIBIQMLIAAgAzcDAAsmAAJAIAANAEGAqsAAQTIQ/gIACyAAIAIgAyAEIAUgASgCEBEQAAsjAQF/QQAhAgJAIAFpQQFHDQBBgICAgHggAWsgAE8hAgsgAgslAgF/AW8gACUBIAElASACJQEgAyUBEBghBRCaAyIEIAUmASAECyQBAX8gACgCACIBIAEoAgBBf2oiATYCAAJAIAENACAAENoBCwskAAJAIAANAEGAqsAAQTIQ/gIACyAAIAIgAyAEIAEoAhARDgALJAACQCAADQBBgKrAAEEyEP4CAAsgACACIAMgBCABKAIQEQ0ACyQAAkAgAA0AQYCqwABBMhD+AgALIAAgAiADIAQgASgCEBENAAskAAJAIAANAEGAqsAAQTIQ/gIACyAAIAIgAyAEIAEoAhARIQALJAACQCAADQBBgKrAAEEyEP4CAAsgACACIAMgBCABKAIQERwACyQAAkAgAA0AQYCqwABBMhD+AgALIAAgAiADIAQgASgCEBEOAAskAAJAIAANAEGAqsAAQTIQ/gIACyAAIAIgAyAEIAEoAhARHwALJAACQCAADQBBgKrAAEEyEP4CAAsgACACIAMgBCABKAIQEQ0ACyABAX9BACEEAkAgASADRw0AIAAgAiABEMIBRSEECyAECyoBAX8CQCAAKAIAIgFBgICAgHhyQYCAgIB4Rg0AIAAoAgQgAUEBEO4CCwsfAAJAIAAoAgBFDQAgACgCBCIAQYQBSQ0AIAAQ6QELCyABAX8gACABKAIQIgM2AgQgACABKAIAIAMgAmxqNgIACyMAAkAgAA0AQYSlwABBMhD+AgALIAAgAiABKAIQEQcAEKQDCyMAAkAgAA0AQYSlwABBMhD+AgALIAAgAiABKAIQEQcAEKQDCx8AAkAgACgCAEUNACAAKAIEIgBBhAFJDQAgABDpAQsLIgACQCAADQBBgKrAAEEyEP4CAAsgACACIAMgASgCEBEKAAshAgF/AW8gACUBIAElASACJQEQFyEEEJoDIgMgBCYBIAMLIQIBfwFvIAAlASABJQEgAiUBEB0hBBCaAyIDIAQmASADCyEBAX8gACABEJoDIgQgAiYBIAQQmgMiBCADJgEgBBCeAgslAAJAAkACQCAALQCAAQ4EAQICAAILIABBFGoQ3QELIAAQrAELCyEAAkAgAA0AQauRwABBMhD+AgALIAAgASgCEBEDABCkAwshAAJAIAANAEGrkcAAQTIQ/gIACyAAIAEoAhARAwAQpAMLHAAgACgCAEEIahBbAkAgAUGEAUkNACABEOkBCwsgAAJAIAANAEGAqsAAQTIQ/gIACyAAIAIgASgCEBEIAAskAQF/QeTUwAAhAQJAQQAoAuDUQA0AQeDUwAAgABBsIQELIAELJAEBf0Hk1MAAIQECQEEAKALg1EANAEHg1MAAIAAQbCEBCyABCyAAAkBBACgC3NNAQQNHDQBB3NPAACAAED0aC0Hc08AACxoAAkAgACgCAEUNACAAEM8CIABBDGoQzwILCxwBAX8gARCWAyECIAAgATYCBCAAIAJBAUc2AgALGQACQCAAKAIARQ0AIABBBGpBBEEEEJUBCwsdAQF/AkAgACgCACIBRQ0AIAAoAgQgAUEBEO4CCwsXAAJAIAFBCUkNACABIAAQUQ8LIAAQLwsdAQF/AkAgACgCACIBRQ0AIAAoAgQgAUEBEO4CCwsdAgF/AW8gACUBIAElARAVIQMQmgMiAiADJgEgAgsdAgF/AW8gACUBIAElARAcIQMQmgMiAiADJgEgAgsdAgF/AW8gACUBIAEgAhAjIQQQmgMiAyAEJgEgAwsdAgF/AW8gACUBIAEgAhAnIQQQmgMiAyAEJgEgAwsbAQF/IAEgAEEAKAKQ1UAiAkHCACACGxEHAAALFwACQCAADQAgAhDxAQALIAAgARCGAwALGwIBfwFvIAAgASACECwhBBCaAyIDIAQmASADCxsCAX8BbyAAIAEgAhAtIQQQmgMiAyAEJgEgAwsXAAJAIAAoAgAiAEGEAUkNACAAEOkBCwsXAAJAIAAoAgAiAEGEAUkNACAAEOkBCwsXAQF/IAAQHiIBNgIEIAAgAUEARzYCAAsXAQF/IAAQHyIBNgIEIAAgAUEARzYCAAsXAQF/IAAQICIBNgIEIAAgAUEARzYCAAsXAQF/IAAQISIBNgIEIAAgAUEARzYCAAsXACAAIAI2AgggACABNgIEIAAgAjYCAAscACAAQQA2AhAgAEIANwIIIABCgICAgMAANwIACxUAIAApAwAgAEEIaikDAEEBIAEQRgsZAgF/AW8gACABEAEhAxCaAyICIAMmASACCxkCAX8BbyAAJQEQByECEJoDIgEgAiYBIAELGQIBfwFvIAAlARAJIQIQmgMiASACJgEgAQsZAgF/AW8gACUBEAohAhCaAyIBIAImASABCxkCAX8BbyAAJQEQCyECEJoDIgEgAiYBIAELGQIBfwFvIAAlARANIQIQmgMiASACJgEgAQsZAgF/AW8gACUBEBEhAhCaAyIBIAImASABCxkCAX8BbyAAIAEQFCEDEJoDIgIgAyYBIAILGQIBfwFvIAAgARAaIQMQmgMiAiADJgEgAgsZAgF/AW8gACUBEBshAhCaAyIBIAImASABCxkCAX8BbyAAJQEQIiECEJoDIgEgAiYBIAELGQIBfwFvIAAlARAkIQIQmgMiASACJgEgAQsTAQF/IAAgASACIAMQOCEEIAQPCxMAIABCADcAACAAQQhqQgA3AAALFQACQCAAKAIIEPkCRQ0AIAAQ6AELCxIAQQAgAK1CIIZCAYQ3A4jVQAsZACABKAIcQc+1wABBCyABKAIgKAIMEQsACxkAIAEoAhxB2rXAAEEOIAEoAiAoAgwRCwALFgAgACgCHCABIAIgACgCICgCDBELAAsXAgF/AW8gABAGIQIQmgMiASACJgEgAQsXAgF/AW8gABAmIQIQmgMiASACJgEgAQsWAQF/IAAgARCaAyIDIAImASADEJwCCxQAIAAoAgAgASgCCCACKAIIEKACCxQAIAAoAgAgASAAKAIEKAIMEQgACw8AIAAgASACIAMgBBA2AAsVAgF/AW8QDCEBEJoDIgAgASYBIAALEQAgACUBIAElASABEOkBEA4LFQIBfwFvEBYhARCaAyIAIAEmASAACxAAIAAlASABJQEgAiUBECgLFQIBfwFvECshARCaAyIAIAEmASAACxEAIAAoAgQgACgCCCABEIgDCxEAIAAoAgQgACgCCCABEIgDCxEAIAAoAgAgACgCBCABEIgDCxABAX8gACABEK4CIQIgAg8LEAEBfyAAIAEQ0AEhAiACDwsRACAAKAIEIAAoAgggARCIAwsQACAAIAI2AgQgACABNgIACyAAIABC4+DWofail51WNwMIIABC0Jamw5Le7cA3NwMACyIAIABC7bqtts2F1PXjADcDCCAAQviCmb2V7sbFuX83AwALEwAgAEHAssAANgIEIAAgATYCAAsRACABIAAoAgAgACgCBBDTAgsQACABIAAoAgAgACgCBBA6CxEBAX8QmgMiASAAJQEmASABCw4AIAAlASABJQEgAhAlCxIBAX8gABDnASIBJQEgARDpAQsMACAAIAEgAhCvAQ8LFQBBhNTAAEGE1MAAEIEBKAIAEOsCCw0AIAAoAgAQlwNBAEcLDwAgACgCACABKAIIELECCw4AIAAoAgAgASACELMCCwsAIAAgARCjAxoACwwAIAAgASACEKEBAAsMACAAIAEgAhCiAQALDQAgACgCAEEBIAEQVAsPAEGotsAAQSsgABDcAQALDAAgACUBIAElARAPCw0AIAAlASAAEOkBEBMLCwAgACABQRQQtgILCgAgACABELQCDwsNACABQZCZwABBAhA6CwsAIAAgAUEnELcCCwkAIAAgARAqAAsLACAAQQFBARCVAQsNAEGFrcAAQRsQ/gIACw4AQaCtwABBzwAQ/gIACw0AIABBuK/AACABEEMLDAAgACABKQIANwMACw0AIABBkLPAACABEEMLDgAgAUGIs8AAQQUQ0wILCgAgASAAEPsCAAsNACAAQay3wAAgARBDCwoAIAIgACABEDoLCgAgACABIAIQNQsKACAAIAElARAACwoAIAAgASUBEAMLCgAgACABJQEQKQsJACAAIAEQyQILCgAgACgCABDKAgsJACAAQQA2AgALCAAgABCKAQALCAAgACUBEAILCAAgACUBEAQLCAAgACUBEAULCAAgACUBEAgLCAAgACUBEBALCAAgACUBEBILCAAgACUBEBkLBwAgABDVAgsHACAAKAIICwkAQcDUwAAQfAsGABCiAwALBwAQLhCdAwsFABCkAwsFABDcAgsEACAACwQAIAALBQAQ3gILAwAACwMAAAsCAAsCAAsL7lQCAEGAgMAAC9tTBQAAAIQAAAAEAAAABgAAAC9ydXN0Yy8wNWY5ODQ2Zjg5M2IwOWExYmUxZmM4NTYwZTMzZmMzYzgxNWNmZWNiL2xpYnJhcnkvY29yZS9zcmMvc3RyL3BhdHRlcm4ucnMAEAAQAE8AAADhBQAAFAAAABAAEABPAAAA4QUAACEAAAAQABAATwAAANUFAAAhAAAAL3J1c3RjLzA1Zjk4NDZmODkzYjA5YTFiZTFmYzg1NjBlMzNmYzNjODE1Y2ZlY2IvbGlicmFyeS9jb3JlL3NyYy9pdGVyL3RyYWl0cy9pdGVyYXRvci5yc5AAEABYAAAAswcAAAkAAAAHAAAABAAAAAQAAAAIAAAAY2FsbGVkIGBSZXN1bHQ6OnVud3JhcCgpYCBvbiBhbiBgRXJyYCB2YWx1ZS9ydXN0Yy8wNWY5ODQ2Zjg5M2IwOWExYmUxZmM4NTYwZTMzZmMzYzgxNWNmZWNiL2xpYnJhcnkvYWxsb2Mvc3JjL3NsaWNlLnJzAAAAMwEQAEoAAACiAAAAGQAAAC9ydXN0Yy8wNWY5ODQ2Zjg5M2IwOWExYmUxZmM4NTYwZTMzZmMzYzgxNWNmZWNiL2xpYnJhcnkvYWxsb2Mvc3JjL3N0cmluZy5ycwCQARAASwAAAOoBAAAXAAAAkAEQAEsAAACOBQAAGwAAAJABEABLAAAAjQUAABsAAAAQABAATwAAAGUEAAAkAAAAEAAQAE8AAADNAQAANwAAAAkAAAAIAAAABAAAAAoAAAALAAAARm5PbmNlIGNhbGxlZCBtb3JlIHRoYW4gb25jZXNyYy9saWIucnMAAFwCEAAKAAAALAAAABYAAABBQkNERUZHSElKS0xNTk9QUVJTVFVWV1hZWmFiY2RlZmdoaWprbG1ub3BxcnN0dXZ3eHl6MDEyMzQ1Njc4OSsvXAIQAAoAAABYAAAAFgAAAFwCEAAKAAAAawAAABQAAABcAhAACgAAAG4AAAAUAAAAXAIQAAoAAABxAAAAFAAAAGF0dGVtcHRlZCB0byB0YWtlIG93bmVyc2hpcCBvZiBSdXN0IHZhbHVlIHdoaWxlIGl0IHdhcyBib3Jyb3dlZDAxTjBjVHVAM3kzJEZAYzMAOQMQAA4AAAA6AAAAAQAAAAAAAABQAxAAAQAAAFADEAABAAAAXAIQAAoAAAA0AQAAIAAAAFwCEAAKAAAAOwEAACoAAABibTlqZEhWaFgyVjVaWE5mYzJWamRYSmxYM04wWVhSbAwAAABcAhAACgAAAAsCAAAvAAAAAAAAAAgAAAAEAAAADQAAAA4AAABcAhAACgAAABwCAABGAAAAXAIQAAoAAABPAgAADwAAAFwCEAAKAAAARAIAAC8AAAAvYjUwZWJiMTg0OGMzNTA2YS5qcwQEEAAUAAAAP189AAEAAAAAAAAAIAQQAAMAAABcAhAACgAAAHACAABZAAAARmFpbGVkIHRvIGNvbnZlcnQgcmVzcG9uc2UgdG8gc3RyaW5nSW52YWxpZCByZXNwb25zZSBmb3JtYXQ6IG1pc3Npbmcgc2VwYXJhdG9yAABcAhAACgAAANoCAAArAAAAXAIQAAoAAADbAgAAKQAAAGh0dHBzOi8vc3AueWluaGFpeXVuLmNvbS9wYXRjaC9zODAxMzE2NTNmYjc2YTk2ZS5qcwC0BBAAMwAAAFByZWZpeCB2YWxpZGF0aW9uIGZhaWxlZDogAADwBBAAGgAAAHN1Y2Nlc3MAXAIQAAoAAACNAwAALwAAAFwCEAAKAAAAkgMAAB4AAABU57G75Z6L5YmN57yA5b+F6aG76Lef6ZqPMi0xMDDkuYvpl7TnmoTlgbbmlbBG57G75Z6L5YmN57yA5b+F6aG76Lef6ZqPMS05OeS5i+mXtOeahOWlh+aVsOWJjee8gOexu+Wei+mUmeivr+WJjee8gOaVsOWtl+agvOW8j+mUmeivr+WJjee8gOexu+Wei+W/hemhu+aYr1TmiJZG5YmN57yA5qC85byP6ZSZ6K+vbWV0aG9kR0VUQ29udGVudC1UeXBldGV4dC9wbGFpbmhlYWRlcnNQT1NUYm9keS9ydXN0Yy8wNWY5ODQ2Zjg5M2IwOWExYmUxZmM4NTYwZTMzZmMzYzgxNWNmZWNiL2xpYnJhcnkvY29yZS9zcmMvc3RyL3BhdHRlcm4ucnMZBhAATwAAAOEFAAAUAAAAGQYQAE8AAADhBQAAIQAAABkGEABPAAAA1QUAACEAAAAQAAAABAAAAAQAAAAIAAAAY2FsbGVkIGBSZXN1bHQ6OnVud3JhcCgpYCBvbiBhbiBgRXJyYCB2YWx1ZS9ydXN0Yy8wNWY5ODQ2Zjg5M2IwOWExYmUxZmM4NTYwZTMzZmMzYzgxNWNmZWNiL2xpYnJhcnkvYWxsb2Mvc3JjL3NsaWNlLnJzAAAA0wYQAEoAAACiAAAAGQAAABkGEABPAAAAZQQAACQAAAAZBhAATwAAAM0BAAA3AAAAEQAAAAAAAAAIAAAABAAAABIAAAATAAAAc3JjL2xpYi5ycwAAaAcQAAoAAAAcAgAARgAAAGgHEAAKAAAATwIAAA8AAAAvYjUwZWJiMTg0OGMzNTA2YS5qc5QHEAAUAAAAP189AAEAAAAAAAAAsAcQAAMAAABoBxAACgAAAHACAABZAAAARmFpbGVkIHRvIGNvbnZlcnQgcmVzcG9uc2UgdG8gc3RyaW5nSW52YWxpZCByZXNwb25zZSBmb3JtYXQ6IG1pc3Npbmcgc2VwYXJhdG9yAABoBxAACgAAANoCAAArAAAAaAcQAAoAAADbAgAAKQAAAGh0dHBzOi8vc3AueWluaGFpeXVuLmNvbS9wYXRjaC9zODAxMzE2NTNmYjc2YTk2ZS5qcwBECBAAMwAAAFByZWZpeCB2YWxpZGF0aW9uIGZhaWxlZDogAACACBAAGgAAAHN1Y2Nlc3NjbG9zdXJlIGludm9rZWQgcmVjdXJzaXZlbHkgb3IgYWZ0ZXIgYmVpbmcgZHJvcHBlZC9ydXN0Yy8wNWY5ODQ2Zjg5M2IwOWExYmUxZmM4NTYwZTMzZmMzYzgxNWNmZWNiL2xpYnJhcnkvYWxsb2Mvc3JjL3Jhd192ZWMucnMAAADdCBAATAAAACoCAAARAAAAL3J1c3RjLzA1Zjk4NDZmODkzYjA5YTFiZTFmYzg1NjBlMzNmYzNjODE1Y2ZlY2IvbGlicmFyeS9zdGQvc3JjL3RocmVhZC9sb2NhbC5ycwA8CRAATwAAABQBAAAZAAAAAAAAAAAAAAABAAAAGQAAAGNhbGxlZCBgUmVzdWx0Ojp1bndyYXAoKWAgb24gYW4gYEVycmAgdmFsdWVjYW5ub3Qgc2FtcGxlIGVtcHR5IHJhbmdlL1VzZXJzL3hpZXBhbi8uY2FyZ28vcmVnaXN0cnkvc3JjL2luZGV4LmNyYXRlcy5pby0xOTQ5Y2Y4YzZiNWI1NTdmL3JhbmQtMC44LjUvc3JjL3JuZy5yc/AJEABYAAAAhgAAAAkAAAAAAAAABAAAAAQAAAAaAAAAVHJ5RnJvbVNsaWNlRXJyb3Jmcm9tX2VudHJvcHkgZmFpbGVkOiAAAHkKEAAVAAAAL1VzZXJzL3hpZXBhbi8uY2FyZ28vcmVnaXN0cnkvc3JjL2luZGV4LmNyYXRlcy5pby0xOTQ5Y2Y4YzZiNWI1NTdmL3JhbmRfY29yZS0wLjYuNC9zcmMvbGliLnJzAAAAmAoQAF0AAACfAQAADQAAAC9Vc2Vycy94aWVwYW4vLmNhcmdvL3JlZ2lzdHJ5L3NyYy9pbmRleC5jcmF0ZXMuaW8tMTk0OWNmOGM2YjViNTU3Zi9yYW5kX2NvcmUtMC42LjQvc3JjL2xlLnJzCAsQAFwAAAAVAAAANAAAAHNyYy9saWIucnMAAHQLEAAKAAAA7AEAACMAAAB0CxAACgAAACMCAAAjAAAAdAsQAAoAAABTAgAAJwAAAHQLEAAKAAAAcwIAAB8AAAB0CxAACgAAAH0CAAAjAAAAdAsQAAoAAAC+AgAAJwAAAHQLEAAKAAAAPwMAACsAAAB0CxAACgAAAFYDAAArAAAAdAsQAAoAAABxAwAAJwAAAHQLEAAKAAAAQgQAAB8AAAB0CxAACgAAAFIEAAAfAAAAL3J1c3RjLzA1Zjk4NDZmODkzYjA5YTFiZTFmYzg1NjBlMzNmYzNjODE1Y2ZlY2IvbGlicmFyeS9jb3JlL3NyYy9zdHIvcGF0dGVybi5ycwAwDBAATwAAAM0BAAA3AAAAKCkvcnVzdGMvMDVmOTg0NmY4OTNiMDlhMWJlMWZjODU2MGUzM2ZjM2M4MTVjZmVjYi9saWJyYXJ5L2NvcmUvc3JjL3NsaWNlL2l0ZXIucnOSDBAATgAAAIMHAAARAAAAL3J1c3RjLzA1Zjk4NDZmODkzYjA5YTFiZTFmYzg1NjBlMzNmYzNjODE1Y2ZlY2IvbGlicmFyeS9zdGQvc3JjL3RocmVhZC9sb2NhbC5ycwDwDBAATwAAABQBAAAZAAAAT1MgRXJyb3I6IAAAUA0QAAoAAABVbmtub3duIEVycm9yOiAAZA0QAA8AAABnZXRyYW5kb206IHRoaXMgdGFyZ2V0IGlzIG5vdCBzdXBwb3J0ZWRlcnJubzogZGlkIG5vdCByZXR1cm4gYSBwb3NpdGl2ZSB2YWx1ZXVuZXhwZWN0ZWQgc2l0dWF0aW9uU2VjUmFuZG9tQ29weUJ5dGVzOiBpT1MgU2VjdXJpdHkgZnJhbWV3b3JrIGZhaWx1cmVSdGxHZW5SYW5kb206IFdpbmRvd3Mgc3lzdGVtIGZ1bmN0aW9uIGZhaWx1cmVSRFJBTkQ6IGZhaWxlZCBtdWx0aXBsZSB0aW1lczogQ1BVIGlzc3VlIGxpa2VseVJEUkFORDogaW5zdHJ1Y3Rpb24gbm90IHN1cHBvcnRlZFdlYiBDcnlwdG8gQVBJIGlzIHVuYXZhaWxhYmxlQ2FsbGluZyBXZWIgQVBJIGNyeXB0by5nZXRSYW5kb21WYWx1ZXMgZmFpbGVkcmFuZFNlY3VyZTogVnhXb3JrcyBSTkcgbW9kdWxlIGlzIG5vdCBpbml0aWFsaXplZE5vZGUuanMgY3J5cHRvIENvbW1vbkpTIG1vZHVsZSBpcyB1bmF2YWlsYWJsZUNhbGxpbmcgTm9kZS5qcyBBUEkgY3J5cHRvLnJhbmRvbUZpbGxTeW5jIGZhaWxlZE5vZGUuanMgRVMgbW9kdWxlcyBhcmUgbm90IGRpcmVjdGx5IHN1cHBvcnRlZCwgc2VlIGh0dHBzOi8vZG9jcy5ycy9nZXRyYW5kb20jbm9kZWpzLWVzLW1vZHVsZS1zdXBwb3J0AAAAHAAAAGNyeXB0bwAAJwAAACYAAAAUAAAAMgAAAC0AAAAvAAAAIQAAAB0AAAAtAAAAAAAAAAAAAAAxAAAALQAAADAAAABlAAAAfA0QAKMNEADJDRAA3Q0QAA8OEAA8DhAAaw4QAIwOEACpDhAAAAAAAAAAAADWDhAABw8QADQPEABkDxAAHQAAAAQAAAAEAAAAHgAAAB8AAAAdAAAABAAAAAQAAAAgAAAAIQAAAEZuT25jZSBjYWxsZWQgbW9yZSB0aGFuIG9uY2UvVXNlcnMveGllcGFuLy5jYXJnby9yZWdpc3RyeS9zcmMvaW5kZXguY3JhdGVzLmlvLTE5NDljZjhjNmI1YjU1N2Yvd2FzbS1iaW5kZ2VuLWZ1dHVyZXMtMC40LjUwL3NyYy9xdWV1ZS5ycwCUEBAAawAAACUAAAAuAAAAlBAQAGsAAAAoAAAAKQAAAJQQEABrAAAAPgAAABoAAACUEBAAawAAAD4AAAAnAAAAL1VzZXJzL3hpZXBhbi8uY2FyZ28vcmVnaXN0cnkvc3JjL2luZGV4LmNyYXRlcy5pby0xOTQ5Y2Y4YzZiNWI1NTdmL3dhc20tYmluZGdlbi1mdXR1cmVzLTAuNC41MC9zcmMvbGliLnJzAAAAQBEQAGkAAACxAAAADwAAAEAREABpAAAAkQAAACcAAABAERAAaQAAALsAAAAkAAAAL1VzZXJzL3hpZXBhbi8uY2FyZ28vcmVnaXN0cnkvc3JjL2luZGV4LmNyYXRlcy5pby0xOTQ5Y2Y4YzZiNWI1NTdmL3dhc20tYmluZGdlbi1mdXR1cmVzLTAuNC41MC9zcmMvdGFzay9zaW5nbGV0aHJlYWQucnMA3BEQAHcAAAAiAAAAFQAAACMAAAAkAAAAJQAAACYAAADcERAAdwAAAGcAAAAlAAAAY2xvc3VyZSBpbnZva2VkIHJlY3Vyc2l2ZWx5IG9yIGFmdGVyIGJlaW5nIGRyb3BwZWQAACsAAAAEAAAABAAAACwAAAAtAAAATGF6eSBpbnN0YW5jZSBoYXMgcHJldmlvdXNseSBiZWVuIHBvaXNvbmVkAADMEhAAKgAAAC9Vc2Vycy94aWVwYW4vLmNhcmdvL3JlZ2lzdHJ5L3NyYy9pbmRleC5jcmF0ZXMuaW8tMTk0OWNmOGM2YjViNTU3Zi9vbmNlX2NlbGwtMS4yMS4zL3NyYy9saWIucnMAAAATEABeAAAACAMAABkAAAByZWVudHJhbnQgaW5pdAAAcBMQAA4AAAAAExAAXgAAAHoCAAANAAAAcmV0dXJuIHRoaXMAECoQABwqEAAoKhAANCoQAExhenkgaW5zdGFuY2UgaGFzIHByZXZpb3VzbHkgYmVlbiBwb2lzb25lZAAAtBMQACoAAAAvVXNlcnMveGllcGFuLy5jYXJnby9yZWdpc3RyeS9zcmMvaW5kZXguY3JhdGVzLmlvLTE5NDljZjhjNmI1YjU1N2Yvb25jZV9jZWxsLTEuMjEuMy9zcmMvbGliLnJzAADoExAAXgAAAAgDAAAZAAAAcmVlbnRyYW50IGluaXQAAFgUEAAOAAAA6BMQAF4AAAB6AgAADQAAAC9Vc2Vycy94aWVwYW4vLmNhcmdvL3JlZ2lzdHJ5L3NyYy9pbmRleC5jcmF0ZXMuaW8tMTk0OWNmOGM2YjViNTU3Zi93YXNtLWJpbmRnZW4tMC4yLjEwMC9zcmMvY29udmVydC9zbGljZXMucnMAAACAFBAAbQAAACQBAAAOAAAAY2xvc3VyZSBpbnZva2VkIHJlY3Vyc2l2ZWx5IG9yIGFmdGVyIGJlaW5nIGRyb3BwZWRKc1ZhbHVlKCkAMhUQAAgAAAA6FRAAAQAAAExhenkgaW5zdGFuY2UgaGFzIHByZXZpb3VzbHkgYmVlbiBwb2lzb25lZAAATBUQACoAAAAvVXNlcnMveGllcGFuLy5jYXJnby9yZWdpc3RyeS9zcmMvaW5kZXguY3JhdGVzLmlvLTE5NDljZjhjNmI1YjU1N2Yvb25jZV9jZWxsLTEuMjEuMy9zcmMvbGliLnJzAACAFRAAXgAAAAgDAAAZAAAAcmVlbnRyYW50IGluaXQAAPAVEAAOAAAAgBUQAF4AAAB6AgAADQAAAC9Vc2Vycy94aWVwYW4vLmNhcmdvL3JlZ2lzdHJ5L3NyYy9pbmRleC5jcmF0ZXMuaW8tMTk0OWNmOGM2YjViNTU3Zi93YXNtLWJpbmRnZW4tMC4yLjEwMC9zcmMvY29udmVydC9zbGljZXMucnNudWxsIHBvaW50ZXIgcGFzc2VkIHRvIHJ1c3RyZWN1cnNpdmUgdXNlIG9mIGFuIG9iamVjdCBkZXRlY3RlZCB3aGljaCB3b3VsZCBsZWFkIHRvIHVuc2FmZSBhbGlhc2luZyBpbiBydXN0ABgWEABtAAAA6AAAAAEAAAAvcnVzdGMvMDVmOTg0NmY4OTNiMDlhMWJlMWZjODU2MGUzM2ZjM2M4MTVjZmVjYi9saWJyYXJ5L2FsbG9jL3NyYy9zdHJpbmcucnMAABcQAEsAAACNBQAAGwAAAC9ydXN0Yy8wNWY5ODQ2Zjg5M2IwOWExYmUxZmM4NTYwZTMzZmMzYzgxNWNmZWNiL2xpYnJhcnkvYWxsb2Mvc3JjL3Jhd192ZWMucnNcFxAATAAAACoCAAARAAAAQwAAAAwAAAAEAAAARAAAAEUAAABGAAAAL3J1c3QvZGVwcy9kbG1hbGxvYy0wLjIuNy9zcmMvZGxtYWxsb2MucnNhc3NlcnRpb24gZmFpbGVkOiBwc2l6ZSA+PSBzaXplICsgbWluX292ZXJoZWFkANAXEAApAAAAqAQAAAkAAABhc3NlcnRpb24gZmFpbGVkOiBwc2l6ZSA8PSBzaXplICsgbWF4X292ZXJoZWFkAADQFxAAKQAAAK4EAAANAAAAQWNjZXNzRXJyb3JjYW5ub3QgYWNjZXNzIGEgVGhyZWFkIExvY2FsIFN0b3JhZ2UgdmFsdWUgZHVyaW5nIG9yIGFmdGVyIGRlc3RydWN0aW9uOiAAgxgQAEgAAABtZW1vcnkgYWxsb2NhdGlvbiBvZiAgYnl0ZXMgZmFpbGVkAADUGBAAFQAAAOkYEAANAAAAbGlicmFyeS9zdGQvc3JjL2FsbG9jLnJzCBkQABgAAABjAQAACQAAAEMAAAAMAAAABAAAAEcAAAAAAAAACAAAAAQAAABIAAAAAAAAAAgAAAAEAAAASQAAAEoAAABLAAAATAAAAE0AAAAQAAAABAAAAE4AAABPAAAAUAAAAFEAAABFcnJvcgAAAFIAAAAMAAAABAAAAFMAAABUAAAAVQAAAGNhcGFjaXR5IG92ZXJmbG93AAAAqBkQABEAAABsaWJyYXJ5L2FsbG9jL3NyYy9yYXdfdmVjLnJzxBkQABwAAAAqAgAAEQAAAGxpYnJhcnkvYWxsb2Mvc3JjL3N0cmluZy5ycwDwGRAAGwAAAOoBAAAXAAAAAAAAAAAAAAABAAAAVgAAAGEgZm9ybWF0dGluZyB0cmFpdCBpbXBsZW1lbnRhdGlvbiByZXR1cm5lZCBhbiBlcnJvciB3aGVuIHRoZSB1bmRlcmx5aW5nIHN0cmVhbSBkaWQgbm90bGlicmFyeS9hbGxvYy9zcmMvZm10LnJzAACCGhAAGAAAAIoCAAAOAAAA8BkQABsAAACNBQAAGwAAACkuLjAxMjM0NTY3ODlhYmNkZWZCb3Jyb3dFcnJvckJvcnJvd011dEVycm9yYWxyZWFkeSBib3Jyb3dlZDogAADoGhAAEgAAAGFscmVhZHkgbXV0YWJseSBib3Jyb3dlZDogAAAEGxAAGgAAAGNhbGxlZCBgT3B0aW9uOjp1bndyYXAoKWAgb24gYSBgTm9uZWAgdmFsdWVpbmRleCBvdXQgb2YgYm91bmRzOiB0aGUgbGVuIGlzICBidXQgdGhlIGluZGV4IGlzIAAAAFMbEAAgAAAAcxsQABIAAAA6IAAAAQAAAAAAAACYGxAAAgAAAAAAAAAMAAAABAAAAF0AAABeAAAAXwAAACAgICAsCn0gfSgoCixsaWJyYXJ5L2NvcmUvc3JjL2ZtdC9udW0ucnMweDAwMDEwMjAzMDQwNTA2MDcwODA5MTAxMTEyMTMxNDE1MTYxNzE4MTkyMDIxMjIyMzI0MjUyNjI3MjgyOTMwMzEzMjMzMzQzNTM2MzczODM5NDA0MTQyNDM0NDQ1NDY0NzQ4NDk1MDUxNTI1MzU0NTU1NjU3NTg1OTYwNjE2MjYzNjQ2NTY2Njc2ODY5NzA3MTcyNzM3NDc1NzY3Nzc4Nzk4MDgxODI4Mzg0ODU4Njg3ODg4OTkwOTE5MjkzOTQ5NTk2OTc5ODk5YXNzZXJ0aW9uIGZhaWxlZDogKmN1cnIgPiAxOQAA0RsQABsAAAAyAgAABQAAAGxpYnJhcnkvY29yZS9zcmMvc3RyL21vZC5ycwEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAwMDAwMDAwMDAwMDAwMDAwQEBAQEAAAAAAAAAAAAAABsaWJyYXJ5L2NvcmUvc3JjL3N0ci9wYXR0ZXJuLnJzAAD/HRAAHwAAAHAFAAASAAAA/x0QAB8AAABwBQAAKAAAAP8dEAAfAAAAYwYAABUAAAD/HRAAHwAAAJEGAAAVAAAA/x0QAB8AAACSBgAAFQAAAFsuLi5dYmVnaW4gPD0gZW5kICggPD0gKSB3aGVuIHNsaWNpbmcgYGB1HhAADgAAAIMeEAAEAAAAhx4QABAAAACXHhAAAQAAAGJ5dGUgaW5kZXggIGlzIG5vdCBhIGNoYXIgYm91bmRhcnk7IGl0IGlzIGluc2lkZSAgKGJ5dGVzICkgb2YgYAC4HhAACwAAAMMeEAAmAAAA6R4QAAgAAADxHhAABgAAAJceEAABAAAAIGlzIG91dCBvZiBib3VuZHMgb2YgYAAAuB4QAAsAAAAgHxAAFgAAAJceEAABAAAA5BwQABsAAACkAQAALAAAAGxpYnJhcnkvY29yZS9zcmMvdW5pY29kZS9wcmludGFibGUucnMAAABgHxAAJQAAABoAAAA2AAAAYB8QACUAAAAKAAAAKwAAAAAGAQEDAQQCBQcHAggICQIKBQsCDgQQARECEgUTHBQBFQIXAhkNHAUdCB8BJAFqBGsCrwOxArwCzwLRAtQM1QnWAtcC2gHgBeEC5wToAu4g8AT4AvoE+wEMJzs+Tk+Pnp6fe4uTlqKyuoaxBgcJNj0+VvPQ0QQUGDY3Vld/qq6vvTXgEoeJjp4EDQ4REikxNDpFRklKTk9kZYqMjY+2wcPExsvWXLa3GxwHCAoLFBc2OTqoqdjZCTeQkagHCjs+ZmmPkhFvX7/u71pi9Pz/U1Samy4vJyhVnaCho6SnqK26vMQGCwwVHTo/RVGmp8zNoAcZGiIlPj/n7O//xcYEICMlJigzODpISkxQU1VWWFpcXmBjZWZrc3h9f4qkqq+wwNCur25v3d6TXiJ7BQMELQNmAwEvLoCCHQMxDxwEJAkeBSsFRAQOKoCqBiQEJAQoCDQLTgM0DIE3CRYKCBg7RTkDYwgJMBYFIQMbBQFAOARLBS8ECgcJB0AgJwQMCTYDOgUaBwQMB1BJNzMNMwcuCAoGJgMdCAKA0FIQAzcsCCoWGiYcFBcJTgQkCUQNGQcKBkgIJwl1C0I+KgY7BQoGUQYBBRADBQtZCAIdYh5ICAqApl4iRQsKBg0TOgYKBhQcLAQXgLk8ZFMMSAkKRkUbSAhTDUkHCoC2Ig4KBkYKHQNHSTcDDggKBjkHCoE2GQc7Ax1VAQ8yDYObZnULgMSKTGMNhDAQFgqPmwWCR5q5OobGgjkHKgRcBiYKRgooBROBsDqAxltlSwQ5BxFABQsCDpf4CITWKQqi54EzDwEdBg4ECIGMiQRrBQ0DCQcQj2CA+gaBtExHCXQ8gPYKcwhwFUZ6FAwUDFcJGYCHgUcDhUIPFYRQHwYGgNUrBT4hAXAtAxoEAoFAHxE6BQGB0CqA1isEAYHggPcpTAQKBAKDEURMPYDCPAYBBFUFGzQCgQ4sBGQMVgqArjgdDSwECQcCDgaAmoPYBBEDDQN3BF8GDAQBDwwEOAgKBigILAQCPoFUDB0DCgU4BxwGCQeA+oQGAAEDBQUGBgIHBggHCREKHAsZDBoNEA4MDwQQAxISEwkWARcEGAEZAxoHGwEcAh8WIAMrAy0LLgEwBDECMgGnBKkCqgSrCPoC+wX9Av4D/wmteHmLjaIwV1iLjJAc3Q4PS0z7/C4vP1xdX+KEjY6RkqmxurvFxsnK3uTl/wAEERIpMTQ3Ojs9SUpdhI6SqbG0urvGys7P5OUABA0OERIpMTQ6O0VGSUpeZGWEkZudyc7PDREpOjtFSVdbXF5fZGWNkam0urvFyd/k5fANEUVJZGWAhLK8vr/V1/Dxg4WLpKa+v8XHz9rbSJi9zcbOz0lOT1dZXl+Jjo+xtre/wcbH1xEWF1tc9vf+/4Btcd7fDh9ubxwdX31+rq9Nu7wWFx4fRkdOT1haXF5+f7XF1NXc8PH1cnOPdHWWJi4vp6+3v8fP19+aAECXmDCPH87P0tTO/05PWlsHCA8QJy/u725vNz0/QkWQkVNndcjJ0NHY2ef+/wAgXyKC3wSCRAgbBAYRgawOgKsFHwiBHAMZCAEELwQ0BAcDAQcGBxEKUA8SB1UHAwQcCgkDCAMHAwIDAwMMBAUDCwYBDhUFTgcbB1cHAgYXDFAEQwMtAwEEEQYPDDoEHSVfIG0EaiWAyAWCsAMaBoL9A1kHFgkYCRQMFAxqBgoGGgZZBysFRgosBAwEAQMxCywEGgYLA4CsBgoGLzGA9Ag8Aw8DPgU4CCsFgv8RGAgvES0DIQ8hD4CMBIKaFgsViJQFLwU7BwIOGAmAviJ0DIDWGoEQBYDhCfKeAzcJgVwUgLgIgN0VOwMKBjgIRggMBnQLHgNaBFkJgIMYHAoWCUwEgIoGq6QMFwQxoQSB2iYHDAUFgKYQgfUHASAqBkwEgI0EgL4DGwMPDWxpYnJhcnkvY29yZS9zcmMvdW5pY29kZS91bmljb2RlX2RhdGEucnMAAABRJRAAKAAAAE0AAAAoAAAAUSUQACgAAABZAAAAFgAAAGF0dGVtcHQgdG8gZGl2aWRlIGJ5IHplcm8AAACcJRAAGQAAAGBhc3luYyBmbmAgcmVzdW1lZCBhZnRlciBjb21wbGV0aW9uAMAlEAAjAAAAIG91dCBvZiByYW5nZSBmb3Igc2xpY2Ugb2YgbGVuZ3RoIHJhbmdlIGVuZCBpbmRleCAAAA4mEAAQAAAA7CUQACIAAABzbGljZSBpbmRleCBzdGFydHMgYXQgIGJ1dCBlbmRzIGF0IAAwJhAAFgAAAEYmEAANAAAAAAMAAIMEIACRBWAAXROgABIXIB8MIGAf7ywgKyowoCtvpmAsAqjgLB774C0A/iA2nv9gNv0B4TYBCiE3JA3hN6sOYTkvGOE5MBzhSvMe4U5ANKFSHmHhU/BqYVRPb+FUnbxhVQDPYVZl0aFWANohVwDgoViu4iFa7OThW9DoYVwgAO5c8AF/XQBwAAcALQEBAQIBAgEBSAswFRABZQcCBgICAQQjAR4bWws6CQkBGAQBCQEDAQUrAzsJKhgBIDcBAQEECAQBAwcKAh0BOgEBAQIECAEJAQoCGgECAjkBBAIEAgIDAwEeAgMBCwI5AQQFAQIEARQCFgYBAToBAQIBBAgBBwMKAh4BOwEBAQwBCQEoAQMBNwEBAwUDAQQHAgsCHQE6AQICAQEDAwEEBwILAhwCOQIBAQIECAEJAQoCHQFIAQQBAgMBAQgBUQECBwwIYgECCQsHSQIbAQEBAQE3DgEFAQIFCwEkCQFmBAEGAQICAhkCBAMQBA0BAgIGAQ8BAAMABBwDHQIeAkACAQcIAQILCQEtAwEBdQIiAXYDBAIJAQYD2wICAToBAQcBAQEBAggGCgIBMB8xBDAKBAMmCQwCIAQCBjgBAQIDAQEFOAgCApgDAQ0BBwQBBgEDAsZAAAHDIQADjQFgIAAGaQIABAEKIAJQAgABAwEEARkCBQGXAhoSDQEmCBkLAQEsAzABAgQCAgIBJAFDBgICAgIMAQgBLwEzAQEDAgIFAgEBKgIIAe4BAgEEAQABABAQEAACAAHiAZUFAAMBAgUEKAMEAaUCAARBBQACTwRGCzEEewE2DykBAgIKAzEEAgIHAT0DJAUBCD4BDAI0CQEBCAQCAV8DAgQGAQIBnQEDCBUCOQIBAQEBDAEJAQ4HAwVDAQIGAQECAQEDBAMBAQ4CVQgCAwEBFwFRAQIGAQECAQECAQLrAQIEBgIBAhsCVQgCAQECagEBAQIIZQEBAQIEAQUACQEC9QEKBAQBkAQCAgQBIAooBgIECAEJBgIDLg0BAgAHAQYBAVIWAgcBAgECegYDAQECAQcBAUgCAwEBAQACCwI0BQUDFwEAAQYPAAwDAwAFOwcAAT8EUQELAgACAC4CFwAFAwYICAIHHgSUAwA3BDIIAQ4BFgUBDwAHARECBwECAQVkAaAHAAE9BAAE/gIAB20HAGCA8AAAQdzTwAALgAEDAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIAAAAiAAAAAAAAAAAAAAAuAAAAAgAAAAAAAAAvAAAAAgAAAAAAAAAwAAAAAgAAAAAAAAAxAAAAAgAAAAAAAAAyAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQAAAAAD/2AEEbmFtZQAREG5vY3R1YV9jb3JlLndhc20BvNgBpAMANndhc21fYmluZGdlbjo6X193YmluZGdlbl9zdHJpbmdfZ2V0OjpoNzA3Y2RmNTNhNmRhZjgyNQE2d2FzbV9iaW5kZ2VuOjpfX3diaW5kZ2VuX3N0cmluZ19uZXc6OmhmNzExOTQyMjVhYzQ3YmJjAjV3YXNtX2JpbmRnZW46Ol9fd2JpbmRnZW5faXNfc3RyaW5nOjpoOTFkOGM1ZDVmYjdmZjRjNwM2d2FzbV9iaW5kZ2VuOjpfX3diaW5kZ2VuX251bWJlcl9nZXQ6Omg4MTZiZDI1ZWJkYmVhNzc2BDN3YXNtX2JpbmRnZW46Ol9fd2JpbmRnZW5faXNfbnVsbDo6aDVkZDI4ODliMTA3YTdiYzQFOHdhc21fYmluZGdlbjo6X193YmluZGdlbl9pc191bmRlZmluZWQ6OmgyMmI4NGQyMzM4ZmFiZTRjBjZ3YXNtX2JpbmRnZW46Ol9fd2JpbmRnZW5fbnVtYmVyX25ldzo6aDk3MWQzY2I5Mjk3ZWE0NmIHUGdldHJhbmRvbTo6aW1wOjpHbG9iYWw6OmNyeXB0bzo6X193YmdfY3J5cHRvXzU3NGU3OGFkOGIxM2I2NWY6OmhjMTcwMGUxNGVjMjUyZmY1CDV3YXNtX2JpbmRnZW46Ol9fd2JpbmRnZW5faXNfb2JqZWN0OjpoMDUwNzI3MTg0NDcxNjQ1MwlSZ2V0cmFuZG9tOjppbXA6Okdsb2JhbDo6cHJvY2Vzczo6X193YmdfcHJvY2Vzc19kYzBmYmFjYzdjMWMwNmY3OjpoNmM2N2QzNDI5YmMxNmExMApVZ2V0cmFuZG9tOjppbXA6OlByb2Nlc3M6OnZlcnNpb25zOjpfX3diZ192ZXJzaW9uc19jMDFkZmQ0NzIyYTg4MTY1OjpoNjg2Mzc3N2I1NWYxZGI1ZgtOZ2V0cmFuZG9tOjppbXA6OlZlcnNpb25zOjpub2RlOjpfX3diZ19ub2RlXzkwNWQzZTI1MWVkZmY4YTI6OmhhZWVmOWU1MzRiNDQxNTZlDFVnZXRyYW5kb206OmltcDo6TW9kdWxlOjpyZXF1aXJlX2ZuOjpfX3diZ19yZXF1aXJlXzYwY2M3NDdhNmJjNTIxNWE6OmgxODQxMTVhOTI4Y2IyN2M2DVVnZXRyYW5kb206OmltcDo6R2xvYmFsOjptc19jcnlwdG86Ol9fd2JnX21zQ3J5cHRvX2E2MWFlYjM1YTI0YzEzMjk6OmgwNzliZWY3ODE1M2M0ZTM5DmZnZXRyYW5kb206OmltcDo6Tm9kZUNyeXB0bzo6cmFuZG9tX2ZpbGxfc3luYzo6X193YmdfcmFuZG9tRmlsbFN5bmNfYWMwOTg4YWJhMzI1NDI5MDo6aGZiYjJiZmVhZDU5ZTQ3MDMPZ2dldHJhbmRvbTo6aW1wOjpXZWJDcnlwdG86OmdldF9yYW5kb21fdmFsdWVzOjpfX3diZ19nZXRSYW5kb21WYWx1ZXNfYjhmNWRiZDVmMzk5NWE5ZTo6aDA2ZDUxM2RjYmQ5NmY2YzcQZXdhc21fYmluZGdlbl9mdXR1cmVzOjpxdWV1ZTo6cXVldWVNaWNyb3Rhc2s6Ol9fd2JnX3F1ZXVlTWljcm90YXNrXzk3ZDkyYjRmY2M4YTYxYzU6OmhkNGIzYTA4OTQ1M2Y1MjVlEXB3YXNtX2JpbmRnZW5fZnV0dXJlczo6cXVldWU6Okdsb2JhbDo6aGFzUXVldWVNaWNyb3Rhc2s6Ol9fd2JnX3F1ZXVlTWljcm90YXNrX2QzMjE5ZGVmODI1NTI0ODU6OmhmN2Q3OTc3NDJiMjhkMjRjEjd3YXNtX2JpbmRnZW46Ol9fd2JpbmRnZW5faXNfZnVuY3Rpb246Omg3ZDczNThjM2UzNGM4ZjM1EzN3YXNtX2JpbmRnZW46Ol9fd2JpbmRnZW5fY2JfZHJvcDo6aDg0ZDNiZTU4NzZmMGNjZjIUUmpzX3N5czo6RnVuY3Rpb246Om5ld19ub19hcmdzOjpfX3diZ19uZXdub2FyZ3NfMTA1ZWQ0NzE0NzVhYWY1MDo6aGE3ZTIwOWZjZjE4NDMyMGIVR2pzX3N5czo6RnVuY3Rpb246OmNhbGwwOjpfX3diZ19jYWxsXzY3MmE0ZDIxNjM0ZDRhMjQ6Omg5ZGMwN2U3M2FjNjYxYWJkFkJqc19zeXM6Ok9iamVjdDo6bmV3OjpfX3diZ19uZXdfNDA1ZTIyZjM5MDU3NmNlMjo6aGJlMTBlYmI3NGEzZjkxY2QXR2pzX3N5czo6RnVuY3Rpb246OmNhbGwxOjpfX3diZ19jYWxsXzdjY2NkZDY5ZTA3OTFhZTI6OmhmZTg5MzhmNmRmMTViMzM3GEdqc19zeXM6OkZ1bmN0aW9uOjpjYWxsMjo6X193YmdfY2FsbF84MzNiZWQ1NzcwZWEyMDQxOjpoNzM0NGZmYmEyYjk1MDE3ORmKAWpzX3N5czo6Xzo6PGltcGwgd2FzbV9iaW5kZ2VuOjpjYXN0OjpKc0Nhc3QgZm9yIGpzX3N5czo6UHJvbWlzZT46Omluc3RhbmNlb2Y6Ol9fd2JnX2luc3RhbmNlb2ZfUHJvbWlzZV85MzUxNjhiOGY0YjQ5ZGIzOjpoNWRkOWI3ZjBhMzY5MmZmORpDanNfc3lzOjpQcm9taXNlOjpuZXc6Ol9fd2JnX25ld18yM2EyNjY1ZmFjODNjNjExOjpoZGY4ZGVhMDk1MjU1YTM4NxtLanNfc3lzOjpQcm9taXNlOjpyZXNvbHZlOjpfX3diZ19yZXNvbHZlXzQ4NTE3ODVjOWM1ZjU3M2Q6OmhjODczNzM1NzVjMjFjODIzHEVqc19zeXM6OlByb21pc2U6OnRoZW46Ol9fd2JnX3RoZW5fNDRiNzM5NDZkMmZiM2U3ZDo6aDk3NTg4NjI1YjA5ZTY5ZjIdRmpzX3N5czo6UHJvbWlzZTo6dGhlbjI6Ol9fd2JnX3RoZW5fNDhiNDA2NzQ5ODc4YTUzMTo6aDAzY2I0MmIzNTdjMjVjMjMee2pzX3N5czo6Z2xvYmFsOjpnZXRfZ2xvYmFsX29iamVjdDo6R0xPQkFMX1RISVM6OmluaXQ6Ol9fd2JnX3N0YXRpY19hY2Nlc3Nvcl9HTE9CQUxfVEhJU181NjU3OGJlN2U5ZjgzMmIwOjpoYTEzNTAyMjAyODRiMzFjNh9tanNfc3lzOjpnbG9iYWw6OmdldF9nbG9iYWxfb2JqZWN0OjpTRUxGOjppbml0OjpfX3diZ19zdGF0aWNfYWNjZXNzb3JfU0VMRl8zN2M1ZDQxOGU0YmY1ODE5OjpoNDVmNjZhMDliNDBhNTI0YiBxanNfc3lzOjpnbG9iYWw6OmdldF9nbG9iYWxfb2JqZWN0OjpXSU5ET1c6OmluaXQ6Ol9fd2JnX3N0YXRpY19hY2Nlc3Nvcl9XSU5ET1dfNWRlMzcwNDNhOTFhOWM0MDo6aDY4YmUxNDEwYjUyMjdmMjUhcWpzX3N5czo6Z2xvYmFsOjpnZXRfZ2xvYmFsX29iamVjdDo6R0xPQkFMOjppbml0OjpfX3diZ19zdGF0aWNfYWNjZXNzb3JfR0xPQkFMXzg4YTkwMmQxM2E1NTdkMDc6Omg0MTk3NmU3OTA0MzYzOGNjIlVqc19zeXM6OldlYkFzc2VtYmx5OjpNZW1vcnk6OmJ1ZmZlcjo6X193YmdfYnVmZmVyXzYwOWNjM2VlZTUxZWQxNTg6OmhkMzA0MTJlZTAwZWI0ZTg3I3lqc19zeXM6OlVpbnQ4QXJyYXk6Om5ld193aXRoX2J5dGVfb2Zmc2V0X2FuZF9sZW5ndGg6Ol9fd2JnX25ld3dpdGhieXRlb2Zmc2V0YW5kbGVuZ3RoX2Q5N2U2MzdlYmUxNDVhOWE6OmgyNjQ2NjgzMzBiZWM1NzljJEZqc19zeXM6OlVpbnQ4QXJyYXk6Om5ldzo6X193YmdfbmV3X2ExMjAwMmE3ZjkxYzc1YmU6Omg0NjhjOGI4ZTlkMGM3YWIyJUZqc19zeXM6OlVpbnQ4QXJyYXk6OnNldDo6X193Ymdfc2V0XzY1NTk1YmRkODY4YjMwMDk6OmhlOTgyMzg1ODc5N2RlOTUxJlxqc19zeXM6OlVpbnQ4QXJyYXk6Om5ld193aXRoX2xlbmd0aDo6X193YmdfbmV3d2l0aGxlbmd0aF9hMzgxNjM0ZTkwYzI3NmQ0OjpoNjllNGQzMjA5MjJmNmJkYSdQanNfc3lzOjpVaW50OEFycmF5OjpzdWJhcnJheTo6X193Ymdfc3ViYXJyYXlfYWE5MDY1ZmE5ZGM1ZGY5Njo6aDRiYWYwZjdmOTQwZmU1NDUoQ2pzX3N5czo6UmVmbGVjdDo6c2V0OjpfX3diZ19zZXRfYmI4Y2VjZjZhNjJiOWY0Njo6aGM0MDg3ZTBjMmNlZTUwZmYpOHdhc21fYmluZGdlbjo6X193YmluZGdlbl9kZWJ1Z19zdHJpbmc6OmhlMmI5NWM2ZDYwYTM4ZDczKjF3YXNtX2JpbmRnZW46Ol9fd2JpbmRnZW5fdGhyb3c6OmhmYTRhM2JlNmI1M2U1ZTgyKzJ3YXNtX2JpbmRnZW46Ol9fd2JpbmRnZW5fbWVtb3J5OjpoYTQzM2EzNzU1Zjc1YmJmMywdX193YmluZGdlbl9jbG9zdXJlX3dyYXBwZXIxMTEtHV9fd2JpbmRnZW5fY2xvc3VyZV93cmFwcGVyMTg3LzpkbG1hbGxvYzo6ZGxtYWxsb2M6OkRsbWFsbG9jPEE+OjptYWxsb2M6Omg1N2VlYmQzM2ViNjdjMmVhMD5ub2N0dWFfY29yZTo6Tm9jdHVhQ29yZTo6c3RhcnQ6Ont7Y2xvc3VyZX19OjpoOTJjMjJlZTg4Y2ZjMmY4ODFCbm9jdHVhX2NvcmU6Ok5vY3R1YUNvcmU6OnVwZGF0ZV9zZXNzaW9uX3N0b3JhZ2U6OmhjNDlhZGY0NzQ1YjE0ZTdiMkw8Y29yZTo6cGluOjpQaW48UD4gYXMgY29yZTo6ZnV0dXJlOjpmdXR1cmU6OkZ1dHVyZT46OnBvbGw6OmhhOTc2ZDg2NTgxOTYzZjEyMzdjb3JlOjpzdHI6OnBhdHRlcm46OlN0clNlYXJjaGVyOjpuZXc6OmhhMDc5MzYwYjMyNjVjODAyNDtub2N0dWFfY29yZTo6Tm9jdHVhQ29yZTo6dmFsaWRhdGVfcHJlZml4OjpoZDY1OWJiZThjNzdmN2JkNTUyY29tcGlsZXJfYnVpbHRpbnM6Om1lbTo6bWVtbW92ZTo6aDc1ZDExZjg1NGZkMzE5MmY2MWNvcmU6OnN0cjo6c2xpY2VfZXJyb3JfZmFpbF9ydDo6aDI5N2U1NTg5OTcyMjk3MDQ3M2NvcmU6OnN0cjo6Y291bnQ6OmRvX2NvdW50X2NoYXJzOjpoNjRlMTZiMDVkMGYxMDJjNDgNX19yZGxfcmVhbGxvYzk1Y29yZTo6Zm10OjpGb3JtYXR0ZXI6OnBhZF9pbnRlZ3JhbDo6aDk5OWZmZjk5MzYwYzFiN2M6LGNvcmU6OmZtdDo6Rm9ybWF0dGVyOjpwYWQ6OmhiNGNiYjlmNzA5YjAwNGMwOzFjb3JlOjpmbXQ6Om51bTo6cGFyc2VfdTY0X2ludG86Omg4ZDU1ZjdhOGNjNjhjOTBmPEVjb3JlOjpjaGFyOjptZXRob2RzOjo8aW1wbCBjaGFyPjo6ZXNjYXBlX2RlYnVnX2V4dDo6aDUxMDc5MzdmNjEzYmI0OWI9TXN0ZDo6c3lzOjp0aHJlYWRfbG9jYWw6OnN0YXRpazo6TGF6eVN0b3JhZ2U8VD46OmluaXRpYWxpemU6Omg0Yjg4ODg3ZGY2ODU3MzEyPgZtZW1jcHk/OG5vY3R1YV9jb3JlOjpOb2N0dWFDb3JlOjpmb3J3YXJkX2RhdGE6OmhjZmI3ZjJlMGFiZGE5YjAxQDFjb3JlOjpzdHI6OmNvbnZlcnRzOjpmcm9tX3V0Zjg6Omg2ZTVlYzM4ZDY0YjBlNjQ5QThkbG1hbGxvYzo6ZGxtYWxsb2M6OkRsbWFsbG9jPEE+OjpmcmVlOjpoNGViNDE3NWQwNjc2MDMzNUI2bm9jdHVhX2NvcmU6Ok5vY3R1YUNvcmU6OmZldGNoX2RhdGE6Omg5ZmQyMzc2MWY3NjIwNGVkQyNjb3JlOjpmbXQ6OndyaXRlOjpoYzZlOTM2Yzc2YWJiMmNhOERKPGNvcmU6Om9wczo6cmFuZ2U6OlJhbmdlPElkeD4gYXMgY29yZTo6Zm10OjpEZWJ1Zz46OmZtdDo6aDFiODM5YzFkZTM5MjhlYzhFUzxjb3JlOjpmbXQ6OmJ1aWxkZXJzOjpQYWRBZGFwdGVyIGFzIGNvcmU6OmZtdDo6V3JpdGU+Ojp3cml0ZV9zdHI6Omg0NmUyZTAyNDI2N2UwZjkxRitjb3JlOjpmbXQ6Om51bTo6Zm10X3UxMjg6Omg4ZWNhZjM0NWNlOTI5NGVhR148Y29yZTo6c3RyOjppdGVyOjpTcGxpdDxQPiBhcyBjb3JlOjppdGVyOjp0cmFpdHM6Oml0ZXJhdG9yOjpJdGVyYXRvcj46Om5leHQ6Omg4YjNjMDZiZmY3NTcwNWFlSDtjb3JlOjpzdHI6OnBhdHRlcm46OlR3b1dheVNlYXJjaGVyOjpuZXh0OjpoM2EzYjk5ZWJkM2ZmNWZmZUk7Y29yZTo6c3RyOjpwYXR0ZXJuOjpUd29XYXlTZWFyY2hlcjo6bmV4dDo6aDNhM2I5OWViZDNmZjVmZmVKN3JhbmRfY29yZTo6U2VlZGFibGVSbmc6OmZyb21fZW50cm9weTo6aGI1OTIwMGYwM2U1MTgxOGVLQWRsbWFsbG9jOjpkbG1hbGxvYzo6RGxtYWxsb2M8QT46OmRpc3Bvc2VfY2h1bms6Omg0NDEwOTI2ZTdiYTgwZmM4TEJvbmNlX2NlbGw6OnVuc3luYzo6T25jZUNlbGw8VD46OmdldF9vcl90cnlfaW5pdDo6aDA0ZThjZTU1MjdiOTA5YTZNBm1lbXNldE5MY29yZTo6dW5pY29kZTo6dW5pY29kZV9kYXRhOjpncmFwaGVtZV9leHRlbmQ6Omxvb2t1cF9zbG93OjpoODJhYmFjOThmZWMwNmQzMk8uY29yZTo6c3RyOjo8aW1wbCBzdHI+OjpmaW5kOjpoYmEwZWU4NTY1MDE4MmQzZlAuY29yZTo6c3RyOjo8aW1wbCBzdHI+OjpmaW5kOjpoYmEwZWU4NTY1MDE4MmQzZlE8ZGxtYWxsb2M6OmRsbWFsbG9jOjpEbG1hbGxvYzxBPjo6bWVtYWxpZ246OmhhZThlOGYxN2VmNDMzZTgyUjNhbGxvYzo6Zm10Ojpmb3JtYXQ6OmZvcm1hdF9pbm5lcjo6aDAzZWJkYTM4NDI1YWFlMDRTNGpzX3N5czo6Z2xvYmFsOjpnZXRfZ2xvYmFsX29iamVjdDo6aGE1OTU3Yzg5YjBhNDg1MjVUOGNvcmU6OmZtdDo6bnVtOjppbXA6OjxpbXBsIHUzMj46Ol9mbXQ6OmhjZmVmYzFiN2EyOGMzYzJlVThzdGQ6OnRocmVhZDo6bG9jYWw6OkxvY2FsS2V5PFQ+Ojp3aXRoOjpoZmVjZWQyNjliZmU5ZTRmZlZhPHdhc21fYmluZGdlbl9mdXR1cmVzOjpKc0Z1dHVyZSBhcyBjb3JlOjpjb252ZXJ0OjpGcm9tPGpzX3N5czo6UHJvbWlzZT4+Ojpmcm9tOjpoZTcwMzU1NGM0ODNmYTY0MlcyY29yZTo6dW5pY29kZTo6cHJpbnRhYmxlOjpjaGVjazo6aDZlZDg1NjE0NmFjMzZhZmJYOHN0ZDo6dGhyZWFkOjpsb2NhbDo6TG9jYWxLZXk8VD46OndpdGg6OmgxMDMyODJiN2E3ZjZjNWRkWUBkbG1hbGxvYzo6ZGxtYWxsb2M6OkRsbWFsbG9jPEE+Ojp1bmxpbmtfY2h1bms6OmhjNjU2ZDZjN2JhNjQ0M2Y5Wl9ub2N0dWFfY29yZTo6Tm9jdHVhQ29yZTo6c2NoZWR1bGVfY29sbGVjdGlvbl9hc3luYzo6e3tjbG9zdXJlfX06Ont7Y2xvc3VyZX19OjpoNjk4MTE1YjMzMzA1ZDQyN1tDd2FzbV9iaW5kZ2VuX2Z1dHVyZXM6OnF1ZXVlOjpRdWV1ZVN0YXRlOjpydW5fYWxsOjpoYWY5YjAyMzUxMzQ0ZWE1YlxKPGFsbG9jOjpzdHJpbmc6OlN0cmluZyBhcyBjb3JlOjpmbXQ6OldyaXRlPjo6d3JpdGVfY2hhcjo6aDBiNWNmMGZmZWQwNTY3NDBdSjxhbGxvYzo6c3RyaW5nOjpTdHJpbmcgYXMgY29yZTo6Zm10OjpXcml0ZT46OndyaXRlX2NoYXI6OmgwYjVjZjBmZmVkMDU2NzQwXkZkbG1hbGxvYzo6ZGxtYWxsb2M6OkRsbWFsbG9jPEE+OjppbnNlcnRfbGFyZ2VfY2h1bms6Omg4ZGIzMDdkNThiNWRjNzViX0Jjb3JlOjpmbXQ6OkZvcm1hdHRlcjo6ZGVidWdfdHVwbGVfZmllbGQxX2ZpbmlzaDo6aGI4MzhiNzdlNTY3ODZjMWJgLHJhbmQ6OnJuZzo6Um5nOjpnZW5fcmFuZ2U6Omg1ZDIxNzhmMDA5NzU2M2YyYUFhbGxvYzo6cmF3X3ZlYzo6UmF3VmVjSW5uZXI8QT46Omdyb3dfYW1vcnRpemVkOjpoMWIwNjZkZjE0ZjA4MzAzN2I2Y29yZTo6c2xpY2U6Om1lbWNocjo6bWVtY2hyX2FsaWduZWQ6OmgwNWMwMWUzNDczZTVhNzdjY3Q8c3RkOjpwYW5pY2tpbmc6OmJlZ2luX3BhbmljX2hhbmRsZXI6OkZvcm1hdFN0cmluZ1BheWxvYWQgYXMgY29yZTo6cGFuaWM6OlBhbmljUGF5bG9hZD46OnRha2VfYm94OjpoY2U2NGE1OTYwMzNmZDdhMGRCb25jZV9jZWxsOjp1bnN5bmM6Ok9uY2VDZWxsPFQ+OjpnZXRfb3JfdHJ5X2luaXQ6Omg4NTQyZGI3NmM0M2M4NmQyZWxjb3JlOjpwdHI6OmRyb3BfaW5fcGxhY2U8bm9jdHVhX2NvcmU6Ok5vY3R1YUNvcmU6OmNvbGxlY3RfYW5kX2ZvcndhcmRfYXN5bmM6Ont7Y2xvc3VyZX19Pjo6aGVhZjhiMjdmZGQ0ZWNlYzVmbGNvcmU6OnB0cjo6ZHJvcF9pbl9wbGFjZTxub2N0dWFfY29yZTo6Tm9jdHVhQ29yZTo6Y29sbGVjdF9hbmRfZm9yd2FyZF9hc3luYzo6e3tjbG9zdXJlfX0+OjpoZWFmOGIyN2ZkZDRlY2VjNWdfbm9jdHVhX2NvcmU6Ok5vY3R1YUNvcmU6OnNjaGVkdWxlX2NvbGxlY3Rpb25fYXN5bmM6Ont7Y2xvc3VyZX19Ojp7e2Nsb3N1cmV9fTo6aDY5ODExNWIzMzMwNWQ0MjdoUWFsbG9jOjpyYXdfdmVjOjpSYXdWZWNJbm5lcjxBPjo6cmVzZXJ2ZTo6ZG9fcmVzZXJ2ZV9hbmRfaGFuZGxlOjpoMWMwNzRlNzk0MWRjODg5M2lBbm9jdHVhX2NvcmU6Ok5vY3R1YUNvcmU6OmdldF9jdXJyZW50X3RpbWVzdGFtcDo6aGU4YjZkZTgxZjI1ZWMxMmFqN3dhc21fYmluZGdlbjo6ZXh0ZXJucmVmOjpTbGFiOjphbGxvYzo6aGJjMDVmMjIzNTEyZGQ4YzNrPWFsbG9jOjpyYXdfdmVjOjpSYXdWZWNJbm5lcjxBPjo6Z3Jvd19leGFjdDo6aGVmZjViZmQ1ODAwNTc3ZjJsTXN0ZDo6c3lzOjp0aHJlYWRfbG9jYWw6OnN0YXRpazo6TGF6eVN0b3JhZ2U8VD46OmluaXRpYWxpemU6Omg4MjlhMjgzMDNjZmFiNGZhbWA8YWxsb2M6OmNvbGxlY3Rpb25zOjp2ZWNfZGVxdWU6OlZlY0RlcXVlPFQsQT4gYXMgY29yZTo6b3BzOjpkcm9wOjpEcm9wPjo6ZHJvcDo6aDVjOWVhZTIxMmU2YjJhZTBuQ2FsbG9jOjpyYXdfdmVjOjpSYXdWZWNJbm5lcjxBPjo6c2hyaW5rX3VuY2hlY2tlZDo6aGJjZTA0NTZmMGY2NzQ4MDlvRzxnZXRyYW5kb206OmVycm9yOjpFcnJvciBhcyBjb3JlOjpmbXQ6OkRpc3BsYXk+OjpmbXQ6Omg4ZTA5Yjg4ZDUxMjQ0OWQ3cEJvbmNlX2NlbGw6OnVuc3luYzo6T25jZUNlbGw8VD46OmdldF9vcl90cnlfaW5pdDo6aGUwYWYzYjk0YmVlMjJiMGRxOmNvcmU6OnN0cjo6PGltcGwgc3RyPjo6dHJpbV9lbmRfbWF0Y2hlczo6aDRmMjVhM2RmNjI5YWFkYTlycDxhbGxvYzo6dmVjOjpWZWM8VD4gYXMgYWxsb2M6OnZlYzo6c3BlY19mcm9tX2l0ZXJfbmVzdGVkOjpTcGVjRnJvbUl0ZXJOZXN0ZWQ8VCxJPj46OmZyb21faXRlcjo6aDUzOTBmMzc0ZmJhMDg5Y2FzUWFsbG9jOjpyYXdfdmVjOjpSYXdWZWNJbm5lcjxBPjo6cmVzZXJ2ZTo6ZG9fcmVzZXJ2ZV9hbmRfaGFuZGxlOjpoZThmYjFiOGE3Njc2MTBkMXQ5Y29yZTo6dW5pY29kZTo6cHJpbnRhYmxlOjppc19wcmludGFibGU6OmhhNDNlZmE1MjVhMGQyYzQxdXA8cmFuZDo6cm5nczo6eG9zaGlybzEyOHBsdXNwbHVzOjpYb3NoaXJvMTI4UGx1c1BsdXMgYXMgcmFuZF9jb3JlOjpTZWVkYWJsZVJuZz46OnNlZWRfZnJvbV91NjQ6OmgyNmI5NjAzMDZiZmZmYTYzdmQ8bm9jdHVhX2NvcmU6Ok5vY3R1YUNvcmUgYXMgd2FzbV9iaW5kZ2VuOjpjb252ZXJ0Ojp0cmFpdHM6OkZyb21XYXNtQWJpPjo6ZnJvbV9hYmk6Omg1OTY5OWQzZDI2MjdkNDdidzp3YXNtX2JpbmRnZW5fZnV0dXJlczo6cXVldWU6OlF1ZXVlOjpuZXc6OmgyNWZiYjg4M2QzYzhlNTMyeDhzdGQ6OnRocmVhZDo6bG9jYWw6OkxvY2FsS2V5PFQ+Ojp3aXRoOjpoNmFmMDkxYjBiZDI1MTBmMHk+d2FzbV9iaW5kZ2VuOjpfX3J0OjpMYXp5Q2VsbDxULEY+Ojp0cnlfd2l0aDo6aGI3NjU3ZjdkOGM2MjI0YjB6aTx3YXNtX2JpbmRnZW5fZnV0dXJlczo6SnNGdXR1cmUgYXMgY29yZTo6Y29udmVydDo6RnJvbTxqc19zeXM6OlByb21pc2U+Pjo6ZnJvbTo6ZmluaXNoOjpoODk5ZmU5ZjM5MGY0NGQ0MXtvPHN0ZDo6cGFuaWNraW5nOjpiZWdpbl9wYW5pY19oYW5kbGVyOjpGb3JtYXRTdHJpbmdQYXlsb2FkIGFzIGNvcmU6OnBhbmljOjpQYW5pY1BheWxvYWQ+OjpnZXQ6Omg0NTdmZjE3NTFjOTZiZjVhfD53YXNtX2JpbmRnZW46Ol9fcnQ6OkxhenlDZWxsPFQsRj46OnRyeV93aXRoOjpoZmQyYTk5OTdkOGRkMjFhYX03c3RkOjpwYW5pY2tpbmc6OnJ1c3RfcGFuaWNfd2l0aF9ob29rOjpoNDUwMWNjZWI3ZGJlMzgzZH44YWxsb2M6OnJhd192ZWM6OlJhd1ZlYzxULEE+Ojpncm93X29uZTo6aDQ2MWY3NzNjNWRiM2ZkZTZ/YjxUIGFzIHdhc21fYmluZGdlbjo6Y2xvc3VyZTo6V2FzbUNsb3N1cmVGbk9uY2U8KCksUj4+OjppbnRvX2ZuX211dDo6e3tjbG9zdXJlfX06OmhkMDFmMTE1N2YwYzk5ODNkgAFCYWxsb2M6OnJhd192ZWM6OlJhd1ZlY0lubmVyPEE+Ojp0cnlfYWxsb2NhdGVfaW46OmhiZWY1ZjM2M2I3NDU2YzEzgQFCb25jZV9jZWxsOjp1bnN5bmM6Ok9uY2VDZWxsPFQ+OjpnZXRfb3JfdHJ5X2luaXQ6OmhjNWJjNTY0OTk1NjIxYTNmggFZPHdhc21fYmluZGdlbl9mdXR1cmVzOjpKc0Z1dHVyZSBhcyBjb3JlOjpmdXR1cmU6OmZ1dHVyZTo6RnV0dXJlPjo6cG9sbDo6aDZkMmU4ZTYzZGM2ZjY2M2KDAUhjb3JlOjpvcHM6OmZ1bmN0aW9uOjpGbk9uY2U6OmNhbGxfb25jZXt7dnRhYmxlLnNoaW19fTo6aDM2NjdkMzBjNzYwZmFhZDmEASVhbGxvYzo6Zm10Ojpmb3JtYXQ6OmgwMzc5MTIwNzEzNjYyN2FmhQElYWxsb2M6OmZtdDo6Zm9ybWF0OjpoMDM3OTEyMDcxMzY2MjdhZoYBLmFsbG9jOjpyYXdfdmVjOjpmaW5pc2hfZ3Jvdzo6aGMwODNkYWJmYmU0ODJjZDeHAS5hbGxvYzo6cmF3X3ZlYzo6ZmluaXNoX2dyb3c6Omg0Y2QxYjQ4NmZjZTUyMmMwiAEuYWxsb2M6OnJhd192ZWM6OmZpbmlzaF9ncm93OjpoMTdiZGJjMjg5ZDAzZmEyZYkBRWFsbG9jOjpjb2xsZWN0aW9uczo6dmVjX2RlcXVlOjpWZWNEZXF1ZTxULEE+Ojpncm93OjpoNDU0NTc2MGQ4YmU0MjNmNIoBQ3N0ZDo6cGFuaWNraW5nOjpiZWdpbl9wYW5pY19oYW5kbGVyOjp7e2Nsb3N1cmV9fTo6aGMyYjlkM2ZjMjZkZGE3ZjmLAUh3YXNtX2JpbmRnZW5fZnV0dXJlczo6dGFzazo6c2luZ2xldGhyZWFkOjpUYXNrOjpzcGF3bjo6aDk1YTJiYTUyNDA0MTAwNGaMAUM8d2FzbV9iaW5kZ2VuOjpKc1ZhbHVlIGFzIGNvcmU6OmZtdDo6RGVidWc+OjpmbXQ6OmhiMjgwMDlkNjFlZDc3YzU5jQEyPGNoYXIgYXMgY29yZTo6Zm10OjpEZWJ1Zz46OmZtdDo6aDc5MGVlNjc1NmZlMDViYzCOATBhbGxvYzo6cmM6OlJjPFQsQT46OmRyb3Bfc2xvdzo6aDIzZTI2ZDEyZTQwNDQ0NDWPATlhbGxvYzo6dmVjOjpWZWM8VCxBPjo6ZXh0ZW5kX2Rlc3VnYXJlZDo6aDkwOGJlNjM4MjM1NTI0NTmQAQxfX2ZpeHVuc2RmdGmRAWg8c3RkOjpwYW5pY2tpbmc6OmJlZ2luX3BhbmljX2hhbmRsZXI6OkZvcm1hdFN0cmluZ1BheWxvYWQgYXMgY29yZTo6Zm10OjpEaXNwbGF5Pjo6Zm10OjpoZWZhMGYwM2EzNDJkNzNmN5IBCF9fbXVsdGkzkwEuY29yZTo6cmVzdWx0Ojp1bndyYXBfZmFpbGVkOjpoNGVjOGViYzBjZDI5ZjU0YpQBMGFsbG9jOjpyYzo6UmM8VCxBPjo6ZHJvcF9zbG93OjpoNmViNjc1YjFhMDkyZjA5NZUBPWFsbG9jOjpyYXdfdmVjOjpSYXdWZWNJbm5lcjxBPjo6ZGVhbGxvY2F0ZTo6aDI3ZTFmMmFmZjdiN2IyYjiWAWg8Y29yZTo6aXRlcjo6YWRhcHRlcnM6OnppcDo6WmlwPEEsQj4gYXMgY29yZTo6aXRlcjo6YWRhcHRlcnM6OnppcDo6WmlwSW1wbDxBLEI+Pjo6bmV3OjpoYWIxODVmMTdmNGJlMWM3YpcBRHdhc21fYmluZGdlbl9mdXR1cmVzOjpxdWV1ZTo6UXVldWU6OnNjaGVkdWxlX3Rhc2s6OmgxNDdiOTFiODI5ZTQ2ODRlmAE4c3RkOjp0aHJlYWQ6OmxvY2FsOjpMb2NhbEtleTxUPjo6d2l0aDo6aDM0OTg1OWVmYTRhNzQwYWKZATBhbGxvYzo6cmM6OlJjPFQsQT46OmRyb3Bfc2xvdzo6aDVlNWIwNjEwOWI3ZDc1OWSaAT93YXNtX2JpbmRnZW46OmNvbnZlcnQ6OmNsb3N1cmVzOjppbnZva2UzX211dDo6aDQ0ZjJmMDRhMWMyZjliY2SbARVfX3diZ19ub2N0dWFjb3JlX2ZyZWWcATFub2N0dWFfY29yZTo6Tm9jdHVhQ29yZTo6c3RhcnQ6OmhiNjc1YzEwMzg4NTY5Nzc0nQE5YWxsb2M6OnZlYzo6VmVjPFQsQT46OmludG9fYm94ZWRfc2xpY2U6OmgyYjY2NzViNmFhNjBjYmY5ngE5YWxsb2M6OnZlYzo6VmVjPFQsQT46OmludG9fYm94ZWRfc2xpY2U6Omg2MzI1MDUwMmI4MjZjMTI3nwE2Y29yZTo6cGFuaWNraW5nOjpwYW5pY19ib3VuZHNfY2hlY2s6OmhjYzQ3YTFkZDIwNDE5YWExoAE7Y29yZTo6Zm10OjpidWlsZGVyczo6RGVidWdTdHJ1Y3Q6OmZpbmlzaDo6aGNkNmEzYzJmNDhkNWYyNGOhAVJjb3JlOjpzbGljZTo6aW5kZXg6OnNsaWNlX2VuZF9pbmRleF9sZW5fZmFpbDo6ZG9fcGFuaWM6OnJ1bnRpbWU6Omg4NGM2Y2M0MTRkMTc0ZjhjogFQY29yZTo6c2xpY2U6OmluZGV4OjpzbGljZV9pbmRleF9vcmRlcl9mYWlsOjpkb19wYW5pYzo6cnVudGltZTo6aGVlNTYxOTE4MjEzYzYyZjOjAThzdGQ6OnRocmVhZDo6bG9jYWw6OkxvY2FsS2V5PFQ+Ojp3aXRoOjpoM2UyMzg0ODc5M2Q4MTE5NKQBN3N0ZDo6YWxsb2M6OmRlZmF1bHRfYWxsb2NfZXJyb3JfaG9vazo6aDUwNjI0MDYzNmM3OWM0YmWlATw8VCBhcyBhbGxvYzo6c3RyaW5nOjpUb1N0cmluZz46OnRvX3N0cmluZzo6aGU0ZjQ2MmVkMWVkZDlhMmKmATw8VCBhcyBhbGxvYzo6c3RyaW5nOjpUb1N0cmluZz46OnRvX3N0cmluZzo6aGU0ZjQ2MmVkMWVkZDlhMmKnAWE8VCBhcyB3YXNtX2JpbmRnZW46OmNsb3N1cmU6Oldhc21DbG9zdXJlRm5PbmNlPEEsUj4+OjppbnRvX2ZuX211dDo6e3tjbG9zdXJlfX06Omg4OTVhYTE1OThiM2ZjNTJmqAFhPFQgYXMgd2FzbV9iaW5kZ2VuOjpjbG9zdXJlOjpXYXNtQ2xvc3VyZUZuT25jZTxBLFI+Pjo6aW50b19mbl9tdXQ6Ont7Y2xvc3VyZX19OjpoZjUyNzFlMmZhYTYyNzJkOKkBazxub2N0dWFfY29yZTo6Tm9jdHVhQ29yZSBhcyB3YXNtX2JpbmRnZW46OmNvbnZlcnQ6OnRyYWl0czo6UmVmRnJvbVdhc21BYmk+OjpyZWZfZnJvbV9hYmk6OmgzNjY3MGYwNTM2Y2IxMjVhqgFbd2FzbV9iaW5kZ2VuX2Z1dHVyZXM6OnRhc2s6OnNpbmdsZXRocmVhZDo6VGFzazo6aW50b19yYXdfd2FrZXI6OnJhd193YWtlOjpoMWU0NzU4M2M4Yzk3ZTMxNqsBCV9fbHNocnRpM6wBRGNvcmU6OnB0cjo6ZHJvcF9pbl9wbGFjZTxub2N0dWFfY29yZTo6Tm9jdHVhQ29yZT46OmhiZDdkMzJmY2MwODg5ZDk4rQFPPGFsbG9jOjpzdHJpbmc6OlN0cmluZyBhcyBjb3JlOjpvcHM6OmluZGV4OjpJbmRleDxJPj46OmluZGV4OjpoNTRlZTM3MDk0ZjVlMGNiNa4BTzxhbGxvYzo6c3RyaW5nOjpTdHJpbmcgYXMgY29yZTo6b3BzOjppbmRleDo6SW5kZXg8ST4+OjppbmRleDo6aDU0ZWUzNzA5NGY1ZTBjYjWvAQ1fX3JkbF9kZWFsbG9jsAFIY29yZTo6b3BzOjpmdW5jdGlvbjo6Rm5PbmNlOjpjYWxsX29uY2V7e3Z0YWJsZS5zaGltfX06OmhkZGQ1OGUwMjQ2MzlmMGU3sQFIY29yZTo6b3BzOjpmdW5jdGlvbjo6Rm5PbmNlOjpjYWxsX29uY2V7e3Z0YWJsZS5zaGltfX06OmhmNDE5YWMwNDZiMDkwMmNlsgFKYWxsb2M6OmNvbGxlY3Rpb25zOjp2ZWNfZGVxdWU6OlZlY0RlcXVlPFQsQT46OnB1c2hfYmFjazo6aGRmM2U1ZjZiMjM1MTkxM2GzASdqc19zeXM6OlJlZmxlY3Q6OnNldDo6aDQ5OGNhZTM2MDU2NzEwNGG0AThzdGQ6OnRocmVhZDo6bG9jYWw6OkxvY2FsS2V5PFQ+Ojp3aXRoOjpoZDg2ODQxMjk3MWM3M2M2Y7UBTzxhbGxvYzo6c3RyaW5nOjpTdHJpbmcgYXMgY29yZTo6b3BzOjppbmRleDo6SW5kZXg8ST4+OjppbmRleDo6aDZiNDBhZmRiNmE1MDM1ODi2AU88YWxsb2M6OnN0cmluZzo6U3RyaW5nIGFzIGNvcmU6Om9wczo6aW5kZXg6OkluZGV4PEk+Pjo6aW5kZXg6Omg2YjQwYWZkYjZhNTAzNTg4twFIY29yZTo6b3BzOjpmdW5jdGlvbjo6Rm5PbmNlOjpjYWxsX29uY2V7e3Z0YWJsZS5zaGltfX06OmhjNjJjODA3MjFiYTFmNmNluAEzd2FzbV9iaW5kZ2VuOjpKc1ZhbHVlOjphc19zdHJpbmc6Omg5ZmI3MTE1ODI3ZDJjMWZiuQEzd2FzbV9iaW5kZ2VuOjpKc1ZhbHVlOjphc19zdHJpbmc6Omg5ZmI3MTE1ODI3ZDJjMWZiugEoYWxsb2M6OnJjOjpSYzxUPjo6bmV3OjpoMjk0YTM1ZWQ1Y2Y2NDFiYbsBKGFsbG9jOjpyYzo6UmM8VD46Om5ldzo6aDVlNWE0MzQ2MTM4Y2NhOGK8ASpqc19zeXM6OkZ1bmN0aW9uOjpjYWxsMjo6aGMxYTA1Y2VhN2ExNDJiNmO9AQ5ub2N0dWFjb3JlX25ld74BfDxhbGxvYzo6dmVjOjpWZWM8VCxBPiBhcyBhbGxvYzo6dmVjOjpzcGVjX2V4dGVuZDo6U3BlY0V4dGVuZDwmVCxjb3JlOjpzbGljZTo6aXRlcjo6SXRlcjxUPj4+OjpzcGVjX2V4dGVuZDo6aGRhYzhhNWMwODc0NDlmM2W/ATlzdGQ6OnRocmVhZDo6bG9jYWw6OnBhbmljX2FjY2Vzc19lcnJvcjo6aDNmYzkzYzI4MzViNzkxNmHAATVjb3JlOjpjZWxsOjpwYW5pY19hbHJlYWR5X2JvcnJvd2VkOjpoNGU0OGI4ZGY2NDFhNjI5YcEBPWNvcmU6OmNlbGw6OnBhbmljX2FscmVhZHlfbXV0YWJseV9ib3Jyb3dlZDo6aGYxYmQ4MDEwNTNjYjEwYWHCAQZtZW1jbXDDASpqc19zeXM6OkZ1bmN0aW9uOjpjYWxsMTo6aDhiMmJlN2IwN2I1ZDI0ZjDEAThzdGQ6OnRocmVhZDo6bG9jYWw6OkxvY2FsS2V5PFQ+Ojp3aXRoOjpoMzA3M2Y5YjZhOWUxYTg0OMUBcmNvcmU6OnB0cjo6ZHJvcF9pbl9wbGFjZTxjb3JlOjpvcHRpb246Ok9wdGlvbjx3YXNtX2JpbmRnZW5fZnV0dXJlczo6dGFzazo6c2luZ2xldGhyZWFkOjpJbm5lcj4+OjpoYWNjNmU2OGQ1YjIyM2QzY8YBMW5vY3R1YV9jb3JlOjpOb2N0dWFDb3JlOjpjbG9uZTo6aGE5MWFjNWYyOWU5ZjQ5ZTjHAY0BPDxhbGxvYzo6Y29sbGVjdGlvbnM6OnZlY19kZXF1ZTo6VmVjRGVxdWU8VCxBPiBhcyBjb3JlOjpvcHM6OmRyb3A6OkRyb3A+Ojpkcm9wOjpEcm9wcGVyPFQ+IGFzIGNvcmU6Om9wczo6ZHJvcDo6RHJvcD46OmRyb3A6OmhiNWIxNTQ0ODU0NGIxYzQxyAFoY29yZTo6cHRyOjpkcm9wX2luX3BsYWNlPGNvcmU6OnJlc3VsdDo6UmVzdWx0PCgpLHdhc21fYmluZGdlbl9mdXR1cmVzOjpxdWV1ZTo6UXVldWU+Pjo6aGY0M2MyOTlkNDI4NDZlOGPJASpqc19zeXM6OkZ1bmN0aW9uOjpjYWxsMDo6aDE3MzRkMWU3YTVjYmQ5YjPKAUk8YWxsb2M6OnN0cmluZzo6U3RyaW5nIGFzIGNvcmU6OmZtdDo6V3JpdGU+Ojp3cml0ZV9zdHI6OmhhZDBlYjU0NTFkMmQxZjliywFUPGNvcmU6OmZtdDo6YnVpbGRlcnM6OlBhZEFkYXB0ZXIgYXMgY29yZTo6Zm10OjpXcml0ZT46OndyaXRlX2NoYXI6Omg2MGQ5NThlYTJiNTE3ZGJizAFid2FzbV9iaW5kZ2VuX2Z1dHVyZXM6OnRhc2s6OnNpbmdsZXRocmVhZDo6VGFzazo6aW50b19yYXdfd2FrZXI6OnJhd193YWtlX2J5X3JlZjo6aDljYmU2Mjc0MmViZWIxOGHNAThhbGxvYzo6cmF3X3ZlYzo6UmF3VmVjPFQsQT46Omdyb3dfb25lOjpoNWMyY2Y1ZjljY2RlY2UxMM4BNmpzX3N5czo6VWludDhBcnJheTo6cmF3X2NvcHlfdG9fcHRyOjpoYjc5OTg0YTBiYTZmMmNiYs8BOXdhc21fYmluZGdlbjo6ZXh0ZXJucmVmOjpTbGFiOjpkZWFsbG9jOjpoYTFkZWY3MjRhNDU5NjI1ZtABEl9fcmRsX2FsbG9jX3plcm9lZNEBUWFsbG9jOjpyYXdfdmVjOjpSYXdWZWNJbm5lcjxBPjo6cmVzZXJ2ZTo6ZG9fcmVzZXJ2ZV9hbmRfaGFuZGxlOjpoNzVmMWNjNTYwZDRjMmJhY9IBMmdldHJhbmRvbTo6ZXJyb3I6OmludGVybmFsX2Rlc2M6OmhmNzQzMzYyZDVkNmJmYjE40wE4c3RkOjpwYW5pY2tpbmc6OnBhbmljX2NvdW50OjppbmNyZWFzZTo6aGE1YjE3MzQxZjk1MWUzMTnUAXE8c3RkOjpwYW5pY2tpbmc6OmJlZ2luX3BhbmljX2hhbmRsZXI6OlN0YXRpY1N0clBheWxvYWQgYXMgY29yZTo6cGFuaWM6OlBhbmljUGF5bG9hZD46OnRha2VfYm94OjpoNzdjYzA0NjMyMWQ3ZTU5ZNUBSTxhbGxvYzo6c3RyaW5nOjpTdHJpbmcgYXMgY29yZTo6Zm10OjpXcml0ZT46OndyaXRlX3N0cjo6aGFkMGViNTQ1MWQyZDFmOWLWAYgBd2FzbV9iaW5kZ2VuOjpjb252ZXJ0OjpzbGljZXM6OjxpbXBsIHdhc21fYmluZGdlbjo6Y29udmVydDo6dHJhaXRzOjpGcm9tV2FzbUFiaSBmb3IgYWxsb2M6OmJveGVkOjpCb3g8W1RdPj46OmZyb21fYWJpOjpoYmNjNWFmZDVhZGQxMGNmNNcBOHN0ZDo6dGhyZWFkOjpsb2NhbDo6TG9jYWxLZXk8VD46OndpdGg6OmhkOTcyYjEwNDZiMTgxNzIw2AFbd2FzbV9iaW5kZ2VuX2Z1dHVyZXM6OnRhc2s6OnNpbmdsZXRocmVhZDo6VGFzazo6aW50b19yYXdfd2FrZXI6OnJhd19kcm9wOjpoNjU2MGY0MDFlMGEzMWZlN9kBOXdhc21fYmluZGdlbjo6Y2xvc3VyZTo6Q2xvc3VyZTxUPjo6bmV3OjpoMTAxZTk2M2JkYTJhNjI1NtoBMGFsbG9jOjpyYzo6UmM8VCxBPjo6ZHJvcF9zbG93OjpoZjZhODA5MGJmMDg5ZjM5ONsBNm9uY2VfY2VsbDo6dW5zeW5jOjpPbmNlQ2VsbDxUPjo6c2V0OjpoNTY2MTRhYjZjNjRhMGI1N9wBKWNvcmU6OnBhbmlja2luZzo6cGFuaWM6OmgzM2U1ZGVlMGMyZTc4MTg03QFsY29yZTo6cHRyOjpkcm9wX2luX3BsYWNlPG5vY3R1YV9jb3JlOjpOb2N0dWFDb3JlOjpzY2hlZHVsZV9jb2xsZWN0aW9uX2FzeW5jOjp7e2Nsb3N1cmV9fT46Omg3YjVhOTJkM2ZhYmRkOTEx3gFsY29yZTo6cHRyOjpkcm9wX2luX3BsYWNlPG5vY3R1YV9jb3JlOjpOb2N0dWFDb3JlOjpzY2hlZHVsZV9jb2xsZWN0aW9uX2FzeW5jOjp7e2Nsb3N1cmV9fT46Omg3YjVhOTJkM2ZhYmRkOTEx3wERX193YmluZGdlbl9tYWxsb2PgARBub2N0dWFjb3JlX3N0YXJ04QE4c3RkOjp0aHJlYWQ6OmxvY2FsOjpMb2NhbEtleTxUPjo6d2l0aDo6aDg5MDhlYWQ4Y2Q1MzNkMTfiATd3YXNtX2JpbmRnZW46OkpzVGhyZWFkTG9jYWw8VD46OndpdGg6OmhmMTk2Nzk1MmU4OWMwNDFm4wFIPGRsbWFsbG9jOjpzeXM6OlN5c3RlbSBhcyBkbG1hbGxvYzo6QWxsb2NhdG9yPjo6YWxsb2M6Omg3ODU0ZWE0OTNiMmUzMzli5AEdbm9jdHVhY29yZV9uZXcgZXh0ZXJucmVmIHNoaW3lAVU8VCBhcyB3YXNtX2JpbmRnZW46OmNsb3N1cmU6Oldhc21DbG9zdXJlRm5PbmNlPCgpLFI+Pjo6aW50b19mbl9tdXQ6Omg5OGVjNGM2ZTYyZTI3NDNh5gEVbm9jdHVhY29yZV9pc19zdWNjZXNz5wEdbm9jdHVhY29yZV9nZXRfY29sbGVjdGVkX2RhdGHoAYQBY29yZTo6cHRyOjpkcm9wX2luX3BsYWNlPGFsbG9jOjpib3hlZDo6Qm94PGR5biBjb3JlOjpvcHM6OmZ1bmN0aW9uOjpGbk11dDwod2FzbV9iaW5kZ2VuOjpKc1ZhbHVlLCk+K091dHB1dCA9ICgpPj46OmhiYzI3NzVhNTRmNTRkMGM06QEZX19leHRlcm5yZWZfdGFibGVfZGVhbGxvY+oBQ2NvcmU6OmZtdDo6Rm9ybWF0dGVyOjpwYWRfaW50ZWdyYWw6OndyaXRlX3ByZWZpeDo6aGM2NWU2YjIwOWE4ZDZmMzXrAV9jb3JlOjpwdHI6OmRyb3BfaW5fcGxhY2U8d2FzbV9iaW5kZ2VuOjpfX3J0OjpSY1JlZjxub2N0dWFfY29yZTo6Tm9jdHVhQ29yZT4+OjpoNWM2M2VhZWY4ZTMzNWM4ZOwBKGFsbG9jOjpyYzo6UmM8VD46Om5ldzo6aDc0MmEwZTllZjE2NjRmNjLtAXs8ZHluIGNvcmU6Om9wczo6ZnVuY3Rpb246OkZuTXV0PCgpPitPdXRwdXQgPSBSIGFzIHdhc21fYmluZGdlbjo6Y2xvc3VyZTo6V2FzbUNsb3N1cmU+OjpkZXNjcmliZTo6ZGVzdHJveTo6aGIyYjM4YzJmY2EyYjQwYTLuAThzdGQ6OnRocmVhZDo6bG9jYWw6OkxvY2FsS2V5PFQ+Ojp3aXRoOjpoNDIwMmMzNmJhOTc2OGJkYu8BfTxkeW4gY29yZTo6b3BzOjpmdW5jdGlvbjo6Rm5NdXQ8KEEsKT4rT3V0cHV0ID0gUiBhcyB3YXNtX2JpbmRnZW46OmNsb3N1cmU6Oldhc21DbG9zdXJlPjo6ZGVzY3JpYmU6OmRlc3Ryb3k6Omg4ZDEzMDViY2UwMWEzMzMw8AEzanNfc3lzOjpVaW50OEFycmF5Ojp2aWV3X211dF9yYXc6OmgzMDkxM2E5Nzg3MTg1MThk8QE0YWxsb2M6OnJhd192ZWM6OmNhcGFjaXR5X292ZXJmbG93OjpoOGNlZDRlYTUxMjYwZGMyMPIBSGNvcmU6OnBhbmlja2luZzo6cGFuaWNfY29uc3Q6OnBhbmljX2NvbnN0X2Rpdl9ieV96ZXJvOjpoZDZjZmJkN2ExZWQwNDQ4Y/MBTWNvcmU6OnBhbmlja2luZzo6cGFuaWNfY29uc3Q6OnBhbmljX2NvbnN0X2FzeW5jX2ZuX3Jlc3VtZWQ6Omg5MGZlODhjOWQzYWJmZDc49AE4c3RkOjp0aHJlYWQ6OmxvY2FsOjpMb2NhbEtleTxUPjo6d2l0aDo6aDE2YWYzZTgwZTkxNDllM2L1AThzdGQ6OnRocmVhZDo6bG9jYWw6OkxvY2FsS2V5PFQ+Ojp3aXRoOjpoYzAxYzM3ZGViODYxZDAyYvYBTDxjb3JlOjphcnJheTo6VHJ5RnJvbVNsaWNlRXJyb3IgYXMgY29yZTo6Zm10OjpEZWJ1Zz46OmZtdDo6aGRkMjk5NDcwZGQ0NmU1Zjb3ATJnZXRyYW5kb206OmltcDo6Z2V0cmFuZG9tX2lubmVyOjpoN2IyY2E5Njk2YTQ5MTAxZPgBVDxUIGFzIHdhc21fYmluZGdlbjo6Y2xvc3VyZTo6V2FzbUNsb3N1cmVGbk9uY2U8QSxSPj46OmludG9fZm5fbXV0OjpoMjIxMzA5OTViZDU1MzE3MfkBVDxUIGFzIHdhc21fYmluZGdlbjo6Y2xvc3VyZTo6V2FzbUNsb3N1cmVGbk9uY2U8QSxSPj46OmludG9fZm5fbXV0OjpoYzBmZDFjYzZmODUwOTM4OPoBEl9fd2JpbmRnZW5fcmVhbGxvY/sBTTxzdGQ6OnRocmVhZDo6bG9jYWw6OkFjY2Vzc0Vycm9yIGFzIGNvcmU6OmZtdDo6RGVidWc+OjpmbXQ6OmgxYWE4ZmM2YWE3YTJiODQ4/AEWX19leHRlcm5yZWZfZHJvcF9zbGljZf0BXHdhc21fYmluZGdlbl9mdXR1cmVzOjp0YXNrOjpzaW5nbGV0aHJlYWQ6OlRhc2s6OmludG9fcmF3X3dha2VyOjpyYXdfY2xvbmU6OmhhMzg5YjM1ZjM2MjkyMDg1/gFIY29yZTo6b3BzOjpmdW5jdGlvbjo6Rm5PbmNlOjpjYWxsX29uY2V7e3Z0YWJsZS5zaGltfX06Omg3Njk5MzAwMTA2MjU2ODAz/wFIY29yZTo6b3BzOjpmdW5jdGlvbjo6Rm5PbmNlOjpjYWxsX29uY2V7e3Z0YWJsZS5zaGltfX06Omg3Njk5MzAwMTA2MjU2ODAzgAIRcnVzdF9iZWdpbl91bndpbmSBAi1jb3JlOjpwYW5pY2tpbmc6OnBhbmljX2ZtdDo6aDYxOTYwMGVmY2UyZGEyNzaCAmpjb3JlOjpwdHI6OmRyb3BfaW5fcGxhY2U8Y29yZTo6cmVzdWx0OjpSZXN1bHQ8KCksY29yZTo6b3B0aW9uOjpPcHRpb248anNfc3lzOjpPYmplY3Q+Pj46Omg1NTllZTJmNzJjOWJkZDdkgwI1Y29yZTo6Zm10OjpGb3JtYXR0ZXI6OmRlYnVnX3N0cnVjdDo6aGUzYmZjNDE3YmY1ZmNjMmKEAuoBY29yZTo6cHRyOjpkcm9wX2luX3BsYWNlPDw8d2FzbV9iaW5kZ2VuX2Z1dHVyZXM6OkpzRnV0dXJlIGFzIGNvcmU6OmNvbnZlcnQ6OkZyb208anNfc3lzOjpQcm9taXNlPj46OmZyb206Ont7Y2xvc3VyZX19IGFzIHdhc21fYmluZGdlbjo6Y2xvc3VyZTo6V2FzbUNsb3N1cmVGbk9uY2U8d2FzbV9iaW5kZ2VuOjpKc1ZhbHVlLCgpPj46OmludG9fZm5fbXV0Ojp7e2Nsb3N1cmV9fT46Omg0ZjhhYWVkYzU3Mjg0M2Q4hQI6d2FzbV9iaW5kZ2VuOjpfX3J0Ojp0YWtlX2xhc3RfZXhjZXB0aW9uOjpoZGI5MTEwNDJmMzUxOGU2MYYCTmNvcmU6OmZtdDo6bnVtOjppbXA6OjxpbXBsIGNvcmU6OmZtdDo6RGlzcGxheSBmb3IgaTMyPjo6Zm10OjpoNjMzMmI1MzQ2ZmQ2NmE5ZocCMGFsbG9jOjphbGxvYzo6ZXhjaGFuZ2VfbWFsbG9jOjpoOWUwNmIyOWFjNmFiZmZkYogCMGFsbG9jOjphbGxvYzo6ZXhjaGFuZ2VfbWFsbG9jOjpoOWUwNmIyOWFjNmFiZmZkYokCIG5vY3R1YWNvcmVfc3RhcnQgbXVsdGl2YWx1ZSBzaGltigJCd2FzbV9iaW5kZ2VuOjpjb252ZXJ0Ojp0cmFpdHM6Oldhc21SZXQ8VD46OmpvaW46OmgxNTRkZWU5ZmY2MTNhY2QwiwI/d2FzbV9iaW5kZ2VuOjpjb252ZXJ0OjpjbG9zdXJlczo6aW52b2tlNF9tdXQ6Omg3NDAxY2E5NGViMTA1YTRmjAJDY29yZTo6YWxsb2M6OmxheW91dDo6TGF5b3V0Ojppc19zaXplX2FsaWduX3ZhbGlkOjpoODZkZmJkMDBkYzg4ZWE0N40CKl9fd2JnX2NhbGxfODMzYmVkNTc3MGVhMjA0MSBleHRlcm5yZWYgc2hpbY4CYWNvcmU6OnB0cjo6ZHJvcF9pbl9wbGFjZTx3YXNtX2JpbmRnZW5fZnV0dXJlczo6cXVldWU6OlF1ZXVlOjpuZXc6Ont7Y2xvc3VyZX19Pjo6aGNhMDQxN2M3YTY3YjgwZDOPAj93YXNtX2JpbmRnZW46OmNvbnZlcnQ6OmNsb3N1cmVzOjppbnZva2UzX211dDo6aDNmMzNhNjYxZmRjMWIyNTKQAj93YXNtX2JpbmRnZW46OmNvbnZlcnQ6OmNsb3N1cmVzOjppbnZva2UzX211dDo6aDQ2MTI1MDdiOGM3ZWY4OTCRAj93YXNtX2JpbmRnZW46OmNvbnZlcnQ6OmNsb3N1cmVzOjppbnZva2UzX211dDo6aDRjNzRlOGQwMzdkYzEwMmOSAj93YXNtX2JpbmRnZW46OmNvbnZlcnQ6OmNsb3N1cmVzOjppbnZva2UzX211dDo6aDVmZjI1MzEwYjkyYzQ1MWOTAj93YXNtX2JpbmRnZW46OmNvbnZlcnQ6OmNsb3N1cmVzOjppbnZva2UzX211dDo6aDlmMDM5ZWNjNGY0ZWExMGaUAj93YXNtX2JpbmRnZW46OmNvbnZlcnQ6OmNsb3N1cmVzOjppbnZva2UzX211dDo6aGE4YWQzZjMyNzA2ODMxODOVAj93YXNtX2JpbmRnZW46OmNvbnZlcnQ6OmNsb3N1cmVzOjppbnZva2UzX211dDo6aGI1NmQ2MDZkYjg1NTRjNDeWAj93YXNtX2JpbmRnZW46OmNvbnZlcnQ6OmNsb3N1cmVzOjppbnZva2UzX211dDo6aGNlYmZhZDZhNTc1OTg1NmOXAkY8W0FdIGFzIGNvcmU6OnNsaWNlOjpjbXA6OlNsaWNlUGFydGlhbEVxPEI+Pjo6ZXF1YWw6Omg1NmVjNjE4M2E0OGE1MzNjmAJlY29yZTo6cHRyOjpkcm9wX2luX3BsYWNlPHN0ZDo6cGFuaWNraW5nOjpiZWdpbl9wYW5pY19oYW5kbGVyOjpGb3JtYXRTdHJpbmdQYXlsb2FkPjo6aDM3ZDgyYTQxN2I2ODk4NTCZAtUBY29yZTo6cHRyOjpkcm9wX2luX3BsYWNlPDxub2N0dWFfY29yZTo6Tm9jdHVhQ29yZTo6c2NoZWR1bGVfY29sbGVjdGlvbl9hc3luYzo6e3tjbG9zdXJlfX06Ont7Y2xvc3VyZX19Ojp7e2Nsb3N1cmV9fSBhcyB3YXNtX2JpbmRnZW46OmNsb3N1cmU6Oldhc21DbG9zdXJlRm5PbmNlPCgpLCgpPj46OmludG9fZm5fbXV0Ojp7e2Nsb3N1cmV9fT46OmhkYmZhNzgxZDM1MDU2MTlkmgJ6PGNvcmU6OnNsaWNlOjppdGVyOjpDaHVua3NFeGFjdDxUPiBhcyBjb3JlOjppdGVyOjp0cmFpdHM6Oml0ZXJhdG9yOjpJdGVyYXRvcj46Ol9faXRlcmF0b3JfZ2V0X3VuY2hlY2tlZDo6aDcyNDU1ZGZjMTU2MmZlNTObAj93YXNtX2JpbmRnZW46OmNvbnZlcnQ6OmNsb3N1cmVzOjppbnZva2UxX211dDo6aDU1MWQxMTlhY2JlMmRjZTacAnw8ZHluIGNvcmU6Om9wczo6ZnVuY3Rpb246OkZuTXV0PChBLCk+K091dHB1dCA9IFIgYXMgd2FzbV9iaW5kZ2VuOjpjbG9zdXJlOjpXYXNtQ2xvc3VyZT46OmRlc2NyaWJlOjppbnZva2U6OmgxZWI4NmMxYzk5ZjIwZDg5nQJUY29yZTo6cHRyOjpkcm9wX2luX3BsYWNlPGNvcmU6OnJlc3VsdDo6UmVzdWx0PCgpLGpzX3N5czo6T2JqZWN0Pj46OmhmMjNiZGY3OWY4NmY0ZmRhngI/d2FzbV9iaW5kZ2VuOjpjb252ZXJ0OjpjbG9zdXJlczo6aW52b2tlMl9tdXQ6Omg5NmM3NGMxODgyYzBiMGEwnwIqX193YmdfY2FsbF83Y2NjZGQ2OWUwNzkxYWUyIGV4dGVybnJlZiBzaGltoAIqX193YmdfdGhlbl80OGI0MDY3NDk4NzhhNTMxIGV4dGVybnJlZiBzaGltoQIYY2xvc3VyZTUyIGV4dGVybnJlZiBzaGltogJYY29yZTo6cHRyOjpkcm9wX2luX3BsYWNlPG5vY3R1YV9jb3JlOjpOb2N0dWFDb3JlOjpzdGFydDo6e3tjbG9zdXJlfX0+OjpoN2MyMjg3NjNlMDg4ODFjOKMCP3dhc21fYmluZGdlbjo6Y29udmVydDo6Y2xvc3VyZXM6Omludm9rZTBfbXV0OjpoMjE2ZGUwYzMzYzI5ZGFkOaQCejxkeW4gY29yZTo6b3BzOjpmdW5jdGlvbjo6Rm5NdXQ8KCk+K091dHB1dCA9IFIgYXMgd2FzbV9iaW5kZ2VuOjpjbG9zdXJlOjpXYXNtQ2xvc3VyZT46OmRlc2NyaWJlOjppbnZva2U6OmhhMWNjOGZhMWVkZDhhZTRmpQJHd2FzbV9iaW5kZ2VuX2Z1dHVyZXM6OnF1ZXVlOjpRdWV1ZTo6bmV3Ojp7e2Nsb3N1cmV9fTo6aDkxYWNhYmUyYTE1MTNkZDCmAj93YXNtX2JpbmRnZW46OmNvbnZlcnQ6OmNsb3N1cmVzOjppbnZva2UxX211dDo6aDZjMDkxMmEwZDdlMjdmMTGnAjljb3JlOjpvcHM6OmZ1bmN0aW9uOjpGbk9uY2U6OmNhbGxfb25jZTo6aDhlZjU4ZGFhNzk3Y2YwNjGoAjljb3JlOjpvcHM6OmZ1bmN0aW9uOjpGbk9uY2U6OmNhbGxfb25jZTo6aDhlZjU4ZGFhNzk3Y2YwNjGpAjljb3JlOjpvcHM6OmZ1bmN0aW9uOjpGbk9uY2U6OmNhbGxfb25jZTo6aDI5MGU1ZWJjNWI2MzlmNDiqAo4CY29yZTo6cHRyOjpkcm9wX2luX3BsYWNlPGNvcmU6Om9wdGlvbjo6T3B0aW9uPCh3YXNtX2JpbmRnZW46OmNsb3N1cmU6OkNsb3N1cmU8ZHluIGNvcmU6Om9wczo6ZnVuY3Rpb246OkZuTXV0PCh3YXNtX2JpbmRnZW46OkpzVmFsdWUsKT4rT3V0cHV0ID0gKCk+LHdhc21fYmluZGdlbjo6Y2xvc3VyZTo6Q2xvc3VyZTxkeW4gY29yZTo6b3BzOjpmdW5jdGlvbjo6Rm5NdXQ8KHdhc21fYmluZGdlbjo6SnNWYWx1ZSwpPitPdXRwdXQgPSAoKT4pPj46OmgzNDgxODJmYzI2Zjg4OGNiqwI3d2FzbV9iaW5kZ2VuOjpjYXN0OjpKc0Nhc3Q6OmR5bl9pbnRvOjpoNmFkYzBkMDM5OTdmZDIwNawCdWNvcmU6OnB0cjo6ZHJvcF9pbl9wbGFjZTxjb3JlOjpyZXN1bHQ6OlJlc3VsdDwoKSxjb3JlOjpjZWxsOjpDZWxsPHdhc21fYmluZGdlbjo6ZXh0ZXJucmVmOjpTbGFiPj4+OjpoYzI1M2E4NzMzYmE5NTljOK0CQmNvcmU6OnB0cjo6ZHJvcF9pbl9wbGFjZTxhbGxvYzo6c3RyaW5nOjpTdHJpbmc+OjpoMjBmNzQ3ZmVhNTIwNzZiNq4CC19fcmRsX2FsbG9jrwJCY29yZTo6cHRyOjpkcm9wX2luX3BsYWNlPGFsbG9jOjpzdHJpbmc6OlN0cmluZz46OmhmY2M2MWU1N2Y3NzQwMDk4sAIqX193YmdfY2FsbF82NzJhNGQyMTYzNGQ0YTI0IGV4dGVybnJlZiBzaGltsQIqX193YmdfdGhlbl80NGI3Mzk0NmQyZmIzZTdkIGV4dGVybnJlZiBzaGltsgJAX193YmdfbmV3d2l0aGJ5dGVvZmZzZXRhbmRsZW5ndGhfZDk3ZTYzN2ViZTE0NWE5YSBleHRlcm5yZWYgc2hpbbMCLl9fd2JnX3N1YmFycmF5X2FhOTA2NWZhOWRjNWRmOTYgZXh0ZXJucmVmIHNoaW20AghfX3JnX29vbbUCL2FsbG9jOjpyYXdfdmVjOjpoYW5kbGVfZXJyb3I6OmgzZDNjNmMxMjQyNmYwYzBitgIsX193YmluZGdlbl9jbG9zdXJlX3dyYXBwZXIxMTEgZXh0ZXJucmVmIHNoaW23AixfX3diaW5kZ2VuX2Nsb3N1cmVfd3JhcHBlcjE4NyBleHRlcm5yZWYgc2hpbbgCQmNvcmU6OnB0cjo6ZHJvcF9pbl9wbGFjZTx3YXNtX2JpbmRnZW46OkpzVmFsdWU+OjpoMzNlNzUxNGU0MGJiNjcwNrkCQmNvcmU6OnB0cjo6ZHJvcF9pbl9wbGFjZTx3YXNtX2JpbmRnZW46OkpzVmFsdWU+OjpoMzNlNzUxNGU0MGJiNjcwNroCR2pzX3N5czo6Z2xvYmFsOjpnZXRfZ2xvYmFsX29iamVjdDo6R0xPQkFMX1RISVM6OmluaXQ6OmhlMDZjOWJlN2NiMDFhNjUzuwJAanNfc3lzOjpnbG9iYWw6OmdldF9nbG9iYWxfb2JqZWN0OjpTRUxGOjppbml0OjpoNjkzMzA2OTIxZjNjNjAxY7wCQmpzX3N5czo6Z2xvYmFsOjpnZXRfZ2xvYmFsX29iamVjdDo6V0lORE9XOjppbml0OjpoMGQ0NjYxNDNkMWQ5YmZkOL0CQmpzX3N5czo6Z2xvYmFsOjpnZXRfZ2xvYmFsX29iamVjdDo6R0xPQkFMOjppbml0OjpoMmMwZjExMzVlNjU5YTFmNb4CYDxhbGxvYzo6dmVjOjpWZWM8VCxBPiBhcyBjb3JlOjpjb252ZXJ0OjpGcm9tPGFsbG9jOjpib3hlZDo6Qm94PFtUXSxBPj4+Ojpmcm9tOjpoY2Q2MWI4YTJlZDQwYmNjMr8COWNvcmU6Om9wczo6ZnVuY3Rpb246OkZuT25jZTo6Y2FsbF9vbmNlOjpoM2U5NWRjOTJiYjM4NjU5NcACSmNvcmU6OmZtdDo6bnVtOjo8aW1wbCBjb3JlOjpmbXQ6OkRpc3BsYXkgZm9yIHUxMjg+OjpmbXQ6OmhiMjlhMTI4OWRjN2JjYWJmwQIkX193YmluZGdlbl9zdHJpbmdfbmV3IGV4dGVybnJlZiBzaGltwgIsX193YmdfY3J5cHRvXzU3NGU3OGFkOGIxM2I2NWYgZXh0ZXJucmVmIHNoaW3DAi1fX3diZ19wcm9jZXNzX2RjMGZiYWNjN2MxYzA2ZjcgZXh0ZXJucmVmIHNoaW3EAi5fX3diZ192ZXJzaW9uc19jMDFkZmQ0NzIyYTg4MTY1IGV4dGVybnJlZiBzaGltxQIqX193Ymdfbm9kZV85MDVkM2UyNTFlZGZmOGEyIGV4dGVybnJlZiBzaGltxgIuX193YmdfbXNDcnlwdG9fYTYxYWViMzVhMjRjMTMyOSBleHRlcm5yZWYgc2hpbccCNF9fd2JnX3F1ZXVlTWljcm90YXNrX2QzMjE5ZGVmODI1NTI0ODUgZXh0ZXJucmVmIHNoaW3IAi9fX3diZ19uZXdub2FyZ3NfMTA1ZWQ0NzE0NzVhYWY1MCBleHRlcm5yZWYgc2hpbckCKV9fd2JnX25ld18yM2EyNjY1ZmFjODNjNjExIGV4dGVybnJlZiBzaGltygItX193YmdfcmVzb2x2ZV80ODUxNzg1YzljNWY1NzNkIGV4dGVybnJlZiBzaGltywIsX193YmdfYnVmZmVyXzYwOWNjM2VlZTUxZWQxNTggZXh0ZXJucmVmIHNoaW3MAilfX3diZ19uZXdfYTEyMDAyYTdmOTFjNzViZSBleHRlcm5yZWYgc2hpbc0CDl9fcnVzdF9yZWFsbG9jzgJSY29yZTo6YXJyYXk6OjxpbXBsIGNvcmU6OmRlZmF1bHQ6OkRlZmF1bHQgZm9yIFtUOyAxNl0+OjpkZWZhdWx0OjpoNDExMTkwOTk2ZTY2OGJhZM8CVTx3YXNtX2JpbmRnZW46OmNsb3N1cmU6OkNsb3N1cmU8VD4gYXMgY29yZTo6b3BzOjpkcm9wOjpEcm9wPjo6ZHJvcDo6aGQ0MDhkOTliMDJkMDhhOTDQAhRfX3diaW5kZ2VuX2V4bl9zdG9yZdECRTxjb3JlOjpjZWxsOjpCb3Jyb3dFcnJvciBhcyBjb3JlOjpmbXQ6OkRlYnVnPjo6Zm10OjpoZjVkOTY0NWRhZmU0N2JhNdICSDxjb3JlOjpjZWxsOjpCb3Jyb3dNdXRFcnJvciBhcyBjb3JlOjpmbXQ6OkRlYnVnPjo6Zm10OjpoOWI3YmQxZTRhMjBkNDY4MdMCMmNvcmU6OmZtdDo6Rm9ybWF0dGVyOjp3cml0ZV9zdHI6OmhiZTU1NTg0ZWY2ZDdjZGNm1AIkX193YmluZGdlbl9udW1iZXJfbmV3IGV4dGVybnJlZiBzaGlt1QIzX193YmdfbmV3d2l0aGxlbmd0aF9hMzgxNjM0ZTkwYzI3NmQ0IGV4dGVybnJlZiBzaGlt1gIYY2xvc3VyZTQwIGV4dGVybnJlZiBzaGlt1wIpanNfc3lzOjpQcm9taXNlOjp0aGVuMjo6aDdiNjQxZDZkOTI2YjBkMWbYAjA8JlQgYXMgY29yZTo6Zm10OjpEZWJ1Zz46OmZtdDo6aDk2YmNmZGQxM2IzY2RiMjLZAi5jb3JlOjpzdHI6OnNsaWNlX2Vycm9yX2ZhaWw6OmgwNjc4NzA0YWQ1MDc5MDY42gItX193YmdfcmVxdWlyZV82MGNjNzQ3YTZiYzUyMTVhIGV4dGVybnJlZiBzaGlt2wI0X193YmdfcmFuZG9tRmlsbFN5bmNfYWMwOTg4YWJhMzI1NDI5MCBleHRlcm5yZWYgc2hpbdwCKV9fd2JnX25ld180MDVlMjJmMzkwNTc2Y2UyIGV4dGVybnJlZiBzaGlt3QIpX193Ymdfc2V0X2JiOGNlY2Y2YTYyYjlmNDYgZXh0ZXJucmVmIHNoaW3eAiBfX3diaW5kZ2VuX21lbW9yeSBleHRlcm5yZWYgc2hpbd8CRTxhbGxvYzo6c3RyaW5nOjpTdHJpbmcgYXMgY29yZTo6Zm10OjpEaXNwbGF5Pjo6Zm10OjpoODk5NjY4NTQ3NDZjYjdhNuACRTxhbGxvYzo6c3RyaW5nOjpTdHJpbmcgYXMgY29yZTo6Zm10OjpEaXNwbGF5Pjo6Zm10OjpoODk5NjY4NTQ3NDZjYjdhNuECMjwmVCBhcyBjb3JlOjpmbXQ6OkRpc3BsYXk+OjpmbXQ6Omg2MDljNzFjOWMxOWEyYjlk4gIMX19ydXN0X2FsbG9j4wITX19ydXN0X2FsbG9jX3plcm9lZOQCRTxhbGxvYzo6c3RyaW5nOjpTdHJpbmcgYXMgY29yZTo6Zm10OjpEaXNwbGF5Pjo6Zm10OjpoODk5NjY4NTQ3NDZjYjdhNuUCiAF3YXNtX2JpbmRnZW46OmNvbnZlcnQ6OnNsaWNlczo6PGltcGwgd2FzbV9iaW5kZ2VuOjpjb252ZXJ0Ojp0cmFpdHM6OkludG9XYXNtQWJpIGZvciBhbGxvYzo6Ym94ZWQ6OkJveDxbVF0+Pjo6aW50b19hYmk6OmgxNjFjZTM3NzM5NGQ5NTM35gIxPFQgYXMgY29yZTo6YW55OjpBbnk+Ojp0eXBlX2lkOjpoMjRkMTc4ZGZjN2YwODc5YecCMTxUIGFzIGNvcmU6OmFueTo6QW55Pjo6dHlwZV9pZDo6aDM4YWY3NzA0N2U0YjhlZjLoAmw8c3RkOjpwYW5pY2tpbmc6OmJlZ2luX3BhbmljX2hhbmRsZXI6OlN0YXRpY1N0clBheWxvYWQgYXMgY29yZTo6cGFuaWM6OlBhbmljUGF5bG9hZD46OmdldDo6aDM3NWEwNjRkZTE0ZTVmZDDpAmU8c3RkOjpwYW5pY2tpbmc6OmJlZ2luX3BhbmljX2hhbmRsZXI6OlN0YXRpY1N0clBheWxvYWQgYXMgY29yZTo6Zm10OjpEaXNwbGF5Pjo6Zm10OjpoZTRkODY2NTk4NDI0M2IzMuoCMjwmVCBhcyBjb3JlOjpmbXQ6OkRpc3BsYXk+OjpmbXQ6OmhjYjljNjJlZjQ1N2FhMjU56wIbX193YmluZGdlbl9vYmplY3RfY2xvbmVfcmVm7AIpX193Ymdfc2V0XzY1NTk1YmRkODY4YjMwMDkgZXh0ZXJucmVmIHNoaW3tAixub2N0dWFjb3JlX2dldF9jb2xsZWN0ZWRfZGF0YSBleHRlcm5yZWYgc2hpbe4CDl9fcnVzdF9kZWFsbG9j7wIhanNfc3lzOjpnbG9iYWw6Omg0N2JhZGNiOTJhNTliNDQw8AJfanNfc3lzOjpfOjo8aW1wbCB3YXNtX2JpbmRnZW46OmNhc3Q6OkpzQ2FzdCBmb3IganNfc3lzOjpQcm9taXNlPjo6aW5zdGFuY2VvZjo6aDE5ZDdjN2FjOWY1ODVkMTHxAihqc19zeXM6OlByb21pc2U6OnRoZW46Omg0YTU0NGQyZWY0NTNjYzg18gIvanNfc3lzOjpVaW50OEFycmF5OjpzdWJhcnJheTo6aGI3NDEwZjlhMTRiYjA2YTDzAgpydXN0X3Bhbmlj9AI/Y29yZTo6c2xpY2U6OmluZGV4OjpzbGljZV9lbmRfaW5kZXhfbGVuX2ZhaWw6OmhiMDAzZTI3NTZjZTljOTdj9QI9Y29yZTo6c2xpY2U6OmluZGV4OjpzbGljZV9pbmRleF9vcmRlcl9mYWlsOjpoZTk1NGY0NGVhMDM5NzQ2YvYCTmNvcmU6OmZtdDo6bnVtOjppbXA6OjxpbXBsIGNvcmU6OmZtdDo6RGlzcGxheSBmb3IgdTMyPjo6Zm10OjpoNTY1YThhNjk2ZGI0ZjQ3ZfcCLmNvcmU6Om9wdGlvbjo6dW53cmFwX2ZhaWxlZDo6aGVlZjRkY2IyNmQzMWVlY2T4AjVfX3diZ19nZXRSYW5kb21WYWx1ZXNfYjhmNWRiZDVmMzk5NWE5ZSBleHRlcm5yZWYgc2hpbfkCIV9fd2JpbmRnZW5fY2JfZHJvcCBleHRlcm5yZWYgc2hpbfoCTXdhc21fYmluZGdlbjo6Y2xvc3VyZTo6Q2xvc3VyZTxUPjo6d3JhcDo6YnJlYWtzX2lmX2lubGluZWQ6OmgxMDQwMmU2ZTllODA0MDYx+wIaX19ydXN0X2FsbG9jX2Vycm9yX2hhbmRsZXL8AjA8JlQgYXMgY29yZTo6Zm10OjpEZWJ1Zz46OmZtdDo6aGUyNzdhZTk3OWM5YThiMzL9Ak13YXNtX2JpbmRnZW46OmNsb3N1cmU6OkNsb3N1cmU8VD46OndyYXA6OmJyZWFrc19pZl9pbmxpbmVkOjpoNDJiZmNjYjI4OTEzZjllN/4CKndhc21fYmluZGdlbjo6dGhyb3dfc3RyOjpoZjc1MzQ3ZDEzOTRlMzYzZP8CTzxhbGxvYzo6cmF3X3ZlYzo6UmF3VmVjPFQsQT4gYXMgY29yZTo6b3BzOjpkcm9wOjpEcm9wPjo6ZHJvcDo6aDM2NjVhMzkwOWVkMDMxN2GAAzF3YXNtX2JpbmRnZW46Ol9fcnQ6OnRocm93X251bGw6Omg3MDUwOTRiMDBiZjdmNDVkgQMyd2FzbV9iaW5kZ2VuOjpfX3J0Ojpib3Jyb3dfZmFpbDo6aDk1MDUzYjcyZTMyMzY2NGWCAy5jb3JlOjpmbXQ6OldyaXRlOjp3cml0ZV9mbXQ6OmhhOGJkZWYyNzg5NjU1NGQzgwNvPHN0ZDo6cGFuaWNraW5nOjpiZWdpbl9wYW5pY19oYW5kbGVyOjpTdGF0aWNTdHJQYXlsb2FkIGFzIGNvcmU6OnBhbmljOjpQYW5pY1BheWxvYWQ+Ojphc19zdHI6Omg0YmY0YmIwMjg3Mjk2ZGFkhAMuY29yZTo6Zm10OjpXcml0ZTo6d3JpdGVfZm10OjpoNzNiYTNjYjI3NTNkYWZhZoUDPjxjb3JlOjpmbXQ6OkVycm9yIGFzIGNvcmU6OmZtdDo6RGVidWc+OjpmbXQ6OmhhYTE5NzYzNzBkMWRjNzk4hgMzYWxsb2M6OmFsbG9jOjpoYW5kbGVfYWxsb2NfZXJyb3I6Omg1NmM0OGUzNTY1NzhkOWVjhwMuY29yZTo6Zm10OjpXcml0ZTo6d3JpdGVfZm10OjpoNTBiZjNhMGU2ZWRkNWY1N4gDMzxzdHIgYXMgY29yZTo6Zm10OjpEaXNwbGF5Pjo6Zm10OjpoOWM3YmJhMDVjODYzNDVlNYkDB21lbW1vdmWKAyRfX3diaW5kZ2VuX3N0cmluZ19nZXQgZXh0ZXJucmVmIHNoaW2LAyRfX3diaW5kZ2VuX251bWJlcl9nZXQgZXh0ZXJucmVmIHNoaW2MAyZfX3diaW5kZ2VuX2RlYnVnX3N0cmluZyBleHRlcm5yZWYgc2hpbY0DJ2pzX3N5czo6UHJvbWlzZTo6bmV3OjpoMWYzYzdhN2M4MGRkOWNhZI4DK2pzX3N5czo6UHJvbWlzZTo6cmVzb2x2ZTo6aGY2YTUxMzc1NmE3YmQwYjiPAzRjb3JlOjpwYW5pYzo6UGFuaWNQYXlsb2FkOjphc19zdHI6OmgyMWRjODEzYjhmM2NjNTM4kANCc3RkOjpzeXM6OmJhY2t0cmFjZTo6X19ydXN0X2VuZF9zaG9ydF9iYWNrdHJhY2U6OmhmNmE4M2EwYTIxNTViOWMwkQMjX193YmluZGdlbl9pc19zdHJpbmcgZXh0ZXJucmVmIHNoaW2SAyFfX3diaW5kZ2VuX2lzX251bGwgZXh0ZXJucmVmIHNoaW2TAyZfX3diaW5kZ2VuX2lzX3VuZGVmaW5lZCBleHRlcm5yZWYgc2hpbZQDI19fd2JpbmRnZW5faXNfb2JqZWN0IGV4dGVybnJlZiBzaGltlQM0X193YmdfcXVldWVNaWNyb3Rhc2tfOTdkOTJiNGZjYzhhNjFjNSBleHRlcm5yZWYgc2hpbZYDJV9fd2JpbmRnZW5faXNfZnVuY3Rpb24gZXh0ZXJucmVmIHNoaW2XAzhfX3diZ19pbnN0YW5jZW9mX1Byb21pc2VfOTM1MTY4YjhmNGI0OWRiMyBleHRlcm5yZWYgc2hpbZgDNmpzX3N5czo6VWludDhBcnJheTo6bmV3X3dpdGhfbGVuZ3RoOjpoOGQ1ZTkxMDAyMjk5MjE5MZkDbzwmd2FzbV9iaW5kZ2VuOjpjbG9zdXJlOjpDbG9zdXJlPFQ+IGFzIHdhc21fYmluZGdlbjo6Y29udmVydDo6dHJhaXRzOjpJbnRvV2FzbUFiaT46OmludG9fYWJpOjpoYjFjMDk4MTAyMzg1ZDU0MZoDF19fZXh0ZXJucmVmX3RhYmxlX2FsbG9jmwM1d2FzbV9iaW5kZ2VuOjpfX3J0OjptYWxsb2NfZmFpbHVyZTo6aDYxMzQwODZiMDZhZDhjNmadAwVzdGFydJ4DRjxqc19zeXM6Ok9iamVjdCBhcyBjb3JlOjpkZWZhdWx0OjpEZWZhdWx0Pjo6ZGVmYXVsdDo6aGUxODNmODFiOTg4MTViMjCfA0J3YXNtX2JpbmRnZW46OmNvbnZlcnQ6OnRyYWl0czo6V2FzbVJldDxUPjo6am9pbjo6aDRiNjk3YjdjZWM5ZmRmOWagA148d2FzbV9iaW5kZ2VuOjpjb252ZXJ0Ojp0cmFpdHM6Oldhc21SZXQ8VD4gYXMgY29yZTo6Y29udmVydDo6RnJvbTxUPj46OmZyb206OmhkN2ViY2RjNTJjMjBmMTU3oQMnd2FzbV9iaW5kZ2VuOjptZW1vcnk6OmhjYjFhN2IxNTliNzM3YTU4ogMmc3RkOjpwcm9jZXNzOjphYm9ydDo6aGQ2ZTlhYTQ5ZjZmMWNmMzKjAxJfX3J1c3Rfc3RhcnRfcGFuaWOkA148d2FzbV9iaW5kZ2VuOjpjb252ZXJ0Ojp0cmFpdHM6Oldhc21SZXQ8VD4gYXMgY29yZTo6Y29udmVydDo6RnJvbTxUPj46OmZyb206OmgxZjlhNGExMWFmYWZiZDM3pQNIPGFsbG9jOjp2ZWM6OlZlYzxULEE+IGFzIGNvcmU6Om9wczo6ZHJvcDo6RHJvcD46OmRyb3A6Omg5NTFiMmU1NWEwZjhmOWI1BxIBAA9fX3N0YWNrX3BvaW50ZXIJEQIABy5yb2RhdGEBBS5kYXRhAHwJcHJvZHVjZXJzAghsYW5ndWFnZQEEUnVzdAAMcHJvY2Vzc2VkLWJ5AwVydXN0Yx0xLjg2LjAgKDA1Zjk4NDZmOCAyMDI1LTAzLTMxKQZ3YWxydXMGMC4yMy4zDHdhc20tYmluZGdlbhMwLjIuMTAwICgyNDA1ZWMyYjQpAEkPdGFyZ2V0X2ZlYXR1cmVzBCsKbXVsdGl2YWx1ZSsPbXV0YWJsZS1nbG9iYWxzKw9yZWZlcmVuY2UtdHlwZXMrCHNpZ24tZXh0",self.location)),n=x(),("string"===typeof e||"function"===typeof Request&&e instanceof Request||"function"===typeof URL&&e instanceof URL)&&(e=fetch(e)),A.t0=k,A.next=9,e;case 9:return A.t1=A.sent,A.t2=n,A.next=13,(0,A.t0)(A.t1,A.t2);case 13:return o=A.sent,r=o.instance,a=o.module,A.abrupt("return",G(r,a));case 17:case"end":return A.stop()}}),A)}))),F.apply(this,arguments)}var J=Object.freeze(Object.defineProperty({__proto__:null,NoctuaCore:M,default:Z,initSync:j,start:v},Symbol.toStringTag,{value:"Module"})),S=function(){function A(){(0,o.Z)(this,A),this._wasmModule=null,this._noctuaCore=null,this._initialized=!1,this._initPromise=null}return(0,r.Z)(A,[{key:"initialize",value:function(){var A=(0,n.Z)(regeneratorRuntime.mark((function A(){var e=this;return regeneratorRuntime.wrap((function(A){while(1)switch(A.prev=A.next){case 0:if(!this._initialized){A.next=2;break}return A.abrupt("return",Promise.resolve());case 2:if(!this._initPromise){A.next=4;break}return A.abrupt("return",this._initPromise);case 4:return this._initPromise=new Promise(function(){var A=(0,n.Z)(regeneratorRuntime.mark((function A(t){return regeneratorRuntime.wrap((function(A){while(1)switch(A.prev=A.next){case 0:return A.prev=0,A.next=3,Z();case 3:e._wasmModule=J,e._noctuaCore=new M(e._fetchCallback.bind(e),e._setTimeoutCallback.bind(e),e._clearTimeoutCallback.bind(e),e._updateSessionStorageCallback.bind(e),e._getTimestampCallback.bind(e)),e._initialized=!0,t(),A.next=14;break;case 9:A.prev=9,A.t0=A["catch"](0),e._initialized=!1,e._initPromise=null,t();case 14:case"end":return A.stop()}}),A,null,[[0,9]])})));return function(e){return A.apply(this,arguments)}}()),A.abrupt("return",this._initPromise);case 6:case"end":return A.stop()}}),A,this)})));function e(){return A.apply(this,arguments)}return e}()},{key:"start",value:function(){var A=(0,n.Z)(regeneratorRuntime.mark((function A(){return regeneratorRuntime.wrap((function(A){while(1)switch(A.prev=A.next){case 0:return A.next=2,this.initialize();case 2:if(this._noctuaCore)try{this._noctuaCore.start()}catch(e){}case 3:case"end":return A.stop()}}),A,this)})));function e(){return A.apply(this,arguments)}return e}()},{key:"getFrameworkBaseUrl",value:function(A){var e=A,t=window["faceConfig"]||window["FaceConfig"]||{};return A.startsWith("http")||A.startsWith("https")||(A=(t["basePath"]||t["BasePath"]||"")+e),A}},{key:"_fetchCallback",value:function(A,e){try{return A=this.getFrameworkBaseUrl(A),fetch(A,e).then((function(A){if(!A.ok)throw new Error("网络响应不正常");return A.text()})).catch((function(){return Promise.resolve(null)}))}catch(t){return Promise.resolve(null)}}},{key:"_setTimeoutCallback",value:function(A,e){try{return setTimeout(A,e)}catch(t){return-1}}},{key:"_clearTimeoutCallback",value:function(A){try{clearTimeout(A)}catch(e){}}},{key:"_updateSessionStorageCallback",value:function(A,e){try{var t=sessionStorage.getItem(A);return sessionStorage.setItem(A,e),t}catch(i){return null}}},{key:"_getTimestampCallback",value:function(){try{return Date.now()}catch(A){return 0}}}]),A}(),R=new S,z=["use","nextTick","Vue"],T=function(A){return z[A]},X={install:function(A){A[T(1)]((function(){R.start().catch((function(){}))}))}};"undefined"!==typeof window&&window[T(2)]&&window[T(2)][T(0)](X)},23152:function(A,e,t){"use strict";var i=t(89584),n=t(95082),o=t(96884),r={name:"VuePlyr",props:{options:{type:Object,required:!1,default:function(){return{}}},emit:{type:Array,required:!1,default:function(){return[]}}},data:function(){return{player:{}}},computed:{opts:function(){var A=this.options;return this.options.hasOwnProperty("hideYouTubeDOMError")||(A.hideYouTubeDOMError=!0),A}},mounted:function(){var A=this;this.player=new o(this.$el.firstChild,this.opts),this.emit.forEach((function(e){A.player.on(e,A.emitPlayerEvent)}))},beforeDestroy:function(){try{this.player.destroy()}catch(A){!this.opts.hideYouTubeDOMError||A.message}},methods:{emitPlayerEvent:function(A){this.$emit(A.type,A)}}};function a(A,e,t,i,n,o,r,a,s,l){"boolean"!==typeof r&&(s=a,a=r,r=!1);var g,c="function"===typeof t?t.options:t;if(A&&A.render&&(c.render=A.render,c.staticRenderFns=A.staticRenderFns,c._compiled=!0,n&&(c.functional=!0)),i&&(c._scopeId=i),o?(g=function(A){A=A||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext,A||"undefined"===typeof __VUE_SSR_CONTEXT__||(A=__VUE_SSR_CONTEXT__),e&&e.call(this,s(A)),A&&A._registeredComponents&&A._registeredComponents.add(o)},c._ssrRegister=g):e&&(g=r?function(A){e.call(this,l(A,this.$root.$options.shadowRoot))}:function(A){e.call(this,a(A))}),g)if(c.functional){var I=c.render;c.render=function(A,e){return g.call(e),I(A,e)}}else{var Q=c.beforeCreate;c.beforeCreate=Q?[].concat(Q,g):[g]}return t}var s,l="undefined"!==typeof navigator&&/msie [6-9]\\b/.test(navigator.userAgent.toLowerCase());function g(A){return function(A,e){return I(A,e)}}var c={};function I(A,e){var t=l?e.media||"default":A,i=c[t]||(c[t]={ids:new Set,styles:[]});if(!i.ids.has(A)){i.ids.add(A);var n=e.source;if(e.map&&(n+="\n/*# sourceURL="+e.map.sources[0]+" */",n+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(e.map))))+" */"),i.element||(i.element=document.createElement("style"),i.element.type="text/css",e.media&&i.element.setAttribute("media",e.media),void 0===s&&(s=document.head||document.getElementsByTagName("head")[0]),s.appendChild(i.element)),"styleSheet"in i.element)i.styles.push(n),i.element.styleSheet.cssText=i.styles.filter(Boolean).join("\n");else{var o=i.ids.size-1,r=document.createTextNode(n),a=i.element.childNodes;a[o]&&i.element.removeChild(a[o]),a.length?i.element.insertBefore(r,a[o]):i.element.appendChild(r)}}}var Q=r,u=function(){var A=this,e=A.$createElement,t=A._self._c||e;return t("div",[A._t("default")],2)},C=[];u._withStripped=!0;var h=function(A){A&&A("data-v-91800632_0",{source:"@keyframes plyr-progress{to{background-position:25px 0}}@keyframes plyr-popup{0%{opacity:.5;transform:translateY(10px)}to{opacity:1;transform:translateY(0)}}@keyframes plyr-fade-in{from{opacity:0}to{opacity:1}}.plyr{-moz-osx-font-smoothing:auto;-webkit-font-smoothing:subpixel-antialiased;direction:ltr;font-family:Avenir,\"Avenir Next\",\"Helvetica Neue\",\"Segoe UI\",Helvetica,Arial,sans-serif;font-variant-numeric:tabular-nums;font-weight:500;line-height:1.7;max-width:100%;min-width:200px;position:relative;text-shadow:none;transition:box-shadow .3s ease}.plyr audio,.plyr video{border-radius:inherit;height:auto;vertical-align:middle;width:100%}.plyr button{font:inherit;line-height:inherit;width:auto}.plyr:focus{outline:0}.plyr--full-ui{box-sizing:border-box}.plyr--full-ui *,.plyr--full-ui ::after,.plyr--full-ui ::before{box-sizing:inherit}.plyr--full-ui a,.plyr--full-ui button,.plyr--full-ui input,.plyr--full-ui label{touch-action:manipulation}.plyr__badge{background:#4a5764;border-radius:2px;color:#fff;font-size:9px;line-height:1;padding:3px 4px}.plyr--full-ui ::-webkit-media-text-track-container{display:none}.plyr__captions{animation:plyr-fade-in .3s ease;bottom:0;color:#fff;display:none;font-size:14px;left:0;padding:10px;position:absolute;text-align:center;transition:transform .4s ease-in-out;width:100%}.plyr__captions .plyr__caption{background:rgba(0,0,0,.8);border-radius:2px;-webkit-box-decoration-break:clone;box-decoration-break:clone;line-height:185%;padding:.2em .5em;white-space:pre-wrap}.plyr__captions .plyr__caption div{display:inline}.plyr__captions span:empty{display:none}@media (min-width:480px){.plyr__captions{font-size:16px;padding:20px}}@media (min-width:768px){.plyr__captions{font-size:18px}}.plyr--captions-active .plyr__captions{display:block}.plyr:not(.plyr--hide-controls) .plyr__controls:not(:empty)~.plyr__captions{transform:translateY(-40px)}.plyr__control{background:0 0;border:0;border-radius:3px;color:inherit;cursor:pointer;flex-shrink:0;overflow:visible;padding:7px;position:relative;transition:all .3s ease}.plyr__control svg{display:block;fill:currentColor;height:18px;pointer-events:none;width:18px}.plyr__control:focus{outline:0}.plyr__control.plyr__tab-focus{box-shadow:0 0 0 5px rgba(0,179,255,.5);outline:0}a.plyr__control{text-decoration:none}a.plyr__control::after,a.plyr__control::before{display:none}.plyr__control.plyr__control--pressed .icon--not-pressed,.plyr__control.plyr__control--pressed .label--not-pressed,.plyr__control:not(.plyr__control--pressed) .icon--pressed,.plyr__control:not(.plyr__control--pressed) .label--pressed{display:none}.plyr--audio .plyr__control.plyr__tab-focus,.plyr--audio .plyr__control:hover,.plyr--audio .plyr__control[aria-expanded=true]{background:#00b3ff;color:#fff}.plyr--video .plyr__control.plyr__tab-focus,.plyr--video .plyr__control:hover,.plyr--video .plyr__control[aria-expanded=true]{background:#00b3ff;color:#fff}.plyr__control--overlaid{background:rgba(0,179,255,.8);border:0;border-radius:100%;color:#fff;display:none;left:50%;padding:15px;position:absolute;top:50%;transform:translate(-50%,-50%);z-index:2}.plyr__control--overlaid svg{left:2px;position:relative}.plyr__control--overlaid:focus,.plyr__control--overlaid:hover{background:#00b3ff}.plyr--playing .plyr__control--overlaid{opacity:0;visibility:hidden}.plyr--full-ui.plyr--video .plyr__control--overlaid{display:block}.plyr--full-ui ::-webkit-media-controls{display:none}.plyr__controls{align-items:center;display:flex;justify-content:flex-end;text-align:center}.plyr__controls .plyr__progress__container{flex:1;min-width:0}.plyr__controls .plyr__controls__item{margin-left:2.5px}.plyr__controls .plyr__controls__item:first-child{margin-left:0;margin-right:auto}.plyr__controls .plyr__controls__item.plyr__progress__container{padding-left:2.5px}.plyr__controls .plyr__controls__item.plyr__time{padding:0 5px}.plyr__controls .plyr__controls__item.plyr__progress__container:first-child,.plyr__controls .plyr__controls__item.plyr__time+.plyr__time,.plyr__controls .plyr__controls__item.plyr__time:first-child{padding-left:0}.plyr__controls .plyr__controls__item.plyr__volume{padding-right:5px}.plyr__controls .plyr__controls__item.plyr__volume:first-child{padding-right:0}.plyr__controls:empty{display:none}.plyr--audio .plyr__controls{background:#fff;border-radius:inherit;color:#4a5764;padding:10px}.plyr--video .plyr__controls{background:linear-gradient(rgba(0,0,0,0),rgba(0,0,0,.7));border-bottom-left-radius:inherit;border-bottom-right-radius:inherit;bottom:0;color:#fff;left:0;padding:20px 5px 5px;position:absolute;right:0;transition:opacity .4s ease-in-out,transform .4s ease-in-out;z-index:3}@media (min-width:480px){.plyr--video .plyr__controls{padding:35px 10px 10px}}.plyr--video.plyr--hide-controls .plyr__controls{opacity:0;pointer-events:none;transform:translateY(100%)}.plyr [data-plyr=airplay],.plyr [data-plyr=captions],.plyr [data-plyr=fullscreen],.plyr [data-plyr=pip]{display:none}.plyr--airplay-supported [data-plyr=airplay],.plyr--captions-enabled [data-plyr=captions],.plyr--fullscreen-enabled [data-plyr=fullscreen],.plyr--pip-supported [data-plyr=pip]{display:inline-block}.plyr__menu{display:flex;position:relative}.plyr__menu .plyr__control svg{transition:transform .3s ease}.plyr__menu .plyr__control[aria-expanded=true] svg{transform:rotate(90deg)}.plyr__menu .plyr__control[aria-expanded=true] .plyr__tooltip{display:none}.plyr__menu__container{animation:plyr-popup .2s ease;background:rgba(255,255,255,.9);border-radius:4px;bottom:100%;box-shadow:0 1px 2px rgba(0,0,0,.15);color:#4a5764;font-size:16px;margin-bottom:10px;position:absolute;right:-3px;text-align:left;white-space:nowrap;z-index:3}.plyr__menu__container>div{overflow:hidden;transition:height .35s cubic-bezier(.4,0,.2,1),width .35s cubic-bezier(.4,0,.2,1)}.plyr__menu__container::after{border:4px solid transparent;border-top-color:rgba(255,255,255,.9);content:'';height:0;position:absolute;right:15px;top:100%;width:0}.plyr__menu__container [role=menu]{padding:7px}.plyr__menu__container [role=menuitem],.plyr__menu__container [role=menuitemradio]{margin-top:2px}.plyr__menu__container [role=menuitem]:first-child,.plyr__menu__container [role=menuitemradio]:first-child{margin-top:0}.plyr__menu__container .plyr__control{align-items:center;color:#4a5764;display:flex;font-size:14px;padding:4px 11px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;width:100%}.plyr__menu__container .plyr__control>span{align-items:inherit;display:flex;width:100%}.plyr__menu__container .plyr__control::after{border:4px solid transparent;content:'';position:absolute;top:50%;transform:translateY(-50%)}.plyr__menu__container .plyr__control--forward{padding-right:28px}.plyr__menu__container .plyr__control--forward::after{border-left-color:rgba(74,87,100,.8);right:5px}.plyr__menu__container .plyr__control--forward.plyr__tab-focus::after,.plyr__menu__container .plyr__control--forward:hover::after{border-left-color:currentColor}.plyr__menu__container .plyr__control--back{font-weight:500;margin:7px;margin-bottom:3px;padding-left:28px;position:relative;width:calc(100% - 14px)}.plyr__menu__container .plyr__control--back::after{border-right-color:rgba(74,87,100,.8);left:7px}.plyr__menu__container .plyr__control--back::before{background:#c1c9d1;box-shadow:0 1px 0 #fff;content:'';height:1px;left:0;margin-top:4px;overflow:hidden;position:absolute;right:0;top:100%}.plyr__menu__container .plyr__control--back.plyr__tab-focus::after,.plyr__menu__container .plyr__control--back:hover::after{border-right-color:currentColor}.plyr__menu__container .plyr__control[role=menuitemradio]{padding-left:7px}.plyr__menu__container .plyr__control[role=menuitemradio]::after,.plyr__menu__container .plyr__control[role=menuitemradio]::before{border-radius:100%}.plyr__menu__container .plyr__control[role=menuitemradio]::before{background:rgba(0,0,0,.1);content:'';display:block;flex-shrink:0;height:16px;margin-right:10px;transition:all .3s ease;width:16px}.plyr__menu__container .plyr__control[role=menuitemradio]::after{background:#fff;border:0;height:6px;left:12px;opacity:0;top:50%;transform:translateY(-50%) scale(0);transition:transform .3s ease,opacity .3s ease;width:6px}.plyr__menu__container .plyr__control[role=menuitemradio][aria-checked=true]::before{background:#00b3ff}.plyr__menu__container .plyr__control[role=menuitemradio][aria-checked=true]::after{opacity:1;transform:translateY(-50%) scale(1)}.plyr__menu__container .plyr__control[role=menuitemradio].plyr__tab-focus::before,.plyr__menu__container .plyr__control[role=menuitemradio]:hover::before{background:rgba(0,0,0,.1)}.plyr__menu__container .plyr__menu__value{align-items:center;display:flex;margin-left:auto;margin-right:-5px;overflow:hidden;padding-left:25px;pointer-events:none}.plyr--full-ui input[type=range]{-webkit-appearance:none;background:0 0;border:0;border-radius:26px;color:#00b3ff;display:block;height:19px;margin:0;padding:0;transition:box-shadow .3s ease;width:100%}.plyr--full-ui input[type=range]::-webkit-slider-runnable-track{background:0 0;border:0;border-radius:2.5px;height:5px;transition:box-shadow .3s ease;-webkit-user-select:none;user-select:none;background-image:linear-gradient(to right,currentColor var(--value,0),transparent var(--value,0))}.plyr--full-ui input[type=range]::-webkit-slider-thumb{background:#fff;border:0;border-radius:100%;box-shadow:0 1px 1px rgba(0,0,0,.15),0 0 0 1px rgba(35,41,47,.2);height:13px;position:relative;transition:all .2s ease;width:13px;-webkit-appearance:none;margin-top:-4px}.plyr--full-ui input[type=range]::-moz-range-track{background:0 0;border:0;border-radius:2.5px;height:5px;transition:box-shadow .3s ease;-moz-user-select:none;user-select:none}.plyr--full-ui input[type=range]::-moz-range-thumb{background:#fff;border:0;border-radius:100%;box-shadow:0 1px 1px rgba(0,0,0,.15),0 0 0 1px rgba(35,41,47,.2);height:13px;position:relative;transition:all .2s ease;width:13px}.plyr--full-ui input[type=range]::-moz-range-progress{background:currentColor;border-radius:2.5px;height:5px}.plyr--full-ui input[type=range]::-ms-track{background:0 0;border:0;border-radius:2.5px;height:5px;transition:box-shadow .3s ease;-ms-user-select:none;user-select:none;color:transparent}.plyr--full-ui input[type=range]::-ms-fill-upper{background:0 0;border:0;border-radius:2.5px;height:5px;transition:box-shadow .3s ease;-ms-user-select:none;user-select:none}.plyr--full-ui input[type=range]::-ms-fill-lower{background:0 0;border:0;border-radius:2.5px;height:5px;transition:box-shadow .3s ease;-ms-user-select:none;user-select:none;background:currentColor}.plyr--full-ui input[type=range]::-ms-thumb{background:#fff;border:0;border-radius:100%;box-shadow:0 1px 1px rgba(0,0,0,.15),0 0 0 1px rgba(35,41,47,.2);height:13px;position:relative;transition:all .2s ease;width:13px;margin-top:0}.plyr--full-ui input[type=range]::-ms-tooltip{display:none}.plyr--full-ui input[type=range]:focus{outline:0}.plyr--full-ui input[type=range]::-moz-focus-outer{border:0}.plyr--full-ui input[type=range].plyr__tab-focus::-webkit-slider-runnable-track{box-shadow:0 0 0 5px rgba(0,179,255,.5);outline:0}.plyr--full-ui input[type=range].plyr__tab-focus::-moz-range-track{box-shadow:0 0 0 5px rgba(0,179,255,.5);outline:0}.plyr--full-ui input[type=range].plyr__tab-focus::-ms-track{box-shadow:0 0 0 5px rgba(0,179,255,.5);outline:0}.plyr--full-ui.plyr--video input[type=range]::-webkit-slider-runnable-track{background-color:rgba(255,255,255,.25)}.plyr--full-ui.plyr--video input[type=range]::-moz-range-track{background-color:rgba(255,255,255,.25)}.plyr--full-ui.plyr--video input[type=range]::-ms-track{background-color:rgba(255,255,255,.25)}.plyr--full-ui.plyr--video input[type=range]:active::-webkit-slider-thumb{box-shadow:0 1px 1px rgba(0,0,0,.15),0 0 0 1px rgba(35,41,47,.2),0 0 0 3px rgba(255,255,255,.5)}.plyr--full-ui.plyr--video input[type=range]:active::-moz-range-thumb{box-shadow:0 1px 1px rgba(0,0,0,.15),0 0 0 1px rgba(35,41,47,.2),0 0 0 3px rgba(255,255,255,.5)}.plyr--full-ui.plyr--video input[type=range]:active::-ms-thumb{box-shadow:0 1px 1px rgba(0,0,0,.15),0 0 0 1px rgba(35,41,47,.2),0 0 0 3px rgba(255,255,255,.5)}.plyr--full-ui.plyr--audio input[type=range]::-webkit-slider-runnable-track{background-color:rgba(193,201,209,.66)}.plyr--full-ui.plyr--audio input[type=range]::-moz-range-track{background-color:rgba(193,201,209,.66)}.plyr--full-ui.plyr--audio input[type=range]::-ms-track{background-color:rgba(193,201,209,.66)}.plyr--full-ui.plyr--audio input[type=range]:active::-webkit-slider-thumb{box-shadow:0 1px 1px rgba(0,0,0,.15),0 0 0 1px rgba(35,41,47,.2),0 0 0 3px rgba(0,0,0,.1)}.plyr--full-ui.plyr--audio input[type=range]:active::-moz-range-thumb{box-shadow:0 1px 1px rgba(0,0,0,.15),0 0 0 1px rgba(35,41,47,.2),0 0 0 3px rgba(0,0,0,.1)}.plyr--full-ui.plyr--audio input[type=range]:active::-ms-thumb{box-shadow:0 1px 1px rgba(0,0,0,.15),0 0 0 1px rgba(35,41,47,.2),0 0 0 3px rgba(0,0,0,.1)}.plyr__poster{background-color:#000;background-position:50% 50%;background-repeat:no-repeat;background-size:contain;height:100%;left:0;opacity:0;position:absolute;top:0;transition:opacity .2s ease;width:100%;z-index:1}.plyr--stopped.plyr__poster-enabled .plyr__poster{opacity:1}.plyr__time{font-size:14px}.plyr__time+.plyr__time::before{content:'\\2044';margin-right:10px}@media (max-width:767px){.plyr__time+.plyr__time{display:none}}.plyr--video .plyr__time{text-shadow:0 1px 1px rgba(0,0,0,.15)}.plyr__tooltip{background:rgba(255,255,255,.9);border-radius:3px;bottom:100%;box-shadow:0 1px 2px rgba(0,0,0,.15);color:#4a5764;font-size:14px;font-weight:500;left:50%;line-height:1.3;margin-bottom:10px;opacity:0;padding:5px 7.5px;pointer-events:none;position:absolute;transform:translate(-50%,10px) scale(.8);transform-origin:50% 100%;transition:transform .2s .1s ease,opacity .2s .1s ease;white-space:nowrap;z-index:2}.plyr__tooltip::before{border-left:4px solid transparent;border-right:4px solid transparent;border-top:4px solid rgba(255,255,255,.9);bottom:-4px;content:'';height:0;left:50%;position:absolute;transform:translateX(-50%);width:0;z-index:2}.plyr .plyr__control.plyr__tab-focus .plyr__tooltip,.plyr .plyr__control:hover .plyr__tooltip,.plyr__tooltip--visible{opacity:1;transform:translate(-50%,0) scale(1)}.plyr .plyr__control:hover .plyr__tooltip{z-index:3}.plyr__controls>.plyr__control:first-child .plyr__tooltip,.plyr__controls>.plyr__control:first-child+.plyr__control .plyr__tooltip{left:0;transform:translate(0,10px) scale(.8);transform-origin:0 100%}.plyr__controls>.plyr__control:first-child .plyr__tooltip::before,.plyr__controls>.plyr__control:first-child+.plyr__control .plyr__tooltip::before{left:16px}.plyr__controls>.plyr__control:last-child .plyr__tooltip{left:auto;right:0;transform:translate(0,10px) scale(.8);transform-origin:100% 100%}.plyr__controls>.plyr__control:last-child .plyr__tooltip::before{left:auto;right:16px;transform:translateX(50%)}.plyr__controls>.plyr__control:first-child .plyr__tooltip--visible,.plyr__controls>.plyr__control:first-child+.plyr__control .plyr__tooltip--visible,.plyr__controls>.plyr__control:first-child+.plyr__control.plyr__tab-focus .plyr__tooltip,.plyr__controls>.plyr__control:first-child+.plyr__control:hover .plyr__tooltip,.plyr__controls>.plyr__control:first-child.plyr__tab-focus .plyr__tooltip,.plyr__controls>.plyr__control:first-child:hover .plyr__tooltip,.plyr__controls>.plyr__control:last-child .plyr__tooltip--visible,.plyr__controls>.plyr__control:last-child.plyr__tab-focus .plyr__tooltip,.plyr__controls>.plyr__control:last-child:hover .plyr__tooltip{transform:translate(0,0) scale(1)}.plyr--video{background:#000;overflow:hidden}.plyr--video.plyr--menu-open{overflow:visible}.plyr__video-wrapper{background:#000;border-radius:inherit;overflow:hidden;position:relative;z-index:0}.plyr__video-embed,.plyr__video-wrapper--fixed-ratio{height:0;padding-bottom:56.25%}.plyr__video-embed iframe,.plyr__video-wrapper--fixed-ratio video{border:0;height:100%;left:0;position:absolute;top:0;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;width:100%}.plyr--full-ui .plyr__video-embed>.plyr__video-embed__container{padding-bottom:240%;position:relative;transform:translateY(-38.28125%)}.plyr__progress{left:6.5px;margin-right:13px;position:relative}.plyr__progress input[type=range],.plyr__progress__buffer{margin-left:-6.5px;margin-right:-6.5px;width:calc(100% + 13px)}.plyr__progress input[type=range]{position:relative;z-index:2}.plyr__progress .plyr__tooltip{font-size:14px;left:0}.plyr__progress__buffer{-webkit-appearance:none;background:0 0;border:0;border-radius:100px;height:5px;left:0;margin-top:-2.5px;padding:0;position:absolute;top:50%}.plyr__progress__buffer::-webkit-progress-bar{background:0 0}.plyr__progress__buffer::-webkit-progress-value{background:currentColor;border-radius:100px;min-width:5px;transition:width .2s ease}.plyr__progress__buffer::-moz-progress-bar{background:currentColor;border-radius:100px;min-width:5px;transition:width .2s ease}.plyr__progress__buffer::-ms-fill{border-radius:100px;transition:width .2s ease}.plyr--video .plyr__progress__buffer{box-shadow:0 1px 1px rgba(0,0,0,.15);color:rgba(255,255,255,.25)}.plyr--audio .plyr__progress__buffer{color:rgba(193,201,209,.66)}.plyr--loading .plyr__progress__buffer{animation:plyr-progress 1s linear infinite;background-image:linear-gradient(-45deg,rgba(35,41,47,.6) 25%,transparent 25%,transparent 50%,rgba(35,41,47,.6) 50%,rgba(35,41,47,.6) 75%,transparent 75%,transparent);background-repeat:repeat-x;background-size:25px 25px;color:transparent}.plyr--video.plyr--loading .plyr__progress__buffer{background-color:rgba(255,255,255,.25)}.plyr--audio.plyr--loading .plyr__progress__buffer{background-color:rgba(193,201,209,.66)}.plyr__volume{align-items:center;display:flex;flex:1;position:relative}.plyr__volume input[type=range]{margin-left:5px;position:relative;z-index:2}@media (min-width:480px){.plyr__volume{max-width:90px}}@media (min-width:768px){.plyr__volume{max-width:110px}}.plyr--is-ios .plyr__volume{display:none!important}.plyr--is-ios.plyr--vimeo [data-plyr=mute]{display:none!important}.plyr:-webkit-full-screen{background:#000;border-radius:0!important;height:100%;margin:0;width:100%}.plyr:-ms-fullscreen{background:#000;border-radius:0!important;height:100%;margin:0;width:100%}.plyr:fullscreen{background:#000;border-radius:0!important;height:100%;margin:0;width:100%}.plyr:-webkit-full-screen video{height:100%}.plyr:-ms-fullscreen video{height:100%}.plyr:fullscreen video{height:100%}.plyr:-webkit-full-screen .plyr__video-wrapper{height:100%;position:static}.plyr:-ms-fullscreen .plyr__video-wrapper{height:100%;position:static}.plyr:fullscreen .plyr__video-wrapper{height:100%;position:static}.plyr:-webkit-full-screen.plyr--vimeo .plyr__video-wrapper{height:0;position:relative;top:50%;transform:translateY(-50%)}.plyr:-ms-fullscreen.plyr--vimeo .plyr__video-wrapper{height:0;position:relative;top:50%;transform:translateY(-50%)}.plyr:fullscreen.plyr--vimeo .plyr__video-wrapper{height:0;position:relative;top:50%;transform:translateY(-50%)}.plyr:-webkit-full-screen .plyr__control .icon--exit-fullscreen{display:block}.plyr:-ms-fullscreen .plyr__control .icon--exit-fullscreen{display:block}.plyr:fullscreen .plyr__control .icon--exit-fullscreen{display:block}.plyr:-webkit-full-screen .plyr__control .icon--exit-fullscreen+svg{display:none}.plyr:-ms-fullscreen .plyr__control .icon--exit-fullscreen+svg{display:none}.plyr:fullscreen .plyr__control .icon--exit-fullscreen+svg{display:none}.plyr:-webkit-full-screen.plyr--hide-controls{cursor:none}.plyr:-ms-fullscreen.plyr--hide-controls{cursor:none}.plyr:fullscreen.plyr--hide-controls{cursor:none}@media (min-width:1024px){.plyr:-webkit-full-screen .plyr__captions{font-size:21px}.plyr:-ms-fullscreen .plyr__captions{font-size:21px}.plyr:fullscreen .plyr__captions{font-size:21px}}.plyr:-webkit-full-screen{background:#000;border-radius:0!important;height:100%;margin:0;width:100%}.plyr:-webkit-full-screen video{height:100%}.plyr:-webkit-full-screen .plyr__video-wrapper{height:100%;position:static}.plyr:-webkit-full-screen.plyr--vimeo .plyr__video-wrapper{height:0;position:relative;top:50%;transform:translateY(-50%)}.plyr:-webkit-full-screen .plyr__control .icon--exit-fullscreen{display:block}.plyr:-webkit-full-screen .plyr__control .icon--exit-fullscreen+svg{display:none}.plyr:-webkit-full-screen.plyr--hide-controls{cursor:none}@media (min-width:1024px){.plyr:-webkit-full-screen .plyr__captions{font-size:21px}}.plyr:-moz-full-screen{background:#000;border-radius:0!important;height:100%;margin:0;width:100%}.plyr:-moz-full-screen video{height:100%}.plyr:-moz-full-screen .plyr__video-wrapper{height:100%;position:static}.plyr:-moz-full-screen.plyr--vimeo .plyr__video-wrapper{height:0;position:relative;top:50%;transform:translateY(-50%)}.plyr:-moz-full-screen .plyr__control .icon--exit-fullscreen{display:block}.plyr:-moz-full-screen .plyr__control .icon--exit-fullscreen+svg{display:none}.plyr:-moz-full-screen.plyr--hide-controls{cursor:none}@media (min-width:1024px){.plyr:-moz-full-screen .plyr__captions{font-size:21px}}.plyr:-ms-fullscreen{background:#000;border-radius:0!important;height:100%;margin:0;width:100%}.plyr:-ms-fullscreen video{height:100%}.plyr:-ms-fullscreen .plyr__video-wrapper{height:100%;position:static}.plyr:-ms-fullscreen.plyr--vimeo .plyr__video-wrapper{height:0;position:relative;top:50%;transform:translateY(-50%)}.plyr:-ms-fullscreen .plyr__control .icon--exit-fullscreen{display:block}.plyr:-ms-fullscreen .plyr__control .icon--exit-fullscreen+svg{display:none}.plyr:-ms-fullscreen.plyr--hide-controls{cursor:none}@media (min-width:1024px){.plyr:-ms-fullscreen .plyr__captions{font-size:21px}}.plyr--fullscreen-fallback{background:#000;border-radius:0!important;height:100%;margin:0;width:100%;bottom:0;left:0;position:fixed;right:0;top:0;z-index:10000000}.plyr--fullscreen-fallback video{height:100%}.plyr--fullscreen-fallback .plyr__video-wrapper{height:100%;position:static}.plyr--fullscreen-fallback.plyr--vimeo .plyr__video-wrapper{height:0;position:relative;top:50%;transform:translateY(-50%)}.plyr--fullscreen-fallback .plyr__control .icon--exit-fullscreen{display:block}.plyr--fullscreen-fallback .plyr__control .icon--exit-fullscreen+svg{display:none}.plyr--fullscreen-fallback.plyr--hide-controls{cursor:none}@media (min-width:1024px){.plyr--fullscreen-fallback .plyr__captions{font-size:21px}}.plyr__ads{border-radius:inherit;bottom:0;cursor:pointer;left:0;overflow:hidden;position:absolute;right:0;top:0;z-index:-1}.plyr__ads>div,.plyr__ads>div iframe{height:100%;position:absolute;width:100%}.plyr__ads::after{background:rgba(35,41,47,.8);border-radius:2px;bottom:10px;color:#fff;content:attr(data-badge-text);font-size:11px;padding:2px 6px;pointer-events:none;position:absolute;right:10px;z-index:3}.plyr__ads::after:empty{display:none}.plyr__cues{background:currentColor;display:block;height:5px;left:0;margin:-2.5px 0 0;opacity:.8;position:absolute;top:50%;width:3px;z-index:3}.plyr__preview-thumb{background-color:rgba(255,255,255,.9);border-radius:3px;bottom:100%;box-shadow:0 1px 2px rgba(0,0,0,.15);margin-bottom:10px;opacity:0;padding:3px;pointer-events:none;position:absolute;transform:translate(0,10px) scale(.8);transform-origin:50% 100%;transition:transform .2s .1s ease,opacity .2s .1s ease;z-index:2}.plyr__preview-thumb--is-shown{opacity:1;transform:translate(0,0) scale(1)}.plyr__preview-thumb::before{border-left:4px solid transparent;border-right:4px solid transparent;border-top:4px solid rgba(255,255,255,.9);bottom:-4px;content:'';height:0;left:50%;position:absolute;transform:translateX(-50%);width:0;z-index:2}.plyr__preview-thumb__image-container{background:#c1c9d1;border-radius:2px;overflow:hidden;position:relative;z-index:0}.plyr__preview-thumb__image-container img{height:100%;left:0;max-height:none;max-width:none;position:absolute;top:0;width:100%}.plyr__preview-thumb__time-container{bottom:6px;left:0;position:absolute;right:0;white-space:nowrap;z-index:3}.plyr__preview-thumb__time-container span{background-color:rgba(0,0,0,.55);border-radius:2px;color:#fff;font-size:14px;padding:3px 6px}.plyr__preview-scrubbing{bottom:0;filter:blur(1px);height:100%;left:0;margin:auto;opacity:0;overflow:hidden;position:absolute;right:0;top:0;transition:opacity .3s ease;width:100%;z-index:1}.plyr__preview-scrubbing--is-shown{opacity:1}.plyr__preview-scrubbing img{height:100%;left:0;max-height:none;max-width:none;object-fit:contain;position:absolute;top:0;width:100%}.plyr--no-transition{transition:none!important}.plyr__sr-only{clip:rect(1px,1px,1px,1px);overflow:hidden;border:0!important;height:1px!important;padding:0!important;position:absolute!important;width:1px!important}.plyr [hidden]{display:none!important}",map:void 0,media:void 0})},d=void 0,B=void 0,E=!1,p=a({render:u,staticRenderFns:C},h,Q,d,E,B,!1,g,void 0,void 0);p.install=function(A){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};e.plyr&&(p.props.options.default=function(){return(0,n.Z)({},e.plyr)}),e.emit&&(p.props.emit.default=function(){return(0,i.Z)(e.emit)}),A.component(p.name,p)},"undefined"!==typeof window&&window.Vue&&window.Vue.use(p),e["Z"]=/^(358|4471|8109)$/.test(t.j)?p:null}}]);
(typeof window=='undefined'?global:window).tc_cfg_5582378869106086={"url":"css/theme-colors-a12f935f.css","colors":["#1b65b9","#3274c0","#4984c7","#5f93ce","#76a3d5","#8db2dc","#a4c1e3","#bbd1ea","#d1e0f1","#ebf4fa","#bbd9ed","#8dbce0","#639fd4","#3e82c7","#1b65b9","#0f4894","#052f6e","#001a47","#000b21","27,101,185"]};

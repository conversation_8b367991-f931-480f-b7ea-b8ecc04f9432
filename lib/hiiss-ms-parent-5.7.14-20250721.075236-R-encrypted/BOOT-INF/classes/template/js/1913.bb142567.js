"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[1913],{19597:function(e,t,a){a.d(t,{Z:function(){return b}});var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"layOut"},[a("div",{staticClass:"searchLine"},[a("conditionCompoent",{staticClass:"searchCondition",attrs:{searchType:e.searchType,searchCondition:e.searchCondition},on:{condition:e.getCondition,export:e.getExport}})],1),a("div",{staticClass:"totalLine"},[a("div",{staticClass:"overSituation"},[a("ta-title",[e._v("总体情况")]),a("overSituation",{staticClass:"situationCard",attrs:{conditionData:e.conditionData},on:{spin:e.getSpin}})],1),a("div",{staticClass:"overTrend"},[a("ta-title",[e._v("整体趋势")]),a("overTrend",{staticClass:"trendCard",attrs:{conditionData:e.conditionData},on:{spin:e.getSpin}})],1)]),a("div",{staticClass:"cycleBox"},e._l(e.showCompoent,(function(t,o){return a(t.compoentName,{ref:t.compoentName.replace("Compoent",""),refInFor:!0,tag:"component",class:2==o||1===e.showCompoent.length?"thirdBox":"firstTwoBox",attrs:{nameList:e.nameList,conditionData:e.conditionData,isDeep:!0},on:{spin:e.getSpin}})})),1)])},n=[],i=a(66347),s=a(95082),r=a(84004),c=a(32717),l=a(8416),p=a(42844),m=a(64511),h=a(32307),u=a(6097),d=a(22722),f=a(55115),v=a(88412);f.w3.prototype.Base=Object.assign(f.w3.prototype.Base,(0,s.Z)({},d.Z));var C={components:{conditionCompoent:r.Z,overSituation:c.Z,overTrend:l.Z,departmentCompoent:p.Z,doctorCompoent:h.Z,ruleCompoent:m.Z,itemCompoent:u.Z,TaTitle:v.Z},props:{nameList:{type:Array},searchType:{type:String},searchCondition:{type:Object}},data:function(){return{showCompoent:[{name:"科室",value:1,compoentName:"departmentCompoent"},{name:"医生",value:2,compoentName:"doctorCompoent"},{name:"规则",value:3,compoentName:"ruleCompoent"},{name:"项目",value:4,compoentName:"itemCompoent"}],conditionData:{},overviewSpin:0}},created:function(){for(var e=this,t=function(t){e.showCompoent=e.showCompoent.filter((function(a){return a.name!=e.nameList[t].name})),"医生"==e.nameList[t].name&&(e.showCompoent=e.showCompoent.filter((function(e){return"科室"!=e.name})))},a=0;a<this.nameList.length;a++)t(a)},mounted:function(){},methods:{getCondition:function(e){var t=this;this.$nextTick((function(){t.Base.pageMask({show:!0,text:"加载中"}),t.overviewSpin=0,t.conditionData=e;var a=0;t.conditionData.searchCondition.keshi&&a++,t.conditionData.searchCondition.yisheng&&a++,t.conditionData.searchCondition.guize&&a++,t.conditionData.searchCondition.xiangmu&&a++,3==a&&(t.conditionData.searchCondition.toPage=!0)}))},getSpin:function(){this.overviewSpin++,Object.keys(this.$refs).length===this.overviewSpin&&this.Base.pageMask({show:!1})},getExport:function(){var e={fileName:"审核总览表",sheets:[]};if(this.$refs.department){var t,a=this.$refs.department[0].$refs.xTable.getColumns(),o=[],n=(0,i.Z)(a);try{for(n.s();!(t=n.n()).done;){var s=t.value;"operate"!==s.property&&!1!==s.visible&&o.push({header:s.title,key:s.property,width:20})}}catch(Z){n.e(Z)}finally{n.f()}var r={name:"科室统计",column:{complex:!1,columns:o},rows:this.$refs.department[0].$refs.xTable.getTableData().tableData};e.sheets.push(r)}if(this.$refs.doctor){var c,l=this.$refs.doctor[0].$refs.xTable.getColumns(),p=[],m=(0,i.Z)(l);try{for(m.s();!(c=m.n()).done;){var h=c.value;"operate"!==h.property&&!1!==h.visible&&p.push({header:h.title,key:h.property,width:20})}}catch(Z){m.e(Z)}finally{m.f()}var u={name:"医生统计",column:{complex:!1,columns:p},rows:this.$refs.doctor[0].$refs.xTable.getTableData().tableData};e.sheets.push(u)}if(this.$refs.rule){var d,f=this.$refs.rule[0].$refs.xTable.getColumns(),v=[],C=(0,i.Z)(f);try{for(C.s();!(d=C.n()).done;){var g=d.value;"operate"!==g.property&&!1!==g.visible&&v.push({header:g.title,key:g.property,width:20})}}catch(Z){C.e(Z)}finally{C.f()}var y={name:"规则统计",column:{complex:!1,columns:v},rows:this.$refs.rule[0].$refs.xTable.getTableData().tableData};e.sheets.push(y)}if(this.$refs.item){var w,b=this.$refs.item[0].$refs.xTable.getColumns(),T=[],x=(0,i.Z)(b);try{for(x.s();!(w=x.n()).done;){var D=w.value;"operate"!==D.property&&!1!==D.visible&&T.push({header:D.title,key:D.property,width:20})}}catch(Z){x.e(Z)}finally{x.f()}var $={name:"项目统计",column:{complex:!1,columns:T},rows:this.$refs.item[0].$refs.xTable.getTableData().tableData};e.sheets.push($)}this.Base.generateExcel(e)}}},g=C,y=a(1001),w=(0,y.Z)(g,o,n,!1,null,"156fd46e",null),b=w.exports},32222:function(e,t,a){a.r(t),a.d(t,{default:function(){return p}});var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("shallowDrill",{attrs:{nameList:e.nameList,searchType:e.searchType,searchCondition:e.searchCondition}})},n=[],i=a(19597),s={components:{shallowDrill:i.Z},name:"doctorStatistic",data:function(){return{nameList:[],searchType:"doctor",searchCondition:{}}},created:function(){Base.getNowPageParam().nameList&&(this.nameList=JSON.parse(Base.getNowPageParam().nameList)),this.nameList.push({name:"医生",value:2}),Base.getNowPageParam().searchCondition&&(this.searchCondition=JSON.parse(Base.getNowPageParam().searchCondition))},mounted:function(){},methods:{}},r=s,c=a(1001),l=(0,c.Z)(r,o,n,!1,null,"3cbe1db0",null),p=l.exports}}]);
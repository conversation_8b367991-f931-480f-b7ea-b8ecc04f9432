(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[9650],{88412:function(t,e,a){"use strict";var i=a(26263),r=a(36766),o=a(1001),n=(0,o.Z)(r.Z,i.s,i.x,!1,null,"5e7ef0ae",null);e["Z"]=n.exports},76406:function(t,e,a){"use strict";a.r(e),a.d(e,{default:function(){return h}});var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"fit"},[a("ta-border-layout",{attrs:{"layout-type":"fixTop"},scopedSlots:t._u([{key:"header",fn:function(){return[a("ta-form",{staticStyle:{"margin-top":"20px"},attrs:{autoFormCreate:t.autoFormCreate,col:3,formLayout:!0}},[a("ta-form-item",{attrs:{require:!1,showInfo:!0,"field-decorator-id":"diseaseInfo",label:"病种信息","label-col":{span:7},span:5,"wrapper-col":{span:17}}},[a("ta-input",{attrs:{placeholder:"请输入病种名称或编码"}})],1),a("ta-form-item",{attrs:{require:!1,showInfo:!0,"field-decorator-id":"itemInfo",label:"项目信息","label-col":{span:7},span:5,"wrapper-col":{span:17}}},[a("ta-input",{attrs:{placeholder:"请输入医保项目名称或编码"}})],1),a("ta-form-item",{staticStyle:{"text-align":"right"},attrs:{require:!1,showInfo:!0,label:" ","label-col":{span:6},span:14,"wrapper-col":{span:18}}},[a("ta-button",{staticStyle:{"margin-right":"10px"},attrs:{type:"primary"},on:{click:t.queryTableData}},[t._v("查询 ")]),a("ta-button",{staticStyle:{"margin-right":"10px"},on:{click:t.fnReset}},[t._v("重置 ")])],1)],1)]},proxy:!0}])},[a("ta-card",{staticClass:"fit"},[a("ta-title",{attrs:{title:"结果查询"}}),a("ta-big-table",{ref:"infoTableRef",attrs:{data:t.tableData,height:"95%","export-config":{},"import-config":{},border:"","highlight-hover-row":"","highlight-current-row":"","auto-resize":"","show-overflow":"",resizable:"",size:"large"},scopedSlots:t._u([{key:"topBar",fn:function(){return[a("ta-checkbox",{on:{change:t.onChange}},[t._v("隐藏医保项目编码")])]},proxy:!0}])},[a("ta-big-table-column",{attrs:{width:50,title:"序号",type:"seq"}}),a("ta-big-table-column",{attrs:{align:"center",field:"diseaseCode","header-align":"center",title:"病种编码"}}),a("ta-big-table-column",{attrs:{align:"center",field:"diseaseName","header-align":"center",title:"病种名称"}}),a("ta-big-table-column",{attrs:{align:"center",field:"itemCode","header-align":"center",title:"医保项目编码"}}),a("ta-big-table-column",{attrs:{align:"center",field:"itemName","header-align":"center",title:"医保项目名称"}}),a("ta-big-table-column",{attrs:{align:"center",field:"ruleDetail","header-align":"center",title:"医保限制条件"}}),a("template",{slot:"bottomBar"},[a("ta-pagination",{ref:"gridPager",staticStyle:{"text-align":"right","margin-right":"120px"},attrs:{dataSource:t.tableData,defaultPageSize:200,pageSizeOptions:["30","50","100","200","500"],params:t.getParam,url:"diseaseManage/queryListByItemCode"},on:{"update:dataSource":function(e){t.tableData=e},"update:data-source":function(e){t.tableData=e}}}),a("ta-button",{staticStyle:{position:"absolute",right:"20px",bottom:"12px"},attrs:{type:"primary",icon:"upload"},on:{click:t.exportExcel}},[t._v("导出 ")])],1)],2)],1)],1)],1)},r=[],o=a(66347),n=a(95082),s=a(88412),l=a(22722),c=a(55115);c.w3.prototype.Base=Object.assign(c.w3.prototype.Base,(0,n.Z)({},l.Z));var u={name:"diseasetemQuery",components:{TaTitle:s.Z},data:function(){return{tableData:[],isAdd:!0,isShowItemCode:!1,drawerVisible:!1,selectedData:{}}},mounted:function(){this.queryTableData()},methods:{onChange:function(t){t.target.checked?this.$refs.infoTableRef.hideColumn(this.$refs.infoTableRef.getColumnByField("itemCode")):this.$refs.infoTableRef.showColumn(this.$refs.infoTableRef.getColumnByField("itemCode")),this.isShowItemCode=t.target.checked,this.queryTableData()},handleChange:function(t){t.file.status,"done"===t.file.status?message.success("".concat(t.file.name," 上传成功")):"error"===t.file.status&&message.error("".concat(t.file.name," 上传失败"))},queryTableData:function(){this.$refs.gridPager.loadData()},fnReset:function(){this.baseInfoForm.resetFields()},autoFormCreate:function(t){this.baseInfoForm=t},getParam:function(){var t=this.baseInfoForm.getFieldsValue();return t.showItemCode=this.isShowItemCode,t},fnImport:function(){},exportExcel:function(){var t,e=this,a=[],i=this.$refs.infoTableRef.getColumns(),r=(0,o.Z)(i);try{for(r.s();!(t=r.n()).done;){var n=t.value;"序号"!==n.title&&"操作"!==n.title&&a.push({header:n.title,key:n.property,width:20})}}catch(s){r.e(s)}finally{r.f()}this.Base.submit(null,{url:"diseaseManage/exportDiseaseItemByItemCode",data:this.getParam(),autoValid:!1},{successCallback:function(t){var i={fileName:"病种项目匹配数据表",sheets:[{name:"worksheet1",column:{complex:!1,columns:a},rows:t.data.diseaseItemList}]};e.Base.generateExcel(i)},failCallback:function(t){e.$message.error("病种项目匹配数据表导出失败")}})},activeMethod:function(t){var e=t.row,a=(t.rowIndex,t.column,t.columnIndex);return 4!==a||4===a&&"2"===e.columnType},saveConfig:function(){var t=this,e=this.form_edit.getFieldsValue();this.isAdd?Base.submit(this.form_edit,{url:"addDemoModel",method:"post",data:e,autoValid:!0}).then((function(e){t.$message.success("新增成功",3),t.queryTableData(),t.drawerVisible=!1})):(e.configId=this.selectedData.configId,Base.submit(this.form_edit,{url:"updateDemoRecord",method:"post",data:e,autoValid:!0}).then((function(e){t.$message.success("修改成功",3),t.queryTableData(),t.drawerVisible=!1})))}}},f=u,d=a(1001),m=(0,d.Z)(f,i,r,!1,null,null,null),h=m.exports},36766:function(t,e,a){"use strict";var i=a(66586);e["Z"]=i.Z},26263:function(t,e,a){"use strict";a.d(e,{s:function(){return i},x:function(){return r}});var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},r=[]},66586:function(t,e){"use strict";var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};e["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}},55382:function(){},61219:function(){}}]);
(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[8490],{88412:function(t,e,a){"use strict";var r=a(26263),i=a(36766),l=a(1001),n=(0,l.Z)(i.Z,r.s,r.x,!1,null,"5e7ef0ae",null);e["Z"]=n.exports},51446:function(t,e,a){"use strict";a.r(e),a.d(e,{default:function(){return x}});var r=function(){var t=this,e=this,a=e.$createElement,r=e._self._c||a;return r("div",{staticClass:"fit"},[r("ta-border-layout",{attrs:{"footer-cfg":{showBorder:!1},layout:{header:"110px",footer:"0px"},showPadding:!0}},[r("div",{attrs:{slot:"header"},slot:"header"},[r("ta-title",{attrs:{title:"查询条件"}}),r("ta-form",{staticStyle:{"padding-top":"5px"},attrs:{autoFormCreate:function(e){t.baseInfoForm=e},col:2,formLayout:!0}},[r("ta-form-item",{attrs:{fieldDecoratorOptions:{rules:[{required:!0,message:"必须选取日期"}]},"init-value":e.rangeValue,"label-col":{span:7},required:!0,span:4,"wrapper-col":{span:17},fieldDecoratorId:"allDate",label:"时间范围"}},[r("ta-range-picker",{attrs:{"allow-one":!0}},[r("ta-icon",{attrs:{slot:"suffixIcon",type:""},slot:"suffixIcon"})],1)],1),r("ta-form-item",{attrs:{"label-col":{span:8},span:4,"wrapper-col":{span:16},"field-decorator-id":"aae500",initValue:"1",label:"审核场景"}},[r("ta-select",{attrs:{"collection-filter":e.filterList,reverseFilter:!0,"collection-type":"AAE500",placeholder:"请选择开单场景"}})],1),r("ta-form-item",{attrs:{"label-col":{span:8},span:4,"wrapper-col":{span:16},"field-decorator-id":"akb020",label:"院区标识"}},[r("ta-select",{attrs:{options:e.akb020List,allowClear:"",placeholder:"院区选择"},on:{change:e.departSelectChange}})],1),r("ta-form-item",{attrs:{"label-col":{span:8},span:4,virtual:!0,"wrapper-col":{span:16},"field-decorator-id":"aaz307",label:"科室名称"}},[r("ta-select",{attrs:{options:e.ksList,"show-search":!0,allowClear:"",placeholder:"科室名称筛选",showSearch:""}})],1),r("ta-form-item",{attrs:{"label-col":{span:8},span:4,"wrapper-col":{span:16},"field-decorator-id":"aaz263",label:"医师姓名"}},[r("ta-select",{attrs:{options:e.doctorList,"show-search":!0,allowClear:"",placeholder:"医师姓名筛选",showSearch:""}})],1),r("ta-form-item",{attrs:{"label-col":{span:8},span:4,"wrapper-col":{span:16},label:" "}},[r("ta-button",{attrs:{icon:"search",type:"primary"},on:{click:e.fnQuery}},[e._v("查询")]),r("ta-button",{attrs:{icon:"redo",type:"default"},on:{click:e.fnReset}},[e._v("重置")])],1)],1)],1),r("div",{staticClass:"fit",staticStyle:{height:"86%"}},[r("ta-title",{staticStyle:{width:"50%"},attrs:{title:"查询结果"}}),r("ta-big-table",{ref:"Table",attrs:{data:e.userList,"tooltip-config":{enabled:!0,contentMethod:e.showTooltipMethod},"auto-resize":"",border:"","empty-text":"-",height:"100%","highlight-current-row":"","highlight-hover-row":"",resizable:"","show-overflow":""},on:{"cell-click":e.cellClickEvent}},[r("ta-big-table-column",{attrs:{align:"center","header-align":"center","min-width":"50",sortable:"",title:"序号",type:"seq"}}),r("ta-big-table-column",{attrs:{align:"left",field:"aae386","header-align":"center","min-width":"180",title:"科室名称"}}),r("ta-big-table-column",{attrs:{align:"left",field:"aac003","header-align":"center","min-width":"180",title:"医师姓名"}}),r("ta-big-table-column",{attrs:{align:"right",field:"bacs","header-align":"center","min-width":"130",title:"备案次数"}}),r("ta-big-table-column",{attrs:{formatter:e.ratioFormat,align:"right",field:"bacszb","header-align":"center","min-width":"180",title:"备案次数占比"}}),r("ta-big-table-column",{attrs:{align:"right",field:"zfxms","header-align":"center","min-width":"130",title:"自费次数(次)"}}),r("ta-big-table-column",{attrs:{formatter:e.ratioFormat,align:"right",field:"zfxmzb","header-align":"center","min-width":"150",title:"自费次数占比"}}),r("ta-big-table-column",{attrs:{align:"right",field:"qxxms","header-align":"center","min-width":"130",title:"取消次数(次)"}}),r("ta-big-table-column",{attrs:{formatter:e.ratioFormat,align:"right",field:"qxxmzb","header-align":"center","min-width":"150",title:"取消次数占比"}}),r("ta-big-table-column",{attrs:{align:"right",field:"shzl","header-align":"center","min-width":"140",title:"审核总次数"}}),r("ta-big-table-column",{attrs:{align:"right",field:"txzl","header-align":"center","min-width":"140",title:"提醒总次数"}}),r("ta-big-table-column",{attrs:{formatter:e.ratioFormat,align:"right",field:"txl","header-align":"center","min-width":"80",title:"提醒率"}}),r("ta-big-table-column",{attrs:{formatter:e.moneyFormat,align:"right",field:"txje","header-align":"center","min-width":"140",title:"提醒金额"}}),r("ta-big-table-column",{attrs:{align:"right",field:"txsl","header-align":"center","min-width":"140",title:"提醒数量"}}),r("template",{slot:"bottomBar"},[r("ta-button",{staticStyle:{float:"right","margin-top":"20px"},attrs:{icon:"download",type:"primary"},on:{click:e.exportTable}},[e._v("导出 ")]),r("ta-pagination",{ref:"gridPager",staticStyle:{float:"right","margin-top":"20px","margin-right":"10px"},attrs:{"data-source":e.userList,defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"],params:e.infoPageParams,url:"reportStatistics/queryReportStatistics"},on:{"update:dataSource":function(t){e.userList=t},"update:data-source":function(t){e.userList=t}}})],1)],2)],1)])],1)},i=[],l=a(66347),n=a(48534),o=a(95082),s=(a(36133),a(88412)),c=a(36797),u=a.n(c),f=a(22722),m=a(55115),d=a(94462);m.w3.prototype.Base=Object.assign(m.w3.prototype.Base,(0,o.Z)({},f.Z));var h={name:"operationYS",components:{TaTitle:s.Z},data:function(){return{userList:[],filterList:"",showTotalTimesDetail:!1,rangeValue:[this.Base.getMoment((new Date).toISOString().slice(0,8)+"-01","YYYY-MM-DD"),this.Base.getMoment((new Date).toISOString().slice(0,10),"YYYY-MM-DD")],akb020:"",ksList:[],akb020List:[],doctorList:[],jxzb:"",txl:""}},mounted:function(){var t=this;return(0,n.Z)(regeneratorRuntime.mark((function e(){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.fnQueryHos(),t.fnQueryDept(""),t.$route.query.params&&(a=JSON.parse(t.$route.query.params),a.allDate=a.allDate.map((function(e){var a=new Date(e);return a.setDate(a.getDate()+1),t.Base.getMoment(a.toISOString().slice(0,11),"YYYY-MM-DD")})),t.$route.query.aaz307&&(a.aaz307=t.$route.query.aaz307),t.baseInfoForm.setFieldsValue(a)),t.queryOptons();case 4:case"end":return e.stop()}}),e)})))()},methods:{moment:u(),fnQueryHos:function(){var t=this,e={url:"/miimCommonRead/queryHospRpcList",showPageLoading:!1,autoValid:!0},a={successCallback:function(e){t.akb020List=e.data.resultData},failCallback:function(e){t.$message.error("医院数据加载失败")}};this.Base.submit(null,e,a)},departSelectChange:function(t){this.fnQueryDept(t)},queryOptons:function(){var t=this,e=[],a=["2","3","4","5","6","7","17","18"];this.Base.submit(null,{url:"miimCommonRead/getAa01AAE500StartStop",data:{aaz499s:JSON.stringify(a)}},{successCallback:function(a){var r=a.data.aae500List;r.includes(12)||r.includes(4)||r.includes(5)||r.includes(7)?(e=r.filter((function(t){return 12!==t&&4!==t&&5!==t&&7!==t})),e.push("dscg")):e=r,t.filterList=e.join(","),t.baseInfoForm.setFieldsValue({scene:e[0].toString()}),t.fnQuery()},failCallback:function(e){t.$message.error("查询场景开关失败")}})},showTooltipMethod:function(t){var e=t.type,a=t.column,r=(t.row,t.items,t._columnIndex,a.property);if("header"===e)return d.Z.props[r]?d.Z.props[r][e]:""},cellClickEvent:function(t){t.row,t.column},exportTable:function(){var t=this,e=this.baseInfoForm.getFieldsValue();if(void 0===e.allDate||e.allDate.length<2)this.$message.error("请选择时间范围！");else{e=this.infoPageParams();var a,r=[],i=this.$refs.Table.getColumns(),n=(0,l.Z)(i);try{for(n.s();!(a=n.n()).done;){var o=a.value;"seq"!==o.type&&"operate"!==o.property&&!1!==o.visible&&r.push({header:o.title,key:o.property,width:20})}}catch(c){n.e(c)}finally{n.f()}var s=i.map((function(t){return t.property}));d.Z.codelist.filter((function(t){return s.includes(t.columnKey)}));this.Base.submit(null,{url:"reportStatistics/exportExcel",data:e,autoValid:!1},{successCallback:function(e){var a=e.data.data,i={fileName:"医师操作统计-按医师结果表",sheets:[{name:"worksheet1",column:{complex:!1,columns:r},rows:a,codeList:d.Z.codelist}]};t.Base.generateExcel(i)},failCallback:function(e){t.$message.error("数据加载失败")}})}},formatAmount:function(t){return t.toFixed(2).replace(/\d(?=(\d{3})+\.)/g,"$&,")},moneyFormat:function(t){var e=t.cellValue;return"--"==e?e:this.formatAmount(e)},ratioFormat:function(t){var e=t.cellValue;return 0==e?"0":e+"%"},fnReset:function(){this.baseInfoForm.resetFields(),this.txl="",this.jxzb="",this.fnQuery()},fnQueryDocter:function(){var t=this,e={akb020:this.akb020};this.Base.submit(null,{url:"miimCommonRead/queryDoctorRpcList",data:e,autoValid:!1},{successCallback:function(e){t.doctorList=e.data.resultData},failCallback:function(e){t.$message.error("医师数据加载失败")}})},fnQueryDept:function(t){var e=this;this.akb020=t||"",this.Base.submit(null,{url:"miimCommonRead/queryDepartRpcList",data:{akb020:this.akb020,hospDeptType:"I"},autoValid:!1},{successCallback:function(t){e.ksList=t.data.resultData,e.fnQueryDocter()},failCallback:function(t){e.$message.error("科室数据加载失败")}})},infoPageParams:function(){var t=this.baseInfoForm.getFieldsValue();return t.startDate=t.allDate[0].format("YYYY-MM-DD HH:mm:ss"),t.endDate=t.allDate[1].format("YYYY-MM-DD HH:mm:ss"),t.txl=this.txl,t.txlcom=this.txl||0===this.txl?t.txlcom:"",t.jxzb=this.jxzb,t.jxzbcom=this.jxzb||0===this.jxzb?t.jxzbcom:"",t.flag="ys",t},fnQuery:function(){var t=this,e=this.baseInfoForm.getFieldValue("allDate");!e||e[0]&&e[1]?this.$nextTick((function(){t.$refs.gridPager.loadData((function(t){}))})):this.$message.error("请选择时间范围！")},fnCustomHeaderCell:function(t){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px",fontWeight:"bold"}}},filterOption:function(t,e){return e.componentOptions.children[0].text.indexOf(t)>=0}}},p=h,g=a(1001),b=(0,g.Z)(p,r,i,!1,null,"5edc0dd1",null),x=b.exports},36766:function(t,e,a){"use strict";var r=a(66586);e["Z"]=r.Z},26263:function(t,e,a){"use strict";a.d(e,{s:function(){return r},x:function(){return i}});var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},i=[]},66586:function(t,e){"use strict";var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};e["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}},94462:function(t,e){"use strict";var a=[{columnKey:"bacszb",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}},{columnKey:"txl",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}},{columnKey:"qxxmzb",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}},{columnKey:"zfxmzb",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}}],r={txl:{header:function(){return"提醒率=提醒总次数/审核总次数"}},jxxmzb:{header:function(){return"继续使用占比=继续使用次数(次)/提醒总次数"}},qxxmzb:{header:function(){return"取消使用占比=取消使用次数/提醒总次数"}},zfxmzb:{header:function(){return"自费使用占比=自费使用次数/提醒总次数"}},wczxmzb:{header:function(){return"无操作使用占比=无操作使用次数/提醒总次数"}}};e["Z"]={codelist:a,props:r}},55382:function(){},61219:function(){}}]);
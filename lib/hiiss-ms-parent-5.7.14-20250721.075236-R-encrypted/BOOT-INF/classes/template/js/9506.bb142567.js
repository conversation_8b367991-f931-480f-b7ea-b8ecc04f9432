"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[9506],{88412:function(t,e,a){var l=a(26263),o=a(36766),i=a(1001),r=(0,i.Z)(o.Z,l.s,l.x,!1,null,"5e7ef0ae",null);e["Z"]=r.exports},9506:function(t,e,a){a.r(e),a.d(e,{default:function(){return m}});var l=function(){var t=this,e=this,a=e.$createElement,l=e._self._c||a;return l("div",{staticClass:"fit"},[l("ta-border-layout",{attrs:{layout:{header:"70px",footer:"55px"},showPadding:!1,"footer-cfg":{showBorder:!1}}},[l("div",{attrs:{slot:"header"},slot:"header"},[l("ta-form",{staticStyle:{position:"absolute",width:"100%","margin-top":"10px"},attrs:{autoFormCreate:function(e){return t.form=e},layout:"horizontal",formLayout:!0}},[l("ta-form-item",{attrs:{fieldDecoratorId:"tabid",fieldDecoratorOptions:{initialValue:e.tabid},span:4,hidden:!0}},[l("ta-input")],1),l("ta-form-item",{staticStyle:{"margin-left":"2%"},attrs:{fieldDecoratorId:"MSYGroup",fieldDecoratorOptions:{initialValue:e.dateFlag},wrapperCol:{span:24},span:5}},[l("ta-radio-group",{attrs:{options:[{label:"月",value:"M"},{label:"季",value:"S"},{label:"年",value:"Y"}]},on:{change:e.handleChangeDateSelect}})],1),l("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:"M"==e.dateFlag,expression:"dateFlag=='M'"}],staticStyle:{"margin-left":"-10%"},attrs:{fieldDecoratorId:"aae043M",wrapperCol:{span:15},require:{message:"必输项!"},fieldDecoratorOptions:{initialValue:e.defaultDateMonth},span:3}},[l("ta-month-picker",{staticStyle:{width:"100%"},attrs:{disabledDate:e.disabledDate,placeholder:"选择月份",allowClear:!1}})],1),l("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:"S"==e.dateFlag,expression:"dateFlag=='S'"}],staticStyle:{"margin-left":"-10%"},attrs:{fieldDecoratorId:"aae043S",wrapperCol:{span:15},require:{message:"必输项!"},fieldDecoratorOptions:{initialValue:e.defaultDateQuarter},span:3}},[l("ta-quarter-picker",{staticStyle:{width:"100%"},attrs:{disabledDate:e.disabledDate,placeholder:"选择季度",allowClear:!1}})],1),l("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:"Y"==e.dateFlag,expression:"dateFlag=='Y'"}],staticStyle:{"margin-left":"-10%"},attrs:{fieldDecoratorId:"aae043Y",wrapperCol:{span:15},require:{message:"必输项!"},fieldDecoratorOptions:{initialValue:e.defaultDateYear},span:3}},[l("ta-year-picker",{staticStyle:{width:"100%"},attrs:{disabledDate:e.disabledDate,placeholder:"选择年份",allowClear:!1}})],1),4!=e.key?l("ta-form-item",{staticStyle:{"margin-left":"-3%"},attrs:{fieldDecoratorId:"aka130",labelCol:{span:9},wrapperCol:{span:12},require:{message:"必输项!"},span:4,initValue:e.CollectionData("AKA130ZK")?e.CollectionData("AKA130ZK")[0].value:""}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("医疗类型")]),l("ta-select",{staticStyle:{width:"100%"},attrs:{"collection-type":"AKA130ZK",allowClear:!1}})],1):e._e(),l("ta-form-item",{attrs:{fieldDecoratorId:"aae141",labelCol:{span:8},wrapperCol:{span:15},span:4}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("医保类型")]),l("ta-select",{attrs:{"collection-type":"AAE141ZK",allowClear:!0}})],1),l("ta-form-item",{attrs:{fieldDecoratorId:"aae140",labelCol:{span:8},wrapperCol:{span:15},span:4}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("险种类型")]),l("ta-select",{attrs:{"collection-type":"AAE140ZK",allowClear:!0}})],1),l("ta-form-item",{attrs:{fieldDecoratorId:"yzb004",labelCol:{span:8},wrapperCol:{span:15},span:4}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("指标名称")]),l("ta-select",{staticStyle:{width:"100%"},attrs:{allowClear:!0,options:e.indicatorsData}})],1),l("ta-button",{staticStyle:{float:"right","margin-right":"10px"},attrs:{icon:"search",type:"primary"},on:{click:e.handleQuery}},[e._v("查询")])],1)],1),l("div",{staticClass:"fit"},[l("ta-title",{attrs:{title:"指标数据汇总"}}),l("ta-tabs",{staticClass:"fit",staticStyle:{height:"93%"},attrs:{type:"card",tabBarGutter:10},on:{change:e.handleChangeTab},model:{value:e.activeKey,callback:function(t){e.activeKey=t},expression:"activeKey"}},[l("ta-tab-pane",{key:"qy",attrs:{tab:"全院",forceRender:!0}},[l("ta-table",{key:(new Date).getTime()+"qy",ref:"qyTableRef",staticClass:"fit",attrs:{columns:e.qyTableColumns,dataSource:e.qyTableData,bordered:!0,size:"small",scroll:{y:"100%"},customRow:e.fnCustomRow},on:{"update:columns":function(t){e.qyTableColumns=t}},scopedSlots:e._u([{key:"ckz",fn:function(t,a,o){return[t>0?l("span",{staticStyle:{color:"red"}},[e._v(e._s(t)),l("ta-icon",{attrs:{type:"arrow-up"}})],1):t<0?l("span",{staticStyle:{color:"blue"}},[e._v(e._s(t)),l("ta-icon",{attrs:{type:"arrow-down"}})],1):l("span",[e._v("0")])]}},{key:"yzb012",fn:function(t,a,o){return[t>0?l("span",{staticStyle:{color:"red"}},[e._v(e._s(t)),l("ta-icon",{attrs:{type:"arrow-up"}})],1):t<0?l("span",{staticStyle:{color:"blue"}},[e._v(e._s(t)),l("ta-icon",{attrs:{type:"arrow-down"}})],1):l("span",[e._v(e._s(t))])]}},{key:"yzb013",fn:function(t,a,o){return[t>0?l("span",{staticStyle:{color:"red"}},[e._v(e._s(t)),l("ta-icon",{attrs:{type:"arrow-up"}})],1):t<0?l("span",{staticStyle:{color:"blue"}},[e._v(e._s(t)),l("ta-icon",{attrs:{type:"arrow-down"}})],1):l("span",[e._v(e._s(t))])]}}])})],1),l("ta-tab-pane",{key:"ks",attrs:{tab:"科室",forceRender:!0}},[l("ta-table",{key:(new Date).getTime()+"ks",ref:"ksTableRef",staticClass:"fit",attrs:{columns:e.ksTableColumns,dataSource:e.ksTableData,bordered:!0,size:"small",scroll:{y:"100%"},customRow:e.fnCustomRow},on:{"update:columns":function(t){e.ksTableColumns=t}},scopedSlots:e._u([{key:"ckz",fn:function(t,a,o){return[t>0?l("span",{staticStyle:{color:"red"}},[e._v(e._s(t)),l("ta-icon",{attrs:{type:"arrow-up"}})],1):t<0?l("span",{staticStyle:{color:"blue"}},[e._v(e._s(t)),l("ta-icon",{attrs:{type:"arrow-down"}})],1):l("span",[e._v("0")])]}},{key:"yzb012",fn:function(t,a,o){return[t>0?l("span",{staticStyle:{color:"red"}},[e._v(e._s(t)),l("ta-icon",{attrs:{type:"arrow-up"}})],1):t<0?l("span",{staticStyle:{color:"blue"}},[e._v(e._s(t)),l("ta-icon",{attrs:{type:"arrow-down"}})],1):l("span",[e._v(e._s(t))])]}},{key:"yzb013",fn:function(t,a,o){return[t>0?l("span",{staticStyle:{color:"red"}},[e._v(e._s(t)),l("ta-icon",{attrs:{type:"arrow-up"}})],1):t<0?l("span",{staticStyle:{color:"blue"}},[e._v(e._s(t)),l("ta-icon",{attrs:{type:"arrow-down"}})],1):l("span",[e._v(e._s(t))])]}}])})],1)],1)],1),l("div",{attrs:{slot:"footer"},slot:"footer"},[l("ta-button",{staticStyle:{position:"absolute",right:"10px",bottom:"15px"},attrs:{type:"primary",icon:"download"},on:{click:function(t){return e.handleExportData(2)}}},[e._v("导出")]),l("ta-pagination",{directives:[{name:"show",rawName:"v-show",value:"qy"==e.activeKey,expression:"activeKey=='qy'"}],ref:"qyPageRef",staticStyle:{float:"right","margin-top":"8px","margin-right":"120px"},attrs:{showSizeChanger:"",showQuickJumper:"",dataSource:e.qyTableData,params:e.pageParams,defaultPageSize:10,pageSizeOptions:["10","20","40"],url:"indicatorsMonitor/queryByPage"},on:{"update:dataSource":function(t){e.qyTableData=t},"update:data-source":function(t){e.qyTableData=t}}}),l("ta-pagination",{directives:[{name:"show",rawName:"v-show",value:"ks"==e.activeKey,expression:"activeKey=='ks'"}],ref:"ksPageRef",staticStyle:{float:"right","margin-top":"8px","margin-right":"120px"},attrs:{showSizeChanger:"",showQuickJumper:"",dataSource:e.ksTableData,params:e.pageParams,defaultPageSize:10,pageSizeOptions:["10","20","40"],url:"indicatorsMonitor/queryByPage"},on:{"update:dataSource":function(t){e.ksTableData=t},"update:data-source":function(t){e.ksTableData=t}}})],1)])],1)},o=[],i=a(66347),r=a(95082),s=a(88412),n=a(36797),c=a.n(n),d=[],u={name:"job",components:{TaTitle:s.Z},data:function(){var t=[{title:"id",dataIndex:"aaz001",align:"center",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"科室编码",dataIndex:"aaz307",align:"center",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"科室名称",dataIndex:"aae386",align:"left",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"指标编码",dataIndex:"yzb001",align:"center",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"医保类型",dataIndex:"aae141",align:"center",width:60,collectionType:"AAE141ZK",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"险种类型",dataIndex:"aae140",align:"center",width:60,collectionType:"AAE140ZK",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"指标名称",dataIndex:"yzb002",align:"center",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"指标类别",dataIndex:"yzb003",align:"center",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"指标值",dataIndex:"yzb099",align:"right",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"指标预警值",dataIndex:"yjz",align:"right",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"指标超控值",scopedSlots:{customRender:"ckz"},dataIndex:"ckz",align:"right",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"同期值",dataIndex:"yzb014",align:"right",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"同比",dataIndex:"yzb012",scopedSlots:{customRender:"yzb012"},align:"right",width:50,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"上期值",dataIndex:"yzb016",align:"right",width:50,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"环比",dataIndex:"yzb013",scopedSlots:{customRender:"yzb013"},align:"right",width:50,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell}];return{qyTableColumns:[].concat(t),qyTableData:[].concat(d),ksTableColumns:[].concat(t),ksTableData:[].concat(d),dateFlag:"M",defaultDateMonth:this.Base.getMoment((new Date).toISOString().slice(0,7),"YYYY-MM"),defaultDateQuarter:this.Base.getMoment(c()().year()+"-"+c()().quarter()+"季度","YYYY-Q季度"),defaultDateYear:this.Base.getMoment((new Date).toISOString().slice(0,4),"YYYY"),activeKey:"qy",tabid:"all",indicatorsData:[],key:""}},methods:{fnCustomHeaderCell:function(t){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px",fontWeight:"bold"}}},disabledDate:function(t){return t>c()().startOf("day")},handleChangeDateSelect:function(t){this.dateFlag=t.target.value},handleQuery:function(){"qy"==this.activeKey?this.$refs.qyPageRef.loadData():this.$refs.ksPageRef.loadData()},handleChangeTab:function(t){"qy"==t?this.form.setFieldsValue({tabid:"all"}):this.form.setFieldsValue({tabid:"depart"}),this.handleQuery()},pageParams:function(){var t=this.form.getFieldsValue();return"M"==this.dateFlag?t.date=t.aae043M.format("YYYYMM"):"S"==this.dateFlag?t.date=t.aae043S.format("YYYYQ"):t.date=t.aae043Y.format("YYYY"),delete t.aae043M,delete t.aae043S,delete t.aae043Y,t},handleExportData:function(t){if(2==t){var e="";return e="qy"==this.activeKey?"全院指标数据.xls":"科室指标数据.xls",void Base.downloadFile({url:"indicatorsMonitor/exportData",options:(0,r.Z)({},this.pageParams()),type:"application/excel",fileName:e}).then((function(t){})).catch((function(t){}))}var a=[],l=[],o=[];"qy"==this.activeKey?(o=this.qyTableColumns,l=this.qyTableData):(o=this.ksTableColumns,l=this.ksTableData);var s,n=(0,i.Z)(o);try{for(n.s();!(s=n.n()).done;){var c=s.value;a.push({header:c.title,key:c.dataIndex,width:20})}}catch(u){n.e(u)}finally{n.f()}var d={sheets:[{name:"worksheet1",column:{complex:!1,columns:a},rows:l,codeList:[{codeType:"AAE140ZK",columnKey:"aae140"}]}]};this.Base.generateExcel(d)},fnInitData:function(){var t=this;this.$refs.qyTableRef.hideColumns(["aaz001","aaz307","aae386","yzb001","yzb003"]),this.$refs.ksTableRef.hideColumns(["aaz001","aaz307","yzb001","yzb003"]),this.Base.submit(null,{url:"indicatorsWarnConfig/queryIndicators",data:{},autoValid:!0},{successCallback:function(e){t.indicatorsData=e.data.indicatorsData},failCallback:function(t){}}),this.handleQuery()},fnCustomRow:function(t,e){var a=this;return{props:{draggable:!0},style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px"},on:{click:function(e){var l="",o="",i="",r=t.yzb002,s=t.yzb001,n=t.yzb003;if("106"==s&&"2"==n)l="indexMonitor.html#/keyIndicators/cjtczf?key=1";else if("106"==s&&"4"==n)l="indexMonitor.html#/keyIndicators/rjtczf?key=2";else if("97"==s&&"7"==n)l="indexMonitor.html#/keyIndicators/rtrcb?key=3";else if("99"==s&&"3"==n)l="indexMonitor.html#/keyIndicators/cwsyl?key=4";else if("98"==s&&"1"==n)l="indexMonitor.html#/keyIndicators/geylfy?key=5";else if("100"==s&&"1"==n)l="indexMonitor.html#/keyIndicators/zfy?key=6";else if("100"==s&&"2"==n)l="indexMonitor.html#/keyIndicators/zfycj?key=7";else if("101"==s&&"3"==n)l="indexMonitor.html#/keyIndicators/yzb?key=8";else if("104"==s&&"3"==n)l="indexMonitor.html#/keyIndicators/clzb?key=9";else{if("115"!=s||"6"!=n)return void a.$message.warning("当前指标没有分析功能！");l="indexMonitor.html#/keyIndicators/zyrc?key=10"}"qy"==a.activeKey?a.Base.submit(null,{url:"indicatorsMonitor/queryMenu",data:{url:l},autoValid:!0},{successCallback:function(e){null!=e.data.menu?(o=e.data.menu.RESOURCEID,i=i+"&aae141="+t.aae141+"&aae140="+t.aae140,l=l+"&yzb001="+s,l+=i,l=l+"&ape032="+a.dateFlag,l="M"==a.dateFlag?l+"&aae043="+a.form.getFieldValue("aae043M").format("YYYYMM"):"S"==a.dateFlag?l+"&aae043="+a.form.getFieldValue("aae043S").format("YYYYQ"):l+"&aae043="+a.form.getFieldValue("aae043Y").format("YYYY"),l=l+"&title="+r,a.Base.closeTabMenu(o),a.Base.openTabMenu({id:o,name:r,url:l,refresh:!0})):a.$message.warning("当前指标没有分析功能！")},failCallback:function(t){}}):(l+="&obj=3",o=s+"--"+n+"--"+t.aaz307,r=r+"--"+t.aae386,i=i+"&aaz307="+t.aaz307+"&aae386="+t.aae386,i=i+"&aae141="+t.aae141+"&aae140="+t.aae140,l=l+"&yzb001="+s,l+=i,l=l+"&ape032="+a.dateFlag,l="M"==a.dateFlag?l+"&aae043="+a.form.getFieldValue("aae043M").format("YYYYMM"):"S"==a.dateFlag?l+"&aae043="+a.form.getFieldValue("aae043S").format("YYYYQ"):l+"&aae043="+a.form.getFieldValue("aae043Y").format("YYYY"),l=l+"&title="+r,a.Base.closeTabMenu(o),a.Base.openTabMenu({id:o,name:r,url:l,refresh:!0}))}}}}},mounted:function(){this.fnInitData()}},f=u,p=a(1001),y=(0,p.Z)(f,l,o,!1,null,"308e94da",null),m=y.exports},36766:function(t,e,a){var l=a(66586);e["Z"]=l.Z},26263:function(t,e,a){a.d(e,{s:function(){return l},x:function(){return o}});var l=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},o=[]},66586:function(t,e){var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};e["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}}}]);
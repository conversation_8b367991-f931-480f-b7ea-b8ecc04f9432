(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[630],{49957:function(e,t,n){"use strict";n.r(t)},83483:function(e,t,n){e=n.nmd(e);var i=n(57847)["default"];n(32564),n(39575),n(38012),function(t){"function"===typeof window.define?window.define.amd?window.define("wangEditor",["jquery"],t):window.define.cmd?window.define((function(e,n,i){return t})):t(window.jQuery):"object"===i(e)&&"object"===i(e.exports)?(n(49957),e.exports=t(n(77387))):t(window.jQuery)}((function(e){if(e&&e.fn&&e.fn.jquery){var t=function(t){var n=window.wangEditor;n&&t(n,e)};return function(e,t){if(e.wangEditor)alert("一个页面不能重复引用 wangEditor.js 或 wangEditor.min.js ！！！");else{var n=function(e){"string"===typeof e&&(e="#"+e);var n=t(e);if(1===n.length){var i=n[0].nodeName;"TEXTAREA"!==i&&"DIV"!==i||(this.valueNodeName=i.toLowerCase(),this.$valueContainer=n,this.$prev=n.prev(),this.$parent=n.parent(),this.init())}};n.fn=n.prototype,n.$body=t("body"),n.$document=t(document),n.$window=t(e),n.userAgent=navigator.userAgent,n.getComputedStyle=e.getComputedStyle,n.w3cRange="function"===typeof document.createRange,n.hostname=location.hostname.toLowerCase(),n.websiteHost="wangeditor.github.io|www.wangeditor.com|wangeditor.coding.me",n.isOnWebsite=n.websiteHost.indexOf(n.hostname)>=0,n.docsite="http://www.kancloud.cn/wangfupeng/wangeditor2/113961",e.wangEditor=n,n.plugin=function(e){n._plugins||(n._plugins=[]),"function"===typeof e&&n._plugins.push(e)}}}(window,e),t((function(e,t){e.fn.init=function(){this.initDefaultConfig(),this.addEditorContainer(),this.addTxt(),this.addMenuContainer(),this.menus={},this.commandHooks()}})),t((function(e,t){e.fn.ready=function(e){this.readyFns||(this.readyFns=[]),this.readyFns.push(e)},e.fn.readyHeadler=function(){var e=this.readyFns;while(e.length)e.shift().call(this)},e.fn.updateValue=function(){var e=this,t=e.$valueContainer,n=e.txt.$txt;if(t!==n){var i=n.html();t.val(i)}},e.fn.getInitValue=function(){var e=this,t=e.$valueContainer,n="",i=e.valueNodeName;return"div"===i?n=t.html():"textarea"===i&&(n=t.val()),n},e.fn.updateMenuStyle=function(){var e=this.menus;t.each(e,(function(e,t){t.updateSelected()}))},e.fn.enableMenusExcept=function(e){this._disabled||(e=e||[],"string"===typeof e&&(e=[e]),t.each(this.menus,(function(t,n){e.indexOf(t)>=0||n.disabled(!1)})))},e.fn.disableMenusExcept=function(e){this._disabled||(e=e||[],"string"===typeof e&&(e=[e]),t.each(this.menus,(function(t,n){e.indexOf(t)>=0||n.disabled(!0)})))},e.fn.hideDropPanelAndModal=function(){var e=this.menus;t.each(e,(function(e,t){var n=t.dropPanel||t.dropList||t.modal;n&&n.hide&&n.hide()}))}})),t((function(e,t){var n=!e.w3cRange;function i(){}e.fn.currentRange=function(e){if(!e)return this._rangeData;this._rangeData=e},e.fn.collapseRange=function(e,t){t=t||"end",t="start"===t,e=e||this.currentRange(),e&&(e.collapse(t),this.currentRange(e))},e.fn.getRangeText=n?i:function(e){if(e=e||this.currentRange(),e)return e.toString()},e.fn.getRangeElem=n?i:function(e){e=e||this.currentRange();var t=e.commonAncestorContainer;return 1===t.nodeType?t:t.parentNode},e.fn.isRangeEmpty=n?i:function(e){return e=e||this.currentRange(),!(!e||!e.startContainer||e.startContainer!==e.endContainer||e.startOffset!==e.endOffset)},e.fn.saveSelection=n?i:function(e){var n,i,a=this,o=a.txt.$txt.get(0);e?n=e.commonAncestorContainer:(i=document.getSelection(),i.getRangeAt&&i.rangeCount&&(e=document.getSelection().getRangeAt(0),n=e.commonAncestorContainer)),n&&(t.contains(o,n)||o===n)&&a.currentRange(e)},e.fn.restoreSelection=n?i:function(t){var n;if(t=t||this.currentRange(),t)try{n=document.getSelection(),n.removeAllRanges(),n.addRange(t)}catch(i){e.error("执行 editor.restoreSelection 时，IE可能会有异常，不影响使用")}},e.fn.restoreSelectionByElem=n?i:function(e,t){e&&(t=t||"end",this.setRangeByElem(e),"start"===t&&this.collapseRange(this.currentRange(),"start"),"end"===t&&this.collapseRange(this.currentRange(),"end"),this.restoreSelection())},e.fn.initSelection=n?i:function(){var e=this;if(!e.currentRange()){var t=e.txt.$txt,n=t.children().first();n.length&&e.restoreSelectionByElem(n.get(0))}},e.fn.setRangeByElem=n?i:function(e){var n=this,i=n.txt.$txt.get(0);if(e&&t.contains(i,e)){var a=e.firstChild;while(a){if(3===a.nodeType)break;a=a.firstChild}var o=e.lastChild;while(o){if(3===o.nodeType)break;o=o.lastChild}var r=document.createRange();a&&o?(r.setStart(a,0),r.setEnd(o,o.textContent.length)):(r.setStart(e,0),r.setEnd(e,0)),n.saveSelection(r)}}})),t((function(e,t){e.w3cRange||(e.fn.getRangeText=function(e){if(e=e||this.currentRange(),e)return e.text},e.fn.getRangeElem=function(e){if(e=e||this.currentRange(),e){var t=e.parentElement();return 1===t.nodeType?t:t.parentNode}},e.fn.isRangeEmpty=function(e){return e=e||this.currentRange(),!e||!e.text},e.fn.saveSelection=function(e){var n,i=this,a=i.txt.$txt.get(0);e?n=e.parentElement():(e=document.selection.createRange(),n="undefined"===typeof e.parentElement?null:e.parentElement()),n&&(t.contains(a,n)||a===n)&&i.currentRange(e)},e.fn.restoreSelection=function(e){var t,n=this;if(e=e||n.currentRange(),e){t=document.selection.createRange();try{t.setEndPoint("EndToEnd",e)}catch(i){}if(0===e.text.length)try{t.collapse(!1)}catch(i){}else t.setEndPoint("StartToStart",e);t.select()}})})),t((function(e,t){e.fn.commandHooks=function(){var e=this,n={insertHtml:function(n){var i,a=t(n),o=e.getRangeElem();i=e.getLegalTags(o),i&&t(i).after(a)}};e.commandHooks=n}})),t((function(e,t){e.fn.command=function(e,t,n,i){var a,o=this;function r(){t&&(o.queryCommandSupported(t)?document.execCommand(t,!1,n):(a=o.commandHooks,t in a&&a[t](n)))}this.customCommand(e,r,i)},e.fn.commandForElem=function(e,t,n,i,a){var o,r;"string"===typeof e?o=e:(o=e.selector,r=e.check);var c=this.getRangeElem();c=this.getSelfOrParentByName(c,o,r),c&&this.setRangeByElem(c),this.command(t,n,i,a)},e.fn.customCommand=function(e,t,n){var i=this,a=i.currentRange();function o(){i.hideDropPanelAndModal()}a?(i.undoRecord(),this.restoreSelection(a),t.call(i),this.saveSelection(),this.restoreSelection(),n&&"function"===typeof n&&n.call(i),i.txt.insertEmptyP(),i.txt.wrapImgAndText(),i.updateValue(),i.updateMenuStyle(),setTimeout(o,200),e&&e.preventDefault()):e&&e.preventDefault()},e.fn.queryCommandValue=function(e){var t="";try{t=document.queryCommandValue(e)}catch(n){}return t},e.fn.queryCommandState=function(e){var t=!1;try{t=document.queryCommandState(e)}catch(n){}return t},e.fn.queryCommandSupported=function(e){var t=!1;try{t=document.queryCommandSupported(e)}catch(n){}return t}})),t((function(e,t){var n;function i(e){var n=this,i=t(e),a=!1;return i.each((function(){if(this===n)return a=!0,!1})),a}e.fn.getLegalTags=function(t){var n=this.config.legalTags;if(n)return this.getSelfOrParentByName(t,n);e.error("配置项中缺少 legalTags 的配置")},e.fn.getSelfOrParentByName=function(e,a,o){if(e&&a){n||(n=e.webkitMatchesSelector||e.mozMatchesSelector||e.oMatchesSelector||e.matchesSelector),n||(n=i);var r=this.txt.$txt.get(0);while(e&&r!==e&&t.contains(r,e)){if(n.call(e,a)){if(!o)return e;if(o(e))return e}e=e.parentNode}}}})),t((function(e,t){var n=20;function i(e){return null==e._redoList&&(e._redoList=[]),e._redoList}function a(e){return null==e._undoList&&(e._undoList=[]),e._undoList}function o(e,t,n){var i=t.val,a=e.txt.$txt.html();if(null!=i){if(i===a)return"redo"===n?void e.redo():"undo"===n?void e.undo():void 0;e.txt.$txt.html(i),e.updateValue(),e.onchange&&"function"===typeof e.onchange&&e.onchange.call(e)}}e.fn.undoRecord=function(){var e=this,t=e.txt.$txt,o=t.html(),r=a(e),c=i(e),l=r.length?r[0]:"";o!==l.val&&(c.length&&(c=[]),r.unshift({range:e.currentRange(),val:o}),r.length>n&&r.pop())},e.fn.undo=function(){var e=this,t=a(e),n=i(e);if(t.length){var r=t.shift();n.unshift(r),o(this,r,"undo")}},e.fn.redo=function(){var e=this,t=a(e),n=i(e);if(n.length){var r=n.shift();t.unshift(r),o(this,r,"redo")}}})),t((function(e,t){e.fn.create=function(){var n=this;e.$body&&0!==e.$body.length||(e.$body=t("body"),e.$document=t(document),e.$window=t(window)),n.addMenus(),n.renderMenus(),n.renderMenuContainer(),n.renderTxt(),n.renderEditorContainer(),n.eventMenus(),n.eventMenuContainer(),n.eventTxt(),n.readyHeadler(),n.initSelection(),n.$txt=n.txt.$txt;var i=e._plugins;i&&i.length&&t.each(i,(function(e,t){t.call(n)}))},e.fn.disable=function(){this.txt.$txt.removeAttr("contenteditable"),this.disableMenusExcept(),this._disabled=!0},e.fn.enable=function(){this._disabled=!1,this.txt.$txt.attr("contenteditable","true"),this.enableMenusExcept()},e.fn.destroy=function(){var e=this,t=e.$valueContainer,n=e.$editorContainer,i=e.valueNodeName;"div"===i?(t.removeAttr("contenteditable"),n.after(t),n.hide()):(t.show(),n.hide())},e.fn.undestroy=function(){var e=this,t=e.$valueContainer,n=e.$editorContainer,i=e.menuContainer.$menuContainer,a=e.valueNodeName;"div"===a?(t.attr("contenteditable","true"),i.after(t),n.show()):(t.hide(),n.show())},e.fn.clear=function(){var e=this,t=e.txt.$txt;t.html("<p><br></p>"),e.restoreSelectionByElem(t.find("p").get(0))}})),t((function(e,t){var n=function(e){this.editor=e,this.init()};n.fn=n.prototype,e.MenuContainer=n})),t((function(e,t){var n=e.MenuContainer;n.fn.init=function(){var e=this,n=t('<div class="wangEditor-menu-container clearfix"></div>');e.$menuContainer=n,e.changeShadow()},n.fn.changeShadow=function(){var e=this.$menuContainer,t=this.editor,n=t.txt.$txt;n.on("scroll",(function(){n.scrollTop()>10?e.addClass("wangEditor-menu-shadow"):e.removeClass("wangEditor-menu-shadow")}))}})),t((function(e,t){var n=e.MenuContainer;n.fn.render=function(){var e=this.$menuContainer,t=this.editor.$editorContainer;t.append(e)},n.fn.height=function(){var e=this.$menuContainer;return e.height()},n.fn.appendMenu=function(e,t){return this._addGroup(e),this._addOneMenu(t)},n.fn._addGroup=function(e){var n,i=this.$menuContainer;this.$currentGroup&&this.currentGroupIdx===e||(n=t('<div class="menu-group clearfix"></div>'),i.append(n),this.$currentGroup=n,this.currentGroupIdx=e)},n.fn._addOneMenu=function(e){var n=e.$domNormal,i=e.$domSelected,a=this.$currentGroup,o=t('<div class="menu-item clearfix"></div>');return i.hide(),o.append(n).append(i),a.append(o),o}})),t((function(e,t){var n=function(e){this.editor=e.editor,this.id=e.id,this.title=e.title,this.$domNormal=e.$domNormal,this.$domSelected=e.$domSelected||e.$domNormal,this.commandName=e.commandName,this.commandValue=e.commandValue,this.commandNameSelected=e.commandNameSelected||e.commandName,this.commandValueSelected=e.commandValueSelected||e.commandValue};n.fn=n.prototype,e.Menu=n})),t((function(e,t){var n=e.Menu;n.fn.initUI=function(){var n=this.editor,i=n.UI.menus,a=this.id,o=i[a];this.$domNormal&&this.$domSelected||(null==o&&(e.warn('editor.UI配置中，没有菜单 "'+a+'" 的UI配置，只能取默认值'),o=i["default"]),this.$domNormal=t(o.normal),/^\./.test(o.selected)?this.$domSelected=this.$domNormal.clone().addClass(o.selected.slice(1)):this.$domSelected=t(o.selected))}})),t((function(e,t){var n=e.Menu;n.fn.render=function(e){this.initUI();var t=this.editor,n=t.menuContainer,i=n.appendMenu(e,this),a=this.onRender;this._renderTip(i),a&&"function"===typeof a&&a.call(this)},n.fn._renderTip=function(n){var i,a,o=this,r=o.editor,c=o.title,l=t('<div class="menu-tip"></div>');function s(){l.show()}function d(){l.hide()}o.tipWidth||(i=t('<p style="opacity:0; filter:Alpha(opacity=0); position:absolute;top:-10000px;">'+c+"</p>"),e.$body.append(i),r.ready((function(){var e=i.outerWidth()+5,t=l.outerWidth(),n=parseFloat(l.css("margin-left"),10);i.remove(),i=null,l.css({width:e,"margin-left":n+(t-e)/2}),o.tipWidth=e}))),l.append(c),n.append(l),n.find("a").on("mouseenter",(function(e){o.active()||o.disabled()||(a=setTimeout(s,200))})).on("mouseleave",(function(e){a&&clearTimeout(a),d()})).on("click",d)},n.fn.bindEvent=function(){var t=this,n=t.$domNormal,i=t.$domSelected,a=t.clickEvent;a||(a=function(n){var i=t.dropPanel||t.dropList||t.modal;if(i&&i.show)i.isShowing?i.hide():i.show();else{var a,o,r=t.editor,c=t.selected;c?(a=t.commandNameSelected,o=t.commandValueSelected):(a=t.commandName,o=t.commandValue),a?r.command(n,a,o):(e.warn('菜单 "'+t.id+'" 未定义click事件'),n.preventDefault())}});var o=t.clickEventSelected||a;n.click((function(e){t.disabled()||(a.call(t,e),t.updateSelected()),e.preventDefault()})),i.click((function(e){t.disabled()||(o.call(t,e),t.updateSelected()),e.preventDefault()}))},n.fn.updateSelected=function(){var e=this,t=(e.editor,e.updateSelectedEvent);t||(t=function(){var e=this,t=e.editor,n=e.commandName,i=e.commandValue;if(i){if(t.queryCommandValue(n).toLowerCase()===i.toLowerCase())return!0}else if(t.queryCommandState(n))return!0;return!1});var n=t.call(e);n=!!n,e.changeSelectedState(n)},n.fn.changeSelectedState=function(e){var t=this,n=t.selected;if(null!=e&&"boolean"===typeof e){if(n===e)return;t.selected=e,e?(t.$domNormal.hide(),t.$domSelected.show()):(t.$domNormal.show(),t.$domSelected.hide())}},n.fn.active=function(e){if(null==e)return this._activeState;this._activeState=e},n.fn.activeStyle=function(e){this.selected;var t=this.$domNormal,n=this.$domSelected;e?(t.addClass("active"),n.addClass("active")):(t.removeClass("active"),n.removeClass("active")),this.active(e)},n.fn.disabled=function(e){if(null==e)return!!this._disabled;if(this._disabled!==e){var t=this.$domNormal,n=this.$domSelected;e?(t.addClass("disable"),n.addClass("disable")):(t.removeClass("disable"),n.removeClass("disable")),this._disabled=e}}})),t((function(e,t){var n=function(e,t,n){this.editor=e,this.menu=t,this.data=n.data,this.tpl=n.tpl,this.selectorForELemCommand=n.selectorForELemCommand,this.beforeEvent=n.beforeEvent,this.afterEvent=n.afterEvent,this.init()};n.fn=n.prototype,e.DropList=n})),t((function(e,t){var n=e.DropList;n.fn.init=function(){var e=this;e.initDOM(),e.bindEvent(),e.initHideEvent()},n.fn.initDOM=function(){var e,n,i=this,a=i.data,o=i.tpl||"<span>{#title}</span>",r=t('<div class="wangEditor-drop-list clearfix"></div>');t.each(a,(function(i,a){e=o.replace(/{#commandValue}/gi,i).replace(/{#title}/gi,a),n=t('<a href="#" commandValue="'+i+'"></a>'),n.append(e),r.append(n)})),i.$list=r},n.fn.bindEvent=function(){var e=this,n=e.editor,i=e.menu,a=i.commandName,o=e.selectorForELemCommand,r=e.$list,c=e.beforeEvent,l=e.afterEvent;r.on("click","a[commandValue]",(function(e){c&&"function"===typeof c&&c.call(e);var r=t(e.currentTarget).attr("commandValue");i.selected&&n.isRangeEmpty()&&o?n.commandForElem(o,e,a,r):n.command(e,a,r),l&&"function"===typeof l&&l.call(e)}))},n.fn.initHideEvent=function(){var n=this,i=n.$list.get(0);e.$body.on("click",(function(e){if(n.isShowing){var a,o=e.target,r=n.menu;a=r.selected?r.$domSelected.get(0):r.$domNormal.get(0),a===o||t.contains(a,o)||i===o||t.contains(i,o)||n.hide()}})),e.$window.scroll((function(){n.hide()})),e.$window.on("resize",(function(){n.hide()}))}})),t((function(e,t){var n=e.DropList;n.fn._render=function(){var e=this,t=e.editor,n=e.$list;t.$editorContainer.append(n),e.rendered=!0},n.fn._position=function(){var e=this,t=e.$list,n=e.editor,i=e.menu,a=n.menuContainer.$menuContainer,o=i.selected?i.$domSelected:i.$domNormal,r=o.offsetParent().position(),c=r.top,l=r.left,s=o.offsetParent().height(),d=o.offsetParent().width(),u=t.outerWidth(),f=n.txt.$txt.outerWidth(),m=c+s,p=l+d/2,h=0-d/2,g=p+u-f;g>-10&&(h=h-g-10),t.css({top:m,left:p,"margin-left":h}),n._isMenufixed&&(m+=a.offset().top+a.outerHeight()-t.offset().top,t.css({top:m}))},n.fn.show=function(){var e=this,t=e.menu;if(e.rendered||e._render(),!e.isShowing){var n=e.$list;n.show(),e._position(),e.isShowing=!0,t.activeStyle(!0)}},n.fn.hide=function(){var e=this,t=e.menu;if(e.isShowing){var n=e.$list;n.hide(),e.isShowing=!1,t.activeStyle(!1)}}})),t((function(e,t){var n=function(e,t,n){this.editor=e,this.menu=t,this.$content=n.$content,this.width=n.width||200,this.height=n.height,this.onRender=n.onRender,this.init()};n.fn=n.prototype,e.DropPanel=n})),t((function(e,t){var n=e.DropPanel;n.fn.init=function(){var e=this;e.initDOM(),e.initHideEvent()},n.fn.initDOM=function(){var e=this,n=e.$content,i=e.width,a=e.height,o=t('<div class="wangEditor-drop-panel clearfix"></div>'),r=t('<div class="tip-triangle"></div>');o.css({width:i,height:a||"auto"}),o.append(r),o.append(n),e.$panel=o,e.$triangle=r},n.fn.initHideEvent=function(){var n=this,i=n.$panel.get(0);e.$body.on("click",(function(e){if(n.isShowing){var a,o=e.target,r=n.menu;a=r.selected?r.$domSelected.get(0):r.$domNormal.get(0),a===o||t.contains(a,o)||i===o||t.contains(i,o)||n.hide()}})),e.$window.scroll((function(e){n.hide()})),e.$window.on("resize",(function(){n.hide()}))}})),t((function(e,t){var n=e.DropPanel;n.fn._render=function(){var e=this,t=e.onRender,n=e.editor,i=e.$panel;n.$editorContainer.append(i),t&&t.call(e),e.rendered=!0},n.fn._position=function(){var e=this,t=e.$panel,n=e.$triangle,i=e.editor,a=i.menuContainer.$menuContainer,o=e.menu,r=o.selected?o.$domSelected:o.$domNormal,c=r.offsetParent().position(),l=c.top,s=c.left,d=r.offsetParent().height(),u=r.offsetParent().width(),f=t.outerWidth(),m=i.txt.$txt.outerWidth(),p=l+d,h=s+u/2,g=0-f/2,v=g;0-g>h-10&&(g=0-(h-10));var w=h+f+g-m;w>-10&&(g=g-w-10),t.css({top:p,left:h,"margin-left":g}),i._isMenufixed&&(p+=a.offset().top+a.outerHeight()-t.offset().top,t.css({top:p})),n.css({"margin-left":v-g-5})},n.fn.focusFirstInput=function(){var e=this,n=e.$panel;n.find("input[type=text],textarea").each((function(){var e=t(this);if(null==e.attr("disabled"))return e.focus(),!1}))},n.fn.show=function(){var t=this,n=t.menu;if(t.rendered||t._render(),!t.isShowing){var i=t.$panel;i.show(),t._position(),t.isShowing=!0,n.activeStyle(!0),e.w3cRange?t.focusFirstInput():e.placeholderForIE8(i)}},n.fn.hide=function(){var e=this,t=e.menu;if(e.isShowing){var n=e.$panel;n.hide(),e.isShowing=!1,t.activeStyle(!1)}}})),t((function(e,t){var n=function(e,t,n){this.editor=e,this.menu=t,this.$content=n.$content,this.init()};n.fn=n.prototype,e.Modal=n})),t((function(e,t){var n=e.Modal;n.fn.init=function(){var e=this;e.initDom(),e.initHideEvent()},n.fn.initDom=function(){var e=this,n=e.$content,i=t('<div class="wangEditor-modal"></div>'),a=t('<div class="wangEditor-modal-close"><i class="wangeditor-menu-img-cancel-circle"></i></div>');i.append(a),i.append(n),e.$modal=i,e.$close=a},n.fn.initHideEvent=function(){var n=this,i=n.$close,a=n.$modal.get(0);i.click((function(){n.hide()})),e.$body.on("click",(function(e){if(n.isShowing){var i,o=e.target,r=n.menu;r&&(i=r.selected?r.$domSelected.get(0):r.$domNormal.get(0),i===o||t.contains(i,o))||a===o||t.contains(a,o)||n.hide()}}))}})),t((function(e,t){var n=e.Modal;n.fn._render=function(){var t=this,n=t.editor,i=t.$modal;i.css("z-index",n.config.zindex+10+""),e.$body.append(i),t.rendered=!0},n.fn._position=function(){var t=this,n=t.$modal,i=n.offset().top,a=n.outerWidth(),o=n.outerHeight(),r=0-a/2,c=0-o/2,l=e.$window.scrollTop();o/2>i&&(c=0-i),n.css({"margin-left":r+"px","margin-top":c+l+"px"})},n.fn.show=function(){var e=this,t=e.menu;if(e.rendered||e._render(),!e.isShowing){e.isShowing=!0;var n=e.$modal;n.show(),e._position(),t&&t.activeStyle(!0)}},n.fn.hide=function(){var e=this,t=e.menu;if(e.isShowing){e.isShowing=!1;var n=e.$modal;n.hide(),t&&t.activeStyle(!1)}}})),t((function(e,t){var n=function(e){this.editor=e,this.init()};n.fn=n.prototype,e.Txt=n})),t((function(e,t){var n=e.Txt;n.fn.init=function(){var e,n=this,i=n.editor,a=i.$valueContainer,o=i.getInitValue();"DIV"===a.get(0).nodeName?(e=a,e.addClass("wangEditor-txt"),e.attr("contentEditable","true")):e=t('<div class="wangEditor-txt" contentEditable="true">'+o+"</div>"),i.ready((function(){n.insertEmptyP()})),n.$txt=e,n.contentEmptyHandle(),n.bindEnterForDiv(),n.bindEnterForText(),n.bindTabEvent(),n.bindPasteFilter(),n.bindFormatText(),n.bindHtml()},n.fn.contentEmptyHandle=function(){var e,n=this,i=n.editor,a=n.$txt;a.on("keydown",(function(e){if(8===e.keyCode){var n=t.trim(a.html().toLowerCase());"<p><br></p>"!==n||e.preventDefault()}})),a.on("keyup",(function(n){if(8===n.keyCode){var o=t.trim(a.html().toLowerCase());o&&"<br>"!==o||(e=t("<p><br/></p>"),a.html(""),a.append(e),i.restoreSelectionByElem(e.get(0)))}}))},n.fn.bindEnterForDiv=function(){e.config.legalTags;var n,i=this,a=i.editor,o=i.$txt;function r(){if(n){var e=t("<p>"+n.html()+"</p>");n.after(e),n.remove()}}o.on("keydown keyup",(function(e){if(13===e.keyCode){var i,o,c=a.getRangeElem(),l=a.getLegalTags(c);if(!l){if(l=a.getSelfOrParentByName(c,"div"),!l)return;i=t(l),"keydown"===e.type&&(n=i,setTimeout(r,0)),"keyup"===e.type&&(o=t("<p>"+i.html()+"</p>"),i.after(o),i.remove(),a.restoreSelectionByElem(o.get(0),"start"))}}}))},n.fn.bindEnterForText=function(){var e,t=this,n=t.$txt;n.on("keyup",(function(n){13===n.keyCode&&(e||(e=function(){t.wrapImgAndText()}),setTimeout(e))}))},n.fn.bindTabEvent=function(){var e=this,t=e.editor,n=e.$txt;n.on("keydown",(function(e){9===e.keyCode&&t.queryCommandSupported("insertHtml")&&t.command(e,"insertHtml","&nbsp;&nbsp;&nbsp;&nbsp;")}))},n.fn.bindPasteFilter=function(){var e=this,n=e.editor,i="",a=e.$txt,o=n.config.legalTags,r=o.split(",");function c(e){if(e&&e.nodeType&&e.nodeName){var n,a,o=e.nodeName.toLowerCase(),d=e.nodeType;if(3===d||1===d){if(n=t(e),"div"===o)return a=[],t.each(e.childNodes,(function(e,t){a.push(t)})),void t.each(a,(function(){c(this)}));if(r.indexOf(o)>=0)i+=l(e);else if(3===d)i+="<p>"+e.textContent+"</p>";else if("br"===o)i+="<br/>";else{if(["meta","style","script","object","form","iframe","hr"].indexOf(o)>=0)return;n=t(s(e)),i+=t("<div>").append(n.clone()).html()}}}}function l(e){var n,i=e.nodeName.toLowerCase(),a="",o="";return["blockquote"].indexOf(i)>=0?(n=t(e),"<"+i+">"+n.text()+"</"+i+">"):["p","h1","h2","h3","h4","h5"].indexOf(i)>=0?(e=s(e),n=t(e),a=n.html(),a=a.replace(/<.*?>/gi,(function(e){return"</a>"===e||0===e.indexOf("<a ")||0===e.indexOf("<img ")?e:""})),"<"+i+">"+a+"</"+i+">"):["ul","ol"].indexOf(i)>=0?(n=t(e),n.children().each((function(){var e=t(s(this)),n=e.html();n=n.replace(/<.*?>/gi,(function(e){return"</a>"===e||0===e.indexOf("<a ")||0===e.indexOf("<img ")?e:""})),o+="<li>"+n+"</li>"})),"<"+i+">"+o+"</"+i+">"):(n=t(s(e)),t("<div>").append(n).html())}function s(e){var n=e.attributes||[],i=[],a=["href","target","src","alt","rowspan","colspan"];t.each(n,(function(e,t){t&&2===t.nodeType&&i.push(t.nodeName)})),t.each(i,(function(t,n){a.indexOf(n)<0&&e.removeAttribute(n)}));var o=e.childNodes;return o.length&&t.each(o,(function(e,t){s(t)})),e}a.on("paste",(function(a){if(n.config.pasteFilter){var o=n.getRangeElem().nodeName;if("TD"!==o&&"TH"!==o){var r,l,s;i="";var d=a.clipboardData||a.originalEvent.clipboardData,u=window.clipboardData;if(n.config.pasteText){if(d&&d.getData)r=d.getData("text/plain");else{if(!u||!u.getData)return;r=u.getData("text")}r&&(i="<p>"+r+"</p>")}else if(d&&d.getData)r=d.getData("text/html"),s=r.split("</html>"),2===s.length&&(r=s[0]),r?(l=t("<div>"+r+"</div>"),c(l.get(0))):(r=d.getData("text/plain"),r&&(r=r.replace(/[ ]/g,"&nbsp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/\n/g,"</p><p>"),i="<p>"+r+"</p>",i=i.replace(/<p>(https?:\/\/.*?)<\/p>/gi,(function(e,t){return'<p><a href="'+t+'" target="_blank">'+t+"</p>"}))));else{if(!u||!u.getData)return;if(i=u.getData("text"),!i)return;i="<p>"+i+"</p>",i=i.replace(new RegExp("\n","g"),"</p><p>")}i&&(n.command(a,"insertHtml",i),e.clearEmptyOrNestP())}}}))},n.fn.bindFormatText=function(){var n=this,i=(n.editor,n.$txt),a=e.config.legalTags,o=a.split(","),r=(o.length,[]);t.each(o,(function(e,t){var n=">\\s*<("+t+")>";r.push(new RegExp(n,"ig"))})),r.push(new RegExp(">\\s*<(li)>","ig")),r.push(new RegExp(">\\s*<(tr)>","ig")),r.push(new RegExp(">\\s*<(code)>","ig")),i.formatText=function(){var e=t("<div>"),n=i.html();return n=n.replace(/\s*</gi,"<"),t.each(r,(function(e,t){t.test(n)&&(n=n.replace(t,(function(e,t){return">\n<"+t+">"})))})),e.html(n),e.text()}},n.fn.bindHtml=function(){var e=this,n=e.editor,i=e.$txt,a=n.$valueContainer,o=n.valueNodeName;i.html=function(e){var n;if("div"===o&&(n=t.fn.html.call(i,e)),void 0===e?(n=t.fn.html.call(i),n=n.replace(/(href|src)\=\"(.*)\"/gim,(function(e,t,n){return t+'="'+n.replace("&amp;","&")+'"'}))):(n=t.fn.html.call(i,e),a.val(e)),void 0===e)return n;i.change()}}})),t((function(e,t){var n=e.Txt,i="propertychange change click keyup input paste";n.fn.render=function(){var e=this.$txt,t=this.editor.$editorContainer;t.append(e)},n.fn.initHeight=function(){var e=this.editor,t=this.$txt,n=e.$valueContainer.height(),i=e.menuContainer.height(),a=n-i;a=a<50?50:a,t.height(a),e.valueContainerHeight=n,this.initMaxHeight(a,i)},n.fn.initMaxHeight=function(n,i){var a=this.editor,o=a.menuContainer.$menuContainer,r=this.$txt,c=t("<div>");if(window.getComputedStyle&&"max-height"in window.getComputedStyle(r.get(0))){var l=parseInt(a.$valueContainer.css("max-height"));if(isNaN(l))return;if(a.menus.fullscreen)return void e.warn("max-height和『全屏』菜单一起使用时，会有一些问题尚未解决，请暂时不要两个同时使用");a.useMaxHeight=!0,c.css({"max-height":l-i+"px","overflow-y":"auto"}),r.css({height:"auto","overflow-y":"visible","min-height":n+"px"}),c.on("scroll",(function(){r.parent().scrollTop()>10?o.addClass("wangEditor-menu-shadow"):o.removeClass("wangEditor-menu-shadow")})),r.wrap(c)}},n.fn.saveSelectionEvent=function(){var e,t=this.$txt,n=this.editor,a=Date.now();function o(){n.saveSelection()}function r(){Date.now()-a<100||(a=Date.now(),o())}function c(){e&&clearTimeout(e),e=setTimeout(o,300)}t.on(i+" focus blur",(function(e){r(),c()})),t.on("mousedown",(function(){t.on("mouseleave.saveSelection",(function(e){r(),c(),n.updateMenuStyle()}))})).on("mouseup",(function(){t.off("mouseleave.saveSelection")}))},n.fn.updateValueEvent=function(){var e,t,n=this.$txt,a=this.editor;function o(){var e=n.html();t!==e&&(a.onchange&&"function"===typeof a.onchange&&a.onchange.call(a),a.updateValue(),t=e)}n.on(i,(function(i){null==t&&(t=n.html()),e&&clearTimeout(e),e=setTimeout(o,100)}))},n.fn.updateMenuStyleEvent=function(){var e=this.$txt,t=this.editor;e.on(i,(function(e){t.updateMenuStyle()}))},n.fn.insertEmptyP=function(){var e=this.$txt,n=e.children();0!==n.length?"<br>"!==t.trim(n.last().html()).toLowerCase()&&e.append(t("<p><br></p>")):e.append(t("<p><br></p>"))},n.fn.wrapImgAndText=function(){var e,n,i=this.$txt,a=i.children("img"),o=i[0],r=o.childNodes,c=r.length;for(a.length&&a.each((function(){t(this).wrap("<p>")})),e=0;e<c;e++)n=r[e],3===n.nodeType&&n.textContent&&t.trim(n.textContent)&&t(n).wrap("<p>")},n.fn.clearEmptyOrNestP=function(){var e=this.$txt,n=e.find("p");n.each((function(){var e,n=t(this),i=n.children(),a=i.length,o=t.trim(n.html());o?1===a&&(e=i.first(),e.get(0)&&"P"===e.get(0).nodeName&&n.html(e.html())):n.remove()}))},n.fn.scrollTop=function(e){var t=this,n=t.editor,i=t.$txt;return n.useMaxHeight?i.parent().scrollTop(e):i.scrollTop(e)},n.fn.showHeightOnHover=function(){var e=this.editor,n=e.$editorContainer,i=e.menuContainer,a=this.$txt,o=t('<i class="height-tip"><i>'),r=!1;function c(e){r||(n.append(o),r=!0);a.position().top,a.outerHeight();var t=e.height(),c=e.position().top,l=parseInt(e.css("margin-top"),10),s=parseInt(e.css("padding-top"),10),d=parseInt(e.css("margin-bottom"),10),u=parseInt(e.css("padding-bottom"),10);i.height();o.css({height:t+s+l+u+d,top:c+i.height()})}function l(){r&&(o.remove(),r=!1)}a.on("mouseenter","ul,ol,blockquote,p,h1,h2,h3,h4,h5,table,pre",(function(e){c(t(e.currentTarget))})).on("mouseleave",(function(){l()}))}})),t((function(e,t){Array.prototype.indexOf||(Array.prototype.indexOf=function(e){for(var t=0,n=this.length;t<n;t++)if(this[t]===e)return t;return-1},Array.prototype.lastIndexOf=function(e){var t=this.length;for(t-=1;t>=0;t--)if(this[t]===e)return t;return-1}),Date.now||(Date.now=function(){return(new Date).valueOf()});var n=window.console,i=function(){};t.each(["info","log","warn","error"],(function(t,a){e[a]=null==n?i:function(t){e.config&&e.config.printLog&&n[a]("wangEditor提示: "+t)}})),e.random=function(){return Math.random().toString().slice(2)},e.placeholder="placeholder"in document.createElement("input"),e.placeholderForIE8=function(n){e.placeholder||n.find("input[placeholder]").each((function(){var e=t(this),n=e.attr("placeholder");""===e.val()&&(e.css("color","#666"),e.val(n),e.on("focus.placeholder click.placeholder",(function(){e.val(""),e.css("color","#333"),e.off("focus.placeholder click.placeholder")})))}))}})),t((function(e,t){e.langs={},e.langs["zh-cn"]={bold:"粗体",underline:"下划线",italic:"斜体",forecolor:"文字颜色",bgcolor:"背景色",strikethrough:"删除线",eraser:"清空格式",source:"源码",quote:"引用",fontfamily:"字体",fontsize:"字号",head:"标题",orderlist:"有序列表",unorderlist:"无序列表",alignleft:"左对齐",aligncenter:"居中",alignright:"右对齐",link:"链接",text:"文本",submit:"提交",cancel:"取消",unlink:"取消链接",table:"表格",emotion:"表情",img:"图片",uploadImg:"上传图片",linkImg:"网络图片",video:"视频",width:"宽",height:"高",location:"位置",loading:"加载中",searchlocation:"搜索位置",dynamicMap:"动态地图",clearLocation:"清除位置",langDynamicOneLocation:"动态地图只能显示一个位置",insertcode:"插入代码",undo:"撤销",redo:"重复",fullscreen:"全屏",openLink:"打开链接"},e.langs.en={bold:"Bold",underline:"Underline",italic:"Italic",forecolor:"Color",bgcolor:"Backcolor",strikethrough:"Strikethrough",eraser:"Eraser",source:"Codeview",quote:"Quote",fontfamily:"Font family",fontsize:"Font size",head:"Head",orderlist:"Ordered list",unorderlist:"Unordered list",alignleft:"Align left",aligncenter:"Align center",alignright:"Align right",link:"Insert link",text:"Text",submit:"Submit",cancel:"Cancel",unlink:"Unlink",table:"Table",emotion:"Emotions",img:"Image",uploadImg:"Upload",linkImg:"Link",video:"Video",width:"width",height:"height",location:"Location",loading:"Loading",searchlocation:"search",dynamicMap:"Dynamic",clearLocation:"Clear",langDynamicOneLocation:"Only one location in dynamic map",insertcode:"Insert Code",undo:"Undo",redo:"Redo",fullscreen:"Full screnn",openLink:"open link"}})),t((function(e,t){e.config={},e.config.zindex=1e4,e.config.printLog=!0,e.config.menuFixed=0,e.config.jsFilter=!0,e.config.legalTags="p,h1,h2,h3,h4,h5,h6,blockquote,table,ul,ol,pre",e.config.lang=e.langs["zh-cn"],e.config.menus=["source","|","bold","underline","italic","strikethrough","eraser","forecolor","bgcolor","|","quote","fontfamily","fontsize","head","unorderlist","orderlist","alignleft","aligncenter","alignright","|","link","unlink","table","emotion","|","img","video","location","insertcode","|","undo","redo","fullscreen"],e.config.colors={"#880000":"暗红色","#800080":"紫色","#ff0000":"红色","#ff00ff":"鲜粉色","#000080":"深蓝色","#0000ff":"蓝色","#00ffff":"湖蓝色","#008080":"蓝绿色","#008000":"绿色","#808000":"橄榄色","#00ff00":"浅绿色","#ffcc00":"橙黄色","#808080":"灰色","#c0c0c0":"银色","#000000":"黑色","#ffffff":"白色"},e.config.familys=["宋体","黑体","楷体","微软雅黑","Arial","Verdana","Georgia","Times New Roman","Microsoft JhengHei","Trebuchet MS","Courier New","Impact","Comic Sans MS","Consolas"],e.config.fontsizes={1:"12px",2:"13px",3:"16px",4:"18px",5:"24px",6:"32px",7:"48px"},e.config.emotionsShow="icon",e.config.emotions={weibo:{title:"微博表情",data:[{icon:"http://img.t.sinajs.cn/t35/style/images/common/face/ext/normal/7a/shenshou_thumb.gif",value:"[草泥马]"},{icon:"http://img.t.sinajs.cn/t35/style/images/common/face/ext/normal/60/horse2_thumb.gif",value:"[神马]"},{icon:"http://img.t.sinajs.cn/t35/style/images/common/face/ext/normal/bc/fuyun_thumb.gif",value:"[浮云]"},{icon:"http://img.t.sinajs.cn/t35/style/images/common/face/ext/normal/c9/geili_thumb.gif",value:"[给力]"},{icon:"http://img.t.sinajs.cn/t35/style/images/common/face/ext/normal/f2/wg_thumb.gif",value:"[围观]"},{icon:"http://img.t.sinajs.cn/t35/style/images/common/face/ext/normal/70/vw_thumb.gif",value:"[威武]"},{icon:"http://img.t.sinajs.cn/t35/style/images/common/face/ext/normal/6e/panda_thumb.gif",value:"[熊猫]"},{icon:"http://img.t.sinajs.cn/t35/style/images/common/face/ext/normal/81/rabbit_thumb.gif",value:"[兔子]"},{icon:"http://img.t.sinajs.cn/t35/style/images/common/face/ext/normal/bc/otm_thumb.gif",value:"[奥特曼]"},{icon:"http://img.t.sinajs.cn/t35/style/images/common/face/ext/normal/15/j_thumb.gif",value:"[囧]"},{icon:"http://img.t.sinajs.cn/t35/style/images/common/face/ext/normal/89/hufen_thumb.gif",value:"[互粉]"},{icon:"http://img.t.sinajs.cn/t35/style/images/common/face/ext/normal/c4/liwu_thumb.gif",value:"[礼物]"},{icon:"http://img.t.sinajs.cn/t35/style/images/common/face/ext/normal/ac/smilea_thumb.gif",value:"[呵呵]"},{icon:"http://img.t.sinajs.cn/t35/style/images/common/face/ext/normal/0b/tootha_thumb.gif",value:"[哈哈]"}]}},e.config.mapAk="TVhjYjq1ICT2qqL5LdS8mwas",e.config.uploadImgUrl="",e.config.uploadTimeout=2e4,e.config.uploadImgFns={},e.config.customUpload=!1,e.config.uploadParams={},e.config.uploadHeaders={},e.config.withCredentials=!0,e.config.hideLinkImg=!1,e.config.pasteFilter=!0,e.config.pasteText=!1,e.config.codeDefaultLang="javascript"})),t((function(e,t){e.UI={},e.UI.menus={default:{normal:'<a href="#" tabindex="-1"><i class="wangeditor-menu-img-command"></i></a>',selected:".selected"},bold:{normal:'<a href="#" tabindex="-1"><i class="wangeditor-menu-img-bold"></i></a>',selected:".selected"},underline:{normal:'<a href="#" tabindex="-1"><i class="wangeditor-menu-img-underline"></i></a>',selected:".selected"},italic:{normal:'<a href="#" tabindex="-1"><i class="wangeditor-menu-img-italic"></i></a>',selected:".selected"},forecolor:{normal:'<a href="#" tabindex="-1"><i class="wangeditor-menu-img-pencil"></i></a>',selected:".selected"},bgcolor:{normal:'<a href="#" tabindex="-1"><i class="wangeditor-menu-img-brush"></i></a>',selected:".selected"},strikethrough:{normal:'<a href="#" tabindex="-1"><i class="wangeditor-menu-img-strikethrough"></i></a>',selected:".selected"},eraser:{normal:'<a href="#" tabindex="-1"><i class="wangeditor-menu-img-eraser"></i></a>',selected:".selected"},quote:{normal:'<a href="#" tabindex="-1"><i class="wangeditor-menu-img-quotes-left"></i></a>',selected:".selected"},source:{normal:'<a href="#" tabindex="-1"><i class="wangeditor-menu-img-code"></i></a>',selected:".selected"},fontfamily:{normal:'<a href="#" tabindex="-1"><i class="wangeditor-menu-img-font2"></i></a>',selected:".selected"},fontsize:{normal:'<a href="#" tabindex="-1"><i class="wangeditor-menu-img-text-height"></i></a>',selected:".selected"},head:{normal:'<a href="#" tabindex="-1"><i class="wangeditor-menu-img-header"></i></a>',selected:".selected"},orderlist:{normal:'<a href="#" tabindex="-1"><i class="wangeditor-menu-img-list-numbered"></i></a>',selected:".selected"},unorderlist:{normal:'<a href="#" tabindex="-1"><i class="wangeditor-menu-img-list-bullet"></i></a>',selected:".selected"},alignleft:{normal:'<a href="#" tabindex="-1"><i class="wangeditor-menu-img-align-left"></i></a>',selected:".selected"},aligncenter:{normal:'<a href="#" tabindex="-1"><i class="wangeditor-menu-img-align-center"></i></a>',selected:".selected"},alignright:{normal:'<a href="#" tabindex="-1"><i class="wangeditor-menu-img-align-right"></i></a>',selected:".selected"},link:{normal:'<a href="#" tabindex="-1"><i class="wangeditor-menu-img-link"></i></a>',selected:".selected"},unlink:{normal:'<a href="#" tabindex="-1"><i class="wangeditor-menu-img-unlink"></i></a>',selected:".selected"},table:{normal:'<a href="#" tabindex="-1"><i class="wangeditor-menu-img-table"></i></a>',selected:".selected"},emotion:{normal:'<a href="#" tabindex="-1"><i class="wangeditor-menu-img-happy"></i></a>',selected:".selected"},img:{normal:'<a href="#" tabindex="-1"><i class="wangeditor-menu-img-picture"></i></a>',selected:".selected"},video:{normal:'<a href="#" tabindex="-1"><i class="wangeditor-menu-img-play"></i></a>',selected:".selected"},location:{normal:'<a href="#" tabindex="-1"><i class="wangeditor-menu-img-location"></i></a>',selected:".selected"},insertcode:{normal:'<a href="#" tabindex="-1"><i class="wangeditor-menu-img-terminal"></i></a>',selected:".selected"},undo:{normal:'<a href="#" tabindex="-1"><i class="wangeditor-menu-img-ccw"></i></a>',selected:".selected"},redo:{normal:'<a href="#" tabindex="-1"><i class="wangeditor-menu-img-cw"></i></a>',selected:".selected"},fullscreen:{normal:'<a href="#" tabindex="-1"><i class="wangeditor-menu-img-enlarge2"></i></a>',selected:'<a href="#" tabindex="-1" class="selected"><i class="wangeditor-menu-img-shrink2"></i></a>'}}})),t((function(e,t){e.fn.initDefaultConfig=function(){var n=this;n.config=t.extend({},e.config),n.UI=t.extend({},e.UI)}})),t((function(e,t){e.fn.addEditorContainer=function(){this.$editorContainer=t('<div class="wangEditor-container"></div>')}})),t((function(e,t){e.fn.addTxt=function(){var t=this,n=new e.Txt(t);t.txt=n}})),t((function(e,t){e.fn.addMenuContainer=function(){var t=this;t.menuContainer=new e.MenuContainer(t)}})),t((function(e,t){e.createMenuFns=[],e.createMenu=function(t){e.createMenuFns.push(t)},e.fn.addMenus=function(){var n=this,i=n.config.menus;function a(e){return i.indexOf(e)>=0}t.each(e.createMenuFns,(function(e,t){t.call(n,a)}))}})),t((function(e,t){e.createMenu((function(t){var n="bold";if(t(n)){var i=this,a=i.config.lang,o=new e.Menu({editor:i,id:n,title:a.bold,commandName:"Bold"});o.clickEventSelected=function(e){var t=i.isRangeEmpty();t?i.commandForElem("b,strong,h1,h2,h3,h4,h5",e,"Bold"):i.command(e,"Bold")},i.menus[n]=o}}))})),t((function(e,t){e.createMenu((function(t){var n="underline";if(t(n)){var i=this,a=i.config.lang,o=new e.Menu({editor:i,id:n,title:a.underline,commandName:"Underline"});o.clickEventSelected=function(e){var t=i.isRangeEmpty();t?i.commandForElem("u,a",e,"Underline"):i.command(e,"Underline")},i.menus[n]=o}}))})),t((function(e,t){e.createMenu((function(t){var n="italic";if(t(n)){var i=this,a=i.config.lang,o=new e.Menu({editor:i,id:n,title:a.italic,commandName:"Italic"});o.clickEventSelected=function(e){var t=i.isRangeEmpty();t?i.commandForElem("i",e,"Italic"):i.command(e,"Italic")},i.menus[n]=o}}))})),t((function(e,t){e.createMenu((function(n){var i="forecolor";if(n(i)){var a=this,o=a.config.lang,r=a.config.colors,c=new e.Menu({editor:a,id:i,title:o.forecolor}),l=t("<div></div>");t.each(r,(function(e,t){l.append(['<a href="#" class="color-item"','    title="'+t+'" commandValue="'+e+'" ','    style="color: '+e+'" ','><i class="wangeditor-menu-img-pencil"></i></a>'].join(""))})),l.on("click","a[commandValue]",(function(e){var n=t(this),i=n.attr("commandValue");c.selected&&a.isRangeEmpty()?a.commandForElem("font[color]",e,"forecolor",i):a.command(e,"forecolor",i)})),c.dropPanel=new e.DropPanel(a,c,{$content:l,width:125}),c.updateSelectedEvent=function(){var e=a.getRangeElem();return e=a.getSelfOrParentByName(e,"font[color]"),!!e},a.menus[i]=c}}))})),t((function(e,t){e.createMenu((function(n){var i="bgcolor";if(n(i)){var a=this,o=a.config.lang,r=a.config.colors,c=new e.Menu({editor:a,id:i,title:o.bgcolor}),l=t("<div></div>");t.each(r,(function(e,t){l.append(['<a href="#" class="color-item"','    title="'+t+'" commandValue="'+e+'" ','    style="color: '+e+'" ','><i class="wangeditor-menu-img-brush"></i></a>'].join(""))})),l.on("click","a[commandValue]",(function(e){var n=t(this),i=n.attr("commandValue");c.selected&&a.isRangeEmpty()?a.commandForElem({selector:"span,font",check:s},e,"BackColor",i):a.command(e,"BackColor",i)})),c.dropPanel=new e.DropPanel(a,c,{$content:l,width:125}),c.updateSelectedEvent=function(){var e=a.getRangeElem();return e=a.getSelfOrParentByName(e,"span,font",s),!!e},a.menus[i]=c}function s(e){var t;return!!(e&&e.style&&null!=e.style.cssText&&(t=e.style.cssText,t&&t.indexOf("background-color:")>=0))}}))})),t((function(e,t){e.createMenu((function(t){var n="strikethrough";if(t(n)){var i=this,a=i.config.lang,o=new e.Menu({editor:i,id:n,title:a.strikethrough,commandName:"StrikeThrough"});o.clickEventSelected=function(e){var t=i.isRangeEmpty();t?i.commandForElem("strike",e,"StrikeThrough"):i.command(e,"StrikeThrough")},i.menus[n]=o}}))})),t((function(e,t){e.createMenu((function(n){var i="eraser";if(n(i)){var a=this,o=a.config.lang,r=new e.Menu({editor:a,id:i,title:o.eraser,commandName:"RemoveFormat"});r.clickEvent=function(e){var n,i=a.isRangeEmpty();i?a.customCommand(e,o,r):a.command(e,"RemoveFormat");function o(){var e,i,a,o,r,c,l,s=this;e=s.getRangeElem(),o=s.getSelfOrParentByName(e,"blockquote"),o&&(r=t(o),n=t("<p>"+r.text()+"</p>"),r.after(n).remove()),i=s.getSelfOrParentByName(e,"p,h1,h2,h3,h4,h5"),i&&(a=t(i),n=t("<p>"+a.text()+"</p>"),a.after(n).remove()),c=s.getSelfOrParentByName(e,"ul,ol"),c&&(l=t(c),n=t("<p>"+l.text()+"</p>"),l.after(n).remove())}function r(){var e=this;n&&e.restoreSelectionByElem(n.get(0))}},a.menus[i]=r}}))})),t((function(e,t){e.createMenu((function(n){var i="source";if(n(i)){var a,o=this,r=o.config.lang,c=new e.Menu({editor:o,id:i,title:r.source});c.isShowCode=!1,c.clickEvent=function(e){var n=this,i=n.editor,o=i.txt.$txt,r=o.outerHeight(),s=o.height();n.$codeTextarea||(n.$codeTextarea=t('<textarea class="code-textarea"></textarea>'));var d=n.$codeTextarea;d.css({height:s,"margin-top":r-s}),d.val(o.html()),d.on("change",(function(e){l()})),o.after(d).hide(),d.show(),c.isShowCode=!0,this.updateSelected(),i.disableMenusExcept("source"),a=o.html()},c.clickEventSelected=function(e){var t=this,n=t.editor,i=n.txt.$txt,o=t.$codeTextarea;o&&(l(),o.after(i).hide(),i.show(),c.isShowCode=!1,this.updateSelected(),n.enableMenusExcept("source"),i.html()!==a&&n.onchange&&"function"===typeof n.onchange&&n.onchange.call(n))},c.updateSelectedEvent=function(){return this.isShowCode},o.menus[i]=c}function l(){var e=c.$codeTextarea,n=o.txt.$txt,i=t.trim(e.val());i||(i="<p><br></p>"),o.config.jsFilter&&(i=i.replace(/<script[\s\S]*?<\/script>/gi,""));try{n.html(i)}catch(a){}}}))})),t((function(e,t){e.createMenu((function(n){var i="quote";if(n(i)){var a=this,o=a.config.lang,r=new e.Menu({editor:a,id:i,title:o.quote,commandName:"formatBlock",commandValue:"blockquote"});r.clickEvent=function(e){var n,i=a.getRangeElem();if(i){var o,r=a.getSelfOrParentByName(i,"blockquote");r?e.preventDefault():(i=a.getLegalTags(i),n=t(i),n.text()&&(i?a.customCommand(e,c,l):a.command(e,"formatBlock","blockquote")))}else e.preventDefault();function c(){o=t("<p>"+n.text()+"</p>"),n.after(o).remove(),o.wrap("<blockquote>")}function l(){var e=this;o&&e.restoreSelectionByElem(o.get(0))}},r.clickEventSelected=function(e){var n,i,o;function r(){var e,n;if(e=t(i),n=e.children(),n.length)return n.each((function(n){var i=t(this);"P"===i.get(0).nodeName?e.after(i):e.after("<p>"+i.text()+"</p>"),o=i})),void e.remove()}function c(){var e=this;o&&e.restoreSelectionByElem(o.get(0))}n=a.getRangeElem(),i=a.getSelfOrParentByName(n,"blockquote"),i?a.customCommand(e,r,c):e.preventDefault()},r.updateSelectedEvent=function(){var e,t=this,n=t.editor;return e=n.getRangeElem(),e=n.getSelfOrParentByName(e,"blockquote"),!!e},a.menus[i]=r,a.ready((function(){var e=this,n=e.txt.$txt,i=!1;n.on("keydown",(function(n){if(13===n.keyCode){var a=e.getRangeElem();if(a=e.getSelfOrParentByName(a,"blockquote"),a)if(i){var o=e.getRangeElem(),r=t(o);r.length&&r.parent().after(r),e.restoreSelectionByElem(o,"start"),i=!1,n.preventDefault()}else i=!0;else i=!1}else i=!1}))})),a.ready((function(){var e,n=this,i=n.txt.$txt;function a(){e&&e.remove()}function o(){if(e){var t=e.prev();t.length?n.restoreSelectionByElem(t.get(0)):n.initSelection()}}i.on("keydown",(function(i){if(8===i.keyCode){var r=n.getRangeElem();if(r=n.getSelfOrParentByName(r,"blockquote"),r){e=t(r);var c=e.text();c||n.customCommand(i,a,o)}}}))}))}}))})),t((function(e,t){e.createMenu((function(n){var i="fontfamily";if(n(i)){var a=this,o=a.config.lang,r=a.config.familys,c=new e.Menu({editor:a,id:i,title:o.fontfamily,commandName:"fontName"}),l={};t.each(r,(function(e,t){l[t]=t}));var s='<span style="font-family:{#commandValue};">{#title}</span>';c.dropList=new e.DropList(a,c,{data:l,tpl:s,selectorForELemCommand:"font[face]"}),c.updateSelectedEvent=function(){var e=a.getRangeElem();return e=a.getSelfOrParentByName(e,"font[face]"),!!e},a.menus[i]=c}}))})),t((function(e,t){e.createMenu((function(t){var n="fontsize";if(t(n)){var i=this,a=i.config.lang,o=i.config.fontsizes,r=new e.Menu({editor:i,id:n,title:a.fontsize,commandName:"fontSize"}),c=o,l='<span style="font-size:{#title};">{#title}</span>';r.dropList=new e.DropList(i,r,{data:c,tpl:l,selectorForELemCommand:"font[size]"}),r.updateSelectedEvent=function(){var e=i.getRangeElem();return e=i.getSelfOrParentByName(e,"font[size]"),!!e},i.menus[n]=r}}))})),t((function(e,t){e.createMenu((function(t){var n="head";if(t(n)){var i,a=this,o=a.config.lang,r=new e.Menu({editor:a,id:n,title:o.head,commandName:"formatBlock"}),c={"<h1>":"标题1","<h2>":"标题2","<h3>":"标题3","<h4>":"标题4","<h5>":"标题5"},l="{#commandValue}{#title}";r.dropList=new e.DropList(a,r,{data:c,tpl:l,beforeEvent:s,afterEvent:d}),r.updateSelectedEvent=function(){var e=a.getRangeElem();return e=a.getSelfOrParentByName(e,"h1,h2,h3,h4,h5"),!!e},a.menus[n]=r}function s(e){a.queryCommandState("InsertOrderedList")?(i=!0,a.command(e,"InsertOrderedList")):i=!1}function d(e){i&&a.command(e,"InsertOrderedList")}}))})),t((function(e,t){e.createMenu((function(t){var n="unorderlist";if(t(n)){var i=this,a=i.config.lang,o=new e.Menu({editor:i,id:n,title:a.unorderlist,commandName:"InsertUnorderedList"});i.menus[n]=o}}))})),t((function(e,t){e.createMenu((function(t){var n="orderlist";if(t(n)){var i=this,a=i.config.lang,o=new e.Menu({editor:i,id:n,title:a.orderlist,commandName:"InsertOrderedList"});i.menus[n]=o}}))})),t((function(e,t){e.createMenu((function(n){var i="alignleft";if(n(i)){var a=this,o=a.config.lang,r=new e.Menu({editor:a,id:i,title:o.alignleft,commandName:"JustifyLeft"});r.updateSelectedEvent=function(){var e=a.getRangeElem();return e=a.getSelfOrParentByName(e,"p,h1,h2,h3,h4,h5,li",(function(e){var n;return!!(e&&e.style&&null!=e.style.cssText&&(n=e.style.cssText,n&&/text-align:\s*left;/.test(n)))||"left"===t(e).attr("align")})),!!e},a.menus[i]=r}}))})),t((function(e,t){e.createMenu((function(n){var i="aligncenter";if(n(i)){var a=this,o=a.config.lang,r=new e.Menu({editor:a,id:i,title:o.aligncenter,commandName:"JustifyCenter"});r.updateSelectedEvent=function(){var e=a.getRangeElem();return e=a.getSelfOrParentByName(e,"p,h1,h2,h3,h4,h5,li",(function(e){var n;return!!(e&&e.style&&null!=e.style.cssText&&(n=e.style.cssText,n&&/text-align:\s*center;/.test(n)))||"center"===t(e).attr("align")})),!!e},a.menus[i]=r}}))})),t((function(e,t){e.createMenu((function(n){var i="alignright";if(n(i)){var a=this,o=a.config.lang,r=new e.Menu({editor:a,id:i,title:o.alignright,commandName:"JustifyRight"});r.updateSelectedEvent=function(){var e=a.getRangeElem();return e=a.getSelfOrParentByName(e,"p,h1,h2,h3,h4,h5,li",(function(e){var n;return!!(e&&e.style&&null!=e.style.cssText&&(n=e.style.cssText,n&&/text-align:\s*right;/.test(n)))||"right"===t(e).attr("align")})),!!e},a.menus[i]=r}}))})),t((function(e,t){e.createMenu((function(n){var i="link";if(n(i)){var a=this,o=a.config.lang,r=new e.Menu({editor:a,id:i,title:o.link}),c=t("<div></div>"),l=t('<div style="margin:20px 10px;" class="clearfix"></div>'),s=l.clone(),d=l.clone().css("margin","0 10px"),u=t('<input type="text" class="block" placeholder="'+o.text+'"/>'),f=t('<input type="text" class="block" placeholder="'+o.link+'"/>'),m=t('<button class="right">'+o.submit+"</button>"),p=t('<button class="right gray">'+o.cancel+"</button>");l.append(u),s.append(f),d.append(m).append(p),c.append(l).append(s).append(d),r.dropPanel=new e.DropPanel(a,r,{$content:c,width:300}),r.clickEvent=function(e){var t=this,n=t.dropPanel;if(n.isShowing)n.hide();else{u.val(""),f.val("http://");var i="",o=a.getRangeElem();o=a.getSelfOrParentByName(o,"a"),o&&(i=o.href||"");var r="",c=a.isRangeEmpty();c?o&&(r=o.textContent||o.innerHTML):r=a.getRangeText()||"",i&&f.val(i),r&&u.val(r),c?u.removeAttr("disabled"):u.attr("disabled",!0),n.show()}},r.updateSelectedEvent=function(){var e=a.getRangeElem();return e=a.getSelfOrParentByName(e,"a"),!!e},p.click((function(e){e.preventDefault(),r.dropPanel.hide()})),m.click((function(n){n.preventDefault();var i,o,c,l,s,d,m=a.getRangeElem(),p=a.getSelfOrParentByName(m,"a"),h=a.isRangeEmpty(),g=a.txt.$txt,v="link"+e.random(),w=t.trim(f.val()),x=t.trim(u.val());w?(x||(x=w),h?p?(i=t(p),c=function(){i.attr("href",w),i.text(x)},l=function(){var e=this;e.restoreSelectionByElem(p)},a.customCommand(n,c,l)):(o='<a href="'+w+'" target="_blank">'+x+"</a>",e.userAgent.indexOf("Firefox")>0&&(o+="<span>&nbsp;</span>"),a.command(n,"insertHtml",o)):(s=g.find("a"),s.attr(v,"1"),a.command(n,"createLink",w),d=g.find("a").not("["+v+"]"),d.attr("target","_blank"),s.removeAttr(v))):r.dropPanel.focusFirstInput()})),a.menus[i]=r}}))})),t((function(e,t){e.createMenu((function(n){var i="unlink";if(n(i)){var a=this,o=a.config.lang,r=new e.Menu({editor:a,id:i,title:o.unlink,commandName:"unLink"});r.clickEvent=function(e){var n=a.isRangeEmpty();if(n){var i=a.getRangeElem(),o=a.getSelfOrParentByName(i,"a");if(o){var r=t(o),c=t("<span>"+r.text()+"</span>");a.customCommand(e,l,s)}else e.preventDefault()}else a.command(e,"unLink");function l(){r.after(c).remove()}function s(){a.restoreSelectionByElem(c.get(0))}},a.menus[i]=r}}))})),t((function(e,t){e.createMenu((function(n){var i="table";if(n(i)){var a,o,r,c=this,l=c.config.lang,s=new e.Menu({editor:c,id:i,title:l.table}),d=t('<div style="font-size: 14px; color: #666; text-align:right;"></div>'),u=t('<table class="choose-table" style="margin-bottom:10px;margin-top:5px;">'),f=t("<span>0</span>"),m=t("<span> 行 </span>"),p=t("<span>0</span>"),h=t("<span> 列</span>");for(o=0;o<15;o++){for(a=t('<tr index="'+(o+1)+'">'),r=0;r<20;r++)a.append(t('<td index="'+(r+1)+'">'));u.append(a)}d.append(u),d.append(f).append(m).append(p).append(h),u.on("mouseenter","td",(function(e){var n=t(e.currentTarget),i=n.attr("index"),a=n.parent(),o=a.attr("index");f.text(o),p.text(i),u.find("tr").each((function(){var e=t(this),n=e.attr("index");parseInt(n,10)<=parseInt(o,10)?e.find("td").each((function(){var e=t(this),n=e.attr("index");parseInt(n,10)<=parseInt(i,10)?e.addClass("active"):e.removeClass("active")})):e.find("td").removeClass("active")}))})).on("mouseleave",(function(e){u.find("td").removeClass("active"),f.text(0),p.text(0)})),u.on("click","td",(function(e){var n,i,a=t(e.currentTarget),o=a.attr("index"),r=a.parent(),l=r.attr("index"),s=parseInt(l,10),d=parseInt(o,10),u="<table>";for(n=0;n<s;n++){for(u+="<tr>",i=0;i<d;i++)u+="<td><span>&nbsp;</span></td>";u+="</tr>"}u+="</table>",c.command(e,"insertHtml",u)})),s.dropPanel=new e.DropPanel(c,s,{$content:d,width:262}),c.menus[i]=s}}))})),t((function(e,t){e.createMenu((function(n){var i="emotion";if(n(i)){var a=this,o=a.config,r=o.lang,c=o.emotions,l=o.emotionsShow;a.emotionUrls=[];var s=new e.Menu({editor:a,id:i,title:r.emotion}),d=t('<div class="panel-tab"></div>'),u=t('<div class="tab-container"></div>'),f=t('<div class="content-container emotion-content-container"></div>');t.each(c,(function(n,i){var a=i.title,o=i.data;e.log("正在处理 "+a+" 表情的数据...");var r=t('<a href="#">'+a+" </a>");u.append(r);var c=t('<div class="content"></div>');if(f.append(c),r.click((function(e){u.children().removeClass("selected"),f.children().removeClass("selected"),c.addClass("selected"),r.addClass("selected"),e.preventDefault()})),"string"===typeof o)e.log("将通过 "+o+" 地址ajax下载表情包"),t.get(o,(function(n){n=t.parseJSON(n),e.log("下载完毕，得到 "+n.length+" 个表情"),m(n,c)}));else{if(!(Object.prototype.toString.call(o).toLowerCase().indexOf("array")>0))return void e.error("data 数据格式错误，请修改为正确格式，参考文档："+e.docsite);m(o,c)}})),d.append(u).append(f),u.children().first().addClass("selected"),f.children().first().addClass("selected"),f.on("click","a[commandValue]",(function(e){var n=t(e.currentTarget),i=n.attr("commandValue");"icon"===l?a.command(e,"InsertImage",i):a.command(e,"insertHtml","<span>"+i+"</span>"),e.preventDefault()})),s.dropPanel=new e.DropPanel(a,s,{$content:d,width:350}),s.clickEvent=function(n){var i=this,a=i.dropPanel;a.isShowing?a.hide():(a.show(),i.imgLoaded||(f.find("img").each((function(){var n=t(this),i=n.attr("_src");n.on("error",(function(){e.error("加载不出表情图片 "+i)})),n.attr("src",i),n.removeAttr("_src")})),i.imgLoaded=!0))},a.menus[i]=s}function m(e,n){t.each(e,(function(e,i){var o=i.icon||i.url,r=i.value||i.title,c="icon"===l?o:r,s=t('<a href="#" commandValue="'+c+'"></a>'),d=t("<img>");d.attr("_src",o),s.append(d),n.append(s),a.emotionUrls.push(o)}))}}))})),t((function(e,t){function n(e,n,i){var a=e.config.lang,o=t('<div style="margin:20px 10px 10px 10px;"></div>'),r=t('<input type="text" class="block" placeholder="http://"/>');o.append(r);var c=t('<button class="right">'+a.submit+"</button>"),l=t('<button class="right gray">'+a.cancel+"</button>");function s(){r.val("")}i.append(o).append(c).append(l),l.click((function(e){e.preventDefault(),n.dropPanel.hide()})),c.click((function(n){n.preventDefault();var i=t.trim(r.val());if(i){var a='<img style="max-width:100%;" src="'+i+'"/>';e.command(n,"insertHtml",a,s)}else r.focus()}))}e.createMenu((function(i){var a="img";if(i(a)){var o=this,r=o.config.lang,c=new e.Menu({editor:o,id:a,title:r.img}),l=t('<div class="panel-tab"></div>'),s=t('<div class="tab-container"></div>'),d=t('<div class="content-container"></div>');l.append(s).append(d);var u=t('<a href="#">'+r.uploadImg+"</a>"),f=t('<a href="#">'+r.linkImg+"</a>");s.append(u).append(f);var m=t('<div class="content"></div>');d.append(m);var p=t('<div class="content"></div>');d.append(p),n(o,c,p),c.dropPanel=new e.DropPanel(o,c,{$content:l,width:400,onRender:function(){var e=o.config.customUploadInit;e&&e.call(o)}}),o.menus[a]=c,o.ready((function(){var e=this,t=e.config,n=t.uploadImgUrl,i=t.customUpload,a=t.hideLinkImg;function o(){c.dropPanel.hide()}n||i?(e.$uploadContent=m,h(),a&&v()):g(),m.click((function(){setTimeout(o)}))}))}function h(){u.click((function(e){s.children().removeClass("selected"),d.children().removeClass("selected"),m.addClass("selected"),u.addClass("selected"),e.preventDefault()})),f.click((function(t){s.children().removeClass("selected"),d.children().removeClass("selected"),p.addClass("selected"),f.addClass("selected"),t.preventDefault(),e.placeholder&&p.find("input[type=text]").focus()})),u.click()}function g(){s.remove(),m.remove(),p.addClass("selected")}function v(){s.remove(),p.remove(),m.addClass("selected")}}))})),t((function(e,t){e.createMenu((function(n){var i="video";if(n(i)){var a=this,o=a.config.lang,r=/^<(iframe)|(embed)/i,c=new e.Menu({editor:a,id:i,title:o.video}),l=t("<div></div>"),s=t('<div style="margin:20px 10px;"></div>'),d=t('<input type="text" class="block" placeholder=\'格式如：<iframe src="..." frameborder=0 allowfullscreen></iframe>\'/>');s.append(d);var u=t('<div style="margin:20px 10px;"></div>'),f=t('<input type="text" value="640" style="width:50px;text-align:center;"/>'),m=t('<input type="text" value="498" style="width:50px;text-align:center;"/>');u.append("<span> "+o.width+" </span>").append(f).append("<span> px &nbsp;&nbsp;&nbsp;</span>").append("<span> "+o.height+" </span>").append(m).append("<span> px </span>");var p=t("<div></div>"),h=t('<a href="http://www.kancloud.cn/wangfupeng/wangeditor2/134973" target="_blank" style="display:inline-block;margin-top:10px;margin-left:10px;color:#999;">如何复制视频链接？</a>'),g=t('<button class="right">'+o.submit+"</button>"),v=t('<button class="right gray">'+o.cancel+"</button>");p.append(h).append(g).append(v),l.append(s).append(u).append(p),v.click((function(e){e.preventDefault(),d.val(""),c.dropPanel.hide()})),g.click((function(e){e.preventDefault();var n,i=t.trim(d.val()),o=parseInt(f.val()),l=parseInt(m.val()),s=t("<div>"),u="<p>{content}</p>";if(i)return r.test(i)?void(isNaN(o)||isNaN(l)?alert("宽度或高度不是数字！"):(n=t(i),n.attr("width",o).attr("height",l),u=u.replace("{content}",s.append(n).html()),a.command(e,"insertHtml",u),d.val(""))):(alert("视频链接格式错误！"),void c.dropPanel.focusFirstInput());c.dropPanel.focusFirstInput()})),c.dropPanel=new e.DropPanel(a,c,{$content:l,width:400}),a.menus[i]=c}}))})),t((function(e,t){var n=function(e){return"onkeyup"in e}(document.createElement("input"));e.baiduMapAk="TVhjYjq1ICT2qqL5LdS8mwas",e.numberOfLocation=0,e.createMenu((function(i){var a="location";if(i(a))if(++e.numberOfLocation>1)e.error("目前不支持在一个页面多个编辑器上同时使用地图，可通过自定义菜单配置去掉地图菜单");else{var o=this,r=o.config,c=r.lang,l=r.mapAk;o.mapData={};var s=o.mapData;s.markers=[],s.mapContainerId="map"+e.random(),s.clearLocations=function(){var e=s.map;e&&(e.clearOverlays(),s.markers=[])},s.searchMap=function(){var e=s.map;if(e){var t,n,i=window.BMap,a=p.val(),o=h.val();""!==a&&(o&&""!==o||e.centerAndZoom(a,11),o&&""!==o&&(t=new i.Geocoder,t.getPoint(o,(function(t){t?(e.centerAndZoom(t,13),n=new i.Marker(t),e.addOverlay(n),n.enableDragging(),s.markers.push(n)):e.centerAndZoom(a,11)}),a)))}};var d=!1;window.baiduMapCallBack=function(){if(!d){d=!0;var t=window.BMap;s.map||(s.map=new t.Map(s.mapContainerId));var i=s.map;i.centerAndZoom(new t.Point(116.404,39.915),11),i.addControl(new t.MapTypeControl),i.setCurrentCity("北京"),i.enableScrollWheelZoom(!0);var a=new t.LocalCity;a.get(o),i.addEventListener("click",(function(e){var n=new t.Marker(new t.Point(e.point.lng,e.point.lat));i.addOverlay(n),n.enableDragging(),s.markers.push(n)}),!1)}function o(t){var a,o,r=t.name;i.setCenter(r),p.val(r),e.placeholder&&h.focus(),n?(o=function(e){"keyup"===e.type&&13===e.keyCode&&e.preventDefault(),a&&clearTimeout(a),a=setTimeout(s.searchMap,500)},p.on("keyup change paste",o),h.on("keyup change paste",o)):(o=function(){if(f.is(":visible")){var e="",t="",n=p.val(),i=h.val();n===e&&i===t||(s.searchMap(),e=n,t=i),a&&clearTimeout(a),a=setTimeout(o,1e3)}else clearTimeout(a)},a=setTimeout(o,1e3))}},s.loadMapScript=function(){var t=document.createElement("script");t.type="text/javascript",t.src="https://api.map.baidu.com/api?v=2.0&ak="+l+"&s=1&callback=baiduMapCallBack";try{document.body.appendChild(t)}catch(n){e.error("加载地图过程中发生错误")}},s.initMap=function(){window.BMap?window.baiduMapCallBack():s.loadMapScript()};var u=new e.Menu({editor:o,id:a,title:c.location});o.menus[a]=u;var f=t("<div></div>"),m=t('<div style="margin:10px 0;"></div>'),p=t('<input type="text"/>');p.css({width:"80px","text-align":"center"});var h=t('<input type="text"/>');h.css({width:"300px","margin-left":"10px"}).attr("placeholder",c.searchlocation);var g=t('<button class="right link">'+c.clearLocation+"</button>");m.append(g).append(p).append(h),f.append(m),g.click((function(e){h.val(""),h.focus(),s.clearLocations(),e.preventDefault()}));var v=t('<div id="'+s.mapContainerId+'"></div>');v.css({height:"260px",width:"100%",position:"relative","margin-top":"10px",border:"1px solid #f1f1f1"});var w=t("<span>"+c.loading+"</span>");w.css({position:"absolute",width:"100px","text-align":"center",top:"45%",left:"50%","margin-left":"-50px"}),v.append(w),f.append(v);var x=t('<div style="margin:10px 0;"></div>'),y=t('<button class="right">'+c.submit+"</button>"),b=t('<button class="right gray">'+c.cancel+"</button>"),k=t('<label style="display:inline-block;margin-top:10px;color:#666;"></label>'),E=t('<input type="checkbox">');k.append(E).append('<span style="display:inline-block;margin-left:5px;">  '+c.dynamicMap+"</span>"),x.append(k).append(y).append(b),f.append(x),b.click((function(e){e.preventDefault(),C(),u.dropPanel.hide()})),y.click((function(e){e.preventDefault();var n,i,a,r=s.map,l=E.is(":checked"),d=s.markers,u=r.getCenter(),f=u.lng,m=u.lat,p=r.getZoom(),h=r.getSize(),g=h.width,v=h.height;if(i=l?"http://ueditor.baidu.com/ueditor/dialogs/map/show.html#":"http://api.map.baidu.com/staticimage?",i=i+"center="+f+","+m+"&zoom="+p+"&width="+g+"&height="+v,d.length>0&&(i+="&markers=",t.each(d,(function(e,t){n=t.getPosition(),e>0&&(i+="|"),i=i+n.lng+","+n.lat}))),l){if(d.length>1)return void alert(c.langDynamicOneLocation);i+="&markerStyles=l,A",a='<iframe class="ueditor_baidumap" src="{src}" frameborder="0" width="'+g+'" height="'+v+'"></iframe>',a=a.replace("{src}",i),o.command(e,"insertHtml",a,C)}else o.command(e,"insertHtml",'<img style="max-width:100%;" src="'+i+'"/>',C)})),u.dropPanel=new e.DropPanel(o,u,{$content:f,width:500}),u.onRender=function(){l===e.baiduMapAk&&e.warn("建议在配置中自定义百度地图的mapAk，否则可能影响地图功能，文档："+e.docsite)},u.clickEvent=function(e){var t=this,n=t.dropPanel,i=!1;n.isShowing?n.hide():(s.map||(i=!0),n.show(),s.initMap(),i||h.focus())}}function C(){h.val("")}}))})),t((function(e,t){function n(){if(!(e.userAgent.indexOf("MSIE 8")>0)&&!window.hljs){var t=document.createElement("script");t.type="text/javascript",t.src="//cdn.bootcss.com/highlight.js/9.2.0/highlight.min.js",document.body.appendChild(t)}}e.createMenu((function(i){var a="insertcode";if(i(a)){setTimeout(n,0);var o=this,r=o.config,c=r.lang,l=o.txt.$txt,s=new e.Menu({editor:o,id:a,title:c.insertcode});s.clickEvent=function(e){var n=this,i=n.dropPanel;if(i.isShowing)i.hide();else{u.val(""),i.show();var a=window.hljs;if(a&&a.listLanguages){if(0!==f.children().length)return;f.css({"margin-top":"9px","margin-left":"5px"}),t.each(a.listLanguages(),(function(e,t){"xml"===t&&(t="html"),t===r.codeDefaultLang?f.append('<option value="'+t+'" selected="selected">'+t+"</option>"):f.append('<option value="'+t+'">'+t+"</option>")}))}else f.hide()}},s.clickEventSelected=function(e){var n=this,i=n.dropPanel;if(i.isShowing)i.hide();else{i.show();var a,r,c=o.getRangeElem(),l=o.getSelfOrParentByName(c,"pre");l&&(l=o.getSelfOrParentByName(c,"code")),l&&(a=t(l),u.val(a.text()),f&&(r=a.attr("class"),r&&f.val(r.split(" ")[0])))}},s.updateSelectedEvent=function(){var e,t=this,n=t.editor;return e=n.getRangeElem(),e=n.getSelfOrParentByName(e,"pre"),!!e};var d=t("<div></div>"),u=t("<textarea></textarea>"),f=t("<select></select>");m(d),s.dropPanel=new e.DropPanel(o,s,{$content:d,width:500}),o.menus[a]=s,l.on("keydown",(function(e){if(13===e.keyCode){var t=o.getRangeElem(),n=o.getSelfOrParentByName(t,"code");n&&o.command(e,"insertHtml","\n")}})),l.on("keydown click",(function(e){setTimeout(p)}))}function m(e){var n=t("<div></div>");n.css({margin:"15px 5px 5px 5px",height:"160px","text-align":"center"}),u.css({width:"100%",height:"100%",padding:"10px"}),u.on("keydown",(function(e){9===e.keyCode&&e.preventDefault()})),n.append(u),e.append(n);var i=t("<div></div>"),a=t('<button class="right">'+c.submit+"</button>"),r=t('<button class="right gray">'+c.cancel+"</button>");i.append(a).append(r).append(f),e.append(i),r.click((function(e){e.preventDefault(),s.dropPanel.hide()}));var d='<pre style="max-width:100%;overflow-x:auto;"><code{#langClass}>{#content}</code></pre>';a.click((function(e){e.preventDefault();var n=u.val();if(n){var i=o.getRangeElem();t.trim(t(i).text())&&0!==d.indexOf("<p><br></p>")&&(d="<p><br></p>"+d);var a=f?f.val():"",r="",c=function(){l.find("pre code").each((function(e,n){var i=t(n);i.attr("codemark")||window.hljs&&(window.hljs.highlightBlock(n),i.attr("codemark","1"))}))};if(a&&(r=' class="'+a+' hljs"'),n=n.replace(/&/gm,"&amp;").replace(/</gm,"&lt;").replace(/>/gm,"&gt;"),s.selected){var m,p=o.getSelfOrParentByName(i,"pre");p&&(p=o.getSelfOrParentByName(i,"code")),p&&(m=t(p),o.customCommand(e,g,v))}else{var h=d.replace("{#langClass}",r).replace("{#content}",n);o.command(e,"insertHtml",h,c)}}else u.focus();function g(){var e;a&&(e=m.attr("class"),e!==a+" hljs"&&m.attr("class",a+" hljs")),m.html(n)}function v(){o.restoreSelectionByElem(p),c()}}))}function p(){var e=o.getRangeElem(),t=o.getSelfOrParentByName(e,"code");t?o.disableMenusExcept("insertcode"):o.enableMenusExcept("insertcode")}}))})),t((function(e,t){e.createMenu((function(t){var n="undo";if(t(n)){var i=this,a=i.config.lang,o=new e.Menu({editor:i,id:n,title:a.undo});o.clickEvent=function(e){i.undo()},i.menus[n]=o,i.ready((function(){var e,t=this,n=t.txt.$txt;function i(){t.undoRecord()}n.on("keydown",(function(n){var a=n.keyCode;n.ctrlKey&&90===a?t.undo():13===a?i():(e&&clearTimeout(e),e=setTimeout(i,1e3))})),t.undoRecord()}))}}))})),t((function(e,t){e.createMenu((function(t){var n="redo";if(t(n)){var i=this,a=i.config.lang,o=new e.Menu({editor:i,id:n,title:a.redo});o.clickEvent=function(e){i.redo()},i.menus[n]=o}}))})),t((function(e,t){var n;e.createMenu((function(t){var i="fullscreen";if(t(i)){var a,o,r=this,c=r.txt.$txt,l=r.config,s=l.zindex||1e4,d=l.lang,u=!1,f=new e.Menu({editor:r,id:i,title:d.fullscreen});f.clickEvent=function(t){var i,l=r.$editorContainer;l.addClass("wangEditor-fullscreen"),a=l.css("z-index"),l.css("z-index",s);var d=c.height(),f=c.outerHeight();r.useMaxHeight&&(o=c.css("max-height"),c.css("max-height","none"),i=c.parent(),i.after(c),i.remove(),c.css("overflow-y","auto"));var m=r.menuContainer;c.height(e.$window.height()-m.height()-(f-d)),r.menuContainer.$menuContainer.attr("style",""),u=!0,r.isFullScreen=!0,n=e.$window.scrollTop()},f.clickEventSelected=function(t){var i=r.$editorContainer;i.removeClass("wangEditor-fullscreen"),i.css("z-index",a),r.useMaxHeight?c.css("max-height",o):r.$valueContainer.css("height",r.valueContainerHeight),r.txt.initHeight(),u=!1,r.isFullScreen=!1,null!=n&&e.$window.scrollTop(n)},f.updateSelectedEvent=function(e){return u},r.menus[i]=f}}))})),t((function(e,t){e.fn.renderMenus=function(){var e,n=this,i=n.menus,a=n.config.menus,o=(n.menuContainer,0);t.each(a,(function(t,n){"|"!==n?(e=i[n],e&&e.render(o)):o++}))}})),t((function(e,t){e.fn.renderMenuContainer=function(){var e=this,t=e.menuContainer;e.$editorContainer;t.render()}})),t((function(e,t){e.fn.renderTxt=function(){var e=this,t=e.txt;t.render(),e.ready((function(){t.initHeight()}))}})),t((function(e,t){e.fn.renderEditorContainer=function(){var e,t,n=this,i=n.$valueContainer,a=n.$editorContainer,o=n.txt.$txt;i===o?(e=n.$prev,t=n.$parent,e&&e.length?e.after(a):t.prepend(a)):(i.after(a),i.hide())}})),t((function(e,t){e.fn.eventMenus=function(){var e=this.menus;t.each(e,(function(e,t){t.bindEvent()}))}})),t((function(e,t){e.fn.eventMenuContainer=function(){}})),t((function(e,t){e.fn.eventTxt=function(){var e=this.txt;e.saveSelectionEvent(),e.updateValueEvent(),e.updateMenuStyleEvent()}})),t((function(e,t){e.plugin((function(){var t=this,n=t.config.uploadImgFns;n.onload||(n.onload=function(t,n){e.log("上传结束，返回结果为 "+t);var i,a=this,o=a.uploadImgOriginalName||"";0===t.indexOf("error|")?(e.warn("上传失败："+t.split("|")[1]),alert(t.split("|")[1])):(e.log("上传成功，即将插入编辑区域，结果为："+t),i=document.createElement("img"),i.onload=function(){var n='<img src="'+t+'" alt="'+o+'" style="max-width:100%;"/>';a.command(null,"insertHtml",n),e.log("已插入图片，地址 "+t),i=null},i.onerror=function(){e.error("使用返回的结果获取图片，发生错误。请确认以下结果是否正确："+t),i=null},i.src=t)}),n.ontimeout||(n.ontimeout=function(t){e.error("上传图片超时"),alert("上传图片超时")}),n.onerror||(n.onerror=function(t){e.error("上传上图片发生错误"),alert("上传上图片发生错误")})}))})),t((function(e,t){window.FileReader&&window.FormData&&e.plugin((function(){var n=this,i=n.config,a=i.uploadImgUrl,o=i.uploadTimeout,r=i.uploadImgFns,c=r.onload,l=r.ontimeout,s=r.onerror;function d(e,t){var n,i=window.atob(e.split(",")[1]),a=new ArrayBuffer(i.length),o=new Uint8Array(a);for(n=0;n<i.length;n++)o[n]=i.charCodeAt(n);return new Blob([a],{type:t})}function u(t,i){var a=document.createElement("img");a.onload=function(){var o='<img src="'+t+'" style="max-width:100%;"/>';n.command(i,"insertHtml",o),e.log("已插入图片，地址 "+t),a=null},a.onerror=function(){e.error("使用返回的结果获取图片，发生错误。请确认以下结果是否正确："+t),a=null},a.src=t}function f(e){if(e.lengthComputable){var t=e.loaded/e.total;n.showUploadProgress(100*t)}}a&&(n.xhrUploadImg=function(i){var r=i.event,m=i.filename||"",p=i.base64,h=i.fileType||"image/png",g=i.name||"wangEditor_upload_file",v=i.loadfn||c,w=i.errorfn||s,x=i.timeoutfn||l,y=n.config.uploadParams||{},b=n.config.uploadHeaders||{},k="png";if(m.indexOf(".")>0?k=m.slice(m.lastIndexOf(".")-m.length+1):h.indexOf("/")>0&&h.split("/")[1]&&(k=h.split("/")[1]),e.isOnWebsite)return e.log("预览模拟上传"),void u(p,r);var E,C=new XMLHttpRequest,$=new FormData;function S(){E&&clearTimeout(E),C&&C.abort&&C.abort(),r.preventDefault(),x&&x.call(n,C),n.hideUploadProgress()}C.onload=function(){E&&clearTimeout(E),n.uploadImgOriginalName=m,m.indexOf(".")>0&&(n.uploadImgOriginalName=m.split(".")[0]),v&&v.call(n,C.responseText,C),n.hideUploadProgress()},C.onerror=function(){E&&clearTimeout(E),r.preventDefault(),w&&w.call(n,C),n.hideUploadProgress()},C.upload.onprogress=f,$.append(g,d(p,h),e.random()+"."+k),t.each(y,(function(e,t){$.append(e,t)})),C.open("POST",a,!0),t.each(b,(function(e,t){C.setRequestHeader(e,t)})),C.withCredentials=n.config.withCredentials||!0,C.send($),E=setTimeout(S,o),e.log("开始上传...并开始超时计算")})}))})),t((function(e,t){e.plugin((function(){var e,n=this,i=n.menuContainer,a=i.height(),o=n.$editorContainer,r=o.width(),c=t('<div class="wangEditor-upload-progress"></div>'),l=!1;function s(){l||(l=!0,c.css({top:a+"px"}),o.append(c))}function d(){c.hide(),e=null}n.showUploadProgress=function(t){e&&clearTimeout(e),s(),c.show(),c.width(t*r/100)},n.hideUploadProgress=function(t){e&&clearTimeout(e),t=t||750,e=setTimeout(d,t)}}))})),t((function(e,t){e.plugin((function(){var n=this,i=n.config,a=i.uploadImgUrl,o=i.uploadTimeout;if(a){var r=n.$uploadContent;if(r){var c=t('<div class="upload-icon-container"><i class="wangeditor-menu-img-upload"></i></div>');r.append(c);var l=new e.UploadFile({editor:n,uploadUrl:a,timeout:o,fileAccept:"image/jpg,image/jpeg,image/png,image/gif,image/bmp"});c.click((function(e){e,l.selectFiles()}))}}}))})),t((function(e,t){if(window.FileReader&&window.FormData){var n=function(e){this.editor=e.editor,this.uploadUrl=e.uploadUrl,this.timeout=e.timeout,this.fileAccept=e.fileAccept,this.multiple=!0};n.fn=n.prototype,n.fn.clear=function(){this.$input.val(""),e.log("input value 已清空")},n.fn.render=function(){var n=this;if(!n._hasRender){e.log("渲染dom");var i=n.fileAccept,a=i?'accept="'+i+'"':"",o=n.multiple,r=o?'multiple="multiple"':"",c=t('<input type="file" '+a+" "+r+"/>"),l=t('<div style="display:none;"></div>');l.append(c),e.$body.append(l),c.on("change",(function(e){n.selected(e,c.get(0))})),n.$input=c,n._hasRender=!0}},n.fn.selectFiles=function(){var t=this;e.log("使用 html5 方式上传"),t.render(),e.log("选择文件"),t.$input.click()},n.fn.selected=function(n,i){var a=this,o=i.files||[];0!==o.length&&(e.log("选中 "+o.length+" 个文件"),t.each(o,(function(e,t){a.upload(t)})))},n.fn.upload=function(t){var n=this,i=n.editor,a=t.name||"",o=t.type||"",r=i.config.uploadImgFns,c=i.config.uploadImgFileName||"wangEditorH5File",l=r.onload,s=r.ontimeout,d=r.onerror,u=new FileReader;function f(){n.clear()}l&&s&&d?(e.log("开始执行 "+a+" 文件的上传"),u.onload=function(t){e.log("已读取"+a+"文件");var n=t.target.result||this.result;i.xhrUploadImg({event:t,filename:a,base64:n,fileType:o,name:c,loadfn:function(e,t){f();var n=this;l.call(n,e,t)},errorfn:function(t){f(),e.isOnWebsite&&alert("wangEditor官网暂时没有服务端，因此报错。实际项目中不会发生");var n=this;d.call(n,t)},timeoutfn:function(t){f(),e.isOnWebsite&&alert("wangEditor官网暂时没有服务端，因此超时。实际项目中不会发生");var n=this;s(n,t)}})},u.readAsDataURL(t)):e.error("请为编辑器配置上传图片的 onload ontimeout onerror 回调事件")},e.UploadFile=n}})),t((function(e,t){if(!window.FileReader||!window.FormData){var n=function(e){this.editor=e.editor,this.uploadUrl=e.uploadUrl,this.timeout=e.timeout,this.fileAccept=e.fileAccept,this.multiple=!1};n.fn=n.prototype,n.fn.clear=function(){this.$input.val(""),e.log("input value 已清空")},n.fn.hideModal=function(){this.modal.hide()},n.fn.render=function(){var n=this,i=n.editor,a=i.config.uploadImgFileName||"wangEditorFormFile";if(!n._hasRender){var o=n.uploadUrl;e.log("渲染dom");var r="iframe"+e.random(),c=t('<iframe name="'+r+'" id="'+r+'" frameborder="0" width="0" height="0"></iframe>'),l=n.multiple,s=l?'multiple="multiple"':"",d=t("<p>选择图片并上传</p>"),u=t('<input type="file" '+s+' name="'+a+'"/>'),f=t('<input type="submit" value="上传"/>'),m=t('<form enctype="multipart/form-data" method="post" action="'+o+'" target="'+r+'"></form>'),p=t('<div style="margin:10px 20px;"></div>');m.append(d).append(u).append(f),t.each(i.config.uploadParams,(function(e,n){m.append(t('<input type="hidden" name="'+e+'" value="'+n+'"/>'))})),p.append(m),p.append(c),n.$input=u,n.$iframe=c;var h=new e.Modal(i,void 0,{$content:p});n.modal=h,n._hasRender=!0}},n.fn.bindLoadEvent=function(){var e=this;if(!e._hasBindLoad){var n=e.editor,i=e.$iframe,a=i.get(0),o=a.contentWindow,r=n.config.uploadImgFns.onload;a.attachEvent?a.attachEvent("onload",c):a.onload=c,e._hasBindLoad=!0}function c(){var i=t.trim(o.document.body.innerHTML);if(i){var a=e.$input.val(),c=a;a.lastIndexOf("\\")>=0&&(c=a.slice(a.lastIndexOf("\\")+1),c.indexOf(".")>0&&(c=c.split(".")[0])),n.uploadImgOriginalName=c,r.call(n,i),e.clear(),e.hideModal()}}},n.fn.show=function(){var e=this,t=e.modal;function n(){t.show(),e.bindLoadEvent()}setTimeout(n)},n.fn.selectFiles=function(){var t=this;e.log("使用 form 方式上传"),t.render(),t.clear(),t.show()},e.UploadFile=n}})),t((function(e,t){e.plugin((function(){var n,i,a=this,o=a.txt,r=o.$txt,c=a.config,l=c.uploadImgUrl,s=c.uploadImgFileName||"wangEditorPasteFile";function d(){var o=/^data:(image\/\w+);base64/,c=r.find("img");e.log("粘贴后，检查到编辑器有"+c.length+"个图片。开始遍历图片，试图找到刚刚粘贴过来的图片"),t.each(c,(function(){var r,c,l=this,d=t(l),u=d.attr("src");i.each((function(){if(l===this)return r=!0,!1})),r||(e.log("找到一个粘贴过来的图片"),o.test(u)?(e.log("src 是 base64 格式，可以上传"),c=u.match(o)[1],a.xhrUploadImg({event:n,base64:u,fileType:c,name:s})):e.log("src 为 "+u+" ，不是 base64 格式，暂时不支持上传"),d.remove())})),e.log("遍历结束")}l&&r.on("paste",(function(o){n=o;var c,l,u=n.clipboardData||n.originalEvent.clipboardData;c=null==u?window.clipboardData&&window.clipboardData.getData("text"):u.getData("text/plain")||u.getData("text/html"),c||(l=u&&u.items,l?(e.log("通过 data.items 得到了数据"),t.each(l,(function(t,i){var o=i.type||"";if(!(o.indexOf("image")<0)){var r=i.getAsFile(),c=new FileReader;e.log("得到一个粘贴图片"),c.onload=function(t){e.log("读取到粘贴的图片");var i=t.target.result||this.result;a.xhrUploadImg({event:n,base64:i,fileType:o,name:s})},c.readAsDataURL(r)}}))):(e.log("未从 data.items 得到数据，使用检测粘贴图片的方式"),i=r.find("img"),e.log("粘贴前，检查到编辑器有"+i.length+"个图片"),setTimeout(d,0)))}))}))})),t((function(e,t){e.plugin((function(){var n=this,i=n.txt,a=i.$txt,o=n.config,r=o.uploadImgUrl,c=o.uploadImgFileName||"wangEditorDragFile";r&&(e.$document.on("dragleave drop dragenter dragover",(function(e){e.preventDefault()})),a.on("drop",(function(i){i.preventDefault();var a=i.originalEvent,o=a.dataTransfer&&a.dataTransfer.files;o&&o.length&&t.each(o,(function(t,a){var o=a.type,r=a.name;if(!(o.indexOf("image/")<0)){e.log("得到图片 "+r);var l=new FileReader;l.onload=function(t){e.log("读取到图片 "+r);var a=t.target.result||this.result;n.xhrUploadImg({event:i,base64:a,fileType:o,name:c})},l.readAsDataURL(a)}}))})))}))})),t((function(e,t){e.plugin((function(){var n,i=this,a=i.txt,o=a.$txt,r="",c=i.useMaxHeight?o.parent():o,l=!1,s=t('<div class="txt-toolbar"></div>'),d=t('<div class="tip-triangle"></div>'),u=t('<a href="#"><i class="wangeditor-menu-img-trash-o"></i></a>'),f=t('<a href="#"><i class="wangeditor-menu-img-search-minus"></i></a>'),m=t('<a href="#"><i class="wangeditor-menu-img-search-plus"></i></a>');function p(){l||(h(),s.append(d).append(u).append(f).append(m),i.$editorContainer.append(s),l=!0)}function h(){var e;function t(t,n){r=o.html();var a=function(){n&&n(),r!==o.html()&&o.change()};e&&i.customCommand(t,e,a)}u.click((function(i){e=function(){n.remove()},t(i,(function(){setTimeout(v,100)}))})),m.click((function(i){e=function(){n.css({width:"100%"})},t(i,(function(){setTimeout(g)}))})),f.click((function(i){e=function(){n.css({width:"auto"})},t(i,(function(){setTimeout(g)}))}))}function g(){if(!i._disabled&&null!=n){n.addClass("clicked");var e=n.position(),t=e.top,a=e.left,o=n.outerHeight(),r=n.outerWidth(),l=t+o,u=a,f=0,m=c.position().top,p=c.outerHeight();l>m+p&&(l=m+p),s.show();var h=s.outerWidth();f=r/2-h/2,s.css({top:l+5,left:u,"margin-left":f}),f<0?(s.css("margin-left","0"),d.hide()):d.show()}}function v(){null!=n&&(n.removeClass("clicked"),n=null,s.hide())}c.on("click","table",(function(e){var i=t(e.currentTarget);p(),n&&n.get(0)===i.get(0)?setTimeout(v,100):(n=i,g(),e.preventDefault(),e.stopPropagation())})).on("click keydown scroll",(function(e){setTimeout(v,100)})),e.$body.on("click keydown scroll",(function(e){setTimeout(v,100)}))}))})),t((function(e,t){e.userAgent.indexOf("MSIE 8")>0||e.plugin((function(){var n,i=this,a=i.config.lang,o=i.txt,r=o.$txt,c="",l=i.useMaxHeight?r.parent():r,s=(i.$editorContainer,""),d=!1,u=t('<div class="img-drag-point"></div>'),f=t('<div class="txt-toolbar"></div>'),m=t('<div class="tip-triangle"></div>'),p=t("<div></div>"),h=t('<a href="#"><i class="wangeditor-menu-img-trash-o"></i></a>'),g=t('<a href="#"><i class="wangeditor-menu-img-search-minus"></i></a>'),v=t('<a href="#"><i class="wangeditor-menu-img-search-plus"></i></a>'),w=t('<a href="#"><i class="wangeditor-menu-img-align-left"></i></a>'),x=t('<a href="#"><i class="wangeditor-menu-img-align-center"></i></a>'),y=t('<a href="#"><i class="wangeditor-menu-img-align-right"></i></a>'),b=t('<a href="#"><i class="wangeditor-menu-img-link"></i></a>'),k=t('<a href="#"><i class="wangeditor-menu-img-unlink"></i></a>'),E=t('<div style="display:none;"></div>'),C=t('<input type="text" style="height:26px; margin-left:10px; width:200px;"/>'),$=t('<button class="right">'+a.submit+"</button>"),S=t('<button class="right gray">'+a.cancel+"</button>"),M=!1;function T(e,a){if(n){var o,l,d=function(){null!=a&&(s=a),c!==r.html()&&r.change()},u=!1,f=n.parent();if("a"===f.get(0).nodeName.toLowerCase()?(l=f,u=!0):l=t('<a target="_blank"></a>'),null==a)return l.attr("href")||"";if(""===a)u&&(o=function(){n.unwrap()});else{if(a===s)return;o=function(){l.attr("href",a),u||n.wrap(l)}}o&&(c=r.html(),i.customCommand(e,o,d))}}function N(){d||(R(),D(),p.append(h).append(g).append(v).append(w).append(x).append(y).append(b).append(k),E.append(C).append(S).append($),f.append(m).append(p).append(E),i.$editorContainer.append(f).append(u),d=!0)}function R(){var e;function a(t,n){var a;c=r.html(),a=function(){n&&n(),c!==r.html()&&r.change()},e&&i.customCommand(t,e,a)}h.click((function(t){T(t,""),e=function(){n.remove()},a(t,(function(){setTimeout(I,100)}))})),v.click((function(t){e=function(){var e=n.get(0),t=e.width,i=e.height;t*=1.1,i*=1.1,n.css({width:t+"px",height:i+"px"})},a(t,(function(){setTimeout(P)}))})),g.click((function(t){e=function(){var e=n.get(0),t=e.width,i=e.height;t*=.9,i*=.9,n.css({width:t+"px",height:i+"px"})},a(t,(function(){setTimeout(P)}))})),w.click((function(t){e=function(){n.parents("p").css({"text-align":"left"}).attr("align","left")},a(t,(function(){setTimeout(I,100)}))})),y.click((function(t){e=function(){n.parents("p").css({"text-align":"right"}).attr("align","right")},a(t,(function(){setTimeout(I,100)}))})),x.click((function(t){e=function(){n.parents("p").css({"text-align":"center"}).attr("align","center")},a(t,(function(){setTimeout(I,100)}))})),b.click((function(e){e.preventDefault(),s=T(e),C.val(s),p.hide(),E.show()})),$.click((function(e){e.preventDefault();var n=t.trim(C.val());n&&T(e,n),setTimeout(I)})),S.click((function(e){e.preventDefault(),C.val(s),p.show(),E.hide()})),k.click((function(e){e.preventDefault(),T(e,""),setTimeout(I)}))}function D(){var t,i,a,o,r,c;function l(e){var l,s;l=e.pageX-t,s=e.pageY-i;var d=a+l,f=o+s;u.css({"margin-left":d,"margin-top":f});var m=r+l,p=c+s;n&&n.css({width:m,height:p})}u.on("mousedown",(function(s){n&&(t=s.pageX,i=s.pageY,a=parseFloat(u.css("margin-left"),10),o=parseFloat(u.css("margin-top"),10),r=n.width(),c=n.height(),f.hide(),e.$document.on("mousemove._dragResizeImg",l),e.$document.on("mouseup._dragResizeImg",(function(t){e.$document.off("mousemove._dragResizeImg"),e.$document.off("mouseup._dragResizeImg"),I(),u.css({"margin-left":a,"margin-top":o}),M=!1})),M=!0)}))}function P(){if(!i._disabled&&null!=n){n.addClass("clicked");var e=n.position(),t=e.top,a=e.left,o=n.outerHeight(),r=n.outerWidth();u.css({top:t+o,left:a+r});var c=t+o,s=a,d=0,p=l.position().top,h=l.outerHeight();c>p+h?c=p+h:u.show(),f.show();var g=f.outerWidth();d=r/2-g/2,f.css({top:c+5,left:s,"margin-left":d}),d<0?(f.css("margin-left","0"),m.hide()):m.show(),i.disableMenusExcept()}}function I(){null!=n&&(n.removeClass("clicked"),n=null,f.hide(),u.hide(),i.enableMenusExcept())}function O(e){var n=!1;return i.emotionUrls?(t.each(i.emotionUrls,(function(t,i){var a=!1;if(e===i&&(n=!0,a=!0),a)return!1})),n):n}l.on("mousedown","img",(function(e){e.preventDefault()})).on("click","img",(function(e){var i=t(e.currentTarget),a=i.attr("src");a&&!O(a)&&(N(),n&&n.get(0)===i.get(0)?setTimeout(I,100):(n=i,P(),p.show(),E.hide(),e.preventDefault(),e.stopPropagation()))})).on("click keydown scroll",(function(e){M||setTimeout(I,100)}))}))})),t((function(e,t){e.plugin((function(){var e,n,i,a,o,r=this,c=r.config.lang,l=r.txt.$txt,s=t('<div class="txt-toolbar"></div>'),d=t('<div class="tip-triangle"></div>'),u=t('<a href="#" target="_blank"><i class="wangeditor-menu-img-link"></i> '+c.openLink+"</a>"),f=!1;function m(){n||(s.append(d).append(u),r.$editorContainer.append(s),n=!0)}function p(){if(e){var t=e.position(),n=t.left,i=t.top,a=e.height(),o=i+a+5,c=r.menuContainer.height(),l=r.txt.$txt.outerHeight();o>c+l&&(o=c+l+5),s.css({top:o,left:n})}}function h(){if(!f&&e){m(),s.show();var t=e.attr("href");u.attr("href",t),p(),f=!0}}function g(){f&&e&&(s.hide(),f=!1)}l.on("mouseenter","a",(function(n){i&&clearTimeout(i),i=setTimeout((function(){var i=n.currentTarget,a=t(i);e=a;var o=a.children("img");o.length&&(o.click((function(e){g()})),o.hasClass("clicked"))||h()}),500)})).on("mouseleave","a",(function(e){a&&clearTimeout(a),a=setTimeout(g,500)})).on("click keydown scroll",(function(e){setTimeout(g,100)})),s.on("mouseenter",(function(e){a&&clearTimeout(a)})).on("mouseleave",(function(e){o&&clearTimeout(o),o=setTimeout(g,500)}))}))})),t((function(e,t){e.plugin((function(){var t=this,n=t.config.menuFixed;if(!1!==n&&"number"===typeof n){var i=parseFloat(e.$body.css("margin-top"),10);isNaN(i)&&(i=0);var a=t.$editorContainer,o=a.offset().top,r=a.outerHeight(),c=t.menuContainer.$menuContainer,l=c.css("position"),s=c.css("top"),d=c.offset().top,u=c.outerHeight();t.txt.$txt;e.$window.scroll((function(){if(!t.isFullScreen){var f=e.$window.scrollTop(),m=c.width();0===d&&(d=c.offset().top,o=a.offset().top,r=a.outerHeight(),u=c.outerHeight()),f>=d&&f+n+u+30<o+r?(c.css({position:"fixed",top:n}),c.width(m),e.$body.css({"margin-top":i+u}),t._isMenufixed||(t._isMenufixed=!0)):(c.css({position:l,top:s}),c.css("width","100%"),e.$body.css({"margin-top":i}),t._isMenufixed&&(t._isMenufixed=!1))}}))}}))})),t((function(e,t){e.createMenu((function(n){var i="indent";if(n(i)){var a=this,o=new e.Menu({editor:a,id:i,title:"缩进",$domNormal:t('<a href="#" tabindex="-1"><i class="wangeditor-menu-img-indent-left"></i></a>'),$domSelected:t('<a href="#" tabindex="-1" class="selected"><i class="wangeditor-menu-img-indent-left"></i></a>')});o.clickEvent=function(e){var n,i=a.getRangeElem(),o=a.getSelfOrParentByName(i,"p");if(!o)return e.preventDefault();function r(){n.css("text-indent","2em")}n=t(o),a.customCommand(e,r)},o.clickEventSelected=function(e){var n,i=a.getRangeElem(),o=a.getSelfOrParentByName(i,"p");if(!o)return e.preventDefault();function r(){n.css("text-indent","0")}n=t(o),a.customCommand(e,r)},o.updateSelectedEvent=function(){var e,n,i=a.getRangeElem(),o=a.getSelfOrParentByName(i,"p");return!!o&&(e=t(o),n=e.css("text-indent"),!(!n||"0px"===n))},a.menus[i]=o}}))})),t((function(e,t){e.createMenu((function(n){var i="lineheight";if(n(i)){var a=this;a.commandHooks.lineHeight=function(e){var n=a.getRangeElem(),i=a.getSelfOrParentByName(n,"p,h1,h2,h3,h4,h5,pre");i&&t(i).css("line-height",e+"")};var o=new e.Menu({editor:a,id:i,title:"行高",commandName:"lineHeight",$domNormal:t('<a href="#" tabindex="-1"><i class="wangeditor-menu-img-arrows-v"></i></a>'),$domSelected:t('<a href="#" tabindex="-1" class="selected"><i class="wangeditor-menu-img-arrows-v"></i></a>')}),r={"1.0":"1.0倍",1.5:"1.5倍",1.8:"1.8倍","2.0":"2.0倍",2.5:"2.5倍","3.0":"3.0倍"},c='<span style="line-height:{#commandValue}">{#title}</span>';o.dropList=new e.DropList(a,o,{data:r,tpl:c}),a.menus[i]=o}}))})),t((function(e,t){e.plugin((function(){var n=this,i=n.config.customUpload;if(i){if(n.config.uploadImgUrl)return alert("自定义上传无效，详看浏览器日志console.log"),void e.error("已经配置了 uploadImgUrl ，就不能再配置 customUpload ，两者冲突。将导致自定义上传无效。");var a=n.$uploadContent;a||e.error("自定义上传，无法获取 editor.$uploadContent");var o=t('<div class="upload-icon-container"><i class="wangeditor-menu-img-upload"></i></div>');a.append(o);var r="upload"+e.random(),c="upload"+e.random();o.attr("id",r),a.attr("id",c),n.customUploadBtnId=r,n.customUploadContainerId=c}}))})),t((function(e,t){e.info("本页面富文本编辑器由 wangEditor 提供 http://wangeditor.github.io/ ")})),window.wangEditor}alert("在引用wangEditor.js之前，先引用jQuery，否则无法使用 wangEditor")}))}}]);
(typeof window=='undefined'?global:window).tc_cfg_5582378869106086={"url":"css/theme-colors-a12f935f.css","colors":["#1b65b9","#3274c0","#4984c7","#5f93ce","#76a3d5","#8db2dc","#a4c1e3","#bbd1ea","#d1e0f1","#ebf4fa","#bbd9ed","#8dbce0","#639fd4","#3e82c7","#1b65b9","#0f4894","#052f6e","#001a47","#000b21","27,101,185"]};

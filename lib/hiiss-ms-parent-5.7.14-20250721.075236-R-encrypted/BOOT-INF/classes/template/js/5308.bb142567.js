"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[5308],{26634:function(e,t,a){a.r(t),a.d(t,{default:function(){return k}});var l=function(){var e=this,t=this,a=t.$createElement,l=t._self._c||a;return l("div",{staticClass:"fit"},[l("ta-border-layout",{staticStyle:{position:"relative"},attrs:{"show-padding":!1,layout:{footer:"0px"}}},[l("ta-card",{staticStyle:{height:"190px"},attrs:{bordered:!1}},[l("div",{staticClass:"title"},[t._v("查询条件")]),l("ta-form",{attrs:{autoFormCreate:function(t){e.searchForm=t}}},[l("ta-row",[l("ta-col",{attrs:{span:4}},[l("ta-form-item",{attrs:{label:"知识元",fieldDecoratorId:"knowmx"}},[l("ta-input",{attrs:{placeholder:"请输入"}})],1)],1),l("ta-col",{attrs:{span:4}},[l("ta-form-item",{attrs:{"field-decorator-id":"ape801",label:"规则大类"}},[l("ta-select",{attrs:{"show-search":!0,"allow-clear":!0,placeholder:"请选择"}},t._l(t.bigRuleList,(function(e,a){return l("ta-select-option",{key:a,attrs:{value:e.ape801}},[t._v(" "+t._s(e.aaa166)+" ")])})),1)],1)],1),l("ta-col",{attrs:{span:4}},[l("ta-form-item",{attrs:{"field-decorator-id":"ykz227",label:"险种"}},[l("ta-select",{attrs:{placeholder:"请选择","collection-type":"MYKZ227","show-search":!0,"allow-clear":!0}})],1)],1),l("ta-col",{attrs:{span:4}},[l("ta-form-item",{attrs:{label:"就医方式",fieldDecoratorId:"ykz248"}},[l("ta-select",{attrs:{placeholder:"请选择","collection-type":"MYKZ248","allow-clear":""}})],1)],1),l("ta-col",{attrs:{span:4}},[l("ta-form-item",{attrs:{label:"监控场景",fieldDecoratorId:"aae501"}},[l("ta-select",{attrs:{placeholder:"请选择",allowClear:!0,"show-search":!0}},t._l(t.aae501List,(function(e,a){return l("ta-select-option",{key:a,attrs:{value:e}},[t._v(" "+t._s(e)+" ")])})),1)],1)],1),l("ta-col",{attrs:{span:4}},[l("span",[t._v(" ")])])],1),l("ta-row",[l("ta-col",{attrs:{span:4}},[l("ta-form-item",{attrs:{label:"同步引擎",fieldDecoratorId:"needsync"}},[l("ta-select",{attrs:{"allow-clear":"",placeholder:"请选择"}},[l("ta-select-option",{attrs:{value:"1"}},[t._v(" 未同步 ")]),l("ta-select-option",{attrs:{value:"0"}},[t._v(" 已同步 ")])],1)],1)],1),l("ta-col",{attrs:{span:4}},[l("ta-form-item",{attrs:{label:"引擎号",fieldDecoratorId:"ruleid"}},[l("ta-select",{attrs:{placeholder:"请选择",allowClear:!0,"show-search":!0,dropdownMatchSelectWidth:!1}},t._l(t.ruleidList,(function(e,a){return l("ta-select-option",{key:a,attrs:{value:e.ruleid}},[t._v(" "+t._s(e.ruleid+":"+e.ruleidlog)+" ")])})),1)],1)],1),l("ta-col",{attrs:{span:4}},[l("ta-form-item",{attrs:{label:"三目编码",fieldDecoratorId:"ake001"}},[l("ta-input",{attrs:{placeholder:"请输入"}})],1)],1),l("ta-col",{attrs:{span:4}},[l("ta-form-item",{attrs:{label:"三目名称",fieldDecoratorId:"ake002"}},[l("ta-input",{attrs:{placeholder:"请输入"}})],1)],1),l("ta-col",{attrs:{span:4}},[l("ta-form-item",{attrs:{label:"有效标志",fieldDecoratorId:"aae100"}},[l("ta-select",{attrs:{placeholder:"请选择","allow-clear":!0,"collection-type":"EFFECTIVE"}})],1)],1),l("ta-col",{attrs:{span:4}},[l("ta-button",{staticStyle:{"margin-left":"10px"},on:{click:t.resetSearch}},[t._v("重置")]),l("ta-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary"},on:{click:t.doSearch}},[t._v("查询")])],1)],1)],1)],1),l("ta-card",{attrs:{bordered:!1}},[l("div",{staticClass:"title"},[t._v(" 规则配置列表 "),l("ta-dropdown",[l("ta-menu",{attrs:{slot:"overlay"},slot:"overlay"},[l("ta-menu-item",[l("ta-popconfirm",{attrs:{title:"确认启用?","cancel-text":"取消","ok-text":"确认"},on:{confirm:function(e){return t.disableOrEnable("1")}}},[l("ta-icon",{attrs:{type:"check-circle"}}),l("span",{staticClass:"mg-l12"},[t._v("启用")])],1)],1),l("ta-menu-divider"),l("ta-menu-item",[l("ta-popconfirm",{attrs:{title:"确认禁用?","cancel-text":"取消","ok-text":"确认"},on:{confirm:function(e){return t.disableOrEnable("0")}}},[l("ta-icon",{attrs:{type:"stop"}}),l("span",{staticClass:"mg-l12"},[t._v("禁用")])],1)],1)],1),l("ta-button",{staticStyle:{float:"right","margin-right":"10px"}},[t._v(" 批量操作 "),l("ta-icon",{attrs:{type:"down"}})],1)],1),l("ta-popconfirm",{attrs:{title:"确定生成确认清单?"},on:{confirm:t.generateConfirmList}},[l("ta-button",{staticStyle:{float:"right"},attrs:{type:"primary"}},[t._v("生成确认清单")])],1),l("ta-button",{staticStyle:{float:"right"},attrs:{type:""},on:{click:function(){e.blnSqlImportVisible=!0,e.fileList=[]}}},[t._v("导入基板批次")])],1),l("ta-big-table",{ref:"ruleTable",attrs:{size:"small",data:t.ruleList,"big-data-checkbox":"","header-drag-style":"",border:"full","use-virtual":"",autoResize:"",resizable:"","row-height":40,height:380,"row-style":t.fnCustomRow}},[l("ta-big-table-column",{attrs:{type:"checkbox",width:"55"}}),l("ta-big-table-column",{attrs:{type:"seq",width:"60",title:"序号",fixed:""}}),t._l(t.columns,(function(e,a){return l("ta-big-table-column",{key:a,attrs:{align:e.align,border:"full",resizable:e.resizable,"show-overflow":e.overflowTooltip,field:e.dataIndex,title:e.title,fixed:e.fixed,width:e.width,collectionType:e.collectionType},scopedSlots:t._u([e.scopedSlots?{key:"default",fn:function(a){var o=a.row;return["needsync"==e.scopedSlots.customRender?["1"==o.needsync?l("ta-tag",{attrs:{color:"red"}},[t._v(" 未同步 ")]):l("ta-tag",{attrs:{color:"green"}},[t._v(" 已同步 ")])]:t._e(),"operation"==e.scopedSlots.customRender?l("div",["2"!=o.ykz167?l("a",{staticStyle:{"margin-left":"5px"},on:{click:function(e){return t.goDetail(o)}}},[t._v("详情")]):t._e(),"1"==o.aae100?l("ta-popconfirm",{attrs:{title:"确定禁用?"},on:{confirm:function(e){return t.disableOrEnableSingle(o,"0")}}},[l("a",{staticStyle:{"margin-left":"5px"},on:{click:function(e){}}},[t._v("禁用")])]):l("ta-popconfirm",{attrs:{title:"确定启用?"},on:{confirm:function(e){return t.disableOrEnableSingle(o,"1")}}},[l("a",{staticStyle:{"margin-left":"5px"},on:{click:function(e){}}},[t._v("启用")])]),l("a",{staticStyle:{"margin-left":"5px"},on:{click:function(e){return t.openLog(o)}}},[t._v("日志")])],1):t._e()]}}:null],null,!0)})}))],2),l("div",[l("ta-pagination",{ref:"rulePager",staticClass:"page",attrs:{dataSource:t.ruleList,params:t.pageParams,url:"/mtt/localruleconfig/ruleQuery/queryRulePage",defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"]},on:{"update:dataSource":function(e){t.ruleList=e},"update:data-source":function(e){t.ruleList=e}}})],1)],1)],1),l("ta-modal",{attrs:{"destroy-on-close":!0,title:"1"==t.ruleEnableStatus?"启用":"停用"},model:{value:t.blnRuleReasonVisible,callback:function(e){t.blnRuleReasonVisible=e},expression:"blnRuleReasonVisible"}},[l("span",{staticStyle:{color:"red"}},[t._v("*")]),t._v(t._s("1"==t.ruleEnableStatus?"启用":"停用")+"意见："),l("ta-textarea",{attrs:{rows:4,placeholder:"请输入"},model:{value:t.descValue,callback:function(e){t.descValue=e},expression:"descValue"}}),l("div",{attrs:{slot:"footer"},slot:"footer"},[l("ta-button",{attrs:{type:"primary"},on:{click:t.doDisableOrEnable}},[t._v("确定")]),l("ta-button",{on:{click:function(e){t.blnRuleReasonVisible=!1}}},[t._v("取消")])],1)],1),l("ta-modal",{attrs:{width:1e3,height:800,title:"操作日志","destroy-on-close":!0},on:{ok:function(e){t.blnRuleLogVisible=!1}},model:{value:t.blnRuleLogVisible,callback:function(e){t.blnRuleLogVisible=e},expression:"blnRuleLogVisible"}},[l("rule-log",{attrs:{record:t.ruleRecord}})],1),l("ta-modal",{attrs:{width:1400,title:"规则详情","destroy-on-close":!0},on:{ok:function(e){t.blnRuleItemVisible=!1}},model:{value:t.blnRuleItemVisible,callback:function(e){t.blnRuleItemVisible=e},expression:"blnRuleItemVisible"}},[l("rule-item-config",{attrs:{record:t.ruleRecord}})],1),l("ta-modal",{attrs:{width:1400,title:"添加三目","destroy-on-close":!0},on:{ok:function(e){t.blnAddItemVisible=!1}},model:{value:t.blnAddItemVisible,callback:function(e){t.blnAddItemVisible=e},expression:"blnAddItemVisible"}},[l("add-item",{attrs:{record:t.ruleRecord}})],1),l("ta-modal",{attrs:{title:"上传数据",visible:t.blnSqlImportVisible,"destroy-on-close":"",width:"550px",height:"150px",destroyOnClose:""},on:{ok:t.handleImport,cancel:function(e){t.blnSqlImportVisible=!1}}},[l("ta-form",{attrs:{autoFormCreate:function(t){e.form=t},"label-width":"120px"}},[l("ta-form-item",{attrs:{label:"选择文件",fieldDecoratorId:"file",require:{message:"请选择文件!"}}},[l("ta-upload",{attrs:{"file-list":t.fileList,"before-upload":t.beforeUpload,remove:t.handleRemove}},[l("ta-button",[t._v("上传加密的zip文件(mtt.zip)")])],1)],1)],1)],1)],1)},o=[],i=a(95082),r=a(89584),s=a(37585),n=function(){var e=this,t=this,a=t.$createElement,l=t._self._c||a;return l("div",[l("div",{staticClass:"title"},[t._v("规则条目")]),l("ta-button-group",[t.record.hasHideNode||"1"!=t.record.aae100?t._e():l("ta-button",{on:{click:t.openAddItem}},[t._v("添加")]),t.record.hasHideNode||"1"!=t.record.aae100?t._e():l("ta-button",{on:{click:t.downloadTemplate}},[t._v("模板下载")]),t.record.hasHideNode||"1"!=t.record.aae100?t._e():l("ta-button",{on:{click:t.openUpdatebatchForm}},[t._v("批量维护")]),t.record.hasHideNode||"1"!=t.record.aae100?t._e():l("ta-button",{on:{click:function(e){return t.openImport()}}},[t._v("导入")])],1),l("div",{staticStyle:{float:"right"}},[t._v(" 关键字: "),l("ta-input",{staticStyle:{width:"200px","margin-right":"10px"},attrs:{placeholder:"请输入三目名称或编码"},model:{value:t.searchParam.keyword,callback:function(e){t.$set(t.searchParam,"keyword",e)},expression:"searchParam.keyword"}}),t._v(" 有效标志: "),l("ta-select",{staticStyle:{width:"100px","margin-right":"10px"},attrs:{placeholder:"请选择","collection-type":"EFFECTIVE","allow-clear":!0},model:{value:t.searchParam.aae100,callback:function(e){t.$set(t.searchParam,"aae100",e)},expression:"searchParam.aae100"}}),l("ta-button",{attrs:{type:"primary"},on:{click:t.doSearch}},[t._v("查询")])],1),l("div",{staticStyle:{clear:"both","margin-bottom":"10px"}}),t.record.hasHideNode||"1"!=t.record.aae100?t._e():l("ta-dropdown",[l("ta-menu",{attrs:{slot:"overlay"},slot:"overlay"},[l("ta-menu-item",[l("ta-popconfirm",{attrs:{title:"确认启用?","cancel-text":"取消","ok-text":"确认"},on:{confirm:function(e){return t.disableOrEnable("1")}}},[l("ta-icon",{attrs:{type:"check-circle"}}),l("span",{staticClass:"mg-l12"},[t._v("启用")])],1)],1),l("ta-menu-divider"),l("ta-menu-item",[l("ta-popconfirm",{attrs:{title:"确认禁用?","cancel-text":"取消","ok-text":"确认"},on:{confirm:function(e){return t.disableOrEnable("0")}}},[l("ta-icon",{attrs:{type:"stop"}}),l("span",{staticClass:"mg-l12"},[t._v("禁用")])],1)],1)],1),l("ta-button",{staticStyle:{float:"right","margin-right":"10px"}},[t._v(" 批量操作 "),l("ta-icon",{attrs:{type:"down"}})],1)],1),l("div",{staticStyle:{clear:"both","margin-bottom":"10px"}}),l("ta-big-table",{ref:"table",attrs:{size:"small",data:t.itemList,"big-data-checkbox":"","header-drag-style":"",border:"full","use-virtual":"",autoResize:"","row-height":40,height:380,"row-style":t.fnCustomRow}},[l("ta-big-table-column",{attrs:{type:"checkbox",width:"55"}}),l("ta-big-table-column",{attrs:{type:"seq",width:"60",title:"序号",fixed:""}}),t._l(t.itemColumns,(function(e,a){return l("ta-big-table-column",{key:a,attrs:{align:e.align,border:"full",resizable:e.resizable,"show-overflow":e.overflowTooltip,field:e.dataIndex,title:e.title,fixed:e.fixed,width:e.width,collectionType:e.collectionType},scopedSlots:t._u(["operation"==e.dataIndex?{key:"default",fn:function(e){var a=e.row;return["1"==a.aae100?l("ta-popconfirm",{attrs:{title:"确定禁用?"},on:{confirm:function(e){return t.disableOrEnableSingle(a,"0")}}},[t.record.hasHideNode||"1"!=t.record.aae100?t._e():l("a",{staticStyle:{"margin-left":"5px"}},[t._v("禁用")])]):l("ta-popconfirm",{attrs:{title:"确定启用?"},on:{confirm:function(e){return t.disableOrEnableSingle(a,"1")}}},[t.record.hasHideNode||"1"!=t.record.aae100?t._e():l("a",{staticStyle:{"margin-left":"5px"}},[t._v("启用")])]),l("a",{staticStyle:{"margin-left":"5px"},on:{click:function(e){return t.openLog(a)}}},[t._v("日志")])]}}:null],null,!0)})}))],2),l("div",[l("ta-pagination",{ref:"pager",staticClass:"page",attrs:{dataSource:t.itemList,params:t.pageParams,url:"mtt/localruleconfig/ruleQuery/queryRuleTheTernaryPage",defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"]},on:{"update:dataSource":function(e){t.itemList=e},"update:data-source":function(e){t.itemList=e}}})],1),l("ta-modal",{attrs:{"destroy-on-close":!0,title:"1"==t.ruleEnableStatus?"启用":"停用"},model:{value:t.blnRuleReasonVisible,callback:function(e){t.blnRuleReasonVisible=e},expression:"blnRuleReasonVisible"}},[l("span",{staticStyle:{color:"red"}},[t._v("*")]),t._v(t._s("1"==t.ruleEnableStatus?"启用":"停用")+"意见： "),l("ta-textarea",{attrs:{rows:4,placeholder:"请输入"},model:{value:t.descValue,callback:function(e){t.descValue=e},expression:"descValue"}}),l("div",{attrs:{slot:"footer"},slot:"footer"},[l("ta-button",{attrs:{type:"primary"},on:{click:t.doDisableOrEnable}},[t._v("确定")]),l("ta-button",{on:{click:function(e){t.blnRuleReasonVisible=!1}}},[t._v("取消")])],1)],1),l("ta-modal",{attrs:{width:1e3,height:800,title:"操作日志","destroy-on-close":!0},on:{ok:function(e){t.blnRuleLogVisible=!1}},model:{value:t.blnRuleLogVisible,callback:function(e){t.blnRuleLogVisible=e},expression:"blnRuleLogVisible"}},[l("rule-item-log",{attrs:{record:t.itemRecord}})],1),l("ta-modal",{attrs:{width:1400,title:"添加三目","destroy-on-close":!0,footer:null},on:{ok:function(e){t.blnAddItemVisible=!1}},model:{value:t.blnAddItemVisible,callback:function(e){t.blnAddItemVisible=e},expression:"blnAddItemVisible"}},[l("add-item",{attrs:{record:t.record},on:{onOk:t.onAddItem}})],1),l("ta-modal",{attrs:{title:"上传数据",visible:t.blnItemImportVisible,"destroy-on-close":"",width:"500px",height:"150px",destroyOnClose:""},on:{ok:t.handleImport,cancel:function(e){t.blnItemImportVisible=!1}}},[l("ta-form",{attrs:{autoFormCreate:function(t){e.form=t},"label-width":"120px"}},[l("ta-form-item",{attrs:{label:"选择文件",fieldDecoratorId:"file",require:{message:"请选择文件!"}}},[l("ta-upload",{attrs:{"file-list":t.fileList,"before-upload":t.beforeUpload,remove:t.handleRemove}},[l("ta-button",[t._v("上传excel文件")])],1)],1)],1),l("addImport",{ref:"addImport",attrs:{visible:t.addImportVisible,dataType:t.dataType,"result-data":t.resultData},on:{close:t.closeAddImportModel}})],1),l("ta-modal",{attrs:{"destroy-on-close":!0,title:"修改意见"},model:{value:t.blnUpdateReasonVisible,callback:function(e){t.blnUpdateReasonVisible=e},expression:"blnUpdateReasonVisible"}},[l("span",{staticStyle:{color:"red"}},[t._v("*")]),t._v("修改意见："),l("ta-textarea",{attrs:{rows:4,placeholder:"请输入"},model:{value:t.descValue,callback:function(e){t.descValue=e},expression:"descValue"}}),l("div",{attrs:{slot:"footer"},slot:"footer"},[l("ta-button",{attrs:{type:"primary"},on:{click:t.changeMkf17Data}},[t._v("确定")]),l("ta-button",{on:{click:function(e){t.blnUpdateReasonVisible=!1}}},[t._v("取消")])],1)],1),l("ta-modal",{attrs:{"destroy-on-close":!0,title:"批量维护"},model:{value:t.blnBatchUpdateVisible,callback:function(e){t.blnBatchUpdateVisible=e},expression:"blnBatchUpdateVisible"}},[l("ta-form",{attrs:{autoFormCreate:function(t){e.itemChangeForm=t}}},[l("ta-row",[l("ta-col",{attrs:{span:this.highFormLayout.checkboxSpan}},[l("ta-form-item",{attrs:{fieldDecoratorId:"ckApe864",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperClo}},[l("ta-checkbox")],1)],1),l("ta-col",{attrs:{span:this.highFormLayout.firstSpan}},[l("ta-form-item",{attrs:{fieldDecoratorId:"ape864",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperClo}},[l("ta-input",{staticClass:"changeInput",attrs:{placeholder:"分组编号"}})],1)],1)],1),l("ta-row",[l("ta-col",{attrs:{span:this.highFormLayout.checkboxSpan}},[l("ta-form-item",{attrs:{fieldDecoratorId:"ckApe865",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperClo}},[l("ta-checkbox")],1)],1),l("ta-col",{attrs:{span:this.highFormLayout.firstSpan}},[l("ta-form-item",{attrs:{fieldDecoratorId:"ape865",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperClo}},[l("ta-input",{staticClass:"changeInput",attrs:{placeholder:"分组名称"}})],1)],1)],1),l("ta-row",[l("ta-col",{attrs:{span:this.highFormLayout.checkboxSpan}},[l("ta-form-item",{attrs:{fieldDecoratorId:"ckApe891",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperClo}},[l("ta-checkbox")],1)],1),l("ta-col",{attrs:{span:this.highFormLayout.firstSpan}},[l("ta-form-item",{attrs:{fieldDecoratorId:"ape891",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperClo}},[l("ta-input-number",{staticClass:"changeInput",attrs:{min:0,max:99,step:.1,placeholder:"频次倍数(乘法)"}})],1)],1)],1),l("ta-row",[l("ta-col",{attrs:{span:this.highFormLayout.checkboxSpan}},[l("ta-form-item",{attrs:{fieldDecoratorId:"ckApe892",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperClo}},[l("ta-checkbox")],1)],1),l("ta-col",{attrs:{span:this.highFormLayout.firstSpan}},[l("ta-form-item",{attrs:{fieldDecoratorId:"ape892",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperClo}},[l("ta-input-number",{staticClass:"changeInput",attrs:{min:0,max:99,step:.1,placeholder:"频次倍数(除法)"}})],1)],1)],1)],1),l("div",{attrs:{slot:"footer"},slot:"footer"},[l("ta-button",{attrs:{type:"primary"},on:{click:t.changeItem}},[t._v("确认")])],1)],1)],1)},c=[],d=a(66347),u=a(43658),p=a(1352),m=a(72049),f={name:"ruleItemConfig",components:{ruleItemLog:m.Z,addItem:p.Z,addImport:u.Z},props:{record:Object},data:function(){return{highFormLayout:{colSpan:8,firstSpan:20,checkboxSpan:4},formItemLayout:{labelCol:{span:6},wrapperClo:{span:18}},blnChangeItemVisible:!1,searchParam:{keyword:void 0,aae100:void 0},itemList:[{ake001:"1",ake002:"2"}],itemColumns:[{title:"三目编码",dataIndex:"ake001",width:200,overflowTooltip:!0},{title:"三目名称",dataIndex:"ake002",width:200,overflowTooltip:!0},{title:"分组编码",dataIndex:"ape864",width:150,overflowTooltip:!0},{title:"分组名称",dataIndex:"ape865",width:100,overflowTooltip:!0},{title:"频次（乘法）",dataIndex:"ape891",width:150,overflowTooltip:!0},{title:"频次（除法）",dataIndex:"ape892",width:150,overflowTooltip:!0},{title:"计价单位",dataIndex:"ake126",width:100,overflowTooltip:!0},{title:"操作",fixed:"right",dataIndex:"operation",width:100,overflowTooltip:!0}],blnRuleReasonVisible:!1,ruleEnableStatus:"1",descValue:void 0,blnRuleLogVisible:!1,itemRecord:{},itemSelected:[],blnAddItemVisible:!1,blnItemImportVisible:!1,fileList:[],addImportVisible:!1,dataType:"result",resultData:{},blnUpdateReasonVisible:!1,changeForm:void 0,blnBatchUpdateVisible:!1}},mounted:function(){this.doSearch()},methods:{fnCustomRow:function(e){var t={};return"0"==e.row.aae100&&(t.backgroundColor="#fcecec"),t},closeAddImportModel:function(e){this.addImportVisible=!1,e&&(this.handleCancelImport(e),this.doSearch())},handleCancelImport:function(e){this.fileList=[]},openImport:function(){this.blnItemImportVisible=!0,this.fileList=[]},handleImport:function(){var e=this;if(this.fileList.length<=0)this.$message.error("请先选择文件");else{var t="/mtt/localruleconfig/ruleQuery/importIncrementFile";this.Base.submit(null,{url:t,data:{file:this.fileList[0],ykz277:this.record.ykz277},isFormData:!0}).then((function(t){e.resultData=t.data.data,e.resultData.errorNum>0?e.dataType="error":0==e.resultData.errorNum&&(e.dataType="result");var a,l=(0,d.Z)(e.resultData.successList);try{for(l.s();!(a=l.n()).done;){var o=a.value;o.ykz277=e.record.ykz277}}catch(i){l.e(i)}finally{l.f()}e.addImportVisible=!0})).catch((function(t){e.$message.info("上传失败"),e.addImportVisible=!1}))}},beforeUpload:function(e){return this.fileList=[],this.fileList=[].concat((0,r.Z)(this.fileList),[e]),!1},handleRemove:function(e){var t=this.fileList.indexOf(e),a=this.fileList.slice();a.splice(t,1),this.fileList=a},openAddItem:function(){this.blnAddItemVisible=!0},downloadTemplate:function(){var e=faceConfig.basePath;location.href=e+"/mtt/localruleconfig/ruleQuery/exportTheTernaryExcel"},doSearch:function(){this.$refs.pager.loadData()},pageParams:function(){return(0,i.Z)((0,i.Z)({},this.searchParam),{},{ykz277:this.record.ykz277})},openLog:function(e){this.itemRecord=e,this.blnRuleLogVisible=!0},openUpdatebatchForm:function(){this.itemSelected=this.$refs.table.getCheckboxRecords(),0!==this.itemSelected.length?this.blnBatchUpdateVisible=!0:this.$message.info("请先选择数据")},changeItem:function(){var e=this;if(this.itemSelected=this.$refs.table.getCheckboxRecords(),0!==this.itemSelected.length){var t,a=(0,d.Z)(this.itemSelected);try{for(a.s();!(t=a.n()).done;){var l=t.value;if("1"!=l.aae100)return void this.$message.warn("勾选了未启用的明细记录，不允许修改，请勾选有效记录")}}catch(i){a.e(i)}finally{a.f()}var o=this.itemChangeForm.getFieldsValue();void 0!==o.ape864&&null!==o.ape864&&(o.ape864=o.ape864.replace(/\s/g,"")),void 0!==o.ape865&&null!==o.ape865&&(o.ape865=o.ape865.replace(/\s/g,"")),this.itemChangeForm.validateFields((function(){!o.ckApe864||void 0!==o.ape864&&null!==o.ape864&&""!==o.ape864?!o.ckApe865||void 0!==o.ape865&&null!==o.ape865&&""!==o.ape865?!o.ckApe891||void 0!==o.ape891&&null!==o.ape891&&""!==o.ape891?!o.ckApe892||void 0!==o.ape892&&null!==o.ape892&&""!==o.ape892?(e.changeForm=o,e.descValue=void 0,e.blnUpdateReasonVisible=!0):e.$message.warn("频次倍速(除法)不能为空"):e.$message.warn("频次倍速(乘法)不能为空"):e.$message.warn("分组名称不能为空"):e.$message.warn("分组编号不能为空")}))}else this.$message.info("请先选择数据")},changeMkf17Data:function(){var e=this,t=this.changeForm;if(this.descValue&&""!=this.descValue.trim()){for(var a=[],l=0;l<this.itemSelected.length;l++){var o={};Object.assign(o,this.itemSelected[l]),t.ckApe864&&(o.ape864=t.ape864),t.ckApe865&&(o.ape865=t.ape865),t.ckApe891&&(o.ape891=t.ape891),t.ckApe892&&(o.ape892=t.ape892),o.log=this.descValue,a.push(o)}Base.submit(null,{url:"mtt/localruleconfig/ruleQuery/batchMaintenance",data:{dtoStr:encodeURIComponent(JSON.stringify(a))}}).then((function(t){e.$message.success("操作成功"),e.blnChangeItemVisible=!1,e.blnUpdateReasonVisible=!1,e.blnBatchUpdateVisible=!1,e.doSearch()}))}else this.$message.info("请输入意见")},disableOrEnable:function(e){this.descValue=void 0;var t=this.$refs.table.getCheckboxRecords();0!=t.length?(this.selectRecords=t,this.ruleEnableStatus=e,this.blnRuleReasonVisible=!0):this.$message.warn("请选择至少一条进行启停")},disableOrEnableSingle:function(e,t){this.descValue=void 0;var a=[e];this.selectRecords=a,this.ruleEnableStatus=t,this.blnRuleReasonVisible=!0},doDisableOrEnable:function(){var e=this;if(this.descValue){var t="mtt/localruleconfig/ruleQuery/closeTernaryContrastByIds";"1"==this.ruleEnableStatus&&(t="mtt/localruleconfig/ruleQuery/openTernaryContrastByIds"),Base.submit(null,{url:t,data:{codes:this.selectRecords.map((function(e){return e.ake001})).join(","),ykz277:this.record.ykz277,note:this.descValue}}).then((function(t){e.$message.success("操作成功"),e.blnRuleReasonVisible=!1,e.doSearch()}))}else this.$message.info("请输入意见")},onPopoverShow:function(){this.itemChangeForm.resetFields(),this.itemChangeForm.setFieldsValue({ckApe864:void 0,ckApe865:void 0,ckApe891:void 0,ckApe892:void 0})},onAddItem:function(e){var t=this;Base.submit(null,{url:"mtt/localruleconfig/ruleQuery/addTheTernaryContrastList",data:{ykz277:e[0].ykz277,theTernaryList:e},autoQs:!1}).then((function(e){t.$message.success("添加成功"),t.blnAddItemVisible=!1,t.doSearch()}))}}},h=f,b=a(1001),g=(0,b.Z)(h,n,c,!1,null,"4f657853",null),v=g.exports,y={name:"localRuleSearch",components:{ruleLog:s.Z,ruleItemConfig:v,addItem:p.Z},data:function(){return{bigRuleList:[],aae501List:[],ruleList:[],ruleidList:[],columns:[{title:"规则id",dataIndex:"ykz276",width:70,overflowTooltip:!0},{title:"知识元",dataIndex:"knowmx",overflowTooltip:!0},{title:"规则大类",dataIndex:"aaa166",width:100,overflowTooltip:!0},{title:"险种",dataIndex:"ykz227",collectionType:"MYKZ227",width:100,overflowTooltip:!0},{title:"就医方式",dataIndex:"ykz248",collectionType:"MYKZ248",width:100,overflowTooltip:!0},{title:"监控场景",dataIndex:"aae501",width:150,overflowTooltip:!0},{title:"引擎号",dataIndex:"ruleidlog",width:200,overflowTooltip:!0},{title:"有效标志",dataIndex:"aae100",collectionType:"EFFECTIVE",width:100,overflowTooltip:!0},{title:"同步引擎",dataIndex:"needsync",scopedSlots:{customRender:"needsync"},width:80,overflowTooltip:!0},{title:"操作",align:"center",width:240,scopedSlots:{customRender:"operation"}}],selectRecords:[],blnRuleReasonVisible:!1,ruleEnableStatus:"1",descValue:void 0,blnRuleLogVisible:!1,ruleRecord:{},blnRuleItemVisible:!1,blnAddItemVisible:!1,fileList:[],blnSqlImportVisible:!1}},watch:{blnRuleItemVisible:function(e){0==e&&this.doSearchCurrentPage()}},mounted:function(){this.queryBigRule(),this.queryAae501List(),this.listRuleid(),this.doSearch()},methods:{handleImport:function(){var e=this;if(this.fileList.length<=0)this.$message.error("请先选择文件");else{var t="/mtt/localruleconfig/ruleUserCommit/doExecuteSqlFile";this.Base.submit(null,{url:t,data:{file:this.fileList[0]},isFormData:!0}).then((function(t){e.$message.success("导入批次成功"),e.blnSqlImportVisible=!1}))}},beforeUpload:function(e){return this.fileList=[],this.fileList=[].concat((0,r.Z)(this.fileList),[e]),!1},handleRemove:function(e){var t=this.fileList.indexOf(e),a=this.fileList.slice();a.splice(t,1),this.fileList=a},doSearch:function(){this.$refs.rulePager.loadData()},doSearchCurrentPage:function(){var e=this,t=this.$refs.rulePager.getPagerInfo(),a=this.searchForm.getFieldsValue();Base.submit(null,{url:"/mtt/localruleconfig/ruleQuery/queryRulePage",data:(0,i.Z)((0,i.Z)({},a),{},{pageNumber:t.current,pageSize:t.pageSize})}).then((function(t){e.ruleList=t.data.pageBean.list}))},resetSearch:function(){this.searchForm.resetFields()},queryAae501List:function(){var e=this;Base.submit(null,{url:"mtt/localruleconfig/ruleQuery/getAae501List"}).then((function(t){e.aae501List=t.data.list}))},queryBigRule:function(){var e=this;Base.submit(null,{url:"mttRuleConfig/listBigRule"}).then((function(t){e.bigRuleList=t.data.list}))},listRuleid:function(){var e=this;Base.submit(null,{url:"mtt/localruleconfig/ruleQuery/getRuleidList"}).then((function(t){e.ruleidList=t.data.list}))},pageParams:function(){var e=this.searchForm.getFieldsValue();return(0,i.Z)({},e)},disableOrEnable:function(e){this.descValue=void 0;var t=this.$refs.ruleTable.getCheckboxRecords();0!=t.length?(this.selectRecords=t,this.ruleEnableStatus=e,this.blnRuleReasonVisible=!0):this.$message.warn("请选择至少一条进行启停")},disableOrEnableSingle:function(e,t){this.descValue=void 0;var a=[e];this.selectRecords=a,this.ruleEnableStatus=t,this.blnRuleReasonVisible=!0},doDisableOrEnable:function(){var e=this;if(this.descValue){var t="mtt/localruleconfig/ruleQuery/openByIds";"0"==this.ruleEnableStatus&&(t="mtt/localruleconfig/ruleQuery/closeByIds"),Base.submit(null,{url:t,data:{ids:this.selectRecords.map((function(e){return e.ykz276})).join(","),note:this.descValue}}).then((function(t){e.$message.success("操作成功"),e.blnRuleReasonVisible=!1,e.doSearchCurrentPage()}))}else this.$message.info("请输入意见")},goDetail:function(e){this.ruleRecord=e,this.blnRuleItemVisible=!0},openLog:function(e){this.ruleRecord=e,this.blnRuleLogVisible=!0},generateConfirmList:function(){var e=this;Base.submit(null,{url:"mtt/localruleconfig/ruleQuery/generateConfirmList",data:{}}).then((function(t){e.$message.success("生成成功")}))},fnCustomRow:function(e){var t={};return"0"==e.row.aae100&&(t.backgroundColor="#fcecec"),t}}},w=y,I=(0,b.Z)(w,l,o,!1,null,"7030e4b9",null),k=I.exports}}]);
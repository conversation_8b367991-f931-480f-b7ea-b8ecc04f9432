(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[3022],{88412:function(t,e,a){"use strict";var r=a(26263),n=a(36766),o=a(1001),i=(0,o.Z)(n.Z,r.s,r.x,!1,null,"5e7ef0ae",null);e["Z"]=i.exports},5872:function(t,e,a){"use strict";a.r(e),a.d(e,{default:function(){return b}});var r=function(){var t=this,e=this,a=e.$createElement,r=e._self._c||a;return r("div",{staticClass:"fit"},[r("ta-border-layout",{attrs:{layout:{header:"110px",footer:"0px"},showPadding:!0,"footer-cfg":{showBorder:!1}}},[r("div",{attrs:{slot:"header"},slot:"header"},[r("ta-title",{attrs:{title:"查询条件"}}),r("ta-form",{staticStyle:{"padding-top":"5px"},attrs:{autoFormCreate:function(e){t.baseInfoForm=e},layout:"horizontal",formLayout:!0}},[r("ta-form-item",{attrs:{span:5,fieldDecoratorId:"allDate",fieldDecoratorOptions:{rules:[{required:!0,message:"必须选取日期"}]},required:!0,"init-value":e.rangeValue,labelCol:{span:6},wrapperCol:{span:18},label:"出院日期"}},[r("ta-range-picker",{attrs:{"allow-one":!0},on:{change:e.onChange}})],1),r("ta-button",{staticStyle:{float:"right"},attrs:{type:"primary",icon:"search"},on:{click:e.fnQuery}},[e._v("查询 ")])],1)],1),r("div",{staticClass:"fit",staticStyle:{height:"89%"}},[r("ta-title",{staticStyle:{width:"50%"},attrs:{title:"查询结果"}}),r("ta-big-table",{ref:"Table",attrs:{height:"100%","show-footer":"","footer-method":e.footerMethod,"highlight-hover-row":"","highlight-current-row":"","tooltip-config":{enabled:!0,contentMethod:e.showTooltipMethod},"auto-resize":"","show-overflow":"",resizable:"","empty-text":"-",border:"",data:e.dataList},on:{"cell-click":e.cellClickEvent}},[r("ta-big-table-column",{attrs:{type:"seq","header-align":"center",align:"center",title:"序号",width:"50",sortable:""}}),r("ta-big-table-column",{attrs:{sortable:"",field:"aae386","header-align":"center",align:"left",title:"科室名称","min-width":"120"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[r("ta-button",{attrs:{size:"small",type:"link"}},[r("span",{staticStyle:{color:"#00bcff","text-decoration":"underline"}},[e._v(e._s(a.aae386))])])]}}])}),r("ta-big-table-column",{attrs:{sortable:"",field:"wgzrc","header-align":"center",align:"right",title:"异常人数(人次)","min-width":"180"}}),r("ta-big-table-column",{attrs:{sortable:"",field:"shzrc","header-align":"center",align:"right",title:"总人数(人次)","min-width":"120"}}),r("ta-big-table-column",{attrs:{sortable:"",field:"hgl","header-align":"center",align:"right",formatter:e.ratioFormat,title:"合格率(%)","min-width":"100"}}),r("ta-big-table-column",{attrs:{sortable:"",field:"wgl","header-align":"center",align:"right",formatter:e.ratioFormat,title:"异常率(%)","min-width":"100"}}),r("ta-big-table-column",{attrs:{sortable:"",field:"ycrc","header-align":"center",align:"right",title:"异常人次(人次)","min-width":"180"}}),r("ta-big-table-column",{attrs:{sortable:"",field:"ycje","header-align":"center",align:"right",formatter:e.moneyFormat,title:"异常金额(元)","min-width":"180"}}),r("template",{slot:"bottomBar"},[r("ta-button",{staticStyle:{float:"right","margin-top":"20px"},attrs:{icon:"download",type:"primary"},on:{click:e.exportTable}},[e._v("导出")]),r("ta-pagination",{ref:"gridPager",staticStyle:{float:"right","margin-top":"20px","margin-right":"10px"},attrs:{defaultPageSize:100,pageSizeOptions:["100","200","500","1000","1500"],"data-source":e.dataList,params:e.infoPageParams,url:"NurdscgStat/queryDscgExcCostKSPage"},on:{"update:dataSource":function(t){e.dataList=t},"update:data-source":function(t){e.dataList=t}}})],1)],2)],1)])],1)},n=[],o=a(66347),i=a(95082),l=a(88412),s=a(36797),c=a.n(s),u=a(22722),f=a(55115),h=a(88097),d=a(92566);a(55192);f.w3.prototype.Base=Object.assign(f.w3.prototype.Base,(0,i.Z)({},u.Z));var g={name:"dscgExcCostKS",components:{TaTitle:l.Z},data:function(){return{dataList:[],rangeValue:[this.Base.getMoment((new Date).toISOString().slice(0,8)+"-01","YYYY-MM-DD"),this.Base.getMoment((new Date).toISOString().slice(0,10),"YYYY-MM-DD")]}},mounted:function(){var t=this;this.$nextTick((function(){t.fnQuery()}))},methods:{moment:c(),onChange:function(t,e){this.fnQuery()},footerMethod:function(t){var e=t.columns,a=t.data;return[e.map((function(t,e){return 0===e?"合计":["aae386"].includes(t.property)?null:["hgl"].includes(t.property)?0===(0,d.Z)(a,"shzrc")||0===(0,d.Z)(a,"wgzrc")?0:(100-parseFloat(0===(0,d.Z)(a,"shzrc")?0:(0,d.Z)(a,"wgzrc")/(0,d.Z)(a,"shzrc")*100)).toFixed(2)+"%":["wgl"].includes(t.property)?0===(0,d.Z)(a,"shzrc")||0===(0,d.Z)(a,"wgzrc")?0:parseFloat(0===(0,d.Z)(a,"shzrc")?0:(0,d.Z)(a,"wgzrc")/(0,d.Z)(a,"shzrc")*100).toFixed(2)+"%":(0,d.Z)(a,t.property)}))]},showTooltipMethod:function(t){var e=t.type,a=t.column,r=(t.row,t.items,t._columnIndex,a.property);return"header"===e&&h.Z.props[r]?h.Z.props[r][e]:""},cellClickEvent:function(t){var e=t.row,a=t.column,r=a.property;if("aae386"===r&&e.aaz307){var n=this.baseInfoForm.getFieldsValue();this.Base.openTabMenu({id:Math.floor(90*Math.random())+10,name:"【"+e.aae386+"】护士站出院科室异常收费汇总",url:"nurdscgStat.html#/dscgExcCostGZ?aaz307=".concat(e.aaz307,"&wgzrc=").concat(e.wgzrc,"&ycje=").concat(e.ycje,"&ycrc=").concat(e.ycrc,"&shzrc=").concat(e.shzrc,"&params=").concat(JSON.stringify(n)),refresh:!1})}},exportTable:function(){var t=this.infoPageParams(),e=t.allDate;if(!e||e[0]&&e[1]){var a,r=[],n=this.$refs.Table.getColumns(),i=(0,o.Z)(n);try{for(i.s();!(a=i.n()).done;){var l=a.value;"序号"!==l.title&&r.push({header:l.title,key:l.property,width:20})}}catch(c){i.e(c)}finally{i.f()}var s={fileName:"护士站出院科室异常收费汇总结果表",sheets:[{name:"worksheet1",column:{complex:!1,columns:r},rows:this.dataList,codeList:h.Z.codelist.filter((function(t){return"shsyl"===t.type}))}]};this.Base.generateExcel(s)}else this.$message.error("请选择时间范围！")},fnReset:function(){this.baseInfoForm.resetFields()},infoPageParams:function(){var t=this.baseInfoForm.getFieldsValue();t.startDate=t.allDate[0].format("YYYY-MM-DD HH:mm:ss");var e=new Date("".concat(t.allDate[1].format("YYYY-MM-DD"),"T00:00:00")),a=new Date(e);return a.setDate(e.getDate()+1),a.setHours(0,0,0,0),t.endDate=this.Base.getMoment(a.toISOString()).format("YYYY-MM-DD HH:mm:ss"),t.cjflag="ks",t},fnQuery:function(){var t=this,e=this.baseInfoForm.getFieldValue("allDate");!e||e[0]&&e[1]?this.$nextTick((function(){t.$refs.gridPager.loadData((function(t){}))})):this.$message.error("请选择时间范围！")},fnCustomHeaderCell:function(t){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px",fontWeight:"bold"}}},filterOption:function(t,e){return e.componentOptions.children[0].text.indexOf(t)>=0},formatAmount:function(t){return t.toFixed(2).replace(/\d(?=(\d{3})+\.)/g,"$&,")},moneyFormat:function(t){var e=t.cellValue;return e&&0!=e?this.formatAmount(e):"0"},ratioFormat:function(t){var e=t.cellValue;return e&&0!=e?e+"%":"0"}}},m=g,p=a(1001),y=(0,p.Z)(m,r,n,!1,null,"0d9a97ab",null),b=y.exports},36766:function(t,e,a){"use strict";var r=a(66586);e["Z"]=r.Z},26263:function(t,e,a){"use strict";a.d(e,{s:function(){return r},x:function(){return n}});var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},n=[]},66586:function(t,e){"use strict";var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};e["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}},88097:function(t,e){"use strict";var a=[{columnKey:"shsyl",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}},{columnKey:"wgl",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}}],r={shsyl:{header:function(){return"物价审核使用率(%)=HIS出院数(人次)/物价审核数(人次)"}},wgl:{header:function(){return"违规率(%)=物价审核违规数(人次)/物价审核数(人次)"}}};e["Z"]={codelist:a,props:r}},55382:function(){},61219:function(){}}]);
"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[4040],{88412:function(t,e,a){var s=a(26263),n=a(36766),o=a(1001),r=(0,o.Z)(n.Z,s.s,s.x,!1,null,"5e7ef0ae",null);e["Z"]=r.exports},64040:function(t,e,a){a.r(e),a.d(e,{default:function(){return z}});var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"layOut"},[a("div",{staticClass:"totalLine"},[a("div",{staticClass:"overSituation"},[a("overSituation",{staticClass:"situationCard",attrs:{roleName:t.roleName,aae500List:"doctor"===t.roleName?t.aae500OpenDoctorList:t.aae500OpenNurseList}})],1)]),"firstBox"===t.nurseClass?a("div",{staticClass:"departRule"},[(t.showAssistNight||t.showAssistAppeal)&&"doctor"===t.roleName||(t.showAssistNight||t.showAssistAppeal)&&"nurse"===t.roleName?a("wobItemsComponent",{staticClass:"firstBox",attrs:{showAssistNight:t.showAssistNight,showAssistAppeal:t.showAssistAppeal,roleName:t.roleName}}):t._e(),"doctor"===t.roleName&&t.aae500OpenDoctorList.includes(1)?a("outpatientCompoent",{staticClass:"firstBox",attrs:{title:"今日普通门诊提醒项目top5",flag:"outPatinet"}}):t._e(),"doctor"===t.roleName&&t.aae500OpenDoctorList.includes(0)?a("outpatientCompoent",{staticClass:"firstBox",attrs:{title:"今日门特处方提醒项目top5",flag:"opsp"}}):t._e(),"doctor"===t.roleName&&t.aae500OpenDoctorList.includes(2)?a("outpatientCompoent",{staticClass:"firstBox",attrs:{title:"今日医生医嘱提醒项目top5",flag:"drord"}}):t._e(),"nurse"===t.roleName&&t.aae500OpenNurseList.includes(3)?a("outpatientCompoent",{staticClass:"firstBox",attrs:{title:"今日护士计费提醒项目top5",flag:"nurseFee"}}):t._e(),"doctor"===t.roleName&&t.aae500OpenDoctorList.includes(4)?a("outpatientCompoent",{staticClass:"firstBox",attrs:{title:"今日医生站出院提醒项目top5",flag:"drDscg"}}):t._e(),"nurse"===t.roleName&&t.aae500OpenNurseList.includes(7)?a("outpatientCompoent",{staticClass:"firstBox",attrs:{title:"今日护士站出院提醒项目top5",flag:"nurDscg"}}):t._e()],1):"secondBox"===t.nurseClass?a("div",{staticClass:"departRule"},[(t.showAssistNight||t.showAssistAppeal)&&"doctor"===t.roleName||(t.showAssistNight||t.showAssistAppeal)&&"nurse"===t.roleName?a("wobItemsComponent",{staticClass:"secondBox",attrs:{showAssistNight:t.showAssistNight,showAssistAppeal:t.showAssistAppeal,roleName:t.roleName}}):t._e(),"doctor"===t.roleName&&t.aae500OpenDoctorList.includes(1)?a("outpatientCompoent",{staticClass:"secondBox",attrs:{title:"今日普通门诊提醒项目top5",flag:"outPatinet"}}):t._e(),"doctor"===t.roleName&&t.aae500OpenDoctorList.includes(0)?a("outpatientCompoent",{staticClass:"secondBox",attrs:{title:"今日门特处方提醒项目top5",flag:"opsp"}}):t._e(),"doctor"===t.roleName&&t.aae500OpenDoctorList.includes(2)?a("outpatientCompoent",{staticClass:"secondBox",attrs:{title:"今日医生医嘱提醒项目top5",flag:"drord"}}):t._e(),"nurse"===t.roleName&&t.aae500OpenNurseList.includes(3)?a("outpatientCompoent",{staticClass:"secondBox",attrs:{title:"今日护士计费提醒项目top5",flag:"nurseFee"}}):t._e(),"doctor"===t.roleName&&t.aae500OpenDoctorList.includes(4)?a("outpatientCompoent",{staticClass:"secondBox",attrs:{title:"今日医生站出院提醒项目top5",flag:"drDscg"}}):t._e(),"nurse"===t.roleName&&t.aae500OpenNurseList.includes(7)?a("outpatientCompoent",{staticClass:"secondBox",attrs:{title:"今日护士站出院提醒项目top5",flag:"nurDscg"}}):t._e()],1):"thirdBox"===t.nurseClass?a("div",{staticClass:"departRule"},[(t.showAssistNight||t.showAssistAppeal)&&"doctor"===t.roleName||(t.showAssistNight||t.showAssistAppeal)&&"nurse"===t.roleName?a("wobItemsComponent",{staticClass:"thirdBox",attrs:{showAssistNight:t.showAssistNight,showAssistAppeal:t.showAssistAppeal,roleName:t.roleName}}):t._e(),"doctor"===t.roleName&&t.aae500OpenDoctorList.includes(1)?a("outpatientCompoent",{staticClass:"thirdBox",attrs:{title:"今日普通门诊提醒项目top5",flag:"outPatinet"}}):t._e(),"doctor"===t.roleName&&t.aae500OpenDoctorList.includes(0)?a("outpatientCompoent",{staticClass:"thirdBox",attrs:{title:"今日门特处方提醒项目top5",flag:"opsp"}}):t._e(),"doctor"===t.roleName&&t.aae500OpenDoctorList.includes(2)?a("outpatientCompoent",{staticClass:"thirdBox",attrs:{title:"今日医生医嘱提醒项目top5",flag:"drord"}}):t._e(),"nurse"===t.roleName&&t.aae500OpenNurseList.includes(3)?a("outpatientCompoent",{staticClass:"thirdBox",attrs:{title:"今日护士计费提醒项目top5",flag:"nurseFee"}}):t._e(),"doctor"===t.roleName&&t.aae500OpenDoctorList.includes(4)?a("outpatientCompoent",{staticClass:"thirdBox",attrs:{title:"今日医生站出院提醒项目top5",flag:"drDscg"}}):t._e(),"nurse"===t.roleName&&t.aae500OpenNurseList.includes(7)?a("outpatientCompoent",{staticClass:"thirdBox",attrs:{title:"今日护士站出院提醒项目top5",flag:"nurDscg"}}):t._e()],1):t._e()])},n=[],o=a(88412),r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"cardFlex"},[a("ta-card",{staticClass:"cardItem"},[a("p",{staticClass:"cardName"},[t._v("今日开单项目数")]),a("p",{staticClass:"cardValue",on:{mouseenter:function(e){return t.mouseenter(e,t.situationData.jrkdxms.toLocaleString("en-us"))},mouseleave:function(e){return t.mouseleave(e,t.situationData.jrkdxms.toLocaleString("en-us"))}}},[t._v(" "+t._s(t.situationData.jrkdxms.toLocaleString("en-us"))+" ")])]),a("ta-card",{staticClass:"cardItem"},[a("p",{staticClass:"cardName"},[t._v("今日开单违规项目数")]),a("p",{staticClass:"cardValue",on:{mouseenter:function(e){return t.mouseenter(e,t.situationData.jrwgxms.toLocaleString("en-us"))},mouseleave:function(e){return t.mouseleave(e,t.situationData.jrwgxms.toLocaleString("en-us"))}}},[t._v(" "+t._s(t.situationData.jrwgxms.toLocaleString("en-us"))+" ")])]),a("ta-card",{staticClass:"cardItem"},[a("p",{staticClass:"cardName"},[t._v("今日开单违规率")]),a("p",{staticClass:"cardValue",on:{mouseenter:function(e){return t.mouseenter(e,t.situationData.jrwgl.toLocaleString("en-us"))},mouseleave:function(e){return t.mouseleave(e,t.situationData.jrwgl.toLocaleString("en-us"))}}},[t._v(" "+t._s(t.situationData.jrwgl.toLocaleString("en-us"))+" ")])]),a("ta-card",{staticClass:"cardItem"},[a("p",{staticClass:"cardName"},[t._v("今日出院人次")]),a("p",{staticClass:"cardValue",on:{mouseenter:function(e){return t.mouseenter(e,t.situationData.jrcyrc.toLocaleString("en-us"))},mouseleave:function(e){return t.mouseleave(e,t.situationData.jrcyrc.toLocaleString("en-us"))}}},[t._v(" "+t._s(t.situationData.jrcyrc.toLocaleString("en-us"))+" ")])]),a("ta-card",{staticClass:"cardItem"},[a("p",{staticClass:"cardName"},[t._v("今日违规出院人次")]),a("p",{staticClass:"cardValue",on:{mouseenter:function(e){return t.mouseenter(e,t.situationData.jrwgcyrc.toLocaleString("en-us"))},mouseleave:function(e){return t.mouseleave(e,t.situationData.jrwgcyrc.toLocaleString("en-us"))}}},[t._v(" "+t._s(t.situationData.jrwgcyrc.toLocaleString("en-us"))+" ")])])],1)},i=[],l={props:{roleName:{type:String,default:""},aae500List:{type:Array,default:[]}},watch:{aae500List:{handler:function(t,e){t.length>0&&this.init()},deep:!0}},data:function(){return{situationData:{jrkdxms:0,jrwgxms:0,jrwgl:0,jrcyrc:0,jrwgcyrc:0},searchObj:{},searchCondition:{}}},created:function(){},mounted:function(){},beforeDestroy:function(){},methods:{init:function(){var t=this;this.Base.submit(null,{url:"doctorWork/queryOverallSituation",data:{roleName:this.roleName,aae500List:this.aae500List},autoValid:!1,autoQs:!1},{successCallback:function(e){200==e.code?(t.situationData.jrkdxms=e.data.data.jrkdxms||0,t.situationData.jrwgxms=e.data.data.jrwgxms||0,t.situationData.jrcyrc=e.data.data.jrcyrc||0,t.situationData.jrwgcyrc=e.data.data.jrwgcyrc||0,t.situationData.jrwgl=e.data.data.jrkdxms?(e.data.data.jrwgxms/e.data.data.jrkdxms*100).toFixed(2)+"%":"0%"):t.$message.error(e.errors[0].msg)}})},mouseenter:function(t,e){e.length>8&&this.$refs.tooltip.open(t.target,"".concat(e))},mouseleave:function(t,e){e.length>8&&this.$refs.tooltip.close()}}},c=l,u=a(1001),d=(0,u.Z)(c,r,i,!1,null,"1806c8f9",null),p=d.exports,m=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"fit"},[a("div",[a("ta-title",{staticStyle:{"font-size":"16px"}},[t._v(" 待办事项 ")]),a("div",{staticClass:"silder"})],1),a("div",[a("div",{directives:[{name:"show",rawName:"v-show",value:t.showAssistNight,expression:"showAssistNight"}]},[a("div",{staticStyle:{padding:"10px 20px 10px 20px",display:"flex","font-size":"14px"}},[a("ta-badge",{attrs:{color:"black",text:"每晚预审"}})],1),a("table",{staticStyle:{width:"95%",margin:"5px 10px","font-size":"18px","text-align":"center"},attrs:{"text-align":"center"}},[t._m(0),a("tr",{staticClass:"turnClass",attrs:{height:"40"},on:{click:function(e){return t.turn("night")}}},[a("td",{attrs:{align:"center"}},[t._v(t._s(t.nightUnHandled))]),a("td",{attrs:{align:"center"}},[t._v(t._s(t.nightHandled))]),a("td",[a("div",{staticStyle:{width:"90%","margin-top":"-6px"}},[a("ta-progress",{attrs:{strokeColor:"rgba(103, 194, 58, 1)",strokeWidth:13,percent:t.nightProcess,status:"active"}})],1)])])])]),a("div",{directives:[{name:"show",rawName:"v-show",value:t.showAssistAppeal,expression:"showAssistAppeal"}],staticStyle:{"margin-top":"20px"}},[a("div",{staticStyle:{padding:"10px 20px 10px 20px",display:"flex","font-size":"14px"}},[a("ta-badge",{attrs:{color:"black",text:"住院申诉处理"}})],1),a("table",{staticStyle:{width:"95%",margin:"5px 10px","font-size":"18px"},attrs:{"text-align":"center"}},[t._m(1),a("tr",{staticClass:"turnClass",attrs:{height:"40"},on:{click:function(e){return t.turn("inhosp")}}},[a("td",{attrs:{align:"center"}},[t._v(" "+t._s(t.needhandcount))]),a("td",{attrs:{align:"center"}},[t._v(t._s(t.needsubmitcount))]),a("td",[a("div",{staticStyle:{width:"90%","margin-top":"-6px"}},[a("ta-progress",{attrs:{type:"line",strokeWidth:13,"stroke-color":"rgba(230, 162, 60, 1)",percent:t.neddPercent,status:"active"}})],1)])])])])])])},f=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("tr",{staticStyle:{width:"80%",background:"#F5F7FA","font-size":"14px"},attrs:{height:"40",align:"center"}},[a("td",{staticStyle:{"border-radius":"5px 0px 0px 5px"}},[t._v("待处理")]),a("td",{staticStyle:{"border-radius":"0px 0px 0px 0px"}},[t._v("已处理")]),a("td",{staticStyle:{"border-radius":"0px 5px 5px 0px"}},[t._v("完成进度")])])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("tr",{staticStyle:{width:"90%",background:"#F5F7FA","font-size":"14px"},attrs:{height:"40",align:"center"}},[a("td",{staticStyle:{"border-radius":"5px 0px 0px 5px"}},[t._v("待处理")]),a("td",{staticStyle:{"border-radius":"0px 0px 0px 0px"}},[t._v("待提交")]),a("td",{staticStyle:{"border-radius":"0px 5px 5px 0px"}},[t._v("完成进度")])])}],h={name:"wobItemsComponent",components:{TaTitle:o.Z},props:{roleName:{type:String},showAssistNight:{type:Boolean},showAssistAppeal:{type:Boolean}},watch:{roleName:function(t){}},data:function(){return{nightHandled:0,nightUnHandled:0,nightProcess:0,needsubmitcount:0,needhandcount:0,neddPercent:0}},mounted:function(){this.init()},methods:{init:function(){var t=this;this.Base.submit(null,{url:"doctorWork/queryWobItems",data:{roleName:this.roleName}},{successCallback:function(e){t.nightHandled=e.data.data.nighthandled||0,t.nightUnHandled=e.data.data.nightunhandled||0;var a=e.data.data.night||0;t.nightProcess=Number(a>0?(t.nightHandled/a*100).toFixed(2):0),t.needsubmitcount=e.data.data.needsubmitcount||0,t.needhandcount=e.data.data.needhandcount||0,t.neddPercent=Number(e.data.data.totalcount>0?((e.data.data.totalcount-t.needhandcount)/e.data.data.totalcount*100).toFixed(2):0)}})},turn:function(t){if("night"===t){var e="";"nurse"===this.roleName&&(e="nurse");var a="approvalHandle.html#/approvalHandle?source=".concat(e);this.Base.openTabMenu({id:20231117140503+t,name:"每晚预审",url:a})}else{var s="appealForDoc.html#/appealForDoc";this.Base.openTabMenu({id:20231117140503+t,name:"申诉材料处理",url:s})}}}},g=h,x=(0,u.Z)(g,m,f,!1,null,"1dbcb8a6",null),v=x.exports,C=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("ta-title",{staticStyle:{"font-size":"16px"}},[t._v(" "+t._s(t.title)+" ")]),a("div",{staticClass:"silder"}),a("div",{staticClass:"departmentBox"},[a("div",[a("ta-big-table",{ref:"xTable",attrs:{height:"230",data:t.departmentData,stripe:"",size:"mini","highlight-hover-row":"","auto-resize":"","show-overflow":"","export-config":{isPrint:!0}}},[a("ta-big-table-column",{attrs:{field:"ake002",title:"项目名称","min-width":"80",align:"center"}}),a("ta-big-table-column",{attrs:{field:"ykz018",title:"限制条件","min-width":"100",align:"center"}}),a("ta-big-table-column",{attrs:{field:"txcs",title:"提醒次数","min-width":"100",align:"center",formatter:t.formatterUs}})],1)],1)])],1)},w=[],b=(a(92566),a(36797)),N=a.n(b),_={name:"outpatientCompoent",components:{TaTitle:o.Z},props:{title:{type:String,default:""},flag:{type:String,default:""}},watch:{},data:function(){return{departmentData:[],urlMap:{outPatinet:"doctorWork/queryOutPatinetOrder",opsp:"doctorWork/queryOpspOrder",drord:"doctorWork/queryDrordOrder",nurseFee:"doctorWork/queryNurseFeeOrder",drDscg:"doctorWork/queryDrDscgOrder",nurDscg:"doctorWork/queryNurDscgOrder"}}},created:function(){},mounted:function(){this.init()},beforeDestroy:function(){},methods:{init:function(){var t=this;this.Base.submit(null,{url:this.urlMap[this.flag]},{successCallback:function(e){t.departmentData=e.data.data}})},more:function(){var t={allDate:[N()().subtract(1,"d").format("YYYY-MM-DD"),N()().subtract(1,"d").format("YYYY-MM-DD")]};if(["drDscg","nurDscg"].includes(this.flag)){var e="reportStatistics.html#/dscgReportStatisticsXM?flag1=".concat(this.flag,"&params=").concat(JSON.stringify(t));this.Base.openTabMenu({id:20231117140503+this.flag,name:"出院审核项目统计",url:e})}else{var a="reportStatistics.html#/reportStatisticsXM?flag1=".concat(this.flag,"&params=").concat(JSON.stringify(t));this.Base.openTabMenu({id:20231117140503+this.flag,name:"开单提醒项目统计",url:a})}},formatterUs:function(t){var e=t.cellValue;return e+"次"}}},S=_,D=(0,u.Z)(S,C,w,!1,null,"8056c5fc",null),y=D.exports,A=a(83231),k={name:"doctorWork",components:{TaTitle:o.Z,overSituation:p,wobItemsComponent:v,outpatientCompoent:y},data:function(){return{roleName:"",aae500OpenDoctorList:[],aae500OpenNurseList:[],doctorClass:"",nurseClass:"",showAssistNight:!0,showAssistAppeal:!0}},created:function(){this.init()},mounted:function(){},methods:{moment:N(),init:function(){var t=this;this.Base.submit(null,{url:"doctorWork/queryCurRole"},{successCallback:function(e){200===e.code?t.roleName=e.data.data:t.$message.error(e.errors[0].msg)}}),this.Base.submit(null,{url:"assistantWindow/queryMenu",data:{roleNum:"1"}},{successCallback:function(e){var a=e.data.data,s=a.flatMap((function(t){return t.menuList})).map((function(t){return t.url})),n=new Set(s),o="approvalHandle.html#/approvalHandle",r="appealForDoc.html#/appealForDoc";t.showAssistNight=n.has(o),t.showAssistAppeal=n.has(r)}});var e=["2","3","7","16"];A.Z.getAa01AAE500StartStop({aaz499s:JSON.stringify(e)},(function(e){t.aae500OpenDoctorList=e.data.aae500List,t.doctorClass=[4,3,2].includes(t.aae500OpenDoctorList.length)?"secondBox":1===t.aae500OpenDoctorList.length?"firstBox":"thirdBox";var a=["4","6"];A.Z.getAa01AAE500StartStop({aaz499s:JSON.stringify(a)},(function(e){t.aae500OpenNurseList=e.data.aae500List,t.nurseClass=2===t.aae500OpenNurseList.length?"secondBox":1===t.aae500OpenNurseList.length?"firstBox":"thirdBox"}))}))}}},L=k,B=(0,u.Z)(L,s,n,!1,null,"f06a2f94",null),z=B.exports},36766:function(t,e,a){var s=a(66586);e["Z"]=s.Z},26263:function(t,e,a){a.d(e,{s:function(){return s},x:function(){return n}});var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},n=[]},66586:function(t,e){var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};e["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}},83231:function(t,e,a){var s=a(48534);a(36133);function n(t){return t?t.akb020&&t.aaz263?"personal":t.akb020&&t.aaz309&&t.aaz307?"group":t.akb020&&t.aaz307?"department":t.akb020?"hospital":"all":"all"}function o(t){return r.apply(this,arguments)}function r(){return r=(0,s.Z)(regeneratorRuntime.mark((function t(e){var a,s,o,r,i,l,c,u,d,p,m,f;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Base.submit(null,{url:"/miimCommonRead/queryDataPermission",showPageLoading:!1,data:{jobnumber:e},autoValid:!0});case 2:return a=t.sent,s=new Set,o=new Set,a.data.permission.forEach((function(t){var e=n(t);"hospital"===e&&s.add(t.akb020),"department"===e&&o.add(t.aaz307)})),r=a.data.permission.filter((function(t){return"department"===n(t)||!o.has(t.aaz307)})).filter((function(t){return"hospital"===n(t)||!s.has(t.akb020)})),i=new Set(r.map((function(t){return t.akb020})).filter((function(t){return null!=t}))),l=new Set(r.map((function(t){return t.aaz307})).filter((function(t){return null!=t}))),c=new Set(r.map((function(t){return t.aaz309})).filter((function(t){return null!=t}))),u=new Set(r.map((function(t){return t.aaz263})).filter((function(t){return null!=t}))),d=!1,p=!1,m=!1,f=!1,1===i.size&&(d=!0),1===l.size&&1===i.size&&(p=!0),1===l.size&&1===i.size&&1===c.size&&(m=!0),1===i.size&&0===l.size&&1===u.size&&(f=!0),t.abrupt("return",{akb020Set:i,aaz307Set:l,aaz263Set:u,aaz309Set:c,akb020Disable:d,aaz307Disable:p,aaz263Disable:f,aaz309Disable:m});case 20:case"end":return t.stop()}}),t)}))),r.apply(this,arguments)}function i(t,e){Base.submit(null,{url:"miimCommonRead/getAa01AAE500StartStop",data:t},{successCallback:function(t){return e(t)}})}function l(t,e){Base.submit(null,{url:"miimCommonRead/insertTableColumShow",data:t,autoValid:!1},{successCallback:function(t){return e("success")},failCallback:function(t){return e("fail")}})}var c={zbx01:{footer:function(){return"统筹金总额"}},zbx02:{footer:function(){return"基金支出总额"}},zbx03:{footer:function(){return"总费用"}},zbx04:{footer:function(){return"总住院日"}}};e["Z"]={permissionCheck:o,getAa01AAE500StartStop:i,insertTableColumShow:l,props:c,moneyNumFormat:function(t){var e=t.cellValue;return e||"—"}}}}]);
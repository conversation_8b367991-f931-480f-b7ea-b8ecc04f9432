"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[3642],{88412:function(t,e,a){var i=a(26263),l=a(36766),o=a(1001),r=(0,o.Z)(l.Z,i.s,i.x,!1,null,"5e7ef0ae",null);e["Z"]=r.exports},33642:function(t,e,a){a.r(e),a.d(e,{default:function(){return d}});var i=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("div",{staticClass:"fit"},[i("ta-border-layout",{attrs:{"layout-type":"fixTop"}},[i("div",{attrs:{slot:"header"},slot:"header"},[i("ta-title",{attrs:{title:"查询表格"}}),i("ta-form",{attrs:{formLayout:"","auto-form-create":function(e){return t.queryParamForm=e}}},[i("ta-form-item",{attrs:{"field-decorator-id":"recordTime","field-decorator-options":{rules:[{required:!0,message:"必须选取日期"}]},required:!0,"init-value":e.defaultValue,span:6}},[i("span",{attrs:{slot:"label"},slot:"label"},[e._v("添加时间")]),i("ta-range-picker",{attrs:{"allow-one":!0}})],1),i("ta-form-item",{attrs:{"field-decorator-id":"akc190",label:"当前就诊号",labelCol:{span:8},wrapperCol:{span:16},span:5}},[i("ta-input")],1),i("ta-button",{staticStyle:{"margin-left":"15px"},attrs:{type:"primary",icon:"search"},on:{click:e.fnQuery}},[e._v("查询")]),i("ta-button",{attrs:{type:"default",icon:"plus"},on:{click:e.fnAdd}},[e._v("新增")])],1)],1),i("div",{staticClass:"fit content-box"},[i("div",{staticClass:"content-title"},[i("ta-title",{attrs:{title:"查询结果"}})],1),i("div",{staticClass:"table-content"},[i("ta-big-table",{ref:"Table",attrs:{border:"",height:"auto",columns:e.auditPatientWhiteTable,data:e.auditPatientWhiteList,"auto-resize":"",resizable:"","highlight-hover-row":"","show-overflow":""}},[i("template",{slot:"bottomBar"},[i("ta-pagination",{ref:"gridPager",staticStyle:{"text-align":"right","margin-right":"100px"},attrs:{defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"],"data-source":e.auditPatientWhiteList,params:e.infoPageParams,url:"auditWhitelist/queryAuditPatientWhitelist"},on:{"update:dataSource":function(t){e.auditPatientWhiteList=t},"update:data-source":function(t){e.auditPatientWhiteList=t}}})],1)],2)],1)]),i("div",[i("ta-modal",{attrs:{width:400,height:200,title:"添加患者审核白名单"},on:{cancel:e.handleModalCancel,ok:e.handleModalConfirm},model:{value:e.modalVisible,callback:function(t){e.modalVisible=t},expression:"modalVisible"}},[i("ta-form",{attrs:{autoFormCreate:function(e){return t.baseform=e},layout:"horizontal",formLayout:!0}},[i("ta-form-item",{attrs:{fieldDecoratorId:"akc190",labelCol:{span:8},wrapperCol:{span:16},span:24,"field-decorator-options":{rules:[{required:!0,message:"必须输入患者当前就诊号"}]}}},[i("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("患者就诊号")]),i("ta-input",{attrs:{placeholder:"请输入"}})],1),i("ta-form-item",{attrs:{fieldDecoratorId:"aae500List",label:"审核场景",labelCol:{span:8},wrapperCol:{span:16},span:24,"field-decorator-options":{rules:[{required:!0,message:"必须选择场景号"}]}}},[i("ta-select",{attrs:{mode:"multiple",showSearch:"",placeholder:"请选择审核场景","collection-type":"AAE500","collection-filter":e.filterList,reverseFilter:!0}})],1)],1)],1)],1)])],1)},l=[],o=a(88412),r=[{type:"seq",title:"序号",width:"60",align:"center"},{title:"当前就诊号",field:"akc190",width:300,align:"center",overflowTooltip:!0},{title:"场景",field:"aae500s",width:500,align:"center",overflowTooltip:!0},{title:"移入白名单时间",field:"aae040",width:400,align:"center",overflowTooltip:!0},{title:"操作人",field:"ykz041",align:"center",overflowTooltip:!0}],n={components:{TaTitle:o.Z},data:function(){return{drawerTitle:"",rqtime:"",defaultValue:[this.Base.getMoment((new Date).toISOString().slice(0,10),"YYYY-MM-DD"),this.Base.getMoment((new Date).toISOString().slice(0,10),"YYYY-MM-DD")],visible:!1,auditPatientWhiteTable:r,auditPatientWhiteList:[],modalVisible:!1,filterList:""}},mounted:function(){this.getAa01AAE500StartStop()},methods:{fnAdd:function(){this.modalVisible=!0},infoPageParams:function(){var t=this.queryParamForm.getFieldsValue();return t.startDate=t.recordTime[0].format("YYYY-MM-DD HH:mm:ss"),t.endDate=t.recordTime[1].format("YYYY-MM-DD HH:mm:ss"),t},fnQuery:function(){this.$refs.gridPager.loadData((function(t){}))},handleModalCancel:function(){},handleModalConfirm:function(){var t=this;this.baseform.validateFields((function(e,a){if(!e){var i=t.baseform.getFieldsValue();i.aae500s=i.aae500List.join(","),t.Base.submit(null,{url:"auditWhitelist/addAuditPatientWhitelist",data:i,autoValid:!1},{successCallback:function(e){"1"===e.data.code&&(t.$message.success("添加成功"),t.modalVisible=!1,t.baseform.resetFields(),t.fnQuery())},failCallback:function(t){}})}}))},getAa01AAE500StartStop:function(){var t=this,e=["16","2","17","18","3","4","7","5","8","6"];this.Base.submit(null,{url:"miimCommonRead/getAa01AAE500StartStop",data:{aaz499s:JSON.stringify(e)}},{successCallback:function(e){var a=e.data.aae500List;t.filterList=a.join(",")},failCallback:function(t){}})}}},s=n,c=a(1001),u=(0,c.Z)(s,i,l,!1,null,"1c5b0b8a",null),d=u.exports},36766:function(t,e,a){var i=a(66586);e["Z"]=i.Z},26263:function(t,e,a){a.d(e,{s:function(){return i},x:function(){return l}});var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},l=[]},66586:function(t,e){var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};e["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}}}]);
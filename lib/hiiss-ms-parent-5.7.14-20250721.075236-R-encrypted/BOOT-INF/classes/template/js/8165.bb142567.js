"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[8165],{88412:function(t,e,i){var a=i(26263),n=i(36766),o=i(1001),r=(0,o.Z)(n.Z,a.s,a.x,!1,null,"5e7ef0ae",null);e["Z"]=r.exports},8165:function(t,e,i){i.r(e),i.d(e,{default:function(){return k}});var a=function(){var t=this,e=this,i=e.$createElement,a=e._self._c||i;return a("div",{staticClass:"fit"},[a("ta-border-layout",{attrs:{"layout-type":"fixTop"}},[a("div",{attrs:{slot:"header"},slot:"header"},[a("ta-title",{attrs:{title:"查询表格"}}),a("ta-form",{attrs:{formLayout:"","auto-form-create":function(e){return t.queryParamForm=e}}},[a("ta-form-item",{attrs:{"field-decorator-id":"requestTime","field-decorator-options":{rules:[{required:!0,message:"必须选取日期"}]},required:!0,"init-value":e.defaultValue,span:5}},[a("span",{attrs:{slot:"label"},slot:"label"},[e._v("请求时间")]),a("ta-range-picker",{attrs:{"allow-one":!0}})],1),a("ta-form-item",{attrs:{"field-decorator-id":"mdtrtId",label:"当次就诊号",span:4}},[a("ta-input")],1),a("ta-form-item",{attrs:{"field-decorator-id":"accessKey",label:"交易接口",span:4}},[a("ta-input")],1),a("ta-form-item",{attrs:{span:4,"wrapper-col":{span:15},"field-decorator-id":"rqtimecom",initValue:"up",label:"请求耗时"}},[a("ta-select",{staticStyle:{width:"38%"}},[a("ta-select-option",{attrs:{value:"up"}},[e._v(" >")]),a("ta-select-option",{attrs:{value:"down"}},[e._v(" <")])],1),a("ta-input-number",{staticStyle:{width:"62%"},attrs:{formatter:function(t){return""===t?"":t+"ms"},max:1e5,min:0,parser:function(t){return t.replace("ms","")}},model:{value:e.rqtime,callback:function(t){e.rqtime=t},expression:"rqtime"}})],1),a("ta-button",{staticStyle:{margin:"0 20px 0 60px"},attrs:{type:"primary",icon:"search"},on:{click:e.fnQuery}},[e._v("查询")]),a("ta-button",{staticStyle:{"margin-right":"20px"},attrs:{type:"default",icon:"redo"},on:{click:e.fnReset}},[e._v("重置")])],1)],1),a("div",{staticClass:"fit content-box"},[a("div",{staticClass:"content-title"},[a("ta-title",{attrs:{title:"查询结果"}})],1),a("div",{staticClass:"table-content"},[a("ta-big-table",{ref:"Table",attrs:{border:"",height:"auto",data:e.UTSLogsListList,columns:e.UTSLogTable,"auto-resize":"",resizable:"","highlight-hover-row":"","show-overflow":""},on:{"cell-click":e.cellClickEvent},scopedSlots:e._u([{key:"response",fn:function(t){t.row;return[a("a",{staticStyle:{cursor:"pointer"}},[a("span",{staticStyle:{color:"#1b65b9"}},[e._v("点击查看详情")])])]}},{key:"request",fn:function(t){t.row;return[a("a",[a("span",{staticStyle:{color:"#1b65b9"}},[e._v("点击查看详情")])])]}},{key:"msg",fn:function(t){var i=t.row;return[a("a",[a("span",{staticStyle:{color:"#1b65b9"}},[e._v(e._s(i.msg))])])]}}])},[a("ta-big-table-column",{attrs:{type:"checkbox",width:"40"}}),a("ta-big-table-column",{attrs:{type:"seq",align:"center",title:"序号",width:"50",sortable:""}}),a("template",{slot:"bottomBar"},[a("ta-pagination",{ref:"gridPager",staticStyle:{"text-align":"right","margin-right":"100px"},attrs:{defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"],"data-source":e.UTSLogsListList,params:e.infoPageParams,url:"utsLogs/queryUTSLogs"},on:{"update:dataSource":function(t){e.UTSLogsListList=t},"update:data-source":function(t){e.UTSLogsListList=t}}})],1)],2)],1)])]),a("div",[a("ta-drawer",{attrs:{title:e.drawerTitle,placement:"right",visible:e.visible,width:700},on:{close:e.onClose}},[[a("ta-form",{attrs:{autoFormCreate:function(e){t.form=e}}},[a("ta-form-item",{attrs:{label:"",fieldDecoratorId:"textarea",initValue:""}},[a("ta-textarea",{attrs:{rows:27}})],1)],1)]],2)],1)],1)},n=[],o=i(88412),r={name:"disabled-config",model:{prop:"config",event:"change"},props:{config:{type:Object,required:!0}},render:function(){var t=this,e=arguments[0];return e("ta-label-con",{attrs:{label:"是否禁用"}},[e("ta-switch",{model:{value:t.config.disabled,callback:function(e){t.$set(t.config,"disabled",e)}}})])}},l={name:"display-config",model:{prop:"config",event:"change"},props:{config:{type:Object,required:!0}},render:function(){var t=this,e=arguments[0];return e("ta-label-con",{attrs:{label:"是否可见"}},[e("ta-switch",{model:{value:t.config.display,callback:function(e){t.$set(t.config,"display",e)}}})])}},s={name:"extra-config",model:{prop:"config",event:"change"},props:{config:{type:Object,required:!0}},render:function(){var t=this,e=arguments[0];return e("ta-label-con",{attrs:{label:"额外信息"}},[e("ta-input",{attrs:{placeholder:"请输入额外信息"},model:{value:t.config.extra,callback:function(e){t.$set(t.config,"extra",e)}}})])}},c={name:"id-config",props:{config:{type:Object,required:!0}},model:{prop:"config",event:"change"},render:function(){var t=this,e=arguments[0];return e("ta-label-con",{attrs:{label:"ID"}},[e("ta-input",{attrs:{placeholder:"请输入ID"},model:{value:t.config.fieldDecoratorId,callback:function(e){t.$set(t.config,"fieldDecoratorId",e)}}})])}},f={name:"initial-value-config",model:{prop:"config",event:"change"},props:{config:{type:Object,required:!0}},render:function(){var t=this,e=arguments[0];return e("ta-label-con",{attrs:{label:"默认值"}},[e("ta-input",{attrs:{placeholder:"请输入默认值"},model:{value:t.config.initialValue,callback:function(e){t.$set(t.config,"initialValue",e)}}})])}},u={name:"label-config",props:{config:{type:Object,required:!0}},model:{prop:"config",event:"change"},render:function(){var t=this,e=arguments[0];return e("ta-label-con",{attrs:{label:"Label"}},[e("ta-input",{attrs:{disabled:"divider"===this.config.type,placeholder:"请输入Label"},model:{value:t.config.label,callback:function(e){t.$set(t.config,"label",e)}}})])}},d={name:"placeholder-config",model:{prop:"config",event:"change"},props:{config:{type:Object,required:!0}},render:function(){var t=this,e=arguments[0];return e("ta-label-con",{attrs:{label:"占位内容"}},[e("ta-input",{attrs:{placeholder:"请输入占位内容"},model:{value:t.config.placeholder,callback:function(e){t.$set(t.config,"placeholder",e)}}})])}},g={name:"required-config",model:{prop:"config",event:"change"},props:{config:{type:Object,required:!0}},render:function(){var t=this,e=arguments[0];return e("div",[e("ta-label-con",{attrs:{label:"是否必填"}},[e("ta-switch",{model:{value:t.config.required,callback:function(e){t.$set(t.config,"required",e)}}})]),!0===this.config.required&&e("ta-label-con",{attrs:{label:"必填提示"}},[e("ta-input",{attrs:{placeholder:"请输入必填提示信息"},model:{value:t.config.message,callback:function(e){t.$set(t.config,"message",e)}}})])])}},p={name:"span-config",model:{prop:"config",event:"change"},props:{config:{type:Object,required:!0}},render:function(){var t=this,e=arguments[0];return e("ta-label-con",{attrs:{label:"所占栅格"}},[e("ta-input-number",{attrs:{disabled:"divider"===this.config.type,min:1,max:24,placeholder:"请输入表单所占的栅格"},style:"width: 100%",model:{value:t.config.span,callback:function(e){t.$set(t.config,"span",e)}}})])}},m={name:"validate-rules-config",model:{prop:"value",event:"change"},props:{value:String},data:function(){return{temp:this.value}},methods:{nativeChangeEvent:function(t){this.$emit("change",t.target.value)}},render:function(){var t=this,e=arguments[0];return e("ta-textarea",{attrs:{placeholder:"请填写数组格式的校验规则，例如：    [{ 'pattern': '[A-Za-z]+', 'message': '必须包含英文'}]",rows:4},model:{value:t.temp,callback:function(e){t.temp=e}},nativeOn:{change:this.nativeChangeEvent}})}},h={name:"validate-config",components:{ValidateRulesConfig:m},model:{prop:"config",event:"change"},props:{config:{type:Object,required:!0}},render:function(){var t=this,e=arguments[0];return e("div",[e("ta-label-con",{attrs:{label:"校验时机"}},[e("ta-select",{style:"width: 100%",attrs:{mode:"multiple",placeholder:"请选择校验时机"},model:{value:t.config.validateTrigger,callback:function(e){t.$set(t.config,"validateTrigger",e)}}},[e("ta-select-option",{attrs:{value:"change"}},["改变"]),e("ta-select-option",{attrs:{value:"blur"}},["失去焦点"])])]),e("ta-label-con",{attrs:{label:"校验规则"}},[e("validate-rules-config",{model:{value:t.config.rules,callback:function(e){t.$set(t.config,"rules",e)}}})])])}},v={name:"width-config",model:{prop:"config",event:"change"},props:{config:{type:Object,required:!0}},render:function(){var t=this,e=arguments[0];return e("ta-label-con",{attrs:{label:"宽度"}},[e("ta-input-number",{style:"width: 100%",attrs:{min:100,placeholder:"请输入列宽度"},model:{value:t.config.width,callback:function(e){t.$set(t.config,"width",e)}}})])}},b={name:"base-config",components:{DisabledConfig:r,DisplayConfig:l,ExtraConfig:s,IdConfig:c,InitialValueConfig:f,LabelConfig:u,PlaceholderConfig:d,RequiredConfig:g,SpanConfig:p,ValidateConfig:h,WidthConfig:v},model:{prop:"config",event:"change"},props:{config:{type:Object,required:!0},configNameList:{type:Array,required:!0}},render:function(){var t=arguments[0];return t("div",[this.configNameList.includes("label")&&t("label-config",{attrs:{config:this.config}}),this.configNameList.includes("id")&&t("id-config",{attrs:{config:this.config}}),this.configNameList.includes("width")&&t("width-config",{attrs:{config:this.config}}),this.configNameList.includes("span")&&t("span-config",{attrs:{config:this.config}}),this.configNameList.includes("initialValue")&&t("initial-value-config",{attrs:{config:this.config}}),this.configNameList.includes("placeholder")&&t("placeholder-config",{attrs:{config:this.config}}),this.configNameList.includes("extra")&&t("extra-config",{attrs:{config:this.config}}),this.$slots.default,this.configNameList.includes("required")&&t("required-config",{attrs:{config:this.config}}),this.configNameList.includes("disabled")&&t("disabled-config",{attrs:{config:this.config}}),this.configNameList.includes("display")&&t("display-config",{attrs:{config:this.config}}),this.configNameList.includes("validate")&&t("validate-config",{attrs:{config:this.config}})])}},y={name:"divider-config",components:{BaseConfig:b},model:{prop:"config",event:"change"},props:{config:{type:Object,required:!0}},data:function(){return{defaultConfigNameList:["label","span"]}},render:function(){var t=arguments[0];return t("base-config",{attrs:{config:this.config,configNameList:this.defaultConfigNameList}})}},q=(i(36797),[{title:"流水号",field:"id",width:140,align:"left",overflowTooltip:!0},{title:"当次就诊号(curr_mdtrt_id)",field:"mdtrtId",width:200,align:"left",overflowTooltip:!0},{title:"交易接口",field:"accessKey",width:150,align:"center",overflowTooltip:!0},{title:"请求耗时(ms)",field:"costTime",width:150,align:"right",overflowTooltip:!0},{title:"请求参数",field:"request",width:240,align:"center",overflowTooltip:!0,scopedSlots:{customRender:"request"},customRender:{default:"request"}},{title:"响应结果",field:"response",width:240,align:"center",overflowTooltip:!0,scopedSlots:{customRender:"response"},customRender:{default:"response"}},{title:"请求时间",field:"requestTime",width:240,align:"center",overflowTooltip:!0},{title:"异常信息",field:"msg",width:300,align:"left",overflowTooltip:!0,scopedSlots:{customRender:"msg"},customRender:{default:"msg"}}]),w={components:{DividerConfig:y,TaTitle:o.Z},data:function(){return{requestDetail:"",responseDetail:"",drawerTitle:"",visible:!1,UTSLogTable:q,UTSLogsListList:[],rqtime:"",defaultValue:[this.Base.getMoment((new Date).toISOString().slice(0,10),"YYYY-MM-DD"),this.Base.getMoment((new Date).toISOString().slice(0,10),"YYYY-MM-DD")]}},mounted:function(){this.fnQuery()},methods:{fnReset:function(){this.rqtime="",this.queryParamForm.resetFields(),this.fnQuery()},infoPageParams:function(){var t=this.queryParamForm.getFieldsValue();return t.startDate=t.requestTime[0].format("YYYY-MM-DD HH:mm:ss"),t.endDate=t.requestTime[1].format("YYYY-MM-DD HH:mm:ss"),t.rqtime=this.rqtime,t.rqtimecom=this.rqtime||0===this.rqtime?t.rqtimecom:"",t},fnQuery:function(){this.$refs.gridPager.loadData((function(t){}))},cellClickEvent:function(t){var e=this,i=t.row,a=t.column;this.drawerTitle=a.title,"响应结果"==a.title&&(this.visible=!0,this.$nextTick((function(){Base.submit(null,{url:"utsLogs/queryResponseDetail",data:{id:i.id}}).then((function(t){e.responseDetail=t.data.result,e.form.setFieldsValue({textarea:e.responseDetail})}))}))),"请求参数"==a.title&&(this.visible=!0,this.$nextTick((function(){Base.submit(null,{url:"utsLogs/queryRequestDetail",data:{id:i.id}}).then((function(t){e.requestDetail=t.data.result,e.form.setFieldsValue({textarea:e.requestDetail})}))}))),"异常信息"==a.title&&(this.visible=!0,this.$nextTick((function(){e.form.setFieldsValue({textarea:[i.msg]})})))},onClose:function(){this.visible=!1}}},L=w,S=i(1001),T=(0,S.Z)(L,a,n,!1,null,"0c9d80d8",null),k=T.exports},36766:function(t,e,i){var a=i(66586);e["Z"]=a.Z},26263:function(t,e,i){i.d(e,{s:function(){return a},x:function(){return n}});var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("h3",{staticStyle:{"font-weight":"normal"}},[i("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),i("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},n=[]},66586:function(t,e){var i={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};e["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:i}}}}}]);
"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[1429],{88412:function(t,e,a){var i=a(26263),l=a(36766),o=a(1001),s=(0,o.Z)(l.Z,i.s,i.x,!1,null,"5e7ef0ae",null);e["Z"]=s.exports},61429:function(t,e,a){a.r(e),a.d(e,{default:function(){return m}});var i=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("div",{staticClass:"fit"},[i("ta-border-layout",{attrs:{layout:{footer:"55px"},showPadding:!0,"footer-cfg":{showBorder:!1}}},[i("div",{staticClass:"fit kpi-report-cntainer",staticStyle:{height:"95%"}},[i("div",{staticClass:"form-container"},[i("ta-form",{staticClass:"form-content-box",attrs:{autoFormCreate:function(e){t.baseInfoForm=e},layout:"horizontal",formLayout:!0}},[i("ta-form-item",{attrs:{fieldDecoratorId:"allDate",labelCol:{span:4},wrapperCol:{span:20},span:6,"init-value":e.rangeValue,fieldDecoratorOptions:{rules:[{required:!0,message:"必须选取时间"}]}}},[i("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("时间")]),i("ta-range-picker",{staticStyle:{width:"100%"},attrs:{"allow-one":!0,disabledDate:e.disabledDate},on:{change:e.selectTimeRange}},[i("ta-icon",{attrs:{slot:"suffixIcon",type:""},slot:"suffixIcon"})],1)],1),i("ta-form-item",{attrs:{fieldDecoratorId:"departments",label:"科室",span:6,labelCol:{span:8},wrapperCol:{span:16}}},[i("ta-select",{attrs:{placeholder:"请选择",allowClear:"",showSearch:!0,optionFilterProp:"children"},on:{change:e.selectDepartments}},e._l(e.ksList,(function(t){return i("ta-select-option",{key:t.value,attrs:{value:t.value}},[e._v(e._s(t.label)+" ")])})),1)],1),i("ta-form-item",{attrs:{fieldDecoratorId:"weight",label:"权重",span:6,labelCol:{span:8},wrapperCol:{span:16}}},[i("div",{staticClass:"form-item-weight"},[i("div",{staticClass:"weight-left"},[i("ta-select",{staticStyle:{width:"65px"},on:{change:e.selectType},model:{value:e.weightType,callback:function(t){e.weightType=t},expression:"weightType"}},e._l(e.symbolList,(function(t){return i("ta-select-option",{key:"symbol"+t.value,attrs:{value:t.value}},[e._v(e._s(t.label))])})),1),i("ta-input-number",{staticStyle:{width:"87px"},attrs:{min:0,max:e.maxWeightValue,step:.1},on:{change:e.selectWeightLeft},model:{value:e.weightLeftValue,callback:function(t){e.weightLeftValue=t},expression:"weightLeftValue"}})],1),4===e.weightType?i("div",{staticClass:"weight-right"},[i("span",{staticClass:"weight-range-text"},[e._v("和")]),i("ta-input-number",{staticStyle:{width:"87px"},attrs:{min:.1,max:1,step:.1},on:{change:e.selectWeightRight},model:{value:e.weightRightValue,callback:function(t){e.weightRightValue=t},expression:"weightRightValue"}})],1):e._e()])])],1),i("div",[i("ta-button",{on:{click:e.exportDataEvent}},[e._v("导出Excel")])],1)],1),i("ta-title",{attrs:{title:"病组列表"}}),i("div",{staticClass:"table-content"},[i("ta-big-table",{ref:"kpiReportTable",attrs:{height:"auto",border:"",stripe:"",resizable:"","highlight-hover-row":"",data:e.infoTableData},scopedSlots:e._u([{key:"bottomBar",fn:function(){return[i("ta-pagination",{ref:"kpiReportTablePage",staticStyle:{"text-align":"right","margin-top":"10px"},attrs:{params:e.pageParams,defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"],"data-source":e.infoTableData,url:"groupService/queryDiseaseGroupInfoPage"},on:{"update:dataSource":function(t){e.infoTableData=t},"update:data-source":function(t){e.infoTableData=t}}})]},proxy:!0}])},[i("ta-big-table-column",{attrs:{type:"seq",title:"序号",width:"60"}}),i("ta-big-table-column",{attrs:{field:"name",title:"病组",width:"35%","show-overflow":""}}),i("ta-big-table-column",{attrs:{field:"cases",title:"病案数",width:"15%",align:"right","show-overflow":""},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("span",{staticStyle:{color:"blue",cursor:"pointer","text-decoration":"underline"},on:{click:function(t){return e.jumpDetail(a)}}},[e._v(e._s(a.cases))])]}}])}),i("ta-big-table-column",{attrs:{field:"total",title:"总费用（元）",width:"10%",align:"right","show-overflow":""}}),i("ta-big-table-column",{attrs:{field:"hospitalization",title:"平均住院日（天）",width:"12%",align:"right","show-overflow":""}}),i("ta-big-table-column",{attrs:{field:"cost",title:"平均费用（天）",width:"10%",align:"right","show-overflow":""}}),i("ta-big-table-column",{attrs:{field:"weight",title:"权重",width:"10%",align:"right","show-overflow":""}})],1)],1)],1)])],1)},l=[],o=a(66347),s=a(88412),n=a(36797),r=a.n(n),u=a(73738),c=a.n(u),h={name:"kpiReport",components:{TaTitle:s.Z},data:function(){var t=[{label:"≥",value:0},{label:"≤",value:1},{label:">",value:2},{label:"<",value:3},{label:"介于",value:4}];return{rangeValue:[this.Base.getMoment(this.createDate().toISOString().slice(0,10),"YYYY-MM-DD"),this.Base.getMoment((new Date).toISOString().slice(0,10),"YYYY-MM-DD")],hosList:[],ksList:[],doctorList:[],akb020:"",aaz307:"",symbolList:t,infoTableData:[],weightType:0,weightLeftValue:.5,maxWeightValue:1,weightRightValue:.6}},mounted:function(){this.fnQueryHos(),this.fnQuery()},methods:{createDate:function(){var t=new Date;return t.setMonth(t.getMonth()-2),t},fnQueryHos:function(){var t=this,e={url:"/miimCommonRead/queryHospRpcList",autoValid:!0},a={successCallback:function(e){t.hosList=e.data.resultData,t.baseInfoForm.setFieldsValue({medinsCode:t.hosList[0].value}),t.akb020=t.hosList[0].value,t.fnQueryDept(t.akb020)},failCallback:function(e){t.$message.error("医院数据加载失败")}};this.Base.submit(null,e,a)},fnQueryDept:function(t){var e=this;this.akb020=t||"",this.Base.submit(null,{url:"miimCommonRead/queryDepartRpcList",data:{akb020:this.akb020},autoValid:!1},{successCallback:function(t){e.baseInfoForm.resetFields("aaz307"),e.ksList=t.data.resultData},failCallback:function(t){e.$message.error("科室数据加载失败")}})},disabledDate:function(t){return t=t.format("YYYYMMDD"),t&&t>r()().startOf("day").format("YYYYMMDD")},fnQuery:function(){var t=this,e=this.baseInfoForm.getFieldError("allDate");this.baseInfoForm.getFieldsValue();if(e)return!1;this.$nextTick((function(){t.$refs.kpiReportTablePage.loadData()}))},exportExcel:function(){var t=this.baseInfoForm.getFieldsValue();if(void 0===t.allDate||t.allDate.length<1)this.$message.error("请选择时间范围！");else{var e,a=t.allDate[0].format("YYYY-MM-DD"),i=t.allDate[1].format("YYYY-MM-DD"),l=[],s=this.infoTableData,n=(0,o.Z)(curColumns);try{for(n.s();!(e=n.n()).done;){var r=e.value;l.push({header:r.title,key:r.dataIndex,width:20})}}catch(c){n.e(c)}finally{n.f()}var u={fileName:"住院开单提醒查询结果表("+a+"-"+i+")",sheets:[{name:"worksheet1",column:{complex:!1,columns:l},rows:s,codeList:[{codeType:"APE893",columnKey:"ape893"},{codeType:"RESAULT",columnKey:"yd"}]}]};this.Base.generateExcel(u)}},exportDataEvent:function(){this.$refs.kpiReportTable.exportData({type:"csv",name:"csv"})},jumpDetail:function(t){var e=new Date(this.baseInfoForm.getFieldValue("allDate")[0].format()).getTime(),a=new Date(this.baseInfoForm.getFieldValue("allDate")[1].format()).getTime();this.Base.openTabMenu({id:"d69969ddd67c4ee8ac0c78030deddbc4",name:"绩效考核报表明细",url:"costcontrol.html#/kpiReportDetail?startTime=".concat(e,"&endTime=").concat(a,"&code=").concat(t.code),refresh:!1})},selectTimeRange:function(){this.fnQuery()},selectDepartments:function(){this.fnQuery()},selectType:function(t){t>=0&&(4===t?(this.maxWeightValue=.9,this.weightLeftValue?this.weightRightValue=Number(Number(this.weightLeftValue)+.1).toFixed(1):this.weightLeftValue=0):this.maxWeightValue=1),this.fnQuery()},selectWeightLeft:function(t){this.weightLeftValue=Number(t).toFixed(1),4===this.weightType&&(!this.weightRightValue||t>=this.weightRightValue)&&(this.weightRightValue=Number(Number(t)+.1).toFixed(1)),t>=0&&this.goQuery()},selectWeightRight:function(t){this.weightRightValue=Number(t).toFixed(1),(!this.weightLeftValue||t<=this.weightLeftValue)&&(this.weightLeftValue=Number(Number(t)-.1).toFixed(1)),t>=0&&this.goQuery()},goQuery:c()((function(){this.fnQuery()}),1e3),pageParams:function(){var t={},e=this.baseInfoForm.getFieldError("allDate");if(e)return{};var a=this.baseInfoForm.getFieldsValue();return t.startdate=a.allDate[0].format("YYYY-MM-DD"),t.enddate=a.allDate[1].format("YYYY-MM-DD"),t.deptcode=a.departments,t.weightType=this.weightType,t.weight=this.weightLeftValue,4===this.weightType&&(t.weightValue=[this.weightLeftValue,this.weightRightValue]),t}}},f=h,g=a(1001),d=(0,g.Z)(f,i,l,!1,null,"7d5efa03",null),m=d.exports},36766:function(t,e,a){var i=a(66586);e["Z"]=i.Z},26263:function(t,e,a){a.d(e,{s:function(){return i},x:function(){return l}});var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},l=[]},66586:function(t,e){var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};e["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}}}]);
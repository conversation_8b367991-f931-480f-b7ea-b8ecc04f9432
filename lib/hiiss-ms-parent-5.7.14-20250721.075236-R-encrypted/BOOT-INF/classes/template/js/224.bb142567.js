"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[224],{224:function(e,t,a){a.r(t),a.d(t,{default:function(){return ve}});var r=function(){var e=this,t=this,a=t.$createElement,r=t._self._c||a;return r("ta-border-layout",{attrs:{layout:{left:"300px"},"show-padding":!1}},[r("template",{slot:"left"},[r("div",{staticClass:"title",staticStyle:{"margin-left":"10px"}},[t._v("节点情况")]),r("div",{staticStyle:{height:"calc(100% - 100px)"}},[r("node-type-list",{attrs:{queryUrl:"mtt/localruleconfig/localCotent/getNodeTypeList"},on:{nodeTypeSelect:t.nodeTypeSelect}})],1)]),r("div",{staticClass:"title",staticStyle:{"margin-left":"10px"}},[t._v("查询条件")]),r("ta-card",{staticStyle:{width:"100%"},attrs:{bordered:!1}},[r("ta-form",{attrs:{"auto-form-create":function(t){return e.searchForm=t}}},[r("ta-row",[r("ta-col",{attrs:{span:8}},[r("ta-form-item",{attrs:{label:"节点名称","field-decorator-id":"ykz010"}},[r("ta-input",{attrs:{"allow-clear":!0}})],1)],1),r("ta-col",{attrs:{span:8}},[r("ta-form-item",{attrs:{label:"节点编码","field-decorator-id":"ykz042"}},[r("ta-input",{attrs:{"allow-clear":!0}})],1)],1),r("ta-col",{attrs:{span:8}},[r("ta-form-item",{attrs:{label:"节点说明","field-decorator-id":"ykz065"}},[r("ta-input",{attrs:{"allow-clear":!0}})],1)],1),r("ta-col",{attrs:{span:8}},[r("ta-form-item",{attrs:{label:"对照关系","field-decorator-id":"relative"}},[r("ta-select",{attrs:{"allow-clear":!0}},[r("ta-select-option",{attrs:{value:"1"}},[t._v("已配置")]),r("ta-select-option",{attrs:{value:"0"}},[t._v("未配置")])],1)],1)],1),r("ta-col",{attrs:{span:8}},[r("ta-form-item",{attrs:{label:"同步引擎","field-decorator-id":"needsync"}},[r("ta-select",{attrs:{"allow-clear":!0}},[r("ta-select-option",{attrs:{value:"1"}},[t._v("未同步")]),r("ta-select-option",{attrs:{value:"0"}},[t._v("已同步")])],1)],1)],1)],1),r("ta-row",[r("ta-col",{staticStyle:{"text-align":"center"}},[r("ta-button",{on:{click:t.onSearch}},[t._v("查询")])],1)],1)],1)],1),r("ta-card",{attrs:{bordered:!1}},[r("node-data",{ref:"nodeData",attrs:{blnPlatform:!0}})],1),r("ta-modal",{attrs:{width:400,height:150,"destroy-on-close":!0,footer:null},model:{value:t.importVisible,callback:function(e){t.importVisible=e},expression:"importVisible"}},[r("import-data",{attrs:{ykz001:t.ykz001,blnPlatform:!0},on:{onImportSuccess:t.onImportSuccess}})],1),r("ta-modal",{attrs:{title:"新增节点",width:900,height:250,"destroy-on-close":!0},model:{value:t.blnAddNodeVisible,callback:function(e){t.blnAddNodeVisible=e},expression:"blnAddNodeVisible"}},[r("ta-form",{attrs:{autoFormCreate:function(t){e.nodeConfForm=t}}},[r("ta-row",{staticStyle:{"margin-left":"-19%"}},[r("ta-col",{attrs:{span:this.addNodeConfStyle.nodeConfSpan}},[r("ta-form-item",{attrs:{fieldDecoratorId:"ykz010",label:"节点内容",required:!0,require:{message:"节点内容不能为空"}}},[r("ta-input")],1),r("ta-form-item",{attrs:{fieldDecoratorId:"ykz065",label:"节点说明"}},[r("ta-textarea",{attrs:{placeholder:"请输入",rows:4}})],1)],1)],1)],1),r("div",{attrs:{slot:"footer"},slot:"footer"},[r("ta-button",{attrs:{type:"primary"},on:{click:t.addNode}},[t._v("保存")])],1)],1)],2)},n=[],i=a(95082),o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"fit"},[a("ta-input",{attrs:{placeholder:"编号或名称或简写筛选"},on:{change:e.filterTable},model:{value:e.searchText,callback:function(t){e.searchText=t},expression:"searchText"}}),a("ta-table",{attrs:{columns:e.nodeTypeColumn,"data-source":e.nodeTypeShowList,bordered:!0,size:"small",scroll:{x:"100%",y:"100%"},"custom-row":e.nodeTypeCustomRow}})],1)},s=[],l=a(66347),c={name:"nodeTypeList",props:{queryUrl:String},data:function(){return{nodeTypeColumn:[{dataIndex:"ykz001",title:"编号",width:100,align:"center",overflowTooltip:!0},{dataIndex:"showName",title:"判断节点类型",width:150,align:"center",overflowTooltip:!0}],nodeTypeList:[],nodeTypeShowList:[],searchText:null}},mounted:function(){this.getNodeTypeList()},methods:{getNodeTypeList:function(){var e=this;this.Base.submit(null,{url:this.queryUrl}).then((function(t){var a,r=(0,l.Z)(t.data.list);try{for(r.s();!(a=r.n()).done;){var n=a.value;n.showName="".concat(n.ykz002,"(").concat(n.ykz063,")")}}catch(i){r.e(i)}finally{r.f()}e.nodeTypeList=t.data.list,e.nodeTypeShowList=t.data.list}))},nodeTypeCustomRow:function(e,t,a){var r=this,n="black",i="white";return e.blnYellow?(n="#73cfb3",i="#ffffd5"):e.blnGreen&&(i="#d0ffbf"),{style:{cursor:"pointer",backgroundColor:i,color:n},on:{click:function(t){r.nodeTypeList.forEach((function(t){e.ykz001==t.ykz001?t.blnYellow=!0:t.blnYellow=!1})),r.$forceUpdate(),r.$emit("nodeTypeSelect",e)}}}},filterTable:function(){var e=this;this.searchText?this.nodeTypeShowList=this.nodeTypeList.filter((function(t){return t.showName.toLowerCase().includes(e.searchText.toLowerCase())||(t.ykz001+"").toLowerCase().includes(e.searchText.toLowerCase())})):this.nodeTypeShowList=this.nodeTypeList}}},d=c,p=a(1001),h=(0,p.Z)(d,o,s,!1,null,"0565c1b4",null),u=h.exports,m=function(){var e=this,t=this,a=t.$createElement,r=t._self._c||a;return r("div",[r("ta-form",{attrs:{"auto-form-create":function(t){return e.searchForm=t}}},[r("ta-row",[r("ta-col",{attrs:{span:8}},[r("ta-form-item",{attrs:{label:"节点名称","field-decorator-id":"ykz010"}},[r("ta-input",{attrs:{"allow-clear":!0}})],1)],1),r("ta-col",{attrs:{span:8}},[r("ta-form-item",{attrs:{label:"节点编码","field-decorator-id":"ykz042"}},[r("ta-input",{attrs:{"allow-clear":!0}})],1)],1),r("ta-col",{attrs:{span:8}},[r("ta-form-item",{attrs:{label:"节点说明","field-decorator-id":"ykz065"}},[r("ta-input",{attrs:{"allow-clear":!0}})],1)],1),r("ta-col",{attrs:{span:8}},[r("ta-form-item",{attrs:{label:"对照关系","field-decorator-id":"relative"}},[r("ta-select",{attrs:{"allow-clear":!0}},[r("ta-select-option",{attrs:{value:"1"}},[t._v("已配置")]),r("ta-select-option",{attrs:{value:"0"}},[t._v("未配置")])],1)],1)],1)],1),r("ta-row",[r("ta-col",{staticStyle:{"text-align":"center"}},[r("ta-button",{on:{click:t.onSearch}},[t._v("查询")]),r("ta-button",{on:{click:t.downloadTemplate}},[t._v("模板下载")]),r("ta-button",{attrs:{type:"primary"},on:{click:t.openAddNode}},[t._v("新增")])],1)],1)],1),r("ta-modal",{attrs:{title:"新增节点",width:900,height:250},model:{value:t.blnAddNodeVisible,callback:function(e){t.blnAddNodeVisible=e},expression:"blnAddNodeVisible"}},[r("ta-form",{attrs:{autoFormCreate:function(t){e.nodeConfForm=t}}},[r("ta-row",{staticStyle:{"margin-left":"-19%"}},[r("ta-col",{attrs:{span:this.addNodeConfStyle.nodeConfSpan}},[r("ta-form-item",{attrs:{fieldDecoratorId:"ykz010",label:"节点内容",required:!0,require:{message:"节点内容不能为空"}}},[r("ta-input")],1),r("ta-form-item",{attrs:{fieldDecoratorId:"ykz065",label:"节点说明"}},[r("ta-textarea",{attrs:{placeholder:"请输入",rows:4}})],1)],1)],1)],1),r("div",{attrs:{slot:"footer"},slot:"footer"},[r("ta-button",{attrs:{type:"primary"},on:{click:t.addNode}},[t._v("保存")])],1)],1)],1)},f=[],v={name:"nodeSearch",props:{},data:function(){return{ruleidList:[],blnAddNodeVisible:!1,addNodeConfStyle:{nodeConfSpan:23},ykz001Options:[]}},mounted:function(){this.getYkz001List()},methods:{getYkz001List:function(){var e=this,t={url:"mtt/localruleconfig/localCotent/getNodeTypeList",data:{}};this.Base.submit(null,t).then((function(t){t.data.success&&(e.ykz001Options=t.data.list)})).catch()},onSearch:function(){var e=this.searchForm.getFieldsValue();this.$emit("onSearch",e)},downloadTemplate:function(){var e=this.searchForm.getFieldsValue();this.$emit("onDownloadTemplate",e)},exportData:function(){var e=this.searchForm.getFieldsValue();this.$emit("onExportData",e)},importData:function(){var e=this.searchForm.getFieldsValue();this.$emit("onImportData",e)},openAddNode:function(){this.blnAddNodeVisible=!0},addNode:function(){var e=this;this.nodeConfForm.validateFields((function(t){if(!t){var a=e.nodeConfForm.getFieldsValue();Base.submit(null,{url:"mttRuleConfig/addNode",data:{dtoStr:JSON.stringify(a)}}).then((function(t){e.$message.success("新增成功"),e.onSearch()}))}}))}}},y=v,g=(0,p.Z)(y,m,f,!1,null,"da8e740e",null),T=g.exports,b=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("ta-table",{attrs:{columns:e.nodeColumn,"data-source":e.nodeData,bordered:!0,"have-sn":!0,size:"small","row-key":"yka062",scroll:{x:"100%",y:"386px"}},scopedSlots:e._u([{key:"needsync",fn:function(t,r){return["1"==r.needsync?a("ta-tag",{attrs:{color:"red"}},[e._v(" 未同步 ")]):a("ta-tag",{attrs:{color:"green"}},[e._v(" 已同步 ")])]}},{key:"relative",fn:function(t,r){return["1"==r.relative?a("a",{on:{click:function(t){return e.configNode(r)}}},[e._v("已配置")]):a("a",{staticStyle:{color:"red"},on:{click:function(t){return e.configNode(r)}}},[e._v("未配置")])]}},{key:"operate",fn:function(t,r){return[a("a",{on:{click:function(t){return e.downloadMke(r)}}},[e._v("下载")])]}},{key:"log",fn:function(t,r){return[a("a",{on:{click:function(t){return e.nodeLog(r)}}},[e._v("查看")])]}}])}),a("ta-pagination",{ref:"pager",staticClass:"page",attrs:{defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"],dataSource:e.nodeData,params:e.nodeParam,url:e.pageUrl},on:{"update:dataSource":function(t){e.nodeData=t},"update:data-source":function(t){e.nodeData=t}},model:{value:e.pageParam.pageNumber,callback:function(t){e.$set(e.pageParam,"pageNumber",t)},expression:"pageParam.pageNumber"}}),a("ta-modal",{attrs:{title:e.ykz072,visible:e.visible,footer:null,width:"90%",height:"700px",zoomable:!0,destroyOnClose:!0},on:{cancel:e.handleNodeCancel}}),a("ta-modal",{attrs:{"destroy-on-close":!0,title:"节点维护",height:650,width:1400},model:{value:e.blnNodeContentVisible,callback:function(t){e.blnNodeContentVisible=t},expression:"blnNodeContentVisible"}},[a("node-content",{attrs:{"clicked-node":e.clickNode}}),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("ta-button",{on:{click:function(t){e.blnNodeContentVisible=!1}}},[e._v("取消")])],1)],1),a("ta-modal",{attrs:{title:"包版本更新记录",visible:e.logVisible,footer:null,width:"80%",height:"500px",zoomable:!0,destroyOnClose:!0},on:{cancel:e.handleCancel}},[a("update-log",{attrs:{ykz042:e.nodeInfos.ykz042}})],1)],1)},k=[],C=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("ta-icon",{staticStyle:{color:"#1890ff"},attrs:{type:"right"}}),e._v(" 节点内容 "),e.hasHideNode()?e._e():a("ta-button",{attrs:{type:"primary"},on:{click:e.doSave}},[e._v("保存")]),a("ta-row",[a("ta-col",{staticStyle:{"margin-top":"10px"},attrs:{span:24}},[a("ta-col",{attrs:{span:12}},[e.hasHideNode()?e._e():a("ta-button-group",{staticStyle:{float:"left"}},[a("ta-button",{on:{click:e.openAddContent}},[e._v("新增")]),a("ta-button",{on:{click:e.openDisable}},[e._v("禁用")]),a("ta-button",{on:{click:e.downloadContentTemplate}},[e._v("模板下载")]),a("ta-button",{on:{click:e.openImportContent}},[e._v("导入")]),a("ta-button",{on:{click:e.removeContent}},[e._v("移除")])],1),a("span",{staticStyle:{"margin-left":"10px","line-height":"35px"}},[e._v(e._s(e.clickedNode.ykz042+":"+e.clickedNode.ykz010))])],1),a("ta-col",{attrs:{span:12}},[a("div",{staticStyle:{float:"right"}},[e._v(" 关键字: "),a("ta-input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入关键字"},model:{value:e.contentSearchText,callback:function(t){e.contentSearchText=t},expression:"contentSearchText"}}),a("ta-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary"},on:{click:e.onNodeContentSearch}},[e._v("查询 ")])],1)]),a("ta-col",{staticStyle:{"margin-top":"10px"},attrs:{span:24}},[a("ta-big-table",{ref:"contentTable",attrs:{data:e.contentListCache,"big-data-checkbox":"","header-drag-style":"","use-virtual":"",border:"full",heightSwitch:!0,height:450,"row-height":40,"row-style":e.contentCustomRow}},[a("ta-big-table-column",{attrs:{type:"checkbox",width:"55"}}),a("ta-big-table-column",{attrs:{type:"seq",width:"60",title:"序号",fixed:""}}),e._l(e.contentColumns,(function(e,t){return a("ta-big-table-column",{key:t,attrs:{align:e.align,resizable:"","show-header-overflow":"","show-overflow":e.overflowTooltip,field:e.dataIndex,title:e.title,fixed:e.fixed,width:e.width,collectionType:e.collectionType}})}))],2),a("ta-pagination",{ref:"pager",staticClass:"page",attrs:{dataSource:e.contentListCache,defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"],params:e.pageParam,url:this.moudleUrl+"/queryNodeData"},on:{"update:dataSource":function(t){e.contentListCache=t},"update:data-source":function(t){e.contentListCache=t}}})],1)],1)],1),a("ta-modal",{attrs:{title:"新增内容",footer:null,width:"60%",height:"600px",zoomable:!0,destroyOnClose:!0},on:{cancel:function(e){}},model:{value:e.addContentVisible,callback:function(t){e.addContentVisible=t},expression:"addContentVisible"}},[a("add-content-one",{attrs:{column:e.contentColumns,ykz001:this.clickedNode.ykz001,ykz063:e.clickedNode.ykz063},on:{fnAddOne:e.fnAddContentOneSuccess}})],1),a("ta-modal",{attrs:{title:"导入内容",destroyOnClose:!0,footer:null,width:500},model:{value:e.importContentVisible,callback:function(t){e.importContentVisible=t},expression:"importContentVisible"}},[a("importData",{attrs:{ykz001:this.clickedNode.ykz001},on:{onImportSuccess:e.onContentImportSuccess}})],1),a("ta-modal",{attrs:{title:"描述原因",footer:null,width:"60%",height:"300px",zoomable:!0,destroyOnClose:!0},on:{cancel:function(t){e.reasonVisible=!1}},model:{value:e.reasonVisible,callback:function(t){e.reasonVisible=t},expression:"reasonVisible"}},[a("save-reason",{attrs:{"dis-data":e.disData,"add-data":e.addData,nodeInfos:e.clickedNode},on:{closeSaved:e.closeSaved}})],1),a("ta-modal",{attrs:{title:"禁用说明"},model:{value:e.disableVisible,callback:function(t){e.disableVisible=t},expression:"disableVisible"}},[a("ta-input",{attrs:{placeholder:"请输入"},model:{value:e.disableDesc,callback:function(t){e.disableDesc=t},expression:"disableDesc"}}),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("ta-button",{attrs:{type:"primary"},on:{click:e.saveDisableDesc}},[e._v("确定")]),a("ta-button",{on:{click:function(t){e.disableVisible=!1}}},[e._v("取消")])],1)],1)],1)},w=[],A=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"fit"},[a("ta-upload",{attrs:{fileList:e.fileList,name:"file",accept:".xlsx, .xls",multiple:!1,action:"","show-upload-list":!1,beforeUpload:e.beforeUpload},on:{change:e.uploadChange}},[a("ta-button",{staticStyle:{"margin-left":"167px","margin-top":"10px",width:"120px"},attrs:{type:"primary"}},[a("ta-icon",{attrs:{type:"upload"}}),e._v(" 导入文件 ")],1)],1)],1)},x=[],S={name:"importData",props:["ykz001"],computed:{importUrl:function(){return"mttRuleConfig/generateImportData"}},data:function(){return{fileList:[]}},methods:{beforeUpload:function(e){return this.fileList.push(e),!1},uploadChange:function(){var e=this;this.fileList.length<=0?this.$message.error("还未选择文件！"):Base.submit(null,{url:this.importUrl,data:{ykz001:this.ykz001,uploadFile:this.fileList[0]},isFormData:!0,autoValid:!0}).then((function(t){t.data.success&&(e.$message.success("导入表格成功"),e.$emit("onImportSuccess",t.data.list))}))}}},I=S,z=(0,p.Z)(I,A,x,!1,null,"72a9f93b",null),R=z.exports,_=function(){var e=this,t=this,a=t.$createElement,r=t._self._c||a;return r("div",{staticStyle:{height:"490px"}},[r("div",{staticClass:"title"},[t._v("新增")]),r("ta-form",{attrs:{layout:"horizontal",formLayout:!0,autoFormCreate:function(t){e.addForm=t}}},[t.customShowType.unboundedInterval.isShow?r("div",[r("div",{staticStyle:{"margin-left":"158px","margin-bottom":"20px"}},[r("span",{staticClass:"show-required"},[t._v("参数编码 :")])]),r("ta-form-item",{staticStyle:{"margin-left":"150px"},attrs:{label:"区间类型",span:7,labelWidth:"90"}},[r("ta-select",{staticStyle:{width:"180px"},attrs:{"collection-type":"MINTERVALTYPE","allow-clear":""},on:{change:t.intervalTypeChange}}),r("div",{class:t.errorTips.intervalType.isError?"ant-form-item-info has-error":"ant-form-item-info"},[r("div",{staticClass:"ant-form-explain"},[t.errorTips.intervalType.isError?r("span",[t._v(t._s(t.errorTips.intervalType.errorMessage))]):t._e()])])],1),"1"==t.intervalType?r("ta-form-item",{attrs:{label:"区间范围",span:10,labelWidth:"80"}},[r("ta-select",{staticStyle:{width:"140px"},attrs:{"collection-type":"MAPE995","allow-clear":""},on:{change:function(e,a){return t.onIntervalCompareTypeChange(e,0)}},model:{value:t.intervalCompareTypeArr[0],callback:function(e){t.$set(t.intervalCompareTypeArr,0,e)},expression:"intervalCompareTypeArr[0]"}}),r("ta-input-number",{staticStyle:{width:"148px","margin-left":"20px"},attrs:{placeholder:"请输入阈值"},on:{change:function(e){return t.onIntervalRangeChange(e,0)}},model:{value:t.intervalRangeArr[0],callback:function(e){t.$set(t.intervalRangeArr,0,e)},expression:"intervalRangeArr[0]"}}),r("div",{class:t.errorTips.intervalType1.isError?"ant-form-item-info has-error":"ant-form-item-info"},[r("div",{staticClass:"ant-form-explain"},[t.errorTips.intervalType1.isError?r("span",[t._v(t._s(t.errorTips.intervalType1.errorMessage))]):t._e()])])],1):t._e(),"2"==t.intervalType?r("ta-form-item",{attrs:{label:"区间范围",span:24}},[r("ta-select",{staticStyle:{width:"120px"},attrs:{"collection-type":"MAPE995","collection-filter":"5,2",reverseFilter:!0,"allow-clear":""},on:{change:function(e){return t.onIntervalCompareTypeChange(e,0)}},model:{value:t.intervalCompareTypeArr[0],callback:function(e){t.$set(t.intervalCompareTypeArr,0,e)},expression:"intervalCompareTypeArr[0]"}}),r("ta-input-number",{staticStyle:{width:"138px","margin-left":"20px"},attrs:{placeholder:"请输入阈值"},on:{change:function(e){return t.onIntervalRangeChange(e,0)}},model:{value:t.intervalRangeArr[0],callback:function(e){t.$set(t.intervalRangeArr,0,e)},expression:"intervalRangeArr[0]"}}),r("ta-select",{staticStyle:{width:"120px","margin-left":"20px"},attrs:{"collection-type":"MAPE995","collection-filter":"3,1",reverseFilter:!0,"allow-clear":""},on:{change:function(e){return t.onIntervalCompareTypeChange(e,1)}},model:{value:t.intervalCompareTypeArr[1],callback:function(e){t.$set(t.intervalCompareTypeArr,1,e)},expression:"intervalCompareTypeArr[1]"}}),r("ta-input-number",{staticStyle:{width:"138px","margin-left":"20px"},attrs:{placeholder:"请输入阈值"},on:{change:function(e){return t.onIntervalRangeChange(e,1)}},model:{value:t.intervalRangeArr[1],callback:function(e){t.$set(t.intervalRangeArr,1,e)},expression:"intervalRangeArr[1]"}}),r("div",{class:t.errorTips.intervalType2.isError?"ant-form-item-info has-error":"ant-form-item-info"},[r("div",{staticClass:"ant-form-explain"},[t.errorTips.intervalType2.isError?r("span",[t._v(t._s(t.errorTips.intervalType2.errorMessage))]):t._e()])])],1):t._e()],1):t._e(),t.customShowType.refineUnboundedInterval.isShow?r("div",[r("div",{staticStyle:{"margin-left":"158px","margin-bottom":"20px"}},[r("span",{staticClass:"show-required"},[t._v("参数编码 :")])]),r("ta-form-item",{staticStyle:{"margin-left":"150px"},attrs:{label:"区间类型",span:6,labelWidth:"90"}},[r("ta-select",{staticStyle:{width:"140px"},attrs:{"collection-type":"MINTERVALTYPE","allow-clear":""},on:{change:t.intervalTypeChange}}),r("div",{class:t.errorTips.intervalType.isError?"ant-form-item-info has-error":"ant-form-item-info"},[r("div",{staticClass:"ant-form-explain"},[t.errorTips.intervalType.isError?r("span",[t._v(t._s(t.errorTips.intervalType.errorMessage))]):t._e()])])],1),"1"==t.intervalType?r("ta-form-item",{attrs:{label:"区间范围",span:12,labelWidth:"80"}},[r("ta-select",{staticStyle:{width:"120px"},attrs:{"collection-type":"MAPE995","allow-clear":""},on:{change:function(e){return t.onIntervalCompareTypeChange(e,0)}},model:{value:t.intervalCompareTypeArr[0],callback:function(e){t.$set(t.intervalCompareTypeArr,0,e)},expression:"intervalCompareTypeArr[0]"}}),r("ta-input-number",{staticStyle:{width:"118px","margin-left":"10px"},attrs:{placeholder:"请输入阈值"},on:{change:function(e){return t.onIntervalRangeChange(e,0)}},model:{value:t.intervalRangeArr[0],callback:function(e){t.$set(t.intervalRangeArr,0,e)},expression:"intervalRangeArr[0]"}}),r("ta-select",{staticStyle:{width:"90px","margin-left":"10px"},attrs:{"collection-type":"M_AGEUNIT","allow-clear":""},on:{change:function(e){return t.onAgeUnitArrChange(e,0)}},model:{value:t.ageUnitArr[0],callback:function(e){t.$set(t.ageUnitArr,0,e)},expression:"ageUnitArr[0]"}}),r("div",{class:t.errorTips.intervalType1.isError?"ant-form-item-info has-error":"ant-form-item-info"},[r("div",{staticClass:"ant-form-explain"},[t.errorTips.intervalType1.isError?r("span",[t._v(t._s(t.errorTips.intervalType1.errorMessage))]):t._e()])])],1):t._e(),"2"==t.intervalType?r("ta-form-item",{attrs:{label:"区间范围",span:24}},[r("ta-select",{staticStyle:{width:"101px"},attrs:{"collection-type":"MAPE995","collection-filter":"5,2",reverseFilter:!0,"allow-clear":""},on:{change:function(e){return t.onIntervalCompareTypeChange(e,0)}},model:{value:t.intervalCompareTypeArr[0],callback:function(e){t.$set(t.intervalCompareTypeArr,0,e)},expression:"intervalCompareTypeArr[0]"}}),r("ta-input-number",{staticStyle:{width:"98px","margin-left":"20px"},attrs:{placeholder:"请输入阈值"},on:{change:function(e){return t.onIntervalRangeChange(e,0)}},model:{value:t.intervalRangeArr[0],callback:function(e){t.$set(t.intervalRangeArr,0,e)},expression:"intervalRangeArr[0]"}}),r("ta-select",{staticStyle:{width:"60px","margin-left":"10px"},attrs:{"collection-type":"M_AGEUNIT","allow-clear":""},on:{change:function(e){return t.onAgeUnitArrChange(e,0)}},model:{value:t.ageUnitArr[0],callback:function(e){t.$set(t.ageUnitArr,0,e)},expression:"ageUnitArr[0]"}}),r("ta-select",{staticStyle:{width:"101px","margin-left":"10px"},attrs:{"collection-type":"MAPE995","collection-filter":"3,1",reverseFilter:!0,"allow-clear":""},on:{change:function(e){return t.onIntervalCompareTypeChange(e,1)}},model:{value:t.intervalCompareTypeArr[1],callback:function(e){t.$set(t.intervalCompareTypeArr,1,e)},expression:"intervalCompareTypeArr[1]"}}),r("ta-input-number",{staticStyle:{width:"98px","margin-left":"10px"},attrs:{placeholder:"请输入阈值"},on:{change:function(e){return t.onIntervalRangeChange(e,1)}},model:{value:t.intervalRangeArr[1],callback:function(e){t.$set(t.intervalRangeArr,1,e)},expression:"intervalRangeArr[1]"}}),r("ta-select",{staticStyle:{width:"60px","margin-left":"10px"},attrs:{"collection-type":"M_AGEUNIT","allow-clear":""},on:{change:function(e){return t.onAgeUnitArrChange(e,1)}},model:{value:t.ageUnitArr[1],callback:function(e){t.$set(t.ageUnitArr,1,e)},expression:"ageUnitArr[1]"}}),r("div",{class:t.errorTips.intervalType2.isError?"ant-form-item-info has-error":"ant-form-item-info"},[r("div",{staticClass:"ant-form-explain"},[t.errorTips.intervalType2.isError?r("span",[t._v(t._s(t.errorTips.intervalType2.errorMessage))]):t._e()])])],1):t._e()],1):t._e(),t.customShowType.jyUnboundedInterval.isShow?r("div",[r("ta-form-item",{staticStyle:{"margin-left":"140px"},attrs:{label:"判断方式",span:8,labelWidth:"100",fieldDecoratorId:"ykz193",require:{message:"请选择判断方式"}}},[r("ta-select",{staticStyle:{width:"236px"},attrs:{"collection-type":"MYKZ193","allow-clear":""},on:{change:t.jyIntervalTypeChange}})],1),"2"==t.jyIntervalType?r("ta-form-item",{staticStyle:{"margin-left":"10px"},attrs:{label:"取值范围",span:8,labelWidth:"100",fieldDecoratorId:"ykz194",require:{message:"请选择取值范围"}}},[r("ta-select",{staticStyle:{width:"236px"},attrs:{"collection-type":"M_QUALITATIVERANGETYPE","allow-clear":""},on:{change:t.intervalTypeChange}})],1):t._e(),r("div",{staticStyle:{clear:"both"}}),"1"==t.jyIntervalType?r("div",[r("div",{staticStyle:{"margin-left":"158px","margin-bottom":"20px"}},[r("span",{staticClass:"show-required"},[t._v("取值范围 :")])]),r("ta-form-item",{staticStyle:{"margin-left":"150px"},attrs:{label:"区间类型",span:7,labelWidth:"90"}},[r("ta-select",{staticStyle:{width:"170px"},attrs:{"collection-type":"MINTERVALTYPE","allow-clear":""},on:{change:t.intervalTypeChange}}),r("div",{class:t.errorTips.intervalType.isError?"ant-form-item-info has-error":"ant-form-item-info"},[r("div",{staticClass:"ant-form-explain"},[t.errorTips.intervalType.isError?r("span",[t._v(t._s(t.errorTips.intervalType.errorMessage))]):t._e()])])],1),"1"==t.intervalType?r("ta-form-item",{attrs:{label:"区间范围",span:10,labelWidth:"80"}},[r("ta-select",{staticStyle:{width:"140px"},attrs:{"collection-type":"MAPE995","allow-clear":""},on:{change:function(e){return t.onIntervalCompareTypeChange(e,0)}},model:{value:t.intervalCompareTypeArr[0],callback:function(e){t.$set(t.intervalCompareTypeArr,0,e)},expression:"intervalCompareTypeArr[0]"}}),r("ta-input-number",{staticStyle:{width:"146px","margin-left":"20px"},attrs:{placeholder:"请输入阈值"},on:{change:function(e){return t.onIntervalRangeChange(e,0)}},model:{value:t.intervalRangeArr[0],callback:function(e){t.$set(t.intervalRangeArr,0,e)},expression:"intervalRangeArr[0]"}}),r("div",{class:t.errorTips.intervalType1.isError?"ant-form-item-info has-error":"ant-form-item-info"},[r("div",{staticClass:"ant-form-explain"},[t.errorTips.intervalType1.isError?r("span",[t._v(t._s(t.errorTips.intervalType1.errorMessage))]):t._e()])])],1):t._e(),"2"==t.intervalType?r("ta-form-item",{attrs:{label:"区间范围",span:24}},[r("ta-select",{staticStyle:{width:"120px"},attrs:{"collection-type":"MAPE995","collection-filter":"5,2",reverseFilter:!0,"allow-clear":""},on:{change:function(e){return t.onIntervalCompareTypeChange(e,0)}},model:{value:t.intervalCompareTypeArr[0],callback:function(e){t.$set(t.intervalCompareTypeArr,0,e)},expression:"intervalCompareTypeArr[0]"}}),r("ta-input-number",{staticStyle:{width:"138px","margin-left":"20px"},attrs:{placeholder:"请输入阈值"},on:{change:function(e){return t.onIntervalRangeChange(e,0)}},model:{value:t.intervalRangeArr[0],callback:function(e){t.$set(t.intervalRangeArr,0,e)},expression:"intervalRangeArr[0]"}}),r("ta-select",{staticStyle:{width:"120px","margin-left":"20px"},attrs:{"collection-type":"MAPE995","collection-filter":"3,1",reverseFilter:!0,"allow-clear":""},on:{change:function(e){return t.onIntervalCompareTypeChange(e,1)}},model:{value:t.intervalCompareTypeArr[1],callback:function(e){t.$set(t.intervalCompareTypeArr,1,e)},expression:"intervalCompareTypeArr[1]"}}),r("ta-input-number",{staticStyle:{width:"138px","margin-left":"20px"},attrs:{placeholder:"请输入阈值"},on:{change:function(e){return t.onIntervalRangeChange(e,1)}},model:{value:t.intervalRangeArr[1],callback:function(e){t.$set(t.intervalRangeArr,1,e)},expression:"intervalRangeArr[1]"}}),r("div",{class:t.errorTips.intervalType2.isError?"ant-form-item-info has-error":"ant-form-item-info"},[r("div",{staticClass:"ant-form-explain"},[t.errorTips.intervalType2.isError?r("span",[t._v(t._s(t.errorTips.intervalType2.errorMessage))]):t._e()])])],1):t._e()],1):t._e()],1):t._e(),t.customShowType.dateUnboundedInterval.isShow?r("div",[r("div",{staticStyle:{clear:"both"}}),r("div",{staticStyle:{"margin-left":"158px","margin-bottom":"20px"}},[r("span",{staticClass:"show-required"},[t._v("参数编码 :")])]),r("ta-form-item",{staticStyle:{"margin-left":"140px"},attrs:{label:"判断类型",span:8,labelWidth:"100"}},[r("ta-select",{staticStyle:{width:"286px"},attrs:{"collection-type":"M_DATEJUDGETYPE",defaultValue:t.dateIntervalType},on:{change:t.dateIntervalTypeChange}})],1),"1"==t.dateIntervalType?[r("ta-form-item",{staticStyle:{"margin-left":"150px"},attrs:{label:"时间选择",span:24,labelWidth:"90"}},[r("my-date-multi-picker",{on:{change:t.checkDateIsExist},model:{value:t.dateRangeArr[0],callback:function(e){t.$set(t.dateRangeArr,0,e)},expression:"dateRangeArr[0]"}}),r("div",{class:t.errorTips.intervalType1.isError?"ant-form-item-info has-error":"ant-form-item-info"},[r("div",{staticClass:"ant-form-explain"},[t.errorTips.intervalType1.isError?r("span",[t._v(t._s(t.errorTips.intervalType1.errorMessage))]):t._e()])])],1),r("ta-form-item",{attrs:{span:24}},[r("div",{staticStyle:{background:"gainsboro","margin-left":"200px","font-size":"16px","margin-bottom":"10px","line-height":"30px","padding-left":"20px"}},[t._v(" "+t._s("="+(t.dateRangeArr[0]?t.dateRangeArr[0]:""))+" ")])])]:t._e(),"2"==t.dateIntervalType?r("div",[r("ta-form-item",{staticStyle:{"margin-left":"150px"},attrs:{label:"区间类型",span:24,labelWidth:"90"}},[r("ta-select",{staticStyle:{width:"286px"},attrs:{"collection-type":"MINTERVALTYPE","allow-clear":""},on:{change:t.intervalTypeChange}}),r("div",{class:t.errorTips.intervalType.isError?"ant-form-item-info has-error":"ant-form-item-info"},[r("div",{staticClass:"ant-form-explain"},[t.errorTips.intervalType.isError?r("span",[t._v(t._s(t.errorTips.intervalType.errorMessage))]):t._e()])])],1),"1"==t.intervalType?[r("ta-form-item",{staticStyle:{"margin-left":"150px"},attrs:{label:"区间范围",span:24,labelWidth:"90"}},[r("ta-select",{staticStyle:{width:"100px"},attrs:{"collection-type":"MAPE995","allow-clear":""},on:{change:function(e){return t.onIntervalCompareTypeChange(e,0)}},model:{value:t.intervalCompareTypeArr[0],callback:function(e){t.$set(t.intervalCompareTypeArr,0,e)},expression:"intervalCompareTypeArr[0]"}}),r("my-date-multi-picker",{staticStyle:{"margin-left":"10px"},on:{change:function(e){return t.checkIntervalRange(0)}},model:{value:t.dateRangeArr[0],callback:function(e){t.$set(t.dateRangeArr,0,e)},expression:"dateRangeArr[0]"}})],1),r("div",{staticStyle:{"margin-left":"200px"}},[r("div",{class:t.errorTips.intervalType1.isError?"ant-form-item-info has-error":"ant-form-item-info"},[r("div",{staticClass:"ant-form-explain"},[t.errorTips.intervalType1.isError?r("span",[t._v(t._s(t.errorTips.intervalType1.errorMessage))]):t._e()])])]),r("ta-form-item",{attrs:{span:24}},[r("div",{staticStyle:{height:"30px",background:"gainsboro","margin-left":"200px","font-size":"16px","margin-bottom":"10px","line-height":"30px","padding-left":"20px"}},[t._v(" "+t._s(t.covertApe995(t.intervalCompareTypeArr[0])+(t.dateRangeArr[0]?t.dateRangeArr[0]:""))+" ")])])]:t._e(),"2"==t.intervalType?[r("ta-form-item",{staticStyle:{"margin-left":"150px"},attrs:{label:"区间范围",span:24,labelWidth:"90"}},[r("ta-select",{staticStyle:{width:"100px"},attrs:{"collection-type":"MAPE995","collection-filter":"5,2",reverseFilter:!0,"allow-clear":""},on:{change:function(e){return t.onIntervalCompareTypeChange(e,0)}},model:{value:t.intervalCompareTypeArr[0],callback:function(e){t.$set(t.intervalCompareTypeArr,0,e)},expression:"intervalCompareTypeArr[0]"}}),r("my-date-multi-picker",{staticStyle:{"margin-left":"10px"},on:{change:function(e){return t.checkIntervalRange(0)}},model:{value:t.dateRangeArr[0],callback:function(e){t.$set(t.dateRangeArr,0,e)},expression:"dateRangeArr[0]"}})],1),r("ta-form-item",{staticStyle:{"margin-left":"140px"},attrs:{label:" ",colon:!1,span:24,labelWidth:"90"}},[r("ta-select",{staticStyle:{width:"100px","margin-left":"10px"},attrs:{"collection-type":"MAPE995","collection-filter":"3,1",reverseFilter:!0,"allow-clear":""},on:{change:function(e){return t.onIntervalCompareTypeChange(e,1)}},model:{value:t.intervalCompareTypeArr[1],callback:function(e){t.$set(t.intervalCompareTypeArr,1,e)},expression:"intervalCompareTypeArr[1]"}}),r("my-date-multi-picker",{staticStyle:{"margin-left":"10px"},on:{change:function(e){return t.checkIntervalRange(1)}},model:{value:t.dateRangeArr[1],callback:function(e){t.$set(t.dateRangeArr,1,e)},expression:"dateRangeArr[1]"}})],1),r("div",{staticStyle:{"margin-left":"200px"}},[r("div",{class:t.errorTips.intervalType2.isError?"ant-form-item-info has-error":"ant-form-item-info"},[r("div",{staticClass:"ant-form-explain"},[t.errorTips.intervalType2.isError?r("span",[t._v(t._s(t.errorTips.intervalType2.errorMessage))]):t._e()])])]),r("ta-form-item",{attrs:{span:24}},[r("div",{staticStyle:{background:"gainsboro","margin-left":"200px","font-size":"16px","margin-bottom":"10px","line-height":"30px","padding-left":"20px"}},[t._v(" "+t._s(t.covertApe995(t.intervalCompareTypeArr[0])+(t.dateRangeArr[0]?t.dateRangeArr[0]:"")+"&"+t.covertApe995(t.intervalCompareTypeArr[1])+(t.dateRangeArr[0]?t.dateRangeArr[1]:""))+" ")])])]:t._e()],2):t._e()],2):t._e(),t.customShowType.scoreTableUnboundedInterval.isShow?r("div",[r("ta-form-item",{attrs:{label:"评分表",span:24,fieldDecoratorId:"socreTable",require:{message:"请选择评分表"}}},[r("ta-select",{staticStyle:{width:"80%"},attrs:{"collection-type":"MYKZ044",labelInValue:"","allow-clear":""},on:{change:t.dateIntervalTypeChange}})],1),r("ta-form-item",{attrs:{label:"对比方式",span:24,fieldDecoratorId:"ape995",require:{message:"请选择对比方式"}}},[r("ta-select",{staticStyle:{width:"80%"},attrs:{"collection-type":"MAPE995","allow-clear":""},on:{change:t.dateIntervalTypeChange}})],1),r("ta-form-item",{attrs:{label:"阈值",span:24,fieldDecoratorId:"aae421",require:{message:"请输入阈值"}}},[r("ta-input-number",{staticStyle:{width:"80%"},attrs:{placeholder:"请输入阈值"}})],1)],1):t._e(),t._l(t.attrs,(function(e){return r("ta-form-item",{key:e.id,attrs:{span:24,require:{enable:t.notNullArr.includes(e.id),message:"不能为空"},fieldDecoratorId:e.id,label:e.key}},[r("ta-input",{staticStyle:{width:"80%"}})],1)})),t._l(t.selectAttr,(function(e){return r("ta-form-item",{key:e.id,attrs:{span:24,require:{enable:t.notNullArr.includes(e.id),message:"不能为空"},fieldDecoratorId:e.id,label:e.key}},[r("ta-select",{staticStyle:{width:"80%"},attrs:{collectionType:e.collection}})],1)})),r("ta-form-item",{attrs:{span:24}},[r("ta-button",{staticStyle:{"margin-left":"60%"},attrs:{type:"success"},on:{click:t.save}},[t._v("保存")])],1)],2)],1)},D=[],N=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("span",[e._v(" 年月日： "),a("ta-year-picker",{ref:"year",staticStyle:{width:"120px"},attrs:{open:e.open,placeholder:"年份-不指定"},on:{change:function(t){return e.onChange(!0)},openChange:e.openChange},model:{value:e.year,callback:function(t){e.year=t},expression:"year"}},[a("template",{slot:"renderExtraFooter"},[a("a",{staticStyle:{float:"right"},on:{click:e.onClickYearNot}},[e._v("不指定年份")]),a("div",{staticStyle:{clear:"both"}})])],2),a("ta-select",{staticStyle:{width:"120px"},attrs:{placeholder:"月份",options:e.months,showSearch:!0},on:{change:function(t){return e.onChange(!0)}},model:{value:e.month,callback:function(t){e.month=t},expression:"month"}}),a("ta-select",{staticStyle:{width:"120px"},attrs:{placeholder:"日期",options:e.days,showSearch:!0},on:{change:function(t){return e.onChange()}},model:{value:e.day,callback:function(t){e.day=t},expression:"day"}}),a("span",{staticStyle:{"margin-left":"20px"}},[e._v("时分秒：")]),a("ta-select",{staticStyle:{width:"95px"},attrs:{placeholder:"时",options:e.hours,showSearch:!0},on:{change:function(t){return e.onChange()}},model:{value:e.hour,callback:function(t){e.hour=t},expression:"hour"}}),a("ta-select",{staticStyle:{width:"95px"},attrs:{placeholder:"分",options:e.minutes,showSearch:!0},on:{change:function(t){return e.onChange()}},model:{value:e.minute,callback:function(t){e.minute=t},expression:"minute"}}),a("ta-select",{staticStyle:{width:"95px"},attrs:{placeholder:"秒",options:e.seconds,showSearch:!0},on:{change:function(t){return e.onChange()}},model:{value:e.second,callback:function(t){e.second=t},expression:"second"}}),a("a",{staticStyle:{display:"none"},attrs:{id:"myFocus"}},[e._v("焦点")])],1)},E=[],$={props:["value"],name:"MyDateMultiPicker",data:function(){return{year:void 0,month:void 0,day:void 0,hour:void 0,minute:void 0,second:void 0,months:[{value:"*",label:"不指定"},{value:"01",label:"一月"},{value:"02",label:"二月"},{value:"03",label:"三月"},{value:"04",label:"四月"},{value:"05",label:"五月"},{value:"06",label:"六月"},{value:"07",label:"七月"},{value:"08",label:"八月"},{value:"09",label:"九月"},{value:"10",label:"十月"},{value:"11",label:"十一月"},{value:"12",label:"十二月"}],days:[],hours:[],minutes:[],seconds:[],open:!1}},mounted:function(){var e=this;this.initDay(31),this.initTimeSelect(),this.$nextTick((function(){e.onChange()}))},methods:{openChange:function(e){this.open=!!e},onClickYearNot:function(){this.year=void 0,this.open=!1,this.onChange(!0)},initDay:function(e){this.days=[],this.days.push({value:"*",label:"不指定"});for(var t=1;t<=e;t++){var a=t+"";t<10&&(a="0"+t),this.days.push({value:a,label:a})}},initTimeSelect:function(){this.hours=[],this.hours.push({value:"*",label:"不指定"});for(var e=0;e<=24;e++){var t=e+"";e<10&&(t="0"+e),this.hours.push({value:t,label:t})}this.minutes=[],this.minutes.push({value:"*",label:"不指定"});for(var a=0;a<=60;a++){var r=a+"";a<10&&(r="0"+a),this.minutes.push({value:r,label:r})}this.seconds=[],this.seconds.push({value:"*",label:"不指定"});for(var n=0;n<=60;n++){var i=n+"";n<10&&(i="0"+n),this.seconds.push({value:i,label:i})}},getDaysInYearMonth:function(e,t){var a=new Date(e,t,0);return a.getDate()},onChange:function(e){var t=this;this.$nextTick((function(){var a=t.year;if(a=t.year?a.format("YYYY"):"*",e)if(a&&t.month&&"*"!=a&&"*"!=t.month){var r=t.month.replace(/\b(0+)/gi,""),n=t.getDaysInYearMonth(parseInt(a),parseInt(r));t.initDay(n),t.day&&parseInt(t.day)>n&&(t.day=void 0)}else a&&"*"!=a||!t.month||"02"!=t.month?t.initDay(31):(t.initDay(29),t.day&&parseInt(t.day)>29&&(t.day=void 0));var i=a+"."+(t.month||"*")+"."+(t.day||"*")+"."+(t.hour||"*")+"."+(t.minute||"*")+"."+(t.second||"*");t.$emit("input",i),t.$emit("change",i)}))}}},M=$,V=(0,p.Z)(M,N,E,!1,null,"d6622136",null),P=V.exports,U={NL:"unboundedInterval",TW:"unboundedInterval",TZ:"unboundedInterval",HD:"unboundedInterval",LT:"unboundedInterval",CT:"unboundedInterval",SD:"unboundedInterval",TD:"unboundedInterval",DE:"unboundedInterval",TL:"unboundedInterval",BM:"unboundedInterval",AK:"unboundedInterval",AL:"unboundedInterval",AM:"unboundedInterval",AN:"unboundedInterval",AY:"unboundedInterval",AR:"unboundedInterval",DG:"refineUnboundedInterval",JY:"jyUnboundedInterval",AF:"dateUnboundedInterval",AE:"dateUnboundedInterval",AI:"dateUnboundedInterval",CD:"dateUnboundedInterval",LB:"scoreTableUnboundedInterval"},L={2:">",5:">=",4:"=",1:"<=",3:"<"},F={name:"addOne",components:{MyDateMultiPicker:P},props:{ykz063:{type:String},ykz001:{type:String},column:{type:Array}},data:function(){return{customShowTypes:U,intervalRange:L,attrs:[],notNullArr:[],selectAttr:[],customShowType:{unboundedInterval:{isShow:!1,field:"aaa005"},refineUnboundedInterval:{isShow:!1,field:"aaa005"},jyUnboundedInterval:{isShow:!1,field:"ykz194"},dateUnboundedInterval:{isShow:!1,field:"aaa005"},scoreTableUnboundedInterval:{isShow:!1}},jyIntervalType:null,intervalType:null,dateIntervalType:"1",isAppointDate:"1",intervalRangeArr:[],intervalCompareTypeArr:[],ageUnitArr:[],dateFormat:"YYYY.MM.DD",dateFormatHHmmss:"YYYY.MM.DD.HH.mm.ss",dateRangeArr:[],endOpen:!1,errorTips:{intervalType:{isError:!1,errorMessage:"请选择区间类型"},intervalType1:{isError:!1,errorMessage:""},intervalType2:{isError:!1,errorMessage:""}}}},mounted:function(){this.initData(),this.notNullArr=this.column.filter((function(e){return e.require})).map((function(e){return e.id}))},methods:{intervalTypeChange:function(e){var t=this;this.intervalRangeArr=[null,null],this.intervalCompareTypeArr=[null,null],this.dateRangeArr=[null,null],this.ageUnitArr=[null,null],this.intervalTypeCheck(e);var a=Object.keys(this.errorTips);a.forEach((function(e){"intervalType"!=e&&(t.errorTips[e].isError=!1,t.errorTips[e].errorMessage="")})),this.intervalType=e},intervalTypeCheck:function(e){this.isNull(e)?this.errorTips.intervalType.isError=!0:this.errorTips.intervalType.isError=!1},jyIntervalTypeChange:function(e){var t=this;this.jyIntervalType=e;var a=Object.keys(this.errorTips);a.forEach((function(e){"intervalType"!=e?(t.errorTips[e].isError=!1,t.errorTips[e].errorMessage=""):t.errorTips[e].isError=!1}))},dateIntervalTypeChange:function(e){this.dateIntervalType=e},isAppointDateChange:function(e){this.isAppointDate=e},onIntervalCompareTypeChange:function(e,t){this.$set(this.intervalCompareTypeArr,t,e),this.checkIntervalRange(t)},checkDateIsExist:function(){this.errorTips.intervalType1.errorMessage="",this.isNull(this.dateRangeArr[0])?(this.errorTips.intervalType1.isError=!0,this.errorTips.intervalType1.errorMessage="请选择时间范围!"):this.errorTips.intervalType1.isError=!1},checkIntervalRange:function(e){if(this.customShowType.dateUnboundedInterval.isShow)if("1"==this.intervalType)this.checkIntervalType1(e);else{this.errorTips.intervalType2.errorMessage="";var t=this.checkArrIsAllExist(this.intervalCompareTypeArr);t&&(this.errorTips.intervalType2.isError=!0,this.errorTips.intervalType2.errorMessage+="请选择比较类型!");var a=this.checkArrIsAllExist(this.dateRangeArr);a&&(this.errorTips.intervalType2.isError=!0,this.errorTips.intervalType2.errorMessage?this.errorTips.intervalType2.errorMessage+=",请选择时间范围!":this.errorTips.intervalType2.errorMessage+="请选择时间范围!"),t||a||(this.errorTips.intervalType2.isError=!1);var r=this.checkDateRange(this.dateRangeArr);r&&(this.errorTips.intervalType2.isError=!0,this.errorTips.intervalType2.errorMessage?this.errorTips.intervalType2.errorMessage+=",时间范围有误!开始时间大于结束时间":this.errorTips.intervalType2.errorMessage+="时间范围有误!开始时间大于结束时间")}else this.onIntervalRangeChange(this.intervalRangeArr[e],e)},checkDateRange:function(e){if(2!=e.length)return alert("!=2"),!1;for(var t=e[0].split("."),a=e[1].split("."),r=0;r<t.length;r++){var n=t[r],i=a[r];if("*"==n||"*"==i)return!1;if(n=parseInt(n),i=parseInt(i),n>i)return!0}return!1},checkArrIsAllExist:function(e){for(var t=!1,a=0;a<e.length;a++)if(t=this.isNull(e[a]),t)break;return t},covertApe995:function(e){return this.intervalRange[e]?this.intervalRange[e]:""},onIntervalRangeChange:function(e,t){if("2"==this.intervalType){var a=null;1==t&&this.intervalRangeArr[0]&&(5==this.intervalCompareTypeArr[0]?1==this.intervalCompareTypeArr[1]?e<this.intervalRangeArr[0]&&(a="区间错误"):e<=this.intervalRangeArr[0]&&(a="区间错误"):1==this.intervalCompareTypeArr[1]?e<this.intervalRangeArr[0]+1&&(a="区间错误"):e<=this.intervalRangeArr[0]+1&&(a="区间错误")),0==t&&this.intervalRangeArr[1]&&(1==this.intervalCompareTypeArr[1]?5==this.intervalCompareTypeArr[0]?e>this.intervalRangeArr[1]&&(a="区间错误"):e>=this.intervalRangeArr[1]&&(a="区间错误"):5==this.intervalCompareTypeArr[0]?e>this.intervalRangeArr[1]-1&&(a="区间错误"):e>=this.intervalRangeArr[1]-1&&(a="区间错误")),this.checkIntervalType2(a)}else this.checkIntervalType1(t)},checkIntervalType1:function(e){var t=this.isNull(this.intervalCompareTypeArr[e]),a=!1,r=!1;if(this.errorTips.intervalType1.errorMessage="",t&&(this.errorTips.intervalType1.isError=!0,this.errorTips.intervalType1.errorMessage+="请选择比较类型!"),this.customShowType.dateUnboundedInterval.isShow?(r=this.isNull(this.dateRangeArr[e]),r&&(this.errorTips.intervalType1.isError=!0,this.errorTips.intervalType1.errorMessage?this.errorTips.intervalType1.errorMessage+=",请选择时间!":this.errorTips.intervalType1.errorMessage="请选择时间!")):(a=this.isNull(this.intervalRangeArr[e]),a&&(this.errorTips.intervalType1.isError=!0,this.errorTips.intervalType1.errorMessage?this.errorTips.intervalType1.errorMessage+=",请输入阈值!":this.errorTips.intervalType1.errorMessage="请输入阈值!")),this.customShowType.refineUnboundedInterval.isShow){var n=this.isNull(this.ageUnitArr[e]);n&&(this.errorTips.intervalType1.isError=!0,this.errorTips.intervalType1.errorMessage?this.errorTips.intervalType1.errorMessage+=",请选择单位!":this.errorTips.intervalType1.errorMessage="请选择单位!"),t||a||n||(this.errorTips.intervalType1.isError=!1)}else this.customShowType.dateUnboundedInterval.isShow?t||r||(this.errorTips.intervalType1.isError=!1):t||a||(this.errorTips.intervalType1.isError=!1)},checkIntervalType2:function(e){if(this.errorTips.intervalType2.errorMessage="",this.checkArrIsAllExist(this.intervalCompareTypeArr)&&(this.errorTips.intervalType2.isError=!0,this.errorTips.intervalType2.errorMessage+="请选择比较类型!"),this.checkArrIsAllExist(this.intervalRangeArr)&&(this.errorTips.intervalType2.isError=!0,this.errorTips.intervalType2.errorMessage?this.errorTips.intervalType2.errorMessage+=",请输入阈值!":this.errorTips.intervalType2.errorMessage="请输入阈值!"),this.isNull(e)||(this.errorTips.intervalType2.isError=!0,this.errorTips.intervalType2.errorMessage?this.errorTips.intervalType2.errorMessage+=","+e:this.errorTips.intervalType2.errorMessage=e),this.customShowType.refineUnboundedInterval.isShow){var t=this.checkArrIsAllExist(this.ageUnitArr);t&&(this.errorTips.intervalType2.isError=!0,this.errorTips.intervalType2.errorMessage?this.errorTips.intervalType2.errorMessage+=",请选择单位!":this.errorTips.intervalType2.errorMessage="请选择单位!"),this.checkArrIsAllExist(this.intervalCompareTypeArr)||this.checkArrIsAllExist(this.intervalRangeArr)||!this.isNull(e)||t||(this.errorTips.intervalType2.isError=!1)}else this.checkArrIsAllExist(this.intervalCompareTypeArr)||this.checkArrIsAllExist(this.intervalRangeArr)||!this.isNull(e)||(this.errorTips.intervalType2.isError=!1)},onAgeUnitArrChange:function(e,t){this.ageUnitArr[t]=e,this.checkIntervalRange(t)},disabledStartDate:function(e){var t=this.dateRangeArr[1];return!(!e||!t)&&e.valueOf()>t.valueOf()},disabledEndDate:function(e){var t=this.dateRangeArr[0];return!(!e||!t)&&t.valueOf()>=e.valueOf()},handleStartOpenChange:function(e){e||(this.endOpen=!0)},handleEndOpenChange:function(e){this.endOpen=e},getCustomShowFieldValue:function(){var e="";switch(this.customShowTypes[this.ykz063]){case"unboundedInterval":if("1"==this.intervalType)e+=this.intervalRange[this.intervalCompareTypeArr[0]]+this.intervalRangeArr[0].toString();else for(var t=0;t<this.intervalCompareTypeArr.length;t++)e+=this.intervalRange[this.intervalCompareTypeArr[t]]+this.intervalRangeArr[t].toString(),t!=this.intervalCompareTypeArr.length-1&&(e+="&");break;case"refineUnboundedInterval":if("1"==this.intervalType)e+=this.intervalRange[this.intervalCompareTypeArr[0]]+this.intervalRangeArr[0].toString()+this.ageUnitArr[0];else for(var a=0;a<this.intervalCompareTypeArr.length;a++)e+=this.intervalRange[this.intervalCompareTypeArr[a]]+this.intervalRangeArr[a].toString()+this.ageUnitArr[a],a!=this.intervalCompareTypeArr.length-1&&(e+="&");break;case"jyUnboundedInterval":if("1"==this.jyIntervalType)if("1"==this.intervalType)e+=this.intervalRange[this.intervalCompareTypeArr[0]]+this.intervalRangeArr[0].toString();else for(var r=0;r<this.intervalCompareTypeArr.length;r++)e+=this.intervalRange[this.intervalCompareTypeArr[r]]+this.intervalRangeArr[r].toString(),r!=this.intervalCompareTypeArr.length-1&&(e+="&");break;case"dateUnboundedInterval":e="1"==this.dateIntervalType?"="+(this.dateRangeArr[0]?this.dateRangeArr[0]:""):"1"==this.intervalType?this.covertApe995(this.intervalCompareTypeArr[0])+(this.dateRangeArr[0]?this.dateRangeArr[0]:""):this.covertApe995(this.intervalCompareTypeArr[0])+(this.dateRangeArr[0]?this.dateRangeArr[0]:"")+"&"+this.covertApe995(this.intervalCompareTypeArr[1])+(this.dateRangeArr[0]?this.dateRangeArr[1]:"");break;default:break}return e},initData:function(){var e=this,t={url:"mtt/localruleconfig/localCotent/getFormInfo",data:{columns:JSON.stringify(this.column),ykz001:this.ykz001}},a={successCallback:function(t){if(t.data.success){e.notNullArr=t.data.notNullArr;var a=t.data.attrs;a=a.filter((function(t){var a=!1;return"aaa005"!=t.id&&"ykz194"!=t.id&&"ykz044"!=t.id&&"ykz043"!=t.id&&"aae421"!=t.id||!e.customShowTypes[e.ykz063]||(e.customShowType[e.customShowTypes[e.ykz063]].isShow=!0,a=!0),!a})),e.attrs=a;var r=t.data.selectAttr;r=r.filter((function(t){var a=!1;return"ykz193"!=t.id&&"ape995"!=t.id||!e.customShowTypes[e.ykz063]||(e.customShowType[e.customShowTypes[e.ykz063]].isShow=!0,a=!0),!a})),e.selectAttr=r}else e.$message.error("初始化数据失败")}};this.Base.submit("",t,a)},save:function(){var e=this;for(var t in"dateUnboundedInterval"==this.customShowTypes[this.ykz063]?"2"==this.dateIntervalType?(this.intervalTypeCheck(this.intervalType),this.checkIntervalRange(0)):this.checkDateIsExist():this.customShowTypes[this.ykz063]&&"scoreTableUnboundedInterval"!=this.customShowTypes[this.ykz063]&&(this.intervalTypeCheck(this.intervalType),this.checkIntervalRange(0)),this.errorTips)if(this.errorTips[t].isError)return;this.addForm.validateFields((function(t){if(!t){var a=e.addForm.getFieldsValue();if(e.customShowTypes[e.ykz063]&&e.customShowType[e.customShowTypes[e.ykz063]].isShow)if("scoreTableUnboundedInterval"==e.customShowTypes[e.ykz063])a.ykz043=a.socreTable.label,a.ykz044=a.socreTable.key,delete a.socreTable;else{var r=e.getCustomShowFieldValue();r&&(a[e.customShowType[e.customShowTypes[e.ykz063]].field]=r)}e.$emit("fnAddOne",a)}}))},isNull:function(e){return 0!=e&&(!e||("{}"===JSON.stringify(e)||"[]"===JSON.stringify(e)))}}},O=F,Y=(0,p.Z)(O,_,D,!1,null,"0b74dd52",null),q=Y.exports,Z=function(){var e=this,t=this,a=t.$createElement,r=t._self._c||a;return r("div",{staticStyle:{height:"100%"}},[r("ta-form",{attrs:{autoFormCreate:function(t){e.reasonForm=t}}},[r("ta-form-item",{attrs:{fieldDecoratorId:"ykz137",label:"更新原因",require:!0}},[r("ta-input",{staticStyle:{width:"80%"}})],1),r("ta-form-item",{attrs:{fieldDecoratorId:"ykz138",label:"更新内容说明",require:!0}},[r("ta-input",{staticStyle:{width:"80%"}})],1),r("ta-form-item",{attrs:{fieldDecoratorId:"ykz163",label:"依据",require:!0}},[r("ta-input",{staticStyle:{width:"80%"}})],1),r("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}],attrs:{fieldDecoratorId:"ykz149",label:"是否参与打包",require:!0,initValue:["1"]}},[r("ta-select",{staticStyle:{width:"80%"},on:{change:t.fnChange}},[r("ta-select-option",{attrs:{value:"0"}},[t._v("不参与打包")]),r("ta-select-option",{attrs:{value:"1"}},[t._v("参与打包")])],1)],1),r("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}],attrs:{fieldDecoratorId:"ykz152",label:"设置原因",require:!0}},[r("ta-input",{staticStyle:{width:"80%"},attrs:{disabled:t.flag}})],1),r("ta-button",{staticStyle:{"margin-left":"45%"},attrs:{type:"success"},on:{click:t.save}},[t._v("保存")])],1)],1)},B=[],j=a(66353),W=a(89584),J=["plChoose","_XID","blnAdd","blnDelete","rid"],H={name:"saveReason",components:{},props:{disData:{type:Array},addData:{type:Array},nodeInfos:{type:Object}},data:function(){return{moudleUrl:"mtt/localruleconfig/localCotent",flag:!0}},mounted:function(){this.reasonForm.setFieldsValue({ykz152:"默认打包"})},methods:{fnChange:function(e,t){"0"===e&&(this.flag=!1,this.reasonForm.setFieldsValue({ykz152:""})),"1"===e&&(this.flag=!0,this.reasonForm.setFieldsValue({ykz152:"默认打包"}))},save:function(){var e=this;if(null!==this.addData&&null!==this.disData){for(var t=this.moudleUrl+"/saveContentResult",a=this.reasonForm.getFieldsValue(),r=(0,W.Z)(this.addData),n=0;n<r.length;n++){var o=r[n],s=(0,i.Z)({},o),l=(s.plChoose,s._XID,s.blnAdd,s.blnDelete,s.rid,(0,j.Z)(s,J));r[n]=(0,i.Z)({},l),r[n].ape888="1"}var c={disData:JSON.stringify(this.disData),addData:JSON.stringify(r),ykz137:a.ykz137,ykz138:a.ykz138,ykz163:a.ykz163,ykz149:a.ykz149[0],ykz152:a.ykz152,ykz001:this.nodeInfos.ykz001,ykz042:this.nodeInfos.ykz042,ykz010:this.nodeInfos.ykz010},d={url:t,data:c,autoValid:!0},p={successCallback:function(t){t.data.success?(e.$emit("closeSaved"),e.$message.success("保存成功")):e.$message.error(t.errors[0].msg)}};this.Base.submit(this.reasonForm,d,p)}else this.$message.warn("请先进行数据操作！")}}},G=H,K=(0,p.Z)(G,Z,B,!1,null,"1bf278b6",null),X=K.exports,Q={name:"nodeContent",props:["clickedNode"],components:{addContentOne:q,importData:R,saveReason:X},data:function(){return{moudleUrl:"mtt/localruleconfig/localCotent",contentColumns:[{dataIndex:"ykz002",title:"节点类型",width:100,align:"center",overflowTooltip:!0},{dataIndex:"ykz010",title:"节点名称",width:100,align:"center",overflowTooltip:!0}],contentList:[],contentSearchText:void 0,addContentVisible:!1,importContentVisible:!1,importNodeInfo:{},contentListCache:[],maxRid:0,conditionColumns:[],reasonVisible:!1,disData:[],addData:[],disableVisible:!1,disableDesc:void 0,hideNodes:["AS","DC","DJ","EX","IT","KD","RM","ST","TP","TY","YX","ZE","ZY"]}},mounted:function(){this.queryContentColumns()},methods:{freshData:function(){this.contentSearchText=void 0,this.getContentList()},contentCustomRow:function(e){var t=e.row,a=(e.rowIndex,"white");return t.blnAdd&&(a="#d0ffbf"),t.blnDelete&&(a="#f5ccd0"),{cursor:"pointer",backgroundColor:a}},queryContentColumns:function(){var e=this;Base.submit(null,{url:this.moudleUrl+"/queryContentColumns",data:{ykz001:this.clickedNode.ykz001}}).then((function(t){e.contentColumns=t.data.info.list,e.conditionColumns=t.data.info.conditionColumns,e.$nextTick((function(){e.getContentList()}))}))},contentPageParam:function(){return{ykz001:this.clickedNode.ykz001,ykz042:this.clickedNode.ykz042,text:this.contentSearchText}},pageParam:function(){return{ykz001:this.clickedNode.ykz001,ykz042:this.clickedNode.ykz042,searchText:this.contentSearchText}},getContentList:function(){this.$refs.pager.loadData()},onNodeContentSearch:function(){this.getContentList()},findMaxRid:function(){this.maxRid=0;var e,t=(0,l.Z)(this.contentList);try{for(t.s();!(e=t.n()).done;){var a=e.value;parseInt(this.maxRid)<parseInt(a.rid)&&(this.maxRid=parseInt(a.rid))}}catch(r){t.e(r)}finally{t.f()}},downloadContentTemplate:function(){var e=this,t={url:this.moudleUrl+"/downloadContentTemplate",data:{ykz001:this.clickedNode.ykz001},autoValid:!0},a={successCallback:function(t){if(t.data.success){var a=t.data.fileName,r=t.data.data,n=document.createElement("a");n.download=a,n.href="data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,"+r,n.click(),e.$message.success("下载成功")}else e.$message.error(t.errors[0].msg)}};this.Base.submit("",t,a)},openAddContent:function(){this.addContentVisible=!0},fnAddContentOneSuccess:function(e){e.ykz042=this.clickedNode.ykz042,e.ykz010=this.clickedNode.ykz010,e.blnAdd=!0,this.contentListCache.push(e),this.addContentVisible=!1,this.$message.success("新增成功")},deleteContent:function(){var e=this.$refs.contentTable.getCheckboxRecords();0!=e.length||this.$message.warn("请勾选要移除的行")},openDisable:function(){this.disableDesc=void 0,this.disableVisible=!0},saveDisableDesc:function(){this.freshRid();var e=this.$refs.contentTable.getCheckboxRecords().map((function(e){return e.rid})),t=this.$refs.contentTable.getCheckboxRecords();if(0!=t.length){var a=t.filter((function(e){return e.blnAdd}));if(a.length>0)this.$message.warn("不能禁用未保存的记录");else if(this.disableDesc){for(var r=0;r<this.contentListCache.length;r++)e.includes(this.contentListCache[r].rid)&&(this.contentListCache[r].blnDelete=!0,this.contentListCache[r].ykz095=this.disableDesc,this.$set(this.contentListCache,r,this.contentListCache[r]));this.disableVisible=!1}else this.$message.warn("请输入禁用原因")}else this.$message.warn("请勾选要禁用的行")},freshRid:function(){var e,t=0,a=(0,l.Z)(this.contentListCache);try{for(a.s();!(e=a.n()).done;){var r=e.value;r.rid=t,t++}}catch(n){a.e(n)}finally{a.f()}},removeContent:function(){this.freshRid();var e=this.$refs.contentTable.getCheckboxRecords(),t=this.$refs.contentTable.getCheckboxRecords().map((function(e){return e.rid}));if(0!=e.length){var a=e.filter((function(e){return!e.blnAdd}));a.length>0?this.$message.warn("只能移除未保存的记录"):this.contentListCache=this.contentListCache.filter((function(e){return!t.includes(e.rid)}))}else this.$message.warn("请勾选要移除的行")},openImportContent:function(){this.importContentVisible=!0},onContentImportSuccess:function(e){var t,a=(0,l.Z)(e);try{for(a.s();!(t=a.n()).done;){var r=t.value;r.ykz042=this.clickedNode.ykz042,r.ykz010=this.clickedNode.ykz010,r.rid=++this.maxRid,r.blnAdd=!0,this.contentListCache.push(r)}}catch(n){a.e(n)}finally{a.f()}this.importContentVisible=!1},doSave:function(){var e=this.contentListCache.filter((function(e){return e.blnDelete})),t=this.contentListCache.filter((function(e){return e.blnAdd}));0!=e.length||0!=t.length?(this.disData=e,this.addData=t,this.reasonVisible=!0):this.$message.warn("没有需要保存的数据")},closeSaved:function(){this.getContentList(),this.reasonVisible=!1},hasHideNode:function(){return this.hideNodes.includes(this.clickedNode.ykz063)||!this.clickedNode.editable}}},ee=Q,te=(0,p.Z)(ee,C,w,!1,null,"4bcdac64",null),ae=te.exports,re=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{height:"100%"}},[a("ta-table",{ref:"mkf49Table",attrs:{size:"small",haveSn:!0,columns:e.columnsMkf49,dataSource:e.dataMkf49,scroll:{y:330,x:"100%"},bordered:"",headerTitleNowrap:!0},on:{"update:columns":function(t){e.columnsMkf49=t}}}),a("ta-pagination",{ref:"mkf49Pager",staticClass:"page",attrs:{dataSource:e.dataMkf49,defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"],params:e.getPageParam,url:"mtt/localruleconfig/localCotent/queryMkf49ByPage"},on:{"update:dataSource":function(t){e.dataMkf49=t},"update:data-source":function(t){e.dataMkf49=t}}})],1)},ne=[],ie={name:"updateLog",components:{},props:{ykz042:{type:String}},data:function(){var e=[{dataIndex:"ykz136",title:"更新ID",width:100,align:"center"},{dataIndex:"ykz042",title:"节点编号",width:100,align:"center"},{dataIndex:"ykz137",title:"更新原因",width:150,align:"center",overflowTooltip:!0},{dataIndex:"ykz138",title:"更新内容说明",width:150,align:"center",overflowTooltip:!0},{dataIndex:"operationlog",title:"操作说明",width:150,align:"center",overflowTooltip:!0},{dataIndex:"operationtype",title:"操作类型",width:100,align:"center"},{dataIndex:"operationuser",title:"操作人员",width:100,align:"center",overflowTooltip:!0},{dataIndex:"operationtime",title:"操作时间",width:200,align:"center"},{dataIndex:"ykz150",title:"设置人员",width:100,align:"center",overflowTooltip:!0},{dataIndex:"ykz151",title:"设置时间",width:200,align:"center"},{dataIndex:"ykz163",title:"依据",width:150,align:"center",overflowTooltip:!0},{dataIndex:"itemver",title:"版本号",width:100,align:"center"},{dataIndex:"ykz001",title:"节点类型",width:100,align:"center"}];return{columnsMkf49:e,dataMkf49:[],selectedRows:[],rowSelection:{selectedRowKeys:[],onChange:this.onSelectChange},visible:!1}},mounted:function(){this.$refs.mkf49Table.hideColumns(["ykz136"]),this.$refs.mkf49Pager.loadData()},methods:{handleCancel:function(e){this.visible=!1},getPageParam:function(){return{ykz042:this.ykz042}},onSelectChange:function(e,t){this.rowSelection.selectedRowKeys=e,this.selectedRows=t},isPackage:function(e){var t=this;this.selectedRows.length<1?this.$message.warning("请选择数据"):0===e?this.visible=!0:this.$confirm({title:"确认参与导包？",onOk:function(){t.save(e)},onCancel:function(){}})},save:function(e){var t,a=this,r="",n=(0,l.Z)(this.selectedRows);try{for(n.s();!(t=n.n()).done;){var i=t.value;r+=i.ykz136+","}}catch(p){n.e(p)}finally{n.f()}var o="";o=0===e?this.form.getFieldValue("ykz152"):"默认打包";var s="ruleConfig/isPackage",c={url:s,data:{ykz136:r,ykz152:o,ykz149:e}},d={successCallback:function(e){e.data.success?(a.$message.success("更新完成"),a.visible=!1,a.$refs.mkf49Pager.loadData()):a.$message.error("更新失败")}};this.Base.submit("",c,d)}}},oe=ie,se=(0,p.Z)(oe,re,ne,!1,null,"9b385cec",null),le=se.exports,ce={name:"nodeData",props:{blnPlatform:Boolean},components:{nodeContent:ae,updateLog:le},computed:{pageUrl:function(){return this.moudleUrl+"/pageNodeData"},mkeDownloadUrl:function(){return this.moudleUrl+"/downloadMke"}},data:function(){return{moudleUrl:"mtt/localruleconfig/localCotent",nodeColumn:[{dataIndex:"ykz010",title:"节点名称",width:150,align:"center",overflowTooltip:!0},{dataIndex:"ykz042",title:"节点编码",width:150,align:"center",overflowTooltip:!0},{dataIndex:"ykz065",title:"节点说明",width:250,align:"center",overflowTooltip:!0},{dataIndex:"needsync",title:"同步引擎",width:100,align:"center",overflowTooltip:!0,scopedSlots:{customRender:"needsync"}},{dataIndex:"relative",title:"对照说明",width:100,align:"center",overflowTooltip:!0,scopedSlots:{customRender:"relative"}},{dataIndex:"operationuser",title:"对照人",width:150,align:"center",overflowTooltip:!0},{dataIndex:"operationtime",title:"对照时间",width:160,align:"center",overflowTooltip:!0},{dataIndex:"log",title:"查看记录",width:100,align:"center",overflowTooltip:!0,scopedSlots:{customRender:"log"}}],nodeData:[],pageParam:{pageNumber:1},searchParam:{},visible:!1,ykz072:"",nodeInfos:{},reload:!0,logVisible:!1,blnNodeContentVisible:!1,clickNode:{}}},watch:{blnNodeContentVisible:function(e){e||this.$refs.pager.loadData()}},methods:{syncPackage:function(e){var t=this;"1"==e.relative?Base.submit(null,{url:"mttRulePublish/syncPackage",data:{ykz042:e.ykz042,ykz001:this.searchParam.ykz001}}).then((function(e){t.$message.success("同步成功"),t.$refs.pager.loadData()})):this.$message.warn("包体没有数据！请配置")},nodeParam:function(){return(0,i.Z)({},this.searchParam)},loadData:function(e){this.pageParam.pageNumber=1,this.searchParam=e,this.$refs.pager.loadData()},getNodeData:function(){return this.nodeData},reloadChild:function(){var e=this;this.reload=!1,this.$nextTick((function(){e.reload=!0}))},handleCancel:function(e){this.logVisible=!1},handleNodeCancel:function(e){this.visible=!1,this.$refs.pager.loadData()},configNode:function(e){this.clickNode=e,this.clickNode.ykz001=this.searchParam.ykz001,this.clickNode.ykz063=this.searchParam.ykz063,this.clickNode.editable=this.searchParam.editable,this.blnNodeContentVisible=!0},nodeLog:function(e){this.nodeInfos=e,this.logVisible=!0},downloadMke:function(e){var t=this;Base.submit(null,{url:this.mkeDownloadUrl,data:{ykz001:this.searchParam.ykz001,ykz042:e.ykz042}}).then((function(a){if(a.data.success){var r=e.ykz042+"-包体内容.xlsx",n=a.data.data,i=document.createElement("a");i.download=r,i.href="data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,"+n,i.click()}else t.$message.error("下载数据失败")}))}}},de=ce,pe=(0,p.Z)(de,b,k,!1,null,"060142aa",null),he=pe.exports,ue={name:"nodeContent",components:{nodeTypeList:u,nodeSearch:T,nodeData:he,importData:R,nodeContent:ae},data:function(){return{moudleUrl:"mtt/localruleconfig/localCotent",searchParam:{},ykz001:null,nodeInfo:{},importVisible:!1,blnAddNodeVisible:!1,addNodeConfStyle:{nodeConfSpan:23}}},mounted:function(){},methods:{openAddNode:function(){this.blnAddNodeVisible=!0},loadData:function(){this.$refs.nodeData.loadData(this.searchParam)},nodeTypeSelect:function(e){this.ykz001=e.ykz001,this.searchParam.ykz001=e.ykz001,this.searchParam.ykz063=e.ykz063,this.searchParam.editable=e.editable,this.nodeInfo.ykz001=e.ykz001,this.nodeInfo.ykz063=e.ykz063,this.nodeInfo.editable=e.editable,this.loadData()},addNode:function(){var e=this;this.nodeConfForm.validateFields((function(t){if(!t){var a=e.nodeConfForm.getFieldsValue();Base.submit(null,{url:"mttRuleConfig/addNode",data:{dtoStr:JSON.stringify((0,i.Z)({ykz001:e.ykz001},a))}}).then((function(t){e.$message.success("新增成功"),e.blnAddNodeVisible=!1,e.onSearch()}))}}))},onSearch:function(){var e=this.searchForm.getFieldsValue(),t=this.$refs.nodeData.getNodeData();null!=this.searchParam.ykz001||0!==t.length?(this.searchParam=e,Object.assign(this.searchParam,this.nodeInfo),this.searchParam.ykz001=this.ykz001,this.loadData()):this.$message.warn("请先选择节点类型")},onDownloadTemplate:function(e){var t=this,a=this.$refs.nodeData.getNodeData();if(null!=this.searchParam.ykz001||0!==a.length){var r={url:this.moudleUrl+"/downloadContentTemplate",data:{ykz001:this.ykz001},autoValid:!0},n={successCallback:function(e){if(e.data.success){var a=e.data.fileName,r=e.data.data,n=document.createElement("a");n.download=a,n.href="data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,"+r,n.click(),t.$message.success("下载成功")}else t.$message.error(e.errors[0].msg)}};this.Base.submit("",r,n)}else this.$message.warn("请先选择节点类型")},onExportData:function(e){var t=this,a=this.$refs.nodeData.getNodeData();null!=this.searchParam.ykz001||0!==a.length?(this.searchParam=e,this.searchParam.ykz001=this.ykz001,Base.submit(null,{url:"nodeContent/exportData",data:(0,i.Z)({},this.searchParam)}).then((function(e){if(e.data.success){var a=e.data.fileName,r=e.data.data,n=document.createElement("a");n.download=a,n.href="data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,"+r,n.click()}else t.$message.error("下载规则路径数据失败")}))):this.$message.warn("请先选择节点类型")},onImportData:function(){null!=this.searchParam.ykz001?this.importVisible=!0:this.$message.warn("请先选择节点类型")},onImportSuccess:function(){this.importVisible=!1,this.loadData()}}},me=ue,fe=(0,p.Z)(me,r,n,!1,null,"8aff5cca",null),ve=fe.exports}}]);
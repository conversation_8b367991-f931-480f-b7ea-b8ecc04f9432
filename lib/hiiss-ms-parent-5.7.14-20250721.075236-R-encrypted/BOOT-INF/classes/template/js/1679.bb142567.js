(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[1679],{88412:function(e,t,a){"use strict";var i=a(26263),r=a(36766),n=a(1001),s=(0,n.Z)(r.Z,i.s,i.x,!1,null,"5e7ef0ae",null);t["Z"]=s.exports},98124:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return h}});var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"fit"},[a("ta-border-layout",{attrs:{"layout-type":"fixTop"},scopedSlots:e._u([{key:"header",fn:function(){return[a("ta-form",{staticStyle:{"margin-top":"20px"},attrs:{autoFormCreate:e.autoFormCreate,col:3,formLayout:!0}},[a("ta-form-item",{attrs:{require:!1,showInfo:!0,"field-decorator-id":"diseaseInfo",label:"病种信息","label-col":{span:7},span:5,"wrapper-col":{span:17}}},[a("ta-input",{attrs:{placeholder:"请输入病种名称或编码"}})],1),a("ta-form-item",{attrs:{require:!1,showInfo:!0,"field-decorator-id":"itemInfo",label:"项目信息","label-col":{span:7},span:5,"wrapper-col":{span:17}}},[a("ta-input",{attrs:{placeholder:"请输入医保项目名称或编码"}})],1),a("ta-form-item",{staticStyle:{"text-align":"right"},attrs:{require:!1,showInfo:!0,label:" ","label-col":{span:6},span:14,"wrapper-col":{span:18}}},[a("ta-button",{staticStyle:{"margin-right":"10px"},attrs:{type:"primary"},on:{click:e.queryTableData}},[e._v("查询 ")]),a("ta-button",{staticStyle:{"margin-right":"10px"},on:{click:e.fnReset}},[e._v("重置 ")]),a("ta-button",{staticStyle:{"margin-right":"10px"},on:{click:e.downloadTemplate}},[e._v("下载模板 ")]),a("ta-upload",{attrs:{name:"file",multiple:!0,headers:e.headers,"file-list":e.fileList,"before-upload":e.beforeUpload,"show-upload-list":!1},on:{change:e.handleChange}},[a("ta-button",{staticStyle:{"margin-right":"10px"}},[e._v("导入 ")])],1)],1)],1)]},proxy:!0}])},[a("ta-card",{staticClass:"fit"},[a("ta-title",{attrs:{title:"结果查询"}}),a("ta-big-table",{ref:"infoTableRef",attrs:{data:e.tableData,height:"95%","export-config":{},"import-config":{},border:"","highlight-hover-row":"","highlight-current-row":"","auto-resize":"","show-overflow":"",resizable:"","keep-source":"","edit-rules":e.validRules,"edit-config":{trigger:"manual",mode:"row",autoClear:!1},size:"large"}},[a("ta-big-table-column",{attrs:{width:50,title:"序号",type:"seq"}}),a("ta-big-table-column",{attrs:{align:"center",field:"diseaseCode","header-align":"center",title:"病种编码","edit-render":{name:"$input",style:{color:"red",width:"100%"}}}}),a("ta-big-table-column",{attrs:{align:"center",field:"diseaseName","header-align":"center",title:"病种名称","edit-render":{name:"$input",style:{color:"red",width:"100%"}}}}),a("ta-big-table-column",{attrs:{align:"center",field:"itemCode","header-align":"center",title:"医保项目编码","edit-render":{name:"$input",style:{color:"red",width:"100%"}}}}),a("ta-big-table-column",{attrs:{align:"center",field:"itemName","header-align":"center",title:"医保项目名称","edit-render":{name:"$input",style:{color:"red",width:"100%"}}}}),a("ta-big-table-column",{attrs:{align:"center",field:"ruleDetail","header-align":"center",title:"医保限制条件"}}),a("ta-big-table-column",{attrs:{align:"center",field:"updateTime","header-align":"center",title:"更新时间"}}),a("ta-big-table-column",{attrs:{width:150,align:"center","header-align":"center",title:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.row,r=t.rowIndex;return[e.editIsActive(i)?[a("a",{on:{click:function(t){return e.editSave(i,r)}}},[e._v(" 保存 ")]),a("ta-divider",{attrs:{type:"vertical"}}),a("a",{on:{click:function(t){return e.editCancel(i)}}},[e._v(" 取消 ")])]:[a("a",{on:{click:function(t){return e.editActive(i)}}},[e._v(" 修改 ")]),a("ta-divider",{attrs:{type:"vertical"}}),a("ta-popconfirm",{attrs:{title:"确认删除该条数据?","ok-text":"确认","cancel-text":"取消"},on:{confirm:function(t){return e.fnDelete(i)}}},[a("a",[e._v(" 删除 ")])])]]}}])}),a("template",{slot:"bottomBar"},[a("ta-pagination",{ref:"gridPager",staticStyle:{"text-align":"right","margin-right":"120px"},attrs:{dataSource:e.tableData,defaultPageSize:200,pageSizeOptions:["30","50","100","200","500"],params:e.getParam,url:"diseaseManage/queryDiseaseItemInfoPage"},on:{"update:dataSource":function(t){e.tableData=t},"update:data-source":function(t){e.tableData=t}}}),a("ta-button",{staticStyle:{position:"absolute",right:"20px",bottom:"12px"},attrs:{type:"primary",icon:"upload"},on:{click:e.exportExcel}},[e._v("导出 ")])],1)],2)],1)],1)],1)},r=[],n=a(66347),s=a(95082),l=a(88412),o=a(22722),c=a(55115);c.w3.prototype.Base=Object.assign(c.w3.prototype.Base,(0,s.Z)({},o.Z));var u={name:"diseaseMatchMg",components:{TaTitle:l.Z},data:function(){return{tableData:[],validRules:{diseaseCode:[{required:!0,message:"必须填写病种编码!"}],diseaseName:[{required:!0,message:"必须填写病种名称!"}],itemCode:[{required:!0,message:"必须填写医保项目编码!"}],itemName:[{required:!0,message:"必须填写医保项目名称!"}]},isAdd:!0,fileList:[],headers:{authorization:"authorization-text"},drawerVisible:!1,selectedData:{}}},mounted:function(){this.queryTableData()},methods:{beforeUpload:function(e){if(e.size/1024>51200)return this.$message.warning("请选择不超过50M的文件"),!1;var t=e.name.split("."),a=t[t.length-1];return-1===["xls","xlsx","csv"].indexOf(a)?(this.$message.warning("只能上传excel文件"),!1):(this.fileList=[e],this.baseInfoForm.setFieldsValue({fileName:e.name}),!1)},editIsActive:function(e){return this.$refs.infoTableRef.isActiveByRow(e)},editActive:function(e){this.$refs.infoTableRef.setActiveRow(e)},editCancel:function(e){var t=this;this.$refs.infoTableRef.revertData(e).then((function(){t.$refs.infoTableRef.clearActived()}))},fnDelete:function(e){var t=this;this.Base.submit(null,{url:"diseaseManage/deleteById",data:e,autoValid:!1},{successCallback:function(e){t.$message.success("删除成功！"),t.queryTableData()},failCallback:function(e){t.$message.error("删除失败！")}})},editSave:function(e,t){var a=this;this.$refs.infoTableRef.validate(e).then((function(){a.$refs.infoTableRef.isUpdateByRow(e)?(a.Base.submit(null,{url:"diseaseManage/editDiseaseItemInfo",data:e,autoValid:!1},{successCallback:function(e){a.$message.success("保存成功！"),a.queryTableData()},failCallback:function(e){a.$message.error("保存失败！")}}),a.$refs.infoTableRef.clearActived()):message.info("数据未有改动")})).catch((function(e){e&&message.error("校验不通过！")}))},handleChange:function(e){var t=this,a=this.baseInfoForm.getFieldsValue();a.file=e.file,this.Base.submit(null,{url:"/diseaseManage/importExcel",data:a,autoQs:!1,isFormData:!0},{successCallback:function(e){t.baseInfoForm.resetFields(),t.$message.success("导入数据成功"),t.queryTableData()},failCallback:function(e){}})},queryTableData:function(){this.$refs.gridPager.loadData()},fnReset:function(){this.baseInfoForm.resetFields()},autoFormCreate:function(e){this.baseInfoForm=e},getParam:function(){return this.baseInfoForm.getFieldsValue()},downloadTemplate:function(){var e,t=[],a=this.$refs.infoTableRef.getColumns(),i=(0,n.Z)(a);try{for(i.s();!(e=i.n()).done;){var r=e.value;"序号"!==r.title&&"医保限制条件"!==r.title&&"更新时间"!==r.title&&"操作"!==r.title&&t.push({header:r.title,key:r.property,width:20})}}catch(l){i.e(l)}finally{i.f()}var s={fileName:"病种项目匹配导入模板表",sheets:[{name:"worksheet1",column:{complex:!1,columns:t},rows:[]}]};this.Base.generateExcel(s)},exportExcel:function(){var e,t=this,a=[],i=this.$refs.infoTableRef.getColumns(),r=(0,n.Z)(i);try{for(r.s();!(e=r.n()).done;){var s=e.value;"序号"!==s.title&&"操作"!==s.title&&a.push({header:s.title,key:s.property,width:20})}}catch(l){r.e(l)}finally{r.f()}this.Base.submit(null,{url:"diseaseManage/queryDiseaseItemInfoList",data:this.getParam(),autoValid:!1},{successCallback:function(e){var i={fileName:"病种项目匹配数据表",sheets:[{name:"worksheet1",column:{complex:!1,columns:a},rows:e.data.diseaseItemList}]};t.Base.generateExcel(i)},failCallback:function(e){t.$message.error("病种项目匹配数据表导出失败")}})},activeMethod:function(e){var t=e.row,a=(e.rowIndex,e.column,e.columnIndex);return 4!==a||4===a&&"2"===t.columnType},saveConfig:function(){var e=this,t=this.form_edit.getFieldsValue();this.isAdd?Base.submit(this.form_edit,{url:"addDemoModel",method:"post",data:t,autoValid:!0}).then((function(t){e.$message.success("新增成功",3),e.queryTableData(),e.drawerVisible=!1})):(t.configId=this.selectedData.configId,Base.submit(this.form_edit,{url:"updateDemoRecord",method:"post",data:t,autoValid:!0}).then((function(t){e.$message.success("修改成功",3),e.queryTableData(),e.drawerVisible=!1})))}}},d=u,f=a(1001),m=(0,f.Z)(d,i,r,!1,null,"0bc31481",null),h=m.exports},36766:function(e,t,a){"use strict";var i=a(66586);t["Z"]=i.Z},26263:function(e,t,a){"use strict";a.d(t,{s:function(){return i},x:function(){return r}});var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:e.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[e._v(e._s(e.title))]),e._t("default")],2)},r=[]},66586:function(e,t){"use strict";var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};t["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}},55382:function(){},61219:function(){}}]);
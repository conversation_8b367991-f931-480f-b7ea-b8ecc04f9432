"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[7576],{88412:function(t,e,a){var l=a(26263),o=a(36766),r=a(1001),i=(0,r.Z)(o.Z,l.s,l.x,!1,null,"5e7ef0ae",null);e["Z"]=i.exports},97576:function(t,e,a){a.r(e),a.d(e,{default:function(){return z}});var l=function(){var t=this,e=this,a=e.$createElement,l=e._self._c||a;return l("div",{staticClass:"fit"},[l("ta-border-layout",{attrs:{"layout-type":"fixTop"}},[l("div",{attrs:{slot:"header"},slot:"header"},[l("ta-title",{attrs:{title:"患者信息"}}),l("ta-form",{attrs:{autoFormCreate:function(e){t.baseInfoForm=e},col:e.col,layout:"horizontal","label-width":"150px",formLayout:!0}},[l("ta-form-item",{attrs:{fieldDecoratorId:"aac003"}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("患者姓名")]),l("span",[e._v(e._s(this.personInfo.aac003))])]),l("ta-form-item",{attrs:{fieldDecoratorId:"akc191"}},["0"===this.flag||"1"===this.flag||"11"===this.flag?l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("就诊号")]):l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v(e._s(this.akc191Title))]),l("span",[e._v(e._s(this.personInfo.akc191))])]),l("ta-form-item",{attrs:{fieldDecoratorId:"aac004"}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("性别")]),l("span",[e._v(e._s(e.CollectionLabel("SEX",this.personInfo.aac004)))])]),l("ta-form-item",{attrs:{fieldDecoratorId:"age"}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("年龄")]),l("span",[e._v(e._s(this.personInfo.age))])]),l("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:void 0!=this.personInfo.aae030,expression:"this.personInfo.aae030 != undefined"}],attrs:{fieldDecoratorId:"aae030"}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("入院时间")]),l("span",[e._v(e._s(this.personInfo.aae030))])]),l("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:void 0!=this.personInfo.aae031,expression:"this.personInfo.aae031 != undefined"}],attrs:{fieldDecoratorId:"aae031"}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("出院时间")]),l("span",[e._v(e._s(this.personInfo.aae031))])]),l("ta-form-item",{attrs:{fieldDecoratorId:"aae141"}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("医保类型")]),l("span",[e._v(e._s(e.CollectionLabel("AAE141",this.personInfo.aae141)))])]),l("ta-form-item",{attrs:{fieldDecoratorId:"aae140"}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("险种类型")]),l("span",[e._v(e._s(e.CollectionLabel("AAE140",this.personInfo.aae140)))])]),l("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:!["11","0","1"].includes(this.flag),expression:"!['11','0','1'].includes(this.flag)"}],attrs:{fieldDecoratorId:"aae140"}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("住院次数")]),l("span",[e._v(e._s(this.personInfo.akc200)+"次")])]),l("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:e.showEmrButton,expression:"showEmrButton"}]},[this.showEmrButton?l("a",{on:{click:e.openEmrPage}},[e._v("查看电子病例")]):e._e()]),l("ta-form-item",{attrs:{fieldDecoratorId:"aae386"}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("当前就诊科室")]),l("span",[e._v(e._s(this.personInfo.aae386))])]),l("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:void 0!=this.personInfo.bch,expression:"this.personInfo.bch != undefined"}],attrs:{fieldDecoratorId:"bch"}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("床位号")]),l("span",[e._v(e._s(this.personInfo.bch))])])],1)],1),l("div",{staticClass:"content-box"},[l("ta-title",{attrs:{title:"审核明细"}}),l("ta-tabs",{staticClass:"fit content-tabs",attrs:{type:"card",tabBarGutter:10},on:{change:e.fnTabChange},model:{value:e.activeKey,callback:function(t){e.activeKey=t},expression:"activeKey"}},["3"!==this.flag?l("ta-tab-pane",{key:"Kc23",staticClass:"tab-pane-box",attrs:{tab:"医嘱信息",forceRender:!1}},[l("ta-form",{attrs:{autoFormCreate:function(a){return t[e.activeKey+"Form"]=a},layout:"horizontal",formLayout:!0}},[l("ta-form-item",{staticStyle:{"margin-top":"10px"},attrs:{fieldDecoratorId:"aae036",span:6,wrapperCol:{span:16}}},[l("span",{attrs:{slot:"label"},slot:"label"},[e._v("医嘱开具时间")]),l("ta-range-picker",{attrs:{"allow-one":!0}},[l("ta-icon",{attrs:{slot:"suffixIcon",type:""},slot:"suffixIcon"})],1)],1),l("ta-form-item",{staticStyle:{"margin-top":"10px"},attrs:{fieldDecoratorId:"ake007",span:4,wrapperCol:{span:15}}},[l("span",{attrs:{slot:"label"},slot:"label"},[e._v("医嘱信息")]),l("ta-input",{attrs:{placeholder:"请输入医嘱名称或编码"}})],1),l("ta-form-item",{staticStyle:{"margin-top":"10px"},attrs:{span:4,"label-width":"90px"}},[l("ta-button",{staticStyle:{"margin-left":"30px"},attrs:{type:"primary",icon:"search"},on:{click:function(t){return e.fnTabChange("Kc23")}}},[e._v("查询")])],1)],1),l("div",{staticClass:"tab-table-box"},[l("ta-big-table",{key:(new Date).getTime()+"kc23",attrs:{size:"small",bordered:!0,columns:e.columns_kc23,scroll:{y:"100%"},border:"",height:"100%","auto-resize":"",resizable:"","highlight-hover-row":"","highlight-current-row":"","show-overflow":"",data:e.gridData}})],1)],1):e._e(),"0"!==this.flag&&"1"!==this.flag?l("ta-tab-pane",{key:"fyxx",staticClass:"tab-pane-box",attrs:{tab:"费用信息"}},[l("ta-form",{attrs:{autoFormCreate:function(a){return t[e.activeKey+"Form"]=a},layout:"horizontal",formLayout:!0}},[l("ta-form-item",{staticStyle:{"margin-top":"10px"},attrs:{fieldDecoratorId:"allDate",span:5,wrapperCol:{span:16}}},[l("span",{attrs:{slot:"label"},slot:"label"},[e._v("项目时间")]),l("ta-range-picker",{attrs:{"allow-one":!0}},[l("ta-icon",{attrs:{slot:"suffixIcon",type:""},slot:"suffixIcon"})],1)],1),l("ta-form-item",{staticStyle:{"margin-top":"10px"},attrs:{fieldDecoratorId:"projectInfo",span:4,wrapperCol:{span:15}}},[l("span",{attrs:{slot:"label"},slot:"label"},[e._v("三目信息")]),l("ta-input",{attrs:{placeholder:"请输入三目名称或编码"}})],1),l("ta-form-item",{staticStyle:{"margin-top":"10px"},attrs:{fieldDecoratorId:"inProjectInfo",span:4,wrapperCol:{span:15}}},[l("span",{attrs:{slot:"label"},slot:"label"},[e._v("院内项目")]),l("ta-input",{attrs:{placeholder:"请输入院内项目名称"}})],1),l("ta-form-item",{staticStyle:{"margin-top":"10px"},attrs:{span:2,"label-width":"80px"}},[l("span",{attrs:{slot:"label"},slot:"label"},[e._v("数量过滤")]),l("ta-switch",{attrs:{title:"开启后过滤数量为0的数据",checkedChildren:"开",unCheckedChildren:"关",defaultChecked:""},on:{change:e.onChangeSwitch}})],1),l("ta-form-item",{staticStyle:{"margin-top":"10px"},attrs:{span:4,"label-width":"90px"}},[l("span",{attrs:{slot:"label"},slot:"label"},[e._v("仅展示疑点")]),l("ta-switch",{attrs:{title:"开启后仅展示疑点数据",checkedChildren:"开",unCheckedChildren:"关"},on:{change:e.onChangeSwitch2},model:{value:e.onlyDoubts,callback:function(t){e.onlyDoubts=t},expression:"onlyDoubts"}}),l("ta-button",{staticStyle:{"margin-left":"30px"},attrs:{type:"primary",icon:"search"},on:{click:function(t){return e.fnTabChange("fyxx")}}},[e._v("查询")])],1)],1),l("div",{staticClass:"tab-table-box"},[l("ta-big-table",{ref:"infoTableRef",attrs:{"show-footer":"","footer-method":e.footerMethod,border:"",height:"100%","auto-resize":"",resizable:"","highlight-hover-row":"","highlight-current-row":"","show-overflow":"",columns:e.columns_fyxx,data:e.gridData,scroll:{y:"100%"}}})],1)],1):e._e(),l("ta-tab-pane",{key:"Kc21k1",staticClass:"tab-pane-box",attrs:{tab:"诊断信息",forceRender:!1}},[l("ta-form",{attrs:{autoFormCreate:function(a){return t[e.activeKey+"Form"]=a},layout:"horizontal",formLayout:!0}},[l("ta-form-item",{staticStyle:{"margin-top":"10px"},attrs:{fieldDecoratorId:"aka121",span:4,wrapperCol:{span:15}}},[l("span",{attrs:{slot:"label"},slot:"label"},[e._v("诊断信息")]),l("ta-input",{attrs:{placeholder:"请输入诊断名称或编码"}})],1),l("ta-form-item",{staticStyle:{"margin-top":"10px"},attrs:{span:4,"label-width":"90px"}},[l("ta-button",{staticStyle:{"margin-left":"30px"},attrs:{type:"primary",icon:"search"},on:{click:function(t){return e.fnTabChange("Kc21k1")}}},[e._v("查询")])],1)],1),l("div",{staticClass:"tab-table-box"},[l("ta-big-table",{key:(new Date).getTime()+"kc21k1",attrs:{size:"small",bordered:!0,columns:e.columns_kc21k1,scroll:{y:"100%"},border:"",height:"100%","auto-resize":"",resizable:"","highlight-hover-row":"","highlight-current-row":"","show-overflow":"",data:e.gridData}})],1)],1),e.Kc37flag?l("ta-tab-pane",{key:"Kc37",staticClass:"tab-pane-box",attrs:{tab:"检验指标",forceRender:!1}},[l("ta-form",{attrs:{autoFormCreate:function(a){return t[e.activeKey+"Form"]=a},layout:"horizontal",formLayout:!0}},[l("ta-form-item",{staticStyle:{"margin-top":"10px"},attrs:{fieldDecoratorId:"aae036",span:5,wrapperCol:{span:16}}},[l("span",{attrs:{slot:"label"},slot:"label"},[e._v("检验时间")]),l("ta-range-picker",{attrs:{"allow-one":!0}},[l("ta-icon",{attrs:{slot:"suffixIcon",type:""},slot:"suffixIcon"})],1)],1),l("ta-form-item",{staticStyle:{"margin-top":"10px"},attrs:{fieldDecoratorId:"dzg006",span:4,wrapperCol:{span:15}}},[l("span",{attrs:{slot:"label"},slot:"label"},[e._v("检验指标")]),l("ta-input",{attrs:{placeholder:"请输入检验指标名称或代码"}})],1),l("ta-form-item",{staticStyle:{"margin-top":"10px"},attrs:{span:4,"label-width":"90px"}},[l("ta-button",{staticStyle:{"margin-left":"30px"},attrs:{type:"primary",icon:"search"},on:{click:function(t){return e.fnTabChange("Kc37")}}},[e._v("查询")])],1)],1),l("div",{staticClass:"tab-table-box"},[l("ta-big-table",{key:(new Date).getTime()+"kc37",attrs:{size:"small",bordered:!0,columns:e.columns_kc37,scroll:{y:"100%"},border:"",height:"100%","auto-resize":"",resizable:"","highlight-hover-row":"","highlight-current-row":"","show-overflow":"",data:e.gridData}})],1)],1):e._e(),e.Kc34flag?l("ta-tab-pane",{key:"Kc34",staticClass:"tab-pane-box",attrs:{tab:"营养风险",forceRender:!1}},[l("div",{staticClass:"tableHeight"},[l("ta-big-table",{key:(new Date).getTime()+"kc34",attrs:{size:"small",bordered:!0,columns:e.columns_kc34,scroll:{y:"100%"},border:"",height:"100%","auto-resize":"",resizable:"","highlight-hover-row":"","highlight-current-row":"","show-overflow":"",data:e.gridData}})],1)]):e._e(),e.Kc39flag?l("ta-tab-pane",{key:"Kc39",staticClass:"tab-pane-box",attrs:{tab:"药敏记录",forceRender:!1}},[l("ta-form",{attrs:{autoFormCreate:function(a){return t[e.activeKey+"Form"]=a},layout:"horizontal",formLayout:!0}},[l("ta-form-item",{staticStyle:{"margin-top":"10px"},attrs:{fieldDecoratorId:"dzf004",span:4,wrapperCol:{span:15}}},[l("span",{attrs:{slot:"label"},slot:"label"},[e._v("检查信息")]),l("ta-input",{attrs:{placeholder:"请输入检查名称或编码"}})],1),l("ta-form-item",{staticStyle:{"margin-top":"10px"},attrs:{fieldDecoratorId:"dzg013",span:4,wrapperCol:{span:15}}},[l("span",{attrs:{slot:"label"},slot:"label"},[e._v("菌种信息")]),l("ta-input",{attrs:{placeholder:"请输入菌种名称或编码"}})],1),l("ta-form-item",{staticStyle:{"margin-top":"10px"},attrs:{fieldDecoratorId:"dzg016",span:5,wrapperCol:{span:15}}},[l("span",{attrs:{slot:"label"},slot:"label"},[e._v("抗生素信息")]),l("ta-input",{attrs:{placeholder:"请输入抗生素名称或编码"}})],1),l("ta-form-item",{staticStyle:{"margin-top":"10px"},attrs:{span:4,"label-width":"90px"}},[l("ta-button",{staticStyle:{"margin-left":"30px"},attrs:{type:"primary",icon:"search"},on:{click:function(t){return e.fnTabChange("Kc39")}}},[e._v("查询")])],1)],1),l("div",{staticClass:"tab-table-box"},[l("ta-big-table",{key:(new Date).getTime()+"kc39",attrs:{size:"small",bordered:!0,columns:e.columns_kc39,scroll:{y:"100%"},border:"",height:"100%","auto-resize":"",resizable:"","highlight-hover-row":"","highlight-current-row":"","show-overflow":"",data:e.gridData}})],1)],1):e._e(),e.Kc41flag?l("ta-tab-pane",{key:"Kc41",staticClass:"tab-pane-box",attrs:{tab:"手术记录",forceRender:!1}},[l("ta-form",{attrs:{autoFormCreate:function(a){return t[e.activeKey+"Form"]=a},layout:"horizontal",formLayout:!0}},[l("ta-form-item",{staticStyle:{"margin-top":"10px"},attrs:{fieldDecoratorId:"dzh001",span:5,wrapperCol:{span:16}}},[l("span",{attrs:{slot:"label"},slot:"label"},[e._v("手术日期")]),l("ta-range-picker",{attrs:{"allow-one":!0}},[l("ta-icon",{attrs:{slot:"suffixIcon",type:""},slot:"suffixIcon"})],1)],1),l("ta-form-item",{staticStyle:{"margin-top":"10px"},attrs:{fieldDecoratorId:"dzh005",span:4,wrapperCol:{span:15}}},[l("span",{attrs:{slot:"label"},slot:"label"},[e._v("手术类型")]),l("ta-input",{attrs:{placeholder:"请输入手术类型名称或编码"}})],1),l("ta-form-item",{staticStyle:{"margin-top":"10px"},attrs:{fieldDecoratorId:"dzh007",span:4,wrapperCol:{span:15}}},[l("span",{attrs:{slot:"label"},slot:"label"},[e._v("手术类别")]),l("ta-input",{attrs:{placeholder:"请输入手术类别名称或编码"}})],1),l("ta-form-item",{staticStyle:{"margin-top":"10px"},attrs:{fieldDecoratorId:"dzh012",span:5,wrapperCol:{span:15}}},[l("span",{attrs:{slot:"label"},slot:"label"},[e._v("手术名称")]),l("ta-input",{attrs:{placeholder:"请输入手术名称或编码"}})],1),l("ta-form-item",{staticStyle:{"margin-top":"10px"},attrs:{span:4,"label-width":"90px"}},[l("ta-button",{staticStyle:{"margin-left":"30px"},attrs:{type:"primary",icon:"search"},on:{click:function(t){return e.fnTabChange("Kc41")}}},[e._v("查询")])],1)],1),l("div",{staticClass:"tab-table-box"},[l("ta-big-table",{key:(new Date).getTime()+"kc41",attrs:{size:"small",bordered:!0,columns:e.columns_kc41,scroll:{y:"100%"},border:"",height:"100%","auto-resize":"",resizable:"","highlight-hover-row":"","highlight-current-row":"","show-overflow":"",data:e.gridData}})],1)],1):e._e()],1),l("div",{staticClass:"content-box-footer"},[l("ta-pagination",{ref:"infoPageRef",staticStyle:{position:"absolute",right:"6px",bottom:"6px"},attrs:{defaultPageSize:100,pageSizeOptions:["100","200","500","1000","1500"],dataSource:e.gridData,params:e.pageParam,url:e.url},on:{"update:dataSource":function(t){e.gridData=t},"update:data-source":function(t){e.gridData=t}}})],1)],1),l("emr-detail",{attrs:{visible:e.showEmrModal,config:e.emrConfig,patientInfo:e.personInfo},on:{handleClose:e.closeEmrPage}})],1)],1)},o=[],r=a(48534),i=(a(36133),a(88412)),n=(a(36797),function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("ta-modal",{attrs:{title:"病案查看",visible:t.visible,height:700,width:1400,bodyStyle:{padding:0},closable:"",footer:null},on:{cancel:t.handleClose}},[a("ta-border-layout",{attrs:{layout:{left:"300px"}}},[a("div",{attrs:{slot:"left"},slot:"left"},[t.config.showTypeSelectBtn?a("ta-select",{attrs:{defaultValue:t.typeDefault,"show-search":"",placeholder:"病例类型","collection-type":"EMR_TYPE"},on:{change:t.typeSelect}}):t._e(),a("div",[t._v("目录: "),a("ta-e-tree",{attrs:{data:t.treeData,"icon-open":"folder-open",props:t.props,"icon-close":"folder"},on:{"node-click":t.nodeClick}})],1)],1),a("div",{staticStyle:{display:"flex",height:"100%"}},[a("div",{staticStyle:{width:"100%","overflow-y":"scroll"}},["img"===t.currenType&&t.imageSrc?a("img",{staticStyle:{width:"100%","object-fit":"contain"},attrs:{src:t.imageSrc},on:{click:t.showImageByViewer}}):t._e(),"url"===t.currenType?a("iframe",{staticStyle:{width:"100%",height:"100%"},attrs:{id:t.iframe.id,src:t.iframe.currentURL}}):t._e()]),a("ta-image-viewer",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}],ref:"myViewer",attrs:{images:t.imagesList}})],1)])],1)}),s=[],c=a(95278),d="emr/",u={getBasePath:function(){return c.Z.basePath},getPageUrl:function(){return d+"queryListByPage"},queryEmrPageList:function(t,e){Base.submit(null,{url:d+"queryEmrPageList",data:t,autoQs:!1},{successCallback:function(t){return e(t)}})},queryEmrPageDetail:function(t,e){Base.submit(null,{url:d+"queryEmrPageDetail",data:t,autoQs:!1},{successCallback:function(t){return e(t)}})}},f=a(34116),m=(a(33240),a(55115));m.w3.use(f.Z);var h={name:"emrDetail",props:{visible:{type:Boolean},patientInfo:{type:Object},config:{type:Object},showSave:{type:Boolean}},computed:{typeDefault:function(){if("1"===this.patientInfo.aae500)return"45"}},data:function(){return{treeData:[],props:{label:"title",id:"id",children:"child"},currenType:"url",imagesList:[],imageSrc:null,base64Image:null,currentFileName:"",iframe:{id:"1",currentURL:""}}},watch:{visible:{handler:function(t){var e=this;t&&(this.base64Image=null,this.currentFileName="",this.imageSrc=null,this.config.showTypeSelectBtn&&!this.typeDefault||this.$nextTick((function(){u.queryEmrPageList({aae500:e.patientInfo.aae500,aaz217:e.patientInfo.aaz217Query},(function(t){e.treeData=t.data.data.emrPages}))})))}}},methods:{handleClose:function(){this.$emit("handleClose")},saveImgAsAttach:function(){this.$emit("saveImage",this.base64Image,this.currentFileName+".png")},showImageByViewer:function(){this.$refs.myViewer.view(0)},typeSelect:function(t){var e=this;this.$nextTick((function(){u.queryEmrPageList({aae500:e.patientInfo.aae500,aaz217:e.patientInfo.aaz217Query,emrType:t},(function(t){e.treeData=t.data.data.emrPages}))}))},nodeClick:function(t,e){var a=this;if(t.emrId){this.currenType=t.emrType;var l={aae500:this.patientInfo.aae500,aaz217:this.patientInfo.aaz217Query,emrId:t.emrId,extension:t.extension};u.queryEmrPageDetail(l,(function(l){"img"===t.emrType&&(a.base64Image=l.data.detailInfo,a.imageSrc="data:image/png;base64,"+a.base64Image,a.currentFileName=e.parent.data.text+"-"+t.text,a.imagesList.pop(),a.imagesList.push({url:a.imageSrc})),"url"===t.emrType&&(a.iframe.currentURL=u.getBasePath()+l.data.detailInfo,a.iframe.id=t.emrId)}))}}}},p=h,g=a(1001),b=(0,g.Z)(p,n,s,!1,null,"23848123",null),v=b.exports,C=a(83231),y={name:"expenseDetails",components:{EmrDetail:v,TaTitle:i.Z},data:function(){var t,e=[{title:"三目编码",field:"ake001",sortable:!0,overflowTooltip:!0,width:"16%",align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"三目名称",field:"ake002",sortable:!0,overflowTooltip:!0,width:"16%",align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"院内项目名称",field:"ake006",overflowTooltip:!0,width:"16%",align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"项目时间",field:"aae036",overflowTooltip:!0,sortable:!0,width:"16%",align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"使用数量",field:"akc226",overflowTooltip:!0,width:"8%",align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"开单科室",field:"aae386",overflowTooltip:!0,width:"14%",align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"开单医生",field:"aac003",overflowTooltip:!0,width:"14%",align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"执行科室",field:"sdksmc",overflowTooltip:!0,width:"14%",align:"center",customHeaderCell:this.fnCustomHeaderCell}];t="0"!=this.$route.query.flag&&"1"!=this.$route.query.flag;var a=[{title:"医嘱项目编码",dataIndex:"ake001",field:"ake001",overflowTooltip:!0,width:"auto",align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"医嘱项目名称",dataIndex:"ake002",field:"ake002",overflowTooltip:!0,align:"center",width:"auto",customHeaderCell:this.fnCustomHeaderCell},{title:"医嘱名称",dataIndex:"ake007",field:"ake007",overflowTooltip:!0,width:"auto",align:"center",visible:t,customHeaderCell:this.fnCustomHeaderCell},{title:"医嘱开具时间",dataIndex:"aae036",field:"aae036",overflowTooltip:!0,align:"center",width:"auto",customHeaderCell:this.fnCustomHeaderCell},{title:"开单科室编码",dataIndex:"aaz307",field:"aaz307",overflowTooltip:!0,align:"center",width:"auto",customHeaderCell:this.fnCustomHeaderCell},{title:"开单科室名称",dataIndex:"aae386",field:"aae386",overflowTooltip:!0,align:"center",width:"auto",customHeaderCell:this.fnCustomHeaderCell},{title:"医嘱开始时间",dataIndex:"aae310",field:"aae310",overflowTooltip:!0,align:"center",width:"auto",customHeaderCell:this.fnCustomHeaderCell},{title:"医嘱结束时间",dataIndex:"aae311",field:"aae311",overflowTooltip:!0,align:"center",width:"auto",customHeaderCell:this.fnCustomHeaderCell}],l=[{title:"诊断编码",dataIndex:"aka120",field:"aka120",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"诊断名称",dataIndex:"aka121",field:"aka121",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"诊断类型",dataIndex:"aka015",field:"aka015",overflowTooltip:!0,collectionType:"AKA015",align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"是否主诊断",dataIndex:"aka016",field:"aka016",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"诊断录入时间",dataIndex:"aae036",field:"aae036",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell}],o=[{title:"体重",dataIndex:"ape150",field:"ape150",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"身高",dataIndex:"ape159",field:"ape159",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"营养风险总评分",dataIndex:"dze003",field:"dze003",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"BMI值",dataIndex:"dze005",field:"dze005",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"数据来源",dataIndex:"dze006",field:"dze006",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell}],r=[{title:"检验指标代码",dataIndex:"dzg005",field:"dzg005",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"检验指标名称",dataIndex:"dzg006",field:"dzg006",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"检验时间",dataIndex:"aae036",field:"aae036",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"检验结果",dataIndex:"dzg007",field:"dzg007",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"单位",dataIndex:"dzg008",field:"dzg008",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"参考值",dataIndex:"dzg010",field:"dzg010",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell}],i=[{title:"检查编码",dataIndex:"dzf003",field:"dzf003",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"检查名称",dataIndex:"dzf004",field:"dzf004",overflowTooltip:!0,align:"left",customHeaderCell:this.fnCustomHeaderCell},{title:"菌种编码",dataIndex:"dzg012",field:"dzg012",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"菌种名称",dataIndex:"dzg013",field:"dzg013",overflowTooltip:!0,align:"left",customHeaderCell:this.fnCustomHeaderCell},{title:"抗生素编码",dataIndex:"dzg015",field:"dzg015",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"抗生素名称",dataIndex:"dzg016",field:"dzg016",overflowTooltip:!0,align:"left",customHeaderCell:this.fnCustomHeaderCell},{title:"定性结果",dataIndex:"dzg018",field:"dzg018",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"检验时间",dataIndex:"aae036",field:"aae036",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell}],n=[{title:"手术日期",dataIndex:"dzh001",field:"dzh001",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"手术类型编码",dataIndex:"dzh004",field:"dzh004",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"手术类型名称",dataIndex:"dzh005",field:"dzh005",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"手术类别编码",dataIndex:"dzh006",field:"dzh006",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"手术类别名称",dataIndex:"dzh007",field:"dzh007",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"手术名称编码",dataIndex:"dzh011",field:"dzh011",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"手术名称",dataIndex:"dzh012",field:"dzh012",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell},{title:"麻醉方式",dataIndex:"dzh013",field:"dzh013",overflowTooltip:!0,align:"center",customHeaderCell:this.fnCustomHeaderCell}];return{url:"",col:{xs:1,sm:2,md:2,lg:4,xl:4,xxl:4},activeKey:"fyxx",columns_fyxx:e,columns_kc23:a,columns_kc21k1:l,columns_kc34:o,columns_kc37:r,columns_kc39:i,columns_kc41:n,gridData:[],kc21k1Data:[],kc23Data:[],kc34Data:[],kc37Data:[],kc39Data:[],kc41Data:[],countShowFlag:!0,onlyDoubts:!1,Kc37flag:!0,Kc34flag:!0,Kc39flag:!0,Kc41flag:!0,rangeValue:[this.Base.getMoment((new Date).toISOString().slice(0,8)+"-01","YYYY-MM-DD"),this.Base.getMoment((new Date).toISOString().slice(0,10),"YYYY-MM-DD")],count:"",money:"",akc191Title:"住院号",visible:!1,fyRecord:{},doubtList:[],auditPathList:[],nodeDetail:{},colors:["red","green","blue"],aaz217:"",akb020:"",flag:"",showEmrButton:!1,emrConfig:{},showEmrModal:!1,personInfo:{}}},mounted:function(){var t=this;return(0,r.Z)(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.$route.query.aaz217&&(t.aaz217=t.$route.query.aaz217),t.$route.query.akb020&&(t.akb020=t.$route.query.akb020),t.$route.query.flag&&(t.flag=t.$route.query.flag),"0"!=t.$route.query.flag&&"1"!=t.$route.query.flag||(t.activeKey="Kc21k1"),t.fnQueryTableTitle(t.aaz217,t.flag);case 5:case"end":return e.stop()}}),e)})))()},methods:{fnQueryTableTitle:function(t,e){var a,l,o=this;try{var r,i,n;a=null===(r=top)||void 0===r||null===(i=r.indexTool)||void 0===i||null===(n=i.getActiveTabMenuId)||void 0===n?void 0:n.call(i),null==a&&(a="")}catch(f){a=""}try{var s,c,d,u=null===(s=top)||void 0===s||null===(c=s.indexTool)||void 0===c||null===(d=c.getUserInfo)||void 0===d?void 0:d.call(c);l=null===u||void 0===u?void 0:u.loginId,null==l&&(l="")}catch(f){l=""}this.Base.submit(null,{url:"miimCommonRead/selectTableColumShow",data:{resourceid:a,loginid:l},autoValid:!1},{successCallback:function(a){var l,r,i,n;(null===(l=a.data)||void 0===l||null===(r=l.list)||void 0===r?void 0:r.length)>0&&null!==a&&void 0!==a&&null!==(i=a.data)&&void 0!==i&&null!==(n=i.list[0])&&void 0!==n&&n.colum&&(o.onlyDoubts=JSON.parse(a.data.list[0].colum).onlyDoubts),o.fnTabChange(o.activeKey),o.getPersonInfo(t,e)},failCallback:function(t){o.$message.error("查询标志失败")}})},queryCountMoney:function(){var t=this;this.Base.submit(null,{url:"dischargeClinic/queryCountMoney",data:this.pageParam,autoValid:!1},{successCallback:function(e){e.data.list.length>0&&e.data.list[0]?t.count=e.data.list[0].count:t.count="0",t.fnTabChange(t.activeKey)},failCallback:function(e){t.$message.error("查询金额数量失败")}})},footerMethod:function(t){var e=this,a=t.columns;return[a.map((function(t,a){return["akc226"].includes(t.property)?"总计:  "+e.count:null}))]},onChangeSwitch:function(t){this.countShowFlag=t,this.fnTabChange("fyxx")},onChangeSwitch2:function(t){this.onlyDoubts=t,this.fnSaveOnlyDoubts(),this.fnTabChange("fyxx")},fnSaveOnlyDoubts:function(){var t=this,e={onlyDoubts:this.onlyDoubts},a=top.indexTool.getActiveTabMenuId(),l=top.indexTool.getUserInfo().loginId,o={colum:JSON.stringify(e),flag:"column",resourceid:a,loginid:l};C.Z.insertTableColumShow(o,(function(e){"success"===e?t.$message.success("保存成功"):t.$message.error("保存失败")}))},tagFlagByAaa005:function(t,e){return!!t.includes(e)},getPersonInfo:function(t,e){var a=this;t&&e&&this.Base.submit(null,{url:"miimCommonRead/queryPersonInfo",data:{aaz217:t,aae500:e},showPageLoading:!1,autoValid:!1},{successCallback:function(l){l.data.akc191Title.length>0?a.akc191Title=l.data.akc191Title[0].label:a.akc191Title="住院号",a.Kc39flag=a.tagFlagByAaa005(l.data.tagsAaa005,"kc39"),a.Kc34flag=a.tagFlagByAaa005(l.data.tagsAaa005,"kc34"),a.Kc37flag=a.tagFlagByAaa005(l.data.tagsAaa005,"kc37"),a.Kc41flag=a.tagFlagByAaa005(l.data.tagsAaa005,"kc41"),a.showEmrButton=l.data.emrConfig.emrLinkEnabled,a.emrConfig=l.data.emrConfig,a.personInfo=l.data.resultData[0],a.personInfo.aaz217Query=t,a.personInfo.aae500=e},failCallback:function(t){a.$message.error("查询个人信息失败")}})},openEmrPage:function(){if(this.emrConfig.isOutLink){var t={aae500:this.$route.query.flag,aaz217:this.aaz217};u.queryEmrPageDetail(t,(function(t){window.open(t.data.detailInfo,"newWindow","toolbar=no, menubar=no, location=no, status=no")}))}else this.showEmrModal=!0},closeEmrPage:function(){this.showEmrModal=!1},pageParam:function(){var t,e,a,l,o,r,i,n,s,c,d,u="Kc34"===this.activeKey?{}:this["".concat(this.activeKey,"Form")].getFieldsValue(),f={akb020:this.akb020,akc190:this.personInfo.akc190,aaz217:this.aaz217,countShowFlag:this.countShowFlag,onlyDoubts:this.onlyDoubts,orderFlag:"other",flag:this.flag};"fyxx"===this.activeKey&&(u.allDate&&(u.allDate[0]&&(f.startDate=u.allDate[0].format("YYYY-MM-DD")),u.allDate[1]&&(f.endDate=u.allDate[1].format("YYYY-MM-DD"))),f.projectInfo=(null===(t=u.projectInfo)||void 0===t?void 0:t.trim())||"",f.inProjectInfo=(null===(e=u.inProjectInfo)||void 0===e?void 0:e.trim())||"");"Kc23"===this.activeKey&&(u.aae036&&(u.aae036[0]&&(f.startDate=u.aae036[0].format("YYYY-MM-DD")),u.aae036[1]&&(f.endDate=u.aae036[1].format("YYYY-MM-DD"))),f.ake007=(null===(a=u.ake007)||void 0===a?void 0:a.trim())||"");"Kc21k1"===this.activeKey&&(f.aka121=(null===(l=u.aka121)||void 0===l?void 0:l.trim())||"");"Kc37"===this.activeKey&&(u.aae036&&(u.aae036[0]&&(f.startDate=u.aae036[0].format("YYYY-MM-DD")),u.aae036[1]&&(f.endDate=u.aae036[1].format("YYYY-MM-DD"))),f.dzg006=(null===(o=u.dzg006)||void 0===o?void 0:o.trim())||"");"Kc39"===this.activeKey&&(f.dzf004=(null===(r=u.dzf004)||void 0===r?void 0:r.trim())||"",f.dzg013=(null===(i=u.dzg013)||void 0===i?void 0:i.trim())||"",f.dzg016=(null===(n=u.dzg016)||void 0===n?void 0:n.trim())||"");"Kc41"===this.activeKey&&(u.dzh001&&(u.dzh001[0]&&(f.startDate=u.dzh001[0].format("YYYY-MM-DD")),u.dzh001[1]&&(f.endDate=u.dzh001[1].format("YYYY-MM-DD"))),f.dzh005=(null===(s=u.dzh005)||void 0===s?void 0:s.trim())||"",f.dzh007=(null===(c=u.dzh007)||void 0===c?void 0:c.trim())||"",f.dzh012=(null===(d=u.dzh012)||void 0===d?void 0:d.trim())||"");return f},fnTabChange:function(t){var e=this;this.gridData=[],this.url="dischargeClinic/get"+t,this.$nextTick((function(){e.$refs.infoPageRef.loadData((function(t){t.data.list&&t.data.list.length>0&&t.data.list[0]?(e.count=t.data.list[0].count+"",e.money="￥"+t.data.list[0].money.toFixed(2)):(e.count="0",e.money="￥0.00")}))}))},fnCustomRow:function(t,e){return{style:{fontFamily:"Microsoft YaHei, Hiragino Sans GB, Pingfang SC, Arial, Helvetica Neue, Helvetica",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px"}}},fnCustomHeaderCell:function(t){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"18px",fontWeight:"bold"}}}}},w=y,x=(0,g.Z)(w,l,o,!1,null,"bd5ab1ae",null),z=x.exports},36766:function(t,e,a){var l=a(66586);e["Z"]=l.Z},26263:function(t,e,a){a.d(e,{s:function(){return l},x:function(){return o}});var l=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},o=[]},66586:function(t,e){var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};e["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}},83231:function(t,e,a){var l=a(48534);a(36133);function o(t){return t?t.akb020&&t.aaz263?"personal":t.akb020&&t.aaz309&&t.aaz307?"group":t.akb020&&t.aaz307?"department":t.akb020?"hospital":"all":"all"}function r(t){return i.apply(this,arguments)}function i(){return i=(0,l.Z)(regeneratorRuntime.mark((function t(e){var a,l,r,i,n,s,c,d,u,f,m,h;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Base.submit(null,{url:"/miimCommonRead/queryDataPermission",showPageLoading:!1,data:{jobnumber:e},autoValid:!0});case 2:return a=t.sent,l=new Set,r=new Set,a.data.permission.forEach((function(t){var e=o(t);"hospital"===e&&l.add(t.akb020),"department"===e&&r.add(t.aaz307)})),i=a.data.permission.filter((function(t){return"department"===o(t)||!r.has(t.aaz307)})).filter((function(t){return"hospital"===o(t)||!l.has(t.akb020)})),n=new Set(i.map((function(t){return t.akb020})).filter((function(t){return null!=t}))),s=new Set(i.map((function(t){return t.aaz307})).filter((function(t){return null!=t}))),c=new Set(i.map((function(t){return t.aaz309})).filter((function(t){return null!=t}))),d=new Set(i.map((function(t){return t.aaz263})).filter((function(t){return null!=t}))),u=!1,f=!1,m=!1,h=!1,1===n.size&&(u=!0),1===s.size&&1===n.size&&(f=!0),1===s.size&&1===n.size&&1===c.size&&(m=!0),1===n.size&&0===s.size&&1===d.size&&(h=!0),t.abrupt("return",{akb020Set:n,aaz307Set:s,aaz263Set:d,aaz309Set:c,akb020Disable:u,aaz307Disable:f,aaz263Disable:h,aaz309Disable:m});case 20:case"end":return t.stop()}}),t)}))),i.apply(this,arguments)}function n(t,e){Base.submit(null,{url:"miimCommonRead/getAa01AAE500StartStop",data:t},{successCallback:function(t){return e(t)}})}function s(t,e){Base.submit(null,{url:"miimCommonRead/insertTableColumShow",data:t,autoValid:!1},{successCallback:function(t){return e("success")},failCallback:function(t){return e("fail")}})}var c={zbx01:{footer:function(){return"统筹金总额"}},zbx02:{footer:function(){return"基金支出总额"}},zbx03:{footer:function(){return"总费用"}},zbx04:{footer:function(){return"总住院日"}}};e["Z"]={permissionCheck:r,getAa01AAE500StartStop:n,insertTableColumShow:s,props:c,moneyNumFormat:function(t){var e=t.cellValue;return e||"—"}}}}]);
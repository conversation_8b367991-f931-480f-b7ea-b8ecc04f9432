"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[50],{88412:function(t,e,a){var o=a(26263),i=a(36766),l=a(1001),r=(0,l.Z)(i.Z,o.s,o.x,!1,null,"5e7ef0ae",null);e["Z"]=r.exports},40050:function(t,e,a){a.r(e),a.d(e,{default:function(){return h}});var o=function(){var t=this,e=this,a=e.$createElement,o=e._self._c||a;return o("div",{staticClass:"fit"},[o("ta-border-layout",{attrs:{layout:{footer:"55px"},showPadding:!0,"footer-cfg":{showBorder:!1}}},[o("div",{staticClass:"fit kpi-report-cntainer",staticStyle:{height:"100%"}},[o("div",{staticClass:"form-container"},[o("ta-form",{staticClass:"form-content-box",attrs:{autoFormCreate:function(e){t.baseInfoForm=e},layout:"horizontal",formLayout:!0}},[o("ta-form-item",{attrs:{fieldDecoratorId:"allDate",labelCol:{span:4},wrapperCol:{span:20},span:6,"init-value":e.rangeValue,fieldDecoratorOptions:{rules:[{required:!0,message:"必须选取时间"}]}}},[o("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("时间")]),o("ta-range-picker",{staticStyle:{width:"100%"},attrs:{"allow-one":!0,disabledDate:e.disabledDate},on:{change:e.selectTimeRange}},[o("ta-icon",{attrs:{slot:"suffixIcon",type:""},slot:"suffixIcon"})],1)],1),o("ta-form-item",{attrs:{fieldDecoratorId:"illness",label:"病组",span:6,labelCol:{span:6},wrapperCol:{span:16},initValue:e.illCode}},[o("ta-select",{attrs:{options:e.illList,placeholder:"请选择",allowClear:"",showSearch:!0,optionFilterProp:"children"},on:{change:e.selectIllness}})],1),o("ta-form-item",{attrs:{fieldDecoratorId:"aaz307",label:"科室",span:6,labelCol:{span:6},wrapperCol:{span:16}}},[o("ta-select",{attrs:{options:e.ksList,placeholder:"请选择",allowClear:"",showSearch:!0,optionFilterProp:"children"},on:{change:e.selectDepartments}})],1),o("ta-form-item",{attrs:{fieldDecoratorId:"aaz263",label:"医生",span:6,labelCol:{span:6},wrapperCol:{span:16}}},[o("ta-select",{attrs:{showSearch:!0,optionFilterProp:"children",placeholder:"请选择",options:e.doctorList,allowClear:""},on:{change:e.selectDoctor}})],1)],1),o("div",[o("ta-button",{on:{click:e.exportDataEvent}},[e._v("导出Excel")])],1)],1),o("ta-title",{attrs:{title:"病例列表"}}),o("div",{staticClass:"table-content"},[o("ta-big-table",{ref:"kpiReportDetailTable",attrs:{height:"auto",border:"",stripe:"",resizable:"","highlight-hover-row":"",data:e.infoTableData},scopedSlots:e._u([{key:"bottomBar",fn:function(){return[o("ta-pagination",{ref:"kpiReportDetailTablePage",staticStyle:{"text-align":"right","margin-top":"10px"},attrs:{params:e.pageParams,defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"],"data-source":e.infoTableData,url:"groupService/queryClinicalDeptInfoPage"},on:{"update:dataSource":function(t){e.infoTableData=t},"update:data-source":function(t){e.infoTableData=t}}})]},proxy:!0}])},[o("ta-big-table-column",{attrs:{type:"seq",title:"序号",width:"60"}}),o("ta-big-table-column",{attrs:{field:"medicalno",title:"病案号",width:"135","show-overflow":""}}),o("ta-big-table-column",{attrs:{field:"name",title:"姓名",width:"80","show-overflow":""}}),o("ta-big-table-column",{attrs:{field:"sex",title:"性别",width:"80","show-overflow":""}}),o("ta-big-table-column",{attrs:{field:"starttime",title:"入院时间",width:"135","show-overflow":""}}),o("ta-big-table-column",{attrs:{field:"endtime",title:"出院时间",width:"135","show-overflow":""}}),o("ta-big-table-column",{attrs:{field:"money",title:"住院费用",width:"135",align:"right","show-overflow":""}}),o("ta-big-table-column",{attrs:{field:"hosday",title:"住院天数",width:"80",align:"right","show-overflow":""}}),o("ta-big-table-column",{attrs:{field:"diagnosis",title:"主要诊断",width:"135","show-overflow":""}}),o("ta-big-table-column",{attrs:{field:"operation",title:"主要手术/操作",width:"135","show-overflow":""}}),o("ta-big-table-column",{attrs:{field:"diseasegroup",title:"病组名称",width:"auto","show-overflow":""}})],1)],1)],1)])],1)},i=[],l=a(66347),r=a(88412),s=a(36797),n=a.n(s),c={name:"kpiReportDetail",components:{TaTitle:r.Z},data:function(){return{rangeValue:[this.Base.getMoment(this.createDate().toISOString().slice(0,10),"YYYY-MM-DD"),this.Base.getMoment((new Date).toISOString().slice(0,10),"YYYY-MM-DD")],illList:[],hosList:[],ksList:[],doctorList:[],akb020:"",aaz307:"",infoTableData:[],illCode:""}},mounted:function(){this.getParams(),this.fnQueryHos()},methods:{getParams:function(){if(this.$route.query.startTime&&this.$route.query.endTime){var t=Number(this.$route.query.startTime),e=Number(this.$route.query.endTime);this.rangeValue=[this.Base.getMoment(n()(t).format("YYYY-MM-DD"),"YYYY-MM-DD"),this.Base.getMoment(n()(e).format("YYYY-MM-DD"),"YYYY-MM-DD")],this.illCode=this.$route.query.code}},createDate:function(){var t=new Date;return t.setMonth(t.getMonth()-2),t},fnQueryGroup:function(t){var e=this,a={};t&&(a.diseasegroup=t),this.Base.submit(null,{url:"groupService/queryGroupList",data:a,autoValid:!1},{successCallback:function(t){e.baseInfoForm.resetFields("illness"),e.illList=t.data.resultData,e.fnQuery()},failCallback:function(t){e.$message.error("病组加载失败")}})},fnQueryHos:function(){var t=this,e={url:"/miimCommonRead/queryHospRpcList",autoValid:!0},a={successCallback:function(e){t.hosList=e.data.resultData,t.baseInfoForm.setFieldsValue({medinsCode:t.hosList[0].value}),t.akb020=t.hosList[0].value,t.fnQueryDept(t.akb020)},failCallback:function(e){t.$message.error("医院数据加载失败")}};this.Base.submit(null,e,a)},fnQueryDept:function(t){var e=this;this.akb020=t||"",this.Base.submit(null,{url:"miimCommonRead/queryDepartRpcList",data:{akb020:this.akb020},autoValid:!1},{successCallback:function(a){e.baseInfoForm.resetFields("aaz307"),e.ksList=a.data.resultData,e.fnQueryDocter(t)},failCallback:function(t){e.$message.error("科室数据加载失败")}})},fnQueryDocter:function(t){var e=this;this.Base.submit(null,{url:"miimCommonRead/queryDoctorRpcList",autoValid:!1},{successCallback:function(t){e.baseInfoForm.resetFields("aaz263"),e.doctorList=t.data.resultData,e.fnQueryGroup(e.illCode)},failCallback:function(t){e.$message.error("医师数据加载失败")}})},disabledDate:function(t){return t=t.format("YYYYMMDD"),t&&t>n()().startOf("day").format("YYYYMMDD")},fnQuery:function(){var t=this,e=this.baseInfoForm.getFieldError("allDate");if(e)return!1;this.$nextTick((function(){t.$refs.kpiReportDetailTablePage.loadData()}))},exportExcel:function(){var t=this.baseInfoForm.getFieldsValue();if(void 0===t.allDate||t.allDate.length<1)this.$message.error("请选择时间范围！");else{var e,a=t.allDate[0].format("YYYY-MM-DD"),o=t.allDate[1].format("YYYY-MM-DD"),i=[],r=this.infoTableData,s=(0,l.Z)(curColumns);try{for(s.s();!(e=s.n()).done;){var n=e.value;i.push({header:n.title,key:n.dataIndex,width:20})}}catch(u){s.e(u)}finally{s.f()}var c={fileName:"住院开单提醒查询结果表("+a+"-"+o+")",sheets:[{name:"worksheet1",column:{complex:!1,columns:i},rows:r,codeList:[{codeType:"APE893",columnKey:"ape893"},{codeType:"RESAULT",columnKey:"yd"}]}]};this.Base.generateExcel(c)}},exportDataEvent:function(){this.$refs.kpiReportDetailTable.exportData({type:"csv",name:"csv"})},selectTimeRange:function(){this.fnQuery()},selectIllness:function(){this.fnQuery()},selectDepartments:function(){this.fnQuery()},selectDoctor:function(){this.fnQuery()},pageParams:function(){var t={},e=this.baseInfoForm.getFieldError("allDate");if(e)return{};var a=this.baseInfoForm.getFieldsValue();return t.starttime=a.allDate[0].format("YYYY-MM-DD"),t.endtime=a.allDate[1].format("YYYY-MM-DD"),t.diseasegroup=a.illness,t.deptcode=a.aaz307,t.doccode=a.aaz263,t}}},u=c,f=a(1001),d=(0,f.Z)(u,o,i,!1,null,"2347f2d7",null),h=d.exports},36766:function(t,e,a){var o=a(66586);e["Z"]=o.Z},26263:function(t,e,a){a.d(e,{s:function(){return o},x:function(){return i}});var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},i=[]},66586:function(t,e){var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};e["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}}}]);
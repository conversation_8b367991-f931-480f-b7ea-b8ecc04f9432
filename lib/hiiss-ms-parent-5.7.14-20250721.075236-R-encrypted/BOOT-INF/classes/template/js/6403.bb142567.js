"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[6403],{44056:function(t,e,a){a.d(e,{Z:function(){return c}});var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"blue_title"},[a("div",{staticClass:"blue_bar"}),a("div",{staticClass:"title_font"},[t._v(t._s(t.title))])])},s=[],n={name:"label",data:function(){return{}},props:{title:{type:String,default:"",required:!0}}},r=n,o=a(1001),l=(0,o.Z)(r,i,s,!1,null,"2841b261",null),c=l.exports},88412:function(t,e,a){var i=a(26263),s=a(36766),n=a(1001),r=(0,n.Z)(s.Z,i.s,i.x,!1,null,"5e7ef0ae",null);e["Z"]=r.exports},6403:function(t,e,a){a.r(e),a.d(e,{default:function(){return d}});var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"fit statisticsAllocation-container"},[a("ta-border-layout",{attrs:{layout:{header:"75px"}}},[a("div",{staticClass:"statisticsAllocation_header",attrs:{slot:"header"},slot:"header"},[a("ta-input-search",{staticStyle:{width:"350px"},attrs:{placeholder:"根据资源名称搜索","allow-clear":"",enterButton:""},on:{search:t.onSearch}})],1),a("div",{staticClass:"fit"},[a("ta-border-layout",{attrs:{centerCfg:{showBar:!0,toolBarStyle:{height:"50px",lineHeight:"50px"}},showBorder:!1}},[a("div",{attrs:{slot:"centerExtraContent"},slot:"centerExtraContent"},[a("div",{staticStyle:{float:"left"}},[a("ta-title",{attrs:{title:"功能清单"}})],1),a("div",{staticStyle:{float:"right"}},[a("ta-button",{attrs:{type:"primary"},on:{click:t.updateConfig}},[t._v(" 更新指标系统数据 ")]),a("ta-button",{attrs:{type:"primary"},on:{click:t.goToConfig}},[t._v(" 功能资源配置 ")]),a("ta-button",{attrs:{type:"primary"},on:{click:t.addStatistics}},[t._v(" 新增 ")])],1)]),a("div",{staticClass:"fit"},[a("ta-big-table",{attrs:{data:t.tableData,height:"auto",border:"",align:"center"}},[a("ta-big-table-column",{attrs:{field:"resourceName",label:"功能名称"}}),a("ta-big-table-column",{attrs:{field:"resourceId",label:"资源ID"}}),a("ta-big-table-column",{attrs:{field:"direction",label:"排列方式","collection-type":"ARRANGEMENT"}}),a("ta-big-table-column",{attrs:{field:"updateDate",label:"修改时间"}}),a("ta-big-table-column",{attrs:{fixed:"right",width:"100",field:"operate",title:"开启图表"},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.row;return[0==i.userChartCount?a("ta-switch",{attrs:{checkedChildren:"开",unCheckedChildren:"关"},on:{change:function(e){return t.switchChange(e,i)}},model:{value:i.switchState,callback:function(e){t.$set(i,"switchState",e)},expression:"row.switchState"}}):a("ta-tooltip",{attrs:{placement:"top"}},[a("template",{slot:"title"},[a("span",[t._v("已有用户添加自定义图表，不可禁用。")])]),a("ta-switch",{attrs:{checkedChildren:"开",unCheckedChildren:"关",disabled:""},model:{value:i.switchState,callback:function(e){t.$set(i,"switchState",e)},expression:"row.switchState"}})],2)]}}])}),a("ta-big-table-column",{attrs:{width:"300",fixed:"right",field:"operate",title:"操作"},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.row;return[a("ta-button",{staticStyle:{border:"none"},attrs:{size:"small",type:"link"},on:{click:function(e){return t.goToPage(i)}}},[t._v("页面跳转")]),a("ta-button",{staticStyle:{border:"none"},attrs:{size:"small",type:"link"},on:{click:function(e){return t.editTarget(i)}}},[t._v("编辑指标")]),a("ta-button",{staticStyle:{border:"none"},attrs:{size:"small",type:"link",disabled:!i.switchState},on:{click:function(e){return t.editChart(i)}}},[t._v("编辑图表")]),a("ta-popconfirm",{attrs:{title:"确定要删除吗?",okText:"确定",cancelText:"取消"},on:{confirm:function(e){return t.deleteTableData(i)}}},[a("ta-button",{staticStyle:{border:"none"},attrs:{size:"small",type:"link"}},[t._v("删除")])],1)]}}])}),a("template",{slot:"bottomBar"},[a("ta-pagination",{ref:"gridPager",staticStyle:{"text-align":"right"},attrs:{defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"],"data-source":t.tableData,params:t.userPageParams,url:"statisticsConfig/queryStatisticsConfigByPage"},on:{"update:dataSource":function(e){t.tableData=e},"update:data-source":function(e){t.tableData=e}}})],1)],2)],1)])],1)])],1)},s=[],n=a(44056),r=a(88412),o={components:{taLabel:n.Z,TaTitle:r.Z},data:function(){return{tableData:[],seachInfo:""}},mounted:function(){this.getTableDataInfo()},methods:{updateConfig:function(){var t=this;this.Base.submit(null,{url:"statisticsConfig/refreshIndexInfoCache",data:{}}).then((function(e){t.$message.success(e.data.result)}))},goToConfig:function(){this.Base.openTabMenu({id:"ec56a0a43b09429482632cb61f7c6908",name:"功能资源管理",url:"sysmg.html#/resourceManagement"})},getTableDataInfo:function(){var t=this;this.$refs.gridPager.loadData((function(e){e.data.pageBean.list.forEach((function(e){1==e.enableChart?t.$set(e,"switchState",!0):t.$set(e,"switchState",!1)}))}))},userPageParams:function(){return{condition:this.seachInfo}},onSearch:function(t){this.seachInfo=t,this.getTableDataInfo()},addStatistics:function(){this.$router.push({path:"/addStatisticsAllocation",query:{type:"新增"}})},switchChange:function(t,e){var a=this,i=0;i=t?0:1,this.Base.submit(null,{url:"statisticsConfig/updateEnableChart",data:{statisticsConfigId:e.statisticsConfigId,enableChart:i,resourceId:e.resourceId}}).then((function(e){t?a.$message.success("开启成功！"):a.$message.success("关闭成功！")}))},goToPage:function(t){this.Base.openTabMenu({id:t.resourceId,name:t.resourceName,url:t.resourceUrl})},editTarget:function(t){var e=this;this.Base.submit(null,{url:"statisticsConfig/queryStatisticsIndexDetail",data:{statisticsConfigId:t.statisticsConfigId}}).then((function(a){a.data.result.statisticsConfigId=t.statisticsConfigId,e.$router.push({path:"/addStatisticsAllocation",query:{type:"修改",pageData:a.data.result}})}))},editChart:function(t){t.switchState&&this.$router.push({path:"/addStatisticsEchats",query:{type:"编辑图表",resourceId:t.resourceId}})},deleteTableData:function(t){var e=this;this.Base.submit(null,{url:"statisticsConfig/delIndexStatisticsConfig",data:{statisticsConfigId:t.statisticsConfigId}}).then((function(t){e.getTableDataInfo()}))}}},l=o,c=a(1001),u=(0,c.Z)(l,i,s,!1,null,"0a76a2ae",null),d=u.exports},36766:function(t,e,a){var i=a(66586);e["Z"]=i.Z},26263:function(t,e,a){a.d(e,{s:function(){return i},x:function(){return s}});var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},s=[]},66586:function(t,e){var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};e["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}}}]);
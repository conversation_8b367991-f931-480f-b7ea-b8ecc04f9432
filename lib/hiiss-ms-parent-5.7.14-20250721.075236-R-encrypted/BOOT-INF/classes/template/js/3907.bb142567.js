(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[3907,362,9624],{88412:function(t,e,a){"use strict";var i=a(26263),l=a(36766),o=a(1001),n=(0,o.Z)(l.Z,i.s,i.x,!1,null,"5e7ef0ae",null);e["Z"]=n.exports},362:function(t,e,a){"use strict";a.r(e),a.d(e,{default:function(){return f}});var i=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"fit"},[i("div",[i("span",{staticStyle:{"font-weight":"bold","font-size":"14px",color:"#333333","margin-left":"10px"}},[i("ta-badge",{attrs:{color:t.badgeColor,text:"项目："}}),t._v(" "+t._s(t.fyRecord.ake002)+" "),i("span",{staticStyle:{"margin-right":"80px"}}),i("ta-badge",{attrs:{color:t.badgeColor,text:"明细总数量："}}),t._v(" "+t._s(t.fyRecord.akc226)+" "),(1==t.fyRecord.ykz020&&t.fyRecord.aae500&&t.fyRecord.aae500,i("span",[i("span",{staticStyle:{"margin-right":"80px"}}),i("ta-badge",{attrs:{color:t.badgeColor,text:"建议扣除数量："}}),t._v(" "+t._s(t.fyRecord.ape805)+" ")],1))],1),i("div",{staticStyle:{"margin-top":"0px","border-top":"0px solid #cccccc","padding-top":"5px"}},[i("ta-tabs",{attrs:{defaultActiveKey:"1"}},t._l(t.doubtList,(function(e,l){return i("ta-tab-pane",{key:l+1},[i("span",{attrs:{slot:"tab"},on:{click:function(a){return t.cardChange(e.id)}},slot:"tab"},[i("img",{attrs:{src:"1"===e.ykz020?a(60037):a(92206)}}),i("span",{staticClass:"tab-title",on:{click:function(a){return t.cardChange(e.id)}}},[t._v(t._s(e.aaa167))])]),i("p",{staticClass:"tab-content",on:{click:function(a){return t.cardChange(e.id)}}},[i("span",{staticClass:"tab-text-label"},[t._v("规则来源：")]),i("span",{staticClass:"tab-text"},[t._v(t._s(e.source))]),i("br"),i("span",{staticClass:"tab-text-label"},[t._v("限制条件：")]),i("span",{staticClass:"tab-text"},[t._v(t._s(e.ykz018))]),i("a",{directives:[{name:"show",rawName:"v-show",value:t.isRuleQuery,expression:"isRuleQuery"}],staticStyle:{"margin-left":"10px"},on:{click:function(a){return t.ruleDetails(e)}}},[t._v(">>规则详情")])])])})),1)],1)]),i("div",{staticClass:"audit-container"},[t._m(0),i("div",{staticClass:"audit-content",attrs:{id:"wai"}},[i("div",{staticClass:"divleft"},[i("div",{staticClass:"header"},[t._v("审核路径")]),i("table",{staticClass:"audit-table"},[t._m(1),i("tbody",t._l(t.auditPathList,(function(e,a){return i("tr",{key:a,staticClass:"audit-row"},[i("td",{staticClass:"audit-index"},[t._v(t._s(a+1))]),i("td",{staticClass:"audit-detail"},t._l(e.nodeInfoList,(function(l,o){return i("span",{key:o,staticClass:"audit-node-container"},[o>0&&o<e.nodeInfoList.length?i("span",[t._v("——")]):t._e(),i("span",{staticClass:"audit-node",style:{color:"3"===l.ykz020?t.colors[0]:t.colors[l.ykz020]},attrs:{tabindex:a+1},on:{click:function(e){return t.nodeChange(l)}}},[t._v(" "+t._s(l.ykz010)+" "),i("span",[t._v("("+t._s(l.ykz002)+")")])])])})),0)])})),0)])]),i("div",{staticClass:"divright"},[i("div",{staticClass:"header"},[t._v("节点详情")]),i("div",{staticClass:"right-content"},[i("div",{staticClass:"right-item"},[t._m(2),i("div",{staticClass:"right-item-content",domProps:{innerHTML:t._s(t.nodeDetail.ykz006)}})]),i("div",{staticClass:"right-item"},[t._m(3),i("div",{staticClass:"right-item-content",domProps:{innerHTML:t._s(t.nodeDetail.ykz008)}})]),i("div",{staticClass:"right-item"},[t._m(4),i("div",{staticClass:"right-item-content",domProps:{innerHTML:t._s(t.nodeDetail.ykz021)}})]),i("div",{staticClass:"right-item"},[t._m(5),i("div",{staticClass:"right-item-content",domProps:{innerHTML:t._s(t.nodeDetail.ykz022)}})])])])])])])},l=[function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"audit-info"},[i("img",{staticClass:"info-icon",attrs:{src:a(67488)}}),i("span",[t._v("审核结果信息")]),i("span",{staticClass:"status-red"},[t._v("红色")]),t._v("节点表示不通过， "),i("span",{staticClass:"status-green"},[t._v("绿色")]),t._v("节点表示通过 ")])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("thead",[a("tr",[a("th",{staticClass:"audit-index"},[t._v("序号")]),a("th",{staticClass:"audit-details"},[t._v("路径审核详情")])])])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"right-item-title"},[t._v("机审"),a("br"),t._v("记录")])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"right-item-title"},[t._v("引导"),a("br"),t._v("信息")])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"right-item-title"},[t._v("人工"),a("br"),t._v("操作")])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"right-item-title"},[t._v("人工"),a("br"),t._v("审核"),a("br"),t._v("理由")])}],o=a(95082),n=a(66353),r=["id"],s={name:"atientDetails",props:{fyRecord:Object},data:function(){return{visible:!1,doubtList:[],auditPathList:[],nodeDetail:{},isRuleQuery:!1,colors:["#D75457","#46C26D","#3693F3"],aaz217:"",akb020:"",flag:"",badgeColor:"#3693F3"}},mounted:function(){this.handleShowInfo(this.fyRecord)},methods:{ruleDetails:function(t){var e,a,i=this;(null===(e=t.nodeDetailVoList)||void 0===e?void 0:e.length)>0?this.Base.submit(null,{url:"mttRuleFind/queryRuleid",data:{ykz108:t.nodeDetailVoList[0].ykz108}},{successCallback:function(e){a=e.data.data[0].ruleid,i.Base.openTabMenu({id:t.ykz018,name:"【"+t.ykz018+"】规则查询",url:"mttRuleConfig.html#/ruleFind?ykz010=".concat(t.ykz018,"&ruleid=").concat(a),refresh:!1})},failCallback:function(t){i.$message.error("规则ID失败")}}):this.$message.error("审核详情缺少节点信息！请检查审核详情！")},handleShowInfo:function(t){var e=this;this.fyRecord=t,this.visible=!0,this.Base.submit(null,{url:"miimCommonRead/queryDoubtList",data:{aaz217:t.aaz217,aaz213:t.aaz213,aae500:t.aae500,type:t.type},autoValid:!0,autoQs:!1},{successCallback:function(t){t.data.list.length>0&&("monitor"===t.data.flag?t.data.list.forEach((function(t,e){for(var a in t.auditPathList){var i=t.auditPathList[a].nodeInfoList.every((function(t){return"1"===t.ykz020}));i&&(t.ykz020="1")}})):t.data.list.forEach((function(t,e){for(var a in t.auditPathList){t.auditPathList[a].nodeInfoList.forEach((function(t,e){t.ykz020=t.ykz173,t.ykz021?t.ykz021="1"===t.ykz021?"是":"否":t.ykz021="无"}));var i=t.auditPathList[a].nodeInfoList.every((function(t){return"1"===t.ykz173}));i&&(t.ykz020="1")}})),t.data.list.sort((function(t,e){var a=parseInt(t.ykz020,10),i=parseInt(e.ykz020,10);return 0===a||3===a?-1:0===i||3===i?1:a-i}))),e.doubtList=t.data.list.map((function(t,e){t.id;var a=(0,n.Z)(t,r);return(0,o.Z)({id:e+1},a)})),e.auditPathList=[],e.nodeDetail={},e.doubtList.length>0&&(e.auditPathList=e.doubtList[0].auditPathList),t.data.ruleQuery&&(e.isRuleQuery="y"==t.data.ruleQuery.toLowerCase())},failCallback:function(t){}})},cardChange:function(t){var e=t-1;this.auditPathList=this.doubtList[e].auditPathList},nodeChange:function(t){this.nodeDetail=t}}},u=s,c=a(1001),d=(0,c.Z)(u,i,l,!1,null,"e9de457e",null),f=d.exports},2598:function(t,e,a){"use strict";a.r(e),a.d(e,{default:function(){return v}});var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("ta-border-layout",{attrs:{layout:{left:"25%"},"show-border":!1},scopedSlots:t._u([{key:"header",fn:function(){return[a("ta-title",{attrs:{title:"查询条件"}}),a("ta-form",{attrs:{autoFormCreate:t.autoFormCreate,layout:"horizontal",formLayout:!0}},[a("ta-form-item",{attrs:{span:5,fieldDecoratorId:"allDate",fieldDecoratorOptions:{rules:[{required:!0,message:"必须选取日期"}]},required:!0,"init-value":t.rangeValue,label:"审核时间"}},[a("ta-range-picker",{attrs:{"allow-one":!0}})],1),a("ta-form-item",{attrs:{span:5,fieldDecoratorId:"flag",label:"审核场景",required:!0}},[a("ta-select",{attrs:{showSearch:"",placeholder:"请选择审核场景","collection-type":"AAE500","collection-filter":t.filterList,reverseFilter:!0}})],1),a("ta-form-item",{attrs:{span:5,"field-decorator-id":"medinsCode",label:"院区标识"}},[a("ta-select",{attrs:{showSearch:"",placeholder:"院区标识筛选",allowClear:"",options:t.hosList}})],1),a("ta-form-item",{attrs:{span:5,fieldDecoratorId:"patientInfo",label:"患者信息"}},[a("ta-input",{attrs:{placeholder:"请输入住院号、就诊号或姓名"}})],1),a("ta-button",{staticStyle:{"margin-right":"10px",float:"right"},attrs:{type:"default",icon:"redo"},on:{click:t.fnReset}},[t._v("重置")]),a("ta-button",{staticStyle:{"margin-right":"20px",float:"right"},attrs:{type:"primary",icon:"search"},on:{click:t.fnQuery}},[t._v("查询 ")])],1)]},proxy:!0},{key:"left",fn:function(){return[a("ta-title",{attrs:{title:"患者列表"}}),a("ta-big-table",{ref:"infoTableRef",attrs:{height:"95%",data:t.dataSource_left,"export-config":{},"import-config":{},border:"","empty-text":"-","highlight-hover-row":"","highlight-current-row":"","auto-resize":"","show-overflow":"",resizable:"",size:"large"},on:{"cell-click":t.cellClickEvent}},[a("ta-big-table-column",{attrs:{title:"序号",width:"50",type:"seq"}}),a("ta-big-table-column",{attrs:{field:"aac003",width:"70",title:"患者"}}),a("ta-big-table-column",{attrs:{field:"akc191","min-width":"100",sortable:"",title:"就诊号"}}),a("ta-big-table-column",{attrs:{field:"wgcs",width:"120",sortable:"",title:"违规项目数"}}),a("ta-big-table-column",{attrs:{field:"operate",width:"80",title:"患者详情",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.row;return[a("div",{staticClass:"opareteItem",on:{click:function(e){return t.patientdetails(i)}}},[t._v(" 查看 ")])]}}])})],1)]},proxy:!0}])},[a("ta-title",{attrs:{title:"审核结果"}}),a("ta-big-table",{ref:"infoTableRef",attrs:{height:"95%",columns:t.infoColumns,data:t.dataSource_right,"export-config":{},"import-config":{},border:"","empty-text":"-","highlight-hover-row":"","highlight-current-row":"","auto-resize":"","show-overflow":"",resizable:"",size:"large"},scopedSlots:t._u([{key:"ape800",fn:function(e){var i=e.row;return[i.ape800&&"0"!==i.ruleState?"0"==i.ape800?a("span",{staticStyle:{color:"#0f990f"}},[a("ta-icon",{attrs:{type:"check-circle",theme:"twoTone",twoToneColor:"#52c41a"}}),t._v("审核通过")],1):"1"==i.ape800?a("span",{staticStyle:{color:"#F59A23"}},[a("ta-icon",{attrs:{type:"question-circle",theme:"twoTone",twoToneColor:"#F59A23"}}),t._v("可疑提醒")],1):"2"==i.ape800?a("span",{staticStyle:{color:"#FF0000"}},[a("ta-icon",{attrs:{type:"exclamation-circle",theme:"twoTone",twoToneColor:"#FF0000"}}),t._v("违规提醒")],1):"3"==i.ape800?a("span",{staticStyle:{color:"#ffc49a"}},[a("ta-icon",{attrs:{type:"bell",theme:"twoTone",twoToneColor:"#e1ba91"}}),t._v("仅提醒")],1):t._e():a("span",[t._v("——")])]}},{key:"ake002",fn:function(e){var i=e.row;return["2"==i.ape800?a("span",{staticStyle:{color:"#FF0000"}},[t._v(t._s(i.ake002))]):a("span",[t._v(t._s(i.ake002))])]}},{key:"akb065",fn:function(e){var i=e.row;return["2"==i.ape800?a("span",{staticStyle:{color:"#FF0000"}},[t._v(t._s(i.akb065))]):a("span",[t._v(t._s(i.akb065))])]}},{key:"operate",fn:function(e){var i=e.row;return["0"===i.ruleState?a("div",[t._v(" — ")]):a("div",{staticClass:"opareteItem",on:{click:function(e){return t.openAuditDetails(i)}}},[t._v(" 审核详情 ")])]}},{key:"operate2",fn:function(e){var i=e.row;return[i.ape893real&&"0"!==i.ruleState?i.ape893real.includes("4")?a("span",{staticStyle:{color:"#E6A23C"}},[t._v(t._s(i.aaz560?i.aaz560:i.ape893))]):i.ape893real.includes("1")?a("span",{staticStyle:{color:"#5DAF34"},domProps:{innerHTML:t._s(i.ape893)}}):i.ape893real.includes("2")?a("span",{staticStyle:{color:"#CF9236"},domProps:{innerHTML:t._s(i.ape893)}}):i.ape893real.includes("0")?a("span",{staticStyle:{color:"#909399"},domProps:{innerHTML:t._s(i.ape893)}}):i.ape893real.includes("3")?a("span",{staticStyle:{color:"#67C23A"},domProps:{innerHTML:t._s(i.ape893)}}):a("span",{domProps:{innerHTML:t._s(i.ape893)}}):a("span",[t._v("--")])]}},{key:"ape896",fn:function(e){var i=e.row;return[null==i.ape896||""==i.ape896?a("span",[t._v("—")]):a("span",{domProps:{innerHTML:t._s(t.CollectionLabel("APE896",i.ape896))}})]}},{key:"ykz126",fn:function(e){var i=e.row;return["5"==i.ykz126?a("span",[t._v("中心端")]):a("span",[t._v("医院端")])]}},{key:"ape804",fn:function(e){var i=e.row;return[null==i.akb065?a("span",[t._v("—")]):a("span",[t._v(" "+t._s(i.akb065)+" ")])]}},{key:"bottomBar",fn:function(){return[a("ta-pagination",{ref:"infoPageRef",staticStyle:{"margin-right":"100px","text-align":"right"},attrs:{dataSource:t.dataSource_right,params:t.infoPageParams,defaultPageSize:100,pageSizeOptions:["100","200","500","1000","1500"],url:"patientAuditResultClinic/queryAuditResults"},on:{"update:dataSource":function(e){t.dataSource_right=e},"update:data-source":function(e){t.dataSource_right=e}}}),a("ta-button-group",{staticStyle:{position:"absolute",right:"6px",bottom:"10px"}},[a("ta-button",{attrs:{type:"primary",icon:"download"},on:{click:t.exportExcel}},[t._v("导出")])],1)]},proxy:!0}])}),a("div",{attrs:{id:"info"}},[a("ta-modal",{staticStyle:{top:"10px"},attrs:{width:"95%",bodyStyle:{paddingBottom:"1px"},"destroy-on-close":!0,footer:null,getContainer:t.getModalContainer,wrapClassName:"out-modal-wrap"},on:{cancel:t.handleCancel},model:{value:t.visible,callback:function(e){t.visible=e},expression:"visible"}},[a("ta-icon",{staticStyle:{color:"black"},attrs:{slot:"closeIcon",type:"close"},slot:"closeIcon"}),a("span",{staticStyle:{"font-weight":"normal","font-size":"14px","text-align":"center",height:"0px",width:"100%"},attrs:{slot:"title"},slot:"title"},[t._v(" 审核详情")]),a("atient-details",{attrs:{fyRecord:t.bfRecord}})],1)],1)],1)},l=[],o=a(66347),n=a(48534),r=a(95082),s=(a(36133),a(88412)),u=a(22722),c=a(83231),d=a(55115),f=a(362);d.w3.prototype.Base=Object.assign(d.w3.prototype.Base,(0,r.Z)({},u.Z));var A={name:"patientAuditResultClinic",components:{atientDetails:f["default"],TaTitle:s.Z},data:function(){var t=[{type:"seq",title:"序号",width:"60",align:"center"},{title:"医院编码",field:"akb020",align:"left",width:80,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"医院名称",field:"akb021",width:80,align:"left",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"开单科室",field:"aae386",align:"left",width:100,sortable:!0,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"开单医师",field:"aac003",align:"left",width:80,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"院内项目名称",field:"ake006",align:"left",minWidth:150,sortable:!0,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"医保项目名称",field:"ake002",sortable:!0,align:"left",width:130,overflowTooltip:!0,customRender:{default:"ake002"},customHeaderCell:this.fnCustomHeaderCell},{title:"预审结果",field:"ape800",align:"center",width:110,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell,scopedSlots:{customRender:"ape800"},customRender:{default:"ape800"}},{title:"金额",field:"akb065",align:"right",width:80,overflowTooltip:!0,customRender:{default:"akb065"},customHeaderCell:this.fnCustomHeaderCell},{title:"项目引导信息",field:"ydxx",align:"left",width:300,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"知识元",field:"ykz018",align:"left",width:300,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"医护操作",field:"ape893",align:"left",width:120,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell,customRender:{default:"operate2"}},{title:"操作人",field:"ykz041",align:"left",width:90,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"登记号",field:"akc190",align:"left",width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"就诊号",field:"akc191",align:"left",sortable:!0,width:100,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"患者姓名",field:"name",align:"left",width:100,sortable:!0,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"性別",field:"aac004",align:"left",width:80,sortable:!0,collectionType:"AAC004",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"年龄",field:"age",align:"left",width:80,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"主诊断",field:"diag_main",align:"left",visible:!1},{title:"其他诊断",field:"diag_others",align:"left",visible:!1},{title:"险种类型",field:"aae140",align:"left",width:100,sortable:!0,collectionType:"AAE140",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"医保编码",field:"ake001",align:"left",width:90,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"院内编码",field:"akc515",align:"left",width:90,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"规则大类",field:"aaa167",sortable:!0,align:"left",width:130},{title:"规则分类",field:"ruleType",align:"center",visible:!1,width:130},{title:"违规来源",field:"ykz126",align:"left",width:100,visible:!1,overflowTooltip:!0,customRender:{default:"ykz126"},customHeaderCell:this.fnCustomHeaderCell},{title:"项目发生时间",field:"aae036",align:"center",width:130,sorter:function(t,e){return t.aae036>e.aae036},sortable:!0,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"院内审批标志",field:"ape896",align:"center",width:140,customRender:{default:"ape896"},filterMethod:this.filterApeMethod,filters:[],overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"单价",field:"akc225",align:"right",width:80,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"规格",field:"aka074",align:"left",width:80,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"数量",field:"akc226",align:"right",width:80,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"数量单位",field:"ake130",align:"left",width:80,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"违规数量",field:"ape805",width:80,align:"left",formatter:c.Z.moneyNumFormat},{title:"违规金额",field:"ape804",width:80,align:"left",customRender:{default:"ape804"}},{title:"医疗费用总额",field:"akc264",align:"left",visible:!1},{title:"操作",field:"operate",align:"center",width:150,fixed:"right",overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell,scopedSlots:{customRender:"operate"},customRender:{default:"operate"}}];return{rangeValue:[this.Base.getMoment((new Date).toISOString().slice(0,8)+"-01","YYYY-MM-DD"),this.Base.getMoment((new Date).toISOString().slice(0,10),"YYYY-MM-DD")],filterList:"",aaz217:"",visible:!1,bfRecord:{},hosList:[],dataSource_left:[],infoColumns:t,dataSource_right:[],akb020:"",permissions:null,paramsDisable:{akb020:!1,aaz307:!1,aaz263:!1}}},mounted:function(){var t=this;return(0,n.Z)(regeneratorRuntime.mark((function e(){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,c.Z.permissionCheck();case 2:t.permissions=e.sent,a=["16","2","17","18","3","4","7","5","8","6"],c.Z.getAa01AAE500StartStop({aaz499s:JSON.stringify(a)},(function(e){var a=e.data.aae500List;a.includes(12)&&a.push(13),t.filterList=a.join(",");var i=a[0].toString();t.baseInfoForm.setFieldsValue({flag:i})})),t.fnQueryHos(),t.fnQueryAPE896();case 7:case"end":return e.stop()}}),e)})))()},methods:{fnQuery:function(){var t=this.infoPageParams();t&&(this.fnQueryTableLeft(),this.$refs.infoTableRef.loadData([]),this.$refs.infoPageRef.reset())},fnReset:function(){this.baseInfoForm.resetFields(["patientInfo","medinsCode"]),this.fnQuery()},filterApeMethod:function(t){var e=t.value,a=(t.option,t.row);t.column;return a.ape896==e},fnQueryAPE896:function(){var t=this;this.Base.submit(null,{url:"miimCommonRead/queryDict",data:{type:"APE896"},autoValid:!1},{successCallback:function(e){t.codeArray=e.data.codeList.map((function(t){return{label:t.label,value:t.value}})),t.$refs.infoTableRef.setFilter(t.$refs.infoTableRef.getColumnByField("ape896"),t.codeArray),t.$refs.infoTableRef.updateData()},failCallback:function(e){t.$message.error("码表数据加载失败")}})},cellClickEvent:function(t){var e=this,a=t.row;this.aaz217=a.akc190,this.$nextTick((function(){e.$refs.infoPageRef.loadData((function(t){}))}))},patientdetails:function(t){var e=this.baseInfoForm.getFieldValue("flag");this.Base.openTabMenu({id:t.akb020+t.aaz217,name:"【"+t.aac003+"】患者详情",url:"detailsQuery.html#/expenseDetailsForOther?akb020=".concat(t.akb020,"&akc190=").concat(t.akc190,"&aaz217=").concat(t.aaz217,"&flag=").concat(t.aae500?t.aae500:e),refresh:!1})},openAuditDetails:function(t){t.aae500=this.baseInfoForm.getFieldValue("flag"),this.bfRecord=t,this.visible=!0},fnQueryTableLeft:function(){var t=this;this.Base.submit(null,{url:"patientAuditResultClinic/queryPatientList",data:this.infoPageParams(),autoValid:!1},{successCallback:function(e){t.dataSource_left=e.data.data},failCallback:function(e){t.$message.error("患者列表数据加载失败")}})},fnQueryHos:function(){var t=this,e={url:"/miimCommonRead/queryHospRpcList",autoValid:!0},a={successCallback:function(e){t.hosList=e.data.resultData,t.baseInfoForm.setFieldsValue({medinsCode:t.$route.query.akb020?t.$route.query.akb020:t.hosList[0].value}),t.permissions&&t.permissions.akb020Set.size>0&&(t.hosList=t.hosList.filter((function(e){return t.permissions.akb020Set.has(e.value)})),t.baseInfoForm.setFieldsValue({medinsCode:t.$route.query.akb020?t.$route.query.akb020:t.hosList[0].value})),t.akb020=t.$route.query.akb020?t.$route.query.akb020:t.hosList[0].value},failCallback:function(e){t.$message.error("医院数据加载失败")}};this.Base.submit(null,e,a)},infoPageParams:function(){var t=this.baseInfoForm.getFieldsValue(),e=t.allDate;if(!e||e[0]&&e[1])return t.startDate=t.allDate[0].format("YYYY-MM-DD"),t.endDate=t.allDate[1].format("YYYY-MM-DD"),t.akb020=t.medinsCode,t.aaz217=this.aaz217,t;this.$message.error("请选择时间范围！")},autoFormCreate:function(t){this.baseInfoForm=t},exportExcel:function(){var t=this,e=this.infoPageParams();if(e){var a,i=e.startDate,l=e.endDate,n=[],r=this.$refs.infoTableRef.getColumns(),s=(0,o.Z)(r);try{for(s.s();!(a=s.n()).done;){var u=a.value;"seq"!==u.type&&"operate"!==u.property&&!1!==u.visible&&(n.push({header:u.title,key:u.property,width:20}),"ape893"===u.property&&n.push({header:"操作内容",key:"aaz560_1",width:20}))}}catch(A){s.e(A)}finally{s.f()}var c=r.map((function(t){return t.property})),d=[{codeType:"AAC004",columnKey:"aac004"},{codeType:"AAE140",columnKey:"aae140"},{columnKey:"ape896",customCollection:function(t,e){"0"==t.value||"1"==t.value?t.value="—":"2"==t.value?t.value="自费":"3"==t.value?t.value="报销":""!=t.value&&null!=t.value||(t.value="—")}},{columnKey:"ape800",customCollection:function(t,e){t.value?"0"==t.value?t.value="审核通过":"1"==t.value?t.value="可疑提醒":"2"==t.value?t.value="违规提醒":"3"==t.value&&(t.value="仅提醒"):t.value="——"}},{columnKey:"ape893",customCollection:function(t,e){var a=document.createElement("div");a.innerHTML=t.value;var i=a.innerText||a.textContent;a=null,t.value=i}},{columnKey:"ykz126",customCollection:function(t,e){"5"==t.value?t.value="中心端":t.value="医院端"}}],f=d.filter((function(t){return c.includes(t.columnKey)}));this.Base.submit(null,{url:"patientAuditResultClinic/exportExcel",data:e,autoValid:!1},{successCallback:function(e){var a=e.data.data;a.forEach((function(t){t.diagnoses&&Object.assign(t,t.diagnoses),t.akb065&&(t.ape804=t.akb065),t.ape893real.indexOf("1")>-1&&(t.aaz560_1=t.ape893.split(/[:：]/)[1],t.ape893=t.ape893.split(/[:：]/)[0]),t.ape893real.indexOf("4")>-1&&(t.aaz560_1=t.ape893.split(/[:：]/)[1],t.ape893=t.ape893.split(/[:：]/)[0])}));var o={fileName:"患者审核结果查询结果表("+i+"-"+l+")",sheets:[{name:"worksheet1",column:{complex:!1,columns:n},rows:a,codeList:f}]};t.Base.generateExcel(o)},failCallback:function(e){t.$message.error("医师数据加载失败")}})}},fnCustomHeaderCell:function(t){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px",fontWeight:"bold"}}},filterOption:function(t,e){return e.componentOptions.children[0].text.indexOf(t)>=0},getModalContainer:function(){return document.getElementById("info")},handleCancel:function(){this.visible=!1,this.showAll=!1}}},p=A,m=a(1001),h=(0,m.Z)(p,i,l,!1,null,"42eb1da8",null),v=h.exports},36766:function(t,e,a){"use strict";var i=a(66586);e["Z"]=i.Z},26263:function(t,e,a){"use strict";a.d(e,{s:function(){return i},x:function(){return l}});var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},l=[]},66586:function(t,e){"use strict";var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};e["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}},83231:function(t,e,a){"use strict";var i=a(48534);a(36133);function l(t){return t?t.akb020&&t.aaz263?"personal":t.akb020&&t.aaz309&&t.aaz307?"group":t.akb020&&t.aaz307?"department":t.akb020?"hospital":"all":"all"}function o(t){return n.apply(this,arguments)}function n(){return n=(0,i.Z)(regeneratorRuntime.mark((function t(e){var a,i,o,n,r,s,u,c,d,f,A,p;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Base.submit(null,{url:"/miimCommonRead/queryDataPermission",showPageLoading:!1,data:{jobnumber:e},autoValid:!0});case 2:return a=t.sent,i=new Set,o=new Set,a.data.permission.forEach((function(t){var e=l(t);"hospital"===e&&i.add(t.akb020),"department"===e&&o.add(t.aaz307)})),n=a.data.permission.filter((function(t){return"department"===l(t)||!o.has(t.aaz307)})).filter((function(t){return"hospital"===l(t)||!i.has(t.akb020)})),r=new Set(n.map((function(t){return t.akb020})).filter((function(t){return null!=t}))),s=new Set(n.map((function(t){return t.aaz307})).filter((function(t){return null!=t}))),u=new Set(n.map((function(t){return t.aaz309})).filter((function(t){return null!=t}))),c=new Set(n.map((function(t){return t.aaz263})).filter((function(t){return null!=t}))),d=!1,f=!1,A=!1,p=!1,1===r.size&&(d=!0),1===s.size&&1===r.size&&(f=!0),1===s.size&&1===r.size&&1===u.size&&(A=!0),1===r.size&&0===s.size&&1===c.size&&(p=!0),t.abrupt("return",{akb020Set:r,aaz307Set:s,aaz263Set:c,aaz309Set:u,akb020Disable:d,aaz307Disable:f,aaz263Disable:p,aaz309Disable:A});case 20:case"end":return t.stop()}}),t)}))),n.apply(this,arguments)}function r(t,e){Base.submit(null,{url:"miimCommonRead/getAa01AAE500StartStop",data:t},{successCallback:function(t){return e(t)}})}function s(t,e){Base.submit(null,{url:"miimCommonRead/insertTableColumShow",data:t,autoValid:!1},{successCallback:function(t){return e("success")},failCallback:function(t){return e("fail")}})}var u={zbx01:{footer:function(){return"统筹金总额"}},zbx02:{footer:function(){return"基金支出总额"}},zbx03:{footer:function(){return"总费用"}},zbx04:{footer:function(){return"总住院日"}}};e["Z"]={permissionCheck:o,getAa01AAE500StartStop:r,insertTableColumShow:s,props:u,moneyNumFormat:function(t){var e=t.cellValue;return e||"—"}}},92206:function(t){"use strict";t.exports="data:image/png;base64,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"},60037:function(t){"use strict";t.exports="data:image/png;base64,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"},67488:function(t){"use strict";t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAEKADAAQAAAABAAAAEAAAAAA0VXHyAAAByklEQVQ4EZVSOywEYRCe+fc8CvHIrbg7RK7xjAQVodBRaCQqhYLCm4hEIQoRWonGEYVOiUIhSgoaQYHQkHgccYsIcW7djvmPvfzn7iRmk51/v/m++eexCEmsYpJSjTvDhwB1/gW9PAkNHIkCntEX/dEfWONYAyUiKFhcAle3UW69fmwwx6vwIkd3z8MhAB5rAGPXC/qNBIVKcvcZzYC0y1icOMJDmOeKWsJAB/n9L8USQyJC78hzVigY7rAIZgGIL4i1u8VcHsW35Q8aBeGQtQ2ImO5ylglPb2An+P75ZBHNJRLbQullC8LEbA0crUBQFLo3ugQR1Kukv84EeGCCNXOzmHOECFusbY+ZwV9iGRMCl7jK2h/ePvuSfyUAi+8lfhT7VwICq5tntxfRE9VwC+eCAf5h0FSSJj2yoIqXNl4w8FjJ82gSCCvR9bh7AxNkwRT3GMXsTOoaCwcePOYn7CCgluZylkZb8Pv0adSgjUVvtvC39/QYnaaJJ0iQiUhNl5MYjCaQZP+8vqppjgZu6+q3WH7zDIb4vSlSRfWtL/dMYnHlStA7/Jr3/hFcB/pemdqCjKsWU4EduJjLuM9IcTZy/mWu5tTGE/kv0KybhGEzosQAAAAASUVORK5CYII="},55382:function(){},61219:function(){}}]);
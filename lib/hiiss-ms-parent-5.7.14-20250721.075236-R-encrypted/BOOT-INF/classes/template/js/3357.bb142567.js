(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[3357],{88412:function(t,a,e){"use strict";var i=e(26263),n=e(36766),r=e(1001),o=(0,r.Z)(n.Z,i.s,i.x,!1,null,"5e7ef0ae",null);a["Z"]=o.exports},84004:function(t,a,e){"use strict";e.d(a,{Z:function(){return v}});var i=function(){var t=this,a=this,e=a.$createElement,i=a._self._c||e;return i("ta-form",{attrs:{layout:"horizontal",formLayout:!0,"auto-form-create":function(a){return t.conditionForm=a}}},[i("ta-form-item",{attrs:{span:4,labelCol:{span:0},wrapperCol:{span:24}}},[i("ta-button",{attrs:{size:a.screenWidth>1595?"default":"small"},on:{click:function(t){return a.trunTime("1")}}},[a._v("近三月")]),i("ta-button",{staticStyle:{margin:"0 10px"},attrs:{size:a.screenWidth>1595?"default":"small"},on:{click:function(t){return a.trunTime("2")}}},[a._v("近六月")]),i("ta-button",{attrs:{size:"small",size:a.screenWidth>1595?"default":"small"},on:{click:function(t){return a.trunTime("3")}}},[a._v("近一年")])],1),a.showRange?i("ta-form-item",{attrs:{label:"时间范围",fieldDecoratorId:"aa",span:5,fieldDecoratorOptions:{rules:[{required:!0,message:"请选择时间范围!"}]},"init-value":a.rangeValue}},[i("ta-range-picker",{attrs:{type:"month","allow-one":!0,disabledDate:a.disabledMonth}})],1):a._e(),i("ta-form-item",{attrs:{fieldDecoratorId:"aae500",label:"审核场景",span:4,fieldDecoratorOptions:{initialValue:"1",rules:[{message:"必须选取审核场景"}]}}},[i("ta-select",{attrs:{placeholder:"请选择审核场景","collection-type":"AAE500","collection-filter":a.filterList,reverseFilter:!0},on:{change:a.fnQueryRuleList}})],1),i("ta-form-item",{attrs:{span:4,"label-col":{span:8},"wrapper-col":{span:16},"field-decorator-id":"akb020",label:"院区标识"}},[i("ta-select",{attrs:{showSearch:"",placeholder:"院区标识筛选",allowClear:"",options:a.hosList},on:{change:a.fnQueryDept}})],1),a.searchCondition&&(a.searchCondition.keshi||a.searchCondition.yisheng)?i("ta-form-item",{attrs:{label:"科室名称",span:6,fieldDecoratorId:"keshi",initValue:a.defaultDepartmentData,labelCol:{span:5},wrapperCol:{span:18}}},[i("ta-select",{attrs:{showSearch:"","show-search":!0,allowClear:"",placeholder:"开单科室筛选",options:a.ksList},on:{change:a.fnQueryGroup}})],1):a._e(),a.searchCondition&&a.searchCondition.yisheng?i("ta-form-item",{attrs:{label:"医生姓名",span:4,fieldDecoratorId:"yisheng","label-width":"70px",fieldDecoratorOptions:{initialValue:this.defaultDoctorData}}},[i("ta-select",{attrs:{showSearch:"",optionLabelProp:"children",mode:"combobox",placeholder:"医生名称筛选",options:a.doctorList,filterOption:a.filterOption,allowClear:""}})],1):a._e(),a.searchCondition&&a.searchCondition.guize?i("ta-form-item",{attrs:{label:"规则名称",span:6,fieldDecoratorId:"guize",fieldDecoratorOptions:{initialValue:this.defaultRuleData},"label-col":{span:5},"wrapper-col":{span:18}}},[i("ta-select",{attrs:{showSearch:"",placeholder:"规则大类",allowClear:"",options:a.ruleList}})],1):a._e(),a.searchCondition&&a.searchCondition.xiangmu?i("ta-form-item",{attrs:{fieldDecoratorId:"projectInfo",span:6,label:"项目编码","label-col":{span:5},"wrapper-col":{span:18}}},[i("ta-input",{attrs:{placeholder:"请输入医保项目名称或编码"}})],1):a._e(),a.searchCondition&&a.searchCondition.xiangmu?i("ta-form-item",{attrs:{fieldDecoratorId:"ake002",span:6,label:"项目名称","label-col":{span:5},"wrapper-col":{span:18}}},[i("ta-input",{attrs:{placeholder:"请输入医保名称"}})],1):a._e(),i("ta-form-item",{attrs:{span:4,"label-col":{span:8},"wrapper-col":{span:16},fieldDecoratorId:"result",label:"预审结果","init-value":a.resultInit}},[i("ta-select",{attrs:{showSearch:"",mode:"multiple","collection-type":"APE800","collection-filter":"0",maxTagCount:1,placeholder:"请选择",allowClear:""}})],1),i("ta-button",{staticStyle:{float:"right","margin-bottom":"10px"},attrs:{icon:"download"},on:{click:a.exportData}},[a._v("导出 ")]),i("ta-button",{staticStyle:{"margin-right":"10px",float:"right","margin-bottom":"10px"},attrs:{type:"primary",icon:"search"},on:{click:a.searchData}},[a._v("查询 ")])],1)},n=[],r=e(48534),o=(e(36133),e(36797)),s=e.n(o);function l(t){return t?t.akb020&&t.aaz263?"personal":t.akb020&&t.aaz309&&t.aaz307?"group":t.akb020&&t.aaz307?"department":t.akb020?"hospital":"all":"all"}function c(t){return u.apply(this,arguments)}function u(){return u=(0,r.Z)(regeneratorRuntime.mark((function t(a){var e,i,n,r,o,s,c,u,d,m,h,f;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Base.submit(null,{url:"/miimCommonRead/queryDataPermission",showPageLoading:!1,data:{jobnumber:a},autoValid:!0});case 2:return e=t.sent,i=new Set,n=new Set,e.data.permission.forEach((function(t){var a=l(t);"hospital"===a&&i.add(t.akb020),"department"===a&&n.add(t.aaz307)})),r=e.data.permission.filter((function(t){return"department"===l(t)||!n.has(t.aaz307)})).filter((function(t){return"hospital"===l(t)||!i.has(t.akb020)})),o=new Set(r.map((function(t){return t.akb020})).filter((function(t){return null!=t}))),s=new Set(r.map((function(t){return t.aaz307})).filter((function(t){return null!=t}))),c=new Set(r.map((function(t){return t.aaz309})).filter((function(t){return null!=t}))),u=new Set(r.map((function(t){return t.aaz263})).filter((function(t){return null!=t}))),d=!1,m=!1,h=!1,f=!1,1===o.size&&(d=!0),1===s.size&&1===o.size&&(m=!0),1===s.size&&1===o.size&&1===c.size&&(h=!0),1===o.size&&0===s.size&&1===u.size&&(f=!0),t.abrupt("return",{akb020Set:o,aaz307Set:s,aaz263Set:u,aaz309Set:c,akb020Disable:d,aaz307Disable:m,aaz263Disable:f,aaz309Disable:h});case 20:case"end":return t.stop()}}),t)}))),u.apply(this,arguments)}function d(t,a){Base.submit(null,{url:"miimCommonRead/getAa01AAE500StartStop",data:t},{successCallback:function(t){return a(t)}})}function m(t,a){Base.submit(null,{url:"miimCommonRead/insertTableColumShow",data:t,autoValid:!1},{successCallback:function(t){return a("success")},failCallback:function(t){return a("fail")}})}var h={zbx01:{footer:function(){return"统筹金总额"}},zbx02:{footer:function(){return"基金支出总额"}},zbx03:{footer:function(){return"总费用"}},zbx04:{footer:function(){return"总住院日"}}},f={permissionCheck:c,getAa01AAE500StartStop:d,insertTableColumShow:m,props:h,moneyNumFormat:function(t){var a=t.cellValue;return a||"—"}},p={props:{searchType:{type:String,default:""},searchCondition:{type:Object},overCondition:{type:Boolean,default:!0}},data:function(){return{rangeType:"1",showRange:!0,defaultDepartmentData:[],defaultDoctorData:[],defaultRuleData:"",defaultDiagnosisData:[],rangeTime:{},screenWidth:document.body.clientWidth,akb020:"",ksList:[],resultInit:[],doctorList:[],ruleList:[],rangeValue:[s()().startOf("year"),this.Base.getMoment((new Date).toISOString().slice(0,10),"YYYY-MM")],filterList:"",hosList:[],permissions:null}},created:function(){},mounted:function(){var t=this;return(0,r.Z)(regeneratorRuntime.mark((function a(){return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return t.fnQueryAa01(),a.next=3,f.permissionCheck();case 3:t.permissions=a.sent,t.initDefault(),t.queryOptons(),t.fnQueryDept(),t.fnQueryRuleList(t.searchCondition&&t.searchCondition.aae500?t.searchCondition.aae500:""),t.initTime(),t.fnQueryHos(),t.screenWidth=document.body.clientWidth,window.onresize=function(){return function(){t.screenWidth=document.body.clientWidth}()};case 12:case"end":return a.stop()}}),a)})))()},methods:{moment:s(),fnQueryAa01:function(){var t=this;this.Base.submit(null,{url:"miimCommonRead/getAa01Aaa005ByAaz499",data:{aaz499:206}},{successCallback:function(a){var e=a.data;t.searchCondition&&t.searchCondition.result?t.resultInit=t.searchCondition.result:t.resultInit="n"===e.aaa005.toLowerCase()?["1","2"]:[]},failCallback:function(a){t.$message.error("查询aa01表数据失败")}})},initDefault:function(){"{}"===JSON.stringify(this.searchCondition)?this.rangeType="1":void 0!==this.searchCondition&&this.conditionForm.setFieldsValue({aae500:this.searchCondition.aae500,akb020:this.searchCondition.akb020,keshi:this.searchCondition.keshi,yisheng:this.searchCondition.yisheng,guize:this.searchCondition.guize,projectInfo:this.searchCondition.xiangmu,ake002:this.searchCondition.ake002})},fnQueryDept:function(t){var a=this;this.akb020=t||"",this.Base.submit(null,{url:"miimCommonRead/queryDepartRpcList",data:{akb020:this.akb020,hospDeptType:"I"},autoValid:!1},{successCallback:function(t){a.ksList=t.data.resultData,a.fnQueryGroup(null)},failCallback:function(t){a.$message.error("科室数据加载失败")}})},fnQueryGroup:function(t){var a=this,e={akb020:this.akb020};t&&(e.aaz307=t),this.permissions.aaz307Disable&&(e.aaz307=this.permissions.aaz307Set.values().next().value),e.aaz307?this.Base.submit(null,{url:"miimCommonRead/queryGroupDic",data:e,autoValid:!1},{successCallback:function(t){a.baseInfoForm.resetFields("aaz309"),a.groupList=t.data.resultData,a.fnQueryDocter()},failCallback:function(t){a.$message.error("医师数据加载失败")}}):(this.groupList=[],this.fnQueryDocter())},fnQueryDocter:function(t){var a=this,e={akb020:this.akb020};this.permissions.aaz307Disable&&(e.departCode=this.permissions.aaz307Set.values().next().value),this.Base.submit(null,{url:"miimCommonRead/queryDoctorRpcList",data:e,autoValid:!1},{successCallback:function(t){a.doctorList=t.data.resultData},failCallback:function(t){a.$message.error("医师数据加载失败")}})},queryOptons:function(){var t=this,a=["2","3","4","5","6","7","17","18"];this.Base.submit(null,{url:"miimCommonRead/getAa01AAE500StartStop",data:{aaz499s:JSON.stringify(a)}},{successCallback:function(a){var e=a.data.aae500List;e.includes(12)&&e.push(13),t.filterList=e.join(",")},failCallback:function(a){t.$message.error("查询场景开关失败")}})},fnQueryHos:function(){var t=this,a={url:"/miimCommonRead/queryHospRpcList",autoValid:!0},e={successCallback:function(a){t.hosList=a.data.resultData,t.permissions&&t.permissions.akb020Set.size>0&&(t.hosList=t.hosList.filter((function(a){return t.permissions.akb020Set.has(a.value)}))),1===t.hosList.length&&t.conditionForm.setFieldsValue({akb020:t.hosList[0].value}),t.$nextTick((function(){t.searchData()}))},failCallback:function(a){t.$message.error("医院数据加载失败")}};this.Base.submit(null,a,e)},fnQueryRuleList:function(t){var a=this,e={url:"/miimCommonRead/queryRuleTypeDicByAae500",autoValid:!0,data:{aae500:t}},i={successCallback:function(t){a.ruleList=t.data.ruleList},failCallback:function(t){a.$message.error("医院数据加载失败")}};this.Base.submit(null,e,i)},getRangeTime:function(){var t=this;this.Base.submit(null,{url:"statisticsAnalysis/common/queryTimeRangeOption"},{successCallback:function(a){t.rangeTime=a.data.result}})},initTime:function(){"{}"===JSON.stringify(this.searchCondition)||void 0!==this.searchCondition&&(this.conditionForm.setFieldsValue({method:this.searchCondition.method}),this.conditionForm.setFieldsValue({aa:[this.searchCondition.startDate,this.searchCondition.endDate]}))},disabledMonth:function(t){return t>s()(this.rangeTime.endTime,"YYYYMM")||t<s()(this.rangeTime.startTime,"YYYYMM")},disabledYear:function(t){return t.year()>Number(this.rangeTime.endTime)||t.year()<Number(this.rangeTime.startTime)},searchData:function(){var t=this.conditionForm.getFieldsValue(),a={};if(2===t.aa.length&&null!=t.aa[0]&&null!=t.aa[1]){t.startDate=s()(t.aa[0]).format("YYYYMM"),t.endDate=s()(t.aa[1]).format("YYYYMM"),a.startDate=t.startDate,a.endDate=t.endDate,a.allDate=[s()(t.aa[0]).startOf("months"),s()(t.aa[1]).endOf("months").subtract("1","day")],a.aae500=t.aae500,a.akb020=t.akb020,void 0!==this.searchCondition&&(this.searchCondition.keshi&&(a.aaz307=this.searchCondition.keshi,a.keshi=this.searchCondition.keshi),this.searchCondition.yisheng&&(a.aaz263=this.searchCondition.yisheng,a.yisheng=this.searchCondition.yisheng),this.searchCondition.guize&&(a.aaz319=this.searchCondition.guize,a.guize=this.searchCondition.guize),this.searchCondition.xiangmu&&(a.projectInfo=this.searchCondition.xiangmu,a.ake002=this.searchCondition.ake002,a.xiangmu=this.searchCondition.xiangmu)),a.aaz307=t.keshi,a.aaz263=t.yisheng,a.aaz319=t.guize,a.projectInfo=t.projectInfo,a.ake002=t.ake002,a.result=this.searchCondition&&this.searchCondition.result?this.searchCondition.result:t.result,this.searchCondition&&this.searchCondition.aaz319&&(a.aaz319=this.searchCondition.aaz319);var e={param:{conditionList:a},searchCondition:a};this.$emit("condition",e)}else this.$message.error("时间范围必填")},exportData:function(){this.$emit("export")},trunTime:function(t){switch(t){case"1":this.conditionForm.setFieldsValue({aa:[s()().subtract("2","month"),s()()]});break;case"2":this.conditionForm.setFieldsValue({aa:[s()().subtract("5","month"),s()()]});break;case"3":this.conditionForm.setFieldsValue({aa:[s()().subtract("1","year"),s()()]});break;default:break}this.searchData()},filterOption:function(t,a){return a.componentOptions.children[0].text.indexOf(t)>=0}}},b=p,g=e(1001),y=(0,g.Z)(b,i,n,!1,null,"4bc3e43c",null),v=y.exports},42844:function(t,a,e){"use strict";e.d(a,{Z:function(){return d}});var i=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",[e("ta-title",{attrs:{title:"按科室"}},[e("span",{staticStyle:{display:"inline-block",float:"right","margin-right":"10px"}},[e("ta-tooltip",{attrs:{placement:"top"}},[e("template",{slot:"title"},[e("span",{staticStyle:{"font-size":"12px"}},[t._v(" 项目数：项目计费次数"),e("br"),t._v(" 异常率：异常项目数/审核项目数"),e("br")])]),e("ta-icon",{attrs:{type:"question-circle"}})],2)],1)]),e("div",{staticClass:"departmentBox"},[e("div",{staticClass:"innerBox1"},[e("ta-big-table",{ref:"xTable",attrs:{height:"500",data:t.departmentData,stripe:"",size:"mini","highlight-hover-row":"","show-footer":"","auto-resize":"","show-overflow":"","footer-method":t.footerMethod,"export-config":{isPrint:!0}},on:{"cell-click":t.turnDepartment}},[e("ta-big-table-column",{attrs:{type:"seq",field:"order",width:"40",title:"排名",align:"center"}}),e("ta-big-table-column",{attrs:{field:"deptName",sortable:"",title:"科室名称","min-width":75,align:"left"},scopedSlots:t._u([{key:"default",fn:function(a){var i=a.row;return[e("a",[t._v(t._s(i.deptName))])]}}])}),e("ta-big-table-column",{attrs:{field:"shzl",sortable:"",title:"审核项目数","min-width":90,align:"right",formatter:t.formatterUs}}),e("ta-big-table-column",{attrs:{field:"txzl",sortable:"",title:"异常项目数","min-width":90,align:"right",formatter:t.formatterUs}}),e("ta-big-table-column",{attrs:{field:"txje",sortable:"",title:"异常金额","min-width":80,align:"right",formatter:t.formatterUs}}),e("ta-big-table-column",{attrs:{field:"txl",sortable:"",title:"异常率","min-width":60,align:"center",formatter:t.formatterTXL}})],1)],1),e("div",{staticClass:"innerBox2",attrs:{id:"departmentChart"}})])],1)},n=[],r=e(92566),o=e(88412),s={components:{TaTitle:o.Z},props:{nameList:{type:Array},isDeep:{type:Boolean,default:!1},conditionData:{type:Object,default:function(){}},otherPage:{type:Boolean}},watch:{conditionData:{handler:function(t,a){this.searchObj=t.param,this.searchCondition=t.searchCondition,this.init()},deep:!0}},data:function(){return{departmentData:[],chartData:[],searchType:"department",searchObj:{},searchCondition:{},departmentChart:null}},created:function(){},mounted:function(){},beforeDestroy:function(){},methods:{formatAmount:function(t){if(isNaN(t)||!t)return 0;var a=parseFloat(t),e=String(a.toFixed(2)),i=/(-?\d+)(\d{3})/;while(i.test(e))e=e.replace(i,"$1,$2");return e},formatAmountNotoFixed:function(t){!isNaN(t)&&t||(t=0);var a=String(parseFloat(t)),e=/(-?\d+)(\d{3})/;while(e.test(a))a=a.replace(e,"$1,$2");return a},formatAmountDependingOnValue:function(t){return Number.isFinite(t)?t>=1e4?this.formatAmount(t/1e4):0==t?0:this.formatAmount(t):"-"},formatUnit:function(t,a){return t>=1e4&&"%"!=a?"万"+a:a},exportDataEvent:function(){this.$refs.xTable.exportData({type:"csv",name:"csv",filename:"科室病案列表"})},footerMethod:function(t){var a=this,e=t.columns,i=t.data;return[e.map((function(t,e){return 0===e?"合计":["deptName"].includes(t.property)?i.length:["txzl","shzl"].includes(t.property)?a.formatAmountDependingOnValue((0,r.Z)(i,t.property))+a.formatUnit((0,r.Z)(i,t.property),"次"):["txje"].includes(t.property)?a.formatAmountDependingOnValue((0,r.Z)(i,t.property))+a.formatUnit((0,r.Z)(i,t.property),"元"):null}))]},init:function(){var t=this;Base.submit(null,{url:"detailsOverview/queryDeptTableStatistics",data:this.searchObj.conditionList,showPageLoading:!1},{successCallback:function(a){t.departmentData=a.data.result.tableData,t.departmentData.forEach((function(t,a){t.txl=(t.txzl/t.shzl*100).toFixed(2)+"%",t.order=a+1}));var e=t.departmentData.slice(0,10),i=t,n=[],r=[];e.forEach((function(t){n.push(t.deptName),r.push(t)})),t.chartData={axis:n,data:r},t.departmentChart=t.$echarts.init(document.getElementById("departmentChart"));var o={tooltip:{trigger:"item",formatter:function(a){var e=a.dataIndex;return t.chartData.axis[e]+"<br>审核项目数："+i.formatAmountNotoFixed(t.chartData.data[e].shzl)+"次<br>异常项目数："+i.formatAmountNotoFixed(t.chartData.data[e].txzl)+"次<br>异常金额："+i.formatAmount(t.chartData.data[e].txje)+"元<br>异常率："+t.chartData.data[e].txl}},legend:{show:!0,selectedMode:!1,top:"bottom",data:["审核项目数","异常项目数"]},angleAxis:{type:"category",data:t.chartData.axis,axisLine:{lineStyle:{color:"#E4E4E4"}},axisLabel:{show:!1,interval:0,color:"#303133"}},radiusAxis:{z:10,axisLine:{lineStyle:{color:"rgba(92,92,92,.5)",width:1,type:"solid"}}},polar:{radius:["20%","80%"]},series:[{type:"bar",itemStyle:{color:"#DFB26F"},data:t.chartData.data.map((function(t){return t.txzl})),coordinateSystem:"polar",stack:"Min Max",name:"异常项目数"},{type:"bar",itemStyle:{color:"#5091DC"},data:t.chartData.data.map((function(t){return t.shzl})),coordinateSystem:"polar",name:"审核项目数",stack:"Min Max"}]};t.departmentChart.setOption(o),t.departmentChart.on("legendselectchanged",(function(a){var e=t.departmentChart.getOption(),i=Object.values(a.selected),n=0;i.map((function(t){t||n++})),n==i.length&&(e.legend[0].selected[a.name]=!0),t.departmentChart.setOption(e)})),t.$nextTick((function(){t.$emit("spin","department")}))}})},turnDepartment:function(t){if(this.searchCondition.toPage){var a=JSON.parse(JSON.stringify(this.searchCondition)),e=t.column.property;if("deptName"===e){var i=t.row.aaz307||"",n=a.projectInfo||"",r=a.aaz263||"",o=a.aae500;a.akb020=a.akb020||"";var s,l;a.guize&&(a.aaz319=a.guize),"1"===a.aae500?(l="普通门诊开单提醒查询",s="querycommon.html#/outpatientClinic?aaz307=".concat(i,"&aaz263=").concat(r,"&ake002=").concat(n,"&akb020=").concat(akb020,"&params=").concat(JSON.stringify(a))):"11"===a.aae500?(l="门特开单提醒查询",s="querycommon.html#/outpatientSpecialClinic?aaz307=".concat(i,"&aaz263=").concat(r,"&ake002=").concat(n,"&aae500=").concat(o,"&akb020=").concat(akb020,"&params=").concat(JSON.stringify(a))):(l="住院患者审核疑点查询",s="querycommon.html#/inpatientGather?aaz307=".concat(i,"&aaz263=").concat(r,"&ake002=").concat(n,"&aae500=").concat(o,"&akb020=").concat(akb020,"&params=").concat(JSON.stringify(a))),this.Base.openTabMenu({id:Math.floor(90*Math.random())+10,name:l,url:s,refresh:!1})}}else{var c=JSON.parse(JSON.stringify(this.searchCondition));c.keshi=t.row.aaz307;var u=JSON.stringify(this.nameList),d=this.nameList.map((function(t){return t.name})).join(">");d+=">科室";var m="statisticAnalysis.html#/departmentStatistic?nameList=".concat(u,"&searchCondition=").concat(JSON.stringify(c));this.Base.openTabMenu({id:20231117140501+t.row.deptName,name:d,url:m,refresh:!1})}},formatterTXL:function(t){t.cellValue;var a=t.row;return(a.txzl/a.shzl*100).toFixed(2)+"%"},formatterRatio:function(t){var a=t.cellValue;return(100*a).toFixed(2)+"%"},formatterUs:function(t){var a=t.cellValue;return new Number(a).toLocaleString("en-us")}}},l=s,c=e(1001),u=(0,c.Z)(l,i,n,!1,null,"213b7f46",null),d=u.exports},32307:function(t,a,e){"use strict";e.d(a,{Z:function(){return d}});var i=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",[e("ta-title",{attrs:{title:"按医生"}},[e("span",{staticStyle:{display:"inline-block",float:"right","margin-right":"10px"}},[e("ta-tooltip",{attrs:{placement:"top"}},[e("template",{slot:"title"},[e("span",{staticStyle:{"font-size":"12px"}},[t._v(" 项目数：项目计费次数"),e("br"),t._v(" 异常率：异常项目数/审核项目数"),e("br")])]),e("ta-icon",{attrs:{type:"question-circle"}})],2)],1)]),e("div",{staticClass:"doctorBox"},[e("div",{staticClass:"innerBox1"},[e("ta-big-table",{ref:"xTable",attrs:{height:"500",data:t.doctorData,stripe:"",size:"mini","highlight-hover-row":"","show-footer":"","auto-resize":"","show-overflow":"","footer-method":t.footerMethod,"export-config":{isPrint:!0}},on:{"cell-click":t.turnDoctor}},[e("ta-big-table-column",{attrs:{type:"seq",field:"order",width:"45",title:"排名",align:"center"}}),e("ta-big-table-column",{attrs:{field:"doctorName",sortable:"",title:"医生姓名","min-width":75,align:"left"},scopedSlots:t._u([{key:"default",fn:function(a){var i=a.row;return[e("a",[t._v(t._s(i.doctorName))])]}}])}),e("ta-big-table-column",{attrs:{field:"deptName",sortable:"",title:"科室名称",align:"left","min-width":70}}),e("ta-big-table-column",{attrs:{field:"shzl",sortable:"",title:"审核项目数",align:"right","min-width":70,formatter:t.formatterUs}}),e("ta-big-table-column",{attrs:{field:"txzl",sortable:"",title:"异常项目数",align:"right","min-width":70,formatter:t.formatterUs}}),e("ta-big-table-column",{attrs:{field:"txje",sortable:"",title:"异常金额","min-width":60,align:"right",formatter:t.formatterUs}}),e("ta-big-table-column",{attrs:{field:"txl",sortable:"",title:"异常率","min-width":50,align:"right",formatter:t.formatterTXL}})],1)],1)])],1)},n=[],r=e(92566),o=e(88412),s={components:{TaTitle:o.Z},props:{nameList:{type:Array},isDeep:{type:Boolean,default:!1},conditionData:{type:Object,default:function(){}},otherPage:{type:Boolean}},watch:{conditionData:{handler:function(t,a){this.searchObj=t.param,this.searchCondition=t.searchCondition,this.init()},deep:!0}},data:function(){return{doctorData:[],searchType:"doctor",searchObj:{},searchCondition:{}}},created:function(){},mounted:function(){},beforeDestroy:function(){},methods:{formatAmount:function(t){if(isNaN(t)||!t)return"";var a=parseFloat(t),e=String(a.toFixed(2)),i=/(-?\d+)(\d{3})/;while(i.test(e))e=e.replace(i,"$1,$2");return e},formatAmountDependingOnValue:function(t){return Number.isFinite(t)?t>=1e4?this.formatAmount(t/1e4):0==t?0:this.formatAmount(t):"-"},formatUnit:function(t,a){return t>=1e4&&"%"!=a?"万"+a:a},exportDataEvent:function(){this.$refs.xTable.exportData({type:"csv",name:"csv",filename:"医生病案列表"})},init:function(){var t=this;this.Base.submit(null,{url:"detailsOverview/queryDoctorTableStatistics",data:this.searchObj.conditionList,showPageLoading:!1},{successCallback:function(a){t.doctorData=a.data.result,t.doctorData.forEach((function(t,a){t.txl=(t.txzl/t.shzl*100).toFixed(2)+"%",t.order=a+1})),t.$nextTick((function(){t.$emit("spin","doctor")}))}})},footerMethod:function(t){var a=this,e=t.columns,i=t.data;return[e.map((function(t,e){return 0===e?"合计":["doctorName"].includes(t.property)?i.length:["txzl","shzl"].includes(t.property)?a.formatAmountDependingOnValue((0,r.Z)(i,t.property))+a.formatUnit((0,r.Z)(i,t.property),"次"):["txje"].includes(t.property)?a.formatAmountDependingOnValue((0,r.Z)(i,t.property))+a.formatUnit((0,r.Z)(i,t.property),"元"):null}))]},showTooltipMethod:function(t){var a=t.type,e=t.column,i=(t.row,t.items,t._columnIndex,e.property);return"header"==a?"YCBAZB"===i?"医生的异常病案数/医生的质控病案数*100%":"YCBAS_ratio"===i?"医生的异常病案数/医生病案总数*100%":"":""},turnDoctor:function(t){var a=this.searchCondition.guize&&this.searchCondition.xiangmu&&!this.searchCondition.keshi;if(a||this.searchCondition.toPage){var e=JSON.parse(JSON.stringify(this.searchCondition)),i=t.column.property;if("doctorName"===i){var n=t.row.aaz307||"",r=e.projectInfo||"",o=t.row.aaz263||"",s=e.aae500;e.akb020=e.akb020||"";var l,c;e.guize&&(e.aaz319=e.guize),"1"===e.aae500?(c="普通门诊开单提醒查询",l="querycommon.html#/outpatientClinic?aaz307=".concat(n,"&aaz263=").concat(o,"&ake002=").concat(r,"&akb020=").concat(akb020,"&params=").concat(JSON.stringify(e))):"11"===e.aae500?(c="门特开单提醒查询",l="querycommon.html#/outpatientSpecialClinic?aaz307=".concat(n,"&aaz263=").concat(o,"&ake002=").concat(r,"&aae500=").concat(s,"&akb020=").concat(akb020,"&params=").concat(JSON.stringify(e))):(c="住院患者审核疑点查询",l="querycommon.html#/inpatientGather?aaz307=".concat(n,"&aaz263=").concat(o,"&ake002=").concat(r,"&aae500=").concat(s,"&akb020=").concat(akb020,"&params=").concat(JSON.stringify(e))),this.Base.openTabMenu({id:Math.floor(90*Math.random())+10,name:c,url:l,refresh:!1})}}else{var u=JSON.parse(JSON.stringify(this.searchCondition));u.yisheng=t.row.aaz263,u.keshi=t.row.aaz307;var d=JSON.stringify(this.nameList),m=this.nameList.map((function(t){return t.name})).join(">");m+=">医生";this.nameList.filter((function(t){return"总览"!=t.name}));var h="statisticAnalysis.html#/doctorStatistic?nameList=".concat(d,"&searchCondition=").concat(JSON.stringify(u));this.Base.openTabMenu({id:20231117140502+t.row.doctorName,name:m,url:h})}},formatterTXL:function(t){t.cellValue;var a=t.row;return(a.txzl/a.shzl*100).toFixed(2)+"%"},formatterRatio:function(t){var a=t.cellValue;return(100*a).toFixed(2)+"%"},formatterUs:function(t){var a=t.cellValue;return new Number(a).toLocaleString("en-us")}}},l=s,c=e(1001),u=(0,c.Z)(l,i,n,!1,null,"575ad2d4",null),d=u.exports},6097:function(t,a,e){"use strict";e.d(a,{Z:function(){return d}});var i=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",[e("ta-title",{attrs:{title:"按项目"}},[e("span",{staticStyle:{display:"inline-block",float:"right","margin-right":"10px"}},[e("ta-tooltip",{attrs:{placement:"top"}},[e("template",{slot:"title"},[e("span",{staticStyle:{"font-size":"12px"}},[t._v(" 异常率：异常项目数/审核项目数 ")])]),e("ta-icon",{attrs:{type:"question-circle"}})],2)],1)]),e("div",{staticClass:"departmentBox"},[e("div",{staticClass:"innerBox1"},[e("ta-big-table",{ref:"xTable",attrs:{height:"500",data:t.itemData,stripe:"",size:"mini","highlight-hover-row":"","show-footer":"","auto-resize":"","show-overflow":"","footer-method":t.footerMethod,"export-config":{isPrint:!0}},on:{"cell-click":t.turnDepartment}},[e("ta-big-table-column",{attrs:{type:"seq",field:"order",width:"45",title:"排名",align:"center"}}),e("ta-big-table-column",{attrs:{field:"xmName",sortable:"",title:"项目名称","min-width":75,align:"left"},scopedSlots:t._u([{key:"default",fn:function(a){var i=a.row;return[e("a",[t._v(t._s(i.xmName))])]}}])}),e("ta-big-table-column",{attrs:{field:"shzl",sortable:"",title:"审核项目数","min-width":70,align:"right",formatter:t.formatterUs}}),e("ta-big-table-column",{attrs:{field:"txzl",sortable:"",title:"异常项目数","min-width":70,align:"right",formatter:t.formatterUs}}),e("ta-big-table-column",{attrs:{field:"txsl",sortable:"",title:"异常数量","min-width":60,align:"right",formatter:t.formatterUs}}),e("ta-big-table-column",{attrs:{field:"txje",sortable:"",title:"异常金额","min-width":60,align:"right",formatter:t.formatterUs}}),e("ta-big-table-column",{attrs:{field:"txl",sortable:"",title:"异常率","min-width":50,align:"center",formatter:t.formatterTXL}})],1)],1),e("div",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}],attrs:{id:"itemChart"}})])],1)},n=[],r=e(92566),o=e(88412),s={components:{TaTitle:o.Z},props:{nameList:{type:Array},isDeep:{type:Boolean,default:!1},conditionData:{type:Object,default:function(){}},otherPage:{type:Boolean}},watch:{conditionData:{handler:function(t,a){this.searchObj=t.param,this.searchCondition=t.searchCondition,this.init()},deep:!0}},data:function(){return{itemData:[],chartData:[],searchType:"department",searchObj:{},searchCondition:{},itemChart:null}},created:function(){},mounted:function(){},beforeDestroy:function(){},methods:{formatAmount:function(t){if(isNaN(t)||!t)return"";var a=parseFloat(t),e=String(a.toFixed(2)),i=/(-?\d+)(\d{3})/;while(i.test(e))e=e.replace(i,"$1,$2");return e},formatAmountDependingOnValue:function(t){return Number.isFinite(t)?t>=1e4?this.formatAmount(t/1e4):0==t?0:this.formatAmount(t):"-"},formatUnit:function(t,a){return t>=1e4&&"%"!=a?"万"+a:a},exportDataEvent:function(){this.$refs.xTable.exportData({type:"csv",name:"csv",filename:"科室病案列表"})},footerMethod:function(t){var a=this,e=t.columns,i=t.data;return[e.map((function(t,e){return 0===e?"合计":["xmName"].includes(t.property)?i.length:["txzl","shzl"].includes(t.property)?a.formatAmountDependingOnValue((0,r.Z)(i,t.property))+a.formatUnit((0,r.Z)(i,t.property),"次"):["txje"].includes(t.property)?a.formatAmountDependingOnValue((0,r.Z)(i,t.property))+a.formatUnit((0,r.Z)(i,t.property),"元"):["txsl"].includes(t.property)?a.formatAmountDependingOnValue((0,r.Z)(i,t.property))+a.formatUnit((0,r.Z)(i,t.property),""):null}))]},showTooltipMethod:function(t){var a=t.type,e=t.column,i=t.row,n=(t.items,t._columnIndex,e.property);return"header"==a?"YCBAZB"===n?"科室的异常病案数/科室的质控病案数*100%":"YCBAS_ratio"===n?"科室的异常病案数/质控病案总数*100%":"":"body"==a?"BAKS_desc"===n&&i.BAKS_desc.length>6?i.BAKS_desc:"":void 0},init:function(){var t=this;this.Base.submit(null,{url:"detailsOverview/queryItemTableStatistics",data:this.searchObj.conditionList,showPageLoading:!1},{successCallback:function(a){t.itemData=a.data.result.tableData,t.itemData.forEach((function(t,a){t.txl=(t.txzl/t.shzl*100).toFixed(2)+"%",t.order=a+1}));var e=t.itemData.slice(0,10),i=[],n=[];e.forEach((function(t){i.push(t.xmName),n.push(t)})),t.chartData={axis:i,data:n},t.itemChart=t.$echarts.init(document.getElementById("itemChart"));var r={tooltip:{trigger:"item",formatter:function(a){var e=a.dataIndex;return t.chartData.axis[e]+"<br>审核项目数："+t.chartData.data[e].shzl+"<br>异常项目数："+t.chartData.data[e].txzl+"<br>异常金额："+t.chartData.data[e].txje+"<br>异常率："+t.chartData.data[e].txl}},legend:{show:!0,selectedMode:!1,top:"bottom",data:["审核项目数","异常项目数"]},angleAxis:{type:"category",data:t.chartData.axis,axisLine:{lineStyle:{color:"#E4E4E4"}},axisLabel:{show:!1,interval:0,color:"#303133"}},radiusAxis:{z:10,axisLine:{lineStyle:{color:"rgba(92,92,92,.5)",width:1,type:"solid"}}},polar:{radius:["20%","80%"]},series:[{type:"bar",itemStyle:{color:"#DFB26F"},data:t.chartData.data.map((function(t){return t.txzl})),coordinateSystem:"polar",stack:"Min Max",name:"异常项目数"},{type:"bar",itemStyle:{color:"#5091DC"},data:t.chartData.data.map((function(t){return t.shzl})),coordinateSystem:"polar",name:"审核项目数",stack:"Min Max"}]};t.itemChart.setOption(r),t.itemChart.on("legendselectchanged",(function(a){var e=t.itemChart.getOption(),i=Object.values(a.selected),n=0;i.map((function(t){t||n++})),n==i.length&&(e.legend[0].selected[a.name]=!0),t.itemChart.setOption(e)})),t.$nextTick((function(){t.$emit("spin","item")}))}})},turnDepartment:function(t){var a=t.row,e=t.column;if(this.searchCondition.toPage){var i=JSON.parse(JSON.stringify(this.searchCondition)),n=e.property;if("xmName"===n){var r=i.aaz307||"",o=a.ake001||"",s=i.aaz263||"",l=i.aae500;i.akb020=i.akb020||"";var c,u;i.guize&&(i.aaz319=i.guize),"1"===i.aae500?(u="普通门诊开单提醒查询",c="querycommon.html#/outpatientClinic?aaz307=".concat(r,"&aaz263=").concat(s,"&ake002=").concat(o,"&akb020=").concat(akb020,"&params=").concat(JSON.stringify(i))):"11"===i.aae500?(u="门特开单提醒查询",c="querycommon.html#/outpatientSpecialClinic?aaz307=".concat(r,"&aaz263=").concat(s,"&ake002=").concat(o,"&aae500=").concat(l,"&akb020=").concat(akb020,"&params=").concat(JSON.stringify(i))):(u="住院患者审核疑点查询",c="querycommon.html#/inpatientGather?aaz307=".concat(r,"&aaz263=").concat(s,"&ake002=").concat(o,"&aae500=").concat(l,"&akb020=").concat(akb020,"&params=").concat(JSON.stringify(i))),this.Base.openTabMenu({id:Math.floor(90*Math.random())+10,name:u,url:c,refresh:!1})}}else{var d=JSON.parse(JSON.stringify(this.searchCondition));d.xiangmu=a.ake001,d.ake002=a.xmName;var m=JSON.stringify(this.nameList),h=this.nameList.map((function(t){return t.name})).join(">");h+=">项目";var f="statisticAnalysis.html#/itemStatistic?nameList=".concat(m,"&searchCondition=").concat(JSON.stringify(d));this.Base.openTabMenu({id:20231117140504+a.deptName,name:h,url:f,refresh:!1})}},formatterTXL:function(t){t.cellValue;var a=t.row;return(a.txzl/a.shzl*100).toFixed(2)+"%"},formatterRatio:function(t){var a=t.cellValue;return(100*a).toFixed(2)+"%"},formatterUs:function(t){var a=t.cellValue;return new Number(a).toLocaleString("en-us")}}},l=s,c=e(1001),u=(0,c.Z)(l,i,n,!1,null,"27b2a841",null),d=u.exports},32717:function(t,a,e){"use strict";e.d(a,{Z:function(){return c}});var i=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticClass:"cardFlex"},[e("ta-card",{staticClass:"cardItem",staticStyle:{"background-color":"rgba(236, 128, 141, 0.298039215686275)"}},[e("p",{staticStyle:{"font-size":"16px","padding-top":"25px"}},[e("span",{staticStyle:{"font-size":"20px",color:"#EC808D"},on:{mouseenter:function(a){return t.mouseenter(a,t.situationData.txje.toLocaleString("en-us"))},mouseleave:function(a){return t.mouseleave(a,t.situationData.txje.toLocaleString("en-us"))}}},[t._v(" "+t._s(t.formatAmountDependingOnValue(t.situationData.txje))+" "),e("ta-tooltip",{ref:"tooltip",attrs:{visible:!1}})],1)]),e("p",{staticStyle:{"font-size":"14px"}},[e("span",[t._v("异常总金额("+t._s(t.formatUnit(t.situationData.txje,"元"))+")")])])]),e("ta-card",{staticClass:"cardItem",staticStyle:{"background-color":"rgba(230, 162, 60, 0.298039215686275)"}},[e("p",{staticStyle:{"font-size":"16px","padding-top":"25px"}},[e("span",{staticStyle:{"font-size":"20px",color:"#F59A23","text-align":"center"},on:{mouseenter:function(a){return t.mouseenter(a,t.situationData.txzl.toLocaleString("en-us"))},mouseleave:function(a){return t.mouseleave(a,t.situationData.txzl.toLocaleString("en-us"))}}},[t._v(" "+t._s(t.formatAmountDependingOnValue(t.situationData.txzl))+" ")])]),e("p",{staticStyle:{"font-size":"14px"}},[e("span",[t._v("异常项目数("+t._s(t.formatUnit(t.situationData.txzl,"次"))+")")])])]),e("ta-card",{staticClass:"cardItem",staticStyle:{"background-color":"rgba(103, 194, 58, 0.149019607843137)"}},[e("p",{staticStyle:{"font-size":"16px","padding-top":"25px"}},[e("span",{staticStyle:{"font-size":"20px",color:"#67C23A"}},[t._v(t._s(t.situationData.txl.toLocaleString("en-us")))])]),e("p",{staticStyle:{"font-size":"14px"}},[e("span",[t._v("异常率")])])])],1)},n=[],r={props:{conditionData:{type:Object,default:function(){}}},watch:{conditionData:{handler:function(t,a){this.searchObj=t.param,this.searchCondition=t.searchCondition,this.init()},deep:!0}},data:function(){return{situationData:{txje:0,txzl:0,txl:0},searchObj:{},searchCondition:{}}},created:function(){},mounted:function(){},beforeDestroy:function(){},methods:{formatAmount:function(t){if(isNaN(t)||!t)return"";var a=parseFloat(t),e=String(a.toFixed(2)),i=/(-?\d+)(\d{3})/;while(i.test(e))e=e.replace(i,"$1,$2");return e},formatAmountDependingOnValue:function(t){return Number.isFinite(t)?t>=1e4?this.formatAmount(t/1e4):0==t?0:this.formatAmount(t):"-"},formatUnit:function(t,a){return t>=1e4&&"%"!=a?"万"+a:a},init:function(){var t=this;this.Base.submit(null,{url:"detailsOverview/queryOverallSituation",data:this.searchObj.conditionList,showPageLoading:!1},{successCallback:function(a){a.data.result&&(t.situationData.txje=a.data.result.txje||0,t.situationData.txzl=a.data.result.txzl||0,t.situationData.txl=a.data.result.shzl?(a.data.result.txzl/a.data.result.shzl*100).toFixed(2)+"%":"0%"),t.$nextTick((function(){t.$emit("spin","overSituation")}))}})},mouseenter:function(t,a){this.$refs.tooltip.open(t.target,"".concat(a))},mouseleave:function(t,a){this.$refs.tooltip.close()}}},o=r,s=e(1001),l=(0,s.Z)(o,i,n,!1,null,"ad8c6152",null),c=l.exports},8416:function(t,a,e){"use strict";e.d(a,{Z:function(){return c}});var i=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{attrs:{id:"overTrend"}})},n=[],r={props:{conditionData:{type:Object,default:function(){}}},watch:{conditionData:{handler:function(t,a){this.searchObj=t.param,this.searchCondition=t.searchCondition,this.init()},deep:!0}},data:function(){return{overTrend:null,searchObj:{},searchCondition:{}}},created:function(){},mounted:function(){},beforeDestroy:function(){},methods:{formatAmount:function(t){!isNaN(t)&&t||(t=0);var a=String(parseFloat(t)),e=/(-?\d+)(\d{3})/;while(e.test(a))a=a.replace(e,"$1,$2");return a},init:function(){var t=this,a=this,e=[],i=[],n=[],r=[];this.Base.submit(null,{url:"detailsOverview/queryOverallTrend",data:this.searchObj.conditionList,showPageLoading:!1},{successCallback:function(o){o.data.result.forEach((function(t){e.push(t.sjfw),i.push(t.shzl),n.push(t.txzl),r.push(t.shzl?(t.txzl/t.shzl*100).toFixed(2):0)}));var s=[{name:"审核项目数",value:i},{name:"异常项目数",value:n},{name:"异常率",value:r}];t.overTrend=t.$echarts.init(document.getElementById("overTrend"));for(var l=["#2E82E4","#FCAE57","#01CFB0"],c={legend:["审核项目数","异常项目数","异常率"],area:e,data:s},u={color:l,grid:{top:"25%",bottom:"12%",left:"6%",right:"6%"},tooltip:{trigger:"axis",axisPointer:{type:"shadow"},formatter:function(t){for(var e=t[0].name,i=0,n=t.length;i<n;i++)e+="<br/>"+t[i].marker+" "+t[i].seriesName+": "+a.formatAmount(t[i].value)+(2===i?"%":"次");return e}},legend:{itemWidth:15,itemHeight:5,textStyle:{fontSize:12,color:"#8C8C8C",padding:[3,30,0,0]},data:c.legend},xAxis:{type:"category",axisTick:{show:!1},axisLine:{lineStyle:{color:"#BFBFBF"}},data:c.area,axisLabel:{show:!0,color:"#000",fontSize:12}},yAxis:[{name:"单位:次",nameTextStyle:{color:"#BFBFBF"},type:"value",axisLabel:{show:!0,color:"#000",fontSize:12,formatter:function(t){return t>=1e4?t/1e4+"万次":0==t?0:t}},splitLine:{show:!1},axisLine:{show:!1},axisTick:{show:!1}},{name:"单位:%",nameTextStyle:{color:"#BFBFBF"},type:"value",position:"right",axisLabel:{show:!0,color:"#000",fontSize:12},splitLine:{show:!1},axisLine:{show:!1},axisTick:{show:!1}}],series:[]},d=0;d<c.legend.length;d++)u.series.push({name:c.legend[d],type:2==d?"line":"bar",barWidth:"15%",yAxisIndex:2==d?1:0,data:c.data[d].value});t.overTrend.setOption(u)}}),this.$nextTick((function(){t.$emit("spin","overTrend")}))}}},o=r,s=e(1001),l=(0,s.Z)(o,i,n,!1,null,"f8dd47da",null),c=l.exports},64511:function(t,a,e){"use strict";e.d(a,{Z:function(){return u}});var i=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",[e("ta-title",{attrs:{title:"按规则"}},[e("span",{staticStyle:{display:"inline-block",float:"right","margin-right":"10px"}},[e("ta-tooltip",{attrs:{placement:"top"}},[e("template",{slot:"title"},[e("span",{staticStyle:{"font-size":"12px"}},[t._v(" 异常占比：当前规则异常项目数/总异常项目数 ")])]),e("ta-icon",{attrs:{type:"question-circle"}})],2)],1)]),e("div",{staticClass:"ruleBox"},[e("div",{staticClass:"innerBox1"},[t._m(0),e("div",{staticClass:"ruleContend"},[e("ta-big-table",{ref:"xTable",attrs:{data:t.ruleData,"export-config":{isPrint:!0},hidden:"true"}},[e("ta-big-table-column",{attrs:{type:"seq",field:"order",width:"45",title:"排名",align:"center"}}),e("ta-big-table-column",{attrs:{field:"gzName",title:"规则名称",align:"left"}}),e("ta-big-table-column",{attrs:{field:"txzl",title:"异常项目数",align:"right"}}),e("ta-big-table-column",{attrs:{field:"txje",title:"异常金额",align:"right"}}),e("ta-big-table-column",{attrs:{field:"txl",title:"异常占比",width:"70",align:"right",formatter:t.formatterYCBAZB}})],1),t._l(t.ruleData,(function(a){return e("div",{staticClass:"hoverRule",on:{click:function(e){return t.turnRule(a.aaz319,a.gzName)}}},[e("div",{staticClass:"ruleData"},[e("a",{staticClass:"ruleName"},[t._v(t._s(a.gzName.length>20?a.gzName.slice(0,18)+"...":a.gzName))]),e("div",{staticClass:"qualityNum"},[t._v(t._s(new Number(a.txzl).toLocaleString("en-us")))]),e("div",{staticClass:"qualityNum"},[t._v(t._s(new Number(a.txje).toLocaleString("en-us")))]),e("div",{staticClass:"ratioNum"},[e("ta-tooltip",[t._v(" "+t._s((a.txzl/a.shzl*100).toFixed(2))+"% ")])],1)]),e("ta-progress",{staticStyle:{width:"96%","margin-left":"10px"},attrs:{percent:Number((a.txzl/a.shzl*100).toFixed(2)),showInfo:!1,strokeColor:"#1890ff"}})],1)}))],2)])])],1)},n=[function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticClass:"ruleTitle"},[e("div",{staticClass:"ruleName"},[t._v("规则名称")]),e("div",{staticClass:"qualityNum"},[t._v("异常项目数")]),e("div",{staticClass:"qualityNum"},[t._v("异常金额")]),e("div",{staticClass:"ratioNum"},[t._v(" 异常占比 ")])])}],r=e(88412),o={components:{TaTitle:r.Z},props:{nameList:{type:Array},isDeep:{type:Boolean,default:!1},conditionData:{type:Object,default:function(){}},otherPage:{type:Boolean}},watch:{conditionData:{handler:function(t,a){this.searchObj=t.param,this.searchCondition=t.searchCondition,this.init()},deep:!0}},data:function(){return{ruleData:[],chartData:[],ruleChart:null,searchType:"rule",searchObj:{},searchCondition:{},ruleList:[]}},created:function(){},mounted:function(){this.fnQueryRuleList()},beforeDestroy:function(){},methods:{formatterYCBAZB:function(t){t.cellValue;var a=t.row;return(a.txzl/a.shzl*100).toFixed(2)+"%"},formatterUs:function(t){var a=t.cellValue;return new Number(a).toLocaleString("en-us")},exportDataEvent:function(){this.$refs.xTable.exportData({type:"csv",name:"csv",filename:"规则病案列表"})},init:function(){var t=this;this.Base.submit(null,{url:"detailsOverview/queryRuleTableStatistics",data:this.searchObj.conditionList,showPageLoading:!1},{successCallback:function(a){t.ruleData=a.data.result,t.ruleData.forEach((function(t,a){t.txl=(t.txzl/t.shzl*100).toFixed(2)+"%",t.order=a+1})),t.$nextTick((function(){t.$emit("spin","rule")}))}})},fnQueryRuleList:function(t){var a=this,e={url:"/miimCommonRead/queryRuleTypeDicByAae500",autoValid:!0,data:{aae500:t}},i={successCallback:function(t){a.ruleList=t.data.ruleList},failCallback:function(t){a.$message.error("医院数据加载失败")}};this.Base.submit(null,e,i)},turnRule:function(t,a){if(this.searchCondition.toPage){var e=JSON.parse(JSON.stringify(this.searchCondition)),i=e.aaz307||"",n=e.projectInfo||"",r=e.aaz263||"",o=e.aae500;e.akb020=e.akb020||"";var s,l,c="",u=this.ruleList.filter((function(t){return t.label===a}));u.length>0&&(c=u[0].value,e.aaz319=c),"1"===e.aae500?(l="普通门诊开单提醒查询",s="querycommon.html#/outpatientClinic?aaz307=".concat(i,"&aaz263=").concat(r,"&ake002=").concat(n,"&akb020=").concat(akb020,"&params=").concat(JSON.stringify(e))):"11"===e.aae500?(l="门特开单提醒查询",s="querycommon.html#/outpatientSpecialClinic?aaz307=".concat(i,"&aaz263=").concat(r,"&ake002=").concat(n,"&aae500=").concat(o,"&akb020=").concat(akb020,"&params=").concat(JSON.stringify(e))):(l="住院患者审核疑点查询",s="querycommon.html#/inpatientGather?aaz307=".concat(i,"&aaz263=").concat(r,"&ake002=").concat(n,"&aae500=").concat(o,"&akb020=").concat(akb020,"&params=").concat(JSON.stringify(e))),this.Base.openTabMenu({id:Math.floor(90*Math.random())+10,name:l,url:s,refresh:!1})}else{var d=JSON.parse(JSON.stringify(this.searchCondition)),m=t.toString(),h="",f=this.ruleList.filter((function(t){return t.label===a}));h=f[0].value,f.length>0&&(d.guize=h),d.aaz319=m;var p=JSON.stringify(this.nameList),b=(this.nameList.filter((function(t){return"总览"!=t.name})),this.nameList.map((function(t){return t.name})).join(">"));b+=">规则";JSON.parse(JSON.stringify(this.nameList));var g="statisticAnalysis.html#/ruleStatistic?nameList=".concat(p,"&searchCondition=").concat(JSON.stringify(d));this.Base.openTabMenu({id:20231117140503+a,name:b,url:g})}}}},s=o,l=e(1001),c=(0,l.Z)(s,i,n,!1,null,"e644cc74",null),u=c.exports},36766:function(t,a,e){"use strict";var i=e(66586);a["Z"]=i.Z},26263:function(t,a,e){"use strict";e.d(a,{s:function(){return i},x:function(){return n}});var i=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("h3",{staticStyle:{"font-weight":"normal"}},[e("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),e("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},n=[]},66586:function(t,a){"use strict";var e={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};a["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:e}}}},55382:function(){},61219:function(){}}]);
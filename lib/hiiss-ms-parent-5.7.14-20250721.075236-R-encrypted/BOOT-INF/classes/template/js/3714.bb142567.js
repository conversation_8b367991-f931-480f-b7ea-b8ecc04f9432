"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[3714],{88412:function(t,e,a){var i=a(26263),l=a(36766),n=a(1001),r=(0,n.Z)(l.Z,i.s,i.x,!1,null,"5e7ef0ae",null);e["Z"]=r.exports},3714:function(t,e,a){a.r(e),a.d(e,{default:function(){return H}});var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticStyle:{height:"120px"}},[a("ta-border-layout",[a("div",{staticStyle:{overflow:"hidden",height:"100%"}},[a("search-term",{ref:"term",staticClass:"fit",attrs:{indexName:t.indexName,typeCode:t.typeCode},on:{fnQuery:t.fnQuery}})],1)])],1),a("div",{staticStyle:{height:"870px","margin-top":"-20px"}},[a("ta-border-layout",{attrs:{layout:{left:"26%"}}},[a("div",{attrs:{slot:"left"},slot:"left"},[a("div",[a("over-standard-index-table",{ref:"overStandardIndexTable",attrs:{indexDataList:t.indexDataList,tableWidth:t.tableWidth1},on:{fnSelectRow:t.fnSelectRow}})],1),a("div",{staticStyle:{width:"103.8%",height:"20px","border-bottom":"14px solid #f0f2f5","margin-left":"-10px","margin-bottom":"10px"}}),a("div",[a("over-value-index-table",{ref:"overStandardIndexTable",attrs:{indexDataList:t.indexDataList,tableWidth:t.tableWidth2},on:{fnSelectRow:t.fnSelectRow}})],1),a("div",{staticStyle:{width:"103.8%",height:"20px","border-bottom":"14px solid #f0f2f5","margin-left":"-10px","margin-bottom":"10px"}}),a("div",[a("excellent-index-table",{ref:"overStandardIndexTable",attrs:{indexDataList:t.indexDataList,tableWidth:t.tableWidth3},on:{fnSelectRow:t.fnSelectRow}})],1)]),a("div",[a("div",[a("index-rank-table",{ref:"indexRankTable",attrs:{paramData:t.paramData,title_barRank:"指标排名"}})],1),a("div",{staticStyle:{width:"101.31%",height:"20px","border-bottom":"14px solid #f0f2f5","margin-left":"-10px"}}),a("div",{staticStyle:{"margin-top":"10px"}},[a("index-trend-line-chart",{ref:"indexTrendLineChart",attrs:{paramData:t.paramData,url:"indexAnalysis/queryYearOnYearTrend",title_barRank:"指标同比趋势"}})],1)])])],1)])},l=[],n=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("div",[i("ta-form",{staticStyle:{"padding-top":"20px"},attrs:{autoFormCreate:function(e){t.form=e},layout:"horizontal",formLayout:!0}},[i("ta-form-item",{attrs:{fieldDecoratorId:"allDate",labelCol:{span:5},wrapperCol:{span:16},span:7,fieldDecoratorOptions:{rules:[{required:!0,message:"必须选取时间"}]},required:!0,"init-value":e.rangeValue}},[i("span",{attrs:{slot:"label"},slot:"label"},[e._v("时间范围")]),i("ta-range-picker",{attrs:{type:"month","allow-one":!0},on:{select:e.fnInitIndexTable}})],1),i("ta-form-item",{staticStyle:{"margin-left":"1%"},attrs:{fieldDecoratorId:"indexName",span:7,labelCol:{span:4},wrapperCol:{span:25},require:{message:"请选择指标项目"}}},[i("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("指标项目")]),i("ta-select",{staticStyle:{width:"80%"},attrs:{placeholder:"请选择指标项目","allow-clear":"",options:e.itemData}})],1),i("ta-form-item",{staticStyle:{"margin-left":"1%"},attrs:{fieldDecoratorId:"insuType",span:6,labelCol:{span:5},wrapperCol:{span:10},require:{message:"请选择险种类型"}}},[i("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("险种类型")]),i("ta-select",{attrs:{placeholder:"险种类型选择",allowClear:""}},[i("ta-select-option",{attrs:{value:"0"}},[e._v("全部")]),i("ta-select-option",{attrs:{value:"310"}},[e._v("职工医保")]),i("ta-select-option",{attrs:{value:"390"}},[e._v("居民医保")])],1)],1),i("ta-button",{staticStyle:{"margin-left":"24px",float:"right"},attrs:{type:"primary",icon:"search"},on:{click:e.fnQuery}},[e._v("查询 ")])],1)],1)},r=[],o=a(36797),s=a.n(o),d={name:"searchTerm",props:{indexName:String,typeCode:String},data:function(){return{itemData:[],rangeValue:[TaUtils.getMoment(new Date((new Date).getTime()).toISOString().slice(0,5)+"01-01","YYYY-MM-DD"),TaUtils.getMoment((new Date).toISOString().slice(0,10),"YYYY-MM-DD")]}},mounted:function(){var t=this;this.$nextTick((function(){t.handleSearchIndicatorItems()}))},watch:{indexName:{handler:function(t){var e=this;t&&this.$nextTick((function(){e.form.setFieldsValue({indexName:t}),e.fnQuery()}))}},typeCode:{handler:function(t){var e=this;t&&this.$nextTick((function(){e.form.setFieldsValue({insuType:t}),e.fnQuery()}))}}},methods:{moment:s(),handleSearchIndicatorItems:function(){var t=this,e={url:"indexAnalysis/queryProjectName"},a={successCallback:function(e){t.itemData=e.data.result},failCallback:function(e){t.$message.error("查询指标项目数据失败")}};this.Base.submit(null,e,a)},fnInitIndexTable:function(t){this.$emit("fnInitIndexTable",t)},fnQuery:function(){var t=this,e=this.form.getFieldsValue();if(null!=e.allDate[0]&&""!=e.allDate[0])if(null!=e.allDate[1]&&""!=e.allDate[1]){for(var a=0;a<this.itemData.length;a++)this.itemData[a].value==e.indexName&&(e.indexCode=this.itemData[a].label);e.startDate=e.allDate[0].format("YYYYMM"),e.endDate=e.allDate[1].format("YYYYMM"),this.form.validateFields((function(a,i){a||t.$emit("fnQuery",e)}))}else this.$message.error("请选择完整日期");else this.$message.error("请选择完整日期")},resetForm:function(){this.form.resetFields()}}},c=d,u=a(1001),h=(0,u.Z)(c,n,r,!1,null,"a3a5f182",null),m=h.exports,f=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticStyle:{"border-bottom":"1px solid rgba(153, 153, 153, 0.14)"}},[a("ta-alert",{attrs:{type:"error",message:"超过标准值的指标",banner:""}},[a("p",{attrs:{slot:"closeText"},slot:"closeText"},[a("span",{staticStyle:{color:"red"}},[t._v(t._s(t.indexDataList.table1.length))])])])],1),a("ta-big-table",{ref:"generalTable",staticStyle:{width:"100%",cursor:"pointer"},attrs:{"highlight-hover-row":"","show-header":!1,height:t.tableWidth,data:t.indexDataList.table1,border:"none"},on:{"cell-click":t.fnSelectRow}},[a("ta-big-table-column",{attrs:{type:"seq",align:"left",title:"排名",width:"55"}}),a("ta-big-table-column",{attrs:{field:"indexName",title:"指标名称",width:"300"}}),a("ta-big-table-column",{attrs:{field:"type",title:"类型",width:"65",align:"right"}})],1)],1)},b=[],p={name:"overStandardIndexTable",props:{indexDataList:Object,tableWidth:Number},data:function(){return{indexName:"",dataList:[]}},mounted:function(){},methods:{fnSelectRow:function(t){var e=t.row;this.indexName=e.indexCode,this.$emit("fnSelectRow",e)}}},g=p,x=(0,u.Z)(g,f,b,!1,null,"382a4253",null),y=x.exports,v=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticStyle:{"border-bottom":"1px solid rgba(153, 153, 153, 0.14)"}},[a("ta-alert",{attrs:{message:"超过预警值的指标",banner:""}},[a("p",{attrs:{slot:"closeText"},slot:"closeText"},[a("span",{staticStyle:{color:"#E6A23C"}},[t._v(t._s(t.indexDataList.table2.length))])])])],1),a("ta-big-table",{ref:"generalTable",staticStyle:{width:"100%",cursor:"pointer"},attrs:{"show-header":!1,"highlight-hover-row":"",height:t.tableWidth,data:t.indexDataList.table2,border:"none"},on:{"cell-click":t.fnSelectRow}},[a("ta-big-table-column",{attrs:{type:"seq",align:"left",title:"排名",width:"55"}}),a("ta-big-table-column",{attrs:{field:"indexName",title:"指标名称",width:"300"}}),a("ta-big-table-column",{attrs:{field:"type",title:"类型",width:"65",align:"right"}})],1)],1)},S=[],w={name:"overValueIndexTable",props:{indexDataList:Object,tableWidth:Number},data:function(){return{indexName:"",dataList:[]}},mounted:function(){},methods:{fnSelectRow:function(t){var e=t.row;this.indexName=e.indexCode,this.$emit("fnSelectRow",e)}}},D=w,T=(0,u.Z)(D,v,S,!1,null,"cc428252",null),k=T.exports,C=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticStyle:{"border-bottom":"1px solid rgba(153, 153, 153, 0.14)"}},[a("ta-alert",{attrs:{type:"success",message:"优秀指标",banner:""}},[a("p",{attrs:{slot:"closeText"},slot:"closeText"},[a("span",{staticStyle:{color:"#67C23A"}},[t._v(t._s(t.indexDataList.table3.length))])])])],1),a("ta-big-table",{ref:"generalTable",staticStyle:{width:"100%",cursor:"pointer"},attrs:{"show-header":!1,"highlight-hover-row":"",height:t.tableWidth,data:t.indexDataList.table3,border:"none"},on:{"cell-click":t.fnSelectRow}},[a("ta-big-table-column",{attrs:{type:"seq",align:"left",title:"排名",width:"55"}}),a("ta-big-table-column",{attrs:{field:"indexName",title:"指标名称",width:"300"}}),a("ta-big-table-column",{attrs:{field:"type",title:"类型",width:"65",align:"right"}})],1)],1)},Y=[],_={name:"excellentIndexTable",props:{indexDataList:Object,tableWidth:Number},data:function(){return{indexName:"",dataList:[]}},mounted:function(){},methods:{fnSelectRow:function(t){var e=t.row;this.indexName=e.indexCode,this.$emit("fnSelectRow",e)}}},L=_,I=(0,u.Z)(L,C,Y,!1,null,"356985cb",null),$=I.exports,R=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{width:"100%"}},[a("div",{staticStyle:{"border-bottom":"1px solid rgba(153, 153, 153, 0.14)"}},[a("ta-title",{attrs:{title:t.title}},[a("ta-button",{staticStyle:{"margin-left":"24px",float:"right"},attrs:{type:"primary",icon:"download"},on:{click:t.fnExport}},[t._v("导出")]),a("ta-radio-group",{staticStyle:{float:"right"},attrs:{value:t.checked},on:{change:t.handleChange}},[a("ta-radio-button",{attrs:{value:"1"}},[t._v("科室")]),a("ta-radio-button",{attrs:{value:"2"}},[t._v("医生")])],1)],1)],1),a("ta-big-table",{ref:"focusTable",attrs:{border:"","highlight-hover-row":"",width:"100%",height:"390",data:t.dataList,border:"inner"},on:{"cell-click":t.cellClickEvent}},[a("ta-big-table-column",{attrs:{type:"seq",align:"center",title:"排名",width:"50"}}),a("ta-big-table-column",{attrs:{field:"indexname",title:"指标项目",width:"380"}}),a("ta-big-table-column",{attrs:{field:"objname",title:"科室",width:"150"}}),a("ta-big-table-column",{attrs:{field:"doctorName",title:"医生",width:"160",visible:!1}}),a("ta-big-table-column",{attrs:{sortablee:"",field:"indexvalue",title:"指标值",sortable:"",width:"140",align:"right"},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.row;e.column;return[a("div",{staticStyle:{position:"relative"}},[i.selectedvalue>i.indexvalue&&i.indexvalue>=i.warnValue?a("div",{staticStyle:{color:"#F59A23"}},[t._v(" "+t._s(t.formatMoney(i.indexvalue))+" ")]):i.indexvalue>=i.selectedvalue?a("div",{staticStyle:{color:"#D9001B"}},[t._v(" "+t._s(t.formatMoney(i.indexvalue))+" ")]):a("div",[t._v(" "+t._s(t.formatMoney(i.indexvalue))+" ")])])]}}])}),a("ta-big-table-column",{attrs:{field:"selectedvalue",title:"标准值",width:"150",align:"right"}}),a("ta-big-table-column",{attrs:{field:"aae140",title:"险种类型",width:"170","collection-type":"AAE140"}}),a("ta-big-table-column",{attrs:{field:"warnvalue",title:"预警值",width:"150",align:"right"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;e.column;return[t._v(" "+t._s(t.formatMoney(a.warnvalue))+" ")]}}])}),a("template",{slot:"bottomBar"},[a("ta-pagination",{ref:"gridPager",staticStyle:{"text-align":"right"},attrs:{"data-source":t.dataList,defaultPageSize:6,params:t.returnParams,url:t.pageUrl,simple:""},on:{"update:dataSource":function(e){t.dataList=e},"update:data-source":function(e){t.dataList=e}}})],1)],2)],1)},M=[],N=a(66347),W=a(88412),A={name:"focusTable",components:{TaTitle:W.Z},props:{paramData:Object,title_barRank:String,type:String,url:String},data:function(){return{pageUrl:"indexAnalysis/queryIndexRanking",title:"",deptcode:"",paramsData:{},checked:"1",dataList:[]}},mounted:function(){this.title=this.title_barRank},methods:{moment:s(),fnExport:function(){var t,e=this,a=[],i=this.$refs.focusTable.getColumns(),l=(0,N.Z)(i);try{for(l.s();!(t=l.n()).done;){var n=t.value;"排名"!==n.title&&a.push({header:n.title,key:n.property,width:20})}}catch(r){l.e(r)}finally{l.f()}this.Base.submit(null,{url:"indexAnalysis/exportIndexRanking",data:this.returnParams(),autoValid:!1},{successCallback:function(t){var i={fileName:"指标排名结果表",sheets:[{name:"worksheet1",column:{complex:!1,columns:a},rows:t.data.result,codeList:[{codeType:"AAE140",columnKey:"aae140"}]}]};e.Base.generateExcel(i)},failCallback:function(t){e.$message.error("指标排名数据加载失败")}})},cellClickEvent:function(t){var e=t.row;this.checked="2",this.$refs.focusTable.showColumn(this.$refs.focusTable.getColumnByField("doctorName")),this.$refs.focusTable.hideColumn(this.$refs.focusTable.getColumnByField("objname")),this.deptcode=e.objcode,this.fnRank()},handleChange:function(t){if("2"==t.target.value){if(""==this.deptcode||null==this.deptcode)return void this.$message.info("请选择科室！");this.$refs.focusTable.showColumn(this.$refs.focusTable.getColumnByField("doctorName")),this.$refs.focusTable.hideColumn(this.$refs.focusTable.getColumnByField("objname"))}else this.deptcode="",this.$refs.focusTable.hideColumn(this.$refs.focusTable.getColumnByField("doctorName")),this.$refs.focusTable.showColumn(this.$refs.focusTable.getColumnByField("objname"));this.checked=t.target.value,this.fnRank()},returnParams:function(){var t=[];t.push(this.paramData.startDate),t.push(this.paramData.endDate);var e={condition:{XZ:this.paramData.insuType,ZBXM:this.paramData.indexName,KS:""!=this.deptcode?this.deptcode:null},showDimensionValue:{RQ:t}};return e},formatMoney:function(t){if(null===t)return"--";if(0===t)return t;var e=parseFloat(t).toFixed(2);return e},fnRank:function(){this.$refs.gridPager.loadData((function(t){}))}}},F=A,Z=(0,u.Z)(F,R,M,!1,null,"610f8c72",null),V=Z.exports,E=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("div",[i("div",{staticStyle:{"border-bottom":"1px solid rgba(153, 153, 153, 0.14)"}},[i("ta-title",{attrs:{title:e.title_barRank}},[i("ta-form",{staticStyle:{float:"right"},attrs:{layout:"inline","auto-form-create":function(e){t.form=e}}},[i("ta-form-item",{staticStyle:{"margin-right":"-70px"},attrs:{label:"对比年份","field-decorator-id":"yearPicker","init-value":e.initValue,require:{message:"请选择！!"}}},[i("ta-year-picker",{staticStyle:{width:"50%"},attrs:{"disabled-date":e.disabledYear,"allow-clear":!1},on:{change:e.onChange}})],1),i("ta-form-item",[e._v(" & ")]),i("ta-form-item",{staticStyle:{width:"100px"},attrs:{"field-decorator-id":"yearPicker2","init-value":e.initValue2,require:{message:"请选择！!"}}},[i("ta-year-picker",{staticStyle:{width:"100%"},attrs:{disabled:!0}})],1)],1)],1)],1),i("div",{staticStyle:{height:"330px"},attrs:{id:"line-main01"}})])},B=[],O=a(1708),q={name:"lineNum",components:{TaTitle:W.Z},props:{title_barRank:String,url:String,paramData:Object},data:function(){return{initValue:TaUtils.getMoment((new Date).toISOString().slice(0,10),"YYYY-MM-DD"),initValue2:TaUtils.getMoment(new Date((new Date).setFullYear((new Date).getFullYear()-1)).getFullYear(),"YYYY-MM-DD"),option:{toolbox:{show:!1,feature:{dataZoom:{yAxisIndex:"none"},restore:{},saveAsImage:{}}},tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},grid:{left:"2%",right:"2%",bottom:"1%",top:"15%",containLabel:!0},legend:{data:["2021年","2022年","同比增长率"],left:"45%",top:"5%",textStyle:{color:"#666666"},itemWidth:15,itemHeight:10,itemGap:25},xAxis:{type:"category",data:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],axisLine:{lineStyle:{color:"black"}},axisLabel:{textStyle:{color:"black"}}},yAxis:[{type:"value",name:"",nameTextStyle:{padding:[0,0,0,230]},axisLine:{show:!0,lineStyle:{color:"black"}},splitLine:{show:!0},axisLabel:{textStyle:{color:"black"}}},{type:"value",name:"百分比",nameTextStyle:{color:"black"},position:"right",axisLine:{show:!0,lineStyle:{color:"black"}},splitLine:{show:!0},axisLabel:{show:!0,formatter:"{value} %",textStyle:{color:"black"}}}],series:[{name:"2021年",type:"bar",yAxisIndex:0,barWidth:"30",itemStyle:{normal:{color:"#6394f9"}},data:[]},{name:"2022年",type:"bar",yAxisIndex:0,barWidth:"30",itemStyle:{normal:{color:"#657797"}},data:[]},{name:"同比增长率",type:"line",yAxisIndex:1,smooth:!1,symbol:"circle",symbolSize:8,itemStyle:{normal:{color:"#59d8a6",borderColor:"#59d8a6",borderWidth:3}},lineStyle:{color:"#59d8a6",width:3},data:[]}]},bfparams:{},chartOpiton:{xAxis:{data:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月"]},series:[{name:"2021年",data:[7,6.9,5.7,8.5,11.5,15.2,17,16.3,14.2,10.3]},{name:"2022年",data:[7,6.9,9.5,14.4,18.5,21.5,25.2,26.5,23.3,18.3]},{name:"同比增长率",data:[-1,-.8,-.5,-1.1,-2,-.7,-.8,-1.2,-.3,-1]}]}}},mounted:function(){this.fnRemindNum(null)},methods:{disabledYear:function(t){return t&&t>s()().startOf("year")},onChange:function(t){var e=this;this.initValue2=TaUtils.getMoment(new Date(t.format("YYYY-MM-DD")).getFullYear()-1,"YYYY-MM-DD"),this.$nextTick((function(){e.fnRemindNum(e.bfparams)}))},fnRemindNum:function(t){var e=this;if(null!=t&&void 0!=t.insuType){var a=[],i=this.form.getFieldsValue();a.push(i.yearPicker2.format("YYYY")),a.push(i.yearPicker.format("YYYY")),this.bfparams=t;this.myChart=O.init(document.getElementById("line-main01")),this.myChart.setOption(this.option);var l={url:this.url,data:{condition:{XZ:t.insuType,ZBXM:t.indexName},indexTimeRange:{CONYEAR:a}},autoValid:!0},n={successCallback:function(a){var i={legend:{data:a.data.result.map((function(t){return t.name}))},yAxis:[{name:t.indexCode}],series:a.data.result};e.myChart.setOption(i)},failCallback:function(t){e.$message.error("查询失败")}};this.Base.submit(null,l,n)}}}},P=q,j=(0,u.Z)(P,E,B,!1,null,"151173f2",null),Q=j.exports,U={name:"indexAnalysis",components:{SearchTerm:m,overStandardIndexTable:y,overValueIndexTable:k,excellentIndexTable:$,indexRankTable:V,indexTrendLineChart:Q},data:function(){return{rangeValue:[TaUtils.getMoment(new Date((new Date).getTime()).toISOString().slice(0,6)+"01-01","YYYY-MM-DD"),TaUtils.getMoment((new Date).toISOString().slice(0,10),"YYYY-MM-DD")],indexName:"",typeCode:"",indexTableUrl:"",indexDataList:{table1:[],table2:[],table3:[]},paramData:{},tableWidth1:0,tableWidth2:0,tableWidth3:0}},created:function(){},mounted:function(){},methods:{fnSelectRow:function(t){this.indexName=t.indexCode,this.typeCode=t.typeCode},fnInitIndexTable:function(t){var e=this,a=[];a.push(this.paramData.startDate),a.push(this.paramData.endDate),this.Base.submit(null,{url:"indexAnalysis/queryAllIndex",data:{showDimensionValue:{RQ:a}}},{successCallback:function(t){e.indexDataList=t.data.result}}),this.tableWidth1=50*this.indexDataList.table1.length,this.tableWidth2=50*this.indexDataList.table2.length,this.tableWidth3=50*this.indexDataList.table3.length,this.tableWidth1+this.tableWidth2+this.tableWidth3>650&&(this.tableWidth3=650-this.tableWidth1-this.tableWidth2)},fnQuery:function(t){var e=this;this.paramData=t,this.$nextTick((function(){e.fnInitIndexTable(t),e.$refs.indexRankTable.fnRank(),e.$refs.indexTrendLineChart.fnRemindNum(t)}))}}},z=U,X=(0,u.Z)(z,i,l,!1,null,"407b87da",null),H=X.exports},36766:function(t,e,a){var i=a(66586);e["Z"]=i.Z},26263:function(t,e,a){a.d(e,{s:function(){return i},x:function(){return l}});var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},l=[]},66586:function(t,e){var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};e["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}}}]);
(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[8e3],{88412:function(e,t,a){"use strict";var i=a(26263),s=a(36766),l=a(1001),r=(0,l.Z)(s.Z,i.s,i.x,!1,null,"5e7ef0ae",null);t["Z"]=r.exports},26663:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return g}});var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"fit"},[a("ta-border-layout",{attrs:{"layout-type":"fixTop"},scopedSlots:e._u([{key:"header",fn:function(){return[a("ta-form",{staticStyle:{"margin-top":"20px"},attrs:{autoFormCreate:e.autoFormCreate,col:3,formLayout:!0}},[a("ta-form-item",{attrs:{require:!1,showInfo:!0,"field-decorator-id":"diseaseInfo",label:"病种信息","label-col":{span:7},span:5,"wrapper-col":{span:17}}},[a("ta-input",{attrs:{placeholder:"请输入病种名称或编码"}})],1),a("ta-form-item",{attrs:{require:!1,showInfo:!0,"field-decorator-id":"hospitalType",label:"结算医院类别","label-col":{span:7},span:5,"wrapper-col":{span:17}}},[a("ta-input",{attrs:{placeholder:"请输入结算医院类别"}})],1),a("ta-form-item",{staticStyle:{"text-align":"right"},attrs:{require:!1,showInfo:!0,label:" ","label-col":{span:6},span:14,"wrapper-col":{span:18}}},[a("ta-button",{staticStyle:{"margin-right":"10px"},attrs:{type:"primary"},on:{click:e.queryTableData}},[e._v("查询 ")]),a("ta-button",{staticStyle:{"margin-right":"10px"},on:{click:e.fnReset}},[e._v("重置 ")]),a("ta-button",{staticStyle:{"margin-right":"10px"},on:{click:e.downloadTemplate}},[e._v("下载模板 ")]),a("ta-upload",{attrs:{name:"file",multiple:!0,headers:e.headers,"file-list":e.fileList,"before-upload":e.beforeUpload,"show-upload-list":!1},on:{change:e.handleChange}},[a("ta-button",{staticStyle:{"margin-right":"10px"}},[e._v("导入 ")])],1)],1)],1)]},proxy:!0}])},[a("ta-card",{staticClass:"fit"},[a("ta-title",{attrs:{title:"结果查询"}}),a("ta-big-table",{ref:"infoTableRef",attrs:{data:e.tableData,height:"95%","export-config":{},"import-config":{},border:"","highlight-hover-row":"","highlight-current-row":"","auto-resize":"","show-overflow":"",resizable:"","header-align":"center",size:"large"}},[a("ta-big-table-column",{attrs:{width:50,title:"序号",type:"seq"}}),a("ta-big-table-column",{attrs:{align:"center",field:"diseaseCode",title:"病种编码","min-width":"80px"}}),a("ta-big-table-column",{attrs:{align:"center",field:"diseaseName",title:"病种名称","min-width":"80px"}}),a("ta-big-table-column",{attrs:{align:"center",field:"hospitalType",title:"结算医院类别","min-width":"120px"}}),a("ta-big-table-column",{attrs:{field:"packageFee",formatter:"formatAmount",title:"包干结算费用标准","min-width":"160px"}}),a("ta-big-table-colgroup",{attrs:{title:"职工医保"}},[a("ta-big-table-column",{attrs:{formatter:"formatAmount",field:"employeeFundPay",title:"统筹基金支付(元)","min-width":"160px"}}),a("ta-big-table-column",{attrs:{formatter:"formatAmount",field:"employeePersonalPay",title:"个人自付(元)","min-width":"160px"}})],1),a("ta-big-table-colgroup",{attrs:{title:"居民医保"}},[a("ta-big-table-column",{attrs:{formatter:"formatAmount",field:"residentFundPay",title:"统筹基金支付(元)","min-width":"160px"}}),a("ta-big-table-column",{attrs:{formatter:"formatAmount",field:"residentPersonalPay",title:"个人自付(元)","min-width":"160px"}})],1),a("ta-big-table-column",{attrs:{align:"center",field:"remark",title:"备注","min-width":"60px"}}),a("ta-big-table-column",{attrs:{align:"center",field:"createTime",title:"更新时间","min-width":"80px"}}),a("template",{slot:"bottomBar"},[a("ta-pagination",{ref:"gridPager",staticStyle:{"text-align":"right","margin-right":"120px"},attrs:{dataSource:e.tableData,defaultPageSize:200,pageSizeOptions:["30","50","100","200","500"],params:e.getParam,url:"singleDiseaseCalc/querySingleDiseaseListInfoPage"},on:{"update:dataSource":function(t){e.tableData=t},"update:data-source":function(t){e.tableData=t}}}),a("ta-button",{staticStyle:{position:"absolute",right:"20px",bottom:"12px"},attrs:{type:"primary",icon:"upload"},on:{click:e.exportExcel}},[e._v("导出 ")])],1)],2)],1)],1)],1)},s=[],l=a(66347),r=a(95082),n=a(88412),o=a(22722),c=a(55115);c.w3.prototype.Base=Object.assign(c.w3.prototype.Base,(0,r.Z)({},o.Z));var u={name:"singleDiseaseCalc",components:{TaTitle:n.Z},data:function(){return{tableData:[],validRules:{diseaseCode:[{required:!0,message:"必须填写病种编码!"}],diseaseName:[{required:!0,message:"必须填写病种名称!"}],itemCode:[{required:!0,message:"必须填写医保项目编码!"}],itemName:[{required:!0,message:"必须填写医保项目名称!"}]},isAdd:!0,fileList:[],headers:{authorization:"authorization-text"},drawerVisible:!1,selectedData:{}}},mounted:function(){this.queryTableData()},methods:{beforeUpload:function(e){if(e.size/1024>51200)return this.$message.warning("请选择不超过50M的文件"),!1;var t=e.name.split("."),a=t[t.length-1];return-1===["xls","xlsx","csv"].indexOf(a)?(this.$message.warning("只能上传excel文件"),!1):(this.fileList=[e],this.baseInfoForm.setFieldsValue({fileName:e.name}),!1)},editIsActive:function(e){return this.$refs.infoTableRef.isActiveByRow(e)},editActive:function(e){this.$refs.infoTableRef.setActiveRow(e)},editCancel:function(e){var t=this;this.$refs.infoTableRef.revertData(e).then((function(){t.$refs.infoTableRef.clearActived()}))},fnDelete:function(e){var t=this;this.Base.submit(null,{url:"diseaseManage/deleteById",data:e,autoValid:!1},{successCallback:function(e){t.$message.success("删除成功！"),t.queryTableData()},failCallback:function(e){t.$message.error("删除失败！")}})},editSave:function(e,t){var a=this;this.$refs.infoTableRef.validate(e).then((function(){a.$refs.infoTableRef.isUpdateByRow(e)?(a.Base.submit(null,{url:"diseaseManage/editDiseaseItemInfo",data:e,autoValid:!1},{successCallback:function(e){a.$message.success("保存成功！"),a.queryTableData()},failCallback:function(e){a.$message.error("保存失败！")}}),a.$refs.infoTableRef.clearActived()):message.info("数据未有改动")})).catch((function(e){e&&message.error("校验不通过！")}))},handleChange:function(e){var t=this,a=this.baseInfoForm.getFieldsValue();a.file=e.file,this.Base.submit(null,{url:"/singleDiseaseCalc/importExcel",data:a,autoQs:!1,isFormData:!0},{successCallback:function(e){t.baseInfoForm.resetFields(),t.$message.success("导入数据成功"),t.queryTableData()},failCallback:function(e){}})},queryTableData:function(){this.$refs.gridPager.loadData()},fnReset:function(){this.baseInfoForm.resetFields()},autoFormCreate:function(e){this.baseInfoForm=e},getParam:function(){return this.baseInfoForm.getFieldsValue()},downloadTemplate:function(){var e,t=[],a=this.$refs.infoTableRef.getColumns(),i=(0,l.Z)(a);try{for(i.s();!(e=i.n()).done;){var s=e.value;"序号"!==s.title&&"医保限制条件"!==s.title&&"更新时间"!==s.title&&"操作"!==s.title&&t.push({key:s.title,width:20})}}catch(n){i.e(n)}finally{i.f()}var r={fileName:"单病种目录导入模板表",sheets:[{name:"worksheet1",column:{complex:!0,rowValues:[["病种编码","病种名称","结算医院类别","包干结算费用标准","职工医保",void 0,"居民医保",void 0,"备注"],["病种编码","病种名称","结算医院类别","包干结算费用标准","统筹基金支付(元)","个人自付(元)","统筹基金支付(元)","个人自付(元)","备注"]],rowHeight:[20,20],columns:t},mergeCell:function(e){e.mergeCells("A1:A2"),e.mergeCells("B1:B2"),e.mergeCells("C1:C2"),e.mergeCells("D1:D2"),e.mergeCells("E1:F1"),e.mergeCells("G1:H1"),e.mergeCells("I1:I2")},rows:[]}]};this.Base.generateExcel(r)},exportExcel:function(){var e,t=this,a=[],i=this.$refs.infoTableRef.getColumns(),s=(0,l.Z)(i);try{for(s.s();!(e=s.n()).done;){var r=e.value;"序号"!==r.title&&"操作"!==r.title&&a.push({key:r.property,width:20})}}catch(n){s.e(n)}finally{s.f()}this.Base.submit(null,{url:"singleDiseaseCalc/querySingleDiseaseListInfoList",data:this.getParam(),autoValid:!1},{successCallback:function(e){var i={fileName:"单病种目录数据表",sheets:[{column:{complex:!0,rowValues:[["病种编码","病种名称","结算医院类别","包干结算费用标准","职工医保",void 0,"居民医保",void 0,"备注","更新时间"],["病种编码","病种名称","结算医院类别","包干结算费用标准","统筹基金支付(元)","个人自付(元)","统筹基金支付(元)","个人自付(元)","备注","更新时间"]],rowHeight:[20,20],columns:a},mergeCell:function(e){e.mergeCells("A1:A2"),e.mergeCells("B1:B2"),e.mergeCells("C1:C2"),e.mergeCells("D1:D2"),e.mergeCells("E1:F1"),e.mergeCells("G1:H1"),e.mergeCells("I1:I2"),e.mergeCells("J1:J2")},rows:e.data.singleDiseaseListList}]};t.Base.generateExcel(i)},failCallback:function(e){t.$message.error("单病种目录数据表导出失败")}})},activeMethod:function(e){var t=e.row,a=(e.rowIndex,e.column,e.columnIndex);return 4!==a||4===a&&"2"===t.columnType},saveConfig:function(){var e=this,t=this.form_edit.getFieldsValue();this.isAdd?Base.submit(this.form_edit,{url:"addDemoModel",method:"post",data:t,autoValid:!0}).then((function(t){e.$message.success("新增成功",3),e.queryTableData(),e.drawerVisible=!1})):(t.configId=this.selectedData.configId,Base.submit(this.form_edit,{url:"updateDemoRecord",method:"post",data:t,autoValid:!0}).then((function(t){e.$message.success("修改成功",3),e.queryTableData(),e.drawerVisible=!1})))}}},f=u,m=a(1001),d=(0,m.Z)(f,i,s,!1,null,"4f2aca48",null),g=d.exports},36766:function(e,t,a){"use strict";var i=a(66586);t["Z"]=i.Z},26263:function(e,t,a){"use strict";a.d(t,{s:function(){return i},x:function(){return s}});var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:e.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[e._v(e._s(e.title))]),e._t("default")],2)},s=[]},66586:function(e,t){"use strict";var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};t["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}},55382:function(){},61219:function(){}}]);
(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[5949],{88412:function(t,e,a){"use strict";var i=a(26263),n=a(36766),s=a(1001),r=(0,s.Z)(n.Z,i.s,i.x,!1,null,"5e7ef0ae",null);e["Z"]=r.exports},74809:function(t,e,a){"use strict";a.r(e),a.d(e,{default:function(){return p}});var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"fit"},[a("ta-border-layout",{attrs:{"layout-type":"fixTop"},scopedSlots:t._u([{key:"header",fn:function(){return[a("ta-form",{staticStyle:{"margin-top":"20px"},attrs:{autoFormCreate:t.autoFormCreate,col:3,formLayout:!0}},[a("ta-form-item",{attrs:{"field-decorator-id":"inhospStas",label:"在院状态","label-col":{span:7},span:4,initValue:1,"wrapper-col":{span:17}}},[a("ta-radio-group",{attrs:{options:t.options},on:{change:t.onChange}})],1),a("ta-form-item",{attrs:{required:!0,"init-value":t.rangeValue,"field-decorator-options":{rules:[{required:!0,message:"必须选取日期"}]},"field-decorator-id":"allDate",label:t.onRadioLable,"label-col":{span:7},span:5,"wrapper-col":{span:17}}},[a("ta-range-picker",{attrs:{"allow-one":!0}})],1),a("ta-form-item",{staticStyle:{"text-align":"right"},attrs:{require:!1,showInfo:!0,label:" ","label-col":{span:6},span:14,"wrapper-col":{span:18}}},[a("ta-button",{staticStyle:{"margin-right":"10px"},attrs:{type:"primary"},on:{click:t.queryTableData}},[t._v("查询 ")]),a("ta-button",{staticStyle:{"margin-right":"10px"},on:{click:t.fnReset}},[t._v("重置 ")])],1)],1)]},proxy:!0}])},[a("ta-card",{staticClass:"fit"},[a("ta-title",{attrs:{title:"结果查询"}}),a("ta-big-table",{ref:"infoTableRef",attrs:{data:t.tableData,height:"95%","export-config":{},"import-config":{},border:"","highlight-hover-row":"","highlight-current-row":"","auto-resize":"","show-overflow":"",resizable:"","header-align":"center",size:"large"}},[a("ta-big-table-column",{attrs:{width:50,title:"序号",type:"seq"}}),a("ta-big-table-column",{attrs:{align:"center",field:"diseaseCode",title:"病种编码","min-width":"80px"}}),a("ta-big-table-column",{attrs:{align:"center",field:"diseaseName",title:"病种名称","min-width":"80px"}}),a("ta-big-table-column",{attrs:{align:"center",field:"mdtrtId",title:"住院就诊号","min-width":"120px"}}),a("ta-big-table-column",{attrs:{align:"center",field:"patnName",title:"患者姓名","min-width":"160px"}}),a("ta-big-table-column",{attrs:{align:"center",field:"admDate",title:"入院时间","min-width":"60px"}}),a("ta-big-table-column",{attrs:{align:"center",field:"dscgDate",title:"出院时间","min-width":"80px"}}),a("ta-big-table-column",{attrs:{align:"center",field:"prntAdmDeptName",title:"入院科室","min-width":"80px"}}),a("ta-big-table-column",{attrs:{align:"center",field:"medfeeSumamt",formatter:"formatAmount",title:"已发生费用(元)","min-width":"80px"}}),a("ta-big-table-column",{attrs:{field:"packageFee",title:"包干结算费用标准","min-width":"80px"}}),a("ta-big-table-column",{attrs:{align:"center",field:"warn",title:"超标预警","min-width":"80px"},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.row;return["正常"===i.warn?a("span",{staticStyle:{"font-size":"12px",color:"rgb(255, 255, 255)",background:"#0f990f",border:"2px solid #0f990f","border-radius":"5px"}},[t._v("正常")]):"已超标"===i.warn?a("span",{staticStyle:{"font-size":"12px",color:"rgb(255, 255, 255)",background:"#ff0006",border:"2px solid #ff0006","border-radius":"5px"}},[t._v("已超标")]):a("span",[t._v("—")])]}}])}),a("template",{slot:"bottomBar"},[a("ta-pagination",{ref:"gridPager",staticStyle:{"text-align":"right","margin-right":"120px"},attrs:{dataSource:t.tableData,defaultPageSize:200,pageSizeOptions:["30","50","100","200","500"],params:t.getParam,url:"singleDiseaseCalc/querySingleDiseaseWarnPage"},on:{"update:dataSource":function(e){t.tableData=e},"update:data-source":function(e){t.tableData=e}}}),a("ta-button",{staticStyle:{position:"absolute",right:"20px",bottom:"12px"},attrs:{type:"primary",icon:"upload"},on:{click:t.exportExcel}},[t._v("导出 ")])],1)],2)],1)],1)],1)},n=[],s=a(66347),r=a(95082),l=a(88412),o=a(22722),c=a(55115);c.w3.prototype.Base=Object.assign(c.w3.prototype.Base,(0,r.Z)({},o.Z));var u=[{label:"在院",value:1},{label:"出院",value:0}],f={name:"singleDiseaseWarn",components:{TaTitle:l.Z},data:function(){return{tableData:[],isAdd:!0,onRadioLable:"在院时间",rangeValue:[this.Base.getMoment((new Date).toISOString().slice(0,8)+"-01","YYYY-MM-DD"),this.Base.getMoment((new Date).toISOString().slice(0,10),"YYYY-MM-DD")],options:u,fileList:[],headers:{authorization:"authorization-text"},drawerVisible:!1,selectedData:{}}},mounted:function(){this.queryTableData()},methods:{onChange:function(t){this.onRadioLable=0===t.target.value?"出院时间":"在院时间"},beforeUpload:function(t){if(t.size/1024>51200)return this.$message.warning("请选择不超过50M的文件"),!1;var e=t.name.split("."),a=e[e.length-1];return-1===["xls","xlsx","csv"].indexOf(a)?(this.$message.warning("只能上传excel文件"),!1):(this.fileList=[t],this.baseInfoForm.setFieldsValue({fileName:t.name}),!1)},editIsActive:function(t){return this.$refs.infoTableRef.isActiveByRow(t)},editActive:function(t){this.$refs.infoTableRef.setActiveRow(t)},editCancel:function(t){var e=this;this.$refs.infoTableRef.revertData(t).then((function(){e.$refs.infoTableRef.clearActived()}))},fnDelete:function(t){var e=this;this.Base.submit(null,{url:"diseaseManage/deleteById",data:t,autoValid:!1},{successCallback:function(t){e.$message.success("删除成功！"),e.queryTableData()},failCallback:function(t){e.$message.error("删除失败！")}})},editSave:function(t,e){var a=this;this.$refs.infoTableRef.validate(t).then((function(){a.$refs.infoTableRef.isUpdateByRow(t)?(a.Base.submit(null,{url:"diseaseManage/editDiseaseItemInfo",data:t,autoValid:!1},{successCallback:function(t){a.$message.success("保存成功！"),a.queryTableData()},failCallback:function(t){a.$message.error("保存失败！")}}),a.$refs.infoTableRef.clearActived()):message.info("数据未有改动")})).catch((function(t){t&&message.error("校验不通过！")}))},handleChange:function(t){var e=this,a=this.baseInfoForm.getFieldsValue();a.file=t.file,this.Base.submit(null,{url:"/singleDiseaseCalc/importExcel",data:a,autoQs:!1,isFormData:!0},{successCallback:function(t){e.baseInfoForm.resetFields(),e.$message.success("导入数据成功"),e.queryTableData()},failCallback:function(t){}})},queryTableData:function(){var t=this,e=this.baseInfoForm.getFieldsValue();if(e.allDate){if(!e.allDate[0])return void this.$message.error("请选择完整的时间范围");if(!e.allDate[1])return void this.$message.error("请选择完整的时间范围")}this.baseInfoForm.validateFields((function(e){e||t.$refs.gridPager.loadData()}))},fnReset:function(){this.baseInfoForm.resetFields()},autoFormCreate:function(t){this.baseInfoForm=t},getParam:function(){var t=this.baseInfoForm.getFieldsValue();return t.allDate&&(t.allDate[0]&&(t.startDate=t.allDate[0].format("YYYY-MM-DD")),t.allDate[1]&&(t.endDate=t.allDate[1].format("YYYY-MM-DD"))),t},exportExcel:function(){var t,e=this,a=[],i=this.$refs.infoTableRef.getColumns(),n=(0,s.Z)(i);try{for(n.s();!(t=n.n()).done;){var r=t.value;"序号"!==r.title&&"操作"!==r.title&&a.push({header:r.title,key:r.property,width:20})}}catch(l){n.e(l)}finally{n.f()}this.Base.submit(null,{url:"singleDiseaseCalc/querySingleDiseaseWarn",data:this.getParam(),autoValid:!1},{successCallback:function(t){var i={fileName:"单病种预警数据表",sheets:[{column:{complex:!1,columns:a},rows:t.data.singleDiseaseWarnList}]};e.Base.generateExcel(i)},failCallback:function(t){e.$message.error("单病种预警数据表导出失败")}})},activeMethod:function(t){var e=t.row,a=(t.rowIndex,t.column,t.columnIndex);return 4!==a||4===a&&"2"===e.columnType},saveConfig:function(){var t=this,e=this.form_edit.getFieldsValue();this.isAdd?Base.submit(this.form_edit,{url:"addDemoModel",method:"post",data:e,autoValid:!0}).then((function(e){t.$message.success("新增成功",3),t.queryTableData(),t.drawerVisible=!1})):(e.configId=this.selectedData.configId,Base.submit(this.form_edit,{url:"updateDemoRecord",method:"post",data:e,autoValid:!0}).then((function(e){t.$message.success("修改成功",3),t.queryTableData(),t.drawerVisible=!1})))}}},d=f,m=a(1001),g=(0,m.Z)(d,i,n,!1,null,"57bae992",null),p=g.exports},36766:function(t,e,a){"use strict";var i=a(66586);e["Z"]=i.Z},26263:function(t,e,a){"use strict";a.d(e,{s:function(){return i},x:function(){return n}});var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},n=[]},66586:function(t,e){"use strict";var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};e["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}},55382:function(){},61219:function(){}}]);
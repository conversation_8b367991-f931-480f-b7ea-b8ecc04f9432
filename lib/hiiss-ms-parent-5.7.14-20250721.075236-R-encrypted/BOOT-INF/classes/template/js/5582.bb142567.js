(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[5582],{88412:function(t,e,a){"use strict";var i=a(26263),o=a(36766),s=a(1001),n=(0,s.Z)(o.Z,i.s,i.x,!1,null,"5e7ef0ae",null);e["Z"]=n.exports},19192:function(t,e,a){"use strict";a.r(e),a.d(e,{default:function(){return g}});var i=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("div",{staticClass:"fit"},[i("ta-border-layout",{attrs:{layout:{header:"110px",footer:"0px"},showPadding:!0,"footer-cfg":{showBorder:!1}}},[i("div",{attrs:{slot:"header"},slot:"header"},[i("ta-title",{attrs:{title:"操作"}}),i("ta-form",{staticStyle:{"padding-top":"5px"},attrs:{autoFormCreate:function(e){t.baseInfoForm=e},layout:"horizontal",formLayout:!0}},[i("ta-button",{staticStyle:{"margin-left":"20px",float:"left"},attrs:{type:"primary",icon:"search"},on:{click:e.fnQuery}},[e._v("查询")]),i("ta-form-item",{staticClass:"uploadFormItem",attrs:{label:"选择文件","field-decorator-id":"fileName",require:!0}},[i("ta-input",{staticClass:"uploadInput",attrs:{placeholder:"请选择文件上传",disabled:!0}}),i("ta-upload",{staticClass:"uploadBtn",attrs:{"file-list":e.fileList,"before-upload":e.beforeUpload,"show-upload-list":!1}},[i("ta-button",{attrs:{type:"primary"}},[e._v(" 浏览 ")])],1)],1),i("ta-button",{staticStyle:{"margin-left":"20px",float:"left"},attrs:{type:"primary",disabled:e.fileUploadDisabled,icon:"upload"},on:{click:e.handleOk}},[e._v("导入 ")]),i("ta-button",{staticStyle:{"margin-left":"20px",float:"left"},attrs:{type:"primary",icon:"redo"},on:{click:e.reflash}},[e._v("刷新缓存 ")])],1)],1),i("div",{staticClass:"fit",staticStyle:{height:"95%"}},[i("ta-title",{staticStyle:{width:"50%"},attrs:{title:"查询结果"}}),i("ta-big-table",{ref:"Table",attrs:{height:"100%","auto-resize":"",resizable:"","highlight-hover-row":"",border:"",size:"mini",data:e.userList},scopedSlots:e._u([{key:"bottomBar",fn:function(){return[i("ta-pagination",{ref:"gridPager",staticStyle:{"text-align":"right","margin-right":"100px"},attrs:{"data-source":e.userList,defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"],params:e.infoPageParams,url:"/codeConver/queryNodeTranscodingInfoPage"},on:{"update:dataSource":function(t){e.userList=t},"update:data-source":function(t){e.userList=t}}})]},proxy:!0}])},[i("ta-big-table-column",{attrs:{type:"seq",align:"center",title:"序号",width:"50"}}),i("ta-big-table-column",{attrs:{field:"nodeSceneName",align:"center",title:"节点类型",width:"auto"}}),i("ta-big-table-column",{attrs:{field:"hospNodeCode",align:"center",title:"院内码编码",width:"auto"}}),i("ta-big-table-column",{attrs:{field:"hospNodeName",align:"center",title:"院内码名称",width:"auto"}}),i("ta-big-table-column",{attrs:{field:"yhNodeCode",align:"center",title:"银海码编码",width:"auto"}}),i("ta-big-table-column",{attrs:{field:"yhNodeName",align:"center",title:"银海码名称",width:"auto"}})],1),i("ta-button",{staticStyle:{position:"absolute",right:"6px",bottom:"21px"},attrs:{type:"primary",icon:"upload"},on:{click:e.exportExcel}},[e._v("导出")])],1)])],1)},o=[],s=a(66347),n=a(95082),r=a(88412),l=a(22722),c=a(36797),u=a.n(c),f=a(55115);f.w3.prototype.Base=Object.assign(f.w3.prototype.Base,(0,n.Z)({},l.Z));var d={name:"diagnosisMonitor",components:{TaTitle:r.Z},data:function(){return{userList:[],uploadVisible:!1,fileUploadDisabled:!0,fileList:[]}},mounted:function(){this.fnQuery()},methods:{moment:u(),reflash:function(){var t=this;this.Base.submit(null,{url:"/codeConver/reflashCache",data:{},autoValid:!1},{successCallback:function(e){"1"===e.data.code?t.$message.success("刷新缓存成功"):t.$message.error("刷新缓存失败")},failCallback:function(e){t.$message.error("刷新缓存失败")}})},exportExcel:function(){var t,e=this,a=[],i=this.$refs.Table.getColumns(),o=(0,s.Z)(i);try{for(o.s();!(t=o.n()).done;){var n=t.value;"序号"!==n.title&&a.push({header:n.title,key:n.property,width:20})}}catch(r){o.e(r)}finally{o.f()}this.Base.submit(null,{url:"codeConver/exportNodeTranscodingInfoPage",data:{},autoValid:!1},{successCallback:function(t){var i={fileName:"银海知识库标准码转换表",sheets:[{name:"worksheet1",column:{complex:!1,columns:a},rows:t.data.result}]};e.Base.generateExcel(i)},failCallback:function(t){e.$message.error("指标排名数据加载失败")}})},handleOk:function(){var t=this,e=this.baseInfoForm.getFieldsValue();e.file=this.fileList[0],this.Base.submit(null,{url:"/codeConver/importExcel",data:e,autoQs:!1,isFormData:!0},{successCallback:function(e){t.baseInfoForm.resetFields(),t.fileUploadDisabled=!0,t.fnQuery(),t.$message.success("导入数据成功")},failCallback:function(e){t.$message.error("导入数据失败")}})},infoPageParams:function(){var t=this.baseInfoForm.getFieldsValue();return t},beforeUpload:function(t){if(t.size/1024>51200)return this.$message.warning("请选择不超过50M的文件"),!1;var e=t.name.split("."),a=e[e.length-1];return-1===["xls","xlsx","csv"].indexOf(a)?(this.$message.warning("只能上传excel文件"),!1):(this.fileList=[t],this.baseInfoForm.setFieldsValue({fileName:t.name}),this.fileUploadDisabled=!1,!1)},fnQuery:function(){var t=this;this.$nextTick((function(){t.$refs.gridPager.loadData((function(t){}))}))},fnCustomHeaderCell:function(t){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px",fontWeight:"bold"}}},filterOption:function(t,e){return e.componentOptions.children[0].text.indexOf(t)>=0}}},p=d,h=a(1001),m=(0,h.Z)(p,i,o,!1,null,"da70910a",null),g=m.exports},36766:function(t,e,a){"use strict";var i=a(66586);e["Z"]=i.Z},26263:function(t,e,a){"use strict";a.d(e,{s:function(){return i},x:function(){return o}});var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},o=[]},66586:function(t,e){"use strict";var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};e["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}},55382:function(){},61219:function(){}}]);
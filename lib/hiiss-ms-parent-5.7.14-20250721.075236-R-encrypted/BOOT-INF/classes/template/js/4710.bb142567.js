"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[4710],{44710:function(t,e,i){i.r(e),i.d(e,{default:function(){return c}});var a=function(){var t=this,e=this,i=e.$createElement,a=e._self._c||i;return a("div",{staticClass:"fit addStatisticsAllocation-container"},[a("ta-border-layout",{attrs:{"show-border":!0,"header-cfg":{showBorder:!1},"footer-cfg":{showBorder:!1},layout:{header:"160px",footer:"60px"}}},[a("div",{staticClass:"addStatisticsAllocation_header",attrs:{slot:"header"},slot:"header"},[a("ta-form",{attrs:{layout:"horizontal",formLayout:!0,"label-width":"100px","auto-form-create":function(e){return t.form=e}}},[a("ta-form-item",{attrs:{label:"功能",fieldDecoratorId:"resourceId",span:6,"label-width":"80px",require:{message:"请选择功能!"}}},[a("ta-tree-select",{attrs:{disabled:e.formDisabled,showSearch:"",filterTreeNode:e.fnFilterTreeNode,treeData:e.featureOption,dropdownStyle:{maxHeight:"400px",overflow:"auto"},allowClear:""}})],1),a("ta-form-item",{attrs:{label:"排列形式",fieldDecoratorId:"direction",span:6,require:{message:"请选择排列形式!"}}},[a("ta-select",{attrs:{disabled:e.formDisabled,"collection-type":"ARRANGEMENT"}})],1),a("ta-form-item",{attrs:{label:"条件",fieldDecoratorId:"conditionList",span:6,"label-width":"80px",require:{message:"请选择条件!"}}},[a("ta-select",{attrs:{disabled:e.formDisabled,allowClear:"",mode:"multiple",maxTagCount:1,options:e.conditionOption},on:{change:e.conditionChange}})],1),a("ta-form-item",{attrs:{label:"维度",fieldDecoratorId:"dimensionList",span:6,require:{message:"请选择维度!"}}},[a("ta-select",{attrs:{disabled:e.formDisabled,allowClear:"",mode:"multiple",maxTagCount:1,options:e.dimensionOption},on:{change:e.dimensionChange}})],1)],1),a("div",{staticClass:"header_box"},[a("div",{staticClass:"box_left"},[a("div",[e._v(" 已选条件： "),e._l(e.conditionBox,(function(t){return a("ta-tag",{key:t.value,attrs:{color:"blue",closable:!e.formDisabled},on:{close:function(i){return e.conditionDefault(t)}}},[e._v(e._s(t.label))])}))],2),a("div",{staticStyle:{"margin-top":"4px"}},[e._v(" 已选维度： "),e._l(e.dimensionBox,(function(t){return a("ta-tag",{key:t.value,attrs:{color:"blue",closable:!e.formDisabled},on:{close:function(i){return e.dimensionDefault(t)}}},[e._v(e._s(t.label))])}))],2)]),a("div",{staticClass:"box_right"},[a("ta-button",{attrs:{type:"primary"},on:{click:e.redactForm}},[e._v(" 编辑 ")]),a("ta-button",{attrs:{type:"primary"},on:{click:e.saveForm}},[e._v(" 保存 ")])],1)])],1),a("div",{staticClass:"fit addStatisticsAllocation_center"},[a("ta-border-layout",{attrs:{centerCfg:{showBar:!0,toolBarStyle:{height:"50px",lineHeight:"50px"}},showBorder:!1}},[a("div",{attrs:{slot:"centerExtraContent"},slot:"centerExtraContent"},[a("div",{staticStyle:{float:"right"}},[a("ta-button",{attrs:{type:"primary"},on:{click:e.newTableData}},[e._v(" 新增 ")])],1)]),a("div",{staticClass:"fit"},[a("ta-big-table",{ref:"xTable",attrs:{height:"auto",border:"",align:"center","tree-config":{children:"children"},data:e.tableData},on:{"checkbox-all":e.selectAllEvent,"checkbox-change":e.selectChangeEvent}},[a("ta-big-table-column",{attrs:{type:"checkbox",width:"60"}}),a("ta-big-table-column",{attrs:{field:"indexId",title:"指标id","tree-node":""}}),a("ta-big-table-column",{attrs:{field:"index",title:"指标名称"}}),a("ta-big-table-column",{attrs:{field:"indexLabel",title:"自定义名称"}}),a("ta-big-table-column",{attrs:{field:"skipName",title:"跳转功能名称"}}),a("ta-big-table-column",{attrs:{title:"操作",width:"300"},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.row;return[a("ta-button",{staticStyle:{border:"none"},attrs:{size:"small",type:"link"},on:{click:function(t){return e.editTableData(i)}}},[e._v("编辑")]),i.add?a("ta-button",{staticStyle:{border:"none"},attrs:{size:"small",type:"link"},on:{click:function(t){return e.addJunior(i)}}},[e._v("添加下级")]):e._e(),a("ta-popconfirm",{attrs:{title:"确定要删除吗?",okText:"确定",cancelText:"取消"},on:{confirm:function(t){return e.deleteTableData(i)}}},[a("ta-button",{staticStyle:{border:"none"},attrs:{size:"small",type:"link"}},[e._v("删除")])],1),a("ta-button",{staticStyle:{border:"none"},attrs:{size:"small",type:"link"},on:{click:function(t){return e.moveTableData(i,"up")}}},[e._v("上移")]),a("ta-button",{staticStyle:{border:"none"},attrs:{size:"small",type:"link"},on:{click:function(t){return e.moveTableData(i,"down")}}},[e._v("下移")])]}}])})],1)],1)])],1),a("div",{staticClass:"addStatisticsAllocation_footer",attrs:{slot:"footer"},slot:"footer"},[a("ta-button",{on:{click:e.cancelTableData}},[e._v(" 取消 ")]),a("ta-button",{attrs:{type:"primary"},on:{click:e.addTableData}},[e._v(" 保存 ")])],1)]),a("ta-modal",{attrs:{title:"保存"},on:{ok:e.handleOk,cancel:e.handleCancel},model:{value:e.visible,callback:function(t){e.visible=t},expression:"visible"}},[a("p",[e._v("保存成功!是否需要设置默认图表？")])]),a("ta-modal",{staticStyle:{width:"750px"},attrs:{title:e.titleName},on:{ok:e.editHandleOk,cancel:e.editCancel},model:{value:e.editVisible,callback:function(t){e.editVisible=t},expression:"editVisible"}},[a("ta-form",{attrs:{layout:"horizontal",formLayout:!0,"label-width":"120px",autoFormCreate:function(e){t.editForm=e}}},[a("ta-form-item",{attrs:{label:"指标",span:12,fieldDecoratorId:"indicators",require:{message:"请选择指标id!"}}},[a("ta-select",{attrs:{showValue:"",showSearch:"",options:e.idOption,virtual:"",labelInValue:""},on:{change:e.indicatorsChange}})],1),a("ta-form-item",{attrs:{label:"名称",span:12,"label-width":"100px",fieldDecoratorId:"indexLabel",require:{message:"请输入指标名称!"}}},[a("ta-input")],1),a("ta-form-item",{attrs:{label:"跳转功能名称",span:12,fieldDecoratorId:"jumpResourceId"}},[a("ta-tree-select",{attrs:{showSearch:"",filterTreeNode:e.fnFilterTreeNode,treeData:e.skipOption,dropdownStyle:{maxHeight:"400px",overflow:"auto"},allowClear:""},on:{change:e.getFeatureName}})],1)],1)],1)],1)},n=[],s=i(95082),o={data:function(){return{resourceId:"6db7691be0714510a65e13ed7e417f98",pageType:"新增",pageData:[],pageRender:!1,choseResourceId:"",tableData:[],visible:!1,editVisible:!1,featureOption:[],conditionOption:[],dimensionOption:[],conditionBox:[],dimensionBox:[],titleName:"",idOption:[],skipOption:[],skipName:"",chosePushId:"",statisticsDefaultChartId:"",formDisabled:!1}},watch:{pageRender:function(t){var e=this;"修改"===this.pageType&&t&&this.$nextTick((function(){e.getPageInfo()}))}},created:function(){var t=this.$route.query;switch(t.type){case"新增":this.pageType="新增";break;case"修改":this.pageType="修改",this.pageData=t.pageData;break;default:break}this.getFeatureOption(),this.getTwoOption()},methods:{getPageInfo:function(){var t=this;this.$nextTick((function(){var e=[],i=[];t.pageData.conditionList.forEach((function(t){e.push(t.value)})),t.pageData.dimensionList.forEach((function(t){i.push(t.value)}));var a={resourceId:t.pageData.resourceId,direction:t.pageData.direction,conditionList:e,dimensionList:i};t.form.setFieldsValue(a),t.pageData.indexTree.forEach((function(e){e.add=!0,e.pushId=t.radomName(36),e.skipName=e.jumpResourceName,delete e.jumpResourceName,e.children?e.children.forEach((function(e){e.add=!1,e.pushId=t.radomName(36),e.skipName=e.jumpResourceName,delete e.jumpResourceName})):e.children=[]})),t.tableData=t.pageData.indexTree,t.saveForm()}))},redactForm:function(){this.formDisabled=!1},saveForm:function(){var t=this;this.form.validateFields((function(e){if(!e){var i=[];t.conditionBox.forEach((function(t){i.push(t.value)})),t.dimensionBox.forEach((function(t){i.push(t.value)}));var a=i.join();t.getIndex(a)}}))},getFeatureOption:function(){var t=this;this.Base.submit(null,{url:"statisticsConfig/queryAllResource",data:{resourceId:this.resourceId}}).then((function(e){t.featureOption=e.data.result,t.skipOption=e.data.result}))},getFeatureName:function(t,e){this.skipName=e[0]},getTwoOption:function(){var t=this;this.Base.submit(null,{url:"statisticsConfig/queryAllDimension"}).then((function(e){t.conditionOption=e.data.result,t.dimensionOption=e.data.result,t.$nextTick((function(){if("修改"===t.pageType){var e=[],i=[];t.pageData.conditionList.forEach((function(i){t.conditionOption.forEach((function(t){t.value==i&&e.push({label:t.label,value:t.value})}))})),t.pageData.dimensionList.forEach((function(e){t.dimensionOption.forEach((function(t){t.value==e&&i.push({label:t.label,value:t.value})}))})),t.pageData.conditionList=e,e.forEach((function(e,i){t.conditionBox.push(e)})),t.pageData.dimensionList=i,i.forEach((function(e,i){t.dimensionBox.push(e)})),t.pageRender=!0}}))}))},conditionChange:function(t){var e=this;this.conditionBox=[],this.conditionOption.forEach((function(i,a){t.forEach((function(t,a){i.value==t&&e.conditionBox.push(i)}))}))},dimensionChange:function(t){var e=this;this.dimensionBox=[],this.dimensionOption.forEach((function(i,a){t.forEach((function(t,a){i.value==t&&e.dimensionBox.push(i)}))}))},fnFilterTreeNode:function(t,e){return-1!==e.title.indexOf(t)},newTableData:function(){this.formDisabled?(this.editVisible=!0,this.titleName="新增"):this.$message.warning("请先保存上方表单")},addTableData:function(){var t=this,e="statisticsConfig/addIndexStatisticsConfig";if(this.formDisabled){var i=this.form.getFieldsValue();this.choseResourceId=i.resourceId;var a=[],n=[];i.conditionList.forEach((function(t){a.push(t+"")})),i.dimensionList.forEach((function(t){n.push(t+"")})),i.conditionList=a,i.dimensionList=n;var s=JSON.stringify(this.tableData);s=JSON.parse(s);var o=[];s.forEach((function(t){t.children?(t.children.length>0&&t.children.forEach((function(t){o.push(t)})),delete t.children,o.push(t)):o.push(t)})),i.indexList=o,"修改"==this.pageType&&(i.statisticsConfigId=this.pageData.statisticsConfigId,e="statisticsConfig/updateIndexStatisticsConfig",i.enableChart=this.pageData.enableChart),this.Base.submit(null,{url:e,data:{jsonStr:JSON.stringify(i)}}).then((function(e){"新增"==t.pageType?t.visible=!0:t.$router.push({path:"/statisticsAllocation",query:{type:"保存"}})}))}else this.$message.warning("请先保存上方表单")},cancelTableData:function(){this.$router.push({path:"/statisticsAllocation",query:{type:"取消"}})},handleOk:function(t){this.visible=!1,this.$router.push({path:"/addStatisticsEchats",query:{type:"新增图表",resourceId:this.choseResourceId}})},handleCancel:function(t){this.$router.push({path:"/statisticsAllocation",query:{type:"保存"}})},editTableData:function(t){var e=this;this.formDisabled?(t.indicators={key:t.indexId,label:t.index},this.titleName="编辑",this.editVisible=!0,this.chosePushId=t.pushId,this.skipName=t.skipName,this.$nextTick((function(){e.editForm.setFieldsValue(t)}))):this.$message.warning("请先保存上方表单")},addJunior:function(t){this.formDisabled?(this.editVisible=!0,this.titleName="添加下级",this.chosePushId=t.pushId):this.$message.warning("请先保存上方表单")},deleteTableData:function(t){var e=this;this.tableData.forEach((function(i,a){i.pushId===t.pushId?0==i.children.length?e.tableData.splice(a,1):e.$message.warning("有下级时不能删除！"):i.children.forEach((function(i,n){i.pushId===t.pushId&&e.tableData[a].children.splice(n,1)}))}))},moveTableData:function(t,e){var i=this,a=this.tableData.length-1;if("up"==e)this.tableData.forEach((function(e,a){if(t.add){if(e.statisticsResourceIndexId==t.statisticsResourceIndexId)if(0==a)i.$message.warning("已经在最上面了！");else{var n=i.tableData[a-1];i.tableData[a-1]=e,i.tableData[a]=n,i.$refs.xTable.reloadData(i.tableData)}}else e.statisticsResourceIndexId==t.statisticsResourceIndexPid&&e.children.forEach((function(a,n){if(a.statisticsResourceIndexId==t.statisticsResourceIndexId)if(0==n)i.$message.warning("已经在最上面了！");else{var s=e.children[n-1];e.children[n-1]=a,e.children[n]=s,i.$refs.xTable.reloadData(i.tableData)}}))}));else for(var n=0;n<this.tableData.length;n++){var s=this.tableData[n];if(t.add){if(s.statisticsResourceIndexId==t.statisticsResourceIndexId){if(n!=a){var o=this.tableData[n+1];return this.tableData[n+1]=s,this.tableData[n]=o,void this.$refs.xTable.reloadData(this.tableData)}this.$message.warning("已经在最下面了！")}}else{var r=s.children.length-1;if(s.statisticsResourceIndexId==t.statisticsResourceIndexPid)for(var l=0;l<s.children.length;l++){var d=s.children[l];if(d.statisticsResourceIndexId==t.statisticsResourceIndexId){if(l!=r){var c=s.children[l+1];return s.children[l+1]=d,s.children[l]=c,void this.$refs.xTable.reloadData(this.tableData)}this.$message.warning("已经在最下面了！")}}}}},indicatorsChange:function(t){this.editForm.setFieldsValue({indexLabel:t.label})},selectAllEvent:function(t){t.checked,t.records},selectChangeEvent:function(t){t.checked,t.records},radomName:function(t){return Number(Math.random().toString().substr(3,t)+Date.now()).toString(36)},uuid:function(t,e){var i,a,n="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".split(""),s=[];if(e=e||n.length,t)for(i=0;i<t;i++)s[i]=n[0|Math.random()*e];else for(s[8]=s[13]=s[18]=s[23]="-",s[14]="4",i=0;i<36;i++)s[i]||(a=0|16*Math.random(),s[i]=n[19==i?3&a|8:a]);return s.join("")},editHandleOk:function(t){var e=this;this.editForm.validateFields((function(t){if(!t){var i=e.editForm.getFieldsValue(),a=(0,s.Z)({},i);switch(a.indexId=a.indicators.key,a.index=a.indicators.label,e.titleName){case"新增":a.add=!0,a.children=[],a.pushId=e.radomName(36),a.skipName=e.skipName,a.statisticsResourceIndexId=e.uuid(32),e.tableData.push(a),e.dataReset();break;case"编辑":e.tableData.forEach((function(t){t.pushId===e.chosePushId?(t.skipName=e.skipName,t.indexId=a.indexId,t.index=a.index,t.indexLabel=a.indexLabel,t.jumpResourceId=a.jumpResourceId,e.dataReset()):t.children.forEach((function(t){t.pushId===e.chosePushId&&(t.skipName=e.skipName,t.indexId=a.indexId,t.index=a.index,t.indexLabel=a.indexLabel,t.jumpResourceId=a.jumpResourceId,e.dataReset())}))}));break;case"添加下级":a.add=!1,a.pushId=e.radomName(36),a.skipName=e.skipName,a.statisticsResourceIndexId=e.uuid(32),e.tableData.forEach((function(t){t.pushId==e.chosePushId&&(a.parentIndexId=t.indexId,a.statisticsResourceIndexPid=t.statisticsResourceIndexId,t.children.push(a),e.dataReset())}));break;default:break}}}))},conditionDefault:function(t){this.conditionBox=this.conditionBox.filter((function(e){return e.value!==t.value}));var e=[];this.conditionBox.forEach((function(t){e.push(t.value)})),this.form.setFieldsValue({conditionList:e})},dimensionDefault:function(t){this.dimensionBox=this.dimensionBox.filter((function(e){return e.value!==t.value}));var e=[];this.dimensionBox.forEach((function(t){e.push(t.value)})),this.form.setFieldsValue({dimensionList:e})},dataReset:function(){var t=this;this.editVisible=!1,this.$nextTick((function(){t.titleName="",t.skipName="",t.editForm.resetFields()}))},editCancel:function(t){var e=this;this.$nextTick((function(){e.titleName="",e.skipName="",e.editForm.resetFields()}))},getIndex:function(t){var e=this;this.Base.submit(null,{url:"statisticsConfig/queryAllIndex",data:{dimensionStr:t}}).then((function(t){if(e.idOption=t.data.result,e.formDisabled=!0,0==e.idOption.length)e.tableData=[],e.$message.warning("当前所选择的维度条件下没有关联指标");else if(e.tableData.length>0){var i=[];e.tableData.forEach((function(t,a){e.idOption.forEach((function(e){e.value==t.indexId&&i.push(t)}))})),e.tableData=i}}))}}},r=o,l=i(1001),d=(0,l.Z)(r,a,n,!1,null,"121d44e1",null),c=d.exports}}]);
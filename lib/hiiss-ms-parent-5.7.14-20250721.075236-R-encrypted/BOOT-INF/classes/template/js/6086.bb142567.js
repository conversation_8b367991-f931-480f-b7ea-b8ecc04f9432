"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[6086],{88412:function(e,t,a){var r=a(26263),n=a(36766),o=a(1001),c=(0,o.Z)(n.Z,r.s,r.x,!1,null,"5e7ef0ae",null);t["Z"]=c.exports},66086:function(e,t,a){a.d(t,{Z:function(){return q}});var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("ta-border-layout",{staticStyle:{height:"730px"},attrs:{"show-padding":!1,layout:{right:"30%"}}},[a("ta-row",{staticStyle:{height:"100%",background:"#F0F2F5"}},[a("ta-col",{staticClass:"card"},[a("ta-title",{attrs:{title:"审核规则标准"}}),a("div",{staticStyle:{"margin-left":"15px"}},[a("span",[e._v("限制条件: "+e._s(e.record.record?e.record.record.condition:""))]),a("ta-row",{staticStyle:{"margin-top":"10px"}},[a("ta-col",{staticStyle:{margin:"5px"},attrs:{span:23}},[a("span",[e._v("人工审核标准")]),a("div",{staticStyle:{height:"180px",border:"1px black solid"}},[e._v(e._s(e.record.standDetail&&e.record.standDetail.ykz066?e.record.standDetail.ykz066:"无"))])]),a("ta-col",{staticStyle:{margin:"5px",display:"none"},attrs:{span:7}},[a("span",[e._v("标准拆分说明")]),a("div",{staticStyle:{height:"180px",border:"1px black solid"}},[e._v(e._s(e.record.standDetail&&e.record.standDetail.ykz068?e.record.standDetail.ykz068:"无"))])])],1)],1)],1),a("ta-col",{staticClass:"card",staticStyle:{"margin-bottom":"0"}},[a("ta-title",{attrs:{title:"同组项目列表"}}),a("div",{staticStyle:{height:"320px"}},[a("ta-table",{attrs:{scroll:{y:300},"data-source":e.projectGroupData,columns:e.projectGroupColumns,bordered:!0}})],1),a("ta-pagination",{ref:"projectGroupPageHelper",attrs:{"data-source":e.projectGroupData,defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"],params:e.getPageHelperParam,url:e.URL.getSameGroupData},on:{"update:dataSource":function(t){e.projectGroupData=t},"update:data-source":function(t){e.projectGroupData=t}}})],1)],1),a("div",{staticStyle:{height:"100%"},attrs:{slot:"right"},slot:"right"},[a("ta-title",{attrs:{title:"审核节点内容"}}),a("ta-e-tree",{attrs:{"expand-on-click-node":!1,data:e.treeData,"default-expand-all":"",props:{children:"children",label:"name",id:"id",isLeaf:"leaf"}},scopedSlots:e._u([{key:"default",fn:function(t){t.node;var r=t.data;return a("a",{on:{click:function(t){return e.openNodeModal(r)}}},[a("ta-popover",{attrs:{trigger:"hover",disabled:!r.ykz078||""===r.ykz078}},[a("p",{attrs:{slot:"content",placement:"right"},domProps:{innerHTML:e._s(r.ykz078)},slot:"content"}),a("span",{attrs:{slot:"reference"},slot:"reference"},[a("span",[e._v(e._s(r.name))]),a("a",{directives:[{name:"show",rawName:"v-show",value:r.ykz042&&""!==r.ykz042,expression:"data.ykz042&&data.ykz042!==''"}],attrs:{title:"查看节点详情"}},[a("ta-icon",{attrs:{type:"plus-circle"}})],1)])])],1)}}])})],1),a("ta-modal",{attrs:{title:e.modalTitle,height:"730px",width:"80%",bodyStyle:{padding:"0"},"destroy-on-close":!0,footer:null},on:{cancel:e.cancelNodeModal},model:{value:e.showModal,callback:function(t){e.showModal=t},expression:"showModal"}},[a("node-detail",{attrs:{record:e.record,node:e.detailData_node}})],1)],1)},n=[],o=a(88412),c=a(39744),i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("ta-row",[a("ta-label-con",{staticStyle:{padding:"10px"},attrs:{"label-col":{span:4},"label-align":"center",label:"节点说明:"}},[a("ta-input",{attrs:{disabled:""},model:{value:e.ykz065,callback:function(t){e.ykz065=t},expression:"ykz065"}})],1),a("ta-collapse",[a("ta-collapse-panel",{attrs:{"force-render":!0,defaultActiveKey:"1"}},[a("ta-title",{attrs:{slot:"header",title:"节点更新记录"},slot:"header"},[a("ta-icon",{attrs:{type:"question-circle-o"}})],1),a("div",{staticStyle:{height:"300px"}},[a("ta-table",{attrs:{columns:e.updateRecordColumns,"data-source":e.updateRecordData,scroll:{y:"220px"}}}),a("ta-pagination",{ref:"nodePageHelper",attrs:{"data-source":e.updateRecordData,url:e.URL.queryPackageUpdateRecord,defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"],params:e.getParams},on:{"update:dataSource":function(t){e.updateRecordData=t},"update:data-source":function(t){e.updateRecordData=t}}})],1)],1)],1),a("ta-table",{staticStyle:{"margin-top":"20px"},attrs:{columns:e.nodeColumns,"data-source":e.nodeData,scroll:{y:"400px"},"have-sn":!0},scopedSlots:e._u([{key:"filterDropdown",fn:function(t){var r=t.setSelectedKeys,n=t.selectedKeys,o=t.confirm,c=t.clearFilters;return a("div",{staticClass:"custom-filter-dropdown"},[a("ta-input",{ref:"searchInput",attrs:{placeholder:"Search name",value:n[0]},on:{change:function(e){return r(e.target.value?[e.target.value]:[])},pressEnter:function(){return e.handleSearch(n,o)}}}),a("ta-button",{attrs:{type:"primary"},on:{click:function(){return e.handleSearch(n,o)}}},[e._v("Search")]),a("ta-button",{on:{click:function(){return e.handleReset(c)}}},[e._v("Reset")])],1)}}])})],1)},l=[],u=a(48534),d=(a(36133),a(32564),void 0),s=[{title:"更新原因",dataIndex:"ykz137",width:120,align:"center",overflowTooltip:!0},{title:"更新内容说明",dataIndex:"ykz138",width:120,align:"center",overflowTooltip:!0},{title:"更新时间",dataIndex:"OperationTime",width:120,align:"center",overflowTooltip:!0}],p=[{title:"节点名称",dataIndex:"ykz010",width:120,align:"center",overflowTooltip:!0},{title:"内容编码",dataIndex:"ykz081",width:120,align:"center",overflowTooltip:!0,scopedSlots:{filterDropdown:"filterDropdown",filterIcon:"filterIcon"},onFilter:function(e,t){return t.ykz081.toString().toLowerCase().includes(e.toLowerCase())},onFilterDropdownVisibleChange:function(e){e&&setTimeout((function(){d.searchInput.focus()}),0)}},{title:"内容名",dataIndex:"ykz082",width:120,align:"center",overflowTooltip:!0,scopedSlots:{filterDropdown:"filterDropdown",filterIcon:"filterIcon"},onFilter:function(e,t){return t.ykz082.toString().toLowerCase().includes(e.toLowerCase())},onFilterDropdownVisibleChange:function(e){e&&setTimeout((function(){d.searchInput.focus()}),0)}},{title:"配置原因",dataIndex:"ape889",width:120,align:"center",overflowTooltip:!0}],f=[].concat(p,[{title:"对比方式",dataIndex:"ape995",width:120,align:"center",overflowTooltip:!0},{title:"阈值",dataIndex:"aae421",width:120,align:"center",overflowTooltip:!0},{title:"结果单位",dataIndex:"aka219",width:120,align:"center",overflowTooltip:!0},{title:"满足方式",dataIndex:"ape991",width:120,align:"center",overflowTooltip:!0}]),y={name:"nodeDetail",components:{TaTitle:o.Z},props:{record:Object,node:Object},data:function(){return{URL:c.J,updateRecordColumns:s,updateRecordData:[],nodeColumns:p,nodeData:[],ykz065:"",searchText:"",searchInput:null}},methods:{getParams:function(){return{ykz042:this.node.ykz042,ykz108:this.record.record.ykz108}},handleSearch:function(e,t){t(),this.searchText=e[0]},handleReset:function(e){e(),this.searchText=""}},mounted:function(){var e=this;return(0,u.Z)(regeneratorRuntime.mark((function t(){var a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,c.Z.queryNodeInfo({ykz042:e.node.ykz042,aae043:e.record.record.aae043,ykz108:e.record.record.ykz108});case 2:if(a=t.sent,!a){t.next=17;break}if(e.ykz065=a.ykz065,e.$refs.nodePageHelper.loadData(),!("1"===a.ykz091&&e.node.ykz042.indexOf("JY")>=0)){t.next=13;break}return t.next=9,c.Z.queryKf41({ykz042:e.node.ykz042,aae043:e.record.record.aae043});case 9:e.nodeData=t.sent,e.nodeColumns=f,t.next=17;break;case 13:return t.next=15,c.Z.queryKf32({ykz042:e.node.ykz042,aae043:e.record.record.aae043});case 15:e.nodeData=t.sent,e.nodeColumns=p;case 17:case"end":return t.stop()}}),t)})))()}},h=y,g=a(1001),m=(0,g.Z)(h,i,l,!1,null,"6e578890",null),x=m.exports,v=[{title:"三目编码",dataIndex:"ake001",width:120,align:"center",overflowTooltip:!0},{title:"三目名称",dataIndex:"ake002",width:120,align:"center",overflowTooltip:!0}],k=function(e){var t=[];if(!Array.isArray(e))return t;var a={};return e.forEach((function(e){delete e.children,a[e.id]=e})),e.forEach((function(e){var r=a[e.pid];r?(r.children||(r.children=[])).push(e):t.push(e)})),t},w={name:"ruleConfigDetail",components:{NodeDetail:x,TaTitle:o.Z},props:{record:Object},data:function(){return{URL:c.J,modalTitle:"节点说明及内涵查询",showModal:!1,projectGroupColumns:v,projectGroupData:[],treeData:[],detailData_node:{}}},methods:{openNodeModal:function(e){this.detailData_node=e,this.showModal=!0},getPageHelperParam:function(){return this.record.record},cancelNodeModal:function(){this.detailData_node={},this.showModal=!1}},mounted:function(){var e=this;this.treeData=k(this.record.ruleTree),this.$nextTick((function(){e.$refs.projectGroupPageHelper.loadData()}))}},S=w,T=(0,g.Z)(S,r,n,!1,null,"372952e9",null),q=T.exports},36766:function(e,t,a){var r=a(66586);t["Z"]=r.Z},26263:function(e,t,a){a.d(t,{s:function(){return r},x:function(){return n}});var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:e.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[e._v(e._s(e.title))]),e._t("default")],2)},n=[]},66586:function(e,t){var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};t["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}},39744:function(e,t,a){a.d(t,{J:function(){return d}});var r=a(82482),n=a(95082),o=a(66353),c=a(48534),i=(a(36133),["form"]),l="/ruleInfo",u="/reasonTemplate",d={getMedicalInsuranceTypeList:"".concat(l,"/queryMedicalInsuranceType"),getSelectOptions:"".concat(l,"/getSelectOptions"),queryAuditStandDetail:"".concat(l,"/queryAuditStandDetail"),getRuleTree:"".concat(l,"/getRuleTree"),queryNodeType:"".concat(l,"/queryNodeType"),queryNodeTypeInUpdate:"".concat(l,"/queryNodeTypeInUpdate"),getSelectOptionsWithMaxBatch:"".concat(l,"/getSelectOptionsWithMaxBatch"),getSmInfo:"".concat(l,"/getSmInfo"),queryNodeInfo:"".concat(l,"/queryNodeInfo"),queryKf32:"".concat(l,"/queryKf32"),queryKf41:"".concat(l,"/queryKf41"),auditRule:"".concat(l,"/auditRule"),queryYdxx:"".concat(l,"/queryYdxx"),queryBxcz:"".concat(l,"/queryBxcz"),getAa10ByAaa100:"miimCommonRead/getAa10ByAaa100",insertKf35:"".concat(l,"/insertKf35"),cancelConfigKf35:"".concat(l,"/cancelConfigKf35"),cancelConfigKf35Type:"".concat(l,"/cancelConfigKf35Type"),insertKf35Type:"".concat(l,"/insertKf35Type"),queryRule:"".concat(l,"/queryRuleInfo"),getSameGroupData:"".concat(l,"/getSameGroupData"),queryRuleInfoByAuditState:"".concat(l,"/queryRuleInfoByAuditState"),queryPackageUpdateRecord:"".concat(l,"/queryPackageUpdateRecord"),queryNoExit:"".concat(l,"/queryNoExit"),queryByPage:"".concat(l,"/queryByPage"),queryReasonTemplateInfoList:"".concat(u,"/queryAe21InfoPage")},s={getMedicalInsuranceTypeList:{url:d.getMedicalInsuranceTypeList},getSelectOptions:{url:d.getSelectOptions},queryAuditStandDetail:{url:d.queryAuditStandDetail},getRuleTree:{url:d.getRuleTree},queryNodeType:{url:d.queryNodeType},queryNodeTypeInUpdate:{url:d.queryNodeTypeInUpdate,returnKey:"nodeType"},getSelectOptionsWithMaxBatch:{url:d.getSelectOptionsWithMaxBatch},getSmInfo:{url:d.getSmInfo,returnKey:"smInfo"},queryNodeInfo:{url:d.queryNodeInfo,returnKey:"nodeInfo"},queryKf32:{url:d.queryKf32,returnKey:"kf32TableData"},queryKf41:{url:d.queryKf41,returnKey:"kf41TableData"},auditRule:{url:d.auditRule,config:{autoQs:!1}},queryYdxx:{url:d.queryYdxx,returnKey:"ydxx"},queryBxcz:{url:d.queryBxcz},getAa10ByAaa100:{url:d.getAa10ByAaa100},insertKf35:{url:d.insertKf35},cancelConfigKf35:{url:d.cancelConfigKf35},cancelConfigKf35Type:{url:d.cancelConfigKf35Type},insertKf35Type:{url:d.insertKf35Type}},p={baseRequest:function(){var e=(0,c.Z)(regeneratorRuntime.mark((function e(t,a,r){var c,l,u,d;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return c=null,l=null,r&&(u=r,c=u.form,l=(0,o.Z)(u,i)),e.next=5,Base.submit(c,(0,n.Z)({url:a,data:t},l)).catch((function(e){throw e}));case 5:return d=e.sent,e.abrupt("return",d.data);case 7:case"end":return e.stop()}}),e)})));function t(t,a,r){return e.apply(this,arguments)}return t}()};Object.entries(s).forEach((function(e){var t=function(){var t=(0,c.Z)(regeneratorRuntime.mark((function t(a){var r;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,this.baseRequest(a,e[1].url,e[1].config);case 2:if(r=t.sent,!e[1].returnKey||""===e[1].returnKey){t.next=5;break}return t.abrupt("return",r[e[1].returnKey]);case 5:return t.abrupt("return",r);case 6:case"end":return t.stop()}}),t,this)})));return function(e){return t.apply(this,arguments)}}();Object.assign(p,(0,r.Z)({},e[0],t))})),t["Z"]=p}}]);
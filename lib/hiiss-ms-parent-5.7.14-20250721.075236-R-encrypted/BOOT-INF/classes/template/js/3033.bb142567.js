"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[3033],{88412:function(e,t,a){var i=a(26263),n=a(36766),r=a(1001),s=(0,r.Z)(n.Z,i.s,i.x,!1,null,"5e7ef0ae",null);t["Z"]=s.exports},6832:function(e,t,a){a.d(t,{Z:function(){return h}});var i=function(){var e=this,t=this,a=t.$createElement,i=t._self._c||a;return i("ta-drawer",{attrs:{width:"930",title:t.title,placement:"right",visible:t.visible,"mask-closable":!1,"get-container":function(){return e.$el.parentNode}},on:{close:t.handleClose}},[i("div",{attrs:{slot:"footer"},slot:"footer"},[i("div",{staticStyle:{display:"flex","justify-content":"center",width:"100%"}},[i("ta-button",{on:{click:t.handleClose}},[t._v(" "+t._s(t.inApproval?"取消":"关闭")+" ")]),i("ta-button",{directives:[{name:"show",rawName:"v-show",value:t.inApproval,expression:"inApproval"}],attrs:{type:"primary"},on:{click:t.handleSave}},[t._v(" 确定 ")])],1)]),i("ta-form",{attrs:{"auto-form-create":function(t){return e.form=t},layout:"horizontal","form-layout":!0,labelAlign:"left","label-width":"100px",col:{xs:1,sm:1,md:1,lg:1,xl:1,xxl:1}}},[i("ta-form-item",{attrs:{label:"疑点Id","field-decorator-id":"aaz213",hidden:!0}},[i("ta-input")],1),i("ta-form-item",{attrs:{label:"参保人","field-decorator-id":"aac003",span:8}},[t._v(" "+t._s(t.params.aac003||"")+" ")]),i("ta-form-item",{attrs:{label:"住院号","field-decorator-id":"akc190",span:8}},[t._v(" "+t._s(t.params.akc190||"")+" ")]),i("ta-form-item",{attrs:{label:"年龄","field-decorator-id":"age",span:8}},[t._v(" "+t._s(t.params.age||"")+" ")]),i("ta-form-item",{attrs:{label:"住院天数","field-decorator-id":"iptDays",span:8}},[t._v(" "+t._s(t.params.iptDays||"")+" ")]),i("ta-form-item",{attrs:{label:"入院日期","field-decorator-id":"aae030",span:8}},[t._v(" "+t._s(t.params.aae030||"")+" ")]),i("ta-form-item",{attrs:{label:"出院日期","field-decorator-id":"aae031",span:8}},[t._v(" "+t._s(t.params.aae031||"")+" ")]),i("ta-form-item",{attrs:{label:"性别","field-decorator-id":"aac003",span:8}},[t._v(" "+t._s(t.CollectionLabel("SEX",t.params.aac004)||"")+" ")]),i("ta-form-item",{attrs:{label:"扣款条目数","field-decorator-id":"wfsl",span:8}},[t._v(" "+t._s(t.params.failCount||"")+"条 ")]),i("ta-form-item",{attrs:{label:" ",span:8}},[i("div",{staticClass:"link-row"},[t.emrLinkEnabled?i("a",{staticClass:"link-entry",attrs:{href:"javascript:void(0);"},on:{click:t.openEmrQuery}},[t._v("电子病历入口")]):t._e(),t.costDetailEnabled?i("a",{staticClass:"link-entry",attrs:{href:"javascript:void(0);"},on:{click:t.openCostDetail}},[t._v("费用明细入口")]):t._e()])]),i("ta-form-item",{attrs:{label:"规则名称","field-decorator-id":"aaa167"}},[t._v(" "+t._s(t.params.aaa167||"")+" ")]),i("ta-form-item",{attrs:{label:"违反项目","field-decorator-id":"ake002"}},[t._v(" "+t._s(t.params.ake002||"")+" ")]),i("ta-form-item",{attrs:{label:"规则描述","field-decorator-id":"ykz018"}},[t._v(" "+t._s(t.params.ykz018||"")+" ")]),i("ta-form-item",{attrs:{label:"诊断","field-decorator-id":"diagnosis"}},[t._v(" "+t._s(t.params.diagnosis||t.params.aka121||"")+" ")]),i("ta-form-item",{attrs:{label:"是否申诉","field-decorator-id":"appealFlag"}},[t._v(" "+t._s("1"===t.params.appealFlag?"申诉":"不申诉，认同并及时整改")+" ")]),i("ta-form-item",{staticStyle:{position:"relative"},attrs:{label:t.inApproval?"补充附件":"预览附件","field-decorator-id":"file"}},[[i("div",[t.inApproval?i("div",[i("div",[i("ta-radio-group",{attrs:{cancelChecked:!0,options:t.plainOptions},model:{value:t.fileType,callback:function(e){t.fileType=e},expression:"fileType"}})],1),i("ta-upload",{attrs:{multiple:!0,"file-list":t.addList,"before-upload":t.beforeUpload,"show-upload-list":!1,accept:"image/jpg,image/png,image/jpeg,IMAGE/JPG,IMAGE/PNG,IMAGE/JPEG"}},[i("ta-button",[i("ta-icon",{attrs:{type:"upload"}}),t._v(" 补充附件 ")],1)],1),i("span",{staticStyle:{color:"#999999","font-size":"12px"}},[t._v(" 只能上传 jpg/png/jpeg 文件 "),i("span",{directives:[{name:"show",rawName:"v-show",value:this.processConfig.configNum,expression:"this.processConfig.configNum"}]},[t._v("，且不超过"+t._s(this.processConfig.configNum+this.processConfig.configUnit))])])],1):t._e(),i("div",{directives:[{name:"show",rawName:"v-show",value:t.fileList.length>0,expression:"fileList.length > 0"}],staticClass:"fileListStyle"},[t._l(t.fileList.filter((function(e){return"2"!==e.upType})),(function(e){return i("div",{key:e.uid||e.fileId,staticClass:"fileItem",staticStyle:{cursor:"pointer"},on:{mouseover:function(a){return t.debounceShowPic(e)},mouseout:function(a){return t.hidePic(e)},click:function(a){return a.stopPropagation(),t.fnDownloadImg(e)}}},[i("ta-icon",{attrs:{type:"link"}}),i("span",{staticClass:"fileName"},[t._v(t._s(e.name||e.fileName))]),i("div",{staticClass:"fileActions"},[i("ta-icon",{directives:[{name:"show",rawName:"v-show",value:e.fileId,expression:"item.fileId"}],staticClass:"downloadIcon",attrs:{type:"download"},on:{click:function(a){return a.stopPropagation(),t.downloadImage(e)}}}),i("ta-icon",{directives:[{name:"show",rawName:"v-show",value:t.inApproval,expression:"inApproval"}],staticClass:"delIcon",attrs:{type:"delete"},on:{click:function(a){return a.stopPropagation(),function(a){return t.handleRemove(e,a)}.apply(null,arguments)}}})],1)],1)})),i("span",{directives:[{name:"show",rawName:"v-show",value:t.fileList.filter((function(e){return"2"===e.upType})).length>0,expression:"fileList.filter(item => item.upType === '2').length > 0"}],staticStyle:{color:"#999999","font-size":"12px"}},[t._v("补充附件:")]),t._l(t.fileList.filter((function(e){return"2"===e.upType})),(function(e){return i("div",{key:e.uid||e.fileId,staticClass:"fileItem",staticStyle:{cursor:"pointer"},on:{mouseover:function(a){return t.debounceShowPic(e)},mouseout:function(a){return t.hidePic(e)},click:function(a){return a.stopPropagation(),t.fnDownloadImg(e)}}},[i("ta-icon",{attrs:{type:"link"}}),i("span",{staticClass:"fileName"},[t._v(t._s(e.name||e.fileName))]),i("div",{staticClass:"fileActions"},[i("ta-icon",{directives:[{name:"show",rawName:"v-show",value:e.fileId,expression:"item.fileId"}],staticClass:"downloadIcon",attrs:{type:"download"},on:{click:function(a){return a.stopPropagation(),t.downloadImage(e)}}}),i("ta-icon",{directives:[{name:"show",rawName:"v-show",value:t.inApproval,expression:"inApproval"}],staticClass:"delIcon",attrs:{type:"delete"},on:{click:function(a){return a.stopPropagation(),function(a){return t.handleRemove(e,a)}.apply(null,arguments)}}})],1)],1)}))],2),i("div",{directives:[{name:"show",rawName:"v-show",value:t.showFile,expression:"showFile"}],staticClass:"filePreview"},[i("img",{ref:"imgPreview",attrs:{src:t.fileUrl,height:"250",width:"100%"}})])])]],2),i("ta-form-item",{attrs:{label:"申诉理由","field-decorator-id":"aaz560"}},[t._v(" "+t._s(t.params.aaz560||"")+" ")]),i("ta-form-item",{attrs:{label:"实际处理人","field-decorator-id":"currPersName"}},[t._v(" "+t._s(t.param.handPers||t.param.currPersName)+" ")]),i("ta-form-item",{directives:[{name:"show",rawName:"v-show",value:["4","6","9","10"].includes(t.params.handStas),expression:"['4','6','9','10'].includes(params.handStas)"}],attrs:{label:["6","9"].includes(t.params.handStas)?"驳回人":"审批人","field-decorator-id":"rejctPers"}},[t._v(" "+t._s(["6","9"].includes(t.params.handStas)?t.params.rejctPers||"":t.params.auditPers||"")+" ")]),i("ta-form-item",{attrs:{label:"审批结果","field-decorator-id":"handStas",require:t.inApproval}},[t.inApproval?i("div",[i("ta-radio-group",{attrs:{options:[{value:"4",label:"通过"},{value:"6",label:"驳回"}]},on:{change:t.changeApprovalResult},model:{value:t.handStasModel,callback:function(e){t.handStasModel=e},expression:"handStasModel"}})],1):i("div",[i("span",[t._v(t._s(["4","10"].includes(t.approvalResult)?"医保办审批通过":"6"==t.approvalResult?"医保办已驳回":""))])])]),i("ta-form-item",{attrs:{label:"驳回原因","field-decorator-id":"rejctReas","field-decorator-options":{rules:[{max:2e3,message:"不得超过2000字符"}]},require:t.inApproval&&"6"===t.approvalResult,hidden:"6"!==t.approvalResult}},[t.inApproval?i("div",[i("ta-textarea",{attrs:{placeholder:"请输入驳回原因",rows:5,"show-length":!0},model:{value:t.rejctReasModel,callback:function(e){t.rejctReasModel=e},expression:"rejctReasModel"}})],1):i("div",[i("p",[t._v(" "+t._s(t.params.rejctReas)+" ")])])])],1),i("div",{staticStyle:{width:"100%","background-color":"#FFFFFF","text-align":"left"}},[i("ta-checkbox",{directives:[{name:"show",rawName:"v-show",value:"3"!=t.param.dataType,expression:"param.dataType != '3'"}],attrs:{disabled:!t.inApproval,checked:t.syncInfo},on:{change:t.changeSyncInfo}},[t._v(" 将附件和申诉理由应用于当前患者该违规项目(任务状态也会同步变更) ")])],1),i("ta-modal",{attrs:{title:this.fileName,height:"600px",width:"80%",bodyStyle:{textAlign:"center"},footer:null},model:{value:t.imgVisible,callback:function(e){t.imgVisible=e},expression:"imgVisible"}},[i("img",{attrs:{src:t.imgSrc},on:{click:t.downloadImage}})])],1)},n=[],r=a(48534),s=(a(32564),a(36133),a(52582)),l=a(36797),o=a.n(l),c={name:"approval",components:{},props:{visible:{type:Boolean,default:!1},param:{type:Object,default:function(){return{}}}},data:function(){return{title:"申诉审批",inApproval:!0,approvalResult:"",handStasModel:"",rejctReasModel:"",fileList:[],addList:[],removeList:[],showFile:!1,syncInfo:!1,fileUrl:"",imgVisible:!1,imgSrc:"",fileName:"",params:{},debounceShowPic:null,basePath:s.Z.getBasePath(),emrLinkEnabled:!1,costDetailEnabled:!1,costDetailUrl:!1,emrVersion:null,hisChromeAddress:null,hisEmrApiUrl:null,plainOptions:[],fileType:"",processConfig:{}}},watch:{visible:{handler:function(e){var t=this;e&&this.$nextTick((function(){"3"===t.param.handStas?(t.inApproval=!0,t.title="申诉审批"):(t.inApproval=!1,t.title="申诉详情",t.approvalResult=t.param.handStas),t.param.syncInfo&&"-1"!=t.param.syncInfo&&(t.syncInfo=!0),t.getAKb020ByJobNumber(),s.Z.getFileList({aaz213:t.param.aaz213},(function(e){t.params=e.data.appeal,t.fileList=e.data.appeal.files,t.params.emrLinkEnabled&&(t.emrLinkEnabled=!0,t.emrVersion=t.params.emrVersion,t.hisChromeAddress=t.params.hisChromeAddress,t.hisEmrApiUrl=t.params.hisEmrApiUrl),t.params.costDetailEnabled&&(t.costDetailEnabled=!0,t.costDetailUrl=t.params.costDetailUrl)}))}))}}},mounted:function(){},created:function(){this.debounceShowPic=this.debounce(this.showPic,1500)},methods:{moment:o(),getAKb020ByJobNumber:function(){var e=this;this.Base.submit(null,{url:"/appeal/common/getAKb020ByJobNumber",data:{loginId:""}},{successCallback:function(t){var a=t.data.akb020;a&&e.Base.submit(null,{url:"miimCommonRead/queryProcessConfig",data:{akb020:a}},{successCallback:function(t){var a,i;e.processConfig=t.data.data,(null!==(a=e.processConfig)&&void 0!==a&&a.attachConfigTypeOne||null!==(i=e.processConfig)&&void 0!==i&&i.attachConfigTypeTwo)&&("3"==e.param.dataType?e.plainOptions=e.processConfig.attachConfigTypeTwo.split(",").map((function(e){return{label:e,value:e}})):e.plainOptions=e.processConfig.attachConfigTypeOne.split(",").map((function(e){return{label:e,value:e}})))}})},failCallback:function(t){e.$message.error(t.errors[0])}})},openEmrQuery:function(){var e=window.QClient;e?e.exec(this.hisChromeAddress,this.hisEmrApiUrl):window.open(this.hisEmrApiUrl,"_blank")},openCostDetail:function(){var e=window.QClient;e?e.exec(this.hisChromeAddress,this.costDetailUrl):window.open(this.costDetailUrl,"_blank")},handleRemove:function(e,t){t.stopPropagation();var a=this.fileList.indexOf(e);e.fileId?this.removeList.push(e.fileId):this.addList=this.addList.filter((function(t){return t.uid!==e.uid}));var i=this.fileList.slice();i.splice(a,1),this.debounceShowPic&&this.debounceShowPic.cancel(),this.showFile=!1,this.fileUrl="",this.fileList=i},beforeUpload:function(e){var t="MB"===this.processConfig.configUnit?1024*this.processConfig.configNum:this.processConfig.configNum;if(e.size/1024>t)return this.$message.warning("请选择不超过"+this.processConfig.configNum+this.processConfig.configUnit+"的文件"),!1;var a=e.name.split("."),i=a[a.length-1].toLowerCase();if(-1===["jpg","png","jpeg"].indexOf(i.toLowerCase()))return this.$message.warning("只能上传jpg/png/jpeg文件"),!1;var n=this.nameRuleCommon(this.processConfig.nameRule1),r=this.nameRuleCommon(this.processConfig.nameRule2),s=this.nameRuleCommon(this.processConfig.nameRule3),l=[n,r,s].filter(Boolean),o=l.join("_"),c="0";try{if(Array.isArray(this.fileList)&&this.fileList.length>0){var u=this.fileList.filter((function(e){var t=(null===e||void 0===e?void 0:e.name)||(null===e||void 0===e?void 0:e.fileName);return"string"===typeof t&&t.substring(0,t.lastIndexOf("_"))===o}));if(Array.isArray(u)&&u.length>0){var d=u.map((function(e){var t=(null===e||void 0===e?void 0:e.name)||(null===e||void 0===e?void 0:e.fileName);if("string"!==typeof t)return 0;var a=t.substring(0,t.lastIndexOf("."));return parseInt(a.substring(a.lastIndexOf("_")+1),10)||0}));c=d.reduce((function(e,t){return Math.abs(t)>Math.abs(e)?t:e}),0)}}}catch(f){this.$message.error("错误处理文件列表:",f),c=0}var p=o?"".concat(o,"_").concat(Number(c)+1,".").concat(i):"".concat(Number(c)+1,".").concat(i),h=this.renameFile(e,p);return e.newFileName=p,h.uid=e.uid,h.upType="2",this.fileList=this.fileList.concat(h),this.addList=this.addList.concat(e),!1},changeSyncInfo:function(e){this.syncInfo=e.target.checked},downloadImage:function(e){var t=this;return(0,r.Z)(regeneratorRuntime.mark((function a(){var i,n,r,s,l;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return null!==e&&void 0!==e&&e.fileId&&(t.imgSrc=t.basePath+"/appeal/drPoint/getFile?fileId="+e.fileId),a.prev=1,a.next=4,fetch(t.imgSrc,{mode:"cors"});case 4:if(i=a.sent,i.ok){a.next=8;break}return t.$message.error("网络响应失败，请稍后重试，状态码:",i.status),a.abrupt("return");case 8:if(n=i.headers.get("Content-Type"),n&&n.startsWith("image/")){a.next=12;break}return t.$message.error("获取的资源不是图片，Content-Type:",n),a.abrupt("return");case 12:return a.next=14,i.blob();case 14:r=a.sent,s=URL.createObjectURL(r),l=document.createElement("a"),l.href=s,l.download=t.fileName||e.fileName,l.style.display="none",document.body.appendChild(l),l.click(),document.body.removeChild(l),URL.revokeObjectURL(s),a.next=29;break;case 26:a.prev=26,a.t0=a["catch"](1),t.$message.error("下载图片时发生错误:",a.t0);case 29:case"end":return a.stop()}}),a,null,[[1,26]])})))()},fnDownloadImg:function(e){e.fileId&&(this.imgSrc=this.basePath+"/appeal/drPoint/getFile?fileId="+e.fileId,this.fileName=e.name||e.fileName,this.imgVisible=!0)},getFiles:function(){var e=s.Z.getPicZipUrl()+"?aaz213="+this.params.aaz213;window.location.href=e},nameRuleCommon:function(e){return"fileType"===e?this.fileType:"akc190"===e||"akc191"===e?this.params["akc190"]:this.params[e]},renameFile:function(e,t){return new File([e],t,{lastModified:e.lastModified,lastModifiedDate:e.lastModifiedDate,name:t,size:e.size,type:e.type,webkitRelativePath:e.webkitRelativePath,uid:e.uid,percent:0,originFileObj:e})},renameFiles:function(e,t){return{lastModified:e.lastModified,lastModifiedDate:e.lastModifiedDate,name:t,size:e.size,type:e.type,webkitRelativePath:e.webkitRelativePath,uid:e.uid,percent:0,originFileObj:e}},showPic:function(e){e.fileId&&(this.showFile=!0,this.fileUrl=this.basePath+"/appeal/drPoint/getFile?fileId="+e.fileId)},debounce:function(e,t){var a,i=function(){for(var i=this,n=arguments.length,r=new Array(n),s=0;s<n;s++)r[s]=arguments[s];a&&clearTimeout(a),a=setTimeout((function(){e.apply(i,r)}),t)};return i.cancel=function(){a&&clearTimeout(a)},i},hidePic:function(e){this.showFile=!1,this.debounceShowPic.cancel()},changeApprovalResult:function(e){this.approvalResult=e.target.value,this.handStas=e.target.value},handleSave:function(){var e=this;this.form.validateFields((function(t){if(!t){var a=e.form.getFieldsValue();delete a.aae030,delete a.aae031,a.aaz213=e.params.aaz213,a.aaz263=e.$route.query.aaz263||"",a.syncInfo=e.syncInfo?"1":"0",e.addList.length>0&&(a.files=e.addList,a.newNames=e.addList.map((function(e){return e.newFileName})).join("$$")),a.removeIds=JSON.stringify(e.removeList),s.Z.singelApproval(a,(function(t){t.errors&&t.errors.length>0&&e.$message.error(t.errors[0].msg),e.$message.success("审批成功"),e.handleClose()}))}}))},handleClose:function(){this.form.resetFields(),this.showFile=!1,this.syncInfo=!1,this.addList=[],this.removeList=[],this.fileList=[],this.handStas="",this.fileType="",this.approvalResult="",this.handStasModel="",this.rejctReasModel="",this.$emit("handleClose")}}},u=c,d=a(1001),p=(0,d.Z)(u,i,n,!1,null,"2cde3dda",null),h=p.exports},44142:function(e,t,a){a.d(t,{Z:function(){return g}});var i=function(){var e=this,t=this,a=t.$createElement,i=t._self._c||a;return i("ta-drawer",{attrs:{title:t.title,visible:t.visible,width:"80%"},on:{close:t.handleClose}},[i("ta-border-layout",{staticStyle:{height:"calc(100%)"},attrs:{"layout-type":"fixTop"}},[i("div",{staticClass:"query-header",attrs:{slot:"header"},slot:"header"},[i("div",{ref:"formBox",staticClass:"query-header-left-3"},[i("ta-form",{attrs:{"auto-form-create":function(t){return e.form=t},layout:"horizontal","form-layout":!0,"label-width":"80px",col:{xs:1,sm:2,md:2,lg:4,xl:4,xxl:4}}},[i("ta-form-item",{attrs:{label:"任务状态","init-value":"0","field-decorator-id":"status"}},[i("ta-select",{staticStyle:{width:"90%"},attrs:{placeholder:"请选择任务状态","allow-clear":!1,"collection-type":"APPEALHANDSTAS","collection-filter":"7,11"},on:{select:t.queryTableData}})],1),i("ta-form-item",{attrs:{label:"开单科室","field-decorator-id":"aac004"}},[i("ta-suggest",{staticStyle:{width:"95%"},attrs:{"option-config":{value:"value",label:"label"},"data-source":t.deptList,placeholder:"输入科室名称","table-title-map":new Map([["label",{name:"科室名称",style:{minWidth:"120px"}}]])},on:{select:function(e,a){return t.deptChange(e,a)},search:t.deptSearch,change:t.suggestChange}})],1),i("ta-form-item",{attrs:{label:"出院科室","field-decorator-id":"aae386"}},[i("ta-suggest",{staticStyle:{width:"95%"},attrs:{"option-config":{value:"value",label:"label"},"data-source":t.deptList2,placeholder:"输入科室名称","table-title-map":new Map([["label",{name:"科室名称",style:{minWidth:"120px"}}]])},on:{select:function(e,a){return t.deptChange(e,a)},search:t.deptSearch,change:t.suggestChange}})],1),i("ta-form-item",{attrs:{"label-width":200,label:"仅查看无申诉处理人的疑点","field-decorator-id":"noneCurrPers"}},[i("ta-switch",{staticStyle:{"margin-top":"5px"},on:{change:t.onSwichChange}})],1)],1)],1),i("div",{staticClass:"query-btn-3"},[i("ta-button",{attrs:{type:"primary",icon:"search"},on:{click:t.queryTableData}},[t._v(" 查询 ")]),i("ta-button",{attrs:{type:"primary",icon:"redo",ghost:!0},on:{click:t.resetForm}},[t._v(" 重置 ")])],1),i("div",{staticStyle:{float:"right",display:"flex","justify-content":"center","margin-bottom":"5px"}},[i("ta-button",{attrs:{icon:"edit",type:"primary"},on:{click:t.openBatchModefyModel}},[t._v(" 批量修改 ")]),i("ta-button",{attrs:{icon:"down-square",type:"primary"},on:{click:t.preHandleSavleAll}},[t._v(" 下发所有数据 ")]),i("ta-button",{attrs:{icon:"check-square",type:"primary"},on:{click:t.handleSave}},[t._v(" 下发选中数据 ")]),i("ta-button",{attrs:{icon:"setting",type:"primary",ghost:!0},on:{click:t.openPlugin}},[t._v(" 下发配置 ")])],1)]),i("div",{staticClass:"fit"},[i("ta-big-table",{ref:"xTable",staticStyle:{width:"100%"},attrs:{border:"","highlight-hover-row":"","show-overflow":"","sort-config":{trigger:"cell"},data:t.dataSource,height:"auto",resizable:!0,"auto-resize":"",size:"small","empty-text":"-",width:"min-width","row-id":"aaz213","keep-source":"","mouse-config":{selected:!0},"edit-config":{trigger:"click",mode:"cell",showStatus:!0},"keyboard-config":{isArrow:!0,isDel:!0,isEnter:!0,isTab:!0,isEdit:!0},"checkbox-config":{trigger:"row",highlight:!0,reserve:!0}},on:{"cell-selected":t.handleCellSelect},scopedSlots:t._u([{key:"bottomBar",fn:function(){return[i("ta-pagination",{ref:"gridPager",staticStyle:{"text-align":"right"},attrs:{size:"small","default-page-size":200,"page-size-options":["30","50","100","200","500"],"data-source":t.dataSource,url:t.pageUrl,params:t.pageParams},on:{"update:dataSource":function(e){t.dataSource=e},"update:data-source":function(e){t.dataSource=e}}})]},proxy:!0}])},[i("ta-big-table-column",{attrs:{type:"checkbox",fixed:"left",width:"40px",align:"center"}}),i("ta-big-table-column",{attrs:{field:"aaz213",title:"管理Id",visible:!1}}),i("ta-big-table-column",{attrs:{type:"seq",fixed:"left",width:"50px",title:"序号",align:"center"}}),i("ta-big-table-column",{attrs:{field:"handStas","show-overflow":"",title:"申诉任务状态",fixed:"left","min-width":"140px",sortable:""},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;e.column;return[i("div",{staticStyle:{position:"relative"}},[a.handStas?[t.CollectionLabel("SEX",a.handStas)?i("div",[i("ta-badge",{attrs:{color:t.statusColors[a.handStas]||"#000000"}}),i("span",{style:{color:t.statusColors[a.handStas]||"#000000"}},[t._v(t._s(t.CollectionLabel("APPEALHANDSTAS",a.handStas)))])],1):i("div",[i("ta-badge",{attrs:{color:"#000000"}}),i("span",{staticStyle:{color:"#000000"}},[t._v("未知状态")])],1)]:[i("ta-badge",{attrs:{color:"#000000"}}),i("span",{staticStyle:{color:"#000000"}},[t._v("未知状态")])]],2)]}}])}),i("ta-big-table-column",{attrs:{field:"akc190",title:"住院门诊号","min-width":"120px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"aac003",title:"参保人","min-width":"100px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"ake002",align:"center","show-overflow":"",title:"医保项目名称","min-width":"160px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"aaa167",title:"违反规则","min-width":"180px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"ape804",align:"center","show-overflow":"",title:"违规金额","min-width":"110px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"aae386V","show-overflow":"",title:"院内出院科室","min-width":"130px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"aac004V",align:"center","show-overflow":"",title:"院内开单科室","min-width":"130px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"aaz570V","show-overflow":"",title:"院内开单医生","min-width":"130px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"aae036",title:"明细时间","min-width":"150px",align:"center",sortable:""}}),i("ta-big-table-column",{attrs:{field:"aae386",title:"疑点出院科室","min-width":"130px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"aac004",title:"疑点开单科室","min-width":"130px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"aaz570",title:"疑点开单医生","min-width":"130px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"akc195","show-overflow":"",title:"申诉截止日期","min-width":"150px",align:"center",sortable:""}}),i("ta-big-table-column",{attrs:{field:"currPers",title:"申诉处理人",width:"100",visible:!1,sortable:""}}),i("ta-big-table-column",{attrs:{fixed:"right",field:"currPersNameInShow",title:"申诉处理人",width:"140","class-name":t.currPersStyle,"edit-render":{},sortable:""},scopedSlots:t._u([{key:"edit",fn:function(e){var a=e.row,n=e.column;return[i("ta-suggest",{staticStyle:{width:"100%"},attrs:{"auto-focus":"","data-source":t.peopleList,"table-title-map":new Map([["label",{name:"医生姓名",style:{minWidth:"350px"}}]]),"option-config":t.optionConfig,"dropdown-match-select-width":!1,"dropdown-style":{width:"100%"},"get-popup-container":t.setPopupContainer},on:{select:function(e,i){return t.handleChangeSuggest(e,a,n,i)},search:t.handleSearch,focus:function(){t.peopleList=[{label:a.currPersNameInShow,value:a.currPersInShow}],t.searchAkb020=a.akb020}},scopedSlots:t._u([{key:"body-cell",fn:function(e){e.cellValue,e.column,e.columnIndex;var a=e.row;e.rowIndex;return[i("span",[t._v(t._s(a.label)+" ("+t._s(a.value)+" "+t._s(a.aae386?a.aae386:"未知科室 "+a.aaz307)+" "+t._s(a.akb021?a.akb021:"未知医院 "+a.akb020)+")")])]}}],null,!0)})]}}])})],1)],1)]),i("plugin",{attrs:{visible:t.pluginVisible},on:{handleClose:t.closePlugin,handleOk:t.updatePlugin}}),i("ta-modal",{attrs:{title:"批量修改",visible:t.batchModefyVisible,height:250,width:500},on:{ok:t.handleBatchModefy,cancel:t.closeBatchModefyModel}},[i("p",[t._v("请选择修改后的申诉处理人")]),i("ta-form",{attrs:{"auto-form-create":function(t){return e.form1=t},layout:"horizontal","form-layout":!0,"label-width":"80px",col:{xs:1,sm:1,md:1,lg:1,xl:1,xxl:1}}},[i("ta-form-item",{attrs:{label:"申诉处理人","field-decorator-id":"currPers","label-width":"100px",require:!0}},[i("ta-suggest",{staticStyle:{width:"100%"},attrs:{"data-source":t.peopleList,"table-title-map":new Map([["label",{name:"姓名",style:{minWidth:"120px"}}]]),"option-config":t.optionConfig,"dropdown-match-select-width":!1,"dropdown-style":{width:"300px"}},on:{select:function(e,a){return t.handleChangeSuggest1(e,a)},search:t.handleSearch2},scopedSlots:t._u([{key:"body-cell",fn:function(e){e.cellValue,e.column,e.columnIndex;var a=e.row;e.rowIndex;return[i("span",[t._v(t._s(a.label)+" ("+t._s(a.value)+" "+t._s(a.aae386?a.aae386:"未知科室 "+a.aaz307)+" "+t._s(a.akb021?a.akb021:"未知医院 "+a.akb020)+")")])]}}])})],1)],1)],1)],1)},n=[],r=a(18701),s=a.n(r),l=a(95082),o=a(52582),c=a(36797),u=a.n(c),d=a(10573),p={name:"distribute",components:{Plugin:d.Z},props:{visible:{type:Boolean,default:!1},params:{type:Object,default:function(){return{period:u()()}}}},data:function(){return{title:"下发申诉",dataSource:[],batchLabel:null,modalWin:null,pageUrl:o.Z.getDistributePageUrl(),validRules:{currPersName:[{required:!0,message:"必填"}]},pluginVisible:!1,optionConfig:{value:"value",label:function(e){return"".concat(e.label," (").concat(e.value," ").concat(e.aae386?e.aae386:"未知科室 "+e.aaz307," ").concat(e.akb021?e.akb021:"未知医院 "+e.akb020,")")}},batchModefyVisible:!1,deptList:[],deptList2:[],currCache:new Map,peopleList:[],searchAkb020:"",statusColors:{0:"#f49924",1:"#1890ff",2:"#faad14",3:"#a0d911",4:"#52c41a",5:"#722ed1",6:"#f5222d",7:"#13c2c2",8:"#eb2f96",9:"#bfbfbf",10:"#0050b3",11:"#979797"}}},watch:{visible:{handler:function(e){var t=this;e&&this.$nextTick((function(){t.$refs.xTable.resetColumn(),t.$refs.xTable.clearCheckboxReserve(),t.$refs.xTable.clearCheckboxRow(),"3"===t.params.dataType&&(t.$refs.xTable.hideColumn(t.$refs.xTable.getColumnByField("aae386V")),t.$refs.xTable.hideColumn(t.$refs.xTable.getColumnByField("aac004V")),t.$refs.xTable.hideColumn(t.$refs.xTable.getColumnByField("aae036"))),t.queryTableData()}))}},dataSource:function(e){var t=this;e.forEach((function(e){if(t.currCache.has(e.aaz213)){var a=t.currCache.get(e.aaz213);return e.currPersInShow=a.currPers,void(e.currPersNameInShow=a.currPersName)}if(e.currPers)return e.currPersInShow=e.currPers,void(e.currPersNameInShow=e.currPersName);e.currPersInShow=e.currPersMatch,e.currPersNameInShow=e.currPersNameMatch}))}},mounted:function(){},methods:{moment:u(),currPersStyle:function(e){if(e.row.currPersInShow!==e.row.currPers)return"curr-cell"},onSwichChange:function(e){var t=this;this.$nextTick((function(){t.queryTableData()}))},deptChange:function(e,t){var a=this;this.$nextTick((function(){a.queryTableData()}))},suggestChange:function(e){var t=this;e||this.$nextTick((function(){t.queryTableData()}))},deptSearch:function(e){var t=this;e&&(o.Z.getDeptList((0,l.Z)((0,l.Z)({},this.params),{},{searchVal:e}),(function(e){t.deptList=e.data.list})),o.Z.getDeptList2((0,l.Z)((0,l.Z)({},this.params),{},{searchVal:e}),(function(e){t.deptList2=e.data.list})))},openBatchModefyModel:function(){this.$refs.xTable.getCheckboxReserveRecords().length>0?(this.batchModefyVisible=!0,this.searchAkb020=this.$refs.xTable.getCheckboxReserveRecords()[0].akb020):this.$message.warning("请选择需要修改的数据")},handleBatchModefy:function(){var e=this,t=this.form1.getFieldsValue(),a=this.$refs.xTable.getCheckboxReserveRecords();this.$refs.xTable.getCheckboxRecords();this.form1.validateFields((function(i){i||(a.forEach((function(a){return e.currCache.set(a.aaz213,{currPers:t.currPers,currPersName:e.batchLabel})})),e.closeBatchModefyModel())}))},closeBatchModefyModel:function(){this.form1.resetFields(),this.batchModefyVisible=!1,this.queryTableData()},handleChangeSuggest1:function(e,t){this.batchLabel=t.label},setPopupContainer:function(e){return e.parentNode},handleSearch2:function(e){var t=this;e&&o.Z.queryPeople({doctorName:e,akb020:this.params.enabeledHosArea?this.searchAkb020:"",sign:"restrict"},(function(e){t.peopleList=e.data.list}))},handleSearch:function(e){var t=this;e&&o.Z.queryPeople({doctorName:e,akb020:this.params.enabeledHosArea?this.searchAkb020:"",sign:"restrict"},(function(e){t.peopleList=e.data.list}))},handleChangeSuggest:function(e,t,a,i){e&&(t.currPersInShow=e,t.currPersNameInShow=i.label,this.$refs.xTable.updateStatus({row:t,column:a}),this.currCache.set(t.aaz213,{currPers:e,currPersName:i.label}))},handleCellSelect:function(e){e.row,e.rowIndex,e.$rowIndex,e.column,e.columnIndex,e.$columnIndex},pageParams:function(){var e=this.form.getFieldsValue(),t=this.$refs.gridPager.getPagerInfo();return Object.assign(e,t,this.params),e},queryTableData:function(){var e=this;this.form.validateFields((function(t){t||e.$refs.gridPager.loadData()}))},resetForm:function(){this.form.resetFields()},openPlugin:function(){this.pluginVisible=!0},updatePlugin:function(){this.queryTableData()},closePlugin:function(){this.pluginVisible=!1},handleSave:function(){var e=this.$refs.xTable.getCheckboxReserveRecords();e=e.filter((function(e){return e.currPersInShow!==e.currPers}));var t=e.filter((function(e){return"0"!==e.handStas&&"1"!==e.handStas})).length,a=e.find((function(e){return!e.currPersInShow}));0!==e.length?a?this.$message.warn("存在已勾选但未填写处理人的数据!"):this.popConfirm(e.length,e,"2",t):this.$message.warn("未勾选任何已编辑数据")},preHandleSavleAll:function(){var e=this,t=this.form.getFieldsValue();Object.assign(t,this.params),t.handlePersons=[],this.currCache.forEach((function(e,a){t.handlePersons.push(a)})),o.Z.preSaveCheck(t,(function(a){"0"!==a.data.data.verificationPassed?a.data.data.totalCount?e.popConfirm(a.data.data.totalCount,[],"1",a.data.data.handledCount,t):e.$message.warn("未查询到申述明细数据，请核实！"):e.$message.warn("下发所有数据校验不通过，存在未指定处理人的明细数据")}))},handleSavleAll:function(){},popConfirm:function(e,t,a,i){var n=this,r=this.$createElement,l={color:"blue"},o={on:{click:function(){return n.save("all",t,a)}},props:{type:"primary"}},c={on:{click:function(){return n.save("unhandled",t,a)}},props:{type:"primary",disabled:e===i}};this.modalWin=this.$confirm({icon:!1,closable:!0,content:r("span",["已勾选",e,"条已编辑数据,当前存在",i,"条已处理数据，请选择继续下发类型",r("br"),'如果选择"',r("span",{style:l},["下发全部数据"]),'",系统将保留已添加的申诉理由和申诉材料，只变更任务申诉处理人']),okText:r("span",["仅下发未处理数据"]),okType:"success",cancelText:r("span",["下发全部数据"]),footer:r("div",[r("ta-button",s()([{},o]),["下发全部数据"]),r("ta-button",s()([{},c]),["仅下发未处理数据"])]),onOk:function(){},onCancel:function(){}})},save:function(e,t,a){var i=this;this.$refs.xTable.fullValidate(null,(function(n){var r,s=t.filter((function(t){return"all"===e||"1"===t.handStas||"0"===t.handStas})).map((function(e){return{aaz213:e.aaz213,currPers:e.currPersInShow,handStas:e.handStas,dataType:e.dataType,aae043:e.aae043,aae141:e.aae141}})),c=i.$route.query;c&&c.aaz263&&(r=c.aaz263);var u=i.form.getFieldsValue();Object.assign(u,i.params),u.handlePersons=[],i.currCache.forEach((function(e,t){u.handlePersons.push(t+"-"+e.currPers)})),o.Z.updateCurrPers((0,l.Z)((0,l.Z)({},i.params),{},{aae043:i.dataSource[0].aae043,updateList:s,saveScope:e,auditPerson:r,dispatchType:a,appealQueryVo:u}),(function(e){i.$message.success("保存成功"),i.modalWin.destroy(),i.$emit("handleClose")}))}))},handleClose:function(){this.pluginVisible=!1,this.batchModefyVisible=!1,this.currCache=new Map,this.peopleList=[],this.$refs.xTable.clearCheckboxReserve(),this.$emit("handleClose")}}},h=p,f=a(1001),m=(0,f.Z)(h,i,n,!1,null,"f5a57f5c",null),g=m.exports},10573:function(e,t,a){a.d(t,{Z:function(){return f}});var i=function(){var e=this,t=this,a=t.$createElement,i=t._self._c||a;return i("ta-modal",{attrs:{title:t.title,visible:t.visible,height:700,width:1e3},on:{ok:t.handleSave,cancel:t.handleClose}},[i("ta-alert",{directives:[{name:"show",rawName:"v-show",value:t.firstTime,expression:"firstTime"}],attrs:{message:"提示：请先进行下发配置，保存后可直接进入首次下发页面",banner:""}}),i("ta-row",{staticClass:"w100Box1"},[i("ta-col",{attrs:{span:"2"}},[i("span",{staticClass:"t1"},[t._v("下发方式：")])]),i("ta-col",[i("ta-radio-group",{attrs:{value:t.partType,options:t.options,"default-value":"0"},on:{change:t.changePartType}})],1)],1),i("ta-row",{staticClass:"w100Box1"},[i("ta-col",{attrs:{span:2}},[i("span",{staticClass:"t1"},[t._v("详细配置：")])]),i("ta-col",{attrs:{span:20}},["1"===t.partType?i("ta-checkbox",{attrs:{checked:t.detailChecked},on:{change:t.changeDetailChecked}},[t._v(" 没有开单医生时，下发给科室指定人员 ")]):t._e(),i("ta-row",{directives:[{name:"show",rawName:"v-show",value:"1"===t.partType&&t.detailChecked||"2"===t.partType,expression:"(partType === '1'&&detailChecked)||partType==='2'"}]},[i("ta-col",{attrs:{span:11}},[t.hosList.length>1?i("ta-tabs",{attrs:{activeKey:t.currentHospital},on:{change:t.tabKeyChange}},t._l(t.hosList,(function(e){return i("ta-tab-pane",{key:e.hospitalId,attrs:{tab:e.hospitalName}})})),1):t._e(),i("ta-card",{attrs:{bordered:"","body-style":{lineHeight:"30px"}}},[i("div",{staticStyle:{display:"flex","justify-content":"space-between"},attrs:{slot:"title"},slot:"title"},[i("ta-checkbox",{attrs:{indeterminate:t.indeterminate,checked:t.checkAll},on:{change:t.onCheckAllChange}},[t._v(" "+t._s(t.checkedList.length||0)+"/"+t._s(t.deptList.length||0)+"项 ")]),i("span",{staticClass:"t1"},[t._v("科室列表")])],1),i("ta-input-search",{staticStyle:{width:"100%"},attrs:{placeholder:""},on:{search:t.filterDeptList,change:function(e){return t.filterDeptList(e.target.value)}}}),i("ta-checkbox-group",{staticClass:"checkBoxList",attrs:{value:t.checkedList},on:{change:t.onChange}},t._l(t.deptList,(function(e){return i("ta-checkbox",{key:e.departmentId,attrs:{value:e.departmentId}},[i("span",[t._v(t._s(e.departmentName))]),i("span",{staticStyle:{float:"right",color:"#888888"}},[t._v(t._s(e.doctorName))])])})),1)],1)],1),i("ta-col",{attrs:{span:1}}),i("ta-col",{attrs:{span:12}},[i("ta-form",{attrs:{"auto-form-create":function(t){return e.form=t},layout:"horizontal","form-layout":!0,"label-width":"80px",col:{xs:1,sm:1,md:1,lg:1,xl:1,xxl:1}}},[i("ta-form-item",{staticStyle:{height:"120px"},attrs:{label:"已选科室","field-decorator-id":"orgId"}},[i("div",{staticStyle:{overflow:"scroll","text-overflow":"ellipsis",height:"110px"}},[t._v(t._s(t.deptNames))])]),i("ta-form-item",{attrs:{label:"申诉处理人","field-decorator-id":"doctorId",require:!0,"label-width":"100px",span:22}},[i("ta-suggest",{staticStyle:{width:"100%"},attrs:{"data-source":t.peopleList,"table-title-map":new Map([["label",{name:"人员姓名",style:{minWidth:"320px"}}]]),"option-config":t.optionConfig,"dropdown-match-select-width":!1},on:{select:function(e,a){return t.handleChangeSuggest(e,a)},search:t.handleSearch},scopedSlots:t._u([{key:"body-cell",fn:function(e){e.cellValue,e.column,e.columnIndex;var a=e.row;e.rowIndex;return[i("span",[t._v(t._s(a.label)+" ("+t._s(a.value)+" "+t._s(a.aae386?a.aae386:"未知科室 "+a.aaz307)+" "+t._s(a.akb021?a.akb021:"未知医院 "+a.akb020)+")")])]}}])})],1),i("ta-form-item",{attrs:{label:"已选科室","field-decorator-id":"doctorName",hidden:!0}},[i("ta-input")],1),i("ta-form-item",{attrs:{label:"","field-decorator-id":"","label-width":10,span:2}},[i("ta-button",{attrs:{type:"primary"},on:{click:t.setPeopleForDept}},[t._v(" 确定 ")])],1)],1)],1)],1)],1)],1)],1)},n=[],r=a(89584),s=a(36797),l=a.n(s),o=a(52582),c=[{value:"1",label:"下发给开单医生"},{value:"2",label:"下发给指定人员"}],u={name:"plugin",components:{},props:{visible:{type:Boolean,default:!1},firstTime:{type:Boolean,default:!1}},data:function(){return{title:"下发配置",partType:"1",options:c,optionConfig:{value:"value",label:function(e){return"".concat(e.label," (").concat(e.value," ").concat(e.aae386?e.aae386:"未知科室 "+e.aaz307," ").concat(e.akb021?e.akb021:"未知医院 "+e.akb020,")")}},detailChecked:!1,indeterminate:!1,checkAll:!1,checkedList:[],hosList:[],currentHospital:null,deptList:[],hospitalMap:new Map,deptListOrigin:[],peopleList:[]}},computed:{deptNames:function(){var e=this,t=this.deptListOrigin.filter((function(t){return e.checkedList.indexOf(t.departmentId)>-1})).map((function(e){return e.departmentName})).join(",");return t}},watch:{visible:{handler:function(e){var t=this;e&&this.$nextTick((function(){o.Z.getPluginSetting({},(function(e){t.hosList=e.data.dispatchCfg.cfgList,t.currentHospital=e.data.dispatchCfg.cfgList[0].hospitalId,t.hosList.forEach((function(e){t.hospitalMap.set(e.hospitalId,e.detailCfg)})),t.deptList=e.data.dispatchCfg.cfgList[0].detailCfg,t.deptListOrigin=(0,r.Z)(t.deptList),"0"===e.data.dispatchCfg.dispatchMode?t.partType="2":"1"===e.data.dispatchCfg.dispatchMode?t.partType="1":(t.partType="1",t.detailChecked=!0)}))}))}}},methods:{tabKeyChange:function(e){this.currentHospital=e,this.deptList=this.hospitalMap.get(e),this.deptListOrigin=(0,r.Z)(this.deptList),this.checkedList=[],this.form.resetFields("doctorId")},moment:l(),onChange:function(e){e.length<this.deptList.length&&0!==e.length?(this.checkAll=!1,this.indeterminate=!0):0===e.length?(this.checkAll=!1,this.indeterminate=!1):(this.checkAll=!0,this.indeterminate=!1),this.checkedList=e},onCheckAllChange:function(e){var t=e.target.checked;t?(this.checkAll=!0,this.checkedList=this.deptList.map((function(e){return e.departmentId}))):(this.checkAll=!1,this.checkedList=[]),this.indeterminate=!1},filterDeptList:function(e){var t=this.checkedList;if(e){this.deptList=this.deptListOrigin.filter((function(t){return t.departmentName.indexOf(e)>-1}));var a=this.deptList.map((function(e){return e.departmentId}));t=this.checkedList.filter((function(e){return a.indexOf(e)>-1}))}else this.deptList=JSON.parse(JSON.stringify(this.deptListOrigin));this.onChange(t)},changePartType:function(e){this.partType=e.target.value},changeDetailChecked:function(e){this.detailChecked=e.target.checked},handleSearch:function(e){var t=this;e&&o.Z.queryPeople({doctorName:e,akb020:this.currentHospital},(function(e){t.peopleList=e.data.list}))},handleChangeSuggest:function(e,t){this.form.setFieldsValue({doctorName:t.label})},setPeopleForDept:function(){var e=this;this.checkedList.length<=0?this.$message.warn("至少选择一个科室进行指定人员设置"):this.form.validateFields((function(t){if(!t){var a=e.form.getFieldsValue();e.deptListOrigin=e.deptListOrigin.map((function(t){return e.checkedList.indexOf(t.departmentId)>-1&&(t.doctorId=a.doctorId,t.doctorName=a.doctorName),t})),e.deptList=e.deptList.map((function(t){return e.checkedList.indexOf(t.departmentId)>-1&&(t.doctorId=a.doctorId,t.doctorName=a.doctorName),t}))}}))},handleSave:function(){var e=this,t={};t.dispatchMode="2"===this.partType?"0":!0===this.detailChecked?"2":"1",t.cfgList=this.hosList,t.doctorId=this.$route.query.aaz263,o.Z.savePluginSetting(t,(function(t){e.handleClose(),e.$emit("handleOk")}))},handleClose:function(){this.detailChecked=!1,this.partType="1",this.deptList=[],this.deptListOrigin=[],this.peopleList=[],this.indeterminate=!1,this.checkAll=!1,this.checkedList=[],this.$emit("handleClose")}}},d=u,p=a(1001),h=(0,p.Z)(d,i,n,!1,null,"c90034ac",null),f=h.exports},39101:function(e,t,a){var i=a(50178),n=a(57665),r=a(1001),s=(0,r.Z)(n.Z,i.s,i.x,!1,null,"8c2b81f0",null);t["Z"]=s.exports},36766:function(e,t,a){var i=a(66586);t["Z"]=i.Z},57665:function(e,t,a){var i=a(38962);t["Z"]=i.Z},26263:function(e,t,a){a.d(t,{s:function(){return i},x:function(){return n}});var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:e.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[e._v(e._s(e.title))]),e._t("default")],2)},n=[]},50178:function(e,t,a){a.d(t,{s:function(){return i},x:function(){return n}});var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("ta-modal",{attrs:{title:e.title,visible:e.visible,height:460,width:850},on:{cancel:e.handleClose}},[a("div",{staticStyle:{height:"100%"}},[a("span",{staticClass:"labelTitle",staticStyle:{"font-size":"12px",color:"#989898"}},[e._v("根据手动匹配算法进行匹配，可能非精准匹配。")]),a("ta-border-layout",{attrs:{"show-border":!1,"header-cfg":{showBorder:!1}}},[a("div",[a("ta-big-table",{ref:"viewRecordTable",attrs:{height:"380px","auto-resize":"",resizable:"","highlight-hover-row":"",border:"",align:"center","header-align":"center",size:"mini",data:e.viewRecordData}},[a("ta-big-table-column",{attrs:{type:"seq",fixed:"left",width:"50px",title:"序号",align:"center"}}),a("ta-big-table-column",{attrs:{field:"aae040",align:"center",sortable:"","show-overflow":"",title:"审核时间","min-width":"100px"}}),a("ta-big-table-column",{attrs:{field:"ape800",align:"center",sortable:"","show-overflow":"",title:"审核结果","min-width":"100px"},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.row;return["0"==i.ape800?a("span",{staticStyle:{color:"#0f990f"}},[a("ta-icon",{attrs:{type:"check-circle",theme:"twoTone",twoToneColor:"#52c41a"}}),e._v("审核通过")],1):"1"==i.ape800?a("span",{staticStyle:{color:"#F59A23"}},[a("ta-icon",{attrs:{type:"question-circle",theme:"twoTone",twoToneColor:"#F59A23"}}),e._v("可疑提醒")],1):"2"==i.ape800?a("span",{staticStyle:{color:"#FF0000"}},[a("ta-icon",{attrs:{type:"exclamation-circle",theme:"twoTone",twoToneColor:"#FF0000"}}),e._v("违规提醒")],1):"3"==i.ape800?a("span",{staticStyle:{color:"#ffc49a"}},[a("ta-icon",{attrs:{type:"bell",theme:"twoTone",twoToneColor:"#e1ba91"}}),e._v("仅提醒")],1):a("div",[e._v(" — ")])]}}])}),a("ta-big-table-column",{attrs:{field:"ape893",align:"center","show-overflow":"",title:"医护操作","min-width":"80px"}},[a("ta-big-table-column-filter",{attrs:{"filter-props":{placeholder:""}}})],1),a("ta-big-table-column",{attrs:{field:"ykz041",align:"center","show-overflow":"",title:"操作人","min-width":"80px"}},[a("ta-big-table-column-filter",{attrs:{"filter-props":{placeholder:""}}})],1),a("template",{slot:"bottomBar"},[a("ta-pagination",{ref:"gridPager",staticStyle:{"text-align":"right"},attrs:{"default-page-size":500,"hide-on-single-page":!0,"page-size-options":["30","50","100","200","500"],"data-source":e.viewRecordData,params:e.infoPageParams,url:"hiddscgPoint/getViewRecordData"},on:{"update:dataSource":function(t){e.viewRecordData=t},"update:data-source":function(t){e.viewRecordData=t}}})],1)],2)],1)])],1),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("div",{staticStyle:{display:"flex","justify-content":"center"}},[a("ta-button",{staticStyle:{"text-align":"center"},on:{click:e.handleClose}},[e._v(" 关闭 ")])],1)])])},n=[]},66586:function(e,t){var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};t["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}},38962:function(e,t,a){a(52582);t["Z"]={name:"viewRecord",props:{title:{type:String,default:"匹配预警记录"},visible:{type:Boolean,default:!1},param:{type:Object,default:function(){return{}}}},data:function(){return{viewRecordData:[]}},watch:{visible:{handler:function(e){var t=this;e&&this.$nextTick((function(){t.$refs.gridPager.loadData()}))}}},mounted:function(){},methods:{infoPageParams:function(){return this.param},handleClose:function(){this.viewRecordData=[],this.$emit("handleClose")}}}},52582:function(e,t,a){var i=a(95082),n=a(95278),r="appeal/hidAppeal/",s="appeal/drPoint/";t["Z"]={getPageUrl:function(){return r+"queryListByPage"},getBasePath:function(){return n.Z.basePath},getAdmissionNumList:function(e,t){Base.submit(null,{url:"appeal/common/fuzzySearchInKf38/patientId",data:(0,i.Z)((0,i.Z)({},e),{},{igronCurrPer:!0})},{successCallback:function(e){return t(e)}})},getDeptList:function(e,t){Base.submit(null,{url:"appeal/common/fuzzySearchInKf38/OprDepartName",data:(0,i.Z)((0,i.Z)({},e),{},{igronCurrPer:!0})},{successCallback:function(e){return t(e)}})},getDeptList2:function(e,t){Base.submit(null,{url:"appeal/common/fuzzySearchInKf38/departName",data:(0,i.Z)((0,i.Z)({},e),{},{igronCurrPer:!0})},{successCallback:function(e){return t(e)}})},getCostTypeList:function(e,t){Base.submit(null,{url:"appeal/common/fuzzySearchInKf38/costType",data:(0,i.Z)((0,i.Z)({},e),{},{igronCurrPer:!0})},{successCallback:function(e){return t(e)}})},getRuleNameList:function(e,t){Base.submit(null,{url:"appeal/common/fuzzySearchInKf38/ruleName",data:(0,i.Z)((0,i.Z)({},e),{},{igronCurrPer:!0})},{successCallback:function(e){return t(e)}})},getPatintNameList:function(e,t){Base.submit(null,{url:"appeal/common/fuzzySearchInKf38/patientName",data:(0,i.Z)((0,i.Z)({},e),{},{igronCurrPer:!0})},{successCallback:function(e){return t(e)}})},getDoctorList:function(e,t){Base.submit(null,{url:"appeal/common/fuzzySearchInKf38/doctorName",data:(0,i.Z)((0,i.Z)({},e),{},{igronCurrPer:!0})},{successCallback:function(e){return t(e)}})},getObjNameList:function(e,t){Base.submit(null,{url:"appeal/common/fuzzySearchInKf38/itemName",data:(0,i.Z)((0,i.Z)({},e),{},{igronCurrPer:!0})},{successCallback:function(e){return t(e)}})},queryPeople:function(e,t){Base.submit(null,{url:"/appeal/common/getDicDoctor",data:e},{successCallback:function(e){return t(e)}})},queryHandledCount:function(e,t){Base.submit(null,{url:r+"queryHandledCount",data:e},{successCallback:function(e){return t(e)}})},getPluginSetting:function(e,t){Base.submit(null,{url:r+"getDispatchCfg",data:e},{successCallback:function(e){return t(e)}})},savePluginSetting:function(e,t){Base.submit(null,{url:r+"saveDispatchCfg",data:e,autoQs:!1},{successCallback:function(e){return t(e)}})},isPluginSetted:function(e,t){Base.submit(null,{url:r+"checkDispatchCfg",data:e},{successCallback:function(e){return t(e)}})},oneModefy:function(e,t){Base.submit(null,{url:r+"cacheDispatchRecord",data:e},{successCallback:function(e){return t(e)}})},batchModefy:function(e,t){Base.submit(null,{url:r+"cacheDispatchRecordBatch",data:e,autoQs:!1},{successCallback:function(e){return t(e)}})},distributeSave:function(e,t){Base.submit(null,{url:r+"dispatchAppeal",data:e},{successCallback:function(e){return t(e)}})},updateCurrPers:function(e,t){Base.submit(null,{url:r+"updateCurrPers",data:e,autoQs:!1},{successCallback:function(e){return t(e)}})},getDistributePageUrl:function(){return r+"getDispatchData"},reOneModefy:function(e,t){Base.submit(null,{url:r+"cacheRedispatchRecord",data:e},{successCallback:function(e){return t(e)}})},reBatchModefy:function(e,t){Base.submit(null,{url:r+"cacheReDispatchRecordBatch",data:e,autoQs:!1},{successCallback:function(e){return t(e)}})},reDistributeSave:function(e,t){Base.submit(null,{url:r+"redispatchAppeal",data:e},{successCallback:function(e){return t(e)}})},batchApproval:function(e,t){Base.submit(null,{url:r+"auditAppealBatch",data:e,autoQs:!1},{successCallback:function(e){return t(e)}})},singelApproval:function(e,t){Base.submit(null,{url:r+"auditAppeal",data:e,autoQs:!1,isFormData:!0},{successCallback:function(e){return t(e)}})},queryAae043List:function(e,t){Base.submit(null,{url:r+"queryAae043List",data:e},{successCallback:function(e){return t(e)}})},getFileList:function(e,t){Base.submit(null,{url:"appeal/drPoint/queryAppealDetail",data:e},{successCallback:function(e){return t(e)}})},getPicZipUrl:function(){return n.Z.basePath+"/appeal/drPoint/fileZipDownload"},exportZip:function(){return n.Z.basePath+"/appeal/hidAppeal/exportZip"},getFileInfo:function(e,t){Base.submit(null,{url:s+"getFileInfos",data:e},{successCallback:function(e){return t(e)}})},preSaveCheck:function(e,t){Base.submit(null,{url:r+"preSaveCheck",data:e,autoQs:!1},{successCallback:function(e){return t(e)}})},reBackRow:function(e,t){Base.submit(null,{url:r+"cancelSubmitAppeal",data:e},{successCallback:function(e){return t(e)}})}}},7485:function(e,t,a){a.d(t,{H:function(){return i}});var i=[{value:"1",label:"待审批",checked:!0,color1:"orange",color2:"#FCB76B"},{value:"2",label:"审批通过未导出",checked:!1,color1:"green",color2:"#87d068"},{value:"3",label:"审批通过已导出",checked:!1,color1:"purple",color2:"#7d02d1"},{value:"4",label:"审批驳回待提交",checked:!1,color1:"red",color2:"#fa0000"},{value:"5",label:"全部",checked:!1,color1:"blue",color2:"#2db7f5"}]}}]);
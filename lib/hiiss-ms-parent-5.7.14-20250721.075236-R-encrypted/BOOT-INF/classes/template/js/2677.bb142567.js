"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[2677],{77146:function(t,e,r){r.r(e),r.d(e,{default:function(){return F}});var i=function(){var t=this,e=this,r=e.$createElement,i=e._self._c||r;return i("div",{staticClass:"fit"},[i("ta-border-layout",{staticStyle:{},attrs:{"layout-type":"fixTop"}},[i("div",{staticStyle:{"padding-top":"10px"},attrs:{slot:"header"},slot:"header"},[i("div",{staticClass:"query-header"},[i("div",{ref:"formBox",staticClass:"query-header-left",style:e.formBoxStyle},[i("ta-form",{attrs:{autoFormCreate:function(e){t.baseInfoForm=e},layout:"horizontal","label-width":"100px",formLayout:!0}},[i("ta-form-item",{attrs:{fieldDecoratorId:"allDate",span:6,label:"结算时间","init-value":e.rangeValue,fieldDecoratorOptions:{rules:[{required:!0,message:"必须选取日期"}]}}},[i("ta-range-picker",{attrs:{"allow-one":!0,type:"month"},on:{change:e.onChange}})],1),i("ta-form-item",{attrs:{fieldDecoratorId:"aaz307",label:"科室名称"}},[i("ta-select",{attrs:{"show-search":!0,allowClear:"",placeholder:"科室名称筛选",options:e.ksList}})],1),i("div",{staticStyle:{display:"flex","margin-left":"20px",float:"right"}},[i("ta-button",{attrs:{type:"primary",icon:"search"},on:{click:e.fnQuery}},[e._v("查询")]),i("ta-button",{staticStyle:{"margin-left":"15px"},attrs:{icon:"redo"},on:{click:e.fnReSet}},[e._v("重置")])],1)],1)],1)])]),i("div",{staticClass:"fit content-box"},[i("div",{staticClass:"content-table"},[i("ta-big-table",{ref:"infoTableRef",attrs:{columns:e.infoColumns,data:e.infoTableData,border:"",height:"auto","highlight-hover-row":"","auto-resize":"","show-overflow":"",resizable:""}})],1),i("div",{staticClass:"content-box-footer"},[i("ta-pagination",{ref:"infoPageRef",staticStyle:{position:"absolute",right:"100px",bottom:"6px"},attrs:{dataSource:e.infoTableData,params:e.infoPageParams,defaultPageSize:100,pageSizeOptions:["100","200","500","1000","1500"],url:"indexAnalysis/queryData"},on:{"update:dataSource":function(t){e.infoTableData=t},"update:data-source":function(t){e.infoTableData=t}}}),i("ta-button",{staticStyle:{position:"absolute",right:"10px",bottom:"6px"},attrs:{icon:"download",type:"primary"},on:{click:e.fnExportExcel}},[e._v("导出")])],1)])])],1)},a=[],l=r(66347),o=r(48534),f=r(95082),n=(r(36133),r(88412)),s=r(362),h=r(36797),m=r.n(h),d=r(22722),g=r(55115);r(50451),r(83231);g.w3.prototype.Base=Object.assign(g.w3.prototype.Base,(0,f.Z)({},d.Z));var c=[],b={name:"indexAnalysis",components:{TaTitle:n.Z,atientDetails:s["default"]},data:function(){var t=[{type:"seq",title:"序号",width:"60",align:"center"},{title:"科室名称",width:"200",field:"aae386",align:"center",sortable:!0},{title:"住院总费用",width:"150",field:"zyzfy",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"住院总费用次均",width:"150",field:"zyzfycj",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"住院药品费",width:"150",field:"zyypf",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"住院药品费次均",width:"150",field:"zyypfcj",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"住院药品费占比",width:"150",field:"zyypfzb",align:"right",sortable:!0,formatter:this.fnFormatterPer},{title:"住院诊疗费",width:"150",field:"zyzlf",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"住院诊疗费次均",width:"150",field:"zyzlfcj",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"住院诊疗费占比",width:"150",field:"zyzlfzb",align:"right",sortable:!0,formatter:this.fnFormatterPer},{title:"住院检查检验费",width:"150",field:"zyjcjyf",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"住院检查检验费次均",width:"170",field:"zyjcjyfcj",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"住院检查检验占比",width:"170",field:"zyjcjyzb",align:"right",sortable:!0,formatter:this.fnFormatterPer},{title:"住院材料费",width:"150",field:"zyclf",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"住院材料费次均",width:"150",field:"zyclfcj",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"住院材料费占比",width:"150",field:"zyclfzb",align:"right",sortable:!0,formatter:this.fnFormatterPer},{title:"住院医保支付费",width:"150",field:"zyybzff",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"住院医保支付费次均",width:"170",field:"zyybzffcj",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"住院医保支付费占比",width:"170",field:"zyybzffzb",align:"right",sortable:!0,formatter:this.fnFormatterPer},{title:"住院统筹支付",width:"150",field:"zytczf",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"住院统筹支付次均",width:"170",field:"zytczfcj",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"住院统筹支付占比",width:"170",field:"zytczfzb",align:"right",sortable:!0,formatter:this.fnFormatterPer},{title:"住院个人负担",width:"150",field:"zygrfd",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"住院个人负担次均",width:"170",field:"zygrfdcj",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"住院个人负担占比",width:"170",field:"zygrfdzb",align:"right",sortable:!0,formatter:this.fnFormatterPer},{title:"住院大额医疗",width:"150",field:"zydeyl",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"住院大额医疗次均",width:"170",field:"zydeylcj",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"住院大额医疗占比",width:"170",field:"zydeylzb",align:"right",sortable:!0,formatter:this.fnFormatterPer},{title:"住院统筹支付日均",width:"170",field:"zytczfrj",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"住院人头人次比",width:"150",field:"zyrtrcb",align:"right",sortable:!0,formatter:this.fnFormatterPer},{title:"次均住院天数",width:"150",field:"cjzyts",align:"right",sortable:!0,formatter:this.fnFormatterPer},{title:"住院总人次",width:"150",field:"zyzrc",align:"right",sortable:!0,formatter:this.fnFormatterPer},{title:"自费费用",width:"150",field:"zffy",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"自费率",width:"150",field:"zfl",align:"right",sortable:!0,formatter:this.fnFormatterPer},{title:"个人支付",width:"150",field:"grzf",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"人均个人支付",width:"150",field:"rjgrzf",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"个人支付率",width:"150",field:"grzfl",align:"right",sortable:!0,formatter:this.fnFormatterPer}],e=[{type:"seq",title:"序号",width:"60",align:"center"},{title:"科室名称",width:"200",field:"aae386",align:"center",sortable:!0},{title:"门诊总费用",width:"150",field:"mzzfy",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"门诊总费用次均",width:"150",field:"mzzfycj",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"门诊药品费",width:"150",field:"mzypf",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"门诊药品费次均",width:"150",field:"mzypfcj",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"门诊药品费占比",width:"150",field:"mzypfzb",align:"right",sortable:!0,formatter:this.fnFormatterPer},{title:"门诊诊疗费",width:"150",field:"mzzlf",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"门诊诊疗费次均",width:"150",field:"mzzlfcj",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"门诊诊疗费占比",width:"150",field:"mzzlfzb",align:"right",sortable:!0,formatter:this.fnFormatterPer},{title:"门诊检查检验费",width:"150",field:"mzjcjyf",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"门诊检查检验费次均",width:"170",field:"mzjcjyfcj",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"门诊检查检验费占比",width:"170",field:"mzjcjyfzb",align:"right",sortable:!0,formatter:this.fnFormatterPer},{title:"门诊材料费",width:"150",field:"mzclf",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"门诊材料费次均",width:"150",field:"mzclfcj",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"门诊材料费占比",width:"150",field:"mzclfzb",align:"right",sortable:!0,formatter:this.fnFormatterPer},{title:"门诊医保支付",width:"150",field:"mzybzf",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"门诊医保支付次均",width:"170",field:"mzybzfcj",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"门诊医保支付占比",width:"170",field:"mzybzfzb",align:"right",sortable:!0,formatter:this.fnFormatterPer},{title:"门诊统筹支付",width:"150",field:"mztczf",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"门诊统筹支付次均",width:"170",field:"mztczfcj",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"门诊统筹支付占比",width:"170",field:"mztczfzb",align:"right",sortable:!0,formatter:this.fnFormatterPer},{title:"门诊个人负担",width:"150",field:"mzgrfd",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"门诊个人负担次均",width:"170",field:"mzgrfdcj",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"门诊个人负担占比",width:"170",field:"mzgrfdzb",align:"right",sortable:!0,formatter:this.fnFormatterPer},{title:"门诊大额医疗",width:"150",field:"mzdeyl",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"门诊大额医疗次均",width:"170",field:"mzdeylcj",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"门诊大额医疗占比",width:"170",field:"mzdeylzb",align:"right",sortable:!0,formatter:this.fnFormatterPer}],r=[{type:"seq",title:"序号",width:"60",align:"center"},{title:"科室名称",width:"200",field:"aae386",align:"center",sortable:!0},{title:"门特总费用",width:"150",field:"mtzfy",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"门特总费用次均",width:"150",field:"mtzfycj",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"门特药品费",width:"150",field:"mtypf",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"门特药品费次均",width:"150",field:"mtypfcj",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"门特药品费占比",width:"150",field:"mtypfzb",align:"right",sortable:!0,formatter:this.fnFormatterPer},{title:"门特诊疗费",width:"150",field:"mtzlf",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"门特诊疗费次均",width:"150",field:"mtzlfcj",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"门特诊疗费占比",width:"150",field:"mtzlfzb",align:"right",sortable:!0,formatter:this.fnFormatterPer},{title:"门特检验检查费",width:"150",field:"mtjcjyf",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"门特检验检查费次均",width:"170",field:"mtjcjyfcj",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"门特检验检查费占比",width:"170",field:"mtjcjyfzb",align:"right",sortable:!0,formatter:this.fnFormatterPer},{title:"门特材料费",width:"150",field:"mtclf",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"门特材料费次均",width:"150",field:"mtclfcj",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"门特材料费占比",width:"150",field:"mtclfzb",align:"right",sortable:!0,formatter:this.fnFormatterPer},{title:"门特医保支付",width:"150",field:"mtybzf",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"门特医保支付次均",width:"170",field:"mtybzfcj",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"门特医保支付占比",width:"170",field:"mtybzfzb",align:"right",sortable:!0,formatter:this.fnFormatterPer},{title:"门特统筹支付",width:"150",field:"mttczf",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"门特统筹支付次均",width:"170",field:"mttczfcj",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"门特统筹支付占比",width:"170",field:"mttczfzb",align:"right",sortable:!0,formatter:this.fnFormatterPer},{title:"门特个人负担",width:"150",field:"mtgrfd",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"门特个人负担次均",width:"170",field:"mtgrfdcj",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"门特个人负担占比",width:"170",field:"mtgrfdzb",align:"right",sortable:!0,formatter:this.fnFormatterPer},{title:"门特医疗大额",width:"150",field:"mtdeyl",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"门特医疗大额次均",width:"170",field:"mtdeylcj",align:"right",sortable:!0,formatter:this.fnFormatter},{title:"门特医疗大额占比",width:"170",field:"mtdeylzb",align:"right",sortable:!0,formatter:this.fnFormatterPer}],i=[];return this.$route.query.pageFlag&&("zy"===this.$route.query.pageFlag?i=t:"mz"===this.$route.query.pageFlag?i=e:"mt"===this.$route.query.pageFlag&&(i=r)),{col:{xs:1,sm:2,md:2,lg:6,xl:6,xxl:4},headers:{authorization:"authorization-text"},rangeValue:[this.Base.getMoment((new Date).toISOString().slice(0,4)+"-01","YYYY-MM"),this.Base.getMoment((new Date).toISOString().slice(0,7),"YYYY-MM")],infoColumns:i,infoTableData:c,formShowAll:!0,showFeeDetail:!1,filterConfig:{remote:!0},sortConfig:{trigger:"default",remote:!0},sortColumn:"",ascOrDesc:"",hasError:!1,akb020:"",aaz307:"",permissions:null,ksList:[],pageFlag:""}},created:function(){return(0,o.Z)(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:case"end":return t.stop()}}),t)})))()},mounted:function(){"zy"===this.$route.query.pageFlag?this.pageFlag="zy":"mz"===this.$route.query.pageFlag?this.pageFlag="mz":"mt"===this.$route.query.pageFlag&&(this.pageFlag="mt"),this.fnQueryDept()},computed:{formBoxStyle:function(){return this.formShowAll?{height:"auto"}:{height:"50px"}}},methods:{fnQueryDept:function(t){var e=this;this.akb020=t||"",this.Base.submit(null,{url:"miimCommonRead/queryDepartRpcList",data:{akb020:this.akb020},autoValid:!1},{successCallback:function(t){e.ksList=t.data.resultData,e.$refs.infoTableRef.setFilter(e.$refs.infoTableRef.getColumnByField("aae386"),e.ksList),e.$refs.infoTableRef.updateData()},failCallback:function(t){e.$message.error("科室数据加载失败")}})},onChange:function(t,e){},disabledStartDate:function(t){return t.format("YYYYMMDD")>this.baseInfoForm.getFieldValue("endDate").format("YYYYMMDD")},disabledEndDate:function(t){return t=t.format("YYYYMMDD"),t>m()().startOf("day").format("YYYYMMDD")||t<this.baseInfoForm.getFieldValue("startDate").format("YYYYMMDD")},infoPageParams:function(){var t=this.baseInfoForm.getFieldsValue();return t.allDate&&(t.allDate[0]&&(t.startDate=t.allDate[0].format("YYYYMM")),t.allDate[1]&&(t.endDate=t.allDate[1].format("YYYYMM"))),t.pageFlag=this.pageFlag,t.sortColumn=this.sortColumn,t.ascOrDesc=this.ascOrDesc,t},sortChangeEvent:function(t){t.column;var e=t.property,r=t.order;this.sortColumn=e,this.ascOrDesc=r,this.fnQuery()},IsInArray:function(t,e){var r=","+t.join(",")+",";return-1!=r.indexOf(","+e+",")},fnQuery:function(){var t=this,e=this.baseInfoForm.getFieldValue("allDate");if(e.length>0&&void 0!=e[0]&&void 0!=e[1]&&e[1].diff(e[0],"months")>=12)return this.$message.error("查询日期范围不能超过12个月"),void(this.hasError=!0);this.baseInfoForm.validateFields(["allDate"],(function(e,r){t.hasError=!!e})),this.hasError||this.$nextTick((function(){t.$refs.infoPageRef.loadData()}))},fnReSet:function(){this.baseInfoForm.resetFields()},fnExportExcel:function(){var t=this;if(this.baseInfoForm.validateFields(["allDate"],(function(e,r){if(e)t.hasError=!0;else{var i=t.baseInfoForm.getFieldValue("allDate");if(i.length>0&&void 0!=i[0]&&void 0!=i[1]&&i[1].diff(i[0],"months")>=12)return t.$message.error("查询日期范围不能超过12个月"),void(t.hasError=!0);t.hasError=!1}})),!this.hasError){var e,r=this.infoPageParams(),i=[],a=this.$refs.infoTableRef.getColumns(),o=(0,l.Z)(a);try{for(o.s();!(e=o.n()).done;){var f=e.value;"seq"!==f.type&&i.push({header:f.title,key:f.property,width:20})}}catch(s){o.e(s)}finally{o.f()}var n=[];this.Base.submit(null,{url:"indexAnalysis/exportExcel",data:r,autoValid:!1},{successCallback:function(e){var r={fileName:"指标分析统计表",sheets:[{name:"worksheet1",column:{complex:!1,columns:i},rows:e.data.exportData,codeList:n}]};t.Base.generateExcel(r)},failCallback:function(e){t.$message.error("指标分析数据导出失败")}})}},fnCustomHeaderCell:function(t){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px",fontWeight:"bold"}}},fnFormatter:function(t){var e=t.cellValue;return e?e+"元":"--"},fnFormatterPer:function(t){var e=t.cellValue;return e||"--"}}},u=b,z=r(1001),y=(0,z.Z)(u,i,a,!1,null,"239552b3",null),F=y.exports}}]);
(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[1058,362,9624],{88412:function(t,a,e){"use strict";var i=e(26263),l=e(36766),r=e(1001),n=(0,r.Z)(l.Z,i.s,i.x,!1,null,"5e7ef0ae",null);a["Z"]=n.exports},71840:function(t,a,e){"use strict";e.r(a),e.d(a,{default:function(){return y}});var i=function(){var t=this,a=this,e=a.$createElement,i=a._self._c||e;return i("div",{staticClass:"fit"},[i("ta-border-layout",{staticStyle:{},attrs:{"layout-type":"fixTop"}},[i("div",{attrs:{slot:"header"},slot:"header"},[i("div",{staticClass:"query-header"},[i("div",{ref:"formBox",staticClass:"query-header-left",style:a.formBoxStyle},[i("ta-form",{attrs:{"auto-form-create":function(a){t.queryForm=a},col:a.col,layout:"horizontal","label-width":"80px",formLayout:!0}},[i("ta-form-item",{attrs:{span:6,label:"查询类型","init-value":"1","field-decorator-id":"selectType",require:{message:"请选择查询类型!"}}},[i("ta-select",{attrs:{options:a.selectTypeList},on:{change:a.fnChangeSelectType}})],1),i("ta-form-item",{attrs:{span:6,label:"审核时间","init-value":a.rangeValue,"field-decorator-id":"auditTime",require:{message:"请选择审核时间范围!"}}},[i("ta-range-picker",{attrs:{"allow-one":!0}})],1),i("ta-form-item",{attrs:{span:6,label:"审核场景","init-value":"5","field-decorator-id":"aae500",require:{message:"请选择审核场景!"}}},[i("ta-select",{attrs:{placeholder:"请选择审核场景","collection-type":"AAE500","collection-filter":a.filterList,reverseFilter:!0,allowClear:""}})],1),i("ta-form-item",{attrs:{span:6,label:"医保类型","field-decorator-id":"aae141"}},[i("ta-select",{attrs:{"allow-clear":!0,"collection-type":"AAE141",placeholder:"请选择医保类型"}})],1),i("ta-form-item",{attrs:{span:6,label:"规则期号","field-decorator-id":"aae043"}},[i("ta-input",{attrs:{"allow-clear":!0,placeholder:"请输入规则期号"}})],1),i("ta-form-item",{attrs:{span:6,label:"审核结果","field-decorator-id":"aae101"}},[i("ta-select",{attrs:{"allow-clear":!0,options:a.aae101List,placeholder:"请选择审核结果"}})],1),i("ta-form-item",{attrs:{span:6,label:"登记号","field-decorator-id":"akc192"}},[i("ta-input",{attrs:{"allow-clear":!0,placeholder:"请输入登记号"}})],1),i("ta-form-item",{attrs:{span:6,label:"就诊号","field-decorator-id":"akc191"}},[i("ta-input",{attrs:{"allow-clear":!0,placeholder:"请输入就诊号"}})],1),i("ta-form-item",{attrs:{span:6,label:"患者姓名","field-decorator-id":"aac003","label-width":85}},[i("ta-input",{attrs:{"allow-clear":!0,placeholder:"可模糊匹配"}})],1),i("ta-form-item",{attrs:{span:6,"label-width":"100px",label:"医保项目编码","field-decorator-id":"ake001"}},[i("ta-input",{attrs:{"allow-clear":!0,placeholder:"请输入医保项目编码"}})],1),i("ta-form-item",{attrs:{span:6,"label-width":"100px",label:"医保项目名称","field-decorator-id":"ake002"}},[i("ta-input",{attrs:{"allow-clear":!0,placeholder:"可模糊匹配"}})],1),i("ta-form-item",{attrs:{span:6,"label-width":"100px",label:"院内项目名称","field-decorator-id":"ake006"}},[i("ta-input",{attrs:{"allow-clear":!0,placeholder:"可模糊匹配"}})],1),i("ta-form-item",{attrs:{span:6,label:"规则序号","field-decorator-id":"ykz032"}},[i("ta-input",{attrs:{"allow-clear":!0,placeholder:"可模糊匹配"}})],1),i("ta-form-item",{attrs:{span:6,label:"限制条件","field-decorator-id":"ykz018"}},[i("ta-input",{attrs:{"allow-clear":!0,placeholder:"可模糊匹配"}})],1),i("ta-form-item",{attrs:{span:6,label:"审核批次号","field-decorator-id":"aze001"}},[i("ta-input",{attrs:{"allow-clear":!0,placeholder:"预审时输入"}})],1)],1)],1),i("div",{staticStyle:{display:"flex","margin-left":"10px",float:"right"}},[i("div",{staticClass:"ctrl-btn",on:{click:a.formShowAllChange}},[i("a",[a._v(a._s(a.formShowAll?"收起":"展开"))]),a.formShowAll?i("ta-icon",{attrs:{type:"caret-up"}}):i("ta-icon",{attrs:{type:"caret-down"}})],1),i("ta-button",{attrs:{type:"primary",icon:"search"},on:{click:a.fnQuery}},[a._v("查询")]),i("ta-button",{attrs:{type:"default",icon:"redo"},on:{click:a.fnReset}},[a._v("重置")])],1)])]),i("div",{staticClass:"fit content-box"},[i("ta-title",{attrs:{title:"查询结果"}}),i("div",{staticClass:"content-table"},[i("ta-big-table",{ref:"dataTable",attrs:{data:a.tableData,border:!0,size:"small",resizable:"","highlight-hover-row":"",height:"auto","auto-resize":"",stripe:"","header-align":"center","export-config":{},"import-config":{},border:"","highlight-current-row":"","show-overflow":""}},[i("ta-big-table-column",{attrs:{type:"seq",title:"序号",width:"3%",align:"center"}}),i("ta-big-table-column",{attrs:{field:"aae500",title:"审核场景",width:"6%","collection-type":"AAE500"}}),i("ta-big-table-column",{attrs:{field:"akc192",title:"登记号",width:"6%"}}),i("ta-big-table-column",{attrs:{field:"akc191",title:"就诊号",width:"6%"}}),i("ta-big-table-column",{attrs:{field:"aac003",title:"姓名",width:"6%"}}),i("ta-big-table-column",{attrs:{field:"aac004",title:"性别",width:"4%","collection-type":"AAC004"}}),i("ta-big-table-column",{attrs:{field:"aae141",title:"医保类型",width:"5%","collection-type":"AAE141"}}),i("ta-big-table-column",{attrs:{field:"ake001",title:"医保项目编码",width:"12%"}}),i("ta-big-table-column",{attrs:{field:"ake002",title:"医保项目名称",width:"10%"}}),i("ta-big-table-column",{attrs:{field:"akc515",title:"院内项目编码",width:"7%"}}),i("ta-big-table-column",{attrs:{field:"ake006",title:"院内项目名称",width:"10%"}}),i("ta-big-table-column",{attrs:{field:"aae101",title:"审核结果标识",width:"7%"},scopedSlots:a._u([{key:"default",fn:function(t){var e=t.row;return["1"===e.aae101?i("span",{staticStyle:{color:"#0f990f"}},[i("ta-icon",{attrs:{type:"check-circle",theme:"twoTone","two-tone-color":"#52c41a"}}),a._v("审核通过")],1):"0"===e.aae101?i("span",{staticStyle:{color:"#FF0000"}},[i("ta-icon",{attrs:{type:"exclamation-circle",theme:"twoTone","two-tone-color":"#FF0000"}}),a._v("审核违规")],1):a._e()]}}])}),i("ta-big-table-column",{attrs:{field:"akc225",align:"right",title:"单价",width:"6%",formatter:a.decimalFormat}}),i("ta-big-table-column",{attrs:{field:"akc226",align:"right",title:"数量",width:"6%",formatter:a.decimalFormat}}),i("ta-big-table-column",{attrs:{field:"akb065",align:"right",title:"金额",width:"6%",formatter:a.decimalFormat}}),i("ta-big-table-column",{attrs:{field:"aae036",title:"项目发生时间",width:"10%"}}),i("ta-big-table-column",{attrs:{field:"aaa167",title:"规则大类",width:"8%"}}),i("ta-big-table-column",{attrs:{field:"ykz032",title:"规则序号",width:"7%"}}),i("ta-big-table-column",{attrs:{field:"ykz018",title:"限制条件",width:"10%"}}),i("ta-big-table-column",{attrs:{field:"ape805",align:"right",title:"扣款数量",width:"6%",formatter:a.decimalFormat}}),i("ta-big-table-column",{attrs:{field:"ape804",align:"right",title:"扣款金额",width:"6%",formatter:a.decimalFormat}}),i("ta-big-table-column",{attrs:{field:"aae386",title:"出院科室",width:"10%"}}),i("ta-big-table-column",{attrs:{field:"aae030",title:"入院时间",width:"10%"}}),i("ta-big-table-column",{attrs:{field:"aae031",title:"出院时间",width:"10%"}}),i("ta-big-table-column",{attrs:{field:"aze002",title:"审核时间",width:"10%"}}),i("ta-big-table-column",{attrs:{field:"aze001",title:"审核批次号",width:"10%"}}),i("ta-big-table-column",{attrs:{field:"aae043",title:"规则期号",width:"10%"}}),i("ta-big-table-column",{attrs:{field:"operate",title:"操作",width:"10%",fixed:"right",align:"center"},scopedSlots:a._u([{key:"default",fn:function(t){var e=t.row;return[i("a",{directives:[{name:"show",rawName:"v-show",value:a.preDisabled,expression:"preDisabled"}],staticClass:"operate-item",on:{click:function(t){return a.fnSearch(e)}}},[a._v(" 患者详情 ")]),i("a",{staticClass:"operate-item",on:{click:function(t){return a.auditDetail(e)}}},[a._v(" "+a._s("1"===e.aae101?"合规详情":"违规详情")+" ")])]}}])})],1)],1),i("div",{staticClass:"content-box-footer"},[i("ta-pagination",{ref:"gridPager",staticStyle:{position:"absolute",right:"6px",bottom:"6px","margin-right":"100px"},attrs:{params:a.pageParams,"default-page-size":50,"page-size-options":["30","50","100","200","500"],"data-source":a.tableData,url:"monitorMg/batchAudit/queryAuditRecord"},on:{"update:dataSource":function(t){a.tableData=t},"update:data-source":function(t){a.tableData=t}}}),i("ta-button-group",{staticStyle:{position:"absolute",right:"6px",bottom:"6px"}},[i("ta-button",{attrs:{type:"primary",icon:"download"},on:{click:a.fnExportData}},[a._v("导出")])],1)],1)],1),i("div",{attrs:{id:"info"}},[i("ta-modal",{staticStyle:{top:"10px"},attrs:{width:"95%",bodyStyle:{paddingBottom:"1px"},"destroy-on-close":!0,footer:null,"get-container":a.getModalContainer,wrapClassName:"inp-modal-wrap"},on:{cancel:a.handleCancel},model:{value:a.visible,callback:function(t){a.visible=t},expression:"visible"}},[i("ta-icon",{staticStyle:{color:"black"},attrs:{slot:"closeIcon",type:"close"},slot:"closeIcon"}),i("span",{staticStyle:{"font-weight":"normal"},attrs:{slot:"title"},slot:"title"},[a._v(" 审核详情")]),i("atient-details",{attrs:{fyRecord:a.bfRecord}})],1)],1)])],1)},l=[],r=e(66347),n=e(95082),s=e(36797),o=e.n(s),c=e(83231),u=e(88412),d=e(362),A=e(48211),f=e(22722),m=e(55115),h=A.Z;m.w3.prototype.Base=Object.assign(m.w3.prototype.Base,(0,n.Z)({},f.Z));var p={name:"auditResult",components:{atientDetails:d["default"],TaTitle:u.Z},data:function(){return{col:{xs:1,sm:2,md:2,lg:2,xl:4,xxl:4},rangeValue:[o()().subtract(1,"week"),o()()],aae101List:[{value:"1",label:"合规"},{value:"0",label:"违规"}],selectTypeList:[{value:"1",label:"实审"},{value:"2",label:"预审"}],filterList:"",formShowAll:!1,aae043List:[],tableData:[],visible:!1,bfRecord:{},preDisabled:!0}},computed:{formBoxStyle:function(){return this.formShowAll?{height:"auto"}:{height:"50px"}}},mounted:function(){var t=this,a=["5","6","7","4","3","18","16","17","2"];c.Z.getAa01AAE500StartStop({aaz499s:JSON.stringify(a)},(function(a){var e=a.data.aae500List;e.includes(12)&&e.push(13),t.filterList=e.join(",")})),this.handlePageRouteData()},methods:{getModalContainer:function(){return document.getElementById("info")},handleCancel:function(){this.visible=!1,this.showAll=!1},fnSearch:function(t){this.Base.openTabMenu({id:t.akb020+t.aaz217,name:"【"+t.aac003+"】患者详情",url:"detailsQuery.html#/expenseDetailsForOther?akb020=".concat(t.akb020,"&akc190=").concat(t.akc190,"&aaz217=").concat(t.aaz217,"&flag=").concat(t.aae500),refresh:!1})},fnExportData:function(){var t=this,a=this.pageParams();if(a){var e,i=[],l=this.$refs.dataTable.getColumns(),n=(0,r.Z)(l);try{for(n.s();!(e=n.n()).done;){var s=e.value;"seq"!==s.type&&"operate"!==s.property&&!1!==s.visible&&i.push({header:s.title,key:s.property,width:20})}}catch(d){n.e(d)}finally{n.f()}var o=l.map((function(t){return t.property})),c=[{codeType:"AAC004",columnKey:"aac004"},{codeType:"AAE500",columnKey:"aae500"},{codeType:"AAE141",columnKey:"aae141"},{columnKey:"aae101",customCollection:function(t,a){"0"==t.value?t.value="审核违规":"1"==t.value?t.value="审核通过":t.value="——"}}],u=c.filter((function(t){return o.includes(t.columnKey)}));this.Base.submit(null,{url:"monitorMg/batchAudit/exportAuditRecord",data:a,autoValid:!1},{successCallback:function(e){var l=e.data.data,r={fileName:("2"==a.selectType?"【预审":"【实审")+"】审核疑点查询结果表",sheets:[{name:"worksheet1",column:{complex:!1,columns:i},rows:l,codeList:u}]};t.Base.generateExcel(r)},failCallback:function(a){t.$message.error("导出失败")}})}},formShowAllChange:function(){this.formShowAll=!this.formShowAll},queryAae043List:function(t){var a=this,e={url:"monitorMg/batchAudit/queryAae043List",data:t,autoQs:!1};this.Base.submit(null,e).then((function(t){var e=t.data.aae043List;e.length>12&&(e=e.slice(0,12)),a.aae043List=e.map((function(t){return{value:t,label:t}}))}))},pageParams:function(){var t=this.queryForm.getFieldsValue(),a=t.auditTime;if(!a||a[0]&&a[1])return t.auditStartTime=t.auditTime[0].format("YYYY-MM-DD 00:00:00"),t.auditEndTime=t.auditTime[1].format("YYYY-MM-DD 23:59:59"),t;this.$message.error("请选择时间范围！")},patientDetail:function(t){var a=t.aae500;if("5"===a)this.Base.openTabMenu({id:t.aaz217,name:"【"+t.aac003+"】住院项目明细",url:"queryManage.html#/itemDetail?akc191=".concat(t.akc191,"&aaz217=").concat(t.aaz217,"&aze001=").concat(t.aze001,"&aze004=1"),refresh:!1});else{var e="7"===a?"1":"2";this.Base.openTabMenu({id:t.aaz217,name:"【"+t.aac003+"】门特项目明细",url:"queryManage.html#/mentorDetail?akc191=".concat(t.akc191,"&aaz217=").concat(t.aaz217,"&aze001=").concat(t.aze001,"&scene=").concat(e,"&aze004=1"),refresh:!1})}},auditDetail:function(t){var a=this.queryForm.getFieldsValue(),e=h(t);"2"==a.selectType&&(e.aae500="preaudit"),this.bfRecord=e,this.visible=!0},fnQuery:function(){var t=this,a=this.pageParams();a&&this.queryForm.validateFields((function(a){a||(t.$refs.gridPager.loadData(),t.formShowAll=!1)}))},fnReset:function(){this.queryForm.resetFields(),this.fnQuery(),this.formShowAll=!0},decimalFormat:function(t){return Number(t.cellValue).toFixed(2)},fieldFormat:function(t,a){var e=t;return a.forEach((function(a,i){t===a.value&&(e=a.label)})),e},fnChangeSelectType:function(t){var a=this;if("1"===t){this.preDisabled=!0}if("2"===t){this.preDisabled=!1}this.$nextTick((function(){a.fnQuery()}))},handlePageRouteData:function(){var t=this,a=this.$route.query,e=a.selectType;e?this.$nextTick((function(){if(t.queryForm.setFieldsValue({selectType:e,ake001:a.ake001,aae500:a.aae500,ykz032:a.ykz032,aae043:a.aae043,akc191:a.akc191,aae141:a.aae141,aze001:a.aze001,aae101:a.aae101}),"null"!==a.startTime&&"null"!==a.endTime){var i=[o()(a.startTime),o()(a.endTime)];t.queryForm.setFieldsMomentValue({auditTime:i})}t.fnChangeSelectType(e)})):this.fnChangeSelectType("1")}}},b=p,g=e(1001),v=(0,g.Z)(b,i,l,!1,null,"2ebf8c3c",null),y=v.exports},362:function(t,a,e){"use strict";e.r(a),e.d(a,{default:function(){return A}});var i=function(){var t=this,a=t.$createElement,i=t._self._c||a;return i("div",{staticClass:"fit"},[i("div",[i("span",{staticStyle:{"font-weight":"bold","font-size":"14px",color:"#333333","margin-left":"10px"}},[i("ta-badge",{attrs:{color:t.badgeColor,text:"项目："}}),t._v(" "+t._s(t.fyRecord.ake002)+" "),i("span",{staticStyle:{"margin-right":"80px"}}),i("ta-badge",{attrs:{color:t.badgeColor,text:"明细总数量："}}),t._v(" "+t._s(t.fyRecord.akc226)+" "),(1==t.fyRecord.ykz020&&t.fyRecord.aae500&&t.fyRecord.aae500,i("span",[i("span",{staticStyle:{"margin-right":"80px"}}),i("ta-badge",{attrs:{color:t.badgeColor,text:"建议扣除数量："}}),t._v(" "+t._s(t.fyRecord.ape805)+" ")],1))],1),i("div",{staticStyle:{"margin-top":"0px","border-top":"0px solid #cccccc","padding-top":"5px"}},[i("ta-tabs",{attrs:{defaultActiveKey:"1"}},t._l(t.doubtList,(function(a,l){return i("ta-tab-pane",{key:l+1},[i("span",{attrs:{slot:"tab"},on:{click:function(e){return t.cardChange(a.id)}},slot:"tab"},[i("img",{attrs:{src:"1"===a.ykz020?e(60037):e(92206)}}),i("span",{staticClass:"tab-title",on:{click:function(e){return t.cardChange(a.id)}}},[t._v(t._s(a.aaa167))])]),i("p",{staticClass:"tab-content",on:{click:function(e){return t.cardChange(a.id)}}},[i("span",{staticClass:"tab-text-label"},[t._v("规则来源：")]),i("span",{staticClass:"tab-text"},[t._v(t._s(a.source))]),i("br"),i("span",{staticClass:"tab-text-label"},[t._v("限制条件：")]),i("span",{staticClass:"tab-text"},[t._v(t._s(a.ykz018))]),i("a",{directives:[{name:"show",rawName:"v-show",value:t.isRuleQuery,expression:"isRuleQuery"}],staticStyle:{"margin-left":"10px"},on:{click:function(e){return t.ruleDetails(a)}}},[t._v(">>规则详情")])])])})),1)],1)]),i("div",{staticClass:"audit-container"},[t._m(0),i("div",{staticClass:"audit-content",attrs:{id:"wai"}},[i("div",{staticClass:"divleft"},[i("div",{staticClass:"header"},[t._v("审核路径")]),i("table",{staticClass:"audit-table"},[t._m(1),i("tbody",t._l(t.auditPathList,(function(a,e){return i("tr",{key:e,staticClass:"audit-row"},[i("td",{staticClass:"audit-index"},[t._v(t._s(e+1))]),i("td",{staticClass:"audit-detail"},t._l(a.nodeInfoList,(function(l,r){return i("span",{key:r,staticClass:"audit-node-container"},[r>0&&r<a.nodeInfoList.length?i("span",[t._v("——")]):t._e(),i("span",{staticClass:"audit-node",style:{color:"3"===l.ykz020?t.colors[0]:t.colors[l.ykz020]},attrs:{tabindex:e+1},on:{click:function(a){return t.nodeChange(l)}}},[t._v(" "+t._s(l.ykz010)+" "),i("span",[t._v("("+t._s(l.ykz002)+")")])])])})),0)])})),0)])]),i("div",{staticClass:"divright"},[i("div",{staticClass:"header"},[t._v("节点详情")]),i("div",{staticClass:"right-content"},[i("div",{staticClass:"right-item"},[t._m(2),i("div",{staticClass:"right-item-content",domProps:{innerHTML:t._s(t.nodeDetail.ykz006)}})]),i("div",{staticClass:"right-item"},[t._m(3),i("div",{staticClass:"right-item-content",domProps:{innerHTML:t._s(t.nodeDetail.ykz008)}})]),i("div",{staticClass:"right-item"},[t._m(4),i("div",{staticClass:"right-item-content",domProps:{innerHTML:t._s(t.nodeDetail.ykz021)}})]),i("div",{staticClass:"right-item"},[t._m(5),i("div",{staticClass:"right-item-content",domProps:{innerHTML:t._s(t.nodeDetail.ykz022)}})])])])])])])},l=[function(){var t=this,a=t.$createElement,i=t._self._c||a;return i("div",{staticClass:"audit-info"},[i("img",{staticClass:"info-icon",attrs:{src:e(67488)}}),i("span",[t._v("审核结果信息")]),i("span",{staticClass:"status-red"},[t._v("红色")]),t._v("节点表示不通过， "),i("span",{staticClass:"status-green"},[t._v("绿色")]),t._v("节点表示通过 ")])},function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("thead",[e("tr",[e("th",{staticClass:"audit-index"},[t._v("序号")]),e("th",{staticClass:"audit-details"},[t._v("路径审核详情")])])])},function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticClass:"right-item-title"},[t._v("机审"),e("br"),t._v("记录")])},function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticClass:"right-item-title"},[t._v("引导"),e("br"),t._v("信息")])},function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticClass:"right-item-title"},[t._v("人工"),e("br"),t._v("操作")])},function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticClass:"right-item-title"},[t._v("人工"),e("br"),t._v("审核"),e("br"),t._v("理由")])}],r=e(95082),n=e(66353),s=["id"],o={name:"atientDetails",props:{fyRecord:Object},data:function(){return{visible:!1,doubtList:[],auditPathList:[],nodeDetail:{},isRuleQuery:!1,colors:["#D75457","#46C26D","#3693F3"],aaz217:"",akb020:"",flag:"",badgeColor:"#3693F3"}},mounted:function(){this.handleShowInfo(this.fyRecord)},methods:{ruleDetails:function(t){var a,e,i=this;(null===(a=t.nodeDetailVoList)||void 0===a?void 0:a.length)>0?this.Base.submit(null,{url:"mttRuleFind/queryRuleid",data:{ykz108:t.nodeDetailVoList[0].ykz108}},{successCallback:function(a){e=a.data.data[0].ruleid,i.Base.openTabMenu({id:t.ykz018,name:"【"+t.ykz018+"】规则查询",url:"mttRuleConfig.html#/ruleFind?ykz010=".concat(t.ykz018,"&ruleid=").concat(e),refresh:!1})},failCallback:function(t){i.$message.error("规则ID失败")}}):this.$message.error("审核详情缺少节点信息！请检查审核详情！")},handleShowInfo:function(t){var a=this;this.fyRecord=t,this.visible=!0,this.Base.submit(null,{url:"miimCommonRead/queryDoubtList",data:{aaz217:t.aaz217,aaz213:t.aaz213,aae500:t.aae500,type:t.type},autoValid:!0,autoQs:!1},{successCallback:function(t){t.data.list.length>0&&("monitor"===t.data.flag?t.data.list.forEach((function(t,a){for(var e in t.auditPathList){var i=t.auditPathList[e].nodeInfoList.every((function(t){return"1"===t.ykz020}));i&&(t.ykz020="1")}})):t.data.list.forEach((function(t,a){for(var e in t.auditPathList){t.auditPathList[e].nodeInfoList.forEach((function(t,a){t.ykz020=t.ykz173,t.ykz021?t.ykz021="1"===t.ykz021?"是":"否":t.ykz021="无"}));var i=t.auditPathList[e].nodeInfoList.every((function(t){return"1"===t.ykz173}));i&&(t.ykz020="1")}})),t.data.list.sort((function(t,a){var e=parseInt(t.ykz020,10),i=parseInt(a.ykz020,10);return 0===e||3===e?-1:0===i||3===i?1:e-i}))),a.doubtList=t.data.list.map((function(t,a){t.id;var e=(0,n.Z)(t,s);return(0,r.Z)({id:a+1},e)})),a.auditPathList=[],a.nodeDetail={},a.doubtList.length>0&&(a.auditPathList=a.doubtList[0].auditPathList),t.data.ruleQuery&&(a.isRuleQuery="y"==t.data.ruleQuery.toLowerCase())},failCallback:function(t){}})},cardChange:function(t){var a=t-1;this.auditPathList=this.doubtList[a].auditPathList},nodeChange:function(t){this.nodeDetail=t}}},c=o,u=e(1001),d=(0,u.Z)(c,i,l,!1,null,"e9de457e",null),A=d.exports},36766:function(t,a,e){"use strict";var i=e(66586);a["Z"]=i.Z},26263:function(t,a,e){"use strict";e.d(a,{s:function(){return i},x:function(){return l}});var i=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("h3",{staticStyle:{"font-weight":"normal"}},[e("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),e("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},l=[]},66586:function(t,a){"use strict";var e={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};a["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:e}}}},83231:function(t,a,e){"use strict";var i=e(48534);e(36133);function l(t){return t?t.akb020&&t.aaz263?"personal":t.akb020&&t.aaz309&&t.aaz307?"group":t.akb020&&t.aaz307?"department":t.akb020?"hospital":"all":"all"}function r(t){return n.apply(this,arguments)}function n(){return n=(0,i.Z)(regeneratorRuntime.mark((function t(a){var e,i,r,n,s,o,c,u,d,A,f,m;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Base.submit(null,{url:"/miimCommonRead/queryDataPermission",showPageLoading:!1,data:{jobnumber:a},autoValid:!0});case 2:return e=t.sent,i=new Set,r=new Set,e.data.permission.forEach((function(t){var a=l(t);"hospital"===a&&i.add(t.akb020),"department"===a&&r.add(t.aaz307)})),n=e.data.permission.filter((function(t){return"department"===l(t)||!r.has(t.aaz307)})).filter((function(t){return"hospital"===l(t)||!i.has(t.akb020)})),s=new Set(n.map((function(t){return t.akb020})).filter((function(t){return null!=t}))),o=new Set(n.map((function(t){return t.aaz307})).filter((function(t){return null!=t}))),c=new Set(n.map((function(t){return t.aaz309})).filter((function(t){return null!=t}))),u=new Set(n.map((function(t){return t.aaz263})).filter((function(t){return null!=t}))),d=!1,A=!1,f=!1,m=!1,1===s.size&&(d=!0),1===o.size&&1===s.size&&(A=!0),1===o.size&&1===s.size&&1===c.size&&(f=!0),1===s.size&&0===o.size&&1===u.size&&(m=!0),t.abrupt("return",{akb020Set:s,aaz307Set:o,aaz263Set:u,aaz309Set:c,akb020Disable:d,aaz307Disable:A,aaz263Disable:m,aaz309Disable:f});case 20:case"end":return t.stop()}}),t)}))),n.apply(this,arguments)}function s(t,a){Base.submit(null,{url:"miimCommonRead/getAa01AAE500StartStop",data:t},{successCallback:function(t){return a(t)}})}function o(t,a){Base.submit(null,{url:"miimCommonRead/insertTableColumShow",data:t,autoValid:!1},{successCallback:function(t){return a("success")},failCallback:function(t){return a("fail")}})}var c={zbx01:{footer:function(){return"统筹金总额"}},zbx02:{footer:function(){return"基金支出总额"}},zbx03:{footer:function(){return"总费用"}},zbx04:{footer:function(){return"总住院日"}}};a["Z"]={permissionCheck:r,getAa01AAE500StartStop:s,insertTableColumShow:o,props:c,moneyNumFormat:function(t){var a=t.cellValue;return a||"—"}}},92206:function(t){"use strict";t.exports="data:image/png;base64,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"},60037:function(t){"use strict";t.exports="data:image/png;base64,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"},67488:function(t){"use strict";t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAEKADAAQAAAABAAAAEAAAAAA0VXHyAAAByklEQVQ4EZVSOywEYRCe+fc8CvHIrbg7RK7xjAQVodBRaCQqhYLCm4hEIQoRWonGEYVOiUIhSgoaQYHQkHgccYsIcW7djvmPvfzn7iRmk51/v/m++eexCEmsYpJSjTvDhwB1/gW9PAkNHIkCntEX/dEfWONYAyUiKFhcAle3UW69fmwwx6vwIkd3z8MhAB5rAGPXC/qNBIVKcvcZzYC0y1icOMJDmOeKWsJAB/n9L8USQyJC78hzVigY7rAIZgGIL4i1u8VcHsW35Q8aBeGQtQ2ImO5ylglPb2An+P75ZBHNJRLbQullC8LEbA0crUBQFLo3ugQR1Kukv84EeGCCNXOzmHOECFusbY+ZwV9iGRMCl7jK2h/ePvuSfyUAi+8lfhT7VwICq5tntxfRE9VwC+eCAf5h0FSSJj2yoIqXNl4w8FjJ82gSCCvR9bh7AxNkwRT3GMXsTOoaCwcePOYn7CCgluZylkZb8Pv0adSgjUVvtvC39/QYnaaJJ0iQiUhNl5MYjCaQZP+8vqppjgZu6+q3WH7zDIb4vSlSRfWtL/dMYnHlStA7/Jr3/hFcB/pemdqCjKsWU4EduJjLuM9IcTZy/mWu5tTGE/kv0KybhGEzosQAAAAASUVORK5CYII="},55382:function(){},61219:function(){}}]);
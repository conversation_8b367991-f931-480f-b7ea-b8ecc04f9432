(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[6923],{88412:function(t,e,a){"use strict";var i=a(26263),l=a(36766),n=a(1001),o=(0,n.Z)(l.Z,i.s,i.x,!1,null,"5e7ef0ae",null);e["Z"]=o.exports},39420:function(t,e,a){"use strict";a.r(e),a.d(e,{default:function(){return F}});var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("ta-border-layout",{attrs:{"show-border":!0,layout:{header:"90px",right:"65%"}}},[a("div",{attrs:{slot:"header"},slot:"header"},[a("ta-title",{staticStyle:{"margin-left":"6px"},attrs:{title:"查询条件"}}),a("search-term",{ref:"term",on:{fnQuery:t.fnQuery}})],1),a("div",{staticStyle:{height:"690px"}},[a("ta-border-layout",{attrs:{"show-border":!1,"header-cfg":{showBorder:!0},layout:{header:"160px"}}},[a("div",{staticClass:"border",attrs:{slot:"header"},slot:"header"},[a("div",[a("ta-title",{attrs:{title:"数据总览"}}),a("div",{staticStyle:{display:"flex"}},[a("div",{staticClass:"box"},[a("div",{staticStyle:{display:"flex","flex-direction":"column","align-items":"center","margin-left":"25%"}},[a("span",{staticClass:"text"},[t._v("总费用")]),a("span",{staticClass:"text2"},[t._v(t._s(this.warningTotal)+"元")])])]),a("div",{staticClass:"box2"},[a("div",{staticStyle:{width:"20px"}}),a("div",{staticStyle:{display:"flex","flex-direction":"column","align-items":"center","margin-left":"25%"}},[a("span",{staticClass:"text"},[t._v("总数量")]),a("span",{staticClass:"text2"},[t._v(t._s(this.warningcount)+"次")])])])])],1),a("div")]),a("div",{staticClass:"border"},[a("div",[a("ta-title",{attrs:{title:"项目类别构成"}},[a("ta-radio-group",{staticStyle:{float:"right"},attrs:{value:t.checked,size:"small"},on:{change:t.handleChange}},[a("ta-radio-button",{attrs:{value:"元"}},[t._v("费用")]),a("ta-radio-button",{attrs:{value:"次"}},[t._v("数量")])],1)],1)],1),a("div",{attrs:{id:"main"}})])])],1),a("div",{staticClass:"border",staticStyle:{height:"690px"},attrs:{slot:"right"},slot:"right"},[a("div",[a("ta-title",{attrs:{title:"科室费用情况"}},[a("ta-radio-group",{staticStyle:{float:"right"},attrs:{value:t.checked2,size:"small"},on:{change:t.handleChange2}},[a("ta-radio-button",{attrs:{value:"元"}},[t._v("费用")]),a("ta-radio-button",{attrs:{value:"次"}},[t._v("数量")])],1)],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"元"==t.checked2,expression:"checked2 == '元'"}],staticStyle:{height:"87%","margin-top":"10px",cursor:"pointer"}},[a("ta-big-table",{ref:"xTable1",attrs:{height:"auto",data:t.tableData,"control-column":t.showHiddenOrSortColumn,"cell-style":t.cellStyle,border:"",resizable:"","auto-resize":"","show-overflow":"","highlight-hover-row":""},on:{"cell-click":t.cellClickEvent}},[a("ta-big-table-column",{attrs:{type:"seq",title:"序号","min-width":"55",align:"center"}}),a("ta-big-table-column",{attrs:{field:"aae386",title:"科室名称",sortable:"","min-width":"140",align:"center"}}),a("ta-big-table-column",{attrs:{field:"aaz307",title:"科室编码","min-width":"140",align:"center",visible:!1}}),a("ta-big-table-column",{attrs:{field:"totalmoney",title:"医疗服务总费用",sortable:"","min-width":"180",align:"center",formatter:"formatAmount"}}),a("ta-big-table-column",{attrs:{field:"fy01",title:"床位费(元)",sortable:"","min-width":"120",align:"center",formatter:"formatFixedNumber",visible:t.shoucolumns}}),a("ta-big-table-column",{attrs:{field:"fy01zb",title:"床位费占比",sortable:"","min-width":"120",align:"center",visible:t.shoucolumns},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;e.column;return[t._v(" "+t._s(t.formatPercentageValue(a.fy01,a.totalmoney))+" ")]}}])}),a("ta-big-table-column",{attrs:{field:"fy02",title:"诊察费(元)",sortable:"","min-width":"120",align:"center",formatter:"formatFixedNumber",visible:t.shoucolumns}}),a("ta-big-table-column",{attrs:{field:"fy02zb",title:"诊察费占比",sortable:"","min-width":"120",align:"center",visible:t.shoucolumns},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;e.column;return[t._v(" "+t._s(t.formatPercentageValue(a.fy02,a.totalmoney))+" ")]}}])}),a("ta-big-table-column",{attrs:{field:"fy23",title:"检查费(元)",sortable:"","min-width":"120",align:"center",formatter:"formatFixedNumber",visible:t.shoucolumns}}),a("ta-big-table-column",{attrs:{field:"fy23zb",title:"检查费占比",sortable:"","min-width":"120",align:"center",visible:t.shoucolumns},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;e.column;return[t._v(" "+t._s(t.formatPercentageValue(a.fy23,a.totalmoney))+" ")]}}])}),a("ta-big-table-column",{attrs:{field:"fy22",title:"检验费(元)",sortable:"","min-width":"120",align:"center",formatter:"formatFixedNumber",visible:t.shoucolumns}}),a("ta-big-table-column",{attrs:{field:"fy22zb",title:"检验费占比",sortable:"","min-width":"120",align:"center",visible:t.shoucolumns},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;e.column;return[t._v(" "+t._s(t.formatPercentageValue(a.fy22,a.totalmoney))+" ")]}}])}),a("ta-big-table-column",{attrs:{field:"fy21",title:"治疗费(元)",sortable:"","min-width":"120",align:"center",formatter:"formatFixedNumber",visible:t.shoucolumns}}),a("ta-big-table-column",{attrs:{field:"fy21zb",title:"治疗费占比",sortable:"","min-width":"120",align:"center",visible:t.shoucolumns},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;e.column;return[t._v(" "+t._s(t.formatPercentageValue(a.fy21,a.totalmoney))+" ")]}}])}),a("ta-big-table-column",{attrs:{field:"fy24",title:"手术费(元)",sortable:"","min-width":"120",align:"center",formatter:"formatFixedNumber",visible:t.shoucolumns}}),a("ta-big-table-column",{attrs:{field:"fy24zb",title:"手术费占比",sortable:"","min-width":"120",align:"center",visible:t.shoucolumns},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;e.column;return[t._v(" "+t._s(t.formatPercentageValue(a.fy24,a.totalmoney))+" ")]}}])}),a("ta-big-table-column",{attrs:{field:"fy26",title:"护理费(元)",sortable:"","min-width":"120",align:"center",formatter:"formatFixedNumber",visible:t.shoucolumns}}),a("ta-big-table-column",{attrs:{field:"fy26zb",title:"护理费占比",sortable:"","min-width":"120",align:"center",visible:t.shoucolumns},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;e.column;return[t._v(" "+t._s(t.formatPercentageValue(a.fy26,a.totalmoney))+" ")]}}])}),a("ta-big-table-column",{attrs:{field:"fy08",title:"卫生材料费(元)",sortable:"","min-width":"140",align:"center",formatter:"formatFixedNumber",visible:t.shoucolumns}}),a("ta-big-table-column",{attrs:{field:"fy08zb",title:"卫生材料费占比",sortable:"","min-width":"140",align:"center",visible:t.shoucolumns},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;e.column;return[t._v(" "+t._s(t.formatPercentageValue(a.fy08,a.totalmoney))+" ")]}}])}),a("ta-big-table-column",{attrs:{field:"fy11",title:"西药费(元)",sortable:"","min-width":"120",align:"center",formatter:"formatFixedNumber",visible:t.shoucolumns}}),a("ta-big-table-column",{attrs:{field:"fy11zb",title:"西药费占比",sortable:"","min-width":"120",align:"center",visible:t.shoucolumns},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;e.column;return[t._v(" "+t._s(t.formatPercentageValue(a.fy11,a.totalmoney))+" ")]}}])}),a("ta-big-table-column",{attrs:{field:"fy13",title:"中药饮片费(元)",sortable:"","min-width":"140",align:"center",formatter:"formatFixedNumber",visible:t.shoucolumns}}),a("ta-big-table-column",{attrs:{field:"fy13zb",title:"中药饮片费占比",sortable:"","min-width":"140",align:"center",visible:t.shoucolumns},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;e.column;return[t._v(" "+t._s(t.formatPercentageValue(a.fy13,a.totalmoney))+" ")]}}])}),a("ta-big-table-column",{attrs:{field:"fy12",title:"中成药费(元)",sortable:"","min-width":"140",align:"center",formatter:"formatFixedNumber",visible:t.shoucolumns}}),a("ta-big-table-column",{attrs:{field:"fy12zb",title:"中成药费占比",sortable:"","min-width":"140",align:"center",visible:t.shoucolumns},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;e.column;return[t._v(" "+t._s(t.formatPercentageValue(a.fy12,a.totalmoney))+" ")]}}])}),a("ta-big-table-column",{attrs:{field:"fy27",title:"膳食费(元)",sortable:"","min-width":"140",align:"center",formatter:"formatFixedNumber",visible:t.shoucolumns}}),a("ta-big-table-column",{attrs:{field:"fy27zb",title:"膳食费占比",sortable:"","min-width":"140",align:"center",visible:t.shoucolumns},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;e.column;return[t._v(" "+t._s(t.formatPercentageValue(a.fy27,a.totalmoney))+" ")]}}])}),a("ta-big-table-column",{attrs:{field:"fy29",title:"输氧费(元)",sortable:"","min-width":"140",align:"center",formatter:"formatFixedNumber",visible:t.shoucolumns}}),a("ta-big-table-column",{attrs:{field:"fy29zb",title:"输氧费占比",sortable:"","min-width":"140",align:"center",visible:t.shoucolumns},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;e.column;return[t._v(" "+t._s(t.formatPercentageValue(a.fy29,a.totalmoney))+" ")]}}])}),a("ta-big-table-column",{attrs:{field:"fy28",title:"输血费(元)",sortable:"","min-width":"140",align:"center",formatter:"formatFixedNumber",visible:t.shoucolumns}}),a("ta-big-table-column",{attrs:{field:"fy28zb",title:"输血费占比",sortable:"","min-width":"140",align:"center",visible:t.shoucolumns},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;e.column;return[t._v(" "+t._s(t.formatPercentageValue(a.fy28,a.totalmoney))+" ")]}}])}),a("ta-big-table-column",{attrs:{field:"fy25",title:"麻醉费(元)",sortable:"","min-width":"120",align:"center",formatter:"formatFixedNumber",visible:t.shoucolumns}}),a("ta-big-table-column",{attrs:{field:"fy25zb",title:"麻醉费占比",sortable:"","min-width":"120",align:"center",visible:t.shoucolumns},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;e.column;return[t._v(" "+t._s(t.formatPercentageValue(a.fy25,a.totalmoney))+" ")]}}])}),a("ta-big-table-column",{attrs:{field:"fy99",title:"其他费(元)",sortable:"","min-width":"140",align:"center",formatter:"formatFixedNumber",visible:t.shoucolumns}}),a("ta-big-table-column",{attrs:{field:"fy99zb",title:"其他费占比",sortable:"","min-width":"140",align:"center",visible:t.shoucolumns},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;e.column;return[t._v(" "+t._s(t.formatPercentageValue(a.fy99,a.totalmoney))+" ")]}}])})],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"次"==t.checked2,expression:"checked2 == '次'"}],staticStyle:{height:"87%","margin-top":"10px",cursor:"pointer"}},[a("ta-big-table",{ref:"xTable2",attrs:{height:"auto",data:t.tableData2,"cell-style":t.cellStyle,"control-column":t.showHiddenOrSortColumn,border:"",resizable:"","auto-resize":"","show-overflow":"","highlight-hover-row":""},on:{"cell-click":t.cellClickEvent}},[a("ta-big-table-column",{attrs:{type:"seq",title:"序号","min-width":"55",align:"center"}}),a("ta-big-table-column",{attrs:{field:"aae386",title:"科室名称",sortable:"","min-width":"140",align:"center"}}),a("ta-big-table-column",{attrs:{field:"aaz307",title:"科室编码","min-width":"140",align:"center",visible:!1}}),a("ta-big-table-column",{attrs:{field:"totalcount",title:"医疗服务总次数",sortable:"","min-width":"180",align:"center"}}),a("ta-big-table-column",{attrs:{field:"fy01",title:"床位费(次)",sortable:"","min-width":"120",align:"center",visible:t.shoucolumns}}),a("ta-big-table-column",{attrs:{field:"fy01zb",title:"床位费占比",sortable:"","min-width":"120",align:"center",visible:t.shoucolumns},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;e.column;return[t._v(" "+t._s(t.formatPercentageValue(a.fy01,a.totalcount))+" ")]}}])}),a("ta-big-table-column",{attrs:{field:"fy02",title:"诊察费(次)",sortable:"","min-width":"120",align:"center",visible:t.shoucolumns}}),a("ta-big-table-column",{attrs:{field:"fy02zb",title:"诊察费占比",sortable:"","min-width":"120",align:"center",visible:t.shoucolumns},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;e.column;return[t._v(" "+t._s(t.formatPercentageValue(a.fy02,a.totalcount))+" ")]}}])}),a("ta-big-table-column",{attrs:{field:"fy23",title:"检查费(次)",sortable:"","min-width":"120",align:"center",visible:t.shoucolumns}}),a("ta-big-table-column",{attrs:{field:"fy23zb",title:"检查费占比",sortable:"","min-width":"120",align:"center",visible:t.shoucolumns},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;e.column;return[t._v(" "+t._s(t.formatPercentageValue(a.fy23,a.totalcount))+" ")]}}])}),a("ta-big-table-column",{attrs:{field:"fy22",title:"检验费(次)",sortable:"","min-width":"120",align:"center",visible:t.shoucolumns}}),a("ta-big-table-column",{attrs:{field:"fy22zb",title:"检验费占比",sortable:"","min-width":"120",align:"center",visible:t.shoucolumns},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;e.column;return[t._v(" "+t._s(t.formatPercentageValue(a.fy22,a.totalcount))+" ")]}}])}),a("ta-big-table-column",{attrs:{field:"fy21",title:"治疗费(次)",sortable:"","min-width":"120",align:"center",visible:t.shoucolumns}}),a("ta-big-table-column",{attrs:{field:"fy21zb",title:"治疗费占比",sortable:"","min-width":"120",align:"center",visible:t.shoucolumns},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;e.column;return[t._v(" "+t._s(t.formatPercentageValue(a.fy21,a.totalcount))+" ")]}}])}),a("ta-big-table-column",{attrs:{field:"fy24",title:"手术费(次)",sortable:"","min-width":"120",align:"center",visible:t.shoucolumns}}),a("ta-big-table-column",{attrs:{field:"fy24zb",title:"手术费占比",sortable:"","min-width":"120",align:"center",visible:t.shoucolumns},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;e.column;return[t._v(" "+t._s(t.formatPercentageValue(a.fy24,a.totalcount))+" ")]}}])}),a("ta-big-table-column",{attrs:{field:"fy26",title:"护理费(次)",sortable:"","min-width":"120",align:"center",visible:t.shoucolumns}}),a("ta-big-table-column",{attrs:{field:"fy26zb",title:"护理费占比",sortable:"","min-width":"120",align:"center",visible:t.shoucolumns},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;e.column;return[t._v(" "+t._s(t.formatPercentageValue(a.fy26,a.totalcount))+" ")]}}])}),a("ta-big-table-column",{attrs:{field:"fy08",title:"卫生材料费(次)",sortable:"","min-width":"140",align:"center",visible:t.shoucolumns}}),a("ta-big-table-column",{attrs:{field:"fy08zb",title:"卫生材料费占比",sortable:"","min-width":"140",align:"center",visible:t.shoucolumns},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;e.column;return[t._v(" "+t._s(t.formatPercentageValue(a.fy08,a.totalcount))+" ")]}}])}),a("ta-big-table-column",{attrs:{field:"fy11",title:"西药费(次)",sortable:"","min-width":"120",align:"center",visible:t.shoucolumns}}),a("ta-big-table-column",{attrs:{field:"fy11zb",title:"西药费占比",sortable:"","min-width":"120",align:"center",visible:t.shoucolumns},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;e.column;return[t._v(" "+t._s(t.formatPercentageValue(a.fy11,a.totalcount))+" ")]}}])}),a("ta-big-table-column",{attrs:{field:"fy13",title:"中药饮片费(次)",sortable:"","min-width":"140",align:"center",visible:t.shoucolumns}}),a("ta-big-table-column",{attrs:{field:"fy13zb",title:"中药饮片费占比",sortable:"","min-width":"140",align:"center",visible:t.shoucolumns},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;e.column;return[t._v(" "+t._s(t.formatPercentageValue(a.fy13,a.totalcount))+" ")]}}])}),a("ta-big-table-column",{attrs:{field:"fy12",title:"中成药费(次)",sortable:"","min-width":"140",align:"center",visible:t.shoucolumns}}),a("ta-big-table-column",{attrs:{field:"fy12zb",title:"中成药费占比",sortable:"","min-width":"140",align:"center",visible:t.shoucolumns},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;e.column;return[t._v(" "+t._s(t.formatPercentageValue(a.fy12,a.totalcount))+" ")]}}])}),a("ta-big-table-column",{attrs:{field:"fy27",title:"膳食费(次)",sortable:"","min-width":"140",align:"center",visible:t.shoucolumns}}),a("ta-big-table-column",{attrs:{field:"fy27zb",title:"膳食费占比",sortable:"","min-width":"140",align:"center",visible:t.shoucolumns},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;e.column;return[t._v(" "+t._s(t.formatPercentageValue(a.fy27,a.totalcount))+" ")]}}])}),a("ta-big-table-column",{attrs:{field:"fy29",title:"输氧费(次)",sortable:"","min-width":"140",align:"center",visible:t.shoucolumns}}),a("ta-big-table-column",{attrs:{field:"fy29zb",title:"输氧费占比",sortable:"","min-width":"140",align:"center",visible:t.shoucolumns},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;e.column;return[t._v(" "+t._s(t.formatPercentageValue(a.fy29,a.totalcount))+" ")]}}])}),a("ta-big-table-column",{attrs:{field:"fy28",title:"输血费(次)",sortable:"","min-width":"140",align:"center",visible:t.shoucolumns}}),a("ta-big-table-column",{attrs:{field:"fy28zb",title:"输血费占比",sortable:"","min-width":"140",align:"center",visible:t.shoucolumns},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;e.column;return[t._v(" "+t._s(t.formatPercentageValue(a.fy28,a.totalcount))+" ")]}}])}),a("ta-big-table-column",{attrs:{field:"fy25",title:"麻醉费(次)",sortable:"","min-width":"120",align:"center",visible:t.shoucolumns}}),a("ta-big-table-column",{attrs:{field:"fy25zb",title:"麻醉费占比",sortable:"","min-width":"120",align:"center",visible:t.shoucolumns},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;e.column;return[t._v(" "+t._s(t.formatPercentageValue(a.fy25,a.totalcount))+" ")]}}])}),a("ta-big-table-column",{attrs:{field:"fy99",title:"其他费(次)",sortable:"","min-width":"140",align:"center",visible:t.shoucolumns}}),a("ta-big-table-column",{attrs:{field:"fy99zb",title:"其他费占比",sortable:"","min-width":"140",align:"center",visible:t.shoucolumns},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;e.column;return[t._v(" "+t._s(t.formatPercentageValue(a.fy99,a.totalcount))+" ")]}}])})],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"元"==t.checked2,expression:"checked2 == '元'"}],staticClass:"content-box-footer"},[a("ta-pagination",{ref:"infoPageRef",staticStyle:{position:"absolute",right:"6px",bottom:"6px","margin-right":"100px"},attrs:{dataSource:t.tableData,params:t.PageParams,defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"],url:"medicalServiceTypeCount/queryKc22Infomoney"},on:{"update:dataSource":function(e){t.tableData=e},"update:data-source":function(e){t.tableData=e}}}),a("ta-button",{staticStyle:{position:"absolute",right:"6px",bottom:"6px"},attrs:{type:"primary",icon:"download"},on:{click:t.exportTable}},[t._v("导出")])],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"次"==t.checked2,expression:"checked2 == '次'"}],staticClass:"content-box-footer"},[a("ta-pagination",{ref:"infoPageRef2",staticStyle:{position:"absolute",right:"6px",bottom:"6px","margin-right":"100px"},attrs:{dataSource:t.tableData2,params:t.PageParams,defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"],url:"medicalServiceTypeCount/queryKc22Infocount"},on:{"update:dataSource":function(e){t.tableData2=e},"update:data-source":function(e){t.tableData2=e}}}),a("ta-button",{staticStyle:{position:"absolute",right:"6px",bottom:"6px"},attrs:{type:"primary",icon:"download"},on:{click:t.exportTable}},[t._v("导出")])],1)]),a("div",{staticClass:"border",staticStyle:{width:"100%",height:"330px"},attrs:{slot:"footer"},slot:"footer"},[a("div",[a("ta-title",{attrs:{title:"医疗费用趋势"}},[a("ta-radio-group",{staticStyle:{float:"right"},attrs:{value:t.checked3,size:"small"},on:{change:t.handleChange3}},[a("ta-radio-button",{attrs:{value:"元"}},[t._v("费用")]),a("ta-radio-button",{attrs:{value:"次"}},[t._v("数量")])],1)],1)],1),a("div",{attrs:{id:"main2"}})])]),a("div",{attrs:{id:"content"}},[a("ta-modal",{attrs:{width:"90%",height:"700px",footer:null,destroyOnClose:!0,getContainer:t.setContainer,title:"查看明细"},on:{cancel:t.handleCancel},model:{value:t.visible,callback:function(e){t.visible=e},expression:"visible"}},[a("detail-info",{attrs:{paramData:t.parameters}})],1)],1)],1)},l=[],n=a(66347),o=a(88412),s=a(48211),r=a(1708),c=a(36797),u=a.n(c),f=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("div",[i("ta-form",{attrs:{autoFormCreate:function(e){t.baseform=e},layout:"horizontal","label-width":"100px",formLayout:!0}},[i("ta-form-item",{attrs:{span:3,labelCol:{span:0},wrapperCol:{span:24}}},[i("ta-button",{attrs:{size:"small"},on:{click:function(t){return e.trunTime("1")}}},[e._v("近三月")]),i("ta-button",{attrs:{size:"small"},on:{click:function(t){return e.trunTime("2")}}},[e._v("近六月")]),i("ta-button",{attrs:{size:"small"},on:{click:function(t){return e.trunTime("3")}}},[e._v("近一年")])],1),i("ta-form-item",{attrs:{fieldDecoratorId:"allDate",span:5,fieldDecoratorOptions:{rules:[{required:!0,message:"必须选取时间"}]},required:!0,"init-value":e.rangeValue}},[i("span",{attrs:{slot:"label"},slot:"label"},[e._v("时间范围")]),i("ta-range-picker",{staticStyle:{width:"105%"},attrs:{type:"month","allow-one":!0}})],1),i("ta-form-item",{attrs:{fieldDecoratorId:"ake001",span:4}},[i("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("项目名称")]),i("ta-select",{attrs:{placeholder:"项目名称筛选",mode:"multiple",maxTagCount:1,allowClear:"",options:e.xmList}})],1),i("ta-form-item",{attrs:{fieldDecoratorId:"aaz307",span:4}},[i("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("科室名称")]),i("ta-select",{attrs:{mode:"multiple",maxTagCount:1,placeholder:"科室名称筛选",allowClear:"",options:e.ksList}})],1),i("ta-form-item",{attrs:{fieldDecoratorId:"aka063",span:3}},[i("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("项目类别")]),i("ta-select",{attrs:{mode:"multiple",maxTagCount:1,placeholder:"请选择",allowClear:"",options:e.aka063List}})],1),i("ta-button-group",{staticStyle:{"margin-right":"20px",float:"right"}},[i("ta-button",{staticStyle:{float:"right"},attrs:{type:"primary",icon:"search"},on:{click:e.fnQuery}},[e._v("查询 ")]),i("ta-button",{staticStyle:{"margin-left":"10px",float:"right"},attrs:{type:"primary",icon:"redo"},on:{click:e.fnReset}},[e._v("重置 ")]),i("ta-button",{staticStyle:{"margin-left":"10px",float:"right"},attrs:{type:"primary"},on:{click:e.config}},[e._v("项目类别配置 ")])],1)],1),i("ta-modal",{attrs:{title:"项目类别配置",height:"280px",width:"300px"},on:{ok:e.handleOk,cancel:e.handleCancel},model:{value:e.visible,callback:function(t){e.visible=t},expression:"visible"}},[i("span",[e._v("选择需要统计的项目类别:")]),i("ta-checkbox-group",{attrs:{value:e.checkedList},on:{change:e.onChange}},[i("ta-row",e._l(e.aka063List2,(function(t,a){return i("ta-col",{key:a,attrs:{span:12}},[i("ta-checkbox",{attrs:{value:t.value}},[e._v(e._s(t.label)+"费")])],1)})),1)],1),i("div",[i("ta-checkbox",{attrs:{indeterminate:e.indeterminate,checked:e.checkAll},on:{change:e.onCheckAllChange}},[e._v(" 全部选中 ")])],1)],1)],1)},m=[],d={name:"span-config",model:{prop:"config",event:"change"},props:{config:{type:Object,required:!0}},render:function(){var t=this,e=arguments[0];return e("ta-label-con",{attrs:{label:"所占栅格"}},[e("ta-input-number",{attrs:{disabled:"divider"===this.config.type,min:1,max:24,placeholder:"请输入表单所占的栅格"},style:"width: 100%",model:{value:t.config.span,callback:function(e){t.$set(t.config,"span",e)}}})])}},h={name:"searchTerm",components:{SpanConfig:d},props:{},data:function(){return{rangeValue:[TaUtils.getMoment(new Date((new Date).getTime()).toISOString().slice(0,8)+"01","YYYY-MM-DD"),TaUtils.getMoment((new Date).toISOString().slice(0,10),"YYYY-MM-DD")],aka063List:[],aka063List2:[],checkedList:[],bfcheckedList:[],indeterminate:!1,checkAll:!0,visible:!1,ksList:[],xmList:[]}},mounted:function(){var t=this;this.$nextTick((function(){t.fnQueryks(),t.fnQueryxm(),t.fnQueryAka063()}))},methods:{moment:u(),trunTime:function(t){switch(t){case"1":this.baseform.setFieldsValue({allDate:[u()().subtract("2","month"),u()()]});break;case"2":this.baseform.setFieldsValue({allDate:[u()().subtract("5","month"),u()()]});break;case"3":this.baseform.setFieldsValue({allDate:[u()().subtract("1","year"),u()()]});break;default:break}this.fnQuery()},handleCancel:function(){this.visible=!1,this.checkedList=this.bfcheckedList,this.bfcheckedList.length<this.aka063List2.length&&0!==this.bfcheckedList.length?(this.checkAll=!1,this.indeterminate=!0):0===this.bfcheckedList.length?(this.checkAll=!1,this.indeterminate=!1):(this.checkAll=!0,this.indeterminate=!1)},onChange:function(t){t.length<this.aka063List2.length&&0!==t.length?(this.checkAll=!1,this.indeterminate=!0):0===t.length?(this.checkAll=!1,this.indeterminate=!1):(this.checkAll=!0,this.indeterminate=!1),this.checkedList=t},onCheckAllChange:function(t){var e=t.target.checked;e?(this.checkAll=!0,this.checkedList=this.aka063List2.map((function(t){return t.value}))):(this.checkAll=!1,this.checkedList=[]),this.indeterminate=!1},handleOk:function(t){var e=this;this.visible=!1;var a=this.aka063List.filter((function(t){return e.checkedList.includes(t.value)}));this.aka063List=a,this.bfcheckedList=this.checkedList,this.fnQuery()},fnQuery:function(){var t=this,e=this.baseform.getFieldsValue();null!=e.allDate[0]&&""!=e.allDate[0]&&null!=e.allDate[1]&&""!=e.allDate[1]?(e.aka063||(e.aka063=this.bfcheckedList),e.startDate=e.allDate[0].format("YYYYMM"),e.endDate=e.allDate[1].format("YYYYMM"),this.baseform.validateFields((function(a,i){a||t.$emit("fnQuery",e)}))):this.$message.error("请选择完整日期")},fnReset:function(){this.baseform.resetFields(),this.aka063List=this.aka063List2,this.checkedList=this.aka063List2.map((function(t){return t.value})),this.bfcheckedList=this.aka063List2.map((function(t){return t.value})),this.indeterminate=!1,this.checkAll=!0,this.fnQuery()},config:function(){this.baseform.resetFields(["aka063","aaz307","ake001"]),this.visible=!0,this.aka063List=this.aka063List2,this.bfcheckedList==this.checkedList?this.bfcheckedList=this.checkedList:this.checkedList=this.bfcheckedList},fnQueryAka063:function(){var t=this;this.Base.submit(null,{url:"auditStatistics/queryAka063",data:{}},{successCallback:function(e){t.aka063List=e.data.aka063List,t.aka063List2=e.data.aka063List,t.checkedList=t.aka063List2.map((function(t){return t.value})),t.bfcheckedList=t.aka063List2.map((function(t){return t.value})),t.fnQuery()},failCallback:function(e){t.$message.error("列表加载失败")}})},fnQueryks:function(){var t=this;this.Base.submit(null,{url:"medicalServiceTypeCount/querySelectksList",data:{}},{successCallback:function(e){t.ksList=e.data.SelectList},failCallback:function(e){t.$message.error("列表加载失败")}})},fnQueryxm:function(){var t=this;this.Base.submit(null,{url:"medicalServiceTypeCount/querySelectxmList",data:{}},{successCallback:function(e){t.xmList=e.data.SelectList},failCallback:function(e){t.$message.error("列表加载失败")}})}}},b=h,g=a(1001),y=(0,g.Z)(b,f,m,!1,null,"046449e8",null),p=y.exports,v=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("div",{staticClass:"fit"},[i("div",{staticStyle:{height:"7%",padding:"1px"}},[i("ta-form",{attrs:{layout:"horizontal","form-layout":!0,"label-width":"100px","auto-form-create":function(e){return t.searchForm=e}}},[i("ta-form-item",{attrs:{fieldDecoratorId:"allDate",labelCol:{span:7},wrapperCol:{span:16},span:5,fieldDecoratorOptions:{rules:[{required:!0,message:"必须选取时间"}]},required:!0,"init-value":e.rangeValue}},[i("span",{attrs:{slot:"label"},slot:"label"},[e._v("时间范围")]),i("ta-range-picker",{staticStyle:{width:"105%"},attrs:{type:"month","allow-one":!0}})],1),i("ta-form-item",{staticStyle:{"margin-left":"1%"},attrs:{fieldDecoratorId:"ake001",span:4,labelCol:{span:7},wrapperCol:{span:15}}},[i("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("项目名称")]),i("ta-select",{attrs:{placeholder:"项目名称筛选",mode:"multiple",maxTagCount:1,allowClear:"",options:e.xmList}})],1),i("ta-form-item",{staticStyle:{"margin-left":"1%"},attrs:{fieldDecoratorId:"aaz307",span:4,labelCol:{span:7},wrapperCol:{span:15}}},[i("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("科室名称")]),i("ta-select",{attrs:{mode:"multiple",maxTagCount:1,placeholder:"科室名称筛选",allowClear:"",options:e.ksList}})],1),i("ta-form-item",{staticStyle:{"margin-left":"1%"},attrs:{fieldDecoratorId:"aka063",span:4,labelCol:{span:7},wrapperCol:{span:15}}},[i("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("项目类别")]),i("ta-select",{attrs:{mode:"multiple",maxTagCount:1,placeholder:"项目类别筛选",allowClear:"",options:e.aka063List}})],1),i("ta-form-item",{attrs:{span:6}},[i("ta-button",{staticStyle:{margin:"0 20px",float:"right"},attrs:{type:"default",icon:"redo"},on:{click:e.fnReset}},[e._v("重置")]),i("ta-button",{staticStyle:{float:"right"},attrs:{icon:"search",type:"primary"},on:{click:e.searchTable}},[e._v("查询")])],1)],1)],1),i("div",{staticStyle:{height:"95%",padding:"5px"}},[i("div",{staticStyle:{height:"90%","margin-top":"10px"}},[i("ta-big-table",{ref:"xTable1",attrs:{height:"auto","show-overflow":"",data:e.tableData,border:"","auto-resize":""},scopedSlots:e._u([{key:"bottomBar",fn:function(){return[i("ta-button",{staticStyle:{float:"right","margin-top":"20px"},attrs:{icon:"download",type:"primary"},on:{click:e.exportTable}},[e._v("导出")]),i("ta-pagination",{ref:"gridPager",staticStyle:{float:"right","margin-top":"20px","margin-right":"10px"},attrs:{url:"medicalServiceTypeCount/queryCostDetails","data-source":e.tableData,params:e.userPageParams,defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"]},on:{"update:dataSource":function(t){e.tableData=t},"update:data-source":function(t){e.tableData=t}}})]},proxy:!0}])},[i("ta-big-table-column",{attrs:{type:"seq",title:"序号",width:"55",align:"center"}}),i("ta-big-table-column",{attrs:{field:"aae386",title:"科室名称","min-width":"120",align:"center"}}),i("ta-big-table-column",{attrs:{field:"aaa103",title:"医疗服务项目类别","min-width":"160",align:"center"}}),i("ta-big-table-column",{attrs:{field:"ake002",title:"医疗服务项目名称","min-width":"160",align:"center"}}),i("ta-big-table-column",{attrs:{field:"aae043",title:"结算时间","min-width":"120",align:"center"}}),i("ta-big-table-column",{attrs:{field:"akc226",title:"数量","min-width":"100",align:"center"}}),i("ta-big-table-column",{attrs:{field:"akb065",title:"金额","min-width":"100",align:"center",formatter:"formatAmount"}})],1)],1)])])},k=[],w=a(95082),x=a(22722),C=a(55115);C.w3.prototype.Base=Object.assign(C.w3.prototype.Base,(0,w.Z)({},x.Z));var _={name:"detailInfo",props:{paramData:Object},data:function(){return{rangeValue:[TaUtils.getMoment(new Date((new Date).getTime()).toISOString().slice(0,5)+"01-01","YYYY-MM-DD"),TaUtils.getMoment((new Date).toISOString().slice(0,10),"YYYY-MM-DD")],tableData:[],condition:{},aka063List:[],ksList:[],xmList:[]}},mounted:function(){var t=this;this.$nextTick((function(){t.fnQueryks(),t.fnQueryxm(),t.fnQueryAka063(),t.paramData&&t.searchForm.setFieldsValue(t.paramData),t.searchForm.setFieldsValue({allDate:t.paramData.allDate}),t.searchTable()}))},methods:{fnReset:function(){this.paramData.aaz307="",this.paramData.aka063="",this.searchForm.resetFields(),this.searchTable()},userPageParams:function(){return this.condition},exportTable:function(){var t,e=this,a=[],i=this.$refs.xTable1.getColumns(),l=(0,n.Z)(i);try{for(l.s();!(t=l.n()).done;){var o=t.value;"序号"!==o.title&&a.push({header:o.title,key:o.property,width:20})}}catch(s){l.e(s)}finally{l.f()}this.Base.submit(null,{url:"medicalServiceTypeCount/exportExcel",data:this.condition,autoValid:!1},{successCallback:function(t){var i={fileName:"科室费用情况明细结果表",sheets:[{name:"worksheet1",column:{complex:!1,columns:a},rows:t.data.data}]};e.Base.generateExcel(i)},failCallback:function(t){e.$message.error("科室费用情况明细数据加载失败")}})},searchTable:function(){var t=this.searchForm.getFieldsValue();null!=t.allDate[0]&&""!=t.allDate[0]&&null!=t.allDate[1]&&""!=t.allDate[1]?(t.startDate=t.allDate[0].format("YYYYMM"),t.endDate=t.allDate[1].format("YYYYMM"),this.condition=t,this.$refs.gridPager.loadData((function(t){}))):this.$message.error("请选择完整日期")},fnQueryAka063:function(){var t=this;this.Base.submit(null,{url:"auditStatistics/queryAka063",data:{}},{successCallback:function(e){t.aka063List=e.data.aka063List},failCallback:function(e){t.$message.error("列表加载失败")}})},fnQueryks:function(){var t=this;this.Base.submit(null,{url:"medicalServiceTypeCount/querySelectksList",data:{}},{successCallback:function(e){t.ksList=e.data.SelectList},failCallback:function(e){t.$message.error("列表加载失败")}})},fnQueryxm:function(){var t=this;this.Base.submit(null,{url:"medicalServiceTypeCount/querySelectxmList",data:{}},{successCallback:function(e){t.xmList=e.data.SelectList},failCallback:function(e){t.$message.error("列表加载失败")}})}}},S=_,L=(0,g.Z)(S,v,k,!1,null,"49064ba4",null),z=L.exports,D=s.Z,T={name:"serviceFeeCount",components:{SearchTerm:p,TaTitle:o.Z,detailInfo:z},data:function(){return{tableData:[],tableData2:[],checked:"元",checked2:"元",checked3:"元",shoucolumns:!1,isClickxm:!1,isClickks:!1,vibrantColors:["#ff0000","#00ff00","#0000ff","#ffff00","#ff00ff","#00ffff","#f0f8ff","#a2a4ff","#8aff81","#ffd700","#ff69b4","#90ee90","#66ccff","#ffebcd","#d65dff","#c4c4c4","#808080","#000000","#ffffff","#ffe6ff","#ffccff","#ff99ff","#ff66cc","#ff3399","#ff0066","#ccff00","#99ff00","#66ff00","#33ff00","#00ff00"],dateFormat:"YYYY/MM/DD",monthFormat:"YYYY/MM",params:{},parameters:{},visible:!1,warningTotal:0,warningcount:0,option:{legend:{}},option2:{legend:{}},option3:{legend:{}},showHiddenOrSortColumn:{themeColor:"red",icon:"appstore",showHiddenConfig:{open:!0,disabledHiddenColumns:function(t){return!1}},columnSortConfig:{open:!0,onMove:function(t,e){e.dragColumn,e.dropColumn;return!0},dragEnd:function(t,e){e.dragColumn,e.resultColumns}},successCallback:this.successCallback,disabledControlCol:function(t){}}}},mounted:function(){},methods:{moment:u(),formatPercentageValue:function(t,e){return t&&0!=t&&e&&0!=e?(t/e*100).toFixed(2)+"%":"0%"},handleCancel:function(){this.$refs.term.fnQuery()},cellStyle:function(t){t.row,t.rowIndex;var e=t.column;t.columnIndex;if("aae386"!==e.property&&"seq"!==e.type)return{color:"#19abf0"}},cellClickEvent:function(t){var e=t.row;this.isClickxm=!0;var a=this.params,i=[];i.push(e.aaz307),a.aaz307=i,this.isClickks&&(a.aka063=[],a.aka063=this.params.aka063),this.parameters=a,this.visible=!0},setContainer:function(){return document.getElementById("content")},fnQuery:function(t){var e=this;if(this.params=t,this.isClickxm=!1,this.isClickks=!1,this.asyncLoad(t),this.shoucolumns=!0,0==t.aka063.length){var a,i=this.$refs.xTable1.getTableColumn().collectColumn.map((function(t){return t.property})),l=(0,n.Z)(i);try{for(l.s();!(a=l.n()).done;){var o=a.value;o&&"aaz307"!=o&&this.$refs.xTable1.showColumn(this.$refs.xTable1.getColumnByField(o))}}catch(y){l.e(y)}finally{l.f()}var s,r=this.$refs.xTable2.getTableColumn().collectColumn.map((function(t){return t.property})),c=(0,n.Z)(r);try{for(c.s();!(s=c.n()).done;){var u=s.value;u&&"aaz307"!=u&&this.$refs.xTable2.showColumn(this.$refs.xTable2.getColumnByField(u))}}catch(y){c.e(y)}finally{c.f()}}else{this.$refs.xTable1.resetColumn();var f,m=(0,n.Z)(t.aka063);try{for(m.s();!(f=m.n()).done;){var d=f.value;d&&"aaz307"!=d&&(this.$refs.xTable1.showColumn(this.$refs.xTable1.getColumnByField("fy"+d)),this.$refs.xTable1.showColumn(this.$refs.xTable1.getColumnByField("fy"+d+"zb")))}}catch(y){m.e(y)}finally{m.f()}this.$refs.xTable2.resetColumn();var h,b=(0,n.Z)(t.aka063);try{for(b.s();!(h=b.n()).done;){var g=h.value;g&&"aaz307"!=g&&(this.$refs.xTable2.showColumn(this.$refs.xTable2.getColumnByField("fy"+g)),this.$refs.xTable2.showColumn(this.$refs.xTable2.getColumnByField("fy"+g+"zb")))}}catch(y){b.e(y)}finally{b.f()}}this.$nextTick((function(){e.$refs.infoPageRef.loadData(),e.$refs.infoPageRef2.loadData()}))},PageParams:function(){return this.params},handleChange:function(t){this.checked=t.target.value,this.asyncLoad(this.params,"pie")},handleChange2:function(t){this.checked2=t.target.value},handleChange3:function(t){this.checked3=t.target.value,this.asyncLoad(this.params,"line")},exportTable:function(){var t,e=[],a=[],i=this.$refs.xTable1.getColumns(),l=this.$refs.xTable2.getColumns(),o=(0,n.Z)(i);try{for(o.s();!(t=o.n()).done;){var s=t.value;"序号"!==s.title&&(s.visible&&e.push({header:s.title,key:s.property,width:20}))}}catch(h){o.e(h)}finally{o.f()}var r,c=(0,n.Z)(l);try{for(c.s();!(r=c.n()).done;){var u=r.value;"序号"!==u.title&&(u.visible&&a.push({header:u.title,key:u.property,width:20}))}}catch(h){c.e(h)}finally{c.f()}var f=D(this.tableData);f.forEach((function(t){var e=t.totalmoney;e>0?Object.keys(t).forEach((function(a){a.startsWith("fy")&&"number"===typeof t[a]&&(t["".concat(a,"zb")]=(t[a]/e*100).toFixed(2)+"%")})):Object.keys(t).forEach((function(e){e.startsWith("fy")&&"number"===typeof t[e]&&(t["".concat(e,"zb")]="0.00%")}))}));var m=D(this.tableData2);m.forEach((function(t){var e=t.totalcount;e>0?Object.keys(t).forEach((function(a){a.startsWith("fy")&&"number"===typeof t[a]&&(t["".concat(a,"zb")]=(t[a]/e*100).toFixed(2)+"%")})):Object.keys(t).forEach((function(e){e.startsWith("fy")&&"number"===typeof t[e]&&(t["".concat(e,"zb")]="0.00%")}))}));var d={fileName:"医疗服务费用统计",sheets:[{name:"金额统计",column:{complex:!1,columns:e},rows:f},{name:"数量统计",column:{complex:!1,columns:a},rows:m}]};this.Base.generateExcel(d)},callback:function(t){},asyncLoad:function(t,e){var a=this;this.myChart=r.init(document.getElementById("main")),this.myChart2=r.init(document.getElementById("main2")),this.myChart.setOption(this.option),this.myChart2.setOption(this.option),this.Base.submit(null,{url:"medicalServiceTypeCount/queryProjectCategory",data:t,autoValid:!1},{successCallback:function(t){var e=[],i=[];a.warningTotal=t.data.data.totalMoney,a.warningcount=t.data.data.totalCount,e="元"==a.checked?t.data.data.moneyCategory:t.data.data.countCategory,i="元"==a.checked3?t.data.data.moneyCategory:t.data.data.countCategory;var l={tooltip:{trigger:"item",formatter:"{a} <br/>{b} : {c}"+a.checked+" ({d}%)"},color:["#f6bf2d","#2595fd","#657796","#63d9aa"],toolbox:{show:!0,feature:{saveAsImage:{show:!0,name:"项目类别构成"}}},legend:{orient:"horizontal",bottom:"5%",icon:"circle",itemGap:15,textStyle:{fontSize:15},data:e.map((function(t){return t.name})),formatter:function(t){for(var e=l.series[0].data,a=0;a<l.series[0].data.length;a++)if(t==e[a].name)return t}},series:[{name:"项目类别构成",type:"pie",radius:"45%",center:["50%","43%"],data:e,itemStyle:{emphasis:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"},normal:{label:{show:!0,formatter:"{b}:{d}%",textStyle:{fontSize:15}}},labelLine:{show:!0}}}]},n=[],o=[];t.data.data2.forEach((function(t){var e={title:{subtext:Object.keys(t)[0]+"项目类别分布"},tooltip:{trigger:"axis",axisPointer:{type:"line"},formatter:"{b}:<br>{c} "+a.checked3},toolbox:{show:!0,feature:{saveAsImage:{show:!0},dataView:{show:!0,readOnly:!1},magicType:{show:!0,type:["line","bar"],option:{bar:{barWidth:"30%"}}},restore:{show:!0},dataZoom:{show:!0}}},xAxis:{triggerEvent:!0,boundaryGap:!1,axisLine:{show:!0,lineStyle:{color:"#797979",width:"1"}},axisLabel:{margin:20,interval:0,formatter:function(t){return t.length>5?t.slice(0,5)+"...":t}},axisTick:{show:!0},type:"category",data:"元"==a.checked3?t[Object.keys(t)[0]].moneyCategory.map((function(t){return t.name})):t[Object.keys(t)[0]].countCategory.map((function(t){return t.name}))},yAxis:{type:"value",splitLine:{lineStyle:{type:"dashed"}},axisLabel:{margin:20},axisLine:{show:!1}},series:[{name:"医疗费用趋势",color:["#"+Math.floor(16777215*Math.random()).toString(16)],data:"元"==a.checked3?t[Object.keys(t)[0]].moneyCategory:t[Object.keys(t)[0]].countCategory,type:"line",splitLine:{show:!0}}]};o.push(Object.keys(t)[0]),n.push(e)}));var s={baseOption:{timeline:{tooltip:{show:!1},axisType:"category",autoPlay:!0,symbolSize:8,height:40,playInterval:1e3,data:o,lineStyle:{width:1},label:{fontSize:10}},tooltip:{},calculable:!0,grid:{top:70,left:80,right:80,bottom:80},xAxis:[{type:"category",axisLabel:{interval:0},data:i.map((function(t){return t.name})),splitLine:{show:!1}}],yAxis:[{type:"value",name:"单位:"+a.checked}],series:[{name:"医疗费用趋势",type:"line"}]},options:n};a.myChart.setOption(l),a.myChart2.setOption(s),a.myChart.off("click"),a.myChart.on("click",(function(t){a.isClickks=!0;var e=a.params,i=[];i.push(t.data.id),e.aka063=i,a.isClickxm&&(e.aaz307=[],e.aaz307=a.params.aaz307),a.parameters=e,a.visible=!0})),a.myChart2.off("click"),a.myChart2.on("click",(function(t){if("timeline"!==t.componentType){a.isClickks=!0;var e=a.params,l=[];l.push(i[t.dataIndex].id),e.aka063=l,a.isClickxm&&(e.aaz307=[],e.aaz307=a.params.aaz307),a.parameters=e,a.visible=!0}}))},failCallback:function(t){}})}}},P=T,V=(0,g.Z)(P,i,l,!1,null,"237cc695",null),F=V.exports},36766:function(t,e,a){"use strict";var i=a(66586);e["Z"]=i.Z},26263:function(t,e,a){"use strict";a.d(e,{s:function(){return i},x:function(){return l}});var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},l=[]},66586:function(t,e){"use strict";var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};e["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}},55382:function(){},61219:function(){}}]);
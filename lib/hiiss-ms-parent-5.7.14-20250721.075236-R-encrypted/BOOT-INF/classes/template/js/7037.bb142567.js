"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[7037],{6723:function(e,t,a){a.r(t),a.d(t,{default:function(){return R}});var r=function(){var e=this,t=this,a=t.$createElement,r=t._self._c||a;return r("ta-border-layout",{attrs:{layout:{header:"210px",footer:"90px"}}},[r("div",{attrs:{slot:"header"},slot:"header"},[r("ta-title",{attrs:{title:"查询条件"}}),r("ta-form",{attrs:{formLayout:"","auto-form-create":function(t){return e.queryParamForm=t}}},[r("ta-form-item",{attrs:{"field-decorator-id":"ykz108",label:"规则运行场景",span:6}},[r("ta-select",{attrs:{"options-key":{value:"id",label:"name"},options:t.insuranceTypeOptions},on:{select:t.searchSelectOptions}})],1),r("ta-form-item",{attrs:{"field-decorator-id":"aae043",label:"期号",span:6,disabled:""}},[r("ta-input")],1),r("ta-form-item",{attrs:{"field-decorator-id":"aaz319",label:"规则大类",span:6}},[r("ta-select",{attrs:{"options-key":{value:"id",label:"name"},options:t.ruleTypeOptions,"allow-clear":""}})],1),r("ta-form-item",{attrs:{"field-decorator-id":"ake001",label:"三目编码",span:6,"show-length":!0}},[r("ta-input")],1),r("ta-form-item",{attrs:{"field-decorator-id":"ake002",label:"三目名称",span:6}},[r("ta-input")],1),r("ta-form-item",{attrs:{"field-decorator-id":"ykz018",label:"限制条件",span:6,"show-length":!0}},[r("ta-input")],1),r("ta-form-item",{attrs:{"field-decorator-id":"ykz019",label:"审核标准",span:6,"show-length":!0}},[r("ta-input")],1),r("ta-form-item",{attrs:{"field-decorator-id":"shjg",label:"审核状态",span:6,required:""}},[r("ta-select",{attrs:{options:[{value:"0",label:"未审核"},{value:"1",label:"通过"},{value:"2",label:"不通过"}]}})],1),r("ta-button",{attrs:{type:"primary"},on:{click:t.searchRule}},[t._v("查询")])],1)],1),r("div",{staticClass:"fit"},[r("ta-row",{attrs:{type:"flex",justify:"space-between"}},[r("ta-col",{attrs:{span:12}},[r("ta-title",{attrs:{title:"知识元信息"}})],1),r("span",[r("ta-button",{attrs:{type:"warning"},on:{click:function(e){return t.setPass(!0)}}},[t._v("设为审核通过")]),r("ta-button",{attrs:{type:"warning"},on:{click:function(e){return t.setPass(!1)}}},[t._v("设为审核不通过")])],1),r("ta-modal",{attrs:{title:"请输入理由",visible:t.showAuditModal},on:{cancel:t.cancelAuditModal,ok:t.updateAudit}},[r("ta-input",{model:{value:t.shly,callback:function(e){t.shly=e},expression:"shly"}})],1)],1),r("ta-table",{ref:"auditTable",attrs:{columns:t.tableColumns,"data-source":t.tableData,rowSelection:t.rowSelection,scroll:{y:"100%"},"row-key":"ake001"},scopedSlots:t._u([{key:"state",fn:function(e,a){return r("a",{on:{click:function(e){return t.openModal(a)}}},[t._v(t._s("0"===a.shjg?"未审核":"1"===a.shjg?"通过":"未通过"))])}}])})],1),r("div",{attrs:{slot:"footer"},slot:"footer"},[r("ta-pagination",{ref:"pageHelper",attrs:{url:t.URL.queryRuleInfoByAuditState,params:t.getPageParam,defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"],"data-source":t.tableData},on:{"update:dataSource":function(e){t.tableData=e},"update:data-source":function(e){t.tableData=e}}})],1),r("ta-modal",{attrs:{title:t.modalTitle,height:"800px",width:"95%",bodyStyle:{padding:"0"},footer:null,"destroy-on-close":!0},on:{cancel:t.modalCancel},model:{value:t.showModal,callback:function(e){t.showModal=e},expression:"showModal"}},[r("div",[r("div",{staticStyle:{border:"14px #F0F2F5 solid","border-bottom":"0"}},[r("ta-title",{attrs:{title:"审核操作"}}),r("ta-form",{staticStyle:{"padding-bottom":"8px"},attrs:{formLayout:"","auto-form-create":function(t){return e.auditForm=t}}},[r("ta-form-item",{attrs:{"field-decorator-id":"shly",span:12,"label-col":{span:4},label:"审核理由:"}},[r("ta-textarea",{attrs:{rows:3}})],1),r("ta-form-item",{attrs:{"field-decorator-id":"shjg",span:6,label:"审核决定:",required:""}},[r("ta-radio-group",{model:{value:t.auditResult,callback:function(e){t.auditResult=e},expression:"auditResult"}},[r("ta-radio",{attrs:{value:"1",checked:!0}},[t._v("通过")]),r("ta-radio",{attrs:{value:"2"}},[t._v("不通过")])],1)],1),r("ta-form-item",{attrs:{span:6}},[r("ta-button",{attrs:{type:"primary"},on:{click:t.updateAuditByModal}},[t._v("提交审核结果")]),r("ta-button",{directives:[{name:"show",rawName:"v-show",value:"2"===t.auditResult,expression:"auditResult==='2'"}],on:{click:t.openRequireModal}},[t._v("规则需求填报")])],1)],1)],1),r("rule-config-detail",{attrs:{record:t.detailRecord}})],1)]),r("ta-modal",{attrs:{title:t.modalTitle,height:"730px",width:"80%",bodyStyle:{padding:"0"},"destroy-on-close":!0},on:{cancel:t.cancelRequireModal},model:{value:t.showRequireModal,callback:function(e){t.showRequireModal=e},expression:"showRequireModal"}},[r("rule-requirement",{attrs:{insuranceTypeOptions:t.insuranceTypeOptions,ruleTypeOptions:t.ruleTypeOptions,"form-data":t.requireModalData}})],1)],1)},o=[],n=a(56664),i=a(95082),l=a(48534),s=(a(36133),a(88412)),u=a(39744),c=a(66086),d=function(){var e=this,t=this,a=t.$createElement,r=t._self._c||a;return r("div",{staticStyle:{height:"700px"}},[r("ta-form",{attrs:{"label-col":{span:4},"auto-form-create":function(t){e.requirementForm=t}}},[r("ta-row",[r("ta-col",{attrs:{span:12}},[r("ta-form-item",{attrs:{label:"规则运行场景","field-decorator-id":"ykz108",required:""}},[r("ta-select",{attrs:{"options-key":{value:"id",label:"name"},options:t.insuranceTypeOptions,disabled:""}})],1),r("ta-form-item",{attrs:{"field-decorator-id":"ykz018",required:"",label:"限制条件"}},[r("ta-textarea",{attrs:{autosize:{minRows:6,maxRows:6}}})],1),r("ta-form-item",{attrs:{"field-decorator-id":"textgzxqsm",label:"规则需求说明"}},[r("ta-textarea",{attrs:{autosize:{minRows:4,maxRows:4}}})],1),r("ta-form-item",{attrs:{label:"文件上传"}},[r("ta-upload",[r("ta-button",[t._v("选择文件")])],1)],1)],1),r("ta-col",{attrs:{span:12}},[r("ta-form-item",{attrs:{label:"规则大类名称","field-decorator-id":"aaz319",disabled:""}},[r("ta-select",{attrs:{"options-key":{value:"id",label:"name"},options:t.ruleTypeOptions,disabled:""}})],1),r("ta-form-item",{attrs:{"field-decorator-id":"ykz019",label:"审核标准"}},[r("ta-textarea",{attrs:{rows:12,autosize:{minRows:12,maxRows:12}}})],1)],1)],1)],1),r("ta-row",{staticStyle:{"margin-left":"10px"}},[r("ta-col",{attrs:{span:12}},[r("ta-row",[r("ta-col",{attrs:{span:16}},[r("ta-label-con",{attrs:{labelAlign:"center","label-col":{span:6},label:"三目名称或编码:"}},[r("ta-input",{model:{value:t.queryParam,callback:function(e){t.queryParam=e},expression:"queryParam"}})],1),r("ta-table",{ref:"sourceTable",attrs:{bordered:"",showCheckbox:!0,columns:t.sourceTableColumns,"row-key":"ake001","data-source":t.sourceData,scroll:{x:240,y:"180px"}}}),r("ta-pagination",{ref:"sourcePageHelper",attrs:{"data-source":t.sourceData,defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"],url:t.URL.getSmInfo,params:t.getPageParam},on:{"update:dataSource":function(e){t.sourceData=e},"update:data-source":function(e){t.sourceData=e}}})],1),r("ta-col",[r("ta-button",{staticStyle:{display:"block"},on:{click:t.getSm}},[t._v("查询")]),r("ta-button",{staticStyle:{"margin-left":"120px","margin-top":"70px"},attrs:{type:"success"},on:{click:t.addRowFromTable}},[t._v("加入>>")])],1)],1)],1),r("ta-col",{attrs:{span:12}},[r("p",[t._v("需求关联项目列表")]),r("ta-label-con",{attrs:{"label-col":{span:4},"label-align":"center",label:"人工录入"}},[r("ta-row",[r("ta-col",{attrs:{offset:1,span:8}},[r("ta-input",{attrs:{placeholder:"三目编码"},model:{value:t.ake001,callback:function(e){t.ake001=e},expression:"ake001"}})],1),r("ta-col",{attrs:{offset:1,span:8}},[r("ta-input",{attrs:{placeholder:"三目名称"},model:{value:t.ake002,callback:function(e){t.ake002=e},expression:"ake002"}})],1),r("ta-col",{attrs:{offset:2,span:4}},[r("ta-button",{attrs:{type:"success"},on:{click:t.addRow}},[t._v("加入↓")])],1)],1)],1),r("ta-table",{attrs:{bordered:"",columns:t.targetTableColumns,"data-source":t.targetData,scroll:{y:"160px"}},scopedSlots:t._u([{key:"delete",fn:function(e,a){return r("a",{on:{click:function(e){return t.deleteRow(a)}}},[t._v("删除")])}}])})],1)],1)],1)},p=[],m={name:"ruleRequirement",props:{formData:Object,insuranceTypeOptions:Array,ruleTypeOptions:Array},data:function(){return{URL:u.J,sourceTableColumns:[{title:"三目编码",dataIndex:"ake001",width:120,align:"center",overflowTooltip:!0},{title:"三目名称",dataIndex:"ake002",width:120,align:"center",overflowTooltip:!0}],sourceData:[],targetTableColumns:[{title:"三目编码",dataIndex:"ake001",width:120,align:"center",overflowTooltip:!0},{title:"三目名称",dataIndex:"ake002",width:120,align:"center",overflowTooltip:!0},{title:"项目来源",dataIndex:"cze010",width:120,align:"center",overflowTooltip:!0},{title:"操作",dataIndex:"action",width:120,align:"center",overflowTooltip:!0,scopedSlots:{customRender:"delete"}}],targetData:[],ake001:"",ake002:"",queryParam:""}},methods:{getPageParam:function(){return{sm:this.queryParam}},addRowFromTable:function(){var e=this,t=this.$refs.sourceTable.getChecked().selectedRows,a=t.filter((function(t){return e.addRowRecord(t)}));this.sourceData=this.sourceData.filter((function(e){return!a.find((function(t){return t.ake001===e.ake001}))}))},ake001RepeatCheck:function(e){return this.targetData.find((function(t){return t.ake001===e}))},addRow:function(){var e={ake001:this.ake001,ake002:this.ake002,cze010:"2"};this.addRowRecord(e)&&(this.ake001="",this.ake002="")},addRowRecord:function(e){return""!==e.ake001&&""!==e.ake002&&(this.ake001RepeatCheck(e.ake001)?(this.$message.error("项目_[编码".concat(e.ake001,"][名称").concat(e.ake002,"]已经存在")),!1):(this.targetData.push(e),!0))},deleteRow:function(e){this.targetData=this.targetData.filter((function(t){return e.ake001!==t.ake001}))},getSm:function(){var e=this;return(0,l.Z)(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.$refs.sourcePageHelper.loadData();case 1:case"end":return t.stop()}}),t)})))()}},mounted:function(){this.requirementForm.setFieldsValue(this.formData)}},f=m,h=a(1001),g=(0,h.Z)(f,d,p,!1,null,"ded790c4",null),w=g.exports,b=[{title:"三目编码",dataIndex:"ake001",width:120,align:"center",overflowTooltip:!0},{title:"三目名称",dataIndex:"ake002",width:120,align:"center",overflowTooltip:!0},{title:"限制条件",dataIndex:"ykz018",width:120,align:"center",overflowTooltip:!0},{title:"审核标准",dataIndex:"ykz019",width:120,align:"center",overflowTooltip:!0},{title:"规则大类",dataIndex:"aaa167",width:120,align:"center",overflowTooltip:!0},{title:"审核人",dataIndex:"shr",width:120,align:"center",overflowTooltip:!0},{title:"审核时间",dataIndex:"shsj",width:120,align:"center",overflowTooltip:!0},{title:"审核状态",dataIndex:"shjg",width:120,align:"center",overflowTooltip:!0,scopedSlots:{customRender:"state"}}],k={name:"ruleConfigAudit",components:{RuleRequirement:w,TaTitle:s.Z,RuleConfigDetail:c.Z},data:function(){return{URL:u.J,shly:"",shjg:"",tableColumns:b,tableData:[],rowSelection:{},insuranceTypeOptions:[],ruleTypeOptions:[],modalTitle:"",showModal:!1,showRequireModal:!1,showAuditModal:!1,detailRecord:{},auditResult:"1",requireModalData:{}}},methods:{getPageParam:function(){return this.queryParamForm.getFieldsValue()},searchSelectOptions:function(e){var t=this;return(0,l.Z)(regeneratorRuntime.mark((function a(){var r;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,u.Z.getSelectOptionsWithMaxBatch({ykz108:e});case 2:r=a.sent,t.queryParamForm.setFieldsValue({aae043:r.batchNo}),t.ruleTypeOptions=r.ruleTypeOptions;case 5:case"end":return a.stop()}}),a)})))()},getTypeListOptions:function(){var e=this;return(0,l.Z)(regeneratorRuntime.mark((function t(){var a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,u.Z.getMedicalInsuranceTypeList({inUse:!0});case 2:a=t.sent,e.insuranceTypeOptions=a.TypeList;case 4:case"end":return t.stop()}}),t)})))()},searchRule:function(){this.$refs.pageHelper.loadData()},modalCancel:function(){this.modalTitle="",this.showModal=!1,this.detailRecord={}},openModal:function(e){var t=this;return(0,l.Z)(regeneratorRuntime.mark((function a(){var r,o,l,s;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return t.modalTitle="规则配置审核【".concat(e.ake001,"——").concat(e.ake002,"】"),t.detailRecord.record=(0,i.Z)((0,i.Z)({},e),{},{aae043:t.queryParamForm.getFieldValue("aae043")}),a.next=4,Promise.all([u.Z.queryAuditStandDetail(t.detailRecord.record),u.Z.getRuleTree(t.detailRecord.record)]);case 4:r=a.sent,o=(0,n.Z)(r,2),l=o[0],s=o[1],t.detailRecord.standDetail=l.auditStand,t.detailRecord.ruleTree=s.ruleTree,t.showModal=!0;case 11:case"end":return a.stop()}}),a)})))()},openRequireModal:function(){this.requireModalData=this.detailRecord.record,this.showRequireModal=!0},cancelRequireModal:function(){this.requireModalData={},this.showRequireModal=!1},setPass:function(e){var t=this;return(0,l.Z)(regeneratorRuntime.mark((function a(){var r;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(r=t.$refs.auditTable.getChecked().selectedRows,0!==r.length&&r[0]){a.next=4;break}return t.$message.error("请勾选审核项目"),a.abrupt("return");case 4:t.shjg=e?"1":"2",t.showAuditModal=!0;case 6:case"end":return a.stop()}}),a)})))()},cancelAuditModal:function(){this.showAuditModal=!1,this.shly="",this.shjg=""},updateAudit:function(){var e=this;return(0,l.Z)(regeneratorRuntime.mark((function t(){var a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a=e.$refs.auditTable.getChecked().selectedRows.map((function(t){return(0,i.Z)((0,i.Z)({},t),{},{shjg:e.shjg,shly:e.shly})})),t.prev=1,t.next=4,u.Z.auditRule({map:e.queryParamForm.getFieldsValue(),list:a});case 4:e.$refs.pageHelper.loadData(),t.next=11;break;case 7:return t.prev=7,t.t0=t["catch"](1),e.$message.error("审核失败，服务器错误"),t.abrupt("return");case 11:e.$message.success("成功！"),e.cancelAuditModal();case 13:case"end":return t.stop()}}),t,null,[[1,7]])})))()},updateAuditByModal:function(){var e=this;return(0,l.Z)(regeneratorRuntime.mark((function t(){var a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a=(0,i.Z)((0,i.Z)({},e.detailRecord.record),e.auditForm.getFieldsValue()),t.prev=1,t.next=4,u.Z.auditRule({map:e.queryParamForm.getFieldsValue(),list:[a]});case 4:e.$refs.pageHelper.loadData(),t.next=11;break;case 7:return t.prev=7,t.t0=t["catch"](1),e.$message.error("审核失败，服务器错误"),t.abrupt("return");case 11:e.$message.success("成功！"),e.modalCancel();case 13:case"end":return t.stop()}}),t,null,[[1,7]])})))()}},mounted:function(){this.getTypeListOptions()}},y=k,v=(0,h.Z)(y,r,o,!1,null,"2f7cdd00",null),R=v.exports}}]);
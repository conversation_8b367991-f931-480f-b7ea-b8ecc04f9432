(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[5497],{88412:function(e,t,a){"use strict";var n=a(26263),i=a(36766),l=a(1001),r=(0,l.Z)(i.Z,n.s,n.x,!1,null,"5e7ef0ae",null);t["Z"]=r.exports},90831:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return m}});var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"fit"},[a("ta-border-layout",{attrs:{"layout-type":"fixTop"},scopedSlots:e._u([{key:"header",fn:function(){return[a("ta-form",{staticStyle:{"margin-top":"20px"},attrs:{autoFormCreate:e.autoFormCreate,col:3,formLayout:!0}},[a("ta-form-item",{attrs:{span:4,"label-col":{span:8},"wrapper-col":{span:16},"field-decorator-id":"aae043","field-decorator-options":{rules:[{required:!0,message:"必须选取日期"}]},required:!0,"init-value":e.defaultValue}},[a("span",{attrs:{slot:"label"},slot:"label"},[e._v("审核时间")]),a("ta-date-picker",{staticStyle:{width:"100%"}})],1),a("ta-form-item",{attrs:{require:!1,showInfo:!0,"field-decorator-id":"compareDay","init-value":1,label:"往前对比",span:4,"label-col":{span:8},"wrapper-col":{span:16}}},[a("ta-input-number",{attrs:{min:1,max:5,formatter:function(e){return e+"天"},parser:function(e){return e.replace("天","")}},on:{change:e.onChange}})],1),a("ta-form-item",{attrs:{require:!1,showInfo:!0,"field-decorator-id":"doubtType",label:"疑点变化类型",span:4,"label-col":{span:8},"wrapper-col":{span:16}}},[a("ta-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择疑点变化类型",allowClear:""}},[a("ta-select-option",{attrs:{value:"0"}},[e._v("新增疑点")]),a("ta-select-option",{attrs:{value:"1"}},[e._v("旧疑点")]),a("ta-select-option",{attrs:{value:"2"}},[e._v("已合规")])],1)],1),a("ta-form-item",{attrs:{require:!1,showInfo:!0,"field-decorator-id":"ape893Type",label:"医师操作变化",span:4,"label-col":{span:8},"wrapper-col":{span:16}}},[a("ta-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择医师操作变化",allowClear:""}},[a("ta-select-option",{attrs:{value:"1"}},[e._v("仍然无操作")]),a("ta-select-option",{attrs:{value:"0"}},[e._v("操作沿用")]),a("ta-select-option",{attrs:{value:"2"}},[e._v("已有新操作")])],1)],1),a("ta-form-item",{attrs:{span:4}}),a("ta-form-item",{staticStyle:{"text-align":"right"},attrs:{require:!1,showInfo:!0,label:" ",span:4,"label-col":{span:6},"wrapper-col":{span:18}}},[a("ta-button",{staticStyle:{"margin-right":"10px"},attrs:{type:"primary"},on:{click:e.queryTableData}},[e._v("查询 ")]),a("ta-button",{staticStyle:{"margin-right":"10px"},on:{click:e.fnReset}},[e._v("重置 ")])],1)],1)]},proxy:!0}])},[a("ta-card",{staticClass:"fit"},[a("ta-tabs",{attrs:{defaultActiveKey:e.activeKey},on:{change:e.callback}},[a("ta-tab-pane",{key:"1",attrs:{tab:"日审对比结果"}},[a("ta-big-table",{ref:"infoTableRef",attrs:{data:e.tableData,height:"620px","export-config":{},"import-config":{},border:"","control-column":e.showHiddenOrSortColumn,"highlight-hover-row":"","highlight-current-row":"","auto-resize":"","show-overflow":"",resizable:"",size:"small"}},[a("ta-big-table-column",{attrs:{width:50,title:"序号",type:"seq"}}),a("ta-big-table-column",{attrs:{title:"疑点变化类型",sortable:"",field:"doubtType",align:"center","header-align":"center","min-width":"140px"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return["1"===n.doubtType?a("span",[a("ta-icon",{attrs:{type:"pause-circle",rotate:90}}),e._v("旧疑点")],1):"0"===n.doubtType?a("span",[a("ta-icon",{attrs:{type:"plus-circle",theme:"twoTone",twoToneColor:"red"}}),e._v("新增疑点")],1):"2"===n.doubtType?a("span",[a("ta-icon",{attrs:{type:"check-circle",theme:"twoTone",twoToneColor:"#52c41a"}}),e._v("已合规")],1):a("span",[e._v("——")])]}}])}),a("ta-big-table-column",{attrs:{title:"医师操作变化",sortable:"",field:"ape893Type",align:"center","header-align":"center",blue:"","min-width":"140px"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return["2"===n.doubtType?a("span",[e._v("——")]):"0"===n.ape893Type?a("span",[a("ta-icon",{attrs:{type:"issues-close"}}),e._v("操作沿用")],1):"1"===n.ape893Type?a("span",[a("ta-icon",{attrs:{type:"stop",theme:"twoTone",twoToneColor:"red"}}),e._v("仍然无操作")],1):"2"===n.ape893Type?a("span",[a("ta-icon",{attrs:{type:"plus",theme:"twoTone",twoToneColor:"#52c41a"}}),e._v("已有新操作")],1):a("span",[e._v("——")])]}}])}),a("ta-big-table-colgroup",{attrs:{align:"center","header-align":"center",title:"疑点操作变更情况跟踪"}},[a("ta-big-table-column",{attrs:{align:"center",field:"field1",title:e.dates[0],visible:!0,"header-align":"center",width:"200"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return["2"!=n.doubtType&&e.dates[0]&&n.changesList.length>0&&n.changesList.filter((function(t){return t.aae043==e.dates[0]})).length>0?a("span",[e._v(e._s(n.changesList.find((function(t){return t.aae043===e.dates[0]})).ape893||{}))]):a("span",[e._v("——")])]}}])}),a("ta-big-table-column",{attrs:{align:"center",field:"field2",title:e.dates[1],visible:!1,"header-align":"center",width:"200"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[e.dates[1]&&n.changesList.length>0&&n.changesList.filter((function(t){return t.aae043==e.dates[1]})).length>0?a("span",[e._v(e._s(n.changesList.find((function(t){return t.aae043===e.dates[1]})).ape893||{}))]):a("span",[e._v("——")])]}}])}),a("ta-big-table-column",{attrs:{align:"center",field:"field3",visible:!1,title:e.dates[2],"header-align":"center",width:"200"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[e.dates[2]&&n.changesList.length>0&&n.changesList.filter((function(t){return t.aae043==e.dates[2]})).length>0?a("span",[e._v(e._s(n.changesList.find((function(t){return t.aae043===e.dates[2]})).ape893||{}))]):a("span",[e._v("——")])]}}])}),a("ta-big-table-column",{attrs:{align:"center",field:"field4",visible:!1,title:e.dates[3],"header-align":"center",width:"200"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[e.dates[3]&&n.changesList.length>0&&n.changesList.filter((function(t){return t.aae043==e.dates[3]})).length>0?a("span",[e._v(e._s(n.changesList.find((function(t){return t.aae043===e.dates[3]})).ape893||{}))]):a("span",[e._v("——")])]}}])}),a("ta-big-table-column",{attrs:{align:"center",field:"field5",visible:!1,title:e.dates[4],"header-align":"center",width:"200"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[e.dates[4]&&n.changesList.length>0&&n.changesList.filter((function(t){return t.aae043==e.dates[4]})).length>0?a("span",[e._v(e._s(n.changesList.find((function(t){return t.aae043===e.dates[4]})).ape893||{}))]):a("span",[e._v("——")])]}}])}),a("ta-big-table-column",{attrs:{align:"center",field:"field6",visible:!1,title:e.dates[5],"header-align":"center",width:"200"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[e.dates[5]&&n.changesList.length>0&&n.changesList.filter((function(t){return t.aae043==e.dates[5]})).length>0?a("span",[e._v(e._s(n.changesList.find((function(t){return t.aae043===e.dates[5]})).ape893||{}))]):a("span",[e._v("——")])]}}])})],1),a("ta-big-table-column",{attrs:{title:"医院编码",sortable:"",field:"akb020",visible:!1,align:"center","header-align":"center","min-width":"100px"}}),a("ta-big-table-column",{attrs:{title:"医院名称",sortable:"",field:"akb021",visible:!1,align:"center","header-align":"center","min-width":"100px"}}),a("ta-big-table-column",{attrs:{title:"入院时间",sortable:"",field:"aae030",visible:!1,align:"center","header-align":"center","min-width":"100px"}}),a("ta-big-table-column",{attrs:{title:"出院时间",sortable:"",field:"aae031",visible:!1,align:"center","header-align":"center","min-width":"100px"}}),a("ta-big-table-column",{attrs:{title:"医保项目名称",sortable:"",field:"ake002",align:"center","header-align":"center","min-width":"140px"}}),a("ta-big-table-column",{attrs:{title:"院内项目名称",sortable:"",field:"ake006",align:"center","header-align":"center","min-width":"140px"}}),a("ta-big-table-column",{attrs:{title:"违反规则内容",sortable:"",field:"ydxx",align:"center","header-align":"center","min-width":"300px"}}),a("ta-big-table-column",{attrs:{title:"预审结果",sortable:"",field:"ape800",align:"center","header-align":"center","min-width":"100px"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[n.ape800?"0"==n.ape800?a("span",{staticStyle:{color:"#0f990f"}},[a("ta-icon",{attrs:{type:"check-circle",theme:"twoTone","two-tone-color":"#52c41a"}}),e._v("审核通过")],1):"1"==n.ape800?a("span",{staticStyle:{color:"#F59A23"}},[a("ta-icon",{attrs:{type:"question-circle",theme:"twoTone","two-tone-color":"#F59A23"}}),e._v("可疑提醒")],1):"2"==n.ape800?a("span",{staticStyle:{color:"#FF0000"}},[a("ta-icon",{attrs:{type:"exclamation-circle",theme:"twoTone","two-tone-color":"#FF0000"}}),e._v("违规提醒")],1):"3"==n.ape800?a("span",{staticStyle:{color:"#ffc49a"}},[a("ta-icon",{attrs:{type:"bell",theme:"twoTone",twoToneColor:"#e1ba91"}}),e._v("仅提醒")],1):e._e():a("span",[e._v("——")])]}}])}),a("ta-big-table-column",{attrs:{title:"开单科室",sortable:"",field:"aae386",align:"center","header-align":"center","min-width":"100px"}}),a("ta-big-table-column",{attrs:{title:"开单医师",sortable:"",field:"aac003",align:"center","header-align":"center","min-width":"100px"}}),a("ta-big-table-column",{attrs:{title:"执行科室",sortable:"",field:"sdksmc",align:"center","header-align":"center","min-width":"100px"}}),a("ta-big-table-column",{attrs:{title:"医保项目编码",sortable:"",field:"ake001",visible:!1,align:"center","header-align":"center","min-width":"140px"}}),a("ta-big-table-column",{attrs:{title:"院内项目编码",sortable:"",field:"akc515",visible:!1,align:"center","header-align":"center","min-width":"140px"}}),a("ta-big-table-column",{attrs:{title:"金额",sortable:"",field:"akb065",visible:!1,align:"center","header-align":"center",formatter:"formatAmount","min-width":"100px"}}),a("ta-big-table-column",{attrs:{title:"当日操作人",sortable:"",field:"ykz041",align:"center","header-align":"center","min-width":"120px"}}),a("ta-big-table-column",{attrs:{title:"操作来源",sortable:"",field:"continueflag",visible:!1,align:"center","header-align":"center","min-width":"100px"}}),a("ta-big-table-column",{attrs:{title:"操作时间",sortable:"",field:"oprtime",visible:!1,align:"center","header-align":"center","min-width":"100px"}}),a("ta-big-table-column",{attrs:{title:"就诊号",sortable:"",field:"akc190",visible:!1,align:"center","header-align":"center","min-width":"100px"}}),a("ta-big-table-column",{attrs:{title:"住院号",sortable:"",field:"akc191",visible:!1,align:"center","header-align":"center","min-width":"100px"}}),a("ta-big-table-column",{attrs:{title:"患者姓名",sortable:"",field:"name",visible:!1,align:"center","header-align":"center","min-width":"100px"}}),a("ta-big-table-column",{attrs:{title:"性別",sortable:"",field:"aac004",visible:!1,align:"center","collection-type":"SEX","header-align":"center","min-width":"100px"}}),a("ta-big-table-column",{attrs:{title:"年龄",sortable:"",field:"age",visible:!1,align:"center","header-align":"center","min-width":"100px"}}),a("ta-big-table-column",{attrs:{title:"来源",sortable:"",field:"type",visible:!1,align:"center","header-align":"center","collection-type":"AKA171","min-width":"100px"}}),a("ta-big-table-column",{attrs:{title:"险种类型",sortable:"",field:"aae140",visible:!1,align:"center","header-align":"center","collection-type":"AAE140","min-width":"100px"}}),a("ta-big-table-column",{attrs:{title:"规则大类",sortable:"",field:"aaa167",visible:!1,align:"center","header-align":"center","min-width":"100px"}}),a("ta-big-table-column",{attrs:{title:"院内审批标志",sortable:"",field:"ape896",visible:!1,align:"center","header-align":"center","min-width":"140px"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[null==n.ape896||""==n.ape896?a("span",[e._v("—")]):a("span",{domProps:{innerHTML:e._s(e.CollectionLabel("APE896",n.ape896))}})]}}])}),a("ta-big-table-column",{attrs:{title:"项目发生时间",sortable:"",field:"aae036",visible:!1,align:"center","header-align":"center","min-width":"140px"}}),a("ta-big-table-column",{attrs:{title:"单价",sortable:"",field:"akc225",visible:!1,align:"center","header-align":"center","min-width":"100px"}}),a("ta-big-table-column",{attrs:{title:"数量",sortable:"",field:"akc226",visible:!1,align:"center","header-align":"center","min-width":"100px"}}),a("ta-big-table-column",{attrs:{title:"违规数量",sortable:"",field:"ape805",visible:!1,align:"center","header-align":"center","min-width":"100px"}}),a("ta-big-table-column",{attrs:{title:"违规金额(元)",sortable:"",field:"ape804",visible:!1,align:"center","header-align":"center","min-width":"130px"}}),a("ta-big-table-column",{attrs:{title:"医疗费用总额(元)",sortable:"",field:"akc264",visible:!1,align:"center","header-align":"center","min-width":"150px"}}),a("ta-big-table-column",{attrs:{title:"审核编号",sortable:"",field:"aaz217",visible:!1,align:"center","header-align":"center","min-width":"100px"}}),a("ta-big-table-column",{attrs:{title:"知识元",sortable:"",field:"ykz018",visible:!1,align:"center","header-align":"center","min-width":"150px"}}),a("template",{slot:"bottomBar"},[a("ta-pagination",{ref:"gridPager",staticStyle:{"text-align":"right","margin-right":"120px"},attrs:{dataSource:e.tableData,defaultPageSize:100,pageSizeOptions:["100","200","500","1000","1500"],params:e.getParam,url:"nightAuditClinic/queryNightDataCompare"},on:{"update:dataSource":function(t){e.tableData=t},"update:data-source":function(t){e.tableData=t}}}),a("ta-button",{staticStyle:{position:"absolute",right:"20px",bottom:"12px"},attrs:{type:"primary",icon:"upload"},on:{click:e.exportExcel}},[e._v("导出 ")])],1)],2)],1),a("ta-tab-pane",{key:"2",attrs:{tab:"当日未审核患者",forceRender:""}},[a("ta-big-table",{ref:"infoTableRef2",attrs:{data:e.tableData2,height:"620px","export-config":{},"import-config":{},border:"","highlight-hover-row":"","highlight-current-row":"","auto-resize":"","show-overflow":"",resizable:"",size:"small"}},[a("ta-big-table-column",{attrs:{width:50,title:"序号",type:"seq"}}),a("ta-big-table-colgroup",{attrs:{align:"center","header-align":"center",title:"疑点操作变更情况跟踪"}},[a("ta-big-table-column",{attrs:{align:"center",field:"field1",title:e.dates[0],visible:!0,"header-align":"center",width:"200"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return["2"!=n.doubtType&&e.dates[0]&&n.changesList.length>0&&n.changesList.filter((function(t){return t.aae043==e.dates[0]})).length>0?a("span",[e._v(e._s(n.changesList.find((function(t){return t.aae043===e.dates[0]})).ape893||{}))]):a("span",[e._v("——")])]}}])}),a("ta-big-table-column",{attrs:{align:"center",field:"field2",title:e.dates[1],visible:!1,"header-align":"center",width:"200"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[e.dates[1]&&n.changesList.length>0&&n.changesList.filter((function(t){return t.aae043==e.dates[1]})).length>0?a("span",[e._v(e._s(n.changesList.find((function(t){return t.aae043===e.dates[1]})).ape893||{}))]):a("span",[e._v("——")])]}}])}),a("ta-big-table-column",{attrs:{align:"center",field:"field3",visible:!1,title:e.dates[2],"header-align":"center",width:"200"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[e.dates[2]&&n.changesList.length>0&&n.changesList.filter((function(t){return t.aae043==e.dates[2]})).length>0?a("span",[e._v(e._s(n.changesList.find((function(t){return t.aae043===e.dates[2]})).ape893||{}))]):a("span",[e._v("——")])]}}])}),a("ta-big-table-column",{attrs:{align:"center",field:"field4",visible:!1,title:e.dates[3],"header-align":"center",width:"200"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[e.dates[3]&&n.changesList.length>0&&n.changesList.filter((function(t){return t.aae043==e.dates[3]})).length>0?a("span",[e._v(e._s(n.changesList.find((function(t){return t.aae043===e.dates[3]})).ape893||{}))]):a("span",[e._v("——")])]}}])}),a("ta-big-table-column",{attrs:{align:"center",field:"field5",visible:!1,title:e.dates[4],"header-align":"center",width:"200"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[e.dates[4]&&n.changesList.length>0&&n.changesList.filter((function(t){return t.aae043==e.dates[4]})).length>0?a("span",[e._v(e._s(n.changesList.find((function(t){return t.aae043===e.dates[4]})).ape893||{}))]):a("span",[e._v("——")])]}}])}),a("ta-big-table-column",{attrs:{align:"center",field:"field6",visible:!1,title:e.dates[5],"header-align":"center",width:"200"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[e.dates[5]&&n.changesList.length>0&&n.changesList.filter((function(t){return t.aae043==e.dates[5]})).length>0?a("span",[e._v(e._s(n.changesList.find((function(t){return t.aae043===e.dates[5]})).ape893||{}))]):a("span",[e._v("——")])]}}])})],1),a("ta-big-table-column",{attrs:{title:"医院编码",sortable:"",field:"akb020",align:"center","header-align":"center","min-width":"100px"}}),a("ta-big-table-column",{attrs:{title:"医院名称",sortable:"",field:"akb021",align:"center","header-align":"center","min-width":"100px"}}),a("ta-big-table-column",{attrs:{title:"入院时间",sortable:"",field:"aae030",align:"center","header-align":"center","min-width":"100px"}}),a("ta-big-table-column",{attrs:{title:"出院时间",sortable:"",field:"aae031",align:"center","header-align":"center","min-width":"100px"}}),a("ta-big-table-column",{attrs:{title:"开单科室",sortable:"",field:"aae386",align:"center","header-align":"center","min-width":"100px"}}),a("ta-big-table-column",{attrs:{title:"开单医师",sortable:"",field:"aac003",align:"center","header-align":"center","min-width":"100px"}}),a("ta-big-table-column",{attrs:{title:"执行科室",sortable:"",field:"sdksmc",align:"center","header-align":"center","min-width":"100px"}}),a("ta-big-table-column",{attrs:{title:"医保项目编码",sortable:"",field:"ake001",align:"center","header-align":"center","min-width":"140px"}}),a("ta-big-table-column",{attrs:{title:"医保项目名称",sortable:"",field:"ake002",align:"center","header-align":"center","min-width":"140px"}}),a("ta-big-table-column",{attrs:{title:"院内项目编码",sortable:"",field:"akc515",align:"center","header-align":"center","min-width":"140px"}}),a("ta-big-table-column",{attrs:{title:"院内项目名称",sortable:"",field:"ake006",align:"center","header-align":"center","min-width":"140px"}}),a("ta-big-table-column",{attrs:{title:"预审结果",sortable:"",field:"ape800",align:"center","header-align":"center","min-width":"100px"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[n.ape800?"0"==n.ape800?a("span",{staticStyle:{color:"#0f990f"}},[a("ta-icon",{attrs:{type:"check-circle",theme:"twoTone","two-tone-color":"#52c41a"}}),e._v("审核通过")],1):"1"==n.ape800?a("span",{staticStyle:{color:"#F59A23"}},[a("ta-icon",{attrs:{type:"question-circle",theme:"twoTone","two-tone-color":"#F59A23"}}),e._v("可疑提醒")],1):"2"==n.ape800?a("span",{staticStyle:{color:"#FF0000"}},[a("ta-icon",{attrs:{type:"exclamation-circle",theme:"twoTone","two-tone-color":"#FF0000"}}),e._v("违规提醒")],1):"3"==n.ape800?a("span",{staticStyle:{color:"#ffc49a"}},[a("ta-icon",{attrs:{type:"bell",theme:"twoTone",twoToneColor:"#e1ba91"}}),e._v("仅提醒")],1):e._e():a("span",[e._v("——")])]}}])}),a("ta-big-table-column",{attrs:{title:"金额",sortable:"",field:"akb065",align:"center","header-align":"center",formatter:"formatAmount","min-width":"100px"}}),a("ta-big-table-column",{attrs:{title:"违反规则内容",sortable:"",field:"ydxx",align:"center","header-align":"center","min-width":"300px"}}),a("ta-big-table-column",{attrs:{title:"操作人",sortable:"",field:"ykz041",align:"center","header-align":"center","min-width":"100px"}}),a("ta-big-table-column",{attrs:{title:"操作来源",sortable:"",field:"continueflag",align:"center","header-align":"center","min-width":"100px"}}),a("ta-big-table-column",{attrs:{title:"操作时间",sortable:"",field:"oprtime",align:"center","header-align":"center","min-width":"100px"}}),a("ta-big-table-column",{attrs:{title:"就诊号",sortable:"",field:"akc190",align:"center","header-align":"center","min-width":"100px"}}),a("ta-big-table-column",{attrs:{title:"住院号",sortable:"",field:"akc191",align:"center","header-align":"center","min-width":"100px"}}),a("ta-big-table-column",{attrs:{title:"患者姓名",sortable:"",field:"name",align:"center","header-align":"center","min-width":"100px"}}),a("ta-big-table-column",{attrs:{title:"性別",sortable:"",field:"aac004",align:"center","collection-type":"SEX","header-align":"center","min-width":"100px"}}),a("ta-big-table-column",{attrs:{title:"年龄",sortable:"",field:"age",align:"center","header-align":"center","min-width":"100px"}}),a("ta-big-table-column",{attrs:{title:"来源",sortable:"",field:"type",align:"center","header-align":"center","collection-type":"AKA171","min-width":"100px"}}),a("ta-big-table-column",{attrs:{title:"险种类型",sortable:"",field:"aae140",align:"center","header-align":"center","collection-type":"AAE140","min-width":"100px"}}),a("ta-big-table-column",{attrs:{title:"规则大类",sortable:"",field:"aaa167",align:"center","header-align":"center","min-width":"100px"}}),a("ta-big-table-column",{attrs:{title:"院内审批标志",sortable:"",field:"ape896",align:"center","header-align":"center","min-width":"140px"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[null==n.ape896||""==n.ape896?a("span",[e._v("—")]):a("span",{domProps:{innerHTML:e._s(e.CollectionLabel("APE896",n.ape896))}})]}}])}),a("ta-big-table-column",{attrs:{title:"项目发生时间",sortable:"",field:"aae036",align:"center","header-align":"center","min-width":"140px"}}),a("ta-big-table-column",{attrs:{title:"单价",sortable:"",field:"akc225",align:"center","header-align":"center","min-width":"100px"}}),a("ta-big-table-column",{attrs:{title:"数量",sortable:"",field:"akc226",align:"center","header-align":"center","min-width":"100px"}}),a("ta-big-table-column",{attrs:{title:"违规数量",sortable:"",field:"ape805",align:"center","header-align":"center","min-width":"100px"}}),a("ta-big-table-column",{attrs:{title:"违规金额(元)",sortable:"",field:"ape804",align:"center","header-align":"center","min-width":"130px"}}),a("ta-big-table-column",{attrs:{title:"医疗费用总额(元)",sortable:"",field:"akc264",align:"center","header-align":"center","min-width":"150px"}}),a("ta-big-table-column",{attrs:{title:"审核编号",sortable:"",field:"aaz217",align:"center","header-align":"center","min-width":"100px"}}),a("ta-big-table-column",{attrs:{title:"知识元",sortable:"",field:"ykz018",align:"center","header-align":"center","min-width":"150px"}}),a("template",{slot:"bottomBar"},[a("ta-pagination",{ref:"gridPager2",staticStyle:{"text-align":"right","margin-right":"120px"},attrs:{dataSource:e.tableData2,defaultPageSize:100,pageSizeOptions:["100","200","500","1000","1500"],params:e.getParam,url:"nightAuditClinic/queryNightDscgCompare"},on:{"update:dataSource":function(t){e.tableData2=t},"update:data-source":function(t){e.tableData2=t}}}),a("ta-button",{staticStyle:{position:"absolute",right:"20px",bottom:"12px"},attrs:{type:"primary",icon:"upload"},on:{click:e.exportExcel2}},[e._v("导出 ")])],1)],2)],1)],1)],1)],1)],1)},i=[],l=a(66347),r=a(48534),o=a(95082),s=(a(36133),a(88412)),c=a(22722),u=a(55115),d=a(83231);u.w3.prototype.Base=Object.assign(u.w3.prototype.Base,(0,o.Z)({},c.Z));var f={name:"nightDataCompare",components:{TaTitle:s.Z},data:function(){return{tableData:[],tableData2:[],activeKey:"1",defaultValue:TaUtils.getMoment((new Date).toISOString().slice(0,10),"YYYY-MM-DD"),showHiddenOrSortColumn:{themeColor:"red",icon:"appstore",showHiddenConfig:{open:!0,disabledHiddenColumns:function(e){return!e.title||["疑点操作变更情况跟踪"].includes(e.title)}},columnSortConfig:{open:!0,onMove:function(e,t){t.dragColumn;var a=t.dropColumn;return!("seq"==a.type||"疑点操作变更情况跟踪"==a.title||"ape893Type"==a.property||"doubtType"==a.property)},dragEnd:function(e,t){t.dragColumn,t.resultColumns}},successCallback:this.fnSaveTableTitle,disabledControlCol:function(e){}},dates:[TaUtils.momentToString(TaUtils.getMoment((new Date).toISOString().slice(0,10),"YYYY-MM-DD"),"YYYY-MM-DD")],isAdd:!0,compareDayInitValue:2,fileList:[],headers:{authorization:"authorization-text"},drawerVisible:!1,selectedData:{}}},created:function(){var e=this;return(0,r.Z)(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$nextTick((function(){e.fnQueryTableTitle()}));case 2:case"end":return t.stop()}}),t)})))()},mounted:function(){var e=this;this.$nextTick((function(){e.queryTableData()}))},methods:{callback:function(e){this.activeKey=e,this.queryTableData()},fnQueryTableTitle:function(){var e=this,t=top.indexTool.getActiveTabMenuId(),a=top.indexTool.getUserInfo().loginId,n=[];this.Base.submit(null,{url:"miimCommonRead/selectTableColumShow",data:{resourceid:t,loginid:a},autoValid:!1,showPageLoading:!1},{successCallback:function(t){if(t.data.list.length>0&&t.data.list[0].colum){var a=JSON.parse(t.data.list[0].colum),i=e.$refs.infoTableRef.getTableColumn().collectColumn;n=a.map((function(e){return e.title}));var l=[];i.forEach((function(e){if("checkbox"==e.type)l.push(e);else{var n=a.find((function(t){return t.title===e.title}));n&&(e.visible=n.visible,e.width=n.width);var i=e;i.sortable?i.width=20*i.title.length+30:i.width=20*i.title.length+10,"操作"==i.title&&(i.width=150),"违反规则内容"==i.title&&(i.width=300),t.data.akc191Title.length>0&&"akc191"===i.property&&(i.title=t.data.akc191Title[0].label),l.push(i)}})),n.length>0&&l.sort((function(e,t){return"序号"===e.title?-1:"序号"===t.title?1:n.indexOf(e.title)-n.indexOf(t.title)})),e.$refs.infoTableRef.loadColumn(l)}},failCallback:function(t){e.$message.error("查询表头失败")}})},fnSaveTableTitle:function(e){var t=this,a=(e.table,e.resultColumnsList),n=a,i=[];n.forEach((function(e){var t=e.title,a=e.visible;e.type;t&&i.push({title:t,visible:a})}));var l=top.indexTool.getActiveTabMenuId(),r=top.indexTool.getUserInfo().loginId,o={colum:JSON.stringify(i),flag:"column",resourceid:l,loginid:r};d.Z.insertTableColumShow(o,(function(e){"success"===e?t.$message.success("保存成功"):t.$message.error("保存失败")}))},getPreviousDates:function(e,t){for(var a=[],n=new Date(e),i=0;i<t;i++){var l=new Date(n);l.setDate(n.getDate()-i);var r=l.getFullYear(),o=String(l.getMonth()+1).padStart(2,"0"),s=String(l.getDate()).padStart(2,"0");a.push("".concat(r,"-").concat(o,"-").concat(s))}return a},onChange:function(e){this.compareDayInitValue=e>5?6:e+1},beforeUpload:function(e){if(e.size/1024>51200)return this.$message.warning("请选择不超过50M的文件"),!1;var t=e.name.split("."),a=t[t.length-1];return-1===["xls","xlsx","csv"].indexOf(a)?(this.$message.warning("只能上传excel文件"),!1):(this.fileList=[e],this.baseInfoForm.setFieldsValue({fileName:e.name}),!1)},editIsActive:function(e){return this.$refs.infoTableRef.isActiveByRow(e)},editActive:function(e){this.$refs.infoTableRef.setActiveRow(e)},editCancel:function(e){var t=this;this.$refs.infoTableRef.revertData(e).then((function(){t.$refs.infoTableRef.clearActived()}))},fnDelete:function(e){var t=this;this.Base.submit(null,{url:"diseaseManage/deleteById",data:e,autoValid:!1},{successCallback:function(e){t.$message.success("删除成功！"),t.queryTableData()},failCallback:function(e){t.$message.error("删除失败！")}})},editSave:function(e,t){var a=this;this.$refs.infoTableRef.validate(e).then((function(){a.$refs.infoTableRef.isUpdateByRow(e)?(a.Base.submit(null,{url:"diseaseManage/editDiseaseItemInfo",data:e,autoValid:!1},{successCallback:function(e){a.$message.success("保存成功！"),a.queryTableData()},failCallback:function(e){a.$message.error("保存失败！")}}),a.$refs.infoTableRef.clearActived()):message.info("数据未有改动")})).catch((function(e){e&&message.error("校验不通过！")}))},handleChange:function(e){var t=this,a=this.baseInfoForm.getFieldsValue();a.file=e.file,this.Base.submit(null,{url:"/diseaseManage/importExcel",data:a,autoQs:!1,isFormData:!0},{successCallback:function(e){t.baseInfoForm.resetFields(),t.$message.success("导入数据成功"),t.queryTableData()},failCallback:function(e){}})},queryTableData:function(){var e=this,t=this.baseInfoForm.getFieldsValue();if(this.dates=this.getPreviousDates(t.aae043.format("YYYY-MM-DD"),6),"1"==this.activeKey){this.$refs.infoTableRef.refreshColumn();for(var a=1;a<=6;a++)this.compareDayInitValue>=a?this.$refs.infoTableRef.showColumn(this.$refs.infoTableRef.getColumnByField("field"+a)):this.$refs.infoTableRef.hideColumn(this.$refs.infoTableRef.getColumnByField("field"+a))}else{this.$refs.infoTableRef2.refreshColumn();for(var n=1;n<=6;n++)this.compareDayInitValue>=n?this.$refs.infoTableRef2.showColumn(this.$refs.infoTableRef2.getColumnByField("field"+n)):this.$refs.infoTableRef2.hideColumn(this.$refs.infoTableRef2.getColumnByField("field"+n))}this.$nextTick((function(){"1"==e.activeKey?e.$refs.gridPager.loadData():e.$refs.gridPager2.loadData()}))},fnReset:function(){this.baseInfoForm.resetFields(),this.onChange(1)},autoFormCreate:function(e){this.baseInfoForm=e},getParam:function(){var e=this.baseInfoForm.getFieldsValue();if(void 0!==e.aae043)return e.aae043end=e.aae043.format("YYYY-MM-DD 00:00:00"),e.aae043=e.aae043.format("YYYYMMDD"),e.compareDay=this.compareDayInitValue,e;this.$message.error("请选择审核时间！")},numberToExcelColumn:function(e){var t="";while(e>0){var a=(e-1)%26;t=String.fromCharCode(65+a)+t,e=Math.floor((e-1)/26)}return t},exportExcel1:function(){var e,t=this,a=[],n=this.$refs.infoTableRef.getColumns(),i=(0,l.Z)(n);try{for(i.s();!(e=i.n()).done;){var r=e.value;"序号"!==r.title&&"医保限制条件"!==r.title&&"更新时间"!==r.title&&"操作"!==r.title&&a.push({headers:r.title,key:r.property,width:20})}}catch(f){i.e(f)}finally{i.f()}var o=[],s=[];s.push("疑点变化类型","医师操作变化");for(var c=0;c<this.compareDayInitValue;c++)s.push(0===c?"疑点操作变更情况跟踪":void 0);var u=s.concat(a.slice(2+this.compareDayInitValue,a.length).map((function(e){return e.headers})));o.push(u),o.push(a.map((function(e){return e.headers})));var d={fileName:"病种项目匹配导入模板表",sheets:[{name:"worksheet1",column:{complex:!0,rowValues:o,rowHeight:[20,20],columns:a},mergeCell:function(e){e.mergeCells("A1:A2"),e.mergeCells("B1:B2"),e.mergeCells(t.numberToExcelColumn(3)+"1:"+t.numberToExcelColumn(3+t.compareDayInitValue)+"1");for(var n=1;n<=a.length;n++)e.mergeCells(t.numberToExcelColumn(n+2+t.compareDayInitValue)+"1:"+t.numberToExcelColumn(n+2+t.compareDayInitValue)+"2")},rows:[]}]};this.Base.generateExcel(d)},exportExcel:function(){var e,t=this,a=this,n=this.getParam(),i=[],r=this.$refs.infoTableRef.getColumns(),o=(0,l.Z)(r);try{for(o.s();!(e=o.n()).done;){var s=e.value;"seq"!==s.type&&"checkbox"!==s.type&&"operate"!==s.property&&!1!==s.visible&&i.push({headers:s.title,key:s.property,width:20})}}catch(m){o.e(m)}finally{o.f()}var c=[],u=[];u.push("疑点变化类型"),u.push("医师操作变化");for(var d=0;d<this.compareDayInitValue;d++)u.push(0===d?"疑点操作变更情况跟踪":void 0);var f=u.concat(i.slice(2+this.compareDayInitValue,i.length).map((function(e){return e.headers})));c.push(f),c.push(i.map((function(e){return e.headers})));var p=r.map((function(e){return e.property})),g=[{codeType:"AAC004",columnKey:"aac004"},{codeType:"AAE140",columnKey:"aae140"},{codeType:"AKA171",columnKey:"type"},{columnKey:"ape896",customCollection:function(e,t){"0"==e.value||"1"==e.value?e.value="—":"2"==e.value?e.value="自费":"3"==e.value?e.value="报销":(""==e.value||e.value,e.value="—")}},{columnKey:"ape893Type",customCollection:function(e,t){"0"==e.value?e.value="操作沿用":"1"==e.value?e.value="仍然无操作":"2"==e.value?e.value="已有新操作":(""==e.value||e.value,e.value="—")}},{columnKey:"doubtType",customCollection:function(e,t){"1"==e.value?e.value="旧疑点":"0"==e.value?e.value="新增疑点":"2"==e.value?e.value="已合规":(""==e.value||e.value,e.value="—")}},{columnKey:"ape800",customCollection:function(e,t){"0"==e.value?e.value="审核通过":"1"==e.value?e.value="可疑提醒":"2"==e.value?e.value="违规提醒":"3"==e.value&&(e.value="仅提醒")}},{columnKey:"ape893",customCollection:function(e,t){var a=document.createElement("div");a.innerHTML=e.value;var n=a.innerText||a.textContent;a=null,e.value=n}}],h=g.filter((function(e){return p.includes(e.columnKey)}));this.Base.submit(null,{url:"nightAuditClinic/exportExcelCompare",data:n,autoValid:!1},{successCallback:function(e){var l=e.data.data;l.forEach((function(e){e.ape805&&e.akc225&&(e.ape804=e.ape805*e.akc225),e.ape893real.indexOf("1")>-1&&(e.aaz560_1=e.ape893.split(/[:：]/)[1],e.ape893=e.ape893.split(/[:：]/)[0]),e.ape893real.indexOf("4")>-1&&(e.aaz560_1=e.ape893.split(/[:：]/)[1],e.ape893=e.ape893.split(/[:：]/)[0]),e.doubtType&&"2"===e.doubtType&&(e.ape893Type="——");for(var a=function(a){e.changesList.length>0&&e.changesList.map((function(e){return e.aae043})).includes(t.dates[a-1])?e["field"+a]=e.changesList.filter((function(e){return e.aae043==t.dates[a-1]}))[0].ape893:e["field"+a]="——"},n=1;n<=t.dates.length;n++)a(n)}));var r={fileName:"每晚预审审核数据对比表("+n.aae043+")",sheets:[{name:"worksheet1",column:{complex:!0,rowValues:c,rowHeight:[20,20],columns:i,headerStyle:{cellStyle:function(e,t,a,n,i){e.fill={type:"pattern",pattern:"solid",fgColor:{argb:"D3D3D3"}},e.alignment={vertical:"middle",horizontal:"center"},e.font={bold:!0}}}},mergeCell:function(e){e.mergeCells("A1:A2"),e.mergeCells("B1:B2"),e.mergeCells(a.numberToExcelColumn(3)+"1:"+a.numberToExcelColumn(2+a.compareDayInitValue)+"1");for(var t=1;t<=i.length;t++)e.mergeCells(a.numberToExcelColumn(t+2+a.compareDayInitValue)+"1:"+a.numberToExcelColumn(t+2+a.compareDayInitValue)+"2")},rows:l,codeList:h,dataStyle:{cellStyle:function(e,t,a,n){e.alignment={vertical:"middle",horizontal:"center"}}}}]};t.Base.generateExcel(r)},failCallback:function(e){t.$message.error("导出失败")}})},exportExcel2:function(){var e,t=this,a=this,n=this.getParam(),i=[],r=this.$refs.infoTableRef2.getColumns(),o=(0,l.Z)(r);try{for(o.s();!(e=o.n()).done;){var s=e.value;"seq"!==s.type&&"checkbox"!==s.type&&"operate"!==s.property&&!1!==s.visible&&i.push({headers:s.title,key:s.property,width:20})}}catch(m){o.e(m)}finally{o.f()}for(var c=[],u=[],d=0;d<this.compareDayInitValue;d++)u.push(0===d?"疑点操作变更情况跟踪":void 0);var f=u.concat(i.slice(this.compareDayInitValue,i.length).map((function(e){return e.headers})));c.push(f),c.push(i.map((function(e){return e.headers})));var p=r.map((function(e){return e.property})),g=[{codeType:"AAC004",columnKey:"aac004"},{codeType:"AAE140",columnKey:"aae140"},{codeType:"AKA171",columnKey:"type"},{columnKey:"ape896",customCollection:function(e,t){"0"==e.value||"1"==e.value?e.value="—":"2"==e.value?e.value="自费":"3"==e.value?e.value="报销":""!=e.value&&null!=e.value||(e.value="—")}},{columnKey:"ape893Type",customCollection:function(e,t){"0"==e.value?e.value="操作沿用":"1"==e.value?e.value="仍然无操作":"2"==e.value?e.value="已有新操作":""!=e.value&&null!=e.value||(e.value="—")}},{columnKey:"doubtType",customCollection:function(e,t){"1"==e.value?e.value="旧疑点":"0"==e.value?e.value="新增疑点":"2"==e.value?e.value="已合规":""!=e.value&&null!=e.value||(e.value="—")}},{columnKey:"ape800",customCollection:function(e,t){"0"==e.value?e.value="审核通过":"1"==e.value?e.value="可疑提醒":"2"==e.value?e.value="违规提醒":"3"==e.value&&(e.value="仅提醒")}},{columnKey:"ape893",customCollection:function(e,t){var a=document.createElement("div");a.innerHTML=e.value;var n=a.innerText||a.textContent;a=null,e.value=n}}],h=g.filter((function(e){return p.includes(e.columnKey)}));this.Base.submit(null,{url:"nightAuditClinic/exportDscgCompare",data:n,autoValid:!1},{successCallback:function(e){var l=e.data.data;l.forEach((function(e){e.ape805&&e.akc225&&(e.ape804=e.ape805*e.akc225),e.ape893real.indexOf("1")>-1&&(e.aaz560_1=e.ape893.split(/[:：]/)[1],e.ape893=e.ape893.split(/[:：]/)[0]),e.ape893real.indexOf("4")>-1&&(e.aaz560_1=e.ape893.split(/[:：]/)[1],e.ape893=e.ape893.split(/[:：]/)[0]);for(var a=function(a){e.changesList.length>0&&e.changesList.map((function(e){return e.aae043})).includes(t.dates[a-1])?e["field"+a]=e.changesList.filter((function(e){return e.aae043==t.dates[a-1]}))[0].ape893:e["field"+a]="——"},n=1;n<=t.dates.length;n++)a(n)}));var r={fileName:"每晚预审患者出院对比表("+n.aae043+")",sheets:[{name:"worksheet1",column:{complex:!0,rowValues:c,rowHeight:[20,20],columns:i,headerStyle:{cellStyle:function(e,t,a,n,i){e.fill={type:"pattern",pattern:"solid",fgColor:{argb:"D3D3D3"}},e.alignment={vertical:"middle",horizontal:"center"},e.font={bold:!0}}}},mergeCell:function(e){e.mergeCells(a.numberToExcelColumn(1)+"1:"+a.numberToExcelColumn(a.compareDayInitValue)+"1");for(var t=1;t<=i.length;t++)e.mergeCells(a.numberToExcelColumn(t+a.compareDayInitValue)+"1:"+a.numberToExcelColumn(t+a.compareDayInitValue)+"2")},rows:l,codeList:h,dataStyle:{cellStyle:function(e,t,a,n){e.alignment={vertical:"middle",horizontal:"center"}}}}]};t.Base.generateExcel(r)},failCallback:function(e){t.$message.error("导出失败")}})},activeMethod:function(e){var t=e.row,a=(e.rowIndex,e.column,e.columnIndex);return 4!==a||4===a&&"2"===t.columnType},saveConfig:function(){var e=this,t=this.form_edit.getFieldsValue();this.isAdd?Base.submit(this.form_edit,{url:"addDemoModel",method:"post",data:t,autoValid:!0}).then((function(t){e.$message.success("新增成功",3),e.queryTableData(),e.drawerVisible=!1})):(t.configId=this.selectedData.configId,Base.submit(this.form_edit,{url:"updateDemoRecord",method:"post",data:t,autoValid:!0}).then((function(t){e.$message.success("修改成功",3),e.queryTableData(),e.drawerVisible=!1})))}}},p=f,g=a(1001),h=(0,g.Z)(p,n,i,!1,null,"eb988654",null),m=h.exports},36766:function(e,t,a){"use strict";var n=a(66586);t["Z"]=n.Z},26263:function(e,t,a){"use strict";a.d(t,{s:function(){return n},x:function(){return i}});var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:e.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[e._v(e._s(e.title))]),e._t("default")],2)},i=[]},66586:function(e,t){"use strict";var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};t["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}},83231:function(e,t,a){"use strict";var n=a(48534);a(36133);function i(e){return e?e.akb020&&e.aaz263?"personal":e.akb020&&e.aaz309&&e.aaz307?"group":e.akb020&&e.aaz307?"department":e.akb020?"hospital":"all":"all"}function l(e){return r.apply(this,arguments)}function r(){return r=(0,n.Z)(regeneratorRuntime.mark((function e(t){var a,n,l,r,o,s,c,u,d,f,p,g;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,Base.submit(null,{url:"/miimCommonRead/queryDataPermission",showPageLoading:!1,data:{jobnumber:t},autoValid:!0});case 2:return a=e.sent,n=new Set,l=new Set,a.data.permission.forEach((function(e){var t=i(e);"hospital"===t&&n.add(e.akb020),"department"===t&&l.add(e.aaz307)})),r=a.data.permission.filter((function(e){return"department"===i(e)||!l.has(e.aaz307)})).filter((function(e){return"hospital"===i(e)||!n.has(e.akb020)})),o=new Set(r.map((function(e){return e.akb020})).filter((function(e){return null!=e}))),s=new Set(r.map((function(e){return e.aaz307})).filter((function(e){return null!=e}))),c=new Set(r.map((function(e){return e.aaz309})).filter((function(e){return null!=e}))),u=new Set(r.map((function(e){return e.aaz263})).filter((function(e){return null!=e}))),d=!1,f=!1,p=!1,g=!1,1===o.size&&(d=!0),1===s.size&&1===o.size&&(f=!0),1===s.size&&1===o.size&&1===c.size&&(p=!0),1===o.size&&0===s.size&&1===u.size&&(g=!0),e.abrupt("return",{akb020Set:o,aaz307Set:s,aaz263Set:u,aaz309Set:c,akb020Disable:d,aaz307Disable:f,aaz263Disable:g,aaz309Disable:p});case 20:case"end":return e.stop()}}),e)}))),r.apply(this,arguments)}function o(e,t){Base.submit(null,{url:"miimCommonRead/getAa01AAE500StartStop",data:e},{successCallback:function(e){return t(e)}})}function s(e,t){Base.submit(null,{url:"miimCommonRead/insertTableColumShow",data:e,autoValid:!1},{successCallback:function(e){return t("success")},failCallback:function(e){return t("fail")}})}var c={zbx01:{footer:function(){return"统筹金总额"}},zbx02:{footer:function(){return"基金支出总额"}},zbx03:{footer:function(){return"总费用"}},zbx04:{footer:function(){return"总住院日"}}};t["Z"]={permissionCheck:l,getAa01AAE500StartStop:o,insertTableColumShow:s,props:c,moneyNumFormat:function(e){var t=e.cellValue;return t||"—"}}},55382:function(){},61219:function(){}}]);
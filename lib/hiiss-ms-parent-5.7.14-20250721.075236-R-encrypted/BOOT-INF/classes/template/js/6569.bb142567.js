(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[6569],{88412:function(e,t,a){"use strict";var l=a(26263),i=a(36766),n=a(1001),r=(0,n.Z)(i.Z,l.s,l.x,!1,null,"5e7ef0ae",null);t["Z"]=r.exports},82877:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return k}});var l=function(){var e=this,t=this,a=t.$createElement,l=t._self._c||a;return l("div",{staticClass:"fit"},[l("ta-border-layout",{attrs:{layout:{header:"180px",footer:"60px"}}},[l("div",{attrs:{slot:"header"},slot:"header"},[l("ta-title",{attrs:{title:"查询条件"}}),l("ta-form",{staticStyle:{"padding-top":"5px"},attrs:{autoFormCreate:function(t){e.queryParamForm=t},layout:"horizontal",formLayout:!0}},[l("ta-form-item",{attrs:{"field-decorator-id":"ykz108",label:"规则运行场景",span:6,labelCol:{span:8},wrapperCol:{span:16},required:""}},[l("ta-select",{attrs:{placeholder:"规则运行场景筛选",dropdownMatchSelectWidth:!1,dropdownStyle:{width:"250px"},allowClear:"",options:t.ykz108Options,optionsKey:{value:"id",label:"name"},showSearch:"",optionFilterProp:"children"},on:{select:t.fnSelectYkz108}})],1),l("ta-form-item",{attrs:{"field-decorator-id":"aae043",label:"期号",span:6,labelCol:{span:8},wrapperCol:{span:16},hidden:!0}},[l("ta-select",{attrs:{"options-key":{value:"id",label:"name"},options:t.aae043Options}})],1),l("ta-form-item",{attrs:{"field-decorator-id":"ake002",label:"医保项目名称",span:6,labelCol:{span:8},wrapperCol:{span:16}}},[l("ta-input",{attrs:{placeholder:"请输入医保项目名称",allowClear:""}})],1),l("ta-form-item",{attrs:{"field-decorator-id":"aaz319",label:"规则大类",span:6,labelCol:{span:8},wrapperCol:{span:16}}},[l("ta-select",{attrs:{showSearch:"",placeholder:"规则大类筛选",allowClear:"","options-key":{value:"id",label:"name"},options:t.aaz319Options}})],1),l("ta-form-item",{attrs:{"field-decorator-id":"aae100",label:"是否启用",span:6,labelCol:{span:14},wrapperCol:{span:8},required:""}},[l("ta-radio-group",{attrs:{"collection-type":"YESORNO",defaultValue:"1"},on:{change:t.fnChangeRadio}})],1),l("ta-form-item",{attrs:{"field-decorator-id":"ykz018",label:"限制条件",span:6,labelCol:{span:8},wrapperCol:{span:16}}},[l("ta-input",{attrs:{placeholder:"请输入限制条件",allowClear:""}})],1),l("ta-form-item",{attrs:{"field-decorator-id":"ake001",label:"医保项目编码",span:6,labelCol:{span:8},wrapperCol:{span:16}}},[l("ta-input",{attrs:{placeholder:"请输入医保项目编码",allowClear:""}})],1),l("div",{staticStyle:{float:"right","margin-right":"80px"},attrs:{span:6}},[l("ta-button",{staticStyle:{margin:"0 20px 0 60px"},attrs:{type:"primary",icon:"search"},on:{click:t.fnQuery}},[t._v("查询")])],1)],1)],1),l("div",{staticClass:"fit",staticStyle:{position:"relative",overflow:"hidden"},attrs:{id:"parentDom"}},[l("ta-drawer",{attrs:{placement:"right",width:"500",closable:!1,visible:t.drawerVisible,"get-container":t.setContainer,"wrap-style":{position:"absolute"}},on:{close:t.onClose}},[l("ta-label-con",{attrs:{label:"启停历史记录"}}),t.ruleLogList.length>0?l("div",[l("ta-timeline",{attrs:{mode:"alternate"}},t._l(t.ruleLogList,(function(e,a){return l("ta-timeline-item",{key:a,attrs:{color:"0"===e.operatetype?"red":"green"}},[t._v(" "+t._s(e.combined_value)+" ")])})),1)],1):l("div",[l("div",{staticStyle:{"text-align":"center",padding:"20px"}},[t._v("暂无启停历史记录")])])],1),l("ta-title",{attrs:{title:"规则信息"}},[l("div",{staticStyle:{float:"right"}},[l("ta-button",{attrs:{type:"primary",hidden:"true"},on:{click:t.addRule}},[t._v("新增规则")]),l("ta-button",{attrs:{type:"primary",hidden:"true"},on:{click:t.deleteRule}},[t._v("删除规则")])],1)]),l("ta-tabs",{attrs:{defaultActiveKey:"1",activeKey:this.activeKey.toString(),type:"card"},on:{change:t.fnChangeTab}},[l("ta-tab-pane",{key:"1",attrs:{tab:"规则列表",forceRender:!0}},[l("ta-table",{ref:"ruleTable",staticStyle:{height:"470px"},attrs:{columns:t.ruleColumns,"data-source":t.ruleData,scroll:{x:"101%",y:"100%"},haveSn:!0,rowSelection:{selectedRowKeys:t.selectedRowKeys,onChange:t.fnOnChange}},on:{"update:columns":function(e){t.ruleColumns=e}},scopedSlots:t._u([{key:"action",fn:function(e,a){return l("a",{},[l("span",{directives:[{name:"show",rawName:"v-show",value:0==t.radioValue,expression:"radioValue==0"}],on:{click:function(e){return t.fnCodeStopStart(a)}}},[t._v("启用编码 | ")]),l("span",{directives:[{name:"show",rawName:"v-show",value:1==t.radioValue,expression:"radioValue==1"}],on:{click:function(e){return t.fnCodeStopStart(a)}}},[t._v("停用编码 | ")]),l("span",{directives:[{name:"show",rawName:"v-show",value:0==t.radioValue,expression:"radioValue==0"}],on:{click:function(e){return t.fnYkz032StopStart(a)}}},[t._v(" 启用规则 | ")]),l("span",{directives:[{name:"show",rawName:"v-show",value:1==t.radioValue,expression:"radioValue==1"}],on:{click:function(e){return t.fnYkz032StopStart(a)}}},[t._v(" 停用规则 | ")]),l("span",{on:{click:function(e){return t.showDrawer(a,"1")}}},[t._v(" 查看历史")])])}}])})],1),l("ta-tab-pane",{key:"2",attrs:{tab:"单据类规则列表",forceRender:!0}},[l("ta-table",{ref:"billRuleTable",staticStyle:{height:"500px"},attrs:{columns:t.billRuleColumns,"data-source":t.ruleData,scroll:{y:"100%"},haveSn:!0,rowSelection:{selectedRowKeys:t.selectedRowKeys,onChange:t.fnOnChange}},on:{"update:columns":function(e){t.billRuleColumns=e}},scopedSlots:t._u([{key:"action",fn:function(e,a){return l("a",{},[l("span",{directives:[{name:"show",rawName:"v-show",value:0==t.radioValue,expression:"radioValue==0"}],on:{click:function(e){return t.fnCodeStopStart(a)}}},[t._v("启用 | ")]),l("span",{directives:[{name:"show",rawName:"v-show",value:1==t.radioValue,expression:"radioValue==1"}],on:{click:function(e){return t.fnCodeStopStart(a)}}},[t._v("停用 | ")]),l("span",{on:{click:function(e){return t.showDrawer(a,"2")}}},[t._v(" 查看历史")])])}}])})],1)],1)],1),l("div",{attrs:{slot:"footer"},slot:"footer"},[l("ta-pagination",{style:{position:"absolute",right:0==t.radioValue?"120px":"0px",bottom:"5px"},attrs:{total:t.total,defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"]},on:{showSizeChange:t.pageSizeChange,change:t.pageChange},model:{value:t.page,callback:function(e){t.page=e},expression:"page"}}),0==t.radioValue?l("ta-button",{staticStyle:{position:"absolute",right:"0",bottom:"5px"},attrs:{type:"primary",icon:"upload"},on:{click:t.exportExcel}},[t._v("导出 ")]):t._e()],1)])],1)},i=[],n=a(56664),r=a(66347),s=a(95082),o=a(88412),u=a(22722),c=a(55115);c.w3.prototype.Base=Object.assign(c.w3.prototype.Base,(0,s.Z)({},u.Z));var d=[{title:"医保项目名称",dataIndex:"ake002",width:150,align:"left",overflowTooltip:!0},{title:"规则序号",dataIndex:"ykz032",width:120,align:"center",overflowTooltip:!0},{title:"关系序号",dataIndex:"ykz033",width:120,align:"center",overflowTooltip:!0},{title:"医保项目编码",dataIndex:"ake001",width:160,align:"left",overflowTooltip:!0},{title:"知识元",dataIndex:"ykz018",width:300,align:"center",overflowTooltip:!0},{title:"引导信息",dataIndex:"ykz266",width:300,align:"center",overflowTooltip:!0},{title:"审核标准",dataIndex:"ykz019",width:150,align:"center",overflowTooltip:!0},{title:"开始时间",dataIndex:"ruleStartTime",width:150,align:"center",overflowTooltip:!0},{title:"结束时间",dataIndex:"ruleEndTime",width:150,align:"center",overflowTooltip:!0},{title:"新老规则",dataIndex:"ykz083",width:120,align:"center",overflowTooltip:!0},{title:"是否启用",dataIndex:"aae100",width:100,align:"center",collectionType:"YESORNO",overflowTooltip:!0},{title:"规则运行场景",dataIndex:"ykz108",width:120,align:"center",overflowTooltip:!0},{title:"规则运行场景",dataIndex:"ykz109",width:120,align:"center",overflowTooltip:!0},{title:"分组编号",dataIndex:"ape864",width:120,align:"center",overflowTooltip:!0},{title:"分组名称",dataIndex:"ape865",width:120,align:"center",overflowTooltip:!0}],h=[{title:"规则序号",dataIndex:"ykz032",width:120,align:"center",overflowTooltip:!0},{title:"关系序号",dataIndex:"ykz033",width:120,align:"center",overflowTooltip:!0},{title:"限制条件",dataIndex:"ykz018",width:450,align:"left",overflowTooltip:!0},{title:"是否启用",dataIndex:"aae100",width:80,align:"center",collectionType:"YESORNO",overflowTooltip:!0}],p={name:"ruleStartStopSimplify",components:{TaTitle:o.Z},data:function(){return{ruleColumns:d,billRuleColumns:h,ykz108Options:[],ykz108OptionsDj:[],ykz108OptionsYz:[],aae043Options:[],aaz319Options:[],ruleData:[],ruleLogList:[],activeKey:1,enable:!1,hidden:!1,drawerVisible:!1,textareaValue:"",ykz108SelectValue:[],bfrecord:{},bftype:0,checkboxValue:!1,selectedRows:[],selectedRowKeys:[],pageSize:30,total:0,page:1,dataList:[],radioValue:1,twoHidden:!1}},mounted:function(){this.$refs.ruleTable.hideColumns(["ykz032","ykz033","ykz019","ykz083","ykz108","ykz109","ape864","ape865","gzlb"]),this.$refs.billRuleTable.hideColumns(["ykz032","ykz033"]),this.fnQueryYkz108()},methods:{exportExcel:function(){var e,t=this,a=this.ykz108Options.find((function(e){return e.id===t.queryParamForm.getFieldValue("ykz108")})),l=[],i=this.twoHidden?this.billRuleColumns:this.ruleColumns,n=(0,r.Z)(i);try{for(n.s();!(e=n.n()).done;){var s=e.value;"序号"!==s.title&&"操作"!==s.title&&l.push({header:s.title,key:s.dataIndex,width:20})}}catch(u){n.e(u)}finally{n.f()}var o={fileName:a.name+"场景规则启停结果表",sheets:[{name:"worksheet1",column:{complex:!1,columns:l},rows:this.dataList,codeList:[{columnKey:"aae100",customCollection:function(e){"0"==e.value?e.value="停用":e.value="启用"}}]}]};this.Base.generateExcel(o)},setContainer:function(){return document.getElementById("parentDom")},showDrawer:function(e,t){var a=this,l=this.queryParamForm.getFieldValue("ykz108");this.drawerVisible=!0;var i={url:"ruleConfig/queryRuleLog",data:{ykz032:e.ykz032,ake001:e.ake001,ruleType:t,ykz108:e.ykz108?e.ykz108:l},autoValid:!0},n={successCallback:function(e){a.ruleLogList=e.data.list},failCallback:function(e){a.$message.error("查看启停历史失败！")}};this.Base.submit(null,i,n)},onClose:function(){this.drawerVisible=!1},pageChange:function(e,t){this.ruleData=this.dataList.slice(t*(e-1),t*e),this.page=e},pageSizeChange:function(e,t){this.pageSize=t,this.pageChange(1,t)},fnQueryYkz108:function(){var e=this,t={url:"ruleInfo/queryYkz108b",autoValid:!0},a={successCallback:function(t){e.ykz108Options=t.data.ykz108,e.ykz108OptionsYz=t.data.ykz108,e.ykz108OptionsDj=t.data.ykz108Dj,e.fnSelectYkz108(e.ykz108Options[0].id)},failCallback:function(e){}};this.Base.submit(null,t,a)},fnSelectYkz108:function(e,t){var a=this.ykz108Options.find((function(t){return t.id===e}));this.queryParamForm.setFieldsValue({ykz108:e}),this.fnQueryAaz319(a)},fnQueryAaz319:function(e){var t=this,a={url:"ruleConfig/queryAaz319",showPageLoading:!1,data:{ykz108:e.ykz108,aae043:this.queryParamForm.getFieldValue("aae043")},autoValid:!0},l={successCallback:function(e){t.aaz319Options=e.data.option},failCallback:function(e){}};this.Base.submit(null,a,l)},fnQuery:function(){var e=this,t=this.queryParamForm.getFieldsValue();if(void 0!=t.ykz108){t.key=this.activeKey,t.aae100=this.radioValue;var a=this.ykz108Options.find((function(e){return e.id===t.ykz108}));t.ykz108=a.ykz108;var l={url:"ruleConfig/queryRule",data:t,autoValid:!0},i={successCallback:function(t){var a=t.data.map;0==a.flag&&(e.dataList=a.list,e.total=e.dataList.length,e.pageChange(1,e.pageSize)),-1==a.flag&&Modal.error({title:"错误",content:a.message})},failCallback:function(e){}};this.Base.submit(null,l,i)}else Modal.warning({title:"警告（必选）",content:"请选择【监控场景】！"})},fnChangeTab:function(e){if(2==e&&0===this.ykz108OptionsDj.length)return this.activeKey=1,void this.$message.warning("未找到单据场景,请检查表mtt_mkf34！");2==e?(this.twoHidden=!0,this.ykz108Options=this.ykz108OptionsDj):(this.twoHidden=!1,this.ykz108Options=this.ykz108OptionsYz),this.fnSelectYkz108(this.ykz108Options[0].id),this.activeKey=e,this.selectedRows=[],this.selectedRowKeys=[],this.fnQuery()},fnYkz032StopStart:function(e){var t=this,a=this.$createElement;this.textareaValue="";var l="",i=6,n="";0==this.radioValue?(l="启用",i=7):(l="停用",i=6);var r=this.ykz108Options.find((function(e){return e.id===t.queryParamForm.getFieldValue("ykz108")}));n=1==this.activeKey?l+"药品规则(限制条件为：'"+e.ykz018+")成功!":l+"规则成功",this.$confirm({width:600,title:1==this.activeKey?a("span",{style:"white-space: pre-line;font-size: 14px"},["是否确认",l,"【",a("span",{style:{color:"blue"}},[r.name]),"】 限制条件为：【",a("span",{style:{color:"blue"}},[e.ykz018]),"】中的所有项目?",a("p",{directives:[{name:"show",value:6===i}]},[a("label",{class:"required-label"},[a("span",{class:"asterisk"},["*"]),"请填写停用原因："]),a("ta-textarea",{attrs:{placeholder:"请输入停用原因"},ref:"myTextareaRef",style:"height:100px",model:{value:t.textareaValue,callback:function(e){t.textareaValue=e}}})])]):a("span",{style:"white-space: pre-line;font-size: 14px"},["是否确认",l,"此规则","\n",a("p",{directives:[{name:"show",value:6===i}]},[a("label",{class:"required-label"},[a("span",{class:"asterisk"},["*"]),"请填写停用原因："]),a("ta-textarea",{attrs:{placeholder:"请输入停用原因"},ref:"myTextareaRef",style:"height:100px",model:{value:t.textareaValue,callback:function(e){t.textareaValue=e}}})])]),onOk:function(a){if(6!==i||t.textareaValue){e.ykz108=r.ykz108,e.aae043=t.queryParamForm.getFieldValue("aae043"),e.type=i,e.operatereason=t.textareaValue,e.activeKey=t.activeKey;var l={url:"ruleConfig/ruleByStopStart",data:e,autoValid:!0},s={successCallback:function(e){var l=e.data.result;""==l?(t.fnQuery(),t.$message.success(n)):Modal.error({title:"错误",content:l}),a()},failCallback:function(e){a()}};t.Base.submit(null,l,s)}else t.$message.error("请输入停用原因")},onCancel:function(){},class:"test"})},fnCodeStopStart:function(e){var t=this,a=this.$createElement;this.textareaValue="",this.ykz108SelectValue=[],this.checkboxValue=!1;var l="",i=0,n="";0==this.radioValue?(l="启用",i=1):(l="停用",i=0);var r=this.ykz108Options.filter((function(e){return e.id!==t.queryParamForm.getFieldValue("ykz108")})),s=this.ykz108Options.find((function(e){return e.id===t.queryParamForm.getFieldValue("ykz108")}));n=1==this.activeKey?l+"药品规则(项目编码为：'"+e.ake001+"' 项目名称为：'"+e.ake002+"')成功!":l+"规则成功",e.type=i,e.activeKey=this.activeKey,this.bftype=i,this.bfrecord=e,this.$confirm({width:600,title:1==this.activeKey?a("span",{style:"white-space: pre-line;font-size: 14px"},["是否确认",l,"【",a("span",{style:{color:"blue"}},[s.name]),"】 【",a("span",{style:{color:"blue"}},[e.ykz018]),"】中的该项目:","\n","项目编码为：",e.ake001,"\n","项目名称为：",a("span",{style:{color:"blue"}},[e.ake002]),a("p",{directives:[{name:"show",value:0===i}]},[a("label",{class:"required-label"},[a("span",{class:"asterisk"},["*"]),"请填写停用原因："]),a("ta-textarea",{attrs:{placeholder:"请输入停用原因"},ref:"myTextareaRef",style:"height:100px",model:{value:t.textareaValue,callback:function(e){t.textareaValue=e}}})]),a("p",[a("taCheckbox",{on:{change:this.onChange}},["同时",l,"其他场景中的该编码："]),a("taSelect",{attrs:{placeholder:"规则运行场景筛选",mode:"multiple",maxTagCount:2,allowClear:!0,options:r},style:"width:100%",on:{change:this.handleChange}}),a("ta-alert",{attrs:{message:a("div",["请注意：该编码在已选场景的所有规则将被",l,"，如果该编码在一个场景中存在多条规则，请谨慎选择！",a("span",{attrs:{id:"rulesNum"},style:"color:red"})]),banner:!0}})])]):a("span",{style:"white-space: pre-line;font-size: 14px"},["是否确认",l,"此规则","\n",a("p",{directives:[{name:"show",value:0===i}]},[a("label",{class:"required-label"},[a("span",{class:"asterisk"},["*"]),"请填写停用原因："]),a("ta-textarea",{attrs:{placeholder:"请输入停用原因"},ref:"myTextareaRef",style:"height:100px",model:{value:t.textareaValue,callback:function(e){t.textareaValue=e}}})])]),onOk:function(a){if(0!==i||t.textareaValue)if(t.checkboxValue&&0===t.ykz108SelectValue.length)t.$message.error("请至少选择一个场景");else{t.checkboxValue&&(e.ykz108List=JSON.stringify(t.ykz108SelectValue)),e.ykz108=s.ykz108,e.aae043=t.queryParamForm.getFieldValue("aae043"),e.type=i,e.activeKey=t.activeKey,e.operatereason=t.textareaValue;var l={url:t.checkboxValue?"ruleConfig/ruleByStopStartBatch":"ruleConfig/ruleByStopStart",data:e,autoValid:!0},r={successCallback:function(e){var l=e.data.result;""==l?(t.fnQuery(),t.$message.success(n)):Modal.error({title:"错误",content:l}),a()},failCallback:function(e){a()}};t.Base.submit(null,l,r)}else t.$message.error("请输入停用原因")},onCancel:function(){},class:"test"})},fnChangeRadio:function(e){"0"===e.target.value||0===e.target.value?(this.enable=!0,this.hidden=!0):(this.enable=!1,this.hidden=!1),this.radioValue=e.target.value,this.fnQuery()},fnOnChange:function(e,t){this.selectedRowKeys=e,this.selectedRows=t},onChange:function(e){this.checkboxValue=e.target.checked,this.checkboxValue&&this.ykz108SelectValue.length>0?0==this.bftype||1==this.bftype?this.queryRulesNum():this.queryRulesNumMulti():document.getElementById("rulesNum").innerHTML=""},handleChange:function(e){this.ykz108SelectValue=e,this.checkboxValue&&this.ykz108SelectValue.length>0?0==this.bftype||1==this.bftype?this.queryRulesNum():this.queryRulesNumMulti():document.getElementById("rulesNum").innerHTML=""},queryRulesNumMulti:function(){var e={url:"ruleConfig/queryRulesNum",data:{ykz108List:JSON.stringify(this.ykz108SelectValue),param:JSON.stringify(this.selectedRows),type:this.bftype,activeKey:this.activeKey},autoValid:!0},t={successCallback:function(e){e.data.result>1?document.getElementById("rulesNum").innerHTML="检测到已选编码在同一场景配置多条规则":document.getElementById("rulesNum").innerHTML=""},failCallback:function(e){tiis.message.error("请求失败！")}};this.Base.submit(null,e,t)},queryRulesNum:function(){var e=this;this.bfrecord.ykz108List=JSON.stringify(this.ykz108SelectValue);var t={url:"ruleConfig/queryRulesNum",data:this.bfrecord,autoValid:!0},a={successCallback:function(t){var a=Object.entries(t.data.result).map((function(e){var t=(0,n.Z)(e,2),a=t[0],l=t[1];return{key:a,value:l}}));if(a.length>0){var l,i="",s=(0,r.Z)(a);try{var o=function(){var t=l.value;i+="在".concat(e.ykz108Options.find((function(e){return e.id===t.key})).name,"场景存在").concat(t.value,"条规则;")};for(s.s();!(l=s.n()).done;)o()}catch(u){s.e(u)}finally{s.f()}document.getElementById("rulesNum").innerHTML="检测到该编码".concat(i)}else document.getElementById("rulesNum").innerHTML=""},failCallback:function(e){tiis.message.error("请求失败！")}};this.Base.submit(null,t,a)},fnRuleConfigByQuery:function(e){var t=this,a=this.$createElement;if(this.textareaValue="",this.ruleData.length<1)this.$message.warning("暂无需要操作的规则!");else{var l=this.queryParamForm.getFieldsValue();if(!l.aaz319)return void this.$message.warning("请必须选择规则大类!");var i=l.aaz319,n=l.ykz018,r=l.aae043,s=this.ykz108Options.find((function(e){return e.id===l.ykz108})),o=s.ykz108,u=l.ake001,c=l.ake002,d="";d=2===e?"停用":"启用";this.ykz108Options.forEach((function(e){e.id===o&&e.name}));var h="";""!==i&&this.aaz319Options.forEach((function(e){e.id===i&&(h=e.name)})),""===i&&""===n?2===e?this.$message.error("不能停用所有规则"):this.$message.error("不能启用所有规则"):this.$confirm({width:600,title:a("span",{style:"white-space: pre-line;font-size: 14px"},["是否确认",d,"【",a("span",{style:"color:blue"},[s.name]),"】 规则大类为：【",a("span",{style:"color:blue"},[h]),"】的所有规则?",a("p",{directives:[{name:"show",value:2===e}]},[a("label",{class:"required-label"},[a("span",{class:"asterisk"},["*"]),"请填写停用原因："]),a("ta-textarea",{attrs:{placeholder:"请输入停用原因"},ref:"myTextareaRef",style:"height:100px",model:{value:t.textareaValue,callback:function(e){t.textareaValue=e}}})])]),onOk:function(a){if(2!==e||t.textareaValue){var l={url:"ruleConfig/ruleByStopStart",data:{ykz108:o,aae043:r,aaz319:i,ykz018:n,ake001:u,ake002:c,type:e,operatereason:t.textareaValue},autoValid:!0},s={successCallback:function(e){var l=e.data.result;""===l?(t.fnQuery(),t.$message.success(d+"规则成功!")):Modal.error({title:"错误",content:l}),a()},failCallback:function(e){a()}};t.Base.submit(null,l,s)}else t.$message.error("请输入停用原因")},onCancel:function(){},class:"test"})}},fnRuleConfigByGrid:function(e){var t=this,a=this.$createElement;this.textareaValue="",this.ykz108SelectValue=[],this.checkboxValue=!1;var l=this.ykz108Options.filter((function(e){return e.id!==t.queryParamForm.getFieldValue("ykz108")})),i=this.ykz108Options.find((function(e){return e.id===t.queryParamForm.getFieldValue("ykz108")})),n=i.ykz108,r=this.queryParamForm.getFieldValue("aae043"),s="";s=4===e?"停用":"启用",this.bftype=e,this.selectedRows.length<1?this.$message.warn("请至少选择一条三目编码!"):this.$confirm({width:600,title:a("span",{style:"white-space: pre-line;font-size: 14px"},["确认",s,"勾选的",this.selectedRows.length,"条编码吗?",a("p",{directives:[{name:"show",value:4===e}]},[a("label",{class:"required-label"},[a("span",{class:"asterisk"},["*"]),"请填写停用原因："]),a("ta-textarea",{attrs:{placeholder:"请输入停用原因"},ref:"myTextareaRef",style:"height:100px",model:{value:t.textareaValue,callback:function(e){t.textareaValue=e}}})]),a("p",{directives:[{name:"show",value:1==this.activeKey}]},[a("taCheckbox",{on:{change:this.onChange}},["同时",s,"其他场景中的该编码："]),a("taSelect",{attrs:{placeholder:"规则运行场景筛选",mode:"multiple",maxTagCount:2,allowClear:!0,options:l},style:"width:100%",on:{change:this.handleChange}}),a("ta-alert",{attrs:{message:a("div",["请注意：该编码在已选场景的所有规则将被",s,"，如果该编码在一个场景中存在多条规则，请谨慎选择！",a("span",{attrs:{id:"rulesNum"},style:"color:red"})]),banner:!0}})])]),onOk:function(a){if(4!==e||t.textareaValue)if(t.checkboxValue&&0===t.ykz108SelectValue.length)t.$message.error("请至少选择一个场景");else{var l="";t.checkboxValue&&(l=JSON.stringify(t.ykz108SelectValue));var i={url:t.checkboxValue?"ruleConfig/ruleByStopStartBatch":"ruleConfig/ruleByStopStart",data:{ykz108:n,aae043:r,ykz108List:l,param:JSON.stringify(t.selectedRows),type:e,operatereason:t.textareaValue,activeKey:t.activeKey},autoValid:!0},o={successCallback:function(e){var l=e.data.result;""==l?(t.fnQuery(),t.$message.success(s+"规则成功!")):Modal.error({title:"错误",content:l}),a()},failCallback:function(e){a()}};t.Base.submit(null,i,o)}else t.$message.error("请输入停用原因")},onCancel:function(){},class:"test"})},deleteRule:function(){var e=this,t=this.ykz108Options.find((function(t){return t.id===e.queryParamForm.getFieldValue("ykz108")})),a=t.ykz108,l=this.queryParamForm.getFieldValue("aae043");this.selectedRows.length<1?this.$message.warn("请至少选择一条规则!"):this.$confirm({width:600,title:"确认删除勾选的"+this.selectedRows.length+"条规则吗?",content:"注意:删除规则操作将同时停用规则",onOk:function(){var t={url:"ruleConfig!ruleByStopStart",data:{ykz108:a,aae043:l,param:e.selectedRows,type:4},autoValid:!0},i={successCallback:function(t){var i={url:"ruleConfigController!deleteRule.do",data:{ykz108:a,aae043:l,param:e.selectedRows,type:4},autoValid:!0},n={successCallback:function(t){e.fnQuery(),e.$message.success("删除规则成功!")},failCallback:function(e){}};e.Base.submit(null,i,n)},failCallback:function(e){}};e.Base.submit(null,t,i)},onCancel:function(){},class:"test"})},addRule:function(){}}},f=p,y=a(1001),m=(0,y.Z)(f,l,i,!1,null,"738e54b9",null),k=m.exports},36766:function(e,t,a){"use strict";var l=a(66586);t["Z"]=l.Z},26263:function(e,t,a){"use strict";a.d(t,{s:function(){return l},x:function(){return i}});var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:e.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[e._v(e._s(e.title))]),e._t("default")],2)},i=[]},66586:function(e,t){"use strict";var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};t["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}},55382:function(){},61219:function(){}}]);
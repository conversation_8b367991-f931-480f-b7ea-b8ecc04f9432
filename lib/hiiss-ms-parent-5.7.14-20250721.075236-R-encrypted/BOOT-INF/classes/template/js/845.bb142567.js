"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[845],{42442:function(t,a,e){e.r(a),e.d(a,{default:function(){return h}});var r=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticClass:"fit"},[e("div",{staticStyle:{height:"160px"}},[e("ta-border-layout",[e("div",{staticStyle:{overflow:"hidden",height:"100%"}},[e("search-term",{ref:"term",staticClass:"fit",on:{fnQuery:t.fnQuery}})],1)])],1),e("div",{staticStyle:{height:"340px","margin-top":"-20px"}},[e("ta-border-layout",{attrs:{layout:{left:"27%"}}},[e("div",{attrs:{slot:"left"},slot:"left"},[e("pie-num",{ref:"pieNum",on:{fnLinkQuery:t.fnLinkQuery}})],1),e("div",[e("line-num",{ref:"lineNum",attrs:{url:"/departmentAnalysis/queryMonthWarningData"},on:{fnLinkQuery:t.fnLinkQuery}})],1)])],1),e("div",{staticStyle:{height:"340px","margin-top":"-20px"}},[e("ta-border-layout",[e("div",{staticStyle:{overflow:"hidden",height:"100%"}},[e("item-rank",{ref:"itemRank",attrs:{paramData:t.parameters,type:"item_jump",title_barRank:"项目排名情况"},on:{fnLinkQuery:t.fnLinkQuery}})],1)])],1),e("div",{staticStyle:{height:"350px"}},[e("ta-border-layout",{staticClass:"border",attrs:{layout:{left:"27%",right:"30%"},"show-border":!1}},[e("div",{attrs:{slot:"left"},slot:"left"},[e("rank-bar",{ref:"deptRank",attrs:{paramData:t.parameters,title_barRank:"科室排名情况",type:"dept"},on:{fnLinkQuery:t.fnLinkQuery}})],1),e("div",[e("doctor-rank-bar",{ref:"doctorRank",attrs:{paramData:t.parameters,title_barRank:"医生排名情况",type:"doctor"},on:{fnLinkQuery:t.fnLinkQuery}})],1),e("div",{attrs:{slot:"right"},slot:"right"},[e("rule-rank-bar",{ref:"ruleRank",attrs:{paramData:t.parameters,title_barRank:"预警规则分布",type:"rule"},on:{fnLinkQuery:t.fnLinkQuery}})],1)])],1)])},n=[],s=e(48534),i=(e(36133),e(90357)),o=e(10010),l=e(61973),p=e(74698),u=e(99566),m=e(50567),f=e(42375),d=e(78061),c=(e(36797),{components:{ItemRank:f.Z,LineNum:l.Z,PieNum:o.Z,SearchTerm:i.Z,RankBar:p.Z,doctorRankBar:u.Z,ruleRankBar:m.Z,detailInfo:d.Z},data:function(){return{parameters:{},visible:!1,t_operate:""}},methods:{fnQuery:function(t){var a=this;return(0,s.Z)(regeneratorRuntime.mark((function e(){var r,n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a.parameters=t,a.parameters.startDate=t.allDate[0].format("YYYY-MM-DD HH:mm:ss"),a.parameters.endDate=t.allDate[1].format("YYYY-MM-DD HH:mm:ss"),r={url:"/departmentAnalysis/queryAllWarningData",data:a.parameters,autoValid:!0},n={successCallback:function(t){a.$refs.pieNum.fnRemindNum("",t),a.$refs.itemRank.fnQuery("",t.data.resultMap.projectPointMap,a.parameters.scene),a.$refs.deptRank.fnRank("",t.data.resultMap.departmentPointMap,a.parameters.scene),a.$refs.doctorRank.fnRank("",t.data.resultMap.doctorPointMap,a.parameters.scene),a.$refs.ruleRank.fnQuery("",t.data.resultMap.rulePointMap,t.data.resultMap.costTypePointMap,a.parameters.scene)},failCallback:function(t){}},e.next=7,a.Base.submit(a.form,r,n);case 7:return e.next=9,a.$refs.lineNum.fnTrend("",a.parameters);case 9:case"end":return e.stop()}}),e)})))()},fnLinkQuery:function(t,a,e){var r=this,n={};switch(n=this.parameters,t){case"pie":this.t_operate="【"+a+"】",this.parameters.startDate=this.parameters.allDate[0].format("YYYY-MM-DD HH:mm:ss"),this.parameters.endDate=this.parameters.allDate[1].format("YYYY-MM-DD HH:mm:ss"),n.doctorCode="",n.departmentCode="",n.ake001="",n.ape893=e.ape893;var s={url:"/departmentAnalysis/queryAllWarningData",data:n,autoValid:!0},i={successCallback:function(t){r.$refs.itemRank.fnQuery(r.t_operate,t.data.resultMap.projectPointMap),r.$refs.deptRank.fnRank(r.t_operate,t.data.resultMap.departmentPointMap),r.$refs.doctorRank.fnRank(r.t_operate,t.data.resultMap.doctorPointMap),r.$refs.ruleRank.fnQuery(r.t_operate,t.data.resultMap.rulePointMap,t.data.resultMap.costTypePointMap)},failCallback:function(t){}};this.$nextTick((function(){r.Base.submit(r.form,s,i)}));break;case"line":this.t_operate="【"+a+"】",n.startDate=e.startDate,n.endDate=e.endDate,n.doctorCode="",n.departmentCode="",n.ake001="",n.ape893="";var o=[];o[0]=TaUtils.getMoment(e.startDate,"YYYY-MM-DD"),o[1]=TaUtils.getMoment(e.endDate,"YYYY-MM-DD"),this.$refs.term.modifyParameters(o),this.parameters.allDate=o;var l={url:"/departmentAnalysis/queryAllWarningData",data:n,autoValid:!0},p={successCallback:function(t){r.$refs.pieNum.fnRemindNum(r.t_operate,t),r.$refs.itemRank.fnQuery(r.t_operate,t.data.resultMap.projectPointMap),r.$refs.deptRank.fnRank(r.t_operate,t.data.resultMap.departmentPointMap),r.$refs.doctorRank.fnRank(r.t_operate,t.data.resultMap.doctorPointMap),r.$refs.ruleRank.fnQuery(r.t_operate,t.data.resultMap.rulePointMap,t.data.resultMap.costTypePointMap)},failCallback:function(t){}};this.$nextTick((function(){r.Base.submit(r.form,l,p)}));break;case"dept":this.t_operate="【"+a+"】",this.parameters.startDate=this.parameters.allDate[0].format("YYYY-MM-DD HH:mm:ss"),this.parameters.endDate=this.parameters.allDate[1].format("YYYY-MM-DD HH:mm:ss"),n.doctorCode="",n.departmentCode=e.departmentCode;var u={url:"/departmentAnalysis/queryAllWarningData",data:n,autoValid:!0},m={successCallback:function(t){r.$refs.doctorRank.fnRank(r.t_operate,t.data.resultMap.doctorPointMap),r.$refs.ruleRank.fnQuery(r.t_operate,t.data.resultMap.rulePointMap,t.data.resultMap.costTypePointMap)},failCallback:function(t){}};this.$nextTick((function(){r.Base.submit(r.form,u,m)}));break;case"rule":break;case"doctor":this.t_operate="【"+a+"】",this.parameters.startDate=this.parameters.allDate[0].format("YYYY-MM-DD HH:mm:ss"),this.parameters.endDate=this.parameters.allDate[1].format("YYYY-MM-DD HH:mm:ss"),n.doctorCode=e.aaz263;var f={url:"/departmentAnalysis/queryAllWarningData",data:n,autoValid:!0},d={successCallback:function(t){r.$refs.ruleRank.fnQuery(r.t_operate,t.data.resultMap.rulePointMap,t.data.resultMap.costTypePointMap)},failCallback:function(t){}};this.$nextTick((function(){r.Base.submit(r.form,f,d)}));break;case"item":this.t_operate="【"+a+"】",this.parameters.startDate=this.parameters.allDate[0].format("YYYY-MM-DD HH:mm:ss"),this.parameters.endDate=this.parameters.allDate[1].format("YYYY-MM-DD HH:mm:ss"),n.doctorCode="",n.departmentCode="",n.ake001=e.ake001;var c={url:"/departmentAnalysis/queryAllWarningData",data:n,autoValid:!0},k={successCallback:function(t){r.$refs.deptRank.fnRank(r.t_operate,t.data.resultMap.departmentPointMap),r.$refs.doctorRank.fnRank(r.t_operate,t.data.resultMap.doctorPointMap),r.$refs.ruleRank.fnQuery(r.t_operate,t.data.resultMap.rulePointMap,t.data.resultMap.costTypePointMap)},failCallback:function(t){}};this.$nextTick((function(){r.Base.submit(r.form,c,k)}));break}}},created:function(){this.parameters.role=this.$route.query.role}}),k=c,M=e(1001),y=(0,M.Z)(k,r,n,!1,null,"0e6cfb50",null),h=y.exports}}]);
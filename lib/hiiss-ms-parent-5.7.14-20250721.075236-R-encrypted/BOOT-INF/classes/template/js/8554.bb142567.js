"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[8554],{8554:function(t,e,a){a.r(e),a.d(e,{default:function(){return g}});var o=function(){var t=this,e=this,a=e.$createElement,o=e._self._c||a;return o("ta-border-layout",{staticStyle:{width:"100%",height:"100vh"},attrs:{"layout-type":"fixTop"}},[o("div",{staticStyle:{padding:"24px 10px 0 10px"},attrs:{slot:"header"},slot:"header"},[o("ta-form",{attrs:{layout:"horizontal","form-layout":!0,"auto-form-create":function(e){return t.form=e},"label-width":"120px"}},[o("ta-form-item",{attrs:{label:"数据模型名称",span:6,"field-decorator-id":"zbmc"}},[o("ta-input",{attrs:{placeholder:"请输入"}})],1),o("div",{staticStyle:{float:"right"}},[o("ta-button",{attrs:{type:"primary"},on:{click:e.fnQuery}},[e._v("查询")])],1)],1)],1),o("cardBox",{staticClass:"fit",attrs:{title:"数据模型列表"}},[o("template",{slot:"extra"},[o("ta-button",{attrs:{type:"primary",icon:"plus",ghost:!0},on:{click:e.fnAdd}},[e._v("创建数据模型")])],1),o("template",{slot:"boxContent"},[o("ta-big-table",{ref:"table_zdysgxlb",attrs:{border:"",stripe:"",resizable:"","auto-resize":"",height:"100%",align:"center",data:e.tableData},scopedSlots:e._u([{key:"bottomBar",fn:function(){return[o("ta-pagination",{ref:"gridPager",staticStyle:{"text-align":"right","margin-top":"10px"},attrs:{"data-source":e.tableData,"default-page-size":10,showQuickJumper:!1,params:e.getParams,showTotal:!0,total:e.total,url:"/smtrpt/kpiInfo/queryPageList"},on:{"update:dataSource":function(t){e.tableData=t},"update:data-source":function(t){e.tableData=t},loaded:e.loadedCallBack}})]},proxy:!0}])},[o("ta-big-table-column",{attrs:{type:"seq",title:"序号",width:"50"}}),o("ta-big-table-column",{attrs:{field:"kpiName",title:"数据模型名称",width:"160","show-overflow":"",sortable:""}}),o("ta-big-table-column",{attrs:{field:"kpiCodg",title:"数据模型编码",width:"160","show-overflow":"",sortable:""}}),o("ta-big-table-column",{attrs:{field:"kpiDscr",title:"数据模型描述",width:"","header-align":"center",align:"left","show-overflow":"",sortable:""}}),o("ta-big-table-column",{attrs:{field:"crteTime",title:"创建时间",width:"160","show-overflow":"",sortable:""}}),o("ta-big-table-column",{attrs:{field:"updtTime",title:"更新时间",width:"160","show-overflow":""}}),o("ta-big-table-column",{attrs:{field:"modierName",title:"操作人",width:"100","show-overflow":""}}),o("ta-big-table-column",{attrs:{title:"操作",width:"140"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("span",{staticStyle:{cursor:"pointer","font-weight":"600",color:"#1A65B9"},on:{click:function(a){return e.rowShow(t.row)}}},[e._v("查看")]),o("span",{staticStyle:{margin:"0 8px",cursor:"pointer","font-weight":"600",color:"#1A65B9"},on:{click:function(a){return e.rowEdit(t.row)}}},[e._v("编辑")]),o("ta-popconfirm",{attrs:{title:"是否删除?",okText:"是",cancelText:"否"},on:{confirm:function(a){return e.rowDelete(t.row,t.rowIndex)}}},[o("ta-icon",{staticStyle:{color:"red"},attrs:{slot:"icon",type:"question-circle-o"},slot:"icon"}),o("span",{staticStyle:{cursor:"pointer","font-weight":"600",color:"#1A65B9"}},[e._v("删除")])],1)]}}])})],1)],1)],2)],1)},i=[],r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{height:"100%"}},[a("div",{staticClass:"flex_boxs"},[a("div",{staticClass:"cardTitle"},[t._v(t._s(t.title)+" "),t._t("desc")],2),a("div",{staticStyle:{width:"50%","text-align":"right","line-height":"60px"}},[t._t("extra")],2)]),a("div",{staticStyle:{width:"100%",height:"calc(100% - 60px)",padding:"0 10px"}},[t._t("boxContent")],2)])},l=[],n={name:"cardBox",props:{title:{type:String,default:""}}},s=n,c=a(1001),d=(0,c.Z)(s,r,l,!1,null,"dd9cc506",null),u=d.exports,p={name:"zbgl",components:{cardBox:u},data:function(){return{total:0,tableData:[]}},mounted:function(){this.fnQuery()},methods:{getParams:function(){var t=this.form.getFieldsValue(),e={kpiName:t.zbmc};return e},fnQuery:function(){this.$refs.gridPager.loadData()},loadedCallBack:function(t){this.total=t.data.pageBean.recordCounts||0,this.tableData=t.data.pageBean.data||[]},fnAdd:function(){this.$router.push({name:"otherPage",params:{pageFlag:"add"}})},rowShow:function(t){var e=t.kpiCodg;this.$router.push({name:"otherPage",params:{pageFlag:"show",kpiCodg:e}})},rowEdit:function(t){var e=t.kpiCodg;this.$router.push({name:"otherPage",params:{pageFlag:"edit",kpiCodg:e}})},rowDelete:function(t,e){var a=this;Base.submit(null,{url:"/smtrpt/kpiInfo/deleteKpiInfo",data:{kpiCodg:t.kpiCodg}}).then((function(t){t.serviceSuccess&&(a.$message.success("删除成功"),a.tableData.splice(e,1),a.fnQuery())}))}}},f=p,h=(0,c.Z)(f,o,i,!1,null,"73b27def",null),g=h.exports}}]);
"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[8367],{88412:function(a,t,e){var l=e(26263),o=e(36766),s=e(1001),n=(0,s.Z)(o.Z,l.s,l.x,!1,null,"5e7ef0ae",null);t["Z"]=n.exports},38367:function(a,t,e){e.r(t),e.d(t,{default:function(){return u}});var l=function(){var a=this,t=this,e=t.$createElement,l=t._self._c||e;return l("div",{staticClass:"fit"},[l("ta-border-layout",{attrs:{layout:{header:"120px"}}},[l("div",{attrs:{slot:"header"},slot:"header"},[l("ta-title",{attrs:{title:"查询条件"}}),l("ta-form",{attrs:{autoFormCreate:function(t){a.form=t},formLayout:"",enctype:"multipart/form-data"}},[l("ta-form-item",{attrs:{fieldDecoratorId:"aaa027",labelCol:{span:8},wrapperCol:{span:16},span:6}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[t._v("统筹区")]),l("ta-tree-select",{attrs:{dropdownStyle:{maxHeight:"400px",overflow:"auto"},treeData:t.treeData,treeDataSimpleMode:t.treeData,"allow-clear":!0},on:{change:t.queryHospitalName}})],1),l("ta-form-item",{attrs:{fieldDecoratorId:"akb020",labelCol:{span:8},wrapperCol:{span:16},span:6}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[t._v("医院选择")]),l("ta-select",{attrs:{"show-search":!0,placeholder:"请选择",allowClear:!0,options:t.akb020Data},on:{change:t.queryInfo}})],1),l("ta-form-item",{attrs:{span:12,labelCol:{span:0},wrapperCol:{span:24}}},[l("ta-button",{staticStyle:{"margin-left":"20px"},attrs:{type:"primary",icon:"download"},on:{click:t.sampleDownload}},[t._v("医院基础信息导入模板下载")]),l("ta-upload",{staticClass:"upload-list-inline",attrs:{name:"fileUpload",multiple:!1,action:"",fileList:t.fileList,accept:".xls,.xlsx",beforeUpload:t.beforeUpload}},[l("ta-button",{attrs:{type:"success"}},[l("ta-icon",{attrs:{type:"upload"}}),t._v(" 批量导入医院信息 ")],1)],1),l("ta-button",{attrs:{id:"upload",hidden:!0},on:{click:t.fileUpload}},[t._v(" 上传 ")])],1)],1)],1),l("ta-title",{attrs:{title:"医院信息列表"}}),l("ta-row",[l("ta-button",{attrs:{type:"primary"},on:{click:t.handleOpenAddBaseInfo}},[l("ta-icon",{attrs:{type:"plus"}}),t._v("添加")],1)],1),l("div",{staticStyle:{height:"calc(100% - 120px)"}},[l("ta-table",{ref:"baseInfoTable",attrs:{columns:t.baseInfoTableColumns,dataSource:t.baseInfoTableData,bordered:!0,size:"small",haveSn:!1,scroll:{y:"100%"},customRow:t.fnCustomRow},on:{"update:columns":function(a){t.baseInfoTableColumns=a}},scopedSlots:t._u([{key:"action",fn:function(a,e,o){return[l("span",[l("a",{on:{click:function(a){return t.handleShowEdit(e)}}},[l("ta-icon",{attrs:{type:"edit"}}),t._v("编辑")],1),l("ta-popconfirm",{attrs:{title:"是否删除?"},on:{confirm:function(){return t.handleDelete(e)}}},[l("a",{style:{marginLeft:"10px"}},[l("ta-icon",{attrs:{type:"delete"}}),t._v("删除")],1)])],1)]}}])}),l("ta-pagination",{ref:"baseInfoPager",staticStyle:{float:"right","margin-top":"8px","margin-right":"10px"},attrs:{showSizeChanger:"",showQuickJumper:"",dataSource:t.baseInfoTableData,params:t.pageParams,defaultPageSize:10,pageSizeOptions:["10","20","40"],url:"hospBaseInfo/queryByPage"},on:{"update:dataSource":function(a){t.baseInfoTableData=a},"update:data-source":function(a){t.baseInfoTableData=a}}})],1)],1),l("ta-modal",{attrs:{width:800,height:400},on:{cancel:t.handleModalCancel,ok:t.handleModalEdit},model:{value:t.modalVisible,callback:function(a){t.modalVisible=a},expression:"modalVisible"}},[l("ta-title",{attrs:{slot:"title",title:t.modalTitle},slot:"title"}),l("ta-form",{attrs:{autoFormCreate:function(t){return a.editForm=t},layout:"horizontal",formLayout:!0}},[l("ta-form-item",{attrs:{fieldDecoratorId:"akb021",labelCol:{span:8},wrapperCol:{span:16},span:24,require:{message:"必输项!"}}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[t._v("医院名称")]),l("ta-input",{attrs:{placeholder:"请输入"},model:{value:t.akb021Data,callback:function(a){t.akb021Data=a},expression:"akb021Data"}})],1),l("ta-form-item",{attrs:{fieldDecoratorId:"akb020",labelCol:{span:8},wrapperCol:{span:16},span:24,require:{message:"必输项!"}}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[t._v("医院编码")]),l("ta-input",{attrs:{placeholder:"请输入",disabled:t.disableCondition},model:{value:t.akb020,callback:function(a){t.akb020=a},expression:"akb020"}})],1),l("ta-form-item",{attrs:{fieldDecoratorId:"aaa027",labelCol:{span:8},wrapperCol:{span:16},span:24,require:{message:"必输项!"}}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[t._v("统筹区")]),l("ta-tree-select",{attrs:{placeholder:"请选择",dropdownStyle:{maxHeight:"400px",overflow:"auto"},treeData:t.treeData,treeDataSimpleMode:t.treeData,"allow-clear":!0},model:{value:t.aaa027Data,callback:function(a){t.aaa027Data=a},expression:"aaa027Data"}})],1),l("ta-form-item",{attrs:{fieldDecoratorId:"aka101",labelCol:{span:8},wrapperCol:{span:16},span:24,require:{message:"必输项!"}}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[t._v("医院等级")]),l("ta-select",{attrs:{placeholder:"请选择","collection-type":"AKA101"},model:{value:t.aka101Data,callback:function(a){t.aka101Data=a},expression:"aka101Data"}})],1),l("ta-form-item",{attrs:{fieldDecoratorId:"akb023",labelCol:{span:8},wrapperCol:{span:16},span:24,require:{message:"必输项!"}}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[t._v("机构类型")]),l("ta-select",{attrs:{placeholder:"请选择","collection-type":"AKB023"},model:{value:t.akb023Data,callback:function(a){t.akb023Data=a},expression:"akb023Data"}})],1),l("ta-form-item",{attrs:{fieldDecoratorId:"aab112",labelCol:{span:8},wrapperCol:{span:16},span:24,require:{message:"必输项!"}}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[t._v("机构性质")]),l("ta-select",{attrs:{placeholder:"请选择","collection-type":"AAB112T"},model:{value:t.aab112Data,callback:function(a){t.aab112Data=a},expression:"aab112Data"}})],1),l("ta-form-item",{attrs:{fieldDecoratorId:"akf015",labelCol:{span:8},wrapperCol:{span:16},span:24,require:{message:"必输项!"}}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[t._v("床位数")]),l("ta-input",{attrs:{placeholder:"请输入"},model:{value:t.akf015Data,callback:function(a){t.akf015Data=a},expression:"akf015Data"}})],1),l("ta-form-item",{attrs:{fieldDecoratorId:"aae100",labelCol:{span:8},wrapperCol:{span:16},span:24,require:{message:"必输项!"},initValue:t.defaultValue}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[t._v("有效标志")]),l("ta-select",{attrs:{options:[{value:"1",label:"有效"},{value:"0",label:"无效"}],placeholder:"请选择"}})],1)],1)],1)],1)},o=[],s=e(95082),n=(e(32564),e(88412)),i={name:"hospDataManage",components:{TaTitle:n.Z},data:function(){var a=[{title:"医院名称",align:"center",dataIndex:"akb021",width:70,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"医院编码",align:"center",dataIndex:"akb020",width:70,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"统筹区",align:"center",dataIndex:"aaa027",width:70,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell,customRender:this.aaa027Format},{title:"医院等级",align:"center",dataIndex:"aka101",width:70,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell,collectionType:"AKA101"},{title:"机构类型",align:"center",dataIndex:"akb023",width:70,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell,collectionType:"AKB023"},{title:"机构性质",align:"center",dataIndex:"aab112",width:70,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell,collectionType:"AAB112T"},{title:"床位数",align:"center",dataIndex:"akf015",width:70,overflowTooltip:!0,customHeaderCell:this.fnCustomHeaderCell},{title:"有效标志",align:"center",dataIndex:"aae100",width:70,overflowTooltip:!0,collectionType:"EFFECTIVE",customHeaderCell:this.fnCustomHeaderCell},{title:"操作",dataIndex:"action",align:"center",width:70,scopedSlots:{customRender:"action"},customHeaderCell:this.fnCustomHeaderCell}];return{treeData:[],defaultValue:"1",modalVisible:!1,baseInfoTableColumns:a,baseInfoTableData:[],akb020:"",akb020Data:[],akb021Data:[],akb023Data:[],aka101Data:[],aab112Data:[],akf015Data:[],aaa027Data:[],aae100Data:[],fileList:[],modalTitle:"",disableCondition:null}},methods:{fnCustomRow:function(a,t){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px"}}},fnCustomHeaderCell:function(a){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px",fontWeight:"bold"}}},aaa027Format:function(a){var t=this.treeData.find((function(t){return t.value===a}));return t?t.label:""},sampleDownload:function(){var a=this;Base.downloadFile({method:"post",fileName:"医院信息管理导入模板.xls",url:"hospBaseInfo/downLoad"}).then((function(t){a.$message.success("下载成功")})).catch((function(t){a.$message.error("下载失败")}))},beforeUpload:function(a){return this.fileList.push(a),document.getElementById("upload").click(),!1},fileUpload:function(){var a=this,t={url:"hospBaseInfo/upload",data:{uploadFile:this.fileList[0]},isFormData:!0,autoValid:!0},e={successCallback:function(t){a.fileList=[],a.fnInitData(),a.$message.success("上传成功")},failCallback:function(t){a.fileList=[],a.$message.error("上传失败")}};Base.submit(this.from,t,e)},fnInitData:function(){var a=this;this.Base.submit(null,{url:"hospBaseInfo/initData",data:{},autoValid:!0},{successCallback:function(t){a.akb020Data=t.data.akb020Data},failCallback:function(t){a.$message.error("初始化数据失败")}}),this.Base.submit(null,{url:"miimCommonRead/getTreeData",data:{}}).then((function(t){a.treeData=t.data.treeData}))},queryInfo:function(a){this.form.setFieldsValue({akb020:a}),this.$refs.baseInfoPager.loadData()},pageParams:function(){var a=this.form.getFieldsValue();return a},handleShowEdit:function(a){var t=this;this.modalTitle="编辑医院基础信息",this.disableCondition=!0,this.modalVisible=!0,setTimeout((function(){t.editForm.setFieldsValue(a)}),3)},handleDelete:function(a){var t=this;this.Base.submit(null,{url:"hospBaseInfo/deleteBaseInfo",data:a,autoValid:!0},{successCallback:function(a){t.$message.success("删除成功"),t.$refs.baseInfoPager.loadData()},failCallback:function(a){t.$message.error("删除失败")}})},handleModalCancel:function(){this.modalVisible=!1,this.editForm.resetFields()},handleModalEdit:function(){var a=this;"编辑医院基础信息"==this.modalTitle?this.Base.submit(this.editForm,{url:"hospBaseInfo/updateBaseInfo",data:(0,s.Z)({},this.editForm.getFieldsValue()),autoValid:!0},{successCallback:function(t){a.$message.success("数据处理成功"),a.$refs.baseInfoPager.loadData(),a.fnInitData(),a.handleModalCancel()},failCallback:function(t){a.$message.error("数据处理失败")}}):this.Base.submit(this.editForm,{url:"hospBaseInfo/insertBaseInfo",data:(0,s.Z)({},this.editForm.getFieldsValue()),autoValid:!0},{successCallback:function(t){a.$message.success("数据处理成功"),a.$refs.baseInfoPager.loadData(),a.fnInitData(),a.handleModalCancel()},failCallback:function(t){a.$message.error("数据处理失败")}})},handleOpenAddBaseInfo:function(){this.disableCondition=!1,this.modalTitle="新增医院基础信息",this.modalVisible=!0},queryHospitalName:function(a){var t=this;this.Base.submit(null,{url:"miimCommonRead/queryAkb020DataByAaa027",data:{aaa027:a}}).then((function(a){t.akb020Data=a.data.akb020Data,t.form.resetFields(["akb020"]),setTimeout((function(){t.$refs.baseInfoPager.loadData()}))}))}},mounted:function(){this.fnInitData(),this.$refs.baseInfoPager.loadData()}},r=i,c=e(1001),d=(0,c.Z)(r,l,o,!1,null,"64a6db70",null),u=d.exports},36766:function(a,t,e){var l=e(66586);t["Z"]=l.Z},26263:function(a,t,e){e.d(t,{s:function(){return l},x:function(){return o}});var l=function(){var a=this,t=a.$createElement,e=a._self._c||t;return e("h3",{staticStyle:{"font-weight":"normal"}},[e("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:a.titleSvg}}),e("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[a._v(a._s(a.title))]),a._t("default")],2)},o=[]},66586:function(a,t){var e={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};t["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:e}}}}}]);
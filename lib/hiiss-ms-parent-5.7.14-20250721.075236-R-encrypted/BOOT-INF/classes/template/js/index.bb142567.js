(function(){var e={11294:function(e,t,n){var i={"./zh-cn":73644,"./zh-cn.js":73644,"moment/locale/zh-cn":73644,"moment/locale/zh-cn.js":73644};function s(e){var t=a(e);return n(t)}function a(e){if(!n.o(i,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return i[e]}s.keys=function(){return Object.keys(i)},s.resolve=a,e.exports=s,s.id=11294},18583:function(e,t,n){"use strict";n.d(t,{d:function(){return o}});var i=n(30228),s=n(95278),a=n(6442),o={methods:{changeTheme:function(e){if(s.Z.onlineTheme)return(0,i.changeTheme)(e)},updateColorWeak:function(e){return(0,a.MP)(e)}}}},30228:function(e,t,n){"use strict";n.r(t),n.d(t,{changeTheme:function(){return c},injectTheme:function(){return h},syncThemeWhenInit:function(){return d}});var i=n(17546),s=n(95278),a=n(6442),o=n(18583),r=n(97369),l=n.n(r);function c(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n=[];if(0===e.indexOf("#"))n.push(e);else{var i=l()(e);Object.keys(i).forEach((function(e){e.indexOf("color")>0&&n.push(i[e])}))}(0,a.xz)(n,t)}function u(e){(0,a.MP)(e)}function d(){var e,t=i.Z.createWebStorage("index_theme",{isLocal:!0}),n=i.Z.getStorage("index_theme","index_theme",!0);c(null!==n&&void 0!==n?n:s.Z.defaultTheme,!1),(0,a.MP)(null!==(e=t.get("dark_mode"))&&void 0!==e?e:s.Z.defaultDarkMode)}function h(e){s.Z.onlineTheme&&(e.mixin(o.d),d())}window.addEventListener("storage",(function(e){if("index_theme"===e.key){var t=JSON.parse(e.newValue),n=t.index_theme,i="true"===t.dark_mode;n&&c(JSON.parse(n),!1),u(i)}}),!1)},83174:function(e,t,n){"use strict";var i=n(95082),s=(n(82526),n(41817),n(72443),n(92401),n(8722),n(32165),n(69007),n(16066),n(83510),n(41840),n(6982),n(32159),n(96649),n(39341),n(60543),n(21703),n(9170),n(32120),n(52262),n(92222),n(50545),n(43290),n(57327),n(69826),n(34553),n(84944),n(86535),n(91038),n(26699),n(82772),n(66992),n(69600),n(94986),n(21249),n(26572),n(85827),n(96644),n(47042),n(2707),n(38706),n(40561),n(33792),n(99244),n(18264),n(96078),n(4855),n(68309),n(35837),n(38862),n(73706),n(51532),n(99752),n(82376),n(73181),n(23484),n(2388),n(88621),n(60403),n(84755),n(25438),n(90332),n(40658),n(40197),n(44914),n(52420),n(60160),n(60970),n(10408),n(73689),n(9653),n(93299),n(35192),n(33161),n(44048),n(78285),n(44363),n(55994),n(61874),n(9494),n(31354),n(56977),n(19601),n(59595),n(35500),n(69720),n(43371),n(38559),n(38880),n(49337),n(36210),n(30489),n(46314),n(43304),n(41825),n(98410),n(72200),n(47941),n(94869),n(33952),n(57227),n(60514),n(41539),n(26833),n(88674),n(17922),n(34668),n(17727),n(36535),n(12419),n(69596),n(52586),n(74819),n(95683),n(39361),n(51037),n(5898),n(67556),n(14361),n(83593),n(39532),n(81299),n(24603),n(28450),n(74916),n(92087),n(88386),n(77601),n(39714),n(70189),n(24506),n(79841),n(27852),n(94953),n(32023),n(78783),n(4723),n(76373),n(66528),n(83112),n(38992),n(82481),n(15306),n(68757),n(64765),n(23123),n(23157),n(73210),n(48702),n(55674),n(15218),n(74475),n(57929),n(50915),n(29253),n(42125),n(78830),n(58734),n(29254),n(37268),n(7397),n(60086),n(80623),n(44197),n(76495),n(87145),n(35109),n(65125),n(82472),n(49743),n(8255),n(29135),n(48675),n(92990),n(18927),n(33105),n(35035),n(74345),n(7174),n(32846),n(98145),n(44731),n(77209),n(96319),n(58867),n(37789),n(33739),n(95206),n(29368),n(14483),n(12056),n(3462),n(30678),n(27462),n(33824),n(55021),n(12974),n(15016),n(4129),n(38478),n(19258),n(84811),n(34286),n(3048),n(77461),n(1999),n(61886),n(8e4),n(83475),n(46273),n(56882),n(78525),n(27004),n(3087),n(97391),n(66342),n(40787),n(23647),n(68216),n(88449),n(31672),n(74326),n(15581),n(78631),n(57640),n(25387),n(64211),n(12771),n(62962),n(71790),n(51568),n(26349),n(67427),n(32279),n(13384),n(2490),n(85567),n(5332),n(79433),n(59849),n(59461),n(82499),n(34514),n(26877),n(9924),n(72608),n(41874),n(66043),n(23748),n(71501),n(10072),n(23042),n(99137),n(71957),n(96306),n(103),n(8582),n(90618),n(74592),n(88440),n(58276),n(35082),n(12813),n(18222),n(24838),n(38563),n(50336),n(7512),n(74442),n(87713),n(46603),n(70100),n(10490),n(13187),n(60092),n(19041),n(30666),n(51638),n(62975),n(15728),n(46056),n(44299),n(5162),n(50292),n(29427),n(99964),n(75238),n(4987),n(1025),n(77479),n(34582),n(47896),n(12647),n(98558),n(84018),n(97507),n(61605),n(49076),n(34999),n(88921),n(96248),n(13599),n(11477),n(64362),n(15389),n(46006),n(90401),n(45164),n(91238),n(54837),n(87485),n(56767),n(69916),n(76651),n(61437),n(35285),n(39865),n(86035),n(50058),n(67501),n(609),n(21568),n(54534),n(95090),n(48824),n(44130),n(35954),n(16850),n(26182),n(8922),n(37380),n(1118),n(5835),n(23767),n(8585),n(8970),n(84444),n(68696),n(78206),n(76478),n(79715),n(12714),n(5964),n(43561),n(32049),n(86020),n(56585),n(75505),n(27479),n(54747),n(33948),n(87714),n(82801),n(1174),n(84633),n(85844),n(61295),n(60285),n(83753),n(41637),n(28594),n(36133),n(67532)),a=n(84175);if((0,a.Z)()||(0,s.Z)())throw new Error("请使用IE11以上版本或Chromium内核浏览器!");"Microsoft Internet Explorer"===navigator.appName&&navigator.appVersion.match(/9./i),navigator.userAgent.indexOf("MSIE 9")>0&&document.addEventListener("selectionchange",(function(){var e=document.activeElement;if("TEXTAREA"===e.tagName||"INPUT"===e.tagName){var t=document.createEvent("CustomEvent");t.initCustomEvent("input",!0,!0,{}),e.dispatchEvent(t)}}));var o=n(95278),r=n(3032),l=n(63822);function c(e,t,n){e.children&&e.children.forEach((function(e){c(e,t,n)})),(!e.children||e.children&&0===e.children.length)&&e.name&&e.name.indexOf(n)>=0&&t.push(e)}function u(e,t){return e.url?{partId:e.id,module:e.url.split("#/")[0],part:e.url.split("#/")[1]||"",prefix:e.prefix||t||"",params:e.params||""}:{module:"404.html",part:"",partId:"ta404",prefix:e.prefix||t||""}}var d={state:(0,i.Z)((0,i.Z)({menuList:[],commonMenuList:[]},o.Z.indexPageConfig),{},{collapsed:!1,showUserInfo:!1,tabMenuList:[],activeTabMenu:"worktable",activeMenuOne:!1,activeMenuTwo:"",ifSearchPane:!1,searchValue:"",userInfo:{userName:"未登录",userImg:"person-head.png",mainRoleName:""},sysInfo:{openSocialLogin:!1,openSmsLogin:!1},authority:[],breadcrumb:[],workTableMenuVertical:!1,workTableMenuHorizon:!1}),getters:{getStateValue:function(e){return e},getMenuListLeft:function(e){var t=[];if("leftTop"==e.menuType||"simple"==e.menuType||"workTable"==e.menuType)try{var n=e.menuList.filter((function(t){return t.id===e.activeMenuOne}));t=n[0].children||[]}catch(i){t=[]}else"left"==e.menuType&&(t=e.menuList);return t},getCollapsed:function(e){return"simple"==e.menuType||"workTable"!=e.menuType&&e.collapsed},getSearchMenuList:function(e){var t=[];return c({children:e.menuList},t,e.searchValue),t},getTabMenuList:function(e){return e.tabMenuList},getCommonMenuList:function(e){return e.commonMenuList},getActiveIframe:function(e){var t=e.activeTabMenu;if("worktable"===t){var n=e.worktable;return(0,i.Z)((0,i.Z)({},n),{},{partId:"worktable"})}var s=e.tabMenuList.filter((function(e){return e.id===t}))[0]||"";return u(s,e.srcPrefix)},getTabMenuByUrl:function(e,t){return function(t){var n=t.url,i=t.prefix,s=t.id,a="";return s&&(a=e.tabMenuList.filter((function(e){return e.id===s}))[0]||"",a)?u(a,e.srcPrefix):!!n&&(a=e.tabMenuList.filter((function(t){return t.url===n&&(!i||i==t.prefix||i==e.prefix)}))[0]||"",u(a,e.srcPrefix))}},getHeaderHeight:function(e){return"simple"===e.menuType?"48px":e.headerHeight}},actions:{setStateValue:function(e,t){var n=e.commit;n("_setStateValue",t)},loadMenuList:function(e,t){var n=e.commit,i=t.menuList;n("_setStateValue",{menuList:JSON.parse(JSON.stringify(i||[]))});var s=!1;i[0]&&i[0].id&&(s=i[0].id),n("_setActiveMenu",{level:"one",menuId:s,init:!0})},setActiveMenu:function(e,t){var n=e.commit,i=t.level,s=t.menuId;n("_setActiveMenu",{level:i,menuId:s})},setUserInfo:function(e){var t=e.state,n=e.commit;return"未登录"==t.userInfo.userName?new Promise((function(e,s){Base.submit(null,{url:"indexRestService/getCurUserAccount"},{successCallback:function(s){var a=(0,i.Z)((0,i.Z)({},s.data.curUserAccount),{},{passwordRSA:s.data.passwordRSA||!0,isSSO:s.data.isSSO||!1,passwordLevel:s.data.passwordLevel||3});n("_setUserInfo",a||{}),e(t.userInfo)}})})):new Promise((function(e,n){e(t.userInfo)}))},setSysInfo:function(e){var t=e.state,n=e.commit;return new Promise((function(e,i){Base.submit(null,{url:"indexRestService/getSysInfo"},{successCallback:function(i){var s={openSocialLogin:i.data.openSocialLogin||!1,openSmsLogin:i.data.openSmsLogin||!1};n("_setSysInfo",s||{}),e(t.sysInfo)}})}))},setAuthority:function(e){var t=e.commit;Base.submit(null,{url:"menu/menuAction/queryAllElement"},{successCallback:function(e){t("_setAuthority",e.data.list||[])}})},addTabMenuList:function(e,t){var n=e.commit,i=t.value;if((null===i||void 0===i||!i.url)&&(null===i||void 0===i||!i.exist))return message.error("该菜单没有配置对应的页面!"),!1;i&&i.id&&(void 0===i.children||i.children.length<=0)&&n("_addTabMenuList",i)},deleteTabMenuList:function(e,t){var n=e.commit,i=t.value;n("_deleteTabMenuList",i)},addCommonMenu:function(e,t){var n=e.state,i=e.commit;Base.submit(null,{url:"menu/common/bindCommonResource",data:{resourceId:t.id}}).then((function(e){if(e.data.result&&0===n.commonMenuList.filter((function(e){return e.id===t.id})).length){var s=n.commonMenuList.concat([t]);i("_setStateValue",{commonMenuList:s}),message.success("添加成功!")}else message.error("添加失败!")}))},deleteCommonMenu:function(e,t){var n=e.state,i=e.commit;Base.submit(null,{url:"menu/common/unbindCommonResource",data:{resourceIds:t.id}}).then((function(e){if(e.data.result){var s=n.commonMenuList.filter((function(e){return e.id!==t.id}));i("_setStateValue",{commonMenuList:s}),message.success("删除成功!")}else message.error("删除失败!")}))}},mutations:{_setStateValue:function(e,t){Object.assign(e,t)},_setActiveMenu:function(e,t){var n=t.level,i=t.menuId,s=t.init,a=e.activeMenuOne,o=e.menuList;"one"!=n||s&&0!=a?"two"==n&&(e.activeMenuTwo=i):(e.activeMenuOne=i,!s||o[0].children&&o[0].children.length||(e.activeMenuOne=""))},_setUserInfo:function(e,t){e.userInfo=Object.assign(e.userInfo,t||{})},_setSysInfo:function(e,t){e.sysInfo=Object.assign(e.sysInfo,t||{})},_setAuthority:function(e,t){e.authority=t},_addTabMenuList:function(e,t){var n,i;return t.params=(null===t||void 0===t||null===(n=t.url)||void 0===n?void 0:n.split("?")[1])||(null===t||void 0===t?void 0:t.params),t.url=null===t||void 0===t||null===(i=t.url)||void 0===i?void 0:i.split("?")[0],"b93e89515da24041b9f1459f77de38fe"===t.id?(e.activeTabMenu="worktable",!1):"breadcrumb"===e.barType?(e.tabMenuList=[t],e.activeTabMenu=t.id,!1):(e.tabMenuList.filter((function(e){return e.id===t.id})).length<=0&&e.tabMenuList.push(t),void(e.activeTabMenu=t.id))},_deleteTabMenuList:function(e,t){try{e.tabMenuList=JSON.parse(JSON.stringify(e.tabMenuList.filter((function(e){return e.id!==t}))))}catch(n){}}}},h=d,m=n(80774),f=(n(9828),n(18583)),p=n(4394),g=n(28856),v=n(89281),b=n(59427),w=n(30965),y=n(76685),A=n(794),k=n(60011),I=n(11782),S=n(87063),T=n(10702),M=n(89606),C=n(6602),L=n(41546),Z=n(32467),x=n(42413),U=n(72596),N=n(80314),E=n(47403),B=n(3950),D=n(16671),R=n(5959),P=n(61015),O=n(75159),F=n(43201),_=n(82668),V=n(84127),G=n(76936),W=n(90150),H=n(32097),j=n(62955),z=n(71368),$=n(74725),Y=n(44436),Q=n(39732),J=(n(99302),n(63116),n(85837),n(26677),n(28218),n(65906),n(7638),n(21688),n(47215),n(32411),n(16848),n(7073),n(91994),n(12263),n(47061),n(99878),n(10955),n(56357),n(38423),n(57568),n(57392),n(43703),n(9468),n(98538),n(58438),n(9585),n(17497),n(47087),n(84395),n(89193),n(59305),n(53573),n(62910),n(74551),n(80201)),K=n(28590),X=n(7029),q=n(73502),ee=n(48600),te=n(76040),ne=n(40103),ie=n(86472),se=n(17546),ae=n(99916),oe=n(51828),re=n(42793),le=n(47168),ce=n(1040),ue=n(48496),de=n(57960),he=n(80619),me=n(87638),fe=n(12344),pe=n(22275),ge=n(27362),ve=n(67190),be=n(82490);r["default"].use(g.Z),r["default"].use(v.Z),r["default"].use(b.ZP),r["default"].use(w.Z),r["default"].use(y.Z),r["default"].use(A.Z),r["default"].use(k.Z),r["default"].use(I.Z),r["default"].use(S.Z),r["default"].use(T.Z),r["default"].use(M.Z),r["default"].use(C.Z),r["default"].use(L.Z),r["default"].use(Z.Z),r["default"].use(x.ZP),r["default"].use(U.Z),r["default"].use(N.ZP),r["default"].use(E.Z),r["default"].use(B.Z),r["default"].use(D.Z),r["default"].use(R.Z),r["default"].use(P.Z),r["default"].use(O.Z),r["default"].use(F.Z),r["default"].use(_.Z),r["default"].use(V.Z),r["default"].use(G.Z),r["default"].use(W.Z),r["default"].use(H.Z),r["default"].use(j.Z),r["default"].use(z.Z),r["default"].use($.Z),r["default"].use(Y.Z),r["default"].use(Q.Z),r["default"].use(P.Z),r["default"].use(p.Z),r["default"].use(j.Z),r["default"].use(z.Z),r["default"].use($.Z),r["default"].use(Y.Z),r["default"].use(P.Z),r["default"].use(W.Z),r["default"].use(H.Z),r["default"].use(Q.Z),r["default"].use(G.Z),r["default"].use(g.Z),r["default"].use(v.Z),r["default"].use(b.ZP),r["default"].use(w.Z),r["default"].use(y.Z),r["default"].use(S.Z),r["default"].use(I.Z),r["default"].use(T.Z),r["default"].use(L.Z),r["default"].use(M.Z),r["default"].use(Z.Z),r["default"].use(C.Z),r["default"].use(A.Z),r["default"].use(k.Z),r["default"].use(x.ZP),r["default"].use(U.Z),r["default"].use(N.ZP),r["default"].use(E.Z),r["default"].use(B.Z),r["default"].use(D.Z),r["default"].use(R.Z),r["default"].use(O.Z),r["default"].use(_.Z),r["default"].use(V.Z);var we={pinyin:be.Z,getNowPageParam:q.Z,objectToUrlParam:ee.Z,getCookie:te.Z,setCookie:ne.Z,getToken:ie.Z,webStorage:se.Z,isIE:ae.Z,notSupported:oe.Z,isIE9:a.Z,isIE10:s.Z,isIE11:re.Z,isChrome:le.Z,isFireFox:ce.Z,isSafari:ue.Z,isSilversea:de.Z,clientSystem:he.Z,clientScreenSize:me.Z,clientBrowser:fe.Z,getWidth:pe.Z,getHeight:ge.Z,getStyle:ve.Z},ye=(0,i.Z)((0,i.Z)((0,i.Z)((0,i.Z)({},J.Z),K.ZP),we),X.ZP);r["default"].prototype.Base=(0,i.Z)((0,i.Z)({},ye),A.Z.$mask),r["default"].prototype.$message=k.Z,r["default"].prototype.$info=y.Z.info,r["default"].prototype.$success=y.Z.success,r["default"].prototype.$error=y.Z.error,r["default"].prototype.$warning=y.Z.warning,r["default"].prototype.$confirm=y.Z.confirm,r["default"].prototype.$notification=F.Z,window.message=k.Z,window.notification=F.Z,window.Modal=y.Z,window.Spin=I.Z,window.Base=r["default"].prototype.Base,window.indexTool={gotoLogin:function(e){var t=(null===e||void 0===e?void 0:e.url)||"login.html";window.location.href=t}},window.TaUtils=(0,i.Z)({},we);var Ae,ke,Ie,Se,Te,Me,Ce,Le=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{attrs:{id:"app"}},[i("index-layout",{ref:"indexLayout"},[i("div",{staticClass:"logo-image",class:{"logo-collapse":!e.showLogoTitle},style:{backgroundImage:"url("+n(61580)("./"+e.indexStore.logoImage)+")"},attrs:{slot:"logo",title:e.showLogoTitle?"":e.indexStore.logoTitle},slot:"logo"},[e.showLogoTitle?i("span",[e._v(e._s(e.indexStore.logoTitle))]):e._e()]),i("menu-horizon",{directives:[{name:"show",rawName:"v-show",value:"left"!==e.indexStore.menuType,expression:"indexStore.menuType !== 'left'"}],attrs:{slot:"topMenu"},slot:"topMenu"}),i("menu-vertical",{attrs:{slot:"leftMenu"},slot:"leftMenu"}),i("search-pane",{attrs:{slot:"searchPane"},slot:"searchPane"}),i("common-menu",{attrs:{slot:"commonMenu"},slot:"commonMenu"}),i("index-tab",{ref:"indexTab",attrs:{slot:"tabs"},slot:"tabs"}),i("iframe-list",{attrs:{slot:"iframes"},slot:"iframes"}),i("user-menu",{ref:"userMenu",attrs:{slot:"userInfo"},slot:"userInfo"})],1),i("div",{attrs:{id:"popCon"}})],1)},Ze=[],xe=function(){var e=this,t=this,n=t.$createElement,i=t._self._c||n;return i("div",{staticClass:"indexLayout"},[i("header",{class:["header","header--"+t.state.headerTheme,"header--"+t.state.menuType],style:Object.assign({},{height:t.headerHeight},t.menuDisplayH)},[i("div",{staticClass:"logo",style:[t.logoWidth,{lineHeight:t.headerHeight}]},[t._t("logo")],2),"top"!==t.state.menuType&&"simple"!==t.state.menuType&&"workTable"!==t.state.menuType?i("div",{staticClass:"collapse",on:{click:t.toggleCollapsed}},[i("ta-icon",{attrs:{type:t.collapsed?"menu-unfold":"menu-fold"}})],1):t._e(),i("div",[t._t("topMenu")],2),i("div",{staticClass:"userInfo",class:{"menu-top":"top"===t.state.menuType}},[i("span",{style:{height:t.headerHeight,lineHeight:t.headerHeight},on:{click:t.guide2}},[i("span",{staticClass:"step3",staticStyle:{"text-decoration":"underline",cursor:"pointer"}},[t._v(t._s(t.$t("help.title")))])]),t.fullscreenVisible?i("span",{style:{height:t.headerHeight,lineHeight:t.headerHeight},on:{click:t.fullScreen}},[t._v(t._s(t.pageSizeCtr?t.$t("screen.exitFullScreen"):t.$t("screen.fullScreen")))]):t._e(),i("span",{style:{height:t.headerHeight,lineHeight:t.headerHeight},on:{click:function(e){t.visible=!0}}},[i("ta-icon",{attrs:{type:"message"}}),t._v(t._s(t.$t("news.newsTitle"))),i("ta-badge",{staticStyle:{height:"45px"},attrs:{count:t.noticeNum,"number-style":{boxShadow:"0 0 0 1px #d9d9d9 inset"}}})],1),i("msg-list",{attrs:{visible:t.visible,"notice-num":t.noticeNum},on:{"update:visible":function(e){t.visible=e},"update:noticeNum":function(e){t.noticeNum=e},"update:notice-num":function(e){t.noticeNum=e},close:function(e){t.visible=!1}}}),i("span",{staticClass:"userPaneBar",style:{height:t.headerHeight,lineHeight:t.headerHeight},on:{click:t.showUserInfo}},[i("span",{staticClass:"step1"},[i("ta-icon",{attrs:{type:"user"}}),t._v(" "+t._s(t.state.userInfo.userName)+" ")],1)])],1)]),t._t("userInfo"),i("div",{staticClass:"content",style:{top:t.headerHeight,paddingLeft:"top"!==t.state.menuType?t.leftWidth:0}},["top"!==t.state.menuType?i("div",{class:["leftCon","leftCon--"+t.state.leftTheme],style:Object.assign({},{width:t.leftWidth,paddingTop:t.tabHeight},t.menuDisplayV)},[t.collapsed?t._e():i("div",{class:["leftConHeader","leftConHeader--"+t.state.leftTheme],style:{height:t.tabHeight,lineHeight:t.tabHeight}},[t.showStore?t._e():i("span",{staticClass:"searchPaneBar",class:{active:t.ifSearchPane}},[i("ta-input-search",{attrs:{placeholder:t.$t("search.search")},on:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.searchMenuListHandle(e)},search:function(e){return t.searchMenuListHandle()}},model:{value:t.seValue,callback:function(e){t.seValue=e},expression:"seValue"}}),i("ta-icon",{staticClass:"search",staticStyle:{color:"#2364F8"},attrs:{type:t.ifSearchPane?"close":"search"},on:{click:t.searchPaneBarHandle}})],1),i("span",{staticClass:"store step2",class:{active:t.showStore}},[i("ta-icon",{attrs:{type:!1===t.showStore?"star":"menu-unfold"},on:{click:function(){e.showStore=!e.showStore}}}),t.showStore?i("span",[t._v(t._s(t.$t("search.favorites")))]):t._e()],1)]),i("div",{staticClass:"leftConContent"},[t._t("leftMenu"),t.ifSearchPane?t._t("searchPane"):t._e(),t.showStore?t._t("commonMenu"):t._e()],2)]):t._e(),"top"!==t.state.menuType?i("div",{staticClass:"dragBar",style:{left:t.leftWidth}}):t._e(),i("div",{staticClass:"centerCon",style:{paddingTop:t.tabHeight}},[i("div",{staticClass:"tabsCon",style:{height:t.tabHeight}},[t._t("tabs")],2),i("div",{staticClass:"iframesCon"},[t._t("iframes")],2),"workTable"===t.state.menuType?i("div",{staticClass:"workTableType"},[i("div",{staticClass:"close-bg left",on:{click:function(e){return t.toggleWorktable("vertical")}}},[i("ta-icon",{attrs:{type:t.workTableMenu.vertical?"step-backward":"step-forward"}})],1),i("div",{staticClass:"close-bg top",on:{click:function(e){return t.toggleWorktable("horizon")}}},[i("ta-icon",{attrs:{type:t.workTableMenu.horizon?"step-backward":"step-forward"}})],1)]):t._e()])])],2)},Ue=[],Ne=function(){var e=this,t=this,n=t.$createElement,i=t._self._c||n;return i("ta-drawer",{attrs:{visible:t.visible,"destroy-on-close":"",width:"350px","wrap-class-name":"msg-list","get-container":t.setContainer},on:{close:t.close}},[i("h4",{staticClass:"msg-head",class:{"msg-new":t.newMsgSize>0}},[t._v(" "+t._s(t.$t("news.newsTitle"))+"（"+t._s(t.newMsgSize)+" "+t._s(t.$t("news.unread"))+"） "),i("ta-icon",{staticClass:"refresh",attrs:{type:"sync"},on:{click:t.refreshMessage}}),i("span",{staticClass:"refresh",staticStyle:{margin:"0px 20px"},on:{click:t.openRecord}},[t._v(t._s(t.$t("news.more")))])],1),i("ta-tabs",{staticClass:"fit",attrs:{"default-active-key":"1",type:"card"},on:{change:t.tabChange},model:{value:t.tabType,callback:function(e){t.tabType=e},expression:"tabType"}},[i("ta-tab-pane",{key:"0",attrs:{tab:t.$t("news.noticeUnread")}},[t._l(t.messageList,(function(e,n){return["0"==e.readSign?i("div",{key:e.mId,staticClass:"msg-item new",class:{active:e==t.clickIndex}},[i("ta-checkbox",{attrs:{checked:e.checked},on:{click:function(n){return t.checkNotice(e)}}}),"01"==e.type?i("ta-icon",{staticStyle:{color:"#ca1064","margin-left":"10px"},attrs:{type:"sound"}}):0==e.type.indexOf("02")?i("ta-icon",{staticStyle:{color:"#04b2dc","margin-left":"10px"},attrs:{type:"form"}}):"03"==e.type?i("ta-icon",{staticStyle:{color:"#108ee9","margin-left":"10px"},attrs:{type:"mail"}}):t._e(),i("ta-icon",{directives:[{name:"show",rawName:"v-show",value:"0"==e.readSign,expression:"item.readSign=='0'"}],staticStyle:{color:"red"},attrs:{type:"exclamation-circle-o"}}),i("div",{staticStyle:{cursor:"pointer"},on:{click:function(n){return t.clickDetail(e)}}},[i("div",{staticClass:"msg-title"},[t._v(" "+t._s(e.title)+" ")]),i("div",{staticClass:"msg-content",domProps:{innerHTML:t._s(e.content)}},[t._v(" "+t._s(e.content)+" ")]),i("div",{staticClass:"msg-time"},[t._v(" "+t._s(e.senderName)+" "+t._s(e.sendDate)+" ")])])],1):t._e()]}))],2),i("ta-tab-pane",{key:"1",attrs:{tab:t.$t("news.noticeRead")}},[t._l(t.messageList,(function(e,n){return["0"!=e.readSign?i("div",{key:e.mId,staticClass:"msg-item new",class:{active:e==t.clickIndex}},[i("ta-checkbox",{attrs:{checked:e.checked},on:{click:function(n){return t.checkNotice(e)}}}),"01"==e.type?i("ta-icon",{staticStyle:{color:"#ca1064","margin-left":"10px"},attrs:{type:"sound"}}):0==e.type.indexOf("02")?i("ta-icon",{staticStyle:{color:"#04b2dc","margin-left":"10px"},attrs:{type:"form"}}):"03"==e.type?i("ta-icon",{staticStyle:{color:"#108ee9","margin-left":"10px"},attrs:{type:"mail"}}):t._e(),i("div",{staticStyle:{cursor:"pointer"},on:{click:function(n){return t.clickDetail(e)}}},[i("div",{staticClass:"msg-title"},[t._v(" "+t._s(e.title)+" ")]),i("div",{staticClass:"msg-content",domProps:{innerHTML:t._s(e.content)}},[t._v(" "+t._s(e.content)+" ")]),i("div",{staticClass:"msg-time"},[t._v(" "+t._s(e.senderName)+" "+t._s(e.sendDate)+" ")])])],1):t._e()]}))],2),i("ta-tab-pane",{key:"2",attrs:{tab:t.$t("news.privateLetter")}},t._l(t.messageList2,(function(e){return i("div",{key:e.chatId,staticClass:"msg-item msg-item-letter",class:{active:e==t.clickIndex,new:"1"==e.newLetter},on:{click:function(n){return t.clickDetail2(e)}}},[i("div",{staticClass:"msg-content"},[t._v(" "+t._s(e.twoName)),i("span",{directives:[{name:"show",rawName:"v-show",value:"0"!=e.chatId,expression:"item.chatId!='0'"}],staticClass:"msg-content-op",on:{click:function(n){return t.removeChat(e,n)}}},[t._v(t._s(t.$t("delete")))])])])})),0)],1),t.showDetail?i("div",{staticClass:"msg-detail",staticStyle:{"padding-top":"70px",width:"500px"}},[i("div",{staticClass:"detail-title"},[i("span",[t._v(t._s(t.title))]),i("span",[t._v(t._s(t.$t("news.sender"))+"："+t._s(t.sender.name))])]),i("div",{staticClass:"msg-detail-con"},[i("div",{staticClass:"msg-detail-detail",domProps:{innerHTML:t._s(t.content)}}),i("div",{directives:[{name:"show",rawName:"v-show",value:null!=t.noticeFileList&&t.noticeFileList.length>=1,expression:"noticeFileList!=null && noticeFileList.length>=1"}]},[i("ta-divider",{attrs:{orientation:"left"}},[t._v(" "+t._s(t.$t("news.files"))+" ")]),i("ta-list",{attrs:{"item-layout":"horizontal","data-source":t.noticeFileList},scopedSlots:t._u([{key:"renderItem",fn:function(e){return i("ta-list-item",{},[i("a",{attrs:{href:t.backUrl+"/message/downloadNoticeFile?annexId="+e.annexId}},[i("ta-icon",{staticStyle:{color:"#0f990f"},attrs:{type:"download"}}),t._v(" "+t._s(e.annexName))],1)])}}],null,!1,2934077177)})],1)]),i("div",{staticClass:"msg-detail-foot"},["03"==t.messageType?i("ta-button",{attrs:{type:"primary"},on:{click:function(e){return t.clickSend()}}},[t._v(" "+t._s(t.$t("news.reply"))+" ")]):0==t.messageType.indexOf("02")?i("ta-button",{attrs:{type:"primary"},on:{click:function(e){return t.clickOpen()}}},[t._v(" "+t._s(t.$t("news.accessFunction"))+" ")]):t._e(),i("ta-button",{on:{click:function(e){return t.clickRemove()}}},[t._v(" "+t._s(t.$t("delete"))+" ")])],1)]):t._e(),t.showDetail2?i("div",{staticClass:"msg-detail",staticStyle:{width:"500px"}},[i("span",{staticClass:"detail-title"},[t._v(t._s(t.sender.name))]),i("div",{staticClass:"msg-detail-con"},[i("div",{staticClass:"chat-content"},[i("div",{directives:[{name:"show",rawName:"v-show",value:"0"!=t.chatId,expression:"chatId!='0'"}],staticClass:"chat-detail-item tip"},[i("span",{staticClass:"chat-history",on:{click:t.chatHistory}},[t._v(t._s(t.$t("news.history")))]),t._v(" ("+t._s(t.$t("news.saved"))+") ")]),t._l(t.chatList,(function(e){return i("div",{key:e.mId},[e.senderId!=t.nowUserId||"you"==e.owner?i("div",{staticClass:"chat-detail-item you"},[i("div",{staticClass:"cd-item-time"},[t._v(" "+t._s(e.sendDate)+" ")]),i("div",{staticClass:"cd-item-content-wrapper clearfix"},[i("div",{staticClass:"cd-item-avatar-wrapper"},[i("ta-avatar",{staticStyle:{backgroundColor:"#87d068"},attrs:{size:"default"}},[t._v(" "+t._s(e.senderName.substring(0,1))+" ")])],1),i("div",{staticClass:"cd-item-content"},[i("i",{staticClass:"cd-item-content-arrow-left"}),i("span",{staticClass:"cd-item-content-text",domProps:{innerHTML:t._s(e.content)}},[t._v(t._s(e.content))]),i("span",{staticClass:"cd-item-content-state "})])])]):i("div",{staticClass:"chat-detail-item me"},[i("div",{staticClass:"cd-item-time"},[t._v(" "+t._s(e.sendDate)+" ")]),i("div",{staticClass:"cd-item-content-wrapper clearfix"},[i("div",{staticClass:"cd-item-avatar-wrapper"},[i("ta-avatar",{staticStyle:{color:"#f56a00",backgroundColor:"#fde3cf"},attrs:{size:"default"}},[t._v(" "+t._s(e.senderName.substring(0,1))+" ")])],1),i("ta-spin",{directives:[{name:"show",rawName:"v-show",value:"0"==e.type,expression:"item.type == '0'"}],attrs:{size:"small"}}),i("div",{staticClass:"cd-item-content"},[i("i",{staticClass:"cd-item-content-arrow-right"}),i("span",{staticClass:"cd-item-content-text"},[t._v(t._s(e.content))]),i("span",{staticClass:"cd-item-content-state "})])],1)])])}))],2),i("ta-textarea",{staticClass:"chat-content-say",attrs:{placeholder:t.$t("news.inputContent")},on:{keydown:t.inputKeydown},model:{value:t.content2,callback:function(e){t.content2=e},expression:"content2"}})],1),i("div",{staticClass:"msg-detail-foot"},[i("ta-button",{attrs:{type:"primary"},on:{click:function(e){return t.clickSend2()}}},[t._v(" "+t._s(t.$t("news.send"))+" ")]),i("ta-button",{on:{click:function(e){return t.clickClear2()}}},[t._v(" "+t._s(t.$t("news.clear"))+" ")]),i("ta-button",{on:{click:function(e){t.showDetail2=!1}}},[t._v(" "+t._s(t.$t("news.close"))+" ")])],1)]):t._e(),"0"==t.tabType||"1"==t.tabType?i("div",{attrs:{slot:"footer"},slot:"footer"},[i("ta-dropdown",[i("ta-menu",{attrs:{slot:"overlay"},on:{click:t.clickNewNotice},slot:"overlay"},[i("ta-menu-item",{key:"01"},[t._v(" "+t._s(t.$t("news.systemNotice"))+" ")]),i("ta-menu-item",{key:"03"},[t._v(" "+t._s(t.$t("news.generalNotice"))+" ")])],1),i("ta-button",{staticStyle:{padding:"0px 10px"},attrs:{type:"primary"}},[t._v(" "+t._s(t.$t("news.new"))),i("ta-icon",{attrs:{type:"up"}})],1)],1),i("ta-button",{staticStyle:{padding:"0 5px"},on:{click:function(e){return t.checkNotices()}}},[t._v(" "+t._s(t.$t("news.selectAll"))+" ")]),i("ta-button",{staticStyle:{padding:"0 5px"},on:{click:function(e){return t.removeNotices()}}},[t._v(" "+t._s(t.$t("delete"))+" ")]),i("ta-button",{staticStyle:{padding:"0 5px"},on:{click:function(e){return t.readNotices()}}},[t._v(" "+t._s(t.$t("news.markRead"))+" ")])],1):"2"==t.tabType?i("div",{attrs:{slot:"footer"},slot:"footer"},[i("ta-button",{staticStyle:{padding:"0px 10px"},attrs:{type:"primary"},on:{click:t.clickShowSelectUserModal}},[t._v(" "+t._s(t.$t("news.addContacts"))+" ")])],1):t._e(),t.showSend?i("div",{staticClass:"msg-send msg-detail"},[i("span",{staticClass:"detail-title"},[t._v(t._s(t.title))]),i("div",{staticClass:"msg-detail-con"},[i("ta-form",{attrs:{"auto-form-create":function(t){e.form=t}}},[i("ta-form-item",{attrs:{label:t.$t("news.receiver"),"label-col":{span:3},"wrapper-col":{span:21}}},[i("ta-user-select",{ref:"withTag",attrs:{id:"withTag",props:t.userDefaultProps,load:t.loadOrgTreeNode,"user-list-data":t.relationUserListData,pagination:!0,"with-tag":!0},on:{close:t.fnCloseUserModal,search:t.fnSearchUser,queryUserList:t.fnQueryUserList,getUserListResult:t.fnGetUserListResult}})],1),i("ta-form-item",{attrs:{label:t.$t("news.theme"),"label-col":{span:3},"wrapper-col":{span:21}}},[i("ta-input",{model:{value:t.sendTitle,callback:function(e){t.sendTitle=e},expression:"sendTitle"}})],1),i("ta-form-item",[i("ta-upload",{attrs:{name:"file",multiple:!0,action:t.backUrl+"/message/uploadNoticeFile","with-credentials":!0,"before-upload":t.beforeUploadNoticeFile,"file-list":t.uploadFileList},on:{change:t.handleUploadChange}},[i("ta-button",[i("ta-icon",{attrs:{type:"upload"}}),t._v(" "+t._s(t.$t("news.uploadFiles"))+" ")],1)],1)],1),i("div",{staticStyle:{width:"100%"}},[i("ta-rich-editor",{ref:"richEditor",staticStyle:{height:"400px"}})],1)],1)],1),i("div",{staticClass:"msg-detail-foot"},[i("ta-button",{attrs:{type:"primary"},on:{click:function(e){return t.sendNotice()}}},[t._v(" "+t._s(t.$t("news.send"))+" ")]),i("ta-button",{on:{click:function(e){return t.close()}}},[t._v(" "+t._s(t.$t("news.close"))+" ")])],1)]):t._e(),i("ta-modal",{attrs:{title:t.$t("news.userSelect"),height:"300px"},on:{ok:t.userSelectOk,close:function(e){t.userModal=!1}},model:{value:t.userModal,callback:function(e){t.userModal=e},expression:"userModal"}},[i("ta-user-input",{attrs:{"org-load-fn":t.handleLoadOrgNode,"user-load-fn":t.handleQueryUserList,"user-select-call":t.handleGetUserListResult}})],1)],1)},Ee=[],Be=(n(32564),n(86704)),De=n(70481),Re="message",Pe={queryAllTaOrg:function(e,t){Base.submit(null,{url:Re+"/getOrgByAsync",data:e},{successCallback:function(e){return t(e)}})},queryBatchUserByOrgId:function(e,t){Base.submit(null,{url:Re+"/queryEffectiveUser",data:e},{successCallback:function(e){return t(e)}})},queryUserList:function(e,t){var n=e.orgId,i=e.userId,s=e.includeChild,a=e.pageSize,o=e.pageNum,r=e.searchVal,l=e.searchType,c=e.searchParam,u={orgId:n,userId:i,showChildUser:s?1:0,pageSize:a,pageNumber:o,name:r};l&&(u[l]=c),Base.submit(null,{url:Re+"/queryEffectiveUser",data:u},{successCallback:function(e){return t(e)}})}},Oe="01",Fe="02",_e="03",Ve="04",Ge="05",We=["audio/chang.wav","audio/dingdingding.wav","audio/duanxin.wav","audio/mofabang.wav","audio/rtx.wav"],He=new De.Z({message:"有消息了!",effect:"flash",audio:{file:"audio/dingdingding.wav"},interval:1e3,updateFavicon:{textColor:"#fff",backgroundColor:"#2F9A00"},notification:{title:"通知！",icon:"",body:"您来了一条新消息,来自 - TA404"}}),je={htmlEncode:function(e){var t=document.createElement("div");void 0!=t.textContent?t.textContent=e:t.innerText=e;var n=t.innerHTML;return t=null,n},htmlDecode:function(e){var t=document.createElement("div");t.innerHTML=e;var n=t.innerText||t.textContent;return t=null,n}},ze={name:"msgList",props:["visible"],data:function(){return{userDefaultProps:{treeNodeKey:"orgId",treeLabel:"label",treeChildren:"children",treeIsLeaf:"isLeaf",listKey:"userId",listTitle:"name",listSubTitle:"mobile",listDescription:"namePath"},relationUserListData:[],userSelectData:[],userListData:[],searchData:[],selectedUserId:"",selectedLoginId:"",selectedName:"",tabType:"0",userModal:!1,backUrl:"",currentUploadFile:{},uploadFileList:[],uploadFileIds:null,messageList:[],checkboxList:[],userList:[],selectUsers:[],clickIndex:"",showDetail:!1,showSend:!1,mId:"",content:"",sender:{},title:"",menuUrl:"",hasFile:!1,noticeFileList:[],nowUserId:"",nowUserName:"",nowLoginId:"",sendTitle:"",sendContext:"",messageType:"",messageList2:[{chatId:"0",twoUserId:"000001",twoName:this.$t("news.xiaoY"),chatList:[{mId:"11",owner:"you",content:this.$t("news.helloY"),time:"2019-08-25 14:02:15",senderId:"000001",senderName:this.$t("news.Y")}]}],chatId:"",chatList:[],showDetail2:!1,content2:"",colorList:["#1B65B9"],nameColor:""}},computed:{newMsgSize:function(){return this.messageList.filter((function(e){return"0"===e.readSign})).length}},watch:{newMsgSize:{handler:function(e,t){this.$emit("update:noticeNum",this.newMsgSize)}}},mounted:function(){var e=this;this.$store.dispatch("setUserInfo").then((function(t){Ce=0,ke=e.nowUserId=t.userId,e.nowLoginId=t.loginId,"string"==typeof ke?(e.nowUserName=t.userName,Se=e.receiveMessage,Te=e.reconnect,Ie=e.createWebSocket,e.Base.submit(null,{url:"message/queryAddress"},{successCallback:function(t){faceConfig.basePath.indexOf("http")>=0?e.backUrl=faceConfig.basePath:e.backUrl="http://"+t.data.ip+":"+t.data.port+faceConfig.basePath,Ae=e.backUrl.replace("http://",""),Ie(Ae)}}),e.queryUserMessage(e.nowUserId)):e.$message.error(e.$t("news.notLogin"))}))},methods:(0,i.Z)((0,i.Z)({},je),{},{createWebSocket:function(e){var t;try{t="WebSocket"in window?new WebSocket("ws://"+e+"/webSocketServer2"):"MozWebSocket"in window?new MozWebSocket("ws://"+e+"/webSocketServer2"):new SockJS("http://"+e+"/sockjs/webSocketServer")}catch(n){}"undefined"!==typeof t&&(t.onopen=function(e){Ce=0,t.send(ke)},t.onmessage=function(e){"pong"!=e.data&&Se(JSON.parse(e.data))},t.onerror=function(e){},t.onclose=function(t){Ce++,Ce<=3&&Te(e)})},reconnect:function(e){setTimeout((function(){Ie(e)}),2e3)},keepAlive:function(e){Me&&clearInterval(Me),Me=setInterval((function(){e.send("ping")}),3e4)},fnSearchUser:function(e){},loadOrgTreeNode:function(e,t){if(0===e.level&&Pe.queryAllTaOrg(null,(function(e){var n=e.data.orgTreeData;if(n[0]&&n[0].children&&n[0].children instanceof Array&&n[0].children.length>0){var i=n[0].children.map((function(e){var t=e;e.childNum>0&&(t.children=[])}));n[0].children=i}return t(n)})),e.level>=1){var n=e.data.orgId,i=e.data.isLeaf,s={orgId:n};Pe.queryAllTaOrg(s,(function(e){var n=e.data.orgTreeData;return i&&(n=n.map((function(e){var t=e;t.children=[]}))),t(n)}))}},fnCloseUserModal:function(e){},fnQueryUserList:function(e,t,n,i,s){var a=this,o={orgId:e,showChildUser:t?"1":"0",pageNumber:n,pageSize:10};i&&(o[i]=s),Pe.queryBatchUserByOrgId(o,(function(e){a.relationUserListData=e.data.userList.list}))},fnGetUserListResult:function(e,t,n){var i=this;this.selectUsers=[],e.forEach((function(e){i.selectUsers.push(e.userId)}))},handleLoadOrgNode:function(e,t){var n=e.data&&e.data.orgId||"";Pe.queryAllTaOrg({orgId:n},(function(e){t(e.data.orgTreeData)}))},handleGetUserListResult:function(e){e&&(this.selectedUserId=e.userId,this.selectedLoginId=e.loginId,this.selectedName=e.name)},handleQueryUserList:function(e,t){var n=e.orgId,i=e.userId,s=e.includeChild,a=e.pageSize,o=e.pageNum,r=e.searchVal,l=e.searchType,c=e.searchParam;Pe.queryUserList({orgId:n,userId:i,includeChild:s,pageSize:a,pageNum:o,searchVal:r,searchType:l,searchParam:c},(function(e){t(e.data.userList.list)}))},setContainer:function(){return document.body},refreshMessage:function(){this.queryUserMessage(this.nowUserId)},openRecord:function(){Base.openTabMenu({url:"messagemg.html#/recordMg",id:"messageRecord",name:this.$t("news.recycle")})},tabChange:function(e){this.tabType=e,"0"===e||"1"===e?this.showDetail2=!1:"2"===e&&(this.showDetail=!1,this.showSend=!1)},clickDetail:function(e){var t=this;this.clickIndex=e,this.showDetail=!0,this.showDetail2=!1,this.showSend=!1,this.uploadFileIds=null,this.mId=e.mId,this.content=e.content,this.title=e.title,this.sender.name=e.senderName,this.sender.id=e.senderId,this.messageType=e.type,this.menuUrl=e.menuUrl,"0"===e.readSign&&(e.menuUrl&&Base.openTabMenu({url:this.menuUrl,id:"messageOpen",name:this.title}),this.Base.submit(null,{url:"message/readNotice",data:{mId:e.mId,userId:this.nowUserId}}),e.readSign="1"),(this.noticeFileList=e.noticeFileList)||this.Base.submit(null,{url:"message/queryNoticeFiles",data:{mId:e.mId}},{successCallback:function(n){t.noticeFileList=e.noticeFileList=n.data.noticeFileList}})},clickNewNotice:function(e){var t,n;this.messageType=e.key,this.title="01"==this.messageType?this.$t("news.systemNotice"):this.$t("news.generalNotice"),this.showDetail=!1,this.showSend=!0,this.selectUsers=[],null===(t=this.$refs.withTag)||void 0===t||t.deleteAll(),this.sendTitle="",null===(n=this.$refs.richEditor)||void 0===n||n.clearContent(),this.uploadFileList=[]},checkNotice:function(e){e.checked=!e.checked},checkNotices:function(){var e=this.tabType,t=this.messageList.findIndex((function(t){return t.readSign===e}));if(t>=0){var n=!this.messageList[t].checked;this.messageList.filter((function(t){return t.readSign===e})).forEach((function(e){return e.checked=n}))}},removeNotices:function(){var e=this,t=this.tabType,n=this.messageList.filter((function(e){return e.checked&&e.readSign===t})).map((function(e){return e.mId})).join(",");n&&0!=n.length?this.Base.submit(null,{url:"message/removeNotices",data:{mIds:n,userId:this.nowUserId}},{successCallback:function(t){e.queryUserMessage(e.nowUserId)}}):message.warn("请勾选要删除的数据!")},readNotices:function(){var e=this.messageList.filter((function(e){return e.checked})).map((function(e){return e.readSign="1",e.mId})).join(",");e&&0!=e.length?this.Base.submit(null,{url:"message/readNotices",data:{mIds:e,userId:this.nowUserId}},{successCallback:function(e){}}):message.warn("请勾选要标记的数据!")},handleUploadChange:function(e){var t=e.fileList;"done"===e.file.status&&e.file.response&&(200===e.file.response.code&&e.file.response.data.fileId?(t.forEach((function(t){t.uid===e.file.uid&&(t.status="success",t.fileId=e.file.response.data.fileId)})),this.$message.success(this.$t("news.uploadSucceeded"))):(t.forEach((function(t){t.uid===e.file.uid&&(t.status="error")})),this.$message.error(e.file.response.errors[0].msg))),this.uploadFileList=t},beforeUploadNoticeFile:function(e,t){return!(e.size>20971520)||(this.$message.error(this.$t("news.uploadRules")),e.status="error",!1)},uploadNoticeFile:function(e){var t=this;this.Base.submit(null,{url:"message/uploadNoticeFile",data:{file:e.file},isFormData:!0},{successCallback:function(n){null==t.uploadFileIds?t.uploadFileIds=n.data.fileId:t.uploadFileIds+=","+n.data.fileId,e.status="done"}})},clickOpen:function(){Base.openTabMenu({url:this.menuUrl,id:"messageOpen",name:this.title})},clickSend:function(){this.showDetail=!1,this.showSend=!0,this.selectUsers=[],this.selectUsers.push(this.sender.id),this.sendTitle="Re: "+this.title,this.title=this.$t("news.reply")+this.title},clickRemove:function(){var e=this;this.Base.submit(null,{url:"message/removeNotices",data:{mIds:this.mId,userId:this.nowUserId}},{successCallback:function(t){var n=e.messageList.findIndex((function(t){return t.mId===e.mId}));e.messageList.splice(n,1),e.showDetail=!1}})},returnDetail:function(){this.showDetail=!0,this.showSend=!1,this.uploadFileIds=null},sendNotice:function(){var e=this,t={};t.title=this.sendTitle,t.content=Be.DS.encode(this.$refs.richEditor.getHtml()),t.senderId=this.nowUserId,t.senderName=this.nowUserName,t.type=this.messageType,t.readSign="0";var n=JSON.stringify(t),i=this.selectUsers.join(",");if(0!==i.length)if(0!==t.title.length)if(0!==this.$refs.richEditor.getText().length){var s=this.uploadFileList.filter((function(e){return e.fileId})).map((function(e){return e.fileId})).join(",");this.Base.submit(null,{url:"message/sendNotice2",data:{userIds:i,voStr:n,annexIds:s}},{successCallback:function(t){var n;e.$message.info(e.$t("news.sendSuccess")),e.sendTitle="",e.$refs.richEditor.clearContent(),e.uploadFileList=[],null===(n=e.$refs.withTag)||void 0===n||n.deleteAll()}})}else this.$message.error(this.$t("news.contentEmpty"));else this.$message.error(this.$t("news.titleEmpty"));else this.$message.error(this.$t("news.receiverEmpty"))},clickDetail2:function(e){var t=this;this.clickIndex=e,this.showDetail=!1,this.showDetail2=!0,this.chatId=e.chatId,this.sender.name=e.twoName,this.sender.id=e.twoUserId,this.messageType=e.type;var n=e.chatId.charCodeAt()%4;this.nameColor=this.colorList[n],this.chatList=e.chatList,e.chatList.length>0?this.Base.submit(null,{url:"message/readLetter",data:{chatId:this.chatId,userId:this.nowUserId}},{successCallback:function(e){}}):"1"===e.newLetter&&this.Base.submit(null,{url:"message/queryUserLetterNoRead",data:{chatId:this.chatId,userId:this.nowUserId}},{successCallback:function(n){t.chatList=e.chatList=n.data.letterList}}),e.newLetter="0"},clickShowSelectUserModal:function(){this.userModal=!0},userSelectOk:function(){var e=this;if(""!==this.selectedLoginId){var t,n=[this.selectedUserId,this.selectedLoginId,this.selectedName];if(n[1]!==this.nowLoginId){if(t=n[1]>this.nowLoginId?n[1]+"-"+this.nowLoginId:this.nowLoginId+"-"+n[1],this.messageList2.filter((function(e){return e.chatId===t})).length>0);else{var i={chatId:t,twoUserId:n[0],twoName:n[2],chatList:[]};this.Base.submit(null,{url:"message/createChat",data:{chatId:t,owner:this.nowUserId,twoUserId:n[0],twoName:n[2]}},{successCallback:function(t){e.messageList2.push(i)}})}this.userModal=!1}else this.$message.info(this.$t("news.chooseSelf"))}else this.userModal=!1},removeChat:function(e,t){var n=this;t.stopPropagation(),this.Base.submit(null,{url:"message/removeChat",data:{chatId:e.chatId,userId:this.nowUserId}},{successCallback:function(t){var i=n.messageList2.findIndex((function(t){return t.chatId===e.chatId}));n.messageList2.splice(i,1)}})},clickSend2:function(){if(0!==this.content2.length){var e={chatId:this.chatId,senderId:this.nowUserId,senderName:this.nowUserName,receiverId:this.sender.id,content:this.content2,type:"0",readSign:"0",owner:"me"};if(e.sendDate=(new Date).toLocaleString(),this.content2="",this.chatList.push(e),"0"===this.chatId)return this.chatList.push(this.reply(e.content)),void(e.type=Ve);e.sendDate="",this.Base.submit(null,{url:"message/sendLetter2",data:{receiverId:e.receiverId,voStr:JSON.stringify(e)}},{successCallback:function(t){e.type=Ve}})}else this.$message.error(this.$t("news.contentEmpty"))},reply:function(e){var t={owner:"you",senderId:"000001"};if(t.senderName=this.$t("news.Y"),-1!==e.indexOf("help"))t.content='<a target="_blank" href="http://114.116.130.110/docs/#/docs/infos/vue/introduce-cn">'+this.$t("news.helpDocument")+"</a>";else if("1"===e)t.content='<a target="_blank" href="http://114.116.130.110/docs/#/docs/infos/vue/introduce-cn">'+this.$t("news.helpDocument")+"</a>";else if("2"===e)t.content='<a target="_blank" href="http://www.baidu.com">'+this.$t("news.projectIntroduction")+"</a>";else if("3"===e)t.content=this.$t("news.noData");else if("4"===e)t.content=this.$t("news.support");else if("5"===e)t.content=this.$t("news.suggest");else{var n=Math.ceil(10*Math.random());switch(n){case 1:t.content=this.$t("news.AI.msg1");break;case 2:t.content=this.$t("news.AI.msg2");break;case 3:t.content=this.$t("news.AI.msg3");break;case 4:t.content=this.$t("news.AI.msg4");break;case 5:t.content=this.$t("news.AI.msg5");break;default:t.content=e.replace("你","我").replace("you","I").replace("不","").replace("吗","").replace("?","!").replace("？","！")}}return t},inputKeydown:function(e){e.ctrlKey&&13===e.keyCode&&this.clickSend2()},clickClear2:function(){this.chatList=[]},chatHistory:function(){var e=this;this.Base.submit(null,{url:"message/queryUserLetter",data:{chatId:this.chatId}},{successCallback:function(t){e.chatList=t.data.letterList}})},close:function(){this.showDetail=!1,this.showDetail2=!1,this.showSend=!1,this.uploadFileIds=null,this.uploadFileList=[],this.clickIndex="",this.tabType="0",this.$emit("close")},queryUserMessage:function(e){var t=this;this.Base.submit(null,{url:"message/queryUserMessage",data:{userId:this.nowUserId,sign:"002"}},{successCallback:function(e){e.data.notice.forEach((function(e){return e.checked=!1})),t.messageList=e.data.notice,e.data.chat&&e.data.chat.length>0&&(t.messageList2=t.messageList2.slice(0,1),t.messageList2.push.apply(t.messageList2,e.data.chat),t.messageList2.forEach((function(e){e.chatList||(e.chatList=[])})))}})},receiveMessage:function(e){var t=this.$createElement,n=t("ta-icon",{attrs:{type:"question"},style:"color: #182027"});Oe===e.type?(n=t("ta-icon",{attrs:{type:"sound"},style:"color: #ca1064"}),this.receiveNotice(e,n)):0===e.type.indexOf(Fe)?(n=t("ta-icon",{attrs:{type:"form"},style:"color: #04b2dc"}),this.receiveNotice(e,n)):_e===e.type?(n=t("ta-icon",{attrs:{type:"mail"},style:"color: #108ee9"}),this.receiveNotice(e,n)):Ve===e.type?(n=t("ta-icon",{attrs:{type:"bell"},style:"color: #becc09"}),this.receiveLetter(e,n)):Ge===e.type&&(n=t("ta-icon",{attrs:{type:"bell"},style:"color: #dc7d04"}),this.receiveLetter(e,n))},receiveNotice:function(e,t){if(e.checked=!1,this.messageList.unshift(e),this.showReceiveNotice(e,t),0===e.type.indexOf(Fe)){var n=parseInt(e.type.charAt(e.type.length-1))%5;He.setURL(We[n])}He.player(),He.notify({})},receiveLetter:function(e,t){var n,i=!0;for(var s in this.messageList2)if(this.messageList2[s].chatId===e.chatId){i=!1,this.messageList2[s].chatList.push(e),this.messageList2[s].newLetter="1",n=this.messageList2[s];break}i&&(n={chatId:e.chatId,twoUserId:e.senderId,twoName:e.senderName,newLetter:"1",chatList:[]},n.chatList.push(e),this.messageList2.push(n)),this.showReceiveLetter(e,t,n)},showReceiveNotice:function(e,t){var n=this,i=e.mId;notification.open({message:e.title,description:function(t){return t("div",{style:{"text-overflow":"ellipsis",overflow:"hidden","max-height":"100px"},domProps:{innerHTML:e.content}})},duration:4.5,icon:t,btn:function(t){return t("ta-button",{props:{type:"primary",size:"small"},on:{click:function(){return n.openNoticeDetail(e)}}},n.$t("news.detail"))},key:i})},openNoticeDetail:function(e){this.$emit("update:visible",!0),this.tabType="0",this.clickDetail(e)},showReceiveLetter:function(e,t,n){var i=this,s=e.mId;notification.open({message:n.twoName,description:function(t){return t("div",{style:{"text-overflow":"ellipsis",overflow:"hidden","max-height":"100px"},domProps:{innerHTML:e.content}})},duration:2.5,icon:t,btn:function(e){return e("ta-button",{props:{type:"primary",size:"small"},on:{click:function(){return i.openLetterDetail(n)}}},i.$t("news.detail"))},key:s})},openLetterDetail:function(e){this.$emit("update:visible",!0),this.tabType="2",this.clickDetail2(e)}})},$e=ze,Ye=n(1001),Qe=(0,Ye.Z)($e,Ne,Ee,!1,null,null,null),Je=Qe.exports,Ke=n(76698),Xe=n(96565),qe=(n(34530),{name:"indexLayout",data:function(){return{fullscreenVisible:!1,pageSizeCtr:!1,seValue:"",visible:!1,tabHeight:"40px",noticeNum:0,showStore:!1,driver:null}},components:{msgList:Je},computed:(0,i.Z)((0,i.Z)({},(0,l.Se)({state:"getStateValue",collapsed:"getCollapsed",getHeaderHeight:"getHeaderHeight"})),{},{logoWidth:function(){var e=this.state,t=e.leftCloseWidth,n=e.logoWidth,i=e.menuType,s=n;return"top"!==i&&"simple"!==i&&this.collapsed&&(s=t),{width:s,minWidth:s}},leftWidth:function(){var e=this.state,t=e.leftCloseWidth,n=e.leftWidth,i=e.menuType,s=e.workTableMenuVertical;return"workTable"===i?s?n:"0px":this.collapsed?t:n},headerHeight:function(){var e=this.state,t=e.menuType,n=e.workTableMenuHorizon;return"workTable"===t?n?this.getHeaderHeight:"0px":this.getHeaderHeight},ifSearchPane:function(){return this.state.ifSearchPane||(this.seValue=""),this.state.ifSearchPane},workTableMenu:function(){return{vertical:this.state.workTableMenuVertical,horizon:this.state.workTableMenuHorizon}},menuDisplayH:function(){var e=this.state.menuType,t=this.workTableMenu.horizon;return"workTable"===e?{display:t?"":"none"}:{}},menuDisplayV:function(){var e=this.state.menuType,t=this.workTableMenu.vertical;return"workTable"===e?{display:t?"":"none"}:{}}}),mounted:function(){this.driver=new Ke.Z({allowClose:!1})},methods:{searchMenuListHandle:function(e){var t=this.seValue;e&&(t=e.target.value.trim()),this.state.searchValue!==t&&this.$store.dispatch("setStateValue",{searchValue:t}),this.$store.dispatch("setStateValue",{ifSearchPane:!0})},searchPaneBarHandle:function(){this.$store.dispatch("setStateValue",{ifSearchPane:!this.ifSearchPane})},toggleCollapsed:function(){this.$store.dispatch("setStateValue",{collapsed:!this.collapsed})},showUserInfo:function(){this.$store.dispatch("setStateValue",{showUserInfo:!0})},toggleWorktable:function(e){if(this.$store.dispatch("setStateValue",{workTableMenuVertical:!this.workTableMenu.vertical}),"horizon"===e){var t=!this.workTableMenu.horizon;this.$store.dispatch("setStateValue",{workTableMenuHorizon:t}),this.$store.dispatch("setStateValue",{workTableMenuVertical:t})}},fullScreen:function(){if(!0===this.pageSizeCtr)this.pageSizeCtr=!1,document.exitFullscreen?document.exitFullscreen():document.msExitFullscreen?document.msExitFullscreen():document.mozCancelFullScreen?document.mozCancelFullScreen():document.webkitCancelFullScreen&&document.webkitCancelFullScreen();else{this.pageSizeCtr=!0;var e=document.documentElement;e.requestFullscreen?e.requestFullscreen():e.msRequestFullscreen?e.msRequestFullscreen():e.mozRequestFullScreen?e.mozRequestFullScreen():e.webkitRequestFullScreen&&e.webkitRequestFullScreen()}},guide2:function(){(0,Xe.Z)({url:"/appeal/common/downloadFIle",type:"application/octet-stream",fileName:"医保小助手.zip"}).then((function(e){0==e.size&&alert("下载小助手安装包失败,请与管理员联系")})).catch((function(e){0==e.size&&alert("下载小助手安装包失败,请与管理员联系")}))},guide:function(){var e=this.$t("help"),t=[{element:".step1",popover:{title:e.step1Title,description:e.step1Desc,position:"left"}},{element:".step2",popover:{title:e.step2Title,description:e.step2Desc,position:"right"}},{element:".step3",popover:{title:e.step3Title,description:e.step3Desc,position:"bottom"}}];this.driver.defineSteps(t),this.driver.start()}}}),et=qe,tt=(0,Ye.Z)(et,xe,Ue,!1,null,"7bd8329b",null),nt=tt.exports,it=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("ta-menu",{ref:"menuHorizon",staticClass:"index-top-menu",style:e.menuHeight,attrs:{mode:"horizontal",theme:e.theme,data:e.menuList,props:e.props,"selected-keys":e.selectedKey},on:{click:e.clickMenu}})},st=[],at={data:function(){return{props:{key:"id",title:"name"}}},computed:(0,i.Z)((0,i.Z)({},(0,l.Se)({state:"getStateValue",menuLeft:"getMenuListLeft",getHeaderHeight:"getHeaderHeight"})),{},{theme:function(){return"base"===this.state.headerTheme||"top"==this.state.menuType?"base":"light"},selectedKey:function(){var e="",t=this.state,n=t.activeMenuOne,i=t.activeMenuTwo,s=t.menuType;return e="top"==s?i:n,[e||""]},menuList:function(){var e=[],t=this.state,n=t.menuList,i=t.menuType;if("top"==i)e=n;else if("leftTop"==i||"simple"==i||"workTable"==i){var s=JSON.parse(JSON.stringify(n));s.forEach((function(t){delete t.children,e.push(t)}))}return e},menuHeight:function(){var e=parseInt(this.getHeaderHeight);return{height:e+"px",lineHeight:e+"px"}},ifSearchPane:function(){return this.state.ifSearchPane}}),methods:{clickMenu:function(e){var t=e.key,n=e.keyPath,i=this.state.menuType;"top"==i?(this.$store.dispatch("setActiveMenu",{level:"one",menuId:n[n.length-1]}),this.$store.dispatch("setActiveMenu",{level:"two",menuId:t}),this.setTabMenu(n)):"leftTop"!=i&&"simple"!=i&&"workTable"!=i||(this.$store.dispatch("setActiveMenu",{level:"one",menuId:t}),0==this.menuLeft.length&&(this.$store.dispatch("setActiveMenu",{level:"two",menuId:t}),this.setTabMenu(n))),this.searchPaneBarHandle()},searchPaneBarHandle:function(){this.ifSearchPane&&this.$store.dispatch("setStateValue",{ifSearchPane:!this.ifSearchPane})},setTabMenu:function(e){for(var t=this.state,n=t.menuList,i=t.barType,s=[],a=n,o=function(t){-1==e[t].indexOf("overflowed-indicator")&&(a=a.filter((function(n){return n.id==e[t]}))[0],"breadcrumb"==i&&s.push(a.name),a=t?a.children:a)},r=e.length-1;r>-1;r--)o(r);s.length&&this.$store.dispatch("setStateValue",{breadcrumb:s}),this.$store.dispatch("addTabMenuList",{value:a})}}},ot=at,rt=(0,Ye.Z)(ot,it,st,!1,null,null,null),lt=rt.exports,ct=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{class:["menu-vertical","menu-vertical--"+e.state.leftTheme]},[n("ta-config-provider",{attrs:{"get-popup-container":e.popupContainer}},[n("ta-menu",{style:{width:"100%"},attrs:{mode:"dropdown"==e.state.menuLeftStyle?"inline":"vertical","inline-collapsed":e.collapsed,theme:e.state.leftTheme,"inline-indent":10,data:e.menuList,props:e.props,"selected-keys":[e.state.activeMenuTwo||""],"open-keys":e.openKeys,"custom-menu-item":e.customMenuItem},on:{openChange:e.openChange,menuItemHover:e.showPopCon,click:e.clickMenu,"update:openKeys":function(t){e.openKeys=t},"update:open-keys":function(t){e.openKeys=t}}}),n("div",{directives:[{name:"show",rawName:"v-show",value:!e.collapsed&&e.support,expression:"!collapsed && support"}],staticClass:"bottom-left"},[n("div",[e._v(e._s(e.supportText))]),e._l(e.support,(function(t,i){return n("div",{key:i},[n("span",{staticStyle:{display:"inline-block",width:"55px"}},[e._v(e._s(t.supportname))]),n("span",[e._v(e._s(t.supportnum))]),n("br")])}))],2)],1)],1)},ut=[],dt={name:"menu-vertical",data:function(){return{props:{key:"id",title:"name"},openKeys:[],support:[],supportText:"服务支持咨询:"}},computed:(0,i.Z)({},(0,l.Se)({state:"getStateValue",collapsed:"getCollapsed",menuList:"getMenuListLeft"})),created:function(){this.querysupport()},methods:{querysupport:function(){var e=this;this.Base.submit(null,{url:"assistantWindow/querySupport",data:{},autoValid:!1},{successCallback:function(t){var n=t.data;if(n.namelenth>0&&n.numlenth>0){e.supportText="服务支持咨询:";var i=1;i=n.namelenth>n.numlenth?n.namelenth:n.numlenth;for(var s=0;s<i;s++)n["supportname"+(0==s?"":s+1)]&&n["supportnum"+(0==s?"":s+1)]&&e.support.push({supportname:n["supportname"+(0==s?"":s+1)]+" ",supportnum:n["supportnum"+(0==s?"":s+1)]})}else e.supportText=""},failCallback:function(t){e.$message.error("查询失败")}})},showPopCon:function(e){this.collapsed&&e.hover?document.getElementById("popCon").style.display="block":document.getElementById("popCon").style.display="none"},popupContainer:function(){return document.getElementById("popCon")},openChange:function(e){"dropdown"!=this.state.menuLeftStyle||0!=this.collapsed?0==e.length?document.getElementById("popCon").style.display="none":document.getElementById("popCon").style.display="block":document.getElementById("popCon").style.display="none"},clickMenu:function(e){var t=e.key,n=e.keyPath;this.$store.dispatch("setActiveMenu",{level:"two",menuId:t});for(var i=this.menuList,s=function(e){i=e?i.filter((function(t){return t.id==n[e]}))[0].children:i.filter((function(t){return t.id==n[e]}))[0]},a=n.length-1;a>-1;a--)s(a);if(this.$store.dispatch("addTabMenuList",{value:i}),"breadcrumb"==this.state.barType){var o=[],r=this.state,l=r.menuType,c=r.menuList,u=r.activeMenuOne,d=this.menuList;"leftTop"==l&&o.push(c.filter((function(e){return e.id==u}))[0].name);for(var h=function(e){d=d.filter((function(t){return t.id==n[e]}))[0],o.push(d.name),d=e?d.children:d},m=n.length-1;m>-1;m--)h(m);this.$store.dispatch("setStateValue",{breadcrumb:o})}},customMenuItem:function(e){var t=this,n=this.$createElement;return n("span",[e.name,e.isCommonMenu?"":n("ta-icon",{class:"menuStar",attrs:{type:"star",title:"点击收藏"},on:{click:function(n){n.stopPropagation(),t.addCommonMenu(e)}}})])},addCommonMenu:function(e){this.$store.dispatch("addCommonMenu",e)}},watch:{menuList:function(e,t){if(this.state.dropdownOpenMenu){var n=[];e.map((function(e){e.id&&n.push(e.id)})),this.openKeys=n}else this.openKeys=[]}}},ht=dt,mt=(0,Ye.Z)(ht,ct,ut,!1,null,null,null),ft=mt.exports,pt=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{class:["searchPane","searchPane--"+e.state.leftTheme]},[n("ta-menu",{style:{width:"100%"},attrs:{mode:"inline",theme:e.state.leftTheme,data:e.menuList,props:e.props,"selected-keys":[e.state.activeMenuTwo||""]},on:{click:e.clickMenu}})],1)},gt=[],vt={name:"search-pane",data:function(){return{props:{key:"id",title:"name"}}},computed:(0,i.Z)({},(0,l.Se)({state:"getStateValue",menuList:"getSearchMenuList"})),methods:{clickMenu:function(e){var t=e.key;e.keyPath;this.$store.dispatch("setActiveMenu",{level:"two",menuId:t});var n=this.menuList.filter((function(e){return e.id==t}))[0];this.$store.dispatch("addTabMenuList",{value:n})}}},bt=vt,wt=(0,Ye.Z)(bt,pt,gt,!1,null,"64867b3b",null),yt=wt.exports,At=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{class:["commonMenu","commonMenu--"+e.state.leftTheme]},[n("ta-menu",{style:{width:"100%"},attrs:{mode:"inline",theme:e.state.leftTheme,data:e.menuList,props:e.props,"selected-keys":[e.state.activeMenuTwo||""],"custom-menu-item":e.customMenuItem},on:{click:e.clickMenu}})],1)},kt=[],It={name:"commonMenu.vue",data:function(){return{props:{key:"id",title:"name"}}},computed:(0,i.Z)({},(0,l.Se)({state:"getStateValue",menuList:"getCommonMenuList"})),methods:{clickMenu:function(e){var t=e.key;e.keyPath;this.$store.dispatch("setActiveMenu",{level:"two",menuId:t});var n=this.menuList.filter((function(e){return e.id==t}))[0];this.$store.dispatch("addTabMenuList",{value:n})},customMenuItem:function(e){var t=this,n=this.$createElement;return n("span",[e.name,n("ta-icon",{class:"menuDelete",attrs:{type:"delete",title:this.$t("search.removeFavorites")},on:{click:function(n){n.stopPropagation(),t.deleteCommonMenu(e)}}})])},deleteCommonMenu:function(e){this.$store.dispatch("deleteCommonMenu",e)}}},St=It,Tt=(0,Ye.Z)(St,At,kt,!1,null,"0b29fed8",null),Mt=Tt.exports,Ct=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"fit"},["tab"===e.state.barType?n("div",{ref:"index-tabs",staticClass:"index-tabs"},[n("div",{ref:"index-worktable",staticClass:"worktable-tab",class:{active:"worktable"===e.activeTab},attrs:{id:"worktable",title:e.worktableName},on:{click:function(t){return e.setTabsted({id:"worktable"})}}},[e._v(" "+e._s(e.worktableName)+" ")]),e._l(e.list1,(function(t){return n("div",{key:t.id,ref:"tab-item",refInFor:!0,staticClass:"tab-item",class:{active:e.activeTab===t.id},attrs:{title:t.name,id:t.id+"-tab"},on:{click:function(n){return e.setTabsted(t)}}},[n("div",[e._v(e._s(t.name))]),n("span",{staticClass:"btn-close",on:{click:function(n){return n.stopPropagation(),e.closeTabFn(t)}}},[n("ta-icon",{attrs:{type:"close"}})],1)])})),n("div",{ref:"tab-more",staticClass:"tab-more",class:{active:e.ifShowMore},attrs:{flag:"tab-more"}},[n("span",{attrs:{flag:"tab-more"},on:{click:function(t){t.stopPropagation(),e.ifShowMore=!e.ifShowMore}}},[n("ta-icon",{attrs:{type:"setting"}})],1),e.ifShowMore?n("div",{staticClass:"tab-more-list",attrs:{flag:"tab-more"}},[n("div",{staticClass:"more-worktable",attrs:{flag:"tab-more"},on:{click:function(t){return t.stopPropagation(),e.setTabsted({id:"worktable"})}}},[e._v(" "+e._s(e.worktableName)+" ")]),e._l(e.list2,(function(t){return n("div",{key:t.id,staticClass:"tab-item",attrs:{id:t.id+"-tab",title:t.name,flag:"tab-more"},on:{click:function(n){return n.stopPropagation(),e.setTabsted(t)}}},[e._v(" "+e._s(t.name)+" "),n("span",{attrs:{flag:"tab-more"}},[e.activeTab===t.id?n("ta-icon",{attrs:{type:"activeTab==menu.id?'close-circle':close"},on:{click:function(n){return n.stopPropagation(),e.closeTabFn(t)}}}):e._e()],1)])})),n("div",{staticClass:"close-all",attrs:{flag:"tab-more"},on:{click:function(t){return e.closeAllTab()}}},[e._v(" "+e._s(e.$t("workTable.closeAll"))+" ")])],2):e._e()])],2):n("ta-breadcrumb",{staticClass:"index-breadcrumb"},[n("ta-breadcrumb-item",[n("a",{on:{click:e.toWorktable}},[n("ta-icon",{attrs:{type:"home"}}),e._v(" "+e._s(e.worktableName))],1)]),e._l(e.state.breadcrumb,(function(t,i){return n("ta-breadcrumb-item",{key:i},[e._v(" "+e._s(t)+" ")])}))],2)],1)},Lt=[],Zt={name:"indexTab",data:function(){return{list1:[],list2:[],ifShowMore:!1}},computed:(0,i.Z)((0,i.Z)({},(0,l.Se)({state:"getStateValue",collapsed:"getCollapsed",tabList:"getTabMenuList",getTabMenuByUrl:"getTabMenuByUrl",getActiveIframe:"getActiveIframe"})),{},{activeTab:function(){return this.state.activeTabMenu},worktableName:function(){var e=this.state.worktable;return e.name||this.$t("workTable.workTable")}}),mounted:function(){var e=this;window.addEventListener("resize",(function(){e.setTab()}),!1)},methods:{setTab:function(){if("tab"===this.state.barType){this.list1=this.tabList,this.list2=[];var e=TaUtils.getWidth(this.$refs["index-tabs"]),t=parseInt(TaUtils.getWidth(this.$refs["index-worktable"]))+80,n=this.tabList,i=[],s=[];this.$nextTick((function(){"worktable"!==this.activeTab&&(t+=TaUtils.getWidth(document.getElementById(this.activeTab+"-tab")));for(var a=0;a<n.length;a++)n[a].id!==this.activeTab?(t+=TaUtils.getWidth(document.getElementById(n[a].id+"-tab")),e>t?i.push(n[a]):s.push(n[a])):i.push(n[a]);this.list1=i.concat([]),this.list2=s.concat([])}))}},setTabsted:function(e){this.$store.dispatch("setStateValue",{activeTabMenu:e.id}),"worktable"===e.id&&(e.id="b93e89515da24041b9f1459f77de38fe"),this.$store.dispatch("setActiveMenu",{level:"two",menuId:e.id}),this.ifShowMore=!1},closeTabFnCheck:function(e){for(var t=this.state.closeTabCallBackModules,n=0;n<t.length;n++){var i,s;if((null===(i=t[n])||void 0===i?void 0:i.id)===e.id||(null===(s=t[n])||void 0===s?void 0:s.url)===e.url&&(!t[n].prefix||t[n].prefix===e.prefix)){var a=this.getTabMenuByUrl(e),o=a.prefix+a.module,r=document.getElementById(o).contentWindow.pageVmObj;return r.$bus.emit("closeTabCallBack",a),!1}}return!0},menuToModule:function(e){return e.url?{partId:e.id,module:e.url.split("#/")[0],part:e.url.split("#/")[1]||"",prefix:e.prefix||this.state.srcPrefix}:{module:"404.html",part:"",partId:"ta404",prefix:e.prefix||this.state.srcPrefix}},closeTabFn:function(e,t){var n=this.menuToModule(e);if(n.module!==this.getActiveIframe.module&&sendMessage(this.getActiveIframe.module,"loadCachedRouter"),sendMessage(n.module,"deleteCachedRouter",n.partId),!t&&!this.closeTabFnCheck(e))return!1;if(e.id===this.activeTab){var i=this.list1.findIndex((function(t){return t.id===e.id})),s="worktable";this.list1[i+1]&&(s=this.list1[i+1].id),this.list1[i-1]&&(s=this.list1[i-1].id),this.list2.length>0&&(s=this.list2[0].id),this.$store.dispatch("setStateValue",{activeTabMenu:s}),this.$store.dispatch("setActiveMenu",{level:"two",menuId:s})}this.$store.dispatch("deleteTabMenuList",{value:e.id}),this.ifShowMore=!1},closeAllTab:function(){this.$store.dispatch("setStateValue",{activeTabMenu:"worktable",tabMenuList:[],activeMenuTwo:""}),this.ifShowMore=!1},toWorktable:function(){this.$store.dispatch("setStateValue",{activeTabMenu:"worktable",tabMenuList:[],breadcrumb:[],activeMenuTwo:""})}},watch:{tabList:function(e,t){this.setTab()},activeTab:function(e,t){this.setTab()},collapsed:function(e){this.setTab()}}},xt=Zt,Ut=(0,Ye.Z)(xt,Ct,Lt,!1,null,"3172e288",null),Nt=Ut.exports,Et=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{ref:"iframeCon",staticClass:"iframeList"},[e.isPageTool?n("ta-page-tool",{attrs:{"is-move":!0,"tool-menu":e.toolMenu,"is-refresh":!1}}):e._e(),n("iframe",{style:e.worktableShow?"":"display:none",attrs:{id:e.worktable.id,src:e.worktable.src,frameborder:"none",scrolling:"auto"}})],1)},Bt=[],Dt=n(89584),Rt={name:"iframeList",data:function(){var e=this;return{src:"",CacheWebStorage:null,cacheTabListStorage:null,toolMenu:[{icon:"rocket",name:this.$t("refresh.refreshAll"),onClick:function(){e.CacheWebStorage.cleanData(),window.location.reload()}},{icon:"sync",name:this.$t("refresh.refreshPage"),onClick:function(){top.indexTool.reload()}},{icon:"question-circle",name:this.$t("refresh.help"),onClick:function(){sendMessage(e.activeTab.module,"fnPageGuide");var t=document.getElementById(e.activeTab.module).contentWindow.fnPageGuide;"function"!==typeof t&&e.$message.info("该页面暂无帮助导航")}}],iframeList:{},worktableShow:!0}},created:function(){this.CacheWebStorage=TaUtils.webStorage.createWebStorage("Ta$CacheStorage"),this.cacheTabListStorage=TaUtils.webStorage.createWebStorage("Ta$cacheTabListStorage")},computed:(0,i.Z)((0,i.Z)({},(0,l.Se)({tabList:"getTabMenuList",activeTab:"getActiveIframe",state:"getStateValue"})),{},{isPageTool:function(){var e=this.state.notPageTool;return-1===e.indexOf(this.activeTab.module)&&-1===e.indexOf(this.activeTab.partId)},worktable:function(){var e=this.state.worktable,t=(0,i.Z)((0,i.Z)({},e),{},{partId:"worktable"});return this.iframeList[this.createId(t)]=t,{id:this.createId(t),src:this.createSrc(t)}}}),methods:{createIframe:function(e){var t=document.createElement("iframe");t.setAttribute("frameBorder","none"),t.setAttribute("frameBorder","0"),t.setAttribute("scrolling","auto"),t.id=this.createId(e),t.style.width="100%",t.style.height="100%";var n=this.createSrc(e);return t.setAttribute("src",n),t},createSrc:function(e){var t="",n="";return t=this.createId(e)+"#/"+e.part,n="_modulePartId_="+e.partId,t.indexOf("?")>-1?t+="&"+n:t+="?"+n,e.params&&(t+="&"+e.params),t},createId:function(e){return e.prefix+e.module},hideIframe:function(e){if("worktable"!==e.id){var t=document.getElementById(this.createId(e));t&&(t.style.display="none")}else this.worktableShow=!1},showIframe:function(e){if("worktable"!==e.id){var t=document.getElementById(this.createId(e)),n=this.createSrc(e);t.setAttribute("src",n),t&&(t.style.display="block")}else this.worktableShow=!1},menuToModule:function(e){return e.url?{partId:e.id,module:e.url.split("#/")[0],part:e.url.split("#/")[1]||"",params:e.params||"",prefix:e.prefix||this.state.srcPrefix}:{module:"404.html",part:"",partId:"ta404",prefix:e.prefix||this.state.srcPrefix}},deleteIframe:function(e){var t=this;e.map((function(e){if(t.iframeList[e]&&"worktable"!==t.iframeList[e].partId){delete t.iframeList[e];var n=document.getElementById(e);n&&n.parentNode.removeChild(n)}}))}},watch:{activeTab:function(e,t){var n=this.createId(e);this.iframeList[n]?(this.showIframe(e),this.iframeList[n]=(0,i.Z)({},e),t.module!==e.module&&sendMessage(e.module,"loadCachedRouter")):(this.iframeList[n]=(0,i.Z)({},e),this.createIframe(e),this.$refs.iframeCon.appendChild(this.createIframe(e))),n===this.createId(t)||this.hideIframe(t)},tabList:function(e){var t=this,n=Object.keys(this.iframeList),i=[],s=[],a=e.map((function(e){var n=t.menuToModule(e);return i.push(n.part),s.push(n),t.createId(n)})),o=(0,Dt.Z)(new Set(a)),r=n.filter((function(e){return o.indexOf(e)<0}));this.deleteIframe(r);for(var l=function(e){var a=n[e];i.length>0&&t.iframeList[a]&&"worktable"!==t.iframeList[a].partId&&i.indexOf(t.iframeList[a].part)<0&&s.forEach((function(e){if(e.module===a){t.$set(t.iframeList,a,e);var n=document.getElementById(a);return n&&n.setAttribute("src",t.createSrc(e)),!1}}))},c=0;c<n.length;c++)l(c);this.cacheTabListStorage.set("Ta$cacheTabListStorage",i);for(var u=0;u<o.length;u++)sendMessage(o[u],"setIncludeTabList")}}},Pt=Rt,Ot=(0,Ye.Z)(Pt,Et,Bt,!1,null,"4830b29b",null),Ft=Ot.exports,_t=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("ta-drawer",{attrs:{visible:e.state.showUserInfo,"destroy-on-close":"",width:"300px","wrap-class-name":"user-menu"},on:{close:e.onClose}},[i("div",{staticClass:"user-info"},[i("div",{staticClass:"person-head"},[i("img",{attrs:{alt:e.userInfo.userName,src:e.userAvatar},on:{error:function(t){e.userAvatar=n(19514)}}})]),i("div",[i("div",{staticClass:"user-name"},[e._v(" "+e._s(e.userInfo.userName)+" ")]),i("div",{staticClass:"user-position"},[e._v(" "+e._s(e.userInfo.mainRoleName)+" ")])])]),i("div",{staticClass:"menu-item"},[e._v(" "+e._s(e.$t("user.signIn"))+" ")]),i("div",{directives:[{name:"show",rawName:"v-show",value:e.userInfo.orgList&&e.userInfo.orgList.length>1,expression:"userInfo.orgList && userInfo.orgList.length > 1"}],staticClass:"menu-item"},[e._v(" "+e._s(e.$t("user.currentOrg"))+" "),i("ta-select",{attrs:{"default-value":e.userInfo.currentOrg},on:{select:e.onCurrentOrgChange}},e._l(e.userInfo.orgList,(function(t,n){return i("ta-select-option",{key:t.orgId,attrs:{title:t.namePath}},[e._v(" "+e._s(t.namePath)+" ")])})),1)],1),e.sysInfo.openSocialLogin||e.sysInfo.openSmsLogin?i("div",{staticClass:"menu-item",class:{active:e.socialBinding},on:{click:function(t){e.socialBinding=!e.socialBinding,e.changePwd=!1}}},[e._v(" "+e._s(e.$t("user.accountAssociation"))+" ")]):e._e(),e.socialBinding?i("div",{staticClass:"user-panel"},[i("social-binding",{on:{close:function(t){e.socialBinding=!1}}})],1):e._e(),i("div",{staticClass:"menu-item",class:{active:e.changePwd},on:{click:function(t){e.changePwd=!e.changePwd,e.socialBinding=!1}}},[e._v(" "+e._s(e.$t("user.changePassword"))+" ")]),e.changePwd?i("div",{staticClass:"user-panel"},[i("img",{staticStyle:{width:"100%"},attrs:{src:n(68323)}}),i("modify-password",{on:{close:function(t){e.changePwd=!1}}})],1):e._e(),e.onlineTheme&&!e.isIE?i("div",{staticClass:"menu-item"},[e._v(" "+e._s(e.$t("user.changeTheme"))+" "),i("ul",{staticClass:"menu-color"},[e._l(e.colorList,(function(t,n,s){return i("li",{key:s,class:{active:n===e.colorDefault},style:{"background-color":t["primary-color"]},on:{click:function(t){return e.themeChange(n)}}})})),i("ta-tooltip",{staticStyle:{height:"20px",padding:"0",margin:"0",width:"20px"}},[i("template",{slot:"title"},[i("span",[e._v(e._s(e.$t("user.customColors")))])]),i("ta-popover",{staticStyle:{height:"20px",padding:"0",margin:"0",width:"20px"},attrs:{trigger:"click"}},[i("ta-color-picker",{on:{change:e.changeColor},model:{value:e.colorPicker,callback:function(t){e.colorPicker=t},expression:"colorPicker"}}),i("ta-button",{staticClass:"rainbow",staticStyle:{height:"20px",padding:"0",margin:"0",width:"20px"},attrs:{slot:"reference"},slot:"reference"})],1)],2)],2)]):e._e(),e.onlineTheme&&!e.isIE?i("div",{staticClass:"menu-item"},[e._v(" "+e._s(e.$t("user.darkMode"))+" "),i("ul",{staticClass:"menu-color"},[i("ta-switch",{attrs:{"default-checked":e.darkModeChecked},on:{change:e.changeDarkMode}})],1)]):e._e(),i("div",{staticClass:"menu-item"},[e._v(" "+e._s(e.$t("user.language"))+" "),i("ta-select",{attrs:{"default-value":e.defaultLocale},on:{select:e.onLocaleSelect}},e._l(e.localeList,(function(t,n){return i("ta-select-option",{key:n},[e._v(" "+e._s(t)+" ")])})),1)],1),i("div",{staticClass:"menu-item"},[e._v(" "+e._s(e.$t("user.navigationMode"))+" "),i("ul",{staticClass:"menu-mode"},e._l(e.modeList,(function(t,s){return i("li",{key:s,class:{active:t===e.state.menuType},style:{"background-image":"url("+n(8129)("./"+t+".png")+")"},attrs:{title:e.modeListCN[s]},on:{click:function(n){return e.modeChange(t)}}})})),0)]),i("div",{staticClass:"btn-logout"},[i("ta-button",{attrs:{block:""},on:{click:function(t){return e.logout()}}},[e._v(" "+e._s(e.$t("user.exit"))+" ")])],1)])},Vt=[],Gt=function(){var e=this,t=this,n=t.$createElement,i=t._self._c||n;return i("ta-form",{staticStyle:{padding:"10px"},attrs:{"auto-form-create":function(t){e.form=t},layout:"horizontal"}},[i("ta-form-item",{attrs:{label:t.$t("password.original"),"field-decorator-id":"oldPassword","field-decorator-options":{rules:[{required:!0,message:t.$t("password.originalPwdEmpty")}]}}},[i("ta-input",{attrs:{placeholder:t.$t("password.originalPwd"),type:"password"}})],1),i("ta-form-item",{attrs:{label:t.$t("password.new"),"field-decorator-id":"newPassword","field-decorator-options":{rules:[{required:!0,message:t.$t("password.newPwdEmpty")},{validator:t.compareToNextPassword}]}}},[i("ta-input",{attrs:{placeholder:t.$t("password.newPwd"),type:"password",onpaste:"return false",oncopy:"return false"}})],1),i("ta-form-item",{attrs:{label:t.$t("password.confirm"),"field-decorator-id":"confirm","field-decorator-options":{rules:[{required:!0,message:t.$t("password.confirmEmpty")},{validator:t.compareToFirstPassword}]}}},[i("ta-input",{attrs:{placeholder:t.$t("password.confirmPwd"),type:"password",onpaste:"return false",oncopy:"return false"},on:{blur:t.handleConfirmBlur}})],1),i("ta-form-item",{attrs:{"wrapper-col":{offset:5}}},[i("ta-button",{attrs:{type:"primary"},on:{click:t.closePane}},[t._v(" "+t._s(t.$t("cancel"))+" ")]),i("ta-button",{staticStyle:{"margin-left":"20px"},attrs:{type:"primary"},on:{click:t.handleSubmit}},[t._v(" "+t._s(t.$t("submit"))+" ")])],1)],1)},Wt=[],Ht=n(57456),jt={name:"modify-password",props:{},data:function(){return{confirmDirty:!1}},computed:(0,i.Z)((0,i.Z)({},(0,l.Se)({state:"getStateValue"})),{},{userInfo:function(){return this.state.userInfo}}),methods:{compareToNextPassword:function(e,t,n){var i=this.form;t&&this.confirmDirty&&i.validateFields(["confirm"],{force:!0});var s=(0,Ht.Z)(t);1==this.userInfo.passwordLevel?s!=this.userInfo.passwordLevel&&n("请输入6位数字"):s<this.userInfo.passwordLevel?n("请至少包含大写字母、小写字母、数字、特殊字符(除去空格)中的"+this.userInfo.passwordLevel+"种，且长度为8~20位"):n()},compareToFirstPassword:function(e,t,n){var i=this.form;t&&t!==i.getFieldValue("newPassword")?n(this.$t("password.different")):n()},handleConfirmBlur:function(e){var t=e.target.value;this.confirmDirty=this.confirmDirty||!!t},handleSubmit:function(){var e=this;this.form.validateFields((function(t,n){if(!t){var i=e.form.getFieldsValue();i.userId=e.userInfo.userId||"",e.userInfo.passwordRSA&&(i.oldPassword=Base.cryptoAsymmetricFn(i.oldPassword),i.newPassword=Base.cryptoAsymmetricFn(i.newPassword)),i.confirm&&delete i.confirm,e.Base.submit(null,{url:"indexRestService/changePassword",data:i},{successCallback:function(t){e.$message.success(e.$t("password.modified")),e.form.resetFields(),e.closePane()}})}}))},closePane:function(){this.$emit("close")}}},zt=jt,$t=(0,Ye.Z)(zt,Gt,Wt,!1,null,null,null),Yt=$t.exports,Qt=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[e.sysInfo.openSocialLogin?n("div",{staticClass:"social-binding"},[n("p",[e._v(e._s(e.$t("third.title")))]),n("div",{staticClass:"bind-list"},e._l(e.bindinglist,(function(t,i){return n("div",{key:i,staticClass:"bind-item"},[n("div",{staticClass:"bind-icon",class:t.isBinding?"color-"+t.providerId:""},[n("ta-icon",{attrs:{type:t.providerId}})],1),n("div",{style:{lineHeight:t.isBinding?"20px":"40px"}},[n("div",{staticClass:"bind-provider"},[e._v(" "+e._s(t.providerId)+" ")]),t.isBinding?n("div",{staticClass:"bind-name"},[e._v(" "+e._s(t.displayname)+" ")]):e._e()]),n("a",{class:{"color-unbind":!!t.isBinding},on:{click:function(n){return e.handleSocialBinding(t)}}},[e._v(e._s(t.isBinding?e.$t("third.unbind"):e.$t("third.bind")))])])})),0)]):e._e(),e.sysInfo.openSmsLogin?n("div",{staticClass:"phone-binding"},[n("p",[e._v(e._s(e.$t("third.phone")))]),e.phonebinded?e._e():n("div",[n("ta-tag",[e._v(e._s(e.$t("third.notBind")))]),n("ta-button",{on:{click:function(t){e.showPhoneBindingPane=!0}}},[e._v(" "+e._s(e.$t("third.bindPhone"))+" ")])],1),e.phonebinded?n("div",[n("ta-tag",[e._v(e._s(e.phone))]),n("ta-button",{on:{click:function(t){e.showPhoneDebindingPane=!0}}},[e._v(" "+e._s(e.$t("third.unbind"))+" ")])],1):e._e(),n("ta-modal",{attrs:{title:e.$t("third.bindPhone"),visible:e.showPhoneBindingPane,footer:null,width:"450px","body-style":{paddingBottom:"10px"}},on:{cancel:function(t){e.showPhoneBindingPane=!1}}},[n("phone-Binding",{attrs:{show:e.showPhoneBindingPane,"show-phone-binding-pane":e.showPhoneBindingPane,"pass-state":"1"},on:{"update:show":function(t){e.showPhoneBindingPane=t}}})],1),n("ta-modal",{attrs:{title:e.$t("third.unbindPhone"),visible:e.showPhoneDebindingPane,footer:null,width:"450px","body-style":{paddingBottom:"10px"}},on:{cancel:function(t){e.showPhoneDebindingPane=!1}}},[n("phone-debinding",{attrs:{show:e.showPhoneDebindingPane,phone:e.phone,"pass-state":"1"},on:{"update:show":function(t){e.showPhoneDebindingPane=t}}})],1)],1):e._e()])},Jt=[],Kt=function(){var e=this,t=this,n=t.$createElement,i=t._self._c||n;return i("div",[i("p",[t._v(t._s(t.$t("phone.note")))]),i("ta-form",{attrs:{"auto-form-create":function(t){e.form=t}}},[i("ta-form-item",{attrs:{label:t.$t("phone.pwd"),"field-decorator-id":"password",require:{message:t.$t("phone.pwdRequire")}}},[i("ta-input",{attrs:{disabled:t.lockInput,type:"password",placeholder:t.$t("phone.pwdPlh")}})],1),i("ta-form-item",{attrs:{label:t.$t("phone.phone"),"field-decorator-id":"mobile",require:{message:t.$t("phone.phoneRequire")}}},[i("ta-input",{attrs:{disabled:t.lockInput,type:"input",placeholder:t.$t("phone.phonePlh")}})],1),i("ta-form-item",{attrs:{label:t.$t("phone.code"),"field-decorator-id":"smsCode",require:{message:t.$t("phone.codeRequire")}}},[i("ta-input",{staticStyle:{width:"50%"},attrs:{type:"input",placeholder:t.$t("phone.codePlh")}}),i("ta-button",{staticStyle:{width:"50%","font-size":"12px",border:"none"},attrs:{disabled:t.time},on:{click:t.sendSms}},[t._v(" "+t._s(0==t.time?t.$t("phone.sendCode"):t.time+t.$t("phone.sendAfter"))+" ")])],1),i("ta-form-item",{attrs:{"wrapper-col":{span:18,offset:6}}},[i("ta-button",{attrs:{disabled:t.lockSubmitButton,type:"primary",block:!0},on:{click:t.doBinding}},[t._v(" "+t._s(t.$t("submit"))+" ")])],1)],1)],1)},Xt=[],qt=void 0,en={name:"phone-Binding",data:function(){return{time:!1,lockInput:!1,lockSubmitButton:!0}},mounted:function(){},computed:(0,i.Z)({},(0,l.Se)({passwordRSAState:"passwordRSAState"})),props:{showPhoneBindingPane:{type:Boolean}},methods:{authRequest:function(e,t){var n=qt.rowData.userId;$api.authRequestForOrgInfo((0,i.Z)((0,i.Z)({},e),{},{userId:n}),(function(n){t(n.orgInfo[e.inputKey])}))},doBinding:function(){var e=this,t=this.form.getFieldValue("mobile"),n=this.form.getFieldValue("smsCode");this.Base.submit(null,{url:"/phone/binding",data:{mobile:t,smsCode:n}},{successCallback:function(t){e.cancelThis();var n=e.$success({content:t.data.msg});setTimeout((function(){return n.destroy()}),2e3)}})},sendSms:function(){var e=this,t=this.form.getFieldValue("mobile"),n=this.form.getFieldValue("password");this.passwordRSAState&&(n=Base.cryptoAsymmetricFn(n)),this.Base.submit(null,{url:"/phone/sendSms",data:{mobile:t,password:n}},{successCallback:function(t){e.lockInput=!0,e.lockSubmitButton=!1;var n=t.data.canResendTime,i=setInterval((function(){0==n?(clearInterval(i),e.time=!1):e.time=n--}),1e3)},failCallback:function(e){}})},cancelThis:function(){this.form.resetFields(),this.$emit("update:show",!1)}}},tn=en,nn=(0,Ye.Z)(tn,Kt,Xt,!1,null,null,null),sn=nn.exports,an=function(){var e=this,t=this,n=t.$createElement,i=t._self._c||n;return i("div",[i("p",[t._v(t._s(t.$t("phone.note")))]),i("ta-form",{attrs:{"auto-form-create":function(t){e.form=t}}},[i("ta-form-item",{attrs:{label:t.$t("phone.pwd"),"field-decorator-id":"password",require:{message:t.$t("phone.pwdRequire")}}},[i("ta-input",{attrs:{disabled:t.lockInput,type:"password",placeholder:t.$t("phone.pwdPlh")}})],1),i("p",[t._v(" "+t._s(t.$t("phone.willSend"))+" "+t._s(t.phone)+" "+t._s(t.$t("phone.sendTo")))]),i("ta-form-item",{attrs:{label:t.$t("phone.code"),"field-decorator-id":"smsCode",require:{message:t.$t("phone.codeRequire")}}},[i("ta-input",{staticStyle:{width:"50%"},attrs:{type:"input",placeholder:t.$t("phone.codePlh")}}),i("ta-button",{staticStyle:{width:"50%","font-size":"12px",border:"none"},attrs:{disabled:t.time},on:{click:t.sendSms}},[t._v(" "+t._s(0==t.time?t.$t("phone.sendCode"):t.time+t.$t("phone.sendAfter"))+" ")])],1),i("ta-form-item",{attrs:{"wrapper-col":{span:18,offset:6}}},[i("ta-button",{attrs:{disabled:t.lockSubmitButton,type:"primary",block:"true"},on:{click:t.doDebinding}},[t._v(" "+t._s(t.$t("submit"))+" ")])],1)],1)],1)},on=[],rn={name:"phone-debinding",data:function(){return{time:!1,lockInput:!1,lockSubmitButton:!0}},computed:(0,i.Z)({},(0,l.Se)({passwordRSAState:"passwordRSAState"})),props:{phone:{type:String}},methods:{sendSms:function(){var e=this,t=this.phone,n=this.form.getFieldValue("password");this.passwordRSAState&&(n=Base.cryptoAsymmetricFn(n)),this.Base.submit(null,{url:"/phone/sendSms",data:{mobile:t,password:n}},{successCallback:function(t){e.lockInput=!0,e.lockSubmitButton=!1;var n=t.data.canResendTime,i=setInterval((function(){0==n?(clearInterval(i),e.time=!1):e.time=n--}),1e3)}})},doDebinding:function(){var e=this,t=this.phone,n=this.form.getFieldValue("smsCode");this.Base.submit(null,{url:"/phone/debinding",data:{mobile:t,smsCode:n}},{successCallback:function(t){e.cancelThis();var n=e.$success({content:t.data.msg});setTimeout((function(){return n.destroy()}),2e3)},failCallback:function(t){var n=e.$success({content:t.Error});setTimeout((function(){return n.destroy()}),2e3)}})},cancelThis:function(){this.form.resetFields(),this.$emit("update:show",!1)}}},ln=rn,cn=(0,Ye.Z)(ln,an,on,!1,null,null,null),un=cn.exports,dn={components:{PhoneDebinding:un,phoneBinding:sn},data:function(){return{bindinglist:[],phonebinded:!1,phone:"",showPhoneBindingPane:!1,showPhoneDebindingPane:!1}},mounted:function(){this.socialBindingQurey(),this.phonebindingQurey()},computed:{sysInfo:function(){return this.$store.state.indexStore.sysInfo}},watch:{showPhoneBindingPane:function(e,t){this.phonebindingQurey()},showPhoneDebindingPane:function(e,t){this.phonebindingQurey()}},methods:{socialBindingQurey:function(){var e=this;0!=this.sysInfo.openSocialLogin&&this.Base.submit(null,{url:"connect",method:"GET"},{successCallback:function(t){e.bindinglist=t.data.bindinglist}})},handleSocialBinding:function(e){var t=this,n=e.isBinding,i=e.providerId,s=e.providerUserId;n?this.Base.submit(null,{url:"connect/"+i+"/"+s,method:"DELETE"},{successCallback:function(e){var n=t.$success({content:e.data.msg});setTimeout((function(){return n.destroy()}),3e3),t.socialBindingQurey()}}):this.Base.submit(null,{url:"connect/"+i},{successCallback:function(e){window.open(e.redirectUrl),window.close()}})},phonebindingQurey:function(){var e=this;0!=this.sysInfo.openSmsLogin&&this.Base.submit(null,{url:"/phone/query"},{successCallback:function(t){var n=t.data.isBinded,i=t.data.phone;1==n?(e.phonebinded=!0,e.phone=i):(e.phonebinded=!1,e.phone="")}})}}},hn=dn,mn=(0,Ye.Z)(hn,Qt,Jt,!1,null,"a73a1e02",null),fn=mn.exports,pn=n(27651),gn=n.n(pn),vn={zh_CN:"简体中文(中国)",en_US:"English(US)"},bn=["left","top","leftTop","simple","workTable"],wn=["左","上","左上","极简","工作台"],yn={name:"UserMenu",components:{modifyPassword:Yt,socialBinding:fn},data:function(){var e,t,n,i,s;return this.themeStorage=TaUtils.webStorage.createWebStorage("index_theme",{isLocal:!0}),TaUtils.isIE()?(i=localStorage.dark_mode,s=localStorage.index_theme):(i=this.themeStorage.get("dark_mode"),s=this.themeStorage.get("index_theme")),{changePwd:!1,colorList:gn(),localeList:vn,defaultLocale:"zh_CN",modeList:bn,modeListCN:wn,themeStorage:null,modeStorage:null,localeStorage:null,socialBinding:!1,userAvatar:"",onlineTheme:o.Z.onlineTheme,colorDefault:null!==(e=s)&&void 0!==e?e:o.Z.defaultTheme,darkModeChecked:null!==(t=i)&&void 0!==t?t:o.Z.defaultDarkMode,colorPicker:0===(null===(n=s)||void 0===n?void 0:n.indexOf("#"))?s:"",isIE:(0,ae.Z)()}},created:function(){var e;this.themeStorage=TaUtils.webStorage.createWebStorage("index_theme",{isLocal:!0}),this.modeStorage=TaUtils.webStorage.createWebStorage("index_mode",{isLocal:!0}),this.localeStorage=se.Z.createWebStorage("locale_mode",{isLocal:!0}),this.defaultLocale=null!==(e=this.localeStorage.get("locale"))&&void 0!==e?e:o.Z.defaultLocale,this.userAvatar=this.userInfo.avatar?0===this.userInfo.avatar.length?n(61580)("./"+this.userInfo.userImg):this.userInfo.avatar:n(61580)("./"+this.userInfo.userImg)},computed:(0,i.Z)((0,i.Z)({},(0,l.Se)({state:"getStateValue"})),{},{userInfo:function(){return this.state.userInfo},sysInfo:function(){return this.$store.state.indexStore.sysInfo}}),methods:{onClose:function(){this.$store.dispatch("setStateValue",{showUserInfo:!1}),this.changePwd=!1},themeChange:function(e){this.changePwd=!1,this.colorDefault=e,TaUtils.isIE()?localStorage.index_theme=e:this.themeStorage.set("index_theme",e),this.changeTheme(e),this.$message.success(this.$t("user.changeThemeSuccess"))},changeColor:function(){this.themeChange(this.colorPicker)},changeDarkMode:function(e){this.changePwd=!1,this.darkModeChecked=e,TaUtils.isIE()?localStorage.dark_mode=e:this.themeStorage.set("dark_mode",e),this.updateColorWeak(e),this.$message.success(this.$t("user.changeThemeSuccess"))},onLocaleSelect:function(e){this.localeStorage.set("locale",e),window.location.reload()},modeChange:function(e){this.changePwd=!1,this.modeStorage||(this.modeStorage=TaUtils.webStorage.createWebStorage("index_mode",{isLocal:!0})),this.$store.dispatch("setStateValue",{workTableMenuVertical:!1}),this.$store.dispatch("setStateValue",{workTableMenuHorizon:!1}),this.modeStorage.set("mode",e),this.$store.dispatch("setStateValue",{menuType:e}),this.$message.success(this.$t("user.switchMode"))},onCurrentOrgChange:function(e){this.Base.submit(null,{url:"/indexRestService/changeCurrentOrg",data:{orgId:e},frontUrl:top.window.location.href}).then((function(e){window.location.reload()})).catch((function(e){}))},logout:function(){var e=this;this.Base.submit(null,{url:"/logout",frontUrl:top.window.location.href}).then((function(t){TaUtils.setCookie(o.Z.basePath+"TA-JTOKEN","",-1,"/"),TaUtils.setCookie(o.Z.basePath+"TA-RJTOKEN","",-1,"/"),TaUtils.setCookie("ALIPAYJSESSIONID","",-1,"/");var n=new Date;n.setTime(n.getTime());var i="; expires="+n.toGMTString();document.cookie="ALIPAYJSESSIONID="+i+"; domain=alipay.com; path=/","false"===e.userInfo.isSSO?window.location.href="login.html":top.window.location.href=t.redirectUrl||"login.html"})).catch((function(e){}))},rainbowPopover:function(){}}},An=yn,kn=(0,Ye.Z)(An,_t,Vt,!1,null,"f5e2b1d8",null),In=kn.exports,Sn=n(3336),Tn=function(e){function t(){return e.$store.state.indexStore.userInfo}function n(t){var n=e.$store.state.indexStore.authority,i=n.findIndex((function(e){return e.resourceId===t}));return!(!n[i]||"0"===n[i].authority)&&n[i]}function i(t){t.name||(t.name="new tab"),e.$store.dispatch("addTabMenuList",{value:t}),t.refresh&&d(t)}function s(t){"string"==typeof t?e.$refs.indexTab.closeTabFn({id:t}):"object"==(0,Sn.Z)(t)&&e.$refs.indexTab.closeTabFn({id:t.id},t.force)}function a(t){var n="";try{var i=[];o({children:e.$store.state.indexStore.menuList},i,t),i.length>0&&(n=i[0].id)}catch(s){}return n}function o(e,t,n){e.children&&e.children.forEach((function(e){o(e,t,n)})),(!e.children||e.children&&0===e.children.length)&&e.url&&e.url.indexOf(n)>=0&&t.push(e)}function r(){e.$store.dispatch("setStateValue",{showUserInfo:!1}),e.$refs.indexTab.ifShowMore=!1}function l(){var t="",n="";try{t=e.$store.state.indexStore.activeTabMenu;var i=e.$store.state.indexStore.tabMenuList.filter((function(e){return e.id===t}));i.length>0&&(n=i[0].url)}catch(s){}return n}function c(){var t="";try{var n=e.$store.state.indexStore.activeTabMenu,i=e.$store.state.indexStore.tabMenuList.filter((function(e){return e.id===n}));i.length>0&&(t=i[0].url)}catch(s){}return t}function u(){var t=[];try{t=t.concat(e.$store.state.indexStore.menuList)}catch(n){}return t}function d(t){try{var n="",i="",s="",a="";a=t?e.$store.getters.getTabMenuByUrl(t):e.$store.getters.getActiveIframe,n=a.prefix+a.module,s=a.part,i=a.partId;var o=document.getElementById(n).contentWindow.pageVmObj;o.$bus.emit("refresh",i,s)}catch(r){message.error("刷新不成功,请确认菜单是否正确")}}function h(){e.$refs.userMenu.logout()}function m(e){var t=(null===e||void 0===e?void 0:e.url)||"login.html";window.location.href=t}var f=function(){};f.prototype={registeredEventList:[]};var p=new f;return f.prototype.on=function(t,n){var i;("string"===typeof t&&p.registeredEventList.push(t),Array.isArray(t))&&(i=p.registeredEventList).push.apply(i,(0,Dt.Z)(t));e.$bus.on(t,n)},f.prototype.off=function(t,n){"string"===typeof t&&(p.registeredEventList=p.registeredEventList.filter((function(e){return e!==t}))),Array.isArray(t)&&(p.registeredEventList=p.registeredEventList.filter((function(e){return!t.includes(e)}))),e.$bus.off(t,n)},f.prototype.once=function(t,n){"string"===typeof t&&p.registeredEventList.push(t),e.$bus.once(t,(function(){p.registeredEventList=p.registeredEventList.filter((function(e){return e!==t}));for(var e=arguments.length,i=new Array(e),s=0;s<e;s++)i[s]=arguments[s];n.call.apply(n,[null].concat(i))}))},f.prototype.emit=function(t){for(var n,i=arguments.length,s=new Array(i>1?i-1:0),a=1;a<i;a++)s[a-1]=arguments[a];(n=e.$bus).emit.apply(n,[t].concat(s))},{getUserInfo:t,getMenuAuthority:n,openTabMenu:i,closeTabMenu:s,closeIndexPops:r,getActiveTabMenuId:l,getActiveTabMenuUrl:c,reload:d,getMenuList:u,logout:h,globalEvent:p,gotoLogin:m,getResourceIdByUrl:a}},Mn=Tn,Cn={name:"index",components:{UserMenu:In,IframeList:Ft,IndexTab:Nt,MenuVertical:ft,MenuHorizon:lt,indexLayout:nt,searchPane:yt,commonMenu:Mt},created:function(){var e=this;this.$store.dispatch("setUserInfo"),this.$store.dispatch("setSysInfo"),this.Base.submit(null,{url:"menu/menuAction/queryRootChildrenMenus"}).then((function(t){var n,i,s,a=[],o=[];a=(null===t||void 0===t||null===(n=t.data)||void 0===n||null===(i=n.menus)||void 0===i?void 0:i.children)||[],o=(null===t||void 0===t||null===(s=t.data)||void 0===s?void 0:s.commonMenuList)||[],e.$store.dispatch("loadMenuList",{menuList:a}),e.$store.dispatch("setStateValue",{commonMenuList:o})})).catch((function(){e.$store.dispatch("loadMenuList",{menuList:[]}),e.$store.dispatch("setStateValue",{commonMenuList:[]})})),this.$store.dispatch("setAuthority");var t=TaUtils.webStorage.createWebStorage("index_mode",{isLocal:!0});this.$store.dispatch("setStateValue",{menuType:t.get("mode")||this.indexStore.menuType})},computed:{indexStore:function(){return this.$store.state.indexStore},showLogoTitle:function(){var e=this.indexStore,t=e.menuType,n=e.collapsed;return"simple"===t||"workTable"===t||"top"===t||!n}},mounted:function(){history.pushState&&(history.pushState(null,null,document.URL),window.addEventListener("popstate",(function(){history.pushState(null,null,document.URL)}))),window.indexTool=Mn(this)}},Ln=Cn,Zn=(0,Ye.Z)(Ln,Le,Ze,!1,null,"7022cd45",null),xn=Zn.exports,Un=n(5190),Nn=n(71746),En=n(69872),Bn=n(18698),Dn=(n(47316),n(28743),n(28412),n(98761),n(76018));function Rn(e){var t={},s=n(74053);return e.keys().forEach((function(n){var a=n.match(/([A-Za-z0-9-_]+)\./i);if(a&&a.length>1){var o=a[1];s.keys().indexOf(n)>=0&&(t[o]=s(n)),t[o]=(0,i.Z)((0,i.Z)({},t[o]),e(n))}})),t}r["default"].use(Dn.Z);var Pn=function(e){var t,n=se.Z.createWebStorage("locale_mode",{isLocal:!0});return new Dn.Z({locale:null!==(t=n.get("locale"))&&void 0!==t?t:"zh_CN",fallbackLocale:"zh_CN",messages:Rn(e),silentTranslationWarn:!0})},On={created:function(){window.pageVmObj=this}},Fn=n(48534),_n=n(73056);function Vn(e){return Gn.apply(this,arguments)}function Gn(){return Gn=(0,Fn.Z)(regeneratorRuntime.mark((function e(t){var n,i;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(n=TaUtils.webStorage.getStorage("Ta$cacheCryptInfo","Ta$cacheCryptInfo",{isLocal:!0}),!0===t||null===n||"{}"===JSON.stringify(n)){e.next=3;break}return e.abrupt("return",!1);case 3:return i=function(){var e=(0,Fn.Z)(regeneratorRuntime.mark((function e(t){var i,s,a,o;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(s=null===t||void 0===t||null===(i=t.data)||void 0===i?void 0:i.cryptoInfo,a=s.randomKeyLength||16,s.randomKey=_n.Z.creat64Key(a),n=TaUtils.webStorage.createWebStorage("Ta$cacheCryptInfo",{isLocal:!0}),n.set("Ta$cacheCryptInfo",s),!((null===s||void 0===s?void 0:s.reqDataLevel)>=1&&(null===s||void 0===s?void 0:s.randomKeyLength)>=16)){e.next=9;break}return o=(0,X.K9)(s.asymmetricAlgo,s.randomKey),e.next=9,Base.submit(null,{url:"indexRestService/getToken",data:{key:o}}).then((function(e){var t;null!==e&&void 0!==e&&null!==(t=e.data)&&void 0!==t&&t.token&&TaUtils.setCookie("Client-ID",e.data.token,0,"/")}));case 9:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),e.next=6,Base.submit(null,{url:"indexRestService/getCryptoInfo",reqDataLevel:!1}).then(function(){var e=(0,Fn.Z)(regeneratorRuntime.mark((function e(t){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,i(t);case 2:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}());case 6:case"end":return e.stop()}}),e)}))),Gn.apply(this,arguments)}function Wn(e){Base.submit(null,{url:"indexRestService/defaultOpen"}).then(function(){var t=(0,Fn.Z)(regeneratorRuntime.mark((function t(n){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Vn();case 2:e();case 3:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())}r["default"].use(Un.Z),r["default"].use(Nn.Z),r["default"].use(En.Z),r["default"].use(Bn.Z),window.faceConfig=o.Z,r["default"].use(l.ZP),r["default"].use(Un.Z),r["default"].use(Nn.Z),r["default"].use(En.Z),r["default"].use(Bn.Z),(0,ae.Z)()||Promise.resolve().then(n.bind(n,30228)).then((function(e){var t=e.injectTheme;t(r["default"])}));var Hn=Pn(n(70962));n(17220),Wn((function(){var e=new l.ZP.Store({modules:(0,i.Z)({indexStore:h},m.Z)});new r["default"]({mixins:[f.d,On],store:e,render:function(e){return e(xn)},i18n:Hn}).$mount("#app")}))},17220:function(){var e,t,n=TaUtils.getCookie("TA-JTOKEN"),i=TaUtils.getCookie("TA-RJTOKEN");null!==n&&void 0!==n&&""!==n&&TaUtils.setCookie((null===(e=faceConfig)||void 0===e?void 0:e.basePath)+"TA-JTOKEN",n,0,"/");null!==i&&void 0!==n&&""!==i&&TaUtils.setCookie((null===(t=faceConfig)||void 0===t?void 0:t.basePath)+"TA-RJTOKEN",i,0,"/")},27651:function(e){var t={green:{"primary-color":"#1DA57A"},red:{"primary-color":"#F5222D"},orange:{"primary-color":"#FA541C"},purple:{"primary-color":"#722ED1"},default:{"primary-color":"#1890FF"},ybblue:{"primary-color":"#1B65B9"},dark:{"primary-color":"#13c2c2"}};e.exports=t},6442:function(e,t,n){"use strict";n.d(t,{MP:function(){return d},xz:function(){return u}});var i=n(89584),s=n(98878),a=n.n(s),o=n(14193),r=n(3032),l=n(60011);n(7638);r["default"].use(l.Z);var c={getAntdSerials:function(e){var t=new Array(9).fill().map((function(t,n){return a().varyColor.lighten(e,n/10)})),n=(0,o.R_)(e),i=a().varyColor.toNum3(e.replace("#","")).join(",");return t.concat(n).concat(i)},changeColor:function(e){var t=this,n=[];e.forEach((function(e){n.push.apply(n,(0,i.Z)(t.getAntdSerials(e)))}));var s={newColors:n,changeUrl:function(e){var t="/";return t="/hiiss-backend/template/",t.concat(e)}};return a().changer.changeColor(s)}};function u(e,t){var n;t&&(n=l.Z.loading(window.pageVmObj.$t("theme.changing"),0)),c.changeColor(e).then((function(){n&&n()}))}function d(e){var t=document.body;e?t.classList.add("colorWeak"):t.classList.remove("colorWeak")}},89580:function(e){e.exports={"primary-color":"#13c2c2","link-color":"#13c2c2"}},42896:function(e){e.exports={"primary-color":"#1890FF"}},23379:function(e){e.exports={"primary-color":"#006dd9","link-color":"#1B65B9","success-color":"#52c41a","warning-color":"#faad14","error-color":"#fe5c65","text-color":"rgba(0, 0, 0, 0.7)","heading-color":"rgba(0, 0, 0, 0.9)","input-color":"rgba(0, 0, 0, 0.9)","shadow-color":"rgba(0, 0, 0, 0.3)","border-color-base":"#b2b2b2","disabled-bg":"#ebebeb","disabled-color":"rgba(0, 0, 0, 0.5)","modal-mask-bg":"rgba(0, 0, 0, 0.75)","input-addon-bg":"#ebebeb","border-color-split":"#b2b2b2","background-color-light":"#ebebeb","item-hover-bg":"#d6e7f9","item-active-bg":"#d6e7f9","tabs-card-head-background":"#fafafa","pagination-item-active-bg":"#006dd9","pagination-item-active-color":"#fff","table-row-hover-bg":"#d6e7f9","text-color-secondary":"#606266","background-color-base":"#F0F2F5"}},62977:function(e){e.exports={"primary-color":"#1DA57A","link-color":"#1DA57A"}},97369:function(e,t,n){var i=n(10641)["default"];e.exports=function(e){var t=n(83574)("./".concat(e,".js"));return i({},t)}},77745:function(e){e.exports={"primary-color":"#FA541C","link-color":"#FA541C"}},93344:function(e){e.exports={"primary-color":"#722ED1","link-color":"#722ED1"}},21301:function(e){e.exports={"primary-color":"#F5222D","link-color":"#F5222D","success-color":"#52c41a"}},73492:function(e){e.exports={"primary-color":"#2364F8","link-color":"#2364F8","success-color":"#67C23A","warning-color":"#E6A23C","error-color":"#F56C6C","text-color":"#303133","text-color-secondary":"#606266","border-color-base":"#DCDFE6","border-color-split":"#EBEEF5","background-color-base":"#F0F2F5","background-color-light":"#F5F7FA","font-family":"Microsoft YaHei,Hiragino Sans GB,Pingfang SC,Arial,Helvetica Neue,Helvetica","font-variant-base":"tabular-nums","font-size-base":"14px","font-size-lg":"16px","font-size-sm":"12px"}},74053:function(e,t,n){var i={"./en_US.json":31613,"./zh_CN.json":58873};function s(e){var t=a(e);return n(t)}function a(e){if(!n.o(i,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return i[e]}s.keys=function(){return Object.keys(i)},s.resolve=a,e.exports=s,s.id=74053},61580:function(e,t,n){var i={"./left.png":25546,"./leftTop.png":57860,"./logo.png":10728,"./logo1.png":47568,"./menu-two-bgimg.png":85780,"./person-head.png":19514,"./simple.png":39903,"./top-part-bgimg.png":16401,"./top.png":37573,"./user-pw-bg.png":68323,"./workTable.png":78784};function s(e){var t=a(e);return n(t)}function a(e){if(!n.o(i,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return i[e]}s.keys=function(){return Object.keys(i)},s.resolve=a,e.exports=s,s.id=61580},8129:function(e,t,n){var i={"./left.png":25546,"./leftTop.png":57860,"./logo.png":10728,"./logo1.png":47568,"./menu-two-bgimg.png":85780,"./person-head.png":19514,"./simple.png":39903,"./top-part-bgimg.png":16401,"./top.png":37573,"./user-pw-bg.png":68323,"./workTable.png":78784};function s(e){var t=a(e);return n(t)}function a(e){if(!n.o(i,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return i[e]}s.keys=function(){return Object.keys(i)},s.resolve=a,e.exports=s,s.id=8129},70962:function(e,t,n){var i={"./en_US.json":5437,"./zh_CN.json":45230};function s(e){var t=a(e);return n(t)}function a(e){if(!n.o(i,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return i[e]}s.keys=function(){return Object.keys(i)},s.resolve=a,e.exports=s,s.id=70962},83574:function(e,t,n){var i={"./dark.js":89580,"./default.js":42896,"./example.js":23379,"./green.js":62977,"./index.js":97369,"./orange.js":77745,"./purple.js":93344,"./red.js":21301,"./ybblue.js":73492};function s(e){var t=a(e);return n(t)}function a(e){if(!n.o(i,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return i[e]}s.keys=function(){return Object.keys(i)},s.resolve=a,e.exports=s,s.id=83574},25546:function(e){"use strict";e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAnCAYAAABJ0cukAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTM4IDc5LjE1OTgyNCwgMjAxNi8wOS8xNC0wMTowOTowMSAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTcgKFdpbmRvd3MpIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOkY5MTg4OENFMUVFMDExRTlBMzBGRDg1MTRBMTFERkY1IiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOkY5MTg4OENGMUVFMDExRTlBMzBGRDg1MTRBMTFERkY1Ij4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6RjkxODg4Q0MxRUUwMTFFOUEzMEZEODUxNEExMURGRjUiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6RjkxODg4Q0QxRUUwMTFFOUEzMEZEODUxNEExMURGRjUiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz7Z+xRfAAAA+UlEQVR42uyZMQ6CMBSGW0Hj4iCJh/BAbMbR0c3E3Um9BQ5OHsNDuLoxoIiJGkJb34uPO/DM/ydf28Dyfw0srTWcyXRA456YEYmR3C5n08HciQOxSpKkjuXhjlgaHRlL1zex7tFgiblRlhDCgruzQCxWqmKt5c4xC0RGb6L2E9Ia+xcCqgMBCEAAAhCAAAQgAAEIQAACEIAABCAAAQioFii1Fffel61AMN6dtAkURXHk7j+Bx3VjfJPRuup6cedcled5lqbplrvzyXSfGAl8V9b10+pA1MST4duZhnjJy6GCH9sTH+nctLvNM8tEsrYd3n3GycaHrwADAIggOrdD0tpHAAAAAElFTkSuQmCC"},57860:function(e){"use strict";e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAnCAYAAABJ0cukAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyhpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTMyIDc5LjE1OTI4NCwgMjAxNi8wNC8xOS0xMzoxMzo0MCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTUuNSAoV2luZG93cykiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6ODNBNDBCQzZBQTA3MTFFOUJENEVEQzk4NjE0QjRDNkYiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6ODNBNDBCQzdBQTA3MTFFOUJENEVEQzk4NjE0QjRDNkYiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDo4M0E0MEJDNEFBMDcxMUU5QkQ0RURDOTg2MTRCNEM2RiIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDo4M0E0MEJDNUFBMDcxMUU5QkQ0RURDOTg2MTRCNEM2RiIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PvqZ1DYAAAEySURBVHja7JkxTsMwFIbtJlStUCuaqipX4ARcgZElYukNGJjKjjpViEu0AxM3QCyIASQGFiQGFrYMgZBKqKSxw3upO9LZT/p/6befnOX/np3F1oo1OGjSeEkekSPlsfa6na+T46P5xfnpeH84LIJ6dXdwReMZua081/K3aD+/vB6uVmXz8f7uVtOaph1Iae4pQeKdyN6f+g2qQ2nhWVm+4MwhAwRKroKwPkL/6PPtwev0URTpxjYAAaoBRAsAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQDZBJC26tzTYAlbLmRhpAmqbXnH0N8P0xUbacUZ37HtwYkydJMovjeMrZ+WZ6h9xx5rcy32+rK3JBXrD5faAk/7iPLQE/tiUvXeZy022eGSZwtfa4+2zjGl/9CTAAoBJLZQX5Y/EAAAAASUVORK5CYII="},10728:function(e){"use strict";e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB0AAAAaCAYAAABLlle3AAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAHaADAAQAAAABAAAAGgAAAABsW8pBAAADTUlEQVRIDaWWS0hVQRjHz70+ytJMBMPCMiLCikR6UJi4S1oI7qIIgmrRIugB1aKEC0EtEgoKWgU96EGLojY96EHhRu0F1a4HYYmmFRpmGWq//3jmOs29J+36wY/vMd8337kzc+aeIIiQkZGRfLgLmUg/Rdsipo4OU7Qnk25OTSd2RXQHb4TkhdDlTJCpeZHCmDd9qqskOJNpF6+uD782tYsXIWk19HjFk3FvUjzNazPmMlgA150O3djif+UbBbdgICyMPlQkbAqTpDqgEhbBbZiotJBYDXlwPyz6gJ419vNCi2AJvAuTpFptEnYhHIVBiBJtyT4oUh1aTdtAMgyn7HxGE9DhOQauNLtJDGTDVtDS+XKPQLWXr6bPnMQv2JXJHDnQ5STIPJ9McAzitaBf0Auf4BBMcVKMSWwG/ABXLuNk6enzyNoNJV7hgPXJ0aTxWCw2AI/w6/D14vfhv7R5np6KHw9jw+g2mA31Wntt+m/w5aAKCMahCXSYlinmC3FdmbvgHKiZ6vTq2Xm1pzqY26FUT1IO2eBLbxjQjaJV0K+7RtH6MG4Ufo3icAK0Z/YGMs3xJYqVwhboT9eMeKDl6JSB5MBcYwXBAvRVGjWhX8EK2AEzQfIRhowVBPPQWaEtpbFG+K6mzdABWm8ratodOnrKQjuAzocEaM91HlzpYY8Hw4BWR7WaSz32M9aCHhWefC+4ovexSqNo3VKf3cF/2EcY0/u+E96EaG496N9CUBM/ASs/McySoXUxyJ+ItJD0GnR16jKZbzthr4VV1jeawAawp+0rdoEG0BXwCyYib0nSSS83k47WZ+FvhgewxMaNJqB39g5IWsHsF7oG7MNgpoi24iEkoMydFF9LbW+6hDuWtElYDvrU0I1jmzZg+6KbRlfcAVgJ05OTYODnwEZ4AZLnYFbOzUvaDOowXAGdbE2gzxbt6Xt4DIehCnKTRY5BXA9+A4ZAopWod1JSTRLKoBHMHy9ae7oO9I5GCuOL4Szoa8GV0zjuRZF+DpKKwH2x0yaSkwta3pPQDr7oFOsCmbwwUTHUwSXQGYiS45Pqxqw6+mtAr8RTGE+0/3PSNdU1Na5QrL+2BlgKxaCl13WnK84XjelavcC11+4Pyv8D3YUrzLJvfuoAAAAASUVORK5CYII="},47568:function(e){"use strict";e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAD0AAAA9CAYAAAAeYmHpAAAACXBIWXMAAAsSAAALEgHS3X78AAAEnklEQVRoge1bT0gUURj/jOwUWMe5+AeMQgjdMvEQuF2CIGiFDtHAuAZF0cGV6FKQa0iHDrVeOkTE7sCgh8AVvHnIhZDASPNakUvSBK2oFP0B/8Sjb2Qc3sx8b95Tg9kfiLDu+977zffn/d73xprNzU2IG/bFjnGVdIxQJR0XVEnHBVXSccF+1Tw13UwCQHKHnl/etowFWSNKZaimm3kA6FFmkI9e2zLyMgaUkUYPv1RiLBirANAm43GVOS319AVQBwBZGQNKSGu6mQGABhW2iOjByIoEadKabh6SffIRkdsz0ki4ToEdUbRqupmOMlCqkGm62QYAs4pIRAErao22ZayIjJX1dOQQUwQWYRlRU5FJa7qZAoAuz8fvAKAff+8EygAwjB52MKDpZuOukPbxcsq2jBwqsnHFpAdxf85w5hbaLiOR1nQzy9uiHMHAcsy2jBQuVBYsahK2ZWQDcrdLZAsTJo1bFCmP2EIB4IwnHEUwaFsG8+4cYQzZ21E8nfPZorh5bFvGFAtLwTzf8q7AmAYUSaEQIo0h5Heg8N02MOzZ2ELIFCwi+gne9QvlLEZiIEQ9HVl5YZ4zMdHr85USFiqZbZCky8mkUf14tyg3KHkHeCxMIEnnh3k3KXByCvJmH4omX5CaCER9TVZFGLoyjYZWzmcl/N2FdcfXPtXTkU5RbGvTdLNIyTPO2CS1MCEasbNSw0gHzRmqvVHtzBEOFd22ZRQ9YxfwYbEClaRsPbjYnKtgNrnDHv++HGCigLXDFxRPp4inKF54O9HBxs+GeQ6l7YJ7h+DkeWC+UtpVlJwWDs0APMYik3GrK/QeK3AXFMwRKn8ppFnIDhC+ty10Aw4BzBNtLq83CgqeoMNFgaIWQ0mzPNR0czzMCxxdHLS4VmITkZcyfnYHqQpOpHr/L/CmW0lUspL2aVZMNN0cDAjzEuezsIJDwVbKoATOugTSKtYG4S6syA1HDj1O7YepKIArPmTZWnKibSIHZBmKE+x2mGcw9x3CBeyJcc/W808vvnh+/87pMKNCd1kslHw0+BTn6yrCu87lWd97rMpoOytuxfWN5h9X7j14FWY0ygVellh5ZcO77CLrG8aV0XYmaPLrG7W1M1+bWxIEw8KkWVNA083CDl7UjSPRYtCXKqPt2wTNWzsxci07VKZMEPWqNuuRp7Lh7Xi1SDleOt515v+9dnDp3K0nl6mTRSKNW1guRKmFVfkyqr08sQfm5G7eW1NK5Y4hnbTyf5C5lGek03iooG4dJSRK8qgDDOUM7yFXfmof9bsPhbotkUmz4oL6eczrKVc7toQCYyosR/1QGW1PB2hzmF48nj4maFP6Uv5839jrieHuTikjHCBZbn/dwYellsnOm+ZZUdvS75xMDHdfkrXhBoUsw9rGgV/z3+qvRnnaKl60kX7xBXM2LdKWevPlxDPqFuXFnr7vjdU4g4TJd9zf/xz+3NQzWR91XuWvVFGAIRzWUvbF9GLiUZPE/LvmaRQUKYGeGxeflo/OnLphdcisZcdJo1dTCrT4SnnlyPuT10duy66p+j8ccUGVdFxQJR0XVEnHBfEjDQB/Ac0tvKO9xJrdAAAAAElFTkSuQmCC"},85780:function(e,t,n){"use strict";e.exports=n.p+"img/menu-two-bgimg.5f5150e1.png"},19514:function(e){"use strict";e.exports="data:image/png;base64,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"},39903:function(e){"use strict";e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAnCAYAAABJ0cukAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAASpJREFUeNpiZAABUU02INkNxDFALMQwiIEAH+/78ACPxY1l2aUS4uK/mMGi3KK9QDIfiDkZBjn48fMX59mL18x///7DdvLwvj2MQDFGYAy8BdKCDEMIgGLiw91TwixANguy49/dPDpU/CAoJCTEwgRkMDMMXcDMBE5CQxcwDgsPDGkw6oFRD4x6YNQDox4Y9cCoB0Y9MOqBUQ+MemDUA6MeGPXAqAdGPTDqgVEPDGkPfBhqDv/3798HmAf+M/z7u3aoeeDt27fLQW6HeODjo2aGf38WAdmfBrvD//79++nly5eLQkNDO0FuB41MswIxLxSD5soG+2j1fyD+BcSfQRg0O/MHiL9BJTmGQMb+B8Q/oG7+AwttEA3yDDOUzTiIQx+E/0ID/j9AgAEAF+FIkITuF+QAAAAASUVORK5CYII="},16401:function(e,t,n){"use strict";e.exports=n.p+"img/top-part-bgimg.c426c015.png"},37573:function(e){"use strict";e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAnCAYAAABJ0cukAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTM4IDc5LjE1OTgyNCwgMjAxNi8wOS8xNC0wMTowOTowMSAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTcgKFdpbmRvd3MpIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOkY2RkM0RjhDMUVFMDExRTlCQTZCOUU3MDE2Qjk1NTcxIiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOkY2RkM0RjhEMUVFMDExRTlCQTZCOUU3MDE2Qjk1NTcxIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6RjZGQzRGOEExRUUwMTFFOUJBNkI5RTcwMTZCOTU1NzEiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6RjZGQzRGOEIxRUUwMTFFOUJBNkI5RTcwMTZCOTU1NzEiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz5y6MC7AAABDUlEQVR42uyZQW7CMBBFxwkgKGJBKg7BCThIlF3v0FVPwKpCXAIWrHoD7tEzRCUxohKqEEn6J3Lu4JH+SM+2svpvHG9sJ1qr9QTjDryBTOIuD47gQ36+H2n/ab7aY3wHM4m/NOMGTOR+OTssHHagwrwUW+WxA68JFiOD4SVkHqlAKnYr1V9oVtf13WL6LMtekv4M2C2XiPGiAAUoQAEKUIACFKAABShAAQpQgAIUoAAF7Aq0bXu1FnzIrAKd9/7LmkBVVSfN3gvkeb4ty/LQNM0t9uCaUbMWRfGp2fVmegwWAX0ri/22ugMP8Kvo68wTDO8DUwMHuwV/IfNz6LbOKpOGtYu4+0oTGt/9CzAAjmlFw5NDYkkAAAAASUVORK5CYII="},68323:function(e,t,n){"use strict";e.exports=n.p+"img/user-pw-bg.aeb705eb.png"},78784:function(e){"use strict";e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAnCAYAAABJ0cukAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAQVJREFUeNrsmUEKgkAUhuelRggtbNEVgq7QMcQTteoQbdp0kq4QdAQDNQokwplpXsyYiyA3oc/eD7/OwKD/92ZczAhaa+EE8+W780H56VC3Z4uV6GpsFEXg+qO24ZsP//aSX48tiqLOCjgDbcL3Ufp8BB8AgjzPKeYXmB2XkCfoykMAIAwAgwAgrUEAhITzh7yEGIABGIABGIABugYoeQYYgAEYgAH+FaDkGWAABmCAbqSUujgAnWXZlhqAybzH7C+AOI7XaZruKASXUl4xa5IkG8yOJ9OB8dR6LPp/Wo1/kx7GN7RvLlVjUzMh8F0o47vNXLlq4x1hPNuGHlcfLW3h9VOAAQD3fk+tT5xl/wAAAABJRU5ErkJggg=="},42480:function(){}},t={};function n(i){var s=t[i];if(void 0!==s)return s.exports;var a=t[i]={id:i,loaded:!1,exports:{}};return e[i].call(a.exports,a,a.exports,n),a.loaded=!0,a.exports}n.m=e,function(){n.amdO={}}(),function(){var e=[];n.O=function(t,i,s,a){if(!i){var o=1/0;for(u=0;u<e.length;u++){i=e[u][0],s=e[u][1],a=e[u][2];for(var r=!0,l=0;l<i.length;l++)(!1&a||o>=a)&&Object.keys(n.O).every((function(e){return n.O[e](i[l])}))?i.splice(l--,1):(r=!1,a<o&&(o=a));if(r){e.splice(u--,1);var c=s();void 0!==c&&(t=c)}}return t}a=a||0;for(var u=e.length;u>0&&e[u-1][2]>a;u--)e[u]=e[u-1];e[u]=[i,s,a]}}(),function(){n.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return n.d(t,{a:t}),t}}(),function(){var e,t=Object.getPrototypeOf?function(e){return Object.getPrototypeOf(e)}:function(e){return e.__proto__};n.t=function(i,s){if(1&s&&(i=this(i)),8&s)return i;if("object"===typeof i&&i){if(4&s&&i.__esModule)return i;if(16&s&&"function"===typeof i.then)return i}var a=Object.create(null);n.r(a);var o={};e=e||[null,t({}),t([]),t(t)];for(var r=2&s&&i;"object"==typeof r&&!~e.indexOf(r);r=t(r))Object.getOwnPropertyNames(r).forEach((function(e){o[e]=function(){return i[e]}}));return o["default"]=function(){return i},n.d(a,o),a}}(),function(){n.d=function(e,t){for(var i in t)n.o(t,i)&&!n.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}}(),function(){n.e=function(){return Promise.resolve()}}(),function(){n.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()}(),function(){n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)}}(),function(){n.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}}(),function(){n.nmd=function(e){return e.paths=[],e.children||(e.children=[]),e}}(),function(){n.j=4826}(),function(){n.p="/hiiss-backend/template/"}(),function(){var e={4826:0,6073:0,2601:0};n.O.j=function(t){return 0===e[t]};var t=function(t,i){var s,a,o=i[0],r=i[1],l=i[2],c=0;if(o.some((function(t){return 0!==e[t]}))){for(s in r)n.o(r,s)&&(n.m[s]=r[s]);if(l)var u=l(n)}for(t&&t(i);c<o.length;c++)a=o[c],n.o(e,a)&&e[a]&&e[a][0](),e[a]=0;return n.O(u)},i=self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[];i.forEach(t.bind(null,0)),i.push=t.bind(null,i.push.bind(i))}();var i=n.O(void 0,[3736,6716,807,8350,6258,5204,5956,4651,630,8155,5088,1803,856,6801,4381,910,3426,602],(function(){return n(83174)}));i=n.O(i)})();
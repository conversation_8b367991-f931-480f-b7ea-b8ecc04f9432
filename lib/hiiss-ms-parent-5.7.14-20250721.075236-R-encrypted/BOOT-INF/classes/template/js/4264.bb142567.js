"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[4264],{94264:function(t,e,a){a.r(e),a.d(e,{default:function(){return M}});var l=function(){var t=this,e=this,a=e.$createElement,l=e._self._c||a;return l("div",{staticStyle:{padding:"10px"}},[l("div",[e._v("规则更新用户确认")]),l("ta-row",[l("ta-col",{attrs:{span:8}},[l("div",{staticClass:"title"},[e._v("需求批次列表 "),l("ta-button",{staticStyle:{float:"right"},on:{click:e.doRefresh}},[e._v("刷新")])],1),l("ta-big-table",{ref:"historyTable",attrs:{data:e.historyList,size:"small","header-drag-style":"","use-virtual":"",border:"full",heightSwitch:!0,height:500,"row-height":20,"row-style":e.customRow},on:{"cell-click":e.cellClick}},[l("ta-big-table-column",{attrs:{type:"seq",width:"60",title:"序号",fixed:""}}),e._l(e.historyColumns,(function(t){return l("ta-big-table-column",{key:t.dataIndex,attrs:{align:t.align,resizable:"","show-header-overflow":"","show-overflow":t.overflowTooltip,field:t.dataIndex,title:t.title,fixed:t.fixed,width:t.width,collectionType:t.collectionType}})}))],2),l("ta-pagination",{ref:"historyPager",staticClass:"page",attrs:{dataSource:e.historyList,params:e.historyPageParam,url:"mtt/localruleconfig/ruleUserCommit/getDemandBatchList",defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"]},on:{"update:dataSource":function(t){e.historyList=t},"update:data-source":function(t){e.historyList=t}}})],1),l("ta-col",{staticStyle:{padding:"10px"},attrs:{span:16}},[l("div",{staticClass:"title"},[e._v("查询条件")]),l("ta-form",{attrs:{"auto-form-create":function(e){return t.searchForm=e},formLayout:!0,"label-width":"70px"}},[l("ta-form-item",{attrs:{fieldDecoratorId:"type",span:6,label:"需求类型"}},[l("ta-select",{attrs:{placeholder:"请选择","collection-type":"M_QEQUIRETYPE","allow-clear":!0,"show-search":!0}})],1),l("ta-form-item",{attrs:{fieldDecoratorId:"changelog",span:6,label:"变更说明"}},[l("ta-input",{attrs:{placeholder:"请输入"}})],1),l("ta-form-item",{attrs:{fieldDecoratorId:"checkStatus",span:6,label:"升级状态"}},[l("ta-select",{attrs:{placeholder:"请选择","collection-type":"M_CHECKSTATUS","allow-clear":!0,"show-search":!0}})],1),l("ta-form-item",{attrs:{fieldDecoratorId:"ykz018",span:6,label:"知识元"}},[l("ta-input",{attrs:{placeholder:"请输入"}})],1),l("ta-form-item",{attrs:{span:6,"field-decorator-id":"ape801",label:"规则大类"}},[l("ta-select",{attrs:{"show-search":!0,"allow-clear":!0,placeholder:"请选择"}},e._l(e.bigRuleList,(function(t,a){return l("ta-select-option",{key:a,attrs:{value:t.ape801}},[e._v(" "+e._s(t.aaa166)+" ")])})),1)],1),l("ta-form-item",{attrs:{fieldDecoratorId:"ykz227",span:6,label:"险种"}},[l("ta-select",{attrs:{placeholder:"请选择","collection-type":"MYKZ227","allow-clear":!0,"show-search":!0}})],1),l("ta-form-item",{attrs:{fieldDecoratorId:"ykz248",span:6,label:"就医方式"}},[l("ta-select",{attrs:{placeholder:"请选择","collection-type":"MYKZ248","allow-clear":!0,"show-search":!0}})],1),l("ta-form-item",{attrs:{span:6,label:"监控场景",fieldDecoratorId:"ykz109"}},[l("ta-select",{attrs:{placeholder:"请选择",allowClear:!0,"show-search":!0}},e._l(e.ykz109List,(function(t,a){return l("ta-select-option",{key:a,attrs:{value:t}},[e._v(" "+e._s(t)+" ")])})),1)],1),l("ta-form-item",{attrs:{span:6,label:"引擎号",fieldDecoratorId:"ykz108"}},[l("ta-select",{attrs:{placeholder:"请选择",allowClear:!0,"show-search":!0}},e._l(e.ykz108List,(function(t,a){return l("ta-select-option",{key:a,attrs:{value:t}},[e._v(" "+e._s(t)+" ")])})),1)],1),l("ta-form-item",[l("ta-button",{staticStyle:{"margin-left":"10px"},on:{click:e.resetFields}},[e._v("重置")]),l("ta-button",{attrs:{type:"primary"},on:{click:e.doSearch}},[e._v("查询")])],1)],1),l("ta-card",{attrs:{bordered:!1}},[l("div",{staticClass:"title"},[e._v("需求清单列表 "),e.clickRecord.firstAae043?["0"==e.clickRecord.checkStatus?l("ta-popconfirm",{attrs:{title:"确认应用？"},on:{confirm:e.applyFirstAll}},[l("ta-button",{staticStyle:{float:"right"},attrs:{type:"primary"}},[e._v("首次应用全量包")])],1):l("span",{staticStyle:{float:"right"}},[e._v("第一次全量包")])]:["0"==e.clickRecord.checkStatus?[l("ta-button",{staticStyle:{float:"right"},attrs:{type:"primary"},on:{click:e.openChangeDetail}},[e._v("需求确认")]),l("ta-popconfirm",{attrs:{title:"确认全部升级？"},on:{confirm:e.upgradeAll}},[l("ta-button",{staticStyle:{float:"right"},attrs:{type:"success"}},[e._v("全部升级")])],1)]:l("ta-button",{staticStyle:{float:"right"},attrs:{type:"primary"},on:{click:e.openChangeDetail}},[e._v("查看")])],l("div",{staticStyle:{clear:"both"}})],2),l("ta-big-table",{ref:"listTable",attrs:{data:e.listData,size:"small","header-drag-style":"","use-virtual":"",border:"full",heightSwitch:!0,height:386,"row-height":20}},[l("ta-big-table-column",{attrs:{type:"seq",width:"60",title:"序号",fixed:""}}),e._l(e.listColumns,(function(t){return l("ta-big-table-column",{key:t.dataIndex,attrs:{align:t.align,resizable:"","show-header-overflow":"","show-overflow":t.overflowTooltip,field:t.dataIndex,title:t.title,fixed:t.fixed,width:t.width,collectionType:t.collectionType}})}))],2),l("ta-pagination",{ref:"listPager",staticClass:"page",attrs:{dataSource:e.listData,params:e.listPageParam,url:"mtt/localruleconfig/ruleUserCommit/pageDemandDetailData",defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"]},on:{"update:dataSource":function(t){e.listData=t},"update:data-source":function(t){e.listData=t}}})],1)],1)],1),e.blnDetailVisible?l("div",{staticStyle:{width:"100%",height:"100%",position:"absolute",top:"0",left:"0","z-index":"10",background:"white"}},[l("ruleChangeDetail",{attrs:{"mtt-history":e.clickRecord,"check-status":e.checkStatus},on:{close:e.onSuccess,success:e.onSuccess}})],1):e._e()],1)},i=[],o=a(95082),r=function(){var t=this,e=this,a=e.$createElement,l=e._self._c||a;return l("div",{staticStyle:{padding:"10px"}},[l("div",[e._v("需求确认")]),l("div",{staticStyle:{"padding-left":"20px"}},[l("ta-button",{staticStyle:{"margin-top":"10px"},attrs:{type:"primary"},on:{click:e.goBack}},[e._v("返回列表")]),l("div",{staticStyle:{background:"#f2f2f2","margin-top":"20px","line-height":"70px","margin-bottom":"10px"}},[l("span",{staticStyle:{"margin-left":"5px"}},[e._v("需求批号: "+e._s(e.mttHistory.aae043))]),l("span",{staticStyle:{"margin-left":"20px"}},[e._v("需求日期: "+e._s(e.mttHistory.createDate))])]),l("ta-steps",{staticStyle:{background:"#f2f2f2"},attrs:{current:e.current,type:"navigation"}},e._l(e.steps,(function(t,a){return l("ta-step",{key:a},[l("template",{slot:"title"},[e._v(e._s(t))])],2)})),1),l("ta-card",{directives:[{name:"show",rawName:"v-show",value:!e.blnLocal&&0==e.current,expression:"!blnLocal && current == 0"}],attrs:{name:"规则批号信息列表",bordered:!1}},[l("div",{staticClass:"title"},[e._v("规则批号信息列表")]),l("ta-big-table",{ref:"historyTable",attrs:{data:e.ruleidCheckList,size:"small","header-drag-style":"","use-virtual":"",border:"full",heightSwitch:!0,height:386,"row-height":20,"auto-resize":""}},[l("ta-big-table-column",{attrs:{type:"seq",width:"60",title:"序号",fixed:""}}),e._l(e.ruleidCheckColumns,(function(t){return l("ta-big-table-column",{key:t.dataIndex,attrs:{align:t.align,resizable:"","show-header-overflow":"","show-overflow":t.overflowTooltip,field:t.dataIndex,title:t.title,fixed:t.fixed,width:t.width,collectionType:t.collectionType}})}))],2),l("ta-button",{staticStyle:{"margin-top":"10px",float:"right","margin-right":"10px"},attrs:{type:"primary"},on:{click:function(t){e.current+=1}}},[e._v("下一步")]),l("div",{staticStyle:{clear:"both"}})],1),l("ta-card",{directives:[{name:"show",rawName:"v-show",value:!e.blnLocal&&1==e.current,expression:"!blnLocal && current == 1"}],attrs:{name:"引擎批号信息",bordered:!1}},[l("ta-row",[l("ta-col",{staticStyle:{padding:"10px"},attrs:{span:8}},[l("div",{staticClass:"title"},[e._v("引擎批号信息")]),l("ta-input",{staticStyle:{width:"80%"},attrs:{placeholder:"规则批号或规则批号说明筛选"},on:{change:e.doRuleidMka44Filter},model:{value:e.ruleidMka44Search,callback:function(t){e.ruleidMka44Search=t},expression:"ruleidMka44Search"}}),l("div",[l("ta-big-table",{ref:"a",attrs:{data:e.ruleidMka44DisplayList,size:"small","header-drag-style":"","auto-resize":!0,border:"full",heightSwitch:!0,height:386,"row-height":20,"row-style":e.customRuleidMka44Row},on:{"cell-click":e.cellRuleidMka44Click}},[l("ta-big-table-column",{attrs:{type:"seq",width:"60",title:"序号",fixed:""}}),e._l(e.ruleidMka44Columns,(function(t){return l("ta-big-table-column",{key:t.dataIndex,attrs:{align:t.align,resizable:"","show-header-overflow":"","show-overflow":t.overflowTooltip,field:t.dataIndex,title:t.title,fixed:t.fixed,width:t.width,collectionType:t.collectionType},scopedSlots:e._u([t.scopedSlots?{key:"default",fn:function(a){var i=a.row;a.rowIndex;return["aae100"==t.scopedSlots.customRender?l("div",["1"==i.aae100?l("span",[e._v("启用")]):l("span",[e._v("停用")])]):e._e()]}}:null],null,!0)})}))],2)],1)],1),l("ta-col",{staticStyle:{padding:"10px"},attrs:{span:16}},[l("div",{staticClass:"title"},[e._v("规则大类列表")]),l("ta-big-table",{ref:"historyTable",attrs:{data:e.mka44List,size:"small","header-drag-style":"","auto-resize":!0,border:"full",heightSwitch:!0,height:386,"row-height":20}},[l("ta-big-table-column",{attrs:{type:"seq",width:"60",title:"序号",fixed:""}}),e._l(e.mka44Columns,(function(t){return l("ta-big-table-column",{key:t.dataIndex,attrs:{align:t.align,resizable:"","show-header-overflow":"","show-overflow":t.overflowTooltip,field:t.dataIndex,title:t.title,fixed:t.fixed,width:t.width,collectionType:t.collectionType}})}))],2)],1)],1),l("ta-button",{staticStyle:{"margin-top":"10px",float:"right","margin-right":"10px"},attrs:{type:"primary"},on:{click:function(t){e.current+=1}}},[e._v("下一步")]),l("ta-button",{staticStyle:{"margin-top":"10px",float:"right","margin-right":"10px"},attrs:{type:"primary"},on:{click:function(t){e.current-=1}}},[e._v("上一步")]),l("div",{staticStyle:{clear:"both"}})],1),l("ta-card",{directives:[{name:"show",rawName:"v-show",value:e.blnLocal&&0==e.current||!e.blnLocal&&"2"==e.current,expression:"blnLocal && current == 0 || !blnLocal && current == '2' "}],attrs:{name:"节点列表确认",bordered:!1}},[l("div",{staticClass:"title"},[e._v("查询条件")]),l("ta-form",{attrs:{autoFormCreate:function(e){t.nodeSearchFrom=e},formLayout:!0}},[l("ta-form-item",{attrs:{span:5,label:"节点类型",fieldDecoratorId:"ykz001"}},[l("ta-select",{attrs:{"allow-clear":!0,"show-search":!0,placeholder:"请选择"}},e._l(e.mkf01List,(function(t,a){return l("ta-select-option",{key:a,attrs:{value:t.ykz001}},[e._v(e._s(t.ykz002))])})),1)],1),l("ta-form-item",{attrs:{span:5,label:"需求类型",fieldDecoratorId:"changetype"}},[l("ta-select",{attrs:{"allow-clear":!0,"collection-type":"M_QEQUIRETYPE",placeholder:"请选择"}})],1),l("ta-form-item",{attrs:{span:5,label:"需求变更说明",fieldDecoratorId:"changelog"}},[l("ta-input",{attrs:{placeholder:"请输入"}})],1),l("ta-form-item",{attrs:{span:5,label:"升级状态",fieldDecoratorId:"checkStatus"}},[l("ta-select",{attrs:{"allow-clear":!0,"collection-type":"M_CHECKSTATUS",placeholder:"请选择"}})],1),l("ta-form-item",{attrs:{span:4,label:"节点编号",fieldDecoratorId:"ykz042"}},[l("ta-input",{attrs:{placeholder:"请输入"}})],1),l("ta-form-item",{attrs:{span:5,label:"节点名称",fieldDecoratorId:"ykz010"}},[l("ta-input",{attrs:{placeholder:"请输入"}})],1),l("ta-form-item",{attrs:{span:5,label:"节点说明",fieldDecoratorId:"ykz065"}},[l("ta-input",{attrs:{placeholder:"请输入"}})],1),l("ta-form-item",{attrs:{span:4}},[l("div",[l("ta-button",{staticStyle:{"margin-left":"20px"},on:{click:e.resetNodeSearch}},[e._v("重置")]),l("ta-button",{attrs:{type:"primary"},on:{click:e.doNodeSearch}},[e._v("查询")]),l("div",{staticStyle:{clear:"both"}})],1)])],1),l("div",{staticClass:"title"},[e._v("节点列表 "),"0"==e.checkStatus?l("ta-dropdown",[l("ta-menu",{attrs:{slot:"overlay"},slot:"overlay"},[l("ta-menu-item",[l("ta-popconfirm",{attrs:{title:"确认升级?","cancel-text":"取消","ok-text":"确认"},on:{confirm:function(t){return e.checkNodeBatch("1")}}},[l("ta-icon",{attrs:{type:"check-circle"}}),l("span",{staticClass:"mg-l12"},[e._v("升级")])],1)],1),l("ta-menu-divider"),l("ta-menu-item",[l("ta-popconfirm",{attrs:{title:"确认不升级?","cancel-text":"取消","ok-text":"确认"},on:{confirm:function(t){return e.checkNodeBatch("2")}}},[l("ta-icon",{attrs:{type:"stop"}}),l("span",{staticClass:"mg-l12"},[e._v("不升级")])],1)],1)],1),l("ta-button",{staticStyle:{float:"right","margin-right":"10px"}},[e._v(" 批量操作 "),l("ta-icon",{attrs:{type:"down"}})],1),l("div",{staticStyle:{clear:"both"}})],1):e._e()],1),l("ta-big-table",{ref:"ykz042Table",attrs:{data:e.ykz042List,size:"small","header-drag-style":"","auto-resize":!0,border:"full",heightSwitch:!0,height:386,"row-height":20}},[l("ta-big-table-column",{attrs:{type:"seq",width:"60",title:"序号",fixed:""}}),l("ta-big-table-column",{attrs:{type:"checkbox",width:"55"}}),e._l(e.ykz042Columns,(function(t){return l("ta-big-table-column",{key:t.dataIndex,attrs:{align:t.align,resizable:"","auto-resize":"","show-header-overflow":"","show-overflow":t.overflowTooltip,field:t.dataIndex,title:t.title,fixed:t.fixed,width:t.width,collectionType:t.collectionType},scopedSlots:e._u([t.scopedSlots?{key:"default",fn:function(a){var i=a.row;a.rowIndex;return["ykz010"==t.scopedSlots.customRender?l("div",[l("a",{on:{click:function(t){return e.openYkz010(i)}}},[e._v("节点名称:"+e._s(i.ykz010))])]):e._e(),"operate"==t.scopedSlots.customRender?l("div",["0"==e.checkStatus?[l("ta-popconfirm",{attrs:{title:"确认升级？"},on:{confirm:function(t){return e.doCheckNodeSingle(i,"1")}}},[l("a",[e._v("升级")])]),l("ta-popconfirm",{attrs:{title:"确认不升级？"},on:{confirm:function(t){return e.doCheckNodeSingle(i,"2")}}},[l("a",{staticStyle:{"margin-left":"10px"}},[e._v("不升级")])])]:l("span",[e._v("无")])],2):e._e()]}}:null],null,!0)})}))],2),l("ta-pagination",{ref:"ykz042Pager",staticClass:"page",attrs:{dataSource:e.ykz042List,params:e.ykz042PageParam,url:"mtt/localruleconfig/ruleUserCommit/queryPageNote",defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"]},on:{"update:dataSource":function(t){e.ykz042List=t},"update:data-source":function(t){e.ykz042List=t}}}),l("ta-button",{staticStyle:{"margin-top":"10px",float:"right","margin-right":"10px"},attrs:{type:"primary"},on:{click:function(t){e.current+=1}}},[e._v("下一步")]),e.blnLocal?e._e():l("ta-button",{staticStyle:{"margin-top":"10px",float:"right","margin-right":"10px"},attrs:{type:"primary"},on:{click:function(t){e.current-=1}}},[e._v("上一步")]),l("div",{staticStyle:{clear:"both"}})],1),l("ta-card",{directives:[{name:"show",rawName:"v-show",value:e.blnLocal&&1==e.current||!e.blnLocal&&"3"==e.current,expression:"blnLocal && current == 1 || !blnLocal && current == '3' "}],attrs:{name:"规则列表确认",bordered:!1}},[l("ta-row",[l("ta-col",{staticStyle:{padding:"10px"},attrs:{span:8}},[l("div",{staticClass:"title"},[e._v("引擎批号信息")]),l("ta-input",{staticStyle:{width:"80%"},attrs:{placeholder:"规则批号或规则批号说明筛选"},on:{change:e.doRuleidMkf74Filter},model:{value:e.ruleidMkf74Search,callback:function(t){e.ruleidMkf74Search=t},expression:"ruleidMkf74Search"}}),l("div",[l("ta-big-table",{ref:"a",attrs:{data:e.ruleidMkf74DisplayList,size:"small","header-drag-style":"","auto-resize":!0,border:"full",heightSwitch:!0,height:386,"row-height":20,"row-style":e.customRuleidMkf74Row},on:{"cell-click":e.cellRuleidMkf74Click}},[l("ta-big-table-column",{attrs:{type:"seq",width:"60",title:"序号",fixed:""}}),e._l(e.ruleidMka44Columns,(function(t){return l("ta-big-table-column",{key:t.dataIndex,attrs:{align:t.align,resizable:"","show-header-overflow":"","show-overflow":t.overflowTooltip,field:t.dataIndex,title:t.title,fixed:t.fixed,width:t.width,collectionType:t.collectionType},scopedSlots:e._u([t.scopedSlots?{key:"default",fn:function(a){var i=a.row;a.rowIndex;return["aae100"==t.scopedSlots.customRender?l("div",["1"==i.aae100?l("span",[e._v("启用")]):l("span",[e._v("停用")])]):e._e()]}}:null],null,!0)})}))],2)],1)],1),l("ta-col",{staticStyle:{padding:"10px"},attrs:{span:16}},[l("ta-form",{attrs:{autoFormCreate:function(e){t.ruleSearchForm=e},formLayout:!0,"label-width":"90px"}},[l("ta-form-item",{attrs:{span:6,label:"需求类型",fieldDecoratorId:"changetype"}},[l("ta-select",{attrs:{"allow-clear":!0,"collection-type":"M_QEQUIRETYPE",placeholder:"请选择"}})],1),l("ta-form-item",{attrs:{span:6,label:"需求变更说明",fieldDecoratorId:"changelog"}},[l("ta-input",{attrs:{placeholder:"请输入"}})],1),l("ta-form-item",{attrs:{span:6,label:"变更类型",fieldDecoratorId:"changetypeMulti"}},[l("ta-select",{attrs:{"allow-clear":!0,"collection-type":"MYKZ137",placeholder:"请选择"}})],1),l("ta-form-item",{attrs:{span:6,label:"升级状态",fieldDecoratorId:"checkStatus"}},[l("ta-select",{attrs:{"allow-clear":!0,"collection-type":"M_CHECKSTATUS",placeholder:"请选择"}})],1),l("ta-form-item",{attrs:{span:6,label:"规则大类",fieldDecoratorId:"ape801"}},[l("ta-select",{attrs:{"show-search":!0,"allow-clear":!0,placeholder:"请选择"}},e._l(e.bigRuleList,(function(t,a){return l("ta-select-option",{key:a,attrs:{value:t.ape801}},[e._v(" "+e._s(t.aaa166)+" ")])})),1)],1),l("ta-form-item",{attrs:{span:6,label:"规则ID",fieldDecoratorId:"ykz277"}},[l("ta-input",{attrs:{placeholder:"请输入"}})],1),l("ta-form-item",{attrs:{span:6,label:"知识元",fieldDecoratorId:"ykz018"}},[l("ta-input",{attrs:{placeholder:"请输入"}})],1),l("ta-form-item",{attrs:{span:6}},[l("div",[l("ta-button",{staticStyle:{"margin-left":"20px"},on:{click:e.resetRuleFields}},[e._v("重置")]),l("ta-button",{attrs:{type:"primary"},on:{click:e.doMkf74Search}},[e._v("查询")]),l("div",{staticStyle:{clear:"both"}})],1)])],1),l("div",{staticClass:"title"},[e._v("规则列表 "),"0"==e.checkStatus?l("ta-dropdown",[l("ta-menu",{attrs:{slot:"overlay"},slot:"overlay"},[l("ta-menu-item",[l("ta-popconfirm",{attrs:{title:"确认升级?","cancel-text":"取消","ok-text":"确认"},on:{confirm:function(t){return e.checkRuleBatch("1")}}},[l("ta-icon",{attrs:{type:"check-circle"}}),l("span",{staticClass:"mg-l12"},[e._v("升级")])],1)],1),l("ta-menu-divider"),l("ta-menu-item",[l("ta-popconfirm",{attrs:{title:"确认不升级?","cancel-text":"取消","ok-text":"确认"},on:{confirm:function(t){return e.checkRuleBatch("2")}}},[l("ta-icon",{attrs:{type:"stop"}}),l("span",{staticClass:"mg-l12"},[e._v("不升级")])],1)],1)],1),l("ta-button",{staticStyle:{float:"right","margin-right":"10px"}},[e._v(" 批量操作 "),l("ta-icon",{attrs:{type:"down"}})],1)],1):e._e(),l("div",{staticStyle:{clear:"both"}})],1),l("ta-big-table",{ref:"mkf74Table",attrs:{data:e.ruleList,size:"small","header-drag-style":"","auto-resize":!0,border:"full",heightSwitch:!0,height:386,"row-height":20}},[l("ta-big-table-column",{attrs:{type:"seq",width:"60",title:"序号",fixed:""}}),l("ta-big-table-column",{attrs:{type:"checkbox",width:"55"}}),e._l(e.ruleColumns,(function(t){return l("ta-big-table-column",{key:t.dataIndex,attrs:{align:t.align,resizable:"","show-header-overflow":"","show-overflow":t.overflowTooltip,field:t.dataIndex,title:t.title,fixed:t.fixed,width:t.width,collectionType:t.collectionType},scopedSlots:e._u([t.scopedSlots?{key:"default",fn:function(a){var i=a.row;a.rowIndex;return["operate"==t.scopedSlots.customRender&&"0"==e.checkStatus?l("div",[l("ta-popconfirm",{attrs:{title:"确认升级？"},on:{confirm:function(t){return e.doCheckRuleSingle(i,"1")}}},[l("a",[e._v("升级")])]),l("ta-popconfirm",{attrs:{title:"确认不升级？"},on:{confirm:function(t){return e.doCheckRuleSingle(i,"2")}}},[l("a",{staticStyle:{"margin-left":"10px"}},[e._v("不升级")])])],1):l("div",[l("a",{on:{click:function(t){return e.openItemDetail(i)}}},[e._v("查看")])])]}}:null],null,!0)})}))],2),l("ta-pagination",{ref:"mkf74Pager",staticClass:"page",attrs:{dataSource:e.ruleList,params:e.mkf74PageParam,url:"mtt/localruleconfig/ruleUserCommit/queryRuleDemandPage",defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"]},on:{"update:dataSource":function(t){e.ruleList=t},"update:data-source":function(t){e.ruleList=t}}})],1)],1),"0"==e.checkStatus?l("ta-popconfirm",{attrs:{title:"确认提交？"},on:{confirm:e.doSubmit}},[l("ta-button",{staticStyle:{"margin-top":"10px",float:"right","margin-right":"50px"},attrs:{type:"primary"}},[e._v("提交")])],1):e._e(),l("ta-button",{staticStyle:{"margin-top":"10px",float:"right","margin-right":"10px"},attrs:{type:"primary"},on:{click:function(t){e.current-=1}}},[e._v("上一步")]),l("div",{staticStyle:{clear:"both"}})],1),l("ta-card",{directives:[{name:"show",rawName:"v-show",value:e.blnLocal&&2==e.current||!e.blnLocal&&"4"==e.current,expression:"blnLocal && current == 2 || !blnLocal && current == '4' "}],attrs:{name:"完成",bordered:!1}},[l("div",{staticStyle:{display:"flex","justify-content":"center","margin-top":"50px","font-size":"50px","line-height":"50px"}},[l("ta-icon",{staticStyle:{color:"mediumseagreen"},attrs:{type:"check-circle"}})],1),l("div",{staticStyle:{display:"flex","justify-content":"center","margin-top":"50px","font-size":"40px"}},[e._v(" 需求确认完成 ")]),l("div",{staticStyle:{color:"mediumseagreen",display:"flex","justify-content":"center","margin-top":"50px"}},[l("a",{on:{click:e.closeThisPage}},[e._v("返回规则更新用户确认列表")])])])],1),l("ta-modal",{attrs:{title:"节点内容",width:1400,height:900,"destroy-on-close":!0,footer:null},model:{value:e.blnMkeVisible,callback:function(t){e.blnMkeVisible=t},expression:"blnMkeVisible"}},[l("node-detail",{attrs:{nodeInfo:e.nodeInfo,"check-status":e.checkStatus}})],1),l("ta-modal",{attrs:{title:"规则条目详情",width:1400,height:900,"destroy-on-close":!0,footer:null},model:{value:e.blnItemVisible,callback:function(t){e.blnItemVisible=t},expression:"blnItemVisible"}},[l("item-detail",{attrs:{ruleInfo:e.ruleInfo,"check-status":e.checkStatus}})],1),l("ta-modal",{attrs:{title:"校验问题",width:1400,height:900,"destroy-on-close":!0,footer:null},model:{value:e.blnErrorVisible,callback:function(t){e.blnErrorVisible=t},expression:"blnErrorVisible"}},e._l(e.errorList,(function(t,a){return l("div",{key:a},[e._v(e._s(t))])})),0)],1)},n=[],s=function(){var t=this,e=this,a=e.$createElement,l=e._self._c||a;return l("div",[l("div",{staticStyle:{background:"#f2f2f2","min-height":"70px","margin-bottom":"10px","padding-top":"10px"}},[l("span",{staticStyle:{"margin-left":"5px"}},[e._v("需求类型: "+e._s(e.CollectionLabel("M_QEQUIRETYPE",e.nodeInfo.changetype)))]),l("span",{staticStyle:{"margin-left":"20px"}},[e._v("需求说明: "+e._s(e.nodeInfo.changelog))]),l("span",{staticStyle:{"margin-left":"20px"}},[e._v("节点名称: "+e._s(e.nodeInfo.ykz010))]),l("span",{staticStyle:{"margin-left":"20px"}},[e._v("节点编号: "+e._s(e.nodeInfo.ykz042))]),l("span",{staticStyle:{"margin-left":"20px"}},[e._v("节点说明: "+e._s(e.nodeInfo.ykz065))])]),l("div",{staticClass:"title"},[e._v("查询条件")]),l("ta-form",{attrs:{"auto-form-create":function(e){return t.searchForm=e},formLayout:!0,"label-width":"70px"}},[l("ta-form-item",{attrs:{fieldDecoratorId:"changetype",span:6,label:"需求类型"}},[l("ta-select",{attrs:{placeholder:"请选择","allow-clear":!0,"show-search":!0,"collection-type":"M_ITEMCHANGETYPE"}})],1),l("ta-form-item",{attrs:{fieldDecoratorId:"changelog",span:6,label:"变更说明"}},[l("ta-input",{attrs:{placeholder:"请输入"}})],1),l("ta-form-item",{attrs:{fieldDecoratorId:"checkStatus",span:6,label:"升级状态"}},[l("ta-select",{attrs:{placeholder:"请选择","collection-type":"M_CHECKSTATUS","collection-filter":"3","allow-clear":!0,"show-search":!0}})],1),e._l(e.conditionColumns,(function(t,e){return l("ta-form-item",{key:e,attrs:{fieldDecoratorId:t.field,span:6,label:t.label}},[l("ta-input",{attrs:{placeholder:"请输入"}})],1)})),l("ta-form-item",[l("ta-button",{staticStyle:{"margin-left":"10px"},on:{click:e.resetFields}},[e._v("重置")]),l("ta-button",{attrs:{type:"primary"},on:{click:e.doSearch}},[e._v("查询")])],1)],2),l("div",["0"==e.checkStatus?l("ta-dropdown",[l("ta-menu",{attrs:{slot:"overlay"},slot:"overlay"},[l("ta-menu-item",[l("ta-popconfirm",{attrs:{title:"确认升级?","cancel-text":"取消","ok-text":"确认"},on:{confirm:function(t){return e.checkNodeBatch("1")}}},[l("ta-icon",{attrs:{type:"check-circle"}}),l("span",{staticClass:"mg-l12"},[e._v("升级")])],1)],1),l("ta-menu-divider"),l("ta-menu-item",[l("ta-popconfirm",{attrs:{title:"确认不升级?","cancel-text":"取消","ok-text":"确认"},on:{confirm:function(t){return e.checkNodeBatch("2")}}},[l("ta-icon",{attrs:{type:"stop"}}),l("span",{staticClass:"mg-l12"},[e._v("不升级")])],1)],1)],1),l("ta-button",{staticStyle:{float:"right","margin-right":"10px"}},[e._v(" 批量操作 "),l("ta-icon",{attrs:{type:"down"}})],1),l("div",{staticStyle:{clear:"both"}})],1):e._e(),l("div",{staticStyle:{clear:"both"}}),l("ta-big-table",{ref:"table",attrs:{data:e.dataList,size:"small","header-drag-style":"","auto-resize":!0,border:"full",heightSwitch:!0,height:386,"row-height":20}},[l("ta-big-table-column",{attrs:{type:"seq",width:"60",title:"序号",fixed:""}}),l("ta-big-table-column",{attrs:{type:"checkbox",width:"55"}}),e._l(e.columns,(function(t){return l("ta-big-table-column",{key:t.dataIndex,attrs:{align:t.align,resizable:"","show-header-overflow":"","show-overflow":t.overflowTooltip,field:t.dataIndex,title:t.title,fixed:t.fixed,width:t.width,collectionType:t.collectionType},scopedSlots:e._u([t.scopedSlots?{key:"default",fn:function(a){var i=a.row;a.rowIndex;return["operate"==t.scopedSlots.customRender?l("div",[l("ta-popconfirm",{attrs:{title:"确认升级？"},on:{confirm:function(t){return e.doCheckNodeSingle(i,"1")}}},[l("a",[e._v("升级")])]),l("ta-popconfirm",{attrs:{title:"确认不升级？"},on:{confirm:function(t){return e.doCheckNodeSingle(i,"2")}}},[l("a",{staticStyle:{"margin-left":"10px"}},[e._v("不升级")])])],1):e._e()]}}:null],null,!0)})}))],2),l("ta-pagination",{ref:"pager",staticClass:"page",attrs:{dataSource:e.dataList,params:e.pageParam,url:"mtt/localruleconfig/ruleUserCommit/queryNodeContentPage",defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"]},on:{"update:dataSource":function(t){e.dataList=t},"update:data-source":function(t){e.dataList=t},loaded:e.onDataLoad}})],1)],1)},c=[],u=a(66347),d={name:"NodeCheckDetail",props:{nodeInfo:Object,checkStatus:String},data:function(){return{conditionColumns:[],dataList:[],columns:[{dataIndex:"changetype",title:"需求类型",collectionType:"M_ITEMCHANGETYPE",align:"center",overflowTooltip:!0},{dataIndex:"changelog",title:"需求变更说明",align:"center",overflowTooltip:!0},{dataIndex:"checkStatus",title:"升级状态",collectionType:"M_CHECKSTATUS",align:"center",overflowTooltip:!0}]}},mounted:function(){this.queryConditionColumns(),this.doSearch()},methods:{queryConditionColumns:function(){var t=this;Base.submit(null,{url:"mtt/localruleconfig/ruleUserCommit/queryConditionColmumns",data:{ykz001:this.nodeInfo.ykz001}}).then((function(e){t.conditionColumns=e.data.list.filter((function(t){return"ykz042"!=t.field})),t.setColumns()}))},setColumns:function(){var t,e=(0,u.Z)(this.conditionColumns);try{for(e.s();!(t=e.n()).done;){var a=t.value;this.columns.push({dataIndex:a.field,title:a.label,align:"center",overflowTooltip:!0})}}catch(l){e.e(l)}finally{e.f()}"0"==this.checkStatus&&this.columns.push({dataIndex:"operate",title:"操作",scopedSlots:{customRender:"operate"},align:"center",overflowTooltip:!0})},resetFields:function(){this.searchForm.resetFields()},doSearch:function(){this.$refs.pager.loadData()},onDataLoad:function(t){if(t&&t.data&&t.data.pageBean){var e,a=(0,u.Z)(t.data.pageBean.list);try{for(a.s();!(e=a.n()).done;){var l=e.value,i=JSON.parse(l.coredata);Object.assign(l,i)}}catch(o){a.e(o)}finally{a.f()}}},doSearchCurrentPage:function(){var t=this,e=this.$refs.pager.getPagerInfo(),a=this.searchForm.getFieldsValue();Base.submit(null,{url:"mtt/localruleconfig/ruleUserCommit/queryNodeContentPage",data:(0,o.Z)((0,o.Z)({aae043:this.nodeInfo.aae043,ykz042:this.nodeInfo.ykz042,ykz001:this.nodeInfo.ykz001},a),{},{pageNumber:e.current,pageSize:e.pageSize})}).then((function(e){t.dataList=e.data.pageBean.list;var a,l=(0,u.Z)(e.data.pageBean.list);try{for(l.s();!(a=l.n()).done;){var i=a.value,o=JSON.parse(i.coredata);Object.assign(i,o)}}catch(r){l.e(r)}finally{l.f()}}))},pageParam:function(){var t=this.searchForm.getFieldsValue();return(0,o.Z)({aae043:this.nodeInfo.aae043,ykz042:this.nodeInfo.ykz042,ykz001:this.nodeInfo.ykz001},t)},doCheckNodeSingle:function(t,e){var a=this;Base.submit(null,{url:"mtt/localruleconfig/ruleUserCommit/checkNodeContentBatch",data:{aae043:this.nodeInfo.aae043,ykz042:this.nodeInfo.ykz042,checkStatus:e,ids:[t.id]},autoQs:!1}).then((function(t){a.$message.success("操作成功"),a.doSearchCurrentPage()}))},checkNodeBatch:function(t){var e=this,a=this.$refs.table.getCheckboxRecords();0!=a.length?Base.submit(null,{url:"mtt/localruleconfig/ruleUserCommit/checkNodeContentBatch",data:{aae043:this.nodeInfo.aae043,ykz042:this.nodeInfo.ykz042,checkStatus:t,ids:a.map((function(t){return t.id}))},autoQs:!1}).then((function(t){e.$message.success("操作成功"),e.doSearchCurrentPage()})):this.$message.info("请至少选择一行进行批量操作")}}},h=d,f=a(1001),p=(0,f.Z)(h,s,c,!1,null,"f3cd2020",null),m=p.exports,g=function(){var t=this,e=this,a=e.$createElement,l=e._self._c||a;return l("div",[l("div",{staticStyle:{background:"#f2f2f2","min-height":"70px","margin-bottom":"10px","padding-top":"10px"}},[l("span",{staticStyle:{"margin-left":"5px"}},[e._v("需求类型: "+e._s(e.CollectionLabel("M_QEQUIRETYPE",e.ruleInfo.changetype)))]),l("span",{staticStyle:{"margin-left":"20px"}},[e._v("需求说明: "+e._s(e.ruleInfo.changelog))]),l("span",{staticStyle:{"margin-left":"20px"}},[e._v("变更类型: "+e._s(e.ruleInfo.changetypeMulti))]),l("span",{staticStyle:{"margin-left":"20px"}},[e._v("规则id: "+e._s(e.ruleInfo.ykz277))]),l("span",{staticStyle:{"margin-left":"20px"}},[e._v("知识元: "+e._s(e.ruleInfo.ykz018))])]),l("div",{staticClass:"title"},[e._v("查询条件")]),l("ta-form",{attrs:{"auto-form-create":function(e){return t.searchForm=e},formLayout:!0,"label-width":"70px"}},[l("ta-form-item",{attrs:{fieldDecoratorId:"changetype",span:6,label:"需求类型"}},[l("ta-select",{attrs:{placeholder:"请选择","allow-clear":!0,"show-search":!0,"collection-type":"M_ITEMCHANGETYPE"}})],1),l("ta-form-item",{attrs:{fieldDecoratorId:"changelog",span:6,label:"变更说明"}},[l("ta-input",{attrs:{placeholder:"请输入"}})],1),l("ta-form-item",{attrs:{fieldDecoratorId:"checkStatus",span:6,label:"升级状态"}},[l("ta-select",{attrs:{placeholder:"请选择","collection-type":"M_CHECKSTATUS","allow-clear":!0,"show-search":!0}})],1),l("ta-form-item",{attrs:{fieldDecoratorId:"ake001",span:6,label:"三目编码"}},[l("ta-input",{attrs:{placeholder:"请输入"}})],1),l("ta-form-item",{attrs:{fieldDecoratorId:"ake002",span:6,label:"三目名称"}},[l("ta-input",{attrs:{placeholder:"请输入"}})],1),l("ta-form-item",[l("ta-button",{staticStyle:{"margin-left":"10px"},on:{click:e.resetFields}},[e._v("重置")]),l("ta-button",{attrs:{type:"primary"},on:{click:e.doSearch}},[e._v("查询")])],1)],1),l("div",["0"==e.checkStatus?l("ta-dropdown",[l("ta-menu",{attrs:{slot:"overlay"},slot:"overlay"},[l("ta-menu-item",[l("ta-popconfirm",{attrs:{title:"确认升级?","cancel-text":"取消","ok-text":"确认"},on:{confirm:function(t){return e.checkNodeBatch("1")}}},[l("ta-icon",{attrs:{type:"check-circle"}}),l("span",{staticClass:"mg-l12"},[e._v("升级")])],1)],1),l("ta-menu-divider"),l("ta-menu-item",[l("ta-popconfirm",{attrs:{title:"确认不升级?","cancel-text":"取消","ok-text":"确认"},on:{confirm:function(t){return e.checkNodeBatch("2")}}},[l("ta-icon",{attrs:{type:"stop"}}),l("span",{staticClass:"mg-l12"},[e._v("不升级")])],1)],1)],1),l("ta-button",{staticStyle:{float:"right","margin-right":"10px"}},[e._v(" 批量操作 "),l("ta-icon",{attrs:{type:"down"}})],1),l("div",{staticStyle:{clear:"both"}})],1):e._e(),l("div",{staticStyle:{clear:"both"}}),l("ta-big-table",{ref:"table",attrs:{data:e.dataList,size:"small","header-drag-style":"","auto-resize":!0,border:"full",heightSwitch:!0,height:386,"row-height":20}},[l("ta-big-table-column",{attrs:{type:"seq",width:"60",title:"序号",fixed:""}}),l("ta-big-table-column",{attrs:{type:"checkbox",width:"55"}}),e._l(e.columns,(function(t){return l("ta-big-table-column",{key:t.dataIndex,attrs:{align:t.align,resizable:"","show-header-overflow":"","show-overflow":t.overflowTooltip,field:t.dataIndex,title:t.title,fixed:t.fixed,width:t.width,collectionType:t.collectionType},scopedSlots:e._u([t.scopedSlots&&"0"==e.checkStatus?{key:"default",fn:function(a){var i=a.row;a.rowIndex;return["operate"==t.scopedSlots.customRender?l("div",[l("ta-popconfirm",{attrs:{title:"确认升级？"},on:{confirm:function(t){return e.doCheckNodeSingle(i,"1")}}},[l("a",[e._v("升级")])]),l("ta-popconfirm",{attrs:{title:"确认不升级？"},on:{confirm:function(t){return e.doCheckNodeSingle(i,"2")}}},[l("a",{staticStyle:{"margin-left":"10px"}},[e._v("不升级")])])],1):e._e()]}}:null],null,!0)})}))],2),l("ta-pagination",{ref:"pager",staticClass:"page",attrs:{dataSource:e.dataList,params:e.pageParam,url:"mtt/localruleconfig/ruleUserCommit/queryRuleItem",defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"]},on:{"update:dataSource":function(t){e.dataList=t},"update:data-source":function(t){e.dataList=t}}})],1)],1)},y=[],k={name:"NodeCheckDetail",props:{ruleInfo:Object,checkStatus:String},data:function(){return{dataList:[],columns:[{dataIndex:"changetype",title:"需求类型",collectionType:"M_ITEMCHANGETYPE",align:"center",overflowTooltip:!0},{dataIndex:"changelog",title:"需求变更说明",align:"center",overflowTooltip:!0},{dataIndex:"checkStatus",title:"升级状态",collectionType:"M_CHECKSTATUS",align:"center",overflowTooltip:!0},{dataIndex:"ake001",title:"三目编码",align:"center",overflowTooltip:!0},{dataIndex:"ake002",title:"三目名称",align:"center",overflowTooltip:!0},{dataIndex:"operate",title:"操作",scopedSlots:{customRender:"operate"},align:"center",overflowTooltip:!0}]}},mounted:function(){this.doSearch()},methods:{resetFields:function(){this.searchForm.resetFields()},doSearch:function(){this.$refs.pager.loadData()},doSearchCurrentPage:function(){var t=this,e=this.$refs.pager.getPagerInfo(),a=this.searchForm.getFieldsValue();Base.submit(null,{url:"mtt/localruleconfig/ruleUserCommit/queryRuleItem",data:(0,o.Z)((0,o.Z)({aae043:this.ruleInfo.aae043,ykz277:this.ruleInfo.ykz277},a),{},{pageNumber:e.current,pageSize:e.pageSize})}).then((function(e){t.dataList=e.data.pageBean.list}))},pageParam:function(){var t=this.searchForm.getFieldsValue();return(0,o.Z)({aae043:this.ruleInfo.aae043,ykz277:this.ruleInfo.ykz277},t)},doCheckNodeSingle:function(t,e){var a=this;Base.submit(null,{url:"mtt/localruleconfig/ruleUserCommit/checkRuleItemBatch",data:{aae043:this.ruleInfo.aae043,ykz277:this.ruleInfo.ykz277,checkStatus:e,ake001s:[t.ake001]},autoQs:!1}).then((function(t){a.$message.success("操作成功"),a.doSearchCurrentPage()}))},checkNodeBatch:function(t){var e=this,a=this.$refs.table.getCheckboxRecords();0!=a.length?Base.submit(null,{url:"mtt/localruleconfig/ruleUserCommit/checkRuleItemBatch",data:{aae043:this.ruleInfo.aae043,ykz277:this.ruleInfo.ykz277,checkStatus:t,ake001s:a.map((function(t){return t.ake001}))},autoQs:!1}).then((function(t){e.$message.success("操作成功"),e.doSearchCurrentPage()})):this.$message.info("请至少选择一行进行批量操作")}}},b=k,v=(0,f.Z)(b,g,y,!1,null,"51f120f5",null),S=v.exports,w={name:"ruleChangeDetail",components:{nodeDetail:m,itemDetail:S},props:{mttHistory:Object,checkStatus:String},watch:{current:function(t){"规则大类需求"==this.steps[t]&&this.getRuleidForMka44List(),"规则需求"==this.steps[t]&&this.getRuleidForMkf74List(),"节点需求"==this.steps[t]&&this.doNodeSearchCurrentPage()},blnMkeVisible:function(t){0==t&&this.doNodeSearchCurrentPage()},blnItemVisible:function(t){0==t&&this.doMkf74SearchCurrentPage()}},data:function(){return{current:0,stepListStand:["规则批号需求","规则大类需求","节点需求","规则需求","完成"],stepListLocal:["节点需求","规则需求","完成"],steps:[],blnLocal:!1,ruleidCheckList:[],ruleidCheckColumns:[{dataIndex:"changetype",title:"需求类型",collectionType:"M_QEQUIRETYPE",align:"center",overflowTooltip:!0},{dataIndex:"changelog",title:"需求变更说明",align:"center",overflowTooltip:!0},{dataIndex:"ruleid",title:"规则批号",align:"center",overflowTooltip:!0},{dataIndex:"aae500",title:"应用场景",collectionType:"MAAE500",align:"center",overflowTooltip:!0},{dataIndex:"ruleidlog",title:"规则批号说明",align:"center",overflowTooltip:!0},{dataIndex:"ykz248",title:"医疗就诊类型",collectionType:"MYKZ248",align:"center",overflowTooltip:!0},{dataIndex:"ykz227",title:"险种",collectionType:"MYKZ227",align:"center",overflowTooltip:!0},{dataIndex:"ykz268",title:"审核目录",collectionType:"MYKZ268",align:"center",overflowTooltip:!0}],ruleidMka44Search:void 0,ruleidMka44List:[],ruleidMka44DisplayList:[],ruleidMka44Click:{},ruleidMka44Columns:[{dataIndex:"ruleid",title:"规则批号",align:"center",overflowTooltip:!0},{dataIndex:"ruleidlog",title:"规则说明",align:"center",overflowTooltip:!0},{dataIndex:"aae100",title:"启停状态",scopedSlots:{customRender:"aae100"},align:"center",overflowTooltip:!0}],mka44List:[],mka44Columns:[{dataIndex:"changetype",title:"需求类型",collectionType:"M_QEQUIRETYPE",align:"center",overflowTooltip:!0},{dataIndex:"changelog",title:"需求变更说明",align:"center",overflowTooltip:!0},{dataIndex:"aaz319",title:"规则编码",align:"center",overflowTooltip:!0},{dataIndex:"aaa167",title:"规则名称",align:"center",overflowTooltip:!0},{dataIndex:"ape800",title:"违规类型",collectionType:"MAPE800",align:"center",overflowTooltip:!0}],ykz042List:[],ykz042Columns:[{dataIndex:"ykz002",title:"节点类型",align:"center",overflowTooltip:!0},{dataIndex:"changetype",title:"需求类型",collectionType:"M_QEQUIRETYPE",align:"center",overflowTooltip:!0},{dataIndex:"changelog",title:"需求变更说明",align:"center",overflowTooltip:!0},{dataIndex:"checkStatusName",title:"升级状态",align:"center",overflowTooltip:!0},{dataIndex:"ykz010",title:"节点名称",scopedSlots:{customRender:"ykz010"},align:"center",overflowTooltip:!0},{dataIndex:"ykz042",title:"节点编号",align:"center",overflowTooltip:!0},{dataIndex:"ykz065",title:"节点说明",align:"center",overflowTooltip:!0},{dataIndex:"operate",title:"操作",scopedSlots:{customRender:"operate"},align:"center",overflowTooltip:!0}],blnMkeVisible:!1,nodeInfo:{},ruleidMkf74Search:void 0,ruleidMkf74List:[],ruleidMkf74DisplayList:[],ruleidMkf74Click:{},ruleList:[],ruleColumns:[{dataIndex:"changetype",title:"需求类型",collectionType:"M_QEQUIRETYPE",align:"center",overflowTooltip:!0},{dataIndex:"changelog",title:"需求变更说明",align:"center",overflowTooltip:!0},{dataIndex:"changetypeMulti",title:"变更类型",align:"center",overflowTooltip:!0},{dataIndex:"checkStatus",title:"升级状态",collectionType:"M_CHECKSTATUS",align:"center",overflowTooltip:!0},{dataIndex:"aaa166",title:"规则大类",align:"center",overflowTooltip:!0},{dataIndex:"ykz277",title:"规则id",align:"center",overflowTooltip:!0},{dataIndex:"ykz018",title:"本地知识元",align:"center",overflowTooltip:!0},{dataIndex:"operate",title:"操作",scopedSlots:{customRender:"operate"},align:"center",overflowTooltip:!0}],blnItemVisible:!1,ruleInfo:{},bigRuleList:[],blnErrorVisible:!1,errorList:[],mkf01List:[]}},mounted:function(){this.blnLocal="2"==this.mttHistory.sourcetype,"1"==this.mttHistory.sourcetype?this.steps=this.stepListStand:this.steps=this.stepListLocal,this.getRuleBatchDemandList(),this.queryBigRule(),this.queryMkf01List()},methods:{queryMkf01List:function(){var t=this;Base.submit(null,{url:"mtt/localruleconfig/ruleUserCommit/queryMkf01List"}).then((function(e){t.mkf01List=e.data.list}))},queryBigRule:function(){var t=this;Base.submit(null,{url:"mttRuleConfig/listBigRule"}).then((function(e){t.bigRuleList=e.data.list}))},customRuleidMka44Row:function(t){var e={cursor:"pointer"};return this.ruleidMka44Click.ruleid&&this.ruleidMka44Click.ruleid==t.row.ruleid&&(e.background="#d0ffbf"),(0,o.Z)({},e)},cellRuleidMka44Click:function(t,e){this.ruleidMka44Click=t.row,this.doMka44Search()},customRuleidMkf74Row:function(t){var e={cursor:"pointer"};return this.ruleidMkf74Click.ruleid&&this.ruleidMkf74Click.ruleid==t.row.ruleid&&(e.background="#d0ffbf"),(0,o.Z)({},e)},cellRuleidMkf74Click:function(t,e){this.ruleidMkf74Click=t.row,this.doMkf74Search()},doMka44Search:function(){var t=this;Base.submit(null,{url:"mtt/localruleconfig/ruleUserCommit/getRuleBigTypePage",data:{aae043:this.mttHistory.aae043,ruleid:this.ruleidMka44Click.ruleid}}).then((function(e){t.mka44List=e.data.list}))},resetRuleFields:function(){this.ruleSearchForm.resetFields()},doMkf74Search:function(){this.ruleidMkf74Click.ruleid?this.$refs.mkf74Pager.loadData():this.$message.info("请先点击左侧审核场景")},doMkf74SearchCurrentPage:function(){var t=this,e=this.$refs.mkf74Pager.getPagerInfo(),a=this.ruleSearchForm.getFieldsValue();Base.submit(null,{url:"mtt/localruleconfig/ruleUserCommit/queryRuleDemandPage",data:(0,o.Z)((0,o.Z)({},a),{},{aae043:this.mttHistory.aae043,ruleid:this.ruleidMkf74Click.ruleid,pageNumber:e.current,pageSize:e.pageSize})}).then((function(e){t.ruleList=e.data.pageBean.list}))},mkf74PageParam:function(){var t=this.ruleSearchForm.getFieldsValue();return(0,o.Z)((0,o.Z)({},t),{},{aae043:this.mttHistory.aae043,ruleid:this.ruleidMkf74Click.ruleid})},goBack:function(){this.$emit("close")},getRuleBatchDemandList:function(){var t=this;Base.submit(null,{url:"mtt/localruleconfig/ruleUserCommit/getRuleBatchDemandList",data:{aae043:this.mttHistory.aae043}}).then((function(e){t.ruleidCheckList=e.data.list}))},getRuleidForMka44List:function(){var t=this;Base.submit(null,{url:"mtt/localruleconfig/ruleUserCommit/getAllRuleidList",data:{aae043:this.mttHistory.aae043,table:"mka44"}}).then((function(e){t.ruleidMka44List=e.data.list,t.doRuleidMka44Filter()}))},doRuleidMka44Filter:function(){var t=this;this.$nextTick((function(){t.ruleidMka44Search?t.ruleidMka44DisplayList=t.ruleidMka44List.filter((function(e){return e.ruleid&&e.ruleid.includes(t.ruleidMka44Search)||e.ruleidlog&&e.ruleidlog.includes(t.ruleidMka44Search)})):t.ruleidMka44DisplayList=t.ruleidMka44List}))},doRuleidMkf74Filter:function(){var t=this;this.$nextTick((function(){t.ruleidMkf74Search?t.ruleidMkf74DisplayList=t.ruleidMkf74List.filter((function(e){return e.ruleid&&e.ruleid.includes(t.ruleidMkf74Search)||e.ruleidlog&&e.ruleidlog.includes(t.ruleidMkf74Search)})):t.ruleidMkf74DisplayList=t.ruleidMkf74List}))},getRuleidForMkf74List:function(){var t=this;Base.submit(null,{url:"mtt/localruleconfig/ruleUserCommit/getAllRuleidList",data:{aae043:this.mttHistory.aae043,table:"mkf74"}}).then((function(e){t.ruleidMkf74List=e.data.list,t.doRuleidMkf74Filter()}))},resetNodeSearch:function(){this.nodeSearchFrom.resetFields()},doCheckNodeSingle:function(t,e){var a=this;Base.submit(null,{url:"mtt/localruleconfig/ruleUserCommit/checkNodeBatch",data:{aae043:this.mttHistory.aae043,checkStatus:e,ykz042s:[t.ykz042]},autoQs:!1}).then((function(t){a.$message.success("操作成功"),a.doNodeSearchCurrentPage()}))},doCheckRuleSingle:function(t,e){var a=this,l="0";["0","2","8"].includes(t.specialType)&&(l="1"),Base.submit(null,{url:"mtt/localruleconfig/ruleUserCommit/checkRuleBatch",data:{aae043:this.mttHistory.aae043,checkStatus:e,ykz277s:[t.ykz277],batchType:l},autoQs:!1}).then((function(i){a.doMkf74SearchCurrentPage(),"1"==e&&"0"==l&&(a.blnItemVisible=!0,a.ruleInfo=t,a.ruleInfo.aae043=a.mttHistory.aae043)}))},openItemDetail:function(t){this.ruleInfo=t,this.ruleInfo.aae043=this.mttHistory.aae043,this.blnItemVisible=!0},checkNodeBatch:function(t){var e=this,a=this.$refs.ykz042Table.getCheckboxRecords();0!=a.length?Base.submit(null,{url:"mtt/localruleconfig/ruleUserCommit/checkNodeBatch",data:{aae043:this.mttHistory.aae043,checkStatus:t,ykz042s:a.map((function(t){return t.ykz042}))},autoQs:!1}).then((function(t){e.$message.success("操作成功"),e.doNodeSearchCurrentPage()})):this.$message.info("请至少选择一行进行批量操作")},checkRuleBatch:function(t){var e=this,a=this.$refs.mkf74Table.getCheckboxRecords();0!=a.length?Base.submit(null,{url:"mtt/localruleconfig/ruleUserCommit/checkRuleBatch",data:{aae043:this.mttHistory.aae043,checkStatus:t,ykz277s:a.map((function(t){return t.ykz277})),batchType:"1"},autoQs:!1}).then((function(t){e.$message.success("操作成功"),e.doMkf74SearchCurrentPage()})):this.$message.info("请至少选择一行进行批量操作")},doNodeSearch:function(){this.$refs.ykz042Pager.loadData()},doNodeSearchCurrentPage:function(){var t=this,e=this.$refs.ykz042Pager.getPagerInfo(),a=this.nodeSearchFrom.getFieldsValue();Base.submit(null,{url:"mtt/localruleconfig/ruleUserCommit/queryPageNote",data:(0,o.Z)((0,o.Z)({aae043:this.mttHistory.aae043},a),{},{pageNumber:e.current,pageSize:e.pageSize})}).then((function(e){t.ykz042List=e.data.pageBean.list}))},ykz042PageParam:function(){var t=this.nodeSearchFrom.getFieldsValue();return(0,o.Z)({aae043:this.mttHistory.aae043},t)},openYkz010:function(t){this.blnMkeVisible=!0,this.nodeInfo=t,this.nodeInfo.aae043=this.mttHistory.aae043},openItemCheck:function(t){this.blnItemVisible=!0,this.ruleInfo=t},doSubmit:function(){var t=this;Base.submit(null,{url:"mtt/localruleconfig/ruleUserCommit/subDemandJudge",data:{aae043:this.mttHistory.aae043}}).then((function(e){null!=e.data.data&&e.data.data.length>0?(t.errorList=e.data.data,t.blnErrorVisible=!0):(t.$message.success("校验无误，正在应用升级。。"),Base.submit(null,{url:"mtt/localruleconfig/ruleUserCommit/subDemand",data:{aae043:t.mttHistory.aae043}}).then((function(e){t.current+=1})))}))},closeThisPage:function(){this.$emit("success")}}},x=w,I=(0,f.Z)(x,r,n,!1,null,"3219d3d1",null),C=I.exports,_={name:"ruleChangeConfirm",components:{ruleChangeDetail:C},data:function(){return{historyList:[],historyColumns:[{dataIndex:"aae043",title:"需求批号",align:"center",overflowTooltip:!0},{dataIndex:"createDate",title:"需求日期",align:"center",overflowTooltip:!0},{dataIndex:"sourcetype",title:"需求来源",collectionType:"M_SOURCETYPE",align:"center",overflowTooltip:!0},{dataIndex:"checkStatus",title:"需求状态",collectionType:"M_HISCHECKSTATUS",align:"center",overflowTooltip:!0}],clickRecord:{},bigRuleList:[],ykz109List:[],ykz108List:[],listData:[],listColumns:[{dataIndex:"type",title:"需求类型",collectionType:"M_QEQUIRETYPE",align:"center",overflowTooltip:!0},{dataIndex:"changelog",title:"需求变更说明",align:"center",overflowTooltip:!0},{dataIndex:"checkStatus",title:"升级状态",collectionType:"M_CHECKSTATUS",align:"center",overflowTooltip:!0},{dataIndex:"ykz108",title:"引擎号",align:"center",overflowTooltip:!0},{dataIndex:"aaa166",title:"规则大类",align:"center",overflowTooltip:!0},{dataIndex:"ykz277",title:"规则id",align:"center",overflowTooltip:!0},{dataIndex:"ykz018",title:"知识元",align:"center",overflowTooltip:!0},{dataIndex:"ykz227",title:"险种",collectionType:"MYKZ227",align:"center",overflowTooltip:!0},{dataIndex:"ykz248",title:"就医方式",collectionType:"MYKZ248",align:"center",overflowTooltip:!0},{dataIndex:"ykz109",title:"监控场景",align:"center",overflowTooltip:!0},{dataIndex:"ykz042",title:"节点编号",align:"center",overflowTooltip:!0},{dataIndex:"ykz010",title:"节点名称",align:"center",overflowTooltip:!0}],blnDetailVisible:!1,checkStatus:void 0}},mounted:function(){this.queryBigRule(),this.doRefresh(),this.getYkz108List(),this.getYkz109List()},methods:{customRow:function(t){var e={cursor:"pointer"};return this.clickRecord.aae043&&this.clickRecord.aae043==t.row.aae043&&(e.background="#d0ffbf"),(0,o.Z)({},e)},cellClick:function(t,e){this.clickRecord=t.row,this.doSearch()},queryBigRule:function(){var t=this;Base.submit(null,{url:"mttRuleConfig/listBigRule"}).then((function(e){t.bigRuleList=e.data.list}))},getYkz108List:function(){var t=this;Base.submit(null,{url:"mtt/localruleconfig/ruleUserCommit/getYkz108List"}).then((function(e){t.ykz108List=e.data.list}))},getYkz109List:function(){var t=this;Base.submit(null,{url:"mtt/localruleconfig/ruleUserCommit/getYkz109List"}).then((function(e){t.ykz109List=e.data.list}))},doRefresh:function(){this.$refs.historyPager.loadData()},doSearch:function(){this.clickRecord.aae043?this.$refs.listPager.loadData():this.$message.warning("请先点击左侧列表")},resetFields:function(){this.searchForm.resetFields()},historyPageParam:function(){return{}},listPageParam:function(){var t=this.searchForm.getFieldsValue();return(0,o.Z)((0,o.Z)({},t),{},{aae043:this.clickRecord.aae043})},openChangeDetail:function(){var t=this;this.clickRecord.aae043?Base.submit(null,{url:"mtt/localruleconfig/ruleUserCommit/checkUserConfirmStatus",data:{aae043:this.clickRecord.aae043}}).then((function(e){t.checkStatus=e.data.checkStatus,t.blnDetailVisible=!0})):this.$message.warning("请先点击左侧列表")},onSuccess:function(){this.blnDetailVisible=!1,this.doRefresh(),this.doSearch(),this.clickRecord={}},applyFirstAll:function(){var t=this;Base.submit(null,{url:"mtt/localruleconfig/ruleUserCommit/applyFirstAll",data:{aae043:this.clickRecord.aae043}}).then((function(e){t.$message.success("应用成功!"),t.doRefresh(),t.clickRecord={}}))},upgradeAll:function(){var t=this;Base.submit(null,{url:"mtt/localruleconfig/ruleUserCommit/upgradeAll",data:{aae043:this.clickRecord.aae043}}).then((function(e){t.$message.success("应用成功!"),t.doRefresh(),t.clickRecord={}}))}}},T=_,z=(0,f.Z)(T,l,i,!1,null,"7d164c20",null),M=z.exports}}]);
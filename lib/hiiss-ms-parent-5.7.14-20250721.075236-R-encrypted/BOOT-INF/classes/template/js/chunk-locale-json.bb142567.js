"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[8155],{31613:function(e){e.exports=JSON.parse('{"theme":{"changing":"Changing theme color..."}}')},58873:function(e){e.exports=JSON.parse('{"theme":{"changing":"正在切换主题"}}')},5437:function(e){e.exports=JSON.parse('{"screen":{"fullScreen":"Full Screen","exitFullScreen":"Exit Full Screen"},"search":{"search":"search...","favorites":"favorites","removeFavorites":"remove favorites"},"help":{"title":"Assistant Download","step1Title":"User Management","step1Desc":"User management can be carried out here, including password modification, skin changing, dark mode, language switching, navigation mode switching and system exit","step2Title":"Favorites","step2Desc":"Here you can see the common menus","step3Title":"Help","step3Desc":"You can restart the guide here"},"news":{"newsTitle":"News","unread":"unread","more":"More","noticeUnread":"notice(unread)","noticeRead":"notice(read)","privateLetter":"private letter","selectAll":"select all","new":"new","markRead":"mark read","sender":"sender","files":"files","reply":"reply: ","accessFunction":"access function","history":"History","saved":"Only the last 7 days will be saved","inputContent":"Please input the content (Ctrl + Enter to send message)","send":"send","close":"close","clear":"clear","createNews":"new notice","return":"back","systemNotice":"system notice","generalNotice":"general notice","addContacts":"add contacts","receiver":"receiver","theme":"theme","uploadFiles":"upload files","userSelect":"user select","xiaoY":"Xiao Y","Y":"Y","helloY":"Hello, this is xiao Y. May I help you?  <br/>1. Development help document <br/> 2. Project introduction <br/> 3. System function introduction <br/> 4. Technical support personnel <br/> 5. Requirements / suggestions","notLogin":"message did not get the login person","recycle":"Message Recycle Bin","uploadSucceeded":"Upload succeeded","uploadRules":"Upload failed. The file is over 20M. It is not supported","receiverEmpty":"Recipient cannot be empty","titleEmpty":"Title cannot be empty","contentEmpty":"Content cannot be empty","sendSuccess":"Sent successfully","chooseSelf":"Can\'t choose yourself","detail":"detail","helpDocument":"Help Document","projectIntroduction":"Project Introduction","noData":"No data, please look forward to it","support":"Support personnel: Yang Linqing Tel: *********** weChat: yanglinqing1111","suggest":"Please send <NAME_EMAIL>","AI":{"msg1":"Sorry, I don\'t know what you\'re talking about.","msg2":"You\'re great!!!","msg3":"Sorry, say that again","msg4":"I don\'t know either","msg5":"What?"}},"user":{"signIn":"Sign In","accountAssociation":"Account Association","changePassword":"Change Password","changeTheme":"Change Theme","customColors":"Custom Colors","changeThemeSuccess":"Theme color modified successfully","darkMode":"Dark Mode","language":"Language","navigationMode":"Navigation Mode","switchMode":"Navigation mode switch succeeded","exit":"Exit current account","currentOrg":"Current Organization"},"password":{"original":"original","originalPwd":"original password","originalPwdEmpty":"The original password cannot be empty","new":"new","newPwd":"new password","newPwdEmpty":"New password cannot be empty","confirm":"confirm","confirmPwd":"confirm password","confirmEmpty":"Confirm password cannot be empty","verification1":"Please include ","verification2":" of uppercase letters, lowercase letters, numbers, special characters (Spaces are not included), and the length is 8-20 bits","different":"The two passwords are different","modified":"Password modified successfully"},"phone":{"note":"You are operating sensitive data, please input password and mobile phone number to obtain verification code:","pwd":"password","pwdRequire":"Please input a password!","pwdPlh":"Current login password","phone":"phone","phoneRequire":"Please input phone number!","phonePlh":"Phone number to be bound","code":"code","codeRequire":"Please enter the verification code!","codePlh":"Verification code received","sendCode":"Send code","sendAfter":"S send again","willSend":"Code will be sent to ","sendTo":""},"third":{"title":"Third party account number","unbind":"unbind","bind":"bind","phone":"phone number","notBind":"Not bound yet!","bindPhone":"Binding mobile phone","unbindPhone":"unBinding mobile phone"},"workTable":{"workTable":"Work Table","closeAll":"Close All"},"refresh":{"refreshAll":"Refresh all","refreshPage":"Refresh this page","help":"Help"},"delete":"delete","return":"return","cancel":"cancel","submit":"submit"}')},45230:function(e){e.exports=JSON.parse('{"screen":{"fullScreen":"全屏显示","exitFullScreen":"退出全屏"},"search":{"search":"搜索...","favorites":"收藏夹","removeFavorites":"移除收藏"},"help":{"title":"小助手下载","step1Title":"用户管理","step1Desc":"这里可以进行用户管理，包括修改密码、换肤、暗黑模式、切换语言、切换导航模式、退出系统","step2Title":"收藏夹","step2Desc":"这里可查看常用菜单","step3Title":"帮助","step3Desc":"这里可以重新进行引导"},"news":{"newsTitle":"消息","unread":"未读","more":"查看更多","noticeUnread":"通知(未读)","noticeRead":"通知(已读)","privateLetter":"私信","selectAll":"全选","new":"新建","markRead":"标记已读","sender":"发送人","files":"附件","reply":"回复：","accessFunction":"进入功能","history":"历史记录","saved":"只会保存最近7天","inputContent":"请输入内容（Ctrl+Enter键发送信息）","send":"发送","close":"关闭","clear":"清空","createNews":"新消息","return":"返回","systemNotice":"系统通知","generalNotice":"普通通知","addContacts":"添加联系人","receiver":"接收人","theme":"主题","uploadFiles":"上传附件","userSelect":"人员选择","xiaoY":"小歪","Y":"歪","helloY":"您好，这里是小歪，请问有什么可以帮助您？<br/>1.开发帮助文档<br/>2.项目介绍<br/>3.系统功能介绍<br/>4.技术支持人员<br/>5.需求/建议","notLogin":"message 未获取到登录人员","recycle":"消息回收站","uploadSucceeded":"上传成功","uploadRules":"上传失败，文件超过20M,暂不支持","receiverEmpty":"接收人不能为空","titleEmpty":"标题不能为空","contentEmpty":"内容不能为空","sendSuccess":"发送成功","chooseSelf":"无法选择自己","detail":"查看详情","helpDocument":"帮助文档","projectIntroduction":"项目介绍","noData":"暂无，敬请期待","support":"支持人员：杨林青 电话: ***********  微信：yanglinqing1111","suggest":"相关问题/建议 请发送邮件到 <EMAIL>","AI":{"msg1":"人家不知道你在说什么","msg2":"你好厉害，666","msg3":"再说一遍","msg4":"我也不知道呐","msg5":"嗯？"}},"user":{"signIn":"签到","changePassword":"修改密码","accountAssociation":"账号关联","changeTheme":"换肤","customColors":"自定义颜色","changeThemeSuccess":"修改主题色成功","darkMode":"暗黑模式","language":"语言","navigationMode":"导航模式","switchMode":"导航模式切换成功","exit":"退出当前账号","currentOrg":"当前机构"},"password":{"original":"原密码","originalPwd":"原密码","originalPwdEmpty":"原密码不能为空","new":"新密码","newPwd":"新密码","newPwdEmpty":"新密码不能为空","confirm":"确认","confirmPwd":"确认密码","confirmEmpty":"确认密码不能为空","verification1":"请至少包含大写字母、小写字母、数字、特殊字符(除去空格)中的","verification2":"种，且长度为8~20位","different":"两次密码不同","modified":"密码修改成功"},"phone":{"note":"您正在操作敏感数据，请输入密码和手机号获取验证码：","pwd":"密码","pwdRequire":"请输入密码!","pwdPlh":"当前登录者密码","phone":"手机号","phoneRequire":"请输入手机号!","phonePlh":"即将绑定的手机号","code":"验证码","codeRequire":"请输入验证码!","codePlh":"收到的验证码","sendCode":"发送验证码","sendAfter":"秒后可重发","willSend":"将向","sendTo":"发送验证码"},"third":{"title":"第三方账号","unbind":"解绑","bind":"绑定","phone":"手机号码","notBind":"还未绑定哦！","bindPhone":"绑定手机","unbindPhone":"解绑手机"},"workTable":{"workTable":"工作台","closeAll":"关闭所有"},"refresh":{"refreshAll":"刷新全部","refreshPage":"刷新本页面","help":"帮助"},"delete":"删除","return":"返回","cancel":"取消","submit":"提交"}')},46474:function(e){e.exports=JSON.parse('{"login":{"systemLogin":"System Login","systemLoginTips":"系统登录","userName":"Username","password":"Password","userNameRequire":"Enter one user name!","userNameLength":"limitation of length!","passwordRequire":"Please input a password!","passwordOldRequire":"Please input a old password!","changePassword":"Change Password","login":"Login","explain":"Ta****** Development Framework","newPassword":"New Password","assistantDownload":"Assistant Download","inputNewPasswordRequire":"Please enter a new password!","inputNewPasswordAgain":"Enter the new password again","passwordStrenthSimple":"Please enter 6 digits","passwordStrenth1":"Please include ","passwordStrenth2":" of uppercase letters, lowercase letters, numbers, special characters (Spaces are not included), and the length is 8-20 bits","passwordDifferentTips":"The two passwords are different!","verificationCode":"Verification Code","verificationCodeRequire":"Please enter the verification code!","getVerificationCodeTips":"Click for verification code","backgroundImg":"Background image","dragImg":"Drag graph","sureToChange":"Swipe right to confirm change","loginMode":{"SMSLogin":"SMS login","SMSLoginTips":"短信登录","OtherLogin":"Other login","OtherLoginTips":"其它登录","smsLoginTips":"SMS verification code login","userPwdLoginTips":"Login with user name and password"},"pswdChangeSuccessTips":"Password modified successfully!","ok":"Ok","cancel":"Cancel","verify":{"verifyTitle":"Please complete the verification","validationSucceeded":"Validation Succeeded","validationFailed":"Validation Failed","waitingVerification":"Wait to Verify","pointTips":"Please click in order"}}}')},17723:function(e){e.exports=JSON.parse('{"login":{"systemLogin":"医保智能监控管理系统登录","systemLoginTips":"HIISS SYSTEM LOGIN","userName":"用户名","password":"密码","userNameRequire":"请输入用户名！","userNameLength":"长度限制","passwordRequire":"请输入密码！","passwordOldRequire":"请输入原始密码！","changePassword":"修改密码","assistantDownload":"小助手下载","login":"立即登录","explain":"四川久远银海软件股份有限公司","newPassword":"新密码","inputNewPasswordRequire":"请输入新密码!","inputNewPasswordAgain":"再次输入新密码","passwordStrenthSimple":"请输入6位数字","passwordStrenth1":"请至少包含大写字母、小写字母、数字、特殊字符(除去空格)中的","passwordStrenth2":"种，且长度为8~20位","passwordDifferentTips":"两次密码不同！","verificationCode":"验证码","verificationCodeRequire":"请输入验证码！","getVerificationCodeTips":"点击获取验证码","backgroundImg":"背景图","dragImg":"拖动图","sureToChange":"向右滑动确认修改","loginMode":{"SMSLogin":"短信登录","SMSLoginTips":"SMS LOGINING","OtherLogin":"其它登录","OtherLoginTips":"OTHER LOGIN","smsLoginTips":"短信验证码登录","userPwdLoginTips":"用户名密码登录"},"pswdChangeSuccessTips":"密码修改成功！","ok":"确认","cancel":"取消","verify":{"verifyTitle":"请完成安全验证","validationSucceeded":"验证成功","validationFailed":"验证失败","waitingVerification":"等待验证","pointTips":"请依次点击"}}}')}}]);
(typeof window=='undefined'?global:window).tc_cfg_5582378869106086={"url":"css/theme-colors-a12f935f.css","colors":["#1b65b9","#3274c0","#4984c7","#5f93ce","#76a3d5","#8db2dc","#a4c1e3","#bbd1ea","#d1e0f1","#ebf4fa","#bbd9ed","#8dbce0","#639fd4","#3e82c7","#1b65b9","#0f4894","#052f6e","#001a47","#000b21","27,101,185"]};

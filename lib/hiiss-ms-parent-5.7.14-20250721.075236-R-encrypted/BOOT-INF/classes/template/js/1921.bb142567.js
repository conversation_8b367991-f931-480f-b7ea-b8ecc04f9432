(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[1921],{88412:function(t,e,a){"use strict";var n=a(26263),i=a(36766),r=a(1001),l=(0,r.Z)(i.Z,n.s,n.x,!1,null,"5e7ef0ae",null);e["Z"]=l.exports},44013:function(t,e,a){"use strict";a.r(e),a.d(e,{default:function(){return y}});var n=function(){var t=this,e=this,a=e.$createElement,n=e._self._c||a;return n("div",{staticClass:"fit"},[n("ta-border-layout",{attrs:{"footer-cfg":{showBorder:!1},layout:{header:"110px",footer:"0px"},showPadding:!0}},[n("div",{attrs:{slot:"header"},slot:"header"},[n("ta-title",{attrs:{title:"查询条件"}}),n("ta-form",{staticStyle:{"padding-top":"5px"},attrs:{autoFormCreate:function(e){t.baseInfoForm=e},col:2,formLayout:!0}},[n("ta-form-item",{attrs:{fieldDecoratorOptions:{rules:[{required:!0,message:"必须选取日期"}]},"init-value":e.defaultValue,"label-col":{span:8},required:!0,span:4,"wrapper-col":{span:16},fieldDecoratorId:"allDate",label:"审核时间"}},[n("ta-date-picker",[n("ta-icon",{attrs:{slot:"suffixIcon",type:""},slot:"suffixIcon"})],1)],1),n("ta-form-item",{attrs:{"label-col":{span:8},span:4,"wrapper-col":{span:16},"field-decorator-id":"ape800",label:"违规等级"}},[n("ta-select",{attrs:{allowClear:"",placeholder:"违规等级筛选"},on:{change:e.handleChange}},[n("ta-select-option",{attrs:{value:"1"}},[e._v("重度可疑")]),n("ta-select-option",{attrs:{value:"2"}},[e._v("明确违规")]),n("ta-select-option",{attrs:{value:"3"}},[e._v("轻度可疑")])],1)],1),n("ta-form-item",{attrs:{"label-col":{span:8},span:4,"wrapper-col":{span:16},"field-decorator-id":"akb020",label:"院区标识"}},[n("ta-select",{attrs:{options:e.akb020List,disabled:e.paramsDisable.akb020,allowClear:"",placeholder:"院区选择"},on:{change:e.departSelectChange}})],1),n("ta-form-item",{attrs:{"label-col":{span:8},span:4,virtual:!0,"wrapper-col":{span:16},"field-decorator-id":"aaz307",label:"开单科室"}},[n("ta-select",{attrs:{options:e.ksList,"show-search":!0,allowClear:"",placeholder:"开单科室筛选",showSearch:""}})],1),n("ta-form-item",{attrs:{span:4,"label-col":{span:8},"wrapper-col":{span:16},fieldDecoratorId:"result",label:"预审结果","init-value":e.resultInit}},[n("ta-select",{attrs:{showSearch:"",mode:"multiple","collection-type":"APE800","collection-filter":"0",maxTagCount:1,placeholder:"请选择",allowClear:""}})],1),n("ta-form-item",{attrs:{"label-col":{span:8},span:4,"wrapper-col":{span:16},label:" "}},[n("ta-button",{attrs:{icon:"search",type:"primary"},on:{click:e.fnQuery}},[e._v("查询")]),n("ta-button",{attrs:{icon:"redo",disabled:e.paramsDisable.akb020,type:"default"},on:{click:e.fnReset}},[e._v("重置")])],1)],1)],1),n("div",{staticClass:"fit",staticStyle:{height:"88%"}},[n("ta-title",{staticStyle:{width:"50%"},attrs:{title:"查询结果"}}),n("ta-big-table",{ref:"Table",attrs:{"control-column":e.showHiddenOrSortColumn,data:e.userList,"footer-method":e.footerMethod,"tooltip-config":{enabled:!0,contentMethod:e.showTooltipMethod},"auto-resize":"",border:"","empty-text":"-",height:"100%","highlight-current-row":"","highlight-hover-row":"",resizable:"","show-footer":"","show-overflow":""},on:{"cell-click":e.cellClickEvent}},[n("ta-big-table-column",{attrs:{align:"center","header-align":"center","min-width":"50",sortable:"",title:"序号",type:"seq"}}),n("ta-big-table-column",{attrs:{align:"left",field:"akb021","header-align":"center","min-width":"180",sortable:"",title:"医院名称"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("ta-button",{attrs:{size:"small",type:"link"}},[n("span",{staticStyle:{color:"#00bcff","text-decoration":"underline"}},[e._v(e._s(a.akb021))])])]}}])}),n("ta-big-table-column",{attrs:{align:"left",field:"akb020","header-align":"center","min-width":"180",sortable:"",title:"医院编码"}}),n("ta-big-table-column",{attrs:{visible:!1,align:"right",field:"shzl","header-align":"center","min-width":"140",sortable:"",title:"审核总次数"}}),n("ta-big-table-column",{attrs:{align:"right",field:"txzl","header-align":"center","min-width":"140",sortable:"",title:"违规次数(次)"}}),n("ta-big-table-column",{attrs:{formatter:e.moneyFormat,align:"right",field:"txje","header-align":"center","min-width":"140",sortable:"",title:"违规金额(元)"}}),n("ta-big-table-column",{attrs:{align:"right",field:"txsl","header-align":"center","min-width":"140",sortable:"",title:"违规数量"}}),n("ta-big-table-column",{attrs:{align:"right",field:"jxxms","header-align":"center","min-width":"150",sortable:"",title:"继续使用次数(次)"}}),n("ta-big-table-column",{attrs:{formatter:e.ratioFormat,align:"right",field:"jxxmzb","header-align":"center","min-width":"180",sortable:"",title:"继续使用次数占比"}}),n("ta-big-table-column",{attrs:{align:"right",field:"qxxms","header-align":"center","min-width":"130",sortable:"",title:"取消次数(次)"}}),n("ta-big-table-column",{attrs:{formatter:e.ratioFormat,align:"right",field:"qxxmzb","header-align":"center","min-width":"150",sortable:"",title:"取消次数占比"}}),n("ta-big-table-column",{attrs:{align:"right",field:"zfxms","header-align":"center","min-width":"130",sortable:"",title:"自费次数(次)"}}),n("ta-big-table-column",{attrs:{formatter:e.ratioFormat,align:"right",field:"zfxmzb","header-align":"center","min-width":"150",sortable:"",title:"自费次数占比"}}),n("ta-big-table-column",{attrs:{align:"right",field:"wzcxms","header-align":"center","min-width":"140",sortable:"",title:"无操作次数(次)"}}),n("ta-big-table-column",{attrs:{formatter:e.ratioFormat,align:"right",field:"wczxmzb","header-align":"center","min-width":"160",sortable:"",title:"无操作次数占比"}}),n("template",{slot:"bottomBar"},[n("ta-button",{staticStyle:{float:"right","margin-top":"20px"},attrs:{icon:"download",type:"primary"},on:{click:e.exportTable}},[e._v("导出 ")]),n("ta-pagination",{ref:"gridPager",staticStyle:{float:"right","margin-top":"20px","margin-right":"10px"},attrs:{"data-source":e.userList,defaultPageSize:200,pageSizeOptions:["30","50","100","200","500"],params:e.infoPageParams,url:"reportStatistics/queryListNight"},on:{"update:dataSource":function(t){e.userList=t},"update:data-source":function(t){e.userList=t}}})],1)],2)],1)])],1)},i=[],r=a(66347),l=a(48534),o=a(95082),s=(a(36133),a(88412)),u=a(36797),c=a.n(u),f=a(22722),m=a(55115),d=a(18671),p=a(92566),h=a(83231);m.w3.prototype.Base=Object.assign(m.w3.prototype.Base,(0,o.Z)({},f.Z));var b={name:"nightStatisticsYY",components:{TaTitle:s.Z},data:function(){return{userList:[],amountData:[],resultInit:[],defaultValue:TaUtils.getMoment((new Date).toISOString().slice(0,10),"YYYY-MM-DD"),akb020:"",akb020List:[],ksList:[],permissions:{},paramsDisable:{akb020:!1},jxzb:"",showHiddenOrSortColumn:{themeColor:"red",icon:"appstore",showHiddenConfig:{open:!0,disabledHiddenColumns:function(t){return!1}},columnSortConfig:{open:!0,onMove:function(t,e){e.dragColumn,e.dropColumn;return!0},dragEnd:function(t,e){e.dragColumn,e.resultColumns}},successCallback:this.fnSaveTableTitle,disabledControlCol:function(t){}},txl:""}},created:function(){var t=this;return(0,l.Z)(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,h.Z.permissionCheck();case 2:return t.permissions=e.sent,e.next=5,t.$nextTick((function(){t.fnQueryTableTitle()}));case 5:case"end":return e.stop()}}),e)})))()},mounted:function(){var t=this;this.fnQueryHos(),this.fnQueryDept(""),this.$nextTick((function(){t.fnQueryAa01()}))},methods:{moment:c(),fnQueryTableTitle:function(){var t=this,e=top.indexTool.getActiveTabMenuId(),a=top.indexTool.getUserInfo().loginId,n=[];this.Base.submit(null,{url:"miimCommonRead/selectTableColumShow",data:{resourceid:e,loginid:a},autoValid:!1},{successCallback:function(e){if(e.data.list.length>0&&e.data.list[0].colum){var a=JSON.parse(e.data.list[0].colum),i=t.$refs.Table.getTableColumn().fullColumn;n=a.map((function(t){return t.title}));var r=[];i.forEach((function(t){if("checkbox"==t.type)r.push(t);else{var n=a.find((function(e){return e.title===t.title}));n&&(t.visible=n.visible,t.width=n.width);var i=t;i.sortable?i.minWidth=20*i.title.length+30:i.minWidth=20*i.title.length+10,"操作"==i.title&&(i.minWidth=150),"项目引导信息"==i.title&&(i.minWidth=300),e.data.akc191Title.length>0&&"akc191"===i.property&&(i.title=e.data.akc191Title[0].label),r.push(i)}})),n.length>0&&r.sort((function(t,e){return"序号"===t.title?-1:"序号"===e.title?1:n.indexOf(t.title)-n.indexOf(e.title)})),t.$refs.Table.loadColumn(r)}},failCallback:function(e){t.$message.error("查询表头失败")}})},fnSaveTableTitle:function(t){var e=this,a=(t.table,t.resultColumnsList),n=a,i=[];n.forEach((function(t){var e=t.title,a=t.visible;t.type;e&&i.push({title:e,visible:a})}));var r=top.indexTool.getActiveTabMenuId(),l=top.indexTool.getUserInfo().loginId,o={colum:JSON.stringify(i),flag:"column",resourceid:r,loginid:l};h.Z.insertTableColumShow(o,(function(t){"success"===t?e.$message.success("保存成功"):e.$message.error("保存失败")}))},fnQueryAa01:function(){var t=this;this.Base.submit(null,{url:"miimCommonRead/getAa01Aaa005ByAaz499",data:{aaz499:206}},{successCallback:function(e){var a=e.data;t.resultInit="n"===a.aaa005.toLowerCase()?["1","2"]:["1","2","3"],t.fnQuery()},failCallback:function(e){t.$message.error("查询aa01表数据失败")}})},fnQueryHos:function(){var t=this,e={url:"/miimCommonRead/queryHospRpcList",showPageLoading:!1,autoValid:!0},a={successCallback:function(e){t.akb020List=e.data.resultData,t.permissions&&t.permissions.akb020Set.size>0&&(t.paramsDisable.akb020=!0,t.akb020List=t.akb020List.filter((function(e){return t.permissions.akb020Set.has(e.value)})),t.baseInfoForm.setFieldsValue({akb020:t.akb020List[0].value})),t.akb020=t.akb020List[0].value},failCallback:function(e){t.$message.error("医院数据加载失败")}};this.Base.submit(null,e,a)},departSelectChange:function(t){this.fnQueryDept(t)},footerMethod:function(t){var e=this,a=t.columns;t.data;return[a.map((function(t,a){return 0===a?"合计":["txzl","sjzsl","txsl","txje","jxxms","qxxms","zfxms","wzcxms"].includes(t.property)?"txje"===t.property?e.formatAmount((0,p.Z)(e.amountData,t.property)):(0,p.Z)(e.amountData,t.property):null}))]},showTooltipMethod:function(t){var e=t.type,a=t.column,n=(t.row,t.items,t._columnIndex,a.property);if("header"===e)return d.Z.props[n]?d.Z.props[n][e]:""},handleChange:function(t){this.fnQuery()},cellClickEvent:function(t){var e=t.row,a=t.column,n=a.property;if("akb021"===n){var i=this.baseInfoForm.getFieldsValue();this.Base.openTabMenu({id:Math.floor(90*Math.random())+10,name:"【"+e.akb021+"】日审科室汇总",url:"reportStatistics.html#/nightStatisticsKS?akb020=".concat(e.akb020,"&params=").concat(JSON.stringify(i)),refresh:!1})}},exportTable:function(){var t=this,e=this.baseInfoForm.getFieldsValue();if(void 0===e.allDate||e.allDate.length<2)this.$message.error("请选择时间范围！");else{e=this.infoPageParams();var a,n=[],i=this.$refs.Table.getColumns(),l=(0,r.Z)(i);try{for(l.s();!(a=l.n()).done;){var o=a.value;"seq"!==o.type&&"operate"!==o.property&&!1!==o.visible&&n.push({header:o.title,key:o.property,width:20})}}catch(c){l.e(c)}finally{l.f()}var s=i.map((function(t){return t.property})),u=d.Z.codelist.filter((function(t){return s.includes(t.columnKey)}));this.Base.submit(null,{url:"reportStatistics/exportExcel",data:e,autoValid:!1},{successCallback:function(a){var i=a.data.data,r={fileName:"日审院区统计结果表("+e.startDate+"-"+e.endDate+")",sheets:[{name:"worksheet1",column:{complex:!1,columns:n},rows:i,codeList:u}]};t.Base.generateExcel(r)},failCallback:function(e){t.$message.error("数据加载失败")}})}},formatAmount:function(t){return t.toFixed(2).replace(/\d(?=(\d{3})+\.)/g,"$&,")},moneyFormat:function(t){var e=t.cellValue;return e&&0!=e?this.formatAmount(e):"0"},ratioFormat:function(t){var e=t.cellValue;return e&&0!=e?e+"%":"0"},fnReset:function(){this.baseInfoForm.resetFields(),this.txl="",this.jxzb="",this.fnQuery()},fnQueryDept:function(t){var e=this;this.akb020=t||"",this.Base.submit(null,{url:"miimCommonRead/queryDepartRpcList",data:{akb020:this.akb020,hospDeptType:"I"},showPageLoading:!1,autoValid:!1},{successCallback:function(t){e.ksList=t.data.resultData},failCallback:function(t){e.$message.error("科室数据加载失败")}})},infoPageParams:function(){var t=this.baseInfoForm.getFieldsValue();return t.startDate=t.allDate.format("YYYYMMDD"),t.endDate=t.allDate.format("YYYYMMDD"),t.aae500="6",t.flag="yy",t},fnQuery:function(){var t=this,e=this.baseInfoForm.getFieldValue("allDate");e?this.$nextTick((function(){t.$refs.gridPager.loadData((function(e){t.amountData=e.data.data}))})):this.$message.error("请选择时间范围！")},fnCustomHeaderCell:function(t){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px",fontWeight:"bold"}}},filterOption:function(t,e){return e.componentOptions.children[0].text.indexOf(t)>=0}}},g=b,v=a(1001),x=(0,v.Z)(g,n,i,!1,null,"0d030f34",null),y=x.exports},36766:function(t,e,a){"use strict";var n=a(66586);e["Z"]=n.Z},26263:function(t,e,a){"use strict";a.d(e,{s:function(){return n},x:function(){return i}});var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},i=[]},66586:function(t,e){"use strict";var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};e["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}},83231:function(t,e,a){"use strict";var n=a(48534);a(36133);function i(t){return t?t.akb020&&t.aaz263?"personal":t.akb020&&t.aaz309&&t.aaz307?"group":t.akb020&&t.aaz307?"department":t.akb020?"hospital":"all":"all"}function r(t){return l.apply(this,arguments)}function l(){return l=(0,n.Z)(regeneratorRuntime.mark((function t(e){var a,n,r,l,o,s,u,c,f,m,d,p;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Base.submit(null,{url:"/miimCommonRead/queryDataPermission",showPageLoading:!1,data:{jobnumber:e},autoValid:!0});case 2:return a=t.sent,n=new Set,r=new Set,a.data.permission.forEach((function(t){var e=i(t);"hospital"===e&&n.add(t.akb020),"department"===e&&r.add(t.aaz307)})),l=a.data.permission.filter((function(t){return"department"===i(t)||!r.has(t.aaz307)})).filter((function(t){return"hospital"===i(t)||!n.has(t.akb020)})),o=new Set(l.map((function(t){return t.akb020})).filter((function(t){return null!=t}))),s=new Set(l.map((function(t){return t.aaz307})).filter((function(t){return null!=t}))),u=new Set(l.map((function(t){return t.aaz309})).filter((function(t){return null!=t}))),c=new Set(l.map((function(t){return t.aaz263})).filter((function(t){return null!=t}))),f=!1,m=!1,d=!1,p=!1,1===o.size&&(f=!0),1===s.size&&1===o.size&&(m=!0),1===s.size&&1===o.size&&1===u.size&&(d=!0),1===o.size&&0===s.size&&1===c.size&&(p=!0),t.abrupt("return",{akb020Set:o,aaz307Set:s,aaz263Set:c,aaz309Set:u,akb020Disable:f,aaz307Disable:m,aaz263Disable:p,aaz309Disable:d});case 20:case"end":return t.stop()}}),t)}))),l.apply(this,arguments)}function o(t,e){Base.submit(null,{url:"miimCommonRead/getAa01AAE500StartStop",data:t},{successCallback:function(t){return e(t)}})}function s(t,e){Base.submit(null,{url:"miimCommonRead/insertTableColumShow",data:t,autoValid:!1},{successCallback:function(t){return e("success")},failCallback:function(t){return e("fail")}})}var u={zbx01:{footer:function(){return"统筹金总额"}},zbx02:{footer:function(){return"基金支出总额"}},zbx03:{footer:function(){return"总费用"}},zbx04:{footer:function(){return"总住院日"}}};e["Z"]={permissionCheck:r,getAa01AAE500StartStop:o,insertTableColumShow:s,props:u,moneyNumFormat:function(t){var e=t.cellValue;return e||"—"}}},18671:function(t,e){"use strict";var a=[{columnKey:"jxxmzb",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}},{columnKey:"txl",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}},{columnKey:"qxxmzb",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}},{columnKey:"zfxmzb",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}},{columnKey:"wczxmzb",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}}],n=[{columnKey:"jxxmzb",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}},{columnKey:"qxxmzb",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}},{columnKey:"zfxmzb",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}},{columnKey:"wczxmzb",customCollection:function(t,e){t.value&&(t.value=t.value+"%")}}],i={txl:{header:function(){return"提醒率=提醒总次数/审核总次数"}},jxxmzb:{header:function(){return"继续使用占比=继续使用次数(次)/提醒总次数"}},qxxmzb:{header:function(){return"取消使用占比=取消使用次数/提醒总次数"}},zfxmzb:{header:function(){return"自费使用占比=自费使用次数/提醒总次数"}},wczxmzb:{header:function(){return"无操作使用占比=无操作使用次数/提醒总次数"}}},r=new Map([["1","门诊审核"],["0","门特审核"],["2","医嘱审核"],["3","计费审核"]]);e["Z"]={codelist:a,codelist2:n,props:i,optionsMap:r}},55382:function(){},61219:function(){}}]);
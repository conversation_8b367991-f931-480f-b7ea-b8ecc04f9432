"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[9306],{9306:function(t,e,a){a.r(e),a.d(e,{default:function(){return c}});var l=function(){var t=this,e=this,a=e.$createElement,l=e._self._c||a;return l("div",{staticClass:"fit"},[l("ta-border-layout",{attrs:{layout:{footer:"60px"},"footer-cfg":{showBorder:!1}}},[l("ta-breadcrumb",{staticStyle:{"padding-left":"14px"}},[l("ta-breadcrumb-item",[l("a",{on:{click:e.backClick}},[e._v("图形管理")])]),l("ta-breadcrumb-item",[e._v("图形"+e._s("add"===e.pageFlag?"创建":"edit"===e.pageFlag?"修改":""))])],1),l("ta-card",{attrs:{title:"基本设置",bordered:!1}},[l("ta-form",{attrs:{layout:"horizontal","form-layout":!0,"auto-form-create":function(e){return t.form=e},"label-width":"120px"}},[l("ta-form-item",{attrs:{label:"图形名称",span:6,require:"","field-decorator-id":"txmc"}},[l("ta-input",{attrs:{allowClear:!0}})],1),l("ta-form-item",{attrs:{label:"是否展示名称","init-value":"1",span:6,"field-decorator-id":"sfzsmc"}},[l("ta-select",{attrs:{allowClear:!0}},[l("ta-select-option",{attrs:{value:"1"}},[e._v("是")]),l("ta-select-option",{attrs:{value:"0"}},[e._v("否")])],1)],1),l("ta-form-item",{attrs:{label:"图形描述",span:12,require:"","field-decorator-id":"txms"}},[l("ta-input",{attrs:{allowClear:!0}})],1),l("ta-form-item",{attrs:{label:"展示方式",span:6,require:"","field-decorator-id":"zsfs"}},[l("ta-select",{attrs:{allowClear:!0},on:{change:e.changeType}},e._l(e.txList,(function(t,a){return l("ta-select-option",{key:a,attrs:{value:t.value}},[e._v(e._s(t.label))])})),1)],1),"04"!==e.chartType?l("div",[l("ta-form-item",{attrs:{label:"单位",span:6,require:"","field-decorator-id":"dw"}},[l("ta-input",{attrs:{allowClear:!0}})],1)],1):l("div",[l("ta-form-item",{attrs:{label:"柱图单位",span:6,require:"","field-decorator-id":"ztdw"}},[l("ta-input",{attrs:{allowClear:!0}})],1),l("ta-form-item",{attrs:{label:"线图单位",span:6,require:"","field-decorator-id":"xtdw"}},[l("ta-input",{attrs:{allowClear:!0}})],1)],1),"02"===e.chartType?l("div",[l("ta-form-item",{attrs:{initValue:"0",label:"柱形方向",span:6,require:"","field-decorator-id":"zxfx"}},[l("ta-select",{attrs:{allowClear:!0}},[l("ta-select-option",{attrs:{value:"0"}},[e._v("竖向")]),l("ta-select-option",{attrs:{value:"1"}},[e._v("横向")])],1)],1),l("ta-form-item",{attrs:{initValue:"0",label:"是否堆叠",span:6,require:"","field-decorator-id":"sfdd"}},[l("ta-select",{attrs:{allowClear:!0}},[l("ta-select-option",{attrs:{value:"0"}},[e._v("不堆叠")]),l("ta-select-option",{attrs:{value:"1"}},[e._v("堆叠")])],1)],1)],1):e._e()],1)],1),l("ta-card",{attrs:{title:"其他设置",bordered:!1}},[l("ta-form",{attrs:{layout:"horizontal","form-layout":!0,"auto-form-create":function(e){return t.form1=e},"label-width":"120px"}},[l("ta-form-item",{attrs:{label:"色系",span:6,"field-decorator-id":"sx",fieldDecoratorOptions:{initialValue:JSON.stringify(e.colorList[0].value)}}},[l("ta-select",{staticClass:"sel_color"},e._l(e.colorList,(function(t,a){return l("ta-select-option",{key:a,attrs:{value:JSON.stringify(t.value)}},[l("div",{staticStyle:{display:"flex"}},e._l(t.value,(function(t,e){return l("div",{key:e,staticStyle:{width:"20px",height:"20px"},style:{background:t}})})),0)])})),1)],1),"04"===e.chartType?l("ta-form-item",{attrs:{label:"柱图坐标轴显示","init-value":"1",span:6,"field-decorator-id":"zbzxs_z"}},[l("ta-select",[l("ta-select-option",{attrs:{value:"1"}},[e._v("是")]),l("ta-select-option",{attrs:{value:"0"}},[e._v("否")])],1)],1):e._e(),"04"===e.chartType?l("ta-form-item",{attrs:{label:"线图坐标轴显示","init-value":"1",span:6,"field-decorator-id":"zbzxs_x"}},[l("ta-select",[l("ta-select-option",{attrs:{value:"1"}},[e._v("是")]),l("ta-select-option",{attrs:{value:"0"}},[e._v("否")])],1)],1):e._e(),l("ta-form-item",{attrs:{label:"图例位置","init-value":"bm",span:6,"field-decorator-id":"tlwz"}},[l("ta-select",[l("ta-select-option",{attrs:{value:"ul"}},[e._v("上左")]),l("ta-select-option",{attrs:{value:"um"}},[e._v("上中")]),l("ta-select-option",{attrs:{value:"ur"}},[e._v("上右")]),l("ta-select-option",{attrs:{value:"bl"}},[e._v("下左")]),l("ta-select-option",{attrs:{value:"bm"}},[e._v("下中")]),l("ta-select-option",{attrs:{value:"br"}},[e._v("下右")])],1)],1)],1)],1),l("ta-card",{attrs:{title:"指标选择",bordered:!1}},[l("ta-form",{attrs:{layout:"horizontal","form-layout":!0,"auto-form-create":function(e){return t.queryForm=e},"label-width":"100px"}},[l("ta-form-item",{attrs:{label:"指标名称",span:6,"field-decorator-id":"zbmc"}},[l("ta-input",{attrs:{allowClear:!0}})],1),l("div",{staticStyle:{float:"right"}},[l("ta-button",{attrs:{type:"primary"},on:{click:e.fnQuery}},[e._v("查询")])],1)],1),l("ta-big-table",{ref:"table_zbxz",attrs:{border:"","keep-source":"",stripe:"","auto-resize":"","highlight-hover-row":"",height:"300",align:"center",data:e.tableData_1},on:{"radio-change":e.radioChange},scopedSlots:e._u([{key:"bottomBar",fn:function(){return[l("ta-pagination",{ref:"gridPager",staticStyle:{"text-align":"right","margin-top":"15px"},attrs:{showQuickJumper:!1,showSizeChanger:!1,current:e.pageNum,total:e.total},on:{change:e.fnChangePage}})]},proxy:!0}])},[l("ta-big-table-column",{attrs:{type:"radio",width:"40"}}),l("ta-big-table-column",{attrs:{field:"kpiName",title:"指标名称",width:"200","show-overflow":""}}),l("ta-big-table-column",{attrs:{field:"kpiDscr",title:"指标描述",width:"","header-align":"center",align:"left","show-overflow":""}}),l("ta-big-table-column",{attrs:{field:"crteTime",title:"创建时间",width:"160","show-overflow":""}})],1),l("ta-tabs",{attrs:{activeKey:e.optionType,size:"small"},on:{change:e.handleTypeChange}},[l("ta-tab-pane",{key:"wdsz",attrs:{tab:"维度设置"}},[l("ta-big-table",{ref:"table_zbxz_wd",attrs:{border:"","keep-source":"",stripe:"","auto-resize":"","highlight-hover-row":"",height:"240",align:"center","radio-config":{highlight:!0,checkMethod:e.checkRadioMethod},data:e.tableData_wd},on:{"radio-change":e.radioChangeWd}},[l("ta-big-table-column",{attrs:{type:"radio",width:"40"}}),l("ta-big-table-column",{attrs:{field:"fld",title:"字段英文名",width:"","show-overflow":""}}),l("ta-big-table-column",{attrs:{field:"fldName",title:"字段中文名",width:"","show-overflow":""}})],1)],1),"01"===e.chartType||"02"===e.chartType?l("ta-tab-pane",{key:"dlsz",attrs:{tab:"度量设置"}},[l("ta-big-table",{ref:"table_zbxz_dl1",attrs:{"row-id":"fld",border:"","keep-source":"",stripe:"","auto-resize":"","highlight-hover-row":"","checkbox-config":{showHeader:!1,checkMethod:e.checCheckboxkMethod1,checkRowKeys:e.defaultCheck1},height:"240",align:"center",data:e.tableData_dl1},on:{"checkbox-change":e.checkBoxChange1}},[l("ta-big-table-column",{attrs:{type:"checkbox",width:"40"}}),l("ta-big-table-column",{attrs:{field:"fld",title:"字段英文名",width:"","show-overflow":""}}),l("ta-big-table-column",{attrs:{field:"fldName",title:"字段中文名",width:"","show-overflow":""}}),l("ta-big-table-column",{attrs:{field:"seq",title:"字段顺序",width:"","show-overflow":""}})],1)],1):e._e(),"03"===e.chartType?l("ta-tab-pane",{key:"dlsz_pie",attrs:{tab:"度量设置"}},[l("ta-big-table",{ref:"table_zbxz_dl1_pie",attrs:{"row-id":"fld",border:"","keep-source":"",stripe:"","auto-resize":"","highlight-hover-row":"","radio-config":{highlight:!0,checkMethod:e.checkRadioMethod,checkRowKey:e.defaultCheck1_pie},height:"240",align:"center",data:e.tableData_dl1_pie},on:{"radio-change":e.checkBoxChange1_pie}},[l("ta-big-table-column",{attrs:{type:"radio",width:"40"}}),l("ta-big-table-column",{attrs:{field:"fld",title:"字段英文名",width:"","show-overflow":""}}),l("ta-big-table-column",{attrs:{field:"fldName",title:"字段中文名",width:"","show-overflow":""}})],1)],1):e._e(),"04"===e.chartType?l("ta-tab-pane",{key:"ztdlsz",attrs:{tab:"柱图度量设置"}},[l("ta-big-table",{ref:"table_zbxz_dl2",attrs:{"row-id":"fld",border:"","keep-source":"",stripe:"","auto-resize":"","highlight-hover-row":"","checkbox-config":{showHeader:!1,checkMethod:e.checCheckboxkMethod2,checkRowKeys:e.defaultCheck2},height:"240",align:"center",data:e.tableData_dl2},on:{"checkbox-change":e.checkBoxChange2}},[l("ta-big-table-column",{attrs:{type:"checkbox",width:"40"}}),l("ta-big-table-column",{attrs:{field:"fld",title:"字段英文名",width:"","show-overflow":""}}),l("ta-big-table-column",{attrs:{field:"fldName",title:"字段中文名",width:"","show-overflow":""}}),l("ta-big-table-column",{attrs:{field:"seq",title:"字段顺序",width:"","show-overflow":""}})],1)],1):e._e(),"04"===e.chartType?l("ta-tab-pane",{key:"xtdlsz",attrs:{tab:"线图度量设置"}},[l("ta-big-table",{ref:"table_zbxz_dl3",attrs:{"row-id":"fld",border:"","keep-source":"",stripe:"","auto-resize":"","highlight-hover-row":"","checkbox-config":{showHeader:!1,checkMethod:e.checCheckboxkMethod3,checkRowKeys:e.defaultCheck3},height:"240",align:"center",data:e.tableData_dl3},on:{"checkbox-change":e.checkBoxChange3}},[l("ta-big-table-column",{attrs:{type:"checkbox",width:"40"}}),l("ta-big-table-column",{attrs:{field:"fld",title:"字段英文名",width:"","show-overflow":""}}),l("ta-big-table-column",{attrs:{field:"fldName",title:"字段中文名",width:"","show-overflow":""}}),l("ta-big-table-column",{attrs:{field:"seq",title:"字段顺序",width:"","show-overflow":""}})],1)],1):e._e()],1)],1),l("div",{attrs:{slot:"footer"},slot:"footer"},[l("div",{staticStyle:{display:"flex","justify-content":"center"}},["show"!==e.pageFlag?l("ta-button",{staticStyle:{margin:"0 16px"},attrs:{type:"primary"},on:{click:e.saveClick}},[e._v("保存")]):e._e(),l("ta-button",{on:{click:e.cancelClick}},[e._v("取消")])],1)])],1)],1)},r=[],i={name:"otherPage",data:function(){return{pageFlag:"",rptChartCfgId:"",chartType:"",pageNum:1,pageSize:10,total:0,optionType:"wdsz",colorList:[{key:"word常规",value:["#ED7D31","#4472C4","#FFC000","#999999","#ff0000"]},{key:"默认",value:["#38A1D9","#69E5C1","#9587dc","#5b56d4","#e6a23c"]}],txList:[{label:"线图",value:"01"},{label:"柱图",value:"02"},{label:"饼图",value:"03"},{label:"柱线图",value:"04"}],tableData_1:[],tableData_2:[],tableData_wd:[],tableData_dl1:[],tableData_dl1_pie:[],tableData_dl2:[],tableData_dl3:[],arr_dl1:[],arr_dl2:[],arr_dl3:[],wdInfo:{},dl_pie:{},kpiSelect:{},chartCfgResult:{},defaultCheck1:[],defaultCheck1_pie:"",defaultCheck2:[],defaultCheck3:[]}},mounted:function(){var t=this;this.pageFlag=this.$route.params.pageFlag,this.rptChartCfgId=this.$route.params.rptChartCfgId,"add"===this.pageFlag?this.$nextTick((function(){t.form.resetFields(),t.queryForm.resetFields(),t.$refs.table_zbxz.clearRadioRow(),t.$refs.table_zbxz_wd.clearRadioRow(),t.optionType="wdsz",t.tableData_1=[],t.tableData_2=[],t.tableData_wd=[],t.tableData_dl1=[],t.tableData_dl1_pie=[],t.tableData_dl2=[],t.tableData_dl3=[],t.arr_dl1=[],t.arr_dl2=[],t.arr_dl3=[],t.kpiSelect={},t.wdInfo={},t.dl_pie={},t.defaultCheck1=[],t.defaultCheck1_pie="",t.defaultCheck2=[],t.defaultCheck3=[],t.queryPageList()})):this.$nextTick((function(){t.kpiSelect={},t.wdInfo={},t.optionType="wdsz",t.queryChartDetailCfg()}))},methods:{backClick:function(){this.$router.push({name:"tbgl"})},queryChartDetailCfg:function(){var t=this;Base.submit(null,{url:"/smtrpt/chartCfg/queryChartDetailCfg",data:{rptChartCfgId:this.rptChartCfgId}}).then((function(e){var a=e.data.chartCfg;t.chartCfgResult=a,t.chartType=a.chartType,t.$nextTick((function(){t.form.setFieldsValue({txmc:a.chartName,sfzsmc:a.chartNameShow,txms:a.chartDesc,zsfs:a.chartType,dw:a.unitS[0],ztdw:a.unitS[0],xtdw:a.unitS[1],zxfx:a.elseCfgS.dirBar,sfdd:a.elseCfgS.stack}),t.form1.setFieldsValue({sx:a.elseCfgS.colorList,zbzxs_z:a.elseCfgS.axisShow_z,zbzxs_x:a.elseCfgS.axisShow_x,tlwz:a.elseCfgS.legendPotion}),t.queryForm.setFieldsValue({zbmc:a.kpiName}),t.queryPageList(),t.queryKpiDetailInfo(a.kpiCodg)}))}))},queryPageList:function(){var t=this,e=this.queryForm.getFieldsValue();Base.submit(null,{url:"/smtrpt/kpiInfo/queryPageList",data:{kpiName:e.zbmc,pageNum:this.pageNum,pageSize:this.pageSize}}).then((function(a){t.total=a.data.pageBean.recordCounts,t.tableData_1=a.data.pageBean.data||[],"edit"===t.pageFlag&&(e.zbmc?(t.$refs.table_zbxz.setRadioRow(t.tableData_1[0]),t.kpiSelect=t.tableData_1[0]):t.$refs.table_zbxz.clearRadioRow())}))},queryKpiDetailInfo:function(t){var e=this;this.tableData_2=[],this.tableData_wd=[],this.tableData_dl1=[],this.tableData_dl1_pie=[],this.tableData_dl2=[],this.tableData_dl3=[],this.arr_dl1=[],this.arr_dl2=[],this.arr_dl3=[],this.defaultCheck1_pie="",this.defaultCheck1=[],this.defaultCheck2=[],this.defaultCheck3=[],Base.submit(null,{url:"/smtrpt/kpiInfo/queryKpiDetailInfo",data:{kpiCodg:t}}).then((function(t){var a=t.data.kpiInfo||{};e.tableData_2=a.fldList,e.tableData_wd=e.tableData_2.slice(),e.tableData_dl1=e.tableData_2.slice(),e.tableData_dl1_pie=e.tableData_2.slice(),e.tableData_dl2=e.tableData_2.slice(),e.tableData_dl3=e.tableData_2.slice(),"edit"===e.pageFlag&&e.chartCfgResult.dimFld&&(e.tableData_wd.forEach((function(t){t.fld===e.chartCfgResult.dimFld&&(e.$refs.table_zbxz_wd.setRadioRow(t),e.wdInfo=t)})),"01"===e.chartType||"02"===e.chartType?(e.tableData_dl1=e.tableData_2.filter((function(t){return t.fld!==e.chartCfgResult.dimFld})),e.chartCfgResult.measureFldList.forEach((function(t,a){e.tableData_dl1.forEach((function(l,r){l.fld===t.fld&&(l.seq=a+1,e.arr_dl1.push(l))}))}))):"03"===e.chartType?(e.tableData_dl1_pie=e.tableData_2.filter((function(t){return t.fld!==e.chartCfgResult.dimFld})),e.tableData_dl1_pie.forEach((function(t){t.fld===e.chartCfgResult.measureFldList[0].fld&&(e.dl_pie=t)}))):"04"===e.chartType&&(e.tableData_dl2=e.tableData_2.filter((function(t){return t.fld!==e.chartCfgResult.dimFld})),e.chartCfgResult.measureFldList.forEach((function(t,a){e.tableData_dl2.forEach((function(l,r){l.fld===t.fld&&(l.seq=a+1,e.arr_dl2.push(l))}))})),e.tableData_dl3=e.tableData_2.filter((function(t){return t.fld!==e.chartCfgResult.dimFld})),e.chartCfgResult.measureFldList.forEach((function(t,a){e.tableData_dl3.forEach((function(l,r){l.fld===t.fld&&(l.seq=a+1,e.arr_dl3.push(l))}))}))))}))},fnChangePage:function(t){this.pageNum=t,this.queryPageList()},changeType:function(t){var e=this;this.$nextTick((function(){e.chartType=t,"04"!==e.chartType?(e.form.setFieldsValue({ztdw:"ztdw"}),e.form.setFieldsValue({xtdw:"xtdw"})):e.form.setFieldsValue({dw:"dw"}),e.$refs.table_zbxz.clearRadioRow(),e.queryForm.resetFields(),e.tableData_wd=[],e.tableData_dl1=[],e.tableData_dl1_pie=[],e.tableData_dl2=[],e.tableData_dl3=[],e.arr_dl1=[],e.arr_dl2=[],e.arr_dl3=[],e.kpiSelect={},e.wdInfo={},e.dl_pie={},e.queryPageList()}))},fnQuery:function(){this.queryPageList()},radioChange:function(t){var e=t.row;this.kpiSelect=e,this.queryKpiDetailInfo(e.kpiCodg)},checkRadioMethod:function(){return!!this.chartType},checCheckboxkMethod1:function(){return!!this.chartType},checCheckboxkMethod2:function(){return!!this.chartType},checCheckboxkMethod3:function(){return!!this.chartType},handleTypeChange:function(t){var e=this;this.optionType=t,"edit"===this.pageFlag&&"dlsz"===this.optionType&&this.$nextTick((function(){e.arr_dl1=[],e.chartCfgResult.measureFldList.forEach((function(t,a){e.tableData_dl1.forEach((function(l,r){l.fld===t.fld&&(l.seq=a+1,e.$refs.table_zbxz_dl1.setCheckboxRow([e.tableData_dl1[r]],!0),e.arr_dl1.push(l))}))}))})),"edit"===this.pageFlag&&"dlsz_pie"===this.optionType&&this.$nextTick((function(){e.dl_pie={},e.tableData_dl1_pie.forEach((function(t){t.fld===e.chartCfgResult.measureFldList[0].fld&&(e.$refs.table_zbxz_dl1_pie.setRadioRow(t),e.dl_pie=t)}))})),"edit"===this.pageFlag&&"ztdlsz"===this.optionType&&this.$nextTick((function(){e.arr_dl2=[];for(var t=0,a=function(a){e.tableData_dl2.forEach((function(l,r){l.seq="",l.fld===e.chartCfgResult.measureFldList[a].fld&&(l.seq=++t,e.$refs.table_zbxz_dl2.setCheckboxRow(l,!0),e.arr_dl2.push(l))}))},l=0;l<parseInt(e.chartCfgResult.elseCfgS.barNums);l++)a(l)})),"edit"===this.pageFlag&&"xtdlsz"===this.optionType&&this.$nextTick((function(){e.arr_dl3=[];for(var t=0,a=function(a){e.tableData_dl3.forEach((function(l,r){l.seq="",l.fld===e.chartCfgResult.measureFldList[a].fld&&(l.seq=++t,e.$refs.table_zbxz_dl3.setCheckboxRow(l,!0),e.arr_dl3.push(l))}))},l=parseInt(e.chartCfgResult.elseCfgS.barNums);l<e.chartCfgResult.measureFldList.length;l++)a(l)}))},radioChangeWd:function(t){var e=t.row;this.wdInfo=e,"01"===this.chartType||"02"===this.chartType?this.tableData_dl1=this.tableData_2.filter((function(t){return t.fld!==e.fld})):"03"===this.chartType?this.tableData_dl1_pie=this.tableData_2.filter((function(t){return t.fld!==e.fld})):"04"===this.chartType&&(this.tableData_dl2=this.tableData_2.filter((function(t){return t.fld!==e.fld})),this.tableData_dl3=this.tableData_2.filter((function(t){return t.fld!==e.fld}))),this.$refs.table_zbxz_dl1&&this.$refs.table_zbxz_dl1.clearCheckboxRow(),this.$refs.table_zbxz_dl1_pie&&this.$refs.table_zbxz_dl1_pie.clearCheckboxRow(),this.$refs.table_zbxz_dl2&&this.$refs.table_zbxz_dl2.clearCheckboxRow(),this.$refs.table_zbxz_dl3&&this.$refs.table_zbxz_dl3.clearCheckboxRow()},checkBoxChange1:function(t){var e=this,a=t.row,l=t.checked;this.$refs.table_zbxz_wd.getRadioRecord()?(this.arr_dl1.includes(a)||!0!==l?this.arr_dl1.includes(a)&&!1===l&&(this.arr_dl1.forEach((function(t,l){t.fld===a.fld&&e.arr_dl1.splice(l,1)})),this.tableData_dl1.forEach((function(t){t.fld===a.fld&&(t.seq="")}))):this.arr_dl1.push(a),this.arr_dl1.forEach((function(t,a){e.tableData_dl1.forEach((function(e){e.fld===t.fld&&(e.seq=a+1)}))}))):(this.$message.warning("请先选择维度"),this.$refs.table_zbxz_dl1.clearCheckboxRow())},checkBoxChange1_pie:function(t){var e=t.row;this.$refs.table_zbxz_wd.getRadioRecord()?this.dl_pie=e:(this.$message.warning("请先选择维度"),this.$refs.table_zbxz_dl1_pie.clearRadioRow())},checkBoxChange2:function(t){var e=this,a=t.row,l=t.checked;t.records;this.$refs.table_zbxz_wd.getRadioRecord()?(this.arr_dl2.includes(a)||!0!==l?this.arr_dl2.includes(a)&&!1===l&&(this.arr_dl2.forEach((function(t,l){t.fld===a.fld&&e.arr_dl2.splice(l,1)})),this.tableData_dl2.forEach((function(t){t.fld===a.fld&&(t.seq="")}))):this.arr_dl2.push(a),this.arr_dl2.forEach((function(t,a){e.tableData_dl2.forEach((function(e){e.fld===t.fld&&(e.seq=a+1)}))}))):(this.$message.warning("请先选择维度"),this.$refs.table_zbxz_dl2.clearCheckboxRow())},checkBoxChange3:function(t){var e=this,a=t.row,l=t.checked;t.records;this.$refs.table_zbxz_wd.getRadioRecord()?(this.arr_dl3.includes(a)||!0!==l?this.arr_dl3.includes(a)&&!1===l&&(this.arr_dl3.forEach((function(t,l){t.fld===a.fld&&e.arr_dl3.splice(l,1)})),this.tableData_dl3.forEach((function(t){t.fld===a.fld&&(t.seq="")}))):this.arr_dl3.push(a),this.arr_dl3.forEach((function(t,a){e.tableData_dl3.forEach((function(e){e.fld===t.fld&&(e.seq=a+1)}))}))):(this.$message.warning("请先选择维度"),this.$refs.table_zbxz_dl3.clearCheckboxRow())},saveClick:function(){var t=this;if(this.kpiSelect.kpiCodg)if(this.wdInfo.fld){if("03"===this.chartType){if(!this.dl_pie.fld)return void this.$message.error("未选择度量")}else if("01"===this.chartType||"02"===this.chartType){if(0===this.arr_dl1.length)return void this.$message.error("未选择度量")}else{if(0===this.arr_dl2.length)return void this.$message.error("未选择柱图度量");if(0===this.arr_dl3.length)return void this.$message.error("未选择线图度量")}var e=this.form.getFieldsValue(),a=this.form1.getFieldsValue();Object.assign(e,a);var l=[],r=[],i={},s={};if("04"!==this.chartType?l.push(e.dw):l.push(e.ztdw,e.xtdw),i.colorList=JSON.parse(e.sx),i.axisShow_z=e.zbzxs_z,i.axisShow_x=e.zbzxs_x,i.legendPotion=e.tlwz,"02"===this.chartType?(i.dirBar=e.zxfx,i.stack=e.sfdd):"04"===this.chartType&&(i.barNums=this.arr_dl2.length.toString()),"03"===this.chartType)r.push(this.dl_pie);else if("01"===this.chartType||"02"===this.chartType)r=this.arr_dl1.map((function(t){return delete t._XID,t}));else{var o=[],d=[];o=this.arr_dl2.map((function(t){return delete t._XID,t})),d=this.arr_dl3.map((function(t){return delete t._XID,t})),r=o.concat(d)}s="add"===this.pageFlag?{chartName:e.txmc,chartNameShow:e.sfzsmc,chartDesc:e.txms,chartType:e.zsfs,kpiCodg:this.kpiSelect.kpiCodg,kpiName:this.kpiSelect.kpiName,dimFld:this.wdInfo.fld?this.wdInfo.fld:"",dimFldName:this.wdInfo.fldName?this.wdInfo.fldName:"",measureFldList:r,unitS:l,elseCfgS:i}:{rptChartCfgId:this.rptChartCfgId,chartName:e.txmc,chartNameShow:e.sfzsmc,chartDesc:e.txms,chartType:e.zsfs,kpiCodg:this.kpiSelect.kpiCodg,kpiName:this.kpiSelect.kpiName,dimFld:this.wdInfo.fld?this.wdInfo.fld:"",dimFldName:this.wdInfo.fldName?this.wdInfo.fldName:"",measureFldList:r,unitS:l,elseCfgS:i};var c=JSON.stringify(s);Base.submit(this.form,{url:"/smtrpt/chartCfg/saveChartCfg",data:{smtrptChartCfg:c},autoValid:!0}).then((function(e){e.serviceSuccess&&(t.$message.success("保存成功"),t.$router.push({name:"tbgl"}))}))}else this.$message.error("未选择维度");else this.$message.error("未选择指标")},cancelClick:function(){this.$router.push({name:"tbgl"})}}},s=i,o=a(1001),d=(0,o.Z)(s,l,r,!1,null,"84474296",null),c=d.exports}}]);
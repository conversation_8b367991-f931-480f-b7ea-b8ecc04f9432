"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[236],{64437:function(t,e,a){a.r(e),a.d(e,{default:function(){return h}});var o=function(){var t=this,e=this,a=e.$createElement,o=e._self._c||a;return o("ta-border-layout",{attrs:{"show-padding":!1,layout:{footer:"0px"}}},[o("ta-card",{attrs:{bordered:!1}},[o("div",{staticClass:"title"},[e._v("查询条件")]),o("ta-row",[o("ta-form",{attrs:{autoFormCreate:function(e){t.searchForm=e},"form-layout":!0}},[o("ta-form-item",{attrs:{fieldDecoratorId:"operationtime",span:4,label:"操作时间",require:{message:"操作时间不能为空"}}},[o("ta-range-picker",{attrs:{placeholder:["开始时间","结束时间"],"allow-clear":!0}})],1),o("ta-form-item",{attrs:{fieldDecoratorId:"ykz001",span:4,label:"节点类型"}},[o("ta-select",{attrs:{"show-search":!0,"allow-clear":!0}},e._l(e.nodeTypeList,(function(t,a){return o("ta-select-option",{key:t.ykz001,attrs:{value:t.ykz001}},[e._v(" "+e._s(t.showName)+" ")])})),1)],1),o("ta-form-item",{attrs:{span:4,label:"节点名称",fieldDecoratorId:"ykz010"}},[o("ta-input",{attrs:{placeholder:"请输入"}})],1),o("ta-form-item",{attrs:{span:4,label:"节点编号",fieldDecoratorId:"ykz042"}},[o("ta-input",{attrs:{placeholder:"请输入"}})],1),o("ta-form-item",{attrs:{span:4}},[o("ta-button",{staticStyle:{"margin-left":"50px"},attrs:{span:2},on:{click:e.resetFields}},[e._v("重置")]),o("ta-button",{attrs:{type:"primary"},on:{click:e.doSearch}},[e._v("查询")])],1)],1)],1)],1),o("ta-card",{attrs:{bordered:!1}},[o("div",{staticClass:"title"},[e._v("节点列表"),o("ta-button",{staticStyle:{float:"right","margin-right":"10px"},attrs:{type:"primary"},on:{click:e.openExportAll}},[e._v("全量导出")])],1),o("ta-table",{attrs:{size:"small","have-sn":!0,columns:e.bigRuleColumns,"data-source":e.bigRuleList,scroll:{x:"100%",y:420}},scopedSlots:e._u([{key:"expandedRowRender",fn:function(t){return o("ta-table",{attrs:{size:"small",columns:t.columns,dataSource:t.dataList},scopedSlots:e._u([{key:"operate",fn:function(a,i){return o("div",{},[o("a",{on:{click:function(a){return e.openItemLog(t,i)}}},[e._v("日志")])])}}])})}}])}),o("div",[o("ta-pagination",{ref:"pager",staticClass:"page",attrs:{defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"],dataSource:e.bigRuleList,params:e.pageParams,url:"mtt/localruleconfig/nodeChange/queryNodePage"},on:{"update:dataSource":function(t){e.bigRuleList=t},"update:data-source":function(t){e.bigRuleList=t}},model:{value:e.pageParam.pageNumber,callback:function(t){e.$set(e.pageParam,"pageNumber",t)},expression:"pageParam.pageNumber"}})],1)],1),o("ta-modal",{attrs:{width:1e3,title:"操作日志"},on:{ok:function(t){e.blnRuleLogVisible=!1}},model:{value:e.blnRuleLogVisible,callback:function(t){e.blnRuleLogVisible=t},expression:"blnRuleLogVisible"}},[o("node-log",{attrs:{record:e.editRecord}})],1),o("ta-modal",{attrs:{"destroy-on-close":!0,width:600,title:"导出"},on:{ok:function(t){e.blnExportVisible=!1}},model:{value:e.blnExportVisible,callback:function(t){e.blnExportVisible=t},expression:"blnExportVisible"}},[o("ta-form",{attrs:{"auto-form-create":function(e){return t.exportForm=e}}},[o("ta-form-item",{attrs:{fieldDecoratorId:"operationtime",label:"操作时间",require:{message:"操作时间必选"}}},[o("ta-range-picker",{attrs:{placeholder:["开始时间","结束时间"],"allow-clear":!0}})],1),o("ta-form-item",{attrs:{fieldDecoratorId:"exportLog",label:"导出说明"}},[o("ta-textarea",{attrs:{rows:4,placeholder:"请输入"}})],1)],1),o("div",{attrs:{slot:"footer"},slot:"footer"},[o("ta-button",{on:{click:function(t){e.blnExportVisible=!1}}},[e._v("取消")]),o("ta-button",{attrs:{type:"primary"},on:{click:e.doExport}},[e._v("确定")])],1)],1),o("ta-modal",{attrs:{width:1e3,height:800,title:"操作日志","destroy-on-close":!0},on:{ok:function(t){e.blnItemLogVisible=!1}},model:{value:e.blnItemLogVisible,callback:function(t){e.blnItemLogVisible=t},expression:"blnItemLogVisible"}},[o("node-log",{attrs:{record:e.itemRecord}})],1)],1)},i=[],r=a(66347),l=a(95082),n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",t._l(t.logList,(function(e,o){return a("div",{key:o,staticStyle:{"border-bottom":"1px dashed black","padding-bottom":"10px","padding-top":"10px"}},[a("ta-row",["2"==e.type?a("ta-col",{attrs:{span:6}},[t._v("停用人:")]):t._e(),"4"==e.type?a("ta-col",{attrs:{span:6}},[t._v("启用人:")]):t._e(),"3"==e.type?a("ta-col",{attrs:{span:6}},[t._v("变更人:")]):t._e(),"1"==e.type?a("ta-col",{attrs:{span:6}},[t._v("提交人:")]):t._e(),a("ta-col",{attrs:{span:18}},[t._v(t._s(e.name))])],1),a("ta-row",["2"==e.type?a("ta-col",{attrs:{span:6}},[t._v("停用时间:")]):t._e(),"4"==e.type?a("ta-col",{attrs:{span:6}},[t._v("启用时间:")]):t._e(),"3"==e.type?a("ta-col",{attrs:{span:6}},[t._v("变更时间:")]):t._e(),"1"==e.type?a("ta-col",{attrs:{span:6}},[t._v("提交时间:")]):t._e(),a("ta-col",{attrs:{span:18}},[t._v(t._s(e.date))])],1),a("ta-row",["2"==e.type?a("ta-col",{attrs:{span:6}},[t._v("停用意见:")]):t._e(),"4"==e.type?a("ta-col",{attrs:{span:6}},[t._v("启用意见:")]):t._e(),"1"==e.type?a("ta-col",{attrs:{span:6}},[t._v("提交意见:")]):t._e(),a("ta-col",{attrs:{span:18}},[t._v(t._s(e.note))])],1),a("div",{staticStyle:{height:"20px"}})],1)})),0)},s=[],c={name:"ruleLog",props:{record:Object},data:function(){return{logList:[]}},mounted:function(){this.queryRuleLog()},methods:{queryRuleLog:function(){var t=this;Base.submit(null,{url:"mtt/localruleconfig/nodeChange/queryLogById",data:{mkename:this.record.mkename,buskey:this.record.buskey}}).then((function(e){t.logList=e.data.data}))}}},u=c,d=a(1001),p=(0,d.Z)(u,n,s,!1,null,"196b1150",null),m=p.exports,f={name:"bigRuleManage",components:{nodeLog:m},data:function(){return{ruleOptions:[],bigNameOptions:[],ruleNameOptions:[],bigRuleColumns:[{dataIndex:"ykz042",title:"节点编号",overflowTooltip:!0,align:"left",width:100},{dataIndex:"ykz010",title:"节点名称",overflowTooltip:!0,align:"left",width:200},{dataIndex:"ykz002",title:"节点类型",overflowTooltip:!0,align:"left",width:100}],bigRuleList:[],pageParam:{pageNumber:1},blnBigRuleUpdate:!1,editBigRecord:{},bigRuleVisible:!1,blnChildRuleUpdate:!1,editChildRecord:{},blnChildRuleVisible:!1,bigRuleRecord:{},editRecord:{},itemRecord:{},blnRuleLogVisible:!1,blnItemLogVisible:!1,ruleidList:[],nodeTypeList:[],blnExportVisible:!1}},mounted:function(){this.getNodeTypeList()},methods:{openExportAll:function(){this.blnExportVisible=!0},doExport:function(){var t=this;this.exportForm.validateFields((function(e){if(!e){var a=t.exportForm.getFieldsValue();a.operationtime&&2==a.operationtime.length&&(a.operationtimeStart=a.operationtime[0].format("YYYY-MM-DD"),a.operationtimeEnd=a.operationtime[1].format("YYYY-MM-DD 23:59:59")),Base.downloadFile({method:"post",url:"mtt/localruleconfig/nodeChange/exportNodes",options:(0,l.Z)({},a),fileName:"节点变更_"+a.operationtime[0].format("YYYYMMDD")+"_"+a.operationtime[1].format("YYYYMMDD")+".dat"}).then((function(e){t.$message.success("下载成功")})).catch((function(e){t.$message.error("下载失败")}))}}))},getNodeTypeList:function(){var t=this;this.Base.submit(null,{url:"mtt/localruleconfig/localCotent/getNodeTypeList"}).then((function(e){var a,o=(0,r.Z)(e.data.list);try{for(o.s();!(a=o.n()).done;){var i=a.value;i.showName="".concat(i.ykz002,"(").concat(i.ykz063,")")}}catch(l){o.e(l)}finally{o.f()}t.nodeTypeList=e.data.list}))},doSearch:function(){var t=this;this.searchForm.validateFields((function(e){e||t.$refs.pager.loadData((function(t){var e,a=t.data.pageBean.list,o=(0,r.Z)(a);try{for(o.s();!(e=o.n()).done;){var i=e.value;i.columns.push({dataIndex:"changetype",title:"操作类型",overflowTooltip:!0,align:"center",width:100}),i.columns.push({dataIndex:"operate",title:"操作",overflowTooltip:!0,scopedSlots:{customRender:"operate"},align:"center",width:100})}}catch(l){o.e(l)}finally{o.f()}}))}))},openRuleLog:function(t){this.editRecord=t,this.blnRuleLogVisible=!0},openItemLog:function(t,e){this.itemRecord=e,this.blnItemLogVisible=!0},ruleNameSearch:function(){var t=this;Base.submit(null,{url:"bigRuleManage/listAAA167"}).then((function(e){e.data.success&&(t.ruleNameOptions=e.data.list)}))},pageParams:function(){var t=this.searchForm.getFieldsValue();return t.operationtime&&2==t.operationtime.length&&(t.operationtimeStart=t.operationtime[0].format("YYYY-MM-DD"),t.operationtimeEnd=t.operationtime[1].format("YYYY-MM-DD 23:59:59")),(0,l.Z)({},t)},resetFields:function(){this.searchForm.resetFields()}}},g=f,b=(0,d.Z)(g,o,i,!1,null,"5ef0c7c2",null),h=b.exports}}]);
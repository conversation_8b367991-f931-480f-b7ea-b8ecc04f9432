"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[7617],{88412:function(t,e,a){var n=a(26263),i=a(36766),r=a(1001),o=(0,r.Z)(i.Z,n.s,n.x,!1,null,"5e7ef0ae",null);e["Z"]=o.exports},92378:function(t,e,a){a.d(e,{Z:function(){return f}});var n=function(){var t=this,e=this,a=e.$createElement,n=e._self._c||a;return n("div",[n("ta-modal",{attrs:{title:"备案理由",visible:e.visible,height:550,width:1e3,"mask-closable":!1,draggable:!0},on:{ok:e.handleSave,cancel:e.handleClose}},[n("div",{attrs:{slot:"footer"},slot:"footer"},[n("ta-button",{style:{marginRight:8},attrs:{type:"primary"},on:{click:e.handleSave}},[e._v(" 保存 ")]),n("ta-button",{on:{click:e.handleClose}},[e._v(" 取消 ")]),n("ta-button",{on:{click:e.saveASTemp}},[e._v(" 保存为备案理由 ")])],1),n("ta-menu",{attrs:{mode:"horizontal"},model:{value:e.current,callback:function(t){e.current=t},expression:"current"}},[n("ta-menu-item",{key:"reason",on:{click:e.queryTemplateDatas}},[e._v(" 备案理由模板 ")]),n("ta-menu-item",{key:"record",on:{click:e.queryRecords}},[e._v(" 历史备案记录 ")])],1),n("ta-big-table",{directives:[{name:"show",rawName:"v-show",value:"record"===e.current[0],expression:"current[0] === 'record'"}],staticStyle:{width:"100%"},attrs:{border:"","highlight-hover-row":"","show-overflow":"",data:e.dataSource1,height:"300",resizable:!0,"auto-resize":"",size:"mini"}},[n("ta-big-table-column",{attrs:{type:"seq",fixed:"left",width:"50px",title:" ",align:"center"}}),n("ta-big-table-column",{attrs:{field:"aaz213",title:"管理Id",visible:!1}}),n("ta-big-table-column",{attrs:{field:"aac003",title:"备案时间","min-width":"100px"}}),n("ta-big-table-column",{attrs:{field:"nl",title:"医生名字","min-width":"60px"}}),n("ta-big-table-column",{attrs:{field:"content",title:"备案理由","min-width":"100px"}}),n("ta-big-table-column",{attrs:{field:"",title:"操作",width:"100px",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("div",{staticClass:"opareteItem",on:{click:function(t){return e.useRowInfo(a)}}},[e._v(" 使用 ")])]}}])})],1),n("ta-big-table",{directives:[{name:"show",rawName:"v-show",value:"reason"===e.current[0],expression:"current[0] === 'reason'"}],staticStyle:{width:"100%"},attrs:{border:"","highlight-hover-row":"","show-overflow":"",data:e.dataSource2,height:"300",resizable:!0,"auto-resize":"",size:"mini"}},[n("ta-big-table-column",{attrs:{type:"seq",fixed:"left",width:"50px",title:" ",align:"center"}}),n("ta-big-table-column",{attrs:{field:"aaz213",title:"管理Id",visible:!1}}),n("ta-big-table-column",{attrs:{field:"id",title:"Id",visible:!1}}),n("ta-big-table-column",{attrs:{field:"content",title:"备案理由","min-width":"500px"}}),n("ta-big-table-column",{attrs:{field:"type",title:"类型","min-width":"80px",align:"center",formatter:e.formatterType}}),n("ta-big-table-column",{attrs:{field:"",title:"操作",width:"130px",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("ta-table-operate",{attrs:{"operate-menu":e.operateMenu,"row-info":t}})]}}])})],1),n("ta-form",{attrs:{"auto-form-create":function(e){return t.form=e},layout:"horizontal","form-layout":!0,"label-width":"80px",col:{xs:1,sm:1,md:1,lg:1,xl:1,xxl:1}}},[n("ta-form-item",{attrs:{label:"引导信息","field-decorator-id":"ykz018",disabled:!0}},[n("ta-textarea",{attrs:{placeholder:"请输入引导信息",rows:3,"show-length":!0}})],1),n("ta-form-item",{attrs:{label:"备案理由","field-decorator-id":"content",require:!0,"field-decorator-options":{rules:[{validator:e.validReason}]},initValue:e.changeAddContent}},[n("ta-textarea",{attrs:{placeholder:"请输入内容；不能为空；不能全是特殊字符、符号、数字或三者组合；最大长度不能超过400各字符",rows:3,"show-length":!0}})],1)],1)],1),n("ta-modal",{attrs:{title:"备案理由模板编辑",visible:e.editTempVisible,draggable:!0},on:{ok:e.editContent,cancel:e.handleEditClose}},[n("ta-form",{attrs:{"auto-form-create":function(e){return t.formEdit=e},layout:"horizontal","form-layout":!0,"label-width":"80px",col:{xs:1,sm:1,md:1,lg:1,xl:1,xxl:1}}},[n("ta-form-item",{attrs:{fieldDecoratorId:"id",hidden:"true",initValue:e.changeId}},[n("ta-input")],1),n("ta-form-item",{attrs:{"field-decorator-id":"editContent",label:"修改内容",require:!0,"field-decorator-options":{rules:[{validator:e.validReason}]},initValue:e.changeContent}},[n("ta-textarea",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入内容；不能为空；不能全是特殊字符、符号、数字或三者组合；最大长度不能超过400各字符",rows:3,"show-length":!0}})],1)],1)],1)],1)},i=[],r=a(93528),o=a(36797),l=a.n(o),s={name:"keepOnRecord",components:{},props:{visible:{type:Boolean,default:!1},params:{type:Object,default:function(){return{}}}},data:function(){var t=this;return{current:["reason"],dataSource1:[],dataSource2:[],editTempVisible:!1,changeAddContent:null,changeContent:null,changeId:null,operateMenu:[{name:"使用",onClick:function(e){t.useRowInfo(e)}},{name:"编辑",isShow:function(t){return"3"===t.type},onClick:function(e){t.editTempVisible=!0,t.changeContent=e.content,t.changeId=e.id}},{name:"删除",isShow:function(t){return"3"===t.type},onClick:function(e){t.$confirm({title:"继续操作",content:"删除不可恢复，是否继续删除?",onOk:function(){t.Base.submit(null,{url:"/nightAudit/deleteAe21",data:{id:e.id,medinscode:t.params.akb020,oprcode:t.params.aaz263}}).then((function(e){t.warnText="删除成功!",t.warnVisible=!0,t.dataSource2=e.data.data})).catch((function(e){t.warnText="删除失败!",t.warnVisible=!0})),t.warnVisible=!0},onCancel:function(){}})}}]}},watch:{visible:{handler:function(t){var e=this;this.$nextTick((function(){e.queryTemplateDatas(),e.form.setFieldsValue({ykz018:e.params.ykz018,content:e.params.aaz560})}))}}},mounted:function(){this.changeAddContent=this.params.aaz263},methods:{moment:l(),validReason:function(t,e,a){var n=this,i=/[^`~!@#$%^&*()_\-+=<>?:"{}|,.\/;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘'，。、\w\r\n\s]/g;e?e.match(i)?parseInt(e)<=0?a([{message:"备案理由的长度不能超过400"}]):(this.$nextTick((function(){n.form.setFieldsValue({duration:parseInt(e)})})),a()):a([{message:"备案理由不能为空；不能全是特殊字符、符号、数字或三者组合"}]):a([{message:"请输入备案理由"}])},queryRecords:function(){r.Z.queryKeepRecords({},(function(t){}))},queryTemplateDatas:function(){var t=this,e={aaz263:this.params.aaz263,akb020:this.params.akb020};r.Z.queryTemplateDatas(e,(function(e){t.dataSource2=e.data.data}))},useRowInfo:function(t){this.form.setFieldsValue(t)},handleSave:function(){var t=this;this.form.validateFields((function(e){e||(t.$emit("handleSave",t.form.getFieldsValue()),t.form.resetFields())}))},editContent:function(){var t=this;this.formEdit.validateFields((function(e){e||(t.Base.submit(null,{url:"/nightAudit/updateAe21",data:{content:t.formEdit.getFieldsValue().editContent,id:t.formEdit.getFieldsValue().id,medinscode:t.params.akb020,oprcode:t.params.aaz263}}).then((function(e){t.warnText="更新成功!",t.warnVisible=!0,t.dataSource2=e.data.data,t.formEdit.resetFields(),t.editTempVisible=!1})).catch((function(e){t.warnText="更新失败!",t.warnVisible=!0})),t.warnVisible=!0)}))},handleClose:function(){this.current=["reason"],this.form.resetFields(),this.$emit("handleClose")},handleEditClose:function(){this.formEdit.resetFields(),this.editTempVisible=!1},saveASTemp:function(){var t=this;this.form.validateFields((function(e){if(!e){var a={medinscode:t.params.akb020,content:t.form.getFieldsValue().content,oprcode:t.params.aaz263,oprname:t.params.aac003,oprdptcode:t.params.aaz307,oprdptname:t.params.aae386,type:"3"};r.Z.saveAe21(a,(function(e){t.dataSource2=e.data.data}))}}))},formatterType:function(t){var e=t.cellValue;return"1"===e?"公共模板":"2"===e?"科室模板":"个人模板"}}},c=s,u=a(1001),d=(0,u.Z)(c,n,i,!1,null,"4f8b1a2a",null),f=d.exports},18338:function(t,e,a){var n=a(69204),i=a(47788),r=(a(47808),a(1001)),o=(0,r.Z)(i.Z,n.s,n.x,!1,null,"44a57520",null);e["Z"]=o.exports},80506:function(t,e,a){var n=a(45606),i=a(44441),r=a(1001),o=(0,r.Z)(i.Z,n.s,n.x,!1,null,"14432a73",null);e["Z"]=o.exports},47808:function(t,e,a){a(80992)},36766:function(t,e,a){var n=a(66586);e["Z"]=n.Z},47788:function(t,e,a){var n=a(75138);e["Z"]=n.Z},44441:function(t,e,a){var n=a(82754);e["Z"]=n.Z},26263:function(t,e,a){a.d(e,{s:function(){return n},x:function(){return i}});var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},i=[]},69204:function(t,e,a){a.d(e,{s:function(){return n},x:function(){return i}});var n=function(){var t=this,e=this,a=e.$createElement,n=e._self._c||a;return n("ta-modal",{attrs:{title:"医保诊断编码补充",visible:e.visible,height:574,width:1e3,"mask-closable":!1,"destroy-on-close":!0,draggable:!0},on:{ok:e.handleSave,cancel:e.handleClose}},[n("div",{attrs:{slot:"footer"},slot:"footer"},[n("ta-button",{style:{marginRight:8},attrs:{type:"primary"},on:{click:e.handleSave}},[e._v(" 保存 ")]),n("ta-button",{on:{click:e.handleClose}},[e._v(" 取消 ")])],1),n("ta-menu",{attrs:{"default-selected-keys":[0],mode:"horizontal"}},e._l(e.ykz042Arr,(function(t,a){return n("ta-menu-item",{key:a,on:{click:function(a){return e.queryTemplateDatas(t)}}},[n("span",[e._v(e._s(e.ykz010Arr[a]))])])})),1),n("ta-input-search",{staticStyle:{width:"260px"},attrs:{placeholder:"请输入诊断名称关键字筛选",size:"small"},on:{search:e.onSearch}}),n("ta-big-table",{staticStyle:{width:"100%"},attrs:{border:"","highlight-hover-row":"","show-overflow":"",data:e.dataSource2,height:"300",resizable:!0,"auto-resize":"",size:"mini"}},[n("ta-big-table-column",{attrs:{type:"seq",fixed:"left",width:"50px",title:" ",align:"center"}}),n("ta-big-table-column",{attrs:{field:"aka120",title:"ICD编码",width:"200px"}}),n("ta-big-table-column",{attrs:{field:"aka121",title:"医保诊断名称 ","min-width":"300px"}}),n("ta-big-table-column",{attrs:{field:"",title:"操作",width:"100px",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("div",{directives:[{name:"show",rawName:"v-show",value:!e.rowInfoArr.includes(a.aka120),expression:"!rowInfoArr.includes(row.aka120)"}],staticClass:"opareteItem",on:{click:function(t){return e.useRowInfo(a)}}},[n("a",[e._v("补充")])]),n("div",{directives:[{name:"show",rawName:"v-show",value:e.rowInfoArr.includes(a.aka120),expression:"rowInfoArr.includes(row.aka120)"}],staticClass:"opareteItem",on:{click:function(t){return e.cancelRowInfo(a)}}},[n("a",{staticStyle:{color:"red"}},[e._v("取消")])])]}}])}),n("template",{slot:"bottomBar"},[n("ta-pagination",{ref:"gridPager",staticStyle:{"text-align":"right"},attrs:{defaultPageSize:200,pageSizeOptions:["30","50","100","200","500"],"data-source":e.dataSource2,params:e.knowledgePageParams,url:"nightAudit/getKnowledgeContentTableData"},on:{"update:dataSource":function(t){e.dataSource2=t},"update:data-source":function(t){e.dataSource2=t}}})],1)],2),n("ta-form",{attrs:{"auto-form-create":function(e){return t.form=e},layout:"horizontal","form-layout":!0,"label-width":"80px",col:{xs:1,sm:1,md:1,lg:1,xl:1,xxl:1}}},[n("ta-form-item",{attrs:{label:"引导信息","field-decorator-id":"ykz018",disabled:!0}},[n("ta-textarea",{attrs:{placeholder:"请输入引导信息",rows:3,"show-length":!0}})],1),n("ta-form-item",{attrs:{label:"已补诊断","field-decorator-id":"content","field-decorator-options":{rules:[{validator:e.validReason}]}}},[n("ta-textarea",{attrs:{placeholder:"请输入内容；不能为空；不能全是特殊字符、符号、数字或三者组合；最大长度不能超过400各字符",rows:3,"show-length":!0}})],1)],1)],1)},i=[]},45606:function(t,e,a){a.d(e,{s:function(){return n},x:function(){return i}});var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",t._l(t.tagList,(function(e){return a("ta-tag",{key:e.label,attrs:{color:e.checked?e.color2:e.color1},on:{click:function(a){return t.handleChange(e)}}},[a("span",{staticClass:"tag-icon"},[t._v(t._s(e.icon))]),a("span",{staticClass:"tag-label"},[t._v(" "+t._s(e.label)+" ")])])})),1)},i=[]},80992:function(){},66586:function(t,e){var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};e["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}},75138:function(t,e,a){var n=a(89584),i=a(93528),r=a(36797),o=a.n(r);e["Z"]={name:"keepOnRecord",components:{},props:{visible:{type:Boolean,default:!1},params:{type:Object,default:function(){return{}}},ykz042Arr:{type:[Array],default:function(){return[]}},ykz010Arr:{type:[Array],default:function(){return[]}}},data:function(){return{dataSource1:[],dataSource2:[],ykz042:"",content:[],aka120List:[],aka121List:[],serchFil:"",timePick:0,rowInfoArr:[]}},watch:{visible:{handler:function(t){var e=this;this.$nextTick((function(){e.queryTemplateDatas(e.ykz042Arr[0]),e.form.setFieldsValue({ykz018:e.params.ykz018,content:e.params.aaz560}),e.content=[]}))}}},mounted:function(){},methods:{moment:o(),validReason:function(t,e,a){var n=this,i=/[^`~!@#$%^&*()_\-+=<>?:"{}|,.\/;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘'，。、\w\r\n\s]/g;e?e.match(i)?parseInt(e)<=0?a([{message:"备案理由的长度不能超过400"}]):(this.$nextTick((function(){n.form.setFieldsValue({duration:parseInt(e)})})),a()):a([{message:"备案理由不能为空；不能全是特殊字符、符号、数字或三者组合"}]):a([{message:"请输入备案理由"}])},onSearch:function(t){var e=this;this.serchFil=t,this.$nextTick((function(){e.$refs.gridPager.loadData((function(t){}))}))},queryRecords:function(){i.Z.queryKeepRecords({},(function(t){}))},queryTemplateDatas:function(t){this.ykz042=t,this.$refs.gridPager.loadData((function(t){}))},knowledgePageParams:function(){var t={fillDiagFlag:"0",searchText:this.serchFil,ykz042:this.ykz042};return t},useRowInfo:function(t){0===this.content.length&&this.content.push("已补充诊断："),this.rowInfoArr.push(t.aka120),this.aka120List.push(t.aka120),this.aka121List.push(t.aka121),this.content.push(t.aka120+" 【"+t.aka121+"】;"),this.form.setFieldsValue({content:this.content.join("")})},cancelRowInfo:function(t){var e=new Set(this.rowInfoArr);e.delete(t.aka120),this.rowInfoArr=(0,n.Z)(e);var a=new Set(this.content);a.delete(t.aka120+" 【"+t.aka121+"】;"),this.content=(0,n.Z)(a),1===this.content.length&&(this.content=[]),this.form.setFieldsValue({content:this.content.join("")})},handleSave:function(){var t=this.form.getFieldsValue();t.aka121=this.aka121List,t.aka120=this.aka120List,this.$emit("handleSave",t),this.form.resetFields()},handleClose:function(){this.serchFil="",this.form.resetFields(),this.$emit("handleClose")},fnReset:function(){this.rowInfoArr=[],this.content=[]}}}},82754:function(t,e,a){var n=a(7485);e["Z"]={name:"tagChange",props:{tagValue:{type:String},statusArr:{type:Array,default:function(){return n.H}}},data:function(){return{tagList:this.statusArr}},watch:{tagValue:function(t){this.changeTag(t)}},mounted:function(){this.changeTag(this.tagValue)},methods:{changeTag:function(t){this.tagList=this.tagList.map((function(e){return e.value===t?e.checked=!0:e.checked=!1,e}))},handleChange:function(t){this.$emit("change",t)}}}},93528:function(t,e){var a="nightAudit/";e["Z"]={getBasePath:function(){return faceConfig.basePath+a},queryTableData:function(t,e){Base.submit(null,{url:a+"queryPatients",data:t,autoQs:!1},{successCallback:function(t){return e(t)}})},queryTableDataForTongJi:function(t,e){Base.submit(null,{url:a+"queryPatientList",data:t,autoQs:!1},{successCallback:function(t){return e(t)}})},updatePatientInfo:function(t,e){Base.submit(null,{url:a+"saveOperation",data:t,autoQs:!1},{successCallback:function(t){return e(t)}})},getRowDetails:function(t,e){Base.submit(null,{url:a+"queryAdviceDetail",data:t,autoQs:!1},{successCallback:function(t){return e(t)}})},queryKeepRecords:function(t,e){Base.submit(null,{url:a+"getRecordFormwork",data:t},{successCallback:function(t){return e(t)}})},queryTemplateDatas:function(t,e){Base.submit(null,{url:a+"getRecordFormwork",data:t},{successCallback:function(t){return e(t)}})},saveAe21:function(t,e){Base.submit(null,{url:a+"saveAe21",data:t,autoQs:!1},{successCallback:function(t){return e(t)}})},queryRuleInfo:function(t,e){Base.submit(null,{url:"/mtt/api/ruleSearch/queryRule",data:t,autoQs:!1},{successCallback:function(t){return e(t)}})},queryPagePackageRule:function(t,e){Base.submit(null,{url:"/mtt/api/ruleSearch/pagePackageContent",data:t,autoQs:!1},{successCallback:function(t){return e(t)}})},checkPromptState:function(t,e){Base.submit(null,{url:a+"checkPromptState",data:t,autoQs:!1},{successCallback:function(t){return e(t)}})},checkHandleState:function(t,e){Base.submit(null,{url:a+"checkHandleState",data:t,autoQs:!1},{successCallback:function(t){return e(t)}})}}},7485:function(t,e,a){a.d(e,{H:function(){return n}});var n=[{value:"1",label:"待审批",checked:!0,color1:"orange",color2:"#FCB76B"},{value:"2",label:"审批通过未导出",checked:!1,color1:"green",color2:"#87d068"},{value:"3",label:"审批通过已导出",checked:!1,color1:"purple",color2:"#7d02d1"},{value:"4",label:"审批驳回待提交",checked:!1,color1:"red",color2:"#fa0000"},{value:"5",label:"全部",checked:!1,color1:"blue",color2:"#2db7f5"}]}}]);
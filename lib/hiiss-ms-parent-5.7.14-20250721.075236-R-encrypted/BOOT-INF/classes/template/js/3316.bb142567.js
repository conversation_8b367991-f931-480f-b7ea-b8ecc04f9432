"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[3316],{72049:function(t,e,a){a.d(e,{Z:function(){return c}});var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",t._l(t.logList,(function(e,o){return a("div",{key:o,staticStyle:{"border-bottom":"1px dashed black","padding-bottom":"10px","padding-top":"10px"}},[a("ta-row",["2"==e.type?a("ta-col",{attrs:{span:6}},[t._v("停用人:")]):t._e(),"4"==e.type?a("ta-col",{attrs:{span:6}},[t._v("启用人:")]):t._e(),"3"==e.type?a("ta-col",{attrs:{span:6}},[t._v("变更人:")]):t._e(),"1"==e.type?a("ta-col",{attrs:{span:6}},[t._v("提交人:")]):t._e(),a("ta-col",{attrs:{span:18}},[t._v(t._s(e.name))])],1),a("ta-row",["2"==e.type?a("ta-col",{attrs:{span:6}},[t._v("停用时间:")]):t._e(),"4"==e.type?a("ta-col",{attrs:{span:6}},[t._v("启用时间:")]):t._e(),"3"==e.type?a("ta-col",{attrs:{span:6}},[t._v("变更时间:")]):t._e(),"1"==e.type?a("ta-col",{attrs:{span:6}},[t._v("提交时间:")]):t._e(),a("ta-col",{attrs:{span:18}},[t._v(t._s(e.date))])],1),a("ta-row",["2"==e.type?a("ta-col",{attrs:{span:6}},[t._v("停用意见:")]):t._e(),"4"==e.type?a("ta-col",{attrs:{span:6}},[t._v("启用意见:")]):t._e(),"3"==e.type?a("ta-col",{attrs:{span:6}},[t._v("修改意见:")]):t._e(),"1"==e.type?a("ta-col",{attrs:{span:6}},[t._v("提交意见:")]):t._e(),a("ta-col",{attrs:{span:18}},[t._v(t._s(e.note))])],1),a("div",{staticStyle:{height:"20px"}})],1)})),0)},l=[],r={name:"ruleLog",props:{record:Object},data:function(){return{logList:[]}},mounted:function(){this.queryRuleLog()},methods:{queryRuleLog:function(){var t=this;Base.submit(null,{url:"mtt/localruleconfig/ruleQuery/queryMkf75Log",data:{ykz277:this.record.ykz277,ake001:this.record.ake001}}).then((function(e){t.logList=e.data.data}))}}},i=r,n=a(1001),s=(0,n.Z)(i,o,l,!1,null,"36b52390",null),c=s.exports},37585:function(t,e,a){a.d(e,{Z:function(){return c}});var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",t._l(t.logList,(function(e,o){return a("div",{key:o,staticStyle:{"border-bottom":"1px dashed black","padding-bottom":"10px","padding-top":"10px"}},[a("ta-row",["2"==e.type?a("ta-col",{attrs:{span:6}},[t._v("停用人:")]):t._e(),"4"==e.type?a("ta-col",{attrs:{span:6}},[t._v("启用人:")]):t._e(),"3"==e.type?a("ta-col",{attrs:{span:6}},[t._v("明细更新人:")]):t._e(),a("ta-col",{attrs:{span:6}},[t._v(t._s(e.name))])],1),a("ta-row",["2"==e.type?a("ta-col",{attrs:{span:6}},[t._v("停用时间:")]):t._e(),"4"==e.type?a("ta-col",{attrs:{span:6}},[t._v("启用时间:")]):t._e(),"3"==e.type?a("ta-col",{attrs:{span:6}},[t._v("明细更新时间:")]):t._e(),a("ta-col",{attrs:{span:6}},[t._v(t._s(e.date))])],1),a("ta-row",["2"==e.type?a("ta-col",{attrs:{span:6}},[t._v("停用意见:")]):t._e(),"4"==e.type?a("ta-col",{attrs:{span:6}},[t._v("启用意见:")]):t._e(),"3"==e.type?a("ta-col",{attrs:{span:6}},[t._v("明细更新意见:")]):t._e(),a("ta-col",{attrs:{span:6}},[t._v(t._s(e.note))])],1)],1)})),0)},l=[],r={name:"ruleLog",props:{record:Object},data:function(){return{logList:[]}},mounted:function(){this.queryRuleLog()},methods:{queryRuleLog:function(){var t=this;Base.submit(null,{url:"mtt/localruleconfig/ruleQuery/queryLogById",data:{id:this.record.ykz276}}).then((function(e){t.logList=e.data.data}))}}},i=r,n=a(1001),s=(0,n.Z)(i,o,l,!1,null,"8218fb22",null),c=s.exports},93316:function(t,e,a){a.r(e),a.d(e,{default:function(){return p}});var o=function(){var t=this,e=this,a=e.$createElement,o=e._self._c||a;return o("ta-border-layout",{attrs:{"show-padding":!1,layout:{footer:"0px"}}},[o("ta-card",{attrs:{bordered:!1}},[o("div",{staticClass:"title"},[e._v("查询条件")]),o("ta-row",[o("ta-form",{attrs:{autoFormCreate:function(e){t.searchForm=e},"form-layout":!0}},[o("ta-form-item",{attrs:{fieldDecoratorId:"operationtime",span:4,label:"操作时间",require:{message:"操作时间不能为空"}}},[o("ta-range-picker",{attrs:{placeholder:["开始时间","结束时间"],"allow-clear":!0}})],1),o("ta-form-item",{attrs:{fieldDecoratorId:"ykz018",span:4,label:"知识元"}},[o("ta-input",{attrs:{placeholder:"请输入"}})],1),o("ta-form-item",{attrs:{"field-decorator-id":"ape801",span:4,label:"规则大类"}},[o("ta-select",{attrs:{"show-search":!0,"allow-clear":!0,placeholder:"请选择"}},e._l(e.bigRuleList,(function(t,a){return o("ta-select-option",{key:a,attrs:{value:t.ape801}},[e._v(" "+e._s(t.aaa166)+" ")])})),1)],1),o("ta-form-item",{attrs:{fieldDecoratorId:"ykz227",span:4,label:"险种"}},[o("ta-select",{attrs:{"collection-type":"MYKZ227",allowClear:!0,"show-search":!0}})],1),o("ta-form-item",{attrs:{fieldDecoratorId:"ykz248",span:4,label:"就医方式"}},[o("ta-select",{attrs:{"collection-type":"MYKZ248",allowClear:!0,"show-search":!0}})],1),o("ta-form-item",{attrs:{span:4,label:"监控场景",fieldDecoratorId:"aae501"}},[o("ta-select",{attrs:{placeholder:"请选择",allowClear:!0,"show-search":!0}},e._l(e.aae501List,(function(t,a){return o("ta-select-option",{key:a,attrs:{value:t}},[e._v(" "+e._s(t)+" ")])})),1)],1),o("ta-form-item",{attrs:{span:4,label:"引擎号",fieldDecoratorId:"ruleid"}},[o("ta-select",{attrs:{placeholder:"请选择",allowClear:!0,"show-search":!0,dropdownMatchSelectWidth:!1}},e._l(e.ruleidList,(function(t,a){return o("ta-select-option",{key:a,attrs:{value:t.ruleid}},[e._v(" "+e._s(t.ruleid+":"+t.ruleidlog)+" ")])})),1)],1),o("ta-form-item",{attrs:{span:4,label:"三目编码",fieldDecoratorId:"ake001"}},[o("ta-input",{attrs:{placeholder:"请输入"}})],1),o("ta-form-item",{attrs:{span:4,label:"三目名称",fieldDecoratorId:"ake002"}},[o("ta-input",{attrs:{placeholder:"请输入"}})],1),o("ta-form-item",{attrs:{span:4}},[o("ta-button",{staticStyle:{"margin-left":"50px"},attrs:{span:2},on:{click:e.resetFields}},[e._v("重置")]),o("ta-button",{attrs:{type:"primary"},on:{click:e.doSearch}},[e._v("查询")])],1)],1)],1)],1),o("ta-card",{attrs:{bordered:!1}},[o("div",{staticClass:"title"},[e._v("规则列表"),o("ta-button",{staticStyle:{float:"right","margin-right":"10px"},attrs:{type:"primary"},on:{click:e.openExportAll}},[e._v("全量导出")])],1),o("ta-table",{attrs:{size:"small","have-sn":!0,columns:e.bigRuleColumns,"data-source":e.dataList,scroll:{x:"100%",y:420}},scopedSlots:e._u([{key:"operate",fn:function(t,a){return o("div",{},[o("a",{on:{click:function(t){return e.openRuleLog(a)}}},[e._v("日志")])])}},{key:"expandedRowRender",fn:function(t){return o("ta-table",{attrs:{size:"small",columns:e.itemColumns,dataSource:t.itemList,scroll:{x:"100%",y:200}},scopedSlots:e._u([{key:"operate",fn:function(a,l){return o("div",{},[o("a",{on:{click:function(a){return e.openItemLog(t,l)}}},[e._v("日志")])])}}])})}}])}),o("div",[o("ta-pagination",{ref:"pager",staticClass:"page",attrs:{defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"],dataSource:e.dataList,params:e.pageParams,url:"mtt/localruleconfig/ruleChange/queryRulePage"},on:{"update:dataSource":function(t){e.dataList=t},"update:data-source":function(t){e.dataList=t}},model:{value:e.pageParam.pageNumber,callback:function(t){e.$set(e.pageParam,"pageNumber",t)},expression:"pageParam.pageNumber"}})],1)],1),o("ta-modal",{attrs:{"destroy-on-close":!0,width:1e3,height:800,title:"操作日志"},on:{ok:function(t){e.blnRuleLogVisible=!1}},model:{value:e.blnRuleLogVisible,callback:function(t){e.blnRuleLogVisible=t},expression:"blnRuleLogVisible"}},[o("rule-log",{attrs:{record:e.ruleRecord}})],1),o("ta-modal",{attrs:{"destroy-on-close":!0,width:1e3,height:800,title:"操作日志"},on:{ok:function(t){e.blnItemLogVisible=!1}},model:{value:e.blnItemLogVisible,callback:function(t){e.blnItemLogVisible=t},expression:"blnItemLogVisible"}},[o("rule-item-log",{attrs:{record:e.itemRecord}})],1),o("ta-modal",{attrs:{"destroy-on-close":!0,width:600,title:"导出"},on:{ok:function(t){e.blnExportVisible=!1}},model:{value:e.blnExportVisible,callback:function(t){e.blnExportVisible=t},expression:"blnExportVisible"}},[o("ta-form",{attrs:{"auto-form-create":function(e){return t.exportForm=e}}},[o("ta-form-item",{attrs:{fieldDecoratorId:"operationtime",label:"操作时间",require:{message:"操作时间必选"}}},[o("ta-range-picker",{attrs:{placeholder:["开始时间","结束时间"],"allow-clear":!0}})],1),o("ta-form-item",{attrs:{fieldDecoratorId:"exportLog",label:"导出说明"}},[o("ta-textarea",{attrs:{rows:4,placeholder:"请输入"}})],1)],1),o("div",{attrs:{slot:"footer"},slot:"footer"},[o("ta-button",{on:{click:function(t){e.blnExportVisible=!1}}},[e._v("取消")]),o("ta-button",{attrs:{type:"primary"},on:{click:e.doExport}},[e._v("确定")])],1)],1)],1)},l=[],r=a(95082),i=a(37585),n=a(72049),s={name:"bigRuleManage",components:{ruleLog:i.Z,ruleItemLog:n.Z},data:function(){return{ruleOptions:[],bigNameOptions:[],ruleNameOptions:[],aae501List:[],bigRuleColumns:[{dataIndex:"ykz277",title:"规则id",overflowTooltip:!0,align:"left",width:100},{dataIndex:"ykz018",title:"知识元",overflowTooltip:!0,align:"left",width:200},{dataIndex:"aaa166",title:"规则大类",overflowTooltip:!0,align:"left",width:100},{dataIndex:"ykz227",title:"险种",collectionType:"MYKZ227",overflowTooltip:!0,align:"left",width:100},{dataIndex:"ykz248",title:"就医方式",collectionType:"MYKZ248",overflowTooltip:!0,align:"left",width:100},{title:"监控场景",dataIndex:"aae501",width:150,overflowTooltip:!0},{title:"引擎号",dataIndex:"ruleidlog",width:200,overflowTooltip:!0},{dataIndex:"changetypes",title:"操作类型",overflowTooltip:!0,align:"left",width:100},{dataIndex:"operate",title:"操作",overflowTooltip:!0,scopedSlots:{customRender:"operate"},align:"center",width:100}],dataList:[],bigRuleList:[],pageParam:{pageNumber:1},itemColumns:[{dataIndex:"ake001",title:"三目编码",overflowTooltip:!0,align:"left",width:200},{dataIndex:"ake002",title:"三目名称",overflowTooltip:!0,align:"left",width:200},{dataIndex:"ape864",title:"分组编号",overflowTooltip:!0,align:"left",width:100},{dataIndex:"ape865",title:"分组名称",overflowTooltip:!0,align:"left",width:100},{dataIndex:"ape891",title:"频次(乘法)",overflowTooltip:!0,align:"left",width:100},{dataIndex:"ape892",title:"频次(除法)",overflowTooltip:!0,align:"left",width:100},{dataIndex:"ake126",title:"计价单位",overflowTooltip:!0,align:"left",width:100},{dataIndex:"changetypes",title:"操作类型",overflowTooltip:!0,align:"left",width:100},{dataIndex:"operate",title:"操作",overflowTooltip:!0,scopedSlots:{customRender:"operate"},align:"center",width:100}],blnBigRuleUpdate:!1,editBigRecord:{},bigRuleVisible:!1,blnChildRuleUpdate:!1,editChildRecord:{},blnChildRuleVisible:!1,bigRuleRecord:{},ruleRecord:{},itemRecord:{},blnRuleLogVisible:!1,blnItemLogVisible:!1,ruleidList:[],blnExportVisible:!1}},mounted:function(){this.queryBigRule(),this.queryAae501List(),this.ruleNameSearch(),this.listRuleid()},methods:{doSearch:function(){var t=this;this.searchForm.validateFields((function(e){e||t.$refs.pager.loadData()}))},openRuleLog:function(t){this.ruleRecord=t,this.blnRuleLogVisible=!0},openItemLog:function(t,e){this.itemRecord=e,this.blnItemLogVisible=!0},queryAae501List:function(){var t=this;Base.submit(null,{url:"mtt/localruleconfig/ruleQuery/getAae501List"}).then((function(e){t.aae501List=e.data.list}))},queryBigRule:function(){var t=this;Base.submit(null,{url:"mttRuleConfig/listBigRule"}).then((function(e){t.bigRuleList=e.data.list}))},listRuleid:function(){var t=this;Base.submit(null,{url:"mtt/localruleconfig/ruleQuery/getRuleidList"}).then((function(e){t.ruleidList=e.data.list}))},ruleNameSearch:function(){},pageParams:function(){var t=this.searchForm.getFieldsValue();return t.operationtime&&2==t.operationtime.length&&(t.operationtimeStart=t.operationtime[0].format("YYYY-MM-DD"),t.operationtimeEnd=t.operationtime[1].format("YYYY-MM-DD 23:59:59")),(0,r.Z)({},t)},resetFields:function(){this.searchForm.resetFields()},openExportAll:function(){this.blnExportVisible=!0},doExport:function(){var t=this;this.exportForm.validateFields((function(e){if(!e){var a=t.exportForm.getFieldsValue();a.operationtime&&2==a.operationtime.length&&(a.operationtimeStart=a.operationtime[0].format("YYYY-MM-DD"),a.operationtimeEnd=a.operationtime[1].format("YYYY-MM-DD 23:59:59")),Base.downloadFile({method:"post",url:"mtt/localruleconfig/ruleChange/exportRules",options:(0,r.Z)({},a),fileName:"规则变更_"+a.operationtime[0].format("YYYYMMDD")+"_"+a.operationtime[1].format("YYYYMMDD")+".dat"}).then((function(e){t.$message.success("下载成功")})).catch((function(e){t.$message.error("下载失败")}))}}))}}},c=s,d=a(1001),u=(0,d.Z)(c,o,l,!1,null,"f7ffbdde",null),p=u.exports}}]);
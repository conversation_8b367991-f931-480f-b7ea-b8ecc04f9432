"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[5412],{80715:function(e,t,a){a.d(t,{Z:function(){return _}});var r,o,i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("ta-select",{attrs:{mode:"multiple",placeholder:e.placeholder,options:e.CollectionData(e.collection),disabled:e.disabled,"allow-clear":e.allowClear},on:{change:e.handleChange},model:{value:e.selectValue,callback:function(t){e.selectValue=t},expression:"selectValue"}})},s=[],l=a(98754),n=a(41538),d={name:"SelectMultiple",props:["collection","value","disabled","placeholder","allowClear"],data:function(){return{selectValue:[]}},watch:{value:{immediate:!0,handler:function(e,t){(0,l.Z)(e)&&""!==e.trim()?this.selectValue=e.split(","):this.selectValue=[]}}},methods:{handleChange:function(e){(0,n.Z)(e)||(e=[]),this.$emit("input",e.join(",")),this.$emit("change",e.join(","))}}},c=d,u=a(1001),m=(0,u.Z)(c,i,s,!1,null,"5c5ae317",null),f=m.exports,h=a(72610),p=(a(26227),a(8145)),g={ADD:"ADD",EDIT:"EDIT",SHOW:"SHOW"},v={components:{selectMultiple:f,taSensitiveInput:h.Z},props:{renderType:{type:String,default:g.ADD},renderProp:{type:Object,default:function(){return{}}},showValues:{type:Object,default:function(){return{}}},simpleShowSlot:{type:Array,default:function(){return[]}},formSetting:{required:!0,type:Object,default:function(){return{}}},isShowParentItem:{type:Boolean,default:!0}},data:function(){return{}},methods:{buildItemShowContext:function(e){var t=this,a=this.$slots.default,r=this.formSetting.formItem,o=this.formSetting.formId||"",i=this.showValues[o];switch(e){case"slot":return-1===this.simpleShowSlot.indexOf(o)?a:i;case"select":case"radio":case"radioButton":return this.CollectionLabel(r.collection,i);case"select-multiple":return(0,l.Z)(i)?i.split(",").map((function(e){return t.CollectionLabel(r.collection,e)})).join(","):i;case"sensitive-input":var s=(0,l.Z)(i)?JSON.parse(i):i;return(0,p.Z)(s)?s.sensitiveField:i;default:return i}},buildItemContext:function(e){var t,a=this.$createElement,r=this.$slots.default,o=this.formSetting.disabled,i=this.formSetting.formItem,s=this.formSetting.formId,l=this.formSetting.label,n=this.formSetting.placeholder,d=this.renderType,c=this.renderProp;switch(e){case"slot":return r;case"select":return a("ta-select",{attrs:{placeholder:n,disabled:o,allowClear:!0,"collection-type":i.collection}});case"select-multiple":return a("select-multiple",{attrs:{placeholder:n,collection:i.collection,disabled:o,allowClear:!0}});case"radio":return a("ta-radio-group",{attrs:{disabled:o,"collection-type":i.collection}});case"radioButton":return a("ta-radio-group",{class:"lalal",attrs:{buttonStyle:"solid",disabled:o}},[null===(t=this.CollectionData(i.collection))||void 0===t?void 0:t.map((function(e){var t=e.label,r=e.value;return a("ta-radio-button",{key:r,attrs:{value:r}},[t])}))]);case"sensitive-input":return a("ta-sensitive-input",{attrs:{inputKey:s,placeholder:n,description:l,"auth-user":d===g.EDIT,authRequest:c.authRequest}});default:return a("ta-input",{attrs:{placeholder:n,disabled:o}})}}},render:function(){var e=arguments[0],t=this.renderType,a=this.formSetting.class||"",r=this.formSetting.formId||"",o=this.formSetting.label,i=this.formSetting.decoratorOptions;if(i&&i.rules){var s=i.rules;s.map((function(e){"number"===e.type&&(e.transform=function(e){return Number(e)},e.message="请输入数字")}))}var l=this.formSetting.formItemLayout,n=this.formSetting.formItem,d=!1!==this.formSetting.display;return d||(a+=" displayNone"),"pResourceName"!==this.formSetting.formId||this.isShowParentItem||(a+=" displayNone"),t===g.SHOW?e("ta-form-item",{attrs:{label:o,className:a,labelCol:l.labelCol,wrapperCol:l.wrapperCol}},[this.buildItemShowContext(n.type)]):e("ta-form-item",{attrs:{label:o,labelCol:l.labelCol,wrapperCol:l.wrapperCol,fieldDecoratorId:r,fieldDecoratorOptions:i},class:a},[this.buildItemContext(n.type)])}},b=v,y=(0,u.Z)(b,r,o,!1,null,"7322d522",null),_=y.exports},66965:function(e,t,a){a.d(t,{Z:function(){return m}});var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("ta-drawer",{attrs:{title:"权限代理",placement:"right",closable:!0,visible:e.visible,destroyOnClose:"",width:"500px"},on:{close:e.closeDrawer}},[a("div",{staticClass:"fit"},[a("ta-row",{staticClass:"fit"},[a("ta-border-layout",{staticStyle:{"border-right-width":"7px"},attrs:{layout:{header:"70px"}}},[a("template",{slot:"header"},[a("div",[e._v("代理角色名称："),a("strong",[e._v(e._s(e.delegateUser))])]),a("div",[e._v("委派截止日期："),a("strong",[e._v(e._s(e.delegateExpireDate))])])]),a("ta-tabs",{staticClass:"fit",attrs:{defaultActiveKey:"1"}},[a("ta-tab-pane",{key:"1",staticStyle:{"padding-top":"10px"},attrs:{tab:"功能菜单权限"}},[a("ta-table",{attrs:{columns:e.menuAuthorityColumns,dataSource:e.repermissionsData,defaultExpandAllRows:"",size:"small",pagination:!1}})],1),a("ta-tab-pane",{key:"2",staticStyle:{"padding-top":"10px"},attrs:{tab:"自定义资源权限"}},[a("ta-table",{attrs:{columns:e.customAuthorityColumns,dataSource:e.customRepermissionsData,defaultExpandAllRows:"",size:"small",pagination:!1}})],1)],1)],2)],1)],1)])},o=[],i=a(46979),s=[{title:"功能名称",dataIndex:"resourceName"}],l=[{title:"功能名称",dataIndex:"resourceName"}],n={name:"showAgentAuthority",props:["visible","rowData"],data:function(){return{menuAuthorityColumns:s,repermissionsData:[],customAuthorityColumns:l,customRepermissionsData:[],delegateUser:"",delegateExpireDate:""}},watch:{visible:function(e){var t=this;e&&i.Z.queryExamineDetail({examineId:this.rowData.id},(function(e){t.delegateUser=e.data.after["delegateUser"],t.delegateExpireDate=e.data.after["delegateExpireDate"];e.data.after["sourceRoleId"];t.repermissionsData=e.data.after["repermissions"],t.customRepermissionsData=e.data.after["customRepermissions"]}))}},methods:{closeDrawer:function(){this.repermissionsData=[],this.customRepermissionsData=[],this.$emit("close")}}},d=n,c=a(1001),u=(0,c.Z)(d,r,o,!1,null,null,null),m=u.exports},6782:function(e,t,a){a.d(t,{Z:function(){return c}});var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("ta-drawer",{attrs:{title:"批量操作代理角色",width:"500",placement:"right",closable:!0,visible:e.visible,destroyOnClose:""},on:{close:e.closeEdit}},[a("div",{staticStyle:{padding:"30px 30px 0 50px","overflow-y":"auto"},domProps:{innerHTML:e._s(this.msg)}})])},o=[],i=a(46979),s={name:"showAgentAuthorityBatch",props:["visible","rowData"],data:function(){return{form:null,formData:{},msg:""}},watch:{visible:function(e){e&&this.setValue()}},methods:{setValue:function(){var e=this;this.msg="",i.Z.queryExamineDetail({examineId:this.rowData.id},(function(t){e.msg="<table class='gridtable' style='width: 100%; height: 100%'><tr><th>代理角色名称</th><th>组织path</th></tr>";for(var a=0;a<t.data.before.length;a++)e.msg=e.msg+"<tr><td>"+t.data.before[a].roleName+" </td><td>"+t.data.before[a].orgNamePath+"</td></tr>";e.msg=e.msg+"</table>"}))},closeEdit:function(){this.$emit("close"),this.formData={}}}},l=s,n=a(1001),d=(0,n.Z)(l,r,o,!1,null,null,null),c=d.exports},18357:function(e,t,a){a.d(t,{Z:function(){return c}});var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("ta-drawer",{attrs:{title:"批量授权/批量收回",placement:"right",closable:!0,visible:e.visible,destroyOnClose:"",width:"500px"},on:{close:e.closeDrawer}},[a("div",[a("ta-alert",{staticClass:"notice-box",attrs:{message:"当前批量操作的角色为: "+e.roleNames,type:"info",showIcon:""}}),a("ta-e-tree",{ref:"tree",attrs:{data:e.treeData,"show-checkbox":"","highlight-current":"",props:e.defaultProps,"node-key":"resourceId","default-checked-keys":e.defaultCheckedList,"default-expand-all":""}})],1)])},o=[],i=a(46979),s={name:"showUsePermissionAuthority",props:["visible","rowData"],data:function(){return{roleNames:"",defaultProps:{children:"children",label:"name",id:"resourceId",disabled:"resourceId"},defaultCheckedList:[],customRePermissions:[],treeData:[]}},watch:{visible:function(e){var t=this;e&&i.Z.queryExamineDetail({examineId:this.rowData.id},(function(e){var a=e.data.after["checkedList"];t.roleNames=e.data.after["roleNames"],t.treeData=e.data.after["rePermissions"],t.$nextTick((function(){var e=[];a.forEach((function(a,r){var o=t.$refs.tree.getNode(a);null!=o&&o.isLeaf&&e.push(o.data.resourceId)})),t.defaultCheckedList=e}))}))}},methods:{closeDrawer:function(){this.$emit("close")}}},l=s,n=a(1001),d=(0,n.Z)(l,r,o,!1,null,null,null),c=d.exports},52782:function(e,t,a){a.d(t,{Z:function(){return m}});var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("ta-drawer",{attrs:{title:"权限复制",placement:"right",closable:!0,visible:e.visible,destroyOnClose:"",width:"500px"},on:{close:e.closeDrawer}},[a("div",{staticClass:"fit"},[a("ta-row",{staticClass:"fit"},[a("ta-border-layout",{staticStyle:{"border-right-width":"7px"},attrs:{layout:{header:"70px"}}},[a("template",{slot:"header"},[a("div",[e._v("原角色："),a("strong",[e._v(e._s(e.sourceRoleName))])]),a("div",[e._v(" 目标角色："),a("strong",[e._v(e._s(e.targetRoleName))])])]),a("ta-tabs",{staticClass:"fit",attrs:{defaultActiveKey:"1"}},[a("ta-tab-pane",{key:"1",staticStyle:{"padding-top":"10px"},attrs:{tab:"功能菜单权限"}},[a("ta-table",{attrs:{columns:e.menuAuthorityColumns,dataSource:e.menuAuthorityData,defaultExpandAllRows:"",size:"small",pagination:!1}})],1),a("ta-tab-pane",{key:"2",staticStyle:{"padding-top":"10px"},attrs:{tab:"自定义对象权限"}},[a("ta-table",{attrs:{columns:e.customAuthorityColumns,dataSource:e.customAuthorityData,defaultExpandAllRows:"",size:"small",pagination:!1}})],1)],1)],2)],1)],1)])},o=[],i=a(46979),s=[{title:"功能名称",dataIndex:"resourceName"},{title:"有效期",dataIndex:"effectTime",customRender:function(e){return e?e.split(" ")[0]:"永久"}}],l=[{title:"功能名称",dataIndex:"resourceName"},{title:"有效期",dataIndex:"effectTime",customRender:function(e){return e?e.split(" ")[0]:"永久"}}],n={name:"showCopyResource",props:["visible","rowData"],data:function(){return{menuAuthorityColumns:s,menuAuthorityData:[],customAuthorityColumns:l,customAuthorityData:[],sourceRoleName:"",targetRoleName:""}},watch:{visible:function(e){var t=this;e&&i.Z.queryExamineDetail({examineId:this.rowData.id},(function(e){t.sourceRoleName=e.data.before["sourceRoleName"],t.targetRoleName=e.data.before["targetRoleName"];e.data.before["sourceRoleId"];t.menuAuthorityData=e.data.after["menuAuthorityData"],t.customAuthorityData=e.data.after["customAuthorityData"]}))}},methods:{closeDrawer:function(){this.menuAuthorityData=[],this.customAuthorityData=[],this.$emit("close")}}},d=n,c=a(1001),u=(0,c.Z)(d,r,o,!1,null,null,null),m=u.exports},67111:function(e,t,a){a.d(t,{Z:function(){return c}});var r=function(){var e=this,t=this,a=t.$createElement,r=t._self._c||a;return r("ta-drawer",{attrs:{title:"角色复制",placement:"right",closable:!0,visible:t.visible,destroyOnClose:"",width:"500px"},on:{close:t.closeDrawer}},[r("ta-form",{attrs:{autoFormCreate:function(t){e.form=t}}},[r("ta-form-item",{attrs:{label:"原角色名称",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol}},[t._v(" "+t._s(t.formData.roleName)+" ")]),r("ta-form-item",{attrs:{label:"新角色名称",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol}},[t._v(" "+t._s(t.formData.newRoleName)+" ")]),r("ta-form-item",{attrs:{label:"新角色组织",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol}},[t._v(" "+t._s(t.formData.orgNamePath)+" ")])],1)],1)},o=[],i=a(46979),s={name:"showCopyRole",props:["visible","rowData"],data:function(){return{formItemLayout:{labelCol:{span:6},wrapperCol:{span:18}},formData:{}}},watch:{visible:function(e){var t=this;e&&i.Z.queryExamineDetail({examineId:this.rowData.id},(function(e){t.formData=e.data.before}))}},methods:{closeDrawer:function(){this.$emit("close")}}},l=s,n=a(1001),d=(0,n.Z)(l,r,o,!1,null,"22ae4552",null),c=d.exports},99325:function(e,t,a){a.d(t,{Z:function(){return c}});var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("ta-drawer",{attrs:{title:"管理员自定义权限授权&再授权查看",placement:"right",closable:!0,visible:e.visible,destroyOnClose:"",width:"500px"},on:{close:e.closeDrawer}},[a("div",[a("ta-alert",{staticClass:"notice-box",attrs:{message:"当前角色为: "+e.roleName,type:"info",showIcon:""}}),a("p",[e._v("授权后的权限为：")]),a("div",{staticClass:"divide-equally",staticStyle:{width:"100%",border:"1px solid #e6f7ff","overflow-x":"scroll"}},[a("p",[a("strong",[e._v("授权权限")])]),a("ta-e-tree",{ref:"tree",attrs:{data:e.treeData,"highlight-current":"",props:e.defaultProps,"node-key":"resourceId"}})],1),a("div",{staticClass:"divide-equally",staticStyle:{width:"100%",border:"1px solid #e6f7ff","overflow-x":"scroll"}},[a("p",[a("strong",[e._v("再授权权限")])]),a("ta-e-tree",{ref:"tree",attrs:{data:e.treeData2,"highlight-current":"",props:e.defaultProps,"node-key":"resourceId"}})],1)],1)])},o=[],i=a(46979),s={name:"showCustomResourceAuthority",props:["visible","rowData"],data:function(){return{roleName:"",defaultProps:{children:"children",label:"name",isLeaf:"isLeaf",id:"resourceId",disabled:"resourceId"},treeData:[],treeData2:[]}},watch:{visible:function(e){var t=this;e&&i.Z.queryExamineDetail({examineId:this.rowData.id},(function(e){t.treeData=e.data.after["rePerList"],t.treeData2=e.data.after["reAuthList"],t.roleName=e.data.after["roleName"]}))}},methods:{closeDrawer:function(){this.$emit("close"),this.treeData=[]}}},l=s,n=a(1001),d=(0,n.Z)(l,r,o,!1,null,"1032cf0a",null),c=d.exports},72470:function(e,t,a){a.d(t,{Z:function(){return c}});var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("ta-drawer",{attrs:{title:"自定义对象权限",placement:"right",closable:!0,visible:e.visible,destroyOnClose:"",width:"500px"},on:{close:e.closeDrawer}},[a("div",[a("ta-alert",{staticClass:"notice-box",attrs:{message:"当前角色为: "+e.roleName,type:"info",showIcon:""}}),a("p",[e._v("授权后的权限为：")]),a("div",{staticStyle:{border:"1px solid #e6f7ff","overflow-x":"auto"}},[a("ta-e-tree",{ref:"tree",attrs:{data:e.treeData,"highlight-current":"",props:e.defaultProps,"node-key":"resourceId","default-expand-all":""}})],1)],1)])},o=[],i=a(46979),s={name:"showCustomResourceUsePermission",props:["visible","rowData"],data:function(){return{roleName:"",defaultProps:{children:"children",label:"resourceName",id:"resourceId",disabled:"resourceId"},treeData:[]}},watch:{visible:function(e){var t=this;e&&i.Z.queryExamineDetail({examineId:this.rowData.id},(function(e){t.roleName=e.data.after["roleName"],t.treeData=e.data.after["list"]}))}},methods:{closeDrawer:function(){this.$emit("close"),this.treeData=[]}}},l=s,n=a(1001),d=(0,n.Z)(l,r,o,!1,null,null,null),c=d.exports},4036:function(e,t,a){a.d(t,{Z:function(){return c}});var r=function(){var e=this,t=this,a=t.$createElement,r=t._self._c||a;return r("ta-drawer",{attrs:{title:"新增组织范围权限",width:"500",placement:"right",closable:!0,visible:t.visible,destroyOnClose:""},on:{close:t.closeDrawer}},[r("ta-alert",{staticClass:"notice-box",attrs:{message:"当前角色为："+t.roleName,type:"info",showIcon:""}}),r("ta-form",{attrs:{autoFormCreate:function(t){e.form=t}}},[r("ta-form-item",{attrs:{label:"组织名称",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol}},[t._v(" "+t._s(t.formData.orgName)+" ")]),r("ta-form-item",{attrs:{label:"组织路径",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol}},[t._v(" "+t._s(t.formData.namePath)+" ")])],1)],1)},o=[],i=a(46979),s={name:"showOrgScope",props:["visible","rowData"],data:function(){return{formItemLayout:{labelCol:{span:6},wrapperCol:{span:18}},formData:{},roleName:""}},watch:{visible:function(e){var t=this;e&&i.Z.queryExamineDetail({examineId:this.rowData.id},(function(e){t.formData=e.data.before,t.roleName=e.data.before["roleName"]}))}},methods:{closeDrawer:function(){this.$emit("close")}}},l=s,n=a(1001),d=(0,n.Z)(l,r,o,!1,null,null,null),c=d.exports},41643:function(e,t,a){a.d(t,{Z:function(){return c}});var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("ta-drawer",{attrs:{title:"更新权限有效时间",width:"500",placement:"right",closable:!0,visible:e.visible,destroyOnClose:""},on:{close:e.closeEdit}},[a("div",{staticStyle:{padding:"30px 30px 0 50px","overflow-y":"auto"},domProps:{innerHTML:e._s(this.msg)}})])},o=[],i=a(46979),s={name:"showPermissionEffectiveTimeBatch",props:["visible","rowData"],data:function(){return{msg:""}},watch:{visible:function(e){e&&this.setValue()}},methods:{setValue:function(){var e=this;this.msg="",i.Z.queryExamineDetail({examineId:this.rowData.id},(function(t){e.msg="<table class='gridtable' style='width: 100%; height: 100%'><tr><th>资源名称</th><th>有效时间</th></tr>";for(var a=0;a<t.data.after.length;a++)e.msg=e.msg+"<tr><td>"+t.data.after[a].resourceName+" </td><td>"+t.data.after[a].effectTime+"</td></tr>";e.msg=e.msg+"</table>"}))},closeEdit:function(){this.$emit("close")}}},l=s,n=a(1001),d=(0,n.Z)(l,r,o,!1,null,null,null),c=d.exports},72331:function(e,t,a){a.d(t,{Z:function(){return c}});var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("ta-drawer",{attrs:{title:"管理员授权&再授权查看",placement:"right",closable:!0,visible:e.visible,destroyOnClose:"",width:"500px"},on:{close:e.closeDrawer}},[a("div",[a("ta-alert",{staticClass:"notice-box",attrs:{message:"当前角色为: "+e.roleName,type:"info",showIcon:""}}),a("div",{staticClass:"divide-equally"},[a("p",[a("strong",[e._v("授权权限")])]),a("ta-e-tree",{ref:"tree",attrs:{data:e.treeData,"show-checkbox":"","highlight-current":"",props:e.defaultProps,"node-key":"resourceId","default-checked-keys":e.oneDefaultCheckedList,"default-expand-all":""}})],1),a("div",{staticClass:"divide-equally"},[a("p",[a("strong",[e._v("再授权权限")])]),a("ta-e-tree",{ref:"tree",attrs:{data:e.treeData,"show-checkbox":"","highlight-current":"",props:e.defaultProps,"node-key":"resourceId","default-checked-keys":e.twoDefaultCheckedList,"default-expand-all":""}})],1)],1)])},o=[],i=a(46979),s={name:"showUsePermissionAuthority",props:["visible","rowData"],data:function(){return{roleName:"",defaultProps:{children:"children",label:"name",isLeaf:"isLeaf",id:"resourceId",disabled:"resourceId"},defaultCheckedList:[],customRePermissions:[],treeData:[],oneDefaultCheckedList:[],twoDefaultCheckedList:[]}},watch:{visible:function(e){var t=this;e&&i.Z.queryExamineDetail({examineId:this.rowData.id},(function(e){var a=e.data.after["rePermissionList"],r=e.data.after["reAuthorityList"];t.roleName=e.data.after["roleName"],t.treeData=e.data.after["resourceTree"],t.$nextTick((function(){var e=[];a.forEach((function(a,r){var o=t.$refs.tree.getNode(a);o.isLeaf&&e.push(o.data.resourceId)})),t.oneDefaultCheckedList=e})),t.$nextTick((function(){var e=[];r.forEach((function(a,r){var o=t.$refs.tree.getNode(a);o.isLeaf&&e.push(o.data.resourceId)})),t.twoDefaultCheckedList=e}))}))}},methods:{closeDrawer:function(){this.$emit("close"),this.treeData=[],this.oneDefaultCheckedList=[],this.twoDefaultCheckedList=[]}}},l=s,n=a(1001),d=(0,n.Z)(l,r,o,!1,null,"0a3ab093",null),c=d.exports},13946:function(e,t,a){a.d(t,{Z:function(){return c}});var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("ta-drawer",{attrs:{title:"相似权限",placement:"right",closable:!0,visible:e.visible,destroyOnClose:"",width:"500px"},on:{close:e.closeDrawer}},[a("div",[a("ta-alert",{staticClass:"notice-box",attrs:{message:"当前角色为: "+e.roleName,type:"info",showIcon:""}}),a("ta-e-tree",{ref:"tree",attrs:{load:e.handleLoadTreeNode,"show-checkbox":"","highlight-current":"",props:e.defaultProps,"node-key":"resourceId","default-checked-keys":e.defaultCheckedList,"default-expanded-keys":e.defaultExpandedNode,lazy:""}})],1)])},o=[],i=a(46979),s={name:"showSimilarAuthority",props:["visible","rowData"],data:function(){return{roleName:"",defaultProps:{children:"children",label:"name",isLeaf:"isLeaf",id:"resourceId",disabled:"resourceId"},defaultCheckedList:[],defaultExpandedNode:[],customRePermissions:[]}},watch:{visible:function(e){var t=this;e&&i.Z.queryExamineDetail({examineId:this.rowData.id},(function(e){t.roleName=e.data.before["roleName"],t.defaultCheckedList=e.data.before["resource"]}))}},methods:{closeDrawer:function(){this.$emit("close")},handleLoadTreeNode:function(e,t){var a=this;if(0===e.level&&i.Z.querySimilarAuthority(null,(function(e){return a.defaultExpandedNode=[e.customRePermissions[0].resourceId],a.customRePermissions=e.customRePermissions,t(e.customRePermissions)})),e.level>=1){var r=e.data.resourceId,o={resourceId:r};i.Z.querySimilarAuthority(o,(function(e){var a=e.customRePermissions;if(a[0].children)return t(a[0].children)}))}}}},l=s,n=a(1001),d=(0,n.Z)(l,r,o,!1,null,null,null),c=d.exports},69593:function(e,t,a){a.d(t,{Z:function(){return c}});var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("ta-drawer",{attrs:{title:"功能菜单权限",placement:"right",closable:!0,visible:e.visible,destroyOnClose:"",width:"500px"},on:{close:e.closeDrawer}},[a("div",[a("ta-alert",{staticClass:"notice-box",attrs:{message:"当前角色为: "+e.roleName,type:"info",showIcon:""}}),a("ta-e-tree",{ref:"tree",attrs:{data:e.treeData,"show-checkbox":"","highlight-current":"",props:e.defaultProps,"node-key":"resourceId","default-checked-keys":e.defaultCheckedList,"default-expand-all":""}})],1)])},o=[],i=a(46979),s={name:"showUsePermissionAuthority",props:["visible","rowData"],data:function(){return{roleName:"",defaultProps:{children:"children",label:"name",isLeaf:"isLeaf",id:"resourceId",disabled:"resourceId"},defaultCheckedList:[],treeData:[]}},watch:{visible:function(e){var t=this;e&&i.Z.queryExamineDetail({examineId:this.rowData.id},(function(e){var a=e.data.after["checkedList"];t.roleName=e.data.after["roleName"],t.treeData=e.data.after["rePermissions"],t.$nextTick((function(){var e=[];a.forEach((function(a,r){var o=t.$refs.tree.getNode(a);o.isLeaf&&e.push(o.data.resourceId)})),t.defaultCheckedList=e}))}))}},methods:{closeDrawer:function(){this.$emit("close"),this.treeData=[],this.defaultCheckedList=[]}}},l=s,n=a(1001),d=(0,n.Z)(l,r,o,!1,null,null,null),c=d.exports},64672:function(e,t,a){a.d(t,{Z:function(){return c}});var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("ta-drawer",{attrs:{title:"更新组织详情",width:"600",placement:"right",closable:!0,visible:e.visible,destroyOnClose:""},on:{close:e.closeEdit}},[a("div",{staticStyle:{padding:"10px 10px 0 20px","overflow-y":"auto"},domProps:{innerHTML:e._s(this.msg)}})])},o=[],i=a(46979),s={name:"showOrg",props:["visible","rowData","tags"],data:function(){return{msg:""}},watch:{visible:function(e){var t=this;if(e){var a={orgName:"组织机构名称",namePath:"组织机构路径",orgType:"组织类型",effective:"有效标识",customNo:"自定义编码",orderNo:"排序号",orgCode:"组织代码",tel:"联系电话",address:"联系地址",createTime:"创建时间"},r={pOrgName:"上级组织机构",contactsName:"联系人",orgManagerName:"负责人",areaValue:"所属行政区",orgTags:"组织标签"},o=new Set(Object.keys(a));o.add("parentId").add("contacts").add("orgManager").add("area").add("tags"),o.add("areaValue").add("pOrgName").add("contactsName").add("orgManagerName").add("orgTags"),i.Z.queryOrgSettingTable((function(e){for(var t=e.resultData,r=0;r<t.length;r++)"1"==t[r].effective&&"0"==t[r].hide&&(o.has(t[r].fieldId)||(a[t[r].fieldId]=t[r].displayText))})),i.Z.queryExamineDetail({examineId:this.rowData.id},(function(e){var o=e.data.before.org,i=e.data.after.org;t.msg="<table class='gridtable' style='width: 100%; height: 100%'><tr><th>数据项</th><th>原数据</th><th>更新后数据</th></tr>",Object.keys(a).forEach((function(e){var r="",s="";"orgType"==e?(r=t.CollectionLabel("orgType",o[e]),s=t.CollectionLabel("orgType",i[e])):"effective"==e?(r="1"==o[e]?"有效":"无效",s="1"==i[e]?"有效":"无效"):(r=void 0==o[e]?"":o[e],s=void 0==i[e]?"":i[e]),t.msg=r!=s?t.msg+'<tr class="trstess"><td>'+a[e]+"</td><td>"+r+" </td><td>"+s+"</td></tr>":t.msg+"<tr ><td>"+a[e]+"</td><td>"+r+" </td><td>"+s+"</td></tr>"})),Object.keys(r).forEach((function(a){var o="",i="";if("orgTags"==a)for(var s in t.tags){var l=t.tags[s];e.data.before.orgTags.indexOf(l.tagId)>-1&&(o=o+l.tagName+"、"),e.data.after.orgTags.indexOf(l.tagId)>-1&&(i=i+l.tagName+"、")}else o=e.data.before[a],i=e.data.after[a];void 0==o&&(o=""),void 0==i&&(i=""),o.toString()!=i.toString()?t.msg=t.msg+'<tr class="trstess"><td>'+r[a]+"</td><td>"+o+" </td><td>"+i+"</td></tr>":t.msg=t.msg+"<tr><td>"+r[a]+"</td><td>"+o+" </td><td>"+i+"</td></tr>"})),t.msg=t.msg+"</table>"}))}}},methods:{closeEdit:function(){this.$emit("close")}}},l=s,n=a(1001),d=(0,n.Z)(l,r,o,!1,null,"0581a5b4",null),c=d.exports},34278:function(e,t,a){a.d(t,{Z:function(){return h}});var r=function(){var e=this,t=this,a=t.$createElement,r=t._self._c||a;return r("ta-drawer",{attrs:{title:"组织详情",width:"500",placement:"right",closable:!0,visible:t.visible,destroyOnClose:""},on:{close:t.closeEdit}},[r("ta-form",{attrs:{autoFormCreate:function(t){e.form=t}}},[r("taContainerMask",{attrs:{show:t.editLoading}}),t._l(t.formNormalShowSettings,(function(e){return[r("renderFormItem",{key:e.id,attrs:{formSetting:e,isShow:!0,showValues:t.formData,simpleShowSlot:t.simpleShowSlot,renderType:"SHOW"}},["effective"==e.id?[t._v(t._s(t.formData.effective?"有效":"无效"))]:"orgManager"==e.id?[t._v(t._s(t.formData.orgManagerName))]:"contacts"==e.id?[t._v(t._s(t.formData.contactsName))]:"tags"==e.id?t._l(t.tags,(function(e){return[t.selectedTags.indexOf(e.tagId)>-1?r("ta-tag",{key:e.tagId,staticClass:"tag-select"},[t._v(t._s(e.tagName))]):t._e()]})):t._e()],2)]})),t.formMoreShowSettings.length>0?r("ta-collapse",{attrs:{bordered:!1}},[r("ta-collapse-panel",{key:"1",staticStyle:{border:"none"},attrs:{header:"更多组织信息"}},[t._l(t.formMoreShowSettings,(function(e){return[r("renderFormItem",{key:e.id,attrs:{formSetting:e,isShow:!0,showValues:t.formData,simpleShowSlot:t.simpleShowSlot,renderType:"SHOW"}},["effective"==e.id?[t._v(t._s(t.formData.effective?"有效":"无效"))]:"orgManager"==e.id?[t._v(t._s(t.formData.orgManagerName))]:"contacts"==e.id?[t._v(t._s(t.formData.contactsName))]:"tags"==e.id?t._l(t.tags,(function(e){return[t.selectedTags.indexOf(e.tagId)>-1?r("ta-tag",{key:e.tagId,staticClass:"tag-select"},[t._v(t._s(e.tagName))]):t._e()]})):t._e()],2)]}))],2)],1):t._e()],2)],1)},o=[],i=a(46979),s=a(80715),l=a(17973),n=["porgId","orgId"],d=["areaValue"],c={name:"showOrgAdd",props:["visible","rowData","tags","chooseResult"],components:{renderFormItem:s.Z},mixins:[l.Z],data:function(){return{form:null,formData:{},simpleShowSlot:d,selectedTags:[],editLoading:!1}},computed:{formNormalShowSettings:function(){return this.formNormalSettings.filter((function(e){return-1==n.indexOf(e.id)}))||[]},formMoreShowSettings:function(){return this.formMoreSettings.filter((function(e){return-1==n.indexOf(e.id)}))||[]}},mounted:function(){var e=this;this.editLoading=!0,i.Z.queryExamineDetail({examineId:this.rowData.id},(function(t){var a=t.data[e.chooseResult],r=a.orgTags,o=a.pOrgName,i=a.orgManagerName,s=a.contactsName,l=t.data[e.chooseResult].org,n=l.parentId,d=l.effective,c=(l.orgName,l.orgId,l.orgType,l.customNo,l.orgCode,l.tel,l.address,l.orderNo);e.selectedTags=r;var u=t.data[e.chooseResult].org;Object.keys(u).forEach((function(t){e.formData[t]=u[t]})),Object.assign(e.formData,{porgId:n,pOrgName:o,areaValue:t.data[e.chooseResult].areaValue,effective:"1"==d,orderNo:c.toString(),orgManagerName:i,contactsName:s}),e.buildForm(e.formData),e.editLoading=!1}))},methods:{closeEdit:function(){this.$emit("close"),this.editLoading=!1,this.formData={}}}},u=c,m=a(1001),f=(0,m.Z)(u,r,o,!1,null,"530a9a1b",null),h=f.exports},40286:function(e,t,a){a.d(t,{Z:function(){return c}});var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("ta-drawer",{attrs:{title:"批量操作组织详情",width:"500",placement:"right",closable:!0,visible:e.visible,destroyOnClose:""},on:{close:e.closeEdit}},[a("div",{staticStyle:{padding:"30px 30px 0 50px","overflow-y":"auto"},domProps:{innerHTML:e._s(this.msg)}})])},o=[],i=a(46979),s={name:"showOrgBatch",props:["visible","rowData","tags"],data:function(){return{form:null,formData:{},msg:""}},watch:{visible:function(e){e&&this.setValue()}},methods:{setValue:function(){var e=this;this.msg="",i.Z.queryExamineDetail({examineId:this.rowData.id},(function(t){e.msg="<table class='gridtable' style='width: 100%; height: 100%'><tr><th>组织名</th><th>组织path</th></tr>";for(var a=0;a<t.data.before.length;a++)e.msg=e.msg+"<tr><td>"+t.data.before[a].orgName+" </td><td>"+t.data.before[a].namePath+"</td></tr>";e.msg=e.msg+"</table>"}))},closeEdit:function(){this.$emit("close"),this.formData={}}}},l=s,n=a(1001),d=(0,n.Z)(l,r,o,!1,null,"3cb910b7",null),c=d.exports},66921:function(e,t,a){a.d(t,{Z:function(){return c}});var r=function(){var e=this,t=this,a=t.$createElement,r=t._self._c||a;return r("ta-drawer",{attrs:{title:"角色信息",placement:"right",closable:!0,visible:t.visible,destroyOnClose:"",width:"500px"},on:{close:t.closeDrawer}},[r("ta-form",{attrs:{autoFormCreate:function(t){e.form=t}}},[r("ta-form-item",{attrs:{label:"角色名称",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol}},[t._v(" "+t._s(t.formData.roleName)+" ")]),r("ta-form-item",{attrs:{label:"所属组织",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol}},[t._v(" "+t._s(t.formData.namePath)+" ")]),r("ta-form-item",{attrs:{label:"有效时间",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol}},[t._v(" "+t._s(t.formData.effectiveTime)+" ")]),r("ta-form-item",{attrs:{label:"角色类型",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol}},[t._v(" "+t._s(t.CollectionLabel("ROLESIGN",t.formData.roleSign))+" ")]),r("ta-form-item",{attrs:{label:"角色描述",labelCol:t.formItemLayout.labelCol,wrapperCol:t.formItemLayout.wrapperCol}},[t._v(" "+t._s(t.formData.roleDesc)+" ")])],1)],1)},o=[],i=a(46979),s={name:"showAddRole",props:["visible","rowData","chooseRoleResult","adminRole"],data:function(){return{formItemLayout:{labelCol:{span:6},wrapperCol:{span:18}},formData:{}}},watch:{visible:function(e){var t=this;e&&i.Z.queryExamineDetail({examineId:this.rowData.id},(function(e){t.formData=e.data[t.chooseRoleResult][0],t.adminRole&&(t.formData["roleSign"]="管理员角色")}))}},methods:{closeDrawer:function(){this.$emit("close")}}},l=s,n=a(1001),d=(0,n.Z)(l,r,o,!1,null,null,null),c=d.exports},55553:function(e,t,a){a.d(t,{Z:function(){return c}});var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("ta-drawer",{attrs:{title:"角色信息",placement:"right",closable:!0,visible:e.visible,destroyOnClose:"",width:"500px"},on:{close:e.closeDrawer}},[a("div",{staticStyle:{padding:"10px 10px 0 20px","overflow-y":"auto"},domProps:{innerHTML:e._s(this.msg)}})])},o=[],i=a(46979),s={name:"showAddRole",props:["visible","rowData","chooseRoleResult","adminRole"],data:function(){return{msg:""}},watch:{visible:function(e){var t=this;e&&i.Z.queryExamineDetail({examineId:this.rowData.id},(function(e){var a={roleName:"角色名称",namePath:"所属组织",effectiveTime:"有效时间",roleSign:"角色类型",roleDesc:"角色描述"};t.msg="<table class='gridtable' style='width: 100%; height: 100%'><tr><th>数据项</th><th>原数据</th><th>更新后数据</th></tr>",Object.keys(a).forEach((function(r){var o="",i="";if("roleSign"==r){if(o=t.CollectionLabel("ROLESIGN",void 0==e.data.before[r]?"":e.data.before[r]),i=t.CollectionLabel("ROLESIGN",void 0==e.data.after[r]?"":e.data.after[r]),t.adminRole)return}else o=void 0==e.data.before[r]?"":e.data.before[r],i=void 0==e.data.after[r]?"":e.data.after[r];t.msg=o!=i?t.msg+'<tr class="trstess"><td>'+a[r]+"</td><td>"+o+" </td><td>"+i+"</td></tr>":t.msg+"<tr><td>"+a[r]+"</td><td>"+o+" </td><td>"+i+"</td></tr>"})),t.msg=t.msg+"</table>"}))}},methods:{closeDrawer:function(){this.$emit("close")}}},l=s,n=a(1001),d=(0,n.Z)(l,r,o,!1,null,null,null),c=d.exports},24740:function(e,t,a){a.d(t,{Z:function(){return c}});var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("ta-drawer",{attrs:{title:e.userRoleVisible?"人员角色详情":"批量操作角色详情",width:"500",placement:"right",closable:!0,visible:e.visible,destroyOnClose:""},on:{close:e.closeEdit}},[a("div",{directives:[{name:"show",rawName:"v-show",value:e.userRoleVisible,expression:"userRoleVisible"}]},[a("ta-alert",{staticClass:"notice-box",attrs:{message:"当前人员为："+e.userName,type:"info",showIcon:""}})],1),a("div",{staticStyle:{padding:"30px 30px 0 50px","overflow-y":"auto"},domProps:{innerHTML:e._s(this.msg)}})])},o=[],i=a(46979),s={name:"showRoleBatch",props:["visible","rowData","tags","userRoleVisible"],data:function(){return{form:null,formData:{},msg:"",userName:""}},watch:{visible:function(e){e&&this.setValue()}},methods:{setValue:function(){var e=this;this.msg="",i.Z.queryExamineDetail({examineId:this.rowData.id},(function(t){e.msg="<table class='gridtable' style='width: 100%; height: 100%'><tr><th>角色名称</th><th>所在组织</th></tr>";for(var a=0;a<t.data.before.length;a++)e.msg=e.msg+"<tr><td>"+t.data.before[a].roleName+" </td><td>"+t.data.before[a].namePath+"</td></tr>";e.msg=e.msg+"</table>",e.userRoleVisible&&(e.userName=t.data.before[0].userName)}))},closeEdit:function(){this.$emit("close"),this.formData={}}}},l=s,n=a(1001),d=(0,n.Z)(l,r,o,!1,null,"149b8708",null),c=d.exports},88728:function(e,t,a){a.d(t,{Z:function(){return h}});var r=function(){var e=this,t=this,a=t.$createElement,r=t._self._c||a;return r("ta-drawer",{attrs:{title:"个人信息",placement:"right",closable:!0,visible:t.visible,destroyOnClose:"",width:"500px"},on:{close:t.closeDrawer}},[r("ta-form",{attrs:{autoFormCreate:function(t){e.form=t}}},[t._l(t.formNormalShowSettings,(function(e){return[r("renderFormItem",{key:e.id,attrs:{formSetting:e,isShow:!0,showValues:t.formData,simpleShowSlot:t.simpleShowSlot,renderType:"SHOW"}},["avatar"==e.id?r("div",{staticClass:"pos-avatar"},[t.imageUrl&&t.imageUrl.length?r("div",{staticClass:"img-avatar",style:{backgroundImage:"url("+t.imageUrl+")"}}):r("i",{staticClass:"anticon anticon-user icon-upload"})]):"orgIdShow"==e.id?[t._v(t._s(t.formData.namePath))]:"tags"==e.id?t._l(t.tags,(function(e){return[t.selectedTags.indexOf(e.tagid)>-1?r("ta-tag",{key:e.tagid,staticClass:"tag-select"},[t._v(t._s(e.tagname))]):t._e()]})):t._e()],2)]})),t.formMoreShowSettings.length>0?r("ta-collapse",{attrs:{bordered:!1}},[r("ta-collapse-panel",{key:"1",staticStyle:{border:"none"},attrs:{header:"更多个人信息"}},[t._l(t.formMoreShowSettings,(function(e){return[r("renderFormItem",{key:e.id,attrs:{formSetting:e,isShow:!0,showValues:t.formData,simpleShowSlot:t.simpleShowSlot,renderType:"SHOW"}},["avatar"==e.id?r("div",{staticClass:"pos-avatar"},[t.imageUrl&&t.imageUrl.length?r("div",{staticClass:"img-avatar",style:{backgroundImage:"url("+t.imageUrl+")"}}):r("i",{staticClass:"anticon anticon-user icon-upload"})]):"orgIdShow"==e.id?[t._v(t._s(t.formData.namePath))]:"tags"==e.id?t._l(t.tags,(function(e){return[t.selectedTags.indexOf(e.tagid)>-1?r("ta-tag",{key:e.tagid,staticClass:"tag-select"},[t._v(t._s(e.tagname))]):t._e()]})):t._e()],2)]}))],2)],1):t._e()],2)],1)},o=[],i=a(46979),s=a(80715),l=a(87508),n=["password","password_2"],d=["loginId"],c={name:"showAddUser",props:["visible","rowData","chooseUserResult"],components:{renderFormItem:s.Z},mixins:[l.Z],data:function(){return{formData:{},simpleShowSlot:d,imageUrl:"",tags:[],selectedTags:[]}},computed:{formNormalShowSettings:function(){return this.formNormalSettings.filter((function(e){return-1==n.indexOf(e.id)}))||[]},formMoreShowSettings:function(){return this.formMoreSettings.filter((function(e){return-1==n.indexOf(e.id)}))||[]}},mounted:function(){var e=this;i.Z.queryExamineDetail({examineId:this.rowData.id},(function(t){for(var a in e.formData=t.data[e.chooseUserResult][0],e.buildForm(e.formData),e.formData.orgIdShow="2",e.imageUrl=t.data[e.chooseUserResult][0].portrait||"",e.selectedTags=[],e.tags=[],t.data[e.chooseUserResult][0].userTags){var r=t.data[e.chooseUserResult][0].userTags[a];e.tags=t.data[e.chooseUserResult][0].userTags,1==r.ischecked&&e.selectedTags.push(r.tagid)}}))},methods:{getFormNecessarySettings:function(e){return[{id:"avatar",formId:"",formItem:{type:"slot",collection:null},label:"用户头像",class:"avatar-form-item",display:!0,exist:!0,formItemLayout:this.formItemLayouts.default},{id:"orgIdShow",formId:"",formItem:{type:"slot",collection:null},label:"所属组织",decoratorOptions:{},display:!0,exist:!0,formItemLayout:this.formItemLayouts.default},{id:"name",formId:"name",formItem:{type:"input",collection:null},label:"姓名",decoratorOptions:{},display:!0,exist:!0,formItemLayout:this.formItemLayouts.default},{id:"loginId",formId:"loginId",formItem:{type:"slot",collection:null},label:"账号",decoratorOptions:{},display:!0,exist:!0,formItemLayout:this.formItemLayouts.default}]},closeDrawer:function(){this.$emit("close")}}},u=c,m=a(1001),f=(0,m.Z)(u,r,o,!1,null,"55fdcbac",null),h=f.exports},59590:function(e,t,a){a.d(t,{Z:function(){return c}});var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("ta-drawer",{attrs:{title:"个人信息",placement:"right",closable:!0,visible:e.visible,destroyOnClose:"",width:"500px"},on:{close:e.closeDrawer}},[a("div",{staticStyle:{padding:"10px 10px 0 20px","overflow-y":"auto"},domProps:{innerHTML:e._s(this.msg)}})])},o=[],i=a(46979),s={name:"showEditUser",props:["visible","rowData"],data:function(){return{imageUrl:"",imageUrlAfter:"",tags:[],selectedTags:[],msg:""}},watch:{visible:function(e){var t=this;if(e){var a={avatar:"用户头像",name:"姓名",loginId:"账号",sex:"性别",jobNumber:"工号",idCardType:"证件类型",idCardNo:"证件号",mobile:"手机号",userTags:"用户标签",education:"学历",email:"邮箱地址",address:"联系地址",zipCode:"邮政编码",workplace:"工作单位"},r=new Set(Object.keys(a));r.add("tags"),i.Z.queryUserSettingTable((function(e){for(var t=e.resultData,o=0;o<t.length;o++)"1"==t[o].effective&&"0"==t[o].hide&&(r.has(t[o].fieldId)||(a[t[o].fieldId]=t[o].displayText))})),i.Z.queryExamineDetail({examineId:this.rowData.id},(function(e){t.msg="<table class='gridtable' style='width: 100%; height: 100%'><tr><th>数据项</th><th>原数据</th><th>更新后数据</th></tr>",Object.keys(a).forEach((function(r){var o="",i="";if("userTags"==r){for(var s in e.data.before[r]){var l=e.data.before[r][s];1==l.ischecked&&(o=o+l.tagname+"、")}for(var n in e.data.after[r]){var d=e.data.after[r][n];1==d.ischecked&&(i=i+d.tagname+"、")}}else"sex"==r?(o=t.CollectionLabel("SEX",e.data.before[r]),i=t.CollectionLabel("SEX",e.data.after[r])):"idCardType"==r?(o=t.CollectionLabel("IDCARDTYPE",void 0==e.data.before[r]?"":e.data.before[r]),i=t.CollectionLabel("IDCARDTYPE",void 0==e.data.after[r]?"":e.data.after[r])):"education"==r?(o=t.CollectionLabel("EDUCATION",void 0==e.data.before[r]?"":e.data.before[r]),i=t.CollectionLabel("EDUCATION",void 0==e.data.after[r]?"":e.data.after[r])):(o=void 0==e.data.before[r]?"":e.data.before[r],i=void 0==e.data.after[r]?"":e.data.after[r]);"avatar"==r?(t.imageUrl=e.data.before.portrait||"",t.imageUrlAfter=e.data.after.portrait||"",t.msg=t.msg+"<tr><td>"+a[r]+'</td><td><div class="pos-avatar"> <div class="img-avatar" style="width: 64px; height: 64px; background-repeat: no-repeat; background-size: contain; background-position: center;background-image:url('+t.imageUrl+')"></div></div></td><td><div class="pos-avatar"> <div class="img-avatar" style="width: 64px; height: 64px; background-repeat: no-repeat; background-size: contain; background-position: center;background-image:url('+t.imageUrlAfter+')"></div></div></td></tr>'):t.msg=o!=i?t.msg+'<tr class="trstess"><td>'+a[r]+"</td><td>"+o+" </td><td>"+i+"</td></tr>":t.msg+"<tr><td>"+a[r]+"</td><td>"+o+" </td><td>"+i+"</td></tr>"})),t.msg=t.msg+"</table>"}))}}},methods:{closeDrawer:function(){this.$emit("close")}}},l=s,n=a(1001),d=(0,n.Z)(l,r,o,!1,null,"438f0158",null),c=d.exports},57701:function(e,t,a){a.d(t,{Z:function(){return c}});var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("ta-drawer",{attrs:{title:e.roleUserVisible?"角色人员详情":"批量操作人员详情",width:"500",placement:"right",closable:!0,visible:e.visible,destroyOnClose:""},on:{close:e.closeEdit}},[a("div",{directives:[{name:"show",rawName:"v-show",value:e.roleUserVisible,expression:"roleUserVisible"}]},[a("ta-alert",{staticClass:"notice-box",attrs:{message:"当前角色为："+e.roleName,type:"info",showIcon:""}})],1),a("div",{staticStyle:{padding:"30px 30px 0 50px","overflow-y":"auto"},domProps:{innerHTML:e._s(this.msg)}})])},o=[],i=a(46979),s={name:"showUserBatch",props:["visible","rowData","tags","roleUserVisible"],data:function(){return{form:null,formData:{},msg:"",roleName:""}},watch:{visible:function(e){e&&this.setValue()}},methods:{setValue:function(){var e=this;this.msg="",i.Z.queryExamineDetail({examineId:this.rowData.id},(function(t){e.msg="<table class='gridtable' style='width: 100%; height: 100%'><tr><th>人员姓名</th><th>账号</th></tr>";for(var a=0;a<t.data.before.length;a++)e.msg=e.msg+"<tr><td>"+t.data.before[a].name+" </td><td>"+t.data.before[a].loginId+"</td></tr>";e.msg=e.msg+"</table>",e.roleUserVisible&&(e.roleName=t.data.before[0].pubRole)}))},closeEdit:function(){this.$emit("close"),this.formData={}}}},l=s,n=a(1001),d=(0,n.Z)(l,r,o,!1,null,"d3409b84",null),c=d.exports},2431:function(e,t,a){a.d(t,{Z:function(){return c}});var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("ta-drawer",{attrs:{title:"人员更改组织",placement:"right",closable:!0,visible:e.visible,destroyOnClose:"",width:"500px"},on:{close:e.closeDrawer}},[a("div",{staticStyle:{padding:"10px 10px 0 20px","overflow-y":"auto"},domProps:{innerHTML:e._s(this.msg)}})])},o=[],i=a(46979),s={name:"showUserEditOrg",props:["visible","rowData"],data:function(){return{msg:""}},watch:{visible:function(e){var t=this;if(e){var a={userName:"用户姓名",orgNamePath:"组织路径"};i.Z.queryExamineDetail({examineId:this.rowData.id},(function(e){t.msg="<table class='gridtable' style='width: 100%; height: 100%'><tr><th>数据项</th><th>原数据</th><th>更新后数据</th></tr>",Object.keys(a).forEach((function(r){var o=void 0==e.data.before[r]?"":e.data.before[r],i=void 0==e.data.after[r]?"":e.data.after[r];t.msg=o!=i?t.msg+'<tr class="trstess"><td>'+a[r]+"</td><td>"+o+" </td><td>"+i+"</td></tr>":t.msg+"<tr><td>"+a[r]+"</td><td>"+o+" </td><td>"+i+"</td></tr>"})),t.msg=t.msg+"</table>"}))}}},methods:{closeDrawer:function(){this.$emit("close")}}},l=s,n=a(1001),d=(0,n.Z)(l,r,o,!1,null,"4ec13072",null),c=d.exports},9080:function(e,t){var a=[{fieldId:"areaValue",formId:"areaValue",displayText:"所属行政区",hide:"0",effective:"1",required:"0",unchangeable:"0",protectPrivacy:"0",orderNo:1,contentSize:null,tiText:null,validReg:null,connectAA10:null,formType:"slot",more:"0"},{fieldId:"customNo",formId:"customNo",displayText:"自定义编码",hide:"0",effective:"1",required:"0",unchangeable:"0",protectPrivacy:"0",orderNo:2,contentSize:30,tiText:"",validReg:"",connectAA10:null,formType:"input",more:"0"},{fieldId:"orderNo",formId:"orderNo",displayText:"排序号",hide:"1",effective:"1",required:"0",unchangeable:"1",protectPrivacy:"0",orderNo:3,contentSize:null,tiText:"",validReg:"",connectAA10:null,formType:"input",more:"0"},{fieldId:"orgCode",formId:"orgCode",displayText:"组织代码",hide:"0",effective:"1",required:"0",unchangeable:"0",protectPrivacy:"0",orderNo:4,contentSize:18,tiText:"",validReg:null,connectAA10:null,formType:"input",more:"0"},{fieldId:"orgManager",formId:"orgManager",displayText:"负责人",hide:"0",effective:"1",required:"0",unchangeable:"0",protectPrivacy:"0",orderNo:5,contentSize:null,tiText:"",validReg:null,connectAA10:null,formType:"slot",more:"0"},{fieldId:"contacts",formId:"contacts",displayText:"联系人",hide:"0",effective:"1",required:"0",unchangeable:"0",protectPrivacy:"0",orderNo:6,contentSize:null,tiText:"",validReg:null,connectAA10:null,formType:"slot",more:"0"},{fieldId:"tel",formId:"tel",displayText:"联系电话",hide:"0",effective:"1",required:"0",unchangeable:"0",protectPrivacy:"0",orderNo:7,contentSize:20,tiText:"",validReg:"/^((\\d{3,4})|\\d{3,4}-)?\\d{7,8}$/",connectAA10:null,formType:"input",more:"0"},{fieldId:"address",formId:"address",displayText:"联系地址",hide:"0",effective:"1",required:"0",unchangeable:"0",protectPrivacy:"0",orderNo:8,contentSize:450,tiText:"",validReg:null,connectAA10:null,formType:"input",more:"0"},{fieldId:"tags",formId:"tags",displayText:"组织标签",hide:"0",effective:"1",required:"0",unchangeable:"0",protectPrivacy:"0",orderNo:10,contentSize:null,tiText:"",validReg:null,connectAA10:null,formType:"slot",more:"0"}],r={fieldId:"field",formId:"field",displayText:"扩展信息",effective:"0",hide:"1",required:"0",unchangeable:"0",protectPrivacy:"0",orderNo:1,contentSize:null,tiText:null,validReg:null,connectAA10:null,formType:"input",more:"1"};function o(e,t){return(Array(t).join(0)+e).slice(-t)}for(var i=10,s=1;s<=i;s++){var l=Object.assign({},r),n=o(s,2);l.fieldId=l.fieldId+n,l.formId=l.formId+n,l.displayText=l.displayText+n,l.orderNo=a.length+1,a.push(l)}t["Z"]=a},51095:function(e,t){var a=[{fieldId:"sex",formId:"sex",displayText:"性别",hide:"0",effective:"1",required:"0",unchangeable:"0",protectPrivacy:"0",orderNo:1,contentSize:null,tiText:"请输入性别",validReg:null,connectAA10:"SEX",formType:"radio",more:"0"},{fieldId:"jobNumber",formId:"jobNumber",displayText:"工号",hide:"0",effective:"1",required:"0",unchangeable:"0",protectPrivacy:"0",orderNo:2,contentSize:null,tiText:"",validReg:"",connectAA10:null,formType:"input",more:"0"},{fieldId:"idCardType",formId:"idCardType",displayText:"证件类型",hide:"0",effective:"1",required:"0",unchangeable:"0",protectPrivacy:"0",orderNo:3,contentSize:null,tiText:"",validReg:"",connectAA10:"IDCARDTYPE",formType:"select",more:"0"},{fieldId:"idCardNo",formId:"idCardNo",displayText:"证件号",hide:"0",effective:"1",required:"0",unchangeable:"0",protectPrivacy:"0",orderNo:4,contentSize:null,tiText:"",validReg:"/(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)/",connectAA10:null,formType:"input",more:"0"},{fieldId:"mobile",formId:"mobile",displayText:"手机号",hide:"0",effective:"1",required:"0",unchangeable:"0",protectPrivacy:"0",orderNo:5,contentSize:null,tiText:"",validReg:"/^1[3|4|5|7|8|9][0-9]\\d{8}$/",connectAA10:null,formType:"input",more:"0"},{fieldId:"tags",formId:"tags",displayText:"用户标签",hide:"0",effective:"1",required:"0",unchangeable:"0",protectPrivacy:"0",orderNo:6,contentSize:null,tiText:"",validReg:null,connectAA10:null,formType:"slot",more:"0"},{fieldId:"education",formId:"education",displayText:"学历",hide:"0",effective:"1",required:"0",unchangeable:"0",protectPrivacy:"0",orderNo:7,contentSize:null,tiText:"",validReg:null,connectAA10:"EDUCATION",formType:"select",more:"1"},{fieldId:"email",formId:"email",displayText:"邮箱地址",hide:"0",effective:"1",required:"0",unchangeable:"0",protectPrivacy:"0",orderNo:8,contentSize:null,tiText:"",validReg:"/^([A-Za-z0-9_\\-\\.])+\\@([A-Za-z0-9_\\-\\.])+\\.([A-Za-z]{2,4})$/",connectAA10:null,formType:"input",more:"1"},{fieldId:"address",formId:"address",displayText:"联系地址",hide:"0",effective:"1",required:"0",unchangeable:"0",protectPrivacy:"0",orderNo:9,contentSize:null,tiText:"",validReg:null,connectAA10:null,formType:"input",more:"1"},{fieldId:"zipCode",formId:"zipCode",displayText:"邮政编码",hide:"0",effective:"1",required:"0",unchangeable:"0",protectPrivacy:"0",orderNo:10,contentSize:null,tiText:"",validReg:"/^[0-9]{6}$/",connectAA10:null,formType:"input",more:"1"},{fieldId:"workplace",formId:"workplace",displayText:"工作单位",hide:"0",effective:"1",required:"0",unchangeable:"0",protectPrivacy:"0",orderNo:11,contentSize:null,tiText:"",validReg:null,connectAA10:null,formType:"input",more:"1"}],r={fieldId:"field",formId:"field",displayText:"扩展信息",effective:"0",hide:"1",required:"0",unchangeable:"0",protectPrivacy:"0",orderNo:1,contentSize:null,tiText:null,validReg:null,connectAA10:null,formType:"input",more:"1"};function o(e,t){return(Array(t).join(0)+e).slice(-t)}for(var i=10,s=1;s<=i;s++){var l=Object.assign({},r),n=o(s,2);l.fieldId=l.fieldId+n,l.formId=l.formId+n,l.displayText=l.displayText+n,l.orderNo=a.length+1,a.push(l)}t["Z"]=a},46979:function(e,t){var a="/examine/taExamineRestService/";t["Z"]={examineSomeone:function(e,t){Base.submit(null,{url:a+"examineSomeone",data:e},{successCallback:function(e){return t(e)}})},batchExamineSomeone:function(e,t){Base.submit(null,{url:a+"batchExamineSomeone",data:e},{successCallback:function(e){return t(e)}})},refusePass:function(e,t){Base.submit(null,{url:a+"refusePass",data:e},{successCallback:function(e){return t(e)}})},batchRefusePass:function(e,t){Base.submit(null,{url:a+"batchRefusePass",data:e},{successCallback:function(e){return t(e)}})},queryExamineDetail:function(e,t){Base.submit(null,{url:a+"queryExamineDetail",data:e},{successCallback:function(e){return t(e)}})},queryTags:function(e,t){Base.submit(null,{url:"org/orguser/orgManagementRestService/queryTags",data:e},{successCallback:function(e){return t(e)}})},queryTagByUserId:function(e,t){Base.submit(null,{url:"/org/orguser/userManagementRestService/queryTagByUserId",data:e,showPageLoading:!1},{successCallback:function(e){return t(e)}})},queryOrgSettingTable:function(e){Base.submit(null,{url:"org/sysmg/manageableFieldsRestService/queryManageableFields",data:{type:"2"}},{successCallback:function(t){return e(t.data)}})},queryUserSettingTable:function(e){Base.submit(null,{url:"org/sysmg/manageableFieldsRestService/queryManageableFields",data:{type:"1"}},{successCallback:function(t){return e(t.data)}})},queryRePermissionByRoleId:function(e,t){Base.submit(null,{url:"org/authority/roleAuthorityManagementRestService/queryRePermissionByRoleId",data:e},{successCallback:function(e){return t(e)}})},querySimilarAuthority:function(e,t){Base.submit(null,{url:"/org/authority/similarAuthorityManagementRestService/queryCurrentUserRePermission",data:e},{successCallback:function(e){return t(e.data)}})},examineChart:function(e,t){Base.submit(null,{url:a+"examineChart",data:e},{successCallback:function(e){return t(e.data)}})}}},87508:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){var _api_index__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__(46979),_projectCommon_js_extendConfig_extendUserSetting__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__(51095),_yh_ta_utils_isArray__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__(41538),formItemLayouts={default:{labelCol:{span:6},wrapperCol:{span:18}}},mixins={data:function(){return{formItemLayouts:formItemLayouts,extendSettings:_projectCommon_js_extendConfig_extendUserSetting__WEBPACK_IMPORTED_MODULE_1__.Z,formSettings:[]}},created:function(){var e=this;_api_index__WEBPACK_IMPORTED_MODULE_0__.Z.queryUserSettingTable((function(t){(0,_yh_ta_utils_isArray__WEBPACK_IMPORTED_MODULE_2__.Z)(t.resultData)&&t.resultData.length>0&&(e.extendSettings=t.resultData)}))},computed:{formNormalSettings:function(){return this.formSettings.filter((function(e){return!e.isMore&&e.exist}))||[]},formMoreSettings:function(){return this.formSettings.filter((function(e){return e.isMore&&e.exist}))||[]}},methods:{buildForm:function(e){this.formSettings=this.getFormSettings(e)},getFormNecessarySettings:function(e){return[{id:"avatar",formId:"",formItem:{type:"slot",collection:null},label:"用户头像",class:"avatar-form-item",display:!0,exist:!0,formItemLayout:formItemLayouts.default},{id:"orgId",formId:"orgId",formItem:{type:"slot",collection:null},label:"所属组织",decoratorOptions:{rules:[{required:!0,message:"请选择人员的所属组织"}]},display:!0,exist:"1"==this.editType,formItemLayout:formItemLayouts.default},{id:"orgIdShow",formId:"",formItem:{type:"slot",collection:null},label:"所属组织",decoratorOptions:{},display:!0,exist:"2"==this.editType,formItemLayout:formItemLayouts.default},{id:"name",formId:"name",formItem:{type:"input",collection:null},label:"姓名",decoratorOptions:{rules:[{required:!0,message:"请输入人员姓名"},{max:20,message:"姓名长度不能大于20"}],initialValue:e.name},display:!0,exist:!0,formItemLayout:formItemLayouts.default},{id:"loginId",formId:"loginId",formItem:{type:"input",collection:null},label:"账号",decoratorOptions:{rules:[{required:!0,message:"请输入人员登录帐号"}],initialValue:e.loginId},display:!0,exist:!0,disabled:"2"==this.editType,formItemLayout:formItemLayouts.default},{id:"password",formId:"password",formItem:{type:"slot",collection:null},label:"登录口令",decoratorOptions:{rules:[{required:!0,message:"请输入登录口令"},{min:6,message:"登录口令不能少于6位字符"},{validator:this.validatePwd}]},display:!0,exist:"1"==this.editType,formItemLayout:formItemLayouts.default},{id:"password_2",formId:"password_2",formItem:{type:"slot",collection:null},label:"确认口令",decoratorOptions:{rules:[{validator:this.compareToFirstPwd},{required:!0,message:"请再次输入登录口令"}]},display:!0,exist:"1"==this.editType,formItemLayout:formItemLayouts.default}]},getFormSettings:function getFormSettings(){var initData=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},propSettings=this.extendSettings.sort((function(e,t){return e.orderNo-t.orderNo})).map((function(setting){var formId=setting.formId,label=setting.displayText||"",propSetting={id:setting.fieldId,formId:formId,class:null,formItem:{type:setting.formType,collection:setting.connectAA10},label:label,display:"0"===setting.hide,exist:"1"===setting.effective,disabled:"1"===setting.unchangeable,formItemLayout:formItemLayouts[formId]||formItemLayouts.default,decoratorOptions:{},isMore:"1"===setting.more},rules=[];if("1"===setting.required&&rules.push({required:!0,message:label+"是必须的"}),isNaN(parseInt(setting.contentSize))||rules.push({max:setting.contentSize,message:label+"内容长度不能超过"+setting.contentSize}),setting.validReg){var isreg;try{isreg=eval(setting.validReg)instanceof RegExp}catch(e){isreg=!1}isreg&&rules.push({pattern:eval(setting.validReg),message:"请输入正确的"+label+"内容"})}return propSetting.decoratorOptions.rules=rules,initData.hasOwnProperty(formId)&&(propSetting.decoratorOptions.initialValue=initData[formId]),propSetting}));return this.getFormNecessarySettings(initData).concat(propSettings)}}};__webpack_exports__["Z"]=mixins},17973:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){var _api_index__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__(46979),_projectCommon_js_extendConfig_extendOrgSetting__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__(9080),_yh_ta_utils_isArray__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__(41538),formItemLayouts={default:{labelCol:{span:6},wrapperCol:{span:18}}},mixins={data:function(){return{formItemLayouts:formItemLayouts,extendSettings:_projectCommon_js_extendConfig_extendOrgSetting__WEBPACK_IMPORTED_MODULE_1__.Z,formSettings:[]}},created:function(){var e=this;_api_index__WEBPACK_IMPORTED_MODULE_0__.Z.queryOrgSettingTable((function(t){(0,_yh_ta_utils_isArray__WEBPACK_IMPORTED_MODULE_2__.Z)(t.resultData)&&t.resultData.length>0&&(e.extendSettings=t.resultData)}))},computed:{formNormalSettings:function(){return this.formSettings.filter((function(e){return!e.isMore&&e.exist}))||[]},formMoreSettings:function(){return this.formSettings.filter((function(e){return e.isMore&&e.exist}))||[]}},methods:{buildForm:function(e){this.formSettings=this.getFormSettings(e)},getFormNecessarySettings:function(e){return[{id:"porgId",formId:"porgId",formItem:{type:"input",collection:null},label:"上级组织机构ID",decoratorOptions:{rules:[{required:!0}],initialValue:e.porgId},display:!1,exist:!0,formItemLayout:formItemLayouts.default},{id:"pOrgName",formId:"pOrgName",formItem:{type:"input",collection:null},label:"上级组织机构",decoratorOptions:{initialValue:e.pOrgName},display:"0"!=e.porgId,exist:!0,disabled:!0,formItemLayout:formItemLayouts.default},{id:"orgId",formId:"orgId",formItem:{type:"input",collection:null},label:"组织机构ID",decoratorOptions:{initialValue:e.orgId},display:!1,exist:!0,formItemLayout:formItemLayouts.default},{id:"orgName",formId:"orgName",formItem:{type:"input",collection:null},label:"组织机构名称",decoratorOptions:{rules:[{pattern:/^[\u4E00-\u9FA5A-Za-z0-9_/\\-]+$/,message:"只能输入中文英文数字、下划线、横杠及斜杠"},{required:!0,message:"组织机构名称不能为空!"},{max:300,message:"组织机构名称超过限制长度!"}],initialValue:e.orgName},display:!0,exist:!0,formItemLayout:formItemLayouts.default},{id:"orgType",formId:"orgType",formItem:{type:"radioButton",collection:"ORGTYPE"},label:"组织类型",decoratorOptions:{rules:[{required:!0,message:"请选择对应组织类型"}],initialValue:e.orgType},display:!0,exist:!0,formItemLayout:formItemLayouts.default},{id:"effective",formId:"effective",formItem:{type:"slot",collection:null},label:"有效标识",decoratorOptions:{valuePropName:"checked",initialValue:e.effective},display:!0,exist:!0,formItemLayout:formItemLayouts.default}]},getFormSettings:function getFormSettings(){var initData=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},propSettings=this.extendSettings.sort((function(e,t){return e.orderNo-t.orderNo})).map((function(setting){var formId=setting.formId,label=setting.displayText||"",propSetting={id:setting.fieldId,formId:formId,class:null,formItem:{type:setting.formType,collection:setting.connectAA10},label:label,display:"0"===setting.hide,exist:"1"===setting.effective,disabled:"1"===setting.unchangeable,formItemLayout:formItemLayouts[formId]||formItemLayouts.default,decoratorOptions:{},isMore:"1"===setting.more},rules=[];if("1"===setting.required&&rules.push({required:!0,message:label+"是必须的"}),isNaN(parseInt(setting.contentSize))||rules.push({max:setting.contentSize,message:label+"内容长度不能超过"+setting.contentSize}),setting.validReg){var isreg;try{isreg=eval(setting.validReg)instanceof RegExp}catch(e){isreg=!1}isreg&&rules.push({pattern:eval(setting.validReg),message:"请输入正确的"+label+"内容"})}return propSetting.decoratorOptions.rules=rules,initData.hasOwnProperty(formId)&&(propSetting.decoratorOptions.initialValue=initData[formId]),propSetting}));return this.getFormNecessarySettings(initData).concat(propSettings)}}};__webpack_exports__["Z"]=mixins}}]);
"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[3872],{43872:function(t,e,a){a.r(e),a.d(e,{default:function(){return E}});var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"fit",staticStyle:{"margin-top":"15px"}},[a("ta-row",{attrs:{type:"flex",justify:"space-around"}},[a("ta-col",{attrs:{span:23}},[a("ta-input-search",{staticStyle:{width:"350px"},attrs:{placeholder:"请输入诊断关键字",enterButton:""},on:{search:t.onSearch},model:{value:t.keyword,callback:function(e){t.keyword=e},expression:"keyword"}})],1),a("ta-col",{staticStyle:{"margin-top":"8px"},attrs:{span:22}},[t._v(" 查询内容："),a("ta-checkbox-group",{attrs:{options:t.plainOptions},on:{change:t.onChange},model:{value:t.checkedList,callback:function(e){t.checkedList=e},expression:"checkedList"}})],1)],1),a("ta-row",{staticStyle:{"margin-top":"10px"},attrs:{type:"flex",justify:"space-around"}},[a("div",{staticClass:"part-top"},[a("ta-title",{attrs:{title:"查询结果"}})],1)]),a("ta-row",{attrs:{type:"flex",justify:"start"}},[a("ta-col",{staticStyle:{"margin-top":"5px","margin-left":"10px"},attrs:{span:20}},[a("div",{staticStyle:{width:"1200px"}},[a("ta-big-table",{attrs:{border:"",stripe:"",resizable:"","highlight-hover-row":"",height:"550",data:t.tableData,"cell-style":function(){return{padding:0}},align:"center"}},[a("ta-big-table-column",{attrs:{field:"ykz010",title:"诊断包",width:"500"},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.row;return[a("p",{staticStyle:{margin:"0"},domProps:{innerHTML:t._s(t.highlight(i.ykz010))}},[t._v(t._s(i.ykz010))])]}}])}),a("ta-big-table-column",{attrs:{width:"693",field:"diagNameList",title:"诊断名称"},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.row;return t._l(i.diagNameList,(function(e,n){return a("div",[n<3?a("span",{domProps:{innerHTML:t._s(t.highlight(e))}},[t._v(t._s(e))]):t._e(),n<i.diagNameList.length-1&&n<3?a("ta-divider",{staticStyle:{margin:"3px"}}):t._e(),3==n?a("span",[a("ta-icon",{staticStyle:{color:"blue","margin-left":"5px"},attrs:{type:"search"}}),a("a",{on:{click:function(e){return t.handleMore(i)}}},[t._v("查看更多")])],1):t._e()],1)}))}}])})],1)],1)])],1),a("ta-modal",{staticStyle:{float:"left"},attrs:{title:"详细内容",height:this.windowWidth+"px",draggable:!0,"destroy-on-close":!0,footer:null},on:{ok:t.handleOk},model:{value:t.visible,callback:function(e){t.visible=e},expression:"visible"}},[a("ta-row",{attrs:{type:"flex",justify:"space-around"}},[a("ta-col",{attrs:{span:23}},[a("ta-input-search",{staticStyle:{width:"350px"},attrs:{placeholder:"请输入诊断名称或诊断编码",enterButton:"查询"},on:{search:t.onSearch2}})],1),a("ta-col",{staticStyle:{"margin-top":"10px"},attrs:{span:23}},[t._v(" 诊断包： "+t._s(t.diagnosticPackage)+" ")])],1),a("ta-row",{attrs:{type:"flex",justify:"start"}},[a("ta-col",{staticStyle:{"margin-top":"20px","margin-left":"10px"},attrs:{span:24}},[a("ta-big-table",{attrs:{border:"",stripe:"",resizable:"","show-overflow":"","highlight-hover-row":"",height:"700",align:"center",data:t.tableData2}},[a("ta-big-table-column",{attrs:{type:"seq",title:"序号",width:"60"}}),a("ta-big-table-column",{attrs:{field:"type",title:"诊断名称"}})],1)],1)],1)],1)],1)},n=[],r=a(66347),s="diagnosticMatching/",l={getBasePath:function(){return faceConfig.basePath+s},queryByDiagnosticKeyword:function(t,e){Base.submit(null,{url:s+"diagnosticMatchingQuery",data:t},{successCallback:function(t){return e(t)}})}},o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},c=[],u={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '},h={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:u}}},d=h,p=a(1001),g=(0,p.Z)(d,o,c,!1,null,"5e7ef0ae",null),f=g.exports,m=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",t._l(t.tagList,(function(e){return a("ta-tag",{key:e.label,attrs:{color:e.checked?e.color2:e.color1},on:{click:function(a){return t.handleChange(e)}}},[a("span",{staticClass:"tag-icon"},[t._v(t._s(e.icon))]),a("span",{staticClass:"tag-label"},[t._v(" "+t._s(e.label)+" ")])])})),1)},y=[],b=[{value:"1",label:"待审批",checked:!0,color1:"orange",color2:"#FCB76B"},{value:"2",label:"审批通过未导出",checked:!1,color1:"green",color2:"#87d068"},{value:"3",label:"审批通过已导出",checked:!1,color1:"purple",color2:"#7d02d1"},{value:"4",label:"审批驳回待提交",checked:!1,color1:"red",color2:"#fa0000"},{value:"5",label:"全部",checked:!1,color1:"blue",color2:"#2db7f5"}],v={name:"tagChange",props:{tagValue:{type:String},statusArr:{type:Array,default:function(){return b}}},data:function(){return{tagList:this.statusArr}},watch:{tagValue:function(t){this.changeTag(t)}},mounted:function(){this.changeTag(this.tagValue)},methods:{changeTag:function(t){this.tagList=this.tagList.map((function(e){return e.value===t?e.checked=!0:e.checked=!1,e}))},handleChange:function(t){this.$emit("change",t)}}},k=v,_=(0,p.Z)(k,m,y,!1,null,"14432a73",null),w=_.exports,x=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("ta-button",[t._v(t._s(t.buttonKey))])},S=[],L={name:"partComponent",props:{buttonKey:{type:String}},data:function(){return{}}},C=L,D=(0,p.Z)(C,x,S,!1,null,"333cfeb6",null),z=D.exports,$=[{label:"诊断包",value:"0"},{label:"诊断名称",value:"1"}],B={name:"diagnosticMatchingQuery.vue",components:{partComponent:z,TaTitle:f,TagChange:w},data:function(){return{name:name,keyword:"",visible:!1,plainOptions:$,checkedList:["0","1"],diagnosticPackage:"",parameters:{},userList:[],tableData:[],bftableData:[],tableData2:[],windowWidth:.49*document.documentElement.clientWidth}},mounted:function(){},methods:{onSearch:function(t){var e=this;if(""!=t&&null!=t){var a=/[\u4e00-\u9fa5]+/g;t.match(a)?t.length<2?this.$message.error("请输入三个以上诊断关键字"):0!=this.checkedList.length?(this.parameters={},-1!=this.checkedList.indexOf("0")&&(this.parameters.ykz010=t.trim()),-1!=this.checkedList.indexOf("1")&&(this.parameters.ykz082=t.trim()),l.queryByDiagnosticKeyword(this.parameters,(function(t){e.tableData=t.data.kf32List,e.tableData.length>60&&e.$message.error("您所查询的关键字存在的诊断包和诊断名称过多，请缩小范围查询更精确的结果")}))):this.$message.error("请选中诊断包或诊断名称"):this.$message.error("请输入中文")}else this.$message.error("请输入诊断包或者诊断名称")},onSearch2:function(t){var e=[];this.bftableData.forEach((function(a){if(a.type.includes(t)){var i={};i["type"]=a.type,e.push(i)}})),this.tableData2=e},onChange:function(t){this.checkedList=t},handleMore:function(t){var e,a=[],i=(0,r.Z)(t.diagNameList);try{for(i.s();!(e=i.n()).done;){var n=e.value,s={};s["type"]=n,a.push(s)}}catch(l){i.e(l)}finally{i.f()}this.diagnosticPackage=t.ykz010,this.tableData2=a,this.bftableData=a,this.visible=!0},handleOk:function(t){this.visible=!1},highlight:function(t){var e='<span class="actives">'.concat(this.keyword.trim(),"</span>"),a=new RegExp(this.keyword.trim(),"gi");return t.replace(a,e)}}},M=B,T=(0,p.Z)(M,i,n,!1,null,"d133a698",null),E=T.exports}}]);
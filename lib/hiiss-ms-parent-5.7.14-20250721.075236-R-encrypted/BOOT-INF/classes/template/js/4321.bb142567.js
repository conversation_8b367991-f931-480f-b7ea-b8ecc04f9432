"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[4321],{85148:function(e,t,a){a.r(t),a.d(t,{default:function(){return D}});var i=function(){var e=this,t=this,a=t.$createElement,i=t._self._c||a;return i("div",{staticClass:"fit"},[i("ta-border-layout",{staticStyle:{},attrs:{"layout-type":"fixTop"}},[i("div",{staticStyle:{"padding-top":"10px"},attrs:{slot:"header"},slot:"header"},[i("div",{staticClass:"query-header"},[i("div",{ref:"formBox",staticClass:"query-header-left",style:t.formBoxStyle},[i("ta-form",{attrs:{autoFormCreate:function(t){e.baseInfoForm=t},layout:"horizontal","label-width":"100px",formLayout:!0}},[i("ta-form-item",{attrs:{fieldDecoratorId:"allDate",span:6,label:"时间范围","init-value":t.rangeValue,fieldDecoratorOptions:{rules:[{required:!0,message:"必须选取日期"}]}}},[i("ta-range-picker",{attrs:{"allow-one":!0,type:"month"}})],1),i("ta-form-item",{attrs:{"field-decorator-id":"statisticsDimension",label:"统计维度",span:6}},[i("ta-select",{attrs:{placeholder:"统计维度筛选",allowClear:"",mode:"multiple",options:t.statisticsDimension},on:{change:t.fnChangeDimension}})],1),i("ta-form-item",{attrs:{"field-decorator-id":"aka130",label:"医疗类别",span:6}},[i("ta-select",{attrs:{placeholder:"医疗类别筛选",allowClear:"",collectionType:"AKA130"}})],1),i("ta-form-item",{attrs:{"field-decorator-id":"aae140",label:"险种类型",span:6}},[i("ta-select",{attrs:{placeholder:"险种类型筛选",allowClear:"",collectionType:"AAE140"}})],1),i("ta-form-item",{attrs:{"field-decorator-id":"aae141",label:"医保类别",span:6}},[i("ta-select",{attrs:{placeholder:"医保类型筛选",allowClear:"",collectionType:"AAE141"}})],1),i("ta-form-item",{attrs:{"field-decorator-id":"aaa027",label:"统筹区域",span:6}},[i("ta-select",{attrs:{placeholder:"统筹区域筛选",allowClear:""}},t._l(t.aaa027List,(function(e,a){return i("ta-select-option",{key:e},[t._v(t._s(e))])})),1)],1),i("div",{staticStyle:{display:"flex","margin-left":"20px",float:"right"}},[i("ta-button",{attrs:{type:"primary",icon:"search"},on:{click:t.fnQuery}},[t._v("查询")]),i("ta-button",{staticStyle:{"margin-left":"15px"},attrs:{icon:"redo"},on:{click:t.fnReSet}},[t._v("重置")])],1)],1)],1)])]),i("div",{staticClass:"fit content-box"},[i("div",{staticStyle:{height:"35px","margin-left":"8px","margin-top":"8px"}},[i("ta-checkbox-group",{on:{change:t.fnCheckboxChange}},[t.showKsBox?i("ta-checkbox",{style:t.ksColor,attrs:{value:"showKs"}},[t._v("科室")]):t._e(),t.showDoctorBox?i("ta-checkbox",{style:t.doctorColor,attrs:{value:"showYs"}},[t._v("医师")]):t._e()],1)],1),i("div",{staticClass:"content-table"},[i("ta-big-table",{ref:"infoTableRef",attrs:{columns:t.infoColumns,data:t.infoTableData,border:"",height:"auto","highlight-hover-row":"","auto-resize":"","show-overflow":"",resizable:"","sort-config":t.sortConfig,"filter-config":t.filterConfig},on:{"sort-change":t.sortChangeEvent,"filter-change":t.filterChangeEvent},scopedSlots:t._u([{key:"ksFilter",fn:function(e){var a=e.$panel,o=e.column;return[i("ta-select",{staticStyle:{margin:"10px",width:"120px"},attrs:{options:t.ksList,mode:"multiple",dropdownMatchSelectWidth:!1,"get-popup-container":t.setPopupContainer},on:{change:function(e){return t.fnKsChangeOption(e,o.filters,a)}}})]}},{key:"doctorFilter",fn:function(e){var a=e.$panel,o=e.column;return[i("ta-select",{staticStyle:{margin:"10px",width:"120px"},attrs:{options:t.doctorList,mode:"multiple",dropdownMatchSelectWidth:!1,"get-popup-container":t.setPopupContainer},on:{change:function(e){return t.fnDoctorChangeOption(e,o.filters,a)}}})]}},{key:"totalFee",fn:function(){return[i("span",{staticStyle:{"background-color":"azure",cursor:"pointer"}},[t.showFeeDetail?i("ta-icon",{attrs:{type:"minus-square",theme:"twoTone"},on:{click:t.showFeeDetailChange}}):i("ta-icon",{attrs:{type:"plus-square",theme:"twoTone"},on:{click:t.showFeeDetailChange}})],1),i("span",{staticStyle:{"margin-left":"5px"}},[t._v("总费用")])]},proxy:!0}])})],1),i("div",{staticClass:"content-box-footer"},[i("ta-pagination",{ref:"infoPageRef",staticStyle:{position:"absolute",right:"100px",bottom:"6px"},attrs:{dataSource:t.infoTableData,params:t.infoPageParams,defaultPageSize:100,pageSizeOptions:["100","200","500","1000","1500"],url:"costStatistics/queryData"},on:{"update:dataSource":function(e){t.infoTableData=e},"update:data-source":function(e){t.infoTableData=e}}}),i("ta-button",{staticStyle:{position:"absolute",right:"10px",bottom:"6px"},attrs:{icon:"download",type:"primary"},on:{click:t.fnExportExcel}},[t._v("导出")])],1)])])],1)},o=[],s=a(89584),n=a(66347),l=a(48534),r=a(95082),c=(a(36133),a(88412)),f=a(362),u=a(36797),h=a.n(u),d=a(22722),m=a(55115),p=(a(50451),a(83231));m.w3.prototype.Base=Object.assign(m.w3.prototype.Base,(0,r.Z)({},d.Z));var g=[],b={name:"costStatistics",components:{TaTitle:c.Z,atientDetails:f["default"]},data:function(){var e=[{type:"seq",title:"序号",width:"60",align:"center"},{title:"医疗类别",width:"150",field:"aka130",align:"center",collectionType:"AKA130",sortable:!0,visible:!1},{title:"险种类型",width:"150",field:"aae140",align:"left",collectionType:"AAE140",sortable:!0,visible:!1},{title:"医保类别",width:"150",field:"aae141",align:"center",collectionType:"AAE141",sortable:!0,visible:!1},{title:"统筹区域",width:"150",field:"aaa027",align:"center",sortable:!0,visible:!1},{title:"科室",width:"150",field:"aae386",align:"center",visible:!1,filters:[{data:""}],customRender:{filter:"ksFilter"}},{title:"医师",width:"150",field:"aaz570",align:"center",visible:!1,filters:[{data:""}],customRender:{filter:"doctorFilter"}},{title:"总费用",field:"total_fee",align:"right",headerAlign:"center",width:"300",sortable:!0,visible:!0,formatter:"formatAmount",customRender:{header:"totalFee"}}];return{col:{xs:1,sm:2,md:2,lg:6,xl:6,xxl:4},headers:{authorization:"authorization-text"},rangeValue:[this.Base.getMoment((new Date).toISOString().slice(0,4)+"-01","YYYY-MM"),this.Base.getMoment((new Date).toISOString().slice(0,7),"YYYY-MM")],aka063List:[],aka063Columns:[],statisticsDimension:[],selectedDimension:[],aaa027List:[],showKs:"0",showYs:"0",infoColumns:e,infoTableData:g,formShowAll:!0,showFeeDetail:!1,filterConfig:{remote:!0},sortConfig:{trigger:"default",remote:!0},sortColumn:"",ascOrDesc:"",hasError:!1,akb020:"",aaz307:"",permissions:null,ksList:[],doctorList:[],selectedKs:[],selectedDoctor:[],ksColor:{},doctorColor:{},showKsBox:!1,showDoctorBox:!1}},created:function(){var e=this;return(0,l.Z)(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,p.Z.permissionCheck();case 2:e.permissions=t.sent,e.permissions.aaz307Disable||(e.showKsBox=!0),e.showDoctorBox=!0,e.Base.submit(null,{url:"costStatistics/queryAka063",data:{}},{successCallback:function(t){e.aka063List=t.data.aka063List;for(var a=0;a<e.aka063List.length;a++){var i={title:e.aka063List[a].title,field:e.aka063List[a].field,align:"right",visible:!1,sortable:!0,formatter:"formatAmount",width:"100"};e.aka063Columns.push(i)}var o={title:"总费用",field:"totalFee_flag",align:"center",visible:!1,customRender:{header:"totalFee"},children:e.aka063Columns};e.infoColumns.push(o)},failCallback:function(t){e.$message.error("院内收费类别列表加载失败")}});case 6:case"end":return t.stop()}}),t)})))()},mounted:function(){this.fnQueryStatisticsDimension(),this.fnQueryDept(),this.fnQueryDocter(),this.fnQueryAaa027()},computed:{formBoxStyle:function(){return this.formShowAll?{height:"auto"}:{height:"50px"}}},methods:{setPopupContainer:function(e){return e.parentNode},fnKsChangeOption:function(e,t,a){var i=this;t.forEach((function(t){i.IsInArray(e,t.value)?a.changeOption(t.value,!0,t):a.changeOption(t.value,!1,t)}))},fnDoctorChangeOption:function(e,t,a){var i=this;t.forEach((function(t){i.IsInArray(e,t.value)?a.changeOption(t.value,!0,t):a.changeOption(t.value,!1,t)}))},showFeeDetailChange:function(){if(this.showFeeDetail=!this.showFeeDetail,this.showFeeDetail){var e,t=(0,n.Z)(this.aka063Columns);try{for(t.s();!(e=t.n()).done;){var a=e.value;this.$refs.infoTableRef.showColumn(this.$refs.infoTableRef.getColumnByField(a.field))}}catch(l){t.e(l)}finally{t.f()}this.$refs.infoTableRef.showColumn(this.$refs.infoTableRef.getColumnByField("totalFee_flag")),this.$refs.infoTableRef.hideColumn(this.$refs.infoTableRef.getColumnByField("total_fee"))}else{var i,o=(0,n.Z)(this.aka063Columns);try{for(o.s();!(i=o.n()).done;){var s=i.value;this.$refs.infoTableRef.hideColumn(this.$refs.infoTableRef.getColumnByField(s.field))}}catch(l){o.e(l)}finally{o.f()}this.$refs.infoTableRef.hideColumn(this.$refs.infoTableRef.getColumnByField("totalFee_flag")),this.$refs.infoTableRef.showColumn(this.$refs.infoTableRef.getColumnByField("total_fee"))}},fnQueryStatisticsDimension:function(){var e=this;this.Base.submit(null,{url:"costStatistics/queryStatisticsDimension",data:{}},{successCallback:function(t){e.statisticsDimension=t.data.statisticsDimension},failCallback:function(t){e.$message.error("统计维度加载失败")}})},fnQueryDept:function(e){var t=this;this.akb020=e||"",this.Base.submit(null,{url:"miimCommonRead/queryDepartRpcList",data:{akb020:this.akb020},autoValid:!1},{successCallback:function(e){t.ksList=e.data.resultData,t.$refs.infoTableRef.setFilter(t.$refs.infoTableRef.getColumnByField("aae386"),t.ksList),t.$refs.infoTableRef.updateData()},failCallback:function(e){t.$message.error("科室数据加载失败")}})},fnQueryDocter:function(e){var t=this,a={akb020:this.akb020};e&&(a.departCode=e),this.Base.submit(null,{url:"miimCommonRead/queryDoctorRpcList",data:a,autoValid:!1},{successCallback:function(e){t.doctorList=e.data.resultData,t.$refs.infoTableRef.setFilter(t.$refs.infoTableRef.getColumnByField("aaz570"),t.doctorList),t.$refs.infoTableRef.updateData()},failCallback:function(e){t.$message.error("医师数据加载失败")}})},fnQueryAaa027:function(){var e=this;this.Base.submit(null,{url:"costStatistics/queryAaa027",data:{},autoValid:!1},{successCallback:function(t){e.aaa027List=t.data.aaa027List},failCallback:function(t){e.$message.error("科室数据加载失败")}})},disabledStartDate:function(e){return e.format("YYYYMMDD")>this.baseInfoForm.getFieldValue("endDate").format("YYYYMMDD")},disabledEndDate:function(e){return e=e.format("YYYYMMDD"),e>h()().startOf("day").format("YYYYMMDD")||e<this.baseInfoForm.getFieldValue("startDate").format("YYYYMMDD")},infoPageParams:function(){var e=this.baseInfoForm.getFieldsValue();return e.allDate&&(e.allDate[0]&&(e.startDate=e.allDate[0].format("YYYYMM")),e.allDate[1]&&(e.endDate=e.allDate[1].format("YYYYMM"))),e.showKs=this.showKs,e.showYs=this.showYs,e.sortColumn=this.sortColumn,e.ascOrDesc=this.ascOrDesc,e.selectedKs=this.selectedKs,e.selectedDoctor=this.selectedDoctor,e},fnChangeDimension:function(e){this.selectedDimension=e;for(var t=this.$refs.infoTableRef.getTableColumn().collectColumn,a=t.splice(1,4),i=0;i<e.length;i++)for(var o=0;o<a.length;o++)e[i]===a[o].own.field&&(a[i]=a.splice(o,1,a[i])[0]);t.splice.apply(t,[1,0].concat((0,s.Z)(a))),this.$refs.infoTableRef.loadColumn(t);var l,r=(0,n.Z)(this.statisticsDimension);try{for(r.s();!(l=r.n()).done;){var c=l.value;this.IsInArray(e,c.value)?this.$refs.infoTableRef.showColumn(this.$refs.infoTableRef.getColumnByField(c.value)):this.$refs.infoTableRef.hideColumn(this.$refs.infoTableRef.getColumnByField(c.value))}}catch(f){r.e(f)}finally{r.f()}this.sortColumn="",this.ascOrDesc="",this.fnQuery()},fnCheckboxChange:function(e){var t=this.$refs.infoTableRef;this.IsInArray(e,"showKs")?(this.showKs="1",this.ksColor={color:"dodgerblue"},t.showColumn(t.getColumnByField("aae386"))):(this.showKs="0",this.ksColor={},t.hideColumn(t.getColumnByField("aae386"))),this.IsInArray(e,"showYs")?(this.showYs="1",this.doctorColor={color:"dodgerblue"},t.showColumn(t.getColumnByField("aaz570"))):(this.showYs="0",this.doctorColor={},t.hideColumn(t.getColumnByField("aaz570"))),this.fnQuery()},sortChangeEvent:function(e){e.column;var t=e.property,a=e.order;this.sortColumn=t,this.ascOrDesc=a,this.fnQuery()},filterChangeEvent:function(e){var t=this,a=e.column,i=(e.property,e.values);e.datas,e.filters,e.$event;"医师"===a.title?this.selectedDoctor=i:this.selectedKs=i,this.$nextTick((function(){t.$refs.infoPageRef.loadData()}))},IsInArray:function(e,t){var a=","+e.join(",")+",";return-1!=a.indexOf(","+t+",")},fnQuery:function(){var e=this;this.baseInfoForm.validateFields(["allDate"],(function(t,a){e.hasError=!!t})),this.hasError||this.$nextTick((function(){e.$refs.infoPageRef.loadData()}))},fnReSet:function(){this.baseInfoForm.resetFields(),this.fnChangeDimension([])},fnBuildColunmn:function(){var e,t=[],a=(0,n.Z)(this.selectedDimension);try{for(a.s();!(e=a.n()).done;){var i=e.value;"aka130"===i?t.push({header:"医疗类别",key:"aka130",width:20}):"aae140"===i?t.push({header:"险种类型",key:"aae140",width:20}):"aae141"===i?t.push({header:"医保类别",key:"aae141",width:20}):"aaa027"===i&&t.push({header:"统筹区域",key:"aaa027",width:20})}}catch(r){a.e(r)}finally{a.f()}"1"===this.showKs&&t.push({header:"科室",key:"aae386",width:20}),"1"===this.showYs&&t.push({header:"医生",key:"aaz570",width:20});var o,s=(0,n.Z)(this.aka063Columns);try{for(s.s();!(o=s.n()).done;){var l=o.value;t.push({header:l.title,key:l.field,width:20})}}catch(r){s.e(r)}finally{s.f()}return t.push({header:"总费用",key:"total_fee",width:20}),t},fnExportExcel:function(){var e=this;if(this.baseInfoForm.validateFields(["allDate"],(function(t,a){e.hasError=!!t})),!this.hasError){var t,a=this.infoPageParams(),i=this.fnBuildColunmn(),o=[],s=(0,n.Z)(this.selectedDimension);try{for(s.s();!(t=s.n()).done;){var l=t.value;"aka130"===l?o.push({codeType:"AKA130",columnKey:"aka130"}):"aae140"===l?o.push({codeType:"AAE140",columnKey:"aae140"}):"aae141"===l&&o.push({codeType:"AAE141",columnKey:"aae141"})}}catch(r){s.e(r)}finally{s.f()}this.Base.submit(null,{url:"costStatistics/exportExcel",data:a,autoValid:!1},{successCallback:function(t){var a={fileName:"费用统计表",sheets:[{name:"worksheet1",column:{complex:!1,columns:i},rows:t.data.exportData,codeList:o}]};e.Base.generateExcel(a)},failCallback:function(t){e.$message.error("医师数据加载失败")}})}},fnCustomHeaderCell:function(e){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px",fontWeight:"bold"}}},filterOption:function(e,t){return t.componentOptions.children[0].text.indexOf(e)>=0}}},y=b,C=a(1001),v=(0,C.Z)(y,i,o,!1,null,"088fb816",null),D=v.exports}}]);
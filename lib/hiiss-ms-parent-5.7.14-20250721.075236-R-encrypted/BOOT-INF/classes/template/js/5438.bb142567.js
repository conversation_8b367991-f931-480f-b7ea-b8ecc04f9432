(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[5438,362],{96723:function(t,e,a){"use strict";a.r(e),a.d(e,{default:function(){return x}});var i=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("div",{staticClass:"fit"},[i("ta-border-layout",{staticStyle:{},attrs:{"layout-type":"fixTop"}},[i("div",{staticStyle:{"padding-top":"10px"},attrs:{slot:"header"},slot:"header"},[i("div",{staticClass:"query-header"},[i("div",{ref:"formBox",staticClass:"query-header-left",style:e.formBoxStyle},[i("ta-form",{attrs:{autoFormCreate:function(e){t.baseInfoForm=e},layout:"horizontal","label-width":"100px",formLayout:!0}},[i("ta-form-item",{attrs:{fieldDecoratorId:"allDate",span:6,label:"时间范围","init-value":e.rangeValue,fieldDecoratorOptions:{rules:[{required:!0,message:"必须选取日期"}]}}},[i("ta-range-picker",{attrs:{"allow-one":!0}},[i("ta-icon",{attrs:{slot:"suffixIcon",type:""},slot:"suffixIcon"})],1)],1),i("ta-form-item",{attrs:{"field-decorator-id":"aae500",label:"审核场景",span:6,fieldDecoratorOptions:{rules:[{required:!0,message:"必须选取审核场景"}]}}},[i("ta-select",{attrs:{placeholder:"请选择审核场景","collection-type":"AAE500","collection-filter":e.filterList,reverseFilter:!0}})],1),i("ta-form-item",{attrs:{"field-decorator-id":"statisticsDimension",label:"统计维度",span:6}},[i("ta-select",{attrs:{placeholder:"统计维度筛选",allowClear:"",mode:"multiple",options:e.statisticsDimension},on:{change:e.fnChangeDimension}})],1),i("ta-form-item",{attrs:{"field-decorator-id":"aka063",label:"费用类别",span:6}},[i("ta-select",{attrs:{placeholder:"费用类别筛选",allowClear:"",options:e.aka063List}})],1),i("ta-form-item",{attrs:{"field-decorator-id":"aae140",label:"险种类型",span:6}},[i("ta-select",{attrs:{placeholder:"险种类型筛选",allowClear:"",collectionType:"AAE140"}})],1),i("ta-form-item",{attrs:{"field-decorator-id":"aaa167",label:"规则名称",span:6}},[i("ta-select",{attrs:{placeholder:"规则名称筛选",allowClear:""}},e._l(e.ruleList,(function(t,a){return i("ta-select-option",{key:t},[e._v(e._s(t))])})),1)],1),i("ta-form-item",{attrs:{"field-decorator-id":"ape893",label:"医护操作",span:6}},[i("ta-select",{attrs:{placeholder:"医护操作筛选",allowClear:"",collectionType:"APE893"}})],1),i("div",{staticStyle:{display:"flex","margin-left":"50px",float:"right"}},[i("ta-button",{attrs:{type:"primary",icon:"search"},on:{click:e.fnQuery}},[e._v("查询")]),i("ta-button",{staticStyle:{"margin-left":"15px"},attrs:{icon:"redo"},on:{click:e.fnReSet}},[e._v("重置")])],1)],1)],1)])]),i("div",{staticClass:"fit content-box"},[i("div",{staticStyle:{height:"35px","margin-left":"8px","margin-top":"8px"}},[i("ta-checkbox-group",{on:{change:e.fnCheckboxChange}},[e.showKsBox?i("ta-checkbox",{style:e.ksColor,attrs:{value:"showKs"}},[e._v("科室")]):e._e(),e.showDoctorBox?i("ta-checkbox",{style:e.doctorColor,attrs:{value:"showYs"}},[e._v("医师")]):e._e()],1)],1),i("div",{staticClass:"content-table"},[i("ta-big-table",{ref:"infoTableRef",attrs:{columns:e.infoColumns,data:e.infoTableData,border:"",height:"auto","highlight-hover-row":"","auto-resize":"","show-overflow":"",resizable:"","sort-config":e.sortConfig,"filter-config":e.filterConfig,"tooltip-config":{enabled:!0,contentMethod:e.showTooltipMethod}},on:{"sort-change":e.sortChangeEvent,"filter-change":e.filterChangeEvent},scopedSlots:e._u([{key:"ksFilter",fn:function(t){var a=t.$panel,s=t.column;return[i("ta-select",{staticStyle:{margin:"10px",width:"120px"},attrs:{options:e.ksList,mode:"multiple",dropdownMatchSelectWidth:!1,"get-popup-container":e.setPopupContainer},on:{change:function(t){return e.fnKsChangeOption(t,s.filters,a)}}})]}},{key:"doctorFilter",fn:function(t){var a=t.$panel,s=t.column;return[i("ta-select",{staticStyle:{margin:"10px",width:"120px"},attrs:{options:e.doctorList,mode:"multiple",dropdownMatchSelectWidth:!1,"get-popup-container":e.setPopupContainer},on:{change:function(t){return e.fnDoctorChangeOption(t,s.filters,a)}}})]}},{key:"totalItem",fn:function(){return[i("span",{staticStyle:{"background-color":"azure",cursor:"pointer"},on:{click:e.showTotalItemDetailChange}},[e.showTotalItemDetail?i("ta-icon",{attrs:{type:"minus-square",theme:"twoTone"}}):i("ta-icon",{attrs:{type:"plus-square",theme:"twoTone"}})],1),i("span",{staticStyle:{"margin-left":"5px"}},[e._v("审核项目总数")])]},proxy:!0},{key:"totalTimes",fn:function(){return[i("span",{staticStyle:{"background-color":"azure",cursor:"pointer"},on:{click:e.showTotalTimesDetailChange}},[e.showTotalTimesDetail?i("ta-icon",{attrs:{type:"minus-square",theme:"twoTone"}}):i("ta-icon",{attrs:{type:"plus-square",theme:"twoTone"}})],1),i("span",{staticStyle:{"margin-left":"5px"}},[e._v("提醒人次")])]},proxy:!0},{key:"totalAmount",fn:function(){return[i("span",{staticStyle:{"background-color":"azure",cursor:"pointer"},on:{click:e.showTotalAmountDetailChange}},[e.showTotalAmountDetail?i("ta-icon",{attrs:{type:"minus-square",theme:"twoTone"}}):i("ta-icon",{attrs:{type:"plus-square",theme:"twoTone"}})],1),i("span",{staticStyle:{"margin-left":"5px"}},[e._v("提醒金额")])]},proxy:!0}])})],1),i("div",{staticClass:"content-box-footer"},[i("ta-pagination",{ref:"infoPageRef",staticStyle:{position:"absolute",right:"100px",bottom:"6px"},attrs:{dataSource:e.infoTableData,params:e.infoPageParams,defaultPageSize:100,pageSizeOptions:["100","200","500","1000","1500"],url:"auditStatistics/queryData"},on:{"update:dataSource":function(t){e.infoTableData=t},"update:data-source":function(t){e.infoTableData=t}}}),i("ta-button",{staticStyle:{position:"absolute",right:"10px",bottom:"6px"},attrs:{icon:"download",type:"primary"},on:{click:function(){return e.fnExportExcel({key:"1"})}}},[e._v("导出")])],1)])])],1)},s=[],o=a(89584),n=a(66347),l=a(48534),r=a(95082),u=(a(36133),a(88412)),c=a(362),d=a(36797),h=a.n(d),f=a(22722),m=a(15076),A=a(55115),p=a(83231),g=a(89770),y=a.n(g),C=a(34810),b=a(73418);y().vfs=C.I.vfs,y().vfs=Object.assign(y().vfs,b.I.vfs),y().fonts={msyh:{normal:"方正黑体简体.TTF",bold:"方正黑体简体.TTF",italics:"方正黑体简体.TTF",bolditalics:"方正黑体简体.TTF"}},A.w3.prototype.Base=Object.assign(A.w3.prototype.Base,(0,r.Z)((0,r.Z)({},f.Z),{},{indexOf:m.Z}));var v=[],w={name:"auditStatistics",components:{TaTitle:u.Z,atientDetails:c["default"]},data:function(){var t=[{type:"seq",title:"序号",width:"60",align:"center"},{title:"人次违规率",width:"150",field:"person_time",align:"center"},{title:"费用违规率",width:"150",field:"cost_time",align:"center"},{title:"费用类别",width:"150",field:"aka063",align:"center",collectionType:"AKA063",sortable:!0,visible:!1},{title:"险种类型",width:"150",field:"aae140",align:"left",collectionType:"AAE140",sortable:!0,visible:!1},{title:"规则名称",width:"150",field:"aaa167",align:"center",sortable:!0,visible:!1},{title:"医护操作",width:"150",field:"ape893",align:"center",collectionType:"APE893",sortable:!0,visible:!1},{title:"科室",width:"150",field:"aae386",align:"center",visible:!1,filters:[{data:""}],customRender:{filter:"ksFilter"},filterMethod:this.fnFilterKsMethod},{title:"医师",width:"150",field:"aac003",align:"center",visible:!1,filters:[],customRender:{filter:"doctorFilter"},filterMethod:this.fnFilterDoctorMethod},{title:"审核项目总数",field:"total_item_flag",align:"center",width:"540",visible:!1,customRender:{header:"totalItem"},children:[{title:"明确违规项目次数",width:"180",field:"violation_item",align:"center",sortable:!0,visible:!1,formatter:"formatThousand"},{title:"高度可疑项目次数",width:"180",field:"highly_suspicious_item",align:"center",sortable:!0,visible:!1,formatter:"formatThousand"},{title:"轻度可疑项目次数",width:"180",field:"slightly_suspicious_item",align:"center",sortable:!0,visible:!1,formatter:"formatThousand"}]},{title:"审核项目总数",field:"total_item",align:"center",width:"300",sortable:!0,visible:!0,formatter:"formatThousand",customRender:{header:"totalItem"}},{title:"提醒人次",field:"advice_times_flag",align:"center",visible:!1,width:"450",customRender:{header:"totalTimes"},children:[{title:"明确违规人次数",width:"150",field:"violation_times",align:"center",sortable:!0,visible:!1,formatter:"formatThousand"},{title:"高度可疑人次数",width:"150",field:"highly_suspicious_times",align:"center",sortable:!0,visible:!1,formatter:"formatThousand"},{title:"轻度可疑人次数",width:"150",field:"slightly_suspicious_times",align:"center",sortable:!0,visible:!1,formatter:"formatThousand"}]},{title:"提醒人次",field:"advice_times",align:"center",width:"300",sortable:!0,visible:!0,formatter:"formatThousand",customRender:{header:"totalTimes"}},{title:"提醒金额",field:"advice_amount_flag",align:"right",headerAlign:"center",visible:!1,width:"450",customRender:{header:"totalAmount"},children:[{title:"明确违规金额",width:"150",field:"violation_amount",headerAlign:"center",align:"right",sortable:!0,visible:!1},{title:"高度可疑金额",width:"150",field:"highly_suspicious_amount",headerAlign:"center",align:"right",sortable:!0,visible:!1},{title:"轻度可疑金额",width:"150",field:"slightly_suspicious_amount",headerAlign:"center",align:"right",sortable:!0,visible:!1}]},{title:"提醒金额",field:"advice_amount",align:"right",headerAlign:"center",width:"300",sortable:!0,visible:!0,customRender:{header:"totalAmount"}},{title:"可能被扣款金额",field:"possible_amount",align:"right",headerAlign:"center",minWidth:"150",sortable:!0}];return{col:{xs:1,sm:2,md:2,lg:6,xl:6,xxl:4},headers:{authorization:"authorization-text"},rangeValue:[this.Base.getMoment((new Date).toISOString().slice(0,8)+"-01","YYYY-MM-DD"),this.Base.getMoment((new Date).toISOString().slice(0,10),"YYYY-MM-DD")],aka063List:[],statisticsDimension:[],selectedDimension:[],ruleList:[],showKs:"0",showYs:"0",infoColumns:t,infoTableData:v,formShowAll:!0,showTotalItemDetail:!1,showTotalTimesDetail:!1,showTotalAmountDetail:!1,sortConfig:{trigger:"default",remote:!0},sortColumn:"",filterConfig:{remote:!0},ascOrDesc:"",hasError:!1,akb020:"",aaz307:"",permissions:null,ksList:[],doctorList:[],selectedKs:[],selectedDoctor:[],ksColor:{},doctorColor:{},showKsBox:!1,showDoctorBox:!1,filterList:""}},mounted:function(){var t=this;return(0,l.Z)(regeneratorRuntime.mark((function e(){var a,i;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,p.Z.permissionCheck();case 2:t.permissions=e.sent,t.permissions.aaz307Disable||(t.showKsBox=!0),a=[],i=["16","2","17","18","3","4","7","5","8","6"],p.Z.getAa01AAE500StartStop({aaz499s:JSON.stringify(i)},(function(e){a=e.data.aae500List,t.filterList=a.join(","),t.baseInfoForm.setFieldsValue({aae500:a[0].toString()})})),t.showDoctorBox=!0,t.fnQueryStatisticsDimension(),t.fnQueryAka063(),t.fnqueryRule(),t.fnQueryDept(),t.fnQueryDocter();case 13:case"end":return e.stop()}}),e)})))()},computed:{formBoxStyle:function(){return this.formShowAll?{height:"auto"}:{height:"50px"}}},methods:{showTooltipMethod:function(t){var e=t.type,a=t.column,i=(t.row,t.items,t._columnIndex,a.property);if("header"===e)return"person_time"==i?"违规人次/总人次":"cost_time"==i?"违规费用/总费用":""},setPopupContainer:function(t){return t.parentNode},fnKsChangeOption:function(t,e,a){var i=this;e.forEach((function(e){i.IsInArray(t,e.value)?a.changeOption(e.value,!0,e):a.changeOption(e.value,!1,e)}))},fnDoctorChangeOption:function(t,e,a){var i=this;e.forEach((function(e){i.IsInArray(t,e.value)?a.changeOption(e.value,!0,e):a.changeOption(e.value,!1,e)}))},formShowAllChange:function(){this.formShowAll=!this.formShowAll},showTotalItemDetailChange:function(){this.showTotalItemDetail=!this.showTotalItemDetail;var t=this.$refs.infoTableRef;this.showTotalItemDetail?(t.showColumn(t.getColumnByField("violation_item")),t.showColumn(t.getColumnByField("highly_suspicious_item")),t.showColumn(t.getColumnByField("slightly_suspicious_item")),t.hideColumn(t.getColumnByField("total_item"))):(t.hideColumn(t.getColumnByField("violation_item")),t.hideColumn(t.getColumnByField("highly_suspicious_item")),t.hideColumn(t.getColumnByField("slightly_suspicious_item")),t.showColumn(t.getColumnByField("total_item")))},showTotalTimesDetailChange:function(){this.showTotalTimesDetail=!this.showTotalTimesDetail;var t=this.$refs.infoTableRef;this.showTotalTimesDetail?(t.showColumn(t.getColumnByField("violation_times")),t.showColumn(t.getColumnByField("highly_suspicious_times")),t.showColumn(t.getColumnByField("slightly_suspicious_times")),t.hideColumn(t.getColumnByField("advice_times"))):(t.hideColumn(t.getColumnByField("violation_times")),t.hideColumn(t.getColumnByField("highly_suspicious_times")),t.hideColumn(t.getColumnByField("slightly_suspicious_times")),t.showColumn(t.getColumnByField("advice_times")))},showTotalAmountDetailChange:function(){this.showTotalAmountDetail=!this.showTotalAmountDetail;var t=this.$refs.infoTableRef;this.showTotalAmountDetail?(t.showColumn(t.getColumnByField("violation_amount")),t.showColumn(t.getColumnByField("highly_suspicious_amount")),t.showColumn(t.getColumnByField("slightly_suspicious_amount")),t.hideColumn(t.getColumnByField("advice_amount"))):(t.hideColumn(t.getColumnByField("violation_amount")),t.hideColumn(t.getColumnByField("highly_suspicious_amount")),t.hideColumn(t.getColumnByField("slightly_suspicious_amount")),t.showColumn(t.getColumnByField("advice_amount")))},fnQueryStatisticsDimension:function(){var t=this;this.Base.submit(null,{url:"auditStatistics/queryStatisticsDimension",data:{}},{successCallback:function(e){t.statisticsDimension=e.data.statisticsDimension},failCallback:function(e){t.$message.error("统计维度加载失败")}})},fnQueryDept:function(t){var e=this;this.akb020=t||"",this.Base.submit(null,{url:"miimCommonRead/queryDepartRpcList",data:{akb020:this.akb020},autoValid:!1},{successCallback:function(t){e.ksList=t.data.resultData,e.$refs.infoTableRef.setFilter(e.$refs.infoTableRef.getColumnByField("aae386"),e.ksList),e.$refs.infoTableRef.updateData()},failCallback:function(t){e.$message.error("科室数据加载失败")}})},fnQueryDocter:function(t){var e=this,a={akb020:this.akb020};t&&(a.departCode=t),this.Base.submit(null,{url:"miimCommonRead/queryDoctorRpcList",data:a,autoValid:!1},{successCallback:function(t){e.doctorList=t.data.resultData,e.$refs.infoTableRef.setFilter(e.$refs.infoTableRef.getColumnByField("aac003"),e.doctorList),e.$refs.infoTableRef.updateData()},failCallback:function(t){e.$message.error("医师数据加载失败")}})},fnQueryAka063:function(){var t=this;this.Base.submit(null,{url:"auditStatistics/queryAka063",data:{}},{successCallback:function(e){t.aka063List=e.data.aka063List},failCallback:function(e){t.$message.error("院内收费类别列表加载失败")}})},fnqueryRule:function(){var t=this;this.Base.submit(null,{url:"auditStatistics/queryRule",data:{}},{successCallback:function(e){t.ruleList=e.data.ruleList},failCallback:function(e){t.$message.error("统计维度加载失败")}})},disabledStartDate:function(t){return t.format("YYYY-MM-DD")>this.baseInfoForm.getFieldValue("endDate").format("YYYY-MM-DD")},disabledEndDate:function(t){return t=t.format("YYYY-MM-DD"),t>h()().startOf("day").format("YYYY-MM-DD")||t<this.baseInfoForm.getFieldValue("startDate").format("YYYY-MM-DD")},infoPageParams:function(){var t=this.baseInfoForm.getFieldsValue();return t.allDate&&(t.allDate[0]&&(t.startDate=t.allDate[0].format("YYYY-MM-DD HH:mm:ss")),t.allDate[1]&&(t.endDate=t.allDate[1].format("YYYY-MM-DD HH:mm:ss"))),t.showKs=this.showKs,t.showYs=this.showYs,t.sortColumn=this.sortColumn,t.ascOrDesc=this.ascOrDesc,t.selectedKs=this.selectedKs,t.selectedDoctor=this.selectedDoctor,t.dateFlag="audit",t},fnCheckboxChange:function(t){this.IsInArray(t,"showKs")?(this.showKs="1",this.ksColor={color:"dodgerblue"},this.$refs.infoTableRef.showColumn(this.$refs.infoTableRef.getColumnByField("aae386"))):(this.showKs="0",this.ksColor={},this.$refs.infoTableRef.hideColumn(this.$refs.infoTableRef.getColumnByField("aae386"))),this.IsInArray(t,"showYs")?(this.showYs="1",this.doctorColor={color:"dodgerblue"},this.$refs.infoTableRef.showColumn(this.$refs.infoTableRef.getColumnByField("aac003"))):(this.showYs="0",this.doctorColor={},this.$refs.infoTableRef.hideColumn(this.$refs.infoTableRef.getColumnByField("aac003"))),this.fnQuery()},sortChangeEvent:function(t){t.column;var e=t.property,a=t.order;this.sortColumn=e,this.ascOrDesc=a,this.fnQuery()},filterChangeEvent:function(t){var e=this,a=t.column,i=(t.property,t.values);t.datas,t.filters,t.$event;"医师"===a.title?this.selectedDoctor=i:this.selectedKs=i,this.$nextTick((function(){e.$refs.infoPageRef.loadData()}))},fnFilterKsMethod:function(t){var e=t.option,a=t.row;return a.aae386===e.data},fnFilterDoctorMethod:function(t){var e=t.value,a=t.row;t.column;return(0,m.Z)(a.aaz263,e)>-1},IsInArray:function(t,e){var a=","+t.join(",")+",";return-1!=a.indexOf(","+e+",")},fnQuery:function(){var t=this;this.baseInfoForm.validateFields(["allDate","aae500"],(function(e,a){t.hasError=!!e})),this.hasError||this.$nextTick((function(){t.$refs.infoPageRef.loadData()}))},fnReSet:function(){this.baseInfoForm.resetFields(),this.baseInfoForm.setFieldsValue({aae500:"1"}),this.fnChangeDimension([]),this.infoTableData=[]},fnBuildColunmn:function(){var t,e=[],a=(0,n.Z)(this.selectedDimension);try{for(a.s();!(t=a.n()).done;){var i=t.value;"aka063"===i?e.push({header:"费用类别",key:"aka063",width:20}):"aae140"===i?e.push({header:"险种类型",key:"aae140",width:20}):"aaa167"===i?e.push({header:"规则名称",key:"aaa167",width:20}):"ape893"===i&&e.push({header:"医护操作",key:"ape893",width:20})}}catch(s){a.e(s)}finally{a.f()}return"1"===this.showKs&&e.push({header:"科室",key:"aae386",width:20}),"1"===this.showYs&&e.push({header:"医生",key:"aac003",width:20}),e.push({header:"明确违规项目次数",key:"violation_item",width:20}),e.push({header:"高度可疑项目次数",key:"highly_suspicious_item",width:20}),e.push({header:"轻度可疑项目次数",key:"slightly_suspicious_item",width:20}),e.push({header:"审核项目总数",key:"total_item",width:20}),e.push({header:"明确违规人次数",key:"violation_times",width:20}),e.push({header:"高度可疑人次数",key:"highly_suspicious_times",width:20}),e.push({header:"轻度可疑人次数",key:"slightly_suspicious_times",width:20}),e.push({header:"提醒人次",key:"advice_times",width:20}),e.push({header:"明确违规金额",key:"violation_amount",width:20}),e.push({header:"高度可疑金额",key:"highly_suspicious_amount",width:20}),e.push({header:"轻度可疑金额",key:"slightly_suspicious_amount",width:20}),e.push({header:"提醒金额",key:"advice_amount",width:20}),e.push({header:"可能被扣款金额",key:"ape804",width:20}),e},fnExportExcel:function(t){var e=this;if(this.baseInfoForm.validateFields(["allDate","aae500"],(function(t,a){e.hasError=!!t})),!this.hasError){var a,i=this.infoPageParams(),s=this.fnBuildColunmn(),o=[],l=(0,n.Z)(this.selectedDimension);try{for(l.s();!(a=l.n()).done;){var r=a.value;"aae140"===r?o.push({codeType:"AAE140",columnKey:"aae140"}):"ape893"===r&&o.push({codeType:"APE893",columnKey:"ape893"})}}catch(u){l.e(u)}finally{l.f()}this.Base.submit(null,{url:"auditStatistics/exportExcel",data:i,autoValid:!1},{successCallback:function(a){var i={fileName:"审核结果统计表",sheets:[{name:"worksheet1",column:{complex:!1,columns:s},rows:a.data.exportData,codeList:o}]};"1"===t.key&&e.Base.generateExcel(i),"2"===t.key&&e.exportPdf(i)},failCallback:function(t){e.$message.error("医师数据加载失败")}})}},exportPdf:function(t){var e=this;return(0,l.Z)(regeneratorRuntime.mark((function a(){var i,s,n,l,r,u,c,d,h,f,m,A;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return i=t.sheets[0].codeList,s=new Map,n=i.filter((function(t){return t.codeType})),l=n.map((function(t){return e.Base.asyncGetCodeData(t.codeType)})),a.next=6,Promise.all(l);case 6:r=a.sent,n.forEach((function(t,e){s.set(t.columnKey,r[e])})),u=i.filter((function(t){return t.customCollection})),u.forEach((function(t){return s.set(t.columnKey,t.customCollection)})),c=[],d=t.sheets[0].column.columns,h=t.sheets[0].rows,f=d.map((function(t){return"number"===typeof t.pdfWidth?t.pdfWidth:70})),c.push(d.map((function(t){return t.header}))),m=f.reduce((function(t,e){return t+e}),0),c.push.apply(c,(0,o.Z)(h.map((function(t){var a=[];return d.forEach((function(i){var o=t[i.key];void 0===o&&(o="");var n=s.get(i.key);if(n)if(n instanceof Function){var l={value:o};n(l),o=l.value}else o=e.Base.getCodeLabel(n,o);a.push({text:o})})),a})))),A={pageSize:{width:m+200,height:300},pageOrientation:"landscape",content:[{table:{dontBreakRows:!0,headerRows:1,widths:f,body:c},style:"cnFont"}],styles:{cnFont:{font:"msyh"}}},y().createPdf(A).download(t.fileName+".pdf");case 19:case"end":return a.stop()}}),a)})))()},fnChangeDimension:function(t){this.selectedDimension=t;for(var e=this.$refs.infoTableRef.getTableColumn().collectColumn,a=e.splice(1,4),i=0;i<t.length;i++)for(var s=0;s<a.length;s++)t[i]===a[s].own.field&&(a[i]=a.splice(s,1,a[i])[0]);e.splice.apply(e,[1,0].concat((0,o.Z)(a))),this.$refs.infoTableRef.loadColumn(e);var l,r=(0,n.Z)(this.statisticsDimension);try{for(r.s();!(l=r.n()).done;){var u=l.value;this.IsInArray(t,u.value)?this.$refs.infoTableRef.showColumn(this.$refs.infoTableRef.getColumnByField(u.value)):this.$refs.infoTableRef.hideColumn(this.$refs.infoTableRef.getColumnByField(u.value))}}catch(c){r.e(c)}finally{r.f()}this.sortColumn="",this.ascOrDesc="",this.fnQuery()},fnCustomHeaderCell:function(t){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px",fontWeight:"bold"}}},filterOption:function(t,e){return e.componentOptions.children[0].text.indexOf(t)>=0}}},k=w,D=a(1001),T=(0,D.Z)(k,i,s,!1,null,"6c767817",null),x=T.exports},362:function(t,e,a){"use strict";a.r(e),a.d(e,{default:function(){return h}});var i=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"fit"},[i("div",[i("span",{staticStyle:{"font-weight":"bold","font-size":"14px",color:"#333333","margin-left":"10px"}},[i("ta-badge",{attrs:{color:t.badgeColor,text:"项目："}}),t._v(" "+t._s(t.fyRecord.ake002)+" "),i("span",{staticStyle:{"margin-right":"80px"}}),i("ta-badge",{attrs:{color:t.badgeColor,text:"明细总数量："}}),t._v(" "+t._s(t.fyRecord.akc226)+" "),(1==t.fyRecord.ykz020&&t.fyRecord.aae500&&t.fyRecord.aae500,i("span",[i("span",{staticStyle:{"margin-right":"80px"}}),i("ta-badge",{attrs:{color:t.badgeColor,text:"建议扣除数量："}}),t._v(" "+t._s(t.fyRecord.ape805)+" ")],1))],1),i("div",{staticStyle:{"margin-top":"0px","border-top":"0px solid #cccccc","padding-top":"5px"}},[i("ta-tabs",{attrs:{defaultActiveKey:"1"}},t._l(t.doubtList,(function(e,s){return i("ta-tab-pane",{key:s+1},[i("span",{attrs:{slot:"tab"},on:{click:function(a){return t.cardChange(e.id)}},slot:"tab"},[i("img",{attrs:{src:"1"===e.ykz020?a(60037):a(92206)}}),i("span",{staticClass:"tab-title",on:{click:function(a){return t.cardChange(e.id)}}},[t._v(t._s(e.aaa167))])]),i("p",{staticClass:"tab-content",on:{click:function(a){return t.cardChange(e.id)}}},[i("span",{staticClass:"tab-text-label"},[t._v("规则来源：")]),i("span",{staticClass:"tab-text"},[t._v(t._s(e.source))]),i("br"),i("span",{staticClass:"tab-text-label"},[t._v("限制条件：")]),i("span",{staticClass:"tab-text"},[t._v(t._s(e.ykz018))]),i("a",{directives:[{name:"show",rawName:"v-show",value:t.isRuleQuery,expression:"isRuleQuery"}],staticStyle:{"margin-left":"10px"},on:{click:function(a){return t.ruleDetails(e)}}},[t._v(">>规则详情")])])])})),1)],1)]),i("div",{staticClass:"audit-container"},[t._m(0),i("div",{staticClass:"audit-content",attrs:{id:"wai"}},[i("div",{staticClass:"divleft"},[i("div",{staticClass:"header"},[t._v("审核路径")]),i("table",{staticClass:"audit-table"},[t._m(1),i("tbody",t._l(t.auditPathList,(function(e,a){return i("tr",{key:a,staticClass:"audit-row"},[i("td",{staticClass:"audit-index"},[t._v(t._s(a+1))]),i("td",{staticClass:"audit-detail"},t._l(e.nodeInfoList,(function(s,o){return i("span",{key:o,staticClass:"audit-node-container"},[o>0&&o<e.nodeInfoList.length?i("span",[t._v("——")]):t._e(),i("span",{staticClass:"audit-node",style:{color:"3"===s.ykz020?t.colors[0]:t.colors[s.ykz020]},attrs:{tabindex:a+1},on:{click:function(e){return t.nodeChange(s)}}},[t._v(" "+t._s(s.ykz010)+" "),i("span",[t._v("("+t._s(s.ykz002)+")")])])])})),0)])})),0)])]),i("div",{staticClass:"divright"},[i("div",{staticClass:"header"},[t._v("节点详情")]),i("div",{staticClass:"right-content"},[i("div",{staticClass:"right-item"},[t._m(2),i("div",{staticClass:"right-item-content",domProps:{innerHTML:t._s(t.nodeDetail.ykz006)}})]),i("div",{staticClass:"right-item"},[t._m(3),i("div",{staticClass:"right-item-content",domProps:{innerHTML:t._s(t.nodeDetail.ykz008)}})]),i("div",{staticClass:"right-item"},[t._m(4),i("div",{staticClass:"right-item-content",domProps:{innerHTML:t._s(t.nodeDetail.ykz021)}})]),i("div",{staticClass:"right-item"},[t._m(5),i("div",{staticClass:"right-item-content",domProps:{innerHTML:t._s(t.nodeDetail.ykz022)}})])])])])])])},s=[function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"audit-info"},[i("img",{staticClass:"info-icon",attrs:{src:a(67488)}}),i("span",[t._v("审核结果信息")]),i("span",{staticClass:"status-red"},[t._v("红色")]),t._v("节点表示不通过， "),i("span",{staticClass:"status-green"},[t._v("绿色")]),t._v("节点表示通过 ")])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("thead",[a("tr",[a("th",{staticClass:"audit-index"},[t._v("序号")]),a("th",{staticClass:"audit-details"},[t._v("路径审核详情")])])])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"right-item-title"},[t._v("机审"),a("br"),t._v("记录")])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"right-item-title"},[t._v("引导"),a("br"),t._v("信息")])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"right-item-title"},[t._v("人工"),a("br"),t._v("操作")])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"right-item-title"},[t._v("人工"),a("br"),t._v("审核"),a("br"),t._v("理由")])}],o=a(95082),n=a(66353),l=["id"],r={name:"atientDetails",props:{fyRecord:Object},data:function(){return{visible:!1,doubtList:[],auditPathList:[],nodeDetail:{},isRuleQuery:!1,colors:["#D75457","#46C26D","#3693F3"],aaz217:"",akb020:"",flag:"",badgeColor:"#3693F3"}},mounted:function(){this.handleShowInfo(this.fyRecord)},methods:{ruleDetails:function(t){var e,a,i=this;(null===(e=t.nodeDetailVoList)||void 0===e?void 0:e.length)>0?this.Base.submit(null,{url:"mttRuleFind/queryRuleid",data:{ykz108:t.nodeDetailVoList[0].ykz108}},{successCallback:function(e){a=e.data.data[0].ruleid,i.Base.openTabMenu({id:t.ykz018,name:"【"+t.ykz018+"】规则查询",url:"mttRuleConfig.html#/ruleFind?ykz010=".concat(t.ykz018,"&ruleid=").concat(a),refresh:!1})},failCallback:function(t){i.$message.error("规则ID失败")}}):this.$message.error("审核详情缺少节点信息！请检查审核详情！")},handleShowInfo:function(t){var e=this;this.fyRecord=t,this.visible=!0,this.Base.submit(null,{url:"miimCommonRead/queryDoubtList",data:{aaz217:t.aaz217,aaz213:t.aaz213,aae500:t.aae500,type:t.type},autoValid:!0,autoQs:!1},{successCallback:function(t){t.data.list.length>0&&("monitor"===t.data.flag?t.data.list.forEach((function(t,e){for(var a in t.auditPathList){var i=t.auditPathList[a].nodeInfoList.every((function(t){return"1"===t.ykz020}));i&&(t.ykz020="1")}})):t.data.list.forEach((function(t,e){for(var a in t.auditPathList){t.auditPathList[a].nodeInfoList.forEach((function(t,e){t.ykz020=t.ykz173,t.ykz021?t.ykz021="1"===t.ykz021?"是":"否":t.ykz021="无"}));var i=t.auditPathList[a].nodeInfoList.every((function(t){return"1"===t.ykz173}));i&&(t.ykz020="1")}})),t.data.list.sort((function(t,e){var a=parseInt(t.ykz020,10),i=parseInt(e.ykz020,10);return 0===a||3===a?-1:0===i||3===i?1:a-i}))),e.doubtList=t.data.list.map((function(t,e){t.id;var a=(0,n.Z)(t,l);return(0,o.Z)({id:e+1},a)})),e.auditPathList=[],e.nodeDetail={},e.doubtList.length>0&&(e.auditPathList=e.doubtList[0].auditPathList),t.data.ruleQuery&&(e.isRuleQuery="y"==t.data.ruleQuery.toLowerCase())},failCallback:function(t){}})},cardChange:function(t){var e=t-1;this.auditPathList=this.doubtList[e].auditPathList},nodeChange:function(t){this.nodeDetail=t}}},u=r,c=a(1001),d=(0,c.Z)(u,i,s,!1,null,"e9de457e",null),h=d.exports},83231:function(t,e,a){"use strict";var i=a(48534);a(36133);function s(t){return t?t.akb020&&t.aaz263?"personal":t.akb020&&t.aaz309&&t.aaz307?"group":t.akb020&&t.aaz307?"department":t.akb020?"hospital":"all":"all"}function o(t){return n.apply(this,arguments)}function n(){return n=(0,i.Z)(regeneratorRuntime.mark((function t(e){var a,i,o,n,l,r,u,c,d,h,f,m;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Base.submit(null,{url:"/miimCommonRead/queryDataPermission",showPageLoading:!1,data:{jobnumber:e},autoValid:!0});case 2:return a=t.sent,i=new Set,o=new Set,a.data.permission.forEach((function(t){var e=s(t);"hospital"===e&&i.add(t.akb020),"department"===e&&o.add(t.aaz307)})),n=a.data.permission.filter((function(t){return"department"===s(t)||!o.has(t.aaz307)})).filter((function(t){return"hospital"===s(t)||!i.has(t.akb020)})),l=new Set(n.map((function(t){return t.akb020})).filter((function(t){return null!=t}))),r=new Set(n.map((function(t){return t.aaz307})).filter((function(t){return null!=t}))),u=new Set(n.map((function(t){return t.aaz309})).filter((function(t){return null!=t}))),c=new Set(n.map((function(t){return t.aaz263})).filter((function(t){return null!=t}))),d=!1,h=!1,f=!1,m=!1,1===l.size&&(d=!0),1===r.size&&1===l.size&&(h=!0),1===r.size&&1===l.size&&1===u.size&&(f=!0),1===l.size&&0===r.size&&1===c.size&&(m=!0),t.abrupt("return",{akb020Set:l,aaz307Set:r,aaz263Set:c,aaz309Set:u,akb020Disable:d,aaz307Disable:h,aaz263Disable:m,aaz309Disable:f});case 20:case"end":return t.stop()}}),t)}))),n.apply(this,arguments)}function l(t,e){Base.submit(null,{url:"miimCommonRead/getAa01AAE500StartStop",data:t},{successCallback:function(t){return e(t)}})}function r(t,e){Base.submit(null,{url:"miimCommonRead/insertTableColumShow",data:t,autoValid:!1},{successCallback:function(t){return e("success")},failCallback:function(t){return e("fail")}})}var u={zbx01:{footer:function(){return"统筹金总额"}},zbx02:{footer:function(){return"基金支出总额"}},zbx03:{footer:function(){return"总费用"}},zbx04:{footer:function(){return"总住院日"}}};e["Z"]={permissionCheck:o,getAa01AAE500StartStop:l,insertTableColumShow:r,props:u,moneyNumFormat:function(t){var e=t.cellValue;return e||"—"}}},92206:function(t){"use strict";t.exports="data:image/png;base64,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"},60037:function(t){"use strict";t.exports="data:image/png;base64,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"},67488:function(t){"use strict";t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAEKADAAQAAAABAAAAEAAAAAA0VXHyAAAByklEQVQ4EZVSOywEYRCe+fc8CvHIrbg7RK7xjAQVodBRaCQqhYLCm4hEIQoRWonGEYVOiUIhSgoaQYHQkHgccYsIcW7djvmPvfzn7iRmk51/v/m++eexCEmsYpJSjTvDhwB1/gW9PAkNHIkCntEX/dEfWONYAyUiKFhcAle3UW69fmwwx6vwIkd3z8MhAB5rAGPXC/qNBIVKcvcZzYC0y1icOMJDmOeKWsJAB/n9L8USQyJC78hzVigY7rAIZgGIL4i1u8VcHsW35Q8aBeGQtQ2ImO5ylglPb2An+P75ZBHNJRLbQullC8LEbA0crUBQFLo3ugQR1Kukv84EeGCCNXOzmHOECFusbY+ZwV9iGRMCl7jK2h/ePvuSfyUAi+8lfhT7VwICq5tntxfRE9VwC+eCAf5h0FSSJj2yoIqXNl4w8FjJ82gSCCvR9bh7AxNkwRT3GMXsTOoaCwcePOYn7CCgluZylkZb8Pv0adSgjUVvtvC39/QYnaaJJ0iQiUhNl5MYjCaQZP+8vqppjgZu6+q3WH7zDIb4vSlSRfWtL/dMYnHlStA7/Jr3/hFcB/pemdqCjKsWU4EduJjLuM9IcTZy/mWu5tTGE/kv0KybhGEzosQAAAAASUVORK5CYII="},55382:function(){},61219:function(){}}]);
"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[3204],{21096:function(t,e,a){a.r(e),a.d(e,{default:function(){return w}});var i=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("div",{staticClass:"fit"},[i("ta-border-layout",{staticStyle:{position:"relative"},attrs:{"show-padding":!1,layout:{footer:"0px"}}},[i("ta-card",{staticStyle:{height:"190px"},attrs:{bordered:!1}},[i("div",{staticClass:"title"},[e._v("查询条件")]),i("ta-form",{attrs:{autoFormCreate:function(e){t.searchForm=e}}},[i("ta-row",[i("ta-col",{attrs:{span:4}},[i("ta-form-item",{attrs:{label:"三目编码",fieldDecoratorId:"ake001"}},[i("ta-input",{attrs:{placeholder:"请输入"}})],1)],1),i("ta-col",{attrs:{span:4}},[i("ta-form-item",{attrs:{label:"三目名称",fieldDecoratorId:"ake002"}},[i("ta-input",{attrs:{placeholder:"请输入"}})],1)],1),i("ta-col",{attrs:{span:4}},[i("ta-form-item",{attrs:{label:"三目类型",fieldDecoratorId:"ake003"}},[i("ta-select",{attrs:{"allow-clear":!0,"collection-type":"MAKE003",placeholder:"请选择"}})],1)],1),i("ta-col",{attrs:{span:4}},[i("ta-form-item",{attrs:{label:"限制条件",fieldDecoratorId:"yke011"}},[i("ta-input",{attrs:{placeholder:"请输入"}})],1)],1),i("ta-col",{attrs:{span:4}},[i("ta-form-item",{attrs:{label:"有效标志",fieldDecoratorId:"aae100"}},[i("ta-select",{attrs:{placeholder:"请选择","allow-clear":!0,"collection-type":"EFFECTIVE"}})],1)],1),i("ta-col",{attrs:{span:4}},[i("ta-button",{staticStyle:{"margin-left":"10px"},on:{click:e.resetSearch}},[e._v("重置")]),i("ta-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary"},on:{click:e.doSearch}},[e._v("查询")])],1)],1)],1)],1),i("ta-card",{attrs:{bordered:!1}},[i("div",{staticClass:"title"},[e._v(" 本地目录维护 "),i("div",{staticStyle:{float:"right"}},[i("ta-button",{on:{click:e.downloadTemplate}},[e._v("模板下载")]),i("ta-button",{on:{click:function(t){return e.openImport()}}},[e._v("导入")]),i("ta-button",{on:{click:e.doExport}},[e._v("导出")]),i("ta-button",{attrs:{type:"primary"},on:{click:e.openAddForm}},[e._v("新增")]),i("ta-dropdown",[i("ta-menu",{attrs:{slot:"overlay"},slot:"overlay"},[i("ta-menu-item",[i("ta-popconfirm",{attrs:{title:"确认启用?","cancel-text":"取消","ok-text":"确认"},on:{confirm:function(t){return e.disableOrEnable("1")}}},[i("ta-icon",{attrs:{type:"check-circle"}}),i("span",{staticClass:"mg-l12"},[e._v("启用")])],1)],1),i("ta-menu-divider"),i("ta-menu-item",[i("ta-popconfirm",{attrs:{title:"确认禁用?","cancel-text":"取消","ok-text":"确认"},on:{confirm:function(t){return e.disableOrEnable("0")}}},[i("ta-icon",{attrs:{type:"stop"}}),i("span",{staticClass:"mg-l12"},[e._v("禁用")])],1)],1)],1),i("ta-button",{staticStyle:{float:"right","margin-right":"10px"}},[e._v(" 批量操作 "),i("ta-icon",{attrs:{type:"down"}})],1)],1)],1),i("div",{staticStyle:{clear:"both"}})]),i("ta-table",{ref:"ruleTable",attrs:{"show-checkbox":!0,size:"small",haveSn:!0,columns:e.columns,dataSource:e.dataList,scroll:{x:"100%",y:385},bordered:"","row-key":"ake001"},on:{"update:columns":function(t){e.columns=t}},scopedSlots:e._u([{key:"operation",fn:function(t,a){return i("div",{},[i("a",{staticStyle:{"margin-left":"5px"},on:{click:function(t){return e.openEditForm(a)}}},[e._v("编辑")]),"1"==a.aae100?i("ta-popconfirm",{attrs:{title:"确定禁用?"},on:{confirm:function(t){return e.disableOrEnableSingle(a,"0")}}},[i("a",{staticStyle:{"margin-left":"5px"},on:{click:function(t){}}},[e._v("禁用")])]):i("ta-popconfirm",{attrs:{title:"确定启用?"},on:{confirm:function(t){return e.disableOrEnableSingle(a,"1")}}},[i("a",{staticStyle:{"margin-left":"5px"},on:{click:function(t){}}},[e._v("启用")])]),i("a",{staticStyle:{"margin-left":"5px"},on:{click:function(t){return e.openLog(a)}}},[e._v("日志")])],1)}}])}),i("div",[i("ta-pagination",{ref:"rulePager",staticClass:"page",attrs:{dataSource:e.dataList,params:e.pageParams,url:e.moduleUrl+"queryPage",defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"]},on:{"update:dataSource":function(t){e.dataList=t},"update:data-source":function(t){e.dataList=t}},model:{value:e.currentPage,callback:function(t){e.currentPage=t},expression:"currentPage"}})],1)],1)],1),i("ta-modal",{attrs:{title:"1"==e.ruleEnableStatus?"启用":"停用"},model:{value:e.blnRuleReasonVisible,callback:function(t){e.blnRuleReasonVisible=t},expression:"blnRuleReasonVisible"}},[i("span",{staticStyle:{color:"red"}},[e._v("*")]),e._v(e._s("1"==e.ruleEnableStatus?"启用":"停用")+"意见："),i("ta-textarea",{attrs:{rows:4,placeholder:"请输入"},model:{value:e.descValue,callback:function(t){e.descValue=t},expression:"descValue"}}),i("div",{attrs:{slot:"footer"},slot:"footer"},[i("ta-button",{attrs:{type:"primary"},on:{click:e.doDisableOrEnable}},[e._v("确定")]),i("ta-button",{on:{click:function(t){e.blnRuleReasonVisible=!1}}},[e._v("取消")])],1)],1),i("ta-modal",{attrs:{title:"上传数据",visible:e.blnItemImportVisible,"destroy-on-close":"",width:"500px",height:"150px","destroy-on-close":!0},on:{ok:e.handleImport,cancel:function(t){e.blnItemImportVisible=!1}}},[i("ta-form",{attrs:{autoFormCreate:function(e){t.form=e},"label-width":"120px"}},[i("ta-form-item",{attrs:{label:"选择文件",fieldDecoratorId:"file",require:{message:"请选择文件!"}}},[i("ta-upload",{attrs:{"file-list":e.fileList,"before-upload":e.beforeUpload,remove:e.handleRemove}},[i("ta-button",[e._v("上传excel文件")])],1)],1)],1),e.addImportVisible?i("addImport",{ref:"addImport",attrs:{"result-data":e.resultData,dataType:e.dataType},on:{close:e.closeAddImportModel}}):e._e()],1),i("ta-modal",{attrs:{width:1e3,"destroy-on-close":!0,title:"操作日志"},on:{ok:function(t){e.blnLogVisible=!1}},model:{value:e.blnLogVisible,callback:function(t){e.blnLogVisible=t},expression:"blnLogVisible"}},[i("item-log",{attrs:{record:e.editRecord}})],1),i("ta-modal",{attrs:{width:500,"destroy-on-close":!0,title:"1"==e.editType?"新增三目":"编辑三目"},on:{ok:function(t){e.blnEditVisible=!1}},model:{value:e.blnEditVisible,callback:function(t){e.blnEditVisible=t},expression:"blnEditVisible"}},[i("ta-form",{attrs:{"auto-form-create":function(e){return t.editForm=e}}},[i("ta-row",[i("ta-col",{attrs:{span:24}},[i("ta-form-item",{attrs:{label:"三目编码",fieldDecoratorId:"ake001",require:{message:"三目编码不能为空"}}},[i("ta-input",{attrs:{disabled:"2"==e.editType,placeholder:"请输入","allow-clear":!0}})],1)],1),i("ta-col",{attrs:{span:24}},[i("ta-form-item",{attrs:{label:"三目名称",fieldDecoratorId:"ake002",require:{message:"三目名称不能为空"}}},[i("ta-input",{attrs:{placeholder:"请输入","allow-clear":!0}})],1)],1),i("ta-col",{attrs:{span:24}},[i("ta-form-item",{attrs:{label:"三目类型",fieldDecoratorId:"ake003",require:{message:"三目类型不能为空"}}},[i("ta-select",{attrs:{placeholder:"请选择","collection-type":"MAKE003","allow-clear":!0}})],1)],1),i("ta-col",{attrs:{span:24}},[i("ta-form-item",{attrs:{label:"限制条件",fieldDecoratorId:"yke011"}},[i("ta-textarea",{attrs:{rows:4,placeholder:"请输入"}})],1)],1)],1)],1),i("div",{attrs:{slot:"footer"},slot:"footer"},[i("ta-button",{on:{click:function(t){e.blnEditVisible=!1}}},[e._v("取消")]),i("ta-button",{attrs:{type:"primary"},on:{click:e.doSave}},[e._v("确定")])],1)],1)],1)},l=[],r=a(95082),s=a(89584),o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("ta-modal",{attrs:{title:"result"===t.dataType?"上传结果":"错误数据",visible:t.visible,height:"result"===t.dataType&&"all"===t.addType?"200px":"700px",width:"result"===t.dataType&&"all"===t.addType?"500px":"1000px",footer:!1,destroyOnClose:""},on:{cancel:t.handleCancel}},[a("ta-border-layout",{attrs:{"show-border":!1,layout:{header:"50px"},"header-cfg":{showBorder:!1}}},[a("div",{attrs:{slot:"header"},slot:"header"},["result"===t.dataType?a("div",[a("span",{staticClass:"header-info"},[t._v("新增条数："+t._s(t.resultData.successNum))]),a("ta-button",{attrs:{type:"primary",disabled:0===Number(t.resultData.successNum)&&0===Number(t.resultData.editNum)},on:{click:t.uploadFile}},[t._v("确认上传")])],1):t._e(),"error"===t.dataType?a("div",[a("span",{staticClass:"header-info"},[t._v("错误数据条数："+t._s(t.resultData.errorNum))])]):t._e()]),"result"===t.dataType?a("div",{staticClass:"fit"},[a("div",{staticStyle:{"margin-bottom":"10px"}},[t._v(" 新增条目 ")]),a("div",{},[a("ta-big-table",{attrs:{height:390,data:0===t.successList.length&&t.resultData.successList?t.resultData.successList.slice(0,t.pageSizeInsert):t.successList,border:"","auto-resize":!0,"show-overflow":""},scopedSlots:t._u([{key:"bottomBar",fn:function(){return[a("ta-pagination",{staticStyle:{"text-align":"right"},attrs:{size:"small",defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"],total:Number(t.resultData.successNum),showSizeChanger:""},on:{change:t.handlePageNumberChangeInsert,showSizeChange:t.handlePageSizeChangeInsert}})]},proxy:!0}],null,!1,1027493839)},[a("ta-big-table-column",{attrs:{title:"序号",type:"seq",width:"60",align:"center"}}),a("ta-big-table-column",{attrs:{field:"ake001",title:"三目编码","min-width":"100",align:"center"}}),a("ta-big-table-column",{attrs:{field:"ake002",title:"三目名称","min-width":"100",align:"center"}}),a("ta-big-table-column",{attrs:{field:"ake003",title:"三目类型",collectionType:"MAKE003","min-width":"100",align:"center"}}),a("ta-big-table-column",{attrs:{field:"yke011",title:"限制条件","show-overflow":!0,"min-width":"100",align:"center"}})],1)],1)]):t._e(),"error"===t.dataType?a("div",{staticClass:"fit"},[a("div",{staticStyle:{"margin-bottom":"10px"}},[t._v(" 错误数据列表 ")]),a("div",{staticStyle:{height:"390px"}},[a("ta-big-table",{attrs:{height:390,data:0===t.errorList.length&&t.resultData.errorList?t.resultData.errorList.slice(0,t.pageSizeError):t.errorList,border:"","auto-resize":!0,"show-overflow":""},scopedSlots:t._u([{key:"bottomBar",fn:function(){return[a("ta-pagination",{staticStyle:{"text-align":"right"},attrs:{size:"small",defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"],total:Number(t.resultData.errorNum),showSizeChanger:""},on:{change:t.handlePageNumberChangeError,showSizeChange:t.handlePageSizeChangeError}})]},proxy:!0}],null,!1,893380724)},[a("ta-big-table-column",{attrs:{title:"序号",type:"seq",width:"60",align:"center"}}),a("ta-big-table-column",{attrs:{field:"ake001",title:"三目编码","min-width":"100",align:"center"}}),a("ta-big-table-column",{attrs:{field:"ake002",title:"三目名称","min-width":"100",align:"center"}}),a("ta-big-table-column",{attrs:{field:"ake003",title:"三目类型",collectionType:"MAKE003","min-width":"100",align:"center"}}),a("ta-big-table-column",{attrs:{field:"yke011",title:"限制条件","show-overflow":!0,"min-width":"100",align:"center"}}),a("ta-big-table-column",{attrs:{field:"errorInfo",title:"原因","min-width":"200",align:"center"}})],1)],1)]):t._e()])],1)},n=[],c={name:"addImport",props:{addType:{type:String,default:"add"},dataType:{type:String,default:"result"},visible:{type:Boolean,default:!0},resultData:{type:Object,default:function(){return{}}}},data:function(){return{pageSizeOptions:["10","20","30","40","50"],pageNumberInsert:1,pageSizeInsert:10,pageNumberEdit:1,pageSizeEdit:10,pageNumberError:1,pageSizeError:10,successList:[],editList:[],errorList:[]}},methods:{handlePageData:function(t,e,a){this[t]=this.resultData[t].slice((this[a]-1)*this[e],this[a]*this[e])},handlePageNumberChangeInsert:function(t){this.pageNumberInsert=t,this.handlePageData("successList","pageSizeInsert","pageNumberInsert")},handlePageSizeChangeInsert:function(t,e){this.pageNumberInsert=t,this.pageSizeInsert=e,this.handlePageData("successList","pageSizeInsert","pageNumberInsert")},handlePageNumberChangeEdit:function(t){this.pageNumberEdit=t,this.handlePageData("errorList","pageSizeEdit","pageNumberEdit")},handlePageSizeChangeEdit:function(t,e){this.pageNumberEdit=t,this.pageSizeEdit=e,this.handlePageData("errorList","pageSizeEdit","pageNumberEdit")},handlePageNumberChangeError:function(t){this.pageNumberError=t,this.handlePageData("errorList","pageSizeError","pageNumberError")},handlePageSizeChangeError:function(t,e){this.pageNumberError=t,this.pageSizeError=e,this.handlePageData("errorList","pageSizeError","pageNumberError")},userPageParams:function(){},handleCancel:function(t){this.$emit("close",!1)},uploadFile:function(){var t=this;if(0!=this.resultData.successList.length){var e="mtt/localruleconfig/miThreeAspects/submitFile";this.Base.submit(null,{url:e,data:this.resultData,autoQs:!1}).then((function(e){t.$message.success("导入成功"),t.$emit("close",!0)})).catch((function(e){t.$emit("close",!0)}))}else this.$message.info("无新增的数据，无需上传")}}},d=c,u=a(1001),p=(0,u.Z)(d,o,n,!1,null,"66f55e5d",null),h=p.exports,m=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",t._l(t.logList,(function(e,i){return a("div",{key:i,staticStyle:{"border-bottom":"1px dashed black","padding-bottom":"10px","padding-top":"10px"}},[a("ta-row",["2"==e.type?a("ta-col",{attrs:{span:6}},[t._v("停用人:")]):t._e(),"4"==e.type?a("ta-col",{attrs:{span:6}},[t._v("启用人:")]):t._e(),"3"==e.type?a("ta-col",{attrs:{span:6}},[t._v("变更人:")]):t._e(),"1"==e.type?a("ta-col",{attrs:{span:6}},[t._v("提交人:")]):t._e(),a("ta-col",{attrs:{span:18}},[t._v(t._s(e.name))])],1),a("ta-row",["2"==e.type?a("ta-col",{attrs:{span:6}},[t._v("停用时间:")]):t._e(),"4"==e.type?a("ta-col",{attrs:{span:6}},[t._v("启用时间:")]):t._e(),"3"==e.type?a("ta-col",{attrs:{span:6}},[t._v("变更时间:")]):t._e(),"1"==e.type?a("ta-col",{attrs:{span:6}},[t._v("提交时间:")]):t._e(),a("ta-col",{attrs:{span:18}},[t._v(t._s(e.date))])],1),a("ta-row",["2"==e.type?a("ta-col",{attrs:{span:6}},[t._v("停用意见:")]):t._e(),"4"==e.type?a("ta-col",{attrs:{span:6}},[t._v("启用意见:")]):t._e(),a("ta-col",{attrs:{span:18}},[t._v(t._s(e.note))])],1),a("div",{staticStyle:{height:"20px"}})],1)})),0)},f=[],b={name:"ruleLog",props:{record:Object},data:function(){return{logList:[]}},mounted:function(){this.queryRuleLog()},methods:{queryRuleLog:function(){var t=this;Base.submit(null,{url:"mtt/localruleconfig/miThreeAspects/queryLogByAke001",data:{ake001:this.record.ake001}}).then((function(e){t.logList=e.data.data}))}}},g=b,v=(0,u.Z)(g,m,f,!1,null,"7cd63c04",null),y=v.exports,S={name:"localRuleSearch",components:{addImport:h,itemLog:y},data:function(){return{moduleUrl:"mtt/localruleconfig/miThreeAspects/",bigRuleList:[],ruleidList:[],dataList:[],columns:[{title:"三目编码",dataIndex:"ake001",width:100,overflowTooltip:!0},{title:"三目名称",dataIndex:"ake002",width:100,overflowTooltip:!0},{title:"三目类型",dataIndex:"ake003",collectionType:"MAKE003",width:100,overflowTooltip:!0},{title:"限制条件",dataIndex:"yke011",width:100,overflowTooltip:!0},{title:"有效标志",dataIndex:"aae100",collectionType:"EFFECTIVE",width:100,overflowTooltip:!0},{title:"操作",align:"center",width:240,scopedSlots:{customRender:"operation"}}],selectRecords:[],blnRuleReasonVisible:!1,ruleEnableStatus:"1",descValue:void 0,blnRuleLogVisible:!1,editRecord:{},blnRuleItemVisible:!1,blnAddItemVisible:!1,blnItemImportVisible:!1,addImportVisible:!1,resultData:{},dataType:void 0,fileList:[],blnLogVisible:!1,blnEditVisible:!1,editType:"1",currentPage:1}},mounted:function(){this.queryBigRule(),this.listRuleid(),this.doSearch()},methods:{downloadTemplate:function(){var t=faceConfig.basePath;location.href=t+"/mtt/localruleconfig/common/download/三目导入模板"},openImport:function(){this.blnItemImportVisible=!0,this.fileList=[]},handleImport:function(){var t=this;if(this.fileList.length<=0)this.$message.error("请先选择文件");else{var e=this.moduleUrl+"importIncrementFile";this.Base.submit(null,{url:e,data:{file:this.fileList[0]},isFormData:!0}).then((function(e){t.resultData=e.data.data,parseInt(t.resultData.errorNum)>0?t.dataType="error":(parseInt(t.resultData.editNum)>0||parseInt(t.resultData.insertNum)>0)&&(t.dataType="result"),t.addImportVisible=!0})).catch((function(e){t.$message.info("上传失败"),t.addImportVisible=!1}))}},beforeUpload:function(t){return this.fileList=[],this.fileList=[].concat((0,s.Z)(this.fileList),[t]),!1},handleRemove:function(t){var e=this.fileList.indexOf(t),a=this.fileList.slice();a.splice(e,1),this.fileList=a},closeAddImportModel:function(t){this.addImportVisible=!1,t&&(this.handleCancelImport(t),this.doSearch())},handleCancelImport:function(t){this.fileList=[]},doExport:function(){var t=this.searchForm.getFieldsValue();Base.downloadFile({url:this.moduleUrl+"exportExcelByVo",options:(0,r.Z)({},t),type:"application/octet-stream",fileName:"目录导出.xls"}).then((function(t){})).catch((function(t){}))},doSearch:function(){this.$refs.rulePager.loadData()},refreshCurrentPage:function(){var t=this,e=this.$refs.rulePager.getPagerInfo(),a=this.searchForm.getFieldsValue();Base.submit(null,{url:this.moduleUrl+"queryPage",data:(0,r.Z)((0,r.Z)({},a),{},{pageNumber:e.current,pageSize:e.pageSize})}).then((function(e){t.dataList=e.data.pageBean.list}))},resetSearch:function(){this.searchForm.resetFields()},queryBigRule:function(){var t=this;Base.submit(null,{url:"mttRuleConfig/listBigRule"}).then((function(e){t.bigRuleList=e.data.list}))},listRuleid:function(){var t=this;Base.submit(null,{url:"mttRuleConfig/listRuleid"}).then((function(e){t.ruleidList=e.data.list}))},pageParams:function(){var t=this.searchForm.getFieldsValue();return(0,r.Z)({},t)},disableOrEnable:function(t){this.descValue=void 0;var e=this.$refs.ruleTable.getChecked().selectedRows;0!=e.length?(this.selectRecords=e,this.ruleEnableStatus=t,this.blnRuleReasonVisible=!0):this.$message.warn("请选择至少一条进行启停")},disableOrEnableSingle:function(t,e){this.descValue=void 0;var a=[t];this.selectRecords=a,this.ruleEnableStatus=e,this.blnRuleReasonVisible=!0},doDisableOrEnable:function(){var t=this;if(this.descValue){var e=this.moduleUrl+"openByIds";"0"==this.ruleEnableStatus&&(e=this.moduleUrl+"closeByIds"),Base.submit(null,{url:e,data:{ids:this.selectRecords.map((function(t){return t.ake001})).join(","),note:this.descValue}}).then((function(e){t.$message.success("操作成功"),t.doSearch(),t.blnRuleReasonVisible=!1}))}else this.$message.info("请输入意见")},goDetail:function(t){this.editRecord=t,this.blnEditVisible=!0},openLog:function(t){this.editRecord=t,this.blnLogVisible=!0},openAddForm:function(){this.editType="1",this.blnEditVisible=!0},openEditForm:function(t){var e=this;this.editRecord=t,this.editType="2",this.blnEditVisible=!0,this.$nextTick((function(){e.editForm.setFieldsValue(t)}))},doSave:function(){var t=this;this.editForm.validateFields((function(e){if(!e){var a=t.editForm.getFieldsValue(),i=t.moduleUrl+"add";"2"==t.editType&&(i=t.moduleUrl+"edit"),Base.submit(null,{url:i,data:(0,r.Z)({},a)}).then((function(e){t.$message.success("操作成功"),"2"==t.editType?t.refreshCurrentPage():t.doSearch(),t.blnEditVisible=!1}))}}))}}},_=S,E=(0,u.Z)(_,i,l,!1,null,"3c428050",null),w=E.exports}}]);
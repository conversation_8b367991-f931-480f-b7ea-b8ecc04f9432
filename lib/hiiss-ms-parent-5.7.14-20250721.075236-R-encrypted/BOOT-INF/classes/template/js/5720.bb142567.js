"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[5720],{88412:function(t,e,a){var l=a(26263),r=a(36766),i=a(1001),o=(0,i.Z)(r.Z,l.s,l.x,!1,null,"5e7ef0ae",null);e["Z"]=o.exports},55720:function(t,e,a){a.r(e),a.d(e,{default:function(){return c}});var l=function(){var t=this,e=this,a=e.$createElement,l=e._self._c||a;return l("div",{attrs:{id:"components-layout-demo-basic"}},[l("ta-layout",[l("ta-layout-sider",{staticStyle:{"background-color":"white",overflow:"auto"},attrs:{width:"25%"}},[l("div",{staticStyle:{"margin-top":"20px"}},[l("div",{staticStyle:{"margin-bottom":"10px"}},[l("ta-button",{staticStyle:{"margin-right":"10px"},on:{click:e.downloadFile}},[e._v("模板下载 ")]),l("ta-upload",{attrs:{name:"file",multiple:!0,"show-upload-list":!1,action:e.backUrl+"/mttRuleCustom/importRuleCustomFile",accept:".xls"},on:{change:e.handleChange}},[l("ta-button",{attrs:{type:"primary",disabled:e.isDisabled}},[e._v(" 导入分类 ")])],1)],1),l("ta-e-tree",{ref:"tree",attrs:{data:e.data,props:e.defaultProps,"default-expand-all":!0,"node-key":"id","expand-on-click-node":!1,"highlight-current":!0},scopedSlots:e._u([{key:"default",fn:function(t){t.node;var a=t.data;return l("span",{staticClass:"custom-tree-node"},[l("span",{staticClass:"custom-name",on:{click:function(t){return e.handleNodeClick(a)}}},[e._v(e._s(a.label))]),l("span",{staticClass:"tree-operate"},[2!==a.level?l("a",{on:{click:function(t){return e.fnAddOrEdit(a,"add")}}},[l("ta-icon",{attrs:{type:"plus-circle-o"}})],1):e._e(),0!==a.level?l("a",{on:{click:function(t){return e.fnAddOrEdit(a,"edit")}}},[l("ta-icon",{attrs:{type:"edit"}})],1):e._e(),0!==a.level?l("ta-popconfirm",{attrs:{title:"确定删除该节点么？"},on:{confirm:function(t){return e.fnDelete(a)}}},[l("a",[l("ta-icon",{attrs:{type:"delete"}})],1)]):e._e()],1)])}}])}),l("ta-modal",{attrs:{title:e.title,visible:e.visible},on:{ok:e.handleOk,cancel:e.handleCancel}},[l("ta-form",{attrs:{autoFormCreate:function(e){t.ruleForm=e}}},["add"===e.modalType?l("ta-form-item",{attrs:{disabled:"",label:"上一级","field-decorator-id":"ruleType1"}},[l("ta-input")],1):e._e(),"edit"===e.modalType&&2===e.currData.level?l("ta-form-item",{attrs:{label:"上一级",fieldDecoratorId:"ruleType3",fieldDecoratorOptions:{rules:[{required:!0,message:"上级分类不能为空"}]}}},[l("ta-tree-select",{attrs:{allowClear:"",showSearch:"",placeholder:"规则分类",dropdownStyle:{maxHeight:"400px",overflow:"auto"},treeData:e.ruleType1Data,treeDataSimpleMode:e.ruleType1Data}})],1):e._e(),l("ta-form-item",{attrs:{label:"规则分类名称",fieldDecoratorId:"ruleType2",fieldDecoratorOptions:{rules:[{required:!0,message:"规则分类不能为空"}]},extra:"add"===e.modalType?"一次新增多个分类可用英文逗号隔开。":""}},[l("ta-input",{attrs:{placeholder:"请输入新增的规则分类"}})],1)],1)],1)],1)]),l("ta-layout-content",[l("div",{staticClass:"fit"},[l("ta-border-layout",{attrs:{layout:{header:"220px"}}},[l("div",{attrs:{slot:"header"},slot:"header"},[l("ta-title",{attrs:{title:"查询条件"}}),l("ta-form",{staticStyle:{"padding-top":"5px"},attrs:{autoFormCreate:function(e){t.searchForm=e},layout:"horizontal",formLayout:!0}},[l("ta-form-item",{attrs:{span:8,label:"知识元",fieldDecoratorId:"ykz018"}},[l("ta-input",{attrs:{placeholder:"请输入"}})],1),l("ta-form-item",{attrs:{"field-decorator-id":"aaa167",label:"规则大类",span:8,labelCol:{span:8},wrapperCol:{span:16}}},[l("ta-select",{attrs:{placeholder:"规则大类筛选",allowClear:"",showSearch:"","options-key":{value:"name",label:"name"},options:e.aaa167Options}})],1),l("ta-form-item",{attrs:{label:"规则分类",span:8,fieldDecoratorId:"ruleType1"}},[l("ta-tree-select",{attrs:{showSearch:"",allowClear:"",dropdownStyle:{maxHeight:"400px",overflow:"auto"},treeData:e.treeData,treeDataSimpleMode:e.treeData},on:{select:e.treeChange}})],1),l("ta-form-item",{attrs:{span:8,label:"配置状态",fieldDecoratorId:"state"}},[l("ta-select",{attrs:{allowClear:"",options:[{value:"1",label:"已配置"},{value:"0",label:"未配置"}],placeholder:"请选择"}})],1),l("ta-button",{staticStyle:{"margin-right":"10px",float:"right"},attrs:{icon:"reload"},on:{click:e.resetSearch}},[e._v("重置 ")]),l("ta-button",{staticStyle:{"margin-right":"20px",float:"right"},attrs:{type:"primary",icon:"search"},on:{click:e.onSearch}},[e._v("查询 ")])],1)],1),l("div",{staticClass:"fit content-box"},[l("ta-title",{staticStyle:{width:"50%"},attrs:{title:"配置信息"}}),l("ta-row",{staticStyle:{"margin-bottom":"10px"}},[l("ta-button",{staticStyle:{"margin-right":"10px",float:"right"},on:{click:function(t){return e.ruleCustom("batch")}}},[e._v("按规则大类批量配置 ")]),l("ta-button",{staticStyle:{"margin-right":"20px",float:"right"},attrs:{type:"primary"},on:{click:function(t){return e.ruleCustom("single")}}},[e._v("配置 ")])],1),l("ta-big-table",{ref:"ruleTable",attrs:{columns:e.columns,data:e.ruleList,"export-config":{},"import-config":{},"checkbox-config":{trigger:"row",highlight:!0,reserve:!0},border:"","empty-text":"-",height:"450","highlight-hover-row":"","highlight-current-row":"","auto-resize":"","show-overflow":"",resizable:"","row-id":"ykz195"},scopedSlots:e._u([{key:"state",fn:function(t){var a=t.row;return["1"==a.state?l("ta-tag",{attrs:{color:"green"}},[e._v(" 已配置 ")]):e._e(),"0"==a.state?l("ta-tag",{attrs:{color:"yellow"}},[e._v(" 未配置 ")]):e._e()]}},{key:"bottomBar",fn:function(){return[l("ta-pagination",{ref:"rulePager",staticStyle:{"text-align":"right","margin-top":"10px"},attrs:{"data-source":e.ruleList,params:e.pageParams,url:"mttRuleCustom/getRuleCustomInfo"},on:{"update:dataSource":function(t){e.ruleList=t},"update:data-source":function(t){e.ruleList=t}}})]},proxy:!0}])})],1)])],1)])],1),e.ruleVisible?l("ta-modal",{attrs:{title:e.ruleTitle,visible:e.ruleVisible,width:"batch"===e.ruleCustomType?1e3:500,height:"batch"===e.ruleCustomType?550:100},on:{ok:e.handleRuleOk,cancel:e.handleRuleCancel}},["single"===e.ruleCustomType?l("div",[l("ta-form",{attrs:{autoFormCreate:function(e){t.ruleCustomForm=e},layout:"horizontal",formLayout:!0}},[l("ta-form-item",{attrs:{label:"规则分类",span:24,labelCol:{span:8},wrapperCol:{span:16},fieldDecoratorId:"ruleType2",require:{message:"请选择!"}}},[l("ta-tree-select",{attrs:{showSearch:"",allowClear:"",dropdownStyle:{maxHeight:"400px",overflow:"auto"},treeData:e.treeData,treeDataSimpleMode:e.treeData}})],1)],1)],1):e._e(),"batch"===e.ruleCustomType?l("div",[l("ta-row",[l("ta-col",{attrs:{span:18}},[l("ta-form",{staticStyle:{"padding-top":"5px"},attrs:{autoFormCreate:function(e){t.searchRuleForm=e},layout:"horizontal",formLayout:!0}},[l("ta-form-item",{attrs:{"field-decorator-id":"aaa167",label:"规则大类",span:16,labelCol:{span:6},wrapperCol:{span:18}}},[l("ta-select",{attrs:{placeholder:"规则大类筛选",allowClear:"",showSearch:"","options-key":{value:"name",label:"name"},options:e.aaa167Options}})],1)],1)],1),l("ta-col",[l("ta-button",{staticStyle:{"margin-right":"10px",float:"right"},attrs:{icon:"reload"},on:{click:e.resetRuleSearch}},[e._v("重置 ")]),l("ta-button",{staticStyle:{"margin-right":"20px",float:"right"},attrs:{type:"primary",icon:"search"},on:{click:e.onRuleSearch}},[e._v("查询 ")])],1)],1),l("div",{staticClass:"fit content-box"},[l("div",{staticClass:"content-table"},[l("ta-big-table",{ref:"ruleCustomTable",attrs:{data:e.ruleCustomList,"export-config":{},"import-config":{},"checkbox-config":{trigger:"row",highlight:!0,reserve:!0},border:"",height:"440","empty-text":"-","highlight-hover-row":"","highlight-current-row":"","auto-resize":"","show-overflow":"",resizable:"","row-id":"aaa167","keep-source":"","edit-config":{trigger:"click",mode:"row",showStatus:!0}},scopedSlots:e._u([{key:"bottomBar",fn:function(){return[l("ta-pagination",{ref:"ruleCustomPager",staticStyle:{"text-align":"right","margin-top":"10px"},attrs:{dataSource:e.ruleCustomList,params:e.pageRuleParams,url:"mttRuleCustom/getRuleData"},on:{"update:dataSource":function(t){e.ruleCustomList=t},"update:data-source":function(t){e.ruleCustomList=t}}})]},proxy:!0}],null,!1,1442940201)},[l("ta-big-table-column",{attrs:{type:"seq",title:"序号",width:"60",align:"center"}}),l("ta-big-table-column",{attrs:{field:"aaa167",title:"规则大类","min-width":"350"}}),l("ta-big-table-column",{attrs:{field:"ruleType1",title:"规则分类","min-width":"350","edit-render":{name:"$tree-select",showPath:!0,props:{treeData:e.treeData,showSearch:!0,allowClear:!0},events:{change:e.handleTreeEditChange}}}})],1)],1)])],1):e._e()]):e._e()],1)},r=[],i=(a(32564),a(69810),a(88412)),o={name:"ruleCustom",components:{TaTitle:i.Z},data:function(){return{data:[],tempData:[],treeData:[],ruleType1Data:[],defaultProps:{children:"children",label:"label",id:"id"},visible:!1,title:"",ruleTitle:"",ruleVisible:!1,ruleCustomType:"",currData:{},ruleCustomData:{},modalType:"",backUrl:"",isDisabled:!1,columns:[{type:"checkbox",width:"60",align:"center"},{type:"seq",title:"序号",width:"60",align:"center"},{field:"ykz018",title:"知识元",minWidth:300,align:"left",overflowTooltip:!0},{field:"aaa167",title:"规则大类",minWidth:300,align:"left",overflowTooltip:!0},{field:"ruleType1",title:"规则分类",minWidth:280,align:"left",overflowTooltip:!0,formatter:function(t){var e=t.cellValue;return e||"--"}},{field:"state",title:"配置状态",minWidth:100,align:"center",customRender:{default:"state"}}],ruleList:[],ruleCustomList:[],aaa167Options:[],editStyle:"border",aaa167List:[],tempAaz319List:[]}},mounted:function(){this.backUrl=faceConfig.basePath,this.getTreeData(),this.onSearch(),this.fnQueryAaz319()},methods:{handleNodeClick:function(t){},fnAddOrEdit:function(t,e){var a=this;this.visible=!0,this.currData=t,this.modalType=e,this.$nextTick((function(){if(a.ruleForm.resetFields(),"add"===a.modalType)a.title="新增规则分类",a.ruleForm.setFieldsValue({ruleType1:t.value});else{if(1===a.currData.level)a.title="编辑1级规则分类";else if(2===a.currData.level){a.title="编辑2级规则分类",a.tempData[0].disabled=!0,a.tempData[0].children.forEach((function(t){delete t.children}));var e=a.data[0].children.filter((function(e){var a=!1;return e.children&&e.children.forEach((function(e){e.value===t.value&&(a=!0)})),a}));a.ruleType1Data=a.tempData,a.ruleForm.setFieldsValue({ruleType3:e[0].value})}a.ruleForm.setFieldsValue({ruleType2:t.value})}}))},fnDelete:function(t){var e=this;t.ruleType2=t.label,delete t.label,delete t.value,this.Base.submit(null,{url:"mttRuleCustom/deleteRule",data:t,autoValid:!0},{successCallback:function(t){e.getTreeData()},failCallback:function(t){e.$message.error("删除数据失败，请检查数据")}})},handleOk:function(){var t=this;this.ruleForm.validateFields((function(e,a){if(!e){var l=t.ruleForm.getFieldsValue();"add"===t.modalType&&l&&l.ruleType1&&"规则分类"===l.ruleType1&&(l={ruleType1:l.ruleType2}),"edit"===t.modalType&&l&&l.ruleType2&&(l.oriRuleType2=t.currData.value,l.ruleType3&&(l.ruleType1=l.ruleType3,delete l.ruleType3)),l.level=t.currData.level,l.modalType=t.modalType,t.Base.submit(null,{url:"mttRuleCustom/insertOrUpdateRule",data:l,autoValid:!0},{successCallback:function(e){t.$nextTick((function(){t.getTreeData()}))},failCallback:function(e){t.$message.error("操作数据失败，请检查数据")}}),t.handleCancel()}}))},handleCancel:function(){this.visible=!1,this.currData={}},treeChange:function(t,e,a){this.ruleCustomData=a.selectedNodes[0]},handleRuleOk:function(){var t=this;if("single"===this.ruleCustomType){var e=this.ruleCustomForm.getFieldsValue(),a=this.$refs.ruleTable.getCheckboxReserveRecords(),l=[];a.forEach((function(t){l.push(t.ykz195)})),e.ykz195List=l,this.Base.submit(null,{url:"mttRuleCustom/ruleCustom",data:e,autoValid:!0,autoQs:!1},{successCallback:function(e){t.$message.success("配置成功！"),t.onSearch()},failCallback:function(e){t.$message.error("操作数据失败，请检查数据")}})}else{if(0===this.aaa167List.length)return void this.handleRuleCancel();var r={aaa167List:this.aaa167List,modalType:"batch"};this.Base.submit(null,{url:"mttRuleCustom/ruleCustom",data:r,autoValid:!0,autoQs:!1},{successCallback:function(e){t.$message.success("配置成功！"),t.onSearch()},failCallback:function(e){t.$message.error("操作数据失败，请检查数据")}})}this.handleRuleCancel()},handleRuleCancel:function(){"batch"===this.ruleCustomType?(this.aaa167List=[],this.tempAaz319List=[],this.searchRuleForm.resetFields()):this.ruleCustomForm.resetFields(),this.ruleVisible=!1},downloadFile:function(){var t=this;Base.downloadFile({method:"post",fileName:"规则分类导入模板.xls",url:"mttRuleCustom/downloadRuleFile"}).then((function(e){t.$message.success("下载成功")})).catch((function(e){t.$message.error("下载失败")}))},handleChange:function(t){var e=this;"done"===t.file.status?(message.info("上传成功!"),this.getTreeData()):t.file.status,setTimeout((function(){e.isDisabled=!1}),3e3)},handleTreeEditChange:function(t,e,a){var l=t.row;if(this.tempAaz319List.includes(l.aaa167)){var r=this.tempAaz319List.indexOf(l.aaa167);this.aaa167List.at(r).ruleType1=l.ruleType1}else{this.tempAaz319List.push(l.aaa167);var i={aaa167:l.aaa167,ruleType1:l.ruleType1};this.aaa167List.push(i)}},getTreeData:function(){var t=this;Base.submit(null,{url:"mttRuleCustom/getRuleTreeData"}).then((function(e){t.data=e.data.list,t.tempData=e.data.list1,Object.assign(t.treeData,t.data),t.treeData.length>0&&(t.treeData[0].disabled=!0,t.treeData[0].children.forEach((function(t){t.disabled=!0})))}))},onSearch:function(){this.$refs.rulePager.loadData(),this.$refs.ruleTable.clearCheckboxReserve()},pageParams:function(){var t=this.searchForm.getFieldsValue();return t.level=this.ruleCustomData.level||"",t},resetSearch:function(){var t=this;this.searchForm.resetFields(),this.ruleCustomData={},this.$nextTick((function(){t.onSearch()}))},onRuleSearch:function(){this.$refs.ruleCustomPager.loadData()},pageRuleParams:function(){var t=this.searchRuleForm.getFieldsValue();return t},resetRuleSearch:function(){var t=this;this.searchRuleForm.resetFields(),this.$nextTick((function(){t.onRuleSearch()}))},callback:function(t){},fnQueryAaz319:function(t){var e=this,a={url:"mttRuleCustom/queryAaz319",autoValid:!0},l={successCallback:function(t){e.aaa167Options=t.data.option},failCallback:function(t){}};this.Base.submit(null,a,l)},ruleCustom:function(t){var e=this;if(this.ruleVisible=!0,this.ruleCustomType=t,"single"===t){this.ruleTitle="配置";var a=this.$refs.ruleTable.getCheckboxReserveRecords();if(0===a.length)return this.$message.warn("请先选择数据"),void(this.ruleVisible=!1)}else this.ruleTitle="按规则大类批量配置",this.$nextTick((function(){e.onRuleSearch()}))}}},s=o,n=a(1001),u=(0,n.Z)(s,l,r,!1,null,"1b666b6a",null),c=u.exports},36766:function(t,e,a){var l=a(66586);e["Z"]=l.Z},26263:function(t,e,a){a.d(e,{s:function(){return l},x:function(){return r}});var l=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},r=[]},66586:function(t,e){var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};e["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}}}]);
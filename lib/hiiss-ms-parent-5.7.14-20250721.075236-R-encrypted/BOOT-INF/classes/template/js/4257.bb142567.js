"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[4257],{34257:function(e,o,t){t.r(o),t.d(o,{default:function(){return h}});var s=function(){var e=this,o=e.$createElement,t=e._self._c||o;return t("div")},a=[],i=t(95082),l=t(63822),c={name:"thirdLogin",data:function(){return{slideCode:null,cryptInfo:null,captchaType:"clickWord",captchaParams:null}},computed:(0,i.Z)({},(0,l.Se)({sysState:"getSysState",showSimpleCheckCode:"showSimpleCheckCode",simpleCheckCodeState:"simpleCheckCodeState",slideCheckCodeState:"slideCheckCodeState",showSlideCheckCode:"showSlideCheckCode",passwordRSAState:"passwordRSAState",showClickWordCheckCode:"showClickWordCheckCode"})),watch:{},mounted:function(){this.handleSubmit()},methods:{handleSubmit:function(){var e=this,o=parent.location.hash.substring(13),t=/([^=&\s]+)[=\s]*([^&\s]*)/g,s={};while(t.exec(o))s[RegExp.$1]=RegExp.$2,s[RegExp.$1]=decodeURIComponent(s[RegExp.$1]);s.password=Base.cryptoAsymmetricFn(s.password),this.Base.submit(this.form,{url:"/login",data:s,autoValid:!0},{successCallback:function(o){e.showClickWordCheckCode&&e.$refs.verify.closeBox();var t=o.data["TA-JTOKEN"],s=o.data["TA-RJTOKEN"];null!==t&&void 0!==t&&""!==t&&TaUtils.setCookie(faceConfig.basePath+"TA-JTOKEN",t,0,"/"),null!==s&&void 0!==t&&""!==s&&TaUtils.setCookie(faceConfig.basePath+"TA-RJTOKEN",s,0,"/"),window.location.href="index.html"},failCallback:function(o){e.showClickWordCheckCode&&e.$refs.verify.closeBox(),"418"===o.errors[0].errorCode&&e.refreshCode()}})}}},d=c,n=t(1001),r=(0,n.Z)(d,s,a,!1,null,null,null),h=r.exports}}]);
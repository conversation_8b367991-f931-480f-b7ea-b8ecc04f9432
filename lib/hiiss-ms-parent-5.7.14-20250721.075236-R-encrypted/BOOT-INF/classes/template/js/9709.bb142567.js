(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[9709],{88412:function(e,t,a){"use strict";var i=a(26263),l=a(36766),r=a(1001),n=(0,r.Z)(l.Z,i.s,i.x,!1,null,"5e7ef0ae",null);t["Z"]=n.exports},58814:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return b}});var i=function(){var e=this,t=this,a=t.$createElement,i=t._self._c||a;return i("div",{staticClass:"fit"},[i("ta-border-layout",{attrs:{"layout-type":"fixTop"},scopedSlots:t._u([{key:"header",fn:function(){return[i("ta-form",{staticStyle:{"margin-top":"20px"},attrs:{autoFormCreate:t.autoFormCreate,col:3,formLayout:!0}},[i("ta-form-item",{attrs:{require:!1,showInfo:!0,"field-decorator-id":"patientInfo",label:"患者信息","label-col":{span:7},span:5,"wrapper-col":{span:17}}},[i("ta-input",{attrs:{allowClear:"",placeholder:"请输入就诊号或姓名"}})],1),i("ta-form-item",{attrs:{require:!1,showInfo:!0,"field-decorator-id":"ake001",label:"医保项目信息","label-col":{span:7},span:5,"wrapper-col":{span:17}}},[i("ta-input",{attrs:{allowClear:"",placeholder:"请输入医保项目编码或名称"}})],1),i("ta-form-item",{attrs:{require:!1,showInfo:!0,"field-decorator-id":"aae040",label:"添加时间","label-col":{span:7},span:5,"init-value":t.rangeValue,"wrapper-col":{span:17}}},[i("ta-range-picker",{attrs:{"allow-one":!0}})],1),i("ta-form-item",{attrs:{require:!1,showInfo:!0,"field-decorator-id":"medtype",label:"就诊类型","label-col":{span:7},span:5,"wrapper-col":{span:17}}},[i("ta-select",{attrs:{"show-search":!0,allowClear:"",options:t.typeList,placeholder:"请选择就诊类型"}})],1),i("ta-form-item",{staticStyle:{"text-align":"right"},attrs:{require:!1,showInfo:!0,label:" ","label-col":{span:6},span:4,"wrapper-col":{span:18}}},[i("ta-button",{staticStyle:{"margin-right":"10px"},attrs:{type:"primary"},on:{click:t.queryTableData}},[t._v("查询 ")]),i("ta-button",{staticStyle:{"margin-right":"10px"},on:{click:t.fnReset}},[t._v("重置 ")])],1)],1)]},proxy:!0}])},[i("ta-card",{staticClass:"fit"},[i("ta-title",{staticStyle:{flex:"none",padding:"1px"},attrs:{title:"白名单列表"}},[i("div",{staticStyle:{float:"right"}},[i("ta-button",{attrs:{type:"primary"},on:{click:function(e){return t.fnAdd()}}},[t._v("添加")]),i("ta-button",{attrs:{type:"primary"},on:{click:function(e){return t.fnDelete("batch")}}},[t._v("批量删除")])],1)]),i("ta-big-table",{ref:"infoTableRef",attrs:{data:t.tableData,height:"95%","export-config":{},"import-config":{},border:"","highlight-hover-row":"","highlight-current-row":"","auto-resize":"","show-overflow":"",resizable:"","keep-source":"","empty-text":"-"}},[i("ta-big-table-column",{attrs:{width:50,type:"checkbox"}}),i("ta-big-table-column",{attrs:{width:50,title:"序号",type:"seq"}}),i("ta-big-table-column",{attrs:{align:"center",field:"akc191",sortable:"","header-align":"center",title:"就诊号"}}),i("ta-big-table-column",{attrs:{align:"center",field:"ake001",sortable:"","header-align":"center",title:"医保项目编码"}}),i("ta-big-table-column",{attrs:{align:"center",field:"ake002",sortable:"","header-align":"center",title:"医保项目名称"}}),i("ta-big-table-column",{attrs:{align:"center",field:"medtype",sortable:"","header-align":"center",title:"就诊类型"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[t._v(" "+t._s("0"===a.medtype?"门诊":"住院")+" ")]}}])}),i("ta-big-table-column",{attrs:{align:"center",field:"aae040",sortable:"","header-align":"center",title:"添加时间"}}),i("ta-big-table-column",{attrs:{width:150,align:"center","header-align":"center",title:"操作"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row,l=e.rowIndex;return[[i("a",{on:{click:function(e){return t.editSave(a,l)}}},[t._v(" 编辑 ")]),i("ta-divider",{attrs:{type:"vertical"}}),i("a",{on:{click:function(e){return t.fnDelete(a)}}},[t._v(" 删除 ")])]]}}])}),i("template",{slot:"bottomBar"},[i("ta-pagination",{ref:"gridPager",staticStyle:{"text-align":"right","margin-right":"120px"},attrs:{dataSource:t.tableData,defaultPageSize:200,pageSizeOptions:["30","50","100","200","500"],params:t.getParam,url:"auditWhitelist/queryAuditWhitelistInfoPage"},on:{"update:dataSource":function(e){t.tableData=e},"update:data-source":function(e){t.tableData=e}}}),i("ta-button",{staticStyle:{position:"absolute",right:"20px",bottom:"12px"},attrs:{type:"primary",icon:"upload"},on:{click:t.exportExcel}},[t._v("导出 ")])],1)],2)],1)],1),i("ta-modal",{attrs:{title:"add"===t.modal?"白名单添加":"白名单编辑",visible:t.addWhiteList,height:280,width:500},on:{ok:t.handleOk,cancel:t.handleCancel}},[i("ta-form",{attrs:{"auto-form-create":function(t){return e.form1=t},layout:"horizontal","form-layout":!0,"label-width":"120px",col:{xs:1,sm:1,md:1,lg:1,xl:1,xxl:1}}},[i("ta-form-item",{attrs:{label:"就诊类型","init-value":"1","field-decorator-id":"medtype","label-width":"93px",require:""}},[i("ta-select",{staticStyle:{width:"100%"},attrs:{options:t.typeList,placeholder:"请选择就诊类型"},on:{select:t.medtypeChange}})],1),i("ta-form-item",{attrs:{label:"患者信息","field-decorator-id":"currPers","label-width":"93px",require:""}},[i("ta-input",{attrs:{placeholder:"请输入患者信息","allow-clear":""}})],1),i("ta-form-item",{attrs:{"field-decorator-id":"ake001",label:"医保项目编码",require:""}},[i("ta-suggest",{staticStyle:{width:"100%"},attrs:{"data-source":t.ake001List,placeholder:"请输入医保项目编码或名称","table-title-map":new Map([["value",{name:"医保项目编码",style:{minWidth:"120px"}}],["label",{name:"医保项目名称",style:{minWidth:"120px"}}]]),"option-config":t.optionConfig2,"dropdown-match-select-width":!1,"dropdown-style":{width:"450px"}},on:{select:t.onSelect2,search:t.handleSearch2},scopedSlots:t._u([{key:"body-cell",fn:function(e){e.cellValue;var a=e.column,l=(e.columnIndex,e.row);e.rowIndex;return[i("div",["医保项目编码"===a.name?i("span",[t._v(t._s(l.value))]):t._e(),"医保项目名称"===a.name?i("span",[t._v(t._s(l.label))]):t._e()])]}}])})],1),i("ta-form-item",{attrs:{label:"医保项目名称","init-value":t.ake001Select.label,require:""}},[i("ta-input",{attrs:{placeholder:"输入医保项目编码,精确匹配名称",value:t.ake001Select.label,disabled:""}})],1)],1)],1)],1)},l=[],r=a(66347),n=a(95082),o=a(88412),s=a(22722),c=a(55115),u=a(36797),d=a.n(u);c.w3.prototype.Base=Object.assign(c.w3.prototype.Base,(0,n.Z)({},s.Z));var f={name:"whiteList",components:{TaTitle:o.Z},data:function(){return{tableData:[],ake001List:[],peopleList:[],rangeValue:[this.Base.getMoment((new Date).toISOString().slice(0,8)+"-01","YYYY-MM-DD"),d()()],isAdd:!0,modal:"add",drawerVisible:!1,addWhiteList:!1,ake001Select:{},currPersSelect:{},optionConfig:{value:"value",label:function(e){return"".concat(e.akc191," ").concat(e.aac003," ").concat(e.aae376?e.aae376:"未知科室")}},optionConfig2:{value:"value",label:function(e){return"".concat(e.value)}},editRow:{},typeList:[{label:"门诊",value:"0"},{label:"住院",value:"1"}]}},mounted:function(){this.queryTableData()},methods:{moment:d(),medtypeChange:function(e){"edit"==this.modal&&(this.editRow.bfmedtype=e,this.handleSearch(this.editRow.akc191))},filterOption:function(e,t){return t.componentOptions.children[0].text.toLowerCase().indexOf(e.toLowerCase())>=0},fnQueryake001List:function(){var e=this,t={url:"/miimCommonRead/queryAke001List",autoValid:!0},a={successCallback:function(t){e.ake001List=t.data.ake001},failCallback:function(t){e.$message.error("规则大类列表数据加载失败")}};this.Base.submit(null,t,a)},handleOk:function(){var e=this;this.form1.validateFields((function(t){if(!t){var a=e.form1.getFieldsValue(),i={};"edit"===e.modal&&(i.id=e.editRow.id),i.aac003=e.currPersSelect.aac003,i.akc190=e.currPersSelect.akc190,i.akc191=a.currPers,i.ake001=e.ake001Select.value,i.ake002=e.ake001Select.label,i.medtype=a.medtype;var l=[],r=[];if(i.akc191||l.push("患者就诊号为空"),i.ake002||r.push("规则大类名称为空"),i.ake001||l.push("规则大类编码为空"),l.length>0){var n=l.join("，");return void e.$message.error("选择的数据有误：".concat(n))}if(r.length>0){var o=r.join("，");e.$message.warning("选择的数据有空的：".concat(o))}e.Base.submit(null,{url:"add"===e.modal?"auditWhitelist/addAuditWhitelistInfo":"auditWhitelist/editAuditWhitelistInfo",data:i,autoValid:!1},{successCallback:function(t){e.$message.success("edit"===e.modal?"修改成功!":"添加成功！"),e.form1.resetFields(),e.ake001List=[],e.ake001Select={},e.editRow={},e.currPersSelect={},e.addWhiteList=!1,e.queryTableData()},failCallback:function(t){e.$message.error("edit"===e.modal?"修改失败!":"添加失败！")}})}}))},handleCancel:function(){this.form1.resetFields(),this.ake001List=[],this.ake001Select={},this.editRow={},this.currPersSelect={},this.addWhiteList=!1,this.queryTableData()},onSelect:function(e,t,a){this.currPersSelect=t},onSelect2:function(e,t,a){this.ake001Select=t},onake001Select:function(e,t){this.ake001Select=e},handleSearch:function(e){var t,a=this;if("add"==this.modal){var i=this.form1.getFieldsValue();t=i.medtype}else t=this.editRow.bfmedtype;e&&this.Base.submit(null,{url:"/nightAudit/getDicDoctor",data:{doctorName:e,medType:t}},{successCallback:function(e){a.peopleList=e.data.list},failCallback:function(e){a.$message.error("查询患者信息失败！")}})},handleSearch2:function(e){var t=this;if(e){var a=this.form1.getFieldsValue(),i=a.currPers;i||this.$message.warn("未选中患者，查询范围过大，可能导致响应变慢！"),this.Base.submit(null,{url:"/miimCommonRead/queryAke001List",data:{ake001:e.trim(),akc191:i}},{successCallback:function(e){t.ake001List=e.data.list},failCallback:function(e){t.$message.error("查询患者信息失败！")}})}},fnAdd:function(){var e=this;this.modal="add",this.$nextTick((function(){e.addWhiteList=!0}))},fnDelete:function(e){var t=this,a=[];if(e.id)a=[e.id];else{var i=this.$refs.infoTableRef.getCheckboxRecords();if(0===i.length)return void this.$message.error("请选择要删除的数据！");a=i.map((function(e){return e.id}))}this.$confirm({title:"确认删除选择的数据?",okText:"删除",okType:"danger",cancelText:"取消",onOk:function(){t.Base.submit(null,{url:"auditWhitelist/deleteById",data:{ids:a},autoValid:!1},{successCallback:function(e){t.$message.success("删除成功！"),t.queryTableData()},failCallback:function(e){t.$message.error("删除失败！")}})},onCancel:function(){t.$message.info("用户取消操作")}})},editSave:function(e,t){var a=this;this.editRow=e,this.editRow.bfmedtype=e.medtype,this.modal="edit",this.handleSearch(e.akc191),this.handleSearch2(e.ake001),this.addWhiteList=!0,this.$nextTick((function(){a.form1.setFieldsValue({currPers:e.akc191,ake001:e.ake001,medtype:e.medtype}),a.currPersSelect.aac003=a.editRow.aac003,a.currPersSelect.akc190=a.editRow.akc190,a.currPersSelect.akc191=a.editRow.akc191,a.ake001Select.value=a.editRow.ake001,a.ake001Select.label=a.editRow.ake002}))},queryTableData:function(){this.$refs.gridPager.loadData()},fnReset:function(){this.baseInfoForm.resetFields(),this.queryTableData()},autoFormCreate:function(e){this.baseInfoForm=e},getParam:function(){var e=this.baseInfoForm.getFieldsValue(),t=e.aae040;if(t){if(t[0]&&!t[1])throw this.$message.error("请选择完整时间范围！"),new Error("请选择完整时间范围！");if(t[1]&&!t[0])throw this.$message.error("请选择完整时间范围！"),new Error("请选择完整时间范围！")}return t&&(t[0]&&(e.startDate=t[0].format("YYYY-MM-DD")),t[1]&&(e.endDate=t[1].format("YYYY-MM-DD"))),e},exportExcel:function(){var e,t=this,a=[],i=this.$refs.infoTableRef.getColumns(),l=(0,r.Z)(i);try{for(l.s();!(e=l.n()).done;){var n=e.value;"序号"!==n.title&&"操作"!==n.title&&"checkbox"!==n.type&&a.push({header:n.title,key:n.property,width:20})}}catch(s){l.e(s)}finally{l.f()}var o=[{columnKey:"medtype",customCollection:function(e,t){"0"==e.value?e.value="门诊":e.value="住院"}}];this.Base.submit(null,{url:"auditWhitelist/queryAuditWhitelistInfoList",data:this.getParam(),autoValid:!1},{successCallback:function(e){var i={fileName:"审核白名单数据表",sheets:[{name:"worksheet1",column:{complex:!1,columns:a},rows:e.data.AuditWhitelistList,codeList:o}]};t.Base.generateExcel(i)},failCallback:function(e){t.$message.error("审核白名单数据表导出失败")}})}}},h=f,m=a(1001),p=(0,m.Z)(h,i,l,!1,null,"457fb0c3",null),b=p.exports},36766:function(e,t,a){"use strict";var i=a(66586);t["Z"]=i.Z},26263:function(e,t,a){"use strict";a.d(t,{s:function(){return i},x:function(){return l}});var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:e.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[e._v(e._s(e.title))]),e._t("default")],2)},l=[]},66586:function(e,t){"use strict";var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};t["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}},55382:function(){},61219:function(){}}]);
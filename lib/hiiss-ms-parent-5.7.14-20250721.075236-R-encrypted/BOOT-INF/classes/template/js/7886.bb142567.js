(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[7886],{88412:function(e,t,i){"use strict";var l=i(26263),o=i(36766),a=i(1001),r=(0,a.Z)(o.Z,l.s,l.x,!1,null,"5e7ef0ae",null);t["Z"]=r.exports},72782:function(e,t,i){"use strict";i.r(t),i.d(t,{default:function(){return m}});var l=function(){var e=this,t=this,i=t.$createElement,l=t._self._c||i;return l("div",{staticClass:"fit"},[l("ta-border-layout",{attrs:{layout:{header:"180px",footer:"0px"},showPadding:!0,"footer-cfg":{showBorder:!1}}},[l("div",{attrs:{slot:"header"},slot:"header"},[l("ta-title",{attrs:{title:"查询条件"}}),l("ta-form",{staticStyle:{"padding-top":"5px"},attrs:{autoFormCreate:function(t){e.searchForm=t},layout:"horizontal",formLayout:!0}},[l("ta-form-item",{attrs:{span:6,label:"知识元",fieldDecoratorId:"ykz018"}},[l("ta-input",{attrs:{placeholder:"请输入"}})],1),l("ta-form-item",{attrs:{span:6,label:"规则类别",fieldDecoratorId:"ykz199"}},[l("ta-select",{attrs:{placeholder:"请选择",collectionType:"MYKZ199",allowClear:!0,"show-search":!0}})],1),l("ta-form-item",{attrs:{span:6,label:"违规定性",fieldDecoratorId:"volaQualCodg"}},[l("ta-select",{attrs:{showSearch:"",placeholder:"请选择",allowClear:!0,"collection-type":"MYKZ200"}})],1),l("ta-form-item",{attrs:{span:6,label:"违规程度",fieldDecoratorId:"ruleSevDeg"}},[l("ta-select",{attrs:{placeholder:"请选择",allowClear:!0,"collection-type":"MAPE800"}})],1),l("ta-form-item",{attrs:{span:6,label:"监控场景",fieldDecoratorId:"ruleid"}},[l("ta-select",{attrs:{placeholder:"请选择",allowClear:!0,"show-search":!0}},t._l(t.ruleidList,(function(e,i){return l("ta-select-option",{key:i,attrs:{value:e.ruleid}},[t._v(" "+t._s(e.ruleidlog)+" ")])})),1)],1),l("ta-form-item",{attrs:{span:6,label:"更新日期",fieldDecoratorId:"updateTimeRange"}},[l("ta-range-picker",{attrs:{"allow-clear":!0}})],1),l("ta-form-item",{attrs:{span:4,label:"医保编码",fieldDecoratorId:"ake001","label-width":"92px"}},[l("ta-input")],1),l("ta-form-item",{attrs:{span:4,label:"医保名称",fieldDecoratorId:"ake002","label-width":"92px"}},[l("ta-input")],1),l("ta-button",{staticStyle:{"margin-right":"10px",float:"right"},attrs:{type:"default",icon:"redo"},on:{click:t.resetSearch}},[t._v("重置 ")]),l("ta-button",{staticStyle:{"margin-right":"20px",float:"right"},attrs:{type:"primary",icon:"search"},on:{click:t.onSearch}},[t._v("查询 ")])],1)],1),l("div",{staticClass:"fit content-box"},[l("ta-title",{staticStyle:{flex:"none","margin-left":"5px","margin-top":"5px"},attrs:{title:"本级规则列表"}},["base"===t.ruleFindModifyType?l("div",{staticStyle:{float:"right"}},[l("ta-button",{attrs:{type:"primary"},on:{click:function(e){return t.modifyAPE800()}}},[t._v("批量修改违规程度")]),t.ruleFindExportVisible?l("ta-button",{staticStyle:{position:"absolute",right:"6px",bottom:"6px"},attrs:{type:"primary",icon:"download"},on:{click:t.goexport}},[t._v(" 导出 ")]):t._e()],1):t._e()]),l("div",{staticClass:"content-table"},[l("ta-big-table",{ref:"ruleTable",attrs:{columns:t.columns,data:t.ruleList,border:"","empty-text":"-",height:"auto","highlight-hover-row":"","auto-resize":"","show-overflow":"",resizable:"","row-key":!0,"row-id":"ykz277"},scopedSlots:t._u([{key:"supnRuleStas",fn:function(e){var i=e.row;return["3"==i.supnRuleStas||"6"==i.supnRuleStas||"7"==i.supnRuleStas?l("ta-tag",{attrs:{color:"red"}},[t._v(t._s(t.CollectionLabel("M_RULE_STAS",i.supnRuleStas))+" ")]):t._e(),"5"==i.supnRuleStas?l("ta-tag",{attrs:{color:"green"}},[t._v(" "+t._s(t.CollectionLabel("M_RULE_STAS",i.supnRuleStas))+" ")]):t._e(),"0"==i.supnRuleStas||"1"==i.supnRuleStas||"4"==i.supnRuleStas?l("ta-tag",{attrs:{color:"blue"}},[t._v(t._s(t.CollectionLabel("M_RULE_STAS",i.supnRuleStas))+" ")]):t._e()]}},{key:"modifyRecord",fn:function(e){var i=e.row;return[i.updatedtime?l("span",[t._v(" "+t._s("修改人:"+i.updatedby+",修改时间:"+i.updatedtime+",修改理由:"+i.updatereason)+" ")]):t._e()]}},{key:"operation",fn:function(e){var i=e.row;return["base"===t.ruleFindModifyType?l("div",{staticClass:"opareteItem",on:{click:function(e){return t.goModify(i)}}},[t._v(" 修改 ")]):t._e(),"base"===t.ruleFindModifyType?l("ta-divider",{attrs:{type:"vertical"}}):t._e(),l("div",{staticClass:"opareteItem",on:{click:function(e){return t.goDetail(i)}}},[t._v(" 详情 ")])]}}])})],1),l("div",{staticClass:"content-box-footer"},[l("ta-pagination",{ref:"rulePager",style:t.exportStyle,attrs:{defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"],dataSource:t.ruleList,params:t.pageParams,url:"mttRuleFind/pageRule"},on:{"update:dataSource":function(e){t.ruleList=e},"update:data-source":function(e){t.ruleList=e}}})],1)],1)]),t.detailVisible?l("div",{staticStyle:{width:"100%",height:"100%",position:"absolute",top:"0",left:"0","z-index":"10",background:"white"}},[l("rule-detail",{attrs:{params:this.params},on:{close:t.detailClose}})],1):t._e(),l("ta-modal",{attrs:{title:"批量修改违规程度",visible:t.batchModefyVisible,height:220,width:450},on:{ok:function(e){return t.handleBatchModefy()},cancel:t.closeBatchModefyModel}},[l("ta-form",{attrs:{"auto-form-create":function(t){return e.form1=t},layout:"horizontal","form-layout":!0,"label-width":"120px",col:{xs:1,sm:1,md:1,lg:1,xl:1,xxl:1}}},[l("ta-form-item",{attrs:{label:"违规程度","field-decorator-id":"ape800",require:!0,span:22}},[l("ta-select",{attrs:{placeholder:"请选择违规程度",allowClear:!0,"collection-type":"MAPE800"}})],1),l("ta-form-item",{attrs:{label:"修改原因","field-decorator-id":"reasons",require:!0,span:22}},[l("ta-textarea",{attrs:{placeholder:"请输入修改原因","max-length":100,"show-max-length":!0,rows:3}})],1)],1)],1)],1)},o=[],a=i(66347),r=i(95082),n=i(469),s=i(22722),c=i(55115),d=i(88412);c.w3.prototype.Base=Object.assign(c.w3.prototype.Base,(0,r.Z)({},s.Z));var u={name:"ruleFind",components:{TaTitle:d.Z,ruleDetail:n["default"]},data:function(){return{blnShowAll:!1,volaQualList:[],ruleidList:[],columns:[{field:"checkbox",type:"checkbox",title:"",minWidth:"40",align:"center"},{type:"seq",title:"序号",minWidth:"60",align:"center"},{field:"ykz277",title:"规则id",minWidth:100,align:"center",overflowTooltip:!0},{field:"ruletype",title:"规则分类",minWidth:120,align:"center",overflowTooltip:!0},{field:"aaa166",title:"规则大类",minWidth:150,align:"center",overflowTooltip:!1},{field:"ykz018",title:"知识元",minWidth:200,align:"center",overflowTooltip:!0},{field:"source",title:"规则依据",minWidth:200,align:"center",overflowTooltip:!0},{field:"ruleidlog",title:"监控场景",minWidth:100,align:"center",overflowTooltip:!0},{field:"ykz199",title:"规则类别",collectionType:"MYKZ199",minWidth:100,align:"center",overflowTooltip:!0},{field:"volaqualcodg",title:"违规定性",collectionType:"MYKZ200",minWidth:100,align:"center",overflowTooltip:!0},{field:"rulever",title:"规则版本",minWidth:100,align:"center",overflowTooltip:!0},{field:"rulesevdeg",title:"违规程度",collectionType:"MAPE800",minWidth:80,align:"center",overflowTooltip:!0},{field:"controllevel",title:"管控等级",collectionType:"CONTROLLEVEL",minWidth:80,align:"center",overflowTooltip:!0},{field:"operationTime",title:"发布日期",minWidth:150,align:"center",overflowTooltip:!0},{field:"modifyRecord",title:"修改记录",minWidth:150,align:"center",overflowTooltip:!0,customRender:{default:"modifyRecord"}},{field:"operation",title:"操作",fixed:"right",minWidth:120,align:"center",customRender:{default:"operation"}}],columns2:[{field:"ykz277",title:"规则id",minWidth:100,align:"center",overflowTooltip:!0},{field:"aaa166",title:"规则大类",minWidth:150,align:"center",overflowTooltip:!1},{field:"ykz018",title:"知识元",minWidth:200,align:"center",overflowTooltip:!0},{field:"ruleidlog",title:"监控场景",minWidth:100,align:"center",overflowTooltip:!0},{field:"ykz199",title:"规则类别",collectionType:"MYKZ199",minWidth:100,align:"center",overflowTooltip:!0},{field:"volaQualName",title:"违规定性",minWidth:100,align:"center",overflowTooltip:!0},{field:"rulever",title:"规则版本",minWidth:100,align:"center",overflowTooltip:!0},{field:"ruleSevDeg",title:"违规程度",collectionType:"M_SEV_DEG",minWidth:70,align:"center",overflowTooltip:!0},{field:"controllevel",title:"管控等级",collectionType:"CONTROLLEVEL",minWidth:70,align:"center",overflowTooltip:!0},{field:"",title:"上报日期",minWidth:150,align:"center",overflowTooltip:!0},{field:"",title:"区域划分代码",collectionType:"",minWidth:100,align:"center",overflowTooltip:!0},{field:"",title:"医保划分",collectionType:"",minWidth:70,align:"center",overflowTooltip:!0},{field:"",title:"规则级别",collectionType:"",minWidth:70,align:"center",overflowTooltip:!0},{field:"operation",title:"操作",minWidth:70,align:"center",customRender:{default:"operation"}}],ruleList:[],ruleList2:[],editVisible:!1,detailVisible:!1,batchModefyVisible:!1,ruleFindModifyType:faceConfig.ruleFindModifyType,params:{},ruleFindExportVisible:!1,exportStyle:{"text-align":"right","margin-top":"10px"}}},mounted:function(){this.listVolaQualA(),this.listRuleid(),this.$route.query.ykz010&&(this.searchForm.setFieldsValue({ykz018:this.$route.query.ykz010}),this.searchForm.setFieldsValue({ruleid:this.$route.query.ruleid})),this.exportVisible(),this.onSearch()},methods:{detailClose:function(){this.detailVisible=!1,this.onSearch()},handleBatchModefy:function(){var e=this,t=this.$refs.ruleTable.getCheckboxRecords();this.form1.validateFields((function(i){if(!i&&t.length>0){var l=e.form1.getFieldsValue();l.records=t,e.Base.submit(null,{url:"violationModify/insertBatch",data:l,autoValid:!1,autoQs:!1},{successCallback:function(t){e.$message.success("修改成功！"),e.closeBatchModefyModel()},failCallback:function(t){e.$message.error("修改失败！")}})}}))},closeBatchModefyModel:function(){this.form1.resetFields(),this.batchModefyVisible=!1,this.onSearch()},modifyAPE800:function(){var e=this.$refs.ruleTable.getCheckboxRecords();0!==e.length?(e.length>1&&this.$message.warning("批量修改，请谨慎操作！"),this.batchModefyVisible=!0,this.$nextTick((function(){}))):this.$message.error("请选择需要修改的数据！")},exportVisible:function(){var e=this;Base.submit(null,{url:"miimCommonRead/getAa01Aaa005ByAaz499",data:{aaz499:190}}).then((function(t){var i=t.data.aaa005;["Y","y"].includes(i)&&(e.ruleFindExportVisible=!0,e.exportStyle={position:"absolute",right:"6px",bottom:"6px","margin-right":"100px"})}))},onSearch:function(){var e=this;this.$refs.rulePager.loadData(),this.$nextTick((function(){"base"===e.ruleFindModifyType?(e.$refs.ruleTable.showColumn(e.$refs.ruleTable.getColumnByField("checkbox")),e.$refs.ruleTable.showColumn(e.$refs.ruleTable.getColumnByField("modifyRecord"))):(e.$refs.ruleTable.hideColumn(e.$refs.ruleTable.getColumnByField("checkbox")),e.$refs.ruleTable.hideColumn(e.$refs.ruleTable.getColumnByField("modifyRecord")))}))},listVolaQualA:function(){var e=this;Base.submit(null,{url:"mttRuleConfig/listVolaQualA"}).then((function(t){e.volaQualList=t.data.list}))},listRuleid:function(){var e=this;Base.submit(null,{url:"mttRuleConfig/listRuleid"}).then((function(t){e.ruleidList=t.data.list}))},pageParams:function(){var e=this.searchForm.getFieldsValue();if(e.updateTimeRange&&2==e.updateTimeRange.length){var t=e.updateTimeRange[0].format("YYYY-MM-DD"),i=e.updateTimeRange[1].format("YYYY-MM-DD");e.timeStart=t,e.timeEnd=i,e.updateTimeRange=null}return{dtoStr:JSON.stringify(e)}},goDetail:function(e){this.detailVisible=!0,this.params=e,this.params.modify=!0},goModify:function(e){this.detailVisible=!0,this.params=e,this.params.modify=!1},goStart:function(e){},goEnd:function(e){},goAdd:function(){alert("新增")},doDelete:function(e){alert("作废")},resetSearch:function(){var e=this;this.searchForm.resetFields(),this.$nextTick((function(){e.onSearch()}))},callback:function(e){},goexport:function(){var e=this,t=this.searchForm.getFieldsValue();if(t.updateTimeRange&&2==t.updateTimeRange.length){var i=t.updateTimeRange[0].format("YYYY-MM-DD"),l=t.updateTimeRange[1].format("YYYY-MM-DD");t.timeStart=i,t.timeEnd=l,t.updateTimeRange=null}var o,r=[],n=this.columns,s=(0,a.Z)(n);try{for(s.s();!(o=s.n()).done;){var c=o.value;"seq"!==c.type&&"操作"!==c.title&&r.push({header:c.title,key:c.field,width:20})}}catch(u){s.e(u)}finally{s.f()}var d=[{codeType:"MYKZ199",columnKey:"ykz199"},{codeType:"MYKZ200",columnKey:"volaqualcodg"},{codeType:"MAPE800",columnKey:"rulesevdeg"}];this.Base.submit(null,{url:"mttRuleFind/exportRule",data:{dtoStr:JSON.stringify(t)},autoValid:!1},{successCallback:function(t){var i={fileName:"规则查询列表导出结果表",sheets:[{name:"worksheet1",column:{complex:!1,columns:r},codeList:d,rows:t.data.data}]};e.Base.generateExcel(i)},failCallback:function(t){e.$message.error("规则查询列表导出失败")}})}}},f=u,p=i(1001),h=(0,p.Z)(f,l,o,!1,null,"a0234f0e",null),m=h.exports},36766:function(e,t,i){"use strict";var l=i(66586);t["Z"]=l.Z},26263:function(e,t,i){"use strict";i.d(t,{s:function(){return l},x:function(){return o}});var l=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("h3",{staticStyle:{"font-weight":"normal"}},[i("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:e.titleSvg}}),i("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[e._v(e._s(e.title))]),e._t("default")],2)},o=[]},66586:function(e,t){"use strict";var i={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};t["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:i}}}},55382:function(){},61219:function(){}}]);
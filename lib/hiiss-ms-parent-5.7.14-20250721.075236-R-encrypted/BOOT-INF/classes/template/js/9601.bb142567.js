"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[9601],{79601:function(t,a,e){e.r(a),e.d(a,{default:function(){return m}});var i=function(){var t=this,a=this,i=a.$createElement,r=a._self._c||i;return r("div",{staticClass:"fit"},[r("div",{staticStyle:{height:"53%"}},[r("ta-border-layout",{staticStyle:{"border-bottom":"none"},attrs:{layout:{header:"63px",left:"50%"}}},[r("div",{attrs:{slot:"header"},slot:"header"},[r("ta-form",{staticStyle:{position:"absolute",width:"100%"},attrs:{autoFormCreate:function(a){t.form=a},layout:"horizontal",formLayout:!0}},[r("ta-row",[r("ta-col",{staticStyle:{width:"290px",top:"8px"},attrs:{span:"6"}},[r("ta-radio-group",{on:{change:a.switchDataType},model:{value:a.dataRadioValue,callback:function(t){a.dataRadioValue=t},expression:"dataRadioValue"}},[r("ta-radio",{attrs:{value:1}},[a._v(a._s(a.radioTitle))]),r("ta-radio",{attrs:{value:2}},[a._v("环比增速")]),r("ta-radio",{attrs:{value:3}},[a._v("同比增速")])],1)],1),r("ta-col",{staticStyle:{width:"290px",left:"50px",top:"8px"},attrs:{span:"4"}},[r("ta-radio-group",{on:{change:a.switchDateType},model:{value:a.dateRadioValue,callback:function(t){a.dateRadioValue=t},expression:"dateRadioValue"}},[r("ta-radio",{attrs:{value:1}},[a._v("月")]),r("ta-radio",{attrs:{value:2}},[a._v("季")]),r("ta-radio",{attrs:{value:3}},[a._v("年")])],1)],1),r("ta-form-item",{staticStyle:{"margin-left":"-4%"},attrs:{fieldDecoratorId:"month",hidden:a.monthHidden,span:3,fieldDecoratorOptions:{initialValue:a.initDate}}},[r("ta-month-picker",{attrs:{format:"YYYYMM"}})],1),r("ta-form-item",{staticStyle:{"margin-left":"-4%"},attrs:{fieldDecoratorId:"quarter",hidden:a.quarterHidden,span:3,fieldDecoratorOptions:{initialValue:a.initDate}}},[r("ta-quarter-picker")],1),r("ta-form-item",{staticStyle:{"margin-left":"-4%"},attrs:{fieldDecoratorId:"year",hidden:a.yearHidden,span:3,fieldDecoratorOptions:{initialValue:a.initDate}}},[r("ta-year-picker")],1),r("ta-form-item",{attrs:{fieldDecoratorId:"yzb001",hidden:!0}},[r("ta-input")],1),r("ta-form-item",{attrs:{fieldDecoratorId:"dx",hidden:!0}},[r("ta-input")],1),4!=a.key?r("ta-form-item",{staticStyle:{"margin-left":"-3%"},attrs:{fieldDecoratorId:"aka130",labelCol:{span:9},wrapperCol:{span:12},require:{message:"必输项!"},span:4,initValue:a.CollectionData("AKA130ZK")&&a.CollectionData("AKA130ZK").length>0?a.CollectionData("AKA130ZK")[0].value:""}},[r("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[a._v("医疗类型")]),r("ta-select",{staticStyle:{width:"100%"},attrs:{"collection-type":"AKA130ZK",allowClear:!1}})],1):a._e(),r("ta-form-item",{attrs:{label:"医保类型",span:4,fieldDecoratorId:"aae141"}},[r("ta-select",{staticStyle:{width:"130px","margin-left":"5px"},attrs:{"collection-type":"AAE141ZK",allowClear:!0}})],1),r("ta-form-item",{staticStyle:{"margin-left":"-3%"},attrs:{label:"险种类型",span:4,fieldDecoratorId:"aae140"}},[r("ta-select",{staticStyle:{width:"130px","margin-left":"5px"},attrs:{"collection-type":"AAE140ZK",allowClear:!0}})],1),r("ta-button",{attrs:{type:"primary",icon:"search"},on:{click:a.fnClickQuery}},[a._v("查询")])],1)],1)],1),r("div",{staticClass:"fit",attrs:{slot:"left"},slot:"left"},[r("div",{staticStyle:{"border-bottom":"1px solid rgba(153,153,153,0.14)"}},[r("ta-title",{attrs:{title:a.leftUpperTitle}},[a._t("default",(function(){return[a._v("构成")]}))],2),r("ta-form",{attrs:{autoFormCreate:function(a){t.form1=a}}},[r("ta-form-item",{staticStyle:{float:"right",width:"150px","margin-top":"-30px","margin-bottom":"-25px"},attrs:{fieldDecoratorId:"aaa100",fieldDecoratorOptions:{initialValue:a.aaa100FirstValue}}},[r("ta-select",{attrs:{size:"small",options:a.aaa100Data},on:{select:a.fnSelectAaa100}})],1)],1)],1),r("ta-echarts",{key:a.constituteKey,ref:"constituteChart",staticStyle:{height:"80%",width:"100%"},attrs:{option:{}}})],1),r("div",{staticClass:"fit"},[r("div",{staticStyle:{"border-bottom":"1px solid rgba(153,153,153,0.14)"}},[r("ta-title",{attrs:{title:a.rightUpperTitle}},[a._t("default",(function(){return[a._v("趋势")]}))],2)],1),r("ta-echarts",{key:a.trendkey,ref:"trendChart",staticStyle:{height:"80%",width:"100%"},attrs:{option:{}}})],1)])],1),r("div",{staticStyle:{height:"47%"}},[r("ta-border-layout",[r("div",{staticClass:"fit"},[r("div",{staticStyle:{"border-bottom":"1px solid rgba(153,153,153,0.14)"}},[r("ta-title",{attrs:{title:a.leftLowerTitle}},[a._t("default",(function(){return[a._v("排名 "),r("ta-popover",{attrs:{width:"100",trigger:"hover",content:"点击更改为升序"}},[a.showAsc?r("img",{staticStyle:{width:"18px",height:"18px"},attrs:{slot:"reference",src:e(48468)},on:{click:a.fnAscClick},slot:"reference"}):r("img",{staticStyle:{width:"18px",height:"18px"},attrs:{slot:"reference",src:e(6430)},on:{click:a.fnAscClick},slot:"reference"})]),r("ta-popover",{attrs:{width:"100",trigger:"hover",content:"点击更改为降序"}},[a.showDesc?r("img",{staticStyle:{width:"18px",height:"18px"},attrs:{slot:"reference",src:e(47546)},on:{click:a.fnDescClick},slot:"reference"}):r("img",{staticStyle:{width:"18px",height:"18px"},attrs:{slot:"reference",src:e(28304)},on:{click:a.fnDescClick},slot:"reference"})])]})),r("span",{staticStyle:{display:"inline-block",float:"right"}},[a.orderShowLast?r("img",{staticStyle:{width:"20px",height:"20px"},attrs:{src:e(53949)},on:{click:a.fnOrderLast}}):r("img",{staticStyle:{width:"20px",height:"20px"},attrs:{src:e(89712)}}),a.orderShowNext?r("img",{staticStyle:{width:"20px",height:"20px"},attrs:{src:e(23554)},on:{click:a.fnOrderNext}}):r("img",{staticStyle:{width:"20px",height:"20px"},attrs:{src:e(61368)}})])],2)],1),r("ta-echarts",{key:a.orderkey,ref:"orderChart",staticStyle:{height:"80%",width:"90%",left:"60px"},attrs:{option:{}}})],1)])],1)])},r=[],s=(e(32564),e(36797)),o=e.n(s),n=e(10530),l=(e(16411),e(66993)),h=e(55115),d=e(88412);h.w3.use(n.Z);var c={components:{TaTitle:d.Z},data:function(){return{dataRadioValue:1,dateRadioValue:1,monthHidden:!1,quarterHidden:!0,yearHidden:!0,initDate:o()(new Date),title:"",leftUpperTitle:"",rightUpperTitle:"",leftLowerTitle:"",rightLowerTitle:"",dp03:[],aaa100Data:[],aaa100FirstValue:"",radioTitle:"",constituteOption:[],trendOption:[],orderOption:[],tabsOption:[],param:{},yzb001:"",yzb003:"",yzb004:"",dx:"",ysm:"",tabType:"aaz263",tabTypeName:"aae387",orderType:"desc",echartsUnit:"万元",orderTotalPages:0,orderPageNumber:1,orderPageSize:15,tabTotalPages:0,tabPageNumber:1,tabPageSize:10,akb021:"",ake002:"",aaa102:"",aaz263:"",aaz307:null,aae387:null,ake001:"",y_val:0,y2_val:40,showAsc:!1,showDesc:!0,tabShowLast:!1,tabShowNext:!1,orderShowLast:!1,orderShowNext:!1,constituteKey:1,trendkey:2,orderkey:3}},methods:{moment:o(),fnInIt:function(){var t=this;this.constituteKey+="constitute",this.trendkey+="trend",this.orderkey+="order",this.tabskey+="tabs",this.fnGetAAA100(),this.form.setFieldsValue({yzb001:this.$route.query.yzb001});var a={url:"rankAnaly/initPage",data:{yzb001:this.$route.query.yzb001},autoValid:!0},e={successCallback:function(a){t.dp03=a.data.dp03;var e="undefined"==t.$route.query.aae141?"":t.$route.query.aae141,i="undefined"==t.$route.query.aae140?"":t.$route.query.aae140;t.form.setFieldsValue({dx:"ys",aae141:e,aae140:i}),t.aaz263=t.$route.query.aaz263,t.ysm=t.$route.query.ysm,t.dateRadioValue=parseInt(t.$route.query.dateRadioValue),t.aae043=t.$route.query.aae043,t.ape032=t.$route.query.ape032,"M"==t.ape032?t.form.setFieldsValue({month:o()(t.aae043,"YYYYMM")}):"S"==t.ape032?t.form.setFieldsValue({quarter:o()(t.aae043,"YYYYQ")}):"Y"==t.ape032&&t.form.setFieldsValue({year:o()(t.aae043,"YYYY")}),t.radioTitle=t.dp03.yzb004,t.title=t.dp03.yzb004,t.ake002=t.dp03.yzb004,t.yzb003=t.dp03.yzb003,t.leftUpperTitle=t.dp03.yzb004,t.rightUpperTitle=t.dp03.yzb004,t.leftLowerTitle=t.dp03.yzb004,t.rightLowerTitle="医疗机构"+t.dp03.yzb004,t.param.orderPageNumber=1,t.param.tabPageNumber=1,setTimeout((function(){t.fnGetConsist(),t.fnGetTrend(),t.leftLowerTitle=t.ake002,t.fnGetOrder()}),100)},failCallback:function(t){}};this.Base.submit(null,a,e)},fnClickQuery:function(){this.constituteKey+="constitute",this.trendkey+="trend",this.orderkey+="order",this.fnReset(),this.fnGetConsist(),this.fnGetTrend(),this.fnGetOrder()},fnReset:function(){this.ake001=null,this.aaa102=null,this.tabPageNumber=1,this.orderPageNumber=1},fnGetAAA100:function(){var t=this,a={url:"rankAnaly/getDp02",data:{yzb001:this.$route.query.yzb001},autoValid:!0},e={successCallback:function(a){t.aaa100Data=a.data.aaa100Data,t.aaa100FirstValue=t.aaa100Data[0].value},failCallback:function(t){}};this.Base.submit(null,a,e)},switchDateType:function(t){1==t.target.value?(this.monthHidden=!1,this.quarterHidden=!0,this.yearHidden=!0):2==t.target.value?(this.monthHidden=!0,this.quarterHidden=!1,this.yearHidden=!0):(this.monthHidden=!0,this.quarterHidden=!0,this.yearHidden=!1)},switchDataType:function(t){this.fnReset(),1==this.dataRadioValue?this.echartsUnit="万元":this.echartsUnit="%",this.orderPageNumber=1,this.tabPageNumber=1,this.fnInIt()},fnGetParamter:function(){return this.param=this.form.getFieldsValue(),this.param.month=this.form.getFieldValue("month").format("YYYYMM"),this.param.quarter=this.form.getFieldValue("quarter").format("YYYYQ"),this.param.year=this.form.getFieldValue("year").format("YYYY"),this.param.yzb003=this.yzb003,this.param.dataRadioValue=this.dataRadioValue,this.param.dateRadioValue=this.dateRadioValue,this.param.aaa100=this.form1.getFieldValue("aaa100"),this.param.aaz263=this.aaz263,this.param.aae387=this.aaz263,this.param.tabType=this.tabType,this.param.aaa102=this.aaa102,this.param.ake001=this.ake001,1==this.dateRadioValue?(this.param.aae043e=this.param.month,this.param.ape032="M"):2==this.dateRadioValue?(this.param.aae043e=this.param.quarter,this.param.ape032="S"):(this.param.aae043e=this.param.year,this.param.ape032="Y"),this.param},fnGetConsist:function(){var t=this;this.param=this.fnGetParamter();var a={url:"rankAnaly/getConsist",data:this.param,autoValid:!0},e={successCallback:function(a){t.constituteOption=a.data.constituteOption,1==t.dataRadioValue?t.$refs.constituteChart.updateOptions(l.Z.createPie(t.constituteOption,"name","val",t.echartsUnit)):t.$refs.constituteChart.updateOptions(l.Z.createStandBar(t.constituteOption,"name","val",t.echartsUnit,"id")),t.$refs.constituteChart.myChart.off("click"),t.$refs.constituteChart.myChart.on("click",(function(a){t.fnGetConsistCallback(a)}))},failCallback:function(t){}};this.Base.submit(this.form,a,e)},fnGetConsistCallback:function(t){this.orderPageNumber=1,this.tabPageNumber=1,this.akb021="",this.ake002=t.name,this.aaa102=t.data.id,this.ake001="",this.leftLowerTitle=this.ake002,this.fnGetOrder(),this.fnGetTrend(),null==this.aae387&&this.fnGetTabs()},fnSelectAaa100:function(){var t=this;setTimeout((function(){t.fnGetConsist()}),30)},fnGetTrend:function(){var t=this;this.param=this.fnGetParamter(),this.rightUpperTitle=this.ake002;var a={url:"rankAnaly/getTrend",data:this.param,autoValid:!0},e={successCallback:function(a){t.trendOption=a.data.trendOption,t.$refs.trendChart.updateOptions(l.Z.createLine(t.trendOption,"name","val",t.echartsUnit,t.title)),t.$refs.trendChart.myChart.off("click"),t.$refs.trendChart.myChart.on("click",(function(t){}))},failCallback:function(t){}};this.Base.submit(this.form,a,e)},fnGetOrder:function(){var t=this;this.param=this.fnGetParamter(),this.param.pageSize=this.orderPageSize,this.param.pageNumber=this.orderPageNumber,this.param.orderType=this.orderType,this.param.id_col="ake001",this.param.name_col="ake002",this.param.orderType=this.orderType;var a={url:"rankAnaly/getOrder",data:this.param,autoValid:!0},e={successCallback:function(a){t.orderOption=a.data.orderOption.data,t.orderTotalPages=a.data.orderOption.totalPages,t.orderPageNumber*t.orderPageSize>=t.orderTotalPages?(t.orderShowNext=!1,t.y2_val=0):(t.orderShowNext=!0,t.y2_val=40),1==t.orderPageNumber?(t.orderShowLast=!1,t.y_val=0):(t.orderShowLast=!0,t.y_val=40),t.$refs.orderChart.updateOptions(l.Z.createStandBar(t.orderOption,"name","val",t.echartsUnit,"id")),t.$refs.orderChart.myChart.off("click"),t.$refs.orderChart.myChart.on("click",(function(a){t.fnGetOrderCallback(a)}))},failCallback:function(t){}};this.Base.submit(this.form,a,e)},fnGetOrderCallback:function(t){this.akb021="",this.ake001=t.data.id,this.ake002=t.name,this.aaa102="",this.fnGetTrend(),this.aae387,"aaz307"==this.tabType?this.rightLowerTitle="科室"+this.ake002:this.rightLowerTitle="医师"+this.ake002},fnAscClick:function(){this.showDesc=!1,this.showAsc=!0,this.orderType="asc",this.y_val=0,this.y2_val=0,this.orderPageNumber=1,this.fnGetOrder()},fnDescClick:function(){this.showDesc=!0,this.showAsc=!1,this.orderType="desc",this.y_val=0,this.y2_val=0,this.orderPageNumber=1,this.fnGetOrder()},fnOrderLast:function(){this.orderPageNumber-=1,this.fnGetOrder(),this.orderPageNumber*this.orderPageSize>=this.orderTotalPages?(this.orderShowNext=!1,this.y2_val=0):(this.orderShowNext=!0,this.y2_val=40),1==this.orderPageNumber?(this.orderShowLast=!1,this.y_val=0):(this.orderShowLast=!0,this.y_val=40)},fnOrderNext:function(){this.orderPageNumber+=1,this.fnGetOrder(),this.orderPageNumber*this.orderPageSize>=this.orderTotalPages?(this.orderShowNext=!1,this.y2_val=0):(this.orderShowNext=!0,this.y2_val=40),1==this.orderPageNumber?(this.orderShowLast=!1,this.y_val=0):(this.orderShowLast=!0,this.y_val=40)},fnGetTabs:function(){var t=this;this.param=this.fnGetParamter(),this.param.pageSize=this.tabPageSize,this.param.pageNumber=this.tabPageNumber,this.param.id_col=this.tabType,this.param.name_col=this.tabTypeName,this.param.orderType="desc",this.rightLowerTitle="医疗机构"+this.ake002;var a={url:"rankAnaly/getTabs",data:this.param,autoValid:!0},e={successCallback:function(a){t.tabTotalPages=a.data.tabsOption.totalPages,t.tabsOption=a.data.tabsOption.data,t.tabPageNumber*t.tabPageSize>=t.tabTotalPages?t.tabShowNext=!1:t.tabShowNext=!0,1==t.tabPageNumber?t.tabShowLast=!1:t.tabShowLast=!0,t.$refs.tabsChart.updateOptions(l.Z.createStandBar(t.tabsOption,"name","val",t.echartsUnit,"id")),t.$refs.tabsChart.myChart.off("click"),t.$refs.tabsChart.myChart.on("click",(function(a){t.fnGetTabsCallback(a)}))},failCallback:function(t){}};this.Base.submit(this.form,a,e)},fnGetTabsCallback:function(t){},fnTabLast:function(){this.tabPageNumber-=1,this.fnGetTabs(),this.tabPageNumber*this.tabPageSize>=this.tabTotalPages?this.tabShowNext=!1:this.tabShowNext=!0,1==this.tabPageNumber?this.tabShowNext=!1:this.tabShowNext=!0},fnTabNext:function(){this.tabPageNumber+=1,this.fnGetTabs(),this.tabPageNumber*this.tabPageSize>=this.tabTotalPages?this.tabShowNext=!1:this.tabShowNext=!0,1==this.tabPageNumber?this.tabShowNext=!1:this.tabShowNext=!0}},mounted:function(){this.fnInIt()}},u=c,f=e(1001),p=(0,f.Z)(u,i,r,!1,null,"4c4d22b2",null),m=p.exports}}]);
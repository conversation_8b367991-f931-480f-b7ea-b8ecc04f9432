"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[442],{70442:function(t,e,a){a.r(e),a.d(e,{default:function(){return X}});var i=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("div",{staticClass:"fit"},[i("ta-border-layout",{staticStyle:{},attrs:{"layout-type":"fixTop"}},[i("div",{staticStyle:{"padding-top":"10px"},attrs:{slot:"header"},slot:"header"},[i("div",{staticClass:"query-header"},[i("div",[i("ta-form",{attrs:{"auto-form-create":function(e){return t.form=e},layout:"horizontal","form-layout":!0,"label-width":"80px",col:{xs:1,sm:2,md:2,lg:4,xl:4,xxl:4}}},[i("ta-form-item",{attrs:{label:"清算期号","field-decorator-id":"issueNo","init-value":e.moment().format("YYYYMM")}},[i("ta-month-picker",{staticStyle:{width:"100%"},attrs:{format:"YYYYMM","value-format":"YYYYMM","allow-clear":!0},on:{change:function(t){return e.fnGetDeptList(t)}}})],1),i("ta-form-item",{attrs:{label:"疑点类型","field-decorator-id":"dataType","init-value":"1"}},[i("ta-select",{attrs:{"allow-clear":!0,placeholder:"疑点类型筛选"}},[i("ta-select-option",{attrs:{value:"1"}},[e._v(" 初审疑点 ")]),i("ta-select-option",{attrs:{value:"2"}},[e._v(" 复审疑点 ")]),i("ta-select-option",{attrs:{value:"3"}},[e._v(" 病例申诉 ")]),i("ta-select-option",{attrs:{value:"4"}},[e._v(" 外部疑点 ")])],1)],1),i("ta-form-item",{attrs:{"label-width":"200px",span:12}},[i("div",{staticStyle:{"margin-left":"20px"}},[i("ta-button",{staticStyle:{"margin-right":"20px"},attrs:{icon:"search",type:"primary"},on:{click:e.queryTableData}},[e._v(" 查询 ")]),i("ta-button",{staticStyle:{"margin-right":"20px"},attrs:{icon:"upload",type:"primary"},on:{click:function(t){return e.handleOpen("1")}}},[e._v(" 导入 ")]),e.enabeledCenterAppeal?i("ta-button",{staticStyle:{"margin-right":"20px"},attrs:{icon:"download",type:"primary"},on:{click:function(t){return e.openVisibleModal()}}},[e._v(" 两定疑点下载 ")]):e._e(),i("ta-popover",{attrs:{"popper-class":"popover-class",width:"200",trigger:"click",placement:"bottom",offset:200},model:{value:e.popoverVisible,callback:function(t){e.popoverVisible=t},expression:"popoverVisible"}},[i("div",{staticStyle:{"font-size":"16px"}},[i("ta-button",{staticStyle:{"margin-right":"6px",border:"none"},on:{click:e.fnOpenSuspectTemplateWin}},[e._v(" 疑点模板设置 ")])],1),i("div",{staticStyle:{"font-size":"16px"}},[i("ta-button",{staticStyle:{"margin-right":"6px",border:"none"},on:{click:function(t){return e.handleOpen("2")}}},[e._v(" 算法设置 ")])],1),i("div",{staticStyle:{"font-size":"16px"}},[i("ta-button",{staticStyle:{"margin-right":"6px",border:"none"},on:{click:function(t){return e.handleOpen("3")}}},[e._v(" 流程设置 ")])],1),i("ta-icon",{staticStyle:{color:"#3382f5","font-size":"16px","margin-top":"8px",cursor:"pointer"},attrs:{slot:"reference",type:"setting"},slot:"reference"})],1)],1)])],1)],1),i("div")])]),i("div",{staticClass:"fit"},[i("div",{staticClass:"part-top"},[i("ta-title",{staticStyle:{float:"left"},attrs:{title:"导入结果"}})],1),i("div",{staticStyle:{height:"calc(100% - 54px)"}},[i("ta-big-table",{ref:"xTable",attrs:{border:"","show-overflow":"","keep-source":"","highlight-hover-row":"","tooltip-config":{theme:"light",contentMethod:e.tooltipMethod},"sort-config":{trigger:"cell"},data:e.dataSource,height:"auto",resizable:"","auto-resize":"",size:"small","empty-text":"-",width:"min-width",align:"center","header-align":"center","edit-config":{trigger:"click",mode:"cell",showStatus:!0},"keyboard-config":{isArrow:!0,isDel:!0,isEnter:!0,isTab:!0,isEdit:!0},"checkbox-config":{trigger:"row",highlight:!0,reserve:!0}},on:{"edit-closed":e.editClosed},scopedSlots:e._u([{key:"bottomBar",fn:function(){return[i("ta-pagination",{ref:"gridPager",staticStyle:{"text-align":"right"},attrs:{size:"small","default-page-size":200,"page-size-options":["30","50","100","200","500"],"data-source":e.dataSource,url:e.pageUrl,params:e.pageParams},on:{"update:dataSource":function(t){e.dataSource=t},"update:data-source":function(t){e.dataSource=t}}})]},proxy:!0}])},[i("ta-big-table-column",{attrs:{type:"seq",fixed:"left","min-width":"50px",title:"序号",align:"center"}}),i("ta-big-table-column",{attrs:{field:"aae043","show-overflow":"",title:"清算期号","min-width":"100px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"akc195","show-overflow":"",title:"申诉截止日期","min-width":"150px",align:"center","edit-render":{name:"$date-picker",props:{format:"YYYY-MM-DD",allowClear:!1},size:"small"},sortable:""}}),i("ta-big-table-column",{attrs:{"show-overflow":"",title:"医疗类别","min-width":"120px",field:"aka130","sort-by":"aka130",sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;t.column;return[i("div",{staticStyle:{position:"relative"}},e._l(a.aka130.split(","),(function(t,l){return i("span",[t?i("span",[e._v(" "+e._s(e.CollectionLabel("AKA130",t))+" "+e._s(a.aka130.split(",").length==l+1?"":",")+" ")]):i("span")])})),0)]}}])}),i("ta-big-table-column",{attrs:{field:"aae141","show-overflow":"","collection-type":"aae141",title:"参保地","min-width":"100px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"dataType","show-overflow":"",title:"疑点类型","min-width":"100px","sort-by":"dataType",sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;t.column;return[i("div",{staticStyle:{position:"relative"}},["1"===a.dataType?i("div",[e._v(" 初审扣款 ")]):"2"===a.dataType?i("div",[e._v(" 复审扣款 ")]):"3"===a.dataType?i("div",[e._v(" 病例申诉 ")]):i("div",[e._v(" 外部疑点 ")])])]}}])}),i("ta-big-table-column",{attrs:{field:"pointNum","show-overflow":"",title:"疑点条目(条)","min-width":"130px",sortable:""}}),i("ta-big-table-column",{attrs:{field:"matchSuccessNum","show-overflow":"",title:"系统匹配情况","min-width":"150px","sort-by":"matchSuccessNum",sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;t.column;return[i("div",{staticStyle:{position:"relative"}},[i("div",[i("span",[e._v("成功"+e._s(a.matchSuccessNum)+"条,失败"+e._s(a.pointNum-a.matchSuccessNum)+"条")])])])]}}])}),i("ta-big-table-column",{attrs:{field:"handStas","show-overflow":"",title:"申诉任务状态","min-width":"180px","sort-by":"handStas",sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;t.column;return[i("div",{staticStyle:{position:"relative"}},["0"===a.handStas?i("div",[i("ta-badge",{attrs:{color:"#2db7f5"}}),i("span",{staticStyle:{color:"#2db7f5"}},[e._v("已导入")])],1):"1"===a.handStas||"2"===a.handStas?i("div",[i("ta-badge",{attrs:{color:"#8302fd"}}),i("span",{staticStyle:{color:"#8302fd"}},[e._v("待处理")])],1):["3","5","6","8","9"].includes(a.handStas)?i("div",[i("ta-badge",{attrs:{color:"#f49924"}}),i("span",{staticStyle:{color:"#f49924"}},[e._v("处理中")])],1):"7"===a.handStas?i("div",[i("ta-badge",{attrs:{color:"#71b505"}}),i("span",{staticStyle:{color:"#71b505"}},[e._v("已完成")])],1):"11"===a.handStas?i("div",[i("ta-badge",{attrs:{color:"#979797"}}),i("span",{staticStyle:{color:"#979797"}},[e._v("已过期")])],1):i("div",[i("ta-badge",{attrs:{color:"#b53405"}}),i("span",{staticStyle:{color:"#b53405"}},[e._v("导入失败")])],1)])]}}])}),i("ta-big-table-column",{attrs:{field:"fileUnCreateNum","show-overflow":"",title:"材料生成状态","min-width":"200px","sort-by":"fileUnCreateNum",sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;t.column;return[i("div",{staticStyle:{position:"relative"}},[i("div",[i("span",[e._v("未生成"+e._s(a.fileUnCreateNum)+"条,")]),i("span",[e._v("生成中"+e._s(a.fileCreatingNum)+"条,")]),i("span",[e._v("成功"+e._s(a.fileCreateSuccessNum)+"条,")]),i("span",[e._v("失败"+e._s(a.fileCreateFailNum)+"条")])])])]}}])}),i("ta-big-table-column",{attrs:{field:"statusCounts","show-overflow":"",title:"申诉任务进度","min-width":"200px","sort-by":"handStas",sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;t.column;return["2"===a.dataType?i("div",[e._v(" "+e._s(a.statusCounts)+" ")]):i("div",[e._v(" "+e._s(a.statusCounts+",（其中已过期"+a.expired+"条）")+" ")])]}}])}),i("ta-big-table-column",{attrs:{field:"importDate","show-overflow":"",title:"导入日期","min-width":"100px",sortable:""}}),i("ta-big-table-column",{attrs:{fixed:"right","show-overflow":"",field:"operate",title:"操作","min-width":"150px",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("div",{staticClass:"opareteItem",on:{click:function(t){return e.importRecord(a)}}},[e._v(" 导入记录 ")]),i("ta-divider",{attrs:{type:"vertical"}}),i("div",{staticClass:"opareteItem",on:{click:function(t){return e.reGenerate(a)}}},[e._v(" 查看明细 ")])]}}])})],1)],1)]),i("ta-modal",{attrs:{title:"算法设置",visible:e.algorConfig,height:560,width:1050},on:{cancel:e.handleClose2}},[i("div",[i("ta-radio-group",{on:{change:e.onChange},model:{value:e.pointChange,callback:function(t){e.pointChange=t},expression:"pointChange"}},e._l(e.pointList,(function(t,a){return i("ta-radio",{key:a,style:e.radioStyle,attrs:{value:t.algorithmKey}},["DynamicAlgorithm"===t.algorithmKey?i("span",[i("span",{staticStyle:{"font-size":"16px"}},[i("b",[e._v("动态算法")])]),i("br"),i("span",{staticStyle:{"margin-left":"20px"}},[e._v("匹配数据源:"),i("br"),i("ta-checkbox-group",{staticStyle:{"margin-left":"20px"},on:{change:e.onAAE500Change},model:{value:e.aae500check,callback:function(t){e.aae500check=t},expression:"aae500check"}},[i("ta-row",e._l(e.filterList,(function(t,a){return i("ta-col",{attrs:{span:4}},[i("ta-checkbox",{key:a,attrs:{value:t.toString()}},[e._v(e._s(e.CollectionLabel("AAE500",t.toString())))])],1)})),1)],1)],1),i("br"),i("span",{staticStyle:{"margin-left":"20px"}},[e._v("疑点匹配&备案理由算法:")]),i("br"),i("div",{staticStyle:{"margin-left":"20px"}},[i("ta-form-model",{ref:"dynamicValidateForm",refInFor:!0,attrs:{layout:"inline",model:e.dynamicValidateForm}},[e._l(e.dynamicValidateForm.domains,(function(t,a){return i("ta-form-model-item",{key:t.key,staticStyle:{width:"360px"},attrs:{prop:"domains."+a+".value"}},[0!==a&&a!==e.dynamicValidateForm.domains.length?i("span",{staticStyle:{margin:"0 5px 0 5px"}},[e._v("+")]):e._e(),i("ta-select",{staticStyle:{width:"150px"},attrs:{placeholder:"选择匹配字段","show-search":"",options:e.ae11List},on:{change:e.domainChange},model:{value:t.columnValue,callback:function(a){e.$set(t,"columnValue",a)},expression:"domain.columnValue"}}),i("ta-select",{directives:[{name:"show",rawName:"v-show",value:t.secondShow,expression:"domain.secondShow"}],staticStyle:{width:"190px"},attrs:{disabled:!0,placeholder:"选择日期格式",options:e.dateList},model:{value:t.secondValue,callback:function(a){e.$set(t,"secondValue",a)},expression:"domain.secondValue"}}),e.dynamicValidateForm.domains.length>1?i("ta-icon",{staticClass:"dynamic-delete-button",attrs:{type:"minus-circle-o",disabled:1===e.dynamicValidateForm.domains.length},on:{click:function(a){return e.removeDomain(t)}}}):e._e(),(a+1)%3===0&&a!==e.dynamicValidateForm.domains.length-1?i("br"):e._e()],1)})),i("ta-form-model-item",[i("ta-button",{attrs:{size:"small",type:"primary",shape:"circle",icon:"plus"},on:{click:e.addDomain}})],1)],2)],1),i("span",{staticStyle:{"margin-left":"20px"}},[e._v("手动匹配:")]),i("br"),i("div",{staticStyle:{"margin-left":"20px"}},[i("ta-form-model",{ref:"dynamicValidateForm",refInFor:!0,attrs:{layout:"inline",model:e.dynamicValidateForm2}},[e._l(e.dynamicValidateForm2.domains,(function(t,a){return i("ta-form-model-item",{key:t.key,staticStyle:{width:"360px"},attrs:{prop:"domains."+a+".value"}},[0!==a&&a!==e.dynamicValidateForm2.domains.length?i("span",{staticStyle:{margin:"0 5px 0 5px"}},[e._v("+")]):e._e(),i("ta-select",{staticStyle:{width:"150px"},attrs:{placeholder:"选择匹配字段","show-search":"",options:e.ae11List},on:{change:e.domainChange2},model:{value:t.columnValue,callback:function(a){e.$set(t,"columnValue",a)},expression:"domain.columnValue"}}),i("ta-select",{directives:[{name:"show",rawName:"v-show",value:t.secondShow,expression:"domain.secondShow"}],staticStyle:{width:"190px"},attrs:{placeholder:"选择日期格式",options:t.secondOptions},model:{value:t.secondValue,callback:function(a){e.$set(t,"secondValue",a)},expression:"domain.secondValue"}}),e.dynamicValidateForm2.domains.length>1?i("ta-icon",{staticClass:"dynamic-delete-button",attrs:{type:"minus-circle-o",disabled:1===e.dynamicValidateForm2.domains.length},on:{click:function(a){return e.removeDomain2(t)}}}):e._e(),(a+1)%3===0&&a!==e.dynamicValidateForm2.domains.length-1?i("br"):e._e()],1)})),i("ta-form-model-item",[i("ta-button",{attrs:{size:"small",type:"primary",shape:"circle",icon:"plus"},on:{click:e.addDomain2}})],1)],2)],1),i("div",{staticStyle:{"margin-left":"20px","white-space":"pre-wrap",width:"700px"}},[e._v("注:如果匹配到多条疑点,则使用有备案理由的,如果是有多条备案理由,则选择最新一条; 如果匹配到的疑点均无备案理由,则使用最新一次的历史备案理由")])]):i("span",{domProps:{innerHTML:e._s(t.introduceText)}})])})),1)],1),i("div",{attrs:{slot:"footer"},slot:"footer"},[i("ta-button",{on:{click:e.handleClose2}},[e._v(" 取消 ")]),i("ta-button",{staticStyle:{"margin-right":"calc(50% - 70px)"},attrs:{type:"primary"},on:{click:e.handleSave}},[e._v(" 保存 ")])],1)]),i("ta-modal",{attrs:{title:"流程设置",visible:e.processConfig,height:710,width:750},on:{cancel:e.handleClose3}},[i("div",[i("span",{staticClass:"labelTitle",staticStyle:{"font-size":"11px",color:"#989898"}},[e._v("该设置仅对医保拒付和病例申诉有效。")]),i("ta-form",{staticStyle:{"margin-top":"10px"},attrs:{layout:"vertical","auto-form-create":function(e){return t.processform=e}}},[i("ta-form-item",{attrs:{"field-decorator-id":"matchFilReason","init-value":"0",fieldDecoratorOptions:{rules:[{required:!0,message:"必须选取"}]}}},[i("span",{staticClass:"labelTitle",attrs:{slot:"label"},slot:"label"},[e._v(" 1、是否需要匹配备案理由作为申诉理由"),i("span",{staticStyle:{"font-size":"11px",color:"#989898"}},[e._v("（仅对医保拒付有效）")])]),i("ta-radio-group",[i("ta-radio",{attrs:{value:"0"}},[e._v("是")]),i("ta-radio",{attrs:{value:"1"}},[e._v("否")])],1)],1),i("ta-form-item",{attrs:{"field-decorator-id":"appealMaterial","init-value":"0",fieldDecoratorOptions:{rules:[{required:!0,message:"必须选取"}]}}},[i("span",{staticClass:"labelTitle",attrs:{slot:"label"},slot:"label"},[e._v(" 2、是否需要生成申诉材料"),i("span",{staticStyle:{"font-size":"11px",color:"#989898"}},[e._v("（仅对医保拒付有效）")])]),i("ta-radio-group",{on:{change:e.complainChange}},[i("ta-radio",{attrs:{value:"0"}},[e._v("是")]),i("ta-radio",{attrs:{value:"1"}},[e._v("否")])],1)],1),i("ta-form-item",{attrs:{label:"申诉模板文件目录地址","field-decorator-id":"tempFiledir",disabled:e.complainDisabled}},[i("ta-input",{attrs:{placeholder:"示例:D:\\HIISS_V5\\complain\\template\\"}})],1),i("ta-form-item",{attrs:{label:"申诉模板文件内容的医院名称","field-decorator-id":"hosName",disabled:e.complainDisabled}},[i("ta-input",{attrs:{placeholder:"示例:xx省银海第一医院"}})],1),i("ta-form-item",{attrs:{"field-decorator-id":"fileSavedir",fieldDecoratorOptions:{rules:[{required:!0,message:"必填项"}]}}},[i("span",{staticClass:"labelTitle",attrs:{slot:"label"},slot:"label"},[e._v(" 3、申诉材料文件存储地址 ")]),i("ta-input",{attrs:{placeholder:"示例:D:/hiiss/appeal/"}})],1),i("ta-form-item",{attrs:{"field-decorator-id":"attachConfig"}},[i("span",{staticClass:"labelTitle",attrs:{slot:"label"},slot:"label"},[e._v("     4、附件配置 ")]),i("div",{staticStyle:{height:"calc(100%)",width:"100%"}},[i("div",[i("span",{staticClass:"ant-form-item-required"},[e._v("文件大小限制:")]),i("ta-input-number",{staticStyle:{width:"120px","margin-left":"10px"},attrs:{placeholder:"请输入整数",min:1,max:10240},model:{value:e.configNum,callback:function(t){e.configNum=t},expression:"configNum"}}),i("ta-select",{staticStyle:{width:"70px","margin-left":"10px"},attrs:{placeholder:"选择单位",defaultValue:"KB"},model:{value:e.configUnit,callback:function(t){e.configUnit=t},expression:"configUnit"}},[i("ta-select-option",{attrs:{value:"KB"}},[e._v("KB")]),i("ta-select-option",{attrs:{value:"MB"}},[e._v("MB")])],1)],1),i("div",{staticClass:"container"},[i("div",{staticClass:"form-item"},[i("label",{staticClass:"form-label",attrs:{for:"report"}},[e._v("报销扣款附件类型:")]),i("ta-textarea",{attrs:{id:"report",placeholder:"请用英文逗号分隔开","max-length":100,"show-max-length":!0,rows:3},model:{value:e.attachConfigTypeOne,callback:function(t){e.attachConfigTypeOne=t},expression:"attachConfigTypeOne"}})],1),i("div",{staticClass:"form-item"},[i("label",{staticClass:"form-label",attrs:{for:"case"}},[e._v("病例申诉附件类型:")]),i("ta-textarea",{attrs:{id:"case",placeholder:"示例：病例首页,病程记录,麻醉记录,手术记录","max-length":100,"show-max-length":!0,rows:3},model:{value:e.attachConfigTypeTwo,callback:function(t){e.attachConfigTypeTwo=t},expression:"attachConfigTypeTwo"}})],1)])])]),i("ta-form-item",{attrs:{"field-decorator-id":"nameRule"}},[i("span",{staticClass:"ant-form-item-required labelTitle",attrs:{slot:"label"},slot:"label"},[e._v(" 5、申诉材料命名规则 ")]),i("div",{staticStyle:{height:"calc(100%)",width:"100%"}},[i("span",[e._v(" 命名展示： "+e._s(e.displayName)+" ")]),i("div",{staticStyle:{"margin-top":"5px"}},[i("ta-select",{staticStyle:{width:"30%"},attrs:{placeholder:"请选择命名","allow-clear":!0,options:e.configOptions},model:{value:e.nameRule1,callback:function(t){e.nameRule1=t},expression:"nameRule1"}}),i("span",{staticStyle:{margin:"10px"}},[e._v("+")]),i("ta-select",{staticStyle:{width:"30%"},attrs:{placeholder:"请选择命名","allow-clear":!0,options:e.configOptions},model:{value:e.nameRule2,callback:function(t){e.nameRule2=t},expression:"nameRule2"}}),i("span",{staticStyle:{margin:"10px"}},[e._v("+")]),i("ta-select",{staticStyle:{width:"30%"},attrs:{placeholder:"请选择命名","allow-clear":!0,options:e.configOptions},model:{value:e.nameRule3,callback:function(t){e.nameRule3=t},expression:"nameRule3"}})],1)])]),i("ta-form-item",{attrs:{"field-decorator-id":"enabeledCenterAppeal","init-value":!1,valuePropName:"checked"}},[i("ta-switch",{attrs:{"checked-children":"是","un-checked-children":"否"}}),i("span",{staticClass:"labelTitle",attrs:{slot:"label"},slot:"label"},[e._v(" 6、开启两定平台接口申诉  ")])],1),i("ta-form-item",{attrs:{"field-decorator-id":"enabeledHosArea","init-value":!1,valuePropName:"checked"}},[i("ta-switch",{attrs:{"checked-children":"是","un-checked-children":"否"}}),i("span",{staticClass:"labelTitle",attrs:{slot:"label"},slot:"label"},[e._v(" 7、申诉下发时选择申诉处理人是否限制为本院区人员  ")])],1)],1)],1),i("div",{attrs:{slot:"footer"},slot:"footer"},[i("ta-button",{on:{click:e.handleClose3}},[e._v(" 取消 ")]),i("ta-button",{staticStyle:{"margin-right":"calc(50% - 70px)"},attrs:{type:"primary"},on:{click:e.handleSave2}},[e._v(" 保存 ")])],1)]),i("ta-modal",{attrs:{title:"导入",visible:e.uploadVisible,height:260,width:500},on:{cancel:function(t){return e.handleClose("1")}}},[i("ta-form",{attrs:{"auto-form-create":function(e){return t.form1=e},layout:"horizontal","form-layout":!0,"label-width":"80px",col:{xs:1,sm:1,md:1,lg:1,xl:1,xxl:1}}},[i("ta-form-item",{attrs:{label:"清算期号","field-decorator-id":"issueNo","init-value":e.moment().format("YYYYMM"),require:!0}},[i("ta-month-picker",{staticStyle:{width:"100%"},attrs:{format:"YYYYMM","value-format":"YYYYMM","allow-clear":!1}})],1),i("ta-form-item",{attrs:{label:"参保地","field-decorator-id":"medinsuType",require:!0}},[i("ta-select",{attrs:{placeholder:"参保地筛选","collection-type":"AAE141"}})],1),i("ta-form-item",{attrs:{label:"疑点类型","field-decorator-id":"dataType",require:!0,"allow-clear":!0}},[i("ta-select",{attrs:{placeholder:"疑点类型筛选"},on:{change:e.medinsuTypeChange}},[i("ta-select-option",{attrs:{value:"1"}},[e._v(" 初审扣款 ")]),i("ta-select-option",{attrs:{value:"2"}},[e._v(" 复审扣款 ")]),i("ta-select-option",{attrs:{value:"3"}},[e._v(" 病例申诉 ")]),i("ta-select-option",{attrs:{value:"4"}},[e._v(" 外部疑点 ")])],1)],1),i("ta-form-item",{staticClass:"uploadFormItem",attrs:{label:"选择文件","field-decorator-id":"fileName",require:!0}},[i("ta-input",{staticClass:"uploadInput",attrs:{placeholder:"请选择文件上传",disabled:!0}}),i("ta-upload",{staticClass:"uploadBtn",attrs:{disabled:e.fileUploadDisabled,"file-list":e.fileList,"before-upload":e.beforeUpload,"show-upload-list":!1}},[i("ta-button",{attrs:{disabled:e.fileUploadDisabled,type:"primary"}},[e._v(" 浏览 ")])],1)],1)],1),i("div",{attrs:{slot:"footer"},slot:"footer"},[i("ta-button",{on:{click:function(t){return e.handleClose("1")}}},[e._v(" 取消 ")]),i("ta-button",{staticStyle:{"margin-right":"calc(50% - 70px)"},attrs:{type:"primary"},on:{click:e.handleUpload}},[e._v(" 导入 ")])],1)],1),i("ta-modal",{attrs:{title:"拒付数据分析",visible:e.uploadVisible2,height:230,width:320},on:{cancel:function(){t.uploadVisible2=!1,t.reLoad=!0}}},[i("ta-form",{attrs:{"auto-form-create":function(e){return t.form2=e},layout:"horizontal","form-layout":!0,"label-width":"80px",col:{xs:1,sm:1,md:1}}},[i("ta-form-item",{attrs:{label:"期号","field-decorator-id":"issueNo","init-value":[e.moment().format("YYYYMM"),e.moment().format("YYYYMM")],require:!0}},[i("ta-range-picker",{staticStyle:{width:"100%"},attrs:{type:"month",format:"YYYYMM","value-format":"YYYYMM","allow-one":!0}})],1),i("ta-form-item",{attrs:{label:"医保类型","field-decorator-id":"hiFeesetlType",require:!0}},[i("ta-select",{staticStyle:{width:"80%"},attrs:{"collection-type":"aae141"}})],1),i("ta-form-item",{attrs:{label:"就诊类型","field-decorator-id":"medType",require:!0}},[i("ta-select",{staticStyle:{width:"80%"},attrs:{"collection-filter":"11,21,14","reverse-filter":!0,"collection-type":"AKA130"}})],1)],1),i("div",{attrs:{slot:"footer"},slot:"footer"},[i("ta-button",{staticStyle:{"margin-right":"calc(50% - 50px)"},attrs:{type:"primary"},on:{click:e.handleCreate}},[e._v(" 生成 ")])],1)],1),i("ta-modal",{attrs:{height:155,width:450,title:"提示"},model:{value:e.visible,callback:function(t){e.visible=t},expression:"visible"}},[i("p",[e._v("期号"+e._s(e.issueNo)+"当前导入条件下已存在数据，请选择导入方式:")]),i("p",[e._v("追加导入:本次导入教据将追加在原数据后")]),i("p",[e._v("覆盖导入:原数据将被覆盖,包括已下发的任务数据,请谨慎操作")]),i("template",{slot:"footer"},[i("ta-button",{attrs:{type:"primary"},on:{click:function(t){return e.confirmImport("1")}}},[e._v(" 追加导入 ")]),i("ta-button",{attrs:{type:"primary"},on:{click:function(t){return e.confirmImport("2")}}},[e._v(" 覆盖导入 ")])],1)],2),i("ta-modal",{attrs:{title:"两定平台疑点下载",height:260,width:500},on:{cancel:e.closeVisibleModal},model:{value:e.centerDownloadModalVisible,callback:function(t){e.centerDownloadModalVisible=t},expression:"centerDownloadModalVisible"}},[i("div",[i("ta-form",{staticStyle:{"margin-top":"10px"},attrs:{autoFormCreate:function(e){t.baseInfoForm=e},layout:"horizontal",formLayout:!0}},[i("ta-form-item",{attrs:{fieldDecoratorId:"allDate",span:24,label:"月份",labelCol:{span:4},"init-value":e.rangeValue,fieldDecoratorOptions:{rules:[{required:!0,message:"必须选取日期"}]}}},[i("ta-month-picker",{staticStyle:{width:"100%"}})],1),e.hosList.length>1?i("ta-form-item",{attrs:{"field-decorator-id":"akb020List",label:"机构",span:24,labelCol:{span:4},fieldDecoratorOptions:{rules:[{required:!0,message:"必须选取院区"}]}}},[i("ta-select",{attrs:{mode:"multiple",showSearch:"",options:e.hosList},on:{select:e.handleSelectHosp,change:e.selectHospChange}})],1):e._e(),i("br"),i("ta-descriptions",{staticStyle:{"margin-left":"20px",width:"91%"},attrs:{title:""}},[i("ta-descriptions-item",{attrs:{label:" "}},[e._v(" 说明：系统从两定平台自动下载申诉疑点，下载时间可能较长，下载成功后系统会发送消息，请留意系统右上角消息通知。点击“接口测试”，可测试两定平台接口连接是否异常。 ")])],1)],1)],1),i("div",{attrs:{slot:"footer"},slot:"footer"},[i("ta-button",{on:{click:e.closeVisibleModal}},[e._v(" 关闭 ")]),i("ta-button",{on:{click:function(t){return e.testCenterInterface()}}},[e._v(" 接口测试 ")]),i("ta-button",{staticStyle:{"margin-right":"20px"},attrs:{type:"primary"},on:{click:e.downloadCenterData}},[e._v(" 下载 ")])],1)]),i("ta-modal",{attrs:{title:"导入记录",visible:e.importRecordView,destroyOnClose:!0,height:400,width:800},on:{cancel:e.handleImportClose}},[i("div",{staticStyle:{height:"100%"}},[i("ta-border-layout",{attrs:{"show-border":!1,"header-cfg":{showBorder:!1}}},[i("div",[i("ta-big-table",{ref:"importRecordTable",attrs:{height:"330px","auto-resize":"",resizable:"","show-overflow":"","highlight-hover-row":"",border:"",align:"center","header-align":"center",size:"mini",data:e.importData}},[i("ta-big-table-column",{attrs:{type:"seq",fixed:"left",width:"50px",title:"序号",align:"center"}}),i("ta-big-table-column",{attrs:{field:"impnum",align:"center",sortable:"","show-overflow":"",title:"疑点条目(条)","min-width":"100px"}}),i("ta-big-table-column",{attrs:{field:"aae040",align:"center",sortable:"","show-overflow":"",title:"导入日期","min-width":"150px"}}),i("ta-big-table-column",{attrs:{field:"status",align:"center",sortable:"","show-overflow":"",title:"导入状态","min-width":"100px"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;t.column;return[i("div",{class:"1"==a.status?"status-success":"status-error"},[e._v(" "+e._s("1"==a.status?"导入成功":"已撤销导入")+" ")])]}}])}),i("ta-big-table-column",{attrs:{fixed:"right",field:"operate",title:"操作","min-width":"100px"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("ta-table-operate",{attrs:{"operate-menu":e.operateMenu,rowInfo:t}})]}}])}),i("template",{slot:"bottomBar"},[i("ta-pagination",{ref:"importRecordGridPager",staticStyle:{"text-align":"right"},attrs:{size:"small","default-page-size":500,"hide-on-single-page":!0,"page-size-options":["100","200","500","1000","2000"],"data-source":e.importData,params:function(){return t.mateRow},url:"hiddscgPoint/getImportHistoryData"},on:{"update:dataSource":function(t){e.importData=t},"update:data-source":function(t){e.importData=t}}})],1)],2)],1)])],1),i("div",{attrs:{slot:"footer"},slot:"footer"},[i("div",{staticStyle:{display:"flex","justify-content":"center"}},[i("ta-button",{staticStyle:{"text-align":"center"},attrs:{type:"primary"},on:{click:e.handleImportClose}},[e._v(" 关闭 ")])],1)])])],1),i("div",[i("set-suspect-template-win",{ref:"setSuspectTemplateWin",attrs:{enabeledCenterAppeal:e.enabeledCenterAppeal}})],1)],1)},l=[],o=a(89584),n="hiddscgPoint/",s={getPageUrl:function(){return n+"queryHiddscgPoint"},newgetPageUrl:function(){return n+"queryKf38SummaryTableData"},getDeptList:function(t,e){Base.submit(null,{url:"miimCommonRead/getAppealQueryData",data:t},{successCallback:function(t){return e(t)}})},getColumns:function(t,e){Base.submit(null,{url:n+"queryTableColumn",data:t},{successCallback:function(t){return e(t)}})},getColumnsInfo:function(t,e){Base.submit(null,{url:n+"updateTableColumn",data:t},{successCallback:function(t){return e(t)}})},saveColumnsInfo:function(t,e){Base.submit(null,{url:n+"saveTableColumn",data:t,autoQs:!1},{successCallback:function(t){return e(t)}})},uploadDatas:function(t,e){Base.submit(null,{url:n+"importExcel",data:t,autoQs:!1,isFormData:!0},{successCallback:function(t){return e(t)}})},getUploadFilter:function(t,e){Base.submit(null,{url:n+"importCheckLegal",data:t},{successCallback:function(t){return e(t)}})},handleBatchGenerate:function(t,e){Base.submit(null,{url:n+"createAppeal",data:t},{successCallback:function(t){return e(t)}})},reGenerate:function(t,e){Base.submit(null,{url:n+"generateSingleAppealAgain",data:t},{successCallback:function(t){return e(t)}})},downloadCenterData:function(t,e){Base.submit(null,{url:n+"downloadCenterData",data:t},{successCallback:function(t){return e(t)}})},queryAkb020List:function(t,e){Base.submit(null,{url:"miimCommonRead/queryHospRpcList",data:t},{successCallback:function(t){return e(t)}})},testCenterInterface:function(t,e){Base.submit(null,{url:n+"testCenterInterface",data:t},{successCallback:function(t){return e(t)}})}},r=a(36797),c=a.n(r),d=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",t._l(t.tagList,(function(e){return a("ta-tag",{key:e.label,attrs:{color:e.checked?e.color2:e.color1},on:{click:function(a){return t.handleChange(e)}}},[a("span",{staticClass:"tag-icon"},[t._v(t._s(e.icon))]),a("span",{staticClass:"tag-label"},[t._v(" "+t._s(e.label)+" ")])])})),1)},u=[],m=[{value:"1",label:"待审批",checked:!0,color1:"orange",color2:"#FCB76B"},{value:"2",label:"审批通过未导出",checked:!1,color1:"green",color2:"#87d068"},{value:"3",label:"审批通过已导出",checked:!1,color1:"purple",color2:"#7d02d1"},{value:"4",label:"审批驳回待提交",checked:!1,color1:"red",color2:"#fa0000"},{value:"5",label:"全部",checked:!1,color1:"blue",color2:"#2db7f5"}],f={name:"tagChange",props:{tagValue:{type:String},statusArr:{type:Array,default:function(){return m}}},data:function(){return{tagList:this.statusArr}},watch:{tagValue:function(t){this.changeTag(t)}},mounted:function(){this.changeTag(this.tagValue)},methods:{changeTag:function(t){this.tagList=this.tagList.map((function(e){return e.value===t?e.checked=!0:e.checked=!1,e}))},handleChange:function(t){this.$emit("change",t)}}},p=f,h=a(1001),b=(0,h.Z)(p,d,u,!1,null,"14432a73",null),g=b.exports,y=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},v=[],w={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '},C={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:w}}},k=C,S=(0,h.Z)(k,y,v,!1,null,"5e7ef0ae",null),x=S.exports,_=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"fit"},[a("ta-modal",{attrs:{title:"疑点模板设置",visible:t.modalVisible,"destroy-on-close":!0,"mask-closable":!1,height:530,width:1e3},on:{cancel:t.onVisibleModal}},[a("ta-radio-group",{on:{change:t.changeRadioGroup},model:{value:t.templateFlag,callback:function(e){t.templateFlag=e},expression:"templateFlag"}},t._l(t.templateTypeList,(function(e){return a("ta-radio-button",{key:e.value,attrs:{value:e.value}},[t._v(" "+t._s(e.label)+" ")])})),1),a("p",[t._v("说明：请在模板列名称中填写中心下发疑点数据的对应列字段名称,系统会按照模板列名称进行数据导入和数据展示。")]),a("ta-big-table",{attrs:{border:"",stripe:"",resizable:"","highlight-hover-row":"",height:"400","header-align":"center",size:"small","edit-config":{trigger:"click",mode:"cell",showStatus:!0,activeMethod:t.activeMethod},data:t.tableData}},[a("ta-big-table-column",{attrs:{field:"columnValue",title:"系统疑点列",header:""},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.row;return["医疗类别"===i.columnValue?a("div",[t._v(" "+t._s(i.columnValue)+" "),a("ta-tooltip",{attrs:{placement:"top"}},[a("template",{slot:"title"},[a("span",{staticStyle:{"font-size":"12px"}},[t._v(" 如果疑点模板列中医疗类别是 akc310str，则用文本判断。"),a("br"),t._v(" 例子：普通住院→住院场景，普通门诊→门诊场景。系统在对应场景的表中查找匹配数据。"),a("br"),t._v(" 如果疑点模板列中医疗类别是 akc310，则用住院门诊号对应。系统按住院门诊号在所有场景中查找患者，住院门诊号按配置的 akc190/akc191 判断。 ")])]),a("ta-icon",{attrs:{type:"question-circle"}})],2)],1):a("div",[t._v(" "+t._s(i.columnValue)+" ")])]}}])}),a("ta-big-table-column",{attrs:{field:"explain",width:"210",title:"系统疑点列说明"}}),a("ta-big-table-column",{attrs:{field:"alias",title:"模板列名称","edit-render":{name:"$input",style:{color:"red",width:"100%"}}}}),t.enabeledCenterAppeal?a("ta-big-table-column",{attrs:{field:"centerColumnName",title:"两定接口字段","edit-render":{name:"$input",style:{color:"red",width:"100%"}}}}):t._e(),a("ta-big-table-column",{attrs:{field:"columnKey",title:"匹配字段","edit-render":{name:"$input",style:{color:"red",width:"100%"}}}}),a("ta-big-table-column",{attrs:{field:"columnTypeValue",width:"230",title:"日期格式选择","edit-render":{name:"$select",props:{options:t.dateList},style:{color:"blue",width:"100%"}}}})],1),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("ta-button",{on:{click:function(e){return t.fnOpenAddColumnWin("0")}}},[t._v(" 新增数据 ")]),a("ta-button",{on:{click:t.onVisibleModal}},[t._v(" 取消 ")]),a("ta-button",{staticStyle:{"margin-right":"calc(50% - 70px)"},attrs:{type:"primary"},on:{click:t.fnSubmitOk}},[t._v(" 保存 ")])],1)],1),a("edit-template-column-win",{ref:"editTemplateColumnWin",on:{parentQuery:t.fnInitPageData}})],1)},V=[],T=[{label:"yyyy-MM-dd hh:mm:ss",value:"yyyy-MM-dd hh:mm:ss"},{label:"yyyy-MM-dd",value:"yyyy-MM-dd"},{label:"yyyy-MM-dd HH:mm:ss",value:"yyyy-MM-dd HH:mm:ss"},{label:"yyyy/MM/dd hh:mm:ss",value:"yyyy/MM/dd hh:mm:ss"},{label:"yyyy/MM/dd HH:mm:ss",value:"yyyy/MM/dd HH:mm:ss"},{label:"yyyy/MM/d H:mm:ss",value:"yyyy/MM/d H:mm:ss"},{label:"MM/dd/yyyy hh:mm:ss a",value:"MM/dd/yyyy hh:mm:ss a"},{label:"MM/dd/yyyy HH:mm:ss",value:"MM/dd/yyyy HH:mm:ss"},{label:"dd-MM-yyyy HH:mm:ss",value:"dd-MM-yyyy HH:mm:ss"},{label:"yyyy/MM/dd HH:mm",value:"yyyy/MM/dd HH:mm"},{label:"yyyy年MM月dd日",value:"yyyy年MM月dd日"},{label:"yyyyMMddHHmmss",value:"yyyyMMddHHmmss"},{label:"yyyyMMdd",value:"yyyyMMdd"}],M=[{label:"初审模板",value:1},{label:"复审模板",value:2},{label:"病例申诉",value:3},{label:"外部疑点",value:4}],D=function(){var t=this,e=this,a=e.$createElement,i=e._self._c||a;return i("div",{staticClass:"fit"},[i("ta-modal",{attrs:{title:"疑点模板设置-添加模板列",visible:e.modalVisible,"destroy-on-close":!0,"mask-closable":!1,height:400,width:600},on:{cancel:e.onVisibleModal,ok:e.fnSubmitOk}},[i("ta-form",{attrs:{layout:"horizontal","form-layout":!0,col:e.col,"auto-form-create":function(e){t.form=e}}},[i("ta-form-item",{attrs:{label:"模板类型",span:24,"field-decorator-id":"importType","init-value":e.winData.dataType,require:{message:"模板类型不能为空"}}},[i("ta-select",{attrs:{options:e.templateTypeList}})],1),i("ta-form-item",{attrs:{label:"系统疑点列",span:24,"field-decorator-id":"columnValue",require:{message:"系统疑点列不能为空"}}},[i("ta-input")],1),i("ta-form-item",{attrs:{label:"系统疑点列说明",span:24,"field-decorator-id":"explain",require:{message:"系统疑点列说明不能为空"}}},[i("ta-input")],1),i("ta-form-item",{attrs:{label:"模板列名称",span:24,"field-decorator-id":"alias"}},[i("ta-input")],1),i("ta-form-item",{attrs:{label:"匹配字段",span:24,"field-decorator-id":"columnKey"}},[i("ta-input")],1),i("ta-form-item",{attrs:{label:"日期格式选择",span:24,"field-decorator-id":"columnTypeValue"}},[i("ta-select",{attrs:{options:e.dateList}})],1)],1)],1)],1)},F=[],z={name:"setSuspectTemplateWin",components:{},data:function(){return{col:{xs:1,sm:2,md:3,lg:4,xl:6,xxl:12},modalVisible:!1,templateTypeList:M,dateList:T,winData:{}}},methods:{onVisibleModal:function(t){this.modalVisible=!this.modalVisible,this.winData={},this.modalVisible&&(this.winData=t)},fnSubmitOk:function(){var t=this;this.form.validateFields((function(e,a){if(!e){var i=t.form.getFieldsValue();i.akb020=t.winData.akb020,s.saveColumnsInfo(i,(function(e){t.$message.success("保存成功"),t.onVisibleModal(),t.$emit("parentQuery")}))}}))}}},L=z,R=(0,h.Z)(L,D,F,!1,null,"5661f601",null),$=R.exports,Y={name:"setSuspectTemplateWin",components:{editTemplateColumnWin:$},props:{enabeledCenterAppeal:{type:Boolean,required:!0}},data:function(){return{modalVisible:!1,templateTypeList:M,templateFlag:1,tableData:[],dateList:T,winData:{}}},methods:{onVisibleModal:function(t){this.modalVisible=!this.modalVisible,this.templateFlag=1,this.tableData=[],this.winData={},this.modalVisible&&(this.winData=t,this.fnInitPageData())},changeRadioGroup:function(t){this.templateFlag=t.target.value,this.fnInitPageData()},activeMethod:function(t){var e=t.row,a=(t.rowIndex,t.column,t.columnIndex);return this.enabeledCenterAppeal?5!==a||5===a&&"2"===e.columnType:4!==a||4===a&&"2"===e.columnType},fnInitPageData:function(){var t=this,e=this.winData;e.dataType=this.templateFlag,s.getColumns(e,(function(e){t.tableData=e.data.resultMap}))},fnOpenAddColumnWin:function(){var t=this.winData;t.dataType=this.templateFlag,this.$refs.editTemplateColumnWin.onVisibleModal(t)},fnSubmitOk:function(){var t=this,e={ae11ListStr:JSON.stringify(this.tableData)};s.getColumnsInfo(e,(function(e){"200"===e.data.code&&t.$message.success("保存成功")}))}}},I=Y,A=(0,h.Z)(I,_,V,!1,null,"5dcfd6d9",null),N=A.exports;function O(){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px",fontWeight:"bold"}}}var B={columns:[{title:"住院号",dataIndex:"akc190",align:"center",width:100,overflowTooltip:!0,customHeaderCell:O},{title:"患者姓名",dataIndex:"aac003",align:"center",width:80,overflowTooltip:!0,customHeaderCell:O},{title:"项目编码",dataIndex:"ake001",align:"center",width:120,overflowTooltip:!0,customHeaderCell:O},{title:"项目名称",dataIndex:"ake006",align:"center",width:80,overflowTooltip:!0,customHeaderCell:O},{title:"违反规则",dataIndex:"aaa167",align:"center",width:200,overflowTooltip:!0,customHeaderCell:O},{title:"申诉理由",dataIndex:"aaz560",align:"center",width:120,overflowTooltip:!0,customHeaderCell:O},{title:"开单科室",dataIndex:"aae386",align:"center",width:160,sorter:function(t,e){return t.aae036>e.aae036},overflowTooltip:!0,customHeaderCell:O},{title:"开单医生",dataIndex:"aaz570",align:"center",width:90,overflowTooltip:!0,customHeaderCell:O},{title:"明细时间",dataIndex:"aae036",align:"center",width:80,overflowTooltip:!0,customHeaderCell:O},{title:"个人编码",dataIndex:"aac001",align:"center",width:150,overflowTooltip:!0,customHeaderCell:O},{title:"材料状态",dataIndex:"fileStas",align:"center",width:150,overflowTooltip:!0,customHeaderCell:O,scopedSlots:{customRender:"fileStas"}},{title:"操作",dataIndex:"ykz018",align:"center",width:100,overflowTooltip:!0,customHeaderCell:O,scopedSlots:{customRender:"operate"}}]},H=a(48534);a(36133);function q(t){return t?t.akb020&&t.aaz263?"personal":t.akb020&&t.aaz309&&t.aaz307?"group":t.akb020&&t.aaz307?"department":t.akb020?"hospital":"all":"all"}function P(t){return U.apply(this,arguments)}function U(){return U=(0,H.Z)(regeneratorRuntime.mark((function t(e){var a,i,l,o,n,s,r,c,d,u,m,f;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Base.submit(null,{url:"/miimCommonRead/queryDataPermission",showPageLoading:!1,data:{jobnumber:e},autoValid:!0});case 2:return a=t.sent,i=new Set,l=new Set,a.data.permission.forEach((function(t){var e=q(t);"hospital"===e&&i.add(t.akb020),"department"===e&&l.add(t.aaz307)})),o=a.data.permission.filter((function(t){return"department"===q(t)||!l.has(t.aaz307)})).filter((function(t){return"hospital"===q(t)||!i.has(t.akb020)})),n=new Set(o.map((function(t){return t.akb020})).filter((function(t){return null!=t}))),s=new Set(o.map((function(t){return t.aaz307})).filter((function(t){return null!=t}))),r=new Set(o.map((function(t){return t.aaz309})).filter((function(t){return null!=t}))),c=new Set(o.map((function(t){return t.aaz263})).filter((function(t){return null!=t}))),d=!1,u=!1,m=!1,f=!1,1===n.size&&(d=!0),1===s.size&&1===n.size&&(u=!0),1===s.size&&1===n.size&&1===r.size&&(m=!0),1===n.size&&0===s.size&&1===c.size&&(f=!0),t.abrupt("return",{akb020Set:n,aaz307Set:s,aaz263Set:c,aaz309Set:r,akb020Disable:d,aaz307Disable:u,aaz263Disable:f,aaz309Disable:m});case 20:case"end":return t.stop()}}),t)}))),U.apply(this,arguments)}function K(t,e){Base.submit(null,{url:"miimCommonRead/getAa01AAE500StartStop",data:t},{successCallback:function(t){return e(t)}})}function E(t,e){Base.submit(null,{url:"miimCommonRead/insertTableColumShow",data:t,autoValid:!1},{successCallback:function(t){return e("success")},failCallback:function(t){return e("fail")}})}var G={zbx01:{footer:function(){return"统筹金总额"}},zbx02:{footer:function(){return"基金支出总额"}},zbx03:{footer:function(){return"总费用"}},zbx04:{footer:function(){return"总住院日"}}},W={permissionCheck:P,getAa01AAE500StartStop:K,insertTableColumShow:E,props:G,moneyNumFormat:function(t){var e=t.cellValue;return e||"—"}},Z=B.columns,J={name:"newDoubtfulPointMg",components:{TaTitle:x,TagChange:g,SetSuspectTemplateWin:N},data:function(){var t=this,e=this.$createElement;return{lastPeriod:"",deptList:[],costTypeList:[],medTypeList:[],dateList:T,formShowAll:!0,fnGenerateButton:!1,tableColumns:Z,dataSource:[],importData:[],pageUrl:s.newgetPageUrl(),downloadVisible:!1,algorConfig:!1,processConfig:!1,columns:[],targetKeys:[],columnsChecked:[],disabled:!1,uploadVisible:!1,uploadVisible2:!1,haveFlag:!1,loading:!1,visible:!1,medicalInsuranceTypeDisabled:!0,getMedicalInsuranceTypeList:[],aae500check:[],fileUploadDisabled:!0,complainDisabled:!1,fileList:[],aaz263:"",aaz307:"",akb020:"",clientId:"",issueNo:"",popoverVisible:!1,importRecordView:!1,pointList:[],ae11List:[],pointChange:"",radioStyle:{height:"auto",marginBottom:"20px"},configOptions:[],configNum:"",configUnit:"KB",attachConfigTypeOne:"",attachConfigTypeTwo:"",nameRule1:"",nameRule2:"",nameRule3:"",enabeledCenterAppeal:!1,centerDownloadModalVisible:!1,rangeValue:this.Base.getMoment((new Date).toISOString().slice(0,7),"YYYY-MM"),hosList:[],mateRow:{},filterList:"",dynamicValidateForm:{domains:[]},dynamicValidateForm2:{domains:[]},operateMenu:[{name:"撤销导入",type:"confirm",disabled:function(t){return"0"===t.status},confirmTitle:e("span",{style:"white-space: pre-line;font-size: 14px"},["撤销导入提示！",e("p",["请确认是否撤销导入?",e("br"),"撤销后导入数据将会被清空，无法再处理。"])]),onOk:function(e,a){t.handDelete(e)}}]}},computed:{formBoxStyle:function(){return this.formShowAll?{height:"auto"}:{height:"50px"}},displayName:function(){var t=this,e=[this.nameRule1,this.nameRule2,this.nameRule3].map((function(e){var a;return null===(a=t.configOptions.find((function(t){return t.value===e})))||void 0===a?void 0:a.label})).filter(Boolean);return e.length>0?"".concat(e.join("_"),"_序号"):"请至少选择一个"}},watch:{},mounted:function(){var t=this,e=["16","2","17","18","3","4","7","5","8","6"];W.getAa01AAE500StartStop({aaz499s:JSON.stringify(e)},(function(e){var a=e.data.aae500List;a.includes(12)&&a.push(13),t.filterList=a})),this.$nextTick((function(){var e=t.$route.query;e&&e.aaz263&&(t.aaz263=e.aaz263,t.aaz307=e.aaz307),t.getAKb020ByJobNumber(),t.queryPointList(!1),t.fnGetDeptList(c()().format("YYYYMM")),t.formShowAll=!1,t.$refs.gridPager.loadData((function(e){t.dataSource=e.data.pageBean.list.map((function(t){return t.falseReasonShow=!1,t}))}))}))},methods:{domainChange:function(t){var e=this;this.dynamicValidateForm.domains.forEach((function(t){var a=e.ae11List.find((function(e){return e.value==t.columnValue}));t.oneValue=a.columnkey,"2"===(null===a||void 0===a?void 0:a.type)?(t.secondShow=!0,t.secondValue||(t.secondValue=a.typevalue)):(t.secondShow=!1,t.secondValue=null)}))},removeDomain:function(t){var e=this.dynamicValidateForm.domains.indexOf(t);-1!==e&&this.dynamicValidateForm.domains.splice(e,1)},addDomain:function(){this.dynamicValidateForm.domains.length>9?this.$message.error("最多添加10个字段"):this.dynamicValidateForm.domains.push({key:Date.now(),secondShow:!1,columnValue:void 0,oneValue:void 0,secondValue:void 0})},domainChange2:function(t){var e=this;this.dynamicValidateForm2.domains.forEach((function(t){var a=e.ae11List.find((function(e){return e.value==t.columnValue}));if(t.oneValue=a.columnkey,"2"===(null===a||void 0===a?void 0:a.type))if(t.secondShow=!0,t.secondValue||(t.secondValue=a.typevalue),e.dynamicValidateForm.domains.some((function(e){return e.columnValue==t.columnValue}))){var i=e.dynamicValidateForm.domains.find((function(e){return e.columnValue==t.columnValue}));t.secondOptions=e.expandDateFormats(i.secondValue)}else t.secondOptions=e.expandDateFormats(t.secondValue);else t.secondShow=!1,t.secondValue=null}))},expandDateFormats:function(t){var e=/[Hhms]/,a=[];if(e.test(t)){var i=t.replace(/\s.*$/,""),l=t.replace(/\s.*$/,"");a.some((function(t){return t.value===i}))||a.push({label:l,value:i}),a.push({label:t,value:t})}else a.push({label:t,value:t});return a},removeDomain2:function(t){var e=this.dynamicValidateForm2.domains.indexOf(t);-1!==e&&this.dynamicValidateForm2.domains.splice(e,1)},addDomain2:function(){this.dynamicValidateForm2.domains.length>9?this.$message.error("最多添加10个字段"):this.dynamicValidateForm2.domains.push({key:Date.now(),secondShow:!1,columnValue:void 0,secondValue:void 0,oneValue:void 0,secondOptions:[]})},handDelete:function(t){var e=this;this.Base.submit(null,{url:"/hiddscgPoint/deleteImportHistory",data:t},{successCallback:function(t){e.$message.success("撤销成功"),e.$nextTick((function(){e.$refs.importRecordGridPager.loadData()}))},failCallback:function(t){e.$message.error("撤销失败")}})},handleImportClose:function(){this.mateRow={},this.importRecordView=!1,this.queryTableData()},importRecord:function(t){var e=this;this.mateRow=t,this.importRecordView=!0,this.$nextTick((function(){e.$refs.importRecordGridPager.loadData()}))},editClosed:function(t){var e=this,a=t.row;t.rowIndex,t.$rowIndex,t.column,t.columnIndex,t.$columnIndex;this.$confirm({content:"确认将整期数据的申诉截止日期都修改为"+c()(a.akc195).format("YYYY-MM-DD")+"吗？",okText:"确认",cancelText:"取消",onOk:function(){e.Base.submit(null,{url:"/hiddscgPoint/updateAkc195",data:a},{successCallback:function(t){e.$message.success("保存成功"),e.queryTableData()},failCallback:function(t){e.$message.error("保存失败")}})},onCancel:function(){e.queryTableData()}})},complainChange:function(t){this.complainDisabled=0!=t.target.value},onAAE500Change:function(t){this.aae500check=t},onChange:function(t){this.pointChange=t.target.value},queryPointList:function(t){var e=this;Base.submit(null,{url:"/hiddscgPoint/queryCurrentAkb020Option",data:{akb020:this.akb020}}).then((function(a){var i;if(e.pointList=a.data.data,e.ae11List=a.data.ae11List,null!==(i=a.data)&&void 0!==i&&i.dynamicData&&Object.values(a.data.dynamicData).every((function(t){return t}))&&(e.aae500check=a.data.dynamicData.aae500check.split(","),e.dynamicValidateForm.domains=a.data.dynamicData.autoField,e.dynamicValidateForm2.domains=a.data.dynamicData.handField,e.domainChange2()),e.pointList.length>0){var l=e.pointList.find((function(t){return"1"===t.chooseFlag}));if(l&&l.algorithmKey)e.pointChange=l.algorithmKey;else{var o=e.pointList.find((function(t){return t.defaultFlag&&"1"===t.defaultFlag}));e.pointChange=o.algorithmKey||"DefaultAlgorithm"}}t&&(e.algorConfig=!0)}))},queryProcessConfig:function(){var t=this;Base.submit(null,{url:"miimCommonRead/queryProcessConfig",data:{akb020:this.akb020}}).then((function(e){var a=e.data;t.processConfig=!0,t.$nextTick((function(){t.configOptions=a.ae11Option,t.processform.setFieldsValue(a.data),t.complainDisabled="0"!=a.data.appealMaterial,t.configNum=a.data.configNum,t.configUnit=a.data.configUnit,t.attachConfigTypeOne=a.data.attachConfigTypeOne,t.attachConfigTypeTwo=a.data.attachConfigTypeTwo,t.nameRule1=a.data.nameRule1,t.nameRule2=a.data.nameRule2,t.nameRule3=a.data.nameRule3}))}))},handleSave:function(){var t=this,e={};if("DynamicAlgorithm"===this.pointChange){if(0===this.aae500check.length)return void this.$message.error("至少选择一个匹配数据源");var a=this.validateData(this.dynamicValidateForm.domains),i=this.validateData(this.dynamicValidateForm2.domains);if(a.length)return void this.$message.error("疑点匹配&备案理由算法校验失败：\n"+a.join("\n"));if(i.length)return void this.$message.error("手动匹配校验失败：\n"+i.join("\n"));e={aae500check:this.aae500check.join(","),autoField:this.dynamicValidateForm.domains,handField:this.dynamicValidateForm2.domains}}this.Base.submit(null,{url:"hiddscgPoint/updateCurrentAkb020Option",data:{akb020:this.akb020,algorithmKey:this.pointChange,mandatoryField:JSON.stringify(e)}},{successCallback:function(e){t.algorConfig=!1,t.$message.success(e.data.msg)},failCallback:function(e){t.$message.error("保存失败")}})},validateData:function(t){var e=[],a=new Set;return 0===t.length?(e.push("请添加至少一条数据"),e):(t.forEach((function(t,i){var l=t.columnValue;null==l||""===l?e.push("第 ".concat(i+1," 项 匹配字段 为空")):a.has(l)?e.push("第 ".concat(i+1,' 项 匹配字段 "').concat(l,'" 重复')):a.add(l)})),e)},handleClose2:function(){this.algorConfig=!1},handleClose3:function(){this.processConfig=!1},handleSave2:function(){var t=this;this.processform.validateFieldsAndScroll((function(e,a){if(t.configNum)if(t.nameRule1||t.nameRule2||t.nameRule3){var i=/^([\u4e00-\u9fa5a-zA-Z0-9\-]+,)*[\u4e00-\u9fa5a-zA-Z0-9\-]+$/;if(!t.attachConfigTypeOne||i.test(t.attachConfigTypeOne))if(!t.attachConfigTypeTwo||i.test(t.attachConfigTypeTwo)){if(t.nameRule1===t.nameRule2&&t.nameRule1||t.nameRule1===t.nameRule3&&t.nameRule1||t.nameRule2===t.nameRule3&&t.nameRule2)t.$message.error("申诉材料命名规则选择重复！");else if(!e){var l=t.processform.getFieldsValue();l.configNum=t.configNum,l.configUnit=t.configUnit,l.attachConfigTypeOne=t.attachConfigTypeOne,l.attachConfigTypeTwo=t.attachConfigTypeTwo,l.nameRule1=t.nameRule1,l.nameRule2=t.nameRule2,l.nameRule3=t.nameRule3,t.Base.submit(null,{url:"hiddscgPoint/updateProcessConfig",data:{akb020:t.akb020,algorithmKey:t.pointChange,processConfig:JSON.stringify(l)}},{successCallback:function(e){t.algorConfig=!1,t.enabeledCenterAppeal=t.processform.getFieldValue("enabeledCenterAppeal"),t.$message.success(e.data.msg)},failCallback:function(e){t.$message.error("保存失败")}})}}else t.$message.error("附件类型格式有误！（病例申诉附件类型）");else t.$message.error("附件类型格式有误！（报销扣款附件类型）")}else t.$message.error("申诉材料命名规则必选一个！");else t.$message.error("文件大小限制必填！")}))},confirmImport:function(t){var e=this,a=this.form1.getFieldsValue();a.importSion=t,a.file=this.fileList[0],a.akb020=this.akb020,s.uploadDatas(a,(function(t){e.handleClose("1"),e.fnGetDeptList(e.lastPeriod),e.$message.success("导入数据成功"),["1","2"].includes(a.dataType)&&e.handleCreate(a)}))},getAKb020ByJobNumber:function(){var t=this;this.Base.submit(null,{url:"/appeal/common/getAKb020ByJobNumber",data:{loginId:this.aaz263}},{successCallback:function(e){t.akb020=e.data.akb020,"01"!=e.data.processConfig&&"11"!=e.data.processConfig||t.$refs.xTable.hideColumn(t.$refs.xTable.getColumnByField("fileUnCreateNum")),t.akb020&&t.Base.submit(null,{url:"miimCommonRead/queryProcessConfig",data:{akb020:t.akb020}},{successCallback:function(e){t.enabeledCenterAppeal=e.data.data.enabeledCenterAppeal}})},failCallback:function(e){t.$message.error(e.errors[0])}})},showConfirm:function(t){this.$confirm({title:"请确认"+t.ake006+"项目实际是否被拒付？",okText:"是",okType:"danger",cancelText:"否",class:"test",onOk:function(){},onCancel:function(){}})},analysisMedicalInsuranceRefusal:function(){this.uploadVisible2=!0},handleCreate:function(t){var e=this,a={};a.issueNum=t.issueNo+"-"+t.issueNo,a.hiFeesetlType=t.medinsuType,a.dataType=t.dataType,a.medType=t.medType,this.Base.submit(null,{url:"refusaldata/createAnalysisReport",data:a},{successCallback:function(t){e.$message.success("执行拒付任务成功"),e.uploadVisible2=!1},failCallback:function(t){e.$message.error("执行拒付任务失败")}})},moment:c(),setPopupContainer:function(t){return t.parentNode},tooltipMethod:function(t){t.items;var e=t.row,a=(t.rowIndex,t.$rowIndex,t.column,t.columnIndex);t.$columnIndex,t.type,t.cell,t.$event;if(11===a&&"0"===e.fileStas)return"失败原因："+e.fileReason},filterMethod:function(t){var e=t.option,a=t.row;return a.handStas===e.data},fnGetDeptList:function(t){var e=this;s.getDeptList({issueNo:t},(function(a){e.deptList=a.data.departmentList,e.costTypeList=a.data.costTypeList,e.medTypeList=a.data.medTypeList,e.lastPeriod=t}))},formShowAllChange:function(){this.formShowAll=!this.formShowAll},pageParams:function(){var t=this.form.getFieldsValue();return t},queryTableData:function(){var t=this;this.form.validateFields((function(e){e||(t.$refs.gridPager.loadData((function(e){t.dataSource=e.data.pageBean.list.map((function(t){return t.falseReasonShow=!1,t}))})),t.formShowAll=!1)}))},showFileReason:function(t,e){t.falseReasonShow=!0,this.$refs.xTable.updateStatus({row:t,column:e})},hideFileReason:function(t,e){t.falseReasonShow=!1,this.$refs.xTable.updateStatus({row:t,column:e})},fnGenerateComplaintMaterials:function(){var t=this;if(0!==this.dataSource.length){var e={};e.issueNo=this.dataSource[0].aae043,e.pointType=this.form.getFieldValue("pointType"),s.handleBatchGenerate(e,(function(e){"200"==e.data.code?t.$message.success(e.data.msg):"201"==e.data.code&&t.$message.info(e.data.msg),t.queryTableData()}))}else this.$message.warning("请先进行查询确定本期是否有数据")},reGenerate:function(t){this.Base.openTabMenu({id:t.aae043+t.aae141+t.aka130+t.dataType,name:"疑点明细【"+t.aae043+"期】",url:"doubtfulPointMg.html#/doubtfulPointMg?issueNo=".concat(t.aae043,"&medinsuType=").concat(t.aae141,"&dataType=").concat(t.dataType),refresh:!1})},resetForm:function(){this.form.resetFields(),this.fnGenerateButton=!1},handleOpen:function(t){var e=this;"0"===t?(this.popoverVisible=!1,this.downloadVisible=!0,s.getColumns({akb020:this.akb020,aaz263:this.aaz263,dataType:this.templateFlag},(function(t){e.targetKeys=t.data.resultMap.filter((function(t){return"1"===t.required||"1"===t.checkState})).map((function(t){return t.columnKey})),e.columns=t.data.resultMap.map((function(t){return t.disabled="1"===t.required,t}))}))):"2"===t?(this.popoverVisible=!1,this.queryPointList(!0)):"3"===t?(this.popoverVisible=!1,this.queryProcessConfig()):(this.uploadVisible=!0,this.$nextTick((function(){e.form1.setFieldsValue({issueNo:c()().format("YYYYMM")})})))},openVisibleModal:function(){var t=this;this.centerDownloadModalVisible=!0,s.queryAkb020List(null,(function(e){e.data.resultData&&(t.hosList=e.data.resultData,t.hosList.unshift({label:"全部",value:"all"}))}))},downloadCenterData:function(){var t=this,e=this.baseInfoForm.getFieldsValue(),a=this.baseInfoForm.getFieldValue("allDate");e.aae043=a.format("YYYYMM"),e.startDate=a.format("YYYY-MM")+"-01 00:00:00";var i=a.endOf("month").date();e.endDate=a.format("YYYY-MM")+"-".concat(i," 23:59:59"),this.baseInfoForm.validateFields((function(a){a||(e.akb020List&&e.akb020List.includes("all")&&(e.akb020List=[]),Base.showMask({show:!0,text:"数据下载中"}),s.downloadCenterData(e,(function(e){Base.showMask({show:!1}),"200"===e.data.code&&t.$message.success("导入数据成功")})))}))},closeVisibleModal:function(){this.centerDownloadModalVisible=!this.centerDownloadModalVisible,this.baseInfoForm.resetFields()},handleSelectHosp:function(t){"all"===t&&this.baseInfoForm.setFieldsValue({akb020List:["all"]})},selectHospChange:function(t){t.includes("all")?this.hosList.forEach((function(t,e){"all"!==t.value&&(t.disabled=!0)})):this.hosList.forEach((function(t,e){t.disabled=!1}))},testCenterInterface:function(){var t=this;s.testCenterInterface(null,(function(e){"200"===e.data.code&&t.$message.success("连接成功")}))},handleClose:function(t){"0"===t?this.downloadVisible=!1:(this.visible=!1,this.uploadVisible=!1,this.fileList=[],this.medicalInsuranceTypeDisabled=!0,this.fileUploadDisabled=!0,this.form1.resetFields()),this.queryTableData()},fnOpenSuspectTemplateWin:function(){var t={akb020:this.akb020,aaz263:this.aaz263};this.$refs.setSuspectTemplateWin.onVisibleModal(t),this.popoverVisible=!1},handleChange:function(t,e,a){this.targetKeys=t},handleSelectChange:function(t,e){this.columnsChecked=[].concat((0,o.Z)(t),(0,o.Z)(e))},handleDownload:function(){var t=this,e=this.columns.map((function(t){return t}));s.getColumnsInfo({ae11ListStr:JSON.stringify(e)},(function(e){(e.data.code="200")&&t.$message.success("保存成功")}))},uploadMonthchange:function(t,e){var a=this,i=this.form1.getFieldsValue();e&&(i.ape800=t),i.ape800&&s.getUploadFilter(i,(function(t){a.medicalInsuranceTypeDisabled=!1,a.getMedicalInsuranceTypeList=t.data.insuList}))},medinsuTypeChange:function(t){this.fileUploadDisabled=!1},beforeUpload:function(t){if(t.size/1024>51200)return this.$message.warning("请选择不超过50M的文件"),!1;var e=t.name.split("."),a=e[e.length-1];return-1===["xls","xlsx","csv"].indexOf(a)?(this.$message.warning("只能上传excel文件"),!1):(this.fileList=[t],this.form1.setFieldsValue({fileName:t.name}),!1)},handleUpload:function(){var t=this,e=this.form1.getFieldsValue();this.issueNo=e.issueNo,this.form1.validateFields((function(a){a||s.getUploadFilter(e,(function(e){e.data.haveFlag?t.visible=!0:t.confirmImport("")}))}))}}},j=J,Q=(0,h.Z)(j,i,l,!1,null,"6a499eab",null),X=Q.exports}}]);
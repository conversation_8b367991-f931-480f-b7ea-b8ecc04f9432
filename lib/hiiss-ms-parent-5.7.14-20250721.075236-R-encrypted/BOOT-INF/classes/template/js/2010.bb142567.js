"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[2010],{62010:function(t,n,c){c.r(n),c.d(n,{default:function(){return s}});var i=function(){var t=this,n=t.$createElement,c=t._self._c||n;return c("div",{staticStyle:{margin:"10px 10px"}},[c("h3",[t._v("页面1的子路由1")]),c("p",{staticStyle:{margin:"20px"}},[t._v(" 保持页面1的子路由1，点击其他tab页面之后再回来，页面1的子路由1的数据将保持 ")]),c("div",{staticStyle:{"margin-left":"20px"}},[c("span",[t._v("点击右侧按钮更改数据：")]),c("ta-button",{staticStyle:{"margin-left":"20px"},on:{click:function(n){t.count++}}},[t._v(" 页面1的子路由1的count: "+t._s(t.count)+" ")])],1)])},a=[],u={name:"child1-1",data:function(){return{count:0}},activated:function(){}},e=u,l=c(1001),r=(0,l.Z)(e,i,a,!1,null,null,null),s=r.exports}}]);
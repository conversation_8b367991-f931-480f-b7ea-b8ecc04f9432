(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[1592],{88412:function(t,e,a){"use strict";var l=a(26263),i=a(36766),r=a(1001),n=(0,r.Z)(i.Z,l.s,l.x,!1,null,"5e7ef0ae",null);e["Z"]=n.exports},12206:function(t,e,a){"use strict";a.r(e),a.d(e,{default:function(){return b}});var l=function(){var t=this,e=this,a=e.$createElement,l=e._self._c||a;return l("div",{staticClass:"fit"},[l("ta-border-layout",{attrs:{layout:{header:"110px",footer:"0px"},showPadding:!0,"footer-cfg":{showBorder:!1}}},[l("div",{attrs:{slot:"header"},slot:"header"},[l("ta-title",{attrs:{title:"查询条件"}}),l("ta-form",{staticStyle:{"padding-top":"5px"},attrs:{autoFormCreate:function(e){t.baseInfoForm=e},layout:"horizontal",formLayout:!0}},[l("ta-form-item",{attrs:{fieldDecoratorId:"allDate",labelCol:{span:7},wrapperCol:{span:16},span:5,fieldDecoratorOptions:{rules:[{required:!0,message:"必须选取时间"}]},required:!0,"init-value":e.rangeValue}},[l("span",{attrs:{slot:"label"},slot:"label"},[e._v("调价时间")]),l("ta-range-picker",{attrs:{"allow-one":!0}},[l("ta-icon",{attrs:{slot:"suffixIcon",type:""},slot:"suffixIcon"})],1)],1),l("ta-form-item",{staticStyle:{"margin-left":"1%"},attrs:{fieldDecoratorId:"itemCode",span:4,labelCol:{span:7},wrapperCol:{span:15}}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("项目编码")]),l("ta-input",{attrs:{placeholder:"请输入项目编码","allow-clear":""}})],1),l("ta-form-item",{staticStyle:{"margin-left":"1%"},attrs:{fieldDecoratorId:"itemName",span:5,labelCol:{span:7},wrapperCol:{span:15}}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("项目名称")]),l("ta-input",{attrs:{placeholder:"请输入项目名称","allow-clear":""}})],1),l("ta-form-item",{staticStyle:{"margin-left":"1%"},attrs:{fieldDecoratorId:"medicalCategory",span:5,labelCol:{span:7},wrapperCol:{span:15}}},[l("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[e._v("医保类别")]),l("ta-select",{attrs:{allowClear:!0,placeholder:"医保类别筛选"},on:{change:e.handleChange}},[l("ta-select-option",{attrs:{value:"甲类"}},[e._v("甲类")]),l("ta-select-option",{attrs:{value:"乙类"}},[e._v("乙类")]),l("ta-select-option",{attrs:{value:"丙类"}},[e._v("丙类")])],1)],1),l("ta-form-item",{attrs:{span:4}},[l("ta-button",{staticStyle:{float:"right"},attrs:{icon:"search",type:"primary"},on:{click:e.fnQuery}},[e._v("查询")]),l("ta-button",{staticStyle:{float:"right"},attrs:{type:"default",icon:"redo"},on:{click:e.fnReset}},[e._v("重置")]),l("ta-button",{staticStyle:{float:"right"},attrs:{type:"primary",icon:"upload"},on:{click:e.newlyadded}},[e._v("导入")])],1)],1)],1),l("div",{staticClass:"fit",staticStyle:{height:"90%"}},[l("ta-title",{staticStyle:{width:"50%"},attrs:{title:"查询结果"}}),l("ta-button",{staticStyle:{"margin-top":"-35px",float:"right"},attrs:{type:"primary"},on:{click:e.ChangeTpStatus}},[e._v("查看统计结果")]),l("ta-big-table",{ref:"Table",attrs:{height:"auto","auto-resize":"",resizable:"","highlight-hover-row":"",border:"",size:"mini",data:e.userList}},[l("ta-big-table-column",{attrs:{type:"seq",align:"center",title:"序号",width:"50"}}),l("ta-big-table-column",{attrs:{field:"remark",title:"备注","min-width":"120",align:"center"}}),l("ta-big-table-column",{attrs:{field:"code",title:"医保编码","min-width":"120",align:"center"}}),l("ta-big-table-column",{attrs:{field:"itemCode",title:"项目编码","min-width":"120",align:"center"}}),l("ta-big-table-column",{attrs:{field:"itemName",title:"项目名称","min-width":"120",align:"center"}}),l("ta-big-table-column",{attrs:{field:"itemContent",title:"项目内涵","min-width":"200",align:"center"}}),l("ta-big-table-column",{attrs:{field:"itemExcept",title:"除外内容","min-width":"120",align:"center"}}),l("ta-big-table-column",{attrs:{field:"unit",title:"计价单位","min-width":"120",align:"center"}}),l("ta-big-table-column",{attrs:{field:"description",title:"说明","min-width":"80",align:"center"}}),l("ta-big-table-column",{attrs:{field:"originalPrice",title:"调整前价格(一类价)","min-width":"180",align:"center",formatter:"formatFixedNumber"}}),l("ta-big-table-column",{attrs:{field:"financeCategory",title:"财务分类","min-width":"120",align:"center"}}),l("ta-big-table-column",{attrs:{field:"medicalCategory",title:"医保类别","min-width":"120",align:"center"}}),l("ta-big-table-column",{attrs:{field:"proposedPrice",title:"拟调整价格(一类价)","min-width":"180",align:"center",formatter:"formatFixedNumber"}}),l("ta-big-table-column",{attrs:{field:"actualAdjustment",title:"实际调价幅度(以一类价为参照)","min-width":"230",align:"center"}}),l("ta-big-table-column",{attrs:{field:"adjustTime",title:"调价时间","min-width":"150",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[l("span",[e._v(e._s(a.adjustTime.split(" ")[0]))])]}}])}),l("template",{slot:"bottomBar"},[l("ta-button",{staticStyle:{float:"right","margin-top":"20px"},attrs:{icon:"download",type:"primary"},on:{click:e.exportTable}},[e._v("导出")]),l("ta-pagination",{ref:"gridPager",staticStyle:{float:"right","margin-top":"20px","margin-right":"10px"},attrs:{defaultPageSize:30,pageSizeOptions:["30","50","100","200","500"],"data-source":e.userList,params:e.infoPageParams,url:"priceAdjust/queryPriceAdjustInfoPage"},on:{"update:dataSource":function(t){e.userList=t},"update:data-source":function(t){e.userList=t}}})],1)],2)],1),l("ta-modal",{attrs:{title:"导入",visible:e.uploadVisible,height:210,width:400},on:{cancel:e.handleClose}},[l("ta-form",{attrs:{"auto-form-create":function(e){return t.form1=e},layout:"horizontal","form-layout":!0,"label-width":"80px",col:{xs:1,sm:1,md:1,lg:1,xl:1,xxl:1}}},[l("ta-form-item",{attrs:{label:"调价时间","field-decorator-id":"adjustTime","init-value":e.moment().format("YYYY-MM-DD"),require:!0}},[l("ta-date-picker",{staticStyle:{width:"100%"},attrs:{format:"YYYY-MM-DD","value-format":"YYYY-MM-DD","allow-clear":!1}})],1),l("ta-form-item",{staticClass:"uploadFormItem",attrs:{label:"选择文件","field-decorator-id":"fileName",require:!0}},[l("ta-input",{staticClass:"uploadInput",attrs:{placeholder:"请选择文件上传",disabled:!0}}),l("ta-upload",{staticClass:"uploadBtn",attrs:{"file-list":e.fileList,"before-upload":e.beforeUpload,"show-upload-list":!1}},[l("ta-button",{attrs:{type:"primary"}},[e._v(" 浏览 ")])],1)],1),l("ta-form-item",[l("ta-button",{attrs:{type:"primary"},on:{click:e.handleDownload}},[e._v(" 下载模板 ")])],1)],1),l("div",{attrs:{slot:"footer"},slot:"footer"},[l("ta-button",{on:{click:function(t){return e.handleClose("1")}}},[e._v(" 取消 ")]),l("ta-button",{staticStyle:{"margin-right":"calc(50% - 70px)"},attrs:{type:"primary"},on:{click:e.handleUpload}},[e._v(" 导入 ")])],1)],1)],1)],1)},i=[],r=a(66347),n=a(95082),s=a(88412),o=a(36797),c=a.n(o),u=(a(55067),a(22722)),f=a(55115);f.w3.prototype.Base=Object.assign(f.w3.prototype.Base,(0,n.Z)({},u.Z));var d=[],m={name:"priceAdjustment",components:{TaTitle:s.Z},data:function(){return{msgScenarioList:[],userList:[],rangeValue:[this.Base.getMoment((new Date).toISOString().slice(0,8)+"-01","YYYY-MM-DD"),this.Base.getMoment((new Date).toISOString().slice(0,10),"YYYY-MM-DD")],userColumns:d,fileList:[],uploadVisible:!1,selectSend:!0,flag:"",msgScenario:"",selectionStart:0,selectionEnd:0,selectSendValue:"1",bfrecord:{},result:"",configflag:""}},mounted:function(){this.fnQuery()},methods:{moment:c(),handleDownload:function(){var t,e=[],a=this.$refs.Table.getColumns(),l=(0,r.Z)(a);try{for(l.s();!(t=l.n()).done;){var i=t.value;if("调价时间"!==i.title&&"医保编码"!==i.title){if(-1!==i.title.indexOf("(")){var n=0,s=i.title.indexOf("("),o=i.title.substring(n,s);i.title=o}e.push({header:i.title,key:i.property,width:20})}}}catch(u){l.e(u)}finally{l.f()}var c={fileName:"物价调价维护导入模板表",sheets:[{name:"worksheet1",column:{complex:!1,columns:e},rows:[]}]};this.Base.generateExcel(c)},handleUpload:function(){var t=this,e=this.form1.getFieldsValue();this.form1.validateFields((function(a){a||(e.file=t.fileList[0],t.Base.submit(null,{url:"/priceAdjust/importExcel",data:e,autoQs:!1,isFormData:!0},{successCallback:function(e){t.form1.resetFields(),t.uploadVisible=!1,t.fnQuery(),t.$message.success("导入数据成功")},failCallback:function(e){t.$message.error("导入数据失败")}}))}))},beforeUpload:function(t){if(t.size/1024>51200)return this.$message.warning("请选择不超过50M的文件"),!1;var e=t.name.split("."),a=e[e.length-1];return-1===["xls","xlsx","csv"].indexOf(a)?(this.$message.warning("只能上传excel文件"),!1):(this.fileList=[t],this.form1.setFieldsValue({fileName:t.name}),!1)},handleClose:function(){this.uploadVisible=!1,this.fileList=[],this.medicalInsuranceTypeDisabled=!0,this.form1.resetFields()},handleChange:function(t){this.selectSendValue=t},ChangeTpStatus:function(){this.Base.openTabMenu({id:1,name:"调价项目费用统计",url:"medicalServiceTypeCount.html#/costStatistics",refresh:!1})},infoPageParams:function(){var t=this.baseInfoForm.getFieldsValue();if(null!=t.allDate[0]&&""!=t.allDate[0]){if(null!=t.allDate[1]&&""!=t.allDate[1])return t.startDate=t.allDate[0].format("YYYY-MM-DD"),t.endDate=t.allDate[1].format("YYYY-MM-DD"),t;this.$message.error("请选择完整日期")}else this.$message.error("请选择完整日期")},fnQuery:function(){var t=this;this.$nextTick((function(){t.$refs.gridPager.loadData((function(t){}))}))},newlyadded:function(){this.uploadVisible=!0},fnReset:function(){this.baseInfoForm.resetFields(),this.fnQuery()},exportTable:function(){var t=this,e=this.baseInfoForm.getFieldsValue();if(null!=e.allDate[0]&&""!=e.allDate[0])if(null!=e.allDate[1]&&""!=e.allDate[1]){e.startDate=e.allDate[0].format("YYYY-MM-DD"),e.endDate=e.allDate[1].format("YYYY-MM-DD");var a,l=[],i=this.$refs.Table.getColumns(),n=(0,r.Z)(i);try{for(n.s();!(a=n.n()).done;){var s=a.value;"序号"!==s.title&&l.push({header:s.title,key:s.property,width:20})}}catch(o){n.e(o)}finally{n.f()}this.Base.submit(null,{url:"priceAdjust/exportExcel",data:e,autoValid:!1},{successCallback:function(e){var a={fileName:"物价调价维护结果表",sheets:[{name:"worksheet1",column:{complex:!1,columns:l},rows:e.data.data}]};t.Base.generateExcel(a)},failCallback:function(e){t.$message.error("物价调价维护数据加载失败")}})}else this.$message.error("请选择完整日期");else this.$message.error("请选择完整日期")},fnCustomHeaderCell:function(t){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",lineHeight:"22px",fontWeight:"bold"}}},filterOption:function(t,e){return e.componentOptions.children[0].text.indexOf(t)>=0}}},p=m,h=a(1001),g=(0,h.Z)(p,l,i,!1,null,"1b53688c",null),b=g.exports},36766:function(t,e,a){"use strict";var l=a(66586);e["Z"]=l.Z},26263:function(t,e,a){"use strict";a.d(e,{s:function(){return l},x:function(){return i}});var l=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("h3",{staticStyle:{"font-weight":"normal"}},[a("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),a("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},i=[]},66586:function(t,e){"use strict";var a={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};e["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:a}}}},55067:function(t,e,a){"use strict";a(95082),a(95278)},55382:function(){},61219:function(){}}]);
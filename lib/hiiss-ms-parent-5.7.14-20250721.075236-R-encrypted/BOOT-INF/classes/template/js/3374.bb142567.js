"use strict";(self["webpackChunkta404_ui_cli"]=self["webpackChunkta404_ui_cli"]||[]).push([[3374],{88412:function(t,a,e){var o=e(26263),l=e(36766),n=e(1001),i=(0,n.Z)(l.Z,o.s,o.x,!1,null,"5e7ef0ae",null);a["Z"]=i.exports},63374:function(t,a,e){e.r(a),e.d(a,{default:function(){return h}});var o=function(){var t=this,a=this,e=a.$createElement,o=a._self._c||e;return o("div",{staticClass:"fit"},[o("ta-border-layout",{attrs:{layout:{header:"120px"}}},[o("div",{attrs:{slot:"header"},slot:"header"},[o("ta-title",{attrs:{title:"查询条件"}}),o("ta-form",{attrs:{autoFormCreate:function(a){t.form=a},formLayout:"",enctype:"multipart/form-data"}},[o("ta-form-item",{attrs:{fieldDecoratorId:"aaa027",labelCol:{span:8},wrapperCol:{span:16},span:6,disabled:a.authObj&&a.authObj.isHospType(),initValue:a.authObj&&a.authObj.isHospType()&&a.treeData[0]?a.treeData[0].value:""}},[o("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[a._v("统筹区")]),o("ta-tree-select",{attrs:{dropdownStyle:{maxHeight:"400px",overflow:"auto"},treeData:a.treeData,treeDataSimpleMode:a.treeData,"allow-clear":!0},on:{change:a.queryHospitalName}})],1),o("ta-form-item",{attrs:{fieldDecoratorId:"akb020",labelCol:{span:8},wrapperCol:{span:16},span:6,disabled:a.authObj&&a.authObj.isHospType(),initValue:a.authObj&&a.authObj.isHospType()?a.authObj.getOrgCode():""}},[o("span",{staticStyle:{color:"#666666"},attrs:{slot:"label"},slot:"label"},[a._v("医院选择")]),o("ta-select",{attrs:{"show-search":!0,placeholder:"请选择",allowClear:!0,options:a.hospitalList},on:{change:a.hospChange}})],1),o("ta-form-item",{attrs:{span:12,labelCol:{span:0},wrapperCol:{span:24}}},[o("ta-button",{attrs:{type:"primary",icon:"download"},on:{click:a.sampleDownload}},[a._v("医护人员信息导入模板下载")]),o("ta-upload",{attrs:{showUploadList:!1,accept:".xls,.xlsx",customRequest:a.fileRequest,action:a.handlUpload}},[o("ta-button",{attrs:{type:"success"}},[o("ta-icon",{attrs:{type:"upload"}}),a._v(" 批量导入医院信息 ")],1)],1)],1)],1)],1),o("div",{staticClass:"fit"},[o("ta-title",{attrs:{title:"医院医生信息"}}),o("ta-row",[o("ta-button",{attrs:{disabled:null===a.akb020||void 0===a.akb020||""==a.akb020,icon:"plus",type:"primary"},on:{click:a.showModal}},[o("ta-icon",{attrs:{type:"add"}}),a._v(" 新增 ")],1)],1),o("ta-modal",{attrs:{title:a.modalTitle,height:"300px",draggable:!0,zoomable:!0,"destroy-on-close":!0},on:{ok:a.handleOk},model:{value:a.visible,callback:function(t){a.visible=t},expression:"visible"}},[o("ta-form",{attrs:{autoFormCreate:function(a){t.editForm=a}}},[o("ta-form-item",{attrs:{label:"医生名称",fieldDecoratorId:"aac003",require:{message:"请输入名称"}}},[o("ta-input",{attrs:{placeholder:"医生名称"}})],1),o("ta-form-item",{attrs:{require:{message:"请输入编号!"},label:"医生编号",fieldDecoratorId:"aaz263",disabled:"编辑医护人员信息"===a.modalTitle}},[o("ta-input",{attrs:{placeholder:"医生编号"}})],1),o("ta-form-item",{attrs:{label:"科室",fieldDecoratorId:"aaz307"}},[o("ta-select",{attrs:{options:a.departmentList,placeholder:"科室编码"}})],1),o("ta-form-item",{attrs:{label:"是否有效",fieldDecoratorId:"aae100",initValue:a.defaultValue}},[o("ta-select",{attrs:{options:[{value:"1",label:"有效"},{value:"0",label:"无效"}],placeholder:"请选择"}})],1)],1)],1),o("div",{staticStyle:{height:"calc(100% - 110px)"}},[o("ta-table",{ref:"docTable",attrs:{bordered:!0,size:"small",columns:a.columns,dataSource:a.tableData,scroll:{y:"100%"},customRow:a.fnCustomRow},on:{"update:columns":function(t){a.columns=t}},scopedSlots:a._u([{key:"action",fn:function(t,e,l){return[o("span",[o("a",{on:{click:function(t){return a.handleShowEdit(e)}}},[o("ta-icon",{attrs:{type:"edit"}}),a._v("编辑")],1),o("ta-popconfirm",{attrs:{title:"是否删除?"},on:{confirm:function(){return a.handleDelete(e)}}},[o("a",{style:{marginLeft:"10px"}},[o("ta-icon",{attrs:{type:"delete"}}),a._v("删除")],1)])],1)]}}])})],1),o("ta-pagination",{ref:"departmentPager",staticStyle:{float:"right","margin-top":"8px","margin-right":"10px"},attrs:{showSizeChanger:"",showQuickJumper:"",dataSource:a.tableData,defaultPageSize:10,pageSizeOptions:["10","20","40"],params:a.pageParams,url:a.docConfig.doctorInfoUrl},on:{"update:dataSource":function(t){a.tableData=t},"update:data-source":function(t){a.tableData=t}}})],1)])],1)},l=[],n=e(48534),i=e(95082),r=(e(36133),e(32564),e(88412)),s=e(75660),u={name:"DoctorInfo",components:{TaTitle:r.Z},data:function(){return{authObj:null,treeData:[],defaultValue:"1",docConfig:{uploadUrl:"/hospDocInfo/upload",departmentUrl:"/Department/queryDepartmentList",doctorInfoUrl:"/hospDocInfo/queryByPage",hospitalListUrl:"/hospDocInfo/initData",doctorUpdateUrl:"/hospDocInfo/updateBaseInfo",doctorDeleteUrl:"/hospDocInfo/deleteDocInfo",doctorInsertUrl:"/hospDocInfo/insertBaseInfo"},modalTitle:"",departmentInfo:{},departmentList:[],visible:!1,akb020:null,hospitalList:[],columns:[{title:"医生名称",align:"center",dataIndex:"aac003",width:"200",customHeaderCell:this.fnCustomHeaderCell,overflowTooltip:!0},{title:"医生编码",align:"center",dataIndex:"aaz263",width:"200",customHeaderCell:this.fnCustomHeaderCell,overflowTooltip:!0},{title:"医院名称",align:"center",dataIndex:"akb020",width:"200",customHeaderCell:this.fnCustomHeaderCell,overflowTooltip:!0,customRender:this.akb020Format},{title:"科室",align:"center",dataIndex:"aaz307",width:"200",customHeaderCell:this.fnCustomHeaderCell,overflowTooltip:!0,customRender:this.aaz307Format},{title:"有效标识",align:"center",dataIndex:"aae100",width:"200",customHeaderCell:this.fnCustomHeaderCell,overflowTooltip:!0,collectionType:"EFFECTIVE"},{title:"统筹区",align:"center",dataIndex:"aaa027",width:"200",customHeaderCell:this.fnCustomHeaderCell,overflowTooltip:!0,customRender:this.aaa027Format},{title:"操作",align:"center",dataIndex:"action",width:"150",scopedSlots:{customRender:"action"},customHeaderCell:this.fnCustomHeaderCell,overflowTooltip:!0}],tableData:[]}},methods:{handleShowEdit:function(t){var a=this;this.modalTitle="编辑医护人员信息",this.visible=!0,this.loadDepartment(t.akb020),setTimeout((function(){a.editForm.setFieldsValue(t)}),3)},fnCustomRow:function(t,a){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",height:"22px"}}},fnCustomHeaderCell:function(t){return{style:{fontFamily:"PingFangSC-Regular",fontSize:"14px",color:"rgba(0,0,0,0.65)",fontWeight:"bold"}}},akb020Format:function(t){var a=this.hospitalList.find((function(a){return a.value===t}));return a?a.label:""},aaz307Format:function(t,a){return"".concat(a.aae386,"(").concat(a.aaz307,")")},aaa027Format:function(t){var a=this.treeData.find((function(a){return a.value===t}));return a?a.label:""},fileRequest:function(){},sampleDownload:function(){var t=this;Base.downloadFile({method:"post",fileName:"医院医生信息导入模板.xls",url:"hospDocInfo/downLoad"}).then((function(a){t.$message.success("下载成功")})).catch((function(a){t.$message.error("下载失败")}))},handlUpload:function(t){var a=this;return this.Base.submit(null,{headers:{"Content-Type":"multipart/form-data"},url:this.docConfig.uploadUrl,autoValid:!0,isFormData:!0,data:{uploadFile:t}},{successCallback:function(t){a.$message.success("上传成功"),a.loadTableData()},failCallback:function(t){a.$message.error("上传失败")}})},handleOk:function(t){var a=this;if("新增医护人员信息"===this.modalTitle){var e=(0,i.Z)((0,i.Z)({},this.form.getFieldsValue()),this.editForm.getFieldsValue());this.Base.submit(null,{url:this.docConfig.doctorInsertUrl,data:(0,i.Z)({},e),autoValid:!0},{successCallback:function(t){a.loadTableData(),a.visible=!1},failCallback:function(t){a.$message.error("添加数据失败，请检查数据")}})}else this.handleUpdate()},showModal:function(){var t=this;this.modalTitle="新增医护人员信息",this.visible=!0,this.loadDepartment(this.akb020),setTimeout((function(){t.form.setFieldsValue()}),3)},loadTableData:function(){this.$refs.departmentPager.loadData()},handleUpdate:function(){var t=this,a=this.editForm.getFieldsValue(),e=this.tableData.find((function(t){return t.aaz263===a.aaz263}));this.Base.submit(null,{url:this.docConfig.doctorUpdateUrl,data:(0,i.Z)({akb020:e.akb020},a),autoValid:!0},{successCallback:function(a){t.loadTableData(),t.visible=!1},failCallback:function(a){t.$message.error("更新失败")}})},pageParams:function(){return this.form.getFieldsValue()},changeData:function(t){var a=t.newData,e=t.record;t.rowKey;Object.assign(e,a)},fnTableChange:function(t){this.tableData=t},handleDelete:function(t){var a=this;this.Base.submit(null,{url:this.docConfig.doctorDeleteUrl,data:{aaz263:t.aaz263,akb020:t.akb020},autoValid:!0},{successCallback:function(t){a.loadTableData()},failCallback:function(t){a.$message.error("删除失败")}})},hospChange:function(t){var a=this;this.akb020=t,setTimeout((function(){a.loadTableData()}),50)},queryHospitalName:function(t){var a=this;this.Base.submit(null,{url:"miimCommonRead/queryAkb020DataByAaa027",data:{aaa027:t}}).then((function(t){a.hospitalList=t.data.akb020Data,a.form.resetFields(["akb020"]),a.akb020=null,a.loadTableData()}))},loadDepartment:function(t){var a=this;this.departmentInfo[t]?this.departmentList=this.departmentInfo[t]:this.Base.submit(null,{url:this.docConfig.departmentUrl,autoValid:!0,data:{akb020:t}},{successCallback:function(e){var o=e.data.aaz307Data;o.forEach((function(t){t.label="".concat(t.label,"(").concat(t.value,")")})),a.departmentInfo[t]=o,a.departmentList=o},failCallback:function(t){a.$message.error("初始化科室数据失败")}})},init:function(){var t=this;this.Base.submit(null,{url:this.docConfig.hospitalListUrl,autoValid:!0},{successCallback:function(a){t.hospitalList=a.data.akb020Data},failCallback:function(a){t.$message.error("初始化医院数据失败")}}),this.Base.submit(null,{url:"miimCommonRead/getTreeData",data:{}}).then((function(a){t.treeData=a.data.treeData,t.loadTableData()})),this.authObj&&this.authObj.isHospType()&&(this.akb020=this.authObj.getOrgCode())}},mounted:function(){var t=this;return(0,n.Z)(regeneratorRuntime.mark((function a(){return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,s.Z.initAuth(t);case 2:t.authObj=a.sent,t.init();case 4:case"end":return a.stop()}}),a)})))()}},c=u,d=e(1001),f=(0,d.Z)(c,o,l,!1,null,null,null),h=f.exports},36766:function(t,a,e){var o=e(66586);a["Z"]=o.Z},26263:function(t,a,e){e.d(a,{s:function(){return o},x:function(){return l}});var o=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("h3",{staticStyle:{"font-weight":"normal"}},[e("ta-icon",{staticStyle:{transform:"rotate(90deg)",color:"#276CF5"},attrs:{component:t.titleSvg}}),e("span",{staticStyle:{"font-size":"14px",color:"#303133","font-family":"'Microsoft YaHei'"}},[t._v(t._s(t.title))]),t._t("default")],2)},l=[]},66586:function(t,a){var e={template:'\n      <svg viewBox="500 400 200 150" focusable="false">\n      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path></svg>\n  '};a["Z"]={name:"taTitle",props:{title:{type:String}},data:function(){return{titleSvg:e}}}},75660:function(t,a,e){var o=e(48534),l=e(13087),n=e(62833),i=(e(36133),function(){function t(a){(0,l.Z)(this,t),this.authList=a.customResources,this.orgCode=a.orgCode}return(0,n.Z)(t,[{key:"getCustomAuthList",value:function(){return this.authList}},{key:"getOrgCode",value:function(){return this.orgCode}},{key:"isHospType",value:function(){return this.authList.includes("hosptype")}}]),t}());a["Z"]={initAuth:function(t){return(0,o.Z)(regeneratorRuntime.mark((function a(){var e;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,t.Base.submit(null,{url:"miimCommonRead/queryCustomResources",data:{}});case 2:return e=a.sent,a.abrupt("return",new i(e.data));case 4:case"end":return a.stop()}}),a)})))()}}}}]);
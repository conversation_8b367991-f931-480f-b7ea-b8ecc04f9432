<!DOCTYPE html>
<html id="page" lang="en">
<head>
    <meta charset="UTF-8">
    <script src="jquery-1.7.1.min.js"></script>
    <title>小助手登录</title>
    <style type="text/css">
        html {
            height: 220px !important;
            width: 180px !important;
        }

        body {
            height: 220px !important;
            width: 180px !important;
        }

        .div1 {
            width: 180px;
            height: 220px;
            /*border: 0px solid #000;*/
            /*box-shadow: 2px 2px 2px 2px rgba(0, 0, 0, 0.2);*/
        }

        button {
            border: 1.5px solid #3382f5;
            margin-top: 20px;
            height: 36px;
            font-weight: bold;
            color: #666666;
            border-radius: 16px;
        }

        button:active, button:hover {
            background: rgb(47, 130, 247) !important;
            color: #FFFFFF !important;
        }

        .loginForm {
            /*margin-top: 8%;*/
            border-radius: 2px;
            height: 220px;
            width: 180px;
            float: left;
        }

        .usernameDiv {
            /*width: 200px;*/
            height: 40px;
            /*padding-left: 10px;*/
            /*padding-top: 20px;*/

        }

        .adminInput {
            width: 100px;
            height: 20px;
            font-size: 12px;
            border-radius: 4px;
            color: #333333;
            border: 1px solid #d7d7d7;
            outline-color: #409eff;
            padding: 2px 2px 2px 10px;
        }

        .passwordDiv {
            width: 170px;
            height: 30px;
            /*padding-left: 10px;*/
            /*padding-top: 20px;*/
        }

        i {
            position: absolute;
        }

        .adminIcon {
            font-size: 22px;
            margin-top: 8px;
        }

        .logoHead {
            /*width: 200px;*/
            height: 20px;
            /*padding-left: 50px;*/
            /*padding-top: 25px;*/
            margin: 10px 25px 20px 25px;
        }

        .usernameLabel {
            text-align: right;
            width: 45px;
            height: 30px;
            font-size: 12px;
            /*margin-left: 10px;*/
            float: left;
            margin-top: 5px;
            margin-right: 8px;
            color: #7f7f7f;
            font-weight: bold;
        }

        .usernameWrapDiv {
            width: 180px;
            /*height: 70px;*/
        }

        .submitDiv {
            width: 88px;
            height: 20px;
            padding-left: 10px;
            padding-top: 15px;
            margin: 0 auto;
            text-align: center;
        }

        .supportDiv {
            width: 170px;
            /*padding-left: 10px;*/
            /*padding-top: 10px;*/
            margin: 0 auto;
            font-size: 12px;
            /*text-align: center;*/
            color: #7f7f7f;
        }

        .submit {
            width: 88px;
            height: 24px;
            border-radius: 4px;
            background-color: rgba(35, 100, 248, 1);
            font-size: 12px;
            color: #ffff;
            cursor: pointer;
            padding: 0 !important;
            border: 1px solid rgba(64, 158, 255, 1);
        }

        img {
            position: absolute;
        }
    </style>
</head>
<body>
<div id="app">
    <div style="height:220px;">
        <div class="div1">
            <div class="loginForm">
                <!--                <form>-->
                <div class="logoHead">
                    <img src="./img/u109.png" style="height: 25px; width: 130px;">
                    <!--						<h2-->
                    <!--								style="margin-left:35px;margin-top:-5px;font-family: Source Han Sans CN;font-weight: bold;color: #2364f8;font-size: 26px;"-->
                    <!--						>医保助手登录</h2>-->
                </div>
                <div class="usernameWrapDiv">
                    <div class="usernameLabel">
                        <label>用户名:</label>
                    </div>
                    <div class="usernameDiv">
                        <i class="layui-icon layui-icon-username adminIcon"></i>
                        <input id="loginUsername" class="layui-input adminInput" type="text" name="username"
                               placeholder="请输入用户名"
                               autocomplete="off"
                        >
                        <input id="deptno" class="layui-input adminInput" type="text" name="deptno"
                               style="display: none">
                    </div>
                </div>
                <div class="usernameWrapDiv">
                    <div class="usernameLabel">
                        <label>密 码:</label>
                    </div>
                    <div class="passwordDiv">
                        <i class="layui-icon layui-icon-password adminIcon"></i>
                        <input id="loginPassword" class="layui-input adminInput" type="password" name="password"
                               placeholder="请输入密码" autocomplete="off"
                        >
                    </div>
                </div>
                <div class="usernameWrapDiv">
                    <div class="submitDiv">
                        <input onclick="login(true)" id="loginBtn" type="button"
                               class="submit layui-btn layui-btn-primary"
                               value="立即登录"
                        ></input>
                    </div>
                    <div id="loginMsg"
                         style="color: red;font-size:12px;height: 12px;width: auto;text-align: center;margin-top: -2px;"></div>
                </div>
                <div class="usernameWrapDiv">
                    <div class="supportDiv">
                        <div id="supportmain">
<!--                            <div>服务支持咨询:</div>-->
<!--                            <span id="supportnum"></span><span id="supportname"></span>-->
<!--                            <br>-->
<!--                            <span id="supportnum2"></span><span id="supportname2"></span>-->
                        </div>
                    </div>
                </div>

                <!--                </form>-->
            </div>
        </div>
    </div>
</div>

<script>
    window.onload = function () {
        const browerWidth = window.outerWidth
        const baseWidth = 290
        // if (browerWidth < 290) {
        //     const zoomValue = browerWidth / baseWidth
        //     document.getElementById('page').style.transform = 'scale(' + zoomValue + ',' + zoomValue + ')'
        //     document.getElementById('page').style.transformOrigin = '0 0'
        // }
        let path_ip_port = window.location.origin + '/hiiss-backend/'
        $.ajax({
            url: path_ip_port + 'assistantWindow/querySupport',
            type: 'post',
            async: false,
            success: function (result) {
                const data = result.data
                if (data.namelenth > 0 && data.numlenth > 0){
                    // 获取容器元素
                    let container = document.getElementById('supportmain')
                    let listItem = document.createElement('div')
                    listItem.textContent = '服务支持咨询:'
                    container.appendChild(listItem)
                    let length = 1
                    if (data.namelenth > data.numlenth){
                        length = data['namelenth']
                    }else {
                        length = data['numlenth']
                    }
                    // console.log(data)
                    // // 遍历数组并生成相应元素
                    for (let i = 0; i < length; i++) {
                        if (data['supportname'+(i==0?"":i+1)] && data['supportnum'+(i==0?"":i+1)]){
                            const nameelement = document.createElement('span');
                            nameelement.textContent = data['supportname'+(i==0?"":i+1)] + " ";
                            nameelement.style.display = 'inline-block';
                            nameelement.style.width = '55px';
                            container.appendChild(nameelement);
                            const numelement = document.createElement('span');
                            numelement.textContent = data['supportnum'+(i==0?"":i+1)];
                            container.appendChild(numelement);
                            container.appendChild(document.createElement('br'));
                        }
                    }
                }
            },
        })
        const params = getParams();
        if (params.isLogin === "true") {
            $("#loginUsername").val(params.userId)
            $("#deptno").val(params.aaz307)
            login(false);
        }
    }

    function login(byPage) {
        const path_ip_port = window.location.origin + '/hiiss-backend/'
        const url = window.location.href;
        const path_ip_port2 = url.substr(0, url.lastIndexOf("assistantLogin.html"))
        const loginUsername = $('#loginUsername').val()
        const loginPassword = $('#loginPassword').val()
        const deptno = $('#deptno').val()
        QClient.DBPNSGetClientId(data => {
            const clientId = data.data
            $.ajax({
                url: path_ip_port + 'assistantWindow/clientLogin',
                data: {
                    loginId: loginUsername,
                    loginPassword: loginPassword,
                    clientId: clientId,
                    deptno: deptno,
                    byPage: byPage
                },
                type: 'post',
                async: false,
                success: function (data) {
                    if (data.data.code === '1') {
                        localStorage.setItem('aaz263', loginUsername)
                        localStorage.setItem('aaz307', data.data.aaz307)
                        localStorage.setItem('username', data.data.username)
                        localStorage.setItem("systemName", data.data.systemName)
                        QClient.invokeMethod("WindowReturnValue", "type=settitle", "{\"title\":\"医保助手\",\"subtitle\":\"" + data.data.username + "\"}");
                        const path = path_ip_port2 + 'assistantPopup.html#/assistantWindow' + '?aaz263=' + data.data.jobnumber + '&aaz307=' + data.data.aaz307 + '&username=' + data.data.username
                        QClient.openUrl(path, 0, 0, 0xFFF1)
                    } else {
                        $('#loginUsername').val('');
                        $('#loginMsg').html(data.data.msg)
                    }
                },
                error: function () {
                    $('#loginMsg').html('登录失败，联系管理员。')
                }
            })
        })
    }

    /**
     * 获取url中？后的数据 以对象的方式返回
     */
    function getParams() {
        const url = window.location;
        const params = new URLSearchParams(url.search);
        const returnParams = {};
        returnParams.userId = params.get("userId");
        returnParams.isLogin = params.get("isLogin");
        returnParams.aaz307 = params.get("aaz307");
        return returnParams
    }

</script>
</body>
</html>



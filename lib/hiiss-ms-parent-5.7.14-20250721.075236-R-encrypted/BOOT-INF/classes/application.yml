ta404:
  application:
    name: ta404
    version: 5.3.2-RELEASE
  component:
    security:
      login-permit-urls:
spring:
  profiles:
    active: devtest,datasource,websecurity,token,mttruleengine
  application:
    name: ${ta404.application.name}
  main:
    allow-bean-definition-overriding: true
  banner:
    location: banner.txt
  output:
    ansi:
      enabled: always
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB
  jackson:
    time-zone: GMT+8:00
    date-format: yyyy-MM-dd HH:mm:ss
    serialization:
      FAIL_ON_EMPTY_BEANS: false
    deserialization:
      FAIL_ON_UNKNOWN_PROPERTIES: false
    parser:
      ALLOW_SINGLE_QUOTES: true

  # kf38表的列数
  kf38ColumnNum: 100
  # 数据库一次性插入的列*行不得大于的数量 32787
  dbSum: 32787
  # 申诉是否开启多机构模式
  orgModel: false

server:
  port: 8089
  servlet:
    session:
      timeout: 7200s
    context-path: /hiiss-backend
  max-http-header-size: 655360
audit:
#  审核结果查询 开单科室是否区别 门诊和住院科室
  distinguishDeptType: false
#  住院患者审核疑点查询 是否启用结算ID匹配查询
  enableSIDMatch: false
  permisson:
    type: ta # 权限实现类型 默认ta   微平台：cas
#    龙华区中心医院特殊处理（project: longhua）
  specail:
    project:
  client:
    appeal:
      file:
        windows-save-path: D:/hiiss/appeal/
        linux-save-path: /hiiss/appeal/
      msg:
        medical-url: /hiiss-backend/template/appealForMedicalInsurance.html#/appealForMedicalInsurance
        doctor-url: /hiiss-backend/template/appealForDoc.html#/appealForDoc
        night-url: /hiiss-backend/template/approvalHandle.html#/approvalHandle
      shortmessage:
        apId: ggf9aD2ai
        ecName: 医保智能监管系统
        secretKey: e8dfg%c5Bmjaj
        sign: HFvIQgOpo
        addSerial:
        smsUrl: http://************:8080/hiiss/shortmessage
        sendType: phone
#     申诉处理电子病例相关配置
      emrLinkEnabled: true
      emrVersion: cduwcch
      out-link: true
      hisChromeAddress: C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe
      hisEmrApiUrl: http://***********:9004/PDB
      hashSecret: tongji123456
      chsPlatformUrl: http://www.baidu.com
      costDetailEnabled: false
      costUrl: http://report.his.tjh.com/webroot/decision/view/report
      iptTemplate: mips/mips-inp-bill-detail-audit.cpt
      optTemplate: mips/mips-outp_bill_detail-audit.cpt
      syncAuxiliary: true
#      extensions:
#        akb020: H11111
    #      showTypeSelectBtn: false
    msg:
      enabled: false
      user: deepblue
      password: yinhai@789456123
      host: **************
      port: 1883
      webAppId: hiissBack
      msgCenterAddress: http://************/msgncas/services/messageWebService
      msgCenterTicket: 3E41FFC19A40AF081E341AFC70AB2E40
      indexwarningUrl: /hiiss-backend/template/indexWarning.html#/indexWarning
    night:
      #小助手每晚审核数据操作结果回传接口
      api:
        # 版本 base 标准接口版。wuHanTongJi 武汉同济医院版本（默认值）
        version: base
        #开关
        open: false
        # 地址
        address: http://***************:8087/eif-server/webService/eifWebService?wsdl
        #webservice接口方法名,如果值为doPostJson则使用http方式调用
        method-name: xxxx
        #武汉同济医院配置，注释的内容均为默认值
#        wu-han-t-j-config:
#          event-type: BMS_REFUND_BILL
#          #入参中id对应Kf10Opr中的字段
#          item-id-property-name: ykc610
#          operator: yinhai_service
#          receiver: HIS
#          sender: SELF_SERVICE
#          #调用成功返回的Code标志
#          success-code: CA
#          #webService接口多入参配置用 , 分割。如：配置为  arg1,arg2,$xml则第一个入参固定为 arg1  ，第二个入参固定为 arg2， 第三个入参为xml数据
#          #默认为只有一个入参
#          web-service-args: $xml
#          #在操作列表中选择ape893为指定值的发送给his。
#          # 如 2:1,3:0 表示将 操作为自费(ape893=2)的项目，在调用his接口中以flag=1的参数方式传给his。
#          #           表示将 操作为取消(ape893=3)的项目，在调用his接口中以flag=0的参数方式传给his。
#          #           如果后期码值变换，方便调整。
#          #           并方便筛选那些操作需要回传给his。
#          send-filter: 2:1
      hisViewPrefix:
  common:
    center:
      address: http://localhost:8080/
      extension:
        sign_no: fms01
    #免密单点登录配置，仅支持session方式
    sso:
      comp: hkdtjyy #平台商：klbr-柯林布瑞、hkdtjyy-华中科技大学同济医学院附属同济医院
      address: http://portal.com/portal/serviceValidate
      defaultUser:
knife4j:
  enable: true #是否启用增强
  #是否允许调试
  setting:
    enableDebug: true
  production: false #为true代表启用生成环境禁用swagger文档
mtt:
  tempFileUrl: /u01/mttTempFile/

smtrp:
  dsc-newest:
    corn: "0 13 0 * * ?"
    enable: true

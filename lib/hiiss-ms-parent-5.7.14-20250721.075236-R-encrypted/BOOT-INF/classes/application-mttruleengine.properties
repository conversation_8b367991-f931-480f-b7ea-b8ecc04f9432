## mtt\u77E5\u8BC6\u5E93\u8C03\u7528\u5F15\u64CE\u5730\u5740
## \u57FA\u5730\u5740
basePath=http://*************:8915
## \u8FD9\u91CC\u6307\u5B9A\u8981\u52A0\u8F7D\u7684\u5F15\u64CE\u53F7ykz108,\u9017\u53F7\u5206\u9694
rule.loadEngines=
rule.sync_path=/web/engine/engineManagement/reLoadEngineByYkz108
rule.getLoadedEngines.path=/web/engine/engineManagement/getLoadedEngines
rule.unLoadEngineByYkz108.path=/web/engine/engineManagement/unLoadEngineByYkz108

## \u652F\u6301\u7684\u8FD0\u884C\u7C7B\u578B \u5728\u8001\u73AF\u5883\u662Fmtt+eng, \u5728\u53CCD\u4E2D\u4E3A mtt
# runscript.enabletypes: mtt,eng
runscript.enabletypes: mtt,eng
## \u5728\u8001\u73AF\u5883\u4E0B\u662Fmtt\uFF0C\u5728\u53CCD+\u4E2D\u662Fdmas_audit
mtt.schema: mtt
## eng\u6570\u636E\u6E90\uFF0C\u5982\u679Cenabletypes\u91CC\u9762\u6709eng,\u8FD9\u4E2A\u9700\u8981\u914D\u7F6E
runscript.eng.driverClassName: org.postgresql.Driver
runscript.eng.url: **********************************************************************************************************************************************************
runscript.eng.username: postgres
runscript.eng.password: ENC(SD5+U4id1/8ihC4hORqZrqztvJfZdYttVN9Jw+BgiDuiltptfMHUEv9xKQBQBMwo)

###################################### \u89C4\u5219\u9884\u4E0A\u7EBF\u652F\u6301 start  ##################
mtt.preload.enable=true
## \u9884\u4E0A\u7EBF\u5F15\u64CE\u5730\u5740
basePreloadPath=http://************:8917
## eng\u9884\u4E0A\u7EBF\u6570\u636E\u6E90\uFF0C\u5982enabletypes\u91CC\u9762\u6709eng,\u4E14\u5F00\u542F\u9884\u4E0A\u7EBF\u529F\u80FD,\u5219\u9700\u8981\u914D\u7F6E
runscript.eng.preload.driverClassName: org.postgresql.Driver
runscript.eng.preload.url: ***************************************************************************************************************************************
runscript.eng.preload.username: ylfwzb_rw
runscript.eng.preload.password: ENC(kXIkmEHNZb0ezlmV8P1PBuaoC7MflsnbGemICAOiFQ/yqfYaLU4z6I0LsRmbU3mQt32r0dcoJGVdaXly7289aw==)
###################################### \u89C4\u5219\u9884\u4E0A\u7EBF\u652F\u6301 end ##################

## \u77E5\u8BC6\u5E93\u914D\u7F6E
## \u662F\u5426\u901A\u8FC7mtt_column_info\u67E5\u8BE2\u8282\u70B9\u52A8\u6001\u8868\u4FE1\u606F
rule.node.mtt_column_info=false
## \u63A5\u53E3\u4E0D\u5C55\u793A\u7F16\u7801\u7684\u8282\u70B9\u7C7B\u578B
rule.node.notCodeYkz063s=ZL,ZY,ZE,DC,RM,CW,YX,EX,AD,BS,BR,DT

############\u8FC7\u65F6--->\u4E0B\u9762\u7684###############################
## \u7981\u7528\u89C4\u5219
rule.disbable_path=/web/engine/disableRuleByYkz032
## \u540C\u6B65\u89C4\u5219\u5185\u5BB9
rule.sync_rule_path=/web/engine/syncRuleByYkz032
## \u540C\u6B65\u5305\u4F53\u5185\u5BB9
rule.sync_ykz042_path=/web/engine/syncYkz042ByYkz032

## \u6570\u636E\u5E93\u52A0\u5BC6\u7684\u5BC6\u94A5\u5730\u5740
## \u89E3\u5BC6\u8BC1\u4E66
mtt.decrypt.enable=false
mtt.decrypt.cerpath=mttcert/engineDownload.p12
## \u968F\u673A\u5BC6\u7801securityKey\u6587\u4EF6
mtt.secretkey.path=mttcert/secret.keyByte

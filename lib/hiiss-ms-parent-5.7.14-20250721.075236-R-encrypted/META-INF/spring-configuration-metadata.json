{"groups": [{"name": "casserver.apis", "type": "com.yinhai.ta404.api.config.CasServiceConfig", "sourceType": "com.yinhai.ta404.api.config.CasServiceConfig"}, {"name": "license", "type": "com.yinhai.ta404.license.LicenseProperties", "sourceType": "com.yinhai.ta404.license.LicenseProperties"}], "properties": [{"name": "casserver.apis.rest-url", "type": "java.lang.String", "sourceType": "com.yinhai.ta404.api.config.CasServiceConfig"}, {"name": "casserver.apis.service-keys", "type": "java.util.Map<java.lang.String,java.lang.String>", "sourceType": "com.yinhai.ta404.api.config.CasServiceConfig"}, {"name": "casserver.apis.wsdl-namespace", "type": "java.lang.String", "sourceType": "com.yinhai.ta404.api.config.CasServiceConfig"}, {"name": "casserver.apis.wsdl-url", "type": "java.lang.String", "sourceType": "com.yinhai.ta404.api.config.CasServiceConfig"}, {"name": "license.licence-file", "type": "java.lang.String", "sourceType": "com.yinhai.ta404.license.LicenseProperties"}], "hints": []}
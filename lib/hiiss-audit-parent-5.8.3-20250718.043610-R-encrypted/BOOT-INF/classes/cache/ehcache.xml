<?xml version="1.0" encoding="UTF-8"?>
<config xmlns:xsi='http://www.w3.org/2001/XMLSchema-instance'
        xmlns='http://www.ehcache.org/v3'
        xsi:schemaLocation="http://www.ehcache.org/v3 http://www.ehcache.org/schema/ehcache-core.xsd">

    <cache-template name="taCacheTemplate">
        <key-type>java.lang.String</key-type>
        <value-type>java.lang.Object</value-type>
        <resources>
            <heap>200000</heap>
            <offheap unit="MB">10</offheap>
        </resources>
    </cache-template>

    <cache alias="appCodeCache" uses-template="taCacheTemplate"/>

    <!--组织缓存-->
    <cache alias="taOrgCache" uses-template="taCacheTemplate"/>

    <!--<cache alias="app" >-->
    <!--<key-type>java.lang.String</key-type>-->
    <!--<value-type>com.yinhai.ehcache.entity.EntityDemoBean</value-type>-->
    <!--<resources>-->
    <!--<heap>200000</heap>-->
    <!--<offheap unit="MB">100</offheap>-->
    <!--</resources>-->
    <!--</cache>-->

    <cache alias="reportCache">
        <key-type>java.lang.String</key-type>
        <value-type>java.util.Collection</value-type>
        <resources>
            <heap>200000</heap>
            <offheap unit="MB">10</offheap>
        </resources>
    </cache>
    <cache alias="childReportCache">
        <key-type>java.lang.String</key-type>
        <value-type>java.util.Collection</value-type>
        <resources>
            <heap>200000</heap>
            <offheap unit="MB">10</offheap>
        </resources>
    </cache>
    <cache alias="menuReportCache">
        <key-type>java.lang.String</key-type>
        <value-type>java.util.Collection</value-type>
        <resources>
            <heap>200000</heap>
            <offheap unit="MB">10</offheap>
        </resources>
    </cache>

    <cache alias="progressCache">
        <key-type>java.lang.String</key-type>
        <value-type>java.lang.Object</value-type>
        <resources>
            <heap>200000</heap>
            <offheap unit="MB">10</offheap>
        </resources>
    </cache>

    <cache alias="elasticJobCache" uses-template="taCacheTemplate"/>

    <cache alias="namewarp" uses-template="taCacheTemplate"/>
</config>

#ååºå¤å¼æï¼å¯ç¨/å è½½çå¼æï¼éå·åé
com.yinhai.medicare.monitor.loadEngines=eng_01_0000_02_02_01,eng_01_0000_02_05_01
#æ§è´¹ç³»ç»åè½æ§å¶åæ°-------------------------------------------------------
#è¿åçç¹æ¯å¦åå«æç»ä¿¡æ¯
com.yinhai.medicare.monitor.core.cost.doubtWithItem=true
#è¿åçç¹æ¯å¦åå«è§åè¯¦ç»ä¿¡æ¯ï¼å¯æ©å±éè¿ä¿¡å·ä½æ§å¶è¯¦ç»ç¨åº¦ï¼
com.yinhai.medicare.monitor.core.cost.doubtWithRule=true
#æºå®¡ç¬¬ä¸ä¸ªæ¥å£æ¯å¦è¿åå®¡æ ¸è¯¦æãç¨äºç³è¿°/æµè¯
com.yinhai.medicare.monitor.core.cost.checkReturnAuditProcess=false
#æºå®¡ç¬¬ä¸ä¸ªæ¥å£æ¯å¦è¿åçç¹
com.yinhai.medicare.monitor.core.cost.checkReturnDoubt=true
#æºå®¡ç¬¬ä¸ä¸ªæ¥å£æ¯å¦è¿åäººå®¡èç¹
com.yinhai.medicare.monitor.core.cost.checkReturnReviewNode=true
#æºå®¡è®°å½æä¹å
com.yinhai.medicare.monitor.core.cost.checkRecordPersistence=true
#å¼æåæ´æä¹å
com.yinhai.medicare.monitor.core.modify.persistence=true
#---------------------------------------------------------------------
#å¼æéç½®åº
com.yinhai.medicare.monitor.mergeEngineDatabase.driver-class-name=org.postgresql.Driver
com.yinhai.medicare.monitor.mergeEngineDatabase.url=*************************************************************************************************************************************************
com.yinhai.medicare.monitor.mergeEngineDatabase.username=ylfwzb
com.yinhai.medicare.monitor.mergeEngineDatabase.password=ENC(4di0CV1MCij//2ZPrwNJcE7pw1zvD5M4Yc7y5cNGCog9NHeLxdqhmW3l5Z7f0V8v)

#ç¬¬ä¸æ¹æ°æ®æº
com.yinhai.medicare.monitor.thirdPartDatabase.driver-class-name=org.postgresql.Driver
com.yinhai.medicare.monitor.thirdPartDatabase.url=***********************************************************************************************************************************************
com.yinhai.medicare.monitor.thirdPartDatabase.username=ylfwzb
com.yinhai.medicare.monitor.thirdPartDatabase.password=ENC(4di0CV1MCij//2ZPrwNJcE7pw1zvD5M4Yc7y5cNGCog9NHeLxdqhmW3l5Z7f0V8v)

#å¼æè¿è¡åº
spring.datasource.znkf.driver-class-name=org.postgresql.Driver
spring.datasource.znkf.url=**********************************************************************************************************************************************
spring.datasource.znkf.username=ylfwzb
spring.datasource.znkf.password=ENC(4di0CV1MCij//2ZPrwNJcE7pw1zvD5M4Yc7y5cNGCog9NHeLxdqhmW3l5Z7f0V8v)

# Redisæ°æ®åºç´¢å¼ï¼é»è®¤ä¸º0ï¼
spring.redis.database=0
# Redisæå¡å¨å°å
spring.redis.host=127.0.0.1
#spring.redis.host=*************
#spring.redis.host=*************
#spring.redis.host=**************
# Redisæå¡å¨è¿æ¥ç«¯å£
spring.redis.port=6379
# Redisæå¡å¨è¿æ¥å¯ç ï¼é»è®¤ä¸ºç©ºï¼
spring.redis.password=
#spring.redis.password=111111
# è¿æ¥æ± æå¤§è¿æ¥æ°ï¼ä½¿ç¨è´å¼è¡¨ç¤ºæ²¡æéå¶ï¼
spring.redis.lettuce.pool.max-active=200
# è¿æ¥æ± æå¤§é»å¡ç­å¾æ¶é´ï¼ä½¿ç¨è´å¼è¡¨ç¤ºæ²¡æéå¶ï¼
spring.redis.lettuce.pool.max-wait=-1
# è¿æ¥æ± ä¸­çæå¤§ç©ºé²è¿æ¥
spring.redis.lettuce.pool.max-idle=10
# è¿æ¥æ± ä¸­çæå°ç©ºé²è¿æ¥
spring.redis.lettuce.pool.min-idle=0
# è¿æ¥è¶æ¶æ¶é´ï¼æ¯«ç§ï¼
spring.redis.timeout=1000

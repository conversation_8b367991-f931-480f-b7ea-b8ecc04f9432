<?xml version="1.0" encoding="utf-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <!--全局异常处理-->
<!--    <import resource="classpath:config/web/mvc.xml"/>-->
    <!--分布式缓存-->
    <import resource="classpath:config/mvc/mvc.xml"/>
    <import resource="classpath:config/db/dbuts-postgresql.xml"/>
<!--    <import resource="classpath:config/db/dbuts.xml"/>-->
    <import resource="classpath:config/db/dbhis.xml"/>
    <import resource="classpath:config/job/jobuts.xml"/>

</beans>

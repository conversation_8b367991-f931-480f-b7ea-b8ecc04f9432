<?xml version='1.0'?>
 
<coherence xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
   xmlns="http://xmlns.oracle.com/coherence/coherence-operational-config"
   xsi:schemaLocation="http://xmlns.oracle.com/coherence/coherence-operational-config
   coherence-operational-config.xsd">
   <cluster-config>
      <member-identity>
         <cluster-name>example-dist</cluster-name>
      </member-identity>
      <multicast-listener>
         <address>127.0.0.1</address>
         <time-to-live>0</time-to-live>
      </multicast-listener>
   </cluster-config>
  
   <configurable-cache-factory-config>
      <init-params>
         <init-param>
            <param-type>java.lang.String</param-type>
            <param-value system-property="coherence.cacheconfig">
               ./cache/cache-config-coherence.xml</param-value>
         </init-param>
      </init-params>
   </configurable-cache-factory-config>
</coherence>
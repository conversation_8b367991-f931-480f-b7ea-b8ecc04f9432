ta404:
  datasource:
    datasource-list: ta404ds
    type: druid
    druid:
      ta404ds:
        datasource:
#          url: **********************************************************************************************************************************************************
#          url: ********************************************************************************************************************************************************
#          username: ylfwzb_rw
#          password: ENC(ReIMMoh2gbb7Zg1+27f52dpNW3IX9iG32PSyCfn+zxaxK81fXXLIkDQd7tyNuBrs)
#          drverClassName: com.mysql.jdbc.Driver
#          driver-class-name: org.postgresql.Driver #可不写
          url: *********************************************************************************************************************************************************
#          url: ******************************************************************************************************************************************************************
          username: postgres
          password: ENC(SD5+U4id1/8ihC4hORqZrqztvJfZdYttVN9Jw+BgiDuiltptfMHUEv9xKQBQBMwo)
          # 初始化连接数
          initial-size: 3
          # 连接池最大连接数量
          max-active: 100
          # 获取链接超时时间 （毫秒）
          max-wait: 28800
          # 空闲连接数的回收周期
          timeBetweenEvictionRunsMillis: 60000
          # 申请连接时候，并当连接为空闲连接时，验证是否有效
          testWhileIdle: true
          # 申请连接时候验证是否有效，优先级高于testWhileIdle
          testOnBorrow: false
          # 验证语句
          validationQuery: SELECT 'x'
        mybatis:
          mapper-locations:
            - classpath*:mapper/read/*.xml
            - classpath*:mapper/write/*.xml
            - classpath*:mapper/*.xml
            - classpath*:mapper.hiiss.**/read/*.xml
            - classpath*:mapper.hiiss.**/write/*.xml
            - classpath*:mapper/hiiss/**/read/*.xml
            - classpath*:mapper/hiiss/**/write/*.xml
          type-aliases-package:
            - com.yinhai.hiiss.**.entity
          base-packages:
            - com.yinhai.ta404.module.mybatis.mapper
            - com.yinhai.ta404.module.**.mapper.read
            - com.yinhai.ta404.module.**.mapper.write
            - com.yinhai.ta404.component.**.mapper.read
            - com.yinhai.ta404.component.**.mapper.write
            - com.yinhai.hiiss.**.mapper.read
            - com.yinhai.hiiss.**.mapper.write

##动态数据源配置
audit:
  datasource:
    dynamic:
      multiEnabled: false
#      配置对应医院使用的数据源
      source-fixmedinsCode-map:
        ds1: H41030500537,H41030500538
        ds2: H41030500539,H51010802242
#        配置数据源
      source-map:
        ds1:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ****************************************************************************************************************************************************************************
          username: audit_his_dev
          password: ENC(Bz+0bzwv1QRa7gdzbg3wda8aZc6DdfyRbu/WdpC0zg9nuqrp/LUd3iv4okM1GyRB)
          # 初始化连接数
          initial-size: 3
          # 连接池最大连接数量
          max-active: 10
          # 获取链接超时时间 （毫秒）
          max-wait: 28800
          # 空闲连接数的回收周期
          timeBetweenEvictionRunsMillis: 60000
          # 申请连接时候，并当连接为空闲连接时，验证是否有效
          testWhileIdle: true
          # 申请连接时候验证是否有效，优先级高于testWhileIdle
          testOnBorrow: false
          # 验证语句
          validationQuery: SELECT 'x'
        ds2:
          driver-class-name: org.postgresql.Driver
          url: *********************************************************************************************************************************************************
          username: postgres
          password: ENC(SD5+U4id1/8ihC4hORqZrqztvJfZdYttVN9Jw+BgiDuiltptfMHUEv9xKQBQBMwo)
          # 初始化连接数
          initial-size: 3
          # 连接池最大连接数量
          max-active: 10
          # 获取链接超时时间 （毫秒）
          max-wait: 28800
          # 空闲连接数的回收周期
          timeBetweenEvictionRunsMillis: 60000
          # 申请连接时候，并当连接为空闲连接时，验证是否有效
          testWhileIdle: true
          # 申请连接时候验证是否有效，优先级高于testWhileIdle
          testOnBorrow: false
          # 验证语句
          validationQuery: SELECT 'x'


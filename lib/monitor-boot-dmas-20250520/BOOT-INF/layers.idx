- "dependencies":
  - "BOOT-INF/lib/HikariCP-3.2.0.jar"
  - "BOOT-INF/lib/bcpkix-jdk18on-1.77.jar"
  - "BOOT-INF/lib/bcprov-jdk18on-1.77.jar"
  - "BOOT-INF/lib/bcutil-jdk18on-1.77.jar"
  - "BOOT-INF/lib/checker-qual-3.5.0.jar"
  - "BOOT-INF/lib/commons-codec-1.15.jar"
  - "BOOT-INF/lib/commons-lang3-3.12.0.jar"
  - "BOOT-INF/lib/commons-pool2-2.11.1.jar"
  - "BOOT-INF/lib/cryptography-core-20241209.jar"
  - "BOOT-INF/lib/jackson-annotations-2.13.4.jar"
  - "BOOT-INF/lib/jackson-core-2.13.4.jar"
  - "BOOT-INF/lib/jackson-databind-2.13.4.2.jar"
  - "BOOT-INF/lib/jackson-datatype-jdk8-2.13.4.jar"
  - "BOOT-INF/lib/jackson-datatype-jsr310-2.13.4.jar"
  - "BOOT-INF/lib/jackson-module-parameter-names-2.13.4.jar"
  - "BOOT-INF/lib/jakarta.annotation-api-1.3.5.jar"
  - "BOOT-INF/lib/jasypt-1.9.3.jar"
  - "BOOT-INF/lib/jasypt-spring-boot-3.0.5.jar"
  - "BOOT-INF/lib/jcl-over-slf4j-1.7.36.jar"
  - "BOOT-INF/lib/jul-to-slf4j-1.7.36.jar"
  - "BOOT-INF/lib/log4j-api-2.17.2.jar"
  - "BOOT-INF/lib/log4j-core-2.17.2.jar"
  - "BOOT-INF/lib/log4j-slf4j-impl-2.17.2.jar"
  - "BOOT-INF/lib/mybatis-3.5.6.jar"
  - "BOOT-INF/lib/mybatis-spring-2.0.6.jar"
  - "BOOT-INF/lib/mysql-connector-j-8.0.31.jar"
  - "BOOT-INF/lib/ojdbc8-21.5.0.0.jar"
  - "BOOT-INF/lib/orai18n-21.5.0.0.jar"
  - "BOOT-INF/lib/postgresql-42.3.7.jar"
  - "BOOT-INF/lib/slf4j-api-1.7.36.jar"
  - "BOOT-INF/lib/snakeyaml-1.30.jar"
  - "BOOT-INF/lib/spring-aop-5.3.23.jar"
  - "BOOT-INF/lib/spring-beans-5.3.23.jar"
  - "BOOT-INF/lib/spring-boot-2.7.5.jar"
  - "BOOT-INF/lib/spring-boot-autoconfigure-2.7.5.jar"
  - "BOOT-INF/lib/spring-boot-jarmode-layertools-2.7.5.jar"
  - "BOOT-INF/lib/spring-context-5.3.23.jar"
  - "BOOT-INF/lib/spring-context-support-5.3.23.jar"
  - "BOOT-INF/lib/spring-core-5.3.23.jar"
  - "BOOT-INF/lib/spring-data-commons-2.7.5.jar"
  - "BOOT-INF/lib/spring-data-keyvalue-2.7.5.jar"
  - "BOOT-INF/lib/spring-data-redis-2.7.5.jar"
  - "BOOT-INF/lib/spring-expression-5.3.23.jar"
  - "BOOT-INF/lib/spring-jcl-5.3.23.jar"
  - "BOOT-INF/lib/spring-jdbc-5.3.23.jar"
  - "BOOT-INF/lib/spring-oxm-5.3.23.jar"
  - "BOOT-INF/lib/spring-tx-5.3.23.jar"
  - "BOOT-INF/lib/spring-web-5.3.23.jar"
  - "BOOT-INF/lib/spring-webmvc-5.3.23.jar"
  - "BOOT-INF/lib/tomcat-embed-core-9.0.68.jar"
  - "BOOT-INF/lib/tomcat-embed-el-9.0.68.jar"
  - "BOOT-INF/lib/tomcat-embed-websocket-9.0.68.jar"
- "spring-boot-loader":
  - "org/"
- "snapshot-dependencies":
- "application":
  - "BOOT-INF/classes/"
  - "BOOT-INF/classpath.idx"
  - "BOOT-INF/layers.idx"
  - "BOOT-INF/lib/monitor-rule-api-20250520.jar"
  - "BOOT-INF/lib/monitor-rule-core-20250520.jar"
  - "BOOT-INF/lib/monitor-rule-core-advice-20250520.jar"
  - "BOOT-INF/lib/monitor-rule-core-bill-20250520.jar"
  - "BOOT-INF/lib/monitor-rule-core-cost-20250520.jar"
  - "BOOT-INF/lib/monitor-rule-core-engine-jdbc-crypted-20250520.jar"
  - "BOOT-INF/lib/monitor-rule-core-scheme-20250520.jar"
  - "BOOT-INF/lib/monitor-rule-core-sql-20250520.jar"
  - "BOOT-INF/lib/monitor-rule-core-validation-20250520.jar"
  - "BOOT-INF/lib/monitor-support-20250520.jar"
  - "BOOT-INF/lib/monitor-web-advice-20250520.jar"
  - "BOOT-INF/lib/monitor-web-advice-api-20250520.jar"
  - "BOOT-INF/lib/monitor-web-bill-20250520.jar"
  - "BOOT-INF/lib/monitor-web-bill-api-20250520.jar"
  - "BOOT-INF/lib/monitor-web-cost-20250520.jar"
  - "BOOT-INF/lib/monitor-web-cost-api-20250520.jar"
  - "BOOT-INF/lib/monitor-web-engine-management-20250520.jar"
  - "BOOT-INF/lib/monitor-web-scheme-20250520.jar"
  - "BOOT-INF/lib/monitor-web-scheme-api-20250520.jar"
  - "META-INF/"
